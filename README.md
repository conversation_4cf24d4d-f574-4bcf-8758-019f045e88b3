# Bnext QMS - 3.0.X
To install and set up the project, follow these steps:

1. Clone the repository:
    ```sh
    git clone http://172.20.1.89/Bnext/QMS_3.0.0.git
    ```

2. Build the project using Maven:
    ```sh
    ./mvnw clean verify
    ```

3. Set up the Angular frontend:
    ```sh
    cd src/main/ngapp
    npm install
    npm run build
    ```

CI

[![Build Status](http://172.20.1.14:8081/buildStatus/icon?job=Bnext+QMS%2FQMS_3.0.0%2F3.1.X)](http://172.20.1.14:8081/job/Bnext%20QMS/job/QMS_3.0.0/job/3.1.X/)

