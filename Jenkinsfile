def mavenOpts = (""
    + " -Xms4g"
    + " -Xmx6g"
    + " -Dorg.slf4j.simpleLogger.showThreadName=true"
    + " -Dorg.slf4j.simpleLogger.showDateTime=true"
    + " -Dorg.slf4j.simpleLogger.dateTimeFormat=[HH:mm:ss:SSS]"
    + " -Dbuildtime.output.log=true"
    + " -Dbuildtime.output.csv=true"
    + " -Dbuildtime.output.csv.file=buildtime.csv"
)
def recipients = emailextrecipients([
    [$class: 'CulpritsRecipientProvider'],
    [$class: 'DevelopersRecipientProvider'],
    [$class: 'RequesterRecipientProvider']
]) + ',<EMAIL>'
def mailer = {
    step([
        $class: 'Mailer',
        notifyEveryUnstableBuild: true,
        recipients: recipients
    ])

}
def buildNumber = env.BUILD_NUMBER as int
if (buildNumber > 1) milestone(buildNumber - 1)
milestone(buildNumber)
pipeline {
    agent any
    triggers {
        pollSCM('H/2 8-20/1 * * *')
        cron('H 19-21 1 1,4,7 *')
    }
    tools {
        maven 'M3'
        jdk 'OpenJDK 23'
    }
    options {
        buildDiscarder(logRotator(daysToKeepStr: "100" ,numToKeepStr: '50', artifactDaysToKeepStr: '100', artifactNumToKeepStr: "5"))
        timestamps()
        timeout(time: 3, unit: 'HOURS')
        office365ConnectorWebhooks([
            [
                name: "Build QMS 3.1.X",
                url: "https://blocknetworks.webhook.office.com/webhookb2/74a51cfd-e542-485f-bb51-6c7b946753cb@e23b8d83-04ea-42d4-bfc7-22cdc0e32506/JenkinsCI/e416a093c4db468ab468a36ab8c04cde/0b7ff94d-3fb7-46d9-9d9b-ec6d71b83ec4/V2hZdFIwVWG1KeKdQ3wsUX8W99hNI75ZkYi1ZjLdbjqbY1",
                notifyFailure: true,
                notifyBackToNormal: true,
                notifyUnstable: true,
                timeout: 30000

            ]
        ])
        disableConcurrentBuilds()
    }
    stages {
        stage ('Clean') {
            steps {
                script {
                    try {
                        bat "delete-build"
                    } catch (err) {
                        echo err.getMessage()
                    }
                }
            }
        }
        stage ('Build OpenJDK 23') {
            parallel {
                stage ('Build Java') {
                    tools {
                        jdk 'OpenJDK 23'
                    }
                    steps {
                        withMaven(
                            maven: 'M3',
                            mavenOpts: mavenOpts,
                            options: [
                                artifactsPublisher(disabled: true)
                            ]
                        ) {
                            sh "mvn verify -T 1C -PBuild-Tomcat-Resources -PBuild-Git-Info -PSkip-War -PRun-CheckJavaRules -PBuild-AspectJ-CompileTime -PRun-Liquibase -PRun-Integration-Test -Dapp.name=QMS_FUR_${GIT_COMMIT}"
                        }
                    }
                }
                stage ('Build Angular') {
                    tools {
                        jdk 'OpenJDK 23'
                    }
                    steps {
                        withMaven(
                            maven: 'M3',
                            mavenOpts: mavenOpts,
                            options: [
                                artifactsPublisher(disabled: true)
                            ]
                        ) {
                            sh "mvn verify -T 1C -PBuild-Angular -PSkip-Unit-Test -PSkip-War -PRun-Liquibase -PSkip-Compile -Dapp.name=QMS_FUR_${GIT_COMMIT}"
                        }
                    }
                }
            }
        }
        stage ('Package') {
            tools {
                jdk 'OpenJDK 23'
            }
            steps {
                withMaven(
                    maven: 'M3',
                    mavenOpts: mavenOpts,
                    options: [
                        artifactsPublisher(disabled: true)
                    ]
                ) {
                    sh "mvn package -T 1C -PBuild-War -PSkip-Unit-Test -PRun-Liquibase -PSkip-Compile -Dapp.name=QMS_FUR_${GIT_COMMIT}"
                }
            }
        }
        stage ('SonarQube') {
            when {
                anyOf {
                    triggeredBy 'TimerTrigger'
                    triggeredBy 'UserIdCause'
                }
            }
            tools {
                jdk 'OpenJDK 23'
            }
            environment {
                PACKAGE_NAME = 'BnextQMS'
                PACKAGE_BRANCH = '3.1.X'
                SONARQUBE_PROPERTIES = (''
                    + ' -Dsonar.sources=src/main/webapp,pom.xml,src/main/java,src/main/ngapp/src'
                    + ' -Dsonar.typescript.node=src/main/ngapp/node/node'
                    + ' -Dorg.slf4j.simpleLogger.log.org.sonarsource=error'
                )
            }
            steps {
                withMaven(
                    maven: 'M3',
                    mavenOpts: mavenOpts,
                    options: [
                        artifactsPublisher(disabled: true)
                    ]
                ) {
                    sh "mvn org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -T 1C -Dsonar.projectKey=${env.PACKAGE_NAME}-${env.PACKAGE_BRANCH} -Dsonar.projectName=${env.PACKAGE_NAME}-${env.PACKAGE_BRANCH} ${env.SONARQUBE_PROPERTIES}"
                }
            }
        }
        stage ('Analysis dependency and plugins updates') {
            when {
                anyOf {
                    triggeredBy 'TimerTrigger'
                    triggeredBy 'UserIdCause'
                }
            }
            tools {
                jdk 'OpenJDK 23'
            }
            steps {
                withMaven(
                    maven: 'M3',
                    mavenOpts: mavenOpts,
                    options: [
                        artifactsPublisher(disabled: true)
                    ]
                ) {
                    sh "mvn site -PBuild-MavenSite -T 1C"
                    sh "mvn versions:display-plugin-updates -Dmaven.version.ignore=[0-9]{8}.[0-9]{6},[0-9]{8},.*-M.*,.*-beta.*,.*-alpha.*,.*.Alpha.*,.*-preview,.*.jre11,.*-gt2-pre1 -T 1C"
                    sh "mvn versions:display-property-updates -Dmaven.version.ignore=[0-9]{8}.[0-9]{6},[0-9]{8},.*-M.*,.*-beta.*,.*.jbossorg-.*,.*-alpha.*,.*.Alpha.*,.*.Beta.*,.*.RC.*,.*-preview,.*.jre11,.*-gt2-pre1,.*.SP.*  -T 1C"
                }
            }
        }
        stage ('Archive artifacts') {
            tools {
                jdk 'OpenJDK 23'
            }
            steps {
                archiveArtifacts(
                    allowEmptyArchive: true,
                    artifacts: 'target/site/*.*, target/*.war',
                    onlyIfSuccessful: true,
                    fingerprint: true
                )
            }
        }
    }

    post {
        failure {
            script {
                mailer()
            }
        }
        success {
            script {
                mailer()
            }
        }
        unstable {
            script {
                mailer()
            }
        }
        fixed {
            script {
                mailer()
            }
        }
    }
}
