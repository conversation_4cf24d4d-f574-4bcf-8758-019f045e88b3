package qms.activity.bulk.util;

import DPMS.DAOInterface.DocumentDataExchangeDAOMock;
import DPMS.DAOInterface.IDocumentDataExchangeDAO;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.ActivityDataExchangeDAOMock;
import qms.activity.DAOInterface.IActivityDataExchangeDAO;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.framework.bulk.imp.ActivityHandler;
import qms.framework.dto.DataExchangeDTO;
import qms.framework.util.CsvParser;
import qms.framework.util.DataExchangeParseError;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.util.FileTestUtils;
import qms.util.TestUtils;

public class ActivityBulkDataTestUtils {

    private static final Map<String, Path> FILES = new HashMap<>();
    
    private static Path loadTemplatePath(final String filename) throws URISyntaxException {
        if (FILES.containsKey(filename)) {
            return FILES.get(filename);
        }
        final Path result = FileTestUtils.loadPathByPath(ActivityBulkDataTestUtils.class, filename);
        FILES.put(filename, result);
        return result;
    }

    private static Path getTemplateRowsCsv() throws URISyntaxException {
        return loadTemplatePath("template-rows.csv");
    }

    private static Path getTemplateVariablesCsv() throws URISyntaxException {
        return loadTemplatePath("template-variables.csv");
    }

    public static Path getTemplateEmptyXlsx() throws URISyntaxException {
        return loadTemplatePath("template-empty.xlsx");
    }

    public static Path getTemplateValidXlsx() throws URISyntaxException {
        return loadTemplatePath("template.xlsx");
    }
    
    public static Path getMissingFieldsXlsx() throws URISyntaxException {
        return loadTemplatePath("missing-fields.xlsx");
    }
    
    public static Path getMissingTaskXlsx() throws URISyntaxException {
        return loadTemplatePath("missing-task.xlsx");
    }
    
    public static Path getInvalidLengthXlsx() throws URISyntaxException {
        return loadTemplatePath("invalid-length.xlsx");
    }
    

    public static Path getDynamicFieldsXlsx() throws URISyntaxException {
        return loadTemplatePath("dynamic-fields.xlsx");
    }
    
    public static Path getInvalidDynamicFieldsXlsx() throws URISyntaxException {
        return loadTemplatePath("invalid-dynamic-fields.xlsx");
    }
    
    public static Path getLongDynamicFieldsXlsx() throws URISyntaxException {
        return loadTemplatePath("long-dynamic-fields.xlsx");
    }

    public static Path getInvalidHeadersXlsx() throws URISyntaxException {
        return loadTemplatePath("template-invalid-headers.xlsx");
    }

    public static DataExchangeDTO defaultTemplateRowsCsv(final Locale locale)
            throws URISyntaxException, IOException, DataExchangeParseError {
        final Path template = getTemplateRowsCsv();
        if (template == null) {
            return null;
        }
        final CsvParser parser = new CsvParser(locale);
        return parser.parse(template, null, null, 0);

    }

    public static DataExchangeDTO defaultTemplateVariablesCsv(final Locale locale)
            throws URISyntaxException, IOException, DataExchangeParseError {
        final Path template = getTemplateVariablesCsv();
        if (template == null) {
            return null;
        }
        final CsvParser parser = new CsvParser(locale);
        return parser.parse(template, null, null, 0);
    }

    public static ActivityBulkConfig mockActivityBulkConfig(
            Locale locale,ILoggedUser admin
    ) throws IOException {
        final IActivityDataExchangeDAO activityDao = new ActivityDataExchangeDAOMock();
        final IDocumentDataExchangeDAO documentDao = new DocumentDataExchangeDAOMock();
        final TimesheetDataSourceDto tsDs = activityDao.getProjectCatalogs(admin);
        final ActivityBulkConfig config = new ActivityBulkConfig(
                "Test-App-QMS",
                "https://localhost:7002/BnextQMS/",
                activityDao,
                documentDao, 
                locale,
                tsDs,
                TestUtils.getFileManager()
        );
        new ActivityHandler(locale, config, null, false, false, activityDao, admin);
        return config;
    }

}
