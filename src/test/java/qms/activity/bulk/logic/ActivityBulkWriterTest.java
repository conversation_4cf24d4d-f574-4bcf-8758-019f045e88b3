package qms.activity.bulk.logic;

import bnext.exception.MakePersistentException;
import bnext.reference.UserRef;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Locale;
import mx.bnext.core.file.FileHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.DAOInterface.ActivityDataExchangeDAOMock;
import qms.activity.DAOInterface.IActivityDataExchangeDAO;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.activity.bulk.util.ActivityBulkDynamicFieldError;
import qms.activity.entity.ActivityBulkFile;
import qms.framework.bulk.dto.IBulkUploadResult;
import qms.framework.bulk.entity.IBulkLog;
import qms.framework.bulk.imp.ActivityHandler;
import qms.framework.bulk.util.BulkConstants;
import qms.framework.bulk.util.BulkError;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MaximumCharactersError;
import qms.framework.util.MeasureTime;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class ActivityBulkWriterTest {
    
    private ILoggedUser admin;
    private Locale locale;
    private ActivityBulkConfig config;
    private IActivityDataExchangeDAO dao;
    private IBulkLog log;

    @BeforeEach
    public void setup() throws Exception {
        admin = new LoggedUser(new UserRef(10L));
        locale = new Locale("es", "MX");
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
        dao = new ActivityDataExchangeDAOMock();
        log = new ActivityBulkFile(1L);
    }

    @Test
    public void testValidTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler handler = new ActivityHandler(locale, config, null, false, false, dao, admin);
            final String filename = "writer-result-testValidTemplate";
            final Path file = handler.uploadAction(filename, template, log, admin).getFile();
            assertNotNull(file, "Write file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }

    @Test
    public void testEmpty() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityHandler handler = new ActivityHandler(locale, config, null, false, false, dao, admin);
            final BulkFileError thrown = assertThrows(BulkFileError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
                assertNotNull(template, "Template must not be null");
                final String filename = "writer-result-testEmpty";
                final IBulkUploadResult result = handler.uploadAction(filename, template, log, admin);
                final Path file = result.getFile();
                assertNotNull(file, "Write file must not be null");
            });
            assertEquals(config.getTags().getTag("parseError"), thrown.getMessage());
            final Path errorFile = handler.getWritter().writeError("writer-error-testEmpty", thrown, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }

    @Test
    public void testMissingFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityHandler handler = new ActivityHandler(locale, config, null, false, false, dao, admin);
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final String filename = "writer-result-testMissingFields";
                final Path file = handler.uploadAction(filename, template, log, admin).getFile();
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("dataValidationError"), thrown.getMessage());
            final Path errorFile = handler.getWritter().writeError("writer-error-testMissingFields", thrown, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingFields test.");
        }
    }

    @Test
    public void testInvalidLength() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {

            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final MaximumCharactersError thrown = assertThrows(MaximumCharactersError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidLengthXlsx();
                assertNotNull(template, "Template must not be null");
                final String filename = "writer-result-testInvalidLength";
                final Path file = handler.uploadAction(filename, template, log, admin).getFile();
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            final String message = config.getTags().getTag("maximumCharactersExceeded")
                    .replace("{maxCharacters}", BulkConstants.MAX_DESCRIPTION_LENGTH.toString());
            assertEquals(message, thrown.getMessage());
            final Path errorFile = handler.getWritter().writeError("writer-error-testInvalidLength", thrown, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidLength test.");
        }
    }

    @Test
    public void testMissingTask() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getMissingTaskXlsx();
                assertNotNull(template, "Template must not be null");
                final String filename = "writer-result-testMissingTask";
                final Path file = handler.uploadAction(filename, template, log, admin).getFile();
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("dataValidationError"), thrown.getMessage());
            final Path errorFile = handler.getWritter().writeError("writer-error-testMissingTask", thrown, admin);
            assertNotNull(errorFile, "Error file must not be null");

        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingTask test.");
        }
    }

    @Test
    public void testInvalidHeaders() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidHeadersXlsx();
                assertNotNull(template, "Template must not be null");
                final String filename = "writer-result-testInvalidHeaders";
                final Path file = handler.uploadAction(filename, template, log, admin).getFile();
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("headersError"), thrown.getMessage());
            final Path errorFile = handler.getWritter().writeError("writer-error-testInvalidHeaders", thrown, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidHeaders test.");
        }
    }

    @Test
    public void testDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final String filename = "writer-result-testDynamicFields";
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final Path file = handler.uploadAction(filename, template, log, admin).getFile();
            assertNotNull(file, "Write file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }

    @Test
    public void testInvalidDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final BulkFileError error = assertThrows(BulkFileError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidDynamicFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final String filename = "writer-result-testInvalidDynamicFields";
                final Path file = handler.uploadAction(filename, template, log, admin).getFile();
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            Throwable thrown = error.getCause();
            assertNotNull(thrown, "Cause must not be null");
            assertEquals(ActivityBulkDynamicFieldError.class, thrown.getClass());
            assertEquals(config.getTags().getTag("invalidDynamicFields"), thrown.getMessage());
            final Path errorFile = handler.getWritter().writeError("writer-error-testInvalidDynamicFields", thrown, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidDynamicFields test.");
        }
    }

    @Test
    public void testLongDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final BulkFileError error = assertThrows(BulkFileError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getLongDynamicFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final String filename = "writer-result-testLongDynamicFields";
                final Path file = handler.uploadAction(filename, template, log, admin).getFile();
                assertNotNull(file, "Write file must not be null");
            }, "Validation must fail");
            Throwable thrown = error.getCause();
            assertNotNull(thrown, "Cause must not be null");
            assertEquals(ActivityBulkDynamicFieldError.class, thrown.getClass());
            assertEquals(config.getTags().getTag("invalidDynamicFields"), thrown.getMessage());
            final Path errorFile = handler.getWritter().writeError("writer-error-testLongDynamicFields", thrown, admin);
            assertNotNull(errorFile, "Error file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }
    
    @Test
    public void testWriteTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path temp = FileHandler.createTempFile("writer-template-testWriteTemplate", ".xlsx", config.getFileManager().getTempFolder());
            try (final OutputStream output = Files.newOutputStream(temp)) {
                final Long activityTypeId = 1L;
                final ActivityBulkUploader uploader = new ActivityBulkUploader(locale, config, activityTypeId, true, false, dao, admin);
                uploader.getWritter().writeTemplate( "writer-template-testWriteTemplate-temp", null, output, admin);
            }
            assertNotNull(temp, "Write file must not be null");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }
    
}
