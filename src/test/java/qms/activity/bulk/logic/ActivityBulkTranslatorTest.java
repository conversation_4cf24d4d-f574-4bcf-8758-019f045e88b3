package qms.activity.bulk.logic;

import bnext.reference.UserRef;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.util.Locale;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkLocalizedData;
import qms.activity.bulk.dto.ActivityBulkWorkBook;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;
import qms.util.QMSException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@ExtendWith(MockitoExtension.class)
public class ActivityBulkTranslatorTest {

    private ILoggedUser admin;
    private ActivityBulkConfig config;

    @BeforeEach
    public void setup() throws Exception {
        admin = new LoggedUser(new UserRef(10L));
        Locale locale = new Locale("es", "MX");
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testValidTemplate() throws URISyntaxException, IOException, BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");

            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(template);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);

            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            assertNotNull(data, "Data must not be null");
            assertNotNull(data.getRows(), "Data rows must not be null");
            assertEquals(
                    1,
                    data.getRows().size(),
                    "Expected to equals number of values Activity Bulk Rows data"
            );
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }
    
    @Test
    public void testDynamicFields() throws URISyntaxException, IOException, BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");

            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(template);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);

            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            assertNotNull(data, "Data must not be null");
            assertNotNull(data.getRows(), "Data rows must not be null");
            assertEquals(
                    1,
                    data.getRows().size(),
                    "Expected to equals number of values Activity Bulk Rows data"
            );
            final Map<String, Integer> dynamicFieldsHeaders = wb.getRowsSheet().getDynamicFieldsHeaders();
            assertNotNull(dynamicFieldsHeaders, "Dynamic Fields headers must not be null");
            assertNotNull(wb.getRowsSheet().getValues(), "Dynamic Fields headers values not be null");
            assertTrue(!wb.getRowsSheet().getValues().isEmpty(), "Dynamic Fields headers values not be empty");
            final Map<String, String> dynamicFieldsValues = wb.getRowsSheet().getValues().get(0).getDynamicFields();
            assertNotNull(dynamicFieldsValues, "Dynamic Fields values must not be null");
            assertTrue(!dynamicFieldsValues.isEmpty(), "Dynamic Fields values must not be empty");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }
    
    @Test
    public void testEmpty() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path emptyTemplate = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
            assertNotNull(emptyTemplate, "Template must not be null");
            final BulkFileError thrown = assertThrows(BulkFileError.class, () -> {
                final ActivityBulkParser parser = new ActivityBulkParser(config);
                final ActivityBulkWorkBook wb = parser.parse(emptyTemplate);
                final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
                final ActivityBulkLocalizedData data = translator.parse(wb, admin);
                assertNull(data, "Activities must be null.");                
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("parseError"), thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }

}
