package qms.activity.bulk.logic;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import bnext.reference.UserRef;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkWorkBook;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.DataExchangeDTO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.DataExchangeParseError;
import qms.framework.util.MeasureTime;

public class ActivityBulkParserTest {

    private static DataExchangeDTO defaultVariablesData;
    private static DataExchangeDTO defaultRowsData;
    private static Locale locale;
    private static ActivityBulkConfig config;
    
    @BeforeAll
    public static void setup() throws URISyntaxException, IOException, DataExchangeParseError {
        locale = new Locale("es", "MX");
        defaultVariablesData = ActivityBulkDataTestUtils.defaultTemplateVariablesCsv(locale);
        defaultRowsData = ActivityBulkDataTestUtils.defaultTemplateRowsCsv(locale);
        final LoggedUser admin = new LoggedUser(new UserRef(10L));
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testActivityBulkRows() throws URISyntaxException, IOException, DataExchangeParseError {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Activity Bulk Rows template must not be null");
            assertNotNull(template, "Activity Bulk Rows path must not be null");
            assertNotNull(defaultRowsData, "Activity Bulk Rows default rows must not be null");

            final DataExchangeDTO sheet = parser.parseSheet(template, 0);
            assertNotNull(sheet, "Activity Bulk Rows sheet must not be null");
            assertNotNull(sheet.getNumberLines(), "Activity Bulk Rows number of lines must not be null");
            assertNotNull(sheet.getValues(), "Activity Bulk Rows values must not be null");
            assertTrue(sheet.getNumberLines() > 0, "Activity Bulk Rows number of lines must be greater than 0");
            assertTrue(!sheet.getValues().isEmpty(), "Activity Bulk Rows value must not be empty");

            assertEquals(defaultRowsData.getNumberLines(), sheet.getNumberLines(), "Expected to equals number of lines in Activity Bulk Rows data");
            assertEquals(defaultRowsData.getValues().get(0).size(), sheet.getValues().get(0).size(), "Expected to equals number of columns in Activity Bulk Rows data");
            if (!Objects.equals(defaultRowsData, sheet)) {
                for (Integer i = 0, l = defaultRowsData.getValues().size(); i < l; i++) {
                    List<String> defaultRow = defaultRowsData.getValues().get(i);
                    List<String> testRow = sheet.getValues().get(i);
                    assertEquals(defaultRow.size(), testRow.size(), "Expected to equals number of item per row in Activity Bulk Rows data for row " + i);
                    for (Integer j = 0, jl = defaultRow.size(); j < jl; j++) {
                        String defaultCell = defaultRow.get(j);
                        String testCell = testRow.get(j);
                        assertEquals(defaultCell, testCell,
                                "Expected to equals data for cell " + j + " and row " + i + "."
                        );
                    }

                }
            }
            assertEquals(defaultRowsData.getValues(), sheet.getValues(), "Expected to equals number of values Activity Bulk Rows data");
            assertEquals(defaultRowsData, sheet, "Expected to equals Activity Bulk Rows data.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testActivityBulkRows test.");
        }
    }

    @Test
    public void testActivityBulkVariables() throws URISyntaxException, IOException, DataExchangeParseError {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Activity Bulk Variables template must not be null");
            assertNotNull(defaultVariablesData, "Activity Bulk Rows default variables must not be null");

            final DataExchangeDTO sheet = parser.parseSheet(template, 1);
            assertNotNull(sheet, "Activity Bulk Variables sheet must not be null");
            assertNotNull(sheet.getNumberLines(), "Activity Bulk Variables number of lines must not be null");
            assertNotNull(sheet.getValues(), "Activity Bulk Variables values must not be null");
            assertTrue(sheet.getNumberLines() > 0, "Activity Bulk Variables number of lines must be greater than 0");
            assertTrue(!sheet.getValues().isEmpty(), "Activity Bulk Variables value must not be empty");
            assertEquals(defaultVariablesData.getNumberLines(), sheet.getNumberLines(), "Expected to equals number of lines in Activity Bulk Variables data");
            assertEquals(defaultVariablesData.getValues().get(0).size(), sheet.getValues().get(0).size(), "Expected to equals number of columns in Activity Bulk Variables data");
            if (!Objects.equals(defaultVariablesData, sheet)) {
                for (Integer i = 0, l = defaultVariablesData.getValues().size(); i < l; i++) {
                    List<String> defaultRow = defaultVariablesData.getValues().get(i);
                    List<String> testRow = sheet.getValues().get(i);
                    assertEquals(defaultRow.size(), testRow.size(), "Expected to equals number of item per row in Activity Bulk Variables data for row " + i);
                    for (Integer j = 0, jl = defaultRow.size(); j < jl; j++) {
                        String defaultCell = defaultRow.get(j);
                        String testCell = testRow.get(j);
                        assertEquals(defaultCell, testCell,
                                "Expected to equals data for cell " + j + " and row " + i + "."
                        );
                    }
                }
            }
            assertEquals(defaultVariablesData.getValues(), sheet.getValues(), "Expected to equals number of values Activity Bulk Variables data");
            assertEquals(defaultVariablesData, sheet, "Expected to equals Activity Bulk Variables data.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testActivityBulkVariables test.");
        }
    }

    @Test
    public void testActivityBulkFile() throws URISyntaxException, IOException, BulkFileError {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Activity Bulk File template must not be null");
            assertNotNull(defaultRowsData, "Activity Bulk Rows default rows must not be null");
            final ActivityBulkWorkBook wb = parser.parse(template);
            assertNotNull(wb, "Activity Bulk File sheet must not be null");
            assertEquals(defaultVariablesData.getNumberLines(), wb.getVariablesSheet().getValues().size() + 1, "Expected to equals number of variables in Activity Bulk File data");
            assertEquals(defaultRowsData.getNumberLines(), wb.getRowsSheet().getValues().size() + 1, "Expected to equals number of rows in Activity Bulk File data");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testActivityBulkAsData test.");
        }
    }
    
    @Test
    public void testDynamicFields() throws URISyntaxException, IOException, BulkFileError, DataExchangeParseError {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Dynamic Fields template must not be null");
            final ActivityBulkWorkBook wb = parser.parse(template);
            assertNotNull(wb, "Dynamic Fields sheet must not be null");
            final Map<String, Integer> dynamicFieldsHeaders = wb.getRowsSheet().getDynamicFieldsHeaders();
            assertNotNull(dynamicFieldsHeaders, "Dynamic Fields headers must not be null");
            final String headers = StringUtils.join(dynamicFieldsHeaders.keySet(), ",");
            assertEquals(5, dynamicFieldsHeaders.keySet().size(), "Expected to equals number of dynamic Fields headers " + headers);
            assertEquals(0, dynamicFieldsHeaders.get("QMS - Módulo"), "Expected to get dynamic Fields index of QMS - Módulo");
            assertEquals(1, dynamicFieldsHeaders.get("QMS - Branch"), "Expected to get dynamic Fields index of QMS - Branch");
            assertEquals(2, dynamicFieldsHeaders.get("QMS - Categoría"), "Expected to get dynamic Fields index of QMS - Categoría");
            assertNotNull(wb.getRowsSheet().getValues(), "Dynamic Fields headers values not be null");
            assertTrue(!wb.getRowsSheet().getValues().isEmpty(), "Dynamic Fields headers values not be empty");
            final Map<String, String> dynamicFieldsValues = wb.getRowsSheet().getValues().get(0).getDynamicFields();
            assertNotNull(dynamicFieldsValues, "Dynamic Fields values must not be null");
            final String values = StringUtils.join(dynamicFieldsValues.keySet(), ",");
            assertEquals(5, dynamicFieldsValues.keySet().size(), "Expected to equals number of dynamic Fields values " + values);
            assertEquals("Documentos", dynamicFieldsValues.get("QMS - Módulo"), "Expected to get dynamic Fields index of QMS - Módulo");
            assertEquals("QMS 3.1.X", dynamicFieldsValues.get("QMS - Branch"), "Expected to get dynamic Fields index of QMS - Branch");
            assertEquals("Bug", dynamicFieldsValues.get("QMS - Categoría"), "Expected to get dynamic Fields index of QMS - Categoría");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }

}
