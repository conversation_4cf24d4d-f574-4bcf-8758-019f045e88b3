package qms.activity.bulk.logic;

import bnext.reference.UserRef;
import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.activity.bulk.util.ActivityBulkSheetType;
import qms.framework.bulk.dto.BulkSheetData;
import qms.framework.dto.DataExchangeDTO;
import qms.util.QMSException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ActivityBulkCatalogWriterTest {

    private Map<ActivityBulkSheetType, BulkSheetData<String, String>> sheets;
    private ActivityBulkCatalogWriter writer;
    private ILoggedUser admin;
    private Locale locale;

    @BeforeEach
    void setUp() throws IOException, QMSException {
        writer = new ActivityBulkCatalogWriter();
        admin = new LoggedUser(new UserRef(10L));
        locale = new Locale("es", "MX");
        ActivityBulkConfig config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
        sheets = writer.buildCatalogsSheets(1L, config, admin);
    }
    @Test
    void testBuildCatalogsSheetsShouldReturnValidSheets() {
        assertNotNull(sheets);
        assertFalse(sheets.isEmpty());
        assertEquals(13, sheets.size());
    }

    @Test
    void testBuildCatalogsSheetsShouldReturnValidSheetsForAllTypes() {
        assertSheet(ActivityBulkSheetType.TYPES, "Plan Semanal Equipo QMS", "Plan Semanal Equipo QMS");
        assertSheet(ActivityBulkSheetType.FORMS, "Solicitud de Vacaciones", "Solicitud de Vacaciones");
        assertSheet(ActivityBulkSheetType.BUSINESS_UNIT_DEPARTMENTS, "1", "QMS - Bnext");
        assertSheet(ActivityBulkSheetType.IMPLEMENTERS, "dmontoya", "Daniela");
        assertSheet(ActivityBulkSheetType.PRE_IMPLEMENTERS, "dmontoya", "Daniela");
        assertSheet(ActivityBulkSheetType.VERIFIERS, "dmontoya", "Daniela");
        assertSheet(ActivityBulkSheetType.CATEGORIES, "Bug", "Bug");
        assertSheet(ActivityBulkSheetType.OBJECTIVES, "Cumplir con Plan Semanal", "Cumplir con Plan Semanal");
        assertSheet(ActivityBulkSheetType.SOURCES, "Plan Semanal Equipo QMS", "Plan Semanal Equipo QMS");
        assertSheet(ActivityBulkSheetType.PRIORITIES, "Media", "Media");
        assertSheet(ActivityBulkSheetType.CLIENTS, "QMS ADMINISTRACÍON DE PROYECTO", "QMS ADMINISTRACÍON DE PROYECTO");
        assertSheet(ActivityBulkSheetType.PLANNERS, "Administración de Proyecto", "Administración de Proyecto");
        assertSheet(ActivityBulkSheetType.TASKS, "Administración de Proyecto", "Administración de Proyecto");
    }

    private void assertSheet(ActivityBulkSheetType type, String expectedFirstValue, String expectedSecondValue) {
        BulkSheetData<String, String> sheet = sheets.get(type);
        assertNotNull(sheet);
        DataExchangeDTO data = sheet.getData();
        assertNotNull(data);
        List<List<String>> values = data.getValues();
        assertNotNull(values);
        assertFalse(values.isEmpty());
        assertTrue(values.size() > 1);
        List<String> row = values.get(1);
        assertNotNull(row);
        assertFalse(row.isEmpty());
        assertEquals(expectedFirstValue, row.get(0));
        assertEquals(expectedSecondValue, row.get(1));
    }
}