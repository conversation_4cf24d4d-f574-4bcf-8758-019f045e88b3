package qms.activity.bulk.logic;

import bnext.exception.MakePersistentException;
import bnext.reference.UserRef;
import java.nio.file.Path;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.DAOInterface.ActivityDataExchangeDAOMock;
import qms.activity.DAOInterface.IActivityDataExchangeDAO;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.activity.entity.ActivityBulkFile;
import qms.framework.bulk.dto.IBulkUploadResult;
import qms.framework.bulk.entity.IBulkLog;
import qms.framework.bulk.imp.ActivityHandler;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@ExtendWith(MockitoExtension.class)
public class ActivityBulkUploaderTest {

    private ILoggedUser admin;
    private Locale locale;
    private ActivityBulkConfig config;
    private IActivityDataExchangeDAO dao;
    private IBulkLog log;

    @BeforeEach
    public void setup() throws Exception {
        admin = new LoggedUser(new UserRef(10L));
        locale = new Locale("es", "MX");
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
        dao = new ActivityDataExchangeDAOMock();
        log = new ActivityBulkFile(1L);
    }

    @Test
    public void testValidTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload( "uploader-testValidTemplate", template, log, admin);
            assertNotNull(result, "Upload result must not be null.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }

    @Test
    public void testEmpty() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = handler.upload( "uploader-testEmpty", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }
    
    @Test
    public void testMissingFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload("uploader-testMissingFields", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingFields test.");
        }
    }
    
    @Test
    public void testInvalidLength() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getInvalidLengthXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload("uploader-testInvalidLength", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidLength test.");
        }
    }

    @Test
    public void testMissingTask() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {        
            final Path template = ActivityBulkDataTestUtils.getMissingTaskXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload( "uploader-testMissingTask", template, log, admin);
            assertNotNull(result, "Upload result must not be null.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingTask test.");
        }
    }

    @Test
    public void testInvalidHeaders() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {          
            final Path template = ActivityBulkDataTestUtils.getInvalidHeadersXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload( "uploader-testInvalidHeaders", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidHeaders test.");
        }
    }

    @Test
    public void testDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload( "uploader-testDynamicFields", template, log, admin);
            assertNotNull(result, "Upload result must not be null.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }
    
    @Test
    public void testInvalidDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {          
            final Path template = ActivityBulkDataTestUtils.getInvalidDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload("uploader-testInvalidDynamicFields", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidDynamicFields test.");
        }
    }
    
    @Test
    public void testLongDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {          
            final Path template = ActivityBulkDataTestUtils.getLongDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult result = uploader.upload( "uploader-testLongDynamicFields", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }

}
