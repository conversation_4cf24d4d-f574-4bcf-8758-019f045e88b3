package qms.activity.bulk.logic;

import bnext.exception.MakePersistentException;
import bnext.reference.UserRef;
import java.nio.file.Path;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.DAOInterface.ActivityDataExchangeDAOMock;
import qms.activity.DAOInterface.IActivityDataExchangeDAO;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.activity.bulk.util.ActivityBulkDynamicFieldError;
import qms.activity.entity.ActivityBulkFile;
import qms.framework.bulk.dto.IBulkSaveResult;
import qms.framework.bulk.dto.IBulkUploadResult;
import qms.framework.bulk.entity.IBulkLog;
import qms.framework.bulk.imp.ActivityHandler;
import qms.framework.bulk.util.BulkConstants;
import qms.framework.bulk.util.BulkError;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MaximumCharactersError;
import qms.framework.util.MeasureTime;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@ExtendWith(MockitoExtension.class)
public class ActivityBulkManagerTest {

    private ILoggedUser admin;
    private Locale locale;
    private ActivityBulkConfig config;
    private IActivityDataExchangeDAO dao;
    private IBulkLog log;

    @BeforeEach
    public void setup() throws Exception {
        admin = new LoggedUser(new UserRef(10L));
        locale = new Locale("es", "MX");
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
        dao = new ActivityDataExchangeDAOMock();
        log = new ActivityBulkFile(1L);
    }

    @Test
    public void testValidTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult u = handler.uploadAction("testValidTemplate", template, log, admin);
            final IBulkSaveResult result = u.getSaveResult();
            assertNotNull(result.getResultRecords(), "Save result must not be null.");
            assertTrue(!result.getResultRecords().isEmpty(), "Save activities must not be empty.");
            assertTrue(result.getRowsErrors().isEmpty(), "Save row errors must be empty.");
            assertTrue(result.getVariablesErrors().isEmpty(), "Save variables errors must be empty.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }

    @Test
    public void testEmpty() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {

            final BulkFileError thrown = assertThrows(BulkFileError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
                final IBulkUploadResult u = handler.uploadAction("testEmpty", template, log, admin);
                final IBulkSaveResult result = u.getSaveResult();
                assertNull(result.getResultRecords(), "Save result must be null.");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("parseError"), thrown.getMessage());

        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }

    @Test
    public void testMissingFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
                final IBulkUploadResult u = handler.uploadAction("testMissingFields", template, log, admin);
                final IBulkSaveResult result = u.getSaveResult();
                assertNull(result.getResultRecords(), "Save result must be null.");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("dataValidationError"), thrown.getMessage());

        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingFields test.");
        }
    }

    @Test
    public void testInvalidLength() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final MaximumCharactersError thrown = assertThrows(MaximumCharactersError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidLengthXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
                final IBulkUploadResult u = handler.uploadAction("testInvalidLength", template, log, admin);
                final IBulkSaveResult result = u.getSaveResult();
                assertNull(result.getResultRecords(), "Save result must be null.");
            }, "Validation must fail");
            final String message = config.getTags().getTag("maximumCharactersExceeded")
                    .replace("{maxCharacters}", BulkConstants.MAX_DESCRIPTION_LENGTH.toString());
            assertEquals(message, thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidLength test.");
        }
    }

    @Test
    public void testMissingTask() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getMissingTaskXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
                final IBulkUploadResult u = handler.uploadAction("testMissingTask", template, log, admin);
                final IBulkSaveResult result = u.getSaveResult();
                assertNull(result.getResultRecords(), "Save result must be null.");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("dataValidationError"), thrown.getMessage());

        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingTask test.");
        }
    }

    @Test
    public void testInvalidHeaders() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidHeadersXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
                final IBulkUploadResult u = handler.uploadAction("testInvalidHeaders", template, log, admin);
                final IBulkSaveResult result = u.getSaveResult();
                assertNull(result.getResultRecords(), "Save result must be null.");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("headersError"), thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidHeaders test.");
        }
    }

    @Test
    public void testDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
            final IBulkUploadResult u = handler.uploadAction("testDynamicFields", template, log, admin);
            final IBulkSaveResult result = u.getSaveResult();
            assertNotNull(result.getResultRecords(), "Save result must not be null.");
            assertTrue(!result.getResultRecords().isEmpty(), "Save activities must not be empty.");
            assertTrue(result.getRowsErrors().isEmpty(), "Save row errors must be empty.");
            assertTrue(result.getVariablesErrors().isEmpty(), "Save variables errors must be empty.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }

    @Test
    public void testInvalidDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getInvalidDynamicFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
                final IBulkUploadResult u = handler.uploadAction("testInvalidDynamicFields", template, log, admin);
                final IBulkSaveResult result = u.getSaveResult();
                assertNull(result.getResultRecords(), "Save result must be null.");
            }, "Validation must fail");
            Throwable cause = thrown.getCause();
            assertNotNull(cause);
            assertInstanceOf(ActivityBulkDynamicFieldError.class, cause);
            assertEquals(config.getTags().getTag("invalidDynamicFields"), thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidDynamicFields test.");
        }
    }

    @Test
    public void testLongDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final BulkError thrown = assertThrows(BulkError.class, () -> {
                final Path template = ActivityBulkDataTestUtils.getLongDynamicFieldsXlsx();
                assertNotNull(template, "Template must not be null");
                final ActivityHandler handler = new ActivityHandler(locale, config, null, true, false, dao, admin);
                final IBulkUploadResult u = handler.uploadAction("testLongDynamicFields", template, log, admin);
                final IBulkSaveResult result = u.getSaveResult();
                assertNull(result.getResultRecords(), "Save result must be null.");
            }, "Validation must fail");
            Throwable cause = thrown.getCause();
            assertNotNull(cause);
            assertInstanceOf(ActivityBulkDynamicFieldError.class, cause);
            assertEquals(config.getTags().getTag("invalidDynamicFields"), thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }

}
