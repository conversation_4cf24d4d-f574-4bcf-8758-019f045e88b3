package qms.activity.bulk.logic;

import DPMS.DAOInterface.IDocumentDataExchangeDAO;
import Framework.Config.Utilities;
import bnext.exception.MakePersistentException;
import java.nio.file.Path;
import java.util.Locale;
import javax.servlet.ServletContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDataExchangeDAO;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.activity.entity.ActivityBulkFile;
import qms.framework.bulk.dto.IBulkUploadResult;
import qms.framework.bulk.entity.IBulkLog;
import qms.framework.bulk.imp.ActivityHandler;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;
import qms.framework.util.SettingsUtil;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.util.IntegralTestConfiguration;
import qms.util.TestSessionUtils;
import qms.util.TestUtils;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@IntegralTestConfiguration
public class ActivityBulkUploaderIT {

    @Autowired
    ServletContext servletContext;

    private ILoggedUser admin;
    private Locale locale;
    private IActivityDataExchangeDAO activityDao;
    private IDocumentDataExchangeDAO documentDao;
    private ActivityBulkConfig config;
    private IBulkLog log;


    @BeforeAll
    public void beforeAll() throws Exception {
        admin = TestSessionUtils.getAdmin(servletContext);
        locale = new Locale("es", "MX");
        activityDao = Utilities.getBean(IActivityDataExchangeDAO.class, servletContext);
        documentDao = Utilities.getBean(IDocumentDataExchangeDAO.class, servletContext);
        log = new ActivityBulkFile(1L);
        
        TimesheetDataSourceDto tsDs = activityDao.getProjectCatalogs(admin);
        config = new ActivityBulkConfig(
                "Test-App-QMS", 
                SettingsUtil.getAppUrl(),
                activityDao,
                documentDao,
                locale, 
                tsDs,
                TestUtils.getFileManager()
        );
    }

    @Test
    public void testValidTemplate() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload( "IT-testValidTemplate", template, log, admin);
            assertNotNull(result, "Upload result must not be null.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }
    
    @Test
    public void testEmpty() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload("IT-testEmpty", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }
    
    @Test
    public void testMissingFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload("IT-testMissingFields", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingFields test.");
        }
    }
    
    @Test
    public void testInvalidLength() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload( "IT-testInvalidLength", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidLength test.");
        }
    }
    
    @Test
    public void testMissingTask() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getInvalidLengthXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload("IT-testMissingTask", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingTask test.");
        }
    }
    
    @Test
    public void testInvalidHeaders() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload("IT-testInvalidHeaders", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testgetInvalidHeadersUpload test.");
        }
    }
    
    @Test
    public void testDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload("IT-testDynamicFields", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }
    
    @Test
    public void testInvalidDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getInvalidDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload( "IT-testInvalidDynamicFields", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidDynamicFields test.");
        }
    }
    
    @Test
    public void testLongDynamicFields() throws Exception, MakePersistentException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getLongDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityHandler uploader = new ActivityHandler(locale, config, null, true, false, activityDao, admin);
            final IBulkUploadResult result = uploader.upload("IT-testLongDynamicFields", template, log, admin);
            assertNotNull(result, "Upload result must be true.");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testLongDynamicFields test.");
        }
    }
    
}
