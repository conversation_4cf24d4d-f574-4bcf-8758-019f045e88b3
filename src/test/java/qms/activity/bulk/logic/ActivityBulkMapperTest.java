package qms.activity.bulk.logic;

import bnext.reference.UserRef;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkActivityIndex;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkLocalizedData;
import qms.activity.bulk.dto.ActivityBulkWorkBook;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;
import qms.util.QMSException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@ExtendWith(MockitoExtension.class)
public class ActivityBulkMapperTest {

    private ILoggedUser admin;
    private ActivityBulkConfig config;

    @BeforeEach
    public void setup() throws Exception {
        admin = new LoggedUser(new UserRef(10L));
        Locale locale = new Locale("es", "MX");
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testValidTemplate() throws URISyntaxException, IOException, BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(template);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
            
                        
            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            assertNotNull(data, "Data must not be null");
            assertNotNull(data.getRows(), "Data rows must not be null");
            assertEquals(
                    1,
                    data.getRows().size(),
                    "Expected to equals number of values Activity Bulk Rows data"
            );
            final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
            final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
            assertNotNull(activities.getActivities(), "Activities must not be null");
            assertTrue(!activities.getActivities().isEmpty(), "Activities must not be empty");
            assertEquals(
                    1,
                    activities.getActivities().size(),
                    "Expected to equals number of activities"
            );
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }
    
    @Test
    public void testDynamicFields() throws URISyntaxException, IOException, BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            
            final Path template = ActivityBulkDataTestUtils.getDynamicFieldsXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(template);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
            
                        
            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            assertNotNull(data, "Data must not be null");
            assertNotNull(data.getRows(), "Data rows must not be null");
            assertEquals(
                    1,
                    data.getRows().size(),
                    "Expected to equals number of values Activity Bulk Rows data"
            );
            final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
            final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
            assertNotNull(activities.getActivities(), "Activities must not be null");
            assertTrue(!activities.getActivities().isEmpty(), "Activities must not be empty");
            assertEquals(
                    1,
                    activities.getActivities().size(),
                    "Expected to equals number of activities"
            );
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testDynamicFields test.");
        }
    }
    
    
    @Test
    public void testEmpty() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path emptyTemplate = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
            assertNotNull(emptyTemplate, "Template must not be null");
            final BulkFileError thrown = assertThrows(BulkFileError.class, () -> {
                final ActivityBulkParser parser = new ActivityBulkParser(config);
                final ActivityBulkWorkBook wb = parser.parse(emptyTemplate);
                final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
                final ActivityBulkLocalizedData data = translator.parse(wb, admin);
                final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
                final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
                assertNull(activities, "Activities must be null.");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("parseError"), thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }

}
