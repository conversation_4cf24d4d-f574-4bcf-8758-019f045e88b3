package qms.activity.bulk.validation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import bnext.reference.UserRef;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.util.Locale;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkActivityIndex;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkLocalizedData;
import qms.activity.bulk.dto.ActivityBulkWorkBook;
import qms.activity.bulk.logic.ActivityBulkMapper;
import qms.activity.bulk.logic.ActivityBulkParser;
import qms.activity.bulk.logic.ActivityBulkTranslator;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.framework.bulk.util.BulkConstants;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MaximumCharactersError;
import qms.framework.util.MeasureTime;
import qms.util.QMSException;

public class ActivityBulkTypeValidatorTest {

    private static Locale locale;
    private static ActivityBulkConfig config;
    private static LoggedUser admin;

    @BeforeAll
    public static void setup() throws URISyntaxException, IOException {
        locale = new Locale("es", "MX");
        admin = new LoggedUser(new UserRef(10L));
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testValidTemplate() throws URISyntaxException, IOException, BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(template);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
            final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
            final ActivityBulkTypeValidator validator = new ActivityBulkTypeValidator(config);
            final Boolean valid = validator.valid(activities);
            assertEquals(true, valid, "Save must be valid");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }

    @Test
    public void testEmpty() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path emptyTemplate = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
            assertNotNull(emptyTemplate, "Template must not be null");
            final BulkFileError thrown = assertThrows(BulkFileError.class, () -> {
                final ActivityBulkParser parser = new ActivityBulkParser(config);
                final ActivityBulkWorkBook wb = parser.parse(emptyTemplate);
                final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
                final ActivityBulkLocalizedData data = translator.parse(wb, admin);
                final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
                final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
                final ActivityBulkTypeValidator validator = new ActivityBulkTypeValidator(config);
                final Boolean valid = validator.valid(activities);
                assertTrue(!valid, "Empty file must be invalid");
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("parseError"), thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }

    @Test
    public void testMissingFields() throws URISyntaxException, IOException, BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path emptyTemplate = ActivityBulkDataTestUtils.getMissingFieldsXlsx();
            assertNotNull(emptyTemplate, "Template must not be null");
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(emptyTemplate);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
            final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
            final ActivityBulkTypeValidator validator = new ActivityBulkTypeValidator(config);
            final Boolean valid = validator.valid(activities);
            assertTrue(!valid, "Missing fields must be invalid");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingFields test.");
        }
    }

    @Test
    public void testInvalidLength() throws URISyntaxException, IOException, BulkFileError {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final MaximumCharactersError thrown = assertThrows(MaximumCharactersError.class, () -> {
                final Path emptyTemplate = ActivityBulkDataTestUtils.getInvalidLengthXlsx();
                assertNotNull(emptyTemplate, "Template must not be null");
                final ActivityBulkParser parser = new ActivityBulkParser(config);
                final ActivityBulkWorkBook wb = parser.parse(emptyTemplate);
                final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
                final ActivityBulkLocalizedData data = translator.parse(wb, admin);
                final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
                final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
                final ActivityBulkTypeValidator validator = new ActivityBulkTypeValidator(config);
                final Boolean valid = validator.valid(activities);
                assertTrue(!valid, "Missing fields must be invalid");
            }, "Validation must fail");
            final String message = config.getTags().getTag("maximumCharactersExceeded")
                    .replace("{maxCharacters}", BulkConstants.MAX_DESCRIPTION_LENGTH.toString());
            assertEquals(message, thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testInvalidLength test.");
        }
    }

    @Test
    public void testMissingTask() throws URISyntaxException, IOException, BulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path emptyTemplate = ActivityBulkDataTestUtils.getMissingTaskXlsx();
            assertNotNull(emptyTemplate, "Template must not be null");
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(emptyTemplate);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            final ActivityBulkMapper mapper = new ActivityBulkMapper(config);
            final ActivityBulkActivityIndex activities = mapper.getNewParsedIndex(true, data, config.getTimesheetDataSource(), admin);
            final ActivityBulkTypeValidator validator = new ActivityBulkTypeValidator(config);
            final Boolean valid = validator.valid(activities);
            assertTrue(!valid, "Missing fields must be invalid");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testMissingTask test.");
        }
    }
}
