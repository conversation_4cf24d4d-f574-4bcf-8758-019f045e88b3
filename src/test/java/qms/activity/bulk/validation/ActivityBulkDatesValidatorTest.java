package qms.activity.bulk.validation;

import bnext.reference.UserRef;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.util.Locale;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import qms.access.dto.LoggedUser;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkLocalizedData;
import qms.activity.bulk.dto.ActivityBulkWorkBook;
import qms.activity.bulk.logic.ActivityBulkParser;
import qms.activity.bulk.logic.ActivityBulkTranslator;
import qms.activity.bulk.util.ActivityBulkDataTestUtils;
import qms.activity.bulk.util.ActivityBulkFileError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;
import qms.util.QMSException;

public class ActivityBulkDatesValidatorTest {

    private static Locale locale;
    private static ActivityBulkConfig config;
    private static LoggedUser admin;

    @BeforeAll
    public static void setup() throws URISyntaxException, IOException {
        locale = new Locale("es", "MX");
        admin = new LoggedUser(new UserRef(10L));
        config = ActivityBulkDataTestUtils.mockActivityBulkConfig(locale, admin);
    }

    @Test
    public void testValidTemplate() throws URISyntaxException, IOException, ActivityBulkFileError, QMSException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path template = ActivityBulkDataTestUtils.getTemplateValidXlsx();
            assertNotNull(template, "Template must not be null");
            final ActivityBulkParser parser = new ActivityBulkParser(config);
            final ActivityBulkWorkBook wb = parser.parse(template);
            final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
            final ActivityBulkLocalizedData data = translator.parse(wb, admin);
            final ActivityBulkDatesValidator validator = new ActivityBulkDatesValidator(config);
            final Boolean valid = validator.valid(data);
            assertEquals(true, valid, "Save must be valid");
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testValidTemplate test.");
        }
    }

    @Test
    public void testEmpty() throws URISyntaxException, IOException {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Path emptyTemplate = ActivityBulkDataTestUtils.getTemplateEmptyXlsx();
            assertNotNull(emptyTemplate, "Template must not be null");
            final ActivityBulkFileError thrown = assertThrows(ActivityBulkFileError.class, () -> {
                final ActivityBulkParser parser = new ActivityBulkParser(config);
                final ActivityBulkWorkBook wb = parser.parse(emptyTemplate);
                final ActivityBulkTranslator translator = new ActivityBulkTranslator(config);
                final ActivityBulkLocalizedData data = translator.parse(wb, admin);
                final ActivityBulkDatesValidator validator = new ActivityBulkDatesValidator(config);
                validator.valid(data);
            }, "Validation must fail");
            assertEquals(config.getTags().getTag("parseError"), thrown.getMessage());
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in testEmpty test.");
        }
    }

}
