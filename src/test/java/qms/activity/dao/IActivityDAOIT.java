package qms.activity.dao;

import Framework.Config.Utilities;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletContext;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.dto.ActivityProgressRefreshDTO;
import qms.activity.dto.ActivityProgressTreeDto;
import qms.activity.entity.Activity;
import qms.activity.util.CommitmentTask;
import qms.framework.rest.SecurityRootUtils;
import qms.timesheet.dto.PlannedActivityValue;
import qms.timesheet.dto.TimesheetDialogDto;
import qms.util.GridFilter;
import qms.util.IntegralTestConfiguration;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@IntegralTestConfiguration
public class IActivityDAOIT {

    private static final Logger LOGGER = LoggerFactory.getLogger(IActivityDAOIT.class);

    @Autowired
    ServletContext servletContext;

    private ILoggedUser admin;

    @BeforeAll
    public void beforeAll() throws Exception {
        admin = SecurityRootUtils.getFirstAdminDto(servletContext);

    }

    @Test
    public void testLoadProgressTreeByParentIdAccessActivity() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final Module module = Module.ACTIVITY;
        final List<ActivityProgressRefreshDTO> results = dao.loadProgressTreeByParentId(module, 1L, 0);
        LOGGER.debug("testAdminLoadProgressTreeByParentIdAccess {} results", results == null ? 0 : results.size());
        assertNotNull(results, "Progress tree must not be null for module " + module.getKey());
    }

    @Test
    public void testGetActivitySurveyCaptureDto() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getActivitySurveyCaptureDto(1L));
    }

    @Test
    public void testGetParentImplementationActivitiesActivity() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final Module module = Module.ACTIVITY;
        final CommitmentTask commitmentTask = CommitmentTask.IMPLEMENTATION;
        final List<ActivityProgressTreeDto> results = dao.getParentActivities(
                module,
                commitmentTask,
                new Date(),
                new Date(),
                true,
                new HashMap<>(),
                admin
        );
        LOGGER.debug("testGetParentImplementationActivitiesActivity {} results", results == null ? 0 : results.size());
        assertNotNull(results, "Load parent activities not be null for module " + module.getKey());
    }

    @Test
    public void testGetParentImplementationActivitiesFilterActivity() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final Module module = Module.ACTIVITY;
        final CommitmentTask commitmentTask = CommitmentTask.IMPLEMENTATION;
        final Map<String, Object> params = new HashMap<>();
        params.put("parentTreeActivityId", 3L);
        final List<ActivityProgressTreeDto> results = dao.getParentActivities(
                module,
                commitmentTask,
                new Date(),
                new Date(),
                false,
                true,
                1L,
                IActivityDAO.getParentTreeFilter(21L),
                params,
                admin
        );
        LOGGER.debug("testGetParentImplementationActivitiesFilterActivity {} results", results == null ? 0 : results.size());
        assertNotNull(results, "Load parent activities not be null for module " + module.getKey());
    }

    @Test
    public void testSwapActivitiesById() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final List<PlannedActivityValue> results = dao.getSwapActivitiesById(2L);
        LOGGER.debug("testSwapActivitiesById {} results", results == null ? 0 : results.size());
        assertNotNull(results, "Load swap activities not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserShowImp() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final List<PlannedActivityValue> results = dao.getTaskActivitiesByLoggedUser(admin, true);
        LOGGER.debug("testGetTaskActivitiesByLoggedUserShowImp {} results", results == null ? 0 : results.size());
        assertNotNull(results, "Get task activities by logged user and show imp must not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserNotShowImp() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final List<PlannedActivityValue> results = dao.getTaskActivitiesByLoggedUser(admin, false);
        LOGGER.debug("testGetTaskActivitiesByLoggedUserNotShowImp {} results", results == null ? 0 : results.size());
        assertNotNull(results, "Get task activities by logged user and not show imp must not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserRowsMap() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final GridFilter filter = new GridFilter();
        filter.setAsumeAlias(false);
        final GridInfo<PlannedActivityValue> results = dao.getTaskActivitiesByLoggedUserRows(filter, admin);
        LOGGER.debug("testGetTaskActivitiesByLoggedUserRowsMapShowImp {} results", results != null);
        assertNotNull(results, "Get task activities rows by logged user must not be null");
    }


    @Test
    public void testGetTaskActivitiesByLoggedUserRowsMapShowImpNoStatus() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final GridFilter filter = new GridFilter();
        filter.setAsumeAlias(false);
        final GridInfo<TimesheetDialogDto> results = dao.getTaskActivitiesByLoggedUserRowsMap(filter, admin, 0, true);
        LOGGER.debug("testGetTaskActivitiesByLoggedUserRowsMapShowImpNoStatus {} results", results == null);
        assertNotNull(results, "Get task activities rows by logged user and show imp must not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserRowsMapNotShowImpNoStatus() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final GridFilter filter = new GridFilter();
        filter.setAsumeAlias(false);
        final GridInfo<TimesheetDialogDto> results = dao.getTaskActivitiesByLoggedUserRowsMap(filter, admin, 0, false);
        LOGGER.debug("testGetTaskActivitiesByLoggedUserRowsMapShowImpNoStatus {} results", results == null);
        assertNotNull(results, "Get task activities rows by logged user and not show imp must not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserRowsMapShowImpOpen() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final GridFilter filter = new GridFilter();
        filter.setAsumeAlias(false);
        final GridInfo<TimesheetDialogDto> results = dao.getTaskActivitiesByLoggedUserRowsMap(
                filter,
                admin,
                Activity.STATUS.IN_PROCESS.getValue(),
                true
        );
        LOGGER.debug("testGetTaskActivitiesByLoggedUserRowsMapShowImpNoStatus {} results", results == null);
        assertNotNull(results, "Get task activities rows by logged user and show imp must not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserRowsMapNotShowImpOpen() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final GridFilter filter = new GridFilter();
        filter.setAsumeAlias(false);
        final GridInfo<TimesheetDialogDto> results = dao.getTaskActivitiesByLoggedUserRowsMap(
                filter,
                admin,
                Activity.STATUS.IN_PROCESS.getValue(),
                false
        );
        LOGGER.debug("testGetTaskActivitiesByLoggedUserRowsMapShowImpNoStatus {} results", results == null);
        assertNotNull(results, "Get task activities rows by logged user and not show imp must not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserRowsMapShowImpClosed() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final GridFilter filter = new GridFilter();
        filter.setAsumeAlias(false);
        final GridInfo<TimesheetDialogDto> results = dao.getTaskActivitiesByLoggedUserRowsMap(
                filter,
                admin,
                Activity.STATUS.VERIFIED.getValue(),
                true
        );
        LOGGER.debug("testGetTaskActivitiesByLoggedUserRowsMapShowImpNoStatus {} results", results == null);
        assertNotNull(results, "Get task activities rows by logged user and show imp must not be null");
    }

    @Test
    public void testGetTaskActivitiesByLoggedUserRowsMapNotShowImpClosed() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        final GridFilter filter = new GridFilter();
        filter.setAsumeAlias(false);
        final GridInfo<TimesheetDialogDto> results = dao.getTaskActivitiesByLoggedUserRowsMap(
                filter,
                admin,
                Activity.STATUS.VERIFIED.getValue(),
                false
        );
        LOGGER.debug("testGetTaskActivitiesByLoggedUserRowsMapShowImpNoStatus {} results", results == null);
        assertNotNull(results, "Get task activities rows by logged user and not show imp must not be null");
    }
    
    @Test
    public void testGetActivityVerificactionInfo() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getActivityVerificactionInfo(1L));
    }
    
    @Test
    public void testGetActivityProgramInfo() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getActivityProgramInfo(1L));
    }
    
    @Test
    public void testGetActivityImplementationInfo() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getActivityImplementationInfo(1L));
    }
    
    @Test
    public void testGetPlanningOwnerRecords() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getPlanningOwnerRecords(
                Module.ACTIVITY,
                CommitmentTask.IMPLEMENTATION,
                Collections.singletonList(1L),
                new Date(),
                new Date()
        ));
    }

    @Test
    public void testGetPlanningOwnerRecordsNoDates() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getPlanningOwnerRecords(
                Module.ACTIVITY,
                CommitmentTask.IMPLEMENTATION,
                Collections.singletonList(1L),
                null,
                null
        ));
    }
    
    @Test
    public void testGetPlanningOwnerRecordsNoCommitment() {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getPlanningOwnerRecords(
                Module.ACTIVITY,
                null,
                Collections.singletonList(1L),
                new Date(),
                new Date()
        ));
    }


}
