package qms.activity.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import mx.bnext.core.util.Loggable;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import qms.activity.dto.ActivityDiff;
import qms.activity.dto.ActivityInfoDTO;
import qms.activity.dto.load.ActivityLoadDTO;
import qms.activity.util.ActivityField;
import qms.util.ReflectionUtil;
import qms.util.TestUtils;

/**
 *
 * <AUTHOR>
 */
public class ActivityUtilTest {

    private static final Logger LOGGER = Loggable.getLogger(ActivityUtilTest.class);

    private ActivityDiff testChange(ActivityField field, final Object value) {
        final String fieldName = field.getFieldName();
        final ActivityInfoDTO info = new ActivityInfoDTO();
        final ActivityDiff diff = new ActivityDiff(info);
        ActivityUtil.evaluateDiff(diff, field, value);
        final Map<String, Object> values = diff.getValues();
        assertEquals(true, values.containsKey(fieldName));
        assertEquals(value, values.get(fieldName));
        final Object sourceValue = ReflectionUtil.getValue(info, fieldName);
        assertEquals(value, sourceValue);
        return diff;
    }

    @Test
    public void testDefaultChange() throws InstantiationException {
        LOGGER.debug("change with default values");
        final List<ActivityField> fields = Arrays.asList(ActivityField.values()).stream()
                .filter((field)-> ActivityUtil.CHANGE_AVAILABLE_FIELD_NAMES.contains(field.getFieldName()))
                .collect(Collectors.toList());
        fields.stream().forEach(field -> {
            final Class fieldType = field.getType();
            final String fieldName = field.getFieldName();
            try {
                final Object value = TestUtils.getDefaultValue(fieldType);
                final ActivityDiff diff = testChange(field, value);
                final ActivityLoadDTO changes = diff.getChanges();
                assertEquals(true, ReflectionUtil.getValue(changes, fieldName) != null);
                assertEquals(value, ReflectionUtil.getValue(changes, fieldName));
            } catch (final InstantiationException | IllegalAccessException ex) {
                throw new RuntimeException(
                        "Invalid type " + fieldType.getName() + " of field " + fieldName,
                        ex
                );
            }
        });
    }

    @Test
    public void testNullChange() throws InstantiationException {
        LOGGER.debug("change with null values");
        final List<ActivityField> fields = Arrays.asList(ActivityField.values()).stream()
                .filter((field)-> ActivityUtil.CHANGE_AVAILABLE_FIELD_NAMES.contains(field.getFieldName()))
                .collect(Collectors.toList());
        fields.stream().forEach(field -> {
            final Object value = null;
            final ActivityDiff diff = testChange(field, value);
            final ActivityLoadDTO changes = diff.getChanges();
            assertEquals(true, ReflectionUtil.getValue(changes, field.getFieldName()) == null);
        });
    }

}
