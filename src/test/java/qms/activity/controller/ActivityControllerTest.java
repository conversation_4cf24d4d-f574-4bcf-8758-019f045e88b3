/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.controller;

import java.lang.reflect.Method;
import java.util.Objects;
import mx.bnext.core.util.Loggable;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import qms.activity.rest.ActivityAuditController;
import qms.activity.rest.ActivityController;
import qms.activity.rest.ActivityFindingController;
import qms.activity.rest.ActivityOutstandingSurveysController;
import qms.activity.rest.ActivityPlannerController;
import qms.framework.util.TestAspectJUtils;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 *
 * <AUTHOR>
 */
public class ActivityControllerTest {

    private static final Logger LOGGER = Loggable.getLogger(ActivityControllerTest.class);

    private static final String CUSTOM_SECURITY_METHOD_NAME = "activityByCode";

    @Test
    public void validActivityMethods() {
        LOGGER.debug("Testing methods ActivityController...");
        final Class<ActivityController> clazz = ActivityController.class;
        Method[] auditMethods = clazz.getDeclaredMethods();
        for (Method m : auditMethods) {
            if (TestAspectJUtils.isAspectJInternalMethod(m)) {
                continue;
            }
            // Mismos valores en la anotación RequestMapping
            if (m.getAnnotation(RequestMapping.class) != null) {
                // Debe tener autorización
                if (!Objects.equals(m.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            m.getAnnotation(PreAuthorize.class)
                            , "Method " + m.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación GetMapping
            if (m.getAnnotation(GetMapping.class) != null) {
                // Debe tener autorización
                if (!Objects.equals(m.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            m.getAnnotation(PreAuthorize.class)
                            , "Method " + m.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación PostMapping
            if (m.getAnnotation(PostMapping.class) != null) {
                // Debe tener autorización
                if (!Objects.equals(m.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            m.getAnnotation(PreAuthorize.class)
                            , "Method " + m.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
        }
    }

    @Test
    public void validAuditMethods() {
        LOGGER.debug("Testing methods ActivityAuditController...");
        final Class<ActivityAuditController> clazz = ActivityAuditController.class;
        Method[] auditMethods = clazz.getDeclaredMethods();
        for (Method m : ActivityController.class.getDeclaredMethods()) {
            if (TestAspectJUtils.isAspectJInternalMethod(m)) {
                continue;
            }
            Method auMethod = getMethod(auditMethods, m.getName(), m.getParameterCount());
            // Existe el método
            assertNotNull(auMethod, "ActivityAuditController - Method " + m.getName() + " not exists.");
            assertEquals(m.getName(), auMethod.getName(), "ActivityAuditController - Method " + m.getName() + " not exists.");
            // Mismos tipos parámetros
            if (m.getParameterCount() > 0) {
                Class<?>[] types = m.getParameterTypes();
                Class<?>[] types1 = auMethod.getParameterTypes();
                assertArrayEquals(types, types1, "ActivityAuditController - Method " + m.getName() + " parameters are different.");
            }
            // Mismos valores en la anotación RequestMapping
            if (m.getAnnotation(RequestMapping.class) != null) {
                RequestMapping annotation = auMethod.getAnnotation(RequestMapping.class);
                assertNotNull(annotation, "Method " + auMethod.getName() + " must have RequestMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(RequestMapping.class).value(), annotation.value(),
                        "ActivityAuditController - Method " + auMethod.getName() + " RequestMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(auMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            auMethod.getAnnotation(PreAuthorize.class),
                            "Method " + auMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación GetMapping
            if (m.getAnnotation(GetMapping.class) != null) {
                GetMapping annotation = auMethod.getAnnotation(GetMapping.class);
                assertNotNull(annotation, "Method " + auMethod.getName() + " must have GetMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(GetMapping.class).value(), annotation.value(),
                        "ActivityAuditController - Method " + auMethod.getName() + " GetMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(auMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            auMethod.getAnnotation(PreAuthorize.class),
                            "Method " + auMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación PostMapping
            if (m.getAnnotation(PostMapping.class) != null) {
                PostMapping annotation = auMethod.getAnnotation(PostMapping.class);
                assertNotNull(annotation, "Method " + auMethod.getName() + " must have PostMapping annotation in " + clazz.getName());
                assertArrayEquals(
                        m.getAnnotation(PostMapping.class).value(), annotation.value(),
                        "ActivityAuditController - Method " + auMethod.getName() + " PostMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(auMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            auMethod.getAnnotation(PreAuthorize.class),
                            "Method " + auMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación GetMapping
            if (m.getAnnotation(GetMapping.class) != null) {
                GetMapping annotation = auMethod.getAnnotation(GetMapping.class);
                assertNotNull(annotation, "Method " + auMethod.getName() + " must have GetMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(GetMapping.class).value(), annotation.value(),
                        "ActivityAuditController - Method " + auMethod.getName() + " GetMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(auMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            auMethod.getAnnotation(PreAuthorize.class),
                            "Method " + auMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación PostMapping
            if (m.getAnnotation(PostMapping.class) != null) {
                PostMapping annotation = auMethod.getAnnotation(PostMapping.class);
                assertNotNull(annotation, "Method " + auMethod.getName() + " must have PostMapping annotation in " + clazz.getName());
                assertArrayEquals(
                        m.getAnnotation(PostMapping.class).value(), annotation.value(),
                        "ActivityAuditController - Method " + auMethod.getName() + " PostMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(auMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            auMethod.getAnnotation(PreAuthorize.class),
                            "Method " + auMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
        }
    }

    @Test
    public void validFindingMethods() {
        LOGGER.debug("Testing methods ActivityFindingController...");
        final Class<ActivityFindingController> clazz = ActivityFindingController.class;
        Method[] findingMethods = clazz.getDeclaredMethods();
        for (Method m : ActivityController.class.getDeclaredMethods()) {
            if (TestAspectJUtils.isAspectJInternalMethod(m)) {
                continue;
            }
            Method findMethod = getMethod(findingMethods, m.getName(), m.getParameterCount());
            // Existe el método
            assertNotNull(findMethod, "ActivityFindingController - Method " + m.getName() + " not exists.");
            assertEquals(m.getName(), findMethod.getName(), "ActivityFindingController - Method " + m.getName() + " not exists.");
            // Mismos tipos de parámetros
            if (m.getParameterCount() > 0) {
                Class<?>[] types = m.getParameterTypes();
                Class<?>[] types1 = findMethod.getParameterTypes();
                assertArrayEquals(types, types1, "ActivityFindingController - Method " + m.getName() + " parameters are different.");
            }
            // Mismos valores en la anotación RequestMapping
            if (m.getAnnotation(RequestMapping.class) != null) {
                RequestMapping annotation = findMethod.getAnnotation(RequestMapping.class);
                assertNotNull(annotation, "Method " + findMethod.getName() + " must have RequestMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(RequestMapping.class).value(), annotation.value(),
                        "ActivityFindingController - Method " + findMethod.getName() + " RequestMapping is different."
                );
                if (!Objects.equals(findMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    // Debe tener autorización
                    assertNotNull(
                            findMethod.getAnnotation(PreAuthorize.class),
                            "Method " + findMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación GetMapping
            if (m.getAnnotation(GetMapping.class) != null) {
                GetMapping annotation = findMethod.getAnnotation(GetMapping.class);
                assertNotNull(annotation, "Method " + findMethod.getName() + " must have GetMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(GetMapping.class).value(), annotation.value(),
                        "ActivityFindingController - Method " + findMethod.getName() + " GetMapping is different."
                );
                if (!Objects.equals(findMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    // Debe tener autorización
                    assertNotNull(
                            findMethod.getAnnotation(PreAuthorize.class),
                            "Method " + findMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación PostMapping
            if (m.getAnnotation(PostMapping.class) != null) {
                PostMapping annotation = findMethod.getAnnotation(PostMapping.class);
                assertNotNull(annotation, "Method " + findMethod.getName() + " must have PostMapping annotation in " + clazz.getName());
                assertArrayEquals(
                        m.getAnnotation(PostMapping.class).value(), annotation.value(),
                        "ActivityFindingController - Method " + findMethod.getName() + " PostMapping is different."
                );
                if (!Objects.equals(findMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    // Debe tener autorización
                    assertNotNull(
                            findMethod.getAnnotation(PreAuthorize.class),
                            "Method " + findMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
        }
    }

    @Test
    public void validOutstandingSurveysMethods() {
        LOGGER.debug("Testing methods ActivityOutstandingSurveysController...");
        final Class<ActivityOutstandingSurveysController> clazz = ActivityOutstandingSurveysController.class;
        Method[] outstandingMethods = clazz.getDeclaredMethods();
        for (Method m : ActivityController.class.getDeclaredMethods()) {
            if (TestAspectJUtils.isAspectJInternalMethod(m)) {
                continue;
            }
            Method ouMethod = getMethod(outstandingMethods, m.getName(), m.getParameterCount());
            // Existe el método
            assertNotNull(ouMethod, "ActivityOutstandingSurveysController - Method " + m.getName() + " not exists.");
            assertEquals(m.getName(), ouMethod.getName(), "ActivityOutstandingSurveysController - Method " + m.getName() + " not exists.");
            // Mismos tipos de parámetros
            if (m.getParameterCount() > 0) {
                Class<?>[] types = m.getParameterTypes();
                Class<?>[] types1 = ouMethod.getParameterTypes();
                assertArrayEquals(types, types1, "ActivityOutstandingSurveysController - Method " + m.getName() + " parameters are different.");
            }
            // Mismos valores en la anotación RequestMapping
            if (m.getAnnotation(RequestMapping.class) != null) {
                RequestMapping annotation = ouMethod.getAnnotation(RequestMapping.class);
                assertNotNull(annotation, "Method " + ouMethod.getName() + " must have RequestMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(RequestMapping.class).value(), annotation.value(),
                        "ActivityOutstandingSurveysController - Method " + ouMethod.getName() + " RequestMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(ouMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            ouMethod.getAnnotation(PreAuthorize.class),
                            "Method " + ouMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación GetMapping
            if (m.getAnnotation(GetMapping.class) != null) {
                GetMapping annotation = ouMethod.getAnnotation(GetMapping.class);
                assertNotNull(annotation, "Method " + ouMethod.getName() + " must have GetMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(GetMapping.class).value(), annotation.value(),
                        "ActivityOutstandingSurveysController - Method " + ouMethod.getName() + " GetMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(ouMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            ouMethod.getAnnotation(PreAuthorize.class),
                            "Method " + ouMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación PostMapping
            if (m.getAnnotation(PostMapping.class) != null) {
                PostMapping annotation = ouMethod.getAnnotation(PostMapping.class);
                assertNotNull(annotation, "Method " + ouMethod.getName() + " must have PostMapping annotation in " + clazz.getName());
                assertArrayEquals(
                        m.getAnnotation(PostMapping.class).value(), annotation.value(),
                        "ActivityOutstandingSurveysController - Method " + m.getName() + " PostMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(ouMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            ouMethod.getAnnotation(PreAuthorize.class),
                            "Method " + ouMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación GetMapping
            if (m.getAnnotation(GetMapping.class) != null) {
                GetMapping annotation = ouMethod.getAnnotation(GetMapping.class);
                assertNotNull(annotation, "Method " + ouMethod.getName() + " must have GetMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(GetMapping.class).value(), annotation.value(),
                        "ActivityOutstandingSurveysController - Method " + ouMethod.getName() + " GetMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(ouMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            ouMethod.getAnnotation(PreAuthorize.class),
                            "Method " + ouMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación PostMapping
            if (m.getAnnotation(PostMapping.class) != null) {
                PostMapping annotation = ouMethod.getAnnotation(PostMapping.class);
                assertNotNull(annotation, "Method " + ouMethod.getName() + " must have PostMapping annotation in " + clazz.getName());
                assertArrayEquals(
                        m.getAnnotation(PostMapping.class).value(), annotation.value(),
                        "ActivityOutstandingSurveysController - Method " + m.getName() + " PostMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(ouMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            ouMethod.getAnnotation(PreAuthorize.class),
                            "Method " + ouMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
        }
    }

    @Test
    public void validPlannerMethods() {
        LOGGER.debug("Testing methods ActivityPlannerController...");
        final Class<ActivityPlannerController> clazz = ActivityPlannerController.class;
        Method[] plannerMethods = clazz.getDeclaredMethods();
        for (Method m : ActivityController.class.getDeclaredMethods()) {
            if (TestAspectJUtils.isAspectJInternalMethod(m)) {
                continue;
            }
            Method plMethod = getMethod(plannerMethods, m.getName(), m.getParameterCount());
            // Existe el método
            assertNotNull(plMethod, "ActivityPlannerController - Method " + m.getName() + " not exists.");
            assertEquals(m.getName(), plMethod.getName(), "ActivityPlannerController - Method " + m.getName() + " not exists.");
            // Mismos tipos de parámetros
            if (m.getParameterCount() > 0) {
                Class<?>[] types = m.getParameterTypes();
                Class<?>[] types1 = plMethod.getParameterTypes();
                assertArrayEquals(types, types1, "ActivityPlannerController - Method " + m.getName() + " parameters are different.");
            }
            // Mismos valores en la anotación RequestMapping
            if (m.getAnnotation(RequestMapping.class) != null) {
                RequestMapping annotation = plMethod.getAnnotation(RequestMapping.class);
                assertNotNull(annotation, "Method " + plMethod.getName() + " must have RequestMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(RequestMapping.class).value(), annotation.value(),
                        "ActivityPlannerController - Method " + plMethod.getName() + " RequestMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(plMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            plMethod.getAnnotation(PreAuthorize.class),
                            "Method " + plMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación GetMapping
            if (m.getAnnotation(GetMapping.class) != null) {
                GetMapping annotation = plMethod.getAnnotation(GetMapping.class);
                assertNotNull(annotation, "Method " + plMethod.getName() + " must have GetMapping annotation.");
                assertArrayEquals(
                        m.getAnnotation(GetMapping.class).value(), annotation.value(),
                        "ActivityPlannerController - Method " + plMethod.getName() + " GetMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(plMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            plMethod.getAnnotation(PreAuthorize.class),
                            "Method " + plMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
            // Mismos valores en la anotación PostMapping
            if (m.getAnnotation(PostMapping.class) != null) {
                PostMapping annotation = plMethod.getAnnotation(PostMapping.class);
                assertNotNull(annotation, "Method " + plMethod.getName() + " must have PostMapping annotation in " + clazz.getName());
                assertArrayEquals(
                        m.getAnnotation(PostMapping.class).value(), annotation.value(),
                        "ActivityPlannerController - Method " + plMethod.getName() + " PostMapping is different."
                );
                // Debe tener autorización
                if (!Objects.equals(plMethod.getName(), CUSTOM_SECURITY_METHOD_NAME)) {
                    assertNotNull(
                            plMethod.getAnnotation(PreAuthorize.class),
                            "Method " + plMethod.getName() + " must be authorized for " + clazz.getName()
                    );
                }
            }
        }
    }

    private Method getMethod(Method[] methods, String methodName, Integer countParameters) {
        for (Method x : methods) {
            if (x.getName().equals(methodName) && x.getParameterCount() == countParameters) {
                return x;
            }
        }
        return null;
    }
}
