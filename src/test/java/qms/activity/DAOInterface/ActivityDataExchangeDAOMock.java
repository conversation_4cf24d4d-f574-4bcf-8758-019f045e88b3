package qms.activity.DAOInterface;

import Framework.Config.TextCodeValue;
import Framework.Config.TextLongValue;
import Framework.Config.UserHasValue;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.BusinessUnitRef;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import qms.access.dto.ILoggedUser;
import qms.activity.core.ActivityUtil;
import qms.activity.dto.ActivityDataSourceDto;
import qms.activity.dto.ActivitySaveData;
import qms.activity.dto.ActivitySaveManyResult;
import qms.activity.dto.ActivityTypeFilllTypeDto;
import qms.activity.dto.DefaultValues;
import qms.activity.dto.DisabledFields;
import qms.activity.dto.HiddenRules;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityType;
import qms.activity.util.ActivityField;
import qms.activity.util.ReportedActivities;
import qms.configuration.dto.BusinessUnitDepartmentDTO;
import qms.custom.dto.DynamicFieldDTO;
import qms.custom.dto.DynamicFieldsDTO;
import qms.custom.entity.DynamicField;
import qms.document.dto.DocumentLinkedSelectorDTO;
import qms.framework.dto.ConditionalFieldDTO;
import qms.framework.dto.SystemLinkDTO;
import qms.framework.enums.ConditionalFieldType;
import qms.planner.dto.PlannerClientDto;
import qms.timesheet.dto.TextPlannerValue;
import qms.timesheet.dto.TimesheetDataSourceDto;

public class ActivityDataExchangeDAOMock implements IActivityDataExchangeDAO {

    private final Long defaultActivityTypeId;
    private final ActivityDataSourceDto defaultActivityDataSourceDto;
    private final ActivityType defaultType;

    public ActivityDataExchangeDAOMock() {
        this.defaultActivityTypeId = 1L;
        this.defaultType = defaultActivityType();
        this.defaultActivityDataSourceDto = defaultDataSource();
    }

    private DynamicFieldsDTO getDyamicsFields() {
        final DynamicFieldsDTO dynamicFieldsConfig = new DynamicFieldsDTO(true);
        dynamicFieldsConfig.setDynamicTableName("dynamicTable1");
        final DynamicFieldDTO dynamicField1 = new DynamicFieldDTO(
                1L,
            "dynamicField1",
            "QMS - Módulo",
            DynamicField.STATUS.ACTIVE.getValue(),
            0,
            0,
            DynamicField.DYNAMIC_FIELD_TYPE.CATALOG.getValue(),
            "varchar(4000)"
        );
        dynamicField1.setOptions(new String[]{
            "-NA-",
            "Active Directory",
            "Actividades",
            "Auditorías",
            "Catálogos",
            "Configuración",
            "Documentos",
            "Encuestas",
            "Equipos",
            "Favoritos",
            "Formularios",
            "Framework",
            "General",
            "Hallazgos",
            "Login",
            "Menú",
            "Organización",
            "Pendientes",
            "Proyectos",
            "Quejas",
            "Timesheet",
            "Timework",
            "Usuarios",
            "Visor",
            "Widgets"
        });
        final DynamicFieldDTO dynamicField2 = new DynamicFieldDTO(
                2L,
            "dynamicField2",
            "QMS - Branch",
            DynamicField.STATUS.ACTIVE.getValue(),
            0,
            0,
            DynamicField.DYNAMIC_FIELD_TYPE.CATALOG_MULTIPLE.getValue(),
            "varchar(4000)"
        );
        dynamicField2.setOptions(new String[]{
            "-NA-",
            "QMS 3.1.37",
            "QMS 3.1.X",
            "QMS 3.2.X",
            "QMS 3.3.X",
            "QMS 2.13.0",
            "QMS PDF GENERATOR 0.0.19"
        });
        final DynamicFieldDTO dynamicFields3 = new DynamicFieldDTO(
                3L,
            "dynamicField3",
            "QMS - Categoría",
            DynamicField.STATUS.ACTIVE.getValue(),
            0,
            0,
            DynamicField.DYNAMIC_FIELD_TYPE.CATALOG.getValue(),
            "varchar(4000)"
        );
        dynamicFields3.setOptions(new String[]{
            "Bug",
            "Cambio",
            "Desarrollo"
        });
        final DynamicFieldDTO dynamicFields4 = new DynamicFieldDTO(
                4L,
            "dynamicField4",
            "Pasos",
            DynamicField.STATUS.ACTIVE.getValue(),
            0,
            1,
            DynamicField.DYNAMIC_FIELD_TYPE.BIG_TEXT.getValue(),
            "varchar(4000)"
        );
        final DynamicFieldDTO dynamicFields5 = new DynamicFieldDTO(
                5L,
            "dynamicField5",
            "Browser URL",
            DynamicField.STATUS.ACTIVE.getValue(),
            0,
            0,
            DynamicField.DYNAMIC_FIELD_TYPE.TEXT.getValue(),
            "varchar(4000)"
        );
        final List<DynamicFieldDTO> dynamicFields = new ArrayList<>(Arrays.asList(
            dynamicField1,
            dynamicField2,
            dynamicFields3,
            dynamicFields4,
            dynamicFields5
        ));
        dynamicFieldsConfig.setDynamicFields(dynamicFields);
        return dynamicFieldsConfig;
    }

    private ActivityDataSourceDto defaultDataSource() {
        final ActivityDataSourceDto datasource = new ActivityDataSourceDto();
        final List<ActivityTypeFilllTypeDto> types = new ArrayList<>(1);
        final String typeName = "Plan Semanal Equipo QMS";
        final ActivityTypeFilllTypeDto type = new ActivityTypeFilllTypeDto(
                ActivityType.FILL_TYPE.PROGRESS.getValue().toString(), typeName, 1L, typeName);
        type.setAddDeliverySetUp(false);
        types.add(type);
        datasource.setTypes(types);
        final List<BusinessUnitDepartmentDTO> businessUnitDepartments = new ArrayList<>(1);
        final BusinessUnitDepartmentDTO businessUnitDepartment = new BusinessUnitDepartmentDTO("QMS - Bnext", 1L, 1L, 1L);
        businessUnitDepartments.add(businessUnitDepartment);
        datasource.setBusinessUnitDepartments(businessUnitDepartments);
        datasource.setTypeId(1L);
        datasource.setTypeName(typeName);
        datasource.setTypeFillTypes(type.getFillTypes());
        datasource.setAddSubtaskByDepartment(false);
        datasource.setConstraints(ActivityUtil.getNewActivityConstraints(defaultType));
        datasource.setEditionRules(new HashMap<>());
        datasource.setHiddenRules(new HiddenRules());
        final DynamicFieldsDTO dynamicFieldsConfig = getDyamicsFields();
        datasource.setDynamicFields(dynamicFieldsConfig);
        datasource.setDisabledFields(getDisabledFields());
        final DefaultValues defaultValues = new DefaultValues();
        defaultValues.setFillType(ActivityType.FILL_TYPE.PROGRESS.getValue());
        final List<SystemLinkDTO> systemLinks = new ArrayList<>(1);
        systemLinks.add(new SystemLinkDTO(1L, "REQID", "-"));
        defaultValues.setSystemLinks(systemLinks);
        defaultValues.setImplementer(1L);
        defaultValues.setVerifier(1L);
        defaultValues.setObjectiveId(1L);
        defaultValues.setPriority(1L);
        defaultValues.setSource(1L);
        defaultValues.setCategoryId(1L);
        datasource.setDefaultValues(defaultValues);
        datasource.setTemplates(new ArrayList<>());
        datasource.setResolutions(new ArrayList<>());
        datasource.setCategories(new ArrayList<>());
        final List<ConditionalFieldDTO> conditionalFields = new ArrayList<>(2);
        final ConditionalFieldDTO condtionalField1 = new ConditionalFieldDTO(
                1L,
                ConditionalFieldType.MATCH_FIELD.getValue(),
                0,
                ActivityField.CATEGORY_ID.getFieldName(),
                "11",
                new Date()
        );
        conditionalFields.add(condtionalField1);
        final ConditionalFieldDTO condtionalField2 = new ConditionalFieldDTO(
                1L,
                ConditionalFieldType.ASSIGNED_FIELD.getValue(),
                0,
                ActivityField.OBJECTIVE_ID.getFieldName(),
                "1",
                new Date()
        );
        conditionalFields.add(condtionalField2);
        datasource.setConditionalFields(conditionalFields);
        final List<TextCodeValue> sources = new ArrayList<>(Collections.singletonList(
                new TextCodeValue("Plan Semanal Equipo QMS", "Plan Semanal Equipo QMS", 1L)
        ));
        datasource.setSources(sources);
        final List<TextCodeValue> categories = new ArrayList<>(Collections.singletonList(
                new TextCodeValue("Bug", "Bug", 11L)
        ));
        datasource.setCategories(categories);
        final List<UserHasValue> users = new ArrayList<>(Arrays.asList(
                new UserHasValue("dmontoya","dmontoya", "Daniela", "dmontoya@dummy", 1L),
                new UserHasValue("faestrada","faestrada", "Fazur", "faestrada@dummy", 2L),
                new UserHasValue("adeleon", "adeleon", "Alfonso", "adeleon@dummy", 3L),
                new UserHasValue("llimas", "llimas", "Luis", "llimas@dummy", 4L),
                new UserHasValue("jarodriguez", "jarodriguez", "José", "jarodriguez@dummy", 5L),
                new UserHasValue("equintanilla", "equintanilla", "Eduardo", "equintanilla@dummy", 6L),
                new UserHasValue("mcruz", "mcruz", "Miguel", "mcruz@dummy", 7L),
                new UserHasValue("ahernandez", "ahernandez", "Alejandro", "ahernandez@dummy", 8L),
                new UserHasValue("admin", "admin", "Administrador", "admin@dummy", 9L),
                new UserHasValue("jadams",  "jadams", "Jessica", "jadams@dummy", 10L)
        ));
        datasource.setImplementers(users);
        datasource.setPreImplementers(users);
        datasource.setVerifiers(users);
        final List<TextCodeValue> priorities = new ArrayList<>(Collections.singletonList(
                new TextCodeValue("Media", "Media", 1L)
        ));
        datasource.setPriorities(priorities);
        datasource.setBusinessUnits(new ArrayList<>());
        final List<TextCodeValue> objectives = new ArrayList<>(Collections.singletonList(
                new TextCodeValue("Cumplir con Plan Semanal", "Cumplir con Plan Semanal", 1L)
        ));
        datasource.setObjectives(objectives);
        datasource.setFillTypes(new ArrayList<>());
        final List<DocumentLinkedSelectorDTO> forms = new ArrayList<>();
        final DocumentLinkedSelectorDTO form1 = new DocumentLinkedSelectorDTO(
                1L,
                "dummy form",
                "dummy form",
                1,
                "1",
                null,
                new Date(),
                "dummy user"
        );
        forms.add(form1);
        datasource.setForms(forms);
        TimesheetDataSourceDto tsDatasource = getProjectCatalogs(null);
        datasource.setClients(tsDatasource.getClients());
        datasource.setPlanners(tsDatasource.getPlanners());
        datasource.setTasks(tsDatasource.getTasks());
        return datasource;
    }

    private DisabledFields getDisabledFields() {
        return new DisabledFields();
    }

    @Override
    public TimesheetDataSourceDto getProjectCatalogs(ILoggedUser loggedUser) {

        final PlannerClientDto client1 = getDefaultClient(1L, "QMS ADMINISTRACÍON DE PROYECTO");
        final PlannerClientDto client2 = getDefaultClient(2L, "QMS PRODUCTO");

        final List<PlannerClientDto> clients = new ArrayList<>(Arrays.asList(
                client1,
                client2
        ));
        final TextPlannerValue planner1 = getDefaultPlanner(client1, 1L, "Administración de Proyecto");

        final TextPlannerValue planner2 = getDefaultPlanner(client2, 2L, "Capacitación");

        final TextPlannerValue planner3 = getDefaultPlanner(client2, 3L, "Desarrollo Versión 3.1.X (Responsiva)");
        final TextPlannerValue planner4 = getDefaultPlanner(client2, 4L, "OPERACIÓN DIARIA");
        final TextPlannerValue planner5 = getDefaultPlanner(client2, 5L, "Peer Review");
        final TextPlannerValue planner6 = getDefaultPlanner(client2, 6L, "QA VERSIÓN 3.1.X");
        final TextPlannerValue planner7 = getDefaultPlanner(client2, 7L, "Usabilidad Versión 3.1.X (Responsiva)");
        final TextPlannerValue planner8 = getDefaultPlanner(client2, 8L, "Operación Diaria");
        final TextPlannerValue planner9 = getDefaultPlanner(client2, 9L, "QA Versión 3.1.X (Responsiva)");

        final List<TextPlannerValue> planners = new ArrayList<>(Arrays.asList(
                planner1,
                planner2,
                planner3,
                planner4,
                planner5,
                planner6,
                planner7,
                planner8,
                planner9
        ));

        final TextPlannerValue task1 = getDefaultTask(client1, planner1, 1L, "Administración de Proyecto");

        final TextPlannerValue task2 = getDefaultTask(client2, planner2, 2L, "Interna");

        final TextPlannerValue task3 = getDefaultTask(client2, planner3, 2L, "Corrección de Incidentes");
        final TextPlannerValue task4 = getDefaultTask(client2, planner3, 3L, "Desarrollo de Funcionalidad");
        final TextPlannerValue task5 = getDefaultTask(client2, planner3, 4L, "Mejora & Optimización");

        final TextPlannerValue task6 = getDefaultTask(client2, planner4, 5L, "Conferencia Telefónica");
        final TextPlannerValue task7 = getDefaultTask(client2, planner4, 6L, "Registro Módulo de Actividades - Plan Semanal");

        final TextPlannerValue task13 = getDefaultTask(client2, planner5, 12L, "Atender comentarios de Peer Review");
        final TextPlannerValue task14 = getDefaultTask(client2, planner5, 13L, "Revisión a Alfonso de León Ávalos");
        final TextPlannerValue task15 = getDefaultTask(client2, planner5, 14L, "Revisión a Fazur Estrada.");
        final TextPlannerValue task16 = getDefaultTask(client2, planner5, 15L, "Revisión a Jessica Adams");

        final TextPlannerValue task17 = getDefaultTask(client2, planner6, 16L, "Documentar Incidentes");
        final TextPlannerValue task18 = getDefaultTask(client2, planner6, 17L, "Validación de Incidentes");

        final TextPlannerValue task35 = getDefaultTask(client2, planner7, 34L, "Prototipo");

        final TextPlannerValue task8 = getDefaultTask(client2, planner8, 7L, "Cerrar Reqs. de semanas anteriores");
        final TextPlannerValue task9 = getDefaultTask(client2, planner8, 8L, "Conferencia Telefónica");
        final TextPlannerValue task10 = getDefaultTask(client2, planner8, 9L, "REQS - Anlálisis de incidentes y Estimación de esfuerzo");
        final TextPlannerValue task11 = getDefaultTask(client2, planner8, 10L, "Registro Módulo de Actividades - Plan Semanal");
        final TextPlannerValue task12 = getDefaultTask(client2, planner8, 11L, "Revisión y seguimiento a E-Mails");
        final TextPlannerValue task36 = getDefaultTask(client2, planner8, 36L, "Soporte");

        final TextPlannerValue task19 = getDefaultTask(client2, planner9, 18L, "Actividades");
        final TextPlannerValue task20 = getDefaultTask(client2, planner9, 10L, "Auditorías");
        final TextPlannerValue task21 = getDefaultTask(client2, planner9, 20L, "Documentos");
        final TextPlannerValue task22 = getDefaultTask(client2, planner9, 21L, "Emcuestas");
        final TextPlannerValue task23 = getDefaultTask(client2, planner9, 22L, "Equipos");
        final TextPlannerValue task24 = getDefaultTask(client2, planner9, 23L, "Etiquetado de Versión");
        final TextPlannerValue task25 = getDefaultTask(client2, planner9, 24L, "Formularios");
        final TextPlannerValue task26 = getDefaultTask(client2, planner9, 25L, "Hallazgos");
        final TextPlannerValue task27 = getDefaultTask(client2, planner9, 26L, "Indicadores");
        final TextPlannerValue task28 = getDefaultTask(client2, planner9, 27L, "Inglés");
        final TextPlannerValue task29 = getDefaultTask(
                client2, planner9, 28L, "Móvil (Widgets, Timework, Timeshet, Pendientes de Actividades y Formularios)"
        );
        final TextPlannerValue task30 = getDefaultTask(client2, planner9, 29L, "Proyectos");
        final TextPlannerValue task31 = getDefaultTask(client2, planner9, 30L, "Quejas");
        final TextPlannerValue task32 = getDefaultTask(client2, planner9, 31L, "Reuniones");
        final TextPlannerValue task33 = getDefaultTask(client2, planner9, 32L, "Timework");
        final TextPlannerValue task34 = getDefaultTask(client2, planner9, 33L, "Widgets");

        final List<TextPlannerValue> tasks = new ArrayList<>(Arrays.asList(
                task1,
                task2,
                task3,
                task4,
                task5,
                task6,
                task7,
                task8,
                task9,
                task10,
                task11,
                task12,
                task13,
                task14,
                task15,
                task16,
                task17,
                task18,
                task19,
                task20,
                task21,
                task22,
                task23,
                task24,
                task25,
                task26,
                task27,
                task28,
                task29,
                task30,
                task31,
                task32,
                task33,
                task34,
                task35,
                task36
        ));
        final TimesheetDataSourceDto datasource = new TimesheetDataSourceDto();
        datasource.setClients(clients);
        datasource.setPlanners(planners);
        datasource.setTasks(tasks);
        return datasource;
    }


    private PlannerClientDto getDefaultClient(final Long clientId, final String clientLabel) {
        return new PlannerClientDto(clientLabel, clientId, clientLabel);
    }

    private TextPlannerValue getDefaultPlanner(final PlannerClientDto client, final Long plannerId, final String plannerLabel) {
        return new TextPlannerValue(
                plannerLabel,
                plannerId,
                plannerLabel,
                client.getValue(),
                client.getCode(),
                plannerId,
                plannerLabel,
                null,
                null
        );
    }

    private TextPlannerValue getDefaultTask(
            final PlannerClientDto client,
            final TextPlannerValue planner,
            final Long taskId,
            final String taskLabel
    ) {
        return new TextPlannerValue(
                taskLabel,
                taskId,
                taskLabel,
                client.getValue(),
                client.getCode(),
                planner.getValue(),
                planner.getCode(),
                taskId,
                taskLabel
        );
    }

    private ActivityType defaultActivityType() {
        final ActivityType type = new ActivityType(1L);
        type.setAddVerificationAvailable(false);
        type.setAddVerificationOnCreate(true);
        type.setVerificationReqOnPlanning(true);
        type.setAddImplementationOnCreate(true);
        type.setAllowsUseOnPlanning(false);
        type.setCustomImplementationDate(ActivityType.IMPLEMENTATION_TYPE.PLANNED_DATE.getValue());
        return type;
    }

    private ReportedActivities reportActivity(ActivitySaveData entity, ILoggedUser loggedUser) {
        final ReportedActivities result = new ReportedActivities(true);
        final Activity data = entity.getData();
        if (data.getCode() == null || data.getCode().isEmpty()) {
            data.setCode("ACT-" + UUID.randomUUID());
        }
        result.setCode(data.getCode());
        result.setImplementationCode(data.getCode() + "-" + ThreadLocalRandom.current().nextInt());
        result.setDescription(data.getDescription());
        if (entity.getChilds() != null) {
            final List<ReportedActivities> childs = entity.getChilds().stream()
                    .map((chid) -> reportActivity(chid, loggedUser))
                    .collect(Collectors.toList());
            result.setChilds(childs);
        }
        return result;
    }

    @Override
    public ActivitySaveManyResult reportActivityMany(List<ActivitySaveData> entities, ILoggedUser loggedUser) {
        final List<ReportedActivities> results = entities.stream()
                .map((entity) -> reportActivity(entity, loggedUser))
                .collect(Collectors.toList());
        return new ActivitySaveManyResult(results);
    }

    @Override
    public ActivityDataSourceDto changeActivityDataSource(
            Long workflowId,
            Long activityTypeId,
            Long businessUnitDepartmentId,
            Module module,
            ILoggedUser loggedUser,
            Map<String, Object> typeOverrides
    ) {
        return defaultActivityDataSourceDto;
    }

    @Override
    public Long getFirstActivityTypeId() {
        return defaultActivityTypeId;
    }

    @Override
    public ActivityDataSourceDto getNewActivityDataSourceByType(
            Module module,
            Long workflowId,
            Long typeId,
            Long businessUnitDepartmentId,
            ILoggedUser loggedUser,
            Map<String, Object> typeOverrides
    ) {
        return defaultActivityDataSourceDto;
    }

    @Override
    public ActivityType findTypeById(Long activityTypeId) {
        return defaultType;
    }

    @Override
    public Set<TextLongValue> getUsers(Set<Long> userIds) {
        return defaultActivityDataSourceDto.getImplementers().stream()
                .filter((imp) -> userIds.contains(Long.valueOf(imp.getValue())))
                .map((imp) -> new TextLongValue(imp.getText(), Long.valueOf(imp.getValue())))
                .collect(Collectors.toSet());
    }

    @Override
    public IActivityDataExchangeDAO getDataExchangeProxy() {
        return this;
    }

    @Override
    public BusinessUnitRef findBusinessUnit(Long businessUnitId) {
        return new BusinessUnitRef(businessUnitId);
    }

    @Override
    public BusinessUnitDepartmentRef findBusinessUnitDepartment(Long businessUnitDepartmentId) {
        return new BusinessUnitDepartmentRef(businessUnitDepartmentId);
    }

    @Override
    public <TYPE> TYPE makePersistent(TYPE entity) {
        return entity;
    }

    @Override
    public <TYPE> TYPE makePersistent(TYPE entity, Long loggedUserId) {
        return entity;
    }


}
