package qms.framework.util;

import DPMS.Action.Action_RequestSurveyCapture;
import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.Mapping.Document;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.User;
import DPMS.Survey.CRUD_SurveyCapture;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.login.Login;
import bnext.login.logic.RenewDataStatus;
import isoblock.surveys.dao.hibernate.OutstandingAnswer;
import isoblock.surveys.dao.hibernate.OutstandingQuestion;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import isoblock.surveys.dao.interfaces.ISurveyField;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpSession;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import qms.access.dto.LoggedUser;
import qms.document.dto.FillFormDTO;
import qms.document.logic.FormHelper;
import qms.form.core.FillHelper;
import qms.form.core.FillManager;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.FieldCommentDTO;
import qms.form.dto.FillHelperData;
import qms.form.dto.OutstandingSurveysSignDTO;
import qms.form.util.IOutstandingSurveysLoadAnswers;
import qms.form.util.SurveyFieldAnswerType;
import qms.framework.dto.FormTestDTO;
import qms.framework.dto.FormViewDTO;
import qms.framework.dto.UserBossTestDTO;
import qms.survey.dto.FieldDTO;
import qms.survey.dto.SurveyDTO;
import qms.survey.dto.SurveyFieldObjectDTO;
import qms.workflow.util.WorkflowAuthRole;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class FormTestManager {

    private static final Logger LOGGER = LoggerFactory.getLogger(FormTestManager.class);

    private ServletContext servletContext;

    public void setServletContext(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    public List<UserBossTestDTO> getUsersWithBoss() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        return dao.HQL_findByQueryLimit(""
                + " SELECT new " + UserBossTestDTO.class.getCanonicalName() + "("
                    + " c.id,"
                    + " c.description,"
                    + " b.id,"
                    + " b.description"
                + ") "
                + " FROM " + User.class.getCanonicalName() + " c"
                + " JOIN c.boss b"
                + " WHERE c.deleted = 0"
                + " AND c.status = " + User.STATUS.ACTIVE.getValue(),
                10
        );
    }

    public FormTestDTO getVacationsForm(final String masterId) {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        return (FormTestDTO) dao.HQL_findSimpleObject(""
                + " SELECT new " + FormTestDTO.class.getCanonicalName() + "("
                + " c.id,"
                + " c.surveyId"
                + ") "
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.status = " + Document.STATUS.ACTIVE.getValue()
                + " AND c.masterId = :masterId",
                "masterId", masterId
        );
    }

    public FormViewDTO viewStartFormFill(final Long userId, final Long surveyId, final Long documentId) throws Exception {
        final Action_RequestSurveyCapture surveyCapture = new Action_RequestSurveyCapture();
        surveyCapture.withServletContext(servletContext);
        setSessionDataMap(surveyCapture.getSession(), userId);
        surveyCapture.setId(surveyId.toString());
        surveyCapture.setDocumentId(documentId);
        surveyCapture.setDocumentDescription("Vacations");
        surveyCapture.setRequestMode(SurveyRequestMode.REQUESTOR.toString());
        final MockHttpServletRequest request = mockRequest(userId);
        surveyCapture.setRequest(request);
        final MockHttpServletResponse response = mockResponse();
        surveyCapture.withServletResponse(response);
        surveyCapture.execute();
        return new FormViewDTO(surveyCapture.getPending(), surveyCapture.getParser());
    }


    private void setSessionDataAttributes(final HttpSession session, final Long userId) {
        session.setAttribute("intusuarioid", userId);
        session.setAttribute(Login.RENEW_DATA_LOCATION, RenewDataStatus.DATA_RENEWED.value());
        session.setAttribute(Login.RENEW_DATA_PASSWORD, RenewDataStatus.DATA_RENEWED.value());
    }
    
    private void setSessionDataMap(final Map session, final Long userId) {
        session.put("intusuarioid", userId);
        session.put(Login.RENEW_DATA_LOCATION, RenewDataStatus.DATA_RENEWED.value());
        session.put(Login.RENEW_DATA_PASSWORD, RenewDataStatus.DATA_RENEWED.value());
    }

    public void requestorCompleted(final UserBossTestDTO user, final FormViewDTO formView) {
        try {
            saveStartFormFill(user, formView);
        } catch (final Exception ex) {
            LOGGER.error("Save new form for vacations failed for user {}.", user.getUserId(), ex);
        }
    }

    public void completeFill(final FormViewDTO formView, final UserBossTestDTO user) {
        requestorCompleted(user, formView);
        final List<FieldDTO> fields = getAuthorizationFields(formView);
        authorizeSteps(fields, formView, user);
    }

    private List<FieldDTO> getAuthorizationFields(final FormViewDTO formView) {
        final List<FieldDTO> fields = formView.getParser().getSurveyData().getFields();
        final List<FieldDTO> autorize = fields.stream()
                .filter(field -> SurveyField.TYPE.SECTION.getValue().equals(field.getType())
                || SurveyField.TYPE.SIGNATURE.getValue().equals(field.getType()))
                .skip(1)
                .collect(Collectors.toList());
        return autorize;
    }

    public void completPartially(final UserBossTestDTO user, final FormViewDTO formView, final Integer indexLimit) {
        requestorCompleted(user, formView);
        final List<FieldDTO> fields = getAuthorizationFields(formView).subList(0, indexLimit);
        authorizeSteps(fields, formView, user);
    }

    private void authorizeSteps(final List<FieldDTO> fields, final FormViewDTO formView, final UserBossTestDTO user) {
        fields.forEach(field -> {
            final SurveyFillEntity fillEntity = SurveyFillEntity.fromValue(field.getFillEntity());
            final SurveyField.TYPE type = SurveyField.TYPE.fromValue(field.getType());
            switch (type) {
                case SECTION:
                    switch (fillEntity) {
                        case USER:
                            Long userId = null;
                            try {
                                userId = field.getFillUserId();
                                saveFormFill(userId, formView);
                            } catch (final Exception ex) {
                                throw new RuntimeException("Complete form for vacations failed for user id " + userId + ".", ex);
                            }
                            break;
                        default:
                            throwNotSupportedFill(field, type, fillEntity);
                    }
                    break;
                case SIGNATURE:
                    switch (fillEntity) {
                        case BOSS:
                            try {
                            signForm(user.getBossId(), formView);
                        } catch (final Exception ex) {
                            throw new RuntimeException("Approve form for vacations failed for boss " + user.getBossId() + ".", ex);
                        }
                        break;
                        case USER:
                            Long userId = null;
                            try {
                                userId = field.getFillUserId();
                                signForm(userId, formView);
                            } catch (final Exception ex) {
                                throw new RuntimeException("Approve form for vacations failed for user id " + userId + ".", ex);
                            }
                            break;
                        case REQUESTOR:
                            try {
                            signForm(user.getUserId(), formView);
                        } catch (final Exception ex) {
                            throw new RuntimeException("Approve form for vacations failed for requestor " + user.getUserId() + ".", ex);
                        }
                        break;
                        default:
                            throwNotSupportedFill(field, type, fillEntity);
                    }
                    break;
                default:
                    throwNotSupportedFill(field, type, fillEntity);
            }
        });
    }

    private void saveStartFormFill(final UserBossTestDTO user, final FormViewDTO formView) throws Exception {
        final Long userId = user.getUserId();
        final CRUD_SurveyCapture surveyCapture = newSurveyCaptureHandler(userId);
        surveyCapture.withServletContext(servletContext);
        surveyCapture.execute();
        final OutstandingSurveys outstandingSurveys = (OutstandingSurveys) generateNewFillAnswers(formView, user);
        surveyCapture.saveRequest(outstandingSurveys);
    }

    private void saveFormFill(final Long userId, final FormViewDTO formView) throws Exception {
        final CRUD_SurveyCapture surveyCapture = newSurveyCaptureHandler(userId);
        surveyCapture.withServletContext(servletContext);
        surveyCapture.execute();
        final IOutstandingSurveysLoadAnswers outstandingSurveys = formView.getOutstandingSurveys();
        outstandingSurveys.setSignatureAsSaveBehavior(false);
        final FormHelper formHelper = new FormHelper(Utilities.getUntypedDAO(servletContext));
        final FillFormDTO fillForm = formHelper.loadFillFormDTO(outstandingSurveys.getRequestId());
        final LoggedUser user = new LoggedUser(userId);
        final FillHelperData fillData = new FillHelperData(
                Module.FORMULARIE,
                formView.getOutstandingSurveys().getSurveyId(),
                formView.getOutstandingSurveys().getRequestId(),
                formView.getOutstandingSurveys().getDocumentId(),
                formView.getOutstandingSurveys().getId(),
                formView.getOutstandingSurveys().getArchived(),
                Objects.equals(formView.getOutstandingSurveys().getDeleted(), 1),
                formView.getOutstandingSurveys().getBusinessUnitDepartmentId(),
                WorkflowAuthRole.ADMIN,
                formView.getOutstandingSurveys().getDteFechaInicio(),
                1
        );
        final FillManager fillManager = new FillManager(
                formView.getOutstandingSurveys().getSurveyId(),
                Utilities.getBean(IFormCaptureDAO.class, servletContext),
                user
        );
        final FillHelper validator = new FillHelper(
                fillData,
                fillManager,
                user
        );
        if (fillForm == null) {
            throw new RuntimeException("Could not save the form " + formView.getOutstandingSurveys().getId() + " as the form data is not available.");
        }
        if (fillForm.getCurrentAutorizationPoolIndex() == null) {
            throw new RuntimeException("Could not save the form " + formView.getOutstandingSurveys().getId() + " as the current autorization pool index is null.");
        }
        final List<Long> objIdsByIndex = getObjIdsByPoolIndex(outstandingSurveys, fillForm.getCurrentAutorizationPoolIndex());
        final SurveyDTO surveyData = formView.getParser().getSurveyData();
        final Map<String, FieldDTO> fields = surveyData.getFields().stream()
                .collect(Collectors.toMap(FieldDTO::getFieldId, item -> item));
        outstandingSurveys.getPreguntasRespondidas().forEach(answer -> {
            final ISurveyField<SurveyFieldObjectDTO> fieldData = validator.getFieldByFieldId(answer.getFieldId());
            final FieldDTO field = fields.get(fieldData.getObj().getField_id());
            if (!objIdsByIndex.contains(field.getObjId())) {
                return;
            }
            generateAnswer(answer, field, false);
        });
        outstandingSurveys.setStatus(OutstandingSurveys.STATUS.CLOSED.getValue());
        outstandingSurveys.setEstatus(outstandingSurveys.getStatus().shortValue());
        surveyCapture.saveRequest((OutstandingSurveys) outstandingSurveys, fillForm.getCurrentAutorizationPoolIndex().longValue());
    }

    private CRUD_SurveyCapture newSurveyCaptureHandler(final Long userId) {
        final CRUD_SurveyCapture surveyCapture = new CRUD_SurveyCapture();
        surveyCapture.withServletContext(servletContext);
        setSessionDataMap(surveyCapture.getSession(), userId);
        final MockHttpServletRequest request = mockRequest(userId);
        surveyCapture.setRequest(request);
        final MockHttpServletResponse response = mockResponse();
        surveyCapture.withServletResponse(response);
        return surveyCapture;
    }

    private void signForm(final Long userId, final FormViewDTO formView) throws Exception {
        final CRUD_SurveyCapture crudSurvey = new CRUD_SurveyCapture();
        crudSurvey.withServletContext(servletContext);
        setSessionDataMap(crudSurvey.getSession(), userId);
        final MockHttpServletRequest request = mockRequest(userId);
        crudSurvey.setRequest(request);
        final MockHttpServletResponse response = mockResponse();
        crudSurvey.withServletResponse(response);
        final OutstandingSurveysSignDTO signData = new OutstandingSurveysSignDTO();
        final OutstandingSurveys outstandingSurveys = (OutstandingSurveys) formView.getOutstandingSurveys();
        outstandingSurveys.setSignatureAsSaveBehavior(true);
        outstandingSurveys.setStatus(OutstandingSurveys.STATUS.IN_PROGRESS_FILLED_PARCIALLY.getValue());
        outstandingSurveys.setEstatus(outstandingSurveys.getStatus().shortValue());
        final FormHelper formHelper = new FormHelper(Utilities.getUntypedDAO(servletContext));
        final FillFormDTO fillForm = formHelper.loadFillFormDTO(outstandingSurveys.getRequestId());
        final List<Long> objIdsByIndex = getObjIdsByPoolIndex(outstandingSurveys, fillForm.getCurrentAutorizationPoolIndex());
        final List<FieldDTO> fields = formView.getParser().getSurveyData().getFields();
        final FieldDTO signField = fields.stream()
                .filter(field -> objIdsByIndex.contains(field.getObjId())
                && SurveyField.TYPE.SIGNATURE.getValue().equals(field.getType())
                )
                .findFirst().orElse(null);
        if (signField == null) {
            throw new RuntimeException("Could not sign the form " + formView.getOutstandingSurveys().getId() + " as sign data is not available.");
        }
        final List<FieldCommentDTO> comments = fields.stream()
                .filter(field -> SurveyField.TYPE.SECTION.getValue().equals(field.getType()) && field.getOrder() < signField.getOrder())
                .map(field -> generateComment(field))
                .collect(Collectors.toList());
        signData.setComments(comments);
        signData.setCurrentAutorizationPoolIndex(fillForm.getCurrentAutorizationPoolIndex().longValue());
        signData.setCurrentRecurrence(fillForm.getCurrentRecurrence());
        signData.setOutstandingSurveyId(outstandingSurveys.getId());
        signData.setOutstandingSurveysData(outstandingSurveys);
        signData.setSurveyFieldObjId(signField.getObjId());
        signData.setSign(true);
        crudSurvey.execute();
        crudSurvey.signTheForm(signData);
    }

    private List<Long> getObjIdsByPoolIndex(final IOutstandingSurveysLoadAnswers outstandingSurveys, final Integer fillAutorizationPoolIndex) {
        if (fillAutorizationPoolIndex == null) {
            return Utilities.EMPTY_LIST;
        }
        final ISurveyCaptureDAO captureDao = Utilities.getBean(ISurveyCaptureDAO.class, servletContext);
        final List<OutstandingSurveysAttendant> poolAttendants = captureDao.getPoolAttendantsInfo(
                outstandingSurveys.getCuestionario().getId(),
                outstandingSurveys.getId(),
                outstandingSurveys.getRequestId()
        ).getAttendants();
        final List<Long> assignedObjIds = poolAttendants
                .stream()
                .filter(filler -> fillAutorizationPoolIndex.equals(filler.getFillAutorizationPoolIndex().intValue()))
                .map(filler -> filler.getFieldObjectId())
                .collect(Collectors.toList());
        return assignedObjIds;
    }

    private FieldCommentDTO generateComment(final FieldDTO field) {
        final FieldCommentDTO comment = new FieldCommentDTO();
        comment.setComment(randomString());
        comment.setFieldObjId(field.getObjId());
        return comment;
    }

    private IOutstandingSurveysLoadAnswers generateNewFillAnswers(final FormViewDTO formView, final UserBossTestDTO user) {
        final IOutstandingSurveysLoadAnswers outstandingSurveys = formView.getOutstandingSurveys();
        outstandingSurveys.setStatus(OutstandingSurveys.STATUS.CLOSED.getValue());
        outstandingSurveys.setEstatus(outstandingSurveys.getStatus().shortValue());
        final List<Long> objIdsByIndex = getObjIdsByPoolIndex(outstandingSurveys, 1);
        final List<FieldDTO> fields = formView.getParser().getSurveyData().getFields();
        final List<OutstandingQuestion> answers = fields.stream()
                .filter(field -> !SurveyField.TYPE.FREE_TEXT.getValue().equals(field.getType())
                && !SurveyField.TYPE.PAGE_BREAK.getValue().equals(field.getType())
                )
                .map(field -> fieldToAnswer(field, objIdsByIndex))
                .collect(Collectors.toList());
        outstandingSurveys.setPreguntasRespondidas(answers);
        return outstandingSurveys;
    }

    private OutstandingQuestion fieldToAnswer(final FieldDTO field, final List<Long> objIdsByIndex) {
        final OutstandingQuestion answer = new OutstandingQuestion(-1L);
        final SurveyField fieldAnswer = new SurveyField(field.getId());
        final SurveyFieldObject obj = new SurveyFieldObject();
        obj.setField_id(field.getFieldId());
        fieldAnswer.setObj(obj);
        fieldAnswer.setType(field.getType());
        answer.setFieldId(field.getId());
        answer.setOrder(field.getOrder());
        answer.setScoreId(0L);
        final Boolean emptyAnswer = !objIdsByIndex.contains(field.getObjId());
        generateAnswer(answer, field, emptyAnswer);
        return answer;
    }

    private void generateAnswer(
            final OutstandingQuestion answer,
            final FieldDTO field,
            final Boolean emptyAnswer
    ) {
        final SurveyField.TYPE type = SurveyField.TYPE.fromValue(field.getType());
        if (type == null) {
            return;
        }
        switch (type) {
            case SECTION:
                if (answer.getRespuesta() == null || answer.getRespuesta().isEmpty()) {
                    answer.setRespuesta(new ArrayList<>(0));
                }
                break;
            case EXTERNAL_CATALOG:
                final Map<String, Object> catalogItem;
                final GridInfo<Map<String, Object>> catalogContents = field.getCatalogData().getCatalogContents();
                if (catalogContents != null && !catalogContents.getData().isEmpty()) {
                    catalogItem = catalogContents.getData().get(randomNumber(catalogContents.getData().size()));
                } else {
                    catalogItem = new HashMap<>(2);
                    catalogItem.put("value", randomNumber().toString());
                    catalogItem.put("label", randomString());
                }
                if (answer.getRespuesta() == null || answer.getRespuesta().isEmpty()) {
                    final List<OutstandingAnswer> options = new ArrayList<>(1);
                    final OutstandingAnswer option = new OutstandingAnswer(-1L);
                    if (emptyAnswer) {
                        option.setCatalogOptionValue(null);
                        option.setCatalogOptionLabel("");
                    } else {
                        option.setCatalogOptionValue(catalogItem.get("value").toString());
                        option.setCatalogOptionLabel(catalogItem.get("label").toString());
                    }
                    options.add(option);
                    answer.setRespuesta(options);
                } else {
                    if (emptyAnswer) {
                        answer.getRespuesta().iterator().next().setCatalogOptionValue(null);
                        answer.getRespuesta().iterator().next().setCatalogOptionLabel("");
                    } else {
                        answer.getRespuesta().iterator().next().setCatalogOptionValue(catalogItem.get("value").toString());
                        answer.getRespuesta().iterator().next().setCatalogOptionLabel(catalogItem.get("label").toString());
                    }
                }
                break;
            case TEXT_FIELD:
                if (answer.getRespuesta() == null || answer.getRespuesta().isEmpty()) {
                    final List<OutstandingAnswer> texts = new ArrayList<>(1);
                    final OutstandingAnswer text = new OutstandingAnswer(-1L);
                    if (Objects.equals(field.getAnswerType(), SurveyFieldAnswerType.ALPHA_NUMERIC.getValue())) {
                        if (emptyAnswer) {
                            text.setDescripcion("");
                        } else {
                            text.setDescripcion(randomString());
                        }
                    } else {
                        text.setDescripcion(randomNumber().toString());
                    }
                    texts.add(text);
                    answer.setRespuesta(texts);
                } else {
                    if (emptyAnswer) {
                        answer.getRespuesta().iterator().next().setDescripcion("");
                    } else {
                        if (Objects.equals(field.getAnswerType(), SurveyFieldAnswerType.ALPHA_NUMERIC.getValue())) {
                            answer.getRespuesta().iterator().next().setDescripcion(randomString());
                        } else {
                            answer.getRespuesta().iterator().next().setDescripcion(randomNumber().toString());
                        }
                    }
                }
                break;
            case TEXT_DATE:
                if (answer.getRespuesta() == null || answer.getRespuesta().isEmpty()) {
                    final List<OutstandingAnswer> dates = new ArrayList<>(1);
                    final OutstandingAnswer date = new OutstandingAnswer(-1L);
                    if (emptyAnswer) {
                        date.setDescripcion("");
                    } else {
                        final Date now = new Date();
                        final Date newDate = DateUtils.addDays(now, randomNumber());
                        date.setDescripcion(Utilities.formatDateWithTime(newDate));
                    }
                    dates.add(date);
                    answer.setRespuesta(dates);
                } else {
                    if (emptyAnswer) {
                        answer.getRespuesta().iterator().next().setDescripcion("");
                    } else {
                        final Date now = new Date();
                        final Date newDate = DateUtils.addDays(now, randomNumber());
                        answer.getRespuesta().iterator().next().setDescripcion(Utilities.formatDateWithTime(newDate));
                    }
                }
                break;
            case TEXT_TIME:
                if (answer.getRespuesta() == null || answer.getRespuesta().isEmpty()) {
                    final List<OutstandingAnswer> dates = new ArrayList<>(1);
                    final OutstandingAnswer date = new OutstandingAnswer(-1L);
                    if (emptyAnswer) {
                        date.setDescripcion("");
                    } else {
                        final Date now = new Date();
                        final Date newDate = DateUtils.addDays(now, randomNumber());
                        date.setDescripcion(Utilities.formatDateBy(newDate, "yyyy-MM-dd'T'HH:mm:ss"));
                    }
                    dates.add(date);
                    answer.setRespuesta(dates);
                } else {
                    if (emptyAnswer) {
                        answer.getRespuesta().iterator().next().setDescripcion("");
                    } else {
                        final Date now = new Date();
                        final Date newDate = DateUtils.addDays(now, randomNumber());
                        answer.getRespuesta().iterator().next().setDescripcion(Utilities.formatDateBy(newDate, "yyyy-MM-dd'T'HH:mm:ss"));
                    }
                }
                break;
            case SIGNATURE:
                if (answer.getRespuesta() == null || answer.getRespuesta().isEmpty()) {
                    final List<OutstandingAnswer> signatures = new ArrayList<>(1);
                    final OutstandingAnswer signature = new OutstandingAnswer(-1L);
                    signatures.add(signature);
                    answer.setRespuesta(signatures);
                }
                break;
        }
    }

    private Integer randomNumber() {
        return new Random().nextInt(100);
    }

    private Integer randomNumber(Integer bound) {
        return new Random().nextInt(bound);
    }

    private String randomString() {
        return RandomStringUtils.random(50, true, true);
    }

    @SuppressWarnings("null")
    private MockHttpServletRequest mockRequest(final Long userId) {
        final MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestedSessionIdValid(true);
        final HttpSession session = request.getSession(true);
        setSessionDataAttributes(session, userId);
        return request;
    }

    private MockHttpServletResponse mockResponse() {
        final MockHttpServletResponse response = new MockHttpServletResponse();
        return response;
    }

    private void throwNotSupportedFill(
            final FieldDTO field,
            final SurveyField.TYPE type,
            final SurveyFillEntity fillEntity
    ) {
        final String message = "Fill type " + fillEntity.name()
                + " not support to fill " + type
                + " for field " + field.getFieldId();
        throw new RuntimeException(message);
    }

}
