package qms.framework.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.Test;

public class SettingsUtilTest {
    
    @Test
    public void testValidAppUrlUnchanged() {
        final String url = "https://localhost:9392/qms-3-0-0-dev/";
        assertEquals(url, SettingsUtil.getAppUrl(url));
    }
    
    @Test
    public void testValidAppUrlMissingSlash() {
        final String url = "https://localhost:9392/qms-3-0-0-dev";
        assertEquals("https://localhost:9392/qms-3-0-0-dev/", SettingsUtil.getAppUrl(url));
    }

    @Test
    public void testValidAppUrlNoSlashUnchanged() {
        final String url = "https://localhost:9392/qms-3-0-0-dev";
        assertEquals(url, SettingsUtil.getAppUrlNoSlash(url));
    }
    
    @Test
    public void testValidAppUrlNoSlashRemoveSlash() {
        final String url = "https://localhost:9392/qms-3-0-0-dev/";
        assertEquals("https://localhost:9392/qms-3-0-0-dev", SettingsUtil.getAppUrlNoSlash(url));
    }

}
