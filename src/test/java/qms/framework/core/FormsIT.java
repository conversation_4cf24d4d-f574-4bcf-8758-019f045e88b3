package qms.framework.core;

import java.io.IOException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;
import qms.framework.util.VacationsTestManager;
import qms.util.IntegralTestConfiguration;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
@IntegralTestConfiguration
public class FormsIT {

    private static final Logger LOGGER = LoggerFactory.getLogger(FormsIT.class);

    @Autowired
    private ServletContext servletContext;
    private VacationsTestManager manager;

    @BeforeAll
    public void prepareEnvironment()
            throws QMSException, NoSuchMethodException, InterruptedException, IOException, ServletException, ExecutionException, TimeoutException {
        manager = new VacationsTestManager(servletContext);
    }

    @Test
    public void requestVacationsComplete() {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        LOGGER.trace("requestVacationsComplete");
        manager.requestVacationsComplete();
        MeasureTime.stop(tStart, "Elapsed time in requestVacationsComplete test.");
    }

    @Test
    public void requestVacationsDraft() {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        LOGGER.trace("requestVacationsDraft");
        manager.requestVacationsDraft();
        MeasureTime.stop(tStart, "Elapsed time in requestVacationsDraft test.");
    }

    @Test
    public void requestVacationsRequested() {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        LOGGER.trace("requestVacationsRequested");
        manager.requestVacationsRequested();
        MeasureTime.stop(tStart, "Elapsed time in requestVacationsRequested test.");
    }

    @Test
    public void requestVacationsApprovedBoss() {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        LOGGER.trace("requestVacationsApprovedBoss");
        manager.requestVacationsApprovedBoss();
        MeasureTime.stop(tStart, "Elapsed time in requestVacationsApprovedBoss test.");
    }

    @Test
    public void requestVacationsFilledRH() {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        LOGGER.trace("requestVacationsFilledRH");
        manager.requestVacationsFilledRH();
        MeasureTime.stop(tStart, "Elapsed time in requestVacationsFilledRH test.");
    }

    @Test
    public void requestVacationsApprovedRH() {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        LOGGER.trace("requestVacationsApprovedRH");
        manager.requestVacationsApprovedRH();
        MeasureTime.stop(tStart, "Elapsed time in requestVacationsApprovedRH test.");
    }

}
