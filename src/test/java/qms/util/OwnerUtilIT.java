package qms.util;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import javax.servlet.ServletContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import qms.framework.entity.Owner;
import qms.workflow.util.WorkflowSupported;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@IntegralTestConfiguration
public class OwnerUtilIT {

    @Autowired
    ServletContext servletContext;

    @Test
    public void testGetPositionId() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        assertDoesNotThrow(() -> OwnerUtil.getPositionId(1l, 1l, dao));
    }

    @Test
    public void tesGetOwnerUsers() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        assertDoesNotThrow(() -> OwnerUtil.getOwnerUsers(new Owner(1l), dao));
    }

    @Test
    public void testGetOwnerPositions() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        assertDoesNotThrow(() -> OwnerUtil.getOwnerPositions(new Owner(1l), dao));
    }

    @Test
    public void testGetRequestActiveUsersRequest() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        assertDoesNotThrow(() -> OwnerUtil.getRequestActiveUsers(WorkflowSupported.REQUEST, new Owner(1l), 1l, dao));
    }

    @Test
    public void testGetRequestActiveUsersFormRequest() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        assertDoesNotThrow(() -> OwnerUtil.getRequestActiveUsers(WorkflowSupported.FORM_REQUEST, new Owner(1l), 1l, dao));
    }

    @Test
    public void testGetCurrentUserNamesRequest() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        assertDoesNotThrow(() -> OwnerUtil.getCurrentUserNames(WorkflowSupported.REQUEST, 1l, 1l, dao));
    }

    @Test
    public void testGetCurrentUserNamesFormRequest() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        assertDoesNotThrow(() -> OwnerUtil.getCurrentUserNames(WorkflowSupported.FORM_REQUEST, 1l, 1l, dao));
    }

}
