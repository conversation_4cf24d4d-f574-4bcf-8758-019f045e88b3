package qms.util;

import Framework.Config.Utilities;
import java.util.List;
import javax.servlet.ServletContext;
import mx.bnext.core.util.GridInfo;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import qms.framework.dto.DebugInformationDTO;
import qms.framework.util.DebungInformationType;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 *
 * <AUTHOR>
 */
@IntegralTestConfiguration
public class DaoDebugInformationIT {

    private static final Logger LOGGER = LoggerFactory.getLogger(DaoDebugInformationIT.class);

    @Autowired
    ServletContext servletContext;


    @Test
    public void testGetDetails() {
        final DaoDebugInformation instance = new DaoDebugInformation(servletContext, Utilities.getLocale());
        final GridInfo result = instance.getDetails();
        assertNotNull(result);
        final List<DebugInformationDTO> items = result.getData();
        if (result.getCount() > 0) {
            items.forEach(item -> {
                switch (item.getType()) {
                    case ERROR:
                        LOGGER.error("Debug type {} with detail {}", new Object[]{
                            item.getType(), item.getMessage()
                        });
                        break;
                    case INFO:
                        LOGGER.info("Debug type {} with detail {}", new Object[]{
                            item.getType(), item.getMessage()
                        });
                        break;
                    case WARNING:
                        LOGGER.warn("Debug type {} with detail {}", new Object[]{
                            item.getType(), item.getMessage()
                        });
                        break;
                    default:
                        LOGGER.error("Debug with detail {}", new Object[]{
                            item.getMessage()
                        });
                }
            });
        }
        final Long errors = items.stream()
                .filter(item -> DebungInformationType.ERROR.equals(item.getType()))
                .count();
        assertEquals(0L, errors);
    }

}
