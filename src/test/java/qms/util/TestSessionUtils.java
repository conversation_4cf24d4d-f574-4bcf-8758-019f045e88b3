package qms.util;

import javax.servlet.ServletContext;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.framework.rest.SecurityRootUtils;

public class TestSessionUtils {

    private static final Logger LOGGER = Loggable.getLogger(TestSessionUtils.class);

    public static ILoggedUser getAdmin(final ServletContext servletContext) {
        try {
            final ILoggedUser loggedUser = SecurityRootUtils.getFirstAdminDto(servletContext);
            return loggedUser;
        } catch (final Exception e) {
            LOGGER.error("Failed to load admin user {}", e);
            return null;
        }
    }

}
