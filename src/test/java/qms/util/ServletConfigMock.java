package qms.util;

import java.util.Enumeration;
import javax.servlet.ServletConfig;
import javax.servlet.ServletContext;

/**
 *
 * <AUTHOR>
 */
public class ServletConfigMock implements ServletConfig {

    private final ServletContext servletContext;

    public ServletConfigMock(ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    @Override
    public String getInitParameter(String name) {
        return servletContext.getInitParameter(name);
    }

    @Override
    public Enumeration<String> getInitParameterNames() {
        return servletContext.getInitParameterNames();
    }

    @Override
    public ServletContext getServletContext() {
        return servletContext;
    }

    @Override
    public String getServletName() {
        return servletContext.getServletContextName();
    }
}
