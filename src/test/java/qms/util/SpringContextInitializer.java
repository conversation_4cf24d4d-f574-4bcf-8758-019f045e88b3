package qms.util;

import Framework.Config.AppInitializer;
import Framework.Config.InitializerTask;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.file.Path;
import java.sql.SQLException;
import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.Nonnull;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import mx.bnext.core.util.ClassLoaderParser;
import mx.bnext.core.util.Loggable;
import mx.bnext.database.BnextJdbcUtil;
import org.slf4j.Logger;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import qms.framework.dto.ConnectionConfigDTO;
import qms.framework.util.ConcurrentUtils;
import qms.framework.util.DatabaseUtil;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class SpringContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    private static final Logger LOGGER = Loggable.getLogger(SpringContextInitializer.class);

    private static final Pattern DATABASE_PATTERN_NAME = Pattern.compile("(.*)databaseName=(.+)(&?.*)");
    private static final String TEMPORAL_DATABASE_PREFIX = "BnextQMS_CI_Test_";

    private final String databaseName;
    private final ConnectionConfigDTO config;

    public SpringContextInitializer() {
        try {
            final Properties connectionProperties;
            try (final InputStream input = ClassLoader.getSystemResourceAsStream("integration-test-connection.properties")) {
                connectionProperties = new Properties();
                connectionProperties.load(input);
            }
            this.config = DatabaseUtil.readDataConnection(connectionProperties);
            if  (config == null) {
                throw new RuntimeException("Connection properties cannot be read");
            }
            final String jdbcUrl = config.getJdbcUrl();
            final Matcher matcherName = DATABASE_PATTERN_NAME.matcher(jdbcUrl);
            final String url;
            if (matcherName.find()) {
                final String foundName = matcherName.group(2).trim();
                if (foundName.isEmpty()) {
                    databaseName = generateDatabaseName();
                } else {
                    databaseName = foundName;
                }
                url = matcherName.replaceAll("$1databaseName=master$3");
            } else {
                databaseName = generateDatabaseName();
                url = jdbcUrl;

            }
            config.setJdbcUrl(url);
        } catch (final IOException ex) {
            throw new RuntimeException(ex);
        }
    }

    private static String generateDatabaseName() {
        return TEMPORAL_DATABASE_PREFIX + UUID.randomUUID().toString().replaceAll("-", "");
    }

    @Override
    public void initialize(@Nonnull final ConfigurableApplicationContext applicationContext) {
        try {
            configureDatabase();
        } catch (final SQLException | IOException ex) {
            throw new RuntimeException(ex);
        }
    }

    private void configureDatabase() throws SQLException, IOException {
        final boolean existsDatabase = BnextJdbcUtil.existsDatabase(
                config.getJdbcUrl(),
                config.getUsername(),
                config.getPassword(),
                databaseName
        );
        if (!existsDatabase) {
            BnextJdbcUtil.createDatabase(
                    config.getJdbcUrl(),
                    config.getUsername(),
                    config.getPassword(),
                    databaseName
            );
        }
        URL url = getClass().getResource("/");
        if (url == null) {
            throw new IOException("Cannot find the root path");
        }
        final String path = URLDecoder.decode(
                url.getPath(), java.nio.charset.StandardCharsets.UTF_8.name()
        );
        final Path targetDirectory = new File(ClassLoaderParser.parseClassesPath(path)).toPath();
        BnextJdbcUtil.updateConnectionProperties(
                config.getDriverClassName(),
                config.getJdbcUrl(),
                config.getHibDialect(),
                config.getUsername(),
                config.getPassword(),
                databaseName,
                config.getTransactionIsolation(),
                config.getMaximumPoolSize(),
                targetDirectory
        );
    }

    public static void setupApp(final ServletContext servletContext) throws ExecutionException, InterruptedException, TimeoutException, ServletException {
        try {
            final List<CompletableFuture<InitializerTask>> inits = AppInitializer.init(servletContext, true);
            if (inits != null && inits.isEmpty()) {
                final CompletableFuture<?> allReady = ConcurrentUtils.allOf(inits);
                allReady.get(30, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to rebuild app", e);
        }
    }

}
