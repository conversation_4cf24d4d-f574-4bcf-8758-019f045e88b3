package qms.util;

import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.IUntypedDAO;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import javax.servlet.ServletContext;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.aop.framework.Advised;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.transaction.annotation.Transactional;
import qms.framework.dto.DebugInformationDTO;
import qms.framework.util.DebungInformationType;
import qms.framework.util.LocaleUtil;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class DaoDebugInformation extends Loggable {

    private static final String GENERIC_DAO_IMPL_CANONICAL_NAME = GenericDAOImpl.class.getCanonicalName();
    private static final String PROXY_DELIMITER = "$$";
    private static final String SUN_PROXY_PREFIX = "com.sun.proxy.$";

    private final ServletContext servletContext;
    private final ResourceBundle tags;

    public DaoDebugInformation(final ServletContext servletContext, final Locale locale) {
        this.servletContext = servletContext;
        this.tags = LocaleUtil.getI18n(DaoDebugInformation.class.getCanonicalName(), locale);
    }

    public GridInfo getDetails() {
        final GridInfo<DebugInformationDTO> result = new GridInfo<>();
        final List<DebugInformationDTO> data = validateConfigurationDaos();
        result.setCount(Long.valueOf(data.size()));
        result.setData(data);
        return result;
    }

    private List<DebugInformationDTO> validateConfigurationDaos() {
        final ApplicationContext beanFactory = Utilities.getBeanFactory(servletContext);
        final List<DebugInformationDTO> results = new ArrayList<>();
        for (final String beanName : beanFactory.getBeanDefinitionNames()) {
            Object bean = null;
            try {
                bean = beanFactory.getBean(beanName);
            } catch (final BeansException e) {
                getLogger().error("Fail loading bean {}, {}", beanName, e.getMessage());
            }
            if (bean instanceof IUntypedDAO) {
                validateSingleConfiguration(bean, results);
            }
        }
        if (results.isEmpty()) {
            results.add(getNoIssuesDetail());
        }
        return results;
    }

    private Object getImplementedBean(final Object springProxy) {
        if (springProxy == null) {
            return null;
        }
        while ((AopUtils.isJdkDynamicProxy(springProxy))) {
            try {
                return getImplementedBean(((Advised) springProxy).getTargetSource().getTarget());
            } catch (final Exception ex) {
                return null;
            }
        }
        return springProxy;
    }

    private Class getDaoClass(final Object proxyBean) {
        final String proxyClassName = proxyBean.getClass().getCanonicalName();
        if (proxyClassName.startsWith(SUN_PROXY_PREFIX)) {
            final Object implementedBean = getImplementedBean(proxyBean);
            if (implementedBean == null) {
                throw new RuntimeException("Invalid bean class " + proxyClassName);
            }
            final Class daoClass = implementedBean.getClass();
            final String daoClassName = daoClass.getCanonicalName();
            if (daoClassName.startsWith(GENERIC_DAO_IMPL_CANONICAL_NAME)) {
                return null;
            }
            return daoClass;
        } else if (proxyClassName.contains(PROXY_DELIMITER)) {
            final String daoClassName = proxyClassName.substring(0, proxyClassName.indexOf(PROXY_DELIMITER));
            if (GENERIC_DAO_IMPL_CANONICAL_NAME.equals(daoClassName)) {
                return null;
            }
            return getDaoImplementedClass(daoClassName);
        } else {
            return null;
        }
    }

    private void validateSingleConfiguration(final Object proxyBean, final List<DebugInformationDTO> results) {
        final Class daoClass = getDaoClass(proxyBean);
        if (daoClass == null) {
            return;
        }
        final String daoName = daoClass.getCanonicalName();
        final List<Method> allMethods = Arrays.asList(daoClass.getDeclaredMethods())
                .stream()
                .filter(method -> isImplementationDaoMethod(daoName, method))
                .collect(Collectors.toList());
        if (getLogger().isTraceEnabled()) {
            allMethods.stream().forEach(method -> {
                getLogger().trace("Validating configuration of bean with class {} and method {}", new Object[]{
                    daoName, method.getName()
                });
            });
        }
        final boolean emptyClass = validateEmptyClasses(allMethods, daoClass, results);
        if (!emptyClass) {
            validateTransactionalMethods(allMethods, daoClass, results);
        }
        validateSingleton(daoClass, results);
    }

    private boolean isImplementationDaoMethod(final String daoName, final Method method) {
        final boolean isSynthetic = method.isSynthetic();
        final boolean isBridge = method.isBridge();
        final Integer modifiers = method.getModifiers();
        final boolean isStatic = Modifier.isStatic(modifiers);
        final boolean isPublic = Modifier.isPublic(modifiers);
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(
                    "Validating if method {} is implementation of bean with class {}. Synthetic: {},  Bridge: {}, Static: {}, Public: {}",
                    new Object[]{
                        method.getName(), daoName, isSynthetic, isBridge, isStatic, isPublic
                    });
        }
        return !isSynthetic && !isBridge && !isStatic && isPublic;
    }

    private Class getDaoImplementedClass(final String daoClassName) {
        try {
            return Class.forName(daoClassName);
        } catch (final ClassNotFoundException e) {
            getLogger().error("Fail loading dao class for {}, {}", daoClassName, e.getMessage());
            return null;
        }
    }

    private boolean validateEmptyClasses(
            final List<Method> allMethods,
            final Class daoClass,
            final List<DebugInformationDTO> results
    ) {
        if (allMethods.isEmpty()) {
            final DebugInformationDTO emptyError = getNoMethodMessage(daoClass);
            results.add(emptyError);
            return true;
        }
        return false;
    }

    private void validateTransactionalMethods(
            final List<Method> allMethods,
            final Class daoClass,
            final List<DebugInformationDTO> results
    ) {
        allMethods
                .stream()
                .filter(method -> method.getAnnotation(Transactional.class) == null)
                .map(method -> {
                    final String daoName = daoClass.getCanonicalName();
                    final DebugInformationDTO error = getTransactionError(daoName, method.getName());
                    return error;
                }).forEach(error -> {
            results.add(error);
        });
    }

    private DebugInformationDTO getNoMethodMessage(final Class daoClass) {
        final Map<String, String> params = new HashMap<>(1);
        params.put("clazz", daoClass.getCanonicalName());
        final String errorMessage = StringSubstitutor.replace(getSafeTag("noMethods"), params);
        return new DebugInformationDTO(DebungInformationType.WARNING, errorMessage);
    }

    private String getSafeTag(final String tagKey) {
        if (tags != null && tags.containsKey(tagKey)) {
            return tags.getString(tagKey);
        }
        return tagKey;
    }

    private DebugInformationDTO getNoIssuesDetail() {
        final String message = getSafeTag("noTransactionalIssues");
        return new DebugInformationDTO(DebungInformationType.INFO, message);
    }

    private DebugInformationDTO getTransactionError(final String clazz, final String method) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("clazz", clazz);
        params.put("method", method);
        final String errorMessage = StringSubstitutor.replace(getSafeTag("transactionalMethodMissing"), params);
        return new DebugInformationDTO(DebungInformationType.ERROR, errorMessage);
    }

    private void validateSingleton(final Class daoClass, final List<DebugInformationDTO> results) {
        final Scope scope = AnnotationUtils.getAnnotation(daoClass, Scope.class);
        if (scope != null
                && scope.value() != null
                && scope.value().equals("prototype")) {
            final DebugInformationDTO singleDetail = getSingleDetail(daoClass.getCanonicalName());
            results.add(singleDetail);
        }
    }

    private DebugInformationDTO getSingleDetail(final String clazz) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("clazz", clazz);
        final String errorMessage = StringSubstitutor.replace(getSafeTag("prototypeDaoInUse"), params);
        return new DebugInformationDTO(DebungInformationType.ERROR, errorMessage);
    }

}

