package qms.form.dao;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.Request;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.Survey;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Stream;
import javax.servlet.ServletContext;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import qms.access.dto.LoggedUser;
import qms.framework.rest.SecurityRootUtils;
import qms.util.IntegralTestConfiguration;
import qms.workflow.util.WorkflowAuthRole;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@IntegralTestConfiguration
public class IFormCaptureDAOIT {
    
    @Autowired
    ServletContext servletContext;

    private LoggedUser admin;
    private LoggedUser fillFormUser;
    private LoggedUser fillOutAccess;

    @BeforeAll
    public void beforeAll() throws Exception {
        final IUserDAO userDao = Utilities.getBean(IUserDAO.class, servletContext);
        final Long firstAdminId = SecurityRootUtils.getFirstAdminUserId(servletContext);
        final UserRef user = userDao.HQLT_findById(UserRef.class, firstAdminId);
        
        admin = new LoggedUser(user);
        admin.setAdmin(true);
        admin.setServices(ProfileServices.getAll());
        
        fillFormUser = new LoggedUser(user);
        fillFormUser.setAdmin(false);
        fillFormUser.setServices(new ArrayList<>(1));
        fillFormUser.getServices().add(ProfileServices.FILL_FORM);

        fillOutAccess = new LoggedUser(user);
        fillOutAccess.setAdmin(false);
        fillOutAccess.setServices(new ArrayList<>(1));
        fillOutAccess.getServices().addAll(Arrays.asList(
                ProfileServices.FILL_OUT_HISTORY,
                ProfileServices.FILL_OUT_HISTORY_BUSINESS_UNIT,
                ProfileServices.FILL_OUT_HISTORY_DEPARTMENT,
                ProfileServices.FILL_OUT_HISTORY_MINE
        ));
        
    }

    private Long getFirstOutsandingSurveysId(final IFormCaptureDAO dao) {
        final Long firstOutsandingSurveyId = dao.HQL_findLong(""
                + " SELECT MIN(c.id)"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c"
                + " WHERE c.answersTableId IS NOT NULL");
        return firstOutsandingSurveyId;
    }
    private Long getFirstRequestId(final IFormCaptureDAO dao) {
        final Long firstRequestId = dao.HQL_findLong(""
                + " SELECT MIN(c.id)"
                + " FROM " + Request.class.getCanonicalName() + " c");
        return firstRequestId;
    } 
    
    private Long getFirstSurveyId(final IFormCaptureDAO dao) {
        final Long firstSurveyId = dao.HQL_findLong(""
                + " SELECT MIN(c.id)"
                + " FROM " + Survey.class.getCanonicalName() + " c");
        return firstSurveyId;
    }  
    
    private Long getFirstSurveyIdWithAnswersTable(final IFormCaptureDAO dao) {
        final Long firstSurveyId = dao.HQL_findLong(""
                + " SELECT MIN(c.id)"
                + " FROM " + Survey.class.getCanonicalName() + " c"
                + " WHERE c.answersTable IS NOT NULL");
        return firstSurveyId;
    }
    
    private Long getFirstBusinessUnitId(final IFormCaptureDAO dao) {
        final Long firstBusinessUnitId = dao.HQL_findLong(""
                + " SELECT MIN(c.id)"
                + " FROM " + BusinessUnit.class.getCanonicalName() + " c");
        return firstBusinessUnitId;
    }
    

    @Test
    public void testGetFormApproverUser() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstBusinessUnitId = getFirstBusinessUnitId(dao);
        assertDoesNotThrow(() -> dao.getFormApproverUser(fillFormUser.getId(), firstBusinessUnitId));
    }

    @Test
    public void testGetDynamicTableOfSurvey() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.getDynamicTableOfSurvey(firstSurveyId));
    } 
    
    @Test
    public void testGetAnswerTable() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyIdWithAnswersTable(dao);
        assertDoesNotThrow(() -> dao.getAnswerTable(firstSurveyId));
    }
    
    @Test
    public void testGetFormSurveyIds() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getAllFormSurveyIds());
    }
    
    @Test
    public void testGetPollSurveyIds() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getPollSurveyIds());
    }
    
    @Test
    public void testGetAuditSurveyIds() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getAuditSurveyIds());
    }

    @Test
    public void testAdminLoadValidFieldIds() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstOutsandingSurveyId = getFirstOutsandingSurveysId(dao);
        assertDoesNotThrow(() -> dao.loadValidFieldIds(firstOutsandingSurveyId, WorkflowAuthRole.ADMIN, fillFormUser, null));
    }
    
    @Test
    public void testAssignedLoadValidFieldIds() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstOutsandingSurveyId = getFirstOutsandingSurveysId(dao);
        assertDoesNotThrow(() -> dao.loadValidFieldIds(firstOutsandingSurveyId, WorkflowAuthRole.ASSIGNED, fillFormUser, null));
    }
    
    @Test
    public void testSingleLoadNobodyFieldIs() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.loadNobodyFieldIs(firstSurveyId));    
    } 
    
    @Test
    public void testSingleloadRequestorFieldsIds() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.loadRequestorFieldsIds(firstSurveyId));    
    } 
    
    @Test
    public void testIsRequestor() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstRequestId = getFirstRequestId(dao);
        assertDoesNotThrow(() -> dao.isRequestor(firstRequestId, fillFormUser));
    } 

    @Test
    public void testFilterIsRequestor() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.filterIsRequestor(firstSurveyId, fillFormUser));
    }  
    
    @Test
    public void testIsRequestInStandBy() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstRequestId = getFirstRequestId(dao);
        assertDoesNotThrow(() -> dao.isRequestInStandBy(firstRequestId));    
    }   
     
    @Test
    public void testFilterRequestByStandBy() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.filterRequestByStandBy(firstSurveyId));    
    }  

    @Test
    public void testLoadOutstandingQuestions() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstOutsandingSurveyId = getFirstOutsandingSurveysId(dao);
        assertDoesNotThrow(() -> dao.loadOutstandingQuestions(firstOutsandingSurveyId, Utilities.EMPTY_LIST));
    }
    
    @Test
    public void testLoadOutstandingAnswers() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstOutsandingSurveyId = getFirstOutsandingSurveysId(dao);
        assertDoesNotThrow(() -> dao.loadOutstandingAnswers(firstOutsandingSurveyId));
    }

    @Test
    public void testGetAnswerTableByOutstandingSurveyId() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstOutsandingSurveyId = getFirstOutsandingSurveysId(dao);
        assertDoesNotThrow(() -> dao.getAnswerTableByOutstandingSurveyId(firstOutsandingSurveyId));
    }
    
    @Test
    public void testGetAnswerTableIdByOutstandingSurveyId() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstOutsandingSurveyId = getFirstOutsandingSurveysId(dao);
        assertDoesNotThrow(() -> dao.getAnswerTableIdByOutstandingSurveyId(firstOutsandingSurveyId));
    }
    
    @Test
    public void testGetFreezedDatas() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.getFreezedDatas(firstSurveyId));    
    }  
      
    @Test
    public void testGetFreezedFieldsCount() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.getFreezedFieldsCount(firstSurveyId));    
    } 

    @Test
    public void testLoadSurveyId() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstOutsandingSurveyId = getFirstOutsandingSurveysId(dao);
        assertDoesNotThrow(() -> dao.loadSurveyId(firstOutsandingSurveyId));
    }
    
    @Test
    public void testGetAnswerTables() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        Long firstSurveyId = getFirstSurveyId(dao);
        assertDoesNotThrow(() -> dao.getAnswerTable(firstSurveyId));    
    }  
    
    @Test
    public void testGetNotifySaveAnswersFailuresCount() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        assertDoesNotThrow(() -> dao.getNotifySaveAnswersFailuresCount());
    }
    
    @Test
    public void testTranslateOutstandingSurveysStatusFunction() {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        final String lang = Utilities.getSettings().getLang().toLowerCase();
        Stream.of(OutstandingSurveys.STATUS.values()).forEach(status -> {
            try {
                Assertions.assertNotEquals(dao.SQL_findSimpleString("SELECT [dbo].[translate_out_survey_status](:status, :lang)", ImmutableMap.of("lang", lang, "status", status.getValue() ), 0), "");    
            } catch (AssertionError e) {
                Assertions.fail(status.toString() + " not exist into the function [translate_out_survey_status], please alter function adding the new status", e);
            }
        });
    }

    @Test
    public void testGetFillOutHistoryRowsAsAdmin() {
        IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        SortedPagedFilter filter = new SortedPagedFilter();
        ProfileServices[] services = new ProfileServices[]{ProfileServices.FILL_OUT_HISTORY};
        GridInfo<Map<String, Object>> results = dao.getFillOutHistoryRows(filter, services, admin);
        assertNotNull(results, "Fill out history records must not be null for ADMIN user ");
    }

    @Test
    public void testGetFillOutHistoryRowsWithFillOutAccess() {
        IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class, servletContext);
        SortedPagedFilter filter = new SortedPagedFilter();
        ProfileServices[] services = new ProfileServices[]{ProfileServices.FILL_OUT_HISTORY};
        GridInfo<Map<String, Object>> results = dao.getFillOutHistoryRows(filter, services, fillOutAccess);
        assertNotNull(results, "Fill out history not be null for user with FILL_OUT_HISTORY access ");
    }

}
