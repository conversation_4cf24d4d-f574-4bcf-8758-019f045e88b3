package qms.timework.dao;

import DPMS.DAOInterface.IUserDAO;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import bnext.reference.UserRef;
import java.util.ArrayList;
import java.util.Arrays;
import javax.servlet.ServletContext;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import qms.access.dto.LoggedUser;
import qms.framework.rest.SecurityRootUtils;
import qms.timework.dto.TimeworkListDto;
import qms.util.IntegralTestConfiguration;
import qms.util.interfaces.IGridFilter;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@IntegralTestConfiguration
public class ITimeworkDAOIT {

    private static final Logger LOGGER = LoggerFactory.getLogger(ITimeworkDAOIT.class);

    @Autowired
    ServletContext servletContext;

    private LoggedUser admin;
    private LoggedUser fillOutAccess;
    private LoggedUser timeworkListAccess;
    private LoggedUser noAccess;
    private LoggedUser timeworkAndFillOutAccess;

    @BeforeAll
    public void beforeAll() throws Exception {
        final IUserDAO userDao = Utilities.getBean(IUserDAO.class, servletContext);
        final Long firstAdminId = SecurityRootUtils.getFirstAdminUserId(servletContext);
        final UserRef user = userDao.HQLT_findById(UserRef.class, firstAdminId);

        admin = new LoggedUser(user);
        admin.setAdmin(true);
        admin.setServices(ProfileServices.getAll());

        fillOutAccess = new LoggedUser(user);
        fillOutAccess.setAdmin(false);
        fillOutAccess.setServices(new ArrayList<>());
        fillOutAccess.getServices().addAll(Arrays.asList(
                ProfileServices.FORM_STOPWATCH_LIST,
                ProfileServices.FILL_OUT_HISTORY,
                ProfileServices.FILL_OUT_HISTORY_BUSINESS_UNIT,
                ProfileServices.FILL_OUT_HISTORY_DEPARTMENT,
                ProfileServices.FILL_OUT_HISTORY_MINE
        ));
        timeworkListAccess = new LoggedUser(user);
        timeworkListAccess.setAdmin(false);
        timeworkListAccess.setServices(new ArrayList<>(1));
        timeworkListAccess.getServices().add(
                ProfileServices.TIMEWORK_LIST
        );
        noAccess = new LoggedUser(user);
        noAccess.setAdmin(false);
        noAccess.setServices(new ArrayList<>(0));
        timeworkAndFillOutAccess = new LoggedUser(user);
        timeworkAndFillOutAccess.setAdmin(false);
        timeworkAndFillOutAccess.setServices(new ArrayList<>());
        timeworkAndFillOutAccess.getServices().addAll(Arrays.asList(
                ProfileServices.TIMEWORK_LIST,
                ProfileServices.FORM_STOPWATCH_LIST,
                ProfileServices.FILL_OUT_HISTORY,
                ProfileServices.FILL_OUT_HISTORY_BUSINESS_UNIT,
                ProfileServices.FILL_OUT_HISTORY_DEPARTMENT,
                ProfileServices.FILL_OUT_HISTORY_MINE
        ));
    }

    @Test
    public void testGetListRecordsAsAdmin() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getListRecords(filter, admin);
        assertNotNull(results, "Timework records must not be null for ADMIN user ");
    }

    @Test
    public void testGetDeletedRecordsAsAdmin() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getDeletedRecords(filter, admin);
        assertNotNull(results, "Timework deleted records must not be null for ADMIN user ");
    }

    @Test
    public void testGetListRecordsWithFillOutAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getListRecords(filter, fillOutAccess);
        assertNotNull(results, "Timework records must not be null for user with FILL_OUT_HISTORY access ");
    }

    @Test
    public void testGetDeletedRecordWithFillOutAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getDeletedRecords(filter, fillOutAccess);
        assertNotNull(results, "Timework deleted records must not be null with FILL_OUT_HISTORY access ");
    }

    @Test
    public void testGetListRecordsWithTimeworkListAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getListRecords(filter, timeworkListAccess);
        assertNotNull(results, "Timework records must not be null for user with TIMEWORK_LIST access ");
    }

    @Test
    public void testGetDeletedRecordWithTimeworkListAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getDeletedRecords(filter, timeworkListAccess);
        assertNotNull(results, "Timework deleted records must not be null with TIMEWORK_LIST access ");
    }

    @Test
    public void testGetListRecordsWithNoAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getListRecords(filter, noAccess);
        assertNotNull(results, "Timework records must not be null for user without access ");
    }

    @Test
    public void testGetDeletedRecordWithNotAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getDeletedRecords(filter, noAccess);
        assertNotNull(results, "Timework deleted records must not be null without access ");
    }

    @Test
    public void testGetListRecordsWithFillOutAndTimeworkListAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getListRecords(filter, timeworkAndFillOutAccess);
        assertNotNull(results, "Timework records must not be null for user with FILL_OUT_HISTORY AND TIMEWORK_LIST access ");
    }

    @Test
    public void testGetDeletedRecordWithFillOutAndTimeworkListAccess() {
        ITimeworkDAO dao = Utilities.getBean(ITimeworkDAO.class, servletContext);
        IGridFilter filter = new SortedPagedFilter();
        GridInfo<TimeworkListDto> results = dao.getDeletedRecords(filter, timeworkAndFillOutAccess);
        assertNotNull(results, "Timework deleted records must not be null with FILL_OUT_HISTORY AND TIMEWORK_LIST access ");
    }
}
