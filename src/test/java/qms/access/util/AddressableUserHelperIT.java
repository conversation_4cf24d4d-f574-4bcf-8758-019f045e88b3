package qms.access.util;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import javax.servlet.ServletContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import qms.util.IntegralTestConfiguration;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@IntegralTestConfiguration
public class AddressableUserHelperIT {

    @Autowired
    ServletContext servletContext;

    @Test
    public void testFindUser() {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        AddressableUserHelper helper = new AddressableUserHelper(dao);
        assertDoesNotThrow(() -> helper.findUser(1l));
    }
    
    @Test
    public void testFindUserByCode() {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        AddressableUserHelper helper = new AddressableUserHelper(dao);
        assertDoesNotThrow(() -> helper.findUserByCode("admin"));
    }
    
    @Test
    public void testGetComplaintManagers() {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        AddressableUserHelper helper = new AddressableUserHelper(dao);
        assertDoesNotThrow(() -> helper.getComplaintManagers(1l));
    }
    
    @Test
    public void testGetCompaintToAssignResponsibles() {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        AddressableUserHelper helper = new AddressableUserHelper(dao);
        assertDoesNotThrow(() -> helper.getCompaintToAssignResponsibles(1l));
    }
    
    @Test
    public void testGetEscalationMailManagers() {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        AddressableUserHelper helper = new AddressableUserHelper(dao);
        assertDoesNotThrow(() -> helper.getEscalationMailManagers());
    }
    
    @Test
    public void testGetInternalRecipients() {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        AddressableUserHelper helper = new AddressableUserHelper(dao);
        assertDoesNotThrow(() -> helper.getInternalRecipients(1l));
    }

    @Test
    public void testGetUsersToActivate() {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        AddressableUserHelper helper = new AddressableUserHelper(dao);
        assertDoesNotThrow(() -> helper.getUsersToActivate());
    }

}
