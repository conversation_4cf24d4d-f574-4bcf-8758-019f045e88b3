package qms.planner.dao;

import DPMS.DAOInterface.IUserDAO;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.reference.UserRef;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletContext;
import mx.bnext.access.ProfileServices;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import qms.access.dto.LoggedUser;
import qms.framework.rest.SecurityRootUtils;
import qms.util.IntegralTestConfiguration;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 *
 * <AUTHOR>
 */
@IntegralTestConfiguration
public class IPlannerDAOIT {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(IPlannerDAOIT.class);

    @Autowired
    ServletContext servletContext;

    private LoggedUser admin;
    private LoggedUser viewAllUser;
    private LoggedUser businessUnitUser;
    private LoggedUser departmentUser;
    private LoggedUser ownerUser;

    @BeforeAll
    public void beforeAll() throws Exception {
        final IUserDAO userDao = Utilities.getBean(IUserDAO.class, servletContext);
        final Long firstAdminId = SecurityRootUtils.getFirstAdminUserId(servletContext);
        final UserRef user = userDao.HQLT_findById(UserRef.class, firstAdminId);
        
        admin = new LoggedUser(user);
        admin.setAdmin(true);
        admin.setServices(ProfileServices.getAll());
        
        viewAllUser = new LoggedUser(user);
        viewAllUser.setAdmin(false);
        viewAllUser.setServices(new ArrayList<>(1));
        viewAllUser.getServices().add(ProfileServices.PLANNER_VIEW_ALL);
        
        businessUnitUser = new LoggedUser(user);
        businessUnitUser.setAdmin(false);
        businessUnitUser.setServices(new ArrayList<>(1));
        businessUnitUser.getServices().add(ProfileServices.PLANNER_VIEW_BUSINESS_UNIT);
        
        departmentUser = new LoggedUser(user);
        departmentUser.setAdmin(false);
        departmentUser.setServices(new ArrayList<>(1));
        departmentUser.getServices().add(ProfileServices.PLANNER_VIEW_BUSINESS_UNIT_DEP);
        
        ownerUser = new LoggedUser(user);
        ownerUser.setAdmin(false);
        ownerUser.setServices(new ArrayList<>(1));
        ownerUser.getServices().add(ProfileServices.PLANNER_VIEW_OWNED);
        
    }

    private List<Map<String, Object>> getPlannerAccessResultWhereAccess(final IUntypedDAO dao, final LoggedUser loggedUser) {
        return IPlannerDAO.getPlannerAccessResult(
                dao,
                loggedUser,
                false,
                true,
                true,
                false,
                true,
                false,
                IPlannerDAO.PLANNER_WHERE_ACCESS
        );
    }

    private List<Map<String, Object>> getPlannerAccessResultTasks(final IUntypedDAO dao, final LoggedUser loggedUser) {
        return IPlannerDAO.getPlannerAccessResult(
                dao,
                loggedUser,
                true,
                false,
                true,
                false,
                true,
                false,
                PlannerDAO.PLANNER_TASKS_SELECT_MAP
        );
    }

    private List<Map<String, Object>> getPlannerAccessResultPlanners(final IUntypedDAO dao, final LoggedUser loggedUser) {
        return IPlannerDAO.getPlannerAccessResult(
                dao, loggedUser, false, false, true, true, true, true, PlannerDAO.PLANNER_PLANNER_SELECT_MAP
        );
    }

    private List<Map<String, Object>> getPlannerAccessResultCatalogs(final IUntypedDAO dao, final LoggedUser loggedUser) {
        return IPlannerDAO.getPlannerAccessResult(
                dao, loggedUser, true, true, true, true, true, false, PlannerDAO.PLANNER_CATALOGS_SELECT_MAP
        );
    }

    @Test
    public void testAdminGetPlannerAccessResultWhereAccess() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultWhereAccess(dao, admin);
        LOGGER.debug("testAdminGetPlannerAccessResultWhereAccess {} results", results.size());
        assertNotNull(results, "Planners access results where access must not be null with admin ");
    }

    @Test
    public void testAdminGetPlannerAccessResultTasks() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultTasks(dao, admin);
        LOGGER.debug("testAdminGetPlannerAccessResultTasks {} results", results.size());
        assertNotNull(results, "Planners access results tasks must not be null with admin ");
    }

    @Test
    public void testAdminGetPlannerAccessResultPlanners() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultPlanners(dao, admin);
        LOGGER.debug("testAdminGetPlannerAccessResultPlanners {} results", results.size());
        assertNotNull(results, "Planners access results planners must not be null with admin ");
    }

    @Test
    public void testAdminGetPlannerAccessResultCatalogs() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultCatalogs(dao, admin);
        LOGGER.debug("testAdminGetPlannerAccessResultCatalogs {} results", results.size());
        assertNotNull(results, "Planners access results catalogs must not be null with admin ");
    }
    
    @Test
    public void testViewAllGetPlannerAccessResultWhereAccess() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultWhereAccess(dao, viewAllUser);
        LOGGER.debug("testViewAllGetPlannerAccessResultWhereAccess {} results", results.size());
        assertNotNull(results, "Planners access results where access must not be null with service PLANNER_VIEW_ALL");
    }

    @Test
    public void testViewAllGetPlannerAccessResultTasks() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultTasks(dao, viewAllUser);
        LOGGER.debug("testViewAllGetPlannerAccessResultTasks {} results", results.size());
        assertNotNull(results, "Planners access results tasks must not be null with service PLANNER_VIEW_ALL ");
    }

    @Test
    public void testViewAllGetPlannerAccessResultPlanners() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultPlanners(dao, viewAllUser);
        LOGGER.debug("testViewAllGetPlannerAccessResultPlanners {} results", results.size());
        assertNotNull(results, "Planners access results planners must not be null with service PLANNER_VIEW_ALL ");
    }

    @Test
    public void testViewAllGetPlannerAccessResultCatalogs() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultCatalogs(dao, viewAllUser);
        LOGGER.debug("testViewAllGetPlannerAccessResultCatalogs {} results", results.size());
        assertNotNull(results, "Planners accers results catalogs must not be null with service PLANNER_VIEW_ALL ");
    }    
    
    @Test
    public void testBusinessUnitGetPlannerAccessResultWhereAccess() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultWhereAccess(dao, businessUnitUser);
        LOGGER.debug("testBusinessUnitGetPlannerAccessResultWhereAccess {} results", results.size());
        assertNotNull(results, "Planners access results where access must not be null with service PLANNER_VIEW_BUSINESS_UNIT");
    }

    @Test
    public void testBusinessUnitGetPlannerAccessResultTasks() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultTasks(dao, businessUnitUser);
        LOGGER.debug("testBusinessUnitGetPlannerAccessResultTasks {} results", results.size());
        assertNotNull(results, "Planners access results tasks must not be null with service PLANNER_VIEW_BUSINESS_UNIT ");
    }

    @Test
    public void testBusinessUnitGetPlannerAccessResultPlanners() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultPlanners(dao, businessUnitUser);
        LOGGER.debug("testBusinessUnitGetPlannerAccessResultPlanners {} results", results.size());
        assertNotNull(results, "Planners access results planners must not be null with service PLANNER_VIEW_BUSINESS_UNIT ");
    }

    @Test
    public void testBusinessUnitGetPlannerAccessResultCatalogs() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultCatalogs(dao, businessUnitUser);
        LOGGER.debug("testBusinessUnitGetPlannerAccessResultCatalogs {} results", results.size());
        assertNotNull(results, "Planners accers results catalogs must not be null with service PLANNER_VIEW_BUSINESS_UNIT ");
    }
    
    @Test
    public void testDepartmentGetPlannerAccessResultWhereAccess() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultWhereAccess(dao, departmentUser);
        LOGGER.debug("testDepartmentGetPlannerAccessResultWhereAccess {} results", results.size());
        assertNotNull(results, "Planners access results where access must not be null with service PLANNER_VIEW_BUSINESS_UNIT_DEP");
    }

    @Test
    public void testDepartmentGetPlannerAccessResultTasks() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultTasks(dao, departmentUser);
        LOGGER.debug("testDepartmentGetPlannerAccessResultTasks {} results", results.size());
        assertNotNull(results, "Planners access results tasks must not be null with service PLANNER_VIEW_BUSINESS_UNIT_DEP ");
    }

    @Test
    public void testDepartmentGetPlannerAccessResultPlanners() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultPlanners(dao, departmentUser);
        LOGGER.debug("testDepartmentGetPlannerAccessResultPlanners {} results", results.size());
        assertNotNull(results, "Planners access results planners must not be null with service PLANNER_VIEW_BUSINESS_UNIT_DEP ");
    }

    @Test
    public void testDepartmentGetPlannerAccessResultCatalogs() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultCatalogs(dao, departmentUser);
        LOGGER.debug("testDepartmentGetPlannerAccessResultCatalogs {} results", results.size());
        assertNotNull(results, "Planners accers results catalogs must not be null with service PLANNER_VIEW_BUSINESS_UNIT_DEP ");
    }
   
    @Test
    public void testOwnerGetPlannerAccessResultWhereAccess() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultWhereAccess(dao, ownerUser);
        LOGGER.debug("testOwnerGetPlannerAccessResultWhereAccess {} results", results.size());
        assertNotNull(results, "Planners access results where access must not be null with service PLANNER_VIEW_OWNED");
    }

    @Test
    public void testOwnerGetPlannerAccessResultTasks() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultTasks(dao, ownerUser);
        LOGGER.debug("testOwnerGetPlannerAccessResultTasks {} results", results.size());
        assertNotNull(results, "Planners access results tasks must not be null with service PLANNER_VIEW_OWNED ");
    }

    @Test
    public void testOwnerGetPlannerAccessResultPlanners() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultPlanners(dao, ownerUser);
        LOGGER.debug("testOwnerGetPlannerAccessResultPlanners {} results", results.size());
        assertNotNull(results, "Planners access results planners must not be null with service PLANNER_VIEW_OWNED ");
    }

    @Test
    public void testOwnerGetPlannerAccessResultCatalogs() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        List<Map<String, Object>> results = getPlannerAccessResultCatalogs(dao, ownerUser);
        LOGGER.debug("testOwnerGetPlannerAccessResultCatalogs {} results", results.size());
        assertNotNull(results, "Planners access results catalogs must not be null with service PLANNER_VIEW_OWNED ");
    }

}
