package ape.pending.core;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import javax.servlet.ServletContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.activity.pending.ActivityPending;
import qms.audit.pending.AuditPending;
import qms.configuration.pending.UserPending;
import qms.device.pending.DevicePending;
import qms.document.pending.DocumentPending;
import qms.document.pending.RequestPending;
import qms.finding.pending.FindingPending;
import qms.form.pending.FormPending;
import qms.framework.rest.SecurityRootUtils;
import qms.planner.pending.PlannerPending;
import qms.util.IntegralTestConfiguration;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@IntegralTestConfiguration
public class IPendingIT {

    @Autowired
    ServletContext servletContext;

    private ILoggedUser admin;

    @BeforeAll
    public void beforeAll() throws Exception {
        admin = SecurityRootUtils.getFirstAdminDto(servletContext);
    }

    @Test
    @Transactional
    public void testAllFindingNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new FindingPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAllActivityNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new ActivityPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAllPlannerNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new PlannerPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAllRequestNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new RequestPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAllDocumentNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new DocumentPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAllUserNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new UserPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAllAuditNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new AuditPending(dao);
        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAllDeviceNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new DevicePending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));
    }

    @Test
    @Transactional
    public void testAlFormNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new FormPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin));

    }

    @Test
    @Transactional
    public void testSingleFindingNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new FindingPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSingleActivityNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new ActivityPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSinglePlannerNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new PlannerPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSingleRequestNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new RequestPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSingleDocumentNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new DocumentPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSingleUserNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new UserPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSingleAuditNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new AuditPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSingleDeviceNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new DevicePending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testSingleFormNormalize() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new FormPending(dao);

        assertDoesNotThrow(() -> pending.normalize(admin, 1l));
    }

    @Test
    @Transactional
    public void testDailyFindingCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new FindingPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyActivityCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new ActivityPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyPlannerCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new PlannerPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyRequestCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new RequestPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyDocumentCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new DocumentPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyUserCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new UserPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyAuditCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new AuditPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyDeviceCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new DevicePending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

    @Test
    @Transactional
    public void testDailyFormCheck() {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        final IPending pending = new FormPending(dao);

        assertDoesNotThrow(() -> pending.dailyCheck(admin));
    }

}
