require(['core', 'loader', 'bnext/i18n!bnext/administrator/surveys/nls/surveycapture.busy', 'bnext/angularNavigator'], (core, loader, i18n, angularNavigator) => {
  core.error(i18n.canNotRetriveData, i18n.retryButton, i18n.exitButton).then(
    async () => {
      await loader.showLoader();
      reloadWindow();
    },
    async () => {
      await loader.showLoader();
      angularNavigator.back();
    }
  );
  function reloadWindow() {
    window.location.replace(window.location.href);
  }
});
