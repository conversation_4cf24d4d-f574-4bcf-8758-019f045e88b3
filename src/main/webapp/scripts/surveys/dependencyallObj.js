dojo.require("bnext.Language");

dependencyObj = function(parametros) {
    console.log('>> new dependencyObj ... '+parametros.onload+', '+parametros.onload)
    if(parametros.id == null || parametros.onload == null) {
        console.log('Error! el objeto dependencyObj no puede ser creado.')
        return;
    }
    this.language = new languageBundle(({
        'id':parametros.id+'.language',
        'modulo':'es.dependency',
        'onload':parametros.id+'.initialize()'
    }));
    
    this.id = parametros.id;
    this.onload = parametros.onload;
    this.initialize = function() {
        console.log('initialize ... "dependencyObj" ');
        var dis = eval(parametros.id);
        
        dis.columnGrupo = ({
            title: dis.language.getString('es.dependency.grupo'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"grupo"
        });
    
        dis.columnUne = ({
            title: dis.language.getString('es.dependency.une'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"une"
        });
    
        dis.columnSubUno = ({
            title: dis.language.getString('es.dependency.subuno'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"subuno"
        });
    
        dis.columnSubDos = ({
            title: dis.language.getString('es.dependency.subdos'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"subdos"
        });
        
        dis.columnRegion = ({
            title: dis.language.getString('es.dependency.region'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"region"
        });
        
        dis.columnPais = ({
            title: dis.language.getString('es.dependency.pais'),
            type:1,
            gridType:5, 
            value:"titulo",
            id:"pais"
        });
      
        dis.columnCiudad = ({
            title: dis.language.getString('es.dependency.ciudad'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"municipio"
        });
        
        dis.columnAreaUne = ({
            title: dis.language.getString('es.dependency.areaune'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"areaUne"
        });
        
        dis.columnSubAreaUne = ({
            title: dis.language.getString('es.dependency.subareaune'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"subAreaUne"
        });
        
        dis.columnAreaSubUno = ({
            title: dis.language.getString('es.dependency.areauno'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"areaSubUno"
        });
        
        dis.columnSubAreaSubUno = ({
            title: dis.language.getString('es.dependency.subareauno'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"subAreaSubUno"
        });
        
        dis.columnAreaSubDos = ({
            title: dis.language.getString('es.dependency.areados'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"areaSubDos"
        });
        
        dis.columnSubAreaSubDos = ({
            title: dis.language.getString('es.dependency.subareados'),
            type:1,
            gridType:5, 
            value:"descripcion",
            id:"subAreaSubDos"
        });
        
        dis.columnOtroArea = ({
            title: dis.language.getString('es.dependency.otroarea'),
            type:1,
            gridType:1,
            id:"otroArea"
        });
        
        dis.columnOtroSubArea = ({
            title: dis.language.getString('es.dependency.otrosubarea'),
            type:1,
            gridType:1,
            id:"otroSubArea"
        });
        eval(dis.onload);
        console.log('dependencyObj ... initialized. '+dis.onload);
    }
//this.initialize();
}
