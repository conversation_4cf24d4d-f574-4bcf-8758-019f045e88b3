define({
  root: {
    'text: #catalogoLabel': 'Perfil',
    licenseCode: 'Esquema de licencias',
    language_strings: [
      { element: '#window_title', text: 'Control de perfiles' }
    ],
    Validate: {
      edit_success: 'El perfil ha sido modificado con éxito.',
      exist_record: 'No se pudo guardar el perfil, está repetido.',
      toggle_status_success: 'El estatus del perfil ha sido actualizado exitosamente.',
      toggling_status: 'Se está actualizando el estatus del perfil...',
      fail: 'Ocurrió un error: %error%'
    },
    messages: {
      accept: 'Aceptar',
      cancel: 'Cancelar',
      statusChangeSuccess: 'Se cambio el estado del rubro correctamente.',
      statusChangeFail: 'No se ha podido cambiar el estado del rubro, intente de nuevo más tarde',
      statusChangeMsg: '¿Esta seguro de que desea cambiar el estado del perfil?'
    },
    escalationManager: 'Escalamiento',
    timeworkManuallyRegister: 'Agregar y modificar registros manualmente de Timework',
    timeworkList: 'Consultar los registros de Timework',
    timeworkSettings: 'Administrar preferencias de Timework',
    formStopwatchRegister: 'Registrar Timework en Formularios',
    formStopwatchList: 'Consultar los registros de Timework en Formularios',
    timeworkRegister: 'Registrar Timework',
    timeworkSync: 'Sincronizar Timework',
    formularie: 'Formulario',
    description: 'Descripción',
    tress: 'Interfaz TRESS',
    admin: 'Administración',
    audit: 'Auditoría',
    action: 'Hallazgo',
    project: 'Proyecto',
    meeting: 'Reunión',
    document: 'Documentos',
    meter: 'Indicador',
    poll: 'Encuesta',
    complaint: 'Quejas',
    device: 'Equipo',
    report: 'Reportes',
    lvl: 'Nivel de acceso',
    edit: 'Editar',
    fiveS: '5S+1',
    catalogs: 'Catálogos',
    reader: 'Lector',
    answer: 'Responder',
    editor: 'Editor',
    in_charge: 'Encargado',
    findingModifyResponsible: 'Modificar responsable en actividad',
    findingModifyImplementation: 'Modificar fecha de implementación en actividad',
    findingModifyVerifier: 'Modificar verificador en actividad',
    findingModifyVerification: 'Modificar fecha de verificación en actividad',
    findingSuperFiller: 'Realizar cualquier actividad',
    findingSuperVerifier: 'Verificar cualquier actividad',
    findingSetApply: 'Marcar cualquier actividad como cancelada',
    findingMaxOpenTimeIgnore: 'Indica si se ignora el tiempo máximo abierto de actividad',
    findingRemoveObjectLinks: 'Eliminar comentarios, documentos y archivos de actividades',
    findingModifyDescription: 'Modificar la descripción de la actividad',
    print_uncontrolled_copies: 'Imprimir Copias No Controladas',
    shareDocument: 'Compartir documentos',
    questionnaires: 'Cuestionarios',
    module_manager: 'Encargado del módulo',
    lead_auditor: 'Auditor líder',
    auditor_support: 'Auditor de apoyo',
    audit_modify_responsible: 'Modificar responsable en actividad',
    audit_modify_implementation: 'Modificar fecha de implementación en actividad',
    audit_modify_verifier: 'Modificar verificador en actividad',
    audit_modify_verification: 'Modificar fecha de verificación en actividad',
    audit_super_filler: 'Realizar cualquier actividad',
    audit_super_verifier: 'Verificar cualquier actividad',
    audit_setApply: 'Marcar cualquier actividad como cancelada',
    audit_max_open_time_ignore: 'Indica si se ignora el tiempo máximo abierto de actividad',
    audit_remove_object_links: 'Eliminar comentarios, documentos y archivos de actividades',
    audit_modify_description: 'Modificar la descripción de la actividad',
    create_team: 'Crear equipos',
    edit_team: 'Editar equipos',
    consult_team: 'Consultar equipos',
    discarded_team: 'Consultar equipos desechados',
    program_services: 'Programar servicios',
    sche_services: 'Ver servicios programados',
    service_history: 'Ver historial de servicios',
    perform_service: 'Realizar servicios',
    service_metric: 'Ver métrica de servicios',
    supervisor: 'Supervisor',
    accounting: 'Contabilidad',
    access: 'Accesos',
    catalog: 'Catálogos',
    system: 'Sistema',
    print_fives: 'Imprimir',
    link_fives: 'Relacionar',
    create_fives: 'Crear y Editar',
    manager_fives: 'Encargado',
    map_fives: 'Mapas',
    section: 'Sección',
    item: 'Artículos',
    catalogsMeetings: 'Reuniones',
    bulkAccessCreator: 'Alta de usuarios con Excel',
    catalogsDocuments: 'Documentos',
    catalogsFormularies: 'Formularios',
    catalogsAudits: 'Auditorías',
    catalogsActivities: 'Actividades',
    catalogsActions: 'Acciones',
    catalogsSurveys: 'Encuestas',
    catalogsComplaints: 'Quejas',
    catalogsMeters: 'Indicadores',
    catalogsProjects: 'Proyectos',
    catalogsDevices: 'Equipos',
    catalogsFiveS: '5S+1',
    catalogsGenerals: 'Generales',
    createForm: 'Crear formularios',
    controlForm: 'Control de formularios',
    fillOutHistory: 'Historial de solicitudes de llenado',
    intBFillOutHistory: 'Historial de solicitudes de llenado',
    fillOnly: 'Llenar formularios',
    fillForm: 'Llenar formularios',
    formFillImpersonation: 'Llenar en nombre de otros usuarios',
    by_org: 'Ver acciones de la organización',
    formActivityModifyResponsible: 'Modificar el responsable de la actividad',
    formActivityModifyImplementation: 'Modificar fecha de implementación en actividad',
    formActivityModifyVerifier: 'Modificar el verificador de la actividad',
    formActivityModifyVerification: 'Modificar fecha de verificación en actividad',
    formActivitySuperFiller: 'Realizar cualquier actividad',
    formActivitySuperVerifier: 'Verificar cualquier actividad',
    formActivitySetApply: 'Cancelar cualquier actividad',
    formActivityMaxOpenTimeIgnore: 'Indica si se ignora el tiempo máximo abierto de actividad',
    formActivityRemoveObjectLinks: 'Eliminar comentarios, documentos y archivos de actividades',
    formActivityModifyDescription: 'Modificar la descripción de cualquier actividad',
    formActivityModifyRecurrence: 'Editar la serie de cualquier actividad',
    formActivityDeleteRecurrence: 'Cancelar la serie de cualquier actividad',
    unrestrictedDocumentAccess: 'Acceso a todos los documentos',
    initializedAtDocumentViewer: 'Visor al inicio',
    activity: 'Actividades',
    activityCreator: 'Crear actividades',
    activityTemplateCreator: 'Crear plantillas publicas',
    activityViewer: 'Ver actividades',
    activityManager: 'Encargado del módulo',
    activityModifyResponsible: 'Modificar responsable',
    activityModifyImplementation: 'Modificar fecha de implementación',
    activityModifyVerifier: 'Modificar verificador',
    activityModifyVerification: 'Modificar fecha de verificación',
    activitySuperFiller: 'Realizar cualquier actividad',
    activitySuperVerifier: 'Verificar cualquier actividad',
    activitySetApply: 'Marcar cualquier actividad como cancelada',
    activityMaxOpenTimeIgnore: 'Indica si se ignora el tiempo máximo abierto de actividad',
    activityRemoveObjectLinks: 'Eliminar comentarios, documentos y archivos',
    activityModifyDescription: 'Modificar la descripción de la actividad',
    report_audit: 'Reportes de auditorías',
    report_finding: 'Reportes de acciones',
    report_document: 'Reportes de documentos',
    reportForm: 'Reportes de formularios',
    reportActivity: 'Reportes de actividades',
    reportTimesheet: 'Reportes de time',
    activityModifyRecurrence: 'Editar la serie de cualquier actividad',
    activityDeleteRecurrence: 'Cancelar la serie de cualquier actividad',
    activityReportsUser: 'Reportes a nivel personal',
    activityReportsDepartment: 'Reportes a nivel {department}',
    activityReportsBussinesUnit: 'Reportes a nivel {facility}',
    activityReportsAll: 'Ver todas las actividades',
    survey: 'Encuesta de satisfacción',
    createSurvey: 'Crear encuestas',
    timeWork: 'Timework',
    timeSheet: 'Timesheet',
    timesheetPlanned: 'Registro planeado',
    timesheetUnplanned: 'Registro no planeado',
    timesheetAllRegisterPlant: 'Total registros de {facility}',
    timesheetAllRegisterDept: 'Total registros de {department}',
    plannerCreator: 'Crear proyectos',
    plannerActivityModifyResponsible: 'Modificar líder del proyecto',
    plannerActivitySuperFiller: 'Marcar realizado cualquier proyecto o tarea',
    plannerActivitySetApply: 'Cancelar cualquier proyecto o tarea',
    plannerActivityRemoveObjectLinks: 'Eliminar comentarios, documentos,archivos de proyectos y tareas',
    plannerActivityModifyDescription: 'Modificar la descripción de proyectos y tareas',
    plannerAddAnyTasks: 'Agregar tareas a cualquier proyecto',
    plannerActivityModifyImplementation: 'Modificar fecha de término en proyectos y tareas',
    plannerViewOwned: 'Ver proyectos en los que participa',
    plannerViewBusinessUnit: 'Ver proyectos de {theFacility}',
    plannerViewBusinessUnitDep: 'Ver proyectos de {theDepartment}',
    plannerViewAll: 'Ver todos los proyectos',
    tsRegisterConnection: 'Timesheet - Crear conexiones',
    tsDeleteConnection: 'Timesheet - Eliminar conexiones',
    tsViewConnection: 'Timesheet - Ver conexiones',
    tsRegisterQuery: 'Timesheet - Registrar consultas',
    tsDeleteQuery: 'Timesheet - Eliminar consultas',
    tsEnableCache: 'Timesheet - Eliminar caché',
    tsSyncCache: 'Timesheet - Sincronizar caché',
    tsViewQuery: 'Timesheet - Ver consultas',
    tsViewSyncEvent: 'Timesheet - Ver eventos de caché',
    tsRegisterReport: 'Timesheet - Crear reportes',
    tsAdminReportAcess: 'Asignar permisos a reportes',
    tsDeleteReport: 'Eliminar reportes',
    honSurveyCreator: 'Crear eventos',
    honSurveyModifier: 'Modificar eventos',
    honSurveyView: 'Visualizar progreso',
    honSurveyViewAll: 'Ver todas las encuestas',
    log: 'Bitacora'
  },
  en: true,
  'es-DA': true,
  'es-AI': true
});
