define({
    language_strings: [
        {element: '#mainTitle', text: 'Realized Services'},
        {element: 'label[for="department\\.description"]', text: '{Department}'},
        {element: 'label[for="device\\.code"]', text: 'Code'},
        {element: 'label[for="device\\.description"]', text: 'Device'},
        {element: 'label[for="deviceGroup\\.description"]', text: 'Group'},
        {element: 'label[for="attendant\\.description"]', text: 'Service Attendant'},
        {element: 'label[for="result\\.description"]', text: 'Result'},
        {element: 'label[for="validFrom"]', text: 'Realized On'},
        {element: 'label[for="validTo"]', text: 'Valid To'},
        {element: 'label[for="planned"]', text: 'Planned On'},
        {element: 'label[for="status"]', text: 'Status'},
        {element: 'label[for="scheduled"]', text: 'Scheduled'},
        {element: 'label[for="nextService"]', text: 'Next Service'},
        {element: 'label[for="lastService"]', text: 'Valid From'},
        {element: 'label[for="device\\.department\\.description"]', text: '{Department}'},
        {element: 'label[for="device\\.deviceGroup\\.description"]', text: 'Group'},
        {element: '#grid_services_header', text: 'Realize'},
        {element: '#grid_edit_header', text: 'View'}
    ],
    StatusTitle: {
        onTime: 'Realized on time',
        onRange: 'Realized on valid time',
        outOfTime: 'Realized out of time'
    },
    Grid: {
        department: '{Department}',
        code: 'Code',
        device: 'Device',
        group: 'Group',
        attendant: 'Attendant',
        result: 'Result',
        validFrom: 'Realized On',
        validTo: 'Valid To',
        plannedOn: 'Planned On',
        status: 'Status',
        deviceDescription: 'Device Description:',
        scheduled: 'Scheduled',
        view: 'View',
        tagYes: 'YES',
        tagNo: 'NO',
        approve: 'Approve'
    },
    Header: {Pattern: {Device: '{description} with code {code}, asigned to {attendant.description}',
        Service: 'Service of {schedule.serviceType.description} to device {schedule.device.description} with code {schedule.device.code}'}},
    label: {
        approveTitle: 'Approve service'
    }
});
