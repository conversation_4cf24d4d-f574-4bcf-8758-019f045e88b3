define({
    
        language_strings:[
            {element:'#mainTitle',text:'Planeación de Corridas'},
            {element:'label[for="deviceId"]',text:'Protocolo:'},
            {element:'#lblDeviceDescription',text:'Descripción del protocolo:'},
            {element:'#lblFirstOccurence',text:'Fecha de primer ejecución:'},
            {element:'label[for="serviceTypeId"]',text:'Tipo de ejecución:'},
            {element:'label[for="responsibleId"]',text:'Responsable del ejecución:'},
            {element:'#label[for="periodicityTypeId"]',text:'Repetir:'},
            {element:'#lblAdvanceDays',text:'Días de anticipación para notificar:'},
            {element:'#saveBtn',value:'Guardar'},
            {element:'#lblGenerateKey',text:'Generar automáticamente:'},
            {element:'#cleanBtn',value:'Limpiar'},
            {element:'#cancelBtn',value:'Cancelar'},
            {element:'#lblCode',text:'Clave del corridas'},
            //--------------------------------------------------------
            {element:'label[for="variableId"]',text:'Variable:'},
            {element:'label[for="unitId"]',text:'Unidad:'},
            {element:'label[for="patternId"]',text:'Patrón:'},
            {element:'#lblPoint',text:'Punto de prueba:'},
            {element:'#lblTreshold',text:'Tolerancia:'},
            {element:'#insertMeasurement',value:'Agregar'},
            {element:'#cancelMeasurement',value:'Cancelar'},
            {element:'#updateMeasurement',value:'Guardar'}
        ],
        Validate:{
            add_success:'La corrida se  guardó correctamente.',
            edit_success: 'La corrida se modificó correctamente.',
            exist_record: "No se puede guardar la corrida, verifique que no esté repetida.",
            saving: "Se están guardando los datos de la corrida...",
            fail: "Ocurrió un error: %error%",
            back: "Regresar",
            periodicity:"No se puede programar un servicio sin seleccionar un intervalo."
        },
        Pattern:{
            Device: 'protocolo {description} con número {code}, cuyo encargado es {attendant.description}'
        },
        Popup:{
            addMeasurement:'Agregar mediciones',
            selectTag:'-- Seleccione --'
        },
        Grid:{
            variable:'Variable',
            unit:'Unidad',
            pattern:'Patrón',
            point:'Punto',
            more:'',
            less:'',
            treshold:'Tolerancia',
            edit:'Editar',
            deleted:'Eliminar',
            no_records:'No se encontraron registros'
        },
        label:{
          treshold:'Solo se pueden introducir 13 caracteres numéricos y 5 decimales.',
          max_days:'Solo se aceptan hasta 3 caracteres numéricos.'
        }
});
