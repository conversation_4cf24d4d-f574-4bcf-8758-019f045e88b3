define({
    root:{
        language_strings:[
            {element:'#mainTitle',text:'Programaciones por autorizar'}      
        ],
        Validate:{
            add_success:'La programación de servicio se inactivó correctamente.',
            edit_success: 'La programación de servicio se inactivó correctamente.',
            exist_record: "No se puede guardar el estatus de programación, verifique que no esté repetido.",
            saving: "Se están guardando los datos de la programación de servicio...",
            fail: "Ocurrió un error: %error%",
            toggling_confirm:'Se inactivará la programación del servicio, y dejará de aparecer en la lista\n\n¿Está seguro de continuar?'
        },
        Pattern:{
            ServiceSchedule: 'Servicio de {serviceType.description} al equipo {device.description} con código {device.code}'
        },
        statusLst:[
          firstComboElement,
          'No Programado',
          'Activo',
          'Inactivo'
        ],
        label:{
          status:'Estado',
          device:'Equipo',
          device_code:'Clav<PERSON>',
          service_type:'Tipo de servicio',
          responsible:'Encargado',
          next_service:'Vencimiento',
          last_service:'Vigente desde',
          department:'{Department}',
          group:'Grupo',
          perform:'Aprobar',
          edit:'Editar',
          approveTitle:'Aprobar la programación'
        }
    },
    en:true,
    "es-JA":true
});