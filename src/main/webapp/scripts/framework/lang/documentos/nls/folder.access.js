define(['bnext/i18n!lang/nls/main'], function(l) {
  return {
    root: {
      'title: #addBusinessUnitBtn': 'Agregar {facilities}',
      'title: #addUserBtn': 'Agregar usuarios',
      'title: #addDepartmentBtn': 'Agregar {departments}',
      'title: #addProcessBtn': 'Agregar procesos',
      language_strings: [
        { element: '#mainTitle', text: 'Opciones de carpeta' },
        { element: '.finishBtn', value: 'Finalizar' },
        { element: '#lblDescription', text: 'Nombre de la carpeta' },
        { element: '#addUserBtn', value: 'Usuarios' },
        { element: '#addDepartmentBtn', value: '{Departments}' },
        { element: '#addProcessBtn', value: 'Procesos' },
        { element: '#addBusinessUnitBtn', value: '{Facilities}' },
        { element: '#saveBtn', value: 'Guardar' },
        { element: '#cleanBtn', value: 'Limpiar' },
        { element: '#cancelBtn', value: 'Cancelar' },
        { element: 'option[value=""]', text: l.firstComboElement }
      ],
      Validate: {
        description: 'Descripción',
        code: 'Clave',
        pushRow: 'Agregar',
        popRow: 'Eliminar',
        btnAdd: 'Recargar',
        descriptionRequired: 'El nombre de la carpeta es un campo requerido',

        sin_users: 'No se han agregado usuarios.',
        sin_user_paginar: 'No se encontraron más coincidencias de usuarios.',
        confirm_user_delete: 'Se removerá al usuario de la lista de permisos \n¿Está seguro de continuar?',
        users: 'Agregar usuarios',

        sin_departments: 'No se han agregado {departments}.',
        sin_departments_paginar: 'No se encontraron más coincidencias de {departments}.',
        confirm_department_deletfe: 'Se removerá a {theDepartment} de la lista de permisos \n¿Está seguro de continuar?',
        departments: 'Agregar {departments}',

        sin_process: 'No se han agregado procesos.',
        sin_process_paginar: 'No se encontraron más coincidencias de procesos.',
        process: 'Agregar procesos',

        sin_businessUnits: 'No se han agregado {facilities}.',
        sin_businessUnits_paginar: 'No se encontraron más coincidencias de {facilities}.',
        businessUnits: 'Agregar {facilities}'
      },
      confirm_message: 'La información será guardada.\n\n¿Está seguro de continuar?',
      cancel_message: 'La información no será guardada.\n\n¿Está seguro de continuar?',
      confirmSaveUpdate: 'El registro fue modificado correctamente. Será enviado a la carpeta.',
      confirmConfigurationSaveUpdate: 'El registro fue modificado correctamente. Será enviado al listado de reportes.',
      confirmFormularieSaveUpdate: 'El registro fue modificado correctamente. Será enviado a Reportes de Formularios.',
      confirmTimesheetSaveUpdate: 'El registro fue modificado correctamente. Será enviado a Reportes de Timesheet.',
      confirmAnswersSaveUpdate: 'Los permisos fueron modificados correctamente. Será enviado a la ventana de control para respuestas de formularios.',
      configurationTitle: 'Permisos del reporte',
      configurationName: 'Nombre:',
      answersTitle: 'Permisos del formulario',
      answersName: 'Nombre:',
      fullName: 'Nombre completo',
      account: 'Cuenta',
      mail: 'Correo',
      colNamesAvatar: 'Avatar',
      node_access_business_unit_dialog_lang_title: 'Seleccionar {facility}',
      node_access_users_dialog_lang_title: 'Seleccionar usuario',
      node_access_departments_dialog_lang_title: 'Seleccionar {department}',
      node_access_area_dialog_lang_title: 'Seleccionar proceso'
    },
    en: true,
    'es-CIESA': true
  };
});