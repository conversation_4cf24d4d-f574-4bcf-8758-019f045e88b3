define(['bnext/i18n!lang/nls/main'], function(l) {
  ;
  return {
    'title: #addBusinessUnitBtn': 'Add {facility}',
    'title: #addUserBtn': 'Add user',
    'title: #addDepartmentBtn': 'Add {departments}',
    'title: #addProcessBtn': 'Add process',
    language_strings: [
      { element: '#mainTitle', text: 'Folder options' },
      { element: '#answersTitle', text: 'Survey options' },
      { element: '.finishBtn', value: 'Finalize' },
      { element: '#lblDescription', text: 'Folder name' },
      { element: '#addUserBtn', value: 'Users' },
      { element: '#addDepartmentBtn', value: '{Departments}' },
      { element: '#addProcessBtn', value: 'Processes' },
      { element: '#addBusinessUnitBtn', value: '{Facility}' },
      { element: '#saveBtn', value: 'Save' },
      { element: '#cleanBtn', value: 'Clean' },
      { element: '#cancelBtn', value: 'Cancel' },
      { element: 'option[value=""]', text: l.firstComboElement }
    ],
    Validate: {
      description: 'Description',
      code: 'ID',
      pushRow: 'Add',
      popRow: 'Delete',
      btnAdd: 'Reload',
      descriptionRequired: 'The folder name is required',

      sin_users: 'No users have been added.',
      sin_user_paginar: 'No more users were found.',
      confirm_user_delete: 'The user will be removed from the list.\nAre you sure?',
      users: 'Add user',

      sin_departments: 'No {departments} have been added.',
      sin_departments_paginar: 'No more {departments} found.',
      departments: 'Add {departments}',

      sin_process: 'No process have been added.',
      sin_process_paginar: 'No more processes found.',
      process: 'Add process',

      sin_businessUnits: 'No {facility} have been added.',
      sin_businessUnits_paginar: 'No more {facility} found.',
      businessUnits: 'Add {facility}'
    },
    confirm_message: 'The data will be saved.\n\nDo you wish to continue?',
    cancel_message: 'The data will not be saved.\n\nDo you wish to continue?',
    confirmSaveUpdate: 'Folder access has been successfully modified, you´ll be sent back to the folder.',
    confirmConfigurationSaveUpdate: 'Folder access has been successfully modified, you´ll be sent back to the report list.',
    confirmFormularieSaveUpdate: 'Folder access has been successfully modified, you´ll be sent back to the form reports list.',
    confirmTimesheetSaveUpdate: 'The record has been updated successfully, you´ll be sent back to the timesheet report.',
    confirmAnswersSaveUpdate: 'The permissions were modified correctly. You will be sent to the control window for form responses.',
    configurationTitle: 'Access settings',
    configurationName: 'Name:',
    answersTitle: 'Form permissions',
    answersName: 'Name:',
    fullName: 'Full name',
    account: 'Account',
    mail: 'Email',
    colNamesAvatar: 'Avatar',
    node_access_business_unit_dialog_lang_title: 'Select {facility}',
    node_access_users_dialog_lang_title: 'Select user',
    node_access_departments_dialog_lang_title: 'Select {department}',
    node_access_area_dialog_lang_title: 'Select process'
  };
});