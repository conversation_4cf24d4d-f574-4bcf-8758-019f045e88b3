define(['bnext/i18n!lang/nls/main'], function(mainLang) {
    return {
        'text:  #window_title': 'Documents',
        'value: #closeDialog': 'Accept',
        'value: #closeDialogCancel': 'Cancel',
        colNames_status: 'Status',
        colNames_code: 'ID ',
        colNames_title: 'Document',
        colNames_originator: 'Originator',
        colNames_version: 'Review',
        colNames_creationDate: 'Creation date',
        colNames_lastModDate: 'Approval date',
        colNames_edit: 'Assign readers',
        colNames_add: 'Add',
        colNames_remove: 'Remove',
        colNames_bUnits:mainLang.bussinessUnits,
        colNames_oUnits:mainLang.organizationalUnits,
        label_reader_added: 'Reader has been successfully added.',
        label_reader_removed: 'Reader has been successfully removed.',
        label_accept: 'Accept',
        label_mailEnabled: 'Notifications has been send to {} reader(s).',
        label_mailDisabled: 'Reader(s) has been set without sending them an email, system is configured to not send mail notifications.',
        label_active: 'Active',
        label_unread: 'Unread',
        label_readUnassigned: 'Read unassigned',
        label_readAssigned: 'Read assigned',
        message_unread: 'No added readers',
        labelMailPreviousVersion: 'have been added {} users automatically by reading the document (Readers of the previous version).'
    };
});