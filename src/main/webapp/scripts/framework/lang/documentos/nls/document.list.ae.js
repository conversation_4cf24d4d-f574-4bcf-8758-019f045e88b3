define(['bnext/i18n!bnext/administrator/document/nls/document-module', 
        'bnext/i18n!lang/configuracion/nls/configuration.department'], function (documentModule, department) {
    return {
        root: {
            language_strings: [
                {element: '#window_title', text: 'Lista de relaciones'},
                {element: '#new_folder', title: 'Presione para crear una nueva carpeta'},
                {element: '#new_controlled_doc', title: 'Presione para crear un nuevo documento'},
                {element: '#new_uncontrolled_doc', title: 'Presione para crear un documento no controlado'},
                {element: '#new_media', title: 'Presione para crear un nuevo medio'},
                {element: '#new_folder_link', title: 'Presione para modificar las referencias'},
                {element: '#delete_folder', title: 'Presione para eliminar la carpeta'},
                {element: '#folder_user_access', title: 'Presione para modificar los permisos'},
                {element: '#move_folder', title: 'Presione para mover la carpeta'},
                {element: '#lblFolderName', text: 'Nombre de la carpeta:'},
                {element: '#add_new_folder', value: 'Agregar'},
                {element: '#cancel_new_folder', value: 'Cancelar'},
                {element: '#goBack', value: 'Regresar'}
            ],
            colNameDocumentControlledType: documentModule.colNameDocumentControlledType,
            documentControlledTypeControlled: documentModule.documentControlledTypeControlled,
            documentControlledTypeUncontrolled: documentModule.documentControlledTypeUncontrolled,
            colNamesVersion: documentModule.version,
            colNamesAuthor: documentModule.versionAuthor,
            colNamesVideo: 'Videos',
            colNamesPlay: 'Reproducir',
            colNamesMove: 'Mover',
            colNamesFechaEnt: documentModule.colNamesFechaEnt,
            colNamesFechaApr: documentModule.colNamesFechaApr,
            colNames: {
                status: 'Estado',
                code: 'Clave ',
                version: documentModule.version,
                title: 'Documento',
                author: documentModule.versionAuthor,
                fechaEnt: 'Fecha Entrada',
                fechaApr: 'Fecha Aprobación',
                edit: 'Ver',
                delete_: 'Borrar',
                control: 'Documentos controlados',
                noControl: 'Documentos no controlados',
                media: 'Medios',
                thumbnail: 'Thumbnail',
                reference: 'Documentos Referenciados',
                redirect: 'Mover documento',
                loadrelated: 'Mostrar documentos relacionados...',
                download: 'Descargar',
                validTo: 'Fecha de vencimiento',
                type: 'Tipo de documento'
            },
            messages_deleteNode: '¿Está seguro de que desea borrar esta carpeta? Sólo se podrá eliminar una carpeta que se encuentre vacía.',
            messages_deleteNodeSuccess: 'La carpeta fue eliminada correctamente.',       
            
            messages: {
                yes: 'Sí',
                no: 'No',
                error: 'Se ha cometido un error, favor de volver a intentarlo más tarde.',
                accept: 'Aceptar',
                cancel: 'Cancelar',
                deleteMsg: '¿Esta seguro de que desea eliminar el documento ${title}?',
                deleteSuccess: 'El documento ha sido borrado exitosamente.',
                imgDelMsg: '¿Esta seguro de que quiere borrar el medio ${title}?',
                imgDelSuccess: 'El medio ha sido borrado exitosamente.',
                moveFolder: '¿Está seguro que desea mover la carpeta?',
                selectFolder: 'Seleccione la carpeta de destino',
                moveFolderSuccess: 'La carpeta ha sido movida.',
                stillMoveFolder: 'La carpeta no se ha podido mover.',
                moveDocument: '¿Está seguro que desea mover el documento?',
                selectDocument: 'Seleccione la carpeta a la que moverá el documento',
                changeDispositionSuccess: 'La disposición ha cambiado correctamente.',
                changeRetetntionSuccess: 'El tiempo de retención ha cambiado correctamente.',
                moveDocumentSuccess: 'El documento ha sido movido.',
                stillMoveDocument: 'El documento no se ha podido mover. \nNo cuenta con permisos en la carpeta seleccionada.',
                deleteMediaMsg: 'El video será eliminado.<br>¿Está seguro de continuar?',
                deleteMediaSuccess: 'Se ha eliminado el video correctamente.'
            },
            nameStatus: {
                active: 'Activo',
                no_control: 'No controlado',
                no_control_eliminated: 'No controlado eliminado',
                in_edition: 'En edición',
                none: '--SELECCIONE--'
            },
            label: {
                add_folder: 'Agregar carpeta',
                add_folder_success: 'La carpeta se ha creado correctamente.',
                too_long_folder_name: 'El nombre de la carpeta es desmasiado largo.',
                no_empty_name: 'La Carpeta debe tener un nombre.',
                add_folder_error: 'La carpeta no pudo ser creada.',
                duplicate_folder: 'La carpeta no pudo ser creada, ya existe una con el mismo nombre.'
            },
            moveFolderToRepository: '¿Está seguro que desea mover la carpeta al repositorio "${destinationFolder}"?',
            moveFolderToFolder: '¿Está seguro que desea mover la carpeta a la carpeta destino "${destinationFolder}"?',
            askForPermissions: '¿Desea mantener los permisos actuales?',
            toggle_success: 'Se ha cambiado exitosamente el estado del documento.',
            toggleMsg: '¿Está seguro de que desea cambiar el estado del documento ${title}?',
            in_authorization: 'No se pudo cambiar el estado. El documento tiene la solicitud ${requestCode} en proceso de autorización.',
            columnBusinessUnit: '{Facility}',
            columnDepartment: department.colNameDepartment,
            shouldClose: '¿Está seguro de que desea cancelar esta solicitud?',
            titleNew: 'Documento nuevo',
            tabNameDocument: 'Documentos',
            messageStillFolder:'No se puede eliminar la carpeta porque aún contiene otras carpetas.',
            messageStillFiles:'No se puede eliminar la carpeta porque aún contiene documentos.',
            messageStillImages:'No se puede eliminar la carpeta porque aún contiene medios.',
            messageStillHasPendingRequests: 'No se puede eliminar la carpeta porque aún contiene documentos con solicitudes activas.',
            columnNameRelatedCodes: 'Clave de documentos relacionados',
            columnNameRelatedNames: 'Título de documentos relacionados',
            colNameFolders: 'Carpetas',
            colNameFolderExplore: 'Explorar',
            colNameFolderName: 'Nombre',
            colNameFolderPath: 'Ruta',
            download: 'Descargar',
            messagePreviewVideo: 'El video no puede ser reproducido en el sistema.<BR><BR> Puede descargarlo para verlo en su equipo.',
            'title: #addFavorite': 'Agregar a favorito(s)',
            'title: #moreOptions': 'Configuración',
            newRecord: 'Documento',
            newRecordTitle: 'Clic para solicitar un nuevo documento',
            newFolder: 'Carpeta',
            newFolderTitle: 'Presione para crear una nueva carpeta',
            newMedia: 'Medio',
            newMediaTitle: 'Clic para crear un nuevo medio'
        },
        en: true,
        "es-AA": true,
        "es-FRISA": true,
        "es-CB": true,
        "es-CC":true,
        'es-MI':true
    };
});