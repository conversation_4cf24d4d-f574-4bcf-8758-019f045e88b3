define({
    'text: #lblDescription': 'Description:',
    'value: #cancelBtn': 'Back',
    'value: #finishBtn_gridDetail': 'Close',
    'value: #flowPreviewButton':'Workflow',
    colNames_missingPosition: 'Missing users',
    colNames_missingUser: 'Missing user',
    colNames_missingBoss: 'Missing boss',
    /**
     *   Consultar "RequestType.js" para ver el significado
     *   de cada numero
     */
    colName_finishDate: 'Authorization date',
    colName_finishDate7: 'Filling date',
    windowTitle1: 'New document authorization detail',
    windowTitle2: 'Document modification authorization detail',
    windowTitle3: 'Document re-approval authorization detail',
    windowTitle4: 'Document cancellation authorization detail',
    windowTitle7: 'Fill',
    windowTitle8: 'Flow detail',
    patternSequence: 'Request from business unit {businessUnitName} of {type} for document "{description}" with code {documentCode}',
    patternSequence7: 'Request from business unit {businessUnitName} of {type} for document "{description}" with fill code {documentCode}',
    colNamesDateFill: 'Filling date or signature',
    colNamesIndex: 'Order',
    colNames_description: 'User/Position',
    colNames_comment: 'Comment',
    statusNames_pending: 'Programmed',
    statusNames_inProgress: 'Processing',
    statusNames_authorized: 'Authorized',
    statusNames_unauthorized: 'Unauthorized',
    statusNames_undefined: 'Undefined',
    statusNames_returned: 'Returned',
    statusNames_skipped: 'Skipped',
    requestTypeCreation: 'creation',
    requestTypeModification: 'modification',
    requestTypeApproval: 'approval',
    requestTypeCancellation: 'cancellation',
    requestTypeFill: 'fill',
    cycleFlow: 'Flow cycle',
    action: 'Action',
    approvedBy: 'Approved by',
    commentAuthorization: 'Comment authorization',
    rejectedBy: 'Rejected by',
    commentRejection: 'Comment rejection',
    contentRejected: 'Content rejected',
    clicView: 'Click to view the content rejected'
});