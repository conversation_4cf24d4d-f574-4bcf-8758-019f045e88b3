define({
    root: {
        language_strings: [
            {element: '#window_title', text: 'Secuencias'},
            {element: '#grid_fechaHoraCreacion_header', text: 'Creación'},
            {element: '#grid_document.description_header', text: 'Título del documento'},
            {element: '#grid_document.code_header', text: 'Clave del documento'},
            {element: '#grid_nextAutorizer_header', text: 'Próximo autorizante'},
            {element: '#grid_status_header', text: 'Estado'},
            {element: '#grid_code_header', text: 'Clave'},
            {element: '#grid_limit_header', text: 'Fecha límite'}
        ],
        colNames: {
            status: 'Estado',
            code: 'ID',
            title: 'Documento',
            author: 'Originador',
            fechaEnt: 'Fecha Entrada',
            fechaApr: 'Fecha Aprobación',
            edit: 'Ver',
            delete_: 'Borrar',
            control: 'Documentos controlados',
            noControl: 'Documentos no controlados',
            media: 'Medios',
            documents:'Documentos',
            creation:'Creación',
            title_doc:'Título del documento',
            code_doc:'Clave del documento',
            n_autho:'Próximo autorizante',
            deadline:'Fecha límite',
            requestType:'Tipo de solicitud',
            request:'Clave de solicitud'
        },
        messages: {
            toggleMsg: '¿Esta seguro de que desea cambiar el estado del documento ${title}?',
            yes: 'Sí',
            no: 'No',
            toggleSuccess: 'Se ha cambiado exitosamente el estado del documento',
            error: 'Se ha cometido un error, favor de volver a intentarlo más tarde',
            accept: 'Aceptar',
            deleteMsg: '¿Esta seguro de que desea eliminar el documento ${title}',
            deleteSuccess: 'El documento ha sido borrado exitosamente.',
            imgDelMsg: '¿Esta seguro de que quiere borrar el medio ${title}?',
            imgDelSuccess: 'El medio ha sido borrado exitosamente.'
        },
        statusNames:['--SELECCIONE--','Activa','Finalizada','Rechazada'],
        requestTypes:['Nueva','Modificación','Reaprobación','Cancelación'],
        titleVerify: 'Detalle de la solicitud',
        outOfRange: 'Fuera de rango',
        approvers: 'Autorizadores'
    },
    en:true
});