define({
    'text: #catalogoLabel': 'Authorization flow',
    language_strings:[
        {element:'#window_title',text:'Authorization flow control'},
        {element:'#title',text:'Title'}
    ],
    label_description: 'Title',
    statusLst:{
        active:'Active',
        inactive:'Inactive'
    },
    colName:{
        status:'Status',
        title:'Title',
        edit:'Edit'
    },
    messages:{
        accept:'Accept',
        cancel: 'Cancel',
        statusChangeSuccess:'The template status has been successfully changed.',
        statusChangeFail:'The template status cannot be changed, please try again later.',
        statusChangeMsg:'Are you sure you want to change the template status?'
    },
    colNameScope: 'Scope',
    colNameOrganizationUnit: 'Business unit',
    statusNames: ['--Select--', 'Active', 'inactive']
});