define({
        'text: #mainTitle': 'Edit data',
        'text: #language option[value=""]': 'System language',
        'text: #language option[value="es-MX"]': 'Spanish (Mexico)',
        'text: #language option[value="en-US"]': 'English (United States)',
        'es-MX': 'Spanish (Mexico)',
        'en-US': 'English (United States)',
        'text: #lblLanguage': 'Language',
        'text: select[size=2] option[value=1]': 'Yes',
        'text: select[size=2] option[value=0]': 'No',
        'text: #lblName': 'Full name',
        'text: #lblUserAccount': 'User account',
        'text: #lblMail': 'E-mail',
        'text: #lblGridSize': 'Records per page',
        'text: #lblDetailGridSize': 'Records in details pages',
        'text: #lblFloatingGridSize': 'Results in emerging windows',
        'text: #lblSearchInSubFolders': 'Search by subfolders',
        'text: #lblShowExternalDialog': 'Show download dialog for Bnext Launcher',
        'text: #lblShowWelcomeDialog': 'Show welcome dialog',
        'text: #lblLegacyOpenGrid': 'Keep filters open during session',
        'text: #lblEdit': 'Edit',
        'placeholder: #isAwayReason': 'Reason',
        'text: .is-away-info-false': 'Enter the reason for your absence and select a substitute analyst for each department where your name is marked in green',
        'text: .is-away-info-true': 'You can modify the current surrogate marked in red, using the option "Modify surrogate(s)"',
        'text: .form-authorizers-section': 'Mark my status as absent to delegate pending authorization from the forms module',
        'text: .business-unit-department-status-name': 'Status',
        'text: .business-unit-department-name': '{Department}',
        'text: .cancelation-approver-user-name': 'Cancellation approver',
        'text: .adjustment-approver-user-name': 'Adjustments approver',
        'text: .reopen-approver-user-name': 'Reopening approver',
        'text: .analyst': 'Auxiliar approver analysts',
        'text: [for="isAway"]': 'I\'m away',
        'text: [for="changeAwayReplacement"]': 'Modify substitute(s)',
        'text: table .analyst-select-container option[value=""]': '-- Select the substitute analyst --',
        'text: #lblTimezone': 'Time zone',
        'value: #saveBtn': 'Save',
        'value: #cancelBtn': 'Cancel',
        'value: #cleanBtn': 'Clear',
        reasonToShowInactive: '{TheDepartment} is inactive, is displayed because there are associated active forms in which you must choose the substitute analyst.',
        isActive: '{TheDepartment} is active',
        requiredNombre: "Enter username",
        requiredCuenta: "Enter an account",
        requiredCorreo: "Enter an email",
        requiredgridSize: "Enter the registration number",
        validCorreo: "The codes don\'t match",
        maxlengthName: 'The name must not exceed 255 characters',
        exit_record: 'The account has been successfully updated. You will need to log in again.',
        exit_operation: 'The operation has been carried out. You will need to log in again for the changes to take effect.',
        accept: 'Accept',
        users: 'Users',
        idioma: 'Language',
        cancel_message: "The information will be saved and it will be sent to the pending screen.\n\nDo you really want to continue?",
        'value: #openAvatarSelector': 'Update avatar',
        'text: #avatar-title': 'Update avatar',
        'value: #button-close': 'Close',
        'value: #button-refresh': 'Refresh',
        avatarTitle: 'Update avatar',
        uploadImage: 'Upload picture',
        deleteImage: 'Delete picture',
        downloadImage: 'Download picture',
        restartPage: 'The avatar was saved successfully. Refreshing the page is required to view the changes.',
        uploadFailed: 'Error loading image, try again, if the error persists contact your administrator',
        deleteFailed: 'Error delete image, try again, if the error persists contact your administrator',
        openMenuFavorites: 'Modify favorites',
        'openMailSettingsMine': 'My mail settings',
        lblPassword: 'Modify password'
        
});