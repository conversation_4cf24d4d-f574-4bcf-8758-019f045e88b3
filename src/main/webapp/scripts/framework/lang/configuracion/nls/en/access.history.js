define({
  language_strings: [{ element: '#window_title', text: 'Access history' }],
  colNames: {
    user: 'User',
    fecha_ent: 'Enter date',
    fecha_sal: 'Exit date',
    hora_ent: 'Enter time',
    hora_sal: 'Exit time'
  },
  colNameLocation: 'Location',
  colNameLoginSource: 'Source',
  colNameAccount: 'Account',
  colNameIPAddress: 'IP address',
  colNameUserAgent: 'User-Agent',
  colNamesToken: 'Token',
  login_source_catalog: 'Bnext QMS',
  login_source_ldap: 'LDAP',
  colNameLcationLatitude: 'Latitude',
  colNameLcationLongitude: 'Longitude',
  device: 'Device',
  pageWidth: 'Page width (px)',
  pageHeight: 'Page height (px)',
  screenWidth: 'Screen width (px)',
  screenHeight: 'Screen height (px)',
  devicePixelRatio: 'Device pixel ratio',
  deviceOrientation: 'Page orientation',
  orientation: {
    'landscape-primary': 'Landscape (Default)',
    'landscape-secondary': 'Landscape (Rotated)',
    'portrait-primary': 'Portrait (Default)',
    'portrait-secondary:': 'Portrait (Rotated)'
  }
});
