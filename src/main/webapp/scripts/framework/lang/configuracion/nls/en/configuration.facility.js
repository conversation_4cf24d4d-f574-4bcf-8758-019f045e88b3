define({
    /***Planta***/
    facility: '{Facility}',
    facilities: '{Facilities}',
    colNamefacility: '{Facility}',
    colCorp: 'Business unit',
    addFcility: 'Create {facility}',
    editFacility: 'Edit {facility}',
    facilitySupervisor: '{Facility} supervisor',
    addSuccess: '{Facility} saved correctly.',
    ediSuccess:'{TheFacility} has been successfully modified.',
    existRecord:"{TheFacility} cannot be saved, please verify it isn\'t duplicated.",
    saving:"Saving {facility}...",
    controlFacility:"Facility control",
    toggleStatusSuccess:'{TheFacility} status has been successfully updated.',
    controlFacilityDepartment:"Relationship between {facility} and {departments}",
    togglingStatus:"Updating status of {facility}...",
    togglingConfirm:"The status of {facility} will change \nDo you want to continue?",
    
    /***Departamentos-Planta***/
    addFacilityDepartment:"Create relationship between {facility} and {department}",
    togglingConfirmDepartment:"The status of {Facility}/{Department} will change \nDo you want to continue?",
    
    /***Preferencias documentos***/
    lblMailToBusinessUnitManager: "Send e-mail to {facility} documents supervisor:",
    lblMailToBusinessUnitPermission: "Send e-mail to the users with permissions per {facility} in the folder:",
    lblExpiredDocumentPendingDocumentManager: "Assign task of expired documents to {theFacility} or {theDepartment} document supervisor:",
    lblExpiredDocumentMailDocumentManager: "Send e-mail of expired documents to {theFacility} or {theDepartment} document supervisor:",
    
    /***Documentos-Plantillas***/
    lblCorp: "Business unit",
    duplicateDetails: 'Existing {facility}',
    DUPLICATE_KEY_PHYSICAL_LOCATION: 'There is already a {facility} with this physical location, please try again with a new one.',
    DUPLICATE_KEY_CODE: 'There is already a {facility} with this ID, please try again with a new one.'
});