define({
    root:{
        language_strings:[
            {element:'#mainTitle',text:'Alta de unidades de negocio'},
            {element:'#lblCode',text:'Clave'},
            {element:'#lblDescription',text:'Nombre'},
            {element:'#lblGenerateKey',text:'Generar automáticamente'},
            {element:'label[for="predecessorId"]',text:'Nivel superior'},
            {element:'label[for="documentManagerId"]',text:'Encargado de documentos'},
            {element:'#saveBtn',value:'Guardar'},
            {element:'#cleanBtn',value:'Limpiar'},
            {element:'#cancelBtn',value:'Cancelar'},
            {element:'#lblDeleteImage',text:'Eiminar Imagen',title:'Clic para eliminar'}
        ],
        Validate:{
            add_success:'La unidad de negocio ha sido agregada con éxito.',
            edit_success:'La unidad de negocio ha sido modificada con éxito.',
            exist_record:"No se puede guardar la unidad de negocio, por favor verifique que no esté repetida.",
            saving:"Se están guardando los datos de la unidad de negocio...",
            fail:"Ocurrió un error: %error%"           
        },
        Default:{
            selectTag:'-- Seleccione --'
        },
        startingConversion: 'Cargando...',
        invalidImage: 'Por favor seleccione un archivo de tipo imagen.',
        filePickerDownloadTitle: 'Clic para descargar el archivo.',
        filePickerViewTitle: 'Clic para ver el archivo.',
        lblLogo: 'Logo',
        filePickerUploading: 'Cargando...'
    },
    en:true,
    'es-CIESA':true
});        