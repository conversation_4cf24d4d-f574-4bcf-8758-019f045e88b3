define({
    'text: .physicalLocation': 'Physical location',
    'text: .expandable-container-target option[value="1"]': 'Yes',
    'text: .expandable-container-target option[value="0"]': 'No',
    language_strings: [
        {element: '#lblCode', text: 'ID'},
        {element: '#lblDescription', text: 'Name'},
        {element: '#lblCopy', text: 'Controlled copies'},
        {element: '#lblCopyUncontrolled', text: 'Uncontrolled copies'},
        {element: '.lblGeneralHeader', text: 'General header'},
        {element: '.lblHeaderTitle', text: 'Header title'},
        {element: '.lblDaysToEscalate', text: 'Days to escalate'},
        {element: '.lblWatermark', text: 'Watermark'},
        {element: '.lblWatermarkOpacity', text: 'Opacity'},
        {element: '.lblFooterTitle', text: 'Footer title'},
        {element: '.lblGeneralFooter', text: 'General footer'},
        {element: '#lblGenerateKey', text: 'Auto generate'},
        {element: 'label[for="predecessor"]', text: 'Upper level'},
        {element: 'label[for="documentManagerId"]', text: 'Document supervisor'},
        {element: 'label[for="news"]', text: 'News'},
        {element: '#saveBtn', value: 'Save'},
        {element: '#cleanBtn', value: 'Clear'},
        {element: '#cancelBtn', value: 'Cancel'},
        {element: '.lblHeaderSize', text: 'Header size'},
        {element: '.lblFooterSize', text: 'Footer Size'}
    ],
    Validate: {
        fail: "There was an error: %error%",
        missingCombo: 'Select an option',
        invalid_required_text: 'This field is mandatory and supports alphanumeric text up to %specify% characters',
        invalidText: "Invalid value.",
        placeHolder: 'E.g. 10% (Including the symbol %)'
    },
    Default: {
        selectTag: '-- Select --'
    },
    startingConversion: 'Please wait...',
    invalidImage: 'The file must be an image.',
    filePickerDownloadTitle: 'Click to download the file.',
    lblLogo: 'Logo:',
    filePickerUploading: 'Please wait...'
});