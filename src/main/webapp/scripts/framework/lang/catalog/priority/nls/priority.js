define(['bnext/i18n!bnext/administrator/finding/nls/finding'], function(findingI18n) { 
    var entityName = findingI18n['text: .intPriorityLbl'].toLowerCase();
    return {
    root:{
        language_strings:[
            /*Titulo de la ventana*/
                {element:"#mainTitle",text:"Alta de " + entityName},
            {element:'#lblGenerateKey',text:'Generar automáticamente'},
            /*Elementos de la forma*/
            {element:"#lblCode",text:"Clave"},
            {element:"#lblDescription",text:"Descripción"},
            {element:"#lblValue",text:"Valor:"},
            /*Botones*/
            {element:"#saveBtn",value:"Guardar"},
            {element:"#cancelBtn",value:"Cancelar"},
            {element:"#cleanBtn",value:"Limpiar"}
        ],
        Validate:{
            /*Mensajes de Validación*/
            yes: 'Sí',
            no: 'No',
                confirm_message: '¿Seguro que desea dar de alta esta ' + entityName + '?',
                requiredTitle:"Ingresa el nombre de la " + entityName,
                requiredDescription:"Ingresa la descripción de la " + entityName,
            missingCombo:"Seleccione un elemento.",
            add_success:"Registro agregado correctamente.",
            edit_success:"Registro modificado correctamente.",
            exist_record:"No se pudo guardar el registro, está repetido.",
            success:"Operación exitosa.",
            fail:"Ocurrió un error."            
        },
        messages: {
            'cancel':'¿Seguro que desea cancelar?',
            loadingControl: 'Cargando'
        }
    },
    en:true,
    'es-MI':true
    };
});