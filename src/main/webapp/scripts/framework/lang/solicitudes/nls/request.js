define(['bnext/i18n!lang/nls/main', 'bnext/i18n!bnext/administrator/document/nls/document-module'], (main, documentModule) => ({
  root: {
    language_strings: [
      { element: '#window_title', text: 'Solicitudes' },
      { element: '#new_document', text: 'Documento nuevo' }
    ],
    colNamesVersion: documentModule.version,
    colNamesAuthor: documentModule.versionAuthor,
    colNamesFechaEnt: documentModule.colNamesFechaEnt,
    colNamesFechaApr: documentModule.colNamesFechaApr,
    colNamesCancel: 'Cancelar',
    colNames: {
      status: 'Estado',
      code: 'Clave',
      version: 'Edición',
      title: 'Documento',
      author: 'Autor de la edición actual',
      edit: 'Modificar',
      reApr: 'Re-aprobar',
      cancel: 'Cancelación',
      view: 'Detalle'
    },
    formLabel: 'Formulario',
    docuLabel: 'Documento',
    messages: {
      shouldClose: '¿Está seguro de que desea cancelar esta solicitud?'
    },
    buttons: {
      accept: main.accept,
      cancel: main.cancel
    },
    common: {
      titleEdit: 'Solicitud de modificación de documento',
      titleNew: 'Solicitud de documento nuevo',
      titleReapr: 'Solicitud de re-aprobación de documento',
      titleCancel: 'Solicitud de cancelación de documento',
      titleNewForm: 'Solicitud de nuevo formulario',
      dialogSuccess: 'La solicitud ha sido guardada exitosamente.',
      dialogAccept: 'Aceptar',
      dialogFail: 'La solicitud no ha sido guardada debido a que la clave o nombre del ',
      dialogFail_: 'documento ya existen en la aplicación',
      dialogNotSave: 'La solicitud no ha sido guardada',
      handleFormRequest_sureMessage: '¿Seguro de que desea realizar una solicitud de "%type%" un formulario?<br>Será enviado a una pantalla diferente.',
      handleFormRequest_title: 'Solicitud a un formulario',
      newDocumentFormType: 'Nuevo documento tipo formulario',
      pleaseSelectAnOption: 'Por favor seleccione una opción',
      pleaseSelectABaseForm: 'Por favor seleccione un formulario base'
    },
    columnBusinessUnit: '{Facility}',
    dictionaryError: 'No es posible cambiar el tipo debido a que difiere en diccionario.',
    columnDepartment: '{Department}',
    colNameDocumentControlledType: 'Clasificación',
    documentControlledTypeControlled: 'Documento controlado',
    documentControlledTypeUncontrolled: 'Documento no controlado',
    dialogSuccessUncontrolledSave: 'El documento ha sido publicado en la carpeta correspondiente',
    noForm: '-- Sin formulario base --',
    'value: #newRecord': 'Documento',
    'title: #newRecord': 'Clic para solicitar un nuevo documento',
    newRecord: 'Documento'
  },
  en: true,
  'es-FRISA': true,
  'es-AA': true,
  'es-CB': true,
  'es-CC': true,
  'es-MI': true,
  'es-CIESA': true
}));
