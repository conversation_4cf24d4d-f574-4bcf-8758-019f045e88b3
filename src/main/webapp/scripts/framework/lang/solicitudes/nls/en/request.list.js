define({
  language_strings: [{ element: '#window_title', text: 'Request list' }],
  colNames: {
    status: 'Status',
    code: 'Request ID',
    version: 'Version',
    type: 'Request type',
    date: 'Request date',
    verify: 'Request verify',
    author: 'Author',
    doc_code: 'Document ID',
    doc_title: 'Document title',
    nodo: 'Folder',
    reason: 'Request reason',
    file: 'File uploaded',
    edit: 'View',
    unlock: 'Unblock',
    pendingTab: 'Pending',
    attendedTab: 'Attended',
    ALLTab: 'All'
  },
  colNamesRejectionDate: 'Rejection date',
  statusNames: {
    selection: '-- SELECT --',
    resquested: 'Requested',
    rejected: 'Rejected',
    returned: 'Returned',
    in_process: 'In process',
    finished: 'Completed'
  },
  statusNameStandBy: 'Draft',
  commonNames: {
    titleVerify: 'Document verification'
  },
  Validate: {
    unlock: 'Unlocked',
    lock: 'Locked',
    unlock_message: 'The request will be unlocked. Are you sure to continue?',
    unlock_success: 'Request correctly unlocked.',
    yes: 'Yes',
    no: 'No',
    _new: 'New',
    _modification: 'Modification',
    _edit_details: 'Edit details',
    _re_approval: 'Re-approval',
    _cancellation: 'Cancellation',
    _promotion: 'Promotion',
    _new_form: 'New form',
    _modification_form: 'Form modification',
    _edit_details_form: 'Form details edition',
    _re_approval_form: 'Form re-approval',
    _cancellation_form: 'Form cancellation',
    _fill_form: 'Form filling'
  },
  columnBusinessUnit: '{Facility}',
  columnDepartment: '{Department}',
  columnAuthorizersNames: 'Authorizer(s)',
  colNameDocumentControlledType: 'Classification',
  documentControlledTypeControlled: 'Controlled document',
  documentControlledTypeUncontrolled: 'Uncontrolled document'
});
