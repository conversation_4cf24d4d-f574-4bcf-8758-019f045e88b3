define({
    language_strings:[
        {element:'#window_title',text:'List of requests to be verified'}
    ],
    colNames:{
        status:'Status',
        code:'ID',
        type:'Type',
        date:'Date',
        reject: 'Reject',
        verify: 'Verify',
        author:'Author',
        doc_code:'Document code',
        doc_desc:'Document title',
        file:'File uploaded',
        edit:'Detail',
        pendingTab:'Pending requests',
        attendedTab:'Request served',
        ALLTab:'-- All --',
        folder: 'Folder',
        department: '{Department}',
        docType: 'Document type',
        reason: 'Request reason'
    },
    codeGenerate: 'Not generated yet.',
    messages:{
        _new : 'New',
        _modification : 'Modification',
        _re_approval : 'Re-approval',
        _cancellation : 'Cancellation',
        _promotion : 'Promotion',
        _new_form : 'New form',
        _modification_form : 'Form Modification',
        _re_approval_form : 'Form Re-approval',
        _cancellation_form : 'Form Cancellation',
        rejectedFor: 'The request was rejected with this comment: ',
        specify_reason:'You must specify a reason for rejecting the request',
        decline_request:'Are you sure you want to reject this request',
        accept:'Accept',
        cancel:'Cancel',
        documentVerification:'Document verification',
        requestBusy:'This application is under verification by another user, please try again later.',
        rejectReason:'Reason for rejection:',
        requestReason:'Request reason'
    },
    colNameDocumentControlledType: 'Classification',
    documentControlledTypeControlled: 'Controlled document',
    documentControlledTypeUncontrolled: 'Uncontrolled document',
    Validate:{
        add_success:'Request verified correctly.',
        decline_success:'The request was succesfully rejected.'
    },
    Cubes_color:['--SELECT--','Requested','Rejected','Returned','In process','Completed'],
    saving: 'Saving...'
});