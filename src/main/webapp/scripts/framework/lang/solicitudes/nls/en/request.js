define({
    language_strings:[
        {element:'#window_title',text:'Requests'},
        {element:'#new_document',text:'New document'}
    ],
    colNamesCancel: 'Cancel',
    colNames:{
        status:'Status',
        code:'ID',
        version: 'Version',
        title:'Document',
        author:'Author of current version',
        fechaEnt:'Creation date',
        fechaApr:'Approval date',
        edit:'Modify',
        reApr:'New approval',
        cancel:'Cancel',
        view: 'Detail'
    },
    formLabel: 'Form',
    docuLabel: 'Document',
    dictionaryError: "You can not change the type because it differs in dictionary.",
    messages:{
            shouldClose:'Do you really want to cancel this request?'
    },
    common:{
            titleEdit:'Modify document',
            titleNew:'New document',
            titleReapr:'Re-approve document',
            titleCancel:'Cancel document',
            dialogSuccess: 'The request has been saved successfully.',
            dialogAccept: 'Accept',
            dialogFail: 'La solicitud no ha sido guardada debido a que la clave o nombre del ',
            dialogFail_: 'documento ya existen en la aplicación',
            dialogNotSave: 'La solicitud no ha sido guardada',
            handleFormRequest_sureMessage:'Are you sure you want to make a "%type%" request?<br>The current screen will close.',
            handleFormRequest_title:'Form request',
            newDocumentFormType:'New form document',
            pleaseSelectAnOption: 'Please select an option',
            pleaseSelectABaseForm: 'Please select a base form'
        },
    columnBusinessUnit: '{Facility}',
    columnDepartment: '{Department}',
    colNameDocumentControlledType: 'Classification',
    documentControlledTypeControlled: 'Controlled document',
    documentControlledTypeUncontrolled: 'Uncontrolled document',
    noForm: '-- No base form --',
    'value: #newRecord': 'Document',
    'title: #newRecord': 'Click to request a new document',
    newRecord: 'Document',
    
});