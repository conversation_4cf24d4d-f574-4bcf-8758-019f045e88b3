define(['bnext/i18n!lang/nls/main'], (main) => ({
  root: {
    language_strings: [
      { element: '#window_title', text: 'Solicitudes' },
      { element: '#new_document', text: 'Documento nuevo' }
    ],
    colNames: {
      status: 'Estado',
      code: 'Clave',
      version: 'Revisión',
      title: 'Documento',
      author: 'Autor de la revisión actual',
      fechaEnt: 'Fecha Creación',
      fechaApr: 'Fecha Aprobado',
      edit: 'Modificar',
      reApr: 'Re-aprobar',
      cancel: 'Cancelación',
      view: 'Detalle'
    },
    messages: {
      shouldClose: '¿Está seguro de que desea cancelar esta solicitud?'
    },
    buttons: {
      accept: main.accept,
      cancel: main.cancel
    },
    common: {
      titleEdit: 'Modificar documento',
      titleNew: 'Documento nuevo',
      titleReapr: 'Re-aprobar documento',
      titleCancel: 'Cancelar documento',
      dialogSuccess: 'La solicitud ha sido guardada exitosamente.',
      dialogAccept: 'Aceptar',
      dialogFail: 'La solicitud no ha sido guardada debido a que la clave o nombre del ',
      dialogFail_: 'documento ya existen en la aplicación',
      dialogNotSave: 'La solicitud no ha sido guardada',
      handleFormRequest_sureMessage: '¿Seguro de que desea realizar una solicitud de "%type%" un formulario?<br>Será enviado a una pantalla diferente.',
      handleFormRequest_title: 'Solicitud a un formulario',
      newDocumentFormType: 'Nuevo documento tipo formulario',
      pleaseSelectAnOption: 'Por favor seleccione una opción',
      pleaseSelectABaseForm: 'Por favor seleccione un formulario base'
    },
    dictionaryError: 'No es posible cambiar el tipo debido a que difiere en diccionario.',
    columnBusinessUnit: '{Facility}',
    columnDepartment: '{Department}'
  },
  en: true,
  'es-AA': true
}));
