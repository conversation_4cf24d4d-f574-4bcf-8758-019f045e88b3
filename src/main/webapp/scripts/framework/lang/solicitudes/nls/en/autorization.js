define({
  language_strings: [{ element: '#window_title', text: 'List of authorizing documents' }],
  colNameStatus: 'Status',
  colNameCode: 'ID',
  colNameType: 'Type',
  colNameDate: 'Date',
  colNameAuthor: 'Author',
  colNameDocCode: 'Document code',
  colNameFile: 'File uploaded',
  colNameEdit: 'Authorize',
  colNameDocDescription: 'Document title',
  colNameDocumentType: 'Document Type',
  colNameReazon: 'Reazon for request',
  colNameBusinessUnitDepartment: '{Department}',
  tabNameNew: 'New',
  tabNameModify: 'Modification',
  tabNameReapprove: 'Re-approval',
  tabNameCancel: 'Cancellation',
  _new: 'New',
  _modification: 'Modification',
  _edit_details: 'Edit details',
  _re_approval: 'Re-approval',
  _cancellation: 'Cancellation',
  _promotion: 'Promotion',
  _new_form: 'New form',
  _modification_form: 'Form Modification',
  _edit_details_form: 'Form details edition',
  _re_approval_form: 'Form Re-approval',
  _cancellation_form: 'Form Cancellation',
  _fill_form: 'Form filling',
  Cubes_color: ['--SELECT--', 'Requested', 'Rejected', 'Returned', 'In process', 'Completed'],
  Validate: {
    unlock_message: 'The request will be unlocked.<br><br>Are you sure to continue?',
    unlock_success: 'Request correctly unlocked',
    yes: 'Yes',
    no: 'No',
    _new: 'New',
    _modification: 'Modification',
    _edit_details: 'Edit details',
    _re_approval: 'Re-approval',
    _cancellation: 'Cancellation',
    _promotion: 'Promotion',
    _new_form: 'New form',
    _modification_form: 'Form Modification',
    _edit_details_form: 'Form details edition',
    _re_approval_form: 'Form Re-approval',
    _cancellation_form: 'Form Cancellation',
    _fill_form: 'Form filling'
  },
  authorizeDocument: 'Authorize the document',
  autorized: 'The request was succesfully authorized.',
  autorizationFailed: 'There was an error authorizing the document.',
  autorization_already_completed: 'The request has already been authorized.',
  autorization_in_process: 'The request is already being processed in another transaction.'
});
