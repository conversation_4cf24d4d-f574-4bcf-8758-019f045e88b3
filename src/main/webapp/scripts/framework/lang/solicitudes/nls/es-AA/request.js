define(['bnext/i18n!lang/nls/main'], (main) => ({
  language_strings: [{ element: '#window_title', text: 'Alta de Solicitudes' }],
  colNames: {
    status: 'Estado',
    code: 'Clave',
    version: 'Versión',
    title: 'Documento',
    author: 'Autor de la versión actual',
    fechaEnt: 'Fecha Creación',
    fechaApr: '<PERSON>cha Aprobado',
    edit: 'Modificar',
    reApr: 'Re-aprobar',
    cancel: 'Cancelación',
    view: 'Detalle'
  },
  messages: {
    shouldClose: '¿Está seguro de que desea cancelar esta solicitud?'
  },
  buttons: {
    accept: main.accept,
    cancel: main.cancel
  },
  common: {
    titleEdit: 'Solicitud de modificación de documento',
    titleNew: 'Solicitud de documento nuevo',
    titleReapr: 'Solicitud de re-aprobación de documento',
    titleCancel: 'Solicitud de cancelación de documento',
    dialogSuccess: 'La solicitud ha sido guardada exitosamente.',
    dialogAccept: 'Aceptar',
    dialogFail: 'La solicitud no ha sido guardada debido a que la clave o nombre del ',
    dialogFail_: 'documento ya existen en la aplicación',
    dialogNotSave: 'La solicitud no ha sido guardada',
    handleFormRequest_sureMessage: '¿Seguro de que desea realizar una solicitud de "%type%" un formulario?<br>Será enviado a una pantalla diferente.',
    handleFormRequest_title: 'Solicitud a un formulario',
    newDocumentFormType: 'Nuevo documento tipo formulario',
    pleaseSelectAnOption: 'Por favor seleccione una opción',
    pleaseSelectABaseForm: 'Por favor seleccione un formulario base'
  },
  dictionaryError: 'No es posible cambiar el tipo debido a que difiere en diccionario.',
  columnBusinessUnit: '{Facility}',
  columnDepartment: '{Department}',
  documentControlledTypeControlled: 'Documento controlado.',
  documentControlledTypeUncontrolled: 'Documento por descargar'
}));
