define(['jquery', 'dojo/Deferred', 'dojo/dom', 'dojo/_base/array', 'dojo/_base/lang', 'bnext/_base/url-util', 'polyfill'], ($, Deferred, dom, array, lang, util) => {
  const win = window;
  win.trace = false;
  function loadLanguage(_language_strings) {
    array.forEach(_language_strings, (item) => {
      try {
        item.element = (item.element || '').replace(/\[(.+?=)\]/g, '[$1""]');
        const e = $(item.element);
        const tagName = e.get(0) ? e.get(0).tagName.toUpperCase() : false;
        e.text(item.text);
        e.before(item.before);
        e.after(item.after);
        e.append(item.append);
        e.prepend(item.prepend);
        e.prop('title', item.title);
        if (
          tagName &&
          ((item.value && $.inArray(tagName, ['INPUT', 'SELECT', 'TEXTAREA']) === -1) || (item.text && $.inArray(tagName, ['INPUT', 'SELECT', 'TEXTAREA']) !== -1))
        ) {
          console?.error(
            `Es posible que el idioma del TAG "${item.element}"esté incorrectamente configurado, se esta usando un ${item.value ? 'VALUE' : 'TEXT'} en un ${tagName}`
          );
        }
        e.val(item.value || e.val());
        e.prop('href', item.href);
        e.prop('target', item.target);
        $(item.element).append(item.content);
        const id = e.attr('id');
        const elem = dom.byId(id);
        if (id !== undefined && elem && elem.replicateLanguage) {
          elem.replicateLanguage();
        }
      } catch (t) {
        alert(t);
        console.error('core.js ... loadLanguage');
        console.error(t);
      }
    });
  }
  function setLang(lang, _onLoaded, isOld) {
    const def = new Deferred();
    require([`bnext/i18n!${lang}`], (_i18n) => {
      setLanguageByLang(_i18n, _onLoaded, isOld);
      def.resolve(_i18n);
    });
    return def.promise;
  }
  /*Define el lenguaje de la vista*/
  function setLanguageByLang(_i18n, _onLoaded, isOld) {
    //carga los lenguajes que se definen directamente sobre bnext.core
    _i18n.language_strings &&
      array.forEach(win.language_strings, (o) => {
        _i18n.language_strings.unshift(o);
      });
    win.language_strings = null;
    isOld && $.extend(true, win, _i18n);
    //se recorre sintaxis nueva para definición de language_strings
    if (!_i18n.language_strings) {
      _i18n.language_strings = [];
    }
    for (const k in _i18n) {
      const valueAvailable = k.indexOf('value:') !== -1;
      const textAvailable = k.indexOf('text:') !== -1;
      const titleAvailable = k.indexOf('title:') !== -1;
      if (!valueAvailable && !textAvailable && !titleAvailable) {
        continue;
      }
      if (titleAvailable) {
        _i18n.language_strings.push({
          element: k.substring(6).trim(),
          title: _i18n[k]
        });
      }
      if (textAvailable) {
        _i18n.language_strings.push({
          element: k.substring(5).trim(),
          text: _i18n[k]
        });
      }
      if (valueAvailable) {
        _i18n.language_strings.push({
          element: k.substring(6).trim(),
          value: _i18n[k]
        });
      }
      delete _i18n[k];
    }
    loadLanguage(_i18n.language_strings);
    const _GET = util.getUrlVars();
    if (_GET && typeof _i18n.titles !== 'undefined' && typeof _GET.type === 'string') {
      const _WT = dom.byId('window_title');
      if (_WT) {
        _WT.innerText = _i18n.titles[_GET.type] || '';
      }
    }
    const _onLoadedLang = _onLoaded || win.onLoadedLanguages;
    _onLoadedLang && typeof _onLoadedLang === 'function' && _onLoadedLang(_i18n);
  }
  function specifiedMessage(msg, prop, value) {
    let property = prop;
    let message = msg;
    if (!property && !value) {
      const _return = {
        set: (returnProp, value) => {
          const localProperty = new RegExp(`%${returnProp}%`, 'g');
          message = message.toString().replace(localProperty, value);
          return _return;
        },
        get: () => message
      };
      return _return;
    }
    if (property && Array.isArray(property)) {
      message = lang.clone(message);
      array.forEach(property, (obj) => {
        const prop = new RegExp(`%${obj.property}%`);
        const val = obj.value;
        message = message.toString().replace(prop, val);
      });
      return message;
    }
    property = new RegExp(`%${property}%`, 'g');
    return message.toString().replace(property, value);
  }

  /*
   * Define el lenguaje de la vista
   *
   * @deprecated utilizar "setLang":
   * En core.setLanguage las variables de idioma caen como globales dentro de window, ejemplo de cambio
   *
   *  De:
   *      core.setLanguage('documentos/document.type.list')
   *
   *  Cambia a:
   *      core.setLang('lang/documentos/nls/document.type.list')
   *
   **/
  function setLanguage(lang, _onLoaded) {
    const language = `lang/${lang.substring(0, lang.lastIndexOf('/'))}/nls${lang.substring(lang.lastIndexOf('/'))}`;
    return setLang(language, _onLoaded, true);
  }
  function getLang() {
    if (dom.byId('SYSTEM_LANGUAGE')) {
      return dom.byId('SYSTEM_LANGUAGE').value || 'es';
    }
    const match = /(qms\/)([a-z]+?(|$)(-[a-zA-Z]+)?)(\/|$)/g.exec(window.top.location.pathname)[2];
    if (match) {
      const lang = match[2];
      if (lang.indexOf('es') === 0) {
        return 'es';
      }
      if (lang.indexOf('en') === 0) {
        return 'en';
      }
    }
    return 'es';
  }
  // noinspection UnnecessaryLocalVariableJS
  const i18nUtil = {
    setLang: setLang,
    loadLanguage: loadLanguage,
    setLanguage: setLanguage,
    getLang: getLang,
    setLanguageByLang: setLanguageByLang,
    specifiedMessage: specifiedMessage
  };
  return i18nUtil;
});
