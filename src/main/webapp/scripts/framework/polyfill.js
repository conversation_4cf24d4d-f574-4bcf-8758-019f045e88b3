//Compatibilidad con IE8 para funcion indexOf en arreglos
Array.prototype.indexOf||(Array.prototype.indexOf=function(r,t){var n;if(null==this)throw new TypeError('"this" is null or not defined');var e=Object(this),i=e.length>>>0;if(0===i)return-1;var a=+t||0;if(1/0===Math.abs(a)&&(a=0),a>=i)return-1;for(n=Math.max(0>a?i-Math.abs(a):a,0);i>n;){if(n in e&&e[n]===r)return n;n++}return-1});
String.prototype.contains||(String.prototype.contains=function(){return-1!==String.prototype.indexOf.apply(this,arguments)});
String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")});
String.prototype.startsWith||(String.prototype.startsWith=function(a,b){return b=b||0,this.indexOf(a,b)===b});
String.prototype.endsWith||(String.prototype.endsWith=function(a,b){var c=this.toString();(void 0===b||b>c.length)&&(b=c.length),b-=a.length;var d=c.indexOf(a,b);return-1!==d&&d===b});
String.prototype.includes||(String.prototype.includes=function(t){var n=!1;return-1!==this.indexOf(t)&&(n=!0),n});
Object["byString"]||(Object["byString"]=function(o,s){s=s["replace"](/\[(\w+)\]/g,".$1");s=s["replace"](/^\./,"");var a=s["split"](".");for(var i=0,n=a["length"];i<n;++i){var k=a[i];if(!(o["constructor"]===Array)){o=[o]};var _o=[];for(var _a=0;_a<o["length"];_a++){if(k in o[_a]){if(o[_a][k]["constructor"]===Array){for(var j=0;j<o[_a][k]["length"];j++){_o["push"](o[_a][k][j])};break}else {_o["push"](o[_a][k])}}else {return "-"}};o=_o};if(!(o["constructor"]===Array)){o=[o]};return o})
navigator.userAgent.toLowerCase().indexOf('firefox') > -1 && (HTMLElement.prototype.click = function() { var evt = this.ownerDocument.createEvent('MouseEvents'); evt.initMouseEvent('click', true, true, this.ownerDocument.defaultView, 1, 0, 0, 0, 0, false, false, false, false, 0, null);this.dispatchEvent(evt);});
Array.prototype.reduce||(Array.prototype.reduce=function(r){"use strict";if(null==this)throw new TypeError("Array.prototype.reduce called on null or undefined");if("function"!=typeof r)throw new TypeError(r+" is not a function");var e,t=Object(this),n=t.length>>>0,o=0;if(2==arguments.length)e=arguments[1];else{for(;n>o&&!(o in t);)o++;if(o>=n)throw new TypeError("Reduce of empty array with no initial value");e=t[o++]}for(;n>o;o++)o in t&&(e=r(e,t[o],o,t));return e});
if (!String.prototype.startsWith) {
  String.prototype.startsWith = function(searchString, position) {
    position = position || 0;
    return this.indexOf(searchString, position) === position;
  };
}
define('polyfill', function(){
    return {};
});
