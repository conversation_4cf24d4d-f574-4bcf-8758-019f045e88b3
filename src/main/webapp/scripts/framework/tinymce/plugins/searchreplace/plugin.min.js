!function(){function e(e,t,n,r,a){function i(e,t){if(t=t||0,!e[0])throw"findAndReplaceDOMText cannot handle zero-length matches";var n=e.index;if(t>0){var r=e[t];if(!r)throw"Invalid capture group";n+=e[0].indexOf(r),e[0]=r}return[n,n+e[0].length,[e[0]]]}function d(e){var t;if(3===e.nodeType)return e.data;if(h[e.nodeName])return"";if(t="",(f[e.nodeName]||p[e.nodeName])&&(t+="\n"),e=e.firstChild)do t+=d(e);while(e=e.nextSibling);return t}function o(e,t,n){var r,a,i,d,o=[],l=0,c=e,s=t.shift(),u=0;e:for(;;){if((f[c.nodeName]||p[c.nodeName])&&l++,3===c.nodeType&&(!a&&c.length+l>=s[1]?(a=c,d=s[1]-l):r&&o.push(c),!r&&c.length+l>s[0]&&(r=c,i=s[0]-l),l+=c.length),r&&a){if(c=n({startNode:r,startNodeIndex:i,endNode:a,endNodeIndex:d,innerNodes:o,match:s[2],matchIndex:u}),l-=a.length-d,r=null,a=null,o=[],s=t.shift(),u++,!s)break}else{if(!h[c.nodeName]&&c.firstChild){c=c.firstChild;continue}if(c.nextSibling){c=c.nextSibling;continue}}for(;;){if(c.nextSibling){c=c.nextSibling;break}if(c.parentNode===e)break e;c=c.parentNode}}}function l(e){var t;if("function"!=typeof e){var n=e.nodeType?e:u.createElement(e);t=function(e,t){var r=n.cloneNode(!1);return r.setAttribute("data-mce-index",t),e&&r.appendChild(u.createTextNode(e)),r}}else t=e;return function(e){var n,r,a,i=e.startNode,d=e.endNode,o=e.matchIndex;if(i===d){var l=i;a=l.parentNode,e.startNodeIndex>0&&(n=u.createTextNode(l.data.substring(0,e.startNodeIndex)),a.insertBefore(n,l));var c=t(e.match[0],o);return a.insertBefore(c,l),e.endNodeIndex<l.length&&(r=u.createTextNode(l.data.substring(e.endNodeIndex)),a.insertBefore(r,l)),l.parentNode.removeChild(l),c}n=u.createTextNode(i.data.substring(0,e.startNodeIndex)),r=u.createTextNode(d.data.substring(e.endNodeIndex));for(var s=t(i.data.substring(e.startNodeIndex),o),f=[],h=0,p=e.innerNodes.length;p>h;++h){var g=e.innerNodes[h],m=t(g.data,o);g.parentNode.replaceChild(m,g),f.push(m)}var x=t(d.data.substring(0,e.endNodeIndex),o);return a=i.parentNode,a.insertBefore(n,i),a.insertBefore(s,i),a.removeChild(i),a=d.parentNode,a.insertBefore(x,d),a.insertBefore(r,d),a.removeChild(d),x}}var c,s,u,f,h,p,g=[],m=0;if(u=t.ownerDocument,f=a.getBlockElements(),h=a.getWhiteSpaceElements(),p=a.getShortEndedElements(),s=d(t)){if(e.global)for(;c=e.exec(s);)g.push(i(c,r));else c=s.match(e),g.push(i(c,r));return g.length&&(m=g.length,o(t,g,l(n))),m}}function t(t){function n(){var e=tinymce.ui.Factory.create({type:"window",layout:"flex",pack:"center",align:"center",onClose:function(){t.focus(),d=!1,o.unmarkAllMatches()},buttons:[{text:"Find",onclick:function(){e.find("form")[0].submit()}},{text:"Replace",disabled:!0,onclick:function(){o.replace(e.find("#replace").value())||e.statusbar.items().slice(1).disabled(!0)}},{text:"Replace all",disabled:!0,onclick:function(){o.replaceAll(e.find("#replace").value()),e.statusbar.items().slice(1).disabled(!0)}},{type:"spacer",flex:1},{text:"Prev",disabled:!0,onclick:function(){o.prev()}},{text:"Next",disabled:!0,onclick:function(){o.next()}}],title:"Find and replace",items:{type:"form",padding:20,labelGap:30,spacing:10,onsubmit:function(t){var n,r,a,i,d;return t.preventDefault(),a=e.find("#case").checked(),d=e.find("#words").checked(),i=e.find("#find").value(),i.length?(i=i.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),i=d?"\\b"+i+"\\b":i,r=new RegExp(i,a?"g":"gi"),n=o.markAllMatches(r),n?o.first():tinymce.ui.MessageBox.alert("Could not find the specified string."),e.statusbar.items().slice(1).disabled(0===n),void 0):(o.unmarkAllMatches(),e.statusbar.items().slice(1).disabled(!0),void 0)},items:[{type:"textbox",name:"find",size:40,label:"Find",value:t.selection.getNode().src},{type:"textbox",name:"replace",size:40,label:"Replace with"},{type:"checkbox",name:"case",text:"Match case",label:" "},{type:"checkbox",name:"words",text:"Whole words",label:" "}]}}).renderTo().reflow();d=!0}function r(e){var t=e.parentNode;t.insertBefore(e.firstChild,e),e.parentNode.removeChild(e)}function a(e,n){function r(){var r,d;for(r=n?t.getBody()[e?"firstChild":"lastChild"]:o[e?"endContainer":"startContainer"],d=new tinymce.dom.TreeWalker(r,t.getBody());r=d.current();){if(1==r.nodeType&&"SPAN"==r.nodeName&&null!==r.getAttribute("data-mce-index"))for(l=r.getAttribute("data-mce-index"),a=r.firstChild;r=d.current();){if(1==r.nodeType&&"SPAN"==r.nodeName&&null!==r.getAttribute("data-mce-index")){if(r.getAttribute("data-mce-index")!==l)return;i=r.firstChild}d[e?"next":"prev"]()}d[e?"next":"prev"]()}}var a,i,d=t.selection,o=d.getRng(!0),l=-1;return e=e!==!1,r(),a&&i&&(t.focus(),e?(o.setStart(a,0),o.setEnd(i,i.length)):(o.setStart(i,0),o.setEnd(a,a.length)),d.scrollIntoView(a.parentNode),d.setRng(o)),l}function i(e){e.parentNode.removeChild(e)}var d,o=this,l=-1;o.init=function(e){e.addMenuItem("searchreplace",{text:"Find and replace",shortcut:"Ctrl+F",onclick:n,separator:"before",context:"edit"}),e.addButton("searchreplace",{tooltip:"Find and replace",shortcut:"Ctrl+F",onclick:n}),e.shortcuts.add("Ctrl+F","",n)},o.markAllMatches=function(n){var r,a;return a=t.dom.create("span",{"class":"mce-match-marker","data-mce-bogus":1}),r=t.getBody(),o.unmarkAllMatches(r),e(n,r,a,!1,t.schema)},o.first=function(){return l=a(!0,!0),-1!==l},o.next=function(){return l=a(!0),-1!==l},o.prev=function(){return l=a(!1),-1!==l},o.replace=function(e,n,d){var o,c,s,u,f,h;if(-1===l&&(l=a(n)),h=a(n),s=t.getBody(),c=tinymce.toArray(s.getElementsByTagName("span")),c.length)for(o=0;o<c.length;o++){var p=c[o].getAttribute("data-mce-index");if(null!==p&&p.length&&(u=f=c[o].getAttribute("data-mce-index"),d||u===l))for(e.length?(c[o].firstChild.nodeValue=e,r(c[o])):i(c[o]);c[++o];)if(u=c[o].getAttribute("data-mce-index"),null!==p&&p.length){if(u!==f){o--;break}i(c[o])}}return-1==h&&(h=a(n,!0)),l=h,d&&t.selection.setCursorLocation(t.getBody(),0),t.undoManager.add(),-1!==l},o.replaceAll=function(e){o.replace(e,!0,!0)},o.unmarkAllMatches=function(){var e,n,a;for(a=t.getBody(),n=a.getElementsByTagName("span"),e=n.length;e--;)a=n[e],a.getAttribute("data-mce-index")&&r(a)},t.on("beforeaddundo keydown",function(e){return d?(e.preventDefault(),!1):void 0})}tinymce.PluginManager.add("searchreplace",t)}();