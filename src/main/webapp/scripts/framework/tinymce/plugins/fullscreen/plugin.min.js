tinymce.PluginManager.add("fullscreen",function(e){function t(){var e,t,n=window,r=document,i=r.body;return i.offsetWidth&&(e=i.offsetWidth,t=i.offsetHeight),n.innerWidth&&n.innerHeight&&(e=n.innerWidth,t=n.innerHeight),{w:e,h:t}}function n(){function n(){s.setStyle(c,"height",t().h-(l.clientHeight-c.clientHeight))}var l,c,u,d=document.body,f=document.documentElement;a=!a,l=e.getContainer().firstChild,c=e.getContentAreaContainer().firstChild,u=c.style,a?(i=u.width,o=u.height,r=l.clientHeight-c.clientHeight,u.width=u.height="100%",s.addClass(d,"mce-fullscreen"),s.addClass(f,"mce-fullscreen"),s.addClass(l,"mce-fullscreen"),s.bind(window,"resize",n),n()):(u.width=i,u.height=o,s.removeClass(d,"mce-fullscreen"),s.removeClass(f,"mce-fullscreen"),s.removeClass(l,"mce-fullscreen"),s.unbind(window,"resize",n)),e.fire("FullscreenStateChanged",{state:a})}var r,i,o,a=!1,s=tinymce.DOM;if(!e.settings.inline)return e.on("init",function(){e.addShortcut("Ctrl+Alt+F","",n)}),e.addMenuItem("fullscreen",{text:"Fullscreen",shortcut:"Ctrl+Alt+F",selectable:!0,onClick:n,onPostRender:function(){var t=this;e.on("FullscreenStateChanged",function(e){t.active(e.state)})},context:"view"}),e.addButton("fullscreen",{tooltip:"Fullscreen",shortcut:"Ctrl+Alt+F",onClick:n,onPostRender:function(){var t=this;e.on("FullscreenStateChanged",function(e){t.active(e.state)})}}),{isFullscreen:function(){return a}}});