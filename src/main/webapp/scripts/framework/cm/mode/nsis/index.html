<!doctype html>

<title>CodeMirror: NSIS mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel=stylesheet href=../../lib/codemirror.css>
<script src=../../lib/codemirror.js></script>
<script src="../../addon/mode/simple.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src=nsis.js></script>
<style type=text/css>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">NSIS</a>
  </ul>
</div>

<article>
<h2>NSIS mode</h2>


<textarea id=code>
; This is a comment
!ifdef ERROR
    !error "Something went wrong"
!endif

OutFile "demo.exe"
RequestExecutionLevel user
SetDetailsPrint listonly

!include "LogicLib.nsh"
!include "WinVer.nsh"

Section -mandatory

    Call logWinVer

    ${If} 1 > 0
      MessageBox MB_OK "Hello world"
    ${EndIf}

SectionEnd

Function logWinVer

    ${If} ${IsWin10}
        DetailPrint "Windows 10!"
    ${ElseIf} ${AtLeastWinVista}
        DetailPrint "We're post-XP"
    ${Else}
        DetailPrint "Legacy system"
    ${EndIf}

FunctionEnd
</textarea>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
    mode: 'nsis',
    indentWithTabs: true,
    smartIndent: true,
    lineNumbers: true,
    matchBrackets: true
  });
</script>

<p><strong>MIME types defined:</strong> <code>text/x-nsis</code>.</p>
</article>