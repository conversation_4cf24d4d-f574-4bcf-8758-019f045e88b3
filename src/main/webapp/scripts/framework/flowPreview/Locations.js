/* global draw2d */
(function(PortLocator,w){
    var Left = PortLocator.extend({
        relocate:function(index, figure){
            var p = figure.getParent();
            this.applyConsiderRotation(figure, 0,p.getHeight()/2);
        }
    });
    var Right = PortLocator.extend({
        relocate:function(index, figure){
            var p = figure.getParent();
            this.applyConsiderRotation(figure, p.getWidth(), p.getHeight()/2);
        }
    });
    var Top = PortLocator.extend({
        relocate:function(index, figure){
            var p = figure.getParent();
            this.applyConsiderRotation(figure, p.getWidth()/2, 0);
        }
    });
    var Bottom = PortLocator.extend({
        relocate:function(index, figure){
            var p = figure.getParent();
            this.applyConsiderRotation(figure, p.getWidth()/2, p.getHeight());
        }
    });
    w.Locations = {
        left: new Left(),
        right: new Right(),
        bottom: new Bottom(),
        top: new Top()
    };
    
})(draw2d.layout.locator.PortLocator,window);