/* 
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
dojo.provide("bnext.uploadComponent");
dojo.require("dijit.Dialog");
dojo.require("dojo.io.iframe");
dojo.require("dojox.html.entities");
dojo.require("dijit.form.Form");
dojo.require("dijit.form.Button");
dojo.require("bnext.misc");
dojo.require("dojo.i18n");
dojo.requireLocalization("bnext", "uploadComponent");
 
uploadComponent = function(file,options){
      
     this.id = "uploadComponent";
     this.messages = dojo.i18n.getLocalization("bnext", "uploadComponent");       
     this.tools = new BnextMisc();
     this.windowTitle=this.messages.windowTitle;  
     this.windowDirections=this.messages.directions;
     this.buttonLabel=this.messages.buttonLabel;
     this.buttonCaption=this.messages.buttonCaption;
     this.cancelLabel=this.messages.buttonCancel 
     this.savePath="files";
     this.fileName=null;
     this.filePath=null;
     this.url=null;
     this.mimeType=null;
     this.peso=null;
     this.fileSize=null;
     this.successMessage=this.messages.success;
     this.failureMessage=this.messages.failure;
     this.selectFileMessage=this.messages.selectFile;
     this.hookOnSuccess=null;
     this.hookOnFailure=null;
     this.window=null;
     this.arguments = new Array();
     this.validations = null;
     this.dialogWindow=null;
     this.setId = function(id){
         this.id=id;
     }
     this.getId = function(){
         return this.id;
     }     
     this.setDialogWindow = function(dialogWindow){
         this.dialogWindow=dialogWindow;
     }
     this.getDialogWindow = function(){
         return this.dialogWindow;
     }
     this.setValidations = function(validations){
         this.validations=validations;
     }
     this.getValidations = function(){
         return this.validations;
     }
     this.setFileSize = function(fileSize){
         this.fileSize=fileSize;
     }
     this.getFileSize = function(){
         return this.fileSize;
     }
     //setters
     this.setWindowTitle= function(title){
         this.windowTitle=title;
     } 
     this.setWindowDirections= function(directions){
         this.windowDirections=directions;
     }
     this.setButtonLabel = function(label){
         this.buttonLabel=label;
     }
     this.setButtonCaption= function(caption){
         this.buttonCaption=caption;
     }
     this.setCancelLabel = function(label){
         this.cancelLabel=label;
     }
     this.setSavePath = function(path){
         this.savePath =path;
     }
     this.setHookOnSuccess = function(hookOnSuccess){
         this.hookOnSuccess=hookOnSuccess;
     }
     this.setHookOnFailure = function(hookOnFailure){
         this.hookOnFailure=hookOnFailure;
     }
     this.setSuccessMessage = function(m){
         this.successMessage=m;
     }      
     this.setFailureMessage = function(m){
         this.failureMessage=m;
     } 
     this.setFilePath= function(path){
        this.filePath=path;
     }
     this.setPeso= function(peso){
         this.peso=peso;
     }
     this.setFileName=function(name){
         this.fileName=name;
     }
     this.setUrl = function(url){
         this.url=url;
     }
     this.setSelectFileMessae = function(message){
         this.selectMessage=message;
     }
     //getters
     this.getWindowTitle= function(){
         return this.windowTitle;
     }          
     this.getWindowDirections = function(){
         return this.windowDirections;
     }
     this.getButtonLabel = function(){
         return this.buttonLabel;
     }
     this.getButtonCaption= function(){
         return this.buttonCaption;
     }
     this.getCancelLabel = function(){
         return this.cancelLabel;
     }
     this.getSavePath = function(){
         return this.savePath;
     }
     this.getHookOnSuccess = function(){
         return this.hookOnSuccess;
     }
     this.getHookOnFailure = function(){
         return this.hookOnFailure;
     }
     this.getSuccessMessage = function(){
         return this.successMessage;
     }
     this.setSelectFileMessage = function(m){
         this.selectFileMessage=m;
     }    
     this.getFailureMessage = function(){
         return this.failureMessage;
     } 
     this.getWindow= function(){
         return this.window;
     }
     this.getFilePath= function(){
         return this.filePath;
     }
     this.getMimeType= function(){
         return this.mimeType;
     }
     this.setMimeType= function(mime){
         this.mimeType=mime;
     }     
     this.getPeso= function(){
         return this.peso;
     }
     this.getFileName= function(){
            return this.fileName;
     }
     this.getUrl = function(){
         return this.url;
     }
     this.getSelectFileMessage = function(){
         return this.selectMessage;
     }
     this.getArguments = function(){
         return this.arguments;
     }
     this.fileForm={
         
        "html":'<form id="{componentId}Upload"  dojoType="dijit.form.Form" class="grid4 gris" action="Upload.fileUploader" method="POST" enctype="multipart/form-data">\n\
                       <div class="envelope border_b_light_gris">\n\
                                <div class="grid4 left">\n\
                                    <div class="grid4">{windowDirections}</div>\n\
                                    <div class="" id="{componentId}Message"></div>\n\
                                    <div class="grid1 left">\n\
                                        <label for="Upload_upload" class="label">{buttonLabel}</label>\n\
                                    </div>\n\
                                    <div class="grid3 left">\n\
                                        <input type="file" name="upload" value="" onChange=\"{componentId}.setFileAttributes(this);\" id="upload"/>\n\
                                    </div>\n\
                                </div>\n\
                        </div>\n\
                        <div class="envelope txt-right pv15">\n\
                        <a href="javascript:{componentId}.cancelAtach();" class="boton">{cancelLabel}</a>|\n\
                        <a id="{componentId}buttonAttach" href="javascript:{componentId}.submit(\'{componentId}Upload\');" class="boton rojo">{buttonCaption}</a>\n\
                        <img id="{componentId}Waiting" src="{waiting}" border="0" style="display:none">\n\
                        <input type="submit" id="{componentId}submitButton" class="boton rojo" value="{buttonCaption}"/ style="display:none">\n\
                        <input type="hidden" name="nameBackup" value="" id="nameBackup"/>\n\
                        <input type="hidden" name="descripcion" value="{descripcion}" id="descripcion"/>\n\
                        <input type="hidden" name="tipo" value="{tipo}" id="tipo"/>\n\
                        <input type="hidden" name="componentId" value="{componentId}" id="componentId"/>\n\
                        <input type="hidden" name="savePath" value="{savePath}" id="savePath"/>\n\
                        </div>   \n\
                    </form>'
       }
     //Procesa opciones;
     if (options!=null){
         if (dojo.exists("windowTitle",options)){
             this.setWindowTitle(options.windowTitle);
         }
         if (dojo.exists("id",options)){
             this.setId(options.id);
         }
         if (dojo.exists("windowDirections",options)){
             this.setWindowDirections(options.windowDirections);
         }
         if (dojo.exists("buttonLabel",options)){
             this.setButtonLabel(options.buttonLabel);
         }
        if (dojo.exists("cancelLabel",options)){
             this.setCancelLabel(options.cancelLabel);
         }
        if (dojo.exists("savePath",options)){
             this.setSavePath(options.savePath);
         }
        if (dojo.exists("hookOnSuccess",options)){
             this.setHookOnSuccess(options.hookOnSuccess);
         }
        if (dojo.exists("hookOnFailure",options)){
             this.setHookOnFailure(options.hookOnFailure);
         }
        if (dojo.exists("selectFileMessage",options)){
             this.setSelectFileMessage(options.selectFileMessage);
         } 
        if (dojo.exists("validations",options)){
             this.setValidations(options.validations);
         } 
     }

    this.validate = function() {
        
    }
    this.cancelAtach= function(){
        //hide imagen de waiting
        var w=dojo.byId(this.getId()+"Waiting");
        if (w!=null){dojo.style(w,"display","none");}
        var b=dojo.byId(this.getId()+"buttonAttach");
        if (b!=null){dojo.style(b,"display","");} 
        
        if (this.getWindow()!=null){
            this.getWindow().hide();
        }
    };
    
    this.submit = function(idForma){
        if(this.validFile() ) {
            dojo.byId(this.getId()+"submitButton").click();
        }
    }
    this.atachFile = function(){
        this.arguments=arguments;

        //log("Entrando a atachFile");
        var containerId=this.getId()+"DialogWindowUpload";
                     
            var container = dojo.create("div",{id:containerId,style:{"display":"none"}},dojo.body(),"last");

            if (!dojo.exists("id",file)){
                dojo.setObject("id","-1",file);
            } 
            if (!dojo.exists("tipo",file)){
                dojo.setObject("tipo","1",file);
            } 
            if (!dojo.exists("descripcion",file)){
                dojo.setObject("descripcion","",file);
            }
            var imagePath=dojo.moduleUrl("bnext", "images/waiting.gif");
            dojo.setObject("waiting",imagePath.toString(),file);
            dojo.setObject("windowDirections",this.getWindowDirections(),file);
            dojo.setObject("buttonLabel",this.getButtonLabel(),file);
            dojo.setObject("buttonCaption",this.getButtonCaption(),file);
            dojo.setObject("componentId",this.getId(),file);
            dojo.setObject("cancelLabel",this.getCancelLabel(),file);
            dojo.setObject("savePath", this.getSavePath(),file);
            var myContenido = dojo.replace(this.fileForm.html,file);
            container.innerHTML=myContenido;
       if (dijit.byId(containerId)==null){
            var ventana = new dijit.Dialog({title:this.getWindowTitle()},containerId);
            this.window=ventana;

            var miForma = dijit.byId(this.getId()+"Upload");
            log("conectando evento onSubmit")
            dojo.connect(miForma, "onSubmit", function(event) {
                //Stop the submit event since we want to control form submission.
                event.preventDefault();
                event.stopPropagation();
                this.id = event.target.componentId.value;
                eval (this.id+".sendForm('"+this.id+"Upload"+"','Upload.fileUploader')");

            });
            log("mostrando ventana");
       }
       this.getWindow().show();
    }      
    this.validFile = function() {
         //validaciones
         log('this.getValidations(): '+dojo.toJson(this.getValidations()));
         if(this.getFileName() == null) {
             this.getDialog('Seleccione un archivo valido');
             return false;
         }
         if(this.getValidations() != null) {
             var ok = false;
             var errMsg = null;
             var fileTypes = '';
             //tipo de archivo
             if(dojo.exists('fileType', this.getValidations())) {
                 for(var i=0;i<this.getValidations().fileType.length;i++) {
                     fileTypes = (i==0 ? this.getValidations().fileType[i] : ', '+this.getValidations().fileType[i]);
                     if(this.getMimeType().toString().toUpperCase().indexOf(this.getValidations().fileType[i].toString().toUpperCase()) != -1) {
                         ok = true;
                         break;
                     }
                 }
             } else {
                 ok = true;
             }
             log('fileTypes: '+fileTypes+', this.getMimeType(): '+this.getMimeType()+', ok:'+ok);
             if(!ok) {
                 errMsg = this.messages.onlyFilesOfType + fileTypes.toString().toUpperCase()+'"';
             }
             ok = true;
             if(dojo.exists('fileSize', this.getValidations())) {
                 if(parseInt(this.getFileSize(),10) > parseInt(this.getValidations().fileSize,10)) {
                     ok = false;
                 }
             }
             if(!ok && errMsg != null) {
                 errMsg += this.messages.andFileSize+this.tools.bytesToSize(this.getValidations().fileSize)+'.';
             } else if(!ok) {
                 errMsg = this.messages.fileSize+this.tools.bytesToSize(this.getValidations().fileSize)+'.';
             }
             return !this.invalidFile(errMsg);
         }   
         return true;
    }
    this.setFileAttributes = function(file) {
         //alert(file.value.substring(file.value.lastIndexOf("\\") == -1 ? 0 : (file.value.lastIndexOf("\\")+1)))
         this.setFileName((file.value.substring(file.value.lastIndexOf("\\") == -1 ? 0 : (file.value.lastIndexOf("\\")+1))));
         if(file.files == null) {
             this.setValidations(null);
             return;
         }
         var fileAttributes = file.files[0];
         log('fileAttributes: '+dojo.objectToQuery(fileAttributes));
         this.setPeso(this.tools.bytesToSize(fileAttributes.size));
         this.setMimeType(fileAttributes.type);
         this.setFileSize(fileAttributes.size)
         this.validFile();
    }
    //regresa true si el FILE es invalido
    this.invalidFile = function(msg) {
        if(msg == null) {
            dojo.byId(this.id+'submitButton').disabled = false;
            return false;
        }
        this.getDialog(msg);
        dojo.byId(this.id+'submitButton').disabled = true;
        return true;
    }
    this.getDialog = function(msg) {
        var path= dojo.moduleUrl("bnext", "images/icon_fracaso.png");               
        this.dialogWindow= new dijit.Dialog({            
            title:this.messages.weAreSorry,
            style:"width: 360px",
            content:"<div><table width='100%'><tr><td width='30px'><img src='"+path.toString()+"' border='0'></td>\n\
                     <td>"+msg+"</td>\n\
                     </tr></table>\n\
                    <div class='envelope sitesize border_b_light_gris mv10'></div>\n\
                    <div class='envelope txt-right pv10'>\n\
                    <a href='javascript:"+this.id+".closeDialog();' class='boton rojo'>"+this.messages.ok+"</a>\n\
                    </div>\n\
                    </div>"
        });
        this.showDialog();
    }
    this.closeDialog = function() {
        if (this.dialogWindow!=null){
            this.dialogWindow.hide();
        }
    }
    this.showDialog = function(){
        if (this.dialogWindow!=null){
            this.dialogWindow.show();
        }
    }    
    this.sendForm = function(formId,formAction) {
        /*if (typeof(timeoutID)!="undefined"){
            window.clearTimeout(timeoutID);
        }*/
        this.filePath=null;
        var nombre = dojo.byId(formId).upload.value;
        if (nombre==null || nombre=="undefined"){
            dojo.byId(this.getId()+"Message").innerHTML=this.getSelectFileMessage();
            return;
        }
        nombre = dojox.html.entities.encode(nombre);
        dojo.byId(formId).nameBackup.value = nombre;
        //imagen de waiting
        var w=dojo.byId(this.getId()+"Waiting");
        if (w!=null){dojo.style(w,"display","");}
        var b=dojo.byId(this.getId()+"buttonAttach");
        if (b!=null){dojo.style(b,"display","none");}        
        dojo.io.iframe.send({
            url: formAction,
            method: "post",
            handleAs: "text",
            form: dojo.byId(formId),
            handle: function(data){ 
                log("informacion recibida");
                var info = dojo.fromJson(data);
                var status = info.operationStatus;
                var result = info.result;
                var error = info.error;
                var w=dojo.byId(result.componentId+"Waiting");
                if (w!=null){dojo.style(w,"display","none");}
                var b=dojo.byId(result.componentId+"buttonAttach");
                if (b!=null){dojo.style(b,"display","");}                  
                if (status==1){
                    var success= eval(result.componentId+".getSuccessMessage()");
                    exitoMsg(success);
                    eval(result.componentId+".setFileName(\""+result.name+"\")");
                    eval(result.componentId+".setUrl(\""+result.url+"\")");
                    eval(result.componentId+".setFilePath(\""+result.filePath+"\")");
                    eval(result.componentId+".setPeso(\""+result.size+"\")");
                    eval(result.componentId+".setMimeType(\""+result.mimetype+"\")");
                    eval(result.componentId+".cancelAtach()");
                    var hookOnSuccess = eval(result.componentId+".getHookOnSuccess()");
                    if (hookOnSuccess!=null){
                        eval(hookOnSuccess);
                    }
                }else{
                    var failure= eval(result.componentId+".getFailureMessage()");
                    FracasoMsg(failure+":"+error,1);
                    var hookOnFailure = eval(result.componentId+".getHookOnFailure()");
                    if (hookOnFailure!=null){
                        eval(hookOnFailure);
                    }                      
                }
                //location.replace('#');
            },
            error:  function (error) {
                log(error);
                FracasoMsg(error,1);              
            }
        });

    }
 }
 
