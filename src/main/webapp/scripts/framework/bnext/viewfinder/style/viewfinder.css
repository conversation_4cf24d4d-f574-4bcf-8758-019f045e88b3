/* 
    Created on : 4/04/2016, 11:02:56 AM
    Author     : <PERSON>
*/
.viewfinder .documentHistory {        
    left: 0;
    width: 100%;
    padding-left: 14.125rem;
}
.viewfinder .documentHistoryClean {
    left: 100%;
    width: 14.063rem;
}
.viewfinder .documentHistoryClean span {
    cursor: pointer;
    padding: 0px 1rem;
}
.viewfinder .documentHistoryClean a {
    cursor: pointer;
    text-decoration: underline;
    color: #212121;
    font-size: 9pt;
}
.viewfinder .documentHistoryItem {
    border-top: 1px solid gray;
    margin-left: -14.063rem;
    position: absolute;
    float: left;
    top: 100%;
    margin-top: -2.25rem;
    text-align: right;
    overflow: hidden;
    height: 2.25rem;
    background-color: #E2E0E1;
    line-height: 2.25rem;
}
.viewfinder .documentHistory .historyNode span {
    display: inline-block;
    width: 9.188rem;
    overflow: hidden;
    word-break: break-all;
    word-wrap: break-word;
    height: 1.063rem;
}
.viewfinder .documentHistory .historyNode {
    cursor: pointer;
    width: 8.125rem;
    border-radius: 0.188rem;
    display: inline-block;
    line-height: 1.625rem;
    text-align: center;
    font-size: 8pt;
}
.viewfinder .docRef {
    min-width: 12.063rem;
    vertical-align: top;
    display: inline-block;
    white-space: nowrap;
    padding-bottom: 1rem;
    text-align: justify;
}
.viewfinder .docRef span {
    color: rgba(0,0,0,0.74);
    margin: 0 0.313rem 0 0.313rem;
    line-height: 1.625rem;
}
.viewfinder .documentHistory .historyNode img {
    padding-top: 1px;
    padding-right: 0.313rem;
    padding-left: 0.313rem;
}
.viewfinder .menu .grid-component-container {
    font-size: 0.75rem;
    width: 100%;
}
.viewfinder .dijitInline {
    font-size: 0.75rem;
}
.viewfinder .grid-component-container .searchField .dojoxCheckedMultiSelectWrapper {
    margin-top: 0px!important;
    width: 100%!important;
    margin-right: -7.938rem!important;
}
.viewfinder {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 3;
    top: 0px;
    left: 0px;
    padding-bottom: 2.25rem;
}
.viewfinder .menu {
    overflow-x: auto;
    padding-top: 1px;
    padding-bottom: 1px;
    padding-left: 0.125rem;
    width: 26.563rem;
    height: 100%;
    display: inline-block;
    float: left;
    background-color: white;
}
.viewfinder .viewerContainer {
    background-color: gray;
    border: 1px solid black;
    width: calc(100% - 26.563rem);
    float: left;
    height: 100%;
}
.viewfinder .viewer {
    width: 100%;
    height: 100%;
    display: inline-block;
    float: left;
    background-color: #6D6D6D;
}
.viewfinder .documentDetail {
    width: calc(100% - 26.563rem);
    background: white;
    height: 11.25rem;
    display: inline-block;
    float: left;
    padding: 0.313rem 0px;
}
.viewfinder .menu div.grid-options{
    display:none;
}
.viewfinder .menuButtons {
    position: absolute;
    left: 27.188rem;
    top: 0.25rem;
    margin: 0.125rem 0.125rem 0px 0.125rem;
}
.viewfinder .menuButtons li {
    display: inline-block;
    float: left;
}
.bnext .viewfinder .searchFields .searchField label.gridButtonLabel.searchLabel .Button {
    width: 100%!important;
    height: 2.875rem;
    margin: 0!important;
    text-align: left;
    vertical-align: middle;
    margin-left: 1.563rem;
    padding-left: 0.75rem!important;
    box-shadow: none;
    font-weight: 600!important;
}
.viewfinder .search-even {
    background-color: white;
    border-radius: 0.313rem;
}
.viewfinder .searchField .catalog-multiple-data-info div {
    line-height: 0.938rem;
}
.viewfinder .eyesClassIdentifier {
    display: none;
}
.viewfinder .message_info {
    width: 100%;
}
.viewfinder li.searchField {
    width: 100%;
    display: block;
    margin: 0px;
    height: 3.875rem;
}
.viewfinder li.searchField .dijitButtonNode {
    height: 100%;
}
.viewfinder .grid-component-container .css-field-dyntable .catalog-multiple-data-info,
.viewfinder .grid-component-container .searchField > .dijitReset,
.viewfinder .grid-component-container .searchFields .searchField .dijitInline.dijitTextBox,
.viewfinder .grid-component-container li.searchField .dijitInline > .dijitTextBox,
.viewfinder .grid-component-container li.searchField .dijitInline > .dojoxCheckedMultiSelect {
    width: 100%!important;
}
.viewfinder .searchField > .dijitInline {
    clear: none;
    display: inline-block;
    float: left;
    border-right: 0.688rem;
    padding-right: 1px;
    width: 100%;
}
.viewfinder .search {
    float: left;
    cursor: default;
}
.viewfinder .documentDetailNodeContainer {
    overflow: auto;
    height: 100%;
}
.viewfinder .documentDetailNodeContainer .grid-container {
    padding-top: 0;
    max-width: 100%;
}
.viewfinder .detailContainer > div {
    display: inline-block;
    min-height: 1.563rem;
    float: left;
}
.viewfinder .detailContainer select {
    color: black;
}
.viewfinder .documentDetailNodeContainer .detailContainer label.label {
    width:9.375rem;
}
.viewfinder .documentDetail li label.label {
    width: 6.875rem;
    min-height: 1.125rem;
    word-wrap: break-word;
    overflow: hidden;
    line-height: 0.688rem;
}
.viewfinder .documentDetail detailContainer {
    padding-top: 0.188rem;
    font-size: 8pt;
}
.viewfinder .fullDetailBtn {
    display: none;
    cursor: pointer;
    background-image: url('../images/documentDetailBtn.png');
    background-color: lightgray;
    line-height: 1.5rem;
    border: 1px solid gray;
    background-size: 1.5rem;
    background-repeat: no-repeat;
}
.viewfinder .fullDetailBtn:hover {
    background-color: #DDD;
}
.viewfinder select.information {
    height: 1.5rem;
    font-size: 8pt;
}
.viewfinder b.information, .viewfinder input.information {
    width: 14.25rem;
    height: 1.375rem;
    overflow: hidden;
}

.gridExpandable .grid-component-container tbody tr:first-child,
.gridExpandable .grid-component-container tbody tr:first-child:hover {
    background: #f4f4f4;
    color: #0000008a;
}

.gridExpandable .grid-component-container tbody tr:hover {
    background-color: #f8f8f8;
    color: #000;
}

.gridExpandable .grid-component-container tbody th{
    border-right: #00000014 solid 1px;
}
.viewfinder .dijitSelect .dijitButtonContents, 
.viewfinder .dijitSelect, 
.viewfinder .dojoxCheckedMultiSelect,
.viewfinder .dojoxCheckedMultiSelectWrapper,
.viewfinder .catalog-multiple-data-info,
.viewfinder .dijitTextBox {
    width: 15.625rem!important;
}
.viewfinder .catalog-multiple-data-info {
    border-radius: 0px;
    margin: 0px;
    text-align: left;
    min-height: 1.438rem;
}
.viewfinder .menu .dojoxMultiSelectItem {
    float: left;
    clear: both;
}
.viewfinder .dojoxCheckedMultiSelectWrapper {
    margin-top: 0px!important;
    margin-right: 0px!important;
    margin-bottom: 0.25rem!important;
    margin-left: 0px!important;
}
.viewfinder .dojoxCheckedMultiSelect{
    margin-top: 0px!important;
    margin-right: 0px!important;
    margin-bottom: 0px!important;
    margin-left: 0px!important;
}
.viewfinder .documentDetail select[size] {
    min-height: 2.875rem;
}
.viewfinder.fullScreen .documentDetail,
.viewfinder.fullScreen .menu {
    visibility: hidden;
}
.viewfinder.fullScreen .menuButtons {
    left: 0px;
    top: 0.25rem;
}
.viewfinder.fullScreen .viewerContainer {
    width: 100%;
    height: 100%;
    padding-top: 0px;
    margin-top: 0px;
    padding-right: 0px;
    margin-right: 0px;
}
.viewfinder .relatedDocumentTd span {
    height: 1.25rem;
    line-height: 1.25rem;
    overflow:hidden;
    white-space:nowrap;
    text-overflow: ellipsis;
    padding-left: 0.5rem;
}
.viewfinder .relatedDocumentsNode {
    overflow: hidden;
    text-align: center;
    height: 100%;
}

.viewfinder .relatedDocumentsNode div {
    display: inline-flex;
    flex-direction: column;
    padding-bottom: 0.125rem;
    width: 100%;
}

.viewfinder .relatedDocumentsNode .relatedDocumentTd {
    width: 100%;
    justify-content: left;
    text-decoration: none;
    margin-bottom: 1rem;
}

.viewfinder div.menuButtons div.Button.selector.btn_rounded {
    border-radius: 2.25rem;
    box-shadow: none;
    background-color: white !important;
}
.viewfinder div.menuButtons div.Button.selector {
    position: static;
    line-height: 2.25rem;
    min-width: 2.25rem;
    height: 2.25rem;
    float: left;
    padding: 0px 0px;
    margin: 0.125rem 0.125rem 0.125rem 0.125rem;
    -moz-border-radius: 0.188rem;
    -webkit-border-radius: 0.188rem;
    border-radius: 0.188rem;
    cursor: pointer;
    -moz-transition-property: opacity;
    -moz-transition-duration: 250ms;
    -moz-transition-delay: 250ms;
    -webkit-transition-property: opacity;
    -webkit-transition-duration: 250ms;
    -webkit-transition-delay: 250ms;
    -o-transition-property: opacity;
    -o-transition-duration: 250ms;
    -o-transition-delay: 250ms;
    transition-property: opacity;
    transition-duration: 250ms;
    transition-delay: 250ms;
}
.viewfinder div.menuButtons div.Button.btn_rounded a.material-icons {
    background-color: inherit!important;
    border-radius: 1.25rem;
}
.viewfinder div.menuButtons a.material-icons {
    line-height: 1.5;
}
.viewfinder div.Button.btn_rounded:hover a.material-icons,
.viewfinder div.Button.btn_rounded:hover {
    background-color: #e0e0e0!important;
}

.viewfinder .grid-container.grid-floating-active .field-display {
    padding-bottom: 0rem;
}

@media print {
    body * {
        visibility: hidden;
    }
    .viewerContainer, .viewerContainer * {
        visibility: visible;
    }
    .viewerContainer {
        position: absolute;
        z-index: 1;
    }
}
.viewfinder .menu .grid-component-container .search_button_holder{
    margin-left: 0px!important;
}
.viewfinder .menu .grid-component-container .search_button_holder .button {
    margin-left: 0.125rem;
    padding: 0 0.813rem!important;
}
/* IE fixes */
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .isTRIDENT .viewfinder {
        position: fixed;
        transform: translateZ(0);
        -moz-transform: translatez(0);
        -ms-transform: translatez(0);
        -o-transform: translatez(0);
        -webkit-transform: translateZ(0);
        -webkit-font-smoothing: antialiased;
    }
}
.viewfinder .searchFields {
    max-width: 100%;
    box-sizing: border-box;
    margin-left: 0;
    margin-bottom: 0;
}
.viewfinder table tbody td.css_view ,
.grid-component-container thead th.css_view,
.grid-component-container thead th.css_version{
    min-width: 1.563rem;
}
.viewfinder table tbody th, 
.viewfinder table tbody td {
    padding: 0;
    text-align: center;
}
.viewfinder table {
    width: auto;
}

.viewfinder .search-filter-chips {
    padding-left: 0;
}

.viewfinder .ChipContainer {
    margin-top: 0.313rem;
}

.viewfinder .grid-component-container .search-style{
    box-shadow: none;
}

.viewfinder .grid-component-container .search-style .searchOpen-style{
    padding: 0px 0px;
    cursor: default;
}

/*pagination*/
.viewfinder .menu table.grid-component-container,
.viewfinder .menu div.fancy-scroll.grid-component-container{
    margin-bottom: 0px;
}

.viewfinder .menu .dataTables_paginate {
    margin: 0px;
    padding-left: 0.313rem;
    padding-right: 0.313rem;
    height: 5.438rem;
}

.menu .dataTables_paginate .pagination_left {
    margin: 0px;
    width: 100%;
    padding: 0px;    
}

.menu .dataTables_paginate .pagination_right {
    margin: 0.313rem 0px 0px 0px;
    width: 100%;
}

.viewfinder .menu .dataTables_paginate span.first.paginate_button,
.viewfinder .menu .dataTables_paginate span.last.paginate_button,
.viewfinder .menu .dataTables_paginate span.next.paginate_button,
.viewfinder .menu .dataTables_paginate span.previous.paginate_button {
    margin: 0.375rem auto;
}

.viewfinder .menu .dataTables_paginate span.example_paginate{
    margin: 0px auto;
}

.viewfinder .menu .dataTables_paginate .spanTotalPages {
    width: 100%;
}
.dijitDialog div.gridExpandable table.display.finder-history-table {
    display: initial;
}
.dijitDialog .dijitDialogPaneContent .gridExpandable.grid-component-container {
    padding-bottom: 0px; 
}

.finder-history-table .view-header,
.finder-history-table .view-cell{
    min-width: 2rem;
    line-height: 0.5rem;
    text-align: center;
}
.finder-history-table .title-cell,
.finder-history-table .title-header{
    width: 100%;
}
.viewfinder .grid-update-container {
    display:none;
}
.dijitPopup.dojoxCheckedMultiSelectPopup .dojoxCheckedMultiSelect .dojoxCheckedMultiSelectWrapper.fancy-scroll {
    min-width: 24.063rem;
}

.viewfinder .tabsContainer {
    width: 100%;
    height: 100%;
}

.viewfinder .tabsContainer .dijitTab {
    padding: 0.125rem 0.313rem 0.125rem 0.313rem;
}

.viewfinder .dijitTabContainer.dijitTabContainerTop .dijitTabListContainer-top.dijitTabController {
    box-shadow: -0.375rem 0px 0.313rem 0.125rem rgba(0, 0, 0, 0.2);
}

.bnext .viewfinder .dijitTabContainerTop-dijitContentPane {
    padding-top: 1px;
    padding-right: 1px;
    padding-bottom: 1px;
    padding-left: 1px;
}
.viewfinder .dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter,
.viewfinder .tabsContainer .dijitTabListContainer-top.dijitLayoutContainer,
.viewfinder .tabsContainer .dijitTabListContainer-top.dijitLayoutContainer .nowrapTabStrip.dijitTabContainerTop-tabs {
    position: initial!important;
    width: 25rem !important;
    display: inline-flex;
}
.viewfinder .tabsContainer .dijitTabListContainer-top.dijitLayoutContainer .dijitButtonContents.tabStripButton {
    background: none;
    border: none;
    display: block;
    right: 0px;
    margin-left: -2.375rem!important;
    width: 5.25rem!important;
    z-index: 13;
    padding: 0px;
}
.viewfinder .dijitTabInner.dijitTabContent.dijitTab,
.viewfinder .dijitTabContainerTop-tabs,
.viewfinder .dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter {
    height: 3.188rem!important;
}
.viewfinder .tabsContainer .dijitTab.dijitTabChecked .tabLabel {
    display: block;
}
.viewfinder .tabsContainer .dijitTab .tabLabel {
    min-height: 0.75rem;
    display: inline-block;
    font-family: 'TypoPRO Titillium Text', sans-serif;
    font-size: 0.875rem;
    letter-spacing: 0.046875rem;
    text-transform: uppercase;
    font-weight: 600;
    padding: 0.938rem;
    position: static!important;
}

.viewfinder .tabsContainer .dijitTabInner.dijitTabContent.dijitTab {
   min-width: 8.3rem;
   text-align: center;
}

.viewfinder .tabsContainer .dijitTabContainer .tabStripButton,
.viewfinder .tabsContainer .tabStrip-disabled .tabStripButton {
    display: none !important;
}
.viewfinder .tabsContainer .dijitTabContainerTop-tabs .dijitTab {
    background: none;
    margin-right: 0px;
    border: none;
}
.viewfinder .tabsContainer .dijitTabContainerTop-container {
    border: none !important;
}
.viewfinder .tabsContainer .dijitTabContainer .dijitTabCheckedHover {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: 0.125rem solid #0f037c;
}
.viewfinder .emptyRelatedDocs {
    text-align: center;
    margin-top: 2rem;
}
.viewfinder .emptyRelatedDocs .emptyIcon {
    font-size: 4rem;
}
.viewfinder .emptyRelatedDocs .emptyTitle {
    font-size: 1.2rem;
}
.viewfinder .emptyRelatedDocs .emptySubtitle {
    font-size: 0.9rem;
}

.viewfinder .menu div.gridInfo.info {
    color: black !important;
    background: none;
    border: none;
    font-size: 1rem;
}

.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li {
    display: flex;
    flex-flow: column;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone label {
   font-size: 0.75rem;
   transform: translateY(-0.25rem);
   width:9.375rem;
   margin-left: 3.5rem;
   text-align: justify;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone input {
    background: none;
    border: none;
    font-weight: 700;
    margin-left: 3.5rem;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select {
    background: transparent;
    overflow: hidden;
    margin-left: 3.5rem;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select[type="multiple"],
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select[type="multiple"]:hover {
    border: none;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select:disabled {
    background-color: transparent;
    opacity: 1;
    font-weight: 800;
    pointer-events: none;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select[size] option,
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select[size] option:checked {
    font-weight: 700;
    background-color: transparent;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select:-internal-list-box:disabled option:checked {
    background-color: transparent;
}

.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select[disabled] > option {
    color: #000;
}
.viewfinder .documentDetailNodeContainer .main-dynfield-container.ulDynamicFields li.cssEntityIdnone select:hover {
    box-shadow: none;
}
.viewfinder .grid-container.grid-floating-active .field-display > div.dijitTextBox .dijitReset.dijitInputField.dijitInputContainer > input.dijitInputInner {
    font-size: 1rem;
}
/* hasta responsividad medium */
@media print, screen and (max-width: 64em) {
    .bnext-viewfinder.screen-mode-normal .documentHistoryItem,
    .bnext-viewfinder.screen-mode-full #exitVisorBtn, 
    .bnext-viewfinder.screen-mode-full #closeVisorBtn,
    .bnext-viewfinder.screen-mode-normal .viewerContainer {
        display: none;
    }

    .bnext-viewfinder.screen-mode-normal .menuButtons {    
        left: auto;
        z-index: 1;
        right: 0px;
        position: fixed;
    }
    .bnext-viewfinder.screen-mode-normal .viewfinder {
        width: 100%;
        padding-bottom: 0.125rem;
    }
    .bnext-viewfinder.screen-mode-normal .viewfinder,
    .bnext-viewfinder.screen-mode-normal .viewfinder .tabsContainer .dijitTabListContainer-top.dijitLayoutContainer .nowrapTabStrip.dijitTabContainerTop-tabs,
    .bnext-viewfinder.screen-mode-normal .viewfinder .dijitTabListWrapper.dijitTabContainerTopNone.dijitAlignCenter,
    .bnext-viewfinder.screen-mode-normal .viewfinder .menu,
    .bnext-viewfinder.screen-mode-normal .dijitPopup,
    .bnext-viewfinder.screen-mode-normal .dijitPopup .dijitLeft,
    .bnext-viewfinder.screen-mode-normal .viewfinder .search_button_holder,
    .bnext-viewfinder.screen-mode-normal .dijitTabListContainer-top.dijitLayoutContainer {
        max-width: 100%;
        width: 100%!important;
    }
    .bnext-viewfinder.screen-mode-normal .button-container {
        width: calc(100% - 0.5rem);
    }
    .bnext-viewfinder.screen-mode-normal .button-container .button {
        width: 100%!important;
    }
    .bnext-viewfinder.screen-mode-normal .searchFields .searchField label.searchLabel.gridButtonLabel,
    .bnext-viewfinder.screen-mode-normal .viewfinder .searchField > .dijitInline {
        width: calc(100% - 0.625rem)!important;
    }
    .bnext-viewfinder.screen-mode-normal .dijitPopup.dojoxCheckedMultiSelectPopup .dojoxCheckedMultiSelect .dojoxCheckedMultiSelectWrapper.fancy-scroll {
        min-width: calc(100% - 0.875rem)!important;
        max-width: calc(100% - 0.875rem);
        padding: 0!important;
        margin: 0!important;
        width: calc(100% - 0.875rem);
    }
    .viewfinder.fullScreen .menuButtons .downloadButton{
        display: none;
    }
    .viewfinder.fullScreen .menuButtons,
    .bnext-viewfinder.screen-mode-normal .menuButtons {
        max-width: 36vw;
        max-height: 2.5rem;
        overflow: hidden;
    }
    .viewfinder.fullScreen .menuButtons .btn_rounded {
        border: solid 1px;
    }
}

/* Medium*/
@media screen and (max-width: 39.9375em) {
    .viewfinder .searchDialog:not(.displayNone) {
        top: 3.125rem;
        max-width: 100%;
    }
    .viewfinder .searchDialog .searchFields h5 {
        padding-top: 1rem;
    }
    .bnext-viewfinder #messages_layout {
        padding-right: 0rem;
        padding-top: 2rem;
    }
    .bnext-viewfinder #messages_layout div .material-icons {
        top: 0.87rem;
        transform: initial;
    }
    .viewfinder .searchFields .close-filter-button {
        padding-top: 1rem !important;
    }
}

/* hasta responsividad small */
@media print, screen and (max-width: 26.5625em) {
    .viewfinder.fullScreen .menuButtons,
    .bnext-viewfinder.screen-mode-normal .menuButtons {
        max-width: 32vw;
    }
    .viewfinder .searchDialog .search_button_holder .button-container .button {
        width: calc(100% - 3.5rem) !important;
    }
    .viewfinder .menu .grid-component-container .search_button_holder {
        margin-left: 1.375rem !important;
    }
}

@media print, screen and (max-width: 21.688rem) {
    .bnext-viewfinder.screen-mode-normal .menuButtons .Button.selector.btn_rounded {
        float: right;
    }
}