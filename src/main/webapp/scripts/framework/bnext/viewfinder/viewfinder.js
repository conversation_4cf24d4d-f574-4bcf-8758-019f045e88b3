/* global require */

define([
    'core', 'dojo/text!bnext/viewfinder/template.html',
    'dojo/dom-attr', 'dojo/_base/lang', 'dojo/_base/array',
    'dojo/_base/declare', 'dojo/_base/query', 'bnext/callMethod',
    'bnext/gridIcons','bnext/FieldListUtil',
    'bnext/DynamicFieldUtil',
    'dojo/Deferred',
    'dojo/dom',
    'dojo/on',
    'dojo/dom-construct',
    'dojo/promise/all',
    'dijit/_TemplatedMixin',
    'dijit/_WidgetBase', 
    'dojo/Evented',
    'dojo/dom-class',
    'bnext/viewfinder/viewfinder-util',
    'bnext/gridComponentUtil',
    'bnext/GridFactory', 
    'bnext/i18n!bnext/viewfinder/nls/viewfinder',
    'bnext/extensionIcons',
    'bnext/_base/media-queries',
    'dijit/layout/TabContainer',
    'dijit/layout/ContentPane',
    'bnext/survey/_util/jsFailureExposure!', 
    'dojo/domReady!',
    'xstyle/css!bnext/viewfinder/style/viewfinder.css'
], 
function (core, template, domAttr, lang, array, declare, query, callMethod, gridIcons, FieldListUtil, DynamicFieldUtil, Deferred,
    dom, on, domConstruct, all, _TM, _WB, Evented, domClass,
    viewfinderUtil,
    gcUtil, GridFactory, i18n, extensionIcons,
    MediaQueries,
    TabContainer,
    ContentPane
) {
    var w = window, cType = gcUtil.cType;
    function setup(viewfinderConstruct) {
        try {
             if (w.top.viewfinder) {
                 return w.top.viewfinder;
             } else {
                var vf = new viewfinderConstruct();
                w.top.viewfinder = vf;
                if (w.wrapper) {
                    w.wrapper.style.display = 'block';
                }
                return vf;
            }
        } catch (e) {
            console.error(e);
            return null;
        }
    }

    function onExitViewFinder(viewFinderViewer) {
        if (typeof Event === 'function') {
            viewFinderViewer.dispatchEvent(new Event('onExitViewFinder'));
        } else {
            const evt = document.createEvent('HTMLEvents');
            evt.initEvent('onExitViewFinder');
            viewFinderViewer.dispatchEvent(evt);
        }
    }
    //se declara widget
    var viewfinder = declare([_WB, _TM, Evented], {
        id: 'viewFinder',
        showFullHistory: null,
        cleanLocalHistory: null,
        history: [],
        templateString: template,
        i18n: i18n,
        viewerNode: null,
        menuNode: null,
        tabContainer: null,
        tabSearch: null,
        tabDetail: null,
        tabRelated: null,
        tabRelatedAdded: false,
        gridNode: null,
        relatedDocumentsNode: null,
        grid: null,
        mainNode: null,
        documentDetailNode: null,
        navigatePendingsAtBack: null,
        closeVisorBtn: null,
        exitVisorBtn: null,
        fullScreenBtn: null,
        exitFullScreenBtns: null,
        exitFullScreenBtn:null,
        fieldUtil: null,
        initialized: false,
        skipInitialize: true,
        initializing: false,
        createdDef: new Deferred(),
        shown: false,
        initializeOptionsDropdown: false,
        created: function() {
            return this.createdDef.promise;
        },
        postCreate: function() {
            this.placeAt(w.document.body);
            this.inherited(arguments);
            this.id = 'vf_' + new Date().getTime();
            this.gridNode = domConstruct.create('table', {
                id: 'g' + this.id
            }, this.tabSearchDom);
            this.fieldUtil = new FieldListUtil('view-finder', this.documentDetailNode, this.i18n, true);
            if (!this.skipInitialize) {
                this.initialize();
            }
        },
        failure: function (err) {
            core.hideLoader();
            return core.error(err);
        },
        selectDocument: function (searchTableDom) {
            searchTableDom = searchTableDom || query('table.grid-component-container', this.menuNode)[0];
            query('tr.selected-content', searchTableDom).forEach(function(tr) {
                domClass.remove(tr, 'selected-content');
            });
            query('div.historyNode', this.historyNode).forEach(function(div) {
                domClass.remove(div, 'selected-content');
            });
            this.tabContainer.selectChild(this.tabDetail);
        },
        setScreenMode: function(mode, mainNode) {
            var _self = this;
            switch(mode.toLowerCase()) {
                case 'full':
                    domClass.add((mainNode && mainNode.mainNode ? mainNode.mainNode : this.mainNode), 'fullScreen');
                    _self.fullScreenBtn.style.display = 'none';
                    _self.exitVisorBtn.style.display = '';
                    _self.exitFullScreenBtns.style.display = '';
                    domClass.add(document.body, 'screen-mode-full');
                    domClass.remove(document.body, 'screen-mode-normal');
                    domClass.add((mainNode && mainNode.menuNode ? mainNode.menuNode : this.menuNode), 'displayNone');
                    _self.tabContainer.resize();
                    _self.initializeOptionsDropdown = true;
                    _self.emit("enterFullScreenExecuted", {
                        'bubbles': false,
                        'cancelable': true
                    });
                    break;
                case 'normal':
                    domClass.remove((mainNode && mainNode.mainNode ? mainNode.mainNode : this.mainNode), 'fullScreen');
                    _self.exitFullScreenBtns.style.display = 'none';
                    _self.exitVisorBtn.style.display = 'none';
                    _self.fullScreenBtn.style.display = '';
                    domClass.add(document.body, 'screen-mode-normal');
                    domClass.remove(document.body, 'screen-mode-full');
                    domClass.remove((mainNode && mainNode.menuNode ? mainNode.menuNode : this.menuNode), 'displayNone');
                    _self.tabContainer.resize();
                    _self.emit("exitFullScreenExecuted", {
                        'bubbles': false,
                        'cancelable': true
                    });
                    break;
            }
        },
        initalizeTabs: function () {
            this.tabContainer = new TabContainer({
                id: this.id + '_tabContainer',
                'class': 'tabsContainer'
            }, this.tabContainerDom);

            this.tabSearch = new ContentPane({
                id: this.id + '_searchTab',
                title: i18n.searchTab,
                content: this.tabSearchDom,
                selected: true,
                'class': 'searchTab fancy-scroll'
            });
            this.tabContainer.addChild(this.tabSearch);

            this.tabDetail = new ContentPane({
                id: this.id + '_detailTab',
                title: i18n.detailTab,
                content: this.tabDetailDom,
                'class': 'detailTab fancy-scroll'
            });
            this.tabContainer.addChild(this.tabDetail);

            this.tabRelated = new ContentPane({
                id: this.id + '_relatedTab',
                title: i18n.relatedTab,
                content: this.tabRelatedDom,
                'class': 'relatedTab fancy-scroll'
            });
            if (this.isModeDesktop()) {
                this.tabContainer.addChild(this.tabRelated);
                this.tabRelatedAdded = true;
            }

            this.tabContainer.startup();

            this.tabContainer.selectChild(this.tabSearch);
        },
        isModeDesktop: function () {
            return MediaQueries.isScreenLarge();
        },
        onResizeWindow: function() {
            if (this.isModeDesktop()) {
                if (!this.tabRelatedAdded) {
                    this.tabContainer.addChild(this.tabRelated);
                    this.tabRelatedAdded = true;
                }
            } else {       
                if (this.tabRelatedAdded) {
                    this.tabContainer.removeChild(this.tabRelated);
                    this.tabRelatedAdded = false;
                }
            }
            this.tabContainer.resize();
        },
        close: function() {
            var _self = this;
            onExitViewFinder(this.viewerNode.contentWindow);
            if (core.getUrlVars()["simpleview"] === "true") {
                window.top.close();
            } else if (_self.navigatePendingsAtBack) {
                require(['bnext/angularNavigator'], function (angularNavigator) {
                    angularNavigator.navigate('pendings');
                    _self.exitAngularFullScreen();
                });                            
            } else {
                _self.hide();
            }
        },
        logout: function() {
            core.dialog(i18n.ValidateConfirmExit, core.i18n.yes, core.i18n.no, i18n.ValidateExit).then(function () {
                require(['bnext/angularElements'], function (angularElements) {
                    angularElements.login().then(function(element) {
                        var loginDialog = element.domNode;
                        loginDialog.isLogOff = true;
                    });
                });
            });
        },
        enterFullScreen() {
            var _self = this;
            _self.setScreenMode('full', _self);
        },
        exitFullScreen() {
            var _self = this;
            _self.setScreenMode('normal', _self);  
        },
        initialize: function(skipShow) {
            this.initializing = true;
            var _self = this;
            _self.initalizeTabs();
            //inicia visor
            _self.view(-1, -1, -1, {
                init: true
            });
            var columns = [], gridSize = dom.byId('gridSize') ? dom.byId('gridSize').value || 10 : 10, viewIcon = gridIcons.preview;
            gcUtil.column(columns)
                .push('view', i18n.colNameVer, cType.RenderCell(function (row, nodeTd, trDom, tableDom) {
                    var fileInfo = extensionIcons.getExtensionInfo(row.fileContent, row);
                    var fileContent = row.fileContent;
                    var ext = ((fileContent ? fileContent.extension  : '') || '').match(/^.+(\..+)$/);
                    if(ext) {
                        ext = i18n.colNameDescFile + ' ' + ext[1].toUpperCase().substr(1) + ': ';
                    } else {
                        ext = i18n.colNameDescFile + ': ';
                    }
                    var viewAnchor = domConstruct.create('a', {
                        href: 'javascript:void(0)',
                        onclick: function() {
                            var documentId = row.id, fileId = row.fileId, code = row.code;
                            _self.selectDocument(tableDom);
                            _self.view(documentId, -1, fileId, {
                                documentCode: code,
                                fullscreen: !_self.isModeDesktop()
                            });
                        },
                        innerHTML: '<img border=0 src="' + fileInfo.iconSrc + '"/>'
                    });
                    trDom.title = ext + fileInfo.title;
                    return viewAnchor;
                }), null, '17px')
                .push('docType', i18n.colNameTipoDoc, cType.DynamicField({
                    id: 'documentType',
                    serviceName: 'DocumentType.SearchFields.action'
                }), null, '90px', 'none')
                .push('code', i18n.colNameCode, cType.Text(), null, '90px')
                .push('description', i18n.colNameDesc, cType.Text())
                .push('version', i18n.colNameVersion, cType.Text('description', true), null, '15px')
                .searchField('department.description', i18n.colNameDepartamento, cType.SelectEntityMultiple({
                    id: 'department',
                    serviceName: 'Documents.BusinessUnitDepartment.action',
                    methodName: 'getDocumentSearchFieldValues'
                }))
            ;
            //inicia busqueda
            GridFactory.create({
                size: gridSize,
                container: this.gridNode.id,
                serviceStore: '../DPMS/VisorMasterList.action',
                methodName: 'getViewfinderRows',
                fullColumns: columns,
                dynamicSearchColumnsEnabled: false
            }).then(function(g) {
                g.setNoRegMessage(i18n.noRegMsg);
                g.setEmptyMessage(i18n.noRegPreMsg);
                g.updateDataInfo();
                if(!g.isSearchCriteriaOpen()) {
                    g.toggleSearchFields(true);
                }
                _self.grid = g;
                _self.createdDef.resolve();
            }, _self.failure);
            window.setFullScreenMode = function() {
                _self.enterFullScreen();
            };
            MediaQueries.on('resized', function() {
                _self.onResizeWindow();
            });
            on(this.closeVisorBtn, 'click', function() {
                _self.close();
            });
            on(this.exitVisorBtn, 'click', function() { 
                _self.logout();
            });
            on(this.fullScreenBtn, 'click', function() {
                _self.enterFullScreen();
            });
            on(this.exitFullScreenBtns, 'click', function() {
                _self.exitFullScreen();
            });
            on(this.showFullHistory, 'click', function() {
                var historyInnerHTML = '', temp, n = 0;
                for(var hk in _self.history) {
                    if(!_self.history.hasOwnProperty(hk)) {
                        continue;
                    }
                    if(!_self.history[hk].documentCode || _self.history[hk].documentCode === 'null') {
                        continue;
                    }
                    n++;
                    temp = _self.history[hk];
                    historyInnerHTML += ''
                        + '<tr class="' + (n%2===0?'odd':'even') + ' n' + n + '">'
                            + '<td class="view-cell">'
                                + '<img class="cursorPointer" data-history-node=' + n + ' src="' + require.toUrl("bnext/images/" + viewIcon)
                                + '" onclick="window.top.viewfinder.view(' + temp.documentId + ',-1 ,' + temp.fileId  + ',{excludeFromHistory: true, historyNode: this, closeDialog: true});"/>'
                            + '</td>'
                            + '<td class="title-cell">'
                                + temp.documentCode
                            + '</td>'
                        + '</tr>'
                    ;
                }
                if(historyInnerHTML) {
                    core.dialog(''
                        + '<div class="gridExpandable grid-component-container finder-history-container fancy-scroll">'
                            + '<table class="display grid-component-container finder-history-table">'
                                + '<tr>'
                                    + '<th class="view-header">' + i18n.colNameVer + '</th>'
                                    + '<th class="ttitle-header">' + i18n.colNameCode + '</th>'
                                + '</tr>' 
                                + historyInnerHTML 
                            + '</table>'
                        + '</div>', null, null, i18n.historyTitle);
                } else {
                    core.dialog(i18n.noHistoryMessage);
                }

            });
            on(this.cleanLocalHistory, 'click', function() {
                domConstruct.empty(_self.historyNode);
            });
            _self.onResizeWindow();
            this.initialized = true;
            if (skipShow) {
                return core.resolvedPromise();
            } else {
                return callMethod({
                    url: 'Visor.Profile.action',
                    method: 'checkProfile',
                    params: ['initializedAtDocumentViewer']
                }).then(function (r) {
                    if (r) {
                        return _self.show();
                    } else {
                        return core.resolvedPromise();
                    }
                });
            }
        },
        view: function(documentId, requestId, fileId, options) {
            var def = new Deferred();
            var _self = this;
            core.showLoader && core.showLoader();
            this.open(documentId, requestId, fileId, options).then(function() {
                _self.tabContainer.resize();
                def.resolve();
            });
            return def.promise;
        },
        playVideo: function(galleryId) {
            var _self = this;
            this.openAngularFullScreen();
            _self.show(-1, -1, -1, null, true).then(function(){
                _self.enterFullScreen();
                _self.viewerNode.src = 'DownloadMedia.action?galleryId=' + galleryId;
                _self.showViewerNode();
            });
        },
        openAngularFullScreen: function() {
            viewfinderUtil.setAngularFullScreen('full');
        },
        exitAngularFullScreen: function() {
            viewfinderUtil.setAngularFullScreen('normal');
        },
        open: function(documentId, requestId, fileId, options) {
            var _self = this, def = new Deferred();
            this.openAngularFullScreen();
            domConstruct.empty(_self.relatedDocumentsNode);
            documentId = documentId || -1;
            if (!core.isNumeric(fileId)) {
                fileId = -1;
            }
            fileId = fileId || -1;
            requestId = requestId || -1;
            options = options || {};
            if (documentId !== -1 && options.historyNode) {
                var n = domAttr.get(options.historyNode, 'data-history-node');
                var containerNode = core.getDefindedDialog().getDialog().containerNode;
                var tr = query('tr.n' + n, containerNode)[0];
                _self.selectDocument(tr, containerNode);
            }
            if (options.navigatePendingsAtBack) {
                _self.navigatePendingsAtBack = options.navigatePendingsAtBack;
            }
            if (options.fullscreen) {
                _self.enterFullScreen();
            }
            if(options.closeDialog) {
                // Cierra dialogo de forma manual.
                _self.ownerDocument.getElementById("dBoxDialog").dispatchEvent(new KeyboardEvent("keydown", {"keyCode":"27"}));
            }
            if (documentId === -1) {
                _self.clearRelatedDocumentsNode();
                _self.buildEmptyRelatedDocumentsNode();
                _self.showViewerNode();
                _self.fieldUtil.clear();
                _self.updateViewerNodeSrc(documentId, requestId, fileId, options);
                _self.downloadDoc.style.display = 'none';
                def.resolve();
                return def.promise;
            }
            callMethod({
                url: 'Viewfinder.DocumentSimple.action',
                method: 'getDocumentSimple',
                params: [documentId] 
            }).then(function(r) {
                var dynamicTableName = r.document.dynamicTableName;
                delete r.document.dynamicTableName;
                _self.fieldUtil.clear();
                domConstruct.empty(_self.downloadDoc);
                if (r.disposition) {
                    r.document.disposition = r.disposition;
                }
                if (r.informationClassification) {
                    r.document.informationClassification = r.informationClassification;
                }
                function renderDocumentSimple(documentId) {
                    _self.fieldUtil.render(r.document, 'field-display medium-12', false, true, false, null, true).then(function(domMap) {
                        _self.fieldUtil.handleAsDisabled(domMap);
                        _self.fieldUtil.placeAtFirst(domMap);
                        _self.fieldUtil.addClassLiNode(domMap, 'originador', 'icon-person');
                        _self.fieldUtil.addClassLiNode(domMap, 'documentType', 'icon-description');
                        _self.fieldUtil.addClassLiNode(domMap, 'lastModificationDate', 'icon-today');
                        _self.fieldUtil.addClassLiNode(domMap, 'creationDate', 'icon-today');
                        _self.fieldUtil.addClassLiNode(domMap, 'code', 'icon-description');
                        _self.fieldUtil.addClassLiNode(domMap, 'department', 'icon-flag');
                        _self.fieldUtil.addClassLiNode(domMap, 'businessUnit', 'icon-domain');
                        _self.fieldUtil.addClassLiNode(domMap, 'collectingAndStoreResponsibleDescription', 'icon-person');
                        _self.fieldUtil.addClassLiNode(domMap, 'disposition', 'icon-description');
                        _self.fieldUtil.addClassLiNode(domMap, 'informationClassification', 'icon-description');
                        if(domMap['nodePath']) {
                            domClass.add(domMap['nodePath'].domNode, 'bnextFolderLink');
                            on(domMap['nodePath'].domNode, 'click', function() {
                               _self.hide();
                                core.navigateLegacy("v.document.list.ae.view?nodo=" + r.document.nodePath.id);
                            });
                        }
                    });
                    if (options.init) {
                        domClass.add(_self.viewerNode, 'displayNone');
                    } else {
                        _self.showViewerNode();
                        _self.updateViewerNodeSrc(documentId, requestId, fileId, options);
                    }
                    if(!options.excludeFromHistory && (fileId !== -1 || documentId !== -1)) {
                        if (options.documentCode) {
                            _self.addHistory(documentId, fileId, options.documentCode);
                        } else {
                            callMethod({
                                url: 'Viewfinder.DocumentSimple.action',
                                method: 'getDocumentCode',
                                params: [documentId] 
                            }).then(
                                function(code) {
                                    _self.addHistory(documentId, fileId, code);
                                },
                                function(f) {
                                    _self.addHistory(documentId, fileId);
                                }
                            );
                        }
                    }
                    if (r.canDownloadDocument) {
                        domConstruct.create('a', {
                            target: '\"_blank\"',
                            href: '../view/v-download-document.view?id=' + documentId,
                           'class': 'material-icons',
                           innerHTML: 'file_download'
                        }, _self.downloadDoc);
                        _self.downloadDoc.style.display = '';
                    } else {
                        _self.downloadDoc.style.display = 'none';
                    }
                    //se obtienen documentos relacionados
                    callMethod({
                        url: 'Viewfinder.DocumentSimple.Related.action',
                        method: 'getRelatedDocuments',
                        params: [documentId] 
                    }).then(function(documentSimpleList) {
                        if (lang.isArray(documentSimpleList)) {
                            // limpiamos el dom
                            _self.clearRelatedDocumentsNode();
                            if (documentSimpleList.length) {
                                domConstruct.create('strong',{
                                        'class': 'docRef',
                                        innerHTML: '<span>' + i18n.docRef + '</span>'
                                    }, _self.relatedDocumentsNode);
                            } else {
                                if (_self.relatedDocumentsNode.children.length === 0) {
                                    _self.buildEmptyRelatedDocumentsNode();
                                }
                            }
                            array.forEach(documentSimpleList, function(documentSimple) {
                                var fileRelatedInfo = extensionIcons.getExtensionInfo(documentSimple.fileContent, documentSimple);
                                domConstruct.create('a', {
                                     href: 'javascript:void(0)',
                                    'class': 'relatedDocumentTd button',
                                    innerHTML: ''+
                                            '<img border=0 src="' + fileRelatedInfo.iconSrc + '"/>' +
                                            '<span title="' + documentSimple.description + '">' + documentSimple.description + '</span>',
                                    onclick: function() {
                                        core.dialog(
                                            core.specifiedMessage(i18n.confirmOpenRelatedDoc, 'code', documentSimple.description), 
                                            core.i18n.yes, 
                                            core.i18n.no
                                        ).then(
                                            function() {
                                                _self.view(documentSimple.id, -1, documentSimple.fileId);
                                            }
                                        );
                                    }
                                }, _self.relatedDocumentsNode);
                            });
                        }
                    });
                    //relatedDocumentsNode
                    def.resolve();
                }
                if (r.document.documentType) {
                    DynamicFieldUtil.load(documentId, 'DocumentType.Document.Custom.action', _self.dynamicFields, r.document.documentType.id, {
                            cacheOn: false,
                            dijits: true,
                            legacy: false,
                            isQuery: true
                        }, dynamicTableName).then(
                            function(dyn) {
                                _self.fieldUtil.handleAsDisabled(dyn.domMap);
                                _self.fieldUtil.addClass(dyn.domMap, 'dynfield');
                                _self.fieldUtil.addClassLi(dyn.domMap, 'medium-12');
                            renderDocumentSimple(documentId);
                        }, function() {
                            renderDocumentSimple(documentId);
                        }
                    );
                } else {
                    renderDocumentSimple(documentId);
                }
            }, function(f) {
                _self.failure(f);
                def.resolve();
            });
            return def.promise;
        },
        updateViewerNodeSrc: function(documentId, requestId, fileId, options) {
            var _self = this;
            var srcNode = '';
            if (options && options.bnextLogo) {
                srcNode += '&bnextLogo=true';
            }
            _self.viewerNode.src = 'view-finder.view' 
                    + '?fileId=' + fileId 
                    + '&documentId=' + documentId 
                    + '&requestId=' + requestId + srcNode;
        },
        showViewerNode: function() {
            var _self = this;
            domClass.remove(_self.viewerNode, 'displayNone');  
        },
        clearRelatedDocumentsNode: function() {
            var _self = this;
            domConstruct.empty(_self.relatedDocumentsNode);
        },
        buildEmptyRelatedDocumentsNode: function() {
            var _self = this;
            domConstruct.create('div', {
                innerHTML: '' 
                    + '<span class="material-icons emptyIcon">file_present</span>'
                    + '<strong><span class="emptyTitle">' + i18n.relatedEmptyTitle + '</span></strong>'
                    + '<span class="emptySubtitle">' + i18n.relatedEmptySubtitle + '</span>',
                'class': 'emptyRelatedDocs'
            }, _self.relatedDocumentsNode);  
        },
        addHistory: function(documentId, fileId, documentCode) {
            this.history.push({
                documentCode: documentCode,
                documentId: documentId,
                fileId: fileId
            });
            this.renderHistory();
        },
        renderHistory: function() {
            var _self = this;
            for(var hk in this.history) {
                if(!this.history.hasOwnProperty(hk)) {
                    continue;
                }
                if(this.history[hk].domNode || !this.history[hk].documentCode) {
                    continue;
                }
                this.history[hk].domNode = domConstruct.create('td', {}, this.historyNode, 'first');
                var div = domConstruct.create('div', {
                    'class': 'historyNode',
                    innerHTML: '<span>' + this.history[hk].documentCode + '</span>',
                    'title': this.history[hk].documentCode,
                    'onclick': function() {
                        _self.view(
                            _self.history[hk].documentId, 
                            -1,
                            _self.history[hk].fileId, {
                                excludeFromHistory: true
                            }
                        );
                        _self.selectDocument(div);
                    }
                }, this.history[hk].domNode);
                domConstruct.create('img', {
                    'src': require.toUrl("bnext/images/" + gridIcons.view),
                    'class': 'left'
                }, div);
            }
        },
        show: function (documentId, requestId, fileId, options, skipDefaultFile) {
            var _self = this;
            var defaultPromise;
            if (!this.shown && !skipDefaultFile) {
                defaultPromise = this.refreshDefaultFileId();
            } else {
                defaultPromise = core.resolvedPromise();
            }
            this.shown = true;
            return defaultPromise.then(function() {
                _self.renderHistory();
                if (documentId && options) {
                    return _self.view(documentId, requestId, fileId, options);
                }
                _self.tabContainer.resize();
                return core.resolvedPromise();
            });
        },
        hide: function() {
            var _self = this;
            core.showLoader().then(function() {
                require(['bnext/angularNavigator'], function (angularNavigator) {
                    core.hideLoader();
                    angularNavigator.hideDocumentViewer();
                    _self.exitAngularFullScreen();
                });                    
            });
        },
        refreshDefaultFileId: function() {
            var _self = this;
            return callMethod({
                url: 'Visor.Settings.action',
                method: 'checkDefaultFile',
                params: []
            }).then(function(result) {
                if (result.isImage || result.fileId === 0) {
                    return _self.view(-1, -1, -1, {bnextLogo: true});
                } else {
                    return _self.view(-1, -1, result.fileId, null);
                }
            });
        }
    });
    return setup(viewfinder);
});