    define({
        relatedDocumentsLbl: 'Related documents',
        ValidateExit: "Log out",
        ValidateConfirmExit: "Session will close.\nDo you want to continue?",
        ValidateConfirmCleanLocalHistory: 'Are you sure you want to clean the buttons history of consultation?',
        label_author: 'Author: ',
        label_businessUnit: 'Plant ',
        label_department: '{Department} ',
        label_documentType: 'Type ',
        label_originador: 'Originator ',
        label_creationDate: 'Creation date ',
        label_lastModificationDate: 'Approved date',
        label_nodePath: 'Path ',
        label_version: 'Version ',
        label_description: 'Document title ',
        label_code: 'ID ',
        label_informationClassification: 'Information classification ',
        label_disposition: 'Disposition ',
        label_collectingAndStoreResponsibleDescription: 'Collecting and storing responsible ',
        backToQMSBtn: 'Back to Bnext QMS',
        showQueries: 'Show all queries...',
        exitBtn: 'Log out system',
        fullScreenBtn: 'Full screen',
        fullDetailBtn: 'Document detail',
        noRegMsg: 'No documents were found with those parameters.',
        noRegPreMsg: 'Choose search parameters.',
        noHistoryMessage: 'No history found.',
        historyTitle: 'History',
        colNameTipoDoc: 'Document type',
        colNameEstado: 'Status',
        colNameVer: 'View',
        colNameCode: 'ID',
        colNameDepartamento: '{Department}',
        colNameDesc: 'Title',
        colNameDescFile: 'File',
        colNameStatusActive: 'Active',
        colNameStatusInautorization: 'In authorization',
        confirmOpenRelatedDoc: 'Do you really want to open the document "%code%"? <br>The current document will be closed.',
        docRef: 'Related documents: ',
        exitFullScreenBtns: 'Exit full screen',
        downloadBtn: 'Download document',
        searchTab: 'Search',
        detailTab: 'Details',
        relatedTab: 'Related',
        relatedEmptyTitle: 'There are no related documents.',
        relatedEmptySubtitle: 'When they are assigned, you can view them here.',
        downloadButton: 'Download'
    });
