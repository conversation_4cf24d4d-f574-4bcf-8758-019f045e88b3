define([
    'dojo/_base/declare', 'dijit/_WidgetBase', 'dijit/_TemplatedMixin','dijit/_WidgetsInTemplateMixin',
    'dojo/text!./template.html', 'bnext/i18n!bnext/filesAttacher/nls/lang','bnext/gridComponent','dojo/query',
    'dojo/_base/array', 'bnext/columnTypes', 'bnext/gridIcons', 'core', 'dojo/dom-construct',
    'dojo/dom-class',
    'dojo/dom',
    'bnext/module/Uploader','xstyle/css!bnext/filesAttacher/style.css'
],
function(
    declare, _WB, _TM, _WT, tem, i18n, gridComponent, query, array,
    columnTypes, gridIcons, core, domConstruct,
    domClass,
    dom
) {
    var salt = 0,
    grid,
    FileAttacher = {
        freezeHeader: false,
        templateString: tem,
        gridSize: -1,
        tags:i18n,
        serviceStore:'',
        currentEntity:0,
        methodName:'',
        uploadPath:'../DPMS/Upload.fileUploader',
        baseClass:'fileAttacher',
        grid:null,
        tableContainerId: null,
        salt:null,
        files:'',
        hasDelete:true,
        hasUpload:true,
        hasNoRegistryMessage: true,
        lblFile: null,
        fileName:null,
        fullScreenButton: false,
        likeFabButton: false,
        constructor:function(){
            this.salt = salt++;
        },
        postCreate: function() {
            var _self = this;
            try{
                this.fileAttacher_idDocs.value = this.files;
                var 
                //Columnas del gridComponent
                colDescription={
                    type:1,
                    id:'description',
                    title:i18n.column_description,
                    isSortable:false
                },
                colDelete={
                    type:columnTypes['function'],
                    id:'delete',
                    title:i18n.column_delete,
                    icon:gridIcons.remove,
                    action:function(obj){
                        core.dialog(_self.tags.delet_file,_self.tags.Validate.Yes,_self.tags.Validate.No).then(
                            function(){
                                _self.removeFileById.call(_self, obj.id);
                            }
                        );
                    }
                },
                colDownload={
                    type:columnTypes['function'],
                    id:'download',
                    title:i18n.column_download,
                    icon:gridIcons.download,
                    action:function(obj){
                            window.open('../view/v-download-attached.view?id='+obj.id);
                    }
                },
                /*Muestra u oculta la columna de eliminar en base a las condiciones de cada ventana*/
                columns = this.hasDelete ?[colDelete, colDownload, colDescription]:[colDownload, colDescription] ;
                /*Asigna el path que se va a utilizar para el uploadPath*/
                this.fUploader.set('uploadPath', this.uploadPath);
                this.fUploader.set('lblFile', this.lblFile);
                this.fUploader.set('likeFabButton', this.likeFabButton);
                if (this.fullScreenButton) {
                    this.fUploader.set('fullScreenButton', this.fullScreenButton);
                    dom.byId('buttonSection').style.width = '100%';
                    dom.byId('labelButton').style.width = '100%';
                    dom.byId('fileContainer').style.width = '100%';
                    dom.byId('fileAttacher_form').style.width = '100%';
                    
                }
                if (this.likeFabButton) {
                    dom.byId('labelButton').style.display = 'block';
                }
                this.grid = new gridComponent({
                    id: 'fileAttacher_grid_'+ this.salt,
                    container: this.tableContainerId || ('fileAttacher_attached_'+ this.salt), 
                    serviceStore:this.serviceStore + "?currentEntityId="+this.currentEntity, 
                    methodName:this.methodName, 
                    columns : columns,
                    noHeader : false,
                    freezeHeader : this.freezeHeader,
                    onLoaded: this.onLoaded
                });
                grid = this.grid;
                this.grid.setPageSize(this.gridSize).then(function (){
                    _self.serializeDocuments();
                });                    
                /*si está desabilitado, oculta el file uploader y cambia la etiqueta del componente*/
                if(!this.hasUpload){
                    domClass.add(this.domNode, 'readonly');
                    query('.fileAttacher .add').style('display','none');
                    if (this.lblUploadFiles)
                    {
                        this.lblUploadFiles.innerText = this.tags.added_files + ':';
                    }
                }
            }catch (e){
                console.log(e,e.stack);
            }
        },
        removeFileById: function(id) {
            if (!id) {
                return;
            }
            var _self = this;
            grid = _self.grid;
            grid.popRowById(id);
            grid.updateDataInfo();
            _self.serializeDocuments();
            _self.onFileRemoved(id);
            _self.updateFileName('...');
        },
        onReady: function() {
            domConstruct.place(this.fileName, this.fUploader.domNode);
        },
        updateFileName: function(value) {
            this.fileName.innerHTML = value || (this.fUploader.uploader.fileInput.value).replace(/C:\\fakepath\\/, '');
            this.fUploader.uploader.fileInput.value = null;
        },
        showLoader: function() {
        },
        hideLoader: function() {
        },
        /**
         * Verfica si un archivo con el mismo nombre ya esta agregado
         * @param {String} nameFile Nombre del archivo
         * @returns {boolean}
         */
        existsFilebyName: function(nameFile) {
            if(!grid) {
                return;
            }
            return array.some(grid.getBean().data, function(item){
                return item && item.description === nameFile;
            });
        },
        /**
         *Agrega el archivo al grid cuando se sube
         * @param {String} file Archivo que se esta subiendo
         */
        onFileSelected: function(file) {
            var self = this;
            try {
                if (this.existsFilebyName(file.description)) {
                    notice({
                        message: this.tags.fileAlreadyAttached,
                        btn1Action: function() {
                            self.addFileToGrid(file);
                        }
                    });
                } else {
                    this.addFileToGrid(file);
                }
            } catch (e) {
                console.log(e, e.stack);
            }
            this.updateFileName(file ? file.description : false);
        },
        addFileToGrid: function(file) {
            this.showLoader();
            this.grid.pushRow({
                status:1,
                code:'',
                id:file.id,
                description:file.description
            });
            this.grid.updateDataInfo();
            this.serializeDocuments();
            if (this.onFileAdded) {
                this.onFileAdded(file.id);
            }
            this.hideLoader();
        },
        onLoaded: function(e) {
            
        },
        onFileAdded: function() {
        },
        onFileRemoved: function() {
            this.serializeDocuments();
            this.fUploader.onDelete.call(this.fUploader);
        },
        /**
         * Dá compatibilidad con los jsps que aún utilizan metodos viejos
         */
        serializeDocuments: function(){
            try{
            this.files = "";
            var _self = this;
            array.forEach(_self.grid.getBean().data,function(item,index){
                _self.files += item.id + ",";
            });                
            this.fileAttacher_idDocs.value = this.files;
            }catch(e){
                console.log(e,e.stack);
            }
        },
        parseAsIdList: function(){
            try{
                return core.parseAsIdList(grid.getBean().data);
            }catch(e){
                return [];
            }
        },
        _getValueAttr:function(){
            return this.files;              
        },
        _getRowsAttr:function(){
            return grid.getBean().data;              
        },
        clearData: function(){
            this.grid.clearData();
            this.fUploader.onDelete.call(this.fUploader);
            this.fileName.innerHTML = "";
        },
        openUploadWindow: function () {
            this.fUploader.openUploadWindow();
        }
    };
    return declare([_WB, _TM,_WT], FileAttacher);
});