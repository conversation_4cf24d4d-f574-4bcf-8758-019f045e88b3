
define([
    'dojo/query',
    'bnext/survey/_base/$Field',
    'bnext/survey/formObject/PropertiesDiv',
    'bnext/survey/_base/$FieldId',
    'bnext/survey/_util/jsFailureExposure!'],
        function(query, $Field, PropertiesDiv, $FieldId) {
            /**
             * Valida ponderaciones y campos requeridos de TODOS los $Field de la forma
             */
            var 
                displayPropertiesDiv = function(fieldId, field, weightingActived) {
                    if(
                            (field.requiredProperties && !field.filledRequiredProperties) 
                            || (weightingActived && field.weightingItemsValidation && !field.weightingItemsValidation())
                            ) {
                        PropertiesDiv(fieldId).style.display = '';
                    } else {
                        PropertiesDiv(fieldId).style.display = 'none';
                    }
                },
                isValidByAll = function(weightingActived) {
                    query('.main_form_obj_div', 'fieldArea').forEach(function(input) {
                        if(!input.id) {
                            return;
                        }
                        var fieldId = $FieldId(input.id), field = $Field(fieldId);
                        displayPropertiesDiv(fieldId, field, weightingActived);
                    });
                    query('.arrayTable', 'fieldArea').forEach(function(table) {
                        if(!table.parentNode.id) {
                            return;
                        }
                        var fieldId = $FieldId(table.parentNode.id), field = $Field(fieldId);
                        displayPropertiesDiv(fieldId, field, weightingActived);
                    });
                }
            ;
            return isValidByAll;
        });