define([
    'dojo/dom',
    'dojo/dom-construct',
    'bnext/survey/_base/$FieldId',
    'bnext/survey/lang/languages',
    'bnext/survey/_util/jsFailureExposure!',
    'bnext/survey/ext/eventExtend!',
    'bnext/survey/ext/elementExtend!',
    'bnext/survey/ext/elementMethodExtend!'
],
        function(dom, domConstruct, $FieldId, lang) {

            var PropertiesDiv = function(refId) {
                var thisId = $FieldId(refId) + '_uiObject';
                var r = dom.byId(thisId + '_requiredProperties');
                if (!r && dom.byId(thisId)) {
                    r = domConstruct.create('div', {
                        innerHTML: "priority_high",
                        title: lang.current("required_properties"),
                        id: thisId + '_requiredProperties',
                        'class': 'requiredProperties material-icons'
                    }, dom.byId(thisId), 'before');
                } else if (!r) {
                    r = 0;
                }
                return r;
            };
            return PropertiesDiv;
        });