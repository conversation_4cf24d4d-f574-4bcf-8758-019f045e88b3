define([
  'bnext/survey/lang/languages',
  'bnext/survey/formObject/ItemizedFormObject',
  'bnext/survey/_code/SurveyTypeRuleDefinition',
  'bnext/survey/formObject/HtmlCheckBox',
  'bnext/survey/_base/$FC',
  'bnext/survey/_code/page',
  'bnext/survey/_util/jsFailureExposure!',
  'bnext/survey/ext/eventExtend!'
], (lang, itemizedFormObject, sTypeRules, htmlCheckBox, $FC, page) => {
  /**
   * Pregunta tipo 'Opción multiple'
   */
  const multiSelect = itemizedFormObject.extend({
    constructor: function () {
      this.base();
      this.type = 'multiSelect';
      this.size = 'large';
      this.layout = 'vertical';
      this.otherOption = 2;
      this.tienePonderacion = true;
      this.requiredProperties = sTypeRules.fieldMultiSelectRequiredProperties;
    },
    startup: function () {
      for (let i = 0; i < 3; i++) {
        this.add(this.buildNewItem(lang.current('option') + (i + 1)));
      }
    },
    buildNewItem: function (defaultText) {
      const item = new htmlCheckBox(defaultText, this.tienePonderacion);
      item.code = this.getNextItemCode();
      return item;
    },
    changeLanguage: function (new_lang) {
      this.base(new_lang);
      $FC('.other').each((el) => {
        el.innerHTML = lang.get(page.form.languages[new_lang], 'other');
      });
    },
    render: function (parentId) {
      this.base(parentId);
      this.applySize(this.size);
    },
    showProperties: function () {
      this.base();
      this.renderProperty(lang.current('otherOption'), 'otherOption', {
        1: lang.current('yes'),
        2: lang.current('no')
      });
      if (this.formArea !== null) {
        this.renderProperty(
          lang.current('size'),
          'size',
          {
            small: lang.current('small'),
            large: lang.current('large')
          },
          'vertical'
        );
      }
      this.renderProperty(lang.current('orientation'), 'layout', {
        vertical: lang.current('vertical'),
        horizontal: lang.current('horizontal')
      });
    }
  });
  return multiSelect;
});
