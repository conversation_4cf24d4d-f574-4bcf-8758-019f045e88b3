
define([
    'dojo/query',
    'bnext/survey/formObject/ValidByDOM',
    'bnext/survey/formObject/ValidAlert',
    'bnext/survey/_util/jsFailureExposure!'],
        function(query, isValidByDOM, isValidAlert) {
            /**
             * Valida ponderaciones y valores requeridos del DOM de un $Field especifico
             */
            var isValidByDOMCreation = function(pregunta) {
                query('input[type=text].ponderada', pregunta).forEach(function(campo) {
                    isValidByDOM(campo);
                });
                isValidAlert({noSelfValidate: true, context: pregunta});
            };
            return isValidByDOMCreation;
        });