define([
  'dojo/on',
  'dojo/dom-construct',
  'dojo/dom-class',
  'dojo/_base/lang',
  'dojo/date/locale',
  'bnext/angularTimePicker',
  'bnext/gridComponentUtil',
  'bnext/survey/lang/languages',
  'bnext/survey/formObject/_formObject',
  'bnext/survey/_code/SurveyTypeRuleDefinition',
  'dojo/text!./templates/Geolocation.html',
  'bnext/survey/_util/jsFailureExposure!'
], (on, domConstruct, domClass, dojoLang, locale, angularTimePicker, gcUtil, lang, formObject, rules, template) => {
  const geolocation = formObject.extend({
    constructor: function () {
      this.base();
      this.requiredProperties = rules.fieldTextFieldRequiredProperties;
      this.type = 'geolocation';
      this.size = 'large';
    },
    setSchema: function (obj) {
      this.base(obj);
      this.size = obj?.size ? obj.size : 'large';
    },
    getSchema: function () {
      const item = this.base();
      if (this.size !== null && this.size !== undefined) {
        item.size = this.size;
      }
      return item;
    },
    render: function (parentId) {
      this.base(parentId);
      const templateValue = dojoLang.replace(template, {
        i18n: {
          gpsInfo: lang.current('gpsInfo'),
          gpsLatitude: lang.current('gpsLatitude'),
          gpsLongitude: lang.current('gpsLongitude')
        },
        fieldSizeClass: this.size === 'large' ? 'small-6' : 'small-12'
      });
      const container = domConstruct.toDom(templateValue);
      domConstruct.place(container, `${this.id}_items`);
      this.applySize(this.size);
    },
    showProperties: function () {
      this.base();
    }
  });
  return geolocation;
});
