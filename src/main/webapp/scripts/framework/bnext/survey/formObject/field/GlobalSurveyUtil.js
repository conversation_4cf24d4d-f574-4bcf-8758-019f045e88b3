define([
  'bnext/survey/formObject/field/GlobalSurveyData',
  'dojo/_base/array',
  'bnext/survey/lang/languages',
  'bnext/survey/_util/CatalogExternalData',
  'bnext/survey/_code/page',
  'dojo/query',
  'dojo/dom',
  'dojo/dom-class'
], (GlobalSurveyData, array, i18n, CatalogExternalData, page, query, dom, domClass) => {
  function updateRequestRestrictForm(parentId) {
    const request = page.getRequestEntity();
    if (request && request.restrictRecordsField !== parentId) {
      request.restrictRecordsByDepartment = true;
      request.validateAccessFormDepartment = false;
      request.restrictRecordsField = parentId;
      page.docComponent.updateAccessFormDepartment();
    }
  }
  function disableRequestRestrictForm() {
    const request = page.getRequestEntity();
    if (!request) {
      return;
    }
    request.restrictRecordsByDepartment = false;
    request.validateAccessFormDepartment = false;
    request.restrictRecordsField = null;
    page.docComponent.updateAccessFormDepartment();
  }
  function refreshGlobalOrgData(type, id, parentId, value) {
    const key = GlobalSurveyData.getOrganizationDataKey(type);
    GlobalSurveyData.initialize(type, id);
    const items = value ? array.filter(CatalogExternalData.comboElementsExternal, (item) => item[key] && +item.value === +value) : null;
    if (items?.length) {
      if (type === 'businessUnitDepartment') {
        updateRequestRestrictForm(parentId);
      }
    } else {
      GlobalSurveyData.clear(type, id);
    }
  }
  function updateGlobalDepartmentData(requestEntity) {
    const type = 'businessUnitDepartment';
    if (requestEntity.restrictRecordsByDepartment) {
      if (requestEntity.validateAccessFormDepartment) {
        const result = GlobalSurveyData.removeOtherMain(type);
        if (result) {
          hideAllIcons(type);
        }
      } else {
        const result = GlobalSurveyData.enableNewIsMain(type, `${requestEntity.restrictRecordsField}_uiObject`);
        if (result) {
          showIcons(type, requestEntity.restrictRecordsField);
          removeOtherIcons(type, null, requestEntity.restrictRecordsField);
        }
      }
    } else {
      const result = GlobalSurveyData.removeOtherMain(type);
      if (result) {
        hideAllIcons(type);
      }
    }
  }
  function showIcons(type, parentId) {
    // muestra el icono indicando que la pregunta está asociada a un departamento/area/sociedad
    for (const domNode of query(`#${parentId} .field-icons-container,#${parentId} .field-icons-container > .${type}-icon`)) {
      domClass.remove(domNode, 'displayNone');
    }
  }
  function hideAllIcons(type) {
    const falseKeyTitle = getFalseKeyTitle(type);
    for (const domNode of query(` .field-icons-container > .${type}-icon`)) {
      domClass.add(domNode, 'is-not-main');
      domNode.title = falseKeyTitle;
    }
  }
  function getTrueKeyTitle(type) {
    const trueKeyTitle = i18n.current('externalCatalogMain_true').replace(/\{something\}/g, i18n.current(`${type}Label`));
    return trueKeyTitle;
  }
  function getFalseKeyTitle(type) {
    const falseKeyTitle = i18n.current('externalCatalogMain_false').replace(/\{something\}/g, i18n.current(`${type}Label`));
    return falseKeyTitle;
  }
  function refreshKeyRender(type, id, parentId, value) {
    const falseKeyTitle = getFalseKeyTitle(type);
    const trueKeyTitle = getTrueKeyTitle(type);
    if (+value === 1) {
      // Quita el atributo `isMain` de todos los campos y deja solo el seleccionado
      GlobalSurveyData.enableNewIsMain(type, id);
      // Oculta y muestra icono indicador de atributo `isMain` correspondiente
      for (const domNode of query(`.field-icons-container > .${type}-icon`)) {
        domClass.add(domNode, 'is-not-main');
        domNode.title = falseKeyTitle;
      }
      for (const domNode of query(`#${parentId} .field-icons-container > .${type}-icon`)) {
        domClass.remove(domNode, 'is-not-main');
        domNode.title = trueKeyTitle;
      }
    } else if (+value === 0) {
      GlobalSurveyData.removeMain(type, id);
      for (const domNode of query(`#${parentId} .field-icons-container > .${type}-icon`)) {
        domClass.add(domNode, 'is-not-main');
        domNode.title = falseKeyTitle;
      }
    }
  }
  function removeOtherIcons(type, id, parentId) {
    const trueKeyTitle = getTrueKeyTitle(type);
    for (const domNode of query(`#${parentId} .field-icons-container > .${type}-icon`)) {
      domClass.remove(domNode, 'is-not-main');
      domNode.title = trueKeyTitle;
    }
    refreshKeyRender(type, id, parentId, 1);
  }
  function getOrganizationContainerPropertiesDom(type) {
    const key = GlobalSurveyData.getOrganizationDataKey(type);
    return query(`#propertiesArea .${key}-hr-class,#propertiesArea .${key}-container-class`);
  }
  function hideKey(type, parentId, fieldId) {
    // oculta el icono indicando que la pregunta está asociada a un departamento/area/sociedad
    for (const domNode of query(`#${parentId} .field-icons-container > .${type}-icon`)) {
      domClass.add(domNode, 'displayNone');
    }
    // oculta el `iconContainer` completo cuando no exista ninguna opción seleccionada
    if (query(`#${parentId} .field-icons-container > *.displayNone`).length === 3) {
      for (const domNode of query(`#${parentId} .field-icons-container`)) {
        domClass.add(domNode, 'displayNone');
      }
    }
    // oculta las propiedades indicando que la pregunta está asociada a un departamento/area/sociedad
    for (const domNode of getOrganizationContainerPropertiesDom(type)) {
      domClass.add(domNode, 'displayNone');
    }
    if (type === 'businessUnitDepartment') {
      const hasMain = GlobalSurveyData.alreadyMainCount(type).length === 0;
      if (!hasMain) {
        disableRequestRestrictForm();
      }
    }
  }
  function showKey(type) {
    // muestra las propiedades indicando que la pregunta está asociada a un departamento/area/sociedad
    for (const domNode of getOrganizationContainerPropertiesDom(type)) {
      domClass.remove(domNode, 'displayNone');
    }
  }
  function displayTypeCheckKey(type, id, parentId, externalItem) {
    const key = GlobalSurveyData.getOrganizationDataKey(type);
    if (externalItem && !externalItem[key]) {
      if (!GlobalSurveyData.get(type, id)) {
        refreshGlobalOrgData(type, id, parentId, null);
      }
      hideKey(type, parentId);
      return null;
    }
    if (externalItem?.[key] && !GlobalSurveyData.get(type, id)) {
      refreshGlobalOrgData(type, id, parentId, externalItem.value);
    } else if (!externalItem) {
      return null;
    }
    showIcons(type, parentId);
    if (GlobalSurveyData.isMain(type, id)) {
      removeOtherIcons(type, id, parentId);
    } else {
      refreshKeyRender(type, id, parentId, 0);
    }
    return true;
  }
  const GlobalSurveyUtil = {
    updateGlobalDepartmentData: updateGlobalDepartmentData,
    refreshGlobalOrgData: refreshGlobalOrgData,
    displayTypeCheckKey: displayTypeCheckKey,
    refreshKeyRender: refreshKeyRender,
    hideKey: hideKey,
    showKey: showKey
  };
  return GlobalSurveyUtil;
});