define([
    'bnext/survey/_base/$SV',
    'bnext/survey/formObject/HtmlArray',
    'bnext/survey/_util/jsFailureExposure!',
    'bnext/survey/ext/eventExtend!'
],
        function($SV, htmlArray) {
            var htmlStarRatingArray = htmlArray.extend({
                constructor: function(columns_) {
                    this.base(columns_);
                    this.numberOfStars = 5;
                },
                getSchema: function() {
                    var item = this.base();
                    item.numberOfStars = this.numberOfStars;
                    item.code = this.code;
                    return item;
                },
                setSchema: function(obj) {
                    this.base(obj);
                    this.numberOfStars = obj.numberOfStars;
                    if (obj) {
                        this.code = obj.code;
                    }
                },
                renderItem: function() {
                    var col = $td({
                        "class": "arrayItem alignCenter"
                    });
                    for (var i = 0; i < this.numberOfStars; i++) {
                        var star = $img({
                            id: this.id + "_star" + i.toString(),
                            src: "../images//scorecard/starrat.gif",
                            title: (i + 1) + "/" + this.numberOfStars,
                            "class": "star",
                            name: i
                        });
                        col.appendChild(star);
                        Event.observe(star, "mouseover", this.starOn.bindAsEventListener(this), 0);
                        Event.observe(star, "mouseout", this.starOff.bindAsEventListener(this), 0);
                    }
                    return(col);
                },
                starOn: function(evt) {
                    var order = evt.target ? evt.target.name : evt.srcElement.name;
                    for (var i = 0; i <= order; i++)
                        $SV(this.id + "_star" + i.toString()).src = "../images//scorecard/starrat-selected.gif";
                },
                starOff: function(evt) {
                    for (var i = 0; i < this.numberOfStars; i++)
                        $SV(this.id + "_star" + i).src = "../images//scorecard/starrat.gif";
                }
            });
            return htmlStarRatingArray;
        });