define([
  'core',
  'dojo/dom',
  'dojo/_base/lang',
  'dojo/dom-construct',
  'dojo/dom-attr',
  'dojo/dom-class',
  'dojo/store/Memory',
  'bnext/survey/lang/languages',
  'bnext/survey/_code/SurveyTypeRuleDefinition',
  'bnext/survey/formObject/PropertiesPositionManager',
  'bnext/administrator/surveys/SurveyCaptureUtils',
  'dojo/query',
  'dojo/dom-style',
  'bnext/survey/_code/FieldRulesDefinition',
  'bnext/survey/formObject/_FieldDefinitionLabels',
  'bnext/survey/formObject/ConditionalFieldProperty',
  'bnext/survey/formObject/WeightedFieldsProperty',
  'bnext/survey/formObject/_ShowWeighting',
  'bnext/survey/formObject/SummationProperty',
  'bnext/survey/formObject/ConditionalExpirationFieldProperty',
  'bnext/survey/formObject/HtmlTextLine',
  'bnext/survey/formObject/SurveyFieldAnswerType',
  'bnext/survey/formObject/PropertiesDiv',
  'bnext/survey/formObject/ValidAlert',
  'bnext/survey/_base/Base',
  'bnext/survey/_base/$H',
  'bnext/survey/_base/$SV',
  'bnext/survey/_code/page',
  'bnext/survey/formObject/_SearchableFieldGrid',
  'bnext/gridComponentUtil',
  'bnext/survey/_util/domUtilities',
  'dijit/registry',
  'dojox/form/CheckedMultiSelect',
  'bnext/survey/_util/ConditionalType',
  'bnext/survey/formObject/HierarchyLevelProperty',
  'xstyle/css!dojox/form/resources/CheckedMultiSelect.css',
  'bnext/survey/_util/jsFailureExposure!',
  'bnext/survey/ext/elementMethodExtend!',
  'dojo/domReady!'
], (
  core,
  dom,
  dojoLang,
  domConstruct,
  domAttr,
  domClass,
  Memory,
  lang,
  sTypeRules,
  propPositionManager,
  SurveyCaptureUtils,
  query,
  domStyle,
  fieldRulesDefinition,
  FieldDefinitionLabels,
  ConditionalFieldProperty,
  WeightedFieldsProperty,
  showWeighting,
  SummationProperty,
  ConditionalExpirationFieldProperty,
  htmlTextLine,
  SurveyFieldAnswerType,
  PropertiesDiv,
  isValidAlert,
  Base,
  $H,
  $SV,
  page,
  _SearchableFieldGrid,
  gcUtil,
  domUtilities,
  registry,
  CheckedMultiSelect,
  ConditionalType,
  HierarchyLevelProperty
) => {
  const cType = gcUtil.cType;
  const formObject = Base.extend({
    constructor: function () {
      this.isSelected = 0;
      this.title = new htmlTextLine(1);
      this.title.cssClass = 'itemTitle';
      this.subTitle = new htmlTextLine(1);
      this.subTitle.cssClass = 'itemSubTitle';
      this.required = 't';
      this.image = 0;
      this.currentHeaderId = 0;
      this.currentItemId = 0;
      this.cssClassUiObjectItems = 'uiObjectItems';
      this.requiredProperties = true;
      this.propertiesArray = [];
      this.titleAttr = false;
      this.filledRequiredProperties = false;
      this.deletable = 1;
      this.hasUpDroppable = 1;
      this.hasDownDroppable = 1;
      this.defaultTitle = null;
      this.defineFieldProperty('conditionalQuestion', 'f');
      this.defineFieldProperty('conditionalExpirationQuestion', '0');
      this.conditionalIds = [];

      this.defineFieldProperty('summationQuestion', 'f');
      this.summationAnswerValues = [];
      this.summationIds = [];
      this.supportedSummationFields = ['textFieldArray', 'textField'];

      this.defineFieldProperty('selectedWeightedField');
      this.weightedDialogFieldIds = [];
      this.weightedFieldIds = [];

      this.conditionalExpirationIds = [];
      this.hierarchyConditionalData = {};
      this.hierarchyConditionalExpirationData = {};
      this.conditionalExpirationDays = {};
      this.conditionalLimits = {};
      this.hierarchyLevelFields = {};
      this.defineFieldProperty('addFindingsEnabled', '0');
      this.defineFieldProperty('addActivitiesEnabled', '0');
      this.defineFieldProperty('activitiesTemplateEnabled', '0');
      this.verifierNameProperties = {
        user: 'activity-verifier-user'
      };
      this.implementNameProperties = {
        'business-unit-position': 'activity-implementer-business-unit-position',
        'organizational-unit-position': 'activity-implementer-organizational-unit-position',
        user: 'activity-implementer-user'
      };
      this.defineFieldProperty('pivotTableOnMobile', true);
      this.defineFieldProperty('configureShowSummationField', true);
      this.customProperties = new sTypeRules.field_FormObjectProperties({ _formObject: this });
      this.defineFieldProperty('hidden', false);
      this.firstLoad = true;
    },
    destructor: () => false,
    typeOf: function () {
      return this.type;
    },
    removeLanguage: function (lang) {
      this.title.removeLanguage(lang);
      if (this.subTitle) {
        this.subTitle.removeLanguage(lang);
      }
    },
    changeLanguage: function (lang) {
      this.title.changeLanguage(lang);
      if (this.subTitle) {
        this.subTitle.changeLanguage(lang);
      }
    },
    select: function (isSelected) {
      if (isSelected) {
        propPositionManager.hideProperties = this.hideProperties;
      } else {
        propPositionManager.hideProperties.call(this);
      }
      this.isSelected = isSelected;
      const container = $SV(this.id);
      const textAreaList = container.getElementsByTagName('TEXTAREA');
      for (let i = 0; i < textAreaList.length; i++) {
        const element = textAreaList[i];
        if (isSelected) {
          Element.addClassName(element, 'isSelected');
        } else {
          Element.removeClassName(element, 'isSelected');
        }
      }
      const inputList = container.getElementsByTagName('INPUT');
      for (i = 0; i < inputList.length; i++) {
        element = inputList[i];
        if (isSelected && element.type === 'text') {
          Element.addClassName(element, 'isSelected');
        } else {
          Element.removeClassName(element, 'isSelected');
        }
      }
      if (propPositionManager) {
        if (this.type === 'menuSelect' && isSelected) {
          propPositionManager.height = 125;
        } else if ((this.type === 'exclusiveSelectYesNo' || this.type === 'textFieldArray') && isSelected) {
          propPositionManager.height = 170;
        } else if ((this.type === 'exclusiveSelect' || this.type === 'multiSelect') && isSelected) {
          propPositionManager.height = 210;
        } else if ((this.type === 'textField' || this.type === 'listSelectArray' || this.type === 'listSelectArrayFlipped') && isSelected) {
          propPositionManager.height = 250;
        } else {
          propPositionManager.height = 172;
        }
        propPositionManager.refreshSize();
      }
      if (isSelected) {
        this.firstLoad = true;
        this.showProperties();
      }
    },
    save: function () {
      this.title.save();
      if (this.subTitle) {
        this.subTitle.save();
      }
      if (this.answerField) {
        this.answerField.save();
      }
    },
    initialize: function (fieldConfig, fieldDefinitionRef) {
      this.base(fieldConfig);
      this.deletable = fieldConfig.deletable;
      this.hasUpDroppable = fieldConfig.hasUpDroppable;
      this.hasDownDroppable = fieldConfig.hasDownDroppable;
      this.defaultTitle = fieldConfig.defaultTitle;
      if (this.defaultTitle) {
        this.title.defaultTitle = this.defaultTitle;
        this.title.initialize({
          defaultTitle: this.defaultTitle
        });
      }
    },
    getVerifierEntity: function (value) {
      for (const key in this.verifierNameProperties) {
        if (!Object.hasOwn(this.verifierNameProperties, key)) {
          continue;
        }
        if (this.verifierNameProperties[key] === value) {
          return key;
        }
      }
      return value;
    },
    getImplementerEntity: function (value) {
      for (const key in this.implementNameProperties) {
        if (!Object.hasOwn(this.implementNameProperties, key)) {
          continue;
        }
        if (this.implementNameProperties[key] === value) {
          return key;
        }
      }
      return value;
    },
    getSchema: function () {
      const item = {};

      const defaultValue = this.getDefaultTextAnswer() || 0;
      item.title = this.title.getSchema();
      if (this.subTitle) {
        item.subTitle = this.subTitle.getSchema();
      }
      if (typeof this.type !== 'undefined') {
        item.type = this.type;
      }
      if (typeof this.required !== 'undefined') {
        item.required = this.required;
      }
      if (typeof this.image !== 'undefined') {
        item.image = this.image;
      }
      if (typeof this.respondentType !== 'undefined') {
        item.respondentType = this.respondentType;
      }
      item.deletable = this.deletable;
      item.hasUpDroppable = this.hasUpDroppable;
      item.hasDownDroppable = this.hasDownDroppable;

      item.activitiesTemplateId = this.activitiesTemplateId || null;

      item.activitiesTemplateImplementerEntity = this.getImplementerEntity(this.activitiesTemplateImplementerEntity) || null;
      //Textos
      item.activityImplementerBusinessUnitPositionLabel = this['activity-implementer-business-unit-position'] || null;
      item.activityImplementerOrganizationalUnitPositionLabel = this['activity-implementer-organizational-unit-position'] || null;
      item.activityImplementerUserLabel = this['activity-implementer-user'] || null;
      //IDs
      item.activityImplementerBusinessUnitPositionId = this['activity-implementer-business-unit-position-entity-value'] || null;
      item.activityImplementerOrganizationalUnitPositionId = this['activity-implementer-organizational-unit-position-entity-value'] || null;
      item.activityImplementerUserId = this['activity-implementer-user-entity-value'] || null;

      item.activitiesTemplateVerifierEntity = this.getVerifierEntity(this.activitiesTemplateVerifierEntity) || null;
      //Textos
      item.activityVerifierBusinessUnitPositionLabel = this['activity-verifier-business-unit-position'] || null;
      item.activityVerifierOrganizationalUnitPositionLabel = this['activity-verifier-organizational-unit-position'] || null;
      item.activityVerifierUserLabel = this['activity-verifier-user'] || null;
      //IDs
      item.activityVerifierBusinessUnitPositionId = this['activity-verifier-business-unit-position-entity-value'] || null;
      item.activityVerifierOrganizationalUnitPositionId = this['activity-verifier-organizational-unit-position-entity-value'] || null;
      item.activityVerifierUserId = this['activity-verifier-user-entity-value'] || null;

      item.currentHeaderId = this.currentHeaderId;
      item.currentItemId = this.currentItemId;
      if (this.propertiesArray) {
        for (const prop of this.propertiesArray) {
          if (this[prop.name] != null && typeof this[prop.name] !== 'undefined') {
            item[prop.name] = this[prop.name];
          }
        }
      }
      if (defaultValue) {
        item.defaultValue = defaultValue;
      }
      item.conditionals = this.getConditionalItems(ConditionalFieldProperty);
      item.summationFields = this.getSummationItems();
      item.weightedFields = this.getSelectedWeightedField();
      item.conditionalsExpiration = this.getConditionalItems(ConditionalExpirationFieldProperty);
      item.conditionalExpirationQuestion = `${this.conditionalExpirationQuestion}` === '1';
      item.formArea = this.formArea;
      if (typeof this.tema !== 'undefined') {
        item.tema = this.tema;
      }
      return item;
    },
    initializer: function () {
      this.title.setSchema('');
      this.respondentType = -1;
      this.tema = 0;
      this.areaPlaza = -1;
      this.pilarOxxo = -1;
      this.required = 't';
      this.image = 0;
      this.currentHeaderId = 0;
      this.currentItemId = 0;
      this.deletable = 1;
      this.hasUpDroppable = 1;
      this.hasDownDroppable = 1;
      this.formArea = null;
    },
    setSchema: function (obj1) {
      const obj = obj1 || {};
      this.title.setSchema(obj.title || '');
      this.respondentType = obj.respondentType?.id ? obj.respondentType.id : -1;
      this.tema = obj.tema?.id ? obj.tema.id : 0;
      this.areaPlaza = obj.areaPlaza?.id ? obj.areaPlaza.id : -1;
      this.pilarOxxo = obj.pilarOxxo?.id ? obj.pilarOxxo.id : -1;
      if (typeof this.subTitle?.setSchema === 'function') {
        this.subTitle?.setSchema(obj.subTitle);
      }
      this.required = obj.required && obj.required === 'f' ? 'f' : 't';
      this.image = obj.image || 0;
      this.currentHeaderId = obj.currentHeaderId || 0;
      this.currentItemId = obj.currentItemId || 0;
      this.conditionalQuestion = obj.conditionalQuestion === 'f' ? 'f' : 't';
      this.pivotTableOnMobile = obj.pivotTableOnMobile || 't';
      this.conditionalIds = this.getConditionalsIds(obj.conditionals);
      this.summationQuestion = obj.summationQuestion === 'f' ? 'f' : 't';
      this.configureShowSummationField = obj.configureShowSummationField;
      this.summationIds = this.getConditionalsIds(obj.summationFields);

      this.selectedWeightedField = obj.selectedWeightedField === 'f' ? 'f' : 't';
      this.weightedFieldIds = this.getConditionalsIds(obj.weightedFields);

      this.conditionalExpirationIds = this.getConditionalsIds(obj.conditionalsExpiration);
      this.conditionalExpirationDays = this.getConditionalExpirationDays(obj.conditionalsExpiration);
      this.conditionalLimits = this.getConditionalLimits(obj.conditionals);
      this.hierarchyConditionalData = this._getHierarchyConditionalData(obj.conditionals);
      this.hierarchyConditionalExpirationData = this._getHierarchyConditionalData(obj.conditionalsExpiration);
      page.form.addObjConditionalFields(obj);
      if (typeof obj.deletable === 'undefined') {
        this.deletable = 1;
      } else {
        this.deletable = obj.deletable;
      }
      if (typeof obj.hasUpDroppable === 'undefined') {
        this.hasUpDroppable = 1;
      } else {
        this.hasUpDroppable = obj.hasUpDroppable;
      }
      if (typeof obj.hasDownDroppable === 'undefined') {
        this.hasDownDroppable = 1;
      } else {
        this.hasDownDroppable = obj.hasDownDroppable;
      }
      this.activitiesTemplateId = obj.activitiesTemplateId || null;

      if (obj.activitiesTemplateImplementerEntity) {
        this.activitiesTemplateImplementerEntity =
          this.implementNameProperties[obj.activitiesTemplateImplementerEntity] || obj.activitiesTemplateImplementerEntity || null;
      } else {
        this.activitiesTemplateImplementerEntity = null;
      }
      //Textos
      this['activity-implementer-business-unit-position'] = obj.activityImplementerBusinessUnitPositionLabel || null;
      this['activity-implementer-organizational-unit-position'] = obj.activityImplementerOrganizationalUnitPositionLabel || null;
      this['activity-implementer-user'] = obj.activityImplementerUserLabel || null;
      //IDs
      this['activity-implementer-business-unit-position-entity-value'] = obj.activityImplementerBusinessUnitPositionId || null;
      this['activity-implementer-organizational-unit-position-entity-value'] = obj.activityImplementerOrganizationalUnitPositionId || null;
      this['activity-implementer-user-entity-value'] = obj.activityImplementerUserId || null;

      if (obj.activitiesTemplateVerifierEntity) {
        this.activitiesTemplateVerifierEntity = this.verifierNameProperties[obj.activitiesTemplateVerifierEntity] || obj.activitiesTemplateVerifierEntity || null;
      } else {
        this.activitiesTemplateVerifierEntity = null;
      }

      //Textos
      this['activity-verifier-business-unit-position'] = obj.activityVerifierBusinessUnitPositionLabel || null;
      this['activity-verifier-organizational-unit-position'] = obj.activityVerifierOrganizationalUnitPositionLabel || null;
      this['activity-verifier-user'] = obj.activityVerifierUserLabel || null;
      //IDs
      this['activity-verifier-business-unit-position-entity-value'] = obj.activityVerifierBusinessUnitPositionId || null;
      this['activity-verifier-organizational-unit-position-entity-value'] = obj.activityVerifierOrganizationalUnitPositionId || null;
      this['activity-verifier-user-entity-value'] = obj.activityVerifierUserId || null;

      if (typeof this.propertiesArray === 'object' && this.propertiesArray.length) {
        for (const prop of this.propertiesArray) {
          if (obj[prop.name] != null && typeof obj[prop.name] !== 'undefined') {
            this[prop.name] = obj[prop.name];
          }
        }
      }
      this.conditionalExpirationQuestion = obj.conditionalExpirationQuestion ? '1' : '0';
      this.daysToExpireLabel = obj.conditionalExpirationQuestion ? '1' : '0';
      if (obj.defaultValue) {
        this.setDefaultTextAnswer(obj.defaultValue);
      }
      this.formArea = obj.formArea || null;
    },
    getDefaultTextAnswer: () => {
      //override this function at every field so it'll save its correct value
      return false;
    },
    getNextHeaderId: function () {
      return this.currentHeaderId++;
    },
    getNextItemId: function () {
      return this.currentItemId++;
    },
    getNextHeaderCode: function () {
      return `head${this.getNextHeaderId()}`;
    },
    getNextItemCode: function () {
      return `item${this.getNextItemId()}`;
    },
    setDefaultTextAnswer: (value) => {
      //override this function at every field so it'll SET its correct value
    },
    /**
     * Propiedades definidas con este metodo son automaticamente colectadas en
     * getSchema()
     *
     * Si se utilizan los parametros "conf" y "label" NO NO NO NO NO NO NO <NO>
     * NO NO NO se debe utilizar el renderProperty de esas propiedades
     *
     * @param {type} propName : Es el "name" debe ser unico y no exitir ya en el field
     * @param {type} value    : Es el nuevo valor que tendra la propiedad, si ya esta definida el valor se redefine
     * @param {type} conf     : es el objeto de atributo->valor que reciben los metodos "renderProperty"
     * @param {type} label    : Es la etiqueta de la propiedad que se le mostrará al usuario
     */
    defineFieldProperty: function (propName, value, conf, label) {
      let exists = false;
      let arrayIdx = -1;
      if (!this.propertiesArray) {
        this.propertiesArray = [];
      }
      for (let i = 0, l = this.propertiesArray.length; i < l; i++) {
        if (this[propName]) {
          exists = true;
          arrayIdx = i;
        }
      }
      if (!exists) {
        const def = {
          name: propName,
          defaultValue: value,
          conf: conf || false,
          label: label || `Property ${this.propertiesArray.length}`
        };
        this.propertiesArray.push(def);
      } else if (conf) {
        this.propertiesArray[arrayIdx].conf = conf;
        if (value != null && typeof value !== 'undefined') {
          this.propertiesArray[arrayIdx].defaultValue = value;
        }
        if (label) {
          this.propertiesArray[arrayIdx].label = label;
        }
      }
      if (value != null && typeof value !== 'undefined') {
        this[propName] = value;
      }
    },
    render: function (parentId) {
      const contentDiv = this.createMainDiv(parentId);
      if (dom.byId(`${this.id}_title`)) {
        //do nothing
      } else {
        this.title.id = `${this.id}_title`;
        this.title.render(contentDiv, 1, this.required);
      }
      if (this.titleAttr) {
        domAttr.set(dom.byId(this.title.id), this.titleAttr);
      }
      if (this.subTitle) {
        this.subTitle.id = `${this.id}_subtitle`;
        this.subTitle.useTipLabel = true;
        this.subTitle.render(contentDiv, 1);
      }
      if (dom.byId(`${this.id}_items`)) {
        //do nothing
      } else {
        const itemsContainer = dom.byId(`${this.id}_items`)
          ? $SV(`${this.id}_items`)
          : $div({
              id: `${this.id}_items`,
              class: this.cssClassUiObjectItems
            });
        contentDiv.appendChild(itemsContainer);
      }
      this.onChangeConditionalQuestion(null, ConditionalFieldProperty.conditional.question, this[ConditionalFieldProperty.conditional.question]);

      //Si el Field a dibujarse tiene alguna propiedad "requerida" entonces
      //se dibuja con el simbolo de alerta que indica que hay que completarlos
      if (this.requiredProperties) {
        if (typeof this.tema === 'undefined' || this.tema < 0) {
          if (this.showIconForProperties) {
            if (this.showIconForProperties()) {
              PropertiesDiv(this.id);
            }
          } else {
            PropertiesDiv(this.id);
          }
        }
      }
    },
    createMainDiv: function (parentId) {
      const parent = $SV(parentId);
      let contentDiv = $SV(this.id);
      if (!contentDiv) {
        contentDiv = $div({
          id: this.id,
          class: `main_form_obj_div css_${this.type}`
        });
        parent.appendChild(contentDiv);
      }
      return contentDiv;
    },
    refresh: function () {
      this.save();
      const parent = $SV(this.id);
      //parent.parentNode.removeChild($SV(this.id));
      //this.render(this.parentId);
    },
    renderImage: (container) => {
      //container.appendChild($div({id:this.id+"_image","class":"field-image"},
      //$img({src:"/uploads/images/"+this.image+"?timestamp="+Math.random()})));
    },
    renderSelectProperty: function (label, property, args) {
      return this.renderComboProperty(
        label,
        property,
        args.data || [],
        args.required || false,
        args.onBeforeChange || null,
        args.parentProperty || null,
        args.skipDefaultOption || false,
        args.onAfterChange || null,
        args.className || null,
        args.domContainer || null
      );
    },
    renderComboProperty: function (label1, property, arr, required, onBeforeChange, parentProperty, skipDefaultOption, onAfterChange, className, domContainer, disabled) {
      let label = label1;
      const selectId = `${this.id}_${property}select2`;
      if (dom.byId(selectId)) {
        return;
      }
      const parent = domContainer || $SV('propertiesArea');
      const _self = this;
      domConstruct.create('hr', { class: `${property}-hr-class` }, parent);
      if (required) {
        label = `${label}*`;
      }
      const fs = domConstruct.create(
        'div',
        {
          class: `propertyWrapper ${property}-container-class ${parentProperty}-parent-class ${className || ''}`,
          innerHTML: `<span>${label}</span>`
        },
        parent
      );
      const ctn = domConstruct.create('span', {}, fs);
      const sel = domConstruct.create(
        'select',
        {
          id: selectId,
          'data-property-name': property,
          'is-required': required ? 'is-required' : 'not-required'
        },
        ctn
      );
      const events = {};
      if (!skipDefaultOption) {
        domConstruct.create(
          'option',
          {
            value: -1,
            innerHTML: lang.current('select_combo')
          },
          sel
        );
      }
      const prop = this[property];
      const value = prop || prop === 0 ? prop : -1;
      if (typeof arr === 'object' && !arr.length) {
        //si es un objeto se utiliza su llave como valor
        for (const key in arr) {
          if (!Object.hasOwn(arr, key)) {
            continue;
          }
          if (typeof arr[key] === 'string') {
            domConstruct.create(
              'option',
              {
                value: key,
                innerHTML: arr[key]
              },
              sel
            );
          } else {
            domConstruct.create(
              'option',
              {
                value: key,
                innerHTML: arr[key].label || 'Invalid label'
              },
              sel
            );
            if (typeof arr[key].action === 'function') {
              events[key] = arr[key].action;
              if (this[`${key}-entity-value`] || this[property] === key) {
                events[key]('refresh', sel);
              }
            }
          }
        }
      } else {
        //si es un arreglo es un arreglo de objetos con forma {value: 1, label: 'opcion 1'}
        if (arr?.length) {
          for (const item of arr) {
            domConstruct.create(
              'option',
              {
                value: item.value,
                innerHTML: item.label
              },
              sel
            );
          }
        }
      }

      _self[`on-change-${property}`] = function (evt) {
        _self[`on-before-change-${property}`] && _self[`on-before-change-${property}`](evt);
        this.setProp(property, sel.value);
        if (required) {
          if (sel) {
            if (+sel.value === -1) {
              domClass.add(sel, 'classic-yellow');
            } else {
              domClass.remove(sel, 'classic-yellow');
            }
            events[sel.value] && events[sel.value](evt, sel);
            isValidAlert({ 'classic-yellow': true, context: _self.id });
          } else {
            console.warn(`ERROR! la validacion de requeridos par el campo ${this.id} no esta correcta`);
          }
        } else {
          events[sel.value] && events[sel.value](evt, sel);
        }
        _self[`on-after-change-${property}`] && _self[`on-after-change-${property}`](evt, sel);
      };
      Event.observe(sel.id, 'change', this[`on-change-${property}`].bind(this), 0);
      sel.value = value;
      if (required) {
        //valido dato actual
        if (+sel.value === -1) {
          domClass.add(sel, 'classic-yellow');
        } else {
          domClass.remove(sel, 'classic-yellow');
          sel.style.backgroundColor = '';
        }
        if (propPositionManager.isVisible()) {
          isValidAlert({ 'classic-yellow': true, context: this.id });
        }
        domClass.add(sel, 'requiredProperty');
      }
      if (typeof onBeforeChange === 'function') {
        this[`on-before-change-${property}`] = onBeforeChange;
      }
      if (typeof onAfterChange === 'function') {
        this[`on-after-change-${property}`] = onAfterChange;
      }
      if (disabled) {
        domAttr.set(sel, 'disabled', true);
      }
      Element.show('propertiesArea');
      return fs;
    },
    getCheckeckMultiSelectWidgetId: function (property) {
      const id = `${this.id}_${property}multiSelect`;
      return id;
    },
    onChangeCheckedMultiSelectProperty: function (w, value) {
      if (!propPositionManager.isVisible()) {
        return;
      }

      if (w.required) {
        if (w) {
          if (value === null || typeof value === 'undefined' || value === '' || +value === -1 || +value === 0 || value.length === 0) {
            domClass.add(w.domNode, 'classic-yellow');
          } else {
            domClass.remove(w.domNode, 'classic-yellow');
          }
          isValidAlert({ 'classic-yellow': true, context: this.id });
        } else {
          console.warn(`ERROR! la validacion de requeridos par el campo ${this.id} no esta correcta`);
        }
      } else {
        domClass.remove(w.domNode, 'classic-yellow');
        isValidAlert({ 'classic-yellow': true, context: this.id });
      }
    },
    renderCheckedMultiSelectProperty: function (label1, property, items, required, shown) {
      let label = label1;
      const wId = this.getCheckeckMultiSelectWidgetId(property);
      let w = registry.byId(wId);
      if (w) {
        w.destroy();
      }
      const parent = $SV('propertiesArea');

      domConstruct.create('hr', { class: `${property}-hr-class` }, parent);
      if (required) {
        label = `${label}*`;
      }
      const fs = domConstruct.create(
        'div',
        {
          id: `propertyWrapper${wId}`,
          class: `propertyWrapper ${property}${shown ? '' : ' displayNone'}`,
          innerHTML: `<span>${label}</span>`
        },
        parent
      );
      const sw = domConstruct.create('span', { class: 'multi-select-widget-container' }, fs);
      const idValue = 'value';
      const searchAttr = 'label';
      const sortedList = new Memory({
        data: items,
        idProperty: idValue
      });
      w = new CheckedMultiSelect(
        {
          id: wId,
          labelAttr: searchAttr,
          searchAttr: searchAttr,
          required: required,
          multiple: true,
          sortByLabel: false, // Need this to override sort
          store: sortedList
        },
        wId
      );
      domAttr.set(w.domNode, 'is-required', required ? 'is-required' : 'not-required');
      w.placeAt(sw);
      const prop = this[property];
      const value = prop || [];
      this[`on-change-${property}`] = (evt) => {
        this[`on-before-change-${property}`] && this[`on-before-change-${property}`](evt);
        this.onChangeCheckedMultiSelectProperty(w, evt);
        this.setProp(property, evt);
        this[`on-after-change-${property}`] && this[`on-after-change-${property}`](evt, w);
      };
      if (required) {
        if (value === null || typeof value === 'undefined' || value === '' || +value === -1 || +value === 0 || value.length === 0) {
          domClass.add(w.domNode, 'classic-yellow');
        } else {
          domClass.remove(w.domNode, 'classic-yellow');
          w.domNode.style.backgroundColor = '';
        }
        domClass.add(w.domNode, 'requiredProperty');
      }
      w.on('change', this[`on-change-${property}`].bind(this));
      w.set('value', value);
      if (required) {
        this.onChangeCheckedMultiSelectProperty(w, value);
      }
      Element.show('propertiesArea');
      return fs;
    },
    getTextPropertyInputId: function (property) {
      return `${this.id}_${property}_input`;
    },
    renderTextProperty: function (label1, property, required, args1) {
      let label = label1;
      let args = args1;
      args = args || {};
      const parent = args.domContainer || $SV('propertiesArea');
      const _self = this;
      const className = args.className || '';
      domConstruct.create(
        'hr',
        {
          class: `${property}-hr-class ${className}-visible-toggle`
        },
        parent
      );
      if (required) {
        label = `${label}*`;
      }
      const fs = domConstruct.create(
        'div',
        {
          class: `propertyWrapper ${property}-container-class ${className}-visible-toggle`,
          innerHTML: `<span>${label}</span>`
        },
        parent
      );
      const ctn = domConstruct.create('span', {}, fs);
      const textInput = domConstruct.create(
        'input',
        {
          id: this.getTextPropertyInputId(property),
          placeholder: args.placeholder || lang.current('onlyNumbers'),
          class: `displayInlineBlock ${args.className || ''}`,
          maxlength: args.maxlength || 255,
          autocomplete: property,
          type: args.type || 'text',
          value: this[property] || '',
          'data-property-name': property,
          'is-required': required ? 'is-required' : 'not-required',
          min: args.min || '',
          max: args.max || ''
        },
        ctn
      );
      if (args.validationRegExp) {
        this[`validation-regexp-property-${property}`] = args.validationRegExp;
        domAttr.set(
          textInput,
          'validation-regexp-property',
          args.validationRegExp
            .toString()
            .substring(1)
            .replace(/g$|i$|\/$/g, '')
        );
      }
      const onChange = _self[`onChange${property.toUpperCase().substring(0, 1)}${property.substring(1)}`];
      _self[`on-change-${property}`] = function (evt) {
        if (textInput.value) {
          textInput.value = textInput.value.replace(args.invalidDataToRemoveRegExp || /[^0-9|^a-z]/gi, '');
        }
        _self[`on-before-change-${property}`] && _self[`on-before-change-${property}`](evt);
        this.setProp(property, textInput.value);
        if (required) {
          _self.isValidateTextInputAsRequired(property, textInput);
        }
        onChange?.apply(_self, evt);
      };
      _self[`on-focus-${property}`] = (evt) => {
        if (textInput.value) {
          textInput.select();
        }
      };
      const onKeyUpModified = _self[`onKeyUpModified${property.toUpperCase().substring(0, 1)}${property.substring(1)}`];
      if (typeof onKeyUpModified === 'function') {
        Event.observe(textInput.id, 'keyup', onKeyUpModified.bind(this), 0);
      }
      Event.observe(textInput.id, 'focus', this[`on-focus-${property}`].bind(this), 0);
      Event.observe(textInput.id, 'change', this[`on-change-${property}`].bind(this), 0);
      if (required) {
        //valido dato actual
        this.isValidateTextInputAsRequired(property, textInput);
      }
      Element.show('propertiesArea');
      return fs;
    },
    isValidateTextInputAsRequired: function (property, textInput) {
      if (!textInput) {
        console.warn(`ERROR! la validacion de requeridos par el campo ${this.id} no esta correcta`);
        return true;
      }
      const validationRegExp = this[`validation-regexp-property-${property}`];
      if (validationRegExp) {
        if (!textInput.value || textInput.value.replace(/\s/g, '').match(validationRegExp) === null) {
          domClass.add(textInput, 'classic-yellow');
        } else {
          domClass.remove(textInput, 'classic-yellow');
          textInput.style.backgroundColor = '';
        }
      } else {
        if (!textInput.value || textInput.value.replace(/\s/g, '').length === 0) {
          domClass.add(textInput, 'classic-yellow');
        } else {
          domClass.remove(textInput, 'classic-yellow');
          textInput.style.backgroundColor = '';
        }
      }
      if (!propPositionManager.isVisible()) {
        return;
      }
      const valid = isValidAlert({ 'classic-yellow': true, 'input-text': true, context: this.id });
      return valid;
    },
    renderSearchableProperty: function (parentDom, label, property, required, gridServiceStore, gridColumnsIdLabel, gridMethodName, parentProperty) {
      const parent = $SV('propertiesArea');

      const prop = this[property];
      const fs = domConstruct.create(
        'div',
        {
          class: `propertyWrapper ${property}-container-class ${parentProperty}-parent-class`
        },
        parentDom
      );
      const ctn = domConstruct.create('span', {}, fs);
      const propertyDisplay = domConstruct.create(
        'div',
        {
          id: `${this.id}-${property}-value`,
          'data-text': lang.current('formsCreateProperties'),
          title: lang.current('formsCreateProperties'),
          'data-property-name': property,
          class: 'Button addToGrid propertyDisplay'
        },
        ctn,
        'last'
      );

      parent.event(propertyDisplay, 'click', (evt) => {
        //propPositionManager.hideThis();
        _SearchableFieldGrid.setServiceStore(gridServiceStore);
        _SearchableFieldGrid.setMethodName(gridMethodName || 'getRows');
        const columns = [];
        gcUtil.column(columns).push(
          'push',
          lang.current('colNameChoose'),
          cType.RenderCell((row, nodeTd, nodeTr, nodeTable, column) => {
            const r = domConstruct.create('input', {
              name: property,
              type: 'radio',
              onclick: (evt) => {
                if (nodeTable.lastSelected) {
                  domClass.remove(nodeTable.lastSelected, 'tr-selected');
                }
                nodeTable.lastSelected = nodeTr;
                domClass.add(nodeTr, 'tr-selected');
                domUtilities.setValue(propertyDisplay, row.description || row[2]);
                domAttr.set(propertyDisplay, 'data-entity-value', row.id || row[0]);
                this[`on-change-${property}`].bind(this)();
              }
            });
            return r;
          }),
          null,
          '40px'
        );
        _SearchableFieldGrid.setTextColumns(gridColumnsIdLabel, columns);
        _SearchableFieldGrid.setField('');
        _SearchableFieldGrid.clearSearchWidgets();
        _SearchableFieldGrid.fillHeader();
        _SearchableFieldGrid.placedAtDialog.show();
        _SearchableFieldGrid.placedAtDialog.set('title', label);
        _SearchableFieldGrid.refreshSearch();
      });
      if (prop || prop === 0) {
        domUtilities.setValue(propertyDisplay, prop);
        domAttr.set(propertyDisplay, 'data-entity-value', this[`${property}-entity-value`]);
      } else {
        domUtilities.setValue(propertyDisplay, '');
        domAttr.set(propertyDisplay, 'data-entity-value', '');
      }
      if (required) {
        const propertyValue = domUtilities.getValue(propertyDisplay);
        if (propertyValue === null || +propertyValue === -1 || propertyValue === '') {
          domClass.add(propertyDisplay, 'classic-yellow');
        } else {
          domClass.remove(propertyDisplay, 'classic-yellow');
          propertyDisplay.style.backgroundColor = '';
        }
        domClass.add(propertyDisplay, 'requiredProperty');
        this[`on-change-${property}`] = () => {
          const currentValue = domUtilities.getValue(propertyDisplay);
          this.setProp(property, currentValue);
          domAttr.get(propertyDisplay, 'data-entity-value') && this.setProp(`${property}-entity-value`, domAttr.get(propertyDisplay, 'data-entity-value'));
          if (currentValue === null || +currentValue === -1 || currentValue === '') {
            domClass.add(propertyDisplay, 'classic-yellow');
          } else {
            domClass.remove(propertyDisplay, 'classic-yellow');
            propertyDisplay.style.backgroundColor = '';
          }
          isValidAlert({ 'classic-yellow': true, context: this.id });
        };
      } else {
        this[`on-change-${property}`] = () => {
          this.setProp(property, domUtilities.getValue(propertyDisplay));
          domAttr.get(propertyDisplay, 'data-entity-value') && this.setProp(`${property}-entity-value`, domAttr.get(propertyDisplay, 'data-entity-value'));
        };
      }
      Event.observe(propertyDisplay.id, 'change', this[`on-change-${property}`].bind(this), 0);
      Element.show('propertiesArea');
      return fs;
    },
    renderActivityTemplateEditor(label, property, shown) {
      const parent = $SV('propertiesArea');
      const nameId = `${dojoLang.clone(property)}_`;
      const fs = $fieldset(
        {
          class: `propertyWrapper ${shown ? '' : 'displayNone'}`,
          id: nameId
        },
        $legend(label)
      );
      parent.appendChild(fs);
      const ctn = $div();
      fs.appendChild(ctn);
      ctn.appendChild(
        $input({
          id: property,
          type: 'button',
          class: 'button red',
          value: lang.current('configure')
        })
      );
      Event.observe(
        property,
        'click',
        (() => {
          this.openAngularActivityTemplateEditor(property);
        }).bind(this),
        0
      );
    },
    openAngularActivityTemplateEditor(property) {
      require(['bnext/angularActivity'], (angularActivity) => {
        const module = SurveyCaptureUtils.getModule();
        const currentValue = this[property];
        angularActivity
          .dialogNewActivity({
            module: module,
            activityQuickTemplateId: currentValue,
            newActivityMode: 'QUICK_SYSTEM_TEMPLATE_EDITOR'
          })
          .then(
            (result) => {
              this.setProp(property, result);
            },
            () => {
              domUtilities.setValue(property, null);
              this.setProp(property, null);
            }
          );
      });
    },
    renderImageProperty: function () {
      const parent = $SV('propertiesArea');
      const fs = $fieldset(
        {
          class: 'propertyWrapper'
        },
        $legend(lang.current('image'))
      );
      parent.appendChild(fs);
      const ctn = $div();
      fs.appendChild(ctn);
      ctn.appendChild(
        $img({
          src: '/images/picture.png'
        })
      );
      if (this.image) {
        ctn.appendChild(
          $input({
            id: 'field_image_remove',
            type: 'button',
            class: 'button red',
            value: lang.current('remove')
          })
        );
        Event.observe(
          'field_image_remove',
          'click',
          function () {
            this.removeImage();
          }.bind(this),
          0
        );
      } else {
        ctn.appendChild(
          $input({
            id: 'field_image_upload',
            type: 'submit',
            class: 'button green',
            value: 'upload',
            onclick: function () {
              page.showUploadImage(page.form.survey_id, this.id);
            }
          })
        );
      }
    },
    removeImage: function () {
      this.image = 0;
      this.refresh();
    },
    doesntShowsNumber: (element) => {
      try {
        return element.itemStashParent || !(element.type in fieldRulesDefinition.countableFields);
      } catch (e) {
        console.error('Error en doesntShowsNumber', e);
        return false;
      }
    },
    getCurentSectionId: function () {
      const currentTargetId = this.parentId;
      const formItems = page.form.formItems;
      let lastSectionId = null;
      for (const field of formItems) {
        const fieldId = field.id;
        if (fieldId === currentTargetId) {
          break;
        }
        if (field.type === 'seccion') {
          lastSectionId = fieldId;
        }
      }
      return lastSectionId;
    },
    getHasOnlyConditionalFieldsCurrentSeccion: function () {
      const sectionId = this.getCurentSectionId();
      const currentTargetId = this.parentId;
      const formItems = page.form.formItems;
      let countSection = 0;
      let startCount = false;
      for (const field of formItems) {
        const fieldId = field.id;
        if (fieldId === currentTargetId) {
          break;
        }
        if (fieldId === sectionId) {
          startCount = true;
          continue;
        }
        if (startCount) {
          countSection++;
          break;
        }
      }
      return countSection === 0;
    },
    getAllQuestionNumber: function () {
      let index = 1;
      const data = new Map();
      const formItems = page.form.formItems;
      for (const formItem of formItems) {
        data.set(formItem.id, index);
        if (!this.doesntShowsNumber(formItem)) {
          index++;
        }
      }
      return data;
    },
    iSupportedConditionalField: (parent, supportedConditionalFields) => {
      if (!parent) {
        return false;
      }
      const fieldType = parent.type;
      return supportedConditionalFields.indexOf(fieldType) !== -1;
    },
    getConfigurableAnswers: function (supportedConditionalFields) {
      const available = this.isAvailableConditionalFields();
      if (!available) {
        return [];
      }
      const currentTargetId = this.parentId;
      const answers = [];
      const formItems = page.form.formItems;
      if (formItems.length === 0) {
        return [];
      }
      const indexData = this.getAllQuestionNumber();
      for (const formField of formItems) {
        const fieldId = formField.id;
        if (fieldId === currentTargetId) {
          break;
        }
        const skip = !this.iSupportedConditionalField(formField, supportedConditionalFields);
        if (skip) {
          continue;
        }
        const fieldType = formField.type;
        if (fieldType === 'catalogSelect') {
          this._getCatalogSelectAnswers(answers, indexData, formField);
        } else if (fieldType === 'textFieldArray') {
          this._getTextFieldArray(answers, indexData, formField, true);
        } else if (fieldType === 'textField') {
          this._getTextFieldAnswer(answers, indexData, formField, true);
        } else {
          const field = dom.byId(fieldId);
          for (const answer of query('input[type=text]', field)) {
            const answerCode = domAttr.get(answer, 'data-item-code');
            const fieldNumber = indexData.get(fieldId) || '';
            const fieldText = `${fieldNumber}. ${formField.uiObject.getTitleText()}`;
            answers.push({
              id: answer.id,
              conditionalType: ConditionalType.SURVEY_ITEM,
              code: fieldText,
              conditionalQuestionCode: fieldId,
              conditionalAnswerCode: answerCode,
              description: answer.value
            });
          }
        }
      }
      return answers;
    },
    getConditionalExpirationDays: (conditional) => {
      const temp = {};
      for (const e of conditional) {
        if (e.conditionalAnswerSelector) {
          temp[e.conditionalAnswerSelector] = {
            daysToExpire: e.daysToExpire,
            daysToNotifyBeforeExpiration: e.daysToNotifyBeforeExpiration
          };
        }
      }
      return temp;
    },
    getConditionalLimits: (conditional) => {
      const temp = {};
      for (const e of conditional) {
        if (e.conditionalAnswerSelector) {
          temp[e.conditionalAnswerSelector] = {
            maxLim: e.maxLim,
            minLim: e.minLim,
            eqLim: e.eqLim
          };
        }
      }
      return temp;
    },
    getConditionalFieldWrapperId: function (wrapperId) {
      return this.parentId + wrapperId;
    },
    showConditionalField: function (conditionalProperty) {
      const fsAnswer = dom.byId(this.getConditionalFieldWrapperId(conditionalProperty.wrapperId));
      const conditional = conditionalProperty.conditional;
      if (fsAnswer) {
        const answers = this.getConfigurableAnswers(conditionalProperty.supportedConditionalFields);
        conditionalProperty.updateConditionalData(answers, this[conditional.idsObjName], this[conditional.hierarchyDataObjName]);
        domStyle.set(fsAnswer, 'display', '');
        const valueData = conditionalProperty.get('valueData');
        if (valueData && valueData.length > 0) {
          PropertiesDiv(this.parentId).style.display = 'none';
        } else {
          PropertiesDiv(this.parentId).style.display = '';
        }
      }
      const contentDiv = $SV(this.id);
      domClass.add(contentDiv, 'conditional-question');
    },
    disableConditionalFields: function (conditionalProperty) {
      const conditional = conditionalProperty.conditional;
      this[conditional.question] = conditional.questionType && conditional.questionType === 'number' ? '0' : 'f';
      const parent = $SV('propertiesArea');
      const options = query('input[name=conditionalQuestion_Field]', parent);
      for (const option of options) {
        if (option.id === 'conditionalQuestion_Fieldt') {
          option.checked = false;
        } else if (option.id === 'conditionalQuestion_Fieldf') {
          option.checked = true;
        }
        domAttr.set(option, 'disabled', 'disabled');
      }
      this.onChangeConditionalQuestion(null, conditionalProperty.conditional.question, this[conditional.question]);
    },
    disableSummaryFields: function (parent) {
      this.summationQuestion = 'f';
      this.configureShowSummationField = true;
      const options = query('input[data-field-name=summationQuestion_]', parent);
      for (const option of options) {
        if (option.getAttribute('data-field-value') === 't') {
          option.checked = false;
        } else if (option.getAttribute('data-field-value') === 'f') {
          option.checked = true;
        }
        domAttr.set(option, 'disabled', 'disabled');
      }
      this.onChangeSummationQuestion(null, 'summationQuestion', 'f');
    },
    hideConditionalFields: function (conditionalFieldWrapperId) {
      const fsAnswer = dom.byId(conditionalFieldWrapperId);
      const propertiesPanelIsOpen = this.isPropertiesPanelOpen();
      if (fsAnswer && propertiesPanelIsOpen) {
        domStyle.set(fsAnswer, 'display', 'none');
        isValidAlert({ 'classic-yellow': true, context: this.id });
      }
    },
    isPropertiesPanelOpen: () => dom.byId('sidebar') !== null && dom.byId('sidebar').getStyle('display') === 'block',
    isAvailableConditionalFields: function () {
      const emptyCurrentSeccion = this.getHasOnlyConditionalFieldsCurrentSeccion();
      if (emptyCurrentSeccion) {
        return false;
      }
      return true;
    },
    _getHierarchyConditionalData: (conditionals) => {
      if (typeof conditionals === 'undefined' || conditionals === null || conditionals.length === 0) {
        return {};
      }
      const hierarchyItems = conditionals.filter((item) => item.conditionalType === ConditionalType.HIERARCHY_VALUE);
      const data = {};
      if (!hierarchyItems.length) {
        return data;
      }
      for (const item of hierarchyItems) {
        if (!data[item.conditionalAnswerSelector]) {
          data[item.conditionalAnswerSelector] = [];
        }
        if (!data[item.conditionalAnswerSelector][item.hierarchyRow]) {
          data[item.conditionalAnswerSelector][item.hierarchyRow] = [];
        }
        item.catalogHierarchyColumn = item.hierarchyColumn;
        item.catalogOptionLabel = item.hierarchyValue;
        item.catalogOptionValue = item.hierarchyValue;
        data[item.conditionalAnswerSelector][item.hierarchyRow].push(item);
      }
      return data;
    },
    getTitleText() {
      return this.title.getCurrentValue() || dom.byId(`${this.id}_title`).value || dom.byId(`${this.id}_title`).innerText;
    },
    _getCatalogSelectAnswers: function (answers, indexData, field) {
      const catalogSubType = field.uiObject?.catalogSubType;
      if (catalogSubType !== 'catalog-hierarchy') {
        return;
      }
      if (!field.uiObject?.hierarchyLabelsDom?.length) {
        return;
      }
      const externalCatalogId = field.uiObject.externalCatalogId;
      const fieldId = field.id;
      const fieldNumber = indexData.get(fieldId) || '';
      const fieldText = `${fieldNumber}. ${field.uiObject.getTitleText()}`;
      const fields = [];
      for (const levelNode of field.uiObject.hierarchyLabelsDom) {
        const dataHierarchyColumn = domAttr.get(levelNode, 'data-hierarchy-column');
        const dataHierarchyLevel = +domAttr.get(levelNode, 'data-hierarchy-level');
        const label = levelNode.innerText;
        fields.push({
          externalCatalogId: externalCatalogId,
          order: dataHierarchyLevel,
          level: dataHierarchyLevel,
          readonly: 0,
          visible: 1,
          column: dataHierarchyColumn,
          label: label,
          catalogContents: {
            data: [],
            count: 500
          }
        });
        const rowId = `${fieldId}_${dataHierarchyColumn}`;
        answers.push({
          id: rowId,
          conditionalType: ConditionalType.HIERARCHY_VALUE,
          code: `${fieldText} - ${label}`,
          conditionalQuestionCode: fieldId,
          conditionalAnswerCode: null,
          description: null,
          hierarchyRow: 0,
          hierarchyLevel: dataHierarchyLevel,
          hierarchyColumn: null,
          hierarchyValue: null
        });
        this.hierarchyLevelFields[rowId] = [...fields];
      }
    },
    _getTextFieldArray: function (answers, indexData, formField, disableCurrency) {
      const field = dom.byId(formField.id);
      const fieldId = field.id;

      for (const row of query('.html-array-item', field)) {
        const rowIndex = domAttr.get(row, 'data-row');
        const rowNodesIdPrefix = `${fieldId}_uiObject_array_${rowIndex}`;

        // Nodos del renglón
        const answerTitleNode = dom.byId(`${rowNodesIdPrefix}_title`);
        const answerColTypeNode = dom.byId(`${rowNodesIdPrefix}_col_type`);

        if (!answerTitleNode || !answerColTypeNode) {
          console.error('[NO FUNCIONAN SUMATORIAS]: Nodos de "titulo", "valor" y "tipo de dato" del campo no encontrados.', row);
          continue;
        }
        const answerType = +answerColTypeNode.value;
        if (
          answerType === SurveyFieldAnswerType.NUMERIC ||
          answerType === SurveyFieldAnswerType.DECIMAL ||
          (answerType === SurveyFieldAnswerType.CURRENCY_USD_TO_MXN && !disableCurrency)
        ) {
          const answerCode = domAttr.get(row, 'data-item-code');
          const fieldNumber = indexData.get(fieldId) || '';
          const fieldText = `${fieldNumber}. ${formField.uiObject.getTitleText()}`;
          const answerTypeName = this._getSurveyFieldAnswerTypeName(answerType);
          answers.push({
            id: row.id,
            conditionalType: ConditionalType.SURVEY_ITEM,
            code: fieldText,
            conditionalQuestionCode: fieldId,
            conditionalAnswerCode: answerCode,
            description: `${answerTitleNode.innerText} / ${answerTypeName}`,
            type: 'textFieldArray'
          });
        }
      }
    },
    _getSurveyFieldAnswerTypeName: (answerType) => {
      if (answerType === SurveyFieldAnswerType.NUMERIC) {
        return lang.current('numerical');
      }
      if (answerType === SurveyFieldAnswerType.DECIMAL) {
        return lang.current('decimal');
      }
      if (answerType === SurveyFieldAnswerType.CURRENCY_USD_TO_MXN) {
        return lang.current('currencyConversionUsdToMxn');
      }
    },
    _getMatrixRowAndColumnName: (formItem) => {
      // Se obtiene el ID del campo matriz
      const stashParentId = formItem.itemStashParent?.parentId || null; // Ej. "field1"
      const matrixCellIds = Object.keys(formItem.itemStashParent.itemStash).filter((key) => {
        return formItem.itemStashParent.itemStash[key].id === formItem.id;
      });
      const matrixCellId = matrixCellIds.length > 0 ? matrixCellIds[0] : null;
      if (!matrixCellIds) {
        return;
      }
      // Se obtiene el INDICE de la fila y columna
      const rowIdx = matrixCellId.replace(/field[0-9]+_uiObject_array_([0-9]+)_[0-9]+_cell/, '$1');
      const columnIdx = matrixCellId.replace(/field[0-9]+_uiObject_array_[0-9]+_([0-9]+)_cell/, '$1');
      // Se obtiene cada titulo
      const rowTitle = dom.byId(`${stashParentId}_uiObject_array_${rowIdx}_title`).innerText;
      const columnTitle = dom.byId(`${stashParentId}_uiObject_items_header_col${columnIdx}`).value;
      return `${rowTitle} / ${columnTitle}`;
    },
    _getTextFieldAnswer: function (answers, indexData, formField, disableCurrency) {
      const fieldId = formField.id;
      const uiObject = formField?.uiObject || {};
      const answerType = uiObject.answerType;
      const hidden = typeof uiObject.hidden === 'string' ? uiObject.hidden === 'true' : uiObject.hidden;
      if (
        !hidden &&
        (answerType === SurveyFieldAnswerType.NUMERIC ||
          answerType === SurveyFieldAnswerType.DECIMAL ||
          (answerType === SurveyFieldAnswerType.CURRENCY_USD_TO_MXN && !disableCurrency))
      ) {
        const fieldNumber = indexData.get(fieldId) || '';
        // Se revisa si el campo está dentro de una matriz de campos para colocar el titulo correcto
        const stashParentId = formField.itemStashParent?.parentId || null; // Ej. "field1"
        const isMatrixChild = stashParentId && query(`.subContainer > #${fieldId}`).length > 0;
        // Se obtiene el titulo del campo según sea un campo normal o un campo dentro de una matriz
        let fieldText;
        if (isMatrixChild) {
          fieldText = `${fieldNumber}. ${this._getMatrixRowAndColumnName(formField)}`;
        } else {
          fieldText = `${fieldNumber}. ${formField.uiObject.getTitleText()}`;
        }
        const answerTypeName = this._getSurveyFieldAnswerTypeName(answerType);
        answers.push({
          id: `${fieldId}_uiObject`,
          conditionalType: ConditionalType.FIELD,
          code: fieldText,
          conditionalQuestionCode: fieldId,
          description: answerTypeName,
          type: 'textField'
        });
      }
    },
    _renderCellAddDescriptionConditionalCell: (row) => {
      if (row.conditionalType === ConditionalType.HIERARCHY_VALUE) {
        const span = domConstruct.create('span', {
          class: 'hierarchyLevelAnswerToSelect',
          innerHTML: lang.current('hierarchyLevelAnswerToSelect')
        });
        return span;
      }
      return row.description;
    },
    _onChangeHierarchyLevelAnswers: function (val, rowId, conditional, property) {
      let value = val;
      if (value && value.length > 0) {
        value = value.filter((item) => !!item);
      } else {
        value = [];
      }
      this[conditional.hierarchyDataObjName][rowId] = value;
      conditional._self.updateConditionalData(this[conditional.answerValuesObjName], this[conditional.idsObjName], this[conditional.hierarchyDataObjName]);
      conditional._self.validateConditionalData();
    },
    _onClickRenderBaseAnswer: function (rowId, conditional) {
      const levels = this.hierarchyLevelFields[rowId];
      const answerSelectorId = `${this.parentId}_${rowId}`;
      page.form.validateOnlyOneAnswerSelector(answerSelectorId);
      let answerSelector = registry.byId(answerSelectorId);
      page.form.changeAnswerSelector(answerSelectorId);
      if (answerSelector) {
        answerSelector.destroyRecursive();
      }
      const parent = $SV('propertiesArea');
      const answerSelectorContainer = $div();
      parent.appendChild(answerSelectorContainer);
      answerSelector = new HierarchyLevelProperty(
        {
          id: answerSelectorId,
          columns: levels,
          label: lang.current('hierarchyLevelAnswerAddDialogTitle'),
          value: this[conditional.hierarchyDataObjName][rowId] || [],
          nullDefaultText: lang.current('hierarchyLevelAnswerNoRegMessage')
        },
        answerSelectorContainer
      );
      answerSelector.on(
        'change',
        dojoLang.hitch(this, function (value) {
          this._onChangeHierarchyLevelAnswers(value, rowId, conditional);
        })
      );
      answerSelector.show();
    },
    _renderBaseDescriptionMissingButton: function (rowId, conditional) {
      const button = domConstruct.create('button', {
        class: 'button ripple',
        type: 'button',
        onclick: dojoLang.hitch(this, function () {
          this._onClickRenderBaseAnswer(rowId, conditional);
        }),
        innerHTML: lang.current('hierarchyLevelAnswerSelect')
      });
      return button;
    },
    _renderBaseDescriptionMissinInput: function (rowId, conditional, property, rowType) {
      const addAvail = conditional._self.additionalInfoFieldAvailability;
      if (addAvail && Object.keys(addAvail).length > 0 && addAvail[property]) {
        if (!rowType) {
          return;
        }
        const available = addAvail[property].find((type) => type === rowType);
        if (!available) {
          return;
        }
      }
      let val = null;
      if (conditional._self.additionalInfo?.[property]) {
        const objProperty = this[conditional._self.additionalInfo[property]];
        if (objProperty && objProperty[rowId] && objProperty[rowId][property] !== null && typeof objProperty[rowId][property] !== 'undefined') {
          val = objProperty[rowId][property];
        }
      }
      const input = domConstruct.create('input', {
        type: 'number',
        value: val,
        onchange: dojoLang.hitch(this, function (event) {
          const input = event.currentTarget;
          if (conditional._self.additionalInfo?.[property]) {
            const objProperty = this[conditional._self.additionalInfo[property]];
            if (!objProperty[rowId]) {
              objProperty[rowId] = {};
            }
            objProperty[rowId][property] = input.value === '' ? null : input.value;
          }
        })
      });
      return input;
    },
    _renderBaseDescriptionConditionalCell: function (row, nodeTd, nodeTr, nodeTable, column) {
      const rowId = row.id;
      const conditional = row.conditional;
      if (row.conditionalType === ConditionalType.HIERARCHY_VALUE) {
        const hierarchyData = this[conditional.hierarchyDataObjName];
        const conditionalData = hierarchyData[rowId];
        if (conditionalData && conditionalData.length > 0) {
          const container = domConstruct.create('div');
          if (conditionalData.length === 1) {
            const items = conditionalData[0];
            const lastField = items[items.length - 1];
            const labelLink = lastField != null ? lastField.catalogOptionValue : null;
            if (labelLink === '' || labelLink === null || typeof labelLink === 'undefined') {
              return this.renderColumnElement(column, row.id, conditional);
            }
            if (!this.hasExpirationDay(column)) {
              domConstruct.create(
                'a',
                {
                  innerHTML: labelLink,
                  href: 'javascript:void(0);',
                  onclick: dojoLang.hitch(this, function () {
                    this._onClickRenderBaseAnswer(rowId, conditional);
                  })
                },
                container
              );
            } else {
              return this.renderColumnElement(column, row.id, conditional);
            }
          } else {
            if (!this.hasExpirationDay(column)) {
              domConstruct.create(
                'a',
                {
                  innerHTML: `${lang.current('hierarchyLeveMultipleAnswers')}(${conditionalData.length})`,
                  href: 'javascript:void(0);',
                  onclick: dojoLang.hitch(this, function () {
                    this._onClickRenderBaseAnswer(rowId, conditional);
                  })
                },
                container
              );
            } else {
              return this.renderColumnElement(column, row.id, conditional);
            }
          }
          return container;
        }
        return this.renderColumnElement(column, row.id, conditional);
      }
      if (column && !!column.renderMissingInput) {
        const input = this._renderBaseDescriptionMissinInput(row.id, conditional, column.id, row.type);
        return input;
      }
      return row.description;
    },
    hasExpirationDay: (column) => column && !!column.renderMissingInput,
    renderColumnElement: function (column, rowId, conditional) {
      if (column && column && !!column.renderMissingInput) {
        const input = this._renderBaseDescriptionMissinInput(rowId, conditional, column.id);
        return input;
      }
      const button = this._renderBaseDescriptionMissingButton(rowId, conditional);
      return button;
    },
    renderConditionalField: function (config) {
      const conditionalProperty = config.conditionalProperty;
      const conditionalConfig = conditionalProperty.conditional;
      const label = lang.current(config.label);
      const compareWith = this.getCompareConditionalWith(conditionalProperty, true);
      const fs = $fieldset(
        {
          id: this.getConditionalFieldWrapperId(conditionalProperty.wrapperId),
          class: 'propertyWrapper',
          style: {
            display: `${this[config.conditionalQuestion]}` === compareWith ? '' : 'none'
          }
        },
        $legend(label)
      );
      const parent = $SV('propertiesArea');
      parent.appendChild(fs);
      conditionalProperty.setConfig({
        configuredFieldName: config.fieldName,
        fieldId: this.parentId,
        renderCellAddDescription: dojoLang.hitch(this, this._renderCellAddDescriptionConditionalCell),
        renderCellBaseDescription: dojoLang.hitch(this, this._renderBaseDescriptionConditionalCell)
      });
      conditionalProperty.placeAt(fs);
      this[conditionalConfig.answerValuesObjName] = this.getConfigurableAnswers(conditionalProperty.supportedConditionalFields);
      if (!this[conditionalConfig.answerValuesObjName].length) {
        this.disableConditionalFields(conditionalProperty);
      }
      const items = this[conditionalConfig.answerValuesObjName];
      const selected = this[conditionalConfig.idsObjName];
      const hierarchyConditionalData = this[conditionalConfig.hierarchyDataObjName];
      conditionalProperty.updateConditionalData(items, selected, hierarchyConditionalData);
    },
    getConditionalItems: function (conditionalProperty) {
      const available = this.isAvailableConditionalFields();
      if (!available) {
        return null;
      }
      const answers = this.getConfigurableAnswers(conditionalProperty.supportedConditionalFields);
      const gridValue = conditionalProperty.syncConditionalData(
        answers,
        this[conditionalProperty.conditional.idsObjName],
        this[conditionalProperty.conditional.hierarchyDataObjName]
      );
      let items = gridValue.valueData;
      if (items && items.length > 0) {
        // Se separan los campo de JERARQUIA del RESTO
        const surveyItems = items.filter((item) => item.conditionalType === ConditionalType.SURVEY_ITEM || item.conditionalType === ConditionalType.FIELD);
        const hierarchyItems = items.filter((item) => item.conditionalType === ConditionalType.HIERARCHY_VALUE);

        // los campos de jerarquía recibe
        if (hierarchyItems?.length > 0) {
          for (const item of hierarchyItems) {
            const fieldRows = this[conditionalProperty.conditional.hierarchyDataObjName][item.id] || [];
            if (fieldRows?.length <= 0) {
              continue;
            }
            for (let i = 0, l = fieldRows.length; i < l; i++) {
              const columnRows = fieldRows[i];
              if (!columnRows?.length) {
                continue;
              }
              for (const columnRow of columnRows) {
                const newItem = { ...item };
                newItem.hierarchyRow = i;
                newItem.hierarchyValue = columnRow.catalogOptionValue;
                newItem.hierarchyColumn = columnRow.catalogHierarchyColumn;
                surveyItems.push(newItem);
              }
            }
          }
        }
        // Adding additional info based on ID
        const objNames = {}; // To prevent update same property multiple times.
        if (conditionalProperty.additionalInfo) {
          for (const info in conditionalProperty.additionalInfo) {
            const objName = conditionalProperty.additionalInfo[info];
            const additionalInfo = this[objName];
            if (additionalInfo && !objNames[objName]) {
              for (const e of surveyItems) {
                if (e.id) {
                  if (e.id in additionalInfo) {
                    Object.assign(e, additionalInfo[e.id]);
                  }
                }
              }
            }
            objNames[objName] = true;
          }
        }
        items = surveyItems;
      }
      return items;
    },
    getConditionalsIds: (obj) => {
      if (typeof obj === 'undefined' || obj === null || obj.length === 0) {
        return [];
      }
      const data = new Set();
      if (!obj.length) {
        return [];
      }
      for (const item of obj) {
        data.add(item.conditionalAnswerSelector);
      }
      return [...data];
    },
    onChangePivotTableOnMobile: (evt, property, value) => {},
    onChangeConditionalQuestion: function (evt, prop, val) {
      let property = prop;
      let value = val;
      value = value ? value : evt.target.value;
      if (property.localName && property.localName === 'select') {
        // Comes from a select, need to find the corrrect ConditionalProperty
        const type = property.getAttribute('data-property-name');
        property = this.getPropertyType(type);
      } else {
        property = this.getPropertyType(property);
      }
      const compareWith = this.getCompareConditionalWith(property, true);
      if (value === compareWith) {
        this.showConditionalField(property);
        this.onChangeHidden(evt, property.conditional.question, 't');
      } else {
        this.hideConditionalFields(this.getConditionalFieldWrapperId(property.wrapperId));
        const contentDiv = $SV(this.id);
        domClass.remove(contentDiv, 'conditional-question');
        this.onChangeHidden(evt, property.conditional.question, 'f');
      }
    },
    getCompareConditionalWith: (conditionalProperty, type) => {
      const conditional = conditionalProperty.conditional;
      if (type) {
        let compareWith = 't';
        if (conditional.questionType && conditional.questionType === 'number') {
          compareWith = '1';
        }
        return compareWith;
      }
      let compareWith = 'f';
      if (conditional.questionType && conditional.questionType === 'number') {
        compareWith = '0';
      }
      return compareWith;
    },
    getPropertyType: (type) => {
      switch (type) {
        case ConditionalFieldProperty.conditional.question:
          return ConditionalFieldProperty;
        case ConditionalExpirationFieldProperty.conditional.question:
          return ConditionalExpirationFieldProperty;
        default:
          return null;
      }
    },
    showProperties: function () {
      page.form.destroyCurrentAnswerSelector();
      this.customProperties.defineProperties();
    },
    hideProperties: () => {
      page.form.destroyCurrentAnswerSelector();
      ConditionalFieldProperty.hide();
    },
    hideProperty: (property) => {
      const nameId = `${dojoLang.clone(property)}_`;
      const elem = dom.byId(nameId);
      if (elem) {
        domStyle.set(elem, 'display', 'none');
      }
    },
    showProperty: (property) => {
      const nameId = `${dojoLang.clone(property)}_`;
      const elem = dom.byId(nameId);
      if (elem) {
        domStyle.set(elem, 'display', '');
      }
    },
    // SUMMATION FUNCTION
    onChangeSummationQuestion: function (evt, property, value) {
      if (value === 't') {
        this.showSummationField();
        this.showShowSummationField();
        this.onChangeHidden(evt, property, 't');
      } else {
        this.hideConditionalFields(this.getSummationFieldWrapperId());
        this.hideShowSummationField();
        const contentDiv = $SV(this.id);
        domClass.remove(contentDiv, 'summation-question');
        this.onChangeHidden(evt, property, 'f');
      }
    },
    onRenderConfigureShowSummationField: (evt, property, value) => {
      console.log(evt);
      console.log(property);
      console.log(value);
    },
    getSummationItems: function () {
      const answers = this.getConfigurableSummationAnswers();
      const gridValue = SummationProperty.syncSummationData(answers, this.summationIds, {});
      let items = gridValue.valueData;
      if (items && items.length > 0) {
        const surveyItems = items.filter((item) => item.conditionalType === ConditionalType.SURVEY_ITEM || item.conditionalType === ConditionalType.FIELD);
        items = surveyItems;
      }
      return items;
    },
    getSummationFieldWrapperId: function () {
      return `${this.parentId}_summation_propertyWrapper`;
    },
    getConfigurableSummationAnswers: function () {
      const currentTargetId = this.parentId;
      const answers = [];
      const formItems = page.form.formItems;
      if (formItems.length === 0) {
        return [];
      }
      const indexData = this.getAllQuestionNumber();
      for (const formField of formItems) {
        const fieldId = formField.id;
        if (fieldId === currentTargetId) {
          break;
        }
        const fieldType = formField.type;
        if (fieldType === 'textFieldArray') {
          this._getTextFieldArray(answers, indexData, formField);
        }
        if (fieldType === 'textField') {
          this._getTextFieldAnswer(answers, indexData, formField);
        }
      }
      return answers;
    },
    showSummationField: function () {
      const fsAnswer = dom.byId(this.getSummationFieldWrapperId());
      if (fsAnswer) {
        const answers = this.getConfigurableSummationAnswers();
        SummationProperty.updateSummationData(answers, this.summationIds, {});
        domStyle.set(fsAnswer, 'display', '');
        if (this.summationIds.length > 0) {
          PropertiesDiv(this.parentId).style.display = 'none';
        } else {
          PropertiesDiv(this.parentId).style.display = '';
        }
      }
      const contentDiv = $SV(this.id);
      domClass.add(contentDiv, 'summation-question');
    },
    showShowSummationField: () => {
      const element = dom.byId('configureShowSummationField_');
      if (element) {
        domStyle.set(element, 'display', '');
      }
    },
    hideShowSummationField: () => {
      const element = dom.byId('configureShowSummationField_');
      if (element) {
        domStyle.set(element, 'display', 'none');
        const check = query('#configureShowSummationField_Fieldfalse[data-field-value="false"]', element);
        for (const item of check) {
          item.checked = true;
          this.configureShowSummationField = true;
        }
      }
    },
    renderSummationField: function () {
      const label = lang.current('summationAnswerSelector');
      const fs = $fieldset(
        {
          id: this.getSummationFieldWrapperId(),
          class: 'propertyWrapper',
          style: {
            display: this.summationQuestion === 't' ? '' : 'none'
          }
        },
        $legend(label)
      );
      const parent = $SV('propertiesArea');
      parent.appendChild(fs);
      SummationProperty.setConfig({
        configuredFieldName: 'summationAnswerSelector',
        fieldId: this.parentId
      });
      SummationProperty.placeAt(fs);
      this.summationAnswerValues = this.getConfigurableSummationAnswers();
      if (this.summationAnswerValues === null || this.summationAnswerValues.length === 0) {
        this.disableSummaryFields(parent);
      }
      SummationProperty.updateSummationData(this.summationAnswerValues, this.summationIds, {});
    },
    renderConfigureShowSummationField: function () {
      this.renderProperty(
        lang.current('configureShowSummationField'),
        'configureShowSummationField',
        {
          true: lang.current('yes'),
          false: lang.current('no')
        },
        null,
        this.onRenderConfigureShowSummationField,
        null,
        {
          display: this.summationQuestion === 't' ? '' : 'none'
        }
      );
    },
    onActivitiesTemplateChange: function (evt, property, value) {
      const cancelStatusesContainer = dom.byId('activitiesTemplateId_');
      if (+value === 1) {
        cancelStatusesContainer && domClass.remove(cancelStatusesContainer, 'displayNone');
      } else {
        if (cancelStatusesContainer) {
          domClass.add(cancelStatusesContainer, 'displayNone');
          this.activitiesTemplateId = null;
        }
      }
    },
    setHiddenDisabled: (disabled) => {
      const isRequiredFalseRadio = dom.byId('required_Fieldf');
      const isConditionalFalseRadio = dom.byId('conditionalQuestion_Fieldf');
      const isSummationFalseRadio = dom.byId('summationQuestion_Fieldf');
      if (disabled) {
        isRequiredFalseRadio?.click();
        isConditionalFalseRadio?.click();
        isSummationFalseRadio?.click();
      }
      if (isRequiredFalseRadio) {
        isRequiredFalseRadio.disabled = disabled;
      }
      const isRequiredTrueRadio = dom.byId('required_Fieldt');
      if (isRequiredTrueRadio) {
        isRequiredTrueRadio.disabled = disabled;
      }
      if (isConditionalFalseRadio) {
        isConditionalFalseRadio.disabled = disabled;
      }
      const isConditionalTrueRadio = dom.byId('conditionalQuestion_Fieldt');
      if (isConditionalTrueRadio) {
        isConditionalTrueRadio.disabled = disabled;
      }
      if (isSummationFalseRadio) {
        isSummationFalseRadio.disabled = disabled;
      }
      const isSummationTrueRadio = dom.byId('summationQuestion_Fieldt');
      if (isSummationTrueRadio) {
        isSummationTrueRadio.disabled = disabled;
      }
    },
    onChangeHidden: function (evt, property, value) {
      const isHidden = typeof value === 'string' ? value === 'true' ||  value === 't' : !!value;
      const radioCheckFalse = dom.byId('hidden_Fieldfalse') || { click: core.$noop };
      const radioCheckTrue = dom.byId('hidden_Fieldtrue') || { click: core.$noop };
      switch (property) {
        default:
        case 'required':
        case 'conditionalQuestion':
        case 'summationQuestion':
        case 'selectedWeightedField':
          if (isHidden) {
            radioCheckFalse.checked = true;
            this.setHiddenDisabled(false);
          }
          return;
        case 'hidden':
          // ¡It's empty and that's OK!
          break;
      }
      //------------------
      if (isHidden) {
        const fieldId = this.id.substring(0, this.id.indexOf('_'));
        const hasConditionals = this.conditionalIds.length > 0 || this.summationIds.length > 0 || this.weightedFieldIds.length > 0;
        const conditionalOfOtherField = page.form.conditionals?.config && Object.keys(page.form.conditionals.config[fieldId]).length > 0;
        let message = '';
        let showDialog = false;
        if (hasConditionals && conditionalOfOtherField) {
          showDialog = true;
          message = `${lang.current('hasConditionals')}<br>${lang.current('conditionalOfOtherField')}<br>${lang.current('continueConfirmation')}`;
        } else if (hasConditionals) {
          showDialog = true;
          message = `${lang.current('hasConditionals')}<br>${lang.current('continueConfirmation')}`;
        } else if (conditionalOfOtherField) {
          showDialog = true;
          message = `${lang.current('conditionalOfOtherField')}<br>${lang.current('continueConfirmation')}`;
        }
        const dontShow = this.firstLoad && this.isPropertiesPanelOpen();

        radioCheckFalse.checked = true;
        if (showDialog && !dontShow) {
          core.confirmDialog(-1, message).then(
            () => {
              radioCheckTrue.checked = true;
              this.setHiddenDisabled(true);
            },
            (e) => {
              radioCheckFalse.click();
            }
          );
        } else {
          radioCheckTrue.checked = true;
          this.setHiddenDisabled(true);
        }
      } else {
        this.setHiddenDisabled(false);
      }
      this.firstLoad = false;
    },
    getWeightedFieldsToSelectFieldWrapperId: function () {
      return `${this.parentId}_weightedFieldsToSelect_propertyWrapper`;
    },
    getSelectedWeightedField: function () {
      const answers = this.getConfigurableWeightedFieldsAnswers();
      const gridValue = WeightedFieldsProperty.syncWeightedFieldsData(answers, this.weightedFieldIds, {});
      return gridValue.valueData || [];
    },
    getConfigurableWeightedFieldsAnswers: function () {
      const currentTargetId = this.parentId;
      const answers = [];
      const formItems = page.form.formItems;
      if (formItems.length === 0) {
        return [];
      }
      const indexData = this.getAllQuestionNumber();
      for (const formField of formItems) {
        const fieldId = formField.id;
        if (fieldId === currentTargetId) {
          break;
        }
        if (formField.uiObject.tienePonderacion || formField.type === 'formWeightingResult') {
          const fieldNumber = indexData.get(fieldId) || '';
          const fieldText = `${fieldNumber}. ${formField.uiObject.getTitleText()}`;
          const answerTypeName = FieldDefinitionLabels[formField.uiObject.type].label;
          answers.push({
            id: `${fieldId}_uiObject`,
            conditionalType: ConditionalType.FIELD,
            code: fieldText,
            conditionalQuestionCode: fieldId,
            description: answerTypeName,
            type: formField.type
          });
        }
      }
      return answers;
    },
    // SHOW / HIDE selectedWeightedField
    onChangeWeightingMode: function (evt, property, value) {
      if (value === 't') {
        this.showWeightedFieldsToSelect();
        this.onChangeHidden(evt, property, 't');
      } else {
        this.hideConditionalFields(this.getWeightedFieldsToSelectFieldWrapperId());
        const contentDiv = $SV(this.id);
        domClass.remove(contentDiv, 'weighted-fields-to-select');
        this.onChangeHidden(evt, property, 'f');
      }
    },
    showWeightedFieldsToSelect: function () {
      const fsAnswer = dom.byId(this.getWeightedFieldsToSelectFieldWrapperId());
      if (fsAnswer) {
        const answers = this.getConfigurableWeightedFieldsAnswers();
        WeightedFieldsProperty.updateWeightedFieldsData(answers, this.weightedFieldIds, {});
        domStyle.set(fsAnswer, 'display', '');
        if (this.weightedFieldIds.length > 0) {
          PropertiesDiv(this.parentId).style.display = 'none';
        } else {
          PropertiesDiv(this.parentId).style.display = '';
        }
      }
      const contentDiv = $SV(this.id);
      domClass.add(contentDiv, 'weighted-fields-to-select');
    },
    renderWeightedFieldsToSelectField: function () {
      const label = lang.current('renderWeightedFieldsToSelectFieldSelector');
      const fs = $fieldset(
        {
          id: this.getWeightedFieldsToSelectFieldWrapperId(),
          class: 'propertyWrapper',
          style: {
            display: this.selectedWeightedField === 't' ? '' : 'none'
          }
        },
        $legend(label)
      );
      const parent = $SV('propertiesArea');
      parent.appendChild(fs);
      WeightedFieldsProperty.setConfig({
        configuredFieldName: 'renderWeightedFieldsToSelectFieldSelector',
        fieldId: this.parentId
      });
      WeightedFieldsProperty.placeAt(fs);
      this.weightedDialogFieldIds = this.getConfigurableWeightedFieldsAnswers();
      if (!showWeighting() || this.weightedDialogFieldIds === null || this.weightedDialogFieldIds.length === 0) {
        this.disableWeightedFieldsSelectionProperty(parent);
      }
      WeightedFieldsProperty.updateWeightedFieldsData(this.weightedDialogFieldIds, this.weightedFieldIds, {});
    },
    disableWeightedFieldsSelectionProperty: function (parent) {
      this.selectedWeightedField = 'f';
      // Se deshabilitan CHECKS
      const options = query('input[data-field-name=selectedWeightedField_]', parent);
      for (const option of options) {
        if (option.getAttribute('data-field-value') === 't') {
          option.checked = false;
        } else if (option.getAttribute('data-field-value') === 'f') {
          option.checked = true;
        }
        domAttr.set(option, 'disabled', 'disabled');
      }
      // Se habilita mensaje de "No hay terminos de calculo disponibles"
      query('.disabled-property-label', parent).forEach((node) => {
        domClass.remove(node, 'displayNone');
      });
      // Se ejecuta la acción de "No" (f) para apagar la configuración
      this.onChangeWeightingMode(null, 'selectedWeightedField', 'f');
    }
  });
  return formObject;
});
