define(['bnext/survey/_util/jsFailureExposure!'], () => {
  // noinspection UnnecessaryLocalVariableJS
  const request = {
    'es-MX': {
      savecopyp: 'Se guardará el formulario con el nombre de "',
      savecopyc: '"<br/><br/>',
      sendRequest: '"<br/><br/>',
      'form-config-une-to-apply': '{Facilities} a las que aplica el formulario',
      'form-attendant-title': 'Responsables de llenar y firmas',
      sectionResponsible: 'Seccion con responsable de llenado',
      cofirmreturncontrol: '¿Está seguro de regresar al control de formularios?',
      cofirmreturndocuments: '¿Está seguro de regresar a solicitudes de documentos?',
      'save-survey-template': '' + 'El formulario podrá ser utilizado como base para la creación de otros formularios.<br><br>',
      nameTemplate: 'Nombre para la plantilla:',
      template: '(<PERSON><PERSON>).',
      createTemplate: 'Crear plantilla',
      cancel: 'Cancelar',
      languages: {
        language_strings: [
          //---------------------------------
          { element: '.NEW-CLASS #guardarCopiaBtn', text: 'Guardar' },
          { element: '.NEW-CLASS #guardarBtn', text: 'Guardar' },
          //---------------------------------
          { element: '.MODIFY-CLASS #guardarCopiaBtn', text: 'Guardar' },
          { element: '.MODIFY-CLASS #guardarBtn', text: 'Guardar' },
          //---------------------------------
          { element: '.EMPTY-CLASS #guardarCopiaBtn', text: 'Guardar copia' },
          { element: '.EMPTY-CLASS #guardarBtn', text: 'Guardar' },
          //---------------------------------
          { element: '.EDIT-CLASS #guardarCopiaBtn', text: 'Guardar copia' },
          { element: '.EDIT-CLASS #guardarBtn', text: 'Guardar' }
          //---------------------------------
        ]
      }
    },
    'en-US': {
      savecopyp: 'The form will be saved with the name "',
      savecopyc: '"<br/><br/>',
      sendRequest: '"<br/><br/>',
      'form-config-une-to-apply': '{Facilities} to which it applies the application form',
      'form-attendant-title': 'Responsible for filling and signatures',
      sectionResponsible: 'Section with assigned responsible',
      cofirmreturncontrol: "Do you really want to return to form's control?",
      cofirmreturndocuments: 'Do you really want to return to document requests?',
      'save-survey-template': '' + 'The form was used as a template for the creation of other forms.<br><br>',
      nameTemplate: 'Template name:',
      template: '(Template).',
      createTemplate: 'Create template',
      cancel: 'Cancel',
      languages: {
        language_strings: [
          //---------------------------------
          { element: '.NEW-CLASS #guardarCopiaBtn', text: 'Save' },
          { element: '.NEW-CLASS #guardarBtn', text: 'Save' },
          //---------------------------------
          { element: '.MODIFY-CLASS #guardarCopiaBtn', text: 'Save' },
          { element: '.MODIFY-CLASS #guardarBtn', text: 'Save' },
          //---------------------------------
          { element: '.EMPTY-CLASS #guardarCopiaBtn', text: 'Save' },
          { element: '.EMPTY-CLASS #guardarBtn', text: 'Save' },
          //---------------------------------
          { element: '.EDIT-CLASS #guardarCopiaBtn', text: 'Save' },
          { element: '.EDIT-CLASS #guardarBtn', text: 'Save' }
          //---------------------------------
        ]
      }
    },
    'es-CIESA': {
      savecopyp: 'Se guardará el formulario con el nombre de "',
      savecopyc: '"<br/><br/>',
      sendRequest: '"<br/><br/>',
      'form-config-une-to-apply': 'Grupo a los que aplica el formulario'
    }
  };
  return request;
});
