define(['bnext/survey/_util/jsFailureExposure!'], () => {
  const en_US = {
    formsCreateProperties: 'Click to select a value',
    formsCreatePropertiesUser: 'Click to select a user',
    colNameChoose: 'Select',
    language: 'english',
    untitledHelpText: 'Click to edit',
    untitledTipHelpText: 'Click to edit tip',
    dropElementsHere_header: 'Drag header elements here',
    dropElementsHere_body: 'Create form in this area',
    dropElementsHere_footer: 'Drag footer elements here',
    dropElementsHere: 'Drag the questionnaire items here',
    add: 'Add',
    edit: 'Edit',
    remove: 'Remove',
    required: 'Required',
    addFindingsEnabled: 'Any answer can create findings',
    addActivitiesEnabled: 'Any answer can create activities',
    yes: 'Yes',
    no: 'No',
    why: 'Why?',
    layout: 'layout',
    horizontal: 'Horizontal',
    vertical: 'Vertical',
    size: 'Field size',
    large: 'Large',
    small: 'Small',
    cantremovelanguage: "Can't remove last language!",
    columns: 'Columns',
    delete_confirm: 'Are you sure you want to erase?',
    delete_all_confirm: 'This will remove all data, please export to backup existing data. Are you sure you want to delete?',
    loading: 'Loading...',
    saving: 'Saving, This would take a few minutes...',
    guardado: 'It successfully saved',
    drophere: 'Drag item here ',
    wantToSave: 'Do you want to save before exiting?',
    addItem: 'Add item',
    delItem: 'Delete item',
    copyThisField: 'Copy this field',
    delThisField: 'Erase this field',
    fieldProperties: 'Field Properties',
    otherOption: 'Text box',
    other: 'Other',
    newThread: 'new thread',
    title: 'Title (click to edit)',
    message: 'message',
    submit: 'submit',
    cancel: 'cancel',
    reply: 'reply',
    theme_name: 'Theme name',
    background: 'Background',
    surveyTitle: 'Survey title',
    questionTitle: 'Question title',
    questionSubTitle: 'Question subtitle',
    fieldText: 'Field text',
    border: 'Border',
    color: 'Color',
    typo: 'Typography',
    exit: 'exit',
    hover: 'Hove color',
    addPage: 'Add new page',
    removePage: 'remove current page',
    deleteAllConfirm: 'are you sure you want do delete all?',
    logo: 'Logo',
    image: 'image',
    upload_logo: 'Logo',
    upload_image_error: 'invalid file (expecting .png, .gif or .jpg)',
    leavePage:
      'You tried to leave this page. \nIf you have made any changes and you have not clicked the save button, your changes will be lost. \n \n Are you sure you want to leave the page?',
    Stars: 'Stars',
    audioFile: 'MP3 audio file',
    option: 'Option ',
    invalidDatabaseQueryConfiguration: 'The external catalog query is invalid, review the hierarchy configuration.',
    answertype: 'Answer type',
    showFillOutDate: 'Show fill out date',
    alphanumeric: 'Alphanumeric',
    mail: 'Email',
    currency: 'Currency',
    currencyConversionUsdToMxn: 'Currency conversion - USD to MXN',
    numerical: 'Numerical',
    onlyNumbers: 'Only numbers',
    decimal: 'Decimal',

    summationQuestion: 'Calculate the sum of fields',
    summationAnswerSelector: 'Do summation using the selected answers',
    summationAnswerSelector_openAddLabel: 'Add answer',
    summationAnswerSelector_baseGrid_noRegMessage: 'Not selected an answer',

    summationAnswerSelector_addGrid_colNameAdd: 'Select',
    summationAnswerSelector_addGrid_colNameCode: 'Question',
    summationAnswerSelector_addGrid_colNameDescription: 'Answer',

    summationAnswerSelector_addDialogTitle: 'Do summation using the selected answers',
    summationAnswerSelector_addGrid_noRegMessage: 'No answers available',

    summationAnswerSelector_baseGrid_colNameCode: 'Question',
    summationAnswerSelector_baseGrid_colNameDescription: 'Answer',

    conditionalQuestion: 'Conditional question',
    pivotTableOnMobile: "Parámetro de TAB'S en movil",
    configureShowSummationField: 'Show field in form',
    'horizontal-value': 'Horizontal value',
    'vertical-value': 'Vertical value',
    conditionalAnswerSelector: 'Show question when selecting answer',
    conditionalAnswerSelector_addGrid_colNameAdd: 'Select',
    conditionalAnswerSelector_openAddLabel: 'Add answer',
    conditionalAnswerSelector_addDialogTitle: 'Show question when selecting answer',
    conditionalAnswerSelector_openAddTitle: 'Add answer',
    conditionalAnswerSelector_addGrid_colNameCode: 'Question',
    conditionalAnswerSelector_addGrid_colNameDescription: 'Answer',
    conditionalAnswerSelector_addGrid_noRegMessage: 'There is not any answer available',
    conditionalAnswerSelector_baseGrid_colNameCode: 'Question',
    conditionalAnswerSelector_baseGrid_colNameDescription: 'Answer',
    conditionalAnswerSelector_baseGrid_noRegMessage: 'Not selected an answer',

    renderWeightedFieldsToSelectFieldSelector: 'Perform calculation using weighted fields',
    renderWeightedFieldsToSelectFieldSelector_propertyDisabled: 'No weighted fields to select',
    renderWeightedFieldsToSelectFieldSelector_addGrid_colNameAdd: 'Select',
    renderWeightedFieldsToSelectFieldSelector_openAddLabel: 'Add field',
    renderWeightedFieldsToSelectFieldSelector_addDialogTitle: 'Show when selecting the field',
    renderWeightedFieldsToSelectFieldSelector_openAddTitle: 'Add field',
    renderWeightedFieldsToSelectFieldSelector_addGrid_colNameCode: 'Type',
    renderWeightedFieldsToSelectFieldSelector_addGrid_colNameDescription: 'Field',
    renderWeightedFieldsToSelectFieldSelector_addGrid_noRegMessage: 'No responses available for selection',
    renderWeightedFieldsToSelectFieldSelector_baseGrid_colNameCode: 'Type',
    renderWeightedFieldsToSelectFieldSelector_baseGrid_colNameDescription: 'Field',
    renderWeightedFieldsToSelectFieldSelector_baseGrid_noRegMessage: 'No terms selected for calculation',

    conditionalExpirationQuestion: 'Conditional expiration',
    conditionalExpirationAnswerSelector: 'Apply expiration using the selected answers',
    conditionalExpirationAnswerSelector_openAddLabel: 'Add answer',
    conditionalExpirationAnswerSelector_baseGrid_noRegMessage: 'Not selected an answer',

    conditionalExpirationAnswerSelector_addGrid_colNameAdd: 'Select',
    conditionalExpirationAnswerSelector_addGrid_colNameCode: 'Question',
    conditionalExpirationAnswerSelector_addGrid_colNameDescription: 'Answer',

    conditionalExpirationAnswerSelector_addDialogTitle: 'Apply expiration using the selected answers',
    conditionalExpirationAnswerSelector_addGrid_noRegMessage: 'No answers available',

    conditionalExpirationAnswerSelector_baseGrid_colNameCode: 'Question',
    conditionalExpirationAnswerSelector_baseGrid_colNameDescription: 'Answer',

    hierarchyLevelAnswerToSelect: 'To configure',
    hierarchyLevelAnswerSelect: 'Configure',
    hierarchyLeveMultipleAnswers: 'Multiple answers',
    hierarchyLevelAnswerAddDialogTitle: 'Hierarchy level answers',
    hierarchyLevelAnswerNoRegMessage: 'There is not any answer available',
    clickelement: 'Click on an item to see its information.',
    orientation: 'Orientation',
    fracasosaving: 'All sections and field arrays must have a question and there must be at least one section. <br/>(The instructions do not count as questions).',
    confirmsaving: 'The form will be saved.  Do you want to continue?',
    confirmSavingTitle: 'System message',
    cofirmreturn: 'Do you really want to return?',
    savecopyp: 'It will be saved as a copy of the questionnaire and will be named "',
    savecopyc: ' (copy)"<br/>',
    cofirmsavecopyp: 'Do you want to continue?',
    fillproperties: 'Please fill out all the properties of the items marked with an exclamation point.',
    required_properties: 'You need fill all the required properties.',
    clausula: 'Clause',
    select_combo: '-- Select --',
    configure: 'Configure',
    required_title: 'The title is required in order to save the survey.',
    'form-config-une-to-apply': '{Facilities} to which it applies the questionnaire',
    'request-data-must-complete': "To save you must fill the data's request.",
    'request-data-missing-restrictRecordsField': 'To save you must fill "Question to select department" field of the request.',
    'request-data-missing-externalCatalogsWithHierarchy': 'To save you must add a field of type "Catalog" configured as external catalog and with hierarchy levels',
    'request-data-invalid-restrictRecordsField': 'To save you must select a valid value for "Question to select department" field of the request',
    'form-attendant-title': 'Pooled',
    catalog: 'Catalog',
    'catalog-type': "Catalog's Type",
    enabledHierarchyInfo: 'The configured query has hierarchy levels',
    'auto-select-single-answer': 'Auto select answer when there is only one option',
    'catalog-external': 'External',
    'catalog-internal': 'Internal',
    'date-selector-default-value': 'Default date value',
    'date-selector-default-value-no': 'Custom date',
    'date-selector-default-value-request': 'Fill request date',
    'date-selector-default-value-approved-current-version': "Approval date of the form's current version",
    includeTime: 'Include time',
    restrictPastDates: 'Restrict dates in the past',
    maxPastHours: 'Maximum hours in the past',
    'request-field-signature-filler-entity': 'Responsible for signing',
    activitiesTemplateEnabled: 'New activities template available',
    allowNegativeValues: 'Allow negative values',
    denyNegativeValues: 'Not allow negative values',
    'field-activities-template': 'New activities template',
    'field-activities-template-implementer-entity': 'New activities responsible',
    'field-activities-template-verifier-entity': 'New activities verifier',
    fillFormWithoutRequestor: 'There is not a section that must be completed by the applicant. This form can not be filled.',
    reportActivity: 'Report activity',
    summation: 'Do summation',
    'request-field-seccion-filler-entity': 'Responsible for completing the section',
    'request-field-seccion-filler-entity-nobody': 'Anyone',
    'request-field-seccion-filler-entity-requestor': 'Applicant',
    'request-field-seccion-filler-entity-boss': 'Boss of person who fill previous section',
    'request-field-seccion-filler-entity-business-unit-position': 'Job in {facility}',
    'request-field-seccion-filler-entity-organizational-unit-position': 'Job in corporate',
    'request-field-seccion-filler-entity-user': 'Specific user',
    'request-field-seccion-filler-entity-user-to-be-defined': 'User to be defined',
    'fileupload-image-size-preview': 'Width size preview',
    'fileupload-image-size-preview-12': '1/8 sheet',
    'fileupload-image-size-preview-25': '1/4 sheet',
    'fileupload-image-size-preview-33': '1/3 sheet',
    'fileupload-image-size-preview-50': 'half sheet',
    'fileupload-image-size-preview-100': 'full sheet',
    'fileupload-image-size-preview-disabled': 'Not available for this size.',
    'hasBusinessUnitKey-is-main': 'Associate {facility} from this catalog',
    'hasBusinessUnitDepartmentKey-is-main': 'Associate {department} from this catalog',
    'hasAreaKey-is-main': 'Associate {area} from this catalog',
    close: 'Close',
    languages: {
      language_strings: [{ element: '#guardarCopiaBtn', text: 'Save copy' }]
    },
    externalCatalogMain_true:
      'This catalog will associate the form to the selected {something}, only one field with this icon can be selected, the rest will be shown in gray.',
    externalCatalogMain_false: 'This catalog could associate the form to the selected {something}. However, only a field with this icon can be selected, shown in green',
    properties: 'Properties',
    globalProperties: 'Global properties',
    general: 'General',
    requestData: 'Request data',
    responsibleFilling: 'Responsible of filling in and signatures',
    fieldForm: 'Form fields',
    complexFields: 'Complex fields',
    automaticallyText: 'Auto-generated text',
    facility: '{Facility}',
    weightedGrade: 'Enable weighted grade',
    confirmDelete: 'Are you sure you want to clear the values of the answers already assigned?',
    complementConfirmDelete: 'if so the same can no longer be recovered',
    singleAnswer: 'Single answer',
    matrix: 'One answer per row matrix',
    YesNoWhy: 'Yes, No and Why',
    attachFiles: 'Attach files',
    fixedText: 'FieldText',
    rowMatrix: 'Vertical matrix of menus',
    menu: 'Menu',
    multipleChoice: 'Multiple choice',
    'catalog-multiple': 'Multiple choice catalog',
    'catalog-hierarchy': 'Catalog with hierarchy',
    verificationList: 'Verification list',
    openText: 'Open text',
    openTextRow: 'Open text by rows',
    fieldMatrix: 'Field matrix',
    pageBreak: 'Page break in print preview',
    date: 'Date',
    timeSelector: 'Time',
    stopwatchSelector: 'Stopwatch and GPS',
    formWeightingResult: 'Weighted grade results',
    stopwatch: 'Stopwatch',
    registerDate: 'Date',
    checkInDate: 'Check-in',
    checkOutDate: 'Check-out',
    gpsCheckIn: 'GPS Check-in',
    gpsCheckout: 'GPS Check-out',
    gpsLatitude: 'Latitude',
    gpsLongitude: 'Longitude',
    gpsInfo: 'GPS Information',
    allowDeleteStopwatchRecord: 'Allow the deletion of the stopwatch record',
    weightingType: 'Weighting grade type',
    SurveyWeightingType_SUM: 'Summation',
    SurveyWeightingType_AVG: 'Average',
    SurveyWeightingType_MAX: 'Maximum value',
    SurveyWeightingType_MIN: 'Minimum value',
    SurveyWeightingType_SUBS: 'Subtraction',
    SurveyWeightingType_MULT: 'Multiplication',
    SurveyWeightingType_DIVS: 'Division',
    SurveyWeightingType_INIT: 'Static value',
    SurveyWeightingType_WeightingMode: 'Select specific answers for calculation',
    pleaseActivateWeighting: 'Please activate the weighted grade',
    table: 'Table',
    signature: 'Signature',
    nameUserRequest: 'Name of applicant',
    nameUserOriginal: 'Name of the original user',
    nameAuthorize: 'Name of authorizer(s)',
    nameUserFilled: 'Name of user filling in',
    formVersion: 'Form version',
    formTitle: 'Form title',
    formId: 'Form ID',
    referenceNumber: 'Reference number',
    dateTimeRequest: 'Date and time the section was filled',
    dateTimeInitialRequest: 'Date and time the request was started',
    dateTimeInitialFill: 'Date and time the request was made',
    sectionResponsible: 'Section with the responsible of filling in',
    pending: 'Task',
    signatureType: 'Days to remind delay',
    sameDay: 'The next day',
    laterDay: 'Configure an specific day count',
    laterDayInput: '#Days to remind delay',
    daysToExpireSectionTitle: 'The section may expire',
    daysToExpireSignatureTitle: 'The signature may expire',
    daysToExpire: '#Days to expire',
    daysToNotifyBeforeExpiration: '#Days to notify before expiration',
    includeInMailTitle: 'Include in progress mail',
    canCancel: 'The authorizer can cancel',
    signRejectApproval: 'Requires approval to return',
    'seccion-attend-progress-state-id': 'Finish progress status',
    'signature-attend-progress-state-id': 'Sign progress status',
    'seccion-partial-progress-statuses': 'Statuses when saving progress',
    'cancel-progress-state-id': 'Cancel progress statuses',
    'reject-progress-state-id': 'Reject progress statuses',
    canFillerCancel: 'The user can cancel',
    seccion: 'Section',
    openAddUserLabel: 'Available users',
    openAddUserTitle: 'Click to add user',
    seccion_addUserDialogTitle: 'Choose next section responsible',
    signature_addUserDialogTitle: 'Choose next signature responsible',
    editableSections_openAddLabel: 'Editable sections',
    editableSections_addDialogTitle: 'Add editable sections',
    editableSections_openAddTitle: 'Click to add editable sections',
    editableSections_addGrid_colNameCode: 'ID',
    editableSections_addGrid_colNameDescription: 'Title',
    editableSections_addGrid_noRegMessage: 'All sections have been selected',
    editableSections_baseGrid_noRegMessage: 'Editable sections not configured',
    editableSections_baseGrid_colNameCode: 'ID',
    editableSections_baseGrid_colNameDescription: 'Title',
    authorizer: 'Authorizer',
    dateHour: 'Date and Time',
    defaultFieldRequestorSection: 'To be filled by applicant',
    fillAutomaticallyAtOpen: 'Fill automatically from URL',
    stage: 'Stage',
    stagePlaceholder: 'Stage name',
    typeParameterName: 'Enter the name of the parameter',
    addQuestionLinkLabel: 'Add a link',
    addQuestionLinkPlaceholder: 'Ex. http://bnext.mx',
    defineIconHelpLink: 'Assign an icon to the link',
    helpIcon: 'Help',
    helpCloudDownload: 'Download',
    helpThumbUp: 'Thumbs up',
    helpFavorite: 'Favorite',
    helpPlayCircleFilled: 'Play',
    helpLocationOn: 'Location on',
    helpMail: 'Mail',
    helpPhone: 'Phone',
    requestAdjust: 'Allows requesting adjustment',
    QRImage: 'QR Image',
    linkQR: 'Link QR',
    linkQRFixed: 'Fixed link',
    linkQROnFill: 'Link on fill',
    addLinkQR: 'Add a link',
    exampleLinkQR: 'Ej. https://bnext.mx',
    scanMe: 'Scan the QR code with your mobile phone to complete the information directly on your device.',
    editTitleAfterCreate: 'Edit title after create',
    hidden: 'Hidden',
    hasConditionals: 'You have conditionals or summations, if you continue you will lose the configuration.',
    conditionalOfOtherField: 'The field is conditional for another, if you continue you will lose the configuration.',
    continueConfirmation: 'do you wish to continue?',
    handwrittenSignature: 'Handwritten signature',
    handwrittenSignatureBtn: 'Sign',
    cleanHandwrittenSignature: 'Clean signature',
    canvasNotSupported: 'Feature not supported by the browser, update to latest version.',
    allowMobileUploadingFrom: 'Allow uploading in mobile only from (restrict to images):',
    camera: 'Camera',
    both: 'Camera and media',
    geolocation: 'Geolocation'
  };
  return en_US;
});
