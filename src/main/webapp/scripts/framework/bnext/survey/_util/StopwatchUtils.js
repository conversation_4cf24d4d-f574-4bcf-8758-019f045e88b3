define([], () => {
  let STOPWATCH_REFRESH_INTERVAL_ID = null;
  const STOPWATCH_REFRESH_WIDGETS = [];

  /**
   * Stops the periodic refresh for the specified widget by removing it from the refresh widgets list.
   *
   * @param {Object} targetWidget - The widget to stop refreshing.
   */
  function timerStopRefreshWidget(targetWidget) {
    let indexId = -1;
    const widgetLength = STOPWATCH_REFRESH_WIDGETS.length;
    for (let i = 0; i < widgetLength; i++) {
      const widget = STOPWATCH_REFRESH_WIDGETS[i];
      if (!widget) {
        continue;
      }
      if (widget?.id === targetWidget?.id) {
        indexId = i;
        break;
      }
    }
    if (indexId !== -1) {
      STOPWATCH_REFRESH_WIDGETS.splice(indexId, 1);
    }
    if (STOPWATCH_REFRESH_WIDGETS.length === 0) {
      stopRefresh();
    }
  }

  /**
   * Starts the periodic refresh for all stopwatch widgets by setting an interval.
   * If the interval is already set, it does nothing.
   */
  function timerStartPeriodicRefresh() {
    if (STOPWATCH_REFRESH_INTERVAL_ID) {
      return;
    }
    STOPWATCH_REFRESH_INTERVAL_ID = true;
    STOPWATCH_REFRESH_INTERVAL_ID = setInterval(() => {
      requestAnimationFrame(executePeriodicStopwatchRefresh);
    }, 1_000);
  }

  /**
   * Executes the periodic refresh for all stopwatch widgets.
   * Iterates through the list of widgets and calls their `refresh` method.
   * Logs an error if a widget is not found.
   */
  function executePeriodicStopwatchRefresh() {
    const widgetLength = STOPWATCH_REFRESH_WIDGETS.length;
    if (widgetLength === 0) {
      stopRefresh();
      return;
    }
    for (let i = 0; i < widgetLength; i++) {
      const widget = STOPWATCH_REFRESH_WIDGETS[i];
      if (!widget) {
        console.error('Stopwatch Widget not found for index:', i);
        continue;
      }
      widget.refreshClock();
    }
  }

  function addAnimations() {
    const widgetLength = STOPWATCH_REFRESH_WIDGETS.length;
    if (widgetLength === 0) {
      return;
    }
    for (let i = 0; i < widgetLength; i++) {
      const widget = STOPWATCH_REFRESH_WIDGETS[i];
      if (!widget) {
        console.error('Cannot add animations for Stopwatch Widget not found for index:', i);
        continue;
      }
      widget.addAnimations();
    }
  }
  function clearAnimations() {
    const widgetLength = STOPWATCH_REFRESH_WIDGETS.length;
    if (widgetLength === 0) {
      return;
    }
    for (let i = 0; i < widgetLength; i++) {
      const widget = STOPWATCH_REFRESH_WIDGETS[i];
      if (!widget) {
        console.error('Cannot clear animations for Stopwatch Widget not found for index:', i);
        continue;
      }
      widget.clearAnimations();
    }
  }

  function addRefreshWidget(widget) {
    STOPWATCH_REFRESH_WIDGETS.push(widget);
  }

  function stopRefresh() {
    clearInterval(STOPWATCH_REFRESH_INTERVAL_ID);
    STOPWATCH_REFRESH_INTERVAL_ID = null;
  }

  function onWindowFocus(e) {
    timerStartPeriodicRefresh();
    addAnimations();
  }

  window.addEventListener('focus', onWindowFocus);

  // noinspection UnnecessaryLocalVariableJS
  /**
   * @exports StopwatchUtils
   * @type {{addRefreshWidget: addRefreshWidget}}
   * @type {{timerStartPeriodicRefresh: timerStartPeriodicRefresh}}
   * @type {{timerStopRefreshWidget: timerStopRefreshWidget}}
   * @type {{stopRefresh: stopRefresh}}
   */
  const StopwatchUtils = {
    addRefreshWidget: addRefreshWidget,
    timerStartPeriodicRefresh: timerStartPeriodicRefresh,
    timerStopRefreshWidget: timerStopRefreshWidget,
    stopRefresh: stopRefresh
  };
  return StopwatchUtils;
});
