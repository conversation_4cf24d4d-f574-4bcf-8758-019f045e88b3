define(['bnext/survey/_util/jsFailureExposure!'],
        function() {

            return {
                load: function(id, req, load) {
                    
                    Date.prototype.toJSON = function() {
                        return"\"" + this.getFullYear() + "-" + (this.getMonth() + 1).toPaddedString(2) + "-" + this.getDate().toPaddedString(2) + "T" + this.getHours().toPaddedString(2) + ":" + this.getMinutes().toPaddedString(2) + ":" + this.getSeconds().toPaddedString(2) + "\"";
                    };

                    load();
                }
            };
        });