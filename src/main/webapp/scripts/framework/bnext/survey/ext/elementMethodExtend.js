define([
    'bnext/survey/_base/$SV',
    'bnext/survey/_base/$H',
    'bnext/survey/_base/$A',
    'bnext/survey/_base/Prototype',
    'bnext/survey/_util/Class',
    'bnext/survey/_util/Enumerable',
    'bnext/survey/_util/jsFailureExposure!',
    'bnext/survey/ext/objectExtend!'],
        function($SV, $H, $A, Prototype, Class, Enumerable) {
            /* elementMethodExtend */
            return {
                load: function(id, req, load) {
                    var Element;
                    window.Element = Element = window.Element || {};

                    Element.Methods = {
                        visible: function(a) {
                            return $SV(a).style.display != "none"
                        },
                        toggle: function(a) {
                            a = $SV(a);
                            Element[Element.visible(a) ? "hide" : "show"](a);
                            return a
                        },
                        getValue : function(a) {
                            if (a.tagName.toUpperCase() === 'INPUT' || a.tagName.toUpperCase() === 'SELECT') {
                                return a.value;
                            }
                            return a.innerHTML || a.innerText;
                        },
                        setValue : function(a, b) {
                            a.value = b;
                            if (a.tagName.toUpperCase() === 'DIV' || a.tagName.toUpperCase() === 'SPAN') {
                                try {
                                    a.innerHTML = b;
                                } catch(e) {
                                    a.innerText = b;                                    
                                }
                            }
                        },
                        hide: function(a) {
                            $SV(a).style.display = "none";
                            return a
                        },
                        show: function(a) {
                            $SV(a).style.display = "";
                            return a
                        },
                        remove: function(a) {
                            a = $SV(a);
                            a.parentNode.removeChild(a);
                            return a
                        },
                        update: function(a, b) {
                            b = typeof b == "undefined" ? "" : b.toString();
                            $SV(a).innerHTML = b.stripScripts();
                            setTimeout(function() {
                                b.evalScripts()
                            }, 10);
                            return a
                        },
                        replace: function(a, b) {
                            a = $SV(a);
                            b = typeof b == "undefined" ? "" : b.toString();
                            if (a.outerHTML) {
                                a.outerHTML = b.stripScripts()
                            } else {
                                var c = a.ownerDocument.createRange();
                                c.selectNodeContents(a);
                                a.parentNode.replaceChild(c.createContextualFragment(b.stripScripts()), a)
                            }
                            setTimeout(function() {
                                b.evalScripts()
                            }, 10);
                            return a
                        },
                        inspect: function(d) {
                            d = $SV(d);
                            var e = "<" + d.tagName.toLowerCase();
                            $H({
                                "id": "id",
                                "className": "class"
                            }).each(function(a) {
                                var b = a.first(), attribute = a.last();
                                var c = (d[b] || "").toString();
                                if (c) {
                                    e += " " + attribute + "=" + c.inspect(1)
                                }
                            });
                            return e + ">"
                        },
                        recursivelyCollect: function(a, b) {
                            a = $SV(a);
                            var c = [];
                            while (a = a[b]) {
                                if (a.nodeType == 1) {
                                    c.push(Element.extend(a))
                                }
                            }
                            return c
                        },
                        ancestors: function(a) {
                            return $SV(a).recursivelyCollect("parentNode")
                        },
                        descendants: function(a) {
                            return $A($SV(a).getElementsByTagName("*")).each(Element.extend)
                        },
                        firstDescendant: function(a) {
                            a = $SV(a).firstChild;
                            while (a && a.nodeType != 1) {
                                a = a.nextSibling
                            }
                            return $SV(a)
                        },
                        immediateDescendants: function(a) {
                            if (!(a = $SV(a).firstChild)) {
                                return[]
                            }
                            while (a && a.nodeType != 1) {
                                a = a.nextSibling
                            }
                            if (a) {
                                return[a].concat($SV(a).nextSiblings())
                            }
                            return[]
                        },
                        previousSiblings: function(a) {
                            return $SV(a).recursivelyCollect("previousSibling")
                        },
                        nextSiblings: function(a) {
                            return $SV(a).recursivelyCollect("nextSibling")
                        },
                        siblings: function(a) {
                            a = $SV(a);
                            return a.previousSiblings().reverse().concat(a.nextSiblings())
                        },
                        readAttribute: function(a, b) {
                            a = $SV(a);
                            if (Prototype.Browser.IE) {
                                if (!a.attributes) {
                                    return null
                                }
                                var t = Element._attributeTranslations;
                                if (t.values[b]) {
                                    return t.values[b](a, b)
                                }
                                if (t.names[b]) {
                                    b = t.names[b]
                                }
                                var c = a.attributes[b];
                                return c ? c.nodeValue : null
                            }
                            return a.getAttribute(b)
                        },
                        getHeight: function(a) {
                            return $SV(a).getDimensions().height
                        },
                        getWidth: function(a) {
                            return $SV(a).getDimensions().width
                        },
                        classNames: function(a) {
                            return new Element.ClassNames(a)
                        },
                        hasClassName: function(a, b) {
                            if (!(a = $SV(a))) {
                                return
                            }
                            var c = a.className;
                            if (c.length == 0) {
                                return 0
                            }
                            if (c == b || c.match(new RegExp("(^|\\s)" + b + "(\\s|$)"))) {
                                return 1
                            }
                            return 0
                        },
                        addClassName: function(a, b) {
                            if (!(a = $SV(a))) {
                                return
                            }
                            Element.classNames(a).add(b);
                            return a
                        },
                        removeClassName: function(a, b) {
                            if (!(a = $SV(a))) {
                                return
                            }
                            Element.classNames(a).remove(b);
                            return a
                        },
                        toggleClassName: function(a, b) {
                            if (!(a = $SV(a))) {
                                return
                            }
                            Element.classNames(a)[a.hasClassName(b) ? "remove" : "add"](b);
                            return a
                        },
                        observe: function() {
                            Event.observe.apply(Event, arguments);
                            return $A(arguments).first()
                        },
                        stopObserving: function() {
                            Event.stopObserving.apply(Event, arguments);
                            return $A(arguments).first()
                        },
                        cleanWhitespace: function(a) {
                            a = $SV(a);
                            var b = a.firstChild;
                            while (b) {
                                var c = b.nextSibling;
                                if (b.nodeType == 3 && !/\S/.test(b.nodeValue)) {
                                    a.removeChild(b)
                                }
                                b = c
                            }
                            return a
                        },
                        empty: function(a) {
                            return $SV(a).innerHTML.blank()
                        },
                        descendantOf: function(a, b) {
                            a = $SV(a), b = $SV(b);
                            while (a = a.parentNode) {
                                if (a == b) {
                                    return 1
                                }
                            }
                            return 0
                        },
                        getStyle: function(a, b) {
                            a = $SV(a);
                            b = b == "float" ? "cssFloat" : b.camelize();
                            var c = a.style[b];
                            if (!c) {
                                var d = document.defaultView.getComputedStyle(a, null);
                                c = d ? d[b] : null
                            }
                            if (b == "opacity") {
                                return c ? parseFloat(c) : 1
                            }
                            return c == "auto" ? null : c
                        },
                        getOpacity: function(a) {
                            return $SV(a).getStyle("opacity")
                        },
                        setStyle: function(a, b, c) {
                            a = $SV(a);
                            var d = a.style;
                            for (var e in b) {
                                if (e == "opacity") {
                                    a.setOpacity(b[e])
                                } else {
                                    d[(e == "float" || e == "cssFloat") ? (d.styleFloat === undefined ? "cssFloat" : "styleFloat") : (c ? e : e.camelize())] = b[e]
                                }
                            }
                            return a
                        },
                        setOpacity: function(a, b) {
                            a = $SV(a);
                            a.style.opacity = (b == 1 || b === "") ? "" : (b < 0.00001) ? 0 : b;
                            return a
                        },
                        getDimensions: function(a) {
                            a = $SV(a);
                            var b = $SV(a).getStyle("display");
                            if (b != "none" && b != null) {
                                return{
                                    width: a.offsetWidth,
                                    height: a.offsetHeight
                                }
                            }
                            var c = a.style;
                            var d = c.visibility;
                            var e = c.position;
                            var f = c.display;
                            c.visibility = "hidden";
                            c.position = "absolute";
                            c.display = "block";
                            var g = a.clientWidth;
                            var h = a.clientHeight;
                            c.display = f;
                            c.position = e;
                            c.visibility = d;
                            return{
                                width: g,
                                height: h
                            }
                        },
                        makePositioned: function(a) {
                            a = $SV(a);
                            var b = Element.getStyle(a, "position");
                            if (b == "static" || !b) {
                                a._madePositioned = 1;
                                //a.style.position = "relative";
                                if (window.opera) {
                                    a.style.top = 0;
                                    a.style.left = 0
                                }
                            }
                            return a
                        },
                        undoPositioned: function(a) {
                            a = $SV(a);
                            if (a._madePositioned) {
                                a._madePositioned = undefined;
                                a.style.position = a.style.top = a.style.left = a.style.bottom = a.style.right = ""
                            }
                            return a
                        },
                        makeClipping: function(a) {
                            a = $SV(a);
                            if (a._overflow) {
                                return a
                            }
                            a._overflow = a.style.overflow || "auto";
                            if ((Element.getStyle(a, "overflow") || "visible") != "hidden") {
                                a.style.overflow = "hidden"
                            }
                            return a
                        },
                        undoClipping: function(a) {
                            a = $SV(a);
                            if (!a._overflow) {
                                return a
                            }
                            a.style.overflow = a._overflow == "auto" ? "" : a._overflow;
                            a._overflow = null;
                            return a
                        }
                    };

                    Object.extend(Element.Methods, {
                        childOf: Element.Methods.descendantOf,
                        childElements: Element.Methods.immediateDescendants
                    });
                    if (Prototype.Browser.Opera) {
                        Element.Methods._getStyle = Element.Methods.getStyle;
                        Element.Methods.getStyle = function(a, b) {
                            switch (b) {
                                case"left":
                                case"top":
                                case"right":
                                case"bottom":
                                    if (Element._getStyle(a, "position") == "static") {
                                        return null
                                    }
                                default:
                                    return Element._getStyle(a, b)
                            }
                        }
                    } else {
                        if (Prototype.Browser.IE) {
                            Element.Methods.getStyle = function(a, b) {
                                a = $SV(a);
                                b = (b == "float" || b == "cssFloat") ? "styleFloat" : b.camelize();
                                var c = a.style[b];
                                if (!c && a.currentStyle) {
                                    c = a.currentStyle[b]
                                }
                                if (b == "opacity") {
                                    if (c = (a.getStyle("filter") || "").match(/alpha\(opacity=(.*)\)/)) {
                                        if (c[1]) {
                                            return parseFloat(c[1]) / 100
                                        }
                                    }
                                    return 1
                                }
                                if (c == "auto") {
                                    if ((b == "width" || b == "height") && (a.getStyle("display") != "none")) {
                                        return a["offset" + b.capitalize()] + "px"
                                    }
                                    return null
                                }
                                return c
                            };

                            Element.Methods.setOpacity = function(a, b) {
                                a = $SV(a);
                                var c = a.getStyle("filter"), style = a.style;
                                if (b == 1 || b === "") {
                                    style.filter = c.replace(/alpha\([^\)]*\)/gi, "");
                                    return a
                                } else {
                                    if (b < 0.00001) {
                                        b = 0
                                    }
                                }
                                style.filter = c.replace(/alpha\([^\)]*\)/gi, "") + "alpha(opacity=" + (b * 100) + ")";
                                return a
                            };

                            Element.Methods.update = function(b, c) {
                                b = $SV(b);
                                c = typeof c == "undefined" ? "" : c.toString();
                                var d = b.tagName.toUpperCase();
                                if (["THEAD", "TBODY", "TR", "TD"].include(d)) {
                                    var e = document.createElement("div");
                                    switch (d) {
                                        case"THEAD":
                                        case"TBODY":
                                            e.innerHTML = "<table><tbody>" + c.stripScripts() + "</tbody></table>";
                                            depth = 2;
                                            break;
                                        case"TR":
                                            e.innerHTML = "<table><tbody><tr>" + c.stripScripts() + "</tr></tbody></table>";
                                            depth = 3;
                                            break;
                                        case"TD":
                                            e.innerHTML = "<table><tbody><tr><td>" + c.stripScripts() + "</td></tr></tbody></table>";
                                            depth = 4
                                    }
                                    $A(b.childNodes).each(function(a) {
                                        b.removeChild(a)
                                    });
                                    depth.times(function() {
                                        e = e.firstChild
                                    });
                                    $A(e.childNodes).each(function(a) {
                                        b.appendChild(a)
                                    })
                                } else {
                                    b.innerHTML = c.stripScripts()
                                }
                                setTimeout(function() {
                                    c.evalScripts()
                                }, 10);
                                return b
                            }
                        } else {
                            if (Prototype.Browser.Gecko) {
                                Element.Methods.setOpacity = function(a, b) {
                                    a = $SV(a);
                                    a.style.opacity = (b == 1) ? 0.999999 : (b === "") ? "" : (b < 0.00001) ? 0 : b;
                                    return a
                                }
                            }
                        }
                    }
                    Element._attributeTranslations = {
                        names: {
                            colspan: "colSpan",
                            rowspan: "rowSpan",
                            valign: "vAlign",
                            datetime: "dateTime",
                            accesskey: "accessKey",
                            tabindex: "tabIndex",
                            enctype: "encType",
                            maxlength: "maxLength",
                            readonly: "readOnly",
                            longdesc: "longDesc"
                        },
                        values: {
                            _getAttr: function(a, b) {
                                return a.getAttribute(b, 2)
                            },
                            _flag: function(a, b) {
                                return $SV(a).hasAttribute(b) ? b : null
                            },
                            style: function(a) {
                                return a.style.cssText.toLowerCase()
                            },
                            title: function(a) {
                                var b = a.getAttributeNode("title");
                                return b.specified ? b.nodeValue : null
                            }
                        }
                    };

                    (function() {
                        Object.extend(this, {
                            href: this._getAttr,
                            src: this._getAttr,
                            type: this._getAttr,
                            disabled: this._flag,
                            checked: this._flag,
                            readonly: this._flag,
                            multiple: this._flag
                        })
                    }).call(Element._attributeTranslations.values);
                    Element.Methods.Simulated = {
                        hasAttribute: function(a, b) {
                            var t = Element._attributeTranslations, node;
                            b = t.names[b] || b;
                            node = $SV(a).getAttributeNode(b);
                            return node && node.specified
                        }
                    };

                    Element.Methods.ByTag = {};

                    Object.extend(Element, Element.Methods);
                    if (!Prototype.BrowserFeatures.ElementExtensions && document.createElement("div").__proto__) {
                        window.HTMLElement = {};

                        window.HTMLElement.prototype = document.createElement("div").__proto__;
                        Prototype.BrowserFeatures.ElementExtensions = 1
                    }
                    Element.hasAttribute = function(a, b) {
                        if (a.hasAttribute) {
                            return a.hasAttribute(b)
                        }
                        return Element.Methods.Simulated.hasAttribute(a, b)
                    };

                    Element.addMethods = function(Form) {
                        var F = Prototype.BrowserFeatures, T = Element.Methods.ByTag;
                        Object.extend(Form, Form.Methods);
                        Object.extend(Form.Element, Form.Element.Methods);
                        Object.extend(Element.Methods.ByTag, {
                            "FORM": Object.clone(Form.Methods),
                            "INPUT": Object.clone(Form.Element.Methods),
                            "SELECT": Object.clone(Form.Element.Methods),
                            "TEXTAREA": Object.clone(Form.Element.Methods)
                        })
                        Object.extend(Element.Methods, {})
                        function copy(a, b, c) {
                            c = c || 0;
                            var d = Element.extend.cache;
                            for (var e in a) {
                                var f = a[e];
                                if (!c || !(e in b)) {
                                    b[e] = d.findOrStore(f)
                                }
                            }
                        }
                        function findDOMClass(a) {
                            var b;
                            var c = {
                                "OPTGROUP": "OptGroup",
                                "TEXTAREA": "TextArea",
                                "P": "Paragraph",
                                "FIELDSET": "FieldSet",
                                "UL": "UList",
                                "OL": "OList",
                                "DL": "DList",
                                "DIR": "Directory",
                                "H1": "Heading",
                                "H2": "Heading",
                                "H3": "Heading",
                                "H4": "Heading",
                                "H5": "Heading",
                                "H6": "Heading",
                                "Q": "Quote",
                                "INS": "Mod",
                                "DEL": "Mod",
                                "A": "Anchor",
                                "IMG": "Image",
                                "CAPTION": "TableCaption",
                                "COL": "TableCol",
                                "COLGROUP": "TableCol",
                                "THEAD": "TableSection",
                                "TFOOT": "TableSection",
                                "TBODY": "TableSection",
                                "TR": "TableRow",
                                "TH": "TableCell",
                                "TD": "TableCell",
                                "FRAMESET": "FrameSet",
                                "IFRAME": "IFrame"
                            };

                            if (c[a]) {
                                b = "HTML" + c[a] + "Element"
                            }
                            if (window[b]) {
                                return window[b]
                            }
                            b = "HTML" + a + "Element";
                            if (window[b]) {
                                return window[b]
                            }
                            b = "HTML" + a.capitalize() + "Element";
                            if (window[b]) {
                                return window[b]
                            }
                            window[b] = {};

                            window[b].prototype = document.createElement(a).__proto__;
                            return window[b]
                        }
                        if (F.ElementExtensions) {
                            copy(Element.Methods, HTMLElement.prototype);
                            copy(Element.Methods.Simulated, HTMLElement.prototype, 1)
                        }
                        if (F.SpecificElementExtensions) {
                            for (var i in Element.Methods.ByTag) {
                                var j = findDOMClass(i);
                                if (typeof j == "undefined") {
                                    continue
                                }
                                copy(T[i], j.prototype)
                            }
                        }
                        Object.extend(Element, Element.Methods);
                        delete Element.ByTag


                        Element.ClassNames = Class.create();
                        Element.ClassNames.prototype = {
                            initialize: function(a) {
                                this.element = $SV(a)
                            },
                            _each: function(b) {
                                this.element.className.split(/\s+/).select(function(a) {
                                    return a.length > 0
                                })._each(b)
                            },
                            set: function(a) {
                                this.element.className = a
                            },
                            add: function(a) {
                                if (this.include(a)) {
                                    return
                                }
                                this.set($A(this).concat(a).join(" "))
                            },
                            remove: function(a) {
                                if (!this.include(a)) {
                                    return
                                }
                                this.set($A(this).without(a).join(" "))
                            },
                            toString: function() {
                                return $A(this).join(" ")
                            }
                        };

                        Object.extend(Element.ClassNames.prototype, Enumerable);

                        Element.collectTextNodes = function(b) {
                            return $A($SV(b).childNodes).collect(function(a) {
                                return(a.nodeType == 3 ? a.nodeValue : (a.hasChildNodes() ? Element.collectTextNodes(a) : ""))
                            }).flatten().join("")
                        };

                        Element.collectTextNodesIgnoreClass = function(b, c) {
                            return $A($SV(b).childNodes).collect(function(a) {
                                return(a.nodeType == 3 ? a.nodeValue : ((a.hasChildNodes() && !Element.hasClassName(a, c)) ? Element.collectTextNodesIgnoreClass(a, c) : ""))
                            }).flatten().join("")
                        };

                        Element.setContentZoom = function(a, b) {
                            a = $SV(a);
                            a.setStyle({
                                fontSize: (b / 100) + "em"
                            });
                            if (Prototype.Browser.WebKit) {
                                window.scrollBy(0, 0)
                            }
                            return a
                        };

                        Element.getInlineOpacity = function(a) {
                            return $SV(a).style.opacity || ""
                        };

                        Element.forceRerendering = function(a) {
                            try {
                                a = $SV(a);
                                var n = document.createTextNode(" ");
                                a.appendChild(n);
                                a.removeChild(n)
                            } catch (e) {
                            }
                        };

                    };
                    load();
                }
            };
        });