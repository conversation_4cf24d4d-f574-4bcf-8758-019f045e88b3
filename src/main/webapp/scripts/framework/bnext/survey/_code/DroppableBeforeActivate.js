define([
    'dojo/dom-attr',
    'bnext/survey/_code/FieldRulesDefinition',
    'bnext/survey/_util/jsFailureExposure!'
],
function (domAttr, fieldRulesDefinition) {
    /**
     * DroppableBeforeActivate.js
     */
    var DroppableBeforeActivate = function (draggedDom, dropArea) {
            var validArea = true;
            var type = domAttr.get(dropArea, 'data-type');
            switch (domAttr.get(dropArea, 'formArea')) {
                case 'headerArea':
                    validArea = draggedDom.id in fieldRulesDefinition.headerValidFields;
                    break;
                case 'footerArea':
                    validArea = draggedDom.id in fieldRulesDefinition.footerValidFields;
                    break;
            }
            if (type === 'fieldArray') {
                validArea = draggedDom.id in fieldRulesDefinition.fieldArrayValidFields;
            }
            return validArea;
    };
    return DroppableBeforeActivate;
});