define(['bnext/survey/_util/jsFailureExposure!'], () => {
  /**
   * FieldRulesDefinition.js
   */
  const innerSectionFields = {
    exclusiveSelect: 1,
    exclusiveSelectArray: 1,
    exclusiveSelectYesNo: 1,
    fileUpload: 1,
    menuSelect: 1,
    multiSelect: 1,
    multiSelectArray: 1,
    textField: 1,
    textFieldArray: 1,
    fieldArray: 1,
    dateSelector: 1,
    timeSelector: 1,
    stopwatchSelector: 1,
    handwrittenSignature: 1,
    geolocation: 1,
    catalogSelect: 1,
    tableField: 1
  };
  /**
   * Son los fields los cuales pueden existir en
   * cualquier parte del cuestionario
   */
  const outerSectionFields = {
    pageBreak: 1,
    QRImage: 1,
    freeText: 1,
    signature: 1,
    formWeightingResult: 1,
    htmlTableFieldItem: 1,
    'request-timestamp': 1,
    'fill-start-timestamp': 1
  };
  /**
   * Son los fields validos para ser parte del HEADER
   */
  const headerValidFields = {
    freeText: 1,
    'request-timestamp': 1,
    'fill-start-timestamp': 1
  };
  const headerFooterValidFreeText = {
    'request-user': 1,
    'originator-user': 1,
    'authorizer-user': 1,
    'document-version': 1,
    'document-title': 1,
    'document-code': 1,
    'document-folio': 1,
    'request-timestamp': 1,
    'fill-start-timestamp': 1
  };
  const invalidAutoTextFreeText = {
    'fill-out-timestamp': 1,
    'fill-out-authorizer': 1
  };
  /**
   * Son los fields validos para ser parte del FOOTER
   */
  const footerValidFields = {
    freeText: 1,
    'request-timestamp': 1,
    'fill-start-timestamp': 1
  };
  const fieldArrayValidFields = {
    exclusiveSelect: 1,
    exclusiveSelectYesNo: 1,
    menuSelect: 1,
    multiSelect: 1,
    textField: 1,
    textFieldArray: 1,
    catalogSelect: 1,
    dateSelector: 1,
    timeSelector: 1,
    stopwatchSelector: 1
  };
  const countableFields = {
    exclusiveSelect: 1,
    exclusiveSelectArray: 1,
    exclusiveSelectYesNo: 1,
    fileUpload: 1,
    listSelectArray: 1,
    listSelectArrayFlipped: 1,
    menuSelect: 1,
    multiSelect: 1,
    multiSelectArray: 1,
    textField: 1,
    textFieldArray: 1,
    fieldArray: 1,
    dateSelector: 1,
    timeSelector: 1,
    stopwatchSelector: 1,
    tableField: 1,
    signature: 1,
    catalogSelect: 1
  };
  return {
    innerSectionFields: innerSectionFields,
    outerSectionFields: outerSectionFields,
    headerValidFields: headerValidFields,
    footerValidFields: footerValidFields,
    fieldArrayValidFields: fieldArrayValidFields,
    countableFields: countableFields,
    headerFooterValidFreeText: headerFooterValidFreeText,
    invalidAutoTextFreeText: invalidAutoTextFreeText
  };
});
