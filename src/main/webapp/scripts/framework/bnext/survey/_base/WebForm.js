define([
  'dojo/_base/lang',
  'dojo/_base/array',
  'dojo/dom',
  'dojo/dom-construct',
  'dojo/dom-attr',
  'dojo/dom-class',
  'dojo/query',
  'bnext/survey/_code/BusinessUnitGridRelation',
  'bnext/survey/_code/FieldRulesDefinition',
  'bnext/survey/_code/DroppableBeforeActivate',
  'bnext/survey/_code/FormArea',
  'bnext/survey/lang/languages',
  'bnext/survey/lang/supported',
  'bnext/survey/QF',
  'bnext/survey/formObject/_setVisualData',
  'bnext/survey/data/SurveyType',
  'bnext/survey/formObject/_SetShowWeighting',
  'bnext/survey/formObject/_ShowWeighting',
  'bnext/survey/formObject/HtmlTextLine',
  'bnext/survey/formObject/ObjectArea',
  'bnext/survey/_code/page',
  'bnext/survey/_base/$SV',
  'bnext/survey/_base/Base',
  'bnext/survey/_base/LanguageBar',
  'bnext/survey/_base/Position',
  'bnext/survey/_util/locales',
  'bnext/survey/_util/Droppables',
  'bnext/survey/_util/DefaultValue',
  'bnext/survey/_base/FormConditional',
  'dijit/registry',
  'bnext/survey/formObject/field/GlobalSurveyData',
  'bnext/survey/_base/$H',
  'bnext/survey/formObject/GlobalPropertiesPositonManager',
  'dojo/on',
  'bnext/survey/_util/jsFailureExposure!',
  'bnext/survey/ext/eventExtend!',
  'bnext/survey/ext/elementExtend!',
  'bnext/survey/ext/elementMethodExtend!'
], (
  dojoLang,
  array,
  dom,
  domConstruct,
  domAttr,
  domClass,
  query,
  buGrid,
  fieldRulesDefinition,
  DroppableBeforeActivate,
  FormArea,
  lang,
  supportedLang,
  QF,
  setVisualData,
  surveyType,
  setShowWeighting,
  weighting,
  htmlTextLine,
  objectArea,
  page,
  $SV,
  Base,
  LanguageBar,
  Position,
  locales,
  Droppables,
  DefaultValue,
  FormConditional,
  registry,
  GlobalSurveyData,
  $H,
  GlobalPropertiesPositonManager,
  on
) => {
  const WebForm = Base.extend({
    constructor: function (parentId_, defaultLanguage, defaultTitle, selectedLanguage) {
      this.defaultTitle = defaultTitle;
      this.width = 500;
      this.formItems = [];
      this.conditionals = new FormConditional();
      this.parentId = parentId_;
      this.currentId = 0;
      this.title = new htmlTextLine(1, '25px');
      this.title.constructAsTextInput = true;
      this.title.cssClass = 'formTitle';
      this.title.id = 'formTitleText';
      this.title.itemText = this.setTitle(defaultTitle);
      this.languages = [];
      this.selectedLanguage = supportedLang.currentIndex(selectedLanguage || 0);
      this.languages[this.languages.length] = supportedLang.currentIndex(defaultLanguage || 0);
      this.langBar = new LanguageBar(this.selectedLanguage);
      this.pages = [];
      this.pages[this.pages.length] = 0;
      this.currentPage = 1;
      this.db_id = -1; //ID maestro de bdd
      this.global_id = -1;
      this.global_obj_id = -1;
      this.surveyType = surveyType.value;
      this.editableTitleGlobal = false;
    },
    getID: function () {
      return this.currentId++;
    },
    createHeaderArea: (parent, pos) => {
      const a = domConstruct.create(
        'div',
        {
          id: 'headerArea',
          formArea: 'headerArea',
          class: 'startArea'
        },
        typeof parent === 'string' ? dom.byId(parent) : parent,
        pos || 'last'
      );
      a.appendChild($span(lang.current('dropElementsHere_header')));
      Droppables.add('headerArea', {
        beforeActivate: DroppableBeforeActivate,
        hoverclass: 'droppFHArea',
        accept: 'candrop',
        onDrop: page.dropped,
        greedy: '1'
      });
    },
    createFormArea: (parent, pos) => {
      const a = domConstruct.create(
        'div',
        {
          id: 'formArea',
          formArea: 'formArea',
          class: 'startArea'
        },
        typeof parent === 'string' ? dom.byId(parent) : parent,
        pos || 'last'
      );
      a.appendChild($span(lang.current('dropElementsHere_body')));
      Droppables.add('formArea', {
        beforeActivate: DroppableBeforeActivate,
        hoverclass: 'dropp',
        accept: 'candrop',
        onDrop: page.dropped,
        greedy: '1'
      });
    },
    createFooterArea: (parent, pos) => {
      const a = domConstruct.create(
        'div',
        {
          id: 'footerArea',
          formArea: 'footerArea',
          class: 'startArea'
        },
        typeof parent === 'string' ? dom.byId(parent) : parent,
        pos || 'last'
      );
      a.appendChild($span(lang.current('dropElementsHere_footer')));
      Droppables.add('footerArea', {
        beforeActivate: DroppableBeforeActivate,
        hoverclass: 'droppFHArea',
        accept: 'candrop',
        onDrop: page.dropped,
        greedy: '1'
      });
    },
    render: function () {
      try {
        const parent = $SV(this.parentId);
        const formHeader = $div({
          id: 'formHeader'
        });
        parent.appendChild(formHeader);
        this.title.render(formHeader, null, null, null, 'none');
        this.langBar.render(formHeader);
        if (this.surveyType === 'request') {
          const headerHrMark = $hr({
            id: 'headerHrMark'
          });
          const footerHrMarl = $hr({
            id: 'footerHrMarl'
          });
          parent.appendChild(headerHrMark);
          this.createFormArea(parent);
          parent.appendChild(footerHrMarl);
          //header y footer
          this.createHeaderArea(headerHrMark, 'before');
          this.createFooterArea(footerHrMarl, 'after');
        } else {
          parent.appendChild($div({ id: 'formArea', class: 'startArea', formArea: 'formArea' }, $span(lang.current('dropElementsHere'))));
          Droppables.add('formArea', {
            hoverclass: 'dropp',
            accept: 'candrop',
            onDrop: page.dropped,
            greedy: '1'
          });
        }
        let initialParent = 'formArea';
        for (let i = 0; i < this.formItems.length; i++) {
          const oArea = this.formItems[i];
          const formArea = oArea.uiObject.formArea;
          oArea.formId = this.formIdByFormArea(oArea, formArea, initialParent);
          //console.log(oArea.id + ' - formArea: ' + formArea + ' - oArea.formId: ' + oArea.formId);
          oArea.render(!oArea.uiObject.location, formArea);
          if (!oArea.superParentId) {
            Droppables.remove(oArea.formId);
            $SV(initialParent).parentNode.removeChild($SV(oArea.formId));
          }
          if (dom.byId(formArea)) {
            initialParent = formArea;
          } else {
            initialParent = `${oArea.id}_down`;
          }
        }

        this.renderProperty(
          lang.current('editTitleAfterCreate'),
          'editableTitleGlobal',
          {
            true: lang.current('yes'),
            false: lang.current('no')
          },
          null,
          null,
          null,
          {
            domContainer: 'globalPropertiesArea'
          }
        );
        dom.byId('globalSidebarCloseBtn').innerHTML = lang.current('close');
        on(dom.byId('globalSidebarCloseBtn'), 'click', (e) => {
          GlobalPropertiesPositonManager.dialog.hide();
        });
      } catch (e) {
        console.log(e);
      }
    },
    getExternalCatalogsWithHierarchyValues: () => {
      const items = page.form.formItems;
      const results = [];
      if (!items?.length) {
        return results;
      }
      for (const item of items) {
        const uiObject = item?.uiObject;
        if (item.type === 'catalogSelect' && uiObject?.catalogType === 'externalCatalogId' && uiObject?.catalogSubType === 'catalog-hierarchy') {
          results.push({
            text: uiObject?.title?.getCurrentText(),
            value: item.id
          });
        }
      }
      return results;
    },
    geRestrictFormData: () => {
      const items = page.form.formItems;
      let result = null;
      if (!items?.length) {
        return result;
      }
      for (const item of items) {
        const uiObject = item?.uiObject;
        if (
          item.type === 'catalogSelect' &&
          uiObject?.catalogType === 'externalCatalogId' &&
          uiObject?.catalogSubType === 'catalog-hierarchy' &&
          uiObject?.businessUnitDepartmentMainField
        ) {
          result = {
            restrictRecordsField: item.id
          };
          return result;
        }
      }
      return result;
    },
    updateRestrictFormData: (requestEntity) => {
      const items = page.form.formItems;
      if (!items?.length) {
        return;
      }
      const businessUnitDepartmentMainField = requestEntity.restrictRecordsByDepartment && !requestEntity.validateAccessFormDepartment;
      for (const item of items) {
        const uiObject = item?.uiObject;
        if (item.type === 'catalogSelect' && uiObject.catalogType === 'externalCatalogId' && uiObject.catalogSubType === 'catalog-hierarchy') {
          if (businessUnitDepartmentMainField) {
            uiObject.businessUnitDepartmentMainField = item.id === requestEntity.restrictRecordsField;
          } else {
            uiObject.businessUnitDepartmentMainField = false;
          }
        }
      }
    },
    formIdByFormArea: (oArea, formArea, initialParent) => {
      let formId;
      if (formArea && dom.byId(formArea)) {
        formId = oArea.superParentId || formArea || initialParent;
      } else {
        formId = oArea.superParentId || initialParent;
      }
      return formId;
    },
    changeLanguage: function (selLang, dontSave) {
      if (!dontSave) this.save();
      this.deSelectObject();
      this.selectedLanguage = supportedLang.currentIndex(selLang || 0);
      this.title.changeLanguage(selLang);
      for (let i = 0; i < this.formItems.length; i++) {
        this.formItems[i].changeLanguage(this.selectedLanguage);
      }
      this.langBar.selectedLanguage = this.selectedLanguage;
    },
    removeLanguage: function () {
      if (this.languages.length > 1) {
        const lang = locales(this.languages[this.selectedLanguage]).language;
        if (confirm(`Are you sure you want to delete ${lang}?`)) {
          const index = this.selectedLanguage;
          this.languages.removeAt(index);
          this.title.removeLanguage(index);
          for (let i = 0; i < this.formItems.length; i++) this.formItems[i].removeLanguage(index);
          this.changeLanguage(0, 1);
          this.refresh(0);
        }
      }
    },
    addLanguage: function () {
      const lb = new QF.common.lightbox('New Language', 'mediumLB');
      const lang = new LanguageBar(0, this.selectedLanguage);
      lb.render(lang.addLanguage);
    },
    save: function () {
      this.title.save();
      for (let i = 0; i < this.formItems.length; i++) this.formItems[i].save();
    },
    clear: (parent_) => {
      const parent = parent_ || $SV('fieldArea');
      const dropDivs = document.getElementsByClassName('dropdiv');
      for (let i = 0; i < dropDivs.length; i++) {
        Droppables.remove(dropDivs[i].id);
      }
      if ($SV('formArea')) {
        Droppables.remove('formArea');
      }
      parent.innerHTML = '';
    },
    refresh: function (saveBeforeRefresh) {
      const parent = $SV('fieldArea');
      this.deSelectObject();
      if (saveBeforeRefresh) this.save();
      //this.clear(parent);
      //this.render(parent.id);
    },
    refreshClear: function (saveBeforeRefresh) {
      const parent = $SV('fieldArea');
      this.deSelectObject();
      if (saveBeforeRefresh) this.save();
      this.clear(parent);
      this.render(parent.id);
    },
    doesntShowsNumberDom: (element) =>
      element.parentNode.className.indexOf('subContainer') !== -1 ||
      !domClass.contains(element, 'formArea') ||
      !(domAttr.get(element, 'data-field-type') in fieldRulesDefinition.countableFields),
    doesntShowsNumber: (element) => {
      try {
        return element.itemStashParent || !(element.type in fieldRulesDefinition.countableFields);
      } catch (e) {
        console.error('Error en doesntShowsNumber', e);
        return false;
      }
    },
    getQuestionNumberByElement: function (clickedElement) {
      let index = 1;
      const formItems = page.form.formItems;
      for (let i = 0; i < formItems.length; i++) {
        if (formItems[i].id === clickedElement.id) {
          break;
        }
        if (!this.doesntShowsNumber(formItems[i])) {
          index++;
        }
      }
      return index;
    },
    selectObject: function (clickedElement, evt) {
      const doesntShowsNumber = this.doesntShowsNumberDom(clickedElement);
      if (evt && doesntShowsNumber) {
        evt.stopPropagation();
      }
      if (this.selectedElement) {
        if (this.selectedElement.id === clickedElement.id) {
          return;
        }
        this.deSelectObject();
      }
      this.selectedElement = clickedElement;
      const actionsDiv = $SV(`${clickedElement.id}_actions`);
      actionsDiv.show();
      const columnsDiv = $SV(`${clickedElement.id}_uiObject_columns`);
      if (columnsDiv) {
        columnsDiv.style.display = '';
        columnsDiv.show();
      }
      const fieldNumber = $SV(`${clickedElement.id}_number`);
      const index = this.getQuestionNumberByElement(clickedElement);
      fieldNumber.innerHTML = !doesntShowsNumber ? `${(index).toString()}.` : '';
      const newY = Position.cumulativeOffset(clickedElement)[1] - 26;
      const newX = Position.cumulativeOffset(clickedElement)[0] + 26;
      actionsDiv.setStyle({
        top: `${newY}px`,
        left: `${newX}px`
      });
      const obj = this.formItems[this.formItems.indexOfId(this.selectedElement.id)];
      obj.select(1);
    },
    deSelectObject: function () {
      if (this.selectedElement) {
        if ($SV(`${this.selectedElement.id}_actions`)) Element.hide(`${this.selectedElement.id}_actions`);
        if (dom.byId(`${this.selectedElement.id}_uiObject_columns`)) {
          Element.hide(`${this.selectedElement.id}_uiObject_columns`);
          dom.byId(`${this.selectedElement.id}_uiObject_columns`).style.display = 'none';
        }
        const obj = this.formItems[this.formItems.indexOfId(this.selectedElement.id)];
        if (obj) {
          obj.select(0);
        }
        this.resetProperties();
        $SV('propertiesArea').innerHTML = `<div class="empty-properties">${lang.current('clickelement')}</div>`;
        this.selectedElement = null;
      }
    },
    resetProperties: () => {
      //destroys DOM
      const parent = $SV('propertiesArea');
      while (parent.firstChild) {
        parent.removeChild(parent.firstChild);
      }
      //destroys DOJO events
      if (!parent.events) {
        return;
      }
      for (const eKey in parent.events) {
        if (!Object.hasOwn(parent.events, eKey)) continue;
        parent.events[eKey].remove?.();
      }
    },
    deleteObject: function (id) {
      const index = +this.formItems.indexOfId(id);
      const formItem = this.formItems[index].uiObject;
      const isSection = formItem.type === 'seccion';
      const formItemFormArea = formItem.formArea;
      const isSubQuestion = formItemFormArea === null;
      formItem.destructor();
      this.formItems.removeAt(index);
      this.conditionals.deleteById(id);
      this.resetProperties();
      this.selectedElement = null;
      if (typeof formItem.removeItems === 'function') {
        formItem.removeItems(formItem, formItemFormArea);
      }
      domConstruct.destroy(dom.byId(id));
      let idField2 = this.formItems[index - 1];
      if (isSubQuestion) {
        return;
      }
      if (idField2) {
        idField2 = idField2.id;
      } else {
        idField2 = 0;
      }
      const formAreaItems = FormArea.getItemsByArea(this.formItems, formItemFormArea);
      if (this.formItems.length === 0) {
        const titleDiv = document.getElementById('formTitleText');
        if (titleDiv) {
          this.title.itemText = this.setTitle(titleDiv.value);
        }
        //crea starArea
        this.refreshClear();
      } else if (formAreaItems.length === 0) {
        this.deSelectObject();
        //destruir dropppables resagados
        for (const doom of query(`.drop${formItemFormArea}`)) {
          Droppables.remove(doom.formId);
          domConstruct.destroy(doom);
        }
        //reconstruir el area
        switch (formItemFormArea) {
          case 'headerArea':
            this.createHeaderArea(dom.byId('headerHrMark'), 'before');
            break;
          case 'formArea':
            this.createFormArea(dom.byId('footerHrMarl'), 'before');
            break;
          case 'footerArea':
            this.createFooterArea(dom.byId('footerHrMarl'), 'after');
            break;
        }
      } else if (index === 0 && formAreaItems.length !== index) {
        if (dom.byId(`${id}_down`)) {
          domConstruct.destroy(dom.byId(`${id}_down`));
        }
        if (!dom.byId(`${formAreaItems[index].id}_up`)) {
          dom.byId(`${id}_up`).id = `${formAreaItems[index].id}_up`;
        } else {
          domConstruct.destroy(dom.byId(`${id}_up`));
        }
      } else if (formAreaItems.length === index || (dom.byId(`${id}_down`) && dom.byId(`${id}_up`) && formAreaItems.length !== index)) {
        if (dom.byId(`${id}_up`)) {
          domConstruct.destroy(dom.byId(`${id}_up`));
        }
        if (dom.byId(`${id}_down`) && dom.byId(idField2) && !dom.byId(`${idField2}_down`)) {
          dom.byId(`${id}_down`).id = `${idField2}_down`;
        } else if (dom.byId(`${idField2}_down`)) {
          domConstruct.destroy(dom.byId(`${id}_down`));
        }
      } else if (!dom.byId(`${id}_down`) && !dom.byId(`${id}_up`)) {
        domConstruct.destroy(dom.byId(`${formAreaItems[index].id}_up`));
      } else if (dom.byId(`${id}_down`) && dom.byId(`${id}_up`)) {
        domConstruct.destroy(dom.byId(`${id}_up`));
      } else {
        if (dom.byId(`${id}_down`)) {
          domConstruct.destroy(dom.byId(`${id}_down`));
        }
        if (dom.byId(`${id}_up`)) {
          domConstruct.destroy(dom.byId(`${id}_up`));
        }
      }
      if (isSection) {
        DefaultValue.updateSectionNumber();
      }
    },
    copyObject: function (id) {
      const sel = this.selectedElement;
      this.deSelectObject();
      const index = this.formItems.indexOfId(id);
      this.formItems[index].save();
      const copy = this.formItems[index].getCopy();
      this.formItems.insertAt(copy, index + 1);
      this.selectedElement = sel;
      this.refresh(1);
    },
    addObjConditionalFields: function (obj) {
      this.conditionals.addObjFields(obj);
    },
    addRowsConditionalFields: function (sourceFieldId, conditions) {
      this.conditionals.addRowsFields(sourceFieldId, conditions);
    },
    removeConditionalFields: function (sourceFeldId, conditions) {
      this.conditionals.removeFields(sourceFeldId, conditions);
    },
    hasConditionalField: function (targetFieldId) {
      const result = this.conditionals.isConfiguredField(targetFieldId, this.formItems);
      return result;
    },
    hasConditionalAnswer: function (targetFieldId, targetAnswerId) {
      const result = this.conditionals.isConfiguredAnswer(targetFieldId, targetAnswerId);
      return result;
    },
    destroyCurrentAnswerSelector: function () {
      if (!this.conditionals || !this.conditionals.currentAnswerSelectorId) {
        return;
      }
      const widget = registry.byId(this.conditionals.currentAnswerSelectorId);
      if (widget) {
        widget.destroyRecursive();
      }
    },
    validateOnlyOneAnswerSelector: function (answerSelectorId) {
      if (this.conditionals.currentAnswerSelectorId && answerSelectorId !== this.conditionals.currentAnswerSelectorId) {
        this.destroyCurrentAnswerSelector();
      }
    },
    changeAnswerSelector: function (answerSelectorId) {
      this.conditionals.currentAnswerSelectorId = answerSelectorId;
    },
    getStashLabel: (index, subItem) => dojoLang.getObject(index, false, subItem) || '',
    setStashLabels: function (schema, stashContainer, cellId) {
      const exp = 'field[0-9]+_uiObject_array_([0-9]+_[0-9]+)_cell';
      let title = schema.title.title[0] || '';
      let plainTitle = schema.title.plainTitle || '';
      if (cellId.match(exp)) {
        const coords = cellId.match(exp)[1].split('_');
        const x = coords[0];
        const y = coords[1];
        const b = this.getStashLabel;
        title = `${b(`uiObject.items.${x}.itemText.itemText.title.0`, stashContainer)}, ${b(`uiObject.headerTitles.${y}.itemText.title.0`, stashContainer)}`;
        plainTitle = `${b(`uiObject.items.${x}.itemText.itemText.plainTitle`, stashContainer)}, ${b(`uiObject.headerTitles.${y}.itemText.plainTitle`, stashContainer)}`;
      }
      schema.title.title[0] = title;
      schema.title.plainTitle = plainTitle;
    },
    getSchema: function () {
      this.save();
      const obj = {};
      const globals = {};
      console.log('obteniendo datos del encabezado...');
      globals.clave = !this.clave ? null : `${this.clave}`;
      globals.title = this.title.getSchema();
      globals.editableTitleGlobal = this.editableTitleGlobal;
      globals.currentId = this.currentId;
      globals.db_id = dom.byId('id').value;
      globals.global_id = this.global_id;
      globals.estatus = this.estatus;
      globals.esquemaTienda = this.esquemaTienda;

      globals.unes = buGrid.added?.getBean()?.data ? buGrid.added.getBean().data : this.unes;

      globals.rol_si = this.rol_si;
      globals.rol_no = this.rol_no;
      globals.hasWeight = weighting() ? 1 : 0;
      globals.global_obj_id = this.global_obj_id;
      globals.languages = this.languages;
      globals.height = $SV('fieldArea').clientHeight;
      globals.width = this.width;
      this.pages[page.survey_page - 1] = this.formItems.length;
      globals.pages = this.pages;
      obj.globals = globals;
      obj.globals.type = this.surveyType;
      obj.globals.request = this.request || null;
      obj.items = [];
      let schemaSave = 0;
      let item;
      let subItem;
      let subItemSchema;
      const usedIds = [];
      let x;
      let location;
      let numeration = 1;

      for (let i = 0; i < this.formItems.length; i++) {
        item = this.formItems[i];
        if (item.isDomAvailable && !item.isDomAvailable()) {
          console.warn(`Object with id "${item.id}" dom is missing.`);
          continue;
        }
        schemaSave = item.getSchema();
        if (schemaSave && usedIds.indexOf(item.id) === -1) {
          schemaSave.order = numeration;
          numeration++;
          schemaSave.id = item.id;
          if (item.uiObject.itemStash) {
            schemaSave.itemStash = [];
            for (x in item.uiObject.itemStash) {
              subItem = item.uiObject.itemStash[x];
              if (subItem.isDomAvailable && !subItem.isDomAvailable()) {
                console.warn(`Object with id "${subItem.id}" is referenced from "${item.id}" but dom is missing.`);
                continue;
              }
              subItemSchema = subItem.getSchema();
              if (!(subItemSchema.location || '').match(/[0-9]+_[0-9]+/)) {
                location = x.substring(x.indexOf('_array_') + 7);
                location = location.substring(0, location.lastIndexOf('_'));
                subItemSchema.location = location;
              }
              subItemSchema.id = subItem.id;
              this.setStashLabels(subItemSchema, subItem.sParent || item, x);
              schemaSave.itemStash.push(subItemSchema);
              usedIds.push(subItem.id);
            }
          }
          usedIds.push(item.id);
          obj.items.push(schemaSave);
        }
      }

      //Logica para el dialogo nuevo.
      return obj;
    },
    setSchema: function (obj) {
      let x;
      let l;
      let i;
      let globals;
      let tt;
      let stash;
      let order;
      let field;
      let subFields;
      let item;
      if (obj) {
        this.db_id = obj.id ? obj.id : -1;
        this.esquemaTienda = obj.globals?.obj?.esquemaTienda ? obj.globals.obj.esquemaTienda : [];
        this.rol_si = obj.globals?.obj?.rol_si ? obj.globals.obj.rol_si : 0;
        this.rol_no = obj.globals?.obj?.rol_no ? obj.globals.obj.rol_no : 0;
        this.unes = obj.globals?.obj?.unes ? obj.globals.obj.unes : [];
        this.estatus = obj.estatus || obj.estatus === 0 ? obj.estatus : 1;
        this.clave = obj.globals?.clave ? obj.globals.clave : -1;
        this.global_id = obj.globals?.id ? obj.globals.id : -1;
        this.global_obj_id = obj.globals?.obj?.id ? obj.globals.obj.id : dom.byId('globalId')?.value ? dom.byId('globalId').value : -1;

        setShowWeighting(!!obj.globals?.obj?.hasWeight);

        if (obj.globals?.obj) {
          globals = obj.globals.obj;
          globals.title && this.title.setSchema(globals.title);

          this.currentId = globals.currentId || 0;
          this.languages = ['es-MX']; //globals.languages||this.languages;
          this.pages = globals.pages || new Array(this.formItems.length);
          this.survey_id = globals.survey_id;
          this.setLimits(globals);
          this.editableTitleGlobal = obj.globals.obj.editableTitle;
        }
        if (obj.fields) {
          subFields = [];
          for (i = 0; i < obj.fields.length; i++) {
            field = obj.fields[i];
            order = field.obj.order - 1;
            this.formItems[order] = new objectArea(field.obj.formArea, field.type);
            this.formItems[order].setSchema(field.type, field);
            this.formItems[order].uiObject.filledRequiredProperties = true;
            this.formItems[order].uiObject.filledWeigthingProperties = true;
            if (field.obj.itemStash && field.obj.itemStash.length !== 0) {
              stash = field.obj.itemStash;
              for (x = 0, l = stash.length; x < l; x++) {
                item = stash[x];
                tt = new objectArea(null, field.type);
                tt.setSchema(item.type, item);
                tt.sParent = this.formItems[order];
                tt.superParentId = `${field.obj.field_id}_uiObject_array_${item.obj.location}_drop`;
                subFields.push(tt);
              }
            }
          }
          for (x = 0, l = subFields.length; x < l; x++) {
            this.formItems.push(subFields[x]);
          }
        }
        if (setVisualData) {
          setVisualData(this);
        }
      }
      /**
       * Aqui hace el render de todo!
       */
      this.refreshClear(0);
      if (subFields?.length) {
        for (i = 0, l = subFields.length; i < l; i++) {
          item = subFields[i];
          if (item.uiObject.type === 'htmlTableFieldItem') {
            item.sParent.uiObject.saveForItem(item.superParentId, item);
          } else {
            item.sParent.uiObject.saveForItem(item.superParentId.replace('_drop', '_cell'), item);
          }
        }
      }
    },
    resetSchema: function () {
      this.formItems = 0;
      this.formItems = [];
      this.conditionals.reset();
      return true;
    },
    setLimits: function (limits) {
      this.multiPage = limits.multiPage || this.multiPage || 0;
      this.multiLang = limits.multiLang || this.multiLang || 0;
      this.questions = limits.questions || this.questions || 1000;
      this.fileUpload = limits.fileUpload || this.fileUpload || 0;
      this.mapSelect = limits.mapSelect || this.mapSelect || 0;
      this.videoField = limits.videoField || this.videoField || 0;
      this.imageField = limits.imageField || this.imageField || 0;
    },
    setTitle(value) {
      if (value === '') {
        return { title: [this.defaultTitle], weight: -1, id: -1, answerType: [], defaultValue: [] };
      }
      return { title: [value], weight: -1, id: -1, answerType: 1, defaultValue: '' };
    }
  });

  return WebForm;
});
