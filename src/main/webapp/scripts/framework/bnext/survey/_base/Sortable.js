define([
    'bnext/survey/_base/Prototype',
    'bnext/survey/_base/$SV', 'bnext/survey/_base/Position', 
    'bnext/survey/_util/Draggables',
    'bnext/survey/_util/Droppables',
    'bnext/survey/_util/jsFailureExposure!', 'bnext/survey/ext/objectExtend!', 'bnext/survey/ext/elementExtend!', 'bnext/survey/ext/eventExtend!',
    'bnext/survey/ext/elementMethodExtend!'
],
        function(Prototype, $SV, Position, Draggables, Droppables) {

            var Sortable = {
                SERIALIZE_RULE: /^[^_\-](?:[A-Za-z0-9\-\_]*)[_](.*)$/,
                sortables: {},
                _findRootElement: function(a) {
                    while (a.tagName.toUpperCase() != "BODY") {
                        if (a.id && Sortable.sortables[a.id]) {
                            return a
                        }
                        a = a.parentNode
                    }
                },
                options: function(a) {
                    a = Sortable._findRootElement($SV(a));
                    if (!a) {
                        return
                    }
                    return Sortable.sortables[a.id]
                },
                destroy: function(a) {
                    var s = Sortable.options(a);
                    if (s) {
                        Draggables.removeObserver(s.element);
                        s.droppables.each(function(d) {
                            Droppables.remove(d)
                        });
                        s.draggables.invoke("destroy");
                        delete Sortable.sortables[s.element.id]
                    }
                },
                create: function(b) {
                    b = $SV(b);
                    var c = Object.extend({
                        element: b,
                        tag: "li",
                        dropOnEmpty: 0,
                        tree: 0,
                        treeTag: "ul",
                        overlap: "vertical",
                        constraint: "vertical",
                        containment: b,
                        handle: 0,
                        only: 0,
                        delay: 0,
                        hoverclass: null,
                        ghosting: 0,
                        quiet: 0,
                        scroll: 0,
                        scrollSensitivity: 20,
                        scrollSpeed: 15,
                        format: this.SERIALIZE_RULE,
                        onChange: Prototype.emptyFunction,
                        onUpdate: Prototype.emptyFunction
                    }, arguments[1] || {});
                    this.destroy(b);
                    var d = {
                        revert: 1,
                        quiet: c.quiet,
                        scroll: c.scroll,
                        scrollSpeed: c.scrollSpeed,
                        scrollSensitivity: c.scrollSensitivity,
                        delay: c.delay,
                        ghosting: c.ghosting,
                        constraint: c.constraint,
                        handle: c.handle
                    };

                    if (c.starteffect) {
                        d.starteffect = c.starteffect
                    }
                    if (c.reverteffect) {
                        d.reverteffect = c.reverteffect
                    } else {
                        if (c.ghosting) {
                            d.reverteffect = function(a) {
                                a.style.top = 0;
                                a.style.left = 0
                            }
                        }
                    }
                    if (c.endeffect) {
                        d.endeffect = c.endeffect
                    }
                    if (c.zindex) {
                        d.zindex = c.zindex
                    }
                    var f = {
                        overlap: c.overlap,
                        containment: c.containment,
                        tree: c.tree,
                        hoverclass: c.hoverclass,
                        onHover: Sortable.onHover
                    };

                    var g = {
                        onHover: Sortable.onEmptyHover,
                        overlap: c.overlap,
                        containment: c.containment,
                        hoverclass: c.hoverclass
                    };

                    Element.cleanWhitespace(b);
                    c.draggables = [];
                    c.droppables = [];
                    if (c.dropOnEmpty || c.tree) {
                        Droppables.add(b, g);
                        c.droppables.push(b)
                    }
                    (this.findElements(b, c) || []).each(function(e) {
                        var a = c.handle ? $SV(e).down("." + c.handle, 0) : e;
                        c.draggables.push(new Draggable(e, Object.extend(d, {
                            handle: a
                        })));
                        Droppables.add(e, f);
                        if (c.tree) {
                            e.treeNode = b
                        }
                        c.droppables.push(e)
                    });
                    if (c.tree) {
                        (Sortable.findTreeElements(b, c) || []).each(function(e) {
                            Droppables.add(e, g);
                            e.treeNode = b;
                            c.droppables.push(e)
                        })
                    }
                    this.sortables[b.id] = c;
                    Draggables.addObserver(new SortableObserver(b, c.onUpdate))
                },
                findElements: function(a, b) {
                    return Element.findChildren(a, b.only, b.tree ? 1 : 0, b.tag)
                },
                findTreeElements: function(a, b) {
                    return Element.findChildren(a, b.only, b.tree ? 1 : 0, b.treeTag)
                },
                onHover: function(a, b, c) {
                    if (Element.isParent(b, a)) {
                        return
                    }
                    if (c > 0.33 && c < 0.66 && Sortable.options(b).tree) {
                        return
                    } else {
                        if (c > 0.5) {
                            Sortable.mark(b, "before");
                            if (b.previousSibling != a) {
                                var d = a.parentNode;
                                a.style.visibility = "hidden";
                                b.parentNode.insertBefore(a, b);
                                if (b.parentNode != d) {
                                    Sortable.options(d).onChange(a)
                                }
                                Sortable.options(b.parentNode).onChange(a)
                            }
                        } else {
                            Sortable.mark(b, "after");
                            var e = b.nextSibling || null;
                            if (e != a) {
                                var f = a.parentNode;
                                a.style.visibility = "hidden";
                                b.parentNode.insertBefore(a, e);
                                if (b.parentNode != f) {
                                    Sortable.options(f).onChange(a)
                                }
                                Sortable.options(b.parentNode).onChange(a)
                            }
                        }
                    }
                },
                onEmptyHover: function(a, b, c) {
                    var d = a.parentNode;
                    var e = Sortable.options(b);
                    if (!Element.isParent(b, a)) {
                        var f;
                        var g = Sortable.findElements(b, {
                            tag: e.tag,
                            only: e.only
                        });
                        var h = null;
                        if (g) {
                            var i = Element.offsetSize(b, e.overlap) * (1 - c);
                            for (f = 0; f < g.length; f += 1) {
                                if (i - Element.offsetSize(g[f], e.overlap) >= 0) {
                                    i -= Element.offsetSize(g[f], e.overlap)
                                } else {
                                    if (i - (Element.offsetSize(g[f], e.overlap) / 2) >= 0) {
                                        h = f + 1 < g.length ? g[f + 1] : null;
                                        break
                                    } else {
                                        h = g[f];
                                        break
                                    }
                                }
                            }
                        }
                        b.insertBefore(a, h);
                        Sortable.options(d).onChange(a);
                        e.onChange(a)
                    }
                },
                unmark: function() {
                    if (Sortable._marker) {
                        Sortable._marker.hide()
                    }
                },
                mark: function(a, b) {
                    var c = Sortable.options(a.parentNode);
                    if (c && !c.ghosting) {
                        return
                    }
                    if (!Sortable._marker) {
                        Sortable._marker = ($SV("dropmarker") || Element.extend(document.createElement("DIV"))).hide().addClassName("dropmarker").setStyle({
                            position: "absolute"
                        });
                        document.getElementsByTagName("body").item(0).appendChild(Sortable._marker)
                    }
                    var d = Position.cumulativeOffset(a);
                    Sortable._marker.setStyle({
                        left: d[0] + "px",
                        top: d[1] + "px"
                    });
                    if (b == "after") {
                        if (c.overlap == "horizontal") {
                            Sortable._marker.setStyle({
                                left: (d[0] + a.clientWidth) + "px"
                            })
                        } else {
                            Sortable._marker.setStyle({
                                top: (d[1] + a.clientHeight) + "px"
                            })
                        }
                    }
                    Sortable._marker.show()
                },
                _tree: function(a, b, c) {
                    var d = Sortable.findElements(a, b) || [];
                    for (var i = 0; i < d.length; ++i) {
                        var e = d[i].id.match(b.format);
                        if (!e) {
                            continue
                        }
                        var f = {
                            id: encodeURIComponent(e ? e[1] : null),
                            element: a,
                            parent: c,
                            children: [],
                            position: c.children.length,
                            container: $SV(d[i]).down(b.treeTag)
                        };

                        if (f.container) {
                            this._tree(f.container, b, f)
                        }
                        c.children.push(f)
                    }
                    return c
                },
                tree: function(a) {
                    a = $SV(a);
                    var b = this.options(a);
                    var c = Object.extend({
                        tag: b.tag,
                        treeTag: b.treeTag,
                        only: b.only,
                        name: a.id,
                        format: b.format
                    }, arguments[1] || {});
                    var d = {
                        id: null,
                        parent: null,
                        children: [],
                        container: a,
                        position: 0
                    };

                    return Sortable._tree(a, c, d)
                },
                _constructIndex: function(a) {
                    var b = "";
                    do {
                        if (a.id) {
                            b = "[" + a.position + "]" + b
                        }
                    } while ((a = a.parent) != null);
                    return b
                },
                sequence: function(b) {
                    b = $SV(b);
                    var c = Object.extend(this.options(b), arguments[1] || {});
                    return $SV(this.findElements(b, c) || []).map(function(a) {
                        return a.id.match(c.format) ? a.id.match(c.format)[1] : ""
                    })
                },
                setSequence: function(b, c) {
                    b = $SV(b);
                    var d = Object.extend(this.options(b), arguments[2] || {});
                    var e = {};

                    this.findElements(b, d).each(function(n) {
                        if (n.id.match(d.format)) {
                            e[n.id.match(d.format)[1]] = [n, n.parentNode]
                        }
                        n.parentNode.removeChild(n)
                    });
                    c.each(function(a) {
                        var n = e[a];
                        if (n) {
                            n[1].appendChild(n[0]);
                            delete e[a]
                        }
                    })
                },
                serialize: function(b) {
                    b = $SV(b);
                    var c = Object.extend(Sortable.options(b), arguments[1] || {});
                    var d = encodeURIComponent((arguments[1] && arguments[1].name) ? arguments[1].name : b.id);
                    if (c.tree) {
                        return Sortable.tree(b, arguments[1]).children.map(function(a) {
                            return[d + Sortable._constructIndex(a) + "[id]=" + encodeURIComponent(a.id)].concat(a.children.map(arguments.callee))
                        }).flatten().join("&")
                    } else {
                        return Sortable.sequence(b, arguments[1]).map(function(a) {
                            return d + "[]=" + encodeURIComponent(a)
                        }).join("&")
                    }
                }
            };
            return Sortable;
        });