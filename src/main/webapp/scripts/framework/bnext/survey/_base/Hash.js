define(['bnext/survey/_util/Enumerable', 'bnext/survey/_base/$H', 'bnext/survey/_util/jsFailureExposure!', 'bnext/survey/ext/objectExtend!'],
    function(Enumerable, $H) {

    var Hash = function(a) {
        if (a instanceof Hash) {
            this.merge(a)
        } else {
            Object.extend(this, a || {})
        }
    };

    Object.extend(Hash, {
        toQueryString: function(d) {
            var e = [];
            e.add = arguments.callee.addPair;
            this.prototype._each.call(d, function(b) {
                if (!b.key) {
                    return
                }
                var c = b.value;
                if (c && typeof c == "object") {
                    if (c.constructor == Array) {
                        c.each(function(a) {
                            e.add(b.key, a)
                        })
                    }
                    return
                }
                e.add(b.key, c)
            });
            return e.join("&")
        }
    });
    Hash.toQueryString.addPair = function(a, b, c) {
        a = encodeURIComponent(a);
        if (b === undefined) {
            this.push(a)
        } else {
            this.push(a + "=" + (b == null ? "" : encodeURIComponent(b)))
        }
    };

    Object.extend(Hash.prototype, Enumerable);
    Object.extend(Hash.prototype, {
        _each: function(a) {
            for (var b in this) {
                var c = this[b];
                if (c && c == Hash.prototype[b]) {
                    continue
                }
                var d = [b, c];
                d.key = b;
                d.value = c;
                a(d)
            }
        },
        keys: function() {
            return this.pluck("key")
        },
        values: function() {
            return this.pluck("value")
        },
        merge: function(c) {
            return $H(c).inject(this, function(a, b) {
                a[b.key] = b.value;
                return a
            })
        },
        remove: function() {
            var a;
            for (var i = 0, length = arguments.length; i < length; i++) {
                var b = this[arguments[i]];
                if (b !== undefined) {
                    if (a === undefined) {
                        a = b
                    } else {
                        if (a.constructor != Array) {
                            a = [a]
                        }
                        a.push(b)
                    }
                }
                delete this[arguments[i]]
            }
            return a
        },
        toQueryString: function() {
            return Hash.toQueryString(this)
        },
        inspect: function() {
            return"#<Hash:{" + this.map(function(a) {
                return a.map(Object.inspect).join(": ")
            }).join(", ") + "}>"
        }/*,
         toJSON:function(){
         return Hash.toJSON(this)
         }*/
    });

    if (function() {
        var i = 0, Test = function(a) {
            this.key = a
        };

        Test.prototype.key = "foo";
        for (var b in new Test("bar")) {
            i++
        }
        return i > 1
    }()) {
        Hash.prototype._each = function(a) {
            var b = [];
            for (var c in this) {
                var d = this[c];
                if ((d && d == Hash.prototype[c]) || b.include(c)) {
                    continue
                }
                b.push(c);
                var e = [c, d];
                e.key = c;
                e.value = d;
                a(e)
            }
        }
    }
    return Hash;
});