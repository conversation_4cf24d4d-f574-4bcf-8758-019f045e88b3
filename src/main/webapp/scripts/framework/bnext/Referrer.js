define(["dojo/_base/declare","dijit/_WidgetBase","dijit/_TemplatedMixin","dijit/_WidgetsInTemplateMixin",
    "dojo/text!./templates/Referrer.html", "dojo/store/Memory", 'dojo/dom-construct',"dojo/_base/array",
    'xstyle/css!bnext/styles/referrer.css'],
function(declare,_WB,_TM,wt,tem,Memory,domC,array){ 
        var data ={};
        function pushListElement(element,parent,searchAttr){
             var list = array.forEach(element,function(item){
                        console.log(element[item])
                        list+=element[item];
             });
             var listContainer = domC.create('ul',{'class':'selected_items'},parent);
             domC.create('li',{innerHTML:element[searchAttr.first],'class':'code'},listContainer);
             domC.create('li',{innerHTML:element[searchAttr.second],'class':'description'},listContainer);
             var listImage = domC.create('li',{'class':'remove_button'},listContainer);
             domC.create('a',{'class':'remove_Element referrer_button'},listImage)
             data.remove(element.id);
        }
        Referrer = {
            templateString:tem,
            tags:{first:'',second:''},
            searchAttr:{first:'',second:''},
            data:[],
            preloaded:[],
            postCreate:function(){                
                data = new Memory({data:this.data});
                console.log(this.preloaded)
                console.log(this.preloaded.length)
                if(this.preloaded.length>0){
                    array.forEach(data,function(item){
                        console.log(item);
                    });
                }
                this.firstFilter.set('store', data);
                this.firstFilter.set('searchAttr', this.searchAttr.first);
                this.secondFilter.set('store', data);
                this.secondFilter.set('searchAttr', this.searchAttr.second);
                
                if(data.data.length > 0){
                    this.firstFilter.set('value',data.data[0].id);
                }
            },addElement:function(evt){
                if(data.data.length > 0){
                    var element = data.get(this.firstFilter.get('value'));
                   pushListElement(element,this.actual_elements,this.searchAttr);
                   if(data.data.length > 0){
                        this.firstFilter.set('value',data.data[0].id);
                    }
                }            
            },
            _syncFirst:function(){
                this.secondFilter.set('value',this.firstFilter.get('value'));
            },
            _syncSecond:function(){
                 this.firstFilter.set('value',this.secondFilter.get('value'));
            }
        }
        return Referrer = declare([_WB,_TM,wt],Referrer);
        
    });