define([
  'dojo/Deferred',
  'dojo/on',
  'bnext/angularElements',
  'dojo/date/locale',
  'dojo/dom-class',
  'dojo/dom-attr',
  'dojo/dom',
  'dojo/dom-construct',
  'bnext/_base/media-queries',
  'bnext/survey/_util/jsFailureExposure!',
  'bnext/plugins/fixCypressWindowTop!',
  'dojo/domReady!'
], (Deferred, on, angularElements, locale, domClass, domAttr, dom, domConstruct, MediaQueries) => {
  const isPhoneDevice = MediaQueries.isIPhone() || MediaQueries.isAndroid();
  if (!angularElements.isElementsAvailable()) {
    console.error('angularTimePicker called in a window without Angular');
    return {
      angularTimePickerSetValue: () => {
        console.error('angularElments not avainable, call it please from angular context. (navigateLegacy)');
        return {};
      }
    };
  }
  const angularTimePicker = {
    def: null,
    event: null,
    close: () => {
      angularTimePicker.flush();
      angularElements.killTimePicker();
      if (angularTimePicker.def) {
        angularTimePicker.def.reject();
      }
    },
    open: (value, minValue, maxValue) => {
      angularTimePicker.flush();
      angularTimePicker.def = new Deferred();
      angularElements.timePicker().then((element) => {
        const angularTimePickerDom = element.domNode;
        if (value) {
          angularTimePickerDom.value = value;
        } else {
          angularTimePickerDom.value = null;
        }
        if (maxValue || minValue) {
          angularTimePickerDom.minValue = minValue || null;
          angularTimePickerDom.maxValue = maxValue || null;
          angularTimePickerDom.isAvailableRange = true;
        }
        angularTimePicker.event = on(angularTimePickerDom, 'change', (time) => {
          angularTimePicker.def.resolve(time.detail || null);
        });
        angularTimePickerDom.isOpen = true;
      });
      return angularTimePicker.def.promise;
    },
    flush: () => {
      if (angularTimePicker.event) {
        angularTimePicker.event.remove();
      }
    }
  };

  function angularTimePickerSetValue(properties) {
    if (!properties.formatSave) {
      properties.formatSave = 'HH:mm';
    }
    if (!properties.formatView) {
      properties.formatView = 'HH:mm';
    }
    if (isPhoneDevice) {
      properties.input.type = 'time';
      domAttr.remove(properties.input, 'readonly');
      return on(properties.input, 'change', (e) => {
        if (properties.input.disabled) {
          return;
        }
        const time = properties.input.value;
        setTimeValue(properties.input, time || null, properties.formatSave);
      });
    }
    return on(properties.input.parentNode, 'click', (e) => {
      let currentValue = null;
      if (properties.input.value) {
        const options = {
          selector: 'date',
          datePattern: properties.formatView
        };
        currentValue = locale.parse(properties.input.value, options);
      }
      if (properties.input.disabled) {
        return;
      }
      angularTimePicker.open(currentValue, properties.minValue, properties.maxValue).then((time) => {
        setTimeValue(properties.input, time || null, properties.formatSave);
      });
    });
  }
  function setTimeValue(timeInput, time, resultFormat) {
    timeInput.readonly = false;
    let valueInput = timeInput;
    const validInputId = domAttr.get(timeInput, 'data-value-input');
    if (resultFormat && !validInputId) {
      valueInput = domConstruct.create(
        'input',
        {
          name: timeInput.name,
          id: timeInput.id + new Date().getTime(),
          type: 'hidden'
        },
        timeInput,
        'before'
      );
      domAttr.remove(timeInput, 'name');
      domAttr.set(timeInput, 'data-value-input', valueInput.id);
    } else if (validInputId) {
      valueInput = dom.byId(validInputId);
    }

    if (timeInput.type === 'time') {
      timeInput.readonly = false;
      domClass.remove(timeInput, 'readonly');
      if (resultFormat !== 'HH:mm') {
        console.error('Standard format is HH:mm!!');
      }
    }

    if (time && timeInput.type === 'time') {
      valueInput.value = timeInput.value = time;
    } else if (time) {
      const preTime = valueInput.value;
      timeInput.value = locale.format(time, { selector: 'date', datePattern: 'HH:mm' });
      valueInput.value = locale.format(time, { selector: 'date', datePattern: resultFormat });
      if (preTime !== valueInput.value) {
        const event = new Event('change');
        timeInput.dispatchEvent(event);
      }
      timeInput.readonly = true;
      domClass.add(timeInput, 'readonly');
    } else if (timeInput.type === 'time') {
      timeInput.value = '';
      valueInput.value = '';
    } else {
      timeInput.value = '';
      valueInput.value = '';
      timeInput.readonly = true;
      domClass.remove(timeInput, 'readonly');
    }
  }
  angularTimePicker.angularTimePickerSetValue = angularTimePickerSetValue;
  return angularTimePicker;
});
