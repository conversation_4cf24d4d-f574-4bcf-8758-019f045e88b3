dojo.provide("bnext.AttachFile");
dojo.require("bnext.file");
dojo.require("bnext.saveHandleUtil");

dojo.require("dojo.io.iframe");
dojo.require("dojox.html._base");
dojo.require("dojo.html");
dojo.require("dojox.html.entities");
 
uploadfile = function(args) {
    log('new uploadfile() ... ')
    this.uploadAtt = args.uploadAtt;
    this.miForma = {"html" : ""
                + "<form id='Upload' accept-charset='windows-1252;UTF-8;ISO-' jsId='Upload' dojoType='dijit.form.Form' name='Upload' action='Upload.fileUploader' method='POST' enctype='multipart/form-data'>"
                    + "<table border='0'>"
                        + "<tr>"
                            + "<td>"
                                + "<input type='file' name='" + this.uploadAtt + "' value='' id='" + this.uploadAtt + "'/>"
                            + "</td>"
                        + "</tr>"
                        + "<tr>"
                            + "<td>"
                                + "<div align='center'>"
                                    + "<button class='btn btnregresar' onClick='cancelAtach();' type='button' >"
                                        + "Cancelar"
                                    + "</button>"
                                    + "<button class='btn btnlimpiar' type='submit' id='Upload_0' >"
                                        + "Anexar"
                                    + "</button>"
                                + "</div>"
                            + "</td>"
                        + "</tr>"
                    + "</table>"
                    + "<input type='hidden' name='daoHibernate' value='{clave}' id='Upload_nameBackup'/>"
                    + "<input type='hidden' name='clave' value='{clave}' id='Upload_clave'/>"
                    + "<input type='hidden' name='resClave' value='{resClave}' id='Upload_resClave'/>"
                    + "<input type='hidden' name='llenClave' value='{llenClave}' id='Upload_llenClave'/>"
                    + "<input type='hidden' name='formaClave' value='{formaClave}' id='Upload_formaClave'/>"
                    + "<input type='hidden' name='tipo' value='{tipo}' id='Upload_tipo'/>"
                    + "<input type='hidden' name='solfolio' value='{solfolio}' id='Upload_solfolio'/>"
                + "</form>"
    }

    this.sendForm = function(formId,formAction) {
        log('this.sendForm('+formId+','+formAction+')');
   
        var miForma = dojo.byId(formId);
        dojo.attr(miForma, 'accept-charset', 'windows-1252;UTF-8;ISO-');
        dojo.attr(miForma, 'action', formAction);
        //dojo.attr(miForma, 'method', 'POST');
        dojo.attr(miForma, 'enctype', 'multipart/form-data');
        
        dojo.io.iframe.send({
            url: formAction,
            method: "post",
            handleAs: "json",
            form: miForma,
            //handle: saveHandleCallback,
            load:   saveHandleCallback
        });
    
    }
}