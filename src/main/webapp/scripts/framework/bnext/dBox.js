define([
    'dojo/_base/lang',
    'dojo/Evented', 
    'dojo/i18n!bnext/nls/dBox',
    'dojo/_base/declare',
    'dijit/registry',
    'dojo/on',
    'dojo/dom-construct',
    'dojo/dom-attr',
    'dojo/dom-class',
    'dojo/dom',
    'dojo/query',
    'dojo/Deferred',
    'dojo/html',
    'dojo/dom-style',
    'dojo/has',
    'dijit/Dialog',
    'dojo/_base/lang',
    'dojo/NodeList-dom'
],
function (dojoLang, Evented, i18n, declare, registry, on, domConstruct, domAttr, domClass, dom, query, Deferred, html, domStyle, has, Dialog, lang) {
    var win = window;
    function dialog(_message, _btn1Text, _btn2Text, title) {
        if (!win.dialogBox) {
            console.error('Missing defineDialogBox.js, using "confirm" message.');
            return {
                'then':function(f,ff){
                    if(confirm(_message)) {
                        return f();
                    } else {
                        return ff();
                    }
                }
            };
        }
        return win.dialogBox(_message, _btn1Text, _btn2Text, title || i18n.default_title);
    }
    var rejectedPromise = {
        e: undefined,
        then: function(success, error) {
            if (error) {
                rejectedPromise.e = error(rejectedPromise.e);
                return rejectedPromise
            }
            return rejectedPromise;
        }
    }
    var dBox = declare([Evented], {
        domDialog:null,                
        newDialog: function (args) {

            var style = {
                minWidth: '300px',
                maxWidth: '95%',
                maxHeight: '700px',
                textAlign: 'center',
                top: '25%'
            };
            if (has('ie') <= 7) {
                style.width = '500px';
            }
            return new Dialog(lang.mixin(args || {}, {
                style: style
            })
                    );
        },
        constructor: function (args) {
            var dialogArgs = {
                'class': 'definedDialog'
            };
            if (typeof args === 'string') {
                dialogArgs.id = args;
            } else if (typeof id === 'object') {
                if (args.id) {
                    dialogArgs = dojoLang.mixin(args || {}, dialogArgs);
                } else {
                    alert('Invalid dBox creation, missing "id"');
                    return;
                }
            }
            var id = dialogArgs.id, d = this.dialog;
            this.buttonObjects = [];
            this.invalidState = false;
            this.events = [];
            this.dBox = true;
            this.dBoxId = id;
            if (!d) {
                d = this.dialog = registry.byId(id);
            }
            if (!d) {
                this.dialog = d = this.newDialog(dialogArgs);
                this.domDialog = this.dialog.domNode;
                var dialogForm
                        = this.dBoxFormDom
                        = domConstruct.create('form', {
                            'target': 'basefrm',
                            'method': 'post',
                            'onsubmit': 'return false'
                        }, this.dialog.containerNode);
                this.textDiv = this.dialog.textDiv = domConstruct.create('pre', {
                    style: {
                        'white-space':'pre-line',
                        'word-break':'break-word',
                        'padding':'1rem',
                        'text-align':'left'
                    }
                }, dialogForm);
                domConstruct.create('hr', {
                    style: {
                        'padding': '0px',
                        'margin': '0px'
                    }
                }, dialogForm);
                this.btnDiv = this.dialog.btnDiv = domConstruct.create('div', {
                    'class': 'fRight',
                    'style': {
                        'text-align': 'right',
                        'padding': '0.5rem',
                        'direction': 'rtl'
                    }
                }, dialogForm);
                this.addButtonObject(0);
                this.addButtonObject(1);
            }
            if (d) {
                if (this.domDialog === null || typeof this.domDialog === 'undefined') {
                    this.domDialog = d.domNode;
                }
                if (this.textDiv === null || typeof this.textDiv === 'undefined') {
                    this.textDiv = d.textDiv;
                }
                if (this.btnDiv === null || typeof this.btnDiv === 'undefined') {
                    this.btnDiv = d.btnDiv;
                }
            }
        },
        enableButton: function (index) {
            domAttr.remove(this['btn' + (index + 1)], 'disabled');
        },
        addButtonObject: function (index) {
            var _self = this;
            _self['btn' + (index + 1)] = domConstruct.create('input', {
                'type': 'button',
                'data-index': index,
                'data-cy': this.dBoxId + '_' + index,
                'className': 'Button',
                style: {
                    'display': 'none'
                }
            }, _self.btnDiv, 'last');
            this.buttonObjects[index] = {
                'index': index,
                'inputDom': _self['btn' + (index + 1)]
            };
            this.buttonObjects[index].evt = on(_self['btn' + (index + 1)], 'click', function (evt) {
                domAttr.set(this, 'disabled', true);
                setTimeout(function () {
                    _self.enableButton(index);
                }, 50);
                _self.emit("clickBtn" + (index + 1), {
                    data: _self.eventedData(evt),
                    evt: evt || null,
                    bubbles: false,
                    cancelable: true
                });
            });
        },
        showBtn: function (index, text) {
            this['btn' + (index + 1)].value = text;
            this['btn' + (index + 1)].style.display = '';
        },
        hideBtn: function (index) {
            this['btn' + (index + 1)].style.display = 'none';
        },
        legacyShow: function (message, btn1Text, btn2Text, title, isCentered) {
            if (win.top.window.waitForSession) {
                return rejectedPromise;
            }
            var def = new Deferred(),
                    evt1, evt2, dBox,
                    btns = [
                        btn1Text,
                        btn2Text
                    ],
                    diableDialogEvents = function () {
                        try {
                            evt1 && evt1.remove();
                        } catch (e) {
                        }
                        ;
                        try {
                            evt2 && evt2.remove();
                        } catch (e) {
                        }
                        ;
                    }
            ;
            diableDialogEvents();
            dBox = this.show(message, btns, title, isCentered);
            evt1 = dBox.on('clickBtn1', function (evt) {
                def.resolve(dBox.hide());
                diableDialogEvents();
            });
            evt2 = dBox.on('clickBtn2', function (evt) {
                def.reject(dBox.hide());
                diableDialogEvents();
            });
            return {
                then: function(resolve, reject) {
                    return def.promise.then(
                        resolve, reject || function() {} // skips "unhandledRejection" error
                    );
                }
            };
        },               
        show: function (message, buttons, title, isCentered) {
            if (win.top.window.waitForSession) {
                return rejectedPromise;
            }
            title = title || Validate.default_title;
            if (this.invalidState) {
                console.warn('WARN! show - Invalid state detected!');
                this.invalidState = false;
                this.hide();
            }
            try {
                var _self = this,
                        dialog = this.dialog,
                        btn1 = _self.btn1,
                        btn2 = _self.btn2
                        ;
                dialog.set('title', title);
                var m = 'Invalid message';
                if (typeof message === 'object') {
                    m = '';
                    for (var key in message) {
                        if (!message.hasOwnProperty(key))
                            continue;
                        m += key + '. ' + message[key] + '<br>';
                    }
                } else if (typeof message === 'string') {
                    m = message;
                }
                html.set(this.textDiv, m);
                this.formNamePatch();
                domConstruct.empty(this.btnDiv);
                var numberButtons = buttons.length;
                for (var i = 0; i < numberButtons; i++) {
                    this.addButtonObject(i);
                    var btnText = buttons[i];
                    if (typeof btnText !== 'string') {
                        if (i === 0) {
                            btnText = window.Validate ? window.Validate.accept : 'Aceptar';
                        } else {
                            btnText = 'display:none';
                        }
                    }
                    if (!this['btn' + (i+1)]) {
                        this.addButtonObject(i); // Añade un boton cuando se espera que haya mas botones de los que existen
                    }
                    this.showBtn(i, btnText);
                    if (btnText === 'display:none') {
                        this.hideBtn(i);
                    }
                }
                 domClass.add(_self['btn1'], 'raised-button');
                dialog.set('style', {
                    top: '30%'
                });
                dialog.set('draggable', false);
                var s = dialog.show();
                if (s) {
                    s.then(function (evt) {
                        _self.emit("show", {
                            'data': _self.eventedData(evt),
                            'evt': evt || null,
                            'bubbles': false,
                            'cancelable': true
                        });
                    });
                } else {
                    console.warn('Warning! dialog was already SHOWN! alternative show called');
                    _self.emit("show", {
                        'data': _self.eventedData(),
                        'bubbles': false,
                        'cancelable': true
                    });
                }
            } catch (e) {
                console.error(e);
                this.invalidState = true;
                this.emit("error", {
                    'bubbles': false,
                    'cancelable': true
                });
                //this.show(message, buttons, title);
            }
            console.log('showing ' + this)
            return this;
        },        
        hide: function (dontClearForm) {
            var _self = this, def = new Deferred();
            if (!this.dBox) {
                console.warn('INVALID THIS!');
                console.warn(this);
            }
            if (this.invalidState) {
                console.warn('WARN! hide - Invalid state detected!');
            }
            try {
                try {
                    for (var i = 0; i < _self.buttonObjects.length; i++) {
                        _self.hideBtn(i);
                    }
                } catch (e) {
                    console.error(e);
                }
                this.clearEventsData();
                this.dBoxFormDom && (this.dBoxFormDom.target = 'basefrm');
                if (!dontClearForm) {
                    this.formNamePatch(true);
                }
                var open = this.dialog.get('open');
                var solvedData;
                if (open) {
                    this.dialog.hide().then(function (evt) {
                        solvedData = {
                            'data': _self.eventedData(evt),
                            'evt': evt || null,
                            'bubbles': true,
                            'cancelable': true
                        };
                        _self.emit("hide", solvedData);
                        def.resolve(solvedData);
                    });
                } else {
                    console.warn('Warning! dialog was already HIDE! alternative show called');
                    solvedData = {
                        'data': _self.eventedData(),
                        'evt': null,
                        'bubbles': true,
                        'cancelable': true
                    }
                    _self.emit("hide", solvedData);
                    def.resolve(solvedData);
                }
            } catch (e) {
                console.error(e);
                //this.invalidState = true;
                this.emit("error", {
                    'bubbles': false,
                    'cancelable': true
                });
                def.reject(e);
            }
            return def.promise;
        },
        clearEventsData: function () {
            if (this.events) {
                for (var eKey in this.events) {
                    if (this.events.hasOwnProperty(eKey)) {
                        this.events[eKey].remove && this.events[eKey].remove();
                        //console.log('removed event ' + eKey + ' from ' + this)
                    }
                }
            }
            this.events = [];
        },
        eventedData: function (evt) {
            return {
                evt: evt || null,
                'form': this.dBoxFormDom
            };
        },
        formNamePatch: function (destroy) {
            var _self = this, name;
            query('[data-dbox-name]', this.dBoxFormDom).forEach(function (input) {
                name = domAttr.get(input, 'data-dbox-name');
                if (name) {
                    _self.dBoxFormDom[name] = input;
                    destroy && domConstruct.destroy(_self.dBoxFormDom[name]);
                }
            });
        },
        submit: function () {
            this.hide(true);
            this.dBoxFormDom.submit();
        },
        /**
         * Agrega eventos TIPO dojo/on, la diferencia con utilizar 
         * directamente ON() es que EVENT() cierra el evento 
         * automaticamente al utilizar el metodo HIDE()
         * 
         * @param {String   || {dojo/on} object} on
         * @param {function || optional} func
         *      El parametro de la funcion recibe un objeto de esta forma
         *          {
         *              //"evt" es el evento por defecto que regresa un ON de tipo CLICK
         *              data: {evt: evt},
         *              bubbles: false,
         *              cancelable: true
         *          }
         * @returns {undefined}
         */
        event: function (onEvent, func, dom) {
            if (typeof onEvent === 'string' && !dom) {
                this.events.push(this.on(onEvent, func));
            } else if (typeof onEvent === 'string' && dom) {
                this.events.push(on(dom, onEvent, func));
            } else {
                this.events.push(onEvent);
            }
        },
        getDialog: function () {
            return this.dialog;
        },
        destroy: function () {
            this.hide();
            var dijitNode = registry.byId(this.dBoxId);
            registry.byId(this.dBoxId).destroyRecursive();
            dijitNode && dijitNode.destroy();
            setTimeout(function () {
                delete this;
            }, 1);
        }
    });
    dBox.dialog = dialog;
    return dBox;
});
