define(['bnext/i18n!lang/nls/main'], (l) => ({
  uppload: 'Subiendo...',
  formTemplate: 'Formulario',
  formTemplatePreview: 'Ver',
  formTemplateModify: 'Modificar',
  formContinue: 'Continuar',
  formTemplateConfirmation: '¿Seguro de que desea modificar el formulario?<br>Será enviado a una pantalla diferente.',
  pleaseSelectTemplate: 'Por favor seleccione un formulario para ver su vista previa',
  fillCode: 'Folio de llenado:',
  status: 'Estado:',
  folder: 'Carpeta...',
  file: 'Archivo:',
  storageTime: 'Tiempo de retención',
  storagePlace: 'Lugar de retención',
  submit: l.save,
  day: 'día(s)',
  month: 'mes(es)',
  year: 'año(s)',
  cancel: l.cancel,
  NEW: 'Documento Nuevo',
  MODIFICACTION: 'Modificación de Documento',
  CANCEL: 'Cancelación de Documento',
  APROVE: 'Reaprobación de Documento',
  FILL: 'Llenado de formulario',
  PROMOTION: 'Publicación corporativa',
  NEW_PROMOTION: 'Nuevo documento corporativo',
  NEW_FORM: 'Nuevo documento tipo formulario',
  currentDepartment: '{Department} actual:',
  version: 'Revisión',
  select: l.select,
  docEditor: 'Crear un documento con el editor',
  loadEditor: 'Cargar el editor en linea',
  life: 'Vigencia',
  accept: 'Aceptar',
  needfile: 'Debe elegir un archivo para subir.',
  needfolder: 'Debe escoger una carpeta para introducir el documento',
  versionInvalidMessage: 'La edición del documento debe ser alfanumérica, sin espacios y menor a 15 caracteres. ',
  tituloInvalidMessage: 'El título del documento debe ser menor a 255 caracteres',
  claveInvalidMessage: 'La clave del documento debe ser menor a 255 caracteres',
  downloadFile: 'Ver archivo',
  viewDoc: 'Ver documento',
  sourceFile: 'Descargar archivo:',
  noForm: '-- Sin formulario base --'
}));
