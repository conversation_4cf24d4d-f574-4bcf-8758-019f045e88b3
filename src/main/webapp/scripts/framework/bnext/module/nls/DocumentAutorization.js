define([
  'bnext/i18n!bnext/administrator/document/nls/document-module',
  'bnext/i18n!bnext/administrator/document/nls/request-module',
  'bnext/i18n!lang/configuracion/nls/configuration.department'
], (documentModule, requestModule, department) => ({
  root: {
    codeGenerate: 'Por generar',
    document: '<PERSON><PERSON><PERSON><PERSON>',
    code: '<PERSON><PERSON><PERSON>',
    file: 'Archivo',
    reqType: requestModule.reqType,
    requestedOn: requestModule.requestedOn,
    requestedBy: requestModule.requestedBy,
    documentControlledType: documentModule.colNameDocumentControlledType,
    enablePdfViewerDiv: documentModule.enablePdfViewer,
    documentType: documentModule.colNamesDocumentType,
    businessUnitDepartment: department.colNameDepartment,
    node: requestModule.node,
    reason: 'Razón de solicitud',
    download: 'Ver archivo',
    downloadOrig: documentModule.donwloadOrig,
    comentarios: 'Comentarios *',
    reject: 'Rechazar',
    accept: 'Autorizar',
    cancel: 'Cancelar',
    viewDoc: 'Ver documento',
    message_rejectAutorization: 'Se requiere escribir una razón válida para rechazar la solicitud.',
    message_rejectComment: 'Se requiere escribir un comentario para aceptar la solicitud.',
    noFillsFormsAvailable: 'No se tienen solicitudes.',
    fillRequests: 'Las siguientes solicitudes de llenado permanecerán en proceso utilizando la versión anterior:',
    colNameDocumentCode: 'Folio de llenado',
    colNameAuthor: 'Solicitante',
    colNameCreationDate: 'Fecha de llenado',
    cancelFillRequestsWarning: 'Las solicitudes de llenado en proceso seguirán abiertas hasta ser finalizadas, no contendrán los cambios realizados al formulario.',
    collectingAndStoreResponsible: 'Responsable de recolectar y archivar',
    collectingAndStoreRespDesc: 'Responsable de recolectar y archivar',
    informationClassification: 'Clasificación de la información',
    disposition: 'Disposición'
  },
  en: true,
  'es-CC': true
}));
