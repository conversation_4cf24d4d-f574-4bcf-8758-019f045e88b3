define([
    'core',
    'dojo/_base/window',
    'dojo/on',
    'dojo/_base/declare',
    'dijit/_WidgetBase',
    'dijit/_TemplatedMixin',
    'dijit/_WidgetsInTemplateMixin',
    'dojo/text!./templates/ReportPreview.html',
    'bnext/i18n!./nls/ReportPreview',
    'bnext/callMethod',
    'bnext/module/util/report-grid',
    'dojo/_base/lang',
    //a partir de aqui se hacen includes de los widgets para el template
    'dijit/Dialog',
    'dijit/form/Button',
    'xstyle/css!./styles/ReportPreview.css',
    'bnext/survey/_util/jsFailureExposure!'
], function (
        core, win, on, declare, _WB, _TM, _WT, template, 
        i18n, callMethod, ReportGrid, lang
) {
    var ReportPreview = {
        templateString: template,
        i18n: i18n,
        urlCatalogAction: null,
        urlModuleAction: null,
        _onLoadColumns: function (id, databaseQueryId, columns, scripts, styles) {
            if (columns && columns.length > 0) {
                ReportGrid.buildGrid(this.urlModuleAction, id, databaseQueryId, columns, this.grid, scripts, styles, true).then(core.hideLoader);
                this._resize();
            } else {
                core.hideLoader();
                core.fracasoMsg(i18n.testFail, core.i18n.accept);
            }
            win.body().classList.add('overflow-hidden');
        },
        _onLoadColumnsFail: function (result) {
            var message = i18n[result] || i18n[result.error];
            if (typeof message === 'undefined' || message === null) {
                message = result.toString();
            }
            core.error(i18n.testFail + message, core.i18n.accept);
            console.error(result);
            core.hideLoader();
        },
        _onClose: function() {
            ReportGrid.rebuildGridContainer('grid', this.grid);
            win.body().classList.remove('overflow-hidden');
            this.dialog.hide();
        },
        _resize: function() {
            this.dialog.resize();
            this.dialog._position();
        },
        postCreate: function() {
            var _self = this;
            on(window, 'resize', function() {
                _self._resize.call(_self);
            });
            this.dialog.show();
            this._resize();
            if (this.operation === 'readonly') {
                this._renderGrid();
            } else {
                ReportGrid.buildGrid(this.urlModuleAction, this.reportId, this.databaseQueryId, this.columns, this.grid, this.scripts, this.styles).then(core.hideLoader);
                this._resize();
            }
        },
        _renderGrid: function() {
            callMethod({
                url: this.urlCatalogAction,
                params: [this.entity.id],
                method: 'getColumnsOfReport'
            }).then(lang.hitch(this, function(columns) {
                var id = this.entity.id;
                var databaseQueryId = this.entity.query.id;
                var reportScripts = this.entity.scripts;
                var reportStyles = this.entity.styles;
                this._onLoadColumns(id, databaseQueryId, columns, reportScripts, reportStyles); 
            }), lang.hitch(this, this._onLoadColumnsFail));            
        }
    };
    ReportPreview = declare([_WB, _TM, _WT], ReportPreview);
    return ReportPreview;
});