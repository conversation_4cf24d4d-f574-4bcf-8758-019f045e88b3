define([
  'core',
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'dijit/_TemplatedMixin',
  'dijit/_WidgetsInTemplateMixin',
  'dojo/text!./templates/UserSelector.html',
  'dojo/store/Memory',
  'dojo/_base/lang',
  'dojo/_base/array',
  'bnext/module/DataTableWrapper',
  'bnext/callMethod',
  'bnext/survey/lang/languages',
  'bnext/i18n!bnext/module/nls/DataTableDefineLanguage',
  'bnext/GridFactory',
  'bnext/gridComponentUtil',
  'bnext/gridIcons',
  'dojo/dom-construct',
  //a partir de aqui se hacen includes de los widgets para el template
  'dijit/Dialog',
  'xstyle/css!../styles/gridComponent.css',
  'dojo/domReady!'
], (core, declare, _WB, _TM, _WT, template, Memory, lang, array, DataTableWrapper, callMethod, i18n, i18nDataTable, GridFactory, gcUtil, gridIcons, domConstruct) => {
  let UserSelector = {
    templateString: template,
    userColumns: null, // Columnas del grid flotante
    i18n: null,
    onUserSelected: null,
    _renderedGrid: false,
    value: null,
    store: null,
    selectedUser: null,
    floatingGridSize: null,
    _getTag: function (key) {
      let tag;
      if (this.i18n) {
        tag = this.i18n[key];
      }
      return tag || i18n.current(key) || `i18n.${key}`;
    },
    _getValueAttr: function () {
      return this.value;
    },
    _setValueAttr: function (value) {
      this.value = value;
    },
    _getStoreAttr: function () {
      return this.store;
    },
    _setStoreAttr: function (store) {
      this.store = store;
      this._updateStore();
    },
    _renderAddDialog: function () {
      const langOpt = lang.clone(i18nDataTable);
      const columns = [];
      const cType = gcUtil.cType;
      gcUtil
        .column(columns)
        .push('select', langOpt.selectUser, cType.Function(['full-row'], lang.hitch(this, this._onUserSelected), gridIcons.approve), null, '50px')
        .push('userDescription', langOpt.userDescription, cType.Text(), null, '200px')
        .push('account', langOpt.account, cType.Text(), null, '200px')
        .push('correo', langOpt.email, cType.Text(), null, '200px')
        .push(
          'departmentId',
          langOpt.businessUnit,
          cType.SelectCatalogMultiple({
            id: 'departmentId',
            description: 'departmentDescription',
            serviceName: 'UserService.action',
            methodName: 'getDepartmentsLite'
          }),
          null,
          '200px'
        )
        .push('departmentsWhereParticipate', langOpt.departmentsWhereParticipate, cType.Text(), null, '200px')
        .push('positions', langOpt.positions, cType.Text(), null, '200px')
        .push('profiles', langOpt.profiles, cType.Text(), null, '200px');
      GridFactory.create({
        size: 5,
        container: `add_${this.id}`,
        serviceStore: 'UserService.action',
        methodName: 'getAllUsersMap',
        fullColumns: columns
      }).then((e) => {
        e.refreshData();
      });
    },
    _onOpenAddClick: function () {
      this.addUserDialog.show(); // muestra el dialog de selección
    },
    _onCloseAddClick: function () {
      this.addUserDialog.hide(); // Oculta el dialog de selección
    },
    _removeValue: () => {},
    _onUserSelected: function (row) {
      if (!core.isNull(row)) {
        this.selectedUser = row;
      } else {
        this.selectedUser = null;
      }
      if (typeof this.onUserSelected === 'function') {
        this.onUserSelected(this.selectedUser);
      }
    },
    getSelectedUser: function () {
      return this.selectedUser;
    },
    _initialize: function () {
      if (!this._renderedGrid) {
        this._renderedGrid = true; // inicializa las area de trabajo
        this._renderAddDialog();
      }
    },
    getTag: function (tag) {
      return this._getTag(tag);
    },
    placeAt: function () {
      this.inherited(arguments);
      this._initialize();
    },
    update: function (items, selected) {
      this.value = selected;
      this.store = new Memory({ data: items });
    },
    syncData: function (items, selected) {
      this.value = selected;
      this.store = new Memory({ data: items });
      const values = [];
      const dataRows = [];
      array.forEach(
        this.value,
        lang.hitch(this, function (id) {
          const item = this.store.get(id);
          if (core.isNull(item)) {
            return;
          }
          dataRows.push(item);
          values.push(id);
        })
      );
      this.value = values;
      return {
        value: values,
        valueData: dataRows
      };
    },
    postCreate: function () {
      this._initialize();
      this.openAdd.title = this.getTag('openAddUserTitle');
      this.openAdd.innerHTML = this.getTag('openAddLabel');
      if (this.fieldType === 'signature') {
        this.addUserDialog.set('title', this.getTag('signature_addUserDialogTitle'));
      } else {
        this.addUserDialog.set('title', this.getTag('seccion_addUserDialogTitle'));
      }
      this.addUserDialog.titleBar.classList.add('sticky-title');
      domConstruct.create(
        'span',
        {
          class: 'material-icons close-icon',
          innerHTML: 'close',
          title: core.i18n.closeLabel,
          onclick: lang.hitch(this, function () {
            this.addUserDialog.hide();
          })
        },
        this.addUserDialog.titleBar
      );
    },
    constructor: function (args) {
      lang.mixin(this, args);
    },
    hide: function () {
      this.addUserDialog.hide();
    },
    show: function () {
      this.addUserDialog.show();
    }
  };
  UserSelector = declare([_WB, _TM, _WT], UserSelector);
  return UserSelector;
});
