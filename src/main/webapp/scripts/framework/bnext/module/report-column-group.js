define([
    'dojo/_base/declare',
    'dojo/_base/lang',
    'bnext/i18n!./nls/ReportColumn',
    './ReportColumn',
    'bnext/i18n!./nls/report-column-group',
    'xstyle/css!./styles/report-column-group.css'
],
function (
    declare, 
    lang,
    BaseI81n,
    ReportColumn,
    i18n
) {
    var customI18n = {};
    lang.mixin(customI18n, BaseI81n);
    lang.mixin(customI18n, i18n);
    var ReportColumnGroup = {
        tagName: 'ReportColumnGroup',
        i18n: customI18n,
        orderAttribute: 'groupingPriority',
        directionAttribute: 'groupingDirection',
        maxSelectionsRecords: 10,
        isLabelFieldAvailable: false,
        isTypeFieldAvailable: false,
        isSortDirectionFieldAvailable: true,
        isWidthFieldAvailable: false
    };
    return ReportColumnGroup = declare([ReportColumn], ReportColumnGroup);
});