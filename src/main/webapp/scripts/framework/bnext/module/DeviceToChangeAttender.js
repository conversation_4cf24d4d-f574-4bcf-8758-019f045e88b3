define([
  'core',
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'bnext/module/DeviceToChangeAttenderDialog',
  'dijit/Dialog',
  'dojo/on',
  'dojo/dom-class',
  'bnext/callMethod',
  'bnext/i18n!./nls/DeviceToChangeAttender'
], (core, declare, _WB, DeviceToChangeAttenderDialog, Dialog, on, domClass, callMethod, i18n) => {
  let dialog = null;
  let selectAction = null;
  function resize() {
    dialog.resize();
    dialog._position();
  }
  const DeviceAttenderBase = {
    form: null,
    postCreate: () => {},
    attend: (pendingRecordId, deviceId, pendingBuilder) => {
      /**
       * @type DeviceAttenderBase
       */
      core.showLoader(i18n.loading);
      callMethod({
        url: '../DPMS/Device.action',
        method: 'loadPending',
        params: [deviceId]
      }).then(
        (device) => {
          if (!dialog) {
            dialog = new Dialog();
          }
          if (!selectAction) {
            selectAction = new DeviceToChangeAttenderDialog({
              deviceId: deviceId,
              dialog: dialog,
              device: device
            });
          }
          selectAction.placeAt(dialog);
          dialog.show();
          on(window, 'resize', resize);
          domClass.toggle(dialog.domNode, 'fixedTop', true);
          resize();
          dialog.set('title', i18n.title);
          core.hideLoader();
        },
        (e) => {
          core.error(e);
          core.hideLoader();
        }
      );
    }
  };
  const DeviceAttender = declare([_WB], DeviceAttenderBase);
  return DeviceAttender;
});
