/* 
    Created on : Mar 8, 2018, 2:30:04 PM
    Author     : <PERSON>
*/
.Report .buttons {
    margin-top: 0.313rem;
    margin-right: 0.125rem;
}
.Report > div > div{
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
    text-align: left;
}
.Report [data-dojo-attach-point=nodeDiv] .dijitDropDownButton{
    padding-left: 1.875rem;
}
.Report {
    max-width: 50rem;
    max-height: 37.5rem;
    overflow: auto;
}
div.contentButtons {
    text-align: center;
    padding: 0.313rem;
}
.bnext .dijitDialog.fullWidthScreen.ReportDialog [data-dojo-attach-point="containerNode"].dijitDialogPaneContent {
    width: 100%!important;
}
.bnext .dijitDialog.fixedTop.ReportDialog div[data-dojo-attach-point="titleBar"] {
    width: auto!important;
}
.bnext .dijitDialog.fixedTop .dijitDialogPaneContent .Report {
    overflow: auto;
    max-height: 100%;  
    height: 100%;
    position: relative;
    padding-top: 0px;
    margin-top: 0px;
}

.bnext .dijitDialog.fixedTop .buttons.contentButtons {
    padding-top: 0.188rem;
    padding-bottom: 0.188rem;
    position: absolute;
    width: 100%;
    text-align: left;
    margin-top: -5.313rem;
    height: auto;
}
.Report {
    min-width: 100%!important;
}
.Report .gridInfo {
    min-height: 15.625rem!important;; 
}
.Report .contentGroup {
    margin-bottom: 3.438rem;
}
.Report label {
    line-height: 1.125rem;
}
.dijitTooltip i {
    font-style: italic;
}
.Report .dijitTextBox {
    width: 20em;	
}
.Report .column_width.dijitTextBox {
    width: 5em;	
}
.Report-Waiting-QMS {
    text-align: center;
    background-color: #FFF;
    background-image: url(../../../../../images/logo/qms.png);
    background-position: 50% 50%;
    border-bottom: #F8F8F8 2.688rem solid;
    background-repeat: no-repeat;
    min-width: 46.375rem;
    height: 100%;
    z-index: 951;
    top: 0;
    left: 0;
    opacity: .89!important;
    position: absolute;
    width: 100%;
}
.Report-Waiting-QMS .loader {
    background-image: url(../../../../../images/common/loading.gif);
    top: 50%;
    position: absolute;
    left: 50%;
    width: 1.938rem;
    height: 1.938rem;
    margin-left: -0.938rem;
    margin-top: -3.125rem;
}
.Report-Waiting-QMS #loadMessage {
    top: 50%;
    position: absolute;
    margin-top: 1.563rem;
    color: #777;
    font-size: 0.5rem;
    width: 100%;
    border: none;
}
.Report .codeGroup .labelText {
    width: 16.25rem;
    padding-bottom: 1px;
}
.Report .descriptionGroup {
    clear: both;
}
.Report .rightLinkedGridWithTitles .spanGridHead {
    text-align: center;
    background-color: #FFF;
    background-image: url('../images/common/loading2.gif');
    background-position: 38% 2.5rem;
    background-repeat: no-repeat;
    width:      46.375rem;
    height:     100%; 
    z-index:    10;
    opacity:    .89;
    position:   absolute; 
}
.Report .rightLinkedGridWithTitles .gridExpandable.grid-component-container {
    max-height: none!important;
    width: auto;
    float: none;
}
.Report div.rightGridContainer {
    padding-top: 0.5rem!important;
}
.Report div.rightLinkedGridWithTitles {
    min-height: 2.5rem;
}
.Report .rightLinkedGridWithTitles div.info {
    width: 25rem;
}
.Report .jsArea .CodeMirror {
    font-size: 0.875rem;
    border: 1px solid #b3b3b3;
    max-width: 53.125rem;
}

.Report .contentGroup table a img{
    vertical-align: baseline;
}

.Report .contentGroup table#queryGrid table{
    min-height: 0px!important;
}
.Report .cssArea .CodeMirror{
    font-size: 0.875rem;
    border: 1px solid #b3b3b3;
    max-width: 53.125rem;
    margin-top: 1rem;
}
.Report .detailsAreaContainer {
    display: block;
    width: auto!important;
    margin-left: 14.063rem;
    height: 5.625rem;
}
.Report .descriptionGroup .descriptionInput,
.Report .detailsAreaContainer textarea {
    min-width: 30.938rem;
    max-width: 44.375rem;
    box-sizing: border-box;
}