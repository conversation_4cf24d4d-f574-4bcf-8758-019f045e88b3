/* global dojoConfig, encodeURIComponent */

define([
    'core',
    'dojo/dom-class',
    'dojo/_base/declare',
    'dijit/_WidgetBase',
    'dijit/_TemplatedMixin',
    'dijit/_WidgetsInTemplateMixin',
    'dojo/text!./templates/FilePicker.html',
    'bnext/callMethod',
    'dojo/json',
    'bnext/i18n!./nls/FilePicker',
    'dojo/dom-construct',
    'dojo/dom',
    'dojo/dom-attr',
    'dojo/_base/lang',
    'bnext/extensionIcons',
    'dijit/Tooltip',
    'bnext/third-party-app/google-drive-picker',
    'bnext/i18n!bnext/administrator/document/nls/pdf-conversion-feedback',
    'dojo/text!bnext/administrator/document/templates/pdf-conversion-feedback.html',
    'bnext/module/util/preview-result-status',
    //a partir de aqui se hacen includes de los widgets para el template
    'dijit/form/Button',
    'bnext/module/Uploader',
    'xstyle/css!./styles/FilePicker.css',
    'bnext/survey/_util/jsFailureExposure!',
    'dojo/domReady!'
], function (core, domClass, declare, _WB, _TM, _WT, template, callMethod, JSON,
    i18n, domConstruct, dom, domAttr, lang, extensionIcons, Tooltip,
    googleDrivePicker, i18nFeedback, templateFeedback, PreviewStatus) {

    function sendFeedback(title, detail, jsexception) {
        callMethod({
            url: 'Feedback.action',
            method: 'send',
            params: [title, detail || '']
        }).then(function (r) {
        }, function (r) {
            core.error(jsexception);
        });
    }
    var WAIT_TIME_CONSTANT = 50;
    var MAX_WAIT_TIME_CONSTANT = 1000;
    var WAIT_TIME_FACTOR_CONSTANT = 1.4;
    var FilePicker = {
        modern: false,
        debug: false,
        externalSelect: false,
        downloadAction: '../view/v-download-attached.view',
        downloadParameters: 'id=[id]',
        exportAction: '../view/v-download-attached.view',
        exportParameters: 'id=[id]',
        uploadAction: '../DPMS/Upload.fileUploader',
        uploadView: '../DPMS/v-uploader-frame.view',
        'default-file-icon': null,
        accept: '*',
        supportedVideos: 'mp4',
        i18n: null,
        _waitTime: WAIT_TIME_CONSTANT,
        _thumbnailAttempts: 0,
        _maximumThumbnailAttempts: 1000,
        _targetDom: null,  
        _enablePdfViewer: 1,
        _busy: false,
        _cancelled: false,
        _timeoutPreview: null,
        templateString: template,
        'data-mode': 'upload',
        _emptyImage: '//:0',
        _errorImage: '../images/common/document.png',
        forceDisableDisplayMode: false,
        _externalConnector: null,
        _downloadFile: function () {
            var anchor = domConstruct.create('a', {
                href: 'javascript: void(0);',
                style: {
                    display: 'none'
                },
                target: '_blank'
            }); 
            if (this['data-mode'] === 'not-supported') {
                domAttr.set(anchor, 'target', '_self');
                anchor.href = this.exportAction + '?' + this.exportParameters.replace("[id]", this.file.id);
            } else {
                anchor.href = this.downloadAction + '?' + this.downloadParameters.replace("[id]", this.file.id);
            }
            anchor.click();
            domConstruct.destroy(anchor);
        },
        _changeMode: function(mode) {
            if ((this.forceDisableDisplayMode && mode === 'display') || mode === 'static')  {
                return;
            }
            this['data-mode'] = mode;
            domAttr.set(this.domNode, 'data-mode', mode);
        },
        _hideExportSection: function() {
            domAttr.set(this.domNode, 'data-external-select', '0');
        },
        _showExportSection: function() {
            domAttr.set(this.domNode, 'data-external-select', '1');
        },
        _renderExporter: function(exporter) {
            if (exporter && typeof exporter.render === 'function') {
                var url = this.exportAction + '?' + this.exportParameters.replace("[id]", this.file.id);
                exporter.render(this.saveToDrive, url, this.fileInfo.title,  'Bnext QMS');
            } else {
                this._hideExportSection();
            }
        },
        _displaySection: function() {
            this._changeMode('display');
            this.fileInfo = extensionIcons.getExtensionInfo(this.file);
            if (this.get('enablePdfViewer') === 1) {
                if (this.fileInfo.isPdf) {
                    this._message = i18n.messageGeneratingPages;
                    this.pdfConversion.innerHTML = i18n.startingPageGeneration;
                } else if (this.fileInfo.isImage) {
                    this._message = i18n.messageGeneratingThumbnail;
                    this.pdfConversion.innerHTML = i18n.startingThumbnailGeneration;
                } else {
                    this._message = i18n.messageStartingConversion;                
                    this.pdfConversion.innerHTML = i18n.startingConversion;
                }
                this._changeMode('downloading');
                this.downloadLabel.innerHTML = this.fileInfo.title;
                this.downloadSrc.src = this.fileInfo.iconSrc;
                this._message = i18n.messageStartingConversion;
                if (this.externalSelect === true) {
                    this._showExportSection();
                    require(['bnext/third-party-app/google-drive-exporter'], function(googleDriveExporter) {
                        googleDriveExporter.then(lang.hitch(this, this._renderExporter));
                    });
                } else {
                    this._hideExportSection();
                }
                this._generateImagePreview();
            } else {
                this._onFileSelected(this.file);
                this._displayNotSupported();   
                this._clearTooltip();   
            };
        },
        _uploadSection: function () {
            this._clearSaveToDrive();
            this._changeMode('upload');
        },
        _onBeforeUpload: function () {
            this._busy = true;
            this._clearTooltip();
            this._changeMode('uploading');
        },
        _setTargetValue: function(fileId) {
            this._targetDom.value = fileId;
        },
        _getTargetValue: function() {
            return this._targetDom.value;
        },
        _onFileUploaded: function(file) {
            this.file = file || null;
            if (file.id && file.id !== 0) {
                this._setTargetValue(this.file.id);
                this._displaySection();
                this.onFileUploaded(this.file);
            } else {
                this._onFailedUpload(file);
            }
        },
        _onFailedUpload: function(file) {
            this._message = null;
            this._setTargetValue(null);
            this._clearPreviewData();
            this._uploadSection();
            this.onFailedUpload(file);
        },
        onFileUploaded: function() {            
        },
        _onFileSelected: function(file) {            
            this.onFileSelected(file);
            this._busy = false;
        },
        onFileSelected: function() {            
        },
        onFailedUpload: function() {
            
        },
        _onDownload: function() {
            this._downloadFile(this.file.id);
        },
        _resetTimeoutPreview: function() {
            if (!core.isNull(this._timeoutPreview)) {
                clearTimeout(this._timeoutPreview);
                this._timeoutPreview = null;
            }
            this._waitTime = WAIT_TIME_CONSTANT;
            this._downloadAttempts = 0;
        },
        _onDelete: function() {
            this._setTargetValue(null);
            this._resetTimeoutPreview();
            this.fUploader.onDelete.call(this.fUploader);
            this.onDelete();
            this._message = null;
            if (this._thumbnailCall) {
                this._thumbnailCall.cancel();
                this._thumbnailCall = null;
            }
            this._clearPreviewData();
            this._uploadSection();
        },
        _isDummyImage: function() {
            try {
                if(!this.previewSrc) {
                    sendFeedback('NO PREVIEWSRC', JSON.stringify(this));
                } else if(!this.previewSrc.src) {
                    sendFeedback('NO PREVIEWSRC.SRC', JSON.stringify(this.previewSrc));
                } else {
                    var imageSrc = this.previewSrc.src;
                    return this._errorImage === imageSrc || /^https?:\/\/:0\/$|^\/\/:0$/.test(imageSrc);
                }
            } catch(e) {
                console.error(e);
                console.error(e.stack);
            }
            return true;
        },
        _getPreviewSrc: function() {
            return '../DPMS/v-preview-file.view?id=' + this.file.id + '&sha512='+ this.file.contentSha512;
        },
        _onLoadPreview: function() {
            if (this._isDummyImage()) {
                return;
            }
            this._changeMode('display');
        },
        _getConversionMessage: function(result) {
            var msg = result.message || '';
            if (typeof msg === 'string' && msg !== '' && msg.indexOf('error') !== -1) {
                if (msg === 'error') {
                var message = lang.replace(templateFeedback, {
                    mainMessage: i18nFeedback.fileNotConvertibleMessage,
                    feedbackTitle: i18nFeedback.feedbackTitle,
                    feedbackMessage: i18nFeedback.fileNotConvertible
                });
                return message;
                } else {
                    var message = lang.replace(templateFeedback, {
                        mainMessage: i18nFeedback.fileNotConvertibleMessage,
                        feedbackTitle: i18nFeedback.feedbackTitle,
                        feedbackMessage: i18nFeedback[msg] || i18nFeedback.fileNotConvertible
                    });
                    return message;
            }
            }
            var failedRequest = this._isFailedRequest(result);
            if (failedRequest) {
                return i18nFeedback.notAvailableTitle + '<br>Error:' + this._failedRequestMessage(result);
            }
            switch(result.status) {
                case PreviewStatus.PDF_CONVERSION_NOT_AVAILABLE:
                    return i18n.pdfGeneratorNotAvailableMessage;
                case PreviewStatus.STARTING_PDF_CONVERSION:
                    return i18n.messageStartingConversion;
                case PreviewStatus.NOT_SUPPORTED_THUMBNAIL:
                    return i18n.messageConvertingFile;
                case PreviewStatus.PDF_CONVERSION_STARTED:
                    if (result.queueSize > 0) {
                        return i18n.messageQueuedFile;
                    }
                    return i18n.messageConvertingFile;
                case PreviewStatus.PDF_CONVERSION_FAILED:
                    return i18n.messagePdfGeneratingFailed;
                case PreviewStatus.PDF_CONVERSION_SUCCESS:
                case PreviewStatus.PAGE_GENERATION_STARTED:
                    if (result.queueSize > 0) {
                        return i18n.messageQueuedGeneratingPages;
                    }
                    return i18n.messageGeneratingPages;
                case PreviewStatus.PDF_CONVERSION_NOT_STARTED:
                case PreviewStatus.PAGE_GENERATION_FAILED:
                    return i18n.messagePageGenerationFailed;
                case PreviewStatus.THUMBNAIL_GENERATION_STARTED:
                    return i18n.messageThumbnailGenerating;
                case PreviewStatus.GTHUMBNAIL_GENERATION_SUCCESS:
                    return i18n.messageThumbnailGenerated;
                case PreviewStatus.THUMBNAIL_GENERATION_FAILED:
                    return i18n.messageThumbnailFailed;
            }
            return null;
        },
        _clearPreviewData: function() {
            this._thumbnailAttempts = 0;
            this._waitTime = WAIT_TIME_CONSTANT;
            if (this._thumbnailTimeoutId) {
            clearTimeout(this._thumbnailTimeoutId);
            this._thumbnailTimeoutId = null;
            }
            this._clearTooltip();
        },
        _showTooltip: function(message){
            if (this['data-mode'] === 'upload') {
                Tooltip.hide(message, this.displayNode);
                Tooltip.show(message, this.uploadNode);
            } else {
                Tooltip.hide(message, this.uploadNode);
                Tooltip.show(message, this.displayNode);
            }
        },
        _clearTooltip: function() {
            Tooltip.hide(this.uploadNode);
            Tooltip.hide(this.displayNode);
        },
        _displayNotSupported: function () {
            this._changeMode('not-supported');
        },
        _displaySupportedVideo: function () {
            this._changeMode('supported-video');
        },
        _displayNotAvailable: function() {
            this.previewSrc.src = this._errorImage;    
        },
        _onErrorPreview: function() {
            this._clearPreviewData();
            if (this._isDummyImage()) {
                return;
            }
            this._displayNotAvailable();
        },
        _initalizeI18n: function(args) {
            if (typeof args.i18n === 'object') {
                this._i18n = lang.mixin(i18n, args.i18n);
            }
            if (args['debug'] === true) {
                this._i18n = {};
                for(var key in i18n) {
                    if (key === '$locale') {
                        continue;
                    }
                    if (typeof key === 'string') {
                        console.warn('Missing i18n for key ' + key);
                        this._i18n[key] = 'i18n.' + key;
                    }
                }
            }
        },
        _loadFileInfo: function() {
            this._changeMode('uploading');
            callMethod({
                url: '../DPMS/FileInfo.action',
                params: [this._getTargetValue()],
                method: 'get'
            }).then(
                lang.hitch(this, this._onLoadInfoSuccess),
                lang.hitch(this, this._onLoadInfoFail)
            );
        },
        _onPrepareFileForPdfViewerSuccess: function(result) {
            if (this.get('enablePdfViewer') !== 1) {
                return;
            }
            this._message = this._getConversionMessage(result);
            if (result.status === PreviewStatus.NOT_SUPPORTED_THUMBNAIL) {
                this._onFileSelected(this.file);
                if (this.supportedVideos.indexOf(this.file.extension) !== -1) {
                    this._displaySupportedVideo(); 
                } else {
                    this._displayNotSupported(); 
                }
                this._clearTooltip();
            } else if (result.status === PreviewStatus.THUMBNAIL_GENERATION_SUCCESS) {
                this.file.numberPages = result.numberPages;
                this.file.hasPdf = result.numberPages !== null && result.numberPages > 0 ? 1 : 0;
                this.file.hasPdfPages = result.numberPages !== null && result.numberPages > 0 ? 1 : 0;
                this._onFileSelected(this.file);
                this.previewSrc.src = this._getPreviewSrc();
                this._clearTooltip();
            } else {
                this._onPrepareFileForPdfViewerFail(result);
            }
            this._cancelled = false;
        },
        _setValueAttr: function(fileId) {
            this._setTargetValue(fileId);
            this._loadFileInfo();
        },
        _setEnablePdfViewerAttr: function(enablePdfViewer) {
            var hasChanges = enablePdfViewer !== this._enablePdfViewe;
            this._enablePdfViewer = enablePdfViewer;
            if (hasChanges && this.file) {
                this._onFileUploaded(this.file);
            }
        },
        _getEnablePdfViewerAttr: function() {
          return this._enablePdfViewer;  
        },
        _failedRequestMessage: function(result) {
            return result.errorObject.error.name;
        },
        _isFailedRequest: function(result) {
            var failedRequest = result.code === 103
                    && result.errorObject && result.errorObject.error
                    && result.errorObject.error.name;
            return failedRequest;
        },
        onDelete: function() {
        },
        _getConversionProgress: function() {
            return (50 + ((50.0 / this._maximumThumbnailAttempts) * this._thumbnailAttempts)) % 100;
        },
        _onPrepareFileForPdfViewerFail: function(result) {
            //TODO: Convetir error 503 a un estado que no represente error, para fines de consulta de errores en Glowroot
            if (this.get('enablePdfViewer') !== 1) {
                return;
            }
            result = result || {};
            this._message = this._getConversionMessage(result);
            var failedRequest = this._isFailedRequest(result);
            if (this._cancelled 
                || result.status === PreviewStatus.NOT_SUPPORTED_THUMBNAIL
                || result.status === PreviewStatus.PDF_CONVERSION_NOT_AVAILABLE
                || failedRequest
                || (typeof result.message === 'string' && result.message.indexOf('error') !== -1)
                || this._thumbnailAttempts > this._maximumThumbnailAttempts) {
                this._clearPreviewData();
                if (result.status === PreviewStatus.NOT_SUPPORTED_THUMBNAIL) {
                    this._displayNotSupported();
                } else {
                    this._displayNotAvailable();
                }
                if (this._thumbnailAttempts > this._maximumThumbnailAttempts) {
                    this._showErrorMessage(i18n.messagePdfGeneratingFailed);
                } else if (this._message && !this._cancelled && !failedRequest) {
                    this._showErrorMessage(this._message);
                }
                this._busy = false;
                this.__cancelled = false;
                this._onFileSelected(null);
            } else {
                if (result.status === PreviewStatus.PDF_CONVERSION_STARTED) {
                    if (result.queueSize > 0) {
                        this.pdfConversion.innerHTML = i18n.queuedPdfConversion;
                    } else {
                    this.pdfConversion.innerHTML = i18n.pdfConversion;
                    }
                } else if (result.status === PreviewStatus.PAGE_GENERATION_STARTED) {
                    if (result.queueSize > 0) {
                        this.pdfConversion.innerHTML = i18n.queuedPageGeneration;                        
                    } else {
                        this.pdfConversion.innerHTML = i18n.pageGeneration;
                    }
                } else if (result.status === PreviewStatus.THUMBNAIL_GENERATION_SUCCESS) {
                    this.pdfConversion.innerHTML = i18n.generatedPdf;
                } else {
                    this.pdfConversion.innerHTML = i18n.startingConversion;
                }
                this._thumbnailTimeoutId = setTimeout(lang.hitch(this, function() {
                    this._generateImagePreview();
                    var newWaitTime = this._waitTime * WAIT_TIME_FACTOR_CONSTANT;
                    if (newWaitTime < MAX_WAIT_TIME_CONSTANT) {
                        this._waitTime = newWaitTime;
                    } else {
                        this._waitTime = this._waitTime + 250;
                    }
                    this._thumbnailAttempts++;
                }), this._waitTime);
            }
        },
        _showErrorMessage: function(message){
            this._changeMode('error');           
            this.forceDisableDisplayMode = true;
            domConstruct.empty(this.errorDom);
            var div = domConstruct.create('div');
            div.innerHTML = message;
            domConstruct.place(div,this.errorDom);
        },
        _generateImagePreview: function() {
            var fileId = this._getTargetValue();
            this.forceDisableDisplayMode = false;
            if (fileId > 0 ) {
                this._thumbnailCall = callMethod({
                    url: '../DPMS/FileInfo.action',
                    params: [this._getTargetValue()],
                    method: 'prepareFileForPdfViewer' 
                });
                this._thumbnailCall.then(
                    lang.hitch(this, this._onPrepareFileForPdfViewerSuccess),
                    lang.hitch(this, this._onPrepareFileForPdfViewerFail)
                );
            } else {
                this._clearPreviewData();
                this._busy = false;
                this._cancelled = false;
                console.warn('No file avialable to generate thumbnail.');
            }
        },
        _onLoadInfoSuccess: function(file) {
            this.file = file;
            this._displaySection();
        },
        _clearSaveToDrive: function() {
            this.saveToDrive.innerHTML = '';
        },
        _onLoadInfoFail: function(e) {
            this._clearSaveToDrive();
            this.saveToDrive.innerHTML = '';
            this.fileInfo = null;
            core.error(e, core.i18n.accept);
            this._changeMode('upload');  
        },
        _getMessageAttr: function() {
            return this._message;
        },
        _getBusyAttr: function() {
            return this._busy;
        },
        _setUploadPathAttr: function(uploadPath) {
            this.uploadAction = uploadPath;
            this.fUploader.set('uploadPath', this.uploadPath);
        },
        _getValueAttr: function() {
            return this._getTargetValue();
        },
        _onExternalPick: function() {
            try {
                core.showLoader();
                this._externalConnector.show().then(core.hideLoader, core.hideLoader);
            } catch(e) {
                core.hideLoader();
                core.error(e);
            }
        },
        showTooltip: function(defaultMessage) {
            var message = this._message || defaultMessage;
            return this._showTooltip(message);
        },
        hide: function () {
            this._clearTooltip();
        },
        cancel: function() {
            this._cancelled = true;
            this._clearPreviewData();            
            this._resetTimeoutPreview();            
        },
        destroy: function () {
            this._resetTimeoutPreview();
            this.inherited(arguments);
            this._message = null;
            if (this._thumbnailCall) {
                this._thumbnailCall.cancel();
                this._thumbnailCall = null;
            }
            this._clearPreviewData();
        },
        constructor: function(args) {
            if (typeof args.targetDom === 'string') {
                this._targetDom = dom.byId(args.targetDom);
            } else if (typeof args.targetDom === 'object') {
                this._targetDom = args.targetDom;
            }
            this._initalizeI18n(args);
        },
        postCreate: function() {
            if (this.modern) {
                domClass.add(this.domNode, 'FilePicker-modern');
            }
            if (core.isNull(this._targetDom)) {
                this._targetDom = this.fileSelected;
            }
            if (+this._getTargetValue() > 0) {
                this._loadFileInfo();
            }
            if (this.externalSelect === true) {
                domAttr.set(this.domNode, 'data-external-select', '1');
                this._externalConnector = new googleDrivePicker({
                    onChange: lang.hitch(this, this._onFileUploaded),
                    onBeforeUpload: lang.hitch(this, this._onBeforeUpload)
                });
            }
            if (this.useDownloadIcon) {
                domClass.add(this.domNode, 'use-download-mode');
            }
        },
        _onUploaderReady: function() {
            this.fUploader.uploader.lblFile.style.color = 'black';
            if(this.modern) {
                this.fUploader.uploader.fileInput.style.width = '100%'; 
                this.fUploader.uploader.lblFile.style.width = '100%';
                this.fUploader.uploader.lblFile.style.textIndent = '0';
                this.fUploader.uploader.lblFile.style.borderRadius = '0';
                this.fUploader.uploader.filePickerBackground.style.marginLeft = '0';
                this.fUploader.uploader.filePickerBackground.style.backgroundPositionX = 'center';
            }
            if(this.externalSelect) {
                this.fUploader.uploader.lblFile.style.color = 'white';
                this.fUploader.uploader.filePickerBackground.style.backgroundImage = 'none';
            }
            this.fUploader.uploader.lblFile.style.lineHeight = '21px';
            this.fUploader.uploader.lblFile.style.textIndent = '25px';
            this.fUploader.uploader.lblFile.style.backgroundPositionX = '5px';
            this.fUploader.uploader.lblFile.style.backgroundPositionY = '1px';
            this.fUploader.uploader.lblFile.style.top = '12px';
            this.fUploader.uploader.filePickerBackground.style.display = 'block';
        }
    };
    FilePicker = declare([_WB, _TM, _WT], FilePicker);
    return FilePicker;
});