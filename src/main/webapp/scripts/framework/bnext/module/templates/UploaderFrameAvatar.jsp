<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<% request.setCharacterEncoding("UTF-8");%>
<html style="overflow: hidden;">
    <head>
        <script>
            window.isLoaded = true;
        </script>
        <link rel="stylesheet" href="../qms/<%=bnext.resources.DynamicCssService.CURRENT_MATERIAL_FONTS_FILE_NAME%>.css?${systemVersion}">
        <style type="text/css">
            .label, label{
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: "TypoPRO Titillium Text", sans-serif, Verdana, Arial, Helvetica;
                font-size: 14px;
                text-transform: uppercase;
                background-image: url(../scripts/framework/bnext/images/clip.gif);
                background-repeat: no-repeat;
                background-size: 24px;
                background-position-x: 11px;
                background-position-y: 8px;
                text-indent: 40px;
            }
            input::-webkit-file-upload-button {
                cursor: pointer;
            }
            .Button, .button, .buttonColor {
                border-radius: 1.75rem;
                color:white;
                font-weight:bold!important;
            }
            .fileUploaderLabel  {
                display: none;
            }
            .FileUploader {
                display: flex !important;
                flex-direction: row !important;
                justify-content: center !important;
                transform: translate(0, 25%) !important;
                width: 2.25rem !important;
                border: solid 1px #ccc !important;
                height: 2.25rem !important;
                border-radius: 50% !important;
                align-items: center;
            }

            body {
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" >
        <form id="uploaderForm" method="POST" enctype="multipart/form-data"></form>
    </body>
</html>
