<div>
    <div>
        <div class="ExternalLink">
            <div class="content">
                <div class="mainMessage">${i18n.openingDocument}</div>
                <div>${i18n.solveTrouble}</div>
                <a target='_blank' href="../administrator/ayuda/installer/Bnext Launcher.msi" class="downloadLink">${i18n.install}</a>
                <p>
                    <span class="plattaform">${i18n.forWindows} 10/8.1/8/7</span>
                </p>
            </div>
        </div>    
        <hr/>
        <div class="buttons externalLinkButtons" data-dojo-attach-point="buttonsDiv">
            <div class="align-check">
                <input type="checkbox" data-dojo-attach-point="checkboxInput" class="material-icons checkboxClosed" />
                <span class="lblCheckbox" >${i18n.messageCheked}</span>
            </div>
            <button class="btnClose" data-dojo-attach-point="close" data-dojo-type="dijit/form/Button" type="button" button="type"
                    data-dojo-attach-event="onClick: _onClose">${i18n.close}</button>
        </div>
        <style>
            .ExternalLink a.downloadLink {
                background: ${systemColor};
                text-shadow: 1px 1px 1px ${systemColor};
            }
        </style>
    </div>
</div>