define([
    'bnext/module/ConfigurationToAssignUserPositionAttender',
    'bnext/angularNavigator'
], 
function (
    ConfigurationToAssignUserPositionAttender,
    angularNavigator
) {
    function end() {
        var pendingBuilder = endHandler.pendingBuilder;
        if(!pendingBuilder) {
            console.error('Something went wrong, missing "pendingBuilder".');
            return;
        }
        pendingBuilder.rebuild();
    }
    function attend(pendingRecordId, entityId, pendingBuilder) {
        var pendingCamelName = pendingBuilder.pendingCamelName;
        if(!this[pendingCamelName] || !this[pendingCamelName].attend) {
            console.error('Unsupported configuration pending attender.');
            return;
        }
        endHandler.pendingBuilder = pendingBuilder;
        this[pendingCamelName].attend(pendingRecordId, entityId, pendingBuilder);
    }
    var 
        endHandler = {}, 
        configurationAttender = {
            configurationToAssignUserPosition: new ConfigurationToAssignUserPositionAttender({onSuccess: end}),
            configurationToActiveUser: {
                attend: function(pendingRecordId, entityId, pendingBuilder) {
                    var pendingUrl = 'v-user-list-to-activate.view?code=' + encodeURIComponent(pendingBuilder.pendingCode);
                    angularNavigator.navigateLegacy(pendingUrl);
                }
            },
            attend: attend
        }
    ;
    return configurationAttender;
});