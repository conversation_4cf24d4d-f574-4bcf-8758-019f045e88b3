define([
        'core', "dojo/_base/declare", "dijit/_WidgetBase",
        'bnext/i18n!./nls/DeviceToApproveServiceAttender',
        'bnext/callMethod',
        'dijit/Dialog',
        'dojo/_base/lang',
        'dojo/Deferred',
        'bnext/schedulingApproval/core',
        'dojo/dom-style',
        'dojo/_base/unload',
        'dojo/domReady!'
    ],
    function(
        core, declare, _WB, i18n,
        callMethod,
        Dialog, base,
        Deferred, authorization, domStyle

    ) {
        var dial = new Dialog(),
            auth_window;
        var
            DeviceToApproveSchedulingAttenderBase = {
                onEnd: core.$noop,
                onSuccess: core.$noop,
                constructor: function(args) {
                    base.mixin(this, args);
                },
                attend: function(pendingRecordId, serviceId) {
                    var def = new Deferred(),
                        _self = this;
                    var args = {
                        url: "../DPMS/Service.action",
                        method: "load",
                        params: [serviceId]
                    };
                    callMethod(args).then(function(r) {
                        var text = i18n.Service;
                        text = base.replace(text, r);
                        if (auth_window && auth_window.destroyRecursive) {
                            auth_window.destroyRecursive();
                        }
                        auth_window = new authorization({
                            description: text, 
                            department: r.schedule.device.department.description,
                            deviceGroup: r.schedule.device.deviceGroup.description,
                            deviceCode: r.schedule.device.code,
                            serviceType: r.result.serviceType.description,
                            responsible: r.attendant.description,
                            onCancel: function() {
                                dial.hide();
                                showLoader();
                                core.navigate('pendings');
                            },
                            onReject: function() {
                                var value = this.comment.value;
                                if (core.isNull(value)
                                        || value.trim().replace(/\n|\r/g, "") === '') {
                                    core.dialog(this.tags.missingComments, core.i18n.Validate.accept);
                                    return;
                                }
                                callMethod({
                                    url: 'Service.action',
                                    method: 'processService',
                                    params: [serviceId, this.comment.value, false]
                                }).then(function(x) {
                                    _self.onEnd();
                                    _self.onSuccess();
                                    dial.hide();
                                    showLoader();
                                    core.dialog(core.i18n.pending_success, core.i18n.accept_label).then(
                                        function () {
                                            core.navigate('pendings');
                                        }
                                    );
                                });
                            },
                            onAccept: function() {
                                var value = this.comment.value;
                                if (core.isNull(value)
                                        || value.trim().replace(/\n|\r/g, "") === '') {
                                    core.dialog(this.tags.missingComments, core.i18n.Validate.accept);
                                    return;                                    
                                }
                                callMethod({
                                    url: 'Service.action',
                                    method: 'processService',
                                    params: [serviceId, this.comment.value, true]
                                }).then(function(x) {
                                    _self.onEnd();
                                    _self.onSuccess();
                                    showLoader();
                                    core.dialog(core.i18n.pending_success, core.i18n.accept_label).then(
                                        function () {
                                            core.navigate('pendings');
                                        }
                                    );
                                    dial.hide();
                                });
                            }
                        });
                        domStyle.set(auth_window.get('reject').domNode, 'display', 'none');
                        auth_window.placeAt(dial);
                        dial.show();
                        dial.set('title', i18n.approveTitle);
                        dial.set('class', 'fixedTop');
                        core.hideLoader();
                    }, core.hideLoader);
                    return def.promise;
                }
            },
            DeviceToApproveSchedulingAttender = declare([_WB], DeviceToApproveSchedulingAttenderBase);
        return DeviceToApproveSchedulingAttender;;
    });