define(['bnext/i18n!lang/nls/en/main'], function (main) {
    return {
        periodicity: 'Periodicity:',
        repeat: 'Repeat:',
        never: 'Never',
        daily: 'Daily',
        weekly: 'Weekly',
        monthly: 'Monthly',
        yearly: 'Yearly',
        hybrid: 'Combined',
        every: 'Every: ',
        days: 'day(s).',
        weeks: 'week(s).',
        months: 'month(s).',
        years: 'year(s).',
        dayOfWeek: 'Day of week',
        dayOfMonth: 'Day of month',
        monday: 'Moday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        saturday: 'Saturday',
        sunday: 'Sunday',
        repeatOn: 'Repeat on:',
        repeatBy: 'Repeat by:',
        periodicityDescription: 'Description:',
        mondayFull: 'monday, ',
        tuesdayFull: 'tuesday, ',
        wednesdayFull: 'wednesday, ',
        thursdayFull: 'thursday, ',
        fridayFull: 'friday, ',
        saturdayFull: 'saturday, ',
        sundayFull: 'sunday, ',
        theDay: 'the day ',
        the: 'the ',
        ofTheMonth: ' of the month',
        dateRankInitial: 'Initial date:',
        dateRankfinal: 'End date:',
        sTime: 'Start Time:',
        fTime: 'End time:',
        lblUsers: 'User:',
        buttonsSelectUser: 'User',
        lblBreakDateType: 'Type:',
        breakDateGeneralType: 'General',
        breakDateByUserType: 'By user',
        notOnce: 'The {event} will never happen.',
        defaultEventTag: 'service',
        firstService: 'The first {event} will be on {data}.',
        Periodicity: {
            never: 'Next {event} on {date} and will never repeat.',
            daily: 'Next {event} on {date} and will repeat every {days} day(s).',
            weekly: 'Next {event} on {date} and will repeat every {weeks} week(s), the day(s) {monday}{tuesday}' +
                    '{wednesday}{thursday}{friday}{saturday}{sunday}.',
            monthly: 'It\'s repeated every month, on day {day} of the last week of the month until {date}',
            yearly: 'Next {event} on {date} and will repeat every {years} year(s).',
            hybrid: 'Next {event} on {date} and will repeat every {years} year(s),{months} month(s), {weeks} week(s)' +
                    ' and {days} day(s).'
        },
        PeriodicityEvent: {
            never: 'Single event.',
            daily: 'Occurs every {days} day(s).',
            weekly: 'Occurs every {weeks} week(s), the day(s) {monday}{tuesday}{wednesday}{thursday}{friday}{saturday}{sunday}.',
            monthly: 'Occurs every {months} month(s), {option}',
            yearly: 'Occurs every {years} year(s).',
            hybrid: 'Occurs every {years} year(s),{months} month(s), {weeks} week(s) and {days} day(s).'
        },
        Ordinals: ['first ', 'second ', 'third ', 'fourth ', 'last ', 'last '],
        dayslst: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
        numericOnly: 'This field only accept numeric values.',
        min: 'Must be greater than 0.',
        selectDay: 'You must select at least  one day. \nToday will be automatically selected.',
        accept: 'Accept',
        buttonsAccept: main.accept,
        buttonsCancel: main.cancel,
        quantityDailyMessage: 'The field each N day must be greater than zero.',
        quantityWeeklyMessage: 'The field each N week must be greater than zero.',
        quantityMonthlyMessage: 'The field each N month must be greater than zero.',
        quantityYearlyMessage: 'The field each N year must be greater than zero.',
        quantityHybridMessage: 'Must capture at least an amount greater than zero.',
        msgCaptureDate: 'Capture date',
        msgInvalidDate: 'Invalid date',
        msgInvalidType: 'Select a type',
        msgSelectAUser: 'Select a user',
        msgInvalidPeriodicity: 'Invalid periodicity, please fill all fields.',
        generating: 'Generating...'
    };
});