define(['dojo/dom-class', 'bnext/_base/media-queries', "dojo/_base/window", "dojo/has", "dojo/_base/sniff", 'dojo/domReady!'],
        function(domClass, MediaQueries, win, has) {
            return {
                load: function(id, req, load) {
                    var n = navigator, dav = n.appVersion, w = window, body = win.body();
                    if (body) {
                        if (dav.contains && dav.contains('Trident')) { // Trindent
                            domClass.add(body, 'isTRIDENT');
                        } else {
                            domClass.add(body, 'notTRIDENT');
                        }
                        
                        if (MediaQueries.isIPhone()) {
                           domClass.add(body, 'iphone');
                        }
                        
                        if (MediaQueries.isAndroid()) {
                           domClass.add(body, 'android');
                        }

                        if (has("ie")) { // Internet Explorer
                            domClass.add(body, 'isIE isIE' + has("ie"));
                        } else {
                            domClass.add(body, 'notIE');
                        }

                        if (has("ff")) { // Firefox 
                            domClass.add(body, 'isFF isFF' + has("ff"));
                        } else {
                            domClass.add(body, 'notFF');
                        }

                        if (has("mozilla")) { // Firefox 
                            domClass.add(body, 'isMOZILLA isMOZILLA' + has("mozilla"));
                        } else {
                            domClass.add(body, 'notMOZILLA');
                        }

                        if (has("opera")) { // Opera 
                            domClass.add(body, 'isOPERA isOPERA' + has("opera"));
                        } else {
                            domClass.add(body, 'notOPERA');
                        }

                        if (has("safari")) { // Opera 
                            domClass.add(body, 'isSAFARI isSAFARI' + has("safari"));
                        } else {
                            domClass.add(body, 'notSAFARI');
                        }

                        if (has("chrome")) { // Opera 
                            domClass.add(body, 'isCHROME isCHROME' + has("chrome"));
                        } else {
                            domClass.add(body, 'notCHROME');
                        }

                        if (
                            typeof w.top !== 'undefined' 
                            && w.top !== null
                            && typeof w.top.parentClose !== 'function' 
                            && !(w.top.frames && w.top.frames.length && window.name === 'basefrm')
                        ) {
                            domClass.add(body, 'is-solo-true');
                        } else {
                            domClass.add(body, 'is-solo-false');
                        }
                    }
                    load();
                }
            };
        });