define(['core', 'dojo/promise/all',
        'bnext/gridIcons', 'bnext/FieldListUtil',
        'bnext/DynamicFieldUtil',
        'dojo/_base/declare', 'dojo/_base/lang', 'bnext/callMethod', 'dojo/on', 'dojox/html/entities', 'dojo/dom-construct',
        'dojo/dom-class', 'dojo/dom-style', 'dojo/dom', 'dojo/_base/array', 'dojo/dom-attr', 'dojo/query', 'dojo/string', 'dojo/keys',
        'dijit/registry', 'dojo/json', 'bnext/gridComponentUtil', 'bnext/exampleMatch', 'bnext/_base/media-queries',
        'dijit/form/NumberSpinner',
        'bnext/module/ChipContainer',
        'bnext/module/Chip',
        'dijit/popup', 'dojo/date/locale',
        'dojox/layout/ContentPane',
        'loader',
        'dijit/Tooltip', 'bnext/_base/BnextDateTextBox', 'dijit/form/TimeTextBox', 'dijit/form/FilteringSelect', 'dijit/form/ValidationTextBox', 'dojox/form/CheckedMultiSelect',
        'bnext/i18n!./nls/gridComponent', 'dojo/aspect', 'timeago/Timeago', './misc', 'dojo/store/Memory', 'dojo/Deferred', 'bnext/gridCubes', 'bnext/columnTypes',
        'xstyle/css!./styles/gridComponent.css',
        'jquery.floatThead.min'
    ],
function(core, all, gridIcons, FieldListUtil, DynamicFieldUtil, declare, lang, callMethod, on, entities, domConstruct,
    domClass, domStyle, dom, array, domAttr, query, string, keys,
    registry, JSON, gcUtil, exampleMatch, MediaQueries, NumberSpinner,
    ChipContainer, Chip, popup, locale, ContentPane, loader,
    Tooltip, BnextDateTextBox, TimeTextBox, FilteringSelect, ValidationTextBox, CheckedMultiSelect,
    i18n, aspect, Timeago, BnextMisc, Memory, Deferred,
    gridCubes, columnTypes
) {
    Tooltip.defaultPosition = ['below'];
    var $GET = core.getUrlVars();
    var $noop = function() {},
        systemColor, w = window,
        cType = gcUtil.cType,
        windowPath = gcUtil.getWindowPath();
    w.gridCubes = gridCubes; //<--- backward compatibility
    w.columnTypes = columnTypes; //<--- backward compatibility
    w.gridIcons = gridIcons; //<--- backward compatibility
    var SHORT_DELIMITER_REGEX = new RegExp("S$");
    
    var excel_icon = '<svg class="fill-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" aria-labelledby="iems-excel-desc iems-excel-title" fit="" preserveAspectRatio="xMidYMid meet"><title id="iems-excel-title">Microsoft Excel Icon</title><desc id="iems-excel-desc">A picture showing the Microsoft Excel logo.</desc><path d="M12.833 11.476v-3.6h.994v3.6zm1.993 0H22v-3.6h-7.174zM6.652 3.552v2.734h4.728a1.449 1.449 0 011.161.589h1.284V2.7H7.5a.853.853 0 00-.848.852zm15.338 8.924h-7.164v3.675H22s-.016-2.588-.01-3.675zm-9.758-4.737v8.527a.852.852 0 01-.852.853H2.853A.853.853 0 012 16.266V7.739a.853.853 0 01.853-.853h8.527a.852.852 0 01.852.853zm-2.64 7.035l-1.728-2.795 1.685-2.747H8.336l-.959 1.751q-.1.189-.186.39h-.012a4.283 4.283 0 00-.17-.375l-.9-1.767H4.791L6.434 12 4.64 14.774h1.3l1.067-1.948a1.231 1.231 0 00.12-.251h.015a1.708 1.708 0 00.124.259l1.034 1.94zM21.147 2.7h-6.321v4.175H22V3.553a.853.853 0 00-.853-.853zM7.476 21.3zm13.7 0zm-7.344-4.149v-4.675h-.994v3.79a1.455 1.455 0 01-1.453 1.453H6.652c0 1.154.017 2.391.017 2.822a.8.8 0 00.807.758h13.7a.8.8 0 00.807-.758c0-.526.017-1.481.017-2.822v-.568z"></path></svg>';
    
    var MessageTypes = {
        INCORRECTSINTAX: 'Incorrect syntax near',
        INVALIDOBJECT: 'Invalid object name'
    };
    
    var indexGenerator = 0;

    const MAX_PAGED_RECORDS = 30_000;
    
    function attachEvents(target, action, params, event, useOnce, column) {
        if (!action) {
            return 0;
        }
        /**
         * Recuerdese que si 'action' viene como 'string' debe estar declarada 'Global'
         **/
        action = typeof action === 'string' ? window[action] : action;
        function callbackEvent(e) {
            try {
                if (column && column.href !== null && typeof column.href !== 'undefined' && column.href !== 'javascript:void(0)' && e.ctrlKey) {
                    return;
                }
                action.apply(target || window, params);
            } catch (e) {
                core.error({
                    e: e,
                    params: params
                });
                console.debug('-->> error \'attachEvents\' [{target},{params},{stack}] : [{');
                console.debug(target);
                console.log('},{');
                console.debug(params);
                console.log('},{');
                console.debug(e.stack);
                console.log('}]');
            }
        }
        if (useOnce) {
            on.once(target, event || 'click', callbackEvent);
        } else {
            on(target, event || 'click', callbackEvent);
        }
        return 1;
    }
    
    function encode(value) {
        if (isNull(value) || value === "") {
            return "-";
        }
        if (isNaN(value)) {
            return entities.encode(value.toString());
        }
        return value.toString();
    }

    function likeDndType(dndType) {
        if (dndType === "dijit.form.ValidationTextBox") {
            return true;
        }
        return false;
    }

    function notEmpty(criterio) {
        return !core.isNull(criterio) && criterio !== '' && criterio === criterio;
    }

    function createAutocomplete(columna, contenedor) {
        var elemento = domConstruct.create('input', {
            'type': 'text',
            'id': columna.id,
            'style': {
                'width': '180px'
            }
        }, contenedor);
        try {
            new autocomplete('autocomplete_' + columna.id, columna.id, columna.searchObj.daoHibernate, columna.searchObj.daoData, elemento, columna.searchObj.serviceValue);
        } catch (e) {
            log('Error!! ' + e);
        }
        return elemento;
    }

    function deferRowPush(data, j, bean, displayedResultsRows, len, currentLocalPage, def) {
        var func = function() {
            if (!data[j]) {
                def.resolve(j);
                return;
            }
            var r = this.pushRow(data[j], j);
            displayedResultsRows.push(({
                'row': r
            }));
            if (bean.count > 0 && this.getResultsInfo()) {
                //cuando acabe de pushear todo
                var inicio = this.getCurrentPage() * this.getPageSize(),
                    fin = inicio + this.getRowCount(),
                    pathExcel = require.toUrl("bnext/images/" + gridIcons.excel),
                    footerInfo = "<div class='totalPages pagination_right' id='totalPages'><span class='spanTotalPages' id='pageCount_" + this.id + "'>" + i18n.showingFrom + "</span></div>";
                footerInfo = lang.replace(footerInfo, {
                    inicio: inicio + 1,
                    fin: fin,
                    cantidad: bean.count,
                    resultados: this.entityName
                });
                if(this.getPaginationInfo()!== null && !this.getEnablePaginationModern()){
                    this.getPaginationInfo().innerHTML = footerInfo;
                }
                if (this.bean.count <= this.bean.data.length) {
                    domClass.add(this.getResultsInfo(), 'displayNone');
                } else {
                    domClass.remove(this.getResultsInfo(), 'displayNone');
                }
            } else if (bean.count > 0 && !this.getResultsInfo()) {
                console.warn("Pagination error, please verify that resultsInfo is defined. Ex: resultsInfo:\"#auto\"");
            }
            if (this.getRowCount() === len) {
                if (len - 1 === j) {
                    if (typeof this.onLoaded === 'function') {
                        this.onLoaded.call(this, this);
                    }
                }
                if (typeof this.onLoadedByRow === 'function') {
                    this.onLoadedByRow.call(this, this);
                }
            }
            def.resolve(j);
        };
        return func;
    }
    var gridComponentArgs = {
        columns: [],
        paramsGet: [],
        openSearchZone: true,
        dijitScope: 'grid-component',
        dynamicSearchEnabled: false,
        dynamicSearchColumnsEnabled: true,
        dynamicSearchFields: null,
        parseFromDynamicResults: false,
        parseResultsUnderscoreToDot: false,
        claseRow: 'grid-row even',
        rowLength: 0,
        messages: 0,
        dataExcelResultsRows: [],
        dataExcelResultsNames: [],
        dataExcelNames: [],
        dataExcelResultsTypes: [],
        resultsInfo: "#auto",
        resultsInfoUp: null,
        noRegMessage: null,
        emptyMessage: null,
        searchContainer: '#auto',
        searchContainerIsHidden: true,
        paginationInfo: '#auto',
        paginationModern: false,
        noEyes: false,
        noPagination: false,
        table: null,
        enableStatistics:false,
        id: "",
        size: 20,
        gridLocalSize: 600,
        gridLocalPage: 0,
        gridLocalDefaultOrder: null,
        field: "",
        dataMap: {},
        direction: 1,
        totalPages: 0,
        currentPage: 0,
        serviceStore: null,
        maxHeightEnabled: true,
        methodName: null,
        methodExtraFilter: [],
        bean: null,
        extraCriteria: [],
        extraLikeCriteria: [],
        freezeHeader: true,
        infragisticsChartInit: null,
        statisticsEnvelope: null,
        statisticsExcludes:[],
        resizable: true,
        localFreezeHeader: false,
        localResizable: false,
        freezeHandler: null,
        searchCriteriaOpen: false,
        searchFilterPanel: false,
        searchPanelIcon: false,
        searchButton: null,
        closeFiltersButton: null,
        supportedChipsTypes: ['multiple','dynfield', 'catalog-multiple', 'multiple-entity', 'multiple-text', 'texto', 'fecha'],
        nullDefaultText: '-',
        isSearchCriteriaOpen: function() {
            return this.searchCriteriaOpen;
        },
        addParamGet: function(name, value) {
            var p = {};
            p[name] = value;
            this.paramsGet.push(p);
        },
        setParamGet: function(name, value) {
            var p = {};
            p[name] = value;
            if (!this.paramsGet) {
            this.paramsGet = [p];
            } else {
                this.paramsGet.push(p);
            }
        },
        clearParamGet: function() {
            this.paramsGet = [];
        },
        getConcatenateParams: function() {
            var params = '?',
                val, param;
            if (this.serviceStore && this.paramsGet.length) {
                for (var idx in this.paramsGet) {
                    if (!this.paramsGet.hasOwnProperty(idx)) {
                        continue;
                    }
                    param = this.paramsGet[idx];
                    for (var key in param) {
                        if (!param.hasOwnProperty(key)) {
                            continue;
                        }
                        val = encodeURIComponent(param[key]);
                        if (params === '?') {
                            params += key + '=' + val;
                        } else {
                            params += '&' + key + '=' + val;
                        }
                    }
                }
            } else {
                params = '';
            }
            return params;
        },
        displayedResults: null,
        type: 'gridComponent',
        hiddenResults: null,
        searchWidgets: null,
        noExcel: null,
        noSettings: null,
        container: null,
        eyeIconName: "visibility",
        eyeIconNameHidden: "visibility_off", 
        displayButtonOpen: false,
        rowCount: 0,
        onLoaded: null,
        onLoadedByRow: null,
        onBeforeRefresh: null,
        onPopRow: null,
        onPushRow: null,
        refreshOnPopRow: true,
        onClicResultsInfo: $noop,
        toogleEstatus: null,
        tools: new BnextMisc(i18n.$locale),
        excelExport: true,
        hookUpdateSuccess: null,
        hookUpdateFailure: null,
        editRecordIcon: gridIcons.edit,
        loggingIcon: gridIcons.log,
        deleteRecordIcon: gridIcons['delete'],
        genericGridIsSortable: true,
        statusList: null,
        codeDefaultDisplay: '',
        codeLabel: null,
        editRecordLabel: i18n.edit_column,
        loggingLabel: i18n.logging_column,
        deleteRecordLabel: i18n.delete_column,
        entityName: i18n.results,
        noHeader: false,
        autoStatusSearch: true,
        searchBusy: false,
        rowsLoading: false,
        createBehaviour: false,
        filterChips: {},
        windowPath: "#invalid",
        savedWindowFilter: "#invalid",
        dateFormat: gcUtil.getLocaleDateFormat(i18n.$locale),
        constructor: function(parameters) {
            this.dynamicSearchFields = [];
            var gridComponentReady = new Deferred();
            if (parameters.windowPath) {
                this.windowPath = parameters.windowPath;
            } else {
                console.warn('GRID ' + parameters.id + ': windowPath setted to "' + windowPath + '"');
            }
            if (parameters.savedWindowFilter) {
                this.savedWindowFilter = parameters.savedWindowFilter;
            }
            this.extraCriteria = [];
            this.extraLikeCriteria = [];
            if (!parameters) {
                throw {
                    name: 'null stuff exception',
                    message: 'parameters needed'
                };
            }
            this.displayedResults = {
                'actual': '-1',
                'displayNames': [],
                'displayTypes': [],
                'displayData': []
            };
            this.hiddenResults = [];
            this.searchWidgets = [];
            var columns;
            columns = parameters.columns;
            if (columns && (columns.length || columns.is === "#auto")) {
                array.forEach(columns, function(col, i) {
                    if (!col.id || typeof col.type !== 'number') {
                        throw {
                            message: 'for the column on index ' + i + ' either id or type is missing',
                            name: 'invalid column exception',
                            column: col
                        };
                    }
                });
            } else if (columns) {
                console && console.warn({
                    name: 'empty columns array',
                    message: 'you should define columns ' + parameters.id
                });
            } else {
                throw {
                    name: 'bad columns array',
                    message: 'columns must not be null or undefined for grid ' + parameters.id
                };
            }
            if (parameters.noHeader) {
                this.noEyes = true;
                this.noPagination = true;
                this.refreshOnPopRow = false;
            }
            declare.safeMixin(this, parameters);
            systemColor = (
                dom.byId('SYSTEM_COLOR') || {
                    value: '#00f'
                }
            ).value;
            switch (systemColor.length) {
                case 4: // #FFF
                    systemColor = systemColor + '5';
                    break;
                case 7: // #FFFFFF
                    systemColor = systemColor + '50';
                    break;
                case 9: // #FFFFFFFF
                    systemColor = systemColor.substring(0, 6) + '50';
                    break;
            }
            this.editRecordActionDefault = "editRecord_" + this.id;
            this.loggingActionDefault = "viewLog";
            this.deleteRecordActionDefault = "deleteRecord_" + parameters.id;

            this.statusList = this.statusList ? this.statusList : [{
                'name': i18n.firstComboElement, // Seleccione
                'icon': 'cubo6.gif',
                'value': ''
            }, {
                'name': i18n.STATUS[1], // Green Cube
                'icon': 'cubo4.gif',
                'value': '1'
            }, {
                'name': i18n.STATUS[0], // Gray Cube 
                'icon': 'cubo6.gif',
                'value': '0'
            }];

            var _self = this;
            if (parameters && lang.exists("id", parameters) && lang.exists("container", parameters)) {
                this.table = dom.byId(parameters.container);
                if (!this.table) {
                    throw new Error(''
                        + 'No existe la TABLE configurada para el grid, con tag de container #' + parameters.container
                    );
                    return;
                }
                var nodeTag = this.table.nodeName ? this.table.nodeName.toString().toLowerCase() : '';
                var tableTag = this.table.tagName ? this.table.tagName.toString().toLowerCase() : '';
                if (nodeTag !== 'table' && tableTag !== 'table') {
                    throw new Error(''
                        + 'Existe una mala configuracion en el grid, el tag del container #' + parameters.container 
                        +  ' debe ser un  <table>, actualmente es <' + (this.table.nodeName || this.table.tagName || 'null').toString().toLowerCase() + '>'
                    );
                    return;
                }

                this.id = parameters.id;
                this.setContainer(parameters.container);

                domAttr.set(parameters.container, {
                    className: 'display',
                    border: 0,
                    cellSpacing: 0,
                    cellPadding: 0
                });
                domClass.add(this.getContainer(), 'grid-component-container');
                if (lang.exists("columns", parameters)) {
                    var columnas = [];
                    if (parameters.columns.is === '#auto') {
                        if (lang.exists("toogleEstatus", parameters.columns) && typeof parameters.columns.toogleEstatus === 'function') {
                            this.toogleEstatus = parameters.columns.toogleEstatus;
                        }
                        if (lang.exists("editRecordActionDefault", parameters.columns)) {
                            this.editRecordActionDefault = parameters.columns.editRecordActionDefault;
                        }
                        if (lang.exists("editRecordIcon", parameters.columns)) {
                            this.editRecordIcon = parameters.columns.editRecordIcon;
                        }
                        if (lang.exists("editRecordLabel", parameters.columns)) {
                            this.editRecordLabel = parameters.columns.editRecordLabel;
                        }
                        if (lang.exists("genericGridIsSortable", parameters.columns) && parameters.columns.genericGridIsSortable) {
                            this.genericGridIsSortable = parameters.columns.genericGridIsSortable;
                        }
                        if (lang.exists("deleteRecordActionDefault", parameters.columns) && parameters.columns.deleteRecordActionDefault) {
                            this.deleteRecordActionDefault = parameters.columns.deleteRecordActionDefault;
                        }
                        columnas.push({
                            title: "ID",
                            type: 0,
                            id: "id",
                            display: "none",
                            isTransient: false
                        });
                        if (typeof parameters.noDefaultCode === 'undefined') {
                            columnas.push({
                                title: this.codeLabel || i18n.colNames.code,
                                type: 1,
                                id: 'code',
                                display: this.codeDefaultDisplay,
                                searchObj: {
                                    col: 1,
                                    type: 'texto'
                                },
                                attributes: {
                                    style: {
                                        width: "120px"
                                    }
                                },
                                isSortable: this.genericGridIsSortable
                            });
                        }
                        if (!parameters.noDefaultDescription) {
                            columnas.push({
                                title: parameters.defaultDescriptionTag || i18n.colNames.description,
                                type: 1,
                                id: 'description',
                                display: parameters.defaultDescriptionDisplay || "",
                                searchObj: {
                                    col: 1,
                                    type: 'texto'
                                },
                                isSortable: this.genericGridIsSortable
                            });
                        }
                        if (lang.exists("extraColumns", parameters.columns)) {
                            array.forEach(parameters.columns.extraColumns, function(columna) {
                                columnas.push(columna);
                            });
                        }
                        if (lang.exists("logging_column", parameters.columns)) {
                            columnas.splice(0, 0, {
                                title: this.loggingLabel,
                                id: "logging",
                                type: 7,
                                attributes: {
                                    style: {
                                        width: "50px"
                                    }
                                },
                                action: this.loggingActionDefault,
                                parameters: ["id"],
                                icon: this.loggingIcon,
                                isSortable: this.genericGridIsSortable
                            });
                        }
                        if (lang.exists("delete_column", parameters.columns)) {
                            columnas.splice(0, 0, {
                                title: this.deleteRecordLabel,
                                id: "delete",
                                type: 7,
                                attributes: {
                                    style: {
                                        width: "50px"
                                    }
                                },
                                action: this.deleteRecordActionDefault,
                                parameters: ["id"],
                                icon: this.deleteRecordIcon,
                                isSortable: this.genericGridIsSortable
                            });
                        }
                        if (!lang.exists("noEdit", parameters.columns)) {
                            var doNotPush = dom.byId('noGridEditAccess') && dom.byId('noGridEditAccess').value === 'true';
                            if (!doNotPush) {
                                columnas.splice(0, 0, {
                                    title: this.editRecordLabel,
                                    id: "edit",
                                    type: 7,
                                    attributes: {
                                        style: {
                                            width: "50px"
                                        }
                                    },
                                    action: this.editRecordActionDefault,
                                    parameters: ["id", "code", "description", "status"],
                                    icon: this.editRecordIcon,
                                    isSortable: this.genericGridIsSortable
                                });
                            }
                        }

                        columnas.splice(0, 0, {
                            title: i18n.colNames.status,
                            type: 13,
                            id: "status",
                            key: 'icon',
                            value: 'value',
                            display: "",
                            attributes: {
                                style: {
                                    width: "50px"
                                }
                            },
                            parameters: ["id", "code", "description", "status"],
                            searchObj: this.autoStatusSearch ? {
                                col: 1,
                                type: "combo",
                                list: this.statusList
                            } : {},
                            action: this.toogleEstatus,
                            list: this.statusList,
                            isSortable: this.genericGridIsSortable
                        });
                    } else {
                        columnas = parameters.columns.concat(columnas);
                    }
                    if (lang.exists("logging_column", parameters)) {
                        var firstTextIndex = array.indexOf((
                            array.map(columnas, function(c) {
                                return c.type;
                            })
                        ), 1) || 1;
                        columnas.splice(firstTextIndex, 0, {
                            title: this.loggingLabel,
                            id: "logging",
                            type: 7,
                            attributes: {
                                style: {
                                    width: "50px"
                                }
                            },
                            action: this.loggingActionDefault,
                            parameters: ["id"],
                            icon: this.loggingIcon,
                            isSortable: this.genericGridIsSortable
                        });
                    }
                    this.setColumns(columnas);
                }
                if (this.getSearchContainer() === '#auto' && !lang.exists("searchContainer", parameters)) {
                    parameters.searchContainer = null;
                }
                if (this.getPaginationInfo() === '#auto' && !lang.exists("paginationInfo", parameters)) {
                    parameters.paginationInfo = null;
                }
                if (this.getResultsInfo() === '#auto' && !lang.exists("resultsInfo", parameters)) {
                    parameters.resultsInfo = null;
                }
                if (lang.exists("resultsInfo", parameters)) {
                    if (parameters.resultsInfo === '#auto') {
                        this.setResultsInfo(domConstruct.create('div', ({
                            'class': 'dataTables_info grid_' + this.id
                        }), this.table, 'after'));
                    } else {
                        this.setResultsInfo(parameters.resultsInfo);
                    }
                }
                if (lang.exists("searchContainer", parameters)) {
                    if (parameters.searchContainer === '#auto') {
                        if (dom.byId('col1')) {
                            this.setSearchContainer(dom.byId('col1'));
                        } else {
                            var
                                _self = this,
                                searchContainer = domConstruct.create('form', {
                                    'class': 'hide-on-linked-grid',
                                    'method': 'post',
                                    'action': '#'
                                }, parameters.container, 'before'),
                                superEnvelope = domConstruct.create('div', {
                                    'class': 'envelope border_b_light_gris search-style'
                                }, searchContainer),
                                iconOpenSearch = require.toUrl("bnext/images/ico_busqueda.gif").toString(),
                                iconOpenedSearch = require.toUrl("bnext/images/ico_busquedaEsconder.gif").toString(),
                                pathFilter = require.toUrl("bnext/images/filtered.png").toString()
                            ;
                            domClass.add(searchContainer.parentNode, 'grid-filled-container');
                            this.enableStatistics = false;
                            if (this.enableStatistics) {
                                require(['bnext/grid-component-charts'], lang.hitch(this, function(gcCharts) {
                                    this.infragisticsChartInit = gcCharts.buildChartsEnvelope(this);
                                    this.statisticsEnvelope = this.infragisticsChartInit.envelope;
                                }));
                            }
                            this.toggleSearchFieldsAction = function() {
                                var open = domClass.contains(searchContainerUL, "displayNone");
                                _self.toggleSearchFields(open);
                            };
                            this.toggleSearchFields = function(open, search = true) {
                                require(['bnext/angularFabMenu'], lang.hitch(this, function(angularFabMenu) {
                                    open ? domClass.remove(searchContainerUL, "displayNone") : domClass.add(searchContainerUL, "displayNone");
                                    if (MediaQueries.isMobilDevice() || MediaQueries.isSmallTouchDevice()) {
                                        angularFabMenu.displayFabMenu(!open);
                                    }
                                    _self.searchCriteriaOpen = open;
                                    if (this.searchPanelIcon) {
                                        if (_self.searchCriteriaOpen) {
                                            domAttr.set(_self.searchPanelIcon, "src", iconOpenedSearch);
                                        } else {
                                            if (!MediaQueries.isDesktop() && search) {
                                                loader.showLoader();
                                            }
                                            domAttr.set(_self.searchPanelIcon, "src", iconOpenSearch);
                                        }
                                    }
                                    this._closeSearchFiltersPanels();
                                    if (MediaQueries.isDesktop() && search) {
                                        _self.gridResized();
                                    } 
                                }));
                            };
                            this.toggleSearchFilterPanel = function(open) {
                                if (open) {
                                    domStyle.set(_self.searchFilterPanel, "display", "block");
                                } else {
                                    domStyle.set(_self.searchFilterPanel, "display", "none");
                                }
                            };
                            domConstruct.create('div', {
                                'class': 'grid-update-container',
                                innerHTML: '' 
                                    + ' <div class="grid-update-last-updated">'
                                        + '<span id="last-update-' + this.id + '"></span>'
                                    + ' </div>'
                                    + ' <div class="grid-update-action"><span class="material-icons cursorPointer" id="last-update-search-' + this.id + '">refresh</span></div>'
                                    + ''
                            }, superEnvelope, 'before');
                            on(dom.byId('last-update-search-' + this.id), 'click', function() {
                                _self.refreshData();
                            });
                            var
                                searchOpen = $GET.search === 'open',
                                env = domConstruct.create('div', {
                                    'class': 'mv5 searchOpen-style',
                                    'data-cy': this.id + 'SearchOpen',
                                    innerHTML: '<i class="material-icons" id="icono_mostrar_' + this.id + '" style="float:left; padding-right:8px;"/>search</i>' +
                                        '<span class="search">' + i18n.menusearch + '</span>' +
                                        '<div class="emphasis filtered-label" id="' + this.id + '_emphasis"><span>' + i18n.filter + '</span> <img id="icono_mostrar_' + this.id + '" src="' + pathFilter + '" /></div>',
                                    onclick: this.toggleSearchFieldsAction
                                }, superEnvelope),
                                searchContainerUL = domConstruct.create('div', {
                                    'data-cy': this.id + 'SearchDialog',
                                    'class': 'searchDialog' + (!searchOpen || _self.searchContainerIsHidden ? ' displayNone' : ''),
                                    id: this.id + '_col1',
                                    style: { 
                                        'float': 'left' 
                                    },
                                    innerHTML: '' +
                                        '<div class="columnaBusqueda fancy-scroll">' +
                                        '<ul id="' + this.id + '_col1_1" class="left searchFields main-dynfield-container"></ul>' +
                                        '</div>'
                                }, superEnvelope);
                            var chipsLi = domConstruct.create('div', {
                                'class': 'search-filter-chips'
                            }, this.table, 'before');
                            var h5 = domConstruct.create('h5', {
                                innerHTML: i18n.filters
                            }, dom.byId(this.id + '_col1_1'));
                            if (MediaQueries.isMobilDevice() || MediaQueries.isSmallTouchDevice()) {
                                domStyle.set(h5, {
                                     display: 'flex',
                                    'justify-content': 'space-between',
                                    'align-items': 'center'
                                });
                                this.closeFiltersButton = domConstruct.create('div', {
                                    style: {
                                        'height': '1.5rem'
                                    },
                                    innerHTML: '<i class="material-icons">close</i>',
                                    'class': 'close-filter-button',
                                    onclick: function () {
                                        var open = domClass.contains(searchContainerUL, "displayNone");
                                        _self.toggleSearchFields(open, false);
                                    }
                                }, h5);
                            }
                            
                            this.filterChipsContainer = new ChipContainer({id: this.id + '_chips'});
                            this.filterChipsContainer.placeAt(chipsLi);
                            var searchCol = domConstruct.create('div', {
                                    'class': 'search_button_holder envelope txt-right pv10'
                                }, searchContainerUL);
                            this.searchFilterPanel = dom.byId(this.id + "_emphasis");
                            this.searchPanelIcon = dom.byId("icono_mostrar_" + this.id);
                            var searchButtonContainer = domConstruct.create('div', {
                                'class': 'button-container',
                                'title': + ' ' + i18n.hidden
                            }, searchCol);
                            this.searchButton = domConstruct.create('button', {
                                'class': 'rojo button raised-button ripple',
                                'title': + ' ' + i18n.hidden,
                                'type': 'button',
                                'data-cy': this.id + 'SearchButton',
                                innerHTML: i18n.applyFilters
                            }, searchButtonContainer);
                            on(this.searchButton, 'mousedown,touchstart',function(event) {
                                _self.toggleSearchFieldsAction();
                                if (_self.searchBusy) {
                                    console.error("New search called while executing a search");
                                } else {
                                    _self.searchBusy = true;
                                    if (_self.onClickSearch && typeof _self.onClickSearch === 'function') {
                                        _self.onClickSearch();
                                    }
                                    _self.newSearch();
                                    window['hideMsgs'] && hideMsgs();
                                    if (_self.expandableNode) {
                                        domClass.remove(_self.expandableNode, 'displayNone');
                                    }
                                }
                            });
                            var clearButtonContainer = domConstruct.create('div', {
                                'class': 'button-container'
                            }, searchCol);
                            domConstruct.create('button', {
                               'data-cy': this.id + 'ClearSearchButton',
                                'class': 'button ripple',
                                'type': 'button',
                                innerHTML: i18n.clear,
                                onclick: function(event) {
                                    _self.clearSearch();
                                }
                            }, clearButtonContainer);
                            var clearSearchButtonContainer = domConstruct.create('div', {
                                'class': 'button-container'
                            }, searchCol);
                            domConstruct.create('button', {
                                'class': 'unfilter button ripple',
                                'data-cy': this.id + 'ClearSearchFiltersButton',
                                'type': 'button',
                                innerHTML: i18n.unfilter,
                                onclick: function(event) {
                                    _self.clearSearch();
                                    if (_self.onClickSearch && typeof _self.onClickSearch === 'function') {
                                        _self.onClickSearch();
                                    }
                                    _self.newSearch();
                                    window['hideMsgs'] && hideMsgs();
                                    if (this.expandableNode) {
                                        domClass.remove(this.expandableNode, 'displayNone');
                                    }
                                }
                            }, clearSearchButtonContainer);
                            this.setSearchContainer(searchContainer); //*/
                            if (dom.byId('example_length')) {
                                domConstruct.place(dom.byId('example_length'), this.getSearchContainer(), 'after');
                            }
                        }
                    }
                } else if (dom.byId('col1')) {
                    this.setSearchContainer(dom.byId('col1'));
                }
                this.setResultsInfoUp(domConstruct.create('div', ({
                    'class': 'message_info mtop10',
                    'style': 'display:none',
                    onclick: this.onClicResultsInfo
                }), this.table, 'before'));
                if (lang.exists("paginationInfo", parameters)) {
                    if (parameters.paginationInfo === '#auto') {
                        this.setPaginationInfo(domConstruct.create('div', ({
                            'class': 'dataTables_paginate paging_full_numbers',
                            id: 'paging_full_numbers_' + this.id
                        }), this.table, 'after'));
                    } else {
                        this.setPaginationInfo(parameters.paginationInfo);
                    }
                }
                if (this.getResultsInfo() != null 
                        && ((this.excelExport && !this.noExcel) || !this.noEyes || !this.noPagination)) {
                    function closeDisplayColumnsOnScroll() {
                        _self.closeDisplayButtonOpen();
                        document.removeEventListener('scroll', closeDisplayColumnsOnScroll, {
                            passive: true
                        });
                    }
                    function closePaginateRowsOnScroll() {
                        _self.closePaginarButtonOpen();
                        document.removeEventListener('scroll', closePaginateRowsOnScroll, {
                            passive: true
                        });
                    }
                    this.gridOptionsDiv = domConstruct.create('div', ({
                        'class': 'grid-options',
                        'data-cy': this.id + '-options'
                    }), this.table, 'before');
                    if (this.excelExport && !this.noExcel) {
                        this.exportButton = domConstruct.create('div', ({
                            title: i18n.colNames["export"],
                            'data-cy': this.id + 'ExportToExcel',
                            'class': 'column-display-div button ripple outlined-button',
                            'innerHTML': '<i class="material-icons" title=' + i18n.colNames["export"] + '>'+excel_icon+'</i>'
                                    + '<span>' + i18n.exportLabel + '</span>'
                        }), this.gridOptionsDiv);
                        on(this.exportButton, 'click', lang.hitch(this, this.dialogExcel));
                    }
                    if(!this.noEyes) {
                        var searchContainerDom = this.getSearchContainer() ? dom.byId(this.getSearchContainer()) : null;
                        if (searchContainerDom) {
                            on(searchContainerDom, 'click', function () {
                                popup.close({
                                    popup: _self.displayDivContainer
                                });
                                _self.displayButtonOpen = false;
                                domClass.add(_self.displayDivContainer.domNode, 'displayNone');
                            });
                        }
                        this.displayButton = domConstruct.create('div', ({
                            title: i18n.hidden,
                            style: (this.noPagination && this.noExcel ? 'margin-right: 7rem;' : ''),
                            'class': 'column-display-div button ripple outlined-button',
                            innerHTML:'<i class="material-icons" title=' + i18n.hiddenLabel + '></i><span>0</span>',               
                            onclick: function(e) {
                                if (_self.displayButtonOpen) {
                                    _self.closeDisplayButtonOpen();
                                } else {
                                    var bottomButton = _self.displayButton.getBoundingClientRect().bottom;
                                    if (bottomButton > 0) {
                                        domStyle.set(_self.displayDiv, 'min-height', (bottomButton - 70) + 'px');
                                    }
                                    popup.open({
                                        popup: _self.displayDivContainer,
                                        around: _self.displayButton,
                                        orient:['above']
                                    });
                                    _self.displayButtonOpen = true;
                                    document.addEventListener('scroll', closeDisplayColumnsOnScroll, {
                                        passive: true
                                    });
                                    _self.closePaginarButtonOpen();
                                    domClass.remove(_self.displayDivContainer.domNode, 'displayNone');
                                    var checkBox = query('input[type="text"]', _self.displayDivContainer.domNode);
                                    if (checkBox.length > 0) {
                                        checkBox[0].focus();
                                    }
                                }
                            }
                        }), this.gridOptionsDiv);
                        this.displayDivContainer = new ContentPane({
                            'class': 'grid-options grid-options-panel grid-options-panel-right displayNone'
                        });
                        this.displayDivContainer.on('blur', function (evt) {
                            if (_self.displayButtonOpen) {
                                document.removeEventListener('scroll', closeDisplayColumnsOnScroll, {
                                    passive: true
                                });
                                _self.closeDisplayButtonOpen();
                            }
                        });
                        this.displayDiv = domConstruct.create('div', ({
                            className:'display-column-chooser fancy-scroll'
                        }));
                        this.renderColumnDisplay(this.displayDiv, parameters.escapeHtmlTitle);
                        this.displayDivContainer.set('content', this.displayDiv);
                    }
                    if(this.getResultsInfo() !== null && !this.noPagination) {
                            if (!this.getEnablePaginationModern()) {
                                this.paginarButton = domConstruct.create('div', ({
                                    title: i18n.paginate,
                                    'data-cy': this.id + 'Paginate',
                                    'class': 'column-display-div button ripple outlined-button paginateButton',
                                    'innerHTML': '<i class="material-icons" title=' + i18n.paginate + '>layers</i><span>'
                                            + i18n.paginate + '</span>',
                                    onclick: function (e) {
                                        if (_self.paginarButtonOpen) {
                                            _self.closePaginarButtonOpen();
                                        } else {
                                            popup.open({
                                                popup: _self.paginarDivContainer,
                                                around: _self.paginarButton,
                                                orient: ['above']
                                            });
                                            _self.paginarButtonOpen = true;
                                            document.addEventListener('scroll', closePaginateRowsOnScroll, {
                                                passive: true
                                            });
                                            _self.closeDisplayButtonOpen();
                                            domClass.remove(_self.paginarDivContainer.domNode, 'displayNone');
                                            var textBox = query('input[type="number"]', _self.paginarDivContainer.domNode);
                                            if (textBox.length > 0) {
                                                textBox[0].focus();
                                            }
                                        }
                                    }
                                }), this.gridOptionsDiv);
                            }
                            var proxy = this;
                            var setPagination = lang.hitch(this, function(e) {
                                var paginationSize = null;
                                if (e.target.classList.contains('pagination-item')) {
                                    paginationSize = e.target.value;
                                    this.getPaginationInfo().querySelectorAll('.pagination-item').forEach(function (item) {
                                        item.classList.remove('focus-pagination');
                                    });
                                    e.target.classList.add('focus-pagination')
                                } else {
                                    paginationSize = dom.byId('paginationSize' + proxy.id).value
                                }
                                var validator = /^[0-9]+$/;
                                if (validator.test(paginationSize)) {
                                    paginationSize = +paginationSize;
                                    if (paginationSize < +e.target.min || paginationSize > +e.target.max) {
                                        var max = new Intl.NumberFormat().format(+e.target.max);
                                        var msg = i18n.invalidRangePaginationSize.replace('{max}', max);
                                        core.dialog(msg);
                                    }  else {
                                        if (this.getPageSize() === paginationSize) {
                                            return;
                                        }
                                        this.searchBusy = true;
                                        this.setPageSize(paginationSize, true).then(function() {
                                          proxy.searchBusy = false;
                                        });
                                    }
                                } else {
                                    core.dialog(i18n.onlyNumbers);
                                }
                            });
                        var isEnter = function(e) {
                            e.preventDefault();
                            this.setOpenSearchZone(false);
                            setPagination(e);
                        };
                        var keyPress = function(e) {
                            if (e.keyCode === keys.ENTER) {
                                e.preventDefault();
                                setPagination(e);
                            }
                        };
                        this.paginarDivContainer = new ContentPane({
                            'class': 'grid-options grid-options-panel divPaginar displayNone'
                        });
                        this.paginarDivContainer.on('blur', function (evt) {
                            if (_self.paginarButtonOpen) {
                                document.removeEventListener('scroll', closePaginateRowsOnScroll, {
                                    passive: true
                                });
                                _self.closePaginarButtonOpen();
                            }
                        });
                        this.paginarDiv = domConstruct.create('div', ({
                            className:'display-column-chooser divContent'
                        }));
                        var contPage2 = domConstruct.create('div', ({
                            id: 'contPag2' + this.id,
                            'class': 'pagination_center'
                        }), this.paginarDiv,'first');
                        var divPages = domConstruct.create('div', ({
                            'class': 'divPages'
                            }), contPage2,'first');
                        domConstruct.create('input', {
                            type: "number",
                            'data-cy': this.id + 'InputPages',
                            'class': 'inputPages',
                            min: 0,
                            fractional: false,
                            max: 2000000000,
                            step: "15",
                            required: true,
                            id: "paginationSize" + this.id,
                            title: i18n.nopags,
                            value: this.getPageSize(),
                            onkeypress: lang.hitch(this, keyPress),
                            onchange: lang.hitch(this, isEnter)
                        }, divPages);
                        domConstruct.create('label', {
                            id: "labelSize" + this.id,
                            class: "labelSize",
                            type: "label",
                            innerHTML: i18n.nopags
                        }, divPages);
                        this.paginarDivContainer.set('content', this.paginarDiv);
                        if (this.getEnablePaginationModern()) {
                            this.getPaginationInfo().innerHTML = 
                                    `<div class="pagination-modern-container pagination_right" id="${'paginationModernContainer' + this.container}"></div>`;
                            var element = dom.byId('paginationModernContainer' + this.container);
                            domConstruct.create('div', {
                                    'class': 'pagination-item',
                                    innerHTML: "5",
                                    value: 5,
                                    onclick: lang.hitch(this, setPagination)
                                }, element);
                                domConstruct.create('div', {
                                    'class': 'pagination-item',
                                    innerHTML: "10",
                                    value: 10,
                                    onclick: lang.hitch(this, setPagination)
                                }, element);
                                domConstruct.create('div', {
                                    'class': 'pagination-item focus-pagination',
                                    innerHTML: "15",
                                    value: 15,
                                    onclick: lang.hitch(this, setPagination)
                                }, element);
                                domConstruct.create('div', {
                                    'class': 'pagination-item',
                                    innerHTML: "25",
                                    value: 25,
                                    onclick: lang.hitch(this, setPagination)
                                }, element);
                                domConstruct.create('div', {
                                    'class': 'pagination-item',
                                    innerHTML: "50",
                                    value: 50,
                                    onclick: lang.hitch(this, setPagination)
                                }, element);
                                domConstruct.create('div', {
                                    'class': 'pagination-item',
                                    innerHTML: "100",
                                    value: 100,
                                    onclick: lang.hitch(this, setPagination)
                                }, element);
                                domConstruct.create('div', {
                                    'class': 'pagination-item',
                                    innerHTML: "500",
                                    value: 500,
                                    onclick: lang.hitch(this, setPagination)
                                }, element);
                        }                      
                    }
                }
                this.expandableNode = domConstruct.create('div', {
                    'id': this.id + '-container',
                    'class': 'gridExpandable fancy-scroll grid-component-container'
                }, this.table, 'before');
                dom.byId(this.getResultsInfoUp()) && domClass.add(this.getResultsInfoUp(), 'grid-component-container');
                dom.byId(this.getResultsInfo()) && domClass.add(this.getResultsInfo(), 'grid-component-container');
                dom.byId(this.getSearchContainer()) && domClass.add(this.getSearchContainer(), 'grid-component-container');
                if(!core.isNull(this.getPaginationInfo())) {
                    domClass.add(this.getPaginationInfo(), 'grid-component-container');
                    if(this.size === 0) {
                        domClass.add(this.getPaginationInfo(), 'displayNone');
                    }
                }
                domConstruct.place(this.table, this.expandableNode);
                if (this.table) {
                    //Limpia la tabla 
                    while (this.table.rows.length > 0) {
                        this.table.deleteRow(0);
                    }
                    this.fillHeader(env).then(function() {
                            // zebreado de campos de búsqueda
                        query('ul.searchFields > li', _self.getSearchContainer()).forEach(function(li, i) {
                            if (i%2 === 0) {
                                domClass.add(li, 'search-odd');
                            } else {
                                domClass.add(li, 'search-even');
                            }
                        });
                        gridComponentReady.resolve(_self);
                    });
                }
            } else {
                if (parameters == null) {
                    log("Necesita enviar el arreglo con los parámetros de configuración [id, columns y container]");
                } else {
                    if (!lang.exists("id", parameters)) {
                        log("Faltó incluir el parámetro id: que es el nombre que le asigno al GridComponent");
                    }
                    if (!lang.exists("container", parameters)) {
                        log("Faltó incluir el parámetro container: que es el id de la tabla donde se mostraran los registros");
                    }
                }
                gridComponentReady.resolve(this);
            }
            if (this.resizable === true && this.inIframe() && !this.inDialog()) {
                this.localResizable = true;
                this.initializeResizeHandler();
            }
            this.activateLocalFreezeHeader();
            gridComponentReady.promise.then(lang.hitch(this, function() {
                this.loadSavedFilter();
                if (typeof parameters.callback === 'function') {
                    parameters.callback(_self);
                }
            }));
            if (parameters.createBehaviour !== null) {
                this.createBehaviour = parameters.createBehaviour;
            }
        },
        _closeSearchFiltersPanels: function() {
            array.forEach(this.searchWidgets, function(widget) {
               if (typeof widget.closeButtonPopup === 'function')  {
                   widget.closeButtonPopup();
               }
            }); 
        },
        _renderHiddenColumnsText: function(){
            if (!this.displayDivContainer || !this.displayDiv || !this.displayButton) {
                return;
            }
            var totalChecks = query('input[type=checkbox]',this.displayDiv).length;
            var checkedChecks = query('input[type=checkbox]:not(:checked)',this.displayDiv).length;
            var totalHidden = totalChecks-checkedChecks;
            var icon = totalHidden !== 0 ? this.eyeIconNameHidden : this.eyeIconName;
            query('span',this.displayButton).forEach(function(e){
                e.innerText = totalHidden + ' ' + i18n.hiddenLabel;;
            });
            query('i',this.displayButton).forEach(function(e){
               e.innerText = icon;
            });
        },
        renderColumnDisplay: function(example_sh, escapeHtmlTitle){
            var col, _self = this;
            var ul = domConstruct.create('ul');
            //Este es un input invisible con el objetivo de realizar focus y blur para cerrar el panel de columnas
            domConstruct.create('input',{
                        type: 'text',
                        style: {
                            opacity: 0,
                            width: 0,
                            height: 0
                        },
                        readonly: '',
                        tabindex: '-1'
                    },ul);
            for (var k = 0; k < this.columns.length; k++) {
                col = this.columns[k];
                if (col.type === columnTypes.search || col.type === columnTypes.function 
                        // Ocultan columnas en perfiles
                        || (col.belongsToGroup && typeof col.belongsToGroup === 'object')) {
                    continue;
                }
                if (lang.exists("id", col)) {
                    var checked = !(col.display !== 'none' && col.type !== 0);
                    var li = domConstruct.create('li',{},ul);
                    var label = domConstruct.create('label',{},li);
                    (function(k){
                        domConstruct.create('input', {
                            'id': 'hidden_'  + _self.id + '_' + col.id,
                            type:'checkbox',
                            'class':'material-icons',
                            checked: checked,
                            onclick: function(){
                                if (!this.checked) {
                                    _self.showColumn(k, this);
                                } else {
                                    // Se filtra la columna de Editar col.type = 7 porque no se contempla en las opciones del dropdown
                                    var column = _self.columns.filter(col => col.type !== 7 && (col.display === "" || typeof col.display === 'undefined'));
                                    if (column.length > 0 && column.length <= 1) {
                                        this.checked = false;
                                        require(['bnext/angularNotice'], function(angularNotice) {
                                            angularNotice.notice(i18n.minColumns);
                                        });
                                        return;
                                    }
                                    _self.hideColumn(k, this);
                                }
                                _self._renderHiddenColumnsText();
                            }
                        }, label);
                    })(k);
                    if (escapeHtmlTitle) {
                        domConstruct.place(document.createTextNode(col.title.replaceAll(/(\<img?.*?\>)/g, " ")),label);                    
                    } else {
                        domConstruct.place(document.createTextNode(col.title),label);
                    }
                }
            }
            domConstruct.place(ul,example_sh);
            this._renderHiddenColumnsText();
        },
        closeDisplayButtonOpen: function() {
            var _self = this;
            if (!_self.displayButtonOpen || !_self.displayDivContainer) {
                return;
            }
            popup.close({
                popup: _self.displayDivContainer
            });
            _self.displayButtonOpen = false;
            domClass.add(_self.displayDivContainer.domNode, 'displayNone');
        },
        closePaginarButtonOpen: function() {
            var _self = this;
            if (!_self.paginarButtonOpen) {
                return;
            }
            popup.close({
                popup: _self.paginarDivContainer
            });
            _self.paginarButtonOpen = false;
            domClass.add(_self.paginarDivContainer.domNode, 'displayNone');
        },
        inDialog: function() {
            var base = this.expandableNode;
            var els = [];
            while (base) {
                els.unshift(base);
                if (domClass.contains(base, 'dijitDialog')) {
                    return true;
                }
                base = base.parentNode;
            }
            return false;
        },
        inIframe: function() {
            try {
                return window.self !== window.top;
            } catch (e) {
                return true;
            }
        },
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this,
                    args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) {
                        func.apply(context, args);
                    }
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) {
                    func.apply(context, args);
                }
            };
        },
        calculateMaxHeight: function() {
            var expandableSelector = $(this.expandableNode);
            var frameHeight = this.resizeHandler.baseFrm.height() * .98;
            var bodyHeight = this.resizeHandler.body.height();
            var maxHeight = frameHeight - (bodyHeight - expandableSelector.height());
            if (maxHeight < 400) {
                maxHeight = 400;
            }
            return maxHeight;
        },
        isVisible: function() {
            var expandableSelector = $(this.expandableNode);
            return expandableSelector.is(':visible');
        },
        resizeRowsArea: function() {
            var expandableSelector = $(this.expandableNode);
            if (!this.isVisible() || !this.maxHeightEnabled) {
                return;
            }
            var maxHeight = this.calculateMaxHeight();
            expandableSelector.css('max-height', maxHeight);
        },
        reflowHeaders: function() {
            if (!this.isVisible()) {
                this.removeFreezeHeader();
                return;
            }
            this.freezeHandler.floatTable.floatThead('reflow');
        },
        initializeResizeHandler: function() {
            on(window, 'resize', this.debounce(lang.hitch(this, this.resizeRowsArea), 250));
            this.resizeHandler = {};
            this.resizeHandler.html = $('html');
            this.resizeHandler.baseFrm = $('#basefrm', window.parent.document);
            this.resizeHandler.body = $('body');
        },
        activateFreezeHeader: function() {
            this.freezeHeader = true;
            this.activateLocalFreezeHeader();
            this.gridResized();
        },
        activateLocalFreezeHeader: function() {
            this.localFreezeHeader = this.noHeader === false && this.freezeHeader === true;
        },
        initializeFreezeHandler: function() {
            var expandableSelector = $(this.expandableNode);
            if (this.localFreezeHeader === false || this.freezeHandler !== null || !this.isVisible()) {
                return;
            }
            this.freezeHandler = {};
            this.freezeHandler.floatTable = $(this.table);
            this.freezeHandler.floatTable.floatThead({
                perfectScrollbar: false,
                scrollContainer: function(domElement) {
                    return domElement.closest(expandableSelector);
                },
                getSizingRow: function(table) {
                    return table.find('thead tr th:visible:not(.floatThead-col)');
                },
                autoReflow: true
            });
        },
        gridResized: function() {
            this.debounce(lang.hitch(this, this.executeResizeEvents), 1)();
        },
        executeResizeEvents: function() {
            if (this.localResizable) {
                this.resizeRowsArea();
            }
            if (this.localFreezeHeader) {
                this.initializeFreezeHandler();
                this.reflowHeaders();
            }
            if (!MediaQueries.isDesktop()) {
                loader.hideLoader();
            }
        },
        removeFreezeHeader: function() {
            if (this.localFreezeHeader && this.freezeHandler) {
                this.freezeHandler.floatTable.floatThead('destroy');
                this.freezeHandler = null;
            }
        },
        clearSearch: function() {
            array.forEach(this.searchWidgets.concat(this.dynamicSearchFields), this.resetWidgetSearch);
            this.resetOrder(true);
        },
        fillHeader: function(env) {
            var self = this;
            if (this.noHeader) {
                domClass.add(this.table, 'noHeader');
            }
            // Eliminar el header anterior
            if (this.table.tHead && this.table.tHead.children && this.table.tHead.children.length === 1) {
                this.table.tHead.removeChild(this.table.tHead.children[0]);
            }
            var
                def = new Deferred(),
                _self = this,
                x = this.table.createTHead(),
                newRow = x.insertRow(0),
                indice = 0,
                cell, dynColumns = [];
            domClass.add(newRow, 'header-row');

            function renderHeader(newRow, col, indice) {
                if (col.type === columnTypes.search) {
                    return null;
                }
                var display = '',
                    title = '',
                    titleHelp = '',
                    skipExportExcel = 'false';
                if (col.belongsToGroup && typeof col.belongsToGroup === 'object') {
                    title = col.belongsToGroup.label;
                    titleHelp = title;
                    if (+col.belongsToGroup.index !== 1) {
                        display = 'none';
                        skipExportExcel = 'true';
                    }
                } else {
                    title = col.title;
                    titleHelp = col.titleHelp || title;
                }
                var cell = domConstruct.create('th', {
                    id: _self.id + "_" + col.id + "_header",
                    innerHTML: '<span class="header-title">' + title + '<span>',
                    'title': titleHelp,
                    'data-label': title,
                    'data-cell-index': indice,
                    'class': 'css_' + col.id.replace(/\./g, "_") + ' csstype_' + col.type + ' ' + _self.id + "_header",
                    style: {
                        display: display
                    },
                    'data-skipExportExcel': skipExportExcel
                }, newRow);
                if (lang.exists('attributes', col) && typeof col.attributes === 'object') {
                    if (lang.exists('style', col.attributes)) {
                        domStyle.set(cell, col.attributes.style);
                    }
                }
                var isSortable = true;
                if (lang.exists("isSortable", _self.columns[indice])) {
                    isSortable = col.isSortable;
                }
                if (!col.isTransient && isSortable) {
                    domClass.add(cell, 'sorting');
                    on(cell, "click", lang.hitch(_self, function(evt) {
                        this.setOrder(domAttr.get(evt.currentTarget, 'data-cell-index'));
                    }));
                }
                return cell;
            }
            var searchFieldProcesses = [];
            for (var j = this.columns.length; j > 0; j--) {
                var col = this.columns[indice];
                if (lang.exists("id", col)) {
                    if (col.isDynField) {
                        console.warn('¡Columnas dinamicas en grid "' + this.id + '", datos "Collection" no funcionan!! "includeProperties" de struts.xml "result\.parseFromDynamicResults" debe estar agregado.');
                        this.dynamicSearchEnabled = true;
                        dynColumns.push(col);
                    }
                    cell = renderHeader(newRow, col, indice);
                    searchFieldProcesses.push(this.searchObjectHandle(col));
                    var selectableHeader = false;
                    if (lang.exists("selectableHeader", this.columns[indice])) {
                        selectableHeader = col.selectableHeader;
                    }
                    if (cell) {
                        if (selectableHeader) {
                            cell.innerHTML = '';
                            var checkbox = domConstruct.create('input', {
                                type: 'checkbox',
                                'class':'material-icons',
                                title: i18n.selectAll,
                                readOnly: 'readOnly'
                            }, cell, 'last');
                            if (lang.exists('attributes', col) && typeof col.attributes === 'object') {
                                if (lang.exists('style', col.attributes)) {
                                    domStyle.set(checkbox, col.attributes.style);
                                }
                            };
                            attachEvents(checkbox, this.selectAllRows, [this, checkbox, indice]);
                        }
                        if (col.display) {
                            cell.style.display = typeof col.display === 'function' ? '' : col.display;
                        }
                        if (col.type !== columnTypes.function && col.type !== columnTypes.functionImage && cell.style.display !== 'none') {
                            cell.classList.add('available-to-print');
                        } else {
                            cell.classList.remove('available-to-print');
                        }
                        indice++;
                    } else if (col.type === columnTypes.search) {
                        indice++;
                    }
                }
            }
            //Crea el pie de pagina
            x = domConstruct.create('tbody', ({}), this.table, 'last');
            if (this.dynamicSearchColumnsEnabled && dynColumns.length) {
                var dynPromises = [],
                    _self = this;;
                for (var fi in dynColumns) {
                    if (!dynColumns.hasOwnProperty(fi)) {
                        continue;
                    }
                    var col = dynColumns[fi];
                    // obtiene todas las columnas de campos dinamicos para crear campos de búsqueda
                    dynPromises.push(
                        callMethod({
                            url: col.searchObj.serviceName,
                            method: 'getDynamicEntitySearchColumns',
                            params: []
                        })
                    );
                }
                all(dynPromises).then(function(results) {
                    // construye cada campo de búsqueda
                    try {
                        var added = {};
                        for (var k in results) {
                            if (!results.hasOwnProperty(k)) {
                                continue;
                            }
                            var result = results[k];
                            if (!result.valid) {
                                core.error('No se pudieron dibujar columnas dinamicas.');
                                continue;
                            }
                            for (var di in result.dynamicFields) {
                                if (!result.dynamicFields.hasOwnProperty(di)) {
                                    continue;
                                }
                                var dynField = result.dynamicFields[di];
                                if (added[dynField.name]) {
                                    //no action
                                } else {
                                    added[dynField.name] = 1;
                                    gcUtil.column(_self.columns).push(dynField.name, dynField.label, cType.Text());
                                    var currentColumn = _self.columns[_self.columns.length - 1];
                                    currentColumn.isDynFieldChild = true;
                                    renderHeader(newRow, currentColumn, indice);
                                    indice++;
                                }
                            }
                        }
                        resolveHeader();
                    } catch (e) {
                        console.error(e);
                        def.reject();
                    }
                });
            } else {
                resolveHeader();
            }
            function resolveHeader() {
                newRow = x.insertRow(0);
                domClass.add(newRow, 'empty-row');
                if (searchFieldProcesses.length) {
                    self.loadSavedFilter(env);
                    all(searchFieldProcesses).then(function(results) {
                        def.resolve(newRow);
                    });
                } else {
                    def.resolve(newRow);
                }
            }
            return def.promise;
        },
        getDynamicSearchFieldsScope: function() {
            return this.dijitScope + '-' + this.id;
        },
        searchObjectHandle: function(col) {
            var def = new Deferred();
            if (lang.exists("searchObj", col) && col.searchObj && col.searchObj.type) {
                var ul = 0;
                if (this.getSearchContainer() && this.getSearchContainer().id === 'col1') {
                    ul = dom.byId('col1_' + col.searchObj.col);
                } else if (this.getSearchContainer()) {
                    ul = dom.byId(this.id + '_col1_' + col.searchObj.col);
                }
                if (ul) {
                    var containerSearch = ul;
                    domAttr.set(ul, 'data-dijit-scope', this.getDynamicSearchFieldsScope());
                    if (col.belongsToGroup && typeof col.belongsToGroup === 'object') {
                        containerSearch = dom.byId('ctn_search_group_label_' + col.belongsToGroup.group);
                        if (!containerSearch) {
                            var x, z;
                            x = domConstruct.create('li', {
                                'class': 'left searchGroup'
                            }, ul);
                            z = domConstruct.create('label', {
                                'class': 'envelope searchLabel',
                                id: 'search_group_label_' + col.belongsToGroup.group,
                                style: {
                                    'text-align': 'left'
                                },
                                onclick: function() {
                                    if (dom.byId('hideShow_' + this.id).src.indexOf('ico_busquedaEsconder') == -1) {
                                        dom.byId('hideShow_' + this.id).src = '../scripts/framework/bnext/images/ico_busquedaEsconder.gif';
                                        dom.byId('ctn_' + this.id).style.display = '';
                                    } else {
                                        dom.byId('hideShow_' + this.id).src = '../scripts/framework/bnext/images/ico_busqueda.gif';
                                        dom.byId('ctn_' + this.id).style.display = 'none';
                                    }
                                },
                                innerHTML: col.belongsToGroup.label
                            }, x);
                            domConstruct.create('img', {
                                src: '../scripts/framework/bnext/images/ico_busqueda.gif',
                                id: 'hideShow_search_group_label_' + col.belongsToGroup.group,
                                border: 0
                            }, z, 'first');
                            containerSearch = domConstruct.create('ul', {
                                'class': 'left searchFields',
                                id: 'ctn_search_group_label_' + col.belongsToGroup.group,
                                style: { 'float': 'left', display: 'none' }
                            }, x);
                            z.style.textAlign = 'left';
                        }
                    }
                    containerSearch = containerSearch || ul;
                    var _self = this;
                    var searchLabel = this.createSearchFieldLabel(col, containerSearch);
                    this.createSearchField(col, ul, searchLabel.li)
                        .then(
                            function(widget) {
                                if (lang.isArray(widget)) {
                                    var hasMultiple = widget.length > 1;
                                    array.forEach(widget, function(item, index) {
                                        if (hasMultiple ) {
                                            if (index > 0) {
                                                var newLabel = _self.createSearchFieldLabel(col, containerSearch, item['data-customTittle']); 
                                                var divLiContainer = _self.createSearchFieldContainer(newLabel.li);
                                                item.placeAt(divLiContainer);
                                                _self.widgetSearchHandle.call(_self, item, newLabel.label, col.searchObj.type);
                                            } else {
                                                searchLabel.label.innerHTML = item['data-customTittle'] || searchLabel.label.innerText;  
                                                _self.widgetSearchHandle.call(_self, item, searchLabel.label, col.searchObj.type);
                                            }
                                        } else {
                                            _self.widgetSearchHandle.call(_self, item, searchLabel.label, col.searchObj.type);
                                        }
                                    });
                                } else {
                                    _self.widgetSearchHandle.call(_self, widget, searchLabel.label, col.searchObj.type);
                                }
                                if (col.searchObj['default'] && widget && widget.set && typeof widget.set === 'function') {
                                    widget.set('value', col.searchObj['default']);
                                }
                                def.resolve();
                            });
                } else {
                    def.resolve();
                    //log('El campo '+col.title+' no fue agregado como columna de busqueda.')
                }
            } else {
                def.resolve();
            }
            return def.promise;
        },
        updateFilterChips: function(widget, value, label, type) {
            var _self = this;
            var id = widget.id;
            
            if (!this.filterChips[id]) {
                this.filterChips[id] = {busy: false};
            }
            var config = this.filterChips[id];
            if (config.busy) {
                return;
            }
            config.busy = true;
            var description = widget.get('description') || widget.get('displayedValue');
            switch (type) {
                case 'texto':
                case 'fecha':
                    var chip = registry.byId('chip_' + widget.id);
                    if (chip) {
                        if (chip.value) {
                            config[chip.value] = null;
                        }
                        chip.set('description', label.textContent + ': ' + description);
                        chip.set('value', value);
                    } else {
                        chip = _self.createFilterChip.call(_self, value, type, description, label, config, widget);
                    }
                    config[value] = chip;
                    break;
                default:
                    array.forEach(value, function(itemValue, index) {
                        if (config[itemValue]) {
                            return;
                        } else {
                            var chip = _self.createFilterChip.call(_self, itemValue, type, description[index], label, config, widget, indexGenerator++);
                            config[itemValue] = chip;
                        }
                    });
            }
            _self.clearFilterChips(config, value, type);
            config.busy = false;
        },
        createFilterChip: function(value, type, description, label, config, widget, index) {
            var id = 'chip_' + widget.id;
            if (typeof index !== 'undefined' && index !== null) {
                id = 'chip_' + widget.id + '_' + index;
            }
            var chip = new Chip({
                value: value,
                description: label.textContent + ': ' + description,
                type: type,
                id: id,
                onRemove: lang.hitch(this, function() {
                    chip.destroyRecursive();
                    delete config[value];
                    var newValue = widget.get('value');
                    switch (chip.type) {
                        case 'texto':
                        case 'fecha':
                            newValue = null;
                            widget.set('displayedValue', newValue);
                            break;
                        default:
                            var index = newValue.indexOf(value);
                            if (index > -1) {
                                newValue.splice(index, 1);
                            }
                    }
                    if (newValue && newValue.length === 0) {
                        this.resetWidgetSearch(widget);
                    } else {
                        widget.set('value', newValue);
                    }
                    this.refreshData();
                })
            });
            chip.placeAt(this.filterChipsContainer);
            return chip;
        },
        resetWidgetSearch: function(widget) {
            if (!widget) {
                return;
            }
            widget.reset();
            if (typeof widget._updateSelection === "function") {
                widget._updateSelection();
            } else {
                widget.set('displayedValue', null);
            }
            widget.set('value', null);
        },
        formatKeyByValue: function(key, valueEmpty, valueType) {
            if (valueEmpty || core.isNull(key)) {
                return key;
            } else if (valueType === 'string' && typeof key === 'number') {
                return key + '';
            } else if (valueType === 'number' && typeof key === 'string') {
                return +key;
            }
            return key;
        },
        clearFilterChips: function(config, value, type) {
            var valueEmpty = core.isNull(value) || value.length === 0;
            var valueType;
            switch (type) {
                case 'texto':
                case 'fecha':
                    valueType = valueEmpty ? 'string' : typeof value;
                    break;
                default:
                    valueType = valueEmpty ? 'string' : typeof value[0];
            }
            if (!core.isObjectEmpty(config)) {
                for (var key in config) {
                    if(!config.hasOwnProperty(key) || key === 'busy') {
                        continue
                    }
                    key = this.formatKeyByValue(key, valueEmpty, valueType);
                    var chip = config[key];
                    var deleteChip;
                    switch (type) {
                        case 'texto':
                            deleteChip = valueEmpty || value !== key;
                            break;
                        case 'fecha':
                            deleteChip = valueEmpty || value.toString() !== key;
                            break;
                        default:
                            deleteChip = value.indexOf(key) === -1;
                    }
                    if (chip && deleteChip) {
                        chip.destroyRecursive();
                        delete config[key];
                    }
                }
            }
        },
        evaluateSearchValue: function (widget, label, value, type, updateSearch = true) {
            if (this.supportedChipsTypes.indexOf(type) !== -1) {
                var addSearchClass = false;
                switch (type) {
                    case 'fecha':
                        if (!isNull(value) && !isNaN(value)) {
                            addSearchClass = true;
                        }
                        break;
                    default:
                        if (value && value.length) {
                            addSearchClass = true;
                        }
                }
                if (addSearchClass) {
                    domClass.add(label, 'search-input-with-value');
                } else {
                    domClass.remove(label, 'search-input-with-value');                                                
                }
                if (updateSearch && dom.byId(this.filterChipsContainer.id)) {
                    this.updateFilterChips(widget, value, label, type);
                }
            } 
            switch (type) {
                case 'integer':
                    if (core.isNumeric(value)) {
                        addSearchClass = true;
                    }
                    break;
                default:
                    if (!core.isNull(value) && value !== '') {
                        addSearchClass = true;
                    }
            }
            if (addSearchClass) {
                domClass.add(label, 'search-input-with-value');
            } else {
                domClass.remove(label, 'search-input-with-value');                                                
            }
        },
        focusSearchWidget: function (label) {
            domClass.add(label, 'search-input-focused');
        },
        getVisibleSearchValue: function(widget, value){
            if (value === '' && widget.displayedValue !== '') {
                value = widget.displayedValue;
            }
            return value;
        },
        blurSearchWidget: function (widget, label, type, updateSearch) {
            var valueWidget = this.getVisibleSearchValue(widget, widget.value);
            this.evaluateSearchValue(widget, label, valueWidget, type, updateSearch);
            domClass.remove(label, 'search-input-focused');
            if (valueWidget === null || valueWidget === "" ||  (type === 'fecha' && !this.isValidDate(valueWidget))) {
                domClass.remove(label, 'search-input-with-value');
            }
        },
        isValidDate: function (value) {
          return value instanceof Date && !isNaN(value)  
        },
        widgetSearchHandle: function(widget, label, type) {
            var _self = this;
            if (widget) {
                on(widget, 'keypress', lang.hitch(this, function(e) {
                    if (e.keyCode === keys.ENTER) {
                        e.preventDefault();
                        if (_self.onClickSearch && typeof _self.onClickSearch === 'function') {
                            _self.onClickSearch();
                        }
                        var valueWidget = _self.getVisibleSearchValue(widget, widget._lastInputEventValue || widget.value);
                        if (e.target.value.trim() !== '') {
                            _self.evaluateSearchValue(widget, label, valueWidget, type);
                            _self.newSearch();
                            _self.getResultsInfoUp().style.display = 'none';
                            window['hideMsgs'] && hideMsgs();
                            if (_self.expandableNode) {
                                domClass.remove(_self.expandableNode, 'displayNone');
                            }
                            if (!MediaQueries.isDesktop()) {
                                _self.toggleSearchFieldsAction();
                            }
                        }
                    }
                }));
                if (typeof widget.on === 'function') {
                    widget.on('change', lang.hitch(this, function(value) {
                        var valueWidget = this.getVisibleSearchValue(widget, value);
                        _self.evaluateSearchValue(widget, label, valueWidget, type);
                    }));
                    widget.on('focus', lang.hitch(this, function(e) {
                        _self.focusSearchWidget(label);
                    }));
                    widget.on('blur', lang.hitch(this, function(e) {
                        _self.blurSearchWidget(widget, label, type, type !== 'fecha');
                    }));
                    if (type === 'fecha') {
                        widget.onClose = function () {
                            _self.blurSearchWidget(widget, label, type, false);
                        };
                    }
                } else {
                    on(widget, 'input', lang.hitch(this, function(value) {
                        _self.evaluateSearchValue(widget, label, value, type);
                    })); 
                    on(widget, 'focus', lang.hitch(this, function(e) {
                        _self.focusSearchWidget(widget, label, type);
                    }));
                    on(widget, 'blur', lang.hitch(this, function(e) {
                        _self.blurSearchWidget(widget, label, type, type !== 'fecha');
                    }));
                    if (type === 'fecha') {
                        widget.onClose = function () {
                            _self.blurSearchWidget(widget, label, type, false);
                        };
                    }
                }
                _self.searchWidgets.push(widget);
            }
        },
        getSearchFieldTittle: function(col){
            return col.searchObj.title || col.title;
        },
        getSearhTimeFieldTitle: function(col) {
          return col.titleHour || col.searchObj.titleHour || col.title;
        },
        createSearchFieldLabel: function(col, containerSearch, customLabel){
            var li = domConstruct.create("li", {
                'class': 'left pv5 searchField '
            }, containerSearch);
            var label = domConstruct.create("label", {
                'for': col.id,
                "class": "envelope searchLabel label"
            }, li);
            label.appendChild(document.createTextNode(customLabel || this.getSearchFieldTittle(col)));  
            return {
                li: li,
                label: label
            };
        },
        createSearchFieldContainer: function(li){
            var divLiContainer = domConstruct.create("div", {
                'class': 'pv5 dijitInline'
            }, li);
            return divLiContainer;
        },
        getStandardLabel: function(sourceLabel, replaceLabel){
            var tempLabel = sourceLabel.replace("{label}", replaceLabel);
            return tempLabel.charAt(0).toUpperCase() + tempLabel.slice(1).toLowerCase();
        },
        createSearchField: function(col, ul, li) {
            var defSt = new Deferred();
            var
                divLiContainer = this.createSearchFieldContainer(li),
                elemento,
                type = col.searchObj.type,
                widjetId = this.id + '_' + (col.id),
                widget = [],
                _self = this;
            switch (type) {
                case 'comboMultiple':
                case 'combo':
                    elemento = domConstruct.create('select', {
                        'id': widjetId,
                        "style": {
                            'width': '180px'
                        }
                    }, divLiContainer);
                    
                    var lista = col.searchObj.list || col.list,
                        idValue = col.searchObj.idValue || 'value',
                        searchAttr = col.searchObj.searchName || 'name';
                    var listaM = new Memory({
                        data: lista,
                        idProperty: idValue
                    });
                    var sortedList = new Memory({
                        data: listaM.query({}, {
                            sort: [{
                                attribute: searchAttr,
                                descending: false
                            }]
                        }),
                        idProperty: idValue
                    });
                    if (lista.length > 0 && lang.exists('searchName', lista[0])) {
                        searchAttr = 'searchName';
                    }

                    var select = new FilteringSelect({
                        store: sortedList,
                        searchAttr: searchAttr || 'description',
                        required: false
                    }, widjetId);
                    select.on('focus', function() {
                        select.loadAndOpenDropDown();
                    });
                    widget.push(select);
                    break;
                case 'owner':
                case 'text-in':
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'id': widjetId,
                        'style': {
                            'width': '180px'
                        }
                    }, divLiContainer);
                    domClass.add(elemento, "dijit dijitReset dijitInlineTable dijitLeft dijitTextBox dijitValidationTextBox");
                    var f = new ValidationTextBox(null, widjetId);
                    if (col.searchObj.defaultValue !== null) {
                        f.set('value', col.searchObj.defaultValue);
                    }
                    widget.push(f);
                    domAttr.set(dom.byId(widjetId), 'data-key', col.searchObj.key);
                    break;
                case 'texto':
                case 'object':
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'id': widjetId,
                        'style': {
                            'width': '180px'
                        }
                    }, divLiContainer);
                    domClass.add(elemento, "dijit dijitReset dijitInlineTable dijitLeft dijitTextBox dijitValidationTextBox");
                    widget.push(
                        new ValidationTextBox(null, widjetId)
                    );
                    domAttr.set(dom.byId(widjetId), 'data-cy', widjetId);
                    break;
                case 'fecha':
                    domStyle.set(divLiContainer, "width", "250px");
                    domStyle.set(divLiContainer, "height", "56px");
                    domClass.remove(divLiContainer, 'pv5');
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'class': 'fecha_lower',
                        'id': widjetId + "_lower",
                        'style': {
                            'width': '107px'
                        }
                    }, divLiContainer);
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'class': 'fecha_upper',
                        'id': widjetId + "_upper",
                        'style': {
                            'width': '107px'
                        }
                    }, divLiContainer);
                    var searchTittle = this.getSearchFieldTittle(col);
                    var fechaLowerLabel = this.getStandardLabel(i18n.searchFieldFrom, searchTittle);
                    widget.push(
                        new BnextDateTextBox({
                            'class': 'fecha_lower dateFilter',
                            onChange: function(value) {
                                var id = this.get("id");
                                id = id.substring(0, id.indexOf("_lower"));
                                id = id + "_upper";
                                var upper = registry.byId(id);
                                if (upper && !upper.get("value")) {
                                    upper.set("value", value);
                                }
                            },
                            onClick: function () {
                                this.openDropDown();
                            },
                            constraints: { datePattern: this.dateFormat },
                            'data-customTittle': fechaLowerLabel,
                            'readonly': true
                        }, widjetId + "_lower")
                    );
                    var fechaUpperLabel = this.getStandardLabel(i18n.searchFieldTo, searchTittle);
                    widget.push(
                        new BnextDateTextBox({
                            'class': 'fecha_upper dateFilter',
                            constraints: { datePattern: this.dateFormat },
                            'data-customTittle': fechaUpperLabel,
                            'readonly': true,
                            onClick: function () {
                                this.openDropDown();
                            }
                        }, widjetId + "_upper")
                    );
                    break;
                case 'percentaje':
                    domStyle.set(divLiContainer, "width", "214px");
                    domClass.remove(divLiContainer, 'pv5');
                    widget.push(
                        new NumberSpinner({
                            id: widjetId,
                            smallDelta: 10,
                            constraints: { min: 0, max: 100, places: 0 }
                        }, divLiContainer)
                    );
                    break;
                case 'long':
                case 'integer':
                    domStyle.set(divLiContainer, "width", "214px");
                    domClass.remove(divLiContainer, 'pv5');
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'id': widjetId,
                        'style': {
                            'width': '180px'
                        }
                    }, divLiContainer);
                    domClass.add(elemento, "dijit dijitReset dijitInlineTable dijitLeft");
                    widget.push(
                        new NumberSpinner({
                            id: widjetId,
                            smallDelta: 1,
                            constraints: {
                                min: -2147483648,
                                max: 2147483647,
                                places: 0,
                                fractional: false,
                                pattern:'#'
                            },
                            onFocus: function () {
                                if (isNaN(this.get("value"))) {
                                    this.set("value", 0);
                                }
                            }
                        }, divLiContainer)
                    );
                    domAttr.set(dom.byId(widjetId), 'data-cy', widjetId);
                    break;
                case 'double':
                    domStyle.set(divLiContainer, "width", "214px");
                    domClass.remove(divLiContainer, 'pv5');
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'id': widjetId,
                        'style': {
                            'width': '180px'
                        }
                    }, divLiContainer);
                    domClass.add(elemento, "dijit dijitReset dijitInlineTable dijitLeft");
                    var min = col.searchObj.rangeMax || -2147483647;
                    var max = col.searchObj.rangeMin || 2147483648;
                    widget.push(
                            new NumberSpinner({
                            id: widjetId,
                            smallDelta: 10,
                            constraints: {
                                min: min,
                                max: max,
                                locale: 'en',
                                type: 'decimal'
                            },
                            onFocus: function () {
                                if (isNaN(this.get("value"))) {
                                    this.set("value", 0);
                                }
                            }
                        }, divLiContainer)
                    );
                    break;
                case 'timestamp':
                    domStyle.set(divLiContainer, "width", "250px");
                    domStyle.set(divLiContainer, "height", "56px");
                    domClass.remove(divLiContainer, 'pv5');
                    if (!!!col.onlyByTime) {
                        elemento = domConstruct.create('input', {
                            'type': 'text',
                            'class': 'timestamp_fecha_lower',
                            'id': widjetId + "_lower",
                            'style': {
                                'width': '100px'
                            }
                        }, divLiContainer);
                    }
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'class': 'timestamp_time_lower',
                        'id': widjetId + "_time_lower",
                        'style': {
                            'width': '100px'
                        }
                    }, divLiContainer);
                    if (!!!col.onlyByTime) {
                        elemento = domConstruct.create('input', {
                            'type': 'text',
                            'class': 'timestamp_fecha_upper',
                            'id': widjetId + "_upper",
                            'style': {
                                'width': '100px'
                            }
                        }, divLiContainer);
                    }
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'class': 'timestamp_time_upper',
                        'id': widjetId + "_time_upper",
                        'style': {
                            'width': '100px'
                        }
                    }, divLiContainer);
                    var searchTittle = this.getSearchFieldTittle(col);
                    var searchTitleTime = this.getSearhTimeFieldTitle(col);
                        if (!!!col.onlyByTime) {
                            var timestampFechaLowerLabel = this.getStandardLabel(i18n.searchFieldDateFrom, searchTittle);
                            widget.push(
                                new BnextDateTextBox({
                                    'class': 'timestamp_fecha_lower',
                                    onChange: function (value) {
                                        var id = this.get("id");
                                        id = id.substring(0, id.indexOf("_lower"));
                                        id = id + "_upper";
                                        var upper = registry.byId(id);
                                        if (upper != null && upper.get("value") == null) {
                                            upper.set("value", value);
                                        }
                                    },
                                    'data-customTittle': timestampFechaLowerLabel,
                                    constraints: {datePattern: this.dateFormat}
                                }, widjetId + "_lower")
                            );
                        }
                    var timestampTimeLowerLabel = this.getStandardLabel(i18n.searchFieldTimeFrom, col.timeLabelField ? col.timeLabelField : searchTitleTime);
                    widget.push(
                        new TimeTextBox({
                            'class': 'timestamp_time_lower',
                            onChange: function(value) {
                                var id = this.get("id");
                                id = id.substring(0, id.indexOf("_lower"));
                                id = id + "_upper";
                                var upper = registry.byId(id);
                                if (upper != null && upper.get("value") == null) {
                                    upper.set("value", value);
                                }
                            },
                            'data-customTittle': timestampTimeLowerLabel
                        }, widjetId + "_time_lower")
                    );
                    if (!!!col.onlyByTime) {
                        var timestampFechaUpperLabel = this.getStandardLabel(i18n.searchFieldDateTo, searchTittle);
                        widget.push(
                            new BnextDateTextBox({
                                'class': 'timestamp_fecha_upper',
                                constraints: {datePattern: this.dateFormat},
                                'data-customTittle': timestampFechaUpperLabel
                            }, widjetId + "_upper")
                        );
                    }
                    var timestampTimeUpperLabel = this.getStandardLabel(i18n.searchFieldTimeTo, col.timeLabelField ? col.timeLabelField : searchTitleTime);
                    widget.push(
                        new TimeTextBox({
                            'class': 'timestamp_time_upper',
                            'data-customTittle': timestampTimeUpperLabel
                        }, widjetId + "_time_upper")
                    );
                    break;
                case 'time':
                    domStyle.set(divLiContainer, "width", "214px");
                    domClass.remove(divLiContainer, 'pv5');
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'class': 'time_lower',
                        'id': widjetId + "_time_lower",
                        'style': {
                            'width': '107px',
                            'background-color': '#FFF'
                        }
                    }, divLiContainer);
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'class': 'time_upper',
                        'id': widjetId + "_time_upper",
                        'style': {
                            'width': '107px',
                            'background-color': '#FFF'
                        }
                    }, divLiContainer);
                    widget.push(
                        new TimeTextBox({
                            'class': 'time_lower',
                            onChange: function(value) {
                                var id = this.get("id");
                                id = id.substring(0, id.indexOf("_lower"));
                                id = id + "_upper";
                                var upper = registry.byId(id);
                                if (upper != null && upper.get("value") == null) {
                                    upper.set("value", value);
                                }
                            },
                            style: {
                                "width": "95px",
                                'background-color': '#FFF'
                            }
                        }, widjetId + "_time_lower")
                    );
                    widget.push(
                        new TimeTextBox({
                            'class': 'time_upper',
                            style: {
                                "width": "95px",
                                'background-color': '#FFF'
                            }
                        }, widjetId + "_time_upper")
                    );
                    break;
                case 'multiple':
                    elemento = domConstruct.create('select', {
                        'id': widjetId,
                        'multiple': true,
                        'size': 5,
                        'class': _self.dijitScope,
                        "style": {
                            'width': '180px'
                        }
                    }, divLiContainer);

                    var lista = col.searchObj.list || col.list,
                        idValue = col.searchObj.idValue || 'value',
                        searchAttr = col.searchObj.searchName || 'text';
                    var listaM = new Memory({
                        data: lista,
                        idProperty: idValue
                    });
                    var sortedList = new Memory({
                        data: listaM.query({}, {}),
                        idProperty: idValue
                    });
                    if (lista.length > 0 && lang.exists('searchName', lista[0])) {
                        searchAttr = 'searchName';
                    }
                    var w = new CheckedMultiSelect({
                        labelAttr: searchAttr,
                        searchAttr: searchAttr,
                        multiple: true,
                        sortByLabel: false, // Need this to override sort
                        store: sortedList
                    }, widjetId);
                    widget.push(w);
                    var u = new FieldListUtil();
                    u.buttonCheckBoxMultiSelect(w, li, sortedList, searchAttr);
                    break;
                case 'automultiple':
                    elemento = domConstruct.create('input', {
                        'type': 'text',
                        'id': widjetId,
                        'style': {
                            'width': '180px'
                        }
                    }, divLiContainer);
                    try {
                        new autocomplete('autocomplete_' + widjetId, widjetId, col.searchObj.daoHibernate, col.searchObj.daoData, elemento, col.searchObj.serviceValue);
                    } catch (e) {
                        log('Error!! ', e);
                    }
                    break;
            }
            if (col.searchObj.type === 'dynfield') {
                var dynfieldService = col.searchObj.serviceName;

                function getCatalog(result) {
                    var dMap = result && result.domMap ? result.domMap : {};
                    var u = new FieldListUtil();
                    u.addClassLi(dMap, 'pv5 left searchField displayNone dynfieldLi');
                    for (var dk in dMap) {
                        if (!dMap.hasOwnProperty(dk)) {
                            continue;
                        }
                        domClass.add(dMap[dk].domLiNode, 'li-type-' + dMap[dk].fieldType);
                        domClass.add(dMap[dk].widjet.domNode, 'field-type-' + dMap[dk].fieldType);
                        if (domClass.contains(dMap[dk].widjet.domNode, 'dojoxCheckedMultiSelect')) {
                            u.buttonCheckBoxMultiSelect(dMap[dk].widjet, dMap[dk].domLiNode);
                        } else {
                            on(dMap[dk].widjet, 'keypress', function(e) {
                                if (e.keyCode === keys.ENTER) {
                                    if (_self.onClickSearch && typeof _self.onClickSearch === 'function') {
                                        _self.onClickSearch();
                                    }
                                    _self.newSearch();
                                    window['hideMsgs'] && hideMsgs();
                                    if (_self.expandableNode) {
                                        domClass.remove(_self.expandableNode, 'displayNone');
                                    }
                                }
                            });
                        }
                        _self.addDynamicSearchField(dMap[dk].widjet);
                        dMap[dk].widjet.set('required', false);
                        query('label.label', dMap[dk].domLiNode).forEach(function(domLbl) {
                            _self.widgetSearchHandle.call(_self, dMap[dk].widjet, domLbl, dMap[dk].fieldType);
                            domClass.add(domLbl, 'searchLabel');
                        });
                    }
                    callMethod({
                        url: dynfieldService,
                        method: 'getDynamicEntityCatalog',
                        params: []
                    }).then(function(list) {
                        var searchAttr = 'text';
                        var listaM = new Memory({
                            data: list,
                            idProperty: 'value'
                        });
                        var sortedList = new Memory({
                            data: listaM.query({}, {
                                sort: [{
                                    attribute: searchAttr,
                                    descending: false
                                }]
                            }),
                            idProperty: 'value'
                        });
                        var tempCol = cType.SelectMultiple(sortedList.data, 'text', 'value', 'string');
                        tempCol.id = col.id;
                        tempCol.title = col.title;
                        tempCol.searchObj.handleIds = true;
                        _self.createSearchField(tempCol, ul, li).then(function(arrayWidget) {
                                var w = arrayWidget[0];
                                on(w, 'change', function(e) {
                                    var val = w.get('value');
                                    query('li.dynfieldLi').forEach(function(li) {
                                        domClass.add(li, 'displayNone');
                                        if (!domClass.contains(li, 'searchAlwaysVisible')) {
                                            domClass.remove(li, 'displayTrue');
                                        }
                                    });
                                    if (lang.isArray(val)) {
                                        handleVal('li.cssEntityId' + val.join(', li.cssEntityId'));
                                    } else {
                                        handleVal('li.cssEntityId' + val);
                                    }

                                    function handleVal(qry) {
                                        query(qry).forEach(function(li) {
                                            domClass.add(li, 'displayTrue');
                                            domClass.remove(li, 'displayNone');
                                        });
                                    }
                                });
                                defSt.resolve(widget.concat(arrayWidget));
                            }
                        );
                    }, defSt.resolve);
                }
                DynamicFieldUtil.get(dynfieldService, ul, 'all', {
                    cacheOn: true,
                    dijits: true,
                    legacy: true
                }, 'recalculate').then(getCatalog, getCatalog);
            } else if (col.searchObj.type === 'multiple-entity') {
                var entityServiceName = col.searchObj.serviceName;
                callMethod({
                    url: entityServiceName, //Documents.BusinessUnitDepartment
                    method: col.searchObj.methodName || 'getActives',
                    params: []
                }).then(function(list) {
                    var searchAttr = 'text';
                    var listaM = new Memory({
                        data: list,
                        idProperty: 'value'
                    });
                    var sortedList = new Memory({
                        data: listaM.query({}, {
                            sort: [{
                                attribute: searchAttr,
                                descending: false
                            }]
                        }),
                        idProperty: 'value'
                    });
                    var tempCol = cType.SelectMultiple(sortedList.data, 'text', 'value', 'string');
                    tempCol.id = col.id;
                    tempCol.title = col.title;
                    _self.createSearchField(tempCol, ul, li).then(function(arrayWidget) {
                        defSt.resolve(widget.concat(arrayWidget));
                    });
                }, defSt.resolve);

            } else if (col.searchObj.type === 'multiple-text') {
                var entityServiceName = col.searchObj.serviceName;
                callMethod({
                    url: entityServiceName,
                    method: col.searchObj.methodName,
                    params: []
                }).then(function(list) {
                    var objectList = [];
                    array.forEach(list, function(item) {
                        objectList.push({
                            value: item,
                            text: item
                        });
                    });
                    var sourceData = new Memory({
                        data: objectList,
                        idProperty: 'value'
                    });
                    var sortedList = new Memory({
                        data: sourceData.query({}, {
                            sort: [{
                                attribute: 'text',
                                descending: false
                            }]
                        }),
                        idProperty: 'value'
                    });
                    var tempCol = cType.SelectMultiple(sortedList.data, 'text', 'value');
                    tempCol.id = col.id;
                    tempCol.title = col.title;
                    _self.createSearchField(tempCol, ul, li).then(function(arrayWidget) {
                        defSt.resolve(widget.concat(arrayWidget));
                    });
                }, defSt.resolve);
            } else {
                defSt.resolve(widget);
            }
            return defSt.promise;
        },
        addDynamicSearchField: function(widget) {
            this.dynamicSearchFields.push(widget);
        },
        getDynamicSearchFields: function() {
            return this.dynamicSearchFields;
        },
        setDynamicSearchFields: function(dynamicSearchFields) {
            this.dynamicSearchFields = dynamicSearchFields;
        },
        destroyWidget: function(widget) {
            var _self = this;
            var chips = _self.filterChips[widget.id];
            if (chips) {
                array.forEach(chips, function(chip) {
                   chip.destroyRecursive && chip.destroyRecursive();
                });
            }
            try {
                widget.destroyRecursive && widget.destroyRecursive();
            } catch(e){
                console.error('Failed to destroy  widget ' + widget.id, e);
            }
        },  
        destroyRecursive: function() {
            try {
                this.inactivateFreezeHeader();
                if (this.getSearchContainer()) {
                    this.clearSearchWidgets();
                }
                if (this.getContainer()) {
                    this.getContainer().innerHTML = '';
                }
                if (this.getResultsInfoUp()) {
                    domConstruct.destroy(this.getResultsInfoUp());
                }
            } catch(e) {
                console.error("Can not destroy gridComponent component", e);
            }
            this.inherited(arguments);
        },
        clearSearchWidgets: function() {
            var _self = this;
            array.forEach(_self.searchWidgets, function(widget, i) {
                _self.destroyWidget.call(_self, widget);
            });
            _self.searchWidgets = [];
            array.forEach(_self.dynamicSearchFields, function(widget) {
                _self.destroyWidget.call(_self, widget);
            });
            if (_self.filterChips) {
                for (var widgetId in _self.filterChips) {
                    if (!_self.filterChips.hasOwnProperty(widgetId)) {
                        continue;
                    }
                    var configChip = _self.filterChips[widgetId];
                    for (var configChipKey in configChip) {
                        if (!configChip.hasOwnProperty(configChipKey) || !configChip[configChipKey]) {
                            continue;
                        }
                        _self.destroyWidget.call(_self, configChip[configChipKey]);
                    }
                }
            }
            if (_self.filterChipsContainer) {
                _self.destroyWidget.call(_self, _self.filterChipsContainer);
            }
            if (_self.paginarDivContainer) {
                _self.destroyWidget.call(_self, _self.paginarDivContainer);
            }
            if (_self.displayDivContainer) {
                _self.destroyWidget.call(_self, _self.displayDivContainer);
            }
            array.forEach(registry.findWidgets(this.getSearchContainer()), function(widget) {
                _self.destroyWidget.call(_self, widget);
            });
            if (_self.gridExportConfig) {
                _self.destroyWidget.call(_self, _self.gridExportConfig);
            }
            query('li', this.getSearchContainer()).forEach(domConstruct.destroy);
            _self.dynamicSearchFields = [];
            _self.filterChips = {};
        },
        setTextColumns: function(textIdArray, defaultColumns) {
            this.columns = defaultColumns || [];
            for (var key in textIdArray) {
                if (!textIdArray.hasOwnProperty(key))
                    continue;
                gcUtil.column(this.columns).push(key, textIdArray[key], cType.Text());
            }
            return this.columns;
        },
        setColumns: function(columnas) {
            this.columns = [];
            for (var idx = 0; idx < columnas.length; idx++) {
                if (!lang.exists("type", columnas[idx])) {
                    log("La columna no. :" + idx + " no tiene definido un tipo 'type'");
                } else {
                    //Las columnas de tipo función no se utilizan como criterios ni para ordenar
                    if (columnas[idx].type == 7) {
                        try {
                            //log(columnas[idx].id+" se marca como transient=true");
                        } catch (e) {}

                        // Cuando en la columna se definen ambos atributos entonces no setear la bandera
                        if (!(lang.exists("isTransient", columnas[idx]) && lang.exists("searchObj", columnas[idx]))) {
                            columnas[idx].isTransient = true;
                        }

                    }
                    if (columnas[idx].type == 9) {
                        try {
                            log(columnas[idx].id + " se marca como sortable=false");
                        } catch (e) {}
                        columnas[idx].isSortable = false;
                    }
                    //Se agrega la columna a la lista de columnas validadas
                    this.columns.push(columnas[idx]);
                }
            }
        },
        resetOrder: function(clearField) {
            /*Quita los estilos de orden de todas las columnas*/
            query("th").forEach(function(item) {
                domClass.remove(item, 'sorting_desc');
                domClass.remove(item, 'sorting_asc');
            });
            if (clearField) {
                this.field = null;
            }
        },
        setOrder: function(indice) {
            if (this.createBehaviour) {
                this.sortTable();
                return;
            }
            var column = this.columns[indice];
            var field = column.sortId || column.idValue || column.id;
            var searchObj = column.searchObj || {};
            var x;
            this.resetOrder();
            if (this.getField().orderBy === field) {
                x = dom.byId(this.id + "_" + this.columns[indice].id + "_header");
                var clase = "";
                if (+this.getDirection() === 1) {
                    this.setDirection(2);
                    clase = "sorting_desc";
                } else {
                    this.setDirection(1);
                    clase = "sorting_asc";
                }
                domClass.add(x, clase);
            } else {
                if (this.getField().orderBy) {
                    for (var k = 0; k < this.columns.length; k++) {
                        if (this.columns[k].id === this.getField().orderBy) {
                            x = dom.byId(this.id + "_" + this.columns[k].id + "_header");
                            domClass.add(x, "sorting");
                            domClass.remove(x, "sorting_asc");
                            break;
                        }
                    }
                }
                var fieldOrderType = 'HQL';
                if (column.isDynFieldChild) {
                    fieldOrderType = 'SQL';
                }
                this.setField(field, fieldOrderType, searchObj);
                if (field !== "") {
                    x = dom.byId(this.id + "_" + this.columns[indice].id + "_header");
                    domClass.add(x, "sorting_asc");
                    domClass.remove(x, "sorting");
                }
                this.setDirection(1);
            }
            this.refreshData();
        },
        sortTable: function() {
            var direccion = +this.getDirection();
            var data = this.getBean().data.sort(function (a, b) {
                if (direccion === 1) {
                    return a.description.localeCompare(b.description);
                } else {
                    return b.description.localeCompare(a.description);
                }
            });
            if (direccion === 1) {
                this.setDirection(2);
            } else {
                this.setDirection(1);
            }
            this.setData(data);
        },
        getPageSize: function() {
            return this.size;
        },
        setPageSize: function(number) {
            if(this.rowCount <= number){
                this.currentPage = 0;
            }
            this.size = number;
            this.setCurrentPage(this.currentPage || 0);
            return this.refreshData();
        },
        refreshCurrentPage: function(size) {
            if (core.isNull(size)) {
                if (this.size !== 0) {
                    size = size || (dom.byId('gridSize') ? dom.byId('gridSize').value : 15) || 15;
                    this.size = this.size || size;
                }
            } else {
                this.size = size;
            }
            this.setCurrentPage(this.currentPage || 0);
            return this.refreshData();
        },
        getCurrentPage: function() {
            return this.currentPage;
        },

        setCurrentPage: function(number) {
            this.currentPage = number;
        },
        getTotalPages: function() {
            return this.totalPages;
        },
        setOpenSearchZone: function(open) {
            this.openSearchZone = open;
        },
        getOpenSearchZone: function() {
          return this.openSearchZone;  
        },
        setTotalPages: function(number) {
            this.totalPages = number;
        },
        getDirection: function() {
            return this.direction;
        },
        setDirection: function(number) {
            this.direction = number;
        },
        getField: function() {
            if (!this.field) {
                this.field = {
                    orderBy: this.getGridLocalDefaultOrder(),
                    type: 'HQL'
                };
            }
            this.field.type = this.field.type || 'HQL';
            return this.field;
        },
        getGridLocalDefaultOrder: function() {
            return this.gridLocalDefaultOrder || (this.field || { orderBy: null }).orderBy;
        },
        setField: function(string, type, searchObj) {
            searchObj = searchObj || {};
            if (!this.field) {
                this.field = {
                    orderBy: this.getGridLocalDefaultOrder(),
                    type: 'HQL'
                };
            }
            this.field.orderBy = string;
            this.field.type = type || 'HQL';
            if (searchObj.extraFilters) {
                this.addExtraFilters(searchObj.extraFilters);
            }
        },
        setPageNumbers: function(limit, parent) {
            var ppActual = parseInt(this.getCurrentPage()),
                cantidadMaximaAMostrar = 2, //Solo se mostraran 2 paginas a la derecha e izquierda de la pagina actual.
                rangoInicial = 0,
                rangoFinal = cantidadMaximaAMostrar * 2;

            // Si la cantidad de paginas es menor al maximo permitido
            // entonces se muestran todas
            if (limit > cantidadMaximaAMostrar * 2) {
                //Si esta en la primera pagina
                if (ppActual === 0) {
                    rangoFinal = cantidadMaximaAMostrar * 2;
                } else {
                    //Si esta en la ultima pagina
                    if (ppActual === limit) {
                        rangoInicial = limit - (cantidadMaximaAMostrar * 2);
                        rangoFinal = limit;
                    } else {
                        var diferencia1 = 0;
                        var diferencia2 = 0;
                        //Calcular rango inicial
                        if ((ppActual + 1) >= cantidadMaximaAMostrar) {
                            var j = 0;
                            if (ppActual + cantidadMaximaAMostrar > limit) {
                                j = 1;
                            }
                            rangoInicial = ppActual - cantidadMaximaAMostrar - j;
                        } else {
                            diferencia1 = cantidadMaximaAMostrar - ppActual;
                        }
                        //calcular rango final
                        if ((ppActual + cantidadMaximaAMostrar) <= limit) {
                            rangoFinal = ppActual + cantidadMaximaAMostrar + (ppActual < cantidadMaximaAMostrar ? cantidadMaximaAMostrar - ppActual : 0);
                        } else {
                            diferencia2 = limit - cantidadMaximaAMostrar * 2;
                        }
                        rangoFinal += diferencia2;
                        rangoInicial += diferencia1;
                    }
                }
            } else {
                rangoFinal = limit;
            }
            if (rangoInicial < 0) {
                rangoInicial = 0;
            }
            var that = this;
            var childs = [];
            for (var i = rangoInicial; i < rangoFinal + 1; i++) {
                //Se encapsula para que el valor de i no cambie al usar el connect con that.goToPage(i);
                (function(i) {
                    var child = domConstruct.create('span', {
                        id: 'pageNumber' + that.id + '_' + i,
                        innerHTML: (i + 1),
                        className: 'paginate_button ' + (ppActual == i ? 'paginate_active' : '')
                    }, parent);
                    on(child, 'click', function() {
                        that.goToPage(i);
                    });
                    childs.push(child)
                })(i);
            }
            if (childs.length === 1 || (this.bean.count <= this.bean.data.length)) {
                domClass.add(this.getPaggingContainer(), 'displayNone');
            } else {
                domClass.remove(this.getPaggingContainer(), 'displayNone');
            }
            return childs;
        },
        goToPage: function(pageNumber) {
            if (this.searchBusy === true) {
                console.error("Page navigation called while executing a search");
                return;
            }
            this.searchBusy = true;
            this.setGridLocalPage(0);
            this.setCurrentPage(pageNumber);
            this.setOpenSearchZone(true);
            return this.refreshData();
        },
        goToLocalPage: function(localPageNumber) {
            this.setGridLocalPage(localPageNumber);
            return this.displayResults();
        },
        fillPagination: function(registros) {
            if (registros > 0 && typeof this.getPaginationInfo() !== 'undefined' && this.getPaginationInfo() !== null) {
                var pageSize = parseInt(this.getPageSize());
                var totalPaginas;
                if (pageSize > 0){
                    totalPaginas = Math.ceil(registros / pageSize);
                } else {
                    totalPaginas = 0;
                    this.setCurrentPage(0);
                }
                var nodeLeft = this.getPaginationInfo().querySelector('#pagination_navigate');
                if (nodeLeft != null) {
                    nodeLeft.remove();
                }
                var objDom = domConstruct.create('div', {
                    className: 'pagination_left',
                    id: 'pagination_navigate'
                }, this.getPaginationInfo(), 'first');
                var nums = domConstruct.create('span', {
                    id: 'divPageNumbers' + this.id
                }, objDom);
                var that = this;
                var first = domConstruct.create('span', {
                    className: 'first paginate_button',
                    id: 'example_first',
                    innerHTML: '<span class="material-icons">first_page</span>'
                }, objDom),
                last = domConstruct.create('span', {
                    className: 'last paginate_button',
                    id: 'example_last',
                    innerHTML: '<span class="material-icons">chevron_left</span>'
                }, objDom),
                pagesN = domConstruct.create('span', {
                    className: 'example_paginate',
                    id: 'example_paginate',
                    innerHTML: '<span class="example_paginate">' + (that.getCurrentPage() + 1) + ' ' + i18n.pageOf + ' ' + (totalPaginas) + '</span>'
                }, objDom),
                next = domConstruct.create('span', {
                    className: 'next paginate_button',
                    id: 'example_next',
                    innerHTML: '<span class="material-icons">chevron_right</span>'
                }, objDom),
                previous = domConstruct.create('span', {
                    className: 'previous paginate_button',
                    id: 'example_previous',
                    innerHTML: '<span class="material-icons">last_page</span>'
                }, objDom);
                if (that.getCurrentPage() === (totalPaginas - 1) || totalPaginas === 1) {
                    next.style = 'color: #c1c1c1; pointer-events: none;';
                    previous.style = 'color: #c1c1c1; pointer-events: none;';
                } else {
                    next.style = '';
                    previous.style = '';
                }
                on(first, 'click', function() {
                    that.debounce(lang.hitch(that, that.goToPage(0), 1000));
                });
                on(last, 'click', function() {
                    that.debounce(lang.hitch(that, that.goToPage(that.getCurrentPage() > 1 ? that.getCurrentPage() - 1 : 0), 1000));
                });
                on(next, 'click', function() {
                    that.debounce(lang.hitch(that, that.goToPage(that.getCurrentPage() < totalPaginas ? that.getCurrentPage() + 1 : totalPaginas)), 1000);
                });
                on(previous, 'click', function() {
                    that.debounce(lang.hitch(that, that.goToPage(totalPaginas), 1000));
                });
            } else {
                if (typeof this.getPaginationInfo() === 'undefined' || this.getPaginationInfo() === null) {
                    log("No se recibio como parámetro 'paginationInfo' que es el id del contenedor donde se despliega la paginación");
                } else {
                    if (registros === 0) {
                        this.getPaginationInfo().innerHTML = "";
                    }
                }
            }
        },
        waiting: function() {
            var tabla = this.getTable();
            if (tabla !== null) {
                this.clearTable(tabla);
                this.clearWaiting(tabla);
                if (!query('.waiting-img', tabla).length) {
                    var renglon = tabla.insertRow();
                    var celda = renglon.insertCell(0);
                    domAttr.set(celda, 'colspan', this.columns.length);
                    var pathWaiting = require.toUrl("bnext/images/waiting.gif");
                    domConstruct.create("img", {
                        src: pathWaiting.toString(),
                        border: 0,
                        'class': 'waiting-img'
                    }, celda, "last");
                    celda.style.align = "center";
                    domClass.add(renglon, 'waiting-row');
                }
            }
        },
        isNotUtilityRow: function(row) {
            return !domClass.contains(row, 'empty-row') &&
                !domClass.contains(row, 'size-row') &&
                !domClass.contains(row, 'header-row') &&
                !domClass.contains(row, 'gridFooter');
        },
        clearWaiting: function(tabla) {
            query('.waiting-row', tabla).forEach(function(row) {
                domConstruct.destroy(row);
            });
        },
        clearTable: function(tabla) {
            var rows = array.filter(tabla.rows, this.isNotUtilityRow);
            array.forEach(rows, function(row) {
                domConstruct.destroy(row);
            });
        },
        displayResults: function(deferred) {
            this.setRowCount(0);
            var def = new Deferred();
            var bean = this.getBean(),
                data = bean.data || [],
                len = +data.length,
                id = this.getContainer(),
                tabla = dom.byId(id);
            //Se resetean los checkbox existentes
            query('thead input[type=checkbox]', tabla).forEach(function(checkbox) {
                domAttr.set(checkbox, 'checked', false);
            });
            this.clearTable(tabla);
            var columnasLen = this.columns.length;
            //lleno nombres de displayedResults
            var displayedResultsNames = [];
            var displayedResultsRows = [];
            var displayedResultsTypes = [];
            this.displayedResults = [];

            for (var i = 0; i < columnasLen; i++) {
                var divTemp = domConstruct.create('div', ({
                    id: 'divTemp_id',
                    style: 'display:none'
                }), query('body')[0], 'last');
                divTemp.innerHTML = this.columns[i].title;
                var t = query('div', divTemp);

                var headerName = null;
                if (t[0]) {
                    headerName = t[0].innerHTML;
                } else {
                    headerName = this.columns[i].title;
                }
                displayedResultsNames.push({
                    'title': headerName,
                    'shown': (this.validateExcelColumn(this.columns[i]) ? 1 : 0),
                    'notExportable': (+this.columns[i].type === columnTypes['hidden'] ||
                        +this.columns[i].type === columnTypes['function'] ? 1 : 0)
                });
                displayedResultsTypes.push(this.columns[i].type);
                domConstruct.destroy(divTemp);
            }
            this.clearTable(tabla);
            this.clearWaiting(tabla);
            var gridLocalSize = len,
                gridLocalPage = this.gridLocalPage,
                gridLocalDefaultOrder = this.getGridLocalDefaultOrder(),
                _self = this;
            query('.gridLocalPages', this.getPaggingContainer()).forEach(domConstruct.destroy);
            if (this.gridLocalSize < len) {
                gridLocalSize = this.gridLocalSize;
            }
            var fun = lang.hitch(this, function() {
                var promises = [];
                for (var j = (gridLocalPage * gridLocalSize); j < ((gridLocalPage * gridLocalSize) + gridLocalSize); j++) {
                    var rowDef = new Deferred();
                    var func = deferRowPush(data, j, bean, displayedResultsRows, gridLocalSize, gridLocalPage, rowDef);
                    promises.push(rowDef);
                    func = lang.hitch(this, func);
                    setTimeout(func, 1);
                }
                if (promises.length === 0) {
                    var rowDef = new Deferred();
                    rowDef.resolve(0);
                    promises.push(rowDef);
                }
                return promises;
            });
            all(fun()).then(lang.hitch(this, function() {
                this.dataExcelResultsNames = displayedResultsNames;
                this.dataExcelResultsTypes = displayedResultsTypes;
                this.dataExcelResultsRows = displayedResultsRows;
                this.updateDataInfo(len);
                this.fillPagination(bean.count);
                if (this.gridLocalSize < len) {
                    gridLocalSize = this.gridLocalSize;
                    //this.getPaggingContainer().id
                    var textVal, pagesLen = len / gridLocalSize;
                    var contId = 'pageNumber' + this.id + '_' + (this.getCurrentPage());
                    if (!dom.byId(contId)) {
                        contId = this.getPaggingContainer().id + '_main';
                        if (gridLocalDefaultOrder) {
                            textVal =
                                (data[0][gridLocalDefaultOrder]) +
                                ' - ' +
                                data[gridLocalSize][gridLocalDefaultOrder];
                        } else {
                            if (+this.size === 0) {
                                textVal = '1.0';
                                this.setCurrentPage(0);
                            }
                        }
                        var main1 = domConstruct.create('span', {
                            'class': 'firstWrapperLocalPages'
                        }, this.getPaggingContainer().id, 'last');
                        var main2 = domConstruct.create('span', {
                            'class': 'mainWrapperLocalPages'
                        }, main1, 'last');
                        var main3 = domConstruct.create('table', {
                            'class': 'mainLocalPages'
                        }, main2, 'last');
                        var main = domConstruct.create('tr', {}, main3, 'last');
                        domConstruct.create('td', {
                            'class': 'gridLocalPages paginate_button ' + (+gridLocalPage === 0 ? 'paginate_active' : ''),
                            innerHTML: textVal,
                            'id': contId,
                            onclick: function(evt) {
                                _self.goToLocalPage(0);
                            }
                        }, main, 'last');
                    }
                    for (var k = 1; k < pagesLen; k++) {
                        var textVal = (this.getCurrentPage() + 1) + '.' + k;
                        if (gridLocalDefaultOrder) {
                            if (data[(k * gridLocalSize)] && data[(k * gridLocalSize) + gridLocalSize]) {
                                textVal =
                                    (data[(k * gridLocalSize)][gridLocalDefaultOrder]) +
                                    ' - ' +
                                    data[(k * gridLocalSize) + gridLocalSize][gridLocalDefaultOrder];
                                if ((k * gridLocalSize) - 1 === ((k * gridLocalSize) + k)) {
                                    this.searchBusy = false;
                                    def.resolve();
                                }
                            } else {
                                console.warn('It seems that pagesLen is not setted properly, pagesLen: ' + pagesLen + ', maxLen: ' + (data.length / gridLocalSize));
                                this.searchBusy = false;
                                def.resolve();
                                continue;
                            }
                        }
                        domConstruct.create('td', {
                            'class': 'gridLocalPages paginate_button ' + (+gridLocalPage === k ? 'paginate_active' : ''),
                            innerHTML: textVal,
                            'data-value': k,
                            'id': contId + '_' + k,
                            onclick: function(evt) {
                                query('.paginate_button.paginate_active', evt.target.parentNode).forEach(function(doom) {
                                    domClass.remove(doom, 'paginate_active');
                                });
                                domClass.add(evt.target, 'paginate_active');
                                _self.goToLocalPage(domAttr.get(evt.target, 'data-value'));
                            }
                        }, contId, 'after');
                        contId = contId + '_' + k;
                    }
                    this.setCurrentPage(+this.size === 0 ? 1 : this.getCurrentPage());
                } else {
                    this.searchBusy = false;
                    def.resolve();
                }
                this.setDisplayedResults({
                    actual: -1,
                    displayNames: displayedResultsNames,
                    displayTypes: displayedResultsTypes,
                    displayData: displayedResultsRows
                });
            }));
            if (typeof deferred !== "undefined" || deferred !== undefined) {
                deferred.progress();
            }
            return def.promise;
        },
        generateRowsData: function(bean) {
            var data = [];
            data = bean.data || [],
                len = +data.length;
            var displayedDataRows = [];
            for (j = 0; j < len; j++) {
                if (!data[j]) {
                    return;
                }
                var r = this.pushRowExecute(data[j], j, false);
                displayedDataRows[j] = r;
            }
            return displayedDataRows;
        },
        updateDataInfo: function(len) {
            len = len || +len === 0 ? len : this.getBean().data.length;
            if (
                this.getResultsInfo()
                && this.getResultsInfo().parentNode
                && query('.grid-bottom-margin', this.getResultsInfo().parentNode).length === 0
            ) {
                domConstruct.create('div', ({
                    'class': 'grid-bottom-margin'
                }), this.getResultsInfo(), 'after');
            }
            if (len === 0) {
                if (this.getPageSize() > 0 && this.isDataAvailable()) {
                    this.setGridLocalPage(0);
                    if (this.getCurrentPage() > 0) {
                        this.setCurrentPage(this.getCurrentPage() - 1);
                    }
                    return this.refreshData();                        
                }
                this.evaluateFreezeHeader(this.getBean());
                return this.setGridToNoData();
            } else {
                return this.setGridToHasData();
            }
        },
        getPaggingContainer: function() {
            return dom.byId('paging_full_numbers_' + this.id);
        },
        setGridToNoData: function(message) {
            domAttr.set(this.getResultsInfoUp(), "id", this.id + "_gridInfo");
            domClass.add(this.getResultsInfoUp(), 'info gridInfo');
            domStyle.set(this.getContainer(), 'display', 'none');
            domStyle.set(this.getResultsInfoUp(), 'display', '');
            this.getResultsInfoUp().innerText = message || (this.searchBusy ? this.getNoRegMessage() : this.getEmptyMessage()) || this.getNoRegMessage() || i18n.noResults;
            var example = dom.byId('example_sh_' + this.id),
                paging = dom.byId('paging_full_numbers_' + this.id);
            example && domStyle.set(example, 'display', 'none');
            paging && domStyle.set(paging, 'display', 'none');
            if (dom.byId("pageCount_" + this.id)) {
                domStyle.set('pageCount_' + this.id, 'display', 'none');
            }
            if (typeof this.onLoaded === 'function') {
                this.onLoaded.apply(this, [this]);
            }
            if (this.getGridOptions()) {
                domClass.add(this.getGridOptions(), 'displayNone');
            }
            if (this.expandableNode) {
                domClass.add(this.expandableNode, 'displayNone');
            }
            return core.resolvedPromise();
        },
        setGridToHasData: function() {
            domStyle.set(this.getContainer(), 'display', '');
            domStyle.set(this.getResultsInfoUp(), 'display', 'none');
            var example = dom.byId('example_sh_' + this.id),
                paging = dom.byId('paging_full_numbers_' + this.id);
            if (!this.noEyes && example) {
                domStyle.set(example, 'display', '');
            }
            paging && domStyle.set(paging, 'display', '');
            if (dom.byId("pageCount_" + this.id)) {
                domStyle.set('pageCount_' + this.id, 'display', '');
            }
            if (this.getGridOptions()) {
                domClass.remove(this.getGridOptions(), 'displayNone');
            }
            if (this.expandableNode) {
                domClass.remove(this.expandableNode, 'displayNone');
            }
            return core.resolvedPromise();
        },
        popRowById: function(id) {
            return this.popRowBy('id', id);
        },
        popRowBy: function(property, value) {
            var data = this.getBean().data;
            var result_array = [];
            var r = null;
            for (var j = 0; j < data.length; j++) {
                if (data[j][property] == value) {
                    r = data[j];
                    this.updateTableTrIds(j);
                } else {
                    result_array.push(data[j]);
                }
            }
            if (r === null) {
                console.error('Not found row with value "' + value + '" for property ' + property);
                return;
            }
            data = result_array;
            this.updateBeanData(data, [r]);
            return r;
        },
        /**
         * Obtiene las filas que coinciden con ciertos atributos de una fila de ejemplo
         *  --> Este metodo se debe poder implementar en otros arreglos como funcion de busqueda
         *      , para eso se debe quitar la linea 'this.updateTableTrIds(j);'
         *  --> El example viene mal parametrizado si aparece un error que dice: 
         *  
         *      TypeError: Object #<Object> has no method 'split'
         *      
         * --> El deber ser seria utilizar un dojo.store en lugar de un Array para "data"
         * 
         * <AUTHOR>
         * @ToDo: Mejorar velocidad
         * 
         * @param example : es la fila de ejemplo
         * @param pop que indica si la fila encontrada será o no extraida
         * @return regresa un Array con las filas encontradas
         **/
        getRowsByExample: function(example, pop) {
            var bean = this.getBean();
            var data = bean.data;
            var result_array = [];
            var r = [];
            if (pop) {
                for (var j = 0; j < data.length; j++) {
                    var get = exampleMatch(data[j], example);
                    if (get) {
                        r[r.length] = data[j];
                        this.updateTableTrIds(j);
                        data.splice(j, 1);
                        j--;
                    } else {
                        result_array.push(data[j]);
                    }
                }
            } else {
                for (var j = 0; j < data.length; j++) {
                    var get = exampleMatch(data[j], example);
                    if (get) {
                        r[r.length] = data[j];
                        result_array.push(data[j]);
                    } else {
                        result_array.push(data[j]);
                    }
                }
            }
            data = result_array;
            this.updateBeanData(data, r);
            return r;
        },
        /**
         * Utilizar siempre que se remueva un TR de la tabla, mantiene la consistensia de los ID's de cada uno
         * 
         * @param initialIndex : es el indice del arreglo "this.getBean().data" del cual removeremos el TR
         * @private
         **/
        updateTableTrIds: function(initialIndex) {
            var k = initialIndex,
                tr;
            if (dom.byId("tr_" + this.id + "_" + k)) {
                domConstruct.destroy(dom.byId("tr_" + this.id + "_" + k));
            }
            k++;
            while (dom.byId("tr_" + this.id + "_" + k)) {
                tr = dom.byId("tr_" + this.id + "_" + k);
                tr.id = "tr_" + this.id + "_" + (k - 1);
                domClass.toggle(tr, 'even odd');
                k++;
            }
        },
        /**
         * Solo se debe utilizar si se tiene la certeza de que los datos en 'data' coinciden con la tabla
         * @private
         **/
        updateBeanData: function(data, r) {
            this.getBean().data = data;
            this.getBean().count = this.getBean().count - r.length;
            var validCount = data.length > 0;
            if (!validCount) {
                this.setRowCount(0);
            } else {
                this.setRowCount(data.length - 1);
            }
            if (typeof this.onPopRow == 'function') {
                this.onPopRow(r);
            } else if (this.onPopRow != null) {}
        },
        //Siempre despues de llamar esta funcion hay que ejecutar el updateDataInfo()
        pushRow: function(row, jota) {
            return this.pushRowExecute(row, jota, true);
        },
        pushRowExecute: function(row, jota, updateDom) {
            var data = {};
            if (updateDom) {
                var bean = this.getBean();
                data = bean.data;
            }
            var j = data.length;
            if (updateDom) {
                if (j === 0 || +jota === 0) {
                    this.claseRow = "grid-row even";
                } else {
                    this.claseRow = (this.claseRow === 'odd' ? "grid-row even" : "grid-row odd");
                }
                if ((jota || +jota === 0) && array.indexOf(data, row) !== -1) {
                    j = jota;
                } else {
                    data.push(row);
                    this.bean.data = data;
                    this.bean.count = data.length;
                }
            }
            if (this.getParseFromDynamicResults()) {
                row = gcUtil.parseDynamicValue(row);
            }
            if (updateDom) {
                //Actualiza contadores
                if (data.length > 0) {
                    var rowCount = data.length;
                    if (this.gridLocalSize < rowCount) {
                        rowCount = this.gridLocalSize - 1;
                } else {
                        rowCount = rowCount - 1;
                    }
                    this.setRowCount(rowCount);
                } else {
                    this.setRowCount(0);
                }
            }

            var id = this.getContainer(),
                nodeTable = null,
                columnasLen = this.columns.length,
                nodeTr = null;
            if (updateDom) {
                nodeTable = dom.byId(id);
                nodeTr = nodeTable.insertRow();
                domAttr.set(nodeTr, "class", this.claseRow);
                domAttr.set(nodeTr, "id", "tr_" + this.id + "_" + j);
                domAttr.set(nodeTr, 'data-index', j);
            }
            var displayedResultsCols = [];
            var groupValues = {};
            for (var l = 0; l < columnasLen; l++) {
                var nodeDiv = null;
                var nodeTd = null,
                    spanContainer = null;
                var currentColumn = this.columns[l];
                if (currentColumn.type === columnTypes.search) {
                    continue;
                }
                var atributo = currentColumn.idValue || currentColumn.id,
                    belongsToGroup = currentColumn.belongsToGroup &&
                    typeof currentColumn.belongsToGroup === 'object';
                if (updateDom) {
                    if (belongsToGroup) {
                        nodeTd = dom.byId('td_' + this.id + '_' + currentColumn.belongsToGroup.group + '_' + j);
                        if (!nodeTd) {
                            nodeTd = domConstruct.create("td", null, nodeTr, "last"); //renglon.insertCell(l);
                            nodeTd.id = 'td_' + this.id + '_' + currentColumn.belongsToGroup.group + '_' + j;
                        }
                        spanContainer = domConstruct.create("span", {
                            id: 'td_' + this.id + '_' + currentColumn.id + '_' + j,
                            'class': 'css_grouped_' + currentColumn.id
                        }, nodeTd, "last");
                    } else {
                        nodeTd = domConstruct.create("td", null, nodeTr, "last"); //renglon.insertCell(l);
                        nodeTd.id = 'td_' + this.id + '_' + currentColumn.id + '_' + j;
                        if (this.noHeader) {
                            nodeDiv = domConstruct.create("div", {
                                'class': this.noHeader ? 'cell-wrapper' : ''
                            }, nodeTd);
                            spanContainer = domConstruct.create("span", {
                                'style': {
                                    'display': 'flex',
                                    'justify-content': 'center'
                                }
                            }, nodeDiv, "last");
                        } else {
                            spanContainer = domConstruct.create("span", {
                                'style': {
                                    'display': 'flex',
                                    'justify-content': 'center'
                                }
                            }, nodeTd, "last");
                        }
                    }
                    if (lang.exists("display", currentColumn) && typeof currentColumn.display !== 'function') {
                        nodeTd.style.display = currentColumn.display;
                    }
                    var cssColumnId = currentColumn.id.replace(/\./g, "_");
                    domClass.add(nodeTd, 'css_' + cssColumnId + ' css_' + this.id + '_' + cssColumnId + ' csstype_' + currentColumn.type + ' ' + this.id + "_cell");
                    domClass.add(nodeTd, (currentColumn.type !== columnTypes.function && currentColumn.type !== columnTypes.functionImage && nodeTd.style.display !== 'none' ? ' available-to-print' : ''));
                    domAttr.set(nodeTd, 'data-cy', cssColumnId + '_' + j + '_' + l);
                }
                var value = "";
                var isTransient = false;
                if (lang.exists("isTransient", this.columns[l])) {
                    isTransient = currentColumn.isTransient;
                }
                if (!isTransient) {
                    value = lang.getObject(atributo, false, row);
                    if (typeof value === 'object' && this.getParseFromDynamicResults()) {
                        var tempVal = '';
                        for (var ok in value) {
                            if (!value.hasOwnProperty(ok)) {
                                continue;
                            }
                            tempVal += value[ok] || '';
                            if (tempVal) {
                                break;
                            }
                        }
                        value = tempVal.replace(/,/g, ', ');
                    }
                }
                var excelValue = 'excelValue',
                    renderCell = this.columns[l].renderCell,
                    renderCellExcel = this.columns[l].renderCellExcel,
                    useOnce = false,
                    useDecode = false;
                if (updateDom && value === null && !renderCell &&
                    this.columns[l].type !== columnTypes.checkbox &&
                    this.columns[l].type !== columnTypes.selectable && !belongsToGroup) {
                    spanContainer.innerHTML = this.nullDefaultText;
                    excelValue = this.nullDefaultText;
                } else if (updateDom && renderCell) {
                    var t = renderCell.call(this, row, nodeTd, nodeTr, nodeTable, this.columns[l]);
                    if (t && typeof t === 'string') {
                        t = document.createTextNode(t);
                    } else if (t && typeof t === 'number') {
                        t = document.createTextNode(t + '');
                    } else if (t && t.tagName === 'INPUT' && (t.type === 'radio' ||  t.type === 'checkbox') ){
                        domClass.add(t, 'material-icons');
                    } else if (!t) {
                        t = document.createTextNode('-');
                    } 
                    spanContainer.appendChild(t);
                    excelValue = "-";
                    if (renderCellExcel) {
                        /**
                         * ¡renderCellExcel.call()!
                         * 
                         * Aquí aproposito no manejan los parametros [nodeTd, nodeTr, nodeTable] 
                         * debido a que realizar operaciones con nodos es muy costoso.
                         */
                        excelValue = renderCellExcel.call(this, row, this.columns[l]);
                    } else {
                        excelValue = nodeTd.textContent || nodeTd.innerText;
                    }
                } else if (renderCellExcel) {
                    var t = renderCellExcel.call(this, row, this.columns[l]);
                    excelValue = t;
                } else {
                    /** se llena objeto con resultados desplegados y se muestran en pantalla **/
                    /**/
                    var columns_type = currentColumn.type;
                    switch (columns_type) {
                        case 0: //hidden
                        case 1: //text
                            useDecode = currentColumn.useDecode;
                            if(useDecode) {
                                value = decodeURIComponent(value);
                            }
                            if (updateDom && encode(value) === "-") {
                                //Texto vacio
                                spanContainer.setAttribute('class', 'hidden');
                            }
                            var encodedValue = encode(value);
                            if (updateDom) {
                                domConstruct.create('div', { innerHTML: encodedValue }, spanContainer, 'only');
                            }
                            excelValue = value;
                            break;
                        case 2: //select
                            if (lang.exists("list", currentColumn) && (currentColumn.list.length > 0)) {
                                for (var m = 0, lcl = currentColumn.list.length; m < lcl; m++) {
                                    var listM = currentColumn.list[m];
                                    if (!core.isNull(value) && (listM.value === value || listM.id == value)) {
                                        if (updateDom) {
                                            spanContainer.innerHTML = listM[currentColumn.key];
                                        }
                                        excelValue = listM[currentColumn.key];
                                        break;
                                    }
                                }
                            }
                            break;
                        case 3: //checkbox
                            if (value) {
                                if (updateDom) {
                                    domConstruct.create("input", {
                                        type: "checkbox",
                                        checked: "checked",
                                        readonly: "readonly"
                                    }, spanContainer, "last");
                                }
                                excelValue = '1';
                            } else {
                                if (updateDom) {
                                    domConstruct.create("input", {
                                        type: "checkbox",
                                        readOnly: "readOnly"
                                    }, spanContainer, "last");
                                }
                                excelValue = '0';
                            }
                            break;
                        case 4: //time
                            value = this.tools.formatFastTimeFromJson(value);
                            if (updateDom) {
                                spanContainer.innerHTML = value;
                            }
                            excelValue = value;
                            break;
                        case 5: //object
                            if (lang.exists("value", currentColumn)) {
                                if (updateDom) {
                                    spanContainer.innerHTML = encode(lang.getObject(currentColumn.value, false, value));
                                }
                                excelValue = lang.getObject(currentColumn.value, false, value);
                            } else {
                                if (updateDom) {
                                    log("No fue definido el parametro 'value' donde se define el atributo del objeto a mostrar");
                                    spanContainer.innerHTML = "-";
                                }
                                excelValue = "-";
                            }
                            break;
                        case 6: //date
                            if (excelValue === 'excelValue' || !excelValue) {
                                excelValue = this.tools.formatFastExcelDateFromJson(value);
                            }
                            if (updateDom) {
                                if (value) {
                                    value = this.tools.formatFastDateFromJson(value);
                                } else {
                                    value = '-';
                                }
                                domConstruct.create('div', { innerHTML: value }, spanContainer, 'only');
                            }
                            break;
                        case 7: //function
                            var anchor = null;
                            if (updateDom) {
                                var href = currentColumn.href || 'javascript:void(0)';
                                if (currentColumn.href !== 'javascript:void(0)' && typeof currentColumn.href !== 'undefined' && currentColumn.parameters !== null) {
                                    currentColumn.parameters.forEach(function(parameter) {
                                        var replaceText = '{' + parameter + '}';
                                        href = href.replace(replaceText, row[parameter]);
                                    });
                                }
                                anchor = domConstruct.create('a', {
                                    'class': 'function-cell',
                                    title: currentColumn.title,
                                    href: href,
                                    onfocus: function(evt) {
                                        evt && evt.stopPropagation && evt.stopPropagation();
                                        this.style.backgroundColor = systemColor;
                                    },
                                    onblur: function() {
                                        this.style.backgroundColor = "";
                                    },
                                    onclick: function(e) {
                                        if (typeof href !== 'undefined' && href !== 'javascript:void(0)') {
                                            e.preventDefault();
                                        }
                                    }
                                }, spanContainer);
                            }
                            if (typeof currentColumn.icon === 'object' && currentColumn.icon.wordbBtnDisplay) {
                                if (updateDom) {
                                    if (currentColumn.icon.bigBtn) {
                                        domClass.add(spanContainer, 'Button');
                                        spanContainer.style.display = 'block';
                                        anchor.style.textDecoration = 'none';
                                    } else {
                                        domClass.add(anchor, 'Button');
                                    }
                                    domClass.add(spanContainer, 'button-grid-value');
                                }
                                var valSpan = (
                                    typeof currentColumn.icon.wordbBtnDisplay === 'function' ?
                                    currentColumn.icon.wordbBtnDisplay.apply(this, [row]) :
                                    currentColumn.icon.wordbBtnDisplay
                                );
                                if (valSpan) {
                                    if (updateDom) {
                                        domConstruct.create("span", {
                                            'class': '',
                                            innerHTML: valSpan
                                        }, anchor, "last");
                                    }
                                    excelValue = valSpan;
                                } else {
                                    if (updateDom) {
                                        spanContainer.innerHTML = '-';
                                    }
                                    excelValue = '-';
                                }
                            } else {
                                if (updateDom) {
                                    var iconPath = require.toUrl("bnext/images/" + (currentColumn.icon || 'ico_edit.gif'));
                                    var img = domConstruct.create("img", {
                                        'class': 'column-icon',
                                        src: iconPath.toString(),
                                        border: 0
                                    }, anchor, "last");
                                }
                            }
                            if (updateDom) {
                                var display = true;
                                if (lang.exists('display', currentColumn)) {
                                    if (typeof currentColumn.display === 'function') {
                                        display = currentColumn.display(row);
                                    }
                                }
                                if (anchor) {
                                    anchor.style.display = display ? '' : 'none';
                                    spanContainer = anchor;
                                }
                                domStyle.set(nodeTd, 'text-align', 'center');
                            }
                            useOnce = currentColumn.useOnce;
                            break;
                        case 8: //image map
                        case 21: //image map multiple
                            if (lang.exists("list", currentColumn) && (currentColumn.list.length > 0)) {
                                var tmp = 0;
                                for (var m = 0, lcl = currentColumn.list.length; m < lcl; m++) {
                                    listM = currentColumn.list[m];
                                    tmp = listM[currentColumn.value || 'value'];
                                    if (tmp === value) {
                                        if (updateDom) {
                                            var icono = listM[currentColumn.key || 'icon'];
                                            iconPath = require.toUrl("bnext/images/" + icono);
                                            domConstruct.create("img", {
                                                src: iconPath.toString(),
                                                border: 0,
                                                title: listM[currentColumn.name || 'name']
                                            }, spanContainer, "last");
                                        }
                                        excelValue = listM[currentColumn.text || currentColumn.name || 'name'];
                                        if (!excelValue) {
                                            excelValue = value;
                                        }
                                        break;
                                    }
                                }
                            }
                            if (updateDom) {
                                domStyle.set(nodeTd, 'text-align', 'center');
                            }
                            break;
                        case 9: //multiple Select
                            if (lang.exists("key", currentColumn)) {
                                var inner = "",
                                    temp, cache = {};
                                if (value && value.length > 0) {
                                    var space = currentColumn.doEnter ? '@br' : ', ';
                                    temp = lang.getObject(currentColumn.key, false, value[0]);
                                    if (temp && !cache[temp]) {
                                        inner = lang.getObject(currentColumn.key, false, value[0]);
                                        cache[temp] = true;
                                    }
                                    for (var ind = 1, len = value.length; ind < len; ind++) {
                                        temp = lang.getObject(currentColumn.key, false, value[ind]);
                                        if (temp && !cache[temp]) {
                                            inner += space + lang.getObject(currentColumn.key, false, value[ind]);
                                        }
                                        cache[temp] = true;
                                    }
                                }
                                if (updateDom) {
                                    spanContainer.innerHTML = encode(inner).replace(/@br/g, '<br>');
                                }
                                excelValue = inner;
                            } else {
                                if (updateDom) {
                                    log("No fue definido el parametro 'key' donde se define el atributo del objeto a mostrar");
                                    spanContainer.innerHTML = "-";
                                }
                                excelValue = '-';
                            }
                            break;
                        case 10: //text map
                            if (lang.exists("list", currentColumn) && (currentColumn.list.length > 0)) {
                                var list = currentColumn.list;
                                for (var m = 0, lcl = list.length; m < lcl; m++) {
                                    if (list[m][currentColumn.value] == value) {
                                        var text = list[m][currentColumn.key];
                                        var plainText = text;
                                        text = encode(text);
                                        if (belongsToGroup) {
                                            text = text === '-' ? '' : text;
                                            plainText = plainText === '-' ? '' : plainText;
                                            if (updateDom) {
                                                var actualNodes = query('span', nodeTd),
                                                    lastValue = 0;
                                                for (var o = 0; o < actualNodes.length; o++) {
                                                    if (actualNodes[o] == spanContainer) {
                                                        break;
                                                    }
                                                    if (actualNodes[o].childNodes.length > 1 ||
                                                        (actualNodes[o].firstChild && actualNodes[o].firstChild.innerHTML)
                                                    ) {
                                                        lastValue = 1;
                                                    }
                                                }
                                                if (lastValue && text) {
                                                    domConstruct.create('span', { innerHTML: ', ' }, spanContainer);
                                                }
                                            }
                                        }
                                        if (updateDom) {
                                            domConstruct.create("span", {
                                                innerHTML: text
                                            }, spanContainer, "last");
                                            if (list[m]['td-style'] && typeof list[m]['td-style'] === 'object') {
                                                try {
                                                    domStyle.set(nodeTd, list[m]['td-style']);
                                                } catch (e) {
                                                    console.log('cell style fail, ' + value + ', ' + j);
                                                }
                                            }
                                        }
                                        excelValue = plainText;
                                        break;
                                    }
                                }
                            }
                            break;
                        case 11: //timestamp
                            excelValue = this.tools.formatFastExcelTimestampFromJson(value);
                            if (updateDom) {
                                spanContainer.innerHTML = this.tools.formatFastTimestampFromJson(value);;
                            }
                            break;
                        case 12: //array of objects
                            /*Value contains the array*/
                            /*key contains the format array of each object and type*/
                            if (lang.exists("key", currentColumn)) {
                                excelValue = "";
                                if (value.length > 0) { //at least one element
                                    for (var indexArray = 0, lcl = value.length; indexArray < lcl; indexArray++) {
                                        var innerContainer = null;
                                        if (updateDom) {
                                            innerContainer = domConstruct.create("div", {
                                                innerHTML: ""
                                            });
                                            domAttr.set(innerContainer, "class", "listItem");
                                        }
                                        var element = value[indexArray];
                                        for (var keyIndex = 0, lk = currentColumn.key.length; keyIndex < lk; keyIndex++) {
                                            var indexedKey = currentColumn.key[keyIndex],
                                                name = indexedKey.name,
                                                type = indexedKey.type,
                                                title = 0;
                                            if (lang.exists('title', indexedKey)) {
                                                title = indexedKey.title;
                                            }
                                            var separator = '\n';
                                            if (lang.exists('separator', indexedKey)) {
                                                separator = indexedKey.separator;
                                            }
                                            var textValue = lang.getObject(name, false, element),
                                                node;
                                            if (updateDom) {
                                                if (title) {
                                                    if (type === "text") {
                                                        node = domConstruct.create(
                                                            "span", {
                                                                innerHTML: title ? '<span class = "' + name + 'Title">' +
                                                                    title +
                                                                    '</span>' +
                                                                    encode(textValue) :
                                                                    encode(textValue)
                                                            },
                                                            innerContainer);
                                                    }
                                                    if (type === "date") {
                                                        textValue = this.tools.formatFastDateFromJson(textValue);
                                                        node = domConstruct.create(
                                                            "span", {
                                                                innerHTML: title ?
                                                                    '<span class = "' + name + 'Title">' +
                                                                    title +
                                                                    '</span>' +
                                                                    encode(textValue) :
                                                                    encode(textValue)
                                                            },
                                                            innerContainer);
                                                    }
                                                    domAttr.set(node, "class", name);
                                                    spanContainer.appendChild(innerContainer);
                                                } else {
                                                    spanContainer.innerHTML += +indexArray === 0 ? encode(textValue) : separator + encode(textValue);
                                                }
                                            }
                                            excelValue += (title ? title : '') + encode(textValue) + separator;
                                        }
                                    }

                                } else {
                                    if (updateDom) {
                                        log("No fue definido el parametro 'value' donde se define el atributo del objeto a mostrar");
                                        spanContainer.innerHTML = "-";
                                    }
                                    excelValue = '-';
                                }
                            } else {
                                if (updateDom) {
                                    log("No fue definido el parametro 'value' donde se define el atributo del objeto a mostrar");
                                    spanContainer.innerHTML = "-";
                                }
                            }
                            break;
                        case 13: //image map
                            if (lang.exists("list", currentColumn) && (currentColumn.list.length > 0)) {
                                tmp = 0;
                                list = currentColumn.list;
                                for (var m = 0, lcl = list.length; m < lcl; m++) {
                                    listM = list[m];
                                    tmp = listM[currentColumn.value];
                                    if (tmp !== '' && +tmp === +value) {
                                        if (updateDom) {
                                            icono = listM[currentColumn.key];
                                            if (!icono) {
                                                spanContainer.innerHTML = "-";
                                                break;
                                            }
                                            if (currentColumn.key === 'cubeId') {
                                                iconPath = require.toUrl("bnext/images/cubo" + listM['cubeId'] + ".gif");
                                            } else {
                                                iconPath = require.toUrl("bnext/images/" + icono);
                                            }
                                            img = domConstruct.create("img", {
                                                src: iconPath.toString(),
                                                title: listM['name'] || listM['description'],
                                                border: 0
                                            }, spanContainer, "last");
                                            spanContainer = img;
                                            domClass.add(spanContainer, 'value-' + value);
                                            domStyle.set(nodeTd, 'text-align', 'center');
                                        }
                                        excelValue = listM['name'] || listM['description'];
                                        break;
                                    }
                                }
                            }
                            break;
                        case 14: //time ago
                            if (updateDom) {
                                new Timeago({
                                    title: value + 'Z'
                                }, spanContainer);
                            }
                            break;
                        case 15: //extended image/text map
                            if (lang.exists("list", currentColumn) && (currentColumn.list.length > 0)) {
                                list = currentColumn.list;
                                if (currentColumn.key === 'text') {
                                    for (var m = 0, lcl = list.length; m < lcl; m++) {
                                        if (list[m].Function(row, spanContainer, nodeTd, nodeTr)) {
                                            if (list[m]['Eval'] && typeof list[m]['Eval'] === 'function') {
                                                var temp = list[m]['Eval'](row, spanContainer, nodeTd, nodeTr);
                                                if (typeof temp === 'string') {
                                                    if (updateDom) {
                                                        spanContainer.innerHTML = temp;
                                                    }
                                                    excelValue = temp;
                                                } else if (updateDom && core.isNode(temp)) {
                                                    spanContainer.appendChild(temp);
                                                } else {
                                                    if (updateDom) {
                                                        spanContainer.innerHTML = temp + '';
                                                    }
                                                    excelValue = temp + '';
                                                }
                                            } else {
                                                if (updateDom) {
                                                    spanContainer.innerHTML = list[m]['text'] || list[m]['description'];
                                                }
                                                excelValue = list[m]['text'] || list[m]['description'];
                                            }
                                            break;
                                        }
                                    }
                                } else {
                                    for (var m = 0, lcl = list.length; m < lcl; m++) {
                                        if (list[m].Function(row)) {
                                            if (updateDom) {
                                                icono = list[m].icon;
                                                if (!icono) {
                                                    console.log('no icon ' + this.getRowCount());
                                                    break;
                                                }
                                                if (currentColumn.key === 'cubeId') {
                                                    iconPath = require.toUrl("bnext/images/cubo" + list[m]['cubeId'] + ".gif");
                                                } else {
                                                    iconPath = require.toUrl("bnext/images/" + icono);
                                                }
                                                img = domConstruct.create("img", {
                                                    src: iconPath.toString(),
                                                    title: list[m]['name'] || list[m]['description'],
                                                    border: 0,
                                                    value: list[m].value || ''
                                                }, spanContainer, "last");
                                                spanContainer = img;
                                                domStyle.set(nodeTd, 'text-align', 'center');
                                            }
                                            excelValue = list[m]['name'] || list[m]['description'];
                                            break;
                                        }
                                    }
                                }
                            } else {
                                console.warn('la columna ' + currentColumn.id + ' esta mal configurada (extended image map)');
                            }
                            break;
                        case 16: //selectable
                            if (updateDom) {
                                var selector = domConstruct.create('input', {
                                    type: 'checkbox',
                                    title: i18n.select,
                                    checked: !!value,
                                    readOnly: 'readOnly'
                                }, spanContainer, 'last');
                                domStyle.set(nodeTd, 'text-align', 'center');
                                if (lang.exists('attributes', currentColumn) && typeof currentColumn.attributes === 'object') {
                                    if (lang.exists('style', currentColumn.attributes)) {
                                        domStyle.set(selector, currentColumn.attributes.style);
                                    }
                                };
                                attachEvents(selector, this.setSelectableRow, [this, row, l, selector]);
                            }
                            excelValue = !!value ? 1 : 0;
                            break;
                        case 17: //Patternable
                            if (lang.exists("key", currentColumn) && lang.exists("idValue", currentColumn)) {
                                var inner = "", innerPlaneText = "",
                                    temp, cache = {};
                                var header =  currentColumn.header
                                                .replace(/@br/g, '')
                                                .replace(/@div/g, '')
                                                .replace(/Editar/g, '')
                                                .replace(/Edit/g, '')
                                                .replace(/@cdiv/g, ',')
                                                .replace(/\[/g, '')
                                                .replace(/]/g, '')
                                                .split(",")
                                                .filter(Boolean);
                                if (value && value.length && value.length > 0) {
                                    for (var a = 0, lcl = value.length; a < lcl; a++) {
                                        if (lang.exists("reference", currentColumn) && lang.exists("pattern", currentColumn)) {
                                            var _inner = "", _innerPlaneText = "";
                                            var _currentReference = "";
                                            var invalidRow = false;
                                            for (j = 0, lcc = currentColumn.reference.length; j < lcc; j++) {
                                                _currentReference = currentColumn.reference[j];
                                                var isDate = false;
                                                var property = "{" + _currentReference + "}";
                                                if (property.substr(property.length - 6) === "@date}") {
                                                    property = property.replace("@date}", "}");
                                                    _currentReference = currentColumn.reference[j].replace("@date", "");
                                                    isDate = true;
                                                }
                                                var _results = "";
                                                if ( value[a][_currentReference] === null)
                                                {
                                                    _results = "-";
                                                    isDate = false;
                                                }else{
                                                    _results = Object.byString(value[a], _currentReference);
                                                }
                                                if (_results.length === 0) {
                                                    invalidRow = true;
                                                }
                                                for (var k = 1; k < _results.length; k++) {
                                                    _inner = _inner.replace(property, property + "@br" + property);
                                                }
                                                for (var k = 0; k < _results.length; k++) {
                                                    var thisResult = _results[k];
                                                    var nameColum = ' [' + header[j] + ': ' + thisResult + ']  ';
                                                    if (isDate) {
                                                        thisResult = jsonDteToStr(thisResult);
                                                    }
                                                    if (typeof currentColumn.customPattern === 'function') {
                                                        thisResult = currentColumn.customPattern(property, thisResult, value[a]);
                                                    }
                                                    if (_inner === "") {
                                                        _inner = currentColumn.pattern.replace(property, thisResult);
                                                        if(property === '{status}'){
                                                            var statusTitle = thisResult.replace(/@img/g, '<img').replace(/@cimg/g, '>');
                                                                statusTitle = ' [' + $(statusTitle).attr('title') + ']  ';
                                                            _innerPlaneText = currentColumn.pattern.replace(property, statusTitle);
                                                        }else{
                                                            _innerPlaneText = currentColumn.pattern.replace(property, nameColum);
                                                        }
                                                    } else {
                                                        _inner = _inner.replace(property, thisResult);
                                                        if(property !== '{edit}'){
                                                            _innerPlaneText = _innerPlaneText.replace(property, nameColum);
                                                    }
                                                }
                                            }
                                            }
                                            if (invalidRow) {
                                                continue;
                                            }
                                            inner += _inner;
                                            innerPlaneText += _innerPlaneText;
                                        }
                                    }
                                } else {
                                    inner = "@ediv" + i18n.noRecords + ".@cdiv";
                                    innerPlaneText = i18n.noRecords;
                                }
                                excelValue = innerPlaneText.replace(/@br/g, '\n')
                                    .replace(/@div/g, '')
                                    .replace(/@divtittle/g, '')
                                    .replace(/@cdivtittle/g, '')
                                    .replace(/@cdiv/g, '')
                                    .replace(/@ediv/g, '')
                                    .replace(/{edit}/g, '');
                                if (currentColumn.header !== '' && value.length > 0) {
                                    inner = currentColumn.header + inner;
                                }
                                if (updateDom) {
                                    spanContainer.innerHTML = encode(inner).replace(/@br/g, '<br/>')
                                        .replace(/@img/g, '<img')
                                        .replace(/@cimg/g, '>')
                                        .replace(/@divtitle/g, '<div title="')
                                        .replace(/@cdivtitle/g, '">')
                                        .replace(/@a/g, '<a')
                                        .replace(/@ca/g, '</a>')
                                        .replace(/@ediv/g, '<div class="alertmessage">')
                                        .replace(/@div/g, '<div>')
                                        .replace(/@cdiv/g, '</div>');
                                }
                            } else {
                                log("No fue definido el parametro 'key' donde se define el atributo del objeto a mostrar");
                                spanContainer.innerHTML = "-";
                            }
                            if (updateDom) {
                                domClass.add(spanContainer, 'patternDialog');
                            }
                            break;
                        case 18: //Percentaje
                            value = ((value || '') + '').replace("D", "");
                            var numberFormatted = (+value > 0 ? value : 0) + '%';
                            if (updateDom) {
                                domConstruct.create('div', { innerHTML: numberFormatted }, spanContainer, 'only');
                            }
                            excelValue = numberFormatted;
                            break;
                        case 20: //Double
                            value = ((value || '') + '').replace("D", "");
                            var numberFormatted = ( typeof (+value) === 'number' ? value : 0);
                            if (updateDom) {
                                domConstruct.create('div', { innerHTML: numberFormatted }, spanContainer, 'only');
                            }
                            excelValue = numberFormatted;
                            break;
                        case 22: //Long
                            value = ((value || '') + '').replace("L", "");
                            var numberFormatted = (+value > 0 ? value : 0);
                            if (updateDom) {
                                domConstruct.create('div', { innerHTML: numberFormatted }, spanContainer, 'only');
                            }
                            excelValue = numberFormatted;
                            break;
                    }
                    if (spanContainer && spanContainer.innerText.length > 100) {
                        domClass.add(nodeTd, 'truncated-cell');
                        domConstruct.create('div', {
                            'class': 'show-full-content-btn material-icons',
                            'title': i18n.fullCellContent,
                            innerHTML: 'open_in_full'
                        }, nodeTd);
                    }
                    if (currentColumn.wrapText) {
                        domStyle.set(nodeTd.firstChild, 'overflow-wrap', 'anywhere');
                    }
                }

                if (excelValue === 'excelValue') {
                    excelValue = '';
                }
                if (typeof currentColumn.belongsToGroup !== 'undefined') {
                    var group = currentColumn.belongsToGroup.group;
                    if (core.isNull(groupValues[group])){
                        displayedResultsCols.push(excelValue);
                        groupValues[group] = excelValue;
                    } else {
                        if (excelValue !== ''){
                            groupValues[group] += ', ' + excelValue;
                            displayedResultsCols[displayedResultsCols.length-1] = groupValues[group];
                        }
                    }
                } else {
                    displayedResultsCols.push(excelValue);
                }
                var parameters = [];
                if (updateDom
                        && lang.exists("action", currentColumn)
                        && currentColumn.action
                        && currentColumn.action !== core.$noop
                        ) {
                    var action = this.columns[l].action,
                        eventsTarget = spanContainer;
                    if (currentColumn.eventContext === 'row-context') {
                        eventsTarget = row;
                    }
                    if (lang.exists("parameters", currentColumn)) {
                        for (var k = 0; k < currentColumn.parameters.length; k++) {
                            var attr = currentColumn.parameters[k] + '';
                            if (attr != null && attr !== "") {
                                if (attr === 'full-row') {
                                    parameters.push(row);
                                } else if (attr === 'clickedDom') {
                                    parameters.push(eventsTarget);
                                } else if (attr === 'tr-dom') {
                                    parameters.push(nodeTr);
                                } else if (attr === 'table-dom') {
                                    parameters.push(nodeTable);
                                } else if (attr === 'grid') {
                                    parameters.push(this);
                                } else {
                                    parameters.push(lang.getObject(attr + '', false, row));
                                }
                            }
                        }
                    } else { //si no se piden parametros especificos se pasa todo el objeto (toda la fila)
                        parameters.push(row);
                    }

                    if (attachEvents(eventsTarget, action, parameters, null, useOnce, currentColumn)) {
                        spanContainer.style.cursor = "pointer";
                    }
                }
            }
            query('.truncated-cell :not(.patternDialog) + .show-full-content-btn', nodeTr).forEach(function (btn) {
                on(btn, 'click', function () {
                    core.dialog(
                            btn.parentNode.innerText.trim().replace(/open_in_full$//* Se remueve el "icono" */, ''), core.i18n.accept_label, null, i18n.fullCellContent
                            );
                });
            });
            query('.truncated-cell .patternDialog + .show-full-content-btn', nodeTr).forEach(function (btn) {
                on(btn, 'click', function () {
                    core.dialog(
                            btn.parentNode.innerHTML, core.i18n.accept_label, null, i18n.fullCellContent
                            );
                });
            });
            if (updateDom) {
                this.setRowCount(this.getRowCount() + 1);
                if (this.onPushRow !== null && typeof this.onPushRow === 'function') {
                    this.onPushRow();
                }
            }
            return displayedResultsCols;
        },
        validateExcelColumn: function(column) {
            return (column.type &&
                +column.type !== columnTypes['hidden'] &&
                +column.type !== columnTypes['function'] &&
                column.display !== 'none');
        },
        dialogExcel: function() {
            var self = this;
            self.closePaginarButtonOpen();
            self.closeDisplayButtonOpen();
            if (self.displayDivContainer) {
                domClass.add(self.displayDivContainer.domNode, 'displayNone');
            }
            if (self.paginarDivContainer) {
                domClass.add(self.paginarDivContainer.domNode, 'displayNone');
            }
            require(['bnext/_base/grid-export-config'], lang.hitch(this, function(GridExportConfig) {
                if (!this.gridExportConfig) {
                    this.gridExportConfig = new GridExportConfig({
                        id: 'grid-export-config-' + this.id,
                        gridId: this.id,
                        onAccept:  function() {
                            try {
                                self.exportItems.call(self);
                            } catch (e) {
                                console.log(e.stack);
                            }
                        },
                        i18n: i18n
                    });
                }
                this.gridExportConfig.show(); 
            }));
        },
        exportItems: function() {
            var self = this;
            loader.showLoader(i18n.generating);
            var itemsExport = this.gridExportConfig.items.value || 1;
            if (+itemsExport === 1) {
                var exportObj = self.getDisplayedResults(true);
                self.executeExport(exportObj);
            } else {
                if (this.bean.count > MAX_PAGED_RECORDS) {
                    const maxCountExportExcel = i18n.maxCountExportExcel
                      .replaceAll('{{maxCount}}', MAX_PAGED_RECORDS.toString())
                      .replaceAll('{{currentCount}}', this.bean.count);
                    core.dialog(maxCountExportExcel, i18n.accept);
                    loader.hideLoader();
                } else {
                    this.loadAllRecords(true).then(function(bean) {
                        var data = self.generateRowsData(bean);
                        var exportObj = self.getDisplayedResults(true);
                        self.disableRowsLoading(false);
                        exportObj.displayData = [];
                        for (var i = 0, l = data.length; i < l; i++) {
                            exportObj.displayData.push({ row: data[i], metadata: [] });
                        }
                        self.executeExport(exportObj);
                    });
                }
            }
        },
        getWindowTitle: function() {
            var mainTitle = (dom.byId('window_title') || dom.byId('mainTitle') || {}).innerText;
            if (!mainTitle) {
                var titleDom = query('.content_title');
                if (titleDom && titleDom.length > 0) {
                    var h3Title = query('h3', titleDom[0]);
                    if (h3Title.length > 0) {
                        mainTitle = h3Title[0].innerText;
                    } else {
                        mainTitle = titleDom[0].innerText;
                    }
                }
                if (!mainTitle) {
                    mainTitle = (dom.byId('title')  || {}).value;
                }
            }
            return mainTitle || window.top.document.title || (window.title || {}).value || 'File';
        },
        executeExport: function(excelExportObj) {
            var gridId = this.id,
                header_title = this.getWindowTitle(),
                excel_v = this.gridExportConfig.format.value || '2007',
                typeExport = +this.gridExportConfig.type.value || 2,
                excelFileName = string.trim(header_title || 'BnextQMS').replace(/\s/g, '_') + '_EXCEL',
                savedObjName = gridId;
            excelFileName = excelFileName.replace(/ó/g, 'o')
                .replace(/á/g, 'a')
                .replace(/é/g, 'e')
                .replace(/í/g, 'i')
                .replace(/ú/g, 'u')
                .replace(/\//g, '_')
                .replace(/\\/g, '_');
            callMethod({
                url: '../DPMS/GridExport.action',
                method: 'executeExport',
                params: [excelExportObj, excel_v, typeExport, excelFileName, savedObjName]
            }).then(function(resultado) {
                loader.hideLoader().then(function() {
                    if (resultado.operationEstatus === 0) {
                        fracasoMsg(resultado.errorMessage);
                    } else if (resultado.operationEstatus === 1) {
                        //Para evitar cache en Internet Explorer con Date().getTime
                        var url = '../view/grid-export-download.view';
                        location.replace(url + '?code=' + resultado.extraJson + '&' + new Date().getTime());
                    }
                });
            }, function() {
                loader.hideLoader().then(function() { 
                    if (typeof window.errorCallBack === 'function') {
                        window.errorCallBack();
                    }
                });
            });
        },
        //Función que devuelve un objeto de la tabla de acuerdo a un atributo y su valor
        searchBean: function(key, value) {
            var object = null;
            var bean = this.getBean();
            if (bean != null && bean.count > 0) {
                bean = bean.data;
                for (var j = 0; j < bean.length; j++) {
                    var test = bean[j];
                    var data = lang.getObject(key, false, test);
                    if (value == data) {
                        object = test;
                        break;
                    }
                }
            }
            return object;
        },
        refreshSearch: function() {
            var _self = this;
            if (_self.onClickSearch && typeof _self.onClickSearch === 'function') {
                _self.onClickSearch();
            }
            return this.newSearch();
        },
        newSearch: function() {
            this.setCurrentPage(0);
            return this.refreshData();
        },
        replaceAll: function(str, needle, newVal) {
            while (str.indexOf(needle) !== -1) {
                str = str.replace(needle, newVal);
            }
            return str;
        },
        existsFilterForColumn: function(column, widjetId) {
            return widjetId && lang.exists("id", column) && (dom.byId(widjetId) ||
                dom.byId(widjetId + "_lower") ||
                dom.byId(widjetId + "_upper") ||
                dom.byId(widjetId + "_time_lower") ||
                dom.byId(widjetId + "_time_upper")
            );
        },
        getRefId: function(column) {
            var refId = column.idValue || column.id;
            refId = refId.toString().substring(refId.toString().indexOf(this.id) + 1);
            if (this.parseResultsUnderscoreToDot) {
                return this.replaceAll(refId, '.', '_');
            }
            return this.replaceAll(refId, '.', '#');
        },
        getRowsFilter: function() {
            var def = new Deferred(),
                _self = this;
            this.claseRow = 'grid-row even';
            var onBeforeRefreshResult = false;
            if (typeof this.onBeforeRefresh === 'function') {
                onBeforeRefreshResult = this.onBeforeRefresh(this);
            }
            if (onBeforeRefreshResult &&
                onBeforeRefreshResult.hasOwnProperty("then") &&
                typeof onBeforeRefreshResult.then === 'function'
            ) {
                onBeforeRefreshResult.then(
                    function() {
                        def.resolve(gcUtil.getParsedCriteria.apply(_self));
                    },
                    function() {
                        _self.searchBusy = false;
                        console.warn('onBeforeRefresh call returned a promise.reject()!!');
                    }
                );
            } else {
                def.resolve(gcUtil.getParsedCriteria.apply(this));
            }
            return def.promise;
        },
        setCriteriaValue: function(column, widjetId, value) {
            var referencia = registry.byId(widjetId);
            if (referencia.get('value')) {
                console.error('Ignored $GET filter because theres already one on it.')
            } else {
                referencia.set('value', value);
            }
        },
        getCriteriaValue: function(column, widjetId) {
            var criterio, dndType;
            var referencia = registry.byId(widjetId);
            if (referencia) {
                if (!referencia.isValid()) {
                    return {
                        invalidField: true,
                        referencia: referencia
                    };
                }
                criterio = referencia.get("value");
                dndType = referencia.get("declaredClass");
                if (lang.exists("searchObj", column) && !lang.exists("searchObj.type", column)) {
                    lang.setObject("searchObj", {
                        "type": "default"
                    }, column);
                }
            } else {
                referencia = dom.byId(widjetId);
                if (referencia) {
                    criterio = referencia.value;
                }
            }
            if (criterio === 'redirect') {
                if (column.searchObj.redirectDijit) {
                    referencia = registry.byId(column.searchObj.redirectDijit);
                    criterio = referencia.get("value");
                    dndType = referencia.get("declaredClass");
                } else if (column.searchObj.redirectDojo) {
                    referencia = dom.byId(column.searchObj.redirectDojo);
                    criterio = referencia.value;
                }
            }

            if (column.searchObj.type !== 'texto' && core.isNumeric(criterio)) {
                criterio = +criterio;
            }
            return {
                criterio: criterio,
                dndType: dndType,
                referencia: referencia
            };
        },
        setCriteriaFilter: function(searchObj, columnValue, filter, referencia, criteria, refId, dndType, widjetId, column) {
            var lower = registry.byId(widjetId + '_lower'),
                upper = registry.byId(widjetId + '_upper'),
                time_lower = registry.byId(widjetId + "_time_lower"),
                time_upper = registry.byId(widjetId + "_time_upper");
            var type = searchObj.type;
            var _self = this,
                handler = {
                    'automultiple': function(criterio) {
                        var hasFilter = false;
                        var array = JSON.parse(domAttr.get(referencia, 'data-array'));
                        if (array) {
                            var cc = '';
                            for (var i = 0; i < array.length; i++) {
                                criterio = array[i].id;
                                if (notEmpty(criterio)) {
                                    cc = cc + (i === 0 ? criterio + '<,>' + criterio : '<,>' + criterio);
                                }
                            }
                            if (cc !== '') {
                                lang.setObject("criteria." + refId, cc, filter);
                                hasFilter = true;
                            } else if (searchObj.required) {
                                lang.setObject("criteria." + refId, '0<,>0', filter);
                                hasFilter = true;
                            }
                        } else if (searchObj.required) {
                            lang.setObject("criteria." + refId, '0<,>0', filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'dynfield': function(criterio) {
                        var hasFilter = false;
                        var cc = '';
                        if (lang.isArray(criterio)) {
                            cc = '';
                            for (var i = 0; i < criterio.length; i++) {
                                var idValue = criterio[i];
                                if ((idValue || idValue === 0) && idValue !== "") {
                                    cc = cc + (i === 0 ? idValue + 'L' : ',' + idValue + 'L');
                                }
                            }
                        } else if (notEmpty(criterio)) {
                            cc = criterio + 'L';
                        }
                        if (cc !== '') {
                            lang.setObject("criteria." + refId, cc, filter);
                            hasFilter = true;
                        } else if (searchObj.required) {
                            lang.setObject("criteria." + refId, '0L,0L', filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'combo': function(criterio) {
                        var hasFilter = false;
                        if (notEmpty(criterio)) {
                            if (likeDndType(dndType)) {
                                lang.setObject("likeCriteria." + refId, criterio, filter);
                                hasFilter = true;
                            } else {
                                var type = searchObj.short ? 'S' : '' ;
                                lang.setObject("criteria." + refId, criterio + type, filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    },
                    'multiple': function(criterio) {
                        var hasFilter = false;
                        var cc = criterio;
                        if (lang.isArray(criterio)) {
                            cc = '';
                            var type = searchObj.multipleDataType === 'long' ? 'L' : '';
                            for (var i = 0; i < criterio.length; i++) {
                                var idValue = criterio[i];
                                if ((idValue || idValue === 0) && idValue !== "") {
                                    if (core.isNumeric(idValue) || searchObj.handleIds) {
                                        cc = cc + (i === 0 ? idValue + type : ',' + idValue + type);
                                    } else {
                                        cc = cc + (i === 0 ? idValue : ',' + idValue);
                                    }
                                }
                            }
                        }
                        if (cc !== '') {
                            var type = searchObj.multipleDataType === 'long' ? 'L' : '';
                            if (lang.isArray(criterio)) {
                                lang.setObject("criteria." + refId, cc, filter);
                            } else {
                                lang.setObject("criteria." + refId, cc + type, filter);
                            }
                            hasFilter = true;
                        } else if (searchObj.required) {
                            lang.setObject("criteria." + refId, '0,0', filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'multiple-entity': function(criterio) {
                        var hasFilter = false;
                        var cc = '';
                        if (lang.isArray(criterio)) {
                            cc = '';
                            for (var i = 0; i < criterio.length; i++) {
                                var idValue = criterio[i];
                                if ((idValue || idValue === 0) && idValue !== "") {
                                    cc = cc + (i === 0 ? idValue + 'L' : ',' + idValue + 'L');
                                }
                            }
                        } else if (notEmpty(criterio)) {
                            cc = criterio + 'L';
                        }
                        if (cc !== '') {
                            lang.setObject("criteria." + refId, cc, filter);
                            hasFilter = true;
                        } else if (searchObj.required) {
                            lang.setObject("criteria." + refId, '0,0', filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'multiple-text': function(criterio) {
                        var hasFilter = false;
                        var cc = '';
                        if (lang.isArray(criterio)) {
                            cc = '';
                            for (var i = 0; i < criterio.length; i++) {
                                var idValue = criterio[i];
                                if ((idValue || idValue === 0) && idValue !== "") {
                                    cc = cc + (i === 0 ? idValue : ',' + idValue);
                                }
                            }
                        } else if (notEmpty(criterio)) {
                            cc = criterio;
                        }
                        if (cc !== '') {
                            lang.setObject("criteria." + refId, cc, filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'comboMultiple': function(criterio) {
                        var hasFilter = false;
                        if (notEmpty(criterio)) {
                            if (likeDndType(dndType)) {
                                lang.setObject("likeCriteria." + refId, criterio + '<,>' + criterio, filter);
                                hasFilter = true;
                            } else {
                                lang.setObject("criteria." + refId, criterio + '<,>' + criterio, filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    },
                    'text-in': function(criterio) {
                        var hasFilter = false;
                        if (notEmpty(criterio)) {
                            var key = domAttr.get(dom.byId(referencia.id), 'data-key');
                            var criteriaType;
                            if (likeDndType(dndType)) {
                                criteriaType = "likeCriteria";
                                hasFilter = true;
                            } else {
                                criteriaType = "criteria";
                                hasFilter = true;
                            }
                            var currentFilter = lang.getObject(criteriaType + "." + refId, false, filter);
                            if(currentFilter) {
                                var cCounterKey = 'criteriaCounter@' + criteriaType + "@" + refId;
                                if(_self[cCounterKey]) {
                                   _self[cCounterKey]++;
                                } else {
                                   _self[cCounterKey] = 1;
                                }
                                lang.setObject(criteriaType + "." + refId + "@excludedFromKey" + _self[cCounterKey], currentFilter, filter);
                                hasFilter = true;
                            }
                            lang.setObject(criteriaType + "." + refId, key + '<text-in>' + criterio, filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'owner': function(criterio) {
                        var hasFilter = false;
                        if (notEmpty(criterio)) {
                            var key = domAttr.get(dom.byId(referencia.id), 'data-key');
                            if (likeDndType(dndType)) {
                                lang.setObject("likeCriteria." + refId, key + '<owner>' + criterio, filter);
                                hasFilter = true;
                            } else {
                                lang.setObject("criteria." + refId, key + '<owner>' + criterio, filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    },
                    'fecha': function(criterio) {
                        var hasFilter = false;
                        if (lower && lower.get("value")) {
                            var date = lower.get("value");
                            criterio = _self.tools.formatDate(date);
                            if (notEmpty(criterio)) {
                                criterio = "<date>" + criterio;
                                lang.setObject("lowerLimit." + refId, criterio, filter);
                                hasFilter = true;
                            }
                        }
                        if (upper && upper.get("value")) {
                            var date = upper.get("value");
                            criterio = _self.tools.formatDate(date);
                            if (notEmpty(criterio)) {
                                criterio = "<date>" + criterio;
                                lang.setObject("upperLimit." + refId, criterio, filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    },
                    'timestamp': function(criterio) {
                        var hasFilter = false;
                        if ((lower && lower.get("value")) || time_lower && time_lower.get('value')) {
                            if ((lower && lower.get("value")) && time_lower && time_lower.get('value')) {
                                criterio = '<timestamp>' + _self.tools.formatDate(lower.get("value")) + " " + _self.tools.formatTime(time_lower.get("value"));
                            } else if (lower && lower.get("value")) {
                                criterio = '<date>' +  _self.tools.formatDate(lower.get("value"));
                            } else if (time_lower && time_lower.get("value")) {
                                criterio = '<time>' + _self.tools.formatTime(time_lower.get("value"))
                            } else {
                                console.error('No deberia ser posible llegar a este escenario revisar la logica')
                            }
                            lang.setObject("lowerLimit." + refId, criterio, filter);
                            hasFilter = true;
                        }
                        if ((upper && upper.get("value")) || time_upper && time_upper.get('value')) {
                            if (upper && upper.get("value") && time_upper && time_upper.get('value')){
                                criterio = '<timestamp>' + _self.tools.formatDate(upper.get("value")) + " " + _self.tools.formatTime(time_upper.get("value"));
                            } else if (upper && upper.get("value")) {
                                criterio = '<date>' +  _self.tools.formatDate(upper.get("value"));
                            } else if (time_upper && time_upper.get('value')) {
                                criterio = '<time>' + _self.tools.formatTime(time_upper.get("value"))
                            } else {
                                console.error('No deberia ser posible llegar a este escenario revisar la logica')
                            }
                            lang.setObject("upperLimit." + refId, criterio, filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'time': function(criterio) {
                        var hasFilter = false;
                        if (time_lower) {
                            criterio = _self.tools.formatTime(time_lower.get("value"));
                            if (notEmpty(criterio)) {
                                criterio = "<time>" + criterio;
                                lang.setObject("lowerLimit." + refId, criterio, filter);
                                hasFilter = true;
                            }
                        }
                        if (time_upper) {
                            criterio = _self.tools.formatTime(time_upper.get("value"));
                            if (notEmpty(criterio)) {
                                criterio = "<time>" + criterio;
                                lang.setObject("upperLimit." + refId, criterio, filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    },
                    'object': function(criterio) {
                        var hasFilter = false;
                        if (notEmpty(criterio)) {
                            if (likeDndType(dndType)) {
                                lang.setObject("likeCriteria." + refId + "#" + columnValue, criterio, filter);
                                hasFilter = true;
                            } else {
                                lang.setObject("criteria." + refId + "#" + columnValue, criterio, filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    },
                    'percentaje': function(criterio) {
                        var hasFilter = false;
                        if (notEmpty(criterio)) {
                            var percentaje = +criterio > 0 ? +criterio : 0;
                            lang.setObject("criteria." + refId, percentaje + "D", filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'double': function(criterio) {
                        var hasFilter = false;
                        if (core.isNumeric(criterio)) {
                            var percentaje = +criterio > 0 ? +criterio : 0;
                            lang.setObject("criteria." + refId, percentaje + "D", filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'long': function(criterio) {
                        if (notEmpty(criterio)) {
                            var longValue = +criterio > 0 ? +criterio : 0;
                            lang.setObject("criteria." + refId, longValue + "L", filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'integer': function(criterio) {
                        var hasFilter = false;
                        if (core.isInteger(criterio)) {
                            lang.setObject("criteria." + refId, criterio, filter);
                            hasFilter = true;
                        }
                        return hasFilter;
                    },
                    'texto': function(criterio) {
                        var hasFilter = false;
                        if (!core.isNull(criterio)
                                 && criterio.trim() !== '') {
                            if (likeDndType(dndType)) {
                                lang.setObject("likeCriteria." + refId, criterio.trim(), filter);
                                hasFilter = true;
                            } else {
                                lang.setObject("criteria." + refId, criterio.trim(), filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    },
                    'default': function(criterio) {
                        if (notEmpty(criterio)) {
                            if (likeDndType(dndType)) {
                                lang.setObject("likeCriteria." + refId, criterio, filter);
                                hasFilter = true;
                            } else {
                                lang.setObject("criteria." + refId, criterio, filter);
                                hasFilter = true;
                            }
                        }
                        return hasFilter;
                    }
                };
            var hasFilter;
            if (!handler[type]) {
                hasFilter = handler['default'](criteria);
            } else {
                hasFilter = handler[type](criteria);
            }
            if (hasFilter && searchObj.extraFilters) {
                this.addExtraFilters(searchObj.extraFilters, filter);
                return true;
            }
            return hasFilter;

        },
        toggleSearchPanel: function(filter) {
            if (this.searchFilterPanel) {
                if (filter && !filter.emptyCriterias) {
                    this.toggleSearchFilterPanel(true);
                    if (!this.searchCriteriaOpen && MediaQueries.isDesktop()) {
                        this.toggleSearchFields(true);
                    }
                } else {
                    this.toggleSearchFilterPanel(false);
                }
            }
        },
        addExtraFilters: function(extraFilters, filter) {
            var _self = this;
            if (typeof extraFilters === 'object' && extraFilters.length > 0) {
                array.forEach(extraFilters, function(extraFilter) {
                    var extraKey = extraFilter.key;
                    var extraType = extraFilter.type;
                    var extraCriteria = extraFilter.value;
                    var extraRefId = _self.getRefId({
                        id: extraKey
                    });
                    var widjetId = _self.id + '_' + (extraKey) || null;
                    var widget = registry.byId(widjetId);
                    if (widget) {
                        widget.set('value', extraCriteria);
                    }
                    if (filter) {
                        _self.setCriteriaFilter(extraType, null, filter, null, extraCriteria, extraRefId, null, widjetId);
                    }
                });                    
            }
        },
        setRowsFilter: function(filter, extraCriteria) {
            var _self = this;
            if (!filter || filter === '{}') {
                return;
            }
            if (typeof filter === 'string') {
                try {
                    filter = JSON.parse(filter);
                } catch (e) {
                    return;
                }
            }
            var emptyObj = {};
            filter = lang.mixin({
                direction: null,
                emptyCriterias: true,
                windowPath: '',
                filterInPatch: null,
                lockedAliasAsume: null,
                asumeAlias: null,
                likeCriteria: emptyObj,
                upperLimit: emptyObj,
                lowerLimit: emptyObj,
                dynamicFieldCriteria: emptyObj,
                gridId: this.id,
                pageSize: 15,
                page: 0,
                field: null,
                criteria: emptyObj,
                columns: {}
            }, filter);
            if (extraCriteria && !isNull(extraCriteria.key) && !isNull(extraCriteria.value)) {
                delete filter.criteria[extraCriteria.key];
                filter.emptyCriterias = gcUtil.areEmptyCriterias(filter);
            } else if (extraCriteria instanceof Array) {
                for (var r = 0; r < extraCriteria.length; r++) {
                    var elemento = extraCriteria[r];
                    if (lang.isObject(elemento) && !isNull(elemento.key) && !isNull(elemento.value)) {
                        delete filter.criteria[elemento.key];
                    } else if (lang.isObject(elemento) &&
                            (!isNull(elemento.keyMap) || !isNull(elemento.keyIn)) &&
                            !isNull(elemento.key)) {
                        delete filter.criteria[elemento.key.toString().replace(/\./g, '#')];
                    } else if (lang.isObject(elemento) &&
                            (!isNull(elemento.idMap) || !isNull(elemento.idIn)) &&
                            !isNull(elemento.key)) {
                        delete filter.criteria[elemento.key.toString().replace(/\./g, '#')];
                    }
                }
                filter.emptyCriterias = gcUtil.areEmptyCriterias(filter);
            }
            this.currentPage = filter.page;
            this.toggleSearchPanel(filter);
            for (var m = 0, lgt = this.columns.length; m < lgt; m++) {
                try {
                    var column = this.columns[m];
                    var columnShown = column.display !== 'none';
                    if (filter.columns[column.id]) {
                        if (columnShown &&
                            filter.columns[column.id].shown === 0) {
                            this.hideColumn(m);
                        } else if (!columnShown &&
                            filter.columns[column.id].shown === 1) {
                            this.showColumn(m);
                        }
                    }
                    var widjetId = this.id + '_' + (column.id) || null;
                    if (this.existsFilterForColumn(column, widjetId)) {
                        var isTransient = column.isTransient || false;
                        //genera criterios de busqueda
                        if (!isTransient) {
                            var refId = this.getRefId(column);
                            var referencia = registry.byId(widjetId);
                            if (!referencia) {
                                referencia = dom.byId(widjetId);
                            }
                            var lower = registry.byId(widjetId + "_lower");
                            var upper = registry.byId(widjetId + "_upper");
                            var time_lower = registry.byId(widjetId + "_time_lower");
                            var time_upper = registry.byId(widjetId + "_time_upper");
                            if ((referencia ||
                                    lower ||
                                    upper ||
                                    time_lower ||
                                    time_upper) &&
                                (column.searchObj && lang.exists("type", column.searchObj))) {
                                switch (column.searchObj.type) {
                                    case 'combo':
                                    case 'texto':
                                        var value = filter.criteria[refId];
                                        
                                        if (!core.isNull(value)) {
                                            value = ((value || '') + '').replace(SHORT_DELIMITER_REGEX, "");
                                            referencia.set('value', value);
                                            continue;
                                        }
                                        value = filter.likeCriteria[refId];
                                        if (!core.isNull(value)) {
                                            referencia.set('value', value);
                                            continue;
                                        }
                                        break;
                                    case 'text-in':
                                        var type = 'criteria';
                                        if(core.isNull(getValue('criteria'))) {
                                            type = 'likeCriteria';
                                        }
                                        if(core.isNull(getValue('likeCriteria'))) {
                                            continue;
                                        }
                                        var values = getValue(type).split('<text-in>');
                                        var key = values[0];
                                        var value = values[1];
                                        if (column.key === key) {
                                            referencia.set('value', value);
                                        }
                                        function getValue(type) {
                                            var current = filter[type];
                                            for(var k in current) {
                                                if(!current.hasOwnProperty(k)) {
                                            continue;
                                        }
                                                var realId = k.replace(/@excludedFromKey.+/,'');
                                                var realKey = current[k].split('<text-in>')[0];
                                                if(realId === refId && realKey === column.key) {
                                                    return current[k];
                                                }
                                            }
                                            return null;
                                        }
                                        break;
                                    case 'owner':
                                        var value = filter.criteria[refId];
                                        if (!core.isNull(value)) {
                                            value = value.split('<owner>')[1];
                                            referencia.set('value', value);
                                            continue;
                                        }
                                        value = filter.likeCriteria[refId];
                                        if (!core.isNull(value)) {
                                            value = value.split('<owner>')[1];
                                            referencia.set('value', value);
                                            continue;
                                        }
                                        break;
                                    case 'fecha':
                                        if (lower) {
                                            var value = filter.lowerLimit[refId];
                                            if (!core.isNull(value)) {
                                                value = this.tools.parseDate(value.split('<date>')[1]);
                                                lower.set('value', value);
                                            }
                                        }

                                        if (upper) {
                                            var value = filter.upperLimit[refId];
                                            if (!core.isNull(value)) {
                                                value = this.tools.parseDate(value.split('<date>')[1]);
                                                upper.set('value', value);
                                            }
                                        }
                                        break;
                                    case 'timestamp':
                                        if (lower) {
                                            var value = filter.lowerLimit[refId];
                                            if (!core.isNull(value)) {
                                                value = this.tools.parseDate(value.split('<timestamp>')[1]);
                                                lower.set('value', value);
                                                continue;
                                            }
                                        }

                                        if (upper) {
                                            var value = filter.upperLimit[refId];
                                            if (!core.isNull(value)) {
                                                value = this.tools.parseDate(value.split('<timestamp>')[1]);
                                                upper.set('value', value);
                                                continue;
                                            }
                                        }
                                        break;
                                    case 'time':
                                        if (time_lower) {
                                            var value = filter.lowerLimit[refId];
                                            if (!core.isNull(value)) {
                                                value = this.tools.parseTime(value.split('<time>')[1]);
                                                time_lower.set('value', value);
                                                continue;
                                            }
                                        }
                                        if (time_upper) {
                                            var value = filter.upperLimit[refId];
                                            if (!core.isNull(value)) {
                                                value = this.tools.parseTime(value.split('<time>')[1]);
                                                time_upper.set('value', value);
                                                continue;
                                            }
                                        }
                                        break;
                                    case 'double':
                                    case 'percentaje':
                                        var value = filter.criteria[refId];
                                        if (!core.isNull(value)) {
                                            var percentaje = ((value || '') + '').replace("D", "");
                                            percentaje = +percentaje > 0 ? +percentaje : 0;
                                            referencia.set('value', percentaje);
                                            continue;
                                        }
                                        break;
                                    case 'long':
                                        var value = filter.criteria[refId];
                                        if (core.isNull(value) || value === '') {
                                            continue;
                                        }
                                        value = +(value.replace("L", ""));
                                        if (core.isInteger(value)) {
                                            referencia.set('value', value);
                                            continue;
                                        }
                                        break;
                                    case 'integer':
                                        var value = filter.criteria[refId];
                                        if (core.isNull(value) || value === '') {
                                            continue;
                                        }
                                        value = +value;
                                        if (core.isInteger(value)) {
                                            referencia.set('value', value);
                                            continue;
                                        }
                                        break;
                                    case 'multiple':
                                    case 'multiple-entity':
                                        var value = filter.criteria[refId];
                                        if (core.isNull(value) || value === '') {
                                            continue;
                                        }
                                        var valArray = [];
                                        if (typeof value === 'number') {
                                            valArray = [value];
                                        } else {
                                            value = value.split(',');
                                            array.forEach(value, function(item) {
                                                valArray.push(item.replace("L", ""));
                                            });
                                        }
                                        referencia.set('value', valArray);
                                        continue;
                                    case 'multiple-text':
                                        var value = filter.criteria[refId];
                                        if (core.isNull(value) || value === '') {
                                            continue;
                                        }
                                        value = value.split(',');
                                        var valArray = [];
                                        array.forEach(value, function(item) {
                                            valArray.push(item);
                                        });
                                        referencia.set('value', valArray);
                                        continue;
                                    case 'dynfield':
                                        var value = filter.criteria[refId];
                                        if (core.isNull(value) || value === '') {
                                            continue;
                                        }
                                        value = value.split(',');
                                        var valArray = [];
                                        array.forEach(value, function(item) {
                                            valArray.push(item.replace("L", ""));
                                        });
                                        referencia.set('value', valArray);
                                        continue;
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.error('Fail settings search parameter.' + filter + ' ', e);
                }
            }
            //carga criterios de busqueda para campos dinamicos
            var dynWidjets = this.getDynamicSearchFields(),
                dw;
            for (var fk in dynWidjets) {
                if (!dynWidjets.hasOwnProperty(fk)) {
                    continue;
                }
                dw = dynWidjets[fk];
                var refId = dw.id.replace(new RegExp(this.getDynamicSearchFieldsScope() + '-'), '');
                var value = lang.getObject(refId, false, filter.dynamicFieldCriteria);
                if (core.isNull(value)) {
                    continue;
                }
                if (value.indexOf(',') !== -1) {
                    value = value.split(',');
                }
                dw.set('value', value);
            }
            _self._renderHiddenColumnsText();
        },
        loadSavedFilter: function() {
            try {
                if (core.isNull(this.savedWindowFilter) ||
                    this.savedWindowFilter === '#invalid') {
                    var windowFiltersDom = dom.byId('savedWindowFilters');
                    if (windowFiltersDom &&
                        windowFiltersDom.value &&
                        !core.isNull(windowFiltersDom.value) &&
                        windowFiltersDom.value !== '#invalid') {
                        this.savedWindowFilter = (JSON.parse(windowFiltersDom.value) || {})[this.id];
                        this.setRowsFilter(this.savedWindowFilter, this.extraCriteria);
                    }
                } else {
                    this.setRowsFilter(this.savedWindowFilter, this.extraCriteria);
                }
            } catch (e) {
                console.error("Fail loading saved filter", e);
            }
        },
        enableRowsLoading: function(allRecords, def) {
            if (!allRecords) {
                this.rowsLoading = true;
            }
        },
        disableRowsLoading: function(allRecords) {
            if (!allRecords) {
                this.rowsLoading = false;
            }
        },
        /**
         *  No utilizar el then para realizar operaciones en el grid. El grid aun no recibe los valores
         * @param {type} allRecords Si se cargan todos los valores
         * @returns {gridComponentL#12.Deferred.promise}
         */
        getRows: function(allRecords, skipWaiting) {
            var _self = this;
            if (!allRecords && this.rowsLoading) {
                console.error("Get rows called while loading rows.");
            }
            var def = new Deferred();
            this.enableRowsLoading(allRecords, def);
            if (this.getServiceStore() && this.getMethodName()) {
                var params;
                if (!skipWaiting) {
                    this.waiting();
                }
                this.getRowsFilter().then(lang.hitch(this,function(filter) {
                    if (!filter) {
                        this.disableRowsLoading(allRecords);
                        console.error('Invalid onBeforeRefresh call!');
                        return;
                    }
                    if (!allRecords && filter.invalidSearch) {
                        if (filter.invalidField && typeof filter.invalidField.focus === 'function') {
                            filter.invalidField.focus();
                        }
                        this.disableRowsLoading(allRecords);
                        def.reject(i18n.invalidSearchValues);
                        return;
                    }
                    if (allRecords) {
                        filter.page = 0;
                        filter.pageSize = 0;
                    }
                    filter.windowPath = this.windowPath;
                    filter.dynamicSearchEnabled = !!this.dynamicSearchEnabled;
                    if (this.onCreateRowFilter && typeof this.onCreateRowFilter === 'function') {
                        this.onCreateRowFilter(filter);
                    }
                    var statisticsFilter = lang.clone(filter);
                    filter.enableStatistics = false;
                    filter.statisticsFields = [];
                    if (!allRecords) {
                        if (this.getOpenSearchZone()) {
                            this.toggleSearchPanel(filter);
                        }
                    }
                    params = [filter];
                    if (typeof this.methodExtraFilter === 'object' && this.methodExtraFilter.length) {
                        params = params.concat(this.methodExtraFilter);
                    }
                    if (typeof this.getRowsPrecondition === 'function') {
                        if (this.getRowsPrecondition(params)) {
                            callMethod({
                                url: this.getServiceStore(),
                                method: this.getMethodName(),
                                params: params
                            }).then(function(bean) {
                                _self.disableRowsLoading(allRecords);
                                def.resolve(bean);
                            }, function(bean) {
                                _self.disableRowsLoading(allRecords);
                                def.reject(bean);
                            });
                        } else {
                            _self.disableRowsLoading(allRecords);
                            def.resolve();
                        }
                    } else {
                        callMethod({
                            url: this.getServiceStore(),
                            method: this.getMethodName(),
                            params: params
                        }).then(function(bean) {
                            _self.disableRowsLoading(allRecords);
                            def.resolve(bean);
                        }, function(bean) {
                            _self.disableRowsLoading(allRecords);
                            def.reject(bean);
                        });
                    }
                    if (!allRecords && this.enableStatistics) {
                        require(['bnext/grid-component-charts'], lang.hitch(this, function(gcCharts) {
                            this.infragisticsChartInit.infragisticsLoad.then(lang.hitch(this, function () {
                                gcCharts.updateStatistics(this.statisticsEnvelope, this.getServiceStore(), this.getMethodName(), statisticsFilter, this.columns);
                            }));
                        }));
                    }
                }), function() {
                    console.warn('Invalid getRowsFilter call!');
                    def.resolve();
                });
            } else {
                this.disableRowsLoading(allRecords);
                log("No se realizó la consulta. Es necesario establecer el parametro serviceStore y methodName ");
                def.resolve();
            }
            return def.promise;
        },
        put: function(row) {
            if (this.dataMap[row.id]) {
                return;
            }
            this.dataMap[row.id] = true;
            this.getBean().data.push(row);
        },
        setData: function(data) {
            this.getBean().data = data;
            this.getBean().count = data.length;
            this.displayResults();
        },
        refreshLastUpdate: function(bean) {
            var lastUpdateDom = dom.byId('last-update-' + this.id);
            if (!lastUpdateDom) {
                return;
            }
            var lastSyncDate;
            if (bean && bean.lastSyncDate) {  
                lastSyncDate = this.tools.parseDateFromJson(bean.lastSyncDate);
            } else {          
                lastSyncDate = new Date();
            }
            lastUpdateDom.innerHTML = ''
                + (i18n.lastUpdate) 
                + ':' + locale.format(lastSyncDate, { selector: "date", datePattern: "dd/MM/yyyy HH:mm:ss" }) 
            ;    
        },
        refreshLocalData: function() {
            this.displayResults();
        },
        refreshData: function(allRecords) {
            var deferred = new Deferred();
            var _self = this;
            _self.closeDisplayButtonOpen();
            _self.closePaginarButtonOpen();
            this.getRows(allRecords).then(lang.hitch(this, function(bean) {
                _self.getResultsInfoUp().style.display = 'none';
                this.refreshLastUpdate(bean);
                if (bean) {
                    if (bean.data) {
                        this.dataMap = {};
                        for (var idx in bean.data) {
                            if (!bean.data.hasOwnProperty(idx) || !bean.data[idx].id) {
                                continue;
                            }
                            this.dataMap[bean.data[idx].id] = true;
                        }
                    }
                    this.setParseFromDynamicResults(bean.parseFromDynamicResults || null);
                }
                this.setBean(bean);
                this.displayResults(deferred).then(deferred.resolve, deferred.reject).then(lang.hitch(this, this.gridResized));
            }), function(e) {
                _self.getResultsInfoUp().style.display = 'none';
                _self.searchBusy = false;
                _self.clearData();
                _self.setGridToNoData();
                deferred.resolve();
                return _self.showDialogError(e);
            });
            return deferred.promise;
        },
        evaluateFreezeHeader: function(bean) {
            if (bean && bean.data && bean.data.length > 0) {
                this.activateLocalFreezeHeader();
            } else {
                this.inactivateFreezeHeader();
            }
        },
        showDialogError: function(e) {
            if (!e || !e.errorObject || !e.errorObject.error || !e.errorObject.error.message) {
                return core.error(i18n.errorLoadingRows);
            }
            if (typeof e.message !== 'undefined' && e.message !== null) {
                if (e.message === 'datasource-credential-error') {
                    return core.error(i18n.datasource_credential_error);
                }
                if (e.errorObject && e.errorObject.error && e.errorObject.error.name && e.errorObject.error.name === 'com.microsoft.sqlserver.jdbc.SQLServerException') {
                    return this.handleSQLServerException(e);
                }
            }
            return core.error(e); 
        },
        handleSQLServerException: function(e) {
            if (e.message.includes(MessageTypes.INCORRECTSINTAX)) {
              const nMessage = e.message.replace(MessageTypes.INCORRECTSINTAX, i18n.incorrectSyntax);
              return core.error(i18n.sqlError + nMessage);
            }
            if (e.message.includes(MessageTypes.INVALIDOBJECT)) {
              const nMessage = e.message.replace(MessageTypes.INVALIDOBJECT, i18n.invalidObject);
              return core.error(i18n.sqlError + nMessage);
            }
            return core.error(e.message);
        },
        loadAllRecords: function(skipWaiting) {
            var deferred = new Deferred();
            var _self = this;
            this.getRows(true, skipWaiting).then(lang.hitch(this, function(bean) {
                if (bean) {
                    this.setParseFromDynamicResults(bean.parseFromDynamicResults || null);
                }
                deferred.resolve(bean);
            }), function(e) {
                _self.searchBusy = false;
                _self.clearData();
                _self.setGridToNoData();
                return _self.showDialogError(e);
            });
            return deferred.promise;
        },
        clearData: function() {
            this.dataMap = {};
            this.setParseFromDynamicResults(null);
            this.setBean({ data: [], count: 0 });
            this.displayResults();
        },
        runHookUpdateSuccess: function() {
            if (this.getHookUpdateSuccess()) {
                eval(this.getHookUpdateSuccess());
            }
        },
        runHookUpdateFailure: function() {
            if (this.getHookUpdateFailure()) {
                eval(this.getHookUpdateFailure());
            }
        },
        runDownloadExcel: $noop,
        setParseFromDynamicResults: function(parseFromDynamicResults) {
            this.parseFromDynamicResults = parseFromDynamicResults;
        },
        getParseFromDynamicResults: function() {
            return this.parseFromDynamicResults || this.parseResultsUnderscoreToDot;
        },
        getOnBeforeRefresh: function() {
            return this.onBeforeRefresh;
        },
        setOnBeforeRefresh: function(onBeforeRefresh) {
            this.onBeforeRefresh = onBeforeRefresh;
        },
        getNoExcel: function() {
            return this.noExcel;
        },
        setNoExcel: function(noExcel) {
            this.noExcel = noExcel;
        },
        getNoSettings: function() {
            return this.noSettings;
        },
        setNoSettings: function(noSettings) {
            this.noSettings = noSettings;
        },
        getOnLoaded: function() {
            return this.onLoaded;
        },
        setOnLoadedByRow: function(onLoadedByRow) {
            this.onLoaded = onLoadedByRow;
        },
        getOnLoadedByRow: function() {
            return this.onLoadedByRow;
        },
        setOnLoaded: function(onLoaded) {
            this.onLoaded = onLoaded;
        },
        getRowCount: function() {
            return this.rowCount;
        },
        setRowCount: function(rowCount) {
            this.rowCount = rowCount;
        },
        getExtraCriteria: function() {
            return this.extraCriteria;
        },
        setExtraCriteria: function(extraCriteria) {
            this.extraCriteria = extraCriteria;
        },
        getExtraLikeCriteria: function() {
            return this.extraLikeCriteria;
        },
        setExtraLikeCriteria: function(extraLikeCriteria) {
            this.extraLikeCriteria = extraLikeCriteria;
        },
        getEmptyMessage: function() {
            return this.emptyMessage;
        },
        setEmptyMessage: function(emptyMessage) {
            this.emptyMessage = emptyMessage;
        },
        getNoRegMessage: function() {
            return this.noRegMessage;
        },
        setNoRegMessage: function(noRegMessage) {
            this.noRegMessage = noRegMessage;
        },
        getSearchContainer: function() {
            return this.searchContainer;
        },
        setSearchContainer: function(searchContainer) {
            this.searchContainer = searchContainer;
        },
        getHiddenResults: function() {
            return this.hiddenResults;
        },
        setHiddenResults: function(hiddenResults) {
            this.container = hiddenResults;
        },
        getContainer: function() {
            return this.container;
        },
        setContainer: function(container) {
            this.container = container;
        },
        getExcelExport: function() {
            return this.excelExport;
        },
        setExcelExport: function(excelExport) {
            this.excelExport = excelExport;
        },
        getHookUpdateFailure: function() {
            return this.hookUpdateFailure;
        },
        setHookUpdateFailure: function(hookUpdateFailure) {
            this.hookUpdateFailure = hookUpdateFailure;
        },
        getHookUpdateSuccess: function() {
            return this.hookUpdateSuccess;
        },
        setHookUpdateSuccess: function(hookUpdateSuccess) {
            this.hookUpdateSuccess = hookUpdateSuccess;
        },
        inactivateFreezeHeader: function() {
            this.removeFreezeHeader();
            this.localFreezeHeader = false;
        },
        getDisplayedResults: function(exportMode) {
            var self = this,
            results = this.displayedResults;
            datosExcel = this.dataExcelResultsRows;
            //si alguna de las propiedades es 0, se vuelve a generar el displayed Result
            results.displayNames = [];
            results.displayTypes = [];
            results.displayData = [];
            var heads;
            if (this.freezeHeader && this.freezeHandler && this.freezeHandler.floatTable) {
                heads = query('tr th', this.freezeHandler.floatTable.floatThead('getRowGroups')[0]);
            } else {
                heads = query('thead tr th', this.container);
            }
            exportMode && this.inactivateFreezeHeader();
            heads.forEach(function(h) {
                if (domClass.contains(h, 'floatThead-col')) {
                    return;
                }
                var id = h.id,
                    txt = domAttr.get(h, 'data-label'),
                    skip = domAttr.get(h, 'data-skipExportExcel'),
                    column = null;
                if (skip === 'true'){
                    return;
                }  
                id = id.replace(self.id + '_', '').replace(/_header/g, '');
                column = array.filter(self.columns, function(col) {
                    return col.id === id;
                })[0];
                results.displayNames.push({
                    'title': txt,
                    'shown': (self.validateExcelColumn(column) ? 1 : 0),
                    'notExportable': ((+column.type === columnTypes['hidden'] ||
                        +column.type === columnTypes['function'] ||
                        +column.type === columnTypes['evaluatedRow'] ||
                        column.skipExcelExport) && !column.forceExcelExport) ? 1 : 0
                });
                results.displayTypes.push(column.type);
            });
            exportMode && this.activateFreezeHeader();
            if (datosExcel.length > 0) {
                var rows = query('tbody tr.grid-row', this.container).filter(this.isNotUtilityRow);
                rows.forEach(function(tr, i) {
                    if (!datosExcel[i]) {
                        return;
                    }
                    var dataX, metadataX;
                    results.displayData[i] = {};
                    results.displayData[i].row = dataX = [];
                    results.displayData[i].metadata = metadataX = [];
                    array.forEach(tr.childNodes, function(td, _index) {
                        if (!datosExcel[i]) {
                            return;
                        }
                        var value = '';
                        if (domClass.contains(td, 'object-list-not-empty')) {
                            metadataX.push({
                                valid: true,
                                color: domAttr.get(td, 'attr-color'),
                                backgroundColor: domAttr.get(td, 'attr-background-color'),
                                numberItems: domAttr.get(td, 'attr-number-items')
                            });
                        } else if (domClass.contains(td, 'export-colors')) {
                            metadataX.push({
                                valid: true,
                                color: domAttr.get(td, 'attr-color'),
                                backgroundColor: domAttr.get(td, 'attr-background-color'),
                                numberItems: 1
                            });
                        } else {
                            metadataX.push({
                                valid: false
                            });
                        }
                        value = datosExcel[i].row[_index];
                        if (value === null || typeof value === 'undefined' || value === '' || value === '-' || domAttr.get(td, 'data-updated')) {
                            value = td.textContent || td.innerText || '-';
                        }
                        dataX.push(value);
                    });
                });
            }
            return this.displayedResults;
        },
        setDisplayedResults: function(displayedResults) {
            this.displayedResults = displayedResults;
        },
        getBean: function() {
            if (!this.bean) {
                this.bean = { data: [], count: 0 };
            }
            return this.bean;
        },
        setBean: function(bean) {
            this.bean = bean;
            this.evaluateFreezeHeader(bean);
        },
        setServiceStore: function(serviceURL) {
            this.serviceStore = serviceURL;
        },
        setMethodName: function(methodName) {
            this.methodName = methodName;
        },
        getServiceStore: function() {
            return this.serviceStore + this.getConcatenateParams();
        },
        getMethodName: function() {
            return this.methodName;
        },
        setMethodExtraFilter: function(methodExtraFilter) {
            this.methodExtraFilter = methodExtraFilter;
        },
        getMethodExtraFilter: function() {
            return methodExtraFilter;
        },
        getTable: function() {
            return this.table;
        },
        setResultsInfo: function(id) {
            if (typeof id === 'string') {
                this.resultsInfo = dom.byId(id);
            } else if (typeof id !== 'undefined') {
                this.resultsInfo = id;
            }

        },
        getResultsInfo: function() {
            return this.resultsInfo;
        },
        setResultsInfoUp: function(elem) {
            this.resultsInfoUp = (elem);
        },
        getResultsInfoUp: function() {
            return this.resultsInfoUp;
        },
        setPaginationInfo: function(id) {
            this.paginationInfo = dom.byId(id);
        },
        getPaginationInfo: function() {
            return this.paginationInfo;
        },
        setEnablePaginationModern: function(enable) {
            this.paginationModern = enable;
        },
        getEnablePaginationModern: function() {
            return this.paginationModern;
        },
        reload: function() {
            this.setPageSize(this.getPageSize());
        },
        cleanCriteria: function() {
            var columnas = this.columns;
            for (var j = 0; j < columnas.length; j++) {
                var elemento = columnas[j];
                if (lang.exists("searchObj", elemento)) {
                    var searchObj = lang.getObject("searchObj", false, elemento);
                    var type = lang.getObject("type", false, searchObj);
                    var e;
                    switch (type) {
                        case 'texto':
                        case 'comboMultiple':
                        case 'combo':
                            e = registry.byId(elemento.id);
                            if (e && e.set) {
                                e.set("value", "");
                            }
                            break;
                        case 'automultiple':
                            var contenedor = dom.byId("externalAutoCompleter_" + elemento.id);
                            var padreContenedor = contenedor.parentNode;
                            domConstruct.destroy(contenedor);
                            createAutocomplete(elemento, padreContenedor);
                            break;
                        case 'timestamp':
                            e = registry.byId(elemento.id + "_time_lower");
                            if (e && e.set) {
                                e.set("value", null);
                            }
                            e = registry.byId(elemento.id + "_time_upper");
                            if (e && e.set) {
                                e.set("value", null);
                            }
                        case 'time':
                            e = registry.byId(elemento.id + "_time_lower");
                            if (e && e.set) {
                                e.set("value", null);
                            }
                            e = registry.byId(elemento.id + "_time_upper");
                            if (e && e.set) {
                                e.set("value", null);
                            }
                        case 'fecha':
                            e = registry.byId(elemento.id + "_lower");
                            if (e && e.set) {
                                e.set("value", null);
                            }
                            e = registry.byId(elemento.id + "_upper");
                            if (e && e.set) {
                                e.set("value", null);
                            }
                            break;
                        case 'percentaje':
                        case 'double':
                        case 'long':
                        case 'integer':
                            e = registry.byId(elemento.id);
                            if (e && e.set) {
                                e.set("value", null);
                            }
                            break;
                    }
                } else {
                    log("No existe serachObj para esa columna:");
                }
            }
        },
        _getCssColumnId: function(id) {
            if (id === null || typeof id === 'undefined') {
                return null;
            }
            return id.replace(/#/g, '\\#');
        },
        _setDisplayForColumns: function(column_index, display){
            var column = this.columns[column_index],
            columnId = this._getCssColumnId(column.id);
            //muestro datos
            var classRow = '.css_' + this.id + '_' + columnId.replace(/\./g, "_");
            columnId = columnId.replace(/\./g, '\\.');
            query('#' + this.id + '_' + columnId + '_header,' + classRow).forEach(function(td) {
                if (td) {
                    td.style.display = display;
                    if (column.type !== columnTypes.function && column.type !== columnTypes.functionImage) {
                        if (display === 'none') {
                            td.classList.remove('available-to-print');
                        } else {
                            td.classList.add('available-to-print');
                        }
                    }
                }
            });
            //cambio la columna en busquedas
            this.columns[column_index].display = display;
            if (this.displayDivContainer) {
                query('#hidden_'  + this.id + '_' + columnId, this.displayDivContainer.domNode).forEach(function(columnConfig) {
                    if (columnConfig) {
                        domAttr.set(columnConfig, 'checked', display === 'none');
                    } else {
                        console.error('Tried to hide an invalid column, ', columnConfig);
                    }

                });
            }
        },
        showColumn: function(column_index) {
            //oculto y muestro datos
            this._setDisplayForColumns(column_index,'');
            //agrego la columna de objeto de exportacion a excel
            try {
                this.getDisplayedResults().displayNames[column_index].shown = 1;
            } catch (e) {
                console.error(e);
            }
            this.activateFreezeHeader();
        },
        hideColumn: function(column_index, dis) {
            this.inactivateFreezeHeader();
            try {
                this._setDisplayForColumns(column_index,'none');
                //quito la columna de objeto de exportacion a excel
                if (this.getDisplayedResults() && this.getDisplayedResults().displayNames && this.getDisplayedResults().displayNames[column_index]) {
                    this.getDisplayedResults().displayNames[column_index].shown = 0;
                }
                return 1;
            } catch (e) {
                console.error(e);
                return 0;
            }
            this.activateFreezeHeader();
        },
        //se pone como 0 para que no tenga limites
        maxWidth: 820,
        linkGridViewMode: false,
        isLinkedTo: null,
        isLinked: false,

        /**
         * Cuando se haga popRowById al grid que se pase aqui, este se añadira al grid del cual fue llamada la funcion 
         *
         * @param {Object} grid: El grid al que se va a conectar el grid actual.
         * @param {Object} id: Si el id de los dos grid no es por default 'id' 
         *              se envía una columna de tipo linked 
         *                {
         *                  base: '',
         *                  linked: ''
         *                } 
         *             con los valores del id tanto en el grid actual 'base' como el id del grid 
         *             a conectar 'linked'.
         * @param {Array} columns: Para transformar un row de un tipo de grid a otro se puede definir 
         *  la equivalencia de las columnas en un arreglo de columnas de tipo linke.
         **/
        linkGrid: function(grid, idConfig, columnsConfig) {
            //ToDo: Revisar si hay otras formas de pasar columnas de grids registradas en el sistema
            var advancedConfig = false;
            this.isLinkedTo = grid;
            this.isLinked = true;
            grid.isLinkedTo = this;
            grid.isLinked = true;
            if (!isNull(idConfig)) {
                advancedConfig = true;
                columnsConfig = columnsConfig || [];
            } else {
                idConfig = {};
            }
            if (!this.linkGridViewMode) {
                this.refreshOnPopRow = false;
                aspect.before(grid, 'popRowById', lang.hitch(grid, function() {
                    //Se verifica que la página actual sea 0 para evitar: 
                    //--> (error en la siguiente situación => Cuando se acaban los registros de la página 5(ejemplo)
                    // en lugar de regresar a la página 4, regresaba a la página 3 debido a la propiedad refreshOnPopRow)
                    //Se verifica que el componente padre cuente con registros en memoria para evitar:
                    //--> (error en la siguiente situación => Cuando se terminan los registros por completo del 
                    // componente padre, éste vuelve a cargar los registros)
                    if (this.currentPage === 0 && (this.getBean().data.length - 1) >= 0) {
                        this.refreshOnPopRow = true;
                    } else {
                        this.refreshOnPopRow = false;
                    }
                }));
                aspect.after(grid, 'popRowById', lang.hitch(this, function(row) {
                    //evita el trigger del after de mas abajo
                    if (advancedConfig) {
                        setObject(idConfig.base, lang.getObject(idConfig.linked, false, row), row);
                        array.forEach(columnsConfig, function(item) {
                            setObject(item.base, lang.getObject(item.linked, false, row), row);
                        });
                    }
                    this.pushRow(row);
                    var rows = query('tbody tr', this.getContainer()).filter(this.isNotUtilityRow);
                    rows.forEach(function(tr, i) {
                        tr.className = i % 2 === 0 ? "even" : "odd";
                    });
                    this.getResultsInfoUp().style.display = 'none';
                    if (this.expandableNode) {
                        domClass.remove(this.expandableNode, 'displayNone');
                    }
                    dom.byId(this.container).style.display = '';
                    if (advancedConfig) {
                        grid.getResultsInfoUp().style.display = 'none';
                    }
                    return row;
                }));
                aspect.after(this, 'popRowById', lang.hitch(grid, function(row) {
                    if (advancedConfig) {
                        setObject(idConfig.linked, lang.getObject(idConfig.base, false, row), row);
                        array.forEach(columnsConfig, function(item) {
                            setObject(item.linked, lang.getObject(item.base, false, row), row);
                        });
                    }
                    this.pushRow(row);
                    var rows = query('tbody tr', this.getContainer()).filter(this.isNotUtilityRow);
                    rows.forEach(function(tr, i) {
                        tr.className = i % 2 === 0 ? "even" : "odd";
                    });
                    this.getResultsInfoUp().style.display = 'none';
                    if (this.expandableNode) {
                        domClass.remove(this.expandableNode, 'displayNone');
                    }
                    dom.byId(this.container).style.display = '';
                    return row;
                }));
                aspect.after(this, 'popRowById', lang.hitch(this, function(row) {
                    if (advancedConfig && idConfig.isComposite) {
                        this.createExtraCompositeCriteria(grid, idConfig.base, idConfig.compositeIds);
                    } else {
                        this.setCriteriaTo(grid, idConfig.base, idConfig.linked);
                    }
                    return row;
                }));
                aspect.after(this, 'pushRow', lang.hitch(this, function(row) {
                    if (this.id === grid.id) {
                        if (advancedConfig && idConfig.isComposite) {
                            this.createExtraCompositeCriteria(grid, idConfig.linked, idConfig.compositeIds);
                        } else {
                            this.setCriteriaTo(grid, idConfig.linked, idConfig.base);
                        }
                    } else {
                        if (advancedConfig && idConfig.isComposite) {
                            this.createExtraCompositeCriteria(grid, idConfig.base, idConfig.compositeIds);
                        } else {
                            this.setCriteriaTo(grid, idConfig.base, idConfig.linked);
                        }
                    }
                    return row;
                }));
            }
        },
        createExtraCompositeCriteria: function(component, column, compositeKeys) {
            var configuration = {
                    data: this.getBean().data,
                    key: '<!Composite>',
                    compositeKeys: compositeKeys,
                    column: column
                },
                criteria = this.createCompositeCriteria(configuration);
            component.getExtraCriteria()[0] = criteria;
        },
        createCompositeCriteria: function(configuration) {
            var data = configuration.data,
                column = configuration.column,
                compositeCriteria = {
                    key: column + configuration.key
                },
                values = [];
            if (data && data.length) {
                array.forEach(data, function(row) {
                    var rowCriteria = {};
                    array.forEach(configuration.compositeKeys, function(compositeKey) {
                        var id = column + '.' + compositeKey;
                        lang.setObject(compositeKey, lang.getObject(id, false, row), rowCriteria);
                    });
                    values.push(rowCriteria);
                });
                compositeCriteria.value = JSON.stringify(values);
            } else {
                compositeCriteria = {};
            }
            return compositeCriteria;
        },
        createCriteria: function(criteria, key, linked) {
            var data = criteria.array,
                separator = criteria.separator,
                value = lang.exists('value', criteria) ? criteria.value : '<zero>',
                pattern = '',
                exclude = {
                    key: criteria.key || 'id',
                    value: value,
                    linked: linked
                },
                criterio;
            key = key || 'id';
            if (data && data.length && data[0]) {
                criterio = lang.getObject(key, false, data[0]);
                pattern = criterio + separator + criterio;
                for (var i = 1, l = data.length; i < l; i++) {
                    try {
                        criterio = lang.getObject(key, false, data[i]);
                    } catch (e) {}
                    if (notEmpty(criterio)) {
                        pattern += separator + criterio;
                    }
                }
                exclude.value = '' + pattern;
            }
            return exclude;
        },
        /*Se excluyen datos que ya estan agregados*/
        setCriteriaTo: function(component, criteriaKey, excludeKey, position) {
            var exclude = {
                    array: this.getBean().data,
                    separator: '<!>',
                    value: null,
                    key: excludeKey
                },
                criteria = this.createCriteria(exclude, criteriaKey, true);
            if (this.getExtraCriteria() && typeof this.getExtraCriteria() === 'object') {
                for (var idxSt in this.getExtraCriteria()) {
                    if (!this.getExtraCriteria().hasOwnProperty(idxSt) || !this.getExtraCriteria()[idxSt].linked) {
                        continue;
                    }
                    if (this.getExtraCriteria()[idxSt].key === criteria.key) {
                        delete this.getExtraCriteria()[idxSt];
                    }
                }
                criteria = [criteria].concat(this.getExtraCriteria());
                component.setExtraCriteria(criteria);
            } else {
                component.setExtraCriteria([criteria]);
            }

        },
        setSelectableRow: function(context, row, index, selector) {
            var selected = domAttr.get(selector, 'checked');
            row[context.columns[index].id] = selected;
        },
        selectAllRows: function(context, input, index) {
            var selected = domAttr.get(input, 'checked');
            array.forEach(context.getBean().data, function(row) {
                row[context.columns[index].id] = selected;
            });
            var selector = "td[id^='td_" + context.id + "_" + context._getCssColumnId(context.columns[index].id) + "'] input";
            query(selector, context.container).forEach(function(input) {
                domAttr.set(input, 'checked', selected);
            });
            var col = context.columns[index];
            if (selected) {
                if (col.onSelectAllRows && typeof col.onSelectAllRows === 'function') {
                    col.onSelectAllRows();
                }
            } else {
                if (col.onDeselectAllRows && typeof col.onDeselectAllRows === 'function') {
                    col.onDeselectAllRows();
                }                
            }
        },
        getSelectedRows: function(columnId) {
            var selectedRows = [];
            array.forEach(this.getBean().data, function(row) {
                if (!!row[columnId]) {
                    selectedRows.push(row);
                }
            });
            return selectedRows;
        },
        getRowSelected: function() {
            var selectedRows;
            query('.grid-row.selected-content', this.getContainer()).forEach(function(row) {
                selectedRows = row;
            });
            return selectedRows;
        }, 
        scrollToRowId: function() {
            query('.grid-row.selected-content', this.getContainer()).forEach(function(row) {
                if (row.scrollIntoView) {
                    row.scrollIntoView();
                }
            });
        },
        setGridLocalSize: function(gridLocalSize) {
            this.gridLocalSize = gridLocalSize;
        },
        getGridLocalSize: function() {
            return this.gridLocalSize;
        },
        setGridLocalPage: function(gridLocalPage) {
            this.gridLocalPage = gridLocalPage;
        },
        getGridLocalPage: function() {
            return this.gridLocalPage;
        },
        getDisplayButton: function() {
            return this.displayButton;
        },
        getExportButton: function() {
            return this.exportButton;
        },
        getGridOptions: function() {
            return this.gridOptionsDiv;
        },
        getRowsLoading: function() {
            return this.rowsLoading;
        },
        isDataAvailable: function() {
            if (this.getBean().count > 0) {
                    return true;
            }
            return (
                    this.getBean().data
                    && this.getBean().data.length > 0
            );
        }
    };
    gridComponentArgs.windowPath = windowPath;
    var gridComponent = window.gridComponent = declare([], gridComponentArgs);
    return gridComponent;
});
