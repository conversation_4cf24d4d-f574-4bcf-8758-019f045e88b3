define(['./callMethod', 'dojo/store/JsonRest', 'dojo/_base/declare', 'dojo/_base/lang', 'dojo/_base/array'], function(callMethod, JSONRest, declare, lang, array) {

  var objectTocriteria = function(object) {
    var crit = {}, keys = getKeys(object);
    array.forEach(keys, function(key) {
      var o = object[key];
      crit[key] = '';
      if (o instanceof Array) {
        crit[key] += o[0];
        array.forEach(o, function(x) {
          crit[key] += '<,>' + o[0];
        });
      } else {
        crit[key] = o;
      }
    });
    return crit;
  };
  var getKeys = function(obj) {
    if (Object.keys) {
      return Object.keys(obj);
    }
    var arr = [];
    for (var i in obj) {
      arr.push(i);
    }
    return arr;
  };
  var SMDStore = {
    saveMethod: 'save',
    getMethod: 'load',
    queryMethod: 'getRows',
    deleteMethod: 'delete',
    get: function(id, options) {
      return callMethod({
        url: this.target,
        method: this.getMethod,
        params: [id]
      });
    },
    put: function(object, options) {
      if((options || {}).overwrite === false){
        object.id  = -1;
      }
      return callMethod({
        url: this.target,
        method: this.saveMethod,
        parms: [object]
      });
    },
    remove: function(id, options) {
      return callMethod({
        url: this.target,
        method: this.deleteMethod,
        params: [id]
      });
    },
    query: function(query, options) {
      
      options = options || {};
      var params = {
        pageSize: 0,
        page: 0,
        field: undefined,
        direction: 1,
        criteria: {}
      };
      if (options.count != null) {
        params.pageSize = options.count;
      }
      if (options.start != null) {
        params.page = params.count != null ? options.start / params.count : options.start;
      }
      if (options.sort && options.sort.length) {
        params.field = options.sort[0].attribute;
        params.direction = options.sort[0].descending ? 1 : 0;
      }
      params.criteria = objectTocriteria(query || {});
      
      return callMethod({
        url: this.target,
        params: [params],
        method: this.queryMethod
      });
    }
  };
  return declare([JSONRest], SMDStore);
});