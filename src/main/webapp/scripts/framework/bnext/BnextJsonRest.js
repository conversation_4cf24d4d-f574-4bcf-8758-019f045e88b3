define([
  'bnext/_base/throttle-queue',
  'bnext/dBox',
  'i18n-util',
  'dojo/Deferred',
  'dojo/_base/lang',
  'dojo/date/locale',
  'dojo/dom',
  'dojo/i18n!bnext/_base/nls/offline-support',
  'dojo/i18n!bnext/nls/callMethod',
  'dojo/rpc/JsonService',
  'dojo/store/JsonRest',
  'bnext/callMethod',
  'loader'
], (ThrottleQueue, dBox, i18nUtil, Deferred, lang, locale, dom, offlineSupport, i18n, jsonServ, JsonRest, callMethod, loader) => {
  const topContext = window.top.window;
  const win = window;
  const MAX_RECONNECTION_ATTEMPTS = 5;
  const WAIT_TIME_CONSTANT = 250;
  const WAIT_TIME_FACTOR_CONSTANT = 1.2;
  let HEALTH_INFO_URL = '../DPMS/LabelInterpolationService.action';
  const HEALTH_INFO_ALIVE_METHOD = 'isAlive';
  const LOGIN_URL = '../qms/{language}/menu/legacy/';
  if (location.href.indexOf('/rest/licencing/') !== -1) {
    HEALTH_INFO_URL = `../${HEALTH_INFO_URL}`;
  }
  topContext.waitRestForSession = false;
  topContext.waitingRestForSessionDefs = false;
  topContext.waitRestForConnection = false;
  topContext.waitingRestForConnectionDefs = false;
  topContext.waitRestForConnectionDebounce = false;

  function resolveWaitForSession(result, args, error) {
    topContext.waitRestForSession = false;
    if (topContext.waitingRestForSessionDefs && topContext.waitingRestForSessionDefs.length > 0) {
      for (const waiting of topContext.waitingRestForSessionDefs) {
        if (!waiting || waiting.isFulfilled()) {
          return;
        }
        if (result) {
          BnextJsonRest(args).then(waiting.resolve, waiting.reject);
        } else {
          errorIncapsulation(args, waiting)(error, true);
        }
      }
    }
    topContext.waitingRestForSessionDefs = false;
  }
  function resolveWaitForConnection(result, args, error) {
    topContext.waitRestForConnection = false;
    if (topContext.waitingRestForConnectionDefs && topContext.waitingRestForConnectionDefs.length > 0) {
      for (const waiting of topContext.waitingRestForConnectionDefs) {
        if (!waiting || waiting.isFulfilled()) {
          return;
        }
        if (result) {
          BnextJsonRest(args).then(waiting.resolve, waiting.reject);
        } else {
          errorIncapsulation(args, waiting)(error, true);
        }
      }
    }
    topContext.waitingRestForConnectionDefs = false;
    topContext.waitRestForConnectionDebounce = false;
  }
  function inputPassword(extraMessage, args, origDef, lastUrl, systemCall) {
    if (topContext.waitRestForConnection) {
      if (origDef) {
        topContext.waitingRestForConnectionDefs.push(origDef);
      }
      return 'waiting-for-connection';
    }
    const userAccountDom = dom.byId('userAccount') || win.top.document.getElementById('userAccount');
    if (!userAccountDom) {
      resolveWaitForSession(false, args);
      win.top.window.waitRestForSession = false;
      const lang = i18nUtil.getLang();
      const loginUrl = LOGIN_URL.replace('{language}', lang); // ToDo: Agregar la URL actual
      return win.location.replace(loginUrl);
    }
    if (topContext.waitRestForSession && !systemCall) {
      if (origDef) {
        topContext.waitingRestForSessionDefs.push(origDef);
      }
      return 'waiting-for-session';
    }
    topContext.waitRestForSession = true;
    if (!topContext.waitingRestForSessionDefs) {
      topContext.waitingRestForSessionDefs = [];
    }
    if (origDef) {
      topContext.waitingRestForSessionDefs.push(origDef);
    }
    require(['bnext/angularLogin'], (angularLogin) => {
      angularLogin.open().then(
        (r) => {
          resolveWaitForSession(true, args, r);
        },
        (e) => {
          resolveWaitForSession(false, args, e);
          console.error(e);
        }
      );
    });
    return { message: 'waiting-for-login' };
  }
  function getErrorMessageObject(message, id) {
    return {
      errorObject: {
        error: {
          message: message,
          id: id
        }
      }
    };
  }
  /*
   * Method that recieves an argument composed of:
   *
   *@return deferred (it can be chained using deferred.then(onSuccess,onFail,onProgress)) o false en caso de una excepcion
   **/
  function errorIncapsulation(args, def) {
    return (r, systemCall) => {
      const result = handleErrback(r, args, def, systemCall);
      if (result && result.xval !== 'nosession' && args.errback) {
        args.errback(r);
      } else if (result === 'not-available' && !topContext.waitRestForConnection) {
        let message;
        if (navigator.onLine) {
          const date = locale.format(new Date(), {
            selector: 'date',
            datePattern: 'dd/MM/yyyy HH:mm:ss'
          });
          message = offlineSupport['not-available-message'].replace('${date}', date);
          def.reject(getErrorMessageObject(message, 'not-available'));
        } else {
          const date = locale.format(new Date(), {
            selector: 'date',
            datePattern: 'dd/MM/yyyy HH:mm:ss'
          });
          message = offlineSupport['offline-message'].replace('${date}', date);
          def.reject(getErrorMessageObject(message, 'offline'));
        }
        loader.hideLoader();
        dBox.dialog(message);
        return false;
      } else if (result === 'exist-record') {
        const message = i18n.exist_record;
        def.reject(getErrorMessageObject(message, 'exist-record'));
        dBox.dialog(message);
        return false;
      }
      if (result === 'forbidden') {
        const message = i18n.forbidden;
        def.reject(getErrorMessageObject(message, 'forbidden'));
        return false;
      }
      if (result === 'waiting-for-connection' || (result && result.message === 'waiting-for-login')) {
        return false;
      }
      def.reject(r);
      return false;
    };
  }
  function reconnect(args, lastError) {
    if (topContext.waitRestForConnectionDebounce && topContext.waitRestForConnectionDebounce.attempts > MAX_RECONNECTION_ATTEMPTS) {
      resolveWaitForConnection(false, args, lastError);
      return;
    }
    topContext.waitRestForConnectionDebounce.add(() => {
      topContext.waitRestForConnectionDebounce.attempts++;
      console.warn('Lost connection to server. Reconnecting ...');
      callMethod({
        url: HEALTH_INFO_URL,
        method: HEALTH_INFO_ALIVE_METHOD,
        params: []
      }).then(
        (r) => {
          loader.hideLoader();
          resolveWaitForConnection(true, args, r);
        },
        (e) => {
          console.warn('Failed to establish connection to server. Retrying ...', e);
          reconnect(args, e);
        }
      );
    });
  }
  function handleDisconnect(err, args, origDef, systemCall) {
    const internalError = isInternalError(args, systemCall);
    if (topContext.waitRestForConnection && !internalError) {
      topContext.waitingRestForConnectionDefs.push(origDef);
      return origDef;
    }
    if (internalError) {
      origDef.reject(err);
      return 'not-available';
    }
    if (topContext.waitRestForConnectionDebounce && topContext.waitRestForConnectionDebounce.attempts > MAX_RECONNECTION_ATTEMPTS) {
      origDef.reject(err);
      resolveWaitForConnection(false, args, err);
      return 'not-available';
    }
    topContext.waitRestForConnection = true;
    if (!topContext.waitingRestForConnectionDefs) {
      topContext.waitingRestForConnectionDefs = [];
    }
    if (!topContext.waitRestForConnectionDebounce) {
      topContext.waitRestForConnectionDebounce = new ThrottleQueue(WAIT_TIME_CONSTANT, WAIT_TIME_FACTOR_CONSTANT, topContext);
      topContext.waitRestForConnectionDebounce.attempts = 0;
    }
    topContext.waitingRestForConnectionDefs.push(origDef);
    loader.showLoader(offlineSupport.reconnecting).then(
      () => {
        reconnect(args, err);
      },
      (error) => {
        resolveWaitForConnection(false, args, error);
      }
    );
    return 'waiting-for-connection';
  }
  function isInternalError(args, systemCall) {
    return systemCall || (args.url === HEALTH_INFO_URL && args.method === HEALTH_INFO_ALIVE_METHOD);
  }
  function handleErrback(r, args, def, systemCall) {
    const internalError = isInternalError(args, systemCall);
    if (topContext.waitRestForConnection && !internalError) {
      topContext.waitingRestForConnectionDefs.push(def);
      return;
    }
    const errorName = lang.getObject('errorObject.error.name', false, r) || 'unknown';
    let err = errorName || 'errback';
    if (err === 'unknown' && r.status === 401) {
      err = 'nosession';
    } else if (err === 'unknown') {
      err = r.errorMessage || 'unknown';
    }
    if ((err === 'nosession' || err === 'invalid-credentials') && !topContext.waitRestForSession) {
      if (typeof args.onSessionExpired === 'function') {
        args.onSessionExpired();
      } else if (typeof win.onSessionExpired === 'function') {
        win.onSessionExpired();
      }
      const inputResult = inputPassword(null, args, def);
      inputResult.xval = err;
      return inputResult;
    }
    if (r.status === 403) {
      return 'forbidden';
    }
    if (r.message?.startsWith('Unable to load')) {
      return handleDisconnect(r, args, def, systemCall);
    }
    if (r.message && r.message.indexOf('UNIQUE KEY') >= 0) {
      return 'exist-record';
    }
    if (r.message && r.message.indexOf('duplicate key') >= 0) {
      return 'exist-record';
    }
    return errorName || 'errback';
  }

  function BnextJsonRest(args) {
    const def = new Deferred();
    try {
      const rest = new JsonRest({ target: args.url });
      rest[args.method || 'put'](args.params || null).then(
        (r) => {
          def.resolve(r);
        },
        errorIncapsulation(args, def)
      );
    } catch (e) {
      errorIncapsulation(args, def)(e);
    }
    return def.promise;
  }
  BnextJsonRest.inputPassword = inputPassword;
  return BnextJsonRest;
});
