// noinspection JSPotentiallyInvalidUsageOfThis
// noinspection JSPotentiallyInvalidUsageOfThis

/**
 *
 * @deprecated Utilizar bnext/module/Uploader, el nuevo encapsula el componente en un iframe lo cual corrige errores de contexto con el FORM y posición de DOM
 * @param {type} declare
 * @param {type} _WB
 * @param {type} _TM
 * @param {type} tem
 * @param {type} file
 * @param {type} lang
 * @param {type} i18n
 * @param {type} core
 * @param {type} callMethod
 */
define([
  'dojo/_base/declare',
  'dijit/_WidgetBase',
  'dijit/_TemplatedMixin',
  'dojo/has',
  'dojo/text!./templates/FileUploader.html',
  'bnext/_base/file',
  'dojo/_base/array',
  'dojo/_base/lang',
  'bnext/i18n!./nls/FileUploader',
  'bnext/callMethod',
  'xstyle/css!bnext/styles/file.css'
], (declare, _WB, _TM, has, tem, file, array, lang, i18n, callMethod) => {
  let UPLOAD_MAX_SIZE = 2147483647; // Equivale a 2.15GB o Integer.MAX_VALUE
  let TOTAL_UPLOADED_BYTES = 2147483647;
  let LIMIT_FILES_UPLOAD_BYTES = 0;

  function getFileName(fakePath) {
    return fakePath.substr(fakePath.lastIndexOf('\\') + 1);
  }

  function findParentForm(targetNode) {
    let node = targetNode;
    while (node.tagName.toLowerCase() !== 'form') {
      if (node.tagName.toLowerCase() === 'body') {
        throw 'El file uploader no esta encapsulado en una forma.';
      }
      node = node.parentNode;
    }
    return node;
  }
  function getContentType(fileInput) {
    //función utiliza HTML5 para obtener el content-type si no se puede usar regresa vacio
    let contentType = '';
    if (fileInput.files?.[0]) {
      contentType = fileInput.files[0].type;
    }
    return contentType;
  }

  /**
   * Suma el tamaño de todos los archivos.
   * @param fileInput
   * @return {number}
   */
  function getAccumulatedSize(fileInput) {
    let size = 0;
    if (fileInput.files) {
      for (let i = 0; i < fileInput.files.length; i++) {
        const currentFile = fileInput.files[i];
        if (currentFile?.size) {
          size += currentFile.size;
        }
      }
    }
    return size;
  }

  function matchesContentType(accepts, type) {
    if (accepts === null || accepts === undefined || accepts.trim() === '*' || accepts.trim() === '') {
      return true;
    }
    const accepsArr = accepts.split(',').map((e) => e.trim());
    return accepsArr.some((acceptedType) => {
      if (acceptedType === type) {
        return true;
      }
      if (acceptedType.includes('*')) {
        const acceptedRegex = acceptedType.replace('*', '.*');
        return type.match(acceptedRegex);
      }
      return false;
    });
  }

  function getFeedbackErrorMessage(response) {
    let msg;
    if (response.errorMessage === 'DATABASE_FULL') {
      msg = i18n.DATABASE_FULL;
    } else if (response.errorMessage === 'CORRUPT_FILE') {
      msg = i18n.CORRUPT_FILE;
    } else if (response.errorMessage === 'SIZE_INVALID') {
      msg = i18n.SIZE_INVALID;
    } else if (response.errorMessage === 'TOTAL_SIZE_EXCEEDED') {
      msg = i18n.TOTAL_SIZE_EXCEEDED;
    } else {
      msg = i18n.failedUpload || '';
    }
    return msg;
  }

  function sendFeedback(response) {
    required(
      ['core'],
      lang.hitch(this, (core) => {
        const msg = getFeedbackErrorMessage(response);
        core.hideLoader(i18n.sendingFeedack);
        callMethod({
          url: 'Feedback.action',
          method: 'send',
          params: [msg, response.jsonEntityData.error || '']
        }).then(
          (r) => {
            core.hideLoader();
            if (r.operationEstatus) {
              core.asyncDialog(i18n.successFeedbackMail, core.i18n.accept_label);
            } else {
              core.asyncDialog(i18n.failFeedbackMail, core.i18n.accept_label);
              console.log(r);
            }
          },
          (r) => {
            core.hideLoader();
            core.asyncDialog(i18n.failFeedbackMail, core.i18n.accept_label);
            console.log(r);
          }
        );
      })
    );
  }

  let FileUploader = {
    templateString: tem,
    file: {
      id: -1,
      description: '',
      lblfile: i18n.lblFile
    },
    accept: '',
    uploadPath: '',
    i18n: i18n,
    imageWith: '',
    imageHeight: '',
    multiple: false,
    fullScreenButton: false,
    likeFabButton: false,
    postCreate: function () {
      file.stylize(this.fileInput);

      this.onReady();
      if (this.fullScreenButton) {
        this.fileInput.style.width = '100%';
        this.lblFile.style.width = '100%';
      }
    },
    onReady: function () {
      this._varFilesStorageSizes();
      this._valitadeMultiple();
      if (this.likeFabButton) {
        this.lblFile.style['background-position-x'] = '50%';
        this.lblFile.style['background-position-y'] = '50%';
      }
    },
    onFileSelected: function (evt) {
      try {
        let val = '';
        if (evt) {
          //si se selecciono nuevo archivo
          val = this.fileInput.value;
          val = getFileName(val);
          if (val.includes('/')) {
            require(['core'], lang.hitch(this, function (core) {
              core.info(i18n.invalidName);
              if (this.fileInput !== null && this.fileInput !== undefined) {
                this._cancelUploadFile();
              }
            }));
            return;
          }
          const fileType = getContentType(this.fileInput);
          const fileAccept = this.accept;
          if (!matchesContentType(this.accept, fileType)) {
            require(['core'], lang.hitch(this, function (core) {
              if (fileAccept.includes('image/')) {
                core.info(i18n.invalidImage);
                if (typeof this.fileInput !== 'undefined') {
                  this._cancelUploadFile();
                }
              } else {
                core.info(i18n.invalidFile);
                if (typeof this.fileInput !== 'undefined') {
                  this._cancelUploadFile();
                }
              }
            }));
            return;
          }
          const accumulatedSize = getAccumulatedSize(this.fileInput);
          if (accumulatedSize > 0 && accumulatedSize > UPLOAD_MAX_SIZE) {
            // El request completo sobrepasa el tamaño permitido
            require(['core'], lang.hitch(this, function (core) {
              const message = core.specifiedMessage(i18n.maxSizeExceeded, 'maxSize', (UPLOAD_MAX_SIZE / 1024 / 1024).toFixed(0));
              core.info(message);
              if (this.fileInput !== null && this.fileInput !== undefined) {
                this._cancelUploadFile();
              }
            }));
            return;
          }
          if (LIMIT_FILES_UPLOAD_BYTES > 0) {
            const sumUploaded = accumulatedSize + TOTAL_UPLOADED_BYTES;
            if (sumUploaded > LIMIT_FILES_UPLOAD_BYTES) {
              require(['core'], lang.hitch(this, function (core) {
                const message = i18n.TOTAL_SIZE_EXCEEDED;
                core.info(message);
                if (this.fileInput !== null && this.fileInput !== undefined) {
                  this._cancelUploadFile();
                }
              }));
              return;
            }
          }
          if (!this.form) {
            this.form = findParentForm(this.domNode);
          }
          if (val !== '') {
            this._hasValidSession().then(
              lang.hitch(this, function () {
                this._uploadFile();
              }),
              lang.hitch(this, function (e) {
                console.error('Failed to check session for upload file', e);
                this._cancelUploadFile();
                this._failedUploadMessage(i18n.invalidSession, {});
              })
            );
          } else {
            this._cancelUploadFile();
            this._failedUploadMessage(i18n.noFileLabel, {});
          }
        }
      } catch (e) {
        console.error('Error en onFileSelected', e);
        this._failedUploadMessage(i18n.unknownErrorSelectingFile, {});
      }
    },
    _hasValidSession: function () {
      this.showLoader();
      window.skipSessionExpiredAction = true;
      return callMethod({
        url: '../DPMS/Generic.action',
        method: 'hasValidSession',
        params: []
      });
    },
    _varFilesStorageSizes: () => {
      callMethod({
        url: '../DPMS/Files-Storage.action',
        method: 'validationFilesStorageSizes',
        params: []
      }).then((map) => {
        if (map) {
          UPLOAD_MAX_SIZE = map.filesSizeMaxUpload;
          TOTAL_UPLOADED_BYTES = map.uploadedFilesStorage;
          LIMIT_FILES_UPLOAD_BYTES = map.filesStorageLimit;
        }
      });
    },
    _valitadeMultiple: function () {
      if (this.multiple) {
        const fileInput = this.fileInput;
        require(['dojo/dom-attr'], lang.hitch(this, (domAttr) => {
          domAttr.set(fileInput, 'multiple', 'multiple');
        }));
      }
    },
    _uploadFile: function () {
      try {
        window.skipSessionExpiredAction = false;
        this.showLoader();
        this.onBeforeUpload();
        const originalData = {
          target: this.form.target,
          action: this.form.action,
          enctype: this.form.enctype,
          encoding: this.form.encoding
        };
        this.form.enctype = 'multipart/form-data';
        this.form.encoding = 'multipart/form-data'; //Cosa mágica para ie8
        const formData = new FormData(this.form);
        fetch(this.uploadPath || 'Upload.fileUploader', {
          method: 'POST',
          body: formData,
          headers: {
            Accept: 'application/json'
          }
        }).then(
          lang.hitch(this, function (httpResponse) {
            if (!httpResponse || !httpResponse.ok) {
              console.error('Response not ok in upload file', httpResponse);
              this._failedUploadMessage(i18n.failedUpload, originalData);
              return;
            }
            httpResponse.json().then(
              lang.hitch(this, function (responseData) {
                this._onSucccesUploadAction(responseData, originalData);
              }),
              lang.hitch(this, function (f) {
                console.error('Failed to parse text response', f);
                this._failedUploadMessage(i18n.failedUpload, originalData);
              })
            );
          }),
          lang.hitch(this, function (f) {
            console.error('Failed upload file', f);
            this._failedUploadMessage(i18n.failedUpload, originalData);
          })
        );
      } catch (e) {
        console.error('Failed to upload file', e);
        this._failedUploadMessage(i18n.failedUpload, originalData);
      }
    },
    _onSucccesUploadAction: function (responseData, originalData) {
      try {
        if (responseData.length === 0) {
          console.error('Failed to parse upload file response', responseData);
          this._failedUploadMessage(i18n.failedUpload, originalData);
          return;
        }
        let success = true;
        array.forEach(
          responseData,
          lang.hitch(this, function (f) {
            if (f.operationEstatus) {
              if (!this.multiple) {
                this.file = f.jsonEntityData;
              }
              // si contentype fue devuelto por el servidor quedarse la informacion del servidor
              f.jsonEntityData.contentType = f.jsonEntityData.contentType || getContentType(this.fileInput);
              if (f.jsonEntityData?.lastModifiedBy) {
                f.jsonEntityData.lastModifiedBy = undefined;
              }
            } else {
              this.onFailedUpload(f);
              success = false;
              if (f.errorMessage === 'SIZE_INVALID') {
                require(['core'], lang.hitch(this, function (core) {
                  const msg = getFeedbackErrorMessage(f);
                  if (f.errorMessage === 'MAX_SIZE_EXCEEDED') {
                    const message = core.specifiedMessage(i18n.maxSizeExceeded, 'maxSize', (UPLOAD_MAX_SIZE / 1024 / 1024).toFixed(0));
                    core.info(message);
                    return;
                  }
                  const errDialog = core.eventedDialog(msg, [core.i18n.accept_label, null]);
                  errDialog.then(
                    lang.hitch(this, function (dialog) {
                      dialog.event(
                        'clickBtn1',
                        lang.hitch(this, () => {
                          dialog.hide();
                        })
                      );
                    })
                  );
                }));
              } else if (f.errorMessage === 'TOTAL_SIZE_EXCEEDED') {
                require(['core'], lang.hitch(this, function (core) {
                  const msg = getFeedbackErrorMessage(f);
                  const errDialog = core.eventedDialog(msg, [core.i18n.accept_label, null]);
                  errDialog.then(
                    lang.hitch(this, function (dialog) {
                      dialog.event(
                        'clickBtn1',
                        lang.hitch(this, () => {
                          dialog.hide();
                        })
                      );
                    })
                  );
                }));
              } else {
                require(['core'], lang.hitch(this, function (core) {
                  const msg = getFeedbackErrorMessage(f);
                  const errDialog = core.eventedDialog(msg, [core.i18n.accept_label, i18n.sendFeedback]);
                  errDialog.then(
                    lang.hitch(this, function (dialog) {
                      dialog.event(
                        'clickBtn1',
                        lang.hitch(this, () => {
                          dialog.hide();
                        })
                      );
                      dialog.event(
                        'clickBtn2',
                        lang.hitch(this, function () {
                          dialog.hide();
                          this._sendFeedback(f);
                        })
                      );
                    })
                  );
                }));
              }
            }
          })
        );
        if (success) {
          this.form.target = originalData.target;
          this.form.action = originalData.action;
          this.form.enctype = originalData.enctype;
          if (originalData.encoding) {
            this.form.encoding = originalData.encoding;
          }
          if (this.multiple) {
            this.file = responseData;
            this.onChange(responseData);
          } else if (Array.isArray(responseData)) {
            this.onChange(responseData[0].jsonEntityData);
          } else {
            this.onChange(responseData.jsonEntityData);
          }
          this.hideLoader();
        } else {
          this._errorUpload(originalData);
        }
      } catch (e) {
        console.error('Failed to parse upload file response', e, responseData);
        this._failedUploadMessage(i18n.failedUpload, originalData);
      }
    },
    _failedUploadMessage: function (errorMessage, originalData) {
      require(['core'], lang.hitch(this, (core) => {
        core.error(errorMessage, core.i18n.accept_label);
      }));
      this._errorUpload(originalData);
      this.onFailedUpload(null);
      this.hideLoader();
    },
    _errorUpload: function (originalData) {
      if (originalData) {
        this.form.target = originalData.target;
        this.form.action = originalData.target;
        this.form.enctype = originalData.enctype;
        if (originalData.encoding) {
          this.form.encoding = originalData.encoding;
        }
      }
      this._cancelUploadFile();
      this.hideLoader();
    },
    _onDelete: function () {
      this._cancelUploadFile();
    },
    _sendFeedback: sendFeedback,
    _cancelUploadFile: function () {
      this.fileInput.value = '';
    },
    onDelete: function () {
      this._onDelete();
    },
    showLoader: () => {},
    hideLoader: () => {},
    onChange: () => {},
    onBeforeUpload: () => {},
    onFailedUpload: () => {}
  };
  FileUploader = declare([_WB, _TM], FileUploader);
  FileUploader.sendFeedback = sendFeedback;
  return FileUploader;
});
