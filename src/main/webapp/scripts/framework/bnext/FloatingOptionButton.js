define([
    'core',
    'dojo/_base/declare',
    'dijit/_WidgetBase',
    'dijit/_TemplatedMixin',
    'dijit/_WidgetsInTemplateMixin',
    'dojo/text!./templates/FloatingOptionButton.html',
    'dojo/dom-class',
    'dojo/dom-construct',
    'dojo/on',
    'xstyle/css!./styles/FloatingOptionButton.css'
], function (core, declare, WB, TM, WT, template, domClass, domConstruct, on) {
    return declare([WB, TM, WT], {
        templateString: template,
        icon:'add',
        text: '',
        onSelected: function (e) {},
        postCreate: function () {
            on(this.optionButton, 'click', this.onSelected);
            this.optionText.innerHTML = this.text;
            this.optionIcon.innerHTML = this.icon;
        },
        onclick: function(e) {
            alert(e);
        }
    })
})