define(['dojo/on', 'dojo/Deferred', 'bnext/angularElements', 'bnext/survey/_util/jsFailureExposure!', 'dojo/domReady!'], (on, Deferred, angularElements) => {
  const topContext = window.top.window;
  topContext.waitForGeolocation = false;
  topContext.waitingForGeolocationDefs = false;

  if (!angularElements.isElementsAvailable()) {
    console.error('angularGeolocation called in a window without Angular');
    return {};
  }
  angularElements.killGeolocation();

  function resolveWaitForGeolocation(success, result) {
    topContext.waitForGeolocation = false;
    if (topContext.waitingForGeolocationDefs && topContext.waitingForGeolocationDefs.length > 0) {
      for (const waiting of topContext.waitingForGeolocationDefs) {
        if (!waiting || waiting.isFulfilled()) {
          return;
        }
        if (success) {
          waiting.resolve(result);
        } else {
          waiting.reject(result);
        }
      }
    }
    topContext.waitingForGeolocationDefs = false;
  }
  function addWaitForGeolocationDef(waitingDef) {
    topContext.waitForGeolocation = true;
    if (!topContext.waitingForGeolocationDefs) {
      topContext.waitingForGeolocationDefs = [];
    }
    topContext.waitingForGeolocationDefs.push(waitingDef);
  }
  function resetElementData(elementNode) {
    elementNode.isBusy = false;
    elementNode.execute = false;
  }

  const angularGeolocation = {
    findGeolocation: () => {
      const def = new Deferred();
      angularElements.geolocation().then(
        (element) => {
          try {
            addWaitForGeolocationDef(def);
            const elementNode = element.domNode;
            if (elementNode) {
              if (elementNode.isBusy) {
                console.warn('angularGeolocation is busy');
                return;
              }
              on.once(elementNode, 'failed', () => {
                resetElementData(elementNode);
                angularElements.killGeolocation();
                resolveWaitForGeolocation(false, null);
              });
              on.once(elementNode, 'executed', (result) => {
                resetElementData(elementNode);
                angularElements.killGeolocation();
                if (result?.detail) {
                  resolveWaitForGeolocation(true, result.detail);
                } else {
                  resolveWaitForGeolocation(false, null);
                }
              });
              elementNode.isBusy = true;
              elementNode.execute = true;
            } else {
              console.error('angularGeolocation element is not available');
              resolveWaitForGeolocation(false, null);
            }
          } catch (e) {
            console.error('angularGeolocation error:', e);
            resolveWaitForGeolocation(false, null);
          }
        },
        () => {
          console.error('angularGeolocation element is not available');
          resolveWaitForGeolocation(false, null);
        }
      );
      return def.promise;
    }
  };

  return angularGeolocation;
});
