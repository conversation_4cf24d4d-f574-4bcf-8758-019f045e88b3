define(['dojo/on', 'dojo/Deferred', 'bnext/angularElements', 'bnext/survey/_util/jsFailureExposure!', 'dojo/domReady!'], (on, Deferred, angularElements) => {
  if (!angularElements.isElementsAvailable()) {
    console.error('angularGeolocation called in a window without Angular');
    return {};
  }
  angularElements.killGeolocation();

  function resetElementData(elementNode) {
    elementNode.isBusy = false;
    elementNode.execute = false;
  }

  const angularGeolocation = {
    findGeolocation: () => {
      const def = new Deferred();
      angularElements.geolocation().then(
        (element) => {
          try {
            const elementNode = element.domNode;
            if (elementNode) {
              if (elementNode.isBusy) {
                console.warn('angularGeolocation is busy');
                def.reject(null);
                return;
              }
              on.once(elementNode, 'failed', () => {
                resetElementData(elementNode);
                angularElements.killGeolocation();
                def.reject(null);
              });
              on.once(elementNode, 'executed', (result) => {
                resetElementData(elementNode);
                angularElements.killGeolocation();
                if (result?.detail) {
                  def.resolve(result.detail);
                } else {
                  def.resolve(null);
                }
              });
              elementNode.isBusy = true;
              elementNode.execute = true;
            } else {
              console.error('angularGeolocation element is not available');
              def.reject(null);
            }
          } catch (e) {
            console.error('angularGeolocation error:', e);
            def.reject(null);
          }
        },
        () => {
          console.error('angularGeolocation element is not available');
          def.reject(null);
        }
      );
      return def.promise;
    }
  };

  return angularGeolocation;
});
