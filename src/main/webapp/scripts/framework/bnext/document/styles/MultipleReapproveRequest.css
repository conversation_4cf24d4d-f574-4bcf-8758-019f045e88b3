
.MultipleReapproveRequestDialog .dijitDialogPaneContent {
    width: 45.625rem!important;
}
.MultipleReapproveRequestDialog .dijitDialogPaneContent .grid-floating-active {
    margin: 0 auto;
}

.MultipleReapproveRequestDialog .dijitDialog {
    background: #F8F8F8!important; 
}
.MultipleReapproveRequestDialog ul.content_area > li {
    width: 100%;
}
.MultipleReapproveRequestDialog div.reasonAreaContainer {
    display: block;
    width: auto!important;
    margin-left: 7.5rem;
    height: 5.625rem; 
}
.MultipleReapproveRequestDialog div.reasonAreaContainer textarea {
    display: inline-block;
    width: 95%!important;
    float: none;
    max-height: 100%;
    height: 100%!important;
    vertical-align: top;
}
.MultipleReapproveRequestDialog .rightLinkedGrid div.info, .rightLinkedGridWithTitles div.info {
    width: auto;
}
.bnext .MultipleReapproveRequestDialog.fixedTop.fullSizeScreen .dijitDialogPaneContent .gridExpandable.grid-component-container {
    max-width: 34.125rem;
    max-height: 25rem !important;
    overflow-y: auto;
    padding-bottom: 0px;
}
.bnext .MultipleReapproveRequestDialog.fixedTop.fullSizeScreen .textarea-component #reason {
    max-width: 53.125rem;
}
.MultipleReapproveRequestDialog .warnDialog {
    width: 100%;
}
.MultipleReapproveRequestDialog .content_area label {
    width: 7.813rem;
}
.bnext .dijitDialog.fixedTop.MultipleReapproveRequestDialog div[data-dojo-attach-point="titleBar"] {
    display: block;
}