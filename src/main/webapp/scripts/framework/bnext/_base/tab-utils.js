define([],
function () {
    var cryptoUtils = window.crypto || window.msCrypto;
    function isNull(value) {
        return typeof value === 'undefined' || value === null;
    }
    function generateUuid() {
        return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, function(c) {
            return (c ^ cryptoUtils.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16);
        });
    }
    function generateNewTabId() {
        var tabId = generateUuid().replace(/-/g, '');
        sessionStorage.setItem('tabId', tabId);
        return tabId;        
        
    }
    var TabUtils = {};
    TabUtils.getTabId = function() {
        var tabId = sessionStorage.getItem('tabId');
        if (!isNull(tabId)) {
            return tabId;
        }
        tabId = generateNewTabId();
        return tabId;

    };
    return TabUtils;
});