// Actualizar también en src/styles/mediaQueries.scss
define(['dojo/on', 'dojo/Evented', 'dojo/_base/declare', 'bnext/_base/debounce-queue'], (on, Evented, declare, DebounceQueue) => {
  const DEBOUNCE_TIMEOUT = 350;
  const MEDIA_QUERY_CONFIG = {
    X_SMALL_WIDTH_BREAKPOINT: '20em', // maxWidth: 320px
    PHONE_WIDTH_BREAKPOINT: '26.5625em', // maxWidth: 425px
    SMALL_WIDTH_BREAKPOINT: '39.9375em', // maxWidth: 639px
    MEDIUM_WIDTH_BREAKPOINT: '40em', // minWidth: 640px
    LARGE_WIDTH_BREAKPOINT: '64em', // minWidth: 1024px
    X_LARGE_WIDTH_BREAKPOINT: '80em', // minWidth: 1280px
    XX_LARGE_WIDTH_BREAKPOINT: '100em', // minWidth: 1600px
    FHD_WIDTH_BREAKPOINT: '120em' // minWidth: 1920px
  };
  const MediaQueries = declare([Evented], {
    windowInnerWidth: null,
    windowInnerHeight: null,
    _widthCache: null,
    _resizePageEvent: null,
    _mediaMatcher: {
      fhd: null,
      xxLarge: null,
      xLarge: null,
      large: null,
      medium: null,
      small: null,
      phone: null,
      landscape: null
    },
    updateMatchMedia: function () {
      this._mediaMatcher = {
        fhd: matchMedia(`(min-width: ${MEDIA_QUERY_CONFIG.FHD_WIDTH_BREAKPOINT})`),
        xxLarge: matchMedia(`(min-width: ${MEDIA_QUERY_CONFIG.XX_LARGE_WIDTH_BREAKPOINT})`),
        xLarge: matchMedia(`(min-width: ${MEDIA_QUERY_CONFIG.X_LARGE_WIDTH_BREAKPOINT})`),
        large: matchMedia(`(min-width: ${MEDIA_QUERY_CONFIG.LARGE_WIDTH_BREAKPOINT})`),
        medium: matchMedia(`(min-width: ${MEDIA_QUERY_CONFIG.MEDIUM_WIDTH_BREAKPOINT})`),
        small: matchMedia(`(max-width: ${MEDIA_QUERY_CONFIG.SMALL_WIDTH_BREAKPOINT})`),
        phone: matchMedia(`(max-width: ${MEDIA_QUERY_CONFIG.PHONE_WIDTH_BREAKPOINT})`),
        landscape: matchMedia(
          `(min-width: ${MEDIA_QUERY_CONFIG.X_SMALL_WIDTH_BREAKPOINT}) and (max-width:${MEDIA_QUERY_CONFIG.LARGE_WIDTH_BREAKPOINT}) and (orientation: landscape) and (hover: none) and (pointer: coarse)`
        )
      };
    },
    isScreenFhd: function () {
      return this._mediaMatcher.fhd.matches;
    },
    isScreenXLarge: function () {
      return this._mediaMatcher.xLarge.matches;
    },
    isScreenXXLarge: function () {
      return this._mediaMatcher.xxLarge.matches;
    },
    isScreenLarge: function () {
      return this._mediaMatcher.large.matches;
    },
    isScreenMedium: function () {
      return this._mediaMatcher.medium.matches;
    },
    isScreenSmall: function () {
      return this._mediaMatcher.small.matches;
    },
    isScreenPhone: function () {
      return this._mediaMatcher.phone.matches;
    },
    isLandscape: function () {
      return this._mediaMatcher.landscape.matches;
    },
    isDesktop: function () {
      return !this.isScreenSmall() && !this.isScreenPhone() && !this.isLandscape();
    },
    isMobilDevice: function () {
      return this.isAndroidDevice() || this.isIPhoneDevice();
    },
    isAndroid: () => navigator.userAgent.toLowerCase().indexOf('android') > -1,
    isAndroidDevice: function () {
      return this.isSmallTouchDevice() && this.isAndroid();
    },
    isIPhoneDevice: function () {
      return this.isSmallTouchDevice() && this.isIPhone();
    },
    isIPhone: () => {
      return navigator.userAgent.toLowerCase().indexOf('iphone') > -1 || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    },
    isSmallTouchDevice: function () {
      return this.isTouchDevice() && (this.isScreenSmall() || this.isLandscape());
    },
    isTouchDevice: () => {
      try {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0;
      } catch (e) {
        return false;
      }
    },
    getWindowInnerWidth: () => (window.innerWidth > 0 ? window.innerWidth : screen.width),
    getWindowInnerHeight: () => (window.innerHeight > 0 ? window.innerHeight : screen.height),
    recalculateSizeMatchers: function (event) {
      this.windowInnerWidth = this.getWindowInnerWidth();
      this.windowInnerHeight = this.getWindowInnerHeight();
      if (event && this._widthCache === this.windowInnerWidth) {
        return;
      }
      this._widthCache = this.windowInnerWidth;
      this.updateMatchMedia();
    },
    _setEvents: function () {
      this._dbResize = new DebounceQueue(DEBOUNCE_TIMEOUT, this);
      this._resizePageEvent = on(window, 'resize, orientationChange, fullscreenchange', () => {
        this._dbResize.add(() => {
          this.recalculateSizeMatchers();
          this.emit('resized', {
            bubbles: false,
            cancelable: true
          });
        });
      });
    },
    constructor: function () {
      this._setEvents();
      this.recalculateSizeMatchers();
    }
  });
  const mediaInstance = new MediaQueries();
  return mediaInstance;
});
