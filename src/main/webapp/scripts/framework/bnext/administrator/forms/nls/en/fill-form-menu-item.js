define({
    label_NEW_REQUEST: 'New request',
    title_NEW_REQUEST: 'Click to start a new request',
    label_OPEN_REQUESTOR_BLANK: 'Open in another window',
    title_OPEN_REQUESTOR_BLANK: 'Click to open in another window',
    label_PREVIEW: 'Preview',
    title_PREVIEW: 'Click to open the preview',
    label_EDIT_REQUEST: 'Continue request',
    title_EDIT_REQUEST: 'Click to continue a previous request',
    label_IMPERSONATE: 'Impersonate',
    title_IMPERSONATE: 'Click to start a new request impersonating other user',
    label_FREEZE_ANSWERS: 'Freeze answers',
    title_FREEZE_ANSWERS: 'Click to start the freeze answer process',
    label_COPY_LINK: 'Copy link',
    title_COPY_LINK: 'Click link to copy to clipboard',
    label_FAVORITE: 'Add favorites',
    title_FAVORITE: 'Click to add to favorites',
    label_COPY_DIRECTED_LINK: 'Copy directed link',
    title_COPY_DIRECTED_LINK: 'Click link to copy to clipboard'
});