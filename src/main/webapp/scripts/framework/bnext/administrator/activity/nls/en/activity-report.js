define({
    'text: #window_title': 'Activities reports',
    'text: #lblReporte': 'Report',
    'text: #implementActivities': 'Activities to attend',
    'text: #lblPlant': '{Facility}',
    'text: #lblImplementer': 'Responsible',
    'text: #lblDateStart': 'From implementation date',
    'text: #lblDateEnd': 'To implementation date',
    'text: #lblFormat': 'Format',
    'text: #lblModule': 'Module',
    'text: option[value="activity"]': 'Activities',
    'text: option[value="audit"]': 'Audits',
    'text: option[value="action"]': 'Findings',
    'text: option[value="meeting"]': 'Meetings',
    'text: #optionPDF': 'PDF',
    'text: #optionHTML': 'HTML',
    'text: #optionEXCEL': 'EXCEL',
    'value: #btnGenerate': 'Generate report',
    'text: #implementerId option.fce': '-- All --',
    selectAll: '-- All --',
    validateSiteURL: 'The URL setting added in advanced settings is not correct.',
    validate_plant: '{TheFacility} field is required.',
    validate_date: 'You must select a date range.',
    validate_module: 'Please select the module related to the activities.'
});