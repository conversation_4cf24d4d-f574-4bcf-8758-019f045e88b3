define(['bnext/i18n!lang/nls/main'], function (main) {
    return {
        root: {
            'text: #window_title': 'Reportes de actividades',
            'text: #lblReporte': 'Reporte',
            'text: #implementActivities': 'Actividades por atender',
            'text: #lblPlant': '{Facility}',
            'text: #lblImplementer': 'Responsable',
            'text: #lblDateStart': 'Fecha de la implementación desde',
            'text: #lblDateEnd': 'Fecha de la implementación hasta',
            'text: #lblFormat': 'Formato',
            'text: #lblModule': 'Módulo',
            'text: option[value=""]': main.firstComboElement,
            'text: option[value="activity"]': 'Actividades',
            'text: option[value="audit"]': 'Auditorías',
            'text: option[value="action"]': 'Hallazgos',
            'text: option[value="meeting"]': 'Reuniones',
            'text: #optionPDF': 'PDF',
            'text: #optionHTML': 'HTML',
            'text: #optionEXCEL': 'EXCEL',
            'value: #btnGenerate': 'Generar reporte',
            'text: #implementerId option.fce': '-- Todos --',
            selectAll: '-- Todos --',
            validateSiteURL: 'La configuración URL agregada en opciones avanzadas no es la correcta.',
            validate_plant: 'El campo {facility} es requerido.',
            validate_date: 'Debe seleccionar un rango de fecha.',
            validate_module: 'Debe seleccionar el módulo al que pertenecen las actividades.'
        },
        en: true
    };
});