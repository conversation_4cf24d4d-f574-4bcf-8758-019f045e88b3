define({
    menu_immediateBoss: 'Scalability hierarchy',
    menu_profile: 'Profiles',
    menuPdfConversion: 'PDF conversion',
    menuPdfExternalProcess: 'External process',
    menuPdfFiles: 'PDF files',
    menuPdfConversionLog: 'History',
    menu_diagram: 'Organizational hierarchy',
    pendingRecords: 'Task summary',
    menuDatabaseConnection: 'Connections',
    menuDocumentToRenew: 'Documents to renew',
    menuDatabaseQuery: 'Queries',
    menuFormReports: 'Reports',
    auditIndividualList: 'Records control',
    editMyUser: 'My account',
    editPassword: 'Edit password',
    editUserData: 'Edit data'
});