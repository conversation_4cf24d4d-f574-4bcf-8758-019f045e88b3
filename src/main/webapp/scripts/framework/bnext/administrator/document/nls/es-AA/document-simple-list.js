define(['bnext/i18n!lang/nls/main', 'bnext/i18n!bnext/administrator/document/nls/document-module'], (main, documentModule) => ({
  status_invalid: 'Nuevo',
  status_active: 'Activo',
  status_in_edition: 'En edición',
  commonTitleReapr: 'Re-aprobar documento',
  commonHandleFormRequest_title: 'Solicitud a un formulario',
  commonHandleFormRequest_sureMessage: '¿Seguro de que desea realizar una solicitud de "%type%" un formulario?<br>Será enviado a una pantalla diferente.',
  commonTitleCancel: 'Cancelar documento',
  messagesShouldClose: '¿Está seguro de que desea cancelar esta solicitud?',
  buttonsAccept: main.accept,
  buttonsCancel: main.cancel,
  colNameStatus: 'Estado',
  colNameEdit: 'Visor Doc.',
  colNameModify: 'Modificar',
  colNameCancel: 'Cancelar',
  colNameApproval: 'Re-aprobar',
  colNameDetail: 'Detalle',
  colNameCode: 'Clave',
  colNameDescription: 'Documento',
  columnBusinessUnit: '{Facility}',
  columnDepartment: '{Department}',
  colNamesAuthor: documentModule.versionAuthor,
  colNameCreationDate: 'Fecha entrada',
  colNameLastModificationDate: 'Fecha aprobación',
  colNameExpirationDate: 'Fecha vencimiento',
  colNamesNodePath: 'Ruta',
  colNameDescFile: 'Archivo',
  colNamesDocumentType: 'Tipo de documento',
  colNamesRetention: 'Tiempo de retención',
  colNamesVersion: documentModule.version,
  span_1: 'Dia(s)',
  span_2: 'Mes(es)',
  span_3: 'Año(s)',
  colNameShareLink: 'Compartir',
  colNameExternalLink: 'Abrir',
  shareLink: 'Compartir enlace a documento',
  externalLink: 'Abrir'
}));
