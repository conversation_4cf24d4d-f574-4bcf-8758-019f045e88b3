/**
 * Utilizar solo cuando el entity de documentos a manejar sea "DocumentSimple.java".
 *
 * @param {type} core
 * @param {type} domClass
 * @param {type} registry
 * @param {type} saveRequestHandle
 * @param {type} DynamicFieldsHandler
 * @param {type} gridIcons
 * @param {type} all
 * @param {type} i18n
 * @param {type} lang
 * @param {type} domConstruct
 * @param {type} gridCubes
 * @param {type} gridComponent
 * @param {type} gcUtil
 * @param {type} dom
 * @param {type} array
 * @param {type} Deferred
 * @param {type} callMethod
 * @param {type} ready
 * @returns {Function}
 */
define([
  'loader',
  'dijit/Dialog',
  'bnext/extensionIcons',
  'bnext/administrator/solicitudes/saveRequestHandle',
  'dojo/dom-class',
  'bnext/document/DynamicFieldsHandler',
  'dojo/promise/all',
  'bnext/i18n!bnext/administrator/document/nls/document-simple-list',
  'dojo/_base/lang',
  'dojo/dom-construct',
  'bnext/gridCubes',
  'bnext/gridComponentUtil',
  'dojo/dom',
  'dojo/_base/array',
  'dojo/Deferred',
  'bnext/GridFactory',
  'bnext/i18n!bnext/administrator/document/nls/document-module',
  'bnext/module/grid-menu',
  './document-menu-item',
  'bnext/module/grid-menu-util',
  'dijit/popup',
  'dijit/TooltipDialog',
  'bnext/callMethod',
  'dojo/json',
  'dojo/domReady!',
  'xstyle/css!bnext/administrator/document/style/document-simple-list.css'
], (
  loader,
  Dialog,
  extensionIcons,
  saveRequestHandle,
  domClass,
  DynamicFieldsHandler,
  all,
  i18n,
  lang,
  domConstruct,
  gridCubes,
  gcUtil,
  dom,
  array,
  Deferred,
  GridFactory,
  documentModule,
  GridMenu,
  DocumentMenuItem,
  GridMenuUtil,
  popup,
  TooltipDialog,
  callMethod,
  dojoJson
) =>
  (gridArgsParam, editActionParam, statusActionParam) => {
    let gridArgs = gridArgsParam;
    const editAction = editActionParam || false;
    const statusAction = statusActionParam || (() => {});
    const storageList = [];
    const dispositionList = [];
    const informationClassificationList = [];
    let docComponent;
    let multipleReapproveComponent;
    let saveRequestHandleNEW;
    let dialog;
    let gridMenu = null;
    const menuItems = [];
    const linkConfig = loadLinkConfig();
    function initializeDialog(dial) {
      let d;
      if (!dial) {
        d = new Dialog({ autofocus: false });
      } else {
        d = dial;
      }
      return d;
    }
    function getStoragePlace() {
      const def = new Deferred();
      callMethod({
        url: '../DPMS/Document.action',
        method: 'getStoragePlaceList',
        params: []
      }).then(
        (resultado) => {
          array.forEach(resultado, (entry, i) => {
            const storage = {
              id: entry.value,
              description: entry.text
            };
            storageList.push(storage);
          });
          def.resolve();
        },
        () => {
          loader.hideLoader();
          def.resolve();
        }
      );
      return def.promise;
    }
    function getInformationClassification() {
      const def = new Deferred();
      callMethod({
        url: '../DPMS/Document.action',
        method: 'getInformationClassificationCatalog',
        params: []
      }).then(
        (resultado) => {
          array.forEach(resultado, (entry, i) => {
            const inf = {
              id: entry.value,
              description: entry.text
            };
            informationClassificationList.push(inf);
          });
          def.resolve();
        },
        () => {
          loader.hideLoader();
          def.resolve();
        }
      );
      return def.promise;
    }
    function getDisposition() {
      const def = new Deferred();
      callMethod({
        url: '../DPMS/Document.action',
        method: 'getDispositionCatalog',
        params: [true]
      }).then(
        (resultado) => {
          array.forEach(resultado, (entry, i) => {
            const d = {
              id: entry.value,
              description: entry.text
            };
            dispositionList.push(d);
          });
          def.resolve();
        },
        () => {
          loader.hideLoader();
          def.resolve();
        }
      );
      return def.promise;
    }
    const cType = gcUtil.cType;
    const columns = [];
    const statusList = [
      { name: i18n.status_active, value: 1, icon: gridCubes.green },
      { name: i18n.status_in_edition, value: 0, icon: gridCubes.red }
    ];
    function prepareColumns() {
      // Se definen las funciones a utilizar en las acciones del Grid
      const typeRequestLabel = {
        edit: i18n.colNameModify,
        cancel: i18n.colNameCancel
      }; // variables utilizadas para los request
      function edit(type, documento) {
        loader.showLoader().then(() => {
          require(['bnext/module/DocumentRequest', 'core'], (DocumentRequest, core) => {
            if (docComponent) {
              docComponent.destroyRecursive();
            }
            if (handleFormRequest(type, documento)) {
              return;
            }
            const save = lang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
            dialog = initializeDialog(dialog);
            let requestType;
            if (type === 'edit') {
              requestType = DocumentRequest.update;
            } else if (type === 'editDetails') {
              requestType = DocumentRequest.editDetails;
            } else {
              requestType = null;
            }
            docComponent = new DocumentRequest({
              isAFormRequest: !!documento.surveyId,
              surveyId: documento.surveyId || null,
              enablePdfViewer: documento.enablePdfViewer || 0,
              type: requestType,
              doc: documento,
              externalFileSelect: core.getExternalFileIntegration(),
              onCancel: cancelConfirm,
              onSubmit: (sol, file) => {
                docComponent.showLoader(core.i18n.Validate.saving);
                return [
                  sol.then(
                    (sobj) => {
                      save(sobj.sol, sobj.file, requestType, docComponent);
                    },
                    (e) => {
                      core.error('No se pudo guardar la solicitud.');
                      console.error('Falla al guardar valores dinámicos');
                      console.error(e);
                      docComponent.hideLoader();
                    }
                  )
                ];
              },
              requestDialog: dialog
            });
            DynamicFieldsHandler.REQUEST.MODIFY(docComponent);
            domClass.add(docComponent.unknownCodeDiv, 'displayNone');
            docComponent.set('title', i18n[`commonTitleEdit_${type}`]);
            docComponent.show();
          });
        });
      }
      const handleFormRequest = (type, documento) => {
        if (!documento.surveyId || type === 'editDetails') {
          return false;
        }
        require(['core'], (core) => {
          const ev = core.eventedDialog(
            `${core.specifiedMessage(i18n.commonHandleFormRequest_sureMessage, 'type', typeRequestLabel[type])}`,
            [core.i18n.Validate.yes, core.i18n.Validate.no],
            i18n.commonHandleFormRequest_title
          );
          ev.then((dialog) => {
            dialog.event('clickBtn1', () => {
              loader.showLoader().then(() => {
                require(['bnext/angularNavigator', 'bnext/angularFabMenu'], (angularNavigator, angularFabMenu) => {
                  angularFabMenu.displayFabMenu(false);
                  dialog.hide();
                  const url = `v.request.survey.mode.action?documentId=${documento.id}&id=${documento.surveyId}&viewMode=MODIFY`;
                  angularNavigator.navigateLegacy(url);
                });
              });
            });
            dialog.event('clickBtn2', () => {
              dialog.hide();
            });
          });
        });
        return true;
      };

      function cancel(documento) {
        function go() {
          openCancel(documento);
        }
        const documentId = documento.id;
        if (documentId) {
          callMethod({
            url: '../DPMS/Document.action',
            method: 'isRequestInProcess',
            params: [documentId, 'FILL']
          }).then(
            (result) => {
              if (result) {
                require(['core'], (core) => {
                  core.info(i18n.fillInProcess, core.i18n.yes, core.i18n.no).then(go);
                });
              } else {
                go();
              }
            },
            (e) => {
              require(['core'], (core) => {
                core.error(e).then(go);
              });
            }
          );
        } else {
          go();
        }
      }
      function openCancel(documento) {
        loader.showLoader().then(() => {
          require(['bnext/module/DocumentRequest', 'core'], (DocumentRequest, core) => {
            if (docComponent) {
              docComponent.destroyRecursive();
            }
            const save = lang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
            dialog = initializeDialog(dialog);
            docComponent = new DocumentRequest({
              isAFormRequest: !!documento.surveyId,
              surveyId: documento.surveyId || null,
              enablePdfViewer: document.enablePdfViewer || 0,
              type: DocumentRequest.cancel,
              doc: documento,
              onCancel: cancelConfirm,
              onSubmit: (sol, file) => {
                docComponent.showLoader(core.i18n.Validate.saving);
                return [
                  sol.then(
                    (sobj) => {
                      save(sobj.sol, sobj.file, 4, docComponent);
                    },
                    (e) => {
                      core.error('No se pudo guardar la solicitud.');
                      console.error('Falla al guardar valores dinamicos');
                      console.error(e);
                      docComponent.hideLoader();
                    }
                  )
                ];
              },
              requestDialog: dialog
            });
            DynamicFieldsHandler.REQUEST.CANCEL(docComponent);
            domClass.add(docComponent.unknownCodeDiv, 'displayNone');
            docComponent.set('title', i18n.commonTitleCancel);
            docComponent.show();
          });
        });
      }
      function evaluateCanReApprove(documento) {
        const def = new Deferred();
        loader.showLoader().then(() => {
          callMethod({
            url: '../DPMS/Document.action',
            method: 'evaluateCanReApprove',
            params: [documento.id]
          }).then(
            (result) => {
              if (result) {
                loader.hideLoader();
                def.resolve();
              } else {
                loader.hideLoader();
                require(['core'], (core) => {
                  core.dialog(i18n.reopenInprocess);
                });
                def.reject();
              }
            },
            (e) => {
              loader.hideLoader();
              console.error(e);
              def.reject();
            }
          );
        });
        return def.promise;
      }
      function aprove(documento) {
        loader.showLoader().then(() => {
          require(['bnext/module/DocumentRequest', 'core'], (DocumentRequest, core) => {
            evaluateCanReApprove(documento).then(
              () => {
                if (docComponent) {
                  docComponent.destroyRecursive();
                }
                const save = lang.hitch(saveRequestHandleNEW, saveRequestHandleNEW.save);
                dialog = initializeDialog(dialog);
                docComponent = new DocumentRequest({
                  isAFormRequest: !!documento.surveyId,
                  surveyId: documento.surveyId || null,
                  enablePdfViewer: document.enablePdfViewer || 0,
                  type: DocumentRequest.aprove,
                  doc: documento,
                  onCancel: cancelConfirm,
                  onSubmit: (sol, file) => {
                    docComponent.showLoader(core.i18n.Validate.saving);
                    return [
                      sol.then(
                        (sobj) => {
                          save(sobj.sol, sobj.file, 3, docComponent);
                        },
                        (e) => {
                          core.error('No se pudo guardar la solicitud.');
                          console.error('Falla al guardar valores dinamicos');
                          console.error(e);
                          docComponent.hideLoader();
                        }
                      )
                    ];
                  },
                  requestDialog: dialog
                });
                DynamicFieldsHandler.REQUEST.APROVE(docComponent);
                domClass.add(docComponent.unknownCodeDiv, 'displayNone');
                docComponent.set('title', i18n.commonTitleReapr);
                docComponent.show();
              },
              (e) => {
                console.error(e);
              }
            );
          });
        });
      }
      function multipleAprove(documents) {
        require(['bnext/document/MultipleReapproveRequest'], (MultipleReapproveRequest) => {
          if (multipleReapproveComponent) {
            multipleReapproveComponent.destroyRecursive();
          }
          multipleReapproveComponent = new MultipleReapproveRequest({
            documents: documents,
            grid: saveRequestHandleNEW.grid
          });
        });
      }
      function shareLink(obj) {
        if (+obj.status === 1) {
          shareLinkAction(obj);
        } else {
          require(['core'], (core) => {
            core.warn(core.specifiedMessage(i18n.obsoleteDocument, 'code', obj.code), core.i18n.yes, core.i18n.no, i18n.obsoleteDocumentTitle).then(() => {
              shareLinkAction(obj);
            });
          });
        }
      }
      function shareLinkAction(obj) {
        const hasFile = obj.fileId !== null && typeof obj.fileId !== 'undefined';
        loader.showLoader().then(() => {
          callMethod({
            url: '../DPMS/Document.action',
            method: 'getShareLink',
            params: [obj.id]
          }).then((shareDocument) => {
            if (!shareDocument || !shareDocument.valid) {
              require(['core'], (core) => {
                core.error(i18n.unauthorizedAction);
              });
              return;
            }
            require(['bnext/module/ShareLink'], (ShareLink) => {
              const dialogLink = new Dialog({ autofocus: false, id: 'shareLinkDialog' });
              new ShareLink({
                title: i18n.shareLink,
                shareDocument: shareDocument,
                dialog: dialogLink,
                hasFile: hasFile,
                enablePdfViewer: obj.enablePdfViewer,
                afterOpen: () => {
                  if (typeof gridArgs.beforeShareLinkOpen === 'function') {
                    gridArgs.beforeShareLinkOpen();
                  }
                },
                afterClose: () => {
                  if (typeof gridArgs.beforeShareLinkClose === 'function') {
                    gridArgs.beforeShareLinkClose();
                  }
                }
              });
              loader.hideLoader();
            });
          }, loader.hideLoader);
        });
      }
      function externalLink(idParam) {
        let id;
        if (Object.hasOwn(idParam, 'id')) {
          id = idParam.id;
        } else {
          id = idParam;
        }
        loader.showLoader().then(() => {
          callMethod({
            url: '../DPMS/Document.action',
            method: 'getExternalLink',
            params: [id]
          }).then((url) => {
            if (!url) {
              require(['core'], (core) => {
                core.error(i18n.unauthorizedAction);
              });
              return;
            }
            require(['bnext/module/ExternalLink'], (ExternalLink) => {
              if (dom.byId('showExternalDialog').value === '1') {
                const dialogLink = new Dialog({ autofocus: false });
                new ExternalLink({ dialog: dialogLink, title: i18n.externalLink });
              }
              window.location = `bnext:${url}`;
              loader.hideLoader();
            });
          }, loader.hideLoader);
        });
      }

      const cancelConfirm = () => {
        docComponent.destroyRecursive();
        docComponent.hide();
        return [
          {
            // biome-ignore lint/suspicious/noThenProperty: Fake deferred
            then: (f) => {
              f?.();
            }
          }
        ];
      };
      function loadTypeConfig(row) {
        return linkConfig.documentTypes[row.documentType.id];
      }
      function getMimeType(row) {
        return row.fileContent?.contentType ? row.fileContent.contentType.toLowerCase() : '';
      }
      function validMimeType(mimeType, row, fileTypes) {
        let validMimmeType = false;
        let ext = /[^.]+$/.exec(row.fileContent ? row.fileContent.description : '');
        ext = ext ? ext[0].toLowerCase() : '';
        array.some(fileTypes, (fileType) => {
          if (fileType.code.toLowerCase() === mimeType || fileType.description.toLowerCase() === ext) {
            validMimmeType = true;
            return false;
          }
        });
        return validMimmeType;
      }
      function renderShareLink(row) {
        const type = loadTypeConfig(row);
        if (!type || type.shareDocuments === 0 || row.surveyId) {
          return false;
        }
        const mimeType = getMimeType(row);
        if (type.shareRestrictFileType) {
          const validMimmeType = validMimeType(mimeType, row, type.shareFileTypes);
          if (!validMimmeType) {
            return false;
          }
        }
        return true;
      }
      function renderExternalLink(row) {
        const type = loadTypeConfig(row);
        if (!type || type.externalLink === 0) {
          return false;
        }
        const mimeType = getMimeType(row);
        if (type.externalRestrictFileType) {
          validMimmeType = validMimeType(mimeType, row, type.externalFileTypes);
          if (!validMimmeType) {
            return false;
          }
        }
        return true;
      }
      function renderRetention(obj) {
        let s = '-';
        if (obj.retentionText && obj.retencionTime !== null) {
          s = `${obj.retentionTime} ${i18n[`span_${obj.retentionText}`]}`;
        }
        return s;
      }
      function renderResponsible(row) {
        let s = '-';
        if (!row.collectingAndStoreResponsibleDescription && row.collectingAndStoreResponsible !== null) {
          s = row.collectingAndStoreResponsible.description;
        }
        return row.collectingAndStoreResponsibleDescription || s;
      }
      gridMenu = new GridMenu(
        {
          type: DocumentMenuItem,
          multipleSelection: true
        },
        gridArgs.buttonsSectionId ? gridArgs.buttonsSectionId : null
      );
      // Se agregan las columnas del grid
      gcUtil.column(columns).push('menu', gridMenu.cellHeader(), gridMenu.cellGridType(), null, '65px');
      // Consultar la configuracion del objeto nodo y agregar las columnas segun la agrupacion seleccionada
      if (gridArgs.includeStatus === true) {
        gcUtil.column(columns).push('status', i18n.colNameStatus, cType.FunctionImage(['id', 'description', 'code'], statusAction, statusList, true), null, '40px');
      }

      if (gridArgs.menuItems && gridArgs.menuItems.length > 0) {
        array.forEach(gridArgs.menuItems, (item) => {
          menuItems.push(item);
        });
      }
      GridMenuUtil.menu(menuItems, DocumentMenuItem)
        .push(
          DocumentMenuItem.MODIFY_DOCUMENT,
          (documento) => edit('edit', documento),
          (documento) => documento.status === 1
        )
        .push(
          DocumentMenuItem.EDIT_DETAILS,
          (documento) => edit('editDetails', documento),
          (documento) => documento.status === 1
        )
        .push(DocumentMenuItem.CANCEL_DOCUMENT, cancel, (documento) => documento.status === 1);

      if (gridArgs.reapprove !== 'true' || (gridArgs.reapprove === 'true' && gridArgs.docRole === '3')) {
        GridMenuUtil.menu(menuItems, DocumentMenuItem).push(
          DocumentMenuItem.RE_APPROVE_DOCUMENT,
          aprove,
          (row) => {
            if (row.status === 1) {
              return row.documentType.documentControlledType === 'controlled';
            }
          },
          null,
          null,
          multipleAprove
        );
      }

      if (gridArgs.includeCanShareDocument !== false && linkConfig.canShareDocument) {
        GridMenuUtil.menu(menuItems, DocumentMenuItem).push(DocumentMenuItem.SHARE_DOCUMENT, shareLink, renderShareLink);
      }
      if (gridArgs.includeExternalLink !== false && linkConfig.externalLink) {
        GridMenuUtil.menu(menuItems, DocumentMenuItem).push(DocumentMenuItem.OPEN_DOCUMENT, externalLink, renderExternalLink);
      }
      if (editAction) {
        GridMenuUtil.menu(menuItems, DocumentMenuItem).push(DocumentMenuItem.DETAIL_DOCUMENT, editAction, true, ['id', 'status']);
      }

      if (gridArgs.includeDownloadDocument !== false) {
        GridMenuUtil.menu(menuItems, DocumentMenuItem).push(
          DocumentMenuItem.DOWNLOAD_DOCUMENT,
          (row) => {
            window.location = `../view/v-download-document.view?id=${row.id}`;
          },
          (row) => {
            if (row.enablePdfViewer === 0) {
              return true;
            }
            if (row.documentType.documentControlledType !== 'uncontrolled' || !row.documentType.downloadFiles) {
              return false;
            }
            return true;
          }
        );
      }
      if (gridArgs.includeGoogleDriveExport !== false) {
        const googleDriveExport = new TooltipDialog({
          id: 'google-drive-export',
          class: 'google-drive-export',
          onMouseLeave: () => {
            if (typeof googleDriveExport.externalDestroy === 'function') {
              googleDriveExport.externalDestroy();
            }
            popup.close(googleDriveExport);
          }
        });
        function getExternalFileIntegration() {
          const externalDomConfig = dom.byId('googleDriveIntegration');
          if (externalDomConfig) {
            return +externalDomConfig.value === 1;
          }
          return false;
        }
        if (getExternalFileIntegration() === true) {
          GridMenuUtil.menu(menuItems, DocumentMenuItem)
            .push(
              DocumentMenuItem.GOOGLE_DRIVE_EXPORT,
              (menuItem, obj) => {
                popup.open({
                  popup: googleDriveExport,
                  around: menuItem.domNode
                });
                require(['bnext/third-party-app/google-drive-exporter'], (googleDriveExporter) => {
                  googleDriveExporter.then((exporter) => {
                    if (exporter && typeof exporter.render === 'function') {
                      const url = `../view/v-download-document.view?id=${obj.id}`;
                      exporter.render(googleDriveExport.get('containerNode'), url, obj.fileContent.description, 'Bnext QMS');
                      googleDriveExport.externalDestroy = () => {
                        googleDriveExport.set('containerNode', '');
                      };
                      menuItem.on('menu-item-removed', () => {
                        if (typeof googleDriveExport.externalDestroy === 'function') {
                          googleDriveExport.externalDestroy();
                          popup.close(googleDriveExport);
                        }
                      });
                    } else {
                      require(['core'], (core) => {
                        core.handleScriptLoadError(exporter.error);
                      });
                    }
                  });
                });
              },
              (menuItem, obj) => obj.documentType.documentControlledType === 'uncontrolled' && obj.documentType.downloadFiles,
              ['menu-item']
            )
            .set('contextMenu', false);
        }
      }
      function viewDoc(row, nodeTd, trDom, tableDom) {
        const fileInfo = extensionIcons.getExtensionInfo(row.fileContent, row);
        const viewAnchor = domConstruct.create('a', {
          href: 'javascript:void(0)',
          class: 'edit-rendered-cell',
          onclick: () => {
            callMethod({
              url: 'Document.action',
              method: 'getHasAccess',
              params: [row.id]
            }).then((result) => {
              if (result.operationEstatus === 1) {
                require(['bnext/angularNavigator'], (angularNavigator) => {
                  const viewerUrl = `v-document-viewer.view?id=${row.id}`;
                  angularNavigator.showDocumentViewer(viewerUrl);
                });
              } else {
                require(['core'], (core) => {
                  core.dialog(i18n.notAccesDocument);
                });
              }
            });
          },
          innerHTML: `<img border=0 src="${fileInfo.iconSrc}"/>`
        });

        trDom.title = `${i18n.colNameDescFile}: ${fileInfo.title}`;
        return viewAnchor;
      }

      const listaExtensiones = [];
      for (const ext in extensionIcons) {
        if (ext === 'defaultIcon' || ext === 'form') {
          continue;
        }
        listaExtensiones.push({ id: ext, description: ext });
      }
      const controlledType = cType.TextMap([
        { name: documentModule.documentControlledTypeControlled, value: 'controlled' },
        { name: documentModule.documentControlledTypeUncontrolled, value: 'uncontrolled' }
      ]);
      const colTypeView = cType.RenderCell(viewDoc);
      colTypeView.isSortable = true;
      colTypeView.idValue = 'fileContent.extension';
      gcUtil
        .column(columns)
        .push('view', i18n.colNameEdit, colTypeView, null, '35px')
        .push('code', i18n.colNameCode, cType.Text(), null, '40px')
        .set('disableStatistics', true)
        .push('version', i18n.colNamesVersion, cType.Text(), null, '45px')
        .push('description', i18n.colNameDescription, cType.Text())
        .set('disableStatistics', true)
        .push(
          'docType',
          i18n.colNamesDocumentType,
          cType.DynamicField({
            id: 'documentType',
            serviceName: 'DocumentType.SearchFields.action'
          }),
          null,
          '90px',
          'none'
        )
        .push('documentType.documentControlledType', documentModule.colNameDocumentControlledType, controlledType, null)
        .push('businessUnit.description', i18n.columnBusinessUnit, cType.Text())
        .push(
          'department.description',
          i18n.columnDepartment,
          cType.SelectEntityMultiple({
            id: 'department',
            serviceName: 'Documents.BusinessUnitDepartment.action',
            methodName: 'getDocumentSearchFieldValues',
            skipCode: true
          })
        )
        .push('department.code', i18n.columnDepartmentCode, cType.Text(), null, null, 'none')
        .push('author.description', i18n.colNamesAuthor, cType.Text())
        .push('creationDate', i18n.colNameCreationDate, cType().Date(true))
        .push('lastModificationDate', i18n.colNameLastModificationDate, cType().Date(true))
        .push('expirationDate', i18n.colNameExpirationDate, cType().Date(true))
        .push(
          'enablePdfViewer',
          documentModule.enablePdfViewer,
          cType.TextMap([
            { name: documentModule.enabledPdfViewer, value: 1 },
            { name: documentModule.disabledPdfViewer, value: 0 }
          ]),
          null,
          null,
          'none'
        )
        .push('nodePath.description', i18n.colNamesNodePath, cType.Text(), null, null, 'none')
        .push('tRetencion', i18n.colNamesRetention, cType.RenderCell(renderRetention, null, renderRetention), null, null, 'none')
        .push('storagePlaceId', i18n.colNameStorage, cType.SelectMultiple(storageList), null, null, 'none')
        .push('disposition', i18n.colNamesDisposition, cType.SelectMultiple(dispositionList, null, null, 'long'), null, null, 'none')
        .push('collectingAndStoreResponsible', i18n.colNamesCollectingAndStore, cType.RenderCell(renderResponsible), null, null, 'none')
        .push(
          'informationClassification',
          i18n.colNamesInformationClassification,
          cType.SelectMultiple(informationClassificationList, null, null, 'long'),
          null,
          null,
          'none'
        )
        .push('fileContent.extension', i18n.colNameExt, cType.SelectMultiple(listaExtensiones), null, null, 'none');
    }
    function loadLinkConfig() {
      const canShareDocument = dom.byId('canShareDocument').value === '1';
      const externalLink = dom.byId('externalLink').value === '1';
      const shareFileTypes = dojoJson.parse(dom.byId('serializedShareFileTypes').value);
      const externalFileTypes = dojoJson.parse(dom.byId('serializedExternalFileTypes').value);
      const documentTypes = dojoJson.parse(dom.byId('serializedDocumentTypes').value);
      const documentTypeConfig = {};
      array.forEach(documentTypes, (type) => {
        documentTypeConfig[type.id] = type;
        documentTypeConfig[type.id].shareFileTypes = [];
        documentTypeConfig[type.id].externalFileTypes = [];
      });
      array.forEach(shareFileTypes, (fileType) => {
        documentTypeConfig[fileType.id].shareFileTypes.push(fileType);
      });
      array.forEach(externalFileTypes, (fileType) => {
        documentTypeConfig[fileType.id].externalFileTypes.push(fileType);
      });
      return {
        canShareDocument: canShareDocument,
        externalLink: externalLink,
        documentTypes: documentTypeConfig
      };
    }
    function refreshMenu() {
      gridMenu.build(this, menuItems);
    }
    function initialize() {
      const d = new Deferred();
      gridArgs = lang.mixin(
        {
          size: dom.byId('gridSize').value,
          serviceStore: 'OptMasterList.action',
          methodName: 'getRows',
          fullColumns: columns,
          enableStatistics: true,
          onBeforeRefresh: () => {
            gridMenu.reset(false);
            // biome-ignore lint/suspicious/noThenProperty: Fake deferred
            return { then: (f) => f?.() };
          },
          onLoaded: refreshMenu,
          windowPath: (dom.byId('windowPath') || { value: '' }).value,
          onClickSearch: () => {
            bandera = true;
          },
          onCreateRowFilter: (filter) => {
            if (bandera) {
              filter.options = {};
              filter.options.searchSubfolders = true;
            }
          }
        },
        gridArgsParam || {}
      );
      dialog = initializeDialog(dialog);
      GridFactory.create(gridArgs).then((grid) => {
        saveRequestHandleNEW = new saveRequestHandle(grid);
        d.resolve({ grid: grid, menu: gridMenu });
      });
      return d.promise;
    }
    const promises = [];
    let bandera;
    const def = new Deferred();
    promises.push(getStoragePlace());
    promises.push(getInformationClassification());
    promises.push(getDisposition());
    all(promises).then(() => {
      def.resolve({
        initialize: initialize,
        prepareColumns: prepareColumns,
        columns: columns,
        menuItems: menuItems,
        startup: () => {
          prepareColumns();
          return initialize();
        }
      });
    });
    return def.promise;
  });
