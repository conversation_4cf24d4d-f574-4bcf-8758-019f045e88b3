require([
    'core',
    'bnext/gridCubes',
    'bnext/DynamicFieldUtil',
    'bnext/module/DocumentRequestView',
    'bnext/module/data/RequestType',
    'dijit/Dialog',
    'bnext/gridComponent',
    'bnext/gridComponentUtil',
    'dojo/dom',
    'bnext/callMethod',
    'bnext/i18n!bnext/administrator/document/nls/document-module',
    'bnext/i18n!bnext/administrator/document/nls/document-manager-delayed-to-authorize',
    'dojo/domReady!'
], 
function (
    core, gridCubes, DynamicFieldUtil, DocumentRequestView, requestType, Dialog, gridComponent, gcUtil, dom, callMethod,
    documentModule, 
    i18nBase
) {
    var
    grid,
    columns = [],
    dial = new Dialog(), 
    widgetBK, 
    cType = gcUtil.cType;
    function doEverything(i18n) {
        var viewDetail = function (request) {
            var promise = callMethod({
                url: 'Request.action',
                method: 'load',
                params: [request.id]
            });
            if (widgetBK) {
                widgetBK.destroyRecursive && widgetBK.destroyRecursive();
                widgetBK = null;
            }
            promise.then(function (request) {
                try {
                    widgetBK = new DocumentRequestView({
                        isAFormRequest: !!request.surveyId,
                        enablePdfViewer: request.enablePdfViewer || 0,
                        surveyId: request.surveyId || null,
                        sol: request,
                        sourcePage: 'requestList',
                        onCancel: function () {
                            widgetBK.hide();
                        },
                        requestDialog: dial
                    });
                    //se carga de valores dinamicos al despuest de settear el objeto Request cargado en SOL
                    DynamicFieldUtil.load(
                        widgetBK.sol.id,
                        'DocumentType.Request.Custom.action', 
                        widgetBK.dynamicFields,
                        widgetBK.sol.documentType.id, {
                        cacheOn: false,
                        dijits: true,
                        legacy: false
                    }, widgetBK.sol.dynamicTableName).then(function(r) {
                        for(var key in r.domMap) {
                            if(!r.domMap.hasOwnProperty(key)) {
                                continue;
                            }
                            if(r.domMap[key].widjet) {
                                r.domMap[key].widjet.set('disabled', true);
                            } else {
                                console.error('Debería ser un widjet!!, dynFueld: "' + key + '"');
                            }
                        }
                    });
                } catch (e) {
                    console.log(e, e.stack);
                }
                widgetBK.set('title', i18n.commonNames.titleVerify);
                widgetBK.show();
            });
        };
        var printNew = function (row) {
            return !row.surveyId && row.type === requestType.NEW;
        },
        printModify = function (row) {
            return !row.surveyId && row.type === requestType.MODIFY;
        },
        printAprove = function (row) {
            return !row.surveyId && row.type === requestType.APROVE;
        },
        printCancel = function (row) {
            return !row.surveyId && row.type === requestType.CANCEL;
        },
        printNewForm = function (row) {
            return row.surveyId && row.type === requestType.NEW;
        },
        printModifyForm = function (row) {
            return row.surveyId && row.type === requestType.MODIFY;
        },
        printAproveForm = function (row) {
            return row.surveyId && row.type === requestType.APROVE;
        },
        printCancelForm = function (row) {
            return row.surveyId && row.type === requestType.CANCEL;
        },
        printFillForm = function (row) {
            return row.surveyId && row.type === requestType.FILL;
        };
        gcUtil.column(columns)
            .push('edit', i18n.colNames.edit, cType.Function(['full-row'], viewDetail))
            .push('rejectionDate', i18n.colNamesRejectionDate, cType.Date(true), null, null, 'none')
            .push('code', i18n.colNames.code, cType.Text())
            .push('version', i18n.colNames.version, cType.Text())
            .push('type', i18n.colNames.type, cType.FunctionTextEval(['id'], core.$noop,
                [
                    {'text': i18n.Validate._new, 'Function': printNew},
                    {'text': i18n.Validate._modification, 'Function': printModify},
                    {'text': i18n.Validate._re_approval, 'Function': printAprove},
                    {'text': i18n.Validate._cancellation, 'Function': printCancel},
                    {'text': i18n.Validate._new_form, 'Function': printNewForm},
                    {'text': i18n.Validate._modification_form, 'Function': printModifyForm},
                    {'text': i18n.Validate._re_approval_form, 'Function': printAproveForm},
                    {'text': i18n.Validate._cancellation_form, 'Function': printCancelForm},
                    {'text': i18n.Validate._fill_form, 'Function': printFillForm}
                ]), null, '85px')
            .push('documentControlledType', i18n.colNameDocumentControlledType, 
                cType.TextMap([
                    {name : i18n.documentControlledTypeControlled, value: 'controlled'}, 
                    {name : i18n.documentControlledTypeUncontrolled, value: 'uncontrolled'}
                ]), null, '195px'
            )
            .push('creationDate', i18n.colNames.date, cType.Date(true))
            .push('businessUnitDescription', i18n.columnBusinessUnit, cType.Text())
            .push('departmentDescription', i18n.columnDepartment, cType.Text())
            .push('authorDescription', i18n.colNames.author, cType.Text())
            .push('documentCode', i18n.colNames.doc_code, cType.Text())
            .push('description', i18n.colNames.doc_title, cType.Text())
            .push('enablePdfViewer', documentModule.enablePdfViewer,  cType.TextMap([
                    {name : documentModule.enabledPdfViewer, value: 1}, 
                    {name : documentModule.disabledPdfViewer, value: 0}
                ]), null, null, 'none');
        grid = new gridComponent(gcUtil.basic({
            size: dom.byId('gridSize').value,
            container: 'dataGrid',
            serviceStore: 'RequestList.action',
            methodName: 'getRequestDelayedToAuthorize',
            windowPath: dom.byId('windowPath').value,
            fullColumns: columns
        }));
        dom.byId('window_title').innerHTML = i18nBase.title;
        grid.reload();
        core.hideLoader();
    };
    core.setLang('lang/solicitudes/nls/request.list').then(doEverything);
});