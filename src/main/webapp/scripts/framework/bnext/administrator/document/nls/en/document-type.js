define({
    'text: #dictionaryIndexId option[value="1"]': 'Numeric',
    'text: #dictionaryIndexId option[value="2"]': 'Alphabetical',
    'text: #dictionaryIndexId option[value="3"]': 'Binary',
    'text: #lblStatus': 'Status',
    'text: #lblShareDocumentsPanel': 'Shared document',
    'text: #lblExternalPanel': 'Open document',
    'text: #lblDictionaryIndex': 'Version dictionary',
    'text: #lblFormModule': 'Forms',
    'text: #lblAddFindingsEnabled': 'All fields can report findings',
    'text: #lbl_filePdfPagesEnabled': 'Form attachments will be open in documents visor',
    'title: #lbl_filePdfPagesEnabled_warn': 'Setting this to "Yes", has an impact on your storage consumption',
    'text: #lblAddActivitiesEnabled': 'All fields can create activities',
    'text: option[value=""]':'-- Select --',
    language_strings: [
        {element: '#mainTitle', text: 'Create document type'},
        {element: '#lblCode', text: 'ID'},
        {element: '#lblDescription', text: 'Description'},
        {element: '#lblIsRetainable', text: 'Has storage place'},
        {element: '#lblDocumentControlledType', text: 'Classification'},
        {element: '#lblRegistryCode', text: 'Nomenclature of auto generated code'},
        {element: '#addDynamicFieldBtn', value: 'Dynamic fields', title: 'Click to add'},
        {element: '#saveBtn', value: 'Save'},
        {element: '#cancelBtn', value: 'Cancel'},
        {element: '#cleanBtn', value: 'Clear'},
        {element: '#informationClassification option[value=""]', text: '-- Select --'},
        {element: '#disposition option[value=""]', text: '-- Select --'}
    ],
    label_entityKey_creationDate: 'Request',
    label_entityKey_author: 'Author',
    label_entityKey_organizationalUnit: 'Business unit',
    label_entityKey_documentType: 'Document type',
    label_entityKey_consecutive: 'Consecutive',
    label_entityKey_freeText: 'Free text',
    status_invalid: 'New',
    status_active: 'Active',
    status_inactive: 'Inactive',
    successfullySaved: 'Document type added successfully.',
    uncontrolled: 'Uncontrolled document',
    controlled: 'Controlled document',
    yes: 'Yes',
    no: 'No',
    confimSave: 'Are you sure you want to continue?',
    confirmCancel: "Your changes were not saved.<br>Are you sure you want to go to control document type?",
    'text: #lblShareDocuments': 'Share documents',
    'text: #lblShareMaxDownloads': 'Maximum downloads per link',
    'text: #lblShareMaxViews': 'Maximum views per link',
    'text: #lblShareLife': 'Life of link(days)',
    'text: #lblShareRestrictUserAgent': 'Restrict user agent for link',
    'value: #addShareUserAgents': 'User agents exceptions',
    'text: #lblShareRestrictFileType': 'Restrict file type for link',
    'value: #addShareFileTypes': 'File types exceptions',
    'text: #formatMessage': 'The "Expiration date" of the document is defined in the {facility}/{department}} relationship with the field "Document validity (months)". If it is not defined in the relationship, then the value of the type will be used.',
    shareUserAgents_dialog_lang_title: 'User agents',
    shareUserAgents_colNameCode: 'User Agent String',
    shareUserAgents_colNameDescription: 'Browser',
    shareFileTypes_dialog_lang_title: 'File types',
    shareFileTypes_colNameCode: 'MIME Type',
    shareFileTypes_colNameDescription: 'Extension',
    'text: #lblExternalLink': 'Open in external app',
    'text: #lblExternalMaxDownloads': 'Maximum downloads per external program',
    'text: #lblExternalLife': 'Life of link(days) for external program',
    'text: #lblExternalRestrictUserAgent': 'Restrict user agent of external program',
    'value: #addExternalUserAgents': 'User agents exceptions',
    'text: #lblExternalRestrictFileType': 'Restrict file type for external program',
    'value: #addExternalFileTypes': 'File types exceptions',
    externalUserAgents_dialog_lang_title: 'User agents',
    externalUserAgents_colNameCode: 'User Agent String',
    externalUserAgents_colNameDescription: 'Browser',
    externalFileTypes_dialog_lang_title: 'File types',
    externalFileTypes_colNameCode: 'MIME Type',
    externalFileTypes_colNameDescription: 'Extension',
    'text: .yesnoOption option[value="1"]': 'Yes',
    'text: .yesnoOption option[value="0"]': 'No',
    validDigit: "Only numbers are allowed",
    name: 'Name',
    document_type_dynamic_fields_dialog_lang_title: 'Add dynamic fields',
    'text: #lblDownloadFiles': 'Download original file:',
    'text: #lblDocumentExpiration': 'Document validity (months)',
    'text: #lblMustRead': 'Read pendings:',
    'text: #lblMustAssignReaders': 'Requires assigning readers:',
    'text: #lblreadOnlyEnabled': 'Restrict download:',
    documentExpiredChange: 'The validity of documents has been modified, ',
    'text: #lblquickPrintEnabled': 'Enable quick print:',
    confirmDisablePdfViewer: 'Disabling the PDF Viewer also disables controlled and uncontrolled copies for this document type. Do you really want to continue?',
    documentExpiration: ' document(s) will be affected: ',
    'text: #lblFormSection': 'Form configuration',
    'text: #lblCollectingAndStoreResponsible': 'Responsible for collecting and storing',
    'text: #lblInformationClassification': 'Information classification',
    'text: #lblDisposition': 'Disposition'
});