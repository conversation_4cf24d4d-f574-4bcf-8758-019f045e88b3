require([
    'core',
    'bnext/administrator/document/document-simple-list', 
    'dojo/dom', 'dojo/promise/all', 'dojo/_base/lang',
    'dojo/parser', 'dijit/registry', 
    'bnext/administrator/document/document-menu-item',
    'bnext/Tabs',
    'bnext/module/grid-menu-util', 'bnext/special/handler!', 'dojo/domReady!'], 
function(
        core,
        documentSimpleList, 
        dom, all, lang,
        parser, registry, DocumentMenuItem, Tabs, GridMenuUtil) {
    function preview(id, nodo, repositorio) {
        core.showLoader().then(function() {
            document.forms['previewForm'].intDocumentoId.value = id;
            document.forms['previewForm'].nodo.value = nodo;
            document.forms['previewForm'].repositorio.value = repositorio;
            document.forms['previewForm'].submit();
        });
    }
    function printDoc(id, nodo, repositorio, surveyId) {
        return core.isNull(surveyId);
    }

    var dialog;
    parser.parse().then(function() {
        dialog = registry.byId('solicitudDialog');
        setTitleBarsColor();
    });
    var doEverything = function(i18n) {
        var menuItems = [];
        var menuItemsSoon = [];
        GridMenuUtil.menu(menuItems, DocumentMenuItem)
                .push(DocumentMenuItem.DETAIL_DOCUMENT, preview, printDoc, ['id', 'nodo.id', 'repositorio.id', 'surveyId']);
        GridMenuUtil.menu(menuItemsSoon, DocumentMenuItem)
                .push(DocumentMenuItem.DETAIL_DOCUMENT, preview, printDoc, ['id', 'nodo.id', 'repositorio.id', 'surveyId']);
        var controlGrid = documentSimpleList({
            container: "dataGrid_control",
            methodName: 'getExpiredDocumentsForCurrentUser',
            serviceStore: "OptDocumentRequest.action",
            menuItems: menuItems,
            includeGoogleDriveExport: false,
            includeCanShareDocument: false,
            includeDownloadDocument: false,
            includeExternalLink: false,
            includeStatus: false,
            reapprove: dom.byId("onlyDocumentManagerReapprove").value,
            docRole: dom.byId('documentRole').value
        }).then(function(docList) {
            docList.prepareColumns();
            return docList.initialize();
        });
        var controlSoonGrid = documentSimpleList({
            container: "dataGrid_soon",
            methodName: 'getExpiredSoonDocumentsForCurrentUser',
            serviceStore: "OptDocumentRequest.action",
            menuItems: menuItemsSoon,
            includeGoogleDriveExport: false,
            includeCanShareDocument: false,
            includeDownloadDocument: false,
            includeExternalLink: false,
            includeStatus: false,
            reapprove: dom.byId("onlyDocumentManagerReapprove").value,
            docRole: dom.byId('documentRole').value
        }).then(function(docList) {
            docList.prepareColumns();
            return docList.initialize();
        });
        all([controlGrid, controlSoonGrid]).then(lang.hitch(this, function (grids) {
            new Tabs({
                tabs: [
                    {title: i18n.tabNameToRenew, grid: grids[0].grid, name: 'control'},
                    {title: i18n.tabNameToRenewSoon, grid: grids[1].grid, name: 'soon'}
                ],
                margin: '5px',
                defaultWidth: 'auto',
                searchNode: 'search',
                paginationNode: 'pagination',
                pageSize: dom.byId('gridSize').value
            }, 'tabNode');
            core.hideLoader();
        }));
    };
    core.setLang('lang/documentos/nls/to-renew-documents').then(doEverything);
});