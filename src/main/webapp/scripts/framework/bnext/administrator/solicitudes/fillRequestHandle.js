define([
    'core', 
    'bnext/callMethod', 'dojo/Deferred',
    'bnext/i18n!bnext/administrator/solicitudes/nls/fillRequestHandle',
    'bnext/_base/refererPage',
    'bnext/_base/tab-utils',
    'bnext/angularNavigator'
],
function(
    core, callMethod, Deferred, i18n, refererPage, TabUtils, angularNavigator
) {
    var isFirst = false;
    /**
     * Es forzoso recibir los 3 primeros parámetros o solo el último
     *
     */
    function openForm(surveyId, documentId, outstandingSurveyId, params, isContinueFill, urlTitle, documentMasterId) {
        var def = new Deferred();
        var view = 'v.request.survey.fill.view';
        if (isContinueFill) {
            view = 'v.request.survey.continue-fill.view';
        }
        // Si no existe formulario que llenar no entrar a esta función
        if (!surveyId) {
            def.resolve(false);
            return def.promise;
        }
        if (outstandingSurveyId) {
                core.showLoader(i18n.loading).then(function() {
                    var taskUrl = view + '?'
                         + 'id=' + surveyId
                         + (documentId ? '&documentId=' + documentId  : '')
                         + (documentMasterId ? '&documentMasterId=' + encodeURIComponent(documentMasterId)  : '')
                         + '&requestMode=REQUESTOR'
                         + '&tabId=' + encodeURIComponent(TabUtils.getTabId())
                         + '&outstandingSurveyId='+outstandingSurveyId
                                 + refererPage.getQueryString() + (params || '');
                    angularNavigator.navigateLegacy(taskUrl, null, null, urlTitle || '');
                    def.resolve(true);
                }, core.hideLoader);
        } else {
            core.showLoader(i18n.loading).then(function() {
                var taskUrl = view + '?'
                    + 'id=' + surveyId
                    + '&documentId=' + documentId
                    + '&tabId=' + encodeURIComponent(TabUtils.getTabId())
                    + '&requestMode=REQUESTOR'
                    + refererPage.getQueryString() + (params || '');
                angularNavigator.navigateLegacy(taskUrl, null, null, urlTitle || '');
                def.resolve(true);
            }, core.hideLoader);
        }
    }
    function attendWithAdminAccess(requestId, isContinueFill) {
        core.showLoader(i18n.preValidateFill).then(function() {
            require(['bnext/module/DocumentFillAttender'], function(DocumentFillAttender) {
               DocumentFillAttender.attend(null, requestId, null, isContinueFill);
            });
        });   
    }
    function fillRequestHandle(surveyId, documentId, outstandingSurveyId, requestId, params, hasAdminAccess, isAdmin, isContinueFill, urlTitle, documentMasterId) {
        if (!surveyId) {
            console.error('Missing surveyId!');
            return;
        }
        if (isFirst && !isContinueFill) {
            return;
        }
        isFirst = true;
        function go() {
            openForm(surveyId, documentId, outstandingSurveyId, params, isContinueFill, urlTitle, documentMasterId);
        }
        if (requestId) {
            if (hasAdminAccess) {
                attendWithAdminAccess(requestId, isContinueFill);
            } else {
                core.showLoader(i18n.preValidateFill).then(function() {
                    callMethod({
                        url: "Request.My.Survey.action",
                        method: "getFillFormPending",
                        params: [outstandingSurveyId, isContinueFill]
                    }).then(
                        function(result) {
                            if (isAdmin && result === 'ADMIN') {
                                attendWithAdminAccess(requestId, isContinueFill);
                            } else if (result === 'ADMIN') {
                                core.hideLoader();
                                isFirst = false;
                                core.dialog(i18n.notNextUser, null, "");                                
                            } else if (result) {
                                core.navigateLegacy(result, null, null, urlTitle);
                            } else {
                                core.hideLoader();
                                isFirst = false;
                                core.dialog(i18n.notNextUser, null, "");
                            }
                        },
                        function(e) {
                            core.hideLoader();
                            core.error(e);
                        }
                    );
                });
            }
        } else if (documentId) {
            core.showLoader(i18n.preValidateFill).then(function(){
                callMethod({
                    url: "../DPMS/Document.action",
                    method: "isRequestInProcess",
                    params: [documentId, 'CANCEL']
                }).then(
                    function(result) {
                        if (result) {
                            core.hideLoader();
                            core.info(i18n.fillInProcess, core.i18n.yes, core.i18n.no).then(go, function() {
                                isFirst = false;
                            });
                        } else {
                            go();
                        }
                    },
                    function(e) {
                        isFirst = false;
                        core.hideLoader();
                        core.error(e);
                    }
                );
            });
        } else {
            go();
        }
    }
    return fillRequestHandle;
});