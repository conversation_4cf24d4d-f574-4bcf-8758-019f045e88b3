define({
    'text: .new-entity #window_title': 'New meeting',
    'text: .edit-entity #window_title': 'Meeting edition',
    'text: #lblCode': 'Code:',
    'text: #lblGenerateKey': 'Auto generate code',
    'text: #lblDescription': 'Subject:',
    'text: #lblLocation': 'Location:',
    'text: label[for=hostId]': 'Host:',
    'text: label[for=typeId]': 'Meeting type:',
    'text: #lblBody': 'Description:',
    'text: #lblInitialDate': 'Initial date:',
    'text: #lblStartOn': 'Start on:',
    'text: #lblFinishOn': 'Finish on:',
    'text: #lblClassified': 'Private meeting:',
    'text: #lblGuest': 'Guests:',
    'text: #documentFormLbl': 'Minute:',
    'text: #addParticipants': 'Participants',
    'text: #addComments': 'Comments',
    'text: #addActivity': 'Activities',
    'text: #form_status': 'Status',
    'text: #form_to_fill': 'Fill Out',  
    'text: #form_code': 'Code',    
    'text: #form_version': 'Version',     
    'value: #saveBtn': 'Save',
    'value: #updateBtn': 'Update',
    'value: #cancelBtn': 'Cancel',
    'value: #cleanBtn': 'Clean',       
    'text: #lblRecurrence': 'Recurrence ID:',     
    'text: #lblShowPeriodicity': 'Recurrent:', 
    'text: #lblPeriodicity': 'Periodicity detail:',
    'text: #lblStatus': 'Status:',
    'text: #showPeriodicity option[value="1"]': 'Yes',
    'text: #showPeriodicity option[value="0"]': 'No',
    'title: #guest': 'Mailing list of external guests.',
    successfullySaved: 'The meeting has been successfully saved.',
    attachments:'Attachments:',
    areYouSureToContinue: 'Are you sure to continue?',
    yes: 'Yes',
    no: 'No',
    accept : 'Accept',
    cancel : 'Cancel',
    status_reported: 'Reported',
    status_ready: 'Ready',
    missingComment: 'Please enter a comment.',
    status_in_process: 'In Process',
    status_concluded: 'Concluded',
    status_recurring: 'Recurring',
    status_closed: 'Closed',
    status_cancelled: 'Cancelled',
    meeting_participants_dialog_lang_title:'Add participants',
    participants_code:'Code',
    participants_name:'Name',
    dialogNewCommentMessage:'Type your comment for the meeting',
    dialogNewCommentTitle:'New Comment',
    documentFormFilledChangeScreeenConfirm:'You will be taken to a different screen to fill the form',
    documentFormContinueChangeScreeenConfirm:'You will continue to fill the form in a different window',
    documentFormNewChangeScreeenConfirm:'You will be taken to a different screen to fill the form',
    minuteSection: 'Minute section',
    recurrentTitle: 'Recurrent meeting editon',
    recurrentViewTitle: 'Recurrent meeting',
    'text: #lblMeetings': 'Generated meetings:',
    colNamesStatus: 'Status',
    invalidDate: 'Invalid date.',
    invalidDateRelErrorMessage:"The meeting must start before it ends.",
    verifyConfirmNewCode: ''
    + 'There was a change in the information that generate the ID. Do you want to use a new ID?<br>'
    + '<br><span class="w175 floatLeft">- ID before change: </span><b>%currentCode%</b>'
    + '<br><span class="w175 floatLeft">- ID after change: </span><b>%codePreview%</b>'
    + '<br><br>The consecutive will be reserved at the moment of accepting the change.',
    reservingCodeSequence: 'The code sequence is being reserved...',
    verifyingCode: 'Verifying code...',

    updateMeeting: 'The meeting will be updated.',
    updateMeetingRecurrence: 'The meeting recurrence will be updated.',
    reportMeeting: 'The meeting will be created.',
    concludeMeeting: 'The meeting will be concluded.',
    inProcessMeeting: 'The meeting will be started.',
    readyMeeting: 'The meeting will be marked as ready.',
    closeMeeting: 'The meeting will be closed.'
});