require(['core', 'dojo/dom', 'dojo/on', 'dojo/query', 'bnext/callMethod', "dojo/ready", 'dojo/domReady!'],
    function (core, dom, on, query, callMethod, ready) {
        function doEverything(i18n) {
            core.hideLoader();
            var data = {};
            var load = function(){
                callMethod({
                    url: 'Meeting-Preferences.action',
                    method: 'get',
                    params: []
                }).then(function(result){
                    for (var id in result) {
                        if (dom.byId(id).tagName.toLowerCase() === 'select') {
                            dom.byId(id).value = result[id] === 0 ? '0' : result[id] === 1 ? '1' : result[id];
                        }
                    }
                });
            };
            load();
            var save = function(){
                query('select', dom.byId('validate_form')).forEach(function (input) {
                    if (input.name) {
                        data[input.name] = input.value;
                    }
                });
                callMethod({
                    url: "Meeting-Preferences.action",
                    method: "updateMeetingPreferences",
                    params: [data]
                }).then(
                    function (resultSave) {
                        if (resultSave === 1) {
                            core.dialog(i18n.message_success, i18n.btnAdd);
                        }else{
                            core.dialog(i18n.error_success, i18n.btnAdd);
                        }
                        core.hideLoader();
                    }
                );
            }; 
            on(dom.byId('saveBtn'), 'click', save);
            on(dom.byId('cancelBtn'), 'click', cancel);
        }
        ready(function() {
            core.setLang('bnext/administrator/catalog/meeting/nls/preferences').then(doEverything);
        });
        
});