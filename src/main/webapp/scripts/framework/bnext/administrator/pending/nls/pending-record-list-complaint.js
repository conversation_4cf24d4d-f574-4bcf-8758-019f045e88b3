define([], 
function () {
    return {
        root: {
            'text: #window_title': 'Pendientes de quejas',
            'text: #optionalEscalationToOriginal': 'La queja seleccionada tenía más de un usuario responsable antes de escalarse, por favor seleccione uno del listado.',
            colName_entityCode: 'Clave de queja',
            colName_entityTypeCode: 'Clave del tipo de queja',
            colName_entityTypeName: 'Tipo de queja',
            msgConfirmSave: '¿Está seguro de que desea que el usuario <b>%newUser%</b> <br>sea el nuevo responsable de atender el pendiente <br>por "<b><i>%pendingName%</i></b>" de la queja con clave <b>%entityCode%</b>?'
        },
        en: true,
        'es-AI': true
    };
});