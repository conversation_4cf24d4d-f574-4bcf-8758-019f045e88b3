define({
    title_auditToConfirmDate: 'Pending confirmation',
    title_auditToConfirmChangeByLeader: 'To authorize date change',
    title_auditToConfirmChangeByManager: 'To authorize date change (module manager)',
    title_auditToFillByLeader: 'To be executed',
    title_auditToFillByHelper: 'To be executed (Support auditor)',
    title_auditToAcceptResult: 'To accept results',
    colNamePending: 'Pending',
    colNameCode: 'ID',
    colNameQuestions: 'Questionnaire without answers',
    colNameType: 'Type',
    colNameProcess: 'Process',
    colNameDepartment: '{Department}',
    colNameArea: '{Area}',
    colNameAuditTeamLeader: 'Audit team leader',
    colNameLiderAudit: 'Main auditor',
    colNameAudited: 'Person being audited',
    colNameSupportAudit: 'Audit helper',
    colNamePlannedStartDate: 'Planned start date',
    colNameStartDate: 'Start date',
    colNamePlannedEndDate: 'Planned end date',
    colNameEndDate: 'End date',
    colNameFillStatus: 'Filled status',
    statusFillProgramed: 'Programmed',
    statusFillInProcess: 'In process',
    statusFillInProcessWithAdvance: 'In process with advances',
    statusFillEnded: 'Finished'
});