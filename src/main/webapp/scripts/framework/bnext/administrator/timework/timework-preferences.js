require([
    'core',
    'dojo/dom',
    'bnext/callMethod',
    'dojo/on',
    'bnext/LinkedGridFactory',
    'bnext/gridComponentUtil',
    'bnext/angularNotice',
    'dojo/domReady!'
], function (core, dom, callMethod, on, LinkedGridFactory, gcUtil, angularNotice) {
    function doEverything(i18n) {
        var enableTimeworkValue = dom.byId('enableTimeworkValue');
        var timeworkUrlValue = dom.byId('timeworkUrlValue');
        var enableTimeworkSyncTestValue = dom.byId('enableTimeworkSyncTestValue');
        var timeworkMailsValue = dom.byId('timeworkMailsValue');
        var timeworkSyncTypeValue = dom.byId('timeworkSyncTypeValue');
        var saveBtn = dom.byId('saveBtn');
        var businessUnits = [];
        var enableTimeworkChange = +dom.byId('enableTimework').value;
        var enableTimeworkSyncTestChange = +dom.byId('enableTimeworkSyncTest').value;
        var businessUnitsIds = [];
        var maxConnectionTimeoutValue = dom.byId('maxConnectionTimeoutValue');
        var maxRequestTimeoutValue = dom.byId('maxRequestTimeoutValue');
        var refreshPageAfterSave = false;
        var currentValueEnableTimework = null;

        initializeFormControl();
        function initializeFormControl() {
            fillYesONotSelects();
            var enableTimework = dom.byId('enableTimework');
            for (var option of enableTimework.options) {
                if (option.value === enableTimeworkValue.value) {
                    option.setAttribute('selected', true);
                    currentValueEnableTimework = option.value;
                }
            }

            var timeworkUrl = dom.byId('timeworkUrl');
            timeworkUrl.value = timeworkUrlValue.value;

            var enableTimeworkSyncTest = dom.byId('enableTimeworkSyncTest');
            for (var option of enableTimeworkSyncTest.options) {
                if (option.value === enableTimeworkSyncTestValue.value) {
                    option.selected = true;
                    enableTimeworkSyncTestChange = +option.value;
                } else {
                    option.selected = false;
                }
            }

            var timeworkMails = dom.byId('timeworkMails');
            timeworkMails.value = timeworkMailsValue.value;

            var timeworkSyncType = dom.byId('timeworkSyncType');
            for (var option of timeworkSyncType.options) {
                if (option.value === timeworkSyncTypeValue.value) {
                    option.selected = true;
                }
                option.innerHTML = i18n[option.value];
            }
            
            var maxConnectionTimeout = dom.byId('maxConnectionTimeout');
            maxConnectionTimeout.value = maxConnectionTimeoutValue.value;

            var maxRequestTimeout = dom.byId('maxRequestTimeout');
            maxRequestTimeout.value = maxRequestTimeoutValue.value;
        }

        function fillYesONotSelects() {
            for (var option of dom.byId('enableTimework').options) {
                if (option.value === '1') {
                    option.innerHTML = core.i18n.yes;
                } else if (option.value === '0'){
                    option.innerHTML = core.i18n.no;
                }
            }
            for (var option of  dom.byId('enableTimeworkSyncTest').options) {
                if (option.value === '1') {
                    option.innerHTML = core.i18n.yes;
                } else if (option.value === '0'){
                    option.innerHTML = core.i18n.no;
                }
            }
        }

        function getData() {
            return {
                enableTimework: enableTimeworkChange,
                timeworkUrl: dom.byId('timeworkUrl').value,
                enableTimeworkSyncTest: enableTimeworkSyncTestChange,
                timeworkMails: dom.byId('timeworkMails').value,
                timeworkSyncType: dom.byId('timeworkSyncType').value,
                maxConnectionTimeout: dom.byId('maxConnectionTimeout').value,
                maxRequestTimeout: dom.byId('maxRequestTimeout').value,
            };
        }

        function loadGridBusinessUnits() {
            var columns = [];
            gcUtil.column(columns)
                    .push('code', i18n.businessUnitCode, gcUtil.cType.Text())
                    .push('description', i18n.businessUnitDescription, gcUtil.cType().Text());
            businessUnits = LinkedGridFactory.create({
                id: 'bussinessUnit_preferences',
                addButton: dom.byId('btnBusinessUnits'),
                dialogClassName: 'fullSizeScreen',
                onAfterAdd: addItems,
                onAfterRemove: removeItems,
                onGroundLoaded: updateFilters
            }, 1, 'Settings.BusinessUnitTimeworkSync.action', i18n, columns);
        }
        loadGridBusinessUnits();

        function add_businessUnit(id) {
            showLoader();
            console.log("[_businessUnit=" + id + "]");
            var args = {
                url: "../DPMS/Settings.BusinessUnitTimeworkSync.action",
                method: "save",
                params: [1, id]
            };
            callMethod(args).then(function (gsh) {
                if (gsh === 0) {
                    var dialogTitle = i18n.msg_varios.dialog_title;
                    var dialogMessage = i18n.msg_varios.error_add_businessUnit;
                    core.dialog(dialogMessage, null, null, dialogTitle);
                }
                hideLoader();
            }, function () {
                hideLoader();
            });
        }

        function addItems() {
            if (!businessUnits) {
                return;
            }
            var data = businessUnits.getGroundData();
            if (data.length > 0) {
                angularNotice.notice(i18n.businessUnitToSync);
                for (var i = 0; i < data.length; i++) {
                    if (businessUnitsIds.indexOf(data[i].id) === -1) {
                        add_businessUnit(data[i].id);
                        updateFilters();
                    }
                }
            }
            return;
        }

        function removeItems(id) {
            if (!businessUnits) {
                return;
            }
            businessUnits.getGroundGrid().popRowById(id);
            delete_businessUnit(id);
            updateFilters();
        }

        function delete_businessUnit(id) {
            core.showLoader();
            console.log("[_businessUnit=" + id + "]");
            var args = {
                url: "../DPMS/Settings.BusinessUnitTimeworkSync.action",
                method: "delete",
                params: [id]
            };
            callMethod(args).then(function () {
                core.hideLoader();
            });
        }

        function updateFilters() {
            if (!businessUnits) {
                return;
            }
            businessUnitsIds = [];
            var data = businessUnits.getGroundData();
            for (var i = 0; i < data.length; i++) {
                businessUnitsIds.push(data[i].id);
            }
        }

        on(saveBtn, 'click', function () {
            var data = getData();
            callMethod({
                url: 'Timework.action',
                method: 'savePreferences',
                params: [data]
            }).then(function (response) {
                if (!response) {
                    if (refreshPageAfterSave) {
                        core.dialog(i18n.preferencesSaved, core.i18n.refresh).then(function () {
                            window.parent.location.reload();
                        });
                        return;
                    }
                    core.dialog(i18n.preferencesSaved);
                } else {
                    core.dialog(i18n.notChangedDone)
                }
            })
        });

        on(dom.byId('enableTimework'), 'change', function (e) {
            enableTimeworkChange = +e.target.value;
            refreshPageAfterSave = enableTimeworkChange !== +currentValueEnableTimework;
        });

        on(dom.byId('enableTimeworkSyncTest'), 'change', function (e) {
            enableTimeworkSyncTestChange = e.target.value;
        });
        on(dom.byId('cancelBtn'), 'click', function() {
                core.navigate('pendings')
        })

        core.hideLoader();
    }

    core.setLang('bnext/administrator/timework/nls/timework-preferences').then(doEverything);
});