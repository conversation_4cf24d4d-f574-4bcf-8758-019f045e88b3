.stopwatchSelector .hierarchy-readonly-fields,
.CatalogHierarchy .hierarchy-readonly-fields,
.CatalogHierarchy .hierarchy-level-fields {
  display: flex;
  background-color: white;
  flex-direction: column;
  flex-wrap: wrap;
}

.geolocation .fieldset-container,
.stopwatchSelector .fieldset-container,
.HierarchyReadonlyField .fieldset-container {
  border-radius: 0.25rem;
  background: none;
  padding: 0.1rem 0.85rem 0.85rem;
  margin: 1px;
  outline: none;
  box-sizing: border-box;
  border: 1px solid #b3b3b3;
  min-height: 4.1rem;
}
.geolocation .fieldset-container > div,
.stopwatchSelector .fieldset-container > div,
.HierarchyReadonlyField .fieldset-container > div {
  display: flex;
  gap: 1rem;
  flex-direction: row;
  flex-wrap: wrap;
}
.geolocation .field-container,
.stopwatchSelector .field-container,
.HierarchyReadonlyField .field-container {
  gap: 0.5rem;
  display: flex;
  flex-direction: row;
  font-size: 0.8rem;
  line-height: 1rem;
  padding: 0.25rem;
  border-radius: 0.25rem;
  border: 1px solid #b3b3b3;
}
.geolocation .field-container,
.stopwatchSelector .field-container {
  flex-wrap: wrap;
  box-sizing: content-box;
}
.geolocation .field-container.small-6,
.stopwatchSelector .field-container.small-6,
.HierarchyReadonlyField .field-container {
  width: 50%;
  flex-basis: calc(50% /*width*/ - 1rem /*padding*/ - 2px /*borde*/);
}
.geolocation .field-container.small-12,
.stopwatchSelector .field-container.small-12 {
  flex-basis: calc(100% /*width*/ - 0.5rem /*padding*/);
}
.geolocation .fieldset-container > div :only-child,
.stopwatchSelector .fieldset-container > div :only-child,
.HierarchyReadonlyField .fieldset-container > div :only-child {
  flex-basis: 100%;
}
.geolocation .fieldset-container legend,
.stopwatchSelector .fieldset-container legend,
.HierarchyReadonlyField .fieldset-container legend {
  font-size: 0.90rem;
  font-weight: bold;
}

.geolocation .input-box-label,
.stopwatchSelector .input-box-label,
.HierarchyReadonlyField .input-box-label {
  width: 35%;
  text-align: right;
}

.geolocation .input-box-label-left,
.stopwatchSelector .input-box-label-left,
.HierarchyReadonlyField .input-box-label-left {
  width: 65%;
  text-align: left;
}

/*Se sobreescribe la clase para que se ajuste al tamaño mas adecuado del contenedor*/
.geolocation .text-ellipsis,
.stopwatchSelector .text-ellipsis,
.HierarchyReadonlyField .text-ellipsis {
  -webkit-line-clamp: 1 !important;
  word-break: break-word !important;
}

@media screen and (max-width: 48em) {
  .geolocation .fieldset-container,
  .stopwatchSelector .fieldset-container,
  .HierarchyReadonlyField .fieldset-container {
    flex-basis: content;
  }

  .HierarchyReadonlyField .fieldset-container > div :only-child,
  .geolocation .field-container.small-6,
  .stopwatchSelector .field-container.small-6,
  .HierarchyReadonlyField .field-container {
    width: 100%;
    flex-basis: calc(100% /*width*/ - 1rem /*padding*/ - 2px /*borde*/);
  }

  .HierarchyReadonlyField .input-box-label {
    width: 45%;
  }
}
