/* global URL */
define([
  'loader',
  'bnext/survey/formObject/SurveyWeightingType',
  'dojo/dom',
  'dojo/query',
  'dojo/on',
  'dojo/dom-attr',
  'bnext/module/Uploader',
  'dojo/_base/lang',
  'bnext/i18n!bnext/administrator/surveys/nls/surveycapture.events',
  'bnext/administrator/surveys/SurveyCaptureMessages',
  'dojo/dom-class',
  'dojo/dom-construct',
  'dojo/_base/window',
  'bnext/_base/media-queries',
  'dijit/registry',
  'bnext/callMethod',
  'dojo/_base/array',
  'bnext/administrator/surveys/SurveyCaptureDomUtils',
  'bnext/administrator/surveys/CatalogHierarchy',
  'bnext/_base/SelectWithSearch',
  'dojo/Deferred',
  'dojo/promise/all',
  'bnext/survey/_util/dateSelectorUtils',
  'bnext/survey/_util/StopwatchSelectorUtils',
  'bnext/angularImageVisualizer'
], (
  loader,
  SurveyWeightingType,
  dom,
  query,
  on,
  domAttr,
  Uploader,
  lang,
  i18n,
  SurveyCaptureMessages,
  domClass,
  domConstruct,
  dojoWindow,
  MediaQueries,
  registry,
  callMethod,
  array,
  SurveyCaptureDomUtils,
  CatalogHierarchy,
  SelectWithSearch,
  Deferred,
  all,
  dateSelectorUtils,
  StopwatchSelectorUtils,
  angularImageVisualizer
) => {
  const w = window;
  const outstandingArchivedDom = dom.byId('outstandingArchived') || { value: null };
  const isArchived = outstandingArchivedDom.value === 'true';
  const form = query('.form')[0];
  const imageFileTypes = ['image/png', 'image/gif', 'image/jpg', 'image/jpeg', 'image/bmp', 'image/tiff', 'image/tif', 'image/webp'];
  const SUPPORTED_OPEN_BROWSER_CONTENT_TYPES = [
    'image/png',
    'image/gif',
    'image/webp',
    'image/jpg',
    'image/jpeg',
    'image/bmp',
    'image/tiff',
    'image/tif',
    'application/pdf',
    'text/plain',
    'text/html',
    'application/xml'
  ];

  const debug = false;

  // noinspection UnnecessaryLocalVariableJS
  const SurveyCaptureFields = {
    initializedAuthorizationPooolindexes: false,
    fillAutoTexts: fillAutoTexts,
    focusFirstUnanswered: focusFirstUnanswered,
    buildFields: buildFields,
    initFields: initFields,
    clearFile: clearFile,
    missingFile: missingFile,
    onFileChanged: onFileChanged
  };

  function autoresize(ctrl) {
    if (['select', 'option', 'input'].indexOf(ctrl.tagName.toLowerCase()) === -1) {
      ctrl.style.height = '0px';
      if (!ctrl.style.cssText) {
        ctrl.style.cssText = `height:${ctrl.scrollHeight + 4}px !important`;
      } else {
        const styles = ctrl.style.cssText.split(';');
        let newStyles = '';
        ctrl.style.cssText = '';
        for (let style of styles) {
          if (style) {
            if (style.indexOf('height') !== -1) {
              style = `height:${ctrl.scrollHeight + 4}px !important`;
            }
            newStyles += `${style};`;
          }
        }
        ctrl.style.cssText = newStyles;
      }
    }
  }

  function focusFirstUnanswered() {
    if (debug) {
      console.log('focusFirst');
    }
    const inputs = query(
      'input[type=text]:not(.displayNone):not([disabled]),\n\
      input[type=radio]:not(.displayNone):not([disabled]),\n\
      input[type=checkbox]:not(.displayNone):not([disabled]),\n\
      textarea:not(.displayNone):not([disabled]),\n\
      select:not(.displayNone):not([disabled]),\n\
      [contenteditable]'
    );
    if (!inputs.length) {
      return;
    }
    const firstInput = inputs[0];
    if (firstInput instanceof HTMLInputElement || firstInput instanceof HTMLSelectElement || firstInput instanceof HTMLTextAreaElement) {
      SurveyCaptureDomUtils.setMyScroll(firstInput);
      firstInput.focus();
    } else {
      SurveyCaptureDomUtils.setMyScroll(firstInput);
      firstInput.focus();
    }
  }

  function fillAutoTexts() {
    const def = new Deferred();
    require(['bnext/administrator/surveys/SurveyCaptureAutoTexts'], (SurveyCaptureAutoTexts) => {
      SurveyCaptureAutoTexts.setupAutoTexts().then(
        () => {
          def.resolve(true);
        },
        (e) => {
          def.reject(e);
        }
      );
    }, (err) => {
      def.reject(err);
    });
    return def.promise;
  }

  function buildFields() {
    const file = buildFileUploadFields();
    const hierarchyFields = buildCatalogHierarchyFields();
    const catalogFields = buildCatalogFields();
    const dateSelectors = dateSelectorUtils.buildDateSelectors();
    const stopwatchMainActions = StopwatchSelectorUtils.buildStopwatchSelectors();
    const freeTextFields = buildFreeTextFields();
    const staticValueFields = buildStaticValueFields();
    return all([file, hierarchyFields, catalogFields, dateSelectors, stopwatchMainActions, freeTextFields, staticValueFields]);
  }
  function missingFile(fieldContainer) {
    const inputFileId = query('input.inputFileId', fieldContainer)[0];
    if (inputFileId.value !== '-1') {
      return;
    }
    const uploadFileDom = query('div.uploadFile', fieldContainer)[0];
    domConstruct.create(
      'span',
      {
        innerHTML: i18n.noFilesAdded,
        class: 'noFilesAdded'
      },
      uploadFileDom
    );
  }

  function clearFile(fieldContainer) {
    for (const button of query('button.deleteFileButton', fieldContainer)) {
      const fileId = domAttr.get(button, 'file-id');
      if (fileId !== null && typeof fileId !== 'undefined') {
        onFileDeleted(fieldContainer, fileId);
      }
    }
  }

  function onFileChanged(fieldContainer, file) {
    const hasFile = !!file;
    if (!hasFile) {
      return;
    }
    const inputFileId = query('input.inputFileId', fieldContainer)[0];
    const inputFileContentType = query('input.inputFileContentType', fieldContainer)[0];
    const inputFileExtension = query('input.inputFileExtension', fieldContainer)[0];
    const inputFileName = query('input.inputFileName', fieldContainer)[0];
    array.forEach(file, (f) => {
      if (inputFileId.value === '-1' && f.jsonEntityData.id !== null) {
        inputFileId.value = '';
      }
      if (inputFileId.value !== '') {
        inputFileId.value += ', ';
        inputFileName.value += ', ';
        inputFileContentType.value += ', ';
        inputFileExtension.value += ', ';
      }
      inputFileId.value += `${f.jsonEntityData.id}`;
      inputFileName.value += f.jsonEntityData.description;
      inputFileContentType.value += f.jsonEntityData.contentType;
      inputFileExtension.value += f.jsonEntityData.extension;
      let newName;
      let newButton;
      let deleteButton;
      let viewButton;
      if (file.length >= 1) {
        newButton = lang.clone(query('button.downloadFileButton', fieldContainer)[0]);
        on(newButton, 'click', (button) => {
          const url = `../view/v-download-attached.view?id=${domAttr.get(button.target, 'file-id')}&documentId=-1`;
          const fileLink = dom.byId('fileLink');
          fileLink.href = url;
          fileLink.click();
        });
        domAttr.set(newButton, 'file-id', `${f.jsonEntityData.id}`);
        domClass.add(newButton, 'fileDom');
        newName = domConstruct.create('h4', { innerText: f.jsonEntityData.description, class: 'fileName fileDom' });
        const enableOpenPdfFiles = dom.byId('enableOpenPdfFiles') && dom.byId('enableOpenPdfFiles').value === 'true';
        const currentInputFileContentType = f.jsonEntityData.contentType.trim();
        if (SUPPORTED_OPEN_BROWSER_CONTENT_TYPES.indexOf(currentInputFileContentType) !== -1) {
          viewButton = lang.clone(query('button.viewFileButton', fieldContainer)[0]);
          on(viewButton, 'click', (button) => {
            const imagesContentTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (imagesContentTypes.includes(f.jsonEntityData.contentType)) {
              const data = {
                slideUrl: `../view/v-download-attached.view?id=${domAttr.get(button.target, 'file-id')}&documentId=-1`,
                maximized: false,
                description: f.jsonEntityData.description,
                open: true
              };
              angularImageVisualizer.show(data);
            } else if (enableOpenPdfFiles) {
              require(['bnext/angularNavigator'], (angularNavigator) => {
                angularNavigator.navigateLegacyBlank(`v-document-viewer.view?fileId=${domAttr.get(button.target, 'file-id')}&simpleview=true`, null, true);
              });
            }
          });
          domAttr.set(viewButton, 'file-id', `${f.jsonEntityData.id}`);
          domClass.add(viewButton, 'fileDom');
        }
        deleteButton = lang.clone(query('button.deleteFileButton', fieldContainer)[0]);
        on(deleteButton, 'click', (button) => {
          const disabled = SurveyCaptureDomUtils.isFieldDisabled(fieldContainer);
          if (disabled) {
            return;
          }
          const fileId = domAttr.get(button.target, 'file-id');
          onFileDeleted(fieldContainer, fileId);
        });
        domAttr.set(deleteButton, 'file-id', `${f.jsonEntityData.id}`);
        domClass.add(deleteButton, 'fileDom');
      }
      if (newName && newButton && deleteButton) {
        newName.innerText += f.jsonEntityData.description;
        domClass.remove(newName, 'displayNone');
        domAttr.set(newName, 'file-id', `${f.jsonEntityData.id}`);
        domAttr.set(newButton, 'file-id', `${f.jsonEntityData.id}`);
        if (viewButton) {
          domAttr.set(viewButton, 'file-id', `${f.jsonEntityData.id}`);
          domConstruct.place(newName, query('button.viewFileButton', fieldContainer)[0], 'before');
          domConstruct.place(viewButton, newName, 'before');
          domAttr.set(viewButton, 'file-id', `${f.jsonEntityData.id}`);
          domConstruct.place(newName, query('button.viewFileButton', fieldContainer)[0], 'before');
          domConstruct.place(viewButton, newName, 'before');
        } else {
          domConstruct.place(newName, query('button.downloadFileButton', fieldContainer)[0], 'before');
        }
        domConstruct.place(newButton, newName, 'before');
        if (deleteButton) {
          domConstruct.place(deleteButton, newButton, 'after');
        }
      }
      const isImage = imageFileTypes.indexOf(f.contentType || f.jsonEntityData.contentType.trim()) !== -1;
      if (!isImage) {
        return;
      }
      const imageSizePreview = query('input.imageSizePreview', fieldContainer)[0].value;
      const fieldSize_small = domAttr.has(fieldContainer, 'data-field-size-small');
      if (!fieldSize_small) {
        const newDom = domConstruct.create('img', {
          class: 'fileImagePreview fileDom',
          'file-id': `${f.jsonEntityData.id}`,
          src: `../view/v-download-attached.view?id=${f.persistentId || f.id}`
        });
        if (typeof +newDom === 'number') {
          newDom.style['max-width'] = `${imageSizePreview}%`;
          newDom.style.height = 'auto';
        }
        newDom.style.display = '';
        domConstruct.place(newDom, newName, 'after');
      }
    });
  }

  function onFileDeleted(fieldContainer, fileId) {
    const inputFileId = query('input.inputFileId', fieldContainer)[0];
    const inputFileContentType = query('input.inputFileContentType', fieldContainer)[0];
    const inputFileExtension = query('input.inputFileExtension', fieldContainer)[0];
    const inputFileName = query('input.inputFileName', fieldContainer)[0];
    const uploader = registry.byId(`${fieldContainer.id}_uploader`);
    if (inputFileId.value.split(',').length <= 1) {
      inputFileId.value = '-1';
      inputFileName.value = '';
      inputFileContentType.value = '';
      inputFileExtension.value = '';
      if (uploader) {
        uploader.uploader.fileInput.value = null;
      }
      const fileContainersClazzDoms = query('.fileDom', fieldContainer);
      for (const dom1 of fileContainersClazzDoms) {
        domConstruct.destroy(dom1);
      }
      return;
    }
    const index = inputFileId.value.replaceAll(' ', '').split(',').indexOf(fileId);

    inputFileId.value = array.filter(inputFileId.value.split(','), (_, i) => i !== index).join(',');
    inputFileName.value = array.filter(inputFileName.value.split(','), (_, i) => i !== index).join(',');
    inputFileContentType.value = array.filter(inputFileContentType.value.split(','), (_, i) => i !== index).join(',');
    inputFileExtension.value = array.filter(inputFileExtension.value.split(','), (_, i) => i !== index).join(',');
    const fileContainersDoms = query(`[file-id="${fileId}"]`, fieldContainer);
    for (const dom1 of fileContainersDoms) {
      domConstruct.destroy(dom1);
    }
  }
  function showErrorCameraNotAvailable() {
    setTimeout(() => {
      require(['core'], (core) => {
        if (MediaQueries.isIPhoneDevice()) {
          core.error(i18n.cameraNotAvailableIPhone);
        } else {
          core.error(i18n.genericCameraNotAvailable);
        }
      });
    }, 1000);
  }
  function checkCameraAvailability() {
    if (MediaQueries.isDesktop()) {
      return;
    }
    try {
      if (!navigator.mediaDevices?.getUserMedia) {
        showErrorCameraNotAvailable();
        return;
      }
      navigator.mediaDevices
        .getUserMedia({
          video: true
        })
        .then(
          async () => {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const cameras = devices.filter((device) => device.kind === 'videoinput');
            if (cameras.length === 0) {
              showErrorCameraNotAvailable();
            }
          },
          (e) => {
            console.error('Error checking if camera is available', e);
            showErrorCameraNotAvailable();
          }
        );
    } catch (e) {
      console.error('Error checking if camera is available', e);
      showErrorCameraNotAvailable();
    }
  }
  function buildFileUploadFields() {
    const def = new Deferred();
    const enableOpenPdfFiles = dom.byId('enableOpenPdfFiles') && dom.byId('enableOpenPdfFiles').value === 'true';
    const documentTypeId = dom.byId('documentTypeId').value || null;
    const surveyType = dom.byId('surveyType').value || null;
    let testForCamera = false;
    try {
      for (const fieldContainer of query('#fields .fileUpload')) {
        if (!enableOpenPdfFiles) {
          for (const button of query('button.viewFileButton', fieldContainer)) {
            domClass.add(button, 'displayNone');
          }
        }
        const uploadFileDom = query('div.uploadFile', fieldContainer)[0];
        const required = domClass.contains(uploadFileDom, 'required');
        let inputFile;
        const container = domConstruct.create('div', { class: 'uploader-container' }, uploadFileDom);
        const disabled = SurveyCaptureDomUtils.isFieldDisabled(fieldContainer);
        if (disabled) {
          for (const buttonDelete of query('button.deleteFileButton', fieldContainer)) {
            domClass.add(buttonDelete, 'displayNone');
          }
        } else {
          try {
            const allowMobileUploadingFrom = fieldContainer.getAttribute('data-allow-mobile-uploading-from');
            let accept = null;
            let capture = null;
            if (typeof allowMobileUploadingFrom !== 'undefined') {
              if (allowMobileUploadingFrom === '0') {
                // Camera and media (only for images)
                accept = 'image/*';
              } else if (allowMobileUploadingFrom === '1') {
                // Only camera
                accept = 'image/*';
                capture = 'user';
              }
              testForCamera = true;
            }
            const uploader = new Uploader({
              id: `${fieldContainer.id}_uploader`,
              onChange: (fileInfo) => {
                onFileChanged(fieldContainer, fileInfo);
              },
              onReady: () => {
                inputFile = uploader.uploader.fileInput;
                labelFile = uploader.uploader.lblFile;
                if (required) {
                  domClass.add(inputFile, 'required');
                }
                if (domClass.contains(query('.surveyPage')[0], 'previewEmpty')) {
                  domConstruct.create(
                    'span',
                    {
                      innerHTML: i18n.noFilesAddedEmpty,
                      class: 'noFilesAddedEmpty'
                    },
                    uploadFileDom
                  );
                }
                inputFile.style.backgroundColor = 'lightgray';
                labelFile.style.backgroundImage = 'none';
                if (!uploader.get('dashedStyle')) {
                  domConstruct.create(
                    'igx-icon',
                    {
                      innerHTML: 'attach_file',
                      class: 'material-icons igx-icon'
                    },
                    uploader.uploader.domNode
                  );
                }
              },
              showLoader: loader.showLoader,
              hideLoader: loader.hideLoader,
              multiple: true,
              dashedStyle: true,
              lblFile: i18n.dashedFileUpload,
              accept: accept,
              capture: capture
            });
            uploader.placeAt(container);
          } catch (e) {
            console.error('Error building file upload fields', e);
          }
        }
        domConstruct.create(
          'a',
          {
            id: 'fileLink',
            href: 'javascript: void(0);',
            style: {
              display: 'none'
            },
            download: 'download'
          },
          fieldContainer
        );
      }
      def.resolve(true);
    } catch (e) {
      console.error('Error building file upload fields', e);
      def.reject(e);
    }
    if (testForCamera) {
      checkCameraAvailability();
    }
    return def.promise;
  }

  function buildCatalogHierarchyFields() {
    const def = new Deferred();
    try {
      const outstandingSurveyId = +dom.byId('outstandingid').value || null;
      const widgets = [];
      for (const fieldContainer of query('#fields .catalogSelect[data-catalog-subtype="catalog-hierarchy"]')) {
        widgets.push(buildCatalogHierarcyField(fieldContainer, outstandingSurveyId));
      }
      if (widgets.length <= 0) {
        def.resolve(true);
        return def.promise;
      }
      all(widgets).then(
        () => {
          def.resolve(true);
        },
        (e) => {
          def.reject(e);
        }
      );
    } catch (e) {
      console.error('Error building catalog hierarchy fields', e);
      def.reject(e);
    }
    return def.promise;
  }

  function buildCatalogHierarcyField(fieldContainer, outstandingSurveyId) {
    const def = new Deferred();
    const dataContainer = query('div.catalog-hierarchy-component', fieldContainer);
    if (dataContainer.length === 0) {
      console.error(`Invalid questionnaire, catalog-hierarchy-component div must exist for \'catalog-hierarchy\' field ${fieldContainer.id}`);
      def.reject(false);
      return def.promise;
    }
    if (!window.serializedCatalogData) {
      console.error(`Invalid questionnaire, serializedCatalogData must exist for \'catalog-hierarchy\' field ${fieldContainer.id}`);
      def.reject(false);
      return def.promise;
    }
    const fieldSerializedCatalogData = window.serializedCatalogData[fieldContainer.id];
    if (!fieldSerializedCatalogData) {
      console.error(`Invalid questionnaire, fieldSerializedCatalogData must exist for \'catalog-hierarchy\' field ${fieldContainer.id}`);
      def.reject(false);
      return def.promise;
    }
    let catalogData = null;
    try {
      catalogData = JSON.parse(fieldSerializedCatalogData);
    } catch (e) {
      console.error('Invalid external catalog data', e);
      console.error('External catalog data:', fieldSerializedCatalogData);
    }
    if (catalogData === null || typeof catalogData === 'undefined' || catalogData === '' || catalogData.length === 0) {
      def.reject(false);
      return def.promise;
    }
    return buildCatalogHierarchyWidget(catalogData, dataContainer, fieldContainer, outstandingSurveyId);
  }

  function buildCatalogHierarchyWidget(catalogData, dataContainer, fieldContainer, outstandingSurveyId) {
    const def = new Deferred();
    require([
      'bnext/administrator/surveys/ConditionalHandler',
      'bnext/administrator/surveys/ConditionalExpirationFieldsValidator',
      'bnext/administrator/surveys/FormWeightingHandler'
    ], (ConditionalHandler, ConditionalExpirationFieldsValidator, FormWeightingHandler) => {
      let answers = null;
      if (window.catalogDataAnswers?.[fieldContainer.id]) {
        answers = window.catalogDataAnswers[fieldContainer.id];
      }
      const disabled = SurveyCaptureDomUtils.isFieldDisabled(fieldContainer);
      const required = domClass.contains(dataContainer[0], 'required');
      const fieldId = domAttr.get(fieldContainer, 'data-field-id');
      const searchServiceStore = `Request.Survey.Capture.action?id=${outstandingSurveyId}&fieldId=${fieldId}&level={level}`;
      const componentWidget = new CatalogHierarchy({
        id: `${fieldContainer.id}_hierarchy`,
        required: required,
        catalogData: catalogData,
        floatingGridSize: dom.byId('floatingGridSize').value,
        searchServiceStore: searchServiceStore,
        searchMethodName: 'getCatalogByLevelRows',
        answers: answers,
        disabled: disabled
      });
      componentWidget.on(
        'change',
        lang.hitch(this, async (value) => {
          await ConditionalHandler.onFieldValueChange({ value: value }, fieldId, 'hierarchy');
          ConditionalExpirationFieldsValidator.onHierarchyChange(value, fieldId);
          await FormWeightingHandler.onWeightingValueChange(fieldId);
        })
      );
      const componentContainer = domConstruct.create('div', { class: 'hierarchy-component-container' }, dataContainer[0]);
      componentWidget.placeAt(componentContainer);
      componentWidget.autoAssignValue(0);
      def.resolve(true);
    });
    return def.promise;
  }
  function addOptionToCatalog(option, propertyValue, propertyLabel, catalogData, catalogDataIndex) {
    if (!option) {
      return;
    }
    const item = {};
    const innerText = option.innerText?.trim();
    item[propertyValue] = option.value;
    item[propertyLabel] = innerText === null || innerText === '' ? i18n.defaultEmptyColumn : innerText;
    catalogData.push(item);
    catalogDataIndex[option.value] = item;
  }
  function buildCatalogFields() {
    const def = new Deferred();
    try {
      const outstandingSurveyId = +dom.byId('outstandingid').value || null;
      for (const fieldContainer of query('#fields .catalogSelect[data-catalog-subtype="catalog"]')) {
        const selectNode = query('select', fieldContainer);
        if (selectNode.length === 0) {
          console.error(`Invalid questionnaire, select must exist for \'catalog\' field ${fieldContainer.id}`);
          continue;
        }
        const required = domClass.contains(selectNode[0], 'required');
        const componentContainer = domConstruct.create('div', { class: 'catalog-container' }, selectNode[0].parentNode);
        const disabled = SurveyCaptureDomUtils.isFieldDisabled(fieldContainer);
        const catalogData = [];
        const catalogDataIndex = {};
        const options = query('option', selectNode[0]);
        const propertyLabel = domAttr.get(selectNode[0], 'data-property-label');
        const propertyValue = domAttr.get(selectNode[0], 'data-property-value');
        array.forEach(options, (option) => {
          addOptionToCatalog(option, propertyValue, propertyLabel, catalogData, catalogDataIndex);
        });
        const hasInitialData = catalogData.length > 0;
        const searchGridActivated = +domAttr.get(selectNode[0], 'data-catalog-contents-count') > 250;
        const fieldId = domAttr.get(fieldContainer, 'data-field-id');
        const searchServiceStore = `Request.Survey.Capture.action?id=${outstandingSurveyId}&fieldId=${fieldId}&level=0`;
        const selectWidget = new SelectWithSearch({
          id: `${fieldContainer.id}_catalogFilteringSelect`,
          disabled: disabled,
          required: required,
          searchGridActivated: searchGridActivated,
          searchServiceStore: searchServiceStore,
          fields: [
            {
              column: propertyLabel,
              label: i18n.colNameDescription
            }
          ],
          floatingGridSize: dom.byId('floatingGridSize').value,
          searchMethodName: 'getCatalogByLevelRows',
          idValueAttr: propertyValue,
          searchAttr: propertyLabel,
          data: catalogData
        });
        selectWidget.on('change', (event) => {
          if (event.selectValue !== selectNode[0].value) {
            selectNode[0].value = event.selectValue;
            if (catalogDataIndex[event.selectValue]) {
              selectNode[0].value = event.selectValue;
            } else {
              if (event.value) {
                domConstruct.create(
                  'option',
                  {
                    value: event.value[propertyValue],
                    innerHTML: event.value[propertyLabel]
                  },
                  selectNode[0]
                );
                catalogData.push(event.value);
                selectNode[0].value = event.selectValue;
                catalogDataIndex[event.selectValue] = event.value;
                selectWidget.set('data', catalogData);
              } else if (hasInitialData && !selectWidget.getDisplayedValue()) {
                const defaultValue = catalogData[0][propertyValue];
                selectNode[0].value = defaultValue;
                selectWidget.set('selectValue', defaultValue);
              }
            }
          }
        });
        selectWidget.on('dataRefreshed', (data) => {
          if (data) {
            array.forEach(data, (item) => {
              if (catalogDataIndex[item[propertyValue]]) {
                return;
              }
              domConstruct.create(
                'option',
                {
                  value: item[propertyValue],
                  innerHTML: item[propertyLabel]
                },
                selectNode[0]
              );
              catalogData.push(item);
              catalogDataIndex[item[propertyValue]] = item;
            });
            selectWidget.set('data', catalogData);
          }
        });
        selectWidget.set('selectValue', selectNode[0].value);
        on(selectNode[0], 'change, bnextChange', () => {
          if (selectWidget.get('selectValue') !== selectNode[0].value) {
            // If no data available, insert the selected one
            const autoselectsingleanswer = selectNode[0].getAttribute('data-autoselectsingleanswer');
            if (catalogData.length === 0) {
              const option = selectNode[0].options[0];
              addOptionToCatalog(option, propertyValue, propertyLabel, catalogData, catalogDataIndex);
              selectWidget.set('data', catalogData);
            } else if (autoselectsingleanswer && autoselectsingleanswer === 'false' && catalogData.length === 1) {
              const option = selectNode[0].options[1];
              addOptionToCatalog(option, propertyValue, propertyLabel, catalogData, catalogDataIndex);
              selectWidget.set('data', catalogData);
            }
            selectWidget.set('selectValue', selectNode[0].value);
          }
        });
        selectWidget.placeAt(componentContainer);
        domClass.add(selectNode[0], 'displayNone');
      }
      def.resolve(true);
    } catch (e) {
      console.error('Error building catalog fields', e);
      def.reject(e);
    }
    return def.promise;
  }

  function buildFreeTextFields() {
    const def = new Deferred();
    try {
      for (const anchor of query('#fields .freeText a')) {
        const target = domAttr.get(anchor, 'target');
        if (target === null || typeof target === 'undefined' || target === '') {
          domAttr.set(anchor, 'target', '_blank');
        }
      }
      def.resolve(true);
    } catch (e) {
      console.error('Error building free text fields', e);
      def.reject(e);
    }
    return def.promise;
  }
  function buildStaticValueFields() {
    const def = new Deferred();
    try {
      for (const inputDom of query('#fields input[data-default-value]')) {
        const defaultValue = domAttr.get(inputDom, 'data-default-value');
        const fieldDom = dom.byId(`field_${domAttr.get(inputDom, 'data-id')}`);
        const weightType = +domAttr.get(fieldDom, 'data-weighting-type');
        const isStaticWeight = weightType === SurveyWeightingType.INIT;
        if (isStaticWeight) {
          const isStaticValue = defaultValue.match(/^\s*(.+?)\s*\|\s*([0-9]+|[0-9]+\.[0-9]+)\s*$/i);
          if (isStaticValue) {
            domAttr.set(fieldDom, 'title', isStaticValue[1]);
            domAttr.set(inputDom, 'title', isStaticValue[1]);
            inputDom.value = isStaticValue[2];
          }
        }
      }
      def.resolve(true);
    } catch (e) {
      console.error('Error building free text fields', e);
      def.reject(e);
    }
    return def.promise;
  }
  function initFields() {
    const def = new Deferred();
    try {
      switch (dom.byId('task').value.toUpperCase()) {
        case 'ACCEPT':
        case 'PREVIEW':
          if (
            !dom.byId('message_reopen_container') &&
            !dom.byId('message_reopen_available') &&
            !dom.byId('message_adjustment_authorization_container') &&
            !dom.byId('message_cancel_container')
          ) {
            if (isArchived) {
              SurveyCaptureMessages.previewMsg(i18n.messageArchived);
            } else {
              SurveyCaptureMessages.previewMsg(i18n.message);
            }
          } else {
            SurveyCaptureMessages.onHideMsgs();
            const reason = dom.byId('activeReopenReason').value || dom.byId('activeAdjustmentReason').value || dom.byId('activeCancelationReason').value;
            for (const elem of query('.message_preview_subtitle')) {
              elem.innerHTML = elem.innerText
                .replace('{reopenRequestorUserName}', dom.byId('reopenRequestorUserName').value)
                .replace('{adjustmentRequestorUserName}', dom.byId('adjustmentRequestorUserName').value)
                .replace('{cancelRequestorUserName}', dom.byId('cancelationRequestorUserName').value)
                .replace('{reason}', `<i>${reason}</i>`); // <--------- ToDo: Colocar una llave diferente para cada tipo de solicitud
            }
          }
          domClass.add(dojoWindow.body(), 'disabled_body');
          for (const elem of query('input[type=text],textarea,select,input[type=radio],input[type=checkbox],input[type=file]', dom.byId('form'))) {
            elem.disabled = true;
          }
          for (const b of query('input[type=button]', dom.byId('buttonContainerSpan'))) {
            switch (b.id) {
              case 'terminarBtn':
              case 'masTardeBtn':
              case 'signFormBtn':
                domClass.add(b, 'displayNone');
                break;
            }
          }
          if (dom.byId('outstandingid').value === '-1') {
            query('.surveyPage').addClass('previewEmpty');
          }
          if (dom.byId('finalScore')) {
            domClass.add(dom.byId('finalScore'), 'displayNone');
          }
          break;
        case 'FILL':
          require(['core', 'bnext/angularNavigator', 'bnext/_base/url-util', 'bnext/angularActivity'], (core, angularNavigator, urlUtil, angularActivity) => {
            const $GET = urlUtil.getUrlVars();
            const activityId = $GET.activityId;
            const impersonateUserId = $GET.impersonateUserId;
            if (activityId) {
              angularActivity.bannerAttendActivity({ activityId: activityId }, 'formularie').then(
                (result) => {
                  if (result) {
                    core.dialog(i18n.messageExit).then(() => {
                      angularNavigator.navigate('pendings');
                    });
                  }
                },
                () => {}
              );
            } else if (impersonateUserId) {
              callMethod({
                url: 'UserService.action',
                method: 'loadUserRef',
                params: [impersonateUserId]
              }).then((result) => {
                if (result) {
                  SurveyCaptureMessages.impersonateMsg(i18n.impersonateMessage.replace(':userName', result.description));
                }
              });
            } else {
              const isAdmin = dom.byId('authRole').value === 'ADMIN';
              if (isAdmin) {
                SurveyCaptureMessages.impersonateMsg(i18n.continueFillWithAdminRole);
              }
            }
          });
      }
      on(form, '[contenteditable]:dblclick', (e) => {
        if (dom.byId('task') && dom.byId('task').value === 'preview') {
          return;
        }
        const inputDom = e.target || e.srcElement;
        if (domClass.contains(inputDom, 'alphaNumeric') && inputDom.tagName !== 'TEXTAREA' && inputDom.tagName !== 'INPUT') {
          require(['core'], function (core) {
            core.ckeditorDialog(this, 'Editar').then(function (editor) {
              editor.editor.setData(inputDom.innerHTML);
              const e = editor.on(
                'accept',
                lang.hitch(this, (evt) => {
                  inputDom.innerHTML = evt.data;
                  e.remove();
                  d.remove();
                })
              );
              const d = editor.on('cancel', () => {
                e.remove();
                d.remove();
              });
            });
          });
        }
      });

      $('.autoresize').keyup(function () {
        autoresize(this);
      });
      $(".boxTextAnswer.textarea[contenteditable='true'][maxlength]").on('keyup paste', function (event) {
        const cntMaxLength = Number.parseInt($(this).attr('maxlength'));
        if (
          $(this).text().length >= cntMaxLength &&
          event.keyCode !== 8 &&
          event.keyCode !== 37 &&
          event.keyCode !== 38 &&
          event.keyCode !== 39 &&
          event.keyCode !== 40
        ) {
          event.preventDefault();
          // Elimina el texto mayor a la longitud maxima.
          $(this).html((i, currentHtml) => currentHtml.substring(0, cntMaxLength - 1));
          // Envia el cursor al final del texto
          const range = document.createRange();
          const sel = w.getSelection();
          range.selectNodeContents(this);
          range.collapse(false);
          sel.removeAllRanges();
          sel.addRange(range);
          this.focus();
          range.detach();
        }
      });
      if (dom.byId('actionsSeedDialog')) {
        require(['core'], (core) => {
          core.dijitChangeIE('actionsSeedDialog');
        });
      }
      def.resolve(true);
    } catch (e) {
      console.error('Error initializing fields', e);
      def.reject(e);
    }
    return def.promise;
  }

  return SurveyCaptureFields;
});
