define([
  'dojo/query',
  'dojo/dom',
  'dojo/_base/array',
  'dojo/dom-class',
  'bnext/survey/_util/ConditionalType',
  'dijit/registry',
  'bnext/administrator/surveys/SurveyCaptureSave',
  'bnext/administrator/surveys/SurveyCaptureLoad',
  'bnext/administrator/surveys/ConditionalFieldsUtils',
  'bnext/administrator/surveys/ConditionalFieldsHandler',
  'dojo/dom-attr'
], (query, dom, array, domClass, ConditionalType, registry, SurveyCaptureSave, SurveyCaptureLoad, ConditionalFieldsUtils, ConditionalFieldsHandler, domAttr) => {
  const ConditionalExpirationFieldsValidator = {
    debug: false,
    onFieldOptionValueChange: onFieldOptionValueChange,
    onHierarchyChange: onHierarchyChange,
    setupExpirationFields: setupExpirationFields,
    updateExpirationFieldCache: updateExpirationFieldCache
  };
  let FIELDS_DATA_BY_ID = {};
  let CONDITIONS_BY_FIELD_ID = {};
  const SHOWN_BY_FIELD_ID = {};

  function setupField(field) {
    const config = FIELDS_DATA_BY_ID[field.id];
    if (!config) {
      return;
    }
    const answers = config.loadAnswersDom;
    if (answers && answers.length > 0) {
      for (const answer of answers) {
        switch (config.type) {
          case 'menuSelect': {
            const selectData = getMenuSelectData(config, field, answer);
            applyFieldValueChange(selectData);
            break;
          }
          case 'multiSelect': {
            const multiSelectData = getMultiSelectData(config, field, answer);
            applyFieldValueChange(multiSelectData);
            break;
          }
          case 'exclusiveSelectYesNo':
          case 'exclusiveSelect': {
            const radioData = getRadioData(config, field, answer);
            applyFieldValueChange(radioData);
            break;
          }
        }
      }
    }
  }
  function isFieldSkippable(triggerConfig, isLoadMode) {
    return !triggerConfig || (!isLoadMode && !triggerConfig.isEditable);
  }
  function getHierarchyData(value, fieldId) {
    const id = getFieldId(fieldId);
    return {
      answerValue: value,
      isChecked: false,
      id: id,
      fieldId: +fieldId
    };
  }
  function getMenuSelectData(config, field, answer) {
    let selectValue = answer.value;
    if (selectValue !== null && typeof selectValue !== 'undefined' && selectValue !== '') {
      selectValue = +selectValue;
    } else {
      selectValue = null;
    }
    return {
      answerValue: selectValue,
      fieldId: +config.id,
      id: field.id
    };
  }
  function getMultiSelectData(config, field, answer) {
    return {
      answerValue: +answer.value,
      isChecked: answer.checked === true,
      fieldId: +config.id,
      id: field.id
    };
  }
  function getRadioData(config, field, answer) {
    let inputValue = false;
    if (answer.checked) {
      inputValue = +answer.value;
    }
    return {
      answerValue: inputValue,
      fieldId: +config.id,
      id: field.id
    };
  }
  function onHierarchyChange(value, fieldId) {
    const id = getFieldId(fieldId);
    const config = FIELDS_DATA_BY_ID[id];
    if (!config || !config.isShown) {
      return;
    }
    const data = getHierarchyData(value, fieldId);
    onFieldValueChange('hierarchy', data);
  }
  function onFieldOptionValueChange(input, fieldId, typeSelection) {
    const id = getFieldId(fieldId);
    const config = FIELDS_DATA_BY_ID[id];
    if (!config || !config.isShown) {
      return;
    }
    const data = {
      answerValue: +input.value,
      isChecked: input.checked === true,
      id: id,
      fieldId: +fieldId
    };
    onFieldValueChange(typeSelection, data);
  }
  function onFieldValueChange(typeSelection, data) {
    const result = applyFieldValueChange(data);
    if (ConditionalExpirationFieldsValidator.debug) {
      if (result.appliedFieldIds.length > 0) {
        console.log(`Applied changes fields of type ${typeSelection}`, result.appliedFieldIds);
      }
    }
    if (result.shownUnavailableFieldIds.length > 0 || result.hiddenUnavailableFieldIds.length > 0) {
      if (result.shownUnavailableFieldIds.length > 0) {
        if (ConditionalExpirationFieldsValidator.debug) {
          console.log(`Shown unavailable fields to apply changes of type ${typeSelection}`, result.shownUnavailableFieldIds);
        }
        recalculateExpiration(result.shownUnavailableFieldIds, data, typeSelection, true);
      } else if (result.hiddenUnavailableFieldIds.length > 0) {
        if (ConditionalExpirationFieldsValidator.debug) {
          console.log(`Hidden unavailable fields to apply changes of type ${typeSelection}`, result.hiddenUnavailableFieldIds);
        }
        recalculateExpiration(result.hiddenUnavailableFieldIds, data, typeSelection, false);
      }
    }
  }
  function recalculateExpiration(fieldsData, data, typeSelection, add) {
    if (!fieldsData || fieldsData.length === 0) {
      return;
    }
    for (const field of fieldsData) {
      const fieldDomId = field.fieldDomId ? field.fieldDomId : field;
      if (!add && data) {
        const fieldId = data.fieldId;
        const answerValue = data.answerValue;
        removeDaysFromField(fieldDomId, fieldId, answerValue, typeSelection);
      }
      calculateExpirationDays(fieldDomId);
    }
  }
  function calculateExpirationDays(fieldDomId) {
    const fieldConfig = FIELDS_DATA_BY_ID[fieldDomId];
    const fieldSection = SHOWN_BY_FIELD_ID[fieldDomId];
    fieldConfig.daysToExpire = Number.MAX_SAFE_INTEGER;
    fieldConfig.daysToNotifyBeforeExpiration = Number.MAX_SAFE_INTEGER;
    for (const answerFieldId in fieldSection) {
      const answerFields = fieldSection[answerFieldId];
      for (const answerOptionId in answerFields) {
        const answerOption = answerFields[answerOptionId];
        const daysToExpire = answerOption.daysToExpire;
        const daysToNotifyBeforeExpiration = answerOption.daysToNotifyBeforeExpiration;
        if (daysToExpire > 0 && daysToNotifyBeforeExpiration > 0) {
          if (fieldConfig.daysToExpire > daysToExpire) {
            fieldConfig.daysToExpire = daysToExpire;
          }
          if (fieldConfig.daysToNotifyBeforeExpiration > daysToNotifyBeforeExpiration) {
            fieldConfig.daysToNotifyBeforeExpiration = daysToNotifyBeforeExpiration;
          }
        }
      }
    }
    // Set attribute to the dom of the question
    if (fieldSection && fieldConfig.daysToExpire !== Number.MAX_SAFE_INTEGER && fieldConfig.daysToNotifyBeforeExpiration !== Number.MAX_SAFE_INTEGER) {
      fieldConfig.domNode.setAttribute('data-conditional-expiration-daysToExpire', fieldConfig.daysToExpire);
      fieldConfig.domNode.setAttribute('data-conditional-expiration-daysToNotifyBeforeExpiration', fieldConfig.daysToNotifyBeforeExpiration);
    } else {
      fieldConfig.domNode.removeAttribute('data-conditional-expiration-daysToExpire');
      fieldConfig.domNode.removeAttribute('data-conditional-expiration-daysToNotifyBeforeExpiration');
    }
  }
  function applyFieldValueChange(data) {
    const config = FIELDS_DATA_BY_ID[data.id];
    if (!config) {
      return getFieldsResult();
    }
    switch (config.type) {
      case 'menuSelect':
      case 'exclusiveSelectYesNo':
      case 'exclusiveSelect':
        return applyChangeByAnswer(data.answerValue, data.fieldId);
      case 'multiSelect':
        if (data.isClear) {
          return getFieldsResult();
        }
        if (data.isChecked) {
          // La respuesta a la que se le hace clic está seleccionada
          return applyChangeByAnswer(data.answerValue, data.fieldId);
        }
        return getFieldsResult();
      case 'catalogSelect':
        if (config.catalogSubtype === 'catalog-hierarchy') {
          return applyHierarchyValueChange(data);
        }
        return getFieldsResult();
      default:
        return getFieldsResult();
    }
  }
  function mergeResult(source, target) {
    if (!source) {
      return;
    }
    target.appliedFieldIds = target.appliedFieldIds.concat(source.appliedFieldIds);
    target.shownUnavailableFieldIds = target.shownUnavailableFieldIds.concat(source.shownUnavailableFieldIds);
    target.hiddenUnavailableFieldIds = target.hiddenUnavailableFieldIds.concat(source.hiddenUnavailableFieldIds);
  }
  function getFieldsResult() {
    return {
      appliedFieldIds: [],
      shownUnavailableFieldIds: [],
      hiddenUnavailableFieldIds: []
    };
  }
  function getFieldId(id) {
    return `field_${id}`;
  }

  /**
   * Setup expiration fields
   * @returns {void}
   */
  function setupExpirationFields() {
    try {
      const fields = buildFieldsCache();
      for (const field of fields) {
        setupConditionalField(field);
      }
      for (const field of fields) {
        setupField(field);
      }
      recalculateExpiration(Object.keys(SHOWN_BY_FIELD_ID), null, null, true);
    } catch (e) {
      console.error('Error setting up expiration fields', e);
    }
  }

  /**
   * Update expiration field cache
   * @param {string} fielId The field id
   */
  function updateExpirationFieldCache(fielId) {
    const fieldConfig = FIELDS_DATA_BY_ID[fielId];
    if (!fieldConfig) {
      return;
    }
    fieldConfig.isShown = ConditionalFieldsUtils.isConditionalFieldShown(fieldConfig.domNode);
  }

  function buildFieldsCache() {
    CONDITIONS_BY_FIELD_ID = {};
    FIELDS_DATA_BY_ID = {};
    const fieldDoms = query('.form-field', 'fields');
    for (const field of fieldDoms) {
      const isConditional = domAttr.get(field, 'data-conditional-expiration-question') === 'true';
      const fieldDomId = field.id;
      const sectionId = domAttr.get(field, 'data-section-id') || null;
      const expectedAuthPoolIndex = domAttr.get(field, 'data-fill-authorization-pool-index') || 1;
      const id = domAttr.get(field, 'data-field-id') || null;
      const typeDom = dom.byId(`${fieldDomId}_type`);
      const catalogSubtype = domAttr.get(field, 'data-catalog-subtype');
      const type = typeDom?.value || null;
      const answers = ConditionalFieldsHandler.getAnswers(field, type);
      const loadAnswerDom = ConditionalFieldsUtils.getAnswersDom(field, type, true);
      FIELDS_DATA_BY_ID[fieldDomId] = {
        domNode: field,
        isShown: ConditionalFieldsUtils.isConditionalFieldShown(field),
        isEditable: ConditionalFieldsUtils.isFieldEditable(field),
        sectionId: sectionId,
        expectedAuthPoolIndex: +expectedAuthPoolIndex,
        type: type,
        catalogSubtype: catalogSubtype,
        id: id,
        isConditional: isConditional,
        answers: answers,
        loadAnswersDom: loadAnswerDom
      };
      const conditionals = parseConditionalByField(fieldDomId);
      if (!conditionals || conditionals.length === 0) {
        continue;
      }
      if (!isConditional) {
        continue;
      }
      for (const conditional of conditionals) {
        conditional.fieldDomId = fieldDomId;
        const questionId = conditional.conditionalQuestionId;
        const answerId = conditional.conditionalAnswerId;
        let question;
        if (conditional.conditionalType === ConditionalType.SURVEY_ITEM) {
          if (!CONDITIONS_BY_FIELD_ID[questionId]) {
            CONDITIONS_BY_FIELD_ID[questionId] = {};
          }
          question = CONDITIONS_BY_FIELD_ID[questionId];
          if (!question[answerId]) {
            question[answerId] = [];
          }
          const questionValue1 = {
            fieldDomId: fieldDomId,
            daysToExpire: conditional.daysToExpire,
            daysToNotifyBeforeExpiration: conditional.daysToNotifyBeforeExpiration
          };
          question[answerId].push(questionValue1);
        } else {
          if (!CONDITIONS_BY_FIELD_ID[questionId]) {
            CONDITIONS_BY_FIELD_ID[questionId] = {};
          }
          question = CONDITIONS_BY_FIELD_ID[questionId];
          if (!question[fieldDomId]) {
            question[fieldDomId] = [];
          }
          const questionValue2 = {
            value: conditional.value,
            daysToExpire: conditional.daysToExpire,
            daysToNotifyBeforeExpiration: conditional.daysToNotifyBeforeExpiration
          };
          question[fieldDomId].push(questionValue2);
        }
      }
    }
    return fieldDoms;
  }
  function applyChangeByAnswer(answerValue, fieldId) {
    const triggersByField = CONDITIONS_BY_FIELD_ID[fieldId];
    if (!triggersByField || !triggersByField[answerValue]) {
      return getFieldsResult();
    }
    const triggersByAnswer = triggersByField[answerValue];
    if (!triggersByAnswer || triggersByAnswer.length === 0) {
      return getFieldsResult();
    }
    return applyChangeByTriggers(answerValue, triggersByAnswer, fieldId);
  }
  function applyChangeByTriggers(answerValue, triggersByAnswer, fieldId) {
    const result = getFieldsResult();
    if (!triggersByAnswer || triggersByAnswer.length === 0) {
      return result;
    }
    for (const triggerByAnswer of triggersByAnswer) {
      const fieldDomId = triggerByAnswer.fieldDomId;
      addDaysToField(fieldDomId, fieldId, answerValue, triggerByAnswer.daysToExpire, triggerByAnswer.daysToNotifyBeforeExpiration);
      result.shownUnavailableFieldIds.push(fieldDomId);
    }
    return result;
  }
  function addDaysToField(fieldDomId, fieldId, answerValue, daysToExpire, daysToNotifyBeforeExpiration) {
    if (!SHOWN_BY_FIELD_ID[fieldDomId]) {
      SHOWN_BY_FIELD_ID[fieldDomId] = {};
    }
    const fieldConfig = SHOWN_BY_FIELD_ID[fieldDomId];
    if (!fieldConfig[fieldId]) {
      fieldConfig[fieldId] = {};
    }
    const fieldParent = fieldConfig[fieldId];
    fieldParent[answerValue] = {
      daysToExpire: daysToExpire,
      daysToNotifyBeforeExpiration: daysToNotifyBeforeExpiration
    };
  }
  function removeDaysFromField(fieldDomId, fieldId, answerValue, typeSelection) {
    const fieldSection = SHOWN_BY_FIELD_ID[fieldDomId];
    if (fieldSection) {
      if (typeSelection && typeSelection === 'multiple') {
        const field = fieldSection[fieldId];
        if (field[answerValue]) {
          delete field[answerValue];
        }
      } else {
        delete fieldSection[fieldId];
      }
    }
  }
  function setupConditionalField(field) {
    const config = FIELDS_DATA_BY_ID[field.id];
    if (!config) {
      return;
    }
    if (config.type === 'catalogSelect' && config.catalogSubtype === 'catalog-hierarchy') {
      const widget = registry.byId(`${config.domNode.id}_hierarchy`);
      let value = null;
      if (widget) {
        value = widget.get('value');
      }
      const data = getHierarchyData(value, config.id);
      return applyHierarchyValueChange(data);
    }
    /**
     * @type {HTMLElement[]}
     */
    let answers;
    //TODO: Actualmente para el tipo radio solo se tiene en memoria la opción seleccionada, revisar como se pueden tener todos los valores
    if (config.type === 'exclusiveSelectYesNo' || config.type === 'exclusiveSelect') {
      answers = ConditionalFieldsUtils.getAnswersDom(field, config.type, true);
    } else {
      answers = config.loadAnswerDom;
    }
    if (!answers || answers.length === 0) {
      return;
    }
    for (const answer of answers) {
      switch (config.type) {
        case 'menuSelect': {
          const selectData = getMenuSelectData(config, field, answer);
          setupAnswerConditionalField(selectData, true);
          break;
        }
        case 'multiSelect': {
          const multiSelectData = getMultiSelectData(config, field, answer);
          setupAnswerConditionalField(multiSelectData, true);
          break;
        }
        case 'exclusiveSelectYesNo':
        case 'exclusiveSelect': {
          const radioData = getRadioData(config, field, answer);
          setupAnswerConditionalField(radioData, true);
          break;
        }
      }
    }
  }
  function setupAnswerConditionalField(data) {
    const config = FIELDS_DATA_BY_ID[data.id];
    if (!config) {
      return;
    }
    switch (config.type) {
      case 'menuSelect':
      case 'exclusiveSelectYesNo':
      case 'exclusiveSelect':
        setupFieldsTypeByAnswer(data.answerValue, data.fieldId, true);
        break;
      case 'multiSelect':
        if (data.isChecked) {
          setupFieldsTypeByAnswer(data.answerValue, data.fieldId, true);
        }
        break;
    }
  }
  function setupFieldsTypeByAnswer(answerValue, fieldId, isLoadMode) {
    const triggersByField = CONDITIONS_BY_FIELD_ID[fieldId];
    if (!triggersByField || !triggersByField[answerValue]) {
      return;
    }
    const triggersByAnswer = triggersByField[answerValue];
    if (!triggersByAnswer || triggersByAnswer.length === 0) {
      return;
    }
    for (const triggerByAnswer of triggersByAnswer) {
      const triggerConfig = FIELDS_DATA_BY_ID[triggerByAnswer];
      if (isFieldSkippable(triggerConfig, isLoadMode)) {
        continue;
      }
      configureShownByAnswer(triggerConfig, answerValue);
    }
  }
  function configureShownByAnswer(triggerConfig, answerValue) {
    if (!SHOWN_BY_FIELD_ID[triggerConfig.id]) {
      SHOWN_BY_FIELD_ID[triggerConfig.id] = {};
    }
    const config = SHOWN_BY_FIELD_ID[triggerConfig.id];
    config[answerValue] = true;
  }
  function parseConditionalByField(fieldId) {
    if (!window.serializedConditionalsExpiration) {
      console.error(`Invalid questionnaire, serializedConditionalsExpiration must exist for field ${fieldId}`);
      return null;
    }
    const fieldSerializedConditionals = window.serializedConditionalsExpiration[fieldId];
    if (!fieldSerializedConditionals) {
      return null;
    }
    if (typeof fieldSerializedConditionals === 'undefined' || fieldSerializedConditionals === '') {
      return null;
    }
    let conditionals = null;
    try {
      conditionals = JSON.parse(fieldSerializedConditionals);
    } catch (e) {
      console.error('Invalid conditionals data', e);
      console.error('Conditionals data:', fieldSerializedConditionals);
    }
    if (conditionals === null || typeof conditionals === 'undefined' || conditionals === '' || conditionals.length === 0) {
      return null;
    }
    for (const conditional of conditionals) {
      if (conditional && typeof conditional.hierarchyValue === 'string') {
        conditional.hierarchyValue = conditional.hierarchyValue.trim();
      }
    }
    const surveyItems = conditionals.filter((item) => item.conditionalType === ConditionalType.SURVEY_ITEM);
    const hierarchyItems = conditionals.filter((item) => item.conditionalType === ConditionalType.HIERARCHY_VALUE);
    if (hierarchyItems.length === 0) {
      return surveyItems;
    }
    const hierarchyRows = parseHierarchyRows(hierarchyItems);
    return surveyItems.concat(hierarchyRows);
  }
  function getMatchHierarcyFields(data, triggersByField) {
    return Object.keys(triggersByField).map((field) => {
      const days = {
        daysToExpire: Number.MAX_SAFE_INTEGER,
        daysToNotifyBeforeExpiration: Number.MAX_SAFE_INTEGER
      };
      let matchesAny = false;
      if (data.answerValue) {
        matchesAny = triggersByField[field].some((conditionalValue) => getMatchHierarchyByCondition(data, conditionalValue, days));
      }
      return { field: field, matchesAny: matchesAny, days: days };
    });
  }
  function getMatchHierarchyByCondition(data, conditionalData, days) {
    const conditionalValue = conditionalData.value;
    const keysAnswers = Object.keys(conditionalValue);
    return keysAnswers.every((keyAnswer) => {
      let toEval = conditionalValue[keyAnswer];
      let answerValue = data.answerValue[keyAnswer];
      if (typeof answerValue === 'number') {
        toEval = +toEval;
      } else if (typeof answerValue === 'string') {
        answerValue = answerValue.trim();
      }
      if (typeof toEval === 'string') {
        toEval = toEval.trim();
      }
      if (toEval === answerValue) {
        if (conditionalData.daysToExpire !== 0 && conditionalData.daysToNotifyBeforeExpiration !== 0) {
          if (days.daysToExpire > conditionalData.daysToExpire) {
            days.daysToExpire = conditionalData.daysToExpire;
          }
          if (days.daysToNotifyBeforeExpiration > conditionalData.daysToNotifyBeforeExpiration) {
            days.daysToNotifyBeforeExpiration = conditionalData.daysToNotifyBeforeExpiration;
          }
        }
        return true;
      }
      return false;
    });
  }
  function applyHierarchyValueChange(data) {
    const triggersByField = CONDITIONS_BY_FIELD_ID[data.fieldId];
    if (!triggersByField) {
      return getFieldsResult();
    }
    if (Object.keys(triggersByField).length === 0) {
      return getFieldsResult();
    }
    const result = getFieldsResult();
    const matchFields = getMatchHierarcyFields(data, triggersByField);
    const shownFields = matchFields.filter((match) => match.matchesAny).map((config) => config.field);
    if (shownFields.length > 0) {
      const triggersByAnswer = matchFields.map((e) => {
        return {
          fieldDomId: e.field,
          daysToExpire: e.days.daysToExpire,
          daysToNotifyBeforeExpiration: e.days.daysToNotifyBeforeExpiration
        };
      });
      const triggerResult = applyChangeByTriggers(null, triggersByAnswer, data.fieldId);
      mergeResult(triggerResult, result);
    }
    return result;
  }
  function parseHierarchyRows(hierarchyItems) {
    const hierarchyRows = [];
    if (!hierarchyItems || hierarchyItems.length === 0) {
      return hierarchyRows;
    }
    const hierarchyData = {};
    for (const conditional of hierarchyItems) {
      buildHierarchyRow(hierarchyRows, hierarchyData, conditional);
    }
    return hierarchyRows;
  }
  function buildHierarchyRow(hierarchyRows, hierarchyData, conditional) {
    const answerSelector = conditional.conditionalAnswerSelector;
    if (!hierarchyData[answerSelector]) {
      hierarchyData[answerSelector] = {};
    }
    const answerData = hierarchyData[answerSelector];
    if (answerData[conditional.hierarchyRow]) {
      const row = answerData[conditional.hierarchyRow];
      row.value[conditional.hierarchyColumn] = conditional.hierarchyValue;
    } else {
      const newRow = {
        conditionalQuestionId: conditional.conditionalQuestionId,
        conditionalAnswerId: null,
        conditionalType: conditional.conditionalType,
        daysToExpire: conditional.daysToExpire,
        daysToNotifyBeforeExpiration: conditional.daysToNotifyBeforeExpiration,
        value: {}
      };
      newRow.value[conditional.hierarchyColumn] = conditional.hierarchyValue;
      answerData[conditional.hierarchyRow] = newRow;
      hierarchyRows.push(newRow);
    }
  }

  return ConditionalExpirationFieldsValidator;
});
