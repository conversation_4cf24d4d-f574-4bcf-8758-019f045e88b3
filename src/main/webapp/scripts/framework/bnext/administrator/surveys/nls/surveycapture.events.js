define(['bnext/i18n!lang/configuracion/nls/configuration.facility'], (facility) => ({
  /***NOTA: Cualquier otra etiqueta que contenga la palabra planta debera ir en el archivo configuration.facility.js.***/
  root: {
    verify: 'Verificando variables mapeadas...',
    message: 'Recuerde que esto es una vista previa y por lo tanto no podrá ver avances ni llenar.',
    messageArchived: 'Recuerde que esto es una vista previa de solo lectura y por lo tanto no podrá llenar.',
    messageControl: 'Será enviado a la pantalla de pendientes. ¿Desea continuar?',
    titleBusinessUnit: facility.facilities,
    colCorp: facility.colCorp,
    noResultFound: 'Sin registros',
    loadingSpecificFields: 'Cargando valores de ":labelObj"...',
    loadingSpecificFieldsSuccesful: 'Se cargaron los valores de ":labelObj" exitosamente.',
    lastComment: 'Último comentario realizado por :userName',
    genericCameraNotAvailable:
      'Cámara no disponible, por favor permita acceso a la cámara en el navegador / revise la configuración de su navegador y dispositivo. Se requiere recargar la página antes de intentar de nuevo.',
    cameraNotAvailableIPhone:
      'Cámara no disponible, por favor permita acceso a la cámara en el navegador / revise la configuración en privacidad de su navegador desde  Preferences / Privacidad. Se requiere recargar la página antes de intentar de nuevo.',
    add: 'Agregar',
    delete: 'Borrar',
    checkInStopwatchButtonTitle: 'Clic para iniciar cronómetro',
    checkOutStopwatchButtonTitle: 'Clic para detener el cronómetro',
    deleteStopwatchButtonTitle: 'Clic para eliminar el registro',
    deleteStopwatchConfirmMessage: '¿Está seguro de que desea eliminar el registro de Timework?',
    deleteStopwatchErrorMessage: 'No se puede eliminar el registro de Timework. Solo el autor del registro puede eliminarlo.',
    disabledDeleteStopwatchButtonTitle: 'El formulario no permite eliminar el registro',
    messageExit: '¿Está seguro de que desea salir?<br>Cualquier información no guardada se perderá.',
    impersonateMessage: 'Usted está llenando el formulario en nombre de :userName',
    continueFillWithAdminRole: 'Puedes continuar con el flujo de este formulario debido a que eres ADMIN',
    colNameDescription: 'Descripción',
    noFilesAdded: 'No se seleccionaron archivos',
    noFilesAddedEmpty: 'Seleccionar archivo',
    invalidMinDateWithTimeSelected: 'Fecha fuera del rango permitido. El valor mínimo es {minDate}.',
    scoreTitle: 'Calificación:',
    incidentTitle: 'Incidencia:',
    defaultEmptyColumn: 'Columna sin información',
    dashedFileUpload: 'Arrastra y suelta un archivo para cargar, o da clic para seleccionar un archivo.',
    forbiddenCron: 'No cuenta con permiso para iniciar cronometro, contacte al Administrador del sistema.'
  },
  'es-CROWN': true,
  en: true,
  'es-CIESA': true
}));
