define(['dojo/_base/declare', 'bnext/_base/HierarchyUtils', 'dojo/_base/lang', 'dojo/_base/array', './HierarchyEntityKeyType', 'core', 'dojo/dom'], (
  declare,
  HierarchyUtils,
  lang,
  array,
  HierarchyEntityKeyType,
  core,
  dom
) => {
  const CatalogDataSource = {
    debug: false,
    id: null,
    getLevelData: function (fields, hierarchyValue, levelField, levelValue, nextField) {
      const serverCount = this._getDescendantCount(fields, hierarchyValue, levelField, levelValue, nextField);
      if (this.debug) {
        console.log(`Catalog datasource of ${this.id} with count of ${serverCount} for field [${nextField.label}][${nextField.column}] .`);
      }
      const searchGridActivated = serverCount === 0 || serverCount > 250;
      if (levelValue === null || typeof levelValue === 'undefined' || levelValue === '') {
        const bean = this._getDescendantBean(fields, hierarchyValue, nextField);
        const pendingRefreshData = !searchGridActivated && serverCount !== bean.count;
        return {
          searchGridActivated: searchGridActivated,
          pendingRefreshData: pendingRefreshData,
          data: bean.data
        };
      }
      const levelColumns = this._getColumnsUpToLevel(fields, levelField.level);
      const valueItems = array.filter(
        levelField.catalogContents.data,
        lang.hitch(this, function (item) {
          return this._compareByProperties(item, hierarchyValue, levelColumns, false);
        })
      );
      if (valueItems.length === 0) {
        const bean = this._getDescendantBean(fields, hierarchyValue, nextField);
        const pendingRefreshData = !searchGridActivated && serverCount !== bean.count;
        return {
          searchGridActivated: searchGridActivated,
          pendingRefreshData: pendingRefreshData,
          data: bean.data
        };
      }
      const id = valueItems[0].id;
      const childItems = array.filter(
        nextField.catalogContents.data,
        lang.hitch(this, (childItem) => childItem.parentId === id)
      );
      const data = this.normalizeData(childItems, nextField.column);
      const pendingRefreshData = !searchGridActivated && serverCount !== childItems.length;
      return {
        searchGridActivated: searchGridActivated,
        pendingRefreshData: pendingRefreshData,
        data: data
      };
    },
    normalizeData: function (data, fieldColumn) {
      if (!data || data.length === 0) {
        return [];
      }
      const notNullData = array.filter(data, (item) => {
        const value = item[fieldColumn];
        return value !== null && typeof value !== 'undefined' && value !== '';
      });
      return this._removeRepeatedData(fieldColumn, notNullData);
    },
    mapValueFromAnswers: function (fields, answers) {
      const value = {};
      const itemColumns = [];
      const fieldsIndex = {};
      let maxLevel = 0;
      if (fields?.length) {
        for (const field of fields) {
          fieldsIndex[field.column] = field;
          if (field.readonly === 0 && maxLevel < field.level) {
            maxLevel = field.level;
          }
        }
      }
      if (answers?.length) {
        for (const answer of answers) {
          value[answer.catalogHierarchyColumn] = answer.catalogOptionValue;
          const field = fieldsIndex[answer.catalogHierarchyColumn];
          if (field?.readonly === 0) {
            itemColumns.push(answer.catalogHierarchyColumn);
          }
        }
      }
      let parentColumns = [];
      if (itemColumns.length > 1) {
        parentColumns = itemColumns.slice(0, itemColumns.length - 1);
      }
      this._defineDescendantData(value, maxLevel, itemColumns, parentColumns);
      return value;
    },
    isNumber: (value) => {
      if (value === null || typeof value === 'undefined') {
        return false;
      }
      if (typeof value === 'number') {
        return true;
      }
      if (typeof value === 'object') {
        return false;
      }
      if (typeof value === 'boolean') {
        return false;
      }
      if (+value === 0) {
        return true;
      }
      const n = +Number(value);
      return n > 0 || n < 0;
    },
    generateAnswers: function (id, catalogData, value) {
      if (!catalogData.hierarchyFields) {
        return [];
      }
      const answers = [];
      for (const field of catalogData.hierarchyFields) {
        let fieldValue = value[field.column];
        if (typeof fieldValue === 'undefined') {
          fieldValue = null;
        }
        if (field.isEntityKeyId && fieldValue !== null && fieldValue !== '' && !this.isNumber(fieldValue)) {
          console.error(`Invalid entity key id for catalog hierarchy field ${id}`);
          fieldValue = -1;
          core.dialog(core.i18n.catalogConfigurationError.replace('{field}', field.label || ''), null, null, core.i18n.titleCatalogoConfigurationError);
          const btnSaveButton = dom.byId('terminarBtn');
          const btnSaveLaterButton = dom.byId('masTardeBtn');
          if (btnSaveButton) {
            btnSaveButton.style.display = 'none';
          }
          if (btnSaveLaterButton) {
            btnSaveLaterButton.style.display = 'none';
          }
        }
        const answer = {
          id: null,
          deleted: 0,
          catalogHierarchyColumn: field.column,
          isCatalogHierarchyEntityKeyId: field.isEntityKeyId,
          catalogOptionLabel: fieldValue,
          catalogOptionValue: fieldValue
        };
        switch (field.entityKeyType) {
          case HierarchyEntityKeyType.BUSINESS_UNIT:
            if (fieldValue !== null) {
              answer.businessUnitId = +fieldValue;
            } else {
              answer.businessUnitId = null;
            }
            break;
          case HierarchyEntityKeyType.BUSINESS_UNIT_DEPARTMENT:
            if (fieldValue !== null) {
              answer.businessUnitDepartmentId = +fieldValue;
            } else {
              answer.businessUnitDepartmentId = null;
            }
            break;
          case HierarchyEntityKeyType.AREA:
            if (fieldValue !== null) {
              answer.areaId = +fieldValue;
            } else {
              answer.areaId = null;
            }
            break;
        }
        answers.push(answer);
      }
      return answers;
    },
    defineCatalogContentsDescendant: function (fields, field) {
      const data = field.catalogContents?.data || [];
      this._defineDataDescendant(fields, field, data);
    },
    defineItemDescendant: function (fields, item, level) {
      const itemColumns = this._getColumnsUpToLevel(fields, level);
      const parentColumns = this._getColumnsUpToLevel(fields, level - 1);
      this._defineDescendantData(item, level, itemColumns, parentColumns);
    },
    refreshHierarchyData: function (fields, field, newData) {
      this._defineDataDescendant(fields, field, newData);
      if (!newData?.length) {
        return;
      }
      const levelColumns = this._getColumnsUpToLevel(fields, field.level);
      for (const newItem of newData) {
        const valueItems = array.filter(
          field.catalogContents.data,
          lang.hitch(this, function (item) {
            return this._compareByProperties(item, newItem, levelColumns, false);
          })
        );
        if (valueItems.length === 0) {
          field.catalogContents.data.push(newItem);
        }
      }
    },
    _defineDataDescendant: function (fields, field, data) {
      if (!data || data.length === 0) {
        return;
      }
      const itemColumns = this._getColumnsUpToLevel(fields, field.level);
      const parentColumns = this._getColumnsUpToLevel(fields, field.level - 1);
      for (const item of data) {
        this._defineDescendantData(item, field.level, itemColumns, parentColumns);
      }
    },
    _defineDescendantData: (item, level, itemColumns, parentColumns) => {
      HierarchyUtils.defineDescendantId(item, itemColumns);
      if (level > 0) {
        HierarchyUtils.defineParentId(item, parentColumns);
      }
    },
    _getDescendantBean: function (fields, hierarchyValue, field) {
      const valueDescendantId = this._generateDescendantId(fields, hierarchyValue, field.level);
      if (valueDescendantId === null || typeof valueDescendantId === 'undefined' || valueDescendantId === '') {
        const data = this.normalizeData(field.catalogContents.data, field.column);
        return {
          data: data,
          count: field.catalogContents.count
        };
      }
      const childItems = array.filter(field.catalogContents.data, (childItem) => childItem.parentId.startsWith(valueDescendantId));
      const data = this.normalizeData(childItems, field.column);
      return {
        data: data,
        count: data.length
      };
    },
    _getDescendantCount: function (fields, hierarchyValue, levelField, levelValue, nextField) {
      const hasLevelValue = levelValue !== null && typeof levelValue !== 'undefined' && levelValue !== '';
      if (hasLevelValue) {
        const levelColumns = this._getColumnsUpToLevel(fields, levelField.level);
        const valueItems = array.filter(
          levelField.catalogContents.data,
          lang.hitch(this, function (item) {
            return this._compareByProperties(item, hierarchyValue, levelColumns, false);
          })
        );
        if (valueItems.length > 0) {
          const count = valueItems[0][`count_${nextField.column}`];
          if (count === null || typeof valueItems[0][`count_${nextField.column}`] === 'undefined') {
            return 0;
          }
          return count;
        }
        return 0;
      }
      if (levelField.level > 0) {
        const ancestorField = this._getAncestorWithValue(fields, hierarchyValue, levelField.level - 1);
        if (ancestorField === null) {
          return this._getDescendantAllCount(levelField, nextField);
        }
        return this._getDescendantCount(fields, hierarchyValue, ancestorField.levelField, ancestorField.levelValue, nextField);
      }
      const maxLevel = fields.length - 1;
      if (levelField.level >= maxLevel) {
        return 0;
      }
      return this._getDescendantAllCount(levelField, nextField);
    },
    _getDescendantAllCount: (levelField, nextField) => {
      if (!levelField.catalogContents.data) {
        return 0;
      }
      let count = 0;
      for (const item of levelField.catalogContents.data) {
        let itemCount = item[`count_${nextField.column}`];
        if (typeof itemCount !== 'number') {
          itemCount = 0;
        }
        count = count + itemCount;
      }
      return count;
    },
    _getAncestorWithValue: function (fields, hierarchyValue, level) {
      if (level < 0) {
        return null;
      }
      const levelField = fields[level];
      const levelValue = hierarchyValue[levelField.column];
      if (levelValue !== null && typeof levelValue !== 'undefined' && levelValue !== '') {
        return {
          levelField: levelField,
          levelValue: levelValue
        };
      }
      return this._getAncestorWithValue(fields, hierarchyValue, level - 1);
    },
    _compareByProperties: (item, parentItem, levelColumns, anyMatch) => {
      const numberLevel = levelColumns.length;
      let counterSame = 0;
      for (let i = 0; i < numberLevel; i++) {
        let valueItem = item[levelColumns[i]];
        if (typeof valueItem === 'undefined') {
          valueItem = null;
        }
        let parentValue = parentItem[levelColumns[i]];
        if (typeof parentValue === 'undefined') {
          parentValue = null;
        }
        if (valueItem === parentValue) {
          counterSame++;
          if (anyMatch) {
            break;
          }
        }
      }
      if (anyMatch) {
        return counterSame > 0;
      }
      return numberLevel === counterSame;
    },
    _getColumnsUpToLevel: (fields, level) => {
      if (level < 0) {
        return [];
      }
      if (!fields) {
        return [];
      }
      const fieldsCount = fields.length;
      const columns = [];
      for (let i = 0; i <= level && i < fieldsCount; i++) {
        columns.push(fields[i].column);
      }
      return columns;
    },
    _generateDescendantId: function (fields, value, level) {
      const columns = this._getColumnsUpToLevel(fields, level);
      return HierarchyUtils.generateDescendantId(value, columns);
    },
    _removeRepeatedData: (fieldColumn, data) => {
      if (!data.length) {
        return [];
      }
      const result = [];
      const addedRows = {};
      for (const item of data) {
        const value = item[fieldColumn];
        if (!addedRows[value]) {
          result.push(item);
          addedRows[value] = item;
        }
      }
      return result;
    }
  };
  return declare([], CatalogDataSource);
});
