define([
  'loader',
  'dojo/dom',
  'dojo/query',
  'dojo/on',
  'dojo/Deferred',
  'bnext/i18n!bnext/administrator/surveys/nls/surveycapture.events',
  'bnext/administrator/surveys/SurveyCaptureMessages',
  'dojo/dom-class',
  'dojo/dom-construct',
  'dojo/dom-attr',
  'bnext/administrator/surveys/SurveyCaptureDomUtils',
  'bnext/administrator/surveys/SurveyCaptureFields'
], (loader, dom, query, on, Deferred, i18n, SurveyCaptureMessages, domClass, domConstruct, domAttr, SurveyCaptureDomUtils, SurveyCaptureFields) => {
  const w = window;
  function checkBusyRequest(fillObject) {
    const defM = new Deferred();
    require(['bnext/administrator/solicitudes/requestBlocker'], (requestBlocker) => {
      requestBlocker.loadRequest(fillObject.id).then(
        (request) => {
          let loggedUserId = 0;
          if (dom.byId('userId')) {
            loggedUserId = dom.byId('userId').value;
          }
          if (dom.byId('loggedUserId')) {
            loggedUserId = dom.byId('loggedUserId').value;
          }
          if (request.isBusy && +request.blockedBy !== +loggedUserId) {
            requestBlocker.showBusyRequest(request);
          } else {
            requestBlocker.pingToSetBusy(request).then(
              (response) => {
                if (+response === 1) {
                  defM.resolve(false);
                } else {
                  requestBlocker.showBusyRequest(request);
                }
              },
              () => {
                console.log('failed lock!');
                requestBlocker.freeRequest(request.id).then(
                  () => {
                    loader.hideLoader();
                    defM.resolve(true);
                  },
                  () => {
                    loader.hideLoader();
                    defM.resolve(true);
                  }
                );
              }
            );
          }
        },
        () => {
          defM.resolve(true);
        }
      );
    });
    return defM.promise;
  }
  function loadFillForm() {
    const def = new Deferred();
    const task = dom.byId('task')?.value?.toUpperCase();
    const preview = task === 'PREVIEW' || task === 'PROGRESS' || task === 'ACCEPT';
    if (preview) {
      def.resolve(false);
      return def.promise;
    }
    require(['bnext/callMethod', 'bnext/administrator/solicitudes/requestBlocker'], (callMethod, requestBlocker) => {
      const requestId = requestBlocker.getRequestId();
      if (requestId === null || typeof requestId === 'undefined') {
        def.resolve(false);
        return;
      }
      callMethod({
        url: 'FillFormService.action',
        method: 'loadFillFormDTO',
        params: [requestId]
      }).then(
        (fillFormDTO) => {
          if (!fillFormDTO) {
            console.error('Error loading fillFormDTO');
            def.resolve(false);
            return;
          }
          checkBusyRequest(fillFormDTO).then(
            () => {
              def.resolve(true);
            },
            () => {
              def.resolve(false);
            }
          );
        },
        (e) => {
          require(['core'], (core) => {
            core.error(e);
          });
          def.resolve(false);
        }
      );
    });
    return def.promise;
  }
  /**
   * Recordar llamar `hideLoader` en el DEFERRED de este metodo
   */
  function onLoadByAnywhere() {
    const def = new Deferred();
    loadFillForm().then(
      () => {
        if (dom.byId('actionsCount')) {
          setActionsCount();
        }
        if (dom.byId('activitiesCount')) {
          setActivitiesCount();
        }

        on(w, 'scroll', SurveyCaptureMessages.onScrollToIt);
        if (w.updateScore) {
          w.updateScore();
        }
        for (const table of query('table')) {
          domClass.add(table, 'stack');
        }
        for (const elem of query('.contenteditable[disabled="disabled"]', dom.byId('form'))) {
          domAttr.set(elem, 'contenteditable', 'false');
          domClass.add(elem, 'disabled');
        }
        SurveyCaptureDomUtils.fixButtonsWidth();
        if (dom.byId('justLoad').value === 'true') {
          for (const elem of query('input[type=text],input[type=number],textarea,select,input[type=radio],input[type=checkbox],input[type=file]', dom.byId('form'))) {
            elem.disabled = true;
          }
          for (const elem of query('input[type=file],.actionBox.scoreContainer', dom.byId('form'))) {
            elem.style.display = 'none';
          }
          for (const elem of query('.contenteditable', dom.byId('form'))) {
            domAttr.set(elem, 'contenteditable', 'false');
          }
        } else {
          for (const elem of query('select option[value=invalid-catalog-value]', dom.byId('form'))) {
            const autoSelectSingleAnswer = domAttr.get(elem.parentElement, 'data-autoSelectSingleAnswer') === 'true';
            if (autoSelectSingleAnswer) {
              domConstruct.destroy(elem);
            }
          }
        }
        const crumbs = dom.byId('selfBreadcrumbs');
        if (crumbs?.value) {
          dom.byId('breadcrumbs').innerHTML = dom.byId('selfBreadcrumbs').value;
        }
        window.addEventListener('visibleFieldsHandleEnd', (e) => {
          try {
            if (dom.byId('justLoad').value !== 'true') {
              SurveyCaptureFields.focusFirstUnanswered();
            }
          } catch (e) {
            console.error(e);
          }
        });
        if (dom.byId('surveyType').value === 'request') {
          SurveyCaptureFields.fillAutoTexts().then(
            () => {
              configureAuditTitles();
              require(['bnext/administrator/surveys/SurveyCaptureVisibleFieldsHandle'], (SurveyCaptureVisibleFieldsHandle) => {
                SurveyCaptureVisibleFieldsHandle().then(
                  () => {
                    def.resolve(true);
                  },
                  (e) => {
                    console.error('Error setting up visible fields', e);
                    def.reject(e);
                  }
                );
              });
            },
            (e) => {
              def.reject(e);
            }
          );
        } else {
          SurveyCaptureFields.buildFields().then(
            () => {
              w.isLoaded = true;
              configureAuditTitles();
              def.resolve(true);
            },
            (e) => {
              def.reject(e);
            }
          );
        }
      },
      (e) => {
        def.reject(e);
      }
    );
    return def.promise;
  }
  function configureAuditTitles() {
    const scoreQuestions = query('.auditScoreTitle');
    if (scoreQuestions !== null && scoreQuestions.length > 0) {
      for (const scoreTitle of scoreQuestions) {
        scoreTitle.innerHTML = i18n.scoreTitle;
      }
    }
    const incidentQuestions = query('.auditIncidentTitle');
    if (incidentQuestions !== null && incidentQuestions.length > 0) {
      for (const incidentTitle of incidentQuestions) {
        incidentTitle.innerHTML = i18n.incidentTitle;
      }
    }
  }
  // noinspection UnnecessaryLocalVariableJS
  const SurveyCaptureLoaded = {
    onLoadByAnywhere: onLoadByAnywhere
  };
  return SurveyCaptureLoaded;
});
