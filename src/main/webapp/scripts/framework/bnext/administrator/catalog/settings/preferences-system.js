/* global $,dojo, myFormDialog */
require([
    'core',
    "dojo/ready",
    'dojo/on',
    'dojo/query',
    'dojo/dom-attr', 
    'dojo/dom-construct',
    'dojo/dom', 
    'dijit/registry',
    "dijit/form/TimeTextBox",
    "bnext/gridComponentUtil",
    'bnext/_base/oidc-config', 'bnext/administrator/catalog/settings/PreferencesActions',
    'dojo/dom-class',
    'bnext/module/FilePicker',
    'bnext/administrator/catalog/settings/preferences-base',
    'dojo/domReady!',
    "dijit/Editor",
    "dojox/editor/plugins/PrettyPrint",
    "dojox/editor/plugins/PageBreak",
    "dojox/editor/plugins/Preview",
    "dojox/editor/plugins/NormalizeIndentOutdent",
    "dojox/editor/plugins/FindReplace",
    "dojox/editor/plugins/PasteFromWord",
    "dojox/editor/plugins/InsertAnchor",
    "dojox/editor/plugins/CollapsibleToolbar",
    "dojox/editor/plugins/Blockquote",
    "dijit/Dialog"
],
function (
    core, ready, on, query, domAttr, domConstruct,
    dom, 
    registry, TimeTextBox, gcUtil,
    OidcConfig, PreferencesActions,
    domClass, FilePicker, preferenceBase
) {
        ready(function () {
        var preferences = new preferenceBase();
        function doEverything(i18n) {
            on(dom.byId('btnPreview'),'click', function () {
                myFormDialog.show();
                dom.byId('imgLogin').innerHTML = registry.byId('login_message').value;
            });
            
            on(dom.byId('btnConfigureCarousel'), 'click', function() {
                core.navigate('menu/settings/preferences/welcome-carousel');
            });
            
            var editor;
            query('label.slow_and_closed', dom.byId('catalog_handle')).forEach(function (labelDom, i) {
                try {
                    var lblFor = domAttr.get(labelDom, 'for');
                    if (lblFor && labelDom.id) {
                        $('#' + lblFor).slideToggle('slow');
                        on(labelDom, 'click', function (e) {
                            query('label.slow_and_closed.open', dom.byId('catalog_handle')).forEach(function (d) {
                                if (d.id && domAttr.get(d, 'for') && d.id !== labelDom.id) {
                                    $('#' + d.id).removeClass('open');
                                    $('#' + domAttr.get(d, 'for')).slideToggle('slow');
                                }
                            });
                            $('#' + lblFor).slideToggle('slow');
                            $('#' + labelDom.id).toggleClass('open');
                        });
                    } else if (lblFor) {
                        console.error('Missconfiguration for slow_and_closed, ID is missing.', lblFor);
                    }
                } catch (e) {
                    console.error(e);
                }
            });
            /*
             * Funcion que decide si se utilizara una autenticacion o no
             * @param {type} value
             */
            var changeSMTPAuth = function (value) {
                value = !isNull(value) ? value : dom.byId('smtpAuthentication').value;
                if (!isNull(value) && value === '1') {
                    changeDisplay('sectionSMTPAuth', 'show');
                } else {
                    changeDisplay('sectionSMTPAuth', 'hide');
                }
            };
            
            function changeGoogleDriveIntegration(value) {
                value = +(!isNull(value) ? value : dom.byId('googleDriveIntegration').value);
                if (value === 1) {
                    changeDisplay('google-drive-integration', 'show');
                } else {
                    changeDisplay('google-drive-integration', 'hide');
                }
            }
            function changeOidcEnabled(value, skipWarning) {
                value = +(!isNull(value) ? value : dom.byId('oidcEnabled').value);
                if (value === 1) {
                    changeDisplay('oidc-enabled', 'show');
                } else {
                    changeDisplay('oidc-enabled', 'hide');
                }
                if (!skipWarning) {
                    showTomcatRestartWarning();
                } 
            }
            function changeEnabledLandingPage(value) {
                value = +(!isNull(value) ? value : dom.byId('enabledLandingPage').value);
                if (value === 1) {
                    changeDisplay('landing-page-enabled', 'show');
                } else {
                    changeDisplay('landing-page-enabled', 'hide');
                }
            }
            function changeExchangeRateConversorEnabled(value) {
                value = +(!isNull(value) ? value : dom.byId('exchangeRateConversorEnabled').value);
                if (value === 1) {
                    changeDisplay('exchange-rate-conversor-enabled', 'show');
                } else {
                    changeDisplay('exchange-rate-conversor-enabled', 'hide');
                }
            }
            function showTomcatRestartWarning() {
                if (!preferences.enabledTomcatRestartWarning) {
                    preferences.enabledTomcatRestartWarning = true;
                }
            }
            
            var changeDisplay = function (sectionClass, action) {
                if (!core.isNull(action) && action === 'show') {
                    query('.' + sectionClass).forEach(function (dom) {
                        dom.style.display = '';
                    });
                } else if (!core.isNull(action) && action === 'hide') {
                    query('.' + sectionClass).forEach(function (dom) {
                        dom.style.display = 'none';
                    });
                }
            };
            
            
            function loadPreferences(resultLoad){
                var langValue = resultLoad.lang + '-' +  resultLoad.locale;
                dom.byId('language').value = langValue;
                if(dom.byId('language').value !== langValue) {
                    domConstruct.create('option', {
                        innerHTML: langValue,
                        value: langValue
                    }, dom.byId('language'));
                    dom.byId('language').value = langValue;
                }
                if (dom.byId('isAreaCustomFieldsEnabled')) {
                    toggleAreaCustomVields(dom.byId('isAreaCustomFieldsEnabled').value === 'true');
                }
                registry.byId('welcome_message').set('value', resultLoad.welcomeMessage);
                registry.byId('login_message').set('value', resultLoad.loginMessage || '');
                dom.byId('imgLogin').innerHTML =  resultLoad.loginMessage || '';
                changeSMTPAuth(resultLoad.smtpAuthentication.toString());
                changeGoogleDriveIntegration(resultLoad.googleDriveIntegration);
                changeOidcEnabled(resultLoad.oidcEnabled, true);
                changeEnabledLandingPage(resultLoad.enabledLandingPage, true);
                changeExchangeRateConversorEnabled(resultLoad.exchangeRateConversorEnabled, true);
                changeOidicSignRedirectUri(resultLoad.url, resultLoad.oidcProvider);
                changeSsoActive(resultLoad.ssoActive);
                changeEnableLoginForm(resultLoad.enableLoginForm);
                
                registry.byId("nightlyProcessTime").set("value", gcUtil.parseDateFromJson(resultLoad.nightlyProcessTime));
                var googleDriveIntegration = +resultLoad.googleDriveIntegration === 1;
                new FilePicker({
                    modern: true,
                    targetDom: 'reportLogoId',
                    i18n: {
                        'filePickerFileLabel': i18n.reportLogo
                    },
                    accept: 'image/*',
                    externalSelect: googleDriveIntegration
                }, 'reportLogo');
                new FilePicker({
                    modern: true,
                    targetDom: 'isotypeLogoId',
                    uploadPath: '../DPMS/UploadIsotype.fileUploader',
                    i18n: {
                        'filePickerFileLabel': i18n.isotypeLogo
                    },
                    accept: 'image/*',
                    externalSelect: googleDriveIntegration,
                    onFileUploaded: function (file) {
                        switch (file.status) {
                            case 'invalid-content-type':
                            case 'invalid-size':
                                dom.byId('isotypeLogoId').value === '';
                                core.dialog(i18n.isotypeUploadMessage);
                                return;
                        }
                    },
                    onFileSelected: function (file) {
                        switch (file.status) {
                            case 'invalid-content-type':
                            case 'invalid-size':
                                dom.byId('isotypeLogoId').value === '';
                                core.dialog(i18n.isotypeUploadMessage);
                                return;
                        }
                    }
                }, 'isotypeLogo');
                toggleAdditionalExtraAreaFields()
            }
            
            function changeSsoActive (ssoActive) {
                if (isNull(ssoActive))
                {
                    dom.byId('ssoActive').selectedIndex = 0;
                    return;
                }
                dom.byId('ssoActive').selectedIndex = ssoActive;
            }
            
            function changeEnableLoginForm (enableLoginForm) {
                if (isNull(enableLoginForm))
                {
                    dom.byId('enableLoginForm').selectedIndex = 1;
                    return;
                }
                dom.byId('enableLoginForm').selectedIndex = enableLoginForm;
            }
            
            function changePsw(){
                domConstruct.create('div', {
                        'class': "cell small-12 medium-6 large-4 textarea-component sectionSMTPAuth",
                        innerHTML: ''
                            + '<input type="password" id="hashedSmtpPsw" name="hashedSmtpPsw" class="String" />'
                            + '<label>' + i18n.hashedSmtpPswPlacehoder + '</label>'
                    }, dom.byId('changePwdTarget'), 'after'
                );
                domConstruct.destroy(dom.byId('btnChangePswContainer'));
            }
            function changeOidcClientSecret() {
                domConstruct.create('div', {
                        'class': "cell small-12 medium-6 large-4 textarea-component oidc-enabled",
                        innerHTML: ''
                            + '<input type="password" id="oidcClientSecret" autocomplete="new-password" name="oidcClientSecret" class="String" />'
                            + '<label>' + i18n.oidcClientSecretPlacehoder + '</label>'
                    }, dom.byId('changeOidcClientSecret'), 'after'
                );
                domConstruct.destroy(dom.byId('btnChangeOidcClientSecretContainer'));
                showTomcatRestartWarning();
            }
            function savePreferences(data) {
                try {
                    dom.byId('welcomeMessage').value = registry.byId('welcome_message').get('value')+"@S";
                } catch(e) {
                    console.error('Failed to save welcomeMessage', e);
                }
                try {
                    dom.byId('loginMessage').value = registry.byId('login_message').get('value')+"@S";
                } catch(e) {
                    console.error('Failed to save loginMessage', e);
                }
                data.welcomeMessage = dom.byId('welcomeMessage').value;
                data.loginMessage = dom.byId('loginMessage').value;
                try {
                    data.nightlyProcessTime = gcUtil.parseDateToPattern(registry.byId("nightlyProcessTime").get("value"), 'yyyy-MM-dd HH:mm:ss') + '@D';
                } catch(e) {
                    console.error('Failed to save nightlyProcessTime', e);
                }
                if (dom.byId('reportLogoId') && dom.byId('reportLogoId').value === '') {
                    data.reportLogoId = '0@L';
                }
                if (dom.byId('welcomeBgId') && !dom.byId('welcomeBgId').value) {
                    data.welcomeBgId = null;
                }
                if (dom.byId('isotypeLogoId') && dom.byId('isotypeLogoId').value === '') {
                    data.isotypeLogoId = '0@L';
                }
            }
            function changeOidicSignRedirectUri(appUrl, oidcProvider) {
                dom.byId('oidcSignRedirectUri').value = OidcConfig.getOidcSignRedirectUri(appUrl, oidcProvider);
            }
            
            function saveValidationMail() {
                return PreferencesActions.getMailValues();
            }
            
            editor = registry.byId('welcome_message');
            dom.byId('language').options[0].innerHTML = i18n.Default.selectTag;
            
            $('#systemColor').ColorPicker({
                onSubmit: function (hsb, hex, rgb, el) {
                    var elx = $(el);
                    elx.val("#" + hex);
                    elx.ColorPickerHide();
                },
                onBeforeShow: function () {
                    $(this).ColorPickerSetColor(this.value);
                }
            }).bind('keyup', function () {
                $(this).ColorPickerSetColor(this.value);
            });
            
            $('#systemSecondaryColor').ColorPicker({
                onSubmit: function (hsb, hex, rgb, el) {
                    var elx = $(el);
                    elx.val("#" + hex);
                    elx.ColorPickerHide();
                },
                onBeforeShow: function () {
                    $(this).ColorPickerSetColor(this.value);
                }
            }).bind('keyup', function () {
                $(this).ColorPickerSetColor(this.value);
            });
            
            core.hideLoader();
            
            new TimeTextBox({
                value: new Date(),
                required: true
            }, dom.byId("nightlyProcessTime"));
            PreferencesActions.setEvents();
            on(dom.byId('isAreaCustomFieldsEnabled'), 'change', function(e){
                toggleAreaCustomVields(e.target.value === 'true');
            });
            on(dom.byId('language'), 'change', function(){
                dom.byId('lang').value = dom.byId('language').value.split('-')[0];
                dom.byId('locale').value = dom.byId('language').value.split('-')[1];
            });
            on(dom.byId('smtpAuthentication'), 'change', function(){
                changeSMTPAuth(this.value);
            });
            on(dom.byId('googleDriveIntegration'), 'change', function(){
                changeGoogleDriveIntegration(this.value);
            });
            on(dom.byId('oidcEnabled'), 'change', function(){
                changeOidcEnabled(this.value);
            });
            on(dom.byId('enabledLandingPage'), 'change', function(){
                changeEnabledLandingPage(this.value);
            });
            on(dom.byId('exchangeRateConversorEnabled'), 'change', function(){
                changeExchangeRateConversorEnabled(this.value);
            });
            on(dom.byId('oidcProvider'), 'change', function(){
                showTomcatRestartWarning();
                changeOidicSignRedirectUri(dom.byId('url').value, +dom.byId('oidcProvider').value);
            });
            on(dom.byId('oidcAuthenticationType'), 'change', function(){
                showTomcatRestartWarning();
            });
            on(dom.byId('oidcAccountAttribute'), 'change', function(){
                showTomcatRestartWarning();
            });
            on(dom.byId('oidcBnextAccountAttribute'), 'change', function(){
                showTomcatRestartWarning();
            });
            on(dom.byId('oidcDomain'), 'input', function() {
                showTomcatRestartWarning();
            });
            on(dom.byId('oidcClientId'), 'input', function() {
                showTomcatRestartWarning();
            });
            on(dom.byId('url'), 'input', function(event) {
                var enabledOidc = +(dom.byId('oidcEnabled').value);
                if (enabledOidc === 1) {
                    changeOidicSignRedirectUri(event.data, +dom.byId('oidcProvider'));
                }
            });
            function toggleAreaCustomVields(show) {
                var toggleSelector = '.area-custom-fields';
                if (show) {
                    query(toggleSelector).forEach(function(domArea) {
                       domClass.remove(domArea, 'hidden');
                    });
                } else {
                    query(toggleSelector).forEach(function(domArea) {
                       domClass.add(domArea, 'hidden');
                    });
                }
            }
            function defineValidationRules() {
                $.validator.addMethod(
                    "regex",
                    function (value, element, regexp) {
                        var re = new RegExp(regexp);
                        return this.optional(element) || re.test(value);
                    },
                    i18n.Validate.invalidText
                );
                $.validator.addMethod(
                    "mailLifeTime",
                    function (value, element, regexp) {
                        var re = new RegExp(regexp);
                        return this.optional(element) || re.test(value);
                    },
                    i18n.invalidMailLifeTime
                );
                $.validator.addMethod( "validTimeZone", function(value) {
                    try {
                        new Date(new Date).toLocaleString("en-US", {timeZone: value});  
                        return true;
                    }
                    catch (ex) {
                        return false;
                    }
                }, i18n.validateTimeZone);
                $.validator.addMethod( "multiemail", function(value, element) {
                    if (this.optional(element)) // return true on optional element 
                        return true;
                    var emails = value.split(/[;]+/); // split element by ;
                    var valid = true;
                    for (var i in emails) {
                        value = emails[i];
                        valid = valid &&
                                $.validator.methods.email.call(this, $.trim(value), element);
                    }
                    return valid;
                }, i18n.validateMail);
                $.validator.addMethod( "validUploadMaxFileSize", function(value) {
                    if (value > 2147483647) { // Equivale a 2.15GB o Integer.MAX_VALUE NOTA: Es el valor que se usaba por default
                        return false;
                    }
                    return true;
                }, i18n.validUploadMaxFileSize);
                var rText = i18n.Validate.textrequired,
                    rMail = i18n.validateMail,
                    rDigit = i18n.Validate.validDigit;
                var labelAreaFieldValidations = {}, labelAreaFieldMessages = {};
                for (let i = 1; i <= 26; i++) {
                    labelAreaFieldValidations['labelAreaField' + i] = {required: true};
                    labelAreaFieldMessages['labelAreaField' + i] = {required: rText};
                }
                $('#catalog_handle').validate({
                    rules: {
                        smtpHost: {required: true},
                        smtpPort: {
                            required: true,
                            digits: true
                        },
                        adminMail: {
                            required: true,
                            email: true
                        },
                        enableTimework: {
                            required: true
                        },
                        enableRegistration: {
                            required: true
                        },
                        useSecondLevelCache: {
                            required: true
                        },
                        regionCatalogEnabled: {
                            required: true
                        },
                        allowExportAllRecords: {
                            required: true
                        },
                        zoneCatalogEnabled: {
                            required: true
                        },
                        isAreaCustomFieldsEnabled: {
                            required: true
                        },
                        additionalExtraAreaFields: {
                            required: true
                        },
                        ...labelAreaFieldValidations,
                        enableDbReplication: {
                            required: true
                        },
                        preloadSecondLevelCache: {
                            required: true
                        },
                        trackGeolocation: {
                            required: true
                        },
                        correo: {email: true},
                        sessionMaxInactiveTime: {
                            required: true,
                            digits: true,
                            maxlength: 11
                        },
                        limitApeCachePreload: {
                            required: true,
                            digits: true,
                            maxlength: 11
                        },
                        pendingHistory: {
                            required: true,
                            digits: true,
                            maxlength: 2
                        },
                        indexRebuild: {
                            required: true
                        },
                        connTimeout: {
                            required: true,
                            digits: true,
                            maxlength: 6
                        },
                        mailLifeTime: {
                            required: true, 
                            digits: true
                       },
                        minimumCodeDigits: {
                            required: true, 
                            digits: true, 
                            maxlength: 1
                        },
                        smtpUid: {
                            required: true,
                            email: true
                        },
                        smtpPsw: {required: true},
                        mailSenderPoolSize: {
                            required: true,
                            digits: true
                        },
                        mailSenderAliveTime: {
                            required: true,
                            digits: true
                        },
                        mailSenderMaxWaitingTasks: {
                            required: true,
                            digits: true
                        },
                        mailSubject: {required: true},
                        url: {required: true},
                        folderName: {required: true},
                        host: {required: true},
                        apePoolSize: {
                            required: true,
                            digits: true
                        },
                        apeAliveTime: {
                            required: true,
                            digits: true
                        },
                        apeMaxWaitingTasks: {
                            required: true,
                            digits: true
                        },
                        cacheQuerySyncImagePoolSize: {
                            required: true,
                            digits: true
                        },
                        cacheQuerySyncAliveTime: {
                            required: true,
                            digits: true
                        },
                        cacheQuerySyncImageMaxWaitingTasks: {
                            required: true,
                            digits: true
                        },
                        apeLazyAliveTime: {
                            required: true,
                            digits: true
                        },
                        apeLazyMaxWaitingTasks: {
                            required: true,
                            digits: true
                        },
                        numAttemptsSession: {
                            required: true, 
                            digits: true, 
                            maxlength: 2
                        },
                        timeoutUnlocking: {
                            required: true,
                            digits: true,
                            maxlength: 2
                        },
                        timeZone: {
                            required: true,
                            validTimeZone: true
                        },
                        timeworkMails: {
                            multiemail: true
                        },
                        timeworkSyncType: {
                            required: true
                        },
                        timeLimitToModifyTimesheet: {
                            required: true, 
                            digits: true, 
                            maxlength: 3
                        },
                        uploadFileMaxSizeBytes: {
                          required: true,  
                          validUploadMaxFileSize: true
                        },
                        language: {required: true},
                        systemId: {required: true},
                        systemColor: {required: true},
                        systemSecondaryColor: {required: true}
                    },
                    messages: {
                        smtpHost: rText,
                        smtpPort: {
                            required: rText,
                            digits: rDigit
                        },
                        adminMail: {
                            required: rText,
                            email: rMail
                        },
                        isAreaCustomFieldsEnabled: {
                            required: rText
                        },
                        additionalExtraAreaFields: {
                            required: rText
                        },
                        ...labelAreaFieldMessages,
                        correo: {
                            email: rMail
                        },
                        mailLifeTime: {
                            required: rText,
                            digits: rDigit
                        },
                        minimumCodeDigits: {
                            required: rText,
                            digits: i18n.Validate.validDigit,
                            maxlength: core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 2)
                        },
                        sessionMaxInactiveTime: {
                            required: rText,
                            digits: i18n.Validate.validDigit,
                            maxlength: core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 2)
                        },
                        pendingHistory: {
                            required: rText,
                            digits: i18n.Validate.validDigit,
                            maxlength: core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 2)
                        },
                        connTimeout: {
                            required: rText,
                            digits: i18n.Validate.validDigit,
                            maxlength: core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 6)
                        },
                        smtpUid: {
                            required: rText,
                            email: rMail
                        
                        },
                        smtpPsw: rText,
                        mailSenderPoolSize: {
                            required: rText,
                            digits: rDigit
                        },
                        mailSenderAliveTime: {
                            required: rText,
                            digits: rDigit
                        }, 
                        mailSenderMaxWaitingTasks: {
                            required: rText,
                            digits: rDigit
                        },
                        mailSubject: rText,
                        url: rText,
                        folderName: rText,
                        host: rText,
                        apePoolSize: {
                            required: rText,
                            digits: rDigit
                        },
                        apeAliveTime: {
                            required: rText,
                            digits: rDigit
                        },
                        apeMaxWaitingTasks: { 
                            required: rText,
                            digits: rDigit
                        },
                        numAttemptsSession: {
                            required: rText,
                            digits: i18n.Validate.validDigit,
                            maxlength: core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 2)
                        },
                        timeoutUnlocking: {
                            required: rText,
                            digits: i18n.Validate.validDigit,
                            maxlength: core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 2)
                        },
                        timeZone: {
                            required: rText,
                            validTimeZone: i18n.validateTimeZone
                        },
                        timeLimitToModifyTimesheet: {
                            required: rText,
                            digits: i18n.Validate.validDigit,
                            maxlength: core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 2)
                        },
                        uploadFileMaxSizeBytes: {
                            required: rText,
                            validUploadMaxFileSize: i18n.validUploadMaxFileSize
                        },
                        language: rText,
                        systemId: rText,
                        systemColor: rText,
                        systemSecondaryColor: rText
                    },
                    errorPlacement: function (error, element) {
                        error.insertBefore(element);
                    }
                });
                $("#catalog_handle #systemColor").rules("add", {regex: "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"});
                $("#catalog_handle #systemSecondaryColor").rules("add", {regex: "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"});
                $("#catalog_handle #mailLifeTime").rules("add", {mailLifeTime: "(^-1$)|(^[0-9]+$)"});
            }
            
            defineValidationRules();
            function customHandleValidation() {
                var enableLoginForm = dom.byId('enableLoginForm');
                var enableWindowsLogin = dom.byId('enabledLandingPage');
                var enableOidcLogin = dom.byId('oidcEnabled');
                if (+enableLoginForm.value === 0 && +enableWindowsLogin.value === 0 && +enableOidcLogin.value === 0) {
                    core.dialog(i18n.signInRequired);
                    return false;
                } else {
                    return true;
                }
            }
            function toggleAdditionalExtraAreaFields(){
                const fieldCount = +dom.byId('additionalExtraAreaFields').value
                const maxFieldNo = 6 + fieldCount;
                for (let i = 7; i <= maxFieldNo; i++) {
                    domClass.remove(dom.byId('labelAreaField' + i).parentNode,'hidden')
                }
                for (let i = maxFieldNo + 1; i <= 26;i++) {
                    domClass.add(dom.byId('labelAreaField' + i).parentNode,'hidden')
                }
            }
            core.setLang('bnext/administrator/catalog/settings/nls/PreferencesActions');
            preferences.initialize(saveValidationMail, loadPreferences, savePreferences, customHandleValidation);
            on(dom.byId('btnChangePsw'), 'click', changePsw);
            on(dom.byId('btnChangeClientSecret'), 'click', changeOidcClientSecret);
            on(dom.byId('additionalExtraAreaFields'),'change',toggleAdditionalExtraAreaFields)
        }
        core.setLanguage('settings/preferences/preferences').then(doEverything);
    });
});

