require([
    'core', 'dojo/dom', 'dojo/on', 'dojo/query', 'dojo/dom-attr', 
    'bnext/administrator/catalog/settings/preferences-base', 'bnext/module/FilePicker',
    'dojo/ready', 'bnext/callMethod', 'dojo/dom-class',
    'bnext/_base/debounce-queue',
    'dojo/domReady!'
],
function (
    core, dom, on, query, domAttr,
    preferenceBase, FilePicker,
    ready, callMethod, domClass,
    DebounceQueue
) {
        ready(function () {
        function doEverything(i18n) {
          var  isValid = false;
          function getPdfConversionData() {
                var data = {};
                query('input, select', dom.byId('pdfViewerConfiguration')).some(function (input) {
                    if (!domClass.contains(input, "noData") && input.name) {
                        if (input.value !== "") {
                            data[input.name] = input.value;
                        }
                    }
                });
                return data;
            };
            function showErrorDetails(result) {
                var message;
                if (result.errorMessage === 'noaccess' ||  result.errorMessage === 'invalid') {
                    message = i18n[result.errorMessage] || result.errorMessage;
                } else if (result.errorObject && result.errorObject.error) {
                    var details = i18n[result.errorObject.name] 
                            || i18n[result.errorObject.message]
                            || result.errorObject.error.message;
                    message = ""
                    + "<div class='errorDetails'>"
                        + i18n.testPdfConversionFail + "<br/>"
                        + "<b>" + i18n.testDetailTitle + "</b><br/>"
                        +  "<ul style='margin-left: 25px;'><li>" + details + "</li></ul> "
                    + "</div>";
                } else {
                    message = ""
                    + "<div class='errorDetails'>"
                        + i18n.testPdfConversionFail + "<br/>"
                        + "<b>" + i18n.testDetailTitle + "</b><br/>"
                        +  "<ul style='margin-left: 25px;'><li>" + (i18n[result.errorMessage] || result.errorMessage) + "</li></ul> "
                    + "</div>";
                }
                core.asyncDialog(message,
                    i18n.msg_varios.accept
                );     
                console.log(result);
            }
            var pdfConversionStatusDom = dom.byId('pdfConversionStatus');
            function getPdfConversionStatus() {
                pdfConversionStatusDom.innerHTML = i18n.testing;
                callMethod({
                    url: 'Document-Settings.action',
                    method: 'getPdfConversionStatus',
                    params: []
                }).then(function (result) {
                    pdfConversionStatusDom.innerHTML = i18n[result] || i18n.errorPdfConversionStatus;
                }, function (result) {
                    pdfConversionStatusDom.innerHTML = i18n[result] || i18n.errorPdfConversionStatus;                        
                });
            }
            function testPdfViewerConfiguration() {
                if (!$('#catalog_handle').valid()) {
                    return;
                }
                core.showLoader(i18n.testingPdfConversion).then(function() {
                    var dataPdfConversion = getPdfConversionData();
                    callMethod({
                        url: 'Document-Settings.action',
                        method: 'testPdfViewerConfiguration',
                        params: [dataPdfConversion]
                    }).then(function (result) {
                        core.hideLoader();
                        if (result && result.operationEstatus) {
                            core.asyncDialog(i18n.testPdfConversionSuccess, i18n.msg_varios.accept);
                        } else if (result) {
                            showErrorDetails(result);
                        } else {
                            core.asyncDialog(i18n.testPdfConversionFail, i18n.msg_varios.accept);                            
                        }
                    }, function (result) {
                        core.hideLoader();
                        if (result) {
                            showErrorDetails(result);
                        } else {
                            core.asyncDialog(i18n.testPdfConversionFail, i18n.msg_varios.accept);
                        }
                    });
                });
            }
            
            core.hideLoader();
            var showHiden = function(){
                query('label.slow_and_closed', dom.byId('catalog_handle')).forEach(function (labelDom, i) {
                    try {
                        var lblFor = domAttr.get(labelDom, 'for');
                        if (lblFor && labelDom.id) {
                            on(labelDom, 'click', function (e) {
                                $('#' + lblFor).slideToggle('slow');
                                $('#' + labelDom.id).toggleClass('open');
                            });
                        }
                    } catch (e) {
                    }
                });
            };
            
            var changeOfficeConverter = function (value) {
                var convertToPDF = dom.byId('programConvertToPDF') !== null ? dom.byId('programConvertToPDF').value : "";
                value = !core.isNull(value) ? value : convertToPDF;
                if (!core.isNull(value) && value === 'ms_office') {
                    changeDisplay('sectionMsOffice', 'show');
                    changeDisplay('sectionLibreOffice', 'hide');
                    changeDisplay('sectionPdfGenerator', 'hide');
                    changeDisplay('sectionGoogleDrive', 'hide');
                } else if (!core.isNull(value) && value === 'libreoffice') {
                    changeDisplay('sectionLibreOffice', 'show');
                    changeDisplay('sectionMsOffice', 'hide');
                    changeDisplay('sectionPdfGenerator', 'hide');
                    changeDisplay('sectionGoogleDrive', 'hide');
                }  else if (!core.isNull(value) && value === 'google_drive') {
                    changeDisplay('sectionLibreOffice', 'hide');
                    changeDisplay('sectionMsOffice', 'hide');
                    changeDisplay('sectionPdfGenerator', 'hide');
                    changeDisplay('sectionGoogleDrive', 'show');
                }  else if (!core.isNull(value) && value === 'pdf_generator') {
                    changeDisplay('sectionLibreOffice', 'hide');
                    changeDisplay('sectionMsOffice', 'hide');
                    changeDisplay('sectionGoogleDrive', 'hide');
                    changeDisplay('sectionPdfGenerator', 'show');

                }
            };
            
            var changeDisplay = function (sectionClass, action) {
                if (!core.isNull(action) && action === 'show') {
                    query('.' + sectionClass).forEach(function (dom) {
                        dom.style.display = '';
                    });
                } else if (!core.isNull(action) && action === 'hide') {
                    query('.' + sectionClass).forEach(function (dom) {
                        dom.style.display = 'none';
                    });
                }
            };
            
            var enableFileCache = function(value) {
                if (value === 1) {
                    changeDisplay('enableFileCacheSection', 'show');
                } else {                    
                    changeDisplay('enableFileCacheSection', 'hide');
                }
            };
            
            showHiden();
            
            on(dom.byId('programConvertToPDF'), 'change', function () {
                changeOfficeConverter(this.value);
            });
            on(dom.byId('enableFileCache'), 'change', function() {
                enableFileCache(+this.value);
            });
            var testDebounce = new DebounceQueue(1000, this);
            on(dom.byId('testPdfViewerConfiguration'), 'click', function() {
                if (directoryADTV.value !== null && directoryADTV.value.trim().length > 0) {
                    isValid = directoryADTV.value.trim().length > 0 &&
                            directoryPlotPDF.value.trim().length > 0 &&
                            directoryPlotPDFWR.value.trim().length > 0 &&
                            directoryPlotPDFFiles.value.trim().length > 0 &&
                            directoryPlotPDFScript.value.trim().length > 0;
                } else {
                    isValid = true;
                }
                if (isValid) {
                    testDebounce.add(testPdfViewerConfiguration);
                } else {
                    core.dialog(i18n.dialogInfo,i18n.ok, i18n.close)
                }
            });
            on(dom.byId('refreshPdfConversionStatus'), 'click', getPdfConversionStatus);
            
            on(dom.byId('documentCodeScope'), 'change', function() {
                var scopeMsg;
                if (this.value === '1') {
                    scopeMsg = i18n['text: .documentCodeScope-1'];
                } else {
                    scopeMsg = i18n['text: .documentCodeScope-2'];
                }
                query('.info.documentCodeScope')[0].innerText = scopeMsg;
            });
            
            defineValidationRules();
            
            function defineValidationRules() {
                var rText = i18n.Validate.textrequired;
                $('#catalog_handle').validate({
                    rules: {
                        daysAnticipation: {required: true,  digits: true},
                        programConvertToPDF: {required: true},
                        downloadAttemptsLimit: {required: true, digits: true},
                        shareDocuments: {required: true},
                        externalLink: {required: true},
                        readers:{required: true}
                    },
                    messages: {
                        daysAnticipation: {
                            required: rText,
                            digits: i18n.Validate.validDigit
                        },
                        programConvertToPDF: rText,
                        downloadAttemptsLimit: {
                            required: rText
                        },
                        shareDocuments: rText,
                        externalLink: rText,
                        readers: rText
                    }
                });
            }
            function loadExternal(data) {
                var googleDriveIntegration = +data.googleDriveIntegration === 1;
                if (googleDriveIntegration) {
                    new FilePicker({
                        targetDom: 'googleDriveKeyId',
                        i18n: {
                            'filePickerFileLabel': i18n.googleDriveKey
                        },
                        externalSelect: true                        
                    }, 'googleDriveKey');
                } else {
                    changeDisplay('sectionGoogleDrive', 'hide');
                    changeDisplay('optionGoogleDrive', 'hide');
                    if(data.programConvertToPDF === 'google_drive'){
                    dom.byId('programConvertToPDF').value = null;
                    changeOfficeConverter();
                    } else {
                        changeOfficeConverter(data.programConvertToPDF);
                }
                }
                new FilePicker({
                    targetDom: 'defaultFileId',
                    useDownloadIcon: true,
                    i18n: {
                        'filePickerFileLabel': i18n.defaultFile
                    },
                    externalSelect: googleDriveIntegration
                }, 'defaultFile');
                changeOfficeConverter(data.programConvertToPDF);
                enableFileCache(data.enableFileCache);
            }
            function savePreferences(data) {
                if(dom.byId('defaultFileId').value === ''){
                    data.defaultFileId = null;
                }
            }
            getPdfConversionStatus();
           
            var directoryADTV = dom.byId('autodeskTvPath');
            var directoryPlotPDF = dom.byId('autodeskTvPlotScriptPath');
            var directoryPlotPDFWR = dom.byId('autodeskTvPlotFolderPath');
            var directoryPlotPDFFiles = dom.byId('autodeskTvFileParams');
            var directoryPlotPDFScript = dom.byId('autodeskTvPlotParams');

            on(dom.byId('saveBtn'), 'click', function () {
                if (directoryADTV.value !== null && directoryADTV.value.trim().length > 0) {
                    isValid = directoryADTV.value.trim().length > 0 &&
                            directoryPlotPDF.value.trim().length > 0 &&
                            directoryPlotPDFWR.value.trim().length > 0 &&
                            directoryPlotPDFFiles.value.trim().length > 0 &&
                            directoryPlotPDFScript.value.trim().length > 0
                } else {
                    isValid = true;
                }
                if (domClass.contains(dom.byId('lblPdfViewerConfiguration'), 'open')) {
                    if (!isValid) {
                        core.dialog(i18n.dialogInfo, i18n.ok, i18n.close)
                    } else {
                        var preferences = new preferenceBase();
                        preferences.routeContinue = "menu/legacy/v-document-preferences.view";
                        preferences.saveData( function() { return true; }, savePreferences);
                    }
                } else {
                    var preferences = new preferenceBase();
                    preferences.routeContinue = "menu/legacy/v-document-preferences.view";
                    preferences.saveData( function() { return true; }, savePreferences);
                }
            })
            on(dom.byId('cancelBtn'), 'click', function() {
                core.navigate('pendings')
            })
           var loadData = new preferenceBase();
           loadData.routeContinue = "menu/legacy/v-document-preferences.view";
           loadData.loadData(loadExternal);
        };
        core.setLang('bnext/administrator/catalog/settings/nls/preferences-document').then(doEverything);
    });
});