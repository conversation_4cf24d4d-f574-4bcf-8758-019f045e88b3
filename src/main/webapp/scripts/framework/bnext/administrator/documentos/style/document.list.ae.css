.documentListAE [aria-label="configMenu_contextMenu"] {
    right: 4.375rem !important;
    left: auto !important;
}
#dataGrid_media tr {
    width: 10rem!important;
    display: inline-block;
}

#dataGrid_media td {
    width: 9.938rem!important;
    display: inline-block;
    border: 0px!important;
    padding: 0 0 0 0;
    max-height: 10.188rem;
}

#dataGrid_media img {
    display: inline-block;
}

#dataGrid_media .css_status span, .css_delete span {
    clear: none;
    background: transparent!important;
    display: inline-block!important;
    float: left;
}

#dataGrid_media .css_status {
    display: block!important;
    height:9.375rem!important;
}

#dataGrid_media td.css_thumbnail {
    height: 10rem;
    display: table-cell;
}

#dataGrid_media td.css_thumbnail td,
#dataGrid_folders .css_explore span{
    height: 100%;
    text-align: center;
    vertical-align: middle;
    display: block;
}

#dataGrid_media tr {
    height: 10rem;
}

#dataGrid_media .imgDelete {
    opacity: .7;
    position: relative;
    top: -10rem;
    left: 0.375rem;
    background: white;
    border-radius: 0.125rem;
    height: 1rem;
    width: 1rem;
}



#dataGrid_media .imgDownload {
    opacity: .7;
    top: -10rem;
    position: relative;
    right: -7.75rem;
    width: 1rem;
    height: 1rem;
    background: white;
    border-radius: 0.125rem;
}

#dataGrid_media ul.ulTexto{
    position: relative;
    top: -4.188rem;
}

#dataGrid_media li.liPrincipal{
    color: white;
    position: relative;
}

#dataGrid_media li.liSecundario{
    opacity: .9;
    color: white;
}

#dataGrid_media div.divOculto{
    position: relative;
    margin-top: -6.875rem;
    opacity: .35;
    background-color: black;
}

#dataGrid_media li.liOculto{
    color: white;
}

.grid-component-container #dataGrid_media td img.thumbnail {
    width: auto;
    height: auto;
}
.grid-component-container #dataGrid_media td .liOculto,
.grid-component-container #dataGrid_media td .liSecundario,
.grid-component-container #dataGrid_media td .liPrincipal {
    word-wrap: break-word;
    word-break: break-all;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 8.875rem;
    max-height: 1.313rem;
}
.grid-filled-container #dataGrid_media-container {
    min-height: 30.188rem;
}
li.name-repository {
    height: 3rem;
    margin-bottom: 1rem;
}
ul.content_area.repository {
    margin-top: -2.125rem;
}
.grid-component-container #dataGrid_media .image-style img {
    max-width: 9.875rem;
    max-height: 9.875rem;
}
.float-right {
    padding-right: 1.875rem;
}
.bnext .dijitDialog.fixedTop div[data-dojo-attach-point="titleBar"] {
    display: block !important;
}
.contentTitle {
    overflow: hidden;
    line-height: initial;
    text-indent: initial;
}

.contentTitle .folder-container a {
    float: left;
    text-align: center;
    text-decoration: none;
    padding: 0.35rem;
    margin: 0;
    height: 2.5rem;
    box-sizing: border-box;
}

.folder-container .subfolder-container {
    text-align: left;
    float: left;
}

.folder-container .subfolder-container .buttonDropMenu.dijitDropDownButton{
    width: 2.188rem !important;
    padding: 0 !important;
    margin: 0 !important;
}
.folder-container .subfolder-container span.dijit.dijitReset.dijitInline.dijitDropDownButton .dijitButtonText {
    visibility: hidden;
}
.folder-container .subfolder-container .buttonDropMenu.dijitDropDownButton{
    background-color: transparent !important;
}
.folder-container .subfolder-container .buttonDropMenu .dijitButtonNode{
    padding: 0;
}

.folder-container .subfolder-container .buttonDropMenu .dijitButtonText {
    min-width: 0.625rem !important;
    padding: 0 !important;
    margin: 0 !important;
    background-color: transparent !important;
}

.folder-container .subfolder-container .buttonDropMenu .dijitArrowButtonInner{
    display: inline-block !important;
    background: url("../../../images/arrow_right_black.svg") center;
    width: 0.75rem !important;
    height: 1rem !important;
    margin-left: -1.875rem;
    background-size: 1.375rem;
}

.folder-container .subfolder-container .buttonDropMenu.dijitDropDownButtonOpened .dijitArrowButtonInner{
    background-image:url("../../../images/arrow_down_black.svg") !important;
}

.subFolderMenu.dijitMenu {
    border: none!important;
    box-shadow: 0 1px 0.5rem 0 rgba(0, 0, 0, 0.26), 0 0.188rem 0.25rem 0 rgba(0, 0, 0, 0.12), 0 0.188rem 0.188rem -0.125rem rgba(0, 0, 0, 0.08);
    border-radius: 0.25rem;
    overflow: hidden;
    min-width: 7.313rem;
    display: block;
    max-height:40.625rem;
    overflow: auto;
}

.subFolderMenu .subFolderItem.dijitMenuItem .dijitMenuItemLabel {
    text-indent: 0.938rem;
    font-size: 0.875rem;
}

.bnext .folder-container:hover .subfolder-container .dijitButtonNode:hover,
.bnext .folder-container:hover .subfolder-container .dijitDropDownButtonActive .dijitButtonNode {
    background: none !important;
}
.folder-container .subfolder-container {
    border-left: 1px solid transparent;
}
.folder-container:hover .subfolder-container{
    border-left-color: #FFF;
}
.contentTitle .folder-container a {
    border-top-left-radius: 0.285714rem;
    border-bottom-left-radius: 0.285714rem;
}

.contentTitle .folder-container a.has-not-childs,
.folder-container .subfolder-container,
.folder-container .subfolder-container .dijitButtonNode,
.folder-container .subfolder-container .dijitButtonNode .dijitButtonContents {
    border-top-right-radius: 0.285714rem;
    border-bottom-right-radius: 0.285714rem;
}
@media screen and (max-width:40em) {
    .actions-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .actions-buttons > #cancel_new_folder {
        padding: 0 !important;
    }

    .documentListAE > .dialog-new-folder {
        position: fixed;
        min-height: 7rem;
        height: 13rem !important;
        padding: 1rem;
    }

    .dialog-new-folder .dijitDialogTitleBar {
        padding: 0.5rem 0 !important;
    }

    .dialog-new-folder .grid-container {
        padding: 0.5rem 0 !important;
    }

    .dialog-new-folder .grid-container .grid-x > .div {
        padding: 1rem 0;
    }
    .dialog-new-folder .grid-container .grid-x > .div.actions-buttons {
        padding:0;
    }

    .dialog-new-folder .dialog-button {
        width: 50%;
    }
    .dialog-new-folder .dialog-button.raised-button {
        background-color: #ffbfff!important;
        color: #000!important;
    }

    .dialog-new-folder .grid-floating-active.grid-container {
        box-shadow: none;
    }

}
#myMoveNodes {
    overflow-y: scroll;
    max-height: 27.188rem;
}

@media screen and (max-width: 39.9375em) {
    #pathBar {
        font-size: .75rem;
        margin: 0 0.5rem 0 0.5rem;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
    .contentTitle {
        margin: 0 0 2rem 0.5rem;
    }
    .content_title .folder-container {
        font-size: 1rem;
    }
    .contentTitle .folder-container a {
        height: 2rem;
    }
    .contentTitle .folder-container a {
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        width: auto;
        max-width: 38%;
    }
    .contentTitle .folder-container:not(:hover) a {
        border: solid 1px;
        border-radius: 4px 0px 0px 4px;
    }
    .contentTitle .folder-container:not(:hover) a.has-not-childs {
        border: solid 1px;
        border-radius: 4px 4px 4px 4px;
    }
    .folder-container:not(:hover) .subfolder-container {
        border: solid 1px;
        border-radius: 0 4px 4px 0;
        height: 2rem;
    }
    .bnext .contentTitle .dijitArrowButtonInner {
        margin: 0 4px 2px 4px;
    }
    .bnext .contentTitle span.dijit.dijitReset.dijitInline.dijitDropDownButton .dijitButtonText {
        height: 2rem;
        line-height: 2rem;
    }
    .content_area > li {
        padding: 0 0.5rem;
    }
    .float-right {
        padding-right: 0.5rem;
        padding-top: 0.25rem;
    }
}

@media screen and (max-width: 26.5625em) {
    .contentTitle .folder-container a {
        width: auto;
        max-width: 34%;
    }
    .float-right {
        padding-right: 0rem;
    }
    .float-right .material-icons {
        font-size: 1.375rem;
    }
}