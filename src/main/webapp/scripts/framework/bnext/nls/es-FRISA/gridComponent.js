define({
    firstPage:"Inicio",
    lastPage:"Última",
    previous:"Anterior",
    following:"Siguiente",
    results:"resultados",
    showingFrom:"Mostrando del {inicio} al {fin} de {cantidad} {resultados} ",
    noResults:"No se encontraron registros",
    exportExcel:"Exportar a Excel",
    cancel:"Cancelar",
    exportLabel:"Exportar",
    format:"Formato",
    edit_column:'Editar',
    delete_column:'Borrar',
    logging_column:'Bitácora',
    firstComboElement:'-- Seleccione --',
    STATUS :["Inactivo","Activo"],
    onlyNumbers:'Solo acepta números',
    menusearch: 'Búsqueda',
    filter: 'Resultados filtrados.',
    search: 'Buscar',
    clear: 'Limpiar',
    unfilter: 'Limpiar y buscar',
    select: 'Seleccionar',
    selectAll: 'Seleccionar todos',
    minColumns:'Al menos una columna debe estar visible.'
});