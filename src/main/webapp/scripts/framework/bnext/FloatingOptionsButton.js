define([
    'core',
    'dojo/_base/declare',
    'dijit/_WidgetBase',
    'dijit/_TemplatedMixin',
    'dijit/_WidgetsInTemplateMixin',
    'dojo/text!./templates/FloatingOptionsButton.html',
    'dojo/dom-class',
    'dojo/dom-construct',
    'xstyle/css!./styles/FloatingOptionsButton.css'
], function (core, declare, WB, TM, WT, template, domClass, domConstruct) {
    return declare([WB, TM, WT], {
        templateString: template,
        position:'bottom-left',
        optionsButton: [],
        icon: 'add',
        postCreate: function () {
            domClass.add(this.options, this.position)
            this.optionsButton.forEach(function (optionButton) {
                domConstruct.place(optionButton.domNode, this.options, 'last');
            });
        }
    })
})