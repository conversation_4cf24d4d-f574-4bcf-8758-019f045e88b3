define([
    'dojo/Deferred',
    'dojo/on', 'dojo/date/locale', 'bnext/dBox',
    'bnext/i18n!bnext/_base/nls/offline-support', 'dojo/dom-attr',
    'bnext/_base/throttle-queue', 'loader'
],
function (
    Deferred, on, locale, dBox,
    offlineSupport, domAttr, ThrottleQueue,
    loader
) {
    var topContext = window.top.window;
    var MAX_RECONNECTION_ATTEMPTS = 5;
    var WAIT_TIME_CONSTANT = 250;
    var WAIT_TIME_FACTOR_CONSTANT = 1.2;
    topContext.waitForIframeConnectionDebounce = {};
    function validateUrl(location) {
        try {
            if (typeof location !== 'object' || typeof location.pathname !== 'string') {
                return;
            }
            var path = location.pathname;
            if (path === null || typeof path === 'undefined') {
                return;
            }
            if (path.indexOf("/view/v-logout.view") !== -1 || path.indexOf("/view/v-login.view") !== -1) {
                topContext.location = 'v-login.view';
            }
        } catch(e) {
            console.error(e);
        }
    }
    function isOffline(content) {
        if (!content || !content.location) {
            return true;
        }
        var href = false;
        try {
            href = content.location.href;
        } catch (e) {
            console.error('Failed to test location of iframe. Application is offline.', e);
            return true;
        }
        if (href) {
            return false;
        }
        return !navigator.onLine;
    }
    function failedRefresh(iFrameId) {
        topContext.waitForIframeConnectionDebounce[iFrameId] = false;
        if (navigator.onLine) {
            var date = locale.format(new Date(), {
                'selector': 'date',
                'datePattern': 'dd/MM/yyyy HH:mm:ss'
            });
            var message = offlineSupport['not-available-message'].replace('${date}', date);
            loader.hideLoader();
            dBox.dialog(message);
        } else {
            var date = locale.format(new Date(), {
                'selector': 'date',
                'datePattern': 'dd/MM/yyyy HH:mm:ss'
            });
            var message = offlineSupport['offline-message'].replace('${date}', date);
            loader.hideLoader();
            dBox.dialog(message);
        }
    }
    function refreshIframe(iFrame, srcIFrame) {
        var id = iFrame.id;
        if (topContext.waitForIframeConnectionDebounce[id]
                && topContext.waitForIframeConnectionDebounce[id].attempts > MAX_RECONNECTION_ATTEMPTS) {
            failedRefresh();
            return;
        }
        if (!topContext.waitForIframeConnectionDebounce[id]) {
            topContext.waitForIframeConnectionDebounce[id] = new ThrottleQueue(WAIT_TIME_CONSTANT, WAIT_TIME_FACTOR_CONSTANT, topContext);
            topContext.waitForIframeConnectionDebounce[id].attempts = 0;
        }
        loader.showLoader(offlineSupport.reconnecting).then(function () {
            topContext.waitForIframeConnectionDebounce[id].add(function () {
                topContext.waitForIframeConnectionDebounce[id].attempts++;
                if (!navigator.onLine) {
                    console.warn('Lost connection to Internet. trying again ...', srcIFrame);
                    refreshIframe(iFrame, srcIFrame);
                    return;
                }
                console.warn('Lost connection to server. Reconnecting ...', srcIFrame);
                try {
                    if (srcIFrame === null || typeof srcIFrame === 'undefined') {
                        var lastSrcIFrame = domAttr.get(iFrame, 'data-src');
                        console.warn('Invalid iframe src, trying again ...', lastSrcIFrame);
                        refreshIframe(iFrame, lastSrcIFrame);
                        return;
                    }
                    var newSrc = srcIFrame;
                    if (srcIFrame.indexOf('?') === -1) {
                        newSrc += '?t=' + new Date().getTime();
                    } else {
                        newSrc += '&t=' + new Date().getTime();
                    }
                    domAttr.set(iFrame, 'src', newSrc);
                    domAttr.set(iFrame, 'data-src', newSrc);
                } catch (e) {
                    console.warn('Failed to reconnecting, trying again ...', e);
                    refreshIframe(iFrame, srcIFrame);
                }
            });
        }, function () {
            refreshIframe(iFrame, srcIFrame);
        });
    }
    function loadIframeEvent(iFrame) {
        var content = (iFrame.contentWindow || iFrame.contentDocument);
        if (!content) {
            console.warn('Location is null for principal frame ' + iFrame.id);
            return;
        }
        if (isOffline(content)) {
            var srcIFrame = domAttr.get(iFrame, 'src');
            refreshIframe(iFrame, srcIFrame);
        } else if (content.location) {
            var id = iFrame.id;
            if (topContext.waitForIframeConnectionDebounce[id]) {
                loader.hideLoader();
                topContext.waitForIframeConnectionDebounce[id] = false;
            }
            validateUrl(content.location);
        } else {
            loader.hideLoader();
            console.error('Failed to load location for principal frame ' + iFrame.id, content);
        }
    }
    function errorIFrameEvent(iFrame) {
        loader.hideLoader();
        if (isOffline(content)) {
            var srcIFrame = domAttr.get(iFrame, 'src');
            refreshIframe(iFrame, srcIFrame);
        } else {
            console.error('Failed to load location for principal frame ' + iFrame.id, content);
        }
    }
    var _waitTime = 100;
    function checkIframeLoaded(def, iframe, flagName) {
        var context = IFrameUtil.getContext(iframe);
        var doc = context.doc;
        var win = context.win;
        var valid = true;
        if(flagName) {
            valid = win[flagName];
        }
        var fail = containsError(doc, win);
        if (doc.readyState === 'complete' && (valid || fail.error)) {
            context.valid = valid;
            context.fail = fail;
            return def.resolve(context);
        }
        setTimeout(function() {
            checkIframeLoaded(def, iframe, flagName);
        }, _waitTime);
    }
    function containsError(doc, win) {
        var error = false, message = '';
        if (doc.title.indexOf('Error') !== -1) {
            message = 'Iframe con falla "' + win.location.href + '", ' + (doc.getElementsByTagName('h1')[0] || {innerText: doc.title}).innerText;
            error = true;
        }
        return {
            message: message,
            error: error
        };
    }
    var IFrameUtil = {
        getContext: function(iframe) {
            var doc = iframe.contentDocument || iframe.contentWindow.document;
            var win = iframe.contentWindow || doc.defaultView || doc.parentWindow;
            return {
                doc: doc, win: win
            };
        },
        isLoaded: function(iframe, flagName, waitTime) {
            if (typeof waitTime !== 'undefined') {
                _waitTime = waitTime;
            }
            var def = new Deferred();
            setTimeout(function() {
                checkIframeLoaded(def, iframe, flagName);
            }, _waitTime);
            return def.promise;
        },
        setLoadEvents: function (iFrame) {
            on(iFrame, 'load', function() {
                loadIframeEvent(iFrame);
            });
            on(iFrame, 'error', function() {
                errorIFrameEvent(iFrame);   
            });
        }
    };
    return IFrameUtil;
});