/* 
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
dojo.provide("bnext.iconManagement");


iconManager= function(){
    this.sources=({
        "basic":{file:"fileicons.gif"
            ,width:47
            ,height:46}
    });

    this.icons = ({
        "ppt":{position:0
        ,resource:"basic"}
        ,"docx":{position:1
        ,resource:"basic"}
        ,"xlsx":{position:2
        ,resource:"basic"}
        ,"pptx":{position:3
        ,resource:"basic"}
        ,"doc":{position:4
        ,resource:"basic"}
        ,"xls":{position:5
        ,resource:"basic"}
        ,"zip":{position:6
        ,resource:"basic"}
        ,"pdf":{position:7
        ,resource:"basic"}
        ,"txt":{position:8
        ,resource:"basic"}
    });

    //Obtiene un objeto que contiene la información de un ícono
    //recibe como parámetro la extensión del archivo
    this.getIcon = function(ext){
            var path= dojo.moduleUrl("bnext", "images/defaultIcon.gif").toString();
            log("Path:"+path);
            var result={"src":path
                ,position:0
                ,width:"47px"
                ,height:"46px"};
            if (dojo.exists(ext,this.icons)){
                var icon = dojo.getObject(ext,false,this.icons);
                if (dojo.exists(icon.resource,this.sources)){
                    var source = dojo.getObject(icon.resource,false,this.sources);
                    path = dojo.moduleUrl("bnext", "images/"+source.file).toString();
                    var position = source.width*icon.position;
                    dojo.setObject("src",path,result);
                    dojo.setObject("position",position+"px",result);
                    dojo.setObject("width",source.width+"px",result);
                    dojo.setObject("height",source.height+"px",result);
                }else{
                    log("No existe ese recurso");
                }
            }else{
                log("No existe esa extension");
            }
            return result;
        }
}