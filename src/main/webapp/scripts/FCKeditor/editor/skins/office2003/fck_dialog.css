/*
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2005 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_dialog.css
 * 	Styles used by the dialog boxes.
 * 
 * File Authors: <AUTHORS>
 */

body
{
	margin: 0px;
	padding: 10px;
	background-color: #f7f8fd;
}

body, td, input, select, textarea
{
	font-size: 11px;
	font-family: 'Microsoft Sans Serif' , Arial, Helvetica, Verdana;
}

body, .BackColor
{
	background-color: #f7f8fd;
}

.PopupBody
{
	margin: 0px;
	padding: 0px;
}

.PopupTitle
{
	font-weight: bold;
	font-size: 14pt;
	color: #0e3460;
	background-color: #8cb2fd;
	padding: 3px 10px 3px 10px;
}

.PopupButtons
{
	border-top: #466ca6 1px solid;
	background-color: #8cb2fd;
	padding: 7px 10px 7px 10px;
}

.But<PERSON>
{
	border: #1c3460 1px solid;
	color: #000a28;
	background-color: #7096d3;
}

.DarkBackground
{
	background-color: #d7d79f;
}

.LightBackground
{
	background-color: #ffffbe;
}

.PopupTitleBorder
{
	border-bottom: #d5d59d 1px solid;
}

.PopupTabArea
{
	color: #0e3460;
	background-color: #8cb2fd;
}

.PopupTabEmptyArea
{
	padding-left: 10px ;
	border-bottom: #466ca6 1px solid;
}

.PopupTab, .PopupTabSelected
{
	border-right: #466ca6 1px solid;
	border-top: #466ca6 1px solid;
	border-left: #466ca6 1px solid;
	padding-right: 5px;
	padding-left: 5px;
	padding-bottom: 3px;
	padding-top: 3px;
	color: #0e3460;
}

.PopupTab
{
	margin-top: 1px;
	border-bottom: #466ca6 1px solid;
	cursor: pointer;
	cursor: hand;
}

.PopupTabSelected
{
	font-weight:bold;
	cursor: default;
	padding-top: 4px;
	border-bottom: #f7f8fd 1px solid;
	background-color: #f7f8fd;
}

.PopupSelectionBox
{
	border: #1e90ff 1px solid;
	background-color: #add8e6;
	cursor: pointer;
	cursor: hand;
}