<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2005 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fckdebug.html
 * 	This is the Debug window.
 * 	It automatically popups if the Debug = true in the configuration file.
 * 
 * File Authors: <AUTHORS>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
	<head>
		<meta name="ROBOTS" content="NOINDEX, NOFOLLOW">
		<title>FCKeditor Debug Window</title>
		<script type='text/javascript'>

var oWindow ;
var oDiv ;

if ( !window.FCKMessages )
	window.FCKMessages = new Array() ;

function Initialize()
{
	oWindow = window.frames[ 'eOutput' ]
	oWindow.document.open() ;
	oWindow.document.write( '<div id="divMsg"><\/div>' ) ;
	oWindow.document.close() ;
	oDiv	= oWindow.document.getElementById('divMsg') ;
}

function Output( message, color )
{
	if ( color )
		message = '<font color="' + color + '">' + message + '<\/font>' ;
		
	window.FCKMessages[ window.FCKMessages.length ] = message ;
	StartTimer() ;
}

function StartTimer()
{
	window.setTimeout( 'CheckMessages()', 100 ) ;
}

function CheckMessages()
{
	if ( window.FCKMessages.length > 0 )
	{
		// Get the first item in the queue
		var sMessage = window.FCKMessages[0] ;
		
		// Removes the first item from the queue
		var oTempArray = new Array() ;
		for ( i = 1 ; i < window.FCKMessages.length ; i++ )
			oTempArray[ i - 1 ] = window.FCKMessages[ i ] ;
		window.FCKMessages = oTempArray ;
		
		var d = new Date() ;
		var sTime = 
			( d.getHours() + 100 + '' ).substr( 1,2 ) + ':' + 
			( d.getMinutes() + 100 + '' ).substr( 1,2 ) + ':' + 
			( d.getSeconds() + 100 + '' ).substr( 1,2 ) + ':' + 
			( d.getMilliseconds() + 1000 + '' ).substr( 1,3 ) ;

		var oMsgDiv = oWindow.document.createElement( 'div' ) ;
		oMsgDiv.innerHTML = sTime + ': <b>' + sMessage + '<\/b>' ;
		oDiv.appendChild( oMsgDiv ) ;
		oMsgDiv.scrollIntoView() ;
	}
}

function Clear()
{
	oDiv.innerHTML = '' ;
}
		</script>
	</head>
	<body onload="Initialize();" bottomMargin="10" leftMargin="10" topMargin="10" rightMargin="10">
		<TABLE height="100%" cellSpacing="5" cellPadding="0" width="100%" border="0">
			<TR>
				<TD>
					<TABLE cellSpacing="0" cellPadding="0" width="100%" border="0">
						<TR>
							<TD><FONT size="+2"><STRONG>FCKeditor Debug Window</STRONG></FONT></TD>
							<TD align="right"><INPUT type="button" value="Clear" onclick="Clear();"></TD>
						</TR>
					</TABLE>
				</TD>
			</TR>
			<TR>
				<TD height="100%" style="BORDER-RIGHT: #696969 1px solid; BORDER-TOP: #696969 1px solid; BORDER-LEFT: #696969 1px solid; BORDER-BOTTOM: #696969 1px solid">
					<iframe id="eOutput" name="eOutput" width="100%" height="100%" scrolling="auto" src="fckblank.html" frameborder="no"></iframe>
				</TD>
			</TR>
		</TABLE>
	</body>
</html>