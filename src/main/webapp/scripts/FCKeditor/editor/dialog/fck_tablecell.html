<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2005 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_tablecell.html
 * 	Cell properties dialog window.
 * 
 * File Authors: <AUTHORS>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<html>
	<head>
		<title>Table Cell Properties</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="robots" content="noindex, nofollow" />
		<script src="common/fck_dialog_common.js" type='text/javascript'></script>
		<script type='text/javascript'>

var oEditor = window.parent.InnerDialogLoaded() ;

// Gets the document DOM
var oDOM = oEditor.FCK.EditorDocument ;

// Array of selected Cells
var aCells = oEditor.FCKTableHandler.GetSelectedCells() ;

window.onload = function()
{
	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage( document ) ;

	SetStartupValue() ;

	window.parent.SetOkButton( true ) ;
	window.parent.SetAutoSize( true ) ;
}

function SetStartupValue()
{
	if ( aCells.length > 0 )
	{
		var oCell = aCells[0] ;
		var iWidth = GetAttribute( oCell, 'width' ) ;

		if ( iWidth.indexOf && iWidth.indexOf( '%' ) >= 0 )
		{
			iWidth = iWidth.substr( 0, iWidth.length - 1 ) ;
			GetE('selWidthType').value = 'percent' ;
		}

		if ( oCell.attributes['noWrap'] != null && oCell.attributes['noWrap'].specified )
			GetE('selWordWrap').value = !oCell.noWrap ;

		GetE('txtWidth').value			= iWidth ;
		GetE('txtHeight').value			= GetAttribute( oCell, 'height' ) ;
		GetE('selHAlign').value			= GetAttribute( oCell, 'align' ) ;
		GetE('selVAlign').value			= GetAttribute( oCell, 'vAlign' ) ;
		GetE('txtRowSpan').value		= GetAttribute( oCell, 'rowSpan' ) ;
		GetE('txtCollSpan').value		= GetAttribute( oCell, 'colSpan' ) ;
		GetE('txtBackColor').value		= GetAttribute( oCell, 'bgColor' ) ;
		GetE('txtBorderColor').value	= GetAttribute( oCell, 'borderColor' ) ;
//		GetE('cmbFontStyle').value		= oCell.className ;
	}
}

// Fired when the user press the OK button
function Ok()
{
	for( i = 0 ; i < aCells.length ; i++ )
	{
		if ( GetE('txtWidth').value.length > 0 )
			aCells[i].width	= GetE('txtWidth').value + ( GetE('selWidthType').value == 'percent' ? '%' : '') ;
		else
			aCells[i].removeAttribute( 'width', 0 ) ;

		if ( GetE('selWordWrap').value == 'false' )
			aCells[i].noWrap = true ;
		else
			aCells[i].removeAttribute( 'noWrap' ) ;

		SetAttribute( aCells[i], 'height'		, GetE('txtHeight').value ) ;
		SetAttribute( aCells[i], 'align'		, GetE('selHAlign').value ) ;
		SetAttribute( aCells[i], 'vAlign'		, GetE('selVAlign').value ) ;
		SetAttribute( aCells[i], 'rowSpan'		, GetE('txtRowSpan').value ) ;
		SetAttribute( aCells[i], 'colSpan'		, GetE('txtCollSpan').value ) ;
		SetAttribute( aCells[i], 'bgColor'		, GetE('txtBackColor').value ) ;
		SetAttribute( aCells[i], 'borderColor'	, GetE('txtBorderColor').value ) ;
//		SetAttribute( aCells[i], 'className'	, GetE('cmbFontStyle').value ) ;
	}

	return true ;
}

function SelectBackColor( color )
{
	if ( color && color.length > 0 )
		GetE('txtBackColor').value = color ;
}

function SelectBorderColor( color )
{
	if ( color && color.length > 0 )
		GetE('txtBorderColor').value = color ;
}

function SelectColor( wich )
{
	oEditor.FCKDialog.OpenDialog( 'FCKDialog_Color', oEditor.FCKLang.DlgColorTitle, 'dialog/fck_colorselector.html', 400, 330, wich == 'Back' ? SelectBackColor : SelectBorderColor, window ) ;
}

		</script>
	</head>
	<body scroll="no" style="OVERFLOW: hidden">
		<table cellSpacing="0" cellPadding="0" width="100%" border="0" height="100%">
			<tr>
				<td>
					<table cellSpacing="1" cellPadding="1" width="100%" border="0">
						<tr>
							<td>
								<table cellSpacing="0" cellPadding="0" border="0">
									<tr>
										<td nowrap><span fckLang="DlgCellWidth">Width</span>:</td>
										<td>&nbsp;<input onkeypress="return IsDigit();" id="txtWidth" type="text" maxLength="4"
												size="3" name="txtWidth">&nbsp;<select id="selWidthType" name="selWidthType">
												<option fckLang="DlgCellWidthPx" value="pixels" selected>pixels</option>
												<option fckLang="DlgCellWidthPc" value="percent">percent</option>
											</select></td>
									</tr>
									<tr>
										<td nowrap><span fckLang="DlgCellHeight">Height</span>:</td>
										<td>&nbsp;<INPUT id="txtHeight" type="text" maxLength="4" size="3" name="txtHeight" onkeypress="return IsDigit();">&nbsp;<span fckLang="DlgCellWidthPx">pixels</span></td>
									</tr>
									<tr>
										<td>&nbsp;</td>
										<td>&nbsp;</td>
									</tr>
									<tr>
										<td nowrap><span fckLang="DlgCellWordWrap">Word Wrap</span>:</td>
										<td>&nbsp;<select id="selWordWrap" name="selAlignment">
												<option fckLang="DlgCellWordWrapNotSet" value="" selected>&lt;Not set&gt;</option>
												<option fckLang="DlgCellWordWrapYes" value="true">Yes</option>
												<option fckLang="DlgCellWordWrapNo" value="false">No</option>
											</select></td>
									</tr>
									<tr>
										<td>&nbsp;</td>
										<td>&nbsp;</td>
									</tr>
									<tr>
										<td nowrap><span fckLang="DlgCellHorAlign">Horizontal Alignment</span>:</td>
										<td>&nbsp;<select id="selHAlign" name="selAlignment">
												<option fckLang="DlgCellHorAlignNotSet" value="" selected>&lt;Not set&gt;</option>
												<option fckLang="DlgCellHorAlignLeft" value="left">Left</option>
												<option fckLang="DlgCellHorAlignCenter" value="center">Center</option>
												<option fckLang="DlgCellHorAlignRight" value="right">Right</option>
											</select></td>
									</tr>
									<tr>
										<td nowrap><span fckLang="DlgCellVerAlign">Vertival Alignement</span>:</td>
										<td>&nbsp;<select id="selVAlign" name="selAlignment">
												<option fckLang="DlgCellVerAlignNotSet" value="" selected>&lt;Not set&gt;</option>
												<option fckLang="DlgCellVerAlignTop" value="top">Top</option>
												<option fckLang="DlgCellVerAlignMiddle" value="middle">Middle</option>
												<option fckLang="DlgCellVerAlignBottom" value="bottom">Bottom</option>
												<option fckLang="DlgCellVerAlignBaseline" value="baseline">Baseline</option>
											</select></td>
									</tr>
								</table>
							</td>
							<td>&nbsp;&nbsp;&nbsp;</td>
							<td align="right">
								<table cellSpacing="0" cellPadding="0" border="0">
									<tr>
										<td nowrap><span fckLang="DlgCellRowSpan">Rows Span</span>:</td>
										<td>&nbsp; <input onkeypress="return IsDigit();" id="txtRowSpan" type="text" maxLength="3"
												size="2" name="txtRows"></td>
										<td></td>
									</tr>
									<tr>
										<td nowrap><span fckLang="DlgCellCollSpan">Columns Span</span>:</td>
										<td>&nbsp; <input onkeypress="return IsDigit();" id="txtCollSpan" type="text" maxLength="2"
												size="2" name="txtColumns"></td>
										<td></td>
									</tr>
									<tr>
										<td>&nbsp;</td>
										<td>&nbsp;</td>
										<td>&nbsp;</td>
									</tr>
									<tr>
										<td nowrap><span fckLang="DlgCellBackColor">Background Color</span>:</td>
										<td>&nbsp;<input id="txtBackColor" type="text" size="8" name="txtCellSpacing"></td>
										<td>&nbsp; <input type="button" fckLang="DlgCellBtnSelect" value="Select..." onclick="SelectColor( 'Back' )"></td>
									</tr>
									<tr>
										<td nowrap><span fckLang="DlgCellBorderColor">Border Color</span>:</td>
										<td>&nbsp;<input id="txtBorderColor" type="text" size="8" name="txtCellPadding"></td>
										<td>&nbsp; <input type="button" fckLang="DlgCellBtnSelect" value="Select..." onclick="SelectColor( 'Border' )"></td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>
</html>
