<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2005 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: fck_universalkey.html
 * 	Unicode Keyboard dialog window.
 * 
 * File Authors: <AUTHORS>
 * 		<PERSON>
 * 		Bernadette Cierzniak
 * 		<PERSON><PERSON> (<EMAIL>)
-->
<html>
	<head>
		<title>Universal Keyboard</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="noindex, nofollow" name="robots">
		<link rel="stylesheet" type="text/css" href="fck_universalkey/fck_universalkey.css" />
		<script type='text/javascript'>

var oEditor = window.parent.InnerDialogLoaded() ;

window.onload = function()
{
	document.body.style.padding = '0px' ;

	// First of all, translate the dialog box texts
	oEditor.FCKLanguageManager.TranslatePage(document) ;

	window.parent.SetOkButton( true ) ;
	window.parent.SetAutoSize( true ) ;	
}

function Ok()
{
	var oArea = document.getElementById( 'uni_area' ) ;

	if ( oArea.value.length > 0 )
		oEditor.FCK.InsertHtml( oArea.value ) ;

	return true ;
}

		</script>
	</head>
	<body style="OVERFLOW: hidden" scroll="no">
		<textarea id="uni_area" cols="40" rows="4" style="width:100%;height:60px;"></textarea>
		<script type='text/javascript' src="fck_universalkey/data.js"></script>
		<script type='text/javascript' src="fck_universalkey/diacritic.js"></script>
		<script type='text/javascript' src="fck_universalkey/dialogue.js"></script>
		<script type='text/javascript' src="fck_universalkey/multihexa.js"></script>
	</body>
</html>
