<html>
	<head>
		<link rel="stylesheet" type="text/css" href="spellerStyle.css" />
		<script src="controlWindow.js"></script>
		<script>
var spellerObject;
var controlWindowObj;

if( parent.opener ) {
	spellerObject = parent.opener.speller;
}

function ignore_word() {
	if( spellerObject ) {
		spellerObject.ignoreWord();
	}
}

function ignore_all() {
	if( spellerObject ) {
		spellerObject.ignoreAll();
	}
}

function replace_word() {
	if( spellerObject ) {
		spellerObject.replaceWord();
	}
}

function replace_all() {
	if( spellerObject ) {
		spellerObject.replaceAll();
	}
}

function end_spell() {
	if( spellerObject ) {
		spellerObject.terminateSpell();
	}
}

function undo() {
	if( spellerObject ) {
		spellerObject.undo();
	}
}

function suggText() {
	if( controlWindowObj ) {
		controlWindowObj.setSuggestedText();
	}
}

var FCKLang = window.parent.parent.FCKLang ;	// by FredCK

function init_spell() {
	// By FredCK (fckLang attributes have been added to the HTML source of this page)
	window.parent.parent.OnSpellerControlsLoad( this ) ;

	var controlForm = document.spellcheck;

	// create a new controlWindow object
 	controlWindowObj = new controlWindow( controlForm );

	// call the init_spell() function in the parent frameset
	if( parent.frames.length ) {
		parent.init_spell( controlWindowObj );
	} else {
		alert( 'This page was loaded outside of a frameset. It might not display properly' );
	}
}

</script>
	</head>
	<body class="controlWindowBody" onLoad="init_spell();" style="OVERFLOW: hidden" scroll="no">	<!-- by FredCK -->
		<form name="spellcheck">
			<table border="0" cellpadding="0" cellspacing="0" border="0" align="center">
				<tr>
					<td colspan="3" class="normalLabel"><span fckLang="DlgSpellNotInDic">Not in dictionary:</span></td>
				</tr>
				<tr>
					<td colspan="3"><input class="readonlyInput" type="text" name="misword" readonly /></td>
				</tr>
				<tr>
					<td colspan="3" height="5"></td>
				</tr>
				<tr>
					<td class="normalLabel"><span fckLang="DlgSpellChangeTo">Change to:</span></td>
				</tr>
				<tr valign="top">
					<td>
						<table border="0" cellpadding="0" cellspacing="0" border="0">
							<tr>
								<td class="normalLabel">
									<input class="textDefault" type="text" name="txtsugg" />
								</td>
							</tr>
							<tr>
								<td>
									<select class="suggSlct" name="sugg" size="7" onChange="suggText();" onDblClick="replace_word();">
										<option></option>
									</select>
								</td>
							</tr>
						</table>
					</td>
					<td>&nbsp;&nbsp;</td>
					<td>
						<table border="0" cellpadding="0" cellspacing="0" border="0">
							<tr>
								<td>
									<input class="buttonDefault" type="button" fckLang="DlgSpellBtnIgnore" value="Ignore" onClick="ignore_word();">
								</td>
								<td>&nbsp;&nbsp;</td>
								<td>
									<input class="buttonDefault" type="button" fckLang="DlgSpellBtnIgnoreAll" value="Ignore All" onClick="ignore_all();">
								</td>
							</tr>
							<tr>
								<td colspan="3" height="5"></td>
							</tr>
							<tr>
								<td>
									<input class="buttonDefault" type="button" fckLang="DlgSpellBtnReplace" value="Replace" onClick="replace_word();">
								</td>
								<td>&nbsp;&nbsp;</td>
								<td>
									<input class="buttonDefault" type="button" fckLang="DlgSpellBtnReplaceAll" value="Replace All" onClick="replace_all();">
								</td>
							</tr>
							<tr>
								<td colspan="3" height="5"></td>
							</tr>
							<tr>
								<td>
									<input class="buttonDefault" type="button" name="btnUndo" fckLang="DlgSpellBtnUndo" value="Undo" onClick="undo();"
										disabled>
								</td>
								<td>&nbsp;&nbsp;</td>
								<td> 
									<!-- by FredCK
									<input class="buttonDefault" type="button" value="Close" onClick="end_spell();">
									-->
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</form>
	</body>
</html>
