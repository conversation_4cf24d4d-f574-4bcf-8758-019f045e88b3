<%@ CodePage=65001 Language="VBScript"%>
<% Option Explicit %>
<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2005 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: sampleposteddata.asp
 * 	This page lists the data posted by a form.
 * 
 * File Authors: <AUTHORS>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
	<head>
		<title>FCKeditor - Samples - Posted Data</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="robots" content="noindex, nofollow">
		<link href="../sample.css" rel="stylesheet" type="text/css" />
	</head>
	<body>
		<h1>FCKeditor - Samples - Posted Data</h1>
		This page lists all data posted by the form.
		<hr>
		<table width="100%" border="1" cellspacing="0" bordercolor="#999999">
			<tr style="FONT-WEIGHT: bold; COLOR: #dddddd; BACKGROUND-COLOR: #999999">
				<td noWrap>Field Name&nbsp;&nbsp;</td>
				<td>Value</td>
			</tr>
			<% 
			Dim sForm
			For Each sForm in Request.Form 
			%>
			<tr>
				<td valign="top" nowrap><b><%=sForm%></b></td>
				<td width="100%"><%=Server.HTMLEncode( Request.Form(sForm) )%></td>
			</tr>
			<% Next %>
		</table>
	</body>
</html>
