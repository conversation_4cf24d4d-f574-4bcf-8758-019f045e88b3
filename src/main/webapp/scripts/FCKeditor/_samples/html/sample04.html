<!--
 * FCKeditor - The text editor for internet
 * Copyright (C) 2003-2005 <PERSON><PERSON>
 * 
 * Licensed under the terms of the GNU Lesser General Public License:
 * 		http://www.opensource.org/licenses/lgpl-license.php
 * 
 * For further information visit:
 * 		http://www.fckeditor.net/
 * 
 * "Support Open Source software. What about a donation today?"
 * 
 * File Name: sample04.html
 * 	Sample page.
 * 
 * File Authors: <AUTHORS>
-->
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
	<head>
		<title>FCKeditor - Sample</title>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="robots" content="noindex, nofollow">
		<link href="../sample.css" rel="stylesheet" type="text/css">
			<script type='text/javascript' src="../../fckeditor.js"></script>
			<script type='text/javascript'>

function FCKeditor_OnComplete( editorInstance )
{
	var oCombo = document.getElementById( 'cmbToolbars' ) ;
	oCombo.value = editorInstance.ToolbarSet.Name ;
	oCombo.style.visibility = '' ;
}

function ChangeLanguage( languageCode )
{
	window.location.href = window.location.pathname + "?" + languageCode ;
}

			</script>
	</head>
	<body>
		<h1>FCKeditor - Javascript - Sample 4</h1>
		This sample shows how to change the editor toolbar.
		<hr>
		<table cellpadding="0" cellspacing="0" border="0">
			<tr>
				<td>
					Select the toolbar to load:&nbsp;
				</td>
				<td>
					<select id="cmbToolbars" onchange="ChangeLanguage(this.value);" style="VISIBILITY: hidden">
						<option value="Default" selected>Default</option>
						<option value="Basic">Basic</option>
					</select>
				</td>
			</tr>
		</table>
		<br>
		<form action="sampleposteddata.asp" method="post" target="_blank">
			<script type='text/javascript'>
<!--
// Automatically calculates the editor base path based on the _samples directory.
// This is usefull only for these samples. A real application should use something like this:
// oFCKeditor.BasePath = '/fckeditor/' ;	// '/fckeditor/' is the default value.
var sBasePath = document.location.pathname.substring(0,document.location.pathname.lastIndexOf('/_samples/')+1) ;

// Get the toolbar from the URL.
var sToolbar ;
if ( document.location.search.length > 1 )
	sToolbar = document.location.search.substr(1) ;

var oFCKeditor = new FCKeditor( 'FCKeditor1' ) ;
oFCKeditor.BasePath	= sBasePath ;

if ( sToolbar != null )
	oFCKeditor.ToolbarSet = sToolbar ;

oFCKeditor.Value	= 'This is some <strong>sample text</strong>. You are using <a href="http://www.fckeditor.net/">FCKeditor</a>.' ;
oFCKeditor.Create() ;
//-->
			</script>
			<br>
			<input type="submit" value="Submit">
		</form>
	</body>
</html>
