<%@ taglib prefix = "c" uri = "http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" isErrorPage="true" %>
<%
    response.addHeader("X-Frame-Options", "SAMEORIGIN");
    response.addHeader("X-XSS-Protection", "1;mode=block");
    response.addHeader("X-Content-Type-Options", "nosniff");
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <%
            String appUrl = request.getRequestURI().replaceAll("/errors.+?jsp", "");
        %>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
        <title>Error 500</title>
        <link rel="shortcut icon" type="image/x-icon" href="<%= appUrl%>/favicon.ico">
        <link rel="stylesheet" href="<%= appUrl%>/qms/<%=bnext.resources.DynamicCssService.CURRENT_MATERIAL_FONTS_FILE_NAME%>.css?${systemVersion}">
        <style type="text/css">
            body {
                text-align:center;
            }
            .mensajes {
                border-radius: 100%;
                background-color: aliceblue;
                display: inline-block;
                margin: 40px auto;
                font-family: Arial;
                font-size: 8pt;
                text-align: center;
                height: 600px;
                width: 600px;
            }
            .mensajes > div > div,
            .mensajes > div,
            .mensajes > div > textarea {
                display: inline-block;
                text-align:center;
            }
            .error-logo {
                margin-top: 110px;
                text-align:center;
                height: 150px;
                width: 150px;
                line-height: 150px;
                font-size: 5rem;
                background-repeat: no-repeat;
                background-image: url('<%= appUrl%>/scripts/framework/bnext/images/bnext_logo_bg.png')
            }
            textarea {
                width: 100%;
                height: 60px;
            }
            .displayNone {
                display: none!important;
            }
        </style>
        <script>
            dojoConfig = {
                parseOnLoad: true,
                isDebug: true,
                debugAtAllCosts: true,
                locale: '<%= session.getAttribute("lang") + "-" + session.getAttribute("locale")%>',
                deps: ['core'],
                cacheBust: '${systemVersion}'
            };
        </script>
        <script type='text/javascript' src="<%= appUrl%>/scripts/framework/dojo/dojo.js?${systemVersion}" >
        </script>
        <script>
            require(['core', 'dojo/domReady!'],
                    function (core) {
                        core.hideLoader();
                    });
        </script>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <div class="mensajes">
            <div class="material-icons error-logo">
                account_circle
            </div>
            <br>
            <div>
                <div class="error-message">
                    <h1>Usuario marcado como inactivo</h1>
                    <p>
                        <%= new java.util.Date() %>
                    </p>
                    <h2>Tu cuenta de usuario fue marcada como inactiva, consulta al administrador del sistema para más información.</h2>
                </div>
            </div>
        </div>
    </body>
</html>
