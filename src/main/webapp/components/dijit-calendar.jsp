<style>
    .bnext .dijitCalendarEnabledDate.dijitCalendarHoveredDate{
        background-color: ${systemColorLight}!important;
    }
    .bnext .dijitCalendarNextYear,
    .bnext .dijitCalendarPreviousYear {
        color: ${systemSecondaryColor}!important;

    }
    .bnext .dijitCalendarSelectedYear {
        background-color: ${systemSecondaryColor}!important;
    }
    .bnext .dijitCalendarEnabledDate .dijitCalendarCurrentDate .dijitCalendarCurrentMonth .dijitCalendarDateTemplate,
    .bnext .dijitCalendarEnabledDate .dijitCalendarCurrentMonth .dijitCalendarDateTemplate .dijitCalendarSelectedDate{
        background-color: ${systemSecondaryColor}!important;
    }
    .bnext .dijitCalendarSelectedDate {
        background-color: ${systemSecondaryColor}!important;
        color: ${systemSecondaryColorTextColor}!important;
    }
    .bnext .dijitCalendarSelectedDate:hover {
        background-color: ${systemSecondaryColorLight}!important;
    }
    .bnext .dijitInline.dijitCalendarPreviousYearHover,
    .bnext .dijitInline.dijitCalendarNextYearHover{
        background-color: ${systemColorLight}!important;
    }
    .bnext div.dijitPopup .dijitCalendarMonthLabelHover{
        background-color: ${systemColorLight}!important;
    }
</style>
<link rel="stylesheet" type="text/css" href="../styles/dijit-calendar.css?${systemVersion}" />