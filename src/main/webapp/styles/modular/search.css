/*grid component search*/
.envelope > h4{ padding: 0.313rem 0px 0.625rem 0px;cursor:pointer; text-decoration: underline;}
.search{
    cursor: pointer;
    font-family: 'TypoPRO Titillium Text', sans-serif;
    font-display: swap;
    color: rgba(0, 0, 0, 0.8);
}
.columnaBusqueda{width:100%;}
.columnaBusqueda:after {content: "."; display: block;height: 0; clear: both; visibility: hidden; }
.searchFields{width:100%!important;display:block;float:left}
.bnext .searchFields .dijitInputContainer input{
    height:  1.75rem;
    font-size: 1rem;
    line-height: 1.75rem;
    font-weight: 400;
    letter-spacing: .009375em;
    padding: 0;
    text-indent: 0.5rem;
}
.bnext .searchFields .dijitInputContainer{
    padding: 0!important;
}
.bnext .searchFields .searchField label {
    width: auto;
    text-align: left;
    position: relative;
    margin-top: -1.563rem !important;
    margin-right: 1px !important;
    margin-bottom: 0.375rem !important;
    margin-left: 0.688rem !important;
    background-color: white;
    text-indent: 0px;
    padding-top: 0.625rem !important;
    padding-right: 0.313rem !important;
    padding-bottom: 0px !important;
    padding-left: 0.313rem !important;
    font-weight:normal;
    pointer-events:none;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    -webkit-transition: all .25s cubic-bezier(.4,0,.2,1);
    -o-transition: all .25s cubic-bezier(.4,0,.2,1);
    transition: all .25s cubic-bezier(.4,0,.2,1);
    font-weight: 400;
    font-size: 1rem;
    letter-spacing: 0.009375rem;
    text-transform: none;
    color: rgba(0, 0, 0, 0.54);
    top: 2.563rem;
    height: auto;
    box-shadow: none;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: calc(100% - 1.5rem);
}
.bnext .searchFields .dijitTextBox .dijitButtonNode {
    min-height: auto;
    box-sizing: border-box;
}
.bnext .searchFields .searchField label.search-input-focused,
.bnext .searchFields .searchField label.search-input-with-value {
    transform: translateY(-1.5rem);
    font-size:0.875rem;
    font-weight: 700;
    color: #000;
}
.bnext .dijitLayoutContainer.dijitDialog .searchFields .searchField label.search-input-focused,
.bnext .dijitLayoutContainer.dijitDialog .searchFields .searchField label.search-input-with-value {
    z-index: 1;
    width: fit-content;
}
.bnext .dijitLayoutContainer.dijitDialog .searchFields .searchField label {
    display: block;
    top: 2.75rem;
    padding: 0 !important;
}
.bnext .dijitLayoutContainer.dijitDialog .searchFields .dijitTextBox:not(.dijitDateTextBox) .dijitArrowButton {
    margin-top: 0.25rem !important;
}
.bnext .searchFields .searchField label.gridButtonLabel {
    position: static;
    margin-top: 0px !important;
    margin-left: 0px !important;
    padding-left: 0px !important;
}
.bnext .searchFields .searchField .dijitInline .dijitTextBox,
.bnext .searchFields .searchField .dijitInline.dijitTextBox,
.bnext .searchFields .searchField .dijitInline .dijitSelect,
.bnext .searchFields .searchField .dijitInline.dijitSelect {
    border-left: 1px solid;
    border-right: 1px solid;
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-radius: 0.25rem;
    box-sizing: border-box;
    background:  none;
    padding-right: 0.938rem;
    padding-left: 0.938rem;
    padding-top: 0.688rem;
    padding-bottom: 0.813rem;
    outline: none;
    border-color: rgba(0,0,0,.24);
    box-sizing: border-box;
    border: 1px solid #b3b3b3;
}

.bnext .searchFields .searchField .dijitInline .dijitSelect,
.bnext .searchFields .searchField .dijitInline.dijitSelect,
.bnext .searchFields .searchField .dijitInline .dijitComboBox,
.bnext .searchFields .searchField .dijitInline.dijitComboBox {
    padding-right: 0.125rem;
}
.bnext .searchFields .searchField .dijitInline .dijitTextBox.dijitHover,
.bnext .searchFields .searchField .dijitInline.dijitTextBox.dijitHover,
.bnext .searchFields .searchField .dijitInline .dijitSelect.dijitHover,
.bnext .searchFields .searchField .dijitInline.dijitSelect.dijitHover {
    border-color: rgba(0,0,0,.87);
}
.bnext .searchFields .searchField .search-input-focused ~ .dijitInline .dijitTextBox,
.bnext .searchFields .searchField .search-input-focused ~ .dijitInline.dijitTextBox,
.bnext .searchFields .searchField .search-input-focused ~ .dijitInline .dijitSelect,
.bnext .searchFields .searchField .search-input-focused ~ .dijitInline.dijitSelect {
    border-width: 0.125rem;
    margin: 0px;
}
 
.bnext .searchFields .searchField .dijitComboBox.dijitDateTextBox input, 
.bnext .searchFields .searchField .dijitComboBox.dijitTimeTextBox input {
    font-size: 0.9rem;
}
.bnext .searchField .dijitComboBox.dijitFocused .dijitArrowButton,
.bnext .searchField .dijitTextBoxFocused .dijitButtonNode {
    border-color:transparent;
    outline: none;
}
div.searchField div.fecha_upper:last-child,
div.searchField div.timestamp_fecha_upper:last-child ,
li.searchField div.fecha_upper:last-child,
li.searchField div.timestamp_fecha_upper:last-child {
    margin-top: 1px!important;
}
div.searchField > div.dijitInline,
li.searchField > div.dijitInline {
    width: 15.625rem;
    height: 3.125rem;
}

div.searchField > div.dijitInline.dijitNumberTextBox,
div.searchField > div.dijitInline >.dijitTextBox,
div.searchField > div.dijitInline >.fecha_upper,
div.searchField > div.dijitInline >.fecha_lower,
div.searchField > div.dijitInline >.timestamp_fecha_lower,
div.searchField > div.dijitInline >.timestamp_fecha_upper,
div.searchField > div.dijitInline >.timestamp_time_lower,
div.searchField > div.dijitInline >.timestamp_time_upper,
li.searchField > div.dijitInline.dijitNumberTextBox,
li.searchField > div.dijitInline >.dijitTextBox,
li.searchField > div.dijitInline >.fecha_upper,
li.searchField > div.dijitInline >.fecha_lower,
li.searchField > div.dijitInline >.timestamp_fecha_lower,
li.searchField > div.dijitInline >.timestamp_fecha_upper,
li.searchField > div.dijitInline >.timestamp_time_lower,
li.searchField > div.dijitInline >.timestamp_time_upper {
    width: 15.563rem;
    height: 3.125rem;
}

div.searchField > div.dijitInline div.dijitInputContainer input,
li.searchField > div.dijitInline div.dijitInputContainer input{
    height: 1.5rem;
    margin: .1rem 0 0 .2rem !important;
    text-indent: 0;
}

div.searchField > div.dijitInline .dijitComboBox.dijitDateTextBox input,
div.searchField > div.dijitInline .dijitComboBox.dijitTimeTextBox input,
li.searchField > div.dijitInline .dijitComboBox.dijitDateTextBox input,
li.searchField > div.dijitInline .dijitComboBox.dijitTimeTextBox input{
    min-height: 1.5rem!important;
}

div.searchField .dijitInline div div.dijitArrowButton,
li.searchField .dijitInline div div.dijitArrowButton {
    outline: none!important;
}

div.searchField,
li.searchField {
    display:block;
    width: 15.625rem;
    height: 4.063rem;
    overflow:hidden;
    margin: 0.125rem 0.688rem;
    float:left;
    box-sizing: border-box;
}
div.searchField .dijitInline,
li.searchField .dijitInline {
    max-width: 100%;
    box-sizing: border-box;
}

.bnext .searchFields .searchField label.searchLabel.gridButtonLabel {
    width:100%!important;
    height: auto;
    pointer-events: auto;
    padding-top: 0.688rem!important;
    padding-bottom: 0px!important;
    box-shadow: none;
    padding-right: 1px!important;
    margin-bottom: 0px!important;
    max-width: 100%;
    transform: none;
}
.search_button_holder:after {
    content: ".";
    display: block;
    height: 0;
    clear: both; 
    visibility: hidden;
}
.search_button_holder {
    margin-top: 0.625rem;
    margin-bottom: 0.625rem;
    float:left;
}
.button{display:block; float:left;}
.bnext .searchFields .searchField label.searchLabel.gridButtonLabel .Button {
    box-shadow: none;
    min-height: 3.125rem;
    border-color: rgba(0,0,0,.24)!important;
    border-radius: 0.25rem;
    justify-content: center;
    flex-direction: column;
    text-align: left;
    padding-top: 0px!important;
    margin-right: 0px;
    width: 15.625rem!important;
    cursor: pointer;
    max-width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: middle;
    line-height: 3.125rem;
    margin-top: -1px;
}

.bnext .searchFields .searchField .catalog-multiple-data-info,
.dijitPopup.dojoxCheckedMultiSelectPopup .dojoxCheckedMultiSelect .catalog-multiple-data-info {
    display: none!important;
}
.dijitPopup.dojoxCheckedMultiSelectPopup .dojoxCheckedMultiSelect .dojoxMultiSelectItemLabel {
    cursor: pointer;
}
.dijitPopup.dojoxCheckedMultiSelectPopup .dojoxCheckedMultiSelect .dojoxCheckedMultiSelectWrapper {
    min-width: 15.563rem;
    cursor: pointer;
}
.search-filter-chips {
    padding-left: 0.625rem;
}
.grid-container  .search-style .searchDialog {
    margin-top: -0.625rem;
}
.search-filter-chips .Chip {
    margin-right: .5em;
    margin-bottom: .5em;
}

.bnext .searchFields .searchField label.search-input-with-value .Button {
    background-image: url('./../../scripts/framework/bnext/images/filtered.png');
    transition: none;
    background-repeat: no-repeat;
    background-position-x: 22.438rem;
    background-position-y: 1.375rem;
    background-size: 0.625rem;
}

/* responsividad tablet */
@media print, screen and (max-width: 39.9375em) {
    .dijitPopup.dojoxCheckedMultiSelectPopup .dojoxCheckedMultiSelect .dojoxCheckedMultiSelectWrapper {
        max-width: calc(100vw - 3.75rem);
    }
}
/*Cosa magica para prevenir comportamiento extraño que muestra un recuadro azul al dar click a un elemento en dispositivos moviles "ISSUE-363"*/
@media print, screen and (max-width: 26.75rem) {
    .search {
        cursor: none;
    }
}