<%-- 
    Document   : happyornot-fill
    Created on : 25 ene 2021, 22:29:39
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="es">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, minimum-scale=1.0, initial-scale=1.0, maximum-scale=1.0">
        <title>${name} - ${question} - ${applicationData}</title>
        <link rel="shortcut icon" type="image/x-icon" href="../favicon.ico">
        <link rel="stylesheet" href="../qms/<%=bnext.resources.DynamicCssService.CURRENT_MATERIAL_FONTS_FILE_NAME%>.css?${systemVersion}">
        <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/happyornot/style/happyornot-fill.css?${systemVersion}" />
        <link rel="manifest" id="custom-manifest">
        <script type='text/javascript'>
            window.top.skipLabelInterpolation = true;
            dojoConfig = {
              parseOnLoad: true,
              locale:'${settingsLang}-${settingsLocale}',
              extraLocale: ["en", "es-MX"],
              deps:['core'],
              async: true,
              cacheBust: '${systemVersion}'
            };
        </script>
        <script type='text/javascript' src="../scripts/framework/dojo/dojo.js?${systemVersion}"></script>
        <script type='text/javascript' src="../scripts/framework/bnext/happyornot/happyornot-fill.js?${systemVersion}"></script>
        <style>
            .displayNone {
                display: none!important;
            }
            body > div.splitter.splitter-area {
                max-height: 100vh;
                max-width: 100vw;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <div class="feedback-message displayNone" id="message-overlay">
            <span id="countDown"></span>
            <div id="content-area" class="content-area">
                <span id="icon-feedback" class="material-icons"></span>
                <span class="message" id="message"></span>
            </div>
        </div>
        <input type="hidden" id="happyornotTargetId" value="${happyornotTargetId}"/>
        <input type="hidden" id="aspectWidth" value="${aspectWidth}"/>
        <input type="hidden" id="feedbackTitle" value="${feedbackTitle}"/>
        <input type="hidden" id="aspectHeight" value="${aspectHeight}"/>
        <input type="hidden" id="messagePosition" value="${messagePosition}"/>
        <input type="hidden" id="waitingTime" value="${waitingTime}"/>
        <input type="hidden" id="answerPerDay" value="${answerPerDay}">
        <input type="hidden" id="allowFeeback" value="${allowFeedback}">
        <input type="hidden" id="saveUserAgent" value="${saveUserAgent}">
        <input type="hidden" id="linkId" value="${linkId}">
        ${html}
    </body>
     <div class="overlay-dialog remove-overlay-dialog" id="overlayDialog">
         <div class="dialog-container">
             <div class="dialog-header">
                 <div class="dialog-title" id="dialogTitle">
                     ${feedbackTitle}
                 </div>
             </div>
              <div class="dialog-body">
                  <span id="feedback"></span>
                  <textarea id="txtFeedback" maxlength="4000"></textarea>
                  <span id="eFeedback"></span>
                  <input id="emailFeedback" type="email" maxlength="100"></input>
             </div>
              <div class="dialog-footer">
                  <button class="button-cancel" id="btnCancel"></button>
                  <button class="button-ok" id="btnOk"> </button>
             </div>
         </div>
    </div>
</html>
