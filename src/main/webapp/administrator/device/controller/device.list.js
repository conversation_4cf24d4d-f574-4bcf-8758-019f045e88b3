require([
  'dojo/on',
  'dojo/query',
  'core',
  'dojo/_base/array',
  'dojo/dom',
  'dojo/dom-class',
  'dijit/registry',
  'bnext/columnTypes',
  'bnext/gridIcons',
  'dojo/json',
  'bnext/gridComponentUtil',
  'bnext/GridFactory',
  'bnext/module/grid-menu',
  'bnext/module/grid-menu-util',
  'bnext/administrator/device/device-menu-items',
  'bnext/angularNavigator',
  'dojo/domReady!'
], (on, query, core, array, dom, domClass, registry, columnTypes, gridIcons, json, gcUtil, GridFactory, GridMenu, GridMenuUtil, DeviceMenuItem, angularNavigator) => {
  const doc = window.document;
  let grid;
  let sId;
  const statusList = [];
  const $GET = getUrlVars();
  const cType = gcUtil.cType();
  const statusNode = dom.byId('status');
  let gridMenu = null;
  function doEverything(i18n) {
    function changeStatus(id, status) {
      if (statusNode.options && statusNode.options.length >= 3) {
        const changeStatus = registry.byId('changeStatus');
        sId = id;
        array.forEach(statusNode.options, (option) => {
          if (option.value === status.toString()) {
            domClass.add(option, 'hidden');
          } else {
            domClass.remove(option, 'hidden');
          }
        });
        statusNode.value = '';
        statusNode.options[0].innerHTML = i18n.Popup.selectTag;
        changeStatus.set('title', i18n.Popup.editStatus);
        changeStatus.set('style', { 'max-width': '760px' });
        changeStatus.show();
      } else {
        core.dialog(i18n.noAvailableStatus, i18n.Validate.accept);
      }
    }
    function disposeDevice(id) {
      angularNavigator.navigateLegacy(`v.device.dispose.view?id=${id}&task=control_device`);
    }
    function editRecord_grid(id) {
      angularNavigator.navigateLegacy(`v.device.view?id=${id}&task=control_device`);
    }
    function scheduleServices(id) {
      angularNavigator.navigateLegacy(`v.service.scheduler.view?deviceId=${id}&task=control_device`);
    }
    function viewHistory(id) {
      angularNavigator.navigateLegacy(`v.realized.services.view?deviceId=${id}&task=control_device`);
    }
    const statuses = json.parse(dom.byId('deviceStatusSerialized').value);
    array.forEach(statuses, (status) => {
      statusList.push({
        id: status.id,
        name: status.description,
        value: status.id,
        cubeId: status.cubeId
      });
    });
    gridMenu = new GridMenu(
      {
        type: DeviceMenuItem,
        multipleSelection: true
      },
      null
    );
    const columns = [];
    gcUtil
      .column(columns)
      .push('status', i18n.colNames.status, cType.FunctionCube(['id', 'status'], changeStatus, statusList, true), null, '30px')
      .push('edit', i18n.colNames.edit, cType.Function(['id'], editRecord_grid, gridIcons.edit), null, '50px')
      .push('code', i18n.colNames.code, cType.Text(), { type: 'texto', col: 1 }, '200px')
      .push('description', i18n.colNames.description, columnTypes.text, { type: 'texto', col: 1 }, '200px')
      .push('purchaseDate', i18n.colNamesPurchaseDate, cType.Date(true), null, null, 'none')
      .push('useDate', i18n.colNamesUseDate, cType.Date(true), null, null, 'none')
      .push('changeDate', i18n.colNames.replace_date, cType.Date(true))
      .push('modelBrand', i18n.colNamesModelBrand, cType.Text())
      .push('serial', i18n.colNamesSerial, cType.Text())
      .push('deviceGroup.description', i18n.colNamesDeviceGroup, cType.Text())
      .push('deviceType.description', i18n.colNamesDeviceType, cType.Text())
      .push('department.description', i18n.colNamesDepartment, cType.Text())
      .push('area.description', i18n.colNamesArea, cType.Text())
      .push('attendant.description', i18n.colNamesAttendant, cType.Text())
      .push('priority.description', i18n.colNamesPriority, cType.Text(), null, null, 'none')
      .push('owner.description', i18n.colNamesOwner, cType.Text())
      .push('classification.description', i18n.colNamesClassification, cType.Text())
      .push('calibrationMethodText', i18n.colNamesCalibration, cType.Text(), null, '200px', 'none')
      .push('localization', i18n.colNamesLocalization, cType.Text(), null, '200px', 'none')
      .push('operationRange', i18n.colNamesOperationRange, cType.Text(), null, '200px', 'none')
      .push('precision', i18n.colNamesPrecision, cType.Text(), null, '200px', 'none')
      .push('treshold', i18n.colNamesTreshold, cType.Text(), null, '200px', 'none')
      .push('uncertainty', i18n.colNamesUncertainty, cType.Text(), null, '200px', 'none');

    const menuItems = [];
    GridMenuUtil.menu(menuItems, DeviceMenuItem)
      .push(DeviceMenuItem.MODIFY_DEVICE, editRecord_grid, true, ['id'])
      .push(DeviceMenuItem.DISPOSE_DEVICE, disposeDevice, true, ['id'])
      .push(DeviceMenuItem.SCHEDULE_DEVICE, scheduleServices, true, ['id'])
      .push(DeviceMenuItem.HISTORY_DEVICE, viewHistory, true, ['id']);
    GridFactory.create({
      size: dom.byId('gridSize').value,
      container: 'dataGrid',
      serviceStore: 'DeviceSimple.action',
      methodName: `getRows${$GET.type || ''}`,
      fullColumns: columns,
      extraCriteria: [{ key: 'deleted', value: 0 }],
      logging_column: true,
      onLoaded: function () {
        gridMenu.build(this, menuItems);
      }
    }).then((g) => {
      grid = g;
      grid.refreshData();
    });
    on(dom.byId('btnCancelStatus'), 'click', () => {
      registry.byId('changeStatus').hide();
    });
    on(dom.byId('btnChangeStatus'), 'click', () => {
      if (statusNode.value !== '') {
        const changeStatus = registry.byId('changeStatus');
        core.showLoader(i18n.Validate.toggling_status);
        core.dialogExecutioner(
          i18n.Validate.toggling_confirm,
          i18n.Validate.yes,
          i18n.Validate.no,
          'Device.action',
          'toggleStatus',
          [sId, statusNode.value],
          i18n.Validate.edit_success,
          i18n.Validate.tooggling_fail,
          i18n.Validate.accept,
          grid
        );
        changeStatus.hide();
        core.hideLoader();
      } else {
        core.dialog(core.i18n.Validate.missingCombo, i18n.Validate.accept);
      }
    });
  }
  core.setLang('lang/device/nls/device.list').then(doEverything).then(core.hideLoader);
});