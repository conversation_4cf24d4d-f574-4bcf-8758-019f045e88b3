<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Control de grupos de equipos</title>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script type="text/javascript" src="../controller/c.scheduled.services.controller?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="mainTitle">
                                    </h3>
                                    <div class="float-right">
                                        <!-- boton de opciones -->
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class ="grid-container">
                            <table id="dataGrid"></table>
                        </div>
                        <div class ="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                            <s:hidden name="status" id="status" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <form name="edit_form" action="v.service.scheduler.view">
            <input type="hidden" id="id" name="id" value=''/>
            <input type="hidden" id="task" name="task" value=''/>
        </form>
        <form name="realize_form" action="v.realize.service.view">
            <input type="hidden" id="serviceScheduleId" name="serviceScheduleId" value=''/>
            <input type="hidden" id="titleType" name="titleType" value='planned'/>
        </form>

        <form name="log_form" action="v.logging.view">
            <input type="hidden" id="record_id" name="record_id" value=''/>
            <input type="hidden" id="logElement" name="logElement" value='ServiceSchedule'/>
        </form>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>
