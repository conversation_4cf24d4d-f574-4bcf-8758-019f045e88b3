<%-- 
    Document   : surveylist
    Created on : 10-abr-2013, 12:11:41
    Author     : <PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <title></title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <jsp:include page="../../components/requiredScripts.jsp" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/styles/Tabs.css?${systemVersion}" />
        <s:set var="js_controller" value="%{js_controller}"/> 
        <script type='text/javascript' src="../controller/c.${js_controller}.list.controller?${systemVersion}"></script>
        <style>
            .css_isTemplateAvailable {
                text-align: center;
                cursor: pointer;
            }
            .generate.material-icons {
                cursor: pointer;
            }
            @media print, screen and (max-width: 39.9375em) { 
                .css_edit {
                    display: none;
                }
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="window_title">
                                    </h3>
                                    <div class="float-right">
                                        <!-- boton de opciones -->
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class ="grid-container">
                            <ul class="grid-x grid-padding-x content_area">
                                <li><div id="search"></div></li>
                                <li><div id="tabNode" ></div></li>
                                <li><table id="grid"></table></li>
                                <li><div id="pagination"></div></li>
                            </ul>
                        </div>
                        <div class ="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                            <input type="hidden" id="jsControllerName" value="${js_controller}"/>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <form method="get" name="edit">
            <input type="hidden" name="id" value="-1" />
        </form>
        <form action="../DPMS/Surveycapture.action" method="get" name="capture">
            <input type="hidden" name="id" value="-1"/>
            <input type="hidden" name="windowTitle" value=""/>
        </form>
        <form method="get" name="request">
            <input type="hidden" name="id" value="-1"/>
            <input type="hidden" name="requestId"/>
            <input type="hidden" name="viewMode"/>
            <input type="hidden" name="url"/>
        </form>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>