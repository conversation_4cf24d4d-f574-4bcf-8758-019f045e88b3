<%-- 
    Document   : request-survey-fill
    Created on : Jun 30, 2015, 5:48:59 PM
    Author     : <PERSON> @ Block Networks S.A. de C.V.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title></title>

        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script type='text/javascript' src="../scripts/framework/bnext/administrator/forms/request-survey-fill-list.js?${systemVersion}"></script>
        <style>
            .css_quick_access span,
            .css_continue_fill span {
                width: 100%;
                text-align: center;
                display: block;
            }
            .css_usersGrid_dialog_colPop img {
                height: 1.25rem !important;
                width: 1.25rem !important;
            }
            .bnext .dijitDialogPaneContent {
                min-width: 16.5rem;
            }
            .buttons {
                float: right;
            }
            #myDialog {
                width: 26.5rem;
            }
            .dijitDialogPaneContentArea tbody tr td label {
                width: unset;
            }
            .dijitDialogPaneContentArea {
                margin: auto;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra bnext">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons" action="../view/v.documentos.view">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="window_title">
                                    </h3>
                                    <div class="float-right">
                                        <!-- boton de opciones -->
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class ="grid-container">
                            <table id="dataGrid"></table>
                        </div>
                        <div class ="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                            <input type="hidden" name="intDocumentoId" id="intDocumentoId" />
                            <input type="hidden" name="impersonateFill" id="impersonateFill" />
                            <input type="hidden" name="vchState" id="vchState" value="Edit" />
                            <input type="hidden" name="nodo" id="nodo" />
                            <input type="hidden" name="repositorio" id="repositorio" />
                            <input type="hidden" name="leer" value="si" />
                            <input type="hidden" name="onClose" id="onClose" value="history.back()" />
                            <input type="hidden" id="loggedUserBusinessUnitName" value="${loggedUserBusinessUnitName}"/>
                            <a type="button" name="impersonateButton" id="impersonateButton" class="Button addToGrid"></a>
                            <table id="usersGrid"></table>
                        </div>                            
                    </form>
                </div>
            </div>
        </div>
        <div data-dojo-type="dijit/Dialog" id="myDialog" title="Select the desired options">
            <table class="dijitDialogPaneContentArea">
                <tr>
                    <td><input type="checkbox" id="dbox1" checked data-dojo-type="dijit/form/CheckBox"></td>
                    <td><label for="dbox1" id="labelNH">No header</label></td>
                </tr>
                <tr>
                    <td><input type="checkbox" id="dbox2" checked data-dojo-type="dijit/form/CheckBox"></td>
                    <td><label for="dbox2" id="labelNM">No menu</label></td>
                </tr>
                <tr>
                    <td><input type="checkbox" id="dbox3" checked data-dojo-type="dijit/form/CheckBox"></td>
                    <td><label for="dbox3" id="labelND">No duplicate access</label></td>
                </tr>
            </table>
            <div class="buttons actionButtons displayFlex">
                <input type="button" id="cancel" value="Cancel" class="button Button"/>
                <input type="button" id="ok" value="Ok" class="button Button"/>
            </div>
        </div>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>