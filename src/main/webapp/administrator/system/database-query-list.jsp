<%-- 
    Document   : database-query-list
    Created on : Feb 22, 2018, 6:30:26 PM
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Consultas a base de datos</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script src="../scripts/framework/bnext/administrator/system/database-query-list.js?${systemVersion}" type="text/javascript"></script>
        <style>
            .bnext .dijitDialog.fixedTop .buttons.contentButtons {
                border: none!important;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left ${module}" id="mainTitle"></h3>
                                    <div class="float-right">
                                        <a href="javascript: void(0);" id="addFavorite" class="material-icons" title="Agregar a favorito(s)">
                                            favorite
                                        </a>
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                        </div>
                        <div class="grid-container table_workarea">
                            <table id="grid"></table>
                        </div>
                        <div class="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                            <input type="hidden" id="canDelete" value="${canDeleteDatabaseQuery}"/>
                            <input type="hidden" id="canUpdate" value="${canUpdateDatabaseQuery}"/>
                            <input type="hidden" id="canSyncCache" value="${canSyncCache}"/>
                            <input type="hidden" id="canEnableCache" value="${canEnableCache}"/>
                            <input type="hidden" id="module" value="${module}"/>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>