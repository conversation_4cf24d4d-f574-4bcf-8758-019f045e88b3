<%-- 
    Document   : input para cats
    Created on : 29/03/2010, 10:39:17 AM
    Author     : <PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8"); %>
<%--  Declaración de los Java Bean --%>
<jsp:useBean id="cat" scope="request" class="isoblock.configuracion.catalogo"/>
<jsp:useBean id="action" scope="request" class="Framework.Action.DefaultAction"/>
<%--  Asignación de propiedades de los Java Beans --%>
<jsp:setProperty name="cat" property="*" />
<%-- Includes --%>
<html class="html-reset">
    <head>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
    </head>
    <body writingsuggestions="false" textprediction="false" class="bnext">
<%@ include file="../../includes/getsesionusuario.jsp"%>
<%@ include file="../../includes/titulotabla.jsi"%>
<jsp:include page="../../components/requiredScripts.jsp" />
<link rel="stylesheet" type="text/css" href="../administrator/common/style/input.css?${systemVersion}" />
<link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
<script type='text/javascript' src="../administrator/common/controller/input.js?${systemVersion}"></script> 
<script language="JavaScript" src="../scripts/template-superior.js"></script>
<%
boolean verValor = false; //esta variable debe estar en true cuando esta patalla se utilize para dar de alta en el catalogo de calificacion de audiotiras
String comboValor = "";
String titulo = cat.parameter("titulo",request);
String titulo_valor = "valor";
String catalogo = action.getCatalog();
String modulos = "";
boolean showSave = true;
boolean showRetencion = false;


if(cat.getVchState().equals(propiedadesJsp.JSP_EDITAR)) {
    cat.datosCatalogo();
}
if(isoblock.common.Properties.ACCIONES_MANAGER.equals(sesionrolacciones) || isoblock.common.Properties.ADMINISTRADOR_MAESTRO.equals(sesionroladministracion)) {
    if ("fuenteaccion".equals(catalogo)) {
        cat.setIntCatalogoId(propiedadesJsp.CATALOGO_ACCIONES);
        modulos = ""
               
              // + propiedadesJsp.SIN_MODULO + "-Sin Liga"
               +propiedadesJsp.MODULO_AUDITORIAS + tags.getString("common.input.ModulodeAuditorias")
               + "," + propiedadesJsp.MODULO_FORMULARIOS + "-Campos en formularios"
               //+ "," + propiedadesJsp.MODULO_AUDITORIAS_PREGUNTAS + "-Preguntas de Auditorias"
               //+ "," + propiedadesJsp.MODULO_INDICADORES + tags.getString("common.input.ModulodeIndicadores")
               + "," + propiedadesJsp.MODULO_QUEJAS + tags.getString("common.input.ModulodeQuejas")
               ;
       titulo = tags.getString("common.input.FUENTESDEACCIONES", false);
    }
}
//cats de quejas
if(isoblock.common.Properties.QUEJAS_LEADER.equals(sesionrolquejas) || isoblock.common.Properties.ADMINISTRADOR_MAESTRO.equals(sesionroladministracion)) {
    if ("fuentequeja".equals(catalogo)) {
        cat.setIntCatalogoId(propiedadesJsp.CATALOGO_QUEJAS);
        titulo = tags.getString("common.input.FUENTESDEQUEJAS");
    }
    
    if ("analisis_queja".equals(catalogo)) {
       cat.setIntCatalogoId(propiedadesJsp.CATALOGO_TEXTO_QUEJA);
       verValor = true;
       comboValor = "SI-"+tags.getString("common.input.SI")+",NO-"+tags.getString("common.input.NO");
       titulo_valor = "requiere-analisis";
       titulo = tags.getString("common.input.TiposdeAnalisisdeQuejas");
    }
}
//cats de quejas
if(isoblock.common.Properties.AUDITORIAS_MANAGER.equals(sesionroldocumentos) || isoblock.common.Properties.ADMINISTRADOR_MAESTRO.equals(sesionroladministracion)) {
    if ("fuentecuestionario".equals(catalogo)) {
        verValor = true;
        cat.setIntCatalogoId(propiedadesJsp.CATALOGO_CUESTIONARIO_CALIFICACIONES);
        titulo = tags.getString("common.input.AltadeTIPOSDECALIFICACIONES");
    }
}
//catalogo de tipos de documento
    if ("tipodocumento".equals(catalogo)) {
        cat.setIntCatalogoId(propiedadesJsp.CATALOGO_TIPO_DOCUMENTO);
        titulo = tags.getString("common.input.ALTADETIPOSDEDOCUMENTO");
        showRetencion = true;
        showSave = isAdmin 
            || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_ENCARGADO)
            || services.contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)
            || services.contains(mx.bnext.access.ProfileServices.CATALOGS_DOCUMENTS);
    }
    if ("lugarAlmacenamiento".equals(catalogo)) {
        cat.setIntCatalogoId(propiedadesJsp.CATALOGO_LUGAR_ALMACENAMIENTO);
        titulo = tags.getString("common.input.ALTADELUGARDEALMACENAMIENTO");
        showRetencion = false;
        showSave = isAdmin 
            || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_ENCARGADO)
            || services.contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)
            || services.contains(mx.bnext.access.ProfileServices.CATALOGS_DOCUMENTS);
    }
    if ("clasificacionInformacion".equals(catalogo)) {
        cat.setIntCatalogoId(propiedadesJsp.CATALOGO_CLASIFICACION_INFORMACION);
        titulo = tags.getString("common.input.ALTADECLASIFICACIONINFORMACION");
        showRetencion = false;
        showSave = isAdmin 
            || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_ENCARGADO)
            || services.contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)
            || services.contains(mx.bnext.access.ProfileServices.CATALOGS_DOCUMENTS);
    }
    if ("disposicion".equals(catalogo)) {
        cat.setIntCatalogoId(propiedadesJsp.CATALOGO_DISPOSICION);
        titulo = tags.getString("common.input.ALTADEDISPOSICION");
        showRetencion = false;
        showSave = isAdmin 
            || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_ENCARGADO)
            || services.contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)
            || services.contains(mx.bnext.access.ProfileServices.CATALOGS_DOCUMENTS);
    }
%>
<script type='text/javascript'>
    var win;
    function reloadWindow(e){
        win.easyRefresh1();
    }
    window.onload = function()
    {
        hideLoader();
    }
    function hideLoader(){
        document.getElementById("loader").style.display = "none";
    }
    function showLoader(){
        document.getElementById("loader").style.display = "";
    }

// Recibe el objeto window w de la ventana que la abre. Se hace un reload en W al momento de cerrarse esta
function whenClose(w){
    win=w;
    document.onmouseover = null;
    window.onunload = reloadWindow;
}
///////////////////////////////////////////////////////////////////////////////////////

function whichCatalog(catalog){
  switch(catalog){
      case 'analisis_queja':
        return "../view/v.complaint.analisis.list.view?catalogo=<%=catalogo%>"; 
        break;
      case 'fuenteaccion':
        return "../view/v.action.resources.list.view?catalogo=<%=catalogo%>"; 
        break;
      case 'lugarAlmacenamiento':
        return "../view/v.storage.place.list.view?catalogo=<%=catalogo%>"; 
        break;
      case 'fuentequeja':
        return "../view/v.complaint.sources.list.view?catalogo=<%=catalogo%>"; 
        break;
      case 'tipodocumento':
        return "../view/v-document-type-list.view?catalogo=<%=catalogo%>"; 
        break;
      case 'fuentecuestionario':
        return "../view/v.questionariee.ratings.list.view?catalogo=<%=catalogo%>"; 
        break;
      case 'clasificacionInformacion':
        return "../view/v.classification.information.list.view?catalogo=<%=catalogo%>"; 
        break;
      case 'disposicion':
        return "../view/v.disposition.list.view?catalogo=<%=catalogo%>"; 
        break;
    }
}
   
function saveCatalogo() {
<%if (cat.getVchState().equals(propiedadesJsp.JSP_EDITAR)) {%>
        document.form1.actionType.value = 'actualizaCatalogo';
        document.form1.msgAction.value = '<%=tags.getString("common.input.Seactualizaronlosdatosdelcatalogocorrectamente")%>';
<%} else {%>
        document.form1.actionType.value = 'insertaCatalogo';
        document.form1.msgAction.value = '<%=tags.getString("common.input.ElRegistroSeGuardoCorrectamente")%>';
<%}%>
        document.form1.msgErrorAction.value = '<%=tags.getString("configuracion.repositoriohandle.javascript.RegistroDuplicado")%>';
        document.form1.className.value = 'isoblock.configuracion.catalogo';
        document.form1.sectionName.value ='catalogo';
        var catalog ='${catalog}';
        document.form1.editPage.value = whichCatalog(catalog);
        if (catalog === 'fuenteaccion') {
            document.form1.addPage.value = '../view/v.action.catalogs.new.view';
        }
        if (catalog === 'lugarAlmacenamiento') {
            document.form1.addPage.value = '../view/v.action.documents.catalogs.new.view';
        }
        if (catalog === 'analisis_queja') {
            document.form1.addPage.value = '../view/v.action.complaints.catalogs.new.view';
        }
        if (catalog === 'clasificacionInformacion') {
            document.form1.addPage.value = '../view/v.action.documents.catalogs.new.information.view';
        }
        if (catalog === 'disposicion') {
            document.form1.addPage.value = '../view/v.action.documents.catalogs.new.disposition.view';
        }
        document.form1.vchState.value = '<%= propiedadesJsp.JSP_ALTA %>';
        document.form1.msgButton1.value = '<%=tags.getString("configuracion.perfileshandle.Alta")%>';
        document.form1.msgButton2.value = '<%=tags.getString("configuracion.perfileshandle.Control")%>';
        document.form1.action="../view/v.common.actional.view";
        showLoader();
        document.form1.submit();
        window.close();
}
///////////////////////////////////////////////////////////////////////////////////////

function limpiaColor(){
    limpiar('vchTexto');
<%if(verValor) {%>
    limpiar('intValor');
<%}%>
    limpiar('txtDescripcion');
}

function limpiar(name){
    eval("document.getElementById('" + name + "').style.backgroundColor='#FFFFFF'");
}

  function validateSubmit() {
    var catalog ='<%=catalogo%>';
    switch(catalog){
      case "tipodocumento":
        $('#form1').validate({
          rules:{
            vchTexto: {
              required : true,
              maxlength : 255
            },
            txtDescripcion: {
              required : true,
              maxlength : 255
            }
          },
          messages:{
            vchTexto: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>'
          }
        });
        var valid = $("#form1").valid();
        if(!valid) {
          return;
        }
      case "lugarAlmacenamiento":
        $('#form1').validate({
          rules:{
            vchTexto: {
              required : true,
              maxlength : 255
            },
            txtDescripcion: {
              required : true,
              maxlength : 255
            }
          },
          messages:{
            vchTexto: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>'
          }, errorPlacement: function (error, element) {
              error.insertBefore(element);
          }
        });
        var valid = $("#form1").valid();
        if(!valid) {
          return;
        }
      case "fuentecuestionario":
        $('#form1').validate({
          rules:{
            vchTexto: {
              required : true
            },
            intValor:{
              required: true
            },
            txtDescripcion: {
              required : true,
              maxlength : 255
            }
          },
          messages:{
            vchTexto: '<%=tags.getString("common.input.obligatorio")%>',
            intValor: '<%=tags.getString("common.input.obligatorio")%>',
            txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>'
          }
        });
        var valid = $("#form1").valid();
        if(!valid) {
          return;
        }
      case "fuenteaccion":
        $('#form1').validate({
          rules:{
            vchTexto: {
              required : true,
              maxlength : 255
            },
            txtDescripcion: {
              required : true,
              maxlength : 255
            },
            intValor:{
              required: true
            }
          },
          messages:{
            vchTexto: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            intValor: '<%=tags.getString("common.input.obligatorio")%>'
          }, errorPlacement: function (error, element) {
              error.insertBefore(element);
          }
        });
        var valid = $("#form1").valid();
        if(!valid) {
          return;
        }
      case "fuentequeja":
        $('#form1').validate({
          rules:{
            vchTexto: {
              required : true,
              maxlength : 255
            },
            txtDescripcion: {
              required : true,
              maxlength : 255
            }
          },
          messages:{
            vchTexto: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            intValor: '<%=tags.getString("common.input.obligatorio")%>'
          }, errorPlacement: function (error, element) {
              error.insertBefore(element);
          }
        });
        var valid = $("#form1").valid();
        if(!valid) {
          return;
        }
      case "analisis_queja":
        $('#form1').validate({
          rules:{
            vchTexto: {
              required : true,
              maxlength : 255
            },
            txtDescripcion: {
              required : true,
              maxlength : 255
            }
          },
          messages:{
            vchTexto: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>'
          }, errorPlacement: function (error, element) {
              error.insertBefore(element);
          }
        });
        var valid = $("#form1").valid();
        if(!valid) {
          return;
        }
      case "clasificacionInformacion":
        $('#form1').validate({
          rules:{
            vchTexto: {
              required : true,
              maxlength : 255
            },
            txtDescripcion: {
              required : true,
              maxlength : 255
            }
          },
          messages:{
            vchTexto: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
            txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>'
          }, errorPlacement: function (error, element) {
            error.insertBefore(element);
          }
        });
        var valid = $("#form1").valid();
        if(!valid) {
          return;
        }
        case "disposicion":
            $('#form1').validate({
              rules:{
                vchTexto: {
                  required : true,
                  maxlength : 255
                },
                txtDescripcion: {
                  required : true,
                  maxlength : 255
                }
              },
              messages:{
                vchTexto: '<%=tags.getString("common.input.obligatorio255caracteres")%>',
                txtDescripcion: '<%=tags.getString("common.input.obligatorio255caracteres")%>'
              }, errorPlacement: function (error, element) {
                  error.insertBefore(element);
              }
            });
            var valid = $("#form1").valid();
            if(!valid) {
              return;
            }
    }
        limpiaColor();
        faltante= "<%=tags.getString("common.input.Faltandellenarlossiguientescampos")%>";
        falta=false;
          
        if(document.getElementById('vchTexto').value.trim("")==""){
            faltante+="\n - <%=tags.getString("common.input.Nombre").replace(":","") %>";
            document.getElementById('vchTexto').style.backgroundColor=requiredfield();
           if(!falta){
                document.getElementById('vchTexto').focus();
                falta=true;
            }
     
    }
    
  
 <%if(verValor) {%>
        if(document.getElementById('intValor').value == ""){
            faltante+="\n <%=tags.getString("common.input.-Valor")%>";
            document.getElementById('intValor').style.backgroundColor=requiredfield();
            if(!falta){
                document.getElementById('intValor').focus();
                falta=true;
            }
        }
<%}%>
    
      
      if(document.getElementById('txtDescripcion').value.trim("") == ""){
            faltante+="\n <%=tags.getString("common.input.-Descripcion")%>";
            document.getElementById('txtDescripcion').style.backgroundColor=requiredfield();
            if(!falta){
                document.getElementById('txtDescripcion').focus();
                falta=true;
            }
        }

        if(falta){
            showDialogRequired(faltante);
        } else {
          
            saveCatalogo();
        }
    }
function limpiaForm(miForm) {
// recorremos todos los campos que tiene el formulario
$(':input', miForm).each(function() {
var type = this.type;
var tag = this.tagName.toLowerCase();
//limpiamos los valores de los campos…
if (type == 'text' || type == 'password' || tag == 'textarea' || type == 'number')
this.value = "";
// excepto de los checkboxes y radios, le quitamos el checked
// pero su valor no debe ser cambiado
else if (type == 'checkbox' || type == 'radio')
this.checked = false;
// los selects le ponesmos el indice a -
else if (tag == 'select')
this.selectedIndex = -1;
});
}



</script>
    <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">        
                    <form class="container-form grid-floating-action-buttons" novalidate="novalidate" name="form1" method="post" action="" id="form1" class="form1">
                        <div class="header grid-container _body">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle"><%=titulo%></h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint"></div>
                        <div class="grid-container">
                            <div class="grid-x grid-padding-x" id="mainDiv">
                                <div class="cell small-12">
                                    <div class="textarea-component">
                                        <textarea maxlength="255" type="text" id="vchTexto" class="input" name="vchTexto" required="required" <% if (!showSave)
                                          out.write("disabled");%>><%=cat.getVchTexto()%></textarea>
                                        <label id="name"><%=tags.getString("common.input.Nombre")%></label>
                                    </div>
                                </div>
                                <%if (!comboValor.isEmpty()) {%>
                                    <div class="cell small-12" id="valorFieldId">
                                        <div class="select-component">
                                            <select name="intValor" id="intValor" class="select required" <% if (!showSave)
                                                out.write("disabled");%>>
                                                <%= cat.comboArray(comboValor.split(","), cat.getIntValor())%>
                                            </select>
                                            <label class="label <%=titulo_valor%>"></label>
                                        </div>
                                    </div>
                                <%} else {%>
                                    <div class="cell small-12" id="valorFieldId">
                                        <div class="textarea-component">
                                            <input type="number" class="input" name="intValor" required="required" id="intValor" value="<%=cat.getIntValor()%>" <% if (!showSave) {
                                                out.write("disabled");
                                            } %>>
                                            <label class="label <%=titulo_valor%>"></label>
                                        </div>
                                    </div>
                                <%}%>
                                <%if (!modulos.isEmpty()) {%>
                                    <div class="cell small-12">
                                        <div class="select-component">
                                            <select name="intModuloId" id="intModuloId" class="select required" <% if (!showSave)
                                                out.write("disabled");%>>
                                                <option value="0"><%=tags.getString("common.input.SinLiga")%></option>
                                                <%= cat.comboArray(modulos.split(","), cat.getIntModuloId())%>
                                            </select>
                                            <label class="label"><%=tags.getString("common.input.Ligaa")%></label>
                                        </div>
                                    </div>
                                <%}%>
                                <div class="cell small-12">
                                    <div class="textarea-component">
                                        <textarea class="input" id="txtDescripcion" required="required" name="txtDescripcion"<% if (!showSave)
                                            out.write("disabled");%>><%=cat.getTxtDescripcion()%></textarea>
                                        <label id="description"></label>
                                    </div>
                                </div>
                                <% if (showRetencion) {%>
                                    <div class="cell small-12">
                                        <div class="select-component">
                                            <select name="esRetenible" id="esRetenible" class="select" <% if (!showSave)
                                            out.write("disabled");%>>
                                                <option value="false" <%= cat.isEsRetenible() ? "" : "selected"%>><%= tags.getString("opcion.NO")%></option>
                                                <option value="true"<%= cat.isEsRetenible() ? "selected" : ""%>><%= tags.getString("opcion.SI")%></option>
                                            </select>
                                            <label for="esRetenible"><%= tags.getString("catalogo.lugarAlmacenamiento.Almacena")%></label>
                                        </div>
                                    </div>
                                <% } %>
                                <div class="cell small-12 actionButtons">
                                    <% if (showSave) {%>
                                        <input class="Button raised-button" type="button" value="<%=tags.getString("boton.Guardar")%>" name="btnSave" id="btnSave" onclick="validateSubmit();">
                                    <% }%>
                                        <input class="Button" type="button" value="<%=tags.getString("boton.Cancelar")%>" name="btnCancel" id="btnCancel">
                                    <% if (showSave) {%>
                                        <input class="Button" type="button" value="<%=tags.getString("boton.Limpiar")%>" name="btnClean" id="btnClean" onclick="limpiaForm();">
                                    <% }%>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <input type="hidden" name="intCatalogoId" id="intCatalogoId" value="<%=cat.getIntCatalogoId()%>">
                                <input type="hidden" name="intTipoId" id="intTipoId" value="<%=cat.getIntTipoId()%>">
                                <input type="hidden" name="vchState" id="vchState" value="<%=cat.getVchState()%>">
                                <input type="hidden" name="catalogo" id="catalogo" value="<%=catalogo%>">
                                <input type=hidden name='actionType' value='x'>
                                <input type=hidden name='className' value=''>
                                <input type=hidden name='editPage' value=''>
                                <input type=hidden name='addPage' value=''>
                                <input type=hidden name='msgAction' value=''>
                                <input type=hidden name='msgButton1' value=''>
                                <input type=hidden name='msgButton2' value=''>
                                <input type=hidden name='sectionName' value=''>
                                <input type="hidden" name="msgErrorAction" value="">
                                <script>
                                    <%if(!verValor) {%>
                                        block('valorFieldId');
                                    <%}%>
                                </script>
                                <%=footer()%>
                                <%@ include file="../../includes/mensajeespera.txt"%>
                            </div>
                        </div>
                    </form>
                    <%try { cat.finalize(); } catch (Throwable t) { t.printStackTrace(); }%>
                    <%--  Finaliza el cuerpo del JSP  --%>
                </div>
            </div>
        </div>
    </body>
</html> 