<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8"); %>
<%--  Declaración de los Java Bean --%>
<jsp:useBean id="configuracion" scope="request" class="isoblock.configuracion.catalogo"/>
<%--  Asignación de propiedades de los Java Beans --%>
<jsp:setProperty name="configuracion" property="*" />
<%-- Includes --%>
<%@ include file="../../templates/superior.tem"%>
<%@ include file="../../includes/getsesionusuario.jsp"%>
<%@ include file="../../includes/titulotabla.jsi"%>
<%
//empieza codigo para establecer parametros de busqueda
int anchoInputs = 55;
String tabla = "tblcatalogo"; //solamente se usa para el update
String self = "catalogo.jsp"; //solamente se usa para el update
String JS = "cambiarEstado";

String query = "SELECT * FROM "+tabla ;
String[] camposEditar = {"0"};
String[] columnDataBase = {
    "vchTexto",
    "txtdescripcion"
};
String[] columnTitle = {
    tags.getString("common.catalogo.NombredelCampo"),
    tags.getString("common.catalogo.Descripcion")
};
String order = configuracion.getOrderBy();
String filtro = " 0 = 1 ";
String campoEstado = "intEstadoC"; //que se pueda borrar cualiquier campo //en esta consulta todos los registros tiene el mismo id, el ID se tomaria como inttipoid
String[] campoEstadosBorrar = {"6"};
String campoId = "intTipoId";
String forma = "form1";
String actionErase = "catalogo.jsp";
String actionUpdate = "catalogo.jsp";

String[] alts = {
    ""
   ,""
   ,""
   ,tags.getString("common.catalogo.Activo")
   ,""
   ,tags.getString("common.catalogo.Inactivo")
};

//empieza codigo para validar tipo de catalogo y permisos
String tituloCatalogo = tags.getString("common.catalogo.CATALOGO");
String catalogo = configuracion.parameter("catalogo", request);
System.out.println("Catalogo: "+configuracion.getCatalogo());
//boolean permiso = false;

//catalogos de acciones
if(sesionrolacciones.equals(isoblock.common.Properties.ACCIONES_MANAGER) || sesionroladministracion.equals(isoblock.common.Properties.ADMINISTRADOR_MAESTRO)) {
    if (catalogo.equals("fuenteaccion")) {
        tituloCatalogo += tags.getString("common.catalogo.FuentedeAcciones");
        filtro = " intcatalogoid = "+isoblock.common.Properties.CATALOGO_ACCIONES;
        configuracion.setIntCatalogoId(isoblock.common.Properties.CATALOGO_ACCIONES);
        camposEditar = new String[3];
        camposEditar[0] = "0";
        camposEditar[1] = "1";
        camposEditar[2] = "2";
    }
}
//catalogos de quejas
if(sesionrolquejas.equals(isoblock.common.Properties.QUEJAS_LEADER) || sesionroladministracion.equals(isoblock.common.Properties.ADMINISTRADOR_MAESTRO)) {
    tituloCatalogo= tags.getString("common.catalogo.CONTROLMASIVO");
    if (catalogo.equals("fuentequeja")) {
        tituloCatalogo+=tags.getString("common.catalogo.FuentedeQuejas");
        filtro = " intcatalogoid = "+isoblock.common.Properties.CATALOGO_QUEJAS;
        configuracion.setIntCatalogoId(isoblock.common.Properties.CATALOGO_QUEJAS);
        camposEditar = new String[3];
        camposEditar[0] = "0";
        camposEditar[1] = "1";
        camposEditar[2] = "2";
    }
    if (catalogo.equals("analisis_queja")) {
        tituloCatalogo+=tags.getString("common.catalogo.TipoAnalisisQuejas");
        filtro = " intcatalogoid = "+isoblock.common.Properties.CATALOGO_TEXTO_QUEJA;
        configuracion.setIntCatalogoId(isoblock.common.Properties.CATALOGO_TEXTO_QUEJA);
        camposEditar = new String[2];
        camposEditar[0] = "0";
        camposEditar[1] = "1";
        //camposEditar[2] = "2";
        columnDataBase = new String[2];
        columnDataBase[0] = "vchTexto";
        columnDataBase[1] = "txtdescripcion";
        //columnDataBase[2] = "intvalor";
        columnTitle = new String[2];
        columnTitle[0] = tags.getString("common.catalogo.NombredelCampo");
        columnTitle[1] = tags.getString("common.catalogo.Descripcion");
        //columnTitle[2] = tags.getString("common.catalogo.Valor");
    }
    
}
//catalogos de auditorias
   if(services.contains(mx.bnext.access.ProfileServices.AUDIT_QUALITY_MANAGER) || isAdmin) {
    tituloCatalogo= tags.getString("common.catalogo.CONTROLMASIVO");
    if (catalogo.equals("estadoauditorias")) {
        /*tituloCatalogo+=tags.getString("common.catalogoslist.FuentedeAuditorias")*/;
        filtro = " intcatalogoid = "+isoblock.common.Properties.CATALOGO_ESTADOS_AUDITORIAS;
        configuracion.setIntCatalogoId(isoblock.common.Properties.CATALOGO_ESTADOS_AUDITORIAS);
        camposEditar = new String[2];
        camposEditar[0] = "";
        camposEditar[1] = "1";
        
    }
}
//catalogos de cuestionario
if(sesionrolquejas.equals(isoblock.common.Properties.AUDITORIAS_MANAGER) || sesionroladministracion.equals(isoblock.common.Properties.ADMINISTRADOR_MAESTRO)) {
    if (catalogo.equals("fuentecuestionario")) {
        tituloCatalogo += tags.getString("common.catalogo.CalificacionesCuestionarios");
        filtro = " intcatalogoid = "+isoblock.common.Properties.CATALOGO_CUESTIONARIO_CALIFICACIONES;
        configuracion.setIntCatalogoId(isoblock.common.Properties.CATALOGO_CUESTIONARIO_CALIFICACIONES);
        columnDataBase = new String[3];
        columnDataBase[0] = "vchTexto";
        columnDataBase[1] = "txtdescripcion";
        columnDataBase[2] = "intvalor";
        columnTitle = new String[3];
        columnTitle[0] = tags.getString("common.catalogo.NombredelCampo");
        columnTitle[1] = tags.getString("common.catalogo.Descripcion");
        columnTitle[2] = tags.getString("common.catalogo.Valor");
        camposEditar = new String[3];
        camposEditar[0] = "0";
        camposEditar[1] = "1";
        camposEditar[2] = "2";
        anchoInputs = 35;
        campoEstado = "";
        campoEstadosBorrar[0] = "4";
    }
}

//catalogos de tipos de documento
if(sesionrolquejas.equals(propiedadesJsp.DOCUMENTOS_MANAGER) || sesionroladministracion.equals(isoblock.common.Properties.ADMINISTRADOR_MAESTRO)) {
    if (catalogo.equals("tipodocumento")) {
        tituloCatalogo += tags.getString("common.catalogo.TIPOSDEDOCUMENTOS");
        filtro = " intcatalogoid = "+propiedadesJsp.CATALOGO_TIPO_DOCUMENTO;
        configuracion.setIntCatalogoId(propiedadesJsp.CATALOGO_TIPO_DOCUMENTO);
        columnDataBase = new String[2];
        columnDataBase[0] = "vchTexto";
        columnDataBase[1] = "txtdescripcion";
        //columnDataBase[2] = "intvalor";
        columnTitle = new String[2];
        columnTitle[0] = tags.getString("common.catalogo.NombredelCampo");
        columnTitle[1] = tags.getString("common.catalogo.Descripcion");
        //columnTitle[2] = tags.getString("common.catalogo.Valor");
        camposEditar = new String[3];
        camposEditar[0] = "0";
        camposEditar[1] = "1";
        //camposEditar[2] = "2";
        anchoInputs = 35;
    }
}

//catalogo de formas
if(sesionroladministracion.equals(isoblock.common.Properties.ADMINISTRADOR_MAESTRO)) {
    if (catalogo.equals("formas")) {
        tituloCatalogo += tags.getString("common.catalogo.PiedeFormas");
        anchoInputs = 35;
        alts[3] = tags.getString("common.catalogo.LosPiesdelaFormaestanActivos,PresioneParaVerlaVistaPreviaEstandardeEstaForma");
        tabla = "tblpieformas"; //para el update
        columnDataBase = new String[3];
        columnDataBase[0] = "vchformanombre";
        columnDataBase[1] = "vchpieizq";
        columnDataBase[2] = "vchpieder";
        columnTitle = new String[3];
        columnTitle[0] = tags.getString("common.catalogo.Formas"); //TODO: Poner esto en ingles
        columnTitle[1] = tags.getString("common.catalogo.PiedeFormaIzquierdo");
        columnTitle[2] = tags.getString("common.catalogo.PiedeFormaDerecho");

        camposEditar = new String[2];
        camposEditar[0] = "1";
        camposEditar[1] = "2";
        query = "SELECT * FROM "+tabla;
        filtro = "0=0";
        //campoEstado = "intestado";
        campoEstado = "";
        campoId = "intpieid";
        actionErase = "";
        JS = "vistaPrevia"; //el java script que se ejecutara al dar clic en el cubo
    }
}
boolean actualizar = false;
if(configuracion.getVchState().equals("update")) {
    actualizar = true;
        for (int i = 0; i < columnDataBase.length; i++) {
            configuracion.find("SELECT "+campoId+" FROM "+tabla+" WHERE " + filtro);
            while (configuracion.next()) {
                if (!configuracion.parameter(columnDataBase[i] + "_" + configuracion.fieldStringS(campoId), request).equals("")) {
                    String strsql = "UPDATE "+tabla+" SET " + columnDataBase[i] + " = '" + String.valueOf(configuracion.parameter(columnDataBase[i] + "_" + configuracion.fieldStringS(campoId), request)).replaceAll("Â", "").trim() + "' WHERE " + filtro + " AND "+campoId+" = " + configuracion.fieldStringS(campoId) + ";";
                    //System.out.println(strsql);
                    if (!configuracion.update(strsql)) {
                        System.out.println("Error: No se pudo actualizar");
                        actualizar = false;
                        break;
                    }
                }
            }
            if(!actualizar){ break; }
        }
}
boolean borrado = false;
if(configuracion.getVchState().equals("delete")) {
    borrado = true;
    if(!configuracion.borrarCatalogo()) {
        borrado = false;
    }
}
boolean cambioEstado = false;
if(configuracion.getVchState().equals("changeState")) {
    cambioEstado = true;
    if(!configuracion.cambiarEstado()) {
        cambioEstado = false;
    }
}
%>
<script>
var winAux;
function callProc(e) {
    winAux.whenClose(window);
}
function agregar() {
    winAux = window.open("../view/v.action.catalogs.new.view?vchTexto=&catalogo=<%=catalogo%>","agregar","height=250px,width=430px,left=200px,top=100px, scrollbars=0");
    try{document.getElementById('vchState').value='update';}catch(e){}
    //winAux.document.onmouseover = callProc;
}
document.getElementsByTagName('body')[0].style.width = '900px';
<% if(!JS.equals("")) {%>
<%
String ancho = "800";
String alto = "800";
String ruta = "800";
%>
function beforeExternalSubmit(){
    try{
        setParamsRefresh1();
    }catch(e){}
}
function vistaPrevia(id) {
    var vp = window.open("iframeCaller.jsp?fromID=true&ancho=<%=ancho%>&alto=<%=alto%>&id="+id,"agregar","height=<%=alto%>,width=<%=ancho%>,left=200,top=100, scrollbars=1, resizable=1");
    this.disable(vp.form);
    vp.focus();
}
<%}%>
function cambiarEstado(aux) {
    document.getElementById("vchState").value = "changeState";
    document.getElementById("intTipoId").value = aux;
    document.form1.submit();
}
</script>

<%--  Inicio parte de documento  --%>
<form name="form1" method="post" action="" id="form1">
    <%=header(tituloCatalogo)%>
    <input type="hidden" name="catalogo" id="catalogo" value="<%=configuracion.getCatalogo()%>">
    <input type="hidden" name="vchState" id="vchState" value="<%=configuracion.getVchState()%>">
    <input type="hidden" name="<%=campoId%>" id='<%= campoId%>' value="<%=configuracion.getIntTipoId() %>">
    <input type="hidden" name="<%=campoEstado%>" id='<%= campoEstado%>' value="">
    <table>
    <%if(filtro.equals(" 0 = 1 ")) {%>
    <tr><td align="center" class="alertmessage"><%=tags.getString("common.catalogo.NoseCuentaconPermisosParaVerEsteCatalogo")%></td></tr>
    <%}%>
<%if(actualizar){%>
    <tr><td align="center" class="alertmessage"><%=tags.getString("common.catalogo.SeActualizoelRegistroCorrectamente")%></td></tr>
<%} else if (configuracion.getVchState().equals("update")) {%>
    <tr><td align="center" class="alertmessage"><%=tags.getString("common.catalogo.NosePuedeActualizarelRegistro")%></td></tr>
<%}%>
<%if(borrado){%>
    <tr><td align="center" class="alertmessage"><%=tags.getString("common.catalogo.SeBorroelRegistroCorrectamente")%></td></tr>
<%} else if (configuracion.getVchState().equals("delete")) {%>
    <tr><td align="center" class="alertmessage"><%=tags.getString("common.catalogo.NosePuedeBorrarelRegistro")%></td></tr>
<%}%>
<%if(cambioEstado){%>
    <tr><td align="center" class="alertmessage"><%=tags.getString("common.catalogo.SeCambioelEstadodelRegistroCorrectamente")%></td></tr>
<%} else if (configuracion.getVchState().equals("changeState")) {%>
    <tr><td align="center" class="alertmessage"><%=tags.getString("common.catalogoslist.NosePudoCambiarelEstadodelRegistro")%></td></tr>
<%}%>
    </table>
    &nbsp;&nbsp;
    <%if (catalogo.equals("estadoauditorias")){%>
        <%=configuracion.listCatalogo(query, columnDataBase, columnTitle, order, filtro, campoEstado, campoEstadosBorrar, campoId, forma, "", alts,camposEditar,actionUpdate,anchoInputs,self,"")%>
    <%}else {%>
        <%=configuracion.listCatalogo(query, columnDataBase, columnTitle, order, filtro, campoEstado, campoEstadosBorrar, campoId, forma, actionErase, alts,camposEditar,actionUpdate,anchoInputs,self,JS)%>
    <%}%>
<%=footer()%>
<%@ include file="../../includes/mensajeespera.txt"%>
<script>
<%if (catalogo.equals("formas")) {%>
    document.getElementById('addChangesBtn').disabled = true;
    document.getElementById('addChangesBtn').style.display = 'none';
<%}%>
<%if (catalogo.equals("estadoauditorias")) {%>
    document.getElementById('addChangesBtn').disabled = true;
    document.getElementById('addChangesBtn').style.display = 'none';
<%}%>
</script>
<input type="hidden" name="shouldSubmit" id="shouldSubmit" value="true" />
</form>
<%--  Finaliza el cuerpo del JSP  --%>
<%try{configuracion.finalize();} catch(Exception e){}%>
<%@ include file="../../templates/inferior.tem"%>