require(['dojo/on','core','bnext/gridComponent', 'bnext/columnTypes', 'dojo/dom', 'bnext/gridComponentUtil', 'dojo/domReady!']
        , function (on,core, gridComponent, columnTypes, dom, gcUtil) {
    var grid = {}, w = window;
    fillFooter(); 
    function doEverything(i18n) {
        var btnAdd = dom.byId("btnAdd");        
        on(btnAdd,"click",function() {
           core.navigateLegacy("v.department.view")
        });   
        var formTemplate_column = {
            title: i18n.colNamesFormTemplate,
            type: columnTypes.text,
            id: 'formTemplate.description',
            isSortable: true,
            isTransient: false,
            searchObj:{col:1,type:'texto'}
        };
        var columns = [formTemplate_column];
        
        gcUtil.column(columns)
            .localizeRecord('../DPMS/Settings.Department.action', i18n)
        ;
        
        grid = new gridComponent({
            size:dom.byId('gridSize').value,
            id:"grid", 
            container:"dataGrid",
            searchContainer:"#auto",
            resultsInfo:"#auto", 
            paginationInfo:"#auto", 
            serviceStore:"Settings.Department.action", 
            methodName:"getRows", 
            columns : {
                is:"#auto"
                ,toogleEstatus: cambiaEstatus,
                extraColumns:columns
            } ,  //columnas,
            logging_column:true
        }); 
        function editRecord_grid (id){
            document.submit_form.id.value = id;
            document.submit_form.submit();
        }
        function cambiaEstatus(id) {
            core.showLoader(i18n.Validate_toggling_status);
            core.dialogExecutioner(i18n.Validate_toggling_confirm, core.i18n.Validate.yes, core.i18n.Validate.no, 
                "Settings.Department.action", "toggleStatus", [id],
                core.i18n.Validate.edit_success, "", core.i18n.Validate.accept, grid);
            core.hideLoader();
        }
        w.editRecord_grid = editRecord_grid;
        return grid.setPageSize(dom.byId('gridSize').value);
    }
    
    core.setLang('lang/catalog/department/nls/department.list').then(function (i18n) {
        doEverything(i18n).then(core.hideLoader);
    });
});