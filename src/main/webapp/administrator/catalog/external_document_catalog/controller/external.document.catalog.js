require([
  'dojo/dom',
  'core',
  'dojo/on',
  'bnext/saveHandle',
  'dojo/Deferred',
  'dojo/dom-class',
  'dojo/query',
  'dojo/dom-attr',
  'dojo/dom-construct',
  'bnext/module/util/table-preview',
  'bnext/LinkedGridFactory',
  'bnext/gridComponentUtil',
  'bnext/callMethod',
  'bnext/angularNavigator',
  'bnext/administrator/system/query-config',
  'dojo/domReady!'
], (dom, core, on, saveHandle, Deferred, domClass, query, domAttr, domConstruct, TablePreview, LinkedGridFactory, gcUtil, callMethod, angularNavigator, QueryConfig) => {
  const id = dom.byId('id');
  const description = dom.byId('description');
  const code = dom.byId('code');
  const valueColumnDom = dom.byId('valueColumn');
  const sortColumnDom = dom.byId('sortColumn');
  const enabledHierarchyDom = dom.byId('enabledHierarchy');
  const displayColumnDom = dom.byId('displayColumn');
  const fieldTypeDom = dom.byId('fieldType');
  const sortDirectionDom = dom.byId('sortDirection');
  const valueColumnIdDom = dom.byId('valueColumnId');
  const sortColumnIdDom = dom.byId('sortColumnId');
  const editableConfiguration = dom.byId('editableConfiguration').value === 'true';
  const duplicate = dom.byId('duplicate').value === 'true';
  const labelColumnIdDom = dom.byId('labelColumnId');
  let hasPendingRest = false;
  let queryGrid;
  function doAll(i18n) {
    $('#validate_form').validate({
      ignore: '.displayNone,.optional',
      rules: {
        code: {
          required: true,
          maxlength: 100
        },
        description: {
          required: true,
          maxlength: 255
        },
        valueColumn: {
          required: true
        },
        sortColumn: {
          required: false
        },
        sortDirection: {
          required: false
        },
        displayColumn: {
          required: true
        },
        fieldType: {
          field_type_required: true
        }
      },
      messages: {
        code: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 100),
        description: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 255),
        fieldType: core.i18n.Validate.missingCombo,
        valueColumn: core.i18n.Validate.missingCombo,
        displayColumn: core.i18n.Validate.missingCombo
      },
      errorPlacement: (error, element) => {
        error.insertBefore(element);
      }
    });
    if (dom.byId('duplicateQuery')) {
      on(dom.byId('duplicateQuery'), 'click', () => {
        core.confirm(i18n.confirmDuplicate, null, null, null, null, 'confirm-duplicate-external-dcoument-catalog').then(() => {
          core.showLoader().then(() => {
            angularNavigator.navigateLegacy(`v.external.document.catalog.view?id=${id.value}&duplicate=true`);
          });
        });
      });
    }
    on(dom.byId('testQuery'), 'click', () => {
      if (fieldTypeDom.value !== 'catalog-hierarchy') {
        const valueColumn = valueColumnDom.value;
        const sortColumn = sortColumnDom.value;
        const displayColumn = displayColumnDom.value;
        if (valueColumn === '' || displayColumn === '' || sortColumn === '') {
          $('#validate_form').valid();
          core.dialog(i18n.Validate.testQuery);
          return;
        }
      }
      core.showLoader(i18n.validation_sql_in_process).then(() => {
        testQuery().then((result) => {
          if (result.operationEstatus) {
            let columns = null;
            if (fieldTypeDom.value === 'catalog-hierarchy') {
              columns = result.columns?.map((column) => column.code);
            } else {
              const valueColumnText = valueColumnDom.options[valueColumnDom.selectedIndex].innerHTML;
              const sortColumnText = sortColumnDom.options[sortColumnDom.selectedIndex].innerHTML;
              const displayColumnText = displayColumnDom.options[displayColumnDom.selectedIndex].innerHTML;
              columns = [valueColumnText, displayColumnText, sortColumnText];
            }
            const table = TablePreview.renderTable(result.samples, columns) || i18n.noQueryData;
            updateLabelValuesExceedsMaxLength(result.labelValuesExceedsMaxLength);
            core.dialog(`${i18n.Validate.query_test_success}<br /><br />${table}`);
          } else {
            core.error(`${i18n.Validate.query_test_error}<br/><br/>${result.errorMessage}`);
          }
          core.hideLoader();
        });
      });
    });
    if (dom.byId('chkGenerate')) {
      on(dom.byId('chkGenerate'), 'click', codeGen);
    }
    $('#statusText').text(i18n[dom.byId('statusLabelKey').value] || i18n.status_invalid);
    on(fieldTypeDom, 'input', onFieldTypeChange);
    on(displayColumnDom, 'input', requestTest);
    on(dom.byId('saveBtn'), 'click', save);
    on(dom.byId('cancelBtn'), 'click', core.cancel);
    function setCustomValidations() {
      $.validator.addMethod('field_type_required', (value) => value !== 'select', core.i18n.Validate.missingField);
    }
    setCustomValidations();
    function updateLabelValuesExceedsMaxLength(labelValuesExceedsMaxLength) {
      const labelValuesExceedsMaxLengthText = dom.byId('labelValuesExceedsMaxLengthText');
      if (labelValuesExceedsMaxLength === null || typeof labelValuesExceedsMaxLength === 'undefined') {
        labelValuesExceedsMaxLengthText.innerHTML = i18n.testFirst;
      } else if (labelValuesExceedsMaxLength === true) {
        labelValuesExceedsMaxLengthText.innerHTML = core.i18n.yes;
      } else {
        labelValuesExceedsMaxLengthText.innerHTML = core.i18n.no;
      }
    }
    function disabledFields() {
      domAttr.set(fieldTypeDom, 'disabled', 'disabled');
      domAttr.set(valueColumnIdDom, 'disabled', 'disabled');
      domAttr.set(sortColumnIdDom, 'disabled', 'disabled');
      domAttr.set(sortDirectionDom, 'disabled', 'disabled');
      domAttr.set(labelColumnIdDom, 'disabled', 'disabled');
    }
    function save() {
      core.showLoader(core.i18n.Validate.saving).then(() => {
        if (!$('#validate_form').valid()) {
          core.hideLoader();
          return;
        }
        const data = queryGrid.getGroundData();
        if (!data || data.length === 0) {
          core.error(i18n.defaultOption);
          core.hideLoader();
          return;
        }
        if (fieldTypeDom.value === 'catalog-hierarchy' && data[0].enableHierarchy !== true) {
          core.error(i18n.mustSelectQueryEnabledHierarchy);
          core.hideLoader();
          return;
        }
        saveAction();
      });
    }
    function disableSelection() {
      domClass.remove(dom.byId('pickQuery'), 'Button');
      domClass.remove(dom.byId('pickQuery'), 'addToGrid');
      queryGrid.getDialog().hide();
    }
    function enableSelection() {
      domClass.add(dom.byId('pickQuery'), 'Button');
      domClass.add(dom.byId('pickQuery'), 'addToGrid');
    }
    function validateSelection() {
      const def = new Deferred();
      if (queryGrid.getGroundData().length > 0) {
        disableSelection();
        def.reject();
      } else {
        enableSelection();
        def.resolve();
      }
      return def;
    }
    function fillSelect(selectDom, optionsData, selectValue) {
      core.fillCombo(selectDom, optionsData, 'text', 'value', false);
      selectDom.value = selectValue;
      if (selectValue !== '' && selectDom.value === '') {
        selectDom.value = '';
      }
    }
    function enableAutoGenerateCode() {
      const chkGenerate = dom.byId('chkGenerate');
      if (chkGenerate) {
        chkGenerate.click();
      }
    }
    function setDuplicateData() {
      if (!duplicate) {
        return;
      }
      id.value = '-1';
      enableAutoGenerateCode();
    }
    function loadQueryColumns(valueColumnValue, displayColumnValue, sortColumnValue) {
      const data = queryGrid.getGroundData();
      if (!data || !data.length) {
        setDuplicateData();
        return core.resolvedPromise();
      }
      const queryId = data[0].id;
      return callMethod({
        url: 'ExternalDocumentCatalog.action',
        method: 'getColumns',
        params: [queryId]
      }).then((resultado) => {
        fillSelect(valueColumnDom, resultado, valueColumnValue);
        fillSelect(displayColumnDom, resultado, displayColumnValue);
        fillSelect(sortColumnDom, resultado, sortColumnValue);
        setDuplicateData();
      }, core.hideLoader);
    }
    function onQueryAfterAdd() {
      requestTest();
      onQuerySelected();
    }
    function onQuerySelected() {
      if (!queryGrid) {
        setDuplicateData();
        return;
      }
      const data = queryGrid.getGroundData();
      if (data && data.length > 0 && data[0].enableHierarchy) {
        fieldTypeDom.value = 'catalog-hierarchy';
        domClass.remove(enabledHierarchyDom, 'displayNone');
      } else {
        domClass.add(enabledHierarchyDom, 'displayNone');
      }
      applyFieldTypeChange();
      if (description.value === null || typeof description.value === 'undefined' || description.value.trim() === '') {
        if (data[0]) {
          description.value = data[0].description;
        }
      }
      const savedValueColumn = valueColumnIdDom.value || '';
      const savedDisplayColumn = labelColumnIdDom.value || '';
      const savedSortColumn = sortColumnIdDom.value || '';
      return loadQueryColumns(savedValueColumn, savedDisplayColumn, savedSortColumn).then(validateSelection);
    }
    function destroySelectOptions(select) {
      for (const optionDom of query('option', select)) {
        if (domAttr.get(optionDom, 'selected')) {
          optionDom.parentNode.value = null;
        }
        domConstruct.destroy(optionDom);
      }
    }
    function changeDisplay(selectorClass, action) {
      if (action === 'show') {
        for (const dom1 of query(`.${selectorClass}`)) {
          dom1.style.display = '';
        }
      } else if (action === 'hide') {
        for (const dom1 of query(`.${selectorClass}`)) {
          dom1.style.display = 'none';
        }
      }
    }
    function requestTest() {
      hasPendingRest = true;
      updateLabelValuesExceedsMaxLength(null);
    }
    function onFieldTypeChange() {
      requestTest();
      applyFieldTypeChange();
    }
    function applyFieldTypeChange() {
      if (fieldTypeDom.value === 'catalog-hierarchy') {
        valueColumnDom.value = '';
        displayColumnDom.value = '';
        sortColumnDom.value = '';
        sortDirectionDom.value = '';
        $(valueColumnDom).rules('remove', 'required');
        $(displayColumnDom).rules('remove', 'required');
        $(sortColumnDom).rules('remove', 'required');
        changeDisplay('columns-config', 'hide');
      } else {
        $(valueColumnDom).rules('add', 'required');
        $(displayColumnDom).rules('add', 'required');
        $(sortColumnDom).rules('add', 'required');
        changeDisplay('columns-config', 'show');
      }
    }
    function setDefaultOptionText(select, text) {
      for (const optionDom of query('option', select)) {
        optionDom.innerHTML = text;
      }
    }
    function onQueryCleared() {
      requestTest();
      destroySelectOptions(valueColumnDom);
      destroySelectOptions(displayColumnDom);
      destroySelectOptions(sortColumnDom);
      const values = [];
      fillSelect(valueColumnDom, values, '');
      fillSelect(displayColumnDom, values, '');
      fillSelect(sortColumnDom, values, '');
      setDefaultOptionText(valueColumnDom, i18n.defaultOption);
      setDefaultOptionText(displayColumnDom, i18n.defaultOption);
      setDefaultOptionText(sortColumnDom, i18n.defaultOption);
      return validateSelection();
    }
    function buildGrid() {
      const columns = [];
      gcUtil
        .column(columns)
        .push('code', core.i18n.colNameCode, gcUtil.cType().Text())
        .push(
          'description',
          core.i18n.colNamesDescription,
          gcUtil.cType().RenderMenuLink({
            legacy: true,
            url: `${QueryConfig.CATALOG_VIEW.formularie}?id={id}`,
            params: ['id', 'description'],
            title: '{description}'
          })
        )
        .push(
          'source',
          i18n.colNameFieldDatabaseSource,
          gcUtil.cType().TextMap([
            { name: i18n.colNameBnextQMS, value: 'bnext-qms' },
            { name: i18n.colNameConfigurable, value: 'configurable' }
          ])
        );

      queryGrid = LinkedGridFactory.create(
        {
          id: 'grid-query',
          freezeHeader: false,
          onBeforeAddActivate: validateSelection,
          onGroundLoaded: onQuerySelected,
          onAfterAdd: onQueryAfterAdd,
          onAfterRemove: onQueryCleared,
          addButton: dom.byId('pickQuery')
        },
        +id.value || -1,
        'ExternalDocumentCatalog.action',
        i18n,
        columns
      );
      if (!editableConfiguration) {
        queryGrid.disable(true, false);
      }
    }
    function testQuery() {
      const def = new Deferred();
      const data = queryGrid.getGroundData();
      const queryId = data[0].id;
      let params;
      const fieldType = fieldTypeDom.value || '';
      const labelColumnId = displayColumnDom.value || -1;
      if (hasPendingRest) {
        params = [-1, queryId, fieldType, labelColumnId];
      } else {
        params = [+id.value || -1, queryId, fieldType, labelColumnId];
      }
      callMethod({
        url: '../DPMS/ExternalDocumentCatalog.action',
        method: 'testQuery',
        params: params
      }).then(
        (result) => {
          def.resolve(result);
        },
        (e) => {
          def.resolve({
            operationEstatus: 0,
            errorMessage: e
          });
        }
      );
      return def;
    }
    function saveAction() {
      const groundData = queryGrid.getGroundData();
      const queryId = groundData[0].id;
      let valueColumn = null;
      if (valueColumnDom.value > 0) {
        valueColumn = {
          id: valueColumnDom.value || null
        };
      }
      let sortColumn = null;
      if (sortColumnDom.value > 0) {
        sortColumn = {
          id: sortColumnDom.value || null
        };
      }
      let labelColumn = null;
      if (displayColumnDom.value > 0) {
        labelColumn = {
          id: displayColumnDom.value || null
        };
      }
      const data = {
        id: +id.value || -1,
        description: description.value,
        code: dom.byId('chkGenerate')?.checked ? '' : code.value,
        deleted: 0,
        status: dom.byId('status').value || 1,
        fieldType: fieldTypeDom.value,
        query: {
          id: queryId
        },
        valueColumn: valueColumn,
        labelColumn: labelColumn,
        sortColumn: sortColumn,
        sortDirection: dom.byId('sortDirection').value || null
      };
      sh.setData(data);
      sh.saveData();
    }
    function codeGen() {
      core.toggleGenerate('#chkGenerate', '#code');
    }
    buildGrid();
    applyFieldTypeChange();
    enableAutoGenerateCode();
    if (!editableConfiguration) {
      disabledFields();
      hasPendingRest = false;
    }
  }
  const sh = new saveHandle({
    savedObjName: 'ExternalDocumentCatalog',
    serviceStore: 'ExternalDocumentCatalog.action',
    methodName: 'saveGeneric'
  });
  core.setLang('lang/catalog/external_document_catalog/nls/external.document.catalog').then(doAll).then(core.hideLoader);
});
//@ sourceURL=c.external.document.catalog.controller
