<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="${cssHtml} html-reset">
    <head>
        <title>Perfiles</title>
        <%@include file="../../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <script type="text/javascript" src="c.organizational.unit.controller?${systemVersion}"></script> 
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons" method="POST" id="validate_form">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle"></h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint"></div>
                        <div class="grid-container">
                            <div class="grid-x grid-padding-x" id="mainDiv">
                                <div class="cell small-12 medium-6" >
                                    <div id="codeContainer" class="textarea-component generate-code-cection">
                                        <input type="text" required="required" name="code" id="txtCode" value=""/>
                                        <label id="lblCode"></label>
                                        <div id="generateCodeSection">
                                            <%@include file="/../components/chk-generate.jsp" %>
                                        </div>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" required="required" name="description" id="description" value=""/>
                                        <label id="lblDescription"></label>
                                    </div>
                                </div>        
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <s:select name="predecessorId" id="predecessorId" 
                                            list="predecessor"
                                            listKey="value" listValue="text" >
                                        </s:select>
                                        <label for="predecessorId"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <s:select name="documentManagerId" id="documentManagerId" 
                                            list="documentManager"
                                            listKey="value" listValue="text" >
                                        </s:select>
                                        <label for="documentManagerId"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div class="table-component">
                                        <div class="">
                                            <div id="filePicker"></div>
                                            <div id="logoOption"></div>
                                            <div class="inside_subheader"></div>
                                            <div id="logoUploader"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="cell small-12 actionButtons">
                                    <jsp:include page="../../../components/saveButtons.jsp"/>   
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <s:hidden name="systemColor" id="SYSTEM_COLOR" />
                                <s:hidden name="userId" id="SESSION_USER" /><%-- Mailisima practica!, este valor se puede obtener directo en el *.java con getLoggedUserId() --%>
                                <s:hidden name="configurationRole" id="SESSION_MODULE_ROLE" />
                                <s:hidden name="id" id="id"/>
                                <s:hidden name="status" id="status"/>
                                <input type="hidden" id="onBtnAdd" value="../view/v.organizational.unit.view" />
                                <input type="hidden" id="onBtnControl" value="../view/v.organizational.unit.list.view" />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%@include file="../../../components/footer.jsp" %>
    </body>
</html>