require(["bnext/saveHandle", "entity/organizationalUnit", 'dojo/query', 'dojo/dom', 'dojo/on',
    'core', 'bnext/callMethod', 'bnext/module/FilePicker', 'dojo/domReady!'],
        function (saveHandle, entity, query, dom, on, core, callMethod, FilePicker) {
            var data = entity, uploader,
                misDatos = new saveHandle({
                    savedObjName: "misDatos",
                    serviceStore: "Settings.OrganizationalUnit.action",
                    hookUpdateSuccess: successful,
                    methodName: "save"
                });
            function doEverything(i18n) {
                dom.byId('predecessorId').options[0].innerHTML = i18n.Default.selectTag;
                dom.byId('documentManagerId').options[0].innerHTML = i18n.Default.selectTag;
                defineValidationRules();
                var id = dom.byId('id').value;
                uploader = new FilePicker({
                    modern: true,
                    useDownloadIcon: false,
                    uploadPath: '../DPMS/UploadImage.fileUploader',
                    accept: 'image/*',
                    downloadParameters: 'fileId=[id]&documentId=-1',
                    i18n: {
                        filePickerDownloadButtonTitle: i18n.filePickerViewTitle,
                        filePickerFileLabel: i18n.lblLogo,
                        filePickerDownloadImageTitle: i18n.filePickerViewTitle,
                        startingConversion: i18n.startingConversion
                    },
                    downloadAction: '../view/v-download.view',
                    onFileSelected: function (req) {
                        if (req) {
                            entity.fileId = req.id;
                        } else {
                            entity.fileId = null;
                        }
                    },
                    onFileUploaded: function (file) {
                        switch (file.status) {
                            case 'invalid-content-type':
                            case 'invalid-extension':
                                delete data.fileId;
                                core.dialog(i18n.invalidImage);
                                return;
                        }
                    },
                    onDelete:function(){
                        entity.fileId = null;     
                    }
                }, 'filePicker');
                on(dom.byId('saveBtn'), 'click', save);
                on(dom.byId('cancelBtn'), 'click', cancel);
                on(dom.byId('chkGenerate'), 'click', toggleGenerateKey);
                if (id && id !== '-1') {
                    callMethod({
                        url: "../DPMS/Settings.OrganizationalUnit.action",
                        method: "load",
                        params: [id]
                    }).then(populate, hideLoader);
                } else {
                    dom.byId('status').value = 1;
                    hideLoader();
                }
            };

            function toggleGenerateKey() {
                toggleGenerate('#chkGenerate', '#txtCode');
            }

            function defineValidationRules() {
                $('#validate_form').validate({
                    rules: {
                        code: {
                            required: true,
                            maxlength: 50
                        },
                        description: {
                            required: true,
                            maxlength: 255
                        },
                        documentManagerId: {
                            required: true
                        }
                    },
                    messages: {
                        code: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 100),
                        description: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 255),
                        documentManagerId: core.i18n.Validate.missingCombo
                    },
                    errorPlacement: function (error, element) {
                        error.insertBefore(element);
                    }
                });
            }

            function populate(object) {
                var loadedVal;
                if (!isNull(object.fileId)) {
                    entity.fileId = object.fileId;
                }
                query('input', 'validate_form').forEach(function (input) {
                    loadedVal = object[input.name];
                    if (input.name && (loadedVal || loadedVal == '0')) {
                        input.value = loadedVal;
                    }
                });
                query('select', 'validate_form').forEach(function (input) {
                    loadedVal = object[input.name];
                    if (input.name && (loadedVal || loadedVal == '0')) {
                        input.value = loadedVal;
                    }
                });

                if (+dom.byId('documentManagerId').value !== +object["documentManagerId"]) {
                    fillCombo('#documentManagerId', [object['documentManager']], 'description', 'id', false)
                    dom.byId('documentManagerId').value = object["documentManagerId"];
                }
                $("#predecessorId option[value='" + object.id + "']").remove();
                if(!core.isNull(entity.fileId)){
                    uploader.set('value', entity.fileId);
                }              
                core.hideLoader();
            }
            function save() {
                var valid = $("#validate_form").valid();
                valid && dialog(core.i18n.Validate.confirm_message, core.i18n.Validate.yes, core.i18n.Validate.no)
                        .then(function () {
                            showLoader(core.i18n.Validate.saving);
                            var data = entity;
                            data.id = dom.byId('id').value;
                            query('input', 'validate_form').forEach(function (input) {
                                if (input.name) {
                                    data[input.name] = input.value;
                                }
                            });
                            var generateCode = dojo.attr(dom.byId('chkGenerate'), 'checked');
                            if (generateCode) {
                                data.code = '';
                            }
                            data.predecessorId = +dom.byId('predecessorId').value || null;
                            data.documentManagerId = +dom.byId('documentManagerId').value || null;
                            misDatos.setData(data);
                            misDatos.saveData();

                        });
            }
            core.setLang('lang/catalog/organizational_unit/nls/organizational.unit').then(doEverything);
        });