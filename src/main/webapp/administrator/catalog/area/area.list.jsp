<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Perfiles</title>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <jsp:include page="../../../components/requiredScripts.jsp" />
        <script type="text/javascript" src="../controller/c.area.list.controller?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="mainTitle">
                                    </h3>
                                    <div class="float-right">
                                        <!-- boton de opciones -->
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                            <div id="buttonsSection">
                               <div id="btnAdd" class="fab-button">
                                    <i class="material-icons">add</i>
                                    <span id="catalogoLabel"></span>
                                </div>
                            </div>
                        </div>
                        <div class ="grid-container table_workarea">
                            <table id="dataGrid"></table>
                        </div>
                        <div class ="displayNone">
                            <s:hidden name="systemColor" id="SYSTEM_COLOR" />
                            <s:hidden name="systemLanguage" id="SYSTEM_LANGUAGE" />
                            <s:hidden name="systemLocalization" id="SYSTEM_LOCALIZATION" />
                            <s:hidden name="gridSize" id="gridSize" />
                            <s:hidden name="savedWindowFilters" id="savedWindowFilters" />
                            <s:hidden name="windowPath" id="windowPath" />
                            <s:hidden name="labelAreaField1" id="labelAreaField1" />
                            <s:hidden name="labelAreaField2" id="labelAreaField2" />
                            <s:hidden name="labelAreaField3" id="labelAreaField3" />
                            <s:hidden name="labelAreaField4" id="labelAreaField4" />
                            <s:hidden name="labelAreaField5" id="labelAreaField5" />
                            <s:hidden name="labelAreaField6" id="labelAreaField6" />
                            <s:hidden name="isAreaCustomFieldsEnabled" id="isAreaCustomFieldsEnabled" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <form name="submit_form" action="v.area.view">
            <input type="hidden" id="id" name="id" value=''/>
        </form>
        <form name="log_form" action="v.logging.view">
            <input type="hidden" id="record_id" name="record_id" value=''/>
            <input type="hidden" id="logElement" name="logElement" value='Area'/>
        </form>
        <%@include file="../../../components/footer.jsp" %>        
    </body>
</html>
