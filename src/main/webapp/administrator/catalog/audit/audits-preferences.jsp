<!DOCTYPE html>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8");%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<html class="${cssHtml} html-reset">
    <head>
        <%@include file="../../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <script src="../scripts/framework/bnext/administrator/catalog/settings/preferences.js?${systemVersion}" type="text/javascript"></script>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
    </head>
    <body writingsuggestions="false" textprediction="false">
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <%@include file="../../../components/loader.jsp" %>
                    <form class="container-form grid-floating-action-buttons" method="POST" id="catalog_handle">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="window_title_audit">Preferencias: Auditorías</h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint"></div>
                        <div class="grid-container">
                            <div class="grid-x grid-padding-x" id="mainDiv">
                                <div class="cell small-12">
                                    <div class="select-component">
                                        <select name="auditedViewSurvey" id="auditedViewSurvey" data-cy="auditedViewSurvey" class="select" value="0">
                                            <option value="1"></option>
                                            <option value="0"></option>
                                        </select>
                                        <label id="lblAuditedViewSurvey"></label>
                                    </div>
                                </div>
                                <div class="cell small-12">
                                    <div class="textarea-component">
                                        <input type="text" id="maxChangeDateAllowed" data-cy="maxChangeDateAllowed" name="maxChangeDateAllowed" required="required"></input>
                                        <label id="lblMaxChangeDateAllowed"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 actionButtons">
                                    <jsp:include page="../../../components/saveButtons.jsp"/>   
                                </div>			
                                <div class="cell small-12">&nbsp;</div>
                            </div>
                        </div>
                    </form>
                    <s:hidden name="systemColor" id="SYSTEM_COLOR" />
                    <s:hidden name="systemLanguage" id="SYSTEM_LANGUAGE" />
                    <s:hidden name="systemLocalization" id="SYSTEM_LOCALIZATION" />
                    <s:hidden name="gridSize" id="gridSize" />
                    <s:hidden name="floatingGridSize" id="floatingGridSize" />
                    <s:hidden name="savedWindowFilters" id="savedWindowFilters" />
                    <s:hidden name="windowPath" id="windowPath" />
                    <s:hidden name="userId" id="userId" /> 
                    <%@include file="../../../components/footer.jsp" %>
                </div>
            </div>
        </div>
    </body>
</html>