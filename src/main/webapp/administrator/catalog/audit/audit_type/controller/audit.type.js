require([
    'core', 'bnext/saveHandle', 'dojo/query', 'dojo/dom', 'dojo/dom-attr', 'dojo/on',
    'bnext/callMethod', 'dijit/Tooltip',
    'dojo/domReady!'
],
function (core, SaveHandle, query, dom, domAttr, on, callMethod, Tooltip) {
    core.fillFooter();
    var misDatos;
    function domByQry(qry) {
        var x = query(qry);
        if (!x || !x.length) {
            return null;
        } else if (x.length > 1) {
            throw "Unexpected result exception! expected 1 result, but found " + x.length + ' items.';
        }
        return x[0];
    }
    function doEverything(i18n) {
        var qualifiedQuestion = domByQry('[value=qualifiedQuestion]');
        var addActivity = domByQry('[value=addActivity]');
        var saveBtn = dom.byId('saveBtn');
        var cancelBtn = dom.byId('cancelBtn');
        var chkGenerate = dom.byId('chkGenerate');
                dom.byId('scope').options[0].innerHTML = i18n.Default.selectTag;
                defineValidationRules(i18n);
        misDatos = new SaveHandle({
                    savedObjName: "misDatos",
                    serviceStore: "Settings.AuditType.action",
                    methodName: "save"
                });
        function save() {
            if (!$("#validate_form").valid()) {
                console.log('Invalid data!');
            } else {
            core.confirmDialog(dom.byId('id').value).then(
                                function () {
                    core.showLoader(core.i18n.Validate.saving);
                                    var data = {
                                        id: -1, 
                                        code: "",
                                        description: "", 
                                        status: 1, 
                                        scope:0,
                                        "qualifiedQuestion": 0, 
                                        "findingCapable": 0, 
                                        "confirmedByAudited": 0,
                                        "acceptedByAudited": 0,
                                        "supportStaff": 0,
                                        "technicalExperts": 0,
                                        "auditorsInTraining": 0
                                    };
                                    data.id = dom.byId('id').value;
                                    query('input[type=text]', 'validate_form').forEach(function (input) {
                                        if (input.name) {
                                            data[input.name] = input.value;
                                        }
                                    });
                                    query('input[type=checkbox]', 'validate_form').forEach(function (input) {
                                        if (input.checked) {
                                            data[input.value] = 1;
                        } else
                                        {
                                            data[input.value] = 0;
                                        }
                                    });
                                    data.scope = +dom.byId('scope').value;
                                    var generateCode = domAttr.get(dom.byId('chkGenerate'), 'checked');
                                    if (generateCode) {
                                        data.code = '';
                                    }
                                    misDatos.setData(data);
                                    misDatos.saveData();
                }, core.hideLoader
                                );
                    }
        }
        function toggleExternalStaff() {
                    var externalStaff = $('#externalStaff');
                    var lblExternalStaff = $('#lblExternalStaff');
                    on(lblExternalStaff, 'click', function (e) {
                        lblExternalStaff.toggleClass('open');
                        externalStaff.slideToggle('slow');
                    });
        }
        function load(id) {
            dom.byId("scope").disabled = true;
                    var args = {
                        url: "../DPMS/Settings.AuditType.action",
                        method: "load",
                params: [id]
            };
            return callMethod(args).then(
                            function (resultado) {
                                populate(resultado);
                            },
                core.hideLoader
                            );
        }
        function clicked(domNode, fn) {
            domNode && on (domNode, 'click', fn);
        }
        function qualifiedQuestionChecked(showMessage) {
            if (addActivity.checked || showMessage) {
                Tooltip.show(i18n.addActivityToQuestions, addActivity);
                setTimeout(function() {
                    Tooltip.hide(addActivity);
                }, 3000)
            }
            activityChecked();
            addActivity.disabled = !qualifiedQuestion.checked;
        }
        addActivity.setAttribute("title", i18n.addActivityToQuestions);
        function activityChecked() {
            if(!qualifiedQuestion.checked && addActivity.checked) {
                addActivity.checked = false;
            }
        }
        clicked(qualifiedQuestion, function(evt) {
            qualifiedQuestionChecked();
        });
        clicked(addActivity, activityChecked);
        clicked(saveBtn, save);
        clicked(cancelBtn, core.cancel);
        clicked(chkGenerate, toggleGenerateKey);
                toggleExternalStaff();
                var id = dom.byId('id').value;
                if (id && +id !== -1) {
            load(id).then(showHelp);
                } else {
                    dom.byId('status').value = 1;
            core.hideLoader().then(showHelp);
                }
        function showHelp() {
            qualifiedQuestionChecked(true);
    }
    }
    function populate(object) {
            var loadedVal = 0;
            query('input[type=text]', 'validate_form').forEach(function (input) {
                loadedVal = object[input.name];
                if (input.name && (loadedVal || +loadedVal === 0)) {
                    input.value = loadedVal;
                }
            });
            query('input[type=checkbox]', 'validate_form').forEach(function (input) {
                loadedVal = object[input.value];
                if (loadedVal) {
                    input.checked = true;
                }
            });
            dom.byId("scope").value = object.scope;
        core.hideLoader();
    }
    function toggleGenerateKey() {
    toggleGenerate('#chkGenerate', '#txtCode');
    }
function defineValidationRules(i18n) {
    $('#validate_form').validate({
        rules: {
            code: {
                required: true
                , maxlength: 100
            },
            description: {
                required: true
                , maxlength: 100
            },
            scope:{required:true}
        },
        messages: {
            code: specifiedMessage(i18n.Validate.invalid_required_text, 'specify', 10),
            description: specifiedMessage(i18n.Validate.invalid_required_text, 'specify', 100),
            scope:i18n.Validate.missingCombo
        }
    });

}
    core.setLang('lang/catalog/audit/audit_type/nls/audit.type').then(doEverything);
});