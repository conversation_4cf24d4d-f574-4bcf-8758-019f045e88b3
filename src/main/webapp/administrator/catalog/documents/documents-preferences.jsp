<!DOCTYPE html>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8");%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<html class="${cssHtml}">
    <head>
        <title>Acciones</title>
        <%@include file="../../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../administrator/settings/preferences/style/preferences.css?${systemVersion}">
        <script src="../scripts/framework/bnext/administrator/catalog/settings/preferences-document.js?${systemVersion}" type="text/javascript"></script>
        <style type="text/css">
            :root {
                --welcome-bg-url: url(../style/s-welcome-bg.style?${welcomeBgHash});
            }
            .defaultFile .information {
                margin-bottom: 10px;
                background: #bfbfbf;
            }
            .defaultFile .information label {
                text-align: left;
                padding: 5px 0px 0px 2px;
            }
            
            .status-container {
                display: flex !important;
                width: 12.813rem;
                justify-content: center;
                align-items: center;
                padding: 1rem !important;
                flex-direction: row-reverse;
                gap: 1rem;
                background: none !important;
            }

            #pdfConversionStatus {
                display: flex;
                align-items: center;
            }

            #refreshPdfConversionStatus {
                cursor: pointer;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../../components/loader.jsp" %>
        <form method="POST" id="catalog_handle">
            <div class ="body claro">
            <div class="content_title">
                <h1 id="window_title_document" class="window-title"></h1>
            </div>
            <ul class="content_area">
                <li><label  id="lblDocs" class="subheader clickable expandable slow_and_closed" for="docs">Documentos</label></li>
                <li id="docs" style="display: none;">
                        <ul>
                            <li>
                                <label id="lblDocumentCodeScope"></label>
                                <div class="info left gridInfo documentCodeScope documentCodeScope-${documentCodeScope}"></div>
                            </li>
                            <li><label id="lblDocumentCodeScopeConfig"></label>
                                <select size="2" id="documentCodeScope" name="documentCodeScope">
                                    <option value=1></option>
                                    <option value=2></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblDaysAnticipation"></label>
                                <input type="text" id="daysAnticipation" name="daysAnticipation">
                            </li>
                            <li>
                                <label id="lblDefaultVersionDoc"></label>
                                <input type="text" id="documentInitialVersion" name="documentInitialVersion" class="String">
                            </li>
                            <li><label id="lblPendingToRead"></label>
                                <select size="2" id="readers" name="readers">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblShowReadersForPrint"></label>
                                <select size="2" id="showReadersForPrint" name="showReadersForPrint">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblPositionForControlledCopy"></label>
                                <select size="2" id="positionForControlledCopy" name="positionForControlledCopy">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblshowPositionsForCopies"></label>
                                <select size="2" id="showPositionsForCopies" name="showPositionsForCopies">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblPrintReceiptEnabled"></label>
                                <select size="2" id="printReceiptEnabled" name="printReceiptEnabled">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblPrintingDateFormat"></label>
                                <s:select name="printingDateFormat" cssClass="customData" id="printingDateFormat" 
                                          list="pritingDateFormats" 
                                          listKey="value" listValue="text" >
                                </s:select>
                            </li>
                            <li>
                                <label id="lblPrintingDateTimeFormat"></label>
                                <s:select name="printingDateTimeFormat" cssClass="customData" id="printingDateTimeFormat" 
                                          list="pritingDateTimeFormats" 
                                          listKey="value" listValue="text" >
                                </s:select>
                            </li>
                            <li><label id="lblExpiredRequestMailDocumentManager"></label>
                                <select size="2" id="expiredRequestMailDocumentManager" name="expiredRequestMailDocumentManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li> 
                            <li><label id="lblExpiredDocumentPendingModuleManager"></label>
                                <select size="2" id="expiredDocumentPendingModuleManager" name="expiredDocumentPendingModuleManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblExpiredDocumentPendingDocumentManager"></label>
                                <select size="2" id="expiredDocumentPendingDocumentManager" name="expiredDocumentPendingDocumentManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li> 
                            <li><label id="lblExpiredDocumentMailModuleManager"></label>
                                <select size="2" id="expiredDocumentMailModuleManager" name="expiredDocumentMailModuleManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li> 
                            <li><label id="lblExpiredDocumentMailDocumentManager"></label>
                                <select size="2" id="expiredDocumentMailDocumentManager" name="expiredDocumentMailDocumentManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li> 
                            <li><label id="lblExpiredDocumentMailLastAuthor"></label>
                                <select size="2" id="expiredDocumentMailLastAuthor" name="expiredDocumentMailLastAuthor">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblReapproveByManager"></label>
                                <select size="2" id="reapproveByDocumentManager" name="reapproveByDocumentManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblRequestReturnsToVerification"></label>
                                <select size="2" id="requestReturnsToVerification" name="requestReturnsToVerification">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <div id="defaultFile"></div>
                                <input type="hidden" id="defaultFileId" name="defaultFileId" class="Long">
                            </li>
                            <li>
                                <label id="lblAllowSerchInSubfolders" for="allowSwitchSearchInSubfolders"></label>
                                <select size="2" id="allowSwitchSearchInSubfolders" name="allowSwitchSearchInSubfolders">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblShareDocuments"></label>
                                <select size="2" id="shareDocuments" name="shareDocuments">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblExternalLink"></label>
                                <select size="2" id="externalLink" name="externalLink">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblSearchSubFolder" ></label>
                                <select size="2" id="searchSubFolder" name="searchSubFolder">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                        </ul>
                    </li>
                    <!--Configuración Visor PDF-->
                    <li><label id="lblPdfViewerConfiguration" class="subheader clickable expandable slow_and_closed" for="pdfViewerConfiguration"></label></li>
                    <li id="pdfViewerConfiguration" style="display: none;">
                        <ul>
                            <li style="display: flex;">
                                <label id="pdfConversionStatusLabel" name="pdfConversionStatusLabel"></label>
                                <div class="info left gridInfo status-container">
                                    <label id="pdfConversionStatus">Testing...</label>
                                    <div class="material-icons" class="Button button" id="refreshPdfConversionStatus">autorenew</div>
                                    <div class="material-icons">info</div>
                                </div>
                            </li>
                            <li>
                                <label id="lblEnableFileCache"></label>
                                <select size="2" id="enableFileCache" name="enableFileCache">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li class="enableFileCacheSection">
                                <label id="lblFileCachePath"></label>
                                <input type="text" id="fileCachePath" name="fileCachePath" class="String" 
                                       placeholder="C:\Bnext\BnextQMS-Cache"
                                       title="Ej. C:\Bnext\BnextQMS-Cache">
                            </li>
                            <li class="enableFileCacheSection">
                                <label id="lblFileCacheStoreDays"></label>
                                <input type="text" id="fileCacheStoreDays" name="fileCacheStoreDays" class="Long">
                            </li>
                            <li>
                                <label id="lblProgramConvertToPDF"></label>
                                <select size="4" id="programConvertToPDF" name="programConvertToPDF" class="String">
                                    <option value="ms_office"></option>
                                    <option value="libreoffice"></option>
                                    <option class="optionGoogleDrive" value="google_drive"></option>
                                    <option value="pdf_generator"></option>
                                </select>
                            </li>
                            <li class="sectionLibreOffice">
                                <label id="lblDirectorioOffice"></label>
                                <input type="text" id="officePath" name="officePath" class="String"
                                       placeholder="C:\DPMS\LibreOfficePortable\App\libreoffice\program"
                                       title="Ej. C:\DPMS\LibreOfficePortable\App\libreoffice\program">
                            </li>  
                            <li class="sectionMsOffice">
                                <label id="lblOfficeToPDFPath"></label>
                                <input type="text" id="officeToPDFPath" name="officeToPDFPath" class="String" 
                                       placeholder="C:\OfficeToPDF"
                                       title="Ej. C:\OfficeToPDF">
                            </li>
                            <li class="sectionMsOffice">
                                <label id="lblOfficeToPdfParams"></label>
                                <input type="text" id="officeToPdfParams" name="officeToPdfParams" class="String"
                                   placeholder="/hidden /print /markup"
                                   title="Ej. /hidden /print /markup">
                            </li>  
                            <li class="sectionMsOffice">
                                <label id="lblexistsPublisher"></label>
                                <input type="text" id="pathPublisher" name="pathPublisher" class="String"
                                       placeholder="C:\Program Files\Microsoft Office\Office15"
                                       title="Ej. C:\Program Files\Microsoft Office\Office15">
                            </li>                            
                            <li class="sectionMsOffice">
                                <label id="lblexistsVisio"></label>
                                <input type="text" id="pathVisio" name="pathVisio" class="String"
                                   placeholder="C:\Program Files\Microsoft Office\Office15"
                                   title="Ej. C:\Program Files\Microsoft Office\Office15">
                            </li>                            
                            <li class="sectionMsOffice">
                                <label id="lblexistsProject"></label>
                                <input type="text" id="pathProject" name="pathProject" class="String"
                                       placeholder="C:\Program Files\Microsoft Office\Office15"
                                       title="Ej. C:\Program Files\Microsoft Office\Office15">
                            </li>       
                            <li>
                                <label id="lblCloseOfficePrograms"></label>
                                <select size="2" id="closeOfficePrograms" name="closeOfficePrograms">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li class="sectionGoogleDrive">
                                <div id="googleDriveKey"></div>
                                <input type="hidden" id="googleDriveIntegration" name="googleDriveIntegration">
                                <input type="hidden" id="googleDriveKeyId" name="googleDriveKeyId" class="Long">
                            </li>       
                            <li class="sectionPdfGenerator">
                                <label id="lblPdfGeneratorUrl"></label>
                                <input type="text" id="pdfGeneratorUrl" class="String" name="pdfGeneratorUrl">
                            </li> 
                            <li class="sectionPdfGenerator">
                                <label id="lblPdfGeneratorAccount"></label>
                                <input type="text" id="pdfGeneratorAccount" class="String" name="pdfGeneratorAccount">
                            </li>
                            <li class="sectionPdfGenerator">
                                <label id="lblPdfGeneratorPassword"></label>
                                <input type="text" id="pdfGeneratorPassword" class="String" name="pdfGeneratorPassword">
                            </li>           
                            <li class="sectionMsOffice">
                                <label id="lblofficeFileAliveTime"></label>
                                <input type="text" id="officeFileAliveTime" name="officeFileAliveTime">
                            </li>   
                            <li>
                                <label id="lblPdfGeneratorTimeout"></label>
                                <input type="text" id="pdfGeneratorTimeout" name="pdfGeneratorTimeout" class="Long">
                            </li>
                            <li>
                                <label id="lblOfficeToPDFAliveTime"></label>
                                <input type="text" id="officeToPDFAliveTime" name="officeToPDFAliveTime" class="Long">
                            </li>
                            <li>
                                <label id="lblOfficeToPDFMaxWaitingTasks"></label>
                                <input type="text" id="officeToPDFMaxWaitingTasks" name="officeToPDFMaxWaitingTasks">
                            </li>
                            <li>
                                <label id="lblAutodeskTvPath"></label>
                                <input type="text" id="autodeskTvPath" name="autodeskTvPath" class="String"
                                       placeholder="C:\Program Files\Autodesk\DWG TrueView 2016 - English"
                                       title="Ej. C:\Program Files\Autodesk\DWG TrueView 2016 - English">
                            </li>       
                            <li class="sectionAudodeskTrueView">
                                <label id="lblAutodeskTvPlotScriptPath"></label>
                                <input type="text" id="autodeskTvPlotScriptPath" name="autodeskTvPlotScriptPath" class="String"
                                       placeholder="c:\dwgToPDF"
                                       title="Ej. c:\dwgToPDF">
                            </li>       
                            <li class="sectionAudodeskTrueView">
                                <label id="lblAutodeskTvPlotFolderPath"></label>
                                <input type="text" id="autodeskTvPlotFolderPath" name="autodeskTvPlotFolderPath" class="String"
                                       placeholder="c:\dwgToPDF"
                                       title="Ej. c:\dwgToPDF">
                            </li>       
                            <li class="sectionAudodeskTrueView">
                                <label id="lblAutodeskTvFileParams"></label>
                                <input type="text" id="autodeskTvFileParams" name="autodeskTvFileParams" class="String"
                                       placeholder="/b"
                                       title="Ej. /b">
                            </li>       
                            <li class="sectionAudodeskTrueView">
                                <label id="lblAutodeskTvPlotParams"></label>
                                <input type="text" id="autodeskTvPlotParams" name="autodeskTvPlotParams" class="String"
                                       placeholder="/nologo"
                                       title="Ej. /nologo">
                            </li>        
                            <li class="sectionAudodeskTrueView">
                                <label id="lblDwgImageDpi"></label>
                                <input type="text" id="dwgImageDpi" name="dwgImageDpi">
                            </li>
                            <li class="sectionPdfImage">
                                <label id="lblPdfImagePoolSize"></label>
                                <input type="text" id="pdfImagePoolSize" name="pdfImagePoolSize">
                            </li>
                            <li class="sectionPdfImage">
                                <label id="lblPdfImageAliveTime"></label>
                                <input type="text" id="pdfImageAliveTime" name="pdfImageAliveTime" class="Long">
                            </li>
                            <li class="sectionPdfImage">
                                <label id="lblPdfImageMaxWaitingTasks"></label>
                                <input type="text" id="pdfImageMaxWaitingTasks" name="pdfImageMaxWaitingTasks">
                            </li>
                            <li class="sectionPdfImage">
                                <label id="lblPdfImageDpi"></label>
                                <input type="text" id="pdfImageDpi" name="pdfImageDpi">
                            </li>
                            <li>
                                <label id="lblPdfImageFormat"></label>
                                <select size="4" id="pdfImageFormat" name="pdfImageFormat" class="String">
                                    <option value="webp">webp</option>
                                    <option value="png">png</option>
                                </select>
                            </li>
                            <li class="sectionPdfImage">
                                <label id="lblPdfMaximumPersistedPages"></label>
                                <input type="text" id="pdfMaximumPersistedPages" name="pdfMaximumPersistedPages">
                            </li>                
                            <li>
                                <label id="lblEnabledDailyTaskDocuments"></label>
                                <select size="2" id="enabledDailyTaskDocuments" name="enabledDailyTaskDocuments">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>     
                            <li>
                                <label id="lblDailyTaskDocuments"></label>
                                <input type="text" id="dailyTaskDocuments" name="dailyTaskDocuments">
                            </li>                         
                            <li>
                                <label id="lblEnabledCleanupPdfRecords"></label>
                                <select size="2" id="enabledCleanupPdfRecords" name="enabledCleanupPdfRecords">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li>
                                <label id="lblMaxCleanupPdfRecords"></label>
                                <input type="text" id="maxCleanupPdfRecords" name="maxCleanupPdfRecords">
                            </li>
                            <li>
                                <label id="lblDownloadAttemptsLimit"></label>
                                <input type="text" id="downloadAttemptsLimit" name="downloadAttemptsLimit">
                            </li>
                            <div>
                                <label id="lblPdfCleanerAliveTime"></label>
                                <input type="text" id="pdfCleanerAliveTime" name="pdfCleanerAliveTime" class="Long">
                            </div>
                            <div>
                                <label id="lblPdfCleanerMaxWaitingTasks"></label>
                                <input type="text" id="pdfCleanerMaxWaitingTasks" name="pdfCleanerMaxWaitingTasks">
                            </div>
                            <li>
                                <label></label>
                                <input type="button" id="testPdfViewerConfiguration" name="testPdfViewerConfiguration" class="Button button noData">
                            </li>
                        </ul>
                    </li>
                    <li><label id="lblMailDifusion" class="subheader clickable expandable slow_and_closed" for="maildifusion"></label></li>
                    <li id="maildifusion" style="display: none;">
                        <ul>
                            <li><label id="lblMailToAuthor"></label>
                                <select size="2" id="mailToAuthor" name="mailToAuthor">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToDocumentManager"></label>
                                <select size="2" id="mailToDocumentManager" name="mailToDocumentManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToBusinessUnitManager"></label>
                                <select size="2" id="mailToBusinessUnitManager" name="mailToBusinessUnitManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToDepartmentManager"></label>
                                <select size="2" id="mailToDepartmentManager" name="mailToDepartmentManager">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToAssignedReaders"></label>
                                <select size="2" id="mailToAssignedReaders" name="mailToAssignedReaders">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToAuthorizers"></label>
                                <select size="2" id="mailToAuthorizers" name="mailToAuthorizers">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToBusinessUnitPermission"></label>
                                <select size="2" id="mailToBusinessUnitPermission" name="mailToBusinessUnitPermission">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToProcessPermission" for="mailToProcessPermission"></label>
                                <select size="2" id="mailToProcessPermission" name="mailToProcessPermission">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToDepartmentPermission" for="mailToDepartmentPermission"></label>
                                <select size="2" id="mailToDepartmentPermission" name="mailToDepartmentPermission">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                            <li><label id="lblMailToUserPermission" for="mailToUserPermission"></label>
                                <select size="2" id="mailToUserPermission" name="mailToUserPermission">
                                    <option value="1"></option>
                                    <option value="0"></option>
                                </select>
                            </li>
                        </ul>
                    </li>
                <!-- ActionButtons -->   
                <jsp:include page="../../../components/saveButtons.jsp"/>   
            </ul>
            </div>
            <%@include file="../../../components/sessionVariables.jsp" %>
            <%@include file="../../../components/footer.jsp" %>
        </form>
    </body>
</html>