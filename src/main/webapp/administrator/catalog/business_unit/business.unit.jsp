<%@page import="Framework.Config.Utilities"%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="${cssHtml} html-reset">
    <head>
        <title>Plantas</title>
        <script charset="utf-8" type="text/javascript" src="../scripts/framework/bnext/ckeditor/ckeditor.js?${systemVersion}" async="true"></script>
        <%@include file="../../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <script type="text/javascript" src="c.business.unit.controller?${systemVersion}"></script>
        <style>
            #copyOptionUncontrolled, #copyOption{ 
                display: none;
            }
            .leftPane{
                width: 60%;
                float:left;
            }
            .Button{padding:5px; border:solid 1px #DDD; margin:2px;background: #EEE; 
                    text-align: center; cursor:pointer; -moz-border-radius: 3px; -webkit-border-radius:3px; border-radius: 3px;
                    text-decoration: none!important; color: black!important;
            }
            .expandable {
                width: auto;
                text-align: left;
                padding-left: 3rem;
                font-weight: 400;
                font-size: 1.5rem;
                letter-spacing: 0;
                text-transform: none;
                line-height: 1.75rem;
                margin-top: 1.75rem;
                margin-bottom: 0;
                margin: 0;
                background-position-y: center;
                background-position-x: left;
                background-repeat: no-repeat;
                background-image: url(../images/common/arrow_sans_right.png);
                float: none;
            }
            .expandable.open {
                background-position-y: center;
                background-position-x: left;
            }
            .expandable-container {
                padding: 1rem;
                background-color: rgba(0, 0, 0, 0.04);
            }
            .expandable-container-target {
                margin-right: 0;
                margin-left: 0;
                padding-left: 1rem;
                padding-right: 1rem;
                padding-bottom: 1rem;
            }
            .open {
                background: url(../images/common/arrow_sans_down.png) no-repeat;
            }
            .required:after {
                content:" *";
            }
        </style>
        
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/PageBreak.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/ShowBlockNodes.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/Preview.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/Save.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/Breadcrumb.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/FindReplace.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/PasteFromWord.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/InsertAnchor.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/CollapsibleToolbar.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/Blockquote.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/Smiley.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/dojox/editor/plugins/resources/css/LocalImage.css?${systemVersion}" />  
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons" method="POST" id="validate_form">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle"></h3>
                            </div>
                        </div>   
                        <div class="grid-container">
                            <div class="grid-x grid-padding-x" id="mainDiv">   
                                <div class="cell small-12">
                                    <div id="ulCode" class="textarea-component generate-code-cection">
                                        <input type="text" required="required" name="code" id="txtCode" value=""/>
                                        <label id="lblCode" class="required"></label>
                                        <div id="generateCodeSection">
                                            <%@include file="/../components/chk-generate.jsp" %>
                                        </div>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" name="description" id="description" required="required"/>
                                        <label id="lblDescription" class="required"></label>
                                    </div>
                                </div>       
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" name="physicalLocation" id="physicalLocation" maxlength="255" value=""/>
                                        <label class="physicalLocation"></label>
                                    </div>
                                </div>      
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <s:select required="required" name="predecessor" id="predecessor" 
                                                  list="predecessor"
                                                  listKey="value" listValue="text" >
                                        </s:select>
                                        <label for="predecessor" class="required"></label>
                                    </div>
                                </div>        
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <s:select required="required" name="attendant" id="attendant" 
                                                  list="attendant"
                                                  listKey="value" listValue="text" >
                                        </s:select>
                                        <label for="attendant" class="required"></label>
                                    </div>
                                </div>        
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <s:select required="required" name="documentManagerId" id="documentManagerId" 
                                                  list="documentManager"
                                                  listKey="value" listValue="text" >
                                        </s:select>
                                        <label for="documentManagerId" class="required"></label>
                                    </div>
                                </div>        
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" name="daysToEscalate" required="required" id="daysToEscalate" value=""/>
                                        <label class="lblDaysToEscalate required"></label>
                                    </div>
                                </div>
                                <% if (Utilities.getSettings().getNewsBySociety() == 1) {%>      
                                <div class="cell small-12 medium-6 large-4 expandable-container-target">
                                    <div class="select-component">
                                        <select id="news" name="news">
                                            <option value="1"></option>
                                            <option value="0"></option>
                                        </select>
                                        <label for="news"></label>
                                    </div>
                                </div>
                                <% } else {%>
                                    <input type="hidden" id="news" name="news" value="0">
                                <% }%>
                                <div class="cell small-12">
                                    <div class="table-component">
                                        <div id="filePicker"></div>
                                    </div>
                                </div>  

                                <!--Sección de copia controlada-->
                                <div class="cell small-12">&nbsp;</div>       
                                <div class="cell small-12">
                                    <div class="elevation-2">
                                        <div class="expandable-container">
                                            <label id="lblCopy" class="subheader clickable expandable" for="copyOption"></label>
                                        </div>
                                        <div id="copyOption" class="grid-x grid-padding-x expandable-container-target">
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <input type="text" name="headerTitle" id="headerTitle" value=""/>
                                                    <label class="lblGeneralHeader"></label>
                                                </div>
                                            </div>  
                                            <div class="cell small-12 placeholder">
                                                <div class="textarea-component suffix-percent-icon">
                                                    <input type="text" name="headerSize" id="headerSize" value="" maxlength="255"/>
                                                    <label class="lblHeaderSize"></label>
                                                </div>
                                            </div>  
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <textarea id="subheaderTitle" class="wysiwyg"></textarea>
                                                    <label class="lblHeaderTitle"></label>                                                
                                                </div> 
                                            </div> 
                                            <div class="cell small-12 placeholder">
                                                <div class="textarea-component suffix-percent-icon">
                                                    <input type="text" name="watermarkOpacity" id="watermarkOpacity" value="30"/>
                                                    <label class="lblWatermarkOpacity"></label>
                                                </div>
                                            </div>  
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <textarea id="watermark" class="wysiwyg"></textarea>
                                                    <label class="lblWatermark"></label>                                                
                                                </div> 
                                            </div> 
                                            <div class="cell small-12 placeholder">
                                                <div class="textarea-component suffix-percent-icon">
                                                    <input type="text" name="footerSize" id="footerSize" value="" maxlength="255" placeholder="Ej. 10% (Incluyendo el simbolo %)"/>
                                                    <label class="lblFooterSize"></label>
                                                </div>
                                            </div>  
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <textarea id="subfooterTitle" class="wysiwyg"></textarea>
                                                    <label class="lblFooterTitle"></label>                                                
                                                </div> 
                                            </div> 
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <input type="text" name="footerTitle" id="footerTitle" value="" maxlength="255" />
                                                    <label class="lblGeneralFooter"></label>
                                                </div>
                                            </div>  
                                        </div>
                                    </div>
                                </div>       
                                <!--Sección de copia no controlada-->
                                <div class="cell small-12">
                                    <div class="elevation-2">
                                        <div class="expandable-container">
                                            <label id="lblCopyUncontrolled" class="subheader clickable expandable" for="copyOptionUncontrolled"></label>
                                        </div>
                                        <div id="copyOptionUncontrolled" class="grid-x grid-padding-x expandable-container-target">
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <input type="text" name="headerTitleUncontrolled" id="headerTitleUncontrolled" value=""/>
                                                    <label class="lblGeneralHeader"></label>
                                                </div>
                                            </div>  
                                            <div class="cell small-12 placeholder">
                                                <div class="textarea-component suffix-percent-icon">
                                                    <input type="text" name="headerSize" id="headerSizeUncontrolled" value="" maxlength="255" placeholder="Ej. 10% (Incluyendo el simbolo %)"/>
                                                    <label class="lblHeaderSize"></label>
                                                </div>
                                            </div>  
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <textarea id="subHeaderTitleUncontrolled" class="wysiwyg"></textarea>
                                                    <label class="lblHeaderTitle"></label>                                                
                                                </div> 
                                            </div> 
                                            <div class="cell small-12 placeholder">
                                                <div class="textarea-component suffix-percent-icon">
                                                    <input type="text" name="watermarkOpacityUncontrolled" id="watermarkOpacityUncontrolled" value="30"/>
                                                    <label class="lblWatermarkOpacity"></label>
                                                </div>
                                            </div>  
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <textarea id="watermarkUncontrolled" class="wysiwyg"></textarea>
                                                    <label class="lblWatermark"></label>                                                
                                                </div> 
                                            </div> 
                                            <div class="cell small-12 placeholder">
                                                <div class="textarea-component suffix-percent-icon">
                                                    <input type="text" id="footerSizeUncontrolled" value="" maxlength="255" placeholder="Ej. 10% (Incluyendo el simbolo %)"/>
                                                    <label class="lblFooterSize"></label>
                                                </div>
                                            </div> 
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <textarea id="subFooterTitleUncontrolled" class="wysiwyg"></textarea>
                                                    <label class="lblFooterTitle"></label>                                                
                                                </div> 
                                            </div> 
                                            <div class="cell small-12">
                                                <div class="textarea-component">
                                                    <input type="text" name="footerTitleUncontrolled" id="footerTitleUncontrolled" value=""/>
                                                    <label class="lblGeneralFooter"></label>
                                                </div>
                                            </div> 
                                        </div>
                                    </div>
                                </div>       
                                <div class="cell small-12">&nbsp;</div>
                                <div class="cell small-12 actionButtons">
                                    <%@include file="../../../components/saveButtons.jsp" %>
                                </div>
                                <s:hidden name="id" id="id"/>
                                <s:hidden name="status" id="status"/>
                                <input type="hidden" id="onBtnAdd" value="../view/v.business.unit.view" />
                                <input type="hidden" id="onBtnControl" value="../view/v.business.unit.list.view" />
                                <s:hidden name="systemColor" id="SYSTEM_COLOR" />
                                <s:hidden name="userId" id="SESSION_USER" /><%-- Mailisima practica!, este valor se puede obtener directo en el *.java con getLoggedUserId() --%>
                                <s:hidden name="configurationRole" id="SESSION_MODULE_ROLE" />
                                <%@include file="../../../components/footer.jsp" %>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
</html>