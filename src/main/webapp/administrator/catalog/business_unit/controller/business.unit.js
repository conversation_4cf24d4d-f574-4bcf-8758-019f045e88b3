require(['bnext/module/FilePicker', "bnext/saveHandle","dojo/Deferred", "dojo/promise/all", "entity/businessUnit","dojo/dom","dojo/dom-attr","dojo/on","dojo/query",
         "bnext/callMethod", "dojo/string",'core', 'bnext/ckeditor',
         'bnext/saveHandleUtil',
         'dojo/_base/lang',
        'dojo/text!bnext/administrator/configuracion/templates/duplicated-business-unit.html',
        'dojo/text!bnext/administrator/configuracion/styles/duplicated-business-unit.css',
        'bnext/i18n!bnext/administrator/configuracion/nls/duplicated-business-unit',
         "dojo/domReady!",
         "dojo/dom-construct"],
  function(FilePicker, saveHandle, Deferred, all, businessUnit, dom, domAttr, on, query,
    callMethod, string, core, ckeditor,
    saveHandleUtil,
    lang,
    DuplicatedBusinessUnitTemplate,
    DuplicatedBusinessUnitCss,
    DuplicatedBusinessUnitI18n  
) {
    var DUPLICATE_KEY_PHYSICAL_LOCATION = "DUPLICATE_KEY_PHYSICAL_LOCATION";
    var DUPLICATE_KEY_CODE = "DUPLICATE_KEY_CODE";
    var data = businessUnit;
    var sh = null;
    function saveCallBack(result) {
        if (result 
                && result.operationEstatus === 0 
                && (result.errorMessage === DUPLICATE_KEY_PHYSICAL_LOCATION) || result.errorMessage === DUPLICATE_KEY_CODE) {
            if (result.errorMessage === DUPLICATE_KEY_PHYSICAL_LOCATION) {
                showDuplicatePhysicalLocationError(result.errorMessage);
            } else {
                showDuplicateCodeError(result.errorMessage);
            }
        } else {
            saveHandleUtil.call(sh, result);
        }
    }
    function getDuplicateMessage(info, errorMessage) {
        info.header = DuplicatedBusinessUnitI18n[errorMessage];
        info.status = DuplicatedBusinessUnitI18n['statuses'][info.status + ''];
        var dialogMessage = lang.replace(DuplicatedBusinessUnitTemplate, {
            i18n: DuplicatedBusinessUnitI18n,
            css: DuplicatedBusinessUnitCss,
            info: info
        });
        return dialogMessage;
    }
    function showDuplicatePhysicalLocationError(errorMessage) {
        var physicalLocation = data.physicalLocation;
        core.showLoader(DuplicatedBusinessUnitI18n.loadingDuplicateDetails).then(function(){
            callMethod({
                url: '../DPMS/Settings.BusinessUnit.action',
                method: 'getBusinessUnitInfoByPhysicalLocation',
                params: [physicalLocation]
            }).then(function (user) {
                var dialogTitle = DuplicatedBusinessUnitI18n.duplicateKeyPhysicalLocationTitle;
                core.hideLoader();
                var dialogMessage = getDuplicateMessage(user, errorMessage);
                core.dialog(dialogMessage, null, null, dialogTitle);
            }, function() {
                core.hideLoader();
            });  
        });
    }
    function showDuplicateCodeError(errorMessage) {
        var code = data.code;
        core.showLoader(DuplicatedBusinessUnitI18n.loadingDuplicateDetails).then(function(){
            callMethod({
                url: '../DPMS/Settings.BusinessUnit.action',
                method: 'getBusinessUnitInfoByCode',
                params: [code]
            }).then(function (user) {
                var dialogTitle = DuplicatedBusinessUnitI18n.duplicateKeyCodeTitle;
                core.hideLoader();
                var dialogMessage = getDuplicateMessage(user, errorMessage);
                core.dialog(dialogMessage, null, null, dialogTitle);
            }, function() {
                core.hideLoader();
            });  
        });
    }
    
    var uploader,
    settings={
      id:-1,
      businessUnitId:0,
      daysToEscalate:0,
      //copias controladas
      headerTitle:"",
      subHeaderTitle:"",
      watermark:"",
      watermarkOpacity:0,
      subFooterTitle:"",
      footerTitle:"",
      //copias no controladas
      headerTitleUncontrolled:"",
      subHeaderTitleUncontrolled:"",
      watermarkOpacityUncontrolled:0,
      watermarkUncontrolled:"",
      subFooterTitleUncontrolled:"",
      footerTitleUncontrolled:""
    },
    type = {_new:-1},
    toggleGenerateKey = function() {
      toggleGenerate('#chkGenerate', '#txtCode');
    };
    function getOptionsData(select) {
      var data = [];
      query('option', select).forEach(function(optionDom) {
          data.push({
              id: optionDom.value,
              description: optionDom.innerHTML
          });
      });
      return data;
    }
    var loadbUnit = function(obj) {
        var loadedVal = 0;            
        if (!isNull(obj.fileId)) {
            data.fileId = obj.fileId;
            uploader.set('value', data.fileId);
        }
        query('input', 'validate_form').forEach(function (input) {
            loadedVal = obj[input.name];
            if (input.name && (loadedVal || loadedVal == '0')) {
                    input.value = loadedVal;
                }
        });
        dom.byId('attendant').value = obj["attendantId"];
        dom.byId('documentManagerId').value = obj["documentManagerId"];
        dom.byId('predecessor').value = obj["organizationalUnitId"];
        if (+dom.byId('attendant').value !== +obj["attendantId"]) {
          var optionsData = getOptionsData(dom.byId('attendant'));
          optionsData.push(obj['attendant']);
          core.fillCombo('#attendant', optionsData, 'description', 'id', false);
          dom.byId('attendant').value = obj["attendantId"];
        }
        if (+dom.byId('documentManagerId').value !== +obj["documentManagerId"]) {
          var optionsData = getOptionsData(dom.byId('documentManagerId'));
          optionsData.push(obj['documentManager']);
          core.fillCombo('#documentManagerId', optionsData, 'description', 'id', false);
          dom.byId('documentManagerId').value = obj["documentManagerId"];
        }
        if (+dom.byId('predecessor').value !== +obj["organizationalUnitId"]) {
          var optionsData = getOptionsData(dom.byId('predecessor'));
          optionsData.push(obj['organizationalUnit']);
          core.fillCombo('#predecessor', optionsData, 'description', 'id', false);
          dom.byId('predecessor').value = obj["organizationalUnitId"];
        }
        toggleDomNoValid();
        attachNoValidEvent();
        core.hideLoader();
    },
    save = function() {
      if ($("#validate_form").valid()) {
        core.confirmDialog(dom.byId('id').value).then(function() {
          core.showLoader(core.i18n.Validate.saving);
          data['id'] = +dom.byId('id').value;
          query('input', 'validate_form').forEach(function(input) {
            if (input.name) {
                    data[input.name] = input.value;
                }
          });
          data['attendantId'] = +dom.byId('attendant').value;
          data['organizationalUnitId'] = +dom.byId('predecessor').value || null;
          data['documentManagerId'] = +dom.byId('documentManagerId').value || null;
          var generateCode = domAttr.get(dom.byId('chkGenerate'), 'checked');
          if (generateCode) {
            data["code"] = '';
          }
          sh.setData(data);
          sh.saveData();
        }, core.hideLoader);
      }
    },
    loadbUnitSettings = function (obj) {
        settings.id = obj.id || -1;
        // daysToEscalate puede ser 0 y la operacion devolveria el otro string, por eso se pone 0 como el default
        dom.byId("daysToEscalate").value = obj.daysToEscalate || '0';
        //copias controladas
        dom.byId("headerTitle").value = obj.headerTitle || '';
        ckeditor.instances.subheaderTitle.setData(obj.subHeaderTitle || '');
        ckeditor.instances.watermark.setData(obj.watermark || '');
        ckeditor.instances.subfooterTitle.setData(obj.subFooterTitle || '');
        dom.byId("watermarkOpacity").value = obj.watermarkOpacity || 30;
        dom.byId("footerTitle").value = obj.footerTitle || '';
        dom.byId("headerSize").value = obj.headerSize || '';
        dom.byId("footerSize").value = obj.footerSize || '';
        //copias no controladas
        dom.byId("headerTitleUncontrolled").value = obj.headerTitleUncontrolled || '';
        ckeditor.instances.subHeaderTitleUncontrolled.setData(obj.subHeaderTitleUncontrolled || '');
        ckeditor.instances.watermarkUncontrolled.setData(obj.watermarkUncontrolled || '');
        ckeditor.instances.subFooterTitleUncontrolled.setData(obj.subHeaderTitleUncontrolled || '');
        dom.byId("watermarkOpacityUncontrolled").value = obj.watermarkOpacityUncontrolled || 30;
        dom.byId("footerTitleUncontrolled").value = obj.footerTitleUncontrolled || '';
        dom.byId("headerSizeUncontrolled").value = obj.headerSizeUncontrolled || '';
        dom.byId("footerSizeUncontrolled").value = obj.footerSizeUncontrolled || '';
        if (dom.byId("news")) {
            dom.byId("news").value = obj.news || 0;
        }
        toggleDomNoValidSettings();
        attachNoValidEventSettings();
    };
    var getSettings = function(id) {
      settings.businessUnitId = id;
      settings.daysToEscalate = dom.byId("daysToEscalate").value;
      //copias controladas
      settings.headerTitle = dom.byId("headerTitle").value;
      settings.subHeaderTitle = string.trim(ckeditor.instances.subheaderTitle.getData());
      settings.watermarkOpacity = dom.byId("watermarkOpacity").value;
      settings.watermark = string.trim(ckeditor.instances.watermark.getData());
      settings.subFooterTitle = string.trim(ckeditor.instances.subfooterTitle.getData());
      settings.footerTitle = dom.byId("footerTitle").value;
      settings.headerSize = dom.byId("headerSize").value;
      settings.footerSize = dom.byId("footerSize").value;
      //copias no controladas
      settings.headerTitleUncontrolled = dom.byId("headerTitleUncontrolled").value;
      settings.subHeaderTitleUncontrolled = string.trim(ckeditor.instances.subHeaderTitleUncontrolled.getData());
      settings.watermarkOpacityUncontrolled = dom.byId("watermarkOpacityUncontrolled").value;
      settings.watermarkUncontrolled = string.trim(ckeditor.instances.watermarkUncontrolled.getData());
      settings.subFooterTitleUncontrolled = string.trim(ckeditor.instances.subFooterTitleUncontrolled.getData());
      settings.footerTitleUncontrolled = dom.byId("footerTitleUncontrolled").value;
      settings.headerSizeUncontrolled = dom.byId("headerSizeUncontrolled").value;
      settings.footerSizeUncontrolled = dom.byId("footerSizeUncontrolled").value;
      settings.news = dom.byId("news").value;
      return settings;
    },
    setEvents = function() {
        var 
            copyOption = $('#copyOption'), lblCopy = $('#lblCopy'),
            copyOptionUncontrolled = $('#copyOptionUncontrolled'), lblCopyUncontrolled = $('#lblCopyUncontrolled')
        ;
        on(dom.byId('saveBtn'), 'click', save);
        on(dom.byId('cancelBtn'), 'click', core.cancel);
        domAttr.set(dom.byId('chkGenerate'), 'click', toggleGenerateKey);
        on(lblCopyUncontrolled, 'click', function (e) {
            lblCopyUncontrolled.toggleClass('open');
            copyOptionUncontrolled.slideToggle('slow');
        });
        on(lblCopy, 'click', function (e) {
            lblCopy.toggleClass('open');
            copyOption.slideToggle('slow');
        });
    },
    doEverything = function(i18n) {
        dom.byId('headerSize').placeholder = i18n.Validate.placeHolder;
        dom.byId('footerSize').placeholder = i18n.Validate.placeHolder;
        dom.byId('headerSizeUncontrolled').placeholder = i18n.Validate.placeHolder;
        dom.byId('footerSizeUncontrolled').placeholder = i18n.Validate.placeHolder;
        dom.byId('predecessor').options[0].innerHTML = i18n.Default.selectTag;
        dom.byId('attendant').options[0].innerHTML = i18n.Default.selectTag;
        dom.byId('documentManagerId').options[0].innerHTML = i18n.Default.selectTag;
        var v = core.i18n.Validate,
        missingCombo = v.missingCombo,
                missingField = v.missingField,
        invalid_required_text = v.invalid_required_text;
        function successful() {
            if (dom.byId('id').value !== type._new) {
                var s = getSettings(dom.byId('id').value);
                callMethod({
                    url: "Settings.BusinessUnitSettings.action",
                    method: "save",
                    params: [s]
                });
            }
          core.hideLoader();
        }
        sh = new saveHandle({
            savedObjName: "sh",
            serviceStore: "Settings.BusinessUnit.action",
            hookUpdateSuccess: successful,
            hookUpdateFailure: "void(0)",
            saveCallback: saveCallBack,
            methodName: "save"
        });
        setEvents();
        $.validator.addMethod(
            "regexAlfanumeric",
            function (value, element, regexp) {
                var re = new RegExp(regexp);
                return this.optional(element) || re.test(value);
            },
            specifiedMessage(invalid_required_text, 'specify', 100)
        );
        $.validator.addMethod(
            'regexNumber',
            function (value, element, regexp) {
                var re = new RegExp(regexp);
                return this.optional(element) || re.test(value);
            },
            core.specifiedMessage(core.i18n.Validate.invalid_required_number, 'specify', 3)
        );
        $('#validate_form').validate({
            ignore: '.ignore',
            rules: {
                code: {
                    required: true,
                    maxlength: 50
                },
                description: {
                    required: true,
                    maxlength: 255
                },
                daysToEscalate:{
                    required: true,
                    digits: true
                },
                predecessor: 'required',
                attendant: 'required',
                documentManagerId: 'required',
                watermarkOpacity: {
                    required: true,
                    digits: true,
                    maxlength: 3
                },
                watermarkOpacityUncontrolled: {
                    required: true,
                    digits: true,
                    maxlength: 3
                },
                headerSize: {
                    maxlength: 50
                },
                footerSize: {
                    maxlength: 50
                },
                headerSizeUncontrolled: {
                    maxlength: 50
                },
                footerSizeUncontrolled: {
                    maxlength: 50
                }
            },
            messages: {
                code: specifiedMessage(invalid_required_text, 'specify', 100),
                description: specifiedMessage(invalid_required_text, 'specify', 255),
                predecessor: missingCombo,
                attendant: missingCombo,
                documentManagerId: missingCombo,
                daysToEscalate: {
                    required:missingField,
                    digits:core.i18n.Validate.numericOnly
                },
                watermarkOpacity: {
                    required:missingField,
                    digits:core.i18n.Validate.numericOnly
                },
                watermarkOpacityUncontrolled: {
                    required:missingField,
                    digits:core.i18n.Validate.numericOnly
                },
                footerTitle: missingField,
                headerTitleUncontrolled: missingField,
                footerTitleUncontrolled: missingField
            },
            errorPlacement: function (error, element) {
                error.insertBefore(element);
            }
        });
        uploader = new FilePicker({
            useDownloadIcon: true,
            modern: true,
            uploadPath: '../DPMS/UploadImage.fileUploader',
            accept: 'image/*',
            downloadParameters: 'fileId=[id]&documentId=-1',
            i18n: {
                filePickerDownloadButtonTitle: i18n.filePickerDownloadTitle,
                filePickerFileLabel: i18n.lblLogo,
                filePickerDownloadImageTitle: i18n.filePickerDownloadTitle,
                startingConversion: i18n.startingConversion
            },
            downloadAction: '../view/v-download.view',
            onFileSelected: function (req) {
                if (req) {
                data.fileId = req.id;
                } else {
                    data.fileId = null;
                }
            },
            onFileUploaded: function (file) {
                switch (file.status) {
                    case 'invalid-content-type':
                    case 'invalid-extension':
                        delete data.fileId;
                        core.dialog(i18n.invalidImage);
                        return;
                }
            },
            onDelete: function () {
                data.fileId = null;
            }
        }, 'filePicker');          
        $("#validate_form #headerSize").rules(
                "add", { regexNumber: "^[0-9]+\\.?([0-9]+)?%$" });
        $("#validate_form #footerSize").rules(
                "add", { regexNumber: "^[0-9]+\\.?([0-9]+)?%$" });
        $("#validate_form #headerSizeUncontrolled").rules(
                "add", { regexNumber: "^[0-9]+\\.?([0-9]+)?%$" });
        $("#validate_form #footerSizeUncontrolled").rules(
                "add", { regexNumber: "^[0-9]+\\.?([0-9]+)?%$" });
        $("#validate_form #txtCode").rules({
            required: true,
            maxlength: 100
        });
        $("#validate_form #description").rules({
            required: true,
            maxlength: 255
        });

        var defCKEditors = []; 
        query('textarea.wysiwyg').forEach(function (textarea) {
            var def = new Deferred();
            defCKEditors.push(def);
            ckeditor.replace(textarea, {
                on: {
                    instanceReady: function () {
                        def.resolve();
                    }
                }
            });
        });
        
        all(defCKEditors).then(function(){
            var id = dom.byId('id').value;
            if (id && id != -1) {
                /*Se cargan los datos de la unidad de negocio*/
                var args = {
                    url: "../DPMS/Settings.BusinessUnit.action",
                    method: "load",
                    params: [id]
                };
                callMethod(args).then(loadbUnit);
                /*Se cargan las configuraciones para las copias controladas en base a la unidad de negocio*/
                args.url = "../DPMS/Settings.BusinessUnitSettings.action";
                args.method = "loadSettings";
                callMethod(args).then(loadbUnitSettings, hideLoader());

                // Se debe de modificar el titulo del dialog de "Alta" -> "Editar "


            } else {
                dom.byId('status').value = 1;
                toggleDomNoValid();
                toggleDomNoValidSettings();
                attachNoValidEvent();
                attachNoValidEventSettings();
                hideLoader();
            }
        });
        
        
    };
    function toggleDomNoValid(){
        core.toggleNoValid(dom.byId('physicalLocation'));
    }
    function toggleDomNoValidSettings(){
        core.toggleNoValid(dom.byId('headerTitle'));
        core.toggleNoValid(dom.byId('footerTitle'));
        core.toggleNoValid(dom.byId('headerTitleUncontrolled'));
        core.toggleNoValid(dom.byId('footerTitleUncontrolled'));
    }
    function attachNoValidEvent(){
        core.attachNoValidEvent(dom.byId('physicalLocation'));
    }
    function attachNoValidEventSettings(){
        core.attachNoValidEvent(dom.byId('headerTitle'));
        core.attachNoValidEvent(dom.byId('headerTitleUncontrolled'));
        core.attachNoValidEvent(dom.byId('footerTitleUncontrolled'));
    }
    on(dom.byId('saveBtn'), 'click', save);
    on(dom.byId('cancelBtn'), 'click', core.cancel);
    domAttr.set(dom.byId('chkGenerate'), 'click', toggleGenerateKey);
    core.setLang('lang/catalog/business_unit/nls/business.unit').then(doEverything);
  });