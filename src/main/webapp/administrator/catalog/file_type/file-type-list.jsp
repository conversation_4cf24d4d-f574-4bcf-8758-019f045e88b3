<%-- 
    Document   : file-type-list
    Created on : 07/07/2016, 12:59:38 PM
    Author     : <PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head class="html-reset">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Lista de Users Agents</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <jsp:include page="../../../components/requiredScripts.jsp" />
        <script src="../scripts/framework/bnext/administrator/catalog/file-type/file-type-list.js?${systemVersion}" type="text/javascript"></script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="mainTitle">
                                    </h3>
                                    <div class="float-right">
                                     
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                               <div id="btnAdd" class="fab-button">
                                    <i class="material-icons">add</i>
                                    <span id="catalogoLabel"></span>
                                </div>
                        </div>
                        <div class ="grid-container table_workarea">
                            <table id="dataGrid"></table>
                        </div>
                        <div class ="displayNone">
                            <s:hidden name="serializedStatusList" id="serializedStatusList" />
                            <%@include file="../../../components/sessionVariables.jsp" %>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%@include file="../../../components/footer.jsp" %>
    </body>
</html>
