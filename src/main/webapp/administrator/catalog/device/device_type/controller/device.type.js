require(['bnext/callMethod', 'core', 'dojo/string', 'entity/deviceType', 'bnext/saveHandle', 'dojo/domReady!'], 
function (callMethod, core, string, deviceType, saveHandle) {
    var data = {};
    fillFooter(); 
    var doEverything = function(i18n) {
        defineValidationRules(i18n);
        dojo.byId('deviceGroupId').options[0].innerHTML=i18n.Default.selectTag;
        misDatos = new saveHandle({
                savedObjName:"misDatos",
                serviceStore:"Settings.DeviceType.action",
                methodName:"save"
            });
        var save = function() {
            var valid = $("#validate_form").valid();
            if(valid) {
                core.dialog(core.i18n.Validate.confirm_message,core.i18n.Validate.yes,core.i18n.Validate.no).then(
                function(){
                    core.showLoader(core.i18n.Validate.saving);
                    data = deviceType;
                    data['id'] = dojo.byId('id').value;
                    dojo.query('input','validate_form').forEach(function(input) {
                        if(input.name) {
                            data[input.name] = input.value;
                        }
                    });
                    dojo.query('select','validate_form').forEach(function(input) {
                        if(input.name) {
                            data[input.name] = input.value;
                        }
                    });
                    
                    var generateCode = dojo.attr(dojo.byId('chkGenerate'),'checked');
                    if(generateCode){data["code"] = '';}
                    misDatos.setData(data);
                    misDatos.saveData(); 
                },hideLoader
                )
            }
        }
        
        var load = function(id) {
            var args = {
                url:"../DPMS/Settings.DeviceType.action",
                method:"load",
                params:[id]}
            callMethod(args).then(
                function(resultado) {
                    populate(resultado);
                },
                hideLoader
            );
        }
        dojo.connect(dojo.byId('saveBtn'), 'click', 'actionButtonsId', save);
        dojo.connect(dojo.byId('cancelBtn'), 'click', 'actionButtonsId', cancel);
        dojo.attr(dojo.byId('chkGenerate'),'click',toggleGenerateKey);
        var id = dojo.byId('id').value;
        if(id && id != -1) {
            load(id);
        } else {
            dojo.byId('status').value=1;
            hideLoader();
        }
    }    

    var toggleGenerateKey=function(){toggleGenerate('#chkGenerate','#code');}

    function defineValidationRules(i18n){
        $('#validate_form').validate({
            rules:{
                code: {
                    required : true
                    ,maxlength : 100
                },
                description: {
                    required : true
                    ,maxlength : 100
                },
                deviceGroupId: {
                    required : true
                }
            },
            messages: {
                code: specifiedMessage(i18n.Validate.invalid_required_text,'specify',10),
                description: specifiedMessage(i18n.Validate.invalid_required_text,'specify',100),
                deviceGroupId: i18n.Validate.missingCombo
            },
            errorPlacement: function (error, element) {
                error.insertBefore(element);
            }
        });
    }

    function populate(object) {
        var loadedVal = 0;

        dojo.query('input','validate_form').forEach(function(input) {
            loadedVal = object[input.name]
            if(input.name && (loadedVal||loadedVal=='0')) {
              input.value = loadedVal;
            }
        });
        dojo.query('select','validate_form').forEach(function(input) {
            loadedVal = object[input.name]
            if(input.name && (loadedVal||loadedVal=='0')) {
              input.value = loadedVal;
            }
        });
        hideLoader();
    }
    core.setLang('lang/catalog/device/device_type/nls/device.type').then(doEverything);
});