require(['dojo/on', 'core', 'bnext/gridComponent', 'dojo/dom', 'dojo/domReady!']
    , function (on, core, gridComponent, dom) {  
    var doEverything = function() {
        var grid = {};
        fillFooter(); 
        
        var btnAdd = dom.byId("btnAdd");
        on(btnAdd, "click", function () {
            core.navigateLegacy("v.device.folio.view")
        });  
        
        var department_column={
           title:"department",
           type:columnTypes.text,
           id:'department.description',
           isSortable:true,
           isTransient:false,
           searchObj:{col:1,type:'texto'}
        },
        area_column={
           title:"area",
           type:columnTypes.text,
           id:'area.description',
           isSortable:true,
           isTransient:false,
           searchObj:{col:1,type:'texto'}
        },
        deviceType_column={
           title:"deviceType",
           type:columnTypes.text,
           id:'deviceType.description',
           isSortable:true,
           isTransient:false,
           searchObj:{col:1,type:'texto'}
        },
        current_column={
           title:"current",
           type:columnTypes.text,
           id:'current',
           isSortable:true,
           isTransient:false,
           searchObj:{col:1,type:'long'}
        },
        edit_column={
            type: columnTypes['function'],
            id:'edit',
            parameters:['id'],
            action:function(id){
                document.location = "../view/v.device.folio.view?id="+id
            }
        },
        grid = new gridComponent({
            size:dom.byId('gridSize').value,
            id:"grid", 
            container:"dataGrid",
            searchContainer:"#auto",
            resultsInfo:"#auto", 
            paginationInfo:"#auto", 
            serviceStore:"DeviceFolio.action", 
            windowPath: dom.byId('windowPath').value,
            methodName:"getRows", 
            columns : [edit_column, department_column,area_column,deviceType_column,current_column],
            logging_column:true
        }); 
        return grid.setPageSize(dom.byId('gridSize').value);
    };
    doEverything().then(function() {
        core.setLang('lang/catalog/device/device_folio/nls/device.folio.list').then(core.hideLoader);
});
});

function cambiaEstatus(id) {
    showLoader(Validate.toggling_status)
    dialogExecutioner(Validate.toggling_confirm,Validate.yes,Validate.no, 
        "DeviceFolio.action", "toggleStatus", [id],
        Validate.edit_success, "", Validate.accept, grid);
        hideLoader();
}