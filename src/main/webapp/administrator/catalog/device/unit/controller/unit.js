require(['dojo/dom', 'dojo/on', 'dojo/dom-attr', 'dojo/query', 'bnext/callMethod', 'core', 'entity/unit', 'bnext/saveHandle', 'dojo/domReady!'], 
function (dom, on, domAttr, query, callMethod, core, unit, saveHandle) {
var data = {};
    fillFooter(); 
    var doEverything = function() {
        defineValidationRules();
        var misDatos = new saveHandle({
                savedObjName:"misDatos",
                serviceStore:"Settings.Unit.action",
                methodName:"save"
            });
        var save = function() {
            var valid = $("#validate_form").valid();
            if(valid) {
                core.dialog(core.i18n.Validate.confirm_message,core.i18n.Validate.yes,core.i18n.Validate.no).then(
                    function(){
                        showLoader(core.i18n.Validate.saving);
                        data = unit;
                        data['id'] = dom.byId('id').value;
                        query('input','validate_form').forEach(function(input) {
                            if(input.name) {
                                data[input.name] = input.value;
                            }
                        });
                        var generateCode = domAttr.get(dom.byId('chkGenerate'),'checked');
                        if(generateCode){data["code"] = '';}
                        misDatos.setData(data);
                        misDatos.saveData(); 
                    }, core.hideLoader
                );
            }
        };
        
        var load = function(id) {
            var args = {
                url:"../DPMS/Settings.Unit.action",
                method:"load",
                params:[id]}
            callMethod(args).then(
                function(resultado) {
                    populate(resultado);
                },
                core.hideLoader
            );
        };
        on(dom.byId('saveBtn'), 'click', save);
        on(dom.byId('cancelBtn'), 'click', cancel);
        on(dom.byId('chkGenerate'),'click', toggleGenerateKey);
        var id = dom.byId('id').value;
        if(id && id != -1) {
            load(id);
        } else {
            dom.byId('status').value=1;
            core.hideLoader();
        }
    }    

    var toggleGenerateKey=function(){toggleGenerate('#chkGenerate','#txtCode');}

    function defineValidationRules(){
        $('#validate_form').validate({
            rules:{
                code: {
                    required : true
                    ,maxlength : 100
                },
                description: {
                    required : true
                    ,maxlength : 100
                }
            },
            messages: {
                code: specifiedMessage(core.i18n.Validate.invalid_required_text,'specify',10),
                description: specifiedMessage(core.i18n.Validate.invalid_required_text,'specify',100)
            },
            errorPlacement: function (error, element) {
                error.insertBefore(element);
            }
        });
    }

    function populate(object) {
        var loadedVal = 0;
        query('input','validate_form').forEach(function(input) {
            loadedVal = object[input.name];
            if(input.name && (loadedVal||loadedVal=='0')) {
              input.value = loadedVal;
            }
        });
        core.hideLoader();
    }
    core.setLang('lang/catalog/device/unit/nls/unit').then(doEverything);
});