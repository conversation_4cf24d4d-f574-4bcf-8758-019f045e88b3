<%-- 
    Document   : activity-source
    Created on : 19/04/2016, 04:42:50 PM
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<% request.setCharacterEncoding("UTF-8"); %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<html class="${cssHtml} html-reset">
    <head>
        <title>Alta de fuente de actividades</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <%@include file="../../components/requiredScripts.jsp" %>
        <script type="text/javascript" src="../scripts/framework/bnext/administrator/activity/activity-source.js?${systemVersion}" ></script>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
    </head>
    <body writingsuggestions="false" textprediction="false">
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <%@include file="../../components/loader.jsp" %>
                    <form class="container-form grid-floating-action-buttons" method="POST" id="validate_form">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle"></h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint"></div>
                        <div class="grid-container">
                            <div class="grid-x grid-padding-x" id="mainDiv">
                                <div class="cell small-12">
                                    <div class="field-display">
                                        <strong id="statusText" class="labelText">${statusLabelKey}</strong>
                                        <label id="lblStatus"></label>
                                        <input type="hidden" name="status" id="status" value="${entity.status}"/>
                                        <input type="hidden" id="statusLabelKey" value="${statusLabelKey}"/>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6" >
                                    <div id="codeContainer" class="textarea-component generate-code-cection">
                                        <input type="text" required="required" name="code" id="code" value="${entity.code}"/>
                                        <label id="lblCode">Clave</label>
                                        <div id="generateCodeSection" display="${isNewEntity ? "" : "none"}">
                                            <%@include file="/../components/chk-generate.jsp" %>
                                        </div>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" id="description" name="description" required="required" value="${entity.description}"></input>
                                        <label id="lblDescription"></label>
                                    </div>
                                </div>        
                                <div class="cell small-12 actionButtons">
                                    <%@include file="../../../components/saveButtons.jsp" %>
                                </div>    
                                <div class="cell small-12">&nbsp;</div>   
                                <div id="serializedEntity" style="display: none">
                                    <s:property value="serializedEntity"/>
                                </div>
                                <textarea id="serializedStatusList" class="displayNone">${serializedStatusList}</textarea>
                                <input type="hidden" name="id" id="id" value="${id}"/>
                                <input type="hidden" name="deleted" id="deleted" value="${entity.deleted}"/>
                                <input type="hidden" id="deleted" value="${entity.deleted}"/>
                                <input type="hidden" id="lastModificationDate" value="${entity.lastModifiedDate}"/>
                                <input type="hidden" id="creationDate" value="${entity.createdDate}"/>
                                <input type="hidden" id="prefix" value="${prefix}"/>
                            </div>
                        </div>
                    </form>    
                    <%@include file="../../components/sessionVariables.jsp" %>
                    <%@include file="../../components/footer.jsp" %>
                </div>
            </div>
        </div>
    </body>
</html>