<%-- 
    Document   : activity-priority-list
    Created on : 30/03/2016, 11:11:20 AM
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <title>Control de prioridad de actividades</title>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script type="text/javascript" src="../scripts/framework/bnext/administrator/activity/activity-priority-list.js?${systemVersion}" ></script>
        <style>
            .box-color {
                height: 1.2rem;
                border-radius: 0.3rem;
                margin: 0.5rem;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../components/loader.jsp" %>
        
         <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="window_title">
                                        Mis actividades
                                    </h3>
                                    <div class="float-right">
                                        <a href="javascript: void(0);" id="addFavorite" class="material-icons" title="Agregar a favorito(s)">
                                            favorite
                                        </a>
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class ="grid-container">
                            <table id="dataGrid"></table>
                        </div>
                        <div class ="displayNone">
                            <s:hidden name="serializedStatusList" id="serializedStatusList" />  
                        </div>
                    </form>
                </div>
            </div> <%@include file="../../../components/sessionVariables.jsp" %>
        </div>  
        
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>
