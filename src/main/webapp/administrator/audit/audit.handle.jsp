<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="${cssHtml} html-reset">
    <head>
        <title>Auditoria</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}">  
        <%@include file="../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <style>
            .bnext .dijitDialogPaneContent {
                min-width: 500px;
            }
            .bnext .dijitDateTextBox {
                max-width: 130px;
            }
            #example_sh_grid_deps {
                display: none;
            }
            .information {
                width: auto;
                border: 0;
                background-color: white!important;
                -moz-border-radius: 3px;
                -webkit-border-radius: 3px;
                border-radius: 0;
                float: none;
                height: auto;
                font-size: 1rem;
                line-height: 1.75rem;
                text-indent: 0;
                margin-bottom: 0;
                padding-left: 0.5rem!important;
                border-bottom: 0px solid #ccc;
                border-top: 0px solid #ccc;
            }
            .information .field-display.icon-check::before {
                background-color: #4fca2b;
            }
            .information .field-display.icon-clear::before {
                background-color: #ffb6c1;
            }
            .grid-bottom-margin,
            .css_auditIndividuals_ground_colGround,
            #departmentsProcesses > div,
            #departmentsProcesses > form {
                display: none;
            }
            #departmentsProcesses > div.linked-grid-buttons-container {
                display: block;
            }
            #departmentsProcessesRequired {
                position: relative;
            }
            #departmentsProcessesRequired > label.error {
                position: absolute;
                width: auto;
                display: block;
                top: -1.25rem;
            }
            .floating-action-buttons .addToGrid.raised-button.fixed-button {
                background-color: #808080!important;
                color: #ccc!important;
            }
            .floating-action-buttons .addToGrid.raised-button.fixed-button.Button {
                background-color: ${systemColor}!important;
                color: #fff!important;
            }
            .grid-container.grid-floating-active .floating-action-buttons .button-component label.error {
                position: absolute;
                line-height: 1rem;
            }
            .split-layout {
                margin-bottom: 3rem;
            }
            .split-layout .container-form {
                min-height: 100%!important;
            }
            label.error {
                font-style: normal;
                font-size: initial!important;
            }
            @media print, screen and (min-width: 40em) {
                .split-layout .container-form {
                    min-height: 100%;
                }

                .grid-floating-active.grid-container.information {
                    background: #efefef;
                }
            }
            @media print, screen and (min-width: 64em) {
                .split-layout .grid-right-panel {
                    min-height: calc(100vh - 4.5em);
                }
            }

            @media print, screen and (min-width: 100em) {
                .split-layout .grid-right-panel {
                    min-height: calc(100vh - 17.3em);
                }
            }
            table {
                border: 1px solid #00000014;
            }
            .content_title {
                padding-bottom: 2rem;
            }
            .dijitTimeTextBox.dijitTimeTextBoxDisabled .dijitArrowButton.dijitDownArrowButton {
                display: none;
            }
            .bnext .dijitTimePickerTick .dijitTimePickerItemInner {
                font-size: 0.8rem;
            }

            .formDialogContainer {
                display: flex;
                flex-direction: column;
            }

            #lblFormDefault {
                font-size: 1rem;
                font-weight: 700;
                padding-left: 1rem;
            }

            .form-selected {
                display: flex;
                justify-content: flex-start;
                flex-direction: row;
                margin-top: 1rem;
                padding: 0;
                position: relative;
            }
            
            #addForms,
            input[type=button].fixed-button,
            .grid-container.grid-floating-active .floating-action-buttons .button-component span.material-icons + input[type="button"],
            .grid-container.grid-floating-active .floating-action-buttons .button-component span.material-icons,
            .linked-grid-buttons-container .button-component input[type="button"].buttondos,
            .linked-grid-buttons-container .button-component span.material-icons,
            .rightLinkedGrid .Button,
            .rightLinkedGrid .Button.addToGrid,
            .rightLinkedGridWithTitles .Button,
            .rightLinkedGridWithTitles .Button.addToGrid {
                color: ${systemSecondaryColor}!important;
                background-color: ${systemColorTextColor}!important;
                border: 1px solid ${systemSecondaryColor};
                box-shadow: none;
            }
            
            .linked-grid-buttons-container .button-component span.material-icons {
                border: 0;
            }
            
            .linked-grid-container .message_info.grid-component-container.info.gridInfo,
            .form-selected > div {
                background-color: ${systemColorLight}!important;
                border: 0!important;
            }
            .form-selected > div.line-height-1 {
                width: 100%;
            }
            
            .container-form.grid-floating-action-buttons .search-filter-chips {
                padding-top: 0rem;
            }
            
            #addForms {
                border-radius: 1.75rem;
            }
            #formSelectedDialogContainer {
                margin-top: 2rem;
            }
            #formSelectDialogContainer > div.button-component {
                margin-top: 0;
            }
            #formSelectDialogContainer {
                background: #fff!important;
                position: absolute;
                top: -1.15rem;
                right: 2rem;
            }
            .grid-right-panel--hidden #closeDialog, #closeDialog {
                display: none;
            }
            /* responsividad small */
            @media screen and (max-width: 26.5625em) {
                #formSelectedDialogContainer {
                    margin-top: 1rem;
                    flex-flow: column;
                }
                #formSelectDialogContainer {
                    position: static;
                    width: 100%;
                    top: 0;
                    left: 0;
                }
                #closeDialog {
                    top: 0.5rem;
                    right: 0.5rem;
                }
            }

            @media screen and (max-width: 64em) {
                #closeDialog {
                    position: absolute;
                    display: block;
                    top: 1rem;
                    right: 1rem;
                }
            }
            
            .line-height-1 {
                display: flex;
                flex-direction: row;
                height: inherit;
                padding-block: 1rem;
            }

            #auditDocuments .button-component {
                display: none;
            }

            .text-left {
                text-align: left;
            }
            
            #commentsTable tbody tr td{
                padding-left: 1rem;
                overflow-wrap: anywhere;
            }
            
            .dialog-comments-container {
                padding: 1rem;
            }
            
            #commentary {
                resize: none;
            }
            
            #filesLabel {
                font-size: 0.7rem;
            }
            
        </style>
        <script type="text/javascript" src="c.audit.handle.controller?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../components/loader.jsp" %>
        <div class="split-layout">
            <div class="grid-container grid-floating-active">
                <div class="grid-x">
                    <div class="cell">
                        <div  class="container-form grid-floating-action-buttons">
                            <form method="POST" id="validate_form">
                                <div class="header grid-container">
                                    <div class="grid-x">
                                        <h3 class="cell igx-card-header__title content_title" id="mainTitle">
                                        </h3>
                                    </div>
                                </div>
                                <div class="floating-action-buttons displayNone">
                                    <div class="button-component process">
                                        <!-- El botón existe solo para ocultarlo, los registros se agregan vía código -->
                                        <span class="material-icons">add</span>
                                        <input id="addAuditIndividualsBtn" title="Click para agregar" type="button" class="addToGrid Button raised-button fixed-button" value="Programación de auditoría" />
                                    </div>
                                </div>
                                <div class="grid-container">
                                    <div class="grid-x grid-padding-x" id="contenido" >
                                        <div class="field-display cell small-12 medium-6 large-3 displayNone" id="statusContainer">
                                            <strong>
                                                <label id="statusText" class="labelText">Por asignar</label>
                                            </strong>
                                            <label id="lblStatus">Estatus</label>
                                            <s:hidden name="status" id="status"/>
                                        </div>
                                        <div class="field-display cell small-12 medium-6 large-3 displayNone">
                                            <strong class="labelText" name="actualStart" id="actualStart">NA</strong>
                                            <label id="lblActualStart"></label>
                                        </div>
                                        <div class="field-display cell small-12 medium-6 large-3 displayNone">
                                            <strong class="labelText" name="actualEnd" id="actualEnd">NA</strong>
                                            <label id="lblActualEnd"></label>
                                        </div>
                                        <div class="cell small-12 medium-6 large-3 field-display" id="scoreContainer">
                                            <strong>
                                                <s:label name="scoreText" value="%{score}" cssClass="labelText"/>
                                            </strong>
                                            <label id="lblScore">Calificación</label>
                                        </div>
                                        <div class="cell small-12 medium-6" >
                                            <div id="codeContainer" class="textarea-component generate-code-cection">
                                                <input type="text" required="required" name="txtCode" id="txtCode"/>
                                                <label id="lblCode"></label>
                                                <div id="generateCodeSection">
                                                    <%@include file="/../components/chk-generate.jsp" %>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cell small-12 medium-6">
                                            <div class="select-component">
                                                <s:select name="auditType" id="auditType" 
                                                          headerKey="" headerValue= "-- Seleccione --"
                                                          list="types"
                                                          listKey="value" listValue="text" >
                                                </s:select>
                                                <label for="auditType"></label>
                                            </div>
                                        </div>
                                        <div id="businessUnitContainer" class="cell small-12 medium-6 select-component displayNone">
                                            <select  name="businessUnit" id="businessUnit" disabled="true">
                                                <option>-- Seleccione --</option>
                                            </select>
                                            <label id="lblBusinessUnit">Planta</label>
                                        </div>
                                        <div id="questionnaireContainer" class="cell small-12 medium-12 select-component displayNone">
                                            <select name="questionnaire" id="questionnaire" >
                                                <option>-- Seleccione --</option>
                                            </select>
                                            <label>
                                                <span id="lblQustionnaire" class="none">Cuestionario:</span>
                                                <span id="lblQustionnaireArea" class="areas">Cuestionario:</span>
                                                <span id="lblQustionnaireProcess" class="process">Cuestionario predeterminado:</span>
                                            </label>
                                        </div>
                                        <div id="processContainer" class="cell small-12 medium-6 process select-component displayNone">
                                            <select name="process" id="process">
                                                <option>-- Seleccione --</option>
                                            </select>
                                            <label id="lblProceso">Proceso</label>
                                        </div>
                                        <div id="buildingContainer" class="cell small-12 medium-6 areas select-component displayNone">
                                            <select name="building" id="building">
                                                <option>-- Seleccione --</option>
                                            </select>
                                            <label id="lblBuilding"></label>
                                        </div>
                                        <div id="areaContainer" class="cell small-12 medium-6 areas select-component displayNone">                    
                                            <select name="area" id="area">
                                                <option>-- Seleccione --</option>
                                            </select>
                                            <label id="lblArea"></label>
                                        </div>
                                        <div id="auditorContainer" class="cell small-12 medium-6 select-component displayNone">
                                            <select name="auditor" id="auditor">
                                                <option>-- Seleccione --</option>
                                            </select>
                                            <label id="lblAuditor">Auditor líder general</label>
                                        </div>
                                        <div class="cell small-12 medium-6 textarea-component">
                                            <label id="lblDias" class="lblDias textarea-componen-suffix"></label>
                                            <input type="number" min="0" data-natural-number id="diasAnticipacion" name="diasAnticipacion" class="diasAnticipacion" value="1" />
                                            <label id="lblDiasAnticipacion">Anticipación para notificar sobre la auditoría </label>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <form method="POST" id="dijit_form">
                                <div class="grid-container">
                                    <div class="grid-x grid-padding-x">
                                        <div id="StartDate" class="textarea-component date-widget cell small-12 medium-6 large-3">
                                            <label class="textarea-componen-suffix material-icons">arrow_drop_down</label>
                                            <input type="text" required="required" id="startDate" name="startDate" value="" readonly=""/>
                                            <label id="lblStart"></label>
                                        </div> 
                                        <div class="widget-component time-widget cell small-12 medium-6 large-3">
                                            <input type="text" required="required" name="startTime" id="startTime" value=""/>     
                                            <label id="lblStartTime"></label>
                                        </div>
                                        <div id="EndDate" class="textarea-component date-widget cell small-12 medium-6 large-3">
                                            <label class="textarea-componen-suffix material-icons">arrow_drop_down</label>
                                            <input type="text" required="required" id="endDate" name="endDate" value="" readonly=""/>
                                            <label id="lblEnd"></label>
                                        </div> 
                                        <div class="widget-component time-widget cell small-12 medium-6 large-3">
                                            <input type="text" required="required" name="endTime" id="endTime" value=""/>
                                            <label id="lblEndTime"></label>
                                        </div>
                                        <div class="cell small-12">
                                            <div class="textarea-component">
                                                <textarea name="objective" id="objective" required="required"></textarea>
                                                <label id="lblObjective"></label>
                                            </div>
                                        </div>
                                        <div class="cell small-12">
                                            <div class="textarea-component">
                                                <textarea  name="criteria" id="criteria" required="required"></textarea>
                                                <label id="lblCriteria"></label>
                                            </div>
                                        </div>
                                        <s:if test="id == -1"> 
                                            <div class="cell form-selected add-button-container displayNone" id="formSelectedDialogContainer">
                                                <div class="displayNone" id="formSelectDialogContainer">
                                                    <div class="button-component">
                                                        <span class="material-icons">fact_check</span>
                                                        <input type="button" id="addForms">
                                                    </div>
                                                </div>
                                                <div class="line-height-1">
                                                    <span id="lblFormDefault"></span>&nbsp;
                                                    <p id="lblFormSelected">-Seleccione-</p>
                                                </div>
                                            </div>
                                        </s:if>
                                        <div class="cell small-12" id="auditHelpers">
                                        </div>
                                        <div class="cell small-12" id="departmentsProcesses">
                                        </div>
                                        <div class="cell small-12 displayNone" id="departmentsProcessesRequired">
                                            <input type="text" required="required" readonly="true" value="" id="addDepsVal" name="addDepsVal"
                                                   style="border: 0px;background: transparent!important;width: 0px;height: 0px;"/>
                                        </div>
                                        <div class="cell small-12" id="auditIndividuals">
                                        </div>
                                        <div class="cell">
                                            <div class="button-component">
                                                <span class="material-icons">add</span>
                                                <input type="button" id="addDocuments">
                                            </div>
                                            <div class="table-component">
                                                <div id="auditDocuments"></div>
                                                <label id="documentsLabel"></label>
                                            </div> 
                                        </div>
                                        <s:if test="id == -1">
                                            <div class="cell small-12 medium-12 large-12">
                                                <div class="button-component">
                                                    <input type="button" id="addFiles">
                                                </div>
                                            </div>
                                        </s:if>
                                        <div class="cell small-12 medium-12 large-12">
                                            <label id="filesLabel" class="text-left-print text-left-print-show w-fit-content"></label>
                                        </div>
                                        <div class="cell small-12 medium-12 large-12">
                                            <table id="fileAttacherTable"></table>
                                        </div>
                                        &nbsp;
                                        <s:if test="id == -1">
                                        <div class="cell small-12 medium-12 large-12 displayNone" id="commentTableId">
                                            <table id="commentsTable" class="display grid-component-container">
                                                <thead>
                                                    <tr class="header-row text-left"><th>Comentario<th></tr>
                                                </thead>
                                                <tbody>
                                                    
                                                </tbody>
                                            </table>
                                        </div>
                                        </s:if>
                                        <s:if test="id != -1">
                                        <div class="cell small-12 linked-grid-container grid-filled-container">
                                            <div class="table-component">   
                                                <table id="grid_comment_created">
                                                    <tr>
                                                        <td>
                                                            <img src="../images/common/waiting.gif" border="0"/>
                                                        </td>
                                                    </tr>
                                                </table>
                                                <label id="comentsAudit"></label>
                                            </div>
                                        </div>
                                        </s:if>
                                        <div class ="separator-form actionButtons cell small-12" id="actionButtonsId">
                                            <button class="button raised-button" type="button" id="saveBtn"></button>
                                            <button class="button" type="button" id="addComments" style="${id != -1 ? 'display:none' : 'display:block'}"></button>
                                            <button class="button" type="button" id="cancelBtn"></button>
                                            <button class="button displayNone" type="button" id="updateBtn"></button>
                                            <button class="button" type="button" id="cleanBtn"></button>
                                        </div>
                                        <div class="actionButtons button-component cell small-12" id="deletedActionButtonsId" style="display:none">
                                            <button class="button raised-button" type="button" id="cancelBtnAtDeleted"></button>
                                        </div>
                                        <div class="cell small-12">&nbsp;</div>
                                        <%@include file="../../components/sessionVariables.jsp" %>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid-container grid-right-panel grid-right-panel--hidden" id="auditTypeDisplay" >
                <div class="grid-x">
                    <div class="grid-right-panel-overlay" id="rightPanelOverlay"></div>
                    <div class="cell small-12 medium-6 large-3 container-cell">
                        <div class="grid-right-panel-container">
                            <div class="fancy-scroll right-panel-content">
                                <div>
                                    <h3 id="rightPannelTitle" class="igx-card-header__title content_title">
                                    </h3>
                                    <span id="closeDialog" class="material-icons">close</span>
                                </div>
                                <div class="information grid-container grid-floating-active">
                                    <div class="field-display">
                                        <strong id="qualifiedQuestion" class="editableKey">Si</strong>
                                        <label id="lblQuestion" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="findingCapable" class="editableKey">Si</strong>
                                        <label id="lblFinding" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="confirmedByAudited" class="editableKey">Si</strong>
                                        <label id="lblConfirmed" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="acceptedByAudited" class="editableKey">Si</strong>
                                        <label id="lblResult" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="addActivity" class="editableKey">Si</strong>
                                        <label id="lblAddActivity" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="minimumScore" class="editableKey">Si</strong>
                                        <label id="lblMinimumScore" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="supportStaff" class="editableKey">Si</strong>
                                        <label id="lblSupportStaff" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="technicalExperts" class="editableKey">Si</strong>
                                        <label id="lblTechnicalExperts" ></label>
                                    </div>
                                    <div class="field-display">
                                        <strong id="auditorsInTraining" class="editableKey">Si</strong>
                                        <label id="lblAuditorsInTraining" ></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="formSelectDialog" data-dojo-type="dijit/Dialog">
                <div class="formDialogContainer">
                    <div class="grid-zone">
                        <table id="grid"></table>
                    </div>
                    <div class="buttons">
                        <input type="button" id="btnClose" class="button finishBtn raised-button right"/>
                    </div>
                </div>
            </div>
            <form action="../view/v.audit.survey.preview.view" method="GET" target="_blank" name="preview">
                <input type="hidden" name="id" value="-1"/>
                <input type="hidden" name="task" value="preview"/>
            </form>
        </div>
            <s:hidden name="systemColor" id="SYSTEM_COLOR" />
            <s:hidden name="id" id="id"/>
            <s:hidden name="task" id="task"/>
            <s:hidden name="floatingGridSize" id="floatingGridSize" />
            <input type="hidden" id="onBtnAdd" value="../view/v.audit.handle.action?id=-1" />
            <input type="hidden" id="onBtnControl" value="../view/v.audit.list.view" />
            <input type="hidden" id="idDocs"/>
            <div dojoType="dijit.Dialog" id="searchDeps" class="dialog-fix">
                <ul class="content_area">
                    <li tabindex="0">
                        <table id="dataGrid_SearchDEP"></table>
                    </li>
                    <li class="separator" style="padding-bottom: 0px !important;">
                        <button class="button right finishBtn raised-button" type="button" id="finishBtn_DEP"></button>
                    </li>
                </ul>
            </div>
            <%@include file="../../components/footer.jsp" %>
    </body>
</html>