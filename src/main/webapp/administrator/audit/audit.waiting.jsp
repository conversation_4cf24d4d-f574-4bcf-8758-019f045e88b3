<%-- 
    Document   : audit.waiting.jsp
    Created on : 11-may-2013, 16:01:13
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html>
    <head>
        <title>Auditoría</title>
        <%@include file="../../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <style>
            #footerComponent {
                width: 100%;
            }
        </style>
        <script type="text/javascript">
            require(['core', 'dojo/dom', 'dojo/on', 'dojo/domReady!'], function(core, dom, on) {
                on(dom.byId('cancelBtn'), 'click', function () {
                    if(top.frames && top.frames.length > 0) {
                        core.dialog(
                            'Será enviado a la pantalla de pendientes, ¿Deseá continuar?'
                        ).then(
                            function() {
                                location.replace('../view/v.pendings.view');
                            }
                        );
                    } else {
                        window.close();
                    }
                });
                core.hideLoader();
            });
            function autoresize(ctrl) {
                ctrl.style.height = '0px';
                ctrl.style.height = (ctrl.scrollHeight) + 'px';
            }
            $('.autoresize').keyup(function () {
                autoresize(this);
            });

            $(function () {
                $("textarea.autoresize").each(function () {
                    this.style.height = (this.scrollHeight)+'px';
                });
            });
        </script>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../../components/loader.jsp" %>
        <form method="POST" id="validate_form">
            <div class="content_title">
                <h1 id="mainTitle">Mensaje del sistema</h1>
            </div>
            <ul class="content_area" id="contenido">
                <li>
                    <label class="labelText" style="width: initial;">Debe esperar a que la auditoría sea realizada para poder ver los resultados.</label>
                </li>
                <li  >
                    <!-- la funcionalidad de cada boton esta validada internamente -->
                    <ul class="actionButtons" id="actionButtonsId">
                        <li>
                            <input class="Button raised-button" type="button" id="cancelBtn" value="Salir"/>
                        </li>
                    </ul>
                </li>
            </ul>
        </form>
        <s:hidden name="systemColor" id="SYSTEM_COLOR" />
        <s:hidden name="id" id="id"/>
        <%@include file="../../../../components/footer.jsp" %>
    </body>
</html>
