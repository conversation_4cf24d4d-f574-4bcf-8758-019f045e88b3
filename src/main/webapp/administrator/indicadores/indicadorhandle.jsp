<%@page import="DPMS.DAOInterface.IProcessDAO"%>
<%@page import="DPMS.DAOInterface.IDepartmentDAO"%>
<%@page import="DPMS.DAOInterface.IBusinessUnitDAO"%>
<%@page import="DPMS.DAOInterface.IBuildingDAO"%>
<%@page import="Framework.Config.Utilities"%>
<%@page import="mx.bnext.access.ProfileServices"%>
<%@page import="DPMS.Mapping.BusinessUnitDepartment"%>
<%/* ======================================================================
   PROYECTO BnextISO
   NOMBRE: indicadorhandle.jsp
   AUTOR: Casi Ing. San Gilberto de los Campanas
   COMPAÑIA: Block Networks.
   FECHA  : 27/07/2007
   DESCRIPCION FUNCIONAL: Alta o modificacion de indicadores.
   COMENTARIOS:
   ====================================================================== */%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8");%>
<%--  Declaración de los Java Bean --%>
<jsp:useBean id="indicador" scope="request" class="isoblock.indicadores.Indicador"/>
<%--  Asignación de propiedades de los Java Beans --%>
<jsp:setProperty name="indicador" property="*" />
<%-- Includes --%>
<%@ include file="../../templates/superior.jsp"%>
<%@ include file="../../includes/getsesionusuario.jsp"%>
<%@ include file="../../includes/titulotabla.jsi"%>
<script type='text/javascript' src="../scripts/numeros.js?${systemVersion}"></script> 
<%@ include file="../../components/appConfig.jsp"%>
<style>
#periodicidadRevision label,#periodicidad label {
    display: inline-block;
  }
</style>
<script type='text/javascript' src="../scripts/framework/dojo/dojo.js?${systemVersion}" ></script>
<script type='text/javascript' src="../scripts/framework/jquery.js?${systemVersion}"></script>
<%--  Comienza el cuerpo del JSP  --%>
<%
  Boolean iViewStatus = false;
  indicador.sesionusuarioid = sesionusuarioid;
  if (indicador.getStrEstado().equals("editar") && request.getParameter("reload") == null || indicador.getStrEstado().equals("ver")) {
    indicador.setAllValues();
    iViewStatus = true;
  }
%>
<script type='text/javascript'>
  var calendario= new ctlSpiffyCalendarBox("calendario", "form1", "dteFecha","btnDate","<%=indicador.getDteFecha()%>",2,0,0,true);
  var calendarioRevision= new ctlSpiffyCalendarBox("calendarioRevision", "form1", "dteFechaPrimeraRevision","btnDateRevision","<%=indicador.getDteFechaPrimeraRevision()%>",2,0,0,true);
  require(['core', 'bnext/callMethod', 'dojo/i18n!bnext/administrator/indicadores/nls/indicadorhandle', 'dojo/domReady!'],
     function(core, callMethod, i18n) {
     core.hideLoader();
  window.onload = function(){
    codigoPeriodicidadIndicador();
    codigoPeriodicidadRevision();
    if (document.form1.intStatusIndicador.value === '1' && document.form1.intIndicadorId.value !== '0') {
        desablitarResponsables();
    }
  };
  function warnNotEditable() {
        core.info('<%=tags.getString("indicadores.indicadorhandle.AdvertenciaCampoNoEditable")%>');
  }
  function desablitarResponsables() {
      document.form1.intAreaIdListaUsuarios.onchange = warnNotEditable;
      document.getElementById('moverTodosLista').onclick = warnNotEditable;
      document.getElementById('moverLista').onclick = warnNotEditable;
      document.getElementById('borrarLista').onclick = warnNotEditable;
      document.getElementById('borrarTodosLista').onclick = warnNotEditable;
  }
     function validateSubmit(){
           core.showLoader(i18n.loader_validating);
           var feedback = legacyValidation();
           if(feedback.isValid()) {
              function messageHideLoader(msg) {
                 core.dialog(msg);
                 core.hideLoader();
              }
              if(document.form1.strEstado.value === 'editar'){
                 parameters();
              }else{
                 callMethod({
                    url: 'MeterService.action',
                    method: 'isValidMeterTitle',
                    params: [document.form1.vchTitulo.value]
                 }).then(
                    function(isValidTitle) {
                       if(isValidTitle) {
                          core.showLoader(i18n.loader_saving);
                          parameters();
                       } else {
                          messageHideLoader(i18n.message_repeated_title);
                       }
                    }, messageHideLoader
                 );
              }
           } else { 
              core.hideLoader();
              core.dialog(feedback.message);
           }
     }       
  window.validateSubmit = validateSubmit;
  });
  function copiausuarios(){
    selecciona_items(document.form1.arrayResponsables);
  }
  function reloadpage(){
    copiausuarios();
  <%-- Este if en el reload es para que se puedan cargar las opciones para el mes --%>
      var opcionSeleccionada = document.form1.tipoPeriodicidad[document.form1.tipoPeriodicidad.selectedIndex].value;
      if(opcionSeleccionada == "monthly"){
        if(document.form1.opcion[getRadioButtonSelectedIndex(document.form1.opcion)].value == "diaDelMes"){
          document.form1.separacion.value = document.form1.separacion1.value;
        }else{
          document.form1.separacion.value = document.form1.separacion2.value;
        }
      }
      var opcionSeleccionadaRevision = document.form1.tipoPeriodicidadRevision[document.form1.tipoPeriodicidadRevision.selectedIndex].value;
      if(opcionSeleccionadaRevision == "monthly"){
        if(document.form1.vchOpcionRevision[getRadioButtonSelectedIndex(document.form1.vchOpcionRevision)].value == "diaDelMes"){
          document.form1.intSeparacionRevision.value = document.form1.separacion1_revision.value;
        }else{
          document.form1.intSeparacionRevision.value = document.form1.separacion2_revision.value;
        }
      }
      document.form1.action="../view/v.indicadores.view";
      document.form1.submit();
    }
    function validaNumeros(){
      if ((event.keyCode < 48 || event.keyCode > 57) && event.keyCode != 46){
        event.returnValue = false;
        event.preventDefault();
      }
    }
    function getRadioButtonSelectedIndex(ctrl){
      for(i=0; i < ctrl.length; i++){
        if(ctrl[i].checked)
          return i;
      }
    }
    function checaStatus(){
      if(document.form1.statusIndicador.checked){
        document.form1.intStatusIndicador.value ='1';
      }else{
        document.form1.intStatusIndicador.value ='0';
      }
    }
    // Limpiar color de determinado campo
    function limpiar(name){
      eval("document.form1." + name + ".style.backgroundColor='#FFFFFF'");
      //alert("limpio '"+name+"'");
    }
    // Limpiar colores de los campos
    function limpiaColor(){
      //validacion de texts vacios
      limpiar('vchTitulo');
      limpiar('vchFuenteInformacion');
      limpiar('intObjetivoId');
      limpiar('intUbicacionId');
      limpiar('intAreaId');
      limpiar('txtFormula');
      limpiar('intMeta');
      limpiar('intDiasAnticipacion');
      limpiar('intUbicacionMonitoreoId');
      limpiar('arrayResponsables');
      limpiar('dteFecha');
      limpiar('dteFechaPrimeraRevision');
    }
    // Se agregaron tags
    
    function legacyValidation(){
      limpiaColor();
      var faltante = '<%=tags.getString("indicadores.indicadorhandle.DatosNecesarios")%>';
      var falta=false;
      //validacion de texts vacios
      if(document.form1.vchTitulo.value.length===0){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.TituloNecesario")%>';
        document.form1.vchTitulo.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      
      if(document.form1.vchFuenteInformacion.value.length===0){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.FuenteInformacionNecesario")%>';
        document.form1.vchFuenteInformacion.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      //combo objetivo
      if(document.form1.intObjetivoId.value==="-1"){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.ObjetivoNecesario")%>';
        document.form1.intObjetivoId.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      if(document.form1.intFacultadId.value==="-1"){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.PlantaNecesario")%>';
        document.form1.intFacultadId.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      //combo departamento del proceso
      if(document.form1.intUbicacionId.value==="-1"){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.DepartamentoProcesoNecesario")%>';
        document.form1.intUbicacionId.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      //combo proceso
      if(document.form1.intAreaId.value==="-1"){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.ProcesoNecesario")%>';
        document.form1.intAreaId.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      //formula
      if(document.form1.txtFormula.value===0){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.FormulaNecesario")%>';
        document.form1.txtFormula.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      //Meta
      if(document.form1.intMeta.value.length===0){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.MetaNecesario")%>';
        document.form1.intMeta.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      //txtMeta
      if(document.form1.txtMeta.value.length===0){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.MetaNecesario")%>';
        document.form1.txtMeta.style.backgroundColor=requiredfield();
        if(!falta){
          falta=true;
        }
      }
      //validacion para periodicidad
      var opcionSeleccionada = document.form1.tipoPeriodicidad[document.form1.tipoPeriodicidad.selectedIndex].value
      var i=0;
      if(opcionSeleccionada == "weekly"){
        if(document.form1.lunes.checked)
          i++;
        if(document.form1.martes.checked)
          i++;
        if(document.form1.miercoles.checked)
          i++;
        if(document.form1.jueves.checked)
          i++;
        if(document.form1.viernes.checked)
          i++;
        if(document.form1.sabado.checked)
          i++;
        if(document.form1.domingo.checked)
          i++;
        if(!(i>0)){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.Perioricidad")%>';
          if(!falta){
            falta=true;
          }
        }
      }
      if(opcionSeleccionada == "monthly"){
        if(document.form1.opcion[getRadioButtonSelectedIndex(document.form1.opcion)].value == "diaDelMes"){
          document.form1.separacion.value = document.form1.separacion1.value;
          //Dia del mes
          if(document.form1.dia.value.length == 0){
            faltante+='<%=tags.getString("indicadores.indicadorhandle.DiaMesNecesario")%>';
            document.form1.dia.style.backgroundColor=requiredfield();
            if(!falta){
              //document.form1.dia.focus();
              falta=true;
            }
          }
          //cuantos meses
          if(document.form1.separacion1.value.length == 0){
            faltante+='<%=tags.getString("indicadores.indicadorhandle.CuantosMesNecesario")%>';
            document.form1.separacion1.style.backgroundColor=requiredfield();
            if(!falta){
              //document.form1.separacion1.focus();
              falta=true;
            }
          }
        }else{
          document.form1.separacion.value = document.form1.separacion2.value;
          if(document.form1.separacion2.value.length == 0){
            faltante+='<%=tags.getString("indicadores.indicadorhandle.CuantosMesNecesario")%>';
            document.form1.separacion2.style.backgroundColor=requiredfield();
            if(!falta){
              //document.form1.separacion2.focus();
              falta=true;
            }
          }
        }
      }
      //valor para la separacion
      if(((opcionSeleccionada == "weekly")||(opcionSeleccionada == "daily"))&&(document.form1.separacion.value.length==0)){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.valorSeparacionNecesario")%>';
        document.form1.separacion.style.backgroundColor=requiredfield();
        if(!falta){
          //document.form1.separacion.focus();
          falta=true;
        }
      }
      //validacion de dias de anticipacion
      if(document.form1.intDiasAnticipacion.value.length==0){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.DiasAnticipacionNecesario")%>';
        document.form1.intDiasAnticipacion.style.backgroundColor=requiredfield();
        if(!falta){
          //document.form1.intDiasAnticipacion.focus();
          falta=true;
        }
      }
      //combo departamento encargado de revisiones
      if(document.form1.intUbicacionMonitoreoId.value=="-1"){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.DepartamentoEngargadoRevisionesNecesario")%>';
        document.form1.intUbicacionMonitoreoId.style.backgroundColor=requiredfield();
        if(!falta){
          //document.form1.intUbicacionMonitoreoId.focus();
          falta=true;
        }
      }
      //validacion para la periodicidad de la revision
      var opcionSeleccionadaRevision = document.form1.tipoPeriodicidadRevision[document.form1.tipoPeriodicidadRevision.selectedIndex].value
      i=0;
      if(opcionSeleccionadaRevision == "weekly"){
        if(document.form1.boolLunesRevision.checked)
          i++;
        if(document.form1.boolMartesRevision.checked)
          i++;
        if(document.form1.boolMiercolesRevision.checked)
          i++;
        if(document.form1.boolJuevesRevision.checked)
          i++;
        if(document.form1.boolViernesRevision.checked)
          i++;
        if(document.form1.boolSabadoRevision.checked)
          i++;
        if(document.form1.boolDomingoRevision.checked)
          i++;
        if(!(i>0)){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.PerioricidadDeRevision")%>';
          if(!falta){
            falta=true;
          }
        }
      }
      if(opcionSeleccionadaRevision == "monthly"){
        if(document.form1.vchOpcionRevision[getRadioButtonSelectedIndex(document.form1.vchOpcionRevision)].value == "diaDelMes"){
          document.form1.intSeparacionRevision.value = document.form1.separacion1_revision.value;
          // revision dia del mes
          if(document.form1.intDiaRevision.value.length == 0){
            faltante+='<%=tags.getString("indicadores.indicadorhandle.RevisionDiaDelMesNecesario")%>';
            document.form1.intDiaRevision.style.backgroundColor=requiredfield();
            if(!falta){
              //document.form1.intDiaRevision.focus();
              falta=true;
            }
          }
          //validacion cada cuantos meses revision
          if(document.form1.separacion1_revision.value.length == 0){
            faltante+='<%=tags.getString("indicadores.indicadorhandle.CuantosMesesRevisionNecesario")%>';
            document.form1.separacion1_revision.style.backgroundColor=requiredfield();
            if(!falta){
              //document.form1.separacion1_revision.focus();
              falta=true;
            }
          }
        }else{//validacion cada cuantos meses revision
          document.form1.intSeparacionRevision.value = document.form1.separacion2_revision.value;
          if(document.form1.separacion2_revision.value.length == 0){
            faltante+='<%=tags.getString("indicadores.indicadorhandle.CuantosMesRevisionNecesario")%>';
            document.form1.separacion2_revision.style.backgroundColor=requiredfield();
            if(!falta){
              //document.form1.separacion2_revision.focus();
              falta=true;
            }
          }
        }
      }
      //validacion agregar valor para la separacion de revision
      if(((opcionSeleccionadaRevision == "weekly")||(opcionSeleccionadaRevision == "daily"))&&(document.form1.intSeparacionRevision.value.length==0)){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.valorSeparacionRevisionNecesario")%>';
        document.form1.intSeparacionRevision.style.backgroundColor=requiredfield();
        if(!falta){
          //document.form1.intSeparacionRevision.focus();
          falta=true;
        }
      }
      if(document.getElementById('intReportarPor').value == "0") {
        //validacion responsables a reportar
        if(document.form1.arrayResponsables.options.length<1 && document.getElementById('intReportarPor').selectedIndex == '0'){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.AsignarResponsableNecesario")%>';
          document.form1.arrayResponsables.style.backgroundColor=requiredfield();
          if(!falta){
            //document.form1.arrayResponsables.focus();
            falta=true;
          }
        }
      } else {
        //validacion responsables a reportar
        if(document.form1.arrayCarreras.options.length<1){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.AsignarResponsableUnidadNecesario")%>';
          document.form1.arrayCarreras.style.backgroundColor=requiredfield();
          if(!falta){
            //document.form1.arrayCarreras.focus();
            falta=true;
          }
        }
      }
      //valida Reporte por areas
      if(document.form1.list_der_Carrera.options.length<1 && document.getElementById('intReportarPor').selectedIndex == '1'){
        faltante+='\n  <%=tags.getString("indicador.indicadorhandle.ElijeAlmenosUnAreaALaCualReportar")%>';
        document.form1.list_der_Carrera.style.backgroundColor=requiredfield();
        falta=true;
      }
      var faltafecha=false;
      //validacion de fecha de levanmiento
  <% if (indicador.parameter("strEstado", request).equals("")) {%>
      if(document.form1.dteFecha.value==""){
        faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaLevanmientoVerificar")%>';
        faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaPrimerLevanmientoVerificar")%>';
        document.form1.dteFecha.style.backgroundColor=requiredfield();
        if(!falta){
          //document.form1.dteFecha.focus();
          falta=true;
        }
        if(!faltafecha){
          faltafecha=true;
        }
      } else  if (!checaFecha()){ //checa la fecha del levantamiento con la fecha de hoy
        if(!faltafecha){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaLevanmientoVerificar")%>';
          faltafecha=true;
        }
        faltante +='<%=tags.getString("indicadores.indicadorhandle.FechaPosteriorIgualLevanmientoVerificar")%>';
        document.form1.dteFecha.style.backgroundColor=requiredfield();
        if(!falta){
          //document.form1.dteFecha.focus();
          falta=true;
        }
      }
  <%}%>
      //validacion de fecha de revision
      if(document.form1.dteFechaPrimeraRevision.value==""){
        if(!faltafecha){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaLevanmientoVerificar")%>';
          faltafecha=true;
        }
        if(!falta){
          //document.form1.dteFecha.focus();
          falta=true;
        }//validacion para la primera revision
        faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaLevanmientoVerificarRevision")%>';
        document.form1.dteFechaPrimeraRevision.style.backgroundColor=requiredfield();
      } else  if (!checaFechaRev()){ //checa la fecha de la revision con la fecha de hoy
        if(!faltafecha){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaLevanmientoVerificar")%>';
          faltafecha=true;
        }//validacion fecha revision posterior
        faltante += '<%=tags.getString("indicadores.indicadorhandle.FechaPosteriorIgualRevisionVerificar")%>';
        document.form1.dteFechaPrimeraRevision.style.backgroundColor=requiredfield();
        if(!falta){
          //document.form1.dteFechaPrimeraRevision.focus();
          falta=true;
        }
      }
      //comparando fechas
      if(!faltafecha) {
        //*/
  <%-- if (!indicador.getStrEstado().equals("editar")) { --%>
        if (!checaFechas()){
          faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaLevanmientoVerificarRevision")%>';
          faltante+='<%=tags.getString("indicadores.indicadorhandle.FechaMenorIgualRevisionVerificar")%>';
          document.form1.dteFecha.style.backgroundColor=requiredfield();
          document.form1.dteFechaPrimeraRevision.style.backgroundColor=requiredfield();
          if(!falta){
            //document.form1.dteFecha.focus();
            falta=true;
          }
          if(!faltafecha){
            faltafecha=true;
          }
        }
  <%-- } --%>
      }
      //si no tuvo errores empieza a guardar el indicador
        return {
            valid: !falta,
            isValid: function() {
                return this.valid;
            },
            message: faltante
        };
    }
    /**
     * Modificacion
     * Se genera el codigo para hacer validar que las fechas no sean menores a la fecha de hoy
     * y que la inicial no sea mayor a la final
     *
     * Luis Limas
     * 30/12/2009
     */
  <%= indicador.checarFechas(
          "checaFechas",
          "form1",
          "dteFecha", //es el nombre que se le dio al text del objeto "calendario"
          "dteFechaPrimeraRevision", //es el nombre que se le dio al text del objeto "calendario2"
          ">", //hace la accion si la fecha de inicio sea > a la final
          false, //condicion para que muestre o no el mensaje de confirmacion
          false, //que permita o no guardar fechas anteriores a hoy
          "", //si no se cumple la comparacion regresa un false
          tags.getString("indicadores.indicadorhandle.-estemensajenodebemostrarse-"), //mensaje de confirmacion
          "",
          "return true;")%>
            //verifica la fecha de inicio con la fecha de hoy
  <%= indicador.checaFecha(
          "checaFecha",
          "form1",
          "dteFecha", //es el nombre que se le dio al text del objeto "calendario"
          false, //condicion para que muestre o no el mensaje de confirmacion
          "", //mensaje de confirmacion
          "", //aparece si la seleccionada fecha es menor a la de hoy
          "return true;")%>
            //verifica la fecha de revision con la fecha de hoy
  <%= indicador.checaFecha(
          "checaFechaRev",
          "form1",
          "dteFechaPrimeraRevision", //es el nombre que se le dio al text del objeto "calendario"
          false, //condicion para que muestre o no el mensaje de confirmacion
          "", //mensaje de confirmacion
          "", //aparece si la seleccionada fecha es menor a la de hoy
          "return true;")%>
            //Se agregaron mas tags
    function parameters(){
        copiausuarios();
        //validacion actualizacion del indicador. Se agregaron mas tags
  <%if (indicador.getStrEstado().equals("editar")) {%>
      document.form1.actionType.value = 'updateIndicador';
      document.form1.msgAction.value = '<%=tags.getString("indicadores.indicadorhandle.IndicadorActualizado")%>';
  <%} else {%>
  <%-- AQUI TENGO QUE REGRESARLE A INSERTA INDICADOR --%>
      //TODO DESCOMENTAR LA FUNCION DE VALIDACION Y REGRESAR AQUI A INSERTAR
      document.form1.actionType.value = 'insertaIndicador';
      document.form1.msgAction.value = '<%=tags.getString("indicadores.indicadorhandle.IndicadorAlta")%>';
  <%}%>
      document.form1.className.value = 'isoblock.indicadores.Indicador';
      document.form1.sectionName.value ='Indicadores';
      document.form1.editPage.value = '../view/v.meter.list.view';
      document.form1.addPage.value = '../view/v.indicadores.view';
      document.form1.msgButton1.value = '<%=tags.getString("boton.Alta")%>';
      document.form1.msgButton2.value = '<%=tags.getString("boton.Control")%>';
      document.form1.action="../view/v.common.actional.view"
      showLoader();
      document.form1.submit();
    }
    function codigoPeriodicidadRevision(){
      var opcionSeleccionada = document.form1.tipoPeriodicidadRevision[document.form1.tipoPeriodicidadRevision.selectedIndex].value;
      var sHTML = "";
      if(opcionSeleccionada == "daily"){
  <%-- El value de el radio opcion se debe de dejar igual para evitar problemas con los meses --%>
        sHTML += "<input type='radio' name='vchOpcionRevision' value='diaDelMes' checked='checked'><%=tags.getString("indicadores.indicadorhandle.Cada")%> <input type='text' name='intSeparacionRevision' value='<%= indicador.getIntSeparacionRevision()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.dias")%>";
      }
      if(opcionSeleccionada == "weekly"){
  <%-- El value de el radio opcion se debe de dejar igual para evitar problemas con los meses --%>
        sHTML += "<input type='radio' name='vchOpcionRevision' value='diaDelMes' checked='checked'><%=tags.getString("indicadores.indicadorhandle.Cada")%> <input type='text' name='intSeparacionRevision' value='<%= indicador.getIntSeparacionRevision()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.semanaslos")%> ";
        sHTML += "<br>";
        sHTML += "<label><input type='checkbox' name='boolDomingoRevision' value='true' <% if (indicador.getBoolDomingoRevision().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Domingo")%></label>&nbsp;";
        sHTML += "<label><input type='checkbox' name='boolLunesRevision' value='true' <% if (indicador.getBoolLunesRevision().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Lunes")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='boolMartesRevision' value='true' <% if (indicador.getBoolMartesRevision().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Martes")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='boolMiercolesRevision' value='true' <% if (indicador.getBoolMiercolesRevision().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Miercoles")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='boolJuevesRevision' value='true' <% if (indicador.getBoolJuevesRevision().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Jueves")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='boolViernesRevision' value='true' <% if (indicador.getBoolViernesRevision().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Viernes")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='boolSabadoRevision' value='true' <% if (indicador.getBoolSabadoRevision().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Sabado")%></label> &nbsp;";
      }
      if(opcionSeleccionada == "monthly"){
  <%-- Este campo oculto es importante, ya que al tener dos campos que deberia llamarse separacion, se renombraron como separacion 1 y 2
  y al final se calcula cual de los dos campos le otorga su valor al campo separacion --%>
        sHTML += "<input type='hidden' name='intSeparacionRevision' value='<%= indicador.getIntSeparacionRevision()%>'>";
        sHTML += "<input type='radio' name='vchOpcionRevision' value='diaDelMes' <% if (indicador.getVchOpcionRevision().equals("diaDelMes")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Eldia")%> <input type='text' name='intDiaRevision' value='<%= indicador.getIntDiaRevision()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.decada")%> <input type='text' name='separacion1_revision' value='<%= indicador.getIntSeparacionRevision()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.mes(es)")%>";
        sHTML += "<br>";
        sHTML += "<input type='radio' name='vchOpcionRevision' value='diaDeLaSemana' <% if (indicador.getVchOpcionRevision().equals("diaDeLaSemana")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.El")%> &nbsp;";
        sHTML += "<select name='intIndiceSemanalRevision'>";
        sHTML += "    <option value='1' <% if (indicador.getIntIndiceSemanalRevision().equals("1")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Primer")%></option>";
        sHTML += "    <option value='2' <% if (indicador.getIntIndiceSemanalRevision().equals("2")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Segundo")%></option>";
        sHTML += "    <option value='3' <% if (indicador.getIntIndiceSemanalRevision().equals("3")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Tercer")%></option>";
        sHTML += "    <option value='4' <% if (indicador.getIntIndiceSemanalRevision().equals("4")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Cuarto")%></option>";
        sHTML += "    <option value='5' <% if (indicador.getIntIndiceSemanalRevision().equals("5")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Ultimo")%></option>";
        sHTML += "</select>";
        sHTML += "&nbsp;";
        sHTML += "<select name='intDiaDeLaSemanaRevision'>";
        sHTML += "    <option value='2' <% if (indicador.getIntDiaDeLaSemanaRevision().equals("2")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Lunes")%></option>";
        sHTML += "    <option value='3' <% if (indicador.getIntDiaDeLaSemanaRevision().equals("3")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Martes")%></option>";
        sHTML += "    <option value='4' <% if (indicador.getIntDiaDeLaSemanaRevision().equals("4")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Miercoles")%></option>";
        sHTML += "    <option value='5' <% if (indicador.getIntDiaDeLaSemanaRevision().equals("5")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Jueves")%></option>";
        sHTML += "    <option value='6' <% if (indicador.getIntDiaDeLaSemanaRevision().equals("6")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Viernes")%></option>";
        sHTML += "    <option value='7' <% if (indicador.getIntDiaDeLaSemanaRevision().equals("7")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Sabado")%></option>";
        sHTML += "    <option value='1' <% if (indicador.getIntDiaDeLaSemanaRevision().equals("1")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Domingo")%></option>";
        sHTML += "</select>";
        sHTML += "&nbsp; <%=tags.getString("indicadores.indicadorhandle.decada")%> <input type='text' name='separacion2_revision' value='<%= indicador.getIntSeparacionRevision()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.mes(es)")%>.";
      }
      periodicidadRevision.innerHTML = sHTML;
    }
    function codigoPeriodicidadIndicador(){
      var opcionSeleccionada = document.form1.tipoPeriodicidad[document.form1.tipoPeriodicidad.selectedIndex].value;
      var sHTML = "";
      if(opcionSeleccionada == "daily"){
  <%-- El value de el radio opcion se debe de dejar igual para evitar problemas con los meses --%>
        sHTML += "<input type='radio' name='opcion' value='diaDelMes' checked='checked'><%=tags.getString("indicadores.indicadorhandle.Cada")%> <input type='text' name='separacion' value='<%= indicador.getSeparacion()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.dias")%>";
      }
      if(opcionSeleccionada == "weekly"){
  <%-- El value de el radio opcion se debe de dejar igual para evitar problemas con los meses --%>
        sHTML += "<label><input type='radio' name='opcion' value='diaDelMes' checked='checked'><%=tags.getString("indicadores.indicadorhandle.Cada")%> <input type='text' name='separacion' value='<%= indicador.getSeparacion()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.semanaslos")%> ";
        sHTML += "<br>";
        sHTML += "<label><input type='checkbox' name='domingo' value='true' <% if (indicador.getDomingo().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Domingo")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='lunes' value='true' <% if (indicador.getLunes().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Lunes")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='martes' value='true' <% if (indicador.getMartes().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Martes")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='miercoles' value='true' <% if (indicador.getMiercoles().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Miercoles")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='jueves' value='true' <% if (indicador.getJueves().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Jueves")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='viernes' value='true' <% if (indicador.getViernes().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Viernes")%></label> &nbsp;";
        sHTML += "<label><input type='checkbox' name='sabado' value='true' <% if (indicador.getSabado().equals("true")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Sabado")%></label> &nbsp;";
      }
      if(opcionSeleccionada == "monthly"){
  <%-- Este campo oculto es importante, ya que al tener dos campos que deberia llamarse separacion, se renombraron como separacion 1 y 2
  y al final se calcula cual de los dos campos le otorga su valor al campo separacion --%>
        sHTML += "<input type='hidden' name='separacion' value='<%= indicador.getSeparacion()%>'>";
        sHTML += "<input type='radio' name='opcion' value='diaDelMes' <% if (indicador.getOpcion().equals("diaDelMes")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.Eldia")%> <input type='text' name='dia' value='<%= indicador.getDia()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.decada")%> <input type='text' name='separacion1' value='<%= indicador.getSeparacion()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.mes(es)")%>";
        sHTML += "<br>";
        sHTML += "<input type='radio' name='opcion' value='diaDeLaSemana' <% if (indicador.getOpcion().equals("diaDeLaSemana")) {%> checked <% }%> /><%=tags.getString("indicadores.indicadorhandle.El")%> &nbsp;";
        sHTML += "<select name='indiceSemanal'>";
        sHTML += "    <option value='1' <% if (indicador.getIndiceSemanal().equals("1")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Primer")%></option>";
        sHTML += "    <option value='2' <% if (indicador.getIndiceSemanal().equals("2")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Segundo")%></option>";
        sHTML += "    <option value='3' <% if (indicador.getIndiceSemanal().equals("3")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Tercer")%></option>";
        sHTML += "    <option value='4' <% if (indicador.getIndiceSemanal().equals("4")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Cuarto")%></option>";
        sHTML += "    <option value='5' <% if (indicador.getIndiceSemanal().equals("5")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Ultimo")%></option>";
        sHTML += "</select>";
        sHTML += "&nbsp;";
        sHTML += "<select name='diaDeLaSemana'>";
        sHTML += "    <option value='2' <% if (indicador.getDiaDeLaSemana().equals("2")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Lunes")%></option>";
        sHTML += "    <option value='3' <% if (indicador.getDiaDeLaSemana().equals("3")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Martes")%></option>";
        sHTML += "    <option value='4' <% if (indicador.getDiaDeLaSemana().equals("4")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Miercoles")%></option>";
        sHTML += "    <option value='5' <% if (indicador.getDiaDeLaSemana().equals("5")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Jueves")%></option>";
        sHTML += "    <option value='6' <% if (indicador.getDiaDeLaSemana().equals("6")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Viernes")%></option>";
        sHTML += "    <option value='7' <% if (indicador.getDiaDeLaSemana().equals("7")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Sabado")%></option>";
        sHTML += "    <option value='1' <% if (indicador.getDiaDeLaSemana().equals("1")) {%> selected <% }%> /><%=tags.getString("indicadores.indicadorhandle.Domingo")%></option>";
        sHTML += "</select>";
        sHTML += "&nbsp; <%=tags.getString("indicadores.indicadorhandle.decada")%> <input type='text' name='separacion2' value='<%= indicador.getSeparacion()%>' onkeypress='validaNumeros()' size='3' maxlength='2' /> <%=tags.getString("indicadores.indicadorhandle.mes(es)")%>.";
      }
      periodicidad.innerHTML = sHTML;
    }
    function verResponsables() {
      block('tr_carreras');
      block('tr_responsables');
    }
    
    function cancelar (){
        dialog("<%=tags.getString("indicadores.indicadorhandle.Cancelar",false)%>", Validate.yes, Validate.no)
        .then(function(){window.location.href='../view/v.meter.list.view';});
      }
    
    function limpiarCampos (){
        dialog("<%=tags.getString("indicadores.indicadorhandle.limpiarCampos")%>", Validate.yes, Validate.no)
        .then(function(){window.location.href='../view/v.indicadores.view';});
    }
    
</script>
<%@include file="../../components/loader.jsp" %>
<form name="form1" method="post">
  <%=header(tags.getString("indicadores.indicadorhandle.INDICADORES"))%>
  <table width="650" border="0">
    <tr>
      <td align="right" style="width: 214px;" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorTitulo")%></td>
      <td>
          <textarea id="vchTitulo" name="vchTitulo" cols="45" rows="5" maxlength="255"><%= indicador.getVchTitulo()%></textarea>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorObjetivo")%></td>
      <td>
          <select name="intObjetivoId" style="width: 339px">
            <option value="-1"><%=tags.getString("indicadores.indicadorhandle.IndicadorSelecciona")%></option>
            <%= indicador.comboTableExtra("tblobjetivo", "intobjetivoid", "vchtitulo", indicador.getIntObjetivoId(),"int_estado = 1")%>
          </select>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorFacultad")%></td>
      <td>
        <select name="intFacultadId" id="intFacultadId" onchange="reloadpage()" style="width: 339px">
          <option value="-1"><%=tags.getString("indicadores.indicadorhandle.IndicadorSelecciona")%></option>
          <% IBusinessUnitDAO planta = Utilities.getBean(IBusinessUnitDAO.class); 
           %>
          <%= planta.getBusinessUnitByUser(loggedUserId.toString(),indicador.getIntFacultadId().toString(),isAdmin) %>
        </select>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorDepartamento")%></td>
      <td>
        <select name="intUbicacionId" id="intUbicacionId" onchange="reloadpage()" style="width: 339px">
          <option value="-1"><%=tags.getString("indicadores.indicadorhandle.IndicadorSelecciona")%></option>
          <% IDepartmentDAO departamento = Utilities.getBean(IDepartmentDAO.class); 
           %>
           <%= departamento.getDepartmentByBusinessUnitByUserSelect(loggedUserId.toString(),indicador.getIntFacultadId().toString(), indicador.getIntUbicacionId().toString(), isAdmin) %>
        </select>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorProceso")%></td>
      <td>
        <select name="intAreaId" id="intAreaId" style="width: 339px">
          <option value="-1"><%=tags.getString("indicadores.indicadorhandle.IndicadorSelecciona")%></option>
          <% IProcessDAO proceso = Utilities.getBean(IProcessDAO.class); 
           %>
           <%= proceso.getProcessByDepartmentByUne(loggedUserId.toString(), indicador.getIntUbicacionId().toString(), indicador.getIntAreaId(), isAdmin) %>
        </select>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorFuenteInformacion")%></td>
      <td>
        <input type="text" name="vchFuenteInformacion" value="<%= indicador.getVchFuenteInformacion()%>" class="input" maxlength="255" size="52">
      </td>
    </tr>
    <tr style="display: none;">
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorEstatus")%>
      <td>
        <input type="hidden" name="intStatusIndicador" value="<%=indicador.getIntStatusIndicador()%>"></td>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorFormula")%></td>
      <td>
        <textarea name="txtFormula" cols="45" rows="5"><%= indicador.getTxtFormula()%></textarea>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorMeta")%></td>
      <td>
        <select name="vchMinMax">
          <option value="min" <% if ((indicador.getVchMinMax()).equals("min")) {%> selected="selected" <% }%>><%=tags.getString("indicadores.indicadorhandle.IndicadorMinimo")%></option>
          <option value="max"<% if ((indicador.getVchMinMax()).equals("max")) {%> selected="selected" <% }%>><%=tags.getString("indicadores.indicadorhandle.IndicadorMaximo")%></option>
        </select>
        <input type="text" name="intMeta" value="<%= indicador.getIntMeta()%>" class="input" maxlength="10" size="10" onkeypress="validaNumeros()">
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorDescripcionMeta")%></td>
      <td>
        <textarea name="txtMeta" cols="45" rows="5"><%= indicador.getTxtMeta()%></textarea>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorPeriodicidad")%></td>
      <td>
          <fieldset style="width:94%" name="fieldsetPeriodicidad">
          <select name="tipoPeriodicidad" onChange="codigoPeriodicidadIndicador()">
            <option value="daily" <% if (indicador.getTipoPeriodicidad().equals("daily")) {%> selected <% }%> ><%=tags.getString("indicadores.indicadorhandle.IndicadorPorDias")%></option>
            <option value="weekly" <% if (indicador.getTipoPeriodicidad().equals("weekly")) {%> selected <% }%> ><%=tags.getString("indicadores.indicadorhandle.IndicadorPorSemanas")%></option>
            <option value="monthly" <% if (indicador.getTipoPeriodicidad().equals("monthly")) {%> selected <% }%> ><%=tags.getString("indicadores.indicadorhandle.IndicadorPorMes")%></option>
          </select>
          <br>
          <div id="periodicidad"></div>
          <style>
              input[name = separacion] {
                  margin-left: 10px;
              }
              input[name = intSeparacionRevision] {
                  margin-left: 10px;
              }
          </style>
          </fieldset>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorPrimerLevantamiento")%></td>
      <td>
        <script>calendario.writeControl()</script>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorDiasAnticipacionProgramar")%></td>
      <td>
        <input type="text" name="intDiasAnticipacion" value="<%= indicador.getIntDiasAnticipacion()%>" class="input" maxlength="2" size="13" onkeypress="validaNumeros()">
        <input type=hidden name='intDiasAnticipacionRevision' value="<%= indicador.getIntDiasAnticipacion()%>">
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorDptoEncargadoRevisiones")%></td>
      <td>
        <select name="intUbicacionMonitoreoId" >
          <option value="-1"><%=tags.getString("indicadores.indicadorhandle.IndicadorSelecciona")%></option>
          <%= indicador.comboUbicacionByLoggedUser(indicador.getIntUbicacionMonitoreoId(),loggedUserId, new mx.bnext.access.ProfileServices[]{},isAdmin, "intFacultadId = " + indicador.getIntFacultadId())%>
        </select>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.Formadecalculo")%></td>
      <td>
        <select name="vchTipoAgrupamiento">
          <% if (indicador.getVchTipoAgrupamiento().equals("sumatoria")) {%>
          <option value="sumatoria" selected="selected"><%=tags.getString("indicadores.indicadorhandle.IndicadorPorSumatoria")%></option>
          <option value="promedio"><%=tags.getString("indicadores.indicadorhandle.IndicadorPorPromedio")%></option>
          <% } else {%>
          <option value="sumatoria"><%=tags.getString("indicadores.indicadorhandle.IndicadorPorSumatoria")%></option>
          <option value="promedio" selected="selected"><%=tags.getString("indicadores.indicadorhandle.IndicadorPorPromedio")%></option>
          <% }%>
        </select>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorPeriodicidadRevision")%></td>
      <td>
          <fieldset style="width:94%" name="fieldsetPeriodicidadRevision">
          <select name="tipoPeriodicidadRevision" onChange="codigoPeriodicidadRevision()">
            <option value="daily" <% if (indicador.getTipoPeriodicidadRevision().equals("daily")) {%> selected <% }%> ><%=tags.getString("indicadores.indicadorhandle.IndicadorDiaria")%></option>
            <option value="weekly" <% if (indicador.getTipoPeriodicidadRevision().equals("weekly")) {%> selected <% }%> ><%=tags.getString("indicadores.indicadorhandle.IndicadorSemanal")%></option>
            <option value="monthly" <% if (indicador.getTipoPeriodicidadRevision().equals("monthly")) {%> selected <% }%> ><%=tags.getString("indicadores.indicadorhandle.IndicadorMensual")%></option>
          </select>
          <br>
          <div id="periodicidadRevision"></div>
        </fieldset>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorFechaPrimeraRevision")%></td>
      <td>
        <script>calendarioRevision.writeControl();</script>
      </td>
    </tr>
    <tr>
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.Responsabledellenado")%></td>
      <td>
          <%if (!indicador.getIntIndicadorId().equals("0") && indicador.getIntStatusIndicador().equals("1")) {%>
        <b>
          <%if (indicador.getIntReportarPor().equals("0")) {%>
            <%=tags.getString("indicadores.indicadorhandle.Usuarios")%>
          <%} else {%>
            <%=tags.getString("indicadores.indicadorhandle.IndicadorUnidad")%>
          <%}%>
        </b>
        <input type="hidden" name="intReportarPor" id="intReportarPor" value="<%=indicador.getIntReportarPor()%>">
        <%} else {%>
        <select name="intReportarPor" id="intReportarPor" onchange="verResponsables();">
          <%=indicador.comboReportarPor()%>
        </select>
        <%}%>
      </td>
    </tr>
    <tr id="tr_responsables" name="tr_responsables">
      <td align="right" class="label"><%=tags.getString("indicadores.indicadorhandle.IndicadorResponsableReportar")%></td>
      <td>
        <%
        indicador.sesionusuarioid = sesionusuarioid;
        indicador.setAdmin(isAdmin); 
        %> 
        <%= indicador.moveUserCmpt("arrayUsuarios", "list_izq_Usuarios", "arrayResponsables", "intAreaIdListaUsuarios", 
                indicador.getArrayUsuarios(), indicador.getList_izq_Usuarios(), indicador.getArrayResponsables(), 
                indicador.getIntAreaIdListaUsuarios(), 
                "1 IN (p.perfil.intBLectorIndicador,p.perfil.intBEditorIndicador,p.perfil.intBEncargadoIndicador)", 
                "reloadpage();", isAdmin, loggedUserId, 
                //Utilities.getActiveServices("intBLectorIndicador,intBEditorIndicador,intBEncargadoIndicador"))
                Utilities.getActiveServices("*"),
                indicador.getIntFacultadId())
        %>    
      </td>
    </tr>
    <!-- Selecciona las carreras -->
    <%logger.debug("indicadorhandler.jsp indicador.getIntUbicacionCarrreraId(): " + indicador.getIntUbicacionCarrreraId());%>
    <tr id="tr_carreras" name="tr_carreras" style="display:none">
      <td align=right class="label" valign="middle"><%=tags.getString("indicadores.indicadorhandle.IndicadorUnidadesReportar")%></td>
      <td>
        <%=indicador.controlListaCarrera("arrayCarreras", "list_izq_Carrera", "list_der_Carrera", "intUbicacionCarrreraId", indicador.getArrayCarreras(), indicador.getList_izq_Carreras(), indicador.getList_der_Carreras(), indicador.getIntUbicacionCarrreraId(), "reloadpage();", "", " intfacultadid = " + indicador.getIntFacultadId(), indicador.getUbicacionIdFromArea())%>
      </td>
    </tr>
    <script>
      if(document.getElementById('intReportarPor').value == "0") {
        document.getElementById('tr_responsables').style.display = "";
        document.getElementById('tr_carreras').style.display = "none";
      } else {
        document.getElementById('tr_responsables').style.display = "none";
        document.getElementById('tr_carreras').style.display = "";
      }
    </script>
    <tr><td colspan="2">&nbsp;</td></tr>
    <tr>
      <td align="center" colspan="2">
        <%if (indicador.getStrEstado().equals("editar") || indicador.getStrEstado().equals("ver")) {%>
        <input type="button" class="button" style="display: <%= indicador.getDeleted().equals("1") ? "none" : ""%>" name="instAdd" value="<%=tags.getString("boton.Aceptar")%>" onClick="validateSubmit();">
        <input type="button" class="button" name="instBack" value="<%=tags.getString("boton.Regresar")%>" onClick="history.back();">
        <%} else {%>
        <input type="button" class="button" name="instAdd" value="<%=tags.getString("boton.Aceptar")%>" onClick="validateSubmit();">
        <input type="button" class="button" name="back" value="<%=tags.getString("boton.Cancelar")%>" onClick="cancelar();">
        <input type="button" class="button" name="instClear" value="<%=tags.getString("boton.LimpiarForma")%>" onClick="limpiarCampos()">
        <%}%>
      </td>
    </tr>
  </table>
  <input type="hidden" id="strEstado" name="strEstado" value="<%= indicador.getStrEstado()%>">
  <input type="hidden" id="intIndicadorId" name="intIndicadorId" value="<%= indicador.getIntIndicadorId()%>">
  <input type="hidden" name="reload" value="1">
  <input type=hidden name='actionType' value=''>
  <input type=hidden name='className' value=''>
  <input type=hidden name='editPage' value=''>
  <input type=hidden name='addPage' value=''>
  <input type=hidden name='msgAction' value=''>
  <input type=hidden name='msgButton1' value=''>
  <input type=hidden name='msgButton2' value=''>
  <input type=hidden name='sectionName' value=''>
  <%if (!indicador.getIntIndicadorId().equals("0")) {%>
  <script>
    document.form1.dteFechaPrimeraRevision.readonly = true;
    document.form1.dteFecha.readonly = true;
    document.form1.btnDateRevision.disabled = true;
    document.form1.btnDate.disabled = true;
  </script>
  <%}%>
  <% if (indicador.getStrEstado().equals("ver")) {%>
  <script>
      document.form1.vchTitulo.disabled = true;
      document.form1.intObjetivoId.disabled = true;
      document.form1.intFacultadId.disabled = true;
      document.form1.intUbicacionId.disabled = true;
      document.form1.intAreaId.disabled = true;
      document.form1.vchFuenteInformacion.disabled = true;
      document.form1.txtFormula.disabled = true;
      document.form1.vchMinMax.disabled = true;
      document.form1.intMeta.disabled = true;
      document.form1.txtMeta.disabled = true;
      document.form1.btnDate.hidden = true;
      document.form1.intDiasAnticipacion.disabled = true;
      document.form1.intUbicacionMonitoreoId.disabled = true;
      document.form1.vchTipoAgrupamiento.disabled = true;
      document.form1.btnDateRevision.hidden = true; 
      document.form1.arrayResponsables.disabled = true;
      document.form1.intAreaIdListaUsuarios.disabled = true;
      document.form1.list_izq_Usuarios.disabled = true;
      document.form1.intUbicacionCarrreraId.disabled = true;    
      document.form1.fieldsetPeriodicidad.disabled = true;
      document.form1.fieldsetPeriodicidadRevision.disabled = true;
  </script>
  <%}%>
  <%=footer()%>
  <%@ include file="../../includes/mensajeespera.txt"%>
</form>
<%try {
    indicador.finalize();
  } catch (Throwable t) {
    t.printStackTrace();
  }%>
<%--  Finaliza el cuerpo del JSP  --%>
<%@ include file="../../templates/inferior.tem"%>