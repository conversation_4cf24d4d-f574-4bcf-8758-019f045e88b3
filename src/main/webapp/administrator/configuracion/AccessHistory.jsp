<!DOCTYPE html>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8"); %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Historial de accesos</title>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <jsp:include page="../../components/requiredScripts.jsp" />
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <script type='text/javascript' src="../controller/c.access.history.controller?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="bnext">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <div class ="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="window_title">
                                </h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                            <div class="button-component" id="newRecordButton">
     
                            </div>
                        </div>
                        <div class="grid-container">
                            <table id="dataGrid"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" id="geolocationAccessHistory" value="${geolocationAccessHistory}"/>
        <%@include file="../../components/sessionVariables.jsp" %>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>
