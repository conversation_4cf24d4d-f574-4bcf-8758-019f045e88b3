#breakDate {
    width: 11.25rem!important;
}

.generalBreakDate{
    color: #3e95e0;
    font-style: italic;
}

.userBreakDate {
    color: #2f8f51;
}
.actionButtons.selectorSeparator {
    float: left;
    margin-top: -00.375rem;
}

.actionButtons li{
    display:inline-block;
}

.controlArea{
    width: 45.813rem!important; 
    background: #FFF!important;
}

.controlArea li {
    float: left;
}

.controlArea .separator {
    border-top-color: #DDD;
    padding-top: 0px!important;
    display:block; float:left;
    margin-top: 0px;
}

.bnext .dijitArrowButtonInner {
    background:url("../../../scripts/framework/dijit/themes/bnext/images/spriteArrows.png") no-repeat scroll 0 center!important;
    width: 0.438rem!important;
    height: 0.438rem!important;
    margin: 0 0.25rem 0 0.25rem!important;
}

.dijitCalendarContainer table {
    clear: both!important;
}

.actionButtons .selector {
    display: inline-block;
    text-align: center;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem 0.125rem 0px 0.125rem;
    background: #F0F0F0;
    -moz-border-radius: 0.188rem;
    -webkit-border-radius: 0.188rem;
    border-radius: 0.188rem;
    cursor: pointer;
    color: #000;
    border: 1px solid #000;
}
.actionButtons .selector:hover{background: #CCC;}
.actionButtons .selector span{line-height:1rem;}
#actionButtonsId div {
    display: inline-block;
    float: none;
}
#actionButtonsId div {
    display: inline-block;
    float: none;
}
#actionButtonsId li{
    float: none;
}
.controlArea.controlAreaGBDC ul li {
    clear: both;
}
.controlArea ul li label{
    width:9.375rem;
}
.bnext .dijitDialogPaneContent {
    width: 100%!important;
}
#frmGeneralBreakCalendar .content_area, 
#frmGeneralBreakCalendar .content_area li, 
#periodicityPickerForm .controlArea {
    padding-bottom: 0px;
}

#periodicityPickerForm .controlArea label {    
    width: auto;
}
#periodicityPickerForm .controlArea label.error {
    top: 2.85rem!important;
}

#frmGeneralBreakCalendar .separator,
#divButtonsAcceptClose .separator,
#idDivButtonBreakDate .separator {
    padding: 0 0 !important;
}
#idDivUserListPP {
    margin-top: 0.8rem;
}
#divButtonsAcceptClose .content_area .actionButtons {
    text-align: right;
}
.radioListUsers{
    margin-left: 1.25rem;
}