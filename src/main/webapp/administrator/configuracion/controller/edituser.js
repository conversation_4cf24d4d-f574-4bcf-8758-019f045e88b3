require([
    'core', 'dojo/on', 'dojo/dom', 'bnext/callMethod', 'dojo/Deferred', 
    'bnext/angularNavigator', 'dojo/dom-construct', 'bnext/gridCubes',
    'dojo/dom-class', 'dojo/query', 'dojo/_base/lang',
    'bnext/module/ProfileFilePicker',
    'bnext/angularFabMenu',
    'dojo/domReady!'],
function (core, on, dom, callMethod, Deferred, angularNavigator, domConstruct, gridCubes, domClass, query, lang, ProfileFilePicker, angularFabMenu) {
    function doEverything(i18n) {
        
        dom.byId('avatar-picture').src = './../view/v-application-avatar.view?t=' + new Date().getTime();
        
        function uploadAvatarPicture() {
            new ProfileFilePicker({
                useDownloadIcon: false,
                modern: true,
                uploadPath: '../DPMS/UploadImage.fileUploader',
                accept: 'image/*',
                i18n: {
                    restartPage: i18n.restartPage,
                    uploadFailed: i18n.uploadFailed,
                    deleteFailed: i18n.deleteFailed
                },
                downloadAction: '../view/v-download.view'
            }, 'filePicker');
        } 
        
        uploadAvatarPicture();
 
        on(dom.byId('button-close'), 'click', function () {
            domClass.remove(dom.byId('avatar-selector-container'), 'show-avatar-container');
            domClass.add(dom.byId('avatar-selector-container'), 'hide-avatar-container');
        });
        
        on(dom.byId('button-refresh'), 'click', function () {
            parent.window.location.reload(true);
        });
        
        var isAwayDom = dom.byId('isAway');
        if (isAwayDom) {
            on(isAwayDom, 'change', function (e) {
                refreshAwayVisibleDom(e.target.value === '1');
            });
        }
        var changeAwayReplacementDom = dom.byId('changeAwayReplacement');
        if (changeAwayReplacementDom) {
            on(changeAwayReplacementDom, 'change', function (e) {
                // Muetra y oculta la columna de analistas
                query('.substitute-column').forEach(function (domNode) {
                    toggleHidden(domNode, e.target.value === '1');
                });
            });
        }
        var claveUsuario = dom.byId("claveUsuario").value,
            data = {}, 
            message = i18n.exit_operation;
        
        var cancel = function () {
            core.dialog(i18n.cancel_message, core.i18n.yes, core.i18n.no).then(function(){
                window.location.href = '../view/v.pendings.view';
            });
        };
        
        if (dom.byId('isAway')) {
            var isAway = dom.byId('isAway').value === '1';
            refreshAwayVisibleDom(isAway);
            query('.current-substitute').forEach(function(domNode) {
                if (!domNode.innerText.trim()) {
                    domClass.add(domNode, 'hidden');
                }
            });
            query('.department-registry-status-0').forEach(function(domNode) {
                domConstruct.create('img', {
                    src: require.toUrl("bnext/images/" + gridCubes.gray),
                    title: i18n.reasonToShowInactive
                }, domNode);
            });
            query('.department-registry-status-1').forEach(function(domNode) {
                domConstruct.create('img', {
                    src: require.toUrl("bnext/images/" + gridCubes.green),
                    title: i18n.isActive
                }, domNode);
            });
            if (isAway) {
                // En caso de entrar y que el usuario ya está "ausente"
                query('.away-selects').forEach(function(domNode) {
                    domClass.add(domNode, 'small-6');
                    domClass.remove(domNode, 'hidden');
                    domClass.remove(domNode, 'small-12');
                });
                query('.substitute-column').forEach(function(domNode) {
                    domClass.add(domNode, 'hidden');
                });
            }
        }
        
        function refreshAwayVisibleDom(isAway) {
            query('.show-when-is-away').forEach(function(domNode) {
                toggleHidden(domNode, isAway);
            });
        }
        function toggleHidden(domNode, isVisible) {
            if (isVisible) {
                domClass.remove(domNode, 'hidden');
            } else {
                domClass.add(domNode, 'hidden');
            }
        }
        function selectLanguage() {
            $('#language option[value="' + $('#txtlanguage').val() + '"]').prop('selected', true);
            $('label[for="language"]').text(i18n.idioma);
            var systemLanguage = $('#defaultSystemLanguage').val();
            var defaultOption = $('#language option[value=""]');
            defaultOption.append(' - ' + (i18n[systemLanguage] || systemLanguage));
        };
        
        function save() {
            var def = new Deferred();
            var language = dom.byId('language').value;
            var langLocale = language.split('-');
            data['id'] = dom.byId('intUsuarioId').value;
            data['lang'] = langLocale[0];
            data['locale'] = langLocale[1];
            data['description'] = dom.byId('description').value;
            data['correo'] = dom.byId('correo').value;
            data['gridSize'] = dom.byId('gridSize').value;
            data['detailGridSize'] = dom.byId('detailGridSize').value;
            data['floatingGridSize'] = dom.byId('floatingGridSize').value;
            data['legacyOpenFilters'] = dom.byId('legacyOpenFilter').value;
            if(dom.byId('searchInSubfolders')){
                data['searchInSubfolders'] = dom.byId('searchInSubfolders').value;
            }
            data['showExternalDialog'] = dom.byId('showExternalDialog').value;
            data['showWelcomeDialog'] = dom.byId('showWelcomeDialog').value;
            data['timezone'] = dom.byId('timezones').value;
            // is away?
            if (dom.byId('isAway')) {
                data.isAway = dom.byId('isAway').value === '1';
                data.isAwayReason = dom.byId('isAwayReason').value || null;
                data.analystPerBusinessUnitDepartment = [];
                if (data.isAway) {
                    query('.analyst-select').forEach(function(select) {
                        data.analystPerBusinessUnitDepartment.push({
                            businessUnitDepartmentId: select.id.replace(/.+?([0-9]+)/g, '$1'),
                            analystUserId: select.value
                        });
                    });
                    data['changeAwayReplacement'] = dom.byId('changeAwayReplacement').value;
                }
            }
            if ($('#frmEditarCuenta').valid()) {
                core.notice({
                    message: core.i18n.confirm_message,
                    btn1Action: function() {
                        core.showLoader().then(function() {
                            callMethod({
                                url: "../DPMS/Settings.User.action",
                                method: "updateUserInfo",
                                params: [data]
                            }).then(function(gsh) {
                                core.hideLoader();
                                if (claveUsuario !== dom.byId('vchNombre').value) {
                                    message = i18n.exit_record;
                                    var logoutUrl = '../view/v-logout.view?user='+ encodeURIComponent(claveUsuario);
                                    core.dialog(message, core.i18n.accept).then(function(){
                                        window.top.location = logoutUrl;
                                    });
                                } else {
                                    core.dialog(message, core.i18n.accept).then(function(){
                                        window.top.location = '../qms/es/welcome';
                                    });
                                }
                                core.hideLoader();
                            }, function() {
                                core.hideLoader();
                                core.failure();
                            }); 
                        });
                    },
                    btn2Action: function() {
                        def.resolve();
                    }
                });
            }
        }
                
        function setValidation() {
            var validationObject = {
                rules: {
                    description: {
                        required: true,
                        maxlength: 255
                    },
                    vchNombre: {
                        required: true,
                        maxlength: 255
                    },
                    correo: {
                        required: true,
                        maxlength: 255,
                        email: true
                    },
                    gridSize: {
                        required: true
                    },
                    timezones: {
                        required: true
                    },
                    detailGridSize: {
                        required: true
                    },
                    floatingGridSize: {
                        required: true
                    }
                },
                messages: {
                    description: {
                        required: i18n.requiredNombre,
                        maxlength: i18n.maxlengthName
                    },
                    vchNombre: {
                        required: i18n.requiredCuenta,
                        maxlength: i18n.maxlengthName
                    },
                    correo: {
                        required: i18n.requiredCorreo,
                        email: i18n.validCorreo
                    },
                    gridSize: {
                        required: i18n.requiredgridSize
                    },
                    detailGridSize: {
                        required: i18n.requiredgridSize
                    },
                    floatingGridSize: {
                        required: i18n.requiredgridSize
                    }
                }
            };
            if (dom.byId('isAway')) {
                var awayRules = {
                    isAway: {
                        required: true
                    },
                    isAwayReason: {
                        required: true
                    }
                };
                var awayMessages = {
                    isAway: {
                        required: core.i18n.requiredField
                    },
                    isAwayReason: {
                        required: core.i18n.requiredField
                    },
                    analyst: {
                        required: core.i18n.requiredField
                    }
                };
                query('.analyst-select').forEach(function(select) {
                    awayRules[select.id] = {
                        required: true
                    };
                    awayMessages[select.id] = {
                        required: core.i18n.requiredField
                    };
                });
                lang.mixin(validationObject.rules, awayRules);
                lang.mixin(validationObject.messages, awayMessages);
            }
            $('#frmEditarCuenta').validate(validationObject);
        };
        function changePassword() {
            core.navigateLegacy('v-edit-user-password.view');
        }
        function openMenuFavorites() {
            angularNavigator.navigate('menu/menu-favorites');
        }
        function openMailSettingsMine() {
            angularNavigator.navigate('menu/mail-settings/mail-settings-mine');
        }
        
        function openAvatar() {
            domClass.remove(dom.byId('avatar-selector-container'), 'hide-avatar-container');
            domClass.add(dom.byId('avatar-selector-container'), 'show-avatar-container');
        }
        
        function selectTimezone() {
            
            var timezoneUser = dom.byId('txtTimezoneUser').value;
            var timezoneSelectDom = dom.byId('timezones');
            if (timezoneSelectDom) {
                timezoneSelectDom.value = timezoneUser;
            }
        }
        
        function toggleAction(option) {
            switch (option.value) {
                case 'changePassword':
                    changePassword();  
                break;
                case 'openFavorites':
                    openMenuFavorites();  
                break;
                case 'openMailSettingsMine':
                    openMailSettingsMine();  
                break;
                case 'openAvatar':
                    openAvatar();  
                break;
            }
        }
        function addFabMenu() {
           // Mostramos la sección de botones flotantes
            angularFabMenu.addFabMenu(
                [
                    { text: i18n.lblPassword, value: 'changePassword', iconName: 'lock', title: i18n.lblPassword },
                    { text: i18n.openMenuFavorites, value: 'openFavorites', iconName: 'favorite', title: i18n.openMenuFavorites },
                    { text: i18n.openMailSettingsMine, value: 'openMailSettingsMine', iconName: 'mail', title: i18n.openMailSettingsMine },
                    { text: i18n.avatarTitle, value: 'openAvatar', iconName: 'account_box', title: i18n.avatarTitle }
                ],
            );
            angularFabMenu.doneAction = function (result) {
                toggleAction(result);
            };
        }
        on(dom.byId('saveBtn'), 'click', save);
        on(dom.byId('cancelBtn'), 'click', cancel);
        selectLanguage();
        selectTimezone();
        setValidation();
        addFabMenu();
        core.hideLoader();
        
    }
    core.setLang('lang/configuracion/nls/edituser').then(doEverything);
});


