require([
    'dojo/on','core', 'dojo/string', 'bnext/gridComponent',
    'dojo/dom', 'bnext/gridComponentUtil', 'bnext/gridCubes', 'bnext/gridIcons', 
    'bnext/i18n!lang/configuracion/nls/configuration.facility',
    'dojo/domReady!'
],
function (
        on,core, string, gridComponent, 
        dom, gcUtil, gridCubes, gridIcons,
        i18nFacility
) {
    function doEverything(i18n) {
        
        var btnAdd = dom.byId("btnAdd");        
        on(btnAdd,"click",function() {
          core.navigateLegacy("v.template.handle.view")
        });   

        var grid, cType = gcUtil.cType, columns = [], statusList = [
            {name: i18n.statusLst.active, icon: gridCubes.green, value: 1}, // Green Cube - EXECUTED
            {name: i18n.statusLst.inactive, icon: gridCubes.gray, value: 0}
        ];
        function toggleStatus(id, code) {
            showLoader(i18n.messages.statusChangeMsg);
            dialogExecutioner(
                    string.substitute(i18n.messages.statusChangeMsg, {title: code}), core.i18n.Validate.yes, core.i18n.Validate.no,
                    "Template.action", "toggleStatus", [id],
                    core.i18n.Validate.edit_success, "", core.i18n.Validate.accept, grid);
            hideLoader();
        }
        function editRecord(id) {
            window.location.replace('v.template.handle.action?id=' + id);
        };
        var scopes = cType.TextMap([
             {name : i18n.colNameOrganizationUnit, value: 1}, 
             {name : i18nFacility.colNamefacility, value: 2}
         ]);
        gcUtil.column(columns)
            .push('status', i18n.colName.status, cType.FunctionImage(['id'], toggleStatus, statusList, true), null, '50px')
            .push('edit', i18n.colName.edit, cType.Function(['id'], editRecord, gridIcons.edit), null, '50px')
            .push('description', i18n.colName.title)
            .push('scope', i18n.colNameScope, scopes, null, '120px')
            .push('organizationalUnit.description', i18n.colNameOrganizationUnit, cType.Text(), 1, '150px')
            .push('businessUnit.description', i18nFacility.colNamefacility, cType.Text(), 1, '150px')
            .localizeRecord('../DPMS/OptTemplate-Localize.action', i18n, ['status'])
        ;
        grid = new gridComponent({
            size: dom.byId('gridSize').value,
            id: "grid",
            container: "dataGrid",
            searchContainer: "#auto",
            resultsInfo: "#auto",
            paginationInfo: "#auto",
            serviceStore: "OptTemplate.action",
            windowPath: dom.byId('windowPath').value,
            methodName: "getRows",
            columns: columns,
            logging_column: true
        });
        grid.setPageSize(dom.byId('gridSize').value);
        core.hideLoader();
    }
    core.setLang('lang/configuracion/nls/template.list').then(doEverything);
});