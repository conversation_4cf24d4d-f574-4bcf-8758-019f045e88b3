<%-- 
    Document   : break-calendar
    Created on : 24/10/2016, 10:45:54 AM
    Author     : <PERSON><PERSON><PERSON> @Block Networks
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html>
    <head>
        <title>Calendario de Suspensiones</title>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}">
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />       
        <%@include file="../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" href="../scripts/framework/bnext/styles/gridComponent.css?${systemVersion}" >
        <link rel="stylesheet" href="../administrator/configuracion/style/break-calendar.css?${systemVersion}" >  
        <script src="../administrator/configuracion/controller/break-calendar.js?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../components/loader.jsp" %>
        <form name="frmBreakCalendar" id="frmBreakCalendar" method="Post" action="">
            <div class="content_title">
                <h1 id="mainTitle"></h1>
            </div>
            <ul class="content_area" id="contenido" >
                <li>   
                    <div class="divMargin">
                        <div id="calendarId"></div>
                    </div>                    
                    <div class="divMargin">
                        <ul>
                            <li>
                                <div><div id="savedDayClass" class="recuadro"><label id="lblSavedDayClass"></label></div></div>
                                <div><div id="genericDayClass" class="recuadro"><label id="lblGenericDayClass"></label></div></div>
                                <div><div id="deletedDayCalendarClass" class="recuadro"><label id="lblDeletedDayCalendarClass"></label></div></div>
                                <div><div id="selectedDayCalendarClass" class="recuadro"><label id="lblSelectedDayCalendarClass"></label></div></div>
                            </li>
                        </ul>
                    </div>
                </li>                
                <li class ="separator">
                    <ul class="actionButtons" id="actionButtonsId">
                        <li>
                            <input data-dojo-attach-point="showPeriodicity" type="button" id="showPeriodicity" class ="Button"/>
                        </li>
                        <li>
                            <input data-dojo-attach-point="addDates" type="button" id="addDates" class ="Button"/>
                        </li>
                        <li>
                            <input type="button" id="showListDates" class ="Button"/>
                        </li>                        
                    </ul>
                </li>
                <li id="breakDates">
                    <ul>                    
                        <li class ="table_workarea">
                            <div id="divListDate" class="displayNone">
                                <table id="dataGrid"></table>
                            </div>
                        </li>                                    
                    </ul>              
                </li>                
            </ul>
             <%@include file="../../components/sessionVariables.jsp" %>
        </form>           
        <div id="periodicityPickerContainer" data-dojo-type="dijit/Dialog" data-dojo-id="periodicityPickerContainer"></div>        
        <%@include file="../../components/footer.jsp" %>
        <input type="hidden" id="admin" name="admin" value="${admin}"/>
        <input type="hidden" id="escalationManager" name="escalationManager" value="${escalationManager}"/>
    </body>
</html>
