<!DOCTYPE html>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<s:set var="corporative" value="corporative" />
<html class="html-reset">
    <head>
        <title>Template Handle</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" href="../scripts/framework/bnext/styles/gridComponent.css?${systemVersion}" >
        <%@include file="../../components/requiredScripts.jsp" %>
        <script type='text/javascript' src="c.template.handle.controller?${systemVersion}"></script>
        <s:set var="allowEdit" value="allowEdit"/>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <style>
            .lblDias {
            background: transparent!important;
            }
            .odd .chip {
                background-color: #DDD;
                text-align: center;
            }
            .odd .chip:hover{
                background: #bdbdbd;
            }
            .dojoDndHandle:hover {
                cursor: -webkit-grab;
                cursor: grab;
            }
            .dojoDndHandle:active {
                cursor: -webkit-grabbing;
                cursor: grabbing; 
            }
            .tableTemplateList {
                width: 100%;
            }
            #templateList > div.even > .contentOrder,
            #templateList > div.even > .contentName,
            #templateList > div.even > .contentResponseTime, 
            #templateList > div.even > .show-for-small-only {
                border-top: 1px #fff solid;
                border-right: 1px #fff solid;
                border-bottom: 1px #fff solid;
                background: #ddd;
            }
            #templateList > div.even > .contentOrder {
                border-left: 1px #fff solid;
            }
            #templateList > div.odd > .contentOrder,
            #templateList > div.odd > .contentName,
            #templateList > div.odd > .contentResponseTime,
            #templateList > div.odd > .show-for-small-only {
                border-top: 1px #ddd solid;
                border-right: 1px #ddd solid;
                border-bottom: 1px #ddd solid;
            }
            #templateList > div.odd > .contentOrder {
                border-left: 1px #ddd solid;
            }
            .dojoDndHandle {
                height: 100%;
                display: flex;
                justify-content: center;
                flex-direction: column;
            }
            #order, 
            #delay, 
            #evaluator {
                text-align: center;
            }
            #editAllowed {
                display:none;
            }
            input[type="number"]::-webkit-inner-spin-button {
                -webkit-appearance: none;
            }
            <s:if test="!allowEdit">
                #saveBtn,
                #cleanBtn,
                #contentType,
                #contentHandler,
                #contentPosition,
                #btnAdd,
                img {
                    display: none;
                }
                #editAllowed{
                    display:block;
                }
                .chip-remove-item {
                 display: none;   
                }
            </s:if>
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <div class="grid-container grid-floating-active">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons" method="POST" id="validate_form">
                        <%@include file="../../components/loader.jsp" %>
                        <div class="header grid-container">
                            <div class="grid-x">
                                <h3 class="cell igx-card-header__title content_title" id="mainTitle"></h3>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                            <div class="button-component" id="btnAdd">
                                <span class="material-icons">add</span>
                                <input class="fixed-button" type="button" id="add" value="add"/>
                            </div>
                        </div>
                        <div class="grid-container">
                            <div class="grid-x grid-padding-x" id="mainDiv">        
                                <div id="editAllowed" class="cell small-12">
                                    <div id="lblEditAllowed" class="controlArea"></div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="textarea-component">
                                        <input type="text" name="title" id="title" <s:if test="!allowEdit"> disabled </s:if> required="required" value=""></input>
                                        <label id="titleText"></label>
                                    </div>
                                </div>
                                <div class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select name="scope" id="scope" class="select" value="0" <s:if test="!allowEdit"> disabled </s:if>>
                                            <option value="">--Seleccione--</option>
                                            <option value="planta">Usuario</option>
                                            <s:if test="corporative">
                                                <option value="corp">Puesto</option>
                                            </s:if>
                                        </select>
                                        <label id="scopeLbl"></label>
                                    </div>
                                </div> 
                                <div id="UNE_CORPLi" style="display: none" class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select name="UNE_CORP" id="UNE_CORP" class="select required" <s:if test="!allowEdit"> disabled </s:if>>
                                        </select>
                                        <label id="UNE_CORPLbl">Asignar al departamento</label>
                                    </div>
                                </div>        
                                <div id="contentType" class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select name="type" id="type" class="select required">
                                            <option value="">-- Seleccione --</option>
                                            <option value="users">Usuario</option>
                                            <option value="JobPositions">Puesto</option>
                                            <!--option value="boss">Puesto</option-->
                                        </select>
                                        <label id="typeLabel"></label>
                                    </div>
                                </div> 
                                <div id="contentHandler" class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="handler" name="handler">
                                            <option value="">-- Seleccione --</option>
                                        </select>
                                        <label id="handlerTxt"></label>
                                    </div>
                                </div>
                                <div id="contentPosition" class="cell small-12 medium-6">
                                    <div class="select-component">
                                        <select id="position" name="position">
                                            <option value="-1">-- Al final --</option>
                                        </select>
                                        <label id="lblposition"></label>
                                    </div>
                                </div>
                                <div class="grid-container tableTemplateList ">
                                    <div class="cell small-12">&nbsp;</div>
                                    <div id="tableTemplateList" class="grid-x grid-padding-x hide-for-small-only">  
                                        <div class="cell small-12 medium-1">
                                            <div id="order"></div>
                                        </div>
                                        <div class="cell small-12 medium-9">
                                            <div id="evaluator"></div>
                                        </div>
                                        <div class="cell small-12 medium-2">
                                            <div id="delay"></div>
                                        </div>
                                    </div>
                                </div>
                                <div id="templateList" class="grid-container tableTemplateList "></div>
                                <div class="cell small-12 actionButtons">
                                    <%@include file="../../components/saveButtons.jsp" %>
                                </div>
                                <div class="cell small-12">&nbsp;</div>
                                <s:hidden name="status" id="status"/>
                                <jsp:include page="../../components/footer.jsp" />
                                <s:hidden name="systemColor" id="SYSTEM_COLOR" />
                                <s:hidden name="userId" id="SESSION_USER" /><%-- Mailisima practica!, este valor se puede obtener directo en el *.java con getLoggedUserId() --%>
                                <s:hidden name="configurationRole" id="SESSION_MODULE_ROLE" />
                                <s:hidden name="id" id="ID" />
                                <s:hidden name="windowName" id="windowName" value="department"/>
                                <s:hidden value="%{allowEdit}" name="allowEdit" id="allowEdit" />
                                <input type="hidden" name="onBtnControl" id="onBtnControl" value="../view/v.template.list.action" />
                                <input type="hidden" name="onBtnAdd" id="onBtnAdd" value="../view/v.template.handle.action" />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </body>
</html>