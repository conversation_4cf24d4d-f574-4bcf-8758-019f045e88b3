require([
    'core', 
    'bnext/gridComponent',
    'bnext/gridComponentUtil',
    'dojo/Deferred', 'bnext/gridCubes', 'bnext/gridIcons', 'bnext/columnTypes',
    'dojo/dom', 'bnext/module/PollToAnswerAttender',
    'dojo/domReady!'],
function(/*core*/core,
        /*bnext/gridComponent*/ gridComponent,
        /*bnext/gridComponentUtil*/ gcUtil,
        /*dojo/Deferred*/ Deferred, gridCubes, gridIcons, columnTypes,
        /*dojo/dom*/ dom, pollToAnswerAttender
){
    fillFooter(); 
    var doEverything = function(i18n) {
        var def = new Deferred(), c = new Array(), cType = gcUtil.cType,
        lst = [
            {name:i18n.cubeNames.select, value:'', icon: gridCubes.gray},
            {name:i18n.cubeNames.programmed, value:1, icon: gridCubes.yellow },
            {name:i18n.cubeNames.processing_no_start, value:2, icon: gridCubes.deepGreen },
            {name:i18n.cubeNames.processing_avanced,value:4,icon: gridCubes.green},
            {name:i18n.cubeNames.processing_autosave,value:3,icon: gridCubes.green},
            {name:i18n.cubeNames.close,value:5,icon: gridCubes.gray},
            {name:i18n.cubeNames.cancel,value:6,icon: gridCubes.black},
            {name:i18n.cubeNames.unfinished,value:7,icon: gridCubes.beige}
        ];
        var isEditable = function(row){
            return row.outstandingSurveys && [2,3,4].indexOf(row.outstandingSurveys.estatus) !== -1;
        },
        isViewable=function(row){
            return row.outstandingSurveys && [5,6,7].indexOf(row.outstandingSurveys.estatus) !== -1;
        };
        gcUtil.column(c)
            .push('outstandingSurveys.status',i18n.colNames.status, cType().FunctionImage(['id'],function(){},lst,true), null, '62px')
            .push('edit',i18n.colNames.answer,cType().FunctionImageEval(['poll.id','respondent.id','outstandingSurveys.estatus'],function (id, userId, estatus){
                    var obj = {outstandingSurveys:{estatus : estatus}};
                    var extraParamsAction = '';
                    if (isViewable(obj)){
                        extraParamsAction = 'justLoad=true&equalsEmpty=true';
                    }
                    pollToAnswerAttender.run(id, userId, null, extraParamsAction);
                },[
                {icon:gridIcons.edit,'Function':isEditable,name:i18n.colNames.answerIcon},
                {icon:gridIcons.view,'Function':isViewable,name:i18n.colNames.viewIcon}
            ]), null, '50px')
            .push('poll.code',i18n.colNames.survey, columnTypes.text, 2, '90px')
            .push('poll.dteStart',i18n.colNames.date_start, cType().Date(true), null,'105px')
            .push('poll.dteEnd',i18n.colNames.date_end, cType().Date(true), null,'105px')
            .push('poll.survey',i18n.colNames.questionnaire, cType().Object('description'), null)
        ;
        var g = new gridComponent(gcUtil.basic({
            statusList: lst,  
            container:"grid",
            methodName: 'getMyPolls',
            serviceStore: "Poll.Respondent.action", 
            windowPath: dom.byId('windowPath').value,
            fullColumns : c, 
            onLoaded: function() {
                def.resolve();
            }
        }));
        g.setPageSize(dom.byId('gridSize').value);
        hideLoader();    
        return def.promise;
    };
    core.setLang('lang/poll/nls/my.poll').then(doEverything);
});