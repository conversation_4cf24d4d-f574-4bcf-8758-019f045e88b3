require(['core',
    'bnext/gridComponent',
    'bnext/gridComponentUtil',
    'dojo/Deferred',
    'dojo/dom',
    'dojo/domReady!']
    ,function(/*core*/core,
            /*bnext/gridComponent*/ gridComponent,
            /*bnext/gridComponentUtil*/ gcUtil,
            /*dojo/Deferred*/ Deferred,
            /*dojo/dom*/ dom){
    fillFooter(); 
    var doEverything = function(i18n) {
        var def = new Deferred(), c = new Array(), cType = gcUtil.cType
        ,statusList=[
            {name:i18n.cubeNames.select, value:'', icon: gridCubes.gray},
            {name:i18n.cubeNames.programmed, value:1, icon: gridCubes.yellow },
            {name:i18n.cubeNames.processing_no_start, value:2, icon: gridCubes.deepGreen },
            {name:i18n.cubeNames.processing_avanced,value:4,icon: gridCubes.green},
            {name:i18n.cubeNames.processing_autosave,value:3,icon: gridCubes.green},
            {name:i18n.cubeNames.close,value:5,icon: gridCubes.gray},
            {name:i18n.cubeNames.cancel,value:6,icon: gridCubes.black},
            {name:i18n.cubeNames.unfinished,value:7,icon: gridCubes.beige}
        ];
        gcUtil.column(c)
            .push('outstandingSurveys.status',i18n.colNames.status, cType().FunctionImage(['id'],function(){},statusList,true), null, '30px')
            .push('edit',i18n.colNames.detail,cType().Function(['poll.id','respondent.id'],function (id,userId){
                showLoader();
                document.capture.id.value = id;
                document.capture.userId.value = userId;//<-- useless?
                document.capture.submit();
            }, gridIcons.edit), null, '50px')
            .push('respondent.description',i18n.colNames.surveyed,columnTypes.text, 1, '250px')
            .push('poll.code',i18n.colNames.survey,columnTypes.text, 2, '90px')
            .push('poll.survey',i18n.colNames.questionnaire, cType().Object('description'), null)
            .push('poll.dteStart',i18n.colNames.date_start, cType().Date(true), null,"90px")
            .push('poll.dteEnd',i18n.colNames.date_end, cType().Date(true), null,"90px")
        var g = new gridComponent(gcUtil.basic({
                statusList:statusList,  
                container:"grid", methodName: 'getRows',
                windowPath: dom.byId('windowPath').value,
                serviceStore:"Poll.Respondent.action", 
                fullColumns : c, 
                onLoaded: function() {
                    hideLoader();
                    def.resolve();
                }
            }));
        g.setPageSize(dom.byId('gridSize').value);
        return def.promise;
    };
    core.setLang('lang/poll/nls/poll.registry.list').then(doEverything);
});