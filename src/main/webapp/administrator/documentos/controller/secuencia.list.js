require(['core',  // core
    'bnext/gridComponent', // gridComponent
    'bnext/columnTypes', // columntypes
    "dojo/_base/array", // array
    'dojo/dom', // dom
    'bnext/module/data/RequestType', // RequestType
    'dojo/dom-construct', // domConstruct
    'dijit/Dialog', // Dialog
    'bnext/gridCubes',
    'dojo/domReady!'
], function (core, gridComponent, columnTypes, array, dom, RequestType, domConstruct, Dialog, gridCubes) {
    var doEverything = function (i18n) {
        function renderNextAutorizer(obj) {
            if(obj.status === 1) {
                var position;
                var indiceMin;
                var sequenceDetails = obj.sequenceDetails;
                // Esta parte filtra la secuencia de autorizantes para el objeto con el Status = 1
                var next = array.filter(sequenceDetails, function(detail, index) {
                    if (detail.status === 1) { //Por autorizar
                        if(core.isNull(position)) {
                            position = index;
                            indiceMin = detail.id.indice;
                        } else if (detail.id.indice < indiceMin) {
                            position = index;
                            indiceMin = detail.id.indice;
                        }
                        return true;
                    }
                    return false;
                });
                // ni no hay un proximo autorizante regresar default
                if (next.length === 0) {
                    return '-';
                }
                if (core.isNull(sequenceDetails[position].id)) {
                    return '-';
                }
                var str = "";
                array.forEach(next, 
                    function(sequenceDetail){
                        if (sequenceDetail.id.indice === indiceMin) {
                            var userArr;
                            if (core.isNull(sequenceDetail.userDescription)){
                                userArr =[];
                            } else {
                                userArr = sequenceDetail.userDescription.split(",");
                            }
                            if (!core.isNull(sequenceDetail.positionDescription)) {
                                var positionArr = sequenceDetail.positionDescription.split(",");
                                userArr = userArr.concat(positionArr);
                            }
                            array.forEach(userArr, function (description){
                                if (description && !str.contains(description)) {
                                    str += description + ", ";
                                }
                            });
                        }
                    }
                );
                if (str.endsWith(", ")) {
                    str = str.substring(0, str.length - 2);
                }
                return str;
            } else {
                return '-';
            }
        }
        function renderLimit(obj) {
            /**
             * @ToDo: Este calculo debe hacerse a nivel java al momento de la autorizacion, 
             *        es una mala practica utilizar una "vista" que hace calculos al momento
             */
            var magicIndex = 0;
            var aut = obj.autorizaciones;
            array.filter(aut, function(o, index) {
                if (o.status === 1) { //Por autorizar
                    magicIndex = magicIndex === 0 ? index : magicIndex;
                    return true;
                }
                return false;
            });
            var magic = aut[magicIndex] || {
                creationDate: null,
                flujo: null
            },
            assigned = magic.creationDate ? grid.tools.parseFastDateFromJson(magic.creationDate) : null;
            if (magic.flujo && magic.flujo.flujoPoolList[magicIndex]) {
                var limit = 0, delay = magic.flujo.flujoPoolList[magicIndex].delay || 0;
                if (assigned) {
                    assigned.setDate(assigned.getDate() + delay);
                    limit = grid.tools.formatFastDate(assigned);
                }
                return limit;
            } else {
                return i18n.outOfRange;
            }
        }
        var grid, widgetBK, dial = new Dialog(),
    statusList = [
        {name: '--SELECCIONE--', icon: gridCubes.black, value: ''},
        {name: 'Activa', icon: gridCubes.blue, value: 1},
        {name: 'Finalizada', icon: gridCubes.gray, value: 2},
        {name: 'Rechazada', icon: gridCubes.red, value: 3}
    ],
    requestTypes = [
        {name: 'Nueva', value: RequestType.NEW},
        {name: 'Modificación', value: RequestType.MODIFY},
        {name: 'Reaprobación', value: RequestType.APROVE},
        {name: 'Cancelación', value: RequestType.CANCEL},
        {name: 'Llenado', value: RequestType.FILL}
    ],
    editarSecuencia = function (id) {
        document.submit_form.requestId.value = id;
        document.submit_form.submit();
    },
    status = {
        type: columnTypes.imageMap,
        id: 'status',
        key: 'icon',
        value: 'value',
        name: 'name',
        list: statusList,
                    parameters: ['id'],
        action: editarSecuencia,
        searchObj: {
            type: 'combo',
            list: statusList,
            col: 1
        }
    },
    code = {
        id: 'code',
        type: 1,
        searchObj: {
            type: 'texto',
            col: 1
        }
    },
    creationDate = {
        id: 'fechaHoraCreacion',
        type: columnTypes.date,
        searchObj: {
            type: 'fecha',
            col: 1
        }
    },
    documents = {
        id: 'documentDescription',
        type: 1,
        searchObj: {
            type: 'texto',
            col: 1
        }
    },
    request_type = {
        id:'requestType',
        type: columnTypes.select,
        list: requestTypes,
        key: 'name',
        value: 'value',
        searchObj: {
            type: 'combo',
            list: requestTypes,
            col: 1
        }
    },
    documentsCode = {
        id: 'documentCode',
        type: 1,
        searchObj: {
            type: 'texto',
            col: 1
        }
    },
    requestCode = {
        id: 'description',
        title: i18n.colNames.request,
        type: 1,
        searchObj: {
            type: 'texto',
            col: 1
        },
        renderCell: function (req) {
            
            var d;
            d = domConstruct.create('a', {innerHTML: req.description, href: 'v-request-detail.view?id='+req.requestId});

            return d;
        }
    },
    nextAutorizer = {
        id: 'nextAutorizer',
        type: 1,
        isSortable:false,
        renderCell: renderNextAutorizer,
        renderCellExcel: renderNextAutorizer
    },
    limit = {
        id: 'limit',
        type: 1,
        isSortable:false,
        renderCell: renderLimit,
        renderCellExcel: renderLimit
    },
    aut = {
        id: 'authorizers',
        type: 1,
        title: i18n.approvers,
        isSortable: false,
        renderCell: function (obj) {
            return obj.sequenceDetails.length;
        },
        renderCellExcel: function (obj) {
            return obj.sequenceDetails.length;
        }
    };
        status.title = i18n.colNames.status;
        code.title = i18n.colNames.code;
        creationDate.title = i18n.colNames.creation;
        documents.title = i18n.colNames.title_doc;
        documentsCode.title = i18n.colNames.code_doc;
        nextAutorizer.title = i18n.colNames.n_autho;
        limit.title = i18n.colNames.deadline;
        request_type.title = i18n.colNames.requestType;
        array.forEach(i18n.statusNames, function (n, i) {
            statusList[i].name = n;
        });
        array.forEach(i18n.requestTypes, function (n, i) {
            requestTypes[i].name = n;
        });
        grid = new gridComponent({
            size: dom.byId('gridSize').value,
            id: "grid",
            container: "dataGrid",
            searchContainer: "#auto",
            resultsInfo: "#auto",
            paginationInfo: "#auto",
            serviceStore: "OptSequence.action",
            methodName: "getRowsSecuencia",
            windowPath: dom.byId('windowPath').value,
            columns: [status, code, creationDate, documents, request_type, documentsCode, requestCode, nextAutorizer, limit, aut]
        });
        grid.setPageSize(dom.byId('gridSize').value);
    };
    core.setLang('lang/documentos/nls/secuencia.list').then(doEverything).then(core.hideLoader);
});