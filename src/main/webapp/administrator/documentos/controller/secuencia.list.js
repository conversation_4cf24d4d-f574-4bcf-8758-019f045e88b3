require([
  'core', // core
  'bnext/gridComponent', // gridComponent
  'bnext/columnTypes', // columntypes
  'dojo/_base/array', // array
  'dojo/dom', // dom
  'bnext/module/data/RequestType', // RequestType
  'dojo/dom-construct', // domConstruct
  'dijit/Dialog', // Dialog
  'bnext/gridCubes',
  'dojo/domReady!'
], (core, gridComponent, columnTypes, array, dom, RequestType, domConstruct, Dialog, gridCubes) => {
  const doEverything = (i18n) => {
    function renderNextAutorizer(obj) {
      if (obj.status === 1) {
        let position;
        let indiceMin;
        const sequenceDetails = obj.sequenceDetails;
        // Esta parte filtra la secuencia de autorizantes para el objeto con el Status = 1
        const next = array.filter(sequenceDetails, (detail, index) => {
          if (detail.status === 1) {
            //Por autorizar
            if (core.isNull(position)) {
              position = index;
              indiceMin = detail.id.indice;
            } else if (detail.id.indice < indiceMin) {
              position = index;
              indiceMin = detail.id.indice;
            }
            return true;
          }
          return false;
        });
        // ni no hay un proximo autorizante regresar default
        if (next.length === 0) {
          return '-';
        }
        if (core.isNull(sequenceDetails[position].id)) {
          return '-';
        }
        let str = '';
        array.forEach(next, (sequenceDetail) => {
          if (sequenceDetail.id.indice === indiceMin) {
            let userArr;
            if (core.isNull(sequenceDetail.userDescription)) {
              userArr = [];
            } else {
              userArr = sequenceDetail.userDescription.split(',');
            }
            if (!core.isNull(sequenceDetail.positionDescription)) {
              const positionArr = sequenceDetail.positionDescription.split(',');
              userArr = userArr.concat(positionArr);
            }
            array.forEach(userArr, (description) => {
              if (description && !str.contains(description)) {
                str += `${description}, `;
              }
            });
          }
        });
        if (str.endsWith(', ')) {
          str = str.substring(0, str.length - 2);
        }
        return str;
      }
      return '-';
    }
    function renderLimit(obj) {
      /**
       * @ToDo: Este calculo debe hacerse a nivel java al momento de la autorizacion,
       *        es una mala practica utilizar una "vista" que hace calculos al momento
       */
      let magicIndex = 0;
      const aut = obj.autorizaciones;
      array.filter(aut, (o, index) => {
        if (o.status === 1) {
          //Por autorizar
          magicIndex = magicIndex === 0 ? index : magicIndex;
          return true;
        }
        return false;
      });
      const magic = aut[magicIndex] || {
        creationDate: null,
        flujo: null
      };
      const assigned = magic.creationDate ? grid.tools.parseFastDateFromJson(magic.creationDate) : null;
      if (magic.flujo?.flujoPoolList[magicIndex]) {
        let limit = 0;
        const delay = magic.flujo.flujoPoolList[magicIndex].delay || 0;
        if (assigned) {
          assigned.setDate(assigned.getDate() + delay);
          limit = grid.tools.formatFastDate(assigned);
        }
        return limit;
      }
      return i18n.outOfRange;
    }
    let widgetBK;
    const dial = new Dialog();
    const statusList = [
      { name: '--SELECCIONE--', icon: gridCubes.black, value: '' },
      { name: 'Activa', icon: gridCubes.blue, value: 1 },
      { name: 'Finalizada', icon: gridCubes.gray, value: 2 },
      { name: 'Rechazada', icon: gridCubes.red, value: 3 }
    ];
    const requestTypes = [
      { name: 'Nueva', value: RequestType.NEW },
      { name: 'Modificación', value: RequestType.MODIFY },
      { name: 'Editar detalles', value: RequestType.EDIT_DETAILS },
      { name: 'Reaprobación', value: RequestType.APROVE },
      { name: 'Cancelación', value: RequestType.CANCEL },
      { name: 'Llenado', value: RequestType.FILL }
    ];
    const editarSecuencia = (id) => {
      document.submit_form.requestId.value = id;
      document.submit_form.submit();
    };
    const status = {
      type: columnTypes.imageMap,
      id: 'status',
      key: 'icon',
      value: 'value',
      name: 'name',
      list: statusList,
      parameters: ['id'],
      action: editarSecuencia,
      searchObj: {
        type: 'combo',
        list: statusList,
        col: 1
      }
    };
    const code = {
      id: 'code',
      type: 1,
      searchObj: {
        type: 'texto',
        col: 1
      }
    };
    const creationDate = {
      id: 'fechaHoraCreacion',
      type: columnTypes.date,
      searchObj: {
        type: 'fecha',
        col: 1
      }
    };
    const documents = {
      id: 'documentDescription',
      type: 1,
      searchObj: {
        type: 'texto',
        col: 1
      }
    };
    const request_type = {
      id: 'requestType',
      type: columnTypes.select,
      list: requestTypes,
      key: 'name',
      value: 'value',
      searchObj: {
        type: 'combo',
        list: requestTypes,
        col: 1
      }
    };
    const documentsCode = {
      id: 'documentCode',
      type: 1,
      searchObj: {
        type: 'texto',
        col: 1
      }
    };
    const requestCode = {
      id: 'description',
      title: i18n.colNames.request,
      type: 1,
      searchObj: {
        type: 'texto',
        col: 1
      },
      renderCell: (req) => {
        return domConstruct.create('a', {
          innerHTML: req.description,
          href: `v-request-detail.view?id=${req.requestId}`
        });
      }
    };
    const nextAutorizer = {
      id: 'nextAutorizer',
      type: 1,
      isSortable: false,
      renderCell: renderNextAutorizer,
      renderCellExcel: renderNextAutorizer
    };
    const limit = {
      id: 'limit',
      type: 1,
      isSortable: false,
      renderCell: renderLimit,
      renderCellExcel: renderLimit
    };
    const aut = {
      id: 'authorizers',
      type: 1,
      title: i18n.approvers,
      isSortable: false,
      renderCell: (obj) => obj.sequenceDetails.length,
      renderCellExcel: (obj) => obj.sequenceDetails.length
    };
    status.title = i18n.colNames.status;
    code.title = i18n.colNames.code;
    creationDate.title = i18n.colNames.creation;
    documents.title = i18n.colNames.title_doc;
    documentsCode.title = i18n.colNames.code_doc;
    nextAutorizer.title = i18n.colNames.n_autho;
    limit.title = i18n.colNames.deadline;
    request_type.title = i18n.colNames.requestType;
    array.forEach(i18n.statusNames, (n, i) => {
      statusList[i].name = n;
    });
    array.forEach(i18n.requestTypes, (n, i) => {
      requestTypes[i].name = n;
    });
    const grid = new gridComponent({
      size: dom.byId('gridSize').value,
      id: 'grid',
      container: 'dataGrid',
      searchContainer: '#auto',
      resultsInfo: '#auto',
      paginationInfo: '#auto',
      serviceStore: 'OptSequence.action',
      methodName: 'getRowsSecuencia',
      windowPath: dom.byId('windowPath').value,
      columns: [status, code, creationDate, documents, request_type, documentsCode, requestCode, nextAutorizer, limit, aut]
    });
    grid.setPageSize(dom.byId('gridSize').value);
  };
  core.setLang('lang/documentos/nls/secuencia.list').then(doEverything).then(core.hideLoader);
});
