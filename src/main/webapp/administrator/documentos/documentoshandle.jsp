<%@page import="Framework.Action.SessionViewer"%>
<%@page import="DPMS.DAOInterface.IDocumentDAO"%>
<%@page import="DPMS.Mapping.Request"%>
<!DOCTYPE html>
<%@page import="DPMS.Mapping.Document"%>
<%@page import="DPMS.Mapping.RequestLite"%>
<%@page import="qms.document.logic.DocumentHelper"%>
<%@page import="Framework.Config.Utilities"%>
<%@page import="isoblock.common.Properties"%>
<%@page import="org.slf4j.LoggerFactory"%>
<%@page import="org.slf4j.Logger"%>
<%@page import="java.util.Objects"%>
<%@page import="java.util.ArrayList"%>
<%@page import="java.util.List"%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8");%>
<jsp:useBean id="doc" scope="request" class="isoblock.documentos.documento"/>
<jsp:useBean id="historialSolicitud" scope="request" class="isoblock.documentos.documento"/>
<%--  Asignación de propiedades de los Java Beans --%>
<jsp:setProperty name="doc" property="*" />
<%@taglib prefix="s" uri="/struts-tags" %>
<%request.setCharacterEncoding("UTF-8");%>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
        <link rel="shortcut icon" href="../images/favicon.ico" />
        <title>${applicationData}</title>
        <!-- nuevo superior.jsp -->
        <link rel="stylesheet" type="text/css" href="../style/s-general-theme.style?${systemVersion}">
        <link rel="stylesheet" type="text/css" href="../styles/spiffyCal.css?${systemVersion}"> 
        <script language="JavaScript" src="../scripts/spiffyCal.js?${systemVersion}"></script>
        <script language="JavaScript" src="../scripts/listas.js?${systemVersion}"></script>
        <script language="JavaScript" src="../scripts/ajax.js?${systemVersion}"></script>
        <script language="JavaScript" src="../scripts/template-superior.js?${systemVersion}"></script>
        <link rel="stylesheet" href="../scripts/framework/dijit/themes/bnext/bnext.css?${systemVersion}" media="screen">
        <script type='text/javascript' src="../scripts/framework/console.patch.js?${systemVersion}"></script>
        <script type='text/javascript' src="../scripts/framework/jquery.js?${systemVersion}"></script>
        <script>
          window.___gcfg = {
            parsetags: 'explicit',
            lang: 'es-MX'   
          };
        </script>
        <script src="https://apis.google.com/js/platform.js" async defer></script>
        <%@ include file="../../components/appConfig.jsp"%>
        <!-- Validations  -->
        <script type='text/javascript' src="../scripts/framework/jquery.validate.min.js?${systemVersion}"></script>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/styles/Tabs.css?${systemVersion}" />
        <link rel='stylesheet' type='text/css' href='../styles/tooltip.css?${systemVersion}' >
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <%@include file="../../components/requiredScripts.jsp"%>
        <style>
            .grid-floating-active.grid-container {
                background: white;
                margin-bottom: 0;
            }
            .content_title {
                padding-bottom: 2rem;
            }
            .selected-folder {
                font-weight: 700;
            }
            #mainDiv {
                padding: 0px!important;
            }
            td {
                padding-top: 5px!important;
                padding-right: 5px!important;
                padding-bottom: 5px!important;
                padding-left: 5px!important;
                vertical-align: middle!important;
            }
            li {
                list-style-type: none;
            }
            .separator {
                padding: 5px 0 !important;
            }
            .alertmessage {
                padding-bottom: 5px!important;
            }
            .dijitDialogUnderlay {
                background-color: rgba(0, 0, 0, 0.38);
                opacity: 0.7;
            }
            .information {
                color: black;
                padding: 0px!important;
                text-indent: 5px;
                line-height: 22px;
                border: solid 1px #DDD;
                background: #EEE;
                -moz-border-radius: 3px; -webkit-border-radius: 3px;  border-radius: 3px;
                float: left;
                width: 230px;
            }
            .grid-container.grid-floating-active .widget-component, 
            .grid-container.grid-floating-active .widget-component .dijitTextBox, 
            .grid-container.grid-floating-active .widget-component .dijitTextBox .dijitInputContainer {
                min-height: 3.125rem!important;
                margin-top: 0px;
            }
            .grid-container.grid-floating-active .widget-component .dijitTextBox .dijitValidationContainer, 
            .grid-container.grid-floating-active .widget-component .dijitTextBox .dijitArrowButtonContainer, 
            .grid-container.grid-floating-active .widget-component .dijitTextBox .dijitInputContainer input {
                margin-top: 1.125rem;
                min-height: 2rem;
                text-align: left;
                outline: 0;
            }
            .bnext .dijitComboBox .dijitArrowButton {
                border-top: none;
                border-right: none;
                border-left: none;
                background: transparent;
                min-height: 32px;
                height: auto;
            }
            .view-document-container {
                vertical-align: middle;
                line-height: 34px;
                max-width: 810px!important;
                box-sizing: border-box;
                display: inline-block;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                text-align: justify;
                margin: 5px 0px;
            }
            .Button.outlined-button, .button.outlined-button, .dijitButton.outlined-button {
                border-color: #066669;
                color: #066669!important;
            }
            .bnext .dijitButtonNode {
                color: #066669!important;
                background-color: transparent!important;
            }
            .bnext .outlined-button .dijitButtonContents {
                border-radius: 4px;
             }
            .view-document-label {
                text-indent: 20px;
                background: #0000!important;
                vertical-align: middle;
                box-sizing: border-box;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                padding: 0 0.8rem 0 2.8rem;;
                text-transform: uppercase;
                font-size: 14px;
            }
            .view-document-container img {
                width: 24px;
                height: 24px;
                cursor: pointer;
                vertical-align: middle;
                position: absolute;
                left: 0.5rem;
                padding: 0.4rem;
            }
            span#accessPermissionReason {
                padding: 15px;
                display: block;
            }
            .path_container,
            .access_permission_reason {
                padding: 10px 0px 20px 0px;
                border-bottom: #DDD solid 2px;
                margin-bottom: 10px;
            }
            .path,
            .access_permission_reason {
                font-weight: bold;
                line-height: 14px;
                margin: 10px auto 0px auto;
                word-wrap: break-word;
                float: none;
                width: 100%;
            }
            .content_area {
                border-left:0px;
                border-right:0px;
            }
            .view-document-container {
                display: inline-block; 
                vertical-align: middle;
                height: 100%;
                max-width: 460px;
            }
            .view-document {
                width: auto;
                min-width: 230px;
            }
            .save-to-drive-container {
                text-align:left;
            }
            <s:if test="viewerfinder">
                body, body.body, .body {
                    width: 703px!important;
                    min-width: 703px!important;
                }
                .separator, #mainTitle, #window_title, #archivosUp, #archivosUpOriginal,
                #add_related_documents_btn, #add_readers_btn, #add_positions_btn,
                #divTable
                {
                    display: none!important;
                }
                #mainDiv {
                    border-radius: 0px;
                }
            </s:if>
            .imgForm{
                height:16px;
                float:right;
                padding:4px 4px 0px 0px;
            }
            .bnext div#readers_dialog div.dijitDialogPaneContent .content_area li.actionButtons {
                margin-top: 5px;
            }
            #mainDiv .content_area{
                padding:0 15px;
            }
            #mainDiv .content_area > li{
                padding:0 0px;
            }
            a.Button.addToGrid {
                margin: 0px!important;
            }
            form.hide-on-linked-grid.grid-component-container {
                padding-bottom: 5px;
            }
            div#paging_full_numbers_gridRelatedDocument {
                display: none;
            }
            form #mainDiv .content_area > li.detail-style {
                padding-bottom: 10px;
            }
            #filepath:hover {
                text-decoration: underline;
                cursor: pointer;
            }
            .bnext .dijitLayoutContainer.dijitDialog .searchFields .searchField label {
                top: 2.5rem;
            }
            .bnext .dijitLayoutContainer.dijitDialog .searchFields {
                padding: 0 0 0 0;
            }
            .bnext .dijitLayoutContainer.dijitDialog .searchFields > .searchField {
                padding-top: 0.5rem;
                padding-bottom: 0.5rem;
            }
            #menu {
                position: relative;
                right: 0.5rem;
                top: 2.5rem;
            }
            .wrap-text{
                overflow:hidden;
                white-space:nowrap;
                text-overflow: ellipsis;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" class="bnext">
        <a name="iniciopagina"></a>
<%
    Logger logger = LoggerFactory.getLogger("isoblock.jsps.getsesionusuario");
    request.setCharacterEncoding("UTF-8");
    isoblock.common.Utilities uc = new isoblock.common.Utilities();
    uc.setRequest(request);
    String sesionusuarioid = (String) session.getAttribute("intusuarioid");
    Long loggedUserId = sesionusuarioid != null ? Long.parseLong(sesionusuarioid) : 0;
    isoblock.common.Properties propiedadesJsp = new isoblock.common.Properties(loggedUserId.intValue());
    java.util.ResourceBundle tagsParam = java.util.ResourceBundle.getBundle("isoblock.common.language", new Properties(Integer.parseInt(session.getAttribute("intusuarioid").toString())).localidad);
    isoblock.common.Letreros tags = new isoblock.common.Letreros(tagsParam);
    List<mx.bnext.access.ProfileServices> services = (List<mx.bnext.access.ProfileServices>) (session.getAttribute("services") == null ? new ArrayList() : session.getAttribute("services"));
    boolean boolNuevo = false, isAdmin = ((String) (session.getAttribute("admin_general") == null ? "" : session.getAttribute("admin_general"))).equals(isoblock.common.Properties.ADMINISTRADOR_MAESTRO);
    String sesionroladministracion = (String) session.getAttribute("introladministracion");
    String sesionroldocumentos = (String) session.getAttribute("introldocumentos");
    logger.debug("this is an old & ugly jsp, your mission if you'd choose to accept it is: DESTROY IT");
    IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
    String
        state = doc.getVchState(),
        boton1 = "",
            boton2 = tags.getString("boton.Regresar"),
            leo = doc.parameter("leer", request),
            idDocumento = doc.parameter("idDocu", request),
            lugarAlmacenamiento = "",
            tiempoAlmacenamiento = "",
        intSolicitudId = doc.parameter("intSolicitudId", request)
    ;
    logger.trace("Estado: '{}'", state);
    boolean fromConfiguracion = doc.parameter("source", request).equals("Configuracion");
    if (!idDocumento.isEmpty()) {
        logger.trace("entra en id idocument");
        doc.setAllValues(idDocumento);
    } else {
        boolNuevo = true;
    }
    logger.trace("--- idDocumento: {} - boolNuevo: {}", idDocumento, boolNuevo);
    doc.setIntRepositorioId(doc.parameter("repositorio", request));
    doc.setIntNodoId(doc.parameter("nodo", request));
    if (!intSolicitudId.isEmpty()) {
        logger.trace("entra en id de solicitud simple");
        RequestLite r = dao.HQLT_findById(RequestLite.class, Long.parseLong(intSolicitudId));
        doc.setIntNodoId(r.getNodo().getId().toString());
        doc.setVchTitulo(r.getDescription());
        doc.setVchClave(r.getDocumentCode());
        if (doc.getIntOriginadorId() == null || doc.getIntOriginadorId().isEmpty()) {
            doc.setIntOriginadorId(r.getAuthor().getId().toString());
        }
        doc.setFileId(r.getFileId());
        //doc.setTxtNombreArchivoSistema(doc.findSimple("SELECT vchnombrearchivo FROM tblsolicitud WHERE intsolicitudid = " + intSolicitudId));
        doc.setVchRazon(r.getReazon());
    }

    if (state.equals("Edit")) {
        doc.setAllValues();
        boton1 = tags.getString("boton.Aceptar");
    } else {
        doc.setIntAutorId(sesionusuarioid);
        boton1 = tags.getString("boton.Aceptar");
    }

    if (!doc.getIntRetencionTime().equals("0")) {
        tiempoAlmacenamiento = doc.getIntRetencionTime() + " "
                + //Si son dias
                (doc.getIntRetencionText().equals(doc.SPAN_TIPO_DIA)
                ? (doc.getIntRetencionTime().equals("1") ? tags.getString("span.dia") : tags.getString("span.dias"))
                //si son meses
                : (doc.getIntRetencionText().equals(doc.SPAN_TIPO_MES)
                ? (doc.getIntRetencionTime().equals("1") ? tags.getString("span.mes") : tags.getString("span.meses"))
                //si son años
                : doc.getIntRetencionText().equals(doc.SPAN_TIPO_ANIO)
                ? (doc.getIntRetencionTime().equals("1") ? tags.getString("span.anio") : tags.getString("span.anios"))
                //si no es ninguna NOTA: NORMALMENTE no debe entrar aqui
                : ""));
        logger.trace("{} | {}", tiempoAlmacenamiento, lugarAlmacenamiento);
        lugarAlmacenamiento = doc.findSimple("SELECT vchtexto FROM tblcatalogo WHERE inttipoid = " + doc.getIntLugarAlmacenamiento() + " AND intcatalogoid = 13");
    }
%>
<script type="text/javascript">
    function errorCallBack() {
    }

    window.onload = function() {
        hideLoader();
    };

    function hideLoader() {
        var loader = document.getElementById("loader");
        if (loader) {
            loader.style.display = "none";
        }
        var mainLoader = window.top.document.getElementById("main-loader");
        if (mainLoader) {
            mainLoader.style.display = "none";
        }
        require(['core', 'dojo/domReady!'], function (core) {
            core.hideLoader();
        });
    }

    function showLoader() {
        require(['core', 'dojo/domReady!'], function (core) {
            core.showLoader();
        });
    }
    window.documentoshandle = {
        'intDocumentoId': '<%= doc.getIntDocumentoId()%>',
        'activeDocument': <%= doc.getIntEstado().equals(Document.ACTIVE_STATUS.toString())%>,
        'dynamicTableName': '<%= doc.getDynamicTableName()%>',
        'isInactiveStatus': '<%= !doc.getIntEstado().isEmpty() && doc.getIntEstado().equals(Document.INACTIVE_STATUS.toString())%>',
        'grid_positions_added_noRegMessage': "<%=tags.getString("documentos.documentoshandle.Nosehanasignadopuestosaldocumentocontrolado")%>",
        'readersOn': <%= Utilities.getSettings().getReaders() == 1 && doc.getMustRead() == 1 %>,
        'positionForControlledCopyOn': <%=
                        !fromConfiguracion 
                        && doc.getIntEstado().equals(Document.ACTIVE_STATUS.toString())
                        && Objects.equals(doc.getEnablePdfViewer(), 1)
                        && Objects.equals(Utilities.getSettings().getPositionForControlledCopy(), 1)
                        && Objects.equals(doc.getCanPrintControlledCopies(), 1)
                        && (
                          isAdmin
                          || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_ENCARGADO)
                          || services.contains(mx.bnext.access.ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT)
                          || services.contains(mx.bnext.access.ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT)
                       )%>,
        'documentTypeId': +'<%= doc.getIntTipoDocumento()%>',
        'retentionType': +'<%= doc.getIntRetencionText() %>',
        'retentionTime': +'<%= doc.getIntRetencionTime() %>',
        'disposition': +'<%= doc.getDisposition() %>'
    };
    hideLoader();
</script>
<script src="../scripts/framework/bnext/administrator/documentos/documentoshandle.js?${systemVersion}" type="text/javascript"></script>
<script type='text/javascript'>

    readed = false;
    function retiene(select) {
        var catalogosRetenibles = [<%
        String str = "SELECT document_type_id FROM document_type WHERE retainable != 0";
        doc.find(str);
        int i = 0;
        while (doc.next()) {
            if (i != 0) {
                out.write(',');
            }
            i++;
            out.write("'" + doc.fieldStringS("document_type_id") + "'");
        }
    %>];
        var contains = catalogosRetenibles.indexOf(select.value);
        if (!(contains === "-1")) {
            document.getElementById('retencion').style.display = '';
            document.getElementById('almacenamiento').style.display = '';
        } else {
            document.getElementById('retencion').style.display = 'none';
            document.getElementsByName('intRetencionTime')[0].value = '';
            document.getElementById('almacenamiento').style.display = 'none';
            document.getElementById('intLugarAlmacenamiento').value = '0';
        }
    }

    function parameters() {
        changeDivs();
        if (document.form1.vchState.value === "cambioClaveTitulo") {
            document.form1.actionType.value = 'cambioClaveTitulo';
            document.form1.msgAction.value = '<%=tags.getString("documentos.documentoshandle.javascript.LosDatosFueronCambiadosConExito")%>';
        } else if (document.form1.vchState.value === "Edit") {
            document.form1.actionType.value = 'updateDocumento';
            document.form1.msgAction.value = '<%=tags.getString("documentos.documentoshandle.javascript.LaactualizaciondeldocumentohasidopuestaenesperadeRevision")%>';
        } else {
            document.form1.actionType.value = 'insertDocumento';
            document.form1.msgAction.value = '<%=tags.getString("documentos.documentoshandle.javascript.ElDocumentosehaagregadoconexitoalalistadeesperaporRevision")%>';
        }
        document.form1.className.value = 'isoblock.documentos.documento';
        document.form1.sectionName.value = 'Documentos';
        document.form1.addPage.value = "../view/v.documentos.view?nodo=<%=doc.getIntNodoId()%>&repositorio=<%=doc.getIntRepositorioId()%>";
        document.form1.msgButton1.value = '<%=tags.getString("documentos.documentoshandle.javascript.Nuevodocumento")%>';
        document.form1.msgButton2.value = '<%=tags.getString("documentos.documentoshandle.javascript.Vercarpeta")%>';
        document.form1.page.value = "../view/v.common.actional.view";
        //document.form1.action += "&page=" + "../view/v.common.actional.view";
        if (document.form1.vchState.value !== "cambioClaveTitulo") {
            document.form1.action += "&" + getALLParams(true);
            document.form1.encoding = 'multipart/form-data';
            document.form1.submit();
        } else {
            document.form2.actionType.value = 'cambioClaveTitulo';
            document.form2.msgAction.value = '<%=tags.getString("documentos.documentoshandle.javascript.LosDatosFueronCambiadosConExito")%>';
            document.form2.className.value = 'isoblock.documentos.documento';
            document.form2.sectionName.value = 'Documentos';
            document.form2.addPage.value = "../view/v.documentos.view?nodo=<%=doc.getIntNodoId()%>&repositorio=<%=doc.getIntRepositorioId()%>";
            document.form2.msgButton1.value = '<%=tags.getString("documentos.documentoshandle.javascript.Nuevodocumento")%>';
            document.form2.msgButton2.value = '<%=tags.getString("documentos.documentoshandle.javascript.Vercarpeta")%>';
            document.form2.vchClave.value = document.form1.vchClave.value;
            //document.form2.vchClave.value = document.getElementById('vchClave').value;
            document.form2.intDocumentoId.value = document.form1.intDocumentoId.value;
            document.form2.intTipoDocumento.value = document.form1.intTipoDocumento.value;
            document.form2.vchTitulo.value = document.form1.vchTitulo.value;

            if (document.form2.idSavedUSRSelectorUsuario && document.form1.idSavedUSRSelectorUsuario) {
                //document.form2.idSavedUSRSelectorUsuario.innerHTML = document.form1.idSavedUSRSelectorUsuario.innerHTML;
                guardar_items(document.form1.idSavedUSRSelectorUsuario, document.form2.idSavedUSRSelectorUsuario);
                selecciona_items(document.form2.idSavedUSRSelectorUsuario);
            }
            //alert(document.form1.className.value)
            document.form2.action = "../view/v.common.actional.view?"
                    /*
                     + '&vchClave=' + document.form1.vchClave.value
                     + '&intDocumentoId=' + document.form1.intDocumentoId.value
                     + '&intTipoDocumento=' + document.form1.intTipoDocumento.value
                     + '&idSavedUSRSelectorUsuario=' + getListAllParams('idSavedUSRSelectorUsuario', 'idSavedUSRSelectorUsuario');
                     //*/
                    ;
            document.form2.submit();
        }
    }

    function isArray(name, forma) {
        total = 0;
        var elementos = forma.elements;
        for (i = 0; i < elementos.length; i++) {
            if (elementos[i].name === name) {
                total++;
                if (total > 1) {
                    return true;
                }
            }
        }
        return false;
    }

    function limpiar(name) {
        eval("document.form1." + name + ".style.backgroundColor='#FFFFFF'");
    }

    function limpiaColor() {
        limpiar('vchTitulo');
        limpiar('vchClave');
        limpiar('intOriginadorId');
        limpiar('txtNombreArchivoSistema');
        limpiar('vchRazon');
    }


    //Funcion que asegura que los campos no estén vací­os
    function validateSubmit() {
        limpiaColor();
        faltante = "<%=tags.getString("documentos.documentoshandle.javascript.Lossiguientesdatossonnecesarios")%>";
        falta = false;

        if (document.form1.vchTitulo.value === "") {
            faltante += "\n <%=tags.getString("documentos.documentoshandle.javascript.Titulo")%>";
            document.form1.vchTitulo.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.vchTitulo.focus();
                falta = true;
            }
        }

        if (document.form1.vchClave.value === "") {
            faltante += "\n <%=tags.getString("documentos.documentoshandle.javascript.Clave")%>";
            document.form1.vchClave.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.vchClave.focus();
                falta = true;
            }
        }

        //TODO: agregado, poner en ingles, se agrego para le generacion de respaldos en la base de datos
        if (document.form1.vchClave.value.toString().substring(0, 7) === "RESP - ") {
            faltante += "\n <%=tags.getString("documentos.documentoshandle.Laclaveingresadaestarestringidaparalageneracionderespaldos")%>";
            document.form1.vchClave.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.vchClave.focus();
                falta = true;
            }
        }


        if (document.form1.intOriginadorId.value === "0") {
            faltante += "\n <%=tags.getString("documentos.documentoshandle.javascript.Originador")%>";
            document.form1.intOriginadorId.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.intOriginadorId.focus();
                falta = true;
            }
        }

        if (document.form1.intTipoDocumento.value === "0") {
            faltante += "\n <%=tags.getString("documentos.documentoslector.Tipodedocumento")%>";
            document.form1.intTipoDocumento.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.intTipoDocumento.focus();
                falta = true;
            }
        }
        if (document.form1.vchRazon.value === "") {
            faltante += "\n <%=tags.getString("documentos.documentoshandle.javascript.Razondelcambio")%>";
            document.form1.vchRazon.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.vchRazon.focus();
                falta = true;
            }
        }

        if (falta) {
            alert(faltante);
            return;
        }
        if (document.form1.pop.value === "1") {
            document.form1.pop.value = '1';
            changeDivs();
            document.form1.submit();
        }
        else
        {
            if (document.form1.vchState.value === "Edit") {
                if (confirm('<%=tags.getString("mensaje.actualizacion")%>')) {
                    parameters();
                }
            }
            else {
                parameters();
            }
        }
    }

    function save() {
        flag = true;
        if (!document.getElementById('existClave')
                && !document.getElementById('chkClaveAutomatica').checked
                ) {
            flag = false;
        }
        if (!document.getElementById('existTitulo')) {
            flag = false;
        }
        if (flag) {
            validateSubmit();
        }
        //refreshParent();
    }
    function validateTitulo() {
        if (document.getElementById('existTitulo').value === 'exist') {
            document.getElementById('repeatTitulo').style.display = '';
            document.form1.vchTitulo.value = '';
        } else if (document.getElementById('existTitulo').value === 'solic') {
            document.getElementById('repeatSolTitulo').style.display = '';
            document.form1.vchTitulo.value = '';
        }
    }
    function validateClave() {
        if (document.getElementById('existClave').value === 'exist') {
            document.getElementById('repeatClave').style.display = '';
            document.form1.vchClave.value = '';
        } else if (document.getElementById('existClave').value === 'solic') {
            document.getElementById('repeatSolClave').style.display = '';
            document.form1.vchClave.value = '';
        }
    }
    function cambiarTituloClave() {
        flag = true;
        if (!document.getElementById('existClave')) {
            flag = false;
        }
        if (!document.getElementById('existTitulo')) {
            flag = false;
        }
        if (!flag) {
            return;
        }

        limpiar('vchTitulo');
        limpiar('vchClave');
        faltante = "<%=tags.getString("documentos.documentoshandle.javascript.Lossiguientesdatossonnecesarios")%>";
        falta = false;

        if (!document.form1.vchTitulo.value) {
            faltante += "\n <%=tags.getString("documentos.documentoshandle.javascript.Titulo")%>";
            document.form1.vchTitulo.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.vchTitulo.focus();
                falta = true;
            }
        }

        if (!document.form1.vchClave.value) {
            faltante += "\n <%=tags.getString("documentos.documentoshandle.javascript.Clave")%>";
            document.form1.vchClave.style.backgroundColor = requiredfield();
            if (!falta) {
                document.form1.vchClave.focus();
                falta = true;
            }
        }

        /*Selecciona al usuario actual y lo elimina del listbox,
         * asi no crea un registro nuevo, y marca el documento como
         * leido*/
        if (readed) {
            var selects = document.getElementById
                    ('selectRightEntitySelectorUsuario');
            for (var i = 0, len = selects.length; i < len; ++i) {
                if (+selects[i].value === <%=sesionusuarioid%>) {
                    selects.selectedIndex = [i];
                    break;
                }
            }
            quitarDerecha("SelectorUsuario");
        }
        if (falta) {
            alert(faltante);
        } else if (confirm('<%=tags.getString("mensaje.actualizacion")%>')) {
            document.form1.vchState.value = 'cambioClaveTitulo';
            parameters();
            //refreshParent();
        }
    }

    // Show/Hide functions for pointer objects
    function showMessage(obj) {
        obj.style.display = '';
    }


    //--------------------------------------------------------------------------------javascript para documentos


// Hace un reload para borrar un documento
    function borrarDocumento(documentoId) {

        if (confirm("<%=tags.getString("proyectos.proyectohandle.javascript.ElDocumentoSeraBorradoDeseaContinuar")%>")) {
            document.form1.action = "../view/v.documentos.view?nodo=<%=doc.parameter("nodo", request)%>&repositorio=<%=doc.parameter("repositorio", request)%>&vchState=Edit&intDocumentoId=<%=doc.getIntDocumentoId()%>&documentoBorrar=" + documentoId + "&aux=borrarDoc";
            changeDivs();
            document.form1.submit();

        }
    }

// Abre ventana para agregar documento



    var winAuxActividades;

// Llama a un procedimiento de la nueva ventana de documentos
    function callProc(e) {
        winAuxActividades.whenClose(window);
    }

// Funciones del comportamiento de archivos

    function ver(tipoB) {
        if (document.form1.verA.value === "true") {
            document.form1.verA.value = "false";
            block("mostrarArchivos");
            block("documents");
            block("esconderArchivos");
        } else {
            block("mostrarArchivos");
            block("documents");
            block("esconderArchivos");
            document.form1.verA.value = "true";
        }
    }

// Funciones del comportamiento de componente de manejo de archivos

// Filtro de repositorios
    function changeNodoId() {
        selecciona_items(document.form1.list_der);
        document.form1.controlListas.value = true;
        recargar();
    }
//checar...
    function recargar() {
        selecciona_items(document.form1.list_der);   //**********************<-- podria ser indispensable en cada reload
        document.form1.vchState.value = "reload";
        document.form1.action = "../view/v.documentos.view?nodo=<%=doc.parameter("nodo", request)%>&repositorio=<%=doc.parameter("repositorio", request)%>&vchState=Edit&intDocumentoId=<%=doc.getIntDocumentoId()%>";
        document.form1.submit();
    }

// Checha el tipo de explorador
    ns4 = (document.layers) ? true : false;
    ie4 = (document.all) ? true : false;

// Funcion que muestra y esconde el control de listas
    function mostrar_listas() {
        hide('listas');
    }

    function easyRefresh() {
        //setParamsRefresh();
        document.form1.submit();
    }
    function setParamsRefresh() {
        var parametros = '?aux=0';
        var temp = '?aux=0';
        var elementos = document.form1.length;
        for (i = 0; i < elementos; i++) {
            if (document.form1.elements[i].id) {
                temp = '&' + document.form1.elements[i].id;
                temp += '=' + document.form1.elements[i].value;
                if (document.form1.elements[i].value && document.form1.elements[i].id) {
                    parametros += temp;
                }
            }
        }
        //alert(parametros);
        document.form1.action = '../view/v.documentos.view';
    }
    function noConoscoLaClave(here) {
        if (here.checked) {
            here.value = '1';
            document.form1.vchClave.value = 'POR GENERAR';
            document.form1.vchClave.disabled = true;
        } else {
            here.value = '0';
            document.form1.vchClave.disabled = false;
            document.form1.vchClave.value = '';
        }
    }
</script>
<%--  Comienza el cuerpo del JSP  --%>
<%@include file="../../includes/titulotabla.jsi"%>
<%@include file="../../components/loader.jsp" %>
<div class="grid-container grid-floating-active">
    <form name="form1" id="form1" method="post" action="">
        <div id="myhiddendiv"></div>
        <div id="menu" class="position-relative float-right displayNone">
            <a href="javascript: void(0);" id="moreOptions" class="material-icons" title="">
                more_vert
            </a>
        </div>
        <%=header(tags.getString("documentos.documentoshandle.header.DOCUMENTOS"))%>
        <div class="grid-x content_area" id="contenido"> 
            <%
                //Comienza código de protección de lista maestra.
                logger.trace("NODO Lista Maestra {}", doc.getIntNodoId());
                logger.trace("Usuario Actual {}", sesionusuarioid);
                doc.setStrIntAutorId(sesionusuarioid);
                logger.trace("Autor: {}", doc.getStrIntAutorId());

                int nodox, userid;
                String iddoc = "0";

                if (!doc.getIntDocumentoId().isEmpty()) {
                    iddoc = doc.getIntDocumentoId();
                    logger.trace("iddoc: {}", iddoc);
                }
                if(doc.getIntNodoId() == null || doc.getIntNodoId().isEmpty()) {
                    doc.setIntNodoId("0");
                }
                nodox = Integer.parseInt(doc.getIntNodoId());
                userid = Integer.parseInt(sesionusuarioid);

                logger.trace("nodo : {}", nodox);
                logger.trace("id de usuario : {}", userid);


                if (doc.getVchState().equals("Edit")) {
                    String nodoName = "";
                    try {
                        nodoName = dao.HQL_findSimpleString("SELECT c.path FROM DPMS.Mapping.NodePath c WHERE c.id = :node",
                                "node", Long.parseLong(doc.getIntNodoId()));
                    } catch (Exception e) {
                        logger.error(e.getMessage(),e);
                }
                    logger.trace("nodoName {}", nodoName);
            %>
        
            <!-- ##################### PATH CONTAINER  ########################## -->
            <div class="cell small-12 medium-12 large-12 folder-selected-area controlArea">
                <span id="lblLocation"><%=tags.getString("documentos.documentoshandle.Localizadoen")%></span>
                <span id="filepath" class="selected-folder bnextFolderLink" data-node-id="<%=doc.getIntNodoId()%>"> <%=nodoName%> </span> 
            </div>
            <%}%>
            <!-- ##################### STATUS  ########################## -->
            <div class="field-display cell small-12 medium-6 large-3">
                <strong name="desplegadointEstado" class="labelText"><%=doc.getStringEstado(Integer.parseInt(doc.getIntEstado()))%></strong>  
                <label><%=tags.getString("documentos.documentoshandle.Estatus")%></label>
                <input type="hidden" class="input" name="vchNombreCompleto" value="<%=doc.getDocumento().getOriginador() != null ? doc.getDocumento().getOriginador().getDescription() : ""%>">
            </div>
            <!-- ##################### DOCUMENT TITLE  ########################## -->
            <div class="field-display cell small-12 medium-6 large-3 icon-description">
                <strong name="vchTitulo" class="labelText"><%=(doc.getVchTitulo().replaceAll("\"", "&quot;"))%></strong>
                <label><%=tags.getString("documentos.documentoshandle.Titulo")%></label>
                <div id="divExisteTitulo" style="position: absolute;margin-top: -15px;"></div>
            </div>
            <% if (doc.getSlimReportName() != null && !doc.getSlimReportName().isEmpty()) { %>
                <div class="field-display cell small-12 medium-6 large-3 icon-description">
                    <strong name="slimReportTitle" class="labelText"><%=doc.getSlimReportName()%></strong>
                    <label><%=tags.getString("documentos.documentoshandle.slimReportName")%></label>
                    <div id="divSlimReportTitle" style="position: absolute;margin-top: -15px;"></div>
                </div>
            <% } %>
            <%
                if (doc.parameter("intClaveAutomatica", request).equals("1")) {
                    logger.trace("---");
                    String number = doc.findSimple("SELECT intdocumentoid FROM tbldocumento ORDER BY intdocumentoid DESC LIMIT 1").isEmpty() ? "0" : doc.findSimple("SELECT intdocumentoid FROM tbldocumento ORDER BY intdocumentoid DESC LIMIT 1");
                    logger.trace("number: {}", number);
                    int claveval = Integer.parseInt(number) + 1;
                    String claveFinalAutomatica = "DOC-" + doc.formatSecClave(claveval);
                    while (!doc.findSimple("SELECT vchclave FROM tbldocumento WHERE vchclave = '" + claveFinalAutomatica + "'").isEmpty()) {
                        claveval++;
                        claveFinalAutomatica = "DOC-" + doc.formatSecClave(claveval);
                    }
                    doc.setVchClave(claveFinalAutomatica);
                }
            %>
            
            <!-- ##################### DOCUMENT CODE  ########################## -->
            <div class="field-display cell small-12 medium-6 large-3 icon-description">
                <input type="hidden" name="version" id="version" value="<%=doc.getIntVersion()%>">
                <input type="hidden" id="masterId" value="<%= doc.getMasterId()%>">
                <input type="hidden" id="code" value="<%= doc.getVchClave()%>">
                <%if (!doc.getVchClaveRechazo().isEmpty()) {%>
                    <strong name="vchClave" class="labelText" onchange="initValidateClave()"><%=doc.getVchClaveRechazo()%></strong>
                <%} else {%>
                    <strong name="vchClave" class="labelText"><%=doc.getVchClave()%></strong>
                <%}%>
                <div id="divExisteClave" style="position: absolute;margin-top: -15px;">
                    <%if (!doc.getIntDocumentoId().isEmpty()) {%>
                    <input type='hidden' name='existClave' id='existClave' value=''>
                    <%}%>
                </div>
                <label><%=tags.getString("documentos.documentoshandle.Clave")%></label>
                <%if (doc.getVchState().equals("Edit")) {%>
                <%}%>
            </div>
            <!-- ##################### DEPARTMENT  ########################## -->
            <% if (!doc.getLastDepartment().equals("")) {%>
            <div class="field-display cell small-12 medium-6 large-3">
                <strong class="labelText"> <%= doc.getLastDepartment() %> </strong>
                <label><%=tags.getString("documentos.documentoslector.lastDepartamento")%></label>
            </div>
            <% }%>        
            <% if (doc.getDocumento().getDepartment() != null) {%>
            <div class="field-display cell small-12 medium-6 large-3">
                <strong class="labelText"> <%= doc.getDocumento().getDepartment().getDescription()%> </strong>
                <% if (doc.getLastDepartment().equals("")) {%>
                    <label><%=tags.getString("documentos.documentoslector.Departamento")%></label>
                <% } else {%>
                    <label><%=tags.getString("documentos.documentoslector.current.Departamento")%></label>
                <% }%>        
            </div>
            <% }%>      
            <!-- ##################### DOCUMENT AUTHOR  ########################## -->
            <div class="field-display cell small-12 medium-6 large-3 icon-person">
                <% String author = doc.getDocumento().getAuthor().getDescription();  %>
                <strong disabled name="authorId" class="labelText"><%=author%></strong>
                <label><%=tags.getString("documentos.documentoshandle.Originador")%></label>
            </div>
            <!-- ##################### DOCUMENT TYPE  ########################## -->
            <div class="field-display cell small-12 medium-6 large-3">
                <strong disabled class="labelText"><%= doc.getIntTipoDocumentoName(dao)%></strong>
                <label><%=tags.getString("documentos.documentoslector.Tipodedocumento")%></label>
                <input type="hidden" value="<%= doc.getIntTipoDocumento()%>" name="intTipoDocumento" id="intTipoDocumento">
            </div>
            <!-- ##################### RETENTION TIME  ########################## -->
            <%if (!doc.getIntRetencionTime().equals("0")) {%>
            <div class="cell small-12 medium-6 large-3">
                <div <%if (doc.getIntRetencionTime().equals("0")) {
                      out.write("style=\"display: none\"");
                  }%> id="retencion" class="field-display icon-today">
                    <% if (doc.getIntRetencionTime().equals("0")) {%>
                    <input type="number" min="0" step="1" style="width: 50px" name="intRetencionTime" onkeypress="onlyNumbers(this)" >
                    <select name="intRetencionText">
                        <option value="<%= doc.SPAN_TIPO_DIA%>"><%= tags.getString("span.dia_s")%></option>
                        <option value="<%= doc.SPAN_TIPO_MES%>"><%= tags.getString("span.mes_es")%></option>
                        <option value="<%= doc.SPAN_TIPO_ANIO%>"><%= tags.getString("span.anio_s")%></option>
                    </select>
                    <%} else {%>
                    <strong class="labelText">
                        <% out.write(tiempoAlmacenamiento);%>
                    </strong>
                    <%}%>
                    <label><%= tags.getString("documentos.tiempoDeRetencion")%></label>
                </div>
            </div>
            <!-- ##################### STORAGE PLACE  ########################## -->
            <div class="cell small-12 medium-6 large-3">
                <div <% if (doc.getIntRetencionTime().equals("0")) {
                      out.write("style=\"display: none\"");
                  }%> id="almacenamiento" class="field-display">
                    <% if (doc.getIntRetencionTime().equals("0")) {%>
                        <select id="intLugarAlmacenamiento" name="intLugarAlmacenamiento" display="none;"></select>
                        <input type="text" disabled value="<%=doc.getRepositorioCode()%>" name="repositorioCode" id="repositorioCode">
                    <% } else {%>              
                    <strong class="labelText">
                        <%out.write(lugarAlmacenamiento);%>
                    </strong>
                    <%}%>
                    <label><%= tags.getString("documentos.LugarDeAlmacenamiento")%></label>
                </div>
            </div>
            <%}%>
            <% if (!doc.getTxtNombreArchivoSistema().isEmpty()) { %>
            <div class="cell small-12 medium-6 large-3 field-display icon-description">
                <strong data-dojo-attach-point="titleDocument" class="labelText wrap-text" title="<%=doc.getTxtNombreArchivoSistema()%>"> <%=doc.getTxtNombreArchivoSistema()%></strong>
                <label class="label"><%= tags.getString("documentos.documentoshandle.TituloArchivo")%></label>
            </div>
            <% } %>
            
            
            <!-- ##################### CollectingAndStoreResponsible  ########################## -->
            <%if (!doc.getCollectingAndStoreResponsible().equals("")) {%>
            <div class="cell small-12 medium-6 large-3">
                <div <%if (doc.getCollectingAndStoreResponsible().equals("")) {
                      out.write("style=\"display: none\"");
                  }%> id="collectingAndStoreResponsible" class="field-display">
                    <strong class="labelText">
                        <% out.write(doc.getCollectingAndStoreResponsible());%>
                    </strong>
                    <label><%= tags.getString("documentos.collectingAndStoreResponsible")%></label>
                </div>
            </div>
            <%}%>
            <!-- ##################### InformationClassification  ########################## -->
            <%if (!doc.getInformationClassification().equals("")) {%>
            <div class="cell small-12 medium-6 large-3">
                <div <%if (doc.getInformationClassification().equals("")) {
                      out.write("style=\"display: none\"");
                  }%> id="informationClassification" class="field-display">
                    <strong class="labelText">
                        <% out.write(doc.getInformationClassification());%>
                    </strong>
                    <label><%= tags.getString("documentos.informationClassification")%></label>
                </div>
            </div>
            <%}%>
            <!-- ##################### Disposition  ########################## -->
            <%if (!doc.getDisposition().equals("")) {%>
            <div class="cell small-12 medium-6 large-3">
                <div <%if (doc.getDisposition().equals("")) {
                      out.write("style=\"display: none\"");
                  }%> id="disposition" class="field-display">
                    <strong class="labelText">
                        <% out.write(doc.getDispositionCode());%>
                    </strong>
                    <label><%= tags.getString("documentos.disposition")%></label>
                </div>
            </div>
            <%}%>
            <!-- ##################### DINAMIC FIELDS  ########################## -->
            <div data-dojo-attach-point="dynamicFields" id="dynamicFields" 
                class= "cell grid-x displayNone main-dynfield-container documentRequest-dynamicFields">
            </div>
        
            <!-- ##################### BUTTONS SEE DOCUMENT ########################## -->
            <div class="cell grid-x">
                <% if (doc.getFileId() != null) { %>
                <div id="archivosUp" class="padding-right-1">
                    <%if ((doc.getIntEstado().equals(isoblock.common.Properties.DOC_EN_AUTORIZACION) && !doc.getVchState().equals("Edit")) || (doc.getIntEstado().equals("3") && doc.findSimple("SELECT intdocumentoid from tbldocumento WHERE intestado=" + isoblock.common.Properties.DOC_EN_AUTORIZACION + " AND vchclave='" + doc.getVchClave().replaceAll("'", "''") + "'").isEmpty())) {%>
                    <%if ((sesionusuarioid.equals(doc.getIntAutorId())
                              || sesionroldocumentos.equals(isoblock.common.Properties.DOCUMENTOS_MANAGER))
                              && leo.isEmpty() /*&& doc.getTxtNombreArchivoSistema().isEmpty()*/) {%>
                    <div class="view-document-container button-component outlined-button" style="border:<%=doc.hasReadPending(sesionusuarioid) ? "dotted 1px #F00;": ""%>">
                        <input type="hidden" name='path' value="<%=doc.DOCUMENTS_PATH%>">
                        <input type="file" class="input" name="txtNombreArchivoSistema">
                        <input type="hidden" name="txtNombreArchivoReal" value="<%=doc.getTxtNombreArchivoSistema()%>">
                        <label class="view-document-label"><%=tags.getString("documentos.documentoshandle.Archivo")%></label>
                    </div>
                    <%} %>
                    <%} %>

                    <%
                    logger.trace("***doc.getTxtNombreArchivoSistema(): {}", doc.getTxtNombreArchivoSistema());
                    if (!doc.getTxtNombreArchivoDisponible().isEmpty()) { %>
                        <div id="file">
                            <a href="javascript: window.openDocumentFinder(+'<%= doc.getIntDocumentoId() %>' || null, +'<%= doc.getFileId() %>' || null, {code: '<%= doc.getVchClave() == null ? "" : doc.getVchClave().replaceAll("'", "") %>'});" 
                                id="readLink" title="<%=tags.getString("documentos.documentoslist.Verdocumento")%>"> 
                            <div class="view-document-container button-component outlined-button dijitButtonContents">
                                <s:if test="documentSupported">
                                    <img 
                                        id="readDocument"
                                        border='0' src="<%=doc.getDocumentIcon()%>"
                                        class="view-document-img"
                                        title="<%=tags.getString("documentos.documentoslist.Verdocumento")%>"/>
                                </s:if>
                                <s:else>
                                    <%  String downloadIcon = doc.getDocumentIcon();
                                        downloadIcon = doc.getDocumentIcon().replace("../scripts/framework/bnext/images/default.png","../scripts/framework/bnext/images/download.png");
                                    %>
                                    <img 
                                        id="readDocument"
                                        border='0' src="<%=downloadIcon%>"   
                                        class="view-document-img"
                                        title="<%=tags.getString("documentos.documentoslist.Descargardocumentotitle")%>"/>
                                </s:else>
                                <span class="view-document-label"><%=tags.getString("documentos.documentoshandle.ArchivoActual")%></span>
                            </div>
                            </a>
                        </div>
                    <%} %>
                </div>
                <%
                if (!doc.getTxtNombreArchivoDisponible().isEmpty()) {

                    boolean uncontrolled = "uncontrolled".equals(doc.getDocumento().getDocumentType().getDocumentControlledType());
                    boolean isDepartmentManager = sesionusuarioid.equals(doc.getDocumento().getDepartment() != null ? doc.getDocumento().getDepartment().getAttendantId() + "" : null);
                    boolean isDocumentManager = sesionroldocumentos.equals(propiedadesJsp.DOCUMENTOS_MANAGER);
                    boolean isDocumentEditor = sesionroldocumentos.equals(propiedadesJsp.DOCUMENTOS_EDITOR);
                    boolean isDocumentAuthor = sesionusuarioid.equals(doc.getIntAutorId());
                    boolean readOnlyEnabled = doc.getDocumento().getDocumentType().getReadOnlyEnabled() == 1;
                    SessionViewer sv = new SessionViewer();
                    if (
                            (
                                uncontrolled &&
                                dao.getAllowDownload(
                                    doc.getDocumento().getId(), 
                                    sv.getLoggedUserDto(dao, loggedUserId, isAdmin)
                                )
                            )
                            || (
                                !uncontrolled
                                && (isDocumentAuthor || isDepartmentManager || isDocumentManager || isDocumentEditor)
                                && ( (readOnlyEnabled && isDocumentManager) || !readOnlyEnabled )
                            )
                    ) {
                %>
                <!-- ##################### BUTTONS DOWNLOAD DOCUMENT ########################## -->
                <div id="archivosUpOriginal" class="padding-right-1">
                    <div>
                        <a target='\"_blank\"' href="<%="../view/v-download-document.view?id=" + doc.getDocumento().getId()%>"
                          title="<%=tags.getString("documentos.documentoshandle.Bajardocumento")%>">
                        <div class="view-document-container button-component outlined-button dijitButtonContents">
                            <img 
                                border='0' src="../scripts/framework/bnext/module/images/download.png"
                                class="view-document-img"
                                title="<%=tags.getString("documentos.documentoshandle.Bajardocumento")%>"/>
                            <span class="view-document-label"><%=tags.getString("documentos.documentoshandle.ArchivoOriginal")%></span>
                        </div>
                        </a>
                    </div>
                </div>
                <div id="save-to-drive-li" class="padding-right-1">
                    <div class="save-to-drive-container">
                        <div id="saveToDrive"></div>
                    </div>
                    <label for="saveToDrive"></label>
                </div>
                <% } %>
                <%}%>
                <% } else {%>
                <!-- ##################### BUTTONS SEE DOCUMENT ########################## -->
                <div id="archivosUp" class="padding-right-1">
                    <div>
                        <a target='\"_blank\"' id="readLink"
                           href="../view/v-request-survey-read.view?id=<%=doc.getSurveyId()%>&documentId=<%=doc.getIntDocumentoId()%>&viewFinder=true">
                            <div id ="form" class="view-document-container button-component outlined-button dijitButtonContents" 
                                 style=" border:<%=doc.hasReadPending(sesionusuarioid) ? "dotted 1px #F00;": ""%>">
                                <%if (doc.hasReadPending(sesionusuarioid)) {%>
                                    <font color='red' id='readAlert' size='2'><strong> !</strong></font>
                                <%}%>
                                <img border='0' src="../scripts/framework/bnext/images/form.png"
                                    id="readDocument" class="imgForm"
                                    title="<%=tags.getString("documentos.documentoshandle.Bajardocumento")%>"/>
                                <span class="view-document-label"><%=tags.getString("documentos.documentoshandle.Ver")%></span>
                            </div>
                        </a>
                    </div>
                </div>
            <%}%>
            </div>
            <%if ((doc.getIntEstado().equals(isoblock.common.Properties.DOC_EN_AUTORIZACION) && !doc.getVchState().equals("Edit"))
                    || (doc.getIntEstado().equals("3")
                    && doc.findSimple("SELECT intdocumentoid from tbldocumento WHERE intestado=" + isoblock.common.Properties.DOC_EN_AUTORIZACION + " AND vchclave='" + doc.getVchClave().replaceAll("'", "''") + "'").isEmpty())) {%>
            <%if ((sesionusuarioid.equals(doc.getIntAutorId()) || sesionroldocumentos.equals(isoblock.common.Properties.DOCUMENTOS_MANAGER)) && leo.isEmpty()) {%>
                <div class="cell small-12 medium-4 large-4 textarea-component">
                    <textarea name='vchRazon' class="richTextarea"><%=doc.getVchRazon()%></textarea>
                    <label><%=tags.getString("documentos.documentoshandle.Razondelcambio")%></label>
                </div>
            <%}%>
            <%}%>

            <%if (doc.getVchState().equals("Edit")
                    && !doc.getIntEstado().equals(Document.NO_CONTROL_STATUS.toString())) {%>
            <!--Aqui va el grid component -->
            <div class="cell small-12 medium-12 large-12 padding-top-1">
                <div class="table_workarea">
                    <table id="datagrid_history"></table>
                </div>
            </div>
            <script type="text/javascript">
                require(['bnext/gridComponent', 'dojo/dom'],
                    function(gridComponent, dom) {
                        var grid,
                        requestTypes = [
                            {name: '<%=tags.getString("documentos.documentoshandle.nuevo")%>', value: 1},
                            {name: '<%=tags.getString("documentos.documentoshandle.modificacion")%>', value: 2},
                            {name: '<%=tags.getString("documentos.documentoshandle.editDetails")%>', value: 10},
                            {name: '<%=tags.getString("documentos.documentoshandle.reaprobacion")%>', value: 3},
                            {name: '<%=tags.getString("documentos.documentoshandle.cancelacion")%>', value: 4},
                            {name: '<%=tags.getString("documentos.documentoshandle.tiempo")%>', value: 8},
                            {name: '<%=tags.getString("documentos.documentoshandle.disposicion")%>', value: 9}
                        ],
                        version = {
                            id: 'version',
                            isSortable: false,
                            type: columnTypes.text,
                            title: '<%=tags.getString("documentos.documentoshandle.revision")%>'
                        },
                        reason = {
                            id: 'reason',
                            isSortable: false,
                            type: columnTypes.text,
                            title: '<%=tags.getString("documentos.documentoshandle.Razonde")%>'
                        },
                        createdOn = {
                            id: 'createdOn',
                            isSortable: false,
                            type: columnTypes.date,
                            title: '<%=tags.getString("documentos.documentoshandle.Fechade")%>'
                        },
                        request_type = {
                            id: 'requestType',
                            isSortable: false,
                            type: columnTypes.select,
                            title: '<%=tags.getString("documentos.documentoshandle.tipodesolicitud")%>',
                            list: requestTypes,
                            key: 'name',
                            value: 'value'
                        },
                        verifiedBy = {
                            id: 'verifiedBy.description',
                            isSortable: false,
                            type: columnTypes.text,
                            title: '<%=tags.getString("documentos.documentoshandle.Verificador")%>'
                        },
                        authorizedBy = {
                            id: 'authorizedBy',
                            isSortable: false,
                            type: columnTypes.multipleSelect,
                            key: 'authorizedBy.description',
                            idValue: 'authorizers',
                            title: '<%=tags.getString("documentos.documentoshandle.Aprobador")%>'
                        },
                        makeGrid = function() {
                            grid = new gridComponent({
                                size: 0,
                                id: "grid",
                                container: "datagrid_history",
                                searchContainer: "na",
                                resultsInfo: "#auto",
                                groupBy: 'version',
                                serviceStore: "DocumentHistory.action",
                                methodName: "getNewDocumentHistory",
                                noEyes: true,
                                noExcel: true,
                                noSettings: true,
                                noPagination: true,
                                columns: [request_type, version, reason, createdOn, verifiedBy, authorizedBy]
                            });
                            <%
                                if (
                                    doc.getIntEstado().equals(Document.DISCONTINUED_STATUS.toString()) 
                                    || doc.getIntEstado().equals(Document.CANCELED_DISCONTINUED_STATUS.toString())
                                ) { %>
                            var eq = [
                                {
                                'value': {
                                    'value': dom.byId('masterId').value,
                                    'type': 'string'
                                },
                                'key': 'masterId'
                                }, {
                                'value': {
                                    'value': dom.byId('version').value,
                                    'type': 'string'
                                },
                                'key': 'version'
                            } 
                            ];
                            <%} else {%>
                            var eq = [{
                                'value': {
                                    'value': dom.byId('masterId').value,
                                    'type': 'string'
                                },
                                'key': 'masterId'
                            }];
                            <%}%>
                            grid.setExtraCriteria(eq); 
                            grid.setPageSize(-1);
                            window.documentoshandle.grid = grid;
                        };
                        makeGrid();
                    });
            </script>
            <%}%>
            <%--Se agrego este 'if' para que no aparesca el link de agregar archivos asta que el documento haya pasado por revision--%>
            <% doc.setStrIntNodoId(String.valueOf(doc.getIntDocumentoId()));%>
            <%if (( isAdmin
                || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_LECTOR)
                || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_EDITOR)
                || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_ENCARGADO)
                || services.contains(mx.bnext.access.ProfileServices.SPECIAL_DOCUMENT_READER)
                || services.contains(mx.bnext.access.ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT)
                || services.contains(mx.bnext.access.ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT))) { %>
            <!-- Inicia Despliegue de Archivos -->
            <%logger.trace("-- doc.getIntEstado(): {}", doc.getIntEstado());%>
            <%if (!doc.getIntEstado().equals(Document.IN_AUTORIZATION_STATUS.toString())) { %>
                <% if (doc.getIntEstado().equals(Document.ACTIVE_STATUS.toString())) { %>
                <div class="cell small-12 medium-12 large-12 padding-bottom-1">
                    <div>
                        <span id="lblDocumentos" class="subheader"><%=tags.getString("documentos.documentoshandle.Documentosrelacionados")%></span>
                        <a id="docsbtn" class="Button addToGrid"><%=tags.getString("boton.Mostrar")%></a>
                    </div>
                </div>
                <div class="cell small-12 medium-12 large-12" id="docs" style="display:none;">
                    <div class="rightLinkedGridWithTitles" style="margin: 0 0 10px 0;">
                        <a id="add_related_documents_btn" name="add_related_documents_btn" class="Button addToGrid">
                            <%=tags.getString("documentos.documentoshandle.AgregarDocumentoRelacionado")%>
                        </a>
                    </div>
                    <div class="table_workarea">
                        <table id="datagrid_related_documents"></table>
                    </div>
                </div>
            <%}%>
            <% if (Utilities.getSettings().getReaders() == 1 && doc.getMustRead() == 1 ) {%>      
            <div class="cell small-12 medium-12 large-12 padding-bottom-1" id="readers_label" style="display:none;">
                <div>
                    <span id="lblReaders" class="subheader"><%=tags.getString("documentos.documentoshandle.Listadodeusuarios")%></span>
                    <a id="readersBtn" class="Button addToGrid"><%=tags.getString("boton.Mostrar")%></a>
                </div>
            </div>
            <div class="cell small-12 medium-12 large-12 padding-bottom-1" id="readers_container" style="display:none;">
                <% if (!fromConfiguracion && doc.getIntEstado().equals(Document.ACTIVE_STATUS.toString())) {%>
                <div class="rightLinkedGridWithTitles" style="margin:0 0 10px 0;">
                    <a id="add_readers_btn" class="Button addToGrid">
                    <%=tags.getString("documentos.documentoshandle.AgregarLectores")%>
                    </a>
                </div>
                <%}%>
                <div class="table_workarea">
                    <table id="datagrid_readers_added"></table>
                </div>
            </div>
            <% }%>
            <%if (
                Objects.equals(Utilities.getSettings().getPositionForControlledCopy(), 1)
                && Objects.equals(doc.getCanPrintControlledCopies(), 1)
                && (
                    isAdmin
                    || services.contains(mx.bnext.access.ProfileServices.DOCUMENTO_ENCARGADO)
                    || services.contains(mx.bnext.access.ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT)
                    || services.contains(mx.bnext.access.ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT)
                )
                && doc.getIntEstado().equals(Document.ACTIVE_STATUS.toString()) 
                && Objects.equals(doc.getEnablePdfViewer(), 1)
            ) { %>
            <div class="cell small-12 medium-12 large-12 padding-bottom-1">
                <div>
                    <span id="lblPosition" class="subheader"><%=tags.getString("documentos.documentoshandle.Listadodepuestos")%></span>
                    <a id="show_positions_btn" class="Button addToGrid"><%=tags.getString("boton.Mostrar")%></a>
                </div>
            </div>
            <div class="cell small-12 medium-12 large-12 padding-bottom-1" id="positions_container" style="display:none;">
                <div class="rightLinkedGridWithTitles" style="margin:0 0 10px 0;">
                    <a id="add_positions_btn" class="Button addToGrid"><%=tags.getString("documentos.documentoshandle.Agregarpuestos")%></a>
                </div>
                <div class="table_workarea">
                    <table id="datagrid_position_added"></table>
                </div>
            </div> 
            <% }%>
        <% }%>
    <% } %>
            <div class="access_permission_reason_container">
                <span id="lblAccessPermissionReason"><%=tags.getString("documentos.documentoshandle.AccessPermissionReason")%></span>
                <div class="information access_permission_reason">
                    <span id="accessPermissionReason"><%=new DocumentHelper(tagsParam.getLocale()).getAccessPermissionReason(Long.decode(iddoc), Long.decode(sesionusuarioid), isAdmin, services)%>. </span>
                </div>
            </div>
            <!-- Finaliza Despliegue de Archivos -->
            <div class="separator cell small-12 large-12 ">
                <ul class="actionButtons" id="actionButtonsId">
                    <%if (doc.getVchState().equals("Edit")) { %>
                    <%if (doc.getIntEstado().equals("3") && doc.findSimple("SELECT intdocumentoid from tbldocumento WHERE intestado=" + isoblock.common.Properties.DOC_EN_AUTORIZACION + " AND vchclave='" + doc.getVchClave().replaceAll("'", "''") + "'").isEmpty()) {
                            if ((sesionusuarioid.equals(doc.getIntAutorId())
                                    || sesionroldocumentos.equals(isoblock.common.Properties.DOCUMENTOS_MANAGER)) && leo.isEmpty()) {
                          boton2 = tags.getString("boton.Cancelar");%>
                    <li><input type="button" class="button" name="Submit" value="<%=boton1%>" onClick="save()"></li>
                    <script type= "text/javascript"> document.form1.intOriginadorId.disabled = false;</script>
                    <% }
               } else if (sesionroldocumentos.equals(isoblock.common.Properties.DOCUMENTOS_MANAGER)
                       && doc.parameter("aceptarEntra", request).isEmpty() && false) {%>
                    <li><input type="button" class="button" name="Submit" value="<%=boton1%>" onClick="cambiarTituloClave()"></li>
                    <script type= "text/javascript">
                        document.form1.vchClave.disabled = false;
                        document.form1.vchTitulo.disabled = false;
                    </script>
                    <%} %>
                    <%} %>
                    <li><input type="button" class="button raised-button" name="Back" id="Back" value="<%=boton2%>"></li>
                        <%  if (!state.equals("Edit")) {%>
                    <li><input type="reset" class="button" name="instClear" value="<%=tags.getString("boton.LimpiarForma")%>" onClick="limpiaColor();"></li>
                        <%  }%>
                </ul>
            </div>
        </div>
        <div style = "display:none">
        <div dojoType="dijit.Dialog" id="readers_dialog" class="fixedTop">
            <ul class="content_area">
                <li tabindex="0">
                    <table id="datagrid_readers_to_add"></table>
                </li>
                <li class="actionButtons separator" style="padding-bottom: 0px !important;">
                    <input class="Button right finishBtn raised-button" type="button" id="close_readers_btn" value="<%=tags.getString("documentos.documentoshandle.Terminar")%>"/>
                    <input class="Button right" type="button" id="add_all_readers_btn" value=""/>
                </li>
            </ul>
        </div>

        <div dojoType="dijit.Dialog" id="positions_dialog">
            <ul class="content_area">
                <li tabindex="0">
                    <table id="datagrid_position_to_add"></table>
                </li>
                <li class="actionButtons separator" style="padding-bottom: 0px !important;">
                    <input class="Button right finishBtn raised-button" type="button" id="close_positions_btn" value="<%=tags.getString("documentos.documentoshandle.Terminar")%>"/>
                </li>
            </ul>
        </div>
    </div>

    <!-- Auxilar Field-->
    
    <input type="hidden" id="color_sistema" value="${systemColor}">
    <input type="hidden" name="intDocumentoId" value="<%=doc.getIntDocumentoId()%>">
    <input type="hidden" name="vchState" value="<%=doc.getVchState()%>">
    <input type="hidden" name="strTitulo" value="<%=doc.getVchTitulo()%>">
    <input type="hidden" name='documentoBorrar' value=''>
    <input type="hidden" name="pop" value="<%=doc.parameter("popUp", request)%>">
    <input type="hidden" name="intNodoId" value="<%=doc.getIntNodoId()%>">
    <input type="hidden" name="intNodoIdaux" value="<%=doc.getIntNodoIdaux()%>">
    <input type="hidden" name="intRepositorioId" value="<%=doc.getIntRepositorioId()%>">
    <input type="hidden" name="intAutorId" value="<%=doc.getIntAutorId()%>">
    <input type="hidden" name="intSolicitudId" value="<%= intSolicitudId%>">
    <input type="hidden" name="floatingGridSize" id="floatingGridSize" value="<%= session.getAttribute("floatingGridSize")%>">
    <input type="hidden" name='page'>
    <input type="hidden" name='actionType' value=''>
    <input type="hidden" name='className' value=''>
    <input type="hidden" name='sectionName' value=''>
    <input type="hidden" name='editPage' value=''>
    <input type="hidden" name='addPage' value=''>
    <input type="hidden" name='msgAction' value=''>
    <input type="hidden" name='msgButton1' value=''>
    <input type="hidden" name='msgButton2' value=''>
    <input type="hidden" name='strEstadoJsp' value=''>
    <input type="hidden" name='strSecuenciaId' value=''>
    <%-- Inicio variables javascript- componente archivos -Variables para listas --%>
    <input type="hidden" name='controlListas' value="">
    <%-- Variables para despliegue de archivos --%>
    <input type="hidden" name="verA" value="<%=doc.parameter("verA", request)%>">
    <input type="hidden" name="fromConfiguracion" id="fromConfiguracion" value="<%=fromConfiguracion%>">
    <input type="hidden" id="editRelatedDocuments" value="${editRelatedDocuments}"/>
    <input type="hidden" id="editReaders" value="${editReaders}"/>
    <input type="hidden" id="editRetention" value="${editRetention}"/>
    <input type="hidden" id="editCopyPositions" value="${editCopyPositions}"/>
    <%=doc.getClaveDeForma("documentos.documentoshandle")%>
</form>
</div>

<%try {%>
<%doc.finalize();
%>
<%} catch (Throwable t) {
        t.printStackTrace();
    }%>
<%--  Finaliza el cuerpo del JSP  --%>
<form name="form2" id="form2" method="post" action="">
    <input type="hidden" id="documentId" value="<%=doc.getDocumento().getId()%>">
    <input type="hidden" id="documentName"  value="<%= doc.getTxtNombreArchivoSistema()%>">
    <input type="hidden" name="intDocumentoId" value="<%=doc.getIntDocumentoId()%>">
    <input type="hidden" name="vchState" value="<%=doc.getVchState()%>">
    <input type="hidden" name="intNodoId" value="<%=doc.getIntNodoId()%>">
    <input type="hidden" name="intRepositorioId" value="<%=doc.getIntRepositorioId()%>">
    <input type="hidden" name="intAutorId" value="<%=doc.getIntAutorId()%>">
    <input type="hidden" name="intSolicitudId" value="<%= intSolicitudId%>">
    <input type="hidden" name="intTipoDocumento" value="<%= doc.getIntTipoDocumento()%>">
    <input type="hidden" name="vchClave" value="<%= doc.getVchClave()%>">
    <input type="hidden" name="vchTitulo" value="<%= (doc.getVchTitulo().replaceAll("\"", "&quot;"))%>">
    <select multiple size="2" name="idSavedUSRSelectorUsuario" id="idSavedUSRSelectorUsuarioForm2" style="display: none"></select>
    <input type="hidden" name='actionType' value=''>
    <input type="hidden" name='className' value=''>
    <input type="hidden" name='sectionName' value=''>
    <input type="hidden" name='editPage' value=''>
    <input type="hidden" name='addPage' value=''>
    <input type="hidden" name='msgAction' value=''>
    <input type="hidden" name='msgButton1' value=''>
    <input type="hidden" name='msgButton2' value=''>
    <input type="hidden" name='strEstadoJsp' value=''>
    <input type="hidden" name='strSecuenciaId' value=''>
    <input type="hidden" id="googleDriveIntegration" value="${googleDriveIntegration}"/>
    <input type="hidden" id="hasRetention" value="<%=doc.getIntLugarAlmacenamiento() != "0" ? "1" : "0"%>">
    <input type="hidden" id="hasDisposition" value="<%=doc.getHasDisposition()%>">
</form>
<input type="hidden" id="loggedUserBusinessUnitName" value="${loggedUserBusinessUnitName}"/>
<%@ include file="../../templates/inferior.tem"%>