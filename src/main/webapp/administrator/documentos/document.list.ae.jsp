<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<s:set value="documentRole" var="documentRole" />
<s:set value="businessUnitsArray" var="businessUnitsArray" />
<s:set value="userId" var="userId" />
<!DOCTYPE html>
<html class="html-reset">
  <head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
    <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
    <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
    <jsp:include page="../../components/requiredScripts.jsp" />
    <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/styles/Tabs.css?${systemVersion}" />
    <link href="../scripts/framework/bnext/administrator/documentos/style/document.list.ae.css?${systemVersion}" rel="stylesheet" type="text/css"/>
    <script src="../scripts/Jssor/jssor.slider-21.1.5.mini.js?${systemVersion}" type="text/javascript"></script>
    <link href="../scripts/Jssor/jssor.slider-21.1.5.css?${systemVersion}" rel="stylesheet" type="text/css"/>
    <script type='text/javascript' src="../administrator/documentos/controller/document.list.ae.js?${systemVersion}"></script>
    <s:set var="documentRole" value="documentRole"></s:set>
    <style type="text/css">
        #buttons img{
          cursor: pointer;
          width: 22px;
          height: 22px;
        }
        #path{
            width:100%;
            border:0;
            background: white;
            font-family: 'TypoPRO Titillium Text', sans-serif;
            font-size: 16px;
        }
        div.info.gridInfo {
            width: 100%;
        }
        td.css_edit {
            text-align: center;
        }
        .contentTitle .folder-container:hover a,
        .folder-container:hover .subfolder-container,
        .folder-container:hover .subfolder-container .dijitButtonNode,
        .folder-container:hover .subfolder-container .dijitButtonNode .dijitButtonContents,
        .subFolderItem.dijitMenuItemHover,
        .subFolderItem.dijitMenuItemSelected {
            background-color: ${systemSecondaryColor}!important;
            color: ${systemSecondaryColorTextColor} !important;
        }
        li .actionButtons {
            margin-top: -15px;
        }
        .documentNodes {
            margin-bottom: 4rem;
        }
    </style>
  </head>
  <body writingsuggestions="false" textprediction="false" class="documentListAE">
    <%@include file="../../components/loader.jsp" %>
    <div class="grid-container grid-floating-active full-width-grid-component">
        <div class="grid-x">
            <div class="cell">
                <form method="POST" class="container-form grid-floating-action-buttons">
                    <div class="header grid-container">
                      <div class="grid-x content_title">
                        <div id="pathBar"></div>
                        <div class="cell position-relative">
                            <div class="grid-x">
                                <div class="cell small-10 medium-10 large-11">
                                    <div class="igx-card-header__title window-title contentTitle" id="titleBar"></div>
                                </div>
                                <div class="cell small-2 medium-2 large-1">
                                    <div class="float-right">
                                        <a href="javascript: void(0);" id="addFavorite" class="material-icons" title="Add shortcut">
                                            favorite
                                        </a>
                                        <a href="javascript: void(0);" id="moreOptions" class="material-icons" title="Cofiguration">
                                            more_vert
                                        </a>
                                    </div>
                                </div>
                            </div>  
                        </div>
                      </div>
                    </div>
                    <div class ="grid-container">
                      <ul class="grid-x grid-padding-x content_area repository">
                        <li><div id="search" ></div></li>
                        <li><div id="tabNode" ></div></li>
                        <li><table id="dataGrid_control"></table></li>
                        <li><table id="dataGrid_folders"></table></li>
                        <li><table id="dataGrid_noControl"></table></li>
                        <li><table id="dataGrid_media"></table></li>
                        <li><table id="dataGrid_mediaVideos"></table></li>
                        <li><table id="dataGrid_reference"></table></li>
                        <li><div id="pagination"></div></li>
                        <li>                        
                            <div class="actionButtons">
                                <input type="button" class="button raised-button" name="goBack" id="goBack">
                            </div>
                        </li>
                      </ul>
                    </div>

                  <div dojoType="dijit.Dialog" id="new_folder_dialog" class="dialog-new-folder">
                    <div class ="grid-container grid-floating-active">
                        <div class="grid-x grid-padding-y">
                            <div class="cell small-11 medium-12 large-12">
                                <div class="textarea-component">
                                    <input type="text" name="fld_description" id="fld_description" value=""/>  
                                    <label id="lblFolderName"></label>
                                </div>
                            </div>
                            <div class="cell small-11 medium-12 large-12 actions-buttons">
                              <input class="dialog-button" type="button" id="cancel_new_folder" value="Cancelar"/>  
                              <input class="dialog-button raised-button" type="button" id="add_new_folder" value="Agregar"/>
                            </div>
                        </div>
                    </div>
                  </div>
                  <%@include file="../../components/sessionVariables.jsp" %>
                  <input type="hidden" id="window_title_repository" value="title"/>
                  <input type="hidden" name="nodo" id="nodo" />
                  <input type="hidden" name="nodoPadre" id="nodoPadre"/>
                  <input type="hidden" name="repositorio" value="Edit" />
                  <input type="hidden" name="vchState" value="Edit" />
                  <input type="hidden" name="strEstado" value="editar" />
                  <input type="hidden" name="intDocumentoId" />
                  <input type="hidden" name="intImageId" />
                  <input type="hidden" id="onBtnAdd" value="#" />
                  <input type="hidden" id="onBtnControl" value="#" />
                  <input type="hidden" id="documentRole" value="${documentRole}" />
                  <input type="hidden" id="businessUnitsArray" value="${businessUnitsArray}" />
                  <input type="hidden" id="userId" value="${userId}" />
                  <textarea id="serializedShareFileTypes" class="displayNone">${serializedShareFileTypes}</textarea> 
                  <textarea id="serializedExternalFileTypes" class="displayNone">${serializedExternalFileTypes}</textarea> 
                  <textarea id="serializedDocumentTypes" class="displayNone">${serializedDocumentTypes}</textarea> 
                  <textarea id="serializedVideoCompatibilityTypes" class="displayNone">${serializedVideoCompatibilityTypes}</textarea>
                  <input type="hidden" id="canShareDocument" value="${canShareDocument}"/>
                  <input type="hidden" id="showExternalDialog" value="${showExternalDialog}"/>
                  <input type="hidden" id="externalLink" value="${externalLink}"/>
                  <input type="hidden" id="onlyDocumentManagerReapprove" value="${onlyDocumentManagerReapprove}" />
                  <input type="hidden" id="documentRole" value="${documentRole}" />
                  <input type="hidden" id="documentEditor" value="${documentEditor}" />
                  <input type="hidden" id="documentManager" value="${documentManager}" />
                  <input type="hidden" id="documentReader" value="${documentReader}" />
                  <input type="hidden" id="isAdmin" value="${admin}" />
                  <s:if test="admin
                        || loggedUserServices.contains(@mx.bnext.access.ProfileServices@DOCUMENTO_ENCARGADO)">
                        <input type="hidden" value="true" id="redirectAccess"/>   
                  </s:if>      
                </form>
            </div>
        </div>
    </div>
    <%@include file="../../components/footer.jsp" %>
  </body>
</html>
