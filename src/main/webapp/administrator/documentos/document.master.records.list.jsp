<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/administrator/solicitudes/style/request.list.css?${systemVersion}">
        <jsp:include page="../../components/requiredScripts.jsp" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/styles/Tabs.css?${systemVersion}" />
        <script type='text/javascript' src="../administrator/documentos/controller/document.master.records.list.js?${systemVersion}"></script>
        <style>
        @media print, screen and (max-width: 39.9375em){
           .floating-action-buttons{
                width: 0 !important;
                height: 0 !important;
                padding: 0 !important;
                margin: 0 !important;
                min-height: 0 !important;
                max-height: 0 !important;
            }
        }
        </style>
    </head>
<body writingsuggestions="false" textprediction="false">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="window_title">
                                    </h3>
                                    <div class="float-right">
                                        <!-- boton de opciones -->
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class ="grid-container">
                            <ul class="grid-x grid-padding-x content_area">
                                <li><div id="search"></div></li>
                                <li><table id="dataGrid_ALL"></table></li>
                                <li><div id="pagination"></div></li>
                            </ul>
                        </div>
                        <div class ="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                            <input type="hidden" name="intSolicitudId" id="intSolicitudId" />
                            <input type="hidden" id="onlyDocumentManagerReapprove" value="${onlyDocumentManagerReapprove}" />
                            <input type="hidden" id="documentRole" value="${documentRole}" />
                            <input type="hidden" name="strEstado" value="editar" />
                            <input type="hidden" name="lector" value="si" />
                            <input type="hidden" name="ver" value="si" />
                            <input type="hidden" name="fuente" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>