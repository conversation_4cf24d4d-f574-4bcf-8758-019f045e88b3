<%@page import="Framework.Config.Utilities"%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title></title>

        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <jsp:include page="../../components/requiredScripts.jsp" />
        <!--<script type='text/javascript' src="../administrator/documentos/controller/document.master.list.js?${systemVersion}"></script>-->
        <%= Utilities.versionedScriptTag("../controller/c.document.master.list.controller")%>
        <style type="text/css">
            td.css_edit {
                text-align: center;
            }
            .content_title {
                padding-bottom: 0px;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons" action="../view/v.documentos.view">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="window_title">
                                    </h3>
                                    <div class="float-right">
                                        <!-- boton de opciones -->
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class ="grid-container">
                            <table id="dataGrid"></table>
                        </div>
                        <div class ="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                            <input type="hidden" name="intDocumentoId" id="intDocumentoId" />
                            <input type="hidden" name="vchState" id="vchState" value="Edit" />
                            <input type="hidden" name="nodo" id="nodo" />
                            <input type="hidden" name="repositorio" id="repositorio" />
                            <input type="hidden" name="leer" value="si" />
                            <input type="hidden" name="onClose" id="onClose" value="history.back()" />
                            <textarea id="serializedShareFileTypes" class="displayNone">${serializedShareFileTypes}</textarea> 
                            <textarea id="serializedExternalFileTypes" class="displayNone">${serializedExternalFileTypes}</textarea> 
                            <textarea id="serializedDocumentTypes" class="displayNone">${serializedDocumentTypes}</textarea> 
                            <input type="hidden" id="canShareDocument" value="${canShareDocument}"/>
                            <input type="hidden" id="showExternalDialog" value="${showExternalDialog}"/>
                            <input type="hidden" id="externalLink" value="${externalLink}"/>
                            <input type="hidden" id="onlyDocumentManagerReapprove" value="${onlyDocumentManagerReapprove}" />
                            <input type="hidden" id="documentRole" value="${documentRole}" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>