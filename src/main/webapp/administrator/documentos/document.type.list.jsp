<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Tipos de documento</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script type='text/javascript' src="../controller/c.document.type.list.controller?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../components/loader.jsp" %>
        
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="window_title">
                                        Mis actividades
                                    </h3>
                                    <div class="float-right">
                                     
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                            <div id="buttonsSection">
                               <div id="btnAdd" class="fab-button">
                                    <i class="material-icons">add</i>
                                    <span id="catalogoLabel"></span>
                                </div>
                            </div>
                        </div>
                        <div class ="grid-container">
                            <table id="dataGrid"></table>
                        </div>
                        <div class ="displayNone">
                            <s:hidden name="serializedStatusList" id="serializedStatusList" />  
                        </div>
                    </form>
                </div>
            </div> 
                            <%@include file="../../../components/sessionVariables.jsp" %>
        </div>  
       
        <form name="log_form" action="v.logging.view">
            <input type="hidden" id="record_id" name="record_id" value=''/>
            <input type="hidden" id="logElement" name="logElement" value='DocumentType'/>
        </form> 
        <input type="hidden" id="shareDocuments" value="${shareDocuments}"/>
        <input type="hidden" id="externalLink" value="${externalLink}"/>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>
