require(['core', 'bnext/gridComponent','bnext/Tabs','dojo/dom', 'bnext/gridComponentUtil', 'bnext/gridIcons', 'dojo/domReady!'],
function(core, gridComponent,Tabs,dom, gcUtil, gridIcons){
    var form = document.forms[0], cType = gcUtil.cType, columns = [],
        isAdmin = dom.byId('isAdmin').value === 'true', userId = dom.byId('userId').value;
    function doEverything(i18n) {
        var grid_participa, grid_noParticipa,
        statusList = [
            {name:'--SELECCIONE--',icon:'',value:''},
            {name:'<PERSON>reado', icon: gridCubes.yellow,value:1},
            {name:'Liberado',icon: gridCubes.green,value:2},
            {name:'Concluido',icon: gridCubes.blue,value:3},
            {name:'<PERSON><PERSON><PERSON>',icon: gridCubes.gray,value:4},
            {name:'<PERSON>cel<PERSON>',icon: gridCubes.black,value:5}
        ];
        function editRecord(id){
            form.strIntProyectoId.value = id;
            form.submit();
        }
        function cancel_project(id) {
             core.dialog(i18n.mensajes.cancelacion, i18n.mensajes.accept, i18n.mensajes.cancel).then(function() {
                showLoader();
                var args = {
                  url:'Project.action',
                  method:'cancel',
                  params:[id]
                };
                callMethod(args).then(function(resultado) {
                  hideLoader();
                  if (+resultado!==0) {
                    dialog(i18n.mensajes.exito, i18n.mensajes.accept).then(function() {
                      grid_participa.refreshData();
                    });
                  }
                },function(e) {
                  dialog(e, i18n.mensajes.accept);
                });
              });
        };
        gcUtil.column(columns)
            .push('status', i18n.colNames.status, cType().FunctionImage(['status'], null, statusList, true))
            .push('edit', i18n.colNames.edit, cType().Function(['id'], editRecord, gridIcons.edit), null, '50px')
            .push('cancelar',i18n.colNames.cancelar, cType().FunctionImageEval(["full-row"], function(proyect){cancel_project(proyect.id)}, 
                [
                    {
                        'icon': gridIcons.remove, 
                        'Function': 
                            function(e) {
                                return +e.status === 1 && (isAdmin || e.author.id === +userId);
                            }, 
                        name: i18n.colNames.cancelar
                    }
                ]), null, '100px')
            .push('title', i18n.colNames.title, cType().Text(), true, '300px')
            .push('author.description', i18n.colNames.author, cType().Text(), true, '250px')
            .push('fechaInicio', i18n.colNames.fInicio, cType().Date(true), true, '200px')
            .push('fechaFin', i18n.colNames.fFin, cType().Date(true), true, '200px')
            .push('detail.presupuestoTotal', i18n.colNames.presupuesto, cType().RenderCell(
                function(o){
                    return '$' + (o.detail.presupuestoTotal || '0.00');
                }), true, '250px');
        var grid_participa = new gridComponent({
            size:dom.byId('gridSize').value,
            id:"grid_participa", 
            container:"dataGrid_participa",
            searchContainer:"#auto",
            resultsInfo:"#auto", 
            paginationInfo:"#auto", 
            serviceStore:"ProjectRows.action", 
            methodName:"getRowsParticipa", 
            windowPath: dom.byId('windowPath').value,
            columns : columns
        }),
        grid_noParticipa = new gridComponent({
            size:dom.byId('gridSize').value,
            id:"grid_noParticipa", 
            container:"dataGrid_noParticipa",
            searchContainer:"#auto",
            resultsInfo:"#auto", 
            paginationInfo:"#auto", 
            serviceStore:"ProjectRows.action", 
            methodName:"getRowsNoParticipa", 
            windowPath: dom.byId('windowPath').value,
            columns:columns
        }),
        tabs = new Tabs({
            tabs:[
                {title: i18n.colNames.participante, grid:grid_participa},
                {title: i18n.colNames.noParticipante, grid:grid_noParticipa}
            ],
            defaultWidth:'auto',
            searchNode:'search',
            paginationNode:'pagination'
        },'tabNode');
        hideLoader();
    };
    core.setLang('lang/proyectos/nls/proyect.list').then(doEverything);
});