require(['core', 'bnext/gridComponent', 'dojo/dom',"dojo/_base/array", 'dojo/domReady!']
	, function (core, gridComponent, dom, array) {
    var grid,form= document.forms[0],userId = document.getElementById('userId').value,_GET = getUrlVars(),
    statusList = [
        {name:'--SELECCIONE--', icon:'', value:''},
        {name:'<PERSON><PERSON><PERSON>', icon: gridCubes.yellow, value:1},
        {name:'En proceso', icon: gridCubes.green, value:2},
        {name:'Concluida', icon: gridCubes.orange, value:3},
        {name:'Verificada', icon: gridCubes.blue, value:4}
    ],
    status = {
        type: columnTypes.imageMap,
        list:statusList,
        id:'status',
        searchObj:{
            type:'combo',
            col:1,
            list:statusList
        }
    },
    edit = {
        type: columnTypes['function'],
        id:'edit',
        parameters:['id','status'],
        action:function(id,status){
            form.strIntActividadId.value = id;
            form.action = status === statusList[3].value ? //concluida
            '../view/v.proyectos.actividades.verifica.view': 
            '../view/v.proyectos.actividades.avance.view';
            form.submit();
        }
    },
    proyect = {
        id:'goal.proyectGoal.proyect.title',
        type:1
    },
    activity = {
        id:'code',
        type:1
    },
    role = {
        id:'role',
        type:1,
        renderCell:function(c){
            return userId === c.responable.id && c.status === statusList[3].value  ? 'Responsable Verificador' : 'Verificador';
        }
    },
    goal = {
        id:'goal.code',
        type:1
    },
    fInicio = {
        id:'fechaInicio',
        type: columnTypes.date
    },
    fFin = {
        id:'fechaFin',
        type: columnTypes.date
    },
    makeGrid = function(){
        var methodName = "getRows"+_GET["tipoJsp"];
        grid = new gridComponent({
            size:15,
            id:"grid", 
            container:"dataGrid",
            searchContainer:"#auto",
            resultsInfo:"#auto", 
            paginationInfo:"#auto",
            windowPath: dom.byId('windowPath').value,
            serviceStore:"Project.action", 
            methodName:methodName, 
            columns : [status,edit,proyect,goal,activity,fInicio,fFin]
        });
        grid.setPageSize(15);
        core.hideLoader();
    },
    setColNames = function(i18n){
        status.title = i18n.colNames.status;
        edit.title = i18n.colNames.edit;
        proyect.title = i18n.colNames.proyect;
        goal.title = i18n.colNames.goal;
        activity.title = i18n.colNames.activity;
        fInicio.title = i18n.colNames.fInicio;
        fFin.title = i18n.colNames.fFin;
        array.forEach(i18n.statusNames,function(t,i){
            statusList[i].name = t;
        });
    };
    core.setLang('lang/proyectos/nls/my.activity').then(setColNames).then(makeGrid);
});