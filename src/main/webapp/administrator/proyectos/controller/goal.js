require(['bnext/filesAttacher/core', 'dojo/dom-construct', 'dojo/dom', 'bnext/callMethod','core','dojo/domReady!'],
    function(fileAttacher, domConstruct, dom ,callMethod, core)
    {
        core.hideLoader();
        
        var boolAlta = dom.byId('boolAlta').value === "true";
        if(boolAlta){
            return;
        }
        var estadoProyecto = dom.byId('estadoProyecto').value;
        var 
        hasDelete = dom.byId('strIntEstado').value === '3' || estadoProyecto ==='4' ? false : true,
        syncDocs = function(){
            if(dom.byId('strIntEstado').value != 0) {
                var args = {
                    url:"../DPMS/Project.action",
                    method:"saveGoalFiles",
                    params:[dom.byId('strIntMetaId').value,fA.get('value')]
                }
                callMethod(args);
            }
        },
        fA=new fileAttacher({
            serviceStore:'../DPMS/Project.action',
            currentEntity:dom.byId('strIntMetaId').value,
            methodName:'getGoalDocuments',
            showLoader:showLoader,
            hideLoader:hideLoader,
            hasDelete:hasDelete,
            hasUpload:hasDelete,
            uploadPath:'../DPMS/Upload.fileUploader',
            files :dom.byId('idDocs').value,
            onFileAdded: syncDocs,
            onFileRemoved: syncDocs
        },'fileAttacher');
        domConstruct.place("trapped", "trap", "replace");
    });
    