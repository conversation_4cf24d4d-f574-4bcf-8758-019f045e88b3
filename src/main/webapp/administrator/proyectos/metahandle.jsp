<%@page import="qms.project.logic.ProjectHelper"%>
<%@page import="Framework.DAO.IUntypedDAO"%>
<%/* ======================================================================
     Proyecto ISOBLOCK 
     NOMBRE: metahandle.jsp
     AUTOR: Rodrigo <PERSON> Prado
     COMPAÑIA: Block Networks.
     FECHA  : 01/23/2005
     DESCRIPCION FUNCIONAL: Alta o modificacion de metas, lista de actividaes,
     lista de documentos de metas.
     ====================================================================== */%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<% request.setCharacterEncoding("UTF-8");%>
<%@ include file="../../includes/getsesionusuario.jsp"%>
<%--  Declaración de los Java Bean --%>
<jsp:useBean id="meta" scope="request" class="isoblock.proyectos.Meta"/>
<jsp:useBean id="actividad" scope="request" class="isoblock.proyectos.Actividad"/>
<%--  Asignación de propiedades de los Java Beans --%>
<jsp:setProperty name="meta" property="*" />
<jsp:setProperty name="actividad" property="*" />
<%-- Includes --%>
<%@ include file="../../templates/superior.jsp"%>
<%@ include file="../../includes/titulotabla.jsp"%>
<jsp:include page="../../components/requiredScripts.jsp" />
<script type='text/javascript' src="../controller/c.goal.controller?${systemVersion}"></script>
<link rel="stylesheet" type="text/css" href="../administrator/proyectos/style/project.css?${systemVersion}">
<%

    boolean boolResponsable = false;    // Determina si es Responsable
    boolean boolVerificador = false;    // Determina si es Verificador

    boolean boolAlta = false;           // Determina si se está dando de alta
    boolean boolEditar = false;         // Determina si se puede editar la meta
    boolean boolAltaAct = false;        // Determina si se puede dar de alta actividades

    boolean boolMsgMeta = false;        // Determina si es necesario mostrar mensaje referente a una meta
    boolean boolMsgActividad = false;   // Determina si es necesario mostrar mensaje referente a una actividad
    boolean boolMsgDoc = false;         // Determina si es necesario mostrar mensaje referente a un documento
    boolean boolMsgPro = false;         // Determina si es necesario mostrar mensaje referente al Proyecto

    String strIntEstadoMeta = "0";      // Guarda el estado de la meta
    String strIntEstadoProyecto = "0";  // Guarda el estado del proyecto

    String strHeader = "";              // Guarda el header de la forma

    String strMsgPro = "";              // Guarda el mensaje a desplegar referente al Proyecto
    String strMsgMeta = "";             // Guarda el mensaje a desplegar referente a una meta
    String strMsgDoc = "";              // Guarda el mensaje a desplegar referente a un documento

    String strProyectoId;            // Guarda el ID del proyecto
    String strBtnCancelar = "";         // Indica hacia donde dirige el botón cerrar

    String strSql = "";                 // Guarda el sql para hacer consultas
    String nextPage;                    // Guadra la siguiente página a desplegar
    String actionType;                  // Guadra el método para guardar el Proyecto
    String vchState;                    // Guarda el estado del JSP:
    //   "guardar" = Guarda el proyecto
    //   "guardarSalir" = Guarda y se sale de la forma
    //   "guardarNext" = Guarda y dirige a la siguiente página
    //   "soloNext" = Dirige a la siguiente página si guardar
    //   "reload" = Indica que hizo un reload
    //   "borrarMeta" = Borrar una meta
    //   "borrarDoc" = Borrar un documento

    vchState = meta.parameter("vchState", request);
    nextPage = meta.parameter("nextPage", request);
    actionType = meta.parameter("actionType", request);
    
    IUntypedDAO dao = Utilities.getUntypedDAO(session.getServletContext());
    ProjectHelper bean = new ProjectHelper(dao);

    if (vchState.equals("guardar") || vchState.equals("guardarNext") || vchState.equals("guardarSalir")) {
// Con estos estados, se guarda el Proyecto con el método indicado...
        strMsgPro = tags.getString("proyectos.metahandle.javascript.LaMetaNoPudoActualizarseCorrectamente");
        if (actionType.equals("insertaMeta")) {
            if (!meta.insertaMeta()) {
                //strMsgPro = tags.getString("proyectos.metahandle.javascript.LaMetaHaSidoDadoDeAlta");
                boolMsgPro = true;
                meta.setStrIntEstado("0");
            }
        } else if (actionType.equals("actualizaMeta")) {
            if (!meta.actualizaMeta()) {
                //strMsgPro = tags.getString("proyectos.metahandle.javascript.LaMetaHaSidoActualizada");
                boolMsgPro = true;
            }
        }
    }

    strBtnCancelar = "../view/v.proyectos.view?strIntProyectoId=" + meta.getStrIntProyectoId();

// Ejecuta la acción guardada en vchState
    if (boolMsgPro) {
// Si ocurrió un error, toma los datos anteriores (automáticamente) y hace nada para desplegar después el error
    } else if (vchState.equals("guardar")) {
// Si pudo ser guardado, toma los nuevos datos del proyecto
        meta.datosMeta();
    } else if (vchState.equals("guardarNext") || vchState.equals("soloNext")) {%>
      <script type="text/javascript">window.location.href = "<%= nextPage%>"</script>
      <%
// Manda a la siguiente página especificada
        //response.sendRedirect(nextPage);
    } else if (vchState.equals("guardarSalir")) {
// Se sale del jsp
        response.sendRedirect(strBtnCancelar);
    } else if (vchState.equals("borrarDoc")) {
// Borra el documento especificado
        if (!meta.borrarDocumento(meta.parameter("intDocumentoId", request), meta.getStrIntNodoId())) {
            strMsgDoc = tags.getString("proyectos.metahandle.javascript.ElDocumentoNoPudoSerBorrado");
            boolMsgDoc = true;
        }
    } else if (vchState.equals("borrarActividad")) {
// Borra la meta especificada
        if (!actividad.borrarActividad(meta.parameter("strIntActividadId", request))) {
            // Si no se pudo borrar la meta...
            strMsgMeta = tags.getString("proyectos.metahandle.javascript.LaActividadNoPudoSerBorrada");
            boolMsgMeta = true;
        }
    } else if (!vchState.equals("reload")) {
// Si no se hizo un reload, toma los datos de la clase
        meta.datosMeta();
    }
    strProyectoId = meta.getStrIntProyectoId();

    strIntEstadoProyecto = meta.findSimple("SELECT intestado FROM tblproyecto WHERE intproyectoid = " + strProyectoId);
    strIntEstadoMeta = meta.getStrIntEstado();
    System.out.println("Proyecto ID: " + strProyectoId + "; Estado del Proyecto: " + strIntEstadoProyecto + "; Estado de la Meta: " + strIntEstadoMeta);

    if ((sesionrolproyectos.equals(isoblock.common.Properties.PROYECTOS_MANAGER) 
         || meta.findSimple("SELECT intautorid FROM tblproyecto WHERE intproyectoid = "+strProyectoId).equals(sesionusuarioid)) 
         && strIntEstadoMeta.equals("0")
         && (strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_CREADO) 
         || strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_LIBERADO) 
         || strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_CONCLUIDO))) {
// Únicamente el ENCARGADO y el autor del proyecto pueden dar de alta cuando el estado del proyecto es CREADO o LIBERADO
        boolAlta = true;
    } else if ((sesionrolproyectos.equals(isoblock.common.Properties.PROYECTOS_MANAGER) 
                || meta.findSimple("SELECT intautorid FROM tblproyecto WHERE intproyectoid = "+strProyectoId).equals(sesionusuarioid)) 
                && strIntEstadoMeta.equals(isoblock.common.Properties.MET_CREADA) 
                && (strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_CREADO) 
                || strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_LIBERADO))) {
// Permite editar sólo si es el autor del proyecto o el ENCARGADO de proyectos y aun no está liberado el proyecto
        boolEditar = true;
    }
// Si es ENCARGADO y el autor del proyecto pueden dar de alta cuando el estado del proyecto es CREADO o LIBERADO, puede dar de alta actividades
    boolAltaAct = (sesionrolproyectos.equals(isoblock.common.Properties.PROYECTOS_MANAGER) 
                   || meta.findSimple("SELECT intautorid FROM tblproyecto WHERE intproyectoid = "+strProyectoId).equals(sesionusuarioid)) 
                   && (strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_CREADO) 
                   || strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_LIBERADO) 
                   || strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_CONCLUIDO));

//FORMA DE OBTENER EL PRESUPUESTO A PARTIR DE LAS ACTIVIDADES
    String strPresActividades = "0";    // Guarda el presupuesto total de las actividades del proyecto actual

// Adquiere el presupuesto total de las actividades dentro del proyecto
    if (strIntEstadoProyecto.equals("0")) {
// Si es alta de proyecto
        strPresActividades = "-1";
    } else {
        
        strPresActividades = bean.obtenerPresupuestoTotalMeta(meta.getStrIntMetaId());
        strPresActividades = strPresActividades == null || strPresActividades.equals("") ? "0.00" : strPresActividades;
    }

// Determinar el header
    if (boolAlta) {
// Si se está dando de alta...
        strHeader = tags.getString("proyectos.metahandle.AltaDeMeta");
    } else {
        strHeader = tags.getString("proyectos.metahandle.DetalleDeMeta");
    }

    strBtnCancelar = "../view/v.proyectos.view?strIntProyectoId=" + meta.getStrIntProyectoId();
%>

<script type="text/javascript">
    window.onload = function(){
        hideLoader();
    }
    <%--------Inicia variables y funciones para los iconos de reportes y gr'aficos-----%>
    if (document.images) {
        var ganttDown=new Image();
        ganttDown.src="../images/buttonIcons/buttonIconGanttDown.gif";
        var ganttUp= new Image();
        ganttUp.src="../images/buttonIcons/buttonIconGantt.gif";
        var ganttOver = new Image();
        ganttOver.src ="../images/buttonIcons/buttonIconGanttOver.gif";
    }

    function cambiarImagen(nombreBoton, nuevaImagen) {
        if (document.images) {
            document[nombreBoton].src=nuevaImagen.src;  
        }
    }

    function abrirReporteGraficaGantt(){
        window.open("../view/v.reporte.gantt.view?proyectoId=<%=meta.getStrIntProyectoId()%>&&metaId=<%=meta.getStrIntMetaId()%>","ventanaReporteGantt","height=560,width=700,left=200,top=100,scrollbars=1, resizable=1");

    }
    <%----------Finaliza las variables y funciones para los iconos de reportes i graficos--------%>

    // Deshabilita campos que no se deben modificar
    function disableThings(){
        <%if (!boolAlta && !boolEditar) {%>
        <%-- Si no tiene permisos para editar... --%>
                document.form1.strVchNombre.readOnly = true;
                document.form1.strTxtDescripcion.readOnly = true;
        <% }%>
    }

<%if (boolAlta || boolEditar || boolAltaAct) {%>
    // Hace un reload para guardar el proyecto
    function parameters(accion, pagina) {
        if(accion === 'soloNext') {
            window.location.href = pagina;
            return;
        }
    <%if (boolAlta) {%>
        //alert(document.form1.strIntProyectoId.value);
        document.form1.strIntAutorId.value = '<%=sesionusuarioid%>';
        document.form1.actionType.value = 'insertaMeta';
        document.form1.action = "../view/v.proyectos.metas.view";
    <%} else if (boolEditar) {%>
        document.form1.actionType.value = 'actualizaMeta';
        document.form1.action = "../view/v.proyectos.view";
    <%}%>
        document.form1.nextPage.value = pagina;
        document.form1.vchState.value = accion;
        showLoader();
        document.form1.submit();
    }

    // Verifica si se hicieron cambios
    function cambiosHechos() {
        <% // Agarrará los datos verdaderos de la meta
        strSql = "SELECT * FROM tblactividad WHERE intactividadid = " + meta.getStrIntMetaId();
        System.out.println(strSql);
        if (meta.find(strSql)) {
            // Si buscó correctamente, pasa al registro único...
            if (meta.next()) {
                // Si pasa al siguiente registro único...%>
                if (document.form1.strVchNombre.value != "<%=meta.fieldStringS("vchnombre")%>" 
                    || document.form1.strTxtDescripcion.value != document.form1.strTxtDescripcionReal.value) {
                    return true;
                }
                return false;
        <%
            }
        }
        %>
    }

    function clearall() {
        document.form1.strVchNombre.value = "";
        document.form1.strTxtDescripcion.value = "";
        limpiaColor();
    }

    // Limpiar color de determinado campo
    function limpiar(name){
        eval("document.form1." + name + ".style.backgroundColor='#FFFFFF'");
    }

    // Limpiar colores de los campos
    function limpiaColor(){
        limpiar('strVchNombre');
        limpiar('strTxtDescripcion');
    }

    function validateSubmit(accion, pagina) {
        limpiaColor();
        faltante="<%=tags.getString("proyectos.metahandle.javascript.LosSiguientesDatosSonNecesarios")%>:";
        falta=false;
        document.form1.vchNombre.value = document.form1.strVchNombre.value; // temporal para evitar errores
        if(document.form1.strVchNombre.value=="") {
            faltante+="\n - <%=tags.getString("proyectos.metahandle.javascript.NombreDeLaMeta")%>";
            document.form1.strVchNombre.style.backgroundColor=requiredfield(); 
            if(!falta){
                document.form1.strVchNombre.focus();            
                falta=true;
            }
        }
        if(document.form1.strTxtDescripcion.value=="") {
            faltante+="\n - <%=tags.getString("proyectos.metahandle.javascript.Descripcion")%>";
            document.form1.strTxtDescripcion.style.backgroundColor=requiredfield(); 
            if(!falta){
                document.form1.strTxtDescripcion.focus();            
                falta=true;
            }
        }

        confirmactualizacion = false;

        if (falta) {
            dialog(faltante);        
        } 
    <%if (boolAlta) {%> 
        else {
            parameters(accion,pagina);
        }
    <%} else {%>
        else if (accion == 'guardar'){
            dialog('<%=tags.getString("mensaje.actualizacion")%>', '<%=tags.getString("boton.Aceptar")%>', '<%=tags.getString("boton.Cancelar")%>').then(function(){
                parameters(accion,pagina);
            });          
        } else {
            parameters(accion,pagina);
        }
    <%}%>
    }

    // Hace un reload para borrar una actividad
    function borrarActividad(actividadId) {
        dialog("<%=tags.getString("proyectos.metahandle.javascript.LaActividadSeraBorradaDelSistemaDeseaContinuar")%>", 
               "<%=tags.getString("boton.Aceptar")%>", "<%=tags.getString("boton.Cancelar")%>").then(function(){
            document.form1.vchState.value = "borrarActividad";
            document.form1.strIntActividadId.value = actividadId;
            showLoader();
            document.form1.submit();
        });
        }

    // Hace un reload para borrar un documento
    function borrarDocumento(documentoId) {
        dialog("<%=tags.getString("proyectos.metahandle.javascript.ElDocumentoSeraBorradoDeseaContinuar")%>", 
               "<%=tags.getString("boton.Aceptar")%>", "<%=tags.getString("boton.Cancelar")%>").then(function(){
            document.form1.vchState.value = "borrarDoc";
            document.form1.intDocumentoId.value = documentoId;
            showLoader();
            document.form1.submit();
        });
        }

<%}%>

    // Hace un reload para mandar a la forma de meta
    function agregarActividad() {
        accion = "Next";
        <% if (actividad.verificaProyectoDesarrollo(meta.getStrIntProyectoId())) {%>
                pagina = "../view/v.proyectos.actividades.view?strIntMetaId=<%=meta.getStrIntMetaId()%>&strIntProyectoId=<%=meta.getStrIntProyectoId()%>";
        <% } else {%>
                pagina = "../view/v.proyectos.actividades.alta.view?strIntMetaId=<%=meta.getStrIntMetaId()%>&strIntProyectoId=<%=meta.getStrIntProyectoId()%>";
        <% }%>
        goTo(accion, pagina);
    }

    // Pregunta si quiere guardar los cambios antes de continuar
    function goTo(accion,pagina) {
    <% if (boolEditar) {%>
        <%-- Si se está modificando, se muestra mensaje de actualización --%>
        confirmcambios = false;
        if (accion == "guardar") {
            validateSubmit(accion,pagina);
        } else if (cambiosHechos()) {
            confirmcambios = confirm("<%=tags.getString("proyectos.metahandle.HaModificadoLaMetaDeseaGuardarLosCambiosAntesDeContinuar")%>");
            if (confirmcambios == true) {
                // Si se hicieron cambios, pregunta si quiere guardar los cambios y si acepta, manda a validateSubmit();
                validateSubmit("guardar" + accion, pagina);
            } else {
                //Si se hicieron cambios, pregunta si quiere guardar los cambios y si no acepta, manda a parameters con el valor de accion = "soloNext";
                window.location.href = '<%=strBtnCancelar%>';
            }
        } else if (accion == "Salir") {
            // Si no se hicieron cambios o no quiere guardarlos y escogió salir, se sale;
            window.location.href = '<%=strBtnCancelar%>';
        } else if (accion == "Next") {
            // Si no se hicieron cambios o no quiere guardarlos solo se dirige a la siguiente página;
            parameters("soloNext", pagina);
        }
    <%} else if (boolAlta) {%>
        validateSubmit(accion,pagina);
    <%} else {%>
        window.location.href = pagina;
    <%}%>
    }

    //Función que decide a que jsp de actividades va a mantar según si estado
    function verActividad(actividadId,intestado) {
        accion = "Next";
    <%if (actividad.verificaProyectoDesarrollo(meta.getStrIntProyectoId())) {%>
        // Si es un proyecto de desarrollo te manda a actividades de desarrollo
        pagina = "../view/v.proyectos.actividades.view?strIntMetaId=<%=meta.getStrIntMetaId()%>&strIntProyectoId=<%=meta.getStrIntMetaId()%>&strIntActividadId=" + actividadId;
    <%} else {%> 
        if (intestado == 1) {
            // Si el estado es creado, manda a editar actividad.
            pagina = "../view/v.proyectos.actividades.edit.view?strIntMetaId=<%=meta.getStrIntMetaId()%>&strIntActividadId=" + actividadId;
        } else if (intestado == 2) {
            // Si el estado es en proceso, manda a avance de actividad.
            pagina = "../view/v.proyectos.actividades.avance.view?strIntMetaId=<%=meta.getStrIntMetaId()%>&strIntActividadId=" + actividadId;
        } else if (intestado == 3 || intestado == 4) {
            // Si el estado es concluida o verificada, manda a verificar actividad.
            pagina = "../view/v.proyectos.actividades.verifica.view?strIntMetaId=<%=meta.getStrIntMetaId()%>&strIntActividadId=" + actividadId;
        } else {
            // Si no agarra bien el estado lleva a donde manda el botón de Cancelar.
            pagina = "<%=strBtnCancelar%>"
        }
    <%}%>
        goTo(accion, pagina);
    }

    var winAuxActividades;
    // Llama a un procedimiento de la nueva ventana de documentos
    function callProc(e) {
        winAuxActividades.whenClose(window);
    }
    // Abre ventana para ver la Gráfica de Gantt
    function reporteGantt() {
        var winAuxProyectos = window.open("../view/v.reporte.gantt.view?strIntMetaId=<%=meta.getStrIntMetaId()%>","winAuxProyectos","height=520,width=760,left=200,top=100, scrollbars=1, resizable=1");
    }
    // Funciones del comportamiento de archivos
    function ver(tipoB) {
        if(document.form1.verA.value == "true") {
            document.form1.verA.value = "false";
        } else {
            document.form1.verA.value = "true";
        }
        block("archivos"); 
        block("esconderArchivos");
        block("mostrarArchivos");
    }

    // Funciones del comportamiento de componente de manejo de archivos
    // Filtro de repositorios
    function changeNodoId() {
        selecciona_items(document.form1.list_der);
        document.form1.controlListas.value=true;
        recargar();
    }
    function easyRefresh() {
        setParamsRefresh();
        document.form1.submit();
    }
    function setParamsRefresh() {
        var parametros = '?aux=0';
        var temp ='?aux=0';
        var elementos = document.form1.length;
        for(i=0;i<elementos;i++) {
            if(document.form1.elements[i].id != null) {
                temp = '&'+document.form1.elements[i].id;
                temp += '='+document.form1.elements[i].value;
                if(document.form1.elements[i].value != '' && document.form1.elements[i].id != '') {
                    parametros += temp;
                }
            }
        }
        //alert(parametros);
        document.form1.action = '../view/v.proyectos.metas.view' + parametros;
    }
    function recargar(){
        selecciona_items(document.form1.list_der);   //**********************<-- podria ser indispensable en cada reload
        document.form1.vchState.value = "reload";
        document.form1.action = "../view/v.proyectos.metas.view";
        showLoader();
        document.form1.submit();
    }

    // Checha el tipo de explorador
    ns4 = (document.layers)? true:false
    ie4 = (document.all)? true:false

    // Funcion que muestra y esconde el control de listas
    function mostrar_listas() {
        // Esconde control listas
        hide('listas');
    }
    document.getElementsByTagName('body')[0].style.width = '780px';

    function hideLoader(){
        window.document.getElementById("loader").style.display = "none";
    }
    function showLoader(){
        window.document.getElementById("loader").style.display = "";
    }
    function BtnCancelar (){
        dialog("<%=tags.getString("proyectos.metahandle.Regresar")%>", Validate.yes, Validate.no)
        .then(function(){window.location.href = '<%=strBtnCancelar%>';});
    }
</script>
<style>
#trapped #fileAttacher {
    margin-left: -16px;
}
</style>
<%@include file="../../components/loader.jsp" %>
<form  name="form1" method="post" action="" class="form-goal-detail">
    <%=header(strHeader)%>
    <%if (!boolAlta && strIntEstadoProyecto.equals(isoblock.common.Properties.PRO_LIBERADO)) {%>
    <div style="background-color: #99ccff;">
    <table width="100%" style=" display: block;">
        <tr valign='middle' bgcolor="" >
            <td><p>&nbsp;</p></td>
            <td width='70' height="22px"><p class='bar_title'><%= tags.getString("iconos.ReporteGraficos")%></p></td>
            <td width="1px" height="22px">&nbsp;</td>
            <td align="left" height="25px">
                <table align="left" cellpadding="0" cellspacing="4" border="0" width="100">
                    <tr>
                        <td align='right'>
                            <a href="javascript:abrirReporteGraficaGantt()" onmouseout="cambiarImagen('gantt',ganttUp);" 
                               onmousedown ="cambiarImagen('gantt',ganttDown);" onmouseup ="cambiarImagen('gantt',ganttUp);"
                               onmouseover="cambiarImagen('gantt',ganttOver);">
                                <img name="gantt" src="../images/buttonIcons/buttonIconGantt.gif" border="0" alt='<%=tags.getString("iconos.GraficaGantt")%>'></a>
                        </td>
                        <td align='left'><p class='bar_text'><%=tags.getString("iconos.Text.GraficaGantt")%></p></td>

                    </tr>
                </table>
            </td>

        </tr>
    </table>
    </div>
    <table cellspacing='0' cellpadding='16' bgcolor='#F5F7F9'>
        <tr class="tblHeader">
            <td>  
                <%}%>

                <table border="0" cellspacing="5" cellpadding="0">

                    <%if (boolMsgPro) {
                    // Si es necesario mostrar un mensaje referente al proyecto, se muestra
                    %>
                    <tr>
                        <td colspan='3' class="alertmessage"><%=strMsgPro%></td>
                    </tr>
                    <tr><td>&nbsp;</td></tr>
                    <%}%>  


                    <tr>   <td colspan="2">&nbsp;  </td>    </tr>
                    <tr>
                        <td align="right" class="label"><%=tags.getString("proyectos.metahandle.NombreDeLaMeta")%>:</td>
                        <td>
                            <input type="text" class="input" name="strVchNombre" size="40" maxlength="255" value="<%=meta.getStrVchNombre()%>">
                        </td>
                    </tr>

                    <tr>
                        <td align="right" class="label"><%=tags.getString("proyectos.metahandle.Descripcion")%>:</td>
                        <td>
                            <textarea rows="4" name="strTxtDescripcion" cols="74" id="txtDescription" style="resize: none;"><%=meta.getStrTxtDescripcion()%></textarea>
                        </td>
                    </tr>

                    <%if (!boolAlta) {%>
                    <%-- Cuando se está de alta no se muestra el presupuesto ni las fechas inicial y final, aún no se dan de alta actividades --%>
                    <tr>
                        <td align="right" class="label"><%=tags.getString("proyectos.metahandle.Presupuesto")%>:</td>
                        <td align="left" class='label'><%=strPresActividades%></td>
                    </tr>
                    <%}%>

                    <%if (!boolAlta) {%>
                    <%-- Si no se está dando de alta y puede editar, lo siguente no muestra --%>
                    <!-- Inicia Despliegue de Archivos -->
                </table>
                <div id ="trap"></div>
                <table id="listActivities">
                    <!-- Finaliza Despliegue de Archivos -->

                    <%}%>
                    
                    <%if (!boolAlta) {%>
                    <!-- Inicia Despliegue de Actividades (Subordinadas de las Metas) -->
                    <tr><td colspan='2'><hr></td></tr>
                    <tr>
                        <td align="left" class="label" colspan="2"><%=tags.getString("proyectos.metahandle.Actividades")%>&nbsp;
                            <%  if (boolAltaAct) {%>
                            <input type="button" class="buttondos" name="addACT" value="<%=tags.getString("proyectos.metahandle.AgregarNueva")%>" onClick="agregarActividad();"> 
                            <%  }%>
                        </td> 
                    </tr>
                    <%  if (boolMsgMeta) {%>
                    <tr> 
                        <td colspan="2" class="alertmessage"><%=strMsgMeta%></td>
                    </tr>
                    <%  }%>
                    <tr>
                        <td colspan='2'>
                            <table id="activities" border='0' >
                                <tr>
                                    <td align="center"><%=tags.getString("proyectos.metahandle.Estado")%></td> 
                                    <td align="center"><%=tags.getString("proyectos.metahandle.Actividad")%></td> 
                                    <td align="center"><%=tags.getString("proyectos.metahandle.UsuarioResponsable")%></td> 
                                    <td align="center"><%=tags.getString("proyectos.metahandle.FechaInicial")%></td> 
                                    <td align="center"><%=tags.getString("proyectos.metahandle.UsuarioVerificador")%></td>
                                    <td align="center"><%=tags.getString("proyectos.metahandle.FechaFinal")%></td>
                                    <td align="center"><%=tags.getString("proyectos.metahandle.Presupuesto")%></td>
                                    <%  if (boolAltaAct) {%>
                                    <td align="center"><%=tags.getString("proyectos.metahandle.Editar")%></td>
                                    <td align="center"><%=tags.getString("proyectos.metahandle.Borrar")%></td>
                                    <%  } else {%>
                                    <td align="center" class="label"><%=tags.getString("proyectos.metahandle.Ver")%></td>
                                </tr>
                                    <%  }%>
                                    <%
                                        String strimg = "";
                                        String stralt = "";
                                        String strFechaInicio = "";
                                        String strFechaFin = "";
                                        String strIntActividadId = "";
                                        String strFltPresupuesto = "0";     // Guarda el presupuesto de las actividades proveniente de rubros
                                        String strVerificador = "";
                                        String strResponsable = "";
                                        String[] arrayDatosImagenEstado;
                                        actividad.listaActividades(meta.getStrIntMetaId());

                                        if (actividad.next()) {
                                            do {
                                                // Mientras existan actividades para mostrar
                                                strIntActividadId = actividad.fieldStringS("intactividadid");
                                                // Obtiene el presupuesto total de la actividad
                                                strFltPresupuesto = bean.obtenerPresupuestoTotalActividades(strIntActividadId);
                                                if (strFltPresupuesto.equals("")) {
                                                    strFltPresupuesto = "0.00";
                                                }
                                                arrayDatosImagenEstado = actividad.obtenerDatosImagenEstado(actividad.fieldStringS("intestado"), tags);
                                                strimg = arrayDatosImagenEstado[0];
                                                stralt = arrayDatosImagenEstado[1];

                                                if (actividad.fieldStringS("dtefechainicio").length() > 5) {
                                                    strFechaInicio = actividad.formatDate2(actividad.fieldDateS("dtefechainicio"));
                                                }
                                                if (actividad.fieldStringS("dtefechafin").length() > 5) {
                                                    strFechaFin = actividad.formatDate2(actividad.fieldDateS("dtefechafin"));
                                                }
                                                strVerificador = actividad.fieldStringS("first_name");
                                                strResponsable = actividad.fieldStringS("resposibleUsers");
                                    %>
                                <tr>
                                    <td align="center"><img border='0' src="<%=strimg%>" title="<%=stralt%>"></td>
                                    <td class="label" align="left"><%=actividad.fieldStringS("vchnombre")%> </td>
                                    <td class="label" align="center"><%=strResponsable%></td>
                                    <td class="label" align="center"><%=strFechaInicio%></td>
                                    <td class="label" align="center"><%=strVerificador%></td>
                                    <td class="label" align="center"><%=strFechaFin%></td>
                                    <td class="label" align="center"><%=strFltPresupuesto%></td>
                                    <td align="center"><a href="javascript:verActividad('<%=strIntActividadId%>','<%=actividad.fieldStringS("intestado")%>')"><img border='0' src="../images/control/ico_edit.gif" title="<%=tags.getString("tooltip.Editar")%>"></a></td>
                                    <%if (boolAltaAct 
                                            && (
                                                //actividad.fieldStringS("created_before_release").equals("0") ||
                                                strIntEstadoProyecto.equals(propiedadesJsp.PRO_CREADO) // solo se pueden borrar en estado creado
                                        )) {%>
                                    <td align="center"><a href="javascript:borrarActividad('<%=strIntActividadId%>')"><img border='0' src="../images/control/delete.png" title="<%=tags.getString("tooltip.Borrar")%>"></a></td>
                                    <%} %>
                                </tr>

                                <%      } while (actividad.next());
                                    } else {%>
                                <%-- Si no existen metas muestra un mensaje --%>
                                <tr>
                                    <td colspan="8" class="alertmessage"><%=tags.getString("proyectos.metahandle.NoSeHanAsignadoActividades")%></td>  
                                </tr>
                                <%  }%>
                            </table>
                        </td>
                    </tr>
                    <!-- Finaliza Despliegue de Metas -->
                    <tr> <td colspan="2">&nbsp;</td>  </tr>
                    <%}%>

                    <tr> <td colspan="2">&nbsp;</td>  </tr>
                    <tr> 
                        <td colspan="2" align="center">
                            <%if (boolAlta) {%>
                                <%-- Si se está dando de alta... --%>
                                <input type="button" class="button" name="instAdd" value="<%=tags.getString("boton.Guardar")%> " onClick="validateSubmit('guardar','');">
                                <input type="button" class="button" name="instback" value="<%=tags.getString("boton.Regresar")%>" onClick="BtnCancelar();">
                                <input type="reset" class="button" name="instClear" value="<%=tags.getString("boton.LimpiarForma")%>" onClick="clearall();">
                                <%} else if (boolEditar || meta.canAddFilesInGoal(sesionrolproyectos.equals(isoblock.common.Properties.PROYECTOS_MANAGER), sesionusuarioid)) {%>
                                <%-- Si se está editando... --%>
                                <input type="button" class="button" name="instAdd" value="<%=tags.getString("boton.Guardar")%> " onClick="validateSubmit('guardar','');">
                                <input type="button" class="button" name="back" value="<%=tags.getString("boton.Regresar")%>" onClick="window.location.href = '<%=strBtnCancelar%>';">
                            <%} else {%>
                                <input type="button" class="button" name="back" value="<%=tags.getString("boton.Regresar")%>" onClick="window.location.href = '<%=strBtnCancelar%>';">
                            <%}%>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <%-- Variables de documento --%>
    <input type=hidden name="intDocumentoId" value="">
    <%-- Variables de Actividad --%>
    <input type=hidden name="strIntActividadId" value="<%=actividad.getStrIntActividadId()%>">
    <input type="hidden" name="ascDesc" value="asc">
    <%-- Variables de Meta --%>
    <input type="hidden" name="strIntProyectoId" value="<%=meta.getStrIntProyectoId()%>">
    <input type="hidden" name="estadoProyecto" id="estadoProyecto" value="<%= strIntEstadoProyecto %>" >
    <input type="hidden" name="strIntEstado" id ="strIntEstado"value="<%=meta.getStrIntEstado()%>">
    <input type="hidden" name="strIntMetaId" id="strIntMetaId" value="<%=meta.getStrIntMetaId()%>">
    <input type="hidden" name="strIntAutorCambiosId" value="<%=sesionusuarioid%>">
    <input type="hidden" name='strIntAutorId' value="<%=meta.getStrIntAutorId()%>">
    <input type="hidden" name='vchNombre' value="<%=meta.getStrVchNombre()%>">
    <input type="hidden" name='strIntNodoId' value="<%=meta.getStrIntNodoId()%>">
    <input type="hidden" name='strIntNodoId' value="<%=meta.getStrIntNodoId()%>">
    <input type="hidden" name="idDocs" id="idDocs" value="<%=meta.getIdDocs()%>">
    <input type="hidden" name="boolAlta" id="boolAlta" value="<%=boolAlta%>">
    <%-- Variables para validar cambios --%>
    <input type="hidden" name="strTxtDescripcionReal" value="">
    <%  if (!vchState.equals("reload")) {%>
    <script>
        document.form1.strTxtDescripcionReal.value = document.form1.strTxtDescripcion.value;
    </script>
    <%  }%>
    <%-- Variables para actionall.jsp --%>
    <input type="hidden" name="vchState" value="">
    <input type="hidden" name="nextPage" value="">
    <input type="hidden" name='actionType' value=''>
    <input type="hidden" name='cambiosHechos' value='<%=meta.parameter("cambiosHechos", request)%>'>
    <%-- Variables para listas --%>
    <input type=hidden name='controlListas' value="<%//=meta.parameter("controlListas",request)%>">
    <%-- Variables para despliegue de archivos --%>
    <input type="hidden" name="verA" value="<%=meta.parameter("verA", request)%>">
    <script type="text/JavaScript"> disableThings(); </script>
</form>
<div style="display: none">
    <div id ="trapped"><div id ="fileAttacher"></div></div>
</div>
<%=footer()%>
<%@ include file="../../includes/mensajeespera.txt"%>
<%try {
    meta.finalize();
    actividad.finalize();
} catch (Throwable t) {
    t.printStackTrace();
}%>
<%--  Finaliza el cuerpo del JSP  --%>
<%@ include file="../../templates/inferior.tem"%>