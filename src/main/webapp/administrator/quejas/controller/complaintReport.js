require(['core', 'dojo/on', 'dojo/dom', 'dojo/query', 'dojo/domReady!'], 
function(core, on, dom, query) {      
    function doEverything (i18n) {
        function printPage() {
            $('a').addClass('hideToPrint');
            var userId = dom.byId('intUsuarioId').value;
            if(userId === '0') {
                $('#userIdLblContainer').addClass('hideToPrint');
                $('#userIdContainer').addClass('hideToPrint');
            } else {
                $('#userIdLblContainer').removeClass('hideToPrint');
                $('#userIdContainer').removeClass('hideToPrint');
            }
            var show = false;
            query('#filterDateContainer li').forEach( function (elem, idx) {
                var dojoArr = query('input[type="hidden"][id*="dteFecha"]', elem);
                if(!dojoArr.some(function(field){
                    return !!field.value;
                }) || dojoArr.length === 0) {
                    $(elem).addClass('hideToPrint');
                } else {
                    $(elem).removeClass('hideToPrint');
                    show = true;
                }
            });
            if (show) {
                $('#filterDatelbl').removeClass('hideToPrint');
            } else {
                $('#filterDatelbl').addClass('hideToPrint');
            }
            core.printFrm();
        }
        on(dom.byId('printButton'), 'click', printPage);
        core.hideLoader();
    };      
    core.setLang('lang/quejas/nls/complaintReport').then(doEverything);
});