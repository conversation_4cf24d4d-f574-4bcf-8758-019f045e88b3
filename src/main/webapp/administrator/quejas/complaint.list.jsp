<%@page import="isoblock.common.Properties"%>
<%@page import="Framework.Config.Utilities"%>
<%@page import="java.util.ArrayList"%>
<%@page import="java.util.List"%>
<%@page import="mx.bnext.access.ProfileServices"%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<%
    List<ProfileServices> services = (List<ProfileServices>) (session.getAttribute("services") == null ? new ArrayList<ProfileServices>() : session.getAttribute("services"));
    boolean isAdmin = ((String) (session.getAttribute("admin_general") == null ? "" : session.getAttribute("admin_general"))).equals(isoblock.common.Properties.ADMINISTRADOR_MAESTRO);
    Integer canChangeStatus = (isAdmin || services.contains(mx.bnext.access.ProfileServices.QUEJA_ENCARGADO)) ? 1 : 0;
%>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Mis reuniones</title>

        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script type='text/javascript' src="../controller/c.complaint.list.controller?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../components/loader.jsp" %>        
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons" action="../view/v.complaint.view">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left" id="window_title"></h3>
                                    <div class="float-right">
                                        <!-- boton de opciones -->
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint" id="selectorSeparator"></div>
                        <div class ="grid-container table_workarea">
                            <ul class="grid-x grid-padding-x content_area">
                                <li id="statusMessage" class="status-message displayNone"></li>
                                <li><div id="search"></div></li>
                                <li><div id="tabNode" ></div></li>
                                <li><table id="dataGrid"></table></li>
                                <li><div id="pagination"></div></li>
                            </ul>
                        </div>
                        <div class ="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                        </div>
                        <input type="hidden" name="intQuejaId" value="intQuejaId" />
                        <input type="hidden" name="vchState" value="edit" />
                        <input type="hidden" name="systemId" id="systemId" value="${systemId}">
                        <input type="hidden" name="quejaId" id="quejaId" value=""/>
                        <input type="hidden" name="canChangeStatus" id="canChangeStatus" value="<%= canChangeStatus %>"/>
                    </form>
                    <div dojoType="dijit.Dialog" id="statusWindow">
                        <form method="POST" name="statusChange" id="statusChange">
                            <ul class="content_area">
                                <li>
                                    <label id="lblStatus">Estatus</label>
                                    <select name="selectedReportType" id="selectedReportType">
                                        <option id="defaultSelect" value=""></option>
                                        <option id="reportedToAssign" value="reportedToAssign"></option>
                                    </select>
                                </li>
                                <li class="separator">
                                    <input class="Button right" type="button" id="cancelDialog" value=""/>
                                    <input class="Button right" type="button" id="saveStatus" value=""/>
                                </li>
                            </ul>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <%@include file="../../../../components/footer.jsp" %>
    </body>
</html>