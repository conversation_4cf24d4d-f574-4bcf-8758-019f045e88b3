/* global parameters, $locale */
require([
    'core',
    'bnext/angularActivity',
    'bnext/angularNavigator',
    'bnext/filesAttacher/core',
    'dojo/dom-construct',
    'dojo/dom',
    'dojo/_base/array',
    'dojo/query',
    'bnext/saveHandle',
    'bnext/administrator/finding/finding-activity-grid',
    'bnext/gridComponentUtil',
    'dojo/on',
    'dojo/dom-attr',
    'dojo/Deferred',
    'dojo/promise/all',
    'dojo/_base/json',
    'dojo/dom-class',
    'bnext/callMethod',
    'dojo/ready',
    'bnext/gridCubes',
    'bnext/i18n!bnext/administrator/finding/nls/finding',
    'bnext/administrator/solicitudes/fillRequestHandle',
    'bnext/administrator/forms/_base/fillOutAction',
    'bnext/ckeditor',
    'bnext/angularNavigator',
    'bnext/IFrameUtil',
    'bnext/LinkedGridFactory',
    'bnext/_base/media-queries',
    'dojo/dom-style',
    'bnext/FloatingOptionsButton',
    'bnext/FloatingOptionButton',
  'i18n-util',
    'dojo/domReady!'
    ],
function (
    core,
    angularActivity,
    angularNavigator,
    fileAttacher,
    domConstruct,
    dom,
    array,
    query,
    sH,
    FindingActivityGrid,
    gcUtil,
    on,
    domAttr,
    Deferred,
    all,
    JSON,
    domClass,
    callMethod,
    ready,
    gridCubes,
    lang,
    fillRequestHandle,
    fillOutAction,
    ckeditor,
    angularNavigator,
    IFrameUtil,
    LinkedGridFactory,
    MediaQueries,
    domStyle,
    FloatingOptionsButton,
    FloatingOptionButton,
    I18nUtil
) {
    var defCKEditors = [], w = window, fA, fE, languageQMS = $locale, linkedGridDocument,dynamicFieldColums = [], id = dom.byId('id').value;
    var _closed = false;
    function doEverything(i18n) {
        //--------------------------------------------------------//
        // Aquí se declaran todas las funciones de la pantalla.   //
        //--------------------------------------------------------//
        // La lógica inicia en el comentario: ¡Inicia lógica!     //
        //--------------------------------------------------------//
        /**
         * Esta funcion decide a donde navegará la pantalla despues de guardar
         * 
         * @param {type} flag
         * @returns {undefined}
         */
        function frameDialogReady(flag) {
            switch(flag){
                case 'isLoaded':
                    IFrameUtil.isLoaded(dom.byId('idFrameDialogo'), 'isLoaded').then(function () {
                        var context = IFrameUtil.getContext(dom.byId('idFrameDialogo'));
                        var msg;
                        if (context.doc.getElementById('errorDetail') !== null) {
                            if (context.doc.getElementById('errorDetail').outerText.includes('DUPLICATE_UNIQUE_KEY')) {
                                core.dialog(i18n.duplicateKey);
                            } else {
                                core.dialog('Unknown exception, see console error');
                                console.error(context.doc.getElementById('errorDetail').outerText);
                            }
                            return;
                        }
                        if (dom.byId('intAprobacion') && dom.byId('intAprobacion').value === "2") {
                            msg = context.doc.getElementById('successMsgReject').innerText;
                        } else {
                           msg = context.doc.getElementById('successMsg').innerText;
                        }
                        core.dialog(msg, i18n.findingAdd, i18n.findingMine).then(function () {
                            angularNavigator.navigateLegacy('v.action.by.type.view');
                        }, function () {
                            angularNavigator.navigateLegacy('v-action-my-actions.view');
                        });
                    });
                    break;
                case 'acceptAnalysis':
                    core.dialog(i18n.acceptAnalysisMessage, i18n.continue, i18n.pendings).then(function () {
                            reloadPage();
                        }, function () {
                            core.navigate('pendings');
                        });
                    break;
            }

        }
        function reloadPage() {
            window.location.reload();
        }
        function showAnalisisHandle() {
            if (!isRequireAnalysisOn) {
                handleFillAnalisis();
            }
            if (!requireAnalysisCombo) {
                return;
            }
            if (requireAnalysisCombo.value === '1') {
                handleFillAnalisis();
            } else if (requireAnalysisCombo.value === '0') {
                if (formToFill) {
                    domClass.add(dom.byId('documentFormInfo'), 'displayNone');
                }
                if (intResponsableId === loggedUserId) {
                    dom.byId('idRowCauseAnalysis') && domClass.add(dom.byId('idRowCauseAnalysis'), 'displayNone');
                }
            }
        }
        function handleFillAnalisis() {
            if (formToFill) {
                domClass.remove(dom.byId('documentFormInfo'), 'displayNone');
            } else if (intResponsableId === loggedUserId || status > 1) {
                dom.byId('idRowCauseAnalysis') && domClass.remove(dom.byId('idRowCauseAnalysis'), 'displayNone');
            }
        }
        function formToFillEval() {
            if (formToFill) {
                if ((requireAnalysisCombo && requireAnalysisCombo.value === '1') || !isRequireAnalysisOn) {
                    domClass.remove(dom.byId('documentFormInfo'), 'displayNone');
                }
                if (status > 2) {
                    if (+documentFormOutstandingSurveyId > 0) {
                        $('#documentFormInfoMessage').text(lang.documentFormInfoMessageFilled);
                        dom.byId('documentFormCube').src = require.toUrl("bnext/images/" + gridCubes.gray);
                        dom.byId('documentFormCube').title = lang.documentFormInfoMessageFilled;
                        on(dom.byId('documentFormActionBtn'), 'click', function () {
                            formFilledView(docTitle, documentFormOutstandingSurveyId);
                        });
                        dom.byId('documentFormActionBtn').title = lang.preview;
                        dom.byId('editForm').src = require.toUrl("bnext/images/preview.png");
                        dom.byId('fillFormColumn').innerHTML = lang.preview;
                    } else {
                        domClass.add(dom.byId('documentFormInfo'), 'displayNone');
                    }
                } else {
                    if (documentFormFilled) {
                        $('#documentFormInfoMessage').text(lang.documentFormInfoMessageFilled);
                        dom.byId('documentFormCube').src = require.toUrl("bnext/images/" + gridCubes.gray);
                        dom.byId('documentFormCube').title = lang.documentFormInfoMessageFilled;
                    } else {
                        $('#documentFormInfoMessage').text(lang.documentFormInfoMessage);
                        dom.byId('documentFormCube').src = require.toUrl("bnext/images/" + gridCubes.orange);
                        dom.byId('documentFormCube').title = lang.documentFormInfoMessage;
                    }
                    if (documentFormFillerId === loggedUserId || isAdmin || isManager) {
                        documentFormToFill = !documentFormFilled;
                        on(dom.byId('documentFormActionBtn'), 'click', documentFormLblAction);
                    } else if (+documentFormOutstandingSurveyId > 0) {
                        on(dom.byId('documentFormActionBtn'), 'click', function () {
                            formFilledView(docTitle, documentFormOutstandingSurveyId);
                        });
                    } else {
                        on(dom.byId('documentFormActionBtn'), 'click', function () {
                            core.dialog(i18n.notTheDocumentFormFillerId).then(function () {}, function () {});
                        });
                    }
                    function documentFormLblAction(evt) {
                        if (documentFormFilled) {
                            formFilledView(docTitle, documentFormOutstandingSurveyId);
                        } else {
                            if (!!+documentFormOutstandingSurveyId) {
                                cMsg = core.specifiedMessage(lang.documentFormContinueChangeScreeenConfirm)
                                        .set('docTitle', docTitle)
                                        .set('docVersion', dom.byId('documentFormVersion').value)
                                        .get()
                                        ;
                                if (fillForm.isInvalid) {
                                    formFilledView(docTitle, documentFormOutstandingSurveyId);
                                } else {
                                    fillOutAction(fillForm, '&findingId=' + findingId, false);
                                }
                            } else {
                                cMsg = core.specifiedMessage(lang.documentFormNewChangeScreeenConfirm)
                                        .set('docTitle', docTitle)
                                        .get()
                                        ;
                                fillRequestHandle(
                                        documentFormSurveyId,
                                        documentFormId,
                                        null,
                                        null,
                                        '&findingId=' + findingId
                                    );
                            }
                        }
                    }
                }
            } else {
                showAnalisisHandle();
            }
            if (dom.byId('documentFormToFill')) {
                dom.byId('documentFormToFill').value = documentFormToFill;
            }
        }
        function renderCKEditors() {
            $('.documentFormLbl').text(lang.documentFormLbl);
            on(dom.byId('intType'), 'change', function (evt) {
                callMethod({
                    url: 'FindingType.ActionSources.action',
                    method: 'getActionSources',
                    params: [evt.target.value || -1]
                }).then(function (resultado) {
                    fillCombo('#intFuente', resultado, 'text', 'value', false);
                });
            });
            all(defCKEditors).then(function () {
                if(w.disableThings) {
                    w.disableThings();
                } else {
                    console.log('w.disableThings is undefined');
                }
                var wysiwygs;
                if (status === 6) {
                    wysiwygs = query('textarea.wysiwyg');
                } else {
                    wysiwygs = query('textarea[disabled].wysiwyg');
                }
                wysiwygs.forEach(function (textarea) {
                    if (domAttr.get(textarea, 'disabled') !== "false") {
                        ckeditor.instances[textarea.id].setReadOnly(true);
                    }
                });
                if (w.procesarFuente) {
                    w.procesarFuente(dom.byId('intFuente'));
                } else {
                    console.log('w.procesarFuente is undefined');
                }
                fillForm = dom.byId('fillForm') ? JSON.fromJson(dom.byId('fillForm').value) : {};
                formToFillEval();
                if (CKEDITOR.instances) {
                    var instances = Object.values(CKEDITOR.instances);
                    for (var i = 0; i < instances.length; i++) {
                        (function (index) {
                            on(instances[index].document['$']['body'], 'keypress', function (e) {
                                if (e.target.outerText.length >= 7500) {
                                    e.preventDefault();
                                }
                            });
                            instances[index].on('paste', function (e) {
                                if (instances[index].readOnly) {
                                    //Parche para el issue https://dev.ckeditor.com/ticket/14768
                                    e.cancel();
                                    return;
                                }
                                if (e.data.dataValue.length + instances[index].document['$']['body'].outerText.length >= 7500) {
                                    e.cancel();
                                }
                            });
                        })(i);
                    }
                }
                core.hideLoader();
            });
            //Se valida si el formulario se agrego despues de haber llenado el analisis de las causas.
            var documentForInfo = dom.byId('documentFormInfo');
            if (!documentFormFilled && (status > actionSatus.ASSIGNED && status <= actionSatus.CLOSED) && documentForInfo){
                documentForInfo.style.display = 'none';
            }
        }
        function formFilledView(docTitle, documentFormOutstandingSurveyId) {
            var cMsg = core.specifiedMessage(lang.documentFormFilledChangeScreeenConfirm)
                    .set('docTitle', docTitle)
                    .set('docVersion', dom.byId('documentFormVersion').value)
                    .get()
                    ;
            core.dialog(cMsg, core.i18n.Validate.yes, core.i18n.Validate.no).then(
                    function () {
                        preview(documentFormOutstandingSurveyId);
                    }
            );
        }
        function preview(outstandingSurveysId, requestId) {
            var strWindowFeatures = "\n\
                status=0 \n\
                ,toolbar=0 \n\
                ,menubar=0 \n\
                ,resizable=1 \n\
                ,scrollbars=1 \n\
            ";
            var newFrm = window.open("", "newFrm", strWindowFeatures);
            document.capture.id.value = 'O' + outstandingSurveysId;
            document.capture.requestId.value = requestId || '';
            document.capture.target = 'newFrm';
            document.capture.action = '../DPMS/v.request.survey.preview.action';
            document.capture.submit();
            newFrm.focus();
        }
        function saveValidate() {
            var requireAnalysisDom = dom.byId('requireAnalysisCombo');
            if (requireAnalysisDom) {
                if (requireAnalysisDom.value === "0" && status === 2 && activityGrids.aciGrid !== null && activityGrids.attGrid === null) {
                    core.dialog(i18n.waitForACI, i18n.myFindingsButton, i18n.findingAdd).then(
                        function () {
                            angularNavigator.navigateLegacy('v-action-my-actions.view');
                        }, function () {
                            angularNavigator.navigateLegacy('v.action.by.type.view');
                        }
                    );
                }
            }
            if($("#form1").valid()){
                var flag = 'isLoaded';
                syncDocs();
                if(intAcceso === "1"){
                    dom.byId('vchDetalleFuente').disabled= false;
                    dom.byId('intFuente').disabled= false;
                } else if(intAcceso === "3"){
                    if(dom.byId('documentFormToFill') 
                            && dom.byId('documentFormToFill').value === 'true' 
                            && dom.byId('documentFormFilled').value !== 'true') {
                         core.dialog(i18n.formMessage);
                                return;
                    }
                    if (!core.isNull(activityGrids.attGrid) && activityGrids.attGrid.getBean().data.length === 0){
                        core.dialog(i18n.aatMessage);
                        return;
                    }
                    core.dialog(i18n.updateMessage, core.i18n.Validate.accept, core.i18n.cancel_label).then(
                        function () {
                            parameters();
                        }, function (){}
                    );
                    return;
                } else if(((intAcceso === "4" && dom.byId('canIAccept').value) || intAcceso === "5") && dom.byId('intAprobacion').value !== "2") {
                    if (!core.isNull(fE.get('value')) && fE.grid.getBean().data.length === 0 ) {
                        core.dialog(i18n.fileEvidenceMessage);
                        return;
                    }
                }else if(intAcceso === "6"){
                    flag = 'acceptAnalysis';
                    var has_aci = dom.byId('has_aci').value === 'true';
                    if(!has_aci && (window['activityGrids'] 
                        && (
                            !window['activityGrids'].aciGrid
                            || !window['activityGrids'].aciGrid.getData
                            || !window['activityGrids'].aciGrid.getData().length
                        )
                    )){
                        core.dialog(i18n.aciMessage);
                        return;
                    }
                    if (!core.isNull(activityGrids.attGrid) && activityGrids.attGrid.getBean().data.length === 0){
                        core.dialog(i18n.aatMessage);
                        return;
                    }
                    if(dom.byId('documentFormToFill') !== null){
                        if(dom.byId('documentFormToFill').value === 'true' && dom.byId('documentFormFilled').value !== 'true') {
                            core.dialog(i18n.formMessage);
                            return;
                        }
                    }  
                }
                if (dom.byId('chkGenerate') !== null && dom.byId('chkGenerate').checked) {
                    dom.byId('strClave').value = "";
                }
                parameters();
                frameDialogReady(flag);
                }
        } 
        function syncDocs() {
            findingsDocumentIds = "";
            if (linkedGridDocument) {
                linkedGridDocument.getGroundDataId().forEach(function (item) {
                    findingsDocumentIds += item.id + ",";
                });
            }
            dom.byId('findingsDocumentIds').value = findingsDocumentIds;
            dom.byId('idFiles').value = fA.get('value');
            dom.byId('idEvidence').value = fE.get('value');
        }
        function saveFiles(saveCallback) {
            var def = new Deferred();
            var misDatos = new sH({
                savedObjName: "misDatos",
                serviceStore: "../DPMS/Action.action",
                methodName: "saveFiles",
                justAccept: "true"
            });
            var data = {};
            data.actionId = dom.byId('intAccionGenericaId').value;
            data.idFiles = fA.get('value');
            data.idEvidence = fE.get('value');
            if (linkedGridDocument) {
                data.findingsDocumentIds = linkedGridDocument.getGroundDataId().map(function (m) {
                    return m.id;
                }).join(',');
            }
            core.showLoader(core.i18n.Validate.saving);
            misDatos.setData(data);
            if (typeof saveCallback === 'function') {
                misDatos.saveCallback = saveCallback;
            }
            misDatos.saveData().then(function() {
                def.resolve();
            }).catch(function (e) {
                console.error(e);
                def.reject(e);
            });
            return def;
        }
        function update() {
            if ($("#form1").valid()) {
                core.showLoader(core.i18n.Validate.saving);
                var data = {};
                var misDatos = new sH({
                    savedObjName: "misDatos",
                    serviceStore: "../DPMS/Action.action",
                    methodName: "updateAction",
                    justAccept: "true",
                    saveCallback: function() {
                        core.hideLoader();
                        var ownerDom = +dom.byId('owner').value;
                        if (ownerDom !== null && ownerDom !== 0 && +data.owner !== ownerDom && +data.owner !== 0) {
                            core.dialog(i18n.changeResponsible, i18n.pendings, i18n.findingMine).then(function () {
                                angularNavigator.navigate("pendings");
                            }, function() {
                                angularNavigator.navigateLegacy("v-action-my-actions.view");
                            });
                        } else {
                            core.dialog(core.i18n.Validate.edit_success);
                        }
                    }
                });
                if (!requireAnalysisCombo) {
                    data.requireAnalysis = requireAnalysisCombo ? requireAnalysisCombo.value : 1;
                } else {
                    data.requireAnalysis = -1;
                }
                data.id = dom.byId('intAccionGenericaId').value;
                data.department = dom.byId('intUbicacionId').value;
                data.type = dom.byId('intType').value;
                data.priority = dom.byId('intPriority').value;
                if (isManager) {
                    data.fuente = dom.byId('intFuente').value;
                    data.detalleFuente = dom.byId('vchDetalleFuente') ? dom.byId('vchDetalleFuente').value : null;
                }
                data.system = dom.byId('intSystem').value;
                data.title = dom.byId('vchTitulo').value;
                data.finding = CKEDITOR.instances.situacion.getData();
                data.consequence = CKEDITOR.instances.consecuencia.getData();
                data.planeFinding = data.finding.replace(/<.*?>/g, "").replaceAll("&nbsp;", " ");
                data.planeConsequence = data.consequence.replace(/<.*?>/g, "").replaceAll("&nbsp;", " ");
                data.owner = dom.byId('intResponsableId') ? dom.byId('intResponsableId').value : '0';
                findingsDocumentIds = "";
                if (linkedGridDocument) {
                    linkedGridDocument.getGroundDataId().forEach(function (item) {
                        findingsDocumentIds += item.id + ",";
                    });
                }
                data.findingsDocumentIds = findingsDocumentIds;
                if (typeof CKEDITOR.instances.analisisCausas !== 'undefined') {
                    data.cause = CKEDITOR.instances.analisisCausas.getData();
                }

                misDatos.setData(data);
                misDatos.saveData();
            }
        }
        function updateRequireAnalysis() {
            core.showLoader();
            callMethod({
                url: 'Action.action',
                method: 'updateRequireAnalysis',
                params: [+dom.byId('intAccionGenericaId').value, +dom.byId('requireAnalysisCombo').value]
            }).then(function () {
                core.dialog(i18n.updateRequireAnalysisOk).then(
                        function () {
                            window.location.href = 'v.pendings.view';
                        }
                );
                core.hideLoader();
            });
        }
        function clearall() {
            if (intAcceso === "4") {
                    CKEDITOR.instances.resultadosEfectividad && CKEDITOR.instances.resultadosEfectividad.setData("");
            } else if (intAcceso === "3" || intAcceso === "6") {
                    CKEDITOR.instances.analisisCausas && CKEDITOR.instances.analisisCausas.setData("");
            } else if (intAcceso === "2") {
                    dom.byId('intResponsableId').value = "";
            } else {
                    if(dom.byId('intFuente').value === "2"){
                        dom.byId('vchDetalleFuente').value = "";
                    }
                    dom.byId('intFuente').value = "";
                    CKEDITOR.instances.situacion.setData();
                    CKEDITOR.instances.consecuencia.setData();
                    document.form1.intUbicacionId.value = "";
            }
        }
        function cancel() {
            _closed = true;
            if (intAcceso === "5") {
                core.goBackHistory();
            } else {
                core.dialog(core.i18n.Validate.cancel_message, core.i18n.Validate.accept, core.i18n.Validate.cancel_label).then(
                    function(){
                        core.goBackHistory();
                    }
                );
            }
        }
        function setValidation() {
            $.validator.addMethod(
                    "cke_required",
                    function (value, element) {
                        var idname = $(element).attr('id');
                        var editor = CKEDITOR.instances[idname];
                        $(element).val(editor.getData());
                        var elementValue = $(element).val();
                        var textValue = editor.editable().getText()
                            .replace(/\n|\r|\u200B/g, "")
                            .trim();
                         return elementValue.length > 0 && textValue.length > 0;
                    },
                    core.i18n.Validate.missingField
            );
            $.validator.addMethod(
                    "cke_maxlength",
                    function (value, element, params) {
                        var idname = $(element).attr('id');
                        var editor = CKEDITOR.instances[idname];
                        $(element).val(editor.getData());
                        return $(element).val().length < +params;
                    },
                    core.i18n.Validate.missingField
            );
            $('#form1').validate({
                ignore: "",
                rules: {
                    intUbicacionId: {
                        required: true
                    },
                    intType: {
                        required: true
                    },
                    intFuente: {
                        required: true
                    },
                    intPriority: {
                        required: true
                    },
                    intSystem: {
                        required: true
                    },
                    vchTitulo: {
                        required: true,
                        maxlength: 255
                    },
                    intResponsableId: {
                        required: true
                    }
                },
                messages: {
                    vchClave: {
                        required: core.i18n.Validate['missingField']
                    },
                    intUbicacionId: core.i18n.Validate.missingCombo,
                    intType: core.i18n.Validate.missingCombo,
                    intFuente: core.i18n.Validate.missingCombo,
                    intPriority: core.i18n.Validate.missingCombo,
                    intSystem: core.i18n.Validate.missingCombo,
                    vchTitulo: specifiedMessage(core.i18n.Validate.invalid_required_text, 'specify', 255),
                    intResponsableId: core.i18n.Validate.missingCombo,
                    situacion: specifiedMessage(i18n.textAreaMessage, 'specify', i18n.findingLabel),
                    consecuencia: specifiedMessage(i18n.textAreaMessage, 'specify', i18n.consequenceLabel),
                    contingencia: specifiedMessage(i18n.textAreaMessage, 'specify', 'contingencia'),
                    analisisCausas: specifiedMessage(i18n.textAreaMessage, 'specify', 'análisis de causas'),
                    resultadosEfectividad: specifiedMessage(i18n.textAreaMessage, 'specify', 'resultado de efectividad')
                },
                errorPlacement: function (error, element) {
                    var elemt = element ? element[0] : {};
                    if (elemt.tagName === "TEXTAREA" && domClass.contains(elemt, 'wysiwyg')) {
                        error.insertBefore("#cke_" + elemt.id);
                        return;
                    }
                    error.insertBefore(element);
                }
            });
            var situacion = dom.byId('situacion');
            var consecuencia = dom.byId('consecuencia');
            var contingencia = dom.byId('contingencia');
            var analisisCausas = dom.byId('analisisCausas');
            var resultadosEfectividad = dom.byId('resultadosEfectividad');
            if (dom.byId("trDetalleFuente").style.display !== 'none') {
                $('#vchDetalleFuente').rules("add", {'required': core.i18n.Validate.missingCombo});
            }
            if (situacion) {
                $(situacion).rules("add", {cke_required: "", cke_maxlength:"7999"});
            }
            if (consecuencia) {
                $(consecuencia).rules("add", {cke_required: "", cke_maxlength: "7999"});
            }
            if (contingencia) {
                $(contingencia).rules("add", {cke_required: "", cke_maxlength: "7999"});
            }
            if (analisisCausas) {
                $(analisisCausas).rules("add", {cke_required: "", cke_maxlength: "7999"});
            }
            if (resultadosEfectividad) {
                $(resultadosEfectividad).rules("add", {cke_required: "", cke_maxlength: "7999"});
            }
            if (requireAnalysisCombo && dom.byId("requireAnalysisCombo").style.display !== 'none') {
                $(requireAnalysisCombo).rules("add", {required: true});
            }
        }
        function changeProceeds() {
            if (intProcede.value === '0') {
                core.dialog(i18n.notProcedChange, core.i18n.Validate.ok);
            }
            var btnUpdate = dom.byId('instAdd2');
            var floatingButton = optionButtons.filter(node => node.icon === 'sync')[0]
            if (btnUpdate) {
                if (intProcede.value === '1') {
                    domStyle.set(btnUpdate, 'display', 'block');
                    domStyle.set(floatingButton?.domNode, 'display', '');
                } else {
                    domStyle.set(btnUpdate, 'display', 'none');
                    domStyle.set(floatingButton?.domNode, 'display', 'none');
                }
            }
            if (!razonNoProcede) {
                return;
            }
            if (intProcede.value === '1') {
                $(razonNoProcede).rules('remove');
            } else {
                $(razonNoProcede).rules("add", {cke_required: "", cke_maxlength: "7999"});
            }

        }
        function changedDepartment() {
            core.hideLoader();
            core.dialog(i18n.changedDepartment, core.i18n.Validate.ok, i18n.myFindingsButton).then(function () {
                core.showLoader();
                var form = document.form1;
                form.intAccionGenericaId.value = findingId;
                form.action = "../view/v.action.by.type.view?accion=" + dom.byId('strClave').value + '&id=' + findingId;
                form.submit();
            }, function () {
                core.showLoader();
                window.location.href = '../view/v-action-my-actions.view?accion=';
            });
        }
        function changeDepartment() {
            core.showLoader();
            var misDatos = new sH({
                savedObjName: "misDatos",
                serviceStore: "../DPMS/Action.action",
                methodName: "changeDepartment",
                saveCallback: changedDepartment,
                hookUpdateFailure: core.hideLoader
            });
            var data = {};
            data.id = dom.byId('intAccionGenericaId').value;
            data.department = dom.byId('intUbicacionId').value;
            misDatos.setData(data);
            misDatos.saveData();
        }
        function reloadFinding() {
            core.showLoader().then(function () {
                reloadPage();
            });
        }
        function newActivity(type) {
            var businessUnitDepartmentId = dom.byId('intUbicacionId') ? +dom.byId('intUbicacionId').value : null;
            var disabledAsReadonly = {
                implementation: true,
                verification: true
            };
            angularActivity.dialogNewActivity({
                module: 'ACTION',
                urlAction: 'Finding-Activity.action',
                parentCode: parentCode,
                businessUnitId: businessUnitId,
                businessUnitDepartmentId: businessUnitDepartmentId,
                findingId: findingId,
                type: type
            }, disabledAsReadonly).then(
                function () {
                    var message = '';
                    if (type === 2) {
                        message = i18n.acceptAatMessage;
                    } else {
                        message = i18n.acceptACIMessage;
                    }
                    saveFiles(function () {
                        core.showLoader().then(function () {
                            core.dialog(message, i18n.continue, i18n.pendings).then(function () {
                                reloadFinding();
                            }, function () {
                                core.navigate('pendings');
                            });
                        });
                    });

                },
                function() {
                    // empty
                }
            );
        }
        function addACI() {
            newActivity(1);
        }
        function addAAT() {
            newActivity(2);
        }
        function setupFileAttacher() {
            var likeFabButton = isMobileDevice();

            if (dom.byId('fileAttacher') && dom.byId('fileAttacherTable')) {
                fA = new fileAttacher({
                    tableContainerId: 'fileAttacherTable',
                    serviceStore: '../DPMS/Action.action',
                    currentEntity: findingId,
                    methodName: 'getDocuments',
                    showLoader: core.showLoader,
                    hideLoader: core.hideLoader,
                    hasDelete: hasDelete,
                    hasUpload: hasUpload,
                    uploadPath: '../DPMS/Upload.fileUploader',
                    files: dom.byId('idFiles').value,
                    gridSize: -1,
                    fullScreenButton: false,
                    lblFile: i18n.labelFile,
                    likeFabButton: likeFabButton
                }, 'fileAttacher');
            } else {
                fA = {
                    get: function () {
                        return null;
                    }
                };
            }
        }
        function setupFileEvidence() {
            if (dom.byId('fileEvidence') && dom.byId('fileEvidenceAttacherTable')) {
                fE = new fileAttacher({
                    tableContainerId: 'fileEvidenceAttacherTable',
                    serviceStore: '../DPMS/Action.action',
                    currentEntity: findingId,
                    methodName: 'getEvidence',
                    showLoader: core.showLoader,
                    hideLoader: core.hideLoader,
                    hasDelete: hasDelete,
                    hasUpload: hasUpload,
                    uploadPath: '../DPMS/Upload.fileUploader',
                    files: dom.byId('idEvidence').value,
                    lblFile: i18n.labelEvidence,
                    gridSize: -1,
                    fullScreenButton: true,
                    onFileAdded: function () {
                        dom.byId('emptyEvidences').style.display = 'none';
                    },
                    onLoaded: function (e) {
                        if (e.bean && e.bean.count > 0) {
                            dom.byId('emptyEvidences').style.display = 'none';
                        } else {
                            dom.byId('emptyEvidences').style.display = 'block';
                        }
                    }
                }, 'fileEvidence');
            } else {
                fE = {
                    get: function () {
                        return null;
                    }
                };
            }
        }
        function setupGridACI() {
            if (!dom.byId('activitiesACIGrid')) {
                return;
            }
            new FindingActivityGrid({
                size: 0,
                freezeHeader: false,
                serviceStore: "Finding-Activity.action?currentEntityId=" + findingId,
                methodName: "getActivitiesACI",
                container: "activitiesACIGrid",
                isManager: isManager,
                searchContainer: 'none',
                noEyes: false,
                noPagination: true,
                noSettings: true,
                noExcel: true,
                onOperationSuccess: reloadFinding,
                paginationInfo: 'none',
                deleteColumn: false
            }, false, true, true, null, false).startup().then(function(grid){
                grid.refreshCurrentPage();
                activityGrids.aciGrid = grid;
            });
        }
        function setupGridAAT() {
            if (!dom.byId('activitiesAATGrid')) {
                return;
            }
            var canDelete = status !== effectivelyClosed;
            new FindingActivityGrid({
                size: 0,
                freezeHeader: false,
                serviceStore: "Finding-Activity.action?currentEntityId=" + findingId,
                methodName: "getActivitiesAAT",
                container: "activitiesAATGrid",
                isManager: isManager,
                searchContainer: 'none',
                noEyes: false,
                noPagination: true,
                noSettings: true,
                noExcel: true,
                onOperationSuccess: reloadFinding,
                paginationInfo: 'none',
                deleteColumn: (isManager && canDelete) || (isResponsibleAnalyzing && canDelete)
            }, false, true, true, null, false).startup().then(function(grid){
                grid.refreshCurrentPage();
                activityGrids.attGrid = grid;
            });
        }
        function onChangeDepartment() {
            core.dialog(i18n.changeDepartment, core.i18n.Validate.yes, core.i18n.Validate.no).then(changeDepartment, function () {
                dom.byId('intUbicacionId').value = originalDepartmentId;
            });
        }
        function _onGenerateCodeChange() {
            toggleGenerate('#chkGenerate', '#vchClave', i18n);
        }
        function _onCodeChange(value) {
            dom.byId('strClave').value = value;
        }

        function isMobileDevice() {
            return !MediaQueries.isDesktop();
        }

        function previewDoc(id) {
            if (id !== null) {
                callMethod({
                    url: "Document.action",
                    method: "getHasAccess",
                    params: [id]
                }).then(function (result) {
                    if (result.operationEstatus === 1) {
                        var viewerUrl = 'v-document-viewer.view?id=' + id + '&documentId=-1&requestId=-1';
                        angularNavigator.navigateLegacyBlank(viewerUrl);
                        core.hideLoader();
                    } else {
                        core.dialog(i18n.notAccesDocument);
                    }
                });
            }
        }

        //---------------------//
        // ¡Inicia lógica!     //
        //---------------------//

        var cType = gcUtil.cType;
        var findingsDocumentIds = "";
        var intAcceso = dom.byId('intAcceso').value;
        var intProcede = dom.byId('intProcede');
        var razonNoProcede = dom.byId('razonNoProcede');
        var status = +dom.byId('status').value;
        var effectivelyClosed = 6;
        const actionSatus = {
            ASSIGNED: 2,
            CLOSED: 6,
            CLOSED_EFFECTIVE: 12,
            CLOSED_UNEFFECTIVE: 13,
            CANCELED: 14
        };
        var businessUnitId = dom.byId('businessUnitId') ? +dom.byId('businessUnitId').value : null;
        var documentFormId = dom.byId('documentFormId') ? dom.byId('documentFormId').value : 0;
        var formToFill = status > 1 && core.isNumeric(documentFormId) && +documentFormId;
        var documentFormFillerId = dom.byId('documentFormFillerId') ? dom.byId('documentFormFillerId').value : '0';
        var loggedUserId = dom.byId('loggedUserId').value;
        var documentFormToFill = dom.byId('documentFormToFill') ? dom.byId('documentFormToFill').value === 'true' : false;
        var documentFormFilled = dom.byId('documentFormFilled') ? dom.byId('documentFormFilled').value === 'true' : false;
        var findingId = dom.byId('id').value;
        var documentFormId = dom.byId('documentFormId') ? dom.byId('documentFormId').value : dom.byId('documentFormId');
        var docTitle = dom.byId('documentFormCode') ? dom.byId('documentFormCode').value : '';
        var documentFormOutstandingSurveyId = dom.byId('documentFormOutstandingSurveyId') ? dom.byId('documentFormOutstandingSurveyId').value : 0;
        var documentFormSurveyId = dom.byId('documentFormSurveyId') ? dom.byId('documentFormSurveyId').value : '0';
        var cMsg, fillForm = JSON.fromJson(dom.byId('fillForm').value);
        var isRequireAnalysisOn = dom.byId('requireAnalysisOn').value === 'true';
        var intResponsableId = dom.byId('intResponsableId') ? dom.byId('intResponsableId').value : '0';
        var requireAnalysisCombo = dom.byId('requireAnalysisCombo');
        var parentCode = dom.byId('strClave').value;
        var
            loggedUser = dom.byId('intAutorId').value,
            isAdmin = dom.byId('admin').value === 'true',
            isAuthor = dom.byId('authorId').value === loggedUser,
            isOwner = dom.byId('owner').value === loggedUser,
            isManager = dom.byId('roleInModule').value === '3' || isAdmin,
            intResponsableId = dom.byId('intResponsableId') ? dom.byId('intResponsableId').value : '0',
            loggedUserId = dom.byId('loggedUserId').value,
            isResponsibleAnalyzing = intResponsableId === loggedUserId,
            isNew = dom.byId('status').value === '0',
            isClosed = dom.byId('status').value === '6',
            isParticipant = false,
            findingId = dom.byId('intAccionGenericaId').value,
            cType = gcUtil.cType,
            activityGrids = {
                aciGrid: null,
                attGrid: null
            },
            originalDepartmentId = dom.byId('intUbicacionId').value
        ;
        if(requireAnalysisCombo && requireAnalysisCombo instanceof HTMLSelectElement){
            requireAnalysisCombo.options[0].innerHTML = core.i18n.firstComboElement;
            requireAnalysisCombo.options[0].value = "";
        }
        ready(function() {
            renderCKEditors();
        });
        core.fillFooter();
        if (dom.byId('findingsDocument')) {
            gcUtil.column(dynamicFieldColums)
                .push('code', i18n.code, cType.Text())
                .push('description', i18n.document, cType.Text())
                .push('businessUnit_description', i18n.businessUnit_description, cType.Text())
                .push('preview', i18n.preview, cType.FunctionByObject(
                  ['id'], previewDoc, {
                      icon: 'ico_preguntas.gif',
                      href: '../qms/' + I18nUtil.getLang() + '/menu/legacy/v-document-viewer.view?id={id}&documentId=-1&requestId=-1'
                  }
                ), null, '60px')
            linkedGridDocument = LinkedGridFactory.create({
                id: 'findingsDocument',
                addButton: dom.byId('addDocuments'),
                onAfterAdd: function (dialog, ground, t, row) {
                    var findingId = dom.byId('intAccionGenericaId').value;
                    if (findingId !== null && findingId !== -1) {
                        var ids = [row.id];
                        var args = {
                            url: 'Action.action',
                            method: 'saveDocuments',
                            params: [findingId, ids]
                        };
                        callMethod(args).then(function () {
                            require(['bnext/angularNotice'], function(angularNotice) {
                                angularNotice.notice(i18n.documentUpdated);
                            });
                        });
                    }
                }
            }, id, 'finding-document.action', i18n, dynamicFieldColums);
        }
        query('textarea.wysiwyg').forEach(function (textarea) {
            var def = new Deferred();
            CKEDITOR.config.autoParagraph = false;
            var actualContents = CKEDITOR.config.contentsCss;
            CKEDITOR.config.contentsCss = [actualContents, '../administrator/action/styles/action.css'];
            defCKEditors.push(def);
            ckeditor.replace(textarea, {
                enterMode : CKEDITOR.ENTER_BR,
                toolbar: [
                    ['Undo', 'Redo', '-', 'Cut', 'Copy', 'Paste', 'PasteText', 'PasteFromWord', 'Replace', '-', 'Outdent', 'Indent', '-'],
                    ['Bold', 'Italic', 'Underline', 'Strike'],
                    ['NumberedList', 'BulletedList', 'Outdent', 'Indent', '-', 'JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'],
                    ['justify', 'Table', '-', 'Link', 'TextColor'],
                    ['Styles', 'Format', 'Font', 'FontSize']
                ],
                on: {
                    instanceReady: function () {
                        def.resolve();
                    },
                    language: languageQMS
                }
            });
        });
        all(defCKEditors).then(function () {
            query('textarea[disabled].wysiwyg').forEach(function (textarea) {
                if (domAttr.get(textarea, 'disabled') !== "false") {
                    ckeditor.instances[textarea.id].setReadOnly(true);
                }
            });
        });
        array.forEach(query(".actParticipant"), function (node) {
            if (node.value === dom.byId('intAutorId').value) {
                isParticipant = true;
            }
        });
        var canIAccept = dom.byId('canIAccept').value;
        var hasDelete = (isManager || isOwner || canIAccept) && !isClosed;
        var hasUpload = (isManager || isAuthor || isOwner || isParticipant || isNew || canIAccept) && !isClosed;
        if (!isNew && !isClosed) {
            showAnalisisHandle();
        }
        w.syncDocs = syncDocs;
        w.saveFiles = saveFiles;
        w.update = update;
        w.activityGrids = activityGrids;
        w.saveValidate = saveValidate;
        w.clearall = clearall;
        w.cancel = cancel;
        w._onGenerateCodeChange = _onGenerateCodeChange;
        w._onCodeChange = _onCodeChange;

        setupFileEvidence();
        setupFileAttacher();
        setupGridACI();
        setupGridAAT();

        if (!hasUpload || isNew) {
            domConstruct.destroy('addFiles');
        }

        //-----------------------------------//
        // Declaración de eventos al DOM!    //
        //-----------------------------------//

        if (dom.byId('addACI')) {
            // Evento para agregar ACI
            on(dom.byId('addACI'), 'click', addACI);
        }
        if (dom.byId('addAAT')) {
            // Evento para agregar AAT
            on(dom.byId('addAAT'), 'click', addAAT);
        }
        if (dom.byId('chkGenerate')) {
            // Evento para activar clave autogenerada
            on(dom.byId('chkGenerate'), 'click', _onGenerateCodeChange);
            }
        if (dom.byId('printButton')) {
            // Evento para imprimir pantalla
            on(dom.byId('printButton'), 'click', core.printFrm);
        }
        if (dom.byId('form1')) {
            // Evento para guardar
            on(dom.byId('form1'), 'submit', function(evt) {
                if (_closed) { 
                    evt.preventDefault();
                }
            });
        }
        if (!isNew && !isClosed) {
            if (dom.byId('intUbicacionId')) {
                // Evento para cambiar DETPO
                on(dom.byId('intUbicacionId'), 'change', onChangeDepartment);
            }
            if (dom.byId('intProcede')) {
                // Evento para cambiar PROCEDE SI/NO
                on(dom.byId('intProcede'), 'change', changeProceeds);
            }
        }
        core.autoheight('vchTitulo');
        setValidation();
        var optionButtons = [];
        const isFinalized = [actionSatus.CLOSED,actionSatus.CANCELED,actionSatus.CLOSED_EFFECTIVE,actionSatus.CLOSED_UNEFFECTIVE].indexOf(status) !== -1;
        if (+intAcceso >= 1 && !isFinalized) {
            optionButtons.push(
                new FloatingOptionButton({
                    icon: 'add',
                    text: i18n.addFiles,
                    onSelected: function () {
                        blurFloatingOptions();
                        fA.openUploadWindow();
                    }
                })
            );
        }
        var canAddDocuments = dom.byId('canAddDocuments');
        if (+intAcceso >= 1 && canAddDocuments && canAddDocuments.value === 'true' && !isFinalized) {
            optionButtons.push(
                new FloatingOptionButton(
                    {
                        icon: 'add',
                        text: i18n.addDocuments,
                        onSelected: function () {
                            blurFloatingOptions();
                            linkedGridDocument.openAddDialog();
                        }
                    })
                );
        }
        var canAddEvidences = (+intAcceso === 4 && canIAccept === 'true') || +intAcceso === 5;
        if (canAddEvidences) {
            optionButtons.push(
                new FloatingOptionButton(
                    {
                        icon: 'add',
                        text: i18n.addEvidences,
                        onSelected: function () {
                            blurFloatingOptions();
                            fE.openUploadWindow();
                        }
                    })
                );
        }
        optionButtons.push(
            new FloatingOptionButton(
                {
                    icon: 'print',
                    text: i18n.print,
                    onSelected: function () {
                        core.printFrm();
                    }
                })
            );
        if (!isFinalized && dom.byId('intAccionGenericaId').value !== '-1') {
            optionButtons.push(
              new FloatingOptionButton(
                {
                    icon: 'sync',
                    text: i18n.update,
                    onSelected: function() {
                        update();
                    }
                })
            );
        }
        new FloatingOptionsButton({
            position: 'top-left', //only top-left and bottom-left
            optionsButton: optionButtons,
            icon: 'more_vert'
        }, "floatingOptionsButtons");
        function blurFloatingOptions() {
            var mainWrapper = dom.byId('floatingOptionsButtons');
            if (mainWrapper) {
                mainWrapper.blur();
            }
        }
        // Bugfix para IOS cuando los select estan muy abajo al abrir el dropdown mueve la pantalla de maneras no deseadas
        if (dom.byId('intResponsableId') && window.matchMedia('(max-width: 48em) and (hover: none)').matches) {
            on(dom.byId('intResponsableId'),'click', function () {
                window.scrollTo(0,document.body.scrollHeight)
            })
        }
    }
    core.setLang('lang/action/nls/action').then(doEverything);
});