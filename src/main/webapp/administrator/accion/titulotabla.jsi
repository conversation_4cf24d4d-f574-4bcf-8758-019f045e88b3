<%@ page import="javax.servlet.http.*" %>
<%@ page import="javax.servlet.*" %>
<%@ page import="java.sql.*" %>
<%@ page import="java.io.*" %>
<%@ page import="java.util.*" %>
<%! 	 		  
 	 //Create first div Header Html Form
 	 public String header(String title) 
     {  
	   String code1="<TABLE ID=\"mainDiv\"> <tr><td>";
           String code2 = headerBasic(title);
           return code1 + code2;
      } 		  
 	 //Create second div Header Html Form
 	 public String header2(String title) 
     {  
	   String code1="<TABLE ID=\"secondDiv\"> <tr><td>";
           String code2 = headerBasic(title);
           return code1 + code2;
      }

 	 //Create basic Header Html Form
 	 public String headerBasic(String title) 
     {  
	 String code=" ";
        code=code + "<table cellpadding='0' cellspacing='0' border='0'>";
	code=code + "<TR>";
	code=code + "<TD>";
	code=code + "</TD>";
	code=code + "<TD></TD>";
	code=code + "<TD>";
	code=code + "</TD>";
	code=code + "</TR>";
	code=code + "<TR>";
	code=code + "<TD></TD>";
        code=code + "<TD>";
	code=code + "<table width='100%' border='0' cellspacing='0' cellpadding='0'>\n";
	code=code + "  <tr>\n";
	code=code + "    <td align='left' valign='top'> \n";
	code=code + "      <table border='0' cellspacing='0' cellpadding='0' bordercolor='black'>\n";
	code=code + "        <tr>\n";
	code=code + "          <td align='center' valign='middle'> \n";
	code=code + "           <table width='100%' border='0' cellspacing='0' cellpadding='0'>\n";
	code=code + "              <tr>\n";
	code=code + "                <td class='title'><p class='title'>"+title+"</p></td>\n";
	code=code + "              </tr>\n";
	code=code + "            </table>\n";
	code=code + "          </td>\n";
	code=code + "        </tr>\n";
	code=code + "        <tr>\n";
	code=code + "          <td align='center' valign='middle'> \n";
	code=code + "            <table width='100%' border='0' cellspacing='0' cellpadding='0'>\n";
	code=code + "              <tr>\n";
	code=code + "                <td align='left' valign='top' bgcolor='#F5F7F9'>\n";
        code=code + "                  <table cellspacing='0' cellpadding='16' bgcolor='#F5F7F9'>\n";
        code=code + "                     <tr>\n";
        code=code + "                       <td>  \n" ;
	 return code;
	 }

		  
 	 //Create first div Footer Html Form
 	 public String footer() 
     {  
	   String code1=" </td></tr></TABLE> ";
           String code2 = footerBasic();
           return code2 + code1;
      } 		  
 	 //Create second div Footer Html Form
 	 public String footer2() 
     {  
	   String code1=" </td></tr></TABLE>  \n";
            code1 += "<script type='text/javascript'> block('secondDiv'); </script>";
           String code2 = footerBasic();
           return code2 + code1;
      }

 	 //Create basic Footer Html Form
 	 public String footerBasic() 
     {
	 String code="";
code=code + "                        </td>\n";
code=code + "                      </tr>\n";
code=code + "                   </table>\n";
code=code + "                 </td>\n";
code=code + "              </tr>\n";
code=code + "            </table>\n";
code=code + "          </td>\n";
code=code + "        </tr>\n";
code=code + "      </table>\n";
code=code + "    </td>\n";
code=code + "  </tr>\n";
code=code + "</table>\n";
code=code + "</TD>";
code=code + "<TD></TD>";
code=code + "</TR>";
code=code + "<TR>";
code=code + "<TD>";
code=code + "</TD>";
code=code + "<TD></TD>";
code=code + "<TD>";
code=code + "</TD>";
code=code + "</TR>";
code=code + "</TABLE> ";
	 return code;
	 }
   %>