require(['dojo/on', 'dojo/dom', 'bnext/gridComponent', 'dojo/ready', 'dojo/dom-construct',
    'dojo/json', 'bnext/gridComponentUtil', 'dijit/registry', 'core', 'dojo/_base/array', 'dijit/Dialog', 'dojo/domReady!'],
    function(on, dom, gridComponent, ready, domConstruct,
        json, gcUtil, registry, core, array){
        var doEverything = function(i18n) { 
            var grid = {},
            gridAdd = {},
            addDialog = {},
            config = {
                businessUnitDepartment: 'businessUnitDepartment',
                building: 'building',
                area: 'area',
                section: 'section',
                item: 'item'
            },
            selectableId = 'selectableId',
            gridSize = dom.byId('floatingGridSize').value,
            deletePrintableBUDepartment = function(id) {
                deleteRow(config.businessUnitDepartment, id);
            },
            deletePrintableBuilding = function(id) {
                deleteRow(config.building, id);
            },
            deletePrintableArea = function(id) {
                deleteRow(config.area, id);
            },
            deletePrintableSection = function(id) {
                deleteRow(config.section, id);
            },
            deletePrintableItem = function(id) {
                deleteRow(config.item, id);
            },
            deleteRow = function(key, id){
                notice({
                    message:i18n.messageS.confirmDelete,
                    btn1Text: core.i18n.Validate.yes,
                    btn2Text: core.i18n.Validate.no,
                    btn1Action: function() {
                        grid[key].popRowById(id);
                        grid[key].updateDataInfo();
                    }
                });
            }, 
            pushPrintableBUDepartment = function(id){
                pushPrintable(config.businessUnitDepartment, id);
            },
            pushPrintableBuilding = function(id){
                pushPrintable(config.building, id);
            },
            pushPrintableArea = function(id){
                pushPrintable(config.area, id);
            },
            pushPrintableSection = function(id){
                pushPrintable(config.section, id);
            },
            pushPrintableItem = function(id){
                pushPrintable(config.item, id);
            },
            pushPrintable = function(key, id){
                gridAdd[key].popRowById(id);
                gridAdd[key].updateDataInfo();
            },
            requireAddPrintableBUDepartment = function() {
                requireAddPrintable(config.businessUnitDepartment);
            },
            requireAddPrintableBuilding = function() {
                requireAddPrintable(config.building);
            },
            requireAddPrintableArea = function() {
                requireAddPrintable(config.area);
            },
            requireAddPrintableSection = function() {
                requireAddPrintable(config.section);
            },
            requireAddPrintableItem = function() {
                requireAddPrintable(config.item);
            },
            requireAddPrintable = function(key) {
                grid[key].createExtraCompositeCriteria(gridAdd[key],'id',['printableId',config[key] + 'Id']);
                addDialog[key].domNode.style.top = '50px';
                addDialog[key].set('title',i18n.messageS.addToPrintList);
                addDialog[key].gridCaller = grid[key];
                addDialog[key].show();
                gridAdd[key].refreshData();
            },
            requireHideAddPrintableBUDepartment = function() {
                requireHideAddPrintables(config.businessUnitDepartment);
            },
            requireHideAddPrintableBuilding = function() {
                requireHideAddPrintables(config.building);
            },
            requireHideAddPrintableArea = function() {
                requireHideAddPrintables(config.area);
            },
            requireHideAddPrintableSection = function() {
                requireHideAddPrintables(config.section);
            },
            requireHideAddPrintableItem = function() {
                requireHideAddPrintables(config.item);
            },
            requireHideAddPrintables = function(key) {
                addDialog[key].hide();
                addDialog[key].gridCaller.updateDataInfo();
            },
            addSelectedPrintableBUDepartment = function() {
                addSelected(config.businessUnitDepartment);
            },
            addSelectedPrintableBuilding = function() {
                addSelected(config.building);
            },
            addSelectedPrintableArea = function() {
                addSelected(config.area);
            },
            addSelectedPrintableSection = function() {
                addSelected(config.section);
            },
            addSelectedPrintableItem = function() {
                addSelected(config.item);
            },
            addSelected = function(key) {
                var selectedRows = gridAdd[key].getSelectedRows(selectableId);
                array.forEach(selectedRows,function(row){
                    gridAdd[key].popRowById(row.id);
                    gridAdd[key].updateDataInfo();
                });
                hideLoader();
            },
            openPrint = function(printables) {
                var form = domConstruct.create('form',{
                    id: 'printForm',
                    method: 'post',
                    target: 'printForm',
                    diplay: 'none',
                    action: '../view/v.printable.viewer.view'
                },dom.byId('actionButtonsId')),
                template = domConstruct.create('input',{
                    id: 'printables',
                    name: 'printables'
                },form); 
                template.value = json.stringify(printables);
                window.open('','printForm','width=870,height=820,toolbar=0,menubar=0,resizable=1,scrollbars=1');  
                form.submit();
                domConstruct.destroy(form);
            },      
            createPrintableObject = function(key, id) {
                return {
                    linkToId: id[config[key] + 'Id'],
                    linkTo: config[key],
                    printableId: id.printableId
                };
            },
            loadPreviewPrintableBUDepartment = function(id) {
                loadPreviewPrintable(config.businessUnitDepartment,id);
            },
            loadPreviewPrintableBuilding = function(id) {
                loadPreviewPrintable(config.building,id);
            },
            loadPreviewPrintableArea = function(id) {
                loadPreviewPrintable(config.area,id);

            },
            loadPreviewPrintableSection = function(id) {
                loadPreviewPrintable(config.section,id);

            },
            loadPreviewPrintableItem = function(id) {
                loadPreviewPrintable(config.item,id);
            },
            loadPreviewPrintable = function(key, id) {
                var printables = [createPrintableObject(key, id)];
                openPrint(printables);
                hideLoader();
            },
            print = function() {
                var printables = [];
                array.forEach(grid[config.businessUnitDepartment].getBean().data,function(item){
                    printables.push(createPrintableObject(config.businessUnitDepartment, item.id));
                });
                array.forEach(grid[config.building].getBean().data,function(item){
                    printables.push(createPrintableObject(config.building, item.id));
                });
                array.forEach(grid[config.area].getBean().data,function(item){
                    printables.push(createPrintableObject(config.area, item.id));
                });
                array.forEach(grid[config.section].getBean().data,function(item){
                    printables.push(createPrintableObject(config.section, item.id));
                });
                array.forEach(grid[config.item].getBean().data,function(item){
                    printables.push(createPrintableObject(config.item, item.id));
                });
                if(printables.length === 0){
                    core.dialog(i18n.messageS.validatePrintList, core.i18n.Validate.accept);
                    return;
                }
                openPrint(printables);
                hideLoader();
            },
            initialize = function() {
                var columns = {},
                columnsAdd = {};
                columns[config.businessUnitDepartment] = [];
                columns[config.building] = [];
                columns[config.area] = [];
                columns[config.section] = [];
                columns[config.item] = [];
                columnsAdd[config.businessUnitDepartment] = [];
                columnsAdd[config.building] = [];
                columnsAdd[config.area] = [];
                columnsAdd[config.section] = [];
                columnsAdd[config.item] = [];
                gcUtil.column(columns[config.building])
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('building.businessUnit.description',i18n.messageS.businessUnit)
                        .push('building.description',i18n.messageS.building)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableBuilding, gridIcons.preview), null, '50px')
                        .push('delete', i18n.messageS.popRow, 
                            gcUtil.cType().Function(['id'], deletePrintableBuilding, gridIcons.remove), null, '50px');
                gcUtil.column(columns[config.businessUnitDepartment])
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('businessUnitDepartment.businessUnit.description',i18n.messageS.businessUnit)
                        .push('businessUnitDepartment.department.description',i18n.messageS.businessUnitDepartment)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableBUDepartment, gridIcons.preview), null, '50px')
                        .push('delete', i18n.messageS.popRow, 
                            gcUtil.cType().Function(['id'], deletePrintableBUDepartment, gridIcons.remove), null, '50px');
                gcUtil.column(columns[config.area])
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('area.building.description',i18n.messageS.building)
                        .push('area.description',i18n.messageS.area)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableArea, gridIcons.preview), null, '50px')
                        .push('delete', i18n.messageS.popRow, 
                            gcUtil.cType().Function(['id'], deletePrintableArea, gridIcons.remove), null, '50px');
                gcUtil.column(columns[config.section])
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('section.area.description',i18n.messageS.area)
                        .push('section.description',i18n.messageS.section)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableSection, gridIcons.preview), null, '50px')
                        .push('delete', i18n.messageS.popRow, 
                            gcUtil.cType().Function(['id'], deletePrintableSection, gridIcons.remove), null, '50px');
                gcUtil.column(columns[config.item])
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type) 
                        .push('item.section.area.description',i18n.messageS.area)
                        .push('item.section.description',i18n.messageS.section)
                        .push('item.description',i18n.messageS.item)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableItem, gridIcons.preview), null, '50px')
                        .push('delete', i18n.messageS.popRow, 
                            gcUtil.cType().Function(['id'], deletePrintableItem, gridIcons.remove), null, '50px');
                gcUtil.column(columnsAdd[config.businessUnitDepartment])
                        .push(selectableId, null, gcUtil.cType().Selectable(), null, '25px')
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('businessUnitDepartment.businessUnit.description',i18n.messageS.businessUnit)
                        .push('businessUnitDepartment.department.description',i18n.messageS.businessUnitDepartment)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableBUDepartment, gridIcons.preview), null, '50px')
                        .push('delete', i18n.messageS.pushRow, 
                            gcUtil.cType().Function(['id'], pushPrintableBUDepartment, gridIcons.append), null, '50px');
                gcUtil.column(columnsAdd[config.building])
                        .push(selectableId, null, gcUtil.cType().Selectable(), null, '25px')
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('building.businessUnit.description',i18n.messageS.businessUnit)
                        .push('building.description',i18n.messageS.building)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableBuilding, gridIcons.preview), null, '50px')
                        .push('delete', i18n.messageS.pushRow, 
                            gcUtil.cType().Function(['id'], pushPrintableBuilding, gridIcons.append), null, '50px');
                gcUtil.column(columnsAdd[config.area])
                        .push(selectableId, null, gcUtil.cType().Selectable(), null, '25px')
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('area.building.description',i18n.messageS.building)
                        .push('area.description',i18n.messageS.area)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableArea, gridIcons.preview), null, '50px')
                        .push('add', i18n.messageS.pushRow, 
                            gcUtil.cType().Function(['id'], pushPrintableArea, gridIcons.append), null, '50px');
                gcUtil.column(columnsAdd[config.section])
                        .push(selectableId, null, gcUtil.cType().Selectable(), null, '25px')
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type)
                        .push('section.area.description',i18n.messageS.area)
                        .push('section.description',i18n.messageS.section)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableSection, gridIcons.preview), null, '50px')
                        .push('add', i18n.messageS.pushRow, 
                            gcUtil.cType().Function(['id'], pushPrintableSection, gridIcons.append), null, '50px');
                gcUtil.column(columnsAdd[config.item])
                        .push(selectableId, null, gcUtil.cType().Selectable(), null, '25px')
                        .push('printable.description',i18n.messageS.description) 
                        .push('printable.printableType.description',i18n.messageS.type) 
                        .push('item.section.area.description',i18n.messageS.area)
                        .push('item.section.description',i18n.messageS.section)
                        .push('item.description',i18n.messageS.item)
                        .push('preview', i18n.messageS.preview, 
                            gcUtil.cType().Function(['id'], loadPreviewPrintableItem, gridIcons.preview), null, '50px')
                        .push('add', i18n.messageS.pushRow, 
                            gcUtil.cType().Function(['id'], pushPrintableItem, gridIcons.append), null, '50px');
                addDialog[config.businessUnitDepartment] = registry.byId('addPrintableBUDepartment');
                addDialog[config.building] = registry.byId('addPrintableBuilding');
                addDialog[config.area] = registry.byId('addPrintableArea');
                addDialog[config.section] = registry.byId('addPrintableSection');
                addDialog[config.item] = registry.byId('addPrintableItem');
                grid[config.businessUnitDepartment] = new gridComponent(gcUtil.basic({
                    genericGridIsSortable: false,
                    size: 0,
                    id: 'gridPrintableBUDepartment',
                    container: 'dataGridPrintableBUDepartment',
                    serviceStore: 'PrintableBusinessUnitDepartment.action', 
                    methodName: 'getRows', 
                    noRegMessage: i18n.messageS.notAddedPrintables,
                    noEyes: true,
                    refreshOnPopRow: false,
                    fullColumns: columns[config.businessUnitDepartment]
                }));
                grid[config.building] = new gridComponent(gcUtil.basic({
                    genericGridIsSortable: false,
                    size: 0,
                    id: 'gridPrintableBuilding',
                    container: 'dataGridPrintableBuilding',
                    serviceStore: 'PrintableBuilding.action', 
                    methodName: 'getRows', 
                    noRegMessage: i18n.messageS.notAddedPrintables,
                    noEyes: true,
                    refreshOnPopRow: false,
                    fullColumns: columns[config.building]
                }));
                grid[config.area] = new gridComponent(gcUtil.basic({
                    genericGridIsSortable: false,
                    size: 0,
                    id: 'gridPrintableArea',
                    container: 'dataGridPrintableArea',
                    serviceStore: 'PrintableArea.action', 
                    methodName: 'getRows', 
                    noRegMessage: i18n.messageS.notAddedPrintables,
                    noEyes: true,
                    refreshOnPopRow: false,
                    fullColumns: columns[config.area]
                }));
                grid[config.section] = new gridComponent(gcUtil.basic({
                    genericGridIsSortable: false,
                    size: 0,
                    id: 'gridPrintableSection',
                    container: 'dataGridPrintableSection',
                    serviceStore: 'PrintableSection.action', 
                    methodName: 'getRows', 
                    noRegMessage: i18n.messageS.notAddedPrintables,
                    noEyes: true,
                    refreshOnPopRow: false, 
                    fullColumns: columns[config.section]

                }));
                grid[config.item] = new gridComponent(gcUtil.basic({
                    genericGridIsSortable: false,
                    size: 0,
                    id: 'gridPrintableItem',
                    container: 'dataGridPrintableItem',
                    serviceStore: 'PrintableItem.action', 
                    methodName: 'getRows', 
                    noRegMessage: i18n.messageS.notAddedPrintables,
                    noEyes: true,
                    refreshOnPopRow: false,
                    fullColumns: columns[config.item]
                }));
                gridAdd[config.businessUnitDepartment] = new gridComponent(gcUtil.basic({
                    size: gridSize,
                    id: 'gridAddPrintableBUDepartment', 
                    container: 'dataGridAddPrintableBUDepartment',
                    searchContainer: '#auto',
                    noRegMessage: i18n.messageS.notFoundPrintables,
                    serviceStore: 'PrintableBusinessUnitDepartment.action',
                    methodName: 'getRowsActive', 
                    noEyes: true,
                    fullColumns: columnsAdd[config.businessUnitDepartment],
                    resultsInfo: '#auto',
                    paginationInfo: '#auto'
                }));
                gridAdd[config.building] = new gridComponent(gcUtil.basic({
                    size: gridSize,
                    id: 'gridAddPrintableBuilding', 
                    container: 'dataGridAddPrintableBuilding',
                    searchContainer: '#auto',
                    noRegMessage: i18n.messageS.notFoundPrintables,
                    serviceStore: 'PrintableBuilding.action',
                    methodName: 'getRowsActive', 
                    noEyes: true,
                    fullColumns: columnsAdd[config.building],
                    resultsInfo: '#auto',
                    paginationInfo: '#auto'
                }));
                gridAdd[config.area] = new gridComponent(gcUtil.basic({
                    size: gridSize,
                    id: 'gridAddPrintableArea', 
                    container: 'dataGridAddPrintableArea',
                    searchContainer: '#auto',
                    noRegMessage: i18n.messageS.notFoundPrintables,
                    serviceStore: 'PrintableArea.action',
                    methodName: 'getRowsActive', 
                    noEyes: true,
                    fullColumns: columnsAdd[config.area],
                    resultsInfo: '#auto',
                    paginationInfo: '#auto'
                }));
                gridAdd[config.section] = new gridComponent(gcUtil.basic({
                    size: gridSize,
                    id: 'gridAddPrintableSection', 
                    container: 'dataGridAddPrintableSection',
                    searchContainer: '#auto',
                    noRegMessage: i18n.messageS.notFoundPrintables,
                    serviceStore: 'PrintableSection.action',
                    methodName: 'getRowsActive', 
                    noEyes:true,
                    fullColumns: columnsAdd[config.section],
                    resultsInfo: '#auto',
                    paginationInfo: '#auto'
                }));
                gridAdd[config.item] = new gridComponent(gcUtil.basic({
                    size: gridSize,
                    id: 'gridAddPrintableItem', 
                    container: 'dataGridAddPrintableItem',
                    searchContainer: '#auto',
                    noRegMessage: i18n.messageS.notFoundPrintables,
                    serviceStore: 'PrintableItem.action',
                    methodName: 'getRowsActive', 
                    noEyes: true,
                    fullColumns: columnsAdd[config.item],
                    resultsInfo: '#auto',
                    paginationInfo: '#auto'
                }));
                grid[config.businessUnitDepartment].size = 0;
                grid[config.building].size = 0;
                grid[config.area].size = 0;
                grid[config.section].size = 0;
                grid[config.item].size = 0;
                grid[config.businessUnitDepartment].linkGrid(gridAdd[config.businessUnitDepartment],
                        gcUtil.createLinkColumn('id','id', true, ['businessUnitDepartmentId','printableId']));
                grid[config.building].linkGrid(gridAdd[config.building],
                        gcUtil.createLinkColumn('id','id', true, ['buildingId','printableId']));
                grid[config.area].linkGrid(gridAdd[config.area],
                        gcUtil.createLinkColumn('id','id', true, ['areaId','printableId']));
                grid[config.section].linkGrid(gridAdd[config.section],
                        gcUtil.createLinkColumn('id','id', true, ['sectionId','printableId']));
                grid[config.item].linkGrid(gridAdd[config.item],
                        gcUtil.createLinkColumn('id','id', true, ['itemId','printableId']));
                grid[config.businessUnitDepartment].setExtraCriteria([{
                        value: '-1L',
                        key: 'id#businessUnitDepartmentId'
                    }]);
                grid[config.building].setExtraCriteria([{
                        value: '-1L',
                        key: 'id#buildingId'
                    }]);
                grid[config.area].setExtraCriteria([{
                        value: '-1L',
                        key: 'id#areaId'
                    }]);
                grid[config.section].setExtraCriteria([{
                        value: '-1L',
                        key: 'id#sectionId'
                    }]);
                grid[config.item].setExtraCriteria([{
                        value: '-1L',
                        key: 'id#itemId'
                    }]);
                grid[config.businessUnitDepartment].refreshData();  
                grid[config.building].refreshData();    
                grid[config.area].refreshData();
                grid[config.section].refreshData();
                grid[config.item].refreshData();
            },
            setEvents = function() {
                on(dom.byId('addPrintableBUDepartmentBtn'), 'click', requireAddPrintableBUDepartment);
                on(dom.byId('addPrintableBuildingBtn'), 'click', requireAddPrintableBuilding);
                on(dom.byId('addPrintableAreaBtn'), 'click', requireAddPrintableArea);
                on(dom.byId('addPrintableSectionBtn'), 'click', requireAddPrintableSection);
                on(dom.byId('addPrintableItemBtn'), 'click', requireAddPrintableItem);
                on(dom.byId('finishBtnPrintableBUDepartment'), 'click', requireHideAddPrintableBUDepartment);
                on(dom.byId('finishBtnPrintableBuilding'), 'click', requireHideAddPrintableBuilding);
                on(dom.byId('finishBtnPrintableArea'), 'click', requireHideAddPrintableArea);
                on(dom.byId('finishBtnPrintableSection'), 'click', requireHideAddPrintableSection);
                on(dom.byId('finishBtnPrintableItem'), 'click', requireHideAddPrintableItem);
                on(dom.byId('addSelectedBtnPrintableBUDepartment'), 'click', addSelectedPrintableBUDepartment);
                on(dom.byId('addSelectedBtnPrintableBuilding'), 'click', addSelectedPrintableBuilding);
                on(dom.byId('addSelectedBtnPrintableArea'), 'click', addSelectedPrintableArea);
                on(dom.byId('addSelectedBtnPrintableSection'), 'click', addSelectedPrintableSection);
                on(dom.byId('addSelectedBtnPrintableItem'), 'click', addSelectedPrintableItem);
                on(dom.byId('printBtn'), 'click', print);
            }         
            initialize(i18n);
            setEvents(i18n);
            hideLoader();
        };   
        ready(function(){
            core.setLang('lang/fiveS/printable/nls/my.printable').then(doEverything);
        });
    });