require(['core', 'bnext/saveHandle','dojo/dom','dojo/on','bnext/gridComponent','bnext/gridComponentUtil',
    'dijit/registry', 'dojo/_base/array', 'dojo/dom-construct', 'bnext/callMethod', 'dojo/_base/lang', 'dojo/json', 'dojo/domReady!'],
    function(core, saveHandle, dom, on, gridComponent, gcUtil, 
        registry, array, domConstruct, callMethod, lang, json){
    fillFooter();   
    function doEverything(i18n) { 
        dom.byId('businessUnitDepartmentSelect').options[0].innerHTML = core.i18n.firstComboElement;
        dom.byId('areaSelect').options[0].innerHTML = core.i18n.firstComboElement;
        var myData = null,
        gridPrintableArea,
        gridAddPrintables,
        selectedRowId = 'selectedRowId',
        businessUnitDepartmentSelect = dom.byId('businessUnitDepartmentSelect'),
        areaSelect = dom.byId('areaSelect'),
        baseServiceStore = 'PrintableAreaLite.action',
        floatingGridSize = dom.byId('floatingGridSize').value,
        defineValidationRules = function(){
            $('#validate_form').validate({
                rules:{
                    areaSelect:{required : true}
                },
                messages: {
                    areaSelect: i18n.Validate.missing_area
                }
            });
        },
        getData = function(){
            var data = {};
            data.areaId = +dom.byId("areaSelect").value;
            data.printableIds = gridPrintableArea.getBean().data;
            return data;
        },
        changeBusinessUnitDepartment = function(){ 
            return callMethod({
                url:'../DPMS/Area.action',
                method:'getAreasByBusinessUnitDepartmentId',
                params:[+businessUnitDepartmentSelect.value || null]
                }).then(
                //success
                function(resultado) {
                    var nr = resultado.length === 0,
                    lastValue = lang.clone(areaSelect.value);
                    fillCombo('#areaSelect', resultado, 'text', 'value', false);
                    if(nr) {
                        domConstruct.destroy(areaSelect.options[0]);
                    }
                    areaSelect.value = lastValue;
                    def.resolve();
                }, function() {
                //error
                    hideLoader();
                    def.resolve();
                });
        }, 
        save = function(){
            var valid = validatData();
            if (valid) {
                confirmDialog().then(
                    function(){
                        showLoader(i18n.Validate.saving);
                        var data = getData();
                        var printables = [];
                        array.forEach(data.printableIds,function(item) {
                            printables.push(item.id.printableId);
                        });
                        myData.setData([data.areaId, printables]);
                        myData.saveData();
                    },hideLoader
                );
            } 
            return null;
        },
        validatData = function() {  
            return $("#validate_form").valid();
        },
        getBaseFilterId = function() {
            return '?currentEntityId=' + (areaSelect.value || '-1');
        },
        initialize = function() {
            defineValidationRules();
            Validate.onBtnAdd = '../DPMS/v.printable.area.action';
            Validate.onBtnControl = '../DPMS/v.printable.area.list.action';
            var liPrintSelected;
            myData = new saveHandle({
                savedObjName: 'myData',
                serviceStore : '../DPMS/PrintableArea.action',
                methodName : 'bulkUpdate',
                hookUpdateSuccess: successful,
                hookUpdateFailure:"void(0)"
            });
            liPrintSelected = domConstruct.create('li',{},dom.byId('actionButtonsId'));
            domConstruct.create('input',{
                'class': 'Button',
                type: 'button',
                id: 'printSelectedBtn',
                value: i18n.messageS.printSelected
            },liPrintSelected);
            hideLoader();
        },
        successful = function() {
            hideLoader();
        },
        cancel = function(){
            notice({
                message : i18n.messageS.sentToControl,
                btn1Action: function(){
                    showLoader(i18n.messageS.loadingControl);
                    document.location = '../DPMS/v.printable.area.list.view';
                },
                btn2Action:function(){}
            });
        },
        deletePrintable = function(id) {
            notice({
                message: i18n.messageS.confirmDetachPrintable,
                btn1Text: i18n.Validate.yes,
                btn2Text: i18n.Validate.no,
                btn1Action: function() {
                    gridPrintableArea.popRowById(id);
                    gridPrintableArea.updateDataInfo();
                }
            });
        },
        pushPrintable = function(id) {
            gridAddPrintables.popRowById(id);
            gridAddPrintables.updateDataInfo();
            if (dom.byId('gridPrintableArea')) {
                dom.byId('gridPrintableArea').style.display ='';
            }
        },
        openPrint = function(printables) {
            var form = domConstruct.create('form',{
                id: 'printForm',
                method: 'post',
                target: 'printForm',
                diplay: 'none',
                action: '../view/v.printable.viewer.view'
            },dom.byId('actionButtonsId')),
            template = domConstruct.create('input',{
                id: 'printables',
                name: 'printables'
            },form); 
            template.value = json.stringify(printables);
            window.open('','printForm','width=870,height=820,toolbar=0,menubar=0,resizable=1,scrollbars=1');  
            form.submit();
            domConstruct.destroy(form);
        },      
        createPrintableObject = function(printableId) {
            return {
                linkToId: areaSelect.value,
                linkTo: 'area',
                printableId: printableId
            };
        },
        loadPreviewPrintable = function(id) {
            var printables = [createPrintableObject(id.printableId)];
            openPrint(printables);
            hideLoader();
        },
        printSelected = function() {
            var selectedRows = gridPrintableArea.getSelectedRows(selectedRowId),
            printables = [];
            if (isNull(selectedRows) || selectedRows.length === 0) {
                return $("#validate_form").valid();
            }
            array.forEach(selectedRows,function(row){
                printables.push(createPrintableObject(row.id.printableId));
            });
            openPrint(printables);
            hideLoader();
        },
        hidePreviewPrintable = function() {
            registry.byId('preview').hide();
        };
        showAddPrintables = function() {
            if (areaSelect.value !== '') {
                dom.byId('gridAddPrintables').style.top = '50px';
                registry.byId('addPrintables').set('title',i18n.messageS.addPrintablesToAreaTitle);
                registry.byId('addPrintables').show();
                gridAddPrintables.setPageSize(floatingGridSize);
                gridPrintableArea.setExtraCriteria([{}]);
            } else {
                $("#validate_form").valid();
            }
        },
        hideAddPrintables = function() {
            registry.byId('addPrintables').hide();
            gridPrintableArea.updateDataInfo();
        },
        refreshPrintableArea = function() {
            gridPrintableArea.setServiceStore(baseServiceStore + getBaseFilterId());
            gridAddPrintables.setExtraCriteria([]);
            gridPrintableArea.refreshData();
        },
        changeArea = function() {
            if ( gridPrintableArea.getBean().data.length > 0) {
                notice({
                    message : i18n.messageS.reloadPrintableAreaGrid,
                    btn1Action: refreshPrintableArea,
                    btn2Action:function(){}
                }); 
            } else {
                refreshPrintableArea();
            }
        },
        configGrids = function() {
            var columnsPrintablesArea = [],
            columnsPrintables = [],
            linkId,
            linkColumns = [];
            gcUtil.column(columnsPrintablesArea)
                    .push(selectedRowId, null, gcUtil.cType().Selectable(), null, '25px')
                    .push('printable.printableType.description',i18n.messageS.type)
                    .push('printable.description',i18n.messageS.description)
                    .push('preview', i18n.messageS.preview, gcUtil.cType().Function(['id'], loadPreviewPrintable, gridIcons.preview), null, '50px')
                    .push('delete', i18n.messageS.delete, gcUtil.cType().Function(['id'], deletePrintable, gridIcons.remove), null, '50px');
            array.forEach(columnsPrintablesArea, function(column) {
                column.isSortable = false;
            });
            gcUtil.column(columnsPrintables)   
                    .push('description',i18n.messageS.description)
                    .push('printableType.description',i18n.messageS.type)
                    .push('add', i18n.messageS.pushRow,
                        gcUtil.cType().Function(["id"],pushPrintable,gridIcons.append),null,'50px');
            gridPrintableArea = new gridComponent(gcUtil.basic({
                refreshOnPopRow: false,
                noEyes: true,
                container: 'gridPrintableArea',
                entityName: 'printableArea',
                serviceStore: baseServiceStore + getBaseFilterId(),
                methodName: 'getGroundRows',
                searchContainer: 'none',
                size: 0,
                noRegMessage: i18n.messageS.notAddedPrintables,
                fullColumns: columnsPrintablesArea,
                genericGridIsSortable: false,
                onLoaded: function() {
                    hideLoader();
                }
            }));
            gridAddPrintables = new gridComponent(gcUtil.basic({
                refreshOnPopRow: false,
                noEyes: true,
                container: 'gridAddPrintables',
                serviceStore: 'Printable.action',
                methodName: 'getRowsActive',
                noRegMessage: i18n.messageS.notFoundPrintables,
                size: floatingGridSize,
                fullColumns : columnsPrintables
            }));
            gridPrintableArea.setServiceStore(baseServiceStore + getBaseFilterId());
            gridPrintableArea.refreshData();    
            linkId = gcUtil.createLinkColumn('id.printableId','id');
            linkColumns.push(gcUtil.createLinkColumn('printable.printableType.description','printableType.description'));
            linkColumns.push(gcUtil.createLinkColumn('printable.description','description'));
            gridPrintableArea.linkGrid(gridAddPrintables, linkId, linkColumns);
        },
        setEvents = function() {
            on(dom.byId('saveBtn'), 'click', function() {
                save();
            });
            on(dom.byId('cancelBtn'), 'click', function() {
                cancel();
            });
            on(dom.byId('addPrintableBtn'), 'click',function () {
                showAddPrintables();
            });
            on(dom.byId('finishBtn_Printables'), 'click', hideAddPrintables);
            on(dom.byId('finishBtn_Preview'), 'click', hidePreviewPrintable);
            on(dom.byId('areaSelect'), 'change', function () {
                changeArea();
            });
            on(dom.byId('businessUnitDepartmentSelect'), 'change', changeBusinessUnitDepartment);
            on(dom.byId('printSelectedBtn'), 'click', printSelected);
        };
        initialize();
        configGrids();
        setEvents();
    }
    core.setLang('lang/fiveS/printable/nls/printable.area').then(doEverything);    
});