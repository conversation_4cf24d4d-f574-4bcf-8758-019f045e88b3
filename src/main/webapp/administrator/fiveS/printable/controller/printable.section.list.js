require(['bnext/gridComponent', 'bnext/gridComponentUtil', 'dojo/Deferred', 'dojo/string', 'dojo/dom',
    'core', 'dojo/domReady!'],
function (gridComponent, gcUtil, Deferred, string, dom,
        core) {
    function doEverything(i18n) {
        fillFooter();
        var grid, 
        columns = [];
    
        changeStatus = function (id, code) {
        dialogExecutioner(string.substitute(i18n.messages.statusChangeMsg, {
            title: code
        }), core.i18n.Validate.yes, core.i18n.Validate.no,
                'PrintableSection.action', 'toggleStatus', [id],
                i18n.messages.statusChangeSuccess, '', i18n.messages.accept, grid);
        };
        
        var def = new Deferred(),
        statusList = [
            {name:firstComboElement, icon:'', value:'' },
            {name: i18n.cubeNames.active, icon: gridCubes.green, value:1},
            {name: i18n.cubeNames.inactive, icon: gridCubes.gray, value:0}
        ];
        gcUtil.column(columns)
            .push('status', i18n.colNames.status,
                gcUtil.cType().FunctionImage(['id'], changeStatus, statusList, true))
            .push('printable.description', i18n.gridLabel.printableDescription,columnTypes.text, {type: 'texto',col: 1})
            .push('section.area.description', i18n.gridLabel.areaDescription,columnTypes.text, {type: 'texto',col: 1})
            .push('section.description', i18n.gridLabel.sectionDescription,columnTypes.text, {type: 'texto',col: 1})
            .push('edit', i18n.colNames.edit, 
                gcUtil.cType().Function(['id.sectionId'], editRecord_grid, gridIcons.edit), null, '50px');
        grid = new gridComponent(gcUtil.basic({
            size: dom.byId('gridSize').value,
            container: 'grid',
            serviceStore: 'PrintableSectionLite.action',
            windowPath: dom.byId('windowPath').value,
            fullColumns: columns,
            toogleEstatus: changeStatus,
            onLoaded: function () {
                def.resolve();
            }
        }));
        grid.setPageSize(dom.byId('gridSize').value);
        return def.promise;
    };
    core.setLang('lang/fiveS/printable/nls/printable.section.list').then(doEverything).then(core.hideLoader);
});

function editRecord_grid (id){
    document.submit_form.id.value = id;
    document.submit_form.submit();
}