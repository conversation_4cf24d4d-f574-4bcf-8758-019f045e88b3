<%-- 
    Document   : printable.building
    Created on : 20/03/2015, 12:10 PM
    Author     : <PERSON><PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>

<!DOCTYPE html>
<html>
    <head>
        <title>Relación de Edificio e Imprimibles</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <%@include file="../../../components/requiredScripts.jsp" %>
        <script type="text/javascript" src="c.printable.building.controller?${systemVersion}"></script>
        <style>
            .hidden1,.hidden2,.hidden3, 
            .grid_gridPrintableBuilding, 
            #paging_full_numbers_gridPrintableBuilding,
            #gridPrintableBuilding .regHelp,
            #gridPrintableBuilding .gridFooter {
                display: none!important
            }
            .hidden1.show,.hidden2.show,.hidden3.show{
                display: block!important
            }
            
            .bnext .dijitDialogPaneContent {
                min-width: 500px;
            }
            .gridExpandable {
                width: 440px!important;
            }
            ul#content_area div.info {
                border-color: gray;
                font-style: italic;
                text-indent: 20px;
                width: 230px;
                float: left;
                clear: right;
                background: url('../images/common/info16.png') no-repeat 4px 3px;
                padding: 5px;
            }            
            ul#content_area div.gridExpandable td {
                border: 1px #DDD solid;
            }
            #footerComponent {
                width: 100%; 
            }
            #gridPrintableBuilding input[type="checkbox"] {
                width: 25px;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../../components/loader.jsp" %>
        <form method="POST" id="validate_form">
            <div class="content_title">
                <h1 id="mainTitle">Relación de Edificios e Imprimibles</h1>
            </div>
            <ul class="content_area" id="content_area">
                <li>
                    <label id="lblBuilding">Edificio: </label>
                    <s:select name="buildingSelect" id="buildingSelect" value="%{buildingId}"
                              list="buildings" listKey="value" listValue="text"/>
                </li>
                <li>                    
                    <label>
                        <a id="addPrintableBtn" title="Click para agregar" class="Button"></a>
                    </label>
                    <table id="gridPrintableBuilding" style="float:left"><tr><td>Presione el boton para agregar</td></tr></table>
                </li>
                <%@include file="../../../components/saveButtons.jsp" %>
            </ul>
            <div dojoType="dijit.Dialog" id="addPrintables">
                <ul class="content_area">
                    <li tabindex="0">
                        <table id="gridAddPrintables"></table>
                    </li>
                    <li class="separator" style="padding-bottom: 0px !important;">
                        <input class="Button right finishBtn" type="button" id="finishBtn_Printables" value=""/>
                    </li>
                </ul>
            </div>
            <div dojoType="dijit.Dialog" id="preview">
                <ul class="content_area">
                    <li tabindex="0">
                        <div id="previewDiv"></div>
                    </li>
                    <li class="separator" style="padding-bottom: 0px !important;">
                        <input class="Button right finishBtn" type="button" id="finishBtn_Preview" value=""/>
                    </li>
                </ul>
            </div>
        </form>
        <s:hidden name="id" id="id"/>
        <s:hidden name="systemColor" id="SYSTEM_COLOR" />
        <s:hidden name="superUser" id="superUser"/>
        <s:hidden name="floatingGridSize" id="floatingGridSize"/> 
        <%@include file="../../../components/footer.jsp" %>
    </body>
</html>