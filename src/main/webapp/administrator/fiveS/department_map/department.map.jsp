<!DOCTYPE html>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@taglib prefix="s" uri="/struts-tags" %>
<html>
    <head>
        <title>Mapa de Departamentos</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" href="../styles/tagger.css?${systemVersion}" >
        <%@include file="../../../components/requiredScripts.jsp" %>   
        <%@include file="../../../components/colorpicker.jsp" %>
        <script type="text/javascript" src="c.department.map.controller?${systemVersion}"></script>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../../components/loader.jsp" %>
        <form method="POST" id="validate_form">
            <div class="content_title">
                <h1 id="mainTitle"></h1>
            </div>
            <ul class="content_area" id="contenido">
                <li>
                    <label id="lblCode"></label>
                    <ul class ="inner_form_item">
                        <li><input type="text" name="code" id="txtCode" value=""/></li>
                        <li><label id="lblGenerateKey"></label>
                            <input type="checkbox" name="chkGenerate" id ="chkGenerate" /></li>
                    </ul>
                </li>
                <li>
                    <label id="lblDescription"></label>
                    <input type="text" name="description" id="description" value=""/>
                </li>
                <li>
                    <s:select name="departmentId" id="departmentId" 
                              list="departments" label="departments"
                              listKey="value" listValue="text" >
                    </s:select>
                </li>                
                <li id="fUploader"></li>
                <li id ="tagMe">
                    <div id ="leftPane">
                        <img id="imgCanvas" src="" class="hidden"/>
                        <div id="tagit" class ="hidden">
                            <div class="box"></div>  
                            <div class="name">
                                <select id = "childId"></select>
                                <input type="button" name="taggitSave" class="button" id ="taggitSave" />
                                <input type="button" name="taggitCancel" class="button" id ="taggitCancel" />
                            </div>                            
                        </div>
                    </div>
                    <div id="rightPane">
                        <div>
                            <ol id="taglist">
                                <li rel="0" class="titleLi">
                                    <div>
                                        <span id="tagname" class="tagName">Elemento</span>
                                        <span class="removeHeader"></span>
                                        <span class="size" id="heightLabel">Alto</span>
                                        <span class="size" id="widthLabel">Ancho</span>
                                        <span class="colorTitle">Color</span>
                                    </div>
                                </li>
                            </ol>
                        </div>
                    </div>
                </li>
                <%@include file="../../../components/saveButtons.jsp" %>                    
            </ul>
            <s:hidden name="systemColor" id="SYSTEM_COLOR" />
            <s:hidden name="id" id="id"/>
            <s:hidden name="status" id="status"/>
            <s:hidden name="mapId" id="mapId"/>
            <input type="hidden" id="onBtnAdd" value="../view/v.department.map.view" />
            <input type="hidden" id="onBtnControl" value="../view/v.department.map.list.view" />
        </form>
        <%@include file="../../../components/footer.jsp" %>
    </body>
</html>