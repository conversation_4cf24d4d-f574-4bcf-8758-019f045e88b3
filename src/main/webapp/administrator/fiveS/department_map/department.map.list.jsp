<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Mapas de departamentos</title>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <jsp:include page="../../../components/requiredScripts.jsp" />
        <script type='text/javascript' src="../controller/c.department.map.list.controller?${systemVersion}"></script>
        <style>
            #footerComponent {
                width: 100%;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../../components/loader.jsp" %>
        <form>
            <div class ="_body">
                <div class="content_title">
                    <h1 id="mainTitle"></h1>
                </div>
                <div id="MessageBox"></div>
                <ul class="content_area">
                    <li class ="table_workarea">
                        <table id="grid"></table>
                    </li>
                </ul>
            </div>
            <%@include file="../../../components/sessionVariables.jsp" %>
        </form>
        <form name="edit" action="v.department.map.view">
            <input type="hidden" id="id" name="id" value='-1'/>
            <input type="hidden" id="status" name="status" value='-1'/>
            <input type="hidden" id="code" name="code" value='-1'/>
            <input type="hidden" id="description" name="description" value='-1'/>
            <input type="hidden" id="departmentId" name="departmentId" value='-1'/>
        </form>
        <%@include file="../../../components/footer.jsp" %>
    </body>
</html>
