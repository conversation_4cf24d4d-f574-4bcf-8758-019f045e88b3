require(['bnext/callMethod', 'dojo/query', 'dojo/dom', 'dojo/on', 'dojo/dom-attr', 'core', 'dojo/domReady!'], 
    function (callMethod, query, dom, on, domAttr, core) {
    fillFooter();
    var doEverything = function (i18n) {
        defineValidationRules();
        var save = function () {
            var valid = $("#validate_form").valid();
            if (valid) {
                notice({
                    message: i18n.tagNames.confirmSaving,
                    btn1Action: function () {
                        showLoader(i18n.Validate.saving);
                        callMethod({
                            url: "../DPMS/Item.action",
                            method: "save",
                            params: [
                                {
                                    "id": dom.byId('id').value,
                                    "description": dom.byId('description').value,
                                    "code": dom.byId('chkGenerate').checked?'':dom.byId('txtCode').value,
                                    "sectionId": +dom.byId('sectionId').value,
                                    "parentId": +dom.byId('parentId').value,
                                    "maximum": +dom.byId('maximum').value,
                                    "minimum": +dom.byId('minimum').value,
                                    "longDescription": dom.byId('longDescription').value
                                }]
                        }).then(
                                function (s) {
                                    if (+s.operationEstatus === 1) {
                                        exitoMsg(i18n.tagNames.succesSaving, {
                                            add: '../view/v.item.view',
                                            control: '../view/v.item.list.view'
                                        });
                                    } else {
                                        //error
                                        fracasoMsg(s);
                                    }
                                    hideLoader();
                                }
                        );
                    }
                });
            }
        };

        var load = function (id) {
            var args = {
                url: "../DPMS/Item.action",
                method: "load",
                params: [id]};
            callMethod(args).then(
                    function (resultado) {
                        populate(resultado);
                    },
                    hideLoader
                    );
        },
        changeParent = function() {
            if (!core.isNull(this.value) && this.value !== '') {
                var args = {
                    url: '../DPMS/Item.action',
                    method: 'getSectionId',
                    params: [this.value]};
                callMethod(args).then( function (resultado) {
                    if (!core.isNull(resultado)) {
                        dom.byId('sectionId').value = resultado;
                        domAttr.set(dom.byId('sectionId'),'disabled','true');
                    }
                },core.hideLoader );
            } else {
                domAttr.remove(dom.byId('sectionId'),'disabled');                        
            }
        },
        updateLimits = function(){
            $('#maximum').rules("add", {
                  min: +$('#minimum').val()
             });  
            $('#minimum').rules("add", {
                  max: +$('#maximum').val()
             });  
        },
        populate = function (object) {
            var loadedVal = 0;
            query('input,textarea', 'validate_form').forEach(function (input) {
                loadedVal = object[input.name];
                if (input.name && (loadedVal || +loadedVal === 0)) {
                    input.value = loadedVal;
                }
            });
            dom.byId('sectionId').value = object.sectionId;
            dom.byId('parentId').value = object.parentId;
            updateLimits();
            core.hideLoader();
        };
        var toggleGenerateKey = function () {
            toggleGenerate('#chkGenerate', '#txtCode');
        };
        dom.byId('parentId').options[0].innerHTML = core.i18n.firstComboElement;
        dom.byId('sectionId').options[0].innerHTML = core.i18n.firstComboElement;
        on(dom.byId('maximum'), 'change', updateLimits);
        on(dom.byId('minimum'), 'change', updateLimits);
        on(dom.byId('saveBtn'), 'click', save);
        on(dom.byId('cancelBtn'), 'click', cancel);
        on(dom.byId('chkGenerate'), 'click', toggleGenerateKey);
        on(dom.byId('parentId'), 'change', changeParent);
        var id = dom.byId('id').value;
        if (id && +id !== -1) {
            load(id);
        } else {
            dom.byId('status').value = 1;
            hideLoader();
        }
        updateLimits();
        function defineValidationRules() {
            $.validator.addMethod(
                "regex",
                function(value, element, regexp) {
                    var re = new RegExp(regexp);
                    return this.optional(element) || re.test(value);
                },
                i18n.Validate.positiveNumber
            );
            $('#validate_form').validate({
                rules: {
                    code: {
                        required: true
                        , maxlength: 100
                    },
                    description: {
                        required: true
                        , maxlength: 100
                    },
                    sectionId: {
                        required: true
                    },
                    maximum: {
                        required: true,
                        number:true,
                        min:+$('#minimum').value,
                        maxlength: 4
                    },
                    minimum: {
                        required: true,
                        number:true,
                        max:+$('#maximum').value,
                        maxlength: 4  
                    }
                },
                messages: {
                    code: specifiedMessage(core.i18n.Validate['invalid_required_text'], 'specify', 10),
                    description: specifiedMessage(core.i18n.Validate['invalid_required_text'], 'specify', 100),
                    sectionId: core.i18n.Validate.missingCombo,
                    maximum:{
                        required:core.i18n.Validate.missingField,
                        number:core.i18n.Validate.numericOnly,
                        min:i18n.Validate.limits,
                        maxlength:i18n.Validate.maxlength
                    },
                    minimum:{
                        required:core.i18n.Validate.missingField,
                        number:core.i18n.Validate.numericOnly,
                        max:i18n.Validate.limits,
                        maxlength:i18n.Validate.maxlength
                    }
                }
            });
            $('#maximum').rules('add', {regex: "^[0-9]+$"});
            $('#minimum').rules('add', {regex: "^[0-9]+$"});
        }
    };
    core.setLang('lang/fiveS/item/nls/item').then(doEverything);
});

