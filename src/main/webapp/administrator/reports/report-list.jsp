<%-- 
    Document   : report-list
    Created on : Mar 8, 2018, 12:37:15 PM
    Author     : <PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="s" uri="/struts-tags" %>
<!DOCTYPE html>
<html class="html-reset">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Configuración de reportes</title>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../scripts/framework/dijit/themes/tundra/tundra.css?${systemVersion}"/>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script src="../scripts/framework/bnext/administrator/reports/report-list.js?${systemVersion}" type="text/javascript"></script>
        <style>
            .bnext .dijitDialog.fullWidthScreen [data-dojo-attach-point="containerNode"].dijitDialogPaneContent {
                width: 100%!important;
                margin-bottom: 0.5rem;
                margin-right: 0.5rem;
                height: 100%!important;
                float: left;
                align-self: flex-start;
            }
            
            .ReportColumn .rightLinkedGridWithTitles .gridExpandable.grid-component-container {
                max-height: none!important;
                max-width: 100%;
                width: auto;
                float: none;
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" class="tundra">
        <%@include file="../../components/loader.jsp" %>
        <div class="grid-container grid-floating-active full-width-grid-component">
            <div class="grid-x">
                <div class="cell">
                    <form class="container-form grid-floating-action-buttons">
                        <div class="header grid-container">
                            <div class="grid-x content_title">
                                <div class="cell position-relative">
                                    <h3 class="igx-card-header__title window-title float-left ${module}" id="mainTitle"></h3>
                                    <div class="float-right">
                                        <a href="javascript: void(0);" id="addFavorite" class="material-icons" title="Agregar a favorito(s)">
                                            favorite
                                        </a>
                                    </div> 
                                </div>
                                <h5 class="cell igx-card-header__subtitle">
                                </h5>
                            </div>
                        </div>
                        <div class="floating-action-buttons hideOnPrint">
                            <s:if test="canUpdate">
                                <div class="button-component">
                                    <span class="material-icons">add</span>
                                    <input id="newRecord" title="Click to add"
                                           type="button" class="addToGrid Button raised-button fixed-button" 
                                           value="Nuevo" />
                                </div>
                            </s:if>
                        </div>
                        <div class ="grid-container table_workarea">
                            <table id="grid"></table>
                        </div>
                        <div class ="displayNone">
                            <%@include file="../../components/sessionVariables.jsp" %>
                            <input type="hidden" id="canDelete" value="${canDelete}"/>
                            <input type="hidden" id="canUpdate" value="${canUpdate}"/>
                            <input type="hidden" id="canAssignPermissions" value="${canAssignPermissions}"/>
                            <input type="hidden" id="module" value="${module}"/>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>