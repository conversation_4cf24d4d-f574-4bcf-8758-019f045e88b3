<%-- 
    Document   : meeting
    Created on : 21/09/2016
    Author     : <PERSON><PERSON>
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<% request.setCharacterEncoding("UTF-8");
   %>
<%@ taglib prefix="s" uri="/struts-tags" %>
<html class="${cssHtml}">
    <head>
        <title>Reuniones</title>
        <%@include file="../../components/requiredScripts.jsp" %>
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}" />
        <script src="../scripts/framework/bnext/administrator/meeting/preferences.js?${systemVersion}" type="text/javascript"></script>
    </head>
    <body writingsuggestions="false" textprediction="false">
        <%@include file="../../components/loader.jsp" %>
        <form method="POST" id="validate_form">
            <div class="content_title">
                <h1 id="mainTitle"></h1>
            </div> 
            <ul class="content_area" id="contenido">
                <li id="serializedEntity" style="display: none">
                    <s:property value="serializedEntity"/>
                </li>
                <li >
                    <s:select name="userScope" id="userScope" 
                              list="scope" label="scope"
                              listKey="value" listValue="text" >
                    </s:select>
                </li>
                <jsp:include page="../../components/saveButtons.jsp"/>   
            </ul>
            <input type="hidden" name="id" id="id" />
            <%@include file="../../components/sessionVariables.jsp" %>
        </form>
        <%@include file="../../components/footer.jsp" %>
    </body>
</html>