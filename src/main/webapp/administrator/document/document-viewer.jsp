<%-- 
    Document   : document-viewer
    Created on : Jun 19, 2018, 10:11:26 AM
    Author     : <PERSON>
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html class="html-reset" translate="no">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
        <title>Visor de documentos</title>
        <link rel="stylesheet" type="text/css" href="../scripts/framework/bnext/viewfinder/style/viewfinder.css?${systemVersion}">
        <link rel="stylesheet" type="text/css" href="../styles/bnext.core.css?${systemVersion}">
        <jsp:include page="../../components/requiredScripts.jsp" />
        <script>
            require([
                'bnext/viewfinder/viewfinder-util'
            ], function(viewfinderUtil) {
                viewfinderUtil.setAngularFullScreen('full');
            });
        </script>
        <script src="../scripts/framework/bnext/administrator/document/document-viewer.js?${systemVersion}" type="text/javascript"></script>
        <link rel="stylesheet" type="text/css" href="../styles/floating-action.css?${systemVersion}" />
        <style>
            .viewfinder {
                border: 0px;
            }
            .viewfinder .tabsContainer .dijitTabInner.dijitTabContent.dijitTab {
                color: ${systemSecondaryColor};
            }
            .viewfinder .tabsContainer .dijitTab.dijitTabHover, 
            .viewfinder .tabsContainer .dijitTab.dijitTabChecked.dijitTabCheckedHover {
                background-color: ${systemSecondaryColor} !important;
                color: ${systemSecondaryColorTextColor} !important;
            }
            .viewfinder .tabsContainer.dijitTabContainer .dijitTabChecked {
                border-bottom: 2px solid ${systemSecondaryColor} !important;
            }
            .viewfinder .relatedDocumentsNode .relatedDocumentTd.button:hover {
                border: 1px solid ${systemSecondaryColor};
            }
        </style>
    </head>
    <body writingsuggestions="false" textprediction="false" class="bnext-viewfinder screen-mode-normal">
    </body>
</html>
