<%@page import="org.slf4j.LoggerFactory"%>
<%@page import="org.slf4j.Logger"%>
<%@ page import="jakarta.servlet.http.*"%>
<%@ page import="jakarta.servlet.*"%>
<%@ page import="java.sql.*"%>
<%@ page import="java.io.*"%>
<%@ page import="java.util.*"%>
<%{%>
<jsp:useBean id="pie" scope="page" class="isoblock.common.bottomForm"/>
<jsp:setProperty name="pie" property="*" />
	<link rel="stylesheet" type="text/css" href="../style/s-general-theme.style?${systemVersion}" />
<%    
    String permisopie = "false";
    if (session.getAttribute("introladministracion") != null) {
        if (((String) session.getAttribute("introladministracion")).equals("1")) {
            permisopie = "true";
        }
    }
    pie.setVchFormaNombre(request.getRequestURI().substring(request.getRequestURI().lastIndexOf("/") + 1));
    if (!pie.getVchFormaNombre().equals("")) {
        pie.setAllValues();
    }
//TODO: Agregar codigo para que si aunque la forma tenga 'handle' en su nombre pero no tiene asignado un pie de pagina que vuelva a ocultarlo o que lo agreg en la bdd
%>
<input type='hidden' name='vchFormaNombre' id='vchFormaNombre' value='<%=pie.getVchFormaNombre()%>'>
<input type='hidden' name='vchPieIzq' id='vchPieIzq' value='<%=pie.getVchPieIzq()%>'>
<input type='hidden' name='vchPieDer' id='vchPieDer' value='<%=pie.getVchPieDer()%>'>
<input type='hidden' name='permisopie' id='permisopie' value='<%=permisopie%>'>
<input type='hidden' name='color_sistema' id='color_sistema' value='<%=pie.COLOR_SISTEMA%>'>
<input type='hidden' name='vchClaveFuenteParameter' id='vchClaveFuenteParameter' value='<%=pie.parameter("vchClave", request)%>'>
<%
    String cerraralsalir = "0";
    try {
        if (session.getAttribute("cerraralsalir") != null) {
            cerraralsalir = ((String) session.getAttribute("cerraralsalir"));
            pie.logger.trace("cerraralsalir: " + cerraralsalir);
        }
    } catch (Exception e) {
        pie.logger.error("Error",e);
    }
%>
<input type='hidden' name='cerraralsalir' id='cerraralsalir' value='<%=cerraralsalir%>'>

<% try {
        pie.finalize();
    } catch (Throwable t) {
        t.printStackTrace();
    }%>

<%}%>
<%!
    //Create first div Header Html Form
    public String header(String title) {
        return head(title); 
    }
    public String header(String title,Boolean standardBody) {
        return head(title,"body",standardBody); 

    }
    //Create second div Header Html Form

    public String header2(String title) {
        String code1 = "<TABLE ID=\"secondDiv\" cellspacing=0 cellpadding=0 style='display:none'> <tr><td style='height: 262px; padding-top: 3px;'>";
        String code2 = headerBasic(title, "2");
        return code1 + code2;
    }
    //Crea header para frames internos (sin titulo)

    public String header3() {
        String code1 = "<TABLE ID=\"thirdDiv\"> <tr><td>";
        String code2 = headerBasic();
        return code1 + code2;
    }

    //Create basic Header Html Form
    public String headerBasic(String title, String tipo) {
        title = Framework.Config.Utilities.standarText(title);
        String code = " ";
        code = code + "<table  cellpadding='0' cellspacing='0' border='0'>";
        code = code + "<TR>";
        code = code + "<TD>";
        code = code + "</TD>";
        code = code + "<TD></TD>";
        code = code + "<TD>";
        code = code + "</TD>";
        code = code + "</TR>";
        code = code + "<TR>";
        code = code + "<TD></TD>";
        code = code + "<TD align='left' >";
        code = code + "<table align='left'  border='0' cellspacing='0' cellpadding='0'>\n";
        code = code + "  <tr>\n";
        code = code + "    <td align='left' valign='top'> \n";
        code = code + "      <table border='0' cellspacing='0' cellpadding='0' bordercolor='black'>\n";
        code = code + "        <tr>\n";
        code = code + "          <td align='bottom' valign='left' style='height:2px;margin-bottom:20px' > \n";

        code = code + "    <div hasLayout='true' id='divTopGen" + tipo + "'  style='width:100%; border-top-left-radius: 0.5em;border-top-right-radius: 0.5em;' >\n";
        code = code + "        <table id='tdivTopGen" + tipo + "' width='100%'  border='0' class='title_gendiv' cellspacing='0' cellpadding='0'>\n";
        code = code + "            <tr> \n";
        code = code + "              <td align='left'   > <img src='../images/header/sin_back.gif' border='0'></td>\n";
        code = code + "              <td align='right'  > <img src='../images/header/sin_back.gif' border='0'></td>\n";
        code = code + "            </tr>\n";
        code = code + "        </table>\n";
        code = code + "    </div>\n";

        code = code + "    <div hasLayout='true' id='divTitleGen" + tipo + "'  align='left' style='background-color:transparent' >\n";
        code = code + "       <table align='left' style='"+(tipo.equals("2")?"margin-top:-43px":"top:-29px")+";position:absolute' border='0' cellspacing='0' cellpadding='0'>\n";
        code = code + "          <tr>\n";
        code = code + "             <td width='100%'><p class='title' id='header_titulo'>" + title + "</p></td>\n";
        code = code + "          </tr>\n";
        code = code + "        </table>\n";
        code = code + "    </div>\n";

        code = code + "            <div id='divTitleGen2" + tipo + "' hasLayout='true' style='z-index:4;background-color:transparent;height:2px;width:100%'>\n";
        code = code + "            <table id='tableTitleGen" + tipo + "'  style='top:-29px;position:absolute;width:105%'   class='title_back' border='0' cellspacing='0' cellpadding='0'>\n";
        code = code + "              <tr>\n";
        code = code + "                 <td align='left' class='title_gen' > <img src='../images/header/sin_back.gif' border='0'></td>\n";
        code = code + "                 <td align='right' class='title_gen' > <img src='../images/header/sin_back.gif' border='0'></td>\n";
        code = code + "              </tr>\n";
        code = code + "            </table>\n";
        code = code + "            </div>\n";



        code = code + "    <script type='text/javascript' >\n";
        code = code + "       document.getElementById('divTopGen" + tipo + "').style.backgroundColor=document.getElementById('color_sistema').value; \n";
        code = code + "       if(document.all) {\n";
        code = code + "          document.getElementById('divTitleGen" + tipo + "').className = 'tituloIE';\n";
        code = code + "          document.getElementById('divTitleGen2" + tipo + "').className = 'fondoIE';\n";
        code = code + "        }else {\n";
        code = code + "          document.getElementById('divTitleGen" + tipo + "').className = 'tituloNoIE';\n";
        code = code + "          document.getElementById('divTitleGen2" + tipo + "').className = 'fondoNoIE';\n";
        code = code + "        }\n";
        code = code + "    </script>\n";

        code = code + "          </td>\n";
        code = code + "        </tr>\n";
        code = code + "        <tr>\n";
        code = code + "          <td align='center' valign='middle'> \n";
        code = code + "            <table id='tablePrinc1' width='100%' border='0' cellspacing='0' cellpadding='0'>\n";
        code = code + "              <tr>\n";
        code = code + "                <td id='tablePrinc' align='left' valign='top' bgcolor='#F5F7F9'>\n";
        code = code + "                  <table cellspacing='0' cellpadding='16' bgcolor='#F5F7F9'>\n";
        code = code + "                     <tr>\n";
        code = code + "                       <td>  \n";
        return code;
    }

//genera codigo para header sin titulo
    public String headerBasic() {
        String code = " ";
        code = code + "<table cellpadding='0' cellspacing='0' border='0'>";
        code = code + "<TR>";
        code = code + "<TD>";
        code = code + "</TD>";
        code = code + "<TD></TD>";
        code = code + "<TD>";
        code = code + "</TD>";
        code = code + "</TR>";
        code = code + "<TR>";
        code = code + "<TD></TD>";
        code = code + "<TD align='center' width='100%'>";
        code = code + "<table width='100%' border='0' cellspacing='0' cellpadding='0'>\n";
        code = code + "  <tr bgcolor='#F5F7F9'>\n";
        code = code + "    <td align='left' valign='top'> \n";
        code = code + "      <table border='0' cellspacing='0' cellpadding='0' bordercolor='black'>\n";
        code = code + "        <tr>\n";
        code = code + "          <td align='center' valign='middle'> \n";
        code = code + "            <table width='100%' border='0' cellspacing='0' cellpadding='0'>\n";
        code = code + "              <tr>\n";
        code = code + "                <td align='left' valign='top' bgcolor='#F5F7F9'>\n";
        code = code + "                  <table cellspacing='0' cellpadding='16' bgcolor='#F5F7F9'>\n";
        code = code + "                     <tr>\n";
        code = code + "                       <td>  \n";
        return code;
    }

    //Create first div Footer Html Form
    public String footer() {
        return foo();
    }

    //Create second div Footer Html Form
    public String footer2() {
        String code1 = " </td></tr></TABLE>  \n";
        //code1 += "<script type='text/javascript' block('secondDiv'); </script>";
        String code2 = footerBasic();
        return code1 + code2;
    }

    //Create basic Footer Html Form
    public String footerBasic() {
        String code = "";
        //code=code + "<input type='hidden' name='cerraralsalir' id='cerraralsalir' value='0'> \n";
        code = code + "<input type='hidden' name='currentURL' id='currentURL' value=''> \n";
        code = code + "<input type='hidden' name='vchClaveFuente' id='vchClaveFuente' value=''> \n";
        code = code + "<script> \n";
        code = code + "    function setVchClaveFuente() { \n";
        //code=code + "         alert('intenta cerrar sesion');\n";
        code = code + "        var fpv = document.getElementById('vchClaveFuenteParameter').value; \n";
        code = code + "        var fjv = ''; \n";
        code = code + "        if(document.getElementById('vchClave')) { \n";
        code = code + "           fjv = document.getElementById('vchClave').value; \n";
        code = code + "        } \n";
        code = code + "        var v = fpv; \n";
        code = code + "        if(v == '') { \n";
        code = code + "           v = fjv\n";
        code = code + "        } \n";
        code = code + "        document.getElementById('vchClaveFuente').value = v; \n";
        code = code + "    } \n";
        code = code + "    setVchClaveFuente(); \n";
        code = code + "    function contiene(cadena,caracter) { \n";
        code = code + "        var contiene = false; \n";
        code = code + "        if(cadena.toString().toLowerCase().indexOf(caracter, 0) != '-1') { \n";
        code = code + "            contiene = true; \n";
        code = code + "        } \n";
        code = code + "        return contiene; \n";
        code = code + "    } \n";
        code = code + "    function setCurrentURL() { \n";
        code = code + "        try {var cURL = window.location.pathname; \n";
        code = code + "        var addAux = true; \n";
        code = code + "        if(!contiene(cURL,'?')) { \n";
        code = code + "           cURL = cURL + '?cURL=aux'; \n";
        code = code + "        } \n";
        code = code + "        var paramsCURL = ''; \n";
        code = code + "        if(document.forms && contiene(cURL,'handle')) { \n";
        code = code + "            for(j=0;j<document.forms.length;j++) { \n";
        code = code + "                if(document.forms[j]) { \n";
        //code=code + "                    document.forms[j].onsubmit = setCurrentURL(); \n";
        //code=code + "                    alert(document.forms[j].onsubmit); \n";
        code = code + "                    var lengthCURL = document.forms[j].length; \n";
        code = code + "                    for(i=0;i<lengthCURL;i++) { \n";
        code = code + "                        if(document.forms[j].item(i)) { \n";
        code = code + "                            if(document.forms[j].item(i).name && document.forms[j].item(i).value) { \n";
        code = code + "                               nameCURL = '&' + document.forms[j].item(i).name; \n";
        code = code + "                               valueCURL = document.forms[j].item(i).value; \n";
        code = code + "                               if(nameCURL.toString() != '' && valueCURL.toString() != '') { \n";
        code = code + "                                  if(contiene(nameCURL.toString(),'id') || contiene(nameCURL.toString(),'clave')) { \n";
        code = code + "                                       paramsCURL += nameCURL + '=' + escape(valueCURL); \n";
        //code=code + "                                       paramsCURL += nameCURL + '=' + valueCURL; \n";
        code = code + "                                  } \n";
        code = code + "                               } \n";
        code = code + "                            } \n";
        code = code + "                        } \n";
        code = code + "                    } \n";
        code = code + "                } \n";
        code = code + "            } \n";
        code = code + "        } \n";
        code = code + "        cURL = cURL + paramsCURL; \n";
        //code=code + "        alert(cURL) \n";
        code = code + "        document.getElementById('currentURL').value = cURL; } catch (e) {} \n";
        code = code + "    } \n";
        code = code + "    setCurrentURL(); \n";

        code = code + "    function closeSession() { \n";
        //code=code + "         alert('intenta cerrar sesion');\n";
        code = code + "         window.top.location='../login/login.jsp?onloadfunction=cerrarSesion'; \n";
        code = code + "    } \n";
        code = code + "    if(document.getElementById('cerraralsalir').value == '1') { \n";
        code = code + "        document.onmouseover = null;  \n";
        code = code + "        window.onunload = closeSession; \n";
        code = code + "    } \n";
//*/
        code += ""
                + "    try{"
                + "    if(document.getElementById('divMain')){"
                + "        if(document.getElementById('busyWindow')){"
                + "        if(document.getElementById('divMain').offsetHeight){"
                + "         document.getElementById('busyWindow').style.height=document.getElementById('divMain').offsetHeight; "
                + "         document.getElementById('busyWindow').style.width=document.getElementById('divMain').offsetWidth;"
                + "         }else{"
                + "          document.getElementById('busyWindow').style.height='100%';  "
                + "         } \n"
                + "       }else{"
                + "         document.getElementById('busyWindow').style.height='100%';  "
                + "         document.getElementById('busyWindow').style.width='100%'; \n"
                + "}"
                + "         }\n"
                + "         } catch (e) {}\n";
        code = code + "</script> \n\n";
        code = code + "                        </td>\n";
        code = code + "                      </tr>\n";
        code = code + "                      <tr>\n";
        code = code + "                       <td align=center>\n";
        code = code + "                           \n";
        code += "  <form name='ffvii' method='post' action=''></form> \n";
        code += "  <form name='formBottom' method='post' action=''> \n";
        code += "    <div id='piejsp' style='display:none'> \n";
        code += "        <table width='100%' cellspacing='0' id='pie_jsp' border='0' cellpadding='5' bgcolor='#F5F7F9'> \n";
        code += "            <tr>\n";
        code += "             <td id='accionesLigadasDiv'>\n";
        code += "                     \n";
        code += "             </td>\n";
        code += "            </tr>\n";
        code += "            <tr><td colspan=2><hr></td></tr>\n";
        code += "            <tr> \n";
        code += "                <td align='left' class='label' ondblclick=\"cambiarPie('izq')\"> \n";
        code += "                    <input type='text' class='text' size='25%' disabled name='VchPieIzq' id='VchPieIzq' value='Bnext QMS'> \n";
        code += "                </td> \n";
        code += "                <td align='right' class='label' ondblclick=\"cambiarPie('der')\"> \n";
        code += "                    <input type='text' class='text' size='25%' disabled style='text-align: right' name='VchPieDer' id='VchPieDer' value='BlockNetworks'> \n";
        code += "                </td> \n";
        code += "            </tr> \n";
        code += "        </table> \n";
        code += "    </div> \n";
        code += "  </form> \n";
        code = code + "                       </td>\n";
        code = code + "                      </tr>\n";

        code = code + "                   </table>\n";
        code = code + "                 </td>\n";
        code = code + "              </tr>\n";
        code = code + "            </table>\n";
        code = code + "          </td>\n";
        code = code + "        </tr>\n";
        code = code + "      </table>\n";
        code = code + "    </td>\n";
        code = code + "  </tr>\n";
        code = code + "</table>\n";
        code = code + "</TD>";
        code = code + "<TD></TD>";
        code = code + "</TR>";
        code = code + "<TR>";
        code = code + "<TD>";
        code = code + "</TD>";
        code = code + "<TD></TD>";
        code = code + "<TD>";
        code = code + "</TD>";
        code = code + "</TR>";
        code = code + "</TABLE> ";
        return code;
    }

    public String foo() {
        /*
        * Codigo pot Rgarza
        */
        String code = "";
        code += "<div id=\"footerComponent\">\n";
        code += "   <span class='rightFooter' ondblclick=\"cambiarPie('der')\"id='VchPieDer'></span>\n";
        code += "   <span class='leftFooter'  ondblclick=\"cambiarPie('izq')\" id='VchPieIzq'></span>\n";
        code += "</div>\n";
        code += "<script type='text/javascript'>\n";
        code += "var footer = document.getElementById('footerComponent');";
        code += "footer.style.marginTop = '5px';";
        code += "footer.style.borderTop = 'solid 2px ' + document.getElementById('color_sistema').value;";
        code += "    function cambiarPie(foot) { \n";
        code += "       var pie; \n";
        code += "       var sPath = window.location.pathname; \n";
        code += "       var sPage = new String(sPath.substring(sPath.lastIndexOf('/') + 1)); \n";
        code += "       var xPage = document.getElementById('vchFormaNombre').value || sPage; \n";
        code += "       if(document.getElementById('permisopie').value == 'true') { \n";
        code += "         if(foot == 'izq') { \n";
        code += "              var pie = window.open('v-footer.view?vchState=izq&vchFormaNombre='+xPage,'basefrm'); \n";
        code += "         } else { \n";
        code += "              var pie = window.open('v-footer.view?vchState=der&vchFormaNombre='+xPage,'basefrm'); \n";
        code += "         } \n";
        code += "       } \n";
        code += "       pie.focus(); \n";
        code += "    } \n";
        code += "   (function(){\n";
        code += "       var VchPieDer = document.getElementById('VchPieDer');\n";
        code += "       var vchPieDer = document.getElementById('vchPieDer');\n";
        code += "       var VchPieIzq = document.getElementById('VchPieIzq');\n";
        code += "       var vchPieIzq = document.getElementById('vchPieIzq');\n";
        code += "       VchPieDer.innerText = vchPieDer.value;\nVchPieIzq.innerText = vchPieIzq.value;\n";
        code += "   })()\n";
        code += "</script>\n";
        code += "</div>";//Cierra mainDiv
        //code += "</div>";//cierra divMain
        return code;
    }

    public String head(String title) {
        return head(title, "body");
    }

    public String head(String title, String bodyClass) {
        return head(title, bodyClass,true);
    }

    public String head(String title, String bodyClass, Boolean standardText) {
        if(standardText) title = Framework.Config.Utilities.standarText(title);
        String code = "";
        //code += "<div id='divMain'>\n";
        code += "   <div class='content_title'><h1 id='mainTitle' class='titleOnPrint'>"+title+"</h1></div>";
        code += "   <div id='mainDiv'>\n";
        
        code += "<script>";
        code += "   document.getElementsByTagName('body')[0].className = '"+bodyClass+"';";
        code += "</script>";
        return code;
    }
%>