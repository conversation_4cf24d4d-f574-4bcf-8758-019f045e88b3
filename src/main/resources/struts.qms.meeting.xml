<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
    <!-- JSP -->
    <package name="qms-meeting-views" extends="struts-default">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/> 
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
            </interceptor-stack>
        </interceptors>
        <default-interceptor-ref name="bnextStack" />
        <global-results>
            <result name="login">/administrator/login/cerrarsesion.jsp?struts=1</result>
            <result name="error">/errors/500.jsp</result>
            <result name="noaccess">/errors/noaccess.jsp</result>
            <result name="nosession">/errors/nosession.jsp</result>
            <result name="nosupport">/administrator/unlicensed/unlicensed.jsp</result>
            <result name="invalid">/errors/invalid.jsp</result>
            <result name="renew-data">/administrator/login/renew-data.jsp</result>
            <result name="inactive">/errors/inactive.jsp</result>
            <result name="reloaded" type="redirect">qms/${loggedUserLang}/welcome</result>
        </global-results>
        <action name="v-meeting-type" class="qms.meeting.core.MeetingTypeAction">
            <param name="entity">true</param>
            <param name="requiredAccess">configuration</param>
            <param name="auditableEntityImpl">qms.meeting.entity.MeetingType</param>
            <result name="success">/administrator/meeting/meeting-type.jsp</result>
        </action>
        <action name="v-meeting-type-list" class="qms.meeting.core.MeetingTypeListAction">
            <param name="serializedStatusList" >true</param> 
            <param name="requiredAccess">meeting</param>
            <param name="auditableEntityImpl">qms.meeting.entity.MeetingType</param>
            <result name="success">/administrator/meeting/meeting-type-list.jsp</result>
        </action>
        <action name="v-meeting" class="qms.meeting.core.MeetingAction">
            <param name="entity">true</param>
            <param name="requiredAccess">meeting</param>
            <param name="auditableEntityImpl">qms.meeting.entity.Meeting</param>
            <result name="success">/administrator/meeting/meeting.jsp</result>
        </action>
        <action name="v-meeting-list" class="Framework.Action.AuditableEntityAction">
            <param name="serializedStatusList" >true</param> 
            <param name="requiredAccess">meeting</param>
            <param name="auditableEntityImpl">qms.meeting.entity.Meeting</param>
            <result name="success">/administrator/meeting/meeting-list.jsp</result>
        </action>
        <action name="v-meeting-to-assist-list" class="Framework.Action.AuditableEntityAction">
            <param name="serializedStatusList" >true</param> 
            <param name="requiredAccess">meeting</param>
            <param name="auditableEntityImpl">qms.meeting.entity.Meeting</param>
            <result name="success">/administrator/meeting/meeting-to-assist-list.jsp</result>
        </action>
        <action name="v-recurring-meeting-list" class="Framework.Action.AuditableEntityAction">
            <param name="serializedStatusList" >true</param> 
            <param name="requiredAccess">meeting</param>
            <param name="auditableEntityImpl">qms.meeting.entity.Meeting</param>
            <result name="success">/administrator/meeting/recurring-meeting-list.jsp</result>
        </action>
        <action name="v-recurring-meeting-recycle-bin" class="Framework.Action.AuditableEntityAction">
            <param name="serializedStatusList" >true</param> 
            <param name="requiredAccess">meeting</param>
            <param name="auditableEntityImpl">qms.meeting.entity.Meeting</param>
            <result name="success">/administrator/meeting/recurring-meeting-recycle-bin.jsp</result>
        </action>
        <action name="v-my-meeting-activity" class="qms.meeting.core.MeetingActivityAction">
            <param name="serializedStatusList" >true</param> 
            <param name="requiredAccess">meeting</param>
            <param name="auditableEntityImpl">qms.activity.entity.Activity</param>
            <result name="success">/administrator/activity/my-activity.jsp</result>
        </action>
        <action name="v-meeting-activity-list" class="qms.meeting.core.MeetingActivityAction">
            <param name="serializedStatusList">true</param> 
            <param name="requiredAccess">activity</param>
            <param name="auditableEntityImpl">qms.activity.entity.Activity</param>
            <result name="success">/administrator/activity/activity-list.jsp</result>
        </action>
        <action name="v-meeting-preferences" class="qms.meeting.core.MeetingPreferencesAction">
            <result name="success">/administrator/meeting/preferences.jsp</result>
        </action>
        <action name="v-meeting-minute-print" class="qms.meeting.core.MeetingMinuteAction">
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="end">/administrator/surveys/surveycaptureend.jsp?logoff=true</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
        <action name="v-meeting-survey-fill" class="DPMS.Action.Action_RequestSurveyCapture">
            <param name="task">fill</param>
            <param name="surveyType">request</param>
            <param name="unrestrictedAccess">true</param>
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/FormNoAccess.jsp</result>
            <result name="end">/administrator/surveys/surveycaptureend.jsp?logoff=true</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
    </package>
    <!-- JSON/SMD -->
    <package name="qms-meeting-json" extends="json-bnext">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/>
                <interceptor-ref name="alias"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="i18n"/>
                <interceptor-ref name="prepare"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="scopedModelDriven"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="conversionError"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
                <interceptor-ref name="debugging"/>
            </interceptor-stack>
        </interceptors>
        <default-interceptor-ref name="bnextStack" />
        
        <action name="MeetingParticipants" class="qms.meeting.service.MeetingService" method="smd">
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        
        <action name="Settings-Meeting-Type" class="qms.meeting.core.MeetingTypeService" method="smd"> 
            <interceptor-ref name="bnextStack" />
            <param name="activeServices">intBAdmonSistema,catalogsAudits</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
        <action name="Meeting" class="qms.meeting.service.MeetingService" method="smd"> 
            <interceptor-ref name="bnextStack" />
            <param name="activeServices">intBAdmonSistema,catalogsAudits</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        
        <action name="Meeting.Activity" class="qms.meeting.core.MeetingActivityService" method="smd"> 
            <interceptor-ref name="bnextStack" />
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        
        <action name="Settings.MeetingType" class="qms.meeting.core.MeetingTypeService" method="smd"> 
            <interceptor-ref name="bnextStack" />
            <param name="className">qms.meeting.entity.MeetingType</param>
            <param name="daoClassName">qms.meeting.dao.MeetingTypeDAO</param>
            <param name="activeServices">intBAdmonSistema,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        
        <action name="Meeting-Participant" class="qms.meeting.service.MeetingService" method="smd"> 
            <interceptor-ref name="bnextStack" />
            <param name="activeServices">intBAdmonSistema,catalogsAudits</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Meeting-Preferences" class="qms.meeting.core.Preferences" method="smd">
            <interceptor-ref name="json">
              <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
              <param name="noChache">true</param>
              <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Meeting-Activity" class="qms.meeting.core.MeetingActivityService" method="smd"> 
            <interceptor-ref name="bnextStack" />
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    </package>
</struts>