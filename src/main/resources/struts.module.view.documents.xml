<?xml version="1.0" encoding="UTF-8" ?>
<!--
    Module     : Documentos
    Created on : 14/01/2013, 07:24:57 PM
    Author     : <PERSON>
-->
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
    <package name="documents-views" extends="struts-default">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/> 
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
            </interceptor-stack>
        </interceptors>
        <default-interceptor-ref name="bnextStack" />
        <global-results>
            <result name="no_multi_thread">/errors/no-multi-thread.jsp</result>
            <result name="login">/administrator/login/cerrarsesion.jsp?struts=1</result>
            <result name="error">/errors/500.jsp</result>
            <result name="noaccess">/errors/noaccess.jsp</result>
            <result name="nosession">/errors/nosession.jsp</result>
            <result name="nosupport">/administrator/unlicensed/unlicensed.jsp</result>
            <result name="invalid">/errors/invalid.jsp</result>
            <result name="renew-data">/administrator/login/renew-data.jsp</result>
            <result name="inactive">/errors/inactive.jsp</result>
            <result name="reloaded" type="redirect">qms/${loggedUserLang}/welcome</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
        </global-results>
        <!-- Formularios --> 
        <action name="v.request.survey.list" class="DPMS.Action.Action_SurveyList">
            <param name="requiredServices">
                hasAuthority('IS_ADMIN')
                OR hasAuthority('FORMULARIO_CONTROL')
            </param>
            <param name="js_controller">request.survey</param>
            <result name="success">/administrator/surveys/survey.list.jsp</result>
        </action> 
        <action name="c.request.survey.list" class="Framework.Action.DefaultAction">
            <param name="requiredAccess">document</param>
            <result name="success">/administrator/surveys/controller/request.survey.list.js</result>
        </action>
        <action name="v.request.survey" class="DPMS.Action.Action_RequestSurvey">
            <param name="setEvaluationType">false</param>
            <param name="surveyType">request</param>
            <param name="viewMode">new</param>
            <result name="success">/administrator/surveys/survey.jsp</result>
        </action>
        <action name="v.request.survey.mode" class="DPMS.Action.Action_RequestSurvey">
            <param name="setEvaluationType">false</param>
            <param name="surveyType">request</param>
            <result name="success">/administrator/surveys/survey.jsp</result>
        </action>
        <action name="v.request.survey.preview" class="DPMS.Action.Action_RequestSurveyCapture">
            <param name="task">preview</param>
            <param name="requestMode">PREVIEW</param>
            <param name="surveyType">request</param>
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="end">/administrator/surveys/surveycaptureend.jsp?logoff=true</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
        <action name="v-request-survey-read" class="DPMS.Action.Action_RequestSurveyRead">
            <param name="task">preview</param>
            <param name="requestMode">PREVIEW</param> 
            <param name="surveyType">request</param>
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="end">/administrator/surveys/surveycaptureend.jsp?logoff=true</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
        <action name="v.request.survey.fill" class="DPMS.Action.Action_RequestSurveyCapture">
            <interceptor-ref name="bnextStack" ></interceptor-ref>
            <param name="task">fill</param>
            <param name="surveyType">request</param>
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/FormNoAccess.jsp</result>
            <result name="end">/administrator/surveys/surveycaptureend.jsp?logoff=true</result>
            <result name="attended">/administrator/pending/pending-attended.jsp</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
        <action name="v.request.survey.continue-fill" class="DPMS.Action.Action_RequestSurveyCapture">
            <interceptor-ref name="bnextStack" ></interceptor-ref>
            <param name="task">fill</param>
            <param name="surveyType">request</param>
            <param name="continueFill">true</param>
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/FormNoAccess.jsp</result>
            <result name="end">/administrator/surveys/surveycaptureend.jsp?logoff=true</result>
            <result name="attended">/administrator/pending/pending-attended.jsp</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
        <action name="v.request.survey.requestor.fill" class="DPMS.Action.Action_RequestSurveyCapture">
            <interceptor-ref name="bnextStack" ></interceptor-ref>
            <param name="task">fill</param>
            <param name="attachmentButtonAvailable">true</param>
            <param name="progressButtonAvailable">false</param>
            <param name="exitButtonAvailable">false</param>
            <param name="printButtonAvailable">false</param>
            <param name="surveyType">request</param>
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/FormNoAccess.jsp</result>
            <result name="end">/administrator/surveys/surveycaptureend.jsp?logoff=true</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
        <action name="v.document.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/document.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.document.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/document.list.js</result>
        </action>
        <!-- View -->
        <action name="v.secuencia.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/secuencia.list.jsp</result>
        </action>
        <!-- View Documentos Listae -->
        <action name="v.documentos.listae" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/documentoslistae.jsp</result>
        </action>
        <!-- View Documentos -->
        <action name="v.documentos" class="DPMS.Action.Action_DocumentHandle">
            <result name="success">/administrator/documentos/documentoshandle.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
            <result name="PDF_GENERATOR_NOT_AVAILABLE">/administrator/documentos/pdf-generator-not-available.jsp</result>
        </action>
        <!-- View Documentos Secuencia-->
        <action name="v.secuencia" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/secuenciahandle.jsp</result>
        </action>
        <!-- View Documentos Documentos Secuencia-->
        <action name="v.documentos.secuencia" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/documentossecuencia.jsp</result>
        </action>
        <!-- View Documentos Reporte General-->
        <action name="v.documentos.reporte.general" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/documentosreportegeneral.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.secuencia.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/secuencia.list.js</result>
        </action>
        <!-- View -->
        <action name="v-document-type" class="qms.document.core.DocumentTypeAction">
            <param name="entity">true</param>
            <param name="requiredAccess">configuration</param>
            <param name="auditableEntityImpl">DPMS.Mapping.DocumentType</param>
            <result name="success">/administrator/document/document-type.jsp</result>
        </action>
        <action name="v-bin-distribution-list" class="Framework.Action.DefaultAction">
            <param name="requiredAccess">document</param>
            <result name="success">/administrator/document/bin-distribution-list.jsp</result>
        </action>
        <action name="v-document-viewer" class="Framework.Action.DefaultAction">
            <param name="requiredAccess">document</param>
            <result name="success">/administrator/document/document-viewer.jsp</result>
        </action>
        <action name="v-document-type-list" class="qms.document.core.DocumentTypeListAction">
            <param name="serializedStatusList" >true</param> 
            <param name="requiredAccess">configuration</param>
            <param name="auditableEntityImpl">DPMS.Mapping.DocumentType</param>
            <result name="success">/administrator/documentos/document.type.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.document.type.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/document.type.list.js</result>
        </action>
        <!-- View -->
        <action name="v.storage.place.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/storage.place.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.storage.place.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/storage.place.list.js</result>
        </action>
        <!-- View -->
        <action name="v.classification.information.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/classification.information.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.classification.information.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/classification.information.list.js</result>
        </action>
        <!-- View -->
        <action name="v.disposition.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/disposition.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.disposition.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/disposition.list.js</result>
        </action>
        <!-- View -->
        <action name="v.document.list.cp" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/document.list.cp.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.document.list.cp" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/document.list.cp.js</result>
        </action>
        <!-- View -->
        <action name="v.document.master.list" class="qms.document.core.DocumentSimpleAction">
            <result name="success">/administrator/documentos/document.master.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.document.master.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/document.master.list.js</result>
        </action>
        <!-- View -->
        <action name="v.document.master.records.list" class="qms.document.core.DocumentSimpleAction">
            <result name="success">/administrator/documentos/document.master.records.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.document.master.records.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/document.master.records.list.js</result>
        </action>
        <!-- View -->
        <action name="v.request.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/solicitudes/request.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.request.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/solicitudes/controller/request.list.js</result>
        </action>
        <!-- View -->
        <action name="v.document.list.ae" class="DPMS.Action.Action_DocumentListAE">
            <result name="success">/administrator/documentos/document.list.ae.jsp</result>
            <result name="noaccess">/administrator/document/document-node-noaccess.jsp</result>
        </action>
        <!-- View -->
        <action name="v.document.papelera" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/papelera.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.document.papelera" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/papelera.js</result>
        </action>
        <!-- View Imagenes -->
        <action name="v.document.imagenes" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/gallery.handle.jsp</result>
        </action>
        <action name="c.document.imagenes" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/gallery.handle.js</result>
        </action>
        <!-- View -->
        <action name="v.request" class="DPMS.Action.Action_Request">
            <result name="success">/administrator/solicitudes/request.jsp</result>
        </action>
        <!-- View -->
        <action name="v-to-renew-documents" class="DPMS.Action.Action_Request">
            <interceptor-ref name="bnextStack" />
            <param name="expiredDocuments">1</param>
            <result name="success">/administrator/documentos/to-renew-documents.jsp</result>
        </action>
        <action name="v-visor-pdf" class="DPMS.Document.CRUD_Download">
            <interceptor-ref name="bnextStack" />
            <param name="documentDetail">true</param>
            <param name="enableFileConfirmation">true</param>
            <result name="success">/administrator/documentos/PDF_Viewer.jsp</result>
            <result name="no_file">/administrator/document/pdf-conversion-loader.jsp?viewFinder=true</result>
            <result name="is_video">/administrator/document/video-player.jsp</result>
            <result name="file_confirmation">/administrator/document/file-download-confirmation.jsp</result>
            <result name="file_download_not_available">/administrator/document/file-download-not-available.jsp</result>
            <result name="is_form" type="redirectAction">view/v.request.survey.preview?id=${surveyId}&amp;documentId=${documentId}&amp;viewFinder=true</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
        </action>
        <action name="Download" class="DPMS.Document.CRUD_Download">
            <result name="success">/administrator/documentos/PDF_Viewer.jsp</result>
            <result name="no_file">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="file_confirmation">/administrator/document/file-download-confirmation.jsp</result>
            <result name="file_download_not_available">/administrator/document/file-download-not-available.jsp</result>
            <result name="is_video">/administrator/document/video-player.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
        </action>
        <action name="v-download" class="DPMS.Document.CRUD_Download">
            <result name="success">/administrator/documentos/PDF_Viewer.jsp</result>
            <result name="no_file">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="file_confirmation">/administrator/document/file-download-confirmation.jsp</result>
            <result name="file_download_not_available">/administrator/document/file-download-not-available.jsp</result>
            <result name="is_video">/administrator/document/video-player.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
        </action>
        <action name="v-download-attached" class="qms.framework.logic.DownloadAttachedAction">     
            <result name="success">/administrator/common/blank.jsp"</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="is_video">/administrator/common/blank.jsp</result>
        </action>
        <action name="v-session-download-video" class="qms.framework.logic.SessionDownloadVideoAction">     
            <result name="success">/administrator/common/blank.jsp"</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
        </action>
        <action name="v-nosession-download-video" class="qms.framework.logic.NoSessionDownloadVideoAction">     
            <result name="success">/administrator/common/blank.jsp"</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
        </action>
        <action name="DownloadMedia" class="qms.document.core.DownloadMediaAction">
            <result name="success">/administrator/common/blank.jsp"</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="is_video">/administrator/document/video-player.jsp</result>
        </action>
        <action name="PreviewMedia" class="qms.document.core.PreviewMediaAction">
            <result name="success">/administrator/common/blank.jsp"</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
        </action>
        <action name="web.service" class="DPMS.Document.CRUD_Download">
            <result name="success">/administrator/documentos/PDF_Viewer.jsp</result>
            <result name="no_file">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="file_confirmation">/administrator/document/file-download-confirmation.jsp</result>
            <result name="file_download_not_available">/administrator/document/file-download-not-available.jsp</result>
            <result name="is_video">/administrator/document/video-player.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
        </action>
        <action name="v-file-pdf-page" class="DPMS.Action.Action_FilePdfPage">
            <result name="success">/administrator/common/blank.jsp"</result>
        </action>
        <action name="v-pdf-page-preview" class="qms.document.logic.FilePdfPageThumbnailAction">
            <result name="success">/administrator/common/blank.jsp"</result>
        </action>
        <!-- View -->
        <action name="v.verification" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/solicitudes/verification.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.verification" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/solicitudes/controller/verification.js</result>
        </action>
        <action name="v.autorization" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/solicitudes/autorization.jsp</result>
        </action>
        <action name="v.fill.form" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/solicitudes/fill-form.jsp</result>
        </action>
        <action name="c.autorization" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/solicitudes/controller/autorization.js</result>
        </action>
        <action name="v.set.readers" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/set.readers.jsp</result>
        </action>
        <action name="c.set.readers" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/set.readers.js</result>
        </action>
        <action name="v.to.read.documents" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/to.read.documents.jsp</result>
        </action>
        <action name="c.to.read.documents" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/to.read.documents.js</result>
        </action>
        <!-- View -->
        <action name="v.related.document.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/related.document.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.related.document.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/related.document.list.js</result>
        </action>
        <!-- View -->
        <action name="v.folder.access" class="DPMS.Action.Action_DocumentListFolderAccess">
            <result name="success">/administrator/documentos/folder.access.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.folder.access" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/folder.access.js</result>
        </action>
        <!-- View Cuestionario Reporte General -->
        <action name="v.cuestionarios.reporte.general" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/cuestionarios/cuestionariosreportegeneral.jsp</result>
        </action>
        <!-- View Flujos de Secuencia-->
        <action name="v.sequence.detail.list" class="qms.document.core.SequenceDetailAction">
            <result name="success">/administrator/documentos/sequence.detail.list.jsp</result>
        </action>
        <action name="v.sequence.flow.preview" class="DPMS.Action.Action_SequenceDiagram">
            <result name="success">/administrator/documentos/sequence.flow.preview.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.sequence.detail.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/sequence.detail.list.js</result>
        </action>
        <!-- View -->
        <action name="v.readers.by.document" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/readers.by.document.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.readers.by.document" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/readers.by.document.js</result>
        </action>
        <!-- View -->
        <action name="v.documents.by.reader" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/documents.by.reader.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.documents.by.reader" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/documents.by.reader.js</result>
        </action>
        <!-- View -->
        <action name="v.document.distribution.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/document.distribution.list.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.document.distribution.list" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/document.distribution.list.js</result>
        </action>
        <!-- Controller -->
        <action name="c.pdf.viewer" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/pdf.viewer.js</result>
        </action>
        <!-- View -->
        <action name="v.print.viewer" class="DPMS.Document.CRUD_Download">
            <result name="success">/administrator/documentos/print.viewer.jsp</result>
            <result name="no_file">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
            <result name="file_confirmation">/administrator/document/file-download-confirmation.jsp</result>
            <result name="file_download_not_available">/administrator/document/file-download-not-available.jsp</result>
            <result name="is_video">/administrator/document/video-player.jsp</result>
            <result name="NO_VIEW_ALLOWED">/administrator/documentos/DocumentNoAccess.jsp</result>
        </action>
        <action name="v-print-viewer-config" class="qms.document.core.PrintViewerConfigAction">
            <result name="success">/administrator/document/print-viewer-config.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.print.viewer" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/print.viewer.js</result>
        </action>
        <!-- Style -->
        <action name="s.print.viewer" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/styles/print.viewer.css</result>
        </action> 
        <!-- View -->
        <action name="v.controlled.document.list.by.deliver" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controlled.document.list.by.deliver.jsp</result>
        </action>
        <!-- View -->
        <action name="v-to-pick-up-physical-copy" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/document/to-pick-up-physical-copy.jsp</result>
        </action>
        <!-- Controller -->
        <action name="c.controlled.document.list.by.deliver" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/documentos/controller/controlled.document.list.by.deliver.js</result>
        </action>
        <!--WebService-->
        <action name="web-service-document" class="qms.document.core.DocumentWebServiceAction">
            <result name="success"></result>
        </action>
        <action name="v-form-viewer-link" class="qms.document.core.FormViewerLinkAction">
            <result name="success">/administrator/surveys/surveycapture.jsp</result>
            <result name="busy">/administrator/surveys/surveycapturebusy.jsp</result>
        </action>
        <action name="v-document-viewer-link" class="qms.document.core.DocumentViewerLinkAction">
            <interceptor-ref name="bnextStack" />
            <result name="success">/administrator/documentos/PDF_Viewer.jsp</result>
            <result name="is_video">/administrator/document/video-player.jsp</result>
            <result name="is_form" type="redirectAction">view/v-form-viewer-link?token=${token}</result>
            <result name="error">/administrator/shared_document/shared_document_error.jsp</result>
        </action>
        <action name="v-default-report-logo" class="qms.framework.core.ReportLogoAction">
            <param name="nullable">true</param>
            <result name="success">/administrator/common/blank.jsp"</result>
        </action>  
        <action name="v-application-logo" class="qms.framework.core.ReportLogoAction">
            <param name="nullable">false</param>
            <result name="success">/administrator/common/blank.jsp"</result>
        </action>
        <action name="v-application-avatar" class="qms.framework.core.AvatarLogoAction">
            <param name="nullable">false</param>
            <result name="success">/administrator/common/blank.jsp"</result>
        </action>
        <action name="v-application-welcome-carousel" class="qms.framework.core.WelcomeCarouselAction">
            <param name="nullable">false</param>
            <result name="success">/administrator/common/blank.jsp"</result>
        </action>
        <action name="v-application-preview-carousel" class="qms.framework.core.PreviewCarouselAction">
            <param name="nullable">false</param>
            <result name="success">/administrator/common/blank.jsp"</result>
        </action>
        <action name="c.general.report.actions" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/accion/controller/general.report.actions.js</result>
        </action>           
        <action name="v-request-detail" class="DPMS.Action.Action_RequestDetail">
            <interceptor-ref name="bnextStack" />
            <param name="requiredAccess">document</param>            
            <param name="requiredServices">
                hasAnyAuthority(
                    'DOCUMENTO_EDITOR', 'DOCUMENTO_ENCARGADO', 'DOCUMENTO_LECTOR', 'UNRESTRICTED_DOCUMENT_ACCESS',
                    'SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT', 'SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT',
                    'SPECIAL_DOCUMENT_READER', 'SPECIAL_DOCUMENT_AUTHORIZER'
                )
            </param>
            <result name="success">/administrator/solicitudes/request-detail.jsp</result>
        </action>
        <action name="v-document-manager-delayed-to-authorize" class="Framework.Action.DefaultAction">
            <interceptor-ref name="bnextStack" />
            <param name="requiredAccess">document</param>            
            <param name="requiredServices">
                hasAnyAuthority(
                    'DOCUMENTO_EDITOR', 'DOCUMENTO_ENCARGADO', 'DOCUMENTO_LECTOR', 'UNRESTRICTED_DOCUMENT_ACCESS',
                    'SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT', 'SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT',
                    'SPECIAL_DOCUMENT_READER', 'SPECIAL_DOCUMENT_AUTHORIZER'
                )
            </param>
            <result name="success">/administrator/solicitudes/document-manager-delayed-to-authorize.jsp</result>
        </action>
    </package>
</struts>