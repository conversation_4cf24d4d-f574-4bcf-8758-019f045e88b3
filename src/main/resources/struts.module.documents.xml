<?xml version="1.0" encoding="UTF-8" ?>
<!--
    Module     : Documentos
    Created on : 14/01/2013, 10:56:57 AM
    Author     : <PERSON>
-->
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
    <package name="json-documents" extends="json-bnext">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/>
                <interceptor-ref name="alias"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="i18n"/>
                <interceptor-ref name="prepare"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="scopedModelDriven"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="conversionError"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
                <interceptor-ref name="debugging"/>
            </interceptor-stack>
        </interceptors>
        <!-- Formularios -->
        <action name="Request.Survey.Capture" class="DPMS.Survey.CRUD_SurveyCapture" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">isoblock.surveys.dao.hibernate.SurveySearch</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Form-Target-To-Fill" class="DPMS.Survey.CRUD_Survey" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="type">request</param>
            <param name="rowsContition">SURVEY_FORM_CONTROL</param>
            <param name="className">DPMS.Mapping.SurveyView</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.position,
                    result\.data\[\d+\]\.une,
                    result\.data\[\d+\]\.corp
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Request.Survey" class="DPMS.Survey.CRUD_Survey" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="type">request</param>
            <param name="rowsContition">SURVEY_FORM_CONTROL</param>
            <param name="className">DPMS.Mapping.SurveyView</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\[\d+\]\.text,
                    result\[\d+\]\.value,
                    result\.data\[\d+\]\.authorId,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.dteCreacion,
                    result\.data\[\d+\]\.estatus,
                    result\.data\[\d+\]\.restrictRecordsByDepartment,
                    result\.data\[\d+\]\.validateAccessFormDepartment,
                    result\.data\[\d+\]\.restrictRecordsField,
                    result\.data\[\d+\]\.restrictRecordsObjId,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.globalObjId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.intQuestions,
                    result\.data\[\d+\]\.isTemplateUseAvailable,
                    result\.data\[\d+\]\.outstandingSurveysList,
                    result\.data\[\d+\]\.request,
                    result\.data\[\d+\]\.requestId,
                    result\.data\[\d+\]\.rolNo,
                    result\.data\[\d+\]\.rolSi,
                    result\.data\[\d+\]\.type,
                    result\.data\[\d+\]\.vchTexto,
                    result\.data\[\d+\]\.plainText,
                    result\.data\[\d+\]\.masterId,
                    result\.data\[\d+\]\.request\.blockedBy,
                    result\.data\[\d+\]\.request\.blockedByName,
                    result\.data\[\d+\]\.request\.businessUnit,
                    result\.data\[\d+\]\.request\.code,
                    result\.data\[\d+\]\.request\.creationDate,
                    result\.data\[\d+\]\.request\.deleted,
                    result\.data\[\d+\]\.request\.description,
                    result\.data\[\d+\]\.request\.documentCode,
                    result\.data\[\d+\]\.request\.fileId,
                    result\.data\[\d+\]\.request\.id,
                    result\.data\[\d+\]\.request\.isBusy,
                    result\.data\[\d+\]\.request\.organizationalUnit,
                    result\.data\[\d+\]\.request\.reazon,
                    result\.data\[\d+\]\.request\.retentionText,
                    result\.data\[\d+\]\.request\.retentionTime,
                    result\.data\[\d+\]\.request\.status,
                    result\.data\[\d+\]\.request\.storagePlaceId,
                    result\.data\[\d+\]\.request\.timeToFreeMinutes,
                    result\.data\[\d+\]\.request\.type,
                    result\.data\[\d+\]\.request\.version,
                    result\.data\[\d+\]\.request\.businessUnit\.code,
                    result\.data\[\d+\]\.request\.businessUnit\.deleted,
                    result\.data\[\d+\]\.request\.businessUnit\.description,
                    result\.data\[\d+\]\.request\.businessUnit\.documentManagerId,
                    result\.data\[\d+\]\.request\.businessUnit\.fileId,
                    result\.data\[\d+\]\.request\.businessUnit\.id,
                    result\.data\[\d+\]\.request\.businessUnit\.organizationalUnitId,
                    result\.data\[\d+\]\.request\.businessUnit\.status,
                    result\.data\[\d+\]\.request\.organizationalUnit\.code,
                    result\.data\[\d+\]\.request\.organizationalUnit\.deleted,
                    result\.data\[\d+\]\.request\.organizationalUnit\.description,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManagerId,
                    result\.data\[\d+\]\.request\.organizationalUnit\.id,
                    result\.data\[\d+\]\.request\.organizationalUnit\.predecessorId,
                    result\.data\[\d+\]\.request\.organizationalUnit\.status,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.code,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.contrasena,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.cuenta,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.description,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.detailGridSize,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.floatingGridSize,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.gridSize,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.id,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.lang,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.locale,
                    result\.data\[\d+\]\.request\.organizationalUnit\.documentManager\.root,
                    result\.data\[\d+\]\.request\.document\.version,
                    result\.data\[\d+\]\.request\.document\.code,
                    result\.data\[\d+\]\.requestStatus,
                    result\.data\[\d+\]\.requestDocumentVersion,
                    result\.data\[\d+\]\.requestVersion
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Request.Fill.Survey" class="DPMS.Survey.CRUD_Survey" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="type">request</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Request.My.Survey" class="DPMS.Survey.CRUD_Survey" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="type">request</param>
            <param name="rowsContition">SURVEY_MY_FORMS</param>
            <param name="className">DPMS.Mapping.SurveyView</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Request.Survey.Status" class="isoblock.surveys.struts2.action.Surveys_CRUD_Action" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">isoblock.surveys.dao.hibernate.Survey</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- USER -->
        <action name="Document.User" class="DPMS.Acceso.CRUD_User" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.User</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_User</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>

            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="excludeProperties">
                    result.*\.puestos,

                    result.*\.defaultWorkflowPosition.status,
                    result.*\.defaultWorkflowPosition.corp.*\.businessUnits,
                    result.*\.defaultWorkflowPosition.corp.*\.documentManager,
                    result.*\.defaultWorkflowPosition.corp.*\.predecessor,
                    result.*\.defaultWorkflowPosition.corp.*\.predecessorId,
                    result.*\.defaultWorkflowPosition.departamentos,
                    result.*\.defaultWorkflowPosition.departmentProcess,
                    result.*\.defaultWorkflowPosition.jefe,
                    result.*\.defaultWorkflowPosition.perfil,
                    result.*\.defaultWorkflowPosition.procesos,
                    result.*\.defaultWorkflowPosition.une.*\.attendant,
                    result.*\.defaultWorkflowPosition.une.*\.attendantId,
                    result.*\.defaultWorkflowPosition.une.*\.documentManager,
                    result.*\.defaultWorkflowPosition.une.*\.documentManagerId,
                    result.*\.defaultWorkflowPosition.une.*\.organizationalUnit,
                    result.*\.defaultWorkflowPosition.une.*\.organizationalUnitId,
                    result.*\.defaultWorkflowPosition.une.*\.status,

                    result.*\.correo,
                    result.*\.deleted,
                    result.*\.cuenta,
                    result.*\.lang,
                    result.*\.locale,
                    result.*\.saveHandling,
                    result.*\.tableInfo,
                    result.*\.contrasena
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT -->
        <action name="Document" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DISTRIBUTION LIST -->
        <action name="Document" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    result.*\.data.*\.DocumentReader.*\readed
                    result.*\.data.*\.DocumentReader.*\document
                    result.*\.data.*\.DocumentReader.*\user
                    result.*\.data.*\.DocumentReader.*\userResponsible
                    result.*\.data.*\.PrintedDocumentDistribution.*\documentDescription
                    result.*\.data.*\.PrintedDocumentDistribution.*\userDescription
                    result.*\.data.*\.PrintedDocumentDistribution.*\assignedBy
                    result.*\.data.*\.PrintedDocumentDistribution.*\readed
                    result.*\.data.*\.Document.*\description
                    result.*\.data.*\.UserRef.*\description
                    result.*\.data.*\.UserRef.*\description
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- RELATED DOCUMENTS LIST -->
        <action name="Document.RelatedDocumentsRows" class="DPMS.Document.CRUD_Document" method="smd">
          <interceptor-ref name="bnextStack" />
          <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
          <interceptor-ref name="json">
            <param name="enableSMD">true</param>
          </interceptor-ref>
          <result type="json">
            <param name="noChache">true</param>
            <param name="enableSMD">true</param>
          </result>
          <result name="inicio">/index.jsp</result>
        </action>
        <!-- GENERAR COPIAS NO CONTROLADAS A DOCUMENTOS -->
        <action name="Document.Uncontrolled.Copies" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBPrintUncontrolledCopies,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT_ASSIGNED BY -->
        <action name="Document.AssignedBy" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT (LECTORES FISICOS)-->
        <action name="PhysicalReader" class="DPMS.Document.CRUD_PhysicalReader" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.PhysicalReader</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT (LECTORES ELECTRONICOS)-->
        <action name="ReadersByDocument" class="DPMS.Document.CRUD_ReadersByDocument" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">qms.document.entity.DocumentReader</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT (ACUSE DE RECIBO)-->
        <action name="ReceiptAcknowledgment" class="DPMS.Document.CRUD_ReceiptAcknowledgment" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.ReceiptAcknowledgment</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT (HISTORIAL DE IMPRESIONES - DOCUMENTOS CONTROLADOS)-->
        <action name="DocumentPrinting" class="DPMS.Document.CRUD_DocumentPrinting" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentPrinting</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="excludeProperties">
                    result.*\.data.*\log
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT -->
        <action name="DocumentHistory" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentHistory</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- DOCUMENT -->
        <action name="Document.Request" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="excludeProperties">
                    result.*\.data.*\.author.*\.code,
                    result.*\.data.*\.author.*\.status,
                    result.*\.data.*\.saveHandling,
                    result.*\.data.*\.deleted,
                    result.*\.data.*\.department.*\.documentManagerId,
                    result.*\.data.*\.department.*\.attendantId,
                    result.*\.data.*\.department.*\.code,
                    result.*\.data.*\.department.*\.status,
                    result.*\.data.*\.department.*\.complaints,
                    result.*\.data.*\.documentType.*\.code,
                    result.*\.data.*\.documentType.*\.isRetainable,
                    result.*\.data.*\.documentType.*\.moduleId,
                    result.*\.data.*\.documentType.*\.status,
                    result.*\.data.*\.flujo,
                    result.*\.data.*\.originador.*\.code,
                    result.*\.data.*\.originador.*\.status,
                    result.*\.data.*\.request,
                    result.*\.data.*\.cancelDate,
                    result.*\.data.*\.isBackup,
                    result.*\.data.*\.rejectCode,
                    result.*\.data.*\.isBackup
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Request" class="DPMS.Document.CRUD_Request" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Request</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Request</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="RequestList" class="DPMS.Document.CRUD_Request" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Request</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Request</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Gallery" class="DPMS.Document.CRUD_Gallery" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Gallery</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Gallery</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- Business Unit Dep -->
        <action name="Documents.BusinessUnitDepartment" class="DPMS.Catalog.CRUD_BusinessUnitDepartment" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.BusinessUnitDepartment</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_BusinessUnitDepartment</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- Guardado / Carga de puestos -->
        <action name="Documents.Position" class="DPMS.Document.CRUD_PhysicalReader" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.PositionLoad</param>
            <param name="activeServices" >intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- Node Simple -->
        <action name="Node" class="DPMS.Document.CRUD_Node" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.NodeSimple</param>
            <param name="activeServices" >intBEditorDocumento,intBEncargadoDocumento,formAdminReportAcess,tsAdminReportAcess</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- Business Unit Dep -->
        <action name="Folder.BusinessUnitDepartment" class="DPMS.Catalog.CRUD_BusinessUnitDepartment" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.BusinessUnitDepartment</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_BusinessUnitDepartment</param>
            <param name="activeServices" >intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>

        <action name="Folder.User" class="DPMS.Acceso.CRUD_User" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.User</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_User</param>
            <param name="activeServices" >intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="excludeProperties">
                    result.*\.data.*\.defaultWorkflowPosition,
                    result.*\.data.*\.puestos.*\.departamentos,
                    result.*\.data.*\.puestos.*\.departmentProcess,
                    result.*\.data.*\.puestos.*\.jefe,
                    result.*\.data.*\.puestos.*\.perfil,
                    result.*\.data.*\.puestos.*\.procesos,
                    result.*\.data.*\.puestos.*\.saveHandling,
                    result.*\.data.*\.puestos.*\.tableInfo,
                    result.*\.data.*\.puestos.*\.deleted,
                    result.*\.data.*\.puestos.*\.code,
                    result.*\.data.*\.puestos.*\.corp,
                    result.*\.data.*\.puestos.*\.une,
                    result.*\.data.*\.contrasena
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Folder.Process" class="DPMS.Catalog.CRUD_DepartmentProcess" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DepartmentProcess</param>
            <param name="activeServices" >intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <!-- Business Unit -->
        <action name="Folder.BusinessUnit" class="DPMS.Catalog.CRUD_BusinessUnit" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.BusinessUnit</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_BusinessUnit</param>
            <param name="activeServices" >intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="Files" class="DPMS.Document.CRUD_File" method="smd">
            <interceptor-ref name="bnextStack"/>
            <param name="className">DPMS.Mapping.FilesLite</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
        </action>
        <action name="SendShareDocument" class="qms.document.core.ShareDocumentService" method="smd">
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="error">/administrator/document/pdf-conversion-loader.jsp</result>
        </action>
        <action name="DocumentSimple" class="qms.document.core.DocumentSimpleService" method="smd">
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    </package>
</struts>