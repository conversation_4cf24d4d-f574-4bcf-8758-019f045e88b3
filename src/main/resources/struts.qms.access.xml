<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
    <!-- JSP -->
    <package name="qms-access-views" extends="struts-default">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/> 
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
            </interceptor-stack>
        </interceptors>
        <default-interceptor-ref name="bnextStack" />
        <global-results>
            <result name="login">/administrator/login/cerrarsesion.jsp?struts=1</result>
            <result name="error">/errors/500.jsp</result>
            <result name="noaccess">/errors/noaccess.jsp</result>
            <result name="nosession">/errors/nosession.jsp</result>
            <result name="nosupport">/administrator/unlicensed/unlicensed.jsp</result>
            <result name="invalid">/errors/invalid.jsp</result>
            <result name="renew-data">/administrator/login/renew-data.jsp</result>
            <result name="inactive">/errors/inactive.jsp</result>
            <result name="reloaded" type="redirect">qms/${loggedUserLang}/welcome</result>
        </global-results>
        <!-- Edit Pass --> 
        <action name="v-edit-user-password" class="bnext.login.logic.EditPasswordAction">
            <result name="success">/administrator/configuracion/editpassword.jsp</result>
        </action>
        <!-- Edit Pass -->
        <action name="v-qr-generator" class="bnext.login.logic.EditPasswordAction">
            <result name="success">/administrator/configuracion/qr-generator.jsp</result>
        </action>
        <action name="licensing-report" class="qms.access.logic.LicensingReportAction"> 
            <result name="empty">/errors/500.jsp</result>
        </action>
    </package>
    <!-- JSON/SMD -->
    <package name="qms-access-json" extends="json-default">
        
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="defaultStack" >
                    <param name="params.ordered">true</param>
                </interceptor-ref>
            </interceptor-stack>
        </interceptors>
        <default-interceptor-ref name="bnextStack" />
        
        <action name="EditPassword" class="qms.framework.service.EditPasswordService" method="smd">
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    </package>
</struts>