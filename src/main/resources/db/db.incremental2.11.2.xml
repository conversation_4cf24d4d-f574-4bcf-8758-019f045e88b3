<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.3.xsd">
    <property name="now" value="sysdate" dbms="oracle"/>
    <property name="now" value="current_timestamp" dbms="mssql"/>
    <property name="now" value="now()" dbms="mysql"/>
    <property name="now" value="now()" dbms="postgresql"/>
    <changeSet author="<PERSON> Quintanilla Flores" id="Fig-2.11.2.0-20161104-EQUINTANILLA-1">
        <tagDatabase tag="2.11.2.0"/>
    </changeSet>
    <changeSet author="<PERSON>" 
               id="Fig-2.11.2.0-20161107-EQUINTANILLA-1">
        <comment>
            Se agrega tabla de profile_license
        </comment>
        <createTable tableName="profile_license">
            <column name="license_code" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="license_service" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>   
        <addPrimaryKey 
            tableName="profile_license"
            columnNames="license_code, license_service"
            constraintName="pk_profile_license"/>
        <addForeignKeyConstraint 
            baseTableName="profile_license" 
            baseColumnNames="created_by" 
            constraintName="fk_profile_license_created_by" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <addForeignKeyConstraint 
            baseTableName="profile_license" 
            baseColumnNames="last_modified_by" 
            constraintName="fk_profile_license_last_modified_by" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <insert tableName="profile_license">
            <column name="license_code" value="ALL-SERVICES" />
            <column name="license_service" value="ALL"  />
            <column name="last_modified_date" valueDate="${now}" />
            <column name="created_date" valueDate="${now}" />
            <column name="created_by" valueNumeric="1" />
            <column name="last_modified_by" valueNumeric="1" />
        </insert>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.2.0-20161107-EQUINTANILLA-2">
        <comment>
            Se agrega columna license_code a perfil
        </comment>
        <addColumn tableName="perfil">
            <column name="license_code" type="varchar(50)"/>
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.2.0-20161107-EQUINTANILLA-3">
        <comment>
            Se agrega not null constraint a columna license_code de tabla perfil
        </comment>
        <update tableName="perfil">
            <column name="license_code" value="ALL-SERVICES"/>
            <where>license_code is null</where>
        </update>
        <addNotNullConstraint tableName="perfil" columnName="license_code" columnDataType="varchar(50)"/>  
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.2.0-20161107-EQUINTANILLA-4">
        <comment>
            Se agrega columna license_code a puesto
        </comment>
        <addColumn tableName="puesto">
            <column name="license_code" type="varchar(50)"/>
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.2.0-20161107-EQUINTANILLA-5">
        <comment>
            Se agrega not null constraint a columna license_code de tabla puesto
        </comment>
        <update tableName="puesto">
            <column name="license_code" value="ALL-SERVICES"/>
            <where>license_code is null</where>
        </update>
        <addNotNullConstraint tableName="puesto" columnName="license_code" columnDataType="varchar(50)"/>  
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.2.0-20161108-EQUINTANILLA-1">
        <comment>
            Se agrega columna license_code a users
        </comment>
        <addColumn tableName="users">
            <column name="license_code" type="varchar(50)"/>
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.2.0-20161108-EQUINTANILLA-2">
        <comment>
            Se agrega not null constraint a columna license_code de tabla users
        </comment>
        <update tableName="users">
            <column name="license_code" value="ALL-SERVICES"/>
            <where>license_code is null</where>
        </update>
        <addNotNullConstraint tableName="users" columnName="license_code" columnDataType="varchar(50)"/>  
    </changeSet>
</databaseChangeLog>