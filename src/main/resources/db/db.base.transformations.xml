<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog  xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.3.xsd">
    <changeSet id="Clementine-*******-********-rgarza-1" author="Ricardo G<PERSON>za Verastegui">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="SURVEY_GLOBAL_OBJECT_BUSINESS_UNIT" />
        </preConditions>
        <comment>
            cambio de nombre para tabla por migracion a oracle
        </comment>
        <renameTable oldTableName="SURVEY_GLOBAL_OBJECT_BUSINESS_UNIT" newTableName="SURVEY_GLOBAL_OBJECT_BU" />
    </changeSet>
    <changeSet id="Clementine-*******-********-rgarza-2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="perfil" columnName="int_b_lector_accion" />
        </preConditions>
        <comment>
            cambio de nombre de columnas para oracle en perfiles
        </comment>
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_accion" newColumnName="p_lector_accion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_accion" newColumnName="p_editor_accion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_accion" newColumnName="p_encargado_accion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_proyecto" newColumnName="p_lector_proyecto" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_proyecto" newColumnName="p_editor_proyecto" />
        <renameColumn tableName="perfil" oldColumnName="int_b_contabilidad_proyecto" newColumnName="p_contabilidad_proyecto" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_proyecto" newColumnName="p_encargado_proyecto" />
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_validacion" newColumnName="p_lector_validacion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_validacion" newColumnName="p_editor_validacion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_validacion" newColumnName="p_encargado_validacion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_reunion" newColumnName="p_lector_reunion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_reunion" newColumnName="p_editor_reunion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_reunion" newColumnName="p_encargado_reunion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_documento" newColumnName="p_lector_documento" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_documento" newColumnName="p_editor_documento" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_documento" newColumnName="p_encargado_documento" />
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_indicador" newColumnName="p_lector_indicador" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_indicador" newColumnName="p_editor_indicador" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_indicador" newColumnName="p_encargado_indicador" />
        <renameColumn tableName="perfil" oldColumnName="int_b_admon_catalogos" newColumnName="p_admon_catalogos" />
        <renameColumn tableName="perfil" oldColumnName="int_b_admon_accesos" newColumnName="p_admon_accesos" />
        <renameColumn tableName="perfil" oldColumnName="int_b_admon_sistema" newColumnName="p_admon_sistema" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_encuesta" newColumnName="p_editor_encuesta" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_encuesta" newColumnName="p_encargado_encuesta" />
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_encuesta" newColumnName="p_lector_encuesta" />
        <renameColumn tableName="perfil" oldColumnName="int_b_editor_queja" newColumnName="p_editor_queja" />
        <renameColumn tableName="perfil" oldColumnName="int_b_encargado_queja" newColumnName="p_encargado_queja" />
        <renameColumn tableName="perfil" oldColumnName="int_b_lector_queja" newColumnName="p_lector_queja" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_auditoria" newColumnName="p_reporte_auditoria" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_reunion" newColumnName="p_reporte_reunion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_accion" newColumnName="p_reporte_accion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_documento" newColumnName="p_reporte_documento" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_encuesta" newColumnName="p_reporte_encuesta" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_queja" newColumnName="p_reporte_queja" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_indicador" newColumnName="p_reporte_indicador" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_validacion" newColumnName="p_reporte_validacion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_configuracion" newColumnName="p_reporte_configuracion" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_cuestionario" newColumnName="p_reporte_cuestionario" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_proyecto" newColumnName="p_reporte_proyecto" />
        <renameColumn tableName="perfil" oldColumnName="int_b_reporte_gerencial" newColumnName="p_reporte_gerencial" />
        <renameColumn tableName="perfil" oldColumnName="int_b_usuario_planta" newColumnName="p_usuario_planta" />
        <renameColumn tableName="perfil" oldColumnName="int_b_usuario_corporativo" newColumnName="p_usuario_corporativo" />
        <renameColumn tableName="perfil" oldColumnName="int_b_ENC_LLENADO" newColumnName="p_ENC_LLENADO" />
        <renameColumn tableName="perfil" oldColumnName="int_b_ENC_SUPERVISOR" newColumnName="p_ENC_SUPERVISOR" />
        <renameColumn tableName="perfil" oldColumnName="int_b_ENC_ENCARGADO" newColumnName="p_ENC_ENCARGADO" />
        <renameColumn tableName="perfil" oldColumnName="int_b_ENC_IN_PROCESS_BI" newColumnName="p_ENC_IN_PROCESS_BI" />
        <renameColumn tableName="perfil" oldColumnName="int_b_audit_helper" newColumnName="p_audit_helper" />
        <renameColumn tableName="perfil" oldColumnName="int_b_audit_lider" newColumnName="p_audit_lider" />
        <renameColumn tableName="perfil" oldColumnName="int_b_audit_quality" newColumnName="p_audit_quality" />
        <renameColumn tableName="perfil" oldColumnName="int_b_audit_survey" newColumnName="p_audit_survey" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_creator" newColumnName="p_dev_creator" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_viewer" newColumnName="p_dev_viewer" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_dispose_viewer" newColumnName="p_dev_dispose_viewer" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_service_scheduler" newColumnName="p_dev_service_scheduler" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_scheduled_services_viewer" newColumnName="p_dev_scheduled_services_view" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_service_history_viewer" newColumnName="p_dev_service_history_view" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_realize_service" newColumnName="p_dev_realize_service" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_service_metric" newColumnName="p_dev_service_metric" />
        <renameColumn tableName="perfil" oldColumnName="int_b_device_manager" newColumnName="p_dev_manager" />
        <renameColumn tableName="perfil" oldColumnName="int_b_survey_respondent" newColumnName="p_survey_respondent" />
        <renameColumn tableName="perfil" oldColumnName="int_b_survey_manager" newColumnName="p_survey_manager" />
        <renameColumn tableName="perfil" oldColumnName="int_b_survey_supervisor" newColumnName="p_survey_supervisor" />
    </changeSet>
    <changeSet author="blocknetworks (generated)" id="1412978825636-914">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <createView replaceIfExists="true" viewName="tblusuario">SELECT u.user_id AS intusuarioid
	,u.code AS vchclave
	,CASE u.first_name
		WHEN NULL
			THEN ' '
		ELSE u.first_name
		END + ' ' + CASE u.primary_last_name
		WHEN NULL
			THEN ' '
		ELSE u.primary_last_name
		END + ' ' + CASE u.secondary_last_name
		WHEN NULL
			THEN ' '
		ELSE u.secondary_last_name
		END AS vchnombrecompleto
	,CASE ISNULL(dbo.getBusinessUnits(u.user_id), '')
		WHEN ''
			THEN CASE ISNULL(dbo.getOrganizationalUnits(u.user_id), '')
					WHEN ''
						THEN ''
					ELSE ' @ ' + dbo.getOrganizationalUnits(u.user_id)
					END
		ELSE ' @ ' + dbo.getBusinessUnits(u.user_id)
		END AS scope
	,u.account AS vchnombre
	,u.password AS txtcontrasena
	,u.mail AS vchcorreo
	,u.description AS txtdescripcion
	,u.STATUS AS intstatususuario
	,u.root AS admin_general
	,'1,2,3,4,5,6,7,8,9,0' AS vchpermisosdocumentos
	,'1,2,3,4' AS vchpermisosquejas
	,0 AS introlrepositorios
	,CASE MAX(pr.p_admon_sistema * 1)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_admon_sistema * 1)
		END AS introladministracion
	,0 AS introlauditorias
	,CASE MAX(pr.p_lector_accion * 1 + p_editor_accion * 2 + p_encargado_accion * 3)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_lector_accion * 1 + p_editor_accion * 2 + p_encargado_accion * 3)
		END AS introlacciones
	,CASE MAX(pr.p_lector_reunion * 1 + p_editor_reunion * 2 + p_encargado_reunion * 3)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_lector_reunion * 1 + p_editor_reunion * 2 + p_encargado_reunion * 3)
		END AS introlreuniones
	,CASE MAX(pr.p_lector_documento * 1 + p_editor_documento * 2 + p_encargado_documento * 3)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_lector_documento * 1 + p_editor_documento * 2 + p_encargado_documento * 3)
		END AS introldocumentos
	,CASE MAX(pr.p_lector_indicador * 1 + p_editor_indicador * 2 + p_encargado_indicador * 3)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_lector_indicador * 1 + p_editor_indicador * 2 + p_encargado_indicador * 3)
		END AS introlindicadores
	,CASE MAX(pr.p_lector_queja * 2 + p_editor_queja * 3 + p_encargado_queja * 4)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_lector_queja * 2 + p_editor_queja * 3 + p_encargado_queja * 4)
		END AS introlquejas
	,CASE MAX(pr.p_lector_proyecto * 1 + p_contabilidad_proyecto * 2 + p_editor_proyecto * 3 + p_encargado_proyecto * 4)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_lector_proyecto * 1 + p_contabilidad_proyecto * 2 + p_editor_proyecto * 3 + p_encargado_proyecto * 4)
		END AS introlproyectos
	,CASE MAX(pr.p_reporte_configuracion)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_configuracion)
		END AS intpermisoconfiguracion
	,CASE MAX(pr.p_reporte_auditoria)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_auditoria)
		END AS intpermisoauditorias
	,CASE MAX(pr.p_reporte_cuestionario)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_cuestionario)
		END AS intpermisocuestionarios
	,CASE MAX(pr.p_reporte_reunion)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_reunion)
		END AS intpermisoreuniones
	,CASE MAX(pr.p_reporte_accion)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_accion)
		END AS intpermisoacciones
	,CASE MAX(pr.p_reporte_documento)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_documento)
		END AS intpermisodocumentos
	,CASE MAX(pr.p_reporte_proyecto)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_proyecto)
		END AS intpermisoproyectos
	,CASE MAX(pr.p_reporte_gerencial)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_gerencial)
		END AS intpermisogerencial
	,CASE MAX(pr.p_reporte_indicador)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_indicador)
		END AS intpermisoindicadores
	,CASE MAX(pr.p_reporte_encuesta)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_encuesta)
		END AS intpermisoencuestas
	,CASE MAX(pr.p_reporte_queja)
		WHEN NULL
			THEN 0
		ELSE MAX(pr.p_reporte_queja)
		END AS intpermisoquejas
	,u.lang + '-' + u.locale AS LANGUAGE
	,u.lang
	,u.locale
	,u.grid_size as gridSize
	,u.detail_grid_size as detailGridSize
	,u.floating_grid_size as floatingGridSize
FROM users u
LEFT JOIN usuario_puesto up ON u.user_id = up.usuario_id
LEFT JOIN puesto p ON up.puesto_id = p.puesto_id
LEFT JOIN perfil pr ON p.perfil_id = pr.perfil_id
LEFT JOIN business_unit bun ON p.business_unit_id = bun.business_unit_id
LEFT JOIN organizational_unit oru ON p.organizational_unit_id = oru.organizational_unit_id
GROUP BY u.user_id
	,u.code
	,u.primary_last_name
	,u.first_name
	,u.secondary_last_name
	,bun.description
	,oru.description
	,u.account
	,u.password
	,u.mail
	,u.description
	,u.STATUS
	,u.root
	,u.lang + '-' + u.locale
	,u.lang
	,u.locale
	,u.grid_size
	,u.detail_grid_size
	,u.floating_grid_size</createView>
    </changeSet>
    <changeSet id="Clementine-*******-********-rgarza-3" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="tblhistorialpeticionpresupuesto" />
        </preConditions>
        <comment>
            se cambia el nombre a uno mas corto para oracle
        </comment>
        <renameTable oldTableName="tblhistorialpeticionpresupuesto" newTableName="historialpeticionpresupuesto" />
        <renameColumn tableName="historialpeticionpresupuesto" oldColumnName="inthistorialpeticionpresupuestoid" newColumnName="historialpeticionpresupuestoid" />
    </changeSet>
    <changeSet id="Clementine-*******-********-rgarza-4" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="tblminuta" columnName="txtaccionesarealizaryasuntospendientes" />
        </preConditions>
        <comment>
            se cambia el nombre a uno mas corto para oracle
        </comment>
        <renameColumn tableName="tblminuta" oldColumnName="txtaccionesarealizaryasuntospendientes" newColumnName="txtaccionesarealizar" />
    </changeSet>
    <changeSet id="Clementine-*******-********-rgarza-5" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="tblrecurrenciareunionprogramacion" />
        </preConditions>
        <comment>
            se cambia el nombre a uno mas corto para oracle
        </comment>
        <renameTable oldTableName="tblrecurrenciareunionprogramacion" newTableName="recurrenciareunionprogramacion" />
    </changeSet>
    <changeSet id="Clementine-*******-********-rgarza-6" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="poll_temporal_respondent_respondent" />
        </preConditions>
        <comment>
            se cambia el nombre a uno mas corto para oracle
        </comment>
        <renameTable oldTableName="poll_temporal_respondent_respondent" newTableName="poll_temporal_respondent_resp" />
    </changeSet>
    <changeSet id="Clementine-*******-********-rgarza-7" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="settings" columnName="mail_to_business_unit_permission" />
        </preConditions>
        <comment>
            se cambia el nombre a uno mas corto para oracle
        </comment>
        <renameColumn tableName="settings" oldColumnName="mail_to_business_unit_permission" newColumnName="mail_to_bu_permission" />
        <renameColumn tableName="settings" oldColumnName="mail_to_department_permission" newColumnName="mail_to_dep_permission" />
        <renameColumn tableName="settings" oldColumnName="office_to_pdf_max_waiting_tasks" newColumnName="office_max_waiting_tasks" />
        <renameColumn tableName="settings" oldColumnName="expired_document_pending_module_manager" newColumnName="expired_doc_pen_module_mgr" />
        <renameColumn tableName="settings" oldColumnName="expired_document_pending_document_manager" newColumnName="expired_doc_pen_doc_mgr" />
        <renameColumn tableName="settings" oldColumnName="expired_document_mail_module_manager" newColumnName="expired_doc_mail_module_mgr" />
        <renameColumn tableName="settings" oldColumnName="expired_document_mail_document_manager" newColumnName="expired_doc_mail_doc_manager" />
        <renameColumn tableName="settings" oldColumnName="expired_document_mail_last_author" newColumnName="expired_doc_mail_last_author" />
    </changeSet>
    <changeSet id="Clementine-*******-20150812-rgarza-12" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <not>
                <dbms type="oracle" />
            </not>
        </preConditions>
        <modifyDataType tableName="EVALUATION_TYPE" columnName="description" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="audit" columnName="description" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="audit_type" columnName="description" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="model_brand" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="serial" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="localization" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="operation_range" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="precision" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="treshold" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="uncertainty" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="device" columnName="dispose_comment" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="gallery" columnName="description" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="measurement" columnName="absolute_error" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="measurement" columnName="second_measurement" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="receipt_acknowledgment" columnName="document_version" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="request" columnName="vch_descripcion" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="service" columnName="code" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="supported_languages" columnName="description" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="tblhistorialacceso" columnName="user_agent" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="tblhistorialacceso" columnName="ip_address" newDataType="VARCHAR(7999)" />
        <modifyDataType tableName="uncontrolled_document" columnName="description" newDataType="VARCHAR(7999)" />
    </changeSet>
    <changeSet id="fk_CODE_SEQUENCE_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_CODE_SEQUENCE_BUSINESS_UNIT_ID_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_CODE_SEQUENCE_BUSINESS_UNIT_ID_business_unit_business_unit_id', 
            'fk_CODE_SEQUENCE_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OA_ad" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_ANSWER_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_ANSWER_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID', 
            'fk_OA_ad', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OA_H_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_ANSWER_HEADER_ID_SURVEY_HEADER_HEADER_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_ANSWER_HEADER_ID_SURVEY_HEADER_HEADER_ID', 
            'fk_OA_H_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OA_I_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_ANSWER_ITEM_ID_SURVEY_ITEM_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_ANSWER_ITEM_ID_SURVEY_ITEM_ITEM_ID', 
            'fk_OA_I_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OA_MO_SMO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_ANSWER_MATRIX_OPTION_ID_SURVEY_MATRIX_OPTION_MATRIX_OPTION_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_ANSWER_MATRIX_OPTION_ID_SURVEY_MATRIX_OPTION_MATRIX_OPTION_ID', 
            'fk_OA_MO_SMO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OA_QUE_OQ" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_ANSWER_QUESTION_ID_OUTSTANDING_QUESTION_QUESTION_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_ANSWER_QUESTION_ID_OUTSTANDING_QUESTION_QUESTION_ID', 
            'fk_OA_QUE_OQ', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OQ_F_SF" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_QUESTION_FIELD_ID_SURVEY_FIELD_FIELD_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_QUESTION_FIELD_ID_SURVEY_FIELD_FIELD_ID', 
            'fk_OQ_F_SF', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OQ_as" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_QUESTION_audit_score_id_audit_score_audit_score_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_QUESTION_audit_score_id_audit_score_audit_score_id', 
            'fk_OQ_as', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OS_LOG" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_SURVEYS_LOG_OUTSTANDING_SURVEYS_ID_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_SURVEYS_LOG_OUTSTANDING_SURVEYS_ID_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID', 
            'fk_OS_LOG', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OS_LOG_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_SURVEYS_LOG_USUARIO_ID_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_SURVEYS_LOG_USUARIO_ID_users_user_id', 
            'fk_OS_LOG_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OS_QUE" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_SURVEYS_QUESTION_OUTSTANDING_SURVEYS_ID_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_SURVEYS_QUESTION_OUTSTANDING_SURVEYS_ID_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID', 
            'fk_OS_QUE', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OS_QUE_OQ" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_SURVEYS_QUESTION_QUESTION_ID_OUTSTANDING_QUESTION_QUESTION_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_SURVEYS_QUESTION_QUESTION_ID_OUTSTANDING_QUESTION_QUESTION_ID', 
            'fk_OS_QUE_OQ', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OS_SB" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_SURVEYS_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_SURVEYS_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID', 
            'fk_OS_SB', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_OS_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_OUTSTANDING_SURVEYS_SURVEY_ID_SURVEY_SURVEY_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_OUTSTANDING_SURVEYS_SURVEY_ID_SURVEY_SURVEY_ID', 
            'fk_OS_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SB_BI_T" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_BI_TRANSFER_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_BI_TRANSFER_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID', 
            'fk_SB_BI_T', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SB_BI_T_SBP" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_BI_TRANSFER_SURVEY_BLOCK_PARENT_ID_SURVEY_BLOCK_PARENT_SURVEY_BLOCK_PARENT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_BI_TRANSFER_SURVEY_BLOCK_PARENT_ID_SURVEY_BLOCK_PARENT_SURVEY_BLOCK_PARENT_ID', 
            'fk_SB_BI_T_SBP', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SB_BI_T_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_BI_TRANSFER_usuario_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_BI_TRANSFER_usuario_id_users_user_id', 
            'fk_SB_BI_T_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SBP_BLOCK_SB" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_PARENT_BLOCK_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_PARENT_BLOCK_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID', 
            'fk_SBP_BLOCK_SB', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SBP_BLOCK" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_PARENT_BLOCK_SURVEY_BLOCK_PARENT_ID_SURVEY_BLOCK_PARENT_SURVEY_BLOCK_PARENT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_PARENT_BLOCK_SURVEY_BLOCK_PARENT_ID_SURVEY_BLOCK_PARENT_SURVEY_BLOCK_PARENT_ID', 
            'fk_SBP_BLOCK', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SBPR_SBP" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_PARENT_RELATED_SURVEY_BLOCK_PARENT_ID_SURVEY_BLOCK_PARENT_SURVEY_BLOCK_PARENT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_PARENT_RELATED_SURVEY_BLOCK_PARENT_ID_SURVEY_BLOCK_PARENT_SURVEY_BLOCK_PARENT_ID', 
            'fk_SBPR_SBP', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SBPR_SBRS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_PARENT_RELATED_SURVEY_BLOCK_RELATED_SURVEY_ID_SURVEY_BLOCK_RELATED_SURVEY_SURVEY_BLOCK_RELATED_SURVEY_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_PARENT_RELATED_SURVEY_BLOCK_RELATED_SURVEY_ID_SURVEY_BLOCK_RELATED_SURVEY_SURVEY_BLOCK_RELATED_SURVEY_ID', 
            'fk_SBPR_SBRS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SBRS_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_RELATED_SURVEY_BUSINESS_UNIT_ID_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_RELATED_SURVEY_BUSINESS_UNIT_ID_business_unit_business_unit_id', 
            'fk_SBRS_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SBRS_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_RELATED_SURVEY_SURVEY_ID_SURVEY_SURVEY_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_RELATED_SURVEY_SURVEY_ID_SURVEY_SURVEY_ID', 
            'fk_SBRS_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SBRS_TS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_RELATED_SURVEY_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_RELATED_SURVEY_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID', 
            'fk_SBRS_TS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SB_SUBDOS_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_SUBDOS_BUSINESS_UNIT_DEPARTMENT_ID_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_SUBDOS_BUSINESS_UNIT_DEPARTMENT_ID_business_unit_department_business_unit_department_id', 
            'fk_SB_SUBDOS_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SB_SUBDOS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_SUBDOS_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_SUBDOS_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID', 
            'fk_SB_SUBDOS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SB_SU_SUB_DOS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_BLOCK_SURVEY_SUB_DOS_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_BLOCK_SURVEY_SUB_DOS_SURVEY_BLOCK_ID_SURVEY_BLOCK_SURVEY_BLOCK_ID', 
            'fk_SB_SU_SUB_DOS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFC_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_CELL_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_CELL_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SFC_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFC_H_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_CELL_HEADER_ID_SURVEY_HEADER_HEADER_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_CELL_HEADER_ID_SURVEY_HEADER_HEADER_ID', 
            'fk_SFC_H_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFC_I_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_CELL_ITEM_ID_SURVEY_ITEM_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_CELL_ITEM_ID_SURVEY_ITEM_ITEM_ID', 
            'fk_SFC_I_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFC_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_CELL_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_CELL_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SFC_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SF_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_H_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_HEADER_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_HEADER_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SF_H_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_H_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_HEADER_HEADER_ID_SURVEY_HEADER_HEADER_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_HEADER_HEADER_ID_SURVEY_HEADER_HEADER_ID', 
            'fk_SF_H_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_I_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_ITEM_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_ITEM_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SF_I_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_I_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_ITEM_ITEM_ID_SURVEY_ITEM_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_ITEM_ITEM_ID_SURVEY_ITEM_ITEM_ID', 
            'fk_SF_I_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFMH_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_MATRIX_HIDDEN_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_MATRIX_HIDDEN_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SFMH_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFMH_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_MATRIX_HIDDEN_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_MATRIX_HIDDEN_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SFMH_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFM_OPTIONS_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_MATRIX_OPTIONS_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_MATRIX_OPTIONS_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SFM_OPTIONS_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFM_OPTIONS_MO_SMO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_MATRIX_OPTIONS_MATRIX_OPTION_ID_SURVEY_MATRIX_OPTION_MATRIX_OPTION_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_MATRIX_OPTIONS_MATRIX_OPTION_ID_SURVEY_MATRIX_OPTION_MATRIX_OPTION_ID', 
            'fk_SFM_OPTIONS_MO_SMO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFO_ad" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_OBJECT_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_OBJECT_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID', 
            'fk_SFO_ad', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFO_ET" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_OBJECT_EVALUATION_TYPE_ID_EVALUATION_TYPE_EVALUATION_TYPE_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_OBJECT_EVALUATION_TYPE_ID_EVALUATION_TYPE_EVALUATION_TYPE_ID', 
            'fk_SFO_ET', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_OBJECT_QUESTION_THEME_ID_QUESTION_THEME_QUESTION_THEME_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_OBJECT_QUESTION_THEME_ID_QUESTION_THEME_QUESTION_THEME_ID', 
            'fk_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFO_TS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_OBJECT_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_OBJECT_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID', 
            'fk_SFO_TS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_OTHER_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_OTHER_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_OTHER_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SF_OTHER_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_OTHER_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_OTHER_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_OTHER_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SF_OTHER_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFS_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_SUBTITLE_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_SUBTITLE_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SFS_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SFS_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_SUBTITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_SUBTITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SFS_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_TITLE_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_TITLE_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_TITLE_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SF_TITLE_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_TITLE_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SF_TITLE_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_ad" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_area_department_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_area_department_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID', 
            'fk_SF_ad', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SF_ad_FO_SFO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_FIELD_area_department_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_FIELD_area_department_FIELD_OBJECT_ID_SURVEY_FIELD_OBJECT_FIELD_OBJECT_ID', 
            'fk_SF_ad_FO_SFO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_GO_OBJ" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID', 
            'fk_SG_GO_OBJ', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_GLOBAL" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_ID_SURVEY_GLOBAL_GLOBAL_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_ID_SURVEY_GLOBAL_GLOBAL_ID', 
            'fk_SG_GLOBAL', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_LANGUAGE_GO_OBJ" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_LANGUAGE_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_LANGUAGE_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID', 
            'fk_SG_LANGUAGE_GO_OBJ', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_LANGUAGE_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_LANGUAGE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_LANGUAGE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SG_LANGUAGE_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_OBJ_BU_BUID_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_OBJECT_BU_BUID_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_OBJECT_BU_BUID_business_unit_business_unit_id', 
            'fk_SG_OBJ_BU_BUID_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_OBJ_BU_GO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_OBJECT_BU_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_OBJECT_BU_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID', 
            'fk_SG_OBJ_BU_GO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_OBJ_TYP_ENC_GO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_OBJECT_TYP_ENC_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_OBJECT_TYP_ENC_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID', 
            'fk_SG_OBJ_TYP_ENC_GO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_OBJ_TYP_ENC_TS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_OBJECT_TYP_ENC_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_OBJECT_TYP_ENC_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID', 
            'fk_SG_OBJ_TYP_ENC_TS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_PAGES_GO_OBJ" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_PAGES_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_PAGES_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID', 
            'fk_SG_PAGES_GO_OBJ', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_PAGES_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_PAGES_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_PAGES_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SG_PAGES_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_TITLE_GO_OBJ" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_TITLE_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_TITLE_GLOBAL_OBJECT_ID_SURVEY_GLOBAL_OBJECT_GLOBAL_OBJECT_ID', 
            'fk_SG_TITLE_GO_OBJ', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SG_TITLE_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_GLOBAL_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_GLOBAL_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SG_TITLE_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_H" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_HEADER_QUESTION_THEME_ID_QUESTION_THEME_QUESTION_THEME_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_HEADER_QUESTION_THEME_ID_QUESTION_THEME_QUESTION_THEME_ID', 
            'fk_SU_H', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_H_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_HEADER_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_HEADER_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SU_H_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_H_TITLE" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_HEADER_TITLE_HEADER_ID_SURVEY_HEADER_HEADER_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_HEADER_TITLE_HEADER_ID_SURVEY_HEADER_HEADER_ID', 
            'fk_SU_H_TITLE', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_H_TITLE_TXT_I_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_HEADER_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_HEADER_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SU_H_TITLE_TXT_I_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_I_ad" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_ITEM_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_ITEM_AREA_DEPARTMENT_ID_AREA_DEPARTMENT_AREA_DEPARTMENT_ID', 
            'fk_SU_I_ad', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_I" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_ITEM_QUESTION_THEME_ID_QUESTION_THEME_QUESTION_THEME_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_ITEM_QUESTION_THEME_ID_QUESTION_THEME_QUESTION_THEME_ID', 
            'fk_SU_I', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_I_TITLE" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_ITEM_TITLE_ITEM_ID_SURVEY_ITEM_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_ITEM_TITLE_ITEM_ID_SURVEY_ITEM_ITEM_ID', 
            'fk_SU_I_TITLE', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_I_TITLE_TXT_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_ITEM_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_ITEM_TITLE_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SU_I_TITLE_TXT_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SMO_I_MO" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_MATRIX_OPTION_ITEM_MATRIX_OPTION_ID_SURVEY_MATRIX_OPTION_MATRIX_OPTION_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_MATRIX_OPTION_ITEM_MATRIX_OPTION_ID_SURVEY_MATRIX_OPTION_MATRIX_OPTION_ID', 
            'fk_SMO_I_MO', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SMO_I_TXT_STI" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_MATRIX_OPTION_ITEM_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_MATRIX_OPTION_ITEM_TEXT_ITEM_ID_SURVEY_TEXT_ITEM_TEXT_ITEM_ID', 
            'fk_SMO_I_TXT_STI', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_R_FS_F_SF" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_RELATED_FIELDS_FIELD_ID_SURVEY_FIELD_FIELD_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_RELATED_FIELDS_FIELD_ID_SURVEY_FIELD_FIELD_ID', 
            'fk_SU_R_FS_F_SF', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_SU_R_FS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_SURVEY_RELATED_FIELDS_SURVEY_ID_SURVEY_SURVEY_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_SURVEY_RELATED_FIELDS_SURVEY_ID_SURVEY_SURVEY_ID', 
            'fk_SU_R_FS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accf_acc_accgen" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_action_files_action_id_tblacciongenerica_intacciongenericaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_action_files_action_id_tblacciongenerica_intacciongenericaid', 
            'fk_accf_acc_accgen', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accf_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_action_files_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_action_files_file_id_files_id', 
            'fk_accf_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_aTakeF_aTake" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_action_to_take_files_action_to_take_id_tblacciontomar_intacciontomarid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_action_to_take_files_action_to_take_id_tblacciontomar_intacciontomarid', 
            'fk_aTakeF_aTake', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_aTakeF_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_action_to_take_files_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_action_to_take_files_file_id_files_id', 
            'fk_aTakeF_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_area_attendant_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_area_attendant_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_area_attendant_id_users_user_id', 
            'fk_area_attendant_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ad_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_area_department_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_area_department_id_business_unit_department_business_unit_department_id', 
            'fk_ad_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_au_attendant_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_attendant_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_attendant_id_users_user_id', 
            'fk_au_attendant_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_au_at" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_audit_type_id_audit_type_audit_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_audit_type_id_audit_type_audit_type_id', 
            'fk_au_at', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_au_author_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_author_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_author_id_users_user_id', 
            'fk_au_author_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_au_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_business_unit_department_audit_id_audit_audit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_business_unit_department_audit_id_audit_audit_id', 
            'fk_au_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auudit_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_business_unit_department_business_unit_department_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_business_unit_department_business_unit_department_id_business_unit_department_business_unit_department_id', 
            'fk_auudit_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_au_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_business_unit_id_business_unit_business_unit_id', 
            'fk_au_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_OS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_OUTSTANDING_SURVEYS_ID_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_OUTSTANDING_SURVEYS_ID_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID', 
            'fk_ai_OS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_au" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_audit_id_audit_audit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_audit_id_audit_audit_id', 
            'fk_ai_au', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_business_unit_department_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_business_unit_department_id_business_unit_department_business_unit_department_id', 
            'fk_ai_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_comm" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_comment_audit_individual_id_audit_individual_audit_individual_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_comment_audit_individual_id_audit_individual_audit_individual_id', 
            'fk_ai_comm', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_comm_author_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_comment_author_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_comment_author_id_users_user_id', 
            'fk_ai_comm_author_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_dp2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_department_process_id_department_process_department_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_department_process_id_department_process_department_process_id', 
            'fk_ai_dp2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_helpers" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_helpers_audit_individual_id_audit_individual_audit_individual_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_helpers_audit_individual_id_audit_individual_audit_individual_id', 
            'fk_ai_helpers', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ai_helpers_helper_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_individual_helpers_helper_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_individual_helpers_helper_id_users_user_id', 
            'fk_ai_helpers_helper_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_au_process" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_process_id_process_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_process_id_process_process_id', 
            'fk_au_process', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_au_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_audit_survey_id_SURVEY_SURVEY_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_audit_survey_id_SURVEY_SURVEY_ID', 
            'fk_au_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_apd_ap" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_autorization_pool_details_autorization_pool_id_autorization_pool_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_autorization_pool_details_autorization_pool_id_autorization_pool_id', 
            'fk_apd_ap', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_apd_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_autorization_pool_details_document_id_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_autorization_pool_details_document_id_document_id', 
            'fk_apd_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_apd_pue" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_autorization_pool_details_puesto_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_autorization_pool_details_puesto_id_puesto_puesto_id', 
            'fk_apd_pue', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_apd_request" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_autorization_pool_details_request_id_request_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_autorization_pool_details_request_id_request_id', 
            'fk_apd_request', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_apd_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_autorization_pool_details_user_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_autorization_pool_details_user_id_users_user_id', 
            'fk_apd_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ap_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_autorization_pool_document_id_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_autorization_pool_document_id_document_id', 
            'fk_ap_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ap_request" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_autorization_pool_request_id_request_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_autorization_pool_request_id_request_id', 
            'fk_ap_request', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bu_attendant_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_attendant_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_attendant_id_users_user_id', 
            'fk_bu_attendant_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bud_attendant_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_department_attendant_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_department_attendant_id_users_user_id', 
            'fk_bud_attendant_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bud_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_department_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_department_business_unit_id_business_unit_business_unit_id', 
            'fk_bud_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bud_dep" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_department_department_id_department_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_department_department_id_department_department_id', 
            'fk_bud_dep', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bud_doc_mgr_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_department_document_manager_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_department_document_manager_id_users_user_id', 
            'fk_bud_doc_mgr_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bu_doc_mgr_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_document_manager_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_document_manager_id_users_user_id', 
            'fk_bu_doc_mgr_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bu_ou2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_organizational_unit_id_organizational_unit_organizational_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_organizational_unit_id_organizational_unit_organizational_unit_id', 
            'fk_bu_ou2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_bu_settings" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_business_unit_settings_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_business_unit_settings_business_unit_id_business_unit_business_unit_id', 
            'fk_bu_settings', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dp2_TS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_department_process_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_department_process_TYPE_SURVEYED_ID_TYPE_SURVEYED_TYPE_SURVEYED_ID', 
            'fk_dp2_TS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dp2_attendant_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_department_process_attendant_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_department_process_attendant_id_users_user_id', 
            'fk_dp2_attendant_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dp2_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_department_process_business_unit_department_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_department_process_business_unit_department_id_business_unit_department_business_unit_department_id', 
            'fk_dp2_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dp2_process" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_department_process_process_id_process_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_department_process_process_id_process_process_id', 
            'fk_dp2_process', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_area" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_area_id_area_area_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_area_id_area_area_id', 
            'fk_dev_area', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_dep_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_department_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_department_id_business_unit_department_business_unit_department_id', 
            'fk_dev_dep_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_devg" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_device_group_id_device_group_device_group_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_device_group_id_device_group_device_group_id', 
            'fk_dev_devg', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_devt" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_device_type_id_device_type_device_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_device_type_id_device_type_device_type_id', 
            'fk_dev_devt', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_document_device_id_device_device_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_document_device_id_device_device_id', 
            'fk_dev_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_folio_area" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_folio_area_id_area_area_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_folio_area_id_area_area_id', 
            'fk_dev_folio_area', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_folio_dep_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_folio_department_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_folio_department_id_business_unit_department_business_unit_department_id', 
            'fk_dev_folio_dep_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_folio_devt" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_folio_device_type_id_device_type_device_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_folio_device_type_id_device_type_device_type_id', 
            'fk_dev_folio_devt', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_dev_responsible_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_responsible_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_responsible_id_users_user_id', 
            'fk_dev_responsible_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_devs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_status_device_status_device_status_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_status_device_status_device_status_id', 
            'fk_devs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_devt_devg" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_device_type_device_group_id_device_group_device_group_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_device_type_device_group_id_device_group_device_group_id', 
            'fk_devt_devg', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doc_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_document_autor_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_document_autor_id_users_user_id', 
            'fk_doc_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doc_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_document_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_document_business_unit_id_business_unit_business_unit_id', 
            'fk_doc_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doc_node_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_document_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_document_node_id_tblnodo_intnodoid', 
            'fk_doc_node_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doc_ou2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_document_organizational_unit_id_organizational_unit_organizational_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_document_organizational_unit_id_organizational_unit_organizational_unit_id', 
            'fk_doc_ou2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doc_originator_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_document_originator_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_document_originator_id_users_user_id', 
            'fk_doc_originator_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk__log" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_document_printing_log_document_printing_id_document_printing_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_document_printing_log_document_printing_id_document_printing_id', 
            'fk__log', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doc_request" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_document_request_id_request_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_document_request_id_request_id', 
            'fk_doc_request', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_flujo_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_flujo_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_flujo_business_unit_id_business_unit_business_unit_id', 
            'fk_flujo_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_flujo_ou2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_flujo_organizational_unit_id_organizational_unit_organizational_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_flujo_organizational_unit_id_organizational_unit_organizational_unit_id', 
            'fk_flujo_ou2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_flujo_pool" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_flujo_pool_flujo_id_flujo_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_flujo_pool_flujo_id_flujo_id', 
            'fk_flujo_pool', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_gallery_author_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_gallery_author_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_gallery_author_id_users_user_id', 
            'fk_gallery_author_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_gallery_node_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_gallery_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_gallery_node_id_tblnodo_intnodoid', 
            'fk_gallery_node_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_goal_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_goal_files_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_goal_files_file_id_files_id', 
            'fk_goal_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_goal_file_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_goal_files_goal_id_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_goal_files_goal_id_tblactividad_intactividadid', 
            'fk_goal_file_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_mPatt_patt" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_measurement_pattern_id_pattern_pattern_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_measurement_pattern_id_pattern_pattern_id', 
            'fk_mPatt_patt', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_measure_unit" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_measurement_unit_id_unit_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_measurement_unit_id_unit_unit_id', 
            'fk_measure_unit', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_mVar_var" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_measurement_variable_id_variable_variable_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_measurement_variable_id_variable_variable_id', 
            'fk_mVar_var', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_mem_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_memorandum_file_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_memorandum_file_file_id_files_id', 
            'fk_mem_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_mem_file_min" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_memorandum_file_file_id_tblminuta_intminutaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_memorandum_file_file_id_tblminuta_intminutaid', 
            'fk_mem_file_min', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_mem_file_reu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_memorandum_file_memorandum_id_tblreunion_intreunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_memorandum_file_memorandum_id_tblreunion_intreunionid', 
            'fk_mem_file_reu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_node_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_node_business_unit_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_node_business_unit_business_unit_id_business_unit_business_unit_id', 
            'fk_node_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_node_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_node_business_unit_department_business_unit_department_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_node_business_unit_department_business_unit_department_id_business_unit_department_business_unit_department_id', 
            'fk_node_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_node_bud_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_node_business_unit_department_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_node_business_unit_department_node_id_tblnodo_intnodoid', 
            'fk_node_bud_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_node_bu_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_node_business_unit_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_node_business_unit_node_id_tblnodo_intnodoid', 
            'fk_node_bu_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_node_bussiness_unit_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_node_bussiness_unit_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_node_bussiness_unit_business_unit_id_business_unit_business_unit_id', 
            'fk_node_bussiness_unit_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_node_bussiness_unit_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_node_bussiness_unit_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_node_bussiness_unit_node_id_tblnodo_intnodoid', 
            'fk_node_bussiness_unit_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ou2_doc_mgr_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_organizational_unit_document_manager_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_organizational_unit_document_manager_id_users_user_id', 
            'fk_ou2_doc_mgr_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ou2_predecessor" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_organizational_unit_predecessor_organizational_unit_organizational_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_organizational_unit_predecessor_organizational_unit_organizational_unit_id', 
            'fk_ou2_predecessor', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_patt_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_pattern_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_pattern_business_unit_id_business_unit_business_unit_id', 
            'fk_patt_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pr_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_physical_readers_document_id_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_physical_readers_document_id_document_id', 
            'fk_pr_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pr_pos_pue" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_physical_readers_position_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_physical_readers_position_id_puesto_puesto_id', 
            'fk_pr_pos_pue', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_author_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_author_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_author_id_users_user_id', 
            'fk_poll_author_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_business_unit_id_business_unit_business_unit_id', 
            'fk_poll_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_respondent_OS" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_respondent_outstanding_surveys_id_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_respondent_outstanding_surveys_id_OUTSTANDING_SURVEYS_OUTSTANDING_SURVEYS_ID', 
            'fk_poll_respondent_OS', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_respondent" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_respondent_poll_id_poll_poll_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_respondent_poll_id_poll_poll_id', 
            'fk_poll_respondent', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_respondent_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_respondent_respondent_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_respondent_respondent_id_users_user_id', 
            'fk_poll_respondent_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_survey_id_SURVEY_SURVEY_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_survey_id_SURVEY_SURVEY_ID', 
            'fk_poll_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_tr_respondent" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_temporal_respondent_respondent_poll_temporal_respondent_id_poll_temporal_respondent_poll_temporal_respondent_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_temporal_respondent_respondent_poll_temporal_respondent_id_poll_temporal_respondent_poll_temporal_respondent_id', 
            'fk_poll_tr_respondent', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_poll_tr_respondent_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_poll_temporal_respondent_respondent_respondent_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_poll_temporal_respondent_respondent_respondent_id_users_user_id', 
            'fk_poll_tr_respondent_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pool_details_pue" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_pool_details_puesto_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_pool_details_puesto_id_puesto_puesto_id', 
            'fk_pool_details_pue', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pool_details_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_pool_details_user_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_pool_details_user_id_users_user_id', 
            'fk_pool_details_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_process_attendant_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_process_attendant_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_process_attendant_id_users_user_id', 
            'fk_process_attendant_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_project_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_project_files_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_project_files_file_id_files_id', 
            'fk_project_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_project_file_proyecto" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_project_files_project_id_tblproyecto_intproyectoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_project_files_project_id_tblproyecto_intproyectoid', 
            'fk_project_file_proyecto', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_area_carr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_area_carrera_id_area_area_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_area_carrera_id_area_area_id', 
            'fk_pue_area_carr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_area" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_area_puesto_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_area_puesto_id_puesto_puesto_id', 
            'fk_pue_area', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_business_unit_id_business_unit_business_unit_id', 
            'fk_pue_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_dep" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_departamento_puesto_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_departamento_puesto_id_puesto_puesto_id', 
            'fk_pue_dep', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_dep_ubi_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_departamento_ubicacion_id_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_departamento_ubicacion_id_business_unit_department_business_unit_department_id', 
            'fk_pue_dep_ubi_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_dp2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_department_process_department_process_id_department_process_department_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_department_process_department_process_id_department_process_department_process_id', 
            'fk_pue_dp2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_puue_dp2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_department_process_puesto_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_department_process_puesto_id_puesto_puesto_id', 
            'fk_puue_dp2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_jefe" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_jefe_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_jefe_id_puesto_puesto_id', 
            'fk_pue_jefe', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_ou2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_organizational_unit_id_organizational_unit_organizational_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_organizational_unit_id_organizational_unit_organizational_unit_id', 
            'fk_pue_ou2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_perfil" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_perfil_id_perfil_perfil_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_perfil_id_perfil_perfil_id', 
            'fk_pue_perfil', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_une_facultad_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_une_facultad_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_une_facultad_id_business_unit_business_unit_id', 
            'fk_pue_une_facultad_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pue_une" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_puesto_une_puesto_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_puesto_une_puesto_id_puesto_puesto_id', 
            'fk_pue_une', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ra_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_receipt_acknowledgment_document_id_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_receipt_acknowledgment_document_id_document_id', 
            'fk_ra_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ra" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_receipt_acknowledgment_document_printing_id_document_printing_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_receipt_acknowledgment_document_printing_id_document_printing_id', 
            'fk_ra', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ra_pos_pue" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_receipt_acknowledgment_position_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_receipt_acknowledgment_position_id_puesto_puesto_id', 
            'fk_ra_pos_pue', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ra_reader_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_receipt_acknowledgment_reader_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_receipt_acknowledgment_reader_id_users_user_id', 
            'fk_ra_reader_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_R_doc_doc1" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_related_document_document_id1_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_related_document_document_id1_document_id', 
            'fk_R_doc_doc1', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_R_doc_doc2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_related_document_document_id2_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_related_document_document_id2_document_id', 
            'fk_R_doc_doc2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_autor_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_autor_id_users_user_id', 
            'fk_request_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_business_unit_id_business_unit_business_unit_id', 
            'fk_request_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_document_id_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_document_id_document_id', 
            'fk_request_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_log_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_log_autor_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_log_autor_id_users_user_id', 
            'fk_request_log_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_log_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_log_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_log_business_unit_id_business_unit_business_unit_id', 
            'fk_request_log_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_log_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_log_document_id_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_log_document_id_document_id', 
            'fk_request_log_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_log_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_log_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_log_file_id_files_id', 
            'fk_request_log_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_log_flujo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_log_flujo_id_flujo_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_log_flujo_id_flujo_id', 
            'fk_request_log_flujo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_log_node_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_log_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_log_node_id_tblnodo_intnodoid', 
            'fk_request_log_node_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_log_ou2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_log_organizational_unit_id_organizational_unit_organizational_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_log_organizational_unit_id_organizational_unit_organizational_unit_id', 
            'fk_request_log_ou2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_node_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_node_id_tblnodo_intnodoid', 
            'fk_request_node_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_request_ou2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_request_organizational_unit_id_organizational_unit_organizational_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_request_organizational_unit_id_organizational_unit_organizational_unit_id', 
            'fk_request_ou2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sfile_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_file_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_file_file_id_files_id', 
            'fk_sfile_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sfile_service" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_file_service_id_service_service_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_file_service_id_service_service_id', 
            'fk_sfile_service', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sm_measure" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_measurement_measurement_id_measurement_measurement_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_measurement_measurement_id_measurement_measurement_id', 
            'fk_sm_measure', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sm_service" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_measurement_service_id_service_service_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_measurement_service_id_service_service_id', 
            'fk_sm_service', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_service_result" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_result_id_service_result_service_result_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_result_id_service_result_service_result_id', 
            'fk_service_result', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_service_result_type" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_result_service_type_id_service_type_service_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_result_service_type_id_service_type_service_type_id', 
            'fk_service_result_type', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ss_dev" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_schedule_device_id_device_device_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_schedule_device_id_device_device_id', 
            'fk_ss_dev', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ss" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_schedule_id_service_schedule_service_schedule_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_schedule_id_service_schedule_service_schedule_id', 
            'fk_ss', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ssm_measure" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_schedule_measurement_measurement_id_measurement_measurement_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_schedule_measurement_measurement_id_measurement_measurement_id', 
            'fk_ssm_measure', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ssm_ss" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_schedule_measurement_service_schedule_id_service_schedule_service_schedule_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_schedule_measurement_service_schedule_id_service_schedule_service_schedule_id', 
            'fk_ssm_ss', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ss_periodicity_periodicidad" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_schedule_periodicity_id_tblperiodicidad_intperiodicidadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_schedule_periodicity_id_tblperiodicidad_intperiodicidadid', 
            'fk_ss_periodicity_periodicidad', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ss_responsible_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_schedule_responsible_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_schedule_responsible_id_users_user_id', 
            'fk_ss_responsible_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ss_service_type" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_service_schedule_service_type_id_service_type_service_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_service_schedule_service_type_id_service_type_service_type_id', 
            'fk_ss_service_type', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_A_eval" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_answer_evaluation_id_tbl_evaluation_evaluation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_answer_evaluation_id_tbl_evaluation_evaluation_id', 
            'fk_tbl_A_eval', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_A_enc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_answer_intencuestaid_tblencuesta_intencuestaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_answer_intencuestaid_tblencuesta_intencuestaid', 
            'fk_tbl_A_enc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_A_option" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_answer_option_id_tbl_option_option_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_answer_option_id_tbl_option_option_id', 
            'fk_tbl_A_option', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_A_QUE" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_answer_question_id_tbl_question_question_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_answer_question_id_tbl_question_question_id', 
            'fk_tbl_A_QUE', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_A_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_answer_user_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_answer_user_id_users_user_id', 
            'fk_tbl_A_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_eval_aut_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_evaluation_created_by_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_evaluation_created_by_users_user_id', 
            'fk_tbl_eval_aut_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_option_QUE" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_option_question_id_tbl_question_question_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_option_question_id_tbl_question_question_id', 
            'fk_tbl_option_QUE', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_QUE_eval" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_question_evaluation_id_tbl_evaluation_evaluation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_question_evaluation_id_tbl_evaluation_evaluation_id', 
            'fk_tbl_QUE_eval', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_QUE_section" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_question_question_section_tbl_section_section_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_question_question_section_tbl_section_section_id', 
            'fk_tbl_QUE_section', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_QUE_type" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_question_question_type_tbl_question_type_question_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_question_question_type_tbl_question_type_question_type_id', 
            'fk_tbl_QUE_type', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_section_eval" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_section_evaluation_id_tbl_evaluation_evaluation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_section_evaluation_id_tbl_evaluation_evaluation_id', 
            'fk_tbl_section_eval', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_usrs_eval_enc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_users_evaluation_intencuestaid_tblencuesta_intencuestaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_users_evaluation_intencuestaid_tblencuesta_intencuestaid', 
            'fk_tbl_usrs_eval_enc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_tbl_usrs_eval_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbl_users_evaluation_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbl_users_evaluation_intusuarioid_users_user_id', 
            'fk_tbl_usrs_eval_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accgen_aceptador_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciongenerica_intaceptadorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciongenerica_intaceptadorid_users_user_id', 
            'fk_accgen_aceptador_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accgen_aprobador_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciongenerica_intaprobadorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciongenerica_intaprobadorid_users_user_id', 
            'fk_accgen_aprobador_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accgen_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciongenerica_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciongenerica_intautorid_users_user_id', 
            'fk_accgen_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accgen_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciongenerica_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciongenerica_intnodoid_tblnodo_intnodoid', 
            'fk_accgen_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accgen_resp_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciongenerica_intresponsableid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciongenerica_intresponsableid_users_user_id', 
            'fk_accgen_resp_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accgen_ubi_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciongenerica_intubicacionid_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciongenerica_intubicacionid_business_unit_department_business_unit_department_id', 
            'fk_accgen_ubi_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_accgen_queja" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciongenerica_queja_id_tblqueja_intquejaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciongenerica_queja_id_tblqueja_intquejaid', 
            'fk_accgen_queja', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_aTake_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciontomar_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciontomar_intnodoid_tblnodo_intnodoid', 
            'fk_aTake_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_aTake_resp_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciontomar_intresponsableid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciontomar_intresponsableid_users_user_id', 
            'fk_aTake_resp_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_aTake_verificador_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciontomar_intverificadorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciontomar_intverificadorid_users_user_id', 
            'fk_aTake_verificador_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_aTake_cref_accgen_c" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblacciontomar_vchclavereferenciada_tblacciongenerica_vchclave';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblacciontomar_vchclavereferenciada_tblacciongenerica_vchclave', 
            'fk_aTake_cref_accgen_c', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_act_dificultad" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividad_intdificultadid_tbldificultad_intdificultadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividad_intdificultadid_tbldificultad_intdificultadid', 
            'fk_act_dificultad', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_act_modneg" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividad_intmodnegid_tblmodneg_intmodnegid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividad_intmodnegid_tblmodneg_intmodnegid', 
            'fk_act_modneg', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_act_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividad_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividad_intnodoid_tblnodo_intnodoid', 
            'fk_act_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_act_obj" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividad_intobjetoid_tblobjeto_intobjetoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividad_intobjetoid_tblobjeto_intobjetoid', 
            'fk_act_obj', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_act_P" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividad_intpadreid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividad_intpadreid_tblactividad_intactividadid', 
            'fk_act_P', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_act_tipo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividad_inttipoid_tbltipo_inttipoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividad_inttipoid_tbltipo_inttipoid', 
            'fk_act_tipo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_act_verificador_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividad_intverificadorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividad_intverificadorid_users_user_id', 
            'fk_act_verificador_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_actobj_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividadobjeto_intactividadid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividadobjeto_intactividadid_tblactividad_intactividadid', 
            'fk_actobj_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_actobj_obj" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividadobjeto_intobjetoid_tblobjeto_intobjetoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividadobjeto_intobjetoid_tblobjeto_intobjetoid', 
            'fk_actobj_obj', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_actresp_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividadresponsable_intactividadid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividadresponsable_intactividadid_tblactividad_intactividadid', 
            'fk_actresp_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_actresp_resp_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblactividadresponsable_intresponsableid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblactividadresponsable_intresponsableid_users_user_id', 
            'fk_actresp_resp_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_areausr_area_process" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblareausuario_intareaid_process_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblareausuario_intareaid_process_process_id', 
            'fk_areausr_area_process', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_areausr_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblareausuario_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblareausuario_intusuarioid_users_user_id', 
            'fk_areausr_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auorayudante_auoria" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditorayudante_intauditoriaid_tblauditoria_intauditoriaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditorayudante_intauditoriaid_tblauditoria_intauditoriaid', 
            'fk_auorayudante_auoria', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auorayudante_auor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditorayudante_intauditorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditorayudante_intauditorid_users_user_id', 
            'fk_auorayudante_auor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auoria_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditoria_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditoria_business_unit_id_business_unit_business_unit_id', 
            'fk_auoria_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auoria_dp2" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditoria_department_process_id_department_process_department_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditoria_department_process_id_department_process_department_process_id', 
            'fk_auoria_dp2', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auoria_auormgr_repo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditoria_intauditorencargadoid_tblrepositorio_intrepositorioid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditoria_intauditorencargadoid_tblrepositorio_intrepositorioid', 
            'fk_auoria_auormgr_repo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auoria_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditoria_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditoria_intautorid_users_user_id', 
            'fk_auoria_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auoria_cue_SU" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditoria_intcuestionarioid_SURVEY_SURVEY_ID';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditoria_intcuestionarioid_SURVEY_SURVEY_ID', 
            'fk_auoria_cue_SU', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auoriacomm_auoria" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditoriacomentario_intauditoriaid_tblauditoria_intauditoriaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditoriacomentario_intauditoriaid_tblauditoria_intauditoriaid', 
            'fk_auoriacomm_auoria', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_auoriacomm_auor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblauditoriacomentario_intauditorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblauditoriacomentario_intauditorid_users_user_id', 
            'fk_auoriacomm_auor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_cambiopen_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcambiopendiente_intactividadid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcambiopendiente_intactividadid_tblactividad_intactividadid', 
            'fk_cambiopen_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_cambiopen_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcambiopendiente_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcambiopendiente_intautorid_users_user_id', 
            'fk_cambiopen_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_carpetaarea_area_process" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcarpetaarea_intareaid_process_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcarpetaarea_intareaid_process_process_id', 
            'fk_carpetaarea_area_process', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_carpetaarea_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcarpetaarea_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcarpetaarea_intnodoid_tblnodo_intnodoid', 
            'fk_carpetaarea_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_carpetausr_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcarpetausuario_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcarpetausuario_intnodoid_tblnodo_intnodoid', 
            'fk_carpetausr_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_carpetausr_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcarpetausuario_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcarpetausuario_intusuarioid_users_user_id', 
            'fk_carpetausr_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_comm_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcomentario_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcomentario_intautorid_users_user_id', 
            'fk_comm_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_comm_ccomentada_reu_c" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcomentario_vchclavecomentada_tblreunion_vchclave';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcomentario_vchclavecomentada_tblreunion_vchclave', 
            'fk_comm_ccomentada_reu_c', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_cue_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcuestionario_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcuestionario_intautorid_users_user_id', 
            'fk_cue_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_cuesec_cue" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcuestionarioseccion_intcuestionarioid_tblcuestionario_intcuestionarioid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcuestionarioseccion_intcuestionarioid_tblcuestionario_intcuestionarioid', 
            'fk_cuesec_cue', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_cuesec_sec" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblcuestionarioseccion_intseccionid_tblseccion_intseccionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblcuestionarioseccion_intseccionid_tblseccion_intseccionid', 
            'fk_cuesec_sec', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_docolector_doco_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbldocumentolector_intdocumentoid_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbldocumentolector_intdocumentoid_document_id', 
            'fk_docolector_doco_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_docolector_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbldocumentolector_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbldocumentolector_intusuarioid_users_user_id', 
            'fk_docolector_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doconodo_creador_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbldocumentonodo_intcreadorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbldocumentonodo_intcreadorid_users_user_id', 
            'fk_doconodo_creador_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doconodo_doco_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbldocumentonodo_intdocumentoid_document_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbldocumentonodo_intdocumentoid_document_id', 
            'fk_doconodo_doco_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_doconodo_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbldocumentonodo_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbldocumentonodo_intnodoid_tblnodo_intnodoid', 
            'fk_doconodo_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_docosreferenciados_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tbldocumentosreferenciados_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tbldocumentosreferenciados_intnodoid_tblnodo_intnodoid', 
            'fk_docosreferenciados_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_enc_eval_tbl" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblencuesta_evaluation_id_tbl_evaluation_evaluation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblencuesta_evaluation_id_tbl_evaluation_evaluation_id', 
            'fk_enc_eval_tbl', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_enc_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblencuesta_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblencuesta_intautorid_users_user_id', 
            'fk_enc_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_enc_encmgr_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblencuesta_intencuestaencargadoid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblencuesta_intencuestaencargadoid_users_user_id', 
            'fk_enc_encmgr_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_firma_firmante_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblfirma_intfirmanteid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblfirma_intfirmanteid_users_user_id', 
            'fk_firma_firmante_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histacceso_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialacceso_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialacceso_intusuarioid_users_user_id', 
            'fk_histacceso_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histadv_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialavance_intactividadid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialavance_intactividadid_tblactividad_intactividadid', 
            'fk_histadv_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histadv_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialavance_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialavance_intautorid_users_user_id', 
            'fk_histadv_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histcambios_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialcambios_intactividadid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialcambios_intactividadid_tblactividad_intactividadid', 
            'fk_histcambios_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histcambios_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialcambios_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialcambios_intautorid_users_user_id', 
            'fk_histcambios_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histcomms_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialcomentarios_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialcomentarios_intusuarioid_users_user_id', 
            'fk_histcomms_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histcomms_chist_aTake_c" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialcomentarios_vchclavehistorial_tblacciontomar_vchclave';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialcomentarios_vchclavehistorial_tblacciontomar_vchclave', 
            'fk_histcomms_chist_aTake_c', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histpetpres_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialpeticionpresupuesto_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialpeticionpresupuesto_intautorid_users_user_id', 
            'fk_histpetpres_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_histpetpres_pres" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblhistorialpeticionpresupuesto_intpresupuestoid_tblpresupuesto_intpresupuestoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblhistorialpeticionpresupuesto_intpresupuestoid_tblpresupuesto_intpresupuestoid', 
            'fk_histpetpres_pres', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_imagen_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblimagen_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblimagen_intautorid_users_user_id', 
            'fk_imagen_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_imagen_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblimagen_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblimagen_intnodoid_tblnodo_intnodoid', 
            'fk_imagen_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_imagen_repo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblimagen_intrepositorioid_tblrepositorio_intrepositorioid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblimagen_intrepositorioid_tblrepositorio_intrepositorioid', 
            'fk_imagen_repo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ind_area_process" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicador_intareaid_process_process_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicador_intareaid_process_process_id', 
            'fk_ind_area_process', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ind_facultad_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicador_intfacultadid_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicador_intfacultadid_business_unit_business_unit_id', 
            'fk_ind_facultad_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ind_objetivo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicador_intobjetivoid_tblobjetivo_intobjetivoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicador_intobjetivoid_tblobjetivo_intobjetivoid', 
            'fk_ind_objetivo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ind_periodicidad" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicador_intperiodicidadid_tblperiodicidad_intperiodicidadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicador_intperiodicidadid_tblperiodicidad_intperiodicidadid', 
            'fk_ind_periodicidad', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ind_ubi_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicador_intubicacionid_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicador_intubicacionid_business_unit_department_business_unit_department_id', 
            'fk_ind_ubi_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ind_ubimonitoreo_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicador_intubicacionmonitoreoid_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicador_intubicacionmonitoreoid_business_unit_department_business_unit_department_id', 
            'fk_ind_ubimonitoreo_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indcalif_carr_area" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorcalificacion_intcarreraid_area_area_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorcalificacion_intcarreraid_area_area_id', 
            'fk_indcalif_carr_area', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indcalif_ind" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorcalificacion_intindicadorid_tblindicador_intindicadorid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorcalificacion_intindicadorid_tblindicador_intindicadorid', 
            'fk_indcalif_ind', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indcalif_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorcalificacion_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorcalificacion_intusuarioid_users_user_id', 
            'fk_indcalif_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indprog_ind" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorprogramacion_intindicadorid_tblindicador_intindicadorid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorprogramacion_intindicadorid_tblindicador_intindicadorid', 
            'fk_indprog_ind', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indresp_ind" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorresponsable_intindicadorid_tblindicador_intindicadorid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorresponsable_intindicadorid_tblindicador_intindicadorid', 
            'fk_indresp_ind', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indrev_area" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorrevision_area_id_area_area_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorrevision_area_id_area_area_id', 
            'fk_indrev_area', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indrev_ind" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorrevision_intindicadorid_tblindicador_intindicadorid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorrevision_intindicadorid_tblindicador_intindicadorid', 
            'fk_indrev_ind', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_indrev_revor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblindicadorrevision_intrevisorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblindicadorrevision_intrevisorid_users_user_id', 
            'fk_indrev_revor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_min_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblminuta_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblminuta_intautorid_users_user_id', 
            'fk_min_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_min_reu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblminuta_intreunionid_tblreunion_intreunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblminuta_intreunionid_tblreunion_intreunionid', 
            'fk_min_reu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_nodo_intnodoP" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblnodo_intnodopadre_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblnodo_intnodopadre_tblnodo_intnodoid', 
            'fk_nodo_intnodoP', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_obj_tipoobj" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblobjeto_inttipoobjetoid_tbltipoobjeto_inttipoobjetoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblobjeto_inttipoobjetoid_tbltipoobjeto_inttipoobjetoid', 
            'fk_obj_tipoobj', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_objrel_hijo_obj" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblobjetosrelacion_inthijoid_tblobjeto_intobjetoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblobjetosrelacion_inthijoid_tblobjeto_intobjetoid', 
            'fk_objrel_hijo_obj', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_objrel_P_obj" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblobjetosrelacion_intpadreid_tblobjeto_intobjetoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblobjetosrelacion_intpadreid_tblobjeto_intobjetoid', 
            'fk_objrel_P_obj', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pri_inv" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblparticipantereunioninvitado_intinvitadoid_tblinvitado_intinvitadoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblparticipantereunioninvitado_intinvitadoid_tblinvitado_intinvitadoid', 
            'fk_pri_inv', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pri_reu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblparticipantereunioninvitado_intreunionid_tblreunion_intreunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblparticipantereunioninvitado_intreunionid_tblreunion_intreunionid', 
            'fk_pri_reu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_parreuusr_reu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblparticipantereunionusuario_intreunionid_tblreunion_intreunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblparticipantereunionusuario_intreunionid_tblreunion_intreunionid', 
            'fk_parreuusr_reu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_parreuusr_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblparticipantereunionusuario_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblparticipantereunionusuario_intusuarioid_users_user_id', 
            'fk_parreuusr_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_permisosrepos_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblpermisosreportes_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblpermisosreportes_intusuarioid_users_user_id', 
            'fk_permisosrepos_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_petpres_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblpeticionpresupuesto_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblpeticionpresupuesto_intautorid_users_user_id', 
            'fk_petpres_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_petpres_pres" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblpeticionpresupuesto_intpresupuestoid_tblpresupuesto_intpresupuestoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblpeticionpresupuesto_intpresupuestoid_tblpresupuesto_intpresupuestoid', 
            'fk_petpres_pres', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_plantillausrs_plantilla" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblplantillausuarios_intplantillaid_tblplantilla_intplantillaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblplantillausuarios_intplantillaid_tblplantilla_intplantillaid', 
            'fk_plantillausrs_plantilla', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_plantillausrs_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblplantillausuarios_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblplantillausuarios_intusuarioid_users_user_id', 
            'fk_plantillausrs_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pregunta_sec" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblpregunta_intseccionid_tblseccion_intseccionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblpregunta_intseccionid_tblseccion_intseccionid', 
            'fk_pregunta_sec', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pres_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblpresupuesto_intactividadid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblpresupuesto_intactividadid_tblactividad_intactividadid', 
            'fk_pres_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_pres_rubro" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblpresupuesto_intrubroid_tblrubro_intrubroid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblpresupuesto_intrubroid_tblrubro_intrubroid', 
            'fk_pres_rubro', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_proyecto_bu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblproyecto_business_unit_id_business_unit_business_unit_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblproyecto_business_unit_id_business_unit_business_unit_id', 
            'fk_proyecto_bu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_proyecto_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblproyecto_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblproyecto_intautorid_users_user_id', 
            'fk_proyecto_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_proyecto_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblproyecto_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblproyecto_intnodoid_tblnodo_intnodoid', 
            'fk_proyecto_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_proyecto_resp_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblproyecto_intresponsableid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblproyecto_intresponsableid_users_user_id', 
            'fk_proyecto_resp_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_proyectoact_act" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblproyectoactividad_intactividadid_tblactividad_intactividadid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblproyectoactividad_intactividadid_tblactividad_intactividadid', 
            'fk_proyectoact_act', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_proyectoact_proyecto" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblproyectoactividad_intproyectoid_tblproyecto_intproyectoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblproyectoactividad_intproyectoid_tblproyecto_intproyectoid', 
            'fk_proyectoact_proyecto', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_queja_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblqueja_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblqueja_intautorid_users_user_id', 
            'fk_queja_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_queja_ubi_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblqueja_intubicacionid_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblqueja_intubicacionid_business_unit_department_business_unit_department_id', 
            'fk_queja_ubi_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_queja_worker_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblqueja_intworkerid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblqueja_intworkerid_users_user_id', 
            'fk_queja_worker_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureu_resp_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunion_intresponsableid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunion_intresponsableid_users_user_id', 
            'fk_recureu_resp_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureu_reu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunion_intreunionid_tblreunion_intreunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunion_intreunionid_tblreunion_intreunionid', 
            'fk_recureu_reu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureu_ubi_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunion_intubicacionid_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunion_intubicacionid_business_unit_department_business_unit_department_id', 
            'fk_recureu_ubi_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureuinv_inv" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunioninvitado_intinvitadoid_tblinvitado_intinvitadoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunioninvitado_intinvitadoid_tblinvitado_intinvitadoid', 
            'fk_recureuinv_inv', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureuinv_recureu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunioninvitado_intrecurrenciareunionid_tblrecurrenciareunion_intrecurrenciareunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunioninvitado_intrecurrenciareunionid_tblrecurrenciareunion_intrecurrenciareunionid', 
            'fk_recureuinv_recureu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureuprog_recureu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunionprogramacion_intrecurrenciareunionid_tblrecurrenciareunion_intrecurrenciareunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunionprogramacion_intrecurrenciareunionid_tblrecurrenciareunion_intrecurrenciareunionid', 
            'fk_recureuprog_recureu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureuusr_recureu" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunionusuario_intrecurrenciareunionid_tblrecurrenciareunion_intrecurrenciareunionid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunionusuario_intrecurrenciareunionid_tblrecurrenciareunion_intrecurrenciareunionid', 
            'fk_recureuusr_recureu', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_recureuusr_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrecurrenciareunionusuario_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrecurrenciareunionusuario_intusuarioid_users_user_id', 
            'fk_recureuusr_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_referencia_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblreferencia_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblreferencia_intautorid_users_user_id', 
            'fk_referencia_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_repousr_repo_repos" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblreporteusuario_intreporteid_tblreportes_intreporteid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblreporteusuario_intreporteid_tblreportes_intreporteid', 
            'fk_repousr_repo_repos', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_repousr_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblreporteusuario_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblreporteusuario_intusuarioid_users_user_id', 
            'fk_repousr_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_repo_creador_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrepositorio_intcreadorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrepositorio_intcreadorid_users_user_id', 
            'fk_repo_creador_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_repo_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrepositorio_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrepositorio_intnodoid_tblnodo_intnodoid', 
            'fk_repo_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_respuesta_auoria" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrespuesta_intauditoriaid_tblauditoria_intauditoriaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrespuesta_intauditoriaid_tblauditoria_intauditoriaid', 
            'fk_respuesta_auoria', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_respuesta_pregunta" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblrespuesta_intpreguntaid_tblpregunta_intpreguntaid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblrespuesta_intpreguntaid_tblpregunta_intpreguntaid', 
            'fk_respuesta_pregunta', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_reu_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblreunion_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblreunion_intnodoid_tblnodo_intnodoid', 
            'fk_reu_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_reu_resp_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblreunion_intresponsableid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblreunion_intresponsableid_users_user_id', 
            'fk_reu_resp_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_reu_ubi_bud" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblreunion_intubicacionid_business_unit_department_business_unit_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblreunion_intubicacionid_business_unit_department_business_unit_department_id', 
            'fk_reu_ubi_bud', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sol_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblsolicitud_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblsolicitud_intnodoid_tblnodo_intnodoid', 
            'fk_sol_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sol_soli_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblsolicitud_intsolicitanteid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblsolicitud_intsolicitanteid_users_user_id', 
            'fk_sol_soli_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sollog_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblsolicitudlog_intnodoid_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblsolicitudlog_intnodoid_tblnodo_intnodoid', 
            'fk_sollog_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_sollog_soli_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblsolicitudlog_intsolicitanteid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblsolicitudlog_intsolicitanteid_users_user_id', 
            'fk_sollog_soli_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_usrexterno_proyecto" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblusuarioexterno_intproyectoid_tblproyecto_intproyectoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblusuarioexterno_intproyectoid_tblproyecto_intproyectoid', 
            'fk_usrexterno_proyecto', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_usrexterno_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblusuarioexterno_intusuarioid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblusuarioexterno_intusuarioid_users_user_id', 
            'fk_usrexterno_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_usrperfil_autor_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_tblusuarioperfil_intautorid_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_tblusuarioperfil_intautorid_users_user_id', 
            'fk_usrperfil_autor_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_udoc_doc_author_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_uncontrolled_document_author_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_uncontrolled_document_author_id_users_user_id', 
            'fk_udoc_doc_author_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_udoc_doc_file" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_uncontrolled_document_file_id_files_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_uncontrolled_document_file_id_files_id', 
            'fk_udoc_doc_file', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_udoc_doc_node_nodo" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_uncontrolled_document_node_id_tblnodo_intnodoid';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_uncontrolled_document_node_id_tblnodo_intnodoid', 
            'fk_udoc_doc_node_nodo', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_usr_settings_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_user_settings_user_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_user_settings_user_id_users_user_id', 
            'fk_usr_settings_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_usr_pue" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_usuario_puesto_puesto_id_puesto_puesto_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_usuario_puesto_puesto_id_puesto_puesto_id', 
            'fk_usr_pue', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_usr_pue_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_usuario_puesto_usuario_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_usuario_puesto_usuario_id_users_user_id', 
            'fk_usr_pue_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_va_aBy_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_authorization_authorized_by_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_authorization_authorized_by_users_user_id', 
            'fk_va_aBy_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_class" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_classification_id_classification_classification_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_classification_id_classification_classification_id', 
            'fk_valid_class', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_comm_aut_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_comment_created_by_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_comment_created_by_users_user_id', 
            'fk_valid_comm_aut_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_comm" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_comment_validation_id_validation_validation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_comment_validation_id_validation_validation_id', 
            'fk_valid_comm', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_dep" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_department_id_department_department_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_department_id_department_department_id', 
            'fk_valid_dep', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_doc" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_document_validation_id_validation_validation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_document_validation_id_validation_validation_id', 
            'fk_valid_doc', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_execution" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_execution_validation_id_validation_validation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_execution_validation_id_validation_validation_id', 
            'fk_valid_execution', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_P" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_parent_id_validation_validation_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_parent_id_validation_validation_id', 
            'fk_valid_P', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_priority" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_priority_id_priority_priority_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_priority_id_priority_priority_id', 
            'fk_valid_priority', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_responsible_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_responsible_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_responsible_id_users_user_id', 
            'fk_valid_responsible_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_study_type" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_study_type_id_study_type_study_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_study_type_id_study_type_study_type_id', 
            'fk_valid_study_type', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_type_P" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_type_parent_validation_type_validation_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_type_parent_validation_type_validation_type_id', 
            'fk_valid_type_P', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_valid_type" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_validation_validation_type_id_validation_type_validation_type_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_validation_validation_type_id_validation_type_validation_type_id', 
            'fk_valid_type', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ver_log_request" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_verification_log_request_id_request_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_verification_log_request_id_request_id', 
            'fk_ver_log_request', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ver_log_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_verification_log_user_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_verification_log_user_id_users_user_id', 
            'fk_ver_log_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ver_request" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_verification_request_id_request_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_verification_request_id_request_id', 
            'fk_ver_request', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ver_usr_usrs" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_verification_user_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_verification_user_id_users_user_id', 
            'fk_ver_usr_usrs', 
            'OBJECT'
        </sql>
    </changeSet> 
    <changeSet id="fk_ver_veri_usrs_usr" author="Ricardo Garza Verastegui">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <sqlCheck expectedResult="1">select COUNT(1) from sys.objects where type_desc = 'FOREIGN_KEY_CONSTRAINT' and name = 'fk_verification_verificator_id_users_user_id';</sqlCheck>
        </preConditions>
        <sql>
            EXEC sp_rename 'dbo.fk_verification_verificator_id_users_user_id', 
            'fk_ver_veri_usrs_usr', 
            'OBJECT'
        </sql>
    </changeSet>
</databaseChangeLog>
