<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.3.xsd">
    <changeSet id="Clementine-2.8.0-20151029" author="<PERSON>" dbms="mssql">
        <validCheckSum>8:3e7d8e1c3de08e24d6e974d9ab0ea8d7</validCheckSum>
        <createView viewName="audit_individual_list" replaceIfExists="true">
            <![CDATA[
                SELECT pro_att.first_name AS audited,
                        pro.attendant_id AS audited_id,
                        team_leader.first_name AS audit_team_leader,
                        au.attendant_id AS audit_team_leader_id,
                        lider.first_name AS lider,
                        ind.attendant_id AS lider_id,
                        au.anticipation,
                        bu.description AS bud_description,
                        bu.business_unit_id,
                        pro.title AS department_process_description,
                        NULL AS area_description,
                        typ.scope,
                        typ.description AS audit_type_description,
                        fill.STATUS AS fill_status,
                        ind.audit_individual_id,
                        ind.status,
                        ind.deleted,
                        ind.code,
                        ind.description,
                        ind.dte_start,
                        ind.dte_end,
                        ind.tmp_start,
                        ind.tmp_end,
                        ind.audit_id,
                        ind.business_unit_department_id,
                        ind.OUTSTANDING_SURVEYS_ID,
                        ind.department_process_id,
                        ind.dte_start_request,
                        ind.dte_end_request,
                        ind.tmp_start_request,
                        ind.tmp_end_request,
                        ind.reason,
                        ind.decline_reason,
                        ind.building_id,
                        ind.area_id,
                        ind.minimum_score,
                        ind.survey_id,
                        ind.support_staff,
                        ind.technical_experts,
                        ind.auditors_in_training,
                        ind.actual_start,
                        ind.actual_end
                FROM audit_individual ind
                INNER JOIN department_process_view pro ON ind.department_process_id = pro.department_process_id
                        AND ind.department_process_id IS NOT NULL
                INNER JOIN business_unit_department_view bu ON bu.business_unit_department_id = ind.business_unit_department_id
                INNER JOIN audits au ON ind.audit_id = au.audit_id
                INNER JOIN users pro_att ON pro.attendant_id = pro_att.user_id
                INNER JOIN users team_leader ON au.attendant_id = team_leader.user_id
                INNER JOIN users lider ON ind.attendant_id = lider.user_id
                LEFT JOIN audit_type typ ON typ.audit_type_id = au.audit_type_id
                LEFT JOIN OUTSTANDING_SURVEYS fill ON fill.OUTSTANDING_SURVEYS_ID = IND.OUTSTANDING_SURVEYS_ID

                UNION ALL

                SELECT are_att.first_name AS audited,
                        are.attendant_id AS audited_id,
                        team_leader.first_name AS audit_team_leader,
                        au.attendant_id AS audit_team_leader_id,
                        lider.first_name AS lider,
                        ind.attendant_id AS lider_id,
                        au.anticipation,
                        bu.description AS bud_description,
                        bu.business_unit_id,
                        NULL AS department_process_description,
                        are.description AS area_description,
                        typ.scope,
                        typ.description AS audit_type_description,
                        fill.STATUS AS fill_status,
                        ind.audit_individual_id,
                        ind.status,
                        ind.deleted,
                        ind.code,
                        ind.description,
                        ind.dte_start,
                        ind.dte_end,
                        ind.tmp_start,
                        ind.tmp_end,
                        ind.audit_id,
                        ind.business_unit_department_id,
                        ind.OUTSTANDING_SURVEYS_ID,
                        ind.department_process_id,
                        ind.dte_start_request,
                        ind.dte_end_request,
                        ind.tmp_start_request,
                        ind.tmp_end_request,
                        ind.reason,
                        ind.decline_reason,
                        ind.building_id,
                        ind.area_id,
                        ind.minimum_score,
                        ind.survey_id,
                        ind.support_staff,
                        ind.technical_experts,
                        ind.auditors_in_training,
                        ind.actual_start,
                        ind.actual_end
                FROM audit_individual ind
                INNER JOIN area are ON ind.area_id = are.area_id
                        AND ind.area_id IS NOT NULL
                INNER JOIN business_unit_department_view bu ON bu.business_unit_department_id = ind.business_unit_department_id
                INNER JOIN audits au ON ind.audit_id = au.audit_id
                INNER JOIN users are_att ON are.attendant_id = are_att.user_id
                INNER JOIN users team_leader ON au.attendant_id = team_leader.user_id
                INNER JOIN users lider ON ind.attendant_id = lider.user_id
                LEFT JOIN audit_type typ ON typ.audit_type_id = au.audit_type_id
                LEFT JOIN OUTSTANDING_SURVEYS fill ON fill.OUTSTANDING_SURVEYS_ID = IND.OUTSTANDING_SURVEYS_ID
            ]]>
        </createView>
    </changeSet>
    <changeSet id="Clementine-2.8.0-20151029-2" author="Julio Guadalupe Alemán Reyes" dbms="mssql">
        <createView viewName="audit_individual_list" replaceIfExists="true">
            <![CDATA[
                SELECT pro_att.first_name AS audited,
                        pro.attendant_id AS audited_id,
                        team_leader.first_name AS audit_team_leader,
                        au.attendant_id AS audit_team_leader_id,
                        lider.first_name AS lider,
                        ind.attendant_id AS lider_id,
                        au.anticipation,
                        bu.description AS bud_description,
                        bu.business_unit_id,
                        pro.title AS department_process_description,
                        NULL AS area_description,
                        typ.scope,
                        typ.description AS audit_type_description,
                        fill.STATUS AS fill_status,
                        ind.audit_individual_id,
                        ind.status,
                        ind.deleted,
                        ind.code,
                        ind.description,
                        ind.dte_start,
                        ind.dte_end,
                        ind.tmp_start,
                        ind.tmp_end,
                        ind.audit_id,
                        ind.business_unit_department_id,
                        ind.OUTSTANDING_SURVEYS_ID,
                        ind.department_process_id,
                        ind.dte_start_request,
                        ind.dte_end_request,
                        ind.tmp_start_request,
                        ind.tmp_end_request,
                        ind.reason,
                        ind.decline_reason,
                        ind.building_id,
                        ind.area_id,
                        ind.minimum_score,
                        ind.survey_id,
                        ind.support_staff,
                        ind.technical_experts,
                        ind.auditors_in_training,
                        ind.actual_start,
                        ind.actual_end
                FROM audit_individual ind
                INNER JOIN department_process_view pro ON ind.department_process_id = pro.department_process_id
                        AND ind.department_process_id IS NOT NULL
                INNER JOIN business_unit_department_view bu ON bu.business_unit_department_id = ind.business_unit_department_id
                INNER JOIN audits au ON ind.audit_id = au.audit_id
                INNER JOIN users pro_att ON pro.attendant_id = pro_att.user_id
                INNER JOIN users team_leader ON au.attendant_id = team_leader.user_id
                INNER JOIN users lider ON ind.attendant_id = lider.user_id
                LEFT JOIN audit_type typ ON typ.audit_type_id = au.audit_type_id
                LEFT JOIN OUTSTANDING_SURVEYS fill ON fill.OUTSTANDING_SURVEYS_ID = IND.OUTSTANDING_SURVEYS_ID

                UNION ALL

                SELECT are_att.first_name AS audited,
                        are.attendant_id AS audited_id,
                        team_leader.first_name AS audit_team_leader,
                        au.attendant_id AS audit_team_leader_id,
                        lider.first_name AS lider,
                        ind.attendant_id AS lider_id,
                        au.anticipation,
                        bu.description AS bud_description,
                        bu.business_unit_id,
                        NULL AS department_process_description,
                        are.description AS area_description,
                        typ.scope,
                        typ.description AS audit_type_description,
                        fill.STATUS AS fill_status,
                        ind.audit_individual_id,
                        ind.status,
                        ind.deleted,
                        ind.code,
                        ind.description,
                        ind.dte_start,
                        ind.dte_end,
                        ind.tmp_start,
                        ind.tmp_end,
                        ind.audit_id,
                        ind.business_unit_department_id,
                        ind.OUTSTANDING_SURVEYS_ID,
                        ind.department_process_id,
                        ind.dte_start_request,
                        ind.dte_end_request,
                        ind.tmp_start_request,
                        ind.tmp_end_request,
                        ind.reason,
                        ind.decline_reason,
                        ind.building_id,
                        ind.area_id,
                        ind.minimum_score,
                        ind.survey_id,
                        ind.support_staff,
                        ind.technical_experts,
                        ind.auditors_in_training,
                        ind.actual_start,
                        ind.actual_end
                FROM audit_individual ind
                INNER JOIN area are ON ind.area_id = are.area_id
                        AND ind.area_id IS NOT NULL
                INNER JOIN business_unit_department_view bu ON bu.business_unit_department_id = ind.business_unit_department_id
                INNER JOIN audits au ON ind.audit_id = au.audit_id
                INNER JOIN users are_att ON are.attendant_id = are_att.user_id
                INNER JOIN users team_leader ON au.attendant_id = team_leader.user_id
                INNER JOIN users lider ON ind.attendant_id = lider.user_id
                LEFT JOIN audit_type typ ON typ.audit_type_id = au.audit_type_id
                LEFT JOIN OUTSTANDING_SURVEYS fill ON fill.OUTSTANDING_SURVEYS_ID = IND.OUTSTANDING_SURVEYS_ID
            ]]>
        </createView>
    </changeSet>
    
</databaseChangeLog>