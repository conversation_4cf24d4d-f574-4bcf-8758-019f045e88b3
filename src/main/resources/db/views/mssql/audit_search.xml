<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.3.xsd">
    <changeSet id="Date-*******-20151117"
               author="<PERSON>" 
               dbms="mssql">
        <createView viewName="audit_search" replaceIfExists="true">
            <![CDATA[
                SELECT 
                    au.audit_id
                  , au.code 
                  , au.description 
                  , au.deleted 
                  , au.anticipation 
                  , au.dte_start 
                  , au.dte_end 
                  , au.dte_anticipation 
                  , au.tmp_start 
                  , au.tmp_end 
                  , au.attendant_id 
                  , au.author_id 
                  , au.business_unit_id 
                  , au.survey_id 
                  , au.status 
                  , au.process_id 
                  ,COUNT(aud.business_unit_department_id) as related_departments
              FROM audits au
              LEFT JOIN audit_business_unit_department aud ON au.audit_id = aud.audit_id
              GROUP by 	au.audit_id
                  , au.code 
                  , au.description 
                  , au.deleted 
                  , au.anticipation 
                  , au.dte_start 
                  , au.dte_end 
                  , au.dte_anticipation 
                  , au.tmp_start 
                  , au.tmp_end 
                  , au.attendant_id 
                  , au.author_id 
                  , au.business_unit_id 
                  , au.survey_id 
                  , au.status 
                  , au.process_id
            ]]>
        </createView>
    </changeSet>
    
</databaseChangeLog>