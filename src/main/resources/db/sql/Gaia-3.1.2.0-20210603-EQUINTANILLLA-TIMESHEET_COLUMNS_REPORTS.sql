UPDATE rc
SET rc.description = 'Hrs.', rc.type = 1, rc.grid_order = 7
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'Hrs' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día Reportado', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'DiaReportado' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día Creado', rc.type = 1, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'DiaCreado' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'Nombre' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Comentario', rc.type = 1, rc.grid_order = 8
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'Comentario' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'Proyecto' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Tarea', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'Tarea' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'Departamento' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeetasks' AND qc.code = 'Cliente' AND dq.code = 'rpt-daily-employeetasks' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployee' AND qc.code = 'Nombre' AND dq.code = 'rpt-daily-missingtaskemployee' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Usuario', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployee' AND qc.code = 'usuario' AND dq.code = 'rpt-daily-missingtaskemployee' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployee' AND qc.code = 'Departamento' AND dq.code = 'rpt-daily-missingtaskemployee' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'mier', rc.type = 1, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'mier' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'dom', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'dom' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'Nombre' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Usuario', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'usuario' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'mar', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'mar' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'lun', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'lun' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'Departamento' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'sab', rc.type = 1, rc.grid_order = 9
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'sab' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'jue', rc.type = 1, rc.grid_order = 7
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'jue' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'vie', rc.type = 1, rc.grid_order = 8
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND qc.code = 'vie' AND dq.code = 'rpt-daily-missingtaskemployeeweekyesterday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-weekly-activeproject' AND qc.code = 'Projecto' AND dq.code = 'rpt-weekly-activeproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs. Totales Acumuladas', rc.type = 5, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-weekly-activeproject' AND qc.code = 'HrsTotalesAcumuladas' AND dq.code = 'rpt-weekly-activeproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-weekly-activeproject' AND qc.code = 'Cliente' AND dq.code = 'rpt-weekly-activeproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-weekly-activeproject' AND qc.code = 'Departamento' AND dq.code = 'rpt-weekly-activeproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Id Proj', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-weekly-activeproject' AND qc.code = 'IdProj' AND dq.code = 'rpt-weekly-activeproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs. Acum de la Semana', rc.type = 5, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-weekly-activeproject' AND qc.code = 'HrsAcumDeLaSemana' AND dq.code = 'rpt-weekly-activeproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-weekhours' AND qc.code = 'hrs' AND dq.code = 'rpt-daily-weekhours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-weekhours' AND qc.code = 'Departamento' AND dq.code = 'rpt-daily-weekhours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'No. Semana Creado', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-weekhours' AND qc.code = 'NoSemanaCreado' AND dq.code = 'rpt-daily-weekhours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'No.Semana Reportada', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-weekhours' AND qc.code = 'NoSemanaReportada' AND dq.code = 'rpt-daily-weekhours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Id Usuario', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-weekhours' AND qc.code = 'IdUsuario' AND dq.code = 'rpt-daily-weekhours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'mier', rc.type = 1, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'mier' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'dom', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'dom' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'Nombre' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Usuario', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'usuario' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'mar', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'mar' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'lun', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'lun' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'Departamento' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'sab', rc.type = 1, rc.grid_order = 9
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'sab' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'jue', rc.type = 1, rc.grid_order = 7
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'jue' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'vie', rc.type = 1, rc.grid_order = 8
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-missingtaskemployeeweektoday' AND qc.code = 'vie' AND dq.code = 'rpt-daily-missingtaskemployeeweektoday' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'turn', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-monthly-spt-clienthours' AND qc.code = 'turn' AND dq.code = 'rpt-monthly-spt-clienthours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'parameter_name', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-monthly-spt-clienthours' AND qc.code = 'parameter_name' AND dq.code = 'rpt-monthly-spt-clienthours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'parameter_value', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-monthly-spt-clienthours' AND qc.code = 'parameter_value' AND dq.code = 'rpt-monthly-spt-clienthours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Rango', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-currentmonthhour' AND qc.code = 'Rango' AND dq.code = 'rpt-range-spt-currentmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-currentmonthhour' AND qc.code = 'Cliente' AND dq.code = 'rpt-range-spt-currentmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 5, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-currentmonthhour' AND qc.code = 'Hrs' AND dq.code = 'rpt-range-spt-currentmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-currentmonthhour' AND qc.code = 'Proyecto' AND dq.code = 'rpt-range-spt-currentmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Rango', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-lastmonthhour' AND qc.code = 'Rango' AND dq.code = 'rpt-range-spt-lastmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-lastmonthhour' AND qc.code = 'Cliente' AND dq.code = 'rpt-range-spt-lastmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-lastmonthhour' AND qc.code = 'Hrs' AND dq.code = 'rpt-range-spt-lastmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-lastmonthhour' AND qc.code = 'Proyecto' AND dq.code = 'rpt-range-spt-lastmonthhour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Rango', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executefullprojecthour' AND qc.code = 'Rango' AND dq.code = 'rpt-range-spt-executefullprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executefullprojecthour' AND qc.code = 'Cliente' AND dq.code = 'rpt-range-spt-executefullprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executefullprojecthour' AND qc.code = 'Hrs' AND dq.code = 'rpt-range-spt-executefullprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executefullprojecthour' AND qc.code = 'Proyecto' AND dq.code = 'rpt-range-spt-executefullprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Comentario', rc.type = 1, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executecurrentprojecthour' AND qc.code = 'Comentario' AND dq.code = 'rpt-range-spt-executecurrentprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día Reportado', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executecurrentprojecthour' AND qc.code = 'DiaReportado' AND dq.code = 'rpt-range-spt-executecurrentprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executecurrentprojecthour' AND qc.code = 'Nombre' AND dq.code = 'rpt-range-spt-executecurrentprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executecurrentprojecthour' AND qc.code = 'Hrs' AND dq.code = 'rpt-range-spt-executecurrentprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executecurrentprojecthour' AND qc.code = 'Cliente' AND dq.code = 'rpt-range-spt-executecurrentprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Tarea', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executecurrentprojecthour' AND qc.code = 'Tarea' AND dq.code = 'rpt-range-spt-executecurrentprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executecurrentprojecthour' AND qc.code = 'Proyecto' AND dq.code = 'rpt-range-spt-executecurrentprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Comentario', rc.type = 1, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executepastprojecthour' AND qc.code = 'Comentario' AND dq.code = 'rpt-range-spt-executepastprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día Reportado', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executepastprojecthour' AND qc.code = 'DiaReportado' AND dq.code = 'rpt-range-spt-executepastprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executepastprojecthour' AND qc.code = 'Nombre' AND dq.code = 'rpt-range-spt-executepastprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executepastprojecthour' AND qc.code = 'Hrs' AND dq.code = 'rpt-range-spt-executepastprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executepastprojecthour' AND qc.code = 'Cliente' AND dq.code = 'rpt-range-spt-executepastprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Tarea', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executepastprojecthour' AND qc.code = 'Tarea' AND dq.code = 'rpt-range-spt-executepastprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-range-spt-executepastprojecthour' AND qc.code = 'Proyecto' AND dq.code = 'rpt-range-spt-executepastprojecthour' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Desarrollador', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-hourreqid' AND qc.code = 'Desarrollador' AND dq.code = 'rpt-daily-erpqa-hourreqid' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'ReqId', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-hourreqid' AND qc.code = 'ReqId' AND dq.code = 'rpt-daily-erpqa-hourreqid' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Semana', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-hourreqid' AND qc.code = 'Semana' AND dq.code = 'rpt-daily-erpqa-hourreqid' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-hourreqid' AND qc.code = 'Proyecto' AND dq.code = 'rpt-daily-erpqa-hourreqid' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Tarea', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-hourreqid' AND qc.code = 'Tarea' AND dq.code = 'rpt-daily-erpqa-hourreqid' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs. Ejecutado', rc.type = 5, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-hourreqid' AND qc.code = 'HrsEjecutado' AND dq.code = 'rpt-daily-erpqa-hourreqid' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-hourreqid' AND qc.code = 'Cliente' AND dq.code = 'rpt-daily-erpqa-hourreqid' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'Nombre' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'mier', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'mier' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'mar', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'mar' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'dom', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'dom' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Usuario', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'usuario' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'lun', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'lun' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'vie', rc.type = 1, rc.grid_order = 7
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'vie' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'jue', rc.type = 1, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'jue' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'sab', rc.type = 1, rc.grid_order = 8
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-erpqa-currentweek' AND qc.code = 'sab' AND dq.code = 'rpt-daily-erpqa-currentweek' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Id_Usuario', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeehours' AND qc.code = 'Id_Usuario' AND dq.code = 'rpt-daily-employeehours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 5, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeehours' AND qc.code = 'hrs' AND dq.code = 'rpt-daily-employeehours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeehours' AND qc.code = 'Departamento' AND dq.code = 'rpt-daily-employeehours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día Creado', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeehours' AND qc.code = 'Dia_Creado' AND dq.code = 'rpt-daily-employeehours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día Reportado', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-employeehours' AND qc.code = 'Dia_Reportado' AND dq.code = 'rpt-daily-employeehours' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Projecto', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-projecthrs' AND qc.code = 'Projecto' AND dq.code = 'rpt-daily-projecthrs' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs. Totales Acumuladas', rc.type = 5, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-projecthrs' AND qc.code = 'HrsTotalesAcumuladas' AND dq.code = 'rpt-daily-projecthrs' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs. Acum del Día', rc.type = 5, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-projecthrs' AND qc.code = 'HrsAcumDelDia' AND dq.code = 'rpt-daily-projecthrs' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-projecthrs' AND qc.code = 'Cliente' AND dq.code = 'rpt-daily-projecthrs' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Id Proj', rc.type = 4, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-daily-projecthrs' AND qc.code = 'IdProj' AND dq.code = 'rpt-daily-projecthrs' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Fecha de creación', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-drilldownuser' AND qc.code = 'fecha_creacion' AND dq.code = 'rpt-adm-drilldownuser' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día reportado', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-drilldownuser' AND qc.code = 'dia_reportado' AND dq.code = 'rpt-adm-drilldownuser' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cuenta', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-drilldownuser' AND qc.code = 'username' AND dq.code = 'rpt-adm-drilldownuser' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Mensaje de registro', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-drilldownuser' AND qc.code = 'log_message' AND dq.code = 'rpt-adm-drilldownuser' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-drilldownuser' AND qc.code = 'name' AND dq.code = 'rpt-adm-drilldownuser' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Diferencia días', rc.type = 5, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-drilldownuser' AND qc.code = 'diferencia_dias' AND dq.code = 'rpt-adm-drilldownuser' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = '# veces', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlytimes' AND qc.code = 'times' AND dq.code = 'rpt-adm-correctlytimes' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Valor', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlytimes' AND qc.code = 'ts_value' AND dq.code = 'rpt-adm-correctlytimes' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlytimes' AND qc.code = 'name' AND dq.code = 'rpt-adm-correctlytimes' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cuenta', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlytimes' AND qc.code = 'username' AND dq.code = 'rpt-adm-correctlytimes' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangepercentaje' AND qc.code = 'Nombre' AND dq.code = 'rpt-adm-rangepercentaje' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = '% Por Proyecto', rc.type = 6, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangepercentaje' AND qc.code = 'PercPorProyecto' AND dq.code = 'rpt-adm-rangepercentaje' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs. del Proyecto', rc.type = 5, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangepercentaje' AND qc.code = 'HrsdelProyecto' AND dq.code = 'rpt-adm-rangepercentaje' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangepercentaje' AND qc.code = 'Cliente' AND dq.code = 'rpt-adm-rangepercentaje' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Total Todos Proyectos', rc.type = 5, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangepercentaje' AND qc.code = 'TotalTodosProyectos' AND dq.code = 'rpt-adm-rangepercentaje' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangepercentaje' AND qc.code = 'Proyecto' AND dq.code = 'rpt-adm-rangepercentaje' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Projecto', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-weeklyproject' AND qc.code = 'Projecto' AND dq.code = 'rpt-adm-weeklyproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-weeklyproject' AND qc.code = 'Cliente' AND dq.code = 'rpt-adm-weeklyproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs Acum de la Semana', rc.type = 5, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-weeklyproject' AND qc.code = 'HrsAcumDeLaSemana' AND dq.code = 'rpt-adm-weeklyproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Id Proj', rc.type = 4, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-weeklyproject' AND qc.code = 'IdProj' AND dq.code = 'rpt-adm-weeklyproject' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Semana Reportado (Lunes 1er Día)', rc.type = 1, rc.grid_order = 8
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'SemanaReportadoLunes1erDia' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Hrs.', rc.type = 5, rc.grid_order = 10
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Hrs' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre de Compañía', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'displaycompany' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Comentario', rc.type = 1, rc.grid_order = 9
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Comentario' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Departamento', rc.type = 1, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Departamento' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día Reportado', rc.type = 1, rc.grid_order = 7
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'DiaReportado' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Compañía', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Compania' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Nombre' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Tarea', rc.type = 1, rc.grid_order = 6
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Tarea' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Proyecto', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Proyecto' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cliente', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Cliente' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Fecha', rc.type = 1, rc.grid_order = 11
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-bytaskdetail' AND qc.code = 'Fecha' AND dq.code = 'rpt-adm-bytaskdetail' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangeperiodv1' AND qc.code = 'name' AND dq.code = 'rpt-adm-rangeperiodv1' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Horas por día', rc.type = 5, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangeperiodv1' AND qc.code = 'hrs_by_day' AND dq.code = 'rpt-adm-rangeperiodv1' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cuenta', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangeperiod' AND qc.code = 'username' AND dq.code = 'rpt-adm-rangeperiod' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Fecha', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangeperiod' AND qc.code = 'no_date' AND dq.code = 'rpt-adm-rangeperiod' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangeperiod' AND qc.code = 'name' AND dq.code = 'rpt-adm-rangeperiod' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre de fecha', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangeperiod' AND qc.code = 'date' AND dq.code = 'rpt-adm-rangeperiod' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Horas por día', rc.type = 5, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-rangeperiod' AND qc.code = 'hrs_by_day' AND dq.code = 'rpt-adm-rangeperiod' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Diferencia días', rc.type = 4, rc.grid_order = 2
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlydetails' AND qc.code = 'diferencia_dias' AND dq.code = 'rpt-adm-correctlydetails' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Mensaje de registro', rc.type = 1, rc.grid_order = 5
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlydetails' AND qc.code = 'log_message' AND dq.code = 'rpt-adm-correctlydetails' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Nombre', rc.type = 1, rc.grid_order = 1
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlydetails' AND qc.code = 'name' AND dq.code = 'rpt-adm-correctlydetails' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Fecha de creación', rc.type = 1, rc.grid_order = 4
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlydetails' AND qc.code = 'fecha_creacion' AND dq.code = 'rpt-adm-correctlydetails' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Cuenta', rc.type = 1, rc.grid_order = 0
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlydetails' AND qc.code = 'username' AND dq.code = 'rpt-adm-correctlydetails' AND r.module = 'timesheet'
UPDATE rc
SET rc.description = 'Día reportado', rc.type = 1, rc.grid_order = 3
FROM report_column rc
INNER JOIN report r ON r.report_id = rc.report_id
INNER JOIN query_column qc ON qc.query_column_id = rc.query_column_id
INNER JOIN database_query dq ON dq.database_query_id = r.database_query_id
WHERE r.code = 'rpt-adm-correctlydetails' AND qc.code = 'dia_reportado' AND dq.code = 'rpt-adm-correctlydetails' AND r.module = 'timesheet'
