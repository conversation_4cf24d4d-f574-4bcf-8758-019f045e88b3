
INSERT INTO pending_type  (pending_type_id, status, default_mail_template_id, reassign_mail_template_id, climb_position_mail_template, notify_boss_mail_template_id, climb_position_hours_wait, notify_boss_hours_wait, menu_url, url, bnext_module, description, code)
SELECT 
        '-47' pending_type_id, '1' status, '-1' default_mail_template_id, '-1' reassign_mail_template_id, '-1' climb_position_mail_template, '-1' notify_boss_mail_template_id, 
        '2688' climb_position_hours_wait,           -- 1 mes
        '672' notify_boss_hours_wait, 		-- 1 semana
        'view/v.menu.view?seccion=Quejas' menu_url, 
        'view/v.my.complaint.view?tab=3&status=1' url, 
        'complaint' bnext_module,
        'Quejas por asignar responsable' description,
        'q-complaint-to-assign' code
FROM dual WHERE -47 NOT IN (SELECT pending_type_id FROM pending_type); 

INSERT INTO pending_type  (pending_type_id, status, default_mail_template_id, reassign_mail_template_id, climb_position_mail_template, notify_boss_mail_template_id, climb_position_hours_wait, notify_boss_hours_wait, menu_url, url, bnext_module, description, code)
SELECT 
        '-48' pending_type_id, '1' status, '-1' default_mail_template_id, '-1' reassign_mail_template_id, '-1' climb_position_mail_template, '-1' notify_boss_mail_template_id, 
        '2688' climb_position_hours_wait,           -- 1 mes
        '672' notify_boss_hours_wait, 		-- 1 semana
        'view/v.menu.view?seccion=Quejas' menu_url, 
        'view/v.my.complaint.view?status=2' url, 
        'complaint' bnext_module,
        'Quejas por dar respuesta' description,
        'q-complaint-to-attend' code
FROM dual WHERE -48 NOT IN (SELECT pending_type_id FROM pending_type); 

INSERT INTO pending_type  (pending_type_id, status, default_mail_template_id, reassign_mail_template_id, climb_position_mail_template, notify_boss_mail_template_id, climb_position_hours_wait, notify_boss_hours_wait, menu_url, url, bnext_module, description, code)
SELECT 
        '-49' pending_type_id, '1' status, '-1' default_mail_template_id, '-1' reassign_mail_template_id, '-1' climb_position_mail_template, '-1' notify_boss_mail_template_id, 
        '2688' climb_position_hours_wait,           -- 1 mes
        '672' notify_boss_hours_wait, 		-- 1 semana
        'view/v.menu.view?seccion=Quejas' menu_url, 
        'view/v.my.complaint.view?tab=1&status=3' url, 
        'complaint' bnext_module,
        'Quejas por verificar respuesta' description,
        'q-complaint-to-verify' code
FROM dual WHERE -49 NOT IN (SELECT pending_type_id FROM pending_type);    


INSERT INTO pending_type  (pending_type_id, status, default_mail_template_id, reassign_mail_template_id, climb_position_mail_template, notify_boss_mail_template_id, climb_position_hours_wait, notify_boss_hours_wait, menu_url, url, bnext_module, description, code)
SELECT 
        '-50' pending_type_id, '1' status, '-1' default_mail_template_id, '-1' reassign_mail_template_id, '-1' climb_position_mail_template, '-1' notify_boss_mail_template_id, 
        '2688' climb_position_hours_wait,           -- 1 mes
        '672' notify_boss_hours_wait, 		-- 1 semana
        'view/v.menu.view?seccion=Quejas' menu_url, 
        'view/v.my.complaint.view?tab=2&status=5' url, 
        'complaint' bnext_module,
        'Quejas atendidas por evaluar efectividad' description,
        'q-complaint-to-evaluate' code
FROM dual WHERE -50 NOT IN (SELECT pending_type_id FROM pending_type);  