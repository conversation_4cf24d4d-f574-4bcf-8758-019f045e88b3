<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.9"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.9
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.9.xsd">
    <changeSet id="p1" author="blocknetworks (generated)" dbms="mssql">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <sql>
            if object_id('dbo.replaceUser', 'p') is not null
                DROP PROCEDURE dbo.replaceUser;
        </sql>
        <sql splitStatements="false">
            CREATE PROCEDURE [dbo].[replaceUser]  @wrong varchar(max), @correct varchar(max)
                AS
                Print 'Replace ' + @wrong + ' with ' + @correct
                /*update history access*/
                update tblhistorialacceso 
                set intusuarioid = (select USER_ID from users where account = ''+@wrong+'')
                where intusuarioid = (select USER_ID from users where account = ''+@correct+'')

                /*removes correct from table*/
                delete from users
                where account = @correct

                /*update wrong username with correct*/
                update users
                set code =@correct, 
                account = @correct, 
                mail = REPLACE(mail,@wrong,@correct)
                where account = ''+@wrong+''

        </sql>
    </changeSet>
</databaseChangeLog>