<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.9"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.9
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.9.xsd">
    <changeSet id="p3" author="blocknetworks (generated)"  dbms="mssql">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <sql>
            if object_id('dbo.leadWithZeros', 'FN') is not null
                DROP Function dbo.leadWithZeros;
        </sql>
        <sql splitStatements="false">
            
            
            create FUNCTION [dbo].[leadWithZeros](@num integer)
                RETURNS varchar(20) 
                AS
                BEGIN
                        DECLARE @nOrig varchar(20),@nNew varchar(20)
                        SELECT @nOrig = CONVERT(VARCHAR,@num) 
                        SELECT @nNew = RIGHT('000'+@nOrig ,4)
                        IF LEN(@nOrig)>=  LEN(@nNew)
                        BEGIN
                                SELECT @nNew = @nOrig
                        END
                        return @nNew
                END;
        </sql>
    </changeSet>
</databaseChangeLog>