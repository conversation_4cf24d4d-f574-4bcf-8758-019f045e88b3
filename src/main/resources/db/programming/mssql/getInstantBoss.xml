<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog/1.9"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog/1.9
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-1.9.xsd">
    <changeSet id="p3" author="blocknetworks (generated)" dbms="mssql" >
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <sql>
            if object_id('dbo.getInstantBoss', 'FN') is not null
                DROP Function dbo.getInstantBoss;
        </sql>
        <sql splitStatements="false">
            create FUNCTION [dbo].[getInstantBoss](@position bigint)
                RETURNS Integer AS
                BEGIN
                DECLARE @boss bigint = null,@user bigint
                WHILE 1=1
                BEGIN
                        SELECT TOP 1 @boss = jefe_id FROM puesto WHERE puesto_id = @position--saca el posible puesto
                        IF @position IS NULL
                        BEGIN
                                RETURN NULL
                        END
                        SELECT TOP 1 @user = up.usuario_id FROM usuario_puesto up  WHERE up.puesto_id = @boss
                        if @user IS NOT NULL
                        BEGIN
                                return @boss
                        END
                        SELECT @position = @boss

                END
                RETURN null
                END;
        </sql>
    </changeSet>
</databaseChangeLog>