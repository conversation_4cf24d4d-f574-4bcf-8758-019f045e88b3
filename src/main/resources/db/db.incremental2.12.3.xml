<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.3.xsd">
    <property name="now" value="sysdate" dbms="oracle"/>
    <property name="now" value="current_timestamp" dbms="mssql"/>
    <property name="now" value="now()" dbms="mysql"/>
    <property name="now" value="now()" dbms="postgresql"/>
    <changeSet author="<PERSON> Quintanilla Flores" 
               id="Jambul-********-20170609-EQUINTANILLA-1">
        <tagDatabase tag="********"/>
    </changeSet>
    <changeSet author="<PERSON>" 
               id="Jambul-********-20170609-EQUINTANILLA-2">
        <comment>
            Se agrega columna de id unico de documentos
        </comment> 
        <addColumn tableName="document">
            <column name="master_id" type="uniqueidentifier"/>
        </addColumn>
        <addColumn tableName="request">
            <column name="document_master_id" type="uniqueidentifier"/>
        </addColumn>
        <addColumn tableName="document_traceability">
            <column name="master_id" type="uniqueidentifier"/>
        </addColumn>
        <createIndex indexName="idx_document_master_id" tableName="document" unique="false">
            <column name="master_id"/>
        </createIndex>
        <createIndex indexName="idx_request_master_id" tableName="request" unique="false">
            <column name="document_master_id"/>
        </createIndex>
        <createIndex indexName="idx_document_trace_id" tableName="document_traceability" unique="false">
            <column name="master_id"/>
        </createIndex>
        <createIndex indexName="idx_document_master_v_id" tableName="document" unique="false">
            <column name="master_id"/>
            <column name="version"/>
        </createIndex>
        <createIndex indexName="idx_request_master_v_id" tableName="request" unique="false">
            <column name="document_master_id"/>
            <column name="version"/>
        </createIndex>
        <createIndex indexName="idx_document_trace_v_id" tableName="document_traceability" unique="false">
            <column name="master_id"/>
            <column name="version"/>
        </createIndex>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-********-20170609-EQUINTANILLA-3">
        <comment>
            Se genera indice de master_id y documento con clave documento
        </comment> 
        <sql splitStatements="false">
            <![CDATA[    
                CREATE NONCLUSTERED INDEX idx_document_master_status
                ON document(master_id,int_estado)
                INCLUDE (vch_clave);
                
                CREATE NONCLUSTERED INDEX idx_doc_trace_code_master
                ON document_traceability (document_code, master_id);
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-********-20170609-EQUINTANILLA-3-1">
        <comment>
            Se genera indice de type y clave documento en solicitud
        </comment>
        <sql splitStatements="false">
            <![CDATA[
                CREATE NONCLUSTERED INDEX [idx_request_type_document_code]
                ON [dbo].[request] ([type])
                INCLUDE ([id],[document_code],[document_id])
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170609-EQUINTANILLA-4">
        <validCheckSum>8:b17b7b029330fee3fec1f94005e431c4</validCheckSum>
        <comment>
            Se genera valor default unico de columna master_id por documento
        </comment> 
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                WITH req AS (
                    SELECT
                     NEWID() as master_id,
                     r1.document_code as document_code,
                     r1.id as request_id1,
                     r2.id as request_id2, 
                     r1.document_id as document_id1, 
                     r2.document_id as document_id2, 
                     '__CNCL____CNCL__' + r1.document_code + '-' + CAST(r2.id AS NVARCHAR) +  '-' + CAST(r1.id AS NVARCHAR) as cancelled_code_1,
                     '__CNCL____CNCL__' + r1.document_code + '-' + CAST(r2.id AS NVARCHAR) as cancelled_code_2,
                     '__CNCL____CNCL__' + r1.document_code + '-' + + CAST(r1.id AS NVARCHAR) as cancelled_code_3,
                     '__CNCL__' + r1.document_code + '-' + CAST(r2.id AS NVARCHAR) as dis_cancelled_code_1,
                     '__CNCL__' + r1.document_code + '-' + CAST(r1.id AS NVARCHAR) as dis_cancelled_code_2
                    FROM  request r1
                    INNER JOIN request r2
                    ON r1.type IN (1, 2, 3, 4) 
                    AND r2.type IN (1, 2, 3, 4)
                    AND  r2.document_code like '__CNCL____CNCL__%' 
                    AND r2.document_code = '__CNCL____CNCL__' + r1.document_code + '-' + CAST(r2.id AS NVARCHAR) +  '-' + CAST(r1.id AS NVARCHAR)
                    
                )
                SELECT 
                    master_id, 
                    document_code,
                    request_id1,
                    request_id2,
                    document_id1,
                    document_id2, 
                    cancelled_code_1, 
                    cancelled_code_2, 
                    cancelled_code_3,
                    dis_cancelled_code_1,
                    dis_cancelled_code_2
                FROM req
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)
                DECLARE @requestId1 bigint
                DECLARE @requestId2 bigint
                DECLARE @documentId1 bigint
                DECLARE @documentId2 bigint
                DECLARE @cancelledCode1 varchar(255)
                DECLARE @cancelledCode2 varchar(255)
                DECLARE @cancelledCode3 varchar(255)
                DECLARE @disCancelledCode1 varchar(255)
                DECLARE @disCancelledCode2 varchar(255)

                OPEN db_cursor
                FETCH NEXT FROM db_cursor 
                INTO 
                    @masterId,
                    @documentCode,
                    @requestId1,
                    @requestId2,
                    @documentId1,
                    @documentId2, 
                    @cancelledCode1,
                    @cancelledCode2,
                    @cancelledCode3,
                    @disCancelledCode1,
                    @disCancelledCode2
                WHILE @@FETCH_STATUS = 0

                BEGIN

                    UPDATE req SET req.document_master_id = @masterId
                    FROM request req
                    WHERE req.document_master_id IS NULL AND ( 
                        req.document_code = @documentCode
                        OR req.id = @requestId1
                        OR req.id = @requestId2
                        OR req.document_code = @cancelledCode1
                        OR req.document_code = @cancelledCode2
                        OR req.document_code = @cancelledCode3
                        OR req.document_code = @disCancelledCode1
                        OR req.document_code = @disCancelledCode2
                        OR req.document_id = @documentId1
                        OR req.document_id = @documentId2
                    )
                    AND req.type IN (1, 2, 3, 4)


                    UPDATE doc SET doc.master_id = @masterId
                    FROM document doc
                    WHERE doc.master_id IS NULL AND ( 
                        doc.vch_clave = @documentCode
                        OR doc.request_id = @requestId1
                        OR doc.request_id = @requestId2
                        OR doc.vch_clave = @cancelledCode1
                        OR doc.vch_clave = @cancelledCode2
                        OR doc.vch_clave = @cancelledCode3
                        OR doc.vch_clave = @disCancelledCode1
                        OR doc.vch_clave = @disCancelledCode2
                        OR doc.id = @documentId1
                        OR doc.id = @documentId2
                    )

                    FETCH NEXT FROM db_cursor 
                        INTO 
                            @masterId,
                            @documentCode,
                            @requestId1,
                            @requestId2,
                            @documentId1,
                            @documentId2, 
                            @cancelledCode1,
                            @cancelledCode2,
                            @cancelledCode3,
                            @disCancelledCode1,
                            @disCancelledCode2

                END
                CLOSE db_cursor
                DEALLOCATE db_cursor
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                    WITH doc AS (
                        SELECT
                            NEWID() as master_id,
                            vch_clave as document_code,
                            ROW_NUMBER() OVER(PARTITION BY vch_clave ORDER BY vch_clave) ct
                        FROM document
                        WHERE int_estado IN (0, 1, 3)
                        AND master_id IS NULL
                    )
                    SELECT master_id, document_code
                    FROM doc WHERE ct = 1
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)

                OPEN db_cursor
                FETCH NEXT FROM db_cursor INTO @masterId, @documentCode
                WHILE @@FETCH_STATUS = 0

                BEGIN
                
                    UPDATE doc
                    SET doc.master_id = @masterId
                    FROM document doc
                    INNER JOIN request req
                    ON doc.id = req.document_id
                    AND req.document_master_id IS NULL
                    AND doc.vch_clave = @documentCode

                    UPDATE doc
                    SET doc.master_id = @masterId
                    FROM document doc
                    INNER JOIN request req
                    ON doc.request_id = req.id
                    WHERE req.document_master_id IS NULL
                    AND doc.vch_clave = @documentCode
                    
                    UPDATE req
                    SET req.document_master_id = @masterId
                    FROM request req
                    INNER JOIN document doc
                    ON doc.id = req.document_id
                    AND req.document_master_id IS NULL
                    AND doc.vch_clave = @documentCode

                    UPDATE req
                    SET req.document_master_id = @masterId
                    FROM request req
                    INNER JOIN document doc
                    ON doc.request_id = req.id
                    WHERE req.document_master_id IS NULL
                    AND doc.vch_clave = @documentCode
                
                    UPDATE request 
                    SET document_master_id = @masterId
                    WHERE document_code = '__CNCLR__' + @documentCode
                    AND document_master_id IS NULL

                    FETCH NEXT FROM db_cursor INTO @masterId, @documentCode

                END
                CLOSE db_cursor
                DEALLOCATE db_cursor            
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                    WITH doc AS (
                        SELECT
                            master_id as master_id,
                            vch_clave as document_code,
                            ROW_NUMBER() OVER(PARTITION BY master_id, vch_clave ORDER BY vch_clave) ct
                        FROM document
                        WHERE int_estado = -2
                        AND master_id IS NULL
                    )
                    SELECT 
                       CASE WHEN master_id IS NULL THEN NEWID() ELSE master_id END as master_id,
                       document_code
                    FROM doc
                    WHERE ct = 1
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)

                OPEN db_cursor
                FETCH NEXT FROM db_cursor INTO @masterId, @documentCode
                WHILE @@FETCH_STATUS = 0

                BEGIN
                    UPDATE document 
                    SET master_id = @masterId 
                    WHERE int_estado = -2 
                    AND vch_clave = @documentCode
                    AND master_id IS NULL
                    
                    UPDATE request 
                    SET document_master_id = @masterId
                    WHERE document_code = @documentCode
                    AND document_master_id IS NULL
                    
                    FETCH NEXT FROM db_cursor INTO @masterId, @documentCode
                END
                CLOSE db_cursor
                DEALLOCATE db_cursor  
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                    SELECT 
                       CASE WHEN master_id IS NULL THEN NEWID() ELSE master_id END as master_id,
                       document_code,
                       request_id
                    FROM (
                        SELECT
                            d2.master_id as master_id,
                            REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '') as document_code,
                            r.id as request_id,
                            ROW_NUMBER() OVER(
                               PARTITION BY 
                                  d.vch_clave, 
                                  REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '')
                              ORDER BY d.vch_clave
                            ) ct
                        FROM request r
                        INNER JOIN document d
                        ON d.int_estado = 2
                        AND d.master_id IS NULL
                        AND REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '') = r.document_code
                        LEFT JOIN document d2
                        ON d2.int_estado = -2
                        AND d2.vch_clave LIKE '__CNCL____DSCT__%'
                        AND d2.vch_clave = '__CNCL____DSCT__' + REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '')
                        UNION
                        SELECT
                            d2.master_id as master_id,
                            REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '') as document_code,
                            r.id as request_id,
                            ROW_NUMBER() OVER(
                               PARTITION BY 
                                  d.vch_clave, 
                                  REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '')
                               ORDER BY d.vch_clave
                            ) ct
                        FROM request r
                        INNER JOIN document d
                        ON d.int_estado = 2
                        AND d.master_id IS NULL
                        AND d.vch_clave = r.document_code
                        LEFT JOIN document d2
                        ON d2.int_estado = -2
                        AND d2.vch_clave LIKE '__CNCL____DSCT__%'
                        AND d2.vch_clave = '__CNCL____DSCT__' + REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '')
                        WHERE REPLACE(REPLACE(d.vch_clave, '__CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '') NOT like '%-' + CAST(r.id AS nvarchar)
                    ) doc
                    WHERE doc.ct = 1
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)
                DECLARE @requestId bigint

                OPEN db_cursor
                FETCH NEXT FROM db_cursor INTO @masterId, @documentCode, @requestId
                WHILE @@FETCH_STATUS = 0

                BEGIN
                    UPDATE doc
                    SET doc.master_id = @masterId 
                    FROM document doc
                    WHERE doc.int_estado = 2
                    AND doc.vch_clave LIKE '__CNCL__%'
                    AND doc.vch_clave = '__CNCL__' + @documentCode + '-' + CAST(@requestId AS nvarchar)
                    AND doc.master_id IS NULL
                    
                    UPDATE req
                    SET req.document_master_id = @masterId 
                    FROM request req
                    WHERE req.document_code LIKE '__CNCL__%'
                    AND req.document_code = '__CNCL__' + @documentCode + '-' + CAST(@requestId AS nvarchar)
                    AND req.document_master_id IS NULL
               
                    UPDATE request 
                    SET document_master_id = @masterId
                    WHERE id = @requestId
                    AND document_master_id IS NULL
                    
                    FETCH NEXT FROM db_cursor INTO @masterId, @documentCode, @requestId
                END
                CLOSE db_cursor
                DEALLOCATE db_cursor            
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                WITH req AS (
                    SELECT 
                        NEWID() as master_id,
                        r.document_code
                    FROM request r
                    WHERE r.document_master_id IS NULL
                    AND r.document_code LIKE '__CNCL__%'
                    AND r.type IN (1, 2, 3, 4)
                    GROUP BY r.document_code
                )
                SELECT 
                    master_id,
                    document_code
                FROM req
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)

                OPEN db_cursor
                FETCH NEXT FROM db_cursor 
                INTO 
                    @masterId,
                    @documentCode
                WHILE @@FETCH_STATUS = 0

                BEGIN

                    UPDATE req SET req.document_master_id = @masterId
                    FROM request req
                    WHERE req.document_master_id IS NULL AND req.document_code = @documentCode
                    AND req.type IN (1, 2, 3, 4)


                    UPDATE doc SET doc.master_id = @masterId
                    FROM document doc
                    WHERE doc.master_id IS NULL AND doc.vch_clave = @documentCode

                    FETCH NEXT FROM db_cursor 
                        INTO 
                            @masterId,
                            @documentCode
                END
                CLOSE db_cursor
                DEALLOCATE db_cursor
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                    SELECT
                       CASE WHEN master_id IS NULL THEN NEWID() ELSE master_id END as master_id,
                       document_code
                    FROM (
                        SELECT
                            master_id as master_id,
                            vch_clave as document_code,
                            ROW_NUMBER() OVER(
                               PARTITION BY 
                                  master_id, 
                                  vch_clave
                              ORDER BY vch_clave
                            ) ct
                        FROM document
                        WHERE int_estado IN  (-1, -3)
                        AND master_id IS NULL
                    ) doc
                    WHERE doc.ct = 1
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)

                OPEN db_cursor
                FETCH NEXT FROM db_cursor INTO @masterId, @documentCode
                WHILE @@FETCH_STATUS = 0

                BEGIN
                
                    UPDATE request 
                    SET document_master_id = @masterId
                    WHERE document_code = @documentCode
                    AND document_master_id IS NULL
                    
                    UPDATE document 
                    SET master_id = @masterId
                    WHERE vch_clave = @documentCode
                    AND master_id IS NULL
                    
                    FETCH NEXT FROM db_cursor INTO @masterId, @documentCode

                END
                CLOSE db_cursor
                DEALLOCATE db_cursor
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
               UPDATE req 
               SET req.document_master_id = NEWID()
               FROM request req
               WHERE req.document_master_id IS NULL
               AND req.document_id IS NULL
               AND req.survey_id IS NULL
               AND req.outstanding_surveys_id IS NULL
               AND req.int_estado = 7
               AND req.type = 1
               AND NOT EXISTS (
                  SELECT 1
                  FROM document doc
                  WHERE doc.vch_clave = req.document_code
                  AND doc.request_id = req.id
               )
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
               UPDATE doc 
               SET doc.master_id = NEWID()
               FROM document doc
               INNER JOIN document_type d_t
               ON d_t.document_type_id = document_type
               AND d_t.document_controlled_type = 'uncontrolled'
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[                                  
                UPDATE req
                SET req.document_master_id = doc.master_id
                FROM request req
                INNER JOIN document doc
                ON doc.id = req.document_id
                AND req.document_master_id IS NULL 
                AND doc.master_id IS NOT NULL
                  
                UPDATE req
                SET req.document_master_id = doc.master_id
                FROM request req
                INNER JOIN document doc
                ON doc.request_id = req.id
                WHERE req.document_master_id IS NULL 
                AND doc.master_id IS NOT NULL              
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                UPDATE req
                SET req.document_master_id = NEWID()
                FROM request req
                WHERE req.int_estado IN (3, 5, 6, 9) 
                AND req.document_master_id IS NULL
                AND req.type = 1
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                UPDATE req
                SET req.document_master_id = doc.master_id
                FROM request req
                INNER JOIN document doc
                ON doc.vch_clave = req.document_code
                WHERE req.document_master_id IS NULL 
                AND doc.master_id IS NOT NULL  
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-********-20170609-EQUINTANILLA-5">
        <validCheckSum>7:a9ed35a591574879f3baf41b20592b8a</validCheckSum>
        <comment>
            Se genera valor default unico de columna master_id por documento
        </comment> 
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                    SELECT
                        NEWID() as master_id,
                        REPLACE(REPLACE(d.vch_clave, '__CNCL____CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '') as document_code,
                        MAX(r.id) as request_id
                    FROM request r
                    INNER JOIN document d
                    ON d.int_estado = 2
                    AND d.master_id IS NULL
                    AND REPLACE(REPLACE(d.vch_clave, '__CNCL____CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '') = r.document_code
                    GROUP BY d.vch_clave, REPLACE(REPLACE(d.vch_clave, '__CNCL____CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '')
                    UNION
                    SELECT
                        NEWID() as master_id,
                        REPLACE(REPLACE(d.vch_clave, '__CNCL____CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '') as document_code,
                        MAX(r.id) as request_id
                    FROM request r
                    INNER JOIN document d
                    ON d.int_estado = 2
                    AND d.master_id IS NULL
                    AND d.vch_clave = r.document_code
                    GROUP BY d.vch_clave, REPLACE(REPLACE(d.vch_clave, '__CNCL____CNCL__',  ''), '-' + CAST(r.id AS nvarchar), '')
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)
                DECLARE @requestId bigint

                OPEN db_cursor
                FETCH NEXT FROM db_cursor INTO @masterId, @documentCode, @requestId
                WHILE @@FETCH_STATUS = 0

                BEGIN
                    UPDATE doc
                    SET doc.master_id = @masterId 
                    FROM document doc
                    WHERE doc.int_estado = 2
                    AND doc.vch_clave = '__CNCL____CNCL__' + @documentCode + '-' + CAST(@requestId AS nvarchar) + '-' + CAST(@requestId AS nvarchar)
                    AND doc.master_id IS NULL
                    
                    UPDATE req
                    SET req.document_master_id = @masterId 
                    FROM request req
                    WHERE req.document_code = '__CNCL____CNCL__' + @documentCode + '-' + CAST(@requestId AS nvarchar) + '-' + CAST(@requestId AS nvarchar)
                    AND req.document_master_id IS NULL
               
                    UPDATE request 
                    SET document_master_id = @masterId
                    WHERE id = @requestId
                    AND document_master_id IS NULL
                    
                    FETCH NEXT FROM db_cursor INTO @masterId, @documentCode, @requestId
                END
                CLOSE db_cursor
                DEALLOCATE db_cursor            
            ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                DECLARE db_cursor CURSOR FOR
                WITH req AS (
                    SELECT
                    r1.document_master_id as master_id,
                    r1.document_code as document_code,
                    r1.id as request_id1,
                    d1.id as request_id2, 
                    r1.document_id as document_id1, 
                    d1.id as document_id2
                   FROM  request r1
                   INNER JOIN document d1
                   ON d1.vch_clave = r1.document_code
                   AND d1.master_id IS NULL
                   WHERE r1.type IN (1, 2, 3, 4)
                   AND r1.document_master_id IS NOT NULL
                   UNION
                   SELECT
                    r1.document_master_id as master_id,
                    r1.document_code as document_code,
                    r1.id as request_id1,
                    d1.id as request_id2, 
                    r1.document_id as document_id1, 
                    d1.id as document_id2
                   FROM  request r1
                   INNER JOIN document d1
                   ON d1.id = r1.document_id
                   AND d1.master_id IS NULL
                   WHERE r1.type IN (1, 2, 3, 4)
                   AND r1.document_master_id IS NOT NULL
                )
                SELECT 
                    master_id, 
                    document_code,
                    request_id1,
                    request_id2,
                    document_id1,
                    document_id2
                FROM req
                DECLARE @masterId uniqueidentifier
                DECLARE @documentCode varchar(255)
                DECLARE @requestId1 bigint
                DECLARE @requestId2 bigint
                DECLARE @documentId1 bigint
                DECLARE @documentId2 bigint

                OPEN db_cursor
                FETCH NEXT FROM db_cursor 
                INTO 
                    @masterId,
                    @documentCode,
                    @requestId1,
                    @requestId2,
                    @documentId1,
                    @documentId2
                WHILE @@FETCH_STATUS = 0

                BEGIN

                    UPDATE req SET req.document_master_id = @masterId
                    FROM request req
                    WHERE req.document_master_id IS NULL AND ( 
                        req.document_code = @documentCode
                        OR req.id = @requestId1
                        OR req.id = @requestId2
                        OR req.document_id = @documentId1
                        OR req.document_id = @documentId2
                    )
                    AND req.type IN (1, 2, 3, 4)


                    UPDATE doc SET doc.master_id = @masterId
                    FROM document doc
                    WHERE doc.master_id IS NULL AND ( 
                        doc.vch_clave = @documentCode
                        OR doc.request_id = @requestId1
                        OR doc.request_id = @requestId2
                        OR doc.id = @documentId1
                        OR doc.id = @documentId2
                    )

                    FETCH NEXT FROM db_cursor 
                        INTO 
                            @masterId,
                            @documentCode,
                            @requestId1,
                            @requestId2,
                            @documentId1,
                            @documentId2

                END
                CLOSE db_cursor
                DEALLOCATE db_cursor
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170809-EQUINTANILLA-1">
        <comment>
            Se eliminan datos de trazabilidad de documentos
        </comment>
        <sql>
        <![CDATA[
            DELETE FROM document_traceability_user
            DELETE FROM document_traceability
        ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170809-EQUINTANILLA-2">
        <validCheckSum>7:10411703fe93619c6c701b3989d4d066</validCheckSum>
        <comment>
            Se agrega cargan datos de trazabilidad de documentos
        </comment>
        <sql>
        <![CDATA[
            INSERT INTO document_traceability (
               document_traceability_id, 
               document_code, 
               version, 
               request_type,
               reason, 
               created_on,
               verified_by,
               master_id
            )
            SELECT 
               req.id as document_traceability_id,
               doc.vch_clave AS document_code,
               doc.version AS version,
               req.type AS type,
               req.reazon AS reason,
               MAX(aut.tspfechahoraautorizacion) AS created_on,
               ver.user_id AS verified_by,
               doc.master_id
            FROM document doc
            INNER JOIN document_type dt
               ON dt.document_type_id = doc.document_type
            LEFT JOIN verification ver
               ON doc.request_id = ver.request_id
            LEFT JOIN tblautorizacion aut
               ON doc.id = aut.document_id
                  AND aut.intestado = 3
            INNER JOIN request req
               ON aut.request_id = req.id
            WHERE doc.int_borrado = 0 AND dt.document_controlled_type = 'controlled'
            GROUP BY 
               doc.vch_clave,
               doc.master_id,
               req.id,
               req.type,
               req.reazon,
               ver.user_id,
               doc.version
            UNION
            SELECT 
               req.id as document_traceability_id,
               doc.vch_clave AS document_code,
               doc.version AS version,
               req.type AS type,
               req.reazon AS reason,
               MAX(doc.creation_date) AS created_on,
               ver.user_id AS verified_by,
               doc.master_id
            FROM document doc
            INNER JOIN document_type dt
               ON dt.document_type_id = doc.document_type
            LEFT JOIN verification ver
               ON doc.request_id = ver.request_id
            INNER JOIN request req
               ON doc.request_id = req.id
            WHERE doc.int_borrado = 0 AND dt.document_controlled_type = 'uncontrolled'
            GROUP BY 
               doc.vch_clave,
               doc.master_id,
               req.id,
               req.type,
               req.reazon,
               ver.user_id,
               doc.version
        ]]>
        </sql>
        <sql>
        <![CDATA[
            INSERT INTO document_traceability_user (document_traceability_id, authorized_by, authorized_index)
            SELECT 
               req.id as document_traceability_id,
               aut.intautorizanteid AS authorized_by,
               aut.intindice AS authorized_index
            FROM document doc
            INNER JOIN document_type dt
               ON dt.document_type_id = doc.document_type
            LEFT JOIN verification ver
               ON doc.request_id = ver.request_id
            LEFT JOIN tblautorizacion aut
               ON doc.id = aut.document_id AND aut.intestado = 3
            INNER JOIN request req
               ON aut.request_id = req.id
            WHERE doc.int_borrado = 0 AND dt.document_controlled_type = 'controlled'
            AND aut.intautorizanteid IS NOT NULL
            GROUP BY 
               doc.vch_clave,
               req.id,
               req.type,
               req.reazon,
               ver.user_id,
               doc.version,
               aut.intautorizanteid,
               aut.intindice
        ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170809-EQUINTANILLA-3">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from HIBERNATE_SEQUENCES where SEQUENCE_NAME = 'document_traceability';</sqlCheck>
        </preConditions>
        <comment>
            Se repara valor inicial de document_traceability
        </comment>
        <sql>
            <![CDATA[
                     INSERT INTO HIBERNATE_SEQUENCES (SEQUENCE_NAME, SEQUENCE_NEXT_HI_VALUE)
                     VALUES ('document_traceability', 100);
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170809-EQUINTANILLA-4">
        <comment>
            Se repara valor inicial de document_traceability
        </comment>
        <sql>
            <![CDATA[
                     UPDATE HIBERNATE_SEQUENCES
                     SET SEQUENCE_NEXT_HI_VALUE =
                         (SELECT (((MAX( document_traceability_id )- 100) / 100) + 101) FROM document_traceability )
                     WHERE SEQUENCE_NAME = 'document_traceability';
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-********-20170809-EQUINTANILLA-5">
        <comment>
            Se genera not null a columna master_id por documento
        </comment> 
        <dropIndex tableName="document" indexName="idx_document_master_id"/>        
        <dropIndex tableName="document_traceability" indexName="idx_doc_trace_code_master"/>
        <dropIndex tableName="document" indexName="idx_document_master_status"/>
        <dropIndex tableName="document_traceability" indexName="idx_document_trace_id"/>
        <dropIndex tableName="document" indexName="idx_document_master_v_id"/>
        <dropIndex tableName="document_traceability" indexName="idx_document_trace_v_id"/>
        <addNotNullConstraint tableName="document"
                       columnDataType="uniqueidentifier"
                       columnName="master_id"/>
        <addNotNullConstraint tableName="document_traceability"
                       columnDataType="uniqueidentifier"
                       columnName="master_id"/>
        <createIndex indexName="idx_document_master_id" tableName="document" unique="false">
            <column name="master_id"/>
        </createIndex>
        <createIndex indexName="idx_document_trace_id" tableName="document_traceability" unique="false">
            <column name="master_id"/>
        </createIndex>
        <createIndex indexName="idx_document_master_v_id" tableName="document" unique="false">
            <column name="master_id"/>
            <column name="version"/>
        </createIndex>
        <createIndex indexName="idx_document_trace_v_id" tableName="document_traceability" unique="false">
            <column name="master_id"/>
            <column name="version"/>
        </createIndex>
        <sql splitStatements="false">
            <![CDATA[    
                CREATE NONCLUSTERED INDEX idx_document_master_status
                ON document(master_id,int_estado)
                INCLUDE (vch_clave);
                
                CREATE NONCLUSTERED INDEX idx_doc_trace_code_master
                ON document_traceability (document_code, master_id);
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-********-20170809-EQUINTANILLA-6">
        <comment>
            Se cambia longitud del campo version de tabla document_traceability
        </comment> 
        <dropIndex tableName="document_traceability" indexName="idx_document_trace_v_id"/>
        <modifyDataType tableName="document_traceability" columnName="version" newDataType="nvarchar(50)" />
        <createIndex indexName="idx_document_trace_v_id" tableName="document_traceability" unique="false">
            <column name="master_id"/>
            <column name="version"/>
        </createIndex>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20170908-LLIMAS-1">
        <comment>
            Se agrega columna para definir si la aplicación valida claves de documentos por PLANTA
            o por nivel APLICACIÓN
        </comment>
        <addColumn tableName="settings">
            <column name="document_code_scope" type="smallint" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20170911-LLIMAS-1">
        <comment>
            Se migra tabla de documentos referenciados a estructura de document_master_id
        </comment>
        <createTable tableName="document_reference">
            <column name="document_master_id" type="uniqueidentifier">
                <constraints nullable="false"/>
            </column>
            <column name="node_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addPrimaryKey
            tableName="document_reference"
            columnNames="document_master_id, node_id"
            constraintName="pk_document_reference_id"/>
        <addForeignKeyConstraint constraintName="fk_document_reference_node_id"
                                 baseTableName="document_reference" baseColumnNames="node_id"
                                 referencedTableName="tblnodo" referencedColumnNames="intnodoid" />
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20170911-LLIMAS-2">
        <validCheckSum>7:bc16a3bdf1221127a7b220c6de523e86</validCheckSum>
        <comment>
            Se llenan valores de tabla de documentos referenciados con estructura de document_master_id
        </comment>
        <sql splitStatements="false">
            <![CDATA[    
                INSERT INTO document_reference (
                        document_master_id
                        ,node_id
                        )
                SELECT d.master_id
                        ,r.intnodoid
                FROM tbldocumentosreferenciados r
                JOIN document d ON r.vchclave = d.vch_clave
                LEFT JOIN document_reference re ON re.document_master_id = d.master_id
                        AND re.node_id = r.intnodoid
                WHERE d.int_estado IN (
                                1
                                ,0
                                )
                        AND re.document_master_id IS NULL;
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20170912-LLIMAS-1">
        <comment>
            Se migra tabla de documentos referenciados a estructura de document_master_id
        </comment>
        <dropTable cascadeConstraints="true"
                   tableName="tbldocumentosreferenciados"/>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-********-20170912-EQUINTANILLA-1">
        <comment>
            Se agrega columna master_id a meeting_type
        </comment> 
        <addColumn tableName="meeting_type">
            <column name="document_master_id" type="uniqueidentifier"/>
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-********-20170912-EQUINTANILLA-2">
        <validCheckSum>7:3f90a605524f4125e173e9863182840b</validCheckSum>
        <validCheckSum>7:89cba1bab6d77ffda1a4176f456f759e</validCheckSum>
        <validCheckSum>7:96afd3bdbe1361db6022ce9cbebad10c</validCheckSum>
        <validCheckSum>7:7188a2ff55516421a8845088b725ae80</validCheckSum>
        <comment>
            Se agrega valor default de columna master_id a meeting_type
        </comment> 
        <sql splitStatements="false">
            <![CDATA[    
               WITH doc AS (
                  SELECT
                     master_id as master_id,
                     vch_clave as document_code,
                     ROW_NUMBER() OVER(PARTITION BY master_id, vch_clave ORDER BY vch_clave) ct
                     FROM document
               )
               UPDATE meet
               SET
                  meet.document_master_id = (
                     SELECT doc.master_id
                     FROM doc      
                     WHERE doc.document_code = meet.document_code
                     AND ct = 1
                  )
               FROM meeting_type meet
            ]]>
        </sql>                   
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170912-EQUINTANILLA-3">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from HIBERNATE_SEQUENCES where SEQUENCE_NAME = 'meeting_type';</sqlCheck>
        </preConditions>
        <comment>
            Se repara valor inicial de meeting_type
        </comment>
        <sql>
            <![CDATA[
                     INSERT INTO HIBERNATE_SEQUENCES (SEQUENCE_NAME, SEQUENCE_NEXT_HI_VALUE)
                     VALUES ('meeting_type', 100);
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170912-EQUINTANILLA-4">
        <comment>
            Se repara valor inicial de meeting_type
        </comment>
        <sql>
            <![CDATA[
                     UPDATE HIBERNATE_SEQUENCES
                     SET SEQUENCE_NEXT_HI_VALUE =
                         (SELECT (((MAX( meeting_type_id )- 100) / 100) + 101) FROM meeting_type )
                     WHERE SEQUENCE_NAME = 'meeting_type';
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20170912-LLIMAS-2-0">
        <preConditions onFail="MARK_RAN">
            <indexExists indexName="ix_finding_type_document_survey_id" />
        </preConditions>
        <comment>
            Se elimina index ix_finding_type_document_survey_id
        </comment>
        <dropIndex tableName="finding_type" indexName="ix_finding_type_document_survey_id"></dropIndex>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20170912-LLIMAS-2-1">
        <preConditions onFail="MARK_RAN">
            <indexExists indexName="ix_finding_type_document_id" />
        </preConditions>
        <comment>
            Se elimina index ix_finding_type_document_id
        </comment>
        <dropIndex tableName="finding_type" indexName="ix_finding_type_document_id"></dropIndex>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20170912-LLIMAS-2">
        <validCheckSum>7:b7dc9d88fc47f08d4daf971a585c1002</validCheckSum>
        <comment>
            1. Se agregan columnas para identificar la planta del formulario asignado por tipos de accion
            2. Se eliminan columnas que no se utilizan
            3. Se agregan valores por defecto
        </comment>
        <addColumn tableName="finding_type">
            <column name="document_master_id" type="uniqueidentifier"/>
        </addColumn>
        <dropForeignKeyConstraint baseTableName="finding_type" constraintName="fk_finding_docid"/>
        <dropForeignKeyConstraint baseTableName="finding_type" constraintName="fk_finding_survey_id"/>
        <dropColumn tableName="finding_type">
            <column name="document_id"/>
            <column name="document_survey_id"/>
        </dropColumn>
        <sql>
            <![CDATA[
                WITH docs
                AS (
                    SELECT 
                       d.master_id master_id,
                       d.vch_clave,
                       ROW_NUMBER() OVER(PARTITION BY d.master_id, d.vch_clave ORDER BY d.vch_clave) ct
                    FROM finding_type f
                    JOIN document d ON d.vch_clave = f.document_code
                    WHERE d.int_estado IN (1, 0)
                )
                UPDATE t
                SET t.document_master_id = docs.master_id
                FROM finding_type t
                JOIN docs ON docs.vch_clave = t.document_code
                WHERE t.document_master_id IS NULL
                AND docs.ct = 1
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170913-EQUINTANILLA-1">
        <comment>Se implementa el pendiente de recoger copias controladas</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueComputed="(SELECT MAX(pending_type_id)+1 FROM ape_pending_type)" />
            <column name="code" value="DOCUMENT-TO-PICK-UP-PHYSICAL-COPY" />
            <column name="description" value="Recoger copias controladas"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-********-20170914-EQUINTANILLA-1">
        <comment>
            * Se agrega columna para indicar quien y cuando se recogio una copia controlada
            * Se agrega columna para indicar quien y cuando se descartó una copia controlada
        </comment> 
        <addColumn tableName="receipt_acknowledgment">
            <column name="collected_by" type="bigint"/>
        </addColumn>
        <addColumn tableName="receipt_acknowledgment">
            <column name="collected_at" type="datetime"/>
        </addColumn>
        <addColumn tableName="receipt_acknowledgment">
            <column name="reported_lost_by" type="bigint"/>
        </addColumn>
        <addColumn tableName="receipt_acknowledgment">
            <column name="reported_lost_at" type="datetime"/>
        </addColumn>
        <addForeignKeyConstraint 
            baseTableName="receipt_acknowledgment" 
            baseColumnNames="collected_by" 
            constraintName="fk_rcp_ack_collected" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <addForeignKeyConstraint 
            baseTableName="receipt_acknowledgment" 
            baseColumnNames="reported_lost_by" 
            constraintName="fk_rcp_ack_reported_lost" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-********-20171130-LLIMAS-1">
        <comment>
            Se elimina indice que no permitía dar de alta opciones de catalogos internos que ya estan borrados
        </comment>
        <sql>
            <![CDATA[
                ALTER TABLE internal_doc_catalog_items DROP CONSTRAINT uk_i_doc_catalog_items
            ]]>
        </sql>
        <sql>
            <![CDATA[
                CREATE UNIQUE NONCLUSTERED INDEX uk_i_doc_catalog_items
                ON internal_doc_catalog_items(internal_doc_catalog_id,vch_description)
                WHERE int_deleted = 0
            ]]>
        </sql>
    </changeSet>

    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-*********-20181105-LLIMAS-1">
        <comment>
            Se agrega columna para definir si cualquiera de los siguientes entities
            fueron cargados desde la pantalla de backend.
        </comment>
        <addColumn tableName="files">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>        
        <addColumn tableName="area">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="business_unit">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="business_unit_department">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="tblcatalogo">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="department_process">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="device">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="device_classification">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="device_group">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="device_status">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="device_type">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="document">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="document_type">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="tblnodo">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="organizational_unit">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="puesto">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="priority">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="perfil">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="tblrepositorio">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="request">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="service">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="service_result">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="service_schedule">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="service_type">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="users">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="dynamic_field">
            <column name="creation_type" type="smallint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-*********-20191401-EQUINTANILLA-1">  
        <comment>
            Se agrega configuración de vistas para links de documentos
        </comment>
        <addColumn tableName="document_type">
            <column name="share_max_views" type="numeric(3, 0)" defaultValueNumeric="1"> 
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet> 
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-*********-20191401-EQUINTANILLA-2">  
        <comment>
            Se agrega campos de vistas para links de documentos
        </comment>
        <addColumn tableName="document_link">
            <column name="views" type="numeric(3,0)" defaultValue="0"/>
        </addColumn>
    </changeSet>
    <changeSet author="Alfonso de León Avalos" id="Jambul-*********-20190808-ADELEON-1">
    <comment>
        Si existe borra la función udf_StripHTML en preparación para modificarla.
    </comment> 
    <sql>
        <![CDATA[
                if object_id('dbo.udf_StripHTML', 'FN') is not null
                DROP Function dbo.udf_StripHTML
            ]]>
    </sql>
    </changeSet>
    <changeSet author="Alfonso de León Avalos" id="Jambul-*********-20190808-ADELEON-2">
    <comment>
        Se modifica la función udf_StripHTML para que también cambie letras con acentos. 
    </comment> 
    <sql splitStatements="false">
        <![CDATA[
            CREATE FUNCTION [udf_StripHTML] (@HTMLText VARCHAR(MAX))
            RETURNS varchar(MAX)
            AS
            BEGIN
            DECLARE @Start  int
            DECLARE @End    int
            DECLARE @Length int

            SET @HTMLText = LTRIM(RTRIM(@HTMLText))
            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &amp; with the '&' character (this needs to be done first, as
            -- '&' might be double encoded as '&amp;amp;')
            SET @Start = CHARINDEX('&amp;', @HTMLText)
            SET @End = @Start + 4
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, '&')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&amp;', @HTMLText)
            SET @End = @Start + 4
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &aacute; with the 'á' character
            SET @Start = CHARINDEX('&aacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'á')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&aacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText


            SET @Start = CHARINDEX('&Aacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'Á')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&Aacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &eacute; with the 'é' character
            SET @Start = CHARINDEX('&eacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'é')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&eacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText


            SET @Start = CHARINDEX('&Eacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'É')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&Eacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &iacute; with the 'í' character
            SET @Start = CHARINDEX('&iacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'í')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&iacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            SET @Start = CHARINDEX('&Iacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'Í')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&Iacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText


            -- Replace the HTML entity &oacute; with the 'ó' character
            SET @Start = CHARINDEX('&oacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'ó')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&oacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            SET @Start = CHARINDEX('&Oacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'Ó')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&Oacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &uacute; with the 'ú' character		
            SET @Start = CHARINDEX('&Uacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'Ú')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&Uacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            SET @Start = CHARINDEX('&uacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, 'ú')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&uacute;', @HTMLText COLLATE Latin1_General_CS_AS)
            SET @End = @Start + 7
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &lt; with the '<' character
            SET @Start = CHARINDEX('&lt;', @HTMLText)
            SET @End = @Start + 3
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, '<')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&lt;', @HTMLText)
            SET @End = @Start + 3
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &gt; with the '>' character
            SET @Start = CHARINDEX('&gt;', @HTMLText)
            SET @End = @Start + 3
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, '>')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&gt;', @HTMLText)
            SET @End = @Start + 3
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &amp; with the '&' character
            SET @Start = CHARINDEX('&amp;amp;', @HTMLText)
            SET @End = @Start + 4
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, '&')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&amp;amp;', @HTMLText)
            SET @End = @Start + 4
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the HTML entity &nbsp; with the ' ' character
            SET @Start = CHARINDEX('&nbsp;', @HTMLText)
            SET @End = @Start + 5
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, ' ')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('&nbsp;', @HTMLText)
            SET @End = @Start + 5
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace any <br> tags with a with the ' ' character
            SET @Start = CHARINDEX('<br>', @HTMLText)
            SET @End = @Start + 3
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, ' ')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('<br>', @HTMLText)
            SET @End = @Start + 3
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace any <br/> tags with a with the ' ' character
            SET @Start = CHARINDEX('<br/>', @HTMLText)
            SET @End = @Start + 4
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, ' ')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('<br/>', @HTMLText)
            SET @End = @Start + 4
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace any <br /> tags with with the ' ' character
            SET @Start = CHARINDEX('<br />', @HTMLText)
            SET @End = @Start + 5
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, ' ')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('<br />', @HTMLText)
            SET @End = @Start + 5
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Remove anything between <whatever> tags
            SET @Start = CHARINDEX('<', @HTMLText)
            SET @End = CHARINDEX('>', @HTMLText, CHARINDEX('<', @HTMLText))
            SET @Length = (@End - @Start) + 1

            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, '')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX('<', @HTMLText)
            SET @End = CHARINDEX('>', @HTMLText, CHARINDEX('<', @HTMLText))
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN @HTMLText

            -- Replace the new line \r with the ' ' character
            SET @Start = CHARINDEX(CHAR(13), @HTMLText)
            SET @End = @Start
            SET @Length = (@End - @Start) + 1


            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, ' ')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX(CHAR(13), @HTMLText)
            SET @End = @Start
            SET @Length = (@End - @Start) + 1
            END

            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))

            -- Replace the new line \n with the ' ' character
            SET @Start = CHARINDEX(CHAR(10), @HTMLText)
            SET @End = @Start
            SET @Length = (@End - @Start) + 1


            WHILE (@Start > 0 AND @End > 0 AND @Length > 0) BEGIN
            SET @HTMLText = STUFF(@HTMLText, @Start, @Length, ' ')
            IF LEN(@HTMLText) = 0
            RETURN LTRIM(RTRIM(@HTMLText))
            SET @Start = CHARINDEX(CHAR(10), @HTMLText)
            SET @End = @Start
            SET @Length = (@End - @Start) + 1
            END

            RETURN LTRIM(RTRIM(@HTMLText))

            END
        ]]>
    </sql>
    </changeSet>
    <changeSet author="Alfonso de León Avalos" id="Jambul-*********-20190808-ADELEON-3">
    <comment>
        Se actualiza las columnas de texto plano para situación y consecuencia que pudieran quedar sin valor. 
    </comment> 
    <sql>
        <![CDATA[
            UPDATE tblacciongenerica
            SET plane_text_situation = dbo.udf_StripHTML(txtsituacion),
                plane_text_consequence = dbo.udf_StripHTML(txtconsecuencia)
        ]]>
    </sql>
</changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-*********-20191119-LLIMAS-1">
        <comment>
            Se agrega columna para identificar el tema de la aplicación
        </comment>
        <addColumn tableName="settings">
            <column name="system_theme" type="varchar(255)" defaultValue="vintage">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-*********-20191119-LLIMAS-2">
        <validCheckSum>7:ec0a22bfa09f2d3d5c96424d00f567ee</validCheckSum>
        <validCheckSum>7:fbe18da460aa847ea92b8758541b805e</validCheckSum>
        <validCheckSum>8:b5a997ae63b38224c49dacf7a7adb755</validCheckSum>
        <validCheckSum>8:8c377b04c8557575d15b2586e201b06c</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(*)
                FROM dual
                WHERE EXISTS (
                    SELECT *
                    FROM sys.columns
                    WHERE Name = 'system_secondary_color'
			AND Object_ID = Object_ID('settings')
		)
            </sqlCheck>
        </preConditions>
        <addColumn tableName="settings">
            <column name="system_secondary_color" type="text" defaultValue="#FFA500">
                <constraints nullable="false" />
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-*********-20191119-LLIMAS-3">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="Constellation Brands">select system_id from settings</sqlCheck>
        </preConditions>
        <comment>
            Para Constellation Brands por defecto se utiliza el tema "Modern V2" con sus colores definidos
        </comment>
        <update tableName="settings">
            <column name="system_theme" value="modern"/>
            <column name="system_color" value="#17335f"/>
            <column name="system_secondary_color" value="#fec240"/>
        </update>
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-*********-20191119-LLIMAS-4">
        <comment>
            Se agrega columna para definir el FILE_ID de fondo de la pantalla de inicio
        </comment>
        <addColumn tableName="settings">
            <column name="welcome_bg_id" type="bigint">
            </column>
        </addColumn>
        <addForeignKeyConstraint constraintName="fk_settings_welcome_bg_id"
                                 baseTableName="settings" baseColumnNames="welcome_bg_id"
                                 referencedTableName="files" referencedColumnNames="id" />
    </changeSet>
    <changeSet author="Luis Carlos Limas Álvarez" id="Jambul-*********-20191121-LLIMAS-1">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="#17335f">select system_secondary_color from settings</sqlCheck>
        </preConditions>
        <comment>
            Se actualiza el color secundario predeterminado para bases de datos nuevas
        </comment>
        <update tableName="settings">
            <column name="system_secondary_color" value="#C1C9D4"/>
        </update>
    </changeSet>
    
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-2.12.3.93-20191202-EQUINTANILLA-1">  
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se corrige el estado de llenado de encuestas que iniciaron y se quedaron en estado programada.
        </comment>
        <sql>
        <![CDATA[
           UPDATE pending
           SET pending.status = 2
           FROM poll poll 
           INNER JOIN poll_respondent resp
           ON resp.poll_id = poll.poll_id
           INNER JOIN OUTSTANDING_SURVEYS pending 
           ON pending.OUTSTANDING_SURVEYS_ID = resp.outstanding_surveys_id
           WHERE poll.status = 2
           AND pending.status = 1
        ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-2.12.3.93-20191202-EQUINTANILLA-2">  
        <comment>
            Se agregan campos de llenado a "poll_output"
        </comment>
        <createView viewName="poll_output" replaceIfExists="true">
        <![CDATA[
                SELECT row_number() OVER (
                        ORDER BY a.question_id
                        ) AS poll_output_id
                ,pe.OUTSTANDING_SURVEYS_ID
                ,qt_id id
                ,fld_obj.INT_ORDER orden
                ,a.q + a.row_text AS pregunta
                ,poll.survey_id
                ,qt_id title_id
                ,poll.poll_id
                ,poll.author_id
                ,poll.deleted
                ,poll.dte_start
                ,poll.dte_end
                ,poll.code poll_code
                ,poll.description poll_description
                ,poll.STATUS
                ,prr.respondent_id
                ,resp.first_name
                ,resp.account
                ,resp.description respondent_description
                ,resp.mail
                ,resp.code respondent_code
                ,resp.default_workflow_position
                ,resp.primary_last_name
                ,resp.secondary_last_name
                ,a.ANSWER_ID
                ,dbo.udf_StripHTML(a.vch_answer) AS respuesta_texto
                ,a.vch_texto AS respuesta_opcion
                ,a.vch_ponderacion AS respuesta_opcion_valor
                ,ou.DTE_CLOSING_DATE AS outstanding_surveys_dte_fecha_cierre
                ,ou.status AS outstanding_surveys_status
                ,bu.description AS respondent_default_business_unit
        FROM (
                SELECT ans.question_id
                        ,ans.ANSWER_ID
                        ,itm.vch_ponderacion
                        ,itm_txt.vch_texto vch_texto
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto AS q
                        ,fld_txt.TEXT_ITEM_ID AS qt_id
                        ,'' AS row_text
                        ,fld.vch_type
                        ,CASE left(fld.vch_type, 11)
                                WHEN 'multiSelect'
                                        THEN sum(convert(INT, itm2.vch_ponderacion))
                                ELSE max(convert(INT, itm2.vch_ponderacion))
                                END AS value
                FROM outstanding_answer ans
                INNER JOIN survey_item itm ON ans.item_id = itm.item_id
                INNER JOIN survey_item_title AS itm_tit ON itm_tit.item_id = itm.item_id
                INNER JOIN survey_text_item AS itm_txt ON itm_txt.text_item_id = itm_tit.text_item_id
                INNER JOIN outstanding_question AS ques ON ques.question_id = ans.question_id
                INNER JOIN survey_field AS fld ON fld.field_id = ques.field_id
                INNER JOIN survey_field_item fld_itm ON fld_itm.field_object_id = fld.field_object_id
                INNER JOIN survey_item AS itm2 ON itm2.item_id = fld_itm.item_id
                INNER JOIN survey_field_title AS fld_tit ON fld_tit.field_object_id = fld.field_object_id
                INNER JOIN survey_text_item AS fld_txt ON fld_tit.text_item_id = fld_txt.text_item_id
                WHERE fld.vch_type NOT IN (
                                'textField'
                                ,'dateSelector'
                                ,'textFieldArray'
                                ,'exclusiveSelectArray'
                                ,'tableField'
                                ,'multiSelectArray'
                                )
                GROUP BY fld.vch_type
                        ,ans.question_id
                        ,ans.ANSWER_ID
                        ,itm.vch_ponderacion
                        ,itm_txt.vch_texto
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto
                        ,fld_txt.TEXT_ITEM_ID

                UNION

                SELECT ans.question_id
                        ,ans.ANSWER_ID
                        ,NULL vch_ponderacion
                        ,NULL vch_texto
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto AS q
                        ,fld_txt.TEXT_ITEM_ID AS qt_id
                        ,'' AS row_text
                        ,fld.vch_type
                        ,NULL AS value
                FROM outstanding_answer ans
                INNER JOIN outstanding_question AS ques ON ques.question_id = ans.question_id
                INNER JOIN survey_field AS fld ON fld.field_id = ques.field_id
                INNER JOIN survey_field_title AS fld_tit ON fld_tit.field_object_id = fld.field_object_id
                INNER JOIN survey_text_item AS fld_txt ON fld_tit.text_item_id = fld_txt.text_item_id
                WHERE fld.vch_type IN (
                                'textField'
                                ,'dateSelector'
                                )
                GROUP BY fld.vch_type
                        ,ans.question_id
                        ,ans.ANSWER_ID
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto
                        ,fld_txt.TEXT_ITEM_ID

                UNION

                SELECT ans.question_id
                        ,ans.ANSWER_ID
                        ,NULL vch_ponderacion
                        ,NULL vch_texto
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto AS q
                        ,fld_txt.TEXT_ITEM_ID AS qt_id
                        ,' - ' + itm_txt.vch_texto AS row_text
                        ,fld.vch_type
                        ,NULL AS value
                FROM outstanding_answer ans
                INNER JOIN outstanding_question AS ques ON ques.question_id = ans.question_id
                INNER JOIN survey_field AS fld ON fld.field_id = ques.field_id
                INNER JOIN survey_field_title AS fld_tit ON fld_tit.field_object_id = fld.field_object_id
                INNER JOIN survey_text_item AS fld_txt ON fld_tit.text_item_id = fld_txt.text_item_id


                INNER JOIN survey_item itm ON ans.item_id = itm.item_id
                INNER JOIN survey_item_title AS itm_tit ON itm_tit.item_id = itm.item_id
                INNER JOIN survey_text_item AS itm_txt ON itm_txt.text_item_id = itm_tit.text_item_id

                WHERE fld.vch_type = 'textFieldArray'
                GROUP BY fld.vch_type
                        ,ans.question_id
                        ,ans.ANSWER_ID
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto
                        ,fld_txt.TEXT_ITEM_ID
                        ,itm_txt.vch_texto

                UNION

                SELECT DISTINCT ans.question_id
                        ,ans.ANSWER_ID
                        ,head.vch_ponderacion
                        ,head_txt.vch_texto vch_texto
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto AS q
                        ,fld_txt.TEXT_ITEM_ID AS qt_id
                        ,' - ' + itm_txt2.vch_texto AS row_text
                        ,fld.vch_type
                        ,CASE left(fld.vch_type, 11)
                                WHEN 'multiSelect'
                                        THEN sum(convert(INT, head2.vch_ponderacion))
                                ELSE max(convert(INT, head2.vch_ponderacion))
                                END AS value
                FROM outstanding_answer ans
                INNER JOIN survey_header AS head ON head.header_id = ans.header_id
                INNER JOIN survey_header_title AS head_tit ON head_tit.header_id = head.header_id
                INNER JOIN survey_text_item AS head_txt ON head_txt.text_item_id = head_tit.text_item_id
                INNER JOIN outstanding_question AS ques ON ques.question_id = ans.question_id
                INNER JOIN survey_field AS fld ON fld.field_id = ques.field_id
                INNER JOIN survey_field_header AS fld_head ON fld_head.field_object_id = fld.field_object_id
                INNER JOIN survey_header AS head2 ON head2.header_id = fld_head.header_id
                INNER JOIN survey_field_title AS fld_tit ON fld_tit.field_object_id = fld.field_object_id
                INNER JOIN survey_text_item AS fld_txt ON fld_tit.text_item_id = fld_txt.text_item_id
                INNER JOIN survey_item AS itm ON itm.item_id = ans.item_id
                INNER JOIN survey_item_title AS itm_tit ON itm_tit.item_id = itm.item_id
                INNER JOIN survey_text_item AS itm_txt2 ON itm_txt2.text_item_id = itm_tit.text_item_id
                WHERE (
                                fld.vch_type = 'exclusiveSelectArray'
                                AND ans.item_id IS NOT NULL
                                )
                        OR (
                                fld.vch_type = 'tableField'
                                AND ans.header_id IS NOT NULL
                                )
                        OR (
                                fld.vch_type = 'multiSelectArray'
                                AND ans.header_id IS NOT NULL
                                AND ans.item_id IS NOT NULL
                                )
                GROUP BY ans.answer_id
                        ,fld.vch_type
                        ,ans.question_id
                        ,head.vch_ponderacion
                        ,head_txt.vch_texto
                        ,ans.vch_answer
                        ,fld.field_object_id
                        ,fld_txt.vch_texto
                        ,fld_txt.TEXT_ITEM_ID
                        ,itm_txt2.vch_texto
                ) a
        INNER JOIN outstanding_surveys_question AS sur_ques ON sur_ques.question_id = a.question_id
        INNER JOIN outstanding_surveys pe ON sur_ques.outstanding_surveys_id = pe.outstanding_surveys_id
        INNER JOIN poll ON pe.SURVEY_ID = poll.survey_id
        INNER JOIN poll_respondent prr ON prr.poll_id = poll.poll_id
                AND prr.outstanding_surveys_id = pe.OUTSTANDING_SURVEYS_ID
        INNER JOIN users resp ON resp.user_id = prr.respondent_id
        LEFT JOIN puesto job ON job.puesto_id = resp.default_workflow_position
        LEFT JOIN business_unit bu ON bu.business_unit_id = job.business_unit_id
        INNER JOIN outstanding_surveys ou ON ou.outstanding_surveys_id = prr.outstanding_surveys_id
        LEFT JOIN survey_field_object AS fld_obj ON fld_obj.field_object_id = a.field_object_id		
        ]]>
        </createView>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-2.12.3.93-20191202-EQUINTANILLA-3">  
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <not>
                <indexExists indexName="ix_OUTSTANDING_QUESTION_field_question_id" />
            </not>
        </preConditions>
        <comment>
            Se agrega index de field_id para llenado de cuestionario
        </comment>
        <sql>
            <![CDATA[
                CREATE NONCLUSTERED INDEX [ix_OUTSTANDING_QUESTION_field_question_id]
                ON [dbo].[OUTSTANDING_QUESTION] ([FIELD_ID])
                INCLUDE ([QUESTION_ID])
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-2.12.3.93-20191202-EQUINTANILLA-4">  
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
            <not>
                <indexExists indexName="ix_OUTSTANDING_ANSWER_question_id_item_id" />
            </not>
        </preConditions>
        <comment>
            Se agrega index de respuestas para llenado de cuestionario
        </comment>
        <sql>
            <![CDATA[            
                CREATE NONCLUSTERED INDEX [ix_OUTSTANDING_ANSWER_question_id_item_id]
                ON [dbo].[OUTSTANDING_ANSWER] ([QUESTION_ID])
                INCLUDE ([ANSWER_ID],[VCH_ANSWER],[ITEM_ID])
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-2.12.3.109-20200514-EQUINTANILLA-1">  
        <validCheckSum>7:5a83bbfd3ead4eeda5ec671081b3d3b4</validCheckSum>
        <validCheckSum>8:a7b6836807518a8d56fd58f42a2ba2cc</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se marcan como borradas las carpetas hijas de una carpeta borrada
        </comment>
        <sql>
            <![CDATA[            
                ALTER TABLE tblnodo DISABLE TRIGGER update_path
            ]]>
        </sql>
        <sql>
            <![CDATA[   
                UPDATE nodo
                SET nodo.int_borrado = 1 
                FROM tblnodo nodo
                WHERE EXISTS (
                    SELECT 1
                    FROM tblnodo del 
                    WHERE del.int_borrado = 1
                    AND replace(nodo.path, '\\', '\') LIKE replace(del.path, '\\', '\') + '%'
                    AND replace(nodo.path, '\\', '\') != replace(del.path, '\\', '\')
                )
                AND NOT EXISTS (
                    SELECT 1
                    FROM tblnodo dup 
                    WHERE replace(nodo.path, '\\', '\') LIKE replace(dup.path, '\\', '\') + '%'
                    AND replace(nodo.path, '\\', '\') != replace(dup.path, '\\', '\')
                    GROUP BY replace(dup.path, '\\', '\')
                    HAVING COUNT(replace(dup.path, '\\', '\')) > 1
                )
                AND nodo.int_borrado = 0
            ]]>
        </sql>
        <sql>
            <![CDATA[   
                ALTER TABLE tblnodo ENABLE TRIGGER update_path
            ]]>
        </sql>
    </changeSet>                                                                                                                                                                                                                                                                                   
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Jambul-2.12.3.109-20200514-EQUINTANILLA-2">  
        <validCheckSum>7:603815fd0d272f351d5c35298ad95d91</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se normaliza ruta de carpetas con doble diagonal
        </comment>
        <sql>
            <![CDATA[            
                ALTER TABLE tblnodo DISABLE TRIGGER update_path
            ]]>
        </sql>
        <sql>
            <![CDATA[   
                UPDATE nodo
                SET nodo.path = replace(nodo.path, '\\', '\')
                FROM tblnodo nodo
                WHERE nodo.path LIKE '%\\%'
            ]]>
        </sql>
        <sql>
            <![CDATA[   
                ALTER TABLE tblnodo ENABLE TRIGGER update_path
            ]]>
        </sql>
    </changeSet>
    <changeSet author="Alfonso de León Avalos"
               id="Jambul-**********-20200625-ADELEON-1">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se modifica el tipo de dato para la columna intmeta en la tabla tblindicadorrevision.
        </comment>
        <sql>
        <![CDATA[
            ALTER TABLE tblindicadorrevision
            ALTER COLUMN intmeta numeric(10,2)
        ]]>
        </sql>
    </changeSet>
    <changeSet author="Alfonso de León Avalos"
               id="Jambul-**********-20200625-ADELEON-2">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se actualizan los valores de la columna intmeta de la tabla tblindicadorrevision para que coincidan con el valor de la tabla tblindicador.
        </comment>
        <sql>
        <![CDATA[
            UPDATE
                tblindicadorrevision
            SET
                tblindicadorrevision.intmeta  = tblindicador.intmeta
            FROM
                tblindicadorrevision
                INNER JOIN tblindicador
                    ON tblindicadorrevision.intindicadorid = tblindicador.intindicadorid
            WHERE
                tblindicadorrevision.intindicadorid = tblindicador.intindicadorid
        ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-**********-20200629-EQUINTANILLA-1">
        <comment>
            Se agrega columna de vigencia de documentos por tipo de documento.
        </comment>
        <addColumn tableName="document_type">
            <column name="document_expiration" type="int" defaultValueNumeric="12">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <sql>
        <![CDATA[
            UPDATE document_type SET document_expiration = (SELECT MAX(document_expiration) FROM settings)
        ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-**********-20200629-EQUINTANILLA-2">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="document" columnName="expiration_date" />                
            </not>
        </preConditions>
        <comment>
            Se agrega columna de expiración de documento a tabla de documentos
        </comment>
        <addColumn tableName="document">
            <column name="expiration_date" type="date" defaultValueDate="${now}">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <sql>
        <![CDATA[
            UPDATE document SET expiration_date = DATEADD(MONTH, (SELECT MAX(document_expiration) FROM settings), last_modification_date)
        ]]>
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores"
               id="Jambul-**********-20200629-EQUINTANILLA-3">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="settings" columnName="document_expiration" />                
        </preConditions>
        <comment>
            Se elimina columna de vigencia de documentos en preferencias.
        </comment>
        <dropDefaultValue tableName="settings" columnName="document_expiration" columnDataType="int"  />  
        <dropColumn tableName="settings" columnName="document_expiration"/>   
    </changeSet>
    <changeSet id="Jambul-2.12.3.113-20200702-EQUINTANILLA-1" 
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>
            Se cambia valor default de 600 a 300 DPI para páginas en Visor de Documentos
        </comment>
        <update tableName="settings">
            <column name="pdf_image_dpi" valueNumeric="300"/>
            <where>pdf_image_dpi is null OR pdf_image_dpi = 600</where>
        </update>
    </changeSet>
    <changeSet id="Jambul-2.12.3.113-20200702-EQUINTANILLA-2" 
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>
            Se cambia valor default de páginas guardadas en base de datos para Visor de Documentos
        </comment>
        <update tableName="settings">
            <column name="pdf_maximum_persisted_pages" valueNumeric="20"/>
            <where>pdf_maximum_persisted_pages is null OR pdf_maximum_persisted_pages = 50</where>
        </update>
    </changeSet>
    <changeSet id="Jambul-**********-20200714-EQUINTANILLA-1" 
            author="Eduardo Guadalupe Quintanilla Flores">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from supported_languages where id = 'es-AA'</sqlCheck>
        </preConditions>
        <comment>
            Se agrega lenguaje de AAK
        </comment>
        <insert tableName="supported_languages">
            <column name="id" value="es-AA" />
            <column name="description" value="Español AAK"  />
        </insert>
    </changeSet>
    <changeSet id="**********-20200720-ADELEON-1" 
        author="Alfonso de León Avalos">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(1) from supported_languages where id = 'es-AI'</sqlCheck>
        </preConditions>
        <comment>
            Se agrega lenguaje de Agroindustrias
        </comment>
        <insert tableName="supported_languages">
            <column name="id" value="es-AI" />
            <column name="description" value="Español Agroindustrias"  />
        </insert>
    </changeSet>
</databaseChangeLog>