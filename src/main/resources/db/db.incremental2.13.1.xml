<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.3.xsd">
    <property name="now" value="sysdate" dbms="oracle"/>
    <property name="now" value="current_timestamp" dbms="mssql"/>
    <property name="now" value="now()" dbms="mysql"/>
    <property name="now" value="now()" dbms="postgresql"/>
    <changeSet author="<PERSON>" id="Comet-2.13.1-20180423-LLIMAS-1">
        <tagDatabase tag="2.13.0.1"/>
    </changeSet>
    
    <changeSet id="add-secondary-color" author="rgarza">
        <validCheckSum>8:01ef053fc1f3201ea8849297506647b8</validCheckSum>
        <validCheckSum>8:8c377b04c8557575d15b2586e201b06c</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT count(*)
                FROM dual
                WHERE EXISTS (
                    SELECT *
                    FROM sys.columns
                    WHERE Name = 'system_secondary_color'
			        AND Object_ID = Object_ID('settings')
		        )
            </sqlCheck>
        </preConditions>
        <addColumn tableName="settings">
            <column name="system_secondary_color" type="text" defaultValue="#FFA500">
                <constraints nullable="false" />
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="RGARZA-********-1" author="rgarza">
        <comment>
            finding_activity_view XML PATH to group_concat
        </comment>
        <createView viewName="finding_activity_view" replaceIfExists="true">
            <![CDATA[
            SELECT fa.finding_id,
                (CASE fa.type
                WHEN 1 THEN 'ACI'
                WHEN 2 THEN 'AAT'
                END) as finding_activity_type,
                (
                    SELECT dbo.GROUP_CONCAT_D(LTRIM(RTRIM(c1)),', ')
                    FROM (
                        SELECT u.first_name AS c1
                        FROM owner_user ou
                        INNER JOIN users u ON u.user_id = ou.user_id
                        WHERE ou.owner_id = a.implementer_id
                        UNION
                        SELECT u.first_name
                        FROM owner_position op
                        INNER JOIN user_position up ON up.position_id = op.position_id
                        INNER JOIN users u ON u.user_id = up.user_id
                        WHERE op.owner_id = a.implementer_id
                        ) AS t1
                    FOR XML PATH('')
                ) AS implementer,
                (
                    SELECT dbo.GROUP_CONCAT_D(LTRIM(RTRIM(c1)),', ')
                    FROM (
                        SELECT u.first_name AS c1
                        FROM owner_user ou
                        INNER JOIN users u ON u.user_id = ou.user_id
                        WHERE ou.owner_id = a.verifier_id
                        UNION
                        SELECT u.first_name
                        FROM owner_position op
                        INNER JOIN user_position up ON up.position_id = op.position_id
                        INNER JOIN users u ON u.user_id = up.user_id
                        WHERE op.owner_id = a.verifier_id
                        ) AS t2
                ) AS verifier,
                a.*
                FROM finding_activity fa
                INNER JOIN activity a ON a.activity_id = fa.activity_id
            ]]>
        </createView>
    </changeSet>
    
    <changeSet id="RGARZA-********-2" author="rgarza">
        <comment>
            document_history_concat XML PATH to group_concat
        </comment>
        <createView viewName="document_history_concat" replaceIfExists="true">
            <![CDATA[
            SELECT o.code
                    ,MAX(o.creation_date) AS creation_date
                    ,o.request_id
                    ,o.request_type
                    ,o.reason
                    ,o.verified_by
                    ,o.version
                    ,(
                            SELECT dbo.GROUP_CONCAT_D(i.authorized_by, ', ')
                            FROM document_history i
                            WHERE i.version = o.version
                                    AND i.code = o.code
                                    AND i.request_id = o.request_id
                                    AND i.request_type = o.request_type
                            ) authorized_by
                    ,r.document_id
                    ,r.survey_id
                    ,r.organizational_unit_id
                    ,r.document_type
                    ,r.int_borrado
                    ,r.autor_id
                    ,r.reazon
                    ,r.file_id
                    ,r.business_unit_id
                    ,r.business_unit_department_id
                    ,r.int_estado
            FROM document_history o
            INNER JOIN request r ON r.id = o.request_id
            GROUP BY o.code
                    ,o.request_id
                    ,o.request_type
                    ,o.reason
                    ,o.verified_by
                    ,o.version
                    ,r.document_id
                    ,r.survey_id
                    ,r.organizational_unit_id
                    ,r.document_type
                    ,r.int_borrado
                    ,r.autor_id
                    ,r.reazon
                    ,r.file_id
                    ,r.business_unit_id
                    ,r.business_unit_department_id
                    ,r.int_estado
            ]]>
        </createView>
    </changeSet>
    <changeSet id="RGARZA-********-3" author="rgarza">
        <comment>
            tress_user_info_view XML PATH to group_concat
        </comment>
        <createView viewName="tress_user_info_view" replaceIfExists="true">
            <![CDATA[
            SELECT u.user_id
                ,u.STATUS
                ,u.account
                ,u.mail
                ,u.first_name full_user_description
                ,(
                        SELECT dbo.GROUP_CONCAT_D(DISTINCT p.vch_descripcion, ', ')
                        FROM usuario_puesto up
                        INNER JOIN puesto p ON up.puesto_id = p.puesto_id
                        WHERE u.user_id = up.usuario_id
                        ) position_description
                ,(
                        SELECT dbo.GROUP_CONCAT_D(DISTINCT b.description, ', ')
                        FROM usuario_puesto up
                        INNER JOIN puesto p ON up.puesto_id = p.puesto_id
                        INNER JOIN business_unit b ON b.business_unit_id = p.business_unit_id
                        WHERE u.user_id = up.usuario_id
                        ) business_unit_description
                ,(
                        SELECT dbo.GROUP_CONCAT_D(DISTINCT b.physical_location, ', ')
                        FROM usuario_puesto up
                        INNER JOIN puesto p ON up.puesto_id = p.puesto_id
                        INNER JOIN business_unit b ON b.business_unit_id = p.business_unit_id
                        WHERE u.user_id = up.usuario_id
                        ) physical_location
                ,(
                        SELECT dbo.GROUP_CONCAT_D(DISTINCT CAST(p.business_unit_id AS VARCHAR(19)), ', ')
                        FROM usuario_puesto up
                        INNER JOIN puesto p ON up.puesto_id = p.puesto_id
                        WHERE u.user_id = up.usuario_id
                        ) business_unit_id
                ,COUNT(1) positions_count
        FROM users u
        LEFT JOIN usuario_puesto ups ON ups.usuario_id = u.user_id
        WHERE u.root = 0
                OR u.root IS NULL
        GROUP BY u.user_id
                ,u.account
                ,u.mail
                ,u.first_name
                ,u.STATUS
            ]]>
        </createView>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Comet-2.13.1.6-********-EQUINTANILLA-1">
        <comment>
            Se agrega columna para mostrar o no la ventana de mensaje de bienvenida
        </comment>
        <addColumn tableName="users">
            <column name="show_welcome_dialog" type="numeric(1,0)" defaultValueNumeric="1">
                <constraints nullable="false" />
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>