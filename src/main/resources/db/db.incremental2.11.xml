<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.3.xsd">
    <property name="now" value="sysdate" dbms="oracle"/>
    <property name="now" value="current_timestamp" dbms="mssql"/>
    <property name="now" value="now()" dbms="mysql"/>
    <property name="now" value="now()" dbms="postgresql"/>
    <changeSet author="<PERSON>" id="Fig-2.11.0.0-20160609-EQUINTANILLA-1">
        <tagDatabase tag="2.11.0.0"/>
    </changeSet>
    <changeSet author="<PERSON>" id="Fig-2.11.0.0-20160726-EQ<PERSON>NTANILLA-1">
        <tagDatabase tag="********"/>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160728-EQUINTANILLA-1">
        <comment>
            Se agrega tabla de activity_objective
        </comment>
        <createTable tableName="activity_objective">
            <column name="activity_objective_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="varchar(255)" />
            <column name="description" type="varchar(255)"/>
            <column name="status" type="smallint" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="smallint" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="datetime"/>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="bigint"/>
            <column name="type" type="numeric(1,0)"> 
                <constraints nullable="false"/> 
            </column>
        </createTable>   
        <addPrimaryKey 
            tableName="activity_objective"
            columnNames="activity_objective_id"
            constraintName="pk_activity_objective"/>
        <addForeignKeyConstraint 
            baseTableName="activity_objective" 
            baseColumnNames="created_by" 
            constraintName="fk_activity_objective_created_by" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <addForeignKeyConstraint 
            baseTableName="activity_objective" 
            baseColumnNames="last_modified_by" 
            constraintName="fk_activity_objective_last_modified_by" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <addUniqueConstraint tableName="activity_objective"
                             columnNames="code"/>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160729-EQUINTANILLA-1">
        <comment>
            Se agrega columna de módulo
        </comment>
        <addColumn tableName="activity_objective">
            <column name="module_task" type="varchar(255)" />
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160801-EQUINTANILLA-1">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(activity_objective_id) from activity_objective</sqlCheck>
        </preConditions>
        <comment>
            Se agrega valores default de activity_objective
        </comment>
        <insert tableName="activity_objective">
            <column name="activity_objective_id" valueNumeric="1"/>
            <column name="code" value="OBJ-001"/>
            <column name="description" value="Avance"/>
            <column name="last_modified_date" valueDate="${now}"/>
            <column name="created_date" valueDate="${now}"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="last_modified_by" valueNumeric="1"/>
            <column name="type" valueNumeric="1"/>
        </insert>
        <insert tableName="activity_objective">
            <column name="activity_objective_id" valueNumeric="2"/>
            <column name="code" value="OBJ-002"/>
            <column name="description" value="Llenar formulario"/>
            <column name="last_modified_date" valueDate="${now}"/>
            <column name="created_date" valueDate="${now}"/>
            <column name="created_by" valueNumeric="1"/>
            <column name="last_modified_by" valueNumeric="1"/>
            <column name="type" valueNumeric="2"/>
            <column name="module_task" value="fill_form"/>
        </insert>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160801-EQUINTANILLA-2">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(sequence_name) from hibernate_sequences where sequence_name = 'activity_objective'</sqlCheck>
        </preConditions>
        <comment>
            Se agrega registro de hibernate_sequences para activity_objective
        </comment>        
        <insert tableName="hibernate_sequences">
            <column name="sequence_name" value="activity_objective"/>
            <column name="sequence_next_hi_value" valueNumeric="3"/>
        </insert>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160801-EQUINTANILLA-3">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">select count(sequence_name) from hibernate_sequences where sequence_name = 'activity_objective'</sqlCheck>
        </preConditions>
        <comment>
            Se actualiza registro de hibernate_sequences para activity_objective
        </comment>        
        <update tableName="hibernate_sequences">
            <column name="sequence_name" value="activity_objective"/>
            <column name="sequence_next_hi_value" valueNumeric="3"/>
            <where>sequence_name = 'activity_objective'</where>
        </update>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160801-EQUINTANILLA-4">
        <comment>
            Se agrega columna de objectivo de actividad
        </comment>
        <addColumn tableName="activity">
            <column name="activity_objective_id" type="bigint"/>
        </addColumn>
        <addForeignKeyConstraint 
            baseTableName="activity" 
            baseColumnNames="activity_objective_id" 
            constraintName="fk_activity_objective" 
            referencedTableName="activity_objective" 
            referencedColumnNames="activity_objective_id" 
            onDelete="RESTRICT"
        />
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160801-EQUINTANILLA-5">
        <comment>
            Se actualiza valor default de activity_objective_id en tabla activity
        </comment>    
        <update tableName="activity">
            <column name="activity_objective_id" valueNumeric="1"/>
            <where>activity_objective_id IS NULL</where>
        </update>
    </changeSet>   
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160801-EQUINTANILLA-6">
        <comment>
            Se agrega restricción de not null para activity_objective_id en tabla activity
        </comment>    
        <addNotNullConstraint tableName="activity" columnName="activity_objective_id" columnDataType="bigint"/>  
    </changeSet>   
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160803-EQUINTANILLA-1">
        <comment>
            Se agrega columna form_id a tabla activities
        </comment>
        <addColumn tableName="activity">
            <column name="form_id" type="bigint" />
        </addColumn>
        <addForeignKeyConstraint 
            baseTableName="activity" 
            baseColumnNames="form_id" 
            constraintName="fk_activity_form_id" 
            referencedTableName="document" 
            referencedColumnNames="id"/>
    </changeSet> 
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160804-EQUINTANILLA-1">
        <comment>
            Se agrega columna para guardar formulario llenado
        </comment>
        <addColumn tableName="activity">
            <column name="outstanding_survey_id" type="bigint"/>
        </addColumn>
        <addForeignKeyConstraint 
            baseTableName="activity" 
            baseColumnNames="outstanding_survey_id" 
            constraintName="fk_activity_osurvey_id" 
            referencedTableName="outstanding_surveys" 
            referencedColumnNames="outstanding_surveys_id" 
            onDelete="RESTRICT"
        />
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160805-EQUINTANILLA-1">
        <comment>
            Se agrega columna para guardar formulario llenado en historial
        </comment>
        <addColumn tableName="activity_history">
            <column name="outstanding_survey_id" type="bigint"/>
        </addColumn>
        <addForeignKeyConstraint 
            baseTableName="activity_history" 
            baseColumnNames="outstanding_survey_id" 
            constraintName="fk_activity_history_osurvey_id" 
            referencedTableName="outstanding_surveys" 
            referencedColumnNames="outstanding_surveys_id" 
            onDelete="RESTRICT"
        />
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-2.11.1-20160815-EQUINTANILLA-1">
        <comment>
            Se agrega columna minimum_code_digits a settings
        </comment>
        <addColumn tableName="settings">
            <column name="minimum_code_digits" type="numeric(1,0)" defaultValue="3">
                <constraints nullable="false" />
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="Bruno Hiram Alva García" 
               id="Fig-2.11.1-20160819-BALVA-1">
        <comment>
            Se agrega columna answered en tblqueja
        </comment>
        <addColumn tableName="tblqueja">
            <column name="answered" type="numeric(1,0)"/>
        </addColumn>        
    </changeSet>
    <changeSet author="Javier Nicolas Guillen" id="Fig-2.11.0.2-20160819-JNICOLAS-1">
        <tagDatabase tag="2.11.0.2"/>
    </changeSet>
    <changeSet author="Javier Nicolas Guillen" id="Fig-2.11.0.2-20160902-JNICOLAS-1">
        <tagDatabase tag="2.11.0.3"/>
    </changeSet>
    <changeSet author="Luis Carlos Limas Alvarez" id="Fig-2.11.0.3-20160906-LLIMAS-1">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(sequence_name) from hibernate_sequences where sequence_name = 'printable_type'</sqlCheck>
        </preConditions>
        <comment>
            Se agrega registro de hibernate_sequences para printable_type
        </comment>        
        <insert tableName="hibernate_sequences">
            <column name="sequence_name" value="printable_type"/>
            <column name="sequence_next_hi_value" valueNumeric="10"/>
        </insert>
    </changeSet>
    <changeSet author="Luis Carlos Limas Alvarez" id="Fig-2.11.0.3-20160906-LLIMAS-2">
        <validCheckSum>7:d69fd50930f49da46fc3809b664cddfe</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">select count(sequence_name) from hibernate_sequences where sequence_name = 'printable_type'</sqlCheck>
        </preConditions>
        <comment>
            Se actualiza registro de hibernate_sequences para printable_type
        </comment>        
        <update tableName="hibernate_sequences">
            <column name="sequence_next_hi_value" valueNumeric="(SELECT (((MAX(printable_type_id) - 100) / 100) + 101) FROM printable_type)"/>
            <where>sequence_name = 'printable_type'</where>
        </update>
    </changeSet>
    <changeSet author="Julio Guadalupe Alemán Reyes" id="Fig-2.11.0.3-20160906-JALEMAN-1">
        <validCheckSum>8:1ba9bf5649d81b82625ee57b5bb4e14c</validCheckSum>
        <comment>
            Se agrega columna thumbnail a files
        </comment>
        <addColumn tableName="files">
            <column name="thumbnail" type="VARBINARY(MAX)"/>
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" id="Fig-2.11.0.3-20160915-EQUINTANILLA-1">
        <tagDatabase tag="2.11.0.4"/>
    </changeSet>
    <changeSet author="Luis Carlos Limas Alvarez" id="Fig-********-20160919-LLIMAS-1">
        <tagDatabase tag="********"/>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" id="Fig-********-20160922-EQUINTANILLA-1">
        <tagDatabase tag="********"/>
    </changeSet>
    <changeSet id="Fig-********-20160928-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de solicitudes por verificar</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="18" />
            <column name="code" value="DOCUMENT-TO-VERIFY-REQUEST" />
            <column name="description" value="Solicitudes de verificar documento"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20160929-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de solicitudes por verificar</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="19" />
            <column name="code" value="DOCUMENT-TO-AUTHORIZE-REQUEST" />
            <column name="description" value="Solicitudes de authorizar documento"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161005-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de borrador de solicitudes de llenado de formularios</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="20" />
            <column name="code" value="DOCUMENT-TO-REQUEST-FILL-DRAFT" />
            <column name="description" value="Borrador de solicitudes de llenado de formularios"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161005-EQUINTANILLA-2"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de solicitudes de llenado de formularios</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="21" />
            <column name="code" value="DOCUMENT-TO-FILL-FORM" />
            <column name="description" value="Solicitudes por completar de llenado de formularios"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" id="Fig-********-20160710-ALARES-1">
        <tagDatabase tag="********"/>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-1">
        <comment>
            Se agrega tabla de meeting_type
        </comment>
        <createTable tableName="meeting_type">
            <column name="meeting_type_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="varchar(255)" />
            <column name="description" type="varchar(255)"/>
            <column name="status" type="smallint" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="smallint" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="datetime"/>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="bigint"/>
            <column name="type" type="numeric(1,0)"> 
                <constraints nullable="false"/> 
            </column>
            <column name="registry_code" type="varchar(max)"/>
            <column name="registry_code_sequence_length" type="numeric(3,0)"/>
            <column name="days_to_close" type="numeric(3,0)"/>
            <column name="minute_id" type="bigint"/>
            <column name="document_code" type="varchar(max)"/>
            <column name="document_description" type="varchar(max)"/>
            <column name="document_id" type="bigint"/>
        </createTable>   
        <addPrimaryKey 
            tableName="meeting_type"
            columnNames="meeting_type_id"
            constraintName="pk_meeting_type"/>
        <addUniqueConstraint tableName="meeting_type" columnNames="code"/>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-2">
        <comment>
            Se agrega tabla de meeting_type_dynamic_field
        </comment>
        <createTable tableName="meeting_type_dynamic_field">
            <column name="dynamic_field_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="meeting_type_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>   
        <addForeignKeyConstraint 
            baseTableName="meeting_type_dynamic_field" 
            baseColumnNames="meeting_type_id" 
            constraintName="fk_mt_df_meeting_type" 
            referencedTableName="meeting_type" 
            referencedColumnNames="meeting_type_id" 
            onDelete="RESTRICT"
        />
        <addForeignKeyConstraint 
            baseTableName="meeting_type_dynamic_field" 
            baseColumnNames="dynamic_field_id" 
            constraintName="fk_mt_df_field_field" 
            referencedTableName="dynamic_field" 
            referencedColumnNames="dynamic_field_id" 
            onDelete="RESTRICT"
        />
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-3">
        <comment>
            Se agrega tabla de meeting
        </comment>
        <createTable tableName="meeting">
            <column name="meeting_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="varchar(255)" />
            <column name="description" type="varchar(255)"/>
            <column name="status" type="smallint" defaultValueNumeric="1">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="smallint" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="datetime"/>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="bigint"/>
            <column name="meeting_type_id" type="bigint"/>
            <column name="start_on" type="datetime"/>
            <column name="finish_on" type="datetime"/>
            <column name="periodicity_id" type="bigint"/>
            <column name="private" type="numeric(1,0)"/>
            <column name="outstanding_survey_id" type="bigint"/>
        </createTable>   
        <addPrimaryKey 
            tableName="meeting"
            columnNames="meeting_id"
            constraintName="pk_meeting"/>
        <addUniqueConstraint tableName="meeting" columnNames="code"/>
        <addForeignKeyConstraint 
            baseTableName="meeting" 
            baseColumnNames="meeting_type_id" 
            constraintName="fk_meeting_meeting_type" 
            referencedTableName="meeting_type" 
            referencedColumnNames="meeting_type_id" 
            onDelete="RESTRICT"
        />
        <addForeignKeyConstraint 
            baseTableName="meeting" 
            baseColumnNames="periodicity_id" 
            constraintName="fk_meeting_periodicity" 
            referencedTableName="tblperiodicidad" 
            referencedColumnNames="intperiodicidadid" 
            onDelete="RESTRICT"
        />
        <addForeignKeyConstraint 
            baseTableName="meeting" 
            baseColumnNames="outstanding_survey_id" 
            constraintName="fk_meeting_outstanding_survey" 
            referencedTableName="outstanding_surveys" 
            referencedColumnNames="outstanding_surveys_id" 
            onDelete="RESTRICT"
        />
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-4">
        <comment>
            Se agrega tabla de meeting_files
        </comment>
        <createTable tableName="meeting_files">
            <column name="meeting_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="file_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable> 
        <addForeignKeyConstraint 
            baseTableName="meeting_files" 
            baseColumnNames="meeting_id" 
            constraintName="fk_meeting_file_meeting" 
            referencedTableName="meeting" 
            referencedColumnNames="meeting_id" 
            onDelete="RESTRICT"
        />
        <addForeignKeyConstraint 
            baseTableName="meeting_files" 
            baseColumnNames="file_id" 
            constraintName="fk_meeting_file_file" 
            referencedTableName="files" 
            referencedColumnNames="id" 
            onDelete="RESTRICT"
        />  
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-5">
        <comment>
            Se agrega tabla de meeting_comments
        </comment>
        <createTable tableName="meeting_comments">
            <column name="meeting_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="comment_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>  
        <addForeignKeyConstraint 
            baseTableName="meeting_comments" 
            baseColumnNames="meeting_id" 
            constraintName="fk_meeting_comments_meeting" 
            referencedTableName="meeting" 
            referencedColumnNames="meeting_id" 
            onDelete="RESTRICT"
        />
        <addForeignKeyConstraint 
            baseTableName="meeting_comments" 
            baseColumnNames="comment_id" 
            constraintName="fk_meeting_comment_comment" 
            referencedTableName="comment" 
            referencedColumnNames="comment_id" 
            onDelete="RESTRICT"
        /> 
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-6">
        <comment>
            Se agrega tabla de meeting_participants
        </comment>
        <createTable tableName="meeting_participants">
            <column name="meeting_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="participant_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>  
        <addForeignKeyConstraint 
            baseTableName="meeting_participants" 
            baseColumnNames="meeting_id" 
            constraintName="fk_meeting_participants_meeting" 
            referencedTableName="meeting" 
            referencedColumnNames="meeting_id" 
            onDelete="RESTRICT"
        />
        <addForeignKeyConstraint 
            baseTableName="meeting_participants" 
            baseColumnNames="participant_id" 
            constraintName="fk_meeting_participants_participants" 
            referencedTableName="users" 
            referencedColumnNames="user_id" 
            onDelete="RESTRICT"
        /> 
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-7">
        <comment>
            Se agrega tabla de meeting_activity
        </comment>
        <createTable tableName="meeting_activity">
            <column name="meeting_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="activity_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>  
        <addForeignKeyConstraint 
            baseTableName="meeting_activity" 
            baseColumnNames="meeting_id" 
            constraintName="fk_meeting_activity_meeting" 
            referencedTableName="meeting" 
            referencedColumnNames="meeting_id" 
            onDelete="RESTRICT"
        />
        <addForeignKeyConstraint 
            baseTableName="meeting_activity" 
            baseColumnNames="activity_id" 
            constraintName="fk_meeting_activity_activity" 
            referencedTableName="activity" 
            referencedColumnNames="activity_id" 
            onDelete="RESTRICT"
        /> 
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-8">
        <comment>
            Se agrega tabla de meeting_preferences
        </comment>
        <createTable tableName="meeting_preferences">
            <column name="scope_for_host" type="varchar(10)"/>
        </createTable>   
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161006-ALARES-9">
        <comment>
            Se agrega valores de pendientes de reuniones/actividades
        </comment>
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="22"/>
            <column name="code" value="MEETING-ACTIVITY-TO-START"/>
            <column name="description" value="Actividad de reuniones por comenzar"/>
            <column name="module" value="meeting"/>
            <column name="status" valueNumeric="1"/>
            <column name="deleted" valueNumeric="0"/>
        </insert>
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="23"/>
            <column name="code" value="MEETING-ACTIVITY-TO-COMPLETE"/>
            <column name="description" value="Actividad de reuniones por completar"/>
            <column name="module" value="meeting"/>
            <column name="status" valueNumeric="1"/>
            <column name="deleted" valueNumeric="0"/>
        </insert>
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="24"/>
            <column name="code" value="MEETING-ACTIVITY-TO-VERIFY"/>
            <column name="description" value="Actividad de reuniones por verificar"/>
            <column name="module" value="meeting"/>
            <column name="status" valueNumeric="1"/>
            <column name="deleted" valueNumeric="0"/>
        </insert>
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="25"/>
            <column name="code" value="MEETING-ACTIVITY-TO-VERIFY-NOT-APPLY"/>
            <column name="description" value="Actividad de reuniones por verificar como no aplica"/>
            <column name="module" value="meeting"/>
            <column name="status" valueNumeric="1"/>
            <column name="deleted" valueNumeric="0"/>
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161003-JLEAL-1"
            author="Josue Leal Ramirez">
        <comment>Se implementa el pendiente de encuestas en proceso por contestar</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="26" />
            <column name="code" value="POLL-TO-ANSWER-IN-PROCESS" />
            <column name="description" value="Encuestas en proceso por contestar"  />
            <column name="module"  value="poll"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet author="Luis Carlos Limas Alvarez" id="Fig-********-20161011-LLIMAS-1">
        <comment>
            Se estandariza nombre del módulo de acciones (hallazgos) a "action"
        </comment>
        <update tableName="ape_pending_type">
            <column name="module" value="action"/>
            <where>module = 'finding'</where>
        </update>
    </changeSet>	
    <changeSet id="Fig-********-20161010-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se agrega configuración de traducción de entities</comment>    
        <addColumn tableName="settings">
            <column name="localize_entities" type="numeric(1,0)" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>	
    <changeSet id="Fig-********-20161010-EQUINTANILLA-2"
            author="Eduardo Guadalupe Quintanilla Flores">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="Yakult">select system_id from settings</sqlCheck>
        </preConditions>
        <comment>
            Para Yakult por defecto queda la traducción de catálgos encendida
        </comment>
        <update tableName="settings">
            <column name="localize_entities" value="1"/>
        </update>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161007-ALARES-1">
        <comment>
            Se elimina columna equivocada en tabla meeting_type
        </comment>
        <dropColumn 
            columnName="type"
            tableName="meeting_type"/>
    </changeSet>
    
    <changeSet author="Aldo Germán Lares Lares" 
               id="Fig-2.11.1-20161010-ALARES-1">
        <comment>
            Se agrega reestricción con tabla de business unit department y documentos
        </comment>
        <addForeignKeyConstraint 
            baseTableName="document" 
            baseColumnNames="business_unit_department_id" 
            constraintName="fk_document_department" 
            referencedTableName="business_unit_department" 
            referencedColumnNames="business_unit_department_id" 
            onDelete="RESTRICT"
        />
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
                id="Fig-2.11.1-20161010-ALARES-2">
        <comment>
            Se agrega valores de pendientes de reuniones
        </comment>
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="27"/>
            <column name="code" value="MEETING-TO-ASSIST"/>
            <column name="description" value="reuniones por asistir"/>
            <column name="module" value="meeting"/>
            <column name="status" valueNumeric="1"/>
            <column name="deleted" valueNumeric="0"/>
        </insert>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
                id="Fig-2.11.1-20161010-ALARES-3">
        <validCheckSum>7:b513b120e6d7fca4950b6d3edf9561c2</validCheckSum>
        <comment>
            Se agrega columna de cuerpo de la reunión
        </comment>
        <addColumn tableName="meeting">
            <column name="body" type="varchar(max)" />
        </addColumn>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
                id="Fig-2.11.1-20161010-ALARES-4">
        <comment>
            Se agrega columna de invitados de la reunión
        </comment>
        <addColumn tableName="meeting">
            <column name="guests" type="varchar(max)" />
        </addColumn>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
                id="Fig-2.11.1-20161010-ALARES-5">
        <comment>
            Se agrega columna de organizador
        </comment>
        <addColumn tableName="meeting">
            <column name="host" type="bigint" />
        </addColumn>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" 
                id="Fig-2.11.1-20161010-ALARES-6">
        <comment>
            Se agrega columna de organizador
        </comment>
        <addColumn tableName="meeting">
            <column name="location" type="varchar(max)" />
        </addColumn>
    </changeSet>
    <changeSet author="Javier Nicolas Guillen" id="Fig-2.11.1-20161012-JNICOLAS-1">  
        <insert tableName="supported_languages">
            <column name="id" value="es-CIESA"/>
            <column name="description" value="Español de CIESA"/>
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161010-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de asignar lectores al documento</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="28" />
            <column name="code" value="DOCUMENT-TO-ASSIGN_READER" />
            <column name="description" value="asignar lectores al documento"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161010-EQUINTANILLA-2"
            author="Eduardo Guadalupe Quintanilla Flores">  
        <comment>
            Se agrega tabla de lectores de documentos
        </comment>
        <createTable tableName="document_reader">
            <column name="document_reader_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="smallint" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="datetime"/>
            <column name="created_date" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="bigint"/>
            <column name="document_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="reader_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="readed_on" type="datetime"/>
            <column name="readed" type="smallint"/>
            <column name="responsible_id" type="bigint"/>
        </createTable>   
        <addPrimaryKey 
            tableName="document_reader"
            columnNames="document_reader_id"
            constraintName="pk_document_reader"/>
        <addForeignKeyConstraint 
            baseTableName="document_reader" 
            baseColumnNames="created_by" 
            constraintName="fk_document_reader_created_by" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <addForeignKeyConstraint 
            baseTableName="document_reader" 
            baseColumnNames="last_modified_by" 
            constraintName="fk_document_reader_last_modified_by" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <addForeignKeyConstraint 
            baseTableName="document_reader" 
            baseColumnNames="document_id" 
            constraintName="fk_document_reader_document_id" 
            referencedTableName="document" 
            referencedColumnNames="id"/>
        <addForeignKeyConstraint 
            baseTableName="document_reader" 
            baseColumnNames="reader_id" 
            constraintName="fk_document_reader_reader_id" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
        <addForeignKeyConstraint 
            baseTableName="document_reader" 
            baseColumnNames="responsible_id" 
            constraintName="fk_document_reader_responsible_id" 
            referencedTableName="users" 
            referencedColumnNames="user_id"/>
    </changeSet>
    <changeSet id="Fig-********-201610010-EQUINTANILLA-3"
            author="Eduardo Guadalupe Quintanilla Flores">  
        <preConditions  onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se cargan datos existenes tabla de lectores de documentos
        </comment>   
        <sql>
            <![CDATA[
                INSERT INTO document_reader (
                        document_reader_id,
                        document_id,
                        reader_id,
                        created_date,
                        readed,
                        readed_on,
                        responsible_id,
                        last_modified_date,
                        created_by,
                        last_modified_by
                )
                SELECT 
                        row_number() over (order by intdocumentoid, intusuarioid),
                    intdocumentoid,
                        intusuarioid,
                        tspfechahora,
                        intleido,
                        dte_readed,
                        responsible_id,
                        tspfechahora,
                        case when responsible_id is null then intusuarioid else responsible_id end,
                        case when responsible_id is null then intusuarioid else responsible_id end
                FROM tbldocumentolector
            ]]>
        </sql>
    </changeSet> 
    <changeSet id="Fig-********-20161010-EQUINTANILLA-4"
            author="Eduardo Guadalupe Quintanilla Flores">  
        <preConditions  onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se agrega llave unica para lectores de documentos
        </comment>
        <sql>
            <![CDATA[
                CREATE UNIQUE NONCLUSTERED INDEX idx_document_reader
                ON document_reader(document_id, reader_id)
                WHERE deleted = 0
            ]]>
        </sql>
    </changeSet>
    <changeSet id="Fig-********-20161010-EQUINTANILLA-5"
            author="Eduardo Guadalupe Quintanilla Flores">  
        <comment>
            Se inserta valor inicial de hibernate_sequences para document_reader
        </comment>
        <insert tableName="hibernate_sequences">
            <column name="sequence_name" value="document_reader"/>
            <column name="sequence_next_hi_value" valueComputed="(SELECT COUNT(c.document_reader_id)+1 FROM document_reader c)"/>
        </insert>
        <update tableName="hibernate_sequences">
            <column name="sequence_next_hi_value" valueNumeric="1"/>
            <where>sequence_name = 'document_reader' AND sequence_next_hi_value IS NULL</where>
        </update>
    </changeSet>	
    <changeSet id="Fig-********-20161010-EQUINTANILLA-6"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de leer documentos</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueNumeric="29" />
            <column name="code" value="DOCUMENT-TO-READ" />
            <column name="description" value="Leer documento"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161012-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de entregar copias controladas</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueComputed="(SELECT MAX(pending_type_id)+1 FROM ape_pending_type)" />
            <column name="code" value="DOCUMENT-TO-DELIVER-PHYSICAL-COPY" />
            <column name="description" value="Entregar copias controladas"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161014-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se implementa el pendiente de renovar documentos</comment>    
        <insert tableName="ape_pending_type">
            <column name="pending_type_id" valueComputed="(SELECT MAX(pending_type_id)+1 FROM ape_pending_type)" />
            <column name="code" value="DOCUMENT-TO-RENEW" />
            <column name="description" value="Renovar documentos"  />
            <column name="module"  value="document"  />
            <column name="status" valueNumeric="1" />
            <column name="deleted" valueNumeric="0"  />
        </insert>
    </changeSet>
    <changeSet id="Fig-********-20161018-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <comment>Se agrega tabla de historial de tareas diarias</comment> 
        <createTable tableName="daily_task">
            <column name="daily_task_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="run_start" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="run_end" type="DATETIME"/>
        </createTable>
    </changeSet>
    <changeSet author="Raul Emiliano Montemayor Rodriguez" id="Fig-********-20161025-RMONTEMAYTOR-1">
        <tagDatabase tag="********"/>
    </changeSet>
    <changeSet author="Luis Carlos Limas Alvarez" id="Fig-********-20161021-LLIMAS-1">
        <comment>
            Se agrega indice para busqueda de usuarios por estatus
        </comment>   
        <createIndex tableName="users" indexName="idx_users_status">
            <column name="status" type="smallint"></column>
        </createIndex>
        <createIndex tableName="users" indexName="idx_users_status_id">
            <column name="user_id" type="bigint"></column>
            <column name="status" type="smallint"></column>
        </createIndex>
    </changeSet>
    <changeSet author="Luis Carlos Limas Alvarez" id="Fig-********-20161024-LLIMAS-1">
        <comment>
            Se agrega indice para busqueda de ape_pending_type y ape_pending_count
        </comment>   
        <createIndex tableName="ape_pending_type" indexName="idx_apependingtype_status_code">
            <column name="status" type="smallint"></column>
            <column name="code" type="varchar(50)"></column>
        </createIndex>
        <createIndex tableName="ape_pending_count" indexName="idx_apependingcount_ots">
            <column name="owner" type="bigint"></column>
            <column name="type" type="bigint"></column>
            <column name="status" type="smallint"></column>
        </createIndex>
    </changeSet>
    <changeSet author="Javier Nicolas Guillen" id="Fig-********-20161028-JNICOLAS-1">
        <comment>
            Se elimina la tabla de meeting_preferences
        </comment>
        <dropTable tableName="meeting_preferences" />
    </changeSet>
    <changeSet author="Javier Nicolas Guillen" id="Fig-********-20161028-JNICOLAS-2">
        <comment>
            Se agrega nuevamente la tabla meeting_preferences agregando la columna id
        </comment>
         <createTable tableName="meeting_preferences">
            <column name="id" type="BIGINT" />
            <column name="scope_for_host" type="varchar(10)" />
        </createTable>
    </changeSet>   
    <changeSet author="Javier Nicolas Guillen" id="Fig-********-20161028-JNICOLAS-3">
        <comment>
            Se agrega valor default en preferencias de reuniones
        </comment>
        <insert tableName="meeting_preferences">
            <column name="id" valueNumeric="1"/>
            <column name="scope_for_host" value="1"/>
        </insert>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" id="Fig-********-20161107-ALARES-1">
        <comment>
            Se agrega tipo en la tabla de daily_task
        </comment> 
        <addColumn tableName="daily_task">
            <column name="type" type="varchar(max)"/>
        </addColumn>
    </changeSet>
    <changeSet id="Fig-********-20161115-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">
        <preConditions onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <sql >
            if object_id('dbo.insert_path', 'TR') is not null
                DROP TRIGGER dbo.insert_path;
        </sql>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-********-20161122-EQUINTANILLA-1">  
        <comment>
            Se agrega configuración para descargar documentos para documentos no controlados por tipo de documento
        </comment>
        <addColumn tableName="document_type">
            <column name="download_files" type="numeric(1, 0)" defaultValueNumeric="0"> 
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="Eduardo Guadalupe Quintanilla Flores" 
               id="Fig-********-20161118-EQUINTANILLA-2">  
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="AA">select locale from settings;</sqlCheck>
        </preConditions>
        <comment>
            Para AAK por defecto queda la bandera por usuario encendida y la general prendida
        </comment>
        <update tableName="settings">
            <column name="allow_switch_search_subfolders" value="1"/>
        </update>
        <update tableName="users">
            <column name="search_in_subfolders" value="1"/>
        </update>
    </changeSet>
    <changeSet author="Aldo Germán Lares Lares" id="Fig-********-20161116-ALARES-1">
        <comment>
            Se agrega columna de password encriptado
        </comment> 
        <addColumn tableName="users">
            <column name="hashedpwd" type="varchar(60)"/>
        </addColumn>
    </changeSet>
    <changeSet id="Fig-********-20170104-EQUINTANILLA-1"
            author="Eduardo Guadalupe Quintanilla Flores">  
        <preConditions  onFail="MARK_RAN">
            <dbms type="mssql" />
        </preConditions>
        <comment>
            Se corrigen autorizaciones con fecha de aprovación sin haber sido aceptadas
        </comment>   
        <sql>
            <![CDATA[
                update autorization_pool_details set finish_date = null 
                where finish_date is not null and accepted is null and currentRecurrence = 1 and status = 1
            ]]>
        </sql>
    </changeSet> 
</databaseChangeLog>