<?xml version="1.0" encoding="UTF-8" ?>
<!--
    Module     : Documentos
    Created on : 14/01/2013, 10:56:57 AM
    Author     : <PERSON>
-->
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
    <package name="json-optimized" extends="json-bnext">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/>
                <interceptor-ref name="alias"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="i18n"/>
                <interceptor-ref name="prepare"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="scopedModelDriven"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="conversionError"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
                <interceptor-ref name="debugging"/>
            </interceptor-stack>
        </interceptors>
        <action name="OptDocumentRequest" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="OptBusinessUnitDepartment" class="DPMS.Document.CRUD_Request" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.BusinessUnitDepartment</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_BusinessUnitDepartment</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\[\d+\]\.id,
                    result\[\d+\]\.description,
                    result\[\d+\]\.formTemplateId
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
        <action name="OptRequest" class="DPMS.Document.CRUD_Request" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Request</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Request</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.type,
                    result\.data\[\d+\]\.fileContent\.id,
                    result\.data\[\d+\]\.fileContent\.description,
                    result\.data\[\d+\]\.fileContent\.extension,
                    result\.data\[\d+\]\.documentCode,
                    result\.data\[\d+\]\.document\.id,
                    result\.data\[\d+\]\.document\.fileId,
                    result\.data\[\d+\]\.document\.requestId,
                    result\.data\[\d+\]\.document\.surveyId,
                    result\.data\[\d+\]\.document\fileContent\.id,
                    result\.data\[\d+\]\.document\.fileContent\.description,
                    result\.data\[\d+\]\.document\.fileContent\.extension,
                    result\.data\[\d+\]\.creationDate,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.creationDate,
                    result\.data\[\d+\]\.author\.id,
                    result\.data\[\d+\]\.author\.description,
                    result\.data\[\d+\]\.businessUnit\.id,
                    result\.data\[\d+\]\.department\.id,
                    result\.data\[\d+\]\.department\.description,
                    result\.data\[\d+\]\.organizationalUnit\.id,
                    result\.data\[\d+\]\.documentType\.id,
                    result\.data\[\d+\]\.documentType\.documentControlledType,
                    result\.data\[\d+\]\.documentType\.downloadFiles,
                    result\.data\[\d+\]\.documentType\.description,
                    result\.data\[\d+\]\.nodo\.id,
                    result\.data\[\d+\]\.nodo\.code,
                    result\.data\[\d+\]\.nodo\.path, 
                    result\.data\[\d+\]\.reazon,
                    result\.data\[\d+\]\.version,
                    result\.data\[\d+\]\.storagePlaceId,
                    result\.data\[\d+\]\.retentionTime,
                    result\.data\[\d+\]\.retentionText,
                    result\.data\[\d+\]\.enablePdfViewer,
                    result\.data\[\d+\]\.fileId,
                    result\.data\[\d+\]\.surveyId,
                    result\.data\[\d+\]\.isBusy,
                    result\.data\[\d+\]\.blockedBy,
                    result\.data\[\d+\]\.generateCode,
                    result\.data\[\d+\]\.dynamicTableName,
                    result\.data\[\d+\]\.collectingAndStoreResponsible,
                    result\.data\[\d+\]\.collectingAndStoreResponsibleDescription,
                    result\.data\[\d+\]\.disposition,
                    result\.data\[\d+\]\.slimReportName,
                    result\.data\[\d+\]\.restrictRecordsByDepartment,
                    result\.data\[\d+\]\.validateAccessFormDepartment
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="OptMasterList" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.statisticsResults.*,
                    result\.parseFromDynamicResults,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.nodePath\.path,
                    result\.data\[\d+\]\.version,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.author\.id,
                    result\.data\[\d+\]\.author\.description,
                    result\.data\[\d+\]\.businessUnit\.description,
                    result\.data\[\d+\]\.businessUnit_id,
                    result\.data\[\d+\]\.businessUnit\.id,
                    result\.data\[\d+\]\.fileContent\.id,
                    result\.data\[\d+\]\.fileContent\.description,
                    result\.data\[\d+\]\.fileContent\.extension,
                    result\.data\[\d+\]\.fileContent_id,
                    result\.data\[\d+\]\.fileContent_description,
                    result\.data\[\d+\]\.fileContent_extension,
                    result\.data\[\d+\]\.department\.description,
                    result\.data\[\d+\]\.department\.id,
                    result\.data\[\d+\]\.department_id,
                    result\.data\[\d+\]\.originador\.description,
                    result\.data\[\d+\]\.creationDate,
                    result\.data\[\d+\]\.lastModificationDate,
                    result\.data\[\d+\]\.validTo,
                    result\.data\[\d+\]\.documentType\.id,
                    result\.data\[\d+\]\.documentType\.documentControlledType,
                    result\.data\[\d+\]\.documentType\.description,
                    result\.data\[\d+\]\.documentType\.dictionaryIndexId,
                    result\.data\[\d+\]\.documentType\.downloadFiles,
                    result\.data\[\d+\]\.retentionText,
                    result\.data\[\d+\]\.retentionTime,
                    result\.data\[\d+\]\.expirationDate,
                    result\.data\[\d+\]\.enablePdfViewer,
                    result\.data\[\d+\]\.storagePlaceId,
                    result\.data\[\d+\]\.fileId\.value,
                    result\.data\[\d+\]\.documentType_id,
                    result\.data\[\d+\]\.documentType_documentControlledType,
                    result\.data\[\d+\]\.documentType_description,
                    result\.data\[\d+\]\.documentType_dictionaryIndexId,
                    result\.data\[\d+\]\.documentType_downloadFiles,
                    result\.data\[\d+\]\.author\.id,
                    result\.data\[\d+\]\.author_id,
                    result\.data\[\d+\]\.author_description,
                    result\.data\[\d+\]\.businessUnit_description,
                    result\.data\[\d+\]\.department_description,
                    result\.data\[\d+\]\.originador_description,
                    result\.data\[\d+\]\.originador\.id,
                    result\.data\[\d+\]\.surveyId,
                    result\.data\[\d+\]\.nodoId,
                    result\.data\[\d+\]\.nodePath_id,
                    result\.data\[\d+\]\.nodePath_path,
                    result\.data\[\d+\]\.nodePath_description,
                    result\.data\[\d+\]\.file\.id,
                    result\.data\[\d+\]\.file_description,
                    result\.data\[\d+\]\.file_contentType,
                    result\.data\[\d+\]\.file_extension,
                    result\.data\[\d+\]\.dyn.*,
                    result\.data\[\d+\]\.C0.*,
                    result\.data\[\d+\]\.contadorHojaRuta,
                    result\.data\[\d+\]\.operacion,
                    result\.data\[\d+\]\.equipo,
                    result\.data\[\d+\]\.ubicacionTecnica,
                    result\.data\[\d+\]\.dynamicTableName
                    result\.data\[\d+\]\.fileId\.value,
                    result\.data\[\d+\]\.department\.processes\[\d+\]\.id,
                    result\.data\[\d+\]\.department\.processes\[\d+\]\.description,
                    result\.data\[\d+\]\.collectingAndStoreResponsible\.description,
                    result\.data\[\d+\]\.collectingAndStoreResponsible\.id,
                    result\.data\[\d+\]\.collectingAndStoreResponsibleDescription,
                    result\.data\[\d+\]\.informationClassification,
                    result\.data\[\d+\]\.disposition
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="LiteMasterList" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.parseFromDynamicResults,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.sharedDocumentStatus,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.sharedDocumentCode,
                    result\.data\[\d+\]\.nodePath\.path,
                    result\.data\[\d+\]\.version,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.sharedDocumentDescription,
                    result\.data\[\d+\]\.author\.id,
                    result\.data\[\d+\]\.author\.description,
                    result\.data\[\d+\]\.businessUnit\.description,
                    result\.data\[\d+\]\.businessUnit_id,
                    result\.data\[\d+\]\.fileContent\.id,
                    result\.data\[\d+\]\.fileContent\.description,
                    result\.data\[\d+\]\.fileContent\.extension,
                    result\.data\[\d+\]\.fileContent_id,
                    result\.data\[\d+\]\.fileContent_description,
                    result\.data\[\d+\]\.fileContent_extension,
                    result\.data\[\d+\]\.department\.description,
                    result\.data\[\d+\]\.department_id,
                    result\.data\[\d+\]\.originador\.description,
                    result\.data\[\d+\]\.creationDate,
                    result\.data\[\d+\]\.lastModificationDate,
                    result\.data\[\d+\]\.validTo,
                    result\.data\[\d+\]\.documentType\.id,
                    result\.data\[\d+\]\.documentType\.documentControlledType,
                    result\.data\[\d+\]\.documentType\.description,
                    result\.data\[\d+\]\.documentType\.dictionaryIndexId,
                    result\.data\[\d+\]\.documentType\.downloadFiles,
                    result\.data\[\d+\]\.retentionText,
                    result\.data\[\d+\]\.retentionTime,
                    result\.data\[\d+\]\.storagePlaceId,
                    result\.data\[\d+\]\.enablePdfViewer,
                    result\.data\[\d+\]\.fileId\.value,
                    result\.data\[\d+\]\.documentType_id,
                    result\.data\[\d+\]\.documentType_documentControlledType,
                    result\.data\[\d+\]\.documentType_description,
                    result\.data\[\d+\]\.documentType_dictionaryIndexId,
                    result\.data\[\d+\]\.documentType_downloadFiles,
                    result\.data\[\d+\]\.author_id,
                    result\.data\[\d+\]\.author_description,
                    result\.data\[\d+\]\.businessUnit_description,
                    result\.data\[\d+\]\.department_description,
                    result\.data\[\d+\]\.originador_description,
                    result\.data\[\d+\]\.surveyId,
                    result\.data\[\d+\]\.nodoId,
                    result\.data\[\d+\]\.nodePath_id,
                    result\.data\[\d+\]\.nodePath_path,
                    result\.data\[\d+\]\.nodePath_description,
                    result\.data\[\d+\]\.file_description,
                    result\.data\[\d+\]\.file_contentType,
                    result\.data\[\d+\]\.file_extension,
                    result\.data\[\d+\]\.dyn.*,
                    result\.data\[\d+\]\.C0.*,
                    result\.data\[\d+\]\.contadorHojaRuta,
                    result\.data\[\d+\]\.operacion,
                    result\.data\[\d+\]\.equipo,
                    result\.data\[\d+\]\.ubicacionTecnica,
                    result\.data\[\d+\]\.dynamicTableName
                    result\.data\[\d+\]\.fileId\.value,
                    result\.data\[\d+\]\.department\.processes\[\d+\]\.description
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        
        <!-- DOCUMENT (ACUSE DE RECIBO)-->
        <action name="OptReceiptAcknowledgment" class="DPMS.Document.CRUD_ReceiptAcknowledgment" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.ReceiptAcknowledgment</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.documentCode,
                    result\.data\[\d+\]\.documentVersion,
                    result\.data\[\d+\]\.readerUser\.description,
                    result\.data\[\d+\]\.reader\.userDescription,
                    result\.data\[\d+\]\.reader\.positionDescription,
                    result\.data\[\d+\]\.fileId,
                    result\.data\[\d+\]\.filePages,
                    result\.data\[\d+\]\.fileContent\.pdfSha512,
                    result\.data\[\d+\]\.documentPrintingId,
                    result\.data\[\d+\]\.reader\.id,
                    result\.data\[\d+\]\.document\.description,
                    result\.data\[\d+\]\.position\.description,
                    result\.data\[\d+\]\.position\.id,
                    result\.data\[\d+\]\.document\.businessUnit\.description,
                    result\.data\[\d+\]\.documentPrinting\.id,
                    result\.data\[\d+\]\.documentPrinting\.printLayout,
                    result\.data\[\d+\]\.documentPrinting\.printRotation
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
    
        <action name="OptReader" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\[\d+\]\.id\.*,
                    result\[\d+\]\.dateTime,
                    result\[\d+\]\.readed,
                    result\[\d+\]\.readedDate,
                    result\[\d+\]\.user\.description,
                    result\[\d+\]\.user\.workflowPosition\.description
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
        <action name="OptPhysicalReader" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\[\d+\]\.id,
                    result\[\d+\]\.status,
                    result\[\d+\]\.printDate,
                    result\[\d+\]\.reader\.userDescription,
                    result\[\d+\]\.reader\.positionDescription
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
    
        <action name="OptComplaint" class="DPMS.Catalog.CRUD_Complaint" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="activeServices" >intBAdmonSistema,catalogsComplaints,intBAdmonAccesos,intBLectorQueja,intBEditorQueja,intBEncargadoQueja</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.department\.department\.description,
                    result\.data\[\d+\]\.fechaCreacion,
                    result\.data\[\d+\]\.resultadoEvaluacion
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
                <param name="excludeProperties">
                    result.*\.documents
                </param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
    
        <action name="OptSequence" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.fechaHoraCreacion,
                    result\.data\[\d+\]\.documentDescription,
                    result\.data\[\d+\]\.requestId,
                    result\.data\[\d+\]\.requestType,
                    result\.data\[\d+\]\.documentCode,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.autorizaciones\[\d+\]\.flujo\.flujoPoolList\[\d+\]\.delay,
                    result\.data\[\d+\]\.autorizaciones\[\d+\]\.status,
                    result\.data\[\d+\]\.autorizaciones\[\d+\]\.fechaHoraAutorizacion,
                    result\.data\[\d+\]\.autorizaciones\[\d+\]\.creationDate,
                    result\.data\[\d+\]\.flujo\.flujoPoolList\[\d+\]\.delay,
                    result\.data\[\d+\]\.flujo\.flujoPoolList\[\d+\]\.workflowPoolIndex,
                    result\.data\[\d+\]\.flujo\.flujoPoolList\[\d+\]\.owners\[\d+\]\.status,
                    result\.data\[\d+\]\.flujo\.flujoPoolList\[\d+\]\.owners\[\d+\]\.positions\[\d+\]\.position\.description,
                    result\.data\[\d+\]\.flujo\.flujoPoolList\[\d+\]\.owners\[\d+\]\.users\[\d+\]\.user\.description,
                    result\.data\[\d+\]\.sequenceDetails\[\d+\]\.id\.indice,
                    result\.data\[\d+\]\.sequenceDetails\[\d+\]\.finishDate,
                    result\.data\[\d+\]\.sequenceDetails\[\d+\]\.userDescription,
                    result\.data\[\d+\]\.sequenceDetails\[\d+\]\.positionDescription,
                    result\.data\[\d+\]\.sequenceDetails\[\d+\]\.status
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
        <!-- Template -->
        <action name="OptTemplate" class="DPMS.Catalog.CRUD_Template" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Workflow</param>
            <param name="activeServices" >intBAdmonSistema,catalogsDocuments,intBAdmonAccesos</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.scope,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.businessUnit\.id,
                    result\.data\[\d+\]\.businessUnit\.description,
                    result\.data\[\d+\]\.organizationalUnit\.id,
                    result\.data\[\d+\]\.organizationalUnit\.description
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
        <action name="OptTemplate-Localize" class="DPMS.Catalog.CRUD_Template" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Workflow</param>
            <param name="activeServices" >intBAdmonSistema,catalogsDocuments,intBAdmonAccesos</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
    
        <!-- DEPARTMENT / PROCESS -->
        <action name="OptDepartmentProcess" class="DPMS.Catalog.CRUD_DepartmentProcess" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DepartmentProcessLoad</param>
            <param name="activeServices" >intBAdmonSistema,intBAdmonAccesos</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.process\.code,
                    result\.data\[\d+\]\.process\.description,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.department\.description,
                    result\.data\[\d+\]\.department\.businessUnit\.description,
                    result\.data\[\d+\]\.attendant\.description
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    
        <action name="OptAction" class="DPMS.Actions.CRUD_Action" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Action</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorAccion,intBEditorAccion,intBEncargadoAccion</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.department\.description,
                    result\.data\[\d+\]\.author\.description,
                    result\.data\[\d+\]\.fuente\.description,
                    result\.data\[\d+\]\.source\.description,
                    result\.data\[\d+\]\.system\.description,
                    result\.data\[\d+\]\.fechaCreacion,
                    result\.data\[\d+\]\.reported_on
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="OptSimpleAction" class="DPMS.Actions.CRUD_Action" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.Action</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorAccion,intBEditorAccion,intBEncargadoAccion</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.department\.description,
                    result\.data\[\d+\]\.author\.description,
                    result\.data\[\d+\]\.fuente\.description,
                    result\.data\[\d+\]\.source\.description,
                    result\.data\[\d+\]\.system,
                    result\.data\[\d+\]\.fechaCreacion,
                    result\.data\[\d+\]\.reported_on
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
        <action name="OptFilteredDocumentReaders" class="DPMS.Document.CRUD_Document" method="smd">
            <interceptor-ref name="bnextStack" />
            <param name="className">DPMS.Mapping.DocumentSimple</param>
            <param name="daoClassName">DPMS.DAO.HibernateDAO_Document</param>
            <param name="activeServices" >intBAdmonSistema,intBLectorDocumento,intBEditorDocumento,intBEncargadoDocumento</param>
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="includeProperties">
                    debug,
                    error,
                    id,
                    result\.consolidates,
                    result\.count,
                    result\.gridId,
                    result\.parseFromDynamicResults,
                    result\.data\[\d+\]\.id,
                    result\.data\[\d+\]\.status,
                    result\.data\[\d+\]\.code,
                    result\.data\[\d+\]\.version,
                    result\.data\[\d+\]\.description,
                    result\.data\[\d+\]\.authorDescription
                </param>
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
            </result>
            <result name="inicio">/index.jsp</result>
        </action>
    </package>
</struts>