repeatedData=The request has data that already exists in the application
repeatedDataCode1=the code exists in a document of the master list
repeatedDataCode2=the code exists in another application of new document
repeatedDataTitle1=the title exists in a document from the master list
repeatedDataTitle2=the title exists in another new document request
rejectReason = Request rejected automatically by Bnext QMS, at verifying request with description: \n:reason 
rejectFillReason = The request to fill out the form with ID :formCode was automatically rejected by the Bnext QMS\u00ae system, at the time of publishing the new version :version
rejectedReason =  / Rejected
automaticAuthorizationByInactiveUser = Automatic system authorization because the user :name: is idle.
automaticAuthorizationByPosition = Authorization has been done automatically because there's no users at position(s) :name:.
