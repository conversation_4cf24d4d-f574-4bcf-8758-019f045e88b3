#pendientes de acciones
ACC-FINDING-TO-ASSIGN = Actions to assign responsible
ACC-FINDING-TO-START-IMPLEMENTATION = Actions to add immediate correction action
ACC-FINDING-TO-ANALYZE = Actions to analyze
ACC-FINDING-TO-ADD-PLAN = Actions to choose an activity
ACC-FINDING-TO-EVALUATE = Actions to accept
FINDING-ACTIVITY-TO-VERIFY = Activities to verify
FINDING-ACTIVITY-TO-COMPLETE = Activities to complete
FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY = Actions to accept cancellation
#pendientes de quejas
COMPLAINT-TO-ASSIGN = Complaints to assign responsible
COMPLAINT-TO-RESPOND = Complaints to answer
COMPLAINT-TO-VERIFY = Complaints to verify answer
COMPLAINT-TO-EVALUATE = Handled complaints to evaluate effectiveness
#cuerpo del reporte
title = Escalation of ${module} module 
subtitle = Tasks escalated today:
table_code = ID
table_times = Escalated times
table_commitment = Commitment
table_from = Escalated from:
table_to = Escalated to:
table_scalate = Escalation
table_task = Delayed tasks
footer_author = Powered by Bnext QMS
footer_page = Page
footer_from = to
escalated_mode_mail = Mail notification
escalated_mode_task = Escalated task
description_activity = activities
description_poll = surveys
description_meeting = meetings
description_document = documents
description_action = The "${entityTypeName}" finding of the ${entityBusinessUnitName} plant and department ${entityDepartmentName} with source was "${entitySourceCode}" has escalated into the next tasks today.
description_complaint = The complaint of the ${entityBusinessUnitName} plant and department ${entityDepartmentName} with source was "${entitySourceCode}" has escalated into the next tasks today.
description_meter = indicators
description_project = projects
description_device = devices
description_formularie = forms
description_fiveS = 5S+1
#modulos
audit = audits
activity = activities
poll = surveys
meeting = meetings
document = documents
action = findings
complaint = quejas,
meter = complaints
project = projects
device = devices
formularie = forms
fiveS = 5S+1