<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.19.0.final using JasperReports Library version 6.19.0-646c68931cebf1a58bc65c4359d1f0ca223c5e94  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="licensing_adjustment_report" pageWidth="595" pageHeight="950" whenNoDataType="AllSectionsNoDetail" columnWidth="565" leftMargin="15" rightMargin="15" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" isFloatColumnFooter="true" resourceBundle="licensing_adjustment_report" whenResourceMissingType="Key" uuid="e5fd9931-0d36-4c0f-8b4e-ee79a4cef369">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="BnextgQMS_X"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#008000">
		<pen lineWidth="0.2" lineStyle="Solid"/>
		<box>
			<pen lineWidth="0.5" lineColor="#008000"/>
			<topPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
			<leftPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
			<bottomPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
			<rightPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#E0E0E0">
		<pen lineWidth="0.2" lineStyle="Solid"/>
		<box>
			<pen lineWidth="0.5" lineColor="#008000"/>
			<topPen lineWidth="0.3" lineStyle="Solid" lineColor="#FFFFFF"/>
			<leftPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
			<bottomPen lineWidth="0.3" lineStyle="Solid" lineColor="#FFFFFF"/>
			<rightPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#E0E0E0">
		<pen lineWidth="0.2" lineStyle="Solid"/>
		<box>
			<pen lineWidth="0.5" lineColor="#008000"/>
			<topPen lineWidth="0.3" lineStyle="Solid" lineColor="#FFFFFF"/>
			<leftPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
			<bottomPen lineWidth="0.3" lineStyle="Solid" lineColor="#FFFFFF"/>
			<rightPen lineWidth="0.3" lineStyle="Solid" lineColor="#008000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#FF0000">
		<pen lineWidth="0.2" lineStyle="Solid"/>
		<box>
			<pen lineWidth="0.5" lineColor="#FF0000"/>
			<topPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
			<leftPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
			<bottomPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
			<rightPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#E0E0E0">
		<pen lineWidth="0.2" lineStyle="Solid"/>
		<box>
			<pen lineWidth="0.5" lineColor="#FF0000"/>
			<topPen lineWidth="0.3" lineStyle="Solid" lineColor="#FFFFFF"/>
			<leftPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
			<bottomPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
			<rightPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#E0E0E0">
		<pen lineWidth="0.2" lineStyle="Solid"/>
		<box>
			<pen lineWidth="0.5" lineColor="#FF0000"/>
			<topPen lineWidth="0.3" lineStyle="Solid" lineColor="#FFFFFF"/>
			<leftPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
			<bottomPen lineWidth="0.3" lineStyle="Solid" lineColor="#FFFFFF"/>
			<rightPen lineWidth="0.3" lineStyle="Solid" lineColor="#FF0000"/>
		</box>
	</style>
	<subDataset name="dataTable" uuid="fe3be4cd-3f07-498e-8a78-863c1153f003">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="BnextgQMS_X"/>
		<parameter name="SCHEMA" class="java.lang.String">
			<parameterDescription><![CDATA[]]></parameterDescription>
		</parameter>
		<queryString language="SQL">
			<![CDATA[SELECT STUFF((
			SELECT DISTINCT ', ' + b.description
			FROM usuario_puesto up
			JOIN puesto p ON up.puesto_id = p.puesto_id
			JOIN business_unit b ON b.business_unit_id = p.business_unit_id
			WHERE u.user_id = up.usuario_id
			FOR XML PATH(''),
				TYPE
			).value('(./text())[1]', 'VARCHAR(MAX)'), 1, 2, '') AS busines_description,
	u.first_name AS NAME,
	MAX(a.tspfechaentrada) AS last_login,
	COUNT(a.inthistorialaccesoid) AS logins
FROM users u
LEFT JOIN tblhistorialacceso a ON a.intusuarioid = u.user_id
WHERE u.STATUS = 1
	AND license_code = $P!{SCHEMA}
GROUP BY u.user_id,
	u.first_name
ORDER BY u.first_name]]>
		</queryString>
		<field name="busines_description" class="java.lang.String"/>
		<field name="name" class="java.lang.String"/>
		<field name="last_login" class="java.util.Date"/>
		<field name="logins" class="java.lang.Integer"/>
		<variable name="countRowsN" class="java.lang.Integer">
			<variableExpression><![CDATA[$V{countRowsN} + 1]]></variableExpression>
			<initialValueExpression><![CDATA[0]]></initialValueExpression>
		</variable>
	</subDataset>
	<subDataset name="dataTable2" uuid="7a44cd7c-a877-485f-a0c6-94d3bdc3813a">
		<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="BnextgQMS_X"/>
		<parameter name="SCHEMA" class="java.lang.String"/>
		<queryString language="SQL">
			<![CDATA[SELECT STUFF((
			SELECT DISTINCT ', ' + b.description
			FROM usuario_puesto up
			JOIN puesto p ON up.puesto_id = p.puesto_id
			JOIN business_unit b ON b.business_unit_id = p.business_unit_id
			WHERE u.user_id = up.usuario_id
			FOR XML PATH(''),
				TYPE
			).value('(./text())[1]', 'VARCHAR(MAX)'), 1, 2, '') AS busines_description,
	u.first_name AS NAME,
	MAX(a.tspfechaentrada) AS last_login,
	COUNT(a.intusuarioid) AS logins,
	u.inactive_by_system AS inactive_system
FROM users u
LEFT JOIN tblhistorialacceso a ON a.intusuarioid = u.user_id
WHERE u.STATUS = 0
	AND license_code = $P!{SCHEMA}
GROUP BY u.user_id,
	u.first_name,
	u.inactive_by_system
ORDER BY u.first_name]]>
		</queryString>
		<field name="busines_description" class="java.lang.String"/>
		<field name="name" class="java.lang.String"/>
		<field name="last_login" class="java.util.Date">
			<fieldDescription><![CDATA[]]></fieldDescription>
		</field>
		<field name="logins" class="java.lang.Integer"/>
		<field name="inactive_system" class="java.lang.Integer"/>
		<variable name="countRowsN" class="java.lang.Integer">
			<variableExpression><![CDATA[$V{countRowsN} + 1]]></variableExpression>
			<initialValueExpression><![CDATA[0]]></initialValueExpression>
		</variable>
	</subDataset>
	<parameter name="SYSTEM_LOGO" class="java.lang.String">
		<defaultValueExpression><![CDATA["qms.png"]]></defaultValueExpression>
	</parameter>
	<parameter name="SYSTEM_COLOR" class="java.lang.String"/>
	<parameter name="SCHEMA" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="LEGEND" class="java.lang.String">
		<parameterDescription><![CDATA[]]></parameterDescription>
	</parameter>
	<parameter name="NUMBER_LICENSES" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[select 
 (select count(1) from users u where u.status = 1 AND u.license_code =  $P!{SCHEMA}) as active_users,
 (select count(1) from users u where u.status = 0 AND u.license_code =  $P!{SCHEMA}) as inactive_users,
 (select count(1) from users u where u.status = 0 AND u.inactive_by_system = 1 AND u.license_code =  $P!{SCHEMA}) as inactive_system_users
from dual]]>
	</queryString>
	<field name="active_users" class="java.lang.Integer"/>
	<field name="inactive_users" class="java.lang.Integer"/>
	<field name="inactive_system_users" class="java.lang.Integer"/>
	<pageHeader>
		<band height="243">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER}.intValue() == 1? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
			<image>
				<reportElement x="0" y="0" width="172" height="86" uuid="ebfdd9a8-d199-4ff2-b0b8-81b0b1a4b19d">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<imageExpression><![CDATA[$P{SYSTEM_LOGO}]]></imageExpression>
			</image>
			<textField>
				<reportElement positionType="Float" x="172" y="0" width="392" height="60" forecolor="#00003A" uuid="c741f21f-60c5-419a-9ff9-0a76712e7bd2"/>
				<textElement>
					<font fontName="Arial" size="22"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{title} +" [" +$P{SCHEMA} + "]."]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="172" y="60" width="392" height="26" forecolor="#AEAAAA" uuid="e4fc1238-cbf2-4a01-a2e2-91e273985256"/>
				<textElement>
					<font fontName="Arial" size="18"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{subtitle}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement positionType="Float" x="-15" y="86" width="595" height="6" backcolor="#D1120F" uuid="0335a511-128f-497f-a00e-95d5029039db">
					<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{SYSTEM_COLOR} == "" || $P{SYSTEM_COLOR} == null ? "#D1120F" : $P{SYSTEM_COLOR}]]></propertyExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement positionType="Float" x="0" y="98" width="565" height="132" backcolor="#E0E0E0" uuid="ef18a112-6975-4828-8f28-84cea498c2d3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement positionType="Float" x="5" y="104" width="554" height="50" uuid="b7fcd148-def0-4268-a077-4d9c677fd5d2">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{LEGEND}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="5" y="160" width="216" height="15" forecolor="#002DB2" uuid="18433a5c-8a6f-4878-b685-315e26420741"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{number_licenses}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="5" y="175" width="216" height="15" forecolor="#002DB2" uuid="78b87168-8678-49db-8b8e-39c97fa1158d"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{active_users}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="5" y="190" width="216" height="15" forecolor="#002DB2" uuid="78b87168-8679-49db-8b7e-39c97fa1158d"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{inactive_users}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="5" y="205" width="217" height="15" forecolor="#002DB2" uuid="ed49fd04-8494-4e29-9eff-8b553303ac4f"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$R{users_disabled}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement positionType="Float" x="226" y="160" width="141" height="15" forecolor="#002DB2" uuid="0ac39e7c-f0e9-467d-be4d-1590265d884b"/>
				<textElement>
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{NUMBER_LICENSES}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement positionType="Float" x="226" y="175" width="141" height="15" forecolor="#002DB2" uuid="ac343e7c-f0e9-465d-be4d-1590265d884b"/>
				<textElement>
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{active_users}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement positionType="Float" x="226" y="190" width="141" height="15" forecolor="#002DB2" uuid="bf7bff8c-bf38-42f9-a374-3cbb4a04fedf"/>
				<textElement>
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inactive_users}]]></textFieldExpression>
			</textField>
			<textField pattern="">
				<reportElement positionType="Float" x="226" y="205" width="141" height="15" forecolor="#002DB2" uuid="83710d60-8bf7-4913-af09-24f34cae3800"/>
				<textElement>
					<font fontName="Arial" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inactive_system_users}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<pageFooter>
		<band height="20">
			<staticText>
				<reportElement x="0" y="6" width="160" height="14" forecolor="#B3B3B3" uuid="9b2287a4-077c-4fd6-aa51-9671a91cb5c3"/>
				<textElement>
					<font size="8"/>
				</textElement>
				<text><![CDATA[Reporte generado por BnextQMS®]]></text>
			</staticText>
			<textField>
				<reportElement x="372" y="6" width="160" height="14" forecolor="#B3B3B3" uuid="6bfadd88-456d-4a29-9f44-214a9b9aca4d"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Página "+$V{PAGE_NUMBER}+" de"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="532" y="6" width="33" height="14" forecolor="#B3B3B3" uuid="f2dd15cd-0e0d-4e12-b2a3-7f8c6b3bb749"/>
				<textElement verticalAlignment="Middle">
					<font size="8" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="-15" y="0" width="595" height="6" backcolor="#D1120F" uuid="f804a683-e1f4-4187-a6b2-dd5eb963448c">
					<propertyExpression name="net.sf.jasperreports.style.backcolor"><![CDATA[$P{SYSTEM_COLOR} == "" || $P{SYSTEM_COLOR} == null ? "#D1120F" : $P{SYSTEM_COLOR}]]></propertyExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
		</band>
	</pageFooter>
	<summary>
		<band height="132">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<componentElement>
				<reportElement x="0" y="21" width="564" height="49" backcolor="#FFFFFF" uuid="80d4c877-1578-4e26-a7bc-2bf2ba400b19">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="dataTable" uuid="f558dec1-89a9-4cf5-94c2-1f5dd3ad2c0d">
						<datasetParameter name="SCHEMA">
							<datasetParameterExpression><![CDATA[$P{SCHEMA}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:column width="563" uuid="c394f2b5-9e58-4fcb-a757-d7c32e1b324f">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
						<jr:tableHeader style="Table_TH" height="20" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="563" height="20" forecolor="#FFFFFF" uuid="c04a0c01-3887-4b31-a736-fc18264f2d1c"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="12" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{active_column_title}.toUpperCase()]]></textFieldExpression>
							</textField>
						</jr:tableHeader>
						<jr:columnHeader style="Table_CH" height="12" rowSpan="1">
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<textField>
								<reportElement x="220" y="0" width="210" height="12" uuid="b784a41d-be3f-4f1f-bfc4-1faada97ba4f">
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Left">
									<font fontName="Arial" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{business_unit}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="30" y="0" width="190" height="12" uuid="a6b092d3-a085-4b75-ba47-54a4bcb2e605">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Left">
									<font fontName="Arial" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{name}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="430" y="0" width="100" height="12" uuid="12b9e8f1-beed-4914-980f-ea12b166a7ec"/>
								<textElement textAlignment="Center">
									<font fontName="Arial" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{last_login}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="530" y="0" width="33" height="12" uuid="f79d44cf-15ad-4b75-9322-4e659d5a316e"/>
								<textElement textAlignment="Center">
									<font fontName="Arial" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{logins}]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="12">
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
								<reportElement x="220" y="0" width="210" height="12" uuid="d892e8f9-8013-4bf7-96d3-6b5f738ac852"/>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{busines_description} == null  ? "" : $F{busines_description}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement positionType="Float" x="30" y="0" width="190" height="12" uuid="73b13e00-8cc3-4c56-a895-a136a7990b0a">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Left" verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{name} == null? "" : $F{name}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement x="0" y="0" width="30" height="12" uuid="271df7c1-ef6f-44b0-9bdb-0fdfa887289a"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{countRowsN}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" pattern="dd/MM/yyyy" isBlankWhenNull="true">
								<reportElement key="" x="430" y="0" width="100" height="12" uuid="7d89d6e3-ec57-4d68-a8b4-7b88a647f187"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{last_login} == null ? "" :$F{last_login}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement x="530" y="0" width="33" height="12" uuid="7446f106-7b29-416a-b537-7a640d9026bd"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{logins} == null ? 0 : $F{logins}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
			<componentElement>
				<reportElement positionType="Float" x="0" y="70" width="564" height="54" uuid="c9573dfe-02ed-4996-a9ad-0cf523f0fcc8">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="dataTable2" uuid="b145eaac-611c-4c1d-8c45-a279e6158eaa">
						<datasetParameter name="SCHEMA">
							<datasetParameterExpression><![CDATA[$P{SCHEMA}]]></datasetParameterExpression>
						</datasetParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					</datasetRun>
					<jr:column width="564" uuid="d835eed0-7b4d-404b-aa2f-7a34d7a88467">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:tableHeader style="Table 1_TH" height="20" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="564" height="20" forecolor="#FFFFFF" uuid="a51fdfec-55e0-47d8-ae41-980b4b572eb0"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="12" isBold="true"/>
									<paragraph lineSpacing="Single"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{inactive_column_title}.toUpperCase()]]></textFieldExpression>
							</textField>
						</jr:tableHeader>
						<jr:columnHeader style="Table 1_CH" height="12">
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<textField>
								<reportElement x="180" y="0" width="160" height="12" uuid="69744fbe-4b21-4b9e-a97d-007ba55afa93">
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement>
									<font fontName="Arial" size="10" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{business_unit}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="30" y="0" width="150" height="12" uuid="071a37ad-bbb5-4fb6-9d97-cb1bb2464626">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement>
									<font fontName="Arial" size="9" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{name}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="440" y="0" width="90" height="12" uuid="528958e8-0797-4ee6-ab4e-0548b8f9f676">
									<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center">
									<font fontName="Arial" size="9" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{last_login}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="530" y="0" width="34" height="12" uuid="2b45e3e2-6754-483f-9b2a-7c8421e7116e"/>
								<textElement textAlignment="Center">
									<font fontName="Arial" size="9" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{logins}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="340" y="0" width="100" height="12" uuid="7f8ab2b4-0727-45ff-a9cd-da060a5b4abf"/>
								<textElement>
									<font fontName="Arial" size="9" isBold="true"/>
								</textElement>
								<textFieldExpression><![CDATA[$R{inactive_system}]]></textFieldExpression>
							</textField>
						</jr:columnHeader>
						<jr:detailCell style="Table 1_TD" height="12">
							<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement x="0" y="0" width="30" height="12" uuid="23660070-66e8-4f92-95e7-93cd5eb7d863">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{countRowsN}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
								<reportElement x="180" y="0" width="160" height="12" uuid="4200a5d0-fd0b-46ec-b4e7-19539700578b">
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{busines_description} == null  ? "" : $F{busines_description}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement x="30" y="0" width="150" height="12" uuid="9de99f32-9ae5-416b-aa64-9a7e29058d06">
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								</reportElement>
								<textElement verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{name} == null ? "" : $F{name}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" pattern="dd/MM/yyyy" isBlankWhenNull="false">
								<reportElement positionType="Float" x="440" y="0" width="90" height="12" uuid="fe3e6c9a-6f3e-4fe7-bded-430b67bd8c7f">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{last_login} == null ? "" : $F{last_login}]]></textFieldExpression>
							</textField>
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement x="530" y="0" width="34" height="12" uuid="8976cc79-66f5-4bfc-b22f-69a2b09416bf">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
									<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font fontName="Arial" size="9"/>
									<paragraph lineSpacing="Double" spacingAfter="4"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{logins} == null ? 0 : $F{logins}]]></textFieldExpression>
							</textField>
							<textField>
								<reportElement x="340" y="0" width="100" height="12" uuid="e5cbcabd-4901-4f3d-a44a-a9cd41959a05"/>
								<textElement>
									<font fontName="Arial" size="9"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{inactive_system} == null ||  $F{inactive_system} != 1 ?  $R{no} :  $R{yes}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</summary>
</jasperReport>
