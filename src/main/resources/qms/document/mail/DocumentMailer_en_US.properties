header = Notification from DOCUMENTS module
subject = Notification from documents
label.title = DOCUMENT DETAILS
label.list.title = DOCUMENTS DETAILS
label.code = ID
label.title = Title
label.lastModificationDate = Last modification
label.expirationOn = Expiration on
title.readersAddedUncontrolledDocument = You have a controlled document pending to read.
title.readersAddedEditionDocument = You have a waiting-for-authorization document pending to read.
title.readersAddedActiveDocument = You have an uncontrolled document pending to read.
subject.documentToRead = Document to read
subject.expireSoon = Documento to renew soon
title.renewByModuleManagerPending = There are <b>:number</b> expired documents in <b>:systemId</b> system , with :month or more months pending in which you are the documents manager assigned to verify them.
title.renewByModuleManagerNotice = There are <b>:number</b> expired documents in <b>:systemId</b> system , with :month or more months old in which you are the Document Module Manager.
title.renewByDocumentManagerPending = There are <b>:number</b> expired documents in the <b>:systemId</b> system , from :month or more months ago in which you are the responsible assigned of the Documents Modile and must verify them.
title.renewByDocumentManagerNotice = There are <b>:number</b> expired documents in the <b>:systemId</b> system , from :month or more months ago in which you are the responsible of the Documents Module.
title.renewByAuthor = There are <b>:number</b> expired documents in the <b>:systemId</b> system , from :month or more months pending in which you are the author of current adition assigned.
title.expireSoonByModuleManager = &nbsp;&nbsp;There are <b>:number</b> documents expiring in the system. Please update them before they expire. This pending comes to you for being the <b>documents module manager</b><br/><br/>
title.expireSoonByDocumentManager = &nbsp;&nbsp;There are <b>:number</b> documents expiring in the system. Please update them before they expire. This pending comes to you for being the <b>documents manager</b> <br/><br/>
title.expireSoonByAuthor = There are <b>:number</b> documents expiring in which you are author of the <b>current version</b>, please update them before they expire. <br/><br/>
label.description=Title
label.version=Version
title.requestedPickUpCopies = You have <b>:number</b> controlled copies to pick up<br/><br/>
title.pickUpPhysicalCop\u00edes = You have <b>:number</b> controlled copies to pick up<br/><br/>
label.documentCode=Document ID
label.documentDescription=Document title
label.documentVersion=Document version
label.position=Position
label.user=User
label.businessUnit = ${Facility}
label.status = Status
label.author = Author
label.department = Department
label.type = Type
label.retentionTime = Time
label.retentionText = Time unit
timeUnit.1 = Days
timeUnit.2 = Months
timeUnit.3 = Years
status.active = Active
status.in_edition = In edition
status.authorization = In authorization
status.canceled = Cancelled
status.canceled_discontinued = Discontinued of cancelled
status.discontinued = Discontinued