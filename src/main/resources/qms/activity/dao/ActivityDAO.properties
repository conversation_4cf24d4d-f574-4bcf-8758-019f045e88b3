changeImplementerToStart = Se cambia responsable de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeImplementerToComplete = Se cambia responsable de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeImplementer = Se cambia responsable de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
removedImplementer = Se elimin\u00f3 el responsable {previous}. La raz\u00f3n del cambio fue: {comment}.
addedImplementer = Se agreg\u00f3 un nuevo responsable {current}. La raz\u00f3n del cambio fue: {comment}.
changeVerifierToStart = Se cambia verificador de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeVerifierToComplete = Se cambia verificador de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeVerifierToVerify = Se cambia verificador de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeVerifierNotApply = Se cambia verificador en una actividad cancelada de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeVerifier = Se cambia verificador de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeReminder = Se cambia fecha de implementaci\u00f3n de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeReminderPlanner = Se cambia fecha de t\u00e9rmino de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeImplementation = Se cambia fecha planeada de implementaci\u00f3n de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeImplementationStart = Se cambia fecha de inicio de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeImplementationVerification = Se cambia fecha de implementaci\u00f3n de {previous} a {current} y verificaci\u00f3n de {previousVerification} a {currentVerification}. La raz\u00f3n del cambio fue: {comment}.
changeVerification = Se cambia fecha de verificaci\u00f3n de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
changeDescription = Se cambia descripci\u00f3n de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeBusinessUnit =  Se cambia ${facility} de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeBusinessUnitDepartment =  Se cambia departamento de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeCategory =  Se cambia categor\u00eda de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeSource =  Se cambia fuente de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeObjective =  Se cambia objetivo de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeIsPlanned =  Se cambia \"Es planeada\" de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changePlannedHours =  Se cambia horas estimadas de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeSystemLink =  Se cambia Ligas a otros sistemas de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changePlannerId =  Se cambia Proyecto de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeClientId =  Se cambia Cliente de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeParentActivityTaskId =  Se cambia Tarea de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changePriority =  Se cambia prioridad de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
changeDynamicField =  Se cambia de valor el campo \"{fieldName}\" de \"{previous}\" a \"{current}\". La raz\u00f3n del cambio fue: {comment}.
activityTaskCancelFillByAuthor = La solicitud de llenado fue cancelada al ser eliminada la actividad originadora por el solicitante del llenado del formulario :user
activityTaskCancelFillGeneric = La solicitud de llenado fue cancelada al ser eliminada la actividad originadora por :user
activityTaskCancelFillNotApplyByAuthor = La solicitud de llenado fue cancelada por la actividad originadora por el solicitante del llenado del formulario :user
activityTaskCancelFillNotApplyGeneric = La solicitud de llenado fue cancelada por la actividad originadora por :user
verifiedFinishedAs-VERIFY = Se verifica la actividad
verifiedFinishedAs-INCOMPLETE = Se determina que la actividad est\u00e1 incompleta
verifiedFinishedAs-UNDONE = Se determina que la actividad no ha sido realizada
verifiedFinishedAs-NOT_APPLY_VERIFY = Se determina que la actividad fue verificada como cancelada
verifiedFinishedAs-APPLY_VERIFY = Se ha rechazado la cancelaci\u00f3n de la actividad
doneParentActivity = Se concluyeron las subtareas.
returnActivityParent =  Se agreg\u00f3 una nueva actividad anidada.
yesNo.true = S\u00ed
yesNo.false = No
VERIFY_ACTION = Se verifica la implementaci\u00f3n
APPLY_VERIFY_ACTION = Se retorna la actividad
NOT_APPLY_VERIFY_ACTION = Se verifica la cancelaci\u00f3n de la actividad
INCOMPLETE_AND_COMPLETE_ACTION = Avance verificado al {progress}%
INCOMPLETE_ACTION = Se verifica la implementaci\u00f3n
UNDONE_ACTION = Se marca como no realizada
ADVANCE_ACTIVITY_ACTION = Se registr\u00f3 un avance del {progress}% con el comentario: {comment}
ADVANCE_ACTIVITY_ACTION_EMPTY_COMMENT = Se registr\u00f3 un avance del {progress}%
DONE_ACTIVITY_ACTION = Se marc\u00f3 la actividad como Realizada con el comentario: {comment}
DONE_ACTIVITY_ACTION_EMPTY_COMMENT = Se marc\u00f3 la actividad como Realizada
VERIFY_ACTION_COMMENT = Se verific\u00f3 la implementaci\u00f3n {description} con el comentario: {comment}
APPLY_VERIFY_ACTION_COMMENT = Se retorna la actividad con el comentario: {comment}
NOT_APPLY_VERIFY_ACTION_COMMENT = Se verific\u00f3 la cancelaci\u00f3n de la actividad con el comentario: {comment}
INCOMPLETE_AND_COMPLETE_ACTION_COMMENT = Se marc\u00f3 la actividad como Incompleta al {progress}% con el comentario: {comment}
UNDONE_ACTION_COMMENT = Se marc\u00f3 la actividad como No Realizada con el comentario: {comment}
INCOMPLETE_ACTION_COMMENT = Se verific\u00f3 la implementaci\u00f3n con el comentario: {comment}
ACTIVITY_RESOLUTION =  y con la resoluci\u00f3n: {resolution}
notApply = Se determina que la actividad fue cancelada. La raz\u00f3n fue: {comment}.
removedDocument = Se elimina relaci\u00f3n con el documento con t\u00edtulo "{description}" y versi\u00f3n "{version}".
addedDocument = Se agrega relaci\u00f3n con el documento con t\u00edtulo "{description}" y versi\u00f3n "{version}".
removedFile = Se elimina archivo "{description}".
addedFile = Se agrega archivo "{description}".
removedComment =  Se elimina comentario "{description}".
addedComment = Se agrega comentario "{description}".
editedComment = Se cambio comentario "{descriptionPrev}" por "{descriptionNew}".
moveChildAddedSubTask = Se agreg\u00f3 la actividad \"{subTask}\" como subtarea.
moveChildAddedAsSubTask = La actividad ahora es una subtarea de \"{activity}\".
cancellationByChangePlannedHours = Se ajustar\u00e1n las horas estimadas de otra actividad.
cancellationByChangePlannedHoursList = Se ajustar\u00e1n las horas estimadas de la siguiente(s) actividad(es): {activitiesList}
changedResolutionTo = Resoluci\u00f3n modificada a
CLOSE_ACTIVITY_MAIN = Se cambia el valor de la resoluci\u00f3n de \"{previousResolution}\" a \"{newResolution}\". La raz\u00f3n del cambio fue: {comment}
PROGRAM_CREATED = Actividad principal creada.
IMPLEMENTATION_CREATED = Implementaci\u00f3n creada.
VERIFICATION_CREATED = Verificaci\u00f3n creada.
reassignVerification = Reasignaci\u00f3n de verificaci\u00f3n
changePreImplementer = Se cambia responsable pre-asignado de {previous} a {current}. La raz\u00f3n del cambio fue: {comment}.
removedPreImplementer = Se elimin\u00f3 el responsable pre-asignado {previous}. La raz\u00f3n del cambio fue: {comment}.
addedPreImplementer = Se agreg\u00f3 un nuevo responsable pre-asignado {current}. La raz\u00f3n del cambio fue: {comment}.
changeImplementationDate = Se actualiza la fecha de implementaci\u00f3n