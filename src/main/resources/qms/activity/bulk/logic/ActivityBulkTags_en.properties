weekOfYearGroupName = Week {week} of {year}
yes = Yes
no = No
PROGRESS = Report percentage of progress
CHILDS = Activities list
FILL_FORM = Fill form
MARK_DONE = Mark done or not done
PLANNER = Planner tracking
PLANNER_TASK = Task tracking
ROW_ID = ID
ROW_PARENT = Parent    
ROW_TYPE = Activity type
ROW_CODE = Code
ROW_DESCRIPTION = Description
ROW_IMPLEMENTERS = Implementer(s)
ROW_VERIFIER = Verifier
ROW_START_DATE = Start date
ROW_IMPLEMENTATION_DATE = Implementation date
ROW_VERIFICATION_DATE = Verification date
ROW_DAYS_TO_VERIFY = Days to verify
ROW_FILL_TYPE = Fill mode
ROW_FILL_FORM = Form to fill
ROW_OBJECTIVE = Objective
ROW_IS_PLANNED = Is planned
ROW_BUSINESS_UNIT_DEPARTMENT = Department
ROW_SYSTEM_LINK = Links to other systems
ROW_CATEGORY = Category
ROW_SOURCE = Source
ROW_PRIORITY = Priority
ROW_CLIENT = Client
ROW_PLANNER = Planner
ROW_TASK = Task
ROW_PLANNED_HOURS = Planned hours
ROW_ACTIVITY_ORDER = Order
ROW_DYNAMIC_FIELDS = Dynamic fields
ROW_PRE_IMPLEMENTER = Pre-assigned responsible(s)
VARIABLE_NAME = Name
VARIABLE_VALUE = Value
parseRowsError = Error reading activities
parseVariablesError = Error reading variables
parseError = Error reading activities
mainContentSheetName = Activities
sheetVariablesName = Variables
sheetSaveResultsName = Results
sheetHelpName = Help
CatalogField.value = ID
CatalogField.code = ID
CatalogField.text = Description
CatalogField.description = Description
CatalogField.version = Version
CatalogField.account = Account
CatalogField.mail = Email
CatalogField.clientCode = Client ID
CatalogField.plannerCode = Project ID
CatalogField.userDefinedCode = Departament ID
TextCodeValue.value = Record ID
TextCodeValue.text = Description
TextCodeValue.code = ID
emptyFile = File has no content
emptyRowsHeader = The activity header must have a value
emptyVariablesHeader = The variable header must have a value
unknownVariablesHeader = Invalid variables header value
headersError = There is at least one invalid header
requiredField = The {field} is a required field
repeatedField = The {field} must have unique values
invalidFieldValue = Invalid value
notFoundParent = A record with the {targetField} of the {sourceField} is not found
parseDateField = Cannot convert field {field} to date. The date must be in {format} format
outOfRangeDateField = The field {field} is outside the allowed date range of {min} to {max}. The date must be in {format} format
computeDaysField = Could not calculate the days of {field}
parseNumberField = Cannot convert field {field} to number
parseFillType = Invalid value of {field}
catalogNotFound = The value of field {field} was not found in the catalog. Please check that the value exists in {app} and the activity type configuration
catalogNotFoundWithItems = The value {items} of the field {field} was not found in the catalog. Please check that the value exists in {app} and the activity type configuration
dataMismatch = The {sourceField} of {targetField} does not match the field {sourceField}
parentNotFound = Value of {sourceField} not found in field {targetField}
maxSubtaskLevelReached = Max subtask level of {maxLevel} reached
saveException = An error occurred while saving activities. Details {error}
SAVE_SUCCESS = Save successful
SAVE_FAILED = Failed to save
SAVE_RESULTS_CODE = ID
SAVE_RESULTS_DESCRIPTION = Description
SAVE_RESULTS_LINK = Link
SAVE_RESULTS_STATUS = Status
SAVE_RESULTS_PARENT = Parent
HELP_SHEET = Sheet
HELP_FIELD = Field
HELP_TYPE = Type
HELP_DESCRIPTION = Description
templateName = Activities type template
notSupportedDynamicFieldType = Dynamic field type {type} unsupported
notFoundOptionDynamicFieldType = The value {value} is not a valid dynamic field option
dataValidationError = Data error validation
invalidDynamicFields = Dynamic fields invalid values
maximumCharactersExceeded = Maximum characters exceeded of {maxCharacters}
HELP_TYPE_ROW_ID = Numeric
HELP_DESCRIPTION_ROW_ID = To add subtasks it is required to assign an {ROW_ID}
HELP_TYPE_ROW_PARENT = Numeric
HELP_DESCRIPTION_ROW_PARENT = To create a subtask, you must capture the parent {ROW_ID}
HELP_TYPE_ROW_TYPE = Catalog
HELP_DESCRIPTION_ROW_TYPE = You can fetch the ID or description of the type
HELP_TYPE_ROW_CODE = Alphanumeric
HELP_DESCRIPTION_ROW_CODE = Must be unique
HELP_TYPE_ROW_DESCRIPTION = Alphanumeric
HELP_DESCRIPTION_ROW_DESCRIPTION = Activity details
HELP_TYPE_ROW_IMPLEMENTERS = Catalog
HELP_DESCRIPTION_ROW_IMPLEMENTERS = You can enter the ID, account or description of the users, allows multiple commas separated value
HELP_TYPE_ROW_VERIFIER = Catalog
HELP_DESCRIPTION_ROW_VERIFIER = You can fetch the ID, account or description of the user
HELP_TYPE_ROW_START_DATE = Date
HELP_DESCRIPTION_ROW_START_DATE = Date in {format} format
HELP_TYPE_ROW_IMPLEMENTATION_DATE = Date
HELP_DESCRIPTION_ROW_IMPLEMENTATION_DATE = Date in {format} format
HELP_TYPE_ROW_VERIFICATION_DATE = Date
HELP_DESCRIPTION_ROW_VERIFICATION_DATE = Date in {format} format
HELP_TYPE_ROW_DAYS_TO_VERIFY = Numeric
HELP_DESCRIPTION_ROW_DAYS_TO_VERIFY = You can capture the days available to verify
HELP_TYPE_ROW_FILL_TYPE = Catalog
HELP_DESCRIPTION_ROW_FILL_TYPE = One of the values \u200b\u200bcan be captured: {PROGRESS}, {FILL_FORM}, {MARK_DONE}.
HELP_TYPE_ROW_FILL_FORM = Catalog
HELP_DESCRIPTION_ROW_FILL_FORM = The ID or description of forms can be captured, it is used when capturing the value {MARK_DONE} in the field {ROW_FILL_TYPE}
HELP_TYPE_ROW_OBJECTIVE = Catalog
HELP_DESCRIPTION_ROW_OBJECTIVE = You can capture the ID or description of the objective
HELP_TYPE_ROW_IS_PLANNED = Catalog
HELP_DESCRIPTION_ROW_IS_PLANNED = Yes or No value must be captured
HELP_TYPE_ROW_BUSINESS_UNIT_DEPARTMENT = Catalog
HELP_DESCRIPTION_ROW_BUSINESS_UNIT_DEPARTMENT = The description of the relationship between facility and department
HELP_TYPE_ROW_SYSTEM_LINK = Alphanumeric
HELP_DESCRIPTION_ROW_SYSTEM_LINK = Link ID to another system
HELP_TYPE_ROW_CATEGORY = Catalog
HELP_DESCRIPTION_ROW_CATEGORY = You can capture the ID or description of the source
HELP_TYPE_ROW_SOURCE = Catalog
HELP_DESCRIPTION_ROW_SOURCE = You can capture the ID or description of the source
HELP_TYPE_ROW_PRIORITY = Catalog
HELP_DESCRIPTION_ROW_PRIORITY = You can capture the ID or description of the priority
HELP_TYPE_ROW_CLIENT = Catalog
HELP_DESCRIPTION_ROW_CLIENT = Customer ID or description can be captured
HELP_TYPE_ROW_PLANNER = Catalog
HELP_DESCRIPTION_ROW_PLANNER = Project ID or description can be captured
HELP_TYPE_ROW_TASK = Catalog
HELP_DESCRIPTION_ROW_TASK = Task ID or description can be captured
HELP_TYPE_ROW_PLANNED_HOURS = Decimal
HELP_DESCRIPTION_ROW_PLANNED_HOURS = Estimated hours should be captured
HELP_TYPE_VARIABLE_NAME = Alphanumeric
HELP_DESCRIPTION_VARIABLE_NAME = Must be unique, when this value matches the value of an {mainContentSheetName} cell, the value of the cell is replaced by the value of this variable.
HELP_TYPE_VARIABLE_VALUE = Alphanumeric
HELP_DESCRIPTION_VARIABLE_VALUE = Value of the variable, it must meet the requirements of the destination cell.
HELP_TYPE_ROW_ACTIVITY_ORDER = Numeric
HELP_DESCRIPTION_ROW_ACTIVITY_ORDER = Must be numeric.
HELP_TYPE_ROW_PRE_IMPLEMENTER = Alphanumeric
HELP_DESCRIPTION_ROW_PRE_IMPLEMENTER = You can enter the ID, account or description of the users, allows multiple commas separated value
bulkUploadDescriptionError = Error processing file {filename}. Error: {error}
bulkUploadDescriptionSuccess = File uploaded {filename} successfully
bulkUploadFilenameError = Error - {filename}-{date}
bulkUploadFilenameSuccess = Upload successful - {filename}-{date}
