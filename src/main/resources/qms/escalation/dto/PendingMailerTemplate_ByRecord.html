<table style="margin-left: 24px; border:solid 1px {systemColor}; font-family: Helvetica, Arial, Verdana, Trebuchet MS, sans-serif; border-collapse: collapse" align="center">
    <tbody>
		<tr>
			<td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.entityCode}</strong></td>
			<td style="border: solid 1px {systemColor}; padding: 4px 8px;">{entityCode}</td>
		</tr>
		<tr>
			<td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.entityDescription}</strong></td>
			<td style="border: solid 1px {systemColor}; padding: 4px 8px;">{entityDescription}</td>
		</tr>
		<!-- [removeLineForPendingOperation="strong"] --><tr>
			<!-- [removeLineForPendingOperation="strong"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.subEntityCode}</strong></td>
			<!-- [removeLineForPendingOperation="strong"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{subEntityCode}</td>
		<!-- [removeLineForPendingOperation="strong"] --></tr>
		<!-- [removeLineForPendingOperation="strong"] --><tr>
			<!-- [removeLineForPendingOperation="strong"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.subEntityDescription}</strong></td>
			<!-- [removeLineForPendingOperation="strong"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{subEntityDescription}</td>
		<!-- [removeLineForPendingOperation="strong"] --></tr>
		<!-- [removeLineForMailTypeAction="delayed,notice"] --><tr>
			<!-- [removeLineForMailTypeAction="delayed,notice"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.deadline}</strong></td>
			<!-- [removeLineForMailTypeAction="delayed,notice"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{deadline}</td>
		<!-- [removeLineForMailTypeAction="delayed,notice"] --></tr>
		<!-- [removeLineForMailTypeAction="deadline"] --><tr>
			<!-- [removeLineForMailTypeAction="deadline"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.commitmentDate}</strong></td>
			<!-- [removeLineForMailTypeAction="deadline"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{commitmentDate}</td>
		<!-- [removeLineForMailTypeAction="deadline"] --></tr>
		<!-- [removeLineForMailAction="remove"] --><tr>
                        <!-- [removeLineForMailAction="remove"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.entityTypeName}</strong></td>
			<!-- [removeLineForMailAction="remove"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{entityTypeName}</td>
		<!-- [removeLineForMailAction="remove"] --></tr>
		<!-- [removeLineForMailAction="remove"] --><tr>
			<!-- [removeLineForMailAction="remove"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.entityBusinessUnitName}</strong></td>
			<!-- [removeLineForMailAction="remove"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{entityBusinessUnitName}</td>
		<!-- [removeLineForMailAction="remove"] --></tr>
		<!-- [removeLineForMailAction="remove"] --><tr>
			<!-- [removeLineForMailAction="remove"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.entityDepartmentName}</strong></td>
			<!-- [removeLineForMailAction="remove"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{entityDepartmentName}</td>
		<!-- [removeLineForMailAction="remove"] --></tr>
		<!-- [removeLineForMailAction="remove"] --><tr>
			<!-- [removeLineForMailAction="remove"] --><td style="text-indent: 12px; min-width: 80px; border: solid 1px {systemColor}; padding: 4px 8px;"><strong>{label.entitySourceCode}</strong></td>
			<!-- [removeLineForMailAction="remove"] --><td style="border: solid 1px {systemColor}; padding: 4px 8px;">{entitySourceCode}</td>
		<!-- [removeLineForMailAction="remove"] --></tr>
    </tbody>
</table>
<br/>
{recordDetail}
{extraTable}