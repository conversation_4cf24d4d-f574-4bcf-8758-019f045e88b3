<?xml version="1.0" encoding="UTF-8" ?>
<!-- 
    Module     : Quejas
    Created on : 14/01/2013, 07:24:57 PM
    Author     : <PERSON>
-->
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
    <package name="complaints-views" extends="struts-default">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/> 
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
            </interceptor-stack>
        </interceptors>
        <default-interceptor-ref name="bnextStack" />
        <global-results>
            <result name="login">/administrator/login/cerrarsesion.jsp?struts=1</result>
            <result name="error">/errors/500.jsp</result>
            <result name="noaccess">/errors/noaccess.jsp</result>
            <result name="nosession">/errors/nosession.jsp</result>
            <result name="nosupport">/administrator/unlicensed/unlicensed.jsp</result>
            <result name="invalid">/errors/invalid.jsp</result>
            <result name="renew-data">/administrator/login/renew-data.jsp</result>
        </global-results>
        <!-- View -->
            <action name="v.complaint.sources.list" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/complaint.sources.list.jsp</result>
                
            </action> 
        <!-- Controller -->
            <action name="c.complaint.sources.list" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/controller/complaint.sources.list.js</result>
                
            </action>
        <!-- View -->
            <action name="v.complaint.analisis.list" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/complaint.analisis.list.jsp</result>
                
            </action> 
        <!-- Controller -->
            <action name="c.complaint.analisis.list" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/controller/complaint.analisis.list.js</result>
                
            </action>
        <!-- View -->
            <action name="v.complaint.list" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/complaint.list.jsp</result>
                
            </action> 
        <!-- Controller -->
            <action name="c.complaint.list" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/controller/complaint.list.js</result>
                
            </action>
            
        <!-- View -->
            <action name="v.my.complaint" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/my.complaint.jsp</result>
                
            </action> 
        <!-- Controller -->
            <action name="c.my.complaint" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/controller/my.complaint.js</result>
                
            </action>
        <!-- view Alta -->
            <action name="v.complaint" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/quejashandle.jsp</result>
                
            </action>

            <action name="c.complaint" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/complaint/complaint.js</result>
                
            </action>
        <!-- view Reporte -->
            <action name="v.complaint.reporte" class="Framework.Action.DefaultAction">
                <result name="success">/administrator/quejas/reporteQuejas.jsp</result>
                
            </action>
        <action name="c.complaint.reporte" class="Framework.Action.DefaultAction">
            <result name="success">/administrator/quejas/controller/complaintReport.js</result>
        </action>
    </package>
</struts>