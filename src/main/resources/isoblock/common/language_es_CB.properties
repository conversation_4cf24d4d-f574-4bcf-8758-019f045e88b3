accion.accionatomarhandle.Accionatomar=Acci&oacute;n a tomar:
accion.accionatomarhandle.boton.CrearAccionatomar=Crear <PERSON>cci&oacute;n a tomar
accion.accionatomarhandle.Clavedelaaccionatomar=Clave de la acci&oacute;n a tomar:
accion.accionatomarhandle.Fechadecumplimiento=Fecha de cumplimiento:
accion.accionatomarhandle.Fechadeevaluacion=Fecha de evaluaci&oacute;n:
accion.accionatomarhandle.header.ACCIONESPREVENTIVAS=ACCIONES PREVENTIVAS
accion.accionatomarhandle.Historialdeavance=Historial de Avance
accion.accionatomarhandle.javascript.Laaccionatomarhansidoverificada=La acci&oacute;n a tomar ha sido verificada.
accion.accionatomarhandle.javascript.LosDatosdelaaccionatomarhansidoactualizados=Los datos de la acci&oacute;n a tomar han sido actualizados.
accion.accionatomarhandle.javscript.Accionatomar=- Acci\\u00f3n a tomar
assignComplaintInsufficientPermissions = No cuenta con acceso para realizar la operaci\u00f3n, verifique que tenga un permiso superior a lector.
accion.accionatomarhandle.javscript.Descripciondelaaccionactual=- Descripci\\u00f3n de la acci\\u00f3n actual
accion.accionatomarhandle.javscript.Evaluarresultado=- Evaluar resultado 
accion.accionatomarhandle.javscript.Fechadeimplementacion=- Fecha de implementaci\\u00f3n
accion.accionatomarhandle.javscript.LaAccionatomarhasidodadadealta=La acci&oacute;n a tomar ha sido dada de alta.
accion.accionatomarhandle.javscript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios: 
accion.accionatomarhandle.Noserecibiolaclaveareferenciar=No se recibi&oacute; la clave a referenciar.
accion.accionatomarhandle.Porcentajedeavance=Porcentaje de avance:
accion.accionatomarhandle.Porcentajedecumplimientodelaaccion=Porcentaje de cumplimiento de la acci&oacute;n:
accion.accionatomarhandleamc.Accionatomar=Acci&oacute;n a tomar:
accion.accionatomarhandleamc.Acciondecorreccioninmediata=Acci&oacute;n de Correcci&oacute;n Inmediata:
accion.accionatomarhandleamc.AccionTomarSobreQueja=ACCION A TOMAR SOBRE LA QUEJA
accion.accionatomarhandleamc.boton.Actualizar=Actualizar
accion.accionatomarhandleamc.boton.CrearAccionatomar=Crear acci&oacute;n a tomar
accion.accionatomarhandleamc.boton.Limpiar=Limpiar
accion.accionatomarhandleamc.boton.Regresar=Regresar
accion.accionatomarhandleamc.Clavedelaaccion=Clave de la acci&oacute;n:
accion.accionatomarhandleamc.ClavedelaQueja= Clave de la queja:
accion.accionatomarhandleamc.Detalledelcambio=Detalle del cambio:
accion.accionatomarhandleamc.Fechadeevaluacion=Fecha de evaluaci&oacute;n:
accion.accionatomarhandleamc.Fechadeimplementacion=Fecha de implementaci&oacute;n:
accion.accionatomarhandleamc.Fechadeverificacion=Fecha de verificaci&oacute;n:
accion.accionatomarhandleamc.header.ACCIONESPREVENTIVAS=ACCIONES PREVENTIVAS
accion.accionatomarhandleamc.javascript.Accionatomar=- Acci\\u00f3n a tomar
accion.accionatomarhandleamc.javascript.ComentarioDeAvance=Comentario de Avance
accion.accionatomarhandleamc.javascript.ElResponsableNoPuedeSerElVerificador=El Responsable no puede ser el Verificador.
accion.accionatomarhandleamc.javascript.-ElResponsableNoPuedeSerElVerificador=El Responsable no puede ser el Verificador.
accion.accionatomarhandleamc.javascript.-ElResponsableNoPuedeSerElVerificador=El Responsable no puede ser el Verificador.
accion.accionatomarhandleamc.javascript.ESTECAMPONOHASIDOMODIFICADO=El campo no ha sido modificado
accion.accionatomarhandleamc.javascript.Evaluarresultado=- Resultados de la acci\u00f3n.
accion.accionatomarhandleamc.javascript.Fechadeimplementacion=- Fecha de implementaci\\u00f3n
accion.accionatomarhandleamc.javascript.Fechadeverificacion=- Fecha de verificaci\\u00f3n
accion.accionatomarhandleamc.javascript.Laaccionatomarhansidoactualizada=La acci&oacute;n a tomar ha sido actualizada.
accion.accionatomarhandleamc.javascript.Laaccionatomarhasidodadadealta=La acci&oacute;n a tomar ha sido dada de alta.
accion.accionatomarhandleamc.javascript.LosDatosdelaaccionatomarhansidoactualizados=Los Datos de la acci&oacute;n a tomar han sido actualizados.
accion.accionatomarhandleamc.javascript.LosDatosdelaaccionatomarhansidoactualizados=Los datos de la acci&oacute;n a tomar han sido actualizados.
accion.accionatomarhandleamc.javascript.LosDatosdelaaccionatomarhansidoactualizados=Los datos de la acci&oacute;n a tomar han sido actualizados.
accion.accionatomarhandleamc.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
accion.accionatomarhandleamc.javascript.Razondecambiodefechadeimplementacion=- Raz\\u00f3n de cambio de fecha de implementaci\\u00f3n
accion.accionatomarhandleamc.javascript.Razondecambiodefechadeverificacion=- Raz\\u00f3n de cambio de fecha de verificaci\\u00f3n
accion.accionatomarhandleamc.javascript.Razondecambiodeporcentaje=- Raz\\u00f3n de cambio de porcentaje
accion.accionatomarhandleamc.javascript.Razondecambiodeverificador=- Raz\\u00f3n de cambio de verificador
accion.accionatomarhandleamc.javascript.-Responsable=- Responsable de implementar la acci\\u00f3n
accion.accionatomarhandleamc.javascript.Responsable=Responsable
accion.accionatomarhandleamc.javascript.-Verificador=- Verificador
accion.accionatomarhandleamc.javascript.Verificador=Verificador
accion.accionatomarhandleamc.Noserecibiolaclaveareferenciar=No se recibi&oacute; la clave a referenciar.
accion.accionatomarhandleamc.Permitircambiosaresponsable=Permitir al responsable modificar campos
accion.accionatomarhandleamc.Porcentajedeavance=Porcentaje de avance:
accion.accionatomarhandleamc.Porcentajedecumplimientodelaaccion=Porcentaje de cumplimiento de la acci&oacute;n:
accion.accionatomarhandleamc.Razondecambiodefechadeimplementacion=Raz&oacute;n de cambio de fecha de implementaci&oacute;n
accion.accionatomarhandleamc.Razondecambiodefechadeverificacion=Raz&oacute;n de cambio de fecha de verificaci&oacute;n
accion.accionatomarhandleamc.Razondecambiodeporcentaje=Raz&oacute;n de cambio de porcentaje
accion.accionatomarhandleamc.Razondecambiodeverificador=Raz&oacute;n de cambio de verificador
accion.accionatomarhandleamc.Responsable=Responsable
accion.accionatomarhandleamc.Responsabledeimplementarlaaccion=Responsable de implementar la acci&oacute;n
accion.accionatomarhandleamc.ResponsabledeActividad=Responsable de actividad
accion.accionatomarhandleamc.Resultadosdelaaccion=Resultados de la acci&oacute;n:
accion.accionatomarhandleamc.SolicituddeAccionaTomar=SOLICITUD DE ACCI\u00d3N A TOMAR
accion.accionatomarhandleamc.SolicituddeAcciondeCorreccionInmediata=SOLICITUD DE ACCI\u00d3N DE CORRECCI\u00d3N INMEDIATA
accion.accionatomarhandleamc.Verificador=Verificador
accion.accionatomarhandlemc.javascript.ComentarioDeAvance=Comentario de Avance
accion.accionatomarlist.Accion=Acci&oacute;n
accion.accionatomarlist.Asignada=Asignada
accion.accionatomarlist.AsignarlaAccionPreventiva=Asignar la Acci&oacute;n Preventiva
accion.accionatomarlist.Autor=Autor
accion.accionatomarlist.Borrar=Borrar
accion.accionatomarlist.Cerrada=Cerrada
accion.accionatomarlist.cerrarAccion=cerrarAccion
accion.accionatomarlist.Clave=Clave
accion.accionatomarlist.Definida=Definida
accion.accionatomarlist.DefinirlasAccionesatomar=Definir las Acciones a tomar
accion.accionatomarlist.Encargado=Encargado
accion.accionatomarlist.Estado= Estado
accion.accionatomarlist.header.CONTROLDEACCIONESPREVENTIVAS=CONTROL DE ACCIONES PREVENTIVAS
accion.accionatomarlist.Implementada=Implementada
accion.accionatomarlist.implementarAccion=Implementar Acci&oacute;n
accion.accionatomarlist.javascript.DeseamarcarlaAccioncomocerrada=\u00bfDesea marcar la Acci\\u00f3n como cerrada?
accion.accionatomarlist.javascript.DeseamarcarlaAccioncomoimplementada=\u00bfDesea marcar la Acci\\u00f3n como implementada?
accion.accionatomarlist.javascript.Laaccionpreventivaseraborradadelsistema=\u00a1La acci\\u00f3n preventiva ser\\u00e1 borrada del sistema!
accion.accionatomarlist.MarcarcomocerradalaAccionPreventiva=Marcar como cerrada la Acci&oacute;n Preventiva
accion.accionatomarlist.MarcarcomoimplementadalaAccionPreventiva=Marcar como implementada la Acci&oacute;n Preventiva
accion.accionatomarlist.NoAsignado=No Asignado
accion.accionatomarlist.Nopuederealizarestaaccion=No puede realizar esta acci&oacute;n
accion.accionatomarlist.Noseencontraronacciones=No se encontraron acciones
accion.accionatomarlist.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accionatomarlist.Reportada=Reportada
accion.accionatomarlist.Reportada=Reportada
accion.accionatomarlist.Sehaeliminadolaaccionpreventiva=Se ha eliminado la acci&oacute;n preventiva
accion.accionatomarlist.Sehamarcadolaaccioncomocerrada=Se ha marcado la acci&oacute;n como cerrada
accion.accionatomarlist.Sehamarcadolaaccioncomoimplementada=Se ha marcado la acci&oacute;n como implementada
accion.accionatomarlist.siguientePaso=Siguiente Paso
accion.accionatomarlist.--TODOS--=-- TODOS --
accion.accionatomarlist.Ver=Ver
accion.accionatomarlist.Verificada=Verificada
accion.accionatomarlist.VerificarlaAccionPreventiva=Verificar la Acci&oacute;n Preventiva
accion.accionatomarlist.Yanohayaccionesposibles=Ya no hay acciones posibles
accion.accioncorrectivahandle.AccionCorrectiva=Acci&oacute;n Correctiva:
accion.accioncorrectivahandle.Analisisdelanoconformidad=An&aacute;lisis de la no conformidad:
accion.accioncorrectivahandle.Analisisrealizado=An&aacute;lisis realizado:
accion.accioncorrectivahandle.Anexo=Anexo:
accion.accioncorrectivahandle.Auditorverificador=Auditor verificador:
accion.accioncorrectivahandle.boton.Acualizardatos=Acualizar datos
accion.accioncorrectivahandle.boton.Cancelar=Cancelar
accion.accioncorrectivahandle.boton.Regresar=Regresar
accion.accioncorrectivahandle.boton.Cerrar=Cerrar
accion.accioncorrectivahandle.boton.CrearAccion=Crear Acci&oacute;n
accion.accioncorrectivahandle.boton.GrabarAnalisis=Grabar An&aacute;lisis
accion.accioncorrectivahandle.boton.GrabarVerificacion=Grabar Verificaci&oacute;n
accion.accioncorrectivahandle.boton.Limpiar=Limpiar
accion.accioncorrectivahandle.Clavedelaaccioncorrectiva=Clave de la acci&oacute;n correctiva:
accion.accioncorrectivahandle.Clavedelaauditoriaalaquepertenece=Clave de la auditor&iacute;a a la que pertenece:
accion.accioncorrectivahandle.Cumple-Nocumple=Cumple - No cumple: 
accion.accioncorrectivahandle.Descripciondelanoconformidad=Descripci&oacute;n de la no conformidad:
accion.accioncorrectivahandle.Efectiva-Noefectiva=Efectiva - No efectiva: 
accion.accioncorrectivahandle.--Encargado--=--Encargado--
accion.accioncorrectivahandle.FechadeImplementacion=Fecha de Implementaci&oacute;n:
accion.accioncorrectivahandle.FechadelaEvaluacion=Fecha de la Evaluaci&oacute;n:
accion.accioncorrectivahandle.FechadelAnalisis=Fecha del An&aacute;lisis: 
accion.accioncorrectivahandle.header.ACCIONESCORRECTIVAS=ACCIONES CORRECTIVAS
accion.accioncorrectivahandle.javascript.-Accioncorrectiva=- Acci\\u00f3n correctiva
accion.accioncorrectivahandle.javascript.-Analisis=- An\\u00e1lisis
accion.accioncorrectivahandle.javascript.-Descripcion=- Descripci\\u00f3n
accion.accioncorrectivahandle.javascript.-Evidencias=- Evidencias
accion.accioncorrectivahandle.javascript.-Fechadeimplementacion=- Fecha de implementaci\\u00f3n
accion.accioncorrectivahandle.javascript.Laaccioncorrectivahansidoevaluada=La acci&oacute;n correctiva ha sido evaluada.
accion.accioncorrectivahandle.javascript.Laaccioncorrectivahasidodadadealta=La acci&oacute;n correctiva ha sido dada de alta.
accion.accioncorrectivahandle.javascript.LosDatosdelaaccioncorrectivahansidoactualizados=Los Datos de la acci&oacute;n correctiva han sido actualizados.
accion.accioncorrectivahandle.javascript.LosDatosdelaaccioncorrectivahansidoactualizados=Los datos de la acci&oacute;n correctiva han sido actualizados.
accion.accioncorrectivahandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunaaccioncorrectiva=Los siguientes datos son necesarios para dar de alta una acci&oacute;n correctiva
accion.accioncorrectivahandle.javascript.-Responsable=- Responsable
accion.accioncorrectivahandle.javascript.-Verificador=- Verificador
accion.accioncorrectivahandle.ObservacionesdelaEvidencia=Observaciones de la Evidencia:
accion.accioncorrectivahandle.UsuarioEncargado=Usuario Encargado:
accion.accioncorrectivahandle.--Verificador--=--Verificador--
accion.accioncorrectivalector.Analizada=Analizada
accion.accioncorrectivalector.Asignada=Asignada
accion.accioncorrectivalector.Auditoria= Auditor&iacute;a:
accion.accioncorrectivalector.Auditoria=Auditor&iacute;a
accion.accioncorrectivalector.Cerrada=Cerrada
accion.accioncorrectivalector.Clave=Clave
accion.accioncorrectivalector.editarAccionCorrectiva=editarAccionCorrectiva
accion.accioncorrectivalector.Estado=Estado
accion.accioncorrectivalector.header.CONSULTADEACCIONESCORRECTIVAS=CONSULTA DE ACCIONES CORRECTIVAS
accion.accioncorrectivalector.Implementacion=Implementaci&oacute;n
accion.accioncorrectivalector.Implementada=Implementada
accion.accioncorrectivalector.Noseencontraronaccionesparalaauditoriaindicada=No se encontraron acciones para la auditor&iacute;a indicada
accion.accioncorrectivalector.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accioncorrectivalector.Responsable=Responsable
accion.accioncorrectivalector.--Todas--=-- TODAS --
accion.accioncorrectivalector.Verificada=Verificada
accion.accioncorrectivalist.Analizada=Analizada
accion.accioncorrectivalist.AnalizarlaAccionCorrectiva=:Analizar la  Acci&oacute;n Correctiva
accion.accioncorrectivalist.Asignada=Asignada
accion.accioncorrectivalist.AsignadaVerlaAccionCorrectiva=Asignada: Ver la Acci&oacute;n Correctiva
accion.accioncorrectivalist.Auditoria=Auditor&iacute;a
accion.accioncorrectivalist.Auditoria=Auditor&iacute;a:
accion.accioncorrectivalist.Borrar=Borrar
accion.accioncorrectivalist.Cerrada=Cerrada
accion.accioncorrectivalist.cerrarAccion=cerrarAccion
accion.accioncorrectivalist.Clave=Clave
accion.accioncorrectivalist.editarAccionCorrectiva=Editar Acci&oacute;n Correctiva
accion.accioncorrectivalist.Estado=Estado
accion.accioncorrectivalist.header.CONTROLDEACCIONESCORRECTIVAS=CONTROL DE ACCIONES CORRECTIVAS
accion.accioncorrectivalist.Implementacion=Implementaci&oacute;n
accion.accioncorrectivalist.Implementada=Implementada
accion.accioncorrectivalist.implementarAccion=Implementar Acci&oacute;n
accion.accioncorrectivalist.ImplementarlaAccionCorrectiva=: Implementar la  Acci&oacute;n Correctiva
accion.accioncorrectivalist.javascript.DeseamarcarlaAccioncomocerrada=\u00bfDesea marcar la Acci\\u00f3n como cerrada?
accion.accioncorrectivalist.javascript.DeseamarcarlaAccioncomoimplementada=\u00bfDesea marcar la Acci\\u00f3n como implementada?
accion.accioncorrectivalist.javascript.Laaccioncorrectivaseraborradadelsistema=\u00a1La acci\\u00f3n correctiva ser\\u00e1 borrada del sistema!
accion.accioncorrectivalist.Marcarcomocerrada=: Marcar como cerrada
accion.accioncorrectivalist.Noseencontraronaccionesparalaauditoriaindicada=No se encontraron acciones para la auditor&iacute;a indicada
accion.accioncorrectivalist.Responsable=Responsable
accion.accioncorrectivalist.Sehaeliminadolaaccioncorrectiva=Se ha eliminado la acci&oacute;n correctiva
accion.accioncorrectivalist.Sehamarcadolaaccioncomocerrada=Se ha marcado la acci&oacute;n como cerrada
accion.accioncorrectivalist.Sehamarcadolaaccioncomoimplementada=Se ha marcado la acci&oacute;n como implementada
accion.accioncorrectivalist.siguientePaso=Siguiente Paso
accion.accioncorrectivalist.--Todas--=-- TODAS --
accion.accioncorrectivalist.Verificada=Verificada
accion.accioncorrectivalist.VerificarlaAccionCorrectiva=: Verificar la  Acci&oacute;n Correctiva
accion.accioncorrectivalist.VerlaAccionCorrectiva=: Ver la  Acci&oacute;n Correctiva
accion.accioncorrectivalist.VerlaAccionCorrectiva=: Ver la  Acci&oacute;n Correctiva
accion.accioncorrectivalist.VerlaAccionCorrectiva=: Ver la  Acci&oacute;n Correctiva
accion.accioncorrectivalist.VerlaAccionCorrectiva=: Ver la  Acci&oacute;n Correctiva
accion.acciongenerica.AnalisisDatos=Analisis de Datos
accion.acciongenerica.AuditoriaExternaCalidad=Auditoria Externa de Calidad
accion.acciongenerica.AuditoriaInternaCalidad=Auditoria Interna de Calidad
accion.acciongenerica.Indicadores=Indicadores
accion.acciongenerica.Operacion=Operacion
accion.acciongenerica.Otros=Otros
accion.acciongenerica.Proyectos=Proyectos
accion.acciongenerica.QuejasCliente=Quejas del Cliente
accion.acciongenerica.RevisionDirectiva=Revisi\u00f3n Directiva
accion.acciongenerica.titulo=T\u00edtulo
accion.acciongenericahandle.Accion=Acci&oacute;n
accion.acciongenericahandle.AccionATomar=Alta de Acci\u00f3n Tomar:
accion.acciongenericahandle.AcciondeCorreccionInmediata=Alta de Acci\u00f3n de Correcci\u00f3n Inmediata:
accion.acciongenericahandle.Accionesarealizarse=Acci&oacute;n(es) a tomar:
accion.acciongenericahandle.AccionRelacionadaPadre= Acci&oacute;n Relacionada(Padre):
accion.acciongenericahandle.Analisisdecausas=An&aacute;lisis de causas:
accion.acciongenericahandle.subirDocumentos=Agregar archivos:
accion.acciongenericahandle.Asignaralaubicacion=Asignar al departamento:
accion.acciongenericahandle.Auditoria=Auditor&iacute;a
accion.acciongenericahandle.Avance=Avance
accion.acciongenericahandle.Borrar=Borrar
accion.acciongenericahandle.boton.AgregarNueva=Agregar Nueva
accion.acciongenericahandle.boton.Controlacciones=Control acciones
accion.acciongenericahandle.boton.Evaluar=Evaluar
accion.acciongenericahandle.boton.Grabarcambios=Grabar cambios
accion.acciongenericahandle.boton.Listaracciones=Listar acciones
accion.acciongenericahandle.Clavedelaaccion=Clave de la acci&oacute;n:
accion.acciongenericahandle.ClavedelaAccionPadre=Clave de la acci&oacute;n padre:
accion.acciongenericahandle.Clavedelaauditoria=Clave de la auditor&iacute;a:
accion.acciongenericahandle.Consecuencia=Consecuencia:
accion.acciongenericahandle.ControlAPD=CONTROL DE ACCIONES SOBRE PRODUCTOS NO CONFORME
accion.acciongenericahandle.ControlAPO=CONTROL DE ACCIONES SOBRE PROYECTOS
accion.acciongenericahandle.Creada=Creada
accion.acciongenericahandle.Editar=Editar
accion.acciongenericahandle.Editaraccioner=Editar acci&oacute;n
accion.acciongenericahandle.Enproceso=En proceso
accion.acciongenericahandle.Estado=Estado
accion.acciongenericahandle.FechaImplementacion=Fecha Implementaci&oacute;n
accion.acciongenericahandle.FechaReportada=Fecha Reportada:
accion.acciongenericahandle.--Fuente--=--Fuente--
accion.acciongenericahandle.Fuente=Fuente:
accion.acciongenericahandle.Prioridad=Prioridad:
accion.acciongenericahandle.Tipo=Tipo:
accion.acciongenericahandle.Sistema=Sistema:
accion.acciongenericahandle.GerentedelaUbicacion=Encargado del departamento
accion.acciongenericahandle.Encargadodelmodulo=Encargado del m\u00f3dulo de acciones
accion.acciongenericahandle.PuedesAutorizar=Puedes Evaluar el hallazgo por ser el encargado del m\u00f3dulo, aunque no seas el encargado del departamento
accion.acciongenericahandle.Implementada=Ejecutada
accion.acciongenericahandle.javascript.Accionatomar=- Acci\\u00f3n(es) a tomar
accion.acciongenericahandle.javascript.AceptarAnalisis=Necesita aceptar el An\\u00e1lisis de Causas
accion.acciongenericahandle.javascript.Analisisdecausas=- An\\u00e1lisis de causas
accion.acciongenericahandle.javascript.Aprobarlaaccion=- Aprobar la acci\\u00f3n
accion.acciongenericahandle.javascript.Asignealmenosunaaccionatomar=- Asigne al menos una acci\\u00f3n a tomar
accion.acciongenericahandle.javascript.Clavedelaauditoria=- Clave de la auditor\\u00eda
accion.acciongenericahandle.javascript.Consecuencia=- Consecuencia
accion.acciongenericahandle.javascript.Controlacciones=Control acciones
accion.acciongenericahandle.javascript.Deseaborrarlaaccionatomar=\u00bfDesea borrar la acci\\u00f3n a tomar?
accion.acciongenericahandle.javascript.Fuentedelaaccion=- Fuente
accion.acciongenericahandle.javascript.Clave=- Clave
accion.acciongenericahandle.javascript.hansidoactualizados=han sido actualizados.
accion.acciongenericahandle.javascript.Laaccionhasidodadadealta=El hallazgo ha sido dado de alta.
accion.acciongenericahandle.javascript.Laoperaci\u00f3nsehallevadoacabo=La operaci&oacute;n se ha llevado a cabo.
accion.acciongenericahandle.javascript.Laoperacionsehallevadoacabo=La operaci&oacute;n se ha llevado a cabo.
accion.acciongenericahandle.javascript.Listaracciones=Listar acciones
accion.acciongenericahandle.javascript.LosDatosdelaaccionhansidoactualizados=Los datos de la acci&oacute;n han sido actualizados.
accion.acciongenericahandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios: 
accion.acciongenericahandle.javascript.Lossiguientesdatossonnecesariosparacontinuar=Los siguientes datos son necesarios para continuar:
accion.acciongenericahandle.javascript.exceedsLimit=Los siguientes datos exceden el l\u00edmite de caracteres
accion.acciongenericahandle.javascript.limit=M\u00e1ximo:
accion.acciongenericahandle.javascript.currentLength:Actual:
accion.acciongenericahandle.javascript.Responsabledeanalizalascausas=- Responsable de analizar las causas
accion.acciongenericahandle.javascript.Situacion=-Hallazgo.
accion.acciongenericahandle.javascript.Ubicacion=- Asignar al departamento.
accion.acciongenericahandle.javascript.Usuario=- Usuario.
accion.acciongenericahandle.Laaccionnecesitaunaacciondecorreccioninmediata=La acci\u00f3n necesita una acci\u00f3n de correcci\u00f3n inmediata.
accion.acciongenericahandle.ListadoAPD=LISTADO DE ACCIONES SOBRE PRODUCTOS NO CONFORME
accion.acciongenericahandle.ListadoAPO=LISTADO DE ACCIONES SOBRE PROYECTOS
accion.acciongenericahandle.Listaracciones=Listar acciones
accion.acciongenericahandle.Nosehanasignadoaccionesatomar=No se han asignado acciones a tomar
accion.acciongenericahandle.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior.
accion.acciongenericahandle.Porasignar=Por asignar
accion.acciongenericahandle.Procedelaaccion=Procede la acci&oacute;n:
accion.acciongenericahandle.Procedelaqueja=Procede la queja
accion.acciongenericahandle.RazonPorLaQueNoProcede=Raz&oacute;n por la que no procede
accion.acciongenericahandle.Responsable=Responsable
accion.acciongenericahandle.--Responsable--=--Responsable--
accion.acciongenericahandle.Responsabledeanalizarlascausas=Responsable de analizar las causas:
accion.acciongenericahandle.ResultadosDeEfectividad=Resultados de efectividad
accion.acciongenericahandle.-ResultadosDeEfectividad=-Resultados de efectividad.
accion.acciongenericahandle.Seleccione=Seleccione
accion.acciongenericahandle.Situacion=Hallazgo:
accion.acciongenericahandle.SOLICITUDDEACCIONESCORRECTIVAS=SOLICITUD DE ACCIONES CORRECTIVAS
accion.acciongenericahandle.SOLICITUDDEACCIONESDEMEJORACONTINUA=SOLICITUD DE ACCIONES DE MEJORA CONTINUA
accion.acciongenericahandle.SOLICITUDDEACCIONESPREVENTIVAS=SOLICITUD DE ACCIONES PREVENTIVAS
accion.acciongenericahandle.Titulo=T&iacute;tulo:
accion.acciongenericahandle.titulo=T&iacute;tulo:
accion.acciongenericahandle.TituloAPD=SOLICITUD DE ACCIONES SOBRE PRODUCTOS NO CONFORME
accion.acciongenericahandle.TituloAPO=SOLICITUD DE ACCIONES SOBRE PROYECTOS
accion.acciongenericahandle.--Ubicacion--=--Departamento--
accion.acciongenericahandle.Ver=Ver
accion.acciongenericahandle.Verificada=Verificada
accion.acciongenericahandle.Verificador=Verificador
accion.acciongenericalist.AccionesEnDondeNoParticipa=Acciones en donde no participa
accion.acciongenericalist.AccionesEnDondeParticipa=Acciones en donde participa
accion.acciongenericalist.AceptarAccion=Presione para VERIFICAR la acci&oacute;n.
accion.acciongenericalist.Analizadayenimplementacion=El estado actual es ANALIZADA.
accion.acciongenericalist.Aprobada=El estado actual es VERIFICADA.
accion.acciongenericalist.Asignada=El estado actual es ASIGNADA.
accion.acciongenericalist.Asignarelresponsable=\ Presione para asignar el responsable.
accion.acciongenericalist.Autor=Autor
accion.acciongenericalist.Borrar=Borrar
accion.acciongenericalist.CanceladaNoProcede= &nbsp;Cancelada (No procede)
accion.acciongenericalist.Cerrada=La acci&oacute;n ha sido VERIFICADA. El estado actual es CERRADA.
accion.acciongenericalist.Clave=Clave
accion.acciongenericalist.CONTROLDEACCIONESCORRECTIVAS=CONTROL DE ACCIONES CORRECTIVAS
accion.acciongenericalist.CONTROLDEACCIONESDEMEJORACONTINUA=CONTROL DE ACCIONES DE MEJORA CONTINUA
accion.acciongenericalist.CONTROLDEACCIONESPREVENTIVAS=CONTROL DE ACCIONES PREVENTIVAS
accion.acciongenericalist.DefinirlasAccionesatomar=\ Presione para definir las acciones a tomar.
accion.acciongenericalist.Editar=Editar
accion.acciongenericalist.EditarAccion=Editar Acci&oacute;n
accion.acciongenericalist.EditarAccionaTomar =Editar Acciones a Tomar
accion.acciongenericalist.EditarAccionesaTomar=Editar Acciones a Tomar
accion.acciongenericalist.EditarAccionesATomar=Editar acciones a tomar.
accion.acciongenericalist.EdoReportada=El estado actual es REPORTADA.
accion.acciongenericalist.Efectiva= (Efectiva)
accion.acciongenericalist.Estado=Estado
accion.acciongenericalist.EstadoAnalizada=Analizada
accion.acciongenericalist.EstadoAprobada=Aprobada
accion.acciongenericalist.EstadoAsignada=Asignada
accion.acciongenericalist.EstadoAsignada2=Asignada
accion.acciongenericalist.EstadoCanceladaNoProcede=Cancelada No Procede
accion.acciongenericalist.EstadoEfectiva=Efectiva
accion.acciongenericalist.EstadoNoEfectiva=No Efectiva
accion.acciongenericalist.EstadoReportada=Reportada
accion.acciongenericalist.EstadoT=Estado:
accion.acciongenericalist.FechaFin=Fecha fin
accion.acciongenericalist.FechaInicio=Fecha inicio
accion.acciongenericalist.Fuente=Fuente
accion.acciongenericalist.Implementadayverificada=El estado actual es EJECUTADA.
accion.acciongenericalist.javascript.DeseamarcarlaAccioncomocerrada=\u00bfDesea marcar la Acci\\u00f3n como cerrada?
accion.acciongenericalist.javascript.Laacciondemejoracontinuaseraborradadelsistema=\u00a1La acci\\u00f3n ser\\u00e1 borrada del sistema!
accion.acciongenericalist.Marcarcomocerrada=\ Presione para marcar como cerrada
accion.acciongenericalist.Modificarelanalisisolasaccionesatomar=\ Presione para modificar el an&aacute;lisis o las acciones a tomar.
accion.acciongenericalist.NoAsignado=No Asignado
accion.acciongenericalist.NoEfectiva= (No efectiva)
accion.acciongenericalist.Noseencontraronacciones=No se encontraron acciones
accion.acciongenericalist.NoSeEncontraronAccionesDondeNoParticipa=No se encontraron acciones donde no participa.
accion.acciongenericalist.NoSeEncontraronAccionesDondeParticipa=No se encontraron acciones donde participa.
accion.acciongenericalist.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.acciongenericalist.PresioneParaVerlaAccion=\ Presione para ver la acci&oacute;n.
accion.acciongenericalist.Reportada=Reportada
accion.acciongenericalist.Responsable=Responsable
accion.acciongenericalist.Sehaeliminadolaacciondemejoracontinua=Se ha eliminado la acci&oacute;n
accion.acciongenericalist.Sehamarcadolaaccioncomocerrada=Se ha marcado la acci&oacute;n como cerrada
accion.acciongenericalist.Tipodeaccion=Tipo de acci&oacute;n
accion.acciongenericalist.titulo = T\\u00edtulo
accion.acciongenericalist.titulo = T\\u00edtulo
accion.acciongenericalist.Titulo=T\\u00edtulo: 
accion.acciongenericalist.TODAS=TODAS
accion.acciongenericalist.TODOS=TODOS
accion.acciongenericalist.Ubicacion=Departamento
accion.acciongenericalist.Ver=Ver
accion.acciongenericalist.VerificarlaAccion=Presione para verificar la acci&oacute;n a tomar.
accion.acciongenericalist.VerlaAccion=: Ver la  Acci&oacute;n
accion.accionmejoracontinuahandle.Accion=Acci&oacute;n
accion.accionmejoracontinuahandle.Accionesarealizarse=Acci&oacute;n(es) a tomar:
accion.accionmejoracontinuahandle.Auditorverificador=Auditor verificador:
accion.accionmejoracontinuahandle.Avance=Avance
accion.accionmejoracontinuahandle.Borrar=Borrar
accion.accionmejoracontinuahandle.boton.Asignar=Asignar
accion.accionmejoracontinuahandle.boton.Cancelar=Cancelar
accion.accionmejoracontinuahandle.boton.Cerrar=Cerrar
accion.accionmejoracontinuahandle.boton.CrearAccion=Crear Acci&oacute;n
accion.accionmejoracontinuahandle.boton.Evaluar=Evaluar
accion.accionmejoracontinuahandle.boton.Grabarcambios=Grabar cambios
accion.accionmejoracontinuahandle.boton.Limpiar=Limpiar
accion.accionmejoracontinuahandle.Clavedelaacciondemejoracontinua=Clave de la acci&oacute;n de mejora continua:
accion.accionmejoracontinuahandle.Costo=de la accion=Costo de la acci&oacute;n:
accion.accionmejoracontinuahandle.Costodelaaccion=Costo de la acci&oacute;n:
accion.accionmejoracontinuahandle.Cumple-Nocumple=Cumple - No cumple:
accion.accionmejoracontinuahandle.Editar=Editar
accion.accionmejoracontinuahandle.EncargadoC=Encargado
accion.accionmejoracontinuahandle.Fechadeevaluacion=Fecha de evaluaci&oacute;n:
accion.accionmejoracontinuahandle.Fechametadecumplimiento=Fecha meta de cumplimiento:
accion.accionmejoracontinuahandle.header.ACCIONESDEMEJORACONTINUA=ACCIONES DE MEJORA CONTINUA
accion.accionmejoracontinuahandle.Implementacion=Implementaci&oacute;n
accion.accionmejoracontinuahandle.javascript.-Asignealmenosunaaccionatomar=- Asigne al menos una acci\\u00f3n a tomar
accion.accionmejoracontinuahandle.javascript.-Auditorverificador=- Auditor verificador
accion.accionmejoracontinuahandle.javascript.-Costodelaaccion=- Costo de la acci\\u00f3n
accion.accionmejoracontinuahandle.javascript.Elusuarioresponsableyauditorverificadorhansidoasignados=El usuario responsable y auditor verificador han sido asignados.
accion.accionmejoracontinuahandle.javascript.-Evaluarresultado=- Evaluar resultado
accion.accionmejoracontinuahandle.javascript.-Fechadecumplimiento=- Fecha de cumplimiento
accion.accionmejoracontinuahandle.javascript.Laacciondemejoracontinuahansidoverificada=La acci&oacute;n de mejora continua han sido verificada.
accion.accionmejoracontinuahandle.javascript.Laacciondemejoracontinuahasidodadadealta=La acci&oacute;n de mejora continua ha sido dada de alta.
accion.accionmejoracontinuahandle.javascript.LosDatosdelaacciondemejoracontinuahansidoactualizados=Los datos de la acci&oacute;n de mejora continua han sido actualizados.
accion.accionmejoracontinuahandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios: 
accion.accionmejoracontinuahandle.javascript.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accionmejoracontinuahandle.javascript.-Recursosrequeridos=- Recursos requeridos
accion.accionmejoracontinuahandle.javascript.-Situacionactual=- Situaci\\u00f3n actual
accion.accionmejoracontinuahandle.javascript.-Situaciondeseada=- Situaci\\u00f3n deseada
accion.accionmejoracontinuahandle.javascript.-Tecnicasausar=- T\\u00e9cnicas a usar
accion.accionmejoracontinuahandle.javascript.-Usuarioencargado=- Usuario encargado
accion.accionmejoracontinuahandle.Nosehanasignadoaccionesatomar=No se han asignado acciones a tomar
accion.accionmejoracontinuahandle.Recursosrequeridos=Recursos requeridos:
accion.accionmejoracontinuahandle.Situacionactual=Situaci&oacute;n actual:
accion.accionmejoracontinuahandle.Situaciondeseada=Situaci&oacute;n deseada:
accion.accionmejoracontinuahandle.Tecnicasausar=T&eacute;cnicas a usar:
accion.accionmejoracontinuahandle.UsuarioEncargado=Usuario Encargado:
accion.accionmejoracontinuahandle.VerificadorC=Verificador
accion.accionmejoracontinualector.Asignada=Asignada
accion.accionmejoracontinualector.Autor=Autor
accion.accionmejoracontinualector.Cerrada=Cerrada
accion.accionmejoracontinualector.Clave=Clave
accion.accionmejoracontinualector.Definida=Definida
accion.accionmejoracontinualector.editarAccionMejoraContinua=editarAccionMejoraContinua
accion.accionmejoracontinualector.Encargado=Encargado
accion.accionmejoracontinualector.Estado\:=Estado:
accion.accionmejoracontinualector.Estado=Estado
accion.accionmejoracontinualector.header.CONSULTADEACCIONESDEMEJORACONTINUA=CONSULTA DE ACCIONES DE MEJORA CONTINUA
accion.accionmejoracontinualector.Implementada=Implementada
accion.accionmejoracontinualector.NoAsignado=No Asignado
accion.accionmejoracontinualector.Noseencontraronacciones=No se encontraron acciones
accion.accionmejoracontinualector.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accionmejoracontinualector.Reportada=Reportada
accion.accionmejoracontinualector.Reportada=Reportada
accion.accionmejoracontinualector.TODOS=TODOS
accion.accionmejoracontinualector.Verificada=Verificada
accion.accionmejoracontinualist.Asignada=Asignada
accion.accionmejoracontinualist.AsignarlaAcciondemejoracontinua=: Asignar la Acci&oacute;n de mejora continua
accion.accionmejoracontinualist.Autor=Autor
accion.accionmejoracontinualist.Borrar=Borrar
accion.accionmejoracontinualist.BorrarlaAcciondemejoracontinua=Borrar la Acci&oacute;n de mejora continua
accion.accionmejoracontinualist.Cerrada=Cerrada
accion.accionmejoracontinualist.cerrarAccion=Cerrar Acci&oacute;n
accion.accionmejoracontinualist.Clave=Clave
accion.accionmejoracontinualist.ContinuardefiniendolaAcciondemejoracontinua=Continuar definiendo la Acci&oacute;n de mejora continua
accion.accionmejoracontinualist.Definida=Definida
accion.accionmejoracontinualist.DefinirlasAccionesatomar=: Definir las Acciones a tomar
accion.accionmejoracontinualist.DeseamarcarlaAccioncomocerrada=\u00bfDesea marcar la Acci\\u00f3n como cerrada?
accion.accionmejoracontinualist.DeseamarcarlaAccioncomoimplementada=\u00bfDesea marcar la Acci\\u00f3n como implementada?
accion.accionmejoracontinualist.editarAccionMejoraContinua=Editar Acci&oacute;n de Mejora Continua
accion.accionmejoracontinualist.Encargado=Encargado
accion.accionmejoracontinualist.Estado\:=Estado:
accion.accionmejoracontinualist.Estado=Estado
accion.accionmejoracontinualist.header.CONTROLDEACCIONESDEMEJORACONTINUA=CONTROL DE ACCIONES DE MEJORA CONTINUA
accion.accionmejoracontinualist.Implementada=Implementada
accion.accionmejoracontinualist.Laacciondemejoracontinuaseraborradadelsistema=\u00a1La acci\\u00f3n de mejora continua ser\\u00e1 borrada del sistema!
accion.accionmejoracontinualist.Marcarcomocerrada=: Marcar como cerrada
accion.accionmejoracontinualist.Marcarcomoimplementada=: Marcar como implementada
accion.accionmejoracontinualist.NoAsignado=No Asignado
accion.accionmejoracontinualist.Noseencontraronacciones=No se encontraron acciones
accion.accionmejoracontinualist.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accionmejoracontinualist.Reportada=Reportada
accion.accionmejoracontinualist.Reportada=Reportada
accion.accionmejoracontinualist.Sehaeliminadolaacciondemejoracontinua=Se ha eliminado la acci&oacute;n de mejora continua
accion.accionmejoracontinualist.Sehamarcadolaaccioncomocerrada=Se ha marcado la acci&oacute;n como cerrada
accion.accionmejoracontinualist.Sehamarcadolaaccioncomoimplementada=Se ha marcado la acci&oacute;n como implementada
accion.accionmejoracontinualist.siguientePaso=Siguiente Paso
accion.accionmejoracontinualist.--TODOS--=-- TODOS --
accion.accionmejoracontinualist.Verificada=Verificada
accion.accionmejoracontinualist.VerificarlaAcciondemejoracontinua=: Verificar la  Acci&oacute;n de Mejora Continua
accion.accionmejoracontinualist.VerlaAcciondemejoracontinua=: Ver la  Acci&oacute;n de mejora continua
accion.accionmejoracontinualist.VerlaAcciondemejoracontinua=: Ver la Acci&oacute;n de mejora continua
accion.accionpreventivahandle.Accion=Acci&oacute;n
accion.accionpreventivahandle.Accionesatomar=Acciones a tomar:
accion.accionpreventivahandle.Auditorverificador=Auditor verificador:
accion.accionpreventivahandle.Avance=Avance
accion.accionpreventivahandle.Borrar=Borrar
accion.accionpreventivahandle.boton.Asignar=Asignar
accion.accionpreventivahandle.boton.Cancelar=Cancelar
accion.accionpreventivahandle.boton.Cerrar=Cerrar
accion.accionpreventivahandle.boton.CrearAccion=Crear Acci&oacute;n
accion.accionpreventivahandle.boton.Evaluar=Evaluar
accion.accionpreventivahandle.boton.Grabarcambios=Grabar cambios
accion.accionpreventivahandle.boton.Limpiar=Limpiar
accion.accionpreventivahandle.Causa=raiz=Causa ra&iacute;z
accion.accionpreventivahandle.Causaraiz=Causa ra&iacute;z
accion.accionpreventivahandle.Clavedelaaccionpreventiva=Clave de la acci&oacute;n preventiva:
accion.accionpreventivahandle.Cumple-Nocumple=Cumple - No cumple:
accion.accionpreventivahandle.Editar=Editar
accion.accionpreventivahandle.--Encargado--=--Encargado--
accion.accionpreventivahandle.Fechadecumplimientofinal=Fecha de cumplimiento final:
accion.accionpreventivahandle.Fechadeevaluacion=Fecha de evaluaci&oacute;n:
accion.accionpreventivahandle.header.ACCIONESPREVENTIVAS=ACCIONES PREVENTIVAS
accion.accionpreventivahandle.Implementacion=Implementaci&oacute;n
accion.accionpreventivahandle.javascript.-Asignealmenosunaaccionatomar=- Asigne al menos una acci\\u00f3n a tomar
accion.accionpreventivahandle.javascript.-Auditorverificador=- Auditor verificador
accion.accionpreventivahandle.javascript.-Causaraiz=- Causa ra\\u00edz
accion.accionpreventivahandle.javascript.Deseaborrarlaaccionatomar=\u00bfDesea borrar la acci\\u00f3n a tomar?
accion.accionpreventivahandle.javascript.Elusuarioresponsableyauditorverificadorhansidoasignados=El usuario responsable y auditor verificador han sido asignados.
accion.accionpreventivahandle.javascript.-Evaluarresultado=- Evaluar resultado
accion.accionpreventivahandle.javascript.-Fechadeimplementacion=- Fecha de implementaci\\u00f3n
accion.accionpreventivahandle.javascript.Laaccionpreventivahansidoverificada=La acci&oacute;n preventiva han sido verificada.
accion.accionpreventivahandle.javascript.Laaccionpreventivahasidodadadealta=La acci&oacute;n preventiva ha sido dada de alta.
accion.accionpreventivahandle.javascript.LosDatosdelaacci\u00f3npreventivahansidoactualizados=Los datos de la acci&oacute;n preventiva han sido actualizados.
accion.accionpreventivahandle.javascript.LosDatosdelaaccionpreventivahansidoactualizados=Los datos de la acci&oacute;n preventiva han sido actualizados.
accion.accionpreventivahandle.javascript.-Noconformidadpotencial=- No conformidad potencial
accion.accionpreventivahandle.javascript.-Situacionactual=- Situaci\\u00f3n actual
accion.accionpreventivahandle.javascript.-Usuarioencargado=- Usuario encargado
accion.accionpreventivahandle.Noconformidadpotencial=No conformidad potencial:
accion.accionpreventivahandle.Nosehanasignadoaccionesatomar=No se han asignado acciones a tomar
accion.accionpreventivahandle.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accionpreventivahandle.Situacionactual=Situaci&oacute;n actual:
accion.accionpreventivahandle.UsuarioEncargado=Usuario Encargado:
accion.accionpreventivahandle.--Verificador--=--Verificador--
accion.accionpreventivalector.Asignada=Asignada
accion.accionpreventivalector.Autor=Autor
accion.accionpreventivalector.Cerrada=Cerrada
accion.accionpreventivalector.Clave=Clave
accion.accionpreventivalector.Definida=Definida
accion.accionpreventivalector.Encargado=Encargado
accion.accionpreventivalector.Estado\:=Estado:
accion.accionpreventivalector.Estado=Estado
accion.accionpreventivalector.header.CONSULTADEACCIONESPREVENTIVAS=CONSULTA DE ACCIONES PREVENTIVAS
accion.accionpreventivalector.Implementada=Implementada
accion.accionpreventivalector.NoAsignado=No Asignado
accion.accionpreventivalector.Noseencontraronacciones=No se encontraron acciones
accion.accionpreventivalector.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accionpreventivalector.Reportada=Reportada
accion.accionpreventivalector.Reportada=Reportada
accion.accionpreventivalector.--TODOS--=-- TODOS --
accion.accionpreventivalector.Verificada=Verificada
accion.accionpreventivalist.Asignada=Asignada
accion.accionpreventivalist.AsignarlaAccionPreventiva=: Asignar la  Acci&oacute;n Preventiva
accion.accionpreventivalist.Autor=Autor
accion.accionpreventivalist.Autor=Autor
accion.accionpreventivalist.Cerrada=Cerrada
accion.accionpreventivalist.Clave=Clave
accion.accionpreventivalist.Definida=Definida
accion.accionpreventivalist.DefinirlasAccionesatomar=: Definir las Acciones a tomar
accion.accionpreventivalist.Encargado=Encargado
accion.accionpreventivalist.Estado\:=Estado:
accion.accionpreventivalist.Estado=Estado
accion.accionpreventivalist.header.CONTROLDEACCIONESPREVENTIVAS=CONTROL DE ACCIONES PREVENTIVAS
accion.accionpreventivalist.Implementada=Implementada
accion.accionpreventivalist.javascript.\u00bfDeseamarcarlaAccioncomocerrada=\u00bfDesea marcar la Acci\\u00f3n como cerrada?
accion.accionpreventivalist.javascript.DeseamarcarlaAccioncomoimplementada=\u00bfDesea marcar la Acci\\u00f3n como implementada?
accion.accionpreventivalist.javascript.Laaccionpreventivaseraborradadelsistema=\u00a1La accion preventiva ser\\u00e1 borrada del sistema!
accion.accionpreventivalist.javascript.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
accion.accionpreventivalist.javascript.Sehaeliminadolaaccionpreventiva=Se ha eliminado la acci&oacute;n preventiva
accion.accionpreventivalist.javascript.Sehamarcadolaaccioncomocerrada=Se ha marcado la acci&oacute;n como cerrada
accion.accionpreventivalist.javascript.Sehamarcadolaaccioncomoimplementada=Se ha marcado la acci&oacute;n como implementada
accion.accionpreventivalist.Marcarcomocerrada=: Marcar como cerrada
accion.accionpreventivalist.Marcarcomoimplementada=: Marcar como implementada
accion.accionpreventivalist.NoAsignado=No Asignado
accion.accionpreventivalist.Noseencontraronacciones=No se encontraron acciones
accion.accionpreventivalist.Reportada=Reportada
accion.accionpreventivalist.--TODOS--=-- TODOS --
accion.accionpreventivalist.Verificada=Verificada
accion.accionpreventivalist.VerificarlaAccionPreventiva=: Verificar la  Acci&oacute;n Preventiva
accion.accionpreventivalist.VerlaAccionPreventiva=: Ver la  Acci&oacute;n Preventiva
accion.accionpreventivalist.VerlaAccionPreventiva=: Ver la  Acci&oacute;n Preventiva
accion.accionpreventivalist.VerlaAccionPreventiva=: Ver la  Acci&oacute;n Preventiva
accion.accionpreventivalist.VerlaAccionPreventiva=: Ver la  Acci&oacute;n Preventiva
accion.accionreportegeneral.chart.anallizadayenimplementacion=Analizada
accion.accionreportegeneral.chart.anallizadayenimplementacions=En implementaci&oacute;n
accion.accionreportegeneral.chart.aprobada=Verificada
accion.accionreportegeneral.chart.asignada=Asignada
accion.accionreportegeneral.chart.CANCELADA=Cancelada(No Procede)
accion.accionreportegeneral.chart.cerrada=Cerrada
accion.accionreportegeneral.chart.correctiva=Correctiva
accion.accionreportegeneral.chart.implementadayverificada=Ejecutada
accion.accionreportegeneral.chart.implementadayverificadas=Verificada
accion.accionreportegeneral.chart.mejoracontinua=Mejora Continua
accion.accionreportegeneral.chart.noasignada=No Asignada
accion.accionreportegeneral.chart.NOPROCEDE=No Efectiva
accion.accionreportegeneral.chart.numerodeacciones=N\u00famero de acciones
accion.accionreportegeneral.chart.preventiva=Preventiva
accion.accionreportegeneral.chart.PROCEDE=Efectiva
accion.accionreportegeneral.chart.producto=Sobre Producto
accion.accionreportegeneral.chart.proyecto=Sobre Proyecto
accion.accionreportegeneral.chart.reportada=Reportada
accion.accionreportegeneral.chart.title=Acciones
accion.accionreportegeneral.header=Reporte general de acciones
accion.accionreportegeneral.Responsable=Responsable
accion.accionreportegeneral.Tipodeaccion=Tipo de acci&oacute;n
accion.acciontomarhandleamc.ComentarioDeAvance=Comentario de Avance
action.statusReported = Reportada
action.statusAssigned = Asignada
action.statusAnalyzedAndInImplementation = Analizada y en implementaci\u00f3n
action.statusImplementedAndChecked = Implementada y en verificaci\u00f3n
action.statusApproved = Aprobada
action.statusClosedEffective = Cerrada Efectiva
action.statusClosedNotEffective = Cerrada No efectiva
action.statusNotApplicableCanceled = No procedi\u00f3 / Cancelada
accion.ejecuta.header.ERROR=ERROR
accion.ejecuta.NOSEPUDOREALIZARLAOPERACIONANTERIOR=NO SE PUDO REALIZAR LA OPERACI&Oacute;N ANTERIOR
accion.ejecutaamc.Define= Define
accion.ejecutaamc.Error= Error
accion.ejecutaamc.header.ERROR=ERROR
accion.ejecutaamc.Inserta= Inserta
accion.ejecutaamc.Modifica= Modifica
accion.ejecutaamc.NOSEPUDOREALIZARLAOPERACIONANTERIOR= NO SE PUDO REALIZAR LA OPERACI&Oacute;N ANTERIOR
accion.ejecutaamc.NOSEPUDOREALIZARLAOPERACIONANTERIOR=NO SE PUDO REALIZAR LA OPERACION ANTERIOR
accion.ejecutaamc.NosePudoRealizarlaOperacionAnterior=NO SE PUDO REALIZAR LA OPERACION ANTERIOR.
accion.ejecutaamc.Verifica= Verifica
accion.ejecutaamcNosePudoRealizarlaOperacionAnterior=NO SE PUDO REALIZAR LA OPERACION ANTERIOR.
accion.misacciones.AccionesATomar=Acciones a tomar
accion.misacciones.CanceladaNoProcede= Cancelada (No procede)
accion.misacciones.Clave=Clave
accion.misacciones.Consecuencia=Consecuencia
accion.misacciones.Editar=Editar
accion.misacciones.Efectiva=(Efectiva)
accion.misacciones.EncargadoDeUbicacion=Encargado de departamento
accion.misacciones.Estado=Estado
accion.misacciones.FechaImplementacion=Fecha de implementaci&oacute;n
accion.misacciones.FechaVerificacion=Fecha de verificaci&oacute;n
accion.misacciones.Fuente=Fuente
accion.misacciones.header.MisAcciones=MIS ACCIONES
accion.misacciones.NoEfectiva= (No Efectiva)
accion.misacciones.NoExistenAccionesEnLasQueParticipaActualmente=No existen acciones en las que participe actualmente.
accion.misacciones.NoParticipaEnAccionesATomarDeEstaAccion=No participa en acciones a tomar de esta acci&oacute;n.
accion.misacciones.Responsable=Responsable
accion.misacciones.ResponsableVerifcador=Responsable / Verificador
accion.misacciones.Rol=Rol
accion.misacciones.RolEnAccionATomar=Rol en acci&oacute;n a tomar
accion.misacciones.Situacion=Situaci&oacute;n
accion.misacciones.Ubicacion=Departamento
accion.misacciones.Verificador=Verificador
actividad.actividadhandle.Archivos=Archivos
actividad.actividadhandle.Catalogos=Cat&aacute;logos
actividad.actividadhandle.ComentarioDeAvance=Comentario de avance
actividad.actividadhandle.Descripcion=Descripci&oacute;n:
actividad.actividadhandle.Regresar=Ser\u00e1 enviado a la pantalla anterior, sin guardar. \u00bfSeguro de que desea continuar?
actividad.actividadhandle.Limpieza=\u00bfEst\u00e1 seguro de limpiar los campos?
actividad.actividadhandle.Dificultad=Dificultad
actividad.actividadhandle.Esconder=Esconder
actividad.actividadhandle.FechaFin=Fecha final
actividad.actividadhandle.FechaInicio=Fecha de inicio:
actividad.actividadhandle.FechaVerificacion=Fecha de Verificaci&oacute;n:
actividad.actividadhandle.javascript.-ComentarioDeAvance=- Comentario de avance
actividad.actividadhandle.javascript.-Descripcion=- Descripci\\u00f3n
actividad.actividadhandle.javascript.EstaSeguroQueNoDeseaVerificarLaActividad=\u00bfEst\\u00e1 seguro que no desea verificar la actividad?
actividad.actividadhandle.javascript.-FechaFin=- Fecha Final
actividad.actividadhandle.javascript.-FechaInicio=- Fecha Inicio
actividad.actividadhandle.javascript.-FechaVerificacion=- Fecha Verificaci\\u00f3n
actividad.actividadhandle.javascript.LaActividadDebeEmpezarDespuesDelDiaDeHoy=La actividad debe empezar despu&eacute;s del d&iacute;a de hoy
actividad.actividadhandle.javascript.LaActividadDebeSerVerificadaDespuesDelDiaDeHoy=La actividad debe ser verificada despu&eacute;s del d&iacute;a de hoy
actividad.actividadhandle.javascript.LaActividadDebeTerminarDespuesDelDiaDeHoy=La actividad debe terminar despu&eacute;s del d&iacute;a de hoy
actividad.actividadhandle.javascript.Laactividadhasidocreada=La actividad ha sido creada.
actividad.actividadhandle.javascript.LaActividadSeraVerificadaDeseaContinuar=La actividad ser\\u00e1 verificada. \u00bfDesea continuar?
actividad.actividadhandle.javascript.Losdatosdelaactividadhansidoactualizados=Los datos de la actividad han sido actualizados.
actividad.actividadhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunaactividad=Los siguientes datos son necesarios:
actividad.actividadhandle.javascript.-Nombre=- Nombre
actividad.actividadhandle.javascript.-Notas=- Notas
actividad.actividadhandle.javascript.-Presupuesto=- Presupuesto
actividad.actividadhandle.javascript.-RazonDeCambioDeFechaDeInicio=- Raz\\u00f3n de cambio de fecha de inicio
actividad.actividadhandle.javascript.-RazonDeCambioDeFechaDeVerificacion=- Raz\\u00f3n de cambio de fecha de verificaci\\u00f3n
actividad.actividadhandle.javascript.-RazonDeCambioDeFechaFinal=- Raz\\u00f3n de cambio de fecha final
actividad.actividadhandle.javascript.-RazonDeCambioDePorcentaje=- Raz\\u00f3n de cambio de porcentaje
actividad.actividadhandle.javascript.-RazonDeCambioDePresupuesto=- Raz\\u00f3n de cambio de presupuesto
actividad.actividadhandle.javascript.-RazonDeCambioDeVerificador=- Raz\\u00f3n de cambio de verificador
actividad.actividadhandle.javascript.-Responsable=- Responsable
actividad.actividadhandle.javascript.-TrabajoPlaneado=- Trabajo planeado
actividad.actividadhandle.javascript.-TrabajoRealizado=- Trabajo realizado
actividad.actividadhandle.javascript.-Verificador=- Verificador
actividad.actividadhandle.MisActividades=Mis actividades
actividad.actividadhandle.ModeloDeNegocios=Modelo de Negocios
actividad.actividadhandle.Mostrar=Mostrar
actividad.actividadhandle.Notas=Notas:
actividad.actividadhandle.Porcentajedecumplimiento=Porcentaje de cumplimiento:
actividad.actividadhandle.Presupuesto=Presupuesto:
actividad.actividadhandle.Responsable=Responsable:
actividad.actividadhandle.Tipo=Tipo
actividad.actividadhandle.TrabajoPlaneado=Trabajo planeado:
actividad.actividadhandle.TrabajoRealizado=Trabajo realizado
actividad.actividadhandle.Verificador=Verificador:
actividad.actividadlist.Actividad=Actividad
actividad.actividadlist.Borrar=Borrar
actividad.actividadlist.Concluida=Concluida
actividad.actividadlist.Creada=Creada
actividad.actividadlist.Editar=Editar
actividad.actividadlist.EnProceso=En Proceso
actividad.actividadlist.Estado=Estado
actividad.actividadlist.FechaFin=Fecha Final
actividad.actividadlist.FechaInicio=Fecha de Inicio
actividad.actividadlist.header.CONTROLACTIVIDADES=CONTROL DE ACTIVIDADES
actividad.actividadlist.Nosehaeliminadolaactividad=No se ha eliminado la actividad. Posiblemente tiene cambios pendientes.
actividad.actividadlist.Responsable=Responsable
actividad.actividadlist.Sehaeliminadolaactividad=Se ha eliminado la actividad.
actividad.actividadlist.Verificada=Verificada
actividad.actividadlist.Verificador=Verificador
actividades.actividadhandle.header.ACTIVIDADES=ACTIVIDADES
actividades.actividadhandle.Nombre=Nombre: 
actividadeslistpeticionespresupuesto.NoSeHaRealizadoNingunCambio=No se ha realizado ning&uacute;n cambio.
all.EsteCampoUnicamentePermiteCaracteresAlfanumericos=Este campo \\u00fanicamente permite caracteres alfanum\\u00e9ricos.
all.EsteCampoUnicamentePermiteNumeros=Este campo \\u00fanicamente permite n\\u00fameros.
auditoria.acciongenericahandle.Fechadeevaluacion=Fecha de evaluaci&oacute;n:
auditoria.auditoriaagenda.boton.Regresar=Regresar
auditoria.auditoriaagenda.Convoca=Convoca:
auditoria.auditoriaagenda.Cuestionario=Cuestionario:
auditoria.auditoriaagenda.header.AGENDA-=AGENDA-
auditoria.auditoriaagenda.Objetivos=Objetivos:
auditoria.auditoriaagenda.Observaciones=Observaciones:
auditoria.auditoriaagenda.Ubicacion=Departamento:
auditoria.auditoriaagenda.Vercomentarios=Ver comentarios
auditoria.auditoriahandle.AnticipacionparaNotificardelaAuditoria=Anticipaci&oacute;n para notificar de la auditor&iacute;a:
auditoria.auditoriahandle.Areas=Proceso:
auditoria.auditoriahandle.Auditorencargado=Auditor encargado:
auditoria.auditoriahandle.Auditorencargado=Auditor L&iacute;der:
auditoria.auditoriahandle.Auditores=Auditores de apoyo:
auditoria.auditoriahandle.boton.AgregarComentario=Agregar Observaci&oacute;n
auditoria.auditoriahandle.boton.CancelarAuditoria= Cancelar Auditor&iacute;a
auditoria.auditoriahandle.boton.Crear=Crear
auditoria.auditoriahandle.boton.NoAceptar=No Aceptar
auditoria.auditoriahandle.boton.Notificar=Notificar
auditoria.auditoriahandle.boton.Regresar=Regresar
auditoria.auditoriahandle.Clave=Clave:
auditoria.auditoriahandle.Comentario=Comentario
auditoria.auditoriahandle.Comentarios=Comentarios:
auditoria.auditoriahandle.Cuestionario=Cuestionario:
auditoria.auditoriahandle.Dia= Dia(s)
auditoria.auditoriahandle.Encargados=Encargados:
auditoria.auditoriahandle.Estatus=Estado:
auditoria.auditoriahandle.Facultad=Unidad productiva:
auditoria.auditoriahandle.Fecha=Fecha
auditoria.auditoriahandle.Fechafin=Fecha de t\u00e9rmino:
auditoria.auditoriahandle.Fechainicio=Fecha de inicio:
auditoria.auditoriahandle.header.AUDITORIAS=AUDITOR&Iacute;AS
auditoria.auditoriahandle.javascript.-Areas=- Procesos
auditoria.auditoriahandle.javascript.-Auditorencargado=- Auditor l\u00edder
auditoria.auditoriahandle.javascript.Auditorias=Auditor&iacute;as
auditoria.auditoriahandle.javascript.Auditorias=Auditorias
auditoria.auditoriahandle.javascript.Auditorias=Auditorias
auditoria.auditoriahandle.javascript.Consultar=Consultar
auditoria.auditoriahandle.javascript.Consultar=Consultar
auditoria.auditoriahandle.javascript.Consultar=Consultar\t
auditoria.auditoriahandle.javascript.Control=Control
auditoria.auditoriahandle.javascript.Crear=Crear
auditoria.auditoriahandle.javascript.-Cuestionario=- Cuestionario
auditoria.auditoriahandle.javascript.dardealtaunaauditoria=Dar de alta una auditor&iacute;a.
auditoria.auditoriahandle.javascript.Elcomentariohasidograbado=El comentario ha sido grabado.
auditoria.auditoriahandle.javascript.Esnecesarioponerunaobservacion=Es necesario poner una observaci\\u00f3n.
auditoria.auditoriahandle.javascript.-Fechafin=- Fecha de t\u00e9rmino
auditoria.auditoriahandle.javascript.-Fechainicio=- Fecha de inicio
auditoria.auditoriahandle.javascript.Laauditoriahasidoagendada=La auditor&iacute;a ha sido agendada.
auditoria.auditoriahandle.javascript.Lafechainicialdebesermenoroigualalafechafinal=La fecha inicial debe ser menor o igual a la fecha final
auditoria.auditoriahandle.javascript.Lanotificaci&oacute;nasidoenviadaviacorreoelectr&oacute;nico=La notificaci&oacute;n ha sido enviada v&iacute;a correo electr&oacute;nico.
auditoria.auditoriahandle.javascript.Lanotificaci\u00f3nasidoenviadaviacorreoelectr\u00f3nico=La notificaci&oacute;n ha sido enviada v&iacute;a correo electr&oacute;nico.
auditoria.auditoriahandle.javascript.Lanotificacionasidoenviadaviacorreoelectronico=La notificaci&oacute;n a sido enviada via correo electr&oacute;nico.
auditoria.auditoriahandle.javascript.Lanotificacionasidoenviadaviacorreoelectronico=La notificaci&oacute;n ha sido enviada v&iacute;a correo electr&oacute;nico.
auditoria.auditoriahandle.javascript.Losdatosdelaauditoriahansidoactualizados=Los datos de la auditor&iacute;a han sido actualizados.
auditoria.auditoriahandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
auditoria.auditoriahandle.javascript.Lossiguientesdatossonnecesariospara=Los siguientes datos son necesarios:
auditoria.auditoriahandle.javascript.-Objetivos=- Objetivos/Alcance
auditoria.auditoriahandle.javascript.-Ubicacion=- Departamento
auditoria.auditoriahandle.javascript.VerAuditoria=Ver Auditor&iacute;a
auditoria.auditoriahandle.javascript.-VerifiquelaHoradeInicioseaMenoralaHoradeFin=- Verifique que la hora de inicio sea menor a la hora de de t\u00e9rmino.
auditoria.auditoriahandle.javascript.VerifiquequelaFechaseaPosterioroIgualaladelDiadeHoy=Verifique que la fecha(s) sea posterior o igual a la dia del hoy.
auditoria.auditoriahandle.Objetivos/Alcance=Objetivos/Alcance:
auditoria.auditoriahandle.Observaciones=Observaciones:
auditoria.auditoriahandle.RazonporlaCuallaAuditoriaseraCancelada=Raz&oacute;n por la cual la auditor&iacute;a ser&aacute; cancelada
auditoria.auditoriahandle.Razonporlaquelaauditoriafuerechazada=Raz&oacute;n por la que la auditor&iacute;a fue rechazada.
auditoria.auditoriahandle.SehaCambiadolaAuditoriaCorrectamente= Se ha cancelado la auditor&iacute;a correctamente.
auditoria.auditoriahandle.--SELECCIONE--=-- SELECCIONE --
auditoria.auditoriahandle.--TODAS--=-- TODAS --
auditoria.auditoriahandle.Ubicacion=Departamento:
auditoria.auditoriahandle.Usuarios=Usuarios
auditoria.auditorialector.Auditorencargado=Auditor L\u00edder:
auditoria.auditorialector.AuditoriaCancelada=Auditor&iacute;a Cancelada
auditoria.auditorialector.AuditoriaPlaneada=Auditor&iacute;a Planeada
auditoria.auditorialector.Auditorresponsable=Auditor L\u00edder
auditoria.auditorialector.boton.Buscar=Buscar
auditoria.auditorialector.Clave=Clave
auditoria.auditorialector.Est=Estado
auditoria.auditorialector.Estado=Cerrada=Estado: Cerrada.
auditoria.auditorialector.EstadoAceptada=Estado: Aceptada.
auditoria.auditorialector.EstadoConfirmada=Estado: Confirmada.
auditoria.auditorialector.EstadoNotificada=Estado: Notificada.
auditoria.auditorialector.EstadoProgramada=Estado: Programada.
auditoria.auditorialector.EstadoRealizada=Estado: Realizada.
auditoria.auditorialector.Estatus=Estado:
auditoria.auditorialector.FechaF=Fecha T.
auditoria.auditorialector.Fechafin=Fecha de t\u00e9rmino:
auditoria.auditorialector.FechaI=Fecha I.
auditoria.auditorialector.Fechainicio=Fecha de inicio:
auditoria.auditorialector.header.CONSULTADEAUDITORIAS=CONSULTA DE AUDITOR&Iacute;AS
auditoria.auditorialector.Noseencontraronregistros=No se encontraron registros.
auditoria.auditorialector.Preg=Preg.
auditoria.auditorialector.--SELECCIONE--=-- SELECCIONE --
auditoria.auditorialector.Todas=TODAS
auditoria.auditorialector.Ubicacion=Departamento:
auditoria.auditorialector.Ver=Ver
auditoria.auditorialector.Verauditoria=Ver auditor&iacute;a
auditoria.auditorialector.Verpreguntas=Ver preguntas
auditoria.auditorialist.Acc=Acciones
auditoria.auditorialist.Agregaraccioncorrectiva=Agregar acci&oacute;n correctiva
auditoria.auditorialist.Auditorencargado=Auditor L\u00edder
auditoria.auditorialist.AuditoriaCancelada=Cancelada.
auditoria.auditorialist.AuditoriaConfirmada=Confirmada.
auditoria.auditorialist.AuditoriaPlaneada=Planeada.
auditoria.auditorialist.AuditoriasEnDondeEsAuditorResponsable=Donde es auditor l&iacute;der:
auditoria.auditorialist.AuditoriasEnDondeNoEsAuditorResponsable=Donde no es auditor l&iacute;der:
auditoria.auditorialist.AuditoriasPlaneadasDondeeselAutor=Donde es el autor:&nbsp;
auditoria.auditorialist.Auditoriasplaneadasdondenoeselautor= Donde no es el autor:
auditoria.auditorialist.AuditoriasPlaneadasDondenoeselAutor=Donde no es el autor:
auditoria.auditorialist.Auditoriasplaneadasdondenoeselautor=Donde no es el autor:
auditoria.auditorialist.Auditorresponsable=Auditor l&iacute;der
auditoria.auditorialist.AutordelaAuditoria=Autor de la Auditor&iacute;a
auditoria.auditorialist.Bor=Borrar
auditoria.auditorialist.Borrarauditoria=Borrar auditor&iacute;a
auditoria.auditorialist.boton.Buscar=Buscar
auditoria.auditorialist.Clave=Clave
auditoria.auditorialist.Departamento= Departamento
auditoria.auditorialist.Est=Estado
auditoria.auditorialist.EstadoAceptada=Aceptada.
auditoria.auditorialist.EstadoAuditoriacompleta=Estado: Auditor&iacute;a Completa.
auditoria.auditorialist.EstadoNotificadaAccionMarcarcomorealizada=El estado actual es NOTIFICADA. Presione para marcar como REALIZADA.
auditoria.auditorialist.EstadoProgramadaAccionAutorizar=El estado actual es PROGRAMADA. Presione para AUTORIZAR.
auditoria.auditorialist.EstadoProgramadaAccionNotificar=El estado actual es PROGRAMADA. Presione para NOTIFICAR.
auditoria.auditorialist.EstadoRealizadaAccionCerrar=El estado actual es REALIZADA. Presione para CERRAR.
auditoria.auditorialist.Estatus=Estado:
auditoria.auditorialist.FechadeInicio=Fecha de Inicio
auditoria.auditorialist.FechadeTermino=Fecha de T&eacute;rmino
auditoria.auditorialist.FechaF=Fecha final
auditoria.auditorialist.Fechafin=Fecha de t\u00e9rmino:
auditoria.auditorialist.FechaI=Fecha inicial
auditoria.auditorialist.Fechainicio=Fecha de inicio:
auditoria.auditorialist.Proceso=Proceso:
auditoria.auditorialist.header.CONTROLDEAUDITORIAS-USR-=CONTROL DE AUDITOR&Iacute;AS-USR-
auditoria.auditorialist.javascript.Auditorias=Auditor&iacute;as
auditoria.auditorialist.javascript.CambiaraelestadodelaauditoriaDeseacontinuar=Cambiar\\u00e1 el estado de la auditor\\u00eda.\\n\u00bfDesea continuar?
auditoria.auditorialist.javascript.Crearauditoria=Crear auditor&iacute;a
auditoria.auditorialist.javascript.Deseacontinuar=\u00bfDesea continuar?
auditoria.auditorialist.javascript.elcuestionariodelaauditoriaDeseahacerlo=el cuestionario de la auditor\\u00eda. \u00bfDesea hacerlo?
auditoria.auditorialist.javascript.Elimineesasligasyvuelvaaintentarlo=Elimine esas ligas y vuelva a intentarlo.
auditoria.auditorialist.javascript.estaligadoaotraspartesdentrodelsistema=esta ligado a otras partes dentro del sistema.
auditoria.auditorialist.javascript.Laauditoriaseraremovidadelsistema,asicomoloscomponentesligadasaella=La auditor\\u00eda ser\\u00e1 removida del sistema, as\\u00ed como los componentes ligadas a ella.
auditoria.auditorialist.javascript.Laseleccionnopudosereliminada=La selecci\\u00f3n no pudo ser eliminada.
auditoria.auditorialist.javascript.Laseleccionnopuedesereliminada,debidoaque=La selecci\\u00f3n no puede ser eliminada, debido a que
auditoria.auditorialist.javascript.Listarauditorias=Listar auditor&iacute;as
auditoria.auditorialist.javascript.Losparticipantesenlaauditoriahansidonotificados.=Los participantes en la auditor&iacute;a han sido notificados.
auditoria.auditorialist.javascript.Losparticipantesenlaauditoriahansidonotificados=Los participantes en la auditor\\u00eda han sido notificados.
auditoria.auditorialist.javascript.LosparticipantesserannotificadosviacorreoelectronicoDeseacontinuar=Los participantes ser\\u00e1n notificados v\\u00eda correo electr\\u00ednico.\\n\u00bfDesea continuar?
auditoria.auditorialist.javascript.Paramarcarlaauditoriacomorealizadadeberallenar=Para marcar la auditor\\u00eda como realizada deber\\u00e1 llenar
auditoria.auditorialist.javascript.VerAuditoria=Ver Auditor&iacute;a
auditoria.auditorialist.Laseleccionhasidoeliminada=La selecci&oacute;n ha sido eliminada.
auditoria.auditorialist.NoSeEncontraronAuditoriasEnDondeEsAuditorResponsable=No se encontraron auditor&iacute;as en donde es auditor l\\u00edder.
auditoria.auditorialist.NoSeEncontraronAuditoriasEnDondeNoEsAuditorResponsable=No se encontraron auditor&iacute;as en donde no es auditor l\\u00edder.
auditoria.auditorialist.Noseencontraronauditoriasplaneadas= No se encontraron auditor&iacute;as planeadas:
auditoria.auditorialist.Noseencontraronauditoriasplaneadas= No se encontraron auditor\u00edas planeadas.
auditoria.auditorialist.NoseEncontraronAuditoriasPlaneadas=No se encontraron auditor\u00edas planeadas.
auditoria.auditorialist.Noseencontraronauditoriasplaneadas=No se encontraron auditor\u00edas planeadas.
auditoria.auditorialist.Noseencontraronregistros=No se encontraron registros.
auditoria.auditorialist.PendientePorAceptar= &nbsp;(Pendiente por Aceptar)
auditoria.Auditorialist.Planeadasdondeeselautor=Planeadas donde es el autor:
auditoria.auditorialist.Preg=Preguntas
auditoria.auditorialist.--SELECCIONE--=-- SELECCIONE --
auditoria.auditorialist.Todas=TODAS
auditoria.auditorialist.TodasAuditorias= Todas las Auditor&iacute;as:
auditoria.auditorialist.TodasLasAuditorias =Todas las Auditor\u00edas
auditoria.auditorialist.Ubicacion=Departamento:
auditoria.auditorialist.Ver=Ver
auditoria.auditorialist.Verauditoria=Ver auditor&iacute;a
auditoria.auditorialist.Verpreguntasdelaauditoria=Ver preguntas de la auditor&iacute;a
auditoria.auditoriaquestionhandle.Borrar=Borrar
auditoria.auditoriaquestionhandle.BorrarPregunta=Borrar Pregunta
auditoria.auditoriaquestionhandle.boton.Agregar=Agregar
auditoria.auditoriaquestionhandle.boton.Cancelar=Cancelar
auditoria.auditoriaquestionhandle.boton.EditarAuditoria=Editar Auditor&iacute;a
auditoria.auditoriaquestionhandle.boton.Guardar=Guardar
auditoria.auditoriaquestionhandle.Estado=Estado:
auditoria.auditoriaquestionhandle.header.CUESTIONARIO/PREGUNTAS=CUESTIONARIO/PREGUNTAS
auditoria.auditoriaquestionhandle.javascript.Elcuestionarioseacabadardealta=El cuestionario se acaba dar de alta.
auditoria.auditoriaquestionhandle.javascript.LapreguntaseraeliminadadelalistaDeseacontinuar=La pregunta ser\\u00e1 eliminada de la lista.\\n\u00bfDesea continuar?
auditoria.auditoriaquestionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunapregunta=Los siguientes datos son necesarios para dar de alta una pregunta
auditoria.auditoriaquestionhandle.javascript.-Redacciondelapregunta=- Redacci\\u00f3n de la pregunta
auditoria.auditoriaquestionhandle.javascript.-Secciondelapregunta=- Secci\\u00f3n de la pregunta
auditoria.auditoriaquestionhandle.javascript.SevaagenerarunnuevocuestionarioconlaspreguntasqueestanenlalistaDeseacontinuar=Se va a generar un nuevo cuestionario con las preguntas que est\\u00e1n en la lista.\\n\u00bfDesea continuar?
auditoria.auditoriaquestionhandle.NoHayPreguntasPorGuardar= No hay preguntas por guardar.
auditoria.auditoriaquestionhandle.Pregunta=Pregunta
auditoria.auditoriaquestionhandle.Redacciondelapregunta=Redacci&oacute;n de la pregunta:
auditoria.auditoriaquestionhandle.Secciinalaquepertenece=Secci&oacute;n a la que pertenece:
auditoria.auditoriaquestionhandle.Secciinalaquepertenece=Secci&oacute;n al que pertenece:
auditoria.auditoriaquestionhandle.--Seccion--=-- Secci&oacute;n --
auditoria.auditoriaquestionhandle.Seccion=Secci&oacute;n
auditoria.auditoriaquestionhandle.Seccionalaquepertenece=Secci&oacute;n a la que pretenece
auditoria.auditoriaquestionhandle.Seccionalaquepertenece=Secci&oacute;n al que pretenece
auditoria.auditoriareportegeneral.Catalogo=Cat&aacute;logo
auditoria.auditoriareportegeneral.chart.aceptada=ACEPTADA
auditoria.auditoriareportegeneral.chart.autorizada=AUTORIZADA
auditoria.auditoriareportegeneral.chart.autorizadaconavisos=AUTORIZADA CON AVISOS
auditoria.auditoriareportegeneral.chart.cancelada=CANCELADA
auditoria.auditoriareportegeneral.chart.cerrada=CERRADA
auditoria.auditoriareportegeneral.chart.confirmada=CONFIRMADA
auditoria.auditoriareportegeneral.chart.numerodeauditorias=N\u00famero de auditor\u00edas
auditoria.auditoriareportegeneral.chart.programada=PROGRAMADA
auditoria.auditoriareportegeneral.chart.realizada=REALIZADA
auditoria.auditoriareportegeneral.chart.title=Auditor\u00edas
auditoria.auditoriareportegeneral.chart.title2=Auditor&iacute;as
auditoria.auditoriareportegeneral.Facultad= Unidad productiva:
auditoria.auditoriareportegeneral.header=Reporte general de Auditor&iacute;as
auditoria.auditoriareportegeneral.RangodeFechasdelReporte=Rango de fechas del reporte:
auditoria.auditoriareportegeneral.REPORTEDEAGENDADEAUDITORIA=Reporte
auditoria.auditoriareportegeneral.ReportedeAgendadeAuditorias=REPORTE DE AGENDA DE AUDITOR&Iacute;AS
auditoria.auditoriareportegeneral.REPORTEDERESULTADOSAUDITORIAS=Reporte de Resultados de Auditor&iacute;as
auditoria.auditoriareportegeneral.REPORTESGENERALES=Reportes Generales
auditoria.auditoriareportegeneral.TipoReporte= Tipo de Reporte:
auditoria.auditoriareportegeneral.TODOS=-- TODOS --
auditoria.misauditorias.AreaAuditada=Proceso Auditado
auditoria.misauditorias.Auditorencargado=Auditor L\u00edder
auditoria.misauditorias.AuditoriaCancelada=Cancelada.
auditoria.misauditorias.AuditoriaConfirmada,PresioneparaMarcarcomoRealizada=Auditor&iacute;a CONFIRMADA. Presione para marcar como REALIZADA.
auditoria.misauditorias.AuditoriaPlaneada=Auditor&iacute;a Planeada
auditoria.misauditorias.AuditoriasEnLasQueParticipasComoAuditorEncargado=Donde es Auditor L&iacute;der          
auditoria.misauditorias.AuditoriasEnLasQueParticipasComoAuditorParticipante=Donde es Auditor de Apoyo
auditoria.misauditorias.AuditoriasEnLasQueParticipasComoEncargadoDeArea=Donde es Encargado de Proceso
auditoria.misauditorias.Auditorresponsable=Auditor L\u00edder
auditoria.misauditorias.Clave=Clave
auditoria.misauditorias.ElEstadoActualesACEPTADA,PresioneparaCerrar=El estado actual es ACEPTADA. Presione para CERRAR.
auditoria.misauditorias.Est=Estado
auditoria.misauditorias.EstadoAceptada = Aceptada. 
auditoria.misauditorias.EstadoCerrada=Cerrada.
auditoria.misauditorias.EstadoConfirmada=Confirmada. 
auditoria.misauditorias.EstadoNotificada=Notificada. 
auditoria.misauditorias.EstadoProgramada=Programada. 
auditoria.misauditorias.EstadoRealizada=Realizada. 
auditoria.misauditorias.Estatus=Estado
auditoria.misauditorias.FechaF=Fecha T.
auditoria.misauditorias.Fechafin=Fecha de T\u00e9rmino
auditoria.misauditorias.FechaI=Fecha I.
auditoria.misauditorias.Fechainicio=Fecha de Inicio
auditoria.misauditorias.header.MISAUDITORIAS=MIS AUDITOR&Iacute;AS
auditoria.misauditorias.Horadeinicio=Hora de inicio
auditoria.misauditorias.Horafin=Hora de t\u00e9rmino
auditoria.misauditorias.NoSeEncontraronRegistros=No se encontraron registros
auditoria.misauditorias.PendientePorAceptar= &nbsp;(Pendiente por Aceptar)
auditoria.misauditorias.Preg=Preguntas
auditoria.misauditorias.PresioneparaAceptarResultados= Presione para aceptar resultados.
auditoria.misauditorias.PresioneparaCerrarla= Presione para cerrarla.
auditoria.misauditorias.PresioneparaMarcarcomoRealizada = Presione para marcar como realizada.
auditoria.misauditorias.--SELECCIONE--=--SELECCIONE--
auditoria.misauditorias.Tienequeesperaraquelaauditoriasearealizada=Tiene que esperar a que la auditor\u00eda sea realizada.
auditoria.misauditorias.Todas=Todas
auditoria.misauditorias.Ubicacion=Departamento
auditoria.misauditorias.Ver=Ver
auditoriareportegeneral.auditado=Auditado
auditoriareportegeneral.auditorencargado=Auditor L\u00edder
auditoriareportegeneral.clausulas=Clausulas
auditoriareportegeneral.conformidad=Conformidad
auditoriareportegeneral.departamento=Departamento
auditoriareportegeneral.desglosehallazgos=Desglose de Hallazgos
auditoriareportegeneral.fecha=Fecha
auditoriareportegeneral.fechafin=Fecha de T\u00e9rmino
auditoriareportegeneral.fechainicio=Fecha de Inicio
auditoriareportegeneral.formatoagenda=FORMATO DE AUDITOR\u00cdAS AGENDADAS
auditoriareportegeneral.hora=Hora
auditoriareportegeneral.noauditoria=Numero de Auditoria
auditoriareportegeneral.pregunta=Pregunta
auditoriareportegeneral.proceso=Proceso
auditoriareportegeneral.procesos=Procesos
auditoriareportegeneral.rangofecha=Rango de Auditor\u00edas
auditoriareportegeneral.reporteresultados=REPORTE DE RESULTADOS DE AUDITOR\u00cdA
auditoriareportegeneral.respuesta=Respuesta
auditoriareportegeneral.sistemacalidad=SISTEMA DE CALIDAD
auditoriareportegeneral.subprocesos=Sub-Procesos
auditorias.auditoriareportegeneral.DebeEspecificarUnRangoDeFechas= Debe especificar un rango de fechas.
auditorias.misauditorias.auditorResponsable=Auditor L\u00edder
auditorias.misauditorias.clave=Clave
auditorias.misauditorias.fechaInicio=Fecha de Inicio
auditorias.misauditorias.fechaTermino=Fecha de T\u00e9rmino
auditorias.preguntasobv.Nosehandadodealtaaccionesquenoest\u00e9nrelacionadasapreguntasenestaauditor\u00eda=No se han dado de alta acciones que no est&eacute;n relacionadas a preguntas en esta auditor&iacute;a
auditorias.preguntasobv.Nosehandadodealtaaccionesquenoestenrelacionadasapreguntasenestaauditoria=No se han dado de alta acciones que no est&eacute;n relacionadas a preguntas en esta auditor&iacute;a.
auditorias.preguntasonv.Accionesnorelacionadasapreguntas=Acciones no relacionadas a preguntas
auditorias.preguntasonv.Autor=Autor
auditorias.preguntasonv.Borrar=Borrar
auditorias.preguntasonv.Clave=Clave
auditorias.preguntasonv.Estado=Estado
auditorias.preguntasonv.Estapreguntanocontieneaccioneslevantadas=Esta pregunta no contiene acciones levantadas.
auditorias.preguntasonv.Fecha/HoraCreacion=Fecha/Hora Creacion
auditorias.preguntasonv.veracciones=Ver Acciones
boton.Aceptar=Guardar
boton.Actualizar=Actualizar
boton.Agregar=Agregar
boton.Alta=Alta
boton.Buscar=Buscar
boton.Cancelar=Cancelar
boton.Cargar= Cargar
boton.Cerrar=Cerrar
boton.Confirmar= Confirmar
boton.Consulta=Consulta
boton.Control=Control
boton.Crear=Crear
boton.Editar=Editar
boton.EnviarArchivos=Subir Archivos
boton.Graficar=Aplicar filtros
boton.Guardar=Guardar
boton.Imprimir=Imprimir
boton.Limpiar=Limpiar
boton.LimpiarForma=Limpiar
boton.Listar=Listar
boton.MisAcciones=Mis acciones
boton.MisActividades=Mis Actividades
boton.MisAuditorias=Mis auditor&iacute;as
boton.Mostrar=Mostrar
boton.Mover=Mover
boton.No=\ \ \ \ No\ \ \ \\ 
boton.Proyectos=Proyectos
boton.ProyectosCambiosPendientes=Proyectos con pendientes
boton.Refrescar=Refrescar
boton.Regresar=Regresar
boton.Si=\ \ \ \ S\u00ed\ \ \ \\
boton.Ver=Ver
boton.VerSolicitud=Ver Solicitud
boton.SaveFiles=Guardar archivos
busqueda.busqueda.Buscar = Buscar
busqueda.busqueda.En = En
busqueda.busqueda=B&uacute;squeda
busqueda.busquedaAvanzada.esconder=Esconder
busqueda.busquedaAvanzada.mostrar=Mostrar
busqueda.busquedaAvanzada=B&uacute;squeda avanzada
campoFechas.LasFechasNoConcuerdanConLasFechasDeLasActividades=Las fechas no concuerdan con las fechas de las Actividades
campoFechas.LasFechasNoSeEncuentranEnElOrdenCorrecto=Las fechas no se encuentran el el orden correcto
campoFechas.VerifiqueLasFechas=Verifique las fechas
campoNumeros.DeseaModificarAutomaticamenteElPresupuestoDelProyecto=\u00bfDesea modificar autom\\u00e1ticamente el presupuesto del proyecto?
campoNumeros.ElPresupuestoDebeContenerUnicamenteNumeros=El presupuesto debe contener \\u00fanicamente n\\u00fameros.
campoNumeros.ElPresupuestoDebeSerMayorAlPresupuestoTotalDeActividades=El presupuesto debe ser mayor al presupuesto total de activdades
campoNumeros.ElPresupuestoNoPuedeExcederMilMillones=El presupuesto no puede exceder mil millones
campoNumeros.ElPresupuestoNoPuedeSerMenorAlTotalGastado=El presupuesto no puede ser menor al total gastado
campoNumeros.ElPresupuestoSeExcedeDe=El presupuesto se excede de
campoNumeros.VerifiqueElCampoDePresupuesto=Verifique el campo de presupuesto
campoNumeros.VerifiquePuntoDecimal=Verifique el punto decimal.
campoNumeros.VerifiqueRangosDelPresupuesto=Verifique rangos del presupuesto
catalogo.lugarAlmacenamiento.Almacena=Genera registros que se almacenan:
combo.--NINGUNO--=-- NINGUNO --
combo.--SELECCIONE--=-- SELECCIONE --
combo.---SINPERFIL---=--- SIN PERFIL ---
combo.--TODAS--=-- TODAS --
combo.--TODOS--=-- TODOS --
common.actionall.Administrador=Administrador
common.actionall.delsistema=del sistema.
common.actionall.Elprocesofuerealizado=El proceso fue realizado
common.actionall.Elprocesonofuerealizado=El proceso no fue realizado
common.actionall.Espereunmomentoporfavor=Espere un momento por favor...
common.actionall.Espereunmomentoporfavor=Espere un momento por favor...
common.actionall.Exito=El proceso fue realizado
common.actionall.Falla=El proceso no fue realizado
common.actionall.Laencuestaseguardocorrectamente=La encuesta se guard\u00f3 correctamente.
common.actionall.Lainformacionnopudoseractualizada=La informaci&oacute;n no pudo ser actualizada.
common.actionall.Noseobtuvierondatos=No se obtuvieron datos.
common.actionall.Porfavorintentedenuevo=Por favor intente de nuevo.
common.actionall.Sielerrorcontinuaporfavorcontacteal=Si el error continua por favor contacte al
common.bottomForm.ElPieDeLaFormaNoPuedeEstarVacio= El pie de la forma no puede estar vacio.
common.bottomForm.NoCuentaConPermisosParaRealizarEstaAccion= No cuenta con permisos para realizar esta accion.
common.bottomForm.PieDeForma,LadoDerecho= PIE DE FORMA, LADO DERECHO
common.bottomForm.PieDeForma,LadoIzquierdo= PIE DE FORMA, LADO IZQUIERDO
common.bottomForm.Texto= Texto:
common.catalogo.Activo= Activo
common.catalogo.CalificacionesCuestionarios= CALIFICACIONES CUESTIONARIOS
common.catalogo.CATALOGO= CATALOGO &nbsp;
common.catalogo.CONTROLMASIVO=CONTROL MASIVO &nbsp;
common.catalogo.Descripcion= Descripci\u00f3n
common.catalogo.Formas= Forma
common.catalogo.FuentedeAcciones= FUENTES DE ACCIONES
common.catalogo.FuentedeQuejas= FUENTES DE QUEJAS
common.catalogo.TipoAnalisisQuejas= TIPOS DE AN\u00c1LISIS DE QUEJAS
common.catalogo.Inactivo= Inactivo
common.catalogo.LosPiesdelaFormaestanActivos,PresioneParaVerlaVistaPreviaEstandardeEstaForma= Los pie de forma siempre estan activos. Presione para ver la vista previa estandar de esta forma.
common.catalogo.NombredelCampo= Nombre del Campo
common.catalogo.NoseCuentaconPermisosParaVerEsteCatalogo= No cuenta con permisos para ver este cat&aacute;logo.
common.catalogo.NosePudoCambiarelEstadodelRegistro= No se pudo cambiar el estado del registro.
common.catalogo.NosePuedeActualizarelRegistro= No se pudo actualizar el registro.
common.catalogo.NosePuedeBorrarelRegistro= No se pudo borrar el registro.
common.catalogo.PiedeFormaDerecho= Pie de forma Derecho
common.catalogo.PiedeFormaIzquierdo= Pie de forma Izquierdo
common.catalogo.PiedeFormas= PIE DE FORMAS
common.catalogo.SeActualizoelRegistroCorrectamente= Se actualiz&oacute; el registro correctamente.
common.catalogo.SeBorroelRegistroCorrectamente= Se borro el registro correctamente.
common.catalogo.SeCambioelEstadodelRegistroCorrectamente= Se cambio el estado del registro correctamente.
common.catalogo.TIPOSDEDOCUMENTOS=TIPOS DE DOCUMENTOS
common.catalogo.Valor= Valor
common.catalogolist.BusquedaEstado= Estado
common.catalogolist.BusquedaNombre=Nombre
common.catalogoslist.Activo= Activo
common.catalogoslist.Busqueda= B&uacute;squeda
common.catalogoslist.CalificacionesCuestionarios=CALIFICACIONES CUESTIONARIOS
common.catalogoslist.CATALOGO=CAT&Aacute;LOGO &nbsp;
common.catalogoslist.Descripcion= Descripci\u00f3n
common.catalogoslist.Estado= Estado:
common.catalogoslist.Esteelementonopuedeserdesactivado=Este elemento no puede ser desactivado
common.catalogoslist.FuentedeAcciones=FUENTES DE ACCIONES
common.catalogoslist.FuentedeAuditorias=ESTADO DE AUDITORIAS
common.catalogoslist.FuentedeQuejas=FUENTES DE QUEJAS
common.catalogoslist.TipoAnalisisQuejas= TIPOS DE AN\u00c1LISIS DE QUEJAS
common.catalogoslist.Inactivo= Inactivo
common.catalogoslist.NoCuentaconPermisosparaVeresteCatalogo= No cuenta con permisos para ver este cat&aacute;logo.
common.catalogoslist.Nombre= Nombre del Campo
common.catalogoslist.NombredelCampo= Nombre del Campo
common.catalogoslist.NosePudoCambiarelEstadodelRegistro= No se pudo cambiar el estado del registro.
common.catalogoslist.NosePuedeActualizarelRegistro=No se pudo actualizar el registro.
common.catalogoslist.NosePuedeBorrarelRegistro,estaSiendoUtilizadoporOtroRecurso= No se pudo borrar el registro, esta siendo utilizado por otro recurso.
common.catalogoslist.SeActualizoelRegistroCorrectamente= Se actualiz&oacute; el registro correctamente.
common.catalogoslist.SeBorroelRegistroCorrectamente= Se borro el registro correctamente.
common.catalogoslist.SeCambioelEstadodelRegistroCorrectamente= Se cambio el estado del registro correctamente.
common.catalogoslist.TODOS= --- TODOS ---
common.catalogoslist.Valor= Valor
common.comentariohandle.boton.Crear=Crear
common.comentariohandle.Clavecomentada=Clave comentada:
common.comentariohandle.Detalle=Detalle:
common.comentariohandle.header.COMENTARIOS=COMENTARIOS
common.comentariohandle.javascript.Crear=Crear
common.comentariohandle.javascript.Elcomentariohasidoagregado=El comentario ha sido agregado.
common.comentariohandle.javascript.Laminutahasidoagregada=La minuta ha sido agregada.
common.comentariohandle.javascript.Listar=Listar
common.comentariohandle.javascript.Necesitahaceralguncomentario=Necesita hacer algun comentario
common.comentariohandle.Responsable=Responsable:
common.comentariohandle.Tipo=Tipo:
common.comentariohandle.cancelConfirm=La informaci\u00f3n no ser\u00e1 guardada. \u00bfEst\u00e1 seguro de cancelar?
common.comentariolist.Autor=Autor
common.comentariolist.Borrar=Borrar
common.comentariolist.BorrarComentario=Borrar Comentario
common.comentariolist.boton.Cerrar=Cerrar
common.comentariolist.Clave=Clave
common.comentariolist.Clavecomentada=Clave comentada
common.comentariolist.Clavecomentada=Clave comentada
common.comentariolist.Comentario=Comentario:
common.comentariolist.Contestado=Contestado
common.comentariolist.Detalle=Detalle
common.comentariolist.Estado=Estado
common.comentariolist.Fecha=Fecha
common.comentariolist.FechaFin=Fecha fin
common.comentariolist.FechaInicio=Fecha inicio
common.comentariolist.header.CONSULTADECOMENTARIOS=CONSULTA DE COMENTARIOS
common.comentariolist.header.CONSULTADECOMENTARIOS-=CONSULTA DE COMENTARIOS-
common.comentariolist.javascript.Elcomentarioseraeliminadodelsistema=El comentario ser\\u00e1 eliminado del sistema
common.comentariolist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=La selecci\\u00f3n no puede ser eliminada, debido a que \nest\\u00e1 ligado a otras partes dentro del sistema.\n\nElimine esas ligas y vuelva a intentarlo.
common.comentariolist.Laseleccionhasidoeliminada=La seleccion ha sido eliminada.
common.comentariolist.Leido=Le&iacute;do
common.comentariolist.Noseencontraronregistros=No se encontraron registros.
common.comentariolist.Nuevo=Nuevo
common.comentariolist.Tipo=Tipo
common.comentariolist.Ubicacion=Departamento
common.comentariolist.VerDetalle=Ver Detalle
common.input.ALTADELUGARDEALMACENAMIENTO=ALTA DE LUGARES DE ALMACENAMIENTO
common.input.ALTADETIPOSDEDOCUMENTO=ALTA DE TIPOS DE DOCUMENTO
common.input.ALTALUGARALMACENAMIENTO=ALTA DE LUGAR DE ALMACENAMIENTO
common.input.ALTADECLASIFICACIONINFORMACION=ALTA DE CLASIFICACI\u00d3N DE INFORMACI\u00d3N
common.input.ALTADEDISPOSICION=ALTA DE DISPOSICI\u00d3N
common.input.-Catalogo=- Cat\\u00e1logo
common.input.Descripcion= Descripci&oacute;n:
common.input.-Descripcion=- Descripci\\u00f3n
common.input.ElRegistroSeGuardoCorrectamente=El registro se guard&oacute; correctamente.
common.input.Faltandellenarlossiguientescampos=Faltan de llenar los siguientes campos:
common.input.FUENTEDEACCION=FUENTE DE ACCI&Oacute;N
common.input.FUENTEDEQUEJA= FUENTE DE QUEJA
common.input.Liga=- Liga
common.input.Nombre= Nombre:
common.input.NoSePuedeGuardarElRegistro,IntenteDeNuevoOContacteAlAdminsitradorDelSistema= No se pudo guardar el registro, intente de nuevo o contacte al administrador del sistema.
common.input.TIPODECALIFICACION=TIPO DE CALIFICACI\\u00d3N
common.input.-Valor=- Valor
common.input.Valor= Valor:
common.papelera.Auditoriaenestado=Auditoria en estado
common.papelera.AUDITORIAS=auditor\u00edas
common.papelera.Auditorresponsable=Auditor l&iacute;der
common.papelera.Auditorresponsable=Auditor L\u00edder
common.papelera.B\u00fasqueda=B\u00fasqueda
common.papelera.Borrar=Borrar
common.papelera.Borrar2=Borrar
common.papelera.Clave=Clave
common.papelera.Cuestionario=Cuestionario
common.papelera.Cuestionario2=Cuestionario
common.papelera.ELIMINADA=ELIMINADA
common.papelera.Estado=Estado
common.papelera.FechadeInicio=Fecha de Inicio
common.papelera.FechaF=Fecha F.
common.papelera.FechaI=Fecha I.
common.papelera.LaAuditoriaSer\u00e1Borrada=La auditor\\u00eda ser\\u00e1 borrada. \u00bfEst\\u00e1 seguro de que desea continuar?
common.papelera.Mostrarfiltrodebusqueda=Mostrar filtro de busqueda
common.papelera.Nocuentaconpermisossuficientes=No cuenta con permisos suficientes.
common.papelera.Ocultarfiltrodebusqueda=Ocultar filtro de busqueda
common.papelera.PAPELERADE=PAPELERA DE
common.papelera.presioneaquipararestaurarelregistro=presione aqui para restaurar el registro.
common.papelera.Seencuentraenlaprimerahoja=Se encuentra en la primera hoja.
common.papelera.Seencuentraenlaultimahoja=Se encuentra en la ultima hoja.
common.papelera.--SELECCIONE--=-- SELECCIONE --
common.papelera.SINPERMISOS=SIN PERMISOS
common.papelera.Ver=Ver
common.papelera.Ver2=Ver
common.pendienteshandle.Actualizarpendientes=Actualizar pendientes
common.pendienteshandle.Ayuda=Ayuda
common.pendienteshandle.Cerrarsesion=Salir
common.pendienteshandle.Checarpornuevospendientes=Checar por nuevos pendientes
common.pendienteshandle.MiCuenta=Mi Cuenta
common.pendienteshandle.Notienespendientes=No tienes pendientes
common.pendienteshandle.Tienespendientes=Tienes pendientes
common.top.Acciones=Acciones
common.top.Auditorias=Auditor&iacute;as
common.top.Configuracion=Configuracion
common.top.Documentos=Documentos
common.top.imagespath=header
common.top.Proyectos=Proyectos
common.top.Ustedtieneunatareapendienteenlaformaactual.PresioneelbotonCancelaroRegresarparapoderrealizarotraaccion=Usted tiene una tarea pendiente en la forma actual. \\nPresione el bot\\u00f3n "Cancelar" \\u00f3 "Regresar" para poder realizar otra acci\\u00f3n.
configuracion.ACTIVO,presioneparamarcarcomoINACTIVO=ACTIVO, presione para marcar como INACTIVO
configuracion.areahandle.boton.Actualizar=Actualizar
configuracion.areahandle.boton.AgregarArea=Agregar Proceso
configuracion.areahandle.boton.Cancelar=Cancelar
configuracion.areahandle.boton.Cancelar=Cancelar
configuracion.areahandle.boton.LimpiarForma=Limpiar Forma
configuracion.areahandle.Clave=Clave:
configuracion.areahandle.--Encargado--=--Encargado--
configuracion.areahandle.header.AREAS=PROCESOS
configuracion.areahandle.javascript.Elareahasidodadadealta=El proceso ha sido dado de alta.
configuracion.areahandle.javascript.-Encargado=- Usuario Encargado
configuracion.areahandle.javascript.LosDatosdelaareahansidoactualizados=Los datos del proceso han sido actualizados.
configuracion.areahandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.areahandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunaarea=Los siguientes datos son necesarios:
configuracion.areahandle.javascript.-Titulo=- T\\u00edtulo
configuracion.areahandle.javascript.-Ubicacion=- Departamento al que pertenece
configuracion.areahandle.Titulo=T&iacute;tulo:
configuracion.areahandle.--Ubicacion--=--Departamento--
configuracion.areahandle.Ubicacionalaquepertenece=Departamento al que pertenece:
configuracion.areahandle.UsuarioEncargado=Usuario Encargado:
configuracion.arealist.Borrar=Borrar
configuracion.arealist.BorrarArea=Borrar Proceso
configuracion.arealist.Busquedaporubicacion=B&uacute;squeda por departamento:
configuracion.arealist.Editar=Editar
configuracion.arealist.EditarArea=Editar Proceso
configuracion.arealist.Encargado=Encargado
configuracion.arealist.header.CONTROLDEAREAS=CONTROL DE PROCESOS
configuracion.arealist.javascript.Elareaseraborradadelsistema=\u00a1El proceso ser\\u00e1 borrado del sistema!
configuracion.arealist.Noseeliminolaarea=No se elimin&oacute; el proceso
configuracion.arealist.Noseencontraronregistros=No se encontraron registros.
configuracion.arealist.Sehaeliminadolaarea=Se ha eliminado el proceso
configuracion.arealist.Seleccioneunaubicacion=Seleccione un departamento
configuracion.arealist.Titulo=T&iacute;tulo
configuracion.arealist.--Todas--=-- TODOS --
configuracion.arealist.Ubicacion=Departamento
configuracion.botoneditar=Editar
configuracion.carrerahandle.Clave=Clave:
configuracion.carrerahandle.-Encargado=- Encargado
configuracion.carrerahandle.Encargado=--Encargado--
configuracion.carrerahandle.LaCarreraHaSidoDadaDeAlta=El &aacute;rea ha sido dada de alta.
configuracion.carrerahandle.LosDatoseLaCarreraHanSidoActualizados=Los datos de el &aacute;rea han sido actualizados
configuracion.carrerahandle.LosSiguientesCampoSonNecesarios= Los siguientes campos son necesarios
configuracion.carrerahandle.-Titulo=- T\\u00edtulo
configuracion.carrerahandle.Titulo=T&iacute;tulo:
configuracion.carrerahandle.-Ubicacion=- Ubicaci\\u00f3n
configuracion.carrerahandle.Ubicacion=--Departamento--
configuracion.carrerahandle.UbicacionALaQuePertenece=Departamento al que pertenece:
configuracion.carrerahandle.UNIDADES=&Aacute;REAS
configuracion.carrerahandle.UsuarioEncargado=Usuario Encargado:
configuracion.carreralist.Borrar=Borrar
configuracion.carreralist.BusquedaPorUbicacion=Busqueda por ubicaci&oacute;n:
configuracion.carreralist.CONTROLDECARRERAS=Control de {areas}
configuracion.carreralist.CONTROLDEUSUARIOS=Control de usuarios
configuracion.carreralist.Editar=Editar
configuracion.carreralist.Encargado=Encargado
configuracion.carreralist.Encargado=Encargado:
configuracion.carreralist.LaCarreraSeraBorradaDelSistema=El \\u00e1rea sera borrada del sistema
configuracion.carreralist.NoSeEliminoLaCarrera=No se elimino el &aacute;rea
configuracion.carreralist.NoSeEncontraronRegistros= No se encontraron registros
configuracion.carreralist.SeHaEliminadoLaCarrera=Se ha eliminado el &aacute;rea
configuracion.carreralist.Titulo=T&iacute;tulo
configuracion.carreralist.Titulo=T&iacute;tulo:
configuracion.carreralist.--Todas--=-- Todas --
configuracion.carreralist.Ubicacion=Ubicaci&oacute;n
configuracion.catalogohandle.Calificaciones=Calificaciones
configuracion.catalogohandle.Descripcion=Descripci&oacute;n:
configuracion.catalogohandle.Fuentes=Fuentes de acciones
configuracion.catalogohandle.javascript.-Descripcion=-Descripci\\u00f3n
configuracion.catalogohandle.javascript.Elelementohasidodadodealta=El elemento ha sido dado de alta en el cat&aacute;logo
configuracion.catalogohandle.javascript.LosDatosdelelementohansidoactualizados=Los datos del elemento han sido actualizados.
configuracion.catalogohandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunelementoenuncatalogo=Los siguientes datos son necesarios:
configuracion.catalogohandle.javascript.-Tipo=-Tipo
configuracion.catalogohandle.javascript.-Titulo=-T\\u00edtulo
configuracion.catalogohandle.javascript.-Valor=-Valor
configuracion.catalogohandle.NombreCatalogo=Nombre del Cat&aacute;logo:
configuracion.catalogohandle.Tipo=Tipo:
configuracion.catalogohandle.Titulo=T&iacute;tulo:
configuracion.catalogohandle.Valor=Valor:
configuracion.catalogolist.Borrar=Borrar
configuracion.catalogolist.BorrarCatalogo=Borrar Cat&aacute;logo
configuracion.catalogolist.Descripcion=Descripci\u00f3n
configuracion.catalogolist.Editar=Editar
configuracion.catalogolist.EditarCatalogo=Editar Cat&aacute;logo
configuracion.catalogolist.header.CONTROLDECATALOGOS=CONTROL DE CAT&Aacute;LOGOS
configuracion.catalogolist.javascript.Elelementoseraborradodelsistema=El elemento ser\\u00e1 eliminado del cat\\u00e1logo. \u00bfDesea continuar?
configuracion.catalogolist.Nosehaeliminadoelelemento=No se ha eliminado el elemento del cat&aacute;logo.
configuracion.catalogolist.Sehaeliminadoelelemento=Se ha eliminado el elemento del cat&aacute;logo.
configuracion.catalogolist.Titulo=T&iacute;tulo
configuracion.catalogolist.Valor=Valor
configuracion.catologohandle.header.CATALOGOS=CAT&Aacute;LOGOS
configuracion.configuracionhandle.AsuntoCorreo=Asunto Correo:
configuracion.configuracionhandle.Autordeldocumento=Autor del Documento:
configuracion.configuracionhandle.AutorizantesenlaSecuencia=Autorizantes en la Secuencia:
configuracion.configuracionhandle.boton.Editar=Editar
configuracion.configuracionhandle.cambiar=Cambiar
configuracion.configuracionhandle.ClaveServidorCorreo=Clave de Servidor de Correo:
configuracion.configuracionhandle.colordelsistema=Color del sistema:
configuracion.configuracionhandle.Configuraci\u00f3ndeMaildeDifusi\u00f3ndeDocumentos=Configuraci\u00f3n de Mail de Difusi\u00f3n de Documentos:
configuracion.configuracionhandle.ConversionDocumentos=Conversi&oacute;n de documentos de office a PDF
configuracion.configuracionhandle.CorreoAdmin=Correo Administrador:
configuracion.configuracionhandle.CuentaServidorCorreo=Cuenta de Servidor de Correo:
configuracion.configuracionhandle.-DBPWD=- Contrase\u00f1a Base Datos
configuracion.configuracionhandle.DBPWD=Contrase\u00f1a Base Datos:
configuracion.configuracionhandle.-DBSERVER=- URL Base Datos
configuracion.configuracionhandle.DBSERVER=URL Base Datos:
configuracion.configuracionhandle.-DBUSER=- Usuario Base Datos
configuracion.configuracionhandle.DBUSER=Usuario Base Datos:
configuracion.configuracionhandle.dias=d\u00eda(s)
configuracion.configuracionhandle.Editar=Editar
configuracion.configuracionhandle.Encargadodelmodulo=Encargado del m\u00f3dulo:
configuracion.configuracionhandle.-EnvioCorreos-=-- Env\\u00edo de Correos--
configuracion.configuracionhandle.EnvioCorreos=Env&iacute;o de Correos:
configuracion.configuracionhandle.Idioma=Idioma:
configuracion.configuracionhandle.javascript.-AsuntoCorreo=- Asunto Correo
configuracion.configuracionhandle.javascript.-ClaveServidorCorreo=- Clave Servidor de Correo
configuracion.configuracionhandle.javascript.-CorreoAdmin=- Correo Administrador
configuracion.configuracionhandle.javascript.-CuentaServidorCorreo=- Cuenta Servidor de Correo
configuracion.configuracionhandle.javascript.-EnvioCorreos=- Env\\u00edo de Correos
configuracion.configuracionhandle.javascript.-Idioma=- Idioma
configuracion.configuracionhandle.javascript.-IdSistema=- ID Sistema
configuracion.configuracionhandle.javascript.LaConfiguracionHaSidoActualizada=La operaci\\u00f3n ha sido realizada. Para que los cambios tengan efecto ahora inicie sesi\\u00f3n nuevamente. \u00bfDesea hacerlo?
configuracion.configuracionhandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.configuracionhandle.javascript.-ServidorCorreo=- Servidor Correo
configuracion.configuracionhandle.javascript.-VigenciaDocumentos=- Vigencia Documentos
configuracion.configuracionhandle.LlaveEncriptacionRC4=Llave Encriptacion RC4:
configuracion.configuracionhandle.MensajedeBienvenida=Mensaje de Bienvenida:
configuracion.configuracionhandle.meses=\ \ \ meses
configuracion.configuracionhandle.No=No
configuracion.configuracionhandle.NumeroRegistrosBusqueda=N&uacute;mero Registros en B&uacute;squedas
configuracion.configuracionhandle.-NumeroRegistrosBusquedas=- N\\u00famero Registros en B\\u00fasquedas
configuracion.configuracionhandle.PDFViewer=Usar visor de documentos PDF
configuracion.configuracionhandle.Permisodeprocesosenlacarpeta=Permiso de procesos en la carpeta:
configuracion.configuracionhandle.Permisodeusuarionombradoenlacarpeta=Permiso de usuario nombrado en la carpeta:
configuracion.configuracionhandle.ServidorCorreo=Servidor de Correo:
configuracion.configuracionhandle.Si=S&iacute;
configuracion.configuracionhandle.-SiteURL=- URL Sitio
configuracion.configuracionhandle.SiteURL=URL Sitio:
configuracion.configuracionhandle.-StringSystemID=- ID Sistema
configuracion.configuracionhandle.StringSystemID=ID Sistema:
configuracion.configuracionhandle.-UploadFolderDocumentos=- UPLOAD_FOLDER_DOCUMENTOS
configuracion.configuracionhandle.UploadFolderDocumentos=UPLOAD_FOLDER_DOCUMENTOS:
configuracion.configuracionhandle.-UploadFolderSolicitudes=- UPLOAD_FOLDER_SOLICITUDES
configuracion.configuracionhandle.UploadFolderSolicitudes=UPLOAD_FOLDER_SOLICITUDES:
configuracion.configuracionhandle.VidadeLigadeCorreos=Vida de Liga de Correos:
configuracion.configuracionhandle.VigenciaDocumentos=Vigencia de Documentos:
configuracion.configuracionreportegeneral.chart.title.AreasPorUbicacion=Procesos por departamento
configuracion.configuracionreportegeneral.chart.title.UsuariosPorUbicacion=Usuarios por departamento
configuracion.configuracionreportegeneral.chart.title=N\u00famero de Procesos por Departamento
configuracion.configuracionreportegeneral.header=REPORTE GENERAL DE CONFIGURACI&Oacute;N
configuracion.configuracionreportegeneral.numerodeareas=N\u00famero de procesos
configuracion.configuracionreportegeneral.NumeroDeUsuarios=N\u00famero de usuarios
configuracion.editpassword.ClavedeAccesoActual=Clave de Acceso Actual:
configuracion.editpassword.ClavedeAccesoNueva=Clave de Acceso Nueva:
configuracion.editpassword.ConfirmeClavedeAccesoNueva=Confirme Clave de Acceso Nueva:
configuracion.editpassword.header.EDITARCLAVEACCES0=EDITAR CLAVE DE ACCESO
configuracion.editpassword.javascript.-ClaveAccesoActual=-Clave de Acceso Actual
configuracion.editpassword.javascript.-ClaveAccesoNueva=-Clave Acceso Nueva
configuracion.editpassword.javascript.-ConfirmacionClaveAccesoNueva=-Confirmaci&oacute;n Clave Acceso Nueva
configuracion.editpassword.javascript.-CuentadeUsuario=-Cuenta de Usuario
configuracion.editpassword.javascript.LaClaveDeAccesoNoEsCorrecta=La Clave de Acceso Actual no es correcta
configuracion.editpassword.javascript.LasClavesDeAccesoNuevasNoCoinciden=Las claves de acceso nuevas no coinciden. Favor de intentar nuevamente
configuracion.edituser.header.EDITARCUENTA=EDITAR CUENTA
configuracion.edituser.javascript.-Cuentadecorreo=- Correo Electr\\u00f3nico
configuracion.edituser.javascript.-Cuentadecorreovalida=- Correo electr\\u00f3nico v\\u00e1lido
configuracion.edituser.javascript.-CuentadeUsuario=- Cuenta de Usuario
configuracion.edituser.javascript.LaClaveDeAccesohasidoactualizada=La Clave de Acceso ha sido actualizada.
configuracion.edituser.javascript.Laclavedelusuariohasidoactualizada=La operaci\\u00f3n ha sido realizada. Para que los cambios tengan efecto ahora inicie sesi\\u00f3n nuevamente. \u00bfDesea hacerlo?
configuracion.edituser.javascript.LosDatosdelusuariohansidoactualizados=La operaci&oacute;n ha sido realizada con &eacute;xito.
configuracion.edituser.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.edituser.javascript.-NombreCompleto=- Nombre Completo
configuracion.facultadhandle.BorrarFacultad =La unidad productiva ser\\u00e1 borrada del sistema
configuracion.facultadhandle.Clave=Clave:
configuracion.facultadhandle.CONTROLFACULTADES =CONTROL DE UNIDADES DE NEGOCIO
configuracion.facultadhandle.DatosFacultadActualizados =Los Datos de la unidad productiva han sido actualizados
configuracion.facultadhandle.DatosFacultadAlta =La unidad productiva ha sido dada de alta.
configuracion.facultadhandle.DatosFacultadNecesarios=Los siguientes datos son necesarios:
configuracion.facultadhandle.-Encargado=- Encargado
configuracion.facultadhandle.Encargado=--Encargado--
configuracion.facultadhandle.FacultadBorrar =Borrar
configuracion.facultadhandle.FacultadEditar =Editar
configuracion.facultadhandle.FacultadEliminada =Se ha eliminado la unidad productiva
configuracion.facultadhandle.FacultadEncargado =Encargado
configuracion.facultadhandle.FACULTADES =UNIDADES DE NEGOCIO
configuracion.facultadhandle.FacultadID =ID
configuracion.facultadhandle.FacultadNoEliminada =No se ha eliminado la unidad productiva, Esto se puede deber a que tenga areas declaradas
configuracion.facultadhandle.FacultadTitulo =T&iacute;tulo
configuracion.facultadhandle.-Titulo=- T\\u00edtulo
configuracion.facultadhandle.Titulo=T&iacute;tulo:
configuracion.facultadhandle.UsuarioEncargado=Usuario Encargado:
configuracion.INACTIVO,presioneparamarcarcomoACTIVO=INACTIVO, presione para marcar com ACTIVO
configuracion.invitadohandle.Correo=Correo electr&oacute;nico:
configuracion.invitadohandle.header.INVITADOS=INVITADOS
configuracion.invitadohandle.javascript.Agregar=Agregar
configuracion.invitadohandle.javascript.-Correo=- Correo electr\\u00f3nico
configuracion.invitadohandle.javascript.-Cuentadecorreovalida=- Cuenta de correo v\\u00e1lida
configuracion.invitadohandle.javascript.-Descripcion=- Descripci\\u00f3n
configuracion.invitadohandle.javascript.Elinvitadohasidodadodealta=El invitado ha sido dado de alta.
configuracion.invitadohandle.javascript.Listar=Listar
configuracion.invitadohandle.javascript.LosDatosdelinvitadohansidoactualizados=Los datos del invitado han sido actualizados
configuracion.invitadohandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.invitadohandle.javascript.Lossiguientesdatossonnecesariosparadardealtaauninvitado=Los siguientes datos son necesarios:
configuracion.invitadohandle.javascript.-Nombre=- Nombre
configuracion.invitadohandle.javascript.-Titulo=- T\\u00edtulo
configuracion.invitadohandle.Nombre=Nombre:
configuracion.invitadolist.ACTIVO=Activo
configuracion.invitadolist.Borrar=Borrar
configuracion.invitadolist.BorrarInvitado=Borrar Invitado
configuracion.invitadolist.Correo=Correo electr&oacute;nico:
configuracion.invitadolist.Correo2=Correo electr\u00f3nico
configuracion.invitadolist.Editar=Editar
configuracion.invitadolist.EditarInvitado=Editar Invitado
configuracion.invitadolist.Estado= Estado
configuracion.invitadolist.header.CONTROLDEINVITADOS=CONTROL DE INVITADOS
configuracion.invitadolist.javascript.Elinvitadoseraborradodelsistema=\u00a1El invitado ser\\u00e1 borrado del sistema!
configuracion.invitadolist.Nombre=Nombre
configuracion.invitadolist.Nombre2=Nombre
configuracion.invitadolist.Nosehaeliminadoelinvitado=No se ha eliminado el invitado
configuracion.invitadolist.Sehaeliminadoelinvitado=Se ha eliminado el invitado
configuracion.LISTADEPERFILES=CONTROL DE PERFILES
configuracion.modneghandle.Descripcion=Descripci\u00f3n
configuracion.modneghandle.header.ALTAMODELODENEGOCIOS=ALTA DE MODELO DE NEGOCIOS
configuracion.modneghandle.javascript.-Descripcion=-Descripci\\u00f3n
configuracion.modneghandle.javascript.ElModeloDeNegociosHaSidoDadoDeAlta=El Modelo de Negocios ha sido dado de Alta
configuracion.modneghandle.javascript.LosDatosDelModeloDeNegociosoHanSidoActualizado=Los datos del Modelo de Negocios han sido Actualizados
configuracion.modneghandle.javascript.LosDatosDelModeloDeNegociosoHanSidoActualizados=Los datos del Modelo de Negocios han sido Actualizados
configuracion.modneghandle.javascript.LosSiguientesDatosSonNecesarios=Los siguientes datos son necesarios\:
configuracion.modneghandle.javascript.-Titulo=-T\\u00edtulo
configuracion.modneghandle.Titulo=T&iacute;tulo
configuracion.modneglist.Borrar=Borrar
configuracion.modneglist.Editar=Editar
configuracion.modneglist.header.CONTROLDEMODELODENEGIOCIOS=CONTROL DE MODELO DE NEGOCIOS
configuracion.modneglist.javascript.ElModeloDeNegociosSeraBorradoDelSistema=El Modelo de Negocios ser\\u00e1 borrado del sistema
configuracion.modneglist.NoSeEliminoElModeloDeNegocios=No se elimin&oacute; el Modelo de Negocios
configuracion.modneglist.NoSeEncontraronRegistros=No se encontraron registros
configuracion.modneglist.SeHaEliminadoElModeloDeNegocios=Se ha eliminado el Modelo de Negocios
configuracion.modneglist.Titulo=T&iacute;tulo
configuracion.objetohandle.Descripcion=Descripci\u00f3n
configuracion.objetohandle.header.ALTAOBJETOS=ALTA DE OBJETOS
configuracion.objetohandle.javascript.-Descripcion=-Descripci\\u00f3n
configuracion.objetohandle.javascript.Elobjetohasidodadadealta=El Objeto ha sido dado de Alta
configuracion.objetohandle.javascript.LosDatosdelobjetohansidoactualizados=Los Datos del Objeto han sido Actualizados
configuracion.objetohandle.javascript.LosSiguientesDatosSonNecesarios=Los siguientes datos son necesarios\:
configuracion.objetohandle.javascript.-TipoDeObjeto=-Tipo
configuracion.objetohandle.javascript.-Titulo=-T\\u00edtulo
configuracion.objetohandle.Tipo=Tipo
configuracion.objetohandle.Titulo=T&iacute;tulo
configuracion.objetolist.Borrar=Borrar
configuracion.objetolist.BorrarObjeto=Borrar Objeto
configuracion.objetolist.Editar=Editar
configuracion.objetolist.header.CONTROLDEOBJETOS=CONTROL DE OBJETOS
configuracion.objetolist.javascript.ElObjetoSeraBorradoDelSistema=El Objeto ser\\u00e1 borrado del sistema
configuracion.objetolist.NoSeEliminoElObjeto=No se elimino el Objeto
configuracion.objetolist.NoSeEncontraronRegistros=No se encontraron registros
configuracion.objetolist.SeHaEliminadoElObjeto=Se ha eliminado el Objeto
configuracion.objetolist.Tipo=Tipo
configuracion.objetolist.Titulo=T&iacute;tulo
configuracion.perfileshandle.Alta=Alta
configuracion.perfileshandle.Control=Control
configuracion.perfileshandle.ElPerfilASidoActualizadoCorrectamente=El perfil a sido actualizado correctamente
configuracion.perfileshandle.ElPerfilASidoEliminadoCorrectamente=El perfil a sido eliminado correctamente
configuracion.perfileshandle.ElPerfilASidoGuardadoCorrectamente=El perfil a sido guardado correctamente
configuracion.perfileshandle.ListaDePerfiles=Lista de Perfiles
configuracion.perfileshandle.LosSiguientesCamposSonNecesarios=Los siguientes campos son necesarios:
configuracion.perfileshandle.PERFILES=PERFILES
configuracion.perfileshandle.PerfilesDeUsuario=Perfiles de Usuario
configuracion.perfileshandle.PermiteRegistroDeUsuarios=Permite registro de usuarios:
configuracion.perfileshandle.TieneQueSeleccionarAlMenosUnPrivilegio=- Tiene que seleccionar al menos un privilegio
configuracion.perfileshandle.Titulo=- T\\u00edtulo
configuracion.perfileshandle.Titulo1= T&iacute;tulo
configuracion.perfileslist.Activo,PresioneParaMarcarComoInactivo= Activo, presione para marcar como inactivo.
configuracion.perfileslist.Busqueda= B&uacute;squeda
configuracion.perfileslist.Estado=Estado:
configuracion.perfileslist.Inactivo,PresioneParaMarcarComoActivo=Inactivo, presione para marcar como activo.
configuracion.perfileslist.INACTIVO,presioneparamarcarcomoACTIVO=INACTIVO, presione para marcar como ACTIVO.
configuracion.perfileslist.LISTADEPERFILES=LISTA DE PERFILES
configuracion.perfileslist.Nombre=Nombre
configuracion.perfileslist.NoSePudoCambiarElEstadoDelPerfil= No se pudo cambiar el estado del perfil.
configuracion.perfileslist.SeCambioElEstadoDelPerfil= Se cambio el estado del perfil.
configuracion.perfileslist.Titulo=T&iacute;tulo
configuracion.perfileslist.---TODOS---= --- TODOS ---
configuracion.pieformalist.Forma=Forma
configuracion.pieformalist.FORMAS=FORMAS
configuracion.pieformalist.PiedeformaDerecho=Pie de forma Derecho
configuracion.pieformalist.PiedeformaIzquierdo=Pie de forma Izquierdo
configuracion.plantillahandle.header.PLANTILLAS=PLANTILLAS
configuracion.plantillahandle.Laplantillahasidocreada=La Plantilla ha sido creada.
configuracion.plantillahandle.Losdatosdelaplantillahansidoactualizados=Datos de la Plantilla han sido actualizados.
configuracion.plantillahandle.NumerodePlantilla=N\u00famero de Plantilla
configuracion.plantillahandle.PlantillaenEdicion=N\u00famero de plantilla nueva
configuracion.plantillahandle.tituloplntilla=T&iacute;tulo de plantilla
configuracion.plantillashandle.Agregar/Quitar= Agregar/Quitar
configuracion.plantillashandle.-CampodeDias= - Campos de d\\u00eda(s)
configuracion.plantillashandle.-DebeIncluirAlMenosUnUsuarioEnLaPlantilladeSecuencia= - Debe incluir al menos un usuario en la plantilla de secuencia.
configuracion.plantillashandle.DebeLlenarLosSiguientesCampos= Debe llenar los siguientes campos:
configuracion.plantillashandle.Dias= Dia(s)
configuracion.plantillashandle.DiasParaAtenderSecuencia= Dias para atender secuencia
configuracion.plantillashandle.NoSeHanAgregadoUsuariosAlaPlantillaDeSecuencia= No se han agregado usuarios a la plantilla de secuencia
configuracion.plantillashandle.Orden= Orden
configuracion.plantillashandle.-Titulo= - T\\u00edtulo.
configuracion.plantillashandle.Usuario= Usuario
configuracion.questionhandle.Estado\:=Estado:
configuracion.questionhandle.header.PREGUNTAS=PREGUNTAS
configuracion.questionhandle.javascript.Lapreguntahasidodadadealta=La pregunta ha sido dada de alta.
configuracion.questionhandle.javascript.LosDatosdelapreguntahansidoactualizados=Los datos de la pregunta han sido actualizados.
configuracion.questionhandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.questionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunapregunta=Los siguientes datos son necesarios para dar de alta una pregunta.
configuracion.questionhandle.javascript.-Redacciondelapregunta=- Redacci\\u00f3n de la pregunta
configuracion.questionhandle.javascript.-Seccionalaquepertenece=- Secci\\u00f3n a la que pertenece
configuracion.questionhandle.javascript.-Secciondelapregunta=- Secci\\u00f3n de la pregunta
configuracion.questionhandle.Redacciondelapregunta=Redacci&oacute;n de la pregunta:
configuracion.questionhandle.--Seccion--=--Secci&oacute;n--
configuracion.questionhandle.Seccionalaquepertenece=Secci&oacute;n a la que pertenece:
configuracion.questionlist.ACTIVA=El estado actual es ACTIVA. Presione para marcar como CANCELADA.
configuracion.questionlist.Borrar=Borrar
configuracion.questionlist.Busquedaporestado=B&uacute;squeda por estado:
configuracion.questionlist.Busquedaporseccion=B&uacute;squeda por Secci&oacute;n:
configuracion.questionlist.CANCELADA=El estado actual es CANCELADA. Presione para marcar como ACTIVA.
configuracion.questionlist.Editar=Editar
configuracion.questionlist.EditarPregunta=Editar Pregunta
configuracion.questionlist.Estado=Estado
configuracion.questionlist.header.CONTROLDEPREGUNTAS=CONTROL DE PREGUNTAS
configuracion.questionlist.javascript.EstaSegurodeQuererBorrarLaPregunta=\u00bfEst\\u00e1 seguro de querer borrar la pregunta?
configuracion.questionlist.javascript.Estasegurodequerercambiarelestadodelapregunta=\u00bfEst\\u00e1 seguro de querer cambiar el estado de la pregunta?
configuracion.questionlist.Noseencontraronregistros=No se encontraron registros.
configuracion.questionlist.Nosehaeliminadolapregunta=No se ha eliminado la pregunta
configuracion.questionlist.Redaccion=Redacci&oacute;n
configuracion.questionlist.Seccion=Secci&oacute;n
configuracion.questionlist.Seccion2=Secci&oacute;n
configuracion.questionlist.Sehaeliminadolapregunta=Se ha eliminado la pregunta
configuracion.questionlist.Todas=-- TODAS --
configuracion.registrolist.FechaDeEntrada=Fecha de Entrada
configuracion.registrolist.FechaDeSalida=Fecha de Salida
configuracion.registrolist.FechasEntre=Fechas entre
configuracion.registrolist.header.REGISTRODEACCESODEUSUARIOS=REGISTRO DE ACCESO DE USUARIOS
configuracion.registrolist.NoRegistrada=No Registrada
configuracion.registrolist.NoSeEncuentranRegistrosPorMostrar=No se encuentran registros por mostrar
configuracion.registrolist.Usuario=Usuario
configuracion.registrolist.Y=y
configuracion.reositoriolist.Borrar=Borrar
configuracion.repositoriohandle.boton.Actualizar=Actualizar
configuracion.repositoriohandle.boton.Cancelar=Cancelar
configuracion.repositoriohandle.boton.Regresar=Regresar
configuracion.repositoriohandle.Clave=Clave:
configuracion.repositoriohandle.EsTopLevel=Es de Nivel Superior: 
configuracion.repositoriohandle.javascript.-Descripcion=- Descripci\\u00f3n
configuracion.repositoriohandle.javascript.-Descripcion=-Descripci\\u00f3n
configuracion.repositoriohandle.javascript.Elrepositoriohasidodadodealta=El repositorio ha sido dado de alta.
configuracion.repositoriohandle.javascript.LimiteRepositorios=No se puede realizar la acci&oacute;n. El n&uacute;mero m&aacute;ximo de repositorios es 10.
configuracion.repositoriohandle.javascript.LosDatosdelrepositoriohansidoactualizados=Los datos del repositorio han sido actualizados.
configuracion.repositoriohandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.repositoriohandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunrepositorio=Los siguientes datos son necesarios::
configuracion.repositoriohandle.javascript.-Titulo=- T\\u00edtulo
configuracion.repositoriohandle.javascript.-Titulo=-T\\u00edtulo
configuracion.repositoriohandle.Raiz=Documentos
configuracion.repositoriohandle.RepositorioPadre=Repositorio Padre: 
configuracion.repositoriolist.Borrar=Borrar
configuracion.repositoriolist.BorrarRepositorio=Borrar Repositorio
configuracion.repositoriolist.CarpetasRelacionadas=El repositorio no ha sido eliminado. El repositorio tiene carpetas.
configuracion.repositoriolist.DocumentosRelacionados=El repositorio no ha sido eliminado. El repositorio tiene documentos.
configuracion.repositoriolist.Editar=Editar
configuracion.repositoriolist.EditarRepositorio=Editar Repositorio
configuracion.repositoriolist.header.CONTROLDEREPOSITORIOS=CONTROL DE REPOSITORIOS
configuracion.repositoriolist.javascript.confirmacionBorradoRepositorio=El repositorio ser\\u00e1 eliminado del sistema. \u00bfDesea continuar?
configuracion.repositoriolist.Nosehaeliminadoelrepositorio=El repositorio no ha sido eliminado.
configuracion.repositoriolist.Sehaeliminadoelrepositorio=El repositorio ha sido eliminado.
configuracion.repositoriolist.Titulo=T&iacute;tulo
configuracion.repositorioolist.Nosehaeliminadoelrepositorio.=No se ha eliminado el respositorio.
configuracion.respaldoshandle.Clave=Clave
configuracion.respaldoshandle.Descargadebasededatos=Descarga de base de datos:
configuracion.respaldoshandle.Descargadedocumentos=Descarga de documentos:
configuracion.respaldoshandle.Fechadelrespaldo=Fecha del respaldo:
configuracion.respaldoshandle.Nohayrespaldos=No hay respaldos.
configuracion.respaldoshandle.Presioneelbotonparagenerarunrespaldo=Presione el boton para generar un respaldo
configuracion.respaldoshandle.Respaldar=Respaldar
configuracion.respaldoshandle.Respaldo=Respaldo
configuracion.respaldoshandle.RESPALDOS=RESPALDOS
configuracion.respaldoshandle.Titulo=T&iacute;tulo:
configuracion.rubro.Noseencontraronregistros=No se encontraron registros.
configuracion.rubrohandle.Descripcion=Descripci&oacute;n:
configuracion.rubrohandle.header.RUBROS=RUBROS
configuracion.rubrohandle.javascript.ActualizacionRubro=Los datos del rubro han sido actualizados.
configuracion.rubrohandle.javascript.-Descripcion=- Descripci\\u00f3n
configuracion.rubrohandle.javascript.Elrubrohasidodadadealta=El rubro ha sido dado de alta.
configuracion.rubrohandle.javascript.-Nombre=- Nombre
configuracion.rubrohandle.Nombre=Nombre\: 
configuracion.rubrolist.Borrar=Borrar
configuracion.rubrolist.BorrarRubro=Borrar Rubro
configuracion.rubrolist.Descripcion=Descripci\u00f3n
configuracion.rubrolist.Editar=Editar
configuracion.rubrolist.header.CONTROLDERUBROS=CONTROL DE RUBROS
configuracion.rubrolist.javascript.Elrubroseraborrododelsistema=\u00a1El rubro sera borrado del sistema\!
configuracion.rubrolist.Nombre=Nombre
configuracion.rubrolist.Noseeliminoelrubro=No se ha eliminaddo el rubro
configuracion.rubrolist.Sehaeliminadoelrubro=Se ha eliminado el rubro
configuracion.seccionhandle.Clave=Clave:
configuracion.seccionhandle.Consejosocomentarios=Puntos de la Norma:
configuracion.seccionhandle.Documentosdereferencia=Documentos de referencia
configuracion.seccionhandle.header.SECCIONES=SECCIONES
configuracion.seccionhandle.javascript.Laseccionhasidodadadealta=La Secci&oacute;n ha sido dada de alta.
configuracion.seccionhandle.javascript.LosDatosdelaseccionhansidoactualizados=Los datos de la secci&oacute;n han sido actualizados.
configuracion.seccionhandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.seccionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunaseccion=Los siguientes datos son necesarios:
configuracion.seccionhandle.javascript.-Numero=- N\\u00famero
configuracion.seccionhandle.javascript.-Titulo=- T\\u00edtulo
configuracion.seccionhandle.nombre=Nombre:
configuracion.seccionhandle.Numero=N&uacute;mero:
configuracion.seccionhandle.Titulo=T&iacute;tulo:
configuracion.seccionlist.Borrar=Borrar
configuracion.seccionlist.BorrarSeccion=Borrar Secci&oacute;n
configuracion.seccionlist.Editar=Editar
configuracion.seccionlist.EditarSeccion=Editar Secci&oacute;n
configuracion.seccionlist.header.CONTROLDESECCIONES=CONTROL DE SECCIONES
configuracion.seccionlist.javascript.Laseccionseraborradadelsistema=\u00a1La secci\u00f3n ser\u00e1 borrada del sistema!
configuracion.seccionlist.NosehaeliminadolaseccionEstosepuededeberaqueauntengatengapreguntasdeclaradas=No se ha eliminado la Secci&oacute;n. Esto se puede deber a que a&uacute;n tenga preguntas declaradas o pertenesca a alg&uacute;n cuestionario.
configuracion.seccionlist.Numero=N&uacute;mero
configuracion.seccionlist.Sehaeliminadolaseccion=Se ha eliminado la secci&oacute;n
configuracion.seccionlist.Titulo=T&iacute;tulo
configuracion.tipohandle.boton.Actualizar=Actualizar
configuracion.tipohandle.boton.Cancelar=Cancelar
configuracion.tipohandle.boton.CrearTipo=Crear Tipo
configuracion.tipohandle.boton.LimpiarForma=Limpiar Forma
configuracion.tipohandle.Descripcion=Descripci&oacute;n:
configuracion.tipohandle.header.ALTATIPO=ALTA DE TIPO
configuracion.tipohandle.header.TIPOS=TIPOS
configuracion.tipohandle.javascript.Crear=Crear
configuracion.tipohandle.javascript.-Descripcion=-Descripci\\u00f3n
configuracion.tipohandle.javascript.ElTipoHaSidoDadoDeAlta=El Tipo ha sido dado de Alta
configuracion.tipohandle.javascript.Eltiposeleccionadohasidoactualizado=El tipo seleccionado ha sido actualizado.
configuracion.tipohandle.javascript.Eltiposeleccionadohasidocreado=El tipo seleccionado ha sido creado.
configuracion.tipohandle.javascript.Listar=Listar
configuracion.tipohandle.javascript.LosDatosDelTipoHanSidoActualizados=Los datos del Tipo han sido Actualizados
configuracion.tipohandle.javascript.LosSiguientesDatosSonNecesarios=Los siguientes datos son necesarios\:
configuracion.tipohandle.javascript.Proporcioneelnombredeltipoquedeseacrear=Proporcione el nombre del tipo que desea crear.
configuracion.tipohandle.javascript.-Titulo=-T\\u00edtulo
configuracion.tipohandle.Titulo=T&iacute;tulo:
configuracion.tipolist.Borrar=Borrar
configuracion.tipolist.boton.Buscar=Buscar
configuracion.tipolist.Editar=Editar
configuracion.tipolist.header.CONTROLDETIPOS=CONTROL DE TIPOS
configuracion.tipolist.javascript.Eltiposeleccionadoseraremovidodelsistema=El tipo seleccionado ser&aacute; removido del sistema.
configuracion.tipolist.javascript.ElTipoSeraBorradoDelSistema=El Tipo ser\\u00e1 borrado del sistema
configuracion.tipolist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=La selecci\\u00f3n no puede ser eliminada, debido a que \\nesta ligado a otras partes dentro del sistema.\n\nElimine esas ligas y vuelva a intentarlo.
configuracion.tipolist.Laseleccionhasidoeliminada=La selecci&oacute;n ha sido eliminada.
configuracion.tipolist.NoSeEliminoElTipo=No se elimino el Tipo
configuracion.tipolist.NoSeEncontraronRegistros=No se encontraron registros
configuracion.tipolist.Noseencontraronregistros=No se encontraron registros.
configuracion.tipolist.SeHaEliminadoElTipo=Se ha eliminado el Tipo
configuracion.tipolist.Titulo\:=T&iacute;tulo:
configuracion.tipolist.Titulo=T&iacute;tulo
configuracion.tipoobjetohandle.Descripcion=Descripci\u00f3n
configuracion.tipoobjetohandle.header.ALTATIPOSDEOBJETOS=ALTA DE TIPO DE OBJETOS
configuracion.tipoobjetohandle.javascript.-Descripcion=-Descripci\\u00f3n
configuracion.tipoobjetohandle.javascript.ElTipoDeObjetoHaSidoDadoDeaAta=El Tipo de Objeto ha sido dado de Alta
configuracion.tipoobjetohandle.javascript.LosDatosDelTipoDeObjetoHanSidoActualizados=Los Datos del Tipo de Objeto han sido Actualizados
configuracion.tipoobjetohandle.javascript.LosSiguientesDatosSonNecesarios=Los siguientes datos son necesarios\:
configuracion.tipoobjetohandle.javascript.-Titulo=-T\\u00edtulo
configuracion.tipoobjetohandle.Titulo=T&iacute;tulo
configuracion.tipoobjetolist.Borrar=Borrar
configuracion.tipoobjetolist.Editar=Editar
configuracion.tipoobjetolist.header.CONTROLDETIPOSDEOBJETOS=CONTROL DE TIPOS DE OBJETOS
configuracion.tipoobjetolist.javascript.ElTipoDeObjetoSeraBorradoDelSistema=El Tipo de Objeto ser\\u00e1 Borrado del sistema
configuracion.tipoobjetolist.NoSeEliminoElTipoDeObjeto=No se elimino el Tipo de Objeto
configuracion.tipoobjetolist.SeHaEliminadoElTipoDeObjeto=Se ha eliminado el Tipo de Objeto
configuracion.tipoobjetolist.Titulo=T&iacute;tulo
configuracion.ubicacionhandle.AdmiteQuejas= Admite quejas
configuracion.ubicacionhandle.Clave=Clave:
configuracion.ubicacionhandle.--Encargado--=--Encargado--
configuracion.ubicacionhandle.--Facultad--= --Unidad productiva--
configuracion.ubicacionhandle.Facultad=Unidad productiva:
configuracion.ubicacionhandle.header.UBICACIONES=DEPARTAMENTOS
configuracion.ubicacionhandle.javascript.-Encargado=- Usuario Encargado
configuracion.ubicacionhandle.javascript.-Facultad=- Unidad productiva
configuracion.ubicacionhandle.javascript.LaUbicacionhasidodadadealta=El departamento ha sido dado de alta.
configuracion.ubicacionhandle.javascript.LosDatosdelaUbicacionhansidoactualizados=Los datos del Departamento han sido actualizados.
configuracion.ubicacionhandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.ubicacionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunaubicacion=Los siguientes datos son necesarios:
configuracion.ubicacionhandle.javascript.-Titulo=- T\\u00edtulo
configuracion.ubicacionhandle.Titulo=T&iacute;tulo:
configuracion.ubicacionhandle.UsuarioEncargado=Usuario Encargado:
configuracion.ubicacionlist.Borrar=Borrar
configuracion.ubicacionlist.BorrarUbicacion=Borrar Departamento
configuracion.ubicacionlist.Editar=Editar
configuracion.ubicacionlist.EditarUbicacion=Editar Departamento
configuracion.ubicacionlist.Encargado=Encargado
configuracion.ubicacionlist.Facultad=Unidad productiva
configuracion.ubicacionlist.header.CONTROLDEUBICACIONES=CONTROL DE DEPARTAMENTOS
configuracion.ubicacionlist.javascript.Laubicacionseraborradadelsistema=\u00a1El departamento ser\\u00e1 borrado del sistema!
configuracion.ubicacionlist.NosehaeliminadolaubicacionEstosepuededeberaquetengaareasdeclaradas=No se ha eliminado el departamento. Esto se puede deber a que tenga procesos declarados.
configuracion.ubicacionlist.Sehaeliminadolaubicacion=Se ha eliminado el departamento
configuracion.ubicacionlist.Titulo=T&iacute;tulo
configuracion.unidadorganizacionalhandle.boton.CrearUnidad=Crear &Aacute;rea
configuracion.unidadorganizacionalhandle.javascript.Crear=Crear
configuracion.unidadorganizacionalhandle.javascript.Launidadorganizacionalhasidocreada=La unidad de negocio ha sido creada.
configuracion.unidadorganizacionalhandle.javascript.Listar=Listar
configuracion.unidadorganizacionalhandle.javascript.LosDatosdelaunidadorganizacionalhansidoactualizados=Los datos de la unidad de negocio han sido actualizados.
configuracion.unidadorganizacionalhandle.javascript.Proporcioneelnombredelaunidadorganizacional=Proporcione el nombre de la unidad de negocio
configuracion.unidadorganizacionalhandle.Titulo=T&iacute;tulo:
configuracion.unidadorganizacionalhandle.UNIDADORGANIZACIONAL=Unidad de negocio
configuracion.unidadorganizacionallist.Borrar=Borrar
configuracion.unidadorganizacionallist.Editar=Editar
configuracion.unidadorganizacionallist.header.CONTROLDEUNIDADORGANIZACIONAL=Control de unidad de negocio
configuracion.unidadorganizacionallist.javascript.Laseleccionnopuedesereliminadadebidoaque\\nestaligadoaotraspartesdentrodelsistema\n\nElimineesasligasyvuelvaaintentarlo=La selecci\\u00f3n no puede ser eliminada, debido a que \\nesta ligado a otras partes dentro del sistema.\n\nElimine esas ligas y vuelva a intentarlo.
configuracion.unidadorganizacionallist.javascript.Launidadorganizacionalseraremovidadelsistema=La unidad de negocio ser&aacute; removida del sistema.
configuracion.unidadorganizacionallist.Laseleccionhasidoeliminada=La selecci&oacute;n ha sido eliminada.
configuracion.unidadorganizacionallist.Noseencontraronregistros=No se encontraron registros.
configuracion.unidadorganizacionallist.Titulo\:=T&iacute;tulo:
configuracion.unidadorganizacionallist.Titulo=T&iacute;tulo
configuracion.userhandle.Areaenlaquesedesempena=Proceso en el que se desempe&ntilde;a:
configuracion.userhandle.AsignarClaveManualmente= Asignar clave manualmente:
configuracion.userhandle.Clave=Clave:
configuracion.userhandle.ClavedeAcceso=Clave de Acceso:
configuracion.userhandle.ConfirmeClavedeAcceso=Confirme Clave de Acceso:
configuracion.userhandle.Correoelectronico=Correo electr&oacute;nico:
configuracion.userhandle.CuentadeUsuario=Cuenta de Usuario:
configuracion.userhandle.gridSize=Registros por p\u00e1gina:
configuracion.userhandle.defaultGridSize=Registros en los detalles:
configuracion.userhandle.floatingGridSize=Registros en las ventanas flotantes:
configuracion.userhandle.Descripcion= Descripci&oacute;n:
configuracion.userhandle.GuardarComoPerfil= Guardar como perfil:
configuracion.userhandle.header.CONFIGURACIONSISTEMA=CONFIGURACI&Oacute;N DEL SISTEMA
configuracion.userhandle.header.USUARIOS=USUARIOS
configuracion.userhandle.javascript.Agregar=Agregar
configuracion.userhandle.javascript.-Agreguealmenosunareaenlaquesedesempena=-Agregue almenos un area en la que se desempe\u00f1a
configuracion.userhandle.javascript.-Areaalaquepertenece=- Proceso en el que se desempe\u00f1a
configuracion.userhandle.javascript.-ClavedeAcceso=- Clave de Acceso
configuracion.userhandle.javascript.-Cuentadecorreo=- Correo electr\\u00f3nico
configuracion.userhandle.javascript.-Cuentadecorreovalida=- Cuenta de correo v\\u00e1lida
configuracion.userhandle.javascript.-CuentadeUsuario=- Cuenta de Usuario
configuracion.userhandle.javascript.Elpasswordnocoincideporfavorintentedenuevo=La clave no coincide, por favor intente de nuevo
configuracion.userhandle.javascript.Elusuariohasidodadodealta=El usuario ha sido dado de alta.
configuracion.userhandle.javascript.Listar=Listar
configuracion.userhandle.javascript.LosDatosdelusuariohansidoactualizados=Los datos del usuario han sido actualizados.
configuracion.userhandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
configuracion.userhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunusuario=Los siguientes datos son necesarios:
configuracion.userhandle.javascript.-NombreCompleto=- Nombre Completo
configuracion.userhandle.LaClaveTecleadaYaExiste= La clave tecleada ya existe
configuracion.userhandle.ModificarLaClaveDeAcceso= Modificar clave de acceso
configuracion.userhandle.NombreCompleto=Nombre Completo:
configuracion.userhandle.permisos=Permisos de Carpeta
configuracion.userhandle.PreferenciasdelSistema=Preferencias del sistema
configuracion.userhandle.PreferenciasPersonales=Preferencias personales:
configuracion.userhandle.RolenAcciones=Rol en Acciones:
configuracion.userhandle.RolenAdministracion=Rol en Administraci&oacute;n:
configuracion.userhandle.RolenAuditorias=Rol en Auditor&iacute;as:
configuracion.userhandle.RolenDocumentos=Rol en Documentos:
configuracion.userhandle.RolenEncuestas= Rol Encuestas:
configuracion.userhandle.RolenIndicadores=Rol en Indicadores:
configuracion.userhandle.RolenProyectos=Rol en Proyectos:
configuracion.userhandle.RolenQuejas= Rol Quejas:
configuracion.userhandle.RolenReportes=Rol en Reportes:
configuracion.userhandle.RolEnValidaciones=Rol en Validaciones:
configuracion.userhandle.---SINPRIVILEGIOS---=--- SIN PRIVILEGIOS ---
configuracion.userhandle.StatusUsuario=Estatus del Usuario:
configuracion.userhandle.Tecleeunaclave=- Teclee una clave
configuracion.userhandle.--TODAS--=-- TODAS --
configuracion.userhandle.Ubicacion=Departamento:
configuracion.userhandle.UsarPreferenciasDelSistema=Usar preferencias del sistema:
configuracion.userhandle.ValorStatusUsuario=Activo
configuracion.userlist.Activo=Activo
configuracion.userlist.Activo=Activo
configuracion.userlist.Area=Proceso
configuracion.userlist.Areaenlaquesedesempena=Proceso en el que se desempe&ntilde;a
configuracion.userlist.Borrar=Borrar
configuracion.userlist.BorrarUsuario=Borrar Usuario
configuracion.userlist.BusquedaEstado=Estado:
configuracion.userlist.CONTROLDEUSUARIOS=CONTROL DE USUARIOS
configuracion.userlist.Correoelectronico=Correo electr&oacute;nico:
configuracion.userlist.Cuenta=Cuenta
configuracion.userlist.CuentadeUsuario=Cuenta de Usuario:
configuracion.userlist.Editar=Editar
configuracion.userlist.EditarUsuario=Editar Usuario
configuracion.userlist.Estado= Estado
configuracion.userlist.Inactivo=Inactivo
configuracion.userlist.Inactivo=Inactivo
configuracion.userlist.javascript.Elusuarioseraeliminadocompletamente=El usuario ser\\u00e1 eliminado completamente. \u00bfDesea continuar?
configuracion.userlist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=La selecci\\u00f3n no puede ser eliminada, debido a que \\nesta ligado a otras partes dentro del sistema.\n\nElimine esas ligas y vuelva a intentarlo.
configuracion.userlist.Laseleccionhasidoeliminada=La selecci\u00f3n ha sido eliminada.
configuracion.userlist.NombreCompleto=Nombre Completo
configuracion.userlist.Noseencontraronregistros=No se encontraron registros.
configuracion.userlist.PorRegistrar=Por Registrar
configuracion.userlist.PORREGISTRAR=Por Registrar
configuracion.userlist.Todas=-- TODAS --
configuracion.userlist.Ubicacion=Departamento
configuracion.userlistEsteusuarionopuedeserborrado=Este usuario no puede ser borrado
configuracion.version.documentos=Versi\u00f3n Predeterminada de Documentos Nuevos:
coonfiguracion.userhandle.Perfiles= Perfiles:
ctividad.actividadlist.Creada=Creada
cuestionario.cuestionarioreportegeneral.chart.numerodecuestionarios=N\u00famero de cuestionarios
cuestionario.preguntasobv.Altadeaccion=Alta de acci&oacute;n
cuestionario.preguntasobv.Borrar=Borrar
cuestionarios.cuestionarioreportegeneral.chart.activo=ACTIVOS
cuestionarios.cuestionarioreportegeneral.chart.borrador=EN BORRADOR
cuestionarios.cuestionarioreportegeneral.chart.cancelado=CANCELADOS
cuestionarios.cuestionarioreportegeneral.chart.title=Cuestionarios
cuestionarios.cuestionarioreportegeneral.header=REPORTE GENERAL DE CUESTIONARIOS
cuestionarios.cuestionarioshandle.Autor=Autor:
cuestionarios.cuestionarioshandle.Borrar=Borrar
cuestionarios.cuestionarioshandle.Borrartodos=Borrar todos
cuestionarios.cuestionarioshandle.boton.Borrartodos=Borrar todos
cuestionarios.cuestionarioshandle.boton.Ordenar=Ordenar
cuestionarios.cuestionarioshandle.Clave=Clave:
cuestionarios.cuestionarioshandle.Cuestionariobase=Cuestionario base:
cuestionarios.cuestionarioshandle.Deleterseccion=Borrar secci&oacute;n
cuestionarios.cuestionarioshandle.Estatus=Estado:
cuestionarios.cuestionarioshandle.header.CUESTIONARIOS=CUESTIONARIOS
cuestionarios.cuestionarioshandle.javascript.dardealtauncuestionario=dar de alta un cuestionario:
cuestionarios.cuestionarioshandle.javascript.Elcuestionariohasidoagregado=El cuestionario ha sido agregado.
cuestionarios.cuestionarioshandle.javascript.Laseccionelegidayaexisteenlalista=La Secci\u00f3n elegida ya existe en la lista.
cuestionarios.cuestionarioshandle.javascript.Listar=Listar
cuestionarios.cuestionarioshandle.javascript.Losdatosdelcuestionariohansidoactualizados=Los datos del cuestionario han sido actualizados.
cuestionarios.cuestionarioshandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
cuestionarios.cuestionarioshandle.javascript.Lossiguientesdatossonnecesariospara=Los siguientes datos son necesarios:
cuestionarios.cuestionarioshandle.javascript.Nuevo=Nuevo
cuestionarios.cuestionarioshandle.javascript.Seleccioneunaseccion=Seleccione una Secci\u00f3n.
cuestionarios.cuestionarioshandle.javascript.-Titulo=- T\\u00edtulo
cuestionarios.cuestionarioshandle.Nosehanasignadosecciones=No se han asignado Secciones
cuestionarios.cuestionarioshandle.Removerseccion=Remover Secci&oacute;n
cuestionarios.cuestionarioshandle.Secciones=Secciones:
cuestionarios.cuestionarioshandle.Seccionesaagregar=Secciones a agregar.
cuestionarios.cuestionarioshandle.--SELECCIONE--=-- SELECCIONE --
cuestionarios.cuestionarioshandle.Titulo\:=T&iacute;tulo:
cuestionarios.cuestionarioshandle.Titulo=T&iacute;tulo
cuestionarios.cuestionarioslector.Autor=Autor
cuestionarios.cuestionarioslector.Clave=Clave
cuestionarios.cuestionarioslector.Cuestionario=Cuestionario
cuestionarios.cuestionarioslector.Est=Estado
cuestionarios.cuestionarioslector.EstadoActivo=Estado: Activo
cuestionarios.cuestionarioslector.EstadoBorrador=Estado: Borrador
cuestionarios.cuestionarioslector.EstadoCancelado=Estado: Cancelado
cuestionarios.cuestionarioslector.Estatus=Estado:
cuestionarios.cuestionarioslector.header.CONSULTADECUESTIONARIOS=CONSULTA DE CUESTIONARIOS
cuestionarios.cuestionarioslector.Noseencontraronregistros=No se encontraron registros.
cuestionarios.cuestionarioslector.Obv=Obv
cuestionarios.cuestionarioslector.Titulo=T&iacute;tulo:
cuestionarios.cuestionarioslector.Todos=TODOS
cuestionarios.cuestionarioslector.Verpreguntas=Ver preguntas
cuestionarios.cuestionarioslist.Autor=Autor
cuestionarios.cuestionarioslist.Borrar=Borrar
cuestionarios.cuestionarioslist.Borrarcuestionario=Borrar cuestionario
cuestionarios.cuestionarioslist.Clave=Clave
cuestionarios.cuestionarioslist.Cuestionario=Cuestionario
cuestionarios.cuestionarioslist.Editarpreguntas=Editar preguntas
cuestionarios.cuestionarioslist.Edt=Edt
cuestionarios.cuestionarioslist.ElcuestionarionopudosermarcadocomoactivoNecesitaalmenosunaseccionyquelasseccionesseleccionadastenganalmenosunapregunta=El cuestionario no pudo ser marcado como activo. Necesita al menos una secci\\u00f3n y que las Secciones seleccionadas tengan al menos una pregunta.
cuestionarios.cuestionarioslist.Est=Estado
cuestionarios.cuestionarioslist.EstadoActivoAccionCancelar=El estado actual es ACTIVO. Presione para marcar como CANCELADO.
cuestionarios.cuestionarioslist.EstadoBorradorAccionMarcarcomoactivo=El estado actual es BORRADOR.  Presione para marcar como ACTIVO.
cuestionarios.cuestionarioslist.EstadoCanceladoAccionMarcarcomoactivo=El estado actual es CANCELADO. Presione para marcar como ACTIVO.
cuestionarios.cuestionarioslist.Estatus=Estado:
cuestionarios.cuestionarioslist.header.CONTROLDECUESTIONARIOS-USR-=CONTROL DE CUESTIONARIOS-USR-
cuestionarios.cuestionarioslist.javascript.CambiaraelestadodelcuestionarioDeseacontinuar=Cambiar\\u00e1 el estado del cuestionario.\\n\u00bfDesea continuar?
cuestionarios.cuestionarioslist.javascript.ElcuestionarioseraremovidodelsistemaDeseacontinuar=El cuestionario ser\\u00e1 removido del sistema.\\n\u00bfDesea continuar?
cuestionarios.cuestionarioslist.javascript.Laseleccionnopuedesereliminadadebidoaque\\nestaligadoaotraspartesdentrodelsistema\n\nElimineesasligasyvuelvaaintentarlo=La selecci\\u00f3n no puede ser eliminada, debido a que \\nest\\u00e1 ligado a otras partes dentro del sistema.\n\nElimine esas ligas y vuelva a intentarlo.
cuestionarios.cuestionarioslist.Laseleccionhasidoeliminada=La selecci&oacute;n ha sido eliminada.
cuestionarios.cuestionarioslist.Noseencontraronregistros=No se encontraron registros.
cuestionarios.cuestionarioslist.Nosepuedeeditar=No se puede editar
cuestionarios.cuestionarioslist.Obv=Vista Previa
cuestionarios.cuestionarioslist.Preguntas=Preguntas
cuestionarios.cuestionarioslist.PresioneParaDarDeAltaPreguntasEnElCuestionarioYVerLasActuales=Presione para dar de alta preguntas en el cuestionario y ver las actuales.
cuestionarios.cuestionarioslist.PresioneParaDarDeAltaSub-ProcesosEnElCuestionarioYVerLasActuales= Presione para dar de alta Secciones en el cuestionario y ver las actuales.
cuestionarios.cuestionarioslist.Subprocesos=Secci&oacute;n
cuestionarios.cuestionarioslist.Titulo=T&iacute;tulo:
cuestionarios.cuestionarioslist.Todos=TODOS
cuestionarios.cuestionarioslist.Ver=Ver
cuestionarios.cuestionarioslist.Vercuestionario=Ver Cuestionario
cuestionarios.cuestionarioslist.Verpreguntas=Ver preguntas
cuestionarios.preguntas.Borrar=Borrar
cuestionarios.preguntas.boton.Borrarpreguntas=Borrar preguntas
cuestionarios.preguntas.Deleterpregunta=Borrar pregunta
cuestionarios.preguntas.header.EDICIONDEPREGUNTAS=EDICI&Oacute;N DE PREGUNTAS
cuestionarios.preguntas.javascript.Lapreguntaelegidayaexisteenlalista=La pregunta elegida ya existe en la lista.
cuestionarios.preguntas.javascript.Laspreguntasdelcuesionariohansidoactualizadas=Las preguntas del cuesionario han sido actualizadas.
cuestionarios.preguntas.javascript.Listar=Listar
cuestionarios.preguntas.javascript.Nuevo=Nuevo
cuestionarios.preguntas.javascript.Seleccioneunapregunta=Seleccione una pregunta
cuestionarios.preguntas.Nosehanasignadopreguntasaestaseccion=No se han asignado preguntas a esta secci&oacute;n
cuestionarios.preguntas.Preguntas=Preguntas:
cuestionarios.preguntas.Preguntasaagregar=Preguntas a agregar.
cuestionarios.preguntas.Removerpregunta=Remover pregunta
cuestionarios.preguntas.Secciones=Secciones:
cuestionarios.preguntas.--SELECCIONE--=-- SELECCIONE --
cuestionarios.preguntas.Titulo\:=T&iacute;tulo:
cuestionarios.preguntas.Titulo=T&iacute;tulo
cuestionarios.preguntasobv.Altaaccion=Alta acci&oacute;n
cuestionarios.preguntasobv.Autor=Autor:
cuestionarios.preguntasobv.boton.AgregarPregunta=Agregar Pregunta
cuestionarios.preguntasobv.boton.Imprimir=Imprimir
cuestionarios.preguntasobv.Calificacion=Calificaci&oacute;n
cuestionarios.preguntasobv.Clave=Clave:
cuestionarios.preguntasobv.Comentario=Comentario/Evidencia
cuestionarios.preguntasobv.ComentariosGenerales=Comentarios Generales:
cuestionarios.preguntasobv.Correctiva=Correctiva
cuestionarios.preguntasobv.Cuestionario=Cuestionario:
cuestionarios.preguntasobv.Deberespondertodaslaspreguntas=Debes responder todas las preguntas
cuestionarios.preguntasobv.Encargados=Encargados:
cuestionarios.preguntasobv.Estatus=Estado:
cuestionarios.preguntasobv.header.CUESTIONARIO-=CUESTIONARIO-
cuestionarios.preguntasobv.javascript.Consultar=Consultar
cuestionarios.preguntasobv.javascript.Esnecesarioponerunaobservacion=Es necesario poner una observaci&oacute;n.
cuestionarios.preguntasobv.javascript.Laauditoriahasidomarcadacomorealizadaylasrespuestassehanguardado=La auditor&iacute;a ha sido marcada como realizada y las respuestas se han guardado.
cuestionarios.preguntasobv.javascript.Lanotificacionasidoenviadaviacorreoelectronico=La notificaci&oacute;n a sido enviada via correo electr&oacute;nico.
cuestionarios.preguntasobv.javascript.Lanotificacionasidoenviadaviacorreoelectronico=La notificaci&oacute;n ha sido enviada v&iacute;a correo electr&oacute;nico.
cuestionarios.preguntasobv.javascript.ListarAuditorias=Control
cuestionarios.preguntasobv.javascript.NuevaAuditoria=Nueva Auditor&iacute;a
cuestionarios.preguntasobv.javascript.-Objetivos=- Objetivos.
cuestionarios.preguntasobv.javascript.Sehanactualizadolasrespuestasdelcuestionario=Se han actualizado las respuestas del cuestionario.
cuestionarios.preguntasobv.javascript.SilarespuestaaunapreguntafueNOoN/Aesnecesarioqueagregueuncomentario=Si la respuesta a una pregunta fue NO o N/A, es necesario\\nque agregue un comentario.
cuestionarios.preguntasobv.javascript.VerAuditoria=Ver Auditor&iacute;a
cuestionarios.preguntasobv.Laaccionseracompletamenteborradadelsistema,\u00bfDeseacontinuar?=La acci\\u00f3n ser\\u00e1 completamente borrada del sistema, \u00bfDesea continuar?
cuestionarios.preguntasobv.Mejoracontinua=Mejora continua
cuestionarios.preguntasobv.Preguntas=Preguntas
cuestionarios.preguntasobv.Preventiva=Preventiva
cuestionarios.preguntasobv.Proceso(s)Auditado(s)=Proceso(s) Auditado(s):
cuestionarios.preguntasobv.Resultados=Aceptar Resultados
cuestionarios.preguntasobv.Seccion=Secci&oacute;n:
cuestionarios.preguntasobv.Titulo=T&iacute;tulo:
cuestionarios.preguntasobv.Total=Total:
cuestionarios.preguntasobv.Ubicacion=Departamento:
cuestionarios.puntonormahandle.Descripcion=Descripci\u00f3n
cuestionarios.puntonormahandle.Elpuntodelanormahasidoactualizado=El punto de la norma ha sido actualizado.
cuestionarios.puntonormahandle.Elpuntodelanormahasidodadodealta=El punto de la norma ha sido dado de alta.
cuestionarios.puntonormahandle.PUNTOSDELANORMA=PUNTOS DE LA NORMA
cuestionarios.puntonormahandle.Titulo=T&iacute;tulo
cuestionarios.puntonormalist.CONTROLDEPUNTOSDELANORMA=CONTROL DE PUNTOS DE LA NORMA
cuestionarios.puntonormalist.Elpuntodelanormaseraborradodelsistema\u00bfDeseacontinuar?=El punto de la norma ser\\u00e1 borrado del sistema. \u00bfDesea continuar?
cuestionarios.puntonormalist.Nosehapodidoborrarelobjeto=No se ha podido borrar el objeto
cuestionarios.puntonormalist.Sehaeliminadoelobjeto=Se ha eliminado el objeto
cuestionarios.puntonormalist.Titulo=T&iacute;tulo
cuestionarios.seccionlist.BusquedaNumero=N&uacute;mero
cuestionarios.seccionlist.Numero=Numero:
documento.fechaCancelacion=Fecha Cancelaci\u00f3n
documento.papelera.cambioEstado=El documento sera restaurado, \u00bfDesea Continuar?
documentos.carpetahandle.header.NUEVACARPETA=NUEVA CARPETA
documentos.carpetahandle.javascript.Lacarpetanohasidonombrada=La carpeta no ha sido nombrada
documentos.carpetahandle.Nombredelacarpeta=Nombre de la carpeta:
documentos.carpetapermisos.Areas=Procesos
documentos.carpetapermisos.header.PermisosDeCarpeta=PERMISOS DE CARPETA
documentos.carpetapermisos.NoCuentaConPermisosParaVerEsteDocumento=No cuenta con permisos para ver este documento.
formularios.NoCuentaConPermisosParaVerEsteFormulario=No cuenta con permisos para ver este formulario.
documentos.carpetapermisos.NoSePudieronActualizarLosPermisosDeLaCarpeta=No se pudieron actualizar los permisos de la carpeta.
documentos.carpetapermisos.Usuarios=Usuarios
documentos.documentohandle.Delasiguientelistadeautorizantes=De la siguiente lista de autorizantes
documentos.documentohandle.Delasiguientelistadeautorizantes=De la siguiente lista de autorizantes
documentos.documentohandle.Eldocumentonofueautorizado=(No autorizado)
documentos.documentohandle.Eldocumentonofueautorizadopor=El documento no fue autorizado por
documentos.documentohandle.Secuenciacompleta=Secuencia completa
documentos.documentonocontroladohandle.Eldocumentohasidoagregado=El documento ha sido agregado
documentos.documentoproyectoshandle.Laclavedeldocumentoelegidoyaexisteenunasolicitud=La clave del documento elegido ya existe en una solicitud.
documentos.documentoproyectoshandle.Yaexisteelt\u00edtulodeldocumentoelegido=Ya existe el t\u00edtulo del documento elegido.
documentos.documentoproyectoshandle.Yaexistelaclavedeldocumentoelegido=Ya existe la clave del documento elegido.
Documentos.Documentos.Limpiar=Limpiar
documentos.documentoshandle.aprobacion=aprobaci&oacute;n
documentos.documentoshandle.Aprobador=Aprobador
documentos.documentoshandle.Aprobador=Aprobador
documentos.collectingAndStoreResponsible=Responsable de recolectar y archivar
documentos.informationClassification=Clasificaci\u00f3n de la informaci\u00f3n
documentos.disposition=Disposici\u00f3n
documentos.documentoshandle.Archivo=Archivo:
documentos.documentoshandle.ArchivoActual=Ver archivo actual
documentos.documentoshandle.ArchivoOriginal=Descargar archivo original
documentos.documentoshandle.ArchivoSubidoEnSolicitud=Archivo subido en solicitud:
documentos.documentoshandle.Asignarlectores=Asignar lectores
documentos.documentoshandle.Bajar=Bajar:
documentos.documentoshandle.Bajardocumento=Bajar documento
documentos.documentoshandle.Formulario=Formulario
documentos.documentoshandle.Ver=Ver
documentos.documentoshandle.boton.CrearSecuencia=Crear Secuencia
documentos.documentoshandle.boton.GenerarRegistro=Generar Registro
documentos.documentoshandle.cambio=cambio
documentos.documentoshandle.Clave=Clave
documentos.documentoshandle.Clave=Clave
documentos.documentoshandle.TituloArchivo=T&iacute;tulo del archivo
documentos.documentoshandle.DetalledelaUltimaaprobacion=Detalle de la &Uacute;ltima aprobaci&oacute;n:
documentos.documentoshandle.DocumentoElectronico=Documento Electronico
documentos.documentoshandle.Elarchivodeberallevarlaextensiondocxlspdfhtmhtml=El archivo deber&aacute; llevar solo las extenciones: .doc/x, .xls/x, .ppt/x, .pdf, .htm &oacute; .html
documentos.documentoshandle.Eldocumentoesnuevonocuentaconrevisiones=El documento es nuevo no cuenta con revisiones.
documentos.documentoshandle.Estatus=Estado
documentos.documentoshandle.Fecha=Fecha
documentos.documentoshandle.Fechade=Fecha de aprobaci&oacute;n
documentos.documentoshandle.header.DOCUMENTOS=DOCUMENTOS
documentos.documentoshandle.header.FORMULARIOS=FORMULARIOS
documentos.documentoshandle.javascript.Archivo=- Archivo
documentos.documentoshandle.javascript.Clave=- Clave
documentos.documentoshandle.javascript.ElDocumentosehaagregadoconexitoalalistadeesperaporRevision=El documento se ha anexado con &eacute;xito.
documentos.documentoshandle.javascript.Faltanlossiguienteselementos=Faltan los siguientes elementos
documentos.documentoshandle.javascript.LaactualizaciondeldocumentohasidopuestaenesperadeRevision=La actualizaci&oacute;n del documento ha sido puesta en espera de Revisi&oacute;n.
documentos.documentoshandle.javascript.LosDatosFueronCambiadosConExito=Los datos fueron cambiados con &eacute;xito.
documentos.documentoshandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
documentos.documentoshandle.javascript.Noconozcolaclave=No conozco la clave
documentos.documentoshandle.javascript.Nuevodocumento=Nuevo documento
documentos.documentoshandle.javascript.Originador=- Originador
documentos.documentoshandle.javascript.Razondelcambio=- Raz\\u00f3n del alta/cambio
documentos.documentoshandle.javascript.Seleccioneundocumentoelectronico=Seleccione un documento electronico.
documentos.documentoshandle.javascript.Titulo=- T\\u00edtulo
documentos.documentoshandle.javascript.Vercarpeta=Ver carpeta
documentos.documentoshandle.Laclaveingresadaestarestringidaparalageneracionderespaldos=La Clave ingresada esta restringida para la generacion de respaldos.
documentos.documentoshandle.lectores=Listado de usuarios que han le\u00eddo el documento:
documentos.documentoshandle.lectoresdelacopiacontrolada=Lectores de la copia controlada:
documentos.documentospendientes.Documentosporentregarcopiacontrolada=Documentos por entregar copia controlada:
documentos.documentospendientes.DocumentosPorRecogerCopiaControlada=Documentos por recoger copia controlada:
documentos.documentoshandle.Localizadoen=Localizado en:
documentos.documentoshandle.AccessPermissionReason=Lo puedes ver por:
documentos.documentoshandle.Nivelde=Nivel de
documentos.documentoshandle.onosepudolocalizarelarchivoindicado=o no se pudo localizar el archivo indicado
documentos.documentoshandle.Originador=Autor de la edici\u00f3n actual
documentos.documentoshandle.Razonde=Raz&oacute;n de la solicitud
documentos.documentoshandle.Razondelcambio=Raz&oacute;n del alta/cambio:
documentos.documentoshandle.revision=Edici\u00f3n
documentos.documentoshandle.--Seleccione--=-- SELECCIONE --
documentos.documentoshandle.Solicitudesrelacionadasaldocumento=Solicitudes relacionadas al documento
documentos.documentoshandle.Titulo=T&iacute;tulo
documentos.documentoshandle.slimReportName=Nombre del reporte
documentos.documentoshandle.Verdetalledeautorizacion=Ver detalle de autorizaci&oacute;n
documentos.documentoshandle.Version=Versi&oacute;n:
documentos.documentoshandle.VolveraPendientes=Volver a Pendientes
documentos.documentoshandle.Yaexisteeltitulooclavedeldocumentoelegido=Ya existe el t&iacute;tulo o clave del documento elegido
documentos.documentoshandle.Yaexisteunarchivoconelmismonombre=Ya existe un archivo con el mismo nombre
documentos.documentoslector.Aprobacion=Aprobaci\u00f3n
documentos.documentoslector.Aprovador=Aprobador
documentos.documentoslector.Carpeta=Carpeta:
documentos.documentoslector.Clave=Clave
documentos.documentoslector.DocumentoCancelado=Documento Cancelado
documentos.documentoslector.DocumentoDescontinuado=Documento Descontinuado
documentos.documentoslector.Edici\u00f3n=Edici\u00f3n
documentos.documentoslector.Enautorizaci\u00f3n=En autorizaci\u00f3n
documentos.documentoslector.Entrada=Entrada
documentos.documentoslector.Est=Estado
documentos.documentoslector.EstadoActivo=El estado actual es ACTIVO.
documentos.documentoslector.EstadoNuevaversionenprocesoderevision=El estado actual es de nueva versi&oacute;n en proceso de REVISI&Oacute;N.
documentos.documentoslector.Estatus=Estado:
documentos.documentoslector.Fechaapb=Fecha apb.
documentos.documentoslector.header.LISTAMAESTRA=LISTA MAESTRA
documentos.documentoslector.header.PENDIENTESPORASIGNARLECTOR=PENDIENTES POR ASIGNAR LECTORES
documentos.documentoslector.Nocontroladoeliminado=No controlado eliminado
documentos.documentoslector.Nombre=Nombre
documentos.documentoslector.Nombre=Nombre:
documentos.documentoslector.Noseencontrarondocumentosenestabusqueda=No se encontraron documentos en esta b&uacute;squeda.
documentos.documentoslector.Originador=Originador
documentos.documentoslector.Revision=Revisi&oacute;n
documentos.documentoslector.ruta=Ruta
documentos.documentoslector.Tipodedocumento=Tipo de documento
documentos.documentoslector.Ver=Ver
documentos.documentoslisate.ElestadoactualesEDICION,Eldocumentotienesecuenciaspendientes(usuarlaopcionver)=El estado actual es EDICION, El documento tiene secuencias pendientes (usar la opcion Ver).
documentos.documentoslist.AccionCambiaraactivo=Acci&oacute;n: Cambiar a activo
documentos.documentoslist.AccionCambiaraactivo=Acci&oacute;n: Cambiar a activo
documentos.documentoslist.AccionCambiaraactivo=Acci&oacute;n: Cambiar a activo
documentos.documentoslist.AccionCambiaraactivo=Acci&oacute;n: Cambiar a activo
documentos.documentoslist.Activo=Activo
documentos.documentoslist.Actualizarmenudenavegacion=Actualizar menu de navegaci&oacute;n
documentos.documentoslist.apbfechafin=Fecha fin de aprobaci&oacute;n
documentos.documentoslist.apbfechainicio=Fecha inicio de aprobaci&oacute;n
documentos.documentoslist.Autorizacion=Autorizaci&oacute;n
documentos.documentoslist.Borrar=Borrar
documentos.documentoslist.Cancelado=Cancelado
documentos.documentoslist.Carpeta=Carpeta:
documentos.documentoslist.Clave=Clave
documentos.documentoslist.Crearnuevacarpeta=Crear nueva carpeta
documentos.documentoslist.Crearnuevodocumento=Crear nuevo documento
documentos.documentoslist.Descontinuado=Descontinuado
documentos.documentoslist.DocumentoActivo= Documento Activo
documentos.documentoslist.DocumentoCancelado= Documento Cancelado
documentos.documentoslist.DocumentoDescontinuado=Documento Descontinuado
documentos.documentoslist.DocumentoEnAutorizacion= Documento en Autorizaci&oacute;n
documentos.documentoslist.DocumentoEnEdicion=Documento en Edici&oacute;n
documentos.documentoslist.DocumentoNoControlado=Documento No controlado
documentos.documentoslist.Edicion=Edici&oacute;n
documentos.documentoslist.Eliminarcarpeta=Eliminar carpeta
documentos.documentoslist.Eliminardocumento=Eliminar documento
documentos.documentoslist.Enautorizacion=En Autorizaci\u00f3n.
documentos.documentoslist.entfechafin=Fecha fin de entrada
documentos.documentoslist.entfechainicio=Fecha inicio de entrada
documentos.documentoslist.Est=Estado
documentos.documentoslist.estado=Estado
documentos.documentoslist.Estado=Estado
documentos.documentoslist.EstadoActivoAccionCambiaraeditar=Estado: Activo  Acci&oacute;n: Cambiar a editar
documentos.documentoslist.Estatus=Estado:
documentos.documentoslist.Fechaapb=Fecha aprobaci\u00f3n
documentos.documentoslist.Fechaent=Fecha entrega
documentos.documentoslist.header.CONTROLDEDOCUMENTOS=CONTROL DE DOCUMENTOS
documentos.documentoslist.javascript.CambiaraelestadodeeldocumentoDeseacontinuar=Cambiar\\u00e1 el estado del documento.\u00bfDesea continuar?
documentos.documentoslist.javascript.Carpeta=Carpeta
documentos.documentoslist.javascript.EldocumentoseraremovidocompletamentedelsistemaDeseacontinuar=El documento ser\\u00e1 removido completamente del sistema.\\n\u00bfDesea continuar?
documentos.documentoslist.javascript.LacarpetaseraremovidacompletamentedelsistemaDeseacontinuar=La carpeta ser\\u00e1 removida completamente del sistema.\\n\u00bfDesea continuar?
documentos.documentoslist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=La selecci\\u00f3n no puede ser eliminada, debido a que \\nesta ligado a otras partes dentro del sistema.\n\nElimine esas ligas y vuelva a intentarlo.
documentos.documentoslist.Lacarpetaestavacia=La carpeta esta vac&iacute;a.
documentos.documentoslist.Laseleccionhasidoeliminada=La selecci&oacute;n ha sido eliminada.
documentos.documentoslist.NoControlado=No controlado
documentos.documentoslist.Nocontrolado=No controlado
documentos.documentoslist.Nocontroladoeliminado=No controlado eliminado
documentos.documentoslist.Nombre=Nombre
documentos.documentoslist.Nombre=Nombre
documentos.documentoslist.Noseencontrarondocumentosenestabusqueda=No se encontraron documentos en esta b&uacute;squeda.
documentos.documentoslist.noSePuedeModificar=No se puede modificar el estado del archivo
documentos.documentoslist.Originador=Originador
documentos.documentoslist.Proyectos=Proyectos.
documentos.documentoslist.Ver=Ver
documentos.documentoslist.Verdocumento=Ver documento
documentos.documentoslist.Descargardocumentotitle=Descargar documento
documentos.documentoslist.Descargardocumento=Descargar documento
documentos.documentoslistadocp.Activo=Activo
documentos.documentoslistae.Activo=Activo
documentos.documentoslistae.Agregardocumentoreferenciado=Agregar documento referenciado
documentos.documentoslistae.Autor=Autor
documentos.documentoslistae.Borrar=Borrar
documentos.documentoslistae.Carpeta=Carpeta:
documentos.documentoslistae.Clave=Clave
documentos.documentoslistae.DocumentosControlados=Documentos Controlados
documentos.documentoslistae.DocumentosNoControlados=Documentos No Controlados
documentos.documentoslistae.DocumentosReferenciados=Documentos Referenciados
documentos.documentoslistae.Eliminarcarpeta=Eliminar carpeta
documentos.documentoslistae.ElsestadoactualesEDICIONporRE-APROBACION.Seesperaqueseactualiceeldocumentosparasunuevarevision(usuarlaopcionver)=El estado actual es EDICI&Oacute;N por RE-APROBACI&Oacute;N. Se espera que actualice el documento para su nueva revisi&oacute;n (Usar la opci&oacute;n Ver).
documentos.documentoslistae.Est=Estado
documentos.documentoslistae.EstadoActivoAccionCambiaraedicion=El estado actual es ACTIVO. Presione para cambiar a EDICI&Oacute;N.
documentos.documentoslistae.EstadoEdicionAccionCambiaraActivo=El estado actual es EDICI&Oacute;N. Presione para cambiar a ACTIVO.
documentos.documentoslistae.EstadoEnedicionAccionSeesperaqueactualizeeldocumentoparasunuevarevisionusarlaopcionVer=El estado actual es EDICI&Oacute;N. Se espera que actualice el documento para su nueva revisi&oacute;n (usar la opci&oacute;n Ver)
documentos.documentoslistae.Estatus=Estado:
documentos.documentoslistae.Fechaapb=Fecha apb.
documentos.documentoslistae.Fechaent=Fecha ent.
documentos.documentoslistae.header.CONSULTADEDOCUMENTOSACTIVOSYEDICION=CONSULTA DE DOCUMENTOS ACTIVOS Y EN EDICI&Oacute;N
documentos.documentoslistae.javascript.CambiaraelestadodeeldocumentoDeseacontinuar=Cambiar\\u00e1 el estado de el documento.\\n\u00bfDesea continuar?
documentos.documentoslistae.javascript.LacarpetaseraremovidacompletamentedelsistemaDeseacontinuar=La carpeta ser\\u00e1 removida completamente del sistema.\\n\u00bfDesea continuar?
documentos.documentoslistae.Lacarpetaestavacia=La carpeta esta vac&iacute;a.
documentos.documentoslistae.Medios=Medios
documentos.documentoslistae.ModificarPermisos=Presione para asignar permisos a la carpeta.
documentos.documentoslistae.Mover=Mover
documentos.documentoslistae.Moverdocumento=Mover documento
documentos.documentoslistae.Nohayimagenesparagaleria= No hay im&aacute;genes para galer&iacute;a.
documentos.documentoslistae.Nombre\:=Nombre:
documentos.documentoslistae.Nombre=Nombre
documentos.documentoslistae.Nopuedealterarelestadodeldocumentoyaquenolepertenece=No puede alterar el estado del documento ya que no le pertenece.
documentos.documentoslistae.Noseencontrarondocumentosenestabusqueda=No se encontraron documentos en esta b&uacute;squeda.
documentos.documentoslistae.Originador=Originador
documentos.documentoslistae.Presioneparacrearunaimagen=Presione para crear un nuevo medio
documentos.documentoslistae.Presioneparacrearundocumentonocontrolado=Presione para crear un documento no controlado
documentos.documentoslistae.Presioneparacrearunmedio=Presione para crear un nuevo medio
documentos.documentoslistae.Seeliminaraeldocumentonocontrolado.Deseacontinuar?=Se eliminar\\u00e1 el documento no controlado. \u00bfDesea continuar?
documentos.documentoslistae.Seeliminaralaimagendelagaleria.Deseacontinuar?=Se eliminar\\u00e1 la imagen de la galer\\u00eda. \u00bfDesea continuar?
documentos.documentoslistae.Ver=Ver
documentos.documentoslistae.Verdocumento=Ver documento
documentos.documentoslistcd.(revision)= (Revisi&oacute;n)
documentos.documentoslistcd.Borrar=Borrar
documentos.documentoslistcd.BorrarDocumento=Borrar Documento
documentos.documentoslistcd.Clave=Clave
documentos.documentoslistcd.DocumentosControlados=Documentos Controlados
documentos.documentoslistcd.DocumentosenEdicion=Documento en Edici&oacute;n.
documentos.documentoslistcd.DocumentosNoControlados=Documentos No Controlados
documentos.documentoslistcd.Eliminardocumento=Eliminar documento
documentos.documentoslistcd.Est=Estado
documentos.documentoslistcd.EstadoCanceladoAccionRestaurarasucarpetacomoenedicion=El estado actual es el de CANCELADO. Presione para restaurar a su carpeta como en EDICI&Oacute;N.
documentos.documentoslistcd.EstadoDescontinuadoEldocumentosolopuedeserborrado=El estado actual es DESCONTINUADO. El documento solo puede ser borrado.
documentos.documentoslistcd.Estatus=Estado:
documentos.documentoslistcd.Fechaapb=Fecha apb.
documentos.documentoslistcd.Fechaent=Fecha ent.
documentos.documentoslistcd.header.DOCUMENTOSCANCELADOSYDESCONTINUADOS=DOCUMENTOS CANCELADOS Y DESCONTINUADOS
documentos.documentoslistcd.javascript.CambiaraelestadodeeldocumentoDeseacontinuar=Cambiar\\u00e1 el estado de el documento.\\n\u00bfDesea continuar?
documentos.documentoslistcd.javascript.EldocumentoseraremovidocompletamentedelsistemaDeseacontinuar=El documento ser\\u00e1 removido completamente del sistema.\\n\u00bfDesea continuar?
documentos.documentoslistcd.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarloPosibleerrorVerifiquequeeldocumentonotengaunasecuenciaasignada=La selecci\\u00f3n no puede ser eliminada, debido a que \\nesta ligado a otras partes dentro del sistema.\n\nElimine esas ligas y vuelva a intentarlo.\n\nPosible error: Verifique que el documento no tenga\\nuna secuencia asignada.
documentos.documentoslistcd.Laseleccionhasidoeliminada=La selecci&oacute;n ha sido eliminada.
documentos.documentoslistcd.Medios=Medios
documentos.documentoslistcd.Nohayimagenesparagaleria=No hay im&aacute;genes para galer&iacute;a.
documentos.documentoslistcd.Nombre=Nombre
documentos.documentoslistcd.Nombre=Nombre:
documentos.documentoslistcd.Noseencontrarondocumentosenestabusqueda=No se encontraron documentos en esta b&uacute;squeda.
documentos.documentoslistcd.Originador=Originador
documentos.documentoslistcd.Seeliminaralaimagendelagaleria\u00bfDeseacontinuar?=Se eliminar\\u00e1 la imagen de la galer\\u00eda. \u00bfDesea continuar?
documentos.documentoslistcd.Titulo=Titulo
documentos.documentoslistcd.Ver=Ver
documentos.documentoslistcd.Verdocumento=Ver documento
documentos.documentoslistpa.Clave=Clave
documentos.documentoslistpa.Enautorizacion=En autorizaci&oacute;n
documentos.documentoslistpa.Est=Estado
documentos.documentoslistpa.EstadoEnautorizacionAccionIraautorizar=Estado: En autorizaci&oacute;n  Acci&oacute;n: Ir a autorizar
documentos.documentoslistpa.EstadoEnautorizacionAccionIraautorizar=Estado: En autorizaci&oacute;n  Acci&oacute;n: Ir a autorizar
documentos.documentoslistpa.Estatus=Estado:
documentos.documentoslistpa.Fechadeentrada=Fecha en que se subi&oacute; el documento
documentos.documentoslistpa.header.CONSULTADEDOCUMENTOSPORAUTORIZAR=CONSULTA DE DOCUMENTOS POR AUTORIZAR
documentos.documentoslistpa.Nombre=Nombre:
documentos.documentoslistpa.Nombredocumento=Nombre documento
documentos.documentoslistpa.NoseencontrarondocumentosconelnombreespecificadoCambieelnombreeintentedenuevo=No se encontraron documentos con el nombre espec&iacute;ficado. Cambie el nombre e intente de nuevo.
documentos.documentoslistpa.Originador=Originador
documentos.documentoslistpa.Ver=Ver
documentos.documentoslistpa.Verdocumento=Ver documento
documentos.documentospendientes.Accionesanalizarlacausaraiz=Acciones analizar la causa ra&iacute;z:
documentos.documentospendientes.Accionesatomarparaimplementar=Acciones a tomar para implementar:
documentos.documentospendientes.AccionesATomarPorRealizar=Acciones a Tomar por Realizar:
documentos.documentospendientes.AccionesATomarPorCompletar=Acciones a Tomar por Completar:
documentos.documentospendientes.AccionesATomarPorVerificar=Acciones a Tomar por Verificar:
documentos.documentospendientes.AccionesATomarPorVerificarNoAplica=Acciones a Tomar por Verificar como cancelada:
documentos.documentospendientes.AccionesCorrectivas=Acciones Correctivas
documentos.documentospendientes.AccionesMejoraContinua=Acciones de Mejora Continua
documentos.documentospendientes.Accionesparaaceptar=Acciones para aceptar:
documentos.documentospendientes.Accionesparaasignar=Acciones para asignar:
documentos.documentospendientes.AccionesPorAceptar=Acciones por Aceptar\:
documentos.documentospendientes.AccionesPorAgregar=Acciones por Agregar Acci\u00f3n de Correcci\u00f3n Inmediata:
documentos.documentospendientes.AccionesPorAnalizar=Acciones por Analizar:
documentos.documentospendientes.AccionesPorAsignar=Acciones por Asignar Responsable:
documentos.documentospendientes.AccionesPreventivas=Acciones Preventivas
documentos.documentospendientes.AccionesProducto=Acciones sobre Productos no conforme
documentos.documentospendientes.AccionesProyecto=Acciones sobre Proyectos
documentos.documentospendientes.Accionestomarparaverificar=Acciones a tomar para verificar:
documentos.documentospendientes.ActividadesRealizar=Actividades por Realizar: 
documentos.documentospendientes.Actividadesretrasadas=Actividades retrasadas:
documentos.documentospendientes.ActividadesVerificar=Actividades Por Verificar:
documentos.documentospendientes.Actualizar=Actualizar
documentos.documentospendientes.AuditoriasporConfirmar=Auditor&iacute;as por Confirmar:
documentos.documentospendientes.Auditoriaspornotificar=Auditor&iacute;as por notificar:
documentos.documentospendientes.CambiosAutorizar=Actividades con Cambios por Autorizar:
documentos.documentospendientes.Documentospendientes=Documentos por autorizar:
documentos.documentospendientes.ExpiredDocuments=Documentos expirados:
documentos.documentospendientes.Documentosporasignarlectores=Documentos por asignar lectores: 
documentos.documentospendientes.Documentosporleer=Documentos por leer: 
documentos.documentospendientes.Encuestasporcerrar=Encuestas por cerrar:
documentos.documentospendientes.Encuestasporcontestar=Encuestas por contestar:
documentos.documentospendientes.header.Pendientes=Pendientes
documentos.documentospendientes.header.PendientesProyecto=Pendientes del M&oacute;dulo de Proyectos
documentos.documentospendientes.MinutasPorRealizar=Minutas por realizar\:
documentos.documentospendientes.PendientesdeEncuestas=Pendientes de Encuestas
documentos.documentospendientes.PendientesdeQuejas=Pendientes de Quejas
documentos.documentospendientes.PendientesdeRegistrodeusuarios=Pendientes de Registro de Usuarios
documentos.documentospendientes.PendientesProyecto=Pendientes de Proyecto
documentos.documentospendientes.PeticionesPresupuestoAutorizar=Actividades con Peticiones de Presupuesto por Autorizar:
documentos.documentospendientes.ProyectosCerrar=Proyectos por Cerrar:
documentos.documentospendientes.Quejasatendidasporevaluarefectividad=Quejas atendidas por evaluar efectividad:
documentos.documentospendientes.QuejasatendidasporevaluarefectividadTooltip=Listado de quejas de las que es autor y han sido respondidas, la cuales debe evaluar el resultado.
documentos.documentospendientes.QuejasatendidasporevaluarefectividadTooltipUR=Voz Ur por Evaluar Efectividad
documentos.documentospendientes.Quejasporasignarresponsable=Quejas por asignar responsable:
documentos.documentospendientes.QuejasporAsignarResponsable=Quejas por Asignar Responsable:
documentos.documentospendientes.QuejasporAsignarResponsableTooltip=Listado de quejas creadas a las cuales es necesario que le asigne un responsable.
documentos.documentospendientes.QuejasporAsignarResponsableTooltipUR=Voz UR por asignar responsable
documentos.documentospendientes.Quejaspordarrespuesta=Quejas por dar respuesta:
documentos.documentospendientes.QuejaspordarrespuestaTooltip=Listado de quejas que debe atender y dar respuesta como el responsable asignado.
documentos.documentospendientes.QuejaspordarrespuestaTooltipUR=Voz Ur por dar Respuesta
documentos.documentospendientes.Quejasporverificarrespuesta=Quejas por verificar respuesta:
documentos.documentospendientes.QuejasporverificarrespuestaTooltip=Listado de quejas respondidas por el responsable de las cuales debe verificar la respuesta.
documentos.documentospendientes.QuejasporverificarrespuestaTooltipUR=Voz Ur por Verificar Respuesta
documentos.documentospendientes.RedactarReporteAuditoria=Reportes de Auditor&iacute;as por Realizar:
documentos.documentospendientes.ReportedeAuditoriasporAceptar=Reporte de Auditor&iacute;as por Aceptar Resultados:
documentos.documentospendientes.Secuenciasporcrear=Secuencias por crear:
documentos.documentospendientes.Solicitudesporatender=Solicitudes por verificar:
documentos.documentospendientes.Formulariosporcompletar=Formularios por completar:
documentos.documentospendientes.Usuariosporactivar=Usuarios por activar:
documentos.documentospendientes.Usuariosporasignarpuesto=Usuarios por asignar un puesto:
documentos.documentosreportegeneral.chart.activo=ACTIVOS
documentos.documentosreportegeneral.chart.cancelado=CANCELADOS
documentos.documentosreportegeneral.chart.descontinuado=DESCONTINUADOS
documentos.documentosreportegeneral.chart.enautorizacion=EN SOLICITUD
documentos.documentosreportegeneral.chart.enedicion=EN EDICI\u00d3N
documentos.documentosreportegeneral.chart.nocontrolado=NO CONTROLADOS
documentos.documentosreportegeneral.chart.inactivo=INACTIVOS
documentos.documentosreportegeneral.chart.numerodedocumentos=N\u00famero de documentos
documentos.documentosreportegeneral.chart.title=Documentos
documentos.documentosreportegeneral.header=REPORTE DE DOCUMENTOS
documentos.documentossecuencia.Borrar=Borrar
documentos.documentossecuencia.Clave=Clave
documentos.documentossecuencia.ElDocumentoNoPudoSerBorrado=El documento no pudo ser borrado.
documentos.documentossecuencia.Enautorizacion=En autorizaci&oacute;n
documentos.documentossecuencia.Est=Estado
documentos.documentossecuencia.EstadoEnautorizacionAccionCrearsecuencia=Estado: En autorizaci&oacute;n  Acci&oacute;n: Crear secuencia
documentos.documentossecuencia.Estatus=Estado:
documentos.documentossecuencia.Fechadeentrada=Fecha de entrada
documentos.documentossecuencia.header.CONSULTADEDOCUMENTOSSINSECUENCIADEAUTORIZACION=CONSULTA DE DOCUMENTOS SIN SECUENCIA DE AUTORIZACI&Oacute;N
documentos.documentossecuencia.Nombre=Nombre:
documentos.documentossecuencia.Nombredocumento=Nombre documento
documentos.documentossecuencia.NoseencontrarondocumentosconelnombreespecificadoCambieelnombreeintentedenuevo=No se encontraron documentos con el nombre espec&iacute;ficado. Cambie el nombre e intente de nuevo.
documentos.documentossecuencia.Originador=Originador
documentos.documentossecuencia.Razonporlaqueserechazalasecuenciadeldocumento=Raz&oacute;n por la que se rechaza la secuencia del documento.
documentos.documentossecuencia.Rechazar.tooltip=Rechazar Secuencia del Documento
documentos.documentossecuencia.Rechazar=Rechazar
documentos.documentossecuencia.Searechazadoiniciarlasecuenciadelasolicituddeldocumentoyseenvioelavisoaloriginadordedichodocumento=Se a rechazado iniciar la secuencia de la solicitud del documento y se envi\\u00f3 el aviso al originador de dicho documento
documentos.documentossecuencia.Seharechazadoiniciarlasecuenciadelasolicituddeldocumentoyseenvioelavisoaloriginadordedichodocumento=Se a rechazado iniciar la secuencia de la solicitud del documento y se envio el aviso al originador de dicho documento.
documentos.documentossecuencia.Ver=Ver
documentos.documentossecuencia.Verdocumento=Ver documento
documentos.galeria.-Anterior=-Anterior
documentos.galeria.Busqueda=B&uacute;squeda
documentos.galeria.CLAVE= CLAVE:
documentos.galeria.Clave= Clave:
documentos.galeria.Descripcion= Descripci&oacute;n:
documentos.galeria.DESCRIPCION= DESCRIPCION:
documentos.galeria.Nohayalbumesafregados= No hay &aacute;lbumes agregados.
documentos.galeria.Nohayimagenesenestealbum=No hay imagenes en este &aacute;lbum.
documentos.galeria.Seleccioneunalbumdelaizquierda=Seleccione un &aacute;lbum de la izquierda.
documentos.galeria.Siguiente-=Siguiente-
documentos.galeria.Titulo= T&iacute;tulo:
documentos.galeria.TITULO= TITULO:
documentos.listarelaciones.(Autorizacion)=(Autorizaci&oacute;n)
documentos.listarelaciones.(Edicion)=(Edicion)
documentos.listarelaciones.(Revision)=(Revisi&oacute;n)
documentos.listarelaciones.DocumentoCancelado=Documento Cancelado
documentos.listarelaciones.DocumentoDescontinuado=Documento Descontinuado
documentos.listarelaciones.header.LISTARELACIONES=LISTA DE RELACIONES
documentos.LugarDeAlmacenamiento=Lugar de Almacenamiento
documentos.Noseencontrarondocumentos=No se encontraron documentos
documentos.papelara.documentoNoControlado=Documentos no Controlados
documentos.papelera.documentoControlado=Documentos Controlados
documentos.papelera.Titulo=T\u00edtulo
documentos.referenciahandle.Nodoareferenciar=Nodo a Referenciar:
documentos.referenciahandle.REFERENCIACIONDEDOCUMENTOS=REFERENCIACION  DE DOCUMENTOS
documentos.secuenciahandle.Agregarusuarioalasecuencia=Agregar usuario a la secuencia
documentos.secuenciahandle.Agregatemplatealasecuencia=Agregar plantilla a la secuencia
documentos.secuenciahandle.Autorizacion=Autorizaci&oacute;n:
documentos.secuenciahandle.AUTORIZADA=AUTORIZADA
documentos.secuenciahandle.Borrar=Borrar
documentos.secuenciahandle.Borrarusuariodelasecuencia=Borrar usuario de la secuencia
documentos.secuenciahandle.boton.Autorizardirectamente=Autorizar directamente
documentos.secuenciahandle.boton.Borrartodos=Borrar todos
documentos.secuenciahandle.boton.Grabarsecuencia=Grabar secuencia
documentos.secuenciahandle.Comentario\:=Comentario:
documentos.secuenciahandle.Comentario=Comentario
documentos.secuenciahandle.Documentosaautorizar=Documentos a autorizar
documentos.secuenciahandle.Documentosinsecuencia=Documentos sin secuencia
documentos.secuenciahandle.Elusuarioyaseencuentraenlasecuencia=El usuario ya se encuentra en la secuencia
documentos.secuenciahandle.ENPROCESO=EN PROCESO
documentos.secuenciahandle.Estado=Estado
documentos.secuenciahandle.Fecha=Fecha
documentos.secuenciahandle.FechaCreacion=Fecha de Creaci&oacute;n
documentos.secuenciahandle.FechaLimite=Fecha L&iacute;mite
documentos.secuenciahandle.header.SECUENCIADEAUTORIZACIONES=SECUENCIA DE AUTORIZACIONES
documentos.secuenciahandle.header.SECUENCIADECANCELACION=SECUENCIA DE CANCELACION
documentos.secuenciahandle.header.SECUENCIADEDOCUMENTONUEVO=SECUENCIA DE DOCUMENTO NUEVO
documentos.secuenciahandle.header.SECUENCIADEMODIFICACION=SECUENCIA DE MODIFICACION
documentos.secuenciahandle.header.SECUENCIADEREAPROBACION=SECUENCIA DE RE-APROBACI&Oacute;N
documentos.secuenciahandle.Irasolicitudrelacionada=Ir a solicitud relacionada
documentos.secuenciahandle.javascript.Agregarusuarioalasecuencia=Agregar usuario
documentos.secuenciahandle.javascript.Comentario=- Comentario
documentos.secuenciahandle.javascript.Eldocumentohasidoautorizado=El documento ha sido autorizado.
documentos.secuenciahandle.javascript.Eliminaratodaslasasignaciones=\u00a1Eliminar\\u00e1 todas las asignaciones!
documentos.secuenciahandle.javascript.Eliminarlaasignacion=\u00bfEliminar la asignaci\\u00f3n?
documentos.secuenciahandle.javascript.Fechalimite=- Fecha l\\u00edmite
documentos.secuenciahandle.javascript.LaFechaNoPuedeSerAyer=La fecha tiene que ser actual o futura. Por favor cambiela
documentos.secuenciahandle.javascript.Lasecuenciahasidodadadealta=La secuencia ha sido dada de alta.
documentos.secuenciahandle.javascript.LosDatosdelasecuenciahansidoactualizados=Los datos de la secuencia han sido actualizados.
documentos.secuenciahandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
documentos.secuenciahandle.javascript.Operacionexitosa=Operaci&oacute;n exitosa.
documentos.secuenciahandle.javascript.Seguro=\u00bfSeguro?
documentos.secuenciahandle.javascript.-Seleccioneunusuario=- Seleccione un usuario
documentos.secuenciahandle.javascript.Usuario=- Usuario
documentos.secuenciahandle.LASECUENCIAYAFUECOMPLETADA=LA SECUENCIA YA FUE COMPLETADA
documentos.secuenciahandle.NOAUTORIZADA=NO AUTORIZADA
documentos.secuenciahandle.NOAUTORIZO=NO AUTORIZO
documentos.secuenciahandle.Nombredeltemplate=Nombre del template:
documentos.secuenciahandle.Nosehanasignadousuarios=No se han asignado usuarios
documentos.secuenciahandle.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
documentos.secuenciahandle.Orden=Orden
documentos.secuenciahandle.PROGRAMADA=PROGRAMADA
documentos.secuenciahandle.Seactualizocorrectamentelacarpetadestino=Se actualiz\\u00f3 correctamente la carpeta destino.
documentos.secuenciahandle.SecuenciaActual=Secuencia Actual:
documentos.secuenciahandle.--Seleccione--=-- SELECCIONE --
documentos.secuenciahandle.SIAUTORIZO=SI AUTORIZO
documentos.secuenciahandle.Solicitudrelacionada=Solicitud relacionada:
documentos.secuenciahandle.Titulodeldocumento=T&iacute;tulo del documento:
documentos.secuenciahandle.Usarestasecuenciacomoplantilla=Usar esta secuencia como plantilla.
documentos.secuenciahandle.Usuario\:=Usuario:
documentos.secuenciahandle.Usuario=Usuario
documentos.secuencialist.ABIERTA=El estado actual es ABIERTA.
documentos.secuencialist.Atrasada=Atrasada.
documentos.secuencialist.AutorizanteSiguiente= Autorizante Siguiente
documentos.secuencialist.BorrarlaSecuencia=Borrar la Secuencia
documentos.secuencialist.CERRADA=El estado actual es CERRADA.
documentos.secuencialist.Clave=Clave
documentos.secuencialist.Clave=Clave
documentos.secuencialist.ClaveDelDocumento= Clave del Documento
documentos.secuencialist.Documento\:=Documento:
documentos.secuencialist.Documento=Documento
documentos.secuencialist.Estado\:=Estado:
documentos.secuencialist.Estado=Estado
documentos.secuencialist.FechaCreacion=Fecha Creaci&oacute;n
documentos.secuencialist.Fechafinaldecreacion=Fecha final de creaci&oacute;n:
documentos.secuencialist.Fechainiciodecreacion=Fecha inicio de creaci&oacute;n:
documentos.secuencialist.header.CONTROLDESECUENCIASDEAUTORIZACION=CONTROL DE SECUENCIAS DE AUTORIZACI&Oacute;N
documentos.secuencialist.javascript.Eldocumentohasidoautorizado=El documento ha sido autorizado.
documentos.secuencialist.javascript.Lasecuenciaseraborradadelsistema=\u00a1La secuencia ser\\u00e1 borrada del sistema!
documentos.secuencialist.javascript.Seguro=\u00bfSeguro?
documentos.secuencialist.LaSecuenciaNOsepuedeborrar=La Secuencia NO se puede borrar
documentos.secuencialist.LasecuenciasAbiertasnopuedenserborradas=La secuencias Abiertas no pueden ser borradas
documentos.secuencialist.Limite= L&iacute;mite
documentos.secuencialist.Noseencontraronsecuenciasconelestadoindicado= No se encontraron secuencias con el estado indicado 
documentos.secuencialist.Nosepudorealizarlaoperacionanterior=No se pudo realizar la operaci&oacute;n anterior
documentos.secuencialist.Sehaeliminadolasecuencia=Se ha eliminado la secuencia 
documentos.secuencialist.---Todos---=-- TODOS --
documentos.slideshow.de=de
documentos.slideshow.DesplegandoMedio=Desplegando Medio
documentos.solicitudeshandle.Actualizar=Actualizar
documentos.solicitudeshandle.Agregar=Agregar
documentos.solicitudeshandle.Archivo=Archivo:
documentos.solicitudeshandle.Archivosubido=Archivo subido:
documentos.solicitudeshandle.BajarArchivo=Bajar Archivo:
documentos.solicitudeshandle.Bajardocumento=Bajar documento
documentos.solicitudeshandle.boton.CancelarDocumento=Cancelar Documento
documentos.solicitudeshandle.boton.CrearSecuencia=Crear Secuencia
documentos.solicitudeshandle.boton.MarcarcomoAtendida=Marcar como Atendida
documentos.solicitudeshandle.boton.RechazarSolicitud=Rechazar Solicitud
documentos.solicitudeshandle.Cancelacion=Cancelaci&oacute;n
documentos.solicitudeshandle.ClavedelDocumento=Clave del Documento:
documentos.solicitudeshandle.DocumentoNuevo=Documento nuevo
documentos.solicitudeshandle.Edicion=Edici&oacute;n
documentos.solicitudeshandle.Fechadesolicitud=Fecha de solicitud:
documentos.solicitudeshandle.header.SOLICITUDDEEDICIONOMODIFICACIONDEDOCUMENTOS=SOLICITUD DE EDICI&Oacute;N O MODIFICACI&Oacute;N DE DOCUMENTOS
documentos.solicitudeshandle.header.SOLICITUDDEEDICIONOMODIFICACIONDEDOCUMENTOS-CLAVE=SOLICITUD DE EDICI&Oacute;N O MODIFICACI&Oacute;N DE DOCUMENTOS - CLAVE:
documentos.solicitudeshandle.javascript.Archivo=- Archivo
documentos.solicitudeshandle.javascript.Clavedeldocumento=- Clave del documento
documentos.solicitudeshandle.javascript.LaactualizaciondeldocumentohasidopuestaenesperadeRevision=La actualizaci&oacute;n del documento ha sido puesta en espera de Revisi&oacute;n.
documentos.solicitudeshandle.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
documentos.solicitudeshandle.javascript.NuevaSolicitud=Nueva Solicitud
documentos.solicitudeshandle.javascript.Razondesolicitud=- Raz\\u00f3n de solicitud
documentos.solicitudeshandle.javascript.Sehacanceladoeldocumento=Se ha cancelado el documento.
documentos.solicitudeshandle.javascript.Sehacanceladoeldocumentoymarcadolasolicitudcomoatendida=Se ha cancelado el documento y marcado la solicitud como atendida.
documentos.solicitudeshandle.javascript.Sehahecholasolicitudconexito=Se ha hecho la solicitud con &eacute;xito.
documentos.solicitudeshandle.javascript.Sehamarcadolasolicitudcomoatendida=Se ha marcado la solicitud como atendida.
documentos.solicitudeshandle.javascript.Titulodeldocumento=- T\\u00edtulo del documento
documentos.solicitudeshandle.javascript.VerSolicitudes=Ver Solicitudes
documentos.solicitudeshandle.Modificacion=Modificaci&oacute;n
documentos.solicitudeshandle.onosepudolocalizarelarchivoindicado=o no se pudo localizar el archivo indicado
documentos.solicitudeshandle.Personaquesolicita=Persona que solicita:
documentos.solicitudeshandle.RazondeSolicitud=Raz&oacute;n de Solicitud:
documentos.solicitudeshandle.RazonporlaquelaSolicitudfuerechazada=Raz&oacute;n por la que la Solicitud fue rechazada.
documentos.solicitudeshandle.Re-aprobacion=Re-aprobaci&oacute;n
documentos.solicitudeshandle.Rechazar=Rechazar
documentos.solicitudeshandle.Subirarchivo=Subir archivo:
documentos.solicitudeshandle.Tipodesolicitud=Tipo de solicitud:
documentos.solicitudeshandle.TitulodelDocumento=T&iacute;tulo del Documento:
documentos.solicitudeshandle.Yaexistepreviamenteelnombredelarchivoelegido=Ya existe previamente el nombre del archivo elegido
documentos.solicitudeshandle.Yaexisteunarchivoconelmismonombre=Ya existe un archivo con el mismo nombre
documentos.solicitudeshandle.Yaexisteunasolicitudhaciaesedocumento=Ya existe una solicitud hacia ese documento
documentos.solicitudeslist.Atendida=El estado actual es ATENDIDA.
documentos.solicitudeslist.Borrar=Borrar
documentos.solicitudeslist.Borrarsolicitud=Rechazar solicitud
documentos.solicitudeslist.BorrarSolicitud=Rechazar Solicitud
documentos.solicitudeslist.Busquedaporestado=B&uacute;squeda por estado:
documentos.solicitudeslist.Cerrada=El estado actual es CERRADA. Presione para Borrar.
documentos.solicitudeslist.Clave=Clave
documentos.solicitudeslist.Estado=Estado
documentos.solicitudeslist.Fecha=Fecha
documentos.solicitudeslist.Fechafin=Fecha fin
documentos.solicitudeslist.Fechainicio=Fecha inicio
documentos.solicitudeslist.header.LISTADODESOLICITUDES=LISTADO DE SOLICITUDES
documentos.solicitudeslist.javascript.Deseacerrarlasolicitud=\u00bfDesea cerrar la solicitud?
documentos.solicitudeslist.javascript.Lasolicitudseraborradadelsistema=\u00a1La solicitud ser\\u00e1 borrada del sistema!
documentos.solicitudeslist.Marcarcomocerrada=\ Presione para marcar como CERRADA.
documentos.solicitudeslist.Nosehaeliminadolasolicitud=No se ha eliminado la solicitud.
documentos.solicitudeslist.Sehaeliminadolasolicitud=Se ha eliminado la solicitud
documentos.solicitudeslist.Solicitada=El estado actual es SOLICITADA.
documentos.solicitudeslist.Solicitante=Solicitante
documentos.solicitudeslist.Tipo=Tipo
documentos.solicitudeslist.--TODOS--=-- TODOS --
documentos.solicitudeslist.Ver=Ver
documentos.solicitudeslist.Verlasolicitud=\ Presione para ver el detalle.
documentos.solicitudeslist.VerSolicitud=Ver Solicitud
documentos.subir.Porfavor,seleccioneeltrayectodelficheroacargar=Por favor, seleccione el trayecto del fichero a cargar
documentos.tiempoDeRetencion=Tiempo de Retenci\u00f3n
documentoshandle.Ver=Ver
documentospendientes.header.PendientesAcciones=Pendientes de Acciones
documentospendientes.header.PendientesAuditorias=Pendientes de Auditorias
documentospendientes.header.PendientesDocumento=Pendientes de Documentos
documentospendientes.header.PendientesIndicadores=Pendientes de Indicadores
documentospendientes.header.PendientesProyectos=Pendientes de Proyectos
documentosproyectos.listasdocumentos.aceptar=Aceptar
documentosproyectos.listasdocumentos.borrar=Borrar
documentosproyectos.listasdocumentos.esconder=Esconder
documentosproyectos.listasdocumentos.mover=Mover
encuesta.elvalQuestionEdit.Lossiguientescampossonnecesarios=Los siguientes campos son necesarios:
encuesta.elvalQuestionEdit.-RedactelaPregunta=   -Redacte la Pregunta.
error.ErrorNoSePuedeMostrarElTexto=Error: No se puede mostrar el texto
evaluacion.encuestas.Agregarcomentario=Agregar Comentario
evaluacion.encuestas.Anticipacionparamandaravisos=Anticipaci\u00f3n para mandar avisos:
evaluacion.encuestas.B\u00fasquedaavanzada=B\u00fasqueda Avanzada
evaluacion.encuestas.Cancelarencuesta=Cancelar encuesta
evaluacion.encuestas.Cerrarencuesta=Cerrar Encuesta
evaluacion.encuestas.Clave=Clave
evaluacion.encuestas.Cuestionariodeencuesta=Cuestionario de Encuesta.
evaluacion.encuestas.Cuestionariodeencuestas=Cuestionario de encuesta:
evaluacion.encuestas.Debeescojerparticipantes=Debe escojer participantes.
evaluacion.encuestas.Descripcion=Descripci\u00f3n
evaluacion.encuestas.ENCUESTA=ENCUESTA
evaluacion.encuestas.Fechadeinicio=Fecha de inicio:
evaluacion.encuestas.Fechafin=Fecha fin:
evaluacion.encuestas.Guardar=Guardar
evaluacion.encuestas.Horadeinicio=Hora de inicio:
evaluacion.encuestas.Horafin=Hora fin:
evaluacion.encuestas.lossiguientescampossonnecesarios=Los siguientes campos son necesarios:
evaluacion.encuestas.Participantesdelaencuesta=Participantes de la encuesta:
evaluacion.encuestas.Regresar=Regresar
evaluacion.encuestas.Regresaramisencuestas=Regresar a mis Encuestas
evaluacion.encuestas.Responsabledelaencuesta=Responsable de la encuesta
evaluacion.encuestas.SELECCIONE=--SELECCIONE--
evaluacion.encuestas.titulo=T\u00edtulo:
evaluacion.encuestas.Tituloencuesta=T&iacute;tulo:
evaluacion.encuestas.Vercuestionario=Ver Cuestionario
evaluacion.encuestas.Verifiquequelahoradeinicioseamenoralahoradefin=Verifique que la hora de inicio sea menor a la hora de fin.
evaluaciones.ecuestas.responsabledelaencuesta=Responsable de la Encuesta
evaluaciones.encuestas.titulo=T\u00edtulo:
evaluaciones.encuestashandle.Agregar/QuitarParticipantes=Agregar/Quitar Participantes
evaluaciones.encuestashandle.AgregarComentario=Agregar Comentario
evaluaciones.encuestashandle.Anticipacionparamandaravisos=Anticipacion para mandar avisos:
evaluaciones.encuestashandle.CancelarEncuesta=Cancelar Encuesta
evaluaciones.encuestashandle.CerrarEncuesta=Cerrar Encuesta
evaluaciones.encuestashandle.Clave=Clave:
evaluaciones.encuestashandle.Comentario=Comentario:
evaluaciones.encuestashandle.Comentarios=Comentarios:
evaluaciones.encuestashandle.ComentarioSolo=Comentario
evaluaciones.encuestashandle.Consultar=Consultar
evaluaciones.encuestashandle.CuestionarioActivo=Cuestionario Activo:
evaluaciones.encuestashandle.-CuestionariodeEncuesta=- Cuestionario de Encuesta.
evaluaciones.encuestashandle.CuestionariodeEncuesta=Cuestionario de Encuesta
evaluaciones.encuestashandle.CUESTIONARIODEENCUESTA=CUESTIONARIO DE ENCUESTA
evaluaciones.encuestashandle.-Debeescpjerparticipantes=- Debe escojer participantes.
evaluaciones.encuestashandle.Debesescribiruncomentario=Debes escribir un comentario.
evaluaciones.encuestashandle.-Descripcion=- Descripci\\u00f3n.
evaluaciones.encuestashandle.Descripcion=Descripci&oacute;n:
evaluaciones.encuestashandle.DETALLESDELAENCUESTA=DETALLES DE LA ENCUESTA
evaluaciones.encuestashandle.Dias=d&iacute;a(s)
evaluaciones.encuestashandle.Editarcuestionario=Editar Cuestionario
evaluaciones.encuestashandle.EditarSecciones=Editar Secciones:
evaluaciones.encuestashandle.Elcomentariohasidograbado=El comentario ha sido grabado.
evaluaciones.encuestashandle.Elnumerodepreguntasnopuedesercero=El N\u00famero de Preguntas no puede ser Cero
evaluaciones.encuestashandle.Elnumerodeseccionesnopuedesercero=El N\u00famero de Secciones no puede ser Cero
evaluaciones.encuestashandle.ENCUESTA=ENCUESTA
evaluaciones.encuestashandle.Estado=Estado:
evaluaciones.encuestashandle.Estasegurodequedeseamodificarlosdatosdelaencuesta=Esta seguro de que desea modificar los datos de la encuesta.
evaluaciones.encuestashandle.Fecha=Fecha
evaluaciones.encuestashandle.-Fechadeinicio=- Fecha de inicio.
evaluaciones.encuestashandle.-Fechafin=- Fecha fin.
evaluaciones.encuestashandle.Fechafin=Fecha fin:
evaluaciones.encuestashandle.FechaInicio=Fecha Inicio:
evaluaciones.encuestashandle.Guardar=Guardar
evaluaciones.encuestashandle.Horadeinicio=Hora de inicio:
evaluaciones.encuestashandle.Horafin=Hora fin:
evaluaciones.encuestashandle.Laencuestahasidocancelada=La encuesta ha sido cancelada.
evaluaciones.encuestashandle.Laencuestaseguardocorrectamente=La encuesta se guard&oacute; correctamente.
evaluaciones.encuestashandle.Laencuestasehacerrado=La encuesta se ha cerrado.
evaluaciones.encuestashandle.Lafechainiicaldebesermenoroigualalafechafinal=La fecha inicial debe ser menor o igual a la fecha final.
evaluaciones.encuestashandle.Lossiguientescampossonnecesarios=Los siguientes campos son necesarios:
evaluaciones.encuestashandle.Participantesdelaencuesta=Participantes de la Encuesta:
evaluaciones.encuestashandle.ParticipantesdelaEncuesta=Participantes de la Encuesta:
evaluaciones.encuestashandle.Raz\u00f3nporlacuallaencuestaser\u00e1cancelada=Raz\u00f3n por la cual la encuesta ser\u00e1 cancelada.
evaluaciones.encuestashandle.Razonporlacuallaencuestaseracancelada=Raz&oacute;n por la cual la encuesta ser&aacute; cancelada
evaluaciones.encuestashandle.Regresar=Regresar
evaluaciones.encuestashandle.Regresar=Regresar
evaluaciones.encuestashandle.-Responsabledelaencuesta=- Responsable de la encuesta.
evaluaciones.encuestashandle.Responsabledelaencuesta=Responsable de la encuesta:
evaluaciones.encuestashandle.Seccion=Secci\u00f3n
evaluaciones.encuestashandle.--SELECCIONE--=--SELECCIONE--
evaluaciones.encuestashandle.-Titulo=- T\\u00edtulo.
evaluaciones.encuestashandle.Titulo=T&iacute;tulo:
evaluaciones.encuestashandle.Usuarios=Usuarios
evaluaciones.encuestashandle.Ver=Ver
evaluaciones.encuestashandle.Verifiquequelafechaseaposterioroigualaldiadehoy=Verifique que la fecha sea posterior o igual al dia de hoy.
evaluaciones.encuestashandle.Verifiquequelafechasseaposterioroigualaldiadehoy=Verifique que la fecha(s) sea posterior o igual a la dia del hoy.
evaluaciones.encuestashandle.-Verifiquequelahoradeiniicoseamenoralahoradefin=- Verifique que la hora de inicio sea menor a la hora de fin.
evaluaciones.encuestaslist.Activa,porresponder=Activa, por responder
evaluaciones.encuestaslist.Activa=Activa
evaluaciones.encuestaslist.Autor=Autor:
evaluaciones.encuestaslist.Buscar=Buscar
evaluaciones.encuestaslist.Busqueda=B&uacute;squeda
evaluaciones.encuestaslist.BusquedaAvanzada=B&uacute;squeda Avanzada
evaluaciones.encuestaslist.Cerrada=Cerrada
evaluaciones.encuestaslist.-CLAVE-=- CLAVE -
evaluaciones.encuestaslist.Clave=Clave
evaluaciones.encuestaslist.ContestarEncuesta=Contestar Encuesta
evaluaciones.encuestaslist.Contestarencuesta=Contestar Encuesta
evaluaciones.encuestaslist.Cuestionario=Cuestionario
evaluaciones.encuestaslist.CuestionarioPuntos=Cuestionario:
evaluaciones.encuestaslist.Encuestasactivasenlasqueparticipa=Encuestas activas en las que participa:
evaluaciones.encuestaslist.Encuestasprogramadasenlasqueparticipa=Encuestas programadas en las que participa:
evaluaciones.encuestaslist.Estado=Estado:
evaluaciones.encuestaslist.EstadoPorCerrar=Estado: Por Cerrar
evaluaciones.encuestaslist.FechaInicio=Fecha de Inicio
evaluaciones.encuestaslist.Fechainicio=Fecha inicio
evaluaciones.encuestaslist.Fechatermino=Fecha fin
evaluaciones.encuestaslist.FechaTermino=Fecha fin:
evaluaciones.encuestaslist.LISTADO=LISTADO
evaluaciones.encuestaslist.LISTADODEENCUESTASCREADAS=CONTROL DE ENCUESTAS
evaluaciones.encuestaslist.LISTADODEENCUESTASCREADASPORUSUARIO=CONTROL DE ENCUESTAS CREADAS POR USUARIO
evaluaciones.encuestaslist.MISENCUESTAS=MIS ENCUESTAS
evaluaciones.encuestaslist.Nombreencuesta=Nombre Encuesta
evaluaciones.encuestaslist.Nosepudoborrarelregistro= No se pudo borrar el registro.
evaluaciones.encuestaslist.Presioneparaverlainformaciondelaencuesta=Presione para ver la informacion de la encuesta
evaluaciones.encuestaslist.Programada=Programada
evaluaciones.encuestaslist.Respondercuestionario=Responder cuestionario
evaluaciones.encuestaslist.Respondida=Respondida
evaluaciones.encuestaslist.Seborroelregistro=Se borr&oacute; el registro.
evaluaciones.encuestaslist.Seborroelregistro1=Se borro el registro.
evaluaciones.encuestaslist.---SELECCIONE---=--- SELECCIONE ---
evaluaciones.encuestaslist.Terminada=Terminada
evaluaciones.encuestaslist.Titulo=T&iacute;tulo
evaluaciones.encuestaslist.Tituloencuesta=T&iacute;tulo encuesta
evaluaciones.encuestaslist.TituloEncuesta=T&iacute;tulo Encuesta:
evaluaciones.encuestaslist.---TODOS---=--- TODOS ---
evaluaciones.encuestaslist.Usuarioencuestado=Usuario encuestado
evaluaciones.encuestaslist.UsuarioEncuestado=Usuario Encuestado:
evaluaciones.evalEdit.EDITARCUESTIONARIO=EDITAR CUESTIONARIO
evaluaciones.evalEdit.LISTADODEPREGUNTAS=LISTADO DE PREGUNTAS
evaluaciones.evalOptionCreate.Dependientes=Dependientes
evaluaciones.evalOptionCreate.Opcion=Opci\u00f3n
evaluaciones.evalOptionCreate.Opciones=Opciones
evaluaciones.evalOptionCreate.Ponderacion=Ponderaci\u00f3n
evaluaciones.evaluacioneshandle.Espereaquesecargenlassecciones=Espere a que se carguen las Secciones
evaluaciones.evaluacioneshandle.Ocurrioelsiguienteerror=Ocurrio el Siguiente Error
evaluaciones.evaluacioneshandle.REDACCI\u00d3NDEENCUESTAS=REDACCI\u00d3N DE ENCUESTAS
evaluaciones.evaluacionlist.FechadeModificacion=Fecha de Modificaci\u00f3n:
evaluaciones.evaluacionlist.Secambioelestadodelcuestionario= Se cambi&oacute; el estado del cuestionario
evaluaciones.questionCreate.Crealaspreguntasdelaevaluaci\u00f3n=Crea las preguntas de la evaluaci\u00f3n
evaluaciones.questionCreate.CREARCUESTIONARIO=CREAR CUESTIONARIO
evaluaciones.questionCreate.Esobligatoria=Es obligatoria:
evaluaciones.questionCreate.Guardarycontinuar=Guardar y continuar
evaluaciones.questionCreate.Hasterminadolacreaci\u00f3ndelcuestionario=Has terminado la creaci\u00f3n del cuestionario.
evaluaciones.questionCreate.Lacantidaddeopcionesnopuedeser0oestarenblanco=La cantidad de opciones no puede ser 0 o estar en blanco.
evaluaciones.questionCreate.Numerodeopciones=N\u00famero de opciones:
evaluaciones.questionCreate.Tipodepregunta=Tipo de pregunta:
evaluaciones.reporteEncuestas.(Laencuestanoesponderada)=(La encuesta no es ponderada)
evaluaciones.reporteEncuestas.*Laencuestagraficadaesunexamen=*La encuesta graficada es un examen
evaluaciones.reporteEncuestas.*Laencuestagraficadanocuentaconponderacion=*La encuesta graficada no cuenta con ponderaci&oacute;n
evaluaciones.reporteEncuestas.*Laspreguntasnograficadassonpreguntasabiertas,camposdefechasyhora=*Las preguntas no graficadas son preguntas abiertas, campos de fechas y hora.
evaluaciones.reporteEncuestas.*Solosemuestranencuestasenestatusterminada=*Solo se muestran encuestas en estatus terminada
evaluaciones.reporteEncuestas.Encuesta*=Encuesta:
evaluaciones.reporteEncuestas.Encuesta=Encuesta
evaluaciones.reporteEncuestas.Filtrodeparticipantes=Filtro de participantes:
evaluaciones.reporteEncuestas.Grafica=Gr&aacute;fica
evaluaciones.reporteEncuestas.Graficarpor=Graficar por:
evaluaciones.reporteEncuestas.Graficarporcentajes=Graficar porcentajes:
evaluaciones.reporteEncuestas.Graficarresultadospor=Graficar resultados por:
evaluaciones.reporteEncuestas.REPORTEDEENCUESTAS=REPORTE DE ENCUESTAS
evaluaciones.reporteEncuestas.--SELECCIONE--=-- SELECCIONE --
evaluaciones.reporteEncuestas.TipodeReporte=Tipo de Reporte:
evaluaciones.reporteEncuestas.--TODO--=-- TODO --
evaluaciones/questionCreate.Haselegidoelformatodeevaluaci\u00f3ndetipoMatrizporloquedebesprimeroseleccionar<br/>cuantasopcionestrendr\u00e1nlaspreguntas=Has elegido el formato de evaluaci\u00f3n de tipo Matriz por lo que debes primero seleccionar<br/> cuantas opciones trendr\u00e1n las preguntas.
grafica.grafica=Mostrar opciones
grafica.graficaAvanzada.esconder=Esconder
grafica.graficaAvanzada.mostrar=Mostrar
grafica.graficaAvanzada=Gr&aacute;fica avanzada
gridGenerator.Busqueda=B&uacute;squeda
gridGenerator.Mostrarfiltrodebusqueda=Mostrar filtro de busqueda
gridGenerator.Ocultarfiltrodebusqueda=Ocultar filtro de busqueda
iconos.GraficaGantt=Gr&aacute;fica de Gantt
iconos.ReporteAvance=Reporte de Avance
iconos.ReporteCambios=Reporte de Cambios
iconos.ReporteGraficos=Reportes y Gr&aacute;ficos
iconos.ReportePeticionesPresupuesto=Reporte de Peticiones de Presupuesto
iconos.Text.GraficaGantt=Gantt
iconos.Text.ReporteAvance=Avance
iconos.Text.ReporteCambios=Cambios
iconos.Text.ReportePeticionesPresupuesto=Peticiones de Presupuesto
include.messagewindow.Aceptar=Aceptar
include.messagewindow.Cancelar=Cancelar
include.messagewindow.Mensajedelsistema=Mensaje del sistema
includes.mensajedeespera.alt=Procesando...
includes.mensajedeespera.mensajeDeEspera=La informaci&oacute;n est&aacute; siendo procesada, <br>espere un momento por favor.
includes.mensajedeespera.titulo=Procesando informaci&oacute;n.
indicador.indicadorhandle.ElijeAlmenosUnAreaALaCualReportar=-Elije almenos un area a la cual reportar
indicador.levantamientoindicadorhandle.EsteIndicadorEsReportadoPorCarreraALaCarrera=Este indicador es reportado al &aacute;rea de:
indicador.levantamientoindicadorhandle.fechaLimiteParaLaRevision=Fecha L&iacute;mite Para la Revis&oacute;n:
indicador.misindicadores.indicadoresPorRegistrar=Indicadores por registrar:
indicador.objetivolist.controlDeObjetivosDeCalidad=CONTROL DE OBJETIVOS DE CALIDAD
indicadores.indicadorespendientes.IndicadoresPorCalificar = Indicadores por Calificar:
indicadores.indicadorespendientes.IndicadoresPorVerificar = Indicadores por Revisar:
indicadores.indicadoresreportegeneral.(CadaPeriodoGraficadoRepresentaUnaRevisionConSuRespectivaMeta)=(Cada periodo graficado representa una revisi&oacute;n con su respectiva meta)
indicadores.indicadoresreportegeneral.boton.Limpiar=Limpiar
indicadores.indicadoresreportegeneral.departamento=Departamento:
indicadores.indicadoresreportegeneral.detalles=Detalles:
indicadores.indicadoresreportegeneral.Facultad=Unidad productiva:
indicadores.indicadoresreportegeneral.Facultad=Unidad productiva:
indicadores.indicadoresreportegeneral.Faltandatosporcapturar=Faltan datos por capturar.
indicadores.indicadoresreportegeneral.FechaMenorIgualFechaFinal = - La fecha inicial debe de ser menor \\u00f3 igual a la fecha final.\\n
indicadores.indicadoresreportegeneral.Fecha=Fecha inicial:
indicadores.indicadoresreportegeneral.header=REPORTE GENERAL DE INDICADORES
indicadores.indicadoresreportegeneral.IndicadorAGraficar=Indicador a graficar:
indicadores.indicadoresreportegeneral.Objetivo=Objetivo:
indicadores.indicadoresreportegeneral.proceso=Proceso:
indicadores.indicadoresreportegeneral.reportarPor=Reportar por:
indicadores.indicadoresreportegeneral.ReportarPorPeriodosDeRevision=Reportar por periodos de revision:
indicadores.indicadoresreportegeneral.--Seleccione--=-- SELECCIONE --
indicadores.indicadoresreportegeneral.Tipodegrafica=Tipo de gr&aacute;fica:
indicadores.indicadoresreportegeneral.--TODAS--=-- TODOS --
indicadores.indicadoresreportegeneral.---TODAS---=----TODAS---
indicadores.indicadoresreportegeneral.unidad=&Aacute;reas:
indicadores.indicadorhandle.AsignarResponsableNecesario = - Debe asignar al menos un responsable.\\n
indicadores.indicadorhandle.AsignarResponsableUnidadNecesario = - Debe asignar al menos un \\u00e1rea.\\n
indicadores.indicadorhandle.Cada=Cada
indicadores.indicadorhandle.CuantosMesesRevisionNecesario = - Debe indicar cada cuantos meses en la revisi\\u00f3n.\\n
indicadores.indicadorhandle.CuantosMesNecesario = - Debe indicar cada cuantos meses.\\n
indicadores.indicadorhandle.CuantosMesRevisionNecesario = - Debe indicar cada cuantos meses.\\n
indicadores.indicadorhandle.Cuarto=Cuarto
indicadores.indicadorhandle.DatosNecesarios = Los siguientes datos son necesarios: \\n
indicadores.indicadorhandle.decada=de cada
indicadores.indicadorhandle.limpiarCampos = \u00bfEst\u00e1 seguro de que desea limpiar los campos?
indicadores.indicadorhandle.Cancelar = Ser\u00e1 enviado a la pantalla de control de indicadores y cualquier cambio no guardado se perder\u00e1 <br>\u00bfSeguro de que desea continuar?
indicadores.indicadorhandle.DepartamentoEngargadoRevisionesNecesario = - Debe asignar un departamento encargado de revisiones.\\n
indicadores.indicadorhandle.DepartamentoNecesario = - Debe asignar un departamento.\\n
indicadores.indicadorhandle.DepartamentoPeriodicidadRevisionesNecesario = - Debe seleccionar al menos un d\\u00eda de la semana en la revisi\\u00f3n.\\n
indicadores.indicadorhandle.DepartamentoProcesoNecesario = - Es necesario especificar un departamento.\\n
indicadores.indicadorhandle.DiaMesNecesario = - Debe indicar que d\\u00eda del mes.\\n
indicadores.indicadorhandle.DiaNecesario = - Debe seleccionar al menos un d\\u00eda de la semana.\\n
indicadores.indicadorhandle.dias=d&iacute;as
indicadores.indicadorhandle.DiasAnticipacionNecesario = - Debe asignar los d\\u00edas de anticipaci\\u00f3n.\\n
indicadores.indicadorhandle.Domingo=Domingo
indicadores.indicadorhandle.El=El
indicadores.indicadorhandle.Eldia=El d&iacute;a
indicadores.indicadorhandle.-estemensajenodebemostrarse-= -este mensaje no debe mostrarse-
indicadores.indicadorhandle.FechaLevanmientoVerificar =- Verificar fechas:\\n
indicadores.indicadorhandle.FechaLevanmientoVerificarRevision = - Debe proporcionar una fecha para el primera revisi\\u00f3n.\\n
indicadores.indicadorhandle.FechaMenorIgualRevisionVerificar = - La fecha de levantamiento debe ser menor \\u00f3 igual a la fecha de revisi\\u00f3n.\\n
indicadores.indicadorhandle.FechaPosteriorIgualLevanmientoVerificar = - Verifique que las fecha del levantamiento sea posterior o igual a la d\\u00eda del hoy.\\n
indicadores.indicadorhandle.FechaPosteriorIgualRevisionVerificar = - Verifique que las fecha de revisi\\u00f3n sea posterior o igual a la d\\u00eda del hoy.\\n
indicadores.indicadorhandle.FechaPrimerLevanmientoVerificar = - Debe proporcionar una fecha para el primer levantamiento.\\n
indicadores.indicadorhandle.FormulaNecesario = - Debe escribir una f\\u00f3rmula.\\n
indicadores.indicadorhandle.FuenteInformacionNecesario = - Debe asignar una fuente de informaci\\u00f3n para el indicador.\\n
indicadores.indicadorhandle.IndicadorActivo = Activo
indicadores.indicadorhandle.IndicadorActualizado = El indicador se ha actualizado correctamente.
indicadores.indicadorhandle.IndicadorAlta = El indicador se ha dado de alta correctamente.
indicadores.indicadorhandle.IndicadorDepartamento = Departamento:
indicadores.indicadorhandle.IndicadorDescripcionMeta = Descripci&oacute;n de la Meta:
indicadores.indicadorhandle.IndicadorDiaria = Diaria
indicadores.indicadorhandle.IndicadorDiasAnticipacionProgramar = D&iacute;as de Anticipaci&oacute;n para programar:
indicadores.indicadorhandle.IndicadorDptoEncargadoRevisiones = Departamento Encargado de Revisiones:
indicadores.indicadorhandle.INDICADORES=INDICADORES
indicadores.indicadorhandle.IndicadorEstatus = Estatus del indicador:
indicadores.indicadorhandle.IndicadorFacultad = ${Facility}:
indicadores.indicadorhandle.IndicadorFechaPrimeraRevision = Fecha para la Primera Revisi&oacute;n:
indicadores.indicadorhandle.IndicadorFormula = F&oacute;rmula:
indicadores.indicadorhandle.IndicadorFuenteInformacion = Fuente de Informaci&oacute;n:
indicadores.indicadorhandle.IndicadorMaximo = M&aacute;ximo
indicadores.indicadorhandle.IndicadorMensual = Mensual
indicadores.indicadorhandle.IndicadorMeta = Meta:
indicadores.indicadorhandle.IndicadorMinimo = M&iacute;nimo
indicadores.indicadorhandle.IndicadorObjetivo = Objetivo:
indicadores.indicadorhandle.IndicadorPeriodicidad = Periodicidad:
indicadores.indicadorhandle.IndicadorPeriodicidadRevision = Periodicidad de Revisi&oacute;n:
indicadores.indicadorhandle.IndicadorPorDias = Diaria
indicadores.indicadorhandle.IndicadorPorMes = Mensual
indicadores.indicadorhandle.IndicadorPorPromedio = Por promedio
indicadores.indicadorhandle.IndicadorPorSemanas = Semanal
indicadores.indicadorhandle.IndicadorPorSumatoria = Por sumatoria
indicadores.indicadorhandle.IndicadorPrimerLevantamiento = Fecha para la primera captura:
indicadores.indicadorhandle.IndicadorProceso = Proceso:
indicadores.indicadorhandle.IndicadorReportarPor = Reportar por:
indicadores.indicadorhandle.IndicadorResponsable = Responsable
indicadores.indicadorhandle.IndicadorResponsableReportar = Responsable de reportar:
indicadores.indicadorhandle.IndicadorSelecciona = --SELECCIONA--
indicadores.indicadorhandle.IndicadorSemanal = Semanal
indicadores.indicadorhandle.IndicadorTipoAgrupamiento = Tipo de agrupamiento para revisiones y reportes:
indicadores.indicadorhandle.IndicadorTitulo = T&iacute;tulo del indicador:
indicadores.indicadorhandle.IndicadorUnidad= &Aacute;rea
indicadores.indicadorhandle.IndicadorUnidadesReportar = &Aacute;reas a reportar:
indicadores.indicadorhandle.AdvertenciaCampoNoEditable = Este campo solo se puede editar cuando el Indicador este en estado inactivo.
indicadores.indicadorhandle.Jueves=Jueves
indicadores.indicadorhandle.Lunes=Lunes
indicadores.indicadorhandle.Martes=Martes
indicadores.indicadorhandle.mes(es)=mes(es)
indicadores.indicadorhandle.MetaNecesario = - Debe escribir una meta m\\u00ednima o m\\u00e1xima.\\n
indicadores.indicadorhandle.Miercoles=Mi&eacute;rcoles
indicadores.indicadorhandle.ObjetivoNecesario = - Debe asignar un objetivo.\\n
indicadores.indicadorhandle.Primer=Primer
indicadores.indicadorhandle.ProcesoNecesario = - Debe asignar un proceso.\\n
indicadores.indicadorhandle.Perioricidad= - Debe por lo menos seleccionar un dia en el campo "Periodicidad".\\n
indicadores.indicadorhandle.PerioricidadDeRevision= - Debe por lo menos seleccionar un dia en el campo "Periodicidad de revisi\u00f3n".\\n
indicadores.indicadorhandle.RevisionDiaDelMesNecesario = - Debe indicar que d\\u00eda del mes de la revisi\\u00f3n.\\n
indicadores.indicadorhandle.Sabado=S&aacute;bado
indicadores.indicadorhandle.Segundo=Segundo
indicadores.indicadorhandle.semanaslos=semanas los:
indicadores.indicadorhandle.Tercer=Tercer
indicadores.indicadorhandle.TituloNecesario = - Debe asignarle un t\\u00edtulo al indicador.\\n
indicadores.indicadorhandle.Ultimo=&Uacute;ltimo
indicadores.indicadorhandle.valorSeparacionNecesario = - Debe de agregar algun valor para la separaci\\u00f3n\\n.
indicadores.indicadorhandle.valorSeparacionRevisionNecesario = - Debe de agregar algun valor para la separaci\\u00f3n de la revisi\\u00f3n.\\n
indicadores.indicadorhandle.Viernes=Viernes
indicadores.indicadorlist.Activo=Activo
indicadores.indicadorlist.Aunnohayindicadorescapturados=A&uacute;n no hay indicadores capturados.
indicadores.indicadorlist.Borrar =Borrar
indicadores.indicadorlist.ControlIndicadores =CONTROL DE INDICADORES
indicadores.indicadorlist.Editar =Editar
indicadores.indicadorlist.Estado =Estado
indicadores.indicadorlist.Inactivo=Inactivo
indicadores.indicadorlist.Indicador =Indicador
indicadores.indicadorlist.MensajeEliminar =Se eliminar\\u00e1 el indicador y ya no habr\\u00e1 manera de recuperarlo. \u00bfDesea realizar esta acci\\u00f3n?
indicadores.indicadorlist.Monitoreo =Monitoreo
indicadores.indicadorlist.Objetivo =Objetivo
indicadores.indicadorlist.Proceso =Proceso
indicadores.indicadorlist.Seeliminaraelindicadoryyanohabramaneraderecuperarlo.\u00bfDesearealizarestaaccion?=Se eliminar\\u00e1 el indicador y ya no habr\\u00e1 manera de recuperarlo. \u00bfDesea realizar esta acci\\u00f3n?
indicadores.indicadorregistroslist.Borrar =Borrar
indicadores.indicadorregistroslist.ControlRegistros =CONTROL DE REGISTROS
indicadores.indicadorregistroslist.Estado =Estado
indicadores.indicadorregistroslist.EstadoCALIFICADA=Estado: CALIFICADA.
indicadores.indicadorregistroslist.EstadoENESPERA,calificarloslevantamientos=Estado: EN ESPERA, calificar los levantamientos.
indicadores.indicadorregistroslist.EstadopendientePORREVISAR=Estado: pendiente POR REVISAR.
indicadores.indicadorregistroslist.EstadoPORCALIFICAR=Estado: POR CALIFICAR.
indicadores.indicadorregistroslist.EstadoPROGRAMADA,aunnollegalafechaprogramada=Estado: PROGRAMADA, aun no llega la fecha por revisar.
indicadores.indicadorregistroslist.EstadoREVISADA=Estado: REVISADA.
indicadores.indicadorregistroslist.Fecha =Fecha
indicadores.indicadorregistroslist.Indicador =Indicador
indicadores.indicadorregistroslist.IndicadoresNoProgramados =No hay indicadores programados.
indicadores.indicadorregistroslist.IndicadoresNoRevision =No hay revisiones de indicadores programadas.
indicadores.indicadorregistroslist.LevantamientoIndicadores =Levantamientos de Indicadores:
indicadores.indicadorregistroslist.LevantamientosdeIndicador=Levantamientos de Indicador:
indicadores.indicadorregistroslist.MensajeEliminar =Se eliminar\\u00e1 el levantamiento del indicador y ya no habr\\u00e1 manera de recuperarlo. \u00bfDesea realizar esta acci\\u00f3n?
indicadores.indicadorregistroslist.MensajeRevisionEliminar =Se eliminar\\u00e1 la revisi\\u00f3n del indicador y ya no habr\\u00e1 manera de recuperarla. \u00bfDesea realizar esta acci\\u00f3n?
indicadores.indicadorregistroslist.Nohayindicadoresprogramados=No hay indicadores programados.
indicadores.indicadorregistroslist.Proceso =Proceso
indicadores.indicadorregistroslist.RevisionBorrar =Borrar
indicadores.indicadorregistroslist.Revisionesdeindicadores=Revisiones de indicadores:
indicadores.indicadorregistroslist.RevisionEstado =Estado
indicadores.indicadorregistroslist.RevisionFecha =Fecha
indicadores.indicadorregistroslist.RevisionIndicador =Indicador
indicadores.indicadorregistroslist.RevisionIndicadores =Revisiones de indicadores:
indicadores.indicadorregistroslist.RevisionProceso =Proceso
indicadores.indicadorregistroslist.RevisionUsuarioMonitoreo =Monitoreo
indicadores.indicadorregistroslist.RevisionVer =Ver
indicadores.indicadorregistroslist.Seeliminaraellevantamientodelindicadoryyanohabramaneraderecuperarlo=Se eliminar\\u00e1 el levantamiento del indicador y ya no habr\\u00e1 manera de recuperarlo. \u00bfDesea realizar esta acci\\u00f3n?
indicadores.indicadorregistroslist.Seeliminaralarevisiondelindicadoryyanohabramaneraderecuperarla.\u00bfDesearealizarestaaccion?=Se eliminar\\u00e1 la revisi\\u00f3n del indicador y ya no habr\\u00e1 manera de recuperarla. \u00bfDesea realizar esta acci\\u00f3n?
indicadores.indicadorregistroslist.UsuarioCarrera =Usuario/${Area}
indicadores.indicadorregistroslist.Ver =Ver
indicadores.levantamientoindicadorhandle.*Fechadelevantamiento=*Fecha de levantamiento.
indicadores.levantamientoindicadorhandle.*Resultadodelevantamiento=*Resultado de levantamiento.
indicadores.levantamientoindicadorhandle.Aceptar=Aceptar
indicadores.levantamientoindicadorhandle.Elindicadorsehacalificadocorrectamente=El indicador se ha calificado correctamente.
indicadores.levantamientoindicadorhandle.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
indicadores.misindicadores.carrera=&Aacute;rea
indicadores.misindicadores.estado=Estado
indicadores.misindicadores.fecha=Fecha
indicadores.misindicadores.indicador=Indicador
indicadores.misindicadores.IndicadoresPorRegistrar:Indicadores por registrar:
indicadores.misindicadores.Indicadoresporrevisar=Indicadores por revisar:
indicadores.misindicadores.misIndicadores=MIS INDICADORES
indicadores.misindicadores.NoTieneIndicadoresProgramados=No tiene indicadores programados.
indicadores.misindicadores.NoTieneRevisionesDeIndicadoresProgramadas= No tiene revisiones de indicadores programadas.
indicadores.misindicadores.proceso=Proceso
indicadores.misindicadores.ver=Ver
indicadores.objetivohandle.Descripcion=Descripci&oacute;n:
indicadores.objetivohandle.Cancelar=Se perderan los cambios no guardados. \u00bfEst\u00e1 seguro de cancelar?
indicadores.objetivohandle.Elobjetivohasidocreado=El objetivo ha sido creado.
indicadores.objetivohandle.Losdatosdelobjetivohansidoactualizados=Los datos del objetivo han sido actualizados.
indicadores.objetivohandle.OBJETIVOS=OBJETIVOS
indicadores.objetivohandle.Titulo=T&iacute;tulo:
indicadores.objetivolist.Activo=Activo
indicadores.objetivolist.Borrar =Borrar
indicadores.objetivolist.Estado =Estado
indicadores.objetivolist.NoHayObjetivosCapturados =A&uacute;n no hay objetivos capturados.
indicadores.objetivolist.Objetivo =Objetivo
indicadores.objetivolist.Seeliminaraelobjetivoyyanohabramaneraderecuperarlo.\u00bfDesearealizarestaaccion?=Se eliminar\\u00e1 el objetivo y ya no habr\\u00e1 manera de recuperarlo. \u00bfDesea realizar esta acci\\u00f3n?
indicadores.objetivolist.Ver =Ver
indicadores.revisionindicadorhandle.*Daracciondealta=*Dar acci&oacute;n de alta
indicadores.revisionindicadorhandle.Aceptar=Aceptar
indicadores.revisionindicadorhandle.AunNoSeCumpleLaFechaDeRevisionFavorDeEsperarALaFechaIndicada=A&uacute;n no se cumple la fecha de revisi&oacute;n, favor de esperar a la fecha indicada.
indicadores.revisionindicadorhandle.Calificado=Calificado
indicadores.revisionindicadorhandle.Comentarios=Comentarios:
indicadores.revisionindicadorhandle.-Debeagregaruncomentario=- Debe agregar un comentario.
indicadores.revisionindicadorhandle.Elcomentariodelindicadorasidoactualizadocorrectamente=El comentario del indicador a sido actualizado correctamente.
indicadores.revisionindicadorhandle.EsteIndicadorNoHaSidoCalificadoPorTodosLosResponsables=Este indicador no ha sido calificado por todos los responsables.
indicadores.revisionindicadorhandle.Larevisionindicadorsehaactualizadocorrectamente=La revisi&oacute;n del indicador se ha actualizado correctamente.
indicadores.revisionindicadorhandle.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios:
indicadores.revisionindicadorhandle.NoHayIndicadoresProgramadosParaEstePeriodo=No hay indicadores programados para este periodo.
indicadores.revisionindicadorhandle.IndicadorNoCalificado=Este indicador no ha sido calificado.
indicadores.revisionindicadorhandle.PorCalificar=Por Calificar
indicadores.revisionindicadorhandle.Programado=Programado
indicadores.revisionindicadorhandle.Responsablesdelindicador=Responsables del indicador:
indicadores.revisionindicadorhandle.Revisado=Revisado
indicadores.revisionindicadorhandle.Verdetallederegistros=Ver detalle de registros
isoblock.common.database.TipoDeObjeto=Tipo de Objeto
isoblock.documentos.documentojava.Nohaylectores=No hay lectores.
isoblock.Proyectos.Actividad.Concluida=Concluida
isoblock.Proyectos.Actividad.Creada=Creada
isoblock.Proyectos.Actividad.EnProceso=En Proceso
isoblock.Proyectos.Actividad.EstadoDesconocido=Estado Desconocido
isoblock.Proyectos.Actividad.Verificada=Verificada
isoblock.scorecards.selectorEntidad.Ocultar=Ocultar
login.login.\u00bfNopuedesaccederatucuenta?=\u00bfNo puedes acceder a tu cuenta?
login.login.\u00bfQueimpidequepuedasaccederatucuenta?=\u00bfQu&eacute; impide que puedas acceder a tu cuenta?
login.login.1.Tecleaquitunombredeusuario=1. Teclea aqu&iacute; t&uacute; nombre de usuario.
login.login.1.Tqcleaaquitudirecciondecorreoelectronico=1. Teclea aqu&iacute; tu direcci&oacute;n de correo electronico.
login.login.2.boton.Enviar=Enviar
login.login.2.Direcciondecorreoelectronico=Direcci&oacute;n de correo electr&oacute;nico:
login.login.2.NombredeUsuario=Nombre de usuario:
login.login.2.PresionaEnviar=2. Presiona Enviar.
login.login.2.SiBnextDPMShaaceptadotunombredeusuario,recibirasuncorreocontucontrase\u00f1a=Si Bnext QMS ha aceptado tu nombre de usuario, recibiras un correo con tu contrase\u00f1a.
login.login.BnextDPMShaaceptadotunombredeusuarioyhamandadouncorreocontucontrase\u00f1a=Bnext QMS ha aceptado tu nombre de usuario y ha mandado un correo con tu contrase\u00f1a.
login.login.BnextDPMShaenviadoaladireccionquehasespecificadounalistadelosnombresdeusuariosasociadosaesadirecciondecorreoelectronico=Bnext QMS ha enviado a la direcci&oacute;n que has especificado una lista de los nombres de usuarios asociados a esa direcci&oacute;n de correo electr&oacute;nico.
login.login.clave=Clave :
login.login.claveonombreincorrecto=Clave o nombre del usuario incorrecto.
login.login.Debeespecificarunadirecciondecorreoelectronico=Debe especificar una direcci&oacute;n de correo electr&oacute;nico
login.login.Debeespecificarunnombredeusuario=Debe especificar un nombre de usuario
login.login.DisculpalasmolestiasocasionadaspornopoderaccederaBnextDPMS=Disculpa las molestias ocasionadas por no poder acceder a Bnext QMS.
login.login.Entrar=Entrar
login.login.entraralsistema=Entrar al sistema
login.login.ERROR=ERROR:
login.login.Heolvidadomicontrase\u00f1a=He olvidado mi contrase\u00f1a.
login.login.Heolvidadominombredeusuario=He olvidado mi nombre de usuario.
login.login.introduzcausuarioyclave=Por favor introduzca su nombre de usuario y clave de acceso.
login.login.Lacuentadeusuarionoseencuentraactiva=La cuenta de usuario no se encuentra activa.
login.login.lasessionhaexpirado=La sesi&oacute;n ha expirado.
login.login.invalidToken=La liga de acceso al sistema ha expirado.
login.login.Nocerrarsesion=No cerrar sesi&oacute;n
login.login.NopuedoaccederamicuentadeBnextDPMS=No puedo acceder a mi cuenta de Bnext QMS
login.login.Notepreocupes,aunqueintentesaccedermuchasveces,tucuentanosebloqueara,yaqueBnextnolimitalosintentosdeacceso=No te preocupes, aunque intentes acceder muchas veces, tu cuenta no se bloquear&aacute;, ya que Bnext QMS no limita los intentos de acceso.
login.login.Pararecuperartucontrase\u00f1adeBnextDPMS,sigueestospasos=Para recuperar tu contrase\u00f1a de Bnext QMS, sigue estos pasos:
login.login.Parasolucionaresteproblemalomasrapidoposible,sigueestospasos=Para solucionar este problema lo m&aacute;s r&aacute;pido posible, sigue estos pasos:
login.login.Parecequemicontrase\u00f1anofunciona=Parece que mi contrase\u00f1a no funciona.
login.login.Resgistrate=Reg\u00edstrate
login.login.Seenviaraaladireccionquehasespecificadounalistadelosnombresdeusuariosasociadosaesadirecciondecorreoelectronico=Se enviar&aacute; a la direcci&oacute;n que has especificado una lista de los nombres de usuarios asociados a esa direcci&oacute;n de correo electr&oacute;nico.
login.login.Serequierelaclavedelusuario=Se requiere la clave del usuario.
login.login.Serequierelacuentadelusuario=Se requiere la cuenta del usuario.
login.login.Sinorecuerdastunombredeusuario,sigueestospasospararecuperarlo=Si no recuerdas tu nombre de usuario, sigue estos pasos para recuperarlo:
login.login.Sisiguesdeformaexactaestasinstrucciones,podrasobtenerlosmejoresresultados=Si sigues de forma exacta estas instrucciones, podr&aacute;s obtener los mejores resultados.
login.login.Siteapareceunmensajedeavisodiciendoquetucuentanoestaactivadadebesesperaraqueestaseaactivadaporeladministradordelsistema=Si te aparece un mensaje de aviso diciendo que tu cuenta no esta activa debes esperar a que esta sea activada por el administrador del sistema.
login.login.Tenencuentaqueelcampodelacontrase\u00f1adistingueentremayusculasyminusculas,porloquedebescomprobarquenohayascometidoningunerrorenestesentidoysilateclaBloq.Mayus.estaactivada=Ten en cuenta que el campo de la contrase\u00f1a distingue entre may&uacute;sculas y min&uacute;sculas, por lo que debes comprobar que no hayas cometido ning&uacute;n error en este sentido y si la tecla Bloq. May&uacute;s est&aacute; activada.
login.login.usuario=Usuario :
login.principal.Acciones=Acciones
login.principal.Auditoria=Auditor&iacute;a
login.principal.Documentos=Documentos
login.principal.Documents=Documentos
login.principal.Encuestas=Encuestas
login.registrarhandle.boton.Registrarse=Registrarse
login.registrarhandle.ClavedeAcceso=Clave de Acceso:
login.registrarhandle.ConfirmeClavedeAcceso=Confirme Clave de Acceso:
login.registrarhandle.Correo=Correo:
login.registrarhandle.--ELIJAUNPERFIL--=--ELIJA UN PERFIL--
login.registrarhandle.Eselnombredeusuarioqueutilizaraparaentraralsistema(Ej.username=Es el nombre de usuario que utilizara para entrar al sistema (Ej. username)
login.registrarhandle.Inicio=Inicio
login.registrarhandle.-Lasclavesdeaccesonocoinciden=- Las claves de acceso no coinciden.
login.registrarhandle.NombreCompleto=Nombre completo:
login.registrarhandle.Nombredeusuario=Nombre de usuario:
login.registrarhandle.Perfil=Perfil:
login.registrarhandle.-Perfildeusuario=- Perfil de usuario
login.registrarhandle.Preferentementeteclee[ApellidoPaterno,ApellidoMaterno,Nombre(s)]=Preferentemente teclee [Apellido Paterno, Apellido Materno, Nombre(s)]
login.registrarhandle.REGISTRODEUSUARIO=REGISTRO DE USUARIO
login.registrarhandle.Ustedseharegistradoexitosamente,espereuncorreoelectronicoavisandolequesucuentahasidoactivada=Usted se ha registrado exitosamente, espere un correo electronico avisandole que su cuenta ha sido activada.
LosSiguientesDatosSonNecesarios=Los siguientes datos son necesarios:
mensaje.actividad.NosePuedePedirCantidadMayorASobrante=No se puede pedir una cantidad mayor al sobrante.
mensaje.actualizacion=Los datos ser\\u00e1n actualizados en el sistema. \u00bfDesea continuar?
menu.folderTreeDownFrame.TraduccionArbolJavaScript = Un &aacute;rbol para la navegaci&oacute;n del sitio se abrir&aacute; aqu&iacute; si usted habilita JavaScript en su navegador.
menu.folderTreeLeftFrame.Acciones=Acciones
menu.folderTreeLeftFrame.Accionesatomar=Acciones a tomar
menu.folderTreeLeftFrame.Accionescorrectivas=Acciones correctivas
menu.folderTreeLeftFrame.Accionespreventivas=Acciones preventivas
menu.folderTreeLeftFrame.Alta/ActualizacionMasiva= Alta/Actualizaci&oacute;n Masiva
menu.folderTreeLeftFrame.Alta=Alta
menu.folderTreeLeftFrame.AltadeEquipos=Alta
menu.folderTreeLeftFrame.AltadeFuentedeAcciones=ALTA DE FUENTES DE ACCIONES
menu.folderTreeLeftFrame.ALTADEFUENTESDEAUDITORIAS=ALTA DE FUENTE DE AUDITORIAS
menu.folderTreeLeftFrame.ALTADEFUENTESDEQUEJAS=ALTA DE FUENTES DE QUEJAS
menu.folderTreeLeftFrame.ALTADETIPOSDECALIFICACIONES=Alta de calificaciones
menu.folderTreeLeftFrame.Areas=Procesos
menu.folderTreeLeftFrame.Auditorias=Auditor&iacute;as
menu.folderTreeLeftFrame.Avanzados=Avanzados
menu.folderTreeLeftFrame.Busqueda=Busqueda
menu.folderTreeLeftFrame.Catalogo=Cat&aacute;logos
menu.folderTreeLeftFrame.CatalogoAuditorias= Auditorias
menu.folderTreeLeftFrame.CatalogoAuditorias=Auditor&iacute;as
menu.folderTreeLeftFrame.CatalogoAuditoriasCalificaciones = Calificaciones
menu.folderTreeLeftFrame.CatalogoAuditoriasCalificacionesControlMasivo = Control Masivo
menu.folderTreeLeftFrame.CatalogoFuenteAuditorias=Estado de Auditor&iacute;as
menu.folderTreeLeftFrame.CatalogoQuejas =Quejas
menu.folderTreeLeftFrame.CatalogoQuejasFuente =Fuente de Quejas
menu.folderTreeLeftFrame.CatalogoQuejasFuenteControlMasivo =Control Masivo
menu.folderTreeLeftFrame.CatalogosAcciones =Acciones
menu.folderTreeLeftFrame.CatalogosAccionesFuenteControlMasivo =Control Masivo
menu.folderTreeLeftFrame.CatalogosFormas =Cat&aacute;logo de Formas
menu.folderTreeLeftFrame.CatalogosFuenteAcciones =Fuente de Acciones
menu.folderTreeLeftFrame.FindingType = Tipos de hallazgos
menu.folderTreeLeftFrame.Comentarios=Comentarios
menu.folderTreeLeftFrame.Documentos=Documentos
menu.folderTreeLeftFrame.ConfiguracioCuestionariosEncuestas = Cuestionarios Encuestas
menu.folderTreeLeftFrame.Configuracion=Configuraci&oacute;n
menu.folderTreeLeftFrame.ConfiguracionEncuestas = Encuestas
menu.folderTreeLeftFrame.ConfiguracionFacultades = ${Facilities}
menu.folderTreeLeftFrame.ConfiguracionIndicadores = Indicadores
menu.folderTreeLeftFrame.ConfiguracionMisEncuestas = Mis Encuestas
menu.folderTreeLeftFrame.ConfiguracionMisIndicadores = Mis Indicadores
menu.folderTreeLeftFrame.ConfiguracionMisQuejas = Mis Quejas
menu.folderTreeLeftFrame.ConfiguracionObjetivos = Objetivos
menu.folderTreeLeftFrame.ConfiguracionPerfiles = Perfiles
menu.folderTreeLeftFrame.ConfiguracionQuejas = Quejas
menu.folderTreeLeftFrame.ConfiguracionQuejasControl = Control
menu.folderTreeLeftFrame.ConfiguracionQuejasControlMasivo = Control Masivo
menu.folderTreeLeftFrame.ConfiguracionQuejasUR=Voz UR
menu.folderTreeLeftFrame.ConfiguracionSistema=Configuraci&oacute;n Sistema
menu.folderTreeLeftFrame.Consulta=Consulta
menu.folderTreeLeftFrame.Control=Control
menu.folderTreeLeftFrame.ControldeRegistros=Control de Registros
menu.folderTreeLeftFrame.LicensingSchemas=Esquemas de licencia
menu.folderTreeLeftFrame.ControlRecurrencias= Control Recurrencias
menu.folderTreeLeftFrame.ControlRegistros= Control de Registros
menu.folderTreeLeftFrame.Cuestionarios=Cuestionarios
menu.folderTreeLeftFrame.DepartamentoProcesoUnidades = &Aacute;reas
menu.folderTreeLeftFrame.Dificultad=Dificultad
menu.folderTreeLeftFrame.Historial=Historial
menu.folderTreeLeftFrame.GaleriaArbol=Galer&iacute;a
menu.folderTreeLeftFrame.Invitados=Invitados
menu.folderTreeLeftFrame.Listadesolicitudes=Lista de solicitudes
menu.folderTreeLeftFrame.ListaMaestra=Lista Maestra
menu.folderTreeLeftFrame.ListaRelaciones=Lista de Relaciones
menu.folderTreeLeftFrame.Mejoracontinua=Mejora continua
menu.folderTreeLeftFrame.MisAcciones=Mis acciones
menu.folderTreeLeftFrame.MisActividades=Mis Actividades
menu.folderTreeLeftFrame.MisAuditorias=Mis Auditor&iacute;as
menu.folderTreeLeftFrame.ModeloDeNegocios=Modelo de Negocios
menu.folderTreeLeftFrame.Objetos=Objetos
menu.folderTreeLeftFrame.Papelera=Papelera
menu.folderTreeLeftFrame.Papeleralarmex=Papelera
menu.folderTreeLeftFrame.Pendientes=Pendientes
menu.folderTreeLeftFrame.Plantilla=Plantillas
menu.folderTreeLeftFrame.Preferencias=Preferencias
menu.folderTreeLeftFrame.Preguntas=Preguntas
menu.folderTreeLeftFrame.Producto=Producto no conforme
menu.folderTreeLeftFrame.Proyecto=Acciones sobre proyectos
menu.folderTreeLeftFrame.Proyectos=Proyectos
menu.folderTreeLeftFrame.Registros=Registros
menu.folderTreeLeftFrame.Reporte=Reporte General
menu.folderTreeLeftFrame.ReportedeAcciones=Acciones
menu.folderTreeLeftFrame.ReportedeAuditorias=Auditor&iacute;as
menu.folderTreeLeftFrame.ReportedeConfiguracion=Configuraci&oacute;n
menu.folderTreeLeftFrame.ReportedeCuestionarios=Cuestionarios
menu.folderTreeLeftFrame.ReportedeDocumentos=Documentos
menu.folderTreeLeftFrame.ReporteDeDocumentos=Reporte de documentos
menu.folderTreeLeftFrame.ReportedeProyectos=Proyectos
menu.folderTreeLeftFrame.ReporteGerencial=Reporte Gerencial
menu.folderTreeLeftFrame.Reportes=Reportes
menu.folderTreeLeftFrame.Repositorios=Repositorios
menu.folderTreeLeftFrame.DiccionariosDeVersionamiento=Diccionarios de versi\u00f3n
menu.folderTreeLeftFrame.Respaldos = Respaldos
menu.folderTreeLeftFrame.Rubros=Rubros
menu.folderTreeLeftFrame.Secciones=Secciones
menu.folderTreeLeftFrame.Secuencias=Secuencias
menu.folderTreeLeftFrame.Solicitudes=Solicitudes
menu.folderTreeLeftFrame.TareaPendiente = Usted tiene una tarea pendiente en la forma actual. Presione el boton Cancelar &oacute; Regresar para poder realizar otra accion.
menu.folderTreeLeftFrame.Tipo=Tipo
menu.folderTreeLeftFrame.TipoDeObjetos=Tipos de Objetos
menu.folderTreeLeftFrame.Tipos=Tipos
menu.folderTreeLeftFrame.TraduccionArbolJavaScript = Un &aacute;rbol para la navegaci&oacute;n del sitio se abrir&aacute; aqu&iacute; si usted habilita JavaScript en su navegador.
menu.folderTreeLeftFrame.Ubicaciones=Departamentos
menu.folderTreeLeftFrame.Usuarios=Usuarios
menu.folderTreeLeftFrame.Llenar=Llenar
menu.folderTreeLeftFrame.DisenadosPorMi=Dise\u00f1ados por m\u00ed 
menu.folderTreeLeftFrame.HistorialDeLlenado=Historial de llenado
menu.folderTreeLeftFrame.AnswerResume=Consolidado de respuestas
menu.folderTreeLeftFrame.MiHistorialDeLlenado=Mi historial de llenado
menu.folderTreeLeftFrame.Formulario=Formularios
menu.folderTreeLeftFrame.CatalogoInterno=Cat\u00e1logo Interno
menu.folderTreeLeftFrame.CatalogoExterno=Cat\u00e1logo Externo
menu.folderTreeLeftFrame.CatalogMeeting=Reuniones
menu.folderTreeLeftFrame.MeetingType=Tipo de reuni\u00f3n
menu.folderTreeLeftFrame.DifusionDocumentos=Difusi\u00f3n de documentos
menu.folderTreeLeftFrame.Consultas=Consultas
menu.folderTreeLeftFrame.MiCuenta.MiCuenta=Mi Cuenta
menu.folderTreeLeftFrame.MiCuenta.Calendario=Calendario de suspensi\u00f3n 
menu.folderTreeLeftFrame.Calendar=Calendario de suspensi\u00f3n 
ocumentos.documentospendientes.QuejasporverificarrespuestaTooltip=Quejas por verificar respuesta
onfiguracion.objetohandle.javascript.LosDatosdelobjetohansidoactualizados=Los Datos del Objeto han sido Actualizados
opcion.NO=NO
opcion.SI=Si
proyecto.altaactividad.header.AltaDeActividad=Alta de actividad para la meta: 
proyecto.altaactividad.Objetos=Objetos
proyecto.altaactividad.Responsables=Responsables
proyecto.editaractividad.Responsables=Responsables
proyecto.proyectolist.Autor=Autor
proyecto.proyectolist.Borrar=Borrar
proyecto.proyectolist.CerrarProyecto=Presione para cerrar proyecto.
proyecto.proyectolist.ControlDeProyecto=CONTROL DE PROYECTOS
proyecto.proyectolist.Editar=Editar
proyecto.proyectolist.Estado=Estado
proyecto.proyectolist.FechaFinal=Fecha final
proyecto.proyectolist.FechaInicial=Fecha inicial
proyecto.proyectolist.javascript.DeseaMarcarElProyectoComoCerrado=\u00bfDesea marcar el proyecto como cerrado?
proyecto.proyectolist.javascript.ElProyectoSeraBorradoDelSistemaDeseaContinuar=El proyecto ser\\u00e1 borrado del sistema. \u00bfDesea continuar?
proyecto.proyectolist.ModificarProyecto=Presione para modifcar el proyecto.
proyecto.proyectolist.Nombre=Nombre
proyecto.proyectolist.NoSeEncontraronProyectosEnLosQueNoParticipa=No se encontraron proyectos en los que no participa.
proyecto.proyectolist.NoSeEncontraronProyectosEnLosQueParticipa=No se encontraron proyectos en los que particpa.
proyecto.proyectolist.NoSePudoRealizarLaOperacionAnterior=No se pudo realizar la operaci&oacute;n anterior
proyecto.proyectolist.Presupuesto=Presupuesto
proyecto.proyectolist.Proyecto=Proyecto
proyecto.proyectolist.ProyectoCerrado=Proyecto Cerrado.
proyecto.proyectolist.ProyectoConcluido=Proyecto Concluido.
proyecto.proyectolist.ProyectoCreado=Proyecto Creado.
proyecto.proyectolist.ProyectoLiberado=Proyecto Liberado.
proyecto.proyectolist.ProyectosEnLosQueNoParticipa=Proyectos en los que no participa
proyecto.proyectolist.ProyectosEnLosQueParticipa=Proyectos en los que participa
proyecto.proyectolist.Responsable=Responsable
proyecto.proyectolist.SeHaEliminadoElProyecto=Se ha eliminado el proyecto.
proyecto.proyectolist.SeHaMarcadoElProyectoComoCerrado=Se ha marcado el proyecto como cerrado.
proyecto.proyectolist.TODOS=TODOS
proyecto.proyectolist.Ver=Ver
proyecto.proyectolist.Verificador=Verificador
proyecto.proyectolist.VerProyecto=Presione para ver proyecto.
proyecto.proyectoreportegeneral.Elpresupuestosolodebecontenernumeros=El presupuesto s&oacute;lo debe contener n&uacute;meros
proyecto.proyectoreportegeneral.Verifiquepuntodecimal=Verifique punto decimal
proyectos..proyectolist.Proyecto=Proyecto:
proyectos.actividad.AgregarDocumento=Agregar Documento:
proyectos.actividad.AltaPeticionGasto=Se agreg&oacute; la petici&oacute;n con &eacute;xito.
proyectos.actividad.btnPeticionGasto=Petici&oacute;n Gasto
proyectos.actividad.Detalledelcambio=Detalle del Cambio
proyectos.actividad.-Dificultad=- Dificultad
proyectos.actividad.DocumentoExistente=Documento Existente
proyectos.actividad.DocumentoNuevo=Documento Nuevo
proyectos.actividad.ExistenPeticionesDeCambioPendientesLaActividadNoPuedeSerConcluidaConCambiosPendientes=Existen peticiones de gastos pendientes. La actividad no puede ser concluida con peticiones de gastos pendientes.
proyectos.actividad.ExistePeticionDeGastoNoAutorizada=Existe petici&oacute;n de gasto no autorizada
proyectos.actividad.-Modulo=- M\\u00f3dulo
proyectos.actividad.NoseHanAgregadoDocumentos=No se han agregado documentos.
proyectos.actividad.NosePudoAltaPeticionGasto=No se pudo dar de alta la petici&oacute;n de gasto.
proyectos.actividad.-Objeto=- Objeto
proyectos.actividad.peticionGastoCantidad=Cantidad:
proyectos.actividad.peticionGastoRazon=Raz&oacute;n:
proyectos.actividad.peticionGastoSobrante=Sobrante:
proyectos.actividad.Razondecambiodefechadeverificacion=Raz&oacute;n de cambio fecha verificaci&oacute;n:
proyectos.actividad.Razondecambiodefechafinal=Raz&oacute;n de cambio fecha fin:
proyectos.actividad.Razondecambiodefechainicial=Raz&oacute;n de cambio fecha Inicial:
proyectos.actividad.Razondecambiodeporcentaje=Raz&oacute;n de cambio porcentaje:
proyectos.actividad.Razondecambiodepresupuesto=Raz&oacute;n de cambio presupuesto:
proyectos.actividad.Razondecambiodeverificador=Raz&oacute;n de cambio verificador:
proyectos.actividad.-Tipo=- Tipo
proyectos.actividad.-TipodeObjeto=- Tipo de Objeto
proyectos.actividadeslistcabios.NoTieneAutorizacion=No tiene autorizaci&oacute;n
proyectos.actividadeslistcambios.Actividad.TipoCambioDesconocido=Tipo Desconocido de Cambio
proyectos.actividadeslistcambios.Actividad.TipoCambioFechaFin=Fecha Final
proyectos.actividadeslistcambios.Actividad.TipoCambioFechaInicio=Fecha Inicial
proyectos.actividadeslistcambios.Actividad.TipoCambioFechaVerificador=Fecha del Verificador
proyectos.actividadeslistcambios.Actividad.TipoCambioPeticionPresupuesto=Petici&oacute;n Presupuesto
proyectos.actividadeslistcambios.Actividad.TipoCambioPresupuesto=Presupuesto
proyectos.actividadeslistcambios.Actividad.TipoCambioVerificador=Verificador
proyectos.actividadeslistcambios.Autorizacion=Autorizaci&oacute;n:
proyectos.actividadeslistcambios.AutorizacionCambios=AUTORIZACI&oacute;N DE CAMBIOS EN ACTIVIDADES
proyectos.actividadeslistcambios.AutorizacoinCambios=AUTORIZACI&oacute;N DE CAMBIOS EN ACTIVIDADES
proyectos.actividadeslistcambios.CambioPropuesto=Cambio Propuesto: 
proyectos.actividadeslistcambios.CantidadSolicitada=Cantidad Solicitada
proyectos.actividadeslistcambios.DescripcionActividad=Descripci\u00f3n
proyectos.actividadeslistcambios.DescripcionProyecto=Descripci\u00f3n
proyectos.actividadeslistcambios.FaltallenarelcomentariodelarazondeNoAutorizaciondelaactividad=Especifique la raz\u00f3n por la cual No Autoriza la actividad.
proyectos.actividadeslistcambios.FechaFinal=Fecha Final
proyectos.actividadeslistcambios.FechaInicial=Fecha Inicial
proyectos.actividadeslistcambios.FechaVerificador=Fecha Verificador
proyectos.actividadeslistcambios.javascript.OperacionRealizadoExito=La operaci&oacute;n ha sido realizada con &eacute;xito
proyectos.actividadeslistcambios.NoActividadesCambiosPendientes=No hay actividades con cambios pendientes por autorizar
proyectos.actividadeslistcambios.NoAutorizo=NO AUTORIZO
proyectos.actividadeslistcambios.NombreActividad=Actividad:
proyectos.actividadeslistcambios.NoSeHaRealizadoNingunCambio=No se ha realizado ning&uacute;n cambio.
proyectos.actividadeslistcambios.Pendiente=PENDIENTE
proyectos.actividadeslistcambios.PresupuestoDisponible=Presupuesto Disponible
proyectos.actividadeslistcambios.RazonCambio=Raz&oacute;n de Cambio
proyectos.actividadeslistcambios.RazonNoAutoriza=Raz&oacute;n por la cual No Autoriza
proyectos.actividadeslistcambios.SiAutorizo=SI AUTORIZO
proyectos.actividadeslistcambios.TipoCambio=Tipo de Cambio: 
proyectos.actividadeslistcambios.ValorAnterior=Valor Anterior: 
proyectos.actividadeslistpeticionespresupuesto.Autorizacion=Autorizaci&oacute;n\:
proyectos.actividadeslistpeticionespresupuesto.AutorizacionPeticiones=AUTORIZACI&Oacute;N EN PETICIONES DE PRESUPUESTO
proyectos.actividadeslistpeticionespresupuesto.CantidadAPedir=Cantidad a Pedir\: 
proyectos.actividadeslistpeticionespresupuesto.CantidadTotal=Cantidad Total\: 
proyectos.actividadeslistpeticionespresupuesto.Comprometido=Comprometido\: 
proyectos.actividadeslistpeticionespresupuesto.DescripcionProyecto=Descripci\u00f3n\:
proyectos.actividadeslistpeticionespresupuesto.FaltallenarelcomentariodelarazondeNoAutorizaciondelaactividad=Especifique la raz\u00f3n por la cual No Autoriza la actividad.
proyectos.actividadeslistpeticionespresupuesto.javascript.OperacionRealizadoExito=La operaci&oacute;n ha sido realizada con &eacute;xito
proyectos.actividadeslistpeticionespresupuesto.NoActividadesCambiosPendientes=No hay actividades con cambios pendientes por autorizar
proyectos.actividadeslistpeticionespresupuesto.NoAutorizo=NO AUTORIZO
proyectos.actividadeslistpeticionespresupuesto.NombreActividad=Actividad\: 
proyectos.actividadeslistpeticionespresupuesto.NombreRubro=Rubro\: 
proyectos.actividadeslistpeticionespresupuesto.NoSeHaRealizadoNingunCambio=No se ha realizado ning\u00fan cambio.
proyectos.actividadeslistpeticionespresupuesto.Pendiente=PENDIENTE
proyectos.actividadeslistpeticionespresupuesto.RazonNoAutoriza=Raz&oacute;n por la cual No Autoriza\:
proyectos.actividadeslistpeticionespresupuesto.RazonPeticion=Raz&oacute;n de la petici&oacute;n:
proyectos.actividadeslistpeticionespresupuesto.SiAutorizo=SI AUTORIZO
proyectos.actividadeslistpeticionespresupuesto.Sobrante=Sobrante\: 
proyectos.actividadhandle.LaActividadSeraMarcadaComoVerificadaDeseaContinuar=La actividad sera marcada como verificada. \u00bfDesea continuar?
proyectos.actividadhandle.Objetos=Objetos
proyectos.actividadhandle.ReporteDeCambios=Reporte de cambios
proyectos.actividadhandle.VerificarActividad=Verificar actividad
proyectos.actividadlist.javascript.Laactividadseraborradodelsistema=La actividad ser\\u00e1 borrada del sistema. \u00bfDesea continuar?
proyectos.altaactividad.javascript.DeseaAjustarLasFechasDelProyectoAEstasNuevasFechas=\u00bfDesea ajustar las fechas del proyecto a estas nuevas fechas?
proyectos.altaactividad.javascript.ElRangoDeFechasNoCoincideConElRangoDeLasFechasDelProyecto=El rango de las fechas no coincide con el rango de las fechas del proyecto
proyectos.altaactividad.LaActividadNoPudoSerDadaDeAltaCorrectamente=La actividad no pudo ser dada de alta correctamente.
proyectos.avanceactividad.AutorizadoTotal=Autorizado total
proyectos.avanceactividad.btnPeticionDeGasto=Petici&oacute;n de gasto
proyectos.avanceactividad.ComentarioDeAvance=Comentario de avance
proyectos.avanceactividad.Comprometido=Comprometido
proyectos.avanceactividad.ComprometidoTotal=Comprometido total
proyectos.avanceactividad.imagen.Aceptar=Aceptar
proyectos.avanceactividad.imagen.Eliminar=Eliminar
proyectos.avanceactividad.imagen.Esconder=Esconder
proyectos.avanceactividad.imagen.mover=Mover
proyectos.avanceactividad.javascript.DebeLlenarLaRazonDeCambio=Debe llenar la raz&oacute;n de cambio.
proyectos.avanceactividad.javascript.ElNuevoPresupuestoNoPuedeSerMenorAlPresupuestoActualMenosElSobrante=El nuevo presupuesto no puede ser menor al presupuesto actual menos el sobrante
proyectos.avanceactividad.javascript.LaCantidadSolicitadaDeberSerMayorACero=La cantidad solicitada debe ser mayor a cero
proyectos.avanceactividad.javascript.LaCantidadSolicitadaEsMayorAlSobrante=La cantidad solicitada es mayor al sobrante
proyectos.avanceactividad.javascript.Razon=Raz\u00f3n
proyectos.avanceactividad.javascript.YaExisteUnaPeticionDeCambioDeFechaPendientePorAutorizar=Ya existe una petici\u00f3n de cambio de fecha pendiente por autorizar.
proyectos.avanceactividad.javascript.YaExisteUnaPeticionDeCambioDeVerificadorPendientePorAutorizar=Ya existe una petici\u00f3n de cambio de verificador pendiente por autorizar
proyectos.avanceactividad.Razon=Raz&oacute;n
proyectos.avanceactividad.RazonDeCambioDeFecha=Raz&oacute;n de cambio de fecha
proyectos.avanceactividad.Sobrante=Sobrante
proyectos.avanceactividad.SobranteTotal=Sobrante total
proyectos.editaractividad.Aceptar=Aceptar
proyectos.editarActividad.Borrar=Borrar
proyectos.editaractividad.btnAgregarRubro=Agregar Rubro
proyectos.editaractividad.Cantidad=Cantidad
proyectos.editarActividad.Descripcion=Descripci\u00f3n
proyectos.editaractividad.Descripcion=Descripci\u00f3n
proyectos.editarActividad.Editar=Editar
proyectos.editaractividad.ElDocumentoNoPudoSerBorrado=El documento no pudo ser borrado.
proyectos.editaractividad.ElPresupuestoNoPudoSerActualizadoCorrectamente=El presupuesto no pudo ser actualizado correctamente.
proyectos.editaractividad.ElPresupuestoNoPudoSerBorrado=El presupuesto no pudo ser borrado.
proyectos.editaractividad.ElPresupuestoNoPudoSerDadoDeAlta=El presupuesto no pudo ser dado de alta.
proyectos.editaractividad.Esconder=Esconder
proyectos.editaractividad.HaModificadoLaActividadDeseaGuardarLosCambiosAntesDeContinuar=Ha modificado la actividad. \u00bfDesea guardar los cambios antes de continuar?
proyectos.editaractividad.header.DetalleDeActividad=DETALLE DE ACTIVIDAD
proyectos.editarActividad.javascript.Cantidad=Cantidad
proyectos.editarActividad.javascript.DescripcionDelRubro=Descripci\\u00f3n del rubro
proyectos.editaractividad.javascript.EstaSeguroQueDeseaBorrarElPresupuesto=\u00bfEst\\u00e1 seguro que desea borrar el presupuesto?
proyectos.editaractividad.javascript.Lossiguientesdatossonnecesarios=Los siguientes datos son necesarios
proyectos.editarActividad.javascript.Rubro=Rubro
proyectos.editaractividad.LaActividadNoPudoSerActualizadaCorrectamente=La actividad no pudo ser actualizada correctamente.
proyectos.editaractividad.LaPeticionNoPudoSerDadaDeAlta=La petici&oacute;n no pudo ser dada de alta.
proyectos.editarActividad.NoSeHanDadoDeAltaRubros=No se han dado de alta rubros.
proyectos.editaractividad.NosePudoDarDeAltaElRubro=No se pudo dar de alta el rubro.
proyectos.editaractividad.Presupuesto=Presupuesto
proyectos.editarActividad.Presupuesto=Presupuesto
proyectos.editaractividad.PresupuestoTotal=Presupuesto total
proyectos.editaractividad.Responsables=Responsables
proyectos.editarActividad.Rubro=Rubro
proyectos.editaractividad.Rubro=Rubro
proyectos.metahandle.Actividad=Actividad
proyectos.metahandle.Actividades=Actividades
proyectos.metahandle.AgregarDocumento=Agregar Documento
proyectos.metahandle.AgregarNueva=Agregar Nueva
proyectos.metahandle.AltaDeMeta=ALTA DE META
proyectos.metahandle.Archivos=Archivos
proyectos.metahandle.Borrar=Borrar
proyectos.metahandle.Concluida=Concluida
proyectos.metahandle.Creada=Creada
proyectos.metahandle.Descripcion=Descripci\u00f3n
proyectos.metahandle.DetalleDeMeta=DETALLE DE META
proyectos.metahandle.DocumentoExistente=Documento Existente
proyectos.metahandle.DocumentoNuevo=Documento Nuevo
proyectos.metahandle.Editar=Editar
proyectos.metahandle.Estado=Estado
proyectos.metahandle.FechaFinal=Fecha Final
proyectos.metahandle.FechaInicial=Fecha Inicial
proyectos.metahandle.GraficaDeGantt=Gr&aacute;fica de Gantt
proyectos.metahandle.HaModificadoLaActividadDeseaGuardarLosCambiosAntesDeContinuar=Ha modificado la Meta. \u00bfDesea guardar los cambios antes de continuar?
proyectos.metahandle.HaModificadoLaMetaDeseaGuardarLosCambiosAntesDeContinuar=Ha modificado la meta \u00bfDesea guardar los cambios antes de continuar?
proyectos.metahandle.javascript.Descripcion=Descripci\u00f3n
proyectos.metahandle.javascript.ElDocumentoNoPudoSerBorrado=El Documento no pudo ser borrado
proyectos.metahandle.javascript.ElDocumentoSeraBorradoDeseaContinuar=El documento ser\\u00e1 borrado. \u00bfDesea continuar?
proyectos.metahandle.javascript.LaActividadNoPudoSerBorrada=La actividad no pudo ser borrada
proyectos.metahandle.javascript.LaActividadSeraBorradaDelSistemaDeseaContinuar=La actividad ser\\u00e1 borrada del sistema. \u00bfDesea continuar?
proyectos.metahandle.javascript.LaMetaHaSidoActualizada=La meta ha sido Actualizada
proyectos.metahandle.javascript.LaMetaHaSidoCerrada=La meta ha sido cerrada
proyectos.metahandle.javascript.LaMetaHaSidoDadoDeAlta=La meta ha sido dada de alta
proyectos.metahandle.javascript.LaMetaNoPudoActualizarseCorrectamente=La meta no pudo actualizarse correctamente
proyectos.metahandle.javascript.LosSiguientesDatosSonNecesarios=Los siguientes datos son necesarios
proyectos.metahandle.javascript.NombreDeLaMeta=Nombre de la meta
proyectos.metahandle.Regresar=Ser\u00e1 enviado a la pantalla anterior, sin guardar. \u00bfSeguro de que desea continuar?
proyectos.metahandle.NombreDeLaMeta=Nombre de la Meta
proyectos.metahandle.NoseHanAgregadoDocumentos=No se han agregado documentos
proyectos.metahandle.NoSeHanAsignadoActividades=No se han agregado actividades
proyectos.metahandle.Presupuesto=Presupuesto
proyectos.metahandle.UsuarioVerificador=Verificador
proyectos.metahandle.UsuarioResponsable=Responsable(s)
proyectos.metahandle.TODOS=TODOS
proyectos.metahandle.Ver=Ver
proyectos.metahandle.Verificada=Verificada
proyectos.metatohandle.EnProceso=En Proceso
proyectos.misactividades.ActividadesPorVerificar=ACTIVIDADES POR VERIFICAR
proyectos.misactividades.Editar=Editar
proyectos.misactividades.Estado=Estado
proyectos.misactividades.FechaFin=Fecha Final
proyectos.misactividades.FechaInicio=Fecha Inicial
proyectos.misactividades.MisActividades=MIS ACTIVIDADES
proyectos.misactividades.NoHayActividadesEnLasQueParticipaActualmente=No Participa en Actividades Actualmente
proyectos.misactividades.NombreActividad=Actividad
proyectos.misactividades.NombreMeta=Meta
proyectos.misactividades.NoParticipaEnActividadesEsteProyecto=No Participa en Actividades en este Proyecto
proyectos.misactividades.Presupuesto=Presupuesto
proyectos.misactividades.Responsable=Responsable
proyectos.misactividades.ResponsableVerifcador=Responsable / Verificador
proyectos.misactividades.Rol=Rol
proyectos.misactividades.Verificador=Verificador
proyectos.proyecto.DocumentoExistente=Documento Existente
proyectos.proyecto.DocumentoNuevo=Documento Nuevo
proyectos.proyecto.NoseHanAgregadoDocumentos=No se han agregado documentos.
proyectos.proyectohandle.Actividades=Actividades
proyectos.proyectohandle.AgregarDocumento=Agregar documento
proyectos.proyectohandle.AgregarNueva=Agregar nueva
proyectos.proyectohandle.AltaDeProyecto=ALTA DE PROYECTO
proyectos.proyectohandle.Archivos=Archivos
proyectos.proyectohandle.Borrar=Borrar
proyectos.proyectohandle.CerrarProyecto=Cerrar proyecto
proyectos.proyectohandle.Clave=Clave
proyectos.proyectohandle.Concluida=Concluida
proyectos.proyectohandle.Creada=Creada
proyectos.proyectohandle.Descripcion=Descripci\u00f3n
proyectos.proyectohandle.DetalleDeProyecto=PROYECTOS
proyectos.proyectohandle.DocumentoExistente=Documento existente
proyectos.proyectohandle.DocumentoNuevo=Documento nuevo
proyectos.proyectohandle.DocumentosExistentes=Documentos existentes
proyectos.proyectohandle.DocumentosNuevos=Documentos nuevos
proyectos.proyectohandle.DocumentosRelacionados=Documentos Relacionados
proyectos.proyectohandle.Editar=Editar
proyectos.proyectohandle.ElProyectoSeraMarcadoComoCerradoDeseaContinuar=El proyecto ser\\u00e1 marcado como cerrado. \u00bfDesea continuar?
proyectos.proyectohandle.EnProceso=En proceso
proyectos.proyectohandle.Estado=Estado
proyectos.proyectohandle.FechaFinal=Fecha final
proyectos.proyectohandle.FechaInicial=Fecha inicial
proyectos.proyectohandle.Folio=Folio
proyectos.proyectohandle.Funcion=Funci&oacute;n
proyectos.proyectohandle.GraficaDeGantt=Gr&aacute;fica de Gantt
proyectos.proyectohandle.HaModificadoElProyectoDeseaGuardarLosCambiosAntesDeContinuar=Ha modificado el proyecto. \u00bfDesea guardar los cambios antes de continuar?
proyectos.proyectohandle.javascript.Clave=Clave
proyectos.proyectohandle.javascript.Descripcion=Descripci\\u00f3n
proyectos.proyectohandle.javascript.ElDocumentoNoPudoSerBorrado=El documento no pudo ser borrado.
proyectos.proyectohandle.javascript.ElDocumentoSeraBorradoDeseaContinuar=El documento ser\\u00e1 borrado. \u00bfDesea Continuar?
proyectos.proyectohandle.javascript.ElProyectoDebeEmpezarDespuesDelDiaDeHoy=El proyecto debe empezar despu&eacute;s del d&iacute;a de hoy
proyectos.proyectohandle.javascript.ElProyectoDebeTerminarDespuesDelDiaDeHoy=El proyecto debe terminar despu&eacute;s del d&iacute;a de hoy
proyectos.proyectohandle.javascript.ElProyectoHaSidoActualizado=El proyecto ha sido actualizado.
proyectos.proyectohandle.javascript.ElProyectoHaSidoActualizadoCorrectamente=El proyecto ha sido actualizado correctamente.
proyectos.proyectohandle.javascript.ElProyectoHaSidoCerrado=El proyecto ha sido cerrado.
proyectos.proyectohandle.javascript.ElProyectoHaSidoDadoDeAlta=El proyecto ha sido dado de alta.
proyectos.proyectohandle.javascript.ElProyectoNoPudoActualizarseCorrectamente=El proyecto no pudo actualizarse correctamente.
proyectos.proyectohandle.javascript.FechaFinal=Fecha final
proyectos.proyectohandle.javascript.FechaInicial=Fecha inicial
proyectos.proyectohandle.javascript.Folio=Folio
proyectos.proyectohandle.javascript.Funcion=Funci\\u00f3n
proyectos.proyectohandle.javascript.LaMetaNoPudoSerBorrada=La meta no pudo ser borrada.
proyectos.proyectohandle.javascript.LaMetaNoPudoSerBorradaReason=La meta no puede ser borrada, ya que tiene actividades en progreso
proyectos.proyectohandle.javascript.LaMetaSeraBorradaDelSistemaDeseaContinuar=La meta ser\\u00e1 borrada del sistema. \u00bfDesea continuar?
proyectos.proyectohandle.javascript.LosSiguientesDatosSonNecesarios=Los siguientes datos son necesarios
proyectos.proyectohandle.javascript.NombreDelProyecto=Nombre del proyecto
proyectos.proyectohandle.javascript.NumeroConvenio=N\\u00famero de Convenio
proyectos.proyectohandle.javascript.Presupuesto=Presupuesto
proyectos.proyectohandle.LiberarProyecto=Liberar proyecto
proyectos.proyectohandle.Meta=Meta
proyectos.proyectohandle.Metas=Metas
proyectos.proyectohandle.NombreDelProyecto=Nombre del proyecto
proyectos.proyectohandle.NoSeHanAsignadoActividades=No se han asignado actividades.
proyectos.proyectohandle.NoSeHanAsignadoMetas=No se han asignado metas
proyectos.proyectohandle.NoSePudoRealizarLaOperacionAnterior=No se pudo realizar la operacion anterior
proyectos.proyectohandle.NumeroDeConvenio=Numero de convenio
proyectos.proyectohandle.PermitirUsarseComoProyectoBase=Permitir usarse como proyecto base
proyectos.proyectohandle.Presupuesto=Presupuesto
proyectos.proyectohandle.ProyectoBase=Proyecto base
proyectos.proyectohandle.ProyectoConfidencial=Proyecto confidencial
proyectos.proyectohandle.ProyectoDeDesarrollo=Proyecto de Desarrollo
proyectos.proyectohandle.TODOS=TODOS
proyectos.proyectohandle.Ver=Ver
proyectos.proyectohandle.-Verifiquequelafechaseaposterioroigualaladiadelhoy=- Verifique que la fecha(s) sea posterior o igual a la d\\u00eda del hoy.
proyectos.proyectolistcontrolcambios.Borrar=Borrar
proyectos.proyectolistcontrolcambios.Editar=Editar
proyectos.proyectolistcontrolcambios.Encargado=ENCARGADO
proyectos.proyectolistcontrolcambios.Estado=Estado
proyectos.proyectolistcontrolcambios.FechaFin=Fecha final
proyectos.proyectolistcontrolcambios.FechaInicio=Fecha inicio
proyectos.proyectolistcontrolcambios.Liberado=Liberado
proyectos.proyectolistcontrolcambios.MisProyectosConCambiosPendientes=Mis Proyectos Con Cambios Pendientes
proyectos.proyectolistcontrolcambios.MisProyectosPeticionesPresupuestosAutorizar=Mis Proyectos con Peticiones de Presupuesto por Autorizar
proyectos.proyectolistcontrolcambios.MisProyectosPorCerrar=Mis Proyectos Por Cerrar
proyectos.proyectolistcontrolcambios.Nombre=Proyecto
proyectos.proyectolistcontrolcambios.NoSeEncontraronProyectosConCambiosPendientes=No se han encontrado proyectos que tengan actividades con cambios pendientes
proyectos.proyectolistcontrolcambios.NoSeEncontraronProyectosPorCerrar=No se han encontrado proyectos por cerrar
proyectos.proyectolistcontrolcambios.OtrosProyectosConCambiosPendientes=Otros Proyectos Con Cambios Pendientes
proyectos.proyectolistcontrolcambios.OtrosProyectosPeticionesPresupuestosAutorizar=Otros Proyectos con Peticiones de Presupuesto por Autorizar
proyectos.proyectolistcontrolcambios.OtrosProyectosPorCerrar=Otros Proyectos Por Cerrar
proyectos.proyectolistcontrolcambios.Presupuesto=Presupuesto
proyectos.proyectolistcontrolcambios.ProyectosConCambiosPendientes=PROYECTOS CON ACTIVIDADES CON CAMBIOS PENDIENTES
proyectos.proyectolistcontrolcambios.ProyectosPeticionesPresupuestosAutorizar=Proyectos con Peticiones de Presupuesto por Autorizar
proyectos.proyectolistcontrolcambios.ProyectosPorCerrar=PROYECTOS POR CERRAR
proyectos.proyectoreportegeneral.chart.Cerrado=Cerrado
proyectos.proyectoreportegeneral.chart.Concluido=Concluido
proyectos.proyectoreportegeneral.chart.Creado=Creado
proyectos.proyectoreportegeneral.chart.Liberado=Liberado
proyectos.proyectoreportegeneral.chart.NumeroDeProyectos=N\u00famero de Proyectos
proyectos.proyectoreportegeneral.chart.title=Proyectos
proyectos.proyectoreportegeneral.header=REPORTE GENERAL DE PROYECTOS
proyectos.proyectoreportegeneral.HorasporProyecto=Horas por Proyecto
proyectos.proyectoshandle.-estemensajenodebemostrarse-=-este mensaje no debe mostrarse-
proyectos.proyectoslistcontrolcambios.UstedNoTieneAccesoaEstaPagina=Usted No Tiene Acceso a &Eacute;sta P&aacute;gina
proyectos.reporteavanceactividad.Autor=Autor
proyectos.reporteavanceactividad.Avance=Avance
proyectos.reporteavanceactividad.Comentario=Comentario
proyectos.reporteavanceactividad.Fecha=Fecha
proyectos.reporteavanceactividad.header=REPORTE DE AVANCE EN ACTIVIDAD
proyectos.reporteavanceactividad.Inicio=Inicio
proyectos.reporteavanceactividad.Nombre=Nombre
proyectos.reporteavanceactividad.Responsable=Responsable
proyectos.reporteavanceactividad.RolAutor=Rol
proyectos.reporteavanceactividad.TrabajoRealizado=Trabajo realizado
proyectos.reporteavanceactividad.TrabajoTotalRealizado=Trabajo total realizado
proyectos.reporteavanceactividad.Verificador=Verificador
proyectos.reportecambiosactividad.Actividad=Actividad
proyectos.reportecambiosactividad.Activity=Actividad
proyectos.reportecambiosactividad.Autorizado=Autorizado
proyectos.reportecambiosactividad.CambioPropuesto=Cambio propuesto
proyectos.reportecambiosactividad.CantidadSolicitada=Cantidad solicitada
proyectos.reportecambiosactividad.header=REPORTE DE CAMBIOS EN ACTIVIDAD
proyectos.reportecambiosactividad.NoAutorizado=No autorizado
proyectos.reportecambiosactividad.Pendiente=Pendiente
proyectos.reportecambiosactividad.PresupuestoDisponible=Presupuesto disponible
proyectos.reportecambiosactividad.RazonDeLaPeticion=Raz&oacute;n de la petici&oacute;n
proyectos.reportecambiosactividad.RazonPorLaQueNoAutoriza=Raz&oacute;n no autoriza
proyectos.reportecambiosactividad.StatusDeAutorizacion=Status de autorizaci&oacute;n
proyectos.reportecambiosactividad.TipoDeCambio=Tipo de cambio
proyectos.reportecambiosactividad.ValorAnterior=Valor anterior
proyectos.reportedocumentosproyectosubidos.Actividad=Actividad
proyectos.reportedocumentosproyectosubidos.ActividadOriginal=Actividad original
proyectos.reportedocumentosproyectosubidos.Autor=Autor
proyectos.reportedocumentosproyectosubidos.header=REPORTE DE DOCUMENTOS SUBIDOS
proyectos.reportedocumentosproyectosubidos.Meta=Meta
proyectos.reportedocumentosproyectosubidos.MetaOriginal=Meta original
proyectos.reportedocumentosproyectosubidos.NoSeEncontraronDocumentosEnEstaBusqueda=No se encontraron documentos en esta b&uacute;squeda.
proyectos.reportedocumentosproyectosubidos.Proyecto=Proyecto
proyectos.reportedocumentosproyectosubidos.ProyectoOriginal=Proyecto original
proyectos.reportedocumentosproyectosubidos.Titulo=T&iacute;tulo
proyectos.reportepeticionespresupuesto.CantidadAPedir=Cantidad
proyectos.reportepeticionespresupuesto.FechaAutorizacion=Fecha Autorizaci&oacute;n
proyectos.reportepeticionespresupuesto.FechaPeticion=Fecha Petici&oacute;n
proyectos.reportepeticionespresupuesto.header=REPORTE DE PETICIONES DE PRESUPUESTO EN ACTIVIDADES
proyectos.reportepeticionespresupuesto.Rubro=Rubro
proyectos.reportepeticionespresupuesto.StatusDeAutorizacion=Estado
proyectos.verificaractividad.Aceptar=Aceptar
proyectos.verificaractividad.ComentarioDeCumplimiento=Comentario de cumplimiento
proyectos.verificaractividad.ComentarioDeDisminucion=Comentario de disminuci&oacute;n
proyectos.verificaractividad.Esconder=Esconder
proyectos.verificaractividad.javascript.-Comentario=- Comentario
proyectos.verificaractividad.javascript.SiCambiaElPresupuestoDeLaActividadEstaCamiaraDeEstadoAEnProcesoEstaSeguroQueDeseaContinuar=Si cambia el presupuesto de la actividad, esta cambiar\\u00e1 de estado a En Proceso. \u00bfEst\\u00e1 seguro que desea continuar?
quejas.misquejas.asignada=Fecha asignada
quejas.misquejas.Autor=Autor
quejas.misquejas.Autordelaqueja=Autor de la queja
quejas.misquejas.Busqueda=B&uacute;squeda
quejas.misquejas.BusquedaAvanzada=B&uacute;squeda Avanzada
quejas.misquejas.Calificacion=Calificaci\u00f3n
quejas.misquejas.Clave=Clave
quejas.misquejas.CONTROL=CONTROL
quejas.misquejas.Controldequejas=CONTROL DE QUEJAS
quejas.misquejas.Departamento=Departamento
quejas.misquejas.Estado=Estado
quejas.misquejas.estadoAtendidaenimplementacion=Estado: Atendida. En implementacion
quejas.misquejas.estadoAtendidaenimplementacion2=Atendida - en proceso
quejas.misquejas.estadoAtendidaporevaluar=Estado: Atendida por evaluar
quejas.misquejas.estadoAtendidaporevaluar2=Atendida por evaluar
quejas.misquejas.estadoEnproceso=Estado: En proceso
quejas.misquejas.estadoEnproceso2=En proceso
quejas.misquejas.estadoEvaluada=Estado: Atendida
quejas.misquejas.estadoEvaluada2=Atendida
quejas.misquejas.estadoFinalizada=Estado: Finalizada
quejas.misquejas.estadoFinalizada2=Finalizada
quejas.misquejas.estadoImplementadaporevaluar=Estado: Implementada. Por evaluar
quejas.misquejas.estadoImplementadaporevaluar2=Por aceptar resultados
quejas.misquejas.estadoNoprocede=Estado: No procede
quejas.misquejas.estadoNoprocede2=Cerrada - no procede
quejas.misquejas.estadoReportada=Estado: Reportada
quejas.misquejas.estadoReportada2=Reportada
quejas.misquejas.estadoReportadaasignada=Estado: Reportada y asignada a responsables
quejas.misquejas.estadoReportadaasignada2=Asignada
quejas.misquejas.Fechareportada=Fecha reportada
quejas.misquejas.FechaReportada=Fecha Reportada
quejas.misquejas.finalizada=Fecha Evaluada
quejas.misquejas.Fuente=Fuente
quejas.misquejas.MISQUEJAS=MIS QUEJAS
quejas.misquejas.Mostrarfiltrodebusqueda=Mostrar filtro de busqueda
quejas.misquejas.noprocede=Fecha No Procede
quejas.misquejas.Ocultarfiltrodebusqueda=Ocultar filtro de busqueda
quejas.misquejas.Quejasenlasqueparticipacomoautordelaqueja=Donde es autor:
quejas.misquejas.Quejasenlasqueparticipacomodue\u00f1odeldepartamento=Donde es encargado:
quejas.misquejas.Quejasenlasqueparticipacomoresponsable=Donde es responsable:
quejas.misquejas.reportada=Fecha reportada
quejas.misquejas.respondida=Fecha Respondida
quejas.misquejas.Seborroelregistro=Se borro el registro.
quejas.misquejas.---SELECCIONE---=--- SELECCIONE ---
quejas.misquejas.---TODOS---=--- TODOS ---
quejas.misquejas.verificada=Fecha Verificada
quejas.misquejas.Worker=Responsable
quejas.quejashandle.GuardadoExitoso=Se ha guardado exitosamente el hallazgo.
quejas.quejashandle.ErrorAlGuardar=Ha ocurrido un error y no se ha podido guardar el hallazgo.
quejas.quejashandle.Analisis=An\u00e1lisis
quejas.quejashandle.AceptarRespuesta=Aceptar Respuesta:
quejas.quejashandle.-Calificacion=- Calificaci\\u00f3n
quejas.quejashandle.Calificacion=Calificaci&oacute;n:
quejas.quejashandle.Clasificacion=Clasificaci\u00f3n:
quejas.quejashandle.Clave=Clave:
quejas.quejashandle.ClavedeQueja=Clave:
quejas.quejashandle.-Cuentadecorreonovalida=- Cuenta de correo no v\\u00e1lida
quejas.quejashandle.DarRespuesta=Dar Respuesta:
quejas.quejashandle.-Departamento.=- Unidad productiva
quejas.quejashandle.Direcciondecorreoalternativapararecibiravisos=Copia de queja para:
quejas.quejashandle.Escuelaofacultadalquesedirigelaqueja=Departamento al que se dirige la queja:
quejas.quejashandle.FechaAsignacion=Fecha Asignaci\u00f3n:
quejas.quejashandle.FechaFinalizada=Fecha Finalizada:
quejas.quejashandle.FechaNOPROCEDE=Fecha NO PROCEDE:
quejas.quejashandle.FechaReportada=Fecha Reportada:
quejas.quejashandle.FechaRespondida=Fecha Respondida:
quejas.quejashandle.FechaVerificada=Fecha Verificada:
quejas.quejashandle.-Fuente=- Fuente
quejas.quejashandle.FuenteDosP=Fuente:
quejas.quejashandle.Laquejahasidoreportada=La queja ha sido reportada.
quejas.quejashandle.Levantaracciones=Levantar Acciones:
quejas.quejashandle.Losdatosdelaquejahansidoguardadosyseenviaronavisoscorrectamente=Los datos de la queja han sido guardados y se enviaron avisos correctamente.
quejas.quejashandle.Lossiguientescampossonnecesarios=Los siguientes campos son necesarios:
quejas.quejashandle.matricula=Matr&iacute;cula:
quejas.quejashandle.MisQuejas=Mis Quejas
quejas.quejashandle.NuevaQueja=Nueva Queja
quejas.quejashandle.PorAsignar=Por Asignar
quejas.quejashandle.QuejaReportadaPor=Reportada por:
quejas.quejashandle.QUEJAS=QUEJAS
quejas.quejashandle.-Razonnoprocede=- Raz\\u00f3n no procede.
quejas.quejashandle.-Razonnoseacepta=- Raz\\u00f3n por la que no se acepta.
quejas.quejashandle.AnalisisCalificacion=- An&aacute;lisis sobre la clasificaci&oacute;n.
quejas.quejashandle.RazonPorLaQueNoProcede=Raz&oacute;n por la que no procede:
quejas.quejashandle.RazonPorLaQueNoSeAcepta=Raz&oacute;n por la que no se acepta:
quejas.quejashandle.-Redacciondelaqueja=- Redacci\\u00f3n de la queja
quejas.quejashandle.Redacciondequeja=Redacci&oacute;n de queja:
quejas.quejashandle.Responsable=Responsable de dar Respuesta:
quejas.quejashandle.-Respuestaalaqueja=- Respuesta a la queja
quejas.quejashandle.SELECCIONE=SELECCIONE
quejas.quejashandle.Cancelar=La informaci\u00f3n no ser\u00e1 guardada. <br> \u00bfEst\u00e1 seguro de cancelar?
quejas.quejashandle.Sereasignoresponsable,redacteporque=(Se reasigno responsable, redacte porque)
quejas.quejashandle.-Prioridad=- Prioridad
quejas.quejashandle.-Clasificacion=- Clasificaci\u00f3n
quejas.quejashandle.-Responsable=- Responsable
quejas.quejaslist.controldequejas=CONTROL DE QUEJAS
quejas.quejaslist.Redacciondelaqueja=Redacci\u00f3n de la Queja
quejas.reporteQuejas.Departamento=Unidad productiva:
quejas.reporteQuejas.Estado=Estado:
quejas.reporteQuejas.FechaAsignda=Fecha Asignada:
quejas.reporteQuejas.FechaAtendida(Enproceso)=Fecha Atendida(En proceso):
quejas.reporteQuejas.FechaCerrada(noprocede)=Fecha Cerrada (no procede):
quejas.reporteQuejas.Fechadecreacion=Fecha de creaci&oacute;n:
quejas.reporteQuejas.Fechadetermino=Fecha de t&eacute;rmino:
quejas.reporteQuejas.FechaPorAceptarResultados=Fecha Por Aceptar Resultados:
quejas.reporteQuejas.Filtrosdefecha=Filtros de fecha:
quejas.reporteQuejas.Grafica=Gr&aacute;fica
quejas.reporteQuejas.Graficarpor=Graficar por:
quejas.reporteQuejas.Loscamposseencuentranvacios=Los campos se encuentran vacios.
quejas.reporteQuejas.Quejasreportadasa=Quejas reportadas a:
quejas.reporteQuejas.Quejasreportadaspor=Quejas reportadas por:
quejas.reporteQuejas.REPORTEDEQUEJAS=REPORTES DE QUEJAS
quejas.reporteQuejas.TipodeReporte=Tipo de Reporte:
quejas.reporteQuejas.--TODOS--=-- TODOS --
quejas.reporteQuejas.--TODOS--=-- TODOS --
quejas.reporteQuejas.Usuario=Usuario:
reportes.jasperreportRangodeFechasdereport=Rango de fechas del reporte:
reportes.jasperreports.Generar=Generar
reportes.reporteGantt.Actividades=Actividades
reportes.reporteGantt.Estimado=Estimado
reportes.reporteGantt.header=GR&Aacute;FICA DE GANTT DE PROYECTO
reportes.reporteGantt.Programado=Programado
Reportes.ReporteGeneralDeAuditorias =REPORTE DE AGENDA DE AUDITOR&Iacute;AS
reportes.reportegerencial.Accionesabiertas=Acciones abiertas
reportes.reportegerencial.Accionesnoatendidas=Acciones no atendidas
reportes.reportegerencial.ACEPTADA=ACEPTADA
reportes.reportegerencial.Ano=A\u00f1o
reportes.reportegerencial.Auditoriasrealizandose=Auditor&iacute;as reali&aacute;ndose
reportes.reportegerencial.CANCELADA=CANCELADA
reportes.reportegerencial.CONFIRMADA=CONFIRMADA
reportes.reportegerencial.Graficarporano=Graficar por a\u00f1o...
reportes.reportegerencial.Graficarpormes=Graficar por mes...
reportes.reportegerencial.PLANEADA=PLANEADA
reportes.reportegerencial.ReporteGerencial=REPORTE GERENCIAL
reportes.reportegerencial.Resumendegraficas=Resumen de gr&aacute;ficas
reportes.reportegerencial.Seguimientoaacciones=Seguimiento a acciones
reportes.reportegerencial.Seguimientoaauditorias=Seguimiento a auditorias
reportes.reportegerencial.Seguimientoadocumentos=Seguimiento a documentos
reportes.reportegerencial.Solicitudesnoatendidas=Solicitudes no atendidas
reportesgenerales.Abril=Abr
reportesgenerales.Agosto=Ago
reportesgenerales.Autor=Autor
reportesgenerales.Autorizante=Autorizante
reportesgenerales.chart.noexisteinformacionparagraficar=No existe informaci\u00f3n para graficar
reportesgenerales.Diciembre=Dic
reportesgenerales.Enero=Ene
reportesgenerales.Estado=Estado
reportesgenerales.Febrero=Feb
reportesgenerales.FechaFin=Fecha final
reportesgenerales.FechaFinCreado=Fecha final creado
reportesgenerales.FechaFinReportada=Fecha final reportada
reportesgenerales.FechaInicio=Fecha inicio
reportesgenerales.FechaInicioCreado=Fecha inicio creado
reportesgenerales.FechaInicioReportada=Fecha inicio reportada
reportesgenerales.Fuente=Fuente
reportesgenerales.Sociedad=${Facility}
reportesgenerales.graficacategorizadapor=Gr\u00e1fica categorizada por:
reportesgenerales.Graficar=Graficar
reportesgenerales.GraficaX=Categorizar por
reportesgenerales.Julio=Jul
reportesgenerales.Junio=Jun
reportesgenerales.Marzo=Mar
reportesgenerales.Mayo=May
reportesgenerales.Mes=Mes
reportesgenerales.Noviembre=Nov
reportesgenerales.numerodeacciones=N\u00famero de acciones
reportesgenerales.Octubre=Oct
reportesgenerales.Originador=Originador
reportesgenerales.PresupuestoMaximo=Presupuesto M&aacute;ximo
reportesgenerales.PresupuestoMinimo=Presupuesto M&iacute;nimo
reportesgenerales.Proyecto=Proyecto
reportesgenerales.Responsable=Auditor L\u00edder
reportesgenerales.Responsableaccion.accionatomarhandle.Noserecibiolaclaveareferenciar=No se recibi&oacute; la clave a referenciar.
reportesgenerales.Septiembre=Sep
reportesgenerales.Tipodeaccion=Tipo de acci\u00f3n
reportesgenerales.Tipodegrafica=Tipo de gr&aacute;fica
reportesgenerales.TODAS=TODAS
reportesgenerales.TODOS=TODOS
reportesgenerales.total=Total
reportesgenerales.Ubicacion=Departamento
reportesgenerales.TipoDocumento=Tipo de Documento
royecto.proyectolist.SeHaMarcadoElProyectoComoCerrado=Se ha marcado el proyecto como cerrado.
solicitudes.documentoshandlesolicitudes.-Agregarunarchivo=- Agregar un Archivo
solicitudes.documentoshandlesolicitudes.-Agregarunaversionvalida=- Agregar una versi\\u00f3n v\\u00e1lida
solicitudes.documentoshandlesolicitudes.Aprobador=Aprobador
solicitudes.documentoshandlesolicitudes.Crearsecuencia=Crear secuencia
solicitudes.documentoshandlesolicitudes.Faltanlossiguienteselementos=Faltan los siguientes elementos
solicitudes.documentoshandlesolicitudes.Nosepudosubirelarchivo,yaexisteunarchivofisicoenelsistemaconelmismonombreOnosepudolocalizarelarchivoindicado=No se pudo subir el archivo, ya existe un archivo f\\u00edsico en el sistema con el mismo nombre \\n\\u00f3 no se pudo localizar el archivo indicado.
solicitudes.documentoshandlesolicitudes.Seactualizocorrectamentelacarpetadestino=Se actualiz\u00f3 correctamente la carpeta destino.
solicitudes.documentoshandlesolicitudes.Version=Versi&oacute;n
solicitudes.solicitudeshandle.Actualmenteeldocumentoseencuentraenunasecuenciaonoseencuentraactivoporloquenopuedeasignarunasecuenciaalasolicitud=Actualmente el documento se encuentra en una secuencia o no se encuentra activo por lo que no puede asignar una secuencia a la solicitud.
solicitudes.solicitudeshandle.Archivoactual=Ver archivo actual:
solicitudes.solicitudeshandle.Carpeta=Carpeta:
solicitudes.solicitudeshandle.CrearDocumento=Crear Documento
solicitudes.solicitudeshandle.CrearSecuencia=Crear Secuencia
solicitudes.solicitudeshandle.-Debeseleccionarunacarpetadestino=- Debe seleccionar una carpeta destino.
solicitudes.solicitudeshandle.DocumentoElectronico=Documento Electr&oacute;nico:
solicitudes.solicitudeshandle.Eldocumentorelacionadoaestasolicitudnoseencuentraactivo,probablementeyafuecancelado,puedepediralencargadodeldocumentoqueloreactivesiesposible.Obien,\u00bfDeseacerrarestasolicitud?=El documento relacionado a esta solicitud no se encuentra activo, probablemente ya fue cancelado, puede pedir al encargado del documento que lo reactive si es posible.\n\n O bien, \u00bfDesea cerrar esta solicitud?
solicitudes.solicitudeshandle.EldocumentorelacionadoalasolicitudnoseencuentraACTIVO-Clavede;Documento=El documento relacionado a la solicitud no se encuentra ACTIVO \n\n     - Clave del Documento:
solicitudes.solicitudeshandle.Elegirotracarpeta=Elegir otra carpeta
solicitudes.solicitudeshandle.ModificarDocumento=Modificar Documento
solicitudes.solicitudeshandle.Noconoscolaclave=No conozco la clave:
solicitudes.solicitudeshandle.RechazarSolicitud=Rechazar Solicitud
solicitudes.solicitudeshandle.Reemplazararchivosubido=Reemplazar archivo subido:
solicitudes.solicitudeshandle.Regresar=Regresar
solicitudes.solicitudeshandle.Seactualizolasolicitud,regreseparaaprobarla=Se actualiz&oacute; la solicitud, regrese para aprobarla.
solicitudes.solicitudeshandle.Serealizoelprocesocorrectamente,lasolicitudhasidomarcadacomorechazada=Se realiz&oacute; el proceso correctamente, la solicitud ha sido marcada como rechazada.
solicitudes.solicitudeshandle.SOLICITUDDECANCELACION-CLAVE=SOLICITUD DE CANCELACION - CLAVE:
solicitudes.solicitudeshandle.SOLICITUDDEDOCUMENTONUEVO-CLAVE=SOLICITUD DE DOCUMENTO NUEVO - CLAVE:
solicitudes.solicitudeshandle.SOLICITUDDEMODIFICACION-CLAVE=SOLICITUD DE MODIFICACION - CLAVE:
solicitudes.solicitudeshandle.SOLICITUDDERE-APROBACION-CLAVE=SOLICITUD DE RE-APROBACION - CLAVE:
solicitudes.solicitudeshandle.Tipodedocumento=Tipo de documento
solicitudes.solicitudeshandle.Ver=Ver
solicitudes.solicitudeslist.ArchivoSubido=Archivo Subido
solicitudes.solicitudeslist.ClaveDoc=Clave Doc.
solicitudes.solicitudeslist.ClaveDocumento= Clave Documento:
solicitudes.solicitudeslist.ClaveSol=Clave Sol
solicitudes.solicitudeslist.ElestadoactualesRECHAZADA,presioneparamarcarcomoCerrada=El estado actual es RECHAZADA, presione para marcar como CERRADA.
solicitudes.solicitudeslist.Enproceso=En proceso
solicitudes.solicitudeslist.EstadoactualesRECHAZADO.Clicparaverdetalle=Estado actual es RECHAZADO. Clic para ver detalle
span.anio_s=A\u00f1o(s)
span.anio=A\u00f1o
span.anios=A\u00f1os
span.dia_s=Dia(s)
span.dia=D\u00eda
span.dias=Dias
span.mes_es=Mes(es)
span.mes=Mes
span.meses=Meses
tooltip.Abajo=Abajo
tooltip.ActualizarMenuNavegacion=Presione para actualizar el men&uacute; de navegaci&oacute;n.
tooltip.AgregarComentario=Presione para agregar un comentario.
tooltip.Arriba=Arriba
tooltip.Borrar=Presione para borrar el registro.
tooltip.Editar=Presione para editar el registro.
tooltip.MoverDocumento=Presione para mover el documento.
tooltip.NuevaCarpeta=Presione para crear una nueva carpeta.
tooltip.NuevoDocumento=Presione para crear un nuevo documento.
tooltip.VerAgenda=Presione para ver la agenda.
tooltip.VerComentarios=Presione para ver los comentarios.
tooltip.VerCuestionario=Presione para ver el cuestionario.
tooltip.VerDetalle=Presione para ver el detalle.
tooltip.VerMinuta=Ver minuta.
tooltip.VerPreguntasAuditoria=Presione para ver las preguntas de la auditor&iacute;a.
quejas.quejasUR.controlQuejas=Control de quejas.
quejas.quejasUR.busquedaTipo=Tipo.
quejas.quejasUR.redaccion=Redacci\u00f3n de la queja.
accion.acciongenericahandle.javascript.Razonporlaquenoprocede=- Raz\u00f3n por la que no procede.
boton.AceptarReal=Aceptar
edit.user.exito.reset=La clave del usuario ha sido actualizada correctamente \u00bfDesea reiniciar el sistema?
documentos.tag.relacionados=Documentos relacionados
accion.accionatomarhandle.javascript.LaAccionDebeVerificarseDespuesDelDiaDeHoy=La acci&oacute;n debe verificarse despu&eacute;s del d&iacute;a de hoy
accion.accionatomarhandle.javascript.LaAccionDebeImplementarseDespuesDelDiaDeHoy=La acci&oacute;n debe implementarse despu&eacute;s del d&iacute;a de hoy
components.comboYesNo.Yes=Si
components.comboYesNo.No=No
administrator.login.cerrarsesion.SeCerroSesion=Se Cerro Sesion
login.login.habilitarcookies=Es necesario habilitar las cookies en su navegador.
components.loader.cargando=Cargando...
menu.folderTreeLeftFrame.config.Interface=Interfaz
menu.folderTreeLeftFrame.config.Sistema=Sistema
menu.folderTreeLeftFrame.config.InterfaceTRESS=TRESS
menu.folderTreeLeftFrame.config.settings=Configuraci\u00f3n
menu.folderTreeLeftFrame.Hisrotial=Historial de accesos
menu.folderTreeLeftFrame.DebugInformation=Informaci\u00f3n de depuraci\u00f3n
menu.folderTreeLeftFrame.Jerarquia=Jerarqu\u00eda organizacional
menu.folderTreeLeftFrame.Unidaddenegocio=Unidad de negocio
menu.folderTreeLeftFrame.Plantas=${Facility}
menu.folderTreeLeftFrame.Departamentos=Departamentos
menu.folderTreeLeftFrame.Building=Edificio
menu.folderTreeLeftFrame.DepartamentosPlantas=Departamentos / 
menu.folderTreeLeftFrame.ProcesosDepartamentos=Procesos / Departamentos
menu.folderTreeLeftFrame.Roles=Roles
menu.folderTreeLeftFrame.Perfil=Perfil
menu.folderTreeLeftFrame.Puesto=Puesto
menu.folderTreeLeftFrame.Catalogos=Cat\u00e1logos
menu.folderTreeLeftFrame.Clausulas=Puntos de las normas
menu.folderTreeLeftFrame.ClauseType=Normas
menu.folderTreeLeftFrame.AuditType=Tipos de Auditor\u00edas
menu.folderTreeLeftFrame.Calificaciones=Calificaciones
menu.folderTreeLeftFrame.Tiposquejas=Tipos de an\u00e1lisis de quejas
menu.folderTreeLeftFrame.Indicadores=Indicadores
menu.folderTreeLeftFrame.Equipos=Equipos
menu.folderTreeLeftFrame.Foliosdeequipo=Folios de equipo
menu.folderTreeLeftFrame.Patronesdemedicion=Patrones de medici\u00f3n
menu.folderTreeLeftFrame.Tiposdeequipos=Tipos de equipos
menu.folderTreeLeftFrame.Estatusdeequipos=Estatus de equipos
menu.folderTreeLeftFrame.Variablesdemedicion=Variables de medici\u00f3n
menu.folderTreeLeftFrame.Unidadesdemedicion=Unidades de medici\u00f3n
menu.folderTreeLeftFrame.Tipodeservicio=Tipo de servicio
menu.folderTreeLeftFrame.Resultadodeservicio=Resultado de servicio
menu.folderTreeLeftFrame.Gruposdeequipos=Grupos de equipos
menu.folderTreeLeftFrame.Consolidadoderespuestas=Consolidado de respuestas
menu.folderTreeLeftFrame.Desechados=Desechados
menu.folderTreeLeftFrame.Servicios=Servicios
menu.folderTreeLeftFrame.Programacion=Programaci\u00f3n
menu.folderTreeLeftFrame.Serviciosprogramados=Servicios programados
menu.folderTreeLeftFrame.Planeados=Planeados
menu.folderTreeLeftFrame.NoPlaneados=No Planeados
menu.folderTreeLeftFrame.Historialdeservicios=Historial de servicios
menu.folderTreeLeftFrame.Servicionoplaneado=Servicio no planeado
menu.folderTreeLeftFrame.Metricadeservicio=M\u00e9trica de servicio
menu.folderTreeLeftFrame.Procesos=Procesos
menu.folderTreeLeftFrame.Calendar=Calendario de Suspensi\u00f3n
configuracion.repositoriohandle.javascript.Lainformacionnoseraguardada=La informaci\u00f3n no ser\u00e1 guardada.\\n\u00bfEst\u00e1 seguro de cancelar?
menu.folderTreeLeftFrame.LugaresdeAlmacenamiento=Lugares de Almacenamiento
common.input.FUENTESDEACCIONES=Fuentes de hallazgos
common.input.FUENTESDEQUEJAS=FUENTES DE QUEJAS
common.input.TiposdeAnalisisdeQuejas=Clasificaci\u00f3n
common.input.AltadeTIPOSDECALIFICACIONES=Alta de TIPOS DE CALIFICACIONES
common.input.obligatorio255caracteres=Este campo es obligatorio y admite texto alfanum\u00e9rico de hasta 255 caracteres.
common.input.obligatorio=Este campo es obligatorio
common.comentario.Agregueuncomentario=Agregue un comentario
configuracion.userhandle.RolenValidaciones=Rol en Validaciones:
evaluaciones.evalAnswerSave.Losdatosseguardaroncorrectamente=Los datos se guardaron correctamente.
evaluaciones.evalAnswerSave.Ocurriounerroralguardarsusrespuestas=Ocurri\u00f3 un error al guardar sus respuestas, contacte al administrador del sistema si el problema persiste.
evaluaciones.evalAnswerSave.Laencuestafuecontestadaparcialmente=La encuesta fue contestada parcialmente, favor de terminarla despu\u00e9s.
evaluaciones.evalAnswerSave.Elformatoelectronicofuellenadoparcialmente=El formato electr\u00f3nico fue llenado parcialmente, puede terminar de llenarlo despu\u00e9s.
evaluaciones.evalOptionEdit.Estaseguroquedeseaborrarlaopcion=\u00bfEst\u00e1 seguro que desea borrar la opci\u00f3n?
evaluaciones.evalOptionEdit.Laopcionfueeliminadasatisfactoriamente=La opci\u00f3n fue eliminada satisfactoriamente.
evaluaciones.evalOptionEdit.Laopcionnopudosereliminada=La opci\u00f3n no pudo ser eliminada.
evaluaciones.questionQuestionEdit.Numerodeopciones=N\u00famero de opciones:
evaluaciones.questionQuestionEdit.Opciones=Opciones:
evaluaciones.questionQuestionEdit.Esobligatoria=Es obligatoria:
evaluaciones.questionQuestionEdit.Losdatosfueronactualizadoscorrectamente=Los datos fueron actualizados correctamente.
evaluaciones.questionQuestionEdit.Lapreguntaseguardocorrectamente=La pregunta seguardo correctamente
evaluaciones.evalSave.Laevaluacionfueactualizadasatisfactoriamente=La evaluaci\u00f3n fue actualizada satisfactoriamente.
evaluaciones.evalSave.Nosepudieronguardarsatisfactoriamentelosdatos=No se pudieron guardar satisfactoriamente los datos.
alert.Ocurriounerroralguardarelregistro=Ocurrio un error al guardar el registro
evaluaciones.evalSectionEdit.CREARSECCION=CREAR SECCION
evaluaciones.evalSectionEdit.Seccion=Secci\u00f3n:
evaluaciones.encuestaslist.Elcuestionarionocuentaconpreguntas=- El cuestionario no cuenta con preguntas - 
evaluaciones.encuestashandle.DocumentodeTips=Documento de Tips: 
evaluaciones.encuestashandle.Presioneaqui=Presione aqu\u00ed.
evaluacion.reportCreate.Debesseleccionarlaevaluacion=Debes seleccionar la evaluaci\u00f3n.
evaluacion.reportCreate.Debeseleccionaruntipodereporte=Debe seleccionar un tipo de reporte.
evaluacion.reportCreate.Debeseleccionarlavariableporlaquegraficara=Debe seleccionar la variable por la que graficar\u00e1.
evaluacion.reportCreate.Debeseleccionarunmes=Debe seleccionar un mes.
evaluacion.reportCreate.Debeseleccionaruna\u00f1o=Debe seleccionar un a\u00f1o.
evaluacion.reportCreate.Debeseleccionarunaquincena=Debe seleccionar una quincena.
evaluacion.reportCreate.Debeseleccionarunmesinicial=Debe seleccionar un mes inicial.
evaluacion.reportCreate.Debeseleccionaruna\u00f1oinicial=Debe seleccionar un a\u00f1o inicial.
evaluacion.reportCreate.Debeseleccionarunmesfinal=Debe seleccionar un mes final.
evaluacion.reportCreate.Debeseleccionaruna\u00f1ofinal=Debe seleccionar un a\u00f1o final.
audit.reportshandle.Reporte=Reporte:
audit.reportshandle.Plantarequerida=El campo ${facility} es requerido.
audit.reportshandle.Reportesdeauditorias=Reportes de auditor\u00edas
audit.reportshandle.Hallazgospordepartamentoenprocesos=Hallazgos por departamento en procesos
audit.reportshandle.Resultadosdeauditoria=Resultados de auditor\u00eda
audit.reportshandle.Auditoriasagendadas=Auditor\u00edas agendadas
audit.reportshandle.Fechadelaauditoria=Fecha de la auditor\u00eda:
audit.reportshandle.Formato=Formato:
audit.reportshandle.PDF=PDF
audit.reportshandle.HTML=Pagina Web (HTML)
audit.reportshandle.Excel=Microsoft Excel (XLS)
audit.reportshandle.Generarreporte=Generar reporte
boton.accion.Iramisacciones=Ir a mis acciones
documentos.documentonocontroladohandle.SubirArchivo=Subir Archivo
documentos.documentospendientes.Pendientes=Pendientes
documentos.documentospendientes.Pendientesdeequipos=Pendientes de equipos
pending.handle.Porconfirmar=Por confirmar:
pending.handle.Porrealizar=Por realizar:
pending.handle.PorrealizarAuditor=Por realizar (Auditor de apoyo):
pending.handle.PorAutorizarCambio =Por autorizar cambio de fecha:
pending.handle.PorAutorizarCambioManager =Por autorizar cambio de fecha (encargado del m\u00f3dulo):
pending.handle.Poraceptarresultados=Por aceptar resultados:
pending.handle.AccionesporAgregarAccionesaTomar=Acciones por Agregar Acciones a Tomar:
pending.handle.Equiposporreemplazar=Equipos por reemplazar:
pending.handle.Equiposporprogramarservicios=Equipos por programar servicios:
pending.handle.ProgramacionesporAutorizar=Programaciones por autorizar:
pending.handle.ServiciosporAutorizar=Servicios por autorizar:
pending.handle.Equiposconserviciosporrealizar=Equipos con servicios por realizar:
pending.handle.Equiposconserviciosvencidos=Equipos con servicios vencidos:
pending.handle.Pendientesdeencuestas=Pendientes de encuestas
pending.handle.Porresponder=Por responder:
accion.acciongenericahandle.VERDETALLEDEACCION=VER DETALLE DE ACCI\u00d3N
quejas.quejashandle.msjusuarioresponsableinactivo=El usuario responsable de darle respuesta a la queja se encuentra inactivo, eliga otro.
poll.poll.handle.Estatus=Estatus:
poll.poll.handle.Clave=Clave:
survey.Seccion=Secci\u00f3n
survey.Abierta=Abierta
survey.Menu=Men\u00fa
survey.Opcionmultiple=Opci\u00f3n m\u00faltiple
survey.Unarespuesta=Una respuesta
survey.SioNo=Si o No
survey.Matriz=Matriz
survey.Matrizverticaldemenus=Matriz vertical de men\u00fas
survey.Instrucciones=Instrucciones
survey.AbiertaRenglones=Abierta (Renglones)
survey.Matrizmultiple=Matriz m\u00faltiple
survey.Matrizdemenus=Matriz de men\u00fas
boton.Guardarcopia=Guardar copia
survey.mostrarsuinformacion=Clic en un elemento para mostrar su informaci\u00f3n.
survey.Mostrarmaspreguntas=Mostrar m\u00e1s tipos de preguntas
survey.Activardesactivarvalores=Activar y desactivar valores de respuesta
survey.Unidadesdenegocioaplicar=${Facilities} a las que aplica el cuestionario
survey.Propiedades=Propiedades generales de elementos del cuestionario
survey.Mostrarmenospreguntas=Mostrar menos tipos de preguntas
survey.Titulodelcuestionario=T\u00edtulo del cuestionario (clic para editar)
survey.alert.Lainformacionnoguardadaseperdera=La informaci\u00f3n no guardada se perder\u00e1, \u00bfEst\u00e1 seguro de que desea cancelar?
survey.message.borrarlosvaloresdelasrespuestasyaasignadas=\u00bfEst\u00e1 seguro de que desea borrar los valores de las respuestas ya asignadas?
survey.message.sieligesilasmismasyanopodranserrecuperadas=si elige "Si" las mismas ya no podr\u00e1n ser recuperadas.
survey.message.Unidadesdenegocioalasqueaplicaelcuestionario=${Facilities} a las que aplica el cuestionario
survey.message.Estecuestionarioyaestasiendousado=Este cuestionario ya est\u00e1 siendo usado en un encuesta, no se permitir\u00e1 guardar cambios
survey.message.Regresandoalistado=Regresando a listado
survey.Estatus\u200b=Estatus
survey.Activo=Activo
survey.Bloqueado=Bloqueado
survey.Sinroles=Sin roles
survey.Conroles=Con roles
survey.Esquemadetienda\u200b=Esquema de tienda\u200b
survey.Plantas=${Facilities}
survey.Agregarplanta=Agregar ${facility}
survey.Finalizar=Finalizar
survey.message.noRegMessage=No se han agregado registros
survey.Unidadesdenegocio=${Facilities}
title.Agregar=Agregar
title.UnidadOrg=Unidad Org.
auditoria.auditoriaquestionhandle.Lapreguntafueborradacorrectamente=La pregunta fue borrada correctamente.
survey.surveycapture.LaOperacionhasidorealizadaconexito=La Operaci\u00f3n ha sido realizada con \u00e9xito.
survey.surveycapture.ErrorDetectado=Error Detectado.
survey.surveycapture.completarlosdatos=Favor de completar los datos. Los datos con un asterisco (*) son obligatorios.
survey.surveycapture.cuestionarioyafuellenado=Este cuestionario ya fue llenado en su totalidad y no cuenta con permiso para ver las respuestas.
survey.surveycapture.oldVersion=Existe una nueva versi\u00f3n disponible del formulario. Si requiere llenar la nueva versi\u00f3n solicite la cancelaci\u00f3n a {requestAuthor} en caso contrario contin\u00fae con el llenado.
survey.surveycapture.Aceptarresultados=Aceptar resultados
survey.surveycapture.Guardaravances=Guardar avances
survey.surveycapture.Terminar=Terminar
survey.surveycapture.Hallazgos=Hallazgos
survey.surveycapture.Imprimir=Imprimir
survey.surveycapture.Salir=Salir
survey.surveycapture.Cerrar=Cerrar
survey.surveycapture.Ocurriounerror=Ocurri\u00f3 un error!
survey.surveycapture.Entendido=Entendido
survey.surveycaptureend.graciasparticipacion=Muchas gracias por su participaci\u00f3n en la encuesta de \u00cdndice de Satisfacci\u00f3n de L\u00edder de tienda, sus respuestas nos ayudar\u00e1n a trabajar mejor para ti.
survey.surveycaptureend.encuestacontestadadetalleinvalida=Esta encuesta ya fue contestada y usted est\u00e1 intentando ver su detalle de una manera inv\u00e1lida.
survey.surveycaptureend.Cuestionario=Cuestionario
survey.surveycaptureend.Cuestionariofinalizadonopermisoespera=Este cuestionario ha sido llenado y no cuenta con permiso para visualizarlo, ser\u00e1 enviado a la pantalla principal de la aplicaci\u00f3n en 
survey.surveycaptureend.Segundos=segundos
indicadores.indicadorhandle.Responsabledellenado=Responsable de llenado:
indicadores.indicadorhandle.Usuarios=Usuarios
proyectos.proyectohandle.SeAgregaraUnaMetaDeseaContinuar=Se agregar\u00e1 una meta. \u00bfDesea continuar?
proyectos.proyectohandle.NoSeHanRealizadoCambiosDeseaContinuar=No se ha realizado ning\u00fan cambio que guardar, \u00bfDesea continuar y regresar a Control de Proyectos?
proyectos.proyectohandle.SeHanRealizadoCambiosDeseaContinuar=Se han realizado cambios en el proyecto, \u00bfDesea guardarlos y continuar?
documentos.pdfviewer.Elmenucontextualestadeshabilitado=El men\u00fa contextual est\u00e1 deshabilitado
documentos.pdfviewer.Inicio=Inicio
documentos.pdfviewer.Atras=Atr\u00e1s
documentos.pdfviewer.Siguiente=Siguiente
documentos.pdfviewer.Ultimo=\u00daltimo
documentos.componente.ErrorNoseencontrounobjetodecodigoJS=\u00a1Error: No se encontro un objeto de codigo JS, ID buscada:("
documentos.componente.seleccione=-- SELECCIONE --
documentos.componente.UnidadProductiva=Unidad Productiva
documentos.componente.Departamento=Departamento
documentos.componente.Proceso=Proceso
documentos.componente.Area=Area
documentos.componente.Usuario=Usuario
documentos.componente.Filtros=Filtros:
documentos.componente.Nombre=Nombre:
documentos.documentoshandle.AgregarLectores=Lectores
documentos.documentoshandle.Descargar=Descargar
documentos.documentoshandle.Agregar=Agregar
documentos.documentoshandle.Cerrar=Cerrar
documentos.documentoshandle.Clave2=Clave
documentos.documentoshandle.Puestos=Puesto(s)
documentos.documentoshandle.Plantas=${Facilities}
documentos.documentoshandle.UnidadesOrg=Unidad(es) Org.
documentos.documentoshandle.Descripcion=Descripci\u00f3n
documentos.documentoshandle.NosehanagregadoLectores=No se han agregado Lectores
documentos.documentoshandle.QuitarLector=Quitar Lector
documentos.documentoshandle.AsignarDocumentosRelacionados=Asignar Documentos Relacionados
documentos.documentoshandle.AgregarDocumentoRelacionado=Documento Relacionado
documentos.documentoshandle.Verificador=Verificador
documentos.documentonocontroladohandle.SubirArchivo=Subir Archivo:
documentos.documentoslistadocp.Carpeta=Carpeta
documentos.documentoslistadocp.Mover=Mover
documentos.login.principal.Repositoriodedocumentos=Repositorio de documentos, doble clic aqu\u00ed para ver las carpetas
documentos.documentoslist.FechaaCancelacion=Fecha Cancelaci\u00f3n
documentos.documentlist.Revision=Revisi\u00f3n
documentos.slideshow.GettheFlashPlayer=Consigue el Reproductor Flash
documentos.slideshow.toseethisplayer=para ver este reproductor.
documentos.referenciahandle.\u00bfEstaseguroquedeseaagregarlosdocumentosseleccionados?=\u00bfEst\u00e1 seguro que desea agregar los documentos seleccionados?
documentos.referenciahandle.Sedebeagregaralmenosundocumentoparareferenciar=Se debe agregar al menos un documento para referenciar
documentos.try.download.later.Descarga=Descarga
documentos.try.download.later.ElarchivosolicitadoestasiendoconvertidoaformatoPDF=El archivo solicitado est\u00e1 siendo convertido a formato PDF para su visualizaci\u00f3n, espere por favor...
documentos.uploadfile.Ocurriounerrordeconexion=Ocurri\u00f3 un error de conexion al intentar subir el archivo, intente de nuevo \u00f3 si el problema persiste contacte al administrador.
documentos.uploadfile.Secompletoelproceso=Secomplet\u00f3 el proceso.
documentos.try.download.later.Ladescargasereintentara=\ (La descarga se reintentar\u00e1 en
documentos.try.download.later.segundos=\ segundos
documentos.solicitudeshandle.Espere...=Espere...
documentos.solicitudeshandle.documentoenestadode=documento en estado de
documentos.solicitudeshandle.solicitudenestadode=solicitud en estado de
documentos.solicitudeshistory.DETALLEDEHISTORIALDELASOLICITUD=DETALLE DE HISTORIAL DE LA SOLICITUD
documentos.solicitudeshistory.PrimeraVersion=Primera Versi\u00f3n
documentos.solicitudeshistory.VersionActual=Versi\u00f3n Actual
documentos.referenciahandle.Seleccione...=Seleccione...
cuestionarios.cuestionarioshandle.Cuestionariosdeauditorias=Cuestionarios de auditor\u00edas
cuestionarios.cuestionarioshandle.Agreguealmenosunaseccion=Agregue al menos una secci\u00f3n.
cuestionarios.preguntas.Seleccionealmenosunaseccion=Seleccione al menos una secci\u00f3n, si a\u00fan no ha agregado una, puede hacerlo en el icono de \\"Secci\u00f3n\\" en el \\"Control de Cuestionarios\\".
cuestionarios.preguntas.Selecioneyagreguealmenosunapreguntadelaseccion=Seleccione y agregue al menos una pregunta de la secci\u00f3n.
cuestionarios.preguntasobv.Respuestasdeauditorias=Respuestas de auditor\u00edas
solicitudes.solicitudeshandle.RegresarSolicitud=Regresar Solicitud
solicitudes.solicitudeshandle.DocumentoElectronico\:=Documento Electr\u00f3nico:
solicitudes.solicitudeshandle.RegresarlaSolicitud=Regresar la Solicitud
solicitudes.solicitudeshandle.Primerodebeseleccionarundocumento=Primero debe seleccionar un documento
solicitudes.solicitudeshandle.Aunnohacesninguncambio=A\u00fan no haces ningun cambio, para continuar debes cambiar un valor
solicitudes.solicitudeshandle.Especifiquelarazonporlaquesequiereregresareldocumento=Especifique la raz\u00f3n por la que se quiere regresar el documento
solicitudes.solicitudeshandle.Soloseaceptannumeros=Solo se aceptan n\u00fameros
solicitudes.solicitudeshandle.Espereaquesecarguenlosdocumentos.=Espere a que se cargen los documentos
documentos.solicitudeshandle.Archivoenprocesodemodificacion=Archivo en proceso de modificaci\u00f3n
documentos.solicitudeshandle.RevisionAnterior=Revisi\u00f3n Anterior
documentos.documentoshandle.Nosehanagregadolectores=No se han agregado lectores
indicadores.indicadorhandle.Formadecalculo=Forma de c\u00e1lculo:
indicadores.revisionindicadorhandle.Detallederegistros=Detalle de registros
indicadores.revisionindicadorhandle.periodo=periodo
indicadores.revisionindicadorhandle.mensajeIsValid=El hallazgo ser\u00e1 dado de alta e iniciar\u00e1 su seguimiento en el m\u00f3dulo de acciones<br/>
indicadores.revisionindicadorhandle.mensajeIsValid2=\u00bfSeguro de que desea continuar?
indicadores.revisionindicadorhandle.mensajeIsValid3=El hallazgo ha sido dado de alta con \u00e9xito.
indicadores.revisionindicadorhandle.mensajeIsValid4=El hallazgo no fue dado de alta.
indicadores.revisionindicadorhandle.mensajeValidatRules=Redacte un hallazgo
indicadores.revisionindicadorhandle.mensajeValidatRules2=Redacte una consecuencia
indicadores.revisionindicadorhandle.mensajeValidatRules3=Redacte un t\u00edtulo
indicadores.revisionindicadorhandle.mensajeValidatRules4=Seleccione una fuente
indicadores.revisionindicadorhandle.mensajeValidatRules5=Seleccione el tipo de acci\u00f3n
indicadores.objectivelist.ListadeCuestionarios=Lista de Cuestionarios
configuracion.repositoriohandle.javascript.RegistroDuplicado=Ha ocurrido un error, verifique que el registro no est\u00e9 duplicado.
audit.reportshandle.Planta=${Facility}:
audit.reportshandle.Proceso=Proceso:
survey.TituloPropiedades=Propiedades
accion.acciongenericahandle.Clave=Clave
common.input.SinLiga=--SIN LIGA--
common.input.Requiereanalisis=Requiere an\u00e1lisis
common.input.SI=SI
common.input.Ligaa=Liga a:
common.input.ModulodeAuditorias=-M\u00f3dulo de Auditor\u00edas
common.input.ModulodeIndicadores=-M\u00f3dulo de Indicadores
common.input.ModulodeQuejas=-M\u00f3dulo de Quejas
common.input.ModulodeValidaciones=-M\u00f3dulo de Validaciones
common.input.Seactualizaronlosdatosdelcatalogocorrectamente=Se actualizaron los datos del cat&aacute;logo correctamente.
common.input.LainformacionnoseraguardadaEstasegurodecancelar=La informaci\u00f3n no ser\u00e1 guardada.\\n\u00bfEst\u00e1 seguro de cancelar?
common.input.RequiereAnalisis=Requiere an\u00e1lisis:
common.input.SI=SI
common.input.NO=NO
menu.folderTreeLeftFrame.DepartamentosPlantas2=${Facilities}
documentos.documentoslector.Departamento=Departamento
documentos.documentoslector.current.Departamento=Departamento actual
documentos.documentoslector.lastDepartamento=Departamento anterior
boton.Eliminar=Eliminar
survey.surveycapture.Calificacionnumerica=Calificaci\u00f3n num\u00e9rica
menu.folderTreeLeftFrame.EstadodeAcciones=Estado de acciones
accion.acciongenericahandle.Seguimientodeaccion=Seguimiento de acci\u00f3n
accion.accionatomarhandleamc.RazondelaultimaModificaciondelaI=Raz\u00f3n de la \u00faltima Modificaci\u00f3n de la<br>Fecha de implementaci\u00f3n
accion.accionatomarhandleamc.RazondelaultimaModificaciondelaV=Raz\u00f3n de la ultima modificaci\u00f3n de la<br>Fecha de Verificaci\u00f3n
survey.sinclausula=--Sin cl\u00e1usula--
reportesgenerales.planta=${Facility}:
menu.folderTreeLeftFrame.ListaDeDistribucionPorDocumento=Por documento
menu.folderTreeLeftFrame.ListaDeDistribucionPorUsuario=Por usuario
menu.folderTreeLeftFrame.ListaDeDistribucionListaCompleta=Lista completa
menu.folderTreeLeftFrame.ListaDeDistribucion=Lista de distribuci\u00f3n
documentos.documentoshandle.Listadodepuestos=Listado de puestos asignados para generar copias controladas
documentos.documentoshandle.Listadodeusuarios=Listado de usuarios que deben o han le\u00eddo el documento
documentos.documentoshandle.Agregarpuestos=Agregar puestos
documentos.documentoshandle.Documentosrelacionados=Documentos relacionados
documentos.documentoshandle.Ocultar=Ocultar
documentos.documentoshandle.Terminar=Terminar
documentos.documentoshandle.Fechadelectura=Fecha de lectura
documentos.documentoshandle.Noleido=No leido
documentos.documentoshandle.Leidosinpendiente=Leido sin pendiente
documentos.documentoshandle.Leidodesdependientes=Leido desde pendientes
documento.documentoshandle.NosehanagregadoLectores=No se han agregado lectores
auditoria.auditorialector.EstadoCerrada=El estado actual es: cerrado
configuracion.configuacionhandle.colordelsistema=Color del sistema:
poll.pollregistrylist.Controlderegistros=Control de registros
proyectos.proyectohandle.javascript.Responsable=Responsable
configuracion.userhandle.Idioma=Idioma:
auditoria.misauditorias.EstadoConfirmada,PresioneparaMarcarcomoRealizada=Estado confirmada, presione para marcar como realizada
auditoria.misauditorias.EstadoAceptada,PresioneparaCerrarla=Estado aceptada, presione para cerrarla
indicadores.indicadorlist.Aunnohayobjetivoscapturados=Aun no hay objetivos capturados
proyectos.proyectohandle.SeAgregaraUnaMetaDeseaGuardarLosCambiosDelProyectoAntesDeContinuar=Se agregar\u00e1 una meta.  \u00bfDesea guardar los cambios del proyecto antes de continuar?
documentos.documentoshandle.Nosehanasignadopuestosaldocumentocontrolado=No se han asignado puestos al documento controlado.
documentos.documentoshandle.Haocurridounerroralintentargenerarlacopiacontrolada=Ha ocurrido un error al intentar generar la copia controlada.
accion.accionatomarhandle.javascript.Lafechadeimplementaciondebesermayoralaactual=La fecha de implementaci\u00f3n debe ser mayor o igual a la actual
accion.accionatomarhandle.javascript.Lafechadeverificaciondebesermayoralaactual=La fecha de verificaci\u00f3n debe ser mayor o igual a la actual
documentos.documentoshandle.reaprobacion=Reaprobaci\u00f3n
documentos.documentoshandle.nuevo=Nuevo
documentos.documentoshandle.modificacion=Modificaci\u00f3n
documentos.documentoshandle.editDetails=Edici\u00f3n de detalles
documentos.documentoshandle.cancelacion=Cancelaci\u00f3n
documentos.documentoshandle.tiempo=Tiempo de almacenamiento
documentos.documentoshandle.disposicion=Disposici\u00f3n
documentos.documentoshandle.tipodesolicitud=Tipo de solicitud
proyectos.actividad.avance.aceptar=Agregar al avance
proyectos.proyectohandle.javascript.Planta=${Facility}
proyectos.proyectohandle.Planta=${Facility}
indicadores.indicadorhandle.PlantaNecesario=-Debe asignar ${aFacility}.\\n
indicadores.indicadorhandle.txtMetaNecesario=-Debe asignar una meta.\\n
action.mail.code=Clave: 
action.mail.title=T\u00edtulo: 
action.mail.author=Autor: 
action.mail.created_on=Fecha de creaci\u00f3n: 
action.mail.source=Fuente: 
action.mail.action=Hallazgo: 
action.mail.consequence=Consecuencia: 
action.mail.department=Departamento: 
action.mail.accepted_by=Aceptada por:
action.mail.accepted_on=Fecha de aceptaci\u00f3n: 
action.mail.attendant=Responsable de la acci\u00f3n: 
action.mail.does_it_proceed=\u00bfProcede?: 
action.mail.root_causes_analisys=An\u00e1lisis de causas: 
action.mail.evaluation_date=Fecha de evaluaci\u00f3n: 
action.mail.evaluation_result=Resultados de la evaluaci\u00f3n: 
action.mail.approved_by=Aprobado por: 
action.mail.approval=Aprobaci\u00f3n: 
action.mail.approved_on=Fecha de aprobaci\u00f3n: 
action.mail.status=Estado: 
action.mail.closed_on=Fecha de cierre:  
action.mail.attachment_number=N\u00famero de adjuntos: 
action.mail.accomplished_on=Fecha de cumplimiento: 
action.mail.not_proceeding_reazon=Raz\u00f3n por la que no procede: 
action.mail.effectiveness_result=Resultados de efectividad: 
action.mail.parent_action=Acci\u00f3n padre: 
action.mail.contingency=Contingencia: 
mail.request.requestApproved=La solicitud que creaste ha sido aprobada.
Proyectos.hrs=hrs
action.mail.not_proceed=No
action.mail.proceed=Si
action.mail.open=Abierta
action.mail.closed=Cerrada
actividad.actividadhandle.javascript.-TrabajoRealizadoDisminuido=-No es posible reducir la cantidad de trabajo realizado de
quejas.handle.EstaSeguroSambiarDepartamentoQueja=\u00bfEsta seguro de que desea cambiar el departamento al que esta dirigido la queja?
documentos.documentoslector.LugarDeAlmacenamiento=Lugar de almacenamiento
survey.surveycapture.CancelFillForm=Cancelaci\u00f3n definitiva
accion.acciongenericahandle.Aceptacion=Aceptaci&oacuten:
message.noValidValue=El valor ingresado no es v\u00e1lido
message.invalidSelectedValue=El valor seleccionado no es v\u00e1lido.
menu.folderTreeLeftFrame.AuditProgram=Programa de Auditor\u00edas
menu.folderTreeLeftFrame.CatalogoActivities = Actividades
menu.folderTreeLeftFrame.CatalogoActivitiesType = Tipo de Actividades
menu.folderTreeLeftFrame.CatalogoActivitiesPriority = Prioridad de Actividades
menu.folderTreeLeftFrame.CatalogoActivitiesSource = Fuente de Actividades
menu.folderTreeLeftFrame.CatalogoActivitiesObjective = Objetivo de actividades
pending.handle.AuditsActivityToComplete = Actividades por realizar:
pending.handle.AuditsActivityToVerify = Actividades por verificar:
pending.handle.AuditsActivityToVerifyNotApply = Actividades por verificar como cancelada:
survey.surveycapture.Activities = Actividades
pending.handle.header.ActivitiesPendings = Pendientes de actividades
pending.handle.ActivitiesToComplete = Por realizar:
pending.handle.ActivitiesToVerify = Por verificar:
pending.handle.ActivitiesToVerifyNotApply = Por verificar como cancelada:
menu.folderTreeLeftFrame.Activities = Control
menu.folderTreeLeftFrame.MyActivity = Mis actividades
menu.folderTreeLeftFrame.ControlActivity = Control de actividades
menu.folderTreeLeftFrame.ControlFindingActivity = Control de actividades
menu.folderTreeLeftFrame.ActivityRecycleBin = Papelera de actividades
menu.folderTreeLeftFrame.RecurrentMeetingControl = Control de recurrencias
menu.folderTreeLeftFrame.MeetingRecurringRecycleBin = Papelera de recurrencias
menu.folderTreeLeftFrame.ConsolidatedActivities = Consolidado de actividades
boton.SaveTemplate=Guardar como plantilla
menu.folderTreeLeftFrame.PapeleraIndicator=Papelera
menu.folderTreeLeftFrame.dynamicField=Campos din\u00e1micos
menu.folderTreeLeftFrame.PapeleraAudit=Papelera de auditor\u00edas
message.accept=Aceptar
message.noValidValue=El valor ingresado no es v\u00e1lido
accion.acciongenericahandle.Aceptacion=Aceptaci&oacuten:
configuracion.userhandle.SearchInSubfolders=Realizar b\u00fasquedas por subcarpetas:
configuracion.userhandle.ShowExternalDialog=Mostrar di\u00e1logo de descarga del <br> instalador Bnext Launcher:
accion.accioncorreccioninmediata.javascript.fechaverificacio=La fecha de verificacion debe ser mayor que la fecha de implementacion
addedActionHistory=Se agreg\u00f3 el historial de comentarios.
CouldNotAddCommentsHistory=No se pudo agregar el historial de comentarios.
accion.accionatomarhandleamc.VerificadorContencion=Verificador de contenci&oacute;n
accion.accionatomarhandleamc.VerificadorActividad=Verificador de actividad
menu.folderTreeLeftFrame.automaticTasks = Tareas autom\u00e1ticas
menu.folderTreeLeftFrame.dailyTasks = Tareas diarias
menu.folderTreeLeftFrame.hourlyTasks = Tareas por hora 
menu.folderTreeLeftFrame.dailyMails = Correos diarios
menu.folderTreeLeftFrame.RecurrentMeetingControl = Control de recurrencias
menu.folderTreeLeftFrame.MeetingRecurringRecycleBin = Papelera de recurrencias
indicadores.revisionindicadorhandle.Indicador=Indicador
menu.folderTreeLeftFrame.ActivitiesReport=Reportes
actividad.actividadhandle.javascript.VerifiqueLosReponsables = Verifique los responsables
actividad.actividadhandle.javascript.ElResponsableNoPuedeSerElVerificador = El verificador no puede ser uno de los usuarios asignados como responsable.
actividad.actividadhandle.AceptadaPor=Aceptada por:
actividad.actividadhandle.RechazadaPor=Rechazada por:
actividad.actividadhandle.FechaDeAceptacion=Fecha de aceptaci\u00f3n:
actividad.actividadhandle.FechaDeRechazo=Fecha de rechazo:
general.reports.badHostError=Comuniquese con el administrador del sistema y pidale verificar los siguientes campos de la configuracion "URL del sitio, Folder del sitio, Host".