# Sample ResourceBundle properties file
isoblock.common.correo.tipoCorreo.auditoria = Aviso de participacion la auditor&iacute;a clave: AUD-
isoblock.common.correo.tipoCorreo.secuencia = Aviso del resultado del documento en la Secuencia de autorizacion clave: <b>SEC-
isoblock.common.correo.tipoCorreo.aviso = Aviso del sistema
isoblock.accion.accionMejoraContinua.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente de mejora contin&uacute;a en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionMejoraContinua.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionMejoraContinua.insertaAccionMejoraContinua = <br>&nbsp;&nbsp; Se ha reportado una acci&oacute;n de mejora contin&uacute;a que falta por ser asignada.
isoblock.accion.accionMejoraContinua.asignaAccionMejoraContinua = <br>&nbsp;&nbsp; Se le ha asignado como encargado de la implementaci&oacute;n de una acci&oacute;n de mejora contin&uacute;a.
isoblock.accion.accionMejoraContinua.setImplementada = <br>&nbsp;&nbsp;Una acci&oacute;n de mejora contin&uacute;a ha sido marcada como implementada lista para su verificaci&oacute;n, favor de atenderla. <br>
isoblock.accion.accionCorrectiva.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente en acciones correctivas en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionCorrectiva.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionCorrectiva.insertaAccionCorrectiva = <br>&nbsp;&nbsp; Se le ha asignado como encargado de la implementaci&oacute;n de una acci&oacute;n correctiva.
isoblock.accion.accionCorrectiva.setImplementada = <br>&nbsp;&nbsp; Una acci&oacute;n correctiva ha sido marcada como implementada lista para su verificaci&oacute;n y su an&aacute;lisis.<br>
isoblock.accion.accionGenerica.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de ACCIONES en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionGenerica.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionGenerica.insertaAccionGenerica = <br>&nbsp;&nbsp;Se ha creado una acci&oacute;n asignada a su departamento, favor de atenderla. Este aviso es informativo y no genera pendientes<br>
isoblock.accion.accionGenerica.insertaAccionGenerica1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionGenerica.asignaAccionGenerica = <br>&nbsp;&nbsp;Se le ha asignado una acci&oacute;n para su evaluaci&oacute;n y an&aacute;lisis, favor de atenderla. <br>
isoblock.accion.accionGenerica.asignaAccionGenerica1 = <br>&nbsp;&nbsp; El encargado del departamento al que se asign&oacute; la acci&oacute;n la ha indicado como no procedente. <br> Por el siguiente motivo: <br />
isoblock.accion.accionGenerica.setImplementada = <br>&nbsp;&nbsp;Una acci&oacute;n de mejora continua ha sido marcada como implementada lista para su verificaci&oacute;n, favor de atenderla.<br>
isoblock.auditoria.auditoria.updateAuditoria = <br>&nbsp;&nbsp; Se acaba de reprogramar una auditor&iacute;a, favor de atenderla.
isoblock.auditoria.auditoria.insertAuditoria = <br>&nbsp;&nbsp; Se ha creado una nueva auditor&iacute;a en la cual usted ha sido asignado como auditor encargado, favor de atenderla.
isoblock.auditoria.auditoria.noaceptaAuditoria = <br>&nbsp;&nbsp; El encargado no estuvo de acuerdo con la notificaci&oacute;n de auditor&iacute;a.
isoblock.auditoria.auditoria.aceptaAuditoria = <br>&nbsp;&nbsp; Se ha aceptado la realizaci&oacute;n de la auditor&iacute;a en la que usted est&aacute; participando, tiene un nuevo pendiente de 'Reportes de Auditor\u00edas por Realizar', favor de atenderlo.
isoblock.auditoria.auditoria.noaceptaAuditoriaReporte = <br>&nbsp;&nbsp; El encargado no estuvo de acuerdo con el reporte de auditor&iacute;a dando como argumento lo siguiente<br>
isoblock.auditoria.auditoria.aceptaAuditoriaReporte = <br>&nbsp;&nbsp; El reporte de auditor&iacute;a ya fue aprobado por todos los encargados.
isoblock.auditoria.auditoria.enviarCorreo.Estatus = Estado:
isoblock.auditoria.auditoria.enviarCorreo.Ubicacion = Departamento:
isoblock.auditoria.auditoria.enviarCorreo.Areas = Procesos:
isoblock.auditoria.auditoria.enviarCorreo.Fechainicio = Fecha inicio:
isoblock.auditoria.auditoria.enviarCorreo.Fechaterminacion = Fecha terminaci&oacute;n:
isoblock.auditoria.auditoria.enviarCorreo.Cuestionario = Cuestionario:
isoblock.auditoria.auditoria.enviarCorreo.Objetivos = Objetivos:
isoblock.auditoria.auditoria.enviarCorreo.Planeacion = Planeaci&oacute;n:
isoblock.auditoria.auditoria.enviarCorreo.Comentarios = Comentarios:
isoblock.documentos.documento.enviaNotificacion = Se ha creado una secuencia de autorizaci&oacute;n para el siguiente documento. Este aviso es informativo y no genera pendientes.
isoblock.accion.accionPreventiva.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente en acciones preventivas en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionPreventiva.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.common.threadFunction.checaAuditorias = <br>&nbsp;&nbsp; Dentro del sistema Bnext QMS, se ha vencido la fecha de realizaci&oacute;n en las siguientes AUDITOR&Iacute;AS\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Clave de auditor&iacute;a</b></td><td width\='70%' bgcolor\='\#FFFFFF'><b>Responsable</b></td></tr><tr><td colspan='2'><!--Se habilito el boton para la cancelaci&aacute;n de las auditor\u00edas--></td></tr>
isoblock.common.threadFunction.checaDocumentosPendientesEncargadoModuloDocumentos = <br>&nbsp;&nbsp; Hay <b>:number</b> documentos expirados en el sistema <b>:systemId</b>, con :month o mas meses de antiguedad en el que eres Encargado del M\u00f3dulo de Documentos y tienes pendiente atenderlos.<br><br>&nbsp;&nbsp;&nbsp;<b>Documento(s)\:</b>
isoblock.common.threadFunction.checaDocumentosSinPendientesEncargadoModuloDocumentos = <br>&nbsp;&nbsp; Hay <b>:number</b> documentos expirados en el sistema <b>:systemId</b>, con :month o mas meses de antiguedad en el que eres Encargado del M\u00f3dulo de Documentos.<br><br>&nbsp;&nbsp;&nbsp;<b>Documento(s)\:</b>
isoblock.common.threadFunction.checaDocumentosPendientesEncargadoDocumentos = <br>&nbsp;&nbsp; Hay <b>:number</b> documentos expirados en el sistema <b>:systemId</b>, con :month o mas meses de antiguedad en el que eres Encargado de Documentos y tienes pendiente atenderlos.<br><br>&nbsp;&nbsp;&nbsp;<b>Documento(s)\:</b>
isoblock.common.threadFunction.checaDocumentosSinPendientesEncargadoDocumentos = <br>&nbsp;&nbsp; Hay <b>:number</b> documentos expirados en el sistema <b>:systemId</b>, con :month o mas meses de antiguedad en el que eres Encargado de Documentos.<br><br>&nbsp;&nbsp;&nbsp;<b>Documento(s)\:</b>
isoblock.common.threadFunction.checaDocumentosSinPendientesAutorEdicionActual = <br>&nbsp;&nbsp; Hay <b>:number</b> documentos expirados en el sistema <b>:systemId</b>, con :month o mas meses de antiguedad en el que eres el Autor de la Edici\u00f3n Actual.<br><br>&nbsp;&nbsp;&nbsp;<b>Documento(s)\:</b>
isoblock.common.threadFunction.avisoMes = <br>&nbsp;&nbsp; Aviso para la impresion del Reporte de Acciones.
isoblock.common.threadFunction.checaAccionesUbicacion=<br>&nbsp;&nbsp; Dentro del sistema Bnext QMS, existen acciones a tomar pertenecientes a su departamento que han alcanzado su fecha de realizaci&oacute;n en las siguientes ACCIONES\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Clave de acci&oacute;n</b></td><td width\='70%' bgcolor\='\#FFFFFF'><b>Responsable</b></td></tr>
isoblock.common.Utilities.creaForma.clave=Clave:
isoblock.accion.accionGenerica.vchClave.PorAsignarse=Por Asignarse
isoblock.accion.accionGenerica.accionT.ACCION=ACCI&Oacute;N
isoblock.accion.accionGenerica.comboProcede.SI=SI
isoblock.accion.accionGenerica.comboProcede.NO=NO
isoblock.accion.accionGenerica.comboAcepto.NOACEPTO=NO ACEPTO
isoblock.accion.accionGenerica.comboAcepto.SIACEPTO=SI ACEPTO
isoblock.accion.accionGenerica.comboEstado.REPORTADA=REPORTADA
isoblock.accion.accionGenerica.comboEstado.ASIGNADA=ASIGNADA
isoblock.accion.accionGenerica.comboEstado.ANALIZADAYENIMPLEMENTACION=ANALIZADA
isoblock.accion.accionGenerica.comboEstado.IMPLEMENTADAYVERIFICADA=EJECUTADA
isoblock.accion.accionGenerica.comboEstado.APROBADA=VERIFICADA
isoblock.accion.accionGenerica.comboEstado.CERRADA=CERRADA
isoblock.accion.accionGenerica.comboEstado.PROCEDE=EFECTIVA
isoblock.accion.accionGenerica.comboEstado.NOPROCEDE=NO EFECTIVA
isoblock.accion.accionGenerica.comboEstado.CANCELADA=NO PROCEDE
isoblock.accion.accionGenerica.hallazgoEstado.OVERDUE=Vencidas
isoblock.accion.accionGenerica.hallazgoEstado.EXPIRESOON=Por vencer
isoblock.accion.accionGenerica.hallazgoEstado.ATTENDED=Atendidas
isoblock.accion.accionGenerica.hallazgoEstado.ONTIME=A tiempo
isoblock.accion.accionGenerica.accionT.ACCIONDEMEJORACONTINUA=ACCI&Oacute;N DE MEJORA CONTINUA
isoblock.accion.accionGenerica.accionT.ACCIONCORRECTIVA=ACCI&Oacute;N CORRECTIVA
isoblock.accion.accionGenerica.accionT.ACCIONPREVENTIVA=ACCI&Oacute;N PREVENTIVA
isoblock.auditoria.auditoria.deleteLinks.mensaje=No hay auditores ayudantes
isoblock.auditoria.auditoria.cambiaEstado.mensaje=Cambio de estado
isoblock.auditoria.auditoria.deleteAuditoria.mensaje=Error en la funcion deleteAuditoria
isoblock.auditoria.auditoria.updateAuditoria.mensaje=Error en updateAuditoria
isoblock.auditoria.auditoria.comboEstado.Programada=Programada
isoblock.auditoria.auditoria.comboEstado.Notificada=Notificada
isoblock.auditoria.auditoria.comboEstado.Realizada=Realizada
isoblock.auditoria.auditoria.comboEstado.Cerrada=Cerrada
isoblock.common.Properties.printAscDesc.asc=Ordenado ascendente
isoblock.common.Properties.printAscDesc.desc=Ordenado descendente
isoblock.common.Properties.printAscDescNumero.asc=Ascender
isoblock.common.Properties.printAscDescNumero.desc=Descender
isoblock.common.Utilities.monthDate.Enero=Enero
isoblock.common.Utilities.monthDate.Febrero=Febrero
isoblock.common.Utilities.monthDate.Marzo=Marzo
isoblock.common.Utilities.monthDate.Abril=Abril
isoblock.common.Utilities.monthDate.Mayo=Mayo
isoblock.common.Utilities.monthDate.Junio=Junio
isoblock.common.Utilities.monthDate.Julio=Julio
isoblock.common.Utilities.monthDate.Agosto=Agosto
isoblock.common.Utilities.monthDate.Septiempre=Septiempre
isoblock.common.Utilities.monthDate.Octubre=Octubre
isoblock.common.Utilities.monthDate.Noviembre=Noviembre
isoblock.common.Utilities.monthDate.Diciembre=Diciembre
isoblock.common.Utilities.shortMonthDate.Ene=Ene
isoblock.common.Utilities.shortMonthDate.Feb=Feb
isoblock.common.Utilities.shortMonthDate.Mar=Mar
isoblock.common.Utilities.shortMonthDate.Abr=Abr
isoblock.common.Utilities.shortMonthDate.May=May
isoblock.common.Utilities.shortMonthDate.Jun=Jun
isoblock.common.Utilities.shortMonthDate.Jul=Jul
isoblock.common.Utilities.shortMonthDate.Ago=Ago
isoblock.common.Utilities.shortMonthDate.Sep=Sep
isoblock.common.Utilities.shortMonthDate.Oct=Oct
isoblock.common.Utilities.shortMonthDate.Nov=Nov
isoblock.common.Utilities.shortMonthDate.Dic=Dic
isoblock.common.comentario.deleteComentario.mensaje=Error en la funcion deleteUser
isoblock.common.comentario.insertComentario.mensaje=En el insert
isoblock.common.comentario.getStringTipo.Hallazgo=Hallazgo
isoblock.common.comentario.getStringTipo.Comentario=Comentario
isoblock.common.comentario.getStringTipo.ComentarioaNotificaciondeAuditoria=Comentario a Notificaci&oacute;n de Auditor&iacute;a
isoblock.common.comentario.getStringTipo.Minuta=Minuta
isoblock.configuracion.question.comboEstadoPregunta.TODAS=-- TODAS --
isoblock.configuracion.question.comboEstadoPregunta.ACTIVA=ACTIVA
isoblock.configuracion.question.comboEstadoPregunta.CANCELADA=CANCELADA
isoblock.configuracion.seccion.strMensaje.enblanco=en blanco
isoblock.configuracion.seccion.strMensaje.errorId=No se tiene un identificador de seccion correcto
isoblock.configuracion.seccion.strMensaje.errorObtener=Error al tratar de obtener un id para la nueva seccion
isoblock.configuracion.tipo.deleteTipo.mensaje=Error en la funcion deleteTipo. Clase: tipo
isoblock.configuracion.unidadorganizacional.deleteUnidadOrganizacional.mensaje=Error en la funcion deleteUser. Clase: unidadorganizacional
isoblock.configuracion.unidadorganizacional.updateUnidadOrganizacional.mensaje=Error en la funcion updateUser. Clase: unidadorganizacional
isoblock.configuracion.unidadorganizacional.deleteUser.mensaje=Error en la funcion deleteUser
isoblock.configuracion.unidadorganizacional.updateUser.mensaje=La cuenta de usuario elegida, ya existe.
isoblock.configuracion.unidadorganizacional.comboRolAuditorias.Lector=Lector
isoblock.configuracion.unidadorganizacional.comboRolAuditorias.Editor=Editor
isoblock.configuracion.unidadorganizacional.comboRolAuditorias.Encargado=Encargado
isoblock.configuracion.unidadorganizacional.comboRolAdministracion.Administrador=Administrador
isoblock.cuestrionarios.cuestionario.getStringEstado.Borrador=Borrador
isoblock.cuestrionarios.cuestionario.getStringEstado.Activo=Activo
isoblock.cuestrionarios.cuestionario.getStringEstado.Cancelado=Cancelado
isoblock.cuestrionarios.cuestionario.deleteCuestionario.mensaje=Error en la funcion deleteCuestionario
isoblock.cuestrionarios.cuestionario.cambiaEstado.mensaje=Cambio de estado
chart.other = Otros
isoblock.documentos.documento.getStringEstado.Revision=Revisi&oacute;n
isoblock.indicadores.GraficaIndicador.estado.Revision=Revisi\u00f3n
isoblock.documentos.documento.getStringEstado.Activo=Activo
isoblock.documentos.documento.getStringEstado.Activos=Activos
isoblock.documentos.documento.getStringEstado.Edicion=Edici&oacute;n
isoblock.documentos.documento.getStringEstado.Cancelado=Cancelado
isoblock.documentos.documento.getStringEstado.Cancelados=Cancelados
isoblock.documentos.documento.getStringEstado.Descontinuados=Descontinuados
isoblock.documentos.documento.getStringEstado.DescontinuadosDeCancelado=Descontinuados de cancelado
isoblock.documentos.documento.getStringEstado.Todos=-- TODOS --
isoblock.accion.accionGenerica.AceptaAccionGenerica=<br>&nbsp;&nbsp;Se ha finalizado la implementaci&oacute;n y verificaci&oacute;n de una acci&oacute;n en su departamento la cual requiere su aceptaci&oacute;n, favor de atenderla. <br>
isoblock.accion.accionGenerica.enviaNotificacion.noCorreo=El o los destinatarios del correo no pudieron ser notificado(s) via correo electr&oacute;nico.
isoblock.accion.accionCorrectiva.noCorreo=El encargado en turno de la acci&oacute;n no pudo ser notificado via correo electr&oacute;nico.
isoblock.accion.accionMejoraContinua.noCorreo=El encargado no pudo ser notificado via correo electr&oacute;nico.
isoblock.accion.accionATomar.noCorreo=El o los destinatarios del correo no pudieron ser notificado(s) via correo electr&oacute;nico.
isoblock.auditoria.auditoria.noCorreo=El auditor encargado no pudo ser notificado via correo electr&oacute;nico.
isoblock.documentos.documento.enviaNotificacion.noCorreo=El encargado de documentos no pudo ser notificado via correo electr&oacute;nico.
isoblock.documentos.documento.updateDocumento.noCambio=(No hubo cambios, el administrador no ser&aacute; notificado)
isoblock.accion.accionPreventiva.noCorreo=El encargado no pudo ser notificado via correo electr&oacute;nico.
isoblock.configuracion.user.insertUser.claveExistente=La cuenta de usuario elegida, ya existe.
isoblock.common.correo.enviacorreo.identificador=Bnext QMS V3 Interno
isoblock.common.Utilities.creaForma.porMedio=Por medio de este correo puede visualizar el pendiente.
isoblock.common.Utilities.creaForma.enviar=Enviar
error.ErrorNoSePuedeMostrarElTexto=Error: No se puede mostrar el texto
isoblock.correo.formatoCorreoArriba=<head><meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1"></head><body><table width='600' border='0' cellpadding='0' cellspacing='0'><tr><td><table width='600' height='30' bgcolor='#1A6FAC' cellpadding='0' cellspacing='0' style='border-bottom-color:#C8E2FB; border-top-color:#C8E2FB; border-left-color:1A6FAC; border-right-color:1A6FAC' border='2' ><tr><td style="font-family:Verdana, Arial, Helvetica, sans-serif; color:#F5F7F9; font-size:11px">&nbsp;&nbsp;<b>
isoblock.correo.formatoCorreoEnmedio=</b></td></tr></table></td></tr><tr><td><table width='600' border='0' cellpadding='3' cellspacing='0' bgcolor='#F5F7F9' style="font-family:Arial, Verdana, Helvetica, sans-serif; color:#000000; font-size:11px"><tr><td>&nbsp;&nbsp;
isoblock.correo.variablesCorreo1=</tr></td></table></td></tr><tr><td><table border='0' cellpadding='0' cellspacing='0'>
isoblock.correo.variablesCorreo2=</table></td></tr><tr><td><table width='600' border='0' cellpadding='0' cellspacing='3' bgcolor='#F5F7F9' style="font-family:Arial, Verdana, Helvetica, sans-serif; color:#000000; font-size:11px"><tr><td>&nbsp;&nbsp;
isoblock.correo.formatoCorreoAbajo=<br><hr><b>Nota: </b>Correo electr&oacute;nico generado autom&aacute;ticamente por el sistema <b>Bnext QMS&reg;</b>.</td></tr></table></td></tr></table></body>
isoblock.correo.accion.variablesCorreo1=Clave:
isoblock.correo.accion.variablesCorreo2=Acci&oacute;n:
isoblock.correo.accion.variablesCorreo3=Fuente:
isoblock.correo.accion.variablesCorreo4=Departamento:
isoblock.correo.accion.variablesCorreo5=Avance:
isoblock.auditoria.auditoria.enviarCorreo.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de AUDITOR&Iacute;AS en el sistema <b>Bnext QMS<b>. <br>
isoblock.auditoria.auditoria.enviarCorreo.notificacion1=<br> Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.correo.auditoria.variablesCorreo1=Clave:
isoblock.correo.auditoria.variablesCorreo2=Estado:
isoblock.correo.auditoria.variablesCorreo3=Departamento:
isoblock.correo.auditoria.variablesCorreo4=Proceso(s):
isoblock.correo.auditoria.variablesCorreo5=Horario:
isoblock.correo.auditoria.variablesCorreo6=&nbsp;&nbsp;&nbsp;Fecha de inicio:
isoblock.correo.auditoria.variablesCorreo7=&nbsp;&nbsp;&nbsp;Fecha de terminaci&oacute;n
isoblock.correo.auditoria.variablesCorreo8=Cuestionario:
isoblock.correo.auditoria.variablesCorreo9=Objetivos/Alcance:
isoblock.correo.auditoria.variablesCorreo10=Comentarios:
isoblock.auditoria.auditoria.notificaAuditoria=<br>&nbsp;&nbsp; Se ha creado una nueva auditor&iacute;a en la cual usted ha sido agregado como participante, favor de atenderla.
isoblock.auditoria.auditoria.noAceptaAuditoria=<br>&nbsp;&nbsp; No se ha aceptado la realizaci&oacute;n de la auditor&iacute;a en la que usted est&aacute; participando, favor de atenderla.
isoblock.correo.notificacion1=Para atenderlo, podr&aacute; visualizarlo en el &aacute;rea de pendientes dentro del sistema.
isoblock.correo.accion.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de ACCIONES del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.auditoria.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de AUDITOR&Iacute;AS del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.documento.notificacion=Notificaci&oacute;n de la secci&oacute;n de DOCUMENTOS del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.documento.variablesCorreo1=Estado\:
isoblock.correo.documento.variablesCorreo2=T&iacute;tulo:
isoblock.correo.documento.variablesCorreo3=Clave:
isoblock.correo.documento.variablesCorreo4=Versi&oacute;n
isoblock.correo.documento.variablesCorreo5=Fecha y Hora de creaci&oacute;n:
isoblock.correo.documento.variablesCorreo6=Autor:
isoblock.correo.documento.variablesCorreo7=&Aacute;rea:
isoblock.correo.secuencia.notificacion=Notificaci&oacute;n de la secci&oacute;n de SECUENCIAS del M&oacute;dulo de Documentos del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.secuencia.variablesCorreo1=Clave de la secuencia:
isoblock.correo.secuencia.variablesCorreo2=T&iacute;tulo del documento:
isoblock.correo.secuencia.variablesCorreo3=Clave del documento:
isoblock.correo.secuencia.variablesCorreo4=Autor:
isoblock.correo.secuencia.variablesCorreo5=Fecha l&iacute;mite de autorizaci&oacute;n:
isoblock.correo.secuencia.variablesCorreo6=Raz&oacute;n:
isoblock.correo.solicitud.notificacion=Notificaci&oacute;n de la secci&oacute;n de SOLICITUDES del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.solicitud.variablesCorreo1=Tipo de solicitud:
isoblock.correo.solicitud.variablesCorreo2=T&iacute;tulo del documento:
isoblock.correo.solicitud.variablesCorreo3=Clave del documento:
isoblock.correo.solicitud.variablesCorreo4=Fecha de solicitud:
isoblock.correo.solicitud.variablesCorreo5=Solicitante:
isoblock.correo.solicitud.variablesCorreo6=Raz&oacute;n:
isoblock.correo.auto.notificacion=Notificaci&oacute;n de actividad en el sistema <b>Bnext QMS</b>.<br>
isoblock.configuracion.user.insertUser.limiteUsuarios=Ha llegado al l&iacute;mite de usuarios posibles seg&uacute;n su licencia del sistema. <br>
isoblock.configuracionhandle.Si=S&iacute;
isoblock.configuracionhandle.No=No
isoblock.configuracionhandle.EspMex=Espa\u00f1ol(M&eacute;xico)
isoblock.configuracionhandle.IngEstUn=Ingl&eacute;s(Estados Unidos)
isoblock.reportesgenerales.comboGraficaX.Estado=Estado
isoblock.reportesgenerales.comboGraficaX.Responsable=Responsable
isoblock.reportesgenerales.comboGraficaX.Autor=Autor
isoblock.reportesgenerales.comboGraficaX.Ubicacion=${Department}
isoblock.accion.accionGenerica.comboGraficaX.Tipodeaccion=Tipo de acci&oacute;n
isoblock.accion.accionGenerica.comboTipoAccion.Todas=-- TODAS --
isoblock.accion.accionGenerica.comboTipoAccion.ACC=Correctiva
isoblock.accion.accionGenerica.comboTipoAccion.APP=Preventiva
isoblock.accion.accionGenerica.comboTipoAccion.AMC=Mejora Continua
isoblock.reportesgenerales.Todos=-- Todos --
isoblock.reportesgenerales.comboTipoGrafica.Bar=Gr&aacute;fica de Barras
isoblock.reportesgenerales.comboTipoGrafica.Pie=Gr&aacute;fica Circular
isoblock.reportesgenerales.comboTipoGrafica.Bar3D=Gr&aacute;fica de Barras 3D
isoblock.reportesgenerales.comboTipoGrafica.Pie3D=Gr&aacute;fica Circular 3D
isoblock.reportesgenerales.comboGraficaX.Originador=Originador
isoblock.reportesgenerales.comboGraficaX.UsuariosporUbicacion=Usuarios por departamento
isoblock.reportesgenerales.comboGraficaX.AreasporUbicacion=Procesos por departamento
isoblock.accion.accionGenerica.comboEstado.NOCERRADA=*NO CERRADA
isoblock.reportesgenerales.comboGraficaX.Mes=Mes
isoblock.reportesgenerales.comboGraficaX.TipoDocumento=Tipo de Documento
isoblock.reportesgenerales.comboGraficaX.Autorizante=Autorizante
isoblock.Proyectos.Actividad.FechaInicio=Fecha de Inicio
isoblock.Proyectos.Actividad.FechaFin=Fecha Final
isoblock.Proyectos.Actividad.Verificador=Verificador
isoblock.Proyectos.Actividad.FechaVerificador=Fecha de Verificaci&oacute;n
isoblock.Proyectos.Actividad.Presupuesto=Presupuesto
isoblock.Proyectos.Actividad.Avance=Avance
isoblock.Proyectos.Actividad.PeticionPresupuesto=Petici&oacute;n de Presupuesto
isoblock.Proyectos.Actividad.TipoCambioDesconocido=Tipo de Cambio Desconocido
isoblock.correo.actividad.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de PROYECTOS en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.actividad.insertaActividadResponsable=<br>&nbsp;&nbsp; Se le ha asignado como encargado de la implementaci&oacute;n de una actividad de un proyecto, favor de atenderla.
isoblock.proyecto.Proyecto.comboEstado.CREADO=Creado
isoblock.proyecto.Proyecto.comboEstado.LIBERADO=Liberado
isoblock.proyecto.Proyecto.comboEstado.CONCLUIDO=Concluido
isoblock.proyecto.Proyecto.comboEstado.CERRADO=Cerrado
isoblock.correo.actividad.variablesCorreo1=Nombre de la Meta\:
isoblock.correo.actividad.variablesCorreo2=Nombre de la Actividad\:
isoblock.correo.actividad.variablesCorreo3=Nombre del Proyecto\:
isoblock.accion.actividad.insertaActividadVerificador=<br>&nbsp;&nbsp; Se le ha asignado como verificador  de una actividad de un proyecto, favor de atenderla.
isoblock.proyectos.actividad.insertaActividad.error=No se pudo agregar la actividad.
isoblock.proyecto.actividad.enviaNotificacion.noCorreo=El responsable y/o el verificador de la actividad no pudieron ser notificado via correo electr&oacute;nico.
isoblock.proyectos.actividad.setProyectoConcluido.detalle=<br>&nbsp;&nbsp; Existe un proyecto que ha sido concluido, favor de atenderlo.
isoblock.proyectos.actividad.setConcluida.detalle=Dentro del sistema existe una actividad marcada como concluida en espera de su verificaci&oacute;n, favor de atenderla. <br>
isoblock.correo.cambioPendiente.variablesCorreo1=Actividad:
isoblock.correo.cambioPendiente.variablesCorreo2=Proyecto:
isoblock.correo.cambioPendiente.variablesCorreo3=Tipo de Cambio:
isoblock.correo.cambioPendiente.variablesCorreo4=Valor Anterior:
isoblock.correo.cambioPendiente.variablesCorreo5=Nuevo Valor:
isoblock.correo.cambioPendiente.variablesCorreo6=Raz&oacute;n de Cambio\:
isoblock.proyectos.actividadCambio.autorizado.detalle=<br>&nbsp;&nbsp; Una petici&oacute;n de cambio de una actividad que usted solicit&oacute; ha sido autorizada. <br>
isoblock.proyectos.actividadCambio.rechazado.detalle=<br>&nbsp;&nbsp; Una petici&oacute;n de cambio de una actividad que usted solicit&oacute; ha sido rechazada. <br>
isoblock.correo.cambioPendiente.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de PROYECTOS en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.proyecto.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de PROYECTOS en el sistema <b>Bnext QMS</b>.<br>
isoblock.proyectos.proyecto.setCancelado.detalle=<br>&nbsp;&nbsp; Un proyecto en el que usted participa ha sido cancelado, favor de atenderlo.
isoblock.correo.proyecto.variablesCorreo1=Clave Proyecto:
isoblock.correo.proyecto.variablesCorreo2=Clave Actividad:
isoblock.correo.proyecto.variablesCorreo3=Fecha Inicio:
isoblock.correo.proyecto.variablesCorreo4=Fecha Final:
isoblock.proyectos.actividad.peticionpresupuesto.autorizado=<br>Una petici&oacute;n de presupuesto de una actividad que usted solicit&oacute; ha sido autorizada. <br>
isoblock.proyectos.actividad.peticionpresupuesto.rechazado=<br>Una petici&oacute;n de presupuesto de una actividad que usted solicit&oacute; ha sido rechazada, favor de atenderla. <br>
isoblock.correo.peticionPresupuesto.notificacion=Notificaci&oacute;n de una autorizaci&oacute;n de petici&oacute;n de presupuesto en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.peticionPresupuesto.variablesCorreo1=Proyecto:
isoblock.correo.peticionPresupuesto.variablesCorreo2=Actividad:
isoblock.correo.peticionPresupuesto.variablesCorreo3=Cantidad a pedir:
isoblock.correo.peticionPresupuesto.variablesCorreo4=Razon de la petici&oacute;n:
isoblock.correo.peticionPresupuesto.variablesCorreo5=Raz&oacute;n no autorizado:
isoblock.correo.cambioPendiente.variablesCorreo7=Raz&oacute;n no autorizado:
isoblock.configuracion.unidadorganizacional.comboRolProyectos.Contabilidad=Contabilidad
isoblock.correo.presupuesto.variablesCorreo1=Proyecto:
isoblock.correo.presupuesto.variablesCorreo2=Actividad:
isoblock.correo.presupuesto.notificacion=Notificaci&oacute;n de una modificaci&oacute;n de presupuesto  de una actividad en el sistema <b>Bnext QMS</b>.<br>
isoblock.proyectos.presupuesto.AgregarPresupuesto=<br>&nbsp;&nbsp; Se ha modificado el rubro de una actividad en que usted participa <br>
isoblock.correo.presupuesto.variablesCorreo3=Nuevo presupuesto:
isoblock.proyectos.actividad.CorreoPorcentajeActividad=<br>&nbsp;&nbsp; Se ha disminuido el porcentaje de avance de una actividad en que usted participa<br>
isoblock.correo.porcentajeActividad.notificacion=Notificaci&oacute;n de modificaci&oacute;n de avance de una actividad en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.porcentajeActividad.variablesCorreo1=Proyecto:
isoblock.correo.porcentajeActividad.variablesCorreo2=Actividad:
isoblock.correo.porcentajeActividad.variablesCorreo3=Nuevo porcentaje de avance:
isoblock.proyectos.proyecto.correoProyectoLiberado=<br>&nbsp;&nbsp; Se ha liberado un proyecto  en el que usted participa.<br>
isoblock.correo.proyectoLiberado.variablesCorreo1=Proyecto:
isoblock.correo.proyectoLiberado.notificacion=Notificaci&oacute;n de un nuevo proyecto liberado en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.autorBajaEstadoActividad.variablesCorreo1=Proyecto:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo2=Actividad:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo3=Nuevo porcentaje de avance:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo4=Rubro:
isoblock.correo.autorBajaEstadoActividad.notificacion=Notificaci&oacute;n de una modificaci&oacute;n del presupuesto y avance de una actividad  en el sistema <b>Bnext QMS</b>.<br>
isoblock.proyectos.CambioPendiente.TipoCambioFechaInicio=Fecha Inicial
isoblock.proyectos.CambioPendiente.TipoCambioFechaFin=Fecha Final
isoblock.proyectos.CambioPendiente.TipoCambioVerificador=Verificador
isoblock.proyectos.CambioPendiente.TipoCambioFechaVerificador=Fecha Verificador
isoblock.proyectos.CambioPendiente.TipoCambioDesconocido=Cambio Desconocido
isoblock.proyectos.actividad.CorreoAutorInsertaRubroActividadConcluida=<br>&nbsp;&nbsp; Se ha agregado un rubro y disminuido el porcentaje de avance de una actividad conluida en que usted participa<br>
isoblock.proyectos.actividad.CorreoAutorModificaRubroActividadConcluida=<br>&nbsp;&nbsp; Se ha modificado un rubro y disminuido el porcentaje de avance de una actividad conluida en que usted participa<br>
isoblock.proyectos.actividad.CorreoAutorModificaRubroActividad=<br>&nbsp;&nbsp; Se ha modificado un rubro de una actividad  en que usted participa<br>
isoblock.correo.presupuestoActividad.variablesCorreo1=Proyecto:
isoblock.correo.presupuestoActividad.variablesCorreo2=Actividad:
isoblock.correo.presupuestoActividad.variablesCorreo3=Nombre rubro:
isoblock.correo.presupuestoActividad.variablesCorreo4=Cantidad total del rubro:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo5=Cantidad total del rubro:
isoblock.correo.presupuestoActividad.notificacion=Notificaci&oacute;n de una modificaci&oacute;n del presupuesto de actividad  en el sistema <b>Bnext QMS</b>.<br>
actividadeslistpeticionespresupuesto.NoSeHaRealizadoNingunCambio=No se ha realizado ning\u00fan cambio.
isoblock.correo.ModuloProyecto.notificacion=Notificaci&oacute;n del M&oacute;dulo de Proyectos en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.Saludo=Estimado(a) 
isoblock.common.threadFunction.checaProyectosPorCerrar=<br>Los siguientes Proyectos, en los cuales usted est&aacute; a cargo, han alcanzado su fecha de finalizaci&oacute;n, pero no han terminado\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Autor</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Responsable</b></td></tr>
isoblock.common.threadFunction.checaActividadesPorTerminarResponsables=<br>Las siguientes Actividades, en las cuales usted es responsable, han alcanzado su fecha de finalizaci&oacute;n, pero no han sido terminadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.common.threadFunction.checaActividadesPorTerminarEncargados=<br> Las siguientes Actividades pertenecen a un proyecto donde usted est&aacute; a cargo y han alcanzado su fecha de finalizaci&oacute;n, pero no han sido terminadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.common.threadFunction.checaProyectoActividadesPorVerificar=<br>Las siguientes Actividades, en las cuales usted es verificador, han alcanzado su fecha de finalizaci&oacute;n, pero no han sido verificadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.common.threadFunction.checaProyectoActividadesPorVerificarEncargados=<br>Las siguientes Actividades pertenecen a un proyecto donde usted est&aacute; a cargo y han alcanzado su fecha de finalizaci&oacute;n, pero no han sido verificadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.accion.accionGenerica.comboGraficaX.Fuente=Fuente
isoblock.common.threadFunction.checaAcciones=<br>&nbsp;&nbsp; Dentro del sistema Bnext QMS, existen acciones a tomar las cuales han alcanzado su fecha de realizaci&oacute;n en las siguientes ACCIONES\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Clave de acci&oacute;n</b></td><td width\='70%' bgcolor\='\#FFFFFF'><b>Departamento</b></td></tr>
isoblock.common.comentario.comboEstado.Todos=-- TODOS --
isoblock.common.comentario.comboEstado.Nuevo=Nuevo
isoblock.common.comentario.comboEstado.Leido=Le&iacute;do
isoblock.common.comentario.comboEstado.Contestado=Contestado
isoblock.documentos.documento.getStringEstado.DocumentoDeProyectos=Documentos de proyectos
isoblock.common.Database.AreaALaQuePertenece=Proceso al que pertenece
isoblock.common.Database.PlantaALaQuePertenece=${Facility} a la que pertenece
isoblock.common.Database.UbicacionALaQuePertenece=Departamento al que pertenece
isoblock.accion.accionGenerica.comboTipoAccion.APD=Sobre producto
isoblock.accion.accionGenerica.comboTipoAccion.APY=Sobre proyecto
isoblock.configuracion.user.ReportedeGerencial=Reporte Gerencial
isoblock.user.permisosReportes.ReportedeConfiguracion=Configuraci&oacute;n
isoblock.configuracion.user.ReportedeAuditorias=Auditorias
isoblock.configuracion.user.ReportedeCuestionarios=Cuestionarios
isoblock.configuracion.user.ReportedeAcciones=Acciones
isoblock.configuracion.user.ReportedeDocumentos=Documentos
isoblock.configuracion.user.ReportedeEncuesta=Encuestas
isoblock.configuracion.user.ReportedeQuejas=Quejas
isoblock.configuracion.user.ReportedeIndicadores=Indicadores
isoblock.configuracion.user.ReportedeProyectos=Proyectos
isoblock.configuracion.userhandle.ValorStatusUsuario=Activo
isoblock.configuracion.userhandle.ValorStatusUsuario2=Inactivo
isoblock.correo.actividad.variablesCorreo4=Porcentaje Avance
isoblock.correo.secuencia.variablesCorreo7=Comentario\:
isoblock.configuracion.usuarioexterno.comboProyectoClienteExterno.noesexterno=El Usuario No es Externo
isoblock.documento.comboCampos.Todos=--CUALQUIER CAMPO--
isoblock.documento.comboCampos.Clave=Clave
isoblock.documento.comboCampos.Nombre=T&iacute;tulo
isoblock.documento.comboCampos.Originador=Originador
isoblock.documento.comboCampos.RazonAltaCambio=Raz&oacute;n Alta/Cambio
documentos.documentossecuencia.Searechazadoiniciarlasecuenciadelasolicituddeldocumentoyseenvioelavisoaloriginadordedichodocumento=Se a rechazado iniciar la secuencia de la solicitud del documento y se envio el aviso al originador de dicho documento
isoblock.correo.quejas.notificacion=Notificaci&oacute;n del m&oacute;dulo de QUEJAS del sistema Bnext QMS
isoblock.correo.quejas.variablesCorreo1=Clave:
isoblock.correo.quejas.variablesCorreo2=Estado:
isoblock.correo.quejas.variablesCorreo3=Fuente:
isoblock.correo.quejas.variablesCorreo4=Departamento:
isoblock.correo.quejas.variablesCorreo5=Responsable Asignado:
isoblock.correo.encuesta.notificacion=Notificaci&oacute;n del M&oacute;dulo de ENCUESTAS del sistema <b>Bnext QMS</b>.
isoblock.correo.encuesta.responder=Acabas de terminar una encuesta.
isoblock.correo.encuesta.variablesCorreo1=Estado:
isoblock.correo.encuesta.variablesCorreo2=T&iacute;tulo:
isoblock.correo.encuesta.variablesCorreo3=Clave:
isoblock.correo.encuesta.variablesCorreo4=Encargado de la encuesta:
isoblock.correo.encuesta.variablesCorreo5=Inicia:
isoblock.correo.encuesta.variablesCorreo6=Termina:
isoblock.correo.encuesta.variablesCorreo7=Descripcion:
isoblock.correo.encuesta.variablesCorreo8=Comentarios generales:
isoblock.common.Database.listControlGeneratorCompleto.Agregar=Agregar
isoblock.correo.indicador.notificacion=Notificaci&oacute;n del M&oacute;dulo de INDICADORES del Sistema <b>Bnext QMS</b>.<br>
isoblock.correo.indicador.variablesCorreo1=Indicador:
isoblock.correo.indicador.variablesCorreo2=Proceso:
isoblock.correo.indicador.variablesCorreo3=Fecha:
isoblock.correo.Configuracion.notificacion=Notificaci&oacute;n del M&oacute;dulo de CONFIGURACI&Oacute;N del sistema Bnext QMS.
isoblock.correo.Prueba.notificacion=Edsadasdasdste es un mail de prueba enviado por el sistema Bnext QMS para confirmar su configuraci&oacute;n
isoblock.correo.Configuracion.variablesCorreo1=<b>Nombre completo:</b>
isoblock.correo.Configuracion.variablesCorreo2=<b>Nombre de usuario:</b>
isoblock.correo.Configuracion.variablesCorreo3=<b>Correo:</b>
isoblock.correo.Configuracion.variablesCorreo4=<b>Perfil:</b>
isoblock.correo.Configuracion.variablesCorreo5=<b>Detalles de perfil:</b>
isoblock.correo.recoveruser.notificacion=Notificaci&oacute;n de recuperaci&aacute;n de USUARIO del sistema Bnext QMS.
isoblock.correo.recoverpass.notificacion=Notificaci&oacute;n de recuperaci&aacute;n de CONTRASE\u00d1A del sistema Bnext QMS.
isoblock.correo.Configuracion.variablesCorreo5=<b>Detalles de perfil:</b>
isoblock.common.Database.listControlGeneratorCompleto.Estado= Estado
isoblock.common.Database.listControlGeneratorCompleto.Autor=Autor
isoblock.common.Database.listControlGeneratorCompleto.Ver= Ver
isoblock.common.Database.listControlGeneratorCompleto.Cuestionario= Cuestionario
isoblock.common.Database.listControlGeneratorCompleto.Borrar= Borrar
isoblock.common.Database.listControlGeneratorCompleto.GuardarCambios= Guardar Cambios
isoblock.common.Database.controlListaUsuariosTodo.ComboTodos= -- TODOS --
isoblock.common.Database.controlListaUsuariosTodo.ComboSeleccione= -- Seleccione --
isoblock.quejas.queja.ElestadoActualesREPORTADAporAsignarResponsable= El estado actual es REPORTADA por asignar Responsable.
isoblock.configuracion.catalogo.GetCatalogoName.ElCatalogoNoExisteOFueBorrado= - El catalalogo no existe o fue borrado -
isoblock.configuracion.user.TuCuentaEnElSistemaBnextDPMSHaSidoDesactivadaPorElAdministradorDelSistema= T&uacute; cuenta en el sistema Bnext QMS ha sido desactivada por el administrador del sistema.
isoblock.configuracion.user.TuCuentaEnElSistemaBnextDPMSHaSidoActivadaPorElAdministradorDelSistema= T&uacute; cuenta en el sistema Bnext QMS ha sido activada por el administrador del sistema.
isoblock.configuracion.user.SeHaRegistradoUnNuevoUsuarioEnElSistemaFavorDeActivarloYAsignarUnProcesoEnElQuePArticipa= Se ha registrado un nuevo usuario en el sistema favor de activarlo y asignar un proceso en el que participa.
isoblock.configuracion.user.ClaveDeAcceso= <br><b>Clave de Acceso: </b>
isoblock.configuracion.user.PERMISOSDEROLENMODULOS= <br><b>PERMISOS DE ROL EN MODULOS</b><br>
isoblock.configuracion.user.-RolAccionesNoAcepto=- Rol Acciones: NO ACCESO
isoblock.configuracion.user.-RolAccionesLector=- Rol Acciones: LECTOR
isoblock.configuracion.user.-RolAccionesEditor=- Rol Acciones: EDITOR
isoblock.configuracion.user.-RolAccionesEncargado=- Rol Acciones: ENCARGADO
isoblock.configuracion.user.-RolAuditoriasNoAcceso=- Rol Auditor&iacute;as: NO ACCESO
isoblock.configuracion.user.-RolAuditoriasLector=- Rol Auditor&iacute;as: LECTOR
isoblock.configuracion.user.-RolAuditoriasEditor=- Rol Auditor&iacute;as: EDITOR
isoblock.configuracion.user.-RolAuditoriasEncargado=- Rol Auditor\u00edas: ENCARGADO
isoblock.configuracion.user.-RolDocumentosNoAcceso=- Rol Documentos: NO ACCESO
isoblock.configuracion.user.-RolDocumentosLector=- Rol Documentos: LECTOR
isoblock.configuracion.user.-RolDocumentosEditor=- Rol Documentos: EDITOR
isoblock.configuracion.user.-RolDocumentosEncargado=- Rol Documentos: ENCARGADO
isoblock.configuracion.user.-RolEncuestasNoAcceso=- Rol Encuestas: NO ACCESO
isoblock.configuracion.user.-RolEncuestasNoLector=- Rol Encuestas: LECTOR
isoblock.configuracion.user.-RolEncuestasEditor=- Rol Encuestas: EDITOR
isoblock.configuracion.user.-RolEncuestasNoLector=- Rol Encuestas: ENCARGADO
isoblock.configuracion.user.-RolIndicadoresNoAcceso=- Rol Indicadores: NO ACCESO
isoblock.configuracion.user.-RolIndicadoresLector=- Rol Indicadores: LECTOR
isoblock.configuracion.user.-RolIndicadoresEditor=- Rol Indicadores: EDITOR
isoblock.configuracion.user.-RolIndicadoresEncargado=- Rol Indicadores: ENCARGADO
isoblock.configuracion.user.-RolProyectosNoAcceso=- Rol Proyectos: NO ACCESO
isoblock.configuracion.user.-RolProyectosLector=- Rol Proyectos: LECTOR
isoblock.configuracion.user.-RolProyectosContabilidad=- Rol Proyectos: CONTABILIDAD
isoblock.configuracion.user.-RolProyectosEditor=- Rol Proyectos: EDITOR
isoblock.configuracion.user.-RolProyectosEncargado=- Rol Proyectos: ENCARGADO
isoblock.configuracion.user.-RolQuejasNoAcceso=- Rol Quejas: NO ACCESO
isoblock.configuracion.user.-RolQuejas-=- Rol Quejas: -
isoblock.configuracion.user.-RolQuejasLector=- Rol Quejas: LECTOR
isoblock.configuracion.user.-RolQuejasEditor=- Rol Quejas: EDITOR
isoblock.configuracion.user.-RolQuejasEncargado=- Rol Quejas: ENCARGADO
isoblock.configuracion.user.PERMISOSDEACCESOAREPORTES=<br><b>PERMISOS DE ACCESO A REPORTES</b><br>
isoblock.configuracion.user.-NOTIENEACCESOAREPORTES=- NO TIENE ACCESO A REPORTES<br>
isoblock.configuracion.user.-ConfiguracionNoAcceso=- Configuracion: NO ACCESO
isoblock.configuracion.user.-ConfiguracionAcceso=- Configuracion: ACCESO
isoblock.configuracion.user.-AuditoriasNoAcceso=- Auditor&iacute;as: NO ACCESO
isoblock.configuracion.user.-AuditoriasAcceso=- Auditor&iacute;as: ACCESO
isoblock.configuracion.user.-CuestionariosNoAcceso=- Cuestionarios: NO ACCESO
isoblock.configuracion.user.-CuestionariosAcceso=- Cuestionarios: ACCESO
isoblock.configuracion.user.-AccionesNoAcceso=- Acciones: NO ACCESO
isoblock.configuracion.user.-AccionesAcceso=- Acciones: ACCESO
isoblock.configuracion.user.-AuditoriasNoAcceso=- Auditor&iacute;as: NO ACCESO
isoblock.configuracion.user.-AuditoriasAcceso=- Auditor&iacute;as: ACCESO
isoblock.configuracion.user.-DocumentosNoAcceso=- Documentos: NO ACCESO
isoblock.configuracion.user.-DocumentosAcceso=- Documentos: ACCESO
isoblock.configuracion.user.-ProyectosNoAcceso=- Proyectos: NO ACCESO
isoblock.configuracion.user.-ProyectosAcceso=- Proyectos: ACCESO
isoblock.configuracion.user.-GerencialNoAcceso=- Gerencial: NO ACCESO
isoblock.configuracion.user.-GerencialAcceso=- Gerencial: ACCESO
isoblock.configuracion.user.-IndicadoresNoAcceso=- Indicadores: NO ACCESO
isoblock.configuracion.user.-IndicadoresAcceso=- Indicadores: ACCESO
isoblock.configuracion.user.-QuejasNoAcceso=- Quejas: NO ACCESO
isoblock.configuracion.user.-QuejasAcceso=- Quejas: ACCESO
isoblock.configuracion.user.-EncuestasNoAcceso=- Encuestas: NO ACCESO
isoblock.configuracion.user.-EncuestasAcceso=- Encuestas: ACCESO
isoblock.configuracion.user.comboEstados.--TODOS--=-- TODOS --
isoblock.configuracion.user.ACTIVO= ACTIVO
isoblock.configuracion.user.INACTIVO= INACTIVO
isoblock.configuracion.user.PORREGISTRAR= POR REGISTRAR
#Tags extra (GraficaIndicador) Indicadores
isoblock.indicadores.Indicadoresporobjetivo=Indicadores por objetivo
#Tags extras (isoblock/accion/accionGenerica.java)
isoblock.accion.accionGenerica.Sehadadodealtanunanuevaaccionenelsistema=Se ha dado de alta una nueva acci&oacute;n en el sistema.
isoblock.accion.accionGenerica.Secerrolaaccion=Se cerr&oacute; la acci&oacute;n.
#Tags extras (isoblock/auditoria/auditoria.java)
isoblock.auditoria.auditoria.Secancelolaautitoria,razon=Se cancel&oacute; la auditor&iacute;a Razon:
isoblock.auditoria.auditoria.Laauditoriaestaenpendienteporaceptar=La auditor&iacute;a est&aacute; en Pendiente por aceptar.
isoblock.auditoria.auditoria.Ustedeselencargadodelprocesoenestaauditoria,tieneunnuevopendientedePendientedeAuditoriasporAceptarResultados,favordeatenderlo=Usted es el encargado de proceso en esta auditor&iacute;a, tiene un nuevo pendiente de 'Reportes de Auditor&iacute;as por Aceptar Resultados', favor de atenderlo.
isoblock.auditoria.auditoria.Sehacreadounanuevaauditoriaenlacualustedeselencargadodeldepartamento,favordeatenderla=Se ha creado una nueva auditor&iacute;a en la cual usted es el encargado del departamento, favor de atenderla.
isoblock.auditoria.auditoria.UstedeselencargadodelProcesosujetoalasiguienteauditoria,TieneunnuevopendienteenAuditoriasporConfirmar,favordeatenderlo=Usted es el Encargado del Proceso sujeto a la siguiente auditor&iacute;a. Tiene un nuevo pendiente en 'Auditor&iacute;as por confirmar', favor de atenderlo.
isoblock.auditoria.auditoria.Sehacreadounanuevaauditoriaenlacualustededeselauditorencargado,ustedpuedemodificarlosdatosdelaauditoriamientrasestanohayasidoMODIFICADA,unavezCONFIRMADAporelencargadodelproceso,estanopodrasermodificada=Se ha creado una nueva auditor&iacute;a en la cual usted es el auditor encargado, usted puede modificar los datos de la auditor&iacute;a mientras esta no haya sido CONFIRMADA, una vez CONFIRMADA por el encargado del proceso, &eacute;sta no podr&aacute; ser modificada.
isoblock.auditoria.auditoria.Confirmada=Confirmada
isoblock.auditoria.auditoria.Aceptada=Aceptada
isoblock.auditoria.auditoria.Cancelada=Cancelada
isoblock.auditoria.auditoria.comboEstado.Planeada=Planeada
isoblock.auditoria.auditoria.comboEstado.Confirmada=Confirmada
isoblock.auditoria.auditoria.comboEstado.Aceptada=Aceptada
isoblock.auditoria.auditoria.comboEstado.Cancelada=Cancelada
isoblock.auditoria.auditoria.comboEstado.Eliminada=Eliminada
isoblock.auditoria.auditoria.comboEstado.PorAsignar=Por Asignar
isoblock.auditoria.auditoria.COMENTARIODECANCELACION=COMENTARIO DE CANCELACI&Oacute;N
isoblock.auditoria.auditoria.Seagregouncomentarioalaauditoria,favorderevisarlo=Se agrego un comentario a la auditoria, favor de revisarlo.
#Tags extras (isoblock/common/Database)
isoblock.common.Database.MoverTodos=Mover Todos
isoblock.common.Database.Mover=Mover
isoblock.common.Database.Eliminar=Eliminar
isoblock.common.Database.EliminarTodos=Eliminar Todos
isoblock.common.Database.--DEPARTAMENTO--=-- DEPARTAMENTO --
isoblock.common.Database.--SELECCIONE--=-- SELECCIONE --
isoblock.common.Database.TipoDeObjeto=Tipo de objeto
isoblock.common.Database.hoy=hoy
isoblock.common.Database.mes=mes
isoblock.common.Database.dia=d&iacute;a
isoblock.common.Database.a\u00f1o=a\u00f1o
isoblock.common.Database.VerDetalle=Ver Detalle
isoblock.common.Database.VerPregutas=Ver Preguntas
isoblock.common.Database.Borrar=Borrar
isoblock.common.Database.Noseencontraronregistros=No se encontraron registros
isoblock.common.Database.Elregistroseraremovidocompletamentedelsistema\u00bfDeseacontinuar?=El registro ser\\u00e1 removido completamente del sistema. \\u00BFDesea continuar?
isoblock.common.Database.-UsuarioInactivo=- Usuario inactivo:
isoblock.common.Database.-Sinasignar-=- Sin asignar -
isoblock.common.Database.Respaldogeneradoalas=Respaldo generado a las
isoblock.common.Database.enlafecha=en la fecha
#Tags extras (isoblock/common/threadFunction.java)
isoblock.common.threadFunction.eselresponsablededepartamentodondeseasignounaqueja,favordeasignarunresponsable=Es el responsable de departamento donde se asigno una queja, favor de asignar un responsable.
isoblock.common.threadFunction.youAreComplaintManager=es el encargado del m\u00f3dulo de quejas, favor de asignar un responsable.
isoblock.common.threadFunction.hasidoasignadocomoResponsabledeunaqueja,favordeatenderla=Ha sido asignado como Responsable de una queja, favor de atenderla.
isoblock.common.threadFunction.deberesvisarlarespuestadadaporelresponsable=debe revisar la respuesta dada por el responsable.
isoblock.common.threadFunction.endondedebeevaluarsuefectividad=En donde debe evaluar su efectividad
isoblock.common.threadFunction.Ustedtiene=Usted tiene 
isoblock.common.threadFunction.pendiente(s)endonde= \ pendiente(s) en donde 
#Tags extras (isoblock/configuracion/catalogo.java)
isoblock.configuracion.catalogo.comvoEstados.Activa=Activa
isoblock.configuracion.catalogo.comvoEstados.Inactiva=Inactiva
isoblock.configuracion.catalogo.comvoEstados.CALIFICACIONES=CALIFICACIONES
isoblock.configuracion.catalogo.comvoEstados.FUENTESDEACCIONES=FUENTES DE ACCIONES
#Tags extras (isoblock/configuracion/masive.java)
isoblock.configuracion.masive.ErrorFila=Error: Fila:
isoblock.configuracion.masive.Laclave=. La clave:
isoblock.configuracion.masive.seencuentrarepetidaenelarchivo=se encuentra repetida en el archivo.
isoblock.configuracion.masive.nombrecompletodeusuario=nombre completo de usuario
isoblock.configuracion.masive.nombredeusuario=nombre de usuario
isoblock.configuracion.masive.Elnombreusuarionodebecontenerespacios=. El nombre usuario no debe contener espacios:
isoblock.configuracion.masive.Elnombreusuario=. El nombre usuario:
isoblock.configuracion.masive.yaestaregistradodentrodelsistemaconunaclavedeusuariodiferente=ya esta registrado dentro del sistema con una clave de usuario diferente.
isoblock.configuracion.masive.AlertaFila=Alerta: Fila:
isoblock.configuracion.masive.esdiferentealqueestaregistradoenelsistema=es diferente al que esta registrado en el sistema.
isoblock.configuracion.masive.Fila=Fila:
isoblock.configuracion.masive.seencuentrarepetidodentrodelarchivo=se encuentra repetido dentro del archivo.
isoblock.configuracion.masive.Lacontrasenanodebedetenerespacios=. La contrase\u00f1a no debe contener espacios:
isoblock.configuracion.masive.Elformatodelcorreoesinvalido=. El formato del correo es invalido:
isoblock.configuracion.masive.ElIDdelperfilnoexiste=. El ID del perfil no existe:
isoblock.configuracion.masive.ElIDdelprocesonoexiste=. El ID del proceso no existe:
isoblock.configuracion.masive.recuerdequelosIDdebenirseparadosporcomas=, recuerde que los ID deben ir separados por comas.
isoblock.configuracion.masive.Ocurriounerroralcargarelperfil=. Ocurrio un error al cargar el perfil:
isoblock.configuracion.masive.ningundatodelusuario=, ningun dato de el usuario
isoblock.configuracion.masive.fuecargado=fue cargado.
isoblock.configuracion.masive.Elcampo=. El campo
isoblock.configuracion.masive.contienecaracteresinvalidos=contiene caracteres invalidos
isoblock.configuracion.masive.sequitaronloscaracteres=se quitaron los caracteres.
#Tags extras (isoblock/configuracion/question.java)
isoblock.configuracion.question.-BORRADOR= - BORRADOR
#Tags extras (isoblock/configuracion/user.java)
isoblock.configuracion.user.Usuario=Usuario :
isoblock.configuracion.user.Clave=Clave :
#Tags extras (isoblock/configuracion/userprofile.java)
isoblock.configuracion.userprofile.comboEstado.Activa=Activa
isoblock.configuracion.userprofile.comboEstado.Inactiva=Inactiva
#Tags extras (isoblock/evaluaciones/Report.java)
isoblock.evaluaciones.Report.comboTiposGrafica.ReportedeEstados=Reporte de Estados
isoblock.evaluaciones.Report.comboTiposGrafica.Resultadosporencuesta=Resultados por encuesta
isoblock.evaluaciones.Report.comboTiposGrafica.Cantidadderespuestas=Cantidad de respuestas
isoblock.evaluaciones.Report.comboGraficarPor.Opciones=Opciones
isoblock.evaluaciones.Report.comboGraficarPor.Preguntas=Preguntas
isoblock.evaluaciones.Report.comboGraficarPor.Secciones=Secciones
isoblock.evaluaciones.Report.comboTipoGraficacionPonderacion.Ponderacion=Ponderaci&oacute;n
isoblock.evaluaciones.Report.crearGrafica.Encuestasenproceso=Encuestas en proceso.
isoblock.evaluaciones.Report.crearGrafica.Estado=Estado
isoblock.evaluaciones.Report.crearGrafica.Cantidad=Cantidad
isoblock.evaluaciones.Report.crearGrafica.Total=Total:
isoblock.evaluaciones.Report.crearGrafica.Resultadosdelaencuesta=Resultados de la encuesta
isoblock.evaluaciones.Report.crearGrafica.Graficaporrespuestasaopciones= Gr\u00e1fica por respuestas a opciones
isoblock.evaluaciones.Report.crearGrafica.Graficaporrespuestasapreguntas= Gr\u00e1fica por respuestas a preguntas
isoblock.evaluaciones.Report.crearGrafica.Graficaporrespuestasasecciones= Gr\u00e1fica por respuestas a secciones
isoblock.evaluaciones.Report.crearGrafica.Mostraragregarparticipantes=Mostrar agregar participantes
isoblock.evaluaciones.Report.crearGrafica.Ocultaragregarparticipantes=Ocultar agregar participantes
isoblock.evaluaciones.Report.crearGrafica.Mostrardetallesdereporte=Mostrar detalles de reporte
isoblock.evaluaciones.Report.crearGrafica.Pregunta=Pregunta
isoblock.evaluaciones.Report.crearGrafica.Porcentaje*=Porcentaje*
isoblock.evaluaciones.Report.crearGrafica.Valor*=Valor*
isoblock.evaluaciones.Report.crearGrafica.Resultadoenpregunta=Resultado en pregunta:
isoblock.evaluaciones.Report.crearGrafica.Resultadoenseccion=Resultado en secci&oacute;n:
isoblock.evaluaciones.Report.crearGrafica.Cantidaddepreguntasnograficadas=Cantidad de preguntas no graficadas:
isoblock.evaluaciones.Report.crearGrafica.Calificacionpromediogeneral=Calificacion promedio general:
isoblock.evaluaciones.Report.crearGrafica.Noexisteinformacionparagraficar=No existe informaci\u00f3n para graficar.
isoblock.evaluaciones.Report.crearGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio=Imposible realizar esta gr&aacute;fica para indicador agrupado por promedio.
#Tags extras (isoblock/evaluaciones/encuesta.java)
isoblock.evaluaciones.encuesta.enviarCorreo.Hayunaencuestaenlacualhasidoasignadocomoparticipantefavordeatenderla=Hay una encuesta en la cual ha sido asignado como participante, favor de atenderla.
isoblock.evaluaciones.encuesta.enviarCorreo.Secancelolaencuesta,Razon=Se cancelo la encuesta, Razon:
isoblock.evaluaciones.encuesta.enviarCorreo.Laencuestahasidocerrada=La encuesta ha sido cerrada.
isoblock.evaluaciones.encuesta.updateEncuesta.Seactualizalaencuesta=Se actualiz&oacute; la encuesta
isoblock.evaluaciones.encuesta.Seactualizaronlosparticipantesyhoradeterminodelaencuesta=Se actualizar\u00f3n los participantes y hora de termino de la encuesta.
isoblock.evaluaciones.encuesta.Participantesnuevos=Participantes nuevos:
isoblock.evaluaciones.encuesta.Programada=Programada
isoblock.evaluaciones.encuesta.Activa=Activa
isoblock.evaluaciones.encuesta.Terminada=Terminada
isoblock.evaluaciones.encuesta.Cancelada=Cancelada
isoblock.evaluaciones.encuesta.PorResponder=Por Responder
isoblock.evaluaciones.encuesta.Respondida=Respondida
isoblock.evaluaciones.encuesta.Contestada=Contestada
isoblock.evaluaciones.encuesta.Inactiva=Inactiva
isoblock.evaluaciones.encuesta.Porcerrar=Por cerrar
isoblock.evaluaciones.encuesta.Cancelada=Cancelada
isoblock.evaluaciones.encuesta.Pordefinir=Por definir
isoblock.evaluaciones.encuesta.Elusuario=El usuario
isoblock.evaluaciones.encuesta.hacerradolaencuestamanualmente=ha cerrado la encuesta manualmente.
isoblock.evaluaciones.encuesta.Seagregouncomentarioalaencuesta=Se agrego un comentario a la encuesta.
#Tags extras (isoblock/evaluaciones/evaluacion.java)
isoblock.evaluaciones.evaluacion.comboEstados.Activa=Activa
isoblock.evaluaciones.evaluacion.comboEstados.Inactiva=Inactiva
isoblock.evaluaciones.evaluacion.getStrEstado.Activa=Activa
isoblock.evaluaciones.evaluacion.getStrEstado.Estaencuestahasidorespondida=Esta encuesta ha sido respondida
isoblock.evaluaciones.evaluacion.getStrEstado.Inactiva=Inactiva
isoblock.evaluaciones.evaluacion.getStrEstado.PorAsignar=Por Asignar
isoblock.evaluaciones.evaluacion.evaluationBuilder.Examen=Examen:
isoblock.evaluaciones.evaluacion.evaluationBuilder.ErrorNosepudoidentificareltipodepregunta=Error: No se pudo identificar el tipo de pregunta.
isoblock.evaluaciones.evaluacion.evaluationBuilder.Hasolvidadocontestarlapregunta(s)=Has olvidado contestar la pregunta(s)
isoblock.evaluaciones.evaluacion.writeCalendar.Fecha=Fecha
#Tags extras (isoblock/indicadores/GraficaIndicador.java)
isoblock.indicadores.GraficaIndicador.Indicadores=Indicadores
isoblock.indicadores.GraficaIndicador.getGrafica.Objetivo=Objetivo:
isoblock.indicadores.GraficaIndicador.getGrafica.Indicadores=Indicadores:
isoblock.indicadores.GraficaIndicador.getGrafica.Total=Total:
isoblock.indicadores.GraficaIndicador.getGrafica.Evoluciondelindicador=Evoluci&oacute;n del indicador
isoblock.indicadores.GraficaIndicador.getGrafica.Fecha=Fecha
isoblock.indicadores.GraficaIndicador.getGrafica.Meta=Meta
isoblock.indicadores.GraficaIndicador.getGrafica.Resultado=Resultado
isoblock.indicadores.GraficaIndicador.getGrafica.Detalledeindicadores=Detalle de indicadores
isoblock.indicadores.GraficaIndicador.getGrafica.Detalledeindicador=Detalle de indicador
isoblock.indicadores.GraficaIndicador.getGrafica.Unidad=&Aacute;rea:
isoblock.indicadores.GraficaIndicador.getGrafica.Usuario=Usuario:
isoblock.indicadores.GraficaIndicador.getGrafica.Facultad=Unidad productiva:
isoblock.indicadores.GraficaIndicador.getGrafica.Total=Total:
isoblock.indicadores.GraficaIndicador.getGrafica.MetaResultado=Meta
isoblock.indicadores.GraficaIndicador.getGrafica.Acumulado=Acumulado:
isoblock.indicadores.GraficaIndicador.getGrafica.Contribucionalameta=Contribuci&oacute;n a la meta
isoblock.indicadores.GraficaIndicador.getGrafica.Responsable=Responsable:
isoblock.indicadores.GraficaIndicador.getGrafica.Resultado=Resultado:
isoblock.indicadores.GraficaIndicador.getGrafica.Total=Total
isoblock.indicadores.GraficaIndicador.getGrafica.Noexisteinformacionparagraficar=No existe informaci\u00f3n para graficar.
isoblock.indicadores.GraficaIndicador.getGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio=Imposible realizar esta gr&aacute;fica para indicador agrupado por promedio.
isoblock.indicadores.GraficaIndicador.getGrafica.Fechaderevision=Fecha de revisi\u00f3n
isoblock.indicadores.GraficaIndicador.getGrafica.valor=Valor
isoblock.indicadores.GraficaIndicador.comboTiposGrafica.Curva=Curva
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Dia=D&iacute;a
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Mes=Mes
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Bimestral=Bimestral
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Tetra=Tetra
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Semestral=Semestral
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.A\u00f1o=A\u00f1o
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Facultad=Unidad productiva
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Departamento=Departamento
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Proceso=Proceso
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Unidad=&Aacute;rea
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Objetivo=Objetivo
isoblock.indicadores.GraficaIndicador.comboGraficarPor.FechadeRevision=Fecha de Revisi&oacute;n
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Usuario=Usuario
#Tags extras (isoblock/indicadores/Indicador.java)
isoblock.indicadores.Indicador.programarLevantamientoIndicador.UnlevantamientodeindicadorenelcualesresponsableseencuentraenestadodeesperaPORCALIFICAR.Favordeatenderlo=Un levantamiento de indicador en el cu&aacute;l es responsable se encuentra en estado de espera POR CALIFICAR. Favor de atenderlo.
isoblock.indicadores.Indicador.programarRevisionIndicador.Sehaprogramadolarevisi&oacute;ndeunindicadordelacualustedeselresponsable,debeesperarhastaqueelindicadorseacalificadoyseencuentreensufechaderevisi&oacute;n.Favordeatenderlo=Se ha programado la revisi&oacute;n de un indicador de la cual usted es el responsable, debe esperar hasta que el indicador sea calificado y se encuentre en su fecha de revisi&oacute;n. Favor de atenderlo.
isoblock.indicadores.Indicador.verificarEstatus.UnlevantamientoenelcualesresponsanleestaenestadodeesperaPORCALIFICAR.Favordeatenderlo=Un levantamiento de indicador en el cu&aacute;l es responsable esta en estado de espera POR CALIFICAR. Favor de atenderlo.
isoblock.indicadores.Indicador.verificarEstatus.UnarevisiondeindicadorenlacualesresponsableestaenesperaPORREVISAR.Favordeatenderlo=Una revisi&oacute;n de indicador en la cu&aacute;l es responsable esta en espera POR REVISAR. Favor de atenderlo.
isoblock.indicadores.Indicador.comboReportarPor.Responsables=Responsables
isoblock.indicadores.Indicador.comboReportarPor.Areas=&Aacute;reas
#Tags extras (isoblock/common/gridGenerator.java)
isoblock.common.gridGenerator.getTitles.Estado=Estado*
isoblock.common.gridGenerator.getTitles.Ver=Ver*
isoblock.common.gridGenerator.getTitles.Cuestionario=Cuestionario*
isoblock.common.gridGenerator.getTitles.Borrar=Borrar*
isoblock.common.gridGenerator.getResults.VerDetalle=Ver Detalle
isoblock.common.gridGenerator.getResults.VerPreguntas=Ver Preguntas
isoblock.common.gridGenerator.getResults.Borrar=Borrar*
isoblock.common.gridGenerator.getResults.Noseencontraronregistros=No se encontraron registros
isoblock.common.gridGenerator.getResults.GuardarCambios=Guardar Cambios
isoblock.common.gridGenerator.getNavegador.Iralaprimerapagina=Ir a la primera p&aacute;gina.
isoblock.common.gridGenerator.Seencuentraenlaultimahoja=Se encuentra en la ultima hoja.
isoblock.common.gridGenerator.Seencuentraenlaprimerahoja=Se encuentra en la primera hoja.
isoblock.common.gridGenerator.getNavegador.Iralapaginaanteriror=Ir a la p&aacute;gina anteriror.
isoblock.common.gridGenerator.getNavegador.Iralapaginasiguiente=Ir a la p&aacute;gina siguiente.
isoblock.common.gridGenerator.getNavegador.Iralaultimapagina=Ir a la ultima p&aacute;gina.
#Tags extras (isoblock/common/gridSearch.java)
isoblock.common.gridSearch.addSearchField.--SELECCIONE--=-- SELECCIONE --
isoblock.common.gridSearch.addSearchField.-eltiposeleccionadonofuncionautilizandoestemetodo-=- el tipo seleccionado no funciona utilizando este metodo -
isoblock.common.gridSearch.getSearchFields.Buscar=Buscar
#Tags extras (isoblock/login/login.java)
isoblock.login.login,recoverUser.HasolicitadounarecuperaciondeunnombredeusuarioparaelsistemaBnextDPMS,siustednorealizoestasolicitudhagacasoomisodeestecorreo,lasiguienteesunalistadeusuariosrelacionadosaestadirecciondecorreoelectronico=Ha solicitado una recuperaci&oacute;n de nombre de usuario para el sistema Bnext QMS, si usted no realiz&oacute; esta solicitud haga caso omiso de este correo, la siguiente es una lista de los usuarios relacionados a esta direcci&oacute;n de correo electronico.
isoblock.login.login.recoverPass.HasolicitadounarecuperaciondeContrasenaparaelsistemaBnextDPMSparaelnombredeusuario=Ha solicitado una recuperaci&aacute;n de Contrase\u00f1a para el sistema Bnext QMS para el nombre de usuario
isoblock.login.login.recoverPass.siustednorealizoestasolicitudhagacasoomisodeestecorreo=, si usted no realiz&oacute; esta solicitud haga caso omiso de este correo.
isoblock.login.login.recoverPass.Contrasena=Contrase\u00f1a:
#Tags extras (isoblock/quejas/queja.java)
isoblock.quejas.queja.insertQueja.Sehareportadounaqueja,favordeatenderlaAutordelaqueja=Se ha reportado una queja, favor de atenderla.<br><b>Autor de la queja: </b>
isoblock.quejas.queja.nextStateControl.SehaasignadounResponsable,ahoralequejaseencuentraenproceso=<br>Se ha asignado un Responsable, ahora la queja se encuentra en proceso.
isoblock.quejas.queja.nextStateControl.UstedhasidoasignadocomoResponsabledeunaqueja,favordeatenderla=<br>Usted ha sido asignado como Responsable de una queja, favor de atenderla.
isoblock.quejas.queja.nextStateControl.Estaquejanohasidoatendidadebidoalasiguienterazon=<br>Esta queja no ha sido atendida debido a la siguiente raz\u00f3n:<br><b>
isoblock.quejas.queja.nextStateControl.Sehadadorespuestaaestaqueja,larespuestafuelasiguiente,favordeatenderla=<br>Se ha dado respuesta a esta queja, la respuesta fue la siguiente, favor de atenderla: <br><b>
isoblock.quejas.queja.nextStateControl.Estaquejahasidoatendidayestaenesperaporevaluar,favordeatenderla=<br>Esta queja ha sido atendida y est&aacute; en espera por evaluar, favor de atenderla.<br><br>
isoblock.quejas.queja.nextStateControl.Larespuestaaestaquejanohasidoaceptada,favordevolverallenarla.Razon=<br>La respuesta a esta queja no ha sido aceptada, favor de volver a atenderla. Raz&oacute;n: <br><b>
isoblock.quejas.queja.nextStateControl.LARESPUESTAAESTAQUEJANOHASIDOACEPTADA,FAVORDEVOLVERALLENARLA=LA RESPUESTA A ESTA QUEJA NO HA SIDO ACEPTADA, FAVOR DE ATENDERLA: <br><br>
isoblock.quejas.queja.nextStateControl.Estaquejahasidore-asignadade=Esta queja ha sido re-asignada de <u>
isoblock.quejas.queja.nextStateControl.austed=</u> a usted <u>
isoblock.quejas.queja.nextStateControl.favordeatenderla.Razon=</u>, favor de atenderla.<br> Raz&oacute;n: <br><b>
isoblock.quejas.queja.nextStateControl.Laquejaqueatendistefuere-asignadaa=La queja que atendiste fue re-asignada a <u>
isoblock.quejas.queja.nextStateControl.Razon=</u><br>.<br> Raz&oacute;n: <br><b>
isoblock.quejas.queja.nextStateControl.Laquejahasidoevaluadayfinalizada,lacalificacionfue=<br>La queja ha sido evaluada y finalizada, la calificacion fue: <b>
isoblock.quejas.queja.getStrCalificacion.MuySatisfecho=Muy Satisfecho
isoblock.quejas.queja.getStrCalificacion.Satisfecho=Satisfecho
isoblock.quejas.queja.getStrCalificacion.PocoSatisfecho=Poco Satisfecho
isoblock.quejas.queja.getStrCalificacion.NadaSatisfecho=Nada Satisfecho
isoblock.quejas.queja.getStringEstadoControl.ElestadoactualesASIGNADAenesperapordarrespuesta=El estado actual es ASIGNADA en espera por dar respuesta.
isoblock.quejas.queja.getStringEstadoControl.ElestadoactualesATENDIDAenesperaporaceptarrespuesta=El estado actual es ATENDIDA en espera por aceptar respuesta.
isoblock.quejas.queja.getStringEstadoControl.ElestadoactualesRESPONDIDA,pendienteporaceptarresultados=El estado actual es RESPONDIDA, pendiente por aceptar resultados.
isoblock.quejas.queja.getStringEstadoControl.Estaquejahasidoatendidayfinalizada=Esta queja ha sido atendida y finalizada.
isoblock.quejas.queja.getStringEstadoControl.EstaquejahasidoatendidayfuemarcadacomoNOPROCEDE=Esta queja ha sido atendida y fue marcada como NO PROCEDE.
isoblock.quejas.queja.getStringEstadoControlSimple.Reportada=Reportada
isoblock.quejas.queja.getStringEstadoControlSimple.Asignada=Asignada
isoblock.quejas.queja.getStringEstadoControlSimple.Atendida(enproceso)=Atendida (en proceso)
isoblock.quejas.queja.getStringEstadoControlSimple.Poraceptarresultados=Por aceptar resultados
isoblock.quejas.queja.getStringEstadoControlSimple.Atendida(finalizada)=Atendida (finalizada)
isoblock.quejas.queja.getStringEstadoControlSimple.Cerrada(noprocede)=Cerrada (no procede)
isoblock.quejas.queja.getStringEstadoAlumno.Reportada=Reportada.
isoblock.quejas.queja.getStringEstadoAlumno.Enproceso=En proceso.
isoblock.quejas.queja.getStringEstadoAlumno.Atendidaporevaluarresultados=Atendida por evaluar resultados.
isoblock.quejas.queja.getStringEstadoAlumno.Finalizada=Finalizada.
isoblock.quejas.queja.getStringEstadoAlumno.PorAsignar=Por Asignar
#Tags extras (isoblock/quejas/reporteQuejas.java)
isoblock.quejas.reporteQueja.comboTiposGrafica.Quejasenproceso=Quejas en proceso
isoblock.quejas.reporteQueja.comboTiposGrafica.Detalledequejas=Detalle de quejas
isoblock.quejas.reporteQueja.comboGraficarPor.Estado=Estado
isoblock.quejas.reporteQueja.comboGraficarPor.QuejasporDepartamento=Quejas por Departamento
isoblock.quejas.reporteQueja.comboGraficarPor.Usuario=Usuario
isoblock.quejas.reporteQueja.comboGraficarPor.Fuente=Fuente
isoblock.quejas.reporteQueja.comboGraficarPor.Clasificacion =Clasificaci&oacute;n
isoblock.quejas.reporteQueja.crearGrafica.Quejasenproceso=Quejas en proceso.
isoblock.quejas.reporteQueja.crearGrafica.Estado=Estado
isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas=Cantidad de quejas
isoblock.quejas.reporteQueja.crearGrafica.Porcentaje=Porcentaje
isoblock.quejas.reporteQueja.crearGrafica.Totaldequejas=Total de quejas:
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasporusuario=Quejas levantadas por usuario
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasaporestados=Quejas levantadas a por estados
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasadepartamentos=Quejas levantadas a departamentos
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasafuentes=Quejas levantadas a fuentes
isoblock.quejas.reporteQueja.crearGrafica.Campo=Estatus
isoblock.quejas.reporteQueja.crearGrafica.Responsable(s)=Responsable(s)
isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas=Cantidad de quejas
isoblock.quejas.reporteQueja.crearGrafica.Porcentaje=Porcentaje
isoblock.quejas.reporteQueja.crearGrafica.#Dias=# Dias
isoblock.quejas.reporteQueja.crearGrafica.DiasPromediodeatencion=Dias Promedio de atencion
isoblock.quejas.reporteQueja.crearGrafica.Totaldequejas=Total de quejas:
isoblock.quejas.reporteQueja.crearGrafica.Noexisteinformacionparagraficar=No existe informaci\u00f3n para graficar.
isoblock.quejas.reporteQueja.crearGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio=Imposible realizar esta gr&aacute;fica para indicador agrupado por promedio.
isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas=Cantidad de quejas
#Tags extras (isoblock/indicadores/RevisionIndicadores.java)
isoblock.indicadores.RevisionIndicadores.getTotalesRevision.positivo= positivo
isoblock.indicadores.RevisionIndicadores.getTotalesRevision.negativo= negativo
#Tags extras (isoblock/configuracion/userprfile.java)
isoblock.configuracion.userprofile.Nosepudocambiarelestadodelperfil=No se pudo cambiar el estado del perfil.
isoblock.configuracion.userprofile.Secambioelestadodelperfilcorrectamente=Se cambio el estado del perfil correctamente.
isoblock.configuracion.userprofile.Nosepudoborrarelperfil=No se pudo borrar el perfil.
isoblock.configuracion.userprofile.Seborroelperfilcorrectamente=Se borr&oacute; el perfil correctamente.
#Tags extras (isoblock/common/gridGenerator/gridSearch.java)
isoblock.common.gridGenerator.gridSearch.Limpiarseleccion=Limpiar selecci&oacute;n
isoblock.common.gridGenerator.gridSearch.Seleccionartodo=Seleccionar todo
#Tags extras (isobloc/common/Database.java)
isoblock.common.Database.controlListaObjetos.combo.--TODAS--=-- TODAS --
isoblock.auditoria.auditoria.LaAuditoriaHaSidoRestauradaCorrectamente=La auditoria ha sido restaurada correctamente.
isoblock.auditoria.auditoria.NoFuePosibleRestaurarLaAuditoria=No fue posible restaurar la auditoria.
isoblock.auditoria.auditoria.LaAuditoriaHaSidoEliminadaCorrectamente=La auditoria ha sido eliminada correctamente.
isoblock.auditoria.auditoria.NoFuePosibleEliminarLaAuditoria=No fue posible eliminar la auditoria.
isoblock.auditoria.auditoria.Laauditoriasehacerradocorrectamente=La auditor&iacute;a se ha cerrado correctamente.
isoblock.auditoria.auditoria.Laauditorianosehapodidocerrar=La auditoria no se ha podido cerrar
#Tags Extras (isoblock/configuracion/invitado.java)
isoblock.configuracion.invitado.borrarInvitado.mensaje=Error en la funci\\u00f3n borrarInvitado.
isoblock.configuracion.invitado.ElinvitadoHaSidoEliminadaCorrectamente=El invitado ha sido eliminado correctamente.
isoblock.configuracion.invitado.NoFuePosibleEliminaralinvitado=No fue posible eliminar al invitado.
isoblock.common.gridsearch.busqueda=B&uacute;squeda
isoblock.common.gridsearch.mostrarfiltrodebusqueda=Mostrar el filtro de b&uacute;squeda
isoblock.common.gridsearch.ocultarFiltrodebusqueda=Ocultar el filtro de busqueda
isoblock.configuracion.question.Lapreguntahasidocancelada=La pregunta ha sido cancelada correctamente
isoblock.configuracion.question.Lapreguntahasidoactivadacorrectamente=La pregunta ha sido activada correctamente
isoblock.configuracion.question.lapreguntasehaenviadoalapapelera=La pregunta ha sido enviada a la papelera
isoblock.configuracion.question.Lapreguntaseharestaurado=La Pregunta ha sido restaurada exitosamente
isoblock.configuracion.question.Lapreguntasehaeleiminadodefinitivamente=La pregunta ha sido eliminada
isoblock.configuracion.invitado.NoFuePosibleEliminaralinvitado=No fue posible eliminar al invitado.
isoblock.correo.indicador.variablesCorreo4=Indicador a Carrera:
quejas.quejashandle.Redacciondequeja=Redacci&oacute;n de queja:
quejas.quejashandle.Direcciondecorreoalternativapararecibiravisos=Copia de queja para:
isoblock.configuracion.user.-RolProyectosContabilidad-=- Rol Proyectos: CONTABILIDAD
isoblock.evaluaciones.Report.comboTiposGrafica.Total=Total:
isoblock.indicadores.Indicador.programarRevisionIndicador.Sehaprogramadolarevisiondeunindicadordelacualustedeselresponsable,debeesperarhastaqueelindicadorseacalificadoyseencuentreensufechaderevision.Favordeatenderlo=Se ha programado la revisi&oacute;n de un indicador de la cual usted es el responsable, debe esperar hasta que el indicador sea calificado y se encuentre en su fecha de revisi&oacute;n. Favor de atenderlo.
isoblock.indicadores.RevisionIndicador.revisarIndicador.Elindicadorhasidorevisadoporelusuariodelsistema=El indicador ha sido revisado por el usuario del sistema: 
isoblock.indicadores.RevisionIndicador.revisarIndicador.Comentario=Comentario:
isoblock.indicadores.Indicador.programarRevisionIndicador.Sehaprogramadolarevisi\u00f3ndeunindicadordelacualustedeselresponsable,debeesperarhastaqueelindicadorseacalificadoyseencuentreensufechaderevisi\u00f3n.Favordeatenderlo=Se ha programado la revisi\u00f3n de un indicador de la cual usted es el responsable, debe esperar hasta que el indicador sea calificado y se encuentre en su fecha de revisi\u00f3n. Favor de atenderlo.
isoblock.configuracion.user.-AccionesNoAcceso--=- Acciones: NO ACCESO
block.configuracion.user.-AuditoriasNoAcceso=- Auditor\u00edas: NO ACCESO
#Tags extras (isoblock/common/gridGenerator/gridGenerator.java)
isoblock.common.gridGenerator.gridGenerator.Seencuentraenlaultimahoja=Se encuentra en la &uacute;ltima hoja.
isoblock.common.gridGenerator.gridGenerator.Seencuentraenlaultimahoja=Se encuentra en la primer hoja.
#Tags extras (isoblock/configuracion/seccionlist.java)
isoblock.configuracion.seccion.borrarSeccion.mensaje=Error en la funci&oacute;n borrarSecci&oacute;n.
isoblock.configuracion.seccion.LaseccionHaSidoEliminadaCorrectamente=La secci&oacute;n ha sido eliminada correctamente.
isoblock.configuracion.seccion.NoFuePosibleEliminarlaseccion=No fue posible eliminar la secci&oacute;n.
#Tags extras (isoblock/configuracion/catalogo.java)
isoblock.configuracion.catalogo.Nosepudocambiarelestadodelcatalogo=No se pudo cambiar el estado del cat&aacute;logo.
isoblock.configuracion.catalogo.Secambioelestadodelcatalogocorrectamente=Se cambio el estado del cat&aacute;logo correctamente.
#Tags extras (isoblock/configuracion/catalogo.java)
isoblock.configuracion.catalogo.NoFuePosibleEliminarelcatalogo=No fue posible eliminar el cat&aacute;logo.
isoblock.configuracion.invitado.ElcatalogoHaSidoEliminadaCorrectamente=El cat&aacute;logo ha sido eliminada correctamente.
isoblock.gridgenerator.gridgenerator.porfavorespereaqueelprocesoanteriorterminedecargarse=Porfavor espere a que el proceso anterior termine
gridGenerator.gridGenerator.ajustes=Ajustes
gridGenerator.gridGenerator.cambiar=Cambiar
gridGenerator.gridGenerator.excel2003=Excel 2003
gridGenerator.gridGenerator.excel2007=Excel 2007
gridGenerator.gridGenerator.exportar=Exportar
gridGenerator.gridGenerator.exportarTabla=Exportar tabla
gridGenerator.gridGenerator.numeroderegistrosenpantalla=N\u00famero de registros en pantalla.
isoblock.scorecards.selectorEntidad.Mostrar=Mostrar/Ocultar
menu.folderTreeLeftFrame.Documentos=Documentos
menu.folderTreeLeftFrame.Proyectos=Proyectos
menu.folderTreeLeftFrame.Acciones=Acciones
menu.folderTreeLeftFrame.Solicitudes=Solicitudes
menu.folderTreeLeftFrame.Listadesolicitudes=Lista de solicitudes
menu.folderTreeLeftFrame.ListaMaestra=Lista Maestra
menu.folderTreeLeftFrame.ListaRelaciones=Lista de Relaciones
menu.folderTreeLeftFrame.GaleriaArbol=Galeria
menu.folderTreeLeftFrame.Papelera=Papelera
menu.folderTreeLeftFrame.Alta=Alta
menu.folderTreeLeftFrame.ConfiguracionQuejasControl=Control
menu.folderTreeLeftFrame.ConfiguracionMisQuejas=Mis Quejas
menu.folderTreeLeftFrame.Catalogo=Catalogo
documentos.solicitudeslist.Solicitada=Solicitada
documentos.solicitudeslist.Atendida=Atendida
solicitudes.solicitudeslist.ElestadoactualesRECHAZADA,presioneparamarcarcomoCerrada=Rechazada
solicitudes.solicitudeslist.Enproceso=En proceso
solicitudes.solicitudeslist.ElestadoactualesRECHAZADA=RECHAZADA
documentos.solicitudeslist.Cerrada=CERRADA
isoblock.scorecards.selectorEntidad.Filtros=Filtros
isoblock.scorecards.selectorEntidad.Ocultar=Filtros
isoblock.scorecards.selectorEntidad.Entidad=Entidad:
isoblock.scorecards.selectorEntidad.--SELECCIONE--=-- SELECCIONE -- 
isoblock.scorecards.selectorEntidad.Zona=Unidad productiva
isoblock.scorecards.selectorEntidad.Plaza=Departamento
isoblock.scorecards.selectorEntidad.Proceso=Proceso
isoblock.scorecards.selectorEntidad.Tienda=Area
isoblock.scorecards.selectorEntidad.Usuario=Usuario
isoblock.scorecards.selectorEntidad.Nombre=Nombre
isoblock.scorecards.selectorEntidad.Nombre2=Nombre:
isoblock.scorecards.selectorEntidad.Zona2=Unidad productiva:
isoblock.scorecards.selectorEntidad.Plaza2=Departamento:
isoblock.scorecards.selectorEntidad.Proceso2=Proceso:
isoblock.scorecards.selectorEntidad.Filtrar=Filtrar:
isoblock.scorecards.selectorEntidad.MoverAleatoriamente=Move randomly
isoblock.scorecards.selectorEntidad.entidades=entidades
isoblock.scorecards.selectorEntidad.Usuarios=Usuarios
isoblock.common.gridSearch.getSearchFields.Limpiar=Limpiar
isoblock.documentos.java.Nosehanagregadosdocumentos=No se han agregado documentos
isoblock.documentojava.Nohaylectores=No hay lectores
quejas.quejashandle.Levantaracciones=Levantar Acciones:
isoblock.quejas.queja.Nosecuentaconaccionesrelacionadas=No se cuenta con acciones relacionadas.
isoblock.documentos.documento.Parausarestecombo=Para usar este combo,
isoblock.documentos.documento.seleccioneunacarpeta=seleccione una carpeta.
isoblock.documentos.documento.Noexistendocumentos=No existen documentos
isoblock.documentos.documento.enlacarpeta=en la carpeta
isoblock.documentos.documento.seleccionada=seleccionada.
#Surveys.
isoblock.evaluaciones.Report.Encuestas=Encuestas.
isoblock.quejas.ReporteQuejas.Quejas=Quejas.
isoblock.correo.por.leer.notificacion=Notificaci\u00f3n de la secci\u00f3n de documentos del modulo del sistema de Bnext QMS
isoblock.correo.por.leer.nota=Usted tiene un nuevo pendiente de documento por leer en el sistema, favor de atenderlo.
isoblock.correo.por.leer.variablesCorreo1=Clave de la secuencia:
isoblock.correo.por.leer.variablesCorreo2=T\u00edtulo del documento:
isoblock.correo.por.leer.variablesCorreo3=Clave del documento:
isoblock.correo.por.leer.variablesCorreo4=Autor:
isoblock.correo.por.leer.variablesCorreo5=Raz\u00f3n:
isoblock.correo.por.leer.variablesCorreo6=Comentario:
isoblock.quejas.queja.getStringEstado=Estado
isoblock.quejas.queja.getStringClave=Clave
isoblock.quejas.queja.getStringResponsable=Responsable
isoblock.quejas.queja.getStringReportada=Reportada
isoblock.quejas.queja.getStringVer=Ver
isoblock.accion.accionGenerica.comboEstado.AccionesaTomarporRealizar=Acciones a Tomar por Realizar
isoblock.accion.accionGenerica.comboEstado.AccionesaTomarporVerificar=Acciones a Tomar por Verificar
# Sample ResourceBundle properties file
isoblock.common.correo.tipoCorreo.auditoria = Aviso de participacion la auditor&iacute;a clave: AUD-
isoblock.common.correo.tipoCorreo.secuencia = Aviso del resultado del documento en la Secuencia de autorizacion clave: <b>SEC-
isoblock.common.correo.tipoCorreo.aviso = Aviso del sistema
isoblock.accion.accionMejoraContinua.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente de mejora contin&uacute;a en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionMejoraContinua.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionMejoraContinua.insertaAccionMejoraContinua = <br>&nbsp;&nbsp; Se ha reportado una acci&oacute;n de mejora contin&uacute;a que falta por ser asignada.
isoblock.accion.accionMejoraContinua.asignaAccionMejoraContinua = <br>&nbsp;&nbsp; Se le ha asignado como encargado de la implementaci&oacute;n de una acci&oacute;n de mejora contin&uacute;a.
isoblock.accion.accionMejoraContinua.setImplementada = <br>&nbsp;&nbsp;Una acci&oacute;n de mejora contin&uacute;a ha sido marcada como implementada lista para su verificaci&oacute;n, favor de atenderla. <br>
isoblock.accion.accionCorrectiva.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente en acciones correctivas en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionCorrectiva.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionCorrectiva.insertaAccionCorrectiva = <br>&nbsp;&nbsp; Se le ha asignado como encargado de la implementaci&oacute;n de una acci&oacute;n correctiva.
isoblock.accion.accionCorrectiva.setImplementada = <br>&nbsp;&nbsp; Una acci&oacute;n correctiva ha sido marcada como implementada lista para su verificaci&oacute;n y su an&aacute;lisis.<br>
isoblock.accion.accionGenerica.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de ACCIONES en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionGenerica.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionGenerica.insertaAccionGenerica = <br>&nbsp;&nbsp;Se ha creado una acci&oacute;n asignada a su departamento, favor de atenderla. Este aviso es informativo y no genera pendientes<br>
isoblock.accion.accionGenerica.insertaAccionGenerica1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.accion.accionGenerica.asignaAccionGenerica = <br>&nbsp;&nbsp;Se le ha asignado una acci&oacute;n para su evaluaci&oacute;n y an&aacute;lisis, favor de atenderla. <br>
isoblock.accion.accionGenerica.asignaAccionGenerica1 = <br>&nbsp;&nbsp; El encargado del departamento al que se asign&oacute; la acci&oacute;n la ha indicado como no procedente. <br> Por el siguiente motivo: <br />
isoblock.accion.accionGenerica.setImplementada = <br>&nbsp;&nbsp;Una acci&oacute;n de mejora continua ha sido marcada como implementada lista para su verificaci&oacute;n, favor de atenderla.<br>
isoblock.auditoria.auditoria.updateAuditoria = <br>&nbsp;&nbsp; Se acaba de reprogramar una auditor&iacute;a, favor de atenderla.
isoblock.auditoria.auditoria.insertAuditoria = <br>&nbsp;&nbsp; Se ha creado una nueva auditor&iacute;a en la cual usted ha sido asignado como auditor encargado, favor de atenderla.
isoblock.auditoria.auditoria.noaceptaAuditoria = <br>&nbsp;&nbsp; El encargado no estuvo de acuerdo con la notificaci&oacute;n de auditor&iacute;a.
isoblock.auditoria.auditoria.aceptaAuditoria = <br>&nbsp;&nbsp; Se ha aceptado la realizaci&oacute;n de la auditor&iacute;a en la que usted est&aacute; participando, tiene un nuevo pendiente de 'Reportes de Auditor\u00edas por Realizar', favor de atenderlo.
isoblock.auditoria.auditoria.noaceptaAuditoriaReporte = <br>&nbsp;&nbsp; El encargado no estuvo de acuerdo con el reporte de auditor&iacute;a dando como argumento lo siguiente<br>
isoblock.auditoria.auditoria.aceptaAuditoriaReporte = <br>&nbsp;&nbsp; El reporte de auditor&iacute;a ya fue aprobado por todos los encargados.
isoblock.auditoria.auditoria.enviarCorreo.Estatus = Estado:
isoblock.auditoria.auditoria.enviarCorreo.Ubicacion = Departamento:
isoblock.auditoria.auditoria.enviarCorreo.Areas = Procesos:
isoblock.auditoria.auditoria.enviarCorreo.Fechainicio = Fecha inicio:
isoblock.auditoria.auditoria.enviarCorreo.Fechaterminacion = Fecha terminaci&oacute;n:
isoblock.auditoria.auditoria.enviarCorreo.Cuestionario = Cuestionario:
isoblock.auditoria.auditoria.enviarCorreo.Objetivos = Objetivos:
isoblock.auditoria.auditoria.enviarCorreo.Planeacion = Planeaci&oacute;n:
isoblock.auditoria.auditoria.enviarCorreo.Comentarios = Comentarios:
isoblock.documentos.documento.enviaNotificacion = Se ha creado una secuencia de autorizaci&oacute;n para el siguiente documento. Este aviso es informativo y no genera pendientes.
isoblock.accion.accionPreventiva.enviaNotificacion.notificacion = Notificaci&oacute;n de un nuevo pendiente en acciones preventivas en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.accionPreventiva.enviaNotificacion.notificacion1 = <br>Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.common.threadFunction.checaAuditorias = <br>&nbsp;&nbsp; Dentro del sistema Bnext QMS, se ha vencido la fecha de realizaci&oacute;n en las siguientes AUDITOR&Iacute;AS\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Clave de auditor&iacute;a</b></td><td width\='70%' bgcolor\='\#FFFFFF'><b>Responsable</b></td></tr><tr><td colspan='2'><!--Se habilito el boton para la cancelacion de las auditor\u00edas--></td></tr>
isoblock.common.threadFunction.avisoMes = <br>&nbsp;&nbsp; Aviso para la impresion del Reporte de Acciones.
isoblock.common.threadFunction.checaAutorizaciones = <br>&nbsp;&nbsp; En la secuencia de autorizaci&oacute;n se le ha expirado el tiempo de atenci&oacute;n a:&nbsp;
isoblock.common.threadFunction.checaAccionesUbicacion=<br>&nbsp;&nbsp; Dentro del sistema Bnext QMS, existen acciones a tomar pertenecientes a su departamento que han alcanzado su fecha de realizaci&oacute;n en las siguientes ACCIONES\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Clave de acci&oacute;n</b></td><td width\='70%' bgcolor\='\#FFFFFF'><b>Responsable</b></td></tr>
isoblock.common.Utilities.creaForma.clave=Clave:
isoblock.accion.accionGenerica.vchClave.PorAsignarse=Por Asignarse
isoblock.accion.accionGenerica.accionT.ACCION=ACCI&Oacute;N
isoblock.accion.accionGenerica.comboProcede.SI=SI
isoblock.accion.accionGenerica.comboProcede.NO=NO
isoblock.accion.accionGenerica.comboAcepto.NOACEPTO=NO ACEPTO
isoblock.accion.accionGenerica.comboAcepto.SIACEPTO=SI ACEPTO
isoblock.accion.accionGenerica.comboEstado.REPORTADA=REPORTADA
isoblock.accion.accionGenerica.comboEstado.ASIGNADA=ASIGNADA
isoblock.accion.accionGenerica.comboEstado.ANALIZADAYENIMPLEMENTACION=ANALIZADA
isoblock.accion.accionGenerica.comboEstado.IMPLEMENTADAYVERIFICADA=EJECUTADA
isoblock.accion.accionGenerica.comboEstado.APROBADA=VERIFICADA
isoblock.accion.accionGenerica.comboEstado.CERRADA=CERRADA
isoblock.accion.accionGenerica.comboEstado.PROCEDE=EFECTIVA
isoblock.accion.accionGenerica.comboEstado.NOPROCEDE=NO EFECTIVA
isoblock.accion.accionGenerica.comboEstado.CANCELADA=NO PROCEDE
isoblock.accion.accionGenerica.accionT.ACCIONDEMEJORACONTINUA=ACCI&Oacute;N DE MEJORA CONTINUA
isoblock.accion.accionGenerica.accionT.ACCIONCORRECTIVA=ACCI&Oacute;N CORRECTIVA
isoblock.accion.accionGenerica.accionT.ACCIONPREVENTIVA=ACCI&Oacute;N PREVENTIVA
isoblock.auditoria.auditoria.deleteLinks.mensaje=No hay auditores ayudantes
isoblock.auditoria.auditoria.cambiaEstado.mensaje=Cambio de estado
isoblock.auditoria.auditoria.deleteAuditoria.mensaje=Error en la funcion deleteAuditoria
isoblock.auditoria.auditoria.updateAuditoria.mensaje=Error en updateAuditoria
isoblock.auditoria.auditoria.comboEstado.Programada=Programada
isoblock.auditoria.auditoria.comboEstado.Notificada=Notificada
isoblock.auditoria.auditoria.comboEstado.Realizada=Realizada
isoblock.auditoria.auditoria.comboEstado.Cerrada=Cerrada
isoblock.common.Properties.printAscDesc.asc=Ordenado ascendente
isoblock.common.Properties.printAscDesc.desc=Ordenado descendente
isoblock.common.Properties.printAscDescNumero.asc=Ascender
isoblock.common.Properties.printAscDescNumero.desc=Descender
isoblock.common.Utilities.monthDate.Enero=Enero
isoblock.common.Utilities.monthDate.Febrero=Febrero
isoblock.common.Utilities.monthDate.Marzo=Marzo
isoblock.common.Utilities.monthDate.Abril=Abril
isoblock.common.Utilities.monthDate.Mayo=Mayo
isoblock.common.Utilities.monthDate.Junio=Junio
isoblock.common.Utilities.monthDate.Julio=Julio
isoblock.common.Utilities.monthDate.Agosto=Agosto
isoblock.common.Utilities.monthDate.Septiempre=Septiempre
isoblock.common.Utilities.monthDate.Octubre=Octubre
isoblock.common.Utilities.monthDate.Noviembre=Noviembre
isoblock.common.Utilities.monthDate.Diciembre=Diciembre
isoblock.common.Utilities.shortMonthDate.Ene=Ene
isoblock.common.Utilities.shortMonthDate.Feb=Feb
isoblock.common.Utilities.shortMonthDate.Mar=Mar
isoblock.common.Utilities.shortMonthDate.Abr=Abr
isoblock.common.Utilities.shortMonthDate.May=May
isoblock.common.Utilities.shortMonthDate.Jun=Jun
isoblock.common.Utilities.shortMonthDate.Jul=Jul
isoblock.common.Utilities.shortMonthDate.Ago=Ago
isoblock.common.Utilities.shortMonthDate.Sep=Sep
isoblock.common.Utilities.shortMonthDate.Oct=Oct
isoblock.common.Utilities.shortMonthDate.Nov=Nov
isoblock.common.Utilities.shortMonthDate.Dic=Dic
isoblock.common.comentario.deleteComentario.mensaje=Error en la funcion deleteUser
isoblock.common.comentario.insertComentario.mensaje=En el insert
isoblock.common.comentario.getStringTipo.Hallazgo=Hallazgo
isoblock.common.comentario.getStringTipo.Comentario=Comentario
isoblock.common.comentario.getStringTipo.ComentarioaNotificaciondeAuditoria=Comentario a Notificaci&oacute;n de Auditor&iacute;a
isoblock.common.comentario.getStringTipo.Minuta=Minuta
isoblock.configuracion.question.comboEstadoPregunta.TODAS=-- TODAS --
isoblock.configuracion.question.comboEstadoPregunta.ACTIVA=ACTIVA
isoblock.configuracion.question.comboEstadoPregunta.CANCELADA=CANCELADA
isoblock.configuracion.seccion.strMensaje.enblanco=en blanco
isoblock.configuracion.seccion.strMensaje.errorId=No se tiene un identificador de seccion correcto
isoblock.configuracion.seccion.strMensaje.errorObtener=Error al tratar de obtener un id para la nueva seccion
isoblock.configuracion.tipo.deleteTipo.mensaje=Error en la funcion deleteTipo. Clase: tipo
isoblock.configuracion.unidadorganizacional.deleteUnidadOrganizacional.mensaje=Error en la funcion deleteUser. Clase: unidadorganizacional
isoblock.configuracion.unidadorganizacional.updateUnidadOrganizacional.mensaje=Error en la funcion updateUser. Clase: unidadorganizacional
isoblock.configuracion.unidadorganizacional.deleteUser.mensaje=Error en la funcion deleteUser
isoblock.configuracion.unidadorganizacional.updateUser.mensaje=La cuenta de usuario elegida, ya existe.
isoblock.configuracion.unidadorganizacional.comboRolAuditorias.Lector=Lector
isoblock.configuracion.unidadorganizacional.comboRolAuditorias.Editor=Editor
isoblock.configuracion.unidadorganizacional.comboRolAuditorias.Encargado=Encargado
isoblock.configuracion.unidadorganizacional.comboRolAdministracion.Administrador=Administrador
isoblock.cuestrionarios.cuestionario.getStringEstado.Borrador=Borrador
isoblock.cuestrionarios.cuestionario.getStringEstado.Activo=Activo
isoblock.cuestrionarios.cuestionario.getStringEstado.Cancelado=Cancelado
isoblock.cuestrionarios.cuestionario.deleteCuestionario.mensaje=Error en la funcion deleteCuestionario
isoblock.cuestrionarios.cuestionario.cambiaEstado.mensaje=Cambio de estado
isoblock.documentos.documento.getStringEstado.Revision=Revisi&oacute;n
isoblock.documentos.documento.getStringEstado.Activo=Activo
isoblock.documentos.documento.getStringEstado.Activos=Activos
isoblock.documentos.documento.getStringEstado.Edicion=Edici&oacute;n
isoblock.documentos.documento.getStringEstado.Cancelado=Cancelado
isoblock.documentos.documento.getStringEstado.Cancelados=Cancelados
isoblock.documentos.documento.getStringEstado.Descontinuados=Descontinuados
isoblock.documentos.documento.getStringEstado.Todos=-- TODOS --
isoblock.accion.accionGenerica.AceptaAccionGenerica=<br>&nbsp;&nbsp;Se ha finalizado la implementaci&oacute;n y verificaci&oacute;n de una acci&oacute;n en su departamento la cual requiere su aceptaci&oacute;n, favor de atenderla. <br>
isoblock.accion.accionGenerica.enviaNotificacion.noCorreo=El o los destinatarios del correo no pudieron ser notificado(s) via correo electr&oacute;nico.
isoblock.accion.accionGenerica.insertaAccionGenerica.error=No se pudo agregar la nueva acci&oacute;n.
isoblock.accion.accionCorrectiva.noCorreo=El encargado en turno de la acci&oacute;n no pudo ser notificado via correo electr&oacute;nico.
isoblock.accion.accionMejoraContinua.noCorreo=El encargado no pudo ser notificado via correo electr&oacute;nico.
isoblock.auditoria.auditoria.noCorreo=El auditor encargado no pudo ser notificado via correo electr&oacute;nico.
isoblock.documentos.documento.enviaNotificacion.noCorreo=El encargado de documentos no pudo ser notificado via correo electr&oacute;nico.
isoblock.documentos.documento.updateDocumento.noCambio=(No hubo cambios, el administrador no ser&aacute; notificado)
isoblock.accion.accionPreventiva.noCorreo=El encargado no pudo ser notificado via correo electr&oacute;nico.
isoblock.configuracion.user.insertUser.claveExistente=La cuenta de usuario elegida, ya existe.
isoblock.common.correo.enviacorreo.identificador=Bnext QMS
isoblock.common.Utilities.creaForma.porMedio=Por medio de este correo puede visualizar el pendiente.
isoblock.common.Utilities.creaForma.enviar=Enviar
error.ErrorNoSePuedeMostrarElTexto=Error: No se puede mostrar el texto
isoblock.correo.formatoCorreoArriba=<head><meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1"></head><body><table width='600' border='0' cellpadding='0' cellspacing='0'><tr><td><table width='600' height='30' bgcolor='#1A6FAC' cellpadding='0' cellspacing='0' style='border-bottom-color:#C8E2FB; border-top-color:#C8E2FB; border-left-color:1A6FAC; border-right-color:1A6FAC' border='2' ><tr><td style="font-family:Verdana, Arial, Helvetica, sans-serif; color:#F5F7F9; font-size:11px">&nbsp;&nbsp;<b>
isoblock.correo.formatoCorreoEnmedio=</b></td></tr></table></td></tr><tr><td><table width='600' border='0' cellpadding='3' cellspacing='0' bgcolor='#F5F7F9' style="font-family:Arial, Verdana, Helvetica, sans-serif; color:#000000; font-size:11px"><tr><td>&nbsp;&nbsp;
isoblock.correo.variablesCorreo1=</tr></td></table></td></tr><tr><td><table border='0' cellpadding='0' cellspacing='0'>
isoblock.correo.variablesCorreo2=</table></td></tr><tr><td><table width='600' border='0' cellpadding='0' cellspacing='3' bgcolor='#F5F7F9' style="font-family:Arial, Verdana, Helvetica, sans-serif; color:#000000; font-size:11px"><tr><td>&nbsp;&nbsp;
isoblock.correo.formatoCorreoAbajo=<br><hr><b>Nota: </b>Correo electr&oacute;nico generado autom&aacute;ticamente por el sistema <b>Bnext QMS&reg</b>.</td></tr></table></td></tr></table></body>
isoblock.correo.accion.variablesCorreo1=Clave:
isoblock.correo.accion.variablesCorreo2=Acci&oacute;n:
isoblock.correo.accion.variablesCorreo3=Fuente:
isoblock.correo.accion.variablesCorreo4=Departamento:
isoblock.correo.accion.variablesCorreo5=Avance:
isoblock.auditoria.auditoria.enviarCorreo.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de AUDITOR&Iacute;AS en el sistema <b>Bnext QMS<b>. <br>
isoblock.auditoria.auditoria.enviarCorreo.notificacion1=<br> Para atenderla, podr&aacute; visualizarla en el &aacute;rea de pendientes dentro del sistema.
isoblock.correo.auditoria.variablesCorreo1=Clave:
isoblock.correo.auditoria.variablesCorreo2=Estado:
isoblock.correo.auditoria.variablesCorreo3=Departamento:
isoblock.correo.auditoria.variablesCorreo4=Proceso(s):
isoblock.correo.auditoria.variablesCorreo5=Horario:
isoblock.correo.auditoria.variablesCorreo6=&nbsp;&nbsp;&nbsp;Fecha de inicio:
isoblock.correo.auditoria.variablesCorreo7=&nbsp;&nbsp;&nbsp;Fecha de terminaci&oacute;n
isoblock.correo.auditoria.variablesCorreo8=Cuestionario:
isoblock.correo.auditoria.variablesCorreo9=Objetivos/Alcance:
isoblock.correo.auditoria.variablesCorreo10=Comentarios:
isoblock.auditoria.auditoria.notificaAuditoria=<br>&nbsp;&nbsp; Se ha creado una nueva auditor&iacute;a en la cual usted ha sido agregado como participante, favor de atenderla.
isoblock.auditoria.auditoria.noAceptaAuditoria=<br>&nbsp;&nbsp; No se ha aceptado la realizaci&oacute;n de la auditor&iacute;a en la que usted est&aacute; participando, favor de atenderla.
isoblock.correo.notificacion1=Para atenderlo, podr&aacute; visualizarlo en el &aacute;rea de pendientes dentro del sistema.
isoblock.correo.accion.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de ACCIONES del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.auditoria.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de AUDITOR&Iacute;AS del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.documento.notificacion=Notificaci&oacute;n de la secci&oacute;n de DOCUMENTOS del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.documento.variablesCorreo1=Estado\:
isoblock.correo.documento.variablesCorreo2=T&iacute;tulo:
isoblock.correo.documento.variablesCorreo3=Clave:
isoblock.correo.documento.variablesCorreo4=Versi&oacute;n
isoblock.correo.documento.variablesCorreo5=Fecha y Hora de creaci&oacute;n:
isoblock.correo.documento.variablesCorreo6=Autor:
isoblock.correo.documento.variablesCorreo7=&Aacute;rea:
isoblock.correo.secuencia.notificacion=Notificaci&oacute;n de la secci&oacute;n de SECUENCIAS del M&oacute;dulo de Documentos del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.secuencia.variablesCorreo1=Clave de la secuencia:
isoblock.correo.secuencia.variablesCorreo2=T&iacute;tulo del documento:
isoblock.correo.secuencia.variablesCorreo3=Clave del documento:
isoblock.correo.secuencia.variablesCorreo4=Autor:
isoblock.correo.secuencia.variablesCorreo5=Fecha l&iacute;mite de autorizaci&oacute;n:
isoblock.correo.secuencia.variablesCorreo6=Raz&oacute;n:
isoblock.correo.solicitud.notificacion=Notificaci&oacute;n de la secci&oacute;n de SOLICITUDES del sistema <b>Bnext QMS</b>.<br>
isoblock.correo.solicitud.variablesCorreo1=Tipo de solicitud:
isoblock.correo.solicitud.variablesCorreo2=T&iacute;tulo del documento:
isoblock.correo.solicitud.variablesCorreo3=Clave del documento:
isoblock.correo.solicitud.variablesCorreo4=Fecha de solicitud:
isoblock.correo.solicitud.variablesCorreo5=Solicitante:
isoblock.correo.solicitud.variablesCorreo6=Raz&oacute;n:
isoblock.correo.auto.notificacion=Notificaci&oacute;n de actividad en el sistema <b>Bnext QMS</b>.<br>
isoblock.configuracion.user.insertUser.limiteUsuarios=Ha llegado al l&iacute;mite de usuarios posibles seg&uacute;n su licencia del sistema. <br>
isoblock.configuracionhandle.Si=S&iacute;
isoblock.configuracionhandle.No=No
isoblock.configuracionhandle.EspMex=Espa\u00f1ol(M&eacute;xico)
isoblock.configuracionhandle.IngEstUn=Ingl&eacute;s(Estados Unidos)
isoblock.reportesgenerales.comboGraficaX.Estado=Estado
isoblock.reportesgenerales.comboGraficaX.Responsable=Responsable
isoblock.reportesgenerales.comboGraficaX.Autor=Autor
isoblock.reportesgenerales.comboGraficaX.Ubicacion=${Department}
isoblock.reportesgenerales.comboGraficaX.Sociedad=${Facility}
isoblock.accion.accionGenerica.comboGraficaX.Tipodeaccion=Tipo de acci&oacute;n
isoblock.accion.accionGenerica.comboTipoAccion.Todas=-- TODAS --
isoblock.accion.accionGenerica.comboTipoAccion.ACC=Correctiva
isoblock.accion.accionGenerica.comboTipoAccion.APP=Preventiva
isoblock.accion.accionGenerica.comboTipoAccion.AMC=Mejora Continua
isoblock.reportesgenerales.Todos=-- Todos --
isoblock.reportesgenerales.comboTipoGrafica.Bar=Gr&aacute;fica de Barras
isoblock.reportesgenerales.comboTipoGrafica.Pie=Gr&aacute;fica Circular
isoblock.reportesgenerales.comboTipoGrafica.Bar3D=Gr&aacute;fica de Barras 3D
isoblock.reportesgenerales.comboTipoGrafica.Pie3D=Gr&aacute;fica Circular 3D
isoblock.reportesgenerales.comboGraficaX.Originador=Originador
isoblock.reportesgenerales.comboGraficaX.UsuariosporUbicacion=Usuarios por departamento
isoblock.reportesgenerales.comboGraficaX.AreasporUbicacion=Procesos por departamento
isoblock.accion.accionGenerica.comboEstado.NOCERRADA=*NO CERRADA
isoblock.reportesgenerales.comboGraficaX.Mes=Mes
isoblock.reportesgenerales.comboGraficaX.Autorizante=Autorizante
isoblock.Proyectos.Actividad.FechaInicio=Fecha de Inicio
isoblock.Proyectos.Actividad.FechaFin=Fecha Final
isoblock.Proyectos.Actividad.Verificador=Verificador
isoblock.Proyectos.Actividad.FechaVerificador=Fecha de Verificaci&oacute;n
isoblock.Proyectos.Actividad.Presupuesto=Presupuesto
isoblock.Proyectos.Actividad.Avance=Avance
isoblock.Proyectos.Actividad.PeticionPresupuesto=Petici&oacute;n de Presupuesto
isoblock.Proyectos.Actividad.TipoCambioDesconocido=Tipo de Cambio Desconocido
isoblock.correo.actividad.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de PROYECTOS en el sistema <b>Bnext QMS</b>.<br>
isoblock.accion.actividad.insertaActividadResponsable=<br>&nbsp;&nbsp; Se le ha asignado como encargado de la implementaci&oacute;n de una actividad de un proyecto, favor de atenderla.
isoblock.proyecto.Proyecto.comboEstado.CREADO=Creado
isoblock.proyecto.Proyecto.comboEstado.LIBERADO=Liberado
isoblock.proyecto.Proyecto.comboEstado.CONCLUIDO=Concluido
isoblock.proyecto.Proyecto.comboEstado.CERRADO=Cerrado
isoblock.correo.actividad.variablesCorreo1=Nombre de la Meta\:
isoblock.correo.actividad.variablesCorreo2=Nombre de la Actividad\:
isoblock.correo.actividad.variablesCorreo3=Nombre del Proyecto\:
isoblock.accion.actividad.insertaActividadVerificador=<br>&nbsp;&nbsp; Se le ha asignado como verificador  de una actividad de un proyecto, favor de atenderla.
isoblock.proyectos.actividad.insertaActividad.error=No se pudo agregar la actividad.
isoblock.proyecto.actividad.enviaNotificacion.noCorreo=El responsable y/o el verificador de la actividad no pudieron ser notificado via correo electr&oacute;nico.
isoblock.proyectos.actividad.setProyectoConcluido.detalle= Existe un proyecto que ha sido concluido, favor de atender el pendiente de proyectos por cerrar. <br><br>
isoblock.proyectos.actividad.setConcluida.detalle=<br>Dentro del sistema existe una actividad marcada como concluida en espera de su verificaci&oacute;n, favor de atenderla. <br>
isoblock.correo.cambioPendiente.variablesCorreo1=Actividad:
isoblock.correo.cambioPendiente.variablesCorreo2=Proyecto:
isoblock.correo.cambioPendiente.variablesCorreo3=Tipo de Cambio:
isoblock.correo.cambioPendiente.variablesCorreo4=Valor Anterior:
isoblock.correo.cambioPendiente.variablesCorreo5=Nuevo Valor:
isoblock.correo.cambioPendiente.variablesCorreo6=Raz&oacute;n de Cambio\:
isoblock.proyectos.actividadCambio.autorizado.detalle=<br>&nbsp;&nbsp; Una petici&oacute;n de cambio de una actividad que usted solicit&oacute; ha sido autorizada. <br>
isoblock.proyectos.actividadCambio.rechazado.detalle=<br>&nbsp;&nbsp; Una petici&oacute;n de cambio de una actividad que usted solicit&oacute; ha sido rechazada. <br>
isoblock.correo.cambioPendiente.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de PROYECTOS en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.proyecto.notificacion=Notificaci&oacute;n de un nuevo pendiente en la secci&oacute;n de PROYECTOS en el sistema <b>Bnext QMS</b>.<br>
isoblock.proyectos.proyecto.setCancelado.detalle=<br>&nbsp;&nbsp; Un proyecto en el que usted participa ha sido cancelado, favor de atenderlo.
isoblock.correo.proyecto.variablesCorreo1=Clave Proyecto:
isoblock.correo.proyecto.variablesCorreo2=Clave Actividad:
isoblock.correo.proyecto.variablesCorreo3=Fecha Inicio:
isoblock.correo.proyecto.variablesCorreo4=Fecha Final:
isoblock.proyectos.actividad.peticionpresupuesto.autorizado=<br>Una petici&oacute;n de presupuesto de una actividad que usted solicit&oacute; ha sido autorizada. <br>
isoblock.proyectos.actividad.peticionpresupuesto.rechazado=<br>Una petici&oacute;n de presupuesto de una actividad que usted solicit&oacute; ha sido rechazada, favor de atenderla. <br>
isoblock.correo.peticionPresupuesto.notificacion=Notificaci&oacute;n de una autorizaci&oacute;n de petici&oacute;n de presupuesto en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.peticionPresupuesto.variablesCorreo1=Proyecto:
isoblock.correo.peticionPresupuesto.variablesCorreo2=Actividad:
isoblock.correo.peticionPresupuesto.variablesCorreo3=Cantidad a pedir:
isoblock.correo.peticionPresupuesto.variablesCorreo4=Razon de la petici&oacute;n:
isoblock.correo.peticionPresupuesto.variablesCorreo5=Raz&oacute;n no autorizado:
isoblock.correo.cambioPendiente.variablesCorreo7=Raz&oacute;n no autorizado:
isoblock.configuracion.unidadorganizacional.comboRolProyectos.Contabilidad=Contabilidad
isoblock.correo.presupuesto.variablesCorreo1=Proyecto:
isoblock.correo.presupuesto.variablesCorreo2=Actividad:
isoblock.correo.presupuesto.notificacion=Notificaci&oacute;n de una modificaci&oacute;n de presupuesto  de una actividad en el sistema <bBnext QMS>.<br>
isoblock.proyectos.presupuesto.AgregarPresupuesto=<br>&nbsp;&nbsp; Se ha modificado el rubro de una actividad en que usted participa <br>
isoblock.correo.presupuesto.variablesCorreo3=Nuevo presupuesto:
isoblock.proyectos.actividad.CorreoPorcentajeActividad=<br>&nbsp;&nbsp; Se ha disminuido el porcentaje de avance de una actividad en que usted participa<br>
isoblock.correo.porcentajeActividad.notificacion=Notificaci&oacute;n de modificaci&oacute;n de avance de una actividad en el sistema <bBnext QMS</b>.<br>
isoblock.correo.porcentajeActividad.variablesCorreo1=Proyecto:
isoblock.correo.porcentajeActividad.variablesCorreo2=Actividad:
isoblock.correo.porcentajeActividad.variablesCorreo3=Nuevo porcentaje de avance:
isoblock.proyectos.proyecto.correoProyectoLiberado=<br>&nbsp;&nbsp; Se ha liberado un proyecto  en el que usted participa.<br>
isoblock.correo.proyectoLiberado.variablesCorreo1=Proyecto:
isoblock.correo.proyectoLiberado.notificacion=Notificaci&oacute;n de un nuevo proyecto liberado en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.autorBajaEstadoActividad.variablesCorreo1=Proyecto:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo2=Actividad:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo3=Nuevo porcentaje de avance:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo4=Rubro:
isoblock.correo.autorBajaEstadoActividad.notificacion=Notificaci&oacute;n de una modificaci&oacute;n del presupuesto y avance de una actividad  en el sistema <b>Bnext QMS</b>.<br>
isoblock.proyectos.CambioPendiente.TipoCambioFechaInicio=Fecha Inicial
isoblock.proyectos.CambioPendiente.TipoCambioFechaFin=Fecha Final
isoblock.proyectos.CambioPendiente.TipoCambioVerificador=Verificador
isoblock.proyectos.CambioPendiente.TipoCambioFechaVerificador=Fecha Verificador
isoblock.proyectos.CambioPendiente.TipoCambioDesconocido=Cambio Desconocido
isoblock.proyectos.actividad.CorreoAutorInsertaRubroActividadConcluida=<br>&nbsp;&nbsp; Se ha agregado un rubro y disminuido el porcentaje de avance de una actividad conluida en que usted participa<br>
isoblock.proyectos.actividad.CorreoAutorModificaRubroActividadConcluida=<br>&nbsp;&nbsp; Se ha modificado un rubro y disminuido el porcentaje de avance de una actividad conluida en que usted participa<br>
isoblock.proyectos.actividad.CorreoAutorModificaRubroActividad=<br>&nbsp;&nbsp; Se ha modificado un rubro de una actividad  en que usted participa<br>
isoblock.correo.presupuestoActividad.variablesCorreo1=Proyecto:
isoblock.correo.presupuestoActividad.variablesCorreo2=Actividad:
isoblock.correo.presupuestoActividad.variablesCorreo3=Nombre rubro:
isoblock.correo.presupuestoActividad.variablesCorreo4=Cantidad total del rubro:
isoblock.correo.autorBajaEstadoActividad.variablesCorreo5=Cantidad total del rubro:
isoblock.correo.presupuestoActividad.notificacion=Notificaci&oacute;n de una modificaci&oacute;n del presupuesto de actividad  en el sistema <b>Bnext QMS</b>.<br>
actividadeslistpeticionespresupuesto.NoSeHaRealizadoNingunCambio=No se ha realizado ning\u00fan cambio.
isoblock.correo.ModuloProyecto.notificacion=Notificaci&oacute;n del M&oacute;dulo de Proyectos en el sistema <b>Bnext QMS</b>.<br>
isoblock.correo.Saludo=Estimado(a) 
isoblock.common.threadFunction.checaProyectosPorCerrar=<br>Los siguientes Proyectos, en los cuales usted est&aacute; a cargo, han alcanzado su fecha de finalizaci&oacute;n, pero no han terminado\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Autor</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Responsable</b></td></tr>
isoblock.common.threadFunction.checaActividadesPorTerminarResponsables=<br>Las siguientes Actividades, en las cuales usted es responsable, han alcanzado su fecha de finalizaci&oacute;n, pero no han sido terminadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.common.threadFunction.checaActividadesPorTerminarEncargados=<br>Las siguientes Actividades pertenecen a un proyecto donde usted est&aacute; a cargo y han alcanzado su fecha de finalizaci&oacute;n, pero no han sido terminadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='35%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.common.threadFunction.checaProyectoActividadesPorVerificar=<br>Las siguientes Actividades, en las cuales usted es verificador, han alcanzado su fecha de finalizaci&oacute;n, pero no han sido verificadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.common.threadFunction.checaProyectoActividadesPorVerificarEncargados=<br>Las siguientes Actividades pertenecen a un proyecto donde usted est&aacute; a cargo y han alcanzado su fecha de finalizaci&oacute;n, pero no han sido verificadas\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Actividad</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Meta</b></td><td width\='30%' bgcolor\='\#FFFFFF'><b>Proyecto</b></td></tr>
isoblock.accion.accionGenerica.comboGraficaX.Fuente=Fuente
isoblock.common.threadFunction.checaAcciones=<br>&nbsp;&nbsp; Dentro del sistema Bnext QMS existen acciones a tomar las cuales han alcanzado su fecha de realizaci&oacute;n en las siguientes ACCIONES\:<br><table width\='100%' border\='0' cellpadding\='2' cellspacing\='0' style\='font-family\:Arial, Verdana, Helvetica, sans-serif; color\:\#000000; font-size\:11px'><tr><td width\='35%' bgcolor\='\#FFFFFF'><b>Clave de acci&oacute;n</b></td><td width\='70%' bgcolor\='\#FFFFFF'><b>Departamento</b></td></tr>
isoblock.common.comentario.comboEstado.Todos=-- TODOS --
isoblock.common.comentario.comboEstado.Nuevo=Nuevo
isoblock.common.comentario.comboEstado.Leido=Le&iacute;do
isoblock.common.comentario.comboEstado.Contestado=Contestado
isoblock.documentos.documento.getStringEstado.DocumentoDeProyectos=Documentos de proyectos
isoblock.common.Database.AreaALaQuePertenece=Proceso al que pertenece
isoblock.common.Database.PlantaALaQuePertenece=${Facility} a la que pertenece
isoblock.common.Database.UbicacionALaQuePertenece=Departamento al que pertenece
isoblock.accion.accionGenerica.comboTipoAccion.APD=Sobre producto
isoblock.accion.accionGenerica.comboTipoAccion.APY=Sobre proyecto
isoblock.configuracion.user.ReportedeGerencial=Reporte Gerencial
isoblock.user.permisosReportes.ReportedeConfiguracion=Configuraci&oacute;n
isoblock.configuracion.user.ReportedeAuditorias=Auditor&iacute;as
isoblock.configuracion.user.ReportedeCuestionarios=Cuestionarios
isoblock.configuracion.user.ReportedeAcciones=Acciones
isoblock.configuracion.user.ReportedeDocumentos=Documentos
isoblock.configuracion.user.ReportedeEncuesta=Encuestas
isoblock.configuracion.user.ReportedeQuejas=Quejas
isoblock.configuracion.user.ReportedeIndicadores=Indicadores
isoblock.configuracion.user.ReportedeProyectos=Proyectos
isoblock.configuracion.userhandle.ValorStatusUsuario=Activo
isoblock.configuracion.userhandle.ValorStatusUsuario2=Inactivo
isoblock.correo.actividad.variablesCorreo4=Porcentaje Avance
isoblock.correo.secuencia.variablesCorreo7=Comentario\:
isoblock.configuracion.usuarioexterno.comboProyectoClienteExterno.noesexterno=El Usuario No es Externo
isoblock.documento.comboCampos.Todos=--CUALQUIER CAMPO--
isoblock.documento.comboCampos.Clave=Clave
isoblock.documento.comboCampos.Nombre=T&iacute;tulo
isoblock.documento.comboCampos.Originador=Originador
isoblock.documento.comboCampos.RazonAltaCambio=Raz&oacute;n Alta/Cambio
documentos.documentossecuencia.Searechazadoiniciarlasecuenciadelasolicituddeldocumentoyseenvioelavisoaloriginadordedichodocumento=Se a rechazado iniciar la secuencia de la solicitud del documento y se envio el aviso al originador de dicho documento
isoblock.correo.quejas.notificacion=Notificaci&oacute;n del m&oacute;dulo de QUEJAS del sistema <br>Bnext QMS<br>
isoblock.correo.quejas.variablesCorreo1=Clave:
isoblock.correo.quejas.variablesCorreo2=Estado:
isoblock.correo.quejas.variablesCorreo3=Fuente:
isoblock.correo.quejas.variablesCorreo4=Departamento:
isoblock.correo.quejas.variablesCorreo5=Responsable Asignado:
isoblock.correo.encuesta.notificacion=Notificaci&oacute;n del M&oacute;dulo de ENCUESTAS del sistema Bnext QMS.
isoblock.correo.encuesta.variablesCorreo1=Estado:
isoblock.correo.encuesta.variablesCorreo2=T&iacute;tulo:
isoblock.correo.encuesta.variablesCorreo3=Clave:
isoblock.correo.encuesta.variablesCorreo4=Encargado de la encuesta:
isoblock.correo.encuesta.variablesCorreo5=Inicia:
isoblock.correo.encuesta.variablesCorreo6=Termina:
isoblock.correo.encuesta.variablesCorreo7=Descripcion:
isoblock.correo.encuesta.variablesCorreo8=Comentarios generales:
isoblock.common.Database.listControlGeneratorCompleto.Agregar=Agregar
isoblock.correo.indicador.notificacion=Notificaci&oacute;n del M&oacute;dulo de INDICADORES del Sistema <b>Bnext QMS</b>.<br>
isoblock.correo.indicador.variablesCorreo1=Indicador:
isoblock.correo.indicador.variablesCorreo2=Proceso:
isoblock.correo.indicador.variablesCorreo3=Fecha:
isoblock.correo.Configuracion.notificacion=Notificaci&oacute;n del M&oacute;dulo de CONFIGURACI&Oacute;N del sistema Bnext QMS.
isoblock.correo.Prueba.notificacion=Este es un mail de prueba enviado por el sistema Bnext QMS para confirmar su configuraci&oacute;n
isoblock.correo.Configuracion.variablesCorreo1=<b>Nombre completo:</b>
isoblock.correo.Configuracion.variablesCorreo2=<b>Nombre de usuario:</b>
isoblock.correo.Configuracion.variablesCorreo3=<b>Correo:</b>
isoblock.correo.Configuracion.variablesCorreo4=<b>Perfil:</b>
isoblock.correo.Configuracion.variablesCorreo5=<b>Detalles de perfil:</b>
isoblock.correo.recoveruser.notificacion=Notificaci&oacute;n de recuperaci&aacute;n de USUARIO del sistema Bnext QMS.
isoblock.correo.recoverpass.notificacion=Notificaci&oacute;n de recuperaci&aacute;n de CONTRASE\u00d1A del sistema Bnext QMS.
isoblock.correo.Configuracion.variablesCorreo5=<b>Detalles de perfil:</b>
isoblock.common.Database.listControlGeneratorCompleto.Estado= Estado
isoblock.common.Database.listControlGeneratorCompleto.Autor=Autor
isoblock.common.Database.listControlGeneratorCompleto.Ver= Ver
isoblock.common.Database.listControlGeneratorCompleto.Cuestionario= Cuestionario
isoblock.common.Database.listControlGeneratorCompleto.Borrar= Borrar
isoblock.common.Database.listControlGeneratorCompleto.GuardarCambios= Guardar Cambios
isoblock.common.Database.controlListaUsuariosTodo.ComboTodos= -- TODOS --
isoblock.common.Database.controlListaUsuariosTodo.ComboSeleccione= -- Seleccione --
isoblock.quejas.queja.ElestadoActualesREPORTADAporAsignarResponsable= El estado actual es REPORTADA por asignar Responsable.
isoblock.configuracion.catalogo.GetCatalogoName.ElCatalogoNoExisteOFueBorrado= - El catalalogo no existe o fue borrado -
isoblock.configuracion.user.TuCuentaEnElSistemaBnextDPMSHaSidoDesactivadaPorElAdministradorDelSistema= T&uacute; cuenta en el sistema Bnext QMS ha sido desactivada por el administrador del sistema.
isoblock.configuracion.user.TuCuentaEnElSistemaBnextDPMSHaSidoActivadaPorElAdministradorDelSistema= T&uacute; cuenta en el sistema Bnext QMS ha sido activada por el administrador del sistema.
isoblock.configuracion.user.SeHaRegistradoUnNuevoUsuarioEnElSistemaFavorDeActivarloYAsignarUnProcesoEnElQuePArticipa= Se ha registrado un nuevo usuario en el sistema favor de activarlo y asignar un proceso en el que participa.
isoblock.configuracion.user.ClaveDeAcceso= <br><b>Clave de Acceso: </b>
isoblock.configuracion.user.PERMISOSDEROLENMODULOS= <br><b>PERMISOS DE ROL EN MODULOS</b><br>
isoblock.configuracion.user.-RolAccionesNoAcepto=- Rol Acciones: NO ACCESO
isoblock.configuracion.user.-RolAccionesLector=- Rol Acciones: LECTOR
isoblock.configuracion.user.-RolAccionesEditor=- Rol Acciones: EDITOR
isoblock.configuracion.user.-RolAccionesEncargado=- Rol Acciones: ENCARGADO
isoblock.configuracion.user.-RolAuditoriasNoAcceso=- Rol Auditor&iacute;as: NO ACCESO
isoblock.configuracion.user.-RolAuditoriasLector=- Rol Auditor&iacute;as: LECTOR
isoblock.configuracion.user.-RolAuditoriasEditor=- Rol Auditor&iacute;as: EDITOR
isoblock.configuracion.user.-RolAuditoriasEncargado=- Rol Auditor\u00edas: ENCARGADO
isoblock.configuracion.user.-RolDocumentosNoAcceso=- Rol Documentos: NO ACCESO
isoblock.configuracion.user.-RolDocumentosLector=- Rol Documentos: LECTOR
isoblock.configuracion.user.-RolDocumentosEditor=- Rol Documentos: EDITOR
isoblock.configuracion.user.-RolDocumentosEncargado=- Rol Documentos: ENCARGADO
isoblock.configuracion.user.-RolEncuestasNoAcceso=- Rol Encuestas: NO ACCESO
isoblock.configuracion.user.-RolEncuestasNoLector=- Rol Encuestas: LECTOR
isoblock.configuracion.user.-RolEncuestasEditor=- Rol Encuestas: EDITOR
isoblock.configuracion.user.-RolEncuestasNoLector=- Rol Encuestas: ENCARGADO
isoblock.configuracion.user.-RolIndicadoresNoAcceso=- Rol Indicadores: NO ACCESO
isoblock.configuracion.user.-RolIndicadoresLector=- Rol Indicadores: LECTOR
isoblock.configuracion.user.-RolIndicadoresEditor=- Rol Indicadores: EDITOR
isoblock.configuracion.user.-RolIndicadoresEncargado=- Rol Indicadores: ENCARGADO
isoblock.configuracion.user.-RolProyectosNoAcceso=- Rol Proyectos: NO ACCESO
isoblock.configuracion.user.-RolProyectosLector=- Rol Proyectos: LECTOR
isoblock.configuracion.user.-RolProyectosContabilidad=- Rol Proyectos: CONTABILIDAD
isoblock.configuracion.user.-RolProyectosEditor=- Rol Proyectos: EDITOR
isoblock.configuracion.user.-RolProyectosEncargado=- Rol Proyectos: ENCARGADO
isoblock.configuracion.user.-RolQuejasNoAcceso=- Rol Quejas: NO ACCESO
isoblock.configuracion.user.-RolQuejas-=- Rol Quejas: -
isoblock.configuracion.user.-RolQuejasLector=- Rol Quejas: LECTOR
isoblock.configuracion.user.-RolQuejasEditor=- Rol Quejas: EDITOR
isoblock.configuracion.user.-RolQuejasEncargado=- Rol Quejas: ENCARGADO
isoblock.configuracion.user.PERMISOSDEACCESOAREPORTES=<br><b>PERMISOS DE ACCESO A REPORTES</b><br>
isoblock.configuracion.user.-NOTIENEACCESOAREPORTES=- NO TIENE ACCESO A REPORTES<br>
isoblock.configuracion.user.-ConfiguracionNoAcceso=- Configuracion: NO ACCESO
isoblock.configuracion.user.-ConfiguracionAcceso=- Configuracion: ACCESO
isoblock.configuracion.user.-AuditoriasNoAcceso=- Auditor&iacute;as: NO ACCESO
isoblock.configuracion.user.-AuditoriasAcceso=- Auditor&iacute;as: ACCESO
isoblock.configuracion.user.-CuestionariosNoAcceso=- Cuestionarios: NO ACCESO
isoblock.configuracion.user.-CuestionariosAcceso=- Cuestionarios: ACCESO
isoblock.configuracion.user.-AccionesNoAcceso=- Acciones: NO ACCESO
isoblock.configuracion.user.-AccionesAcceso=- Acciones: ACCESO
isoblock.configuracion.user.-AuditoriasNoAcceso=- Auditor&iacute;as: NO ACCESO
isoblock.configuracion.user.-AuditoriasAcceso=- Auditor&iacute;as: ACCESO
isoblock.configuracion.user.-DocumentosNoAcceso=- Documentos: NO ACCESO
isoblock.configuracion.user.-DocumentosAcceso=- Documentos: ACCESO
isoblock.configuracion.user.-ProyectosNoAcceso=- Proyectos: NO ACCESO
isoblock.configuracion.user.-ProyectosAcceso=- Proyectos: ACCESO
isoblock.configuracion.user.-GerencialNoAcceso=- Gerencial: NO ACCESO
isoblock.configuracion.user.-GerencialAcceso=- Gerencial: ACCESO
isoblock.configuracion.user.-IndicadoresNoAcceso=- Indicadores: NO ACCESO
isoblock.configuracion.user.-IndicadoresAcceso=- Indicadores: ACCESO
isoblock.configuracion.user.-QuejasNoAcceso=- Quejas: NO ACCESO
isoblock.configuracion.user.-QuejasAcceso=- Quejas: ACCESO
isoblock.configuracion.user.-EncuestasNoAcceso=- Encuestas: NO ACCESO
isoblock.configuracion.user.-EncuestasAcceso=- Encuestas: ACCESO
isoblock.configuracion.user.comboEstados.--TODOS--=-- TODOS --
isoblock.configuracion.user.ACTIVO= ACTIVO
isoblock.configuracion.user.INACTIVO= INACTIVO
isoblock.configuracion.user.PORREGISTRAR= POR REGISTRAR
#Tags extra (GraficaIndicador) Indicadores
isoblock.indicadores.Indicadoresporobjetivo=Indicadores por objetivo
#Tags extras (isoblock/accion/accionGenerica.java)
isoblock.accion.accionGenerica.Sehadadodealtanunanuevaaccionenelsistema=Se ha dado de alta una nueva acci&oacute;n en el sistema.
isoblock.accion.accionGenerica.Secerrolaaccion=Se cerr&oacute; la acci&oacute;n.
#Tags extras (isoblock/auditoria/auditoria.java)
isoblock.auditoria.auditoria.Secancelolaautitoria,razon=Se cancel&oacute; la auditor&iacute;a Razon:
isoblock.auditoria.auditoria.Laauditoriaestaenpendienteporaceptar=La auditor&iacute;a est&aacute; en Pendiente por aceptar.
isoblock.auditoria.auditoria.Ustedeselencargadodelprocesoenestaauditoria,tieneunnuevopendientedePendientedeAuditoriasporAceptarResultados,favordeatenderlo=Usted es el encargado de proceso en esta auditor&iacute;a, tiene un nuevo pendiente de 'Reportes de Auditor&iacute;as por Aceptar Resultados', favor de atenderlo.
isoblock.auditoria.auditoria.Sehacreadounanuevaauditoriaenlacualustedeselencargadodeldepartamento,favordeatenderla=Se ha creado una nueva auditor&iacute;a en la cual usted es el encargado del departamento, favor de atenderla.
isoblock.auditoria.auditoria.UstedeselencargadodelProcesosujetoalasiguienteauditoria,TieneunnuevopendienteenAuditoriasporConfirmar,favordeatenderlo=Usted es el Encargado del Proceso sujeto a la siguiente auditor&iacute;a. Tiene un nuevo pendiente en 'Auditor&iacute;as por confirmar', favor de atenderlo.
isoblock.auditoria.auditoria.Sehacreadounanuevaauditoriaenlacualustededeselauditorencargado,ustedpuedemodificarlosdatosdelaauditoriamientrasestanohayasidoMODIFICADA,unavezCONFIRMADAporelencargadodelproceso,estanopodrasermodificada=Se ha creado una nueva auditor&iacute;a en la cual usted es el auditor encargado, usted puede modificar los datos de la auditor&iacute;a mientras esta no haya sido CONFIRMADA, una vez CONFIRMADA por el encargado del proceso, &eacute;sta no podr&aacute; ser modificada.
isoblock.auditoria.auditoria.Confirmada=Confirmada
isoblock.auditoria.auditoria.Aceptada=Aceptada
isoblock.auditoria.auditoria.Cancelada=Cancelada
isoblock.auditoria.auditoria.comboEstado.Planeada=Planeada
isoblock.auditoria.auditoria.comboEstado.Confirmada=Confirmada
isoblock.auditoria.auditoria.comboEstado.Aceptada=Aceptada
isoblock.auditoria.auditoria.comboEstado.Cancelada=Cancelada
isoblock.auditoria.auditoria.comboEstado.Eliminada=Eliminada
isoblock.auditoria.auditoria.comboEstado.PorAsignar=Por Asignar
isoblock.auditoria.auditoria.COMENTARIODECANCELACION=COMENTARIO DE CANCELACI&Oacute;N
isoblock.auditoria.auditoria.Seagregouncomentarioalaauditoria,favorderevisarlo=Se agrego un comentario a la auditor&iacute;a, favor de revisarlo.
#Tags extras (isoblock/common/Database)
isoblock.common.Database.MoverTodos=Mover Todos
isoblock.common.Database.Mover=Mover
isoblock.common.Database.Eliminar=Eliminar
isoblock.common.Database.EliminarTodos=Eliminar Todos
isoblock.common.Database.--DEPARTAMENTO--=-- DEPARTAMENTO --
isoblock.common.Database.--SELECCIONE--=-- SELECCIONE --
isoblock.common.Database.TipoDeObjeto=Tipo de objeto
isoblock.common.Database.hoy=hoy
isoblock.common.Database.mes=mes
isoblock.common.Database.dia=d&iacute;a
isoblock.common.Database.a\u00f1o=a\u00f1o
isoblock.common.Database.VerDetalle=Ver Detalle
isoblock.common.Database.VerPregutas=Ver Preguntas
isoblock.common.Database.Borrar=Borrar
isoblock.common.Database.Noseencontraronregistros=No se encontraron registros
isoblock.common.Database.Elregistroseraremovidocompletamentedelsistema\u00bfDeseacontinuar?=El registro ser\\u00e1 removido completamente del sistema. \\u00BFDesea continuar?
isoblock.common.Database.-UsuarioInactivo=- Usuario inactivo:
isoblock.common.Database.-Sinasignar-=- Sin asignar -
isoblock.common.Database.Respaldogeneradoalas=Respaldo generado a las
isoblock.common.Database.enlafecha=en la fecha
#Tags extras (isoblock/common/threadFunction.java)
isoblock.common.threadFunction.eselresponsablededepartamentodondeseasignounaqueja,favordeasignarunresponsable=Es el responsable de departamento donde se asigno una queja, favor de asignar un responsable.
isoblock.common.threadFunction.hasidoasignadocomoResponsabledeunaqueja,favordeatenderla=Ha sido asignado como Responsable de una queja, favor de atenderla.
isoblock.common.threadFunction.deberesvisarlarespuestadadaporelresponsable=debe revisar la respuesta dada por el responsable.
isoblock.common.threadFunction.endondedebeevaluarsuefectividad=En donde debe evaluar su efectividad
isoblock.common.threadFunction.Ustedtiene=Usted tiene 
isoblock.common.threadFunction.pendiente(s)endonde= \ pendiente(s) en donde 
#Tags extras (isoblock/configuracion/catalogo.java)
isoblock.configuracion.catalogo.comvoEstados.Activa=Activa
isoblock.configuracion.catalogo.comvoEstados.Inactiva=Inactiva
isoblock.configuracion.catalogo.comvoEstados.CALIFICACIONES=CALIFICACIONES
isoblock.configuracion.catalogo.comvoEstados.FUENTESDEACCIONES=FUENTES DE ACCIONES
#Tags extras (isoblock/configuracion/masive.java)
isoblock.configuracion.masive.ErrorFila=Error: Fila:
isoblock.configuracion.masive.Laclave=. La clave:
isoblock.configuracion.masive.seencuentrarepetidaenelarchivo=se encuentra repetida en el archivo.
isoblock.configuracion.masive.nombrecompletodeusuario=nombre completo de usuario
isoblock.configuracion.masive.nombredeusuario=nombre de usuario
isoblock.configuracion.masive.Elnombreusuarionodebecontenerespacios=. El nombre usuario no debe contener espacios:
isoblock.configuracion.masive.Elnombreusuario=. El nombre usuario:
isoblock.configuracion.masive.yaestaregistradodentrodelsistemaconunaclavedeusuariodiferente=ya esta registrado dentro del sistema con una clave de usuario diferente.
isoblock.configuracion.masive.AlertaFila=Alerta: Fila:
isoblock.configuracion.masive.esdiferentealqueestaregistradoenelsistema=es diferente al que esta registrado en el sistema.
isoblock.configuracion.masive.Fila=Fila:
isoblock.configuracion.masive.seencuentrarepetidodentrodelarchivo=se encuentra repetido dentro del archivo.
isoblock.configuracion.masive.Lacontrasenanodebedetenerespacios=. La contrase\u00f1a no debe contener espacios:
isoblock.configuracion.masive.Elformatodelcorreoesinvalido=. El formato del correo es invalido:
isoblock.configuracion.masive.ElIDdelperfilnoexiste=. El ID del perfil no existe:
isoblock.configuracion.masive.ElIDdelprocesonoexiste=. El ID del proceso no existe:
isoblock.configuracion.masive.recuerdequelosIDdebenirseparadosporcomas=, recuerde que los ID deben ir separados por comas.
isoblock.configuracion.masive.Ocurriounerroralcargarelperfil=. Ocurrio un error al cargar el perfil:
isoblock.configuracion.masive.ningundatodelusuario=, ningun dato de el usuario
isoblock.configuracion.masive.fuecargado=fue cargado.
isoblock.configuracion.masive.Elcampo=. El campo
isoblock.configuracion.masive.contienecaracteresinvalidos=contiene caracteres invalidos
isoblock.configuracion.masive.sequitaronloscaracteres=se quitaron los caracteres.
#Tags extras (isoblock/configuracion/question.java)
isoblock.configuracion.question.-BORRADOR= - BORRADOR
#Tags extras (isoblock/configuracion/user.java)
isoblock.configuracion.user.Usuario=Usuario :
isoblock.configuracion.user.Clave=Clave :
#Tags extras (isoblock/configuracion/userprofile.java)
isoblock.configuracion.userprofile.comboEstado.Activa=Activa
isoblock.configuracion.userprofile.comboEstado.Inactiva=Inactiva
#Tags extras (isoblock/evaluaciones/Report.java)
isoblock.evaluaciones.Report.comboTiposGrafica.ReportedeEstados=Reporte de Estados
isoblock.evaluaciones.Report.comboTiposGrafica.Resultadosporencuesta=Resultados por encuesta
isoblock.evaluaciones.Report.comboTiposGrafica.Cantidadderespuestas=Cantidad de respuestas
isoblock.evaluaciones.Report.comboGraficarPor.Opciones=Opciones
isoblock.evaluaciones.Report.comboGraficarPor.Preguntas=Preguntas
isoblock.evaluaciones.Report.comboGraficarPor.Secciones=Secciones
isoblock.evaluaciones.Report.comboTipoGraficacionPonderacion.Ponderacion=Ponderaci&oacute;n
isoblock.evaluaciones.Report.crearGrafica.Encuestasenproceso=Encuestas en proceso.
isoblock.evaluaciones.Report.crearGrafica.Estado=Estado
isoblock.evaluaciones.Report.crearGrafica.Cantidad=Cantidad
isoblock.evaluaciones.Report.crearGrafica.Total=Total:
isoblock.evaluaciones.Report.crearGrafica.Resultadosdelaencuesta=Resultados de la encuesta
isoblock.evaluaciones.Report.crearGrafica.Graficaporrespuestasaopciones= Gr\u00e1fica por respuestas a opciones
isoblock.evaluaciones.Report.crearGrafica.Graficaporrespuestasapreguntas= Gr\u00e1fica por respuestas a preguntas
isoblock.evaluaciones.Report.crearGrafica.Graficaporrespuestasasecciones= Gr\u00e1fica por respuestas a secciones
isoblock.evaluaciones.Report.crearGrafica.Mostraragregarparticipantes=Mostrar agregar participantes
isoblock.evaluaciones.Report.crearGrafica.Ocultaragregarparticipantes=Ocultar agregar participantes
isoblock.evaluaciones.Report.crearGrafica.Mostrardetallesdereporte=Mostrar detalles de reporte
isoblock.evaluaciones.Report.crearGrafica.Pregunta=Pregunta
isoblock.evaluaciones.Report.crearGrafica.Porcentaje*=Porcentaje*
isoblock.evaluaciones.Report.crearGrafica.Valor*=Valor*
isoblock.evaluaciones.Report.crearGrafica.Resultadoenpregunta=Resultado en pregunta:
isoblock.evaluaciones.Report.crearGrafica.Resultadoenseccion=Resultado en secci&oacute;n:
isoblock.evaluaciones.Report.crearGrafica.Cantidaddepreguntasnograficadas=Cantidad de preguntas no graficadas:
isoblock.evaluaciones.Report.crearGrafica.Calificacionpromediogeneral=Calificaci&aacute;n promedio general:
isoblock.evaluaciones.Report.crearGrafica.Calificacionpromediogeneral=Calificacion promedio general:
isoblock.evaluaciones.Report.crearGrafica.Noexisteinformacionparagraficar=No existe informaci\u00f3n para graficar.
isoblock.evaluaciones.Report.crearGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio=Imposible realizar esta gr&aacute;fica para indicador agrupado por promedio.
#Tags extras (isoblock/evaluaciones/encuesta.java)
isoblock.evaluaciones.encuesta.enviarCorreo.Hayunaencuestaenlacualhasidoasignadocomoparticipantefavordeatenderla=Hay una encuesta en la cual ha sido asignado como participante, favor de atenderla.
isoblock.evaluaciones.encuesta.enviarCorreo.Secancelolaencuesta,Razon=Se cancelo la encuesta, Razon:
isoblock.evaluaciones.encuesta.enviarCorreo.Laencuestahasidocerrada=La encuesta ha sido cerrada.
isoblock.evaluaciones.encuesta.updateEncuesta.Seactualizalaencuesta=Se actualiz&oacute; la encuesta
isoblock.evaluaciones.encuesta.Seactualizaronlosparticipantesyhoradeterminodelaencuesta=Se actualizar\u00f3n los participantes y hora de termino de la encuesta.
isoblock.evaluaciones.encuesta.Participantesnuevos=Participantes nuevos:
isoblock.evaluaciones.encuesta.Programada=Programada
isoblock.evaluaciones.encuesta.Activa=Activa
isoblock.evaluaciones.encuesta.Terminada=Terminada
isoblock.evaluaciones.encuesta.Cancelada=Cancelada
isoblock.evaluaciones.encuesta.PorResponder=Por Responder
isoblock.evaluaciones.encuesta.Respondida=Respondida
isoblock.evaluaciones.encuesta.Contestada=Contestada
isoblock.evaluaciones.encuesta.Inactiva=Inactiva
isoblock.evaluaciones.encuesta.Porcerrar=Por cerrar
isoblock.evaluaciones.encuesta.Cancelada=Cancelada
isoblock.evaluaciones.encuesta.Pordefinir=Por definir
isoblock.evaluaciones.encuesta.Elusuario=El usuario
isoblock.evaluaciones.encuesta.hacerradolaencuestamanualmente=ha cerrado la encuesta manualmente.
isoblock.evaluaciones.encuesta.Seagregouncomentarioalaencuesta=Se agrego un comentario a la encuesta.
#Tags extras (isoblock/evaluaciones/evaluacion.java)
isoblock.evaluaciones.evaluacion.comboEstados.Activa=Activa
isoblock.evaluaciones.evaluacion.comboEstados.Inactiva=Inactiva
isoblock.evaluaciones.evaluacion.getStrEstado.Activa=Activa
isoblock.evaluaciones.evaluacion.getStrEstado.Estaencuestahasidorespondida=Esta encuesta ha sido respondida
isoblock.evaluaciones.evaluacion.getStrEstado.Inactiva=Inactiva
isoblock.evaluaciones.evaluacion.getStrEstado.PorAsignar=Por Asignar
isoblock.evaluaciones.evaluacion.evaluationBuilder.Examen=Examen:
isoblock.evaluaciones.evaluacion.evaluationBuilder.ErrorNosepudoidentificareltipodepregunta=Error: No se pudo identificar el tipo de pregunta.
isoblock.evaluaciones.evaluacion.evaluationBuilder.Hasolvidadocontestarlapregunta(s)=Has olvidado contestar la pregunta(s)
isoblock.evaluaciones.evaluacion.writeCalendar.Fecha=Fecha
#Tags extras (isoblock/indicadores/GraficaIndicador.java)
isoblock.indicadores.GraficaIndicador.Indicadores=Indicadores
isoblock.indicadores.GraficaIndicador.getGrafica.Objetivo=Objetivo:
isoblock.indicadores.GraficaIndicador.getGrafica.Indicadores=Indicadores:
isoblock.indicadores.GraficaIndicador.getGrafica.Total=Total:
isoblock.indicadores.GraficaIndicador.getGrafica.Evoluciondelindicador=Evoluci&oacute;n del indicador
isoblock.indicadores.GraficaIndicador.getGrafica.Fecha=Fecha
isoblock.indicadores.GraficaIndicador.getGrafica.Meta=Meta
isoblock.indicadores.GraficaIndicador.getGrafica.Resultado=Resultado
isoblock.indicadores.GraficaIndicador.getGrafica.Detalledeindicadores=Detalle de indicadores
isoblock.indicadores.GraficaIndicador.getGrafica.Detalledeindicador=Detalle de indicador
isoblock.indicadores.GraficaIndicador.getGrafica.Unidad=&Aacute;rea:
isoblock.indicadores.GraficaIndicador.getGrafica.Usuario=Usuario:
isoblock.indicadores.GraficaIndicador.getGrafica.Facultad=Unidad productiva:
isoblock.indicadores.GraficaIndicador.getGrafica.Total=Total:
isoblock.indicadores.GraficaIndicador.getGrafica.Meta=Meta:
isoblock.indicadores.GraficaIndicador.getGrafica.Acumulado=Acumulado:
isoblock.indicadores.GraficaIndicador.getGrafica.Contribucionalameta=Contribuci&oacute;n a la meta
isoblock.indicadores.GraficaIndicador.getGrafica.Responsable=Responsable:
isoblock.indicadores.GraficaIndicador.getGrafica.Resultado=Resultado:
isoblock.indicadores.GraficaIndicador.getGrafica.Total=Total
isoblock.indicadores.GraficaIndicador.getGrafica.Noexisteinformacionparagraficar=No existe informaci\u00f3n para graficar.
isoblock.indicadores.GraficaIndicador.getGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio=Imposible realizar esta gr&aacute;fica para indicador agrupado por promedio.
isoblock.indicadores.GraficaIndicador.getGrafica.Fechaderevision=Fecha de revisi\u00f3n
isoblock.indicadores.GraficaIndicador.comboTiposGrafica.Curva=Curva
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Dia=D&iacute;a
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Mes=Mes
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Bimestral=Bimestral
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Tetra=Tetra
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.Semestral=Semestral
isoblock.indicadores.GraficaIndicador.comboPeriodoGrafica.A\u00f1o=A\u00f1o
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Facultad=Unidad productiva
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Departamento=Departamento
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Proceso=Proceso
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Unidad=&Aacute;rea
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Objetivo=Objetivo
isoblock.indicadores.GraficaIndicador.comboGraficarPor.FechadeRevision=Fecha de Revisi&oacute;n
isoblock.indicadores.GraficaIndicador.comboGraficarPor.Usuario=Usuario
#Tags extras (isoblock/indicadores/Indicador.java)
isoblock.indicadores.Indicador.programarLevantamientoIndicador.UnlevantamientodeindicadorenelcualesresponsableseencuentraenestadodeesperaPORCALIFICAR.Favordeatenderlo=Un levantamiento de indicador en el cu&aacute;l es responsable se encuentra en estado de espera POR CALIFICAR. Favor de atenderlo.
isoblock.indicadores.Indicador.programarRevisionIndicador.Sehaprogramadolarevisi&oacute;ndeunindicadordelacualustedeselresponsable,debeesperarhastaqueelindicadorseacalificadoyseencuentreensufechaderevisi&oacute;n.Favordeatenderlo=Se ha programado la revisi&oacute;n de un indicador de la cual usted es el responsable, debe esperar hasta que el indicador sea calificado y se encuentre en su fecha de revisi&oacute;n. Favor de atenderlo.
isoblock.indicadores.Indicador.verificarEstatus.UnlevantamientoenelcualesresponsanleestaenestadodeesperaPORCALIFICAR.Favordeatenderlo=Un levantamiento de indicador en el cu&aacute;l es responsable esta en estado de espera POR CALIFICAR. Favor de atenderlo.
isoblock.indicadores.Indicador.verificarEstatus.UnarevisiondeindicadorenlacualesresponsableestaenesperaPORREVISAR.Favordeatenderlo=Una revisi&oacute;n de indicador en la cu&aacute;l es responsable esta en espera POR REVISAR. Favor de atenderlo.
isoblock.indicadores.Indicador.comboReportarPor.Responsables=Responsables
isoblock.indicadores.Indicador.comboReportarPor.Areas=&Aacute;reas
#Tags extras (isoblock/common/gridGenerator.java)
isoblock.common.gridGenerator.getTitles.Estado=Estado*
isoblock.common.gridGenerator.getTitles.Ver=Ver*
isoblock.common.gridGenerator.getTitles.Cuestionario=Cuestionario*
isoblock.common.gridGenerator.getTitles.Borrar=Borrar*
isoblock.common.gridGenerator.getResults.VerDetalle=Ver Detalle
isoblock.common.gridGenerator.getResults.VerPreguntas=Ver Preguntas
isoblock.common.gridGenerator.getResults.Borrar=Borrar*
isoblock.common.gridGenerator.getResults.Noseencontraronregistros=No se encontraron registros
isoblock.common.gridGenerator.getResults.GuardarCambios=Guardar Cambios
isoblock.common.gridGenerator.getNavegador.Iralaprimerapagina=Ir a la primera p&aacute;gina.
isoblock.common.gridGenerator.Seencuentraenlaultimahoja=Se encuentra en la ultima hoja.
isoblock.common.gridGenerator.Seencuentraenlaprimerahoja=Se encuentra en la primera hoja.
isoblock.common.gridGenerator.getNavegador.Iralapaginaanteriror=Ir a la p&aacute;gina anteriror.
isoblock.common.gridGenerator.getNavegador.Iralapaginasiguiente=Ir a la p&aacute;gina siguiente.
isoblock.common.gridGenerator.getNavegador.Iralaultimapagina=Ir a la ultima p&aacute;gina.
#Tags extras (isoblock/common/gridSearch.java)
isoblock.common.gridSearch.addSearchField.--SELECCIONE--=-- SELECCIONE --
isoblock.common.gridSearch.addSearchField.-eltiposeleccionadonofuncionautilizandoestemetodo-=- el tipo seleccionado no funciona utilizando este metodo -
isoblock.common.gridSearch.getSearchFields.Buscar=Buscar
#Tags extras (isoblock/login/login.java)
isoblock.login.login,recoverUser.HasolicitadounarecuperaciondeunnombredeusuarioparaelsistemaBnextDPMS,siustednorealizoestasolicitudhagacasoomisodeestecorreo,lasiguienteesunalistadeusuariosrelacionadosaestadirecciondecorreoelectronico=Ha solicitado una recuperaci&oacute;n de nombre de usuario para el sistema Bnext QMS, si usted no realiz&oacute; esta solicitud haga caso omiso de este correo, la siguiente es una lista de los usuarios relacionados a esta direcci&oacute;n de correo electronico.
isoblock.login.login.recoverPass.HasolicitadounarecuperaciondeContrasenaparaelsistemaBnextDPMSparaelnombredeusuario=Ha solicitado una recuperaci&aacute;n de Contrase\u00f1a para el sistema Bnext QMS para el nombre de usuario
isoblock.login.login.recoverPass.siustednorealizoestasolicitudhagacasoomisodeestecorreo=, si usted no realiz&oacute; esta solicitud haga caso omiso de este correo.
isoblock.login.login.recoverPass.Contrasena=Contrase\u00f1a:
#Tags extras (isoblock/quejas/queja.java)
isoblock.quejas.queja.insertQueja.Sehareportadounaqueja,favordeatenderlaAutordelaqueja=Se ha reportado una queja, favor de atenderla.<br><b>Autor de la queja: </b>
isoblock.quejas.queja.nextStateControl.SehaasignadounResponsable,ahoralequejaseencuentraenproceso=<br>Se ha asignado un Responsable, ahora la queja se encuentra en proceso.
isoblock.quejas.queja.nextStateControl.UstedhasidoasignadocomoResponsabledeunaqueja,favordeatenderla=<br>Usted ha sido asignado como Responsable de una queja, favor de atenderla.
isoblock.quejas.queja.nextStateControl.Estaquejanohasidoatendidadebidoalasiguienterazon=<br>Esta queja no ha sido atendida debido a la siguiente raz\u00f3n:<br><b>
isoblock.quejas.queja.nextStateControl.Sehadadorespuestaaestaqueja,larespuestafuelasiguiente,favordeatenderla=<br>Se ha dado respuesta a esta queja, la respuesta fue la siguiente, favor de atenderla: <br><b>
isoblock.quejas.queja.nextStateControl.Estaquejahasidoatendidayestaenesperaporevaluar,favordeatenderla=<br>Esta queja ha sido atendida y est&aacute; en espera por evaluar, favor de atenderla.<br><br>
isoblock.quejas.queja.nextStateControl.Larespuestaaestaquejanohasidoaceptada,favordevolverallenarla.Razon=<br>La respuesta a esta queja no ha sido aceptada, favor de volver a atenderla. Raz&oacute;n: <br><b>
isoblock.quejas.queja.nextStateControl.LARESPUESTAAESTAQUEJANOHASIDOACEPTADA,FAVORDEVOLVERALLENARLA=LA RESPUESTA A ESTA QUEJA NO HA SIDO ACEPTADA, FAVOR DE ATENDERLA: <br><br>
isoblock.quejas.queja.nextStateControl.Estaquejahasidore-asignadade=Esta queja ha sido re-asignada de <u>
isoblock.quejas.queja.nextStateControl.austed=</u> a usted <u>
isoblock.quejas.queja.nextStateControl.favordeatenderla.Razon=</u>, favor de atenderla.<br> Raz&oacute;n: <br><b>
isoblock.quejas.queja.nextStateControl.Laquejaqueatendistefuere-asignadaa=La queja que atendiste fue re-asignada a <u>
isoblock.quejas.queja.nextStateControl.Razon=</u><br>.<br> Raz&oacute;n: <br><b>
isoblock.quejas.queja.nextStateControl.Laquejahasidoevaluadayfinalizada,lacalificacionfue=<br>La queja ha sido evaluada y finalizada, la calificacion fue: <b>
isoblock.quejas.queja.getStrCalificacion.MuySatisfecho=Muy Satisfecho
isoblock.quejas.queja.getStrCalificacion.Satisfecho=Satisfecho
isoblock.quejas.queja.getStrCalificacion.PocoSatisfecho=Poco Satisfecho
isoblock.quejas.queja.getStrCalificacion.NadaSatisfecho=Nada Satisfecho
isoblock.quejas.queja.getStringEstadoControl.ElestadoactualesASIGNADAenesperapordarrespuesta=El estado actual es ASIGNADA en espera por dar respuesta.
isoblock.quejas.queja.getStringEstadoControl.ElestadoactualesATENDIDAenesperaporaceptarrespuesta=El estado actual es ATENDIDA en espera por aceptar respuesta.
isoblock.quejas.queja.getStringEstadoControl.ElestadoactualesRESPONDIDA,pendienteporaceptarresultados=El estado actual es RESPONDIDA, pendiente por aceptar resultados.
isoblock.quejas.queja.getStringEstadoControl.Estaquejahasidoatendidayfinalizada=Esta queja ha sido atendida y finalizada.
isoblock.quejas.queja.getStringEstadoControl.EstaquejahasidoatendidayfuemarcadacomoNOPROCEDE=Esta queja ha sido atendida y fue marcada como NO PROCEDE.
isoblock.quejas.queja.getStringEstadoControlSimple.Reportada=Reportada
isoblock.quejas.queja.getStringEstadoControlSimple.Asignada=Asignada
isoblock.quejas.queja.getStringEstadoControlSimple.Atendida(enproceso)=Atendida (en proceso)
isoblock.quejas.queja.getStringEstadoControlSimple.Poraceptarresultados=Por aceptar resultados
isoblock.quejas.queja.getStringEstadoControlSimple.Atendida(finalizada)=Atendida (finalizada)
isoblock.quejas.queja.getStringEstadoControlSimple.Cerrada(noprocede)=Cerrada (no procede)
isoblock.quejas.queja.getStringEstadoAlumno.Reportada=Reportada.
isoblock.quejas.queja.getStringEstadoAlumno.Enproceso=En proceso.
isoblock.quejas.queja.getStringEstadoAlumno.Atendidaporevaluarresultados=Atendida por evaluar resultados.
isoblock.quejas.queja.getStringEstadoAlumno.Finalizada=Finalizada.
isoblock.quejas.queja.getStringEstadoAlumno.PorAsignar=Por Asignar
#Tags extras (isoblock/quejas/reporteQuejas.java)
isoblock.quejas.reporteQueja.comboTiposGrafica.Quejasenproceso=Quejas en proceso
isoblock.quejas.reporteQueja.comboTiposGrafica.Detalledequejas=Detalle de quejas
isoblock.quejas.reporteQueja.comboGraficarPor.Estado=Estado
isoblock.quejas.reporteQueja.comboGraficarPor.QuejasporDepartamento=Quejas por Departamento
isoblock.quejas.reporteQueja.comboGraficarPor.Usuario=Usuario
isoblock.quejas.reporteQueja.comboGraficarPor.Fuente=Fuente
isoblock.quejas.reporteQueja.crearGrafica.Quejasenproceso=Quejas en proceso.
isoblock.quejas.reporteQueja.crearGrafica.Estado=Estado
isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas=Cantidad de quejas
isoblock.quejas.reporteQueja.crearGrafica.Porcentaje=Porcentaje
isoblock.quejas.reporteQueja.crearGrafica.Totaldequejas=Total de quejas:
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasporusuario=Quejas levantadas por usuario
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasaporestados=Quejas levantadas por estados
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasadepartamentos=Quejas levantadas por departamentos
isoblock.quejas.reporteQueja.crearGrafica.Quejaslevantadasafuentes=Quejas levantadas por fuentes
isoblock.quejas.reporteQueja.crearGrafica.Quejasporclasificacion=Quejas por clasificacion
isoblock.quejas.reporteQueja.crearGrafica.Campo=Estatus
isoblock.quejas.reporteQueja.crearGrafica.Responsable(s)=Responsable(s)
isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas=Cantidad de quejas
isoblock.quejas.reporteQueja.crearGrafica.Porcentaje=Porcentaje
isoblock.quejas.reporteQueja.crearGrafica.#Dias=# Dias
isoblock.quejas.reporteQueja.crearGrafica.DiasPromediodeatencion=Dias Promedio de atencion
isoblock.quejas.reporteQueja.crearGrafica.Totaldequejas=Total de quejas:
isoblock.quejas.reporteQueja.crearGrafica.Noexisteinformacionparagraficar=No existe informaci\u00f3n para graficar.
isoblock.quejas.reporteQueja.crearGrafica.Imposiblerealizarestagraficaparaindicadoragrupadoporpromedio=Imposible realizar esta gr&aacute;fica para indicador agrupado por promedio.
isoblock.quejas.reporteQueja.crearGrafica.Cantidaddequejas=Cantidad de quejas
#Tags extras (isoblock/indicadores/RevisionIndicadores.java)
isoblock.indicadores.RevisionIndicadores.getTotalesRevision.positivo= positivo
isoblock.indicadores.RevisionIndicadores.getTotalesRevision.negativo= negativo
#Tags extras (isoblock/configuracion/userprfile.java)
isoblock.configuracion.userprofile.Nosepudocambiarelestadodelperfil=No se pudo cambiar el estado del perfil.
isoblock.configuracion.userprofile.Secambioelestadodelperfilcorrectamente=Se cambio el estado del perfil correctamente.
isoblock.configuracion.userprofile.Nosepudoborrarelperfil=No se pudo borrar el perfil.
isoblock.configuracion.userprofile.Seborroelperfilcorrectamente=Se borr&oacute; el perfil correctamente.
#Tags extras (isoblock/common/gridGenerator/gridSearch.java)
isoblock.common.gridGenerator.gridSearch.Limpiarseleccion=Limpiar selecci&oacute;n
isoblock.common.gridGenerator.gridSearch.Seleccionartodo=Seleccionar todo
#Tags extras (isobloc/common/Database.java)
isoblock.common.Database.controlListaObjetos.combo.--TODAS--=-- TODAS --
isoblock.auditoria.auditoria.LaAuditoriaHaSidoRestauradaCorrectamente=La auditor&iacute;a ha sido restaurada correctamente.
isoblock.auditoria.auditoria.NoFuePosibleRestaurarLaAuditoria=No fue posible restaurar la auditor\u00eda.
isoblock.auditoria.auditoria.LaAuditoriaHaSidoEliminadaCorrectamente=La auditor&iacute;a ha sido eliminada correctamente.
isoblock.auditoria.auditoria.NoFuePosibleEliminarLaAuditoria=No fue posible eliminar la  auditor\u00eda.
isoblock.auditoria.auditoria.Laauditoriasehacerradocorrectamente=La auditor&iacute;a se ha cerrado correctamente.
isoblock.auditoria.auditoria.Laauditorianosehapodidocerrar=La  auditor\u00eda no se ha podido cerrar
#Tags Extras (isoblock/configuracion/invitado.java)
isoblock.configuracion.invitado.borrarInvitado.mensaje=Error en la funci\\u00f3n borrarInvitado.
isoblock.configuracion.invitado.ElinvitadoHaSidoEliminadaCorrectamente=El invitado ha sido eliminado correctamente.
isoblock.configuracion.invitado.NoFuePosibleEliminaralinvitado=No fue posible eliminar al invitado.
isoblock.common.gridsearch.busqueda=B&uacute;squeda
isoblock.common.gridsearch.mostrarfiltrodebusqueda=Mostrar el filtro de b&uacute;squeda
isoblock.common.gridsearch.ocultarFiltrodebusqueda=Ocultar el filtro de busqueda
isoblock.configuracion.question.Lapreguntahasidocancelada=La pregunta ha sido cancelada correctamente
isoblock.configuracion.question.Lapreguntahasidoactivadacorrectamente=La pregunta ha sido activada correctamente
isoblock.configuracion.question.lapreguntasehaenviadoalapapelera=La pregunta ha sido enviada a la papelera
isoblock.configuracion.question.Lapreguntaseharestaurado=La Pregunta ha sido restaurada exitosamente
isoblock.configuracion.question.Lapreguntasehaeleiminadodefinitivamente=La pregunta ha sido eliminada
isoblock.configuracion.invitado.NoFuePosibleEliminaralinvitado=No fue posible eliminar al invitado.
isoblock.correo.indicador.variablesCorreo4=Indicador a Carrera:
quejas.quejashandle.Redacciondequeja=Redacci&oacute;n de queja:
quejas.quejashandle.Direcciondecorreoalternativapararecibiravisos=Copia de queja para:
isoblock.configuracion.user.-RolProyectosContabilidad-=- Rol Proyectos: CONTABILIDAD
isoblock.evaluaciones.Report.comboTiposGrafica.Total=Total:
isoblock.indicadores.Indicador.programarRevisionIndicador.Sehaprogramadolarevisiondeunindicadordelacualustedeselresponsable,debeesperarhastaqueelindicadorseacalificadoyseencuentreensufechaderevision.Favordeatenderlo=Se ha programado la revisi&oacute;n de un indicador de la cual usted es el responsable, debe esperar hasta que el indicador sea calificado y se encuentre en su fecha de revisi&oacute;n. Favor de atenderlo.
isoblock.indicadores.RevisionIndicador.revisarIndicador.Elindicadorhasidorevisadoporelusuariodelsistema=El indicador ha sido revisado por el usuario del sistema: 
isoblock.indicadores.RevisionIndicador.revisarIndicador.Comentario=Comentario:
isoblock.indicadores.Indicador.programarRevisionIndicador.Sehaprogramadolarevisi\u00f3ndeunindicadordelacualustedeselresponsable,debeesperarhastaqueelindicadorseacalificadoyseencuentreensufechaderevisi\u00f3n.Favordeatenderlo=Se ha programado la revisi\u00f3n de un indicador de la cual usted es el responsable, debe esperar hasta que el indicador sea calificado y se encuentre en su fecha de revisi\u00f3n. Favor de atenderlo.
isoblock.configuracion.user.-AccionesNoAcceso--=- Acciones: NO ACCESO
block.configuracion.user.-AuditoriasNoAcceso=- Auditor\u00edas: NO ACCESO
#Tags extras (isoblock/common/gridGenerator/gridGenerator.java)
isoblock.common.gridGenerator.gridGenerator.Seencuentraenlaultimahoja=Se encuentra en la &uacute;ltima hoja.
isoblock.common.gridGenerator.gridGenerator.Seencuentraenlaultimahoja=Se encuentra en la primer hoja.
#Tags extras (isoblock/configuracion/seccionlist.java)
isoblock.configuracion.seccion.borrarSeccion.mensaje=Error en la funci&oacute;n borrarSecci&oacute;n.
isoblock.configuracion.seccion.LaseccionHaSidoEliminadaCorrectamente=La secci&oacute;n ha sido eliminada correctamente.
isoblock.configuracion.seccion.NoFuePosibleEliminarlaseccion=No fue posible eliminar la secci&oacute;n.
#Tags extras (isoblock/configuracion/catalogo.java)
isoblock.configuracion.catalogo.Nosepudocambiarelestadodelcatalogo=No se pudo cambiar el estado del cat&aacute;logo.
isoblock.configuracion.catalogo.Secambioelestadodelcatalogocorrectamente=Se cambio el estado del cat&aacute;logo correctamente.
#Tags extras (isoblock/configuracion/catalogo.java)
isoblock.configuracion.catalogo.NoFuePosibleEliminarelcatalogo=No fue posible eliminar el cat&aacute;logo.
isoblock.configuracion.invitado.ElcatalogoHaSidoEliminadaCorrectamente=El cat&aacute;logo ha sido eliminada correctamente.
isoblock.gridgenerator.gridgenerator.porfavorespereaqueelprocesoanteriorterminedecargarse=Porfavor espere a que el proceso anterior termine
gridGenerator.gridGenerator.ajustes=Ajustes
gridGenerator.gridGenerator.cambiar=Cambiar
gridGenerator.gridGenerator.excel2003=Excel 2003
gridGenerator.gridGenerator.excel2007=Excel 2007
gridGenerator.gridGenerator.exportar=Exportar
gridGenerator.gridGenerator.exportarTabla=Exportar tabla
gridGenerator.gridGenerator.numeroderegistrosenpantalla=N\u00famero de registros en pantalla.
isoblock.scorecards.selectorEntidad.Mostrar=Mostrar/Ocultar
menu.folderTreeLeftFrame.Documentos=Documentos
menu.folderTreeLeftFrame.Proyectos=Proyectos
menu.folderTreeLeftFrame.Acciones=Acciones
menu.folderTreeLeftFrame.Solicitudes=Solicitudes
menu.folderTreeLeftFrame.Listadesolicitudes=Lista de solicitudes
menu.folderTreeLeftFrame.ListaMaestra=Lista Maestra
menu.folderTreeLeftFrame.ListaRelaciones=Lista de Relaciones
menu.folderTreeLeftFrame.GaleriaArbol=Galeria
menu.folderTreeLeftFrame.Papelera=Papelera
menu.folderTreeLeftFrame.Alta=Alta
menu.folderTreeLeftFrame.ConfiguracionQuejasControl=Control
menu.folderTreeLeftFrame.ConfiguracionMisQuejas=Mis Quejas
menu.folderTreeLeftFrame.Catalogo=Catalogo
documentos.solicitudeslist.Solicitada=Solicitada
documentos.solicitudeslist.Atendida=Atendida
solicitudes.solicitudeslist.ElestadoactualesRECHAZADA,presioneparamarcarcomoCerrada=Rechazada
solicitudes.solicitudeslist.Enproceso=En proceso
solicitudes.solicitudeslist.ElestadoactualesRECHAZADA=RECHAZADA
documentos.solicitudeslist.Cerrada=CERRADA
isoblock.scorecards.selectorEntidad.Filtros=Filtros
isoblock.scorecards.selectorEntidad.Ocultar=Filtros
isoblock.scorecards.selectorEntidad.Entidad=Entidad:
isoblock.scorecards.selectorEntidad.--SELECCIONE--=-- SELECCIONE -- 
isoblock.scorecards.selectorEntidad.Zona=Unidad productiva
isoblock.scorecards.selectorEntidad.Plaza=Departamento
isoblock.scorecards.selectorEntidad.Proceso=Proceso
isoblock.scorecards.selectorEntidad.Tienda=Area
isoblock.scorecards.selectorEntidad.Usuario=Usuario
isoblock.scorecards.selectorEntidad.Nombre=Nombre
isoblock.scorecards.selectorEntidad.Nombre2=Nombre:
isoblock.scorecards.selectorEntidad.Zona2=Unidad productiva:
isoblock.scorecards.selectorEntidad.Plaza2=Departamento:
isoblock.scorecards.selectorEntidad.Proceso2=Proceso:
isoblock.scorecards.selectorEntidad.Filtrar=Filtrar:
isoblock.scorecards.selectorEntidad.MoverAleatoriamente=Move randomly
isoblock.scorecards.selectorEntidad.entidades=entidades
isoblock.scorecards.selectorEntidad.Usuarios=Usuarios
isoblock.common.gridSearch.getSearchFields.Limpiar=Limpiar
isoblock.documentos.java.Nosehanagregadosdocumentos=No se han agregado documentos
isoblock.documentojava.Nohaylectores=No hay lectores
quejas.quejashandle.Levantaracciones=Levantar Acciones:
isoblock.quejas.queja.Nosecuentaconaccionesrelacionadas=No se cuenta con acciones relacionadas.
isoblock.documentos.documento.Parausarestecombo=Para usar este combo,
isoblock.documentos.documento.seleccioneunacarpeta=seleccione una carpeta.
isoblock.documentos.documento.Noexistendocumentos=No existen documentos
isoblock.documentos.documento.enlacarpeta=en la carpeta
isoblock.documentos.documento.seleccionada=seleccionada.
#Surveys.
isoblock.evaluaciones.Report.Encuestas=Encuestas.
isoblock.quejas.ReporteQuejas.Quejas=Quejas.
isoblock.correo.por.leer.notificacion=Notificaci\u00f3n de la secci\u00f3n de documentos del modulo del sistema de Bnext QMS
isoblock.correo.por.leer.nota=Usted tiene un nuevo pendiente de documento por leer en el sistema, favor de atenderlo.
isoblock.correo.por.leer.variablesCorreo1=Clave de la secuencia:
isoblock.correo.por.leer.variablesCorreo2=T\u00edtulo del documento:
isoblock.correo.por.leer.variablesCorreo3=Clave del documento:
isoblock.correo.por.leer.variablesCorreo4=Autor:
isoblock.correo.por.leer.variablesCorreo5=Raz\u00f3n:
isoblock.correo.por.leer.variablesCorreo6=Comentario:
isoblock.quejas.queja.getStringEstado=Estado
isoblock.quejas.queja.getStringClave=Clave
isoblock.quejas.queja.getStringResponsable=Responsable
isoblock.quejas.queja.getStringReportada=Reportada
isoblock.quejas.queja.getStringVer=Ver
isoblock.accion.accionGenerica.comboEstado.AccionesaTomarporRealizar=Acciones a Tomar por Realizar
isoblock.accion.accionGenerica.comboEstado.AccionesaTomarporVerificar=Acciones a Tomar por Verificar
isoblock.accion.accionGenerica.ClavedeAuditoria=Clave de Auditor\u00eda
isoblock.accion.accionGenerica.ClavedeQuejas=Clave de Queja
isoblock.accion.accionGenerica.ClavedeRevision=Clave de Revisi\u00f3n de Indicador
isoblock.accion.accionGenerica.ClavedeProtocolo=Clave de Protocolo
isoblock.accion.accionGenerica.Seleccione=Seleccione
isoblock.accion.accionGenerica.SinResultados=Sin resultados
isoblock.quejas.AccionesRelacionadas=Acciones Relacionadas
survey.Titulodelcuestionario=T\u00edtulo del cuestionario (clic para editar)
indicadores.indicadorhandle.Responsabledellenado=Responsable de llenado
isoblock.indicadores.Indicador.comboReportarPor.Usuarios=Usuarios
isoblock.documentos.documento.selecioneunacarpeta
isoblock.documentos.documento.selecioneunacarpeta=selecione una carpeta
isoblock.documentos.documento.Lareferenciahasidocreadaconexito=La referencia ha sido creada con \u00e9xito
isoblock.documentos.documento.VerCarpeta=Ver Carpeta
isoblock.documentos.documento.Ocurriounerrornoidentificadoalactualizarelsistema=Ocurri\u00f3 un error no identificado al actualizar el sistema
isoblock.documentojava.Porleer=Por leer
isoblock.documentojava.Leidoesteusuariohaleidoeldocumentosinhabertenidoquehacerlo=Le\u00eddo, este usuario ha le\u00eddo el documento sin haber tenido que hacerlo.
isoblock.documentojava.Leidoesteusuarioatendiosupendienteyleyoeldocumento=Le\u00eddo, este usuario atendi\u00f3 su pendiente y ley\u00f3 el documento
isoblock.documentojava.Estado=Estado
isoblock.documentojava.Lector=Lector
isoblock.documentojava.FechaHora=Fecha/Hora
isoblock.surveys.struts2.forms.create.template=(Plantilla)
isoblock.surveys.struts2.action.Elcuestionarioseencuentrabloqueado=El cuestionario se encuentra bloqueado y no puede ser modificado.
isoblock.surveys.struts2.action.Sehamodificadoelestatusconexito=Se ha modificado el estatus con \u00e9xito.
isoblock.surveys.struts2.action.Elcuestionariofueguardadoconexito=El cuestionario fue guardado con \u00e9xito.
isoblock.surveys.struts2.action.Lacopiadelcuestionariofueguardadaexitosamente=La copia del cuestionario fue guardada exitosamente, ser\u00e1 enviado al listado completo.
isoblock.surveys.struts2.action.copia=\ (copia)
isoblock.quejas.reporteQueja.comboGraficarPor.Departamento=Departamento
mail.request.requestApproved=La solicitud que creaste ha sido aprobada.
isoblock.correo.validations.variablesCorreo4=${Area}:
isoblock.correo.validations.variablesCorreo5=Responsable:
isoblock.correo.validations.variablesCorreo9=N\u00famero de Control de Cambios:
isoblock.correo.validations.variablesCorreo3=Departamento:
isoblock.correo.validations.variablesCorreo6=Descripci\u00f3n:
isoblock.correo.validations.notificacion=Notificaci&oacute;n de una nueva VALIDACI&Oacute;N. <br>
isoblock.correo.validations.variablesCorreo8=Prioridad:
isoblock.correo.validations.variablesCorreo2=N\u00famero de Protocolo:
isoblock.correo.validations.variablesCorreo7=Tipo de Estudio:
isoblock.configuracion.catalogo.Nosepudocambiarelestadodelcatalogoud=No se pudo cambiar el estado del cat\u00e1logo.
isoblock.correo.validations.variablesCorreo1=Tipo de Validaci\u00f3n:
Proyectos.hrs=hrs
isoblock.quejas.queja.insertQueja.AltaQueja,ustedareportadounaqueja=Usted a reportado una queja <br>
isoblock.quejas.queja.insertQueja.QuejaAtendida,laquejaqueustedreportihasidoatendida=La queja que usted report\u00f3 ha sido atendida <br>
isoblock.quejas.queja.insertQueja.CopiaMail,EsteAvisoEsInformativo=Este aviso es informativo. <br>Se ha reportado una queja.
boton.SaveTemplate=Guardar como plantilla
menu.folderTreeLeftFrame.PapeleraIndicator=Papelera
menu.folderTreeLeftFrame.userAgent=User Agent
menu.folderTreeLeftFrame.fileType=Tipo de archivo
menu.folderTreeLeftFrame.PapeleraAudit=Papelera de auditor\u00edas
accion.acciongenericahandle.Aceptacion=Aceptaci&oacute;n
testingEmail=Correo de Prueba..
isoblock.common.correo.asunto=Correo enviado por la aplicaci&oacute;n
isoblock.common.threadFunction.activities_to_be_finish_responsible = Actividades de proyectos por terminar (responsables)
isoblock.common.threadFunction.activities_to_be_finish_managers = Actividades de proyectos por terminar (encargados)
isoblock.common.threadFunction.activities_to_be_verify = Actividades de proyectos por verificar
isoblock.common.threadFunction.activities_to_be_verify_managers = Actividades de proyectos por verificar (encargados)
CLOSED_EFFECTIVE = Cerrada efectiva
CLOSED_NOT_EFFECTIVE = Cerrada no efectiva