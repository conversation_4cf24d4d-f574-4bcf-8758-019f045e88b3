accion.accionatomarhandle.Accionatomar=Action to take:
accion.accionatomarhandle.boton.CrearAccionatomar=Create Action to take
accion.accionatomarhandle.boton.CrearAccionatomar=Create Action:
accion.accionatomarhandle.Clavedelaaccionatomar=Action ID:
accion.accionatomarhandle.Fechadecumplimiento=Acomplishment date:
accion.accionatomarhandle.Fechadecumplimiento=Fulfillment date:
accion.accionatomarhandle.Fechadeevaluacion=Evaluation date:
accion.accionatomarhandle.header.ACCIONESPREVENTIVAS=PREVENTIVE ACTIONS
accion.accionatomarhandle.Historialdeavance=Progress history
accion.accionatomarhandle.javascript.Laaccionatomarhansidoverificada=The action has been verified
accion.accionatomarhandle.javascript.Laaccionatomarhansidoverificada=The action to take has been verified.
accion.accionatomarhandle.javascript.LosDatosdelaaccionatomarhansidoactualizados=The data of the action has been updated.
accion.accionatomarhandle.javscript.Accionatomar=- Action to take
assignComplaintInsufficientPermissions = You do not have access to perform the operation, verify that you have a higher role than reader.
accion.accionatomarhandle.javscript.Descripciondelaaccionactual=- Current action description
accion.accionatomarhandle.javscript.Evaluarresultado=- Evaluate result
accion.accionatomarhandle.javscript.Evaluarresultado=- Evaluate results
accion.accionatomarhandle.javscript.Fechadeimplementacion=- Implementation date
accion.accionatomarhandle.javscript.LaAccionatomarhasidodadadealta=The Action has been created.
accion.accionatomarhandle.javscript.Lossiguientesdatossonnecesarios=Following items are required
accion.accionatomarhandle.javscript.Lossiguientesdatossonnecesarios=The following items are necessary:
accion.accionatomarhandle.Noserecibiolaclaveareferenciar=Didn\'t recieve id to reference
accion.accionatomarhandle.Noserecibiolaclaveareferenciar=Not received the key to reference.
accion.accionatomarhandle.Porcentajedeavance=Progress percentage:
accion.accionatomarhandle.Porcentajedecumplimientodelaaccion=Action acomplishment percentage:
accion.accionatomarhandle.Porcentajedecumplimientodelaaccion=Action fulfillment percentage:
accion.accionatomarhandleamc.Accionatomar=Action to take:
accion.accionatomarhandleamc.Acciondecorreccioninmediata=Immediate Corrective Action:
accion.accionatomarhandleamc.AccionTomarSobreQueja =ACTION TO TAKE ON THE COMPLAINT
accion.accionatomarhandleamc.boton.Actualizar=Update
accion.accionatomarhandleamc.boton.CrearAccionatomar=Create
accion.accionatomarhandleamc.boton.CrearAccionatomar=Create Action to take
accion.accionatomarhandleamc.boton.Limpiar=Clear
accion.accionatomarhandleamc.boton.Regresar=Return
accion.accionatomarhandleamc.Clavedelaaccion=Action ID:
accion.accionatomarhandleamc.ClavedelaQueja= ID Complaint:
accion.accionatomarhandleamc.Detalledelcambio=Modification detail:
accion.accionatomarhandleamc.Fechadeevaluacion=Evaluation date:
accion.accionatomarhandleamc.Fechadeimplementacion=Date of implementation:
accion.accionatomarhandleamc.Fechadeverificacion=Date of verification:
accion.accionatomarhandleamc.header.ACCIONESPREVENTIVAS=PREVENTIVE ACTIONS
accion.accionatomarhandleamc.javascript.Accionatomar=- Action to take
accion.accionatomarhandleamc.javascript.ComentarioDeAvance=Comments of Advances
accion.accionatomarhandleamc.javascript.-ElResponsableNoPuedeSerElVerificador=- The leader must not be the verifier
accion.accionatomarhandleamc.javascript.-ElResponsableNoPuedeSerElVerificador=The Person in Charge can't be the Verifier
accion.accionatomarhandleamc.javascript.ElResponsableNoPuedeSerElVerificador=The Responsible must not be the Verifier
accion.accionatomarhandleamc.javascript.ESTECAMPONOHASIDOMODIFICADO=The field has not been edited.
accion.accionatomarhandleamc.javascript.Evaluarresultado=- Action results.
accion.accionatomarhandleamc.javascript.Fechadeimplementacion=- Implementation date
accion.accionatomarhandleamc.javascript.Fechadeverificacion=- Verification date
accion.accionatomarhandleamc.javascript.Laaccionatomarhansidoactualizada=The action to take has been updated.
accion.accionatomarhandleamc.javascript.Laaccionatomarhansidoactualizada=The action to take has been updated.
accion.accionatomarhandleamc.javascript.Laaccionatomarhasidodadadealta=The action to take has been added.
accion.accionatomarhandleamc.javascript.Laaccionatomarhasidodadadealta=The action to take has been added.
accion.accionatomarhandleamc.javascript.LosDatosdelaaccionatomarhansidoactualizados=The data from action to take have been updated.
accion.accionatomarhandleamc.javascript.Lossiguientesdatossonnecesarios=Following items are required
accion.accionatomarhandleamc.javascript.Lossiguientesdatossonnecesarios=The following items are necessary:
accion.accionatomarhandleamc.javascript.Razondecambiodefechadeimplementacion=- Implementation date change motive
accion.accionatomarhandleamc.javascript.Razondecambiodefechadeverificacion=- Verification date change motive
accion.accionatomarhandleamc.javascript.Razondecambiodeporcentaje=- Percentage change motive
accion.accionatomarhandleamc.javascript.Razondecambiodeverificador=- Verifier change motive
accion.accionatomarhandleamc.javascript.-Responsable=Responsible of executing action:
accion.accionatomarhandleamc.javascript.Responsable=Leader
accion.accionatomarhandleamc.javascript.-Verificador=- Verifier
accion.accionatomarhandleamc.javascript.Verificador=Verifier
accion.accionatomarhandleamc.Noserecibiolaclaveareferenciar=Not received the key to reference.
accion.accionatomarhandleamc.Noserecibiolaclaveareferenciar=There is no Action code.
accion.accionatomarhandleamc.Permitircambiosaresponsable=Permit responsible modify fields
accion.accionatomarhandleamc.Porcentajedeavance=Progress Percent:
accion.accionatomarhandleamc.Porcentajedeavance=Advance of percentage:
accion.accionatomarhandleamc.Porcentajedecumplimientodelaaccion=Action fulfillment percentage:
accion.accionatomarhandleamc.Porcentajedecumplimientodelaaccion=Progress Percent:
accion.accionatomarhandleamc.Razondecambiodefechadeimplementacion=Date changed reason 
accion.accionatomarhandleamc.Razondecambiodefechadeverificacion=Date changed reason
accion.accionatomarhandleamc.Razondecambiodeporcentaje=Advance change reason
accion.accionatomarhandleamc.Razondecambiodeverificador=Verifier change motive
accion.accionatomarhandleamc.Responsable=Leader
accion.accionatomarhandleamc.Responsabledeimplementarlaaccion=Implementation responsible
accion.accionatomarhandleamc.ResponsabledeActividad=Activity responsible
accion.accionatomarhandleamc.Resultadosdelaaccion=Action Results:
accion.accionatomarhandleamc.Verificador=Verifier
accion.accionatomarhandlemc.javascript.ComentarioDeAvance=Comments
accion.accionatomarlist.Accion=Action
accion.accionatomarlist.Asignada=Asigned
accion.accionatomarlist.Asignada=Assigned
accion.accionatomarlist.AsignarlaAccionPreventiva=Asign preventive action
accion.accionatomarlist.AsignarlaAccionPreventiva=Assign Preventive Action
accion.accionatomarlist.Autor=Author
accion.accionatomarlist.Autor=Autor
accion.accionatomarlist.Borrar=Delete
accion.accionatomarlist.Cerrada=Closed
accion.accionatomarlist.cerrarAccion=Close Action
accion.accionatomarlist.cerrarAccion=closeAction
accion.accionatomarlist.Clave=ID
accion.accionatomarlist.Clave=ID:
accion.accionatomarlist.Definida=Defined
accion.accionatomarlist.Definida=Defined
accion.accionatomarlist.DefinirlasAccionesatomar=Define actions to take
accion.accionatomarlist.DefinirlasAccionesatomar=Define actions to take
accion.accionatomarlist.Encargado=In Charge:
accion.accionatomarlist.Encargado=Leader
accion.accionatomarlist.Estado= Status
accion.accionatomarlist.Estado= Status:
accion.accionatomarlist.header.CONTROLDEACCIONESPREVENTIVAS=PREVENTIVE ACTIONS CONTROL
accion.accionatomarlist.header.CONTROLDEACCIONESPREVENTIVAS=PREVENTIVE ACTIONS EDITION
accion.accionatomarlist.Implementada=Implemented
accion.accionatomarlist.Implementada=Implemented
accion.accionatomarlist.implementarAccion=implemen Action
accion.accionatomarlist.implementarAccion=Implement Action
accion.accionatomarlist.javascript.DeseamarcarlaAccioncomocerrada=Do you want to set the action as closed?
accion.accionatomarlist.javascript.DeseamarcarlaAccioncomocerrada=Do you wish to close the action?
accion.accionatomarlist.javascript.DeseamarcarlaAccioncomoimplementada=Do you want to set the action as implemented?
accion.accionatomarlist.javascript.DeseamarcarlaAccioncomoimplementada=Do you wish to implement the action?
accion.accionatomarlist.javascript.Laaccionpreventivaseraborradadelsistema=The preventive action will be DELETED!
accion.accionatomarlist.javascript.Laaccionpreventivaseraborradadelsistema=The preventive action will be removed from the system!
accion.accionatomarlist.MarcarcomocerradalaAccionPreventiva=close Preventive Action
accion.accionatomarlist.MarcarcomocerradalaAccionPreventiva=Set the Preventive Action as closed
accion.accionatomarlist.MarcarcomoimplementadalaAccionPreventiva=Implement Preventive Action
accion.accionatomarlist.MarcarcomoimplementadalaAccionPreventiva=Mark the Preventive Action as implemented
accion.accionatomarlist.NoAsignado=Not Assigned
accion.accionatomarlist.NoAsignado=Unasigned
accion.accionatomarlist.Nopuederealizarestaaccion=Cannot perform this action.
accion.accionatomarlist.Nopuederealizarestaaccion=Can't update the action
accion.accionatomarlist.Noseencontraronacciones=Didn't find actions
accion.accionatomarlist.Noseencontraronacciones=No actions found
accion.accionatomarlist.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionatomarlist.Nosepudorealizarlaoperacionanterior=Unable to perform the above operation.
accion.accionatomarlist.Reportada=Reportada
accion.accionatomarlist.Reportada=Reported
accion.accionatomarlist.Reportada=Reported
accion.accionatomarlist.Reportada=Reported
accion.accionatomarlist.Sehaeliminadolaaccionpreventiva=The Preventive action has been deleted
accion.accionatomarlist.Sehaeliminadolaaccionpreventiva=The preventive action has been deleted
accion.accionatomarlist.Sehamarcadolaaccioncomocerrada=The action has been closed
accion.accionatomarlist.Sehamarcadolaaccioncomocerrada=The action has been set as closed.
accion.accionatomarlist.Sehamarcadolaaccioncomoimplementada=The action has been implemented
accion.accionatomarlist.Sehamarcadolaaccioncomoimplementada=The action has been marked as implemented
accion.accionatomarlist.siguientePaso=Next Step
accion.accionatomarlist.siguientePaso=Next Step
accion.accionatomarlist.--TODOS--=-- ALL --
accion.accionatomarlist.--TODOS--=-- ALL --
accion.accionatomarlist.Ver=View
accion.accionatomarlist.Ver=View
accion.accionatomarlist.Verificada=Verified
accion.accionatomarlist.Verificada=Verified
accion.accionatomarlist.VerificarlaAccionPreventiva=Verify Preventive Action
accion.accionatomarlist.VerificarlaAccionPreventiva=Verify Preventive Action
accion.accionatomarlist.Yanohayaccionesposibles=No more possible Actions
accion.accionatomarlist.Yanohayaccionesposibles=There is no longer possible actions
accion.accioncorrectivahandle.AccionCorrectiva=Corrective action:
accion.accioncorrectivahandle.AccionCorrectiva=Corrective Action\:
accion.accioncorrectivahandle.Analisisdelanoconformidad=Nonconformity Analysis\:
accion.accioncorrectivahandle.Analisisdelanoconformidad=Unconformity analisis:
accion.accioncorrectivahandle.Analisisrealizado=Analysis made\:
accion.accioncorrectivahandle.Analisisrealizado=Made analisis:
accion.accioncorrectivahandle.Anexo=Appendix\:
accion.accioncorrectivahandle.Anexo=Included:
accion.accioncorrectivahandle.Auditorverificador=Verifying auditant:
accion.accioncorrectivahandle.Auditorverificador=Verifying Auditor\:
accion.accioncorrectivahandle.boton.Acualizardatos=Update data
accion.accioncorrectivahandle.boton.Acualizardatos=Update data:
accion.accioncorrectivahandle.boton.Cancelar=CANCEL
accion.accioncorrectivahandle.boton.Cancelar=Cancel
accion.accioncorrectivahandle.boton.Cerrar=Close
accion.accioncorrectivahandle.boton.Cerrar=Close
accion.accioncorrectivahandle.boton.CrearAccion=Create Action
accion.accioncorrectivahandle.boton.CrearAccion=Create Action
accion.accioncorrectivahandle.boton.GrabarAnalisis=Record analisis:
accion.accioncorrectivahandle.boton.GrabarAnalisis=Save Analysis
accion.accioncorrectivahandle.boton.GrabarVerificacion=Record verification:
accion.accioncorrectivahandle.boton.GrabarVerificacion=Save Verification
accion.accioncorrectivahandle.boton.Limpiar=Clean
accion.accioncorrectivahandle.boton.Limpiar=Clear
accion.accioncorrectivahandle.Clavedelaaccioncorrectiva=Corrective action ID:
accion.accioncorrectivahandle.Clavedelaaccioncorrectiva=Corrective Action ID:
accion.accioncorrectivahandle.Clavedelaauditoriaalaquepertenece=Corresponding audit code:
accion.accioncorrectivahandle.Clavedelaauditoriaalaquepertenece=ID of the audit it belongs to:
accion.accioncorrectivahandle.Cumple-Nocumple=Meets - Doesn't meets:
accion.accioncorrectivahandle.Cumple-Nocumple=Satisfies- Not Satisfies\: 
accion.accioncorrectivahandle.Descripciondelanoconformidad=Description of nonconformity\:
accion.accioncorrectivahandle.Descripciondelanoconformidad=Unconformity description
accion.accioncorrectivahandle.Efectiva-Noefectiva=Efective - Unefective:
accion.accioncorrectivahandle.Efectiva-Noefectiva=Effective - Non effective\: 
accion.accioncorrectivahandle.--Encargado--=-- IN CHARGE --
accion.accioncorrectivahandle.--Encargado--=--Leader--
accion.accioncorrectivahandle.FechadeImplementacion=Implementation date:
accion.accioncorrectivahandle.FechadeImplementacion=Implementation Date\:
accion.accioncorrectivahandle.FechadelaEvaluacion=Evaluation date:
accion.accioncorrectivahandle.FechadelaEvaluacion=Evaluation Date\: 
accion.accioncorrectivahandle.FechadelAnalisis=Analisis date:
accion.accioncorrectivahandle.FechadelAnalisis=Analysis Date\: 
accion.accioncorrectivahandle.header.ACCIONESCORRECTIVAS=CORRECTIVE ACTIONS
accion.accioncorrectivahandle.header.ACCIONESCORRECTIVAS=CORRECTIVE ACTIONS
accion.accioncorrectivahandle.javascript.-Accioncorrectiva=- Corrective Action
accion.accioncorrectivahandle.javascript.-Accioncorrectiva=- Corrective Action
accion.accioncorrectivahandle.javascript.-Analisis=- Analysis
accion.accioncorrectivahandle.javascript.-Analisis=- Analysis
accion.accioncorrectivahandle.javascript.-Descripcion=- Description
accion.accioncorrectivahandle.javascript.-Descripcion=- Description
accion.accioncorrectivahandle.javascript.-Evidencias=- Evidence
accion.accioncorrectivahandle.javascript.-Evidencias=- Evidences
accion.accioncorrectivahandle.javascript.-Fechadeimplementacion=- Implementation Date
accion.accioncorrectivahandle.javascript.-Fechadeimplementacion=- Implementation date
accion.accioncorrectivahandle.javascript.Laaccioncorrectivahansidoevaluada=The corrective action has been evaluated
accion.accioncorrectivahandle.javascript.Laaccioncorrectivahansidoevaluada=The corrective action has been evaluated
accion.accioncorrectivahandle.javascript.Laaccioncorrectivahasidodadadealta=The corrective action has been added
accion.accioncorrectivahandle.javascript.Laaccioncorrectivahasidodadadealta=The preventive action has been created
accion.accioncorrectivahandle.javascript.LosDatosdelaaccioncorrectivahansidoactualizados=Data from the corrective action has been updated
accion.accioncorrectivahandle.javascript.LosDatosdelaaccioncorrectivahansidoactualizados=Los Datos de la acci\u00f3n correctiva han sido actualizados.
accion.accioncorrectivahandle.javascript.LosDatosdelaaccioncorrectivahansidoactualizados=The corrective action has been updated
accion.accioncorrectivahandle.javascript.LosDatosdelaaccioncorrectivahansidoactualizados=The corrective action has been updated
accion.accioncorrectivahandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunaaccioncorrectiva=The following data are necessary to add a corrective action.
accion.accioncorrectivahandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunaaccioncorrectiva=The next data y nessesary to create a corrective action:
accion.accioncorrectivahandle.javascript.-Responsable=- Leader
accion.accioncorrectivahandle.javascript.-Responsable=- Responsible
accion.accioncorrectivahandle.javascript.-Verificador=- Verifier
accion.accioncorrectivahandle.javascript.-Verificador=- Verifier
accion.accioncorrectivahandle.ObservacionesdelaEvidencia=Evidence observations\:
accion.accioncorrectivahandle.ObservacionesdelaEvidencia=Evidence obsevation:
accion.accioncorrectivahandle.UsuarioEncargado=User in charge:
accion.accioncorrectivahandle.UsuarioEncargado=User in Charge\:
accion.accioncorrectivahandle.--Verificador--=-- VERIFIER --
accion.accioncorrectivahandle.--Verificador--=--Verifier--
accion.accioncorrectivalector.Analizada=Analized
accion.accioncorrectivalector.Analizada=Analyzed
accion.accioncorrectivalector.Asignada=Asigned
accion.accioncorrectivalector.Asignada=Assigned
accion.accioncorrectivalector.Auditoria= Audit:
accion.accioncorrectivalector.Auditoria= Auditor&iacute;a:
accion.accioncorrectivalector.Auditoria=Audit
accion.accioncorrectivalector.Auditoria=Audit
accion.accioncorrectivalector.Cerrada=Closed
accion.accioncorrectivalector.Cerrada=Closed
accion.accioncorrectivalector.Clave=Code
accion.accioncorrectivalector.Clave=ID
accion.accioncorrectivalector.editarAccionCorrectiva=Editar Accion Correctiva
accion.accioncorrectivalector.editarAccionCorrectiva=editCorrectiveAction
accion.accioncorrectivalector.Estado=Status
accion.accioncorrectivalector.Estado=Status
accion.accioncorrectivalector.header.CONSULTADEACCIONESCORRECTIVAS=CORRECTIVE ACTION LIST
accion.accioncorrectivalector.header.CONSULTADEACCIONESCORRECTIVAS=Corrective actions consultation:
accion.accioncorrectivalector.Implementacion=Implementation
accion.accioncorrectivalector.Implementacion=Implementation
accion.accioncorrectivalector.Implementada=Implemented
accion.accioncorrectivalector.Implementada=Implemented
accion.accioncorrectivalector.Noseencontraronaccionesparalaauditoriaindicada=Can't find actions for the selected audit:
accion.accioncorrectivalector.Noseencontraronaccionesparalaauditoriaindicada=There are no actions for the indicated audit
accion.accioncorrectivalector.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accioncorrectivalector.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accioncorrectivalector.Responsable=Leader
accion.accioncorrectivalector.Responsable=Responsible
accion.accioncorrectivalector.--Todas--=-- ALL --
accion.accioncorrectivalector.--Todas--=-- ALL --
accion.accioncorrectivalector.Verificada=Verified
accion.accioncorrectivalector.Verificada=Verifier
accion.accioncorrectivalist.Analizada=Analized
accion.accioncorrectivalist.Analizada=Analyzed
accion.accioncorrectivalist.AnalizarlaAccionCorrectiva=\:Analyze Corrective Action
accion.accioncorrectivalist.AnalizarlaAccionCorrectiva=Analise the corrective action
accion.accioncorrectivalist.Asignada=Asigned
accion.accioncorrectivalist.Asignada=Assigned
accion.accioncorrectivalist.AsignadaVerlaAccionCorrectiva=Assigned: Show corrective action
accion.accioncorrectivalist.AsignadaVerlaAccionCorrectiva=Assigned\: Show Corrective Action
accion.accioncorrectivalist.Auditoria=Audit
accion.accioncorrectivalist.Auditoria=Audit
accion.accioncorrectivalist.Auditoria=Audit:
accion.accioncorrectivalist.Auditoria=Auditor&iacute;a:
accion.accioncorrectivalist.Borrar=delete
accion.accioncorrectivalist.Borrar=Delete
accion.accioncorrectivalist.Cerrada=Close
accion.accioncorrectivalist.Cerrada=Closed
accion.accioncorrectivalist.cerrarAccion=Close Action
accion.accioncorrectivalist.cerrarAccion=closeAction
accion.accioncorrectivalist.Clave=Code
accion.accioncorrectivalist.Clave=ID
accion.accioncorrectivalist.editarAccionCorrectiva=Edit corrective action
accion.accioncorrectivalist.editarAccionCorrectiva=editCorrectiveAction
accion.accioncorrectivalist.Estado=Status
accion.accioncorrectivalist.Estado=Status
accion.accioncorrectivalist.header.CONTROLDEACCIONESCORRECTIVAS=CORRECTIVE ACTIONS CONTROL
accion.accioncorrectivalist.header.CONTROLDEACCIONESCORRECTIVAS=CORRECTIVE ACTIONS EDITION
accion.accioncorrectivalist.Implementacion=Implementation
accion.accioncorrectivalist.Implementacion=Implementation
accion.accioncorrectivalist.Implementada=Implemented
accion.accioncorrectivalist.Implementada=Implemented
accion.accioncorrectivalist.implementarAccion=Implement action
accion.accioncorrectivalist.implementarAccion=implementAction
accion.accioncorrectivalist.ImplementarlaAccionCorrectiva=: Implement the corrective action
accion.accioncorrectivalist.ImplementarlaAccionCorrectiva=\: Implement Corrective Action
accion.accioncorrectivalist.javascript.DeseamarcarlaAccioncomocerrada=Do you wish to close the action?
accion.accioncorrectivalist.javascript.DeseamarcarlaAccioncomocerrada=Do you wish to set the action closed?
accion.accioncorrectivalist.javascript.DeseamarcarlaAccioncomoimplementada=Do you wish to impplement the action?
accion.accioncorrectivalist.javascript.DeseamarcarlaAccioncomoimplementada=Do you wish to mark the action as implemented?
accion.accioncorrectivalist.javascript.Laaccioncorrectivaseraborradadelsistema=The action will be removed!
accion.accioncorrectivalist.javascript.Laaccioncorrectivaseraborradadelsistema=The corrected action has been deleted form the system
accion.accioncorrectivalist.Marcarcomocerrada=\: Mark as closed
accion.accioncorrectivalist.Marcarcomocerrada=Close
accion.accioncorrectivalist.Noseencontraronaccionesparalaauditoriaindicada=Can't find actions for the specified audit
accion.accioncorrectivalist.Noseencontraronaccionesparalaauditoriaindicada=There are no actions for the indicated audit
accion.accioncorrectivalist.Responsable=Leader
accion.accioncorrectivalist.Responsable=Responsible
accion.accioncorrectivalist.Sehaeliminadolaaccioncorrectiva=Corrective action has been deleted
accion.accioncorrectivalist.Sehaeliminadolaaccioncorrectiva=Corrective Action has been eliminated
accion.accioncorrectivalist.Sehamarcadolaaccioncomocerrada=The action has been clased
accion.accioncorrectivalist.Sehamarcadolaaccioncomocerrada=The action has been marked as closed
accion.accioncorrectivalist.Sehamarcadolaaccioncomoimplementada=The action has been implemented
accion.accioncorrectivalist.Sehamarcadolaaccioncomoimplementada=The action has been marked as implemented
accion.accioncorrectivalist.siguientePaso=Next step
accion.accioncorrectivalist.siguientePaso=nextStep
accion.accioncorrectivalist.--Todas--=-- ALL --
accion.accioncorrectivalist.--Todas--=-- ALL --
accion.accioncorrectivalist.Verificada=Verified
accion.accioncorrectivalist.Verificada=Verified
accion.accioncorrectivalist.VerificarlaAccionCorrectiva=: Verify corrective action
accion.accioncorrectivalist.VerificarlaAccionCorrectiva=\: Verify Corrective Action
accion.accioncorrectivalist.VerlaAccionCorrectiva=: Show la  Accion Correctiva
accion.accioncorrectivalist.VerlaAccionCorrectiva=: Show la  Accion Correctiva
accion.accioncorrectivalist.VerlaAccionCorrectiva=: Show la  Accion Correctiva
accion.accioncorrectivalist.VerlaAccionCorrectiva=\: Show Corrective Action
accion.accioncorrectivalist.VerlaAccionCorrectiva=Show corrective action
accion.accioncorrectivalist.VerlaAccionCorrectiva=Show corrective action
accion.accioncorrectivalist.VerlaAccionCorrectiva=Show the corrective action
accion.accioncorrectivalist.VerlaAccionCorrectiva=Show the corrective action
accion.acciongenerica.AnalisisDatos=Analisis de Datos
accion.acciongenerica.AuditoriaExternaCalidad=Auditoria Externa de Calidad
accion.acciongenerica.AuditoriaInternaCalidad=Auditoria Interna de Calidad
accion.acciongenerica.Indicadores=Indicadores
accion.acciongenerica.Operacion=Operacion
accion.acciongenerica.Otros=Otros
accion.acciongenerica.Proyectos=Proyectos
accion.acciongenerica.QuejasCliente=Quejas del Cliente
accion.acciongenerica.RevisionDirectiva=Revision Directiva
accion.acciongenerica.titulo=Title
accion.acciongenericahandle.Accion=Action
accion.acciongenericahandle.AcciondeCorreccionInmediata=Create immediate correction action:
accion.acciongenericahandle.Accionesarealizarse=Activities:
accion.acciongenericahandle.AccionRelacionadaPadre= Related Action (Father):
accion.acciongenericahandle.Analisisdecausas=Cause analysis:
accion.acciongenericahandle.subirDocumentos=Select file:
accion.acciongenericahandle.Asignaralaubicacion=Assign to department:
accion.acciongenericahandle.Auditoria=Audit
accion.acciongenericahandle.Avance=Progress
accion.acciongenericahandle.Borrar=Delete
accion.acciongenericahandle.boton.AgregarNueva=Create New
accion.acciongenericahandle.boton.Controlacciones=Control Actions
accion.acciongenericahandle.boton.Evaluar=Evaluate
accion.acciongenericahandle.boton.Grabarcambios=Save Changes
accion.acciongenericahandle.boton.Listaracciones=List Actions
accion.acciongenericahandle.Clavedelaaccion=Action ID:
accion.acciongenericahandle.ClavedelaAccionPadre=Father action key:
accion.acciongenericahandle.Clavedelaauditoria=Audit ID:
accion.acciongenericahandle.Consecuencia=Consequence:
accion.acciongenericahandle.ControlAPD=ACTIONS EDITION
accion.acciongenericahandle.ControlAPO=ACTIONS EDITION
accion.acciongenericahandle.Creada=Created
accion.acciongenericahandle.Editar=Edit
accion.acciongenericahandle.Editaraccioner=Edit action
accion.acciongenericahandle.Enproceso=In process
accion.acciongenericahandle.Estado=Status
accion.acciongenericahandle.FechaImplementacion=Implementation date
accion.acciongenericahandle.FechaReportada=Reported date:
accion.acciongenericahandle.--Fuente--=--Source--
accion.acciongenericahandle.Fuente=Source:
accion.acciongenericahandle.Prioridad=Priority:
accion.acciongenericahandle.Tipo=Type:
accion.acciongenericahandle.Sistema=System:
accion.acciongenericahandle.GerentedelaUbicacion=Department Manager:
accion.acciongenericahandle.Encargadodelmodulo=Module manager
accion.acciongenericahandle.PuedesAutorizar=You can evaluate the finding because you are the module manager, but this is assigned to department manager
accion.acciongenericahandle.Implementada=Implemented
accion.acciongenericahandle.javascript.Accionatomar=- Actions to take
accion.acciongenericahandle.javascript.AceptarAnalisis=Please, save Cause analysis
accion.acciongenericahandle.javascript.Analisisdecausas=- Cause analysis
accion.acciongenericahandle.javascript.Aprobarlaaccion=- Approve action
accion.acciongenericahandle.javascript.Asignealmenosunaaccionatomar=- Assign at least one action to take
accion.acciongenericahandle.javascript.Clavedelaauditoria=- Audit ID
accion.acciongenericahandle.javascript.Consecuencia=- Consequence
accion.acciongenericahandle.javascript.Controlacciones=Control actions
accion.acciongenericahandle.javascript.Deseaborrarlaaccionatomar=Do you want to remove the action?
accion.acciongenericahandle.javascript.Fuentedelaaccion=- Source
accion.acciongenericahandle.javascript.Clave=- ID
accion.acciongenericahandle.javascript.hansidoactualizados=have been updated.
accion.acciongenericahandle.javascript.Laaccionhasidodadadealta=The finding has been created.
accion.acciongenericahandle.javascript.Laoperaci\u00f3nsehallevadoacabo=The finding has been successfully updated.
accion.acciongenericahandle.javascript.Listaracciones=List Actions
accion.acciongenericahandle.javascript.LosDatosdelaaccionhansidoactualizados=The finding has been successfully updated.
accion.acciongenericahandle.javascript.Lossiguientesdatossonnecesarios=Following items are necessary:
accion.acciongenericahandle.javascript.Lossiguientesdatossonnecesariosparacontinuar=The following items are necessary to continue:
accion.acciongenericahandle.javascript.exceedsLimit=The following items exceeds the characters limit
accion.acciongenericahandle.javascript.limit=Maximum:
accion.acciongenericahandle.javascript.currentLength:Current:
accion.acciongenericahandle.javascript.Responsabledeanalizalascausas=- Leader of cause analysis
accion.acciongenericahandle.javascript.Situacion=- Find.
accion.acciongenericahandle.javascript.Ubicacion=- Assign to department.
accion.acciongenericahandle.javascript.Usuario=- Leader.
accion.acciongenericahandle.Laaccionnecesitaunaacciondecorreccioninmediata=The action requires immediate corrective action.
accion.acciongenericahandle.ListadoAPD=LISTING OF ACTIONS ON PRODUCTS.
accion.acciongenericahandle.ListadoAPO=LISTING OF ACTIONS ON PROJECTS
accion.acciongenericahandle.Listaracciones=List Actions
accion.acciongenericahandle.Nosehanasignadoaccionesatomar=Actions to take have not been assigned .
accion.acciongenericahandle.Nosepudorealizarlaoperacionanterior=Unable to perform the above operation.
accion.acciongenericahandle.Porasignar=To be assigned
accion.acciongenericahandle.Procedelaaccion=The actions proceeds:
accion.acciongenericahandle.Procedelaqueja=Does the complaint proceed?
accion.acciongenericahandle.RazonPorLaQueNoProcede=Motive that causes the action not to proceed
accion.acciongenericahandle.Responsable=Responsible
accion.acciongenericahandle.--Responsable--=--Leader--
accion.acciongenericahandle.Responsabledeanalizarlascausas=Responsible of analizing causes:
accion.acciongenericahandle.ResultadosDeEfectividad=Effectiveness evaluation
accion.acciongenericahandle.-ResultadosDeEfectividad=-Effectiveness evaluation.
accion.acciongenericahandle.Seleccione=Select
accion.acciongenericahandle.Situacion=Finding:
accion.acciongenericahandle.SOLICITUDDEACCIONESCORRECTIVAS=CORRECTIVE ACTION REQUEST
accion.acciongenericahandle.SOLICITUDDEACCIONESDEMEJORACONTINUA=CONTINUOUS IMPROVEMENT ACTION REQUEST
accion.acciongenericahandle.SOLICITUDDEACCIONESPREVENTIVAS=PREVENTIVE ACTION REQUEST
accion.acciongenericahandle.Titulo=Title:
accion.acciongenericahandle.titulo=Title:
accion.acciongenericahandle.TituloAPD=ACTION REQUEST
accion.acciongenericahandle.TituloAPO=ACTION REQUEST
accion.acciongenericahandle.--Ubicacion--=--Department--
accion.acciongenericahandle.Ver=View
accion.acciongenericahandle.Verificada=Verified
accion.acciongenericahandle.Verificador=Verifier
accion.acciongenericalist.AccionesEnDondeNoParticipa=Actions where you do not participate
accion.acciongenericalist.AccionesEnDondeParticipa=Actions where you participate
accion.acciongenericalist.AceptarAccion=Press to verify the action.
accion.acciongenericalist.Analizadayenimplementacion=The actual status is ANALYZED.
accion.acciongenericalist.Aprobada=The actual status is APRROVED.
accion.acciongenericalist.Asignada=The actual status is ASSIGNED.
accion.acciongenericalist.Asignarelresponsable=\ Press to assign the leader.
accion.acciongenericalist.Autor=Author
accion.acciongenericalist.Borrar=Delete
accion.acciongenericalist.CanceladaNoProcede= &nbsp;Cancelled (Not Proceed)
accion.acciongenericalist.Cerrada=The action has been VERIFIED. The actual status is CLOSED.
accion.acciongenericalist.Clave=ID
accion.acciongenericalist.CONTROLDEACCIONESCORRECTIVAS=CONTROL OF CORRECTIVE ACTIONS
accion.acciongenericalist.CONTROLDEACCIONESDEMEJORACONTINUA=CONTROL OF ACTIONS CONTINUOUS IMPROVEMENT
accion.acciongenericalist.CONTROLDEACCIONESPREVENTIVAS=CONTROL OF PREVENTIVE ACTIONS
accion.acciongenericalist.DefinirlasAccionesatomar=\ Press to define the actions to take.
accion.acciongenericalist.Editar=Edit
accion.acciongenericalist.Editar=Edit
accion.acciongenericalist.EditarAccion=Edit Action
accion.acciongenericalist.EditarAccionaTomar =Edit Actions to Take
accion.acciongenericalist.EditarAccionesaTomar=Edit Actions to Take
accion.acciongenericalist.EditarAccionesATomar=Edit actions to take.
accion.acciongenericalist.EdoReportada=The actual status is REPORTED.
accion.acciongenericalist.Efectiva= &nbsp;(Effective)
accion.acciongenericalist.Estado=Status
accion.acciongenericalist.EstadoAnalizada=Analizada
accion.acciongenericalist.EstadoAprobada=Aprobada
accion.acciongenericalist.EstadoAsignada=Asignada
accion.acciongenericalist.EstadoAsignada2=Asignada
accion.acciongenericalist.EstadoCanceladaNoProcede=Cancelada No Procede
accion.acciongenericalist.EstadoEfectiva=Efectiva
accion.acciongenericalist.EstadoNoEfectiva=No Efectiva
accion.acciongenericalist.EstadoReportada=Reportada
accion.acciongenericalist.EstadoT=Status:
accion.acciongenericalist.FechaFin=End date
accion.acciongenericalist.FechaInicio=Start date
accion.acciongenericalist.Fuente=Source
accion.acciongenericalist.Implementadayverificada=The actual status is EXECUTED.
accion.acciongenericalist.javascript.DeseamarcarlaAccioncomocerrada=Do you want to set the action as closed?
accion.acciongenericalist.javascript.Laacciondemejoracontinuaseraborradadelsistema=The action will be removed from the system!
accion.acciongenericalist.Marcarcomocerrada=Press to set as closed.
accion.acciongenericalist.Modificarelanalisisolasaccionesatomar=Press to modify analysis or the actions to take.
accion.acciongenericalist.NoAsignado=Not Assigned
accion.acciongenericalist.NoEfectiva= &nbsp;(Not Effective)
accion.acciongenericalist.Noseencontraronacciones= The actions were not found
accion.acciongenericalist.NoSeEncontraronAccionesDondeNoParticipa=Actions where you do not participate were not found.
accion.acciongenericalist.NoSeEncontraronAccionesDondeParticipa=Actions where you participate were not found.
accion.acciongenericalist.Nosepudorealizarlaoperacionanterior=Unable to perform the above operation
accion.acciongenericalist.PresioneParaVerlaAccion=\ Press to view the action.
accion.acciongenericalist.Reportada=Reported
accion.acciongenericalist.Responsable=Leader
accion.acciongenericalist.Sehaeliminadolaacciondemejoracontinua=The action was removed
accion.acciongenericalist.Sehamarcadolaaccioncomocerrada=The action has been set as closed
accion.acciongenericalist.Tipodeaccion=Action type
accion.acciongenericalist.titulo = Title
accion.acciongenericalist.titulo = Title
accion.acciongenericalist.Titulo=Title
accion.acciongenericalist.TODAS=ALL
accion.acciongenericalist.TODOS=ALL
accion.acciongenericalist.Ubicacion=Department
accion.acciongenericalist.Ver=View
accion.acciongenericalist.Ver=View
accion.acciongenericalist.VerificarlaAccion= Press to verify the action to take.
accion.acciongenericalist.VerlaAccion=: View the Action
accion.accionmejoracontinuahandle.Accion=Action
accion.accionmejoracontinuahandle.Accion=Action
accion.accionmejoracontinuahandle.Accionesarealizarse=Action(s) to take:
accion.accionmejoracontinuahandle.Accionesarealizarse=Actions to take\:
accion.accionmejoracontinuahandle.Auditorverificador=Verifying auditer:
accion.accionmejoracontinuahandle.Auditorverificador=Verifying Auditor\:
accion.accionmejoracontinuahandle.Avance=Advance
accion.accionmejoracontinuahandle.Avance=Advancement
accion.accionmejoracontinuahandle.Borrar=Delete
accion.accionmejoracontinuahandle.Borrar=Delete
accion.accionmejoracontinuahandle.boton.Asignar=Asign
accion.accionmejoracontinuahandle.boton.Asignar=Assign
accion.accionmejoracontinuahandle.boton.Cancelar=Cancel
accion.accionmejoracontinuahandle.boton.Cancelar=Cancel
accion.accionmejoracontinuahandle.boton.Cerrar=close
accion.accionmejoracontinuahandle.boton.Cerrar=Close
accion.accionmejoracontinuahandle.boton.CrearAccion=Create action
accion.accionmejoracontinuahandle.boton.CrearAccion=Create Action
accion.accionmejoracontinuahandle.boton.Evaluar=Evaluate
accion.accionmejoracontinuahandle.boton.Evaluar=Evaluate
accion.accionmejoracontinuahandle.boton.Grabarcambios=Save changes
accion.accionmejoracontinuahandle.boton.Grabarcambios=Save Changes
accion.accionmejoracontinuahandle.boton.Limpiar=Clean
accion.accionmejoracontinuahandle.boton.Limpiar=Clear
accion.accionmejoracontinuahandle.Clavedelaacciondemejoracontinua=Constant upgradiong action ID:
accion.accionmejoracontinuahandle.Clavedelaacciondemejoracontinua=Improvement Action code:
accion.accionmejoracontinuahandle.Costo=from the action=Cost of action:
accion.accionmejoracontinuahandle.Costodelaaccion=Action charge:
accion.accionmejoracontinuahandle.Costodelaaccion=Cost of action\:
accion.accionmejoracontinuahandle.Cumple-Nocumple=Meets - Doesn't meets:
accion.accionmejoracontinuahandle.Cumple-Nocumple=Satisfies - Not Satisfies\:
accion.accionmejoracontinuahandle.Editar=Edit
accion.accionmejoracontinuahandle.Editar=Edit
accion.accionmejoracontinuahandle.EncargadoC=Leader
accion.accionmejoracontinuahandle.EncargadoC=User in charge:
accion.accionmejoracontinuahandle.Fechadeevaluacion=Evaluation date:
accion.accionmejoracontinuahandle.Fechadeevaluacion=Evaluation date\:
accion.accionmejoracontinuahandle.Fechametadecumplimiento=Acomplishment goal day:
accion.accionmejoracontinuahandle.Fechametadecumplimiento=Target Fullfillment Date\:
accion.accionmejoracontinuahandle.header.ACCIONESDEMEJORACONTINUA=CONSTANT UPGARDING ACTIONS
accion.accionmejoracontinuahandle.header.ACCIONESDEMEJORACONTINUA=IMPROVEMENT ACTIONS
accion.accionmejoracontinuahandle.Implementacion=Implementation
accion.accionmejoracontinuahandle.Implementacion=Implementation
accion.accionmejoracontinuahandle.javascript.-Asignealmenosunaaccionatomar=- Assign at least one action to take
accion.accionmejoracontinuahandle.javascript.-Asignealmenosunaaccionatomar=- Assign at least one action to take
accion.accionmejoracontinuahandle.javascript.-Auditorverificador=- Verifying auditer
accion.accionmejoracontinuahandle.javascript.-Auditorverificador=- Verifying Auditor
accion.accionmejoracontinuahandle.javascript.-Costodelaaccion=- Cost of Action
accion.accionmejoracontinuahandle.javascript.-Costodelaaccion=- The action charge
accion.accionmejoracontinuahandle.javascript.Elusuarioresponsableyauditorverificadorhansidoasignados=The responsible user and the verifier auditer have been asigned.
accion.accionmejoracontinuahandle.javascript.Elusuarioresponsableyauditorverificadorhansidoasignados=The user in charge and the verifying auditor have been assigned
accion.accionmejoracontinuahandle.javascript.-Evaluarresultado=- Evaluate result
accion.accionmejoracontinuahandle.javascript.-Evaluarresultado=- Evaluate Result
accion.accionmejoracontinuahandle.javascript.-Fechadecumplimiento=- Acomplishment date
accion.accionmejoracontinuahandle.javascript.-Fechadecumplimiento=- Fullfillment Date
accion.accionmejoracontinuahandle.javascript.Laacciondemejoracontinuahansidoverificada=The constant upgarding action has been verified
accion.accionmejoracontinuahandle.javascript.Laacciondemejoracontinuahansidoverificada=The improvement action has been verified.
accion.accionmejoracontinuahandle.javascript.Laacciondemejoracontinuahasidodadadealta=The constant upgarding action has been created
accion.accionmejoracontinuahandle.javascript.Laacciondemejoracontinuahasidodadadealta=The improvement action has been added.
accion.accionmejoracontinuahandle.javascript.LosDatosdelaacciondemejoracontinuahansidoactualizados=Data from the improvement action has been updated
accion.accionmejoracontinuahandle.javascript.LosDatosdelaacciondemejoracontinuahansidoactualizados=The constant upgarding action data has been updated
accion.accionmejoracontinuahandle.javascript.Lossiguientesdatossonnecesarios=The following items are required
accion.accionmejoracontinuahandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
accion.accionmejoracontinuahandle.javascript.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionmejoracontinuahandle.javascript.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionmejoracontinuahandle.javascript.-Recursosrequeridos=- Required resources
accion.accionmejoracontinuahandle.javascript.-Recursosrequeridos=- Required Resources
accion.accionmejoracontinuahandle.javascript.-Situacionactual=- Actual situation
accion.accionmejoracontinuahandle.javascript.-Situacionactual=- Actual situation
accion.accionmejoracontinuahandle.javascript.-Situaciondeseada=- Desired situation
accion.accionmejoracontinuahandle.javascript.-Situaciondeseada=- Desired situation
accion.accionmejoracontinuahandle.javascript.-Tecnicasausar=- Required Techniques
accion.accionmejoracontinuahandle.javascript.-Tecnicasausar=- Techniques to use
accion.accionmejoracontinuahandle.javascript.-Usuarioencargado=- User in charge
accion.accionmejoracontinuahandle.javascript.-Usuarioencargado=- User in charge
accion.accionmejoracontinuahandle.Nosehanasignadoaccionesatomar=Haven't assigned actions
accion.accionmejoracontinuahandle.Nosehanasignadoaccionesatomar=There are no actions to take.
accion.accionmejoracontinuahandle.Recursosrequeridos=Required resources:
accion.accionmejoracontinuahandle.Recursosrequeridos=Required Resources\:
accion.accionmejoracontinuahandle.Situacionactual=Actual situation:
accion.accionmejoracontinuahandle.Situacionactual=Actual situation\:
accion.accionmejoracontinuahandle.Situaciondeseada=Desired situation:
accion.accionmejoracontinuahandle.Situaciondeseada=Desired situation\:
accion.accionmejoracontinuahandle.Tecnicasausar=Required Tecniques
accion.accionmejoracontinuahandle.Tecnicasausar=Techniques to use\:
accion.accionmejoracontinuahandle.UsuarioEncargado=User in charge:
accion.accionmejoracontinuahandle.UsuarioEncargado=User in Charge\:
accion.accionmejoracontinuahandle.VerificadorC=Verifier
accion.accionmejoracontinuahandle.VerificadorC=Verifier
accion.accionmejoracontinualector.Asignada=Asigned
accion.accionmejoracontinualector.Asignada=Assigned
accion.accionmejoracontinualector.Autor=Author
accion.accionmejoracontinualector.Autor=Autor
accion.accionmejoracontinualector.Cerrada=Closed
accion.accionmejoracontinualector.Cerrada=Closed
accion.accionmejoracontinualector.Clave=Code
accion.accionmejoracontinualector.Clave=ID
accion.accionmejoracontinualector.Definida=Defined
accion.accionmejoracontinualector.Definida=Defined
accion.accionmejoracontinualector.editarAccionMejoraContinua=Edit constant upgrading action
accion.accionmejoracontinualector.editarAccionMejoraContinua=editImprovementAction
accion.accionmejoracontinualector.Encargado=Leader
accion.accionmejoracontinualector.Encargado=User in charge
accion.accionmejoracontinualector.Estado\:=Status:
accion.accionmejoracontinualector.Estado\:=Status:
accion.accionmejoracontinualector.Estado=Status
accion.accionmejoracontinualector.Estado=Status
accion.accionmejoracontinualector.header.CONSULTADEACCIONESDEMEJORACONTINUA=CONSTANT UPGARDING ACTIONS CHECK
accion.accionmejoracontinualector.header.CONSULTADEACCIONESDEMEJORACONTINUA=IMPROVEMENT ACTION LIST
accion.accionmejoracontinualector.Implementada=Implemented
accion.accionmejoracontinualector.Implementada=Implemented
accion.accionmejoracontinualector.NoAsignado=Not Assigned
accion.accionmejoracontinualector.NoAsignado=Unasigned
accion.accionmejoracontinualector.Noseencontraronacciones=Can't find actions
accion.accionmejoracontinualector.Noseencontraronacciones=No actions found
accion.accionmejoracontinualector.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionmejoracontinualector.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionmejoracontinualector.Reportada=Reportada
accion.accionmejoracontinualector.Reportada=Reported
accion.accionmejoracontinualector.Reportada=Reported
accion.accionmejoracontinualector.Reportada=Reported
accion.accionmejoracontinualector.TODOS=ALL
accion.accionmejoracontinualector.TODOS=ALL
accion.accionmejoracontinualector.Verificada=Verified
accion.accionmejoracontinualector.Verificada=Verified
accion.accionmejoracontinualist.Asignada=Assigned
accion.accionmejoracontinualist.Asignada=Assigned
accion.accionmejoracontinualist.AsignarlaAcciondemejoracontinua=: Assign improvement continuous action
accion.accionmejoracontinualist.AsignarlaAcciondemejoracontinua=\: Assign improvement continuous action
accion.accionmejoracontinualist.Autor=Author
accion.accionmejoracontinualist.Autor=Author
accion.accionmejoracontinualist.Borrar=Delete
accion.accionmejoracontinualist.Borrar=Delete
accion.accionmejoracontinualist.BorrarlaAcciondemejoracontinua=Delete the continuous improvement action
accion.accionmejoracontinualist.BorrarlaAcciondemejoracontinua=Delete the continuous improvement action
accion.accionmejoracontinualist.Cerrada=Closed
accion.accionmejoracontinualist.Cerrada=Closed
accion.accionmejoracontinualist.cerrarAccion=closeAction
accion.accionmejoracontinualist.cerrarAccion=closeAction
accion.accionmejoracontinualist.Clave=Code
accion.accionmejoracontinualist.Clave=ID
accion.accionmejoracontinualist.ContinuardefiniendolaAcciondemejoracontinua=Continue defining action continuous improvement
accion.accionmejoracontinualist.Definida=Defined
accion.accionmejoracontinualist.Definida=Defined
accion.accionmejoracontinualist.DefinirlasAccionesatomar=: Define actions to take
accion.accionmejoracontinualist.DefinirlasAccionesatomar=\: Define Actions to take
accion.accionmejoracontinualist.DeseamarcarlaAccioncomocerrada=Do you wish to set the action as closed?
accion.accionmejoracontinualist.DeseamarcarlaAccioncomocerrada=Do you wish to set the action as closed?
accion.accionmejoracontinualist.DeseamarcarlaAccioncomoimplementada=Do you wish to set the action as implemented?
accion.accionmejoracontinualist.DeseamarcarlaAccioncomoimplementada=Do you wish to set the action as implemented?
accion.accionmejoracontinualist.editarAccionMejoraContinua=editarAccionMejoraContinua
accion.accionmejoracontinualist.editarAccionMejoraContinua=editImprovementAction
accion.accionmejoracontinualist.Encargado=Leader
accion.accionmejoracontinualist.Encargado=Manager
accion.accionmejoracontinualist.Estado\:=Status:
accion.accionmejoracontinualist.Estado\:=Status:
accion.accionmejoracontinualist.Estado=Status
accion.accionmejoracontinualist.Estado=Status
accion.accionmejoracontinualist.header.CONTROLDEACCIONESDEMEJORACONTINUA=CONTROL OF CONTINUOUS IMPROVEMENT ACTIONS
accion.accionmejoracontinualist.header.CONTROLDEACCIONESDEMEJORACONTINUA=CONTROL OF CONTINUOUS IMPROVEMENT ACTIONS
accion.accionmejoracontinualist.Implementada=Implemented
accion.accionmejoracontinualist.Implementada=Implemented
accion.accionmejoracontinualist.Laacciondemejoracontinuaseraborradadelsistema=\u00a1Continuous improvement action will be deleted from the system!
accion.accionmejoracontinualist.Laacciondemejoracontinuaseraborradadelsistema=The action will be removed from the system\!
accion.accionmejoracontinualist.Marcarcomocerrada=: Set as Closed
accion.accionmejoracontinualist.Marcarcomocerrada=\: Mark as closed
accion.accionmejoracontinualist.Marcarcomoimplementada=: Set as implemented
accion.accionmejoracontinualist.Marcarcomoimplementada=\: Mark as Implemented
accion.accionmejoracontinualist.NoAsignado=Not Assigned
accion.accionmejoracontinualist.NoAsignado=Not Assigned
accion.accionmejoracontinualist.Noseencontraronacciones=Actions were not found
accion.accionmejoracontinualist.Noseencontraronacciones=Actions were not found
accion.accionmejoracontinualist.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionmejoracontinualist.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionmejoracontinualist.Reportada=Reportada
accion.accionmejoracontinualist.Reportada=Reported
accion.accionmejoracontinualist.Reportada=Reported
accion.accionmejoracontinualist.Reportada=Reported
accion.accionmejoracontinualist.Sehaeliminadolaacciondemejoracontinua=Constant upgrading action has been deleted
accion.accionmejoracontinualist.Sehaeliminadolaacciondemejoracontinua=The improvement action has been eliminated
accion.accionmejoracontinualist.Sehamarcadolaaccioncomocerrada=The action has been closed
accion.accionmejoracontinualist.Sehamarcadolaaccioncomocerrada=The action has been marked as closed
accion.accionmejoracontinualist.Sehamarcadolaaccioncomoimplementada=The action has been implemented
accion.accionmejoracontinualist.Sehamarcadolaaccioncomoimplementada=The action has been marked as implemented
accion.accionmejoracontinualist.siguientePaso=Next step
accion.accionmejoracontinualist.siguientePaso=nextStep
accion.accionmejoracontinualist.--TODOS--=-- ALL --
accion.accionmejoracontinualist.--TODOS--=--ALL--
accion.accionmejoracontinualist.Verificada=Verified
accion.accionmejoracontinualist.Verificada=Verified
accion.accionmejoracontinualist.VerificarlaAcciondemejoracontinua=: Verify Improvement Continuous Action
accion.accionmejoracontinualist.VerificarlaAcciondemejoracontinua=: Verify the Improvement Action
accion.accionmejoracontinualist.VerlaAcciondemejoracontinua=: Show improvement continuous action
accion.accionmejoracontinualist.VerlaAcciondemejoracontinua=: Show improvement continuous action
accion.accionmejoracontinualist.VerlaAcciondemejoracontinua=: Show la  Accionde mejora continua
accion.accionmejoracontinualist.VerlaAcciondemejoracontinua=: View the Improvement Action
accion.accionpreventivahandle.Accion=Action
accion.accionpreventivahandle.Accionesatomar=Actions to take:
accion.accionpreventivahandle.Auditorverificador=Verifying Auditor:
accion.accionpreventivahandle.Avance=Advance
accion.accionpreventivahandle.Borrar=Delete
accion.accionpreventivahandle.boton.Asignar=Assign
accion.accionpreventivahandle.boton.Cancelar=Cancel
accion.accionpreventivahandle.boton.Cerrar=Close
accion.accionpreventivahandle.boton.CrearAccion=Create Action
accion.accionpreventivahandle.boton.Evaluar=Evaluate
accion.accionpreventivahandle.boton.Grabarcambios=Save Changes
accion.accionpreventivahandle.boton.Limpiar=Clear
accion.accionpreventivahandle.Causa=root = Root cause
accion.accionpreventivahandle.Causaraiz=Root cause
accion.accionpreventivahandle.Clavedelaaccionpreventiva=Preventive Action ID:
accion.accionpreventivahandle.Cumple-Nocumple=Satisfies - Not Satisfies:
accion.accionpreventivahandle.Editar=Edit
accion.accionpreventivahandle.--Encargado--=--Leader--
accion.accionpreventivahandle.Fechadecumplimientofinal=Final Fullfillment Date:
accion.accionpreventivahandle.Fechadeevaluacion=Evaluation Date:
accion.accionpreventivahandle.header.ACCIONESPREVENTIVAS=PREVENTIVE ACTIONS
accion.accionpreventivahandle.Implementacion=Implementation
accion.accionpreventivahandle.javascript.-Asignealmenosunaaccionatomar=- Assign at least one action to take
accion.accionpreventivahandle.javascript.-Auditorverificador=- Verifying Auditor
accion.accionpreventivahandle.javascript.-Causaraiz=- Root Cause
accion.accionpreventivahandle.javascript.Deseaborrarlaaccionatomar=Do you want to erase the action to take?
accion.accionpreventivahandle.javascript.Elusuarioresponsableyauditorverificadorhansidoasignados=The user in charge and the verifying auditor have been assigned.
accion.accionpreventivahandle.javascript.Elusuarioresponsableyauditorverificadorhansidoasignados=The user in charge and the verifying auditor have been assigned.
accion.accionpreventivahandle.javascript.-Evaluarresultado=- Evaluate Result
accion.accionpreventivahandle.javascript.-Fechadeimplementacion=- Implementation Date
accion.accionpreventivahandle.javascript.Laaccionpreventivahansidoverificada=The preventive action has been verified.
accion.accionpreventivahandle.javascript.Laaccionpreventivahasidodadadealta=The preventive action has been added.
accion.accionpreventivahandle.javascript.LosDatosdelaacci\u00f3npreventivahansidoactualizados=Data from the preventive action has been updated.
accion.accionpreventivahandle.javascript.LosDatosdelaaccionpreventivahansidoactualizados=Data from the preventive action has been updated.
accion.accionpreventivahandle.javascript.-Noconformidadpotencial=- Potential non conformity
accion.accionpreventivahandle.javascript.-Situacionactual=- Actual Situation
accion.accionpreventivahandle.javascript.-Usuarioencargado=- User in charge
accion.accionpreventivahandle.Noconformidadpotencial=Potential non conformity:
accion.accionpreventivahandle.Nosehanasignadoaccionesatomar=Actions to take have not been assigned
accion.accionpreventivahandle.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
accion.accionpreventivahandle.Nosepudorealizarlaoperacionanterior=Unable to perform the above operation
accion.accionpreventivahandle.Situacionactual=Actual Situation\:
accion.accionpreventivahandle.UsuarioEncargado=User in Charge:
accion.accionpreventivahandle.--Verificador--=--Verifier--
accion.accionpreventivalector.Asignada=Assigned
accion.accionpreventivalector.Autor=Author
accion.accionpreventivalector.Cerrada=Closed
accion.accionpreventivalector.Clave=ID
accion.accionpreventivalector.Definida=Defined
accion.accionpreventivalector.Encargado=Leader
accion.accionpreventivalector.Estado\:=Status:
accion.accionpreventivalector.Estado=Status
accion.accionpreventivalector.header.CONSULTADEACCIONESPREVENTIVAS=PREVENTIVE ACTION LIST
accion.accionpreventivalector.Implementada=Implemented
accion.accionpreventivalector.NoAsignado=Not Assigned
accion.accionpreventivalector.Noseencontraronacciones=Actions were not found
accion.accionpreventivalector.Nosepudorealizarlaoperacionanterior=Unable to perform the above operation
accion.accionpreventivalector.Reportada=Reported
accion.accionpreventivalector.--TODOS--=-- ALL --
accion.accionpreventivalector.Verificada=Verified
accion.accionpreventivalist.Asignada=Assigned
accion.accionpreventivalist.AsignarlaAccionPreventiva=: Assign Preventive Action
accion.accionpreventivalist.Autor=Author
accion.accionpreventivalist.Cerrada=Closed
accion.accionpreventivalist.Cerrada=Closed
accion.accionpreventivalist.Clave=ID
accion.accionpreventivalist.Definida=Defined
accion.accionpreventivalist.DefinirlasAccionesatomar=: Define actions to take
accion.accionpreventivalist.Encargado=Leader
accion.accionpreventivalist.Estado\:=Status:
accion.accionpreventivalist.Estado=Status
accion.accionpreventivalist.header.CONTROLDEACCIONESPREVENTIVAS=CONTROL OF PREVENTIVE ACTIONS
accion.accionpreventivalist.Implementada=Implemented
accion.accionpreventivalist.javascript.\u00bfDeseamarcarlaAccioncomocerrada=Do you want to set the action closed?
accion.accionpreventivalist.javascript.DeseamarcarlaAccioncomoimplementada=Do you want to set the action as implemented?
accion.accionpreventivalist.javascript.Laaccionpreventivaseraborradadelsistema=The action will be removed from the system!
accion.accionpreventivalist.javascript.Nosepudorealizarlaoperacionanterior=Unable to perform the above operation
accion.accionpreventivalist.javascript.Sehaeliminadolaaccionpreventiva=The preventive action has been eliminated 
accion.accionpreventivalist.javascript.Sehamarcadolaaccioncomocerrada=The action has been set as closed
accion.accionpreventivalist.javascript.Sehamarcadolaaccioncomoimplementada=The action has been set as implemented
accion.accionpreventivalist.Marcarcomocerrada=: Set as closed
accion.accionpreventivalist.Marcarcomoimplementada=: Set as implemented
accion.accionpreventivalist.NoAsignado=Not Assigned
accion.accionpreventivalist.Noseencontraronacciones=Actions were not found
accion.accionpreventivalist.Reportada=Reported
accion.accionpreventivalist.--TODOS--=-- ALL --
accion.accionpreventivalist.Verificada=Verified
accion.accionpreventivalist.VerificarlaAccionPreventiva=: Verify Preventive Action
accion.accionpreventivalist.VerlaAccionPreventiva=: View the Preventive Action
accion.accionreportegeneral.chart.anallizadayenimplementacion=ANALYSED AND IMPLEMENTIG
accion.accionreportegeneral.chart.anallizadayenimplementacion=Analized and implementing
accion.accionreportegeneral.chart.anallizadayenimplementacions=En implementaci\u00f3n
accion.accionreportegeneral.chart.anallizadayenimplementacions=Is Implementing
accion.accionreportegeneral.chart.aprobada=APPROVED
accion.accionreportegeneral.chart.aprobada=Verified
accion.accionreportegeneral.chart.asignada=ASSIGNED
accion.accionreportegeneral.chart.asignada=Assigned
accion.accionreportegeneral.chart.CANCELADA=Cancelled(discarded)
accion.accionreportegeneral.chart.cerrada=CLOSED
accion.accionreportegeneral.chart.cerrada=Closed
accion.accionreportegeneral.chart.correctiva=Corrective
accion.accionreportegeneral.chart.implementadayverificada=IMPLEMENTED AND VERIFIED
accion.accionreportegeneral.chart.implementadayverificada=Implemented and verified
accion.accionreportegeneral.chart.implementadayverificadas=VERIFICADA
accion.accionreportegeneral.chart.implementadayverificadas=Verified
accion.accionreportegeneral.chart.mejoracontinua=Continual Improvement
accion.accionreportegeneral.chart.noasignada=Not Assigned
accion.accionreportegeneral.chart.NOPROCEDE=Non effective
accion.accionreportegeneral.chart.numerodeacciones=Number of actions
accion.accionreportegeneral.chart.preventiva=Preventive
accion.accionreportegeneral.chart.PROCEDE=Effective
accion.accionreportegeneral.chart.producto=Product based
accion.accionreportegeneral.chart.proyecto=Project based
accion.accionreportegeneral.chart.reportada=REPORTED
accion.accionreportegeneral.chart.reportada=Reported
accion.accionreportegeneral.chart.title=Actions
accion.accionreportegeneral.chart.title=Actions
accion.accionreportegeneral.header=General report of findings
accion.accionreportegeneral.Responsable=Responsible
accion.accionreportegeneral.Tipodeaccion=Action type
accion.acciontomarhandleamc.ComentarioDeAvance=Advance comment
action.statusReported = Reported
action.statusAssigned = Assigned
action.statusAnalyzedAndInImplementation = Analyzed and in implementation
action.statusImplementedAndChecked = Implemented and in verification
action.statusApproved = Approved
action.statusClosedEffective = Closed / Effective
action.statusClosedNotEffective = Closed / Ineffective
action.statusNotApplicableCanceled = Not applicable / Cancelled
accion.ejecuta.header.ERROR=ERROR
accion.ejecuta.NOSEPUDOREALIZARLAOPERACIONANTERIOR=TRANSACTION UNSUCCESSFUL
accion.ejecutaamc.Define= Define
accion.ejecutaamc.Error= Error
accion.ejecutaamc.header.ERROR=ERROR
accion.ejecutaamc.Inserta= Insert
accion.ejecutaamc.Modifica= Modifies
accion.ejecutaamc.NOSEPUDOREALIZARLAOPERACIONANTERIOR=TRANSACTION UNSUCCESSFUL
accion.ejecutaamc.NOSEPUDOREALIZARLAOPERACIONANTERIOR=UNABLE TO PERFORM THE AVOBE OPERATION.
accion.ejecutaamc.NosePudoRealizarlaOperacionAnterior=UNABLE TO PERFORM THE AVOBE OPERATION.
accion.ejecutaamc.Verifica= Verifies
accion.ejecutaamcNosePudoRealizarlaOperacionAnterior=UNABLE TO PERFORM THE AVOBE OPERATION.
accion.misacciones.AccionesATomar=Actions to take
accion.misacciones.CanceladaNoProcede= Cancelled (Not Applicable)
accion.misacciones.Clave=ID
accion.misacciones.Consecuencia=Consecuence
accion.misacciones.Editar=Edit
accion.misacciones.Efectiva= &nbsp;(Effective)
accion.misacciones.EncargadoDeUbicacion=Department manager
accion.misacciones.Estado=Status
accion.misacciones.FechaImplementacion=Implementation date
accion.misacciones.FechaVerificacion=Verification date
accion.misacciones.Fuente=Source
accion.misacciones.header.MisAcciones=MY FINDINGS
accion.misacciones.NoEfectiva= &nbsp;(No Effective)
accion.misacciones.NoExistenAccionesEnLasQueParticipaActualmente=There are no actions in which you are involved.
accion.misacciones.NoParticipaEnAccionesATomarDeEstaAccion=You do not participate in actions to take of this action.
accion.misacciones.Responsable=Responsible
accion.misacciones.ResponsableVerifcador=Leader / Verifier
accion.misacciones.Rol=Role
accion.misacciones.RolEnAccionATomar=Roll in an action to take
accion.misacciones.Situacion=Description
accion.misacciones.Ubicacion=Department
accion.misacciones.Verificador=Verifier
actividad.actividadhandle.Archivos=Files
actividad.actividadhandle.Catalogos=Catalogues
actividad.actividadhandle.ComentarioDeAvance=Progess comment
actividad.actividadhandle.Descripcion=Description:
actividad.actividadhandle.Regresar=It will be sent to the previous screen without saving. Are you sure you want to continue?
actividad.actividadhandle.Limpieza=Are you sure to clear the fields?
actividad.actividadhandle.Dificultad=Dificulty
actividad.actividadhandle.Esconder=Hide
actividad.actividadhandle.FechaFin=End date
actividad.actividadhandle.FechaInicio=Start date:
actividad.actividadhandle.FechaVerificacion=Date of verification:
actividad.actividadhandle.javascript.-ComentarioDeAvance=- Comment
actividad.actividadhandle.javascript.-Descripcion=- Description
actividad.actividadhandle.javascript.EstaSeguroQueNoDeseaVerificarLaActividad=Are you sure you do not want to verify the activity?
actividad.actividadhandle.javascript.-FechaFin=- Fulfillment date
actividad.actividadhandle.javascript.-FechaInicio=- Start date
actividad.actividadhandle.javascript.-FechaVerificacion=- Verificaction date
actividad.actividadhandle.javascript.LaActividadDebeEmpezarDespuesDelDiaDeHoy=The activity must start after today
actividad.actividadhandle.javascript.LaActividadDebeSerVerificadaDespuesDelDiaDeHoy=The activity must be verified after today
actividad.actividadhandle.javascript.LaActividadDebeTerminarDespuesDelDiaDeHoy=The activity must end after today
actividad.actividadhandle.javascript.Laactividadhasidocreada=The activity was added.
actividad.actividadhandle.javascript.LaActividadSeraVerificadaDeseaContinuar=The activity will be marked as verified. Do you want to continue?
actividad.actividadhandle.javascript.Losdatosdelaactividadhansidoactualizados=The activity was updated.
actividad.actividadhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunaactividad=The following items are required
actividad.actividadhandle.javascript.-Nombre=- Name
actividad.actividadhandle.javascript.-Notas=- Notes
actividad.actividadhandle.javascript.-Presupuesto=- Budget
actividad.actividadhandle.javascript.-RazonDeCambioDeFechaDeInicio=- Initial date change motive
actividad.actividadhandle.javascript.-RazonDeCambioDeFechaDeVerificacion=- Verification date change motive
actividad.actividadhandle.javascript.-RazonDeCambioDeFechaFinal=- Final date change motive
actividad.actividadhandle.javascript.-RazonDeCambioDePorcentaje=- Percentage change motive
actividad.actividadhandle.javascript.-RazonDeCambioDePresupuesto=- Budget change motive
actividad.actividadhandle.javascript.-RazonDeCambioDeVerificador=- Verifier change motive
actividad.actividadhandle.javascript.-Responsable=- Leader
actividad.actividadhandle.javascript.-TrabajoPlaneado=- Work planned
actividad.actividadhandle.javascript.-TrabajoRealizado=- Work done
actividad.actividadhandle.javascript.-Verificador=- Verifier
actividad.actividadhandle.MisActividades=My activities
actividad.actividadhandle.ModeloDeNegocios=Business Entity
actividad.actividadhandle.Mostrar=Show
actividad.actividadhandle.Notas=Notes:
actividad.actividadhandle.Porcentajedecumplimiento=Fulfillment percentage:
actividad.actividadhandle.Presupuesto=Budget:
actividad.actividadhandle.Responsable=Responsible:
actividad.actividadhandle.Tipo=Type
actividad.actividadhandle.TrabajoPlaneado=Planned work:
actividad.actividadhandle.TrabajoRealizado=Work done
actividad.actividadhandle.Verificador=Verifier:
actividad.actividadlist.Actividad=Activity
actividad.actividadlist.Borrar=Delete
actividad.actividadlist.Concluida=Concluded
actividad.actividadlist.Creada=Created
actividad.actividadlist.Editar=Edit
actividad.actividadlist.EnProceso=In Process
actividad.actividadlist.Estado=Status
actividad.actividadlist.FechaFin=Final date
actividad.actividadlist.FechaInicio=Start date
actividad.actividadlist.header.CONTROLACTIVIDADES=ACTIVITIES CONTROL
actividad.actividadlist.Nosehaeliminadolaactividad=The activity cannot be removed. Maybe it has pending changes.
actividad.actividadlist.Responsable=Leader
actividad.actividadlist.Sehaeliminadolaactividad=The activity was removed. Maybe it has changes.
actividad.actividadlist.Verificada=Verified
actividad.actividadlist.Verificador=Verifier
actividades.actividadhandle.header.ACTIVIDADES=ACTIVITIES
actividades.actividadhandle.Nombre=Name:
actividadeslistpeticionespresupuesto.NoSeHaRealizadoNingunCambio=There has not been any changes
all.EsteCampoUnicamentePermiteCaracteresAlfanumericos=This field only allows alphanumeric characters.
all.EsteCampoUnicamentePermiteNumeros=This field only allows numbers.
auditoria.acciongenericahandle.Fechadeevaluacion=Evaluation date:
auditoria.auditoriaagenda.boton.Regresar=Return
auditoria.auditoriaagenda.Convoca=Convoque:
auditoria.auditoriaagenda.Cuestionario=Questionnaire:
auditoria.auditoriaagenda.header.AGENDA-=SCHEDULE-
auditoria.auditoriaagenda.Objetivos=Objectives:
auditoria.auditoriaagenda.Observaciones=Remarks:
auditoria.auditoriaagenda.Ubicacion=Department:
auditoria.auditoriaagenda.Vercomentarios=Show comments
auditoria.auditoriahandle.AnticipacionparaNotificardelaAuditoria=Anticipation for the audit report:
auditoria.auditoriahandle.Areas=Process:
auditoria.auditoriahandle.Auditorencargado=Leader Auditor:
auditoria.auditoriahandle.Auditores=Auditors:
auditoria.auditoriahandle.Auditores=Support auditors:
auditoria.auditoriahandle.boton.AgregarComentario=Add Comment
auditoria.auditoriahandle.boton.CancelarAuditoria= Cancel Audit
auditoria.auditoriahandle.boton.Crear=Create
auditoria.auditoriahandle.boton.NoAceptar=Not Accept
auditoria.auditoriahandle.boton.Notificar=Notify
auditoria.auditoriahandle.boton.Regresar=Return
auditoria.auditoriahandle.Clave=ID:
auditoria.auditoriahandle.Comentario=Comment
auditoria.auditoriahandle.Comentarios=Comments
auditoria.auditoriahandle.Cuestionario=Questionnaire:
auditoria.auditoriahandle.Dia= Day(s)
auditoria.auditoriahandle.Encargados=Managers:
auditoria.auditoriahandle.Estatus=Status:
auditoria.auditoriahandle.Facultad=Business Unit:
auditoria.auditoriahandle.Fecha=Date
auditoria.auditoriahandle.Fechafin=Fulfillment date:
auditoria.auditoriahandle.Fechainicio=Start date:
auditoria.auditoriahandle.header.AUDITORIAS=AUDITS
auditoria.auditoriahandle.javascript.-Areas=- Processes.
auditoria.auditoriahandle.javascript.-Auditorencargado=- leader Auditor.
auditoria.auditoriahandle.javascript.Auditorias=Audits
auditoria.auditoriahandle.javascript.Auditorias=Audits
auditoria.auditoriahandle.javascript.Auditorias=Audits
auditoria.auditoriahandle.javascript.Consultar=List
auditoria.auditoriahandle.javascript.Consultar=List
auditoria.auditoriahandle.javascript.Consultar=List
auditoria.auditoriahandle.javascript.Crear=Create
auditoria.auditoriahandle.javascript.-Cuestionario=- Questionnaire.
auditoria.auditoriahandle.javascript.dardealtaunaauditoria=Create Audit.
auditoria.auditoriahandle.javascript.Elcomentariohasidograbado=Comment added
auditoria.auditoriahandle.javascript.Esnecesarioponerunaobservacion=Give a remark.
auditoria.auditoriahandle.javascript.-Fechafin=- Fulfillment date.
auditoria.auditoriahandle.javascript.-Fechainicio=- Start date.
auditoria.auditoriahandle.javascript.Laauditoriahasidoagendada=Audit scheduled
auditoria.auditoriahandle.javascript.Lafechainicialdebesermenoroigualalafechafinal=The start date must be minor than fulfillment date
auditoria.auditoriahandle.javascript.Lanotificaci&oacute;nasidoenviadaviacorreoelectr&oacute;nico=The notification has been sent via e-mail.
auditoria.auditoriahandle.javascript.Lanotificaci\u00f3nasidoenviadaviacorreoelectr\u00f3nico=The notification has been sent by e-mail.
auditoria.auditoriahandle.javascript.Lanotificacionasidoenviadaviacorreoelectronico=The notification has been sent by e-mail.
auditoria.auditoriahandle.javascript.Lanotificacionasidoenviadaviacorreoelectronico=The notification has been sent by e-mail.
auditoria.auditoriahandle.javascript.Losdatosdelaauditoriahansidoactualizados=Audit updated
auditoria.auditoriahandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
auditoria.auditoriahandle.javascript.Lossiguientesdatossonnecesariospara=The following items are necessary:
auditoria.auditoriahandle.javascript.-Objetivos=- Objetives/Goals
auditoria.auditoriahandle.javascript.-Ubicacion=- Department.
auditoria.auditoriahandle.javascript.VerAuditoria=Show Audit
auditoria.auditoriahandle.javascript.VerAuditoria=Show Audits
auditoria.auditoriahandle.javascript.-VerifiquelaHoradeInicioseaMenoralaHoradeFin=- Verify that the start time is less than the end time.
auditoria.auditoriahandle.javascript.VerifiquequelaFechaseaPosterioroIgualaladelDiadeHoy=Verify the date (s) is later than or equal to the day of today.
auditoria.auditoriahandle.Objetivos/Alcance=Objectives/Goals:
auditoria.auditoriahandle.Observaciones=Remarks:
auditoria.auditoriahandle.RazonporlaCuallaAuditoriaseraCancelada=Reason why the audit will be cancelled
auditoria.auditoriahandle.Razonporlaquelaauditoriafuerechazada=The reason for which the audit was rejected.
auditoria.auditoriahandle.SehaCambiadolaAuditoriaCorrectamente= The audit has been cancelled correctly.
auditoria.auditoriahandle.--SELECCIONE--=-- SELECT --
auditoria.auditoriahandle.--TODAS--=-- ALL --
auditoria.auditoriahandle.Ubicacion=Department:
auditoria.auditoriahandle.Usuarios=Users
auditoria.auditorialector.Auditorencargado=Leader Auditor:
auditoria.auditorialector.AuditoriaCancelada=Audit Cancelled
auditoria.auditorialector.AuditoriaPlaneada=Audit Planned
auditoria.auditorialector.Auditorresponsable=Leader Auditor
auditoria.auditorialector.boton.Buscar=Search
auditoria.auditorialector.Clave=ID
auditoria.auditorialector.Est=Status
auditoria.auditorialector.Estado=Closed=Status: Closed
auditoria.auditorialector.EstadoAceptada=Estado: Accepted
auditoria.auditorialector.EstadoCerrada=Status: Closed
auditoria.auditorialector.EstadoCerrada=The actual status is CLOSED.
auditoria.auditorialector.EstadoConfirmada=Status: Confirmed
auditoria.auditorialector.EstadoNotificada=Status: Notified
auditoria.auditorialector.EstadoNotificada=The actual status is NOTIFIED.
auditoria.auditorialector.EstadoProgramada=Status: Programmed
auditoria.auditorialector.EstadoProgramada=The actual status is PROGRAMMED.
auditoria.auditorialector.EstadoRealizada=Status: Completed
auditoria.auditorialector.EstadoRealizada=Status: Done
auditoria.auditorialector.EstadoRealizada=The actual status is DONE.
auditoria.auditorialector.Estatus=Status:
auditoria.auditorialector.FechaF=End date
auditoria.auditorialector.Fechafin=Fulfillment date:
auditoria.auditorialector.FechaI=Start Date
auditoria.auditorialector.Fechainicio=Start date:
auditoria.auditorialector.header.CONSULTADEAUDITORIAS=AUDITS LIST
auditoria.auditorialector.Noseencontraronregistros=There are no results to display.
auditoria.auditorialector.Preg=Questions
auditoria.auditorialector.--SELECCIONE--=-- SELECT --
auditoria.auditorialector.Todas=All
auditoria.auditorialector.Ubicacion=Department:
auditoria.auditorialector.Ver=View
auditoria.auditorialector.Verauditoria=Show Audit
auditoria.auditorialector.Verpreguntas=Show questions
auditoria.auditorialist.Acc=Actions
auditoria.auditorialist.Agregaraccioncorrectiva=Create corrective action
auditoria.auditorialist.Auditorencargado=Leader Auditor
auditoria.auditorialist.AuditoriaCancelada=Audit Cancelled
auditoria.auditorialist.AuditoriaConfirmada=Audit Confirmed
auditoria.auditorialist.AuditoriaPlaneada=Audit Planned
auditoria.auditorialist.AuditoriasEnDondeEsAuditorResponsable=Where you are the responsible:
auditoria.auditorialist.AuditoriasEnDondeNoEsAuditorResponsable=Where you are not the responsible:
auditoria.auditorialist.AuditoriasPlaneadasDondeeselAutor=Where you are the author:
auditoria.auditorialist.AuditoriasPlaneadasDondenoeselAutor=Where you are not the author:
auditoria.auditorialist.Auditoriasplaneadasdondenoeselautor=Where you are not the author:
auditoria.auditorialist.Auditorresponsable=Leader Auditor
auditoria.auditorialist.AutordelaAuditoria=Author of the audit
auditoria.auditorialist.Bor=Delete
auditoria.auditorialist.Borrarauditoria=Delete audit
auditoria.auditorialist.boton.Buscar=Search
auditoria.auditorialist.Clave=ID
auditoria.auditorialist.Departamento= Department
auditoria.auditorialist.Est=Status
auditoria.auditorialist.EstadoAceptada=Status\: Accepted
auditoria.auditorialist.EstadoAuditoriacompleta=The actual status is COMPLETE.
auditoria.auditorialist.EstadoNotificadaAccionMarcarcomorealizada=The actual status is NOTIFIED. Press to set DONE.
auditoria.auditorialist.EstadoProgramadaAccionAutorizar=The actual status is PROGRAMMED. Press to AUTORIZE.
auditoria.auditorialist.EstadoProgramadaAccionNotificar=The actual status is PROGRAMMED. Press to NOTIFY.
auditoria.auditorialist.EstadoRealizadaAccionCerrar=The actual status is DONE. Press to CLOSE.
auditoria.auditorialist.Estatus=Status:
auditoria.auditorialist.FechadeTermino=End Date
auditoria.auditorialist.FechaF=End date
auditoria.auditorialist.Fechafin=End date:
auditoria.auditorialist.FechaI=Start date
auditoria.auditorialist.Fechainicio=Start date:
auditoria.auditorialist.Proceso=Process:
auditoria.auditorialist.header.CONTROLDEAUDITORIAS-USR-=CONTROL OF AUDITS-USR-
auditoria.auditorialist.javascript.Auditorias=Audits
auditoria.auditorialist.javascript.CambiaraelestadodelaauditoriaDeseacontinuar=The audit status will change.\\nDo you want to continue?
auditoria.auditorialist.javascript.Crearauditoria=Create audits
auditoria.auditorialist.javascript.Deseacontinuar=Do you want to continue?
auditoria.auditorialist.javascript.elcuestionariodelaauditoriaDeseahacerlo=Answer the questionnaire . Do you want to answer it?
auditoria.auditorialist.javascript.Elimineesasligasyvuelvaaintentarlo=Delete the references and try again.
auditoria.auditorialist.javascript.estaligadoaotraspartesdentrodelsistema=It is linked to other parts inside the system.
auditoria.auditorialist.javascript.Laauditoriaseraremovidadelsistema,asicomoloscomponentesligadasaella=The audit will be removed from the system, as well as the components linked to it.
auditoria.auditorialist.javascript.Laseleccionnopudosereliminada=The selection couldn\\'t be deleted.
auditoria.auditorialist.javascript.Laseleccionnopuedesereliminada,debidoaque=The selection cannot be eliminated, due to
auditoria.auditorialist.javascript.Listarauditorias=Edit Audits
auditoria.auditorialist.javascript.Losparticipantesenlaauditoriahansidonotificados.=The participants were notified.
auditoria.auditorialist.javascript.Losparticipantesenlaauditoriahansidonotificados=The participants were notified.
auditoria.auditorialist.javascript.LosparticipantesserannotificadosviacorreoelectronicoDeseacontinuar=The participants will be notified by e-mail.\\nDo you want to continue?
auditoria.auditorialist.javascript.Paramarcarlaauditoriacomorealizadadeberallenar=To set the audit as done you need
auditoria.auditorialist.javascript.VerAuditoria=Show Audit
auditoria.auditorialist.Laseleccionhasidoeliminada=The selection was removed.
auditoria.auditorialist.NoSeEncontraronAuditoriasEnDondeEsAuditorResponsable=No audits in which you are the responsible have been found.
auditoria.auditorialist.NoSeEncontraronAuditoriasEnDondeNoEsAuditorResponsable=No audits in which you are not the responsible have been found.
auditoria.auditorialist.Noseencontraronauditoriasplaneadas=No planned audits were found.
auditoria.auditorialist.NoseEncontraronAuditoriasPlaneadas=We found no audits planned.
auditoria.auditorialist.Noseencontraronregistros=There are no results to display.
auditoria.auditorialist.PendientePorAceptar= &nbsp;(Pending by Accept)
auditoria.Auditorialist.Planeadasdondeeselautor=Planned where it is the author:
auditoria.auditorialist.Preg=Questions
auditoria.auditorialist.--SELECCIONE--=-- SELECT --
auditoria.auditorialist.Todas=All
auditoria.auditorialist.TodasAuditorias= All The Audits:
auditoria.auditorialist.Ubicacion=Department:
auditoria.auditorialist.Ver=View
auditoria.auditorialist.Verauditoria=View Audit
auditoria.auditorialist.Verpreguntasdelaauditoria=View questions
auditoria.auditoriaquestionhandle.Borrar=Delete
auditoria.auditoriaquestionhandle.BorrarPregunta=Delete Question
auditoria.auditoriaquestionhandle.boton.Agregar=Create
auditoria.auditoriaquestionhandle.boton.Cancelar=Cancel
auditoria.auditoriaquestionhandle.boton.EditarAuditoria=Edit Audit
auditoria.auditoriaquestionhandle.boton.Guardar=Save
auditoria.auditoriaquestionhandle.Estado=Status:
auditoria.auditoriaquestionhandle.header.CUESTIONARIO/PREGUNTAS=QUESTIONNAIRE/QUESTIONS
auditoria.auditoriaquestionhandle.javascript.Elcuestionarioseacabadardealta=The questionnaire  was created.
auditoria.auditoriaquestionhandle.javascript.LapreguntaseraeliminadadelalistaDeseacontinuar=The question will be removed.\\nDo you want to continue?
auditoria.auditoriaquestionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunapregunta=The following items are necessary to create a question
auditoria.auditoriaquestionhandle.javascript.-Redacciondelapregunta=- Redaction of the question
auditoria.auditoriaquestionhandle.javascript.-Secciondelapregunta=- Section of the question
auditoria.auditoriaquestionhandle.javascript.SevaagenerarunnuevocuestionarioconlaspreguntasqueestanenlalistaDeseacontinuar=A new questionnaire  will be created with the selected questions.\\nDo you want to continue?
auditoria.auditoriaquestionhandle.NoHayPreguntasPorGuardar= There are no questions to save.
auditoria.auditoriaquestionhandle.Pregunta=Question
auditoria.auditoriaquestionhandle.Redacciondelapregunta=Wording of the question:
auditoria.auditoriaquestionhandle.Secciinalaquepertenece=Section to which belongs:
auditoria.auditoriaquestionhandle.Seccion=Section
auditoria.auditoriaquestionhandle.--Seccion--=--Section--
auditoria.auditoriaquestionhandle.Seccionalaquepertenece=Section to which belongs
auditoria.auditoriaquestionhandle.Seccionalaquepertenece=Section to which belongs:
auditoria.auditoriareportegeneral.Catalogo=Catalogue
auditoria.auditoriareportegeneral.chart.aceptada=ACCEPTED
auditoria.auditoriareportegeneral.chart.autorizada=AUTORIZED
auditoria.auditoriareportegeneral.chart.autorizada=AUTORIZED
auditoria.auditoriareportegeneral.chart.autorizadaconavisos=NOTIFIED
auditoria.auditoriareportegeneral.chart.autorizadaconavisos=NOTIFIED
auditoria.auditoriareportegeneral.chart.cancelada= CANCELLED
auditoria.auditoriareportegeneral.chart.cerrada=CLOSED
auditoria.auditoriareportegeneral.chart.cerrada=CLOSED
auditoria.auditoriareportegeneral.chart.confirmada=CONFIRMED
auditoria.auditoriareportegeneral.chart.numerodeauditorias=Number of audits
auditoria.auditoriareportegeneral.chart.programada=PROGRAMMED
auditoria.auditoriareportegeneral.chart.programada=PROGRAMMED
auditoria.auditoriareportegeneral.chart.realizada=DONE
auditoria.auditoriareportegeneral.chart.realizada=DONE
auditoria.auditoriareportegeneral.chart.title=Audits
auditoria.auditoriareportegeneral.chart.title=Audits
auditoria.auditoriareportegeneral.chart.title2=Audits
auditoria.auditoriareportegeneral.Facultad= Business Unit:
auditoria.auditoriareportegeneral.header=AUDITS SUMMARY
auditoria.auditoriareportegeneral.header=Audits summary
auditoria.auditoriareportegeneral.RangodeFechasdelReporte=The report date range:
auditoria.auditoriareportegeneral.REPORTEDEAGENDADEAUDITORIA=AGENDA REPORT OF AUDIT
auditoria.auditoriareportegeneral.ReportedeAgendadeAuditorias=AUDITS AGENDA REPORT.
auditoria.auditoriareportegeneral.REPORTEDERESULTADOSAUDITORIAS=AUDIT RESULTS REPORT
auditoria.auditoriareportegeneral.REPORTESGENERALES=GENERAL REPORTS
auditoria.auditoriareportegeneral.TipoReporte= Report Type:
auditoria.auditoriareportegeneral.TODOS=-- ALL --
auditoria.misauditorias.AreaAuditada=Audited Process
auditoria.misauditorias.Auditorencargado=Auditor in charge
auditoria.misauditorias.AuditoriaCancelada=Audit Cancelled
auditoria.misauditorias.AuditoriaCancelada=Audit Cancelled
auditoria.misauditorias.AuditoriaConfirmada,PresioneparaMarcarcomoRealizada=Confirmed audit. Press to mark as COMPLETED.
auditoria.misauditorias.AuditoriaPlaneada=Audit Planned
auditoria.misauditorias.AuditoriasEnLasQueParticipasComoAuditorEncargado=Where you are Leader Auditor          
auditoria.misauditorias.AuditoriasEnLasQueParticipasComoAuditorParticipante=Where are Support Auditor
auditoria.misauditorias.AuditoriasEnLasQueParticipasComoEncargadoDeArea=Where you are attendand of Process
auditoria.misauditorias.Auditorresponsable=Leader Auditor
auditoria.misauditorias.Clave=Code
auditoria.misauditorias.ElEstadoActualesACEPTADA,PresioneparaCerrar=The current status is ACCEPTED. Press to Close.
auditoria.misauditorias.Est=Status
auditoria.misauditorias.EstadoAceptada,PresioneparaCerrarla=Status: Accepted, Press to Close It.
auditoria.misauditorias.EstadoAceptada=Status: Accepted
auditoria.misauditorias.EstadoCerrada=Status: Closed
auditoria.misauditorias.EstadoConfirmada,PresioneparaMarcarcomoRealizada =Status: Confirmed, Press to mark as completed.
auditoria.misauditorias.EstadoConfirmada=Status: Confirmed
auditoria.misauditorias.EstadoNotificada=Status: Notified
auditoria.misauditorias.EstadoProgramada=Status: Programmed
auditoria.misauditorias.EstadoRealizada=Status: Completed
auditoria.misauditorias.Estatus=Status
auditoria.misauditorias.FechaF=Final Date
auditoria.misauditorias.Fechafin=Ending Date
auditoria.misauditorias.FechaI=Initial Date
auditoria.misauditorias.Fechainicio=Starting Date
auditoria.misauditorias.header.MISAUDITORIAS=MY AUDITS
auditoria.misauditorias.Horadeinicio=Starting Time
auditoria.misauditorias.Horafin=Ending Time
auditoria.misauditorias.NoSeEncontraronRegistros=There are no results to display
auditoria.misauditorias.PendientePorAceptar= &nbsp;(Pending by Accept)
auditoria.misauditorias.Preg=Question
auditoria.misauditorias.PresioneparaAceptarResultados=, Press to accept results.
auditoria.misauditorias.--SELECCIONE--=--SELECT--
auditoria.misauditorias.Tienequeesperaraquelaauditoriasearealizada=You have to wait until the audit is performed.
auditoria.misauditorias.Todas=All
auditoria.misauditorias.Ubicacion=Department
auditoria.misauditorias.Ver=View
auditoriareportegeneral.auditado=Audited
auditoriareportegeneral.auditorencargado=Auditor Leader
auditoriareportegeneral.clausulas=Standard
auditoriareportegeneral.conformidad=Accordance
auditoriareportegeneral.departamento=Department
auditoriareportegeneral.desglosehallazgos=Breakdown of Findings
auditoriareportegeneral.fecha=Date
auditoriareportegeneral.fechafin=Ending Date
auditoriareportegeneral.fechainicio=Initial Date
auditoriareportegeneral.formatoagenda=AUDITS DIARIES
auditoriareportegeneral.hora=Hour
auditoriareportegeneral.noauditoria=Audit Number
auditoriareportegeneral.pregunta=Question
auditoriareportegeneral.proceso=Processes
auditoriareportegeneral.procesos=Processes
auditoriareportegeneral.rangofecha=Data Range
auditoriareportegeneral.reporteresultados=AUDIT RESULTS REPORT
auditoriareportegeneral.respuesta=Answer
auditoriareportegeneral.sistemacalidad=QUALITY SYSTEM
auditoriareportegeneral.subprocesos=Sub-Processes
auditorias.auditoriareportegeneral.DebeEspecificarUnRangoDeFechas= You must specify a date range.
auditorias.misauditorias.auditorResponsable=Leader Auditor
auditorias.misauditorias.clave=ID
auditorias.misauditorias.fechaInicio=Start date
auditorias.misauditorias.fechaTermino=End Date:
auditorias.preguntasobv.Nosehandadodealtaaccionesquenoest\u00e9nrelacionadasapreguntasenestaauditor\u00eda=There are no actions unrelated to questions in this audit
auditorias.preguntasobv.Nosehandadodealtaaccionesquenoestenrelacionadasapreguntasenestaauditoria=Not been registered actions which are not related to this audit questions.
auditorias.preguntasonv.Accionesnorelacionadasapreguntas=Unrelated actions to the questions
auditorias.preguntasonv.Autor=Author
auditorias.preguntasonv.Borrar=Delete
auditorias.preguntasonv.Clave=ID
auditorias.preguntasonv.Estado=Status
auditorias.preguntasonv.Estapreguntanocontieneaccioneslevantadas=This question does not contain actions
auditorias.preguntasonv.Fecha/HoraCreacion=Date/Time of Creation
auditorias.preguntasonv.veracciones=View Actions
boton.Aceptar=Accept
boton.Actualizar=Update
boton.Agregar=Add
boton.Alta=New
boton.Buscar=Search
boton.Cancelar=Cancel
boton.Cargar= Load
boton.Cerrar=Close
boton.Confirmar= Confirm
boton.Consulta=List
boton.Consulta=List
boton.Control=Control
boton.Crear=Create
boton.Editar=Edit
boton.EnviarArchivos=Upload Files
boton.Graficar=Apply filters
boton.Guardar=Save
boton.Imprimir=Print
boton.Imprimir=Print
boton.Limpiar=Clear
boton.LimpiarForma=Clear
boton.Listar=List
boton.MisAcciones=My findings
boton.MisActividades=My Activities
boton.MisAuditorias=My audits
boton.Mostrar=Show
boton.Mover=Move
boton.No=\ \ \ \ No\ \ \ \\ 
boton.Proyectos=Projects
boton.ProyectosCambiosPendientes=Projects with pendings
boton.Refrescar=Refresh
boton.Refrescar=Refresh
boton.Regresar=Back
boton.Si=\ \ \ \ Yes\ \ \ \\\ 
boton.Ver=Back
boton.Ver=View
boton.VerSolicitud=View Request
boton.SaveFiles=Save files
busqueda.busqueda.Buscar = Find
busqueda.busqueda.En = In
busqueda.busqueda=Search
busqueda.busquedaAvanzada.esconder=Hide
busqueda.busquedaAvanzada.mostrar=View
busqueda.busquedaAvanzada=Advanced search
campoFechas.LasFechasNoConcuerdanConLasFechasDeLasActividades=The dates aren\\'t compatible with the Activities dates
campoFechas.LasFechasNoSeEncuentranEnElOrdenCorrecto=The dates aren\\'t in the right order
campoFechas.VerifiqueLasFechas=Verify dates
campoNumeros.DeseaModificarAutomaticamenteElPresupuestoDelProyecto=Do you want to modify the projects budget?
campoNumeros.ElPresupuestoDebeContenerUnicamenteNumeros=The budjet must contain only numbers
campoNumeros.ElPresupuestoDebeSerMayorAlPresupuestoTotalDeActividades=The budget must be greater than the total budget of activdades
campoNumeros.ElPresupuestoNoPuedeExcederMilMillones=The budget must not exceed a billion
campoNumeros.ElPresupuestoNoPuedeSerMenorAlTotalGastado=The budget must greater than the total spent
campoNumeros.ElPresupuestoSeExcedeDe=The budget exceed of
campoNumeros.VerifiqueElCampoDePresupuesto=Verify budget
campoNumeros.VerifiquePuntoDecimal=Verify decimal point.
campoNumeros.VerifiqueRangosDelPresupuesto=Verify budget range
catalogo.lugarAlmacenamiento.Almacena=Generates records that are stored:
combo.--NINGUNO--=-- NONE --
combo.--SELECCIONE--=-- SELECT --
combo.---SINPERFIL---=--- NO PROFILE ---
combo.--TODAS--=-- ALL --
combo.--TODOS--=-- ALL --
common.actionall.Administrador=Administrator
common.actionall.Administrador=Administrator
common.actionall.delsistema=of the system.
common.actionall.delsistema=of the system.
common.actionall.Elprocesofuerealizado=Process successfully completed
common.actionall.Elprocesonofuerealizado=This process could not be completed
common.actionall.Espereunmomentoporfavor=Wait a moment please...
common.actionall.Espereunmomentoporfavor=Wait a moment please...
common.actionall.Exito=Process successfully completed
common.actionall.Falla=This process could not be completed
common.actionall.Falla=This process could not be completed
common.actionall.Laencuestaseguardocorrectamente=The survey was successfully saved.
common.actionall.Lainformacionnopudoseractualizada=The information couldn\'t be updated.
common.actionall.Lainformacionnopudoseractualizada=The information couldn\'t be updated.
common.actionall.Noseobtuvierondatos=Data were not obtained.
common.actionall.Porfavorintentedenuevo=Please try again.
common.actionall.Porfavorintentedenuevo=Please try again.
common.actionall.Sielerrorcontinuaporfavorcontacteal=If the error continuous please contact the
common.actionall.Sielerrorcontinuaporfavorcontacteal=If the error continuous please contact the
common.bottomForm.ElPieDeLaFormaNoPuedeEstarVacio= The foot of the form cannot be empty.
common.bottomForm.NoCuentaConPermisosParaRealizarEstaAccion= Do not have permissions to perform this action.
common.bottomForm.PieDeForma,LadoDerecho= FOOT OF FORM, RIGHT SIDE
common.bottomForm.PieDeForma,LadoIzquierdo= FOOT OF FORM, LEFT SIDE
common.bottomForm.Texto= Text:
common.catalogo.Activo= Active
common.catalogo.CalificacionesCuestionarios= QUALIFICATIONS QUESTIONNARIES
common.catalogo.CATALOGO= CATALOGUE &nbsp;
common.catalogo.CONTROLMASIVO=MASSIVE CONTROL &nbsp;
common.catalogo.Descripcion= Description
common.catalogo.Formas= Form
common.catalogo.FuentedeAcciones= SOURCE OF ACTIONS
common.catalogo.FuentedeQuejas= SOURCE OF COMPLAINTS
common.catalogo.TipoAnalisisQuejas= TYPES OF COMPLAINT ANALYSIS
common.catalogo.Inactivo= Inactive
common.catalogo.LosPiesdelaFormaestanActivos,PresioneParaVerlaVistaPreviaEstandardeEstaForma= The form footer always are active. Click to see the view prior standard this form.
common.catalogo.NombredelCampo= Field Name
common.catalogo.NoseCuentaconPermisosParaVerEsteCatalogo= It does not have permission to view this catalog.
common.catalogo.NosePudoCambiarelEstadodelRegistro= The Status of the registry could not be changed.
common.catalogo.NosePuedeActualizarelRegistro= The registry  could not be updated.
common.catalogo.NosePuedeBorrarelRegistro= Could not delete the registry.
common.catalogo.PiedeFormaDerecho= Form Right Foot
common.catalogo.PiedeFormaIzquierdo= Form Left Foot
common.catalogo.PiedeFormas= FORM FOOTER
common.catalogo.SeActualizoelRegistroCorrectamente= The registry was successfully updated.
common.catalogo.SeBorroelRegistroCorrectamente= Registry  is deleted successfully.
common.catalogo.SeCambioelEstadodelRegistroCorrectamente= Changed the log status successfully.
common.catalogo.TIPOSDEDOCUMENTOS=DOCUMENTO TYPES
common.catalogo.Valor= Value
common.catalogolist.BusquedaEstado=Status
common.catalogolist.BusquedaNombre=Name
common.catalogoslist.Activo= Active
common.catalogoslist.Busqueda= Search
common.catalogoslist.CalificacionesCuestionarios=QUALIFICATIONS QUESTIONNARIES
common.catalogoslist.CATALOGO=CATALOGUE &nbsp;
common.catalogoslist.Descripcion= Description
common.catalogoslist.Estado= Status:
common.catalogoslist.Esteelementonopuedeserdesactivado=This element can not be disabled
common.catalogoslist.FuentedeAcciones=SOURCE OF ACTIONS
common.catalogoslist.FuentedeAuditorias=AUDITS STATUS
common.catalogoslist.FuentedeQuejas=SOURCE OF COMPLAINTS
common.catalogoslist.TipoAnalisisQuejas=TYPES OF COMPLAINT ANALYSIS
common.catalogoslist.Inactivo= Inactive
common.catalogoslist.NoCuentaconPermisosparaVeresteCatalogo= It does not have permission to view this catalog.
common.catalogoslist.Nombre= Field Name
common.catalogoslist.NombredelCampo= Field Name
common.catalogoslist.NosePudoCambiarelEstadodelRegistro= The Status of the registry could not be changed.
common.catalogoslist.NosePuedeActualizarelRegistro= The registry  could not be updated.
common.catalogoslist.NosePuedeBorrarelRegistro,estaSiendoUtilizadoporOtroRecurso= Could not delete the registry, this being used by another resource.
common.catalogoslist.SeActualizoelRegistroCorrectamente= The registry is updated successfully.
common.catalogoslist.SeBorroelRegistroCorrectamente= Registry  is deleted successfully.
common.catalogoslist.SeCambioelEstadodelRegistroCorrectamente= Changed the log status successfully.
common.catalogoslist.TODOS= --- ALL ---
common.catalogoslist.Valor= Value
common.comentariohandle.boton.Crear=Create
common.comentariohandle.boton.Crear=Create
common.comentariohandle.Clavecomentada=Commented id:
common.comentariohandle.Clavecomentada=Commented id:
common.comentariohandle.Detalle=Detail:
common.comentariohandle.Detalle=Detail:
common.comentariohandle.header.COMENTARIOS=COMMENTS
common.comentariohandle.header.COMENTARIOS=COMMENTS
common.comentariohandle.javascript.Crear=Create
common.comentariohandle.javascript.Crear=Create
common.comentariohandle.javascript.Elcomentariohasidoagregado=The comment was added.
common.comentariohandle.javascript.Elcomentariohasidoagregado=The comment was added.
common.comentariohandle.javascript.Laminutahasidoagregada=the minute was added.
common.comentariohandle.javascript.Laminutahasidoagregada=the minute was added.
common.comentariohandle.javascript.Listar=List
common.comentariohandle.javascript.Listar=List
common.comentariohandle.javascript.Necesitahaceralguncomentario=A comment is required
common.comentariohandle.javascript.Necesitahaceralguncomentario=A comment is required
common.comentariohandle.Responsable=Responsible:
common.comentariohandle.Responsable=Responsible:
common.comentariohandle.cancelConfirm=The information will not be saved. Are you sure you want to cancel??
common.comentariohandle.Tipo=Type:
common.comentariohandle.Tipo=Type:
common.comentariolist.Autor=Author
common.comentariolist.Autor=Author
common.comentariolist.Borrar=Delete
common.comentariolist.Borrar=Delete
common.comentariolist.BorrarComentario=Delete Comment
common.comentariolist.boton.Cerrar=Close
common.comentariolist.boton.Cerrar=Close
common.comentariolist.Clave=Code
common.comentariolist.Clave=Code
common.comentariolist.Clavecomentada=Clave comentada:Commented code:
common.comentariolist.Clavecomentada=Commented code
common.comentariolist.Clavecomentada=Commented code
common.comentariolist.Clavecomentada=Commented key:
common.comentariolist.Comentario=Commented:
common.comentariolist.Comentario=Commented:
common.comentariolist.Contestado=Replied
common.comentariolist.Contestado=Replied
common.comentariolist.Detalle=Detail
common.comentariolist.Detalle=Detail
common.comentariolist.Estado=Status
common.comentariolist.Estado=Status
common.comentariolist.Fecha=Date
common.comentariolist.Fecha=Date
common.comentariolist.FechaFin=Final date
common.comentariolist.FechaInicio=Initial date
common.comentariolist.header.CONSULTADECOMENTARIOS=COMMENTS LIST
common.comentariolist.header.CONSULTADECOMENTARIOS=COMMENTS LIST
common.comentariolist.header.CONSULTADECOMENTARIOS-=COMMENTS LIST-
common.comentariolist.header.CONSULTADECOMENTARIOS-=COMMENTS LIST-
common.comentariolist.javascript.Elcomentarioseraeliminadodelsistema=The comment will be removed.
common.comentariolist.javascript.Elcomentarioseraeliminadodelsistema=The comment will be removed.
common.comentariolist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=The selection can\'t be removed,\\nit is referenced from other part of the system.
common.comentariolist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=The selection cannot be removed,\\nit is referenced from other part of the system.
common.comentariolist.Laseleccionhasidoeliminada=The selection was removed.
common.comentariolist.Laseleccionhasidoeliminada=The selection was removed.
common.comentariolist.Leido=Read
common.comentariolist.Leido=Read
common.comentariolist.Noseencontraronregistros=There are no results to display.
common.comentariolist.Noseencontraronregistros=There are no results to display.
common.comentariolist.Nuevo=New
common.comentariolist.Nuevo=New
common.comentariolist.Tipo=Type
common.comentariolist.Tipo=Type
common.comentariolist.Ubicacion=Department
common.comentariolist.VerDetalle=Show Detail
common.input.ALTADETIPOSDEDOCUMENTO=DOCUMENT TYPE
common.input.ALTALUGARALMACENAMIENTO=CREATE SAVE PLACE
common.input.-Catalogo=- Catalogue
common.input.-Descripcion=- Description
common.input.Descripcion= Description:
common.input.ElRegistroSeGuardoCorrectamente=Record saved correctly.
common.input.Faltandellenarlossiguientescampos=Missing fill the following fields:
common.input.FUENTEDEACCION=SOURCE OF ACTION
common.input.FUENTEDEQUEJA=SOURCE OF COMPLAINTS
common.input.Liga=- Liga
common.input.Nombre= Name:
common.input.NoSePuedeGuardarElRegistro,IntenteDeNuevoOContacteAlAdminsitradorDelSistema= Unable to save the registry, try again or contact your system administrator.
common.input.TIPODECALIFICACION=QUALIFICATIONS TYPE.
common.input.-Valor=- Value
common.input.Valor= Value:
common.papelera.Auditoriaenestado=Audit in status
common.papelera.AUDITORIAS=AUDITS
common.papelera.Auditorresponsable=Responsible auditor
common.papelera.Auditorresponsable=Responsible auditor
common.papelera.B\u00fasqueda=Search
common.papelera.Borrar=Delete
common.papelera.Borrar2=Delete
common.papelera.Clave=ID
common.papelera.Cuestionario=Questionnaire
common.papelera.Cuestionario2=Questionnarie
common.papelera.ELIMINADA=REMOVED
common.papelera.Estado=Status
common.papelera.FechadeInicio=Start Date
common.papelera.FechaF=End Date
common.papelera.FechaI=Start Date
common.papelera.LaAuditoriaSer\u00e1Borrada=The audit will be deleted. Are you sure you want to continue?
common.papelera.Mostrarfiltrodebusqueda=Show search filter
common.papelera.Nocuentaconpermisossuficientes=Do not have sufficient permissions.
common.papelera.Ocultarfiltrodebusqueda=The ide search filter
common.papelera.PAPELERADE=PAPER BIN OF
common.papelera.presioneaquipararestaurarelregistro=press here to restore the registry.
common.papelera.Seencuentraenlaprimerahoja=It is located on the first sheet.
common.papelera.Seencuentraenlaultimahoja=It is located on the last sheet.
common.papelera.--SELECCIONE--=-- SELECT --
common.papelera.SINPERMISOS=WITHOUT PERMISSIONS
common.papelera.Ver=View
common.papelera.Ver2=View
common.pendienteshandle.Actualizarpendientes=Update tasks
common.pendienteshandle.Actualizarpendientes=Update tasks
common.pendienteshandle.Ayuda=Help
common.pendienteshandle.Cerrarsesion=Exit
common.pendienteshandle.Cerrarsesion=Logout
common.pendienteshandle.Checarpornuevospendientes=Check for new tasks.
common.pendienteshandle.Checarpornuevospendientes=Check for new tasks.
common.pendienteshandle.MiCuenta=My Account
common.pendienteshandle.Notienespendientes=No new tasks.
common.pendienteshandle.Notienespendientes=No new tasks.
common.pendienteshandle.Tienespendientes=You have new tasks
common.pendienteshandle.Tienespendientes=You have new tasks
common.top.Acciones=Actions
common.top.Acciones=Actions
common.top.Auditorias=Audits
common.top.Auditorias=Audits
common.top.Configuracion=Configuration
common.top.Configuracion=Configuration
common.top.Documentos=Documents
common.top.Documentos=Documents
common.top.imagespath=header/english
common.top.imagespath=header/english
common.top.Proyectos=Projects
common.top.Ustedtieneunatareapendienteenlaformaactual.PresioneelbotonCancelaroRegresarparapoderrealizarotraaccion=You have a pending task in the current form. \\nPress the button "Cancel" or "Return" to perform another action.
configuracion.ACTIVO,presioneparamarcarcomoINACTIVO=ACTIVE, press to set as INACTIVE
configuracion.areahandle.boton.Actualizar=Update
configuracion.areahandle.boton.AgregarArea=Create Process
configuracion.areahandle.boton.Cancelar=Cancel
configuracion.areahandle.boton.Cancelar=Cancel
configuracion.areahandle.boton.LimpiarForma=Clear Form
configuracion.areahandle.Clave=ID:
configuracion.areahandle.--Encargado--=--Leader--
configuracion.areahandle.header.AREAS=PROCESS
configuracion.areahandle.javascript.Elareahasidodadadealta=Process was added.
configuracion.areahandle.javascript.-Encargado=- User in Charge
configuracion.areahandle.javascript.LosDatosdelaareahansidoactualizados=The process data has been updated.
configuracion.areahandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
configuracion.areahandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunaarea=The following items are necessary:
configuracion.areahandle.javascript.-Titulo=- Title
configuracion.areahandle.javascript.-Ubicacion=- Department to which they belong
configuracion.areahandle.Titulo=Title:
configuracion.areahandle.--Ubicacion--=--Department--
configuracion.areahandle.Ubicacionalaquepertenece=Department to which they belong:
configuracion.areahandle.Ubicacionalaquepertenece=Department:
configuracion.areahandle.UsuarioEncargado=User in Charge:
configuracion.arealist.Borrar=Delete
configuracion.arealist.BorrarArea=Delete Process
configuracion.arealist.Busquedaporubicacion=Search by Department:
configuracion.arealist.Editar=Edit
configuracion.arealist.EditarArea=Edit Process
configuracion.arealist.Encargado=Leader
configuracion.arealist.header.CONTROLDEAREAS=PROCESS CONTROL
configuracion.arealist.javascript.Elareaseraborradadelsistema=The process will be removed from the system!
configuracion.arealist.Noseeliminolaarea=The process was not removed
configuracion.arealist.Noseencontraronregistros=There are no results to display.
configuracion.arealist.Sehaeliminadolaarea=The process was removed
configuracion.arealist.Seleccioneunaubicacion=Select department
configuracion.arealist.Titulo=Title
configuracion.arealist.--Todas--=--ALL--
configuracion.arealist.Ubicacion=Department
configuracion.botoneditar=Edit
configuracion.carrerahandle.Clave=ID:
configuracion.carrerahandle.-Encargado=- Manager
configuracion.carrerahandle.Encargado=--Chief--
configuracion.carrerahandle.LaCarreraHaSidoDadaDeAlta=The area has been added
configuracion.carrerahandle.LosDatoseLaCarreraHanSidoActualizados=The data from the area have been updated
configuracion.carrerahandle.LosSiguientesCampoSonNecesarios= The following fields are required
configuracion.carrerahandle.-Titulo=- Title
configuracion.carrerahandle.Titulo=Title:
configuracion.carrerahandle.-Ubicacion=- Location
configuracion.carrerahandle.Ubicacion=--Departament--
configuracion.carrerahandle.UbicacionALaQuePertenece=Working Departament:
configuracion.carrerahandle.UNIDADES=UNITS
configuracion.carrerahandle.UsuarioEncargado=Chief User:
configuracion.carreralist.Borrar=Delete
configuracion.carreralist.BusquedaPorUbicacion=Search by location:
configuracion.carreralist.CONTROLDECARRERAS=AREAS CONTROL
configuracion.carreralist.CONTROLDEUSUARIOS=USERS CONTROL
configuracion.carreralist.Editar=Edit
configuracion.carreralist.Encargado=Manager
configuracion.carreralist.Encargado=Manager:
configuracion.carreralist.LaCarreraSeraBorradaDelSistema=The Area will be deleted from the system
configuracion.carreralist.NoSeEliminoLaCarrera=The Area can not be deleted
configuracion.carreralist.NoSeEncontraronRegistros= There are no results to display
configuracion.carreralist.SeHaEliminadoLaCarrera=The Area has been deleted
configuracion.carreralist.Titulo=Title
configuracion.carreralist.Titulo=Title:
configuracion.carreralist.--Todas--=-- All --
configuracion.carreralist.Ubicacion=Location
configuracion.catalogohandle.Calificaciones=Score
configuracion.catalogohandle.Descripcion=Description:
configuracion.catalogohandle.Fuentes=Action Sources
configuracion.catalogohandle.javascript.-Descripcion=-Description
configuracion.catalogohandle.javascript.Elelementohasidodadodealta=The element has been added.
configuracion.catalogohandle.javascript.LosDatosdelelementohansidoactualizados=Transaction successful.
configuracion.catalogohandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunelementoenuncatalogo=The following items are necessary:
configuracion.catalogohandle.javascript.-Tipo=-Type
configuracion.catalogohandle.javascript.-Titulo=-Title
configuracion.catalogohandle.javascript.-Valor=-Value
configuracion.catalogohandle.NombreCatalogo=Name:
configuracion.catalogohandle.Tipo=Type:
configuracion.catalogohandle.Titulo=Title:
configuracion.catalogohandle.Valor=Value:
configuracion.catalogolist.Borrar=Delete
configuracion.catalogolist.BorrarCatalogo=Borrar Cat\u00e1logo
configuracion.catalogolist.Descripcion=Description
configuracion.catalogolist.Editar=Edit
configuracion.catalogolist.EditarCatalogo=Edit Catalogue
configuracion.catalogolist.header.CONTROLDECATALOGOS=\ CONTROL OF CATALOGUES
configuracion.catalogolist.javascript.Elelementoseraborradodelsistema=The element has been removed. Do you want to continue?
configuracion.catalogolist.Nosehaeliminadoelelemento=The element was not removed.
configuracion.catalogolist.Sehaeliminadoelelemento=The element was removed.
configuracion.catalogolist.Titulo=Title
configuracion.catalogolist.Valor=Value
configuracion.catologohandle.header.CATALOGOS=CATALOGUES
configuracion.configuracionhandle.AsuntoCorreo=Email Subject:
configuracion.configuracionhandle.Autordeldocumento=Author of Document:
configuracion.configuracionhandle.AutorizantesenlaSecuencia=Authorizing Sequence:
configuracion.configuracionhandle.boton.Editar=Edit
configuracion.configuracionhandle.cambiar=Change
configuracion.configuracionhandle.ClaveServidorCorreo=Password of Email Server:
configuracion.configuracionhandle.colordelsistema=Color of system:
configuracion.configuracionhandle.Configuraci\u00f3ndeMaildeDifusi\u00f3ndeDocumentos=Mail Settings Document Dissemination:
configuracion.configuracionhandle.ConversionDocumentos=Export office  documents to PDF
configuracion.configuracionhandle.CorreoAdmin=Administrator\u00b4s e-mail:
configuracion.configuracionhandle.CuentaServidorCorreo=Account of Email Server:
configuracion.configuracionhandle.-DBPWD=- Database Password
configuracion.configuracionhandle.DBPWD=Database Password:
configuracion.configuracionhandle.-DBSERVER=- URL Database
configuracion.configuracionhandle.DBSERVER=URL DataBase\:
configuracion.configuracionhandle.-DBUSER=- Database User
configuracion.configuracionhandle.DBUSER=Database User:
configuracion.configuracionhandle.dias=day(s)
configuracion.configuracionhandle.Editar=Edit
configuracion.configuracionhandle.Encargadodelmodulo=Module Manager:
configuracion.configuracionhandle.-EnvioCorreos-=-- Send emails--
configuracion.configuracionhandle.EnvioCorreos=Send e-mails:
configuracion.configuracionhandle.Idioma=Language:
configuracion.configuracionhandle.javascript.-AsuntoCorreo=- Email Subject
configuracion.configuracionhandle.javascript.-ClaveServidorCorreo=- Email Server Password
configuracion.configuracionhandle.javascript.-CorreoAdmin=- Administrator e-mail
configuracion.configuracionhandle.javascript.-CuentaServidorCorreo=- Email Server Account
configuracion.configuracionhandle.javascript.-EnvioCorreos=- Send emails
configuracion.configuracionhandle.javascript.-Idioma=- Language
configuracion.configuracionhandle.javascript.-IdSistema=- ID System
configuracion.configuracionhandle.javascript.LaConfiguracionHaSidoActualizada=Transaction successful. For changes take effect sign in again. Do you want to do it?
configuracion.configuracionhandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
configuracion.configuracionhandle.javascript.-ServidorCorreo=- Email Server
configuracion.configuracionhandle.javascript.-VigenciaDocumentos=- Documents valid for:
configuracion.configuracionhandle.LlaveEncriptacionRC4=Encryption key RC4:
configuracion.configuracionhandle.MensajedeBienvenida=Welcome Message:
configuracion.configuracionhandle.meses=\ \ \ months
configuracion.configuracionhandle.No=No
configuracion.configuracionhandle.NumeroRegistrosBusqueda=Registers number on search
configuracion.configuracionhandle.-NumeroRegistrosBusquedas=- Registers number on search
configuracion.configuracionhandle.PDFViewer=Use PDF viewer
configuracion.configuracionhandle.Permisodeprocesosenlacarpeta=Permit the process in the folder:
configuracion.configuracionhandle.Permisodeusuarionombradoenlacarpeta=User permission on the folder named:
configuracion.configuracionhandle.ServidorCorreo=Email Server:
configuracion.configuracionhandle.Si=Yes
configuracion.configuracionhandle.-SiteURL=- Site URL
configuracion.configuracionhandle.SiteURL=Site URL:
configuracion.configuracionhandle.-StringSystemID=- System ID
configuracion.configuracionhandle.StringSystemID=System ID:
configuracion.configuracionhandle.-UploadFolderDocumentos=- DOCUMENT UPLOAD_FOLDER
configuracion.configuracionhandle.UploadFolderDocumentos=DOCUMENT UPLOAD_FOLDER\:
configuracion.configuracionhandle.-UploadFolderSolicitudes=- REQUESTS UPLOAD_FOLDER
configuracion.configuracionhandle.UploadFolderSolicitudes=REQUESTS UPLOAD_FOLDER\:
configuracion.configuracionhandle.VidadeLigadeCorreos=Life of Link Post:
configuracion.configuracionhandle.VigenciaDocumentos=Documents valid for:
configuracion.configuracionreportegeneral.chart.title.AreasPorUbicacion=Processes by department
configuracion.configuracionreportegeneral.chart.title.UsuariosPorUbicacion=Users by department
configuracion.configuracionreportegeneral.chart.title=Number of Processes per Department
configuracion.configuracionreportegeneral.chart.title=Number of Processes per Location
configuracion.configuracionreportegeneral.header=CONFIGURATION SUMMARY
configuracion.configuracionreportegeneral.numerodeareas=Number of processes
configuracion.configuracionreportegeneral.NumeroDeUsuarios=Number of users
configuracion.editpassword.ClavedeAccesoActual=Current password:
configuracion.editpassword.ClavedeAccesoNueva=New password:
configuracion.editpassword.ConfirmeClavedeAccesoNueva=Confirm New Password:
configuracion.editpassword.header.EDITARCLAVEACCES0=EDIT PASSWORD
configuracion.editpassword.javascript.-ClaveAccesoActual=-Current Access Account
configuracion.editpassword.javascript.-ClaveAccesoNueva=-New Access Account
configuracion.editpassword.javascript.-ConfirmacionClaveAccesoNueva=-Confirm New Access Account
configuracion.editpassword.javascript.-CuentadeUsuario=-User Account
configuracion.editpassword.javascript.LaClaveDeAccesoNoEsCorrecta=The Current Access Account is not correct.
configuracion.editpassword.javascript.LasClavesDeAccesoNuevasNoCoinciden=Access codes does not match. Please try again
configuracion.edituser.header.EDITARCUENTA=My Account
configuracion.edituser.javascript.-Cuentadecorreo=- E-mail Account
configuracion.edituser.javascript.-Cuentadecorreovalida=- Valid E-mail Account
configuracion.edituser.javascript.-CuentadeUsuario=- User Account
configuracion.edituser.javascript.LaClaveDeAccesohasidoactualizada=The Access Account was updated.
configuracion.edituser.javascript.Laclavedelusuariohasidoactualizada=Transaction successful. For changes take effect sign in again. Do you want to do it?
configuracion.edituser.javascript.LosDatosdelusuariohansidoactualizados=Transaction successful.
configuracion.edituser.javascript.Lossiguientesdatossonnecesarios=The following items are required:
configuracion.edituser.javascript.-NombreCompleto=- Full Name
configuracion.facultadhandle.BorrarFacultad =The business unit will be deleted from the system
configuracion.facultadhandle.Clave=ID:
configuracion.facultadhandle.CONTROLFACULTADES = BUSINESS UNITS CONTROL
configuracion.facultadhandle.DatosFacultadActualizados =The data of the business unit have been updated
configuracion.facultadhandle.DatosFacultadAlta =The business unit has been added
configuracion.facultadhandle.DatosFacultadNecesarios =The following data are necessary:
configuracion.facultadhandle.-Encargado=- Chief
configuracion.facultadhandle.Encargado=--Chief--
configuracion.facultadhandle.FacultadBorrar =Delete
configuracion.facultadhandle.FacultadEditar =Edit
configuracion.facultadhandle.FacultadEliminada =The business unit has been deleted
configuracion.facultadhandle.FacultadEncargado =Responsible
configuracion.facultadhandle.FACULTADES =BUSINESS UNITS
configuracion.facultadhandle.FacultadID =ID
configuracion.facultadhandle.FacultadNoEliminada =The business unit has not been removed, this may be due to having declared areas
configuracion.facultadhandle.FacultadTitulo =Title
configuracion.facultadhandle.-Titulo=- Title
configuracion.facultadhandle.Titulo=Title:
configuracion.facultadhandle.UsuarioEncargado=Chief User:
configuracion.INACTIVO,presioneparamarcarcomoACTIVO=INACTIVE, press to set as ACTIVE.
configuracion.invitadohandle.Correo=E-mail:
configuracion.invitadohandle.header.INVITADOS=Invited
configuracion.invitadohandle.javascript.Agregar=Add
configuracion.invitadohandle.javascript.-Correo=- E-mail
configuracion.invitadohandle.javascript.-Cuentadecorreovalida=- Valid e-mail account
configuracion.invitadohandle.javascript.-Descripcion=- Description
configuracion.invitadohandle.javascript.Elinvitadohasidodadodealta=The guest was added.
configuracion.invitadohandle.javascript.Listar=List
configuracion.invitadohandle.javascript.LosDatosdelinvitadohansidoactualizados=The guest data have been updated
configuracion.invitadohandle.javascript.Lossiguientesdatossonnecesarios=The following items are necessary:
configuracion.invitadohandle.javascript.Lossiguientesdatossonnecesariosparadardealtaauninvitado=The following items are necessary:
configuracion.invitadohandle.javascript.-Nombre=- Name
configuracion.invitadohandle.javascript.-Titulo=- Title
configuracion.invitadohandle.Nombre=Name:
configuracion.invitadolist.ACTIVO=Active
configuracion.invitadolist.Borrar=Delete
configuracion.invitadolist.BorrarInvitado=Delete Guest
configuracion.invitadolist.Correo=E-mail
configuracion.invitadolist.Correo2=E-mail
configuracion.invitadolist.Correo2=E-mail
configuracion.invitadolist.Editar=Edit
configuracion.invitadolist.EditarInvitado=Edit Guest
configuracion.invitadolist.Estado= Status
configuracion.invitadolist.header.CONTROLDEINVITADOS=GUESTS CONTROL 
configuracion.invitadolist.javascript.Elinvitadoseraborradodelsistema=The guest will be removed from the system!
configuracion.invitadolist.Nombre=Name
configuracion.invitadolist.Nombre2=Name
configuracion.invitadolist.Nosehaeliminadoelinvitado=The guest was not removed
configuracion.invitadolist.Sehaeliminadoelinvitado=The guest was removed
configuracion.LISTADEPERFILES=PROFILES LIST
configuracion.modneghandle.Descripcion=Description
configuracion.modneghandle.header.ALTAMODELODENEGOCIOS=NEW BUSINESS ENTITY
configuracion.modneghandle.javascript.-Descripcion=-Description
configuracion.modneghandle.javascript.ElModeloDeNegociosHaSidoDadoDeAlta=The Business Model has been added
configuracion.modneghandle.javascript.LosDatosDelModeloDeNegociosoHanSidoActualizado=The data Business Model has been updated
configuracion.modneghandle.javascript.LosDatosDelModeloDeNegociosoHanSidoActualizados=The business model data have been updated
configuracion.modneghandle.javascript.LosSiguientesDatosSonNecesarios=The following data is necesary:
configuracion.modneghandle.javascript.-Titulo=-Title
configuracion.modneghandle.Titulo=Title
configuracion.modneglist.Borrar=Delete
configuracion.modneglist.Editar=Edit
configuracion.modneglist.header.CONTROLDEMODELODENEGIOCIOS=BUSINESS ENTITIES EDITION
configuracion.modneglist.javascript.ElModeloDeNegociosSeraBorradoDelSistema=The Business Entity will be Deleted
configuracion.modneglist.NoSeEliminoElModeloDeNegocios=The Business Entity could not be Deleted
configuracion.modneglist.NoSeEncontraronRegistros=There are no results to display
configuracion.modneglist.SeHaEliminadoElModeloDeNegocios=The Business Entity has been Deleted
configuracion.modneglist.Titulo=Title
configuracion.objetohandle.Descripcion=Description
configuracion.objetohandle.header.ALTAOBJETOS=NEW OBJECT
configuracion.objetohandle.javascript.-Descripcion=-Description
configuracion.objetohandle.javascript.Elobjetohasidodadadealta=The Object has been added
configuracion.objetohandle.javascript.LosDatosdelobjetohansidoactualizados=The Objects Data has been Updated
configuracion.objetohandle.javascript.LosSiguientesDatosSonNecesarios=The following data is necesary\:
configuracion.objetohandle.javascript.-TipoDeObjeto=-Type
configuracion.objetohandle.javascript.-Titulo=-Title
configuracion.objetohandle.Tipo=Type
configuracion.objetohandle.Titulo=Title
configuracion.objetolist.Borrar=Delete
configuracion.objetolist.BorrarObjeto=Delete Objet
configuracion.objetolist.Editar=Edit
configuracion.objetolist.header.CONTROLDEOBJETOS=OBJECTS EDITION
configuracion.objetolist.javascript.ElObjetoSeraBorradoDelSistema=The Object will be permanently deleted
configuracion.objetolist.NoSeEliminoElObjeto=The Object was not deleted
configuracion.objetolist.NoSeEncontraronRegistros=There are no results to display
configuracion.objetolist.SeHaEliminadoElObjeto=The Object has been deleted
configuracion.objetolist.Tipo=Type
configuracion.objetolist.Titulo=Title
configuracion.perfileshandle.Alta=New
configuracion.perfileshandle.Control=Control
configuracion.perfileshandle.ElPerfilASidoActualizadoCorrectamente=The Profile has been updated successfully
configuracion.perfileshandle.ElPerfilASidoEliminadoCorrectamente=The Profile has been removed successfully
configuracion.perfileshandle.ElPerfilASidoGuardadoCorrectamente=The Profile has been saved successfully
configuracion.perfileshandle.ListaDePerfiles=Profiles List
configuracion.perfileshandle.LosSiguientesCamposSonNecesarios=The following fields are required:
configuracion.perfileshandle.PERFILES=PROFILES
configuracion.perfileshandle.PerfilesDeUsuario=User Profiles
configuracion.perfileshandle.PermiteRegistroDeUsuarios=Allows user registration:
configuracion.perfileshandle.TieneQueSeleccionarAlMenosUnPrivilegio=You must select at least one privilege
configuracion.perfileshandle.Titulo=- Title
configuracion.perfileshandle.Titulo1= Title
configuracion.perfileslist.Activo,PresioneParaMarcarComoInactivo= Active, press to mark as inactive.
configuracion.perfileslist.Busqueda= Search
configuracion.perfileslist.Estado=Status:
configuracion.perfileslist.Inactivo,PresioneParaMarcarComoActivo=Inactive, press to mark as active.
configuracion.perfileslist.INACTIVO,presioneparamarcarcomoACTIVO=INACTIVE, press to set as ACTIVE
configuracion.perfileslist.LISTADEPERFILES=LIST OF PROFILES
configuracion.perfileslist.Nombre=Name
configuracion.perfileslist.NoSePudoCambiarElEstadoDelPerfil= The status of the profile could not be changed.
configuracion.perfileslist.SeCambioElEstadoDelPerfil= The status of the profile has changed.
configuracion.perfileslist.Titulo=Title
configuracion.perfileslist.---TODOS---= --- ALL ---
configuracion.pieformalist.Forma=Form
configuracion.pieformalist.FORMAS=FORMS
configuracion.pieformalist.PiedeformaDerecho=Form Rigth Foot
configuracion.pieformalist.PiedeformaIzquierdo=Form Left Foot
configuracion.plantillahandle.header.PLANTILLAS=TEMPLATES
configuracion.plantillahandle.Laplantillahasidocreada=The template has been created
configuracion.plantillahandle.Losdatosdelaplantillahansidoactualizados=Template data has been updated 
configuracion.plantillahandle.NumerodePlantilla=Template number
configuracion.plantillahandle.PlantillaenEdicion=New Template
configuracion.plantillahandle.tituloplntilla=Template title
configuracion.plantillashandle.Agregar/Quitar= Add/Remove
configuracion.plantillashandle.-CampodeDias= - Field day(s)
configuracion.plantillashandle.-DebeIncluirAlMenosUnUsuarioEnLaPlantilladeSecuencia= - It must contain at least one user in the sequence template.
configuracion.plantillashandle.DebeLlenarLosSiguientesCampos= You must complete the following fields:
configuracion.plantillashandle.Dias= Day(s)
configuracion.plantillashandle.DiasParaAtenderSecuencia= Days to respond sequence
configuracion.plantillashandle.NoSeHanAgregadoUsuariosAlaPlantillaDeSecuencia= No users were added to the sequence template.
configuracion.plantillashandle.Orden= Order
configuracion.plantillashandle.-Titulo= - Title.
configuracion.plantillashandle.Usuario= User
configuracion.questionhandle.Estado\:=Status:
configuracion.questionhandle.header.PREGUNTAS=QUESTIONS
configuracion.questionhandle.javascript.Lapreguntahasidodadadealta=The questions have been added.
configuracion.questionhandle.javascript.LosDatosdelapreguntahansidoactualizados=The questions data has been updated.
configuracion.questionhandle.javascript.Lossiguientesdatossonnecesarios=The following items are necessary:
configuracion.questionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaunapregunta=The following items are necessary:
configuracion.questionhandle.javascript.-Redacciondelapregunta=- Question wording
configuracion.questionhandle.javascript.-Seccionalaquepertenece=- Section to which belongs
configuracion.questionhandle.javascript.-Secciondelapregunta=- Section of the Question 
configuracion.questionhandle.Redacciondelapregunta=Question wording:
configuracion.questionhandle.Redacciondelapregunta=Question wording:
configuracion.questionhandle.--Seccion--=--Section--
configuracion.questionhandle.Seccionalaquepertenece=Section to which belongs:
configuracion.questionlist.ACTIVA=The actual status is ACTIVE.
configuracion.questionlist.Borrar=Delete
configuracion.questionlist.Busquedaporestado=Search by status:
configuracion.questionlist.Busquedaporseccion=Search by section:
configuracion.questionlist.CANCELADA=The actual status is CANCELLED.
configuracion.questionlist.Editar=Edit
configuracion.questionlist.EditarPregunta=Edit question
configuracion.questionlist.Estado=Status
configuracion.questionlist.header.CONTROLDEPREGUNTAS=QUESTIONS CONTROL
configuracion.questionlist.javascript.EstaSegurodeQuererBorrarLaPregunta=Are you sure you want to delete the question?
configuracion.questionlist.javascript.Estasegurodequerercambiarelestadodelapregunta=Are you sure you want to change the status of the question?
configuracion.questionlist.Noseencontraronregistros=There are no results to display.
configuracion.questionlist.Nosehaeliminadolapregunta=The question has not been removed
configuracion.questionlist.Redaccion=Wording
configuracion.questionlist.Seccion=Section
configuracion.questionlist.Seccion2=Section
configuracion.questionlist.Sehaeliminadolapregunta=The question has been removed
configuracion.questionlist.Todas=-- ALL --
configuracion.registrolist.FechaDeEntrada=Login Date
configuracion.registrolist.FechaDeSalida=Logoff Date
configuracion.registrolist.FechasEntre=Dates between
configuracion.registrolist.header.REGISTRODEACCESODEUSUARIOS=USER ACCESS LOG
configuracion.registrolist.NoRegistrada=Not Registered
configuracion.registrolist.NoSeEncuentranRegistrosPorMostrar=The are no logs to show.
configuracion.registrolist.Usuario=User
configuracion.registrolist.Y=and
configuracion.reositoriolist.Borrar=Delete
configuracion.repositoriohandle.boton.Actualizar=Update
configuracion.repositoriohandle.boton.Cancelar=Cancel
configuracion.repositoriohandle.Clave=ID:
configuracion.repositoriohandle.javascript.-Descripcion=- Description
configuracion.repositoriohandle.javascript.-Descripcion=-Description
configuracion.repositoriohandle.javascript.Elrepositoriohasidodadodealta=The repository was added.
configuracion.repositoriohandle.javascript.LimiteRepositorios=The action cannot be made.Only can be 10 repositories.
configuracion.repositoriohandle.javascript.LosDatosdelrepositoriohansidoactualizados=The repository data have been updated.
configuracion.repositoriohandle.javascript.Lossiguientesdatossonnecesarios=The following itmes are necessary:
configuracion.repositoriohandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunrepositorio=The following items are required:
configuracion.repositoriohandle.javascript.-Titulo=- Title
configuracion.repositoriohandle.javascript.-Titulo=-Title
configuracion.repositoriolist.Borrar=Delete
configuracion.repositoriolist.BorrarRepositorio=Delete Repositories
configuracion.repositoriolist.CarpetasRelacionadas=The repository cannot be removed because it has folder inside.
configuracion.repositoriolist.DocumentosRelacionados=The repository cannot be removed because it has some documents inside.
configuracion.repositoriolist.Editar=Edit
configuracion.repositoriolist.EditarRepositorio=Edit Repositories
configuracion.repositoriolist.header.CONTROLDEREPOSITORIOS=REPOSITORIES CONTROL 
configuracion.repositoriolist.javascript.confirmacionBorradoRepositorio=The repository will be removed. Do you want to continue?
configuracion.repositoriolist.Nosehaeliminadoelrepositorio=The repository cannot be removed.
configuracion.repositoriolist.Sehaeliminadoelrepositorio=The repository was removed.
configuracion.repositoriolist.Titulo=Title
configuracion.repositorioolist.Nosehaeliminadoelrepositorio.=The repository cannot be removed.
configuracion.respaldoshandle.Clave=ID
configuracion.respaldoshandle.Descargadebasededatos=Database Download:
configuracion.respaldoshandle.Descargadedocumentos=Documents Download:
configuracion.respaldoshandle.Fechadelrespaldo=Backup Date:
configuracion.respaldoshandle.Nohayrespaldos=There_are_no_backups.
configuracion.respaldoshandle.Presioneelbotonparagenerarunrespaldo=Press the button to generate a backup
configuracion.respaldoshandle.Respaldar=Backup
configuracion.respaldoshandle.Respaldo=Backup
configuracion.respaldoshandle.RESPALDOS=BACKUPS
configuracion.respaldoshandle.Titulo=Title:
configuracion.rubro.Noseencontraronregistros=There are no results to display.
configuracion.rubrohandle.Descripcion=Description: 
configuracion.rubrohandle.header.RUBROS=BUDGET TYPE
configuracion.rubrohandle.javascript.ActualizacionRubro=Concept data has been updated
configuracion.rubrohandle.javascript.-Descripcion=- Description
configuracion.rubrohandle.javascript.Elrubrohasidodadadealta=Budget type has been added.
configuracion.rubrohandle.javascript.-Nombre=- Name
configuracion.rubrohandle.Nombre=Name\: 
configuracion.rubrolist.Borrar=Delete
configuracion.rubrolist.BorrarRubro=Delete Budget Type
configuracion.rubrolist.Descripcion=Description
configuracion.rubrolist.Editar=Edit
configuracion.rubrolist.header.CONTROLDERUBROS=BUDGET TYPE EDIT 
configuracion.rubrolist.javascript.Elrubroseraborrododelsistema=The budget type will be removed\!
configuracion.rubrolist.Nombre=Name
configuracion.rubrolist.Noseeliminoelrubro=The budget type couldn\\'t be removed
configuracion.rubrolist.Sehaeliminadoelrubro=The budget type has been removed
configuracion.seccionhandle.Clave=ID:
configuracion.seccionhandle.Consejosocomentarios=Points of the Standard:
configuracion.seccionhandle.Documentosdereferencia=Reference document
configuracion.seccionhandle.header.SECCIONES=SECTIONS
configuracion.seccionhandle.javascript.Laseccionhasidodadadealta=The section has been added.
configuracion.seccionhandle.javascript.LosDatosdelaseccionhansidoactualizados=The subprocess data have been updated.
configuracion.seccionhandle.javascript.LosDatosdelaseccionhansidoactualizados=Transaction successful.
configuracion.seccionhandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
configuracion.seccionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunaseccion=The following items are necessary:
configuracion.seccionhandle.javascript.-Numero=- Number
configuracion.seccionhandle.javascript.-Titulo=- Title
configuracion.seccionhandle.nombre=Name:
configuracion.seccionhandle.nombre=Name:
configuracion.seccionhandle.Numero=Number:
configuracion.seccionhandle.Numero=Number:
configuracion.seccionhandle.Titulo=Title:
configuracion.seccionlist.Borrar=Delete
configuracion.seccionlist.BorrarSeccion=Delete Section
configuracion.seccionlist.Editar=Edit
configuracion.seccionlist.EditarSeccion=Edit Section
configuracion.seccionlist.header.CONTROLDESECCIONES=SECTIONS CONTROL
configuracion.seccionlist.javascript.Laseccionseraborradadelsistema=The section will be removed from the system!
configuracion.seccionlist.NosehaeliminadolaseccionEstosepuededeberaqueauntengatengapreguntasdeclaradas=The section wasn't removed. This may be due to still having declared questions or belongs to any questionnaire.
configuracion.seccionlist.Numero=Number
configuracion.seccionlist.Sehaeliminadolaseccion=The section has been removed
configuracion.seccionlist.Titulo=Title
configuracion.tipohandle.boton.Actualizar=Update
configuracion.tipohandle.boton.Cancelar=Cancel
configuracion.tipohandle.boton.CrearTipo=Create Type
configuracion.tipohandle.boton.LimpiarForma=Clear
configuracion.tipohandle.Descripcion=Description:
configuracion.tipohandle.header.ALTATIPO=NEW TYPE
configuracion.tipohandle.header.TIPOS=TYPES
configuracion.tipohandle.javascript.Crear=Create
configuracion.tipohandle.javascript.-Descripcion=-Description
configuracion.tipohandle.javascript.ElTipoHaSidoDadoDeAlta=The Type has been Added
configuracion.tipohandle.javascript.Eltiposeleccionadohasidoactualizado=The selected type has been updated.
configuracion.tipohandle.javascript.Eltiposeleccionadohasidocreado=The selected type has been added.
configuracion.tipohandle.javascript.Listar=Edit
configuracion.tipohandle.javascript.LosDatosDelTipoHanSidoActualizados=The Types data has been Updated
configuracion.tipohandle.javascript.LosSiguientesDatosSonNecesarios=The following data are necessary:
configuracion.tipohandle.javascript.Proporcioneelnombredeltipoquedeseacrear=Give the type name
configuracion.tipohandle.javascript.-Titulo=-Title
configuracion.tipohandle.Titulo=Title:
configuracion.tipolist.Borrar=Delete
configuracion.tipolist.boton.Buscar=Search
configuracion.tipolist.Editar=Edit
configuracion.tipolist.header.CONTROLDETIPOS=TYPES CONTROL
configuracion.tipolist.javascript.Eltiposeleccionadoseraremovidodelsistema=The selection type will be removed from the system.
configuracion.tipolist.javascript.ElTipoSeraBorradoDelSistema=The Type will be deleted permanently
configuracion.tipolist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=The selection can\'t be removed, because \\nit\'s referenced in other part of the system. \n\nRemove the references and try again.
configuracion.tipolist.Laseleccionhasidoeliminada=The section was removed.
configuracion.tipolist.NoSeEliminoElTipo=The Type could not be deleted
configuracion.tipolist.NoSeEncontraronRegistros=There are no results to display
configuracion.tipolist.Noseencontraronregistros=There are no results to display.
configuracion.tipolist.SeHaEliminadoElTipo=The Type has been deleted
configuracion.tipolist.Titulo\:=Title:
configuracion.tipolist.Titulo=Title
configuracion.tipoobjetohandle.Descripcion=Description
configuracion.tipoobjetohandle.header.ALTATIPOSDEOBJETOS=NEW OBJECTS TYPE
configuracion.tipoobjetohandle.javascript.-Descripcion=-Description
configuracion.tipoobjetohandle.javascript.ElTipoDeObjetoHaSidoDadoDeaAta=The Objects Type has been Added
configuracion.tipoobjetohandle.javascript.LosDatosDelTipoDeObjetoHanSidoActualizados=The Data of the Objects Type has been Updated
configuracion.tipoobjetohandle.javascript.LosSiguientesDatosSonNecesarios=The following data is necessary\:
configuracion.tipoobjetohandle.javascript.-Titulo=-Title
configuracion.tipoobjetohandle.Titulo=Title
configuracion.tipoobjetolist.Borrar=Delete
configuracion.tipoobjetolist.Editar=Edit
configuracion.tipoobjetolist.header.CONTROLDETIPOSDEOBJETOS=OBJECTS TYPES EDITION
configuracion.tipoobjetolist.javascript.ElTipoDeObjetoSeraBorradoDelSistema=The Objects Type will be Deleted
configuracion.tipoobjetolist.NoSeEliminoElTipoDeObjeto=The Objects Type was not Deleted
configuracion.tipoobjetolist.SeHaEliminadoElTipoDeObjeto=The Objects Type has been Deleted
configuracion.tipoobjetolist.Titulo=Title
configuracion.ubicacionhandle.AdmiteQuejas= Accepts complaints
configuracion.ubicacionhandle.Clave=ID:
configuracion.ubicacionhandle.--Encargado--=--Leader--
configuracion.ubicacionhandle.--Facultad--= --Business Unit--
configuracion.ubicacionhandle.Facultad=Business Unit:
configuracion.ubicacionhandle.header.UBICACIONES=DEPARTMENTS
configuracion.ubicacionhandle.javascript.-Encargado=- User in Charge
configuracion.ubicacionhandle.javascript.-Facultad=- Business Unit
configuracion.ubicacionhandle.javascript.LaUbicacionhasidodadadealta=The department has been added.
configuracion.ubicacionhandle.javascript.LosDatosdelaUbicacionhansidoactualizados= The department data has been updated.
configuracion.ubicacionhandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
configuracion.ubicacionhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunaubicacion=The following items are necessary:
configuracion.ubicacionhandle.javascript.-Titulo=- Title
configuracion.ubicacionhandle.Titulo=Title:
configuracion.ubicacionhandle.UsuarioEncargado=User in Charge:
configuracion.ubicacionlist.Borrar=Delete
configuracion.ubicacionlist.BorrarUbicacion=Delete department
configuracion.ubicacionlist.Editar=Edit
configuracion.ubicacionlist.EditarUbicacion=Edit department
configuracion.ubicacionlist.Encargado=Leader
configuracion.ubicacionlist.Facultad=Business Unit
configuracion.ubicacionlist.header.CONTROLDEUBICACIONES=DEPARTMENT CONTROL
configuracion.ubicacionlist.javascript.Laubicacionseraborradadelsistema=The department will be removed from the system!
configuracion.ubicacionlist.NosehaeliminadolaubicacionEstosepuededeberaquetengaareasdeclaradas=The Department hasn't been removed. This may be due to having declared processes.
configuracion.ubicacionlist.Sehaeliminadolaubicacion=The department has been removed
configuracion.ubicacionlist.Titulo=Title
configuracion.unidadorganizacionalhandle.boton.CrearUnidad=Create area
configuracion.unidadorganizacionalhandle.javascript.Crear=Create
configuracion.unidadorganizacionalhandle.javascript.Launidadorganizacionalhasidocreada=The organizational unit has been added.
configuracion.unidadorganizacionalhandle.javascript.Listar=List
configuracion.unidadorganizacionalhandle.javascript.LosDatosdelaunidadorganizacionalhansidoactualizados=The organizational unit data have been updated.
configuracion.unidadorganizacionalhandle.javascript.Proporcioneelnombredelaunidadorganizacional=Give the organizational unit name.
configuracion.unidadorganizacionalhandle.Titulo=Title:
configuracion.unidadorganizacionalhandle.UNIDADORGANIZACIONAL=ORGANIZATIONAL UNIT
configuracion.unidadorganizacionallist.Borrar=Delete
configuracion.unidadorganizacionallist.Editar=Edit
configuracion.unidadorganizacionallist.header.CONTROLDEUNIDADORGANIZACIONAL=ORGANIZATIONAL UNIT CONTROL
configuracion.unidadorganizacionallist.javascript.Laseleccionnopuedesereliminadadebidoaque\\nestaligadoaotraspartesdentrodelsistema\n\nElimineesasligasyvuelvaaintentarlo=The selection can\'t be removed, because \\nit\'s referenced in other part of the system. \n\nRemove the references and try again.
configuracion.unidadorganizacionallist.javascript.Launidadorganizacionalseraremovidadelsistema=The organizational unit will be removed from the system
configuracion.unidadorganizacionallist.Laseleccionhasidoeliminada=The selection has been removed.
configuracion.unidadorganizacionallist.Noseencontraronregistros=There are no results to display
configuracion.unidadorganizacionallist.Titulo\:=Title:
configuracion.unidadorganizacionallist.Titulo=Title
configuracion.userhandle.Areaenlaquesedesempena=Process:
configuracion.userhandle.AsignarClaveManualmente= Assign ID Manually:
configuracion.userhandle.Clave=ID:
configuracion.userhandle.ClavedeAcceso=Access code:
configuracion.userhandle.Idioma=Language:
configuracion.userhandle.ConfirmeClavedeAcceso=Confirm access account:
configuracion.userhandle.Correoelectronico=E-mail:
configuracion.userhandle.CuentadeUsuario=User account:
configuracion.userhandle.gridSize=Records per page:
configuracion.userhandle.defaultGridSize=Records in details pages:
configuracion.userhandle.floatingGridSize=Results in emerging windows:
configuracion.userhandle.Descripcion= Description:
configuracion.userhandle.GuardarComoPerfil= Save as Profile:
configuracion.userhandle.header.CONFIGURACIONSISTEMA=SYSTEM CONFIGURATION
configuracion.userhandle.header.USUARIOS=USERS
configuracion.userhandle.javascript.Agregar=Add
configuracion.userhandle.javascript.-Agreguealmenosunareaenlaquesedesempena=-Add at least one Process
configuracion.userhandle.javascript.-Areaalaquepertenece=- Process
configuracion.userhandle.javascript.-ClavedeAcceso=- Access account
configuracion.userhandle.javascript.-Cuentadecorreo=- E-mail account
configuracion.userhandle.javascript.-Cuentadecorreovalida=- Valid e-mail account
configuracion.userhandle.javascript.-CuentadeUsuario=- User account
configuracion.userhandle.javascript.Elpasswordnocoincideporfavorintentedenuevo=The password is incorrect,please try again
configuracion.userhandle.javascript.Elusuariohasidodadodealta=The user has been added.
configuracion.userhandle.javascript.Listar=List
configuracion.userhandle.javascript.LosDatosdelusuariohansidoactualizados=The users data have been updated.
configuracion.userhandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
configuracion.userhandle.javascript.Lossiguientesdatossonnecesariosparadardealtaaunusuario=The following items are necessary:
configuracion.userhandle.javascript.-NombreCompleto=- Full Name
configuracion.userhandle.LaClaveTecleadaYaExiste= The password typed already exists
configuracion.userhandle.ModificarLaClaveDeAcceso= Modify the access key
configuracion.userhandle.NombreCompleto=Complete name:
configuracion.userhandle.NombreCompleto=Full Name:
configuracion.userhandle.permisos=Permissions Folder
configuracion.userhandle.PreferenciasdelSistema=System preferences
configuracion.userhandle.PreferenciasPersonales=Personal preferences:
configuracion.userhandle.RolenAcciones=Actions Role:
configuracion.userhandle.RolenAdministracion=Administrator Role:
configuracion.userhandle.RolenAuditorias=Audits Role:
configuracion.userhandle.RolenDocumentos=Documents Role:
configuracion.userhandle.RolenEncuestas= Surveys Role:
configuracion.userhandle.RolenIndicadores=Metrics Role:
configuracion.userhandle.RolenProyectos=Project Role:
configuracion.userhandle.RolenQuejas= Complaints Role:
configuracion.userhandle.RolenReportes=Reports Role\:
configuracion.userhandle.---SINPRIVILEGIOS---=--- NO PRIVILEGES ---
configuracion.userhandle.StatusUsuario=User Status\:
configuracion.userhandle.Tecleeunaclave=- Type a key
configuracion.userhandle.--TODAS--=-- ALL --
configuracion.userhandle.Ubicacion=Department:
configuracion.userhandle.UsarPreferenciasDelSistema=Use system preferences:
configuracion.userhandle.ValorStatusUsuario=Active
configuracion.userlist.Activo=Active
configuracion.userlist.Activo=Active
configuracion.userlist.Area=Process
configuracion.userlist.Areaenlaquesedesempena=Process
configuracion.userlist.Borrar=Delete
configuracion.userlist.BorrarUsuario=Delete User
configuracion.userlist.BusquedaEstado=Status:
configuracion.userlist.CONTROLDEUSUARIOS=USERS CONTROL
configuracion.userlist.Correoelectronico=E-mail\:
configuracion.userlist.Cuenta=Account
configuracion.userlist.CuentadeUsuario=User account:
configuracion.userlist.Editar=Edit
configuracion.userlist.EditarUsuario=Edit User
configuracion.userlist.Estado= Status
configuracion.userlist.Inactivo=Inactive
configuracion.userlist.Inactivo=Inactive
configuracion.userlist.javascript.Elusuarioseraeliminadocompletamente=The user will be completely deleted. \u00bfDo you want to continue?
configuracion.userlist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=The selection can\'t be removed, because \\nit\'s referenced in other part of the system. \n\nRemove the references and try again.
configuracion.userlist.Laseleccionhasidoeliminada=The selection has been removed
configuracion.userlist.NombreCompleto=Full Name:
configuracion.userlist.Noseencontraronregistros=There are no results to display.
configuracion.userlist.PorRegistrar=To Register
configuracion.userlist.PORREGISTRAR=To Register
configuracion.userlist.Todas=-- ALL --
configuracion.userlist.Ubicacion=Department
configuracion.userlistEsteusuarionopuedeserborrado=This user can not be deleted
configuracion.version.documentos=New Default Document Version:
coonfiguracion.userhandle.Perfiles= Profiles:
ctividad.actividadlist.Creada=Created
cuestionario.cuestionarioreportegeneral.chart.numerodecuestionarios=Number of questionnaries
cuestionario.preguntasobv.Altadeaccion=Add action
cuestionario.preguntasobv.Borrar=Delete
cuestionarios.cuestionarioreportegeneral.chart.activo=ACTIVE
cuestionarios.cuestionarioreportegeneral.chart.activo=ACTIVE
cuestionarios.cuestionarioreportegeneral.chart.borrador=DRAFT
cuestionarios.cuestionarioreportegeneral.chart.borrador=DRAFT
cuestionarios.cuestionarioreportegeneral.chart.cancelado=CANCELLED
cuestionarios.cuestionarioreportegeneral.chart.cancelado=CANCELLED
cuestionarios.cuestionarioreportegeneral.chart.title=Documents
cuestionarios.cuestionarioreportegeneral.chart.title=Documents
cuestionarios.cuestionarioreportegeneral.header=DOCUMENTS SUMMARY
cuestionarios.cuestionarioshandle.Autor=Author:
cuestionarios.cuestionarioshandle.Borrar=Delete
cuestionarios.cuestionarioshandle.Borrartodos=Delete all
cuestionarios.cuestionarioshandle.boton.Borrartodos=Delete all
cuestionarios.cuestionarioshandle.boton.Ordenar=Order
cuestionarios.cuestionarioshandle.Clave=ID:
cuestionarios.cuestionarioshandle.Cuestionariobase=Base questionnaire:
cuestionarios.cuestionarioshandle.Deleterseccion=Delete section
cuestionarios.cuestionarioshandle.Estatus=Status:
cuestionarios.cuestionarioshandle.header.CUESTIONARIOS=QUESTIONNARIES
cuestionarios.cuestionarioshandle.javascript.dardealtauncuestionario=To create a questionnaire:
cuestionarios.cuestionarioshandle.javascript.Elcuestionariohasidoagregado=The questionnaire has been created.
cuestionarios.cuestionarioshandle.javascript.Laseccionelegidayaexisteenlalista=The chosen section already exists in the list
cuestionarios.cuestionarioshandle.javascript.Laseccionelegidayaexisteenlalista=The chosen section already exists in the list
cuestionarios.cuestionarioshandle.javascript.Listar=List
cuestionarios.cuestionarioshandle.javascript.Losdatosdelcuestionariohansidoactualizados=The questionnaire data have been updated.
cuestionarios.cuestionarioshandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
cuestionarios.cuestionarioshandle.javascript.Lossiguientesdatossonnecesariospara=The following items are necessary:
cuestionarios.cuestionarioshandle.javascript.Nuevo=New
cuestionarios.cuestionarioshandle.javascript.Seleccioneunaseccion=Seleccione una Secci\u00f3n.
cuestionarios.cuestionarioshandle.javascript.-Titulo=- Title
cuestionarios.cuestionarioshandle.Nosehanasignadosecciones=There are no sections
cuestionarios.cuestionarioshandle.Removerseccion=Remove Section
cuestionarios.cuestionarioshandle.Secciones=Sections:
cuestionarios.cuestionarioshandle.Seccionesaagregar=Sections to add
cuestionarios.cuestionarioshandle.--SELECCIONE--=-- SELECT --
cuestionarios.cuestionarioshandle.Titulo\:=Title:
cuestionarios.cuestionarioshandle.Titulo=Title
cuestionarios.cuestionarioslector.Autor=Author
cuestionarios.cuestionarioslector.Clave=ID
cuestionarios.cuestionarioslector.Cuestionario=Questionnaire
cuestionarios.cuestionarioslector.Est=Status
cuestionarios.cuestionarioslector.EstadoActivo=Status: Active
cuestionarios.cuestionarioslector.EstadoBorrador=Status: In edition
cuestionarios.cuestionarioslector.EstadoCancelado=Status: Cancelled
cuestionarios.cuestionarioslector.Estatus=Status:
cuestionarios.cuestionarioslector.header.CONSULTADECUESTIONARIOS=QUESTIONNAIRE LIST
cuestionarios.cuestionarioslector.Noseencontraronregistros=There are no results to display.
cuestionarios.cuestionarioslector.Obv=Comments
cuestionarios.cuestionarioslector.Titulo=Title:
cuestionarios.cuestionarioslector.Todos=ALL
cuestionarios.cuestionarioslector.Verpreguntas=Show questions
cuestionarios.cuestionarioslist.Autor=Author
cuestionarios.cuestionarioslist.Borrar=Delete
cuestionarios.cuestionarioslist.Borrarcuestionario=Delete questionnaire
cuestionarios.cuestionarioslist.Clave=ID
cuestionarios.cuestionarioslist.Cuestionario=Questionnaire 
cuestionarios.cuestionarioslist.Editarpreguntas=Edit questions
cuestionarios.cuestionarioslist.Edt=Edit
cuestionarios.cuestionarioslist.ElcuestionarionopudosermarcadocomoactivoNecesitaalmenosunaseccionyquelasseccionesseleccionadastenganalmenosunapregunta=The questionnaire couldn\'t be set as active. You need at least a section and the selected Sections must have at least a question.
cuestionarios.cuestionarioslist.Est=Status
cuestionarios.cuestionarioslist.EstadoActivoAccionCancelar=The actual status is ACTIVE. Press to set as CANCELLED.
cuestionarios.cuestionarioslist.EstadoBorradorAccionMarcarcomoactivo=The actual status is ON EDITION. Press to set as ACTIVE.
cuestionarios.cuestionarioslist.EstadoCanceladoAccionMarcarcomoactivo=The actual status is CANCELLED. Press to set as ACTIVE.
cuestionarios.cuestionarioslist.Estatus=Status:
cuestionarios.cuestionarioslist.header.CONTROLDECUESTIONARIOS-USR-=EDITION OF QUESTIONNAIRE-USR-
cuestionarios.cuestionarioslist.javascript.CambiaraelestadodelcuestionarioDeseacontinuar=The status of the questionnaire  will change.\\nDo you want to continue?
cuestionarios.cuestionarioslist.javascript.ElcuestionarioseraremovidodelsistemaDeseacontinuar=The questionnaire  will be removed.\\nDo you want to continue?
cuestionarios.cuestionarioslist.javascript.Laseleccionnopuedesereliminadadebidoaque\\nestaligadoaotraspartesdentrodelsistema\n\nElimineesasligasyvuelvaaintentarlo=The selection can\'t be removed, because \\nit\'s referenced in other part of the system. \n\nRemove the references and try again.
cuestionarios.cuestionarioslist.Laseleccionhasidoeliminada=The selection has been removed.
cuestionarios.cuestionarioslist.Noseencontraronregistros=There are no results to display.
cuestionarios.cuestionarioslist.Nosepuedeeditar=Can\'t be edited
cuestionarios.cuestionarioslist.Obv=Preview
cuestionarios.cuestionarioslist.Preguntas=Questions
cuestionarios.cuestionarioslist.PresioneParaDarDeAltaPreguntasEnElCuestionarioYVerLasActuales= Press to register a questions in the questionnaire and view the current.
cuestionarios.cuestionarioslist.PresioneParaDarDeAltaSub-ProcesosEnElCuestionarioYVerLasActuales= Press to register sections in the questionnaire and view the current.
cuestionarios.cuestionarioslist.Subprocesos=Sections
cuestionarios.cuestionarioslist.Titulo=Title:
cuestionarios.cuestionarioslist.Todos=ALL
cuestionarios.cuestionarioslist.Ver=View
cuestionarios.cuestionarioslist.Vercuestionario=Show questionnaire 
cuestionarios.cuestionarioslist.Verpreguntas=Show questions
cuestionarios.preguntas.Borrar=Delete
cuestionarios.preguntas.boton.Borrarpreguntas=Delete questions
cuestionarios.preguntas.Deleterpregunta=Delete question
cuestionarios.preguntas.header.EDICIONDEPREGUNTAS=QUESTIONS EDIT
cuestionarios.preguntas.javascript.Lapreguntaelegidayaexisteenlalista=The selected question is already in the list.
cuestionarios.preguntas.javascript.Laspreguntasdelcuesionariohansidoactualizadas=The questions has been updated.
cuestionarios.preguntas.javascript.Listar=List
cuestionarios.preguntas.javascript.Nuevo=Create
cuestionarios.preguntas.javascript.Seleccioneunapregunta=Select a question
cuestionarios.preguntas.Nosehanasignadopreguntasaestaseccion=The are no questions for this section
cuestionarios.preguntas.Preguntas=Questions:
cuestionarios.preguntas.Preguntasaagregar=Questions to add.
cuestionarios.preguntas.Removerpregunta=Remove question
cuestionarios.preguntas.Secciones=Sections:
cuestionarios.preguntas.--SELECCIONE--=-- SELECT --
cuestionarios.preguntas.Titulo\:=Title:
cuestionarios.preguntas.Titulo=Title
cuestionarios.preguntasobv.Altaaccion=New action
cuestionarios.preguntasobv.Autor=Author:
cuestionarios.preguntasobv.boton.AgregarPregunta=Add Question
cuestionarios.preguntasobv.boton.Imprimir=Print
cuestionarios.preguntasobv.Calificacion=Score
cuestionarios.preguntasobv.Clave=Code:
cuestionarios.preguntasobv.Comentario=Comment
cuestionarios.preguntasobv.ComentariosGenerales=General Comment:
cuestionarios.preguntasobv.Correctiva=Corrective
cuestionarios.preguntasobv.Cuestionario=Questionnaire:
cuestionarios.preguntasobv.Deberespondertodaslaspreguntas=You must to answer all the questions
cuestionarios.preguntasobv.Encargados=Leader:
cuestionarios.preguntasobv.Estatus=Status:
cuestionarios.preguntasobv.header.CUESTIONARIO-=QUESTIONNAIRE-
cuestionarios.preguntasobv.javascript.Consultar=List
cuestionarios.preguntasobv.javascript.Esnecesarioponerunaobservacion=Give a remark
cuestionarios.preguntasobv.javascript.Laauditoriahasidomarcadacomorealizadaylasrespuestassehanguardado=The audit has been set as done and responses have been saved.
cuestionarios.preguntasobv.javascript.Lanotificacionasidoenviadaviacorreoelectronico=The notification has been sent by e-mail.
cuestionarios.preguntasobv.javascript.Lanotificacionasidoenviadaviacorreoelectronico=The notification has been sent by e-mail.
cuestionarios.preguntasobv.javascript.ListarAuditorias=List Audits
cuestionarios.preguntasobv.javascript.NuevaAuditoria=New Audit
cuestionarios.preguntasobv.javascript.-Objetivos=- Objectives.
cuestionarios.preguntasobv.javascript.Sehanactualizadolasrespuestasdelcuestionario=The answers have been updated.
cuestionarios.preguntasobv.javascript.SilarespuestaaunapreguntafueNOoN/Aesnecesarioqueagregueuncomentario=If the answer to the question was NO or N/A, you need\\nto add a comment.
cuestionarios.preguntasobv.javascript.VerAuditoria=Show Audit
cuestionarios.preguntasobv.Laaccionseracompletamenteborradadelsistema,\u00bfDeseacontinuar?=The action will be completely deleted from the system. Do you want to continue?
cuestionarios.preguntasobv.Mejoracontinua=Continual improvement
cuestionarios.preguntasobv.Preguntas=Questions
cuestionarios.preguntasobv.Preventiva=Preventive
cuestionarios.preguntasobv.Proceso(s)Auditado(s)=Process Audited:
cuestionarios.preguntasobv.Resultados=Accept Results
cuestionarios.preguntasobv.Seccion=Section:
cuestionarios.preguntasobv.Titulo=Title:
cuestionarios.preguntasobv.Total=Final Score:
cuestionarios.preguntasobv.Ubicacion=Department:
cuestionarios.puntonormahandle.Descripcion=Description
cuestionarios.puntonormahandle.Elpuntodelanormahasidoactualizado=The point of the norm has been updated.
cuestionarios.puntonormahandle.Elpuntodelanormahasidodadodealta=The point of the norm has been added.
cuestionarios.puntonormahandle.PUNTOSDELANORMA=POINTS OF THE NORM
cuestionarios.puntonormahandle.Titulo=Title
cuestionarios.puntonormalist.CONTROLDEPUNTOSDELANORMA=CONTROL OF POINTS OF THE NORM
cuestionarios.puntonormalist.Elpuntodelanormaseraborradodelsistema\u00bfDeseacontinuar?=The point of the norm will be deleted from the system. Do you want to continue?
cuestionarios.puntonormalist.Nosehapodidoborrarelobjeto=The object has not been deleted
cuestionarios.puntonormalist.Sehaeliminadoelobjeto=The object has been removed
cuestionarios.puntonormalist.Titulo=Title
cuestionarios.seccionlist.BusquedaNumero=Number
cuestionarios.seccionlist.Numero=Number:
documento.fechaCancelacion=Cancel Date
documento.papelera.cambioEstado=El documento sera restaurado, \u00bfDesea Continuar?
documentos.carpetahandle.header.NUEVACARPETA=NEW FOLDER
documentos.carpetahandle.javascript.Lacarpetanohasidonombrada=The folder has no title
documentos.carpetahandle.Nombredelacarpeta=Folder's title:
documentos.carpetapermisos.Areas=Processes
documentos.carpetapermisos.header.PermisosDeCarpeta=FOLDER PERMISSIONS
documentos.carpetapermisos.NoCuentaConPermisosParaVerEsteDocumento=You do not have permission to see this document.
formularios.NoCuentaConPermisosParaVerEsteFormulario=You do not have permission to see this form.
documentos.carpetapermisos.NoSePudieronActualizarLosPermisosDeLaCarpeta=Folder permissions were not updated.
documentos.carpetapermisos.Usuarios=Users
documentos.documentohandle.Delasiguientelistadeautorizantes=From the following autorization list
documentos.documentohandle.Delasiguientelistadeautorizantes=From the following autorization list
documentos.documentohandle.Eldocumentonofueautorizado=The document wasn't autorized
documentos.documentohandle.Eldocumentonofueautorizadopor=The document was dissaproved by
documentos.documentohandle.Secuenciacompleta=Full sequence
documentos.documentonocontroladohandle.Eldocumentohasidoagregado=The document has been added
documentos.documentoproyectoshandle.Laclavedeldocumentoelegidoyaexisteenunasolicitud=The ID chosen document already exist in an application.
documentos.documentoproyectoshandle.Yaexisteelt\u00edtulodeldocumentoelegido=There is already chosen the title of the document.
documentos.documentoproyectoshandle.Yaexistelaclavedeldocumentoelegido=There is already chosen the id document 
Documentos.Documentos.Limpiar=Clear
documentos.documentoshandle.aprobacion=Date
documentos.documentoshandle.Aprobador=Approver
documentos.documentoshandle.Aprobador=Approvers
documentos.collectingAndStoreResponsible=Responsible for collecting and filing
documentos.informationClassification=Information classification
documentos.disposition=Disposition
documentos.documentoshandle.Archivo=File:
documentos.documentoshandle.ArchivoActual=View current file
documentos.documentoshandle.ArchivoOriginal=Download original file
documentos.documentoshandle.ArchivoSubidoEnSolicitud=File uploaded on Request:
documentos.documentoshandle.Asignarlectores=Set readers:
documentos.documentoshandle.Bajar=Download:
documentos.documentoshandle.Bajardocumento=Download file
documentos.documentoshandle.Formulario=Form
documentos.documentoshandle.Ver=View
documentos.documentoshandle.boton.CrearSecuencia=Create Sequence
documentos.documentoshandle.boton.GenerarRegistro=Generate Registry
documentos.documentoshandle.cambio=Description
documentos.documentoshandle.Clave=ID
documentos.documentoshandle.Clave=ID
documentos.documentoshandle.TituloArchivo=File title
documentos.documentoshandle.DetalledelaUltimaaprobacion=Last approval detail:
documentos.documentoshandle.DocumentoElectronico=Electronic Document
documentos.documentoshandle.Elarchivodeberallevarlaextensiondocxlspdfhtmhtml=The file must include the extension .doc/x, .xls/x, .ppt/x, .pdf, .htm or .html
documentos.documentoshandle.Eldocumentoesnuevonocuentaconrevisiones=The document is new has no reviews.
documentos.documentoshandle.Estatus=Status
documentos.documentoshandle.Fecha=Date
documentos.documentoshandle.Fechade=Approval date
documentos.documentoshandle.header.DOCUMENTOS=DOCUMENTS
documentos.documentoshandle.header.FORMULARIOS=FORMS
documentos.documentoshandle.javascript.Archivo=- File
documentos.documentoshandle.javascript.Clave=- ID
documentos.documentoshandle.javascript.ElDocumentosehaagregadoconexitoalalistadeesperaporRevision=Document adition succesfull
documentos.documentoshandle.javascript.Faltanlossiguienteselementos=The following elements are missing
documentos.documentoshandle.javascript.LaactualizaciondeldocumentohasidopuestaenesperadeRevision=The update of the document has been put on hold for review
documentos.documentoshandle.javascript.LosDatosFueronCambiadosConExito=The information was updated successfully.
documentos.documentoshandle.javascript.Lossiguientesdatossonnecesarios=The following items are necessary:
documentos.documentoshandle.javascript.Noconozcolaclave=I don\\'t known the password
documentos.documentoshandle.javascript.Nuevodocumento=New document
documentos.documentoshandle.javascript.Originador=- Originator
documentos.documentoshandle.javascript.Razondelcambio=- Reason of upload/modification
documentos.documentoshandle.javascript.Seleccioneundocumentoelectronico=Select an electronic document.
documentos.documentoshandle.javascript.Titulo=- Title
documentos.documentoshandle.javascript.Vercarpeta=Show folder
documentos.documentoshandle.Laclaveingresadaestarestringidaparalageneracionderespaldos=The entered passwordkey is restricted to the generation of backups.
documentos.documentoshandle.lectores=Reading logs:
documentos.documentoshandle.Localizadoen=Folder:
documentos.documentoshandle.AccessPermissionReason= You have access because:
documentos.documentoshandle.Nivelde=Level of
documentos.documentoshandle.onosepudolocalizarelarchivoindicado=or the indicated file couldn\\'t locate.
documentos.documentoshandle.Originador=Author of current version
documentos.documentoshandle.Razonde=Purpose of request
documentos.documentoshandle.Razondelcambio=Upload/Modification reason:
documentos.documentoshandle.revision=Version
documentos.documentoshandle.--Seleccione--=-- SELECT --
documentos.documentoshandle.Solicitudesrelacionadasaldocumento=Requests related to the document
documentos.documentoshandle.Titulo=Document name
documentos.documentoshandle.slimReportName=Report name
documentos.documentoshandle.Verdetalledeautorizacion=Last authorization detail
documentos.documentoshandle.Version=Version:
documentos.documentoshandle.VolveraPendientes=Return to Pending
documentos.documentoshandle.Yaexisteeltitulooclavedeldocumentoelegido=The title already exists or the selected document key .
documentos.documentoshandle.Yaexisteunarchivoconelmismonombre=Already exist a document with the same title
documentos.documentoslector.Aprobacion=Approval
documentos.documentoslector.Aprovador=Signer
documentos.documentoslector.Carpeta=Folder:
documentos.documentoslector.Clave=ID
documentos.documentoslector.DocumentoCancelado=Cancelled Document
documentos.documentoslector.DocumentoDescontinuado=Discontinued Document
documentos.documentoslector.Edici\u00f3n=Edit
documentos.documentoslector.Enautorizaci\u00f3n=In authorization
documentos.documentoslector.Entrada=Date
documentos.documentoslector.Est=Status
documentos.documentoslector.EstadoActivo=The actual status is ACTIVE.
documentos.documentoslector.EstadoNuevaversionenprocesoderevision=The actual status is new version in REVISION process.
documentos.documentoslector.Estatus=Status:
documentos.documentoslector.Fechaapb=Approval date.
documentos.documentoslector.header.LISTAMAESTRA=MASTER LIST
documentos.documentoslector.header.PENDIENTESPORASIGNARLECTOR=PENDING TO ASSIGN READER
documentos.documentoslector.Nocontroladoeliminado=Eliminated uncontrolled
documentos.documentoslector.Nombre=Name
documentos.documentoslector.Nombre=Name:
documentos.documentoslector.Noseencontrarondocumentosenestabusqueda=No documents were found
documentos.documentoslector.Originador=Originator
documentos.documentoslector.Revision=Revision
documentos.documentoslector.ruta=Path
documentos.documentoslector.Tipodedocumento=Document Type
documentos.documentoslector.Ver=View
documentos.documentoslisate.ElestadoactualesEDICION,Eldocumentotienesecuenciaspendientes(usuarlaopcionver)=The current status is EDITION, the document has pending sequences (use the view option).
documentos.documentoslist.AccionCambiaraactivo=Action: Set as Active
documentos.documentoslist.AccionCambiaraactivo=Action: Set as Active
documentos.documentoslist.AccionCambiaraactivo=Action: Set as Active
documentos.documentoslist.AccionCambiaraactivo=Action: set as Active
documentos.documentoslist.Activo= Active
documentos.documentoslist.Actualizarmenudenavegacion=Refresh menus
documentos.documentoslist.apbfechafin=Aprobation final date
documentos.documentoslist.apbfechainicio=Aprobation start date
documentos.documentoslist.Autorizacion= Authorization
documentos.documentoslist.Borrar=Delete
documentos.documentoslist.Cancelado= Cancelled
documentos.documentoslist.Carpeta=Folder:
documentos.documentoslist.Clave=ID
documentos.documentoslist.Crearnuevacarpeta=New folder
documentos.documentoslist.Crearnuevodocumento=New document
documentos.documentoslist.Descontinuado= Discontinued
documentos.documentoslist.DocumentoActivo= Active document
documentos.documentoslist.DocumentoCancelado= Cancelled document
documentos.documentoslist.DocumentoDescontinuado= Discontinued document
documentos.documentoslist.DocumentoEnAutorizacion= Authorization Document
documentos.documentoslist.DocumentoEnEdicion= Edit document
documentos.documentoslist.DocumentoNoControlado= Not controlled document
documentos.documentoslist.Edicion= Edit
documentos.documentoslist.Eliminarcarpeta=Delete folder
documentos.documentoslist.Eliminardocumento=Delete document
documentos.documentoslist.Enautorizacion=In Authorizati\u00f3n.
documentos.documentoslist.entfechafin=Entering final date
documentos.documentoslist.entfechainicio=Entering start date
documentos.documentoslist.Est=Status
documentos.documentoslist.estado=Status
documentos.documentoslist.estado=Status
documentos.documentoslist.Estado=Status
documentos.documentoslist.EstadoActivoAccionCambiaraeditar=Status: Active  Action: Set as On Edition
documentos.documentoslist.Estatus=Status:
documentos.documentoslist.Fechaapb=Approval date
documentos.documentoslist.Fechaent=Entrance date
documentos.documentoslist.header.CONTROLDEDOCUMENTOS=DOCUMENTS CONTROL
documentos.documentoslist.javascript.CambiaraelestadodeeldocumentoDeseacontinuar=The status of the document will change. Do you want to continue?
documentos.documentoslist.javascript.CambiaraelestadodeeldocumentoDeseacontinuar=The status of the document will change. Do you want to continue?
documentos.documentoslist.javascript.Carpeta=Folder
documentos.documentoslist.javascript.EldocumentoseraremovidocompletamentedelsistemaDeseacontinuar=The docuement will be removed\\nDo you want to continue?
documentos.documentoslist.javascript.LacarpetaseraremovidacompletamentedelsistemaDeseacontinuar=The folder wil be removed.\\nDo you want to continue?
documentos.documentoslist.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarlo=The selection can\'t be removed, because \\nit\'s referenced in other part of the system. \n\nRemove the references and try again.
documentos.documentoslist.Lacarpetaestavacia=The folder is empty
documentos.documentoslist.Laseleccionhasidoeliminada=The selection was removed.
documentos.documentoslist.NoControlado= Not controlled
documentos.documentoslist.Nocontrolado=Uncontrolled.
documentos.documentoslist.Nocontroladoeliminado=Uncontrolled Eliminated
documentos.documentoslist.Nombre=Name
documentos.documentoslist.Nombre=Name
documentos.documentoslist.Noseencontrarondocumentosenestabusqueda=No documents were found.
documentos.documentoslist.noSePuedeModificar=The document's status cannot be modified
documentos.documentoslist.noSePuedeModificar=The document's status cannot be modified
documentos.documentoslist.Originador=Originator
documentos.documentoslist.Proyectos=Projects.
documentos.documentoslist.Ver=View
documentos.documentoslist.Verdocumento=Show document
documentos.documentoslist.Descargardocumentotitle=Download document
documentos.documentoslist.Descargardocumento=Download document
documentos.documentoslistadocp.Activo=Active
documentos.documentoslistae.Activo=Active
documentos.documentoslistae.Activo=Active
documentos.documentoslistae.Agregardocumentoreferenciado=Add document referenced
documentos.documentoslistae.Autor=Author
documentos.documentoslistae.Borrar=Delete
documentos.documentoslistae.Carpeta=Folder:
documentos.documentoslistae.Clave=ID
documentos.documentoslistae.DocumentosControlados=Controlled Documents
documentos.documentoslistae.DocumentosNoControlados=Documents Not Controlled
documentos.documentoslistae.DocumentosReferenciados=Referenced Documents
documentos.documentoslistae.Eliminarcarpeta=Delete folder
documentos.documentoslistae.ElsestadoactualesEDICIONporRE-APROBACION.Seesperaqueseactualiceeldocumentosparasunuevarevision(usuarlaopcionver)=The current status is EDITION by REAPPROVAL. It is expected to update the document for new revsion (use the view option).
documentos.documentoslistae.Est=Status
documentos.documentoslistae.EstadoActivoAccionCambiaraedicion=The actual status is ACTIVE. Press to set ON EDITION.
documentos.documentoslistae.EstadoEdicionAccionCambiaraActivo=The actual status is ON EDITION. Press to set as ACTIVE.
documentos.documentoslistae.EstadoEnedicionAccionSeesperaqueactualizeeldocumentoparasunuevarevisionusarlaopcionVer=The current state is EDITION. Is expected to update the document for its new revision (using the view option)
documentos.documentoslistae.Estatus=Status:
documentos.documentoslistae.Fechaapb=Approval date
documentos.documentoslistae.Fechaent=Entrance date
documentos.documentoslistae.header.CONSULTADEDOCUMENTOSACTIVOSYEDICION=LIST ACTIVE AND EDITING DOCUMENTS
documentos.documentoslistae.javascript.CambiaraelestadodeeldocumentoDeseacontinuar=The status of the document will change.\\nDo you want to continue?
documentos.documentoslistae.javascript.LacarpetaseraremovidacompletamentedelsistemaDeseacontinuar=The folder will be removed completely from the system.\\n Do you want to continue?
documentos.documentoslistae.Lacarpetaestavacia=The folder is empty.
documentos.documentoslistae.Medios=Media
documentos.documentoslistae.ModificarPermisos=Press to modify folder permissions.
documentos.documentoslistae.Mover=Move
documentos.documentoslistae.Moverdocumento=Move document
documentos.documentoslistae.Nohayimagenesparagaleria= There is no images to gallery.
documentos.documentoslistae.Nombre\:=Name:
documentos.documentoslistae.Nombre=Name
documentos.documentoslistae.Nopuedealterarelestadodeldocumentoyaquenolepertenece=Can\\'t alter the status of the document that doesn\\'t belong to you.
documentos.documentoslistae.Noseencontrarondocumentosenestabusqueda=No documents were found.
documentos.documentoslistae.Originador=Originator
documentos.documentoslistae.Presioneparacrearunaimagen=Press to create a new media
documentos.documentoslistae.Presioneparacrearundocumentonocontrolado=Press to create an uncontrolled document
documentos.documentoslistae.Presioneparacrearunmedio=Press to create a new image
documentos.documentoslistae.Seeliminaraeldocumentonocontrolado.Deseacontinuar?=The uncontrolled document will be deleted. Do you want to continue?
documentos.documentoslistae.Seeliminaralaimagendelagaleria.Deseacontinuar?=The gallery image will be deleted. Do you want to continue?
documentos.documentoslistae.Ver=View
documentos.documentoslistae.Verdocumento=Show document
documentos.documentoslistcd.(revision)= (Revision)
documentos.documentoslistcd.Borrar=Delete
documentos.documentoslistcd.BorrarDocumento=Delete Document
documentos.documentoslistcd.Clave=ID
documentos.documentoslistcd.DocumentosControlados=Controlled Documents
documentos.documentoslistcd.DocumentosenEdicion=Edit Document.
documentos.documentoslistcd.DocumentosNoControlados=Documents Not Controlled
documentos.documentoslistcd.Eliminardocumento=Delete document
documentos.documentoslistcd.Est=Status
documentos.documentoslistcd.EstadoCanceladoAccionRestaurarasucarpetacomoenedicion=The actual status is CANCELLED. Press to restore ON EDITION.
documentos.documentoslistcd.EstadoDescontinuadoEldocumentosolopuedeserborrado=The actual status is Discontinued. The document only can be removed.
documentos.documentoslistcd.Estatus=Status:
documentos.documentoslistcd.Fechaapb=Approval date
documentos.documentoslistcd.Fechaent=Entrance date
documentos.documentoslistcd.header.DOCUMENTOSCANCELADOSYDESCONTINUADOS=CANCELLED AND DISCONTINUED DOCUMENTS
documentos.documentoslistcd.javascript.CambiaraelestadodeeldocumentoDeseacontinuar=The status of the document will change.\\nDo you want to continue?
documentos.documentoslistcd.javascript.EldocumentoseraremovidocompletamentedelsistemaDeseacontinuar=The document will be completely removed from the system.\\nDo you want to continue?
documentos.documentoslistcd.javascript.LaseleccionnopuedesereliminadadebidoaqueestaligadoaotraspartesdentrodelsistemaElimineesasligasyvuelvaaintentarloPosibleerrorVerifiquequeeldocumentonotengaunasecuenciaasignada=The selection can\'t be removed, because \\nit\'s referenced in other part of the system. \n\nRemove the references and try again.\n\nPossible error: Verify that the document doesn't have assigned sequences.
documentos.documentoslistcd.Laseleccionhasidoeliminada=The selection has been removed.
documentos.documentoslistcd.Medios=Media
documentos.documentoslistcd.Nohayimagenesparagaleria=No images to Gallery.
documentos.documentoslistcd.Nombre=Name
documentos.documentoslistcd.Nombre=Name:
documentos.documentoslistcd.Noseencontrarondocumentosenestabusqueda=No documents were found.
documentos.documentoslistcd.Originador=Originator
documentos.documentoslistcd.Seeliminaralaimagendelagaleria\u00bfDeseacontinuar?=The gallery image will be deleted. Do you want to continue?
documentos.documentoslistcd.Ver=View
documentos.documentoslistcd.Verdocumento=Show document
documentos.documentoslistpa.Clave=ID
documentos.documentoslistpa.Enautorizacion=Authorizing
documentos.documentoslistpa.Est=Status
documentos.documentoslistpa.EstadoEnautorizacionAccionIraautorizar=Status: Authorizing  Action: Authorize
documentos.documentoslistpa.EstadoEnautorizacionAccionIraautorizar=Status: In Authorization  Action: Go to authorize
documentos.documentoslistpa.Estatus=Status:
documentos.documentoslistpa.Fechadeentrada=Date on which the document was uploaded
documentos.documentoslistpa.header.CONSULTADEDOCUMENTOSPORAUTORIZAR=LIST PENDING DOCUMENTS FOR AUTHORIZATION
documentos.documentoslistpa.Nombre=Name:
documentos.documentoslistpa.Nombredocumento=Name
documentos.documentoslistpa.NoseencontrarondocumentosconelnombreespecificadoCambieelnombreeintentedenuevo=No documents were found with the specified name. Change the name and try again.
documentos.documentoslistpa.Originador=Originator
documentos.documentoslistpa.Ver=View
documentos.documentoslistpa.Verdocumento=Show document
documentos.documentospendientes.Accionesanalizarlacausaraiz=Actions to analize:
documentos.documentospendientes.Accionesatomarparaimplementar=Actions to take:
documentos.documentospendientes.AccionesATomarPorRealizar=Activities to do:
documentos.documentospendientes.AccionesATomarPorCompletar=Activities to complete:
documentos.documentospendientes.AccionesATomarPorVerificar=Activities to verify:
documentos.documentospendientes.AccionesATomarPorVerificarNoAplica=Actions to verify as cancelled:
documentos.documentospendientes.AccionesCorrectivas=Corrective Actions:
documentos.documentospendientes.AccionesCorrectivas=Corrective Actions:
documentos.documentospendientes.AccionesMejoraContinua=Continual Improvement Actions: 
documentos.documentospendientes.AccionesMejoraContinua=Continual Improvement Actions: 
documentos.documentospendientes.Accionesparaaceptar=Actions to accept:
documentos.documentospendientes.Accionesparaasignar=Actions to assign:
documentos.documentospendientes.AccionesPorAceptar=Actions to accept:
documentos.documentospendientes.AccionesPorAgregar=Actions to add immediate correction action:
documentos.documentospendientes.AccionesPorAnalizar=Actions to analyze:
documentos.documentospendientes.AccionesPorAsignar=Actions to assign responsible:
documentos.documentospendientes.AccionesPreventivas=Preventive Actions:
documentos.documentospendientes.AccionesPreventivas=Preventive Actions:
documentos.documentospendientes.AccionesProducto=Product Actions:
documentos.documentospendientes.AccionesProyecto=Project Actions:
documentos.documentospendientes.Accionestomarparaverificar=Actions to take for verify:
documentos.documentospendientes.ActividadesRealizar=Activities to do:
documentos.documentospendientes.Actividadesretrasadas=Delayed activities:
documentos.documentospendientes.ActividadesVerificar=Activities to verify: 
documentos.documentospendientes.Actualizar=Update
documentos.documentospendientes.AuditoriasporConfirmar=Audits to Confirm:
documentos.documentospendientes.Auditoriaspornotificar=Audits to Notify:
documentos.documentospendientes.CambiosAutorizar=Activites with changes to authorize: 
documentos.documentospendientes.Documentospendientes=Documents to authorize:
documentos.documentospendientes.ExpiredDocuments=Expired documents:
documentos.documentospendientes.Documentosporasignarlectores=Documents to assign a viewer:
documentos.documentospendientes.Documentosporleer=Documents to read:
documentos.documentospendientes.Encuestasporcerrar=Surveys to colse
documentos.documentospendientes.Encuestasporcontestar=Surveys to answer
documentos.documentospendientes.header.Pendientes=New tasks
documentos.documentospendientes.header.PendientesProyecto=New Tasks on Project\\'s Module
documentos.documentospendientes.MinutasPorRealizar=Minutes to do:
documentos.documentospendientes.PendientesdeEncuestas=Survey - tasks
documentos.documentospendientes.PendientesdeQuejas=Complaints - tasks
documentos.documentospendientes.PendientesdeRegistrodeusuarios=User registration - tasks
documentos.documentospendientes.PendientesProyecto=Pendings of Project
documentos.documentospendientes.PeticionesPresupuestoAutorizar=Activities with budget requests to authorize:
documentos.documentospendientes.PeticionesPresupuestoAutorizar=Activities with budget requests to authorize:
documentos.documentospendientes.ProyectosCerrar=Projects to Close: 
documentos.documentospendientes.Quejasatendidasporevaluarefectividad=Handled complaints to evaluate effectiveness:
documentos.documentospendientes.QuejasatendidasporevaluarefectividadTooltip=\ List of complaints which is author and have been answered, which should evaluate the outcome.
documentos.documentospendientes.QuejasatendidasporevaluarefectividadTooltipUR=Voz Ur por Evaluar Efectividad
documentos.documentospendientes.QuejasporAsignarResponsable=Complaints to assign responsible:
documentos.documentospendientes.QuejasporAsignarResponsableTooltip=List of created complaints wich it\u00b4s neccesary to appoint a responsable.
documentos.documentospendientes.QuejasporAsignarResponsableTooltipUR=Voz UR por asignar responsable
documentos.documentospendientes.Quejaspordarrespuesta=Complaints to answer:
documentos.documentospendientes.QuejaspordarrespuestaTooltip=List of complaints should address and respond as responsible assigned.
documentos.documentospendientes.QuejaspordarrespuestaTooltipUR=Voz Ur por dar Respuesta
documentos.documentospendientes.Quejasporverificarrespuesta=Complaints to verify answer:
documentos.documentospendientes.QuejasporverificarrespuestaTooltip=\ List of answered complaints  by the responsable of which must verify the response.
documentos.documentospendientes.QuejasporverificarrespuestaTooltipUR=Voz Ur por Verificar Respuesta
documentos.documentospendientes.RedactarReporteAuditoria=Audits to Make Report:
documentos.documentospendientes.ReportedeAuditoriasporAceptar=Audits report to accept results:
documentos.documentospendientes.Secuenciasporcrear=Sequences to create:
documentos.documentospendientes.Solicitudesporatender=Requests:
documentos.documentospendientes.Solicitudesporatender=Requests to verify:
documentos.documentospendientes.Formulariosporcompletar=Forms to complete:
documentos.documentospendientes.Usuariosporactivar=Users to activate:
documentos.documentospendientes.Usuariosporasignarpuesto=Users to assign position:
documentos.documentosreportegeneral.chart.activo=ACTIVE
documentos.documentosreportegeneral.chart.activo=ACTIVE
documentos.documentosreportegeneral.chart.cancelado=CANCELLED
documentos.documentosreportegeneral.chart.cancelado=CANCELLED
documentos.documentosreportegeneral.chart.descontinuado=DISCONTINUED
documentos.documentosreportegeneral.chart.descontinuado=DISCONTINUED
documentos.documentosreportegeneral.chart.enautorizacion=REVISION
documentos.documentosreportegeneral.chart.enautorizacion=REVISION
documentos.documentosreportegeneral.chart.enedicion=EDITING
documentos.documentosreportegeneral.chart.enedicion=EDITING
documentos.documentosreportegeneral.chart.numerodedocumentos=Number of documents
documentos.documentosreportegeneral.chart.title=Documents
documentos.documentosreportegeneral.chart.title=Documents
documentos.documentosreportegeneral.chart.nocontrolado=NO CONTROL
documentos.documentosreportegeneral.chart.inactivo=INACTIVES
documentos.documentosreportegeneral.header=Document report
documentos.documentosreportegeneral.header=Document report
documentos.documentossecuencia.Borrar=Delete
documentos.documentossecuencia.Clave=ID
documentos.documentossecuencia.ElDocumentoNoPudoSerBorrado=The document could not be deleted.
documentos.documentossecuencia.Enautorizacion=Authorizing
documentos.documentossecuencia.Est=Status
documentos.documentossecuencia.EstadoEnautorizacionAccionCrearsecuencia=Status: Authorizing  Action: Create sequence
documentos.documentossecuencia.Estatus=Status:
documentos.documentossecuencia.Fechadeentrada=Entrance date
documentos.documentossecuencia.header.CONSULTADEDOCUMENTOSSINSECUENCIADEAUTORIZACION=LIST OF DOCUMENTS WITHOUT SEQUECE FOR AUTHORIZATION
documentos.documentossecuencia.Nombre=Name:
documentos.documentossecuencia.Nombredocumento=Name
documentos.documentossecuencia.NoseencontrarondocumentosconelnombreespecificadoCambieelnombreeintentedenuevo=No documents were found with the specified name. Change the name and try again.
documentos.documentossecuencia.Originador=Originator
documentos.documentossecuencia.Razonporlaqueserechazalasecuenciadeldocumento=Reason for rejecting the sequence of document.
documentos.documentossecuencia.Rechazar.tooltip=Reject Document Sequence
documentos.documentossecuencia.Rechazar=Reject
documentos.documentossecuencia.Searechazadoiniciarlasecuenciadelasolicituddeldocumentoyseenvioelavisoaloriginadordedichodocumento=The sequence of the application of the document has been rejected, a notice has been sent to the originator of the document.
documentos.documentossecuencia.Seharechazadoiniciarlasecuenciadelasolicituddeldocumentoyseenvioelavisoaloriginadordedichodocumento=It has rejected to start the sequence request and are sending the notice to the originator of the document.
documentos.documentossecuencia.Ver=View
documentos.documentossecuencia.Verdocumento=Show document
documentos.galeria.-Anterior=-Previous
documentos.galeria.Busqueda=Search
documentos.galeria.CLAVE= ID:
documentos.galeria.Clave= ID:
documentos.galeria.DESCRIPCION= DESCRIPTION:
documentos.galeria.Descripcion= Description:
documentos.galeria.Nohayalbumesafregados= There is no added albums.
documentos.galeria.Nohayimagenesenestealbum=There are no images in this album.
documentos.galeria.Seleccioneunalbumdelaizquierda=Select an album on the left.
documentos.galeria.Siguiente-=Next-
documentos.galeria.TITULO= TITLE:
documentos.galeria.Titulo= Title:
documentos.listarelaciones.(Autorizacion)=(Authorization)
documentos.listarelaciones.(Edicion)=(Version)
documentos.listarelaciones.(Revision)=(Revision)
documentos.listarelaciones.DocumentoCancelado=Cancelled Document
documentos.listarelaciones.DocumentoDescontinuado=Discontinued Document
documentos.listarelaciones.header.LISTARELACIONES=RELATIONS LIST
documentos.LugarDeAlmacenamiento=Storage Area
documentos.Noseencontrarondocumentos=No documents avaliable.
documentos.papelara.documentoNoControlado=Uncontrolled Documents
documentos.papelera.documentoControlado=Controlled Documents
documentos.papelera.Titulo=Title
documentos.referenciahandle.Nodoareferenciar=Referencing Node:
documentos.referenciahandle.REFERENCIACIONDEDOCUMENTOS=REFERENCING DOCUMENTS
documentos.secuenciahandle.Agregarusuarioalasecuencia=Add user to sequence
documentos.secuenciahandle.Agregatemplatealasecuencia=Add template to sequence
documentos.secuenciahandle.Autorizacion=Authorization:
documentos.secuenciahandle.AUTORIZADA=AUTHORIZED
documentos.secuenciahandle.Borrar=Delete
documentos.secuenciahandle.Borrarusuariodelasecuencia=Delete user of sequence
documentos.secuenciahandle.boton.Autorizardirectamente=Authorize directly
documentos.secuenciahandle.boton.Borrartodos=Delete all
documentos.secuenciahandle.boton.Grabarsecuencia=Save sequence
documentos.secuenciahandle.Comentario\:=Note:
documentos.secuenciahandle.Comentario=Note
documentos.secuenciahandle.Documentosaautorizar=Documents to authorize
documentos.secuenciahandle.Documentosinsecuencia=Documents without sequence
documentos.secuenciahandle.Elusuarioyaseencuentraenlasecuencia=The user is already exist in the sequence
documentos.secuenciahandle.ENPROCESO=IN PROCCES
documentos.secuenciahandle.Estado=Status
documentos.secuenciahandle.Fecha=Date
documentos.secuenciahandle.FechaCreacion=Creation Date
documentos.secuenciahandle.FechaLimite=Limit date
documentos.secuenciahandle.header.SECUENCIADEAUTORIZACIONES=SEQUENCES OF AUTHORIZATION
documentos.secuenciahandle.header.SECUENCIADECANCELACION=SEQUENCES OF CANCELATION
documentos.secuenciahandle.header.SECUENCIADEDOCUMENTONUEVO=SEQUENCES OF NEW DOCUMENTS
documentos.secuenciahandle.header.SECUENCIADEMODIFICACION=SEQUENCES OF MODIFICATION
documentos.secuenciahandle.header.SECUENCIADEREAPROBACION=SEQUENCES OF RE-APPROVAL
documentos.secuenciahandle.Irasolicitudrelacionada=Go to related request
documentos.secuenciahandle.javascript.Agregarusuarioalasecuencia=Add user
documentos.secuenciahandle.javascript.Comentario=- Comment
documentos.secuenciahandle.javascript.Eldocumentohasidoautorizado=The document has been authorized.
documentos.secuenciahandle.javascript.Eliminaratodaslasasignaciones=The assignations will be removed!
documentos.secuenciahandle.javascript.Eliminarlaasignacion=Delete the assigment?
documentos.secuenciahandle.javascript.Fechalimite=- Due date
documentos.secuenciahandle.javascript.LaFechaNoPuedeSerAyer=The date has to be actual or future. Please change it.
documentos.secuenciahandle.javascript.Lasecuenciahasidodadadealta=The sequence has been created.
documentos.secuenciahandle.javascript.Lasecuenciahasidodadadealta=The sequence was created.
documentos.secuenciahandle.javascript.LosDatosdelasecuenciahansidoactualizados=The sequence was updated
documentos.secuenciahandle.javascript.Lossiguientesdatossonnecesarios=The following items are necessary:
documentos.secuenciahandle.javascript.Operacionexitosa=Transaction successful.
documentos.secuenciahandle.javascript.Seguro=Are you sure?
documentos.secuenciahandle.javascript.-Seleccioneunusuario=- Select one user
documentos.secuenciahandle.javascript.Usuario=- User
documentos.secuenciahandle.LASECUENCIAYAFUECOMPLETADA=COMPLETE SEQUENCE
documentos.secuenciahandle.NOAUTORIZADA=NO AUTHORIZED
documentos.secuenciahandle.NOAUTORIZO=NO AUTHORIZE
documentos.secuenciahandle.Nombredeltemplate=Template name:
documentos.secuenciahandle.Nosehanasignadousuarios=No users assigned
documentos.secuenciahandle.Nosepudorealizarlaoperacionanterior=Last transaction unsuccessful
documentos.secuenciahandle.Orden=Order
documentos.secuenciahandle.PROGRAMADA=PROGRAMMED
documentos.secuenciahandle.Seactualizocorrectamentelacarpetadestino=The destination folder was successfully updated.
documentos.secuenciahandle.SecuenciaActual=Sequence:
documentos.secuenciahandle.--Seleccione--=-- SELECT --
documentos.secuenciahandle.SIAUTORIZO=AUTHORIZE
documentos.secuenciahandle.Solicitudrelacionada=Related request:
documentos.secuenciahandle.Titulodeldocumento=Document name:
documentos.secuenciahandle.Usarestasecuenciacomoplantilla=Use this sequence as a template.
documentos.secuenciahandle.Usuario\:=User:
documentos.secuenciahandle.Usuario=User
documentos.secuencialist.ABIERTA=The actual status is OPEN.
documentos.secuencialist.Atrasada=Backward.
documentos.secuencialist.AutorizanteSiguiente= Next Authorizing
documentos.secuencialist.BorrarlaSecuencia=Delete sequence
documentos.secuencialist.CERRADA=The actual status is CLOSED.
documentos.secuencialist.Clave=ID
documentos.secuencialist.ClaveDelDocumento= Document ID
documentos.secuencialist.Documento\:=Document:
documentos.secuencialist.Documento=Document
documentos.secuencialist.Estado\:=Status:
documentos.secuencialist.Estado=Status
documentos.secuencialist.FechaCreacion=Creation date
documentos.secuencialist.Fechafinaldecreacion=Creation final date:
documentos.secuencialist.Fechainiciodecreacion=Creation start date:
documentos.secuencialist.header.CONTROLDESECUENCIASDEAUTORIZACION=AUTHORIZATION SEQUENCES CONTROL
documentos.secuencialist.javascript.Eldocumentohasidoautorizado=The document has been authorized.
documentos.secuencialist.javascript.Lasecuenciaseraborradadelsistema=The sequence will be removed !
documentos.secuencialist.javascript.Seguro=Are you sure?
documentos.secuencialist.LaSecuenciaNOsepuedeborrar=The sequence can\'t be removed
documentos.secuencialist.LasecuenciasAbiertasnopuedenserborradas=The open sequence can\'t be removed
documentos.secuencialist.Limite= Limit
documentos.secuencialist.Noseencontraronsecuenciasconelestadoindicado=No sequences were found
documentos.secuencialist.Nosepudorealizarlaoperacionanterior=Transaction unsuccessful
documentos.secuencialist.Sehaeliminadolasecuencia=The sequence has been removed
documentos.secuencialist.---Todos---=-- ALL --
documentos.slideshow.de=of
documentos.slideshow.DesplegandoMedio=Displaying Medium
documentos.solicitudeshandle.Actualizar=Update
documentos.solicitudeshandle.Agregar=Add
documentos.solicitudeshandle.Archivo=File:
documentos.solicitudeshandle.Archivo=File:
documentos.solicitudeshandle.Archivosubido=Uploaded file:
documentos.solicitudeshandle.Archivosubido=Uploaded file:
documentos.solicitudeshandle.BajarArchivo=Download file:
documentos.solicitudeshandle.BajarArchivo=Download file:
documentos.solicitudeshandle.Bajardocumento=Download document
documentos.solicitudeshandle.Bajardocumento=Download document
documentos.solicitudeshandle.boton.CancelarDocumento=Cancel Document
documentos.solicitudeshandle.boton.CancelarDocumento=Cancel Document
documentos.solicitudeshandle.boton.CrearSecuencia=Create Sequence
documentos.solicitudeshandle.boton.CrearSecuencia=Create Sequence
documentos.solicitudeshandle.boton.MarcarcomoAtendida=Set as Attended
documentos.solicitudeshandle.boton.MarcarcomoAtendida=Set as Attended
documentos.solicitudeshandle.boton.RechazarSolicitud=Reject Request
documentos.solicitudeshandle.Cancelacion=Cancelation
documentos.solicitudeshandle.Cancelacion=Cancellation
documentos.solicitudeshandle.ClavedelDocumento=ID:
documentos.solicitudeshandle.DocumentoNuevo=New document
documentos.solicitudeshandle.Edicion=Version
documentos.solicitudeshandle.Fechadesolicitud=Request date
documentos.solicitudeshandle.header.SOLICITUDDEEDICIONOMODIFICACIONDEDOCUMENTOS=DOCUMENTS MODIFICATION REQUEST
documentos.solicitudeshandle.header.SOLICITUDDEEDICIONOMODIFICACIONDEDOCUMENTOS-CLAVE=DOCUMENTS MODIFICATION REQUEST - ID:
documentos.solicitudeshandle.javascript.Archivo=- File
documentos.solicitudeshandle.javascript.Clavedeldocumento=- ID
documentos.solicitudeshandle.javascript.LaactualizaciondeldocumentohasidopuestaenesperadeRevision=Document\\'s update is in Revision.
documentos.solicitudeshandle.javascript.Lossiguientesdatossonnecesarios=The following items are required:
documentos.solicitudeshandle.javascript.NuevaSolicitud=New request
documentos.solicitudeshandle.javascript.Razondesolicitud=- Request reason
documentos.solicitudeshandle.javascript.Sehacanceladoeldocumento=Document has been cancelled.
documentos.solicitudeshandle.javascript.Sehacanceladoeldocumentoymarcadolasolicitudcomoatendida=Document has been cancelled and has been set as attended.
documentos.solicitudeshandle.javascript.Sehahecholasolicitudconexito=Transaction successful
documentos.solicitudeshandle.javascript.Sehamarcadolasolicitudcomoatendida=The request has been set as attended.
documentos.solicitudeshandle.javascript.Titulodeldocumento=- Title of the document
documentos.solicitudeshandle.javascript.VerSolicitudes=Show request
documentos.solicitudeshandle.Modificacion=Modification
documentos.solicitudeshandle.Modificacion=Modification
documentos.solicitudeshandle.onosepudolocalizarelarchivoindicado=or the file was not found
documentos.solicitudeshandle.onosepudolocalizarelarchivoindicado=or the indicated file couldn\'t locate.
documentos.solicitudeshandle.Personaquesolicita=Author:
documentos.solicitudeshandle.RazondeSolicitud=Request Reason:
documentos.solicitudeshandle.RazondeSolicitud=Request Reason:
documentos.solicitudeshandle.RazonporlaquelaSolicitudfuerechazada=Reason for the request to be rejected
documentos.solicitudeshandle.Re-aprobacion=Re-approval
documentos.solicitudeshandle.Re-aprobacion=Re-approval
documentos.solicitudeshandle.Rechazar=Reject
documentos.solicitudeshandle.Subirarchivo=Upload file:
documentos.solicitudeshandle.Subirarchivo=Upload file:
documentos.solicitudeshandle.Tipodesolicitud=Request type:
documentos.solicitudeshandle.Tipodesolicitud=Request type:
documentos.solicitudeshandle.TitulodelDocumento=Title:
documentos.solicitudeshandle.Yaexistepreviamenteelnombredelarchivoelegido=Already exists previously the selected file name.
documentos.solicitudeshandle.Yaexistepreviamenteelnombredelarchivoelegido=The file's name already exist
documentos.solicitudeshandle.Yaexisteunarchivoconelmismonombre=Document with the same title already exists
documentos.solicitudeshandle.Yaexisteunarchivoconelmismonombre=Document with the same title already exists
documentos.solicitudeshandle.Yaexisteunasolicitudhaciaesedocumento=The document has one request attached already.
documentos.solicitudeshandle.Yaexisteunasolicitudhaciaesedocumento=The document has one request attached already.
documentos.solicitudeslist.Atendida=Attended
documentos.solicitudeslist.Atendida=The actual status is ATTENDED.
documentos.solicitudeslist.Borrar=Erase
documentos.solicitudeslist.Borrar=Reject
documentos.solicitudeslist.Borrarsolicitud=Reject request
documentos.solicitudeslist.Borrarsolicitud=Reject request
documentos.solicitudeslist.BorrarSolicitud=Reject Request
documentos.solicitudeslist.Busquedaporestado=Search by Status:
documentos.solicitudeslist.Busquedaporestado=Search by Status:
documentos.solicitudeslist.Cerrada=Closed
documentos.solicitudeslist.Cerrada=The actual status is CLOSED.
documentos.solicitudeslist.Clave=ID
documentos.solicitudeslist.Estado=Status
documentos.solicitudeslist.Estado=Status
documentos.solicitudeslist.Fecha=Date
documentos.solicitudeslist.Fecha=Date
documentos.solicitudeslist.Fechafin=Finish date
documentos.solicitudeslist.Fechainicio=Start date
documentos.solicitudeslist.header.LISTADODESOLICITUDES=REQUESTS LIST
documentos.solicitudeslist.header.LISTADODESOLICITUDES=REQUEST'S LIST
documentos.solicitudeslist.javascript.Deseacerrarlasolicitud=Do you want to close the request?
documentos.solicitudeslist.javascript.Deseacerrarlasolicitud=Set request as closed?
documentos.solicitudeslist.javascript.Lasolicitudseraborradadelsistema=The request will be removed!
documentos.solicitudeslist.javascript.Lasolicitudseraborradadelsistema=The request will be removed!
documentos.solicitudeslist.Marcarcomocerrada=: Set Closed
documentos.solicitudeslist.Marcarcomocerrada=Press to mark as CLOSED.
documentos.solicitudeslist.Nosehaeliminadolasolicitud=The request can\'t be removed.
documentos.solicitudeslist.Nosehaeliminadolasolicitud=The request cannot be removed.
documentos.solicitudeslist.Sehaeliminadolasolicitud=The request was removed
documentos.solicitudeslist.Sehaeliminadolasolicitud=The request was removed
documentos.solicitudeslist.Solicitada=Requested
documentos.solicitudeslist.Solicitada=The actual status is REQUESTED.
documentos.solicitudeslist.Solicitante=Requested
documentos.solicitudeslist.Solicitante=Requested
documentos.solicitudeslist.Tipo=Type
documentos.solicitudeslist.Tipo=Type
documentos.solicitudeslist.--TODOS--=-- ALL --
documentos.solicitudeslist.--TODOS--=--ALL--
documentos.solicitudeslist.Ver=View
documentos.solicitudeslist.Ver=View
documentos.solicitudeslist.Verlasolicitud=: Show the request
documentos.solicitudeslist.Verlasolicitud=\ Press to show the detail.
documentos.solicitudeslist.VerSolicitud=View Request
documentos.subir.Porfavor,seleccioneeltrayectodelficheroacargar=Please, select the path of the file to load
documentos.tiempoDeRetencion=Retention Time
documentoshandle.Ver=View
documentospendientes.header.PendientesAcciones=Action - tasks
documentospendientes.header.PendientesAuditorias=Audit - tasks
documentospendientes.header.PendientesDocumento=Document - tasks
documentospendientes.header.PendientesIndicadores=Indicators - tasks
documentospendientes.header.PendientesProyectos=Project - tasks
documentosproyectos.listasdocumentos.aceptar=Accept
documentosproyectos.listasdocumentos.borrar=Delete
documentosproyectos.listasdocumentos.esconder=Hide
documentosproyectos.listasdocumentos.mover=Move
encuesta.elvalQuestionEdit.Lossiguientescampossonnecesarios=The following fields are required:
encuesta.elvalQuestionEdit.-RedactelaPregunta=   -Please state the question.
error.ErrorNoSePuedeMostrarElTexto=Error: The text cannot be shown
error.ErrorNoSePuedeMostrarElTexto=Error: The text cannot be shown
evaluacion.encuestas.Agregarcomentario=Add comment
evaluacion.encuestas.Anticipacionparamandaravisos=To send advance notices:
evaluacion.encuestas.B\u00fasquedaavanzada=Advanced Search
evaluacion.encuestas.Cancelarencuesta=Cancel Survey
evaluacion.encuestas.Cerrarencuesta=Close Survey
evaluacion.encuestas.Clave=ID
evaluacion.encuestas.Cuestionariodeencuesta=Survey Questionnarie.
evaluacion.encuestas.Cuestionariodeencuestas=Survey Questionnarie:
evaluacion.encuestas.Debeescojerparticipantes=Participants must choose.
evaluacion.encuestas.Descripcion=Description
evaluacion.encuestas.ENCUESTA=SURVEYS
evaluacion.encuestas.Fechadeinicio=Start Date
evaluacion.encuestas.Fechafin=End Date
evaluacion.encuestas.Guardar=Save
evaluacion.encuestas.Horadeinicio=Start time:
evaluacion.encuestas.Horafin=End Time:
evaluacion.encuestas.lossiguientescampossonnecesarios=The follow fields are required:
evaluacion.encuestas.Participantesdelaencuesta=Survey participants:
evaluacion.encuestas.Regresar=Back
evaluacion.encuestas.Regresaramisencuestas=Back to my Surveys
evaluacion.encuestas.Responsabledelaencuesta=Responsible for the survey.
evaluacion.encuestas.SELECCIONE=-- SELECT --
evaluacion.encuestas.titulo=Title
evaluacion.encuestas.Tituloencuesta=Survey Title
evaluacion.encuestas.Vercuestionario=View questionnaire
evaluacion.encuestas.Verifiquequelahoradeinicioseamenoralahoradefin=Verify that the start time is less the final time.
evaluaciones.ecuestas.responsabledelaencuesta=Responsible for the survey:
evaluaciones.encuestas.titulo=Title:
evaluaciones.encuestashandle.Agregar/QuitarParticipantes=Add / Remove Participants
evaluaciones.encuestashandle.AgregarComentario=Add Comment
evaluaciones.encuestashandle.Anticipacionparamandaravisos=Anticipation to send notices:
evaluaciones.encuestashandle.CancelarEncuesta=Cancel Survey
evaluaciones.encuestashandle.CerrarEncuesta=Close Survey
evaluaciones.encuestashandle.Clave=ID:
evaluaciones.encuestashandle.Comentario=Comment:
evaluaciones.encuestashandle.Comentarios=Comments:
evaluaciones.encuestashandle.ComentarioSolo=Comment
evaluaciones.encuestashandle.Consultar=Consult
evaluaciones.encuestashandle.CuestionarioActivo=Active Questionnaire:
evaluaciones.encuestashandle.-CuestionariodeEncuesta=- Survey questionnaire.
evaluaciones.encuestashandle.CuestionariodeEncuesta=Survey Questionnaire
evaluaciones.encuestashandle.CUESTIONARIODEENCUESTA=SURVEY QUESTIONNAIRE
evaluaciones.encuestashandle.-Debeescpjerparticipantes=- Must choose participants.
evaluaciones.encuestashandle.Debesescribiruncomentario=You must write a comment.
evaluaciones.encuestashandle.-Descripcion=- Description.
evaluaciones.encuestashandle.Descripcion=Description:
evaluaciones.encuestashandle.DETALLESDELAENCUESTA=SURVEY DETAILS
evaluaciones.encuestashandle.Dias=day(s)
evaluaciones.encuestashandle.Editarcuestionario=Edit Questionnaire
evaluaciones.encuestashandle.EditarSecciones=Edit Sections:
evaluaciones.encuestashandle.Elcomentariohasidograbado=The comment has been saved.
evaluaciones.encuestashandle.Elnumerodepreguntasnopuedesercero=The Number of Questions can not be Zero
evaluaciones.encuestashandle.Elnumerodeseccionesnopuedesercero=The Number of Sections can no be Zero
evaluaciones.encuestashandle.ENCUESTA=SURVEYS
evaluaciones.encuestashandle.Estado=Status:
evaluaciones.encuestashandle.Estasegurodequedeseamodificarlosdatosdelaencuesta=Are you sure you want to modify the survey data?.
evaluaciones.encuestashandle.Fecha=Date
evaluaciones.encuestashandle.-Fechadeinicio=- Start date.
evaluaciones.encuestashandle.-Fechafin=- End date.
evaluaciones.encuestashandle.Fechafin=End Date:
evaluaciones.encuestashandle.FechaInicio=Start Date:
evaluaciones.encuestashandle.Guardar=Save
evaluaciones.encuestashandle.Horadeinicio=Start Time :
evaluaciones.encuestashandle.Horafin=End Time:
evaluaciones.encuestashandle.Laencuestahasidocancelada=The survey has been cancelled
evaluaciones.encuestashandle.Laencuestaseguardocorrectamente=The survey was saved successfully.
evaluaciones.encuestashandle.Laencuestasehacerrado=The survey has been closed
evaluaciones.encuestashandle.Lafechainiicaldebesermenoroigualalafechafinal=The starting date must be less than or equal to the end date.
evaluaciones.encuestashandle.Lossiguientescampossonnecesarios=The following items are required:
evaluaciones.encuestashandle.Participantesdelaencuesta=Participants of the survey:
evaluaciones.encuestashandle.ParticipantesdelaEncuesta=Participants of the Survey:
evaluaciones.encuestashandle.Raz\u00f3nporlacuallaencuestaser\u00e1cancelada=Reason for the survey will be cancelled.
evaluaciones.encuestashandle.Razonporlacuallaencuestaseracancelada=Reason for what the survey will be cancelled
evaluaciones.encuestashandle.Regresar=Back
evaluaciones.encuestashandle.-Responsabledelaencuesta=- Responsible for the survey.
evaluaciones.encuestashandle.Responsabledelaencuesta=Responsible for the survey:
evaluaciones.encuestashandle.Seccion=Section
evaluaciones.encuestashandle.--SELECCIONE--=--SELECT--
evaluaciones.encuestashandle.-Titulo=- Title.
evaluaciones.encuestashandle.Titulo=Title:
evaluaciones.encuestashandle.Usuarios=Users
evaluaciones.encuestashandle.Ver=View
evaluaciones.encuestashandle.Verifiquequelafechaseaposterioroigualaldiadehoy=Verify that the date is late than or equal to the present day.
evaluaciones.encuestashandle.Verifiquequelafechasseaposterioroigualaldiadehoy=Verify the date (s) is later than or equal to the day of today.
evaluaciones.encuestashandle.-Verifiquequelahoradeiniicoseamenoralahoradefin=- Verify that the start time is less than the end time.
evaluaciones.encuestaslist.Activa,porresponder=Active, To respond
evaluaciones.encuestaslist.Activa=Active
evaluaciones.encuestaslist.Autor=Author:
evaluaciones.encuestaslist.Buscar=Search
evaluaciones.encuestaslist.Busqueda=Search
evaluaciones.encuestaslist.BusquedaAvanzada=Advanced Search
evaluaciones.encuestaslist.Cerrada=Closed
evaluaciones.encuestaslist.-CLAVE-=- ID -
evaluaciones.encuestaslist.Clave=ID
evaluaciones.encuestaslist.ContestarEncuesta=Answer Survey
evaluaciones.encuestaslist.Contestarencuesta=Answer Survey
evaluaciones.encuestaslist.Cuestionario=Questionnaire
evaluaciones.encuestaslist.CuestionarioPuntos=Questionnaire:
evaluaciones.encuestaslist.Encuestasactivasenlasqueparticipa=Active surveys where you participate:
evaluaciones.encuestaslist.Encuestasprogramadasenlasqueparticipa=Programmed  surveys where you participate:
evaluaciones.encuestaslist.Estado=Status:
evaluaciones.encuestaslist.EstadoPorCerrar=Status: To Close
evaluaciones.encuestaslist.Fechainicio=Start date
evaluaciones.encuestaslist.FechaInicio=Start Date
evaluaciones.encuestaslist.Fechatermino=End Date:
evaluaciones.encuestaslist.FechaTermino=End Date:
evaluaciones.encuestaslist.LISTADO=LIST
evaluaciones.encuestaslist.LISTADODEENCUESTASCREADAS=LIST OF SURVEYS CREATED
evaluaciones.encuestaslist.LISTADODEENCUESTASCREADASPORUSUARIO=LIST OF SURVEYS CREATED BY USER
evaluaciones.encuestaslist.MISENCUESTAS=MY SURVEYS
evaluaciones.encuestaslist.Nombreencuesta=Name
evaluaciones.encuestaslist.Nosepudoborrarelregistro= Could not delete the registry.
evaluaciones.encuestaslist.Presioneparaverlainformaciondelaencuesta=Click to view the survey information
evaluaciones.encuestaslist.Programada=Programmed
evaluaciones.encuestaslist.Respondercuestionario=Answer questionnaire
evaluaciones.encuestaslist.Respondida=Answered
evaluaciones.encuestaslist.Seborroelregistro=Registry was deleted.
evaluaciones.encuestaslist.Seborroelregistro1=The record is deleted.
evaluaciones.encuestaslist.---SELECCIONE---=--- SELECT ---
evaluaciones.encuestaslist.Terminada=Completed
evaluaciones.encuestaslist.Titulo=Title
evaluaciones.encuestaslist.Tituloencuesta=Survey Title
evaluaciones.encuestaslist.TituloEncuesta=Survey Title:
evaluaciones.encuestaslist.---TODOS---=--- ALL ---
evaluaciones.encuestaslist.Usuarioencuestado=Respondent user
evaluaciones.encuestaslist.UsuarioEncuestado=Respondent User:
evaluaciones.evalEdit.EDITARCUESTIONARIO=EDIT QUESTIONNAIRE
evaluaciones.evalEdit.LISTADODEPREGUNTAS=LIST OF QUESTIONS
evaluaciones.evalOptionCreate.Dependientes=Dependents
evaluaciones.evalOptionCreate.Opcion=Option
evaluaciones.evalOptionCreate.Opciones=Options
evaluaciones.evalOptionCreate.Ponderacion=Weighing
evaluaciones.evaluacioneshandle.Espereaquesecargenlassecciones=Wait to Load the Sections
evaluaciones.evaluacioneshandle.Ocurrioelsiguienteerror=The following error ocurred
evaluaciones.evaluacioneshandle.REDACCI\u00d3NDEENCUESTAS=SURVEY EDITION
evaluaciones.evaluacionlist.FechadeModificacion=Modification Date:
evaluaciones.evaluacionlist.Secambioelestadodelcuestionario= The test status has been changed
evaluaciones.questionCreate.Crealaspreguntasdelaevaluaci\u00f3n=Create assessment question
evaluaciones.questionCreate.CREARCUESTIONARIO=CREATE QUESTIONNAIRE
evaluaciones.questionCreate.Esobligatoria=Mandatory:
evaluaciones.questionCreate.Guardarycontinuar=Save and Continue
evaluaciones.questionCreate.Guardarycontinuar=Save and Continue
evaluaciones.questionCreate.Hasterminadolacreaci\u00f3ndelcuestionario=You have completed the creation of the questionnaire.
evaluaciones.questionCreate.Lacantidaddeopcionesnopuedeser0oestarenblanco=The number of options can not be 0 or be blank.
evaluaciones.questionCreate.Numerodeopciones=Number of options:
evaluaciones.questionCreate.Tipodepregunta=Question Type:
evaluaciones.reporteEncuestas.(Laencuestanoesponderada)=(The survey is not weighted)
evaluaciones.reporteEncuestas.*Laencuestagraficadaesunexamen=*The survey plotted is a test
evaluaciones.reporteEncuestas.*Laencuestagraficadanocuentaconponderacion=*The survey does not have weight plotted
evaluaciones.reporteEncuestas.*Laspreguntasnograficadassonpreguntasabiertas,camposdefechasyhora=*The questions not plotted are open questions, date and time fields.
evaluaciones.reporteEncuestas.*Solosemuestranencuestasenestatusterminada=*Only show completed status surveys
evaluaciones.reporteEncuestas.Encuesta*=Survey*:
evaluaciones.reporteEncuestas.Encuesta=Survey
evaluaciones.reporteEncuestas.Filtrodeparticipantes=Participants filter:
evaluaciones.reporteEncuestas.Grafica=Graph
evaluaciones.reporteEncuestas.Graficarpor=Graph by:
evaluaciones.reporteEncuestas.Graficarporcentajes=Graph percentage:
evaluaciones.reporteEncuestas.Graficarresultadospor=Graph results by:
evaluaciones.reporteEncuestas.REPORTEDEENCUESTAS=SURVEYS REPORT
evaluaciones.reporteEncuestas.--SELECCIONE--=-- SELECT --
evaluaciones.reporteEncuestas.TipodeReporte=Report Type:
evaluaciones.reporteEncuestas.--TODO--=-- ALL --
evaluaciones/questionCreate.Haselegidoelformatodeevaluaci\u00f3ndetipoMatrizporloquedebesprimeroseleccionar<br/>cuantasopcionestrendr\u00e1nlaspreguntas=Youhave chosen the format type assessment matrix so you should first select options trendr\u00e1n <br/> few questions.
grafica.grafica=Show options
grafica.graficaAvanzada.esconder=Hide
grafica.graficaAvanzada.mostrar=Show
grafica.graficaAvanzada=Advanced graph
gridGenerator.Busqueda=Search
gridGenerator.Mostrarfiltrodebusqueda=Show search filter
gridGenerator.Ocultarfiltrodebusqueda=Hide search filter
iconos.GraficaGantt=Gantt Chart
iconos.ReporteAvance=Progress Report
iconos.ReporteCambios=Change Report
iconos.ReporteGraficos=Reports and Graphics
iconos.ReportePeticionesPresupuesto=Budget Request Report
iconos.Text.GraficaGantt=Gantt
iconos.Text.ReporteAvance=Progress
iconos.Text.ReporteCambios=Change's Request
iconos.Text.ReportePeticionesPresupuesto=Budget's Requests
include.messagewindow.Aceptar=Accept
include.messagewindow.Cancelar=Cancel
include.messagewindow.Mensajedelsistema=Message of the system
includes.mensajedeespera.alt=Processing...
includes.mensajedeespera.alt=Processing...
includes.mensajedeespera.mensajeDeEspera=The information is being processed, <br> wait a moment please.
includes.mensajedeespera.mensajeDeEspera=The information is being processed, <br> wait a moment please.
includes.mensajedeespera.titulo=Processing information
indicador.indicadorhandle.ElijeAlmenosUnAreaALaCualReportar=-Select at least one process to report to.
indicador.levantamientoindicadorhandle.EsteIndicadorEsReportadoPorCarreraALaCarrera=This indicator is reported by area to the area
indicador.levantamientoindicadorhandle.fechaLimiteParaLaRevision=Limit date for revision:
indicador.levantamientoindicadorhandle.formula=Formula:
indicador.levantamientoindicadorhandle.Indicador=Indicator:
indicador.levantamientoindicadorhandle.Cancelar=It will be sent to outstanding indicators for rating and unsaved Are you sure you want to continue?
indicador.levantamientoindicadorhandle.objetivo=Objective:
indicador.levantamientoindicadorhandle.ProcesoQueSeCalifica=Process being evaluated:
indicador.levantamientoindicadorhandle.resultado=Result:
indicador.levantamientoindicadorhandle.RevisionDeIndicadores=INDICATOR CHECK
indicador.levantamientoindicadorhandle.UsuarioResponsable=User responsible:
indicador.misindicadores.indicadoresPorRegistrar=Unregistered Indicators:
indicador.objetivolist.controlDeObjetivosDeCalidad=CONTOL OF QUALITY OBJECTIVES
indicadores.indicadorespendientes.IndicadoresPorCalificar = Indicators to grade:
indicadores.indicadorespendientes.IndicadoresPorVerificar = Indicator to check:
indicadores.indicadoresreportegeneral.(CadaPeriodoGraficadoRepresentaUnaRevisionConSuRespectivaMeta)=(Each graphed period represents a revision with it's respective goal)
indicadores.indicadoresreportegeneral.boton.Limpiar=Clear
indicadores.indicadoresreportegeneral.departamento=Department:
indicadores.indicadoresreportegeneral.detalles=Details:
indicadores.indicadoresreportegeneral.Facultad=Business Unit:
indicadores.indicadoresreportegeneral.Facultad=Business Unit:
indicadores.indicadoresreportegeneral.Faltandatosporcapturar=Missing data to capture.
indicadores.indicadoresreportegeneral.FechaMenorIgualFechaFinal = - The start date must be less than or equal to the end date.\\n
indicadores.indicadoresreportegeneral.Fecha=Start Date:
indicadores.indicadoresreportegeneral.header=Indicators report
indicadores.indicadoresreportegeneral.IndicadorAGraficar=Indicator to graph:
indicadores.indicadoresreportegeneral.Objetivo=Objective:
indicadores.indicadoresreportegeneral.proceso=Process:
indicadores.indicadoresreportegeneral.reportarPor=Report by:
indicadores.indicadoresreportegeneral.ReportarPorPeriodosDeRevision=Report by revision periods:
indicadores.indicadoresreportegeneral.--Seleccione--=-- SELECT --
indicadores.indicadoresreportegeneral.Tipodegrafica=Graph type:
indicadores.indicadoresreportegeneral.--TODAS--=-- ALL --
indicadores.indicadoresreportegeneral.---TODAS---=----ALL---
indicadores.indicadoresreportegeneral.unidad=Area:
indicadores.indicadorhandle.AsignarResponsableNecesario = - You must assign at least one responsible.\\n
indicadores.indicadorhandle.AsignarResponsableUnidadNecesario = - You must assign at least one area.\\n
indicadores.indicadorhandle.Cada=Every
indicadores.indicadorhandle.CuantosMesesRevisionNecesario = - You must indicate each how months in the review.\\n
indicadores.indicadorhandle.CuantosMesNecesario = - You must indicate each how many months.
indicadores.indicadorhandle.CuantosMesRevisionNecesario = \\n - You must indicate each how many months.\\n
indicadores.indicadorhandle.Cuarto=Fourth
indicadores.indicadorhandle.DatosNecesarios = The following data are necessary: \\n
indicadores.indicadorhandle.decada=of each
indicadores.indicadorhandle.limpiarCampos = Are you sure you want to clear the fields?
indicadores.indicadorhandle.Cancelar = It will be sent to the control indicators screen and any unsaved changes will be lost <br>Are you sure you want to continue?
indicadores.indicadorhandle.DepartamentoEngargadoRevisionesNecesario = - You must assign a department reviews.\\n
indicadores.indicadorhandle.DepartamentoNecesario = - You must assign a department.\\n
indicadores.indicadorhandle.DepartamentoPeriodicidadRevisionesNecesario = \\n - You must select at least one day of the week in review.\\n
indicadores.indicadorhandle.DepartamentoProcesoNecesario = - It is necessary to specify a department.\\n
indicadores.indicadorhandle.DiaMesNecesario = - You must indicate which day of the month.\\n
indicadores.indicadorhandle.DiaNecesario = - You must select at least one weekday.\\n
indicadores.indicadorhandle.dias=days
indicadores.indicadorhandle.DiasAnticipacionNecesario = - You should assign the days advance.\\n
indicadores.indicadorhandle.Domingo=Sunday
indicadores.indicadorhandle.El=The
indicadores.indicadorhandle.Eldia=The day
indicadores.indicadorhandle.-estemensajenodebemostrarse-=-this message should not be displayed-
indicadores.indicadorhandle.FechaLevanmientoVerificar =- Verify dates:\\n
indicadores.indicadorhandle.FechaLevanmientoVerificarRevision = - You must provide a date for the first review.\\n
indicadores.indicadorhandle.FechaMenorIgualRevisionVerificar = - The date of uprising must be less than or equal to the date of review.\\n
indicadores.indicadorhandle.FechaPosteriorIgualLevanmientoVerificar = - Verify that the date of the uprising is greater than or equal to the day of today.\\n
indicadores.indicadorhandle.FechaPosteriorIgualRevisionVerificar = - Verify that the date of review is greater than or equal to the day of today.\\n
indicadores.indicadorhandle.FechaPrimerLevanmientoVerificar = - must provide a date for the first uprising.\\n
indicadores.indicadorhandle.FormulaNecesario = - You must type a formula.\\n
indicadores.indicadorhandle.FuenteInformacionNecesario = - You must assign a source of information for the indicator.\\n
indicadores.indicadorhandle.IndicadorActivo = Active
indicadores.indicadorhandle.IndicadorActualizado = The indicator has been updated successfully
indicadores.indicadorhandle.IndicadorAlta = The indicator has been created successfully.
indicadores.indicadorhandle.IndicadorDepartamento = Department:
indicadores.indicadorhandle.IndicadorDescripcionMeta = Goal description:
indicadores.indicadorhandle.IndicadorDiaria = Daily
indicadores.indicadorhandle.IndicadorDiasAnticipacionProgramar = Days of anticipation to program:
indicadores.indicadorhandle.IndicadorDptoEncargadoRevisiones = Department responsible of inspections:
indicadores.indicadorhandle.INDICADORES=Indicators
indicadores.indicadorhandle.IndicadorEstatus = Status indicator:
indicadores.indicadorhandle.IndicadorFacultad = ${Facility}:
indicadores.indicadorhandle.IndicadorFechaPrimeraRevision = Date for the first review:
indicadores.indicadorhandle.IndicadorFormula = Formula:
indicadores.indicadorhandle.IndicadorFuenteInformacion = Source of information:
indicadores.indicadorhandle.IndicadorMaximo = Maximum
indicadores.indicadorhandle.IndicadorMensual = Monthly
indicadores.indicadorhandle.IndicadorMeta = Goal:
indicadores.indicadorhandle.IndicadorMinimo = Minimum
indicadores.indicadorhandle.IndicadorObjetivo = Objective:
indicadores.indicadorhandle.IndicadorPeriodicidad = Cycle:
indicadores.indicadorhandle.IndicadorPeriodicidadRevision = Inspection cycle:
indicadores.indicadorhandle.IndicadorPorDias = Daily
indicadores.indicadorhandle.IndicadorPorMes = Monthly
indicadores.indicadorhandle.IndicadorPorPromedio = By average
indicadores.indicadorhandle.IndicadorPorSemanas = Weekly
indicadores.indicadorhandle.IndicadorPorSumatoria = By addition
indicadores.indicadorhandle.IndicadorPrimerLevantamiento = Date of first capture:
indicadores.indicadorhandle.IndicadorProceso = Process:
indicadores.indicadorhandle.IndicadorReportarPor = Report by:
indicadores.indicadorhandle.IndicadorResponsable = Responsible
indicadores.indicadorhandle.IndicadorResponsableReportar = Responsible of reporting:
indicadores.indicadorhandle.IndicadorSelecciona = --SELECT--
indicadores.indicadorhandle.IndicadorSemanal = Weekly
indicadores.indicadorhandle.IndicadorTipoAgrupamiento = Type of grouping for reviews and reports:
indicadores.indicadorhandle.IndicadorTitulo = Title of indicator:
indicadores.indicadorhandle.IndicadorUnidad= Area
indicadores.indicadorhandle.IndicadorUnidadesReportar = Areas to report:
indicadores.indicadorhandle.AdvertenciaCampoNoEditable = This field can only be edited when the Indicator is inactive.
indicadores.indicadorhandle.Jueves=Thursday
indicadores.indicadorhandle.Lunes=Monday
indicadores.indicadorhandle.Martes=Tuesday
indicadores.indicadorhandle.mes(es)=month(s)
indicadores.indicadorhandle.MetaNecesario = - You must type a minimum or maximum target.\\n
indicadores.indicadorhandle.Miercoles=Wednesday
indicadores.indicadorhandle.ObjetivoNecesario = - You must assign an objective.\\n
indicadores.indicadorhandle.Primer=First
indicadores.indicadorhandle.ProcesoNecesario = - You must assign a process.\\n
indicadores.indicadorhandle.RevisionDiaDelMesNecesario = - You must indicate which day of the month of review.\\n
indicadores.indicadorhandle.Sabado=Saturday
indicadores.indicadorhandle.Segundo=Second
indicadores.indicadorhandle.semanaslos=weeks the:
indicadores.indicadorhandle.Tercer=Third
indicadores.indicadorhandle.TituloNecesario = - You must assign a title to the indicator.\\n
indicadores.indicadorhandle.Ultimo=Last
indicadores.indicadorhandle.valorSeparacionNecesario = - You must add some value for the separation.\\n
indicadores.indicadorhandle.valorSeparacionRevisionNecesario = - You must add some value for the separation of the review.\\n
indicadores.indicadorhandle.Viernes=Friday
indicadores.indicadorlist.Activo=Active
indicadores.indicadorlist.Aunnohayobjetivoscapturados=There is no objectives captured.
indicadores.indicadorlist.Borrar =Delete
indicadores.indicadorlist.ControlIndicadores =CONTROL OF INDICATORS
indicadores.indicadorlist.Editar =Edit
indicadores.indicadorlist.Estado =Status
indicadores.indicadorlist.Inactivo=Inactive
indicadores.indicadorlist.Indicador =Indicator
indicadores.indicadorlist.MensajeEliminar =The indicator will be deleted and there will be no way to recover it. To do this?
indicadores.indicadorlist.Monitoreo =Monitoring
indicadores.indicadorlist.Objetivo =Objective
indicadores.indicadorlist.Proceso =Process
indicadores.indicadorlist.Seeliminaraelindicadoryyanohabramaneraderecuperarlo.\u00bfDesearealizarestaaccion?=The indicator will be deleted and there will be no way to recover it. To do this?
indicadores.indicadorregistroslist.Borrar =Delete
indicadores.indicadorregistroslist.ControlRegistros =REGISTRIES CONTROL
indicadores.indicadorregistroslist.Estado =Status
indicadores.indicadorregistroslist.EstadoCALIFICADA=Status: QUALIFIED
indicadores.indicadorregistroslist.EstadoENESPERA,calificarloslevantamientos=Status: WAITING, qualify the lifting.
indicadores.indicadorregistroslist.EstadopendientePORREVISAR=Status: pending TO CHECK.
indicadores.indicadorregistroslist.EstadoPORCALIFICAR=Status: TO QUALIFY.
indicadores.indicadorregistroslist.EstadoPROGRAMADA,aunnollegalafechaprogramada=Status: Programmed, Not yet is the programmed date.
indicadores.indicadorregistroslist.EstadoREVISADA=Status: REVISED
indicadores.indicadorregistroslist.Fecha =Date
indicadores.indicadorregistroslist.Indicador =Indicator
indicadores.indicadorregistroslist.IndicadoresNoProgramados =There is no scheduled indicators.
indicadores.indicadorregistroslist.IndicadoresNoRevision =There is no scheduled indicators reviews.
indicadores.indicadorregistroslist.LevantamientoIndicadores =Lifting of indicators:
indicadores.indicadorregistroslist.LevantamientosdeIndicador=Lifting of indicators:
indicadores.indicadorregistroslist.MensajeEliminar =The lifting of the indicator will be deleted and there will be no way to recover it. To do this?
indicadores.indicadorregistroslist.MensajeRevisionEliminar =The revision of the indicator will be deleted and there will be no way to recover it. To do this?
indicadores.indicadorregistroslist.Nohayindicadoresprogramados=There is no programmed indicators.
indicadores.indicadorregistroslist.Proceso =Process
indicadores.indicadorregistroslist.RevisionBorrar =Delete
indicadores.indicadorregistroslist.Revisionesdeindicadores=Indicators Revisions:
indicadores.indicadorregistroslist.RevisionEstado =Status
indicadores.indicadorregistroslist.RevisionFecha =Date
indicadores.indicadorregistroslist.RevisionIndicador =Indicator
indicadores.indicadorregistroslist.RevisionIndicadores =Reviews of indicators:
indicadores.indicadorregistroslist.RevisionProceso =Process
indicadores.indicadorregistroslist.RevisionUsuarioMonitoreo =Monitoring
indicadores.indicadorregistroslist.RevisionVer =View
indicadores.indicadorregistroslist.Seeliminaraellevantamientodelindicadoryyanohabramaneraderecuperarlo=The lifting of the indicator will be deleted and there will be no way to recover it. To do this?
indicadores.indicadorregistroslist.Seeliminaralarevisiondelindicadoryyanohabramaneraderecuperarla.\u00bfDesearealizarestaaccion?=The revision of the indicator will be deleted and there will be no way to recover it. To do this?
indicadores.indicadorregistroslist.UsuarioCarrera =User/Area
indicadores.indicadorregistroslist.Ver =View
indicadores.levantamientoindicadorhandle.*Fechadelevantamiento=*Lifting date.
indicadores.levantamientoindicadorhandle.*Resultadodelevantamiento=*Lifting results.
indicadores.levantamientoindicadorhandle.Aceptar=Accept
indicadores.levantamientoindicadorhandle.Elindicadorsehacalificadocorrectamente=The indicator has been evaluated correctly.
indicadores.levantamientoindicadorhandle.Lossiguientesdatossonnecesarios=The following data are necessary:
indicadores.misindicadores.carrera=Area
indicadores.misindicadores.estado=Status
indicadores.misindicadores.fecha=Date
indicadores.misindicadores.indicador=Indicator
indicadores.misindicadores.IndicadoresPorRegistrar:Idicators pending to register:
indicadores.misindicadores.Indicadoresporrevisar=Indicators to check:
indicadores.misindicadores.misIndicadores=MY INDICATORS
indicadores.misindicadores.NoTieneIndicadoresProgramados=There are no pending indicators.
indicadores.misindicadores.NoTieneRevisionesDeIndicadoresProgramadas= no revisions of programed indicators.
indicadores.misindicadores.proceso=Process
indicadores.misindicadores.ver=View
indicadores.objetivohandle.Descripcion=Description:
indicadores.objetivohandle.Cancelar=Unsaved changes will be lost. Are you sure you want to cancel?
indicadores.objetivohandle.Elobjetivohasidocreado=The objective has been created
indicadores.objetivohandle.Losdatosdelobjetivohansidoactualizados=The data of the objective have been updated
indicadores.objetivohandle.OBJETIVOS=OBJECTIVES
indicadores.objetivohandle.Titulo=Title:
indicadores.objetivolist.Activo=Active
indicadores.objetivolist.Borrar =Delete
indicadores.objetivolist.Estado =Status
indicadores.objetivolist.NoHayObjetivosCapturados =There are still not captured objectives.
indicadores.objetivolist.Objetivo =Objective
indicadores.objetivolist.Seeliminaraelobjetivoyyanohabramaneraderecuperarlo.\u00bfDesearealizarestaaccion?=The objective will be deleted and there will be no way to recover it. To do this?
indicadores.objetivolist.Ver =View
indicadores.revisionindicadorhandle.Indicador= Meter
indicadores.revisionindicadorhandle.*Daracciondealta=*Create a new action
indicadores.revisionindicadorhandle.Aceptar=Accept
indicadores.revisionindicadorhandle.AunNoSeCumpleLaFechaDeRevisionFavorDeEsperarALaFechaIndicada=The revision date has not been reached, please wait for the designated date.
indicadores.revisionindicadorhandle.Calificado=Qualified
indicadores.revisionindicadorhandle.Comentarios=Comments:
indicadores.revisionindicadorhandle.-Debeagregaruncomentario=- You must add a comment.
indicadores.revisionindicadorhandle.Elcomentariodelindicadorasidoactualizadocorrectamente=The comment indicator to been updated successfully.
indicadores.revisionindicadorhandle.EsteIndicadorNoHaSidoCalificadoPorTodosLosResponsables=This indicator has not been graded by all the responsibles.
indicadores.revisionindicadorhandle.Larevisionindicadorsehaactualizadocorrectamente=The indicator review was updated correctly.
indicadores.revisionindicadorhandle.Lossiguientesdatossonnecesarios=The following data are necessary:
indicadores.revisionindicadorhandle.NoHayIndicadoresProgramadosParaEstePeriodo=There are no programed indicators for this period.
indicadores.revisionindicadorhandle.IndicadorNoCalificado=This indicator has not been qualified.
indicadores.revisionindicadorhandle.PorCalificar=To Qualify
indicadores.revisionindicadorhandle.Programado=Programmed
indicadores.revisionindicadorhandle.Responsablesdelindicador=Responsible for the indicator:
indicadores.revisionindicadorhandle.Revisado=Revised
indicadores.revisionindicadorhandle.Verdetallederegistros=View details from registries
isoblock.common.database.TipoDeObjeto=Object type
isoblock.documentos.documentojava.Nohaylectores=No readers.
isoblock.Proyectos.Actividad.Concluida=Concluded
isoblock.Proyectos.Actividad.Creada=Created
isoblock.Proyectos.Actividad.EnProceso=In Progress
isoblock.Proyectos.Actividad.EstadoDesconocido=Unknown Status
isoblock.Proyectos.Actividad.Verificada=Verified
isoblock.scorecards.selectorEntidad.Ocultar=Hide
login.login.\u00bfNopuedesaccederatucuenta?=Can't access to your account?
login.login.\u00bfQueimpidequepuedasaccederatucuenta?=What prevents you that you can access to your account?
login.login.1.Tecleaquitunombredeusuario=1. Type your username here.
login.login.1.Tqcleaaquitudirecciondecorreoelectronico=1. Type your e-mail address here.
login.login.2.boton.Enviar=Send
login.login.2.Direcciondecorreoelectronico=E-mail Address:
login.login.2.NombredeUsuario=Username:
login.login.2.PresionaEnviar=2. Press Send.
login.login.2.SiBnextDPMShaaceptadotunombredeusuario,recibirasuncorreocontucontrase\u00f1a=If Bnext QMS has accepted your username, you will recive an e-mail with your password.
login.login.BnextDPMShaaceptadotunombredeusuarioyhamandadouncorreocontucontrase\u00f1a=Bnext QMS has accepted your user name and has sent an email with your password. 
login.login.BnextDPMShaenviadoaladireccionquehasespecificadounalistadelosnombresdeusuariosasociadosaesadirecciondecorreoelectronico=Bnext QMS has sent to the address you have specified a list of the names of users associated with that e-mail address.
login.login.clave=Password :
login.login.clave=Password :
login.login.claveonombreincorrecto=The password or username are incorrect.
login.login.claveonombreincorrecto=The password or username are incorrect.
login.login.Debeespecificarunadirecciondecorreoelectronico=You must specify an E-mail Address
login.login.Debeespecificarunnombredeusuario=You must specify a username
login.login.DisculpalasmolestiasocasionadaspornopoderaccederaBnextDPMS=Sorry for the inconvenience caused by not being able to access Bnext QMS.
login.login.Entrar=Login
login.login.Entrar=Login
login.login.entraralsistema=System login
login.login.entraralsistema=System login
login.login.ERROR=ERROR:
login.login.ERROR=ERROR:
login.login.Heolvidadomicontrase\u00f1a=I forgot my password.
login.login.Heolvidadominombredeusuario=I forgot my username.
login.login.introduzcausuarioyclave=Please type your username and password.
login.login.introduzcausuarioyclave=Please type your username and password.
login.login.Lacuentadeusuarionoseencuentraactiva=The user account is not active.
login.login.lasessionhaexpirado=Session has expired.
login.login.invalidToken=The system access link has expired.
login.login.Nocerrarsesion=Do not log off
login.login.NopuedoaccederamicuentadeBnextDPMS=I can\'t access to my account of Bnext QMS.
login.login.Notepreocupes,aunqueintentesaccedermuchasveces,tucuentanosebloqueara,yaqueBnextnolimitalosintentosdeacceso=Do not worry, even if you try to access many times, your account will not lock. Bnext DPMS not limit the access attempts.
login.login.Pararecuperartucontrase\u00f1adeBnextDPMS,sigueestospasos=To recover your password of Bnext QMS, follow these steps: 
login.login.Parasolucionaresteproblemalomasrapidoposible,sigueestospasos=To resolve this issue as quickly as possible, follow these steps:
login.login.Parecequemicontrase\u00f1anofunciona=It seems that my password is not working.
login.login.Resgistrate=Register
login.login.Seenviaraaladireccionquehasespecificadounalistadelosnombresdeusuariosasociadosaesadirecciondecorreoelectronico=It will send to the address that you have specified a list of usernames associated with that e-mail address.
login.login.Serequierelaclavedelusuario=The user password is required
login.login.Serequierelaclavedelusuario=The user password is required
login.login.Serequierelacuentadelusuario=The user account is required
login.login.Serequierelacuentadelusuario=The user account is required
login.login.Sinorecuerdastunombredeusuario,sigueestospasospararecuperarlo=If you not remember your password, follow these steps to recover it:
login.login.Sisiguesdeformaexactaestasinstrucciones,podrasobtenerlosmejoresresultados=If you follow these instructions exactly, you can get the best results.
login.login.Siteapareceunmensajedeavisodiciendoquetucuentanoestaactivadadebesesperaraqueestaseaactivadaporeladministradordelsistema=If you receive a warning message saying that your account is not active, you must wait for this is active by the system administrator.
login.login.Tenencuentaqueelcampodelacontrase\u00f1adistingueentremayusculasyminusculas,porloquedebescomprobarquenohayascometidoningunerrorenestesentidoysilateclaBloq.Mayus.estaactivada=Note that the password field distinguishes between uppercase and lowercase letters, so you must check that you have not committed no error in this regard and if the Caps Lock key is enabled.
login.login.usuario=User :
login.login.usuario=User :
login.principal.Acciones=Actions
login.principal.Auditoria=Audit
login.principal.Documentos=Documents
login.principal.Documents=Documents
login.principal.Encuestas=Surveys
login.registrarhandle.boton.Registrarse=Register
login.registrarhandle.ClavedeAcceso=Access Key:
login.registrarhandle.ConfirmeClavedeAcceso=Confirm access key:
login.registrarhandle.Correo=Mail:
login.registrarhandle.--ELIJAUNPERFIL--=--SELECT A PROFILE--
login.registrarhandle.Eselnombredeusuarioqueutilizaraparaentraralsistema(Ej.username=Is the username used to enter the system (e.g. username)
login.registrarhandle.Inicio=Start
login.registrarhandle.-Lasclavesdeaccesonocoinciden=- Do not match the access key.
login.registrarhandle.NombreCompleto=Full Name:
login.registrarhandle.Nombredeusuario=Username:
login.registrarhandle.Perfil=Profile:
login.registrarhandle.-Perfildeusuario=- User profile
login.registrarhandle.Preferentementeteclee[ApellidoPaterno,ApellidoMaterno,Nombre(s)]=Preferably type [Paternal surname , Maternal surname, Name (s)]
login.registrarhandle.REGISTRODEUSUARIO=USER REGISTRY
login.registrarhandle.Ustedseharegistradoexitosamente,espereuncorreoelectronicoavisandolequesucuentahasidoactivada=You have successfully registered, wait for an email saying that your account has been activated.
LosSiguientesDatosSonNecesarios=The following data is necesary:
mensaje.actividad.NosePuedePedirCantidadMayorASobrante=You can\\'t request an amount bigger than the exceeding.
mensaje.actualizacion=Information will be updated. Do you want to continue?
menu.folderTreeDownFrame.TraduccionArbolJavaScript = A tree for site navigation will open here if you enable JavaScript in your browser.
menu.folderTreeLeftFrame.Acciones=Actions
menu.folderTreeLeftFrame.Acciones=Actions
menu.folderTreeLeftFrame.Accionesatomar=Actions to take
menu.folderTreeLeftFrame.Accionesatomar=Actions to take
menu.folderTreeLeftFrame.Accionescorrectivas=Corrective Actions
menu.folderTreeLeftFrame.Accionescorrectivas=Corrective Actions
menu.folderTreeLeftFrame.Accionespreventivas=Preventive Actions
menu.folderTreeLeftFrame.Accionespreventivas=Preventive Actions
menu.folderTreeLeftFrame.Alta/ActualizacionMasiva= New / Massive Update
menu.folderTreeLeftFrame.Alta=Create
menu.folderTreeLeftFrame.Alta=Create
menu.folderTreeLeftFrame.AltadeEquipos=Register
menu.folderTreeLeftFrame.AltadeFuentedeAcciones=NEW SOURCE OF ACTIONS
menu.folderTreeLeftFrame.ALTADEFUENTESDEAUDITORIAS=ADD SOURCE OF AUDITS
menu.folderTreeLeftFrame.ALTADEFUENTESDEQUEJAS=NEW COMPLAINTS SOURCES
menu.folderTreeLeftFrame.ALTADETIPOSDECALIFICACIONES=NEW TYPES OF QUALIFICATIONS
menu.folderTreeLeftFrame.Areas=Processes
menu.folderTreeLeftFrame.Areas=Processes
menu.folderTreeLeftFrame.Auditorias=Audits
menu.folderTreeLeftFrame.Auditorias=Audits
menu.folderTreeLeftFrame.Avanzados=Advanced
menu.folderTreeLeftFrame.Avanzados=Advanced
menu.folderTreeLeftFrame.Busqueda=Search
menu.folderTreeLeftFrame.Busqueda=Search
menu.folderTreeLeftFrame.Catalogo =Catalogs
menu.folderTreeLeftFrame.CatalogoAuditorias= Audits
menu.folderTreeLeftFrame.CatalogoAuditorias=Audits
menu.folderTreeLeftFrame.CatalogoAuditoriasCalificaciones = Audit Qualifications
menu.folderTreeLeftFrame.CatalogoAuditoriasCalificacionesControlMasivo = Massive Control
menu.folderTreeLeftFrame.CatalogoFuenteAuditorias=Audits Status
menu.folderTreeLeftFrame.CatalogoQuejas =Complaints
menu.folderTreeLeftFrame.CatalogoQuejasFuente =complaint source 
menu.folderTreeLeftFrame.CatalogoQuejasFuenteControlMasivo =Massive Control
menu.folderTreeLeftFrame.CatalogosAcciones =Actions
menu.folderTreeLeftFrame.CatalogosAccionesFuenteControlMasivo =Massive Control
menu.folderTreeLeftFrame.CatalogosFormas =Forms Catalog
menu.folderTreeLeftFrame.CatalogosFuenteAcciones =Source of actions
menu.folderTreeLeftFrame.FindingType = Action Type
menu.folderTreeLeftFrame.Comentarios=Comment search
menu.folderTreeLeftFrame.Documentos=Documents
menu.folderTreeLeftFrame.ConfiguracioCuestionariosEncuestas = Survey Questionnaires
menu.folderTreeLeftFrame.Configuracion=Configuration
menu.folderTreeLeftFrame.Configuracion=Settings
menu.folderTreeLeftFrame.ConfiguracionEncuestas = Surveys
menu.folderTreeLeftFrame.ConfiguracionFacultades = ${Facilities}
menu.folderTreeLeftFrame.ConfiguracionIndicadores = Indicators report
menu.folderTreeLeftFrame.ConfiguracionMisEncuestas = My Surveys
menu.folderTreeLeftFrame.ConfiguracionMisIndicadores = My indicators
menu.folderTreeLeftFrame.ConfiguracionMisQuejas = My Complaints
menu.folderTreeLeftFrame.ConfiguracionObjetivos = Objectives
menu.folderTreeLeftFrame.ConfiguracionPerfiles = Profiles
menu.folderTreeLeftFrame.ConfiguracionQuejas =Complaints
menu.folderTreeLeftFrame.ConfiguracionQuejasControl = Control
menu.folderTreeLeftFrame.ConfiguracionQuejasControlMasivo = Massive Control
menu.folderTreeLeftFrame.ConfiguracionQuejasUR=Voz UR
menu.folderTreeLeftFrame.ConfiguracionSistema=System Configuration
menu.folderTreeLeftFrame.Consulta=List
menu.folderTreeLeftFrame.Consulta=List
menu.folderTreeLeftFrame.Control=Control
menu.folderTreeLeftFrame.Control=Control
menu.folderTreeLeftFrame.ControldeRegistros=Record control
menu.folderTreeLeftFrame.LicensingSchemas=Licensing schemas
menu.folderTreeLeftFrame.ControlRecurrencias= Meeting recurrences
menu.folderTreeLeftFrame.ControlRegistros= Records Control
menu.folderTreeLeftFrame.Cuestionarios=Questionnaires
menu.folderTreeLeftFrame.Cuestionarios=Questionnaires
menu.folderTreeLeftFrame.DepartamentoProcesoUnidades = Areas
menu.folderTreeLeftFrame.Dificultad=Dificulty
menu.folderTreeLeftFrame.Historial=History
menu.folderTreeLeftFrame.GaleriaArbol=Media files
menu.folderTreeLeftFrame.Invitados=Invited
menu.folderTreeLeftFrame.Invitados=Invited
menu.folderTreeLeftFrame.Listadesolicitudes=Request list
menu.folderTreeLeftFrame.ListaMaestra=Master List
menu.folderTreeLeftFrame.ListaMaestra=Master List
menu.folderTreeLeftFrame.ListaRelaciones=Relationships list
menu.folderTreeLeftFrame.Mejoracontinua=Continual Improvement
menu.folderTreeLeftFrame.Mejoracontinua=Continue Improvement
menu.folderTreeLeftFrame.MisAcciones=My findings
menu.folderTreeLeftFrame.MisActividades=My Activities
menu.folderTreeLeftFrame.MisAuditorias=My Audits
menu.folderTreeLeftFrame.ModeloDeNegocios=Business Entity
menu.folderTreeLeftFrame.Objetos=Object
menu.folderTreeLeftFrame.Papelera=Recycle bin
menu.folderTreeLeftFrame.PapeleraAudit=Recycle bin
menu.folderTreeLeftFrame.PapeleraIndicator=Indicators bin
menu.folderTreeLeftFrame.Papeleralarmex=Recycle Bin
menu.folderTreeLeftFrame.Pendientes=New Tasks
menu.folderTreeLeftFrame.Pendientes=New Tasks
menu.folderTreeLeftFrame.Plantilla=Templates
menu.folderTreeLeftFrame.Preferencias=Preferences
menu.folderTreeLeftFrame.Escalation = Escalation
menu.folderTreeLeftFrame.FindingPendings = Actions - tasks
menu.folderTreeLeftFrame.ComplaintPendings = Complaints - tasks
menu.folderTreeLeftFrame.Preguntas=Questions
menu.folderTreeLeftFrame.Preguntas=Questions
menu.folderTreeLeftFrame.Producto=Nonconforming  product
menu.folderTreeLeftFrame.Proyecto=Actions about projects
menu.folderTreeLeftFrame.Proyectos=Projects
menu.folderTreeLeftFrame.Registros=Logs
menu.folderTreeLeftFrame.Reporte=Summary
menu.folderTreeLeftFrame.Reporte=Summary
menu.folderTreeLeftFrame.ReportedeAcciones=Actions
menu.folderTreeLeftFrame.ReportedeAcciones=Actions
menu.folderTreeLeftFrame.ReportedeAuditorias=Audits
menu.folderTreeLeftFrame.ReportedeAuditorias=Audits
menu.folderTreeLeftFrame.ReportedeConfiguracion=Configuration
menu.folderTreeLeftFrame.ReportedeConfiguracion=Settings
menu.folderTreeLeftFrame.ReportedeCuestionarios=Questionnaires
menu.folderTreeLeftFrame.ReportedeCuestionarios=Questionnaires
menu.folderTreeLeftFrame.ReporteDeDocumentos=Document report
menu.folderTreeLeftFrame.ReportedeDocumentos=Documentos
menu.folderTreeLeftFrame.ReportedeDocumentos=Documents
menu.folderTreeLeftFrame.ReportedeProyectos=Projects
menu.folderTreeLeftFrame.ReporteGerencial=Executive report
menu.folderTreeLeftFrame.ReporteGerencial=Executive report
menu.folderTreeLeftFrame.Reportes=Reports
menu.folderTreeLeftFrame.Reportes=Reports
menu.folderTreeLeftFrame.Repositorios=Repository
menu.folderTreeLeftFrame.Repositorios=Repository
menu.folderTreeLeftFrame.DiccionariosDeVersionamiento=Version dictionaries
menu.folderTreeLeftFrame.Respaldos = Backups
menu.folderTreeLeftFrame.Reuniones=Meetings
menu.folderTreeLeftFrame.Rubros=Budgets
menu.folderTreeLeftFrame.Secciones=Sections
menu.folderTreeLeftFrame.Secciones=Sections
menu.folderTreeLeftFrame.Secuencias=Sequences
menu.folderTreeLeftFrame.Secuencias=Sequences
menu.folderTreeLeftFrame.Solicitudes=Requests
menu.folderTreeLeftFrame.TareaPendiente = You have a pending task in the current form.  \\nPress the button cancel or return to perform another action.
menu.folderTreeLeftFrame.Tipo=Type
menu.folderTreeLeftFrame.TipoDeObjetos=Objects Types
menu.folderTreeLeftFrame.Tipos=Type
menu.folderTreeLeftFrame.Tipos=Type
menu.folderTreeLeftFrame.TraduccionArbolJavaScript = A tree for site navigation will open here if you enable JavaScript in your browser.
menu.folderTreeLeftFrame.Ubicaciones=Departments
menu.folderTreeLeftFrame.Ubicaciones=Departments
menu.folderTreeLeftFrame.Ubicaciones=Departments
menu.folderTreeLeftFrame.Ubicaciones=Departments
menu.folderTreeLeftFrame.Usuarios=Users
menu.folderTreeLeftFrame.Usuarios=Users
menu.folderTreeLeftFrame.Llenar=Available forms
menu.folderTreeLeftFrame.DisenadosPorMi=Designed by me
menu.folderTreeLeftFrame.HistorialDeLlenado=History of requests
menu.folderTreeLeftFrame.AnswerResume=Answer resume
menu.folderTreeLeftFrame.MiHistorialDeLlenado=My history of requests
menu.folderTreeLeftFrame.Formulario=Forms
menu.folderTreeLeftFrame.CatalogoInterno=Internal catalog
menu.folderTreeLeftFrame.CatalogoExterno=External catalog
menu.folderTreeLeftFrame.CatalogMeeting=Meetings
menu.folderTreeLeftFrame.MeetingType=Meeting type
menu.folderTreeLeftFrame.AuditProgram=Audit program
menu.folderTreeLeftFrame.DifusionDocumentos=Diffusi\u00f3n documents
menu.folderTreeLeftFrame.Consultas=Query
menu.folderTreeLeftFrame.MiCuenta.MiCuenta=Account
menu.folderTreeLeftFrame.MiCuenta.Calendario=Break date calendar 
menu.folderTreeLeftFrame.Pendientes=Pendings
menu.folderTreeLeftFrame.Calendar=Break date calendar 
ocumentos.documentospendientes.QuejasporverificarrespuestaTooltip=Quejas por verificar respuesta
onfiguracion.objetohandle.javascript.LosDatosdelobjetohansidoactualizados=The Objects data has been Updated
opcion.NO=NO
opcion.SI=YES
proyecto.altaactividad.header.AltaDeActividad=New activity for goal: 
proyecto.altaactividad.Objetos=Objects
proyecto.altaactividad.Responsables=Persons in charge
proyecto.editaractividad.Responsables=Persons in charge
proyecto.proyectolist.Autor=Author
proyecto.proyectolist.Borrar=Delete
proyecto.proyectolist.CerrarProyecto=Press to close the project.
proyecto.proyectolist.ControlDeProyecto=PROJECT EDITION
proyecto.proyectolist.Editar=Edit
proyecto.proyectolist.Estado=Status
proyecto.proyectolist.FechaFinal=Final date
proyecto.proyectolist.FechaInicial=Initial date
proyecto.proyectolist.javascript.DeseaMarcarElProyectoComoCerrado=Do you want to set the project status as closed?
proyecto.proyectolist.javascript.ElProyectoSeraBorradoDelSistemaDeseaContinuar=The project will be deleted. Do you want to continue?
proyecto.proyectolist.ModificarProyecto=Press to modify the project.
proyecto.proyectolist.Nombre=Name
proyecto.proyectolist.NoSeEncontraronProyectosEnLosQueNoParticipa=Projects in which it participates were not found.
proyecto.proyectolist.NoSeEncontraronProyectosEnLosQueParticipa=Projects in which you are enrolled, haven't been found.
proyecto.proyectolist.NoSePudoRealizarLaOperacionAnterior=Transaction unsuccessful
proyecto.proyectolist.Presupuesto=Budget
proyecto.proyectolist.Proyecto=Project
proyecto.proyectolist.ProyectoCerrado=Project Closed.
proyecto.proyectolist.ProyectoConcluido=Project Concluded.
proyecto.proyectolist.ProyectoCreado=Project Created.
proyecto.proyectolist.ProyectoLiberado=Project Released.
proyecto.proyectolist.ProyectosEnLosQueNoParticipa=Projects in which you aren\\'t involved
proyecto.proyectolist.ProyectosEnLosQueParticipa=Projects in which you are involved
proyecto.proyectolist.Responsable=Leader
proyecto.proyectolist.SeHaEliminadoElProyecto=The project has been deleted.
proyecto.proyectolist.SeHaMarcadoElProyectoComoCerrado=The project has been set as closed
proyecto.proyectolist.TODOS=ALL
proyecto.proyectolist.Ver=View
proyecto.proyectolist.Verificador=Verifier
proyecto.proyectolist.VerProyecto=Press to view the project.
proyecto.proyectoreportegeneral.Elpresupuestosolodebecontenernumeros=The budget should only contain numbers
proyecto.proyectoreportegeneral.Verifiquepuntodecimal=Verify decimal point
proyectos..proyectolist.Proyecto=Project:
proyectos.actividad.AgregarDocumento=Add Document:
proyectos.actividad.AltaPeticionGasto=The request has been added.
proyectos.actividad.btnPeticionGasto=Budget request
proyectos.actividad.Detalledelcambio=Modification detail
proyectos.actividad.-Dificultad=- Difficulty
proyectos.actividad.DocumentoExistente=Existing Document
proyectos.actividad.DocumentoNuevo=New Document
proyectos.actividad.ExistenPeticionesDeCambioPendientesLaActividadNoPuedeSerConcluidaConCambiosPendientes=There are pending expenses requests. The activity can\\'t conclude.
proyectos.actividad.ExistePeticionDeGastoNoAutorizada=There is an unathorized budget modification request
proyectos.actividad.-Modulo=- Module
proyectos.actividad.NoseHanAgregadoDocumentos=There are no documents added.
proyectos.actividad.NosePudoAltaPeticionGasto=The request has not been added.
proyectos.actividad.-Objeto=- Object
proyectos.actividad.peticionGastoCantidad=Amount:
proyectos.actividad.peticionGastoRazon=Reason:
proyectos.actividad.peticionGastoSobrante=Exceeding:
proyectos.actividad.Razondecambiodefechadeverificacion=Verification date change motive:
proyectos.actividad.Razondecambiodefechafinal=End date change motive:
proyectos.actividad.Razondecambiodefechainicial=Start date change motive:
proyectos.actividad.Razondecambiodeporcentaje=Porcentage change motive:
proyectos.actividad.Razondecambiodepresupuesto=Budget change motive:
proyectos.actividad.Razondecambiodeverificador=Verifier change motive:
proyectos.actividad.-Tipo=- Type
proyectos.actividad.-TipodeObjeto=- Object type
proyectos.actividadeslistcabios.NoTieneAutorizacion=You don\\'t have authorization
proyectos.actividadeslistcambios.Actividad.TipoCambioDesconocido=Unknow Change Type
proyectos.actividadeslistcambios.Actividad.TipoCambioFechaFin=Final Date
proyectos.actividadeslistcambios.Actividad.TipoCambioFechaInicio=Start Date
proyectos.actividadeslistcambios.Actividad.TipoCambioFechaVerificador=Verification Date
proyectos.actividadeslistcambios.Actividad.TipoCambioPeticionPresupuesto=Budget Request
proyectos.actividadeslistcambios.Actividad.TipoCambioPresupuesto=Budget
proyectos.actividadeslistcambios.Actividad.TipoCambioVerificador=Verifier
proyectos.actividadeslistcambios.Autorizacion=Autorization: 
proyectos.actividadeslistcambios.AutorizacionCambios=AUTHORIZATION OF CHANGES IN ACTIVITIES
proyectos.actividadeslistcambios.AutorizacoinCambios=AUTHORIZATION OF CHANGES IN ACTIVITIES
proyectos.actividadeslistcambios.CambioPropuesto=Proposed change: 
proyectos.actividadeslistcambios.CantidadSolicitada=Requested Amount
proyectos.actividadeslistcambios.DescripcionActividad=Description
proyectos.actividadeslistcambios.DescripcionProyecto=Description
proyectos.actividadeslistcambios.FaltallenarelcomentariodelarazondeNoAutorizaciondelaactividad=Missing fill the comment of the reason of No Authorization activity
proyectos.actividadeslistcambios.FechaFinal=End Date
proyectos.actividadeslistcambios.FechaInicial=Start Date
proyectos.actividadeslistcambios.FechaVerificador=Verifier Date
proyectos.actividadeslistcambios.javascript.OperacionRealizadoExito=The transaction has been successful
proyectos.actividadeslistcambios.NoActividadesCambiosPendientes=There are no activities waiting for authorization
proyectos.actividadeslistcambios.NoAutorizo=REJECT
proyectos.actividadeslistcambios.NombreActividad=Activity: 
proyectos.actividadeslistcambios.NoSeHaRealizadoNingunCambio=No changes have been done
proyectos.actividadeslistcambios.Pendiente=STAND BY
proyectos.actividadeslistcambios.PresupuestoDisponible=Available Budget
proyectos.actividadeslistcambios.RazonCambio=Change Motive
proyectos.actividadeslistcambios.RazonNoAutoriza=Reason Why is not Being Authorize
proyectos.actividadeslistcambios.SiAutorizo=AUTHORIZE
proyectos.actividadeslistcambios.TipoCambio=Change Type: 
proyectos.actividadeslistcambios.ValorAnterior=Last Value: 
proyectos.actividadeslistpeticionespresupuesto.Autorizacion=Authorization\:
proyectos.actividadeslistpeticionespresupuesto.AutorizacionPeticiones=BUDGET AUTHORIZATION 
proyectos.actividadeslistpeticionespresupuesto.CantidadAPedir=Amount\: 
proyectos.actividadeslistpeticionespresupuesto.CantidadTotal=Total Amount\: 
proyectos.actividadeslistpeticionespresupuesto.Comprometido= Hold\: 
proyectos.actividadeslistpeticionespresupuesto.DescripcionProyecto=Description
proyectos.actividadeslistpeticionespresupuesto.FaltallenarelcomentariodelarazondeNoAutorizaciondelaactividad=Missing fill the comment of the reason of No Authorization activity
proyectos.actividadeslistpeticionespresupuesto.javascript.OperacionRealizadoExito=The transaction has been successful
proyectos.actividadeslistpeticionespresupuesto.NoActividadesCambiosPendientes=There are no activities waiting for authorization
proyectos.actividadeslistpeticionespresupuesto.NoAutorizo=REJECT
proyectos.actividadeslistpeticionespresupuesto.NombreActividad=Activity\:
proyectos.actividadeslistpeticionespresupuesto.NombreRubro=Budget Type\: 
proyectos.actividadeslistpeticionespresupuesto.NoSeHaRealizadoNingunCambio=There have not been any changes.
proyectos.actividadeslistpeticionespresupuesto.Pendiente=STAND BY
proyectos.actividadeslistpeticionespresupuesto.RazonNoAutoriza=Reason Why is not Being Authorize
proyectos.actividadeslistpeticionespresupuesto.RazonPeticion=Budget Request's Reason\:
proyectos.actividadeslistpeticionespresupuesto.SiAutorizo=AUTHORIZE
proyectos.actividadeslistpeticionespresupuesto.Sobrante=Exceeding\: 
proyectos.actividadhandle.LaActividadSeraMarcadaComoVerificadaDeseaContinuar=The activity will be marked as verified. \u00bfDo you wish to continue?
proyectos.actividadhandle.Objetos=Objects
proyectos.actividadhandle.ReporteDeCambios=Modification petitions report
proyectos.actividadhandle.VerificarActividad=Verify activity
proyectos.actividadlist.javascript.Laactividadseraborradodelsistema=The activity will be removed. Do you want to continue?
proyectos.altaactividad.javascript.DeseaAjustarLasFechasDelProyectoAEstasNuevasFechas=<br>Do you want to adjust project dates to these new dates?
proyectos.altaactividad.javascript.ElRangoDeFechasNoCoincideConElRangoDeLasFechasDelProyecto=These dates are not in the range of project dates
proyectos.altaactividad.LaActividadNoPudoSerDadaDeAltaCorrectamente=The activity could not be created.
proyectos.avanceactividad.AutorizadoTotal=Authorized total
proyectos.avanceactividad.btnPeticionDeGasto=Budget request
proyectos.avanceactividad.ComentarioDeAvance=Comment
proyectos.avanceactividad.Comprometido=Committed
proyectos.avanceactividad.ComprometidoTotal=Total Committed
proyectos.avanceactividad.imagen.Aceptar=Accept
proyectos.avanceactividad.imagen.Eliminar=Delete
proyectos.avanceactividad.imagen.Esconder=Hide
proyectos.avanceactividad.imagen.mover=Move
proyectos.avanceactividad.javascript.DebeLlenarLaRazonDeCambio=You must fill the change motive.
proyectos.avanceactividad.javascript.ElNuevoPresupuestoNoPuedeSerMenorAlPresupuestoActualMenosElSobrante=The new budget must not be smaller than the current budget minus the left over
proyectos.avanceactividad.javascript.LaCantidadSolicitadaDeberSerMayorACero=The requested amount must be greater than zero
proyectos.avanceactividad.javascript.LaCantidadSolicitadaEsMayorAlSobrante=The requested amount is greater than the left over
proyectos.avanceactividad.javascript.Razon=Motive
proyectos.avanceactividad.javascript.YaExisteUnaPeticionDeCambioDeFechaPendientePorAutorizar=There is a date modification petition pending for authorization.
proyectos.avanceactividad.javascript.YaExisteUnaPeticionDeCambioDeVerificadorPendientePorAutorizar=There is a verificator modification petition pending for authorization.
proyectos.avanceactividad.Razon=Motive
proyectos.avanceactividad.RazonDeCambioDeFecha=Date change motive
proyectos.avanceactividad.Sobrante=Left over
proyectos.avanceactividad.SobranteTotal=Total exceeding
proyectos.editaractividad.Aceptar=Accept
proyectos.editarActividad.Borrar=Delete
proyectos.editaractividad.btnAgregarRubro=New Budget type
proyectos.editaractividad.Cantidad=Amount
proyectos.editarActividad.Descripcion=Description
proyectos.editaractividad.Descripcion=Description
proyectos.editarActividad.Editar=Edit
proyectos.editaractividad.ElDocumentoNoPudoSerBorrado=The document was not deleted.
proyectos.editaractividad.ElPresupuestoNoPudoSerActualizadoCorrectamente=The budget could not be modified properly.
proyectos.editaractividad.ElPresupuestoNoPudoSerBorrado=The budget could not be deleted.
proyectos.editaractividad.ElPresupuestoNoPudoSerDadoDeAlta=The budget could not be created.
proyectos.editaractividad.Esconder=Hide
proyectos.editaractividad.HaModificadoLaActividadDeseaGuardarLosCambiosAntesDeContinuar=The activity has been modified. \u00bfDo you want to save changes before continuing?
proyectos.editaractividad.header.DetalleDeActividad=ACTIVITY DETAIL
proyectos.editarActividad.javascript.Cantidad=Amount
proyectos.editarActividad.javascript.DescripcionDelRubro=Budget type description
proyectos.editaractividad.javascript.EstaSeguroQueDeseaBorrarElPresupuesto=Are you sure you want to delete the budget?
proyectos.editaractividad.javascript.Lossiguientesdatossonnecesarios=The following items are required
proyectos.editarActividad.javascript.Rubro=Budget type
proyectos.editaractividad.LaActividadNoPudoSerActualizadaCorrectamente=The activity could not be modified properly.
proyectos.editaractividad.LaPeticionNoPudoSerDadaDeAlta=The budget request could not be created.
proyectos.editarActividad.NoSeHanDadoDeAltaRubros=No sections have been created.
proyectos.editaractividad.NosePudoDarDeAltaElRubro=The budget type could not be created.
proyectos.editaractividad.Presupuesto=Budget
proyectos.editarActividad.Presupuesto=Budget
proyectos.editaractividad.PresupuestoTotal=Total budget
proyectos.editaractividad.Responsables=Persons in charge
proyectos.editarActividad.Rubro=Budget type
proyectos.editaractividad.Rubro=Budget type
proyectos.metahandle.Actividad=Activity
proyectos.metahandle.Actividades=Activities
proyectos.metahandle.AgregarDocumento=Add Document
proyectos.metahandle.AgregarNueva=Add New
proyectos.metahandle.AltaDeMeta=NEW GOAL
proyectos.metahandle.Archivos=Files
proyectos.metahandle.Borrar=Delete
proyectos.metahandle.Concluida=Concluded
proyectos.metahandle.Creada=Created
proyectos.metahandle.Descripcion=Description
proyectos.metahandle.DetalleDeMeta=GOAL DETAIL
proyectos.metahandle.DocumentoExistente=Existing Document
proyectos.metahandle.DocumentoNuevo=New Document
proyectos.metahandle.Editar=Edit
proyectos.metahandle.Estado=Status
proyectos.metahandle.FechaFinal=Final Date
proyectos.metahandle.FechaInicial=Initial Date
proyectos.metahandle.GraficaDeGantt=Gantt\\'s Graph
proyectos.metahandle.HaModificadoLaActividadDeseaGuardarLosCambiosAntesDeContinuar=The Goal has been modified. Do you wish to save changes before continuing?
proyectos.metahandle.HaModificadoLaMetaDeseaGuardarLosCambiosAntesDeContinuar=The goal has been modified. \u00bfDo you want to save changes before continuing?
proyectos.metahandle.javascript.Descripcion=Description
proyectos.metahandle.javascript.ElDocumentoNoPudoSerBorrado=The Document could not be deleted
proyectos.metahandle.javascript.ElDocumentoSeraBorradoDeseaContinuar=The document will be erased. Do you want to continue?
proyectos.metahandle.javascript.LaActividadNoPudoSerBorrada=The activity could not be deleted
proyectos.metahandle.javascript.LaActividadSeraBorradaDelSistemaDeseaContinuar=The activity will be deleted from the system. Do you want to continue?
proyectos.metahandle.javascript.LaMetaHaSidoActualizada=The goal has been modified
proyectos.metahandle.javascript.LaMetaHaSidoCerrada=The goal has been closed
proyectos.metahandle.javascript.LaMetaHaSidoDadoDeAlta=The goal has need added
proyectos.metahandle.javascript.LaMetaNoPudoActualizarseCorrectamente=The goal could not be modified correctly
proyectos.metahandle.javascript.LaMetaNoPudoActualizarseCorrectamente=The Goal couldn't be modified correctly
proyectos.metahandle.javascript.LosSiguientesDatosSonNecesarios=The following items are required
proyectos.metahandle.javascript.NombreDeLaMeta=Goal's Name
proyectos.metahandle.Regresar=It will be sent to the previous screen without saving. Are you sure you want to continue?
proyectos.metahandle.NombreDeLaMeta=Goal Name
proyectos.metahandle.NoseHanAgregadoDocumentos=No documents have been added
proyectos.metahandle.NoSeHanAsignadoActividades=No activities have been added
proyectos.metahandle.Presupuesto=Budget
proyectos.metahandle.UsuarioVerificador=Verifier
proyectos.metahandle.UsuarioResponsable=Responsible(s)
proyectos.metahandle.TODOS=ALL
proyectos.metahandle.Ver=View
proyectos.metahandle.Verificada=Verified
proyectos.metatohandle.EnProceso=In Process
proyectos.misactividades.ActividadesPorVerificar=ACTIVITIES TO VERIFY
proyectos.misactividades.Editar=Edit
proyectos.misactividades.Estado=Status
proyectos.misactividades.FechaFin=Final Date
proyectos.misactividades.FechaInicio=Initial Date
proyectos.misactividades.MisActividades=MY ACTIVITIES
proyectos.misactividades.NoHayActividadesEnLasQueParticipaActualmente=You Are Not Participating in Any Activity
proyectos.misactividades.NombreActividad=Activity
proyectos.misactividades.NombreMeta=Goal
proyectos.misactividades.NoParticipaEnActividadesEsteProyecto=You do not participate in activities of this project
proyectos.misactividades.Presupuesto=Budget
proyectos.misactividades.Responsable=Responsible
proyectos.misactividades.ResponsableVerifcador=Responsible / Verifier
proyectos.misactividades.Rol=Role
proyectos.misactividades.Verificador=Verifier
proyectos.proyecto.DocumentoExistente=Existing Document
proyectos.proyecto.DocumentoNuevo=New Document
proyectos.proyecto.NoseHanAgregadoDocumentos=There are no documents added.
proyectos.proyectohandle.Actividades=Activities
proyectos.proyectohandle.AgregarDocumento=New document
proyectos.proyectohandle.AgregarNueva=New Goal
proyectos.proyectohandle.AltaDeProyecto=CREATE PROJECT
proyectos.proyectohandle.Archivos=Files
proyectos.proyectohandle.Borrar=Delete
proyectos.proyectohandle.CerrarProyecto=Close project
proyectos.proyectohandle.Clave=ID
proyectos.proyectohandle.Concluida=Concluded
proyectos.proyectohandle.Creada=Created
proyectos.proyectohandle.Descripcion=Description
proyectos.proyectohandle.DetalleDeProyecto=PROJECTS
proyectos.proyectohandle.DocumentoExistente=Existing document
proyectos.proyectohandle.DocumentoNuevo=New document
proyectos.proyectohandle.DocumentosExistentes=New documents
proyectos.proyectohandle.DocumentosNuevos=New documents
proyectos.proyectohandle.DocumentosRelacionados=Related Documents
proyectos.proyectohandle.Editar=Edit
proyectos.proyectohandle.ElProyectoSeraMarcadoComoCerradoDeseaContinuar=The project will be set closed. Do you wish to continue?
proyectos.proyectohandle.EnProceso=In process
proyectos.proyectohandle.Estado=Status
proyectos.proyectohandle.FechaFinal=End date
proyectos.proyectohandle.FechaInicial=Start date
proyectos.proyectohandle.Folio=Reference number:
proyectos.proyectohandle.Funcion=Function
proyectos.proyectohandle.GraficaDeGantt=Gantt\\'s graph
proyectos.proyectohandle.HaModificadoElProyectoDeseaGuardarLosCambiosAntesDeContinuar=The project has been modified. \u00bfDo you wish to save changes before continuing?
proyectos.proyectohandle.javascript.Clave=Code
proyectos.proyectohandle.javascript.Descripcion=Description
proyectos.proyectohandle.javascript.ElDocumentoNoPudoSerBorrado=The document could not be erased.
proyectos.proyectohandle.javascript.ElDocumentoSeraBorradoDeseaContinuar=The document will be deleted. Do you wish to continue?
proyectos.proyectohandle.javascript.ElProyectoDebeEmpezarDespuesDelDiaDeHoy=The project must start after today
proyectos.proyectohandle.javascript.ElProyectoDebeTerminarDespuesDelDiaDeHoy=The project must end after today
proyectos.proyectohandle.javascript.ElProyectoHaSidoActualizado=The project has been updated.
proyectos.proyectohandle.javascript.ElProyectoHaSidoActualizadoCorrectamente=The project has been successfully updated.
proyectos.proyectohandle.javascript.ElProyectoHaSidoCerrado=The project has been closed.
proyectos.proyectohandle.javascript.ElProyectoHaSidoDadoDeAlta=The project has been created.
proyectos.proyectohandle.javascript.ElProyectoNoPudoActualizarseCorrectamente=The project was not correctly updated.
proyectos.proyectohandle.javascript.FechaFinal=Final date
proyectos.proyectohandle.javascript.FechaInicial=Initial date
proyectos.proyectohandle.javascript.Folio=Folio
proyectos.proyectohandle.javascript.Funcion=Function
proyectos.proyectohandle.javascript.LaMetaNoPudoSerBorrada=The goal could not be erased.
proyectos.proyectohandle.javascript.LaMetaNoPudoSerBorradaReason=The goal could not be erased because it has activities in progress
proyectos.proyectohandle.javascript.LaMetaSeraBorradaDelSistemaDeseaContinuar=The goal will be deleted. \u00bfDo you want to continue?
proyectos.proyectohandle.javascript.LosSiguientesDatosSonNecesarios=The following items are required
proyectos.proyectohandle.javascript.NombreDelProyecto=Project name
proyectos.proyectohandle.javascript.NumeroConvenio=Agreement Number
proyectos.proyectohandle.javascript.Presupuesto=Budjet
proyectos.proyectohandle.javascript.Responsable=Attendant
proyectos.proyectohandle.LiberarProyecto=Release project
proyectos.proyectohandle.Meta=Goal
proyectos.proyectohandle.Metas=Goals
proyectos.proyectohandle.NombreDelProyecto=Project name
proyectos.proyectohandle.NoSeHanAsignadoActividades=There are no activities.
proyectos.proyectohandle.NoSeHanAsignadoMetas=The are no assigned goals
proyectos.proyectohandle.NoSePudoRealizarLaOperacionAnterior=Transaction unsuccessful
proyectos.proyectohandle.NumeroDeConvenio=Agreement number
proyectos.proyectohandle.PermitirUsarseComoProyectoBase=Allow to use it as a main project
proyectos.proyectohandle.Presupuesto=Budjet
proyectos.proyectohandle.ProyectoBase=Main project:
proyectos.proyectohandle.ProyectoConfidencial=Confidential project
proyectos.proyectohandle.ProyectoDeDesarrollo=Development Project
proyectos.proyectohandle.TODOS=ALL
proyectos.proyectohandle.Ver=View
proyectos.proyectohandle.-Verifiquequelafechaseaposterioroigualaladiadelhoy=- Verify the date (s) is later than or equal to the day of today.
proyectos.proyectolistcontrolcambios.Borrar=Delete
proyectos.proyectolistcontrolcambios.Editar=Edit
proyectos.proyectolistcontrolcambios.Encargado=LEADER
proyectos.proyectolistcontrolcambios.Estado=Status
proyectos.proyectolistcontrolcambios.FechaFin=End date
proyectos.proyectolistcontrolcambios.FechaInicio=Start date
proyectos.proyectolistcontrolcambios.Liberado=Released
proyectos.proyectolistcontrolcambios.MisProyectosConCambiosPendientes=My Pending Project Activities
proyectos.proyectolistcontrolcambios.MisProyectosPeticionesPresupuestosAutorizar=My Projects with Pending Budget\'s Request Authorization
proyectos.proyectolistcontrolcambios.MisProyectosPorCerrar=My Closing Projects
proyectos.proyectolistcontrolcambios.Nombre=Project
proyectos.proyectolistcontrolcambios.NoSeEncontraronProyectosConCambiosPendientes=Projects with pending activities have not been found
proyectos.proyectolistcontrolcambios.NoSeEncontraronProyectosPorCerrar=There are no closing projects
proyectos.proyectolistcontrolcambios.OtrosProyectosConCambiosPendientes=Other Pending Project Activiites
proyectos.proyectolistcontrolcambios.OtrosProyectosPeticionesPresupuestosAutorizar=Other Projects with Pending Budget\'s Request Authorization
proyectos.proyectolistcontrolcambios.OtrosProyectosPorCerrar=Other Closing Projects
proyectos.proyectolistcontrolcambios.Presupuesto=Budget
proyectos.proyectolistcontrolcambios.ProyectosConCambiosPendientes=PROJECTS WITH ACTIVITY PENDING CHANGES
proyectos.proyectolistcontrolcambios.ProyectosPeticionesPresupuestosAutorizar=Projects with Pending Budget\'s Request Authorization
proyectos.proyectolistcontrolcambios.ProyectosPorCerrar=CLOSING PROJECTS
proyectos.proyectoreportegeneral.chart.Cerrado=Closed
proyectos.proyectoreportegeneral.chart.Concluido=Completed
proyectos.proyectoreportegeneral.chart.Creado=Created
proyectos.proyectoreportegeneral.chart.Liberado=In progress
proyectos.proyectoreportegeneral.chart.NumeroDeProyectos=Number of Projects
proyectos.proyectoreportegeneral.chart.title=Projects
proyectos.proyectoreportegeneral.header=Project report
proyectos.proyectoreportegeneral.HorasporProyecto=Hours by Project
proyectos.proyectoshandle.-estemensajenodebemostrarse-=-this message should not be displayed-
proyectos.proyectoslistcontrolcambios.UstedNoTieneAccesoaEstaPagina=You Do not Have Access To This Page
proyectos.reporteavanceactividad.Autor=Author
proyectos.reporteavanceactividad.Avance=Advance
proyectos.reporteavanceactividad.Comentario=Commentary
proyectos.reporteavanceactividad.Fecha=Date
proyectos.reporteavanceactividad.header=ADVANCE REPORT IN ACTIVITIES
proyectos.reporteavanceactividad.Inicio=Start
proyectos.reporteavanceactividad.Nombre=Name
proyectos.reporteavanceactividad.Responsable=Responsible
proyectos.reporteavanceactividad.RolAutor=Role
proyectos.reporteavanceactividad.TrabajoRealizado=Work done
proyectos.reporteavanceactividad.TrabajoTotalRealizado=Total work done
proyectos.reporteavanceactividad.Verificador=Verifier
proyectos.reportecambiosactividad.Actividad=Activity
proyectos.reportecambiosactividad.Activity=Activity
proyectos.reportecambiosactividad.Autorizado=Authorized
proyectos.reportecambiosactividad.CambioPropuesto=Value proposed
proyectos.reportecambiosactividad.CantidadSolicitada=Requested amount
proyectos.reportecambiosactividad.header=MODIFICATION REPORT IN ACTIVITIES
proyectos.reportecambiosactividad.NoAutorizado=Rejected
proyectos.reportecambiosactividad.Pendiente=Task
proyectos.reportecambiosactividad.PresupuestoDisponible=Available budget
proyectos.reportecambiosactividad.RazonDeLaPeticion=Petition motive
proyectos.reportecambiosactividad.RazonPorLaQueNoAutoriza=Rejection motive
proyectos.reportecambiosactividad.StatusDeAutorizacion=Authorization status
proyectos.reportecambiosactividad.TipoDeCambio=Type of modification
proyectos.reportecambiosactividad.ValorAnterior=Original value
proyectos.reportedocumentosproyectosubidos.Actividad=Activity
proyectos.reportedocumentosproyectosubidos.ActividadOriginal=Original activity
proyectos.reportedocumentosproyectosubidos.Autor=Author
proyectos.reportedocumentosproyectosubidos.header=UPLOADED DOCUMENTS REPORT
proyectos.reportedocumentosproyectosubidos.Meta=Goal
proyectos.reportedocumentosproyectosubidos.MetaOriginal=Original goal
proyectos.reportedocumentosproyectosubidos.NoSeEncontraronDocumentosEnEstaBusqueda=No documents were found.
proyectos.reportedocumentosproyectosubidos.Proyecto=Project
proyectos.reportedocumentosproyectosubidos.ProyectoOriginal=Original project
proyectos.reportedocumentosproyectosubidos.Titulo=Title
proyectos.reportepeticionespresupuesto.CantidadAPedir=Amount
proyectos.reportepeticionespresupuesto.FechaAutorizacion=Authorization Date
proyectos.reportepeticionespresupuesto.FechaPeticion=Request date
proyectos.reportepeticionespresupuesto.header=BUDGET\'S REQUEST REPORT IN ACTIVITIES
proyectos.reportepeticionespresupuesto.Rubro=Concept
proyectos.reportepeticionespresupuesto.StatusDeAutorizacion=Status
proyectos.verificaractividad.Aceptar=Accept
proyectos.verificaractividad.ComentarioDeCumplimiento=Comment
proyectos.verificaractividad.ComentarioDeDisminucion=Comment
proyectos.verificaractividad.Esconder=Hide
proyectos.verificaractividad.javascript.-Comentario=- Comment
proyectos.verificaractividad.javascript.SiCambiaElPresupuestoDeLaActividadEstaCamiaraDeEstadoAEnProcesoEstaSeguroQueDeseaContinuar=If you change the activities budget, the activity status will change to In Progress. Do you want to continue?
quejas.misquejas.asignada=Assigned Date
quejas.misquejas.Autor=Author
quejas.misquejas.Autordelaqueja=Author of the complaint
quejas.misquejas.Busqueda=Search
quejas.misquejas.BusquedaAvanzada=Advanced search
quejas.misquejas.Calificacion=Score
quejas.misquejas.Clave=ID
quejas.misquejas.CONTROL=CONTROL
quejas.misquejas.Controldequejas=CONTROL OF COMPLAINTS
quejas.misquejas.Departamento=Department
quejas.misquejas.Estado=Status
quejas.misquejas.estadoAtendidaenimplementacion=Status: attended in process
quejas.misquejas.estadoAtendidaenimplementacion2=Attended - In process
quejas.misquejas.estadoAtendidaporevaluar=Status: Attended to evaluate
quejas.misquejas.estadoAtendidaporevaluar2=Attended to evaluate
quejas.misquejas.estadoEnproceso=Status: In Process
quejas.misquejas.estadoEnproceso2=In proces
quejas.misquejas.estadoEvaluada=Status: Attended
quejas.misquejas.estadoEvaluada2=Attended
quejas.misquejas.estadoFinalizada=Status: Completed
quejas.misquejas.estadoFinalizada2=Completed
quejas.misquejas.estadoImplementadaporevaluar=Status: Processed to evaluate
quejas.misquejas.estadoImplementadaporevaluar2=To accept results
quejas.misquejas.estadoNoprocede=Status: Not applicable
quejas.misquejas.estadoNoprocede2=Closed - Not applicable
quejas.misquejas.estadoReportada=Status: Reported
quejas.misquejas.estadoReportada2=Reported
quejas.misquejas.estadoReportadaasignada=Status: Reported and assigned to responsible
quejas.misquejas.estadoReportadaasignada2=Assigned
quejas.misquejas.Fechareportada=Reported date
quejas.misquejas.FechaReportada=Reported Date
quejas.misquejas.finalizada=Evaluated Date
quejas.misquejas.Fuente=Source
quejas.misquejas.MISQUEJAS=MY COMPLAINTS
quejas.misquejas.Mostrarfiltrodebusqueda=Display search filter
quejas.misquejas.noprocede=Do not Proceed Date
quejas.misquejas.Ocultarfiltrodebusqueda=Hide search filter
quejas.misquejas.Quejasenlasqueparticipacomoautordelaqueja=Where you are author:
quejas.misquejas.Quejasenlasqueparticipacomodue\u00f1odeldepartamento=Where you are responsible:
quejas.misquejas.Quejasenlasqueparticipacomoresponsable=Where you are responsible:
quejas.misquejas.reportada=Reported Date
quejas.misquejas.respondida=Answered Date
quejas.misquejas.Seborroelregistro=The regisry was removed.
quejas.misquejas.---SELECCIONE---=--- SELECT ---
quejas.misquejas.---TODOS---=--- ALL ---
quejas.misquejas.verificada=Verfied Date
quejas.misquejas.Worker=Leader
quejas.quejashandle.GuardadoExitoso=The finding has been successfully saved.
quejas.quejashandle.ErrorAlGuardar=An error has occurred and the finding could not saved.
quejas.quejashandle.Analisis=Analysis
quejas.quejashandle.AceptarRespuesta=Do you accept the answer?
quejas.quejashandle.-Calificacion=- Qualification
quejas.quejashandle.Calificacion=Grade:
quejas.quejashandle.Clasificacion=Classification:
quejas.quejashandle.Clave=Reporting User ID:
quejas.quejashandle.ClavedeQueja=Complaint ID:
quejas.quejashandle.-Cuentadecorreonovalida=- Invalid mail account
quejas.quejashandle.DarRespuesta=Answer to the complaint:
quejas.quejashandle.-Departamento.=- Department
quejas.quejashandle.Direcciondecorreoalternativapararecibiravisos=Copy of complaint to (e-mail):
quejas.quejashandle.Escuelaofacultadalquesedirigelaqueja=Department to which the complaint is directed:
quejas.quejashandle.FechaAsignacion=Asign on:
quejas.quejashandle.FechaFinalizada=Finished on:
quejas.quejashandle.FechaNOPROCEDE=Cancelled on:
quejas.quejashandle.FechaReportada=Reported on:
quejas.quejashandle.FechaRespondida=Answered on:
quejas.quejashandle.FechaVerificada=Verified on:
quejas.quejashandle.-Fuente=- Source
quejas.quejashandle.FuenteDosP=Source:
quejas.quejashandle.Laquejahasidoreportada=The complaint has been reported.
quejas.quejashandle.Levantaracciones=Lift Actions:
quejas.quejashandle.Losdatosdelaquejahansidoguardadosyseenviaronavisoscorrectamente=Complaint information has been updated and the corresponding notices have been sent.
quejas.quejashandle.Lossiguientescampossonnecesarios=The following items are required:
quejas.quejashandle.matricula=ID:
quejas.quejashandle.MisQuejas=My Complaints
quejas.quejashandle.NuevaQueja=New Complaint
quejas.quejashandle.PorAsignar=To be assigned
quejas.quejashandle.QuejaReportadaPor=Complaint reported by:
quejas.quejashandle.QUEJAS=COMPLAINTS
quejas.quejashandle.-Razonnoprocede=- Reason for complaint not proceeding
quejas.quejashandle.-Razonnoseacepta=- Reason not to accept the answer
quejas.quejashandle.AnalisisCalificacion=- Complaint analysis.
quejas.quejashandle.RazonPorLaQueNoProcede=Reason for complaint not proceeding:
quejas.quejashandle.RazonPorLaQueNoSeAcepta=Reason not to accept the answer:
quejas.quejashandle.-Redacciondelaqueja=- Wording of the complaint
quejas.quejashandle.Redacciondequeja=Complaint
quejas.quejashandle.Responsable=Person responsible of answering the complaint:
quejas.quejashandle.-Respuestaalaqueja=- Answer to the complaint
quejas.quejashandle.SELECCIONE=SELECT
quejas.quejashandle.Cancelar=The information will not be saved. Are you sure you want to cancel?
quejas.quejashandle.Sereasignoresponsable,redacteporque=(The responsible reassigned, write because)
quejas.quejashandle.-Prioridad=- Priority
quejas.quejashandle.-Clasificacion=- Classification
quejas.quejashandle.-Responsable=- Responsible
quejas.quejaslist.controldequejas=CONTROL OF COMPLAINTS
quejas.quejaslist.Redacciondelaqueja=Drafting of complaint
quejas.reporteQuejas.Departamento=Business Unit:
quejas.reporteQuejas.Estado=Status:
quejas.reporteQuejas.FechaAsignda=Assigned Date:
quejas.reporteQuejas.FechaAtendida(Enproceso)=Attended Date(In Process):
quejas.reporteQuejas.FechaCerrada(noprocede)=Closed Date (Not Procede):
quejas.reporteQuejas.Fechadecreacion=Creation date:
quejas.reporteQuejas.Fechadetermino=End date:
quejas.reporteQuejas.FechaPorAceptarResultados=Date for Accept Results:
quejas.reporteQuejas.Filtrosdefecha=Date Filters:
quejas.reporteQuejas.Grafica=Graph
quejas.reporteQuejas.Graficarpor=Graph by:
quejas.reporteQuejas.Loscamposseencuentranvacios=The fields are empty.
quejas.reporteQuejas.Quejasreportadasa=Reported complaints to:
quejas.reporteQuejas.Quejasreportadaspor=Reported complaints by:
quejas.reporteQuejas.REPORTEDEQUEJAS=COMPLAINTS REPORTS
quejas.reporteQuejas.TipodeReporte=Report Type:
quejas.reporteQuejas.--TODOS--=-- ALL --
quejas.reporteQuejas.--TODOS--=-- ALL --
quejas.reporteQuejas.Usuario=User:
reportes.jasperreportRangodeFechasdereport=The report date range:
reportes.jasperreports.Generar=Generate
reportes.reporteGantt.Actividades=Activities
reportes.reporteGantt.Estimado=Estimated
reportes.reporteGantt.header=PROJECT GANTT GRAPH
reportes.reporteGantt.Programado=Scheduled
reportes.reportegerencial.Accionesabiertas=Open Actions
reportes.reportegerencial.Accionesnoatendidas=Unattended actions
reportes.reportegerencial.ACEPTADA=ACCEPTED
reportes.reportegerencial.Ano=Year
reportes.reportegerencial.Auditoriasrealizandose=Audits in process.
reportes.reportegerencial.CANCELADA=CANCELLED
reportes.reportegerencial.CONFIRMADA=CONFIRMED
reportes.reportegerencial.Graficarporano=Graph by year...
reportes.reportegerencial.Graficarpormes=Graph by month...
reportes.reportegerencial.PLANEADA=PLANNED
reportes.reportegerencial.ReporteGerencial=MANAGEMENT REPORT
reportes.reportegerencial.Resumendegraficas=Graphs
reportes.reportegerencial.Seguimientoaacciones=Actions details
reportes.reportegerencial.Seguimientoaauditorias=Audits details
reportes.reportegerencial.Seguimientoadocumentos=Documents details
reportes.reportegerencial.Solicitudesnoatendidas=The following items are necessary:
reportesgenerales.Abril=Apr
reportesgenerales.Agosto=Aug
reportesgenerales.Autor=Author
reportesgenerales.Autorizante=Authorizer
reportesgenerales.chart.noexisteinformacionparagraficar=No information to graph.
reportesgenerales.Diciembre=Dic
reportesgenerales.Enero=Jan
reportesgenerales.Estado=Status
reportesgenerales.Febrero=Feb
reportesgenerales.FechaFin=Final date
reportesgenerales.FechaFinCreado=Final date created
reportesgenerales.FechaFinReportada=Final date reported
reportesgenerales.FechaInicio=Initial date
reportesgenerales.FechaInicioCreado=Initial date created
reportesgenerales.FechaInicioReportada=Initial date created
reportesgenerales.Fuente=Source
reportesgenerales.Sociedad=${Facility}
reportesgenerales.graficacategorizadapor=Graph categorized by status:
reportesgenerales.Graficar=Graph
reportesgenerales.GraficaX=Categorize by
reportesgenerales.Julio=Jul
reportesgenerales.Junio=Jun
reportesgenerales.Marzo=Mar
reportesgenerales.Mayo=May
reportesgenerales.Mes=Month
reportesgenerales.Noviembre=Nov
reportesgenerales.numerodeacciones=Number of actions
reportesgenerales.Octubre=Oct
reportesgenerales.Originador=Originator
reportesgenerales.PresupuestoMaximo=Maximum budget
reportesgenerales.PresupuestoMinimo=Minimum budget
reportesgenerales.Proyecto=Project
reportesgenerales.Responsable=Leader
reportesgenerales.Responsableaccion.accionatomarhandle.Noserecibiolaclaveareferenciar=Not received the key to reference
reportesgenerales.Septiembre=Sep
reportesgenerales.Tipodeaccion=Action Type
reportesgenerales.Tipodegrafica=Graph type
reportesgenerales.TODAS=ALL
reportesgenerales.TODOS=ALL
reportesgenerales.total=Total
reportesgenerales.total=Total
reportesgenerales.Ubicacion=Department
reportesgenerales.TipoDocumento=Document type
royecto.proyectolist.SeHaMarcadoElProyectoComoCerrado=The project has been set as closed.
solicitudes.documentoshandlesolicitudes.-Agregarunarchivo=- Add a file
solicitudes.documentoshandlesolicitudes.-Agregarunaversionvalida=- Add a valid version
solicitudes.documentoshandlesolicitudes.Aprobador=Approver
solicitudes.documentoshandlesolicitudes.Crearsecuencia=Create sequence
solicitudes.documentoshandlesolicitudes.Faltanlossiguienteselementos=The following elements are missing
solicitudes.documentoshandlesolicitudes.Nosepudosubirelarchivo,yaexisteunarchivofisicoenelsistemaconelmismonombreOnosepudolocalizarelarchivoindicado=It could not upload the file, there is a physical file on the system with the same name file \\nor could not locate the indicated file.
solicitudes.documentoshandlesolicitudes.Seactualizocorrectamentelacarpetadestino=The destination folder was successfully updated.
solicitudes.documentoshandlesolicitudes.Version=Version
solicitudes.solicitudeshandle.Actualmenteeldocumentoseencuentraenunasecuenciaonoseencuentraactivoporloquenopuedeasignarunasecuenciaalasolicitud=Currently the document is in a sequence or is not active, so you can not assign a string to the application.
solicitudes.solicitudeshandle.Archivoactual=Actual File:
solicitudes.solicitudeshandle.Carpeta=Folder:
solicitudes.solicitudeshandle.CrearDocumento=Create Document
solicitudes.solicitudeshandle.CrearSecuencia=Create Sequence
solicitudes.solicitudeshandle.-Debeseleccionarunacarpetadestino=- You must select a destination folder.
solicitudes.solicitudeshandle.DocumentoElectronico=Electronic Document:
solicitudes.solicitudeshandle.Eldocumentorelacionadoaestasolicitudnoseencuentraactivo,probablementeyafuecancelado,puedepediralencargadodeldocumentoqueloreactivesiesposible.Obien,\u00bfDeseacerrarestasolicitud?=The document relating to this request is not active, probably already was cancelled, you can ask to the person in charge of the document to reactivate it if it is posible.\n\n Or well, Do you want to close this request?.
solicitudes.solicitudeshandle.EldocumentorelacionadoalasolicitudnoseencuentraACTIVO-Clavede;Documento=The document relating to the request is not ACTIVE \n\n      - Document ID:
solicitudes.solicitudeshandle.Elegirotracarpeta=Choose another folder
solicitudes.solicitudeshandle.ModificarDocumento=Modify Document
solicitudes.solicitudeshandle.Noconoscolaclave=I don't known the password:
solicitudes.solicitudeshandle.RechazarSolicitud=Reject Request
solicitudes.solicitudeshandle.Reemplazararchivosubido=Replace the uploaded file:
solicitudes.solicitudeshandle.Regresar=Back
solicitudes.solicitudeshandle.Seactualizolasolicitud,regreseparaaprobarla=The request has been updated , return to approve it.
solicitudes.solicitudeshandle.Serealizoelprocesocorrectamente,lasolicitudhasidomarcadacomorechazada=The process has been correctly done, the request has been marked as rejected.
solicitudes.solicitudeshandle.SOLICITUDDECANCELACION-CLAVE=CANCELLED REQUEST   - ID:
solicitudes.solicitudeshandle.SOLICITUDDEDOCUMENTONUEVO-CLAVE=NEW DOCUMENT REQUEST   - ID:
solicitudes.solicitudeshandle.SOLICITUDDEMODIFICACION-CLAVE=MODIFICATION REQUEST   - ID:
solicitudes.solicitudeshandle.SOLICITUDDERE-APROBACION-CLAVE=RE-APPROVAL REQUEST   - ID:
solicitudes.solicitudeshandle.Tipodedocumento=Document Type:
solicitudes.solicitudeshandle.Ver=View
solicitudes.solicitudeslist.ArchivoSubido=Uploaded File
solicitudes.solicitudeslist.ClaveDoc=Doc ID
solicitudes.solicitudeslist.ClaveDocumento= Document ID:
solicitudes.solicitudeslist.ClaveSol=ID
solicitudes.solicitudeslist.ElestadoactualesRECHAZADA,presioneparamarcarcomoCerrada=The current status is REJECTED, press to set as CLOSED.
solicitudes.solicitudeslist.Enproceso=In Process
solicitudes.solicitudeslist.EstadoactualesRECHAZADO.Clicparaverdetalle=Current status is DENIED. Clic to see detail
span.anio_s=Year(s)
span.anio=Year
span.anios=Years
span.dia_s=Day(s)
span.dia=Day
span.dias=Day
span.mes_es=Mes(es)
span.mes=Month
span.meses=Months
tooltip.Abajo=Down
tooltip.ActualizarMenuNavegacion=Press to refresh menu.
tooltip.AgregarComentario=Press to add a comment.
tooltip.Arriba=Up
tooltip.Borrar=Press to remove the register.
tooltip.Editar=Press to edit the register.
tooltip.MoverDocumento=Press to move the document.
tooltip.NuevaCarpeta=Press to create a new folder.
tooltip.NuevoDocumento=Press to create a new document.
tooltip.VerAgenda=Press to see the schedule.
tooltip.VerComentarios=Press to show the comments.
tooltip.VerCuestionario=Press to view the questionnaire.
tooltip.VerDetalle=Press to view detail.
tooltip.VerMinuta=View minute.
tooltip.VerPreguntasAuditoria=Press to show the questions of the audit.
quejas.quejasUR.controlQuejas=Control of complaints.
quejas.quejasUR.busquedaTipo=Type.
quejas.quejasUR.redaccion=Drafting of the complaint.
indicadores.indicadorhandle.Perioricidad= - At least select a day in the field "Periodicity".\\n
indicadores.indicadorhandle.PerioricidadDeRevision= - At least select a day in the field "Revision\\'s Periodicity"\\n
accion.acciongenericahandle.javascript.Razonporlaquenoprocede=- Reason for not applicable.
boton.AceptarReal=Accept
edit.user.exito.reset=Password has been succesfully updated. Do you want to logoff?
documentos.tag.relacionados=Related documents
accion.accionatomarhandle.javascript.LaAccionDebeVerificarseDespuesDelDiaDeHoy=The action sould be checked after the day of today
accion.accionatomarhandle.javascript.LaAccionDebeImplementarseDespuesDelDiaDeHoy=The action sould be implemented after the day of today
components.comboYesNo.Yes=Yes
components.comboYesNo.No=No
administrator.login.cerrarsesion.SeCerroSesion=Sesion Closed
login.login.habilitarcookies=It is necessary to enable cookies on your browser.
components.loader.cargando=Loading...
menu.folderTreeLeftFrame.config.Interface=Interface
menu.folderTreeLeftFrame.config.InterfaceTRESS=TRESS
menu.folderTreeLeftFrame.config.settings=Settings
menu.folderTreeLeftFrame.config.Sistema=System
menu.folderTreeLeftFrame.Hisrotial=Access history
menu.folderTreeLeftFrame.DebugInformation=Debug information
menu.folderTreeLeftFrame.Jerarquia=Organizational hierarchy
menu.folderTreeLeftFrame.Unidaddenegocio=${Facility}
menu.folderTreeLeftFrame.Plantas=${Facility}
menu.folderTreeLeftFrame.Departamentos=Departments
menu.folderTreeLeftFrame.Building=Building
menu.folderTreeLeftFrame.DepartamentosPlantas=Departments / 
menu.folderTreeLeftFrame.ProcesosDepartamentos=Processes / Departments
menu.folderTreeLeftFrame.Roles=Roles
menu.folderTreeLeftFrame.Perfil=Profile
menu.folderTreeLeftFrame.Puesto=Job
menu.folderTreeLeftFrame.Catalogos=Catalogs
menu.folderTreeLeftFrame.Clausulas=Standard clauses
menu.folderTreeLeftFrame.AuditType=Audit Type
menu.folderTreeLeftFrame.ClauseType=Standard
menu.folderTreeLeftFrame.Calificaciones=Grades
menu.folderTreeLeftFrame.Tiposquejas=Complaint analysis types
menu.folderTreeLeftFrame.Indicadores=Indicators
menu.folderTreeLeftFrame.Equipos=Device
menu.folderTreeLeftFrame.Foliosdeequipo=Device reference number
menu.folderTreeLeftFrame.Patronesdemedicion=Measurement standards
menu.folderTreeLeftFrame.Tiposdeequipos=Device types
menu.folderTreeLeftFrame.Estatusdeequipos=Devices status
menu.folderTreeLeftFrame.Variablesdemedicion=Measurement variables
menu.folderTreeLeftFrame.Unidadesdemedicion=Measurement units
menu.folderTreeLeftFrame.Tipodeservicio=Service types 
menu.folderTreeLeftFrame.Resultadodeservicio=Service Result
menu.folderTreeLeftFrame.Gruposdeequipos=Groups of devices
menu.folderTreeLeftFrame.Consolidadoderespuestas=Response Repository
menu.folderTreeLeftFrame.Desechados=Discarded
menu.folderTreeLeftFrame.Servicios=Services
menu.folderTreeLeftFrame.Programacion=Programming
menu.folderTreeLeftFrame.Serviciosprogramados=Services scheduled
menu.folderTreeLeftFrame.Planeados=Planned
menu.folderTreeLeftFrame.NoPlaneados=Unplanned
menu.folderTreeLeftFrame.Historialdeservicios=Service history
menu.folderTreeLeftFrame.Servicionoplaneado=Unplanned service
menu.folderTreeLeftFrame.Metricadeservicio=Service metrics
menu.folderTreeLeftFrame.Procesos=Processes
configuracion.repositoriohandle.javascript.Lainformacionnoseraguardada=The information will not be saved.\\nAre you sure you cancel it?
common.input.ALTADELUGARDEALMACENAMIENTO=Create storage places
menu.folderTreeLeftFrame.LugaresdeAlmacenamiento=Storage place
common.input.ALTADECLASIFICACIONINFORMACION=Create information clasification
common.input.ALTADEDISPOSICION=Create disposition
common.input.FUENTESDEACCIONES=Create source of findings
common.input.FUENTESDEQUEJAS=Source of complaints
common.input.TiposdeAnalisisdeQuejas=Clasification
common.input.AltadeTIPOSDECALIFICACIONES=New TYPES OF QUALIFICATIONS
common.input.obligatorio255caracteres=This field is required and supports alphanumeric text up to 255 characters.
common.input.obligatorio=This field is required
common.comentario.Agregueuncomentario=Add a comment
configuracion.userhandle.RolenValidaciones=Validations role:
evaluaciones.evalAnswerSave.Losdatosseguardaroncorrectamente=The data is saved correctly.
evaluaciones.evalAnswerSave.Ocurriounerroralguardarsusrespuestas=There was an error saving your answers, contact the system administrator if the problem persists.
evaluaciones.evalAnswerSave.Laencuestafuecontestadaparcialmente=The survey was answered partially, please finish it later.
evaluaciones.evalAnswerSave.Elformatoelectronicofuellenadoparcialmente=The electronic format was partially filled, you can finish filling later.
evaluaciones.evalOptionEdit.Estaseguroquedeseaborrarlaopcion=Are you sure you want to delete the option?
evaluaciones.evalOptionEdit.Laopcionfueeliminadasatisfactoriamente=The option was deleted successfully.
evaluaciones.evalOptionEdit.Laopcionnopudosereliminada=The choice could not be eliminated.
evaluaciones.questionQuestionEdit.Numerodeopciones=Number of options:
evaluaciones.questionQuestionEdit.Opciones=Options:
evaluaciones.questionQuestionEdit.Esobligatoria=Is mandatory:
evaluaciones.questionQuestionEdit.Losdatosfueronactualizadoscorrectamente=Data updated successfully.
evaluaciones.questionQuestionEdit.Lapreguntaseguardocorrectamente=The question was saved correctly
evaluaciones.evalSave.Laevaluacionfueactualizadasatisfactoriamente=The assessment was updated successfully.
evaluaciones.evalSave.Nosepudieronguardarsatisfactoriamentelosdatos=Could not save data successfully.
alert.Ocurriounerroralguardarelregistro=An error occurred while saving the record
evaluaciones.evalSectionEdit.CREARSECCION=CREATE SECTION
evaluaciones.evalSectionEdit.Seccion=Section:
evaluaciones.encuestaslist.Elcuestionarionocuentaconpreguntas=- The questionnaire has questions -
evaluaciones.encuestashandle.DocumentodeTips=Document Tips:
evaluaciones.encuestashandle.Presioneaqui=Press here.
evaluacion.reportCreate.Debesseleccionarlaevaluacion=You must select the evaluation.
evaluacion.reportCreate.Debeseleccionaruntipodereporte=You must select a report type.
evaluacion.reportCreate.Debeseleccionarlavariableporlaquegraficara=You must select the variable for which will plot.
evaluacion.reportCreate.Debeseleccionarunmes=You must select a month.
evaluacion.reportCreate.Debeseleccionaruna\u00f1o=You must select a year.
evaluacion.reportCreate.Debeseleccionarunaquincena=You must select a fortnight.
evaluacion.reportCreate.Debeseleccionarunmesinicial=You must select an initial month.
evaluacion.reportCreate.Debeseleccionaruna\u00f1oinicial=You must select a starting year.
evaluacion.reportCreate.Debeseleccionarunmesfinal=You must select a final month.
evaluacion.reportCreate.Debeseleccionaruna\u00f1ofinal=You must select a final year.
audit.reportshandle.Reporte=Report:
audit.reportshandle.Plantarequerida=The ground plant is required.
audit.reportshandle.Reportesdeauditorias=Audit reports
audit.reportshandle.Hallazgospordepartamentoenprocesos=Findings by department processes
audit.reportshandle.Resultadosdeauditoria=Audit results
audit.reportshandle.Auditoriasagendadas=Scheduled audits
audit.reportshandle.Fechadelaauditoria=Audit date:
audit.reportshandle.Formato=Format:
audit.reportshandle.PDF=PDF
audit.reportshandle.HTML=Web Page (HTML)
audit.reportshandle.Excel=Microsoft Excel (XLS)
audit.reportshandle.Generarreporte=Generate
accion.accionatomarhandleamc.SolicituddeAcciondeCorreccionInmediata=Request of immediate correction action
boton.accion.Iramisacciones=Go to my findings
documentos.documentonocontroladohandle.SubirArchivo=Upload File
documentos.documentospendientes.Pendientes=Tasks
documentos.documentospendientes.Pendientesdeequipos=Devices - task
pending.handle.Porconfirmar=Pending confirmation:
pending.handle.PorAutorizarCambio =To authorize date change:
pending.handle.PorAutorizarCambioManager =To authorize date change (module manager):
pending.handle.Porrealizar=To be performed:
pending.handle.PorrealizarAuditor=To be performed (Support auditor):
pending.handle.Poraceptarresultados=Pending to accept results:
pending.handle.AccionesporAgregarAccionesaTomar=Actions to choose an activity:
pending.handle.Equiposporreemplazar=Devices to be replaced:
pending.handle.Equiposporprogramarservicios=Services to be programed:
pending.handle.ProgramacionesporAutorizar=Scheduling to Approve:
pending.handle.ServiciosporAutorizar=Servicios to approve:
pending.handle.Equiposconserviciosporrealizar=Services to be performed:
pending.handle.Equiposconserviciosvencidos=Due services:
pending.handle.Pendientesdeencuestas=Surveys - tasks
pending.handle.Porresponder=To be applied:
accion.acciongenericahandle.VERDETALLEDEACCION=VIEW DETAILS ACTION
quejas.quejashandle.msjusuarioresponsableinactivo=The user in charge of answering the complaint is inactive, choose another.
poll.poll.handle.Estatus=Status:
poll.poll.handle.Clave=Code:
survey.Seccion=Section
survey.Abierta=Open
survey.Menu=Menu
survey.Opcionmultiple=Multiple option
survey.Unarespuesta=One answer
survey.SioNo=Yes or No
survey.Matriz=Array
survey.Matrizverticaldemenus=Vertical array of menus
survey.Instrucciones=Instructions
survey.AbiertaRenglones=Open (Lines)
survey.Matrizmultiple=Multiple array
survey.Matrizdemenus=Menus array
boton.Guardarcopia=Save Copy
survey.mostrarsuinformacion=Click on an item to see its information.
survey.Mostrarmaspreguntas=Show more types of questions
survey.Activardesactivarvalores=Enable and disable response values
survey.Unidadesdenegocioaplicar=Questionnaire related ${facilities}
survey.Propiedades=General properties of the questionnaire items
survey.Mostrarmenospreguntas=Show fewer types of questions
survey.Titulodelcuestionario=Questionnaire title(click to edit)
survey.alert.Lainformacionnoguardadaseperdera=The unsaved information will be lost, Are you sure you want to cancel?
survey.message.borrarlosvaloresdelasrespuestasyaasignadas=Are you sure you want to clear the values \u200b\u200bof the responses already assigned?
survey.message.sieligesilasmismasyanopodranserrecuperadas=if you choose "Yes" the same can no longer be recovered.
survey.message.Unidadesdenegocioalasqueaplicaelcuestionario=Questionnaire related ${facilities}
survey.message.Estecuestionarioyaestasiendousado=This questionnaire is already being used in a survey will not save changes
survey.message.Regresandoalistado=Returning to list
survey.Estatus\u200b=Status
survey.Activo=Active
survey.Bloqueado=Locked
survey.Sinroles=No roles
survey.Conroles=With roles
survey.Esquemadetienda\u200b=Store scheme
survey.Plantas=${Facilities}
survey.Agregarplanta=Add ${facility}
survey.Finalizar=Finalize
survey.message.noRegMessage=No records added
survey.Unidadesdenegocio=${Facilities}
title.Agregar=Add
title.UnidadOrg=Org. Unit
auditoria.auditoriaquestionhandle.Lapreguntafueborradacorrectamente=The question has been deleted successfully.
survey.surveycapture.LaOperacionhasidorealizadaconexito=The operation has been successfully completed.
survey.surveycapture.ErrorDetectado=Error Detected.
survey.surveycapture.completarlosdatos=Please complete the data. Data with an asterisk (*) are required.
survey.surveycapture.cuestionarioyafuellenado=This questionnaire has already been completed in full and does not have permission to view the answers.
survey.surveycapture.oldVersion=There is a new version of the form available. If you need to fill in the new version, request the cancellation to {requestAuthor} otherwise continue with the filling.
survey.surveycapture.Aceptarresultados=Accept results
survey.surveycapture.Guardaravances=Save progress
survey.surveycapture.captureTime=Capture time
survey.surveycapture.Terminar=Finish
survey.surveycapture.Hallazgos=Findings
survey.surveycapture.Imprimir=Print
survey.surveycapture.Salir=Exit
survey.surveycapture.Cerrar=Close
survey.surveycapture.Ocurriounerror=An error occured!
survey.surveycapture.Entendido=Understood
survey.surveycaptureend.graciasparticipacion=Thank you very much for your participation in the survey Satisfaction Index Store Leader, your answers will help us work better for you.
survey.surveycaptureend.encuestacontestadadetalleinvalida=This survey has been answered and you are trying to view its detail in a invalid way
survey.surveycaptureend.Cuestionario=Questionnaire
survey.surveycaptureend.Cuestionariofinalizadonopermisoespera=This questionnaire has been filled and does not have permission to view, will be sent to the main screen of the application in
survey.surveycaptureend.Segundos=seconds
poll.pollregistrylist.Controlderegistros=Control records
indicadores.indicadorhandle.Responsabledellenado=Responsible of completing:
indicadores.indicadorhandle.Usuarios=Users
proyectos.proyectohandle.SeAgregaraUnaMetaDeseaContinuar=A new goal will be added. Continue?
proyectos.proyectohandle.NoSeHanRealizadoCambiosDeseaContinuar=There has\u00b4t been change to save, Do you want to continue and return to Project Control?
proyectos.proyectohandle.SeHanRealizadoCambiosDeseaContinuar=There are changes in the project, Do you want to save and continue?
documentos.pdfviewer.Elmenucontextualestadeshabilitado=The context menu is disabled
documentos.pdfviewer.Inicio=Start
documentos.pdfviewer.Atras=Back
documentos.pdfviewer.Siguiente=Next
documentos.pdfviewer.Ultimo=Last
boton.AltaR=Add meetings
documentos.componente.ErrorNoseencontrounobjetodecodigoJS=Error:No code found of a JS object, ID sought
documentos.componente.seleccione=-- SELECT --
documentos.componente.UnidadProductiva=Production Unit
documentos.componente.Departamento=Departament
documentos.componente.Proceso=Process
documentos.componente.Area=Area
documentos.componente.Usuario=User
documentos.componente.Filtros=Filters:
documentos.componente.Nombre=Name:
documentos.documentoshandle.AgregarLectores=Readers
documentos.documentoshandle.Descargar=Download
documentos.documentoshandle.Agregar=Add
documentos.documentoshandle.Cerrar=Close
documentos.documentoshandle.Clave2=ID
documentos.documentoshandle.Puestos=Job
documentos.documentoshandle.Plantas=${Facilities}
documentos.documentoshandle.UnidadesOrg=Units Corp.
documentos.documentoshandle.Descripcion=Description
documentos.documentoshandle.NosehanagregadoLectores=Readers not added
documentos.documentoshandle.QuitarLector=Remove reader
documentos.documentoshandle.AsignarDocumentosRelacionados=Assign related documents
documentos.documentoshandle.AgregarDocumentoRelacionado=Related Document
documentos.documentoslistcd.Titulo=Title
auditoria.auditorialist.FechadeInicio=Start Date
documentos.documentoshandle.Verificador=Verifiers
documentos.documentonocontroladohandle.SubirArchivo=Upload File:
documentos.documentoslistadocp.Carpeta=Mover
documentos.documentoslistadocp.Mover=Move
documentos.login.principal.Repositoriodedocumentos=Repositories, click to see the folders
documentos.documentoslist.FechaaCancelacion=Cancellation Date
documentos.documentlist.Revision=Revision
documentos.slideshow.GettheFlashPlayer=Get the Flash Player
documentos.slideshow.toseethisplayer=to see this player.
documentos.referenciahandle.\u00bfEstaseguroquedeseaagregarlosdocumentosseleccionados?=Are you sure you want to add the selected documents?
documentos.referenciahandle.Sedebeagregaralmenosundocumentoparareferenciar=You must add at least one document for referencing
documentos.try.download.later.Descarga=Download
documentos.try.download.later.ElarchivosolicitadoestasiendoconvertidoaformatoPDF=The requested file is being converted to PDF format for viewing, please wait ......
documentos.uploadfile.Ocurriounerrordeconexion=Ocurri\u00f3 un error de conexion al intentar subir el archivo, intente de nuevo \u00f3 si el problema persiste contacte al administrador.
documentos.uploadfile.Secompletoelproceso=Secomplet\u00f3 el proceso.
documentos.try.download.later.Ladescargasereintentara=\ (The download will retry in
documentos.try.download.later.segundos=\ seconds)
documentos.solicitudeshandle.Espere...=Wait...
documentos.solicitudeshandle.documentoenestadode=document in state of
documentos.solicitudeshandle.solicitudenestadode=request in state of
documentos.solicitudeshistory.DETALLEDEHISTORIALDELASOLICITUD=HISTORY DETAIL REQUEST
documentos.solicitudeshistory.PrimeraVersion=Fisrt Version
documentos.solicitudeshistory.VersionActual=Current Version
documentos.referenciahandle.Seleccione...=Select...
cuestionarios.cuestionarioshandle.Cuestionariosdeauditorias=Audit Questionnaires
cuestionarios.cuestionarioshandle.Agreguealmenosunaseccion=Add at least one section.
cuestionarios.preguntas.Seleccionealmenosunaseccion=Select a section, if you have not added one, you can do it in the icon \\"Section \\" in the \\"Control Questionnaires\\".
cuestionarios.preguntas.Selecioneyagreguealmenosunapreguntadelaseccion=Select and add at least one question from the section
cuestionarios.preguntasobv.Respuestasdeauditorias=Answers audits
solicitudes.solicitudeshandle.RegresarSolicitud=Return request
solicitudes.solicitudeshandle.DocumentoElectronico\:=Electronic Document:
solicitudes.solicitudeshandle.RegresarlaSolicitud=Return the request
solicitudes.solicitudeshandle.Primerodebeseleccionarundocumento=First you must select a document
solicitudes.solicitudeshandle.Aunnohacesninguncambio=Still do not make any change, to continue you must change a value
solicitudes.solicitudeshandle.Especifiquelarazonporlaquesequiereregresareldocumento=Enter the reason why you want to return the document
solicitudes.solicitudeshandle.Soloseaceptannumeros=Only numbers are allowed
solicitudes.solicitudeshandle.Espereaquesecarguenlosdocumentos.=Wait for the document to be loaded
documentos.solicitudeshandle.Archivoenprocesodemodificacion=File modification process
documentos.solicitudeshandle.RevisionAnterior=Previous Review:
documentos.documentoshandle.Nosehanagregadolectores=Not been added readers
indicadores.indicadorhandle.Formadecalculo=Calculation form:
indicadores.revisionindicadorhandle.Detallederegistros=Detail record
indicadores.revisionindicadorhandle.periodo=period
indicadores.revisionindicadorhandle.mensajeIsValid=The finding will be discharged and will start tracking the action module <br/>
indicadores.revisionindicadorhandle.mensajeIsValid2=Are you sure you want to continue?
indicadores.revisionindicadorhandle.mensajeIsValid3=The action has been created.
indicadores.revisionindicadorhandle.mensajeIsValid4=The finding was not discharged.
indicadores.revisionindicadorhandle.mensajeValidatRules=Write a finding
indicadores.revisionindicadorhandle.mensajeValidatRules2=Write a consequence
indicadores.revisionindicadorhandle.mensajeValidatRules3=Write a title
indicadores.revisionindicadorhandle.mensajeValidatRules4=Select a source
indicadores.revisionindicadorhandle.mensajeValidatRules5=Select the type of action
indicadores.objectivelist.ListadeCuestionarios=Questionnaires List
configuracion.repositoriohandle.javascript.RegistroDuplicado=There was an error, please verify the information you entered isn\\'t duplicated.
audit.reportshandle.Planta=Plant:
audit.reportshandle.Proceso=Process:
survey.TituloPropiedades=Properties
accion.acciongenericahandle.Clave=ID
accion.accionatomarhandleamc.SolicituddeAccionaTomar=Perform new action
common.input.SinLiga=--NO LINK--
common.input.Requiereanalisis=Analisis Required 
common.input.SI=YES
common.input.Ligaa=Link to:
common.input.ModulodeAuditorias=-Audit's Module
common.input.ModulodeIndicadores=-Indicators's Module
common.input.ModulodeQuejas=-Complaints's Module
common.input.ModulodeValidaciones=-Validation's Module
common.input.Seactualizaronlosdatosdelcatalogocorrectamente=The catalog data was updated correctly.
common.input.LainformacionnoseraguardadaEstasegurodecancelar=The information will not be saved. \\nDo you really want to cancel?
common.input.RequiereAnalisis=Requires analysis:
common.input.SI=YES
common.input.NO=NO
menu.folderTreeLeftFrame.DepartamentosPlantas2=${Facility}
documentos.documentoslector.Departamento=Department
documentos.documentoslector.current.Departamento=Current department
documentos.documentoslector.lastDepartamento=Last department
boton.Eliminar=Delete
survey.surveycapture.Calificacionnumerica=Numeric evaluation
menu.folderTreeLeftFrame.EstadodeAcciones=Actions status
accion.acciongenericahandle.Seguimientodeaccion=Finding follow-up
accion.accionatomarhandleamc.RazondelaultimaModificaciondelaI=Last change reason of<br>date
accion.accionatomarhandleamc.RazondelaultimaModificaciondelaV=Last change reason of<br>date
survey.sinclausula=--No clause--
reportesgenerales.planta=${Facility}:
menu.folderTreeLeftFrame.ListaDeDistribucionPorDocumento=By document
menu.folderTreeLeftFrame.ListaDeDistribucionPorUsuario=By user
menu.folderTreeLeftFrame.ListaDeDistribucionListaCompleta=Full list
menu.folderTreeLeftFrame.ListaDeDistribucion=Distribution list
documentos.documentoshandle.Listadodepuestos=List of assigned jobs to generate controlled copies
documentos.documentoshandle.Listadodeusuarios=List of users assigned to read the document
documentos.documentoshandle.Agregarpuestos=Jobs
documentos.documentoshandle.Documentosrelacionados=Related documents
documentos.documentoshandle.Ocultar=Hide
documentos.documentoshandle.Terminar=Finish
documentos.documentoshandle.Fechadelectura=Read date
documentos.documentoshandle.Noleido=Unread
documentos.documentoshandle.Leidosinpendiente=Read without task
documentos.documentoshandle.Leidodesdependientes=Read from task
documento.documentoshandle.NosehanagregadoLectores=No readers added
configuracion.configuacionhandle.colordelsistema=System color:
proyectos.proyectohandle.SeAgregaraUnaMetaDeseaGuardarLosCambiosDelProyectoAntesDeContinuar=A goal will be added. Do you want to save changes before continue?
documentos.documentoshandle.Nosehanasignadopuestosaldocumentocontrolado=Jobs have not been assigned to document
documentos.documentoshandle.Haocurridounerroralintentargenerarlacopiacontrolada=There was an error on the document printing.
accion.accionatomarhandle.javascript.Lafechadeimplementaciondebesermayoralaactual=The implementation date must be greater or equal than the current
accion.accionatomarhandle.javascript.Lafechadeverificaciondebesermayoralaactual=The check date must be greater or equal than the current
documentos.documentoshandle.reaprobacion=Re-approve
documentos.documentoshandle.nuevo=New
documentos.documentoshandle.modificacion=Modification
documentos.documentoshandle.cancelacion=Cancellation
documentos.documentoshandle.tiempo=Storage time
documentos.documentoshandle.disposicion=Disposition
documentos.documentoshandle.tipodesolicitud=Request type
proyectos.actividad.avance.aceptar=Add to progress
proyectos.proyectohandle.javascript.Planta=${Facility}
proyectos.proyectohandle.Planta=${Facility}
indicadores.indicadorhandle.PlantaNecesario=-${AFacility} must be assigned.\\n
indicadores.indicadorhandle.txtMetaNecesario=-A target must be assigned.\\n
action.mail.code=Code: 
action.mail.title=Title: 
action.mail.author=Author: 
action.mail.created_on=Creation date: 
action.mail.source=Source: 
action.mail.action=Finding: 
action.mail.consequence=Consequence: 
action.mail.department=Department: 
action.mail.accepted_by=Accepted by:
action.mail.accepted_on=Accepted on: 
action.mail.attendant=Attendant: 
action.mail.does_it_proceed=Does it proceed?: 
action.mail.root_causes_analisys=Root causes analisys: 
action.mail.evaluation_date=Evaluation date: 
action.mail.evaluation_result=Evaluation results: 
action.mail.approved_by=Approved by: 
action.mail.approval=Approval: 
action.mail.approved_on=Approved on: 
action.mail.status=Status: 
action.mail.closed_on=Closure date:  
action.mail.attachment_number=Attachments Qty: 
action.mail.accomplished_on=Accomplished on: 
action.mail.not_proceeding_reazon=Not proceeding reazon: 
action.mail.effectiveness_result=Effectiveness results: 
action.mail.parent_action=Parent action: 
action.mail.contingency=Contingency: 
configuracion.repositoriohandle.boton.Regresar=Regresar
documentos.documentospendientes.Documentosporentregarcopiacontrolada=Documents to deliver controlled copy:
documentos.documentospendientes.DocumentosPorRecogerCopiaControlada=Documents to collect controlled copy:
indicadores.indicadorlist.Aunnohayindicadorescapturados=A&uacute;n no hay indicadores capturados.
auditoria.misauditorias.PresioneparaCerrarla=Presione para cerrarla.
auditoria.auditoriahandle.javascript.Control=Control
configuracion.userhandle.RolEnValidaciones=Rol en Validaciones:
configuracion.repositoriohandle.RepositorioPadre=Repositorio Padre: 
auditoria.misauditorias.PresioneparaMarcarcomoRealizada=Presione para marcar como realizada.
auditoria.auditorialist.TodasLasAuditorias=Todas las Auditor\u00edas
accion.acciongenericahandle.AccionATomar=Alta de Acci\u00f3n Tomar:
Reportes.ReporteGeneralDeAuditorias=REPORTE DE AGENDA DE AUDITOR&Iacute;AS
configuracion.repositoriohandle.EsTopLevel=Es de Nivel Superior: 
configuracion.repositoriohandle.Raiz=Documentos
documentos.documentoshandle.lectoresdelacopiacontrolada=Lectores de la copia controlada:
mail.request.requestApproved=La solicitud que creaste ha sido aprobada.
Proyectos.hrs=hrs
action.mail.not_proceed=No
action.mail.proceed=Yes
action.mail.open=Open
action.mail.closed=Closed
actividad.actividadhandle.javascript.-TrabajoRealizadoDisminuido=The amount of work may not be reduced from 
quejas.handle.EstaSeguroSambiarDepartamentoQueja=Are you sure, you wish to change the department to witch the complaint was submitted?
documentos.documentoslector.LugarDeAlmacenamiento=Storage Location
accion.acciongenericahandle.Aceptacion=Acceptance
survey.surveycapture.CancelFillForm=Definitive cancellation
message.noValidValue=The value joined is not valid
menu.folderTreeLeftFrame.ActivitiesType = Activities's Type
menu.folderTreeLeftFrame.CatalogoActivities = Activities
menu.folderTreeLeftFrame.CatalogoActivitiesType = Activities's Type
menu.folderTreeLeftFrame.CatalogoActivitiesPriority = Activities's Priority
menu.folderTreeLeftFrame.CatalogoActivitiesSource = Activities's Source
menu.folderTreeLeftFrame.CatalogoActivitiesObjective = Activities's Objective
pending.handle.AuditsActivityToComplete = Activities to do:
pending.handle.AuditsActivityToVerify = Activities to verify:
pending.handle.AuditsActivityToVerifyDelayed = Delayed activities to verify:
pending.handle.AuditsActivityToVerifyNotApply = Activities to verify as cancelled:
survey.surveycapture.Activities = Activities
pending.handle.header.ActivitiesPendings = Activities - Tasks
pending.handle.ActivitiesToComplete = To do:
pending.handle.ActivitiesToVerify = To verify:
pending.handle.ActivitiesToVerifyDelayed = Delayed activities to verify:
pending.handle.ActivitiesToVerifyNotApply = To verify as cancelled:
menu.folderTreeLeftFrame.Activities = List
menu.folderTreeLeftFrame.MyActivity = My activities
menu.folderTreeLeftFrame.ControlActivity = Activities control
menu.folderTreeLeftFrame.ControlFindingActivity = Activities control
menu.folderTreeLeftFrame.ActivityRecycleBin = Activities recycle bin
menu.folderTreeLeftFrame.RecurrentMeetingControl = Recurrence list
menu.folderTreeLeftFrame.MeetingRecurringRecycleBin = Recurrence recycle bin
menu.folderTreeLeftFrame.ConsolidatedActivities = Consolidated activities
menu.folderTreeLeftFrame.ActivitiesReport = Reports
message.noValidValue=The captured value is not valid.
message.invalidSelectedValue=The selected option is invalid.
accion.acciongenericahandle.Aceptacion=Acceptance
boton.SaveTemplate=Save as template
menu.folderTreeLeftFrame.dynamicField=Dynamic field
components.loader.cargando=Loading...
reportesgenerales.Estado=Status
menu.folderTreeLeftFrame.PapeleraMeetrings=Meeting trash
emptyReportActions=No results found
accion.accionatomarhandleamc.VerificadorContencion=Action verifier:
accion.rememberCreate=Remember to create/assign actions to take.
accion.accionatomarhandleamc.ResponsabledeActividad=Responsible of the activity:
progressComment=Comentario de avance
accion.accionatomarhandleamc.VerificadorActividad=Verifier activity
accion.accioncorreccioninmediata.javascript.fechaverificacio=The date of verification must be greater than the date of implementation
menu.folderTreeLeftFrame.fileType=File type
configuracion.userhandle.SearchInSubfolders=Search by subfolders:
configuracion.userhandle.ShowExternalDialog=Show download dialog of Bnext Launcher <br> installer to open documents:
message.accept=Accept
indicadores.revisionindicadorhandle.IndicadorNoCalificado=This indicator is not qualified.
addedActionHistory=History comments added.
CouldNotAddCommentsHistory=Could not add comments history.
menu.folderTreeLeftFrame.automaticTasks = Automatic tasks
menu.folderTreeLeftFrame.dailyTasks = Daily tasks
menu.folderTreeLeftFrame.hourlyTasks = Hourly tasks
menu.folderTreeLeftFrame.dailyMails = Daily mails
menu.folderTreeLeftFrame.RecurrentMeetingControl = Recurrence list
menu.folderTreeLeftFrame.MeetingRecurringRecycleBin = Recurrence recycle bin
indicadores.revisionindicadorhandle.Indicador=Indicator
actividad.actividadhandle.javascript.VerifiqueLosReponsables = Verify the responsible
actividad.actividadhandle.javascript.ElResponsableNoPuedeSerElVerificador = The verifier can not be a user of those assigned as responsible.
actividad.actividadhandle.AceptadaPor=Accepted by:
actividad.actividadhandle.RechazadaPor=Rejected by:
actividad.actividadhandle.FechaDeAceptacion=Acceptance date:
actividad.actividadhandle.FechaDeRechazo=Rejection date:
accion.accioncorrectivahandle.boton.Regresar=Return
menu.folderTreeLeftFrame.config.Generales=General
menu.folderTreeLeftFrame.userAgent=User Agent
accion.acciongenericahandle.javascript.Laoperacionsehallevadoacabo=The operation has been executed.
general.reports.badHostError=Please contact yor system administrator and ask him to verify the following configuration fields"Site URL, Site Folder, Host".
ACC-FINDING-TO-ASSIGN = Actions to assign responsible
ACC-FINDING-TO-START-IMPLEMENTATION = Actions to add immediate correction action
ACC-FINDING-TO-ANALYZE = Actions to analyze
ACC-FINDING-TO-ADD-PLAN = Actions to choose an activity
ACC-FINDING-TO-EVALUATE = Actions to accept
FINDING-ACTIVITY-TO-VERIFY = Activities to verify
FINDING-ACTIVITY-TO-COMPLETE = Activities to complete
FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY = Actions to verify as Cancelled
FINDING-ACTIVITY-TO-VERIFY-DELAYED = Delayed activites to verify
COMPLAINT-TO-ASSIGN = Complaints to assign responsible
COMPLAINT-TO-RESPOND = Complaints to answer
COMPLAINT-TO-VERIFY = Complaints to verify answer
COMPLAINT-TO-EVALUATE = Handled complaints to evaluate effectiveness
action = Findings
complaint = Complaints