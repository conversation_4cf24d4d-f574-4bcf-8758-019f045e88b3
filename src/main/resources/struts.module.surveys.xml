<?xml version="1.0" encoding="UTF-8" ?>
<!-- 
    Module     : Encuestas
    Created on : 08/01/2013, 10:56:57 AM
    Author     : <PERSON>
-->
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.5//EN"
        "http://struts.apache.org/dtds/struts-2.5.dtd">
<struts>
    <package name="json-surveys" extends="json-bnext">
        <interceptors>
            <interceptor-stack name="bnextStack">
                <interceptor-ref name="exception"/>
                <interceptor-ref name="alias"/>
                <interceptor-ref name="servletConfig"/>
                <interceptor-ref name="i18n"/>
                <interceptor-ref name="prepare"/>
                <interceptor-ref name="chain"/>
                <interceptor-ref name="scopedModelDriven"/>
                <interceptor-ref name="modelDriven"/>
                <interceptor-ref name="datetime"/>
                <interceptor-ref name="staticParams"/>
                <interceptor-ref name="actionMappingParams"/>
                <interceptor-ref name="params"/>
                <interceptor-ref name="conversionError"/>
                <interceptor-ref name="workflow">
                    <param name="excludeMethods">input,back,cancel,browse</param>
                </interceptor-ref>
                <interceptor-ref name="debugging"/>
            </interceptor-stack>
        </interceptors>
        <action name="Survey" class="isoblock.surveys.struts2.action.Surveys_CRUD_Action" method="smd">
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <!--
                        using excludeProperties add around 20 seconds
                        to the response time for surveys with hundreds of questions
                 -->
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
                <param name="excludeNullProperties">true</param>
            </result>
        </action>
        <action name="SurveyDataSource" class="isoblock.surveys.struts2.action.Surveys_CRUD_Action" method="smd">
            <interceptor-ref name="json">
                <param name="enableSMD">true</param>
                <param name="excludeProperties">
                    result.*\.logo.info,
                    result.*\.foto.info,
                    result.*\.archivos.*\.info,
                    result.*\.plan.tareas,
                    result.*\.pais\.[^id],
                    result.*\.filedata,
                    result.*\.puestos.*\.regiones, 
                    result.*\.puestos.*\.paises,
                    result.*\.puestos.*\.unes, 
                    result.*\.puestos.*\.subUno, 
                    result.*\.puestos.*\.subDos,
                    result.*\.puestos.*\.perfilId,
                    result.*\.data.*\.puestos.*\.regiones, 
                    result.*\.data.puestos.*\.paises,
                    result.*\.data.puestos.*\.unes, 
                    result.*\.data.puestos.*\.subUno, 
                    result.*\.data.puestos.*\.subDos,
                    result.*\.data.puestos.*\.perfilId,
                    result.*\.files.*\.info,
                    result.*\.plan.*\.busquedasArchivos,
                    result.*\.globals.*\.surveyList,
                    result.*\.globals.*\.saveHandling,
                    result.*\.globals.*\.tableInfo,
                    result.*\.globals.*\.obj.*\.surveyGlobalList,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyFieldObjectList,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyFieldObjectList1,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyFieldObjectList2,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyFieldObjectList3,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyGlobalObjectList,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyGlobalObjectList1,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyGlobalObjectList2,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyHeaderList,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyItemList,
                    result.*\.globals.*\.obj.*\.pages.*\.surveyMatrixOptionsList,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyFieldObjectList,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyFieldObjectList1,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyFieldObjectList2,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyFieldObjectList3,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyGlobalObjectList,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyGlobalObjectList1,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyGlobalObjectList2,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyHeaderList,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyItemList,
                    result.*\.globals.*\.obj.*\.languages.*\.surveyMatrixOptionsList,
                    result.*\.globals.*\.obj.*\.title.*\.surveyFieldObjectList,
                    result.*\.globals.*\.obj.*\.title.*\.surveyFieldObjectList1,
                    result.*\.globals.*\.obj.*\.title.*\.surveyFieldObjectList2,
                    result.*\.globals.*\.obj.*\.title.*\.surveyFieldObjectList3,
                    result.*\.globals.*\.obj.*\.title.*\.surveyGlobalObjectList,
                    result.*\.globals.*\.obj.*\.title.*\.surveyGlobalObjectList1,
                    result.*\.globals.*\.obj.*\.title.*\.surveyGlobalObjectList2,
                    result.*\.globals.*\.obj.*\.title.*\.surveyHeaderList,
                    result.*\.globals.*\.obj.*\.title.*\.surveyItemList,
                    result.*\.globals.*\.obj.*\.title.*\.surveyMatrixOptionsList,
                    result.*\.fields.*\.surveyList,
                    result.*\.fields.*\.obj.*\.surveyFieldList,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyFieldObjectList1,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyFieldObjectList2,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyFieldObjectList3,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyGlobalObjectList,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyGlobalObjectList1,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyGlobalObjectList2,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyHeaderList,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyItemList,
                    result.*\.fields.*\.obj.*\.headerTitles.*\.title.*\.surveyMatrixOptionsList,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyFieldObjectList1,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyFieldObjectList2,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyFieldObjectList3,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyGlobalObjectList,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyGlobalObjectList1,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyGlobalObjectList2,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyHeaderList,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyItemList,
                    result.*\.fields.*\.obj.*\.matrixOptions.*\.title.*\.surveyMatrixOptionsList,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyFieldObjectList1,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyFieldObjectList2,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyFieldObjectList3,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyGlobalObjectList,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyGlobalObjectList1,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyGlobalObjectList2,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyHeaderList,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyItemList,
                    result.*\.fields.*\.obj.*\.hiddenMatrixItems.*\.surveyMatrixOptionsList,
                    result.*\.fields.*\.obj.*\.items.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyFieldObjectList1,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyFieldObjectList2,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyFieldObjectList3,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyGlobalObjectList,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyGlobalObjectList1,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyGlobalObjectList2,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyHeaderList,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyItemList,
                    result.*\.fields.*\.obj.*\.items.*\.itemText.*\.surveyMatrixOptionsList,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyFieldObjectList1,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyFieldObjectList2,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyFieldObjectList3,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyGlobalObjectList,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyGlobalObjectList1,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyGlobalObjectList2,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyHeaderList,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyItemList,
                    result.*\.fields.*\.obj.*\.subTitle.*\.surveyMatrixOptionsList,
                    result.*\.fields.*\.obj.*\.title.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.title.*\.surveyFieldObjectList1,
                    result.*\.fields.*\.obj.*\.title.*\.surveyFieldObjectList2,
                    result.*\.fields.*\.obj.*\.title.*\.surveyFieldObjectList3,
                    result.*\.fields.*\.obj.*\.title.*\.surveyGlobalObjectList,
                    result.*\.fields.*\.obj.*\.title.*\.surveyGlobalObjectList1,
                    result.*\.fields.*\.obj.*\.title.*\.surveyGlobalObjectList2,
                    result.*\.fields.*\.obj.*\.title.*\.surveyHeaderList,
                    result.*\.fields.*\.obj.*\.title.*\.surveyItemList,
                    result.*\.fields.*\.obj.*\.title.*\.surveyMatrixOptionsList,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyFieldObjectList,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyFieldObjectList1,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyFieldObjectList2,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyFieldObjectList3,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyGlobalObjectList,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyGlobalObjectList1,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyGlobalObjectList2,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyHeaderList,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyItemList,
                    result.*\.fields.*\.obj.*\.otherOptionText.*\.surveyMatrixOptionsList,
                    result.*\.saveHandling,
                    result.*\.tableInfo
                </param> 
            </interceptor-ref>
            <result type="json">
                <param name="noChache">true</param>
                <param name="enableSMD">true</param>
                <param name="excludeNullProperties">true</param>
            </result>
        </action>

    </package>
</struts>