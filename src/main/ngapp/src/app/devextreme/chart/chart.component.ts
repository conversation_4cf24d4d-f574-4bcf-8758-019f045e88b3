import * as DateUtil from '@/core/utils/date-util';
import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, type OnDestroy, type OnInit, inject, input } from '@angular/core';
import { DxChartModule } from 'devextreme-angular/ui/chart';
import { DxiSeriesModule, DxiValueAxisModule, DxoArgumentAxisModule, DxoCommonSeriesSettingsModule, DxoLabelModule, DxoLegendModule } from 'devextreme-angular/ui/nested';
import type { TimeworkScheduler } from 'src/app/modules/timework/timework-widget/timework-widget.interfaces';
import type { ChartData } from './chart-series-settings.enum';

@Component({
  selector: 'app-chart',
  templateUrl: './chart.component.html',
  styleUrls: ['./chart.component.scss'],
  imports: [DxChartModule, DxoCommonSeriesSettingsModule, DxiValueAxisModule, DxoArgumentAxisModule, DxoLabelModule, DxoLegendModule, DxiSeriesModule]
})
export class ChartTimelineComponent implements OnInit, OnDestroy {
  cdr = inject(ChangeDetectorRef);
  datePipe = inject(DatePipe);

  readonly dataSource = input<TimeworkScheduler[]>([]);

  readonly size = input<object>(undefined);

  itemsChart: ChartData[] = [];

  border = {
    color: 'white',
    width: 1,
    dashStyle: 'solid',
    visible: true
  };

  ngOnInit(): void {
    for (const item of this.dataSource()) {
      if (item.endDate !== null) {
        this.itemsChart.push({
          startDate: this.datePipe.transform(item.startDate, 'HH:mm', 'America/Monterrey'),
          endDate: this.datePipe.transform(item.endDate, 'HH:mm', 'America/Monterrey'),
          day: DateUtil.format(item.day, 'MMM D, YY'),
          duration: item.duration
        });
      } else {
        this.itemsChart.push({
          startDate: this.datePipe.transform(item.startDate, 'HH:mm', 'America/Monterrey'),
          endDate: this.datePipe.transform(new Date(), 'HH:mm', 'America/Monterrey'),
          day: DateUtil.format(item.day, 'MMM D, YY'),
          duration: item.duration
        });
      }
    }
    this.cdr.detectChanges();
  }

  customizedLabel(arg) {
    let labelText = '';
    if (arg.index === 1) {
      labelText = `Duración: ${arg.point.data.duration}`;
    }
    return labelText;
  }

  ngOnDestroy(): void {
    this.cdr.detach();
  }
}
