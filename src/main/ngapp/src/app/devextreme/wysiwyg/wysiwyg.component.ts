import { BnextCoreComponent } from '@/core/bnext-core.component';
import * as DomUtil from '@/core/utils/dom-util';
import { equalsObject } from '@/core/utils/object';
import {
  type AfterViewInit,
  Component,
  ElementRef,
  type OnChanges,
  type OnDestroy,
  type OnInit,
  SecurityContext,
  type SimpleChanges,
  inject,
  input,
  output,
  viewChild
} from '@angular/core';
import { DomSanitizer, type SafeHtml } from '@angular/platform-browser';
import { IgxDialogComponent } from '@infragistics/igniteui-angular';
import { type DxHtmlEditorComponent, DxHtmlEditorModule } from 'devextreme-angular/ui/html-editor';
import { DxiItemModule, DxoMediaResizingModule, DxoToolbarModule } from 'devextreme-angular/ui/nested';
import { BnextLoaderActivationService } from 'src/app/core/services/bnext-loader-activation.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';
import { DEVEXTREME_LANG_CONFIG } from '../devetreme.utils';
import { WysiwygConstants } from './wysiwyg.constants';
import type { WysiwygData, WysiwygMention } from './wysiwyg.interfaces';

@Component({
  selector: 'app-wysiwyg',
  templateUrl: './wysiwyg.component.html',
  styleUrls: ['./wysiwyg.component.scss'],
  imports: [IgxDialogComponent, DxHtmlEditorModule, DxoToolbarModule, DxiItemModule, DxoMediaResizingModule]
})
export class WysiwygComponent extends BnextCoreComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  loaderService = inject(BnextLoaderActivationService);
  sanitizer = inject(DomSanitizer);
  noticeService = inject(NoticeService);

  static LANG_CONFIG = DEVEXTREME_LANG_CONFIG;

  readonly okLabel = input('ok');

  readonly editorEight = input('10rem');

  readonly mentionDataSource = input<WysiwygMention<any>[]>([]);

  readonly contentEditable = input(false);

  readonly contextMenuEnabled = input(true);

  readonly allowResizing = input(true);

  readonly maxCharacters = input(4000); // <-- Coloar `null` o valores menores a 0 para deshabilitar la validación
  readonly hasDomTarget = input(true);

  readonly editor = viewChild<DxHtmlEditorComponent>('editor');

  readonly editorRef = viewChild('editor', { read: ElementRef });

  readonly dialog = viewChild<IgxDialogComponent>('dialog');

  public readonly valueResult = output<any>();

  private _currentData: WysiwygData;
  private _mentionMap: DataMap<WysiwygMention<any>> = {};

  get html(): SafeHtml {
    return this._currentData?.html || null;
  }

  get titleLabel(): string {
    return this._currentData?.dialogName || 'titleLabel';
  }

  public ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.mentionDataSource && !equalsObject(changes.mentionDataSource.previousValue, changes.mentionDataSource.currentValue)) {
      this.refreshMentionMap();
    }
  }

  public override ngOnInit(): void {
    super.ngOnInit();
  }

  public override ngOnDestroy(): void {
    super.ngOnDestroy();
    const editorRef = this.editorRef();
    if (editorRef) {
      editorRef.nativeElement.removeEventListener('keypress', this.onKeyPressEvent);
    }
    const editor = this.editor();
    if (editor) {
      editor.instance.element().removeEventListener('paste', this.onPasteEvent);
      editor.instance.element().removeEventListener('drop', this.onDropEvent);
    }
  }

  public ngAfterViewInit(): void {
    super.ngAfterViewInit();
    const editorRef = this.editorRef();
    if (editorRef) {
      editorRef.nativeElement.addEventListener('keypress', this.onKeyPressEvent());
    }
    const editor = this.editor();
    if (editor) {
      editor.instance.element().querySelector('.dx-htmleditor-content').addEventListener('paste', this.onPasteEvent());

      editor.instance.element().querySelector('.dx-htmleditor-content').addEventListener('drop', this.onDropEvent());
    }
  }

  private refreshMentionMap(): void {
    for (const mention of this.mentionDataSource()) {
      this._mentionMap[this.mentionId(mention)] = mention;
    }
  }

  private getMentionValue(id: string): WysiwygMention<any> {
    if (!this._mentionMap) {
      return null;
    }
    return this._mentionMap[id] || null;
  }

  mentionBgColor(id: string): string {
    const mention = this.getMentionValue(id);
    if (mention) {
      return mention.bgColor;
    }
    return null;
  }

  mentionColor(id: string): string {
    const mention = this.getMentionValue(id);
    if (mention) {
      return mention.color;
    }
    return null;
  }

  mentionId(item: WysiwygMention<any>): string {
    if (!item) {
      return null;
    }
    if (item.type) {
      return `${item.type}_${item.value}`;
    }
    if (item.value) {
      return String(item.value);
    }
    return null;
  }

  load(data: WysiwygData): void {
    this._currentData = data;
    this.openShadowDom();
    this.setValue(data.text || '', data.html || '');
  }

  open(data: WysiwygData): void {
    this._currentData = data;
    const editor = this.editor();
    if (editor) {
      editor.value = this.html;
    }
    if (this.hasDomTarget()) {
      this.openShadowDom();
    }
    this.dialog().open();
    this.setFocus();
  }

  close(): void {
    this.loaderService.show();
    const content = this.getContent();
    let newHtml = '';
    for (let index = 0; index < content.children.length; index++) {
      newHtml += content.children[index].outerHTML; // <-- se usa el OUTERHTML para evtar copiar `WysiwygConstants.STYLE_LIST`
    }
    if (newHtml === '<p><br></p>') {
      newHtml = '';
    }
    if (this.contentEditable()) {
      newHtml = `<div contenteditable>${newHtml}</div>`;
    }
    newHtml += `<style>${WysiwygConstants.STYLE_LIST}</style>`;
    const editorRef = this.editorRef();
    const text = editorRef.nativeElement.innerText === '\n' ? '' : editorRef.nativeElement.innerText.trim();
    this.setValue(text, newHtml);
    this.dialog().close();
    this.loaderService.hide();
  }

  private setValue(text: string, html: any): void {
    if (this.hasDomTarget()) {
      this._currentData.text = text;
      if (typeof this._currentData.shadowRoot !== 'undefined') {
        this._currentData.html = this._currentData.shadowRoot.innerHTML = this.sanitizer.sanitize(SecurityContext.HTML, this.sanitizer.bypassSecurityTrustHtml(html));
      }
    }
    this.valueResult.emit({ text: text, html: html });
  }

  private setFocus(): void {
    setTimeout(() => {
      const p: any = document.querySelector('.ql-editor.dx-htmleditor-content[contenteditable="true"]');
      p.tabIndex = 1;
      p.focus();
      if (p.hasChildNodes()) {
        // if the element is not empty
        const s = window.getSelection();
        const r = document.createRange();
        const e = p.childElementCount > 0 ? p.lastChild : p;
        r.setStart(e, 1);
        r.setEnd(e, 1);
        s.removeAllRanges();
        s.addRange(r);
      }
    }, 300);
  }

  private getDomNode(): ElementRef {
    if (typeof this._currentData.domNode === 'function') {
      return this._currentData.domNode();
    }
    return this._currentData.domNode;
  }

  private openShadowDom(): void {
    const elem: ElementRef = this.getDomNode();
    if (elem) {
      DomUtil.addClass(elem, 'wysiwyg-html-content');
    }
    if (this._currentData.shadowRoot) {
      return;
    }
    try {
      this._currentData.shadowRoot = elem.nativeElement.attachShadow({ mode: 'open' });
    } catch (e) {
      console.log('fixWysiwygCss', elem, e.stack);
    }
  }

  private onPasteEvent(): EventListener {
    return (event) => {
      const editor = this.editor();
      if (this.isMaxLengthExceeded(editor.instance.getLength())) {
        this.noticeService.notice('root.common.message.maxCharacterReached');
        const cutedText = editor.instance.getText(0, this.maxCharacters());
        editor.value = '';
        editor.instance.insertText(0, cutedText, null);
      } else {
        console.log('onPasteEvent', event);
      }
    };
  }

  private onDropEvent(): EventListener {
    return (e) => e.preventDefault();
  }

  private onKeyPressEvent(): EventListener {
    return (e) => {
      if (this.isMaxLengthExceeded(this.editorRef().nativeElement.innerText.length)) {
        e.preventDefault();
        this.noticeService.notice('root.common.message.maxCharacterReached');
      }
    };
  }

  private isMaxLengthExceeded(length: number): boolean {
    const maxCharacters = this.maxCharacters();
    if (maxCharacters == null || maxCharacters < 0) {
      return false;
    }
    return length >= maxCharacters;
  }

  private getContent() {
    return this.editor().instance.element().querySelector('.dx-htmleditor-content');
  }
}
