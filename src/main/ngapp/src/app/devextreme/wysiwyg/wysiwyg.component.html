<igx-dialog #dialog [title]="titleLabel" [leftButtonLabel]="okLabel()" (leftButtonSelect)="close()">
  <div class="wysiwyg-dialog-layout">
    <div class="dx-viewport">
      <ng-content select="[leftSide]"></ng-content>
      <dx-html-editor #editor [value]="html" [style.height]="editorEight()">
        <dxi-mention [valueExpr]="mentionId" [displayExpr]="'text'" [searchExpr]="'text'" [dataSource]="mentionDataSource()" [template]="'itemTemplate'"></dxi-mention>
        <span
          *dxTemplate="let args of 'itemTemplate'"
          class="wysiwyg-mention-item"
          [style.background-color]="mentionBgColor(args.id) || 'grey'"
          [style.color]="mentionColor(args.id) || 'black'"
          [attr.data-span-id]="args.id"
          [attr.data-marker]="args.marker"
          [attr.data-mention-value]="args.value"
        >
          {{ args.value }}
        </span>
        <dxo-toolbar [multiline]="true">
          <dxi-item [name]="'undo'"></dxi-item>
          <dxi-item [name]="'redo'"></dxi-item>
          <dxi-item [name]="'separator'"></dxi-item>
          <dxi-item [name]="'size'" [acceptedValues]="['8pt', '10pt', '12pt', '14pt', '18pt', '24pt', '36pt']"></dxi-item>
          <dxi-item [name]="'font'" [items]="['Arial', 'Courier New', 'Georgia', 'Impact', 'Lucida Console', 'Tahoma', 'Times New Roman', 'Verdana']"></dxi-item>
          <dxi-item [name]="'separator'"></dxi-item>
          <dxi-item [name]="'bold'"></dxi-item>
          <dxi-item [name]="'italic'"></dxi-item>
          <dxi-item [name]="'strike'"></dxi-item>
          <dxi-item [name]="'underline'"></dxi-item>
          <dxi-item [name]="'separator'"></dxi-item>
          <dxi-item [name]="'alignLeft'"></dxi-item>
          <dxi-item [name]="'alignCenter'"></dxi-item>
          <dxi-item [name]="'alignRight'"></dxi-item>
          <dxi-item [name]="'alignJustify'"></dxi-item>
          <dxi-item [name]="'separator'"></dxi-item>
          <dxi-item [name]="'orderedList'"></dxi-item>
          <dxi-item [name]="'bulletList'"></dxi-item>
          <dxi-item [name]="'separator'"></dxi-item>
          <dxi-item [name]="'header'" [items]="[false, 1, 2, 3, 4, 5]"></dxi-item>
          <dxi-item [name]="'separator'"></dxi-item>
          <dxi-item [name]="'color'"></dxi-item>
          <dxi-item [name]="'background'"></dxi-item>
          <dxi-item [name]="'separator'"></dxi-item>
          <dxi-item [name]="'clear'"></dxi-item>
          <dxi-item [name]="'blockquote'"></dxi-item>
        </dxo-toolbar>
        <dxo-media-resizing [enabled]="true"></dxo-media-resizing>
      </dx-html-editor>
    </div>
    <ng-content select="[rightSide]"></ng-content>
  </div>
</igx-dialog>
