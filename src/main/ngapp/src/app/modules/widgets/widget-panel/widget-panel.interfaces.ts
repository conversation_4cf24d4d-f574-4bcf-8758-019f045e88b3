import type { TemplateRef } from '@angular/core';
import type { IgcDockManagerPaneType, IgcSplitPaneOrientation } from '@infragistics/igniteui-dockmanager';
import type { WidgetMode } from './widget-panel.enums';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export type IWidgetBaseComponent = {};
export interface WidgetConfig {
  slot: string;
  header: string;
  headerId: string;
  icon: string;
  path?: string;
  selected?: boolean;
  isPinned?: boolean;
  sizeWidget?: SizeWidget;
  mode?: WidgetMode;
  size?: number;
  showInSmallTouchDevice?: boolean;
  template?: TemplateRef<IWidgetBaseComponent>;
  orientation?: IgcSplitPaneOrientation;
}

export interface WidgetSplitContainer {
  type?: IgcDockManagerPaneType;
  orientation?: IgcSplitPaneOrientation;
  size?: number;
  panes: any[];
  id?: string;
}

export interface WidgetOptions {
  resizable?: boolean;
  shadow?: boolean;
  draggable?: boolean;
}

export interface WidgetCss {
  css: HTMLStyleElement;
  ready: Promise<void>;
}

export interface SizeWidget {
  width?: number;
  height?: number;
  responsiveClass?: string;
  isOnTab?: boolean;
}
export interface WidgetResize {
  widgets: readonly WidgetConfig[];
  refresh: boolean;
}
