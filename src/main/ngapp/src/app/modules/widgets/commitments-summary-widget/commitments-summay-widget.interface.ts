export class DataSourceCommitmentsSummary {
  progress: number;
  score: number;
  sumActualHoursUnplanned: number;
  plannedCount: number;
  closedCount: number;
  inProgressCount: number;
  sumPlannedHours: number;
  sumEstimatedHours: number;
  sumActualHoursPlanned: number;
  planDeviation: number;
  unplannedCount: number;
  cancelledCount: number;
  undoneCount: number;
  sumActualHoursCancelled: number;
  sumPlannedHoursCancelled: number;
  sumUnplannedHours: number;
}
