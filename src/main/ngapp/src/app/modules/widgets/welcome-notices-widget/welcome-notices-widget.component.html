<div class="welcome-container">
  <ng-container [ngTemplateOutlet]="carousel"> </ng-container>
</div>
@if (isDialogOpen) {
  <igx-dialog
    #dialog
    [closeOnEscape]="true"
    [closeOnOutsideSelect]="true"
    [leftButtonLabel]="'root.common.button.close' | translate: this"
    (leftButtonSelect)="dialog.close(); isDialogOpen = false; navigation = false"
    [title]="'root.modules.widgets.panel-widget.i18n.widgets.welcomeNoticesHeader' | translate: this"
  >
    <div class="welcome-dialog">
      <ng-container [ngTemplateOutlet]="carousel"> </ng-container>
    </div>
  </igx-dialog>
}
<ng-template #carousel>
  @if (!welcomeImages?.length) {
    <div class="message-container empty-state-container">
      <div class="icon-empty"><igx-icon family="material" class="material-icons">newspaper</igx-icon></div>
      <span>
        <p>
          <strong>{{ 'pipe.empty-list-not-today' | translate: this }}</strong>
        </p>
      </span>
    </div>
  } @else {
    <igx-carousel [navigation]="navigation" animationType="fade" [interval]="intervalImage">
      @for (item of welcomeImages; track item) {
        <igx-slide #slide [class.available-dialog]="availableDialog()" (activeChange)="slideChange(item, $event)" (click)="openDialog(item)">
          <div class="welcome-slide-container">
            @if (item.imageUrl) {
              <div class="cell full-height welcome-notice-image fancy-scroll" [title]="item.description" [style.background-image]="'url(' + item.imageUrl + ')'"></div>
            } @else {
              <div class="cell full-height welcome-notice-image fancy-scroll default-welcome-image" [title]="item.description"></div>
            }
            @if (showWelcomeMessage()) {
              <div class="description-welcome" [innerHTML]="welcomeMessage"></div>
            }
          </div>
        </igx-slide>
      }
    </igx-carousel>
  }
</ng-template>
