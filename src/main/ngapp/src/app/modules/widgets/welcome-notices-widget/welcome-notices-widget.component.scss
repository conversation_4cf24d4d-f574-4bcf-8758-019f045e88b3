//Los colores del componente se pueden configurar desde el archivo componentColors.scss en la carpeta src
@use 'src/styles/mediaQueries' as *;
@use 'src/styles/immutable-colors' as *;
@use 'sass:string';

.welcome-container {
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: string.unquote('calc(100% - 1rem)');
  igx-slide.available-dialog {
    cursor: pointer;
  }
}
.welcome-slide-container {
  display: flex;
  height: 100%;
  width: 100%;
}

.welcome-notice-image {
  word-break: break-word;
  border: 1px solid #cfcfcf;
  background-color: $bg-color;
  background-repeat: no-repeat;
  background-size: contain;
  background-position-x: center;
  background-position-y: center;
}

.welcome-dialog {
  width: string.unquote('calc(100vw - 4.25rem)');
  height: string.unquote('calc(100vh - 9.75rem)');

  ::ng-deep .igx-carousel .igx-carousel-indicators--bottom {
    z-index: 10006;

    .igx-nav-dot {
      box-shadow: none;
      width: 15px;
      height: 15px;
    }
  }
}
.description-welcome {
  position: absolute;
  top: 1rem;
  z-index: 1;
  width: 100%;
  height: -moz-fit-content;
  height: 100%;
}
.default-welcome-image {
  background-image: url('./../../../../assets/images/login/Bnext-QMS-Cover.webp');
}
:host ::ng-deep igx-carousel .igx-carousel-indicators--bottom {
  z-index: 3;

  .igx-nav-dot {
    box-shadow: none;
    width: 15px;
    height: 15px;
  }
}
