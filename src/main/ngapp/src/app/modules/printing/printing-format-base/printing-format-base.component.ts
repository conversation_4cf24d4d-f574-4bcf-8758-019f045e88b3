import { Directive, ElementRef, type OnD<PERSON>roy, type OnInit, inject, viewChild } from '@angular/core';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';

import { Form<PERSON>uilder, FormControl, type FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import type { IChangeCheckboxEventArgs } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import type { MultiSelectComponent } from 'src/app/core/multi-select/multi-select.component';
import { AppService } from 'src/app/core/services/app.service';
import { BnextLoaderActivationService } from 'src/app/core/services/bnext-loader-activation.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';

import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FormUtil } from 'src/app/core/utils/form-util';
import type { IPersistableCodeDescription } from 'src/app/core/utils/interfaces';

import { stringColors, stringToColour } from '@/core/utils/string-util';
import type { WysiwygComponent } from 'src/app/devextreme/wysiwyg/wysiwyg.component';
import type { WysiwygData } from 'src/app/devextreme/wysiwyg/wysiwyg.interfaces';
import { PrintingFormatComponent } from '../printing-format/printing-format.component';
import { FixedField, type FixedFieldValues } from '../printing-format/printing-format.enums';
import { PrintFormatVariableTypes } from './printing-format-base.enums';
import type {
  FixedFieldDTO,
  FlexiFieldDTO,
  IAnswerMetadataFieldDTO,
  PrintFormatVariable,
  PrintingFormatDTO,
  PrintingFormatDataSourceDTO,
  PrintingFormatFieldCode
} from './printing-format-base.interfaces';
import { PrintingFormatBaseUtils } from './printing-format-base.utils';

@Directive()
export abstract class PrintingFormatBaseComponent extends BnextCoreComponent implements OnInit, OnDestroy, FabButton, i18n {
  noticeService = inject(NoticeService);
  api = inject(AppService);

  formBuilder = inject(FormBuilder);

  loader = inject(BnextLoaderActivationService);
  activatedRoute = inject(ActivatedRoute);

  // Se utiliza mismo lenguaje que `PrintingFormatComponent` //

  private _fixedFieldsData: IPersistableCodeDescription[] = [];
  private _flexiFieldsData: FlexiFieldDTO[] = [];
  private _fixedFieldsValues: (string | number)[] = []; // <-- Guardar los ids en el ENUM de PrintingFormatFixedField
  private _flexiFieldsValues: (string | number)[] = []; // <-- Guardar los nombres de las columnas
  private _ready = false;
  private _flexiFieldsDataStages: string[] = [];
  private _title = null;
  private _isFormatValid = true;
  private _fieldsMap: DataMap<IAnswerMetadataFieldDTO | FlexiFieldDTO> = {};
  private _wysiwygVariables: PrintFormatVariable[] = [];

  override get customTagName(): string {
    return PrintingFormatComponent.LANG_CONFIG.componentName;
  }
  override get componentPath(): string {
    return PrintingFormatComponent.LANG_CONFIG.componentPath;
  }
  get isFormatValid(): boolean {
    return this._isFormatValid;
  }
  get title(): string {
    return this._title;
  }
  get flexiFieldsDataStages(): string[] {
    return this._flexiFieldsDataStages;
  }
  get wysiwygVariables(): PrintFormatVariable[] {
    return this._wysiwygVariables;
  }
  get showGenerateCode() {
    return false;
  }
  get controllerPrintingFormatBaseModule(): string {
    return `printing-format/base/${this.moduleName}`;
  }
  get fixedFieldsData(): IPersistableCodeDescription[] {
    return this._fixedFieldsData;
  }
  get flexiFieldsData(): FlexiFieldDTO[] {
    return this._flexiFieldsData;
  }
  get ready(): boolean {
    // Se sobre escribe en `edit` para validar el `id`
    return this._ready;
  }

  flexiFieldsDataFilterStage: string = null;
  format: WysiwygData;
  masterId: string = null;
  fieldsKey = 'code';
  showHelp = false;
  busy = true;
  mainForm: FormGroup<{
    code: FormControl<string>;
    description: FormControl<string>;
    details: FormControl<string>;
    fixedFields: FormControl<string[]>;
    flexiFields: FormControl<string[]>;
    generateCode: FormControl<boolean>;
  }>;

  // FabButton
  fabFloat = true;
  fabOptionsAvailable?: string[]; // <-- [OPCIONAL], Oculta opciones del arreglo `fabButtons`
  fabButtons: (FabButtonMenuItem | DropdownMenuItem)[] = [
    {
      iconName: 'save',
      text: 'root.common.button.save',
      value: CommonAction.SAVE
    },
    {
      iconName: 'arrow_back',
      text: 'root.common.button.back',
      value: CommonAction.BACK
    }
  ];

  readonly wysiwyg = viewChild<WysiwygComponent>('wysiwyg');

  readonly formatRef = viewChild('formatRef', { read: ElementRef });

  readonly gridFlexiFields = viewChild<MultiSelectComponent<any>>('flexiFields');

  readonly gridFixedFields = viewChild<MultiSelectComponent<any>>('fixedFields');

  abstract get moduleName(): string;

  abstract get htmlId(): number;

  abstract fillLoad(entity: PrintingFormatDTO): PrintingFormatFieldCode;

  abstract openCreate(): void;

  abstract openList(): void;

  protected init(): void {
    this.navLang
      .getRouteParam(':masterId', null, false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: ([masterId]) => {
          this.masterId = masterId;
          this.load();
        },
        error: (error) => {
          console.log(error);
          this.busy = false;
        }
      });
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.init();
  }

  protected load(id?: number): void {
    let dataSourceController = `${this.controllerPrintingFormatBaseModule}/data-source/${encodeURIComponent(this.masterId)}`;
    if (id) {
      dataSourceController = `${this.controllerPrintingFormatBaseModule}/data-source/${encodeURIComponent(this.masterId)}/${id}`;
    }
    this.api
      .get({ url: dataSourceController, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (dataSource: PrintingFormatDataSourceDTO) => {
          this._title = dataSource.title;
          this._flexiFieldsData = dataSource.flexiFields.sort((a, b) => a.order - b.order);
          this.fillStages(dataSource.flexiFields);
          this.fillFieldsMap(dataSource.flexiFields);
          if (id) {
            this.fillLoadFields(this.fillLoad(dataSource.load));
          }
          this.busy = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.log(error);
          this.busy = false;
        }
      });
  }

  private fillStages(flexiFields: FlexiFieldDTO[]): void {
    this._flexiFieldsDataStages = Array.from(new Set(flexiFields.map((field) => field.fieldStage)));
    let colors;
    for (const field of flexiFields) {
      colors = stringColors(field.fieldStage);
      field.bgColor = colors.bgColor;
      field.color = colors.color;
    }
  }

  private fillFieldsMap(fields: (IAnswerMetadataFieldDTO | FlexiFieldDTO)[]): void {
    for (const field of fields) {
      this._fieldsMap[field[this.fieldsKey]] = field;
    }
  }
  private fieldSurveyAnswerMetadataId(fieldCode: string | number): number | FixedFieldValues {
    return this._fieldsMap[fieldCode]?.id || null;
  }
  private fieldCode(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.code || `[CODE: ${fieldCode}]`;
  }
  private fieldDescription(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.description || `[DESC: ${fieldCode}]`;
  }
  private fieldBgColor(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.bgColor || null;
  }
  private fieldColor(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.color || null;
  }

  override langReady(): void {
    // form
    this.mainForm = this.formBuilder.group({
      code: new FormControl(),
      description: new FormControl(null, Validators.required),
      details: new FormControl(null, Validators.required),
      fixedFields: new FormControl(null, Validators.required),
      generateCode: new FormControl(true),
      flexiFields: new FormControl(null, Validators.required)
    });
    // buttons
    for (const item of this.fabButtons) {
      if (item.text.startsWith('root.')) {
        item.text = this.tag(item.text);
      } else {
        item.text = this.tag(`fixedFields.${item.text}`);
      }
    }
    // campos fijos
    let colors;
    let description;
    let enumValue;
    this._fixedFieldsData = Object.keys(FixedField).map((enumName) => {
      description = this.tag(`fixedFields.${enumName}`);
      colors = stringColors(description);
      enumValue = +FixedField[enumName];
      return {
        id: enumValue,
        code: PrintingFormatBaseUtils.getFixedFieldAlias(enumValue),
        bgColor: colors.bgColor,
        color: colors.color,
        description: description
      };
    });
    this.fillFieldsMap(this._fixedFieldsData);
    // wysiwyg
    this.format = {
      domNode: () => this.formatRef(),
      dialogName: this.format?.dialogName || this.tag('title')
    };
    // ready
    this.busy = false;
    this._ready = true;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  stageColor(stage: string): string {
    return stringToColour(stage);
  }

  filterByStage(newStage?: string): void {
    if (typeof newStage === 'string') {
      this.flexiFieldsDataFilterStage = newStage;
    } else {
      this.flexiFieldsDataFilterStage = null;
    }
  }

  fabButtonAction(item: FabButtonMenuItem | DropdownMenuItem): void {
    switch (item.value) {
      case CommonAction.SAVE:
        this.save();
        break;
      case CommonAction.BACK:
        this.return();
        break;
      default:
        console.warn(`Action is not supported ${item.value}`);
        break;
    }
  }

  protected save(): void {
    this._isFormatValid = (this.wysiwyg().editor().value || '').trim().length > 0;
    const isFormValid = FormUtil.isValid(this.mainForm).status;
    if (!isFormValid || !this.isFormatValid) {
      let msg;
      if (!this.isFormatValid && !isFormValid) {
        msg = `${this.tag('html-format-is-required')}, ${this.tag('root.common.message.required-notice').toLowerCase()}`;
      } else if (!this.isFormatValid) {
        msg = this.tag('html-format-is-required');
      } else {
        msg = this.tag('root.common.message.required-notice');
      }
      this.noticeService.notice(msg);
      this.cdr.detectChanges();
      return;
    }
    const entity: PrintingFormatDTO = this.getEntityData();
    this.busy = true;
    this.loader.show();
    this.api.post({ url: `${this.controllerPrintingFormatBaseModule}/save`, cancelableReq: this.$destroy, postBody: entity, handleFailure: false }).subscribe({
      next: (result) => {
        if (this.debug()) {
          console.log('>> save, result: ', result);
        }
        this.busy = false;
        this.loader.hide();
        this.dialogService.info(this.tag('save-success'), 'root.common.button.control', 'root.common.button.create', 'root.common.error.messageSystem').then(
          () => this.openList(),
          () => this.openCreate()
        );
        this.menuService.refreshLoggedUser();
        this.cdr.detectChanges();
        this.loader.hide();
      },
      error: (error) => {
        this.busy = false;
        this.loader.hide();
        ErrorHandling.notifyError(error, this.navLang);
        this.cdr.detectChanges();
      }
    });
  }

  public openWysiwyg() {
    this._isFormatValid = true;
    this.wysiwyg().open(this.format);
  }

  private getParsedFixedFields(fullDto = false): FixedFieldDTO[] {
    if (!this._fixedFieldsValues || this._fixedFieldsValues.length === 0) {
      return null;
    }
    let enumValue: FixedFieldValues;
    const fixedFields = this._fixedFieldsValues.map((code) => {
      enumValue = this.fieldSurveyAnswerMetadataId(code) as FixedFieldValues;
      if (fullDto) {
        return {
          id: enumValue,
          code: code as string,
          description: this.fieldDescription(code),
          bgColor: this.fieldBgColor(code),
          color: this.fieldColor(code),
          surveyAnswerMetadataId: null,
          enumValue: enumValue
        };
      }
      return {
        id: this.fieldSurveyAnswerMetadataId(code),
        code: code as string,
        enumValue: enumValue
      };
    });
    return fixedFields;
  }

  private getParsedSurveyFields(fullDto = false): FlexiFieldDTO[] {
    if (!this._flexiFieldsValues || this._flexiFieldsValues.length === 0) {
      return null;
    }
    const flexiFields = this._flexiFieldsValues.map((value) => {
      if (fullDto) {
        return {
          id: this.fieldSurveyAnswerMetadataId(value), // <--- surveyAnswerMetadataId
          code: this.fieldCode(value),
          description: this.fieldDescription(value),
          bgColor: this.fieldBgColor(value),
          color: this.fieldColor(value),
          surveyAnswerMetadataId: this.fieldSurveyAnswerMetadataId(value),
          enumValue: null
        };
      }
      return {
        id: this.fieldSurveyAnswerMetadataId(value), // <--- surveyAnswerMetadataId
        code: this.fieldCode(value),
        surveyAnswerMetadataId: this.fieldSurveyAnswerMetadataId(value)
      };
    });
    return flexiFields;
  }

  protected refreshWysiwygVariables(): void {
    const fixedFields = this.getParsedFixedFields(true);
    const flexiFields = this.getParsedSurveyFields(true);
    this._wysiwygVariables = [];
    if (fixedFields) {
      this._wysiwygVariables.push(
        ...fixedFields.map((field) => ({
          bgColor: field.bgColor || null,
          color: field.color || null,
          text: field.description || field.code,
          type: PrintFormatVariableTypes.FIXED,
          value: field.code
        }))
      );
    }
    if (flexiFields) {
      this._wysiwygVariables.push(
        ...flexiFields.map((field) => ({
          bgColor: field.bgColor || null,
          color: field.color || null,
          text: field.description || field.code,
          type: PrintFormatVariableTypes.FLEXI,
          value: field.code
        }))
      );
    }
  }

  public onChangeSurveyFieldsValues(value: (string | number)[]): void {
    this._flexiFieldsValues = value;
    this.refreshWysiwygVariables();
    this.cdr.detectChanges();
  }
  public onChangeFixedFieldsValues(value: (string | number)[]): void {
    this._fixedFieldsValues = value;
    this.refreshWysiwygVariables();
    this.cdr.detectChanges();
  }
  protected fillLoadFields(fieldCodes: PrintingFormatFieldCode): void {
    if (!fieldCodes) {
      return;
    }
    this._flexiFieldsValues = fieldCodes.flexiFields;
    this._fixedFieldsValues = fieldCodes.fixedFields;
    this.refreshWysiwygVariables();
  }
  protected getEntityData(): PrintingFormatDTO {
    const data = this.mainForm.value;
    const entity: PrintingFormatDTO = {
      id: -1,
      code: data.code,
      description: data.description,
      fileId: this.htmlId,
      html: this.format.html,
      masterId: this.masterId,
      details: data.details,
      fixedFields: this.getParsedFixedFields() || [],
      flexiFields: this.getParsedSurveyFields() || []
    };
    return entity;
  }

  changeGenerateCode(event: IChangeCheckboxEventArgs): void {
    if (event.checked) {
      this.mainForm.controls.code.disable();
    } else {
      this.mainForm.controls.code.enable();
    }
  }

  return(): void {
    this.navLang.back();
  }
}
