export enum PrintingFormatAction {
  ADD_PRINTING_FORMAT = 'ADD_PRINTING_FORMAT',
  BACK_PRINTING_FORMAT = 'BACK_PRINTING_FORMAT'
}

export type FixedFieldType =
  | 'REQUESTOR'
  | 'STAGE'
  | 'PROGRESS'
  | 'CODE'
  | 'STATUS_PROGRESS'
  | 'STATUS'
  | 'FILLING_DATE'
  | 'BUSINESS_UNIT'
  | 'BUSINESS_UNIT_DEPARTMENT'
  | 'AREA'
  | 'LAST_MODIFIED_DATE'
  | 'CREATED_DATE'
  | 'REQUEST'
  | 'OUTSTANDING_SURVEYS'
  | 'AREA_custom_field1'
  | 'AREA_custom_field2'
  | 'AREA_custom_field3'
  | 'AREA_custom_field4'
  | 'AREA_custom_field5'
  | 'AREA_custom_field6'
  | 'AREA_custom_field7'
  | 'AREA_custom_field8'
  | 'AREA_custom_field9'
  | 'AREA_custom_field10'
  | 'AREA_custom_field11'
  | 'AREA_custom_field12'
  | 'AREA_custom_field13'
  | 'AREA_custom_field14'
  | 'AREA_custom_field15'
  | 'AREA_custom_field16'
  | 'AREA_custom_field17'
  | 'AREA_custom_field18'
  | 'AREA_custom_field19'
  | 'AREA_custom_field20'
  | 'AREA_custom_field21'
  | 'AREA_custom_field22'
  | 'AREA_custom_field23'
  | 'AREA_custom_field24'
  | 'AREA_custom_field25'
  | 'AREA_custom_field26';

export type FixedFieldValues =
  | 1 // Solicitante
  | 2 // Etapa
  | 3 // Avance
  | 4 // Clave
  | 5 // Estado + Avance
  | 6 // Estado
  | 7 // Fecha de llenado
  | 8
  | 9
  | 10
  | 11
  | 12
  | 13
  | 14;

export const FixedField: Record<FixedFieldType, number> = {
  REQUESTOR: 1, // Solicitante
  STAGE: 2, // Etapa
  PROGRESS: 3, // Avance
  CODE: 4, // Clave
  STATUS_PROGRESS: 5, // Estado + Avance
  STATUS: 6, // Estado
  FILLING_DATE: 7, // Fecha de llenado
  BUSINESS_UNIT: 8,
  BUSINESS_UNIT_DEPARTMENT: 9,
  AREA: 10,
  LAST_MODIFIED_DATE: 11,
  CREATED_DATE: 12,
  REQUEST: 13,
  OUTSTANDING_SURVEYS: 14,
  AREA_custom_field1: 15,
  AREA_custom_field2: 16,
  AREA_custom_field3: 17,
  AREA_custom_field4: 18,
  AREA_custom_field5: 19,
  AREA_custom_field6: 20,
  AREA_custom_field7: 21,
  AREA_custom_field8: 22,
  AREA_custom_field9: 23,
  AREA_custom_field10: 24,
  AREA_custom_field11: 25,
  AREA_custom_field12: 26,
  AREA_custom_field13: 27,
  AREA_custom_field14: 28,
  AREA_custom_field15: 29,
  AREA_custom_field16: 30,
  AREA_custom_field17: 31,
  AREA_custom_field18: 32,
  AREA_custom_field19: 33,
  AREA_custom_field20: 34,
  AREA_custom_field21: 35,
  AREA_custom_field22: 36,
  AREA_custom_field23: 37,
  AREA_custom_field24: 38,
  AREA_custom_field25: 39,
  AREA_custom_field26: 40
} as const;
