import * as GridUtil from '@/core/grid/utils/grid-util';
import { DatePipe } from '@angular/common';
import { Component, type OnInit, inject, viewChild } from '@angular/core';
import { type CellType, IgxButtonDirective, IgxIconComponent, IgxLinearProgressBarComponent, IgxRippleDirective, type RowType } from '@infragistics/igniteui-angular';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import { GridComponent } from 'src/app/core/grid/grid.component';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import { type ActionStrip, IconColumn, TimestampColumn } from 'src/app/core/grid/utils/grid.interfaces';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';

import { Session } from 'src/app/core/local-storage/session';
import { AppService } from 'src/app/core/services/app.service';

import { NoticeService } from 'src/app/core/services/notice.service';
import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';

import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';

@Component({
  selector: 'app-planner-task-delivery-type-list',
  templateUrl: './planner-task-delivery-type-list.component.html',
  styleUrls: ['./planner-task-delivery-type-list.component.scss'],
  imports: [IgxLinearProgressBarComponent, GridComponent, IgxButtonDirective, IgxRippleDirective, IgxIconComponent, BnextTranslatePipe]
})
export class PlannerTaskDeliveryTypeListComponent extends BnextCoreComponent implements OnInit, i18n {
  datePipe = inject(DatePipe);
  service = inject(AppService);

  noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.settings.planner-settings',
    componentName: 'planner-task-delivery-type-list'
  };
  get customTagName(): string {
    return PlannerTaskDeliveryTypeListComponent.LANG_CONFIG.componentName;
  }
  override get componentPath(): string {
    return 'modules.settings.planner-settings';
  }

  override get tagName(): string {
    return 'planner-task-delivery-type-list';
  }

  busy = false;
  title = '';
  id = 'planner-task-delivery-type-list';
  name = 'planner-task-delivery-type-list';
  url = 'timesheet/taskDeliveryType/list';
  columns: GridColumn[];

  readonly grid = viewChild<GridComponent>('grid');

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.getData(row?.data) : []),
      rowIdentifierKey: 'entity_code'
    }
  };

  getData(row: any): DropdownMenuItem[] {
    return [
      {
        text: 'Editar',
        value: CommonAction.EDIT,
        modernHref: `settings/planner-settings/planner_task_delivery_type/${row.entity_id}`,
        iconName: 'edit'
      }
    ];
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.perPage = Session.getGridSize();
    this.columns = [];
    // Se construyen columnas
    GridUtil.columns(this.columns)
      .subs(this.subs)
      .push(
        'entity_status',
        new IconColumn({
          width: '100px',
          render: { callback: (cell) => this.statusCallback(cell), isDefaultIconStatus: true }
        })
      )
      .push('entity_code', '150px')
      .push('entity_description', '250px')
      .push('entity_taskDeliveryTypeInfo', '500px')
      .push('businessUnitDepartmentName', '200px')
      .push(
        'createdDate',
        new TimestampColumn({
          hidden: true,
          width: '150px'
        })
      )
      .push('lastModifiedDate', new TimestampColumn({ width: '150px' }))
      .langAsync(this.translateService, PlannerTaskDeliveryTypeListComponent.LANG_CONFIG, 'i18n');
    this.cdr.detectChanges();
  }

  statusCallback(cell: CellType) {
    this.dialogService.confirm(this.tag('status-dialog-message')).then(() => {
      this.service.get({ cancelableReq: this.$destroy, url: `timesheet/taskDeliveryType/toggle-status/${cell.row.data.entity_id}`, handleFailure: false }).subscribe(
        () => {
          this.grid().refresh();
          this.noticeService.notice(this.tag('accept-dialog-message'));
        },
        (error) => {
          console.error(error);
          ErrorHandling.notifyError(error, this.navLang);
        }
      );
    });
  }

  override langReady() {
    this.title = this.tag('title');
    console.log('title:', this.title);
    this.cdr.detectChanges();
  }

  toogleMenu(drop: GridDropDownItem) {
    this.noticeService.notice(drop.row.entity_code);
    switch (drop.item.value) {
      case CommonAction.EDIT:
        this.menuService.navigate(`menu/settings/planner-settings/planner_task_delivery_type/${drop.row.entity_id}`, Module.CATALOGS);
        break;
      case CommonAction.DELETE:
        // ToDo: Borrar proyectos
        break;
    }
  }

  navigateUrl() {
    this.menuService.navigate('menu/settings/planner-settings/planner_task_delivery_type/add');
  }
}
