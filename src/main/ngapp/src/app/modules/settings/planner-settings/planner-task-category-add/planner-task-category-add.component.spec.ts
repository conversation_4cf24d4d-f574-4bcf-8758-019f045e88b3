import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';

import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { PlannerTaskCategoryAddComponent } from './planner-task-category-add.component';

describe('PlannerTaskCategoryAddComponent', () => {
  let component: PlannerTaskCategoryAddComponent;
  let fixture: ComponentFixture<PlannerTaskCategoryAddComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, PlannerTaskCategoryAddComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PlannerTaskCategoryAddComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
