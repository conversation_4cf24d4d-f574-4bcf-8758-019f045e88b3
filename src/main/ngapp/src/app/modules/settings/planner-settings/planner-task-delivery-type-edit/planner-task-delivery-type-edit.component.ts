import { ComboComponent } from '@/core/combo/combo.component';
import { AutoresizeDirective } from '@/core/directives/autoresize';
import { DropdownSearchComponent } from '@/core/dropdown-search/dropdown-search.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { NgClass } from '@angular/common';
import { type AfterViewInit, Component, type OnDestroy, type OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  IgxButtonDirective,
  IgxCheckboxComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxRippleDirective
} from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs/operators';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { PlannerTaskDeliveryTypeAddComponent } from '../planner-task-delivery-type-add/planner-task-delivery-type-add.component';
import type { TaskDeliveryTypeEntity } from '../planner-task-delivery-type-add/planner-task-delivery-type.interface';

@Component({
  selector: 'app-planner-task-delivery-type-edit',
  templateUrl: '../planner-task-delivery-type-add/planner-task-delivery-type-add.component.html',
  styleUrls: ['../planner-task-delivery-type-add/planner-task-delivery-type-add.component.scss'],
  imports: [
    FormsModule,
    NgClass,
    ReactiveFormsModule,
    IgxLinearProgressBarComponent,
    IgxInputGroupComponent,
    IgxInputDirective,
    IgxLabelDirective,
    IgxCheckboxComponent,
    ComboComponent,
    DropdownSearchComponent,
    AutoresizeDirective,
    IgxButtonDirective,
    IgxRippleDirective,
    BnextTranslatePipe
  ]
})
export class PlannerTaskDeliveryTypeEditComponent extends PlannerTaskDeliveryTypeAddComponent implements OnInit, OnDestroy, AfterViewInit {
  activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.settings.planner-settings',
    componentName: 'planner-task-category-add'
  };

  private _loaded = false;

  /** Ajustes al lenguaje para utilizar las etiquetas de "PlannerClientAddComponent" **/
  get customTagName(): string {
    return PlannerTaskDeliveryTypeAddComponent.LANG_CONFIG.componentName;
  }
  override get componentPath(): string {
    return PlannerTaskDeliveryTypeAddComponent.LANG_CONFIG.componentPath;
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
  }

  override langReady() {
    super.langReady();
  }

  catalogReady() {
    if (this._loaded) {
      return;
    }
    this._loaded = true;
    this.navLang
      .getRouteParam(':id', null, false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([id]) => {
        if (id === null) {
          return;
        }
        this.id = +id;
        this.load(+id);
      });
  }

  private load(id: number) {
    this.busy = true;
    this.service.get({ cancelableReq: this.$destroy, url: `timesheet/taskDeliveryType/load/${id}` }).subscribe(
      (data: TaskDeliveryTypeEntity) => {
        let businessUnitDepartmentIds = data.businessUnitDepartmentIds;
        if (+data.businessUnitDepartments?.length > 0 && typeof data.businessUnitDepartments[0] === 'object') {
          businessUnitDepartmentIds = data.businessUnitDepartments.map((b) => b.id);
        }
        this.mainForm.patchValue({
          code: data.code,
          generateCode: false,
          description: data.description,
          help: data.help || '',
          businessUnitDepartmentIds: businessUnitDepartmentIds,
          taskDeliveryTypeInfo: data.taskDeliveryTypeInfo
        });
        this.initializeDepartmentProcesses(data.departmentProcessId || null); // <-- parche para llenar el combo aún y cuando aún no hay "options"
        this.cdr.detectChanges();
        this.busy = false;
      },
      () => {
        this.busy = false;
      }
    );
  }

  protected override getEntityData(): TaskDeliveryTypeEntity {
    const data = super.getEntityData();
    data.id = this.id;
    return data;
  }
}
