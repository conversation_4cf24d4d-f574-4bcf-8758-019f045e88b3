<div class="grid-container grid-floating-active">
  <div class="grid-x">
    <div class="cell">
      <form [formGroup]="mainForm" class="container-form grid-floating-action-buttons">
        <div class="header grid-container">
          <div class="grid-x">
            <h3 class="cell igx-card-header__title">{{ titleTag | translate: this }}</h3>
          </div>
        </div>
        <div class="grid-container">
          <div class="grid-x grid-padding-x">
            <div class="cell large-6">
              <igx-input-group type="line">
                <input igxInput formControlName="title" type="text" />
                <label igxLabel for="fullName1">{{ 'root.common.field.title' | translate: this }}</label>
              </igx-input-group>
            </div>
            <div class="cell large-6">
              <igx-input-group type="line">
                <input igxInput formControlName="description" type="text" />
                <label igxLabel for="fullName1">{{ 'root.common.field.description' | translate: this }}</label>
              </igx-input-group>
            </div>
            <div class="cell small-12">
              <div class="grid-multi-select-container">
                <button [igxButton]="'outlined'" type="button" class="inner-icon round" igxRipple (click)="businessUnits.openDialog()">
                  <igx-icon family="material">add</igx-icon>
                  <label>{{ 'root.common.field.businessUnit' | translate: this }}</label>
                </button>
                <app-grid-multi-select
                  #businessUnits
                  id="businessUnits"
                  name="businessUnits"
                  [dataColumns]="businessUnitsColumns"
                  [dataUrl]="'access/data-source/business-unit/list'"
                  [dialogTitle]="'root.common.field.businessUnit' | translate: this"
                  [hasDelete]="true"
                  [inputType]="4"
                  [label]="businessUnits.getValueGridCount() + ' ' + ('root.common.field.businessUnit' | translate: this) + '(s)'"
                  [primaryKey]="'id'"
                  [valueColumns]="businessUnitsColumns"
                  (rowSelectionChange)="onBusinessUnitSelect($event)"
                >
                </app-grid-multi-select>
              </div>
            </div>
            <div class="cell small-12">&nbsp;</div>
            <div class="cell small-12 large-12 button-section">
              <div class="button-sample">
                <button [igxButton]="'contained'" igxRipple (click)="save()">{{ 'root.common.button.save' | translate: this }}</button>
              </div>
              <div class="button-sample">
                <button [igxButton]="'outlined'" igxRipple (click)="cancel()">{{ 'root.common.button.cancel' | translate: this }}</button>
              </div>
              <div class="button-sample">
                <button [igxButton]="'outlined'" igxRipple (click)="clear()">{{ 'root.common.button.clear' | translate: this }}</button>
              </div>
            </div>
            <div class="cell small-12">&nbsp;</div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
