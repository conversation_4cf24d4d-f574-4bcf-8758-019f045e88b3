import { GridMultiSelectComponent } from '@/core/grid-multi-select/grid-multi-select.component';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { type AfterViewInit, Component, inject, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { IgxButtonDirective, IgxIconComponent, IgxInputDirective, IgxInputGroupComponent, IgxLabelDirective, IgxRippleDirective } from '@infragistics/igniteui-angular';
import { DocumentRepositoryAddComponent } from '../document-repository-add/document-repository-add.component';
import type { IRepository } from '../document-repository.interfaces';
@Component({
  selector: 'app-document-repository-edit',
  templateUrl: './../document-repository-add/document-repository-add.component.html',
  styleUrls: ['./document-repository-edit.component.scss'],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    IgxInputGroupComponent,
    IgxInputDirective,
    IgxLabelDirective,
    IgxButtonDirective,
    IgxRippleDirective,
    IgxIconComponent,
    GridMultiSelectComponent,
    BnextTranslatePipe
  ]
})
export class DocumentRepositoryEditComponent extends DocumentRepositoryAddComponent implements AfterViewInit {
  activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.settings.document-settings',
    componentName: 'document-repository'
  };

  public get customTagName(): string {
    return DocumentRepositoryAddComponent.LANG_CONFIG.componentName;
  }

  id: number;

  public get componentPath(): string {
    return DocumentRepositoryAddComponent.LANG_CONFIG.componentPath;
  }

  get titleTag(): string {
    return 'document-repository-edit-title';
  }

  readonly businessUnits = viewChild<GridMultiSelectComponent>('businessUnits');

  override ngAfterViewInit(): void {
    this.navLang.getRouteParam('document-repository/:id', null, false, this.activatedRoute).subscribe(([id]) => {
      if (id === null) {
        console.error('Null Id');
        return;
      }
      this.id = +id;
      this.service.get({ url: `document/getDocumentRepositoryById/${+id}`, cancelableReq: this.$destroy }).subscribe((response: IRepository) => {
        if (response) {
          this.mainForm.patchValue({
            title: response.code,
            description: response.description
          });
          this.businessUnits().value = response.businessUnits;
          super.setBusinessUnits(response.businessUnits);
          this.cdr.detectChanges();
        }
      });
    });
  }

  protected override getEntityData(): IRepository {
    const data = super.getEntityData();
    data.id = this.id;
    return data;
  }
}
