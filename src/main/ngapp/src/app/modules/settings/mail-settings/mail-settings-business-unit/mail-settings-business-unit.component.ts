import { Component, type OnInit, inject } from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { Session } from 'src/app/core/local-storage/session';
import { MailSettingsUtils } from './../mail-settings-util';

import { BnextCoreComponent } from 'src/app/core/bnext-core.component';

import { GridComponent } from '@/core/grid/grid.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { ActivatedRoute } from '@angular/router';
import { AppService } from 'src/app/core/services/app.service';

import { IgxTabContentComponent, IgxTabHeaderComponent, IgxTabHeaderLabelDirective, IgxTabItemComponent, IgxTabsComponent } from '@infragistics/igniteui-angular';

@Component({
  selector: 'app-mail-settings-business-unit',
  templateUrl: './mail-settings-business-unit.component.html',
  styleUrls: ['./mail-settings-business-unit.component.scss'],
  imports: [IgxTabsComponent, IgxTabItemComponent, IgxTabHeaderComponent, IgxTabHeaderLabelDirective, IgxTabContentComponent, GridComponent, BnextTranslatePipe]
})
export class MailSettingsBusinessUnitComponent extends BnextCoreComponent implements OnInit {
  private api = inject(AppService);

  activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.settings.mail-settings',
    componentName: 'mail-settings-business-unit'
  };
  LangConfig = MailSettingsBusinessUnitComponent.LANG_CONFIG;

  typesName: string = null;
  typesDataUrl: string = null;
  typeUpdateUrl: string = null;
  schedulesName: string = null;
  schedulesDataUrl: string = null;
  scheduleUpdateUrl: string = null;
  typesColumns: GridColumn[] = [];
  selectedTabIndex = 0;
  schedulesColumns: GridColumn[] = [];
  titleLabel: string;
  title = '';
  id: number = null;

  constructor() {
    super();

    this.perPage = Session.getGridSize();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.typesColumns = MailSettingsUtils.typeColumns(this.$destroy, this.translateService);
    this.schedulesColumns = MailSettingsUtils.scheduleColumns(this.$destroy, this.translateService);
    this.loader.hide();
    this.navLang
      .getRouteParams(':id', ['id'], true, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: ([id]) => {
          if (id) {
            this.typesDataUrl = `business-units/mail-settings/types/${id}`;
            this.typeUpdateUrl = `business-units/mail-settings/types/update/${id}`;
            this.schedulesDataUrl = `business-units/mail-settings/schedules/${id}`;
            this.scheduleUpdateUrl = `business-units/mail-settings/schedules/update/${id}`;
            this.typesName = `mail-settings-business-unit-type-${id}`;
            this.schedulesName = `mail-settings-business-unit-schedule-${id}`;
            this.id = +id;
            this.loadName(+id);
            this.cdr.detectChanges();
          } else {
            this.id = null;
            console.error('Undefined id');
          }
        },
        error: () => {
          this.id = null;
          console.error('Undefined id');
        }
      });
    this.cdr.detectChanges();
  }

  private loadName(id: number) {
    this.api.get({ cancelableReq: this.$destroy, url: `business-units/name/${id}` }).subscribe(
      (response: any) => {
        this.subs.push(
          this.translateService.getFrom(MailSettingsBusinessUnitComponent.LANG_CONFIG, 'title').subscribe((tag) => (this.title = `${tag} ${response.name}`))
        );
      },
      () => {}
    );
  }

  onSelectedIndexChange() {
    this.cdr.detectChanges();
  }
}
