<div class="grid-container grid-left-panel grid-floating-active">
  <div class="grid-x">
    <div class="cell">
      <form [formGroup]="mainForm" class="container-form grid-floating-action-buttons">
        <igx-linear-bar class="overlayer" type="success" [class.visibility-hidden]="!busy" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
        <div class="header grid-container">
          <div class="grid-x">
            <h3 class="cell igx-card-header__title">{{ 'formTitle' | translate: this }}</h3>
          </div>
        </div>
        <div class="grid-container">
          <div class="grid-x grid-padding-x">
            <div class="cell small-12 medium-6">
              <igx-input-group [ngClass]="displayDensityClass" [hidden]="mainForm.value.generateCode" theme="material">
                <input #code igxInput name="code" formControlName="code" type="text" [required]="!mainForm.value.generateCode" maxlength="255" />
                <label igxLabel for="code">{{ 'root.common.field.code' | translate: this }}</label>
              </igx-input-group>
              <igx-input-group [ngClass]="displayDensityClass" [hidden]="!mainForm.value.generateCode" theme="material">
                <input #autoCode igxInput name="autoCode" type="text" [hidden]="!mainForm.value.generateCode" readonly [value]="'UGROUP-####'" />
                <label igxLabel for="autoCode">{{ 'root.common.field.code' | translate: this }}</label>
              </igx-input-group>
              <igx-checkbox
                [checked]="showGenerateCode"
                [hidden]="!showGenerateCode"
                name="generateCode"
                formControlName="generateCode"
                (change)="changeGenerateCode($event)"
              >
                {{ 'root.common.field.generate-code' | translate: this }}
              </igx-checkbox>
            </div>
            <div class="cell small-12 medium-6">
              <igx-input-group [ngClass]="displayDensityClass" theme="material">
                <input igxInput name="description" formControlName="description" type="text" [required]="true" maxlength="255" />
                <label igxLabel for="description">{{ 'root.common.field.name' | translate: this }}</label>
              </igx-input-group>
            </div>
            <div class="cell small-12">&nbsp;</div>
            <div class="cell small-12">
              @if (hasSaveAccess && saveButtonAvailable) {
                <button
                  [style.color]="mainForm.invalid ? '#eff1f2' : null"
                  [igxRipple]="mainForm.invalid ? 'red' : 'white'"
                  [igxButton]="'contained'"
                  type="button"
                  (click)="save($event)"
                >
                  {{ 'root.common.button.save' | translate: this }}
                </button>
              }
              <button [igxButton]="hasSaveAccess && saveButtonAvailable ? 'flat' : 'contained'" type="button" igxRipple (click)="return()">
                {{ 'root.common.button.cancel' | translate: this }}
              </button>
              @if (id === null) {
                <button [igxButton]="'flat'" type="button" igxRipple (click)="resetForm()">{{ 'root.common.button.clear' | translate: this }}</button>
              }
            </div>
            <div class="cell small-12">&nbsp;</div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
