.button-group {
  display: flex;
  flex: 1 0 30%;
  margin-top: 0.875rem;
  margin-bottom: 3rem;
  padding-left: 0.313rem;
  padding-right: 0.313rem;
}

.button-group button {
  width: 100%;
}

::ng-deep .igx-icon-button--contained.igx-icon-button--disabled,
::ng-deep .igx-button--contained.igx-button--disabled {
  color: #383838 !important;
}

.header {
  padding-left: 0.938rem;
}

.card-header h3.igx-card-header__title {
  display: flex;
}

.module-container ::ng-deep .igx-card igx-card-content {
  padding-top: 0px;
}

::ng-deep .igx-combo__checkbox {
  display: none;
}

.module-configurations {
  display: flow-root;
}
.module-container {
  float: left;
}
.module-container:nth-child(odd) {
  float: left;
}
.module-container:nth-child(even) {
  float: right;
}
