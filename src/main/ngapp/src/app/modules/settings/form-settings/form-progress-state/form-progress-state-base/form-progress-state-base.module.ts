import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HammerModule } from '@angular/platform-browser';
import { IgxCheckboxModule, IgxDividerModule, IgxIconModule, IgxInputGroupModule, IgxProgressBarModule } from '@infragistics/igniteui-angular';
import { BnextTranslateModule } from 'src/app/core/i18n/bnext-translate.module';

@NgModule({
  imports: [
    CommonModule,
    BnextTranslateModule,
    HammerModule,
    FormsModule,
    ReactiveFormsModule,
    IgxInputGroupModule,
    IgxDividerModule,
    IgxIconModule,
    IgxCheckboxModule,
    IgxProgressBarModule
  ],
  exports: [
    CommonModule,
    BnextTranslateModule,
    HammerModule,
    FormsModule,
    ReactiveFormsModule,
    IgxInputGroupModule,
    IgxDividerModule,
    IgxIconModule,
    IgxCheckboxModule,
    IgxProgressBarModule
  ]
})
export class FormProgressStateBaseModule {}
