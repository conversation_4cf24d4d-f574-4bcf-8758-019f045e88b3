import { type ComponentFixture, TestBed } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { ActivityTypesAddComponent } from './activity-types-add.component';

import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { UserSelectComponent } from 'src/app/core/user-select/user-select.component';

describe('ActvityTypesAddComponent', () => {
  let component: ActivityTypesAddComponent;
  let fixture: ComponentFixture<ActivityTypesAddComponent>;

  configureTestSuite();

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, UserSelectComponent, ActivityTypesAddComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivityTypesAddComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
