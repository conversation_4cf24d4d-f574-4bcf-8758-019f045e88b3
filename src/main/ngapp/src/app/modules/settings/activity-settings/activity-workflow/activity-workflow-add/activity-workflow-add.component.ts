import { Component, type OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { MultiSelectComponent } from '@/core/multi-select/multi-select.component';
import { NgClass } from '@angular/common';
import {
  IgxCheckboxComponent,
  IgxDividerDirective,
  IgxHintDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxPrefixDirective
} from '@infragistics/igniteui-angular';
import { ActivityWorkflowBaseComponent } from '../activity-workflow-base/activity-workflow-base.component';

@Component({
  selector: 'app-activity-workflow-add',
  templateUrl: './../activity-workflow-base/activity-workflow-base.component.html',
  styleUrls: ['./../activity-workflow-base/activity-workflow-base.component.scss'],
  imports: [
    NgClass,
    IgxLinearProgressBarComponent,
    FormsModule,
    ReactiveFormsModule,
    IgxInputGroupComponent,
    IgxInputDirective,
    IgxLabelDirective,
    IgxCheckboxComponent,
    IgxPrefixDirective,
    IgxIconComponent,
    IgxHintDirective,
    IgxDividerDirective,
    MultiSelectComponent,
    BnextTranslatePipe
  ]
})
export class ActivityWorkflowAddComponent extends ActivityWorkflowBaseComponent implements OnInit {
  get showGenerateCode() {
    return true;
  }
  override langReady(): void {
    this.busy = false;
  }
}
