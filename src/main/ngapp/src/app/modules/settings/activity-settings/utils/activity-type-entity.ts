import type { ActivityTypeLinked } from './activity-type-linked';

export interface SystemLink {
  id?: number;
  url: string;
  regexp: string;
  status?: number;
  label?: string;
}

export enum ActivityTypeNumberAttr {
  anticipationAttendDays = 'anticipationAttendDays',
  authorDeleteRecurrence = 'authorDeleteRecurrence',
  authorEditApply = 'authorEditApply',
  authorEditDynamicFields = 'authorEditDynamicFields',
  authorEditImplementation = 'authorEditImplementation',
  authorEditImplementer = 'authorEditImplementer',
  authorEditBusinessUnitDepartment = 'authorEditBusinessUnitDepartment',
  authorEditRecurrence = 'authorEditRecurrence',
  authorEditVerification = 'authorEditVerification',
  authorEditVerifier = 'authorEditVerifier',
  defaultActivityObjectiveId = 'defaultActivityObjectiveId',
  defaultActivitySourceId = 'defaultActivitySourceId',
  defaultDaysToVerify = 'defaultDaysToVerify',
  defaultPriorityId = 'defaultPriorityId',
  defaultVerifierAuthor = 'defaultVerifierAuthor',
  enablePeriodicity = 'enablePeriodicity',
  implementerEditApply = 'implementerEditApply',
  implementerEditDynamicFields = 'implementerEditDynamicFields',
  implementerEditBusinessUnitDeparment = 'implementerEditBusinessUnitDeparment',
  preassignedUserEditClientPlannerTask = 'preassignedUserEditClientPlannerTask',
  restrictAnticipationAttend = 'restrictAnticipationAttend',
  scopeVerifier = 'scopeVerifier',
  scopeImplementer = 'scopeImplementer',
  showBusinessUnitDepartment = 'showBusinessUnitDepartment',
  showCode = 'showCode',
  showDescription = 'showDescription',
  showFillForm = 'showFillForm',
  showFillType = 'showFillType',
  showGroupName = 'showGroupName',
  showObjective = 'showObjective',
  showPriority = 'showPriority',
  showSource = 'showSource',
  showType = 'showType',
  showVerifierUser = 'showVerifierUser',
  verifierEditApply = 'verifierEditApply',
  defaultCustomVerifier = 'defaultCustomVerifier',
  verifierEditDynamicFields = 'verifierEditDynamicFields',
  verifierEditBusinessUnitDepartment = 'verifierEditBusinessUnitDepartment'
}

export enum ActivityTypeFlagAttr {
  showDaysToVerify = 'showDaysToVerify',
  showImplementation = 'showImplementation',
  showImplementationPeriodicity = 'showImplementationPeriodicity',
  showImplementerUser = 'showImplementerUser',
  showStartDate = 'showStartDate',
  showVerificactionPeriodicity = 'showVerificactionPeriodicity',
  showVerification = 'showVerification'
}

export enum ActivityTypeBooleanAttr {
  addDeliverySetUp = 'addDeliverySetUp',
  addImplementationOnCreate = 'addImplementationOnCreate',
  addSubtaskByDepartment = 'addSubtaskByDepartment',
  addVerificationAvailable = 'addVerificationAvailable',
  addVerificationOnCreate = 'addVerificationOnCreate',
  allowsPreAssignment = 'allowsPreAssignment',
  verificationReqOnPlanning = 'verificationReqOnPlanning',
  defaultIsPlanned = 'defaultIsPlanned',
  defaultPlannedHours = 'defaultPlannedHours',
  followUpImplementationDelay = 'followUpImplementationDelay',
  mustUpdateImplementationAtReturn = 'mustUpdateImplementationAtReturn',
  projectsAvailable = 'projectsAvailable',
  showIsPlanned = 'showIsPlanned',
  showPlannedHours = 'showPlannedHours',
  workingHoursAvailable = 'workingHoursAvailable',
  allowsUseOnPlanning = 'allowsUseOnPlanning',
  showActivityOrder = 'showActivityOrder'
}

export type ActivityTypeNumber = Record<ActivityTypeNumberAttr, number>;

export type ActivityTypeFlag = Record<ActivityTypeFlagAttr, 1 | 0>;

export type ActivityTypeBoolean = Record<ActivityTypeBooleanAttr, boolean>;

export interface ActivityTypeFlags extends ActivityTypeNumber, ActivityTypeFlag, ActivityTypeBoolean {}
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ActivityTypeOptionalFlags extends Partial<ActivityTypeFlags> {}

export interface ActivityTypeEntity extends ActivityTypeFlags {
  code: string;
  customImplementationDate: 1 | 2; // [1 = SPECIFIC_DATE] [SAME_DAY = 2]
  defaultImplementerAuthor: 0 | 1; // [1 = El creador] [0 = Sin responsable]
  description: string;
  fillTypes: string;
  id?: number;
  linked: ActivityTypeLinked;
  maxOpenTime: number;
  maxOpenTimeUnit: string;
  module: string;
  notifyImplementTime: number;
  notifyImplementTimeUnit: string;
  dynamicFieldViewName: string;
  notifyVerifyTime: number;
  notifyVerifyTimeUnit: string;
  showDynamicFields: string;
  systemLinks: SystemLink[];
  verificationType: 1 | 2; // (SPECIFIC_DATE = 1), (SAME_DAY_WITH_TOLERARANCE = 2);
  registryCode: string;
  defaultLoggedUserDepartment?: boolean;
  mustUpdateImplementationAtReturn: boolean;
  defaultResolutionId?: number;
  isCopy: boolean;
}

export interface ActivityTypeSaveAsCopy {
  id: number;
  saveAsCopyName: string;
}
