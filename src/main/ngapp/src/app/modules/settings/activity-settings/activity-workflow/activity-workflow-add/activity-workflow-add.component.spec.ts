import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';

import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ActivityWorkflowAddComponent } from './activity-workflow-add.component';

describe('ActivityWorkflowAddComponent ', () => {
  let component: ActivityWorkflowAddComponent;
  let fixture: ComponentFixture<ActivityWorkflowAddComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, ActivityWorkflowAddComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivityWorkflowAddComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
