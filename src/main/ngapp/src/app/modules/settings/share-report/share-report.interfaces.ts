import type { PeriodicityEntity } from 'src/app/core/periodicity/utils/periodicity-entity';
import type { Persistable } from 'src/app/core/utils/interfaces';
import type { TextHasValue, TextLongValue } from 'src/app/core/utils/text-has-value';
import type { BusinessUnitDepartmentEntity } from 'src/app/shared/activities/core/activities-core.interfaces';

export type ShareReportLinkedField = 'columns' | 'businessUnits' | 'departments' | 'users' | 'userTeams' | 'processes';
export interface ShareReportLinked {
  columns?: Persistable[];
  businessUnits?: Persistable[];
  departments?: Persistable[];
  users?: Persistable[];
  userTeams?: Persistable[];
  processes?: Persistable[];
}

export enum ShareReportAction {
  OPEN_REPORT = 'OPEN_REPORT',
  OPEN_QUERY = 'OPEN_QUERY',
  OPEN_CONNECTION = 'OPEN_CONNECTION'
}

export interface ReportValue extends TextLongValue {
  details: string;
  deleted: number;
}

export class ShareReportEntity {
  id?: number;
  status?: number;
  code?: string;
  maxRecords?: number;
  description?: string;
  recipients?: string;
  commitmentDate?: Date;
  plainRecipients?: string;
  details?: string;
  queryConditions?: string;
  columns?: Persistable[];
  businessUnits?: Persistable[];
  departments?: Persistable[];
  users?: Persistable[];
  userTeams?: Persistable[];
  processes?: Persistable[];
  linked?: ShareReportLinked;
  periodicity?: PeriodicityEntity;
  reportId?: number;
}

export class ShareReportDataSource {
  columns: TextHasValue[];
  reports: ReportValue[];
  businessUnits: TextHasValue[];
  users: TextHasValue[];
  userTeams: TextLongValue[];
  processes: TextHasValue[];
  departments: BusinessUnitDepartmentEntity[];
}
