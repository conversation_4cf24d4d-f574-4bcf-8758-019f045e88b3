import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';

import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ShareReportAddComponent } from './share-report-add.component';

describe('ShareReportAddComponent', () => {
  let component: ShareReportAddComponent;
  let fixture: ComponentFixture<ShareReportAddComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, ShareReportAddComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ShareReportAddComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
