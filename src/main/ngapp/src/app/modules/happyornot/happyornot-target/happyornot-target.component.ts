import type { i18n } from '@/core/bnext-core.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import type { LinedAreaItem } from '@/core/lined-textarea/lined-area.interfaces';
import { LoaderComponent } from '@/core/loader/loader.component';
import { RestApiModule } from '@/core/rest-api.module';
import { SelectComponent } from '@/core/select/select.component';
import * as DateUtil from '@/core/utils/date-util';
import * as DomUtil from '@/core/utils/dom-util';
import type { ITextHasValue } from '@/core/utils/text-has-value';
import { DatePipe, NgClass } from '@angular/common';
import { type AfterViewInit, Component, type ElementRef, type OnDestroy, type OnInit, inject, output, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { DomSanitizer, type SafeHtml } from '@angular/platform-browser';
import {
  IgxButtonDirective,
  IgxDatePickerComponent,
  IgxDialogComponent,
  IgxDialogTitleDirective,
  IgxForOfDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxListComponent,
  IgxListItemComponent,
  IgxPrefixDirective,
  IgxSuffixDirective,
  IgxTabContentComponent,
  IgxTabHeaderComponent,
  IgxTabHeaderIconDirective,
  IgxTabItemComponent,
  IgxTabsComponent,
  IgxTimePickerComponent
} from '@infragistics/igniteui-angular';
import { NgxKjuaComponent } from 'ngx-kjua';
import { BnextCoreComponent } from 'src/app/core/bnext-core.component';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { LinedAreaComponent } from 'src/app/core/lined-textarea/lined-area.component';
import { Session } from 'src/app/core/local-storage/session';
import { AppService } from 'src/app/core/services/app.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';
import { Deferred } from 'src/app/core/utils/deferred';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FeatureFlag, isFeatureAvailable } from 'src/app/core/utils/feature-util';
import { FormUtil } from 'src/app/core/utils/form-util';
import type { HappyornotSurveyEntity, HappyornotSurveyTarget } from './../happyornot-add/happyornot-add.interfaces';
import { HappyornotTargetUtils } from './happyornot-target-utils';
import { HappyornotPublishTarget } from './happyornot-target.enums';
import type { HappyPublishAlertMail, HappyPublishTarget, HappyornotLink, HappyornotSurveySaved, HappyornotTarget } from './happyornot-target.interfaces';

@Component({
  selector: 'app-happyornot-target',
  templateUrl: './happyornot-target.component.html',
  styleUrls: ['./happyornot-target.component.scss'],
  imports: [
    IgxDialogComponent,
    FormsModule,
    ReactiveFormsModule,
    NgClass,
    IgxTabsComponent,
    IgxTabItemComponent,
    IgxTabHeaderComponent,
    IgxIconComponent,
    IgxTabHeaderIconDirective,
    IgxTabContentComponent,
    SelectComponent,
    IgxButtonDirective,
    IgxListComponent,
    IgxForOfDirective,
    IgxListItemComponent,
    IgxSuffixDirective,
    IgxInputGroupComponent,
    IgxPrefixDirective,
    IgxInputDirective,
    IgxLabelDirective,
    NgxKjuaComponent,
    LinedAreaComponent,
    IgxDialogTitleDirective,
    IgxDatePickerComponent,
    IgxTimePickerComponent,
    LoaderComponent,
    BnextTranslatePipe
  ]
})
export class HappyornotTargetComponent extends BnextCoreComponent implements OnInit, AfterViewInit, OnDestroy, i18n {
  sanitizer = inject(DomSanitizer);
  datePipe = inject(DatePipe);
  noticeService = inject(NoticeService);

  service = inject(AppService);

  formBuilder = inject(UntypedFormBuilder);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.happyornot',
    componentName: 'happyornot-target'
  };

  override get componentPath(): string {
    return 'modules.happyornot';
  }

  override get tagName(): string {
    return 'happyornot-target';
  }

  html: SafeHtml;
  url = '';
  canvas = 'canvas';
  imageAsCode = false;
  imageText = '';
  imgNativeElement = undefined;
  mode = 'image';
  base64Img = '';
  crisp = false;
  private targetId = 0;
  private targetName = '';
  rowIndex = 1;
  loading = false;
  urlApp = '';
  linkDataHidden: HappyornotLink;

  publishTargetForm: UntypedFormGroup;
  sharedFormConfig: UntypedFormGroup;

  EnumPublishTarget = HappyornotPublishTarget;

  publishTargetNames: HappyPublishTarget[] = [
    {
      publishTarget: HappyornotPublishTarget.ANONYMOUS,
      targetName: '',
      status: 1,
      deleted: 0
    }
  ];
  publishTargetMails: HappyPublishAlertMail[] = [
    {
      mail: Session.getUserMail(),
      deleted: 0
    }
  ];
  tempPublishTargetMailsIndex: any[] = [];
  publishArrayIndex: {
    ANONYMOUS: number;
    USER: number;
  } = {
    ANONYMOUS: 1,
    USER: 0
  };
  busy = true;
  confirmMix = true;
  data: HappyornotSurveyEntity | HappyornotSurveyTarget = null;
  happyornotUserSurveyAvaliable = isFeatureAvailable(FeatureFlag.HAPPYORNOT_USER_SURVEY);

  publishTarget = HappyornotPublishTarget.ANONYMOUS;
  publishTargets: ITextHasValue<string>[] = [
    {
      text: HappyornotPublishTarget.ANONYMOUS,
      value: HappyornotPublishTarget.ANONYMOUS
    },
    {
      text: HappyornotPublishTarget.USER,
      value: HappyornotPublishTarget.USER
    }
  ];

  disabled = false;

  get id(): number {
    return this.data?.id || null;
  }

  public readonly saved = output<number>();

  public readonly published = output<HappyornotSurveySaved>();

  readonly publishDialog = viewChild<IgxDialogComponent>('publishDialog');

  readonly QRDialog = viewChild<IgxDialogComponent>('QRDialog');

  readonly buffer = viewChild<ElementRef>('buffer');

  readonly checkbox = viewChild<ElementRef>('checkbox');

  readonly imgToDownload = viewChild<ElementRef>('imgToDownload');

  readonly list = viewChild<IgxListComponent>('list');

  readonly addTargetsDialog = viewChild<IgxDialogComponent>('addTargetsDialog');

  readonly linedTextArea = viewChild<LinedAreaComponent>('linedTextarea');

  readonly shareDialogConfig = viewChild<IgxDialogComponent>('shareDialogConfig');

  override ngOnInit(): void {
    super.ngOnInit();
    this.setPublishTargetValues();
    this.service.get({ cancelableReq: this.$destroy, url: 'happyornot/setting/url/' }).subscribe((res) => {
      if (typeof res !== 'string') {
        return;
      }
      this.urlApp = res;
      if (this.urlApp.substring(this.urlApp.length - 1) !== '/') {
        this.urlApp += '/';
      }
    });
    this.initilizeFormsLinks();
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
  }

  private initilizeFormsLinks() {
    this.sharedFormConfig = this.formBuilder.group({
      url: new UntypedFormControl('', Validators.required),
      viewsPerLink: new UntypedFormControl(),
      expireDate: new UntypedFormControl(null),
      tagName: new UntypedFormControl(''),
      expireTime: new UntypedFormControl(null)
    });
  }

  setPublishTargetValues() {
    const dataMap: DataMap = {};
    for (let index = 0; index < this.publishTargetNames.length; index++) {
      const target = this.publishTargetNames[index];
      if (target.deleted === 0) {
        let name;
        if (target.id) {
          name = `targetName${target.id}`;
        } else {
          name = `targetNameTemp${index}`;
        }
        dataMap[name] = new UntypedFormControl(target.targetName, [Validators.required]);
      }
    }
    for (let index = 0; index < this.publishTargetMails.length; index++) {
      const mail = this.publishTargetMails[index];
      let name;
      if (mail.id) {
        name = `alert${mail.id}`;
      } else {
        name = `alertTemp${index}`;
      }
      dataMap[name] = new UntypedFormControl(mail.mail);
    }
    this.publishTargetForm = new UntypedFormGroup(dataMap);
  }

  openEdit(happyornotSurveyId: number, statusSurvey?: boolean): void {
    this.disabled = statusSurvey ? statusSurvey : false;
    this.loading = true;
    this.service.get({ cancelableReq: this.$destroy, url: `happyornot/targets/${happyornotSurveyId}`, handleFailure: false }).subscribe(
      (data: HappyornotTarget) => {
        this.data = {
          id: happyornotSurveyId,
          targetNames: [],
          targetMails: []
        } as HappyornotSurveyTarget;
        this.publishTargetNames = data.targetNames.map((e: HappyPublishTarget) => {
          return {
            id: e.id,
            targetName: e.targetName,
            status: +e.status,
            publishTarget: e.publishTarget,
            userId: e.userId || null,
            isDeleteAvailable: false,
            token: e.token || '',
            url: e.url || '',
            deleted: e.deleted
          };
        });
        this.publishTargetMails = data.targetMails.map((e: HappyPublishAlertMail) => {
          return {
            mail: e.mail,
            id: e.id,
            userId: e.userId || null,
            deleted: 0
          };
        });
        this.setPublishTargetValues();
        this.busy = false;
        this.cdr.detectChanges();
        this.publishDialog().open();
        this.loading = false;
      },
      (e) => {
        ErrorHandling.notifyError(e, this.navLang);
        this.busy = false;
      }
    );
  }

  openNew(data: HappyornotSurveyEntity): void {
    this.data = data;
    this.publishDialog().open();
  }

  publish(): void {
    if (this.publishDialog().isOpen) {
      this.tempPublishTargetMailsIndex = [];
      const data: DataMap = this.data;
      data.publishTarget = this.publishTarget || null;
      data.targetMails = [];
      data.targetNames = [];
      for (const value of this.publishTargetMails) {
        data.targetMails.push({
          mail: value.mail,
          userId: value.userId || null,
          id: value.id || -1,
          deleted: value.deleted || 0
        });
      }
      for (const value of this.publishTargetNames) {
        data.targetNames.push({
          type: value.publishTarget,
          description: value.targetName,
          userId: value.userId || null,
          status: value.status,
          id: value.id || -1,
          happyornotLinks: this.generateSurveyLink(value),
          deleted: value.deleted || 0
        });
      }
      if (this.publishTargetMails.length > 0 || this.publishTargetNames.length > 0) {
        this.save(data as HappyornotSurveyEntity, true);
      } else {
        this.noticeService.notice(this.translate.instant('empty-target-advices'));
      }
    } else {
      console.error('Publish dialog must be opened');
    }
  }

  //Genera un link base si la ubicación no cuenta con uno.
  private generateSurveyLink(value: any): HappyornotLink[] {
    if ((typeof value.id === 'undefined' || value.id === -1) && typeof value.happyornotLinks === 'undefined') {
      return new Array({ status: 1, deleted: false, id: -1, viewsPerLink: null });
    }
    return value.happyornotLinks;
  }

  save(data: HappyornotSurveyEntity | HappyornotSurveyTarget, publish = false): Promise<boolean> {
    const def = new Deferred<boolean>();
    if (!this.publishDialog().isOpen && publish) {
      console.error('Publish configuration must be setted from dialog');
      setTimeout(() => {
        def.resolve(false);
      }, 1);
      return def.promise;
    }
    if (publish && !FormUtil.isValid(this.publishTargetForm).status) {
      this.dialogService.info(this.tag('root.common.error.fill-required'));
      setTimeout(() => {
        def.resolve(false);
      }, 1);
      return def.promise;
    }
    const controller = publish ? 'happyornot/save-publish' : 'happyornot/save';
    this.loader.show();
    this.busy = true;
    this.service.post({ url: controller, cancelableReq: this.$destroy, postBody: data, options: null, handleFailure: false }).subscribe(
      (result: HappyornotSurveySaved) => {
        console.log('saved: ', result);
        this.loader.hide();
        this.busy = false;
        if (publish) {
          this.publishDialog().close();
          this.published.emit(result);
        }
        this.saved.emit(result.happyornotTargetId);
        this.cdr.detectChanges();
      },
      (e) => {
        this.loader.hide();
        this.busy = false;
        def.reject(e);
        ErrorHandling.notifyError(e, this.navLang);
      },
      () => {
        this.loader.hide();
        this.busy = false;
        def.resolve(true);
        if (publish) {
          this.noticeService.notice(this.tag('save-publish-success'));
        } else {
          this.noticeService.notice(this.tag('save-success'));
        }
      }
    );
    return def.promise;
  }

  changeSurveyMode(_target): void {
    // ToDo: Mostrar / Ocultar funcionalidad para agregar usuarios
  }

  toggleStatus(happyornotTarget: HappyPublishTarget): void {
    if (happyornotTarget.status === 1) {
      happyornotTarget.status = 0;
    } else {
      happyornotTarget.status = 1;
    }
  }

  itemLink(happyornotTargetId: number): string {
    if (this.urlApp.length > 0 && this.urlApp !== '' && !this.urlApp.includes('localhost')) {
      return `${this.urlApp}view/v-happyornot-survey.view?id=${happyornotTargetId}`;
    }
    if (this.urlApp.includes('localhost')) {
      console.warn(`CURRENT URL: ${this.urlApp}`);
    }
    return `${RestApiModule.app()}view/v-happyornot-survey.view?id=${happyornotTargetId}`;
  }

  updateValue(field: any, itemIdx: number, array: any[], itemName: string) {
    array[itemIdx][itemName] = field.value;
  }

  remove(itemIdx: number, array: any[], itemName: string): void {
    if (array.filter((f) => f.deleted === 0).length < 2) {
      this.noticeService.notice(this.tag('minimum-one'));
      return;
    }
    this.publishTargetForm.removeControl(itemName);
    if (itemName.indexOf('alert') !== -1) {
      this.tempPublishTargetMailsIndex.push(itemIdx);
      array[itemIdx].deleted = 1;
    } else {
      array.splice(itemIdx, 1);
    }
  }

  addAlert(): void {
    this.publishTargetMails.push({
      mail: null,
      deleted: 0
    });
    this.publishTargetForm.addControl(`alertTemp${this.publishTargetMails.length - 1}`, new UntypedFormControl());
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 300);
  }

  addPublishTarget(target: HappyornotPublishTarget): void {
    if (this.confirmMix && this.publishTargetNames.length && this.publishTargetNames[0].publishTarget !== target) {
      this.confirmMix = false;
      this.dialogService.confirm(this.tag('confirm-restart-targets')).then(() => {
        this.addPublishTargetAction(target);
      });
    } else {
      this.showDialogTargets();
    }
  }

  copy(text: string): void {
    DomUtil.copyToClipboard(text);
    this.noticeService.notice(this.tag('copied'));
  }

  refreshPublishArrayIdx(): void {
    this.publishArrayIndex.ANONYMOUS =
      this.publishTargetNames.findIndex((item) => {
        return item.publishTarget === HappyornotPublishTarget.ANONYMOUS;
      }) || 0;
    this.publishArrayIndex.USER =
      this.publishTargetNames.findIndex((item) => {
        return item.publishTarget === HappyornotPublishTarget.USER;
      }) || 0;
  }

  private addPublishTargetAction(target: HappyornotPublishTarget, targetName?: string): void {
    const item: HappyPublishTarget = {
      publishTarget: target,
      targetName: targetName || null,
      isDeleteAvailable: true,
      status: 1
    };
    switch (target) {
      case HappyornotPublishTarget.ANONYMOUS:
        this.publishTargetNames.splice(this.publishArrayIndex.ANONYMOUS, 0, item);
        break;
      case HappyornotPublishTarget.USER:
        this.publishTargetNames.splice(this.publishArrayIndex.USER + this.publishArrayIndex.ANONYMOUS, 0, item);
        break;
    }
    this.publishArrayIndex[target]++;
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 300);
  }

  override langReady(): void {
    for (const item of this.publishTargets) {
      item.text = this.tag(item.text);
    }
  }

  close() {
    for (const index of this.tempPublishTargetMailsIndex) {
      this.publishTargetMails[index].deleted = 0;
    }
    this.tempPublishTargetMailsIndex = [];
    this.publishDialog().close();
  }

  public statusLabel(target: HappyPublishTarget): string {
    return target.status === 1 ? 'status_active' : 'status_inactive';
  }

  public openDialogQR(text: string, targetId: number, targetName: string) {
    this.targetId = targetId;
    this.loadQrCode(targetId).then((base64) => {
      this.targetName = targetName;
      this.buffer().nativeElement.src = base64 ? base64 : HappyornotPublishTarget.DEFAULT_B64;
      this.url = text;
      this.QRDialog().open();
    });
  }

  public uploadImage(e) {
    if (e.target.files.length > 0) {
      const reader = new FileReader();
      reader.readAsDataURL(e.target.files[0]);
      reader.onload = (data: any) => {
        this.base64Img = data.target.result;
        this.updatePreviewQr();
      };
      reader.onloadend = () => {
        this.checkbox().nativeElement.click();
        this.updateQrImage();
      };
    }
  }

  private updatePreviewQr() {
    const buffer = this.buffer();
    buffer.nativeElement.src = this.base64Img;
    this.imgNativeElement = buffer.nativeElement;
  }

  public updateQrImage() {
    if (this.base64Img.length > 0) {
      const data = {
        id: this.targetId,
        qrBase64: this.base64Img
      };

      this.service.post({ url: 'happyornot/updateBase64', cancelableReq: this.$destroy, postBody: data, options: null, handleFailure: false }).subscribe(
        () => {
          this.noticeService.notice('Actualizado');
          this.buffer().nativeElement.src = this.base64Img;
        },
        (e) => {
          this.busy = false;
          ErrorHandling.notifyError(e, this.navLang);
        }
      );
    } else {
      this.noticeService.notice('No se puedar guardar sin subir una imagen');
    }
  }

  public async getRef() {
    const qr_src = document.querySelector('#QrCode').children[0].children[0].getAttribute('src');
    const image = await fetch(qr_src);
    const imageBlog = await image.blob();
    const imageURL = URL.createObjectURL(imageBlog);
    const imgToDownload = this.imgToDownload();
    imgToDownload.nativeElement.href = imageURL;
    imgToDownload.nativeElement.download = this.targetName;
    imgToDownload.nativeElement.click();
  }

  public loadQrCode(targetId: number): Promise<string> {
    return HappyornotTargetUtils.loadQrCode(this.service, targetId, this.$destroy);
  }

  public onDialogQrOpened(): void {
    this.imgNativeElement = this.buffer().nativeElement;
    this.cdr.detectChanges();
  }

  public showDialogTargets() {
    this.addTargetsDialog().open();
  }

  markAsTouched(item: any, controlName: string) {
    const conttrol = this.publishTargetForm.controls[controlName];
    conttrol?.setValue(item.value);
    conttrol?.setValidators([Validators.required]);
    conttrol?.markAsTouched();
    conttrol?.updateValueAndValidity();
  }

  openShare(happyornotTargetId: number) {
    this.service.get({ cancelableReq: this.$destroy, url: `happyornot/getSurveyLink/${happyornotTargetId}` }).subscribe((link: HappyornotLink) => {
      this.sharedFormConfig.patchValue({
        url: link.url,
        viewsPerLink: link.viewsPerLink || null,
        expireDate: link.expireDate == null ? null : DateUtil.safe(link.expireDate),
        expireTime: DateUtil.safe(link.expireTime) || null,
        tagName: link.urlName || null
      });
      this.linkDataHidden = {
        code: link.code,
        token: link.token,
        deleted: link.deleted,
        status: link.status,
        viewCounter: link.viewCounter,
        id: link.id
      };
      this.targetId = happyornotTargetId;
      this.cdr.detectChanges();
    });
    this.shareDialogConfig().open();
  }

  openShareDialogConfig() {
    this.shareDialogConfig().open();
  }

  closeShareDialogConfig() {
    this.shareDialogConfig().close();
    this.sharedFormConfig.reset();
  }

  applyShareDialogConfig() {
    const configLink: HappyornotLink = {
      url: this.sharedFormConfig.get('url').value,
      viewsPerLink: this.sharedFormConfig.get('viewsPerLink').value,
      expireDate: this.sharedFormConfig.get('expireDate').value,
      expireTime: this.sharedFormConfig.get('expireTime').value,
      urlName: this.sharedFormConfig.get('tagName').value,
      code: this.linkDataHidden.code,
      deleted: this.linkDataHidden.deleted,
      status: this.linkDataHidden.status,
      token: this.linkDataHidden.token,
      viewCounter: this.linkDataHidden.viewCounter,
      id: this.linkDataHidden.id
    };
    this.service.post({ url: 'happyornot/saveSurveyLink', cancelableReq: this.$destroy, postBody: configLink }).subscribe((_response: HappyornotLink) => {
      if (+_response === 1) {
        this.noticeService.notice(this.translate.instant('link-updated'), false, 2);
        this.shareDialogConfig().close();
      }
    });
  }

  public onRowChanged(data) {
    this.publishTargetNames[data.index].targetName = data.text;
    this.setPublishTargetValues();
  }

  public onAddRow(data: LinedAreaItem): void {
    this.publishTargetNames.push({
      publishTarget: HappyornotPublishTarget.ANONYMOUS,
      targetName: data.targetName,
      status: data.status,
      deleted: 0,
      isDeleteAvailable: data.isDeleteAvailable,
      happyornotLinks: new Array({ status: 1, deleted: false, id: -1, viewsPerLink: null })
    });
    this.cdr.detectChanges();
  }

  public onRemoveRow(data): void {
    this.publishTargetNames[data.index].deleted = 1;
    this.publishTargetForm.removeControl(data.value ? `targetName${data.value}` : `targetNameTemp${data.index}`);
  }
}
