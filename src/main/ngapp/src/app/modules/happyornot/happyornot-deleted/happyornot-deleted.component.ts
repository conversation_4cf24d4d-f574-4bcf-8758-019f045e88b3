import * as GridUtil from '@/core/grid/utils/grid-util';
import { Component, type OnDestroy, type OnInit } from '@angular/core';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { HappyornotListComponent } from '../happyornot-list/happyornot-list.component';

import { GridComponent } from '@/core/grid/grid.component';
import { IgxLinearProgressBarComponent, type RowType } from '@infragistics/igniteui-angular';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import { HappyornotListUtil } from '../happyornot-list/happyornot-list-util';
import { HappyornotTargetComponent } from '../happyornot-target/happyornot-target.component';

@Component({
  selector: 'app-happyornot-deleted',
  templateUrl: './../happyornot-list/happyornot-list.component.html',
  styleUrls: ['./../happyornot-list/happyornot-list.component.scss'],
  imports: [IgxLinearProgressBarComponent, GridComponent, HappyornotTargetComponent]
})
export class HappyornotDeletedComponent extends HappyornotListComponent implements OnInit, OnDestroy {
  private _componentPath = HappyornotListUtil.LANG_CONFIG.componentPath;

  /** Ajustes al lenguaje para utilizar las etiquetas de "HappyornotListComponent" **/
  public get customTagName(): string {
    return HappyornotListUtil.LANG_CONFIG.componentName;
  }
  public get componentPath(): string {
    return this._componentPath;
  }
  public set componentPath(value: string) {
    this._componentPath = value;
  }

  id = 'happyornot-deleted';
  name = 'happyornot-deleted';
  url = 'happyornot/paperbin';

  override ngOnInit(): void {
    super.ngOnInit();
    // Se construyen columnas
    this.columns.splice(0, 3);
    GridUtil.columns(this.columns).subs(this.subs).langAsync(this.translateService, this.LangConfig, 'i18n');
    this.title = this.tag('titleDeleted');
    this.actionStripConfig = this.setActionStripConfig();
    this.cdr.detectChanges();
  }

  setActionStripConfig() {
    return {
      dropdownAlwaysVisible: false,
      dropdownActionLimit: 6,
      render: {
        menuActionOptions: (row: RowType) => (row ? this.getData() : []),
        rowIdentifierKey: 'code',
        toggleButtonTitle: this.translateService.instant('root.common.button.REMOVE')
      }
    };
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  getOptions(): DropdownMenuItem[] {
    return [
      {
        text: 'Restaurar',
        value: CommonAction.REMOVE,
        iconName: 'restore_from_trash',
        hidden: false
      }
    ];
  }

  toogleMenu(drop: GridDropDownItem) {
    this.noticeService.notice(drop.row.entity_code);
    switch (drop.item.value) {
      case CommonAction.REMOVE:
        this.restore(drop);
        break;
    }
  }

  restore(drop: any) {
    const controller = 'happyornot/restore-question';
    this.busy = true;
    this.service.post({ url: controller, cancelableReq: this.$destroy, postBody: drop.row.entity_id, options: null, handleFailure: false }).subscribe(
      () => {
        this.busy = false;
        this.noticeService.notice(this.tag('restore-success'));
        this.grid().refresh();
      },
      (e) => {
        this.busy = false;
        ErrorHandling.notifyError(e, this.navLang);
      }
    );
  }

  override getData(): DropdownMenuItem[] {
    return [
      {
        text: this.translateService.instant('root.common.button.REMOVE'),
        value: CommonAction.REMOVE,
        iconName: 'restore_from_trash',
        hidden: false
      }
    ];
  }
}
