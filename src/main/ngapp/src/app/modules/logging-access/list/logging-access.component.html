<div class="grid-container grid-floating-active full-width-grid-container">
  <app-grid
    id="logging-access"
    #grid
    name="logging-access"
    [url]="url"
    [perPage]="perPage"
    [columns]="columns"
    class="container-form grid-floating-action-buttons"
    [titleLabel]="title"
    [topZoneEnable]="true"
  >
    <ng-container ngProjectAs="[dialogTopZone]">
      <div class="description-container">
        <strong>{{ 'lblDescription' | translate: this }}</strong>
        <span>{{ descriptionLogging() }}</span>
      </div>
    </ng-container>
  </app-grid>
</div>
