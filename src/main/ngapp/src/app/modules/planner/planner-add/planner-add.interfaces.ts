import type { TextLongValue } from '@/core/utils/text-has-value';
import type { ActivityTypeFilllTypeDto } from '@/shared/activities/core/activities-core.interfaces';
import type { ActivityEntity } from 'src/app/shared/activities/core/utils/activity-base.interfaces';
import type { PlannerAction } from './planner-add.enums';

export interface IPlanner {
  id?: number;
  code?: string;
  taskTypeId?: number;
  parentPlannerId?: number;
  description: string;
  businessUnitDepartmentId: number;
  businessUnitId: number;
  clientId: number;
  ownerId?: number;
  ownerNames?: string;
  tags?: string;
  activityId?: number;
  status?: number;
  isEndless?: boolean;
  startDate: Date;
  endDate: Date;
  contactMailList?: string;
  plainContactMailList?: string;
  soldHours?: number;
  approvedHours?: number;
  plannedProgress?: number;
  realProgress?: number;
}
export interface IPlannerSaveDto {
  strategy?: PlannerAction;
  project: IPlanner;
  activity: ActivityEntity;
}
export interface IPlannerClientDto extends TextLongValue {
  businessUnitIds: number[];
}
export interface IPlannerDataSourceDto {
  clients: IPlannerClientDto[];
  taskTypes: ActivityTypeFilllTypeDto[];
}

export enum PlannerStatus {
  DRAFT = 1,
  ACTIVE = 2,
  INACTIVE = 3,
  FINISHED = 4
}
