import type { GridColumn } from '@/core/grid/utils/grid-column';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';

import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { Component, type OnInit, inject, viewChild } from '@angular/core';

import * as GridUtil from '@/core/grid/utils/grid-util';
import { type ActionStrip, DateColumn, DoubleColumn, IntegerColumn, ObjectListColumn, TextColumn, TimestampColumn } from '@/core/grid/utils/grid.interfaces';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { DatePipe } from '@angular/common';
import { type CellType, IgxLinearProgressBarComponent, type RowType } from '@infragistics/igniteui-angular';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import { GridComponent } from 'src/app/core/grid/grid.component';
import { GridColumnVisibility, type GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { Session } from 'src/app/core/local-storage/session';
import { AppService } from 'src/app/core/services/app.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { ActivityCoreUtil } from 'src/app/shared/activities/core/activities-core.utils';

@Component({
  selector: 'app-planner-tasks',
  templateUrl: './planner-tasks.component.html',
  styleUrls: ['./planner-tasks.component.scss'],
  imports: [IgxLinearProgressBarComponent, GridComponent, BnextTranslatePipe]
})
export class PlannerTasksComponent extends BnextCoreComponent implements OnInit, i18n {
  datePipe = inject(DatePipe);

  service = inject(AppService);
  noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.planner',
    componentName: 'planner-tasks'
  };

  override get componentPath(): string {
    return PlannerTasksComponent.LANG_CONFIG.componentPath;
  }

  override get tagName(): string {
    return PlannerTasksComponent.LANG_CONFIG.componentName;
  }

  id = 'project-tasks';
  name = 'project-tasks';
  url = 'planner/project/tasks/list';
  columns: GridColumn[] = [];
  perPage: number = Session.getGridSize();
  busy = false;

  LangConfig = PlannerTasksComponent.LANG_CONFIG;

  readonly grid = viewChild<GridComponent>('grid');

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.getData(row?.data) : []),
      rowIdentifierKey: 'task_code'
    }
  };

  override ngOnInit(): void {
    super.ngOnInit();
    this.columns = [];
    const dte = new DateColumn();
    const statuses = ActivityCoreUtil.getLocalizedValuesList({
      translateService: this.translate,
      subs: this.subs
    });
    GridUtil.columns(this.columns)
      .subs(this.subs)
      .push('task_description', '350px')
      .push(
        'task_status',
        new ObjectListColumn({
          width: '175px',
          render: {
            valueKey: 'value',
            labelKey: 'label',
            items: statuses
          }
        })
      )
      .push('task_code', '145px')
      .push('entity_client_description', '150px')
      .push('entity_businessUnit_description', '150px')
      .push('entity_businessUnitDepartment_description', '200px')
      .push(
        'task_progress',
        new DoubleColumn({
          width: '80px'
        })
      )
      .push(
        'task_plannedImplementationDate',
        new DateColumn({
          width: '180px',
          hidden: true
        })
      )
      .push(
        'task_reminder',
        new DateColumn({
          width: '180px'
        })
      )
      .push(
        'task_implementationOn',
        new DateColumn({
          width: '180px',
          hidden: true
        })
      )
      .push(
        'task_anticipationAttendDays',
        new IntegerColumn({
          width: '100px',
          hidden: true
        })
      )
      .push(
        'task_plannedVerificationDate',
        new DateColumn({
          width: '180px',
          hidden: true
        })
      )
      .push(
        'task_verificationOn',
        new DateColumn({
          width: '180px',
          hidden: true
        })
      )
      .push(
        'task_daysToVerify',
        new IntegerColumn({
          width: '100px',
          hidden: true
        })
      )
      .push('task_groupName', '250px', GridColumnVisibility.HIDDEN)
      .push(
        'a_description',
        new TextColumn({
          width: '150px'
        })
      )
      .push('task_parentCode', '150px')
      .push('task_objective_description', '170px', GridColumnVisibility.HIDDEN)
      .push('task_source_description', '190px', GridColumnVisibility.HIDDEN)
      .push(
        'task_plannedHours',
        new DoubleColumn({
          width: '150px'
        })
      )
      .push(
        'task_actualHours',
        new DoubleColumn({
          width: '150px'
        })
      )
      .push(
        'task_createdDate',
        new TimestampColumn({
          hidden: true,
          width: '175px'
        })
      )
      .push('task_lastModifiedDate', new TimestampColumn({ width: '175px', hidden: true }))
      // Proyecto
      .push('entity_startDate', dte)
      .push('entity_endDate', dte)
      .push('entity_contactMailList', new TextColumn({ width: '250px', hidden: true }))
      .push(
        'a_plannedHours',
        new DoubleColumn({
          hidden: true,
          width: '150px'
        })
      )
      .push(
        'a_actualHours',
        new DoubleColumn({
          hidden: true,
          width: '150px'
        })
      )
      .push(
        'entity_soldHours',
        new DoubleColumn({
          width: '160px'
        })
      )
      .push(
        'entity_approvedHours',
        new DoubleColumn({
          hidden: true,
          width: '160px'
        })
      )
      .push(
        'entity_plannedProgress',
        new DoubleColumn({
          hidden: true,
          width: '160px'
        })
      )
      .push(
        'entity_realProgress',
        new DoubleColumn({
          width: '160px'
        })
      )
      .push(
        'entity_createdDate',
        new TimestampColumn({
          hidden: true,
          width: '175px'
        })
      )
      .push('entity_lastModifiedDate', new TimestampColumn({ width: '175px', hidden: true }))
      .langAsync(this.translate, PlannerTasksComponent.LANG_CONFIG, 'i18n');
    this.grid().refresh();
    this.cdr.detectChanges();
  }

  override langReady() {
    // ToDo
  }

  getData(row: any): DropdownMenuItem[] {
    return [
      {
        text: this.translateService.instant('root.common.button.OPEN_DETAIL'),
        value: CommonAction.EDIT,
        modernHref: `activities/planner/${row.task_id}`,
        iconName: 'edit',
        hidden: false
      }
    ];
  }

  toogleMenu(drop: GridDropDownItem) {
    switch (drop.item.value) {
      case CommonAction.EDIT:
        this.menuService.navigate(`menu/planner/task-detail/${drop.row.task_id}`, Module.PLANNER);
        break;
    }
  }

  statusCallback(cell: CellType) {
    const status = cell.row.data.entity_status;
    let msg = this.i18n['status-active-dialog-message'] as string;

    if (status === 2) {
      //Estatus activo
      msg = this.i18n['status-inactive-dialog-message'] as string;
    }
    this.dialogService.confirm(msg).then(() => {
      this.service.get({ url: `planner/project/toggle-status/${cell.row.data.task_id}`, handleFailure: false, cancelableReq: this.$destroy }).subscribe(
        () => {
          this.grid().refresh();
          this.noticeService.notice(this.i18n['accept-dialog-message'] as string);
        },
        (error) => {
          console.error(error);
          ErrorHandling.notifyError(error, this.navLang);
        }
      );
    });
  }
}
