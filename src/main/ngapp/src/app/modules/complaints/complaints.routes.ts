import type { Route } from '@angular/router';

export const ROUTES: Route[] = [
  {
    path: 'report',
    loadComponent: () => import('@/modules/complaints/reports/reports.component').then((m) => m.ReportsComponent)
  },
  {
    path: 'complaint-sources/add',
    loadComponent: () => import('@/modules/complaints/source-complaints/source-complaints.component').then((m) => m.SourceComplaintsComponent)
  },
  {
    path: 'complaint-sources/edit/:typeId',
    loadComponent: () => import('@/modules/complaints/source-complaints-edit/source-complaints-edit.component').then((m) => m.SourceComplaintsEditComponent)
  }
];
