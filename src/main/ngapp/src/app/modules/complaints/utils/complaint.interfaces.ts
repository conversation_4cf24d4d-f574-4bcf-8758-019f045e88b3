import type { TextHasValue } from '@/core/utils/text-has-value';

export interface ComplaintCatalogsDto {
  priority: TextHasValue[];
  sourcePriority: TextHasValue[];
  department: TextHasValue[];
  users: TextHasValue[];
  clasifications: TextHasValue[];
}

export interface IComplaintCalification extends TextHasValue {
  stars: number;
}

export interface ComplaintChartData {
  value: number;
  label: string;
}

export interface ComplaintObjectFilter {
  type: string;
  where: ComplaintObjectFilterWhere;
}

export interface ComplaintObjectFilterWhere {
  type: string;
  value: number;
}

export interface ITextHasValueComplaint extends TextHasValue {
  type: string;
}

export interface ISourceComplaint {
  code: string;
  description: string;
  module?: string;
  status: number;
  deleted: number;
  isRetainable?: number;
  id: {
    tipoId?: number;
    catalogoId?: number;
  };
  value?: string;
}
