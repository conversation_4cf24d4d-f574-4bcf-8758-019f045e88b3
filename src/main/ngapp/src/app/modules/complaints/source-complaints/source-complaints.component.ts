import { AppService } from '@/core/services/app.service';
import { Component, type OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, type UntypedFormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import type { ISourceComplaint } from '../utils/complaint.interfaces';

import { BnextCoreComponent } from '@/core/bnext-core.component';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { ReportsComponent } from '../reports/reports.component';

import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { BnextLoaderActivationService } from '@/core/services/bnext-loader-activation.service';
import { DialogService } from '@/core/services/dialog.service';
import { NgClass } from '@angular/common';
import { IgxButtonDirective, IgxHintDirective, IgxInputDirective, IgxInputGroupComponent, IgxLabelDirective } from '@infragistics/igniteui-angular';

@Component({
  selector: 'app-source-complaints',
  templateUrl: './source-complaints.component.html',
  styleUrls: ['./source-complaints.component.scss'],
  imports: [
    NgClass,
    FormsModule,
    ReactiveFormsModule,
    IgxInputGroupComponent,
    IgxLabelDirective,
    IgxInputDirective,
    IgxHintDirective,
    IgxButtonDirective,
    BnextTranslatePipe
  ]
})
export class SourceComplaintsComponent extends BnextCoreComponent implements OnInit {
  fb = inject(UntypedFormBuilder);
  service = inject(AppService);

  loader = inject(BnextLoaderActivationService);
  dialog = inject(DialogService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.complaints',
    componentName: 'source-complaints'
  };

  get componentPath(): string {
    return 'modules.complaints';
  }

  public form: UntypedFormGroup;
  protected subject = new Subject();

  ngOnInit(): void {
    this.form = this.fb.group({
      name: new UntypedFormControl('', [Validators.required]),
      description: new UntypedFormControl('', [Validators.required]),
      typeId: new UntypedFormControl(null)
    });
  }

  public onSave(): void {
    this.save();
  }

  private save(): void {
    if (this.form.valid) {
      const data: ISourceComplaint = {
        code: this.form.get('name').value,
        description: this.form.get('description').value,
        deleted: 0,
        status: 4,
        id: {}
      };
      this.loader.show();
      this.service
        .post({ url: 'complaint/save-complaint-source', postBody: data, cancelableReq: this.subject })
        .pipe(takeUntil(this.subject))
        .subscribe((response: ISourceComplaint) => {
          if (response) {
            this.loader.hide();
            this.dialog
              .success(
                this.translateService.instantFrom(ReportsComponent.LANG_CONFIG, 'complaintSourceSaved', {
                  code: response.code
                }),
                'root.common.button.create',
                'root.common.button.control'
              )
              .then(
                () => {
                  this.menuService.navigate('menu/complaints/complaint-sources/add');
                },
                () => {
                  this.menuService.navigateLegacy('v.complaint.sources.list.view');
                }
              );
          }
        });
    }
  }

  public onCancel(): void {
    this.navLang.back();
  }
}
