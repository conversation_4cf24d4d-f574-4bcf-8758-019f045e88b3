import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ActivitiesImplementationComponent } from './activities-implementation.component';

describe('ActivitiesImplementationComponent', () => {
  let component: ActivitiesImplementationComponent;
  let fixture: ComponentFixture<ActivitiesImplementationComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivitiesImplementationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
