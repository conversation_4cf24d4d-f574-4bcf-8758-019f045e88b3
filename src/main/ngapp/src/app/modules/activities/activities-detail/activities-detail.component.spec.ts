import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ActivitiesDetailComponent } from './activities-detail.component';

describe('ActivitiesDetailComponent', () => {
  let component: ActivitiesDetailComponent;
  let fixture: ComponentFixture<ActivitiesDetailComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, ActivitiesDetailComponent],
      providers: [MockProviders.PROVIDERS]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivitiesDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
