import { type ComponentFixture, TestBed } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ActivitiesEvaluationDashboardComponent } from './activities-evaluation-dashboard.component';

describe('ActivitiesEvaluationDashboardComponent', () => {
  let component: ActivitiesEvaluationDashboardComponent;
  let fixture: ComponentFixture<ActivitiesEvaluationDashboardComponent>;

  configureTestSuite();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MockCoreModule, ActivitiesEvaluationDashboardComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivitiesEvaluationDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
