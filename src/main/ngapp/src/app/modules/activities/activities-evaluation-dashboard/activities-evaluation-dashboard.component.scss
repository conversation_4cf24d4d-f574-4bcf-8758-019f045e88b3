@use 'src/styles/mediaQueries' as *;
@use 'src/app/shared/activities/core/style/statusColors' as *;
@use 'src/app/core/grid/grid-toolbar';
@use 'src/styles/immutable-colors' as *;
@use 'src/styles/menu' as *;

:host ::ng-deep {
  .progress-as-label {
    width: 100%;
    height: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .igx-circular-bar {
    width: 3rem;
    height: 3rem;
  }

  .score-details {
    width: 100%;
    height: 100%;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    &:hover {
      margin: 0.2rem;
      background-color: #f5ee9a;
      border-radius: 3rem;
    }
  }
  .commitment {
    &--ONTIME {
      color: $green;
    }
    &--DELAYED {
      color: $red;
    }
    &--ATTENDED {
      color: $gray;
    }
  }
  .description-cell {
    display: flex;
    flex-direction: row;
    min-width: 0;
    width: 100%;
    .description-value {
      display: inline-block;
      align-items: center;
      flex-grow: 1;
      flex-shrink: 1;
      flex-basis: 0%;
      word-wrap: break-word;
      align-self: center;
    }
  }
  .center-column {
    display: block;
    width: 100%;
    text-align: center;

    > * {
      margin: 0.125rem;
    }
    &--many {
      margin-left: 1.25rem;

      > * {
        margin-left: -1.25rem;
        border: 1px solid $bg-color;
      }
    }
  }
  igx-grid-toolbar-hiding button span,
  igx-grid-toolbar-pinning button span {
    margin-right: 0.2rem;
  }
  igx-circular-bar {
    &.progress-completed {
      .igx-circular-bar__outer {
        stroke: green !important;
      }
    }
  }
  igx-paginator .igx-input-group--border .igx-input-group__bundle igx-suffix {
    padding: 0;
  }

  .igx-grid__tbody-content {
    height: inherit !important;
  }
}

.grid-update-container {
  justify-content: flex-end !important;
  align-items: center !important;
  gap: 1rem;
}

.grid-update-last-updated {
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-update-last-updated {
  font-style: italic;
  font-weight: 300;
  font-size: 0.8em;
  padding-right: 0.125rem;
}

.full-width-grid-container .grid-container {
  width: 100%;
  background: transparent;
}

.grid-update-action {
  display: flex;
}

::ng-deep {
  @media print, screen and (min-width: $xlarge) and (max-width: 109.375rem) {
    .hierarchicalGrid .igx-grid-toolbar .igx-grid-toolbar__actions {
      app-grid-export .igx-icon-button span:last-of-type,
      igx-grid-toolbar-pinning .igx-icon-button span:last-of-type,
      igx-grid-toolbar-hiding .igx-icon-button span:last-of-type,
      app-grid-export .igx-button span:last-of-type,
      igx-grid-toolbar-pinning .igx-button span:last-of-type,
      igx-grid-toolbar-hiding .igx-button span:last-of-type {
        display: none;
      }
    }
  }
}

::ng-deep {
  /* responsividad small */
  @media print, screen and (max-width: $small) {
    .igx-paginator {
      padding: 0 1rem;

      .igx-page-size .igx-page-size__label {
        display: none;
      }
    }
  }
}

:host {
  &.maximized {
    position: fixed;
    top: 0;
    left: 4.25rem;
    width: calc(100vw - $aside-width);
    height: 100vh;
    background: $bg-color;
    z-index: 2;
  }

  igx-tree-grid {
    height: 95vh !important;
  }
}
