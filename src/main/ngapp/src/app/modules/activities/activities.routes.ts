import { ConfirmableLeaveGuard } from '@/core/utils/confirmable-leave.guard';
import type { Route } from '@angular/router';

export const ROUTES: Route[] = [
  {
    path: 'add',
    loadComponent: () => import('./activities-add/activities-add.component').then((m) => m.ActivitiesAddComponent)
  },
  {
    path: 'add/workflow/:workflow',
    loadComponent: () => import('./activities-add/activities-add.component').then((m) => m.ActivitiesAddComponent)
  },
  {
    path: 'reports',
    loadComponent: () => import('./activities-reports/activities-reports.component').then((m) => m.ActivitiesReportsComponent)
  },
  {
    path: 'reports/:id',
    loadComponent: () => import('./activities-report/activities-report.component').then((m) => m.ActivitiesReportComponent)
  },
  {
    path: 'reports/:id/:interval',
    loadComponent: () => import('./activities-report/activities-report.component').then((m) => m.ActivitiesReportComponent)
  },
  {
    path: 'add-advanced',
    loadComponent: () => import('./activities-add/activities-add.component').then((m) => m.ActivitiesAddComponent)
  },
  {
    path: 'add-advanced/workflow/:workflow',
    loadComponent: () => import('./activities-add/activities-add.component').then((m) => m.ActivitiesAddComponent)
  },
  {
    path: 'add-many',
    loadComponent: () => import('./activities-add-many/activities-add-many.component').then((m) => m.ActivitiesAddManyComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'add-many/workflow/:workflow',
    loadComponent: () => import('./activities-add-many/activities-add-many.component').then((m) => m.ActivitiesAddManyComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'add-plan',
    loadComponent: () => import('./activities-add-plan/activities-add-plan.component').then((m) => m.ActivitiesAddPlanComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'add-plan/workflow/:workflow',
    loadComponent: () => import('./activities-add-plan/activities-add-plan.component').then((m) => m.ActivitiesAddPlanComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'add-plan/workflow/:workflow/:interval',
    loadComponent: () => import('./activities-add-plan/activities-add-plan.component').then((m) => m.ActivitiesAddPlanComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'workload', // <-- ToDo: ISSUE-1602
    loadComponent: () => import('./activities-workload/activities-workload.component').then((m) => m.ActivitiesWorkloadComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'workload/workflow/:workflow', // <-- ToDo: ISSUE-1602
    loadComponent: () => import('./activities-workload/activities-workload.component').then((m) => m.ActivitiesWorkloadComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'workload/workflow/:workflow/:interval', // <-- ToDo: ISSUE-1602
    loadComponent: () => import('./activities-workload/activities-workload.component').then((m) => m.ActivitiesWorkloadComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'my-workload', // <-- ToDo: ISSUE-1602
    loadComponent: () => import('./activities-my-workload/activities-my-workload.component').then((m) => m.ActivitiesMyWorkloadComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'my-workload/workflow/:workflow', // <-- ToDo: ISSUE-1602
    loadComponent: () => import('./activities-my-workload/activities-my-workload.component').then((m) => m.ActivitiesMyWorkloadComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'my-workload/workflow/:workflow/:interval', // <-- ToDo: ISSUE-1602
    loadComponent: () => import('./activities-my-workload/activities-my-workload.component').then((m) => m.ActivitiesMyWorkloadComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'favorite-add',
    loadComponent: () => import('./activities-add-many/activities-add-many.component').then((m) => m.ActivitiesAddManyComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'favorite-add/workflow/:workflow',
    loadComponent: () => import('./activities-add-many/activities-add-many.component').then((m) => m.ActivitiesAddManyComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'list',
    loadComponent: () => import('./activities-list/activities-list.component').then((m) => m.ActivitiesListComponent)
  },
  {
    path: 'list/:module',
    loadComponent: () => import('./activities-list/activities-list.component').then((m) => m.ActivitiesListComponent)
  },
  {
    path: 'list/workflow/:workflow',
    loadComponent: () => import('./activities-list/activities-list.component').then((m) => m.ActivitiesListComponent)
  },
  {
    path: 'mine',
    loadComponent: () => import('./my-activities/my-activities.component').then((m) => m.MyActivitiesComponent)
  },
  {
    path: 'mine/:module',
    loadComponent: () => import('./my-activities/my-activities.component').then((m) => m.MyActivitiesComponent)
  },
  {
    path: 'mine/workflow/:workflow',
    loadComponent: () => import('./my-activities/my-activities.component').then((m) => m.MyActivitiesComponent)
  },
  {
    path: 'mine/:module/workflow/:workflow',
    loadComponent: () => import('./my-activities/my-activities.component').then((m) => m.MyActivitiesComponent)
  },
  {
    path: 'deleted',
    loadComponent: () => import('./activities-deleted/activities-deleted.component').then((m) => m.ActivitiesDeletedComponent)
  },
  {
    path: 'deleted/workflow/:workflow',
    loadComponent: () => import('./activities-deleted/activities-deleted.component').then((m) => m.ActivitiesDeletedComponent)
  },
  {
    path: 'consolidated',
    loadComponent: () => import('./consolidated-activities/consolidated-activities.component').then((m) => m.ConsolidatedActivitiesComponent)
  },
  {
    path: 'consolidated/:module',
    loadComponent: () => import('./consolidated-activities/consolidated-activities.component').then((m) => m.ConsolidatedActivitiesComponent)
  },
  {
    path: 'consolidated/workflow/:workflow',
    loadComponent: () => import('./consolidated-activities/consolidated-activities.component').then((m) => m.ConsolidatedActivitiesComponent)
  },
  {
    path: 'consolidated/:module/workflow/:workflow',
    loadComponent: () => import('./consolidated-activities/consolidated-activities.component').then((m) => m.ConsolidatedActivitiesComponent)
  },
  {
    path: 'weekly-score',
    loadComponent: () => import('./activities-score-query/activities-score-query.component').then((m) => m.ActivitiesScoreQueryComponent)
  },
  {
    path: 'weekly-score/:interval',
    loadComponent: () => import('./activities-score-query/activities-score-query.component').then((m) => m.ActivitiesScoreQueryComponent)
  },
  {
    path: 'planning-owner-dashboard',
    loadComponent: () =>
      import('./activities-planning-owner-dashboard/activities-planning-owner-dashboard.component').then((m) => m.ActivitiesPlanningOwnerDashboardComponent)
  },
  {
    path: 'planning-owner-dashboard/:interval',
    loadComponent: () =>
      import('./activities-planning-owner-dashboard/activities-planning-owner-dashboard.component').then((m) => m.ActivitiesPlanningOwnerDashboardComponent)
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./activities-dashboard/activities-dashboard.component').then((m) => m.ActivitiesDashboardComponent)
  },
  {
    path: 'dashboard/:interval',
    loadComponent: () => import('./activities-dashboard/activities-dashboard.component').then((m) => m.ActivitiesDashboardComponent)
  },
  {
    path: 'owner-dashboard',
    loadComponent: () => import('./activities-owner-dashboard/activities-owner-dashboard.component').then((m) => m.ActivitiesOwnerDashboardComponent)
  },
  {
    path: 'owner-dashboard/:interval',
    loadComponent: () => import('./activities-owner-dashboard/activities-owner-dashboard.component').then((m) => m.ActivitiesOwnerDashboardComponent)
  },
  {
    path: 'evaluation-dashboard',
    loadComponent: () => import('./activities-evaluation-dashboard/activities-evaluation-dashboard.component').then((m) => m.ActivitiesEvaluationDashboardComponent)
  },
  {
    path: 'evaluation-dashboard/:interval',
    loadComponent: () => import('./activities-evaluation-dashboard/activities-evaluation-dashboard.component').then((m) => m.ActivitiesEvaluationDashboardComponent)
  },
  {
    path: 'commitments-summary',
    loadComponent: () => import('./activities-commitments-summary/activities-commitments-summary.component').then((m) => m.ActivitiesCommitmentsSummaryComponent)
  },
  {
    path: 'commitments-summary/:userId',
    loadComponent: () => import('./activities-commitments-summary/activities-commitments-summary.component').then((m) => m.ActivitiesCommitmentsSummaryComponent)
  },
  {
    path: 'commitments-summary/:userId/:interval',
    loadComponent: () => import('./activities-commitments-summary/activities-commitments-summary.component').then((m) => m.ActivitiesCommitmentsSummaryComponent)
  },
  {
    path: 'template/list',
    loadComponent: () => import('./activities-template-list/activities-template-list.component').then((m) => m.ActivitiesTemplateListComponent)
  },
  {
    path: 'template/:id',
    loadComponent: () => import('./activities-template-modify/activities-template-modify.component').then((m) => m.ActivitiesTemplateModifyComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'template',
    loadComponent: () => import('./activities-template-create/activities-template-create.component').then((m) => m.ActivitiesTemplateCreateComponent),
    canDeactivate: [ConfirmableLeaveGuard]
  },
  {
    path: 'code/:code',
    loadComponent: () => import('./activities-detail-code-wrapper/activities-detail-code-wrapper.component').then((m) => m.ActivitiesDetailCodeWrapperComponent)
  },
  {
    path: 'code/:code/:module',
    loadComponent: () => import('./activities-detail-code-wrapper/activities-detail-code-wrapper.component').then((m) => m.ActivitiesDetailCodeWrapperComponent)
  },
  {
    path: ':id',
    loadComponent: () => import('./activities-detail/activities-detail.component').then((m) => m.ActivitiesDetailComponent)
  },
  {
    path: ':module/:id',
    loadComponent: () => import('./activities-detail/activities-detail.component').then((m) => m.ActivitiesDetailComponent)
  }
];
