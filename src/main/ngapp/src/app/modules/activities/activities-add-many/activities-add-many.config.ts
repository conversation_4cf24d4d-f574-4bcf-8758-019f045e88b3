import type { CommitmentTask } from '@/shared/activities/core/activities-core.enums';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import type { BusinessUnitDepartmentEntity, PlannerStartDatesEntity } from 'src/app/shared/activities/core/activities-core.interfaces';
import type { ActivityLinkedDataSource, ActivityLinkedSource, IActivityAddManyConfiguration } from 'src/app/shared/activities/core/utils/activity-base.interfaces';

export class ActivityAddManyConfiguration implements IActivityAddManyConfiguration {
  _module = Module.ACTIVITY;
  _isGroupsAvailable = true;
  _isParticipantsAvailable = false;
  _isFlexibleCommitmentTask = false;
  _flexibleCommitmentTaskAvailable: CommitmentTask[] = null;
  _isTabContainer = true;
  _isSaveButtonEnabled = false;
  _isVerifierRemoved = true;
  _isRowModuleReplacementEnabled = true;
  _commitmentTask = null;
  _defaultRowModuleReplacementDeep = 1;
  _isButtonSectionHidden = true;
  _defaultTypeId: number = null;
  _defaultTypeController: string = null;
  _defaultTypeControllerImplementation: string = null;
  _defaultTypeControllerVerification: string = null;
  _defaultGroupName: string = null;
  _defaultBusinessUnitDepartment: BusinessUnitDepartmentEntity = null;
  _defaultPlannerStartDatesEntity: PlannerStartDatesEntity = null;
  _customImplementationLabel: string = null;
  _defaultImplementerIds: number[] = [];
  _linkedSource: ActivityLinkedSource = null;
  _linkedDataSource: ActivityLinkedDataSource = null;
  _recurrenceId: number = null;
  _parentActivityId: number = null;
  _parentActivityImplementationId: number = null;
  _parentActivityVerificationId: number = null;
  _parentTreeActivityId: number = null;
  _parentPlannerId: number = null;
  _isPopEnabled = false;
  _isSetupAfterViewInit = false;
  _isCancelButtonEnabled = false;

  constructor(
    _defaultTypeId: number,
    _defaultTypeController: string,
    _defaultGroupName: string,
    _defaultBusinessUnitDepartment: BusinessUnitDepartmentEntity,
    _customImplementationLabel: string,
    _defaultPlannerStartDatesEntity: PlannerStartDatesEntity,
    _defaultImplementerIds: number[],
    _isSaveButtonEnabled: boolean,
    _isParticipantsAvailable: boolean,
    _isGroupsAvailable: boolean,
    _parentPlannerId: number,
    _module: Module,
    _isVerifierRemoved = true,
    _isRowModuleReplacementEnabled = true,
    _defaultRowModuleReplacementDeep = 1,
    _isCancelButtonEnabled = false,
    _isButtonSectionHidden = true,
    _isTabContainer = true,
    _linkedSource: ActivityLinkedSource = null,
    _linkedDataSource: ActivityLinkedDataSource = null,
    _recurrenceId: number = null,
    _parentActivityId: number = null,
    _parentActivityImplementationId: number = null,
    _parentActivityVerificationId: number = null,
    _parentTreeActivityId: number = null,
    _isPopEnabled = false,
    _isSetupAfterViewInit = false
  ) {
    this._defaultTypeId = _defaultTypeId;
    this._defaultTypeController = _defaultTypeController || null;
    this._defaultGroupName = _defaultGroupName;
    this._defaultBusinessUnitDepartment = _defaultBusinessUnitDepartment;
    this._customImplementationLabel = _customImplementationLabel;
    this._defaultPlannerStartDatesEntity = _defaultPlannerStartDatesEntity;
    this._defaultImplementerIds = _defaultImplementerIds;
    this._isVerifierRemoved = _isVerifierRemoved;
    this._isRowModuleReplacementEnabled = _isRowModuleReplacementEnabled;
    this._defaultRowModuleReplacementDeep = _defaultRowModuleReplacementDeep;
    this._isParticipantsAvailable = _isParticipantsAvailable;
    this._isSaveButtonEnabled = _isSaveButtonEnabled;
    this._isGroupsAvailable = _isGroupsAvailable;
    this._parentPlannerId = _parentPlannerId;
    this._module = _module;
    this._isButtonSectionHidden = _isButtonSectionHidden;
    this._isTabContainer = _isTabContainer;
    this._linkedSource = _linkedSource;
    this._linkedDataSource = _linkedDataSource;
    this._recurrenceId = _recurrenceId;
    this._parentActivityId = _parentActivityId;
    this._parentActivityImplementationId = _parentActivityImplementationId;
    this._parentActivityVerificationId = _parentActivityVerificationId;
    this._parentTreeActivityId = _parentTreeActivityId;
    this._isPopEnabled = _isPopEnabled;
    this._isSetupAfterViewInit = _isSetupAfterViewInit;
    this._isCancelButtonEnabled = _isCancelButtonEnabled;
  }

  public setDefaultTypeId(_defaultTypeId: number): void {
    this._defaultTypeId = _defaultTypeId;
  }

  public setLinkedSource(_linkedSource: ActivityLinkedSource): void {
    this._linkedSource = _linkedSource;
  }

  public setLinkedDataSource(_linkedDataSource: ActivityLinkedDataSource): void {
    this._linkedDataSource = _linkedDataSource;
  }

  public setIsVerifierRemoved(_isVerifierRemoved: boolean): void {
    this._isVerifierRemoved = _isVerifierRemoved;
  }

  public setIsMainActivitySubtaskModuleEnabled(_isRowModuleReplacementEnabled: boolean): void {
    this._isRowModuleReplacementEnabled = _isRowModuleReplacementEnabled;
  }

  public setDefaultMainActivitySubtaskModuleDeep(_defaultRowModuleReplacementDeep: number): void {
    this._defaultRowModuleReplacementDeep = _defaultRowModuleReplacementDeep;
  }

  public setDefaultGroupName(_defaultGroupName: string): void {
    this._defaultGroupName = _defaultGroupName;
  }

  public setDefaultBusinessUnitDepartment(_defaultBusinessUnitDepartment: BusinessUnitDepartmentEntity): void {
    this._defaultBusinessUnitDepartment = _defaultBusinessUnitDepartment;
  }

  public setCustomImplementationLabel(_customImplementationLabel: string): void {
    this._customImplementationLabel = _customImplementationLabel;
  }

  public setDefaultPlannerStartDatesEntity(_defaultPlannerStartDatesEntity: PlannerStartDatesEntity): void {
    this._defaultPlannerStartDatesEntity = _defaultPlannerStartDatesEntity;
  }

  public setDefaultImplementerIds(_defaultImplementerIds: number[]): void {
    this._defaultImplementerIds = _defaultImplementerIds;
  }

  public setIsSaveButtonEnabled(_isSaveButtonEnabled: boolean): void {
    this._isSaveButtonEnabled = _isSaveButtonEnabled;
  }

  public setIsParticipantsAvailable(_isParticipantsAvailable: boolean): void {
    this._isParticipantsAvailable = _isParticipantsAvailable;
  }

  public setIsGroupsAvailable(_isGroupsAvailable: boolean): void {
    this._isGroupsAvailable = _isGroupsAvailable;
  }

  public setParentPlannerId(_parentPlannerId: number): void {
    this._parentPlannerId = _parentPlannerId;
  }

  public setParentActivityId(_parentActivityId: number): void {
    this._parentActivityId = _parentActivityId;
  }

  public setIsRowModuleReplacementEnabled(_isRowModuleReplacementEnabled: boolean): void {
    this._isRowModuleReplacementEnabled = _isRowModuleReplacementEnabled;
  }

  public setDefaultRowModuleReplacementDeep(_defaultRowModuleReplacementDeep: number): void {
    this._defaultRowModuleReplacementDeep = _defaultRowModuleReplacementDeep;
  }

  public setParentActivityImplementationId(_parentActivityImplementationId: number): void {
    this._parentActivityImplementationId = _parentActivityImplementationId;
  }

  public setParentActivityVerificationId(_parentActivityVerificationId: number): void {
    this._parentActivityVerificationId = _parentActivityVerificationId;
  }

  public setRecurrenceId(_recurrenceId: number): void {
    this._recurrenceId = _recurrenceId;
  }

  public setParentTreeActivityId(parentTreeActivityId: number): void {
    this._parentTreeActivityId = parentTreeActivityId;
  }

  public setModule(_module: Module): void {
    this._module = _module;
  }

  get module(): Module {
    return this._module;
  }

  public setDefaultTypeController(_defaultTypeController: string): void {
    this._defaultTypeController = _defaultTypeController;
  }

  public setDefaultTypeControllerVerification(_defaultTypeControllerVerification: string): void {
    this._defaultTypeControllerVerification = _defaultTypeControllerVerification;
  }

  get defaultTypeControllerVerification(): string {
    return this._defaultTypeControllerVerification;
  }

  public setDefaultTypeControllerImplementation(_defaultTypeControllerImplementation: string): void {
    this._defaultTypeControllerImplementation = _defaultTypeControllerImplementation;
  }

  get defaultTypeControllerImplementation(): string {
    return this._defaultTypeControllerImplementation;
  }

  public setIsFlexibleCommitmentTask(_isFlexibleCommitmentTask: boolean): void {
    this._isFlexibleCommitmentTask = _isFlexibleCommitmentTask;
  }

  get isFlexibleCommitmentTask(): boolean {
    return this._isFlexibleCommitmentTask;
  }

  public setFlexibleCommitmentTaskAvailable(_flexibleCommitmentTaskAvailable: CommitmentTask[]): void {
    this._flexibleCommitmentTaskAvailable = _flexibleCommitmentTaskAvailable;
  }

  get flexibleCommitmentTaskAvailable(): CommitmentTask[] {
    return this._flexibleCommitmentTaskAvailable;
  }

  public setCommitmentTask(_commitmentTask: CommitmentTask): void {
    this._commitmentTask = _commitmentTask;
  }

  get commitmentTask(): CommitmentTask {
    return this._commitmentTask;
  }

  get defaultImplementerIds(): number[] {
    return this._defaultImplementerIds;
  }

  get defaultTypeId(): number {
    return this._defaultTypeId || null;
  }

  get linkedSource(): ActivityLinkedSource {
    return this._linkedSource || null;
  }

  get linkedDataSource(): ActivityLinkedDataSource {
    return this._linkedDataSource || null;
  }

  get parentActivityId(): number {
    return this._parentActivityId || null;
  }

  get recurrenceId(): number {
    return this._recurrenceId || null;
  }

  get parentActivityImplementationId(): number {
    return this._parentActivityImplementationId || null;
  }

  get parentActivityVerificationId(): number {
    return this._parentActivityVerificationId || null;
  }

  get parentTreeActivityId(): number {
    return this._parentTreeActivityId || null;
  }

  get parentPlannerId(): number {
    return this._parentPlannerId || null;
  }

  get isSetupAfterViewInit(): boolean {
    return this._isSetupAfterViewInit || null;
  }

  get isPopEnabled(): boolean {
    return this._isPopEnabled || null;
  }

  set isPopEnabled(popEnabled: boolean) {
    this._isPopEnabled = popEnabled;
  }

  get defaultGroupName(): string {
    return this._defaultGroupName;
  }

  get defaultBusinessUnitDepartment(): BusinessUnitDepartmentEntity {
    return this._defaultBusinessUnitDepartment;
  }

  get defaultPlannerStartDatesEntity(): PlannerStartDatesEntity {
    return this._defaultPlannerStartDatesEntity;
  }

  get availableMenuOptions(): string[] {
    return [];
  }

  get defaultTypeController(): string {
    return this._defaultTypeController;
  }

  get subTitleLabel(): string {
    return null;
  }

  get customImplementationLabel(): string {
    return this._customImplementationLabel;
  }

  get isHeaderAvailable(): boolean {
    return false;
  }

  get isTabContainer(): boolean {
    return this._isTabContainer;
  }

  get isParticipantsAvailable(): boolean {
    return this._isParticipantsAvailable;
  }

  get isGroupsAvailable(): boolean {
    return this._isGroupsAvailable;
  }

  get isButtonSectionHidden(): boolean {
    return this._isButtonSectionHidden;
  }

  get isVerifierRemoved(): boolean {
    return this._isVerifierRemoved;
  }

  get isRowModuleReplacementEnabled(): boolean {
    return this._isRowModuleReplacementEnabled;
  }

  get defaultRowModuleReplacementDeep(): number {
    return this._defaultRowModuleReplacementDeep;
  }

  get isCancelButtonEnabled(): boolean {
    return this._isCancelButtonEnabled;
  }

  get isRightPanelHidden(): boolean {
    return true;
  }

  get isSaveButtonEnabled(): boolean {
    return this._isSaveButtonEnabled;
  }
}
