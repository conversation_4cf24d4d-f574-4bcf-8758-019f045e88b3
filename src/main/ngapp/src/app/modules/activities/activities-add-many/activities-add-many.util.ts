import { FieldType } from '@/core/field-list/field-list.enums';
import { randomUUID } from '@/core/utils/crypto-utils';
import * as DateUtil from '@/core/utils/date-util';
import { Deferred } from '@/core/utils/deferred';
import { EnumUtil } from '@/core/utils/enum-util';
import { FeatureFlag, isFeatureAvailable } from '@/core/utils/feature-util';
import * as NumberUtil from '@/core/utils/number-util';
import { cloneObject, cloneWithShallowArrays, isObject } from '@/core/utils/object';
import { sortArrayByAttribute } from '@/core/utils/string-util';
import { AddManyAction, RowAction } from '@/shared/activities/activities-add-many.enums';
import type { ActivityRow, DefaultRules, DisabledDatesHolder, GroupRow } from '@/shared/activities/activities-add-many.interfaces';
import type { ActivityCatalogs, ActivityConstraints, BusinessUnitDepartmentEntity } from '@/shared/activities/core/activities-core.interfaces';
import { DateRangeType } from '@infragistics/igniteui-angular';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FieldHandlerDataBase } from 'src/app/core/dynamic-field/field-handler/field-handler.interfaces';
import type { FieldDefinition, IFieldDefinition } from 'src/app/core/field-list/field-list.interfaces';
import { CommonAction } from 'src/app/core/utils/enums';
import type { TextHasValue } from 'src/app/core/utils/text-has-value';
import { FavoriteTaskType, Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ActivityStatus, FillType } from 'src/app/shared/activities/core/activities-core.enums';
import type { ActivityEntity } from 'src/app/shared/activities/core/utils/activity-base.interfaces';
import { type ActivitiesFieldName, DEFAULT_FIELD_DEFINITION, updateCatalogTaskData } from 'src/app/shared/activities/core/utils/activity-util';
import type { FavoriteTaskSaveDTO, QuickAccessHolder } from './../../menu/menu-main/menu-main.interfaces';
import type { MenuService } from './../../menu/services/menu.service';

export const DEFAULT_HEADER: ActivitiesFieldName[] = ['groupName', 'typeId', 'businessUnitDepartmentId'];

export const INVALID_HIDDEN_MENU_OPTIONS: string[] = ['businessUnitId', 'belongSeries', 'documents', 'dynamicFields', 'descriptionFields', 'startDate'];

export const INVALID_HIDDEN_FIELDS: string[] = ['businessUnitId', 'belongSeries', 'documents', 'dynamicFields', 'descriptionFields'];

// Se eliminan variables que no llegan al DTO, El único propositos de esto es el DEBUG
export const INVALID_ROW_DTO_FIELDS: string[] = [
  'busy',
  'catalog',
  'constraints',
  'defaultValues',
  'error',
  'hiddenFieldsDropdownOpen',
  'invalidFields',
  'conditionalDictionary',
  'isActiveActionItem',
  'isActiveNewItem',
  'parentRow', // <-- Referencia circular
  'virtualized', // <-- Referencia circular
  'virtualizedIndex', // <-- Referencia circular
  'forDirective', // <-- Referencia circular
  'saved',
  'selectedActionItem',
  'selectedNewItem',
  'valid'
];

export const DEFAULT_HIDDEN_MENU_OPTIONS: DropdownMenuItem[] = [
  {
    text: 'hiddenFields',
    value: AddManyAction.HEADER,
    header: true
  }
];

export const DEFAULT_MENU_OPTIONS: DropdownMenuItem[] = [
  {
    text: 'Acciones',
    value: AddManyAction.HEADER,
    header: true,
    hidden: true
  },
  {
    text: 'Agregar actividad',
    value: CommonAction.ADD,
    iconName: 'add'
  },
  {
    text: 'Agregar grupo',
    value: RowAction.ADD_GROUP,
    iconName: 'add'
  },
  {
    text: 'Guardar',
    value: CommonAction.SAVE,
    iconName: 'save'
  },
  {
    text: 'Eliminar',
    value: CommonAction.DELETE,
    iconName: 'delete'
  },
  {
    text: 'Duplicar',
    value: CommonAction.COPY,
    iconName: 'file_copy'
  },
  {
    text: 'Modificar encabezado',
    value: AddManyAction.HEADER_MODIFY,
    header: true,
    hidden: true
  },
  {
    text: 'Responsable',
    value: AddManyAction.HEADER_IMPLEMENTER,
    header: false,
    hidden: true
  },
  {
    text: 'Verificador',
    value: AddManyAction.HEADER_VERIFIER,
    header: false,
    hidden: true
  },
  {
    text: 'Fecha de inicio',
    value: AddManyAction.HEADER_START_DATE,
    header: false,
    hidden: true
  },
  {
    text: 'Fecha de implementación',
    value: AddManyAction.HEADER_END_DATE,
    header: false,
    hidden: true
  },
  {
    text: 'Fecha de verificación',
    value: AddManyAction.HEADER_VERIFICATION_DATE,
    header: false,
    hidden: true
  },
  {
    text: 'Modo de llenado',
    value: AddManyAction.HEADER_FILL_TYPE,
    header: false,
    hidden: true
  },
  {
    text: 'Formulario a llenar',
    value: AddManyAction.HEADER_FILL_FORM,
    header: false,
    hidden: true
  },
  {
    text: 'Departamento',
    value: AddManyAction.HEADER_BUSINESS_UNIT_DEPARTMENT_ID,
    header: false,
    hidden: true
  },
  {
    text: 'Objetivo',
    value: AddManyAction.HEADER_OBJECTIVE_ID,
    header: false,
    hidden: true
  },
  {
    text: 'Categoría',
    value: AddManyAction.HEADER_CATEGORY_ID,
    header: false,
    hidden: true
  },
  {
    text: 'Es planeada',
    value: AddManyAction.HEADER_IS_PLANNED,
    header: false,
    hidden: true
  },
  {
    text: 'Horas estimadas',
    value: AddManyAction.HEADER_PLANNED_HOURS,
    header: false,
    hidden: true
  },
  {
    text: 'Fuente',
    value: AddManyAction.HEADER_SOURCE,
    header: false,
    hidden: true
  },
  {
    text: 'Prioridad',
    value: AddManyAction.HEADER_PRIORITY,
    header: false,
    hidden: true
  },
  {
    text: 'Día(s) para verificar',
    value: AddManyAction.HEADER_DAYS_TO_VERIFY,
    header: false,
    hidden: true
  }
];

export const FILED_CATALOGS: FieldDefinition[] = [
  DEFAULT_FIELD_DEFINITION.typeId,
  DEFAULT_FIELD_DEFINITION.implementer,
  DEFAULT_FIELD_DEFINITION.verifier,
  DEFAULT_FIELD_DEFINITION.fillType,
  DEFAULT_FIELD_DEFINITION.fillForm,
  DEFAULT_FIELD_DEFINITION.businessUnitDepartmentId,
  DEFAULT_FIELD_DEFINITION.plannerTask,
  DEFAULT_FIELD_DEFINITION.objectiveId,
  DEFAULT_FIELD_DEFINITION.categoryId,
  DEFAULT_FIELD_DEFINITION.source,
  DEFAULT_FIELD_DEFINITION.priority,
  DEFAULT_FIELD_DEFINITION.taskDeliveryTypeId,
  DEFAULT_FIELD_DEFINITION.taskCategoryId,
  DEFAULT_FIELD_DEFINITION.activityResolutionId,
  DEFAULT_FIELD_DEFINITION.preImplementer
];

export const DEFAULT_FIELD_DEFINITION_ARRAY: FieldDefinition[] = [
  DEFAULT_FIELD_DEFINITION.implementer,
  DEFAULT_FIELD_DEFINITION.preImplementer,
  DEFAULT_FIELD_DEFINITION.verifier,
  DEFAULT_FIELD_DEFINITION.startDate,
  DEFAULT_FIELD_DEFINITION.implementation,
  DEFAULT_FIELD_DEFINITION.implementationPeriodicity,
  DEFAULT_FIELD_DEFINITION.verification,
  DEFAULT_FIELD_DEFINITION.verificationPeriodicity,
  DEFAULT_FIELD_DEFINITION.daysToVerify,
  DEFAULT_FIELD_DEFINITION.fillType,
  DEFAULT_FIELD_DEFINITION.fillForm,
  DEFAULT_FIELD_DEFINITION.businessUnitDepartmentId,
  DEFAULT_FIELD_DEFINITION.plannerTask,
  DEFAULT_FIELD_DEFINITION.systemLinks,
  DEFAULT_FIELD_DEFINITION.objectiveId,
  DEFAULT_FIELD_DEFINITION.isPlanned,
  DEFAULT_FIELD_DEFINITION.plannedHours,
  DEFAULT_FIELD_DEFINITION.categoryId,
  DEFAULT_FIELD_DEFINITION.source,
  DEFAULT_FIELD_DEFINITION.priority,
  DEFAULT_FIELD_DEFINITION.enableDeliverySetUp,
  DEFAULT_FIELD_DEFINITION.taskDeliveryTypeId,
  DEFAULT_FIELD_DEFINITION.taskCategoryId,
  DEFAULT_FIELD_DEFINITION.activityOrder
];

export let currentColorIndex = 0;
/**
 * Paleta extraída de todos los colores disponibles de Material Design
 *
 */
export const palette = [
  '#009688', // azul verde
  '#4CAF50', // verde
  '#8BC34A', // verde lima
  '#3F51B5', // azul oscuro
  '#03A9F4', // azul cielo
  '#607D8B', // azul gris
  '#2196F3', // azul
  '#673AB7', // morado oscuro
  '#9C27B0', // morado
  '#FF9800', // naranja
  '#FF5722', // naranja intenso
  '#F44336', // naranja rally
  '#E91E63', // magenta
  '#FFC107', // mostaza
  '#795548', // café
  '#9E9E9E' // gris
];

export function getUuid(): string {
  return randomUUID();
}

export function getNewColor() {
  if (!palette[currentColorIndex]) {
    currentColorIndex = 0;
  }
  return palette[currentColorIndex++];
}

export function catalogBkName(field: FieldDefinition) {
  return `bk${field.catalogName.replace(/^(.)/, (str) => str.toUpperCase())}`;
}

export function getDefaultFieldDefinitionArray(): FieldDefinition[] {
  return cloneObject(DEFAULT_FIELD_DEFINITION_ARRAY);
}

export function setDisabledDate(dateHolder: DisabledDatesHolder, type: DateRangeType, value: number | Date, arrays: string | string[], defaultValue?: Date): void {
  if ((value === null || typeof value === 'undefined') && typeof defaultValue === 'undefined') {
    return;
  }
  if (value === null || typeof value === 'undefined') {
    value = defaultValue;
  }
  if (NumberUtil.isInteger(value)) {
    value = new Date(value);
  }
  if (typeof arrays === 'string') {
    dateHolder[arrays].push({
      type: type,
      dateRange: [value]
    });
  } else {
    for (const arrayName of arrays) {
      dateHolder[arrayName].push({
        type: type,
        dateRange: [value]
      });
    }
  }
}

export function cloneConstraintDates(originalHolder: DisabledDatesHolder, cloneHolder: DisabledDatesHolder) {
  if (originalHolder.startDisabledDates) {
    cloneHolder.startDisabledDates = cloneObject(originalHolder.startDisabledDates);
  } else {
    cloneHolder.startDisabledDates = [];
  }
  if (originalHolder.implementationDisabledDates) {
    cloneHolder.implementationDisabledDates = cloneObject(originalHolder.implementationDisabledDates);
  } else {
    cloneHolder.implementationDisabledDates = [];
  }
  if (originalHolder.implementationPeriodicityDisabledDates) {
    cloneHolder.implementationPeriodicityDisabledDates = cloneObject(originalHolder.implementationPeriodicityDisabledDates);
  } else {
    cloneHolder.implementationPeriodicityDisabledDates = [];
  }
  if (originalHolder.verificationDisabledDates) {
    cloneHolder.verificationDisabledDates = cloneObject(originalHolder.verificationDisabledDates);
  } else {
    cloneHolder.verificationDisabledDates = [];
  }
  if (originalHolder.verificationPeriodicityDisabledDates) {
    cloneHolder.verificationPeriodicityDisabledDates = cloneObject(cloneHolder.verificationPeriodicityDisabledDates);
  } else {
    cloneHolder.verificationPeriodicityDisabledDates = [];
  }
}

export function setConstraintDates(constraints: ActivityConstraints, dateHolder: DisabledDatesHolder, setTodayStartDate?: boolean, plannerDate?: Date) {
  let date: Date;
  if (plannerDate) {
    date = plannerDate;
  } else {
    date = DateUtil.today();
  }
  // Si inicializa todo vacio porque calcula todas las fechas desabilitadas nuevamente.
  dateHolder.startDisabledDates = [];
  dateHolder.implementationDisabledDates = [];
  dateHolder.implementationPeriodicityDisabledDates = [];
  dateHolder.verificationDisabledDates = [];
  dateHolder.verificationPeriodicityDisabledDates = [];

  // startDisabledDates
  // Se deshabilitan todas las fechas antes del día actual.
  if (setTodayStartDate) {
    setDisabledDate(dateHolder, DateRangeType.Before, date, 'startDisabledDates');
  } else {
    // Se deshabilitan todas las fechas antes hace dos año.
    const d = plannerDate || DateUtil.addYearsToNow(-2);
    setDisabledDate(dateHolder, DateRangeType.Before, d, 'startDisabledDates');
  }

  // Si existe fecha de inicio se deshabilitan las fechas menores para la fecha de implementación y verificación.
  setDisabledDate(dateHolder, DateRangeType.Before, dateHolder.temporary.startDate, ['implementationDisabledDates', 'verificationDisabledDates'], date);

  // Si existe fecha de verificacion se desahilitan las fechas mayores para implentación.
  if (constraints?.maxConstraint && dateHolder.activity.belongSeries && dateHolder.activity.verificationPeriodicity) {
    setDisabledDate(dateHolder, DateRangeType.After, dateHolder.activity.verificationPeriodicity.nextDateStart, 'implementationDisabledDates');
  }
  // Si existen valores por defecto para la fecha minima de implentacion de deshabilitan las fechas menores a esta.
  setDisabledDate(
    dateHolder,
    DateRangeType.Before,
    constraints?.minPlannedImplementation,
    ['implementationDisabledDates', 'implementationPeriodicityDisabledDates'],
    date
  );

  // Si existen valores por defecto para la fecha minima de verificacion de deshabilitan las fechas menores a esta.
  setDisabledDate(dateHolder, DateRangeType.Before, constraints?.minPlannedVerification, ['verificationDisabledDates', 'verificationPeriodicityDisabledDates']);
  // Si existe fecha de implementacion se deshabilitan las fechas menores a esta para la fecha de verificación.
  if (dateHolder.activity.belongSeries && dateHolder.activity.implementationPeriodicity) {
    setDisabledDate(dateHolder, DateRangeType.Before, dateHolder.activity.implementationPeriodicity.nextDateStart, 'verificationPeriodicityDisabledDates', date);
  } else {
    setDisabledDate(dateHolder, DateRangeType.Before, dateHolder.activity.implementation, 'verificationDisabledDates', date);
  }
  // Si existen fecha maxima por defecto para verificación se dehabilitan las fechas mayores a esta.
  setDisabledDate(dateHolder, DateRangeType.After, constraints?.maxPlannedVerification, ['verificationDisabledDates', 'verificationPeriodicityDisabledDates']);
  // Si es desde proyectos toma en cuenta la fecha de termino del proyecto
  setDisabledDate(dateHolder, DateRangeType.After, constraints?.plannerVerificationDate, [
    'startDisabledDates',
    'implementationDisabledDates',
    'implementationPeriodicityDisabledDates',
    'verificationDisabledDates',
    'verificationPeriodicityDisabledDates'
  ]);
}

export function setCatalogValues(catalogRow: ActivityRow, ds: ActivityCatalogs, lang: string, autoSort = false, loadTaskData = false): void {
  for (const field of FILED_CATALOGS) {
    if (field.catalogNames) {
      for (const catalogName of field.catalogNames) {
        catalogRow.catalog[catalogName] = ds[catalogName] || [];
        if (autoSort) {
          sortArrayByAttribute(catalogRow.catalog[catalogName], 'text', lang); // ToDo: El componente de "select" debe ordenar los combos
        }
        if (field.catalogBk) {
          catalogRow.catalog[`bk${catalogName}`] = catalogRow.catalog[catalogName];
        }
      }
    } else {
      if (!field.catalogName || !catalogRow.catalog.hasOwnProperty(field.catalogName) || !ds.hasOwnProperty(field.catalogName)) {
        continue;
      }
      catalogRow.catalog[field.catalogName] = ds[field.catalogName] || [];
      if (autoSort) {
        sortArrayByAttribute(catalogRow.catalog[field.catalogName], 'text', lang); // ToDo: El componente de "select" debe ordenar los combos
      }
      if (field.catalogBk && field.catalogName) {
        catalogRow.catalog[catalogBkName(field)] = catalogRow.catalog[field.catalogName];
      }
    }
  }
  if (loadTaskData) {
    const catalog = catalogRow.catalog;
    updateCatalogTaskData(catalog.clients, catalog.planners, catalog.tasks);
  }
}

export function getEmptyActivity(): ActivityEntity {
  const a: ActivityEntity = {
    anticipationAttendDays: null,
    activityOrder: null,
    activityResolutionId: null,
    businessUnitDepartmentId: null,
    businessUnitId: null,
    childs: null,
    code: null,
    daysToVerify: null,
    description: null,
    dynamicFieldData: null,
    dynamicTableName: null,
    dynamicTableNameId: null,
    fillType: FillType.PROGRESS,
    fillForm: null,
    finishImplementationOn: null,
    systemLinks: null,
    finishVerificationOn: null,
    implementation: null,
    implementationPeriodicity: null,
    implementer: null,
    preImplementer: null,
    linked: null,
    nextImplementationDate: null,
    nextVerificationDate: null,
    objectiveId: null,
    isPlanned: true,
    followUpImplementationDelay: true,
    mustUpdateImplementationAtReturn: true,
    plannedHours: null,
    priority: null,
    plannerTask: null,
    categoryId: null,
    source: null,
    belongSeries: 0,
    startImplementationOn: null,
    startVerificationOn: null,
    status: EnumUtil.getValue(ActivityStatus, ActivityStatus.REPORTED),
    typeId: null,
    verificationPeriodicity: null,
    verifier: null,
    verification: null,
    enableDeliverySetUp: false,
    taskDeliveryTypeId: null,
    taskCategoryId: null,
    actualHours: null
  };
  return a;
}

export function getActivityRowInstance(dropdowns = true): ActivityRow {
  return getRowInstance(undefined, dropdowns) as ActivityRow;
}

export function getRowInstance(root: DefaultRules, isGroup?: boolean, dropdowns = true): ActivityRow | GroupRow {
  const row: ActivityRow | GroupRow = {
    actionItems: [],
    activity: getEmptyActivity(),
    busy: false,
    catalog: null,
    childColor: null,
    commitmentTask: null,
    constraints: null,
    defaultValues: null,
    deleted: false,
    disabledFields: null,
    error: null,
    hasChild: false,
    conditionalDictionary: null,
    hiddenFieldsDropdownOpen: false,
    hiddenMenuAvailableOptions: [],
    hiddenMenuOptions: [],
    hiddenRules: null,
    implementationDisabledDates: [],
    implementationPeriodicityDisabledDates: [],
    invalidFields: [],
    index: 0,
    isActiveActionItem: false,
    isActiveNewItem: false,
    isGroup: false,
    isMainParent: false,
    isVariableDroppable: {},
    level: 0,
    linkedData: { comments: [], documents: [], files: [] },
    parentRow: null,
    pendingFocusInvalidFields: false,
    rowCount: 0,
    rowId: getUuid(),
    rows: [],
    saved: false,
    shouldBeSaved: false,
    collapsed: false,
    selectedActionItem: RowAction.DELETE_ROW,
    selectedNewItem: RowAction.ADD_ROW,
    showParticipants: false,
    showComments: false,
    showDocuments: false,
    showFiles: false,
    startDisabledDates: [],
    temporary: { startDate: null, fileNames: [], participants: [], dynamicValidator: null },
    uniqueColor: getNewColor(),
    valid: false,
    variables: {},
    verificationDisabledDates: [],
    verificationPeriodicityDisabledDates: [],
    showRowActions: false
  };
  let result = row;
  if (isGroup) {
    const group: GroupRow = row as GroupRow;
    group.color = getNewColor();
    group.customGroupName = false;
    group.defaultGroupName = null;
    group.header = [...DEFAULT_HEADER];
    group.hiddenMenuAvailableOptions = [AddManyAction.HEADER];
    group.isGroup = true;
    group.markRequiredFields = false;
    group.menuOptions = cloneObject(DEFAULT_MENU_OPTIONS);
    group.menuAvailableOptions = [AddManyAction.HEADER, CommonAction.ADD, RowAction.ADD_GROUP, AddManyAction.HEADER_MODIFY];
    if (!isFeatureAvailable(FeatureFlag.ACTIVITY_ADD_GROUP)) {
      // ToDo: Habilitar grupos de actividades, actualmente se deshabilita ya que se encuentra inestable
      group.menuOptions = group.menuOptions.filter((drop) => drop.value !== RowAction.ADD_GROUP && drop.value !== CommonAction.DELETE);
      group.menuAvailableOptions = group.menuAvailableOptions.filter((value) => value !== RowAction.ADD_GROUP && value !== CommonAction.DELETE);
    }
    // DEFAULT VALUES FOR: "catalogs", "hiddenRules", "hiddenMenuOptions" and "defaultValues"
    setRowDefaultRules(root, group);
    result = group;
  }
  if (!dropdowns) {
    result.hiddenMenuOptions = null;
    result.actionItems = null;
    if (isGroup) {
      (result as GroupRow).menuOptions = null;
    }
  }
  return result;
}

export function setRowDefaultRules(root: DefaultRules, row: ActivityRow) {
  if (row.catalog === null || typeof row.catalog === 'undefined') {
    row.catalog = cloneWithShallowArrays(root.defaultCatalog);
  }
  if (row.defaultValues === null || typeof row.defaultValues === 'undefined') {
    row.defaultValues = cloneObject(root.defaultValues);
  }
  if (row.hiddenMenuOptions === null || typeof row.hiddenMenuOptions === 'undefined' || row.hiddenMenuOptions.length === 0) {
    row.hiddenMenuOptions = cloneObject(root.defaultHiddenMenuOptions);
  }
  if (row.hiddenRules === null || typeof row.hiddenRules === 'undefined') {
    row.hiddenRules = cloneObject(root.defaultHiddenRules);
  }
  if (row.disabledFields === null || typeof row.disabledFields === 'undefined') {
    row.disabledFields = cloneObject(root.defaultDisabledFields);
  }
}

export function getGroupInstance(root: DefaultRules, dynamicFieldsDb: FieldHandlerDataBase, lang: string, dropdowns = true): GroupRow {
  const group: GroupRow = getRowInstance(root, true, dropdowns) as GroupRow;
  // Se agrega ID del grupo
  group.activity.groupId = getUuid();
  if (dropdowns) {
    for (let i = 0; i < group.menuOptions.length; i++) {
      group.menuOptions[i] = { ...group.menuOptions[i] };
    }
    defineHiddenFields(root, group, dynamicFieldsDb, lang);
  }
  return { ...group };
}

export function getFieldDefinition(field: IFieldDefinition | string): FieldDefinition {
  const isFieldDefinition: boolean = field && typeof field !== 'string';
  if (isFieldDefinition) {
    return field as FieldDefinition;
  }
  const fieldName: string = isFieldDefinition ? (field as IFieldDefinition).name : (field as string);
  const fieldDef = DEFAULT_FIELD_DEFINITION[fieldName];
  if (fieldDef) {
    return fieldDef;
  }

  for (const key in DEFAULT_FIELD_DEFINITION) {
    if (!DEFAULT_FIELD_DEFINITION.hasOwnProperty(key)) {
      continue;
    }
    const defaultField = DEFAULT_FIELD_DEFINITION[key];
    if (defaultField.saveFieldName === fieldName) {
      return defaultField;
    }
  }
  return null;
}

export function skipRowFieldByRecurrence(row: ActivityRow, field: IFieldDefinition | string) {
  return skipFieldByRecurrence(field, row?.activity?.belongSeries, !!row.parentRow?.rowId);
}

export function skipFieldByRecurrence(field: IFieldDefinition | string, belongSeries: number, isSubtask: boolean) {
  if (field === null || typeof field === 'undefined') {
    return false;
  }
  const fieldName: string = typeof field === 'string' ? (field as string) : (field as IFieldDefinition).name;
  if (INVALID_HIDDEN_FIELDS.indexOf(fieldName) !== -1) {
    return false;
  }
  const fieldDef = getFieldDefinition(field);
  if (!fieldDef || !fieldDef.configurableByRecurrence) {
    return false;
  }
  if (isSubtask && belongSeries === 1) {
    return true;
  }
  return (fieldDef.hiddenByRecurrence && belongSeries === 1) || (!fieldDef.hiddenByRecurrence && belongSeries !== 1);
}

/**
 * @deprecated: HiddenRules debe llenarse desde backend, de otra manera se presentarán inconsistencias con
 * la pantalla de alta de actividad avanzada y la de detalle.
 *
 * @param root
 * @param row
 * @param dynamicFieldsDb
 * @param lang
 */
export function defineHiddenFields(root: DefaultRules, row: ActivityRow, dynamicFieldsDb: FieldHandlerDataBase, lang: string): void {
  const headers = row.hiddenMenuOptions.filter((option) => option.header) || [];
  row.hiddenMenuOptions = [];
  for (const fieldName in row.hiddenRules) {
    if (
      row.disabledFields[fieldName] ||
      !row.hiddenRules.hasOwnProperty(fieldName) ||
      !DEFAULT_FIELD_DEFINITION[fieldName] ||
      dynamicFieldsDb.dynamicFieldsByName?.[fieldName] ||
      INVALID_HIDDEN_MENU_OPTIONS.indexOf(fieldName) !== -1 ||
      skipRowFieldByRecurrence(row, fieldName)
    ) {
      continue;
    }
    row.hiddenMenuOptions.push({
      text: root.fieldLabels[fieldName] || fieldName,
      value: fieldName
    });
    if (!validHiddenCatalogs(fieldName, row)) {
      continue;
    }
    row.hiddenMenuAvailableOptions.push(fieldName);
  }
  if (dynamicFieldsDb.availableDynamicFields && dynamicFieldsDb.dynamicFieldsById) {
    for (const dynamicField of dynamicFieldsDb.availableDynamicFields) {
      const field = dynamicFieldsDb.dynamicFieldsById[dynamicField];
      let isHidden = false;
      if (row.hiddenRules && (row.hiddenRules[field.name] === null || typeof row.hiddenRules[field.name] === 'undefined')) {
        const hiddenDynamicFields = row.hiddenRules.dynamicFields || [];
        isHidden = hiddenDynamicFields.indexOf(dynamicField) === -1;
        row.hiddenRules[field.name] = isHidden;
      } else if (row.hiddenRules) {
        isHidden = row.hiddenRules[field.name];
      }
      if (field.id) {
        row.hiddenMenuOptions.push({
          text: field.label,
          value: field.name,
          selected: isHidden
        });
        row.hiddenMenuAvailableOptions.push(field.name);
      }
    }
  }
  sortArrayByAttribute(row.hiddenMenuOptions, 'text', lang);
  row.hiddenMenuOptions = headers.concat(row.hiddenMenuOptions);
}

export function validHiddenCatalogs(fieldName: string, group: GroupRow | ActivityRow) {
  const fieldDefinition = DEFAULT_FIELD_DEFINITION[fieldName];
  return !(fieldDefinition && fieldDefinition.fieldType === FieldType.DROPDOWN_SEARCH && group.catalog[fieldDefinition.catalogName]?.length === 0);
}

export function fixFieldCatalogFromActivity(
  row: ActivityRow,
  parentRow: ActivityRow,
  catalog: ActivityCatalogs,
  fieldName: string,
  catalogName: string,
  fieldActivityName: string
) {
  if (!catalog || (!catalogName && fieldName !== 'typeId')) {
    const fieldDefinition = DEFAULT_FIELD_DEFINITION[fieldName];
    if (fieldDefinition && fieldDefinition.fieldType === FieldType.PLANNER_TASK) {
      return;
    }
    if (catalog) {
      console.error('Invalid template catalog at field: ', fieldName);
    } else {
      console.error('Not defined template catalog at field: ', fieldName);
    }
    return;
  }
  const value = row.activity[fieldName] || parentRow?.activity[fieldName]; // los grupos no tienen parent row
  if (value !== null && typeof value !== 'undefined' && catalog[catalogName]) {
    if (catalog[catalogName].find((t) => t.value === value)) {
      return;
    }
    if (fieldName === 'fillForm') {
      const fillForm = row.activity.fillForm || parentRow.activity.fillForm;
      catalog[catalogName].push(fillForm);
      return;
    }
    let item: TextHasValue;
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' || DateUtil.isDate(value) || Array.isArray(value)) {
      const text = row.activity[fieldActivityName] || parentRow.activity[fieldActivityName] || 'Missing';
      item = {
        text: text,
        value: value
      };
    } else {
      item = value;
    }
    if (fieldName === 'businessUnitDepartmentId') {
      const department = item as BusinessUnitDepartmentEntity;
      department.businessUnitId = row.activity.businessUnitId || parentRow.activity.businessUnitId;
    }
    catalog[catalogName].push(item);
  }
}

export function ensureValueExistsInCatalogs(row: ActivityRow, catalog: ActivityCatalogs) {
  for (const field of FILED_CATALOGS) {
    const fieldCatalog = catalog[field.catalogName];
    if (!fieldCatalog) {
      continue;
    }
    const fieldName = field.name;
    if (fieldName === 'implementer') {
      if (row?.temporary[fieldName]) {
        for (const key in row.temporary[fieldName]) {
          if (row.temporary[fieldName].hasOwnProperty(key)) {
            const value = +row.temporary[fieldName][key];
            if (!fieldCatalog.find((t: TextHasValue) => +t.value === value)) {
              if (row.defaultValues && row.defaultValues[fieldName] === value) {
                row.defaultValues[fieldName] = null;
              }
              delete row.temporary[fieldName][key];
            }
          }
        }
      }
    } else if (field.isTemporaryValue) {
      if (row?.temporary[fieldName] !== null && typeof row?.temporary[fieldName] !== 'undefined') {
        const value = +row.temporary[fieldName];
        if (!fieldCatalog.find((t: TextHasValue) => +t.value === value)) {
          if (row.defaultValues && row.defaultValues[fieldName] === value) {
            row.defaultValues[fieldName] = null;
          }
          row.temporary[fieldName] = null;
        }
      }
    } else {
      if (row?.activity[fieldName] !== null && typeof row?.activity[fieldName] !== 'undefined') {
        const value = row.activity[fieldName]; // <-- A veces son objetos
        if (isObject(value) && value.hasOwnProperty('value')) {
          if (!fieldCatalog.find((t: TextHasValue) => +t.value === +value.value)) {
            if (row.defaultValues && row.defaultValues[fieldName] === value) {
              row.defaultValues[fieldName] = null;
            }
            row.activity[fieldName] = null;
          }
        } else {
          if (!fieldCatalog.find((t: TextHasValue) => +t.value === +value)) {
            if (row.defaultValues && row.defaultValues[fieldName] === value) {
              row.defaultValues[fieldName] = null;
            }
            row.activity[fieldName] = null;
          }
        }
      }
    }
  }
}

export function getMimimumCatalogFromActivity(row: ActivityRow, parentRow: ActivityRow, keepPrevious = false) {
  let minimumCatalog: ActivityCatalogs;
  if (row.catalog && keepPrevious) {
    minimumCatalog = cloneWithShallowArrays(row.catalog);
  } else {
    minimumCatalog = {
      bkTasks: [],
      bkImplementers: [],
      bkVerifiers: [],
      businessUnitDepartments: [],
      businessUnits: [],
      fillForms: [],
      fillTypes: [],
      implementers: [],
      objectives: [],
      conditionalFields: [],
      clients: [],
      planners: [],
      priorities: [],
      sources: [],
      tasks: [],
      types: [],
      verifiers: [],
      taskDeliveryTypes: [],
      taskCategories: [],
      categories: []
    };
  }
  for (const field of FILED_CATALOGS) {
    if (field.catalogBk && field.catalogName) {
      fixFieldCatalogFromActivity(row, parentRow, minimumCatalog, field.name, catalogBkName(field), field.fieldActivityName);
    }
    fixFieldCatalogFromActivity(row, parentRow, minimumCatalog, field.name, field.catalogName, field.fieldActivityName);
  }
  return minimumCatalog;
}

export function setMininimumCatalog(catalog: ActivityCatalogs, row: ActivityRow): void {
  if (!row.catalog || !catalog) {
    return;
  }
  for (const field of FILED_CATALOGS) {
    if (!catalog[field.catalogName]?.length) {
      continue;
    }
    if (field.catalogBk && field.catalogName) {
      const bkName = catalogBkName(field);
      if (!row.catalog[bkName]?.length) {
        row.catalog[bkName] = catalog[bkName];
      }
    }
    if (!row.catalog[field.catalogName]?.length) {
      row.catalog[field.catalogName] = catalog[field.catalogName];
    }
  }
}

export function calculateDiffDays(startDate: Date | string, endDate: Date | string): number {
  if (!startDate || !endDate) {
    return null;
  }
  const diffTime = Math.abs(DateUtil.safe(endDate).getTime() - DateUtil.safe(startDate).getTime());
  return Math.ceil(diffTime / (1000 * 3600 * 24));
}

export function addTemplateQuickAccess(templateActivityTemplateId: number, holder: QuickAccessHolder, menuService: MenuService, description: string) {
  const saved = new Deferred<FavoriteTaskSaveDTO>();
  menuService.addQuickAccess({
    def: saved,
    icon: 'done',
    urlParams: `favUrl=2&templateActivityTemplateId=${templateActivityTemplateId}`,
    menuPath: 'menu/activities/add-many',
    type: FavoriteTaskType.ACTIVITY_TEMPLATE,
    infoMessage: 'Se agregará la plantilla a su menú de favoritos',
    templateActivityTemplateId: templateActivityTemplateId,
    moduleName: String(Module.ACTIVITY).toLowerCase(),
    taskName: description
  });
  saved.promise.then((result) => {
    if (result.success) {
      holder.isFavorite = 1;
      holder.favoriteTaskId = result.id;
    }
  });
}
