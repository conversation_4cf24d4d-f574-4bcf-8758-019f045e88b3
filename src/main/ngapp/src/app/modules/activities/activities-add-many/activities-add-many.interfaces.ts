export interface ReportedActivities {
  success: boolean;
  recurrenceId: number;
  parentTreeActivityId: number;
  implementationId: number;
  implementationCode: string;
  verificationId: number;
  verificationCode: string;
  description?: string;
  code?: string;
  childs?: ReportedActivities[];
  commitmentTask: number;
  replacingOldVerification: boolean;
}

export enum RedirectAction {
  CREATE = 'CREATE',
  CONTROL = 'CONTROL',
  MY_ACTIVITIES = 'MY_ACTIVITIES',
  PLANNING = 'PLANNING',
  PENDINGS = 'PENDINGS'
}
