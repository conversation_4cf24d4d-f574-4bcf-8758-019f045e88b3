import { BnextCoreComponent } from '@/core/bnext-core.component';
import * as GridUtil from '@/core/grid/utils/grid-util';
import { AppService } from '@/core/services/app.service';
import { type AfterViewInit, Component, Input, type OnInit, SecurityContext, inject, viewChild } from '@angular/core';
import {
  DefaultSortingStrategy,
  type IGroupingExpression,
  IgxDialogComponent,
  IgxNumberSummaryOperand,
  type IgxSummaryResult,
  SortingDirection
} from '@infragistics/igniteui-angular';

import { GridComponent } from '@/core/grid/grid.component';
import {
  AvatarColumn,
  DateColumn,
  DoubleColumn,
  DropDownColumn,
  HtmlColumn,
  IntegerColumn,
  LinkColumn,
  MultipleEntityColumn,
  ObjectListColumn,
  PercentageColumn,
  type RenderizableColumn,
  type RenderizableSummary,
  TextColumn,
  TimeColumn,
  YesNoColumn
} from '@/core/grid/utils/grid.interfaces';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { Session } from '@/core/local-storage/session';
import * as DateUtil from '@/core/utils/date-util';
import { DateInterval } from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';
import { cloneObject, equalsObject } from '@/core/utils/object';
import { ActivityStatus } from '@/shared/activities/core/activities-core.enums';
import { getActivityController } from '@/shared/activities/core/utils/activity-util';
import { DomSanitizer } from '@angular/platform-browser';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { GridDropDownItem, GridIcon } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { GridCellType } from 'src/app/core/grid/utils/grid-cell-type';
import { GridAvgSummary, type GridColumn } from 'src/app/core/grid/utils/grid-column';
import { type DisplayDensity, EnumDisplayDensity } from 'src/app/core/utils/display-density';
import { CommonAction } from 'src/app/core/utils/enums';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ActivityCoreUtil } from 'src/app/shared/activities/core/activities-core.utils';
import { ActivitiesCommitmentsSummaryComponent } from '../activities-commitments-summary/activities-commitments-summary.component';
import { ActivitiesDashboardComponent } from '../activities-dashboard/activities-dashboard.component';
import { CommitmentStatus } from '../activities-dashboard/activities-dashboard.enums';
import { ActivitiesTreeScoreComponent } from '../activities-tree-score/activities-tree-score.component';
import type { IActivitiesScoreQueryRow, IActivitiesScoreQuerySubRow, IActivitiesScoreWeekIndex } from './activities-score-query.interfaces';

export class GridScoreSummary extends IgxNumberSummaryOperand implements IgxNumberSummaryOperand {
  static label = 'Cumplimiento';
  public operate(_scores: number[] = [], rows: IActivitiesScoreQueryRow[] = [], _fieldName?: string): IgxSummaryResult[] {
    /**
     * Cumplimiento = SUMA(Horas de registros no cancelados * progress) / SUMA(Horas de registros planeados)
     **/
    const result = [];
    let numerator: number;
    let denominator: number;
    let key: string;
    if (rows?.length) {
      numerator = rows
        .filter((row: IActivitiesScoreQueryRow) => row.activityStatus !== ActivityStatus.NOT_APPLY && row.activityStatus !== ActivityStatus.NOT_APPLY_VERIFIED)
        .map((row: IActivitiesScoreQueryRow) => row.sumEstimatedHours * row.progress)
        .reduce((a, b) => +a + +b, 0);
      denominator = rows
        .filter((row: IActivitiesScoreQueryRow) => row.activityIsPlanned === true)
        .map((row: IActivitiesScoreQueryRow) => row.sumEstimatedHours)
        .reduce((a, b) => +a + +b, 0);
      key = rows.map((row: IActivitiesScoreQueryRow) => `${row.activityCommitmentWeek}:${row.implementerId}`).join(',');
    } else {
      numerator = 0.0;
      denominator = 1.0;
      key = '';
    }
    result.push({
      key: key,
      label: GridScoreSummary.label,
      summaryResult: denominator ? NumberUtil.round(numerator / (denominator || 1), 2) : 'NA'
    });
    return result;
  }
}
export interface IDetailRangeDay {
  start: Date;
  end: Date;
  column: GridColumn<IActivitiesScoreQueryRow>;
  cell: GridCellType<IActivitiesScoreQueryRow>;
}
// activities/weekly-score/:interval
@Component({
  selector: 'app-activities-score-query',
  templateUrl: './activities-score-query.component.html',
  styleUrls: ['./activities-score-query.component.scss'],
  imports: [GridComponent, IgxDialogComponent, ActivitiesTreeScoreComponent, BnextTranslatePipe]
})
export class ActivitiesScoreQueryComponent extends BnextCoreComponent implements OnInit, AfterViewInit {
  sanitizer = inject(DomSanitizer);

  api = inject(AppService);

  service = inject(AppService);

  override get componentPath(): string {
    // Se reutilizan las etiquetas del reporte de resumen de compromisos
    return ActivitiesCommitmentsSummaryComponent.LANG_CONFIG.componentPath;
  }

  override get tagName(): string {
    // Se reutilizan las etiquetas del reporte de resumen de compromisos
    return ActivitiesCommitmentsSummaryComponent.LANG_CONFIG.componentName;
  }

  private _defaultGroupingExpressions: IGroupingExpression[] = [
    { fieldName: 'activityCommitmentWeek', dir: SortingDirection.Desc, ignoreCase: true, strategy: DefaultSortingStrategy.instance() },
    { fieldName: 'ownerName', dir: SortingDirection.Asc, ignoreCase: true, strategy: DefaultSortingStrategy.instance() }
  ];
  private _groupingExpressions: IGroupingExpression[] = cloneObject(this._defaultGroupingExpressions);
  private _columnDetailHours = 'detailHours'; // Columna del detalle de horas
  private _columnDetailProgress = 'detailProgress'; // Columna del detalle de avance

  columns: GridColumn<IActivitiesScoreQueryRow>[] = [];
  rangeSelectorColumns: GridColumn<IActivitiesScoreQueryRow>[] = [];
  rowHeight = '25px';

  readonly EnumDisplayDensity = EnumDisplayDensity;

  // TODO: Skipped for migration because:
  //  This input is inherited from a superclass, but the parent cannot be migrated.
  @Input()
  public override displayDensity: DisplayDensity = 'compact';

  historyColumns: GridColumn[];
  timesheetColumns: GridColumn[];
  historyDialogGridUrl = null;
  timesheetDialogGridUrl = null;
  history_rightButtonLabel = '';
  history_title = '';
  history_emptyGridHistory = '';
  timesheet_rightButtonLabel = '';
  timesheet_title = '';
  timesheet_emptyGridTimesheet = '';

  readonly grid = viewChild<GridComponent>('grid');

  readonly historyDialog = viewChild('historyDialog', { read: IgxDialogComponent });

  readonly timesheetDialog = viewChild('timesheetDialog', { read: IgxDialogComponent });

  readonly scoreDetailGrid = viewChild('scoreDetail', { read: GridComponent });

  readonly timesheetDetailGrid = viewChild('timesheetDetail', { read: GridComponent });

  readonly treeScoreDialog = viewChild('treeScoreDialog', { read: ActivitiesTreeScoreComponent });

  get urlModule(): string {
    return 'activities/activity';
  }

  url = `${this.urlModule}/score-query`;

  get groupingExpressions() {
    const grid = this.grid();
    if (grid?.isStateSaved === 'UNPERSISTED' || grid?.isStateSaved === 'UNSAVED') {
      return this._groupingExpressions;
    }
    return null;
  }

  getData(row: any): DropdownMenuItem[] {
    return [
      {
        text: 'Abrir detalle',
        value: CommonAction.EDIT,
        iconName: 'edit',
        modernHref: `activities/${row?.activityId}`,
        hidden: false
      }
    ];
  }

  ngOnInit(): void {
    super.ngOnInit();
    const statuses: GridIcon[] = ActivityCoreUtil.getLocalizedValuesList({
      translateService: this.translateService,
      $destroy: this.$destroy
    });
    const stages: GridIcon[] = ActivityCoreUtil.getLocalizedStageList({
      translateService: this.translateService,
      $destroy: this.$destroy
    });
    this.rangeSelectorColumns = [];
    GridUtil.columns(this.rangeSelectorColumns)
      .push(this._columnDetailHours, new DoubleColumn({ hasSummary: true, searchable: false, allowCellClick: true }), '138px')
      .push(
        this._columnDetailProgress,
        new PercentageColumn({
          hasSummary: true,
          searchable: false,
          allowCellClick: true,
          summaries: GridAvgSummary
        }),
        '138px'
      )
      .push(
        'verified',
        new HtmlColumn<IActivitiesScoreQueryRow>({
          searchable: false,
          width: '180px',
          renderCell: (cell: GridCellType<IActivitiesScoreQueryRow>) => {
            if (!this.isVerified(cell)) {
              return '';
            }
            return this.sanitizer.sanitize(SecurityContext.HTML, this.sanitizer.bypassSecurityTrustHtml('<span class="material-icons verified-column">verified</span>'));
          }
        })
      )
      .push(
        'activityStage',
        new ObjectListColumn({
          width: '138px',
          hidden: true,
          render: {
            valueKey: 'value',
            labelKey: 'label',
            items: stages
          }
        })
      )
      .push(
        'activityStatus',
        new ObjectListColumn({
          width: '138px',
          hidden: true,
          render: {
            valueKey: 'value',
            labelKey: 'label',
            items: statuses
          }
        })
      )
      .translate(this.$destroy, this.translateService, ActivitiesCommitmentsSummaryComponent.LANG_CONFIG, 'activities.column');
    this.historyColumns = [];
    GridUtil.columns(this.historyColumns)
      .push('activityDescription', new TextColumn({ width: '300px', groupable: false }))
      .push('progressActivity', new PercentageColumn({ width: '100px', groupable: false }))
      .push('historyDescription', new TextColumn({ width: '300px', groupable: false }))
      .push('authorDescription', new TextColumn({ width: '200px', groupable: false }))
      .push('historyDate', new DateColumn({ width: '150px', groupable: false }))
      .translate(this.$destroy, this.translateService, ActivitiesDashboardComponent.LANG_CONFIG, 'history');
    this.timesheetColumns = [];
    GridUtil.columns(this.timesheetColumns)
      .push('activityDescription', new TextColumn({ width: '300px', groupable: false }))
      .push('workedHours', new DoubleColumn({ decimalCount: 2, groupable: false, width: '210px', hasSummary: true }))
      .push('description', new TextColumn({ autoSize: true, groupable: false }))
      .push('timesheetStart', new TimeColumn({ width: '100px', dateFormat: 'HH:mm', groupable: false }))
      .push('timesheetEnd', new TimeColumn({ width: '100px', dateFormat: 'HH:mm', groupable: false }))
      .push('userDescription', new TextColumn({ width: '200px', groupable: false }))
      .push('clientDescription', new TextColumn({ width: '150px', groupable: false }))
      .push('plannerDescription', new TextColumn({ width: '150px', groupable: false }))
      .push('taskDescription', new TextColumn({ width: '150px', groupable: false }))
      .translate(this.$destroy, this.translateService, ActivitiesDashboardComponent.LANG_CONFIG, 'timesheet');
    this.cdr.detectChanges();
  }

  private buildGrid(): void {
    const statuses: GridIcon[] = ActivityCoreUtil.getLocalizedValuesList({
      translateService: this.translateService,
      $destroy: this.$destroy
    });
    this.columns = [];
    const menuOptions = (cell) => this.getData(cell?.row?.data);
    const activityUrl = getActivityController(Module.ACTIVITY);
    GridUtil.columns(this.columns)
      .push(
        'ownerName',
        new AvatarColumn({
          width: '138px',
          groupable: true,
          fixedFilter: true,
          avatarIdKey: 'implementerId',
          avatarNameKey: 'ownerName'
        })
      )
      .push(
        'delayed',
        new HtmlColumn<IActivitiesScoreQueryRow>({
          searchable: false,
          width: '180px',
          renderCell: (cell: GridCellType<IActivitiesScoreQueryRow>) => {
            if (!cell.row?.data) {
              return '';
            }
            const commitmentDate = DateUtil.safe(cell.row.data.activityCommitmentDate);
            const status = this.getCommitmentStatus(cell.row.data.activityStatus, commitmentDate, cell.row.data.activityAnticipationAttendDays);
            if (!commitmentDate || !status) {
              return '';
            }
            const value = `<div class="commitment--${status}">${this.getCommitmentStatusString(cell.row.data, commitmentDate, status)}</div>`;
            return this.sanitizer.sanitize(SecurityContext.HTML, this.sanitizer.bypassSecurityTrustHtml(value));
          }
        })
      )
      .push(
        'activityName',
        new LinkColumn({
          linkHref: `./${this.getLang()}/menu/activities/{activityId}`,
          linkParams: ['activityId'],
          width: '400px'
        })
      )
      .push(
        'action', // ToDo: Utilizar <igx-action-strip> https://www.infragistics.com/products/ignite-ui-angular/angular/components/treegrid/row-actions
        new DropDownColumn({
          actionCountLimit: 4,
          alwaysVisible: false,
          width: '71px',
          render: {
            menuOptions: menuOptions,
            rowIdentifierKey: 'activityId'
          }
        })
      )
      .push(
        'verifierName',
        new AvatarColumn({
          width: '138px',
          groupable: true,
          avatarIdKey: 'verifierId',
          avatarNameKey: 'verifierName',
          hidden: true
        })
      )
      .push('activityIsPlanned', new YesNoColumn({ boolean: true, fixedFilter: true, hidden: true }), '138px')
      .push('activityCode', new TextColumn({ width: '138px', fixedFilter: true, hidden: true }))
      .push('bussinessName', new TextColumn({ width: '138px', hidden: true }))
      .push(
        'bussinessDepartmentId',
        new MultipleEntityColumn({
          itemsController: `${activityUrl}/business-unit-departments`,
          defaultFilterValue: [Session.getBusinessUnitDepartmentId()],
          width: '138px',
          fixedFilter: true,
          hidden: true
        })
      )
      .push('categoryName', new TextColumn({ width: '138px', hidden: true }))
      .push('sourceName', new TextColumn({ width: '138px', hidden: true }))
      .push(
        'score',
        new DoubleColumn({
          width: '156px',
          textWhenEmpty: 'NA',
          searchable: false,
          allowSummaryClick: true,
          hasSummary: true,
          summaries: GridScoreSummary
        })
      ) // <-- Calculado [searchable = false]
      .push(
        'progress', // Avance
        new DoubleColumn({
          width: '138px',
          searchable: false,
          hasSummary: true,
          textWhenEmpty: 'NA',
          summaries: GridAvgSummary,
          hidden: true
        })
      ) // <-- Calculado [searchable = false]
      .push(
        'sumEstimatedHours', // Horas estimadas
        new DoubleColumn({
          width: '138px',
          searchable: false,
          hidden: true,
          hasSummary: true
        })
      ) // <-- Calculado [searchable = false]
      .push(
        'sumActualHours', // Horas consumidas
        new DoubleColumn({
          width: '138px',
          searchable: false,
          hasSummary: true
        })
      ) // <-- Calculado [searchable = false]
      .push('activityCommitmentDate', new DateColumn({ width: '175px', hidden: true }))
      .push('activityCommitmentWeek', new IntegerColumn({ width: '180px', fixedFilter: true, hidden: true }))
      .push(
        'activityCommitmentYear',
        new IntegerColumn({
          defaultFilterValue: new Date().getFullYear(),
          width: '180px',
          fixedFilter: true,
          hidden: true
        })
      )
      .push(
        'dates',
        new HtmlColumn<IActivitiesScoreQueryRow>({
          searchable: false,
          width: '180px',
          renderCell: (cell: GridCellType<IActivitiesScoreQueryRow>) => {
            const plannedImplementationDate = DateUtil.safe(cell.row.data.activityPlannedImplementationDate);
            const plannedVerificationDate = DateUtil.safe(cell.row.data.activityPlannedVerificationDate);
            const commitmentDate = DateUtil.safe(cell.row.data.activityCommitmentDate);
            let value: string = commitmentDate ? DateUtil.format(commitmentDate, this.dayJsDateFormat) : '';
            if (plannedImplementationDate && plannedVerificationDate && !DateUtil.isSameDay(plannedImplementationDate, plannedVerificationDate)) {
              value = `${DateUtil.format(plannedImplementationDate, this.dayJsDateFormat)} - ${DateUtil.format(plannedVerificationDate, this.dayJsDateFormat)}`;
            } else if (plannedImplementationDate) {
              value = DateUtil.format(plannedImplementationDate, this.dayJsDateFormat);
            } else if (plannedVerificationDate) {
              value = DateUtil.format(plannedVerificationDate, this.dayJsDateFormat);
            }
            return this.sanitizer.sanitize(SecurityContext.HTML, this.sanitizer.bypassSecurityTrustHtml(value));
          }
        })
      )
      .push('activityPlannedImplementationDate', new DateColumn({ width: '175px', fixedFilter: true, hidden: true }))
      .push('activityPlannedImplementationWeek', new IntegerColumn({ width: '180px', hidden: true }))
      .push('activityPlannedImplementationYear', new IntegerColumn({ width: '180px', hidden: true }))
      .push('activityPlannedVerificationDate', new DateColumn({ width: '175px', fixedFilter: true, hidden: true }))
      .push('activityPlannedVerificationWeek', new IntegerColumn({ width: '180px', hidden: true }))
      .push('activityPlannedVerificationYear', new IntegerColumn({ width: '180px', hidden: true }))
      .push(
        'activityStatus',
        new ObjectListColumn({
          width: '250px',
          hidden: true,
          fixedFilter: true,
          render: {
            valueKey: 'value',
            labelKey: 'label',
            items: statuses
          }
        })
      )
      .push(
        'activityResolutionId',
        new MultipleEntityColumn({
          hidden: true,
          itemsController: `${activityUrl}/resolutions/0`,
          width: '250px'
        })
      )
      .translate(this.$destroy, this.translateService, ActivitiesCommitmentsSummaryComponent.LANG_CONFIG, 'activities.column');
  }

  private isVerified(cell: GridCellType<IActivitiesScoreQueryRow>): boolean {
    if (!cell.column?.field) {
      return false;
    }
    const data = cell.row.data;
    const customWeekDay: Date = DateUtil.parse(cell.column.field.substring(0, 8), 'YYYYMMDD', this.getLanguage());
    let row: IActivitiesScoreQuerySubRow;
    if (!customWeekDay) {
      return false;
    }
    for (const item of data.datesRangeData) {
      row = item;
      if (row.dayRangeDate && DateUtil.isSameDay(customWeekDay, new Date(row.dayRangeDate))) {
        if (+row.detailProgress === 100 && +row.activityStage === 2) {
          return true;
        }
        if (+row.detailProgress === 0 && row.activityStatus === ActivityStatus.NOT_APPLY_VERIFIED) {
          return true;
        }
      }
    }
    return false;
  }

  private isRowWithCommitmentInverval(anticipationAttendDays: number): boolean {
    return anticipationAttendDays !== null && typeof anticipationAttendDays !== 'undefined' && anticipationAttendDays > 0;
  }

  private inProgress(activityStatus: number): boolean {
    switch (activityStatus) {
      case ActivityStatus.REPORTED:
      case ActivityStatus.RETURNED:
      case ActivityStatus.IN_PROCESS:
      case ActivityStatus.MAIN_CLOSED:
        return true;
    }
    return false;
  }

  private getCommitmentStatusString(data: IActivitiesScoreQueryRow, commitmentDate: Date, status: CommitmentStatus): string {
    switch (status) {
      case CommitmentStatus.ATTENDED:
        if (data.progress === 100) {
          return this.translate.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'attended');
        }
        return '';
      case CommitmentStatus.ONTIME:
        return this.translate.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'ontime');
      case CommitmentStatus.DELAYED: {
        const hasInterval = this.isRowWithCommitmentInverval(data.activityAnticipationAttendDays);
        if (hasInterval) {
          const anticipationPeriod = DateUtil.add(commitmentDate, DateInterval.DAY, data.activityAnticipationAttendDays);
          return this.translate.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'delayed', {
            days: DateUtil.calculateDayDiff(anticipationPeriod)
          });
        }
        return this.translate.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'delayed', {
          days: DateUtil.calculateDayDiff(commitmentDate)
        });
      }
    }
  }

  private getCommitmentStatus(activityStatus: number, commitmentDate: Date, anticipationAttendDays: number): CommitmentStatus {
    if (this.inProgress(activityStatus)) {
      if (!commitmentDate) {
        return null;
      }
      const hasInterval = this.isRowWithCommitmentInverval(anticipationAttendDays);
      if (hasInterval) {
        const anticipationPeriod = DateUtil.add(commitmentDate, DateInterval.DAY, anticipationAttendDays);
        const today = DateUtil.today();
        if (
          equalsObject(today, commitmentDate) ||
          equalsObject(today, anticipationPeriod) ||
          DateUtil.isGreater(commitmentDate, today) ||
          DateUtil.isBetween(today, DateUtil.trunc(commitmentDate), anticipationPeriod, 'day')
        ) {
          return CommitmentStatus.ONTIME;
        }
        return CommitmentStatus.DELAYED;
      }
      if (DateUtil.isGreater(commitmentDate) || DateUtil.isToday(commitmentDate)) {
        return CommitmentStatus.ONTIME;
      }
      return CommitmentStatus.DELAYED;
    }
    return CommitmentStatus.ATTENDED;
  }

  ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.cdr.detectChanges();
  }

  toogleMenu(drop: GridDropDownItem<IActivitiesScoreQueryRow>) {
    switch (drop.item.value) {
      case CommonAction.EDIT:
        this.menuService.navigate(`menu/activities/${drop.row.activityId}`, Module.ACTIVITY);
        break;
    }
  }

  onStateCleared() {
    // Reiniciar grupos
    this._groupingExpressions = cloneObject(this._defaultGroupingExpressions);
    const grid = this.grid();
    grid.groupingExpressions = this._groupingExpressions;
    grid.searchComponent()?.setDefaultFilters();
    this.cdr.detectChanges();
    grid.cdr.detectChanges();
  }

  onSummaryClick(data: RenderizableSummary<IActivitiesScoreQueryRow>) {
    if (data.column.field === 'score') {
      const groupData = this.parseGroupData(data.summaryResults[0].key);
      if (groupData && groupData.implementersIds?.length > 0) {
        const range = this.grid()?.rangeSelector?.range;
        if (groupData.implementersIds?.length === 1) {
          this.treeScoreDialog().openImplementer({
            implementerId: groupData.implementersIds[0],
            start: DateUtil.safe(range?.start),
            end: DateUtil.safe(range?.end)
          });
        } else {
          this.treeScoreDialog().openWeek({
            week: groupData.week,
            start: DateUtil.safe(range?.start),
            end: DateUtil.safe(range?.end)
          });
        }
      }
    }
  }

  private parseGroupData(key: string): IActivitiesScoreWeekIndex {
    if (key === null || typeof key === 'undefined' || key === '') {
      return null;
    }
    const implementersIds = new Set<number>();
    let week: number = null;
    for (const weekPlain of key.split(',')) {
      const weekData = weekPlain.split(':');
      week = +weekData[0];
      implementersIds.add(+weekData[1]);
    }
    if (week === null) {
      console.error(`Week must be defined ${key}`);
    }
    const ids = Array.from(implementersIds);
    const data: IActivitiesScoreWeekIndex = {
      week,
      implementersIds: ids
    };
    return data;
  }

  override langReady() {
    this.buildGrid();
    GridScoreSummary.label = this.translate.instantFrom(ActivitiesCommitmentsSummaryComponent.LANG_CONFIG, 'activities.column.score');
  }

  private openHistoryDetail(data: IDetailRangeDay): void {
    const start = data.start.getTime();
    const end = data.end.getTime();
    const activityId = data.cell.row.data.activityId;
    this.historyDialogGridUrl = `${this.urlModule}/score-query/history/${start}/${end}/${activityId}`;
    this.history_rightButtonLabel = this.translateService.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'history.rightButtonLabel');
    this.history_title = this.translateService.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'history.title');
    this.history_emptyGridHistory = this.translateService.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'history.emptyGridHistory');
    this.historyDialog().open();
  }

  private openTimesheetDetail(data: IDetailRangeDay): void {
    const start = data.start.getTime();
    const end = data.end.getTime();
    const activityId = data.cell.row.data.activityId;
    this.timesheetDialogGridUrl = `${this.urlModule}/timesheet/${start}/${end}/${activityId}`;
    this.timesheet_rightButtonLabel = this.translateService.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'history.rightButtonLabel');
    this.timesheet_title = this.translateService.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'history.title');
    this.timesheet_emptyGridTimesheet = this.translateService.instantFrom(ActivitiesDashboardComponent.LANG_CONFIG, 'history.emptyGridHistory');
    this.timesheetDialog().open();
  }

  onCellDetailClick(_data: RenderizableColumn<IActivitiesScoreQueryRow>): void {
    // Para mostrar historial de avance
    if (_data.column.field.includes(this._columnDetailProgress)) {
      this.openHistoryDetail({
        cell: _data.cell,
        column: _data.column,
        start: DateUtil.safe(_data.dayColumn.day),
        end: DateUtil.safe(_data.dayColumn.day)
      });
    } else if (_data.column.field.includes(this._columnDetailHours)) {
      this.openTimesheetDetail({
        cell: _data.cell,
        column: _data.column,
        start: DateUtil.safe(_data.dayColumn.day),
        end: DateUtil.safe(_data.dayColumn.day)
      });
    }
  }
}
