export interface ActivityRelationshipDTO {
  activityId: number;
  codeActivity: string;
  descriptionActivity: string;
  comment: string;
  relationType: number;
  statusActivity: number;
  statusLabel?: string;
  href?: string;
}

export interface ActivityRelationshipSave {
  codeActivity: string;
  comment: string;
  relationType: number;
}

export interface ActivityRelationshipEntity {
  activityId: number;
  relationActivityId: number;
  comment: string;
  relationType: number;
  createdDate: Date;
  lastModifiedDate: Date;
  createdBy: number;
  lastModifiedBy: number;
  deleted: boolean;
}
