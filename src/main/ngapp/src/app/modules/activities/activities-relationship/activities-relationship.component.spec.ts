import { MockCoreModule } from '@/core/test/mock-core.module';
import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ActivitiesRelationshipComponent } from './activities-relationship.component';

describe('ActivitiesRelationshipComponent', () => {
  let component: ActivitiesRelationshipComponent;
  let fixture: ComponentFixture<ActivitiesRelationshipComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, ActivitiesRelationshipComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivitiesRelationshipComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
