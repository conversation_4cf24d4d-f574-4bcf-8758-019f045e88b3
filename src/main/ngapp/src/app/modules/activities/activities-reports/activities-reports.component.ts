import { BnextCoreComponent } from '@/core/bnext-core.component';
import { GridComponent } from '@/core/grid/grid.component';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { Session } from '@/core/local-storage/session';
import { Deferred } from '@/core/utils/deferred';
import type { MenuService } from '@/modules/menu/services/menu.service';
import { DatePipe } from '@angular/common';
import { Component, type OnInit, inject } from '@angular/core';
import type { RowType } from '@infragistics/igniteui-angular';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import { type ActionStrip, LinkColumn, TextColumn, TimestampColumn } from 'src/app/core/grid/utils/grid.interfaces';
import { CommonAction } from 'src/app/core/utils/enums';
import { FavoriteTaskType, Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import type { FavoriteTaskSaveDTO, QuickAccessHolder } from '../../menu/menu-main/menu-main.interfaces';

@Component({
  selector: 'app-activities-reports',
  styleUrls: ['./activities-reports.component.scss'],
  templateUrl: './activities-reports.component.html',
  imports: [GridComponent]
})
export class ActivitiesReportsComponent extends BnextCoreComponent implements OnInit {
  datePipe = inject(DatePipe);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.activities',
    componentName: 'activities-reports'
  };

  url = 'activity-reports/reports';
  columns: GridColumn[];
  titleLabel: string;
  editAccess =
    Session.hasService(ProfileServices.IS_ADMIN) ||
    (Session.hasService(ProfileServices.USUARIO_CORPORATIVO) && Session.hasService(ProfileServices.ACTIVITY_ADMON_REPORT_ACCESS));
  LangConfig = ActivitiesReportsComponent.LANG_CONFIG;

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.getData(row?.data) : []),
      rowIdentifierKey: 'id'
    }
  };

  override ngOnInit(): void {
    this.perPage = Session.getGridSize();
    const s = this.translate.getFrom(this.LangConfig, 'activityReport.title').subscribe({ next: (tag) => (this.titleLabel = tag) });
    if (s) {
      this.subs.push(s);
    }
    this.columns = [];
    GridUtil.columns(this.columns)
      .subs(this.subs)
      .push(
        'title',
        new LinkColumn({
          linkHref: `./${this.getLang()}/menu/activities/reports/{id}`,
          linkParams: ['id'],
          width: '450px'
        })
      )
      .push(
        'description',
        new TextColumn({
          width: '450px'
        })
      )
      .push('createdBy', new TextColumn({ width: '300px' }))
      .push('createdDate', new TimestampColumn({ width: '150px' }))
      .push('lastModifiedBy', new TextColumn({ width: '300px' }))
      .push('lastModifiedDate', new TimestampColumn({ width: '150px' }))
      .langAsync(this.translateService, this.LangConfig, 'reports.column');
    this.cdr.detectChanges();
  }

  private getData(row: any): DropdownMenuItem[] {
    return [
      {
        text: this.translateService.instant('root.common.button.open'),
        value: CommonAction.OPEN_DETAIL,
        iconName: 'open_in_browser',
        modernHref: `activities/reports/${row.id}`,
        hidden: false
      },
      {
        text: this.translateService.instant('root.common.button.accessSettings'),
        value: CommonAction.EDIT,
        legacyHref: `v-activity-report-access.view?id=${row.nodeId}`,
        iconName: 'settings',
        hidden: !this.editAccess
      },
      {
        text: this.translateService.instant('root.common.button.add-favorite'),
        value: CommonAction.FAVORITE,
        iconName: 'favorite_border',
        hidden: false
      }
    ];
  }

  toggleMenuAction(drop: GridDropDownItem) {
    switch (drop.item.value) {
      case CommonAction.OPEN_DETAIL:
        this.menuService.navigate(`menu/activities/reports/${drop.row.id}`, Module.ACTIVITY);
        break;
      case CommonAction.EDIT:
        this.menuService.navigateLegacy(`v-activity-report-access.view?id=${drop.row.nodeId}`, Module.ACTIVITY);
        break;
      case CommonAction.FAVORITE:
        this.addActivityReportToFavorite(drop.row.id, drop.row, this.menuService, drop.row.description);
        break;
      default:
        break;
    }
  }

  public addActivityReportToFavorite(reportId: number, holder: QuickAccessHolder, menuService: MenuService, description: string): void {
    const saved = new Deferred<FavoriteTaskSaveDTO>();
    menuService.addQuickAccess({
      def: saved,
      icon: 'done',
      urlParams: `menu/activities/reports/${reportId}`,
      menuPath: 'menu/activities/reports',
      type: FavoriteTaskType.REPORT,
      infoMessage: this.translate.instantFrom(ActivitiesReportsComponent.LANG_CONFIG, 'add-to-favorite'),
      reportId: reportId,
      moduleName: String(Module.ACTIVITY).toLowerCase(),
      taskName: description
    });
    saved.promise.then((result) => {
      if (result.success) {
        holder.isFavorite = 1;
        holder.favoriteTaskId = result.id;
      }
    });
  }
}
