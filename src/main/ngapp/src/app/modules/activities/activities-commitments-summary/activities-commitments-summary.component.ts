import { BnextCoreComponent } from '@/core/bnext-core.component';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { Component, Input, type OnInit, inject, viewChild } from '@angular/core';
import type { ICommitmentsSummaryRow } from './activities-commitments-summary.interfaces';

import { DatePipe } from '@angular/common';

import * as GridUtil from '@/core/grid/utils/grid-util';
import { DoubleColumn, LongColumn, MultipleEntityColumn, PercentageColumn, type RenderizableColumn, TextColumn } from '@/core/grid/utils/grid.interfaces';
import * as DateUtil from '@/core/utils/date-util';
import { ActivatedRoute } from '@angular/router';
import { DefaultSortingStrategy, type IGroupingExpression, SortingDirection } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import { GridComponent } from 'src/app/core/grid/grid.component';
import type { DisplayDensity } from 'src/app/core/utils/display-density';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { getActivityController } from 'src/app/shared/activities/core/utils/activity-util';
import { ActivitiesTreeScoreComponent } from '../activities-tree-score/activities-tree-score.component';

@Component({
  selector: 'app-activities-commitments-summary',
  templateUrl: './activities-commitments-summary.component.html',
  styleUrls: ['./activities-commitments-summary.component.scss'],
  imports: [GridComponent, ActivitiesTreeScoreComponent]
})
export class ActivitiesCommitmentsSummaryComponent extends BnextCoreComponent implements OnInit {
  datePipe = inject(DatePipe);

  activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.activities',
    componentName: 'activities-commitments-summary'
  };

  LangConfig = ActivitiesCommitmentsSummaryComponent.LANG_CONFIG;

  // TODO: Skipped for migration because:
  //  This input is inherited from a superclass, but the parent cannot be migrated.
  @Input()
  public override displayDensity: DisplayDensity = 'compact';

  readonly treeScoreDialog = viewChild('treeScoreDialog', { read: ActivitiesTreeScoreComponent });

  readonly grid = viewChild('gridDef', { read: GridComponent });

  get urlModule(): string {
    return 'activities/activity';
  }

  get allowMaximize() {
    return true;
  }
  get ready() {
    return this.url !== null;
  }
  public url: string = null;
  columns: GridColumn<ICommitmentsSummaryRow>[] = [];
  titleLabel: string;
  expr: IGroupingExpression[] = [
    {
      fieldName: 'bussinessDepartmentId',
      dir: SortingDirection.Asc,
      ignoreCase: true,
      strategy: DefaultSortingStrategy.instance()
    }
  ];

  override ngOnInit() {
    super.ngOnInit();
    setTimeout(() => {
      this.getUrl();
    }, 0);
    const activityUrl = getActivityController(Module.ACTIVITY);
    this.translate
      .getFrom(this.LangConfig, 'activities.title')
      .pipe(takeUntil(this.$destroy))
      .subscribe({ next: (tag) => (this.titleLabel = tag) });
    this.columns = [];
    GridUtil.columns(this.columns)
      .push(
        'bussinessDepartmentId',
        new MultipleEntityColumn({
          itemsController: `${activityUrl}/business-unit-departments`,
          width: '300px'
        })
      )
      .push('ownerName', new TextColumn({ width: '200px', filterable: true }))
      .push('progress', new DoubleColumn({ width: '120px', filterable: false, searchable: true }))
      .push('score', new PercentageColumn({ width: '150px', filterable: false, searchable: false, allowCellClick: true }))
      .push('planDeviation', new PercentageColumn({ width: '150px', filterable: false, searchable: false }))
      .push('plannedCount', new LongColumn({ width: '150px', filterable: false, searchable: true }))
      .push('sumPlannedHours', new DoubleColumn({ width: '150px', filterable: false, searchable: true }))
      .push('sumActualHoursPlanned', new DoubleColumn({ width: '150px', filterable: false, searchable: true }))
      .push('sumActualHoursCancelled', new DoubleColumn({ width: '150px', filterable: false, searchable: true }))
      .push('sumActualHoursUnplanned', new DoubleColumn({ width: '150px', filterable: false, searchable: true }))
      .push('inProgressCount', new LongColumn({ width: '150px', filterable: false, searchable: true }))
      .push('undoneCount', new LongColumn({ width: '150px', filterable: false, searchable: true }))
      .push('unplannedCount', new LongColumn({ width: '150px', filterable: false, searchable: true }))
      .push('cancelledCount', new LongColumn({ width: '150px', filterable: false, searchable: true }))
      .push('closedCount', new LongColumn({ width: '150px', filterable: false, searchable: true }))
      .translate(this.$destroy, this.translateService, ActivitiesCommitmentsSummaryComponent.LANG_CONFIG, 'activities.column');
  }

  onCellDetailClick(data: RenderizableColumn<ICommitmentsSummaryRow>): void {
    // Para mostrar historial de avance
    if (data.column.field === 'score') {
      const range = this.grid()?.rangeSelector?.range;
      this.treeScoreDialog().openImplementer({
        implementerId: data.cell.row.data.implementerId,
        start: DateUtil.safe(range?.start),
        end: DateUtil.safe(range?.end)
      });
    }
  }

  getUrl(): void {
    this.navLang
      .getRouteParam(':userId', null, false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: ([userId]) => {
          if (userId !== null && typeof userId !== 'undefined' && +userId > 0) {
            this.url = `${this.urlModule}/commitments-summary/${userId}`;
          } else {
            this.url = `${this.urlModule}/commitments-summary`;
          }
          this.cdr.detectChanges();
        },
        error: () => {
          this.url = `${this.urlModule}/commitments-summary`;
          this.cdr.detectChanges();
        }
      });
  }
}
