import { MockCoreModule } from '@/core/test/mock-core.module';
import { type ComponentFixture, TestBed } from '@angular/core/testing';
import { GridComponent } from 'src/app/core/grid/grid.component';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ActivitiesCommitmentsSummaryComponent } from './activities-commitments-summary.component';

describe('ActivitiesCommitmentsSummaryComponent', () => {
  let component: ActivitiesCommitmentsSummaryComponent;
  let fixture: ComponentFixture<ActivitiesCommitmentsSummaryComponent>;

  configureTestSuite();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MockCoreModule, GridComponent, ActivitiesCommitmentsSummaryComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivitiesCommitmentsSummaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
