import { GridAvgSummary, type GridColumn } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import {
  DateColumn,
  DoubleColumn,
  DynamicColumn,
  IntegerColumn,
  LongColumn,
  MultipleEntityColumn,
  ObjectListColumn,
  TextColumn,
  TimestampColumn,
  YesNoColumn
} from '@/core/grid/utils/grid.interfaces';
import { LicensesModules } from '@/core/licenses/utils/licenses-modules';
import { ActivityCoreUtil } from '@/shared/activities/core/activities-core.utils';

import type { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import { LocalStorageItem } from '@/core/local-storage/local-storage-enums';
import { FeatureFlag, isFeatureAvailable } from '@/core/utils/feature-util';
import { ActivitySupportedModules } from '@/shared/activities/activity-supported-modules';
import type { RowType } from '@infragistics/igniteui-angular';
import type { Subject } from 'rxjs';
import { GridColumnVisibility, type GridIcon } from 'src/app/core/grid/utils/grid-base.interfaces';
import { GridDataType } from 'src/app/core/grid/utils/grid-data-type';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import type { LicensesModuleEntity } from 'src/app/core/licenses/utils/bnext-module';
import { LocalStorageSession } from 'src/app/core/local-storage/local-storage-session';
import type { DataMap } from 'src/app/core/utils/data-map';
import type { IConsolidatedActivitiesColumn, IConsolidatedActivitiesRow } from './consolidated-activities.enums';

export const LIST_UTIL_LANG_CONFIG: BnextComponentPath = {
  componentPath: 'modules.activities',
  componentName: 'consolidated-activities'
};

export function getListUtilControlColumns(
  $destroy: Subject<any>,
  translateService: BnextTranslateService,
  workflowId: number,
  activityUrl: string,
  excluded: IConsolidatedActivitiesColumn[] = [],
  statuses: GridIcon[] = ActivityCoreUtil.getLocalizedValuesList({
    translateService: translateService,
    $destroy: $destroy
  }),
  classifications: GridIcon[] = ActivityCoreUtil.getClassificationValuesList({
    translateService: translateService,
    $destroy: $destroy
  }),
  modules: LicensesModuleEntity[] = LicensesModules.getLocalizedValues(
    {
      translateService: translateService,
      $destroy: $destroy,
      subs: []
    },
    ActivitySupportedModules.VALUES
  ),
  dateFormat?: string,
  dateFormatTimeStamp?: string
): GridColumn[] {
  const columns: GridColumn[] = [];
  GridUtil.columns(columns)
    .excluded(excluded || [])
    .push(
      'entity_code',
      new TextColumn({
        groupable: false,
        width: '275px'
      })
    )
    .push('entity_description', '350px')
    .push(
      'entity_status',
      new ObjectListColumn({
        width: '250px',
        render: {
          valueKey: 'value',
          labelKey: 'label',
          items: statuses
        }
      })
    )
    .push(
      'entity_commitmentTask',
      new ObjectListColumn({
        width: '250px',
        render: {
          valueKey: 'value',
          labelKey: 'label',
          items: classifications
        }
      })
    )
    .push(
      'entity_activityResolution_id',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/resolutions/${workflowId}`,
        width: '250px'
      })
    )
    .push('entity_isPlanned', new YesNoColumn({ hidden: true, boolean: true }), '200px')
    .push(
      'entity_plannedHours',
      new DoubleColumn({
        hasSummary: true,
        width: '155px',
        textWhenEmpty: '0.0'
      })
    )
    .push(
      'entity_actualHours',
      new DoubleColumn({
        hasSummary: true,
        width: '155px',
        textWhenEmpty: '0.0'
      })
    )
    .push(
      'entity_type_id',
      new DynamicColumn({
        itemsController: `${activityUrl}/activity-types/${workflowId}`,
        dynamicColumnController: `activities-custom/${workflowId}`
      })
    )
    .push(
      'entity_priority_id',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/priorities`,
        cellClasses: { 'cell-priorityColor': true },
        cellStyles: {
          color: (data: DataMap<any>) => data.entity_priorityColor || 'inherit'
        },
        width: '150px'
      })
    )
    .push(
      'entity_businessUnit_id',
      new MultipleEntityColumn({
        hidden: true,
        itemsController: 'business-units',
        width: '220px'
      })
    )
    // USar AccessController si se requiere departamentos a los que tiene acceso el usuario
    // Usar BusinessInitsDepartmentController se requiere departamentos con restricción del permiso de UNRESTRICTED_DOCUMENT_ACCESS
    .push(
      'entity_businessUnitDepartment_id',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/business-unit-departments`,
        width: '220px'
      })
    )
    .push(
      'entity_progress',
      new DoubleColumn({
        summaries: GridAvgSummary,
        hasSummary: true,
        width: '105px'
      })
    )
    .push(
      'entityImplementerNames',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/data-source/users-assigned-to-activities`,
        width: '220px',
        typeDropDownValue: GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS
      })
    )
    .push(
      'entity_preImplementerNames',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/data-source/users-assigned-to-activities`,
        width: '200px',
        typeDropDownValue: GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS
      })
    )
    .push(
      'entity_category_id',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/categories/${workflowId}`,
        width: '190px'
      })
    )
    .push(
      'entity_createdYear',
      new IntegerColumn({
        width: '180px',
        hidden: true
      })
    )
    .push(
      'entity_createdWeek',
      new IntegerColumn({
        width: '180px',
        hidden: true
      })
    )
    .push(
      'entity_commitmentWeek',
      new IntegerColumn({
        width: '180px',
        hidden: true
      })
    )
    .push(
      'entity_plannedImplementationWeek',
      new IntegerColumn({
        width: '180px',
        hidden: true
      })
    )
    .push(
      'entity_plannedVerificationWeek',
      new IntegerColumn({
        width: '180px',
        hidden: true
      })
    )
    .push(
      'entity_plannedImplementationDate',
      new DateColumn({
        width: '180px',
        hidden: true,
        dateFormat: dateFormat
      })
    )
    .push(
      'entity_reminder',
      new DateColumn({
        width: '180px',
        dateFormat: dateFormat
      })
    )
    .push(
      'entity_implementationOn',
      new DateColumn({
        width: '180px',
        hidden: true,
        dateFormat: dateFormat
      })
    )
    .push(
      'entity_anticipationAttendDays',
      new IntegerColumn({
        width: '205px',
        hidden: true
      })
    )
    .push(
      'singleVerifierId',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/data-source/users-assigned-to-activities`,
        width: '220px'
      }),
      GridColumnVisibility.HIDDEN
    )
    .push(
      'entity_plannedVerificationDate',
      new DateColumn({
        width: '180px',
        hidden: true,
        dateFormat: dateFormat
      })
    )
    .push(
      'entity_verificationOn',
      new DateColumn({
        width: '180px',
        hidden: true,
        dateFormat: dateFormat
      })
    )
    .push(
      'entity_daysToVerify',
      new IntegerColumn({
        width: '205px',
        hidden: true
      })
    )
    .push(
      'entity_activityOrder',
      new LongColumn({
        width: '205px',
        hidden: true
      })
    )
    .push(
      'clientDescription',
      new TextColumn({
        width: '180px'
      })
    )
    .push(
      'plannerActivityDescription',
      new TextColumn({
        width: '180px'
      })
    )
    .push(
      'plannerTask_description',
      new TextColumn({
        width: '180px'
      })
    )
    .push(
      'entity_rescheduled',
      new IntegerColumn({
        width: '205px',
        hidden: true
      })
    )
    .push('entity_hasRescheduled', new YesNoColumn({ hidden: true, boolean: true }));

  if (isFeatureAvailable(FeatureFlag.ACTIVITY_ADD_GROUP)) {
    // ToDo: Habilitar grupos de actividades, actualmente se deshabilita ya que se encuentra inestable
    GridUtil.columns(columns)
      .excluded(excluded || [])
      .push('entity_groupName', '250px', GridColumnVisibility.HIDDEN);
  }
  GridUtil.columns(columns)
    .excluded(excluded || [])
    .push('entity_parentCode', '150px')
    .push(
      'entity_objective_id',
      new MultipleEntityColumn({
        hidden: true,
        itemsController: `${activityUrl}/objectives`,
        width: '170px'
      })
    )
    .push(
      'entity_source_id',
      new MultipleEntityColumn({
        hidden: true,
        itemsController: `${activityUrl}/sources`,
        width: '190px'
      })
    )
    .push(
      'entity_type_module',
      new ObjectListColumn({
        hidden: true,
        width: '190px',
        render: {
          valueKey: 'code',
          labelKey: 'description',
          items: modules
        }
      })
    )
    .push(
      'entity_createdDate',
      new TimestampColumn({
        width: '175px',
        dateFormat: dateFormatTimeStamp
      })
    )
    .push(
      'entity_countComments',
      new LongColumn({
        hidden: true,
        width: '180px'
      })
    )
    .push(
      'entity_createdBy',
      new MultipleEntityColumn({
        itemsController: `${activityUrl}/data-source/users-assigned-to-activities`,
        width: '150px'
      })
    )
    .push('creatorDepartment_description', '150px')
    .push(
      'entity_lastModifiedDate',
      new TimestampColumn({
        width: '175px',
        hidden: true,
        dateFormat: dateFormatTimeStamp
      })
    )
    .translate($destroy, translateService, LIST_UTIL_LANG_CONFIG, 'activities.column');
  return columns;
}
export function getRowStyles(): DataMap<string | ((row: RowType) => string)> {
  return {
    'border-left': (row: RowType) =>
      `solid ${
        row.data?.data?.entity_priorityColor ? row.data.data.entity_priorityColor : row.data?.entity_priorityColor ? row.data.entity_priorityColor : 'transparent'
      } 0.5rem`,
    overflow: 'hidden'
  };
}
export function saveActivityNavigator(module: string, rowData: IConsolidatedActivitiesRow[]): void {
  if (!rowData || !Array.isArray(rowData)) {
    return;
  }
  try {
    const data: string[] = rowData.map((r) => {
      return `${r.entity_code}@${module}`;
    });
    LocalStorageSession.setValue(LocalStorageItem.ACTIVITY_NAVIGATOR_LIST, data);
  } catch (e) {
    console.error(e);
  }
}
export function saveActivityNavigatorHistory(code: string): void {
  try {
    const current = LocalStorageSession.getValue(LocalStorageItem.ACTIVITY_NAVIGATOR_HISTORY_LIST) || [];
    LocalStorageSession.setValue(LocalStorageItem.ACTIVITY_NAVIGATOR_HISTORY_LIST, (current || []).concat(code));
  } catch (e) {
    console.error(e);
  }
}
/**
 * Devuelve un arreglo de claves de actividades
 */
export function loadActivityNavigator(excluded: string[] = []): string[] {
  try {
    if (excluded.length > 0) {
      return (LocalStorageSession.getValue(LocalStorageItem.ACTIVITY_NAVIGATOR_LIST) || []).filter((c) => excluded.indexOf(c) === -1);
    }
    return LocalStorageSession.getValue(LocalStorageItem.ACTIVITY_NAVIGATOR_LIST) || [];
  } catch (e) {
    console.error(e);
    return [];
  }
}
