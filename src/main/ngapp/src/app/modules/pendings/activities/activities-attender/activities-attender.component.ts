import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { Component, Input, type OnInit, ViewChild, inject, output, viewChild } from '@angular/core';
import {
  type DateRangeDescriptor,
  DateRangeType,
  IgxBadgeModule,
  IgxButtonModule,
  IgxDatePickerModule,
  IgxDialogComponent,
  IgxDialogModule,
  IgxIconModule,
  IgxInputGroupModule,
  IgxRippleModule,
  PickerInteractionMode
} from '@infragistics/igniteui-angular';
import { Subject, race, take, takeUntil, throttleTime } from 'rxjs';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ImplementAction, VerifyAction } from '../activities-pending/activities-pending.enums';
import { type ActionPending, ActivityCancellationReason, type ActivityPendingDto } from '../activities-pending/activities-pending.interfaces';

import { GridMultiSelectComponent } from '@/core/grid-multi-select/grid-multi-select.component';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import { DateColumn, DoubleColumn, LinkColumn, ObjectListColumn, TextColumn, TooltipColumn } from '@/core/grid/utils/grid.interfaces';
import { AppService } from '@/core/services/app.service';
import { BnextLoaderActivationService } from '@/core/services/bnext-loader-activation.service';
import * as DateUtil from '@/core/utils/date-util';
import { EnumUtil } from '@/core/utils/enum-util';
import { FormUtil } from '@/core/utils/form-util';
import { ActivityTaskStatus, FillType } from '@/shared/activities/core/activities-core.enums';
import { ActivityCoreUtil } from '@/shared/activities/core/activities-core.utils';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { HammerModule } from '@angular/platform-browser';
import type { RowSelection } from 'src/app/core/grid-base-select/grid-base-select.interfaces';
import { BnextTranslateModule } from 'src/app/core/i18n/bnext-translate.module';
import { SelectComponent } from 'src/app/core/select/select.component';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';

import { SafeHtmlPipe } from 'src/app/core/utils/safe-html.pipe';
import type { ActivtyPendingAttendResult } from 'src/app/shared/activities/core/utils/activity-base.interfaces';
import type { IActivityResolutionItem } from '../../../settings/activity-settings/activity-resolution/activity-resolution.interfaces';
import { ActivityInfoConstant } from '../activity-info/activity-info.constant';
import { ActivityResolutionComponent, type ResolutionDialogResult } from '../activity-resolution/activity-resolution.component';
import { ActivityVerifyComponent } from '../activity-verify/activity-verify.component';
import type { ReassignVerifyDialogResult, VerifyDialogResult } from '../activity-verify/activity-verify.interfaces';
import { FillTypeComponent } from '../fill-type/fill-type.component';
import type { ChangeProgress } from '../fill-type/fill-type.interfaces';
import type { ActivityPendingEventedDto } from '../pendings-activities/pendings.activities.interfaces';
import { SwapActivitiesComponent } from '../swap-activities/swap-activities.component';
import { ToReassignVerifyComponent } from '../to-reassign-verify/to-reassign-verify.component';
import type { AttenderAcceptedData, AttenderProgressChange, IActivitiesAttenderComponent } from './activities-attender.interfaces';

@Component({
  selector: 'app-activities-attender',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SafeHtmlPipe,
    BnextTranslateModule,
    HammerModule,
    IgxDatePickerModule,
    IgxDialogModule,
    IgxBadgeModule,
    IgxInputGroupModule,
    IgxIconModule,
    IgxButtonModule,
    IgxRippleModule,
    FillTypeComponent,
    SelectComponent,
    GridMultiSelectComponent,
    SwapActivitiesComponent,
    ToReassignVerifyComponent,
    ActivityResolutionComponent,
    ActivityVerifyComponent
  ],
  templateUrl: './activities-attender.component.html',
  styleUrls: ['./activities-attender.component.scss']
})
export class ActivitiesAttenderComponent extends BnextCoreComponent implements i18n, OnInit, IActivitiesAttenderComponent {
  loader = inject(BnextLoaderActivationService);

  private noticeService = inject(NoticeService);
  private api = inject(AppService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.activities.pending',
    componentName: 'activities-attender'
  };

  override get componentPath(): string {
    return 'modules.activities.pending';
  }

  override get tagName(): string {
    return 'activities-attender';
  }

  EnumImplementAction = ImplementAction;
  EnumVerifyAction = VerifyAction;
  implementations: any[];
  ImpColumns: GridColumn[];
  implementsTitle = 'Implementaciones de la serie';
  implementationsGridTitle = '';
  resolutionDescription: string = null;
  resolutions: IActivityResolutionItem[] = [];
  topZoneEnable = false;
  openVerifyAttender = false;
  openActivityAttender = false;
  openToReassignVerify = false;
  showImplementationsGrid = false;
  openEntityDialog = false;
  private recordId = null;
  private fromPendings = false;
  public formIncomplete: FormGroup = null;
  okDialogButton = null;
  selectedVerifer = null;
  private typeId = null;
  heigthActivitiesGrid = '500px';
  datePickerMode: PickerInteractionMode = this.isSmallTouchDevice ? PickerInteractionMode.Dialog : PickerInteractionMode.DropDown;
  disabledDates: DateRangeDescriptor[] = [];
  activityTaskStatusEnum = ActivityTaskStatus;
  activityResolutionInterpolate = '';
  userNameInterpolate = '';

  readonly dialog = viewChild('dialog', { read: IgxDialogComponent });

  // TODO: Skipped for migration because:
  //  This query overrides a field from a superclass, while the superclass field
  //  is not migrated.
  @ViewChild('verifyAttender', { read: ActivityVerifyComponent })
  public verifyAttender: ActivityVerifyComponent;
  readonly activityAttender = viewChild('activityAttender', { read: ActivityResolutionComponent });
  readonly reassignVerify = viewChild('toReassignVerify', { read: ToReassignVerifyComponent });
  readonly reassignMultipleVerify = viewChild('toReassignMultipleVerify', { read: ToReassignVerifyComponent });
  readonly implementationsGrid = viewChild<GridMultiSelectComponent>('implementationsGridDef');

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public entity: ActivityPendingEventedDto = null;

  public readonly progressChange = output<AttenderProgressChange>();
  public readonly accepted = output<AttenderAcceptedData>();
  public readonly attendedMany = output<any>(); // Cuando se terminaron de verificar todas las implementaciones
  public readonly cancelled = output<true>();
  public readonly showProgressBar = output<boolean>();
  public readonly refreshAll = output<any>();

  ngOnInit(): void {
    super.ngOnInit();
    this.ImpColumns = [];
    const activityStatuses = ActivityCoreUtil.getLocalizedValuesList({
      translateService: this.translate,
      subs: this.subs
    });
    GridUtil.columns(this.ImpColumns)
      .subs(this.subs)
      .push(
        'status',
        new ObjectListColumn({
          width: '100px',
          render: {
            valueKey: 'value',
            labelKey: 'label',
            items: activityStatuses
          }
        })
      )
      .push('code', '180px')
      .push(
        'description',
        new LinkColumn({
          linkHref: `./${this.getLang()}/menu/activities/{id}`,
          linkParams: ['id'],
          width: '250px'
        })
      )
      .push(
        'commitmentDate',
        new DateColumn({
          width: '200px'
        })
      )
      .push('activityType', '250px')
      .push(
        'progress', // Progreso
        new DoubleColumn({
          width: '118px',
          hidden: true,
          fixedFilter: true
        })
      )
      // Columnas que están en el campo tooltip
      .push('businessUnitDepartmentName', new TextColumn({ width: '200px', hidden: true, fixedFilter: true }))
      .push('implementerNames', new TextColumn({ width: '200px', hidden: true, fixedFilter: true }))
      .push('verifierName', new TextColumn({ width: '200px', hidden: true, fixedFilter: true }))
      .push('lastAction', new TextColumn({ width: '200px', hidden: true, fixedFilter: true }))
      .push('resolutionDescription', new TextColumn({ width: '200px', hidden: true, fixedFilter: true }))
      .push(
        'tooltip',
        new TooltipColumn({
          autoSize: true,
          searchable: false,
          filterable: false,
          sortable: false,
          groupable: false
        })
      )
      .langAsync(this.translateService, ActivityInfoConstant.LANG_CONFIG, 'i18n');
  }

  public detachEventedEntity(pending: ActivityPendingEventedDto) {
    pending.itemAction = null;
    pending.progressBackup = null;
    pending.commentValue = null;
    if (pending.customEvents) {
      pending.customEvents = false;
      if (pending.onProgressUpdated) {
        pending.onProgressUpdated.unsubscribe();
      }
      if (pending.onProgressHasComment) {
        pending.onProgressHasComment.unsubscribe();
      }
    }
  }

  onCancelDialog() {
    this.entity.busy = false;
    this.entity.progress = this.entity.progressBackup;
    this.cancelled.emit(true);
    this.closeDialog();
  }

  onAcceptDialog(): void {
    switch (this.entity.itemAction) {
      case ImplementAction.NOT_APPLY:
      case VerifyAction.NOT_APPLY_VERIFY:
      case VerifyAction.ACCEPT_NOT_APPLY: // Para el pendiente {MODULE}-TO-VERIFY-NOT-APPLY
        if (this.entity?.cancellationReason !== ActivityCancellationReason.NONE && !this.entity?.swapActivities?.length) {
          this.dialogService.error(this.tag('swapActivitiesRequired'));
          return;
        }
        if (!this.entity?.commentValue || (this.resolutions.length > 0 && !this.entity?.resolutionId)) {
          this.dialogService.error(this.tag('dataRequired'));
          return;
        }
        break;
      case VerifyAction.INCOMPLETE: {
        this.cdr.detectChanges();
        this.formIncomplete.markAllAsTouched();
        this.formIncomplete.updateValueAndValidity();
        if (!FormUtil.isValid(this.formIncomplete, [], true).status) {
          this.noticeService.notice(this.translate.instantFrom(ActivityInfoConstant.LANG_CONFIG, 'fillFormFieldsIncomplate'), false);
          this.formIncomplete.get('commentValue').markAsPristine();
          return;
        }
        const data: AttenderAcceptedData = {
          progress: this.entity.progress || undefined,
          comment: this.formIncomplete?.get('commentValue').value,
          activity: this.entity
        };
        if (this.entity.fillType === FillType.MARK_DONE) {
          this.entity.progress = 0;
          data.progress = 0;
        }
        if (this.entity.mustUpdateImplementationAtReturn) {
          data.commitmentDate = DateUtil.safe(this.formIncomplete?.get('commitmentDate').value);
        }
        this.openVerifyAttender = true;
        this.cdr.detectChanges();
        this.verifyAttender.open(this.entity, this.formIncomplete?.get('commentValue').value).then(
          (frm: DialogResult | VerifyDialogResult) => {
            data.activity.resolutionId = (frm as VerifyDialogResult).resolutionId;
            this.accepted.emit(data);
          },
          () => {
            data.activity.progress = data.activity.progressBackup;
            console.warn('Cancel resolution dialog, unsaving  incomplete');
          }
        );
        this.closeDialog();
        return;
      }
      case ImplementAction.PROGRESS:
        this.cdr.detectChanges();
        this.formIncomplete.markAllAsTouched();
        this.formIncomplete.updateValueAndValidity();
        if (!FormUtil.isValid(this.formIncomplete, [], true).status) {
          this.noticeService.notice(this.translate.instantFrom(ActivityInfoConstant.LANG_CONFIG, 'fillCommentBoxProgress'), false);
          this.formIncomplete.get('commentValue').markAsPristine();
          return;
        }
        // Validación cuando la actividad se marca como NA - Se abre la ventana de cancelación
        if (!this.entity.progress && this.entity.apply === 0) {
          this.entity.commentValue = this.formIncomplete?.get('commentValue').value;
          this.entity.itemAction = ImplementAction.NOT_APPLY;
          this.updatePendingTags(this.entity);
          this.onOpeningDialog(ImplementAction.NOT_APPLY);
          this.cdr.detectChanges();
          return;
        }
        break;
    }
    const data = {
      progress: this.entity.progress || undefined,
      comment: this.formIncomplete?.get('commentValue').value || this.entity.commentValue,
      activity: this.entity
    };
    this.accepted.emit(data);
    this.closeDialog();
  }

  onProgressDialogChange(progress: ChangeProgress): void {
    this.progressChange.emit({
      progress: progress,
      activity: this.entity
    });
  }

  override langReady() {
    this.implementsTitle = this.tag('implementsTitle');
    if (this.entity) {
      this.updatePendingTags(this.entity);
    }
  }

  public openDialog(activity: ActivityPendingEventedDto, comment?: string, commentLabel?: string): void {
    if (this.debug()) {
      console.log(`Attending activity pending with data: ${activity} and comment: ${comment}`);
    }
    if (comment !== null && typeof comment !== 'undefined' && comment !== '') {
      this.entity.commentValue = comment;
    }
    activity.taskStatus = EnumUtil.getValue(ActivityTaskStatus, activity.taskStatus);
    this.entity = activity;
    this.activityResolutionInterpolate = `<b> ${activity.activityResolution} </b>`;
    this.userNameInterpolate = `<b> ${activity.lastActionUserName || activity.implementerNames} </b>`;
    if (this.entity.itemAction === VerifyAction.INCOMPLETE) {
      this.formIncomplete = new FormGroup({
        commentValue: new FormControl('', Validators.required)
      });
      if (this.entity.mustUpdateImplementationAtReturn) {
        const currentCommitment = DateUtil.safe(this.entity.implementationOn || this.entity.implementation || this.entity.commitmentDate);
        if (currentCommitment) {
          this.disabledDates.push({ type: DateRangeType.Before, dateRange: [currentCommitment] });
        }
        this.formIncomplete.addControl('commitmentDate', new FormControl(DateUtil.safe(new Date()), Validators.required));
      }
    }
    if (this.entity.itemAction === ImplementAction.PROGRESS) {
      this.formIncomplete = new FormGroup({
        commentValue: new FormControl('', Validators.required)
      });
    }
    this.updatePendingTags(activity, commentLabel);
    this.openEntityDialog = true;
    this.cdr.detectChanges();
    this.dialog().open();
  }

  public openResolutionDialog(typeId: number, action: ImplementAction, comment?: string, commentLabel?: string): Promise<ResolutionDialogResult> {
    if (this.debug()) {
      console.log(`Attending activity pending with typeId: ${typeId} and comment: ${comment}`);
    }
    this.openActivityAttender = true;
    this.cdr.detectChanges();
    return this.activityAttender().openFinishActivityAttender(typeId, action, comment, commentLabel);
  }

  public openVerifyManyDialog(activity: ActivityPendingEventedDto, recordId: number, refreshPendings, typeId: number): void {
    this.showImplementationsGrid = true;
    this.cdr.detectChanges();
    this.implementationsGridTitle = this.tag('verifyManyGridTitle');
    this.okDialogButton = null;
    this.entity = activity;
    const implementationsGrid = this.implementationsGrid();
    implementationsGrid.dataUrl = `pendings/implementActivities/${recordId}`;
    this.recordId = recordId;
    this.typeId = typeId;
    implementationsGrid.openDialog();
    this.fromPendings = refreshPendings;
  }

  public openReassignVerify(activity: ActivityPendingDto, refreshPendings: boolean): void {
    this.openToReassignVerify = true;
    this.cdr.detectChanges();
    const reassignVerify = this.reassignVerify();
    reassignVerify.useDialog = true;
    reassignVerify.openDialog(activity.recordId, activity.module.toLowerCase()).then((frm: ReassignVerifyDialogResult) => {
      const changeData = {
        verifierId: frm.verifierId,
        verificationDate: frm.verificationDate,
        reasonChange: frm.reasonChange
      };
      this.loader.show();
      this.api
        .post({
          url: `activities/${activity.module.toLowerCase()}/reassing-verifier/${activity.recordId}`,
          cancelableReq: this.$destroy,
          postBody: changeData
        })
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: (_result: ActivtyPendingAttendResult) => {
            this.showProgressBar.emit(false);
            const message = this.tag('reassignedSucess');
            this.noticeService.notice(message);
            if (refreshPendings) {
              // Actualizar listado de pendientes
              this.attendedMany.emit(activity);
            } else {
              // Actualizar detalle de actividad
              this.attendedMany.emit(_result);
            }
            this.loader.hide();
            return true;
          },
          error: (error) => {
            if (error.error) {
              this.dialogService.error(this.translate.instantFrom(ActivitiesAttenderComponent.LANG_CONFIG, `i18n.${error.error}`));
            }
            this.showProgressBar.emit(false);
            this.loader.hide();
            return false;
          }
        });
    });
  }

  public openReassignMultipleVerify(refreshPendings = false): void {
    this.implementationsGridTitle = this.tag('multipleReassignGridTitle');
    this.okDialogButton = this.tag('okDialogButton');
    this.topZoneEnable = true;
    const implementationsGrid = this.implementationsGrid();
    implementationsGrid.dataUrl = `pendings/verifiableActivities${this.selectedVerifer != null ? `/${this.selectedVerifer.id}` : ''}`;
    implementationsGrid.openDialog();
    this.fromPendings = refreshPendings;
  }

  onOpeningDialog(itemAction: ImplementAction | VerifyAction): void {
    this.resolutionDescription = null;
    this.entity.resolutionId = null;
    this.resolutions = [];
    if (itemAction === ImplementAction.NOT_APPLY || itemAction === VerifyAction.NOT_APPLY_VERIFY || itemAction === VerifyAction.ACCEPT_NOT_APPLY) {
      this.api
        .get({
          cancelableReq: this.$destroy,
          url: `activity/pending/check-for-resolutions/${this.entity.typeId}`,
          handleFailure: false
        })
        .subscribe({
          next: (resolutions: IActivityResolutionItem[]) => {
            if (resolutions.length) {
              if (itemAction === ImplementAction.NOT_APPLY) {
                this.resolutions = resolutions.filter((resol) => !resol.closeActivityEnabled);
              } else {
                this.resolutions = resolutions;
              }
              this.cdr.detectChanges();
            }
          }
        });
    }
  }

  detectChanges(): void {
    this.cdr.detectChanges();
  }

  onClosedDialog(): void {
    this.entity = null;
    this.resolutionDescription = null;
    this.cancelled.emit(true);
  }

  closeDialog(): void {
    const dialog = this.dialog();
    if (dialog) {
      dialog.close();
    }
    this.openEntityDialog = false;
    this.entity = null;
    this.cdr.detectChanges();
  }

  onSwapActivitiesChanged(value: number[]) {
    this.entity.swapActivities = value;
  }

  public buildLocalEventedEntity(pending: ActivityPendingDto, action?: ActionPending): ActivityPendingEventedDto {
    const activity = pending as ActivityPendingEventedDto;
    if (activity?.availableActions?.length > 0) {
      activity.notApplyAccess =
        activity.availableActions.indexOf(ImplementAction.NOT_APPLY) !== -1 ||
        activity.availableActions.indexOf(VerifyAction.NOT_APPLY_VERIFY) !== -1 ||
        activity.availableActions.indexOf(VerifyAction.ACCEPT_NOT_APPLY) !== -1;
    } else {
      activity.notApplyAccess = false;
    }
    if (!activity.customEvents) {
      activity.customEvents = true;
      activity.onProgressUpdated = new Subject<string>();
      activity.onProgressHasComment = new Subject<boolean>();
      this.linkCommentEvent(activity);
    }
    activity.cancelReasons = [
      { text: ActivityCancellationReason.CHANGE_PLANNED_HOURS, value: ActivityCancellationReason.CHANGE_PLANNED_HOURS },
      { text: ActivityCancellationReason.NEW_ACTIVITY, value: ActivityCancellationReason.NEW_ACTIVITY },
      { text: ActivityCancellationReason.NONE, value: ActivityCancellationReason.NONE }
    ];
    if (!activity.cancellationReason) {
      switch (activity.module) {
        case Module.PLANNER:
          activity.cancellationReason = ActivityCancellationReason.NONE;
          break;
        default:
          if (action) {
            switch (action) {
              case ImplementAction.NOT_APPLY:
              case VerifyAction.NOT_APPLY_VERIFY:
              case VerifyAction.ACCEPT_NOT_APPLY:
                if (activity.cancellationReason === null || typeof activity.cancellationReason === 'undefined') {
                  activity.cancellationReason = ActivityCancellationReason.NEW_ACTIVITY;
                }
                break;
              default:
                activity.cancellationReason = null;
                break;
            }
          } else {
            activity.cancellationReason = null;
          }
          break;
      }
    }
    this.updatePendingTags(activity);
    return activity;
  }

  private updatePendingTags(activity: ActivityPendingEventedDto, commentLabel?: string) {
    switch (activity.itemAction) {
      case ImplementAction.PROGRESS:
        activity.dialogTitle = this.tag('progressTitle');
        if (commentLabel) {
          activity.commentLabel = commentLabel;
        } else {
          activity.commentLabel = this.tag('progressLabel');
        }
        break;
      case ImplementAction.NOT_APPLY:
        if (activity.module === Module.PLANNER) {
          activity.dialogTitle = this.tag('planner-discardTitle');
          if (commentLabel) {
            activity.commentLabel = commentLabel;
          } else {
            activity.commentLabel = this.tag('planner-cancelCommentLabel');
          }
          activity.dialogMessage = this.tag('planner-discardMessage');
        } else {
          activity.dialogTitle = this.tag('generics-discardTitle');
          if (commentLabel) {
            activity.commentLabel = commentLabel;
          } else {
            activity.commentLabel = this.tag('generics-cancelCommentLabel');
          }
          activity.dialogMessage = this.tag('generic-discardMessage');
          activity.cancelReasonLabel = this.tag('generics-cancelReasonLabel');
        }
        for (const reason of activity.cancelReasons) {
          reason.text = this.tag(`activityCancellationReasons.${reason.value}`);
        }
        break;
      case VerifyAction.NOT_APPLY_VERIFY:
        activity.dialogTitle = this.tag('generics-discardVerificationTitle');
        if (commentLabel) {
          activity.commentLabel = commentLabel;
        } else {
          activity.commentLabel = this.tag('generics-cancelCommentLabel');
        }
        activity.dialogMessage = this.tag('generic-discardVerificationMessage');
        activity.cancelReasonLabel = this.tag('generics-cancelReasonLabel');
        for (const reason of activity.cancelReasons) {
          reason.text = this.tag(`activityCancellationReasons.${reason.value}`);
        }
        break;
      case VerifyAction.ACCEPT_NOT_APPLY: // Etiquetas solo para el pendiente {MODULE}-TO-VERIFY-NOT-APPLY
        activity.dialogTitle = this.tag('generics-discardVerificationAcceptTitle');
        if (commentLabel) {
          activity.commentLabel = commentLabel;
        } else {
          activity.commentLabel = this.tag('generics-cancelCommentLabel');
        }
        activity.dialogMessage = this.tag('generic-discardVerificationAcceptMessage');
        activity.cancelReasonLabel = this.tag('generics-cancelReasonLabel');
        for (const reason of activity.cancelReasons) {
          reason.text = this.tag(`activityCancellationReasons.${reason.value}`);
        }
        break;
      case VerifyAction.INCOMPLETE:
        activity.dialogTitle = this.tag('progressTitle');
        if (commentLabel) {
          activity.commentLabel = commentLabel;
        } else {
          activity.commentLabel = this.tag('generics-incompleteCommentLabel');
        }
        break;
    }
  }

  private linkCommentEvent(activity: ActivityPendingEventedDto) {
    race(
      activity.onProgressUpdated.pipe(takeUntil(this.$destroy)).pipe(
        throttleTime(1000, undefined, {
          leading: true,
          trailing: true
        })
      ),
      activity.onProgressHasComment.pipe(takeUntil(this.$destroy))
    )
      .pipe(take(1)) // force it to complete
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (code) => {
          let noticeMsg = this.translate.instantFrom(ActivityInfoConstant.LANG_CONFIG, 'comment');
          if (code && typeof code === 'string') {
            noticeMsg += code;
          }
          this.noticeService.notice(noticeMsg);
          this.cdr.detectChanges();
        },
        error: (err) => console.error(err),
        complete: () => this.linkCommentEvent(activity) // reset it once complete
      });
  }

  public toggleMenu(_event) {
    if (this.implementations.length > 0) {
      switch (_event) {
        case VerifyAction.VERIFY:
          this.openVerifyAttender = true;
          this.cdr.detectChanges();
          this.verifyAttender
            .open(
              {
                recordId: this.recordId,
                typeId: this.typeId
              },
              '',
              this.tag('verify'),
              this.tag('activity-verified')
            )
            .then((frm: VerifyDialogResult) => {
              this.showProgressBar.emit(true);
              const resolutionId = frm.resolutionId || null;
              if (resolutionId) {
                if (this.implementations) {
                  for (const item of this.implementations) {
                    item.resolutionId = resolutionId;
                    item.verifyAction = VerifyAction.VERIFY;
                  }
                }
              } else {
                if (this.implementations) {
                  for (const item of this.implementations) {
                    item.verifyAction = VerifyAction.VERIFY;
                  }
                }
              }
              if (this.implementations) {
                this.loader.show();
                let url = `activity/pending/to-verify/${this.recordId}`;
                if (resolutionId) {
                  url = `activity/pending/to-verify/${this.recordId}/${resolutionId}`;
                }
                this.api
                  .post({
                    url: url,
                    postBody: {
                      comment: frm.inputValue,
                      implementations: this.implementations,
                      verifyAction: VerifyAction.VERIFY,
                      progress: 100
                    },
                    cancelableReq: this.$destroy
                  })
                  .pipe(takeUntil(this.$destroy))
                  .subscribe({
                    next: (_result) => {
                      this.showProgressBar.emit(false);
                      if (this.entity) {
                        this.entity.itemAction = VerifyAction.VERIFY;
                      }
                      const message = this.tag('verifiedSucess');
                      this.noticeService.notice(message);
                      this.attendPending(_result);
                      this.loader.hide();
                    },
                    error: () => {
                      this.showProgressBar.emit(false);
                      this.loader.hide();
                    }
                  });
              }
            });
          break;
        case VerifyAction.UNDONE:
          this.openVerifyAttender = true;
          this.cdr.detectChanges();
          this.verifyAttender
            .open(
              {
                recordId: this.recordId,
                typeId: this.typeId
              },
              '',
              this.tag('undone'),
              this.tag('activity-undoned')
            )
            .then((frm: VerifyDialogResult) => {
              const resolutionId = frm.resolutionId || null;
              if (resolutionId) {
                if (this.implementations) {
                  for (const item of this.implementations) {
                    item.resolutionId = resolutionId;
                    item.verifyAction = VerifyAction.UNDONE;
                  }
                }
              } else {
                if (this.implementations) {
                  for (const item of this.implementations) {
                    item.verifyAction = VerifyAction.UNDONE;
                    item.progress = 0;
                  }
                }
              }
              this.showProgressBar.emit(true);
              if (this.implementations) {
                this.loader.show();
                let url = `activity/pending/to-undone-many/${this.recordId}`;
                if (resolutionId) {
                  url = `activity/pending/to-undone-many/${this.recordId}/${resolutionId}`;
                }
                this.api
                  .post({
                    url: url,
                    postBody: {
                      comment: frm.inputValue,
                      implementations: this.implementations,
                      verifyAction: VerifyAction.VERIFY
                    },
                    cancelableReq: this.$destroy
                  })
                  .pipe(takeUntil(this.$destroy))
                  .subscribe(
                    (_result) => {
                      if (this.entity) {
                        this.entity.itemAction = VerifyAction.UNDONE;
                      }
                      const message = this.tag('verifiedSucess');
                      this.noticeService.notice(message);
                      this.showProgressBar.emit(false);
                      this.loader.hide();
                      this.attendPending(_result);
                    },
                    () => {
                      this.loader.hide();
                      this.showProgressBar.emit(false);
                    }
                  );
              }
            });
          break;
        case VerifyAction.NOT_APPLY_VERIFY:
        case VerifyAction.ACCEPT_NOT_APPLY: {
          let title = this.tag('not-apply');
          let message = this.tag('activity-not-apply');
          // Para el pendiente {MODULE}-TO-VERIFY-NOT-APPLY
          if (_event === VerifyAction.ACCEPT_NOT_APPLY) {
            title = this.tag('not-apply');
            message = this.tag('activity-not-apply');
          }
          this.openVerifyAttender = true;
          this.cdr.detectChanges();
          this.verifyAttender
            .open(
              {
                recordId: this.recordId,
                typeId: this.typeId
              },
              '',
              title,
              message
            )
            .then((frm: VerifyDialogResult) => {
              this.showProgressBar.emit(true);
              const resolutionId = frm.resolutionId || null;
              if (resolutionId) {
                if (this.implementations) {
                  for (const item of this.implementations) {
                    item.resolutionId = resolutionId;
                    item.verifyAction = VerifyAction.NOT_APPLY_VERIFY;
                  }
                }
              } else {
                if (this.implementations) {
                  for (const item of this.implementations) {
                    item.verifyAction = VerifyAction.NOT_APPLY_VERIFY;
                  }
                }
              }
              if (this.implementations) {
                this.loader.show();
                this.api
                  .post({
                    url: `activity/pending/to-verify-not-many/${this.recordId}`,
                    postBody: {
                      comment: frm.inputValue,
                      implementations: this.implementations,
                      verifyAction: VerifyAction.VERIFY,
                      cancellation: {
                        apply: 0,
                        resolutionId: resolutionId
                      }
                    },
                    cancelableReq: this.$destroy
                  })
                  .pipe(takeUntil(this.$destroy))
                  .subscribe(
                    (_result: ActivtyPendingAttendResult) => {
                      if (this.entity) {
                        this.entity.itemAction = VerifyAction.NOT_APPLY_VERIFY;
                      }
                      this.showProgressBar.emit(false);
                      this.loader.hide();
                      const message = this.tag('verifiedSucess');
                      this.noticeService.notice(message);
                      this.attendPending(_result);
                    },
                    () => {
                      this.loader.hide();
                      this.showProgressBar.emit(false);
                    }
                  );
              }
            });
          break;
        }
        case VerifyAction.REASSIGN_VERIFY: {
          const activityId = this.recordId || this.implementations[0].id;
          this.openToReassignVerify = true;
          this.cdr.detectChanges();
          this.reassignVerify()
            .openDialog(activityId, 'activity')
            .then((frm: ReassignVerifyDialogResult) => {
              this.verifyMany(frm);
            });
          break;
        }
        case VerifyAction.MULTY_REASSIGN_VERIFY:
          if (this.validMultiReassign()) {
            this.dialogService
              .confirm(this.tag('confirmReassign').replace('{count}', this.implementations.length.toString()).replace('{userName}', this.selectedVerifer.description))
              .then(
                () =>
                  this.verifyMany({
                    verifierId: this.reassignMultipleVerify().form.controls.verifier.value.id,
                    verificationDate: this.reassignMultipleVerify().form.controls.verification.value,
                    dialog: null,
                    custom: null,
                    selectValue: null,
                    selectText: null,
                    inputValue: '',
                    reasonChange: this.reassignMultipleVerify().form.controls.reasonChange.value
                  }),
                () => {}
              );
          } else {
            this.reassignMultipleVerify().form.markAllAsTouched();
            this.reassignMultipleVerify().form.controls.verifier.updateValueAndValidity();
            this.reassignMultipleVerify().form.controls.verification.updateValueAndValidity();
            this.reassignMultipleVerify().form.controls.reasonChange.updateValueAndValidity();
            this.noticeService.notice(this.tag('incompleteFormError'));
          }
          break;
      }
    }
  }

  private verifyMany(frm: ReassignVerifyDialogResult) {
    const changeData = {
      verifierId: frm.verifierId,
      verificationDate: frm.verificationDate,
      reasonChange: frm.reasonChange,
      implementations: this.implementations
    };
    this.loader.show();
    if (this.implementations) {
      this.api
        .post({ url: 'activities/activity/reassing-verifier-many', cancelableReq: this.$destroy, postBody: changeData })
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: (_result) => {
            this.showProgressBar.emit(false);
            const message = this.tag('reassignedSucess');
            this.noticeService.notice(message);
            if (this.selectedVerifer != null) {
              this.implementationsGrid().closeDialog();
              if (this.fromPendings) {
                // Actualizar listado de pendientes
                this.refreshAll.emit(true);
              } else {
                // Actualizar detalle de actividad
                this.attendedMany.emit(_result);
              }
              this.showImplementationsGrid = false;
            } else {
              this.attendPending(_result);
            }
            this.loader.hide();
          },
          error: () => {
            this.showProgressBar.emit(false);
            this.loader.hide();
            this.showImplementationsGrid = false;
          }
        });
    }
  }

  private attendPending(_result: ActivtyPendingAttendResult): void {
    // Se revisa si ya se validaron todas.
    const allVerified = this.implementationsGrid().dataGrid().data.length - this.implementationsGrid().dataGrid().selectedRows.length <= 0;
    // Se valida si viene de pendientes.
    if (this.fromPendings) {
      // Actualizar listado de pendientes si ya estan verificadas todas las implementaciones.
      if (allVerified) {
        this.attendedMany.emit(this.entity);
      }
    } else {
      // Actualizar detalle de la actividad.
      this.attendedMany.emit(_result);
    }
    // Cerrar el dialogo que muestra las implementaciones a verificar si no hay pendientes.
    if (allVerified) {
      this.implementationsGrid().closeDialog();
    }
    this.implementationsGrid().updateGridsData();
  }

  public fillImplementations(event: RowSelection<DataMap[]>) {
    this.implementations = event.value;
  }

  public changeResolution(resolucionId: number | string): void {
    if (resolucionId) {
      this.resolutionDescription = this.resolutions?.find((r) => r.value === resolucionId).description;
    }
  }

  public closeImplementationsGridDialog(): void {
    if (this.topZoneEnable) {
      this.topZoneEnable = false;
    }
    this.reassignMultipleVerify().clearData();
    const implementationsGrid = this.implementationsGrid();
    implementationsGrid.value = [];
    this.selectedVerifer = null;
    implementationsGrid.dataUrl = 'pendings/verifiableActivities';
  }

  filterImplementers(value: any) {
    if (value != null) {
      this.selectedVerifer = value;
    } else {
      this.selectedVerifer = null;
    }
    const implementationsGrid = this.implementationsGrid();
    if (implementationsGrid) {
      implementationsGrid.value = [];
      implementationsGrid.forceNewUrl(`pendings/verifiableActivities${this.selectedVerifer != null ? `/${this.selectedVerifer.id}` : ''}`);
    }
  }

  onInput(e) {
    this.formIncomplete.patchValue({
      commentValue: e.target.value
    });
  }

  clearCommentValue(textarea) {
    this.entity.commentValue = '';
    this.formIncomplete.get('commentValue').reset();
    textarea.value = '';
  }

  public onClearImplementersOnVerifyMany(): void {
    this.implementations = [];
  }

  public onClosedReassignVerifyDialog(): void {
    this.openToReassignVerify = false;
  }

  validMultiReassign(): boolean {
    if (this.showImplementationsGrid) {
      return this.reassignMultipleVerify()?.form?.valid || false;
    }
    return false;
  }
}
