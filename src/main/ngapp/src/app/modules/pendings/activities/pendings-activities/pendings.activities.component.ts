import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import type { DropdownMenuItem } from '@/core/dropdown-menu/dropdown-menu.interfaces';
import { FieldDisplayType } from '@/core/field-display/field-display.enums';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import { AppService } from '@/core/services/app.service';
import { BnextLoaderActivationService } from '@/core/services/bnext-loader-activation.service';
import { NoticeService } from '@/core/services/notice.service';
import type { DataMap } from '@/core/utils/data-map';
import * as DateUtil from '@/core/utils/date-util';
import { EnumUtil } from '@/core/utils/enum-util';
import { CommonAction } from '@/core/utils/enums';
import { ErrorHandling } from '@/core/utils/error-handling';
import { cloneObject } from '@/core/utils/object';
import type { PlannerTaskData } from '@/shared/planner/planner-task/planner-task.interfaces';
import { type AfterViewInit, Component, type OnDestroy, type OnInit, inject, output, viewChild } from '@angular/core';
import type { IgxDialogComponent } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs/operators';
import { Session } from 'src/app/core/local-storage/session';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import { TabUtils } from 'src/app/core/utils/tab-utils';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { PendingsUtil } from 'src/app/modules/menu/pendings/pendings-util';
import type { PendingFeasibleDto } from 'src/app/modules/menu/pendings/pendings.interfaces';
import { StopwatchType } from 'src/app/modules/timesheet/timesheet-stopwatch/timesheet-stopwatch.enums';
import type { TimesheetDto } from 'src/app/modules/timesheet/timesheet-widget/timesheet-widget.interfaces';
import { ActivityStatus, CommitmentTask, FillType } from 'src/app/shared/activities/core/activities-core.enums';
import type { ActivtyPendingAttendResult } from 'src/app/shared/activities/core/utils/activity-base.interfaces';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import { PlannerStatus } from '../../../planner/planner-add/planner-add.interfaces';
import { TimesheetWidgetUtils } from '../../../timesheet/timesheet-widget/timesheet-widget.utils';
import { TimesheetService } from '../../../timework/services/timesheet.service';
import { ActivitiesAttenderComponent } from '../activities-attender/activities-attender.component';
import type { AttenderAcceptedData, AttenderProgressChange } from '../activities-attender/activities-attender.interfaces';
import { VerifyAction } from '../activities-pending/activities-pending.enums';
import { type ActionPending, ActivityHistoryType, type ActivityPendingDto, type PendingCancellationDto } from '../activities-pending/activities-pending.interfaces';
import { ActivityInfoConstant } from '../activity-info/activity-info.constant';
import type { ResolutionDialogResult } from '../activity-resolution/activity-resolution.component';
import { ActivityVerifyComponent } from '../activity-verify/activity-verify.component';
import type { IActivityVerifyComponent, VerifyDialogResult } from '../activity-verify/activity-verify.interfaces';
import { CommonActionUtil } from '../common-actions/common-actions-util';
import type { ChangeProgress } from '../fill-type/fill-type.interfaces';
import { ToCompleteUtil } from '../to-complete/to-complete-util';
import { ImplementAction, ImplementStatus } from './../activities-pending/activities-pending.enums';
import type { ActivityPendingEventedDto, ActivityPickedPending } from './pendings.activities.interfaces';

@Component({
  selector: 'app-pendings-activities',
  templateUrl: './pendings.activities.component.html',
  styleUrls: ['./pendings.activities.component.scss'],
  imports: [ActivitiesAttenderComponent]
})
export class PendingsActivitiesComponent extends BnextCoreComponent implements OnInit, OnDestroy, AfterViewInit, i18n {
  loader = inject(BnextLoaderActivationService);
  private api = inject(AppService);
  private noticeService = inject(NoticeService);
  private timesheetService = inject(TimesheetService);

  // El componente existe de manera estática, una sola instancia en pendientes
  // No agregar propiedades compartidas en PendingsActivitiesComponent
  LangConfigActivityInfo = ActivityInfoConstant.LANG_CONFIG;

  override get componentPath(): string {
    return 'modules.activities.core';
  }

  override get tagName(): string {
    return 'activity-info';
  }

  EnumFieldDisplayType = FieldDisplayType;
  EnumFillType = FillType;
  EnumImplementStatus = ImplementStatus;
  EnumImplementAction = ImplementAction;
  EnumActivityStatus = ActivityStatus;
  ImpColumns: GridColumn[];
  implementsTitle = 'Implementación de la serie';
  dataMenuImp: DropdownMenuItem[] = [
    {
      text: 'Verificar como "Realizada"',
      value: VerifyAction.VERIFY,
      iconName: 'done'
    },
    {
      text: 'Cerrar como "No realizada"',
      value: VerifyAction.UNDONE,
      iconName: 'clear'
    },
    {
      text: 'Verificar como "Cancelada"',
      value: VerifyAction.NOT_APPLY_VERIFY,
      iconName: 'block'
    }
  ];
  dataMenuOpen: DropdownMenuItem[] = [
    {
      text: 'Abrir detalle',
      value: CommonAction.OPEN_DETAIL,
      iconName: 'open_in_browser'
    }
  ];

  timesheetWidgedDateSelected: Date;
  dataUrl = '';
  implementations: any[];

  readonly dialog = viewChild<IgxDialogComponent>('dialog');

  readonly activityAttender = viewChild<ActivitiesAttenderComponent>('activityAttender');

  public readonly hideRightPanel = output<boolean>();
  public readonly pendingAttended = output<number>();
  public readonly taskOpenDetail = output<boolean>();
  public readonly showProgressBar = output<boolean>();
  public readonly busyUpdated = output<boolean>();
  public readonly refreshAll = output<any>();

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.lang(this.LangConfigActivityInfo, Module.PLANNER);
    this.lang(this.LangConfigActivityInfo, 'pendings');
    this.lang(this.LangConfigActivityInfo, 'comment');
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
  }

  public pickMenuOption(item: ActivityPickedPending): void {
    const menuItem = item.menuItem;
    const pending = this.activityAttender().buildLocalEventedEntity(item.pending, menuItem.value as ActionPending);
    const comment = item.comment;
    const rollbackData = cloneObject(pending.commentRequired?.rollbackData); // Info del progreso anterior
    // se revisan si las acciones del menú siguen disponibles
    const activityAttender = this.activityAttender();
    switch (menuItem.value) {
      case ImplementAction.FINISH:
      case ImplementAction.PROGRESS:
      case ImplementAction.UNDONE:
      case ImplementAction.NOT_APPLY:
      case ImplementAction.FILL:
      case VerifyAction.VERIFY:
      case VerifyAction.APPLY_VERIFY:
        // se realizan las acciones del menú solo si se tiene permiso
        this.isPendingAvailable(pending).then((available: boolean) => {
          if (available) {
            this.doPendingAction(menuItem, pending, comment, rollbackData);
          }
        });
        break;
      case VerifyAction.VERIFY_IMPLEMENTATIONS:
        this.activityAttender().openVerifyManyDialog(pending, item.pending.recordId, true, item.pending.typeId);
        break;
      case VerifyAction.REASSIGN_VERIFY:
        this.activityAttender().openReassignVerify(item.pending, true);
        break;
      case VerifyAction.MULTY_REASSIGN_VERIFY:
        activityAttender.showImplementationsGrid = true;
        this.cdr.detectChanges();
        activityAttender.openReassignMultipleVerify(true);
        break;
      default:
        // se realizan las acciones del menú
        this.doPendingAction(menuItem, pending, comment);
        break;
    }
  }

  private implementActionFinish(pending: ActivityPendingEventedDto, comment: string, menuItem: DropdownMenuItem, resolutionId?: number) {
    pending.isLoading = true;
    this.acceptProgressItem(pending, 100, comment, resolutionId).then((saved) => {
      this.finalizePending(pending, comment);
      if (saved) {
        let message: string;
        if (this.translateService.containsKeyFrom(ActivityInfoConstant.LANG_CONFIG, `${pending.module}.${menuItem.value}.success-save`)) {
          message = this.translateService.instantFrom(ActivityInfoConstant.LANG_CONFIG, `${pending.module}.${menuItem.value}.success-save`);
        } else {
          message = this.translateService.instantFrom(ActivityInfoConstant.LANG_CONFIG, `${menuItem.value}.success-save`);
        }
        this.noticeService.notice(message);
      } else {
        let message: string;
        if (this.translateService.containsKeyFrom(ActivityInfoConstant.LANG_CONFIG, `${pending.module}.${menuItem.value}.failed-save`)) {
          message = this.translateService.instantFrom(ActivityInfoConstant.LANG_CONFIG, `${pending.module}.${menuItem.value}.failed-save`);
        } else {
          message = this.translateService.instantFrom(ActivityInfoConstant.LANG_CONFIG, `${menuItem.value}.failed-save`);
        }
        this.noticeService.notice(message);
      }
    });
  }
  doPendingAction(menuItem: DropdownMenuItem, pending: ActivityPendingEventedDto, comment?: string, rollbackData?: { key: string; value: any }[]) {
    const plannerTask: Partial<PlannerTaskData> = pending.plannerTask || {};
    pending.itemAction = menuItem.value as ActionPending;
    switch (menuItem.value) {
      case ImplementAction.FINISH:
        this.activityHasEnableStopwatch(
          pending,
          comment,
          ImplementAction.FINISH,
          this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatchFinishAlert'),
          menuItem
        );
        break;
      case ImplementAction.PROGRESS:
        if (typeof comment === 'undefined') {
          this.saveProgress(pending);
        } else {
          this.acceptProgressItem(pending, pending.progress, comment).then((result) => {
            if (!result && rollbackData) {
              for (const kv of rollbackData) {
                pending[kv.key] = kv.value;
              }
              this.cdr.detectChanges();
            }
          });
        }
        break;
      case ImplementAction.FILL:
        this.fillForm(pending);
        break;
      case ImplementAction.UNDONE:
        this.activityHasEnableStopwatch(pending, comment, ImplementAction.UNDONE, this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatchUndoneAlert'));
        break;
      case ImplementAction.NOT_APPLY:
        pending.progressBackup = pending.progress;
        this.activityHasEnableStopwatch(
          pending,
          comment,
          ImplementAction.NOT_APPLY,
          this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatchNotApplyAlert')
        );
        break;
      case ImplementAction.OPEN_FORM:
        this.menuService.navigateLegacy(
          `v.request.survey.fill.view?id=IMPLEMENT&tabId=${TabUtils.getTabId()}&requestMode=PREVIEW&activityId=${pending.recordId}&fromPath=pendings&pendingRecordId=${pending.pendingRecordId}`
        );
        break;
      case VerifyAction.OPEN_FORM_VERIFY:
        this.menuService.navigateLegacy(
          `v.request.survey.fill.view?id=VERIFY&tabId=${TabUtils.getTabId()}&requestMode=PREVIEW&activityId=${pending.recordId}&fromPath=pendings`
        );
        break;
      case ImplementAction.OPEN_FLOW:
      case VerifyAction.OPEN_FLOW:
        this.menuService.navigateLegacy(`v.sequence.detail.list.view?sourcePage=angular_pendings&outstandingSurveyId=${pending.outstandingSurveyId}`);
        break;
      case VerifyAction.NOT_APPLY_VERIFY:
      case VerifyAction.ACCEPT_NOT_APPLY: // Para el pendiente {MODULE}-TO-VERIFY-NOT-APPLY
        this.activityAttender().openDialog(pending, comment);
        break;
      case VerifyAction.APPLY_VERIFY:
        this.saveVerifyApply(pending, comment);
        break;
      case VerifyAction.INCOMPLETE:
        this.saveProgress(pending, 'Motivo para regresar a "Por realizar"', pending.progress > 10 ? pending.progress - 10 : 5);
        break;
      case VerifyAction.VERIFY:
        this.saveVerify(pending, comment);
        break;
      case CommonAction.OPEN_DETAIL: {
        let path = '';
        if (pending.fillType === FillType.PLANNER_TASK) {
          path = `menu/planner/task-detail/${pending.recordId}`;
        } else if (pending.commitmentTask === CommitmentTask.VERIFICATION) {
          const showimpl = true;
          path = `menu/activities/${pending.module}/${pending.recordId}?impl=${showimpl}`;
        } else {
          path = `menu/activities/${pending.module}/${pending.recordId}`;
        }
        this.menuService.navigate(path, pending.module);
        this.activityAttender().detachEventedEntity(pending);
        this.taskOpenDetail.emit(true);
        break;
      }
      case CommonAction.CAPTURE_TIME:
        this.setBusyPending(pending);
        this.activityHasEnableStopwatch(
          pending,
          comment,
          ImplementAction.CAPTURE_TIME,
          this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatchCaptureTimeAlert')
        );
        break;
      case ImplementAction.STOP_STOPWATCH:
      case ImplementAction.START_STOPWATCH:
        if (menuItem.value === ImplementAction.START_STOPWATCH) {
          const dto: TimesheetDto = TimesheetWidgetUtils.getTimesheetInstance(pending.pendingRecordId, new Date(), {
            clientId: plannerTask.clientId || null,
            clientDescription: plannerTask.clientName || null,
            plannerId: plannerTask.plannerId || null,
            plannerDescription: plannerTask.plannerName || null,
            activityId: +plannerTask.value || null,
            activityDescription: plannerTask.text || null,
            clientRecordLocked: !!plannerTask.clientId,
            plannerRecordLocked: !!plannerTask.plannerId,
            taskRecordLocked: !!(plannerTask.plannerId !== null && plannerTask.value !== null),
            pendingRecordId: pending.pendingRecordId,
            activityPlannedId: pending.recordId || null,
            activityPlannedCode: pending.code || null,
            activityPlannedDescription: pending.description || null,
            activityPlannedModule: pending.module || null,
            hasEnableStopwatch: false,
            plannerStatus: plannerTask.plannerStatus || null
          });
          this.api
            .post({ url: 'timesheet/dialog/task-activities/1', cancelableReq: this.$destroy, postBody: {} })
            .pipe(takeUntil(this.$destroy))
            .subscribe((dataSource) => {
              const activityPlanned = dataSource.data.find((f) => f.activityPlannedId === pending.recordId);
              if (activityPlanned) {
                dto.clientId = activityPlanned.clientId;
                dto.clientDescription = activityPlanned.clientDescription;
                dto.plannerId = activityPlanned.plannerId;
                dto.plannerDescription = activityPlanned.plannerDescription;
                dto.activityId = activityPlanned.taskId;
                dto.activityDescription = activityPlanned.taskDescription;
              }
              const start = TimesheetWidgetUtils.fixDate(DateUtil.safe(new Date()));
              if (start) {
                start.setSeconds(0);
                start.setMilliseconds(0);
              }
              const end = DateUtil.trunc(new Date());
              dto.start = start;
              dto.end = end;
              dto.hasEnableStopwatch = true;
              dto.tags = [pending.code];
              dto.registry = ' ';
              dto.stopwatchType = StopwatchType.AUTOMATIC;
              const startDate = DateUtil.safe(dto.start);
              const endDate = DateUtil.safe(dto.end);
              endDate.setHours(startDate.getHours());
              endDate.setMinutes(startDate.getMinutes() + 5);
              dto.end = endDate;
              dto.stopwatchLocalTimeStart = new Date();
              this.saveStopwatch(dto);
            });
        } else {
          this.timesheetService.stopStopwatchFromPendingListService(true);
        }
        break;
    }
    this.showProgressBar.emit(false);
  }

  private saveStopwatch(dto: TimesheetDto) {
    this.api.post({ url: 'timesheet/save', cancelableReq: this.$destroy, postBody: dto }).subscribe(
      (result: TimesheetDto) => {
        dto.timesheetId = result.timesheetId;
        dto.stopwatchLocalTimeStart = result.stopwatchLocalTimeStart;
        this.timesheetService.startNewStopwatch('', dto.tags as [], TimesheetWidgetUtils.fixDate(DateUtil.safe(new Date())), dto);
        this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.successCreate'));
        this.timesheetService.updateUIStopwatchService(true);
      },
      (error) => {
        if (error.status === 409) {
          switch (error.error) {
            case 'dates-spliced':
              ErrorHandling.notifyMessage(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.datesSpliced'), this.navLang);
              break;
            case 'outof-limit':
              ErrorHandling.notifyMessage(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.dateOutOfLimit'), this.navLang);
              break;
            default:
              if (error.error?.stopwatch_running_message === 'stopwatch-dates-spliced') {
                const registryDate = error.error.registry[0].date;
                ErrorHandling.notifyMessage(
                  this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatch-running', { date: DateUtil.format(registryDate, 'DD/MM/YYYY') }),
                  this.navLang
                );
                return;
              }
              ErrorHandling.notifyError(error, this.navLang);
              break;
          }
        } else {
          ErrorHandling.notifyError(error, this.navLang);
        }
      }
    );
  }

  isPendingAvailable(pending: ActivityPendingEventedDto): Promise<boolean> {
    return new Promise<boolean>((available) => {
      this.api
        .get({ cancelableReq: this.$destroy, url: `pendings/is-feasible/${pending.pendingTypeCode}/${pending.pendingRecordId}` })
        .subscribe((result: PendingFeasibleDto) => {
          this.cdr.detectChanges();
          switch (result.valid) {
            case 'VALID':
              available(true);
              return;
            case 'BUSY':
              this.dialogService.info(`La actividad de clave ${pending.code} ya está siendo atendido por ${result.blockedBy}.`);
              break;
            default: {
              let message;
              switch (result.status) {
                case 'INACTIVE':
                case 'ATTENDED':
                  message = `La actividad con clave ${pending.code} ya fue atendida.`;
                  break;
                case 'HISTORY':
                  message = '' + 'Usted ya no es el responsable de esta actividad ' + 'debido a que ya fue escalado.';
                  break;
              }
              this.noticeService.notice(message);
              break;
            }
          }
          available(false);
        });
    });
  }

  onAcceptedAttenderDialog(data: AttenderAcceptedData): void {
    const acceptedData = data;
    switch (acceptedData.activity.itemAction) {
      case VerifyAction.INCOMPLETE:
      case ImplementAction.PROGRESS:
        this.acceptProgressItem(acceptedData.activity, acceptedData.progress, acceptedData.comment, acceptedData.activity.resolutionId, acceptedData.commitmentDate);
        break;
      case ImplementAction.NOT_APPLY:
        acceptedData.activity.busy = true;
        this.showProgressBar.emit(true);
        this.saveNotApplyComment(acceptedData.activity, acceptedData.activity.commentValue);
        break;
      case VerifyAction.NOT_APPLY_VERIFY:
      case VerifyAction.ACCEPT_NOT_APPLY:
        acceptedData.activity.busy = true;
        this.showProgressBar.emit(true);
        this.saveVerifyNotApplyComment(acceptedData.activity, acceptedData.activity.commentValue);
        break;
    }
    return;
  }

  /**
   * Método que actualiza emite los eventos para actualizar el listado de pendientes de verificación en serie
   * @param pending
   */
  onAttendedManyAttenderDialog(pending: ActivityPendingEventedDto): void {
    this.showProgressBar.emit(false);
    this.pendingAttended.emit(pending.pendingRecordId);
    this.hideRightPanel.emit(true);
    if (pending != null) {
      pending.isLoading = false;
      pending.hidden = true;
      this.activityAttender().detachEventedEntity(pending);
    }
    this.cdr.detectChanges();
  }
  onRefreshAll() {
    this.refreshAll.emit(true);
  }
  acceptDialogCancellationItem(): void {}

  private acceptProgressItem(activity: ActivityPendingEventedDto, progress?: number, comment?: string, resolutionId?: number, commitmentDate?: Date): Promise<boolean> {
    activity.busy = true;
    if (progress) {
      activity.progress = progress;
    }
    if (this.isProgressDialogOpen()) {
      if (!activity.commentValue) {
        this.noticeService.notice('Capture un comentario');
        return null;
      }
      if (activity.progress === 0 && activity.itemAction !== VerifyAction.INCOMPLETE) {
        this.noticeService.notice('Capture un porcentaje de avance');
        return null;
      }
      this.activityAttender().closeDialog();
    }
    if (typeof comment === 'undefined') {
      comment = activity.commentValue || '';
      activity.commentValue = '';
    }
    if (activity.apply === 1) {
      // Solo se selecciona una resolución cuando se capturó avance a la actividad y llegó a 100
      if (activity.progress === 100 && !resolutionId) {
        return this.activityAttender()
          .openResolutionDialog(activity.typeId, ImplementAction.PROGRESS, comment, null)
          .then(
            (value: ResolutionDialogResult) => {
              return this.sendProgress(activity, activity.itemAction, activity.progress, comment, value.resolutionId);
            },
            (reason: any) => {
              console.warn('Cancel resolution dialog, unsaving progress: ', reason);
              activity.busy = false;
              activity.progress = activity.progressBackup;
              this.showProgressBar.emit(false);
              return null;
            }
          );
      }
      if (activity.itemAction === VerifyAction.INCOMPLETE) {
        return this.sendProgress(activity, activity.itemAction, activity.progress, comment, resolutionId, commitmentDate);
      }
      return this.sendProgress(activity, activity.itemAction, activity.progress, comment, resolutionId);
    }
    return this.sendProgress(activity, ImplementAction.NOT_APPLY, activity.progress, comment);
  }

  private isProgressDialogOpen(): boolean {
    return this.dialog()?.isOpen || false;
  }

  sendProgress(activity: ActivityPendingEventedDto, action: string, progress: number, comment: string, resolutionId?: number, commitmentDate?: Date): Promise<boolean> {
    switch (action) {
      case ImplementAction.NOT_APPLY:
        activity.isLoading = true;
        this.showProgressBar.emit(true);
        return new Promise<boolean>((saved) => {
          const swapActivities = activity.swapActivities ? activity.swapActivities : [];
          const notApplyData: PendingCancellationDto = {
            apply: activity.apply,
            cancellationReason: activity.cancellationReason,
            swapActivities: swapActivities,
            comment: comment
          };
          const s = this.api.post({ url: CommonActionUtil.CONTROLLER.NOT_APPLY + activity.recordId, cancelableReq: this.$destroy, postBody: notApplyData }).subscribe(
            (result: ActivtyPendingAttendResult) => {
              saved(true);
              if (result?.changes?.availableActions) {
                activity.availableActions = result?.changes?.availableActions;
              }
              let message: string;
              if (activity.module === Module.PLANNER) {
                const plannerTags = this.i18n[Module.PLANNER] as DataMap;
                const pendings = plannerTags.pendings as DataMap<string>;
                message = pendings.discardedSuccess;
              } else {
                const pendings = this.i18n.pendings as DataMap<string>;
                message = pendings.discardedSuccess;
              }
              this.finalizePending(activity, comment, true, message);
              activity.busy = false;
              this.showProgressBar.emit(false);
              this.noticeService.notice(message);
              s.unsubscribe();
            },
            (_error) => {
              this.finalizePending(activity, comment, false);
              activity.busy = false;
              this.showProgressBar.emit(false);
              saved(false);
            }
          );
        });
      case VerifyAction.INCOMPLETE:
        if (!EnumUtil.isEqual(activity.commitmentTask, CommitmentTask.VERIFICATION)) {
          console.error('Invalid activity, only VERIFICATION registries are allowed');
          return null;
        }
        activity.isLoading = true;
        this.showProgressBar.emit(true);
        return new Promise<boolean>((saved) => {
          const resolutionId = activity.resolutionId ? `/${activity.resolutionId}` : '';
          const s = this.api
            .post({
              url: `activity/pending/to-verify/${activity.recordId}${resolutionId}`,
              postBody: {
                comment: comment,
                implementations: null,
                verifyAction: VerifyAction.INCOMPLETE,
                progress: progress || 0,
                commitmentDate: commitmentDate
              },
              cancelableReq: this.$destroy
            })
            .subscribe(
              (_result) => {
                saved(true);
                this.finalizePending(activity, comment, true, `Retrocedio a ${progress}%`);
                activity.busy = false;
                this.showProgressBar.emit(false);
                this.noticeService.notice('root.common.message.regression-saved');
                s.unsubscribe();
              },
              (_error) => {
                this.finalizePending(activity, comment, false);
                activity.busy = false;
                this.showProgressBar.emit(false);
                saved(false);
              }
            );
        });
      case CommonAction.EDIT:
        // do edition
        break;
      case ImplementAction.FINISH:
      case ImplementAction.PROGRESS:
        activity.isLoading = true;
        this.showProgressBar.emit(true);
        return new Promise<boolean>((saved) => {
          const s = this.api
            .post({
              url: `activity/pending/to-complete/${activity.recordId}/${progress}`,
              cancelableReq: this.$destroy,
              postBody: { comment: comment, resolutionId: resolutionId }
            })
            .subscribe(
              (_result) => {
                saved(true);
                const lastAction = this.translate.instantFrom(PendingsUtil.LANG_CONFIG, 'lastActionDescription', {
                  progress: progress,
                  comment: comment
                });
                this.finalizePending(activity, comment, progress === 100, lastAction);
                activity.busy = false;
                this.showProgressBar.emit(false);
                this.noticeService.notice('root.common.message.progress-saved');
                s.unsubscribe();
              },
              (_error) => {
                this.finalizePending(activity, comment, false);
                activity.busy = false;
                this.showProgressBar.emit(false);
                saved(false);
              }
            );
        });
    }
    return null;
  }

  onProgressDialogChange(data: AttenderProgressChange): void {
    this.progressChange(data.progress, data.activity);
  }

  progressChange(progress: ChangeProgress, activity: ActivityPendingEventedDto): void {
    if (!activity.customEvents) {
      activity = this.activityAttender().buildLocalEventedEntity(activity);
    }
    activity.progress = progress.value;
    activity.apply = progress.apply ? 1 : 0;
    if (activity?.commentRequired && !activity.commentRequired.value) {
      activity.onProgressUpdated.next(activity.code);
    }
    this.cdr.detectChanges();
  }

  public progressComment(activity: ActivityPendingEventedDto, comment: string) {
    if (typeof activity !== 'undefined') {
      activity = this.activityAttender().buildLocalEventedEntity(activity);
    }
    if (!comment || comment === '') {
      activity.onProgressHasComment.next(false);
    }
  }

  private saveUndone(pending: ActivityPendingEventedDto, comment?: string) {
    if (typeof comment === 'undefined') {
      this.activityAttender()
        .openResolutionDialog(pending.typeId, ImplementAction.UNDONE, '', 'Captura el motivo para marcar la actividad como "No realizada"')
        .then(
          (value: ResolutionDialogResult) => {
            pending.busy = true;
            this.showProgressBar.emit(true);
            this.saveUndoneComment(pending, value.inputValue, value.resolutionId);
          },
          (reason: any) => {
            console.warn('Cancel resolution dialog, unsaving activity undone: ', reason);
          }
        );
    } else {
      this.showProgressBar.emit(true);
      this.saveUndoneComment(pending, comment);
    }
  }

  private saveUndoneComment(pending: ActivityPendingEventedDto, comment: string, resolutionId?: number) {
    const activityId = pending.recordId;
    pending.isLoading = true;
    this.api
      .post({
        url: `activity/pending/to-undone/${activityId}`,
        cancelableReq: this.$destroy,
        postBody: { comment: comment, resolutionId: resolutionId }
      })
      .pipe(takeUntil(this.$destroy))
      .subscribe(
        (result) => {
          console.log(result);
          this.finalizePending(pending, comment);
          this.showProgressBar.emit(false);
        },
        () => {
          pending.isLoading = false;
          this.showProgressBar.emit(false);
        }
      );
  }

  private saveNotApplyComment(pending: ActivityPendingEventedDto, comment: string) {
    const activityId = pending.recordId;
    pending.isLoading = true;
    pending.apply = 0;
    const swapActivities = pending.swapActivities ? pending.swapActivities : [];
    const notApplyData: PendingCancellationDto = {
      apply: pending.apply,
      cancellationReason: pending.cancellationReason,
      swapActivities: swapActivities,
      comment: comment,
      resolutionId: pending.resolutionId
    };
    const s = this.api.post({ url: CommonActionUtil.CONTROLLER.NOT_APPLY + +activityId, cancelableReq: this.$destroy, postBody: notApplyData }).subscribe(
      (result: ActivtyPendingAttendResult) => {
        console.log(result);
        if (result?.changes?.availableActions) {
          pending.availableActions = result?.changes?.availableActions;
        }
        let message: string;
        if (pending.module === Module.PLANNER) {
          const plannerTags = this.i18n[Module.PLANNER] as DataMap;
          const pendings = plannerTags.pendings as DataMap;
          message = pendings.discardedSuccess;
        } else {
          const pendings = this.i18n.pendings as DataMap<string>;
          message = pendings.discardedSuccess;
        }
        this.noticeService.notice(message);
        this.finalizePending(pending, comment);
        s.unsubscribe();
      },
      () => {
        pending.isLoading = false;
        this.showProgressBar.emit(false);
      }
    );
    this.showProgressBar.emit(false);
  }

  private saveVerifyNotApplyComment(pending: ActivityPendingEventedDto, comment: string) {
    pending.isLoading = true;
    const swapActivities = pending.swapActivities ? pending.swapActivities : [];
    const notApplyData: PendingCancellationDto = {
      apply: 0,
      cancellationReason: pending.cancellationReason,
      swapActivities: swapActivities,
      comment: comment,
      implementations: null,
      resolutionId: pending.resolutionId
    };
    const s = this.api.post({ url: CommonActionUtil.CONTROLLER.NOT_APPLY_VERIFY + pending.recordId, cancelableReq: this.$destroy, postBody: notApplyData }).subscribe(
      (_result: ActivtyPendingAttendResult) => {
        this.finalizePending(pending, comment);
        this.showProgressBar.emit(false);
        s.unsubscribe();
      },
      () => {
        pending.isLoading = false;
        this.showProgressBar.emit(false);
      }
    );
  }

  private saveVerifyApply(pending: ActivityPendingEventedDto, comment: string) {
    if (typeof comment === 'undefined') {
      this.dialogService
        .input(
          this.translateService.instantFrom(ActivityInfoConstant.LANG_CONFIG, 'apply'),
          'root.common.button.ok',
          'root.common.button.cancel',
          'Rechazar la cancelación'
        )
        .then((frm: DialogResult) => {
          pending.busy = true;
          this.showProgressBar.emit(true);
          this.saveVerifyApplyComment(pending, frm.inputValue);
        });
    } else {
      this.saveVerifyApplyComment(pending, comment);
    }
  }

  private saveVerifyApplyComment(pending: ActivityPendingEventedDto, comment: string) {
    const activityId = pending.recordId;
    pending.isLoading = true;
    const s = this.api
      .post({
        url: CommonActionUtil.CONTROLLER.APPLY_VERIFY + +activityId,
        postBody: { apply: 1, comment: comment } /* PendingVerifyNotApplyDto */,
        cancelableReq: this.$destroy
      })
      .subscribe({
        next: (result: ActivtyPendingAttendResult) => {
          console.log(result);
          this.finalizePending(pending, comment);
          this.showProgressBar.emit(true);
          s.unsubscribe();
        },
        error: () => {
          pending.isLoading = false;
          this.showProgressBar.emit(false);
        }
      });
  }

  public saveProgress(activity: ActivityPendingEventedDto, title?: string, overrideProgress?: number) {
    activity.commentValue = '';
    activity.progressBackup = activity.progress;
    if (overrideProgress) {
      activity.progress = overrideProgress;
    }
    this.activityAttender().openDialog(activity, null, title);
  }

  get verifyAttender():
    | IActivityVerifyComponent
    | {
        open: (activity: Partial<ActivityPendingDto>, comment?: string) => Promise<DialogResult>;
      } {
    return ActivityVerifyComponent.getVerifyAttenter(this.activityAttender(), this.dialogService);
  }

  public saveVerify(pending: ActivityPendingEventedDto, comment: string) {
    this.verifyAttender.open(pending, comment).then((frm: DialogResult | VerifyDialogResult) => {
      pending.busy = true;
      pending.resolutionId = (frm as VerifyDialogResult).resolutionId;
      this.showProgressBar.emit(true);
      this.saveVerifyComment(pending, frm.inputValue, pending.resolutionId);
    });
  }

  public saveVerifyComment(pending: ActivityPendingEventedDto, comment: string, resolutionId: number) {
    const activityId = pending.recordId;
    pending.isLoading = true;
    const progress = !pending.childCount ? 100 : pending.progress;
    let verifyUrl = `activity/pending/to-verify/${activityId}`;
    if (resolutionId) {
      verifyUrl += `/${resolutionId}`;
    }
    const s = this.api
      .post({
        url: verifyUrl,
        postBody: {
          comment: comment,
          implementations: null,
          verifyAction: VerifyAction.VERIFY,
          progress: progress
        },
        cancelableReq: this.$destroy
      })
      .subscribe({
        next: (_result) => {
          this.finalizePending(pending, comment);
          this.showProgressBar.emit(false);
          const pendings = this.i18n.pendings as DataMap<string>;
          const message = pendings.verifiedSucess;
          this.noticeService.notice(message);
          s.unsubscribe();
        },
        error: () => {
          pending.isLoading = false;
          this.showProgressBar.emit(false);
        }
      });
  }

  private finalizePending(pending: ActivityPendingEventedDto, comment: string, setHide = true, actionMessage?: string) {
    // Se actualiza el estado del pendiente
    if (actionMessage) {
      pending.lastAction = actionMessage;
      pending.listItemClassName = 'last-action-active';
    }
    // Se oculta el pendiente y se apaga "loader"
    if (setHide) {
      pending.hidden = true;
      this.activityAttender().detachEventedEntity(pending);
      this.showProgressBar.emit(false);
      this.pendingAttended.emit(pending.recordId);
      this.hideRightPanel.emit(true);
    }
    pending.isLoading = false;
    // Se agrega el comentaio que se guardó
    if (!pending.history) {
      pending.history = [];
    }
    pending.history.push({
      id: -1,
      description: comment,
      lastModifiedDate: new Date(),
      lastModifiedBy: Session.getUserName(),
      progress: pending.progress,
      apply: pending.apply,
      stage: null,
      createdBy: Session.getUserId(),
      activityHistoryType: ActivityHistoryType.FINISH_IMPLEMENTATION
    });
    if (pending.commentRequired) {
      pending.commentRequired.value = null;
    }
    this.activityAttender().detachEventedEntity(pending);
    this.cdr.detectChanges();
  }

  private fillForm(pending: ActivityPendingEventedDto): void {
    ToCompleteUtil.fillForm(this.menuService, pending, 'pendings');
  }

  getDataMenuImp(_row: any): DropdownMenuItem[] {
    return this.dataMenuImp;
  }

  getDataMenuOpen(_row: any): DropdownMenuItem[] {
    return this.dataMenuOpen;
  }

  public langReady(): void {
    this.implementsTitle = this.tag('implementsTitle');
    for (const item of this.dataMenuImp) {
      switch (item.value) {
        case VerifyAction.VERIFY:
          item.text = this.tag('actions.VERIFY');
          break;
        case VerifyAction.UNDONE:
          item.text = this.tag('actions.UNDONE');
          break;
        case VerifyAction.NOT_APPLY_VERIFY:
          item.text = this.tag('actions.NOT_APPLY_VERIFY');
          break;
        case VerifyAction.ACCEPT_NOT_APPLY:
          item.text = this.tag('actions.ACCEPT_NOT_APPLY');
          break;
        default:
          console.warn(`Translate is not configurated for ${item.text} with value ${item.value}`);
          break;
      }
    }
    for (const item of this.dataMenuOpen) {
      item.text = this.translate.instantFrom(PendingsUtil.LANG_CONFIG, 'action.OPEN_DETAIL');
    }
  }

  private releasePending(pending: ActivityPendingEventedDto): void {
    pending.busy = false;
    this.busyUpdated.emit(true);
  }

  private setBusyPending(pending: ActivityPendingEventedDto): void {
    pending.busy = true;
    this.busyUpdated.emit(true);
  }

  private activityHasEnableStopwatch(pending: ActivityPendingEventedDto, comment: string, action: string, message: string, menuItem?: DropdownMenuItem): void {
    const isTimesheetAvailable = Session.hasService(ProfileServices.TS_REGISTER_PLANNED) || Session.hasService(ProfileServices.TS_REGISTER_UNPLANNED);
    if (!isTimesheetAvailable) {
      this.executeActivityAction(pending, comment, action, menuItem);
      return;
    }
    this.api.get({ url: `timesheet/activity-has-stopwatch-running/${pending.id}`, cancelableReq: this.$destroy }).subscribe((response) => {
      if (response) {
        this.releasePending(pending);
        this.dialogService.info(message);
        return;
      }
      this.executeActivityAction(pending, comment, action, menuItem);
    });
  }

  private executeActivityAction(pending: ActivityPendingEventedDto, comment: string, action: string, menuItem?: DropdownMenuItem): void {
    const plannerTask: Partial<PlannerTaskData> = pending.plannerTask || {};
    switch (action) {
      case ImplementAction.CAPTURE_TIME: {
        const plannerRecordLocked = plannerTask.plannerStatus === PlannerStatus.ACTIVE;
        const taskRecordLocked = !!plannerTask.plannerId && !!plannerTask.value && !!plannerTask.value && plannerRecordLocked;
        this.timesheetService
          .openNewTimesheet(
            '',
            [pending.code],
            {
              clientId: plannerTask.clientId || null,
              clientDescription: plannerTask.clientName || null,
              plannerId: plannerTask.plannerId || null,
              plannerDescription: plannerTask.plannerName || null,
              activityId: +plannerTask.value || null,
              activityDescription: plannerTask.text || null,
              clientRecordLocked: !!plannerTask.clientId,
              plannerRecordLocked: !!(pending.plannerTask && plannerRecordLocked),
              taskRecordLocked: !!(pending.plannerTask && taskRecordLocked),
              pendingRecordId: pending.pendingRecordId,
              activityPlannedId: pending.recordId || null,
              activityPlannedCode: pending.code || null,
              activityPlannedDescription: pending.description || null,
              activityPlannedModule: pending.module || null,
              hasEnableStopwatch: false,
              plannerStatus: plannerTask.plannerStatus || null
            },
            () => {
              this.releasePending(pending);
            }
          )
          .then(
            (value: TimesheetDto) => {
              if (this.debug()) {
                console.log('Opened new timesheet.', value);
              }
              this.releasePending(pending);
            },
            (value: TimesheetDto) => {
              if (this.debug()) {
                console.log('Cancelled open new timesheet.', value);
              }
              this.releasePending(pending);
              this.cdr.detectChanges();
            }
          );
        break;
      }
      case ImplementAction.UNDONE:
        this.saveUndone(pending, comment);
        break;
      case ImplementAction.NOT_APPLY:
        this.activityAttender().openDialog(pending, comment);
        break;
      case ImplementAction.FINISH:
        if (typeof comment === 'undefined') {
          let title: string;
          if (this.translateService.containsKeyFrom(ActivityInfoConstant.LANG_CONFIG, `${pending.module}.${menuItem.value}.title`)) {
            title = this.translateService.instantFrom(ActivityInfoConstant.LANG_CONFIG, `${pending.module}.${menuItem.value}.title`);
          } else {
            title = this.translateService.instantFrom(ActivityInfoConstant.LANG_CONFIG, `${menuItem.value}.title`);
          }
          this.activityAttender()
            .openResolutionDialog(pending.typeId, ImplementAction.FINISH, comment, title)
            .then((value: ResolutionDialogResult) => {
              this.implementActionFinish(pending, value.inputValue, menuItem, value.resolutionId);
            });
        } else {
          this.implementActionFinish(pending, comment, menuItem);
        }
        break;
    }
  }
}
