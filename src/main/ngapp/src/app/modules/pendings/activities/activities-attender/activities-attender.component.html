@if (entity && openEntityDialog) {
  <igx-dialog
    #dialog
    [title]="entity.dialogTitle"
    [leftButtonLabel]="'root.common.button.cancel' | translate: this"
    [rightButtonLabel]="'root.common.button.ok' | translate: this"
    [closeOnOutsideSelect]="false"
    (leftButtonSelect)="onCancelDialog()"
    (rightButtonSelect)="onAcceptDialog()"
    (closed)="onClosedDialog()"
    (opening)="onOpeningDialog(entity.itemAction)"
  >
    @switch (entity.itemAction) {
      @case (EnumImplementAction.PROGRESS) {
        <ng-container *ngTemplateOutlet="progressForm"></ng-container>
      }
      @case (EnumImplementAction.NOT_APPLY) {
        <ng-container *ngTemplateOutlet="cancellationForm"></ng-container>
      }
      @case (EnumVerifyAction.NOT_APPLY_VERIFY) {
        <ng-container *ngTemplateOutlet="cancellationForm"></ng-container>
      }
      @case (EnumVerifyAction.ACCEPT_NOT_APPLY) {
        <ng-container *ngTemplateOutlet="cancellationForm"></ng-container>
      }
      @case (EnumVerifyAction.INCOMPLETE) {
        <ng-container *ngTemplateOutlet="incompleteForm"></ng-container>
      }
      @default {
        Not supported item action {{ entity.itemAction || 'not-selected' }}
      }
    }
  </igx-dialog>
}
<ng-template #progressForm>
  <form [formGroup]="formIncomplete">
    <igx-input-group [ngClass]="displayDensityClass" type="box" #inputGroup class="text-input-dialog" theme="material">
      <label igxLabel for="commentValue">{{ entity.commentLabel }}</label>
      <textarea
        #commentValue
        (input)="onInput($event)"
        autoresize
        class="large-field"
        igxInput
        name="commentValue"
        type="text"
        maxlength="255"
        formcontrolname="commentValue"
      ></textarea>
      <igx-suffix>
        <igx-icon (click)="clearCommentValue(commentValue)">clear</igx-icon>
      </igx-suffix>
    </igx-input-group>
  </form>
  <div class="progress-container">
    @if (entity.progressBackup && entity.fillType === 1) {
      <igx-badge type="success" class="last-action-badge" [value]="'Anterior: ' + entity.progressBackup + '%'"></igx-badge>
    }
    <app-fill-type
      [progress]="entity.progress"
      [displayDensity]="displayDensity"
      [notApplyAccess]="entity.notApplyAccess"
      (progressChange)="onProgressDialogChange($event)"
      [fillType]="entity?.fillType || null"
    ></app-fill-type>
  </div>
</ng-template>
<ng-template #incompleteForm>
  <form [formGroup]="formIncomplete">
    @if (entity?.mustUpdateImplementationAtReturn) {
      <div class="mb-1 commitment-field">
        <igx-date-picker
          #commitmentDate
          [cancelButtonLabel]="'root.common.button.cancel' | translate: this"
          [disabledDates]="disabledDates"
          [disabled]="entity.authorEditImplementation === 0"
          [ngClass]="displayDensityClass"
          [displayFormat]="'dd/MM/yyyy'"
          [inputFormat]="'dd/MM/yyyy'"
          [mode]="datePickerMode"
          [required]="true"
          [todayButtonLabel]="'root.common.button.today' | translate: this"
          formControlName="commitmentDate"
          name="commitmentDate"
        >
          <label igxLabel>{{ 'root.common.field.commitmentDate' | translate: this }}</label>
        </igx-date-picker>
      </div>
    }
    <igx-input-group [ngClass]="displayDensityClass" type="box" #inputGroup class="text-input-dialog" theme="material">
      <label igxLabel for="commentValue">{{ entity.commentLabel }}</label>
      <textarea
        #commentValue
        (input)="onInput($event)"
        autoresize
        class="large-field"
        igxInput
        name="commentValue"
        type="text"
        maxlength="255"
        formcontrolname="commentValue"
      ></textarea>
      <igx-suffix>
        <igx-icon (click)="clearCommentValue(commentValue)">clear</igx-icon>
      </igx-suffix>
    </igx-input-group>
  </form>
  <div class="progress-container">
    @if (entity.progressBackup && entity.fillType === 1) {
      <igx-badge type="success" class="last-action-badge" [value]="'Anterior: ' + entity.progressBackup + '%'"></igx-badge>
    }
    <app-fill-type
      [progress]="entity.progress"
      [displayDensity]="displayDensity"
      [notApplyAccess]="entity.notApplyAccess"
      (progressChange)="onProgressDialogChange($event)"
      [fillType]="entity?.fillType || null"
    ></app-fill-type>
  </div>
</ng-template>
<ng-template #cancellationForm>
  @if (entity.taskStatus === activityTaskStatusEnum.NOT_APPLY_BY_IMPLEMENTER && entity.activityResolution !== null && entity.activityResolution !== undefined) {
    <div class="implementer-resolution-info">
      @if (entity.activityResolution !== null && entity.activityResolution !== undefined) {
        <span
          [innerHTML]="
            'root.common.message.responsible-resolution-label'
              | translate: this : { userName: userNameInterpolate, activityResolution: activityResolutionInterpolate }
              | safeHtml
          "
        ></span>
      }
    </div>
  }
  @if (entity.module === 'PLANNER') {
    <div class="dialogScrollableMessage" [innerHTML]="entity.dialogMessage"></div>
  }
  @if (entity.module !== 'PLANNER') {
    <div class="cancellation-container">
      <app-select
        [(ngModel)]="entity.cancellationReason"
        [label]="entity.cancelReasonLabel"
        [data]="entity.cancelReasons"
        valueKey="value"
        displayKey="text"
      ></app-select>
      @if (entity.cancellationReason !== 'NONE') {
        <app-swap-activities (changedValue)="onSwapActivitiesChanged($event)" [module]="entity.module" [entityId]="entity.recordId"></app-swap-activities>
      }
      @if (resolutions.length > 0) {
        <app-select
          (change)="changeResolution($event)"
          [(ngModel)]="entity.resolutionId"
          [data]="resolutions"
          [displayDensity]="displayDensity"
          [label]="'root.modules.menu.menu-main.menu.activityResolution' | translate: this"
          [required]="true"
          displayKey="text"
          name="resolution"
          valueKey="value"
        >
        </app-select>
      }
      @if (resolutionDescription) {
        <div class="message-from">{{ resolutionDescription }}</div>
      }
    </div>
  }
  <igx-input-group [ngClass]="displayDensityClass" type="box" class="text-input-dialog cancel-reason-comments" theme="material">
    <label igxLabel for="commentValue">{{ entity.commentLabel }}</label>
    <textarea
      [required]="true"
      autoresize
      class="large-field fix-width"
      igxInput
      [(ngModel)]="entity.commentValue"
      name="commentValue"
      type="text"
      maxlength="255"
    ></textarea>
    <igx-suffix>
      <igx-icon (click)="entity.commentValue = ''">clear</igx-icon>
    </igx-suffix>
  </igx-input-group>
</ng-template>
@if (showImplementationsGrid) {
  <app-grid-multi-select
    #implementationsGridDef
    style="display: none"
    [actionsTopEnable]="true"
    [dataColumns]="ImpColumns"
    [dialogTitle]="implementationsGridTitle"
    [displayDensity]="displayDensity"
    [eventWithValues]="true"
    [hasDelete]="true"
    [heightDataGrid]="heigthActivitiesGrid"
    [help]="'help'"
    [id]="'ImpId'"
    [inputType]="4"
    [label]="implementsTitle"
    [okDialogButton]="okDialogButton"
    [okDialogDisabled]="!validMultiReassign()"
    [primaryKey]="'id'"
    [selectionMode]="'multiple'"
    [showOnlyCancelButton]="okDialogButton === null"
    [tooltipTemplate]="tooltipMore"
    [topZoneEnable]="topZoneEnable"
    [valueColumns]="ImpColumns"
    (baseDialogClosed)="closeImplementationsGridDialog()"
    (clearSearchGlobalFilters)="onClearImplementersOnVerifyMany()"
    (dataLoaded)="implementationsGrid().value = []"
    (dataRefresh)="onClearImplementersOnVerifyMany()"
    (dialogRightButton)="toggleMenu('MULTY_REASSIGN_VERIFY')"
    (rowSelectionChange)="fillImplementations($event)"
  >
    <app-to-reassign-verify
      dialogTopZone
      #toReassignMultipleVerify
      [displayDensity]="displayDensity"
      [useDialog]="false"
      [verifierDataUrl]="'activities/activity/verifiers/list'"
      (formChange)="filterImplementers($event)"
    ></app-to-reassign-verify>
    @if (!topZoneEnable) {
      <ng-container ngProjectAs="[actions-top-zone]">
        <div class="actions-container">
          <span>{{ 'root.common.field.actions' | translate: this }}</span>
          <igx-icon (click)="toggleMenu('VERIFY')" [title]="'root.common.button.VERIFY' | translate: this">done</igx-icon>
          <igx-icon (click)="toggleMenu('UNDONE')" [title]="'root.common.button.UNDONE' | translate: this">clear</igx-icon>
          <igx-icon (click)="toggleMenu('NOT_APPLY_VERIFY')" [title]="'root.common.button.NOT_APPLY_VERIFY' | translate: this">block</igx-icon>
          <igx-icon (click)="toggleMenu('REASSIGN_VERIFY')" [title]="'root.common.button.REASSIGN_VERIFY' | translate: this">perm_identity</igx-icon>
        </div>
      </ng-container>
    }
  </app-grid-multi-select>
}
@if (openVerifyAttender) {
  <app-activity-verify [displayDensity]="displayDensity" #verifyAttender></app-activity-verify>
}
@if (openActivityAttender) {
  <app-activity-resolution [displayDensity]="displayDensity" #activityAttender></app-activity-resolution>
}
@if (openToReassignVerify) {
  <app-to-reassign-verify
    [displayDensity]="displayDensity"
    #toReassignVerify
    [useDialog]="!topZoneEnable"
    (closedReassignVerifyDialog)="onClosedReassignVerifyDialog()"
  ></app-to-reassign-verify>
}
<ng-template #tooltipMore let-dataTooltip>
  <div class="dialog-extra-info-wrapper">
    <div class="dialog-extra-info-item">
      <label class="dialog-extra-info-label">{{ 'root.modules.activities.core.activity-info.i18n.progress' | translate: this }}:</label>
      <span>{{ dataTooltip.data.progress }}</span>
    </div>
    <div class="dialog-extra-info-item">
      <label class="dialog-extra-info-label">{{ 'root.modules.activities.core.activity-info.i18n.businessUnitDepartmentName' | translate: this }}:</label>
      <span>{{ dataTooltip.data.businessUnitDepartmentName }}</span>
    </div>
    <div class="dialog-extra-info-item">
      <label class="dialog-extra-info-label">{{ 'root.modules.activities.core.activity-info.i18n.implementerNames' | translate: this }}:</label>
      <span>{{ dataTooltip.data.implementerNames }}</span>
    </div>
    <div class="dialog-extra-info-item">
      <label class="dialog-extra-info-label">{{ 'root.modules.activities.core.activity-info.i18n.verifierName' | translate: this }}:</label>
      <span>{{ dataTooltip.data.verifierName }}</span>
    </div>
    <div class="dialog-extra-info-item">
      <label class="dialog-extra-info-label">{{ 'root.modules.activities.core.activity-info.i18n.resolutionDescription' | translate: this }}:</label>
      <span>{{ dataTooltip.data.resolutionDescription }}</span>
    </div>
    <div class="dialog-extra-info-item">
      <label class="dialog-extra-info-label">{{ 'root.modules.activities.core.activity-info.i18n.lastAction' | translate: this }}:</label>
      <span>{{ dataTooltip.data.lastAction }}</span>
    </div>
    <div class="dialog-extra-info-item">
      <label class="dialog-extra-info-label">{{ 'root.modules.activities.core.activity-info.i18n.filesCount' | translate: this }}:</label>
      <span>{{ dataTooltip.data.filesCount }}</span>
    </div>
  </div>
</ng-template>
