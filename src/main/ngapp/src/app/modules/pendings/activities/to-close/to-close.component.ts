import { BnextCoreComponent } from '@/core/bnext-core.component';
import { AutoresizeDirective } from '@/core/directives/autoresize';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { SelectComponent } from '@/core/select/select.component';
import { AppService } from '@/core/services/app.service';
import { NoticeService } from '@/core/services/notice.service';
import { Component, inject, input, output, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { IgxDialogComponent, IgxInputDirective, IgxInputGroupComponent } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs/operators';
import type { ActivityLoadDto, AttendResult } from 'src/app/shared/activities/core/utils/activity-base.interfaces';
import type { IActivityResolutionItem } from '../../../settings/activity-settings/activity-resolution/activity-resolution.interfaces';
import type { ActivityHistoryEntity } from '../activities-pending/activites-history.interfaces';

@Component({
  selector: 'app-to-close',
  templateUrl: './to-close.component.html',
  styleUrls: ['./to-close.component.scss'],
  imports: [IgxDialogComponent, FormsModule, SelectComponent, ReactiveFormsModule, IgxInputGroupComponent, AutoresizeDirective, IgxInputDirective, BnextTranslatePipe]
})
export class ToCloseComponent extends BnextCoreComponent {
  private noticeService = inject(NoticeService);

  private service = inject(AppService);

  readonly activity = input<ActivityLoadDto>(undefined);

  readonly addedHistory = output<ActivityHistoryEntity[]>();
  readonly attended = output<AttendResult>();

  readonly dialog = viewChild('dialog', { read: IgxDialogComponent });

  inputValue = new UntypedFormControl(null, Validators.required);
  resolucionId = new UntypedFormControl(null, Validators.required);
  resolutions = Array<IActivityResolutionItem>();

  public handleCloseActivity() {
    this.service
      .get({ url: `activity/pending/check-for-resolutions/${this.activity().typeId}`, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe((response: IActivityResolutionItem[]) => {
        if (response.length > 0) {
          this.resolutions = response;
          this.dialog().open();
        } else {
          this.dialogService.info('root.modules.activities.activities-detail.empty-resolutions').then(() => {});
        }
      });
  }

  onRightButtonSelect() {
    if (!this.inputValue.valid || !this.resolucionId.valid) {
      this.inputValue.markAllAsTouched();
      this.inputValue.updateValueAndValidity();
      this.resolucionId.markAllAsTouched();
      this.resolucionId.updateValueAndValidity();
      this.noticeService.notice(this.tag('root.common.error.fill-required'));
      return;
    }
    const data = {
      comment: this.inputValue.value
    };
    this.service
      .post({ url: `activity/pending/to-close-type-main-activity/${this.activity().id}/${this.resolucionId.value}`, cancelableReq: this.$destroy, postBody: data })
      .pipe(takeUntil(this.$destroy))
      .subscribe((response) => {
        if (response) {
          this.noticeService.notice(this.translate.instant('root.modules.activities.core.activity-info.activityTypeMainClosed'));
        }
        if (response.history && response.history.length > 0) {
          this.addedHistory.emit(response.history);
        }
        this.attended.emit(response);
      });
    this.dialog().close();
    this.cdr.detectChanges();
  }

  onLeftButtonSelect() {
    this.dialog().close();
  }
}
