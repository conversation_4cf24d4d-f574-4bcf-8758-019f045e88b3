import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { AutoresizeDirective } from '@/core/directives/autoresize';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { SelectComponent } from '@/core/select/select.component';
import { AppService } from '@/core/services/app.service';
import type { DialogService } from '@/core/services/dialog.service';
import { NoticeService } from '@/core/services/notice.service';
import { ErrorHandling } from '@/core/utils/error-handling';
import { SafeHtmlPipe } from '@/core/utils/safe-html.pipe';
import { ActivityTaskStatus } from '@/shared/activities/core/activities-core.enums';
import { useAnimation } from '@angular/animations';
import { NgClass } from '@angular/common';
import { type AfterViewInit, Component, type OnDestroy, type OnInit, ViewChild, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import {
  HorizontalAlignment,
  IgxDialogComponent,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxSuffixDirective,
  type OverlaySettings,
  type PositionSettings,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import { slideInTop, slideOutBottom } from '@infragistics/igniteui-angular/animations';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import { EnumUtil } from 'src/app/core/utils/enum-util';
import type { IActivityResolutionItem } from '../../../settings/activity-settings/activity-resolution/activity-resolution.interfaces';
import type { IActivitiesAttenderComponent } from '../activities-attender/activities-attender.interfaces';
import type { ActivityPendingDto } from '../activities-pending/activities-pending.interfaces';
import { ActivityInfoConstant } from '../activity-info/activity-info.constant';
import type { IActivityVerifyComponent, ResolutionType, VerifyDialogResult } from './activity-verify.interfaces';

@Component({
  selector: 'app-activity-verify',
  templateUrl: './activity-verify.component.html',
  styleUrls: ['./activity-verify.component.scss'],
  imports: [
    NgClass,
    IgxDialogComponent,
    FormsModule,
    SelectComponent,
    ReactiveFormsModule,
    IgxInputGroupComponent,
    AutoresizeDirective,
    IgxInputDirective,
    IgxSuffixDirective,
    IgxIconComponent,
    SafeHtmlPipe,
    BnextTranslatePipe
  ]
})
export class ActivityVerifyComponent extends BnextCoreComponent implements i18n, OnInit, OnDestroy, AfterViewInit, IActivityVerifyComponent {
  api = inject(AppService);

  noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.activities',
    componentName: 'activity-verify'
  };

  positionSettings: PositionSettings = {
    openAnimation: useAnimation(slideInTop, { params: { duration: '300ms' } }),
    closeAnimation: useAnimation(slideOutBottom, { params: { duration: '300ms' } }),
    horizontalDirection: HorizontalAlignment.Center,
    verticalDirection: VerticalAlignment.Middle,
    horizontalStartPoint: HorizontalAlignment.Center,
    verticalStartPoint: VerticalAlignment.Middle,
    minSize: { height: 500, width: 500 }
  };

  overlaySettings: OverlaySettings;

  busy = false;
  dialogOpen = false;
  inputValue = new UntypedFormControl(null, Validators.required);
  verifyTitle = 'Actividad verificada';
  verifyMessage = 'Su verificación a la actividad será guardada, si desea continuar capture un comentario de verificación.';
  public pending = null;
  userNameInterpolate = '';
  activityResolutionInterpolate = '';
  resolucionId = new UntypedFormControl(null, Validators.required);
  resolutionDescription: string = null;
  resolutions: IActivityResolutionItem[] = [];
  activityTaskStatusEnum = ActivityTaskStatus;

  override get componentPath(): string {
    return ActivityVerifyComponent.LANG_CONFIG.componentPath;
  }

  override get tagName(): string {
    return ActivityVerifyComponent.LANG_CONFIG.componentName;
  }

  // TODO: Skipped for migration because:
  //  Class of this query is referenced in the signature of another class.
  @ViewChild('dialog', { read: IgxDialogComponent })
  dialog: IgxDialogComponent;

  private resolver: (value: VerifyDialogResult) => void = null;
  private rejector: (reason?: any) => void = null;

  constructor() {
    super();

    this.overlaySettings = {
      outlet: this.elem,
      modal: false
    };
  }

  public static getVerifyAttenter(
    commonAttender: IActivitiesAttenderComponent,
    dialogService: DialogService
  ):
    | IActivityVerifyComponent
    | {
        open: (activity: Partial<ActivityPendingDto>, comment?: string) => Promise<DialogResult>;
      } {
    if (commonAttender) {
      commonAttender.openVerifyAttender = true;
      commonAttender.detectChanges();
      return commonAttender.verifyAttender;
    }
    return {
      open: (_activity: Partial<ActivityPendingDto>): Promise<DialogResult> => {
        return new Promise<DialogResult>((resolve, reject) => {
          dialogService
            .input(
              'Su verificación a la actividad será guardada, si desea continuar capture un comentario de verificación.',
              'root.common.button.ok',
              'root.common.button.cancel',
              'Actividad verificada'
            )
            .then(resolve, reject);
        });
      }
    };
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.translateService.getFrom(ActivityInfoConstant.LANG_CONFIG, ['activity-verified', 'verify']).subscribe((x) => {
      this.verifyTitle = x['modules.activities.core.activity-info.activity-verified'];
      this.verifyMessage = x['modules.activities.core.activity-info.verify'];
    });
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
  }

  override langReady() {
    // ToDo: do something!
  }

  onDialogClosed() {
    if (this.rejector) {
      this.rejector();
    }
  }

  onRightButtonSelect() {
    if (!this.inputValue.valid || !this.resolucionId.valid) {
      this.inputValue.markAllAsTouched();
      this.inputValue.updateValueAndValidity();
      this.resolucionId.markAllAsTouched();
      this.resolucionId.updateValueAndValidity();
      this.noticeService.notice(this.tag('root.common.error.fill-required'));
      return;
    }
    this.resolver({
      dialog: null,
      custom: null,
      selectValue: null,
      selectText: null,
      inputValue: this.inputValue.value,
      resolutionId: this.resolucionId.value || null
    });
    this.clear();
    if (this.dialog) {
      this.dialog.close();
    }
    this.dialogOpen = false;
    this.cdr.detectChanges();
  }

  onLeftButtonSelect() {
    this.rejector({
      dialog: null,
      custom: null,
      inputValue: this.inputValue.value
    });
    this.clear();
    if (this.dialog) {
      this.dialog.close();
    }
    this.dialogOpen = false;
    this.cdr.detectChanges();
  }

  clear() {
    this.resolucionId.reset();
    this.resolucionId.setErrors({});
    this.resolucionId.markAsPristine();
    this.resolucionId.markAsUntouched();
    this.resolucionId.updateValueAndValidity();
    this.resolutionDescription = null;
    this.resolutions = [];
    this.resolver = null;
    this.rejector = null;
  }

  public changeResolution(resolucionId: number | string): void {
    if (resolucionId) {
      this.resolutionDescription = this.resolutions?.find((r) => r.value === resolucionId).description;
    }
  }

  public clearInput(): void {
    this.inputValue.patchValue('');
  }

  public open(
    pending: Partial<ActivityPendingDto>,
    comment?: string,
    verifyMessage?: string,
    verifyTitle?: string,
    resolutionType?: ResolutionType
  ): Promise<VerifyDialogResult> {
    return new Promise<VerifyDialogResult>((resolve, reject) => {
      let url: string;
      if (resolutionType === null || typeof resolutionType === 'undefined') {
        url = `activity/pending/check-for-resolutions/${pending.typeId}`;
      } else {
        url = `activity/pending/check-for-resolutions/${pending.typeId}/${resolutionType}`;
      }
      // Se actualizan los mensajes
      if (verifyMessage) {
        this.verifyMessage = verifyMessage;
      }
      if (verifyTitle) {
        this.verifyTitle = verifyTitle;
      }
      if (typeof pending.taskStatus === 'number') {
        const enumName = EnumUtil.getName(ActivityTaskStatus, pending.taskStatus);
        pending.taskStatus = ActivityTaskStatus[enumName];
      }
      if (!pending.taskStatus && typeof pending.taskStatus !== 'string') {
        console.error('Invalid enum value for taskStatus', pending.taskStatus);
      }
      this.pending = pending;
      this.activityResolutionInterpolate = `<b> ${pending.activityResolution} </b>`;
      this.userNameInterpolate = `<b> ${pending.lastActionUserName || pending.implementerNames} </b>`;
      this.api.get({ cancelableReq: this.$destroy, url: url, handleFailure: false }).subscribe({
        next: (resolutions: IActivityResolutionItem[]) => {
          if (resolutions.length) {
            this.resolutions = resolutions;
            this.resolver = resolve;
            this.rejector = reject;
            this.inputValue.patchValue(comment || '', { onlySelf: true, emitEvent: false });
            this.dialogOpen = true;
            this.cdr.detectChanges();
            this.dialog.open();
          } else if (comment) {
            resolve({
              dialog: null,
              custom: null,
              selectValue: null,
              selectText: null,
              inputValue: comment,
              resolutionId: null
            });
          } else {
            this.dialogService
              .input(
                {
                  message: this.verifyMessage,
                  inputValue: comment,
                  inputMaxLimit: 4000
                },
                'root.common.button.ok',
                'root.common.button.cancel',
                this.verifyTitle
              )
              .then((frm: DialogResult) => {
                resolve(frm as VerifyDialogResult);
              }, reject);
          }
        },
        error: (e) => {
          reject(e);
          ErrorHandling.notifyError(e, this.navLang);
        }
      });
    });
  }
}
