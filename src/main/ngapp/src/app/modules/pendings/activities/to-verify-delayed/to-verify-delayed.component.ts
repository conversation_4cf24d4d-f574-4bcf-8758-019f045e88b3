import { CommentMultiSaveComponent } from '@/core/comment-multi-save/comment-multi-save.component';
import { AutoresizeDirective } from '@/core/directives/autoresize';
import { DocumentMultiSelectComponent } from '@/core/document-multi-select/document-multi-select.component';
import { DropdownMenuComponent } from '@/core/dropdown-menu/dropdown-menu.component';
import { FieldDisplayComponent } from '@/core/field-display/field-display.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { MultiFileUploadComponent } from '@/core/multi-file-upload/multi-file-upload.component';
import { NgClass } from '@angular/common';
import { Component, type OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  IgxDialogComponent,
  IgxHintDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxSuffixDirective
} from '@infragistics/igniteui-angular';
import { FillType } from 'src/app/shared/activities/core/activities-core.enums';
import { ActivitiesAttenderComponent } from '../activities-attender/activities-attender.component';
import { ActivitiesWidgetComponent } from '../activities-widget/activities-widget.component';
import { FillTypeComponent } from '../fill-type/fill-type.component';
import { ToVerifyComponent } from '../to-verify/to-verify.component';

@Component({
  selector: 'app-to-verify-delayed',
  templateUrl: '../activities-pending/activities-pending.template.html',
  styleUrls: ['../activities-pending/activities-pending.template.scss'],
  imports: [
    NgClass,
    IgxDialogComponent,
    DropdownMenuComponent,
    IgxLinearProgressBarComponent,
    FieldDisplayComponent,
    FillTypeComponent,
    ActivitiesWidgetComponent,
    DocumentMultiSelectComponent,
    MultiFileUploadComponent,
    CommentMultiSaveComponent,
    IgxInputGroupComponent,
    IgxLabelDirective,
    FormsModule,
    AutoresizeDirective,
    IgxInputDirective,
    ReactiveFormsModule,
    IgxSuffixDirective,
    IgxIconComponent,
    IgxHintDirective,
    ActivitiesAttenderComponent,
    BnextTranslatePipe
  ]
})
export class ToVerifyDelayedComponent extends ToVerifyComponent implements OnInit {
  verifyServiceName = 'to-verify-delayed';

  onDataSourceRefresh() {
    this.dialogLabel = `root.modules.activities.core.activity-info.fillType.${FillType[this.fillType]}`;
  }
}
