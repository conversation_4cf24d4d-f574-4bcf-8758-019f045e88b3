import { CommentMultiSaveComponent } from '@/core/comment-multi-save/comment-multi-save.component';
import { AutoresizeDirective } from '@/core/directives/autoresize';
import { DocumentMultiSelectComponent } from '@/core/document-multi-select/document-multi-select.component';
import { DropdownMenuComponent } from '@/core/dropdown-menu/dropdown-menu.component';
import { FieldDisplayComponent } from '@/core/field-display/field-display.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { MultiFileUploadComponent } from '@/core/multi-file-upload/multi-file-upload.component';
import { NgClass } from '@angular/common';
import { Component, type OnDestroy, type OnInit, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  IgxDialogComponent,
  IgxHintDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxSuffixDirective
} from '@infragistics/igniteui-angular';
import { EnumUtil } from 'src/app/core/utils/enum-util';
import { CommitmentTask } from 'src/app/shared/activities/core/activities-core.enums';
import type { ActivtyPendingAttendResult } from 'src/app/shared/activities/core/utils/activity-base.interfaces';
import { ActivitiesAttenderComponent } from '../activities-attender/activities-attender.component';
import type { AttenderAcceptedData } from '../activities-attender/activities-attender.interfaces';
import { ActivitiesPendingDirective } from '../activities-pending/activities-pending.component';
import { ImplementAction, PendingAttendResult } from '../activities-pending/activities-pending.enums';
import type { ActivityPendingDto } from '../activities-pending/activities-pending.interfaces';
import { ActivitiesWidgetComponent } from '../activities-widget/activities-widget.component';
import { ActivityResolutionComponent, type ResolutionDialogResult } from '../activity-resolution/activity-resolution.component';
import { FillTypeComponent } from '../fill-type/fill-type.component';
import { ToCompleteUtil } from './to-complete-util';

@Component({
  selector: 'app-to-complete',
  templateUrl: '../activities-pending/activities-pending.template.html',
  styleUrls: ['../activities-pending/activities-pending.template.scss'],
  imports: [
    NgClass,
    IgxDialogComponent,
    DropdownMenuComponent,
    IgxLinearProgressBarComponent,
    FieldDisplayComponent,
    FillTypeComponent,
    ActivitiesWidgetComponent,
    DocumentMultiSelectComponent,
    MultiFileUploadComponent,
    CommentMultiSaveComponent,
    IgxInputGroupComponent,
    IgxLabelDirective,
    FormsModule,
    AutoresizeDirective,
    IgxInputDirective,
    ReactiveFormsModule,
    IgxSuffixDirective,
    IgxIconComponent,
    IgxHintDirective,
    ActivitiesAttenderComponent,
    BnextTranslatePipe
  ]
})
export class ToCompleteComponent extends ActivitiesPendingDirective implements OnInit, OnDestroy {
  pendingController = 'activity/pending/to-complete/';

  readonly commonAttender = viewChild<ActivitiesAttenderComponent>('commonAttender');

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  public attend(id?: number, comment?: string, resolutionId?: number) {
    if (typeof id === 'undefined') {
      id = this.id || null;
    }
    if (typeof comment === 'undefined') {
      comment = this.comment || '';
    }
    if (comment != null) {
      this.comment = comment;
    }
    if (!id) {
      throw new Error(`ToStart: Missing activityId for progress: ${this.progress}`);
    }
    this.isRightButtonAvailable = false;
    if (this.notApply) {
      this.saveNotApply(ImplementAction.NOT_APPLY);
      return;
    }
    this.isLoading = true;
    const s = this.api
      .post({
        url: `${this.pendingController + id}/${this.progress}`,
        cancelableReq: this.$destroy,
        postBody: { comment: comment, resolutionId: resolutionId }
      })
      .subscribe((result) => {
        s.unsubscribe();
        this.attendedAction(result, PendingAttendResult.PROGRESS);
      });
  }

  public undone(id?: number, comment?: string) {
    if (typeof id === 'undefined') {
      id = this.id || null;
    }
    if (typeof comment === 'undefined') {
      comment = this.comment || '';
    }
    this.isLoading = true;
    const s = this.api.post({ url: `${this.controller + id}/${this.progress}`, cancelableReq: this.$destroy, postBody: comment }).subscribe((result) => {
      s.unsubscribe();
      this.attendedAction(result, PendingAttendResult.PROGRESS);
    });
  }

  public acceptProgressItem() {
    if (!this.isProgressDialogValid()) {
      return;
    }
    this.progress = this.clickedImplementation.progress;
    if (this.progress === 100) {
      this.commonAttender()
        .openResolutionDialog(
          this.clickedImplementation.typeId,
          ImplementAction.PROGRESS,
          this.comment,
          this.translate.instantFrom(ActivityResolutionComponent.LANG_CONFIG, 'progressActivityComplete')
        )
        .then((value: ResolutionDialogResult) => {
          this.attend(this.clickedImplementation.recordId, value.inputValue, value.resolutionId);
          this.hideProgressDialog();
        });
      return;
    }
    this.attend(this.clickedImplementation.recordId, this.comment || null);
    this.hideProgressDialog();
  }

  public toggleMenu(value: string | number, _selected: boolean, implementation?: ActivityPendingDto) {
    this.clickedImplementation = implementation || null;
    if (EnumUtil.isEqual(this.commitmentTask, CommitmentTask.IMPLEMENTATION)) {
      this.progressHolder = implementation || this;
    } else {
      this.progressHolder = this;
    }
    this.comment = '';
    this.notApply = false;
    switch (value) {
      case ImplementAction.FILL:
        if (typeof implementation === 'undefined') {
          console.error('Activity to FILL is undefined', value);
        } else {
          ToCompleteUtil.fillForm(this.menuService, implementation, 'detail');
        }
        break;
      case ImplementAction.PROGRESS:
        this.toggleModifyProgress(implementation, true);
        break;
      case ImplementAction.NOT_APPLY:
        this.notApply = true;
        this.saveNotApply(ImplementAction.NOT_APPLY);
        break;
      case ImplementAction.FINISH:
        this.progress = 100;
        this.commonAttender()
          .openResolutionDialog(implementation.typeId, ImplementAction.FINISH, '', null)
          .then(
            (value: ResolutionDialogResult) => {
              this.attend(implementation.recordId, value.inputValue, value.resolutionId);
            },
            () => {
              this.progress = this.progressHolder.progress || 0;
            }
          );
        break;
      case ImplementAction.UNDONE:
        this.commonAttender()
          .openResolutionDialog(implementation.typeId, ImplementAction.UNDONE, '', null)
          .then(
            (value: ResolutionDialogResult) => {
              this.comment = value.inputValue;
              this.isLoading = true;
              const s = this.api
                .post({
                  url: `activity/pending/to-undone/${implementation.recordId}`,
                  cancelableReq: this.$destroy,
                  postBody: { comment: this.comment, resolutionId: value.resolutionId }
                })
                .subscribe((result) => {
                  s.unsubscribe();
                  this.attendedAction(result, PendingAttendResult.UNDONE);
                });
            },
            (reason: any) => {
              console.warn('Cancel resolution dialog, unsaving activity undone: ', reason);
              this.isLoading = false;
            }
          );
        break;
      default:
        this.doPendingAction(value, implementation);
        break;
    }
  }

  public acceptActivityAttender(_data: AttenderAcceptedData) {}

  public onAttendedManyAttenderDialog(_result: ActivtyPendingAttendResult): void {}
}
