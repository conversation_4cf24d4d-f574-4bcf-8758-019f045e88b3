import { MockCoreModule } from '@/core/test/mock-core.module';
import { type ComponentFixture, TestBed } from '@angular/core/testing';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { ToCloseComponent } from './to-close.component';

describe('ToCloseComponent', () => {
  let component: ToCloseComponent;
  let fixture: ComponentFixture<ToCloseComponent>;

  configureTestSuite();

  beforeEach(async () => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ToCloseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
