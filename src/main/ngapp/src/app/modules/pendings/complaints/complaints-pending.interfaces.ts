import type { ComplaintStatus } from '../../complaints/utils/complaints.enums';
import type { PendingDto } from '../../menu/pendings/pendings.interfaces';

export interface ComplaintPendingDto extends PendingDto {
  id: number;
  status: ComplaintStatus;
  fechaCreacion: string;
  businessUnitDepartmentId: number;
  pendingRecordId: number;
  authorDescription: string;
  businessUnitDepartmentDescription: string;
  complaintSourceDescription: string;
  priorityDescription: string;
  catalogCode: string;
}
