import type { ReportColumnRuleDTO, ReportsFieldDTO } from '@/core/grid-report/gird-report.interfaces';
import { GridComponent } from '@/core/grid/grid.component';
import { GridColumnVisibility, type GridDropDownItem } from '@/core/grid/utils/grid-base.interfaces';
import * as GridUtil from '@/core/grid/utils/grid-util';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { TransformedFieldsRulesEditorComponent } from '@/core/transformed-fields-rules-editor/transformed-fields-rules-editor.component';
import { CommonAction } from '@/core/utils/enums';
import type { ISlimReportsTransformedField } from '@/modules/forms/slim-report-base/slim-report-base.interfaces';
import { Component, type OnInit, computed, inject, signal, viewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import {
  IgxDialogComponent,
  IgxDialogTitleDirective,
  IgxIconComponent,
  IgxLinearProgressBarComponent,
  IgxTabContentComponent,
  IgxTabHeaderComponent,
  IgxTabItemComponent,
  IgxTabsComponent
} from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import { type ActionStrip, HtmlColumn, IntegerColumn, LinkColumn, TextColumn, TimestampColumn } from 'src/app/core/grid/utils/grid.interfaces';
import { AppService } from 'src/app/core/services/app.service';
import { NoticeService } from 'src/app/core/services/notice.service';

function toTransformedField(rules: any, field: { id; description }): ISlimReportsTransformedField[] {
  return [
    {
      id: field.id,
      code: field.id,
      description: field.description,
      order: 1,
      deleted: 0,
      rules: rules.map(
        (r) =>
          ({
            id: r.id,
            evaluatedField: {
              text: r.evalFieldText,
              value: r.id,
              fieldCode: r.id,
              fieldSource: r.evalFieldSource,
              fieldValueType: r.evalFieldType
            },
            config: {
              type: r.typeName,
              typeFinalValue: r.evalFieldValue
            },
            finalValue: r.finalValue,
            order: r.orderValue,
            valid: true
          }) satisfies ReportColumnRuleDTO
      ),
      valid: true
    }
  ];
}

function toReportField(rules: any): ReportsFieldDTO[] {
  return rules.map((f) => ({
    text: f.evalFieldText,
    value: f.id,
    fieldCode: 'string',
    fieldSource: 'TRANSFORMED',
    fieldValueType: f.evalFieldType
  }));
}

@Component({
  selector: 'app-form-log-lists',
  styleUrls: ['form-log-lists.component.scss'],
  templateUrl: './form-log-lists.component.html',
  imports: [
    GridComponent,
    IgxLinearProgressBarComponent,
    IgxTabsComponent,
    IgxTabContentComponent,
    IgxTabHeaderComponent,
    IgxTabItemComponent,
    IgxDialogComponent,
    IgxDialogTitleDirective,
    TransformedFieldsRulesEditorComponent,
    IgxIconComponent,
    BnextTranslatePipe
  ]
})
export class FormLogListsComponent extends BnextCoreComponent implements OnInit, i18n {
  // services
  api = inject(AppService);
  activatedRoute = inject(ActivatedRoute);
  noticeService = inject(NoticeService);
  titleService = inject(Title);

  oldFieldRules = signal<any>([]);
  oldField = signal<{ id; description }>({ id: null, description: null });
  oldTransformedFieldValues = computed<ISlimReportsTransformedField[]>(() => toTransformedField(this.oldFieldRules(), this.oldField()));
  oldAvailableFieldsForTransformation = computed<ReportsFieldDTO[]>(() => toReportField(this.oldFieldRules()));
  newFieldRules = signal<any>([]);
  newField = signal<{ id; description }>({ id: null, description: null });
  newTransformedFieldValues = computed<ISlimReportsTransformedField[]>(() => toTransformedField(this.newFieldRules(), this.newField()));
  newAvailableFieldsForTransformation = computed<ReportsFieldDTO[]>(() => toReportField(this.newFieldRules()));
  rulesDialog = viewChild<IgxDialogComponent>('rulesDialog');

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: () => [
        {
          text: 'Ver',
          value: CommonAction.OPEN_DETAIL,
          iconName: 'open_in_browser',
          hidden: false
        }
      ],
      rowIdentifierKey: 'id'
    }
  };

  // lang
  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.forms',
    componentName: 'form-log-lists'
  };
  override get componentPath(): string {
    return 'modules.forms';
  }
  override get tagName(): string {
    return 'form-log-lists';
  }

  // init
  override ngOnInit(): void {
    super.ngOnInit();
    this.init();
  }
  override langReady(): void {
    this.init();
  }

  // readonly vars
  readonly grid = viewChild<GridComponent>('grid');

  // private vars
  private _reportCode: string = null;
  private _masterId: string = null;
  private _titleLabel: string = null;
  titleLabelRule: string;
  private _outstandingSurveysId: number = null;
  private _ready = false;
  private _busy = true;

  // public vars
  url = null; // <-- Se inicializa en `init()`
  transformedFieldUrl = null; // <-- Se inicializa en `init()`
  columns: GridColumn[] = null;
  transformedFieldColumns: GridColumn[] = null;
  LangConfig = FormLogListsComponent.LANG_CONFIG;

  // logic
  get ready(): boolean {
    return this._ready;
  }
  get busy(): boolean {
    return this._busy || !this._ready;
  }
  get reportCode(): string {
    return this._reportCode;
  }
  get titleLabel(): string {
    return this._titleLabel;
  }
  get masterId(): string {
    return this._masterId;
  }
  get outstandingSurveysId(): number {
    return this._outstandingSurveysId;
  }

  protected init(): void {
    this.navLang
      .getRouteParams(':masterId', ['outstandingSurveysId', 'reportCode', 'masterId', 'formCode'], true, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([outstandingSurveysId, reportCode, masterId, formCode]) => {
        this._masterId = masterId || null;
        this._reportCode = reportCode || null;
        this._outstandingSurveysId = +outstandingSurveysId || null;
        this._titleLabel = this.tag('titleLabel');
        this.titleLabelRule = this.tag('titleLabelRule');
        if (outstandingSurveysId) {
          this.url = `forms/form-log-lists/${encodeURIComponent(masterId)}/${encodeURIComponent(reportCode)}/${outstandingSurveysId}`;
          this.titleService.setTitle(`${formCode} - ${this._titleLabel} - ${reportCode}`);
        } else {
          this.url = `forms/form-log-lists/${encodeURIComponent(masterId)}/${encodeURIComponent(reportCode)}`;
          this.transformedFieldUrl = `forms/transformed-field-history-lists/${encodeURIComponent(masterId)}/${encodeURIComponent(reportCode)}`;
          this.titleService.setTitle(`${this._titleLabel} - ${reportCode}`);
        }
        this.setColumns();
        this.setTransformedFieldColumns();
        this._busy = false;
        this._ready = true;
        this.cdr.detectChanges();
      });
  }
  private setColumns(): void {
    this.columns = [];
    GridUtil.columns(this.columns)
      .push(
        'bulkDate',
        this.tag('column.bulkDate'),
        new TimestampColumn({
          width: '200px'
        })
      )
      .push(
        'bulkDescription',
        this.tag('column.bulkDescription'),
        new TextColumn({
          width: '200px'
        })
      )
      .push(
        'bulkUserName',
        this.tag('column.bulkUserName'),
        new TextColumn({
          width: '200px'
        })
      )
      .push(
        'originalFileName',
        this.tag('column.originalFileName'),
        new LinkColumn({
          linkHref: `../rest/forms/slim-report/bulk/original-template/${this.masterId}/c/${this.reportCode}/{bulkLogId}`,
          linkParams: ['bulkLogId'],
          width: '235px'
        })
      )
      .push(
        'resultFileName',
        this.tag('column.resultFileName'),
        new LinkColumn({
          linkHref: `../rest/forms/slim-report/bulk/upload-template/${this.masterId}/c/${this.reportCode}/{bulkLogId}`,
          linkParams: ['bulkLogId'],
          width: '235px'
        })
      );
  }
  private setTransformedFieldColumns() {
    this.transformedFieldColumns = [];
    const changeTypeLabels = {
      1: this.tag('column2.create'),
      2: this.tag('column2.update'),
      3: this.tag('column2.delete')
    };
    GridUtil.columns(this.transformedFieldColumns)
      .push('createdBy', this.tag('column2.createdBy'))
      .push(
        'createdAt',
        this.tag('column2.createdAt'),
        new TimestampColumn({
          width: '200px'
        })
      )
      .push(
        'changeType',
        this.tag('column2.changeType'),
        new HtmlColumn({
          renderCell: (cell) => `<span class="change-type change-type-${cell.row.data.changeType}">${changeTypeLabels[cell.row.data.changeType]}</span>`,
          groupable: false
        })
      )
      .push('oldFieldDescription', this.tag('column2.oldFieldDescription'), GridColumnVisibility.HIDDEN)
      .push('newFieldDescription', this.tag('column2.newFieldDescription'))
      .push(
        'previousRuleCount',
        this.tag('column2.previousRuleCount'),
        new IntegerColumn({
          width: '250px'
        })
      )
      .push(
        'currentRuleCount',
        this.tag('column2.currentRuleCount'),
        new IntegerColumn({
          width: '250px'
        })
      )
      .push(
        'ruleDiffCount',
        this.tag('column2.ruleDiffCount'),
        new IntegerColumn({
          width: '250px'
        })
      );
  }

  loadTransformedFieldDiff(f: any) {
    if (!f) {
      return;
    }
    const ids = [f.oldTransformedFieldId, f.newTransformedFieldId].filter((id) => !!id);
    this.api.get({ url: `forms/transformed-fields-rules/${ids.join(',')}`, cancelableReq: this.$destroy }).subscribe((result) => {
      const rulesByField: Record<any, any[]> = {};
      if (f.oldTransformedFieldId) {
        rulesByField[f.oldTransformedFieldId] = [];
        this.oldField.set({ id: f.oldTransformedFieldId, description: f.oldFieldDescription });
      }
      if (f.newTransformedFieldId) {
        rulesByField[f.newTransformedFieldId] = [];
        this.newField.set({ id: f.newTransformedFieldId, description: f.newFieldDescription });
      }
      for (const elem of result) {
        rulesByField[elem.transformedFieldId].push(elem);
      }
      if (f.oldTransformedFieldId) {
        this.oldFieldRules.set(rulesByField[f.oldTransformedFieldId]);
      }
      if (f.newTransformedFieldId) {
        this.newFieldRules.set(rulesByField[f.newTransformedFieldId]);
      }
      this.rulesDialog().open();
    });
  }

  onStripClick(event: GridDropDownItem) {
    if (event.item.value === CommonAction.OPEN_DETAIL) {
      this.loadTransformedFieldDiff(event.row);
    }
  }
}
