@if (busy) {
  <igx-linear-bar class="overlayer" type="success" [striped]="false"
                  [indeterminate]="true"></igx-linear-bar>
}
<igx-tabs>
  <igx-tab-item>
    <igx-tab-header>
      <span igxTabHeaderLabel>{{ 'i18n.titleLabel' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content>
      <div class="grid-container grid-floating-active full-width-grid-container">
        @if (ready) {
          <app-grid
            class="container-form grid-floating-action-buttons"
            [columns]="columns"
            [name]="LangConfig.componentName"
            [id]="LangConfig.componentName"
            [perPage]="perPage"
            [showActionStrip]="true"
            [titleLabel]="titleLabel"
            [url]="url"
          >
          </app-grid>
        }
      </div>
    </igx-tab-content>
  </igx-tab-item>
  <igx-tab-item>
    <igx-tab-header>
      <span igxTabHeaderLabel> {{ 'i18n.titleLabelRule' | translate: this }}</span>
    </igx-tab-header>
    <igx-tab-content>
      <div class="grid-container grid-floating-active full-width-grid-container">
        @if (ready) {
          <app-grid
            class="container-form grid-floating-action-buttons"
            [columns]="transformedFieldColumns"
            [name]="LangConfig.componentName + 2"
            [id]="LangConfig.componentName + 2"
            (dropdownMenu)="onStripClick($event)"
            [perPage]="perPage"
            [actionStripConfig]="actionStripConfig"
            [showActionStrip]="true"
            [titleLabel]="titleLabelRule"
            [url]="transformedFieldUrl"
          >
          </app-grid>
        }
      </div>
    </igx-tab-content>
  </igx-tab-item>
</igx-tabs>
<igx-dialog #rulesDialog>
  <div igxDialogTitle>
    <div class="title-container d-flex w-100">
      <div class="dialog-title">{{ 'i18n.titleLabelRule' | translate: this }}</div>
      <igx-icon class="ml-auto" (keyup)="rulesDialog.close()" (click)="rulesDialog.close()">close</igx-icon>
    </div>
  </div>
  <div class="grid-x">
    <div class="cell large-6 small-12">
      <div class="dialog-content-item-label">{{ 'i18n.oldRule' | translate: this }}</div>
      <app-transformed-fields-rules-editor [readonly]="true" [transformedFieldsValues]="oldTransformedFieldValues()" [availableFieldsForTransformation]="oldAvailableFieldsForTransformation()"></app-transformed-fields-rules-editor>
    </div>
    <div class="cell large-6 small-12">
      <div class="dialog-content-item-label">{{ 'i18n.newRule' | translate: this }}</div>
      <app-transformed-fields-rules-editor [readonly]="true" [transformedFieldsValues]="newTransformedFieldValues()" [availableFieldsForTransformation]="newAvailableFieldsForTransformation()"></app-transformed-fields-rules-editor>
    </div>
  </div>
</igx-dialog>

<!-- dummy para que angular no borre nuestras clases -->
<div class="d-none change-type-1 change-type-2 change-type-3 change-type"></div>
