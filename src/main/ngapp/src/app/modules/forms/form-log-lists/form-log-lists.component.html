@if (busy) {
  <igx-linear-bar class="overlayer" type="success" [striped]="false"
                  [indeterminate]="true"></igx-linear-bar>
}
<igx-tabs>
  <igx-tab-item>
    <igx-tab-header>
      <span igxTabHeaderLabel>Cargas y Descargas</span>
    </igx-tab-header>
    <igx-tab-content>
      <div class="grid-container grid-floating-active full-width-grid-container">
        @if (ready) {
          <app-grid
            class="container-form grid-floating-action-buttons"
            [columns]="columns"
            [name]="LangConfig.componentName"
            [id]="LangConfig.componentName"
            [perPage]="perPage"
            [showActionStrip]="true"
            [titleLabel]="titleLabel"
            [url]="url"
          >
          </app-grid>
        }
      </div>
    </igx-tab-content>
  </igx-tab-item>
  <igx-tab-item>
    <igx-tab-header>
      <span igxTabHeaderLabel>Reglas</span>
    </igx-tab-header>
    <igx-tab-content>
      <div class="grid-container grid-floating-active full-width-grid-container">
        @if (ready) {
          <app-grid
            class="container-form grid-floating-action-buttons"
            [columns]="transformedFieldColumns"
            [name]="LangConfig.componentName"
            [id]="LangConfig.componentName"
            [perPage]="perPage"
            [showActionStrip]="true"
            [titleLabel]="titleLabel"
            [url]="transformedFieldUrl"
          >
          </app-grid>
        }
      </div>
    </igx-tab-content>
  </igx-tab-item>
</igx-tabs>
