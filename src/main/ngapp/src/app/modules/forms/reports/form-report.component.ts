import { BnextCoreComponent } from '@/core/bnext-core.component';
import type { ReportDTO } from '@/core/grid-report/gird-report.interfaces';
import { GridReportComponent } from '@/core/grid-report/grid-report.component';
import { Session } from '@/core/local-storage/session';
import { AppService } from '@/core/services/app.service';
import { ErrorHandling } from '@/core/utils/error-handling';
import { BulkUploadComponent } from '@/shared/bulk-upload/bulk-upload.component';
import { ProfileServices } from '@/shared/roles/profiles/utils/profile-services.enums';
import { type AfterViewInit, Component, type OnDestroy, inject, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { IgxLinearProgressBarComponent, IgxRippleDirective, type RowType } from '@infragistics/igniteui-angular';
import { IgxButtonModule } from 'igniteui-angular';
import { takeUntil } from 'rxjs';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { ActionStrip } from 'src/app/core/grid/utils/grid.interfaces';
import type { DialogService } from 'src/app/core/services/dialog.service';
import { CommonAction } from 'src/app/core/utils/enums';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';

@Component({
  selector: 'app-form-report',
  templateUrl: './form-report.component.html',
  styleUrls: ['./form-report.component.scss'],
  imports: [BulkUploadComponent, GridReportComponent, FormsModule, ReactiveFormsModule, IgxRippleDirective, IgxButtonModule, IgxLinearProgressBarComponent]
})
export class FormReportComponent extends BnextCoreComponent implements AfterViewInit, OnDestroy {
  private activatedRoute = inject(ActivatedRoute);
  private api = inject(AppService);

  private isAdmin = Session.hasService(ProfileServices.IS_ADMIN);
  protected baseUrl = 'forms';
  protected primaryKey = 'request_id';
  protected actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.menuAction(row?.data) : []),
      rowIdentifierKey: this.primaryKey
    }
  };
  private defaultMenuActionOptions: DropdownMenuItem[] = [
    {
      text: 'Previsualizar',
      value: CommonAction.OPEN_DETAIL,
      iconName: 'preview'
    },
    {
      text: 'Vistas de impresión',
      value: CommonAction.PRINTING_OPTIONS,
      iconName: 'print'
    },
    {
      text: 'Historial',
      value: CommonAction.LOGGING,
      iconName: 'timeline',
      hidden: !this.isAdmin
    }
  ];

  readonly gridReport = viewChild<GridReportComponent>('gridReport');
  readonly bulkDialog = viewChild<BulkUploadComponent>('bulkDialog');

  private _bulkDialogUploadController: string = null;
  private _bulkDialogUploadTemplateController: string = null;
  private _optionsReady = false;
  private _busy = true;
  private _excelUploadEnabled = false;
  private _masterId: string = null;
  private _reportCode: string = null;

  get excelUploadEnabled() {
    return this._excelUploadEnabled;
  }
  get busy(): boolean {
    return this._busy;
  }

  protected get moduleName(): string {
    return 'formularie';
  }

  protected get masterId(): string {
    return this._masterId;
  }

  protected get bulkDialogUploadController(): string {
    return this._bulkDialogUploadController;
  }

  protected get bulkDialogUploadTemplateController(): string {
    return this._bulkDialogUploadTemplateController;
  }

  protected get dialogService(): DialogService {
    return this.navLang?.dialogService;
  }

  public ngAfterViewInit() {
    super.ngAfterViewInit();
    this.navLang
      .getRouteParams(':masterId', ['id', 'reportCode', 'masterId'], true, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([id, reportCode, masterId]) => {
        let url = `${this.baseUrl}/reportConfig`;
        this._reportCode = reportCode || null;
        if (reportCode) {
          url += `/c/${reportCode}`;
        } else if (id) {
          url += `/${id}`;
        }
        const gridReport = this.gridReport();
        if (masterId) {
          this._masterId = masterId;
          gridReport.masterId = this._masterId;
          url += `/${encodeURIComponent(this._masterId)}`;
        }
        if (reportCode) {
          this.api
            .get({ url: `${this.baseUrl}/reportConfig/g/i/${reportCode}`, handleFailure: true, cancelableReq: this.$destroy })
            .pipe(takeUntil(this.$destroy))
            .subscribe({
              next: (reportId: number) => {
                gridReport.loadReport(+reportId, url).then(() => {
                  this._busy = false;
                });
              },
              error: (error) => {
                console.error(error);
                ErrorHandling.notifyError(error, this.navLang);
                this.cdr.detectChanges();
              }
            });
          this._bulkDialogUploadController = `${this.baseUrl}/slim-report/bulk/upload-template/${this.masterId}/c/${this._reportCode}`;
          this._bulkDialogUploadTemplateController = `${this.baseUrl}/slim-report/bulk/download-template/${this.masterId}/c/${this._reportCode}`;
        } else {
          gridReport.loadReport(+id, url).then(() => {
            this._busy = false;
          });
          this._bulkDialogUploadController = `${this.baseUrl}/slim-report/bulk/upload-template/${this.masterId}/i/${id}`;
          this._bulkDialogUploadTemplateController = `${this.baseUrl}/slim-report/bulk/download-template/${this.masterId}/i/${id}`;
        }
      });
  }

  public ngOnDestroy(): void {
    super.ngOnDestroy();
    this.$destroy.next(null);
    this.$destroy.complete();
  }

  protected actionStripOptions(options: CommonAction[]): void {
    // Se elimina acceso a las opciones que el usuario no tiene acceso, estás acciones se definen en backend `reportConfig`
    this.defaultMenuActionOptions = this.defaultMenuActionOptions.filter((item) => options.includes(item.value as CommonAction));
    this._optionsReady = true;
  }

  private menuAction(row: any): DropdownMenuItem[] {
    if (!this._optionsReady) {
      return [];
    }
    if (row.archived) {
      return [this.defaultMenuActionOptions[0]].map((item) => {
        if (item.value === CommonAction.OPEN_DETAIL) {
          item.modernHref = `legacy/${this.openDetailUrl(row[this.primaryKey], row.outstanding_surveys_id)}`;
        }
        return item;
      });
    }
    return [...this.defaultMenuActionOptions].map((item) => {
      switch (item.value) {
        case CommonAction.OPEN_DETAIL:
          item.modernHref = `legacy/${this.openDetailUrl(row[this.primaryKey], row.outstanding_surveys_id)}`;
          break;
        case CommonAction.LOGGING:
          item.modernHref = `forms/form-log-lists/${this.masterId}/${this._reportCode}/${row.outstanding_surveys_id}/${row.code_name}`;
          break;
      }
      return item;
    });
  }

  protected toogleMenu(drop: GridDropDownItem) {
    const requestId = +drop.row[this.primaryKey];
    const outstandingSurveysId = +drop.row.outstanding_surveys_id;
    const formCode = drop.row.code_name;
    switch (drop.item.value) {
      case CommonAction.OPEN_DETAIL:
        this.menuService.navigateLegacy(this.openDetailUrl(requestId, +outstandingSurveysId), Module.FORMULARIE);
        break;
      case CommonAction.PRINTING_OPTIONS:
        this.loader.show();
        this.menuService.printReportTemplate({ masterId: this.masterId, moduleName: this.moduleName, requestId: requestId });
        break;
      case CommonAction.LOGGING:
        if (outstandingSurveysId) {
          this.menuService.navigate(`menu/forms/form-log-lists/${this.masterId}/${this._reportCode}/${outstandingSurveysId}/${formCode}`, Module.FORMULARIE);
        } else {
          this.menuService.navigate(`menu/forms/form-log-lists/${this.masterId}/${this._reportCode}/`, Module.FORMULARIE);
        }
        break;
    }
  }

  private openDetailUrl(requestId: number, outstandingSurveysId: number) {
    if (requestId) {
      return `v.request.survey.preview.view?task=preview&requestId=${requestId}`;
    }
    return `v.request.survey.preview.view?task=preview&id=O${outstandingSurveysId}`;
  }

  public onReportConfigChanged(event: ReportDTO): void {
    this._excelUploadEnabled = event.excelUploadEnabled;
    this._reportCode = event.slimReportCode;
    this._masterId = event.documentMasterId || null;
  }
  public onExcelUpload(_event: any): void {
    console.log('onExcelUpload', _event);
    this.bulkDialog().show();
  }
}
