import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { GridComponent } from '@/core/grid/grid.component';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import { Session } from '@/core/local-storage/session';
import { Deferred } from '@/core/utils/deferred';
import type { MenuService } from '@/modules/menu/services/menu.service';
import { DatePipe } from '@angular/common';
import { Component, type OnInit, inject, viewChild } from '@angular/core';
import type { RowType } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import { type ActionStrip, LinkColumn, TextColumn } from 'src/app/core/grid/utils/grid.interfaces';
import { CommonAction } from 'src/app/core/utils/enums';
import { FavoriteTaskType, Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import type { FavoriteTaskSaveDTO, QuickAccessHolder } from '../../menu/menu-main/menu-main.interfaces';
import { type IReportsListComponentTemplate, LANG_CONFIG } from './reports-list.interfaces';

@Component({
  selector: 'app-reports-list',
  styleUrls: ['reports-list.component.scss'],
  templateUrl: 'reports-list.component.html',
  imports: [GridComponent]
})
export class ReportsListComponent extends BnextCoreComponent implements OnInit, IReportsListComponentTemplate, i18n {
  datePipe = inject(DatePipe);

  url = 'forms/reports';
  columns: GridColumn[];
  titleLabel: string;
  isAdmin = Session.hasService(ProfileServices.IS_ADMIN);
  editAccess =
    Session.hasService(ProfileServices.IS_ADMIN) ||
    (Session.hasService(ProfileServices.USUARIO_CORPORATIVO) && Session.hasService(ProfileServices.FORM_ADMON_REPORT_ACCESS));
  LangConfig = LANG_CONFIG;

  get customTagName(): string {
    return LANG_CONFIG.componentName;
  }

  override get componentPath(): string {
    return LANG_CONFIG.componentPath;
  }

  private reportsListMenuActions: DropdownMenuItem[] = [
    {
      text: 'Abrir',
      value: CommonAction.OPEN_DETAIL,
      iconName: 'open_in_browser',
      hidden: false
    },
    {
      text: 'Permisos',
      value: CommonAction.EDIT,
      iconName: 'settings',
      hidden: !this.editAccess
    },
    {
      text: 'Historial',
      value: CommonAction.LOGGING,
      iconName: 'timeline',
      hidden: !this.isAdmin
    },
    {
      text: 'Favoritos',
      value: CommonAction.FAVORITE,
      iconName: 'favorite_border',
      hidden: false
    }
  ];

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.getData(row?.data) : []),
      rowIdentifierKey: 'id',
      toggleButtonTitle: ''
    }
  };

  readonly grid = viewChild<GridComponent>('grid');

  get ready(): boolean {
    return !!this.url;
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.init();
  }

  public langReady(): void {
    this.actionStripConfig.render.toggleButtonTitle = this.tag('open');
    for (const f of this.reportsListMenuActions) {
      f.text = this.tag(`${f.value}`);
    }
  }

  protected init(): void {
    this.perPage = Session.getGridSize();
    this.translate
      .getFrom(this.LangConfig, 'formReport.title')
      .pipe(takeUntil(this.$destroy))
      .subscribe({ next: (tag) => (this.titleLabel = tag) });
    this.columns = [];
    GridUtil.columns(this.columns)
      .push(
        'title',
        new LinkColumn({
          linkHref: `./${this.getLang()}/menu/forms/reports/c/{reportCode}/{documentMasterId}`,
          linkParams: ['reportCode', 'documentMasterId'],
          width: '450px'
        })
      )
      .push(
        'description',
        new TextColumn({
          width: '450px'
        })
      )
      .translate(this.$destroy, this.translateService, this.LangConfig, 'reports.column');
    this.cdr.detectChanges();
  }

  private getData(row: any): DropdownMenuItem[] {
    let openDetailUrl: string;
    if (row.documentMasterId) {
      openDetailUrl = `forms/reports/c/${row.reportCode}/${encodeURIComponent(row.documentMasterId)}`;
    } else {
      openDetailUrl = `forms/reports/c/${row.reportCode}`;
    }
    return this.reportsListMenuActions.map((f: DropdownMenuItem) => {
      switch (f.value) {
        case CommonAction.OPEN_DETAIL:
          f.modernHref = openDetailUrl;
          return f;
        case CommonAction.EDIT:
          f.legacyHref = `v-form-report-access.view?id=${row.nodeId}`;
          return f;
        case CommonAction.FAVORITE:
          return f;
        default:
          return f;
      }
    });
  }

  toggleMenuAction(drop: GridDropDownItem): void {
    const reportCode: string = encodeURIComponent(drop.row?.reportCode || '');
    const documentMasterId: string = encodeURIComponent(drop.row?.documentMasterId || '');
    switch (drop.item.value) {
      case CommonAction.OPEN_DETAIL:
        if (drop.row.documentMasterId) {
          this.menuService.navigate(`menu/forms/reports/c/${reportCode}/${documentMasterId}`, Module.FORMULARIE);
        } else {
          this.menuService.navigate(`menu/forms/reports/c/${reportCode}`, Module.FORMULARIE);
        }
        break;
      case CommonAction.LOGGING:
        this.menuService.navigate(`menu/forms/form-log-lists/${documentMasterId}/${reportCode}`, Module.FORMULARIE);
        break;
      case CommonAction.CUSTOM_ACTION:
        this.menuService.navigate(drop.item.modernHref, Module.FORMULARIE);
        break;
      case CommonAction.EDIT:
        this.menuService.navigateLegacy(`v-form-report-access.view?id=${drop.row.nodeId}`, Module.FORMULARIE);
        break;
      case CommonAction.FAVORITE:
        this.addReportToFavorite(drop.row.id, drop.row, this.menuService, drop.row.description, drop);
        break;
      case CommonAction.DELETE:
        this.deleteReport(drop);
        break;
      default:
        break;
    }
  }

  public addReportToFavorite(reportId: number, holder: QuickAccessHolder, menuService: MenuService, description: string, drop: GridDropDownItem): void {
    const saved = new Deferred<FavoriteTaskSaveDTO>();
    menuService.addQuickAccess({
      def: saved,
      icon: 'done',
      urlParams: `menu/forms/reports${reportId}`,
      menuPath: 'menu/forms/reports',
      type: FavoriteTaskType.REPORT,
      infoMessage: this.translate.instantFrom(LANG_CONFIG, 'add-to-favorite'),
      reportId: reportId,
      moduleName: String(Module.FORMULARIE).toLowerCase(),
      taskName: description,
      documentMasterId: drop.row.documentMasterId
    });
    saved.promise.then((result) => {
      if (result.success) {
        holder.isFavorite = 1;
        holder.favoriteTaskId = result.id;
      }
    });
  }

  protected deleteReport(_drop: GridDropDownItem): void {}

  protected refreshGrid(): void {
    this.grid().refresh();
  }
}
