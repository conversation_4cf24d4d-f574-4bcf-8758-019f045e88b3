import type { ReportColumnRuleDTO } from '@/core/grid-report/gird-report.interfaces';
import type { FieldSourceType } from '@/modules/forms/slim-report-base/slim-report-base.enums';
import type { FixedFieldType } from '@/modules/printing/printing-format/printing-format.enums';
import type { ICode, IDescription, IValid, Persistable } from 'src/app/core/utils/interfaces';

export interface FormSlimReportDTO extends Persistable, ICode, IDescription {
  businessUnitAccessValues: number[];
  businessUnitDepartmentAccessValues: number[];
  details: string; // <-- descripción larga
  documentMasterId: string;
  fixedFields: number[]; // <-- Guardar los nombres de las columnas
  fixedFilters: number[]; // <-- Guardar los nombres de las columnas
  processAccessValues: number[];
  surveyFields: string[]; // <-- Guardar los ids en el ENUM de FixedField
  surveyFilters: string[]; // <-- Guardar los ids en el ENUM de FixedField
  relatedFields: string[];
  userAccessValues: number[];
  restrictRecordsByDepartment: boolean;
  excelIdFields: string;
  excelUploadEnabled: boolean;
  whereFillerUserParticipate: boolean;
  localTimeForDates: boolean;
  orderColumns: ISlimReportOrderColumns[];
  ghostFields: ISlimReportsGhostField[];
  transformedFields: ISlimReportsTransformedField[];
}

export interface FormFixedField extends Persistable, IDescription {}
export interface SurveyDataFieldDTO {
  catalogLabel?: string;
  catalogSubType?: string;
  externalCatalogId?: number;
  fieldStage: string;
  sectionDesc: string;
  title: string;
  name: string; // <-- nombre de columna en BD
  answerPartType: number;
  type: string;
  id: number; // <-- id de columna en BD
}
export interface FormSlimReportDataSourceDTO {
  areaFields: Record<FixedFieldType, string | number> & {fieldsEnabled: boolean, additionalExtraAreaFields: number};
  title: string;
  surveyId: number;
  surveyFields: SurveyDataFieldDTO[];
}

export interface ISlimReportOrderColumns {
  id: string;
  description: string;
  alias?: string;
  order: number;
  fieldType: FieldSourceType;
}

export interface ISlimReportsGhostField extends Persistable, ICode, IDescription {
  alias?: string;
  order: number;
  id: number;
  code: string;
  description: string;
  // alias: string;
  name?: string;
  deleted: number;
}
export interface ISlimReportsTransformedField extends ISlimReportsGhostField, IValid {
  rules: ReportColumnRuleDTO[];
}
export interface IDeletedResponse {
  deletedQuery: number;
  deletedReport: number;
  deletedSlimReport: number;
}
