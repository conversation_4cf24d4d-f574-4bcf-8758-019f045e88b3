import { EnumUtil } from '@/core/utils/enum-util';
import { Directive, type OnDestroy, type OnInit, inject, viewChild } from '@angular/core';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';

import * as GridUtil from '@/core/grid/utils/grid-util';
import { ConfigApp } from '@/core/local-storage/config-app';
import { FormBuilder, FormControl, type FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import type { IChangeCheckboxEventArgs, IComboSelectionChangingEventArgs } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import type { RowSelection } from 'src/app/core/grid-base-select/grid-base-select.interfaces';
import type { GridMultiSelectComponent } from 'src/app/core/grid-multi-select/grid-multi-select.component';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import { AvatarColumn, TextColumn } from 'src/app/core/grid/utils/grid.interfaces';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import type { MultiSelectComponent } from 'src/app/core/multi-select/multi-select.component';
import { AppService } from 'src/app/core/services/app.service';
import { BnextLoaderActivationService } from 'src/app/core/services/bnext-loader-activation.service';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import type { DialogMessage } from 'src/app/core/services/dialog.service.interfaces';
import { NoticeService } from 'src/app/core/services/notice.service';
import { Deferred } from 'src/app/core/utils/deferred';

import { EntityUtil } from 'src/app/core/utils/entity';
import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FormUtil } from 'src/app/core/utils/form-util';
import type { IMailableUser, IPersistableCodeDescription } from 'src/app/core/utils/interfaces';

import { stringToColour } from '@/core/utils/string-util';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { FixedField } from '../../printing/printing-format/printing-format.enums';
import type { FormFixedField, FormSlimReportDTO, FormSlimReportDataSourceDTO, ISlimReportOrderColumns, SurveyDataFieldDTO } from './slim-report-base.interfaces';

@Directive()
export abstract class SlimReportBaseComponent extends BnextCoreComponent implements OnInit, OnDestroy, FabButton, i18n {
  noticeService = inject(NoticeService);
  api = inject(AppService);

  formBuilder = inject(FormBuilder);

  loader = inject(BnextLoaderActivationService);
  activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.forms',
    componentName: 'slim-report-base'
  };

  private _fixedFieldsData: FormFixedField[] = [];
  private _surveyFieldsData: SurveyDataFieldDTO[] = [];
  private _fixedFieldsValues: (string | number)[] = []; // <-- Guardar los ids en el ENUM de FixedField
  private _surveyFieldsValues: (string | number)[] = []; // <-- Guardar los nombres de las columnas
  private _ready = false;
  private _surveyFieldsDataStages: string[] = [];
  private _title = null;
  private _slimReportsOrderColumns: ISlimReportOrderColumns[] = [];
  private dragSlimReportSurveyField = '';

  surveyFieldsDataFilterStage: string = null;

  override get customTagName(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentName;
  }
  override get componentPath(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentPath;
  }
  get title(): string {
    return this._title;
  }
  get surveyFieldsDataStages(): string[] {
    return this._surveyFieldsDataStages;
  }
  get showGenerateCode() {
    return false;
  }
  get controller(): string {
    return 'forms/slim-report';
  }
  abstract get saveController(): string;

  get businessUnitAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/business-unit-access/list`;
  }
  get businessUnitDepartmentAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/business-unit-department-access/list`;
  }
  get userAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/user-access/list`;
  }
  get processAccessController(): string {
    // Se sobre escribe en `edit` para incluir el `id`
    return `${this.controller}/process-access/list`;
  }
  get fixedFieldsData(): FormFixedField[] {
    return this._fixedFieldsData;
  }
  get surveyFieldsData(): SurveyDataFieldDTO[] {
    return this._surveyFieldsData;
  }
  get ready(): boolean {
    // Se sobre escribe en `edit` para validar el `id`
    return this._ready;
  }

  private restrictRecordsByDepartment = 1;
  private validateAccessFormDepartment = 1;

  get slimReportsOrderColumns(): ISlimReportOrderColumns[] {
    return this._slimReportsOrderColumns;
  }

  set slimReportsOrderColumns(_slimReportsOrderColumns) {
    this._slimReportsOrderColumns = _slimReportsOrderColumns;
  }

  readonly businessUnitAccess = viewChild<GridMultiSelectComponent>('businessUnitAccess');
  readonly businessUnitDepartmentAccess = viewChild<GridMultiSelectComponent>('businessUnitDepartmentAccess');
  readonly userAccess = viewChild<GridMultiSelectComponent>('userAccess');
  readonly processAccess = viewChild<GridMultiSelectComponent>('processAccess');
  readonly surveyFieldsComponent = viewChild<MultiSelectComponent<SurveyDataFieldDTO>>('surveyFields');

  readonly fixedFieldsComponent = viewChild<MultiSelectComponent<FormFixedField>>('fixedFields');

  masterId: string = null;
  showHelp = false;
  busy = true;
  mainForm: FormGroup<{
    code: FormControl<string>;
    description: FormControl<string>;
    details: FormControl<string>;
    fixedFieldsValues: FormControl<FormFixedField[]>;
    generateCode: FormControl<boolean>;
    surveyFieldsValues: FormControl<SurveyDataFieldDTO[]>;
    restrictRecordsByDepartment: FormControl<boolean>;
    whereFillerUserParticipate: FormControl<boolean>;
    localTimeForDates: FormControl<boolean>;
  }>;

  // FabButton
  fabFloat = true;
  fabOptionsAvailable?: string[]; // <-- [OPCIONAL], Oculta opciones del arreglo `fabButtons`
  fabButtons: (FabButtonMenuItem | DropdownMenuItem)[] = [
    {
      iconName: 'save',
      text: 'root.common.button.save',
      value: CommonAction.SAVE
    },
    {
      iconName: 'arrow_back',
      text: 'root.common.button.back',
      value: CommonAction.BACK
    }
  ];

  // SECCIÓN: Permisos
  businessUnitAccessColumns: GridColumn[] = [];
  businessUnitDepartmentAccessColumns: GridColumn[] = [];
  userAccessColumns: GridColumn[] = [];
  processAccessColumns: GridColumn[] = [];

  businessUnitAccessValues: IPersistableCodeDescription[] = [];
  businessUnitDepartmentAccessValues: IPersistableCodeDescription[] = [];
  userAccessValues: IMailableUser[] = [];
  processAccessValues: IPersistableCodeDescription[] = [];

  constructor() {
    super();

    // coluimnas de permisos
    GridUtil.columns(this.businessUnitAccessColumns, this.businessUnitDepartmentAccessColumns, this.userAccessColumns, this.processAccessColumns)
      .push(
        'code',
        new TextColumn({
          autoSize: true,
          fixedFilter: true
        })
      )
      .push(
        'description',
        new TextColumn({
          autoSize: true,
          fixedFilter: true
        })
      )
      .translate(this.$destroy, this.translateService, SlimReportBaseComponent.LANG_CONFIG, 'columns');
    GridUtil.columns(this.userAccessColumns)
      .removeLast()
      .push(
        'account',
        new TextColumn({
          autoSize: true,
          fixedFilter: true
        })
      )
      .push(
        'description',
        new AvatarColumn({
          autoSize: true,
          fixedFilter: true,
          avatarIdKey: 'id',
          avatarNameKey: 'description',
          showAvatarNameKey: true,
          avatarSecundaryKey: 'correo'
        })
      )
      .translate(this.$destroy, this.translateService, SlimReportBaseComponent.LANG_CONFIG, 'columns');
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.navLang
      .getRouteParam(':masterId', null, false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([masterId]) => {
        this.activatedRoute.queryParams.subscribe((params) => {
          const { restrictRecordsByDepartment, validateAccessFormDepartment } = params;
          this.restrictRecordsByDepartment = +restrictRecordsByDepartment;
          this.validateAccessFormDepartment = +validateAccessFormDepartment;
          if (Boolean(+validateAccessFormDepartment) || !+restrictRecordsByDepartment) {
            this.mainForm.get('restrictRecordsByDepartment').disable();
          }
          this.masterId = masterId;
          // campos del formulario
          this.api.get({ url: `${this.controller}/data-source/${encodeURIComponent(this.masterId)}`, cancelableReq: this.$destroy }).subscribe({
            next: (result: FormSlimReportDataSourceDTO) => {
              this._title = result.title;
              this._surveyFieldsData = result.surveyFields.sort((a, b) => a.id - b.id);
              this._surveyFieldsDataStages = Array.from(new Set(result.surveyFields.map((field) => field.fieldStage)));
              this.busy = false;
              this.cdr.detectChanges();
              this.loadData();
              this._surveyFieldsData.map((m) => {
                m.title = m.title || this.tag('defaultTitle');
              });
            },
            error: (error) => {
              console.log(error);
              this.busy = false;
            }
          });
        });
      });
  }
  override langReady(): void {
    // form
    this.mainForm = this.formBuilder.group({
      code: new FormControl(),
      description: new FormControl(null, Validators.required),
      details: new FormControl(null, Validators.required),
      fixedFieldsValues: new FormControl(null, Validators.required),
      generateCode: new FormControl(true),
      surveyFieldsValues: new FormControl(null, Validators.required),
      whereFillerUserParticipate: new FormControl(false),
      localTimeForDates: new FormControl(false),
      restrictRecordsByDepartment: new FormControl(false)
    });
    // buttons
    for (const item of this.fabButtons) {
      if (item.text.startsWith('root.')) {
        item.text = this.tag(item.text);
      } else {
        item.text = this.tag(`fixedFields.${item.text}`);
      }
    }
    // campos fijos
    this._fixedFieldsData = EnumUtil.getStringValues(FixedField).map((key) => {
      return {
        id: FixedField[key],
        description: this.tag(`fixedFields.${key}`),
        fixed: true
      };
    });
    // Se agregan etiquetas de columnas
    GridUtil.columns(this.businessUnitAccessColumns, this.businessUnitDepartmentAccessColumns, this.userAccessColumns, this.processAccessColumns).translate(
      this.$destroy,
      this.translateService,
      SlimReportBaseComponent.LANG_CONFIG
    );
    this.busy = false;
    this._ready = true;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  self(): any {
    return this;
  }

  stageColor(stage: string): string {
    return stringToColour(stage);
  }
  filterByStage(newStage?: string): void {
    if (typeof newStage === 'string') {
      this.surveyFieldsDataFilterStage = newStage;
    } else {
      this.surveyFieldsDataFilterStage = null;
    }
  }
  onChangeSurveyFieldsValues(value: (string | number)[]): void {
    this._surveyFieldsValues = value;
  }
  onChangeFixedFieldsValues(value: (string | number)[]): void {
    this._fixedFieldsValues = value;
  }

  onBusinessUnitAccessSelect(args: RowSelection<any[]>): void {
    if (!args?.value) {
      return;
    }
    if (Array.isArray(args.value)) {
      this.businessUnitAccessValues = args.value;
    } else {
      this.businessUnitAccessValues = [];
    }
  }

  onBusinessUnitDepartamentAccessSelect(args: RowSelection<any[]>): void {
    if (!args?.value) {
      return;
    }
    if (Array.isArray(args.value)) {
      this.businessUnitDepartmentAccessValues = args.value;
    } else {
      this.businessUnitDepartmentAccessValues = [];
    }
  }

  onProcessAccessSelect(args: RowSelection<any[]>): void {
    if (!args?.value) {
      return;
    }
    if (Array.isArray(args.value)) {
      this.processAccessValues = args.value;
    } else {
      this.processAccessValues = [];
    }
  }

  onUserAccessSelect(args: RowSelection<any[]>): void {
    if (!args?.value) {
      return;
    }
    if (Array.isArray(args.value)) {
      this.userAccessValues = args.value;
    } else {
      this.userAccessValues = [];
    }
  }

  fabButtonAction(item: FabButtonMenuItem | DropdownMenuItem): void {
    switch (item.value) {
      case CommonAction.SAVE:
        this.save();
        break;
      case CommonAction.BACK:
        this.return();
        break;
      default:
        console.warn(`Action is not supported ${item.value}`);
        break;
    }
  }

  protected save(): void {
    if (!FormUtil.isValid(this.mainForm).status) {
      this.noticeService.notice(this.tag('requiredNotice'));
      this.cdr.detectChanges();
      return;
    }
    const formReportMaxColumns = ConfigApp.getFormReportMaxColumns();
    if (formReportMaxColumns > 0 && this.slimReportsOrderColumns?.length > formReportMaxColumns) {
      const message = this.tag('maxFormReportColumnsNotice')
        .replace('{maxColumns}', formReportMaxColumns.toString())
        .replace('{currentColumns}', this.slimReportsOrderColumns?.length?.toString() || '0');
      this.dialogService.error(message);
      return;
    }
    const entity: FormSlimReportDTO = this.getEntityData();
    this.busy = true;
    this.loader.show();
    this.api.post({ url: `${this.saveController}`, cancelableReq: this.$destroy, postBody: entity, handleFailure: false }).subscribe({
      next: (result) => {
        if (this.debug()) {
          console.log('>> save, result: ', result);
        }
        this.busy = false;
        this.loader.hide();
        const dialogConf: DialogMessage = {
          message: this.tag('save-success'),
          rightButton: 'root.common.button.control',
          leftButton: 'root.common.button.create',
          title: 'root.common.error.messageSystem',
          closeOnEscape: false,
          action: new Deferred<DialogResult>()
        };
        this.dialogService.dialogMessage(dialogConf).then(
          () => this.openList(),
          () => this.openCreate()
        );
        this.menuService.refreshLoggedUser();
        this.cdr.detectChanges();
        this.loader.hide();
      },
      error: (error) => {
        this.busy = false;
        this.loader.hide();
        ErrorHandling.notifyError(error, this.navLang);
        this.cdr.detectChanges();
      }
    });
  }

  protected openCreate(): void {
    this.menuService.navigate(`menu/forms/slim-report/${encodeURIComponent(this.masterId)}/add`, Module.FORMULARIE);
  }

  protected openList() {
    this.menuService.navigate(
      `menu/forms/slim-reports/${encodeURIComponent(this.masterId)}` +
        `?restrictRecordsByDepartment=${this.restrictRecordsByDepartment}` +
        `&validateAccessFormDepartment=${this.validateAccessFormDepartment}`,
      Module.FORMULARIE
    );
  }

  protected getEntityData(): FormSlimReportDTO {
    const data = this.mainForm.value;
    const entity: FormSlimReportDTO = {
      id: -1,
      code: data.code,
      description: data.description,
      documentMasterId: this.masterId,
      details: data.details,
      fixedFields: (this._fixedFieldsValues as number[]) || [],
      surveyFields: (this._surveyFieldsValues as string[]) || [],
      relatedFields: this.getRelatedFields(),
      businessUnitAccessValues: EntityUtil.getIds(this.businessUnitAccess().valueArray || []),
      businessUnitDepartmentAccessValues: EntityUtil.getIds(this.businessUnitDepartmentAccess().valueArray || []),
      userAccessValues: EntityUtil.getIds(this.userAccess().valueArray || []),
      processAccessValues: EntityUtil.getIds(this.processAccess().valueArray || []),
      whereFillerUserParticipate: data.whereFillerUserParticipate ?? false,
      localTimeForDates: data.localTimeForDates ?? false,
      restrictRecordsByDepartment: data.restrictRecordsByDepartment ?? false,
      orderColumns: this.slimReportsOrderColumns
    };
    return entity;
  }

  private getRelatedFields(): string[] {
    if (!this._surveyFieldsData?.length || !this._surveyFieldsValues?.length) {
      return [];
    }
    const selectedExternalCatalogs =
      this._surveyFieldsData
        .filter((field) => this._surveyFieldsValues.includes(field.name))
        .map((field) => field.externalCatalogId)
        .filter((id) => !!id) || [];
    if (!selectedExternalCatalogs?.length) {
      return [];
    }
    const result =
      this._surveyFieldsData
        .filter((field) => selectedExternalCatalogs.indexOf(field.externalCatalogId) !== -1)
        .map((field) => field.name)
        .filter((name) => !!name) || [];
    return result;
  }

  changeGenerateCode(event: IChangeCheckboxEventArgs): void {
    if (event.checked) {
      this.mainForm.controls.code.disable();
    } else {
      this.mainForm.controls.code.enable();
    }
  }

  return(): void {
    this.navLang.back();
  }

  protected loadData(): void {}

  protected ghostCreateHandler(_event) {
    _event.style.visibility = 'hidden';
  }

  protected dragEndHandler(_event: HTMLElement) {
    _event.style.visibility = 'visible';
  }

  protected dragStartHandler(surveyField: string) {
    this.dragSlimReportSurveyField = surveyField;
  }

  protected onEnterHandler(ev): void {
    if (this.dragSlimReportSurveyField === ev.owner.element.nativeElement.id) {
      return;
    }
    const dragIndex = this.slimReportsOrderColumns.findIndex((field) => field.id === this.dragSlimReportSurveyField);
    const dropIndex = this.slimReportsOrderColumns.findIndex((field) => field.id === ev.owner.element.nativeElement.id);
    this.swapFields(dragIndex, dropIndex);
  }

  private swapFields(dragIndex: number, dropIndex: number) {
    const flagDragField = this.slimReportsOrderColumns[dragIndex];
    const flagDropField = this.slimReportsOrderColumns[dropIndex];
    const flagDropFieldOrder = flagDropField.order;
    flagDropField.order = flagDragField.order;
    this.slimReportsOrderColumns.splice(dragIndex, 1, flagDropField);
    flagDragField.order = flagDropFieldOrder;
    this.slimReportsOrderColumns.splice(dropIndex, 1, flagDragField);
    this.cdr.detectChanges();
  }

  protected fillSlimReportsColumnsOrder(item: IComboSelectionChangingEventArgs): void {
    for (const value of item.added) {
      if (this.instanceOfFormFixedField(value)) {
        const data: FormFixedField = value as unknown as FormFixedField;
        this.slimReportsOrderColumns.push({ order: this.slimReportsOrderColumns.length + 1, description: data.description, fixedField: true, id: data.id.toString() });
      } else {
        this.slimReportsOrderColumns.push({ order: this.slimReportsOrderColumns.length + 1, description: value.title, fixedField: false, id: value.name });
      }
    }
    for (const value of item.removed) {
      if (this.instanceOfFormFixedField(value)) {
        const data: FormFixedField = value as unknown as FormFixedField;
        const idIndex = this.slimReportsOrderColumns.findIndex((f) => f.id === data.id.toString());
        if (idIndex === -1) {
          continue;
        }
        this.slimReportsOrderColumns.splice(idIndex, 1);
        this.updatePositionAfterRemoveItem();
      } else {
        const idIndex = this.slimReportsOrderColumns.findIndex((f) => f.id === value.name);
        if (idIndex === -1) {
          continue;
        }
        this.slimReportsOrderColumns.splice(idIndex, 1);
        this.updatePositionAfterRemoveItem();
      }
    }
  }

  private updatePositionAfterRemoveItem(): void {
    for (const item of this.slimReportsOrderColumns) {
      const index = this.slimReportsOrderColumns.indexOf(item);
      item.order = index + 1;
    }
  }

  private instanceOfFormFixedField(object: any): object is FormFixedField {
    return 'fixed' in object;
  }

  protected fillSlimReportGridOrder(slimReportOrderColumns: ISlimReportOrderColumns[]): void {
    if (slimReportOrderColumns === null || typeof slimReportOrderColumns === 'undefined' || slimReportOrderColumns.length === 0) {
      return;
    }
    const slimReportOrderColumnsFixed: ISlimReportOrderColumns[] = slimReportOrderColumns.filter((f) => f.fixedField === true);
    const slimReportOrderColumnsSurvey: ISlimReportOrderColumns[] = slimReportOrderColumns.filter((f) => f.fixedField === false);
    for (const field of slimReportOrderColumnsSurvey) {
      const multiSelectValue = this.surveyFieldsComponent().data.find((f) => f.name === field.description);
      if (multiSelectValue) {
        field.description = multiSelectValue.title;
        field.id = multiSelectValue.name;
      }
    }
    for (const fixed of slimReportOrderColumnsFixed) {
      const multiSelectValue = this.fixedFieldsComponent().data.find((f) => +f.id === +fixed.id);
      if (multiSelectValue) {
        fixed.description = multiSelectValue.description;
      }
    }
    this.slimReportsOrderColumns.push(...slimReportOrderColumnsSurvey);
    this.slimReportsOrderColumns.push(...slimReportOrderColumnsFixed);
    if (slimReportOrderColumns[0].order === null || typeof slimReportOrderColumns[0].order === 'undefined') {
      for (const f of slimReportOrderColumns) {
        const index = slimReportOrderColumns.indexOf(f);
        f.order = index + 1;
      }
    }
    this.slimReportsOrderColumns.sort((a, b) => a.order - b.order);
  }
}
