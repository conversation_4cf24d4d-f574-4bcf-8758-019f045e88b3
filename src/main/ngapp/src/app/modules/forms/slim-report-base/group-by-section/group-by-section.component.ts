import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { DropdownSearchComponent } from '@/core/dropdown-search/dropdown-search.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { SelectComponent } from '@/core/select/select.component';
import { FormUtil } from '@/core/utils/form-util';
import { cloneArrayWithModifiedPosition } from '@/core/utils/signal-util';
import type { ITextValue, TextHasValue } from '@/core/utils/text-has-value';
import { SlimReportBaseComponent } from '@/modules/forms/slim-report-base/slim-report-base.component';
import {
  type AggregateFunctionType,
  FieldSourceCodePrefix
} from '@/modules/forms/slim-report-base/slim-report-base.enums';
import type { ISlimReportsSummaryGroupField } from '@/modules/forms/slim-report-base/slim-report-base.interfaces';
import { getUniqueCode } from '@/modules/forms/slim-report-base/slim-report-base.util';
import { NgClass } from '@angular/common';
import { Component, type OnDestroy, type OnInit, input, model, output } from '@angular/core';
import {
  type FormControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormControl,
  UntypedFormGroup,
  Validators
} from '@angular/forms';
import {
  IgxButtonDirective,
  IgxDividerDirective,
  IgxForOfDirective,
  IgxIconComponent,
  IgxLinearProgressBarComponent,
  IgxListComponent,
  IgxListItemComponent,
  IgxRippleDirective
} from 'igniteui-angular';

@Component({
  selector: 'app-group-by-section',
  templateUrl: './group-by-section.component.html',
  styleUrl: './group-by-section.component.scss',
  imports: [
    NgClass, // <-- Debe ir al inicio
    BnextTranslatePipe,
    FormsModule,
    IgxButtonDirective,
    IgxDividerDirective,
    IgxIconComponent,
    IgxLinearProgressBarComponent,
    IgxListComponent,
    IgxListItemComponent,
    IgxRippleDirective,
    ReactiveFormsModule,
    IgxForOfDirective,
    SelectComponent,
    DropdownSearchComponent
  ]
})
export class GroupBySectionComponent extends BnextCoreComponent implements OnInit, OnDestroy, i18n {

  override get customTagName(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentName;
  }

  override get componentPath(): string {
    return SlimReportBaseComponent.LANG_CONFIG.componentPath;
  }

  override langReady() {

  }

  aggregateFunctions: ITextValue<AggregateFunctionType>[] = [
    {text: 'SUM', value: 'SUM'},
    {text: 'AVG', value: 'AVG'},
    {text: 'MAX', value: 'MAX'},
    {text: 'MIN', value: 'MIN'}
  ];

  onlyNumbersAvailableFields = input<TextHasValue<string>[]>([]);
  surveyAvailableFields = input<TextHasValue<string>[]>([]);
  surveySelectedFields = input<TextHasValue<string>[]>([]);
  fieldRemoved = output<ISlimReportsSummaryGroupField>();
  fieldAdded = output<ISlimReportsSummaryGroupField>();
  fieldChanged = output<ISlimReportsSummaryGroupField>();
  summaryFieldValues = model<ISlimReportsSummaryGroupField[]>([]);
  summaryGroupFieldForm = new UntypedFormGroup({});

  touched = false;

  public addSummaryGroupField(f?: ISlimReportsSummaryGroupField) {
    const name = f?.name || `summaryGroupFieldTemp${this.summaryFieldValues().length}`;
    const field: ISlimReportsSummaryGroupField = f || {
      id: -1 * (this.summaryFieldValues().length + 1),
      aggregateFunctionForElse: 'STRING_AGG',
      aggregateFunctionForResult: null,
      code: null,
      deleted: 0,
      description: null,
      groupByColumnName: null,
      groupByFieldName: null,
      groupValueFieldName: null,
      name: name,
      order: 9999,
      valid: false
    };
    this.addSummaryGroupFieldFormControl(name, 'aggregateFunctionForResult', field.aggregateFunctionForResult);
    this.addSummaryGroupFieldFormControl(name, 'groupByColumnName', field.groupByColumnName);
    this.addSummaryGroupFieldFormControl(name, 'groupByFieldName', field.groupByFieldName);
    this.addSummaryGroupFieldFormControl(name, 'groupValueFieldName', field.groupValueFieldName);
    this.summaryFieldValues.update(v => [...v, field]);
  }

  removeSummaryGroupField(itemIdx: number, itemName: string): void {
    if (this.summaryFieldValues().filter((f) => f.deleted === 0).length < 0) {
      return;
    }
    this.summaryGroupFieldForm.removeControl(itemName);
    this.summaryFieldValues.update(v => [...v.slice(0, itemIdx), ...v.slice(itemIdx + 1)]);
    this.fieldRemoved.emit(this.summaryFieldValues()[itemIdx]);
  }
  public addSummaryGroupFieldFormControl(itemName: string, key: keyof ISlimReportsSummaryGroupField, value?: string ): FormControl {
    this.summaryGroupFieldForm.addControl(`${itemName}_ ${key}`, new UntypedFormControl('', [Validators.required]));
    const fc = <FormControl>this.summaryGroupFieldForm.get(`${itemName}_ ${key}`) || null;
    if (fc) {
      fc.setValue(value || null);
    }
    return fc;
  }
  getSummaryGroupFieldFormControl(item: ISlimReportsSummaryGroupField, key: keyof ISlimReportsSummaryGroupField): FormControl {
    let fc = <FormControl>this.summaryGroupFieldForm.get(`${item.name}_ ${key}`);
    if (!this.summaryGroupFieldForm.get(`${item.name}_ ${key}`)) {
       fc = this.addSummaryGroupFieldFormControl(item.name, key);
       fc.setValue(item[key]);
    }
    return fc || null;
  }
  setSummaryGroupFieldFormControlValue(value: string, itemIdx: number, key: keyof ISlimReportsSummaryGroupField): void {
    this.getSummaryGroupFieldFormControl(this.summaryFieldValues()[itemIdx], key)?.setValue(value);
  }

  onChangeAggregateFunctionForResult(value: AggregateFunctionType, itemIdx: number) {
    this.onChangeAction({ aggregateFunctionForResult: value }, itemIdx);
  }
  onChangeGroupByFieldName(item: TextHasValue<string>, itemIdx: number) {
    this.onChangeAction({ groupByFieldName: item?.value }, itemIdx);
  }
  onChangeGroupByColumnName(item: TextHasValue<string>, itemIdx: number) {
    this.onChangeAction({ groupByColumnName: item?.value }, itemIdx);
  }
  onChangeGroupValueFieldName(item: TextHasValue<string>, itemIdx: number) {
    this.onChangeAction({ groupValueFieldName: item?.value}, itemIdx);
  }
  async onClickChangeDescription(itemIdx: number) {
    const field = this.summaryFieldValues()[itemIdx];
    const dialogResult = await this.dialogService.input({
      message: this.tag('transformedFields.please-capture-field-message'),
      inputMaxLimit: 128,
      inputMinLimit: 5,
      inputValue: field.description || null
    }, 'root.common.button.ok', 'root.common.button.cancel');
    this.onChangeAction({ description: dialogResult.inputValue }, itemIdx, (values) => {
      if (!this.summaryFieldValues()[itemIdx].code) {
        values.code = getUniqueCode(dialogResult.inputValue, this.summaryFieldValues(), 1, FieldSourceCodePrefix[FieldSourceCodePrefix.SUMMARY_GROUP]);
      }
      return values;
    });
  }

  public onChangeAction(values: Partial<ISlimReportsSummaryGroupField>, itemIdx: number, fn?: (values: Partial<ISlimReportsSummaryGroupField>) => Partial<ISlimReportsSummaryGroupField>) {
    const isNew = !this.summaryFieldValues()[itemIdx].code;
    if (typeof fn === 'function') {
      values = fn(values);
    }
    this.summaryFieldValues.update(a => cloneArrayWithModifiedPosition(a, values, itemIdx));
    Object.keys(values).forEach(key => {
      this.setSummaryGroupFieldFormControlValue(values[key], itemIdx, <keyof ISlimReportsSummaryGroupField>key);
    })
    this.refreshValidity(itemIdx);
    const field = this.summaryFieldValues()[itemIdx];
    if (field.code) {
      if (isNew) {
        this.fieldRemoved.emit(field);
        this.fieldAdded.emit(field);
      } else {
        this.fieldChanged.emit(field);
      }
    }
  }
  private refreshValidity(itemIdx: number) {
    const field = this.summaryFieldValues()[itemIdx];
    field.valid = !!(
      field.id !== undefined && field.id !== null &&
      field.aggregateFunctionForElse &&
      field.aggregateFunctionForResult &&
      field.code &&
      field.deleted !== undefined && field.deleted !== null &&
      field.description &&
      field.groupByColumnName &&
      field.groupByFieldName &&
      field.groupValueFieldName &&
      field.name &&
      field.order !== undefined && field.order !== null
    );
  }

  public isValid(): boolean {
    this.touched = true;
    return FormUtil.isValid(this.summaryGroupFieldForm, [], true).status && !(
      this.summaryFieldValues().filter(f => !f.valid).length > 0
    );
  }
}
