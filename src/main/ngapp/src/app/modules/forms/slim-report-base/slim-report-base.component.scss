::ng-deep {
  igx-buttongroup {
    .igx-icon-button + .igx-icon-button,
    .igx-button + .igx-button {
      > .igx-icon {
        margin-left: 0.5rem;
      }
      + .igx-icon-button > .igx-icon,
      + .igx-button > .igx-icon {
        margin-left: 0;
      }
    }
    .igx-icon-button,
    .igx-button > {
      .igx-icon {
        line-height: 1rem;
        font-size: 1rem;
        + span {
          margin-left: 0.25rem;
        }
      }
      span {
        line-height: 1rem;
      }
    }
  }

  app-grid-multi-select div.grid-base-select-container .value-grid.collapsable.uncollapsed {
    padding: 0;
  }

  app-grid-multi-select div.grid-base-select-container .grid-layout.is-large {
    margin: 0;
  }
}
:host ::ng-deep {
  .grid-base-select-container.grid-base-select-container--desktop {
    padding: 1px;
    .grid-component-container.elevation-2 {
      box-shadow: none;
      border-top: 1px solid #e6e6e6;
    }
    .grid-base-select-title {
      padding-left: 1rem;
    }
  }
  margin-right: 0.25rem;
  line-height: 1rem;
  font-size: 1rem;
}
.grid-multi-select-container > button {
  right: 4rem;
}

igx-buttongroup {
  --ig-size: var(--ig-size-large);
}

.slim-reports-grid-order {
  margin-top: 40px;
  display: flex;
  height: fit-content;
  flex-direction: row;
  max-height: 15rem;
  min-height: 1rem;
  gap: 1rem;
  flex-wrap: wrap;
  padding: 1rem;
  border: solid 1px #ccc;
  align-items: stretch;
  overflow-y: scroll;
  justify-content: space-between;
  igx-badge {
    transform: translate(-50%, -1.125rem);
    position: relative;
  }
}

.item-grid {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 0.3em;
  border: 1px solid #ccc;
  z-index: 2;
  min-height: 2.25rem;
  max-height: 3.25rem;
  width: 12rem;
  cursor: grab;
  &-info {
    width: inherit;
    display: flex;
    justify-content: space-between;
    padding: 0;
    height: inherit;
    align-items: center;
    > span {
      line-height: 1rem;
      display: flex;
      align-items: center;
      margin-left: 0.5rem;
    }
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  flex-wrap: wrap;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: break-spaces;
}
