export type FieldSourceType = 'TRANSFORMED' | 'GHOST' | 'FIXED' | 'FLEXI' | 'SUMMARY_GROUP';
export type TransformedFieldRuleType =
  | 'IS_EMPTY'
  | 'IS_NOT_EMPTY'
  | 'STARTS_WITH'
  | 'CONTAINS'
  | 'ENDS_WITH'
  | 'EQUALS'
  | 'NOT_EQUALS'
  | 'GREATER_THAN'
  | 'LESS_THAN'
  | 'GREATER_THAN_OR_EQUALS'
  | 'LESS_THAN_OR_EQUALS';
export type AggregateFunctionType = 'AVG' | 'SUM' | 'MIN' | 'MAX' | 'STRING_AGG';
export type SlimReportValueType = 'string' | 'number' | 'field' | 'date';
export type TransformedFieldRuleValueType = SlimReportValueType | 'same_as_evaluated_field' | 'none';
export type TransformedTypeFinalValueType = 'CUSTOM_TEXT' | 'EVALUATED_FIELD';

export const FieldSource: Record<FieldSourceType, FieldSourceType> = {
  SUMMARY_GROUP: 'SUMMARY_GROUP',
  TRANSFORMED: 'TRANSFORMED',
  GHOST: 'GHOST',
  FIXED: 'FIXED',
  FLEXI: 'FLEXI'
} as const;

export const FieldSourceCodePrefix: Record<FieldSourceType, string> = {
  TRANSFORMED: 't_',
  SUMMARY_GROUP: 'sg_',
  GHOST: 'g_',
  FIXED: 'fi_',
  FLEXI: 'fl_'
} as const;

export const TransformedFieldRuleValueEnabled: Record<TransformedFieldRuleType, TransformedFieldRuleValueType> = {
  CONTAINS: 'string',
  ENDS_WITH: 'string',
  EQUALS: 'same_as_evaluated_field',
  GREATER_THAN: 'number',
  GREATER_THAN_OR_EQUALS: 'number',
  IS_EMPTY: 'none',
  IS_NOT_EMPTY: 'none',
  LESS_THAN: 'number',
  LESS_THAN_OR_EQUALS: 'number',
  NOT_EQUALS: 'same_as_evaluated_field',
  STARTS_WITH: 'string'
} as const;

export const FieldSourceValue: Record<FieldSourceType, number> = {
  TRANSFORMED: 1,
  GHOST: 2,
  FIXED: 3,
  FLEXI: 4,
  SUMMARY_GROUP: 5
} as const;

export const TransformedFieldRuleValue: Record<TransformedFieldRuleType, number> = {
  CONTAINS: 1,
  ENDS_WITH: 2,
  EQUALS: 3,
  GREATER_THAN: 4,
  GREATER_THAN_OR_EQUALS: 5,
  IS_EMPTY: 6,
  IS_NOT_EMPTY: 7,
  LESS_THAN: 8,
  LESS_THAN_OR_EQUALS: 9,
  NOT_EQUALS: 10,
  STARTS_WITH: 11
} as const;

export const TransformedTypeFinalValue: Record<TransformedTypeFinalValueType, number> = {
  CUSTOM_TEXT: 1,
  EVALUATED_FIELD: 2
} as const;
