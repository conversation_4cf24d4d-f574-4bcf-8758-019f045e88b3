import { DropdownMenuComponent } from '@/core/dropdown-menu/dropdown-menu.component';
import { DropdownSearchComponent } from '@/core/dropdown-search/dropdown-search.component';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import type { TextHasValue, TextLongValue } from '@/core/utils/text-has-value';
import type { FormPublishMigrationColumnDTO, FormPublishMigrationColumnDataSourceDTO } from '@/modules/forms/form-migrations/form-migrations.interfaces';
import { DatePipe } from '@angular/common';
import { Component, type OnInit, inject } from '@angular/core';
import { type FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { IgxLinearProgressBarComponent } from 'igniteui-angular';
import { takeUntil } from 'rxjs/operators';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import { Session } from 'src/app/core/local-storage/session';
import { AppService } from 'src/app/core/services/app.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import { CommonAction } from 'src/app/core/utils/enums';
import { FormMigrationsAction } from './form-migrations.enums';

@Component({
  selector: 'app-form-migrations',
  styleUrls: ['form-migrations.component.scss'],
  templateUrl: './form-migrations.component.html',
  imports: [BnextTranslatePipe, DropdownMenuComponent, ReactiveFormsModule, IgxLinearProgressBarComponent, IgxLinearProgressBarComponent, DropdownSearchComponent]
})
export class FormMigrationsComponent extends BnextCoreComponent implements OnInit, FabButton, i18n {
  datePipe = inject(DatePipe);

  activatedRoute = inject(ActivatedRoute);
  api = inject(AppService);
  noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.forms',
    componentName: 'form-migrations'
  };

  override get componentPath(): string {
    return FormMigrationsComponent.LANG_CONFIG.componentPath;
  }

  override get tagName(): string {
    return FormMigrationsComponent.LANG_CONFIG.componentName;
  }

  private _busy = true;
  private _ready = false;
  private _masterId: string = null;
  private _rootDocuments: TextLongValue[];
  private _migrationFields: FormPublishMigrationColumnDTO[];
  private _fabOptionsAvailable: string[] = [FormMigrationsAction.ADD_FORM_MIGRATION, CommonAction.BACK];
  private _fabOptions: DropdownMenuItem[] = [
    {
      text: FormMigrationsAction.ADD_FORM_MIGRATION,
      value: FormMigrationsAction.ADD_FORM_MIGRATION,
      iconName: 'add'
    },
    {
      text: CommonAction.BACK,
      value: CommonAction.BACK,
      iconName: 'arrow_back'
    }
  ];

  titleLabel: string;
  editAccess = Session.isAdmin();
  mainForm: FormGroup<any>;

  override ngOnInit(): void {
    super.ngOnInit();
    this.init();
  }

  override langReady(): void {
    for (const item of this.fabButtons) {
      item.text = this.tag(`${item.value}`);
    }
  }

  get rootDocuments(): TextLongValue[] {
    return this._rootDocuments || [];
  }

  get migrationFields(): FormPublishMigrationColumnDTO[] {
    return this._migrationFields || [];
  }

  get busy(): boolean {
    return !this._ready || this._busy;
  }

  get ready(): boolean {
    return this._ready;
  }

  get masterId(): string {
    return this._masterId;
  }

  get fabButtons(): (FabButtonMenuItem | DropdownMenuItem)[] {
    return this._fabOptions;
  }

  get fabOptionsAvailable(): string[] {
    return this._fabOptionsAvailable;
  }

  get fabFloat(): boolean {
    return true;
  }

  get fabShowBack(): boolean {
    return true;
  }

  protected init(): void {
    this.navLang
      .getRouteParam(':masterId', null, false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([masterId]) => {
        this._masterId = masterId;
        this.api
          .get<FormPublishMigrationColumnDataSourceDTO>({
            url: `forms/migrations/data-source/${masterId}`,
            cancelableReq: this.$destroy
          })
          .subscribe((dataSource: FormPublishMigrationColumnDataSourceDTO) => {
            this._migrationFields = dataSource.migrationFields;
            this._rootDocuments = dataSource.rootDocuments;
            this._ready = true;
            this._busy = false;
            this.cdr.detectChanges();
          });
      });
  }

  documentFields(_documentId: number): TextLongValue[] {
    return []; // <-- agregar BD
  }

  onChangeRootDocument(_selected: TextHasValue): void {
    // actualizar BD para método documentFields(id)
  }

  fabButtonAction(_selectedItem: FabButtonMenuItem | DropdownMenuItem): void {
    switch (_selectedItem.value) {
      case FormMigrationsAction.ADD_FORM_MIGRATION:
        break;
      case CommonAction.BACK:
        this.navLang.back();
        break;
      default:
        console.warn(`Action is not supported ${_selectedItem.value}`);
        break;
    }
  }
}
