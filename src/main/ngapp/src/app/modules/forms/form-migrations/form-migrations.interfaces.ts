import type { TextLongValue } from '@/core/utils/text-has-value';

export interface FormPublishMigrationColumnDTO {
  id: number;
  code: string;
  description: string;
  status: number;
  deleted: number;
  lastModifiedDate: Date;
  createdDate: Date;
  createdBy: number;
  lastModifiedBy: number;
  rootFieldColumnName: string;
  rootDocumentId: number;
  rootDocumentVersion: string;
  targetFieldColumnName: string;
  targetDocumentId: number;
  targetDocumentVersion: string;
}

export interface FormPublishMigrationColumnDataSourceDTO {
  rootDocuments: TextLongValue[];
  migrationFields: FormPublishMigrationColumnDTO[];
}
