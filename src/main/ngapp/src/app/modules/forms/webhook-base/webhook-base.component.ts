import { Directive, ElementRef, type OnD<PERSON>roy, type OnInit, inject, viewChild } from '@angular/core';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';

import { Form<PERSON>uilder, FormControl, type FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import type { IChangeCheckboxEventArgs } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import type { MultiSelectComponent } from 'src/app/core/multi-select/multi-select.component';
import { AppService } from 'src/app/core/services/app.service';
import { BnextLoaderActivationService } from 'src/app/core/services/bnext-loader-activation.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';

import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FormUtil } from 'src/app/core/utils/form-util';
import type { IPersistableCodeDescription } from 'src/app/core/utils/interfaces';

import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { stringColors, stringToColour } from '@/core/utils/string-util';
import { PrintFormatVariableTypes } from '@/modules/printing/printing-format-base/printing-format-base.enums';
import type {
  FixedFieldDTO,
  FlexiFieldDTO,
  IAnswerMetadataFieldDTO,
  PrintingFormatFieldCode
} from '@/modules/printing/printing-format-base/printing-format-base.interfaces';
import { PrintingFormatBaseUtils } from '@/modules/printing/printing-format-base/printing-format-base.utils';
import { FixedField, type FixedFieldValues } from '@/modules/printing/printing-format/printing-format.enums';
import type { WysiwygData } from 'src/app/devextreme/wysiwyg/wysiwyg.interfaces';
import { createJSONEditor } from 'vanilla-jsoneditor';
import type { FIELDS_ACTIONS, IChangedElement, WebhookDTO, WebhookDataSourceDTO } from './webhook-base.interfaces';

@Directive()
export abstract class WebhookBaseComponent extends BnextCoreComponent implements OnInit, OnDestroy, FabButton, i18n {
  noticeService = inject(NoticeService);
  api = inject(AppService);

  formBuilder = inject(FormBuilder);

  loader = inject(BnextLoaderActivationService);
  activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.forms',
    componentName: 'webhook-base'
  };

  // Se utiliza mismo lenguaje que `PrintingFormatComponent` //

  private _fixedFieldsData: IPersistableCodeDescription[] = [];
  private _flexiFieldsData: FlexiFieldDTO[] = [];
  private _fixedFieldsValues: (string | number)[] = []; // <-- Guardar los ids en el ENUM de PrintingFormatFixedField
  private _flexiFieldsValues: (string | number)[] = []; // <-- Guardar los nombres de las columnas
  private _ready = false;
  private _flexiFieldsDataStages: (string | number)[] = [];
  private _title = null;
  private _fieldsMap: DataMap<IAnswerMetadataFieldDTO | FlexiFieldDTO> = {};

  loading = false;

  override get customTagName(): string {
    return WebhookBaseComponent.LANG_CONFIG.componentName;
  }
  override get componentPath(): string {
    return WebhookBaseComponent.LANG_CONFIG.componentPath;
  }

  get title(): string {
    return this._title;
  }
  get flexiFieldsDataStages(): (string | number)[] {
    return this._flexiFieldsDataStages;
  }
  get showGenerateCode() {
    return false;
  }
  get controller(): string {
    //return 'forms/webhook';
    //return `forms/webhook/base/${this.moduleName}`;
    return `webhook/base/${this.moduleName}`;
  }
  get fixedFieldsData(): IPersistableCodeDescription[] {
    return this._fixedFieldsData;
  }
  get flexiFieldsData(): FlexiFieldDTO[] {
    return this._flexiFieldsData;
  }
  get ready(): boolean {
    // Se sobre escribe en `edit` para validar el `id`
    return this._ready;
  }

  flexiFieldsDataFilterStage: string = null;
  format: WysiwygData;
  masterId: string = null;
  stageFieldObjectId: number = null;
  stageFieldCode: string = null;
  fieldsKey = 'code';
  showHelp = false;
  busy = true;
  mainForm: FormGroup<{
    code: FormControl<string>;
    description: FormControl<string>;
    details: FormControl<string>;
    fixedFields: FormControl<(string | number)[]>;
    flexiFields: FormControl<(string | number)[]>;
    generateCode: FormControl<boolean>;
    stage: FormControl<string>;
    stageDescription: FormControl<string>;
    masterId: FormControl<string>;
    url: FormControl<string>;
    headers: FormControl<string>;
  }>;

  // FabButton
  fabFloat = true;
  fabOptionsAvailable?: string[]; // <-- [OPCIONAL], Oculta opciones del arreglo `fabButtons`
  fabButtons: (FabButtonMenuItem | DropdownMenuItem)[] = [
    {
      iconName: 'save',
      text: 'root.common.button.save',
      value: CommonAction.SAVE
    },
    {
      iconName: 'arrow_back',
      text: 'root.common.button.back',
      value: CommonAction.BACK
    }
  ];

  textAreaValue = '';

  readonly formatRef = viewChild('formatRef', { read: ElementRef });

  readonly gridFlexiFields = viewChild<MultiSelectComponent<any>>('flexiFields');

  readonly gridFixedFields = viewChild<MultiSelectComponent<any>>('fixedFields');

  readonly jsoneditor = viewChild('jsoneditor', { read: ElementRef });

  editor;

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.setupEditor();
  }

  protected setupEditor(): void {
    if (this.editor) {
      return;
    }
    const jsoneditor = this.jsoneditor();
    if (!jsoneditor?.nativeElement) {
      console.error('JSON Editor element not found');
      return;
    }
    try {
      // JSON EDITOR
      const initialJsonTemplate: any = {
        data: {}
      };
      let content = {
        text: undefined,
        json: initialJsonTemplate
      };
      this.editor = createJSONEditor({
        target: jsoneditor?.nativeElement,
        props: {
          content,
          onChange: (updatedContent, previousContent, { contentErrors, patchResult }) => {
            // content is an object { json: JSONData } | { text: string }
            console.log('onChange', { updatedContent, previousContent, contentErrors, patchResult });
            content = updatedContent;
          },
          options: {
            modes: ['code', 'tree'],
            mode: 'code'
          }
        }
      });
    } catch (error) {
      console.error('Error creating JSON Editor', error);
    }
  }

  abstract get moduleName(): string;

  abstract get htmlId(): number;

  abstract fillLoad(entity: WebhookDTO): PrintingFormatFieldCode;

  abstract openCreate(): void;

  abstract openList(): void;

  protected init(): void {
    // Route
    this.navLang
      .getRouteParams(':masterId/:fieldObjectId', ['masterId', 'fieldObjectId'], false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: ([masterId, fieldObjectId]) => {
          this.masterId = masterId;
          this.stageFieldObjectId = +fieldObjectId;
          this.mainForm.patchValue({ masterId: this.masterId });
          this.load();
        },
        error: (error) => {
          console.log(error);
          this.busy = false;
        }
      });
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.init();
  }

  protected load(id?: number): void {
    let dataSourceController = `${this.controller}/data-source/${encodeURIComponent(this.masterId)}`;
    if (id) {
      dataSourceController = `${this.controller}/data-source/${encodeURIComponent(this.masterId)}/${id}`;
    }
    this.api
      .get({ url: dataSourceController, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (dataSource: WebhookDataSourceDTO) => {
          this._title = dataSource.title;
          this._flexiFieldsData = dataSource.flexiFields.sort((a, b) => a.order - b.order);
          this.fillStages(dataSource.flexiFields);
          this.fillFieldsMap(dataSource.flexiFields);
          if (id) {
            // Look for the current data and overwrite
            // TODO: Handle the assigment when form is updated (back-end)
            const section = dataSource.sectionFields.filter(
              (section) => section.fieldObjectId === this.stageFieldObjectId || section.code === dataSource.load.stageFieldCode
            );
            if (section.length === 1) {
              dataSource.load.stageFieldObjectId = section[0].fieldObjectId;
              dataSource.load.stageFieldCode = section[0].code;
              dataSource.load.stage = section[0].fieldStage;
              dataSource.load.stageDescription = section[0].description.replace(/(?:<img[^>]*>\s*)+/g, ' ').substring(0, 50);
            }
            this.fillLoadFields(this.fillLoad(dataSource.load));
          } else {
            const section = dataSource.sectionFields.filter((section) => section.fieldObjectId === this.stageFieldObjectId)[0];
            this.stageFieldCode = section.code;
            const sectionDescription = section.description.replace(/(?:<img[^>]*>\s*)+/g, ' ').substring(0, 50);
            this.mainForm.patchValue({ stage: section.fieldStage, stageDescription: sectionDescription });
          }
          this.busy = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.log(error);
          this.busy = false;
        }
      });
  }

  private fillStages(flexiFields: FlexiFieldDTO[]): void {
    this._flexiFieldsDataStages = Array.from(new Set(flexiFields.map((field) => field.fieldStage)));
    let colors;
    for (const field of flexiFields) {
      colors = stringColors(field.fieldStage);
      field.bgColor = colors.bgColor;
      field.color = colors.color;
    }
  }

  private fillFieldsMap(fields: (IAnswerMetadataFieldDTO | FlexiFieldDTO)[]): void {
    for (const field of fields) {
      this._fieldsMap[field[this.fieldsKey]] = field;
    }
  }
  private fieldSurveyAnswerMetadataId(fieldCode: string | number): number {
    return this._fieldsMap[fieldCode]?.id || null;
  }
  private fieldCode(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.code || `[CODE: ${fieldCode}]`;
  }
  private fieldDescription(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.description || `[DESC: ${fieldCode}]`;
  }
  private fieldBgColor(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.bgColor || null;
  }
  private fieldColor(fieldCode: string | number): string {
    return this._fieldsMap[fieldCode]?.color || null;
  }

  override langReady(): void {
    // form
    this.mainForm = this.formBuilder.group({
      code: new FormControl(),
      description: new FormControl(null, Validators.required),
      details: new FormControl(null, Validators.required),
      fixedFields: new FormControl(null, Validators.required),
      generateCode: new FormControl(true),
      flexiFields: new FormControl(null, Validators.required),
      stage: new FormControl(null, Validators.required),
      stageDescription: new FormControl(null, Validators.required),
      masterId: new FormControl(null, Validators.required),
      url: new FormControl(null, Validators.required),
      headers: new FormControl()
    });
    // buttons
    for (const item of this.fabButtons) {
      if (item.text.startsWith('root.')) {
        item.text = this.tag(item.text);
      } else {
        item.text = this.tag(`fixedFields.${item.text}`);
      }
    }
    // campos fijos
    let colors;
    let description;
    let enumValue;
    this._fixedFieldsData = Object.keys(FixedField).map((enumName) => {
      description = this.tag(`fixedFields.${enumName}`);
      colors = stringColors(description);
      enumValue = +FixedField[enumName];
      return {
        id: enumValue,
        code: PrintingFormatBaseUtils.getFixedFieldAlias(enumValue),
        bgColor: colors.bgColor,
        color: colors.color,
        description: description
      };
    });
    this.fillFieldsMap(this._fixedFieldsData);
    // wysiwyg
    this.format = {
      domNode: () => this.formatRef(),
      dialogName: this.format?.dialogName || this.tag('title')
    };
    // ready
    this.busy = false;
    this._ready = true;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  stageColor(stage: string | number): string {
    return stringToColour(stage as string);
  }

  filterByStage(newStage?: string | number): void {
    if (typeof newStage === 'string') {
      this.flexiFieldsDataFilterStage = newStage;
    } else {
      this.flexiFieldsDataFilterStage = null;
    }
  }

  fabButtonAction(item: FabButtonMenuItem | DropdownMenuItem): void {
    switch (item.value) {
      case CommonAction.SAVE:
        this.save();
        break;
      case CommonAction.BACK:
        this.return();
        break;
      default:
        console.warn(`Action is not supported ${item.value}`);
        break;
    }
  }

  protected save(): void {
    const isFormValid = FormUtil.isValid(this.mainForm).status;
    if (!isFormValid) {
      const msg = this.tag('root.common.message.required-notice');
      this.noticeService.notice(msg);
      this.cdr.detectChanges();
      return;
    }
    const entity: WebhookDTO = this.getEntityData();
    this.busy = true;
    this.loader.show();
    this.api.post({ url: `${this.controller}/save`, cancelableReq: this.$destroy, postBody: entity, handleFailure: false }).subscribe({
      next: (result) => {
        if (this.debug) {
          console.log('>> save, result: ', result);
        }
        this.busy = false;
        this.loader.hide();
        this.dialogService.info(this.tag('save-success'), 'root.common.button.control', 'root.common.button.create', 'root.common.error.messageSystem').then(
          () => this.openList(),
          () => this.openCreate()
        );
        this.menuService.refreshLoggedUser();
        this.cdr.detectChanges();
        this.loader.hide();
      },
      error: (error) => {
        this.busy = false;
        this.loader.hide();
        ErrorHandling.notifyError(error, this.navLang);
        this.cdr.detectChanges();
      }
    });
  }

  private getParsedFixedFields(fullDto = false): FixedFieldDTO[] {
    if (!this._fixedFieldsValues || this._fixedFieldsValues.length === 0) {
      return null;
    }
    let enumValue: FixedFieldValues;
    const fixedFields = this._fixedFieldsValues.map((code) => {
      enumValue = this.fieldSurveyAnswerMetadataId(code) as FixedFieldValues;
      if (fullDto) {
        return {
          id: enumValue,
          code: code as string,
          description: this.fieldDescription(code) as string,
          bgColor: this.fieldBgColor(code) as string,
          color: this.fieldColor(code) as string,
          surveyAnswerMetadataId: null,
          enumValue: enumValue
        };
      }
      return {
        id: this.fieldSurveyAnswerMetadataId(code),
        code: code as string,
        enumValue: enumValue
      };
    });
    return fixedFields;
  }

  private getParsedSurveyFields(fullDto = false): FlexiFieldDTO[] {
    if (!this._flexiFieldsValues || this._flexiFieldsValues.length === 0) {
      return null;
    }
    const flexiFields = this._flexiFieldsValues.map((value) => {
      if (fullDto) {
        return {
          id: this.fieldSurveyAnswerMetadataId(value), // <--- surveyAnswerMetadataId
          code: this.fieldCode(value),
          description: this.fieldDescription(value),
          bgColor: this.fieldBgColor(value),
          color: this.fieldColor(value),
          surveyAnswerMetadataId: this.fieldSurveyAnswerMetadataId(value),
          enumValue: null
        };
      }
      return {
        id: this.fieldSurveyAnswerMetadataId(value), // <--- surveyAnswerMetadataId
        code: this.fieldCode(value),
        surveyAnswerMetadataId: this.fieldSurveyAnswerMetadataId(value)
      };
    });
    return flexiFields;
  }

  protected refreshVariables(element: IChangedElement): void {
    const fixedFields = this.getParsedFixedFields(true);
    const flexiFields = this.getParsedSurveyFields(true);
    const variables = [];
    if (fixedFields) {
      variables.push(
        ...fixedFields.map((field) => ({
          bgColor: field.bgColor || null,
          color: field.color || null,
          text: field.description || field.code,
          type: PrintFormatVariableTypes.FIXED,
          value: field.code
        }))
      );
    }
    if (flexiFields) {
      variables.push(
        ...flexiFields.map((field) => ({
          bgColor: field.bgColor || null,
          color: field.color || null,
          text: field.description || field.code,
          type: PrintFormatVariableTypes.FLEXI,
          value: field.code
        }))
      );
    }
    if (!this.loading) {
      this.addProperties(element?.action, element?.element);
    }
    this.textAreaValue = variables.map((a) => `${a.value}: ${a.text}`).join('\n');
  }
  public getActionType(current_array: (string | number)[], new_array: (string | number)[]): IChangedElement {
    if (current_array.length > new_array.length) {
      const difference = current_array.filter((element) => !new_array.includes(element))[0];
      return { action: 'REMOVE', element: difference };
    }
    const difference = new_array.filter((element) => !current_array.includes(element))[0];
    if (!difference) {
      return { action: 'UPDATE', element: null };
    }
    return { action: 'ADD', element: difference };
  }
  public onChangeSurveyFieldsValues(value: (string | number)[]): void {
    const element = this.getActionType(this._flexiFieldsValues, value);
    this._flexiFieldsValues = value;
    this.refreshVariables(element);
    this.cdr.detectChanges();
  }
  public onChangeFixedFieldsValues(value: (string | number)[]): void {
    const element = this.getActionType(this._fixedFieldsValues, value);
    this._fixedFieldsValues = value;
    this.refreshVariables(element);
    this.cdr.detectChanges();
  }
  protected fillLoadFields(fieldCodes: PrintingFormatFieldCode): void {
    if (!fieldCodes) {
      return;
    }
    this._flexiFieldsValues = fieldCodes.flexiFields;
    this._fixedFieldsValues = fieldCodes.fixedFields;
    this.refreshVariables(null);
  }
  protected getEntityData(): WebhookDTO {
    const data = this.mainForm.value;
    const entity: WebhookDTO = {
      id: -1,
      code: data.code,
      description: data.description,
      fileId: this.htmlId,
      json: JSON.stringify(this.getJsonFromEditor()),
      masterId: this.masterId,
      details: data.details,
      stage: data.stage,
      stageDescription: data.stageDescription,
      stageFieldObjectId: this.stageFieldObjectId,
      stageFieldCode: this.stageFieldCode,
      url: data.url,
      headers: data.headers,
      fixedFields: this.getParsedFixedFields() || [],
      flexiFields: this.getParsedSurveyFields() || []
    };
    return entity;
  }

  changeGenerateCode(event: IChangeCheckboxEventArgs): void {
    if (event.checked) {
      this.mainForm.controls.code.disable();
    } else {
      this.mainForm.controls.code.enable();
    }
  }

  return(): void {
    this.navLang.back();
  }

  async addProperties(action: FIELDS_ACTIONS, id: string | number) {
    let currentJson = this.editor.get();
    if (!currentJson.json) {
      currentJson = {
        text: undefined,
        json: JSON.parse(currentJson.text)
      };
    }
    if (action === 'ADD') {
      currentJson.json.data[id] = '${'.concat(id as string).concat('}');
      await this.editor.set(currentJson);
    } else if (action === 'UPDATE') {
      await this.editor.set(currentJson);
    } else if (action === 'REMOVE') {
      this.clearProps(currentJson, id);
      await this.editor.set(currentJson);
    } else {
      await this.editor.set(currentJson);
    }
  }

  getJsonFromEditor() {
    let currentJson = this.editor.get();
    if (!currentJson.json) {
      currentJson = {
        text: undefined,
        json: JSON.parse(currentJson.text)
      };
    }
    return currentJson.json;
  }

  async updateJson(json) {
    let currentJson = this.editor.get();
    if (!currentJson.json) {
      currentJson = {
        text: undefined,
        json: json
      };
    } else {
      currentJson.json = json;
    }
    await this.editor.set(currentJson);
  }

  clearProps(obj, keys) {
    if (keys === null || typeof keys === 'undefined') {
      return;
    }
    if (Array.isArray(obj)) {
      for (const item of obj) {
        this.clearProps(item, keys);
      }
    } else if (typeof obj === 'object' && obj != null) {
      for (const key of Object.getOwnPropertyNames(obj)) {
        if (keys.indexOf(key) !== -1) {
          obj[key] = '';
        } else {
          this.clearProps(obj[key], keys);
        }
      }
    }
  }
}
