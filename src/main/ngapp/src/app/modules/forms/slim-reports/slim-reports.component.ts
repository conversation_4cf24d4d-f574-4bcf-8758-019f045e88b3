import { GridComponent } from '@/core/grid/grid.component';
import * as GridUtil from '@/core/grid/utils/grid-util';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import type { MenuService } from '@/modules/menu/services/menu.service';
import { DatePipe } from '@angular/common';
import { Component, type OnInit, ViewChild, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import type { RowType } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { GridColumn } from 'src/app/core/grid/utils/grid-column';
import { type ActionStrip, LinkColumn, TextColumn } from 'src/app/core/grid/utils/grid.interfaces';
import { Session } from 'src/app/core/local-storage/session';
import { AppService } from 'src/app/core/services/app.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import { Deferred } from 'src/app/core/utils/deferred';
import { CommonAction } from 'src/app/core/utils/enums';
import { FavoriteTaskType, Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import type { FavoriteTaskSaveDTO, QuickAccessHolder } from '../../menu/menu-main/menu-main.interfaces';
import { type IReportsListComponentTemplate, LANG_CONFIG } from '../reports-list/reports-list.interfaces';
import type { IDeletedResponse } from '../slim-report-base/slim-report-base.interfaces';
import { SlimReportsAction } from './slim-reports.enums';

@Component({
  selector: 'app-slim-reports',
  styleUrls: ['slim-reports.component.scss'],
  templateUrl: './slim-reports.component.html',
  imports: [GridComponent]
})
export class SlimReportsComponent extends BnextCoreComponent implements OnInit, IReportsListComponentTemplate, FabButton, i18n {
  datePipe = inject(DatePipe);

  activatedRoute = inject(ActivatedRoute);
  api = inject(AppService);
  noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.forms',
    componentName: 'slim-reports'
  };

  override get componentPath(): string {
    return 'modules.forms';
  }

  override get tagName(): string {
    return 'slim-reports';
  }

  columns: GridColumn[];
  titleLabel: string;
  editAccess =
    Session.hasService(ProfileServices.IS_ADMIN) ||
    (Session.hasService(ProfileServices.USUARIO_CORPORATIVO) && Session.hasService(ProfileServices.FORM_ADMON_REPORT_ACCESS));

  // TODO: Skipped for migration because:
  //  Class of this query is referenced in the signature of another class.
  @ViewChild('grid')
  grid: GridComponent;

  get ready(): boolean {
    return !!this.url;
  }

  private _masterId: string = null;
  private _fabOptionsAvailable: string[] = [CommonAction.ADD, CommonAction.BACK];
  private _fabOptions: DropdownMenuItem[] = [
    {
      text: SlimReportsAction.ADD_SLIM_REPORT,
      value: SlimReportsAction.ADD_SLIM_REPORT,
      iconName: 'add'
    },
    {
      text: SlimReportsAction.BACK_SLIM_REPORT,
      value: SlimReportsAction.BACK_SLIM_REPORT,
      iconName: 'arrow_back'
    }
  ];

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.slimReportsOptions(row?.data) : []),
      rowIdentifierKey: 'entity_code'
    }
  };

  url = null; // <-- Se inicializa en `init()`
  LangConfig = SlimReportsComponent.LANG_CONFIG;

  get fabButtons(): (FabButtonMenuItem | DropdownMenuItem)[] {
    return this._fabOptions;
  }
  get fabOptionsAvailable(): string[] {
    return this._fabOptionsAvailable;
  }
  get fabFloat(): boolean {
    return true;
  }
  get fabShowBack(): boolean {
    return true;
  }
  get masterId(): string {
    return this._masterId;
  }

  public restrictRecordsByDepartment = 1;
  public validateAccessFormDepartment = 1;

  override ngOnInit(): void {
    super.ngOnInit();
    this.init();
  }

  override langReady(): void {
    for (const item of this.fabButtons) {
      item.text = this.tag(`${item.value}`);
    }
  }

  protected init(): void {
    this.navLang
      .getRouteParam(':masterId', null, false, this.activatedRoute)
      .pipe(takeUntil(this.$destroy))
      .subscribe(([masterId]) => {
        this.activatedRoute.queryParams.subscribe((params) => {
          const { restrictRecordsByDepartment, validateAccessFormDepartment } = params;
          this.restrictRecordsByDepartment = restrictRecordsByDepartment;
          this.validateAccessFormDepartment = validateAccessFormDepartment;
          if (masterId) {
            this._masterId = masterId;
            this.setColumns({
              hiddenFormName: true
            });
            this.url = `forms/slim-reports/${encodeURIComponent(masterId)}`;
          } else {
            this.setColumns({
              hiddenFormName: false
            });
            this.url = 'forms/slim-reports';
            this._fabOptionsAvailable = []; // <-- Se requiere `masterId` para poder agregar un nuevo reporte
          }
          this.cdr.detectChanges();
        });
      });
  }

  private setColumns(args: { hiddenFormName: boolean }): void {
    this.columns = [];
    GridUtil.columns(this.columns)
      .push(
        'documentDescription',
        new TextColumn({
          width: '450px',
          hidden: args.hiddenFormName
        })
      )
      .push(
        'title',
        new LinkColumn({
          linkHref: `./${this.getLang()}/menu/forms/reports/c/{reportCode}/{documentMasterId}`,
          linkParams: ['reportCode', 'documentMasterId'],
          width: '450px'
        })
      )
      .push(
        'description',
        new TextColumn({
          width: '450px'
        })
      )
      .translate(this.$destroy, this.translateService, this.LangConfig, 'reports.column');
  }

  fabButtonAction(_selectedItem: FabButtonMenuItem | DropdownMenuItem): void {
    switch (_selectedItem.value) {
      case SlimReportsAction.ADD_SLIM_REPORT:
        this.menuService.navigate(
          // eslint-disable-next-line max-len
          `menu/forms/slim-report/${encodeURIComponent(this.masterId)}/add?restrictRecordsByDepartment=${this.restrictRecordsByDepartment}&validateAccessFormDepartment=${this.validateAccessFormDepartment}`,
          Module.FORMULARIE
        );
        break;
      case SlimReportsAction.BACK_SLIM_REPORT:
        this.navLang.back();
        break;
      default:
        console.warn(`Action is not supported ${_selectedItem.value}`);
        break;
    }
  }

  toggleMenuAction(drop: GridDropDownItem): void {
    switch (drop.item.value) {
      case CommonAction.OPEN_DETAIL:
        this.menuService.navigate(`menu/forms/reports/c/${drop.row.reportCode}/${encodeURIComponent(this.masterId)}`, Module.FORMULARIE);
        break;
      case CommonAction.CUSTOM_ACTION:
        this.menuService.navigate(
          // eslint-disable-next-line max-len
          `menu/forms/slim-report/${encodeURIComponent(this.masterId)}/edit/${drop.row.slimReportId}?restrictRecordsByDepartment=${this.restrictRecordsByDepartment}&validateAccessFormDepartment=${this.validateAccessFormDepartment}`,
          Module.FORMULARIE
        );
        break;
      case CommonAction.EDIT:
        this.menuService.navigateLegacy(`v-form-report-access.view?id=${drop.row.nodeId}`, Module.FORMULARIE);
        break;
      case CommonAction.FAVORITE:
        this.addReportToFavorite(drop.row.id, drop.row, this.menuService, drop.row.description);
        break;
      case CommonAction.DELETE:
        this.deleteReport(drop);
        break;
      default:
        break;
    }
  }

  private addReportToFavorite(reportId: number, holder: QuickAccessHolder, menuService: MenuService, description: string): void {
    const saved = new Deferred<FavoriteTaskSaveDTO>();
    menuService.addQuickAccess({
      def: saved,
      icon: 'done',
      urlParams: `menu/forms/reports${reportId}`,
      menuPath: 'menu/forms/reports',
      type: FavoriteTaskType.REPORT,
      infoMessage: this.translate.instantFrom(LANG_CONFIG, 'add-to-favorite'),
      reportId: reportId,
      moduleName: String(Module.FORMULARIE).toLowerCase(),
      taskName: description,
      documentMasterId: this.masterId
    });
    saved.promise.then((result) => {
      if (result.success) {
        holder.isFavorite = 1;
        holder.favoriteTaskId = result.id;
      }
    });
  }

  private slimReportsOptions(row: any): DropdownMenuItem[] {
    return [
      {
        text: 'Abrir',
        value: CommonAction.OPEN_DETAIL,
        iconName: 'open_in_browser',
        modernHref: `forms/reports/c/${row.reportCode}/${encodeURIComponent(this.masterId)}`,
        hidden: false
      },
      {
        text: 'Editar',
        value: CommonAction.CUSTOM_ACTION,
        iconName: 'edit',
        // eslint-disable-next-line max-len
        modernHref: `forms/slim-report/${encodeURIComponent(this.masterId)}/edit/${row.slimReportId}?restrictRecordsByDepartment=${this.restrictRecordsByDepartment}&validateAccessFormDepartment=${this.validateAccessFormDepartment}`,
        hidden: false
      },
      {
        text: this.translateService.instant('root.common.button.add-favorite'),
        value: CommonAction.FAVORITE,
        iconName: 'favorite_border',
        hidden: false
      },
      {
        text: 'Eliminar',
        value: CommonAction.DELETE,
        iconName: 'delete',
        hidden: false
      }
    ];
  }

  deleteReport(_drop: GridDropDownItem): void {
    this.loader.show();
    const reportId = _drop.row.id;
    this.api
      .get({ url: `forms/slim-report/delete/${reportId}`, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (deleted: IDeletedResponse) => {
          if (deleted.deletedReport === 1) {
            this.menuService.refreshMenu();
            this.refreshGrid();
            this.noticeService.notice(this.translate.instant('slim-report-deleted-success'));
          } else {
            this.refreshGrid();
            this.noticeService.notice(this.translate.instant('slim-report-deleted-failed'));
          }
          this.loader.hide();
        }
      });
  }

  protected refreshGrid(): void {
    this.grid.refresh();
  }
}
