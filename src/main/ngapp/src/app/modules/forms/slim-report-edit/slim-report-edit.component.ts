import { Component, type OnDestroy, type OnInit } from '@angular/core';
import type { i18n } from 'src/app/core/bnext-core.component';

import { GridMultiSelectComponent } from '@/core/grid-multi-select/grid-multi-select.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { MultiSelectComponent } from '@/core/multi-select/multi-select.component';
import { TransformedFieldsRulesEditorComponent } from '@/core/transformed-fields-rules-editor/transformed-fields-rules-editor.component';
import * as NumberUtil from '@/core/utils/number-util';
import { AsyncPipe, NgClass } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxButtonGroupComponent,
  IgxCheckboxComponent,
  IgxComboHeaderDirective,
  IgxDividerDirective,
  IgxDragDirective,
  IgxDropDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLinearProgressBarComponent,
  IgxRippleDirective
} from '@infragistics/igniteui-angular';
import { IgxForOfDirective, IgxListComponent, IgxListItemComponent, IgxSuffixDirective, IgxSwitchComponent } from 'igniteui-angular';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import { SlimReportAddComponent } from '../slim-report-add/slim-report-add.component';
import type { FormSlimReportDTO, ISlimReportOrderColumns } from '../slim-report-base/slim-report-base.interfaces';

@Component({
  selector: 'app-slim-report-edit',
  templateUrl: '../slim-report-base/slim-report-base.component.html',
  styleUrls: ['../slim-report-base/slim-report-base.component.scss', '../slim-report-add/slim-report-add.component.scss', './slim-report-edit.component.scss'],
  imports: [
    NgClass, // <-- Debe ir al inicio
    BnextTranslatePipe,
    FormsModule,
    GridMultiSelectComponent,
    IgxBadgeComponent,
    IgxButtonDirective,
    IgxButtonGroupComponent,
    IgxCheckboxComponent,
    IgxComboHeaderDirective,
    IgxDividerDirective,
    IgxDragDirective,
    IgxDropDirective,
    IgxForOfDirective,
    IgxIconComponent,
    IgxInputDirective,
    IgxInputGroupComponent,
    IgxLabelDirective,
    IgxLinearProgressBarComponent,
    IgxListComponent,
    IgxListItemComponent,
    IgxRippleDirective,
    IgxSuffixDirective,
    IgxSwitchComponent,
    MultiSelectComponent,
    ReactiveFormsModule,
    AsyncPipe,
    TransformedFieldsRulesEditorComponent
  ]
})
export class SlimReportEditComponent extends SlimReportAddComponent implements OnInit, OnDestroy, FabButton, i18n {
  /**
   * Utiliza el lenguaje de `SlimReportBaseComponent`
   **/
  id: number = null;

  override get showGenerateCode() {
    return false;
  }
  override get ready(): boolean {
    return typeof this.id === 'number';
  }
  override get saveController(): string {
    return `${this.controller}/update`;
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override langReady(): void {
    super.langReady();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  protected override getEntityData(): FormSlimReportDTO {
    const data = super.getEntityData();
    data.id = this.id;
    return data;
  }

  protected override loadData(): void {
    this.navLang.getRouteParam(':id', null, false, this.activatedRoute).subscribe({
      next: ([id]) => {
        if (!NumberUtil.isInteger(id)) {
          console.error('Invalid slim report id');
          return;
        }
        this.id = +id;
        this.api.get({ url: `${this.controller}/load/${id}`, cancelableReq: this.$destroy }).subscribe({
          next: (result) => {
            result.generateCode = false;
            this.busy = false;
            let excelIdFieldValues = [];
            if (result.excelIdFields) {
              excelIdFieldValues = result.excelIdFields.split(',');
            }
            this.mainForm.patchValue({
              generateCode: result.generateCode,
              code: result.code,
              description: result.description,
              details: result.details,
              fixedFieldsValues: result.fixedFields,
              fixedFiltersValues: result.fixedFilters,
              restrictRecordsByDepartment: result.restrictRecordsByDepartment,
              whereFillerUserParticipate: result.whereFillerUserParticipate,
              localTimeForDates: result.localTimeForDates,
              excelUploadEnabled: result.excelUploadEnabled,
              excelIdFieldValues: excelIdFieldValues,
              surveyFieldsValues: result.surveyFields,
              surveyFiltersValues: result.surveyFilters
            });
            if (result.ghostFields) {
              for (const ghostField of result.ghostFields) {
                const item = this.addGhostField(`ghostField${this.ghostFieldsValues().length}`);
                Object.assign(item, ghostField);
                this.getGhostFormControl(item.name).setValue(item.description, {
                  // emitEvent: false,
                  onlySelf: true
                });
                this.onChangeGhostDescAction(item.description, this.ghostFieldsValues().length - 1, true);
              }
            }
            this.addTransformedFields(result.transformedFields);
            this.cdr.detectChanges();
            this.updateFixedFields(result.fixedFields);
            this.updateFiltersFields(result.surveyFields);
            this.loadBusinessUnitRestrict(+id);
            this.loadBusinessUnitDepartmentsRestrict(+id);
            this.loadUsersRestrict(+id);
            this.loadProcessRestrict(+id);
            const slimReportOrderColumns: ISlimReportOrderColumns[] = result.orderColumns;
            super.fillSlimReportGridOrder(slimReportOrderColumns);
          },
          error: (error) => {
            console.log(error);
            this.busy = false;
            this.cdr.detectChanges();
          }
        });
      },
      error: () => {
        throw new Error('Not defined  Id');
      }
    });
  }

  private loadBusinessUnitRestrict(slimReportId: number) {
    this.api.get({ url: `${this.controller}/business-unit-access/list/${slimReportId}`, cancelableReq: this.$destroy }).subscribe((response) => {
      this.businessUnitAccess().value = response;
    });
  }

  private loadBusinessUnitDepartmentsRestrict(slimReportId: number) {
    this.api.get({ url: `${this.controller}/business-unit-department-access/list/${slimReportId}`, cancelableReq: this.$destroy }).subscribe((response) => {
      this.businessUnitDepartmentAccess().value = response;
    });
  }

  private loadUsersRestrict(slimReportId: number) {
    this.api.get({ url: `${this.controller}/user-access/list/${slimReportId}`, cancelableReq: this.$destroy }).subscribe((response) => {
      this.userAccess().value = response;
    });
  }

  private loadProcessRestrict(slimReportId: number) {
    this.api.get({ url: `${this.controller}/process-access/list/${slimReportId}`, cancelableReq: this.$destroy }).subscribe((response) => {
      this.processAccess().value = response;
    });
  }
}
