import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import type { IFormPendingConfig } from '@/modules/forms/pending-info/pending-info.interfaces';
import { FormConfigFieldType, type IForm } from '@/modules/forms/utils/form.interfaces';
import { type AfterViewInit, Component, type OnDestroy, type OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, type UntypedFormGroup } from '@angular/forms';
import type { SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { IgxLinearProgressBarComponent, IgxSwitchComponent } from '@infragistics/igniteui-angular';
import { forkJoin, takeUntil } from 'rxjs';
import { filter } from 'rxjs/operators';
import type { FabButton } from 'src/app/core/bnext-core.interfaces';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from 'src/app/core/fab-button-menu/fab-button-menu.interface';
import { AppService } from 'src/app/core/services/app.service';
import { NoticeService } from 'src/app/core/services/notice.service';

import * as NumberUtil from '@/core/utils/number-util';
import { CommonAction } from 'src/app/core/utils/enums';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { FormUtil } from 'src/app/core/utils/form-util';

// @routing: qms/es/menu/forms/pending-info/:id
@Component({
  selector: 'app-pending-info',
  styleUrls: ['./pending-info.component.scss'],
  templateUrl: './pending-info.component.html',
  imports: [FormsModule, ReactiveFormsModule, IgxSwitchComponent, BnextTranslatePipe, IgxLinearProgressBarComponent]
})
export class PendingInfoComponent extends BnextCoreComponent implements OnInit, AfterViewInit, OnDestroy, i18n, FabButton {
  private noticeService = inject(NoticeService);
  private api = inject(AppService);
  private formBuilder = inject(UntypedFormBuilder);
  private activatedRoute = inject(ActivatedRoute);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.forms',
    componentName: 'pending-info'
  };

  LangConfig = PendingInfoComponent.LANG_CONFIG;

  public get customTagName(): string {
    return PendingInfoComponent.LANG_CONFIG.componentName;
  }
  override get componentPath(): string {
    return PendingInfoComponent.LANG_CONFIG.componentPath;
  }

  controller = 'forms';

  id: number;
  formData: IForm;
  seccionsFields: IFormPendingConfig[];
  configurableFields: IFormPendingConfig[];
  updated: IFormPendingConfig[] = [];
  busy = true;
  touched = false;
  mainForm: UntypedFormGroup;

  protected readonly filter = filter;

  // FabButton
  fabFloat = true;
  fabOptionsAvailable?: string[]; // <-- [OPCIONAL], Oculta opciones del arreglo 'fabButtons'
  fabButtons: (FabButtonMenuItem | DropdownMenuItem)[] = [
    {
      iconName: 'save',
      text: 'root.common.button.save',
      value: CommonAction.SAVE
    },
    {
      iconName: 'arrow_back',
      text: 'root.common.button.back',
      value: CommonAction.BACK
    }
  ];

  override ngOnInit(): void {
    super.ngOnInit();
    this.mainForm = this.formBuilder.group({
      code: new UntypedFormControl(),
      description: new UntypedFormControl()
    });
    this.translateService
      .getTuples(this.fabButtons.map((i) => i.text))
      .pipe(takeUntil(this.$destroy))
      .subscribe((labels: string[]) => {
        for (let i = 0; i < labels.length; i++) {
          const item = this.fabButtons[i];
          item.text = labels[i];
        }
      });
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.navLang.getRouteParam('pending-info/:id', null, false, this.activatedRoute).subscribe({
      next: ([id]) => {
        if (!NumberUtil.isInteger(id)) {
          console.error('Can not load data for pending-info, invalid id', id);
          return;
        }
        this.id = +id;

        const fieldsUrl = `${this.controller}/pending-info-fields/${id}`;
        const fields = this.api.get({
          url: fieldsUrl,
          cancelableReq: this.$destroy
        });

        const loadUrl = `${this.controller}/${id}`;
        const load = this.api.get({
          url: loadUrl,
          cancelableReq: this.$destroy
        });

        forkJoin<[IFormPendingConfig[], IForm]>([fields, load])
          .pipe(takeUntil(this.$destroy))
          .subscribe({
            next: ([values, result]) => this.onLoadedFields(result, values),
            error: (error) => {
              console.log('Error loading pending info', error);
              this.busy = false;
              this.cdr.detectChanges();
            }
          });
      },
      error: (e) => {
        this.busy = false;
        throw new Error(`Not defined  Id ${e}`);
      }
    });
  }

  private onLoadedFields(result: IForm, fields: IFormPendingConfig[]): void {
    this.formData = result;
    const configurableFields = fields.filter((i: IFormPendingConfig) => i.fieldType === FormConfigFieldType.FIELD);
    this.configurableFields = configurableFields;
    const sectionIds = new Set(configurableFields.map((i) => i.seccionId));
    this.seccionsFields = fields.filter((i: IFormPendingConfig) => i.fieldType === FormConfigFieldType.SECCION && sectionIds.has(i.seccionId));
    this.cdr.detectChanges();
    this.mainForm.patchValue(result);
    this.busy = false;
    this.cdr.detectChanges();
  }

  override langReady(): void {
    super.langReady();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  fabButtonAction(item: FabButtonMenuItem | DropdownMenuItem): void {
    switch (item.value) {
      case CommonAction.SAVE:
        this.save();
        break;
      case CommonAction.BACK:
        this.navLang.back();
        break;
    }
  }

  protected save(): void {
    if (!FormUtil.isValid(this.mainForm).status) {
      this.touched = true;
      this.noticeService.notice(this.tag('requiredNotice'));
      this.cdr.detectChanges();
      return;
    }
    this.updated = this.seccionsFields.concat(this.configurableFields).filter((i) => i.updated);
    if (this.updated.length === 0) {
      return;
    }
    this.busy = true;
    this.loader.show();
    const url = `${this.controller}/pending-info/save/${this.id}`;
    this.api
      .post({
        url: url,
        cancelableReq: this.$destroy,
        postBody: this.updated,
        handleFailure: false
      })
      .subscribe({
        next: (result) => {
          if (this.debug()) {
            console.log('>> save, result: ', result);
          }
          this.loader.hide();
        },
        error: (error) => {
          this.busy = false;
          this.loader.hide();
          ErrorHandling.notifyError(error, this.navLang);
          this.cdr.detectChanges();
        },
        complete: () => {
          this.busy = false;
          this.loader.hide();
          this.noticeService.notice(this.tag('save-success'));
          this.menuService.navigate(`menu/forms/pending-info/${this.id}`);
        }
      });
  }

  invalid(fieldName: string): boolean {
    return FormUtil.isFieldValid(fieldName, this.mainForm, this.touched);
  }

  public onChangeShowInChips(event: boolean, item: IFormPendingConfig): void {
    for (const element of this.configurableFields) {
      if (element.name === item.name) {
        element.showInChips = event ? 1 : 0;
        element.updated = true;
      }
    }
  }

  public onChangeShowInDetails(event: boolean, item: IFormPendingConfig): void {
    for (const element of this.configurableFields) {
      if (element.name === item.name) {
        element.showInDetails = event ? 1 : 0;
        element.updated = true;
      }
    }
  }

  getQuestionTitle(title: string): SafeHtml {
    return `${this.tag('question')}: ${title !== '' ? title : this.tag('undefined')}`;
  }
}
