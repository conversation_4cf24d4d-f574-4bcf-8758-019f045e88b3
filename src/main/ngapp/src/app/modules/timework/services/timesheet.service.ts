import { Deferred } from '@/core/utils/deferred';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import type { TimesheetRegistry } from '../../timesheet/timesheet-add/timesheet-add.interfaces';
import type { TimesheetDataSourceDto } from '../../timesheet/timesheet-add/timesheet-datasource';
import type {
  CaptureTimeEvent,
  ChangePlannerEvent,
  SetupTimesheetData,
  TimesheetDeleteEvent,
  TimesheetEditEvent
} from '../../timesheet/timesheet-dialog/timesheet-dialog.interfaces';
import type { PlannedType } from '../../timesheet/timesheet-widget/timesheet-widget.enums';
import type { TimesheetActivityPlanned, TimesheetDto, TimesheetServiceDto, UpdateUIStopwatch } from '../../timesheet/timesheet-widget/timesheet-widget.interfaces';
@Injectable()
export class TimesheetService {
  private _timesheetAdd = new Subject<TimesheetServiceDto>();
  public timesheetAdd = this._timesheetAdd.asObservable();

  private _timesheetPush = new Subject<TimesheetDto>();
  public timesheetPush = this._timesheetPush.asObservable();

  private _timesheetStartStopwatch = new Subject<TimesheetServiceDto>();
  public timesheetStartStopwatch = this._timesheetStartStopwatch.asObservable();

  private _timesheetStartStopwatchPush = new Subject<TimesheetDto>();
  public timesheetStartStopwatchPush = this._timesheetStartStopwatchPush.asObservable();

  private _hasStopwatchrunning = new Subject<TimesheetDto>();
  public hasStopwatchrunning = this._hasStopwatchrunning.asObservable();

  private _updateUIStopwatch = new Subject<UpdateUIStopwatch>();
  public updateUIStopwatch = this._updateUIStopwatch.asObservable();

  private _updateStopwatchRegistryEmpty = new Subject<TimesheetDto>();
  public updateStopwatchRegistryEmpty = this._updateStopwatchRegistryEmpty.asObservable();

  private _stopStopwatchFromPendingList = new Subject<boolean>();
  public stopStopwatchFromPendingList = this._stopStopwatchFromPendingList.asObservable();

  private _replaceItem = new Subject<TimesheetDto>();
  public replaceItemObs = this._replaceItem.asObservable();

  private _refreshDialogDataSourceCatalogs = new Subject<boolean>();
  public refreshDialogDataSourceCatalogs = this._refreshDialogDataSourceCatalogs.asObservable();

  private _onClosedDialog = new Subject<boolean>();
  public onClosedDialog = this._onClosedDialog.asObservable();

  private _onOpenEdit = new Subject<TimesheetEditEvent>();
  public onOpenEdit = this._onOpenEdit.asObservable();

  private _onDeleteRegistry = new Subject<TimesheetDeleteEvent>();
  public onDeleteRegistry = this._onDeleteRegistry.asObservable();

  private _onChangePlannerTask = new Subject<ChangePlannerEvent>();
  public onChangePlannerTask = this._onChangePlannerTask.asObservable();

  private _onCaptureTimesheet = new Subject<CaptureTimeEvent>();
  public onCaptureTimesheet = this._onCaptureTimesheet.asObservable();

  private _onModified = new Subject<TimesheetDto>();
  public onModified = this._onModified.asObservable();

  private _onSetupFromDataSource = new Subject<SetupTimesheetData>();
  public onSetupFromDataSource = this._onSetupFromDataSource.asObservable();

  private _onRepaintScheduler = new Subject<TimesheetRegistry>();
  public onRepaintScheduler = this._onRepaintScheduler.asObservable();

  private _onTimesheetWidgetDateChange = new Subject<Date>();
  public onTimesheetWidgetDateChange = this._onTimesheetWidgetDateChange.asObservable();

  public openNewTimesheet(comment?: string, tags?: string[], dataActivityPlanned?: TimesheetActivityPlanned, onOpenedWindow?: () => void): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    this._timesheetAdd.next({
      pendingRecordId: dataActivityPlanned.pendingRecordId,
      comment: comment,
      tags: tags,
      dataActivityPlanned: dataActivityPlanned,
      def: def,
      onOpenedWindow: onOpenedWindow
    });
    return def.promise;
  }

  public openNew(
    plannedType: PlannedType,
    pendingRecordId?: number,
    comment?: string,
    tags?: string[],
    date?: Date,
    dataActivityPlanned?: TimesheetActivityPlanned,
    enableStopwatch?: boolean,
    disablePlannedActivityField?: boolean,
    onOpenedWindow?: () => void
  ): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    this._onCaptureTimesheet.next({
      plannedType,
      pendingRecordId,
      comment,
      tags,
      date,
      dataActivityPlanned,
      enableStopwatch,
      disablePlannedActivityField,
      onOpenedWindow,
      def
    });
    return def.promise;
  }

  public startNewStopwatch(comment?: string, tags?: string[], date?: Date, dataActivityPlanned?: TimesheetDto) {
    this._timesheetStartStopwatch.next({
      pendingRecordId: dataActivityPlanned.pendingRecordId,
      comment: comment,
      tags: tags,
      date: date,
      dataActivityPlanned: dataActivityPlanned,
      timesheetId: dataActivityPlanned.timesheetId,
      stopwatchType: dataActivityPlanned.stopwatchType
    });
  }

  public hasStopwatchrunningService(dto: TimesheetDto) {
    this._hasStopwatchrunning.next(dto);
  }

  public updateStopwatchRegistryEmptyService(dto: TimesheetDto): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    dto.def = def;
    this._updateStopwatchRegistryEmpty.next(dto);
    return def.promise;
  }

  public pushItemTimesheet(item: TimesheetDto): void {
    this._timesheetPush.next(item);
  }

  public updateUIStopwatchService(update: boolean, dto?: TimesheetDto, repaintSchedular = true) {
    this._updateUIStopwatch.next({ update: update, dto: dto, repaintScheduler: repaintSchedular });
  }

  public stopStopwatchFromPendingListService(stop: boolean) {
    this._stopStopwatchFromPendingList.next(stop);
  }

  public replaceItem(item: TimesheetDto) {
    this._replaceItem.next(item);
  }

  public refreshDialogDataSourceCatalogsService(): void {
    this._refreshDialogDataSourceCatalogs.next(true);
  }

  public closedDialog(): void {
    this._onClosedDialog.next(true);
  }

  public openEdit(data: TimesheetDto, isOpenedFromTooltip = false): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    this._onOpenEdit.next({ data, isOpenedFromTooltip, def });
    return def.promise;
  }

  public deleteRegistry(timesheetId: number): Promise<boolean> {
    const def = new Deferred<boolean>();
    this._onDeleteRegistry.next({ timesheetId, def });
    return def.promise;
  }

  public setNewChangePlannerTask(modifiedValue: any, activityPlannedId: number): void {
    this._onChangePlannerTask.next({ modifiedValue, activityPlannedId });
  }

  public modified(item: TimesheetDto): void {
    this._onModified.next(item);
  }

  public setupFromDataSource(dataSource: TimesheetDataSourceDto): void {
    this._onSetupFromDataSource.next({ dataSource, hasExternalDataSource: true });
  }

  public repaintScheduler(registry: TimesheetRegistry): void {
    this._onRepaintScheduler.next(registry);
  }

  public timesheetWidgetDateChange(date: Date): void {
    this._onTimesheetWidgetDateChange.next(date);
  }
}
