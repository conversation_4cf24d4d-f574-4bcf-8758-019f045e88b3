import { type ComponentFixture, TestBed } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { TimeworkReportComponent } from './timework-report.component';

describe('TimeworkReportComponent', () => {
  let component: TimeworkReportComponent;
  let fixture: ComponentFixture<TimeworkReportComponent>;

  configureTestSuite();

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, TimeworkReportComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TimeworkReportComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
