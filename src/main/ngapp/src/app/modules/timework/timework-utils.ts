import type { Language } from '@/core/bnext-core.interfaces';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import { Session } from '@/core/local-storage/session';
import { WeeklyDay } from '@/core/periodicity/utils/weekly-day';
import { WeeklyDays } from '@/core/periodicity/utils/weekly-days';
import type { DialogService } from '@/core/services/dialog.service';
import type { NavigateLanguageService } from '@/core/services/navigate-lang.service';
import * as DateUtil from '@/core/utils/date-util';
import { ErrorHandling, type ErrorHandlingInput } from '@/core/utils/error-handling';
import * as NumberUtil from '@/core/utils/number-util';
import { TimeworkAction } from '@/modules/timework/timework-widget/timework-widget.enums';
import type { TimeworkEntity } from '@/modules/timework/timework-widget/timework-widget.interfaces';
import type { CheckMethodResponse, GeolocationMessage, TimeworkDuration, TimeworkJsonEntityData, UserDaysPermission } from '@/modules/timework/timework.interfaces';

export const TIMEWORK_LANG_CONFIG: BnextComponentPath = {
  componentPath: 'modules.timework',
  componentName: 'timework-widget'
};

export function checkTodayAvailable(permission: UserDaysPermission, defaultValue: boolean): boolean {
  if (!permission) {
    return defaultValue;
  }
  switch (permission.today) {
    case 1:
      return permission.sunday > 0;
    case 2:
      return permission.monday > 0;
    case 3:
      return permission.tuesday > 0;
    case 4:
      return permission.wednesday > 0;
    case 5:
      return permission.thursday > 0;
    case 6:
      return permission.friday > 0;
    case 7:
      return permission.saturday > 0;
    default:
      return false;
  }
}

export function getUnavailableDaysByPermission(permission: UserDaysPermission, translate: BnextTranslateService): string {
  const weekDays = WeeklyDays.values.map((c) => c.id);
  const weekDaysAvailableToOrder = [];
  let unavailableDays = '';
  for (const key in permission) {
    if (permission.hasOwnProperty(key) && permission[key] === 0 && weekDays[permission.today - 1].toLocaleLowerCase() !== `weekly${key}`.toLocaleLowerCase()) {
      weekDaysAvailableToOrder.push({ day: key, index: WeeklyDay[`weekly${key.charAt(0).toUpperCase() + key.slice(1)}`] });
    }
  }
  for (const f of weekDaysAvailableToOrder.sort((a, b) => {
    return a.index - b.index;
  })) {
    unavailableDays += `${translate.instantFrom(BnextTranslateService.DATE_COMMON, f.day)}, `;
  }
  unavailableDays = unavailableDays.slice(0, unavailableDays.length - 2);
  return unavailableDays;
}

export function getCheckOutDurationForEntities(entities: TimeworkEntity[]): TimeworkDuration {
  if (!entities || entities.length === 0) {
    console.error('Can not get check out duration, missing entities');
    return null;
  }
  let totalMinutes = 0;
  for (const entity of entities) {
    const duration = DateUtil.diff(entity.localCheckIn, entity.localCheckOut);
    if (!duration) {
      continue;
    }
    totalMinutes += duration.hours * 60 + duration.minutes;
  }
  const hours = Math.floor(totalMinutes / 60);
  const minutes = NumberUtil.round(totalMinutes % 60, 0);
  return { hours, minutes };
}

export function parseTimeworkJsonEntities(timeworkId: number, records: TimeworkJsonEntityData[], language: Language): TimeworkEntity[] {
  if (!records || records.length === 0) {
    console.error('Can not parse time work records');
    return null;
  }
  if (records.length === 1) {
    return [parseTimeworkJsonEntity(timeworkId, records[0], language)];
  }
  return records.map((record) => parseTimeworkJsonEntity(timeworkId, record, language));
}

function parseTimeworkJsonEntity(timeworkId: number, record: TimeworkJsonEntityData, language: Language) {
  const localCheckIn = DateUtil.parseIsoDate(record.localCheckIn, language);
  const localCheckOut = DateUtil.parseIsoDate(record.localCheckOut, language);
  const entity: TimeworkEntity = {
    id: record.id ?? timeworkId,
    checkIn: DateUtil.parseIsoDate(record.checkIn, language),
    checkInTz: record.checkInTz,
    checkOut: DateUtil.parseIsoDate(record.checkOut, language),
    checkOutTz: record.checkOutTz,
    localCheckIn: localCheckIn,
    localCheckInTz: record.localCheckInTz,
    localCheckInText: DateUtil.formatTime(localCheckIn),
    localCheckOut: localCheckOut,
    localCheckOutTz: record.localCheckOutTz,
    localCheckOutText: DateUtil.formatTime(localCheckOut),
    checkInLatitude: record.checkInLatitude,
    checkInLongitude: record.checkInLongitude,
    checkOutLatitude: record.checkOutLatitude,
    checkOutLongitude: record.checkOutLongitude
  };
  const duration = DateUtil.diff(entity.localCheckIn, entity.localCheckOut);
  if (duration) {
    entity.duration = `${duration.hours}h ${duration.minutes}m`;
  } else {
    entity.duration = '';
  }
  return entity;
}

export function parseNewEntity(data: TimeworkEntity): TimeworkEntity {
  let checkIn: Date;
  let checkInTz: string;
  if (Number.isInteger(data.checkIn)) {
    checkIn = DateUtil.parse(data.checkIn);
    checkInTz = data.checkInTz;
  } else if (data.checkIn && data.checkIn instanceof Date) {
    checkIn = data.checkIn;
    checkInTz = data.checkInTz;
  } else {
    checkIn = null;
    checkInTz = null;
  }
  let localCheckIn: Date;
  let localCheckInTz: string;
  let localCheckInText = '';
  if (Number.isInteger(data.localCheckIn)) {
    localCheckIn = DateUtil.parse(data.localCheckIn);
    localCheckInText = DateUtil.formatTime(data.localCheckIn);
    localCheckInTz = data.localCheckInTz;
  } else if (data.localCheckIn && data.localCheckIn instanceof Date) {
    localCheckIn = data.localCheckIn;
    localCheckInText = DateUtil.formatTime(data.localCheckIn);
    localCheckInTz = data.localCheckInTz;
  } else {
    localCheckIn = null;
    localCheckInTz = null;
  }
  let checkOut: Date;
  let checkOutTz: string;
  if (Number.isInteger(data.checkOut)) {
    checkOut = DateUtil.parse(data.checkOut);
    checkOutTz = data.checkOutTz;
  } else if (data.checkOut && data.checkOut instanceof Date) {
    checkOut = data.checkOut;
    checkOutTz = data.checkOutTz;
  } else {
    checkOut = null;
    checkOutTz = null;
  }
  let localCheckOut: Date;
  let localCheckOutTz: string;
  let localCheckOutText = '';
  if (Number.isInteger(data.localCheckOut)) {
    localCheckOut = DateUtil.parse(data.localCheckOut);
    localCheckOutText = DateUtil.formatTime(data.localCheckOut);
    localCheckOutTz = data.localCheckOutTz;
  } else if (data.localCheckOut && data.localCheckOut instanceof Date) {
    localCheckOut = data.localCheckOut;
    localCheckOutTz = data.localCheckOutTz;
    localCheckOutText = DateUtil.formatTime(data.localCheckOut);
  } else {
    localCheckOut = null;
    localCheckOutTz = null;
  }
  let totalDuration = '-';
  if (checkOut !== null) {
    const duration = DateUtil.diff(localCheckIn, localCheckOut);
    const hours = duration.hours;
    const minutes = duration.minutes;
    totalDuration = `${hours}h ${minutes}m`;
  } else {
    totalDuration = '-';
  }
  return {
    id: data.id || -1,
    checkIn: checkIn,
    checkInTz: checkInTz,
    localCheckIn: localCheckIn,
    localCheckInTz: localCheckInTz,
    checkOut: checkOut,
    checkOutTz: checkOutTz,
    localCheckOut: localCheckOut,
    localCheckOutTz: localCheckOutTz,
    duration: totalDuration,
    localCheckInText: localCheckInText,
    localCheckOutText: localCheckOutText,
    checkInLatitude: data.checkInLatitude,
    checkInLongitude: data.checkInLongitude,
    checkOutLatitude: data.checkOutLatitude,
    checkOutLongitude: data.checkOutLongitude
  };
}

export function checkCurrent(id: string | number, items: TimeworkEntity[]): TimeworkEntity {
  let filteredArr: TimeworkEntity[];
  if (items) {
    filteredArr = items.filter((item) => {
      return +item.id === +id;
    });
  } else {
    filteredArr = [];
  }
  if (filteredArr.length === 1) {
    return filteredArr[0];
  }
  if (filteredArr.length > 1) {
    console.warn('Duplicated entries');
    return filteredArr[0];
  }
  console.warn('New entry');
  return null;
}

export function getFailedGeolocationMessage(timeOutError, permissionDenied, translate: BnextTranslateService): GeolocationMessage {
  let message: string;
  if (timeOutError) {
    message = translate.instantFrom(TIMEWORK_LANG_CONFIG, 'timeout-geolocation');
    return { message, isError: true, title: null };
  }
  if (permissionDenied) {
    if (!window.location.href.startsWith('https://') && !window.location.href.startsWith('https://localhost')) {
      // Only secure origins are allowed (see: https://goo.gl/Y0ZkNV).
      const title = translate.instantFrom(TIMEWORK_LANG_CONFIG, 'geolocation-only-https.title', { browser: Session.getBrowser() });
      const message = translate.instantFrom(TIMEWORK_LANG_CONFIG, 'geolocation-only-https.message');
      return { message, isError: false, title };
    }
    if (Session.getBrowser() !== null) {
      const title = translate.instantFrom(BnextTranslateService.MESSAGE_COMMON, 'geolocation-permission-dennied.title');
      const message = translate.instantFrom(BnextTranslateService.MESSAGE_COMMON, `geolocation-permission-dennied.${Session.getBrowser()}`);
      return { message, isError: false, title };
    }
    message = translate.instantFrom(TIMEWORK_LANG_CONFIG, 'failed-geolocation');
    return { message, isError: true, title: null };
  }
  message = translate.instantFrom(TIMEWORK_LANG_CONFIG, 'failed-geolocation');
  return { message, isError: true, title: null };
}

export function showFailedGeolocationMessage(timeOutError: boolean, permissionDenied: boolean, dialogService: DialogService, translate: BnextTranslateService) {
  const message = getFailedGeolocationMessage(timeOutError, permissionDenied, translate);
  if (message.isError) {
    if (message.title) {
      // noinspection JSIgnoredPromiseFromCall
      dialogService.error(message.title, message.message);
    } else {
      // noinspection JSIgnoredPromiseFromCall
      dialogService.error(message.message);
    }
  } else {
    if (message.title) {
      // noinspection JSIgnoredPromiseFromCall
      dialogService.info(message.message, 'root.common.button.ok', null, message.title);
    } else {
      // noinspection JSIgnoredPromiseFromCall
      dialogService.info(message.message, 'root.common.button.ok');
    }
  }
}

export function showErrorGeolocationBlocked(_error: GeolocationPositionError, translate: BnextTranslateService, dialogService: DialogService): void {
  // noinspection JSIgnoredPromiseFromCall
  dialogService.error(translate.instantFrom(TIMEWORK_LANG_CONFIG, 'geolocationBlocked'));
}

export function showErrorGeolocation(error: GeolocationPositionError, translate: BnextTranslateService, dialogService: DialogService): boolean {
  console.error('Missing GEOLOCATION!! try again', error);
  if (error.code === 1) {
    showFailedGeolocationMessage(false, true, dialogService, translate);
    return false;
  }
  if (error.code === 2) {
    showErrorGeolocationBlocked(error, translate, dialogService);
    return false;
  }
  showFailedGeolocationMessage(false, false, dialogService, translate);
  return false;
}

export function showDoCheckActionError(e: string | ErrorHandlingInput, dialogService: DialogService, navLang: NavigateLanguageService, translate: BnextTranslateService) {
  const httpError = e as { status: number; error: string };
  if (
    httpError &&
    httpError.status === 500 &&
    (httpError.error?.includes('ConstraintViolationException') || httpError.error?.includes('UQ_TIMEWORK_CHECK_IN_CONSTRAINT'))
  ) {
    // noinspection JSIgnoredPromiseFromCall
    dialogService.error(translate.instantFrom(TIMEWORK_LANG_CONFIG, 'duplicate-check-message-error'));
  } else {
    // noinspection JSIgnoredPromiseFromCall
    ErrorHandling.notifyError(e, navLang);
  }
}

export function onDoCheckSuccessStatus(gsh: CheckMethodResponse, method: TimeworkAction, language: Language, items: TimeworkEntity[]): TimeworkEntity[] {
  const current = checkCurrent(gsh.savedId, items);
  const isCheckOut = method === TimeworkAction.CHECK_OUT;
  const isCheckIn = method === TimeworkAction.CHECK_IN;
  if (isCheckOut && current) {
    if (gsh.jsonEntityData?.data?.length > 0) {
      return parseTimeworkJsonEntities(gsh.savedId, gsh.jsonEntityData.data, language);
    }
    console.error('Missing CHECKOUT date!! try again');
    return null;
  }
  if (isCheckIn && current) {
    console.error('Duplicated checkin!');
    return null;
  }
  if (isCheckOut) {
    console.error('Missing checkin!');
    return null;
  }
  if (isCheckIn) {
    if (gsh.jsonEntityData?.data?.length > 0) {
      return parseTimeworkJsonEntities(gsh.savedId, gsh.jsonEntityData.data, language);
    }
    console.error('Missing CHECKIN date!! try again');
    return null;
  }
  console.error('Unknown method');
  return null;
}
