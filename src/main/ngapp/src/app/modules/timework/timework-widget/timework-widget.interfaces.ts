import type { TimeworkAction, TimeworkMode } from '@/modules/timework/timework-widget/timework-widget.enums';

export class TimeworkEntity {
  id: string | number;
  checkIn: number | Date;
  checkOut: number | Date;
  checkInTz: string;
  checkOutTz: string;
  localCheckIn: number | Date;
  localCheckOut: number | Date;
  localCheckInTz: string;
  localCheckOutTz: string;
  focused?: boolean;
  duration?: string;
  localCheckInText?: string;
  localCheckOutText?: string;
  checkInLatitude?: number;
  checkInLongitude?: number;
  checkOutLatitude?: number;
  checkOutLongitude?: number;
}

export interface DoCheckActionConfig {
  timeworkMode: TimeworkMode;
  timeworkAction: TimeworkAction;
  items: TimeworkEntity[];
}

export interface DoCheckExternalData {
  documentId: number | null;
  documentMasterId: string | null;
  documentDescription: string | null;
  surveyId: number | null;
  surveyFieldId: number | null;
  outsandingSurveyId: number | null;
  outstandingSurveyCode: string | null;
}

export class TimeworkScheduler {
  day: number | Date;
  startDate: number | Date; // checkIn
  endDate: number | Date; // checkOut
  duration: string;
  minutesWorked: number;
  hoursWorked: number;
}

export class TimeworkTimeByday {
  day: number | Date;
  hours: number;
  minutes: number;
}
