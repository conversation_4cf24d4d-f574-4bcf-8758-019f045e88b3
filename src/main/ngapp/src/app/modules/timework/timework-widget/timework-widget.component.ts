import { BnextCoreComponent } from '@/core/bnext-core.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import * as DateUtil from '@/core/utils/date-util';
import type { ErrorHandlingInput } from '@/core/utils/error-handling';
import type { CheckMethodResponse, DoCheckPayload, UserDaysPermission } from '@/modules/timework/timework.interfaces';
import { DatePipe, NgClass, NgTemplateOutlet } from '@angular/common';
import { type AfterViewInit, Component, type ElementRef, Input, type OnDestroy, type OnInit, inject, input, viewChild } from '@angular/core';
import {
  IgxButtonDirective,
  IgxCardActionsComponent,
  IgxCardComponent,
  IgxCardContentDirective,
  IgxCardHeaderComponent,
  IgxChipComponent,
  IgxDividerDirective,
  IgxIconComponent,
  IgxLinearProgressBarComponent,
  IgxListComponent,
  IgxListItemComponent,
  IgxListLineSubTitleDirective,
  IgxListLineTitleDirective,
  IgxRippleDirective
} from '@infragistics/igniteui-angular';
import { delay, of, takeUntil } from 'rxjs';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { Session } from 'src/app/core/local-storage/session';
import { AppService } from 'src/app/core/services/app.service';

import { ConfigApp } from '@/core/local-storage/config-app';
import {
  TIMEWORK_LANG_CONFIG,
  checkTodayAvailable,
  getCheckOutDurationForEntities,
  getUnavailableDaysByPermission,
  onDoCheckSuccessStatus,
  parseNewEntity,
  showDoCheckActionError,
  showErrorGeolocation,
  showFailedGeolocationMessage
} from '@/modules/timework/timework-utils';
import { ChartTimelineComponent } from '../../../devextreme/chart/chart.component';
import { Module } from '../../menu/menu-definition/menu-definition.enum';
import { MenuWidgetService } from '../../menu/menu-widget/menu-widget.service';
import type { SizeWidget, WidgetConfig } from '../../widgets/widget-panel/widget-panel.interfaces';
import { TimesheetService } from '../services/timesheet.service';
import { TimeworkAction, TimeworkMode } from './timework-widget.enums';
import type { TimeworkEntity, TimeworkScheduler, TimeworkTimeByday } from './timework-widget.interfaces';
import { TimeworkWidgetUtils } from './timework-widget.utils';

@Component({
  selector: 'app-timework-widget',
  templateUrl: './timework-widget.component.html',
  styleUrls: ['./timework-widget.component.scss', './timework-widget-medias.component.scss'],
  imports: [
    NgClass,
    NgTemplateOutlet,
    DatePipe,
    IgxCardComponent,
    IgxCardHeaderComponent,
    IgxButtonDirective,
    IgxRippleDirective,
    IgxIconComponent,
    IgxCardContentDirective,
    IgxDividerDirective,
    IgxCardActionsComponent,
    IgxLinearProgressBarComponent,
    IgxListComponent,
    IgxListItemComponent,
    IgxListLineTitleDirective,
    IgxListLineSubTitleDirective,
    IgxChipComponent,
    ChartTimelineComponent,
    BnextTranslatePipe
  ]
})
export class TimeworkWidgetComponent extends BnextCoreComponent implements OnDestroy, OnInit, AfterViewInit {
  private service = inject(AppService);
  private menuWidgetService = inject(MenuWidgetService);
  timesheetService = inject(TimesheetService);

  public static LANG_CONFIG: BnextComponentPath = TIMEWORK_LANG_CONFIG;

  override get componentPath(): string {
    return TimeworkWidgetComponent.LANG_CONFIG.componentPath;
  }
  public get customTagName(): string {
    return TimeworkWidgetComponent.LANG_CONFIG.componentName;
  }

  readonly timeworkCheckSection = viewChild<ElementRef>('timeworkCheckSection');
  readonly contentTimework = viewChild<ElementRef>('contentTimework');

  readonly showMoreAvailable = input(true);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public size: SizeWidget = {};
  public header = input<string>(undefined);
  timeFormat = 'h:mm:ss a';
  isLoading = false;
  needPachBrowser = this.isAppleDevice() && this.isIPhoneNotInstallled();
  waiting = false;
  busy = false;
  items: TimeworkEntity[] = [];
  itemsScheduler: TimeworkScheduler[] = [];
  workedTimeByDay: TimeworkTimeByday[] = [];
  isNewEntryAvailable = false;
  shouldScale = false;
  userDaysPermission: UserDaysPermission = null;
  isTodayAvailable = false;
  unavailableDays = '';
  override watchResizeEvents = true;
  hours: string;
  minutes: string;
  activeAm: boolean;
  activePm: boolean;
  private timerId = null;
  private serverId = null;
  overflow = false;
  delayMiliseconds = 0;
  totalHours = 0;
  totalMinutes = 0;
  // chart Timeline
  isDataAvailable = false;
  isDataChartAvailable = false;
  sizeTimelineChart = {
    height: 400
  };
  // stadistics
  startDateWeek = DateUtil.formatIsoWeekStart();
  endDateWeek = DateUtil.formatIsoWeekEnd();
  totalHoursWeek = 0;
  averageHoursWeek = 0;
  totalHoursMonth = 0;
  minHeightResponsive = this.windowInnerHeight <= 720 ? 535 : 700;
  timeworkDisabled = '';

  private previousWidth = 0;
  private previousHeight = 0;

  get disableCheckIn() {
    return this.busy || !this.isNewEntryAvailable;
  }

  get disabledCheckout() {
    return this.busy || this.isNewEntryAvailable;
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    clearInterval(this.timerId);
    clearInterval(this.serverId);
    this.cdr.detach();
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.menuWidgetService.requestRefreshWidgetSize.pipe(takeUntil(this.$destroy)).subscribe((refreshData) => {
      this.refreshSize(refreshData.widgets);
      this.detectChanges();
    });
    this.menuWidgetService.refreshPendingsRequest.pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.refresh();
    });
    this.refresh();
  }

  override ngAfterViewInit() {
    this.detectChanges();
  }

  private refreshSize(widgets: readonly WidgetConfig[]): void {
    const widget = widgets.find((item) => item.header === this.header());
    if (widget) {
      this.size = widget.sizeWidget;
    }
  }

  public refresh(): void {
    this.items = [];
    this.workedTimeByDay = [];
    this.totalHours = 0;
    this.totalMinutes = 0;
    this.refreshServerTime();
    this.setCurrentTime();
    this.serverId = this.defineRefreshServerInterval();
    this.timerId = this.updateTime();
    this.refreshUserDaysPermission();
    this.refreshServer();
    this.shouldScale = this.px_ratio >= 1.5;
  }
  private defineRefreshServerInterval(): any {
    return setInterval(
      () => {
        this.refreshServerTime();
      },
      1000 * 60 * 60 * 24
    );
  }

  private refreshServerTime() {
    this.service
      .get({
        cancelableReq: this.$destroy,
        url: 'timework-widget/now',
        handleFailure: false
      })
      .subscribe({
        next: (time) => {
          if (time === null) {
            this.delayMiliseconds = 0;
          } else {
            const now = new Date(Date.now());
            const serverTime = DateUtil.parse(time.now);
            this.delayMiliseconds = serverTime.getTime() - now.getTime();
            if (this.requiresTimeAdjustment()) {
              const nowString = now.toLocaleString();
              const serverString = serverTime.toLocaleString();
              console.warn(`Local clock running is not the same than the server. Local: ${nowString} Server: ${serverString}`);
            }
          }
          this.setCurrentTime();
        },
        error: (_error) => {
          this.delayMiliseconds = 0;
          this.setCurrentTime();
        }
      });
  }

  private requiresTimeAdjustment() {
    const time = this.delayMiliseconds;
    if (time == null || typeof time === 'undefined') {
      return false;
    }
    return Math.abs(time) > 2500;
  }

  private setCurrentTime() {
    const time = new Date(Date.now());
    if (this.requiresTimeAdjustment()) {
      time.setTime(time.getTime() + this.delayMiliseconds);
    }
    this.hours = this.leftPadZero(time.getHours() % 12 || 12);
    this.minutes = this.leftPadZero(time.getMinutes());
    if (time.getHours() >= 12) {
      this.activeAm = false;
      this.activePm = true;
    } else {
      this.activeAm = true;
      this.activePm = false;
    }
    this.cdr.detectChanges();
  }

  private updateTime(): any {
    return setInterval(() => {
      this.setCurrentTime();
    }, 2500);
  }

  private leftPadZero(value: number) {
    return value < 10 ? `0${value}` : value.toString();
  }

  refreshServer() {
    if (this.waiting) {
      return;
    }
    this.waiting = true;
    this.service
      .get({
        cancelableReq: this.$destroy,
        url: 'timework-widget/mine'
      })
      .subscribe({
        next: (data: TimeworkEntity[]) => {
          this.items = [];
          this.totalHours = 0;
          this.totalMinutes = 0;
          const items = data;
          if (items && items.length > 0) {
            for (const item of items) {
              this.addNewEntities(+item.id, [
                {
                  id: item.id,
                  checkIn: item.checkIn,
                  checkOut: item.checkOut,
                  checkInTz: item.checkInTz,
                  checkOutTz: item.checkOutTz,
                  localCheckIn: item.localCheckIn,
                  localCheckOut: item.localCheckOut,
                  localCheckInTz: item.localCheckInTz,
                  localCheckOutTz: item.localCheckOutTz
                }
              ]);
            }
          }
          this.items.sort((a, b) => {
            if (a.localCheckIn > b.localCheckIn) {
              return -1;
            }
            if (a.localCheckIn < b.localCheckIn) {
              return 1;
            }
            return 0;
          });
          this.waiting = false;
          this.refreshNewEntry();
        },
        error: (error) => {
          console.log(error);
          this.waiting = false;
        }
      });
  }

  refreshNewEntry() {
    const items = this.items;
    this.isNewEntryAvailable = items && (items.length === 0 || (items[0] && !!items[0].checkOut));
    this.cdr.detectChanges();
  }

  checkinClick() {
    if (!this.isTodayAvailable) {
      return;
    }
    this.busy = true;
    this.isLoading = true;
    of(null)
      .pipe(delay(1000))
      .subscribe(() => this.checkClick(this.isGeolocationRequired(), TimeworkAction.CHECK_IN));
  }

  checkoutClick() {
    this.busy = true;
    this.isLoading = true;
    of(null)
      .pipe(delay(1000))
      .subscribe(() => this.checkClick(this.isGeolocationRequired(), TimeworkAction.CHECK_OUT));
  }

  checkPermissionClick() {
    this.refreshUserDaysPermission(true);
  }

  private isGeolocationRequired(): boolean {
    return ConfigApp.getTrackLocation() && !Session.isAnonymous();
  }

  private checkClick(geolocationRequired: boolean, method: TimeworkAction): void {
    if (!this.isTodayAvailable) {
      if (this.unavailableDays.length === 0) {
        this.dialogService.error(this.translate.instant('timeworkDisabledShort'));
      } else {
        this.dialogService.error(this.timeworkDisabled);
      }
      return;
    }
    if (geolocationRequired) {
      this.navLang.findGeolocation(geolocationRequired).then(
        (position) => {
          if (position) {
            this.doCheckClick(method, position as GeolocationPosition);
          } else {
            showFailedGeolocationMessage(false, false, this.dialogService, this.translate);
          }
        },
        (error) => this.onFindGeolocationError(error)
      );
    } else {
      this.doCheckClick(method, null);
    }
  }

  private onFindGeolocationError(error: GeolocationPositionError) {
    const failed = showErrorGeolocation(error, this.translate, this.dialogService);
    if (failed) {
      this.waiting = false;
      this.busy = false;
      this.isLoading = false;
      this.cdr.detectChanges();
    }
  }

  private doCheckClick(method: TimeworkAction, position: GeolocationPosition) {
    /**
     * Supported URLS:
     * - timework-widget/1/check-in
     * - timework-widget/1/check-out
     */
    const url = `timework-widget/${TimeworkMode.SEQUENTIAL_TIME}/${method}`;
    const data: DoCheckPayload = position
      ? {
          location: position.coords
        }
      : null;
    this.isLoading = true;
    this.busy = true;
    this.service
      .post({
        url: url,
        cancelableReq: this.$destroy,
        postBody: data,
        options: null,
        handleFailure: false
      })
      .subscribe({
        next: (gsh) => {
          if (gsh.operationEstatus === 0) {
            this.onDoCheckFailedStatus(gsh);
          } else {
            this.onDoCheckSuccessStatus(gsh, method);
          }
          this.busy = false;
          this.isLoading = false;
          this.refreshNewEntry();
          this.cdr.detectChanges();
        },
        error: (e) => this.onDoCheckClickActionError(e)
      });
  }

  private onDoCheckFailedStatus(gsh: CheckMethodResponse) {
    const message = this.translate.instantFrom(TimeworkWidgetComponent.LANG_CONFIG, `${gsh.errorMessage}`);
    this.dialogService.error(message);
    if (gsh.errorMessage === 'failed-permission') {
      this.refreshUserDaysPermission();
    }
  }

  private onDoCheckSuccessStatus(gsh: CheckMethodResponse, method: TimeworkAction) {
    const entities = onDoCheckSuccessStatus(gsh, method, this.getLanguage(), this.items);
    if (!entities) {
      return;
    }
    this.addNewEntities(gsh.savedId, entities);
  }

  private onDoCheckClickActionError(e: string | ErrorHandlingInput) {
    showDoCheckActionError(e, this.dialogService, this.navLang, this.translate);
    this.waiting = false;
    this.busy = false;
    this.isLoading = false;
    this.cdr.detectChanges();
  }

  private addNewEntities(currentTimeworkId: number, records: TimeworkEntity[]) {
    if (!this.items) {
      this.items = [];
    }
    this.items = this.items.filter((item) => +item.id !== +currentTimeworkId);
    for (const record of records) {
      const entity = this.addNewEntity(record);
      if (!entity) {
        continue;
      }
      this.items.unshift(entity);
    }
    const duration = getCheckOutDurationForEntities(this.items);
    this.totalHours = duration.hours;
    this.totalMinutes = duration.minutes;
    this.detectChanges();
    this.cdr.detectChanges();
  }

  private addNewEntity(data: TimeworkEntity): TimeworkEntity {
    const entity = parseNewEntity(data);
    if (!entity) {
      console.error('Can not add new item');
      return null;
    }
    return entity;
  }

  public detectChanges() {
    this.isDataAvailable = false;
    this.isDataChartAvailable = false;
    if (!Session.hasAccess(Module.TIMEWORK)) {
      return;
    }
    if (this.windowInnerHeight <= 720) {
      this.sizeTimelineChart.height = 250;
    }
    if (this.size.height && this.size.height > this.minHeightResponsive) {
      if (this.size.responsiveClass === 'largeResponsive' || this.size.responsiveClass === 'xlargeResponsive' || this.size.responsiveClass === 'xxlargeResponsive') {
        // Agregamos el componente chart current week
        this.getDataScheduler(true);
        this.isDataAvailable = true;
        this.getTotalHoursThisMonth();
        setTimeout(() => {
          this.isDataChartAvailable = true;
        }, 10);
      } else if (this.size.responsiveClass === 'mediumResponsive') {
        // Agregamos el componente chips current week
        this.getDataScheduler(false);
      } else {
        // Agregamos el componente chips current week
        this.getDataScheduler(true);
      }
    } else {
      if (this.size.responsiveClass !== 'xsmallResponsive' && this.size.responsiveClass !== 'smallResponsive' && this.size.responsiveClass !== 'mediumResponsive') {
        // Agregamos el componente chips current week
        this.getDataScheduler(true);
        this.isDataAvailable = true;
      }
      if (this.size.responsiveClass === 'xxlargeResponsive') {
        this.getTotalHoursThisMonth();
      }
    }
    this.cdr.detectChanges();
  }

  private getDataScheduler(week: boolean) {
    let endDateRange: string | number | Date;
    let startDateRange: string;
    if (week) {
      startDateRange = this.startDateWeek;
      endDateRange = this.endDateWeek;
    } else {
      endDateRange = DateUtil.format(new Date(), 'YYYY-MM-DD');
      if (this.windowInnerHeight <= 700) {
        startDateRange = DateUtil.formatSubtractDays(endDateRange, 3);
      } else {
        startDateRange = DateUtil.formatSubtractDays(endDateRange, 10);
      }
    }
    this.service
      .get({
        cancelableReq: this.$destroy,
        url: `timework-widget/recordsByDateRange/${startDateRange}/${endDateRange}`
      })
      .subscribe({
        next: (gridInfo: TimeworkScheduler[]) => {
          this.itemsScheduler = [];
          const items = gridInfo;
          if (items && items.length > 0) {
            for (const item of items) {
              if (item.endDate !== null) {
                const timeWorkedCalculated = DateUtil.diff(item.startDate, item.endDate);
                item.startDate = new Date(item.startDate);
                item.endDate = new Date(item.endDate);
                item.day = new Date(item.day);
                item.minutesWorked = timeWorkedCalculated.minutes;
                item.hoursWorked = timeWorkedCalculated.hours;
                this.itemsScheduler.push(item);
              }
            }
          }
          this.workedTimeByDay = TimeworkWidgetUtils.totalTimeByDay(this.itemsScheduler);
          for (const item of this.workedTimeByDay) {
            const time = new Date();
            time.setSeconds(0);
            time.setMinutes(0);
            time.setHours(0);
            time.setHours(item.hours);
            time.setMinutes(item.minutes);
            item.hours = time.getHours();
            item.minutes = time.getMinutes();
          }
          if (this.isDataAvailable) {
            // Calculamos las estadísticas
            this.totalHoursWeek = TimeworkWidgetUtils.calculateSumHoursWeek(this.workedTimeByDay);
            this.averageHoursWeek = Math.floor(this.totalHoursWeek / this.workedTimeByDay.length) || 0;
          }
        },
        error: (error) => {
          console.log(error);
        }
      });
  }

  private getTotalHoursThisMonth(): void {
    this.totalHoursMonth = 0;
    const startDateRange = DateUtil.formatStartOfMonth();
    const endDateRange = DateUtil.formatEndOfMonth();
    this.service
      .get({
        cancelableReq: this.$destroy,
        url: `timework-widget/hoursByDateRange/${startDateRange}/${endDateRange}`
      })
      .subscribe({
        next: (hours: number) => {
          if (hours !== null) {
            this.totalHoursMonth = Math.floor(hours / 60);
          }
        },
        error: (error) => {
          console.log(error);
        }
      });
  }

  private itemsListHeight(): string {
    const timeworkCheckSection = this.timeworkCheckSection();
    if (timeworkCheckSection) {
      const offset = 1; // Como offsetHeight no maneja decimales, puede haber diferencias minimas, por ello se agrega un offset para compensar.
      const padding = 20;
      const timeworkCheckSectionHeight = timeworkCheckSection.nativeElement.offsetHeight - offset;
      const contentTimeworkHeight = this.contentTimework().nativeElement.offsetHeight - offset;

      const minHeight = contentTimeworkHeight - timeworkCheckSectionHeight - contentTimeworkHeight / 10 - padding;
      if (minHeight < 40) {
        return 'unset';
      }
      return `${minHeight.toString()}px`;
    }
    return `${this.calculateItemsListHeight()}px`;
  }

  private calculateItemsListHeight(): number {
    if (this.size.height < this.minHeightResponsive) {
      if (this.size.responsiveClass === 'xsmallResponsive' || this.size.responsiveClass === 'smallResponsive') {
        if (this.windowInnerHeight <= 720) {
          if (this.size.responsiveClass === 'xsmallResponsive') {
            return 100;
          }
          return 175;
        }
        return this.size.height - 185;
      }
      return this.size.height - 110;
    }
    if (this.size.responsiveClass === 'xsmallResponsive' || this.size.responsiveClass === 'smallResponsive') {
      return this.size.responsiveClass === 'xsmallResponsive' ? this.size.height - 204 : this.size.height - 218;
    }
    if (this.size.responsiveClass === 'mediumResponsive') {
      return this.size.height - 285;
    }
    return this.size.height - 335;
  }

  get itemsMinHeight(): string {
    const heigth = this.calculateItemsMinHeight();
    return heigth === -1 ? '100%' : `${heigth}px`;
  }

  private calculateItemsMinHeight(): number {
    if (this.size.height < this.minHeightResponsive) {
      if (this.size.responsiveClass === 'xsmallResponsive' || this.size.responsiveClass === 'smallResponsive') {
        return -1;
      }
      if (this.windowInnerHeight <= 720) {
        return this.size.height - 130;
      }
      return this.size.height - 120;
    }
    if (this.size.responsiveClass === 'largeResponsive' || this.size.responsiveClass === 'xlargeResponsive' || this.size.responsiveClass === 'xxlargeResponsive') {
      return this.size.height - 120;
    }
    return this.size.height - 115;
  }

  protected afterWindowResized(event: Event) {
    this.shouldScale = this.px_ratio >= 1.5;
    // Window size hasn't changed, prevent updating.
    const target = event.target as any;
    if (this.previousWidth === target.innerWidth && this.previousHeight === target.innerHeight) {
      return;
    }
    this.previousWidth = target.innerWidth;
    this.previousHeight = target.innerHeight;
    const height = this.itemsListHeight();
    const listContainer = document.querySelector('.timework-list-container');
    if (listContainer) {
      (listContainer as HTMLElement).style['min-height'] = height;
      (listContainer as HTMLElement).style['max-height'] = height;
    }
  }

  private refreshUserDaysPermission(sendNotice = false) {
    this.service
      .get({ url: 'timework-widget/user', cancelableReq: this.$destroy, handleFailure: false })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (gsh) => {
          this.onLoadUserDaysPermission(gsh, sendNotice);
        },
        error: (error) => {
          if (error?.status === 403) {
            console.warn('User does not have access to timework');
            this.dialogService.error(this.translate.instantFrom(TIMEWORK_LANG_CONFIG, 'notAccessToTimework'));
          } else {
            console.error('Error loading user days permission', error);
          }
        }
      });
  }

  private onLoadUserDaysPermission(gsh: UserDaysPermission, sendNotice: boolean): void {
    this.userDaysPermission = gsh;
    this.setUnavailableDays();
    this.checkTodayAvailable();
    this.cdr.detectChanges();
    if (sendNotice && this.isTodayAvailable) {
      // noinspection JSIgnoredPromiseFromCall
      this.dialogService.info(this.tag('timeworkAvailable'), null, null, this.translate.instantFrom(BnextTranslateService.ERROR_COMMON, 'messageSystem'));
    } else if (sendNotice && !this.isTodayAvailable) {
      // noinspection JSIgnoredPromiseFromCall
      this.dialogService.info(this.tag('timeworkUnavailable'), null, null, this.translate.instantFrom(BnextTranslateService.ERROR_COMMON, 'messageSystem'));
    }
  }

  private checkTodayAvailable(): void {
    this.isTodayAvailable = checkTodayAvailable(this.userDaysPermission, this.isTodayAvailable);
  }

  private setUnavailableDays() {
    this.unavailableDays = getUnavailableDaysByPermission(this.userDaysPermission, this.translate);
    this.translateService.getFrom(TimeworkWidgetComponent.LANG_CONFIG, 'timeworkDisabled', { unavailableDays: this.unavailableDays }).subscribe((trans) => {
      this.timeworkDisabled = trans;
    });
  }
}
