import { BnextCoreComponent } from '@/core/bnext-core.component';
import { Component, type OnInit, inject, viewChild } from '@angular/core';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';

import type { DropdownMenuItem } from '@/core/dropdown-menu/dropdown-menu.interfaces';
import { GridComponent } from '@/core/grid/grid.component';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import { type ActionStrip, DateColumn, ObjectListColumn, TextColumn, TimeColumn, TimestampColumn } from '@/core/grid/utils/grid.interfaces';
import { AppService } from '@/core/services/app.service';
import { NoticeService } from '@/core/services/notice.service';
import { CommonAction } from '@/core/utils/enums';
import { ErrorHandling } from '@/core/utils/error-handling';
import { DatePipe } from '@angular/common';
import type { RowType } from '@infragistics/igniteui-angular';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import { TimeworkErpStatuses } from '../timework-mine/timework-erp-statuses';

@Component({
  selector: 'app-timework-deleted',
  templateUrl: './timework-deleted.component.html',
  styleUrls: ['./timework-deleted.component.scss'],
  imports: [GridComponent]
})
export class TimeworkDeletedComponent extends BnextCoreComponent implements OnInit {
  datePipe = inject(DatePipe);

  service = inject(AppService);
  noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.timework',
    componentName: 'timework-deleted'
  };

  url = 'timework-widget/deleted/list';
  columns: GridColumn[];
  title = '';
  perPage = 5;
  busy = false;
  errorMessage: any;

  readonly grid = viewChild<GridComponent>('grid');

  LangConfig = TimeworkDeletedComponent.LANG_CONFIG;
  public _componentPath = this.LangConfig.componentPath;
  public get customTagName(): string {
    return TimeworkDeletedComponent.LANG_CONFIG.componentName;
  }
  public get componentPath(): string {
    return this._componentPath;
  }
  public set componentPath(value: string) {
    this._componentPath = value;
  }

  actionStripConfig: ActionStrip;

  override ngOnInit(): void {
    super.ngOnInit();
    this.columns = [];
    const erpStatuses = TimeworkErpStatuses.getLocalizedValues({
      translateService: this.translate,
      subs: this.subs
    });
    GridUtil.columns(this.columns)
      .subs(this.subs)
      .push('userName', new TextColumn({ autoSize: true }))
      .push('registerDate', new DateColumn({ autoSize: true }))
      .push('checkIn', new TimeColumn({ width: '200px' }))
      .push('checkOut', new TimeColumn({ width: '200px' }))
      .push('checkInTz', new TextColumn({ width: '250px', hidden: true }))
      .push('checkOutTz', new TextColumn({ width: '250px', hidden: true }))
      .push('localCheckIn', new TimestampColumn({ width: '200px', hidden: true }))
      .push('localCheckOut', new TimestampColumn({ width: '200px', hidden: true }))
      .push('localCheckInTz', new TextColumn({ width: '250px', hidden: true }))
      .push('localCheckOutTz', new TextColumn({ width: '250px', hidden: true }))
      .push('workedTime', new TextColumn({ width: '250px' }))
      .push(
        'createdDate',
        new TimestampColumn({
          hidden: true,
          width: '280px',
          dateFormat: 'DD/MM/YYYY H:mm:ss'
        })
      )
      .push('lastModifiedDate', new TimestampColumn({ width: '280px' }))
      .push(
        'erpStatus',
        new ObjectListColumn({
          width: '250px',
          hidden: true,
          render: {
            valueKey: 'value',
            labelKey: 'text',
            items: erpStatuses
          }
        })
      )
      .push('erpError', new TextColumn({ width: '250px', hidden: true }))
      .push('documentDescription', new TextColumn({ width: '250px', hidden: true }))
      .push('outstandingSurveyCode', new TextColumn({ width: '150px', hidden: true }))
      .langAsync(this.translateService, TimeworkDeletedComponent.LANG_CONFIG, 'i18n');
    this.subs.push(this.translateService.getFrom(TimeworkDeletedComponent.LANG_CONFIG, 'i18n.title').subscribe((tag) => (this.title = tag)));
    this.actionStripConfig = this.setActionStripConfig();
    this.cdr.detectChanges();
  }

  setActionStripConfig() {
    return {
      dropdownAlwaysVisible: false,
      dropdownActionLimit: 6,
      render: {
        menuActionOptions: (row: RowType) => (row ? this.getData() : []),
        rowIdentifierKey: 'code',
        toggleButtonTitle: this.translateService.instant('root.common.button.REMOVE')
      }
    };
  }

  getData(): DropdownMenuItem[] {
    return [
      {
        text: this.translateService.instant('root.common.button.REMOVE'),
        value: CommonAction.REMOVE,
        iconName: 'restore_from_trash',
        hidden: false
      }
    ];
  }

  restore(drop: any) {
    const url = 'timework-widget/deleted/restore';
    this.busy = true;
    this.service.post({ url, cancelableReq: this.$destroy, postBody: drop.row.id, options: null, handleFailure: false }).subscribe(
      () => {
        this.busy = false;
        this.noticeService.notice(this.tag('restore-success'));
        this.grid().refresh();
      },
      (e) => {
        this.busy = false;
        this.filterError(e);
      }
    );
  }

  filterError(e) {
    if (e.error.includes('constraint') || e.error.includes('UQ_TIMEWORK_CHECK_IN_CONSTRAINT')) {
      this.dialogService.error(this.translate.instant('restore-error'), this.translate.instant('duplicate-restore-timework'));
    } else {
      ErrorHandling.notifyError(this.errorMessage, this.navLang);
    }
  }
  toogleMenu(drop: GridDropDownItem) {
    switch (drop.item.value) {
      case CommonAction.REMOVE:
        this.restore(drop);
        break;
    }
  }
}
