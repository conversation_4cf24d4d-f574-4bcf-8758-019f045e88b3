import type { GridColumn } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import { type ActionStrip, DateColumn, ObjectListColumn, TextColumn, TimeColumn, TimestampColumn } from '@/core/grid/utils/grid.interfaces';
import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import { GridComponent } from 'src/app/core/grid/grid.component';
import { TimeworkErpStatuses } from './timework-erp-statuses';

import { BnextCoreComponent } from '@/core/bnext-core.component';
import { Component, type OnInit, inject, viewChild } from '@angular/core';

import { DatePipe } from '@angular/common';
import type { RowType } from '@infragistics/igniteui-angular';
import { takeUntil } from 'rxjs/operators';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import { Session } from 'src/app/core/local-storage/session';
import { CommonAction } from 'src/app/core/utils/enums';
import { TimeworkMineUtil } from './timework-mine-util';
import type { TimeworkMineListDto } from './timework-mine.interfaces';

@Component({
  selector: 'app-timework-mine',
  templateUrl: './timework-mine.component.html',
  styleUrls: ['./timework-mine.component.scss'],
  imports: [GridComponent]
})
export class TimeworkMineComponent extends BnextCoreComponent implements OnInit {
  datePipe = inject(DatePipe);

  url = 'timework-widget/mine/list';
  columns: GridColumn[];
  titleLabelKey = 'title';
  titleLabel: string;

  readonly grid = viewChild<GridComponent<TimeworkMineListDto>>('grid');

  LangConfig = TimeworkMineUtil.LANG_CONFIG;
  public _componentPath = this.LangConfig.componentPath;
  public get customTagName(): string {
    return TimeworkMineUtil.LANG_CONFIG.componentName;
  }
  public get componentPath(): string {
    return this._componentPath;
  }
  public set componentPath(value: string) {
    this._componentPath = value;
  }

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.getData(row?.data) : []),
      rowIdentifierKey: 'entity_code'
    }
  };

  getData(_row: any): DropdownMenuItem[] {
    return [
      {
        text: 'Bitácora',
        value: CommonAction.LOGGING,
        iconName: 'timeline'
      }
    ];
  }

  toogleMenu(drop: GridDropDownItem<TimeworkMineListDto>) {
    switch (drop.item.value) {
      case CommonAction.LOGGING:
        this.menuService.navigate(`menu/logging-access/${drop.row.id}/Timework`);
        break;
      default:
        this.dialogService.error('Unsupported action!');
        break;
    }
  }

  override ngOnInit(): void {
    this.perPage = Session.getGridSize();
    const erpStatuses = TimeworkErpStatuses.getLocalizedValues({
      translateService: this.translate,
      subs: this.subs
    });
    this.translate
      .getFrom(this.LangConfig, this.titleLabelKey)
      .pipe(takeUntil(this.$destroy))
      .subscribe((tag) => (this.titleLabel = tag));
    this.columns = [];
    GridUtil.columns(this.columns)
      .subs(this.subs)
      .push(
        'code',
        new TextColumn({
          width: '300px',
          hidden: true
        })
      )
      .push('registerDate', new DateColumn({ autoSize: true, hidden: true }))
      .push('checkIn', new TimeColumn({ width: '200px', hidden: true }))
      .push('checkOut', new TimeColumn({ width: '200px', hidden: true }))
      .push('checkInTz', new TextColumn({ width: '250px', hidden: true }))
      .push('checkOutTz', new TextColumn({ width: '250px', hidden: true }))
      .push('localCheckIn', new TimestampColumn({ width: '200px' }))
      .push('localCheckOut', new TimestampColumn({ width: '200px' }))
      .push('localCheckInTz', new TextColumn({ width: '250px', hidden: true }))
      .push('localCheckOutTz', new TextColumn({ width: '250px', hidden: true }))
      .push('workedTime', new TextColumn({ width: '250px' }))
      .push('documentDescription', new TextColumn({ width: '250px', hidden: true }))
      .push('outstandingSurveyCode', new TextColumn({ width: '150px', hidden: true }))
      .push(
        'createdDate',
        new TimestampColumn({
          hidden: true,
          width: '200px'
        })
      )
      .push('lastModifiedDate', new TimestampColumn({ width: '200px' }))
      .push(
        'erpStatus',
        new ObjectListColumn({
          width: '250px',
          hidden: true,
          render: {
            valueKey: 'value',
            labelKey: 'text',
            items: erpStatuses
          }
        })
      )
      .push('erpError', new TextColumn({ width: '250px', hidden: true }))
      .langAsync(this.translateService, TimeworkMineUtil.LANG_CONFIG, 'columns')
      .langAsync(this.translateService, BnextTranslateService.FIELD_COMMON, ['code', 'lastModifiedDate'], true);
    this.cdr.detectChanges();
  }
}
