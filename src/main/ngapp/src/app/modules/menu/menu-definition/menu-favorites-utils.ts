import { ErrorHandling } from '@/core/utils/error-handling';
import type { QuickAccessHolder } from './../menu-main/menu-main.interfaces';

import type { NavigateLanguageService } from '@/core/services/navigate-lang.service';
import type { DataMap } from '@/core/utils/data-map';
import * as DateUtil from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';

import { keysObject } from '@/core/utils/object';
import type { TextHasValue } from 'src/app/core/utils/text-has-value';
import type { MenuService } from './../services/menu.service';

export class MenuFavoritesUtils {
  public static GRID_STATUS_LIST: TextHasValue[] = [
    {
      value: 'folder',
      text: 'folder'
    },
    {
      value: 'assignment',
      text: 'assignment'
    },
    {
      value: 'check',
      text: 'check'
    },
    {
      value: 'done',
      text: 'done'
    },
    {
      value: 'launch',
      text: 'launch'
    }
  ];

  public static removeQuickAccess(holder: QuickAccessHolder, menuService: MenuService, navLang: NavigateLanguageService) {
    try {
      menuService.removeQuickAccess([holder.favoriteTaskId]);
      holder.isFavorite = 0;
    } catch (e) {
      ErrorHandling.notifyError(e, navLang);
    }
  }

  /**
   * Recibe un MAPA de con 1 solo nivel de llaves con valor y
   * las convierte a un objeto que transforma los puntos "." en atributos del objeto
   * padre.
   *
   * Se utiliza para guardar todos los parametros en 1 solo listado en base de datos.
   *
   * Ejemplo:
   *
   * [>> IN >>]
   *     {
   *         "llave1.hijo1.attr1_boolean": "valor true o false",
   *         "llave1.hijo1.attr2_number": "valor numerico",
   *         "llave1.hijo1.attr3_date": "valor numerico que se convertirá a fecha",
   *         "llave2.hijo1_string": "valor string",
   *         "llave2.hijo2": "valor de lo que sea convertido a string",
   *         "llave2.hijo3": "valor de lo que sea convertido a string"
   *     }
   *
   * [<< OUT <<]
   *     {
   *         "llave1": {
   *             "hijo1": {
   *                 "attr1": "true | false",
   *                 "attr2": "Number(numero)",
   *                 "attr3": "new Date(numero)",
   *             }
   *          }
   *         "llave2": {
   *             "hijo1": "String(valor string)",
   *             "hijo2": "String(valor string)",
   *             "hijo3": "String(valor string)",
   *         }
   *     }
   * @param paramsHolder
   * @param keys
   * @param parentKey
   * @param result
   * @returns
   */
  public static getFavoriteParams(paramsHolder: DataMap<string>, keys: any[], parentKey: string | number, arrayProps: string[], result: DataMap<any> = {}): DataMap<any> {
    if (!paramsHolder || typeof paramsHolder !== 'object') {
      return result;
    }
    for (const key of keys) {
      if (key === null || typeof key === 'undefined') {
        console.warn(`Invalid favorite key '${key}]'.`);
        continue;
      }
      if (key.indexOf('.') !== -1) {
        let isArray = false;
        const newKeys = key.split(/(^.+?)\./) || [0, 0];
        if (newKeys.length > 2) {
          // Soporta a objetos de arreglos, por ejemplo files.0.description, la propiedad files debe ser un arreglo
          const nextLevelPath = (newKeys[2] || '').toString();
          const resultKeyName = MenuFavoritesUtils.getNormalizedPropertyName(nextLevelPath);
          if (nextLevelPath !== null && typeof nextLevelPath !== 'undefined' && nextLevelPath !== '') {
            if (nextLevelPath.indexOf('.') !== -1) {
              const nextLevelKeys = nextLevelPath.split(/(^.+?)\./) || [0, 0];
              if (NumberUtil.isInteger(nextLevelKeys[1])) {
                isArray = true;
              }
            } else if (NumberUtil.isInteger(resultKeyName)) {
              isArray = true;
            }
          }
        }
        const currentKey = newKeys.splice(0, 2)[1];
        if (!result[currentKey] && (isArray || arrayProps.indexOf(currentKey.toString()) !== -1)) {
          result[currentKey] = [];
        } else if (!result[currentKey]) {
          result[currentKey] = {};
        }
        if (parentKey !== '') {
          MenuFavoritesUtils.getFavoriteParams(paramsHolder, newKeys, `${parentKey}.${currentKey}`, arrayProps, result[currentKey]);
        } else {
          MenuFavoritesUtils.getFavoriteParams(paramsHolder, newKeys, currentKey, arrayProps, result[currentKey]);
        }
      } else {
        const resultKey = key;
        let value = paramsHolder[key];
        if (parentKey !== '') {
          value = paramsHolder[`${parentKey}.${key}`];
        }
        const resultKeyName = MenuFavoritesUtils.getNormalizedPropertyName(resultKey);
        let resultValue: any;
        if (resultKey.match(/_date$/) && NumberUtil.isNumber(value)) {
          resultValue = new Date(+value);
        } else if (resultKey.match(/_boolean$/) || String(value) === 'true' || String(value) === 'false') {
          resultValue = String(value) === 'true';
        } else if (resultKey.match(/_number$/) && NumberUtil.isInteger(value)) {
          resultValue = +value;
        } else if (resultKey.match(/_string$/)) {
          resultValue = String(value);
        } else {
          resultValue = String(value);
        }
        result[resultKeyName] = resultValue;
      }
    }
    return result;
  }

  private static getNormalizedPropertyName(resultKey: string) {
    if (resultKey.match(/_date$/)) {
      return resultKey.replace(/_date$/, '');
    }
    if (resultKey.match(/_boolean$/)) {
      return resultKey.replace(/_boolean$/, '');
    }
    if (resultKey.match(/_number$/)) {
      return resultKey.replace(/_number$/, '');
    }
    if (resultKey.match(/_string$/)) {
      return resultKey.replace(/_string$/, '');
    }
    return resultKey;
  }

  /**
   * Recibe un objeto JS de con varios niveles y
   * las convierte a un MAPA que transforma los subniveles del objeto en puntos "."
   *
   * Se utiliza para guardar todos los parametros en 1 solo listado en base de datos.
   *
   * Ejemplo:
   *
   * [>> IN >>]
   *     {
   *         "llave1": {
   *             "hijo1": {
   *                 "attr1": "true | false",
   *                 "attr2": "Number(numero)",
   *                 "attr3": "new Date(numero)",
   *             }
   *          }
   *         "llave2": {
   *             "hijo1": "String(valor string)",
   *             "hijo2": "String(valor string)",
   *             "hijo3": "String(valor string)",
   *         }
   *     }
   *
   * [<< OUT <<]
   *     {
   *         "llave1.hijo1.attr1_boolean": "valor true o false",
   *         "llave1.hijo1.attr2_number": "valor numerico",
   *         "llave1.hijo1.attr3_date": "valor numerico que se convertirá a fecha",
   *         "llave2.hijo1_string": "valor string",
   *         "llave2.hijo2": "valor de lo que sea convertido a string",
   *         "llave2.hijo3": "valor de lo que sea convertido a string"
   *     }
   *
   * @param paramsHolder
   * @param value
   * @param parentKey
   * @param result
   * @returns
   */
  public static setFavoriteParams(paramsHolder: DataMap<string>, value: any, parentKey: string, ignoreKeys: Set<string> = null): void {
    if (value === null || typeof value === 'undefined') {
      return;
    }
    if (MenuFavoritesUtils.isPrimitive(value)) {
      MenuFavoritesUtils.setFavoriteParam(paramsHolder, value, parentKey, ignoreKeys);
      return;
    }
    // Se recorren todas las keys
    for (const key of keysObject(value)) {
      // Se excluye el `key !== 'element'` por que se utiliza en `language_strings`
      const valueKey = value[key];
      if (valueKey == null || typeof valueKey === 'undefined' || ignoreKeys?.has(key)) {
        continue;
      }
      const paramKey = `${parentKey}.${key}`;
      MenuFavoritesUtils.setFavoriteParam(paramsHolder, valueKey, paramKey, ignoreKeys);
    }
  }

  private static isPrimitive(value: any): boolean {
    return value !== Object(value) || DateUtil.isDate(value);
  }

  private static setFavoriteParam(paramsHolder: DataMap<string>, propValue: any, propKey: string, ignoreKeys: Set<string> = null): void {
    if (typeof propValue === 'boolean') {
      paramsHolder[`${propKey}_boolean`] = String(propValue).trim();
    } else if (NumberUtil.isNumber(propValue)) {
      paramsHolder[`${propKey}_number`] = String(propValue);
    } else if (DateUtil.isDate(propValue)) {
      paramsHolder[`${propKey}_date`] = String(DateUtil.safe(propValue).getTime());
    } else if (typeof propValue === 'string') {
      paramsHolder[`${propKey}_string`] = String(propValue).trim();
    } else if (typeof propValue === 'object') {
      // arreglos y objetos
      MenuFavoritesUtils.setFavoriteParams(paramsHolder, propValue, propKey, ignoreKeys);
    } else {
      paramsHolder[propKey] = String(propValue).trim();
    }
  }
}
