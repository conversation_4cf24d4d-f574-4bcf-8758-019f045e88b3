<div [class]="'chart-container ' + size.responsiveClass">
  <div class="container-data-chart-header" [class.scale_widgets]="shouldScale">
    <label class="pending-data-chart-header"> {{ 'pendings_by_module' | translate: this }} </label>
  </div>
  <div class="container" [style.max-height]="this.size.height - 38 + 'px'" [class.min-height]="this.size.height < minHeightResponsive">
    <div class="chart-elements" [style.min-height]="minHeightChartE">
      @if (!emptyChart) {
        <div class="pie-chart">
          <div class="dx-viewport">
            <dx-pie-chart
              #pieChart
              [dataSource]="chartDataSource"
              [animation]="false"
              type="doughnut"
              centerTemplate="centerTemplate"
              [innerRadius]="0.65"
              (onPointClick)="onChartClic($event)"
              (onLegendClick)="onLegendClick($event)"
              [customizePoint]="customPoint"
              [size]="sizeChart"
            >
              <dxi-series argumentField="moduleKey" valueField="tasks" lowValueField="1">
                <dxo-label [visible]="false">
                  <dxo-connector [visible]="false"></dxo-connector>
                </dxo-label>
              </dxi-series>
              <dxo-legend [visible]="legendVisible" horizontalAlignment="center" verticalAlignment="top" [customizeText]="customLegendText"></dxo-legend>
              <dxo-tooltip [enabled]="true" [customizeTooltip]="customTooltip"></dxo-tooltip>
              <svg *dxTemplate="let pieChart of 'centerTemplate'">
                <circle cx="100" cy="100" [attr.r]="pieChart.getInnerRadius() - 18" fill="#eee"></circle>
                <text text-anchor="middle" style="font-size: 18px" x="70" y="90" fill="#494949">
                  <tspan x="100" style="font-weight: 600">{{ calculateTotal(pieChart) }}</tspan>
                  <tspan x="100" dy="20px">{{ 'pendings_label' | translate: this }}</tspan>
                </text>
              </svg>
            </dx-pie-chart>
          </div>
        </div>
      }
      @if (emptyChart) {
        <div class="empty-state">
          <span class="material-icons" [style.height]="sizeChart.height + 'px'" [style.width]="sizeChart.height + 'px'" [style.font-size]="sizeChart.height + 20 + 'px'">
            donut_large
          </span>
          @if (showEmptyAdvise) {
            <span class="empty-advise"
              ><span class="material-icons">info</span>&nbsp;<strong>{{ 'empty_advise' | translate: this }}</strong></span
            >
          }
        </div>
      }
      <div class="legend-custom" [hidden]="legendCustomHidden">
        @for (module of chartDataSource; track module.moduleKey) {
          <div class="legend-item">
            <span class="dot" [style.background-color]="getColor(module.moduleKey)"></span>
            <span [innerHTML]="getModuleName(module.moduleKey)"></span>
            <div class="legend-item-bar">
              <dx-progress-bar class="linear-bar" [value]="module.tasksPercent" [min]="0" [showStatus]="false"></dx-progress-bar>
              <span
                ><strong>{{ module.tasks }}</strong></span
              >
            </div>
            @for (pendingType of module.pendingsType; track pendingType; let index = $index) {
              <span class="legend-item-detail" [class.visible-detail]="size.height > minHeightResponsive" [hidden]="size.height < minHeightResponsive">
                @if (index < 4) {
                  <span
                    ><strong [style.color]="getColor(module.moduleKey)">{{ pendingType.suffix }}</strong
                    >&nbsp;{{ pendingType.text }}</span
                  >
                }
              </span>
            }
            @if (module.pendingsType.length > 4) {
              <span class="legend-item-detail" [hidden]="size.height < minHeightResponsive" (click)="openDialog(module)">
                <igx-icon family="material">open_in_full</igx-icon>
              </span>
            }
          </div>
        }
      </div>
    </div>
    @if (!showEmptyAdvise && emptyChart) {
      <div class="empty-advise-container">
        <span class="empty-advise"
          ><span class="material-icons">info</span>&nbsp;<strong>{{ 'empty_advise' | translate: this }}</strong></span
        >
      </div>
    }
    <!-- CHART pendings -->
    @if (largeChartVisible) {
      <div class="large-chart">
        <igx-category-chart
          #largeChart
          width="100%"
          [height]="minHeightResponsive < minHeightResponsive ? '250px' : '290px'"
          [dataSource]="splineDataSource"
          chartType="Spline"
          [yAxisTitle]="'pendings_by_module' | translate: this"
          isHorizontalZoomEnabled="false"
          isVerticalZoomEnabled="false"
          [tooltipTemplate]="valueTooltip"
          [markerTypes]="'none'"
          [crosshairsDisplayMode]="'None'"
        >
        </igx-category-chart>
        <ng-template let-series="series" let-item="item" #valueTooltip>
          <span>{{ item[series.valueMemberPath] }}</span>
        </ng-template>
        <div class="filter-range-chart">
          <label class="pending-data-chart-header"> {{ 'timeInterval' | translate: this }} </label>
          <igx-date-picker [class.display-density--compact]="true" [(ngModel)]="range.start" (valueChange)="dateStartChange($event)" mode="dialog">
            <label igxLabel>{{ 'date-range-start' | translate: this }}</label>
          </igx-date-picker>
          <igx-date-picker [class.display-density--compact]="true" [(ngModel)]="range.end" (valueChange)="dateEndChange($event)" mode="dialog">
            <label igxLabel>{{ 'date-range-end' | translate: this }}</label>
          </igx-date-picker>
        </div>
      </div>
    }
  </div>
</div>

<igx-dialog #moduleDetailDialog leftButtonLabel="OK" (leftButtonSelect)="moduleDetailDialog.close()">
  <div class="grid-container">
    <dx-pie-chart #pieModuleChart [dataSource]="chartModuleDataSource" [animation]="false" type="doughnut" centerTemplate="centerTemplate" [innerRadius]="0.65">
      <dxi-series argumentField="text" valueField="suffix">
        <dxo-label [visible]="true">
          <dxo-connector [visible]="false" [width]="1"></dxo-connector>
        </dxo-label>
      </dxi-series>
      <dxo-legend orientation="horizontal" horizontalAlignment="center" verticalAlignment="bottom" itemTextPosition="right"></dxo-legend>
      <dxo-tooltip [enabled]="false" [customizeTooltip]="customTooltip"></dxo-tooltip>
      <svg *dxTemplate="let pieModuleChart of 'centerTemplate'">
        <circle cx="100" cy="100" [attr.r]="pieModuleChart.getInnerRadius() - 18" fill="#eee"></circle>
        <text text-anchor="middle" style="font-size: 18px" x="70" y="90" fill="#494949">
          <tspan x="100" style="font-weight: 600">{{ calculateTotal(pieModuleChart) }}</tspan>
          <tspan x="100" dy="20px">{{ 'pendings_label' | translate: this }}</tspan>
        </text>
      </svg>
    </dx-pie-chart>
  </div>
</igx-dialog>
