@use 'src/styles/mediaQueries' as *;
@use 'src/styles/panel-sizes' as *;
@use 'src/styles/immutable-colors' as *;
@use 'sass:string';

:host ::ng-deep {
  .container-data-chart-header {
    padding: 1rem;

    .pending-data-chart-header {
      text-transform: none;
      letter-spacing: normal;
      line-height: 0.2rem;
      font-size: 1rem;
      font-weight: bold;
    }
    &.scale_widgets {
      padding: 1rem 1rem 0 1rem;
    }
  }

  .chart-elements {
    .pie-chart {
      margin: auto;
    }
    .legend-custom {
      .legend-item {
        min-width: 10rem;
        .dot {
          height: 0.8rem;
          width: 0.8rem;
          background-color: #bbb;
          border-radius: 50%;
          display: inline-block;
          margin-right: 0.5rem;
        }
        &-bar {
          display: flex;
          flex-direction: row;
          align-items: center;
          .linear-bar {
            width: string.unquote('calc(100% - 2rem)');
          }
          span {
            padding-left: 1.4rem;
          }
        }
        &-detail {
          font-size: 0.875rem;
          display: flex;
          flex-direction: column;
          padding-left: 0.5rem;
        }
      }
    }
    .empty-state {
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-content: center;
      .empty-advise {
        display: flex;
        padding: 0.5rem;
      }
    }
  }

  .empty-advise-container {
    padding: 0.5rem;
    .empty-advise {
      display: flex;
      padding: 0.5rem;
    }
  }

  dx-pie-chart {
    margin: 0 auto;
  }

  .large-chart {
    padding-top: 1rem;
  }

  .xsmallResponsive {
    .container {
      .chart-elements {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem 0 0 0;
        .pie-chart {
          margin: auto;
        }
        .legend-custom {
          padding: 1.2rem 0.5rem;
          display: flex;
          flex-flow: row wrap;
          justify-content: center;

          .legend-item {
            min-width: 5rem;
            width: 100%;
            max-width: string.unquote('calc(100% - 15rem)');
            padding: 0.3rem 0.8rem;

            &-detail {
              display: none;
            }
          }
        }
        .empty-state {
          flex-flow: row;
          .empty-advise {
            font-size: 0.75rem;
            padding: 0.2rem;
          }
        }
      }

      .large-chart {
        display: none;
      }
      &.min-height {
        .chart-elements {
          .pie-chart {
            margin: auto;
          }
          .legend-custom {
            padding: 1.8rem 0 0.5rem 0;
          }
        }
      }
    }
  }

  .smallResponsive {
    .container {
      .chart-elements {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;
        .pie-chart {
          margin: auto;
        }
        .legend-custom {
          padding: 1.2rem 0.5rem;
          display: flex;
          flex-flow: row wrap;
          justify-content: center;

          .legend-item {
            min-width: 5rem;
            width: 100%;
            max-width: string.unquote('calc(100% - 20rem)');
            padding: 0.2rem 0.8rem;

            &-detail {
              display: none;
            }
          }
        }
      }
      .large-chart {
        display: none;
      }
      &.min-height {
        .chart-elements {
          .pie-chart {
            margin: auto;
          }
          .legend-custom {
            padding: 1.8rem 0 0.5rem 0;
          }
        }
      }
    }
  }

  .mediumResponsive {
    .container {
      .chart-elements {
        display: flex;
        height: 100%;
        align-items: center;
        padding: 0;
        flex-direction: row;
        justify-content: space-evenly;
        .pie-chart {
          margin: unset;
        }
        .legend-custom {
          padding: 0;
          display: flex;
          flex-flow: row wrap;
          justify-content: space-evenly;
          align-items: baseline;
          align-items: center;

          .legend-item {
            min-width: 10rem;
            width: 100%;
            padding: 0.8rem;
            max-width: string.unquote('calc(100% - ' + $single-column-panel-width + ')');

            &-detail {
              display: none;
              &.visible-detail {
                display: flex;
              }
            }
          }
        }
      }
      .large-chart {
        display: none;
      }
      &.min-height {
        .chart-elements {
          display: flex;
          height: 100%;
          align-items: center;
          padding: 0 1rem;
          .pie-chart {
            margin: unset;
          }
          .legend-custom {
            padding: 0;
            display: flex;
            flex-flow: row wrap;
            justify-content: space-around;
            align-items: center;
            width: 100%;
            height: auto;

            .legend-item {
              min-width: 9rem;
              width: 100%;
              padding: 0.2rem 0.8rem;
              max-width: string.unquote('calc(100% - ' + $single-column-panel-width + ')');

              &-detail {
                display: none;
                &.visible-detail {
                  display: flex;
                }
              }
            }
          }
        }
      }
    }
  }

  .largeResponsive {
    .container {
      .chart-elements {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 1.5rem;
        .pie-chart {
          margin: auto;
        }
        .legend-custom {
          padding: 0rem 0 0rem 1.5rem;
          display: flex;
          flex-flow: row wrap;
          justify-content: flex-start;
          height: 100%;
          align-items: baseline;

          .legend-item {
            min-width: 14rem;
            width: 100%;
            padding-bottom: 0rem;
            max-width: string.unquote('calc(100% - 60rem)');
            padding: 0rem 1rem;
          }
        }
      }
      .large-chart {
        display: block;
        width: string.unquote('calc(100% - 7rem)');
        margin: auto;
      }
      &.min-height {
        display: flex;
        justify-content: space-around;

        .chart-elements {
          display: flex;
          justify-content: space-around;
          padding: 0 0.4rem;
          .pie-chart {
            margin: auto;
          }
          .legend-custom {
            padding: 0 0 0 1rem;
            align-items: center;
            display: flex;
            flex-flow: row wrap;
            align-items: center;
            .legend-item {
              max-width: string.unquote('calc(100% - 60rem)');
              padding: 0rem 0.5rem 0 0.5rem;
              &-detail {
                display: none;
              }
            }
          }
        }
        .large-chart {
          display: block;
        }
      }
    }
  }

  .xlargeResponsive {
    .container {
      .chart-elements {
        display: flex;
        justify-content: space-between;
        padding: 0 1rem;
        .pie-chart {
          margin: auto;
        }
        .legend-custom {
          padding: 0 0 0 1rem;
          display: flex;
          flex-flow: row wrap;
          justify-content: flex-start;
          height: 100%;
          align-items: baseline;

          .legend-item {
            min-width: 14.5rem;
            width: 100%;
            padding-bottom: 1rem;
            max-width: string.unquote('calc(100% - 60rem)');
            padding: 0rem 0.5rem;
          }
        }
      }
      .large-chart {
        display: flex;
        width: string.unquote('calc(100% - 7rem)');
        margin: auto;
        flex-direction: row;
      }
      &.min-height {
        display: flex;
        justify-content: space-around;

        .chart-elements {
          display: flex;
          justify-content: space-around;
          padding: 0 0.4rem;
          .pie-chart {
            margin: auto;
          }
          .legend-custom {
            padding: 0 0 0 1rem;
            display: flex;
            flex-flow: row wrap;
            justify-content: flex-start;
            height: 100%;
            align-items: center;
            .legend-item {
              min-width: 14.5rem;
              width: 100%;
              padding-bottom: 1rem;
              max-width: string.unquote('calc(100% - 60rem)');
              padding: 0rem 0.5rem;
            }
          }
        }
        .large-chart {
          display: none;
        }
      }
    }
  }

  .xxlargeResponsive {
    .container {
      .chart-elements {
        display: flex;
        justify-content: space-around;
        padding: 0.5rem 1.5rem;
        .pie-chart {
          margin: auto;
        }
        .legend-custom {
          padding: 0;
          display: flex;
          flex-flow: row wrap;
          justify-content: flex-start;
          height: 100%;
          align-items: baseline;

          .legend-item {
            min-width: 14rem;
            width: 100%;
            max-width: string.unquote('calc(100% - 72rem)');
            padding: 0 1rem;
          }
        }
      }
      .large-chart {
        display: flex;
        width: string.unquote('calc(100% - 12rem)');
        margin: auto;
        flex-direction: row;
      }
      &.min-height {
        display: flex;
        justify-content: space-around;

        .chart-elements {
          display: flex;
          justify-content: space-around;
          padding: 0 0 0 0.5rem;
          .pie-chart {
            margin: auto;
          }
          .legend-custom {
            padding: 0;
            justify-content: center;
            align-items: center;
            .legend-item {
              max-width: string.unquote('calc(100% - 75rem)');
              padding: 0.5rem 1rem;
              &-detail {
                display: none;
              }
            }
          }
        }
        .large-chart {
          display: block;
          width: string.unquote('calc(100% - 2rem)');
          margin-right: 2rem;
          padding-top: 0;
        }
        .empty-state .empty-advise {
          padding: 0.2rem;
        }
      }
    }
  }

  /*
  Los siguientes estilos son solo para quitar el margen de la gráfica en modo laptop
  responsividad only 1366px a 1782px */
  @media screen and (min-width: 85.375rem) and (max-width: 111.375rem) {
    .container-data-chart-header {
      padding: 1rem 1rem 0 1rem !important;
    }
    .container.min-height {
      .pie-chart {
        margin: 0 !important;
      }
    }
    .empty-state .empty-advise {
      padding: 0.5rem 0 !important;
    }
  }

  /* responsividad small only */
  @media print, screen and (max-width: $small) {
    @media (hover: none) and (pointer: coarse) {
      .chart-container {
        background: $bg-color;
        display: flex;
        width: 100%;
        height: string.unquote('calc(100vh - 6.5rem)');
        flex-flow: column;
        justify-content: flex-start;
      }
      .container-data-chart-header {
        padding: 0.2rem 1rem;
      }
      .container {
        .chart-elements {
          display: flex;
          flex-direction: column;
          align-items: center;

          .legend-custom {
            padding: 1.5rem 1rem;
            display: flex;
            flex-flow: row wrap;
            .legend-item {
              min-width: 5rem;
              width: 100%;
              max-width: string.unquote('calc(50vw - 1rem)');
              padding: 0 0.5rem;

              &-detail {
                display: none;
              }
            }
          }
        }
        .large-chart {
          display: none;
        }
      }
    }
  }

  /* responsividad xsmall 425px */
  @media print, screen and (max-width: $xsmall) {
    @media (hover: none) and (pointer: coarse) {
      .chart-container {
        background: $bg-color;
        display: flex;
        width: 100%;
        height: string.unquote('calc(100vh - 6.5rem)');
        flex-flow: column;
        justify-content: flex-start;
      }
      .container-data-chart-header {
        padding: 0.2rem 1rem;
      }
      .container {
        .chart-elements {
          display: flex;
          flex-direction: column;
          align-items: center;

          .legend-custom {
            padding: 1.5rem 1rem;
            display: flex;
            flex-flow: row wrap;
            .legend-item {
              min-width: 5rem;
              width: 100%;
              max-width: string.unquote('calc(50vw - 1rem)');
              padding: 0 0.5rem;

              &-detail {
                display: none;
              }
            }
          }
        }

        .large-chart {
          display: none;
        }
      }
    }
  }

  @media only screen and (min-width: 20rem) and (max-width: 60rem) and (orientation: landscape) and (hover: none) and (pointer: coarse) {
    .chart-container {
      background: $bg-color;
      display: flex;
      width: 100%;
      height: string.unquote('calc(100vw - 6.5rem)');
      flex-flow: column;
      justify-content: flex-start;
    }
    .container-data-chart-header {
      padding: 0.2rem 1rem;
    }
    .container {
      .chart-elements {
        display: flex;
        flex-direction: row;
        align-items: center;
        min-height: 100% !important;

        .legend-custom {
          padding: 0.5rem 0.5rem;
          display: flex;
          flex-flow: row wrap;
          .legend-item {
            min-width: 5rem;
            width: 100%;
            max-width: string.unquote('calc(50vh - 2rem)');
            padding: 0 0.5rem;

            &-detail {
              display: none;
            }
          }
        }
      }

      .large-chart {
        display: none;
      }
    }
  }

  igx-icon {
    cursor: pointer;
  }
}
