import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { MockLocalStorage } from 'src/app/core/test/utils/mock-local-storage';
import { MenuWidgetComponent } from './menu-widget.component';
import { MenuWidgetService } from './menu-widget.service';

describe('MenuWidgetComponent', () => {
  let component: MenuWidgetComponent;
  let fixture: ComponentFixture<MenuWidgetComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule],
      providers: [...MockProviders.PROVIDERS, MenuWidgetService]
    }).compileComponents();
    MockLocalStorage.defaultSession();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MenuWidgetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
