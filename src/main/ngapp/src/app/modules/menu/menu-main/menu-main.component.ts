import { BnextCoreComponent } from '@/core/bnext-core.component';
import type { DropdownMenuItem } from '@/core/dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonConfig, FabButtonMenuItem } from '@/core/fab-button-menu/fab-button-menu.interface';
import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import { MenuMainLocalConfig } from '@/core/indexed-db/menu-main-local-config';
import { ConfigApp } from '@/core/local-storage/config-app';
import { LocalStorageItem } from '@/core/local-storage/local-storage-enums';
import { LoginAvailable } from '@/core/local-storage/login-available';
import { AppService } from '@/core/services/app.service';
import { AuthService } from '@/core/services/auth.service';
import { dispatchResize } from '@/core/utils/DeviceUtil';
import { randomUUID } from '@/core/utils/crypto-utils';
import * as DateUtil from '@/core/utils/date-util';
import { EnumUtil } from '@/core/utils/enum-util';
import { CommonAction } from '@/core/utils/enums';
import { FeatureFlag, isFeatureAvailable, isItemEnabledByApp, isModuleAvailable } from '@/core/utils/feature-util';
import { cloneObject, equalsObject } from '@/core/utils/object';
import { nameInitials, stringEndsWith } from '@/core/utils/string-util';
import { AiChatButtonComponent } from '@/modules/ai/ai-chat-button/ai-chat-button.component';
import { MAIN } from '@/modules/menu/menu-definition/menu-definition-main';
import { WORKFLOW_MENU_NAME, buildMenu, buildWorkflowMenu, hasAccess, hasAccessAdmon } from '@/modules/menu/menu-definition/menu-definition.builder';
import { EXCLUDED_MODULES, MODULE_ICON } from '@/modules/menu/menu-definition/menu-definition.const';
import { CommonModule, DatePipe, Location } from '@angular/common';
import {
  type AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  type OnChanges,
  type OnDestroy,
  type OnInit,
  Renderer2,
  type SimpleChanges,
  ViewChild,
  ViewEncapsulation,
  inject,
  signal,
  viewChild
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HammerModule, Title } from '@angular/platform-browser';
import {
  ActivatedRoute,
  type ActivatedRouteSnapshot,
  type ActivationEnd,
  type ActivationStart,
  type ChildActivationEnd,
  type ChildActivationStart,
  type GuardsCheckEnd,
  type GuardsCheckStart,
  type NavigationCancel,
  NavigationEnd,
  type NavigationError,
  type NavigationSkipped,
  NavigationStart,
  type ResolveEnd,
  type ResolveStart,
  type RouteConfigLoadEnd,
  type RouteConfigLoadStart,
  Router,
  RouterModule,
  type RoutesRecognized,
  type Scroll
} from '@angular/router';
import {
  IgxAvatarComponent,
  IgxBadgeComponent,
  IgxDialogComponent,
  IgxDividerDirective,
  IgxFlexDirective,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxLayoutDirective,
  IgxNavDrawerItemDirective,
  IgxNavDrawerMiniTemplateDirective,
  IgxNavDrawerTemplateDirective,
  IgxNavbarComponent,
  IgxNavbarTitleDirective,
  IgxNavigationDrawerComponent,
  IgxPrefixDirective,
  IgxRippleDirective,
  IgxSuffixDirective,
  IgxToggleActionDirective
} from '@infragistics/igniteui-angular';
import { type Observable, Subject, type Subscription, forkJoin } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { DropdownMenuComponent } from 'src/app/core/dropdown-menu/dropdown-menu.component';
import { FabButtonMenuComponent } from 'src/app/core/fab-button-menu/fab-button-menu.component';
import { BnextTranslateModule } from 'src/app/core/i18n/bnext-translate.module';
import { WidgetsHeader } from 'src/app/core/indexed-db/widgets-layout-config';
import { AboutApp, type AboutAppEntity } from 'src/app/core/local-storage/about-app';
import type { ConfigAppEntity } from 'src/app/core/local-storage/config-app.interfaces';
import { LocalStorageSession } from 'src/app/core/local-storage/local-storage-session';
import { Session } from 'src/app/core/local-storage/session';
import { RestApiModule } from 'src/app/core/rest-api.module';
import type { DialogResult } from 'src/app/core/services/custom-dialog.interfaces';
import { type DraggingZoneOptions, DraggingZoneReleaseService } from 'src/app/core/services/dragging-zone-release.service';
import { LoginExpiredService } from 'src/app/core/services/login-expired.service';
import { LoginService } from 'src/app/core/services/login.service';
import { NoticeService } from 'src/app/core/services/notice.service';
import type { DataMap } from 'src/app/core/utils/data-map';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import { SafeUrlPipe } from 'src/app/core/utils/safe-url.pipe';
import { StringFormat } from 'src/app/core/utils/string-format';
import { TabUtils } from 'src/app/core/utils/tab-utils';
import { EvaluateAccessAs, FavoriteTaskType, Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ProfileServiceUtils } from 'src/app/shared/roles/profiles/utils/profile-service-utils';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import { ActivityService, type DeleteDocumentDTO, type DeleteDocumentService } from '../../activities/activities-service/activities.service';
import type { ReportMenuItem } from '../../timesheet/timesheet-add/timesheet-add.interfaces';
import { TimesheetDialogComponent } from '../../timesheet/timesheet-dialog/timesheet-dialog.component';
import { TimesheetService } from '../../timework/services/timesheet.service';
import { WidgetPanelComponent } from '../../widgets/widget-panel/widget-panel.component';
import type { Menu, WindowLabels } from '../menu-definition/menu-definition.interfaces';
import type { RouteLoadEstrategy } from '../menu-definition/menu-definition.type';
import { MenuWarningComponent } from '../menu-warning/menu-warning.component';
import { MenuWidgetComponent } from '../menu-widget/menu-widget.component';
import type { BasicPendingData } from '../menu-widget/menu-widget.interfaces';
import { MenuWidgetService } from '../menu-widget/menu-widget.service';
import { MenuMainUtil } from './menu-main-util';
import { DEFAULT_FRAME_SRC, DOCUMENT_VIEWER_SRC, MENU_MAIN_LANG_CONFIG, PDF_VIEWER_SRC, UNIQUE_HASH_FRAME_PARAMETER, UNIQUE_HASH_FRAME_REGEX } from './menu-main.const';
import { FavoritesTasks } from './menu-main.enum';
import type {
  ActivityTemplateMenuItem,
  AppBarTitle,
  ExternalAppMenuItem,
  FavoriteMenuItem,
  FavoriteTaskSaveDTO,
  FormFillOnlyMenuItem,
  HistoryWindow,
  LegacyConfig,
  MenuBarHidden,
  MenuPositionPan,
  ModuleLabel,
  Param,
  QualifiedGenericMenuItem,
  QualifiedMenuItem
} from './menu-main.interfaces';

declare global {
  interface Window {
    disableBeforeunload: boolean;
  }
}

const PATH_REGEX = /=\$\(.+\)/;
const MENU_REGEX = /.+?menu\//;
const TAB_REGEX = /&threadId=[0-9a-zA-Z]+/;
const TAB_FIRST_REGEX = /\?tabId=[0-9a-zA-Z]+/;
const TARGET_URL_REGEX = /\?.+/;
const NAV_TITLE_REGEX = /[a-z]\.[a-z]/;
const KEY_VALE_REGEX = /^.*\?/;
const PARAM_BUILDER_REGEX = /regExp:(.+?)\)/;
const REMOVE_LANG_REGEX = /.*menu\//;
const MODULE_CHECK_REGEX = /([?&])module=(.*?)(&|$)/;
const REPLACE_VIEW_REGEX = /.+view\//;
const EXACT_MATCH_REGEX = /^".*?"$/;
const CLEAR_WINDOW_URL_REGEX = /.+?menu\/legacy\//;

@Component({
  encapsulation: ViewEncapsulation.None,
  selector: 'app-menu-main',
  templateUrl: './menu-main.component.html',
  styleUrls: ['./menu-main.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    HammerModule,
    DropdownMenuComponent,
    SafeUrlPipe,
    FormsModule,
    IgxNavDrawerTemplateDirective,
    IgxNavigationDrawerComponent,
    IgxNavDrawerItemDirective,
    IgxIconButtonDirective,
    IgxNavDrawerMiniTemplateDirective,
    IgxFlexDirective,
    IgxIconComponent,
    IgxNavbarTitleDirective,
    IgxLayoutDirective,
    IgxRippleDirective,
    IgxToggleActionDirective,
    IgxLabelDirective,
    IgxPrefixDirective,
    IgxInputGroupComponent,
    IgxNavbarComponent,
    IgxBadgeComponent,
    IgxAvatarComponent,
    IgxDialogComponent,
    IgxSuffixDirective,
    IgxDividerDirective,
    IgxInputDirective,
    ReactiveFormsModule,
    MenuWidgetComponent,
    RouterModule,
    FabButtonMenuComponent,
    TimesheetDialogComponent,
    BnextTranslateModule,
    MenuWarningComponent,
    AiChatButtonComponent
  ]
})
export class MenuMainComponent extends BnextCoreComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  private titleService = inject(Title);
  private draggingZoneReleaseService = inject(DraggingZoneReleaseService);
  private noticeService = inject(NoticeService);
  private changeDetector = inject(ChangeDetectorRef);
  private router = inject(Router);
  private location = inject(Location);
  private api = inject(AppService);
  private authService = inject(AuthService);

  private datePipe = inject(DatePipe);
  private render = inject(Renderer2);
  private login = inject(LoginService);
  private loginExpiredService = inject(LoginExpiredService);
  private menuWidgetService = inject(MenuWidgetService);
  private timesheetService = inject(TimesheetService);
  private activityService = inject(ActivityService);
  private activatedRoute = inject(ActivatedRoute);

  #changedOpenDrawer = new Subject<boolean>();
  #openedDrawer = false;
  #pendingsLoading = false;

  public override get componentPath(): string {
    return 'modules.menu';
  }

  private get baseFrameSrc(): string {
    if (this.currentWindow?.legacy) {
      return this.currentWindow.frameSrc;
    }
    return DEFAULT_FRAME_SRC;
  }

  protected get documentViewerSrc(): string {
    return DOCUMENT_VIEWER_SRC;
  }

  #routeReload: DataMap<RouteLoadEstrategy> = {};
  protected showPendings = false;
  #basefrm: ElementRef;
  #basefrmLoadedEvent = false;
  #menuLoadedEvent = false;
  #documentViewer: ElementRef;
  #documentViewerLoadedEvent = false;
  #modernContainerScrollTicking = false;

  protected shouldRefreshPending = true;
  protected showWarningBanner = false;
  protected warningBannerId: LocalStorageItem = null;
  private isPopState = false;
  private parameterBuilderMenu: Menu = null;
  protected parameterBuilderArray: Param[] = [];
  protected parameterBuilderTitle: string;
  protected parameterBuilderRightButton: string;
  protected parameterBuilderLeftButton: string;
  protected currentWindow: HistoryWindow;
  protected currentWindowCss: string;
  protected navItems: Menu[] = [];
  private showNavItems: Menu[] = [];
  private unauthorizedItems: Menu[] = [];
  private currentMenu: Menu;
  private selectedItem: Menu[] = [];
  private parentItem: Menu;
  protected drawerState: {
    miniTemplate: boolean;
    open: boolean;
    pin: boolean;
    pinThreshold: number;
  } = {
    miniTemplate: true,
    open: false,
    pin: true,
    pinThreshold: 0
  };
  protected homeUrl = this.getHomeUrl();
  protected searchMenu = '';
  private breadcrumbs = '';
  private module = '';
  protected avatarSha512 = '-';
  protected pendingCount: number = null;
  protected appBarTitle: string = null;
  protected appBarSubTitle: string = null;
  // Propiedad de mobil solamente, da prioridad a appBarTitle en la barra superior
  protected isModuleTitle: boolean = null;
  protected userInitials: string;
  protected userName: string;
  protected userMail: string;
  protected legacyActive: LegacyConfig = {
    show: false,
    hasDocumentViewer: false,
    shownDocumentViewer: false,
    documentViewerClosing: false
  };
  protected menuBarHidden: MenuBarHidden = {
    viewfinder: false,
    activeDirectoryAdd: false,
    profile: false,
    help: false,
    signOut: false
  };
  private touchDevice = this.isTouchDevice;
  protected mobileShowHelper = false;
  protected settingsModule = false;
  protected navNavigationRail = false;
  protected navItemsRail: Menu[] = [];
  protected navItemsMini: Menu[] = [];
  protected selectedItemRail: Menu;
  private dialogMessage = '';
  protected isPendingsAvailable = true;
  protected about: AboutAppEntity;
  protected config: ConfigAppEntity;
  protected showContextualMenu: boolean;
  private favoriteItemSelected: Menu;
  protected favoriteUrl: string;
  protected favoriteActivity: string;
  protected favoriteDescription: string;
  private favoriteReportId: number;
  protected codeToSearch: string =
    LocalStorageSession.getValue(LocalStorageItem.SEARCH_MODULE_TEXT) !== null ? LocalStorageSession.getValue(LocalStorageItem.SEARCH_MODULE_TEXT) : '';

  // Fab Menu
  protected fabMenuIsAvailable = false;
  protected paramBuilderDialogOpen = false;
  protected igxFavoriteUpdateOpen = false;
  protected avatarDialogOpen = false;
  protected hideHeader = false;
  protected hideSideMenu = false;
  protected fabMenuFloat = false;
  protected fabMenuItems: FabButtonMenuItem[] = [];
  protected fabMenuItemsAvailable: (string | number)[] = [];
  protected fabShowBackAvailable = false;
  aiEnabled = signal(false);
  private itemBack: DropdownMenuItem = {
    text: CommonAction.BACK,
    value: CommonAction.BACK,
    iconName: 'arrow_back'
  };
  protected currentTimesheetWidgetDate: Date = new Date();

  protected moduleSearchItems = [
    { text: 'Seleccionar módulo', value: 'default-option' },
    { text: 'Actividades', value: Module.ACTIVITY },
    { text: 'Actividades de auditorías', value: Module.AUDIT },
    { text: 'Actividades de hallazgos', value: Module.ACTION },
    { text: 'Actividades de formularios', value: Module.FORMULARIE }
  ];

  private validServices = ProfileServiceUtils.getAllServices();

  protected hasAccessTimesheet = false;

  private subscriptionsOnDestroy: Subscription[] = [];
  private menuApiSubscription: Subscription;
  readonly paramBuilderDialog = viewChild<IgxDialogComponent>('paramBuilderDialog');
  readonly mainContent = viewChild<ElementRef>('mainContent');
  readonly drawer = viewChild(IgxNavigationDrawerComponent);
  readonly drawerRail = viewChild<IgxNavigationDrawerComponent>('drawerRail');

  // TODO: Skipped for migration because:
  //  Accessor queries cannot be migrated as they are too complex.
  @ViewChild('basefrm')
  private set basefrm(_basefrm: ElementRef) {
    this.#basefrm = _basefrm;
    if (_basefrm && !this.#basefrmLoadedEvent) {
      this.bindLegacyFrameEvents(this.#basefrm);
      this.#basefrmLoadedEvent = true;
    } else if (!_basefrm) {
      this.#basefrmLoadedEvent = false;
    }
  }

  private get basefrm(): ElementRef {
    return this.#basefrm;
  }

  protected override watchResizeEvents = true;
  readonly search = viewChild<ElementRef>('search');

  readonly entry = viewChild<ElementRef>('entry');

  readonly contextualMenu = viewChild<ElementRef>('contextualMenu');

  readonly igxFavoriteUpdate = viewChild<IgxDialogComponent>('igxFavoriteUpdate');

  readonly selectModuleSearch = viewChild<ElementRef>('selectModuleSearch');

  // TODO: Skipped for migration because:
  //  Accessor queries cannot be migrated as they are too complex.
  @ViewChild('documentViewer')
  private set documentViewer(value: ElementRef) {
    this.#documentViewer = value;
    if (value && !this.#documentViewerLoadedEvent) {
      this.#documentViewer.nativeElement.addEventListener('load', this.onLoadDocumentViewer.bind(this));
      this.#documentViewer.nativeElement.addEventListener('error', this.onErrorDocumentViewer.bind(this));
      this.#documentViewerLoadedEvent = true;
    }
  }

  private get documentViewer(): ElementRef {
    return this.#documentViewer;
  }

  readonly avatarDialog = viewChild<IgxDialogComponent>('avatarDialog');

  // TODO: Skipped for migration because:
  //  Accessor queries cannot be migrated as they are too complex.
  @ViewChild('mainAppBar', { read: ElementRef })
  public set mainAppBar(value: ElementRef) {
    this.menuService.setMainAppBar(value, this.isScreenLarge);
  }

  private TASK_MENU = {
    key: '',
    path: 'menu/legacy',
    icon: '',
    link: '',
    text: 'Tasks',
    level: 0,
    elevation: 0,
    access: new Set([]),
    breadcrumbs: '',
    breadcrumbsKey: '',
    module: Module.NONE,
    open: false,
    show: false,
    isFavorite: false
  };

  private settingsLabelForShow = {
    CONFIGURATION: true,
    ORGANIZATION: true,
    USERS: true,
    CATALOGS: true,
    ITEM_FOR_RENDER: ''
  };

  public constructor() {
    super();

    this.loader.show();
    this.currentMenu = this.TASK_MENU;
    this.router.routeReuseStrategy.shouldReuseRoute = (future, curr) => this.shouldReuseRoute(future, curr);
    this.router.events.pipe(takeUntil(this.$destroy)).subscribe((evt) => {
      this.shouldReload(evt);
      if (evt instanceof NavigationEnd) {
        this.setTitleFromUrl(evt.url);
      }
      if (evt instanceof NavigationStart && evt.navigationTrigger === 'popstate') {
        this.setTitleFromUrl(evt.url, true);
      }
    });
  }

  private setTitleFromUrl(url: string, browserStatePop = false): void {
    if ((!url || this.isUrlAlreadyLoaded(url)) && !browserStatePop) {
      return;
    }
    let item = this.navItems.find((item: Menu) => {
      if (item.path || item.link) {
        return url.indexOf(item.link || item.path) !== -1;
      }
      return false;
    });
    if (item) {
      this.menuService.changeTitle(this.getNavTitle(item));
    } else {
      const windowLabel = this.getWindowLabelFromUrl(url);
      const moduleLbl = this.getModuleLabel(windowLabel);
      item = this.basicLegacyMenu(url, url, moduleLbl.label || '...', moduleLbl.module);
      this.menuService.changeTitle(this.getNavTitle(item));
    }
  }

  private shouldReuseRoute(future: ActivatedRouteSnapshot, curr: ActivatedRouteSnapshot): boolean {
    const currentPath = this.getCurrentRoutePath(curr);
    if (this.location.path().endsWith(currentPath) && this.getRouteLoad(currentPath) === 'reload') {
      return false;
    }
    const paramsMatch: boolean = equalsObject(curr.params, future.params);
    const queryParamsMatch: boolean = equalsObject(curr.queryParams, future.queryParams);
    const routeMatch: boolean = equalsObject(curr.routeConfig, future.routeConfig);
    return paramsMatch && routeMatch && queryParamsMatch;
  }

  private shouldReload(
    evt:
      | NavigationStart
      | NavigationEnd
      | NavigationCancel
      | NavigationError
      | RoutesRecognized
      | GuardsCheckStart
      | GuardsCheckEnd
      | RouteConfigLoadStart
      | RouteConfigLoadEnd
      | ChildActivationStart
      | ChildActivationEnd
      | ActivationStart
      | ActivationEnd
      | Scroll
      | ResolveStart
      | ResolveEnd
      | NavigationSkipped
  ) {
    if (evt instanceof NavigationEnd) {
      this.router.navigated = false;
      const afterUrl = evt.urlAfterRedirects.replace(MENU_REGEX, '');
      if (
        evt.id === 1 // <-- El ID se inicia en 1 (ventana recién abierta) y cada vez que navegas se incrementa +1
      ) {
        this.setRouteLoad(afterUrl, 'no-reload');
      }
    } else if (evt instanceof NavigationStart && evt.url && evt.url.indexOf('/menu/legacy/') !== -1) {
      const view = this.getWindowViewFromUrl(evt.url);
      if (view) {
        this.onLegacyLoaded(view, true, true);
      }
    } else if (evt instanceof NavigationStart && evt.url && evt.url.indexOf('/menu/legacy/') === -1 && this.currentWindow?.legacy) {
      if (this.debug()) {
        console.log(`Requested angular window from legacy iframe ${evt.url}`, evt);
      }
      this.onModernNavigationLoaded();
    }
  }

  private getCurrentRoutePath(snapshot1: ActivatedRouteSnapshot): string {
    let snapshot = snapshot1;
    if (!snapshot || !snapshot.url || !snapshot.url[0] || !snapshot.url[0].path) {
      return null;
    }
    const path = [
      snapshot.url
        .map((v) => v.path)
        .reduce((v, r) => {
          return `${v}/${r}`;
        })
    ];
    while (snapshot.parent?.url?.length) {
      path.push(
        snapshot.parent.url
          .map((v) => v.path)
          .reduce((v, r) => {
            return `${v}/${r}`;
          })
      );
      snapshot = snapshot.parent;
    }
    return path.reverse().reduce((v, r) => {
      return `${v}/${r}`;
    });
  }

  private setRouteLoad(path: string, value: RouteLoadEstrategy): void {
    if (!this.#routeReload) {
      this.#routeReload = {};
    }
    if (value === 'reload') {
      for (const r in this.#routeReload) {
        if (!Object.hasOwn(this.#routeReload, r)) {
          continue;
        }
        this.#routeReload[r] = 'no-reload';
      }
    }
    this.#routeReload[path] = value;
  }

  private getRouteLoad(path: string): RouteLoadEstrategy {
    if (!this.#routeReload) {
      this.#routeReload = {};
    }
    return this.#routeReload[path];
  }

  public override ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.menuApiSubscription) {
      this.menuApiSubscription.unsubscribe();
    }
    if (this.subscriptionsOnDestroy) {
      for (const subs of this.subscriptionsOnDestroy) {
        if (subs) {
          subs.unsubscribe();
        }
      }
    }
    this.changeDetector.detach();
  }

  public override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.translate.get('pending_records').subscribe((lbl) => {
      this.TASK_MENU.text = lbl;
    });
    if (this.isScreenSmall || this.isScreenPhone) {
      this.drawerState.miniTemplate = false;
      this.drawerState.open = true;
      this.drawerState.pin = false;
      this.drawerState.pinThreshold = 5000;
    } else {
      this.drawerState.miniTemplate = true;
      this.drawerState.open = false;
      this.drawerState.pin = true;
      this.drawerState.pinThreshold = 0;
    }
    this.changeDetector.detectChanges();
    this.initializeMenu();
    if (isFeatureAvailable(FeatureFlag.MENU_ENABLE_GESTURES)) {
      this.initializePanEvents();
    }
  }

  private initializePanEvents(): void {
    const drawer = this.drawer();
    drawer.enableGestures = false;
    const position = drawer.isPinnedRight; // '1': right, '0': left
    if (drawer && !this.isScreenLarge && !this.#menuLoadedEvent) {
      console.warn('Overriding Hammer touch events. HammerConfigService not applied to menu gestures.');
      const startPosition: MenuPositionPan = {
        x: 0
      };
      const mainContent = this.mainContent();
      drawer.touchManager.addEventListener(mainContent.nativeElement, 'panstart', (evt) => {
        startPosition.x = position === '1' ? window.innerWidth - (evt.center.x + evt.distance) : evt.center.x - evt.distance;
      });
      drawer.touchManager.addEventListener(mainContent.nativeElement, 'panend', (evt) => {
        const hasChangeVertical = this.hasChangePanVertical(evt);
        if (hasChangeVertical) {
          return;
        }
        const deltaX = position === '1' ? -evt.deltaX : evt.deltaX;
        const visibleWidth: number = startPosition.x + 64 + deltaX;
        // 264: en angular debe recorrerse al menos esa distancia para abrir el menú
        if (visibleWidth >= 450) {
          this.drawer().toggle();
          this.changeDetector.detectChanges();
        }
      });
      this.#menuLoadedEvent = true;
    }
  }
  private hasChangePanVertical(evt: { deltaX: number; deltaY: number }): boolean {
    const y = Math.abs(evt.deltaY);
    const x = Math.abs(evt.deltaX);
    if (y > x) {
      return Math.abs(y - x) > 100;
    }
    return false;
  }

  private initializePanLegacyEvents(): void {
    const drawer = this.drawer();
    if (drawer && !this.isScreenLarge && this.#basefrm.nativeElement.contentDocument) {
      console.warn('Overriding Hammer touch events. HammerConfigService not applied to menu gestures.');
      const document = this.#basefrm.nativeElement.contentDocument.querySelector('.grid-container.grid-floating-active');
      const position = drawer.isPinnedRight; // '1': right, '0': left
      const startPositionLegacy: MenuPositionPan = {
        x: 0
      };
      if (document !== null) {
        drawer.touchManager.addEventListener(document, 'panstart', (evt) => {
          startPositionLegacy.x = position === '1' ? window.innerWidth - (evt.center.x + evt.distance) : evt.center.x - evt.distance;
        });
        drawer.touchManager.addEventListener(document, 'panend', (evt) => {
          const hasChangeVertical = this.hasChangePanVertical(evt);
          if (hasChangeVertical) {
            return;
          }
          const deltaX = position === '1' ? -evt.deltaX : evt.deltaX;
          const visibleWidth: number = startPositionLegacy.x + 64 + deltaX;
          // 300: en legacy debe recorrerse al menos esa distancia para abrir el menú
          if (visibleWidth >= 450) {
            this.drawer().toggle();
            this.changeDetector.detectChanges();
          }
        });
      }
    }
  }

  private refreshUserData(): Promise<boolean> {
    return this.login.fillUserData(true, this.$destroy, true);
  }

  private refreshLoggedUser(clearSession = false, navigate = true): void {
    if (clearSession) {
      Session.clear();
    }
    this.refreshUserData().then(
      () => {
        if (Session.isRenewDataNeeded()) {
          location.replace('../rest/renew-data');
          return;
        }
        if (!Session.getValue()) {
          return;
        }
        if (Session.isAway()) {
          this.showAwayWarning();
        }
        if (!Session.getProvidedScreenResolution()) {
          this.login.submitResolution(this.$destroy).then(
            () => this.refreshUserData(),
            () => {
              // Do nothing
            }
          );
        }
        this.userName = Session.getUserName();
        this.userInitials = nameInitials(this.userName);
        this.avatarSha512 = Session.getUniqueSessionToken();
        this.setTimezone(ConfigApp.getSystemTimezone());
        if (this.isValidUserMail()) {
          this.userMail = Session.getUserMail();
        } else {
          this.userMail = 'Sin correo electrónico';
        }
        this.refreshMenu(true, navigate);
        if (LocalStorageSession.getValue(LocalStorageItem.CURRENT_PATH_USER) !== null && this.navItems.length > 0) {
          this.searchMenu = LocalStorageSession.getValue(LocalStorageItem.CURRENT_PATH_USER);
          this.onFilterChange();
        }
      },
      () => {
        console.error('There is no session');
      }
    );
  }

  private requestLocation(): void {
    if (!Session.getProvidedLocation() && ConfigApp.getTrackLocation()) {
      this.login.requestLocation(this.$destroy).then(
        () => this.refreshUserData(),
        () => {
          // Do nothing
        }
      );
    }
  }

  private showBannerWarning(warningBannerId: LocalStorageItem): void {
    if (this.showWarningBanner) {
      return;
    }
    this.warningBannerId = warningBannerId;
    this.showWarningBanner = true;
    this.cdr.detectChanges();
  }

  protected onClosedWarningBanner(): void {
    if (!this.showWarningBanner) {
      return;
    }
    this.warningBannerId = null;
    this.showWarningBanner = false;
    this.cdr.detectChanges();
  }

  private showAwayWarning(): void {
    const warningId = LocalStorageItem.BANNER_AWAY_WARNING_READED;
    LocalStorageSession.setValue(warningId, false);
    this.showWarning(warningId);
  }

  private showWarning(warningId: LocalStorageItem) {
    if (LocalStorageSession.getValue(warningId) === null || LocalStorageSession.getValue(warningId) === 'false') {
      this.showBannerWarning(warningId);
    }
  }

  private removeQuickAccess(favoriteTaskIds: number[]): void {
    this.api
      .post({
        url: 'access/delete-my-links',
        cancelableReq: this.$destroy,
        postBody: favoriteTaskIds
      })
      .subscribe({
        next: () => {
          this.noticeService.notice('Se eliminó el acceso directo');
          this.refreshMenu(false);
        },
        error: (error) => {
          throw error;
        }
      });
  }

  private addQuickAccess(item: QualifiedGenericMenuItem): void {
    const urlParams = item.urlParams || '';
    const solveDef = (result: FavoriteTaskSaveDTO) => {
      if (item.def) {
        item.def.resolve(result);
      }
    };
    if (urlParams.length > 4000) {
      solveDef({ id: null, success: false });
      this.dialogService.info('El contenido es demasiado largo, intente reducirlo y trate de nuevo');
      return;
    }
    if (item.isSystemGenerated) {
      this.persistQuickAccess(item, item.systemGeneratedName).then(
        (result) => {
          solveDef(result);
        },
        (result) => {
          solveDef(result);
        }
      );
    } else {
      this.dialogService
        .input(
          {
            inputMessage: 'root.common.message.capture-name-shortcut',
            inputMaxLimit: 30,
            inputValue: item.taskName?.substring(0, 30) || null,
            message: item.infoMessage || 'root.common.message.shortcut-info-message',
            maximizeAvailable: false
          },
          'root.common.button.ok',
          'root.common.button.cancel',
          'root.common.message.add-shortcut'
        )
        .then(
          (dialogResult: DialogResult) => {
            const favorite = this.navItems.filter(
              (navItem: Menu) =>
                navItem.isFavorite &&
                navItem.text === dialogResult.inputValue &&
                (navItem.path === item.urlParams || navItem.path === `${item.menuPath}/${item.reportId}`)
            );
            if (favorite.length > 0) {
              this.translateService.get('root.common.message.shortcut-duplicated-message').subscribe((duplicated) => {
                this.noticeService.notice(duplicated, false);
              });
            } else {
              this.persistQuickAccess(item, dialogResult.inputValue).then(
                (result) => {
                  this.translateService.get('root.common.message.shortcut-saved-message').subscribe((saved) => {
                    this.noticeService.notice(saved, false);
                  });
                  this.refreshMenu(false);
                  solveDef(result);
                },
                (result) => {
                  solveDef(result);
                }
              );
            }
          },
          () => {
            solveDef({ id: null, success: false });
          }
        );
    }
  }

  private persistQuickAccess(item: QualifiedGenericMenuItem, name: string): Promise<FavoriteTaskSaveDTO> {
    let modulename = 'none';
    // Se agrega esta validación porque se pierde el valor de module cuando caduca la sesión o se borra caché
    if (this.module != null) {
      modulename = this.module.split('.').pop();
    }
    const urlParams = item.urlParams || '';
    const favorite: QualifiedGenericMenuItem = {
      activityId: item.activityId || null,
      documentId: item.documentId || null,
      documentMasterId: item.documentMasterId || null,
      href: item.href || null,
      isSystemGenerated: item.isSystemGenerated || false,
      icon: item.icon || MODULE_ICON[this.module] || 'link',
      menuPath: item.menuPath || null,
      moduleName: this.moduleName(item.moduleName || modulename) || null,
      name: name,
      params: item.params,
      surveyId: item.surveyId || null,
      taskName: item.taskName || null,
      templateActivityTemplateId: item.templateActivityTemplateId || null,
      type: item.type || FavoriteTaskType.MENU_PATH,
      urlParams: urlParams,
      reportId: item.reportId
    };
    if (!favorite.menuPath && favorite.type === FavoriteTaskType.MENU_PATH) {
      favorite.menuPath = this.navLang.currentPath(false);
    }
    this.translateService.get('root.common.message.saving').subscribe((saving) => {
      this.noticeService.notice(saving, false);
    });
    return new Promise<FavoriteTaskSaveDTO>((resolve) => {
      const s = this.api.post({ url: 'access/favorite/add/menu-path', cancelableReq: this.$destroy, postBody: favorite, options: null, handleFailure: false }).subscribe(
        (result) => {
          resolve(result);
          s.unsubscribe();
        },
        (error) => {
          ErrorHandling.notifyError(error, this.navLang);
          resolve({ id: null, success: false });
          s.unsubscribe();
        }
      );
    });
  }

  private refreshMenu(loadView: boolean, navigate = true): void {
    this.showNavItems = this.navItems.filter((item: Menu) => item.show === true);
    this.navItems = [];
    this.unauthorizedItems = [];
    this.initializeView(loadView, navigate);
  }

  protected dragEnter(event: Event) {
    if (!this.draggingZoneReleaseService.containsFile(event)) {
      return;
    }
    this.draggingZoneReleaseService.show();
  }

  private initializeMenu(): void {
    this.navItems = [];
    this.unauthorizedItems = [];
  }

  protected dragLeave(event: DragEvent) {
    if (event.clientX === 0 && event.clientY === 0) {
      this.draggingZoneReleaseService.release();
    }
  }

  private subscribeServices() {
    this.menuService.refreshMenuAccess.pipe(takeUntil(this.$destroy)).subscribe((loadView) => {
      this.refreshMenu(loadView, false);
    });
    this.draggingZoneReleaseService.draggingZone.subscribe((options: DraggingZoneOptions) => {
      if (options.dropZoneVisible) {
        this.render.addClass(this.mainContent().nativeElement, 'dragging-zone');
      } else {
        this.render.removeClass(this.mainContent().nativeElement, 'dragging-zone');
      }
    });
    this.menuService.changeAppbarTitle.pipe(takeUntil(this.$destroy)).subscribe((title: string | AppBarTitle) => {
      if (this.debug()) {
        console.log('changeTitle: ', title);
      }
      if (!title) {
        if (this.appBarTitle !== null && typeof this.appBarTitle !== 'undefined') {
          this.appBarTitle = null;
          this.appBarSubTitle = null;
          this.isModuleTitle = false;
          this.changeDetector.detectChanges();
        }
      } else if (typeof title === 'string') {
        if (this.appBarTitle !== title) {
          this.appBarTitle = title;
          this.appBarSubTitle = null;
          this.isModuleTitle = false;
          this.changeDetector.detectChanges();
        }
      } else if ('title' in title) {
        if (this.appBarTitle !== title.title || this.appBarSubTitle !== title.subtitle) {
          this.appBarTitle = title.title;
          this.appBarSubTitle = title.subtitle || '';
          this.isModuleTitle = title.isModuleTitle;
          this.changeDetector.detectChanges();
        }
      }
    });
    this.menuService.showPendings.pipe(takeUntil(this.$destroy)).subscribe((pending: BasicPendingData) => {
      this.displayFullTask(false, pending);
    });
    this.menuService.changeAppbarPendingCount.pipe(takeUntil(this.$destroy)).subscribe((count) => {
      if (count > this.pendingCount) {
        this.menuWidgetService.refresh(true, null);
      }
      this.pendingCount = count;
      this.changeDetector.detectChanges();
    });
    this.menuService.closeWindow.pipe(takeUntil(this.$destroy)).subscribe((close) => {
      if (close) {
        this.closeCurrentWindow();
      }
    });
    this.menuService.documentViewerUrl.pipe(takeUntil(this.$destroy)).subscribe((value) => {
      if (value) {
        this.showDocumentViewer(value);
      } else {
        this.hideDocumentViewer();
      }
    });
    this.menuService.quickAccess.pipe(takeUntil(this.$destroy)).subscribe((item: QualifiedGenericMenuItem) => {
      this.addQuickAccess(item);
    });
    this.menuService.quickAccessRemove.pipe(takeUntil(this.$destroy)).subscribe((favoriteTaskIds: number[]) => {
      return this.removeQuickAccess(favoriteTaskIds);
    });
    this.menuService.refreshUserData.pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.refreshLoggedUser(false, false);
    });
    this.menuService.changeWindow.pipe(takeUntil(this.$destroy)).subscribe((item) => {
      if (this.debug()) {
        console.log('changeWindow requested: ', item);
      }
      if (item === null || item.path === null) {
        return;
      }
      if (item.key === 'pendings') {
        this.displayFullTask();
        return;
      }
      let menu: Menu;
      if (this.navItems) {
        menu = this.navItems.find((element) => {
          if (element.path === 'menu/legacy') {
            return element.link === item.link;
          }
          return element.path === item.path;
        });
      }
      if (menu) {
        this.navigateMenu(menu, null, item.forceReload);
      } else {
        if (item.link) {
          const windowLabel = this.getWindowLabelFromLink(item.link);
          if (windowLabel === PDF_VIEWER_SRC) {
            this.setFullScreen();
          }
          if (!item.text) {
            item.text = this.translate.instant(`views.${windowLabel}`) || windowLabel;
          }
        } else if (item.path) {
          if (!item.text) {
            const keyTitle = item.path.replace(/\//g, '.');
            item.text = this.translate.instant(keyTitle) || null;
          }
        }
        this.navigateMenu(item, null, item.forceReload);
      }
    });
    this.menuService.addFabMenu.pipe(takeUntil(this.$destroy)).subscribe((items: FabButtonMenuItem[] | FabButtonConfig) => {
      return this.fabMenuOptions(items);
    });
    this.menuService.toggleDisplayFabMenu.pipe(takeUntil(this.$destroy)).subscribe((display: boolean) => {
      this.fabMenuIsAvailable = display;
      this.cdr.detectChanges();
    });
    this.activityService.deleteDocument.pipe(takeUntil(this.$destroy)).subscribe((data: DeleteDocumentService) => {
      this.deleteDocumentActivity(data);
    });
    this.menuWidgetService.pendingsLoading.pipe(takeUntil(this.$destroy)).subscribe((loading) => {
      this.#pendingsLoading = loading;
    });
    this.timesheetService.onTimesheetWidgetDateChange.pipe(takeUntil(this.$destroy)).subscribe((date: Date) => {
      this.currentTimesheetWidgetDate = date;
    });
  }

  private isValidUserMail(): boolean {
    return Session.isValidMail() || (Session.getUserMail() && Session.getUserMail().indexOf('@dummy') !== -1);
  }

  private displayFullTask(refreshPendings = true, pending?: BasicPendingData) {
    const iframe = MenuMainUtil.getLegacyFrame();
    if (this.legacyActive.show && iframe) {
      this.displayFullTasksAndEvaluteCanExit(refreshPendings, iframe, pending);
      return;
    }
    this.continueDisplayFullTask(refreshPendings, pending);
  }

  private continueDisplayFullTask(refreshPendings = true, pending?: BasicPendingData): void {
    this.showTasks(refreshPendings, pending);
    this.currentWindow = null;
    this.currentWindowCss = null;
    this.legacyActive.show = false;
    this.clearLegacyUrl();
    this.#modernContainerScrollTicking = false;
    this.fabShowBackAvailable = false;
    this.hideDocumentViewer();
    this.fixUrlLocation('');
    this.loader.hide();
    this.menuService.addFabOptionsMenu([]);
  }

  private displayFullTasksAndEvaluteCanExit(refreshPendings, iframe, pending?: BasicPendingData): void {
    if (iframe === null) {
      this.continueDisplayFullTask(refreshPendings, pending);
      return;
    }
    if (MenuMainUtil.hasIframeWithConfig(iframe)) {
      MenuMainUtil.evaluateCanDeactivate(iframe, this.translate, this.dialogService).then(
        () => {
          console.warn('[canDeactivate] - Display full task, The user continue with the navigation');
          MenuMainUtil.resetCanDeactivate(iframe);
          this.continueDisplayFullTask(refreshPendings, pending);
        },
        () => {
          console.warn('[canDeactivate] - The user canceled the navigation');
        }
      );
      return;
    }
    this.continueDisplayFullTask(refreshPendings, pending);
  }

  private onLegacyLoaded(view: string, frameLoaded = false, forceReload = false) {
    const url = `view/${view}`;
    if (view === 'v.pendings.view') {
      this.displayFullTask();
      return;
    }
    if (view === DOCUMENT_VIEWER_SRC || view === PDF_VIEWER_SRC) {
      this.setFullScreen();
    }
    this.hideTasks(true);
    let cleanUrl = url;
    if (cleanUrl.indexOf('?') !== -1) {
      cleanUrl = cleanUrl.substring(0, cleanUrl.indexOf('?'));
    }
    const menu = this.navItems.find((item: Menu) => {
      if (item.link?.includes('?')) {
        item.link = item.link.substring(0, item.link.indexOf('?'));
      }
      return item.link === cleanUrl;
    });
    let search = this.clearUniqueParameter(location.search);
    if (search && search.indexOf('tabId=') !== -1) {
      const tabId = TabUtils.getTabId();
      if (search.indexOf('tabId=null')) {
        search = search.replace(TAB_REGEX, '').replace('tabId=null', `&tabId=${tabId}`);
      } else {
        search = search.replace(TAB_REGEX, '').replace(TAB_FIRST_REGEX, `&tabId=${tabId}`);
      }
    }
    if (menu) {
      const m = { ...menu };
      if (search.length > 0) {
        m.link += search;
      }
      this.navigateMenu(m, frameLoaded, forceReload);
    } else {
      const moduleLbl = this.getModuleLabel(view);
      if (url.indexOf('?') !== -1) {
        this.navigateMenu(this.basicLegacyMenu(view, url, moduleLbl.label || '...', moduleLbl.module), frameLoaded, forceReload);
      } else {
        this.navigateMenu(this.basicLegacyMenu(view, url + search, moduleLbl.label || '...', moduleLbl.module), frameLoaded, forceReload);
      }
    }
  }

  private setFullScreen() {
    const mainBody = window.top.window.document.body;
    if (mainBody && !mainBody.classList.contains('full-screen')) {
      mainBody.classList.add('full-screen');
      dispatchResize();
    }
  }

  // ToDo: Este metodo hace muchas subscripciones cada vez que se navega entre pantallas, probablemente ocasionen un memory-leak, cambiar a promesas
  private initializeView(loadView: boolean, navigate = true) {
    this.subscriptionsOnDestroy.push(
      this.menuLoad().subscribe(() => {
        if (!loadView) {
          for (const showItem of this.showNavItems) {
            const menu = this.navItems.find((item: Menu) => item.breadcrumbs === showItem.breadcrumbs || (showItem.isFavorite && item.isFavorite && item.show === false));
            if (menu) {
              menu.show = true;
              menu.open = showItem.open;
            }
          }
          this.changeDetector.detectChanges();
          return;
        }
        this.subscriptionsOnDestroy.push(
          this.navLang.getRouteParam('legacy/:view', null, false, this.activatedRoute).subscribe(
            ([view]) => this.onLegacyLoaded(view),
            // MODERN: no regresan "view"
            () => {
              this.api.get({ url: 'api/about', cancelableReq: this.$destroy }).subscribe((about: AboutAppEntity) => {
                if (about.licenceDbType !== 'PRODUCTION' && about.licenceType === 'PRODUCTION') {
                  if (Session.getServices().includes(ProfileServices.ADMON_ACCESOS)) {
                    this.showWarning(LocalStorageItem.BANNER_DB_WARNING_READED);
                  }
                } else if (about.licenceType === 'PRODUCTION' && !about.reachableUrl) {
                  this.showWarning(LocalStorageItem.BANNER_URL_SETTINGS_WARNING_READED);
                }
              });

              if (ConfigApp.hasValue()) {
                this.config = ConfigApp.getValue();
              } else {
                const thiryDaysAgo = DateUtil.add(new Date(), 'day', -30);
                this.config = {
                  enableTimework: 0,
                  trackLocation: 0,
                  oidcEnabled: 0,
                  oidcProvider: null,
                  systemId: '-',
                  welcomeMessage: '-',
                  timeLimitToModifyTimesheet: thiryDaysAgo,
                  regionCatalogEnabled: 0,
                  exchangeRateConversorEnabled: 0,
                  zoneCatalogEnabled: 0,
                  formReportMaxColumns: 0,
                  welcomeBgId: 0,
                  enableRegistration: 0,
                  enabledLandingPage: 0,
                  landingPageUrl: null,
                  timeworkUrl: '-',
                  timeworkMails: '-',
                  systemTimezone: '-',
                  enableLoginForm: 1,
                  aiEnabled: false
                };
              }
              this.onModernNavigationLoaded(navigate);
            }
          )
        );
      })
    );
  }

  private onModernNavigationLoaded(navigate = true): void {
    const locationPath = this.router.routerState.snapshot.url.substring(1);
    const path = locationPath.substring(locationPath.indexOf(this.getLang()) + this.getLang().length + 1);
    this.hideTasks();
    if (path.lastIndexOf('/') === -1) {
      // la raíz no se toma en cuenta para generar pestañas
      if (path.includes('welcome') && Session.showWelcome()) {
        this.showWelcome();
      } else if (path.includes('welcome')) {
        const showViewerAtStart = Session.hasService(ProfileServices.DOCUMENT_STARTED_AT_DOCUMENT_VIEWER);
        if (showViewerAtStart && LocalStorageSession.getValue(LocalStorageItem.SHOWED_PREVISUALIZER_DOCUMENT) === null) {
          this.showDocumentViewer();
          LocalStorageSession.setValue(LocalStorageItem.SHOWED_PREVISUALIZER_DOCUMENT, true);
        } else {
          if (navigate && Session.getModules().length === 1 && (this.pendingCount === null || this.pendingCount === 0) && Session.hasAccess(Module.HAPPYORNOT)) {
            const menu = this.getModuleWithHigherPriority();
            this.navigateMenu(menu);
          } else {
            this.showTasks();
          }
        }
      }
      this.hasAvatarProfile();
      return;
    }
    // Se agrega validación para obtener el menú cuando se usó una url para móviles
    let menu = this.navItems.find((item: Menu) => item.path === path || item.mobilePath === path);
    if (!menu) {
      menu = this.unauthorizedItems.find((item: Menu) => item.path === path);
      if (menu) {
        const services = Session.getServices();
        const hasAccessItem = this.hasAccessToItem(menu, services);
        if (!hasAccessItem) {
          console.warn(`Invalid accces to menu with path ${menu.path}`);
          this.displayFullTask();
          return;
        }
      }
    }
    if (!navigate) {
      return;
    }
    if (!menu) {
      console.log(`Missing menu item, using default item. "${path}"`);
      const isLegacy = path?.startsWith('menu/legacy');
      let moduleLbl: ModuleLabel;
      if (isLegacy) {
        moduleLbl = this.getModuleLabel(this.navLang.getCurrentComponentName(path));
      } else {
        moduleLbl = this.getModuleLabel(this.navLang.getCurrentComponentName(path), 'menu');
      }
      menu = this.basicModernMenu(path, path, moduleLbl.label, moduleLbl.module);
    }
    this.navigateMenu(menu);
  }

  private hasAccessToItem(menu: Menu, services: string[]): boolean {
    if (!isModuleAvailable(menu.module)) {
      return false;
    }
    if (!isFeatureAvailable(menu.featureFlag)) {
      return false;
    }
    if (menu.forbidAnonymous && Session.isAnonymous()) {
      return false;
    }
    if (!hasAccess(Array.from(menu.access), services, EvaluateAccessAs.OR, this.validServices)) {
      return false;
    }
    return isItemEnabledByApp(menu.enabledByApp, menu.enabledByAppField, menu.path);
  }

  private hasAvatarProfile() {
    this.api.get({ cancelableReq: this.$destroy, url: 'users/check-if-has-avatar' }).subscribe((response) => {
      if (response > 0) {
        this.showInformationDialogs();
      } else {
        if (LocalStorageSession.getValue(LocalStorageItem.CLOSED_DIALOG_UPDATE_AVATAR) === null && !Session.isAnonymous()) {
          this.avatarDialogOpen = true;
          this.cdr.detectChanges();
          this.avatarDialog().open();
        } else {
          this.showInformationDialogs();
        }
      }
    });
  }

  protected showInformationDialogs() {
    const avatarDialog = this.avatarDialog();
    if (avatarDialog) {
      avatarDialog.close();
    }
    this.avatarDialogOpen = false;
    LocalStorageSession.setValue(LocalStorageItem.CLOSED_DIALOG_UPDATE_AVATAR, 1);
    if (LocalStorageSession.hasValue(LocalStorageItem.FEEDBACK_LOCATION) && LocalStorageSession.getValue(LocalStorageItem.FEEDBACK_LOCATION) === 'true') {
      if (Session.getBrowser() !== null) {
        const title = this.translate.instantFrom(BnextTranslateService.MESSAGE_COMMON, 'geolocation-permission-dennied.title');
        const message = this.translate.instantFrom(BnextTranslateService.MESSAGE_COMMON, `geolocation-permission-dennied.${Session.getBrowser()}`);
        this.dialogService.info(message, 'root.common.button.ok', null, title).then(
          () => LocalStorageSession.setValue(LocalStorageItem.FEEDBACK_LOCATION, false),
          () => LocalStorageSession.setValue(LocalStorageItem.FEEDBACK_LOCATION, false)
        );
      }
    }
  }

  protected goToProfileView() {
    const avatarDialog = this.avatarDialog();
    if (avatarDialog) {
      avatarDialog.close();
    }
    this.avatarDialogOpen = false;
    LocalStorageSession.setValue(LocalStorageItem.CLOSED_DIALOG_UPDATE_AVATAR, 1);
    this.navigate('menu/legacy/v.user.edit.view');
  }

  private getModuleWithHigherPriority(): Menu {
    // Para aplicar la funcionalidad a todos los modulos, distiguir entre modulos con pendientes y sin pendientes
    // Si cuenta con modulos con pendientes ignora y continua normalmente
    // Si cuenta sin modulos sin pendientes verificar prioridad del modulo y luego prioridad de pantallas para redirigir a la adecuada
    const priorityNumber = [];
    for (const m of this.navItems) {
      priorityNumber.push(m.priority);
    }
    const min = Math.min(...priorityNumber);
    return this.navItems.filter((m) => m.priority === min)[0];
  }

  private getModuleLabel(labelKey?: string, i18n = 'views'): ModuleLabel {
    const module: Module = Module[this.navLang.queryParam('module')] || Module.NONE;
    let label = this.navLang.queryParam('text');
    if (!label && labelKey) {
      const key = `${i18n}.${labelKey.replace(new RegExp(`.+?/${this.getLang()}(/|$)`), '')}`;
      this.translate.get(key).subscribe((text) => {
        label = text || `Missing key "${key}"`;
      });
    }
    return {
      module: module,
      label: label
    };
  }

  public override ngOnInit(): void {
    super.ngOnInit();
    if (AboutApp.hasValue()) {
      this.about = AboutApp.getValue();
    } else {
      this.about = {
        buildVersion: '-',
        licenceType: '-',
        licences: '-',
        licences_exed: '-',
        licences_used: '-',
        licenceLanguage: '-',
        buildNumber: '-',
        buildDate: '-',
        revisionDate: '-',
        buildProfile: '-',
        licenceDbType: '-',
        reachableUrl: false
      };
    }
    Session.getLazySession()
      .pipe(takeUntil(this.$destroy))
      .subscribe(() => {
        this.homeUrl = this.getHomeUrl();
        this.hasAccessTimesheet = Session.hasAccess(Module.TIMESHEET);
      });
    this.login.loginAvailable(this.$destroy).then(
      (status) => {
        const services = Session.getServices();
        this.aiEnabled.set(ConfigApp.getValue().aiEnabled);
        this.menuBarHidden.activeDirectoryAdd = status === 'SSO_ONLY' || !hasAccessAdmon(services);
        this.menuBarHidden.signOut = status === 'SSO_ONLY';
        this.refreshLoggedUser(true);
      },
      () => {
        this.menuBarHidden.activeDirectoryAdd = false;
        this.menuBarHidden.signOut = true;
        this.refreshLoggedUser(true);
      }
    );
    this.subscribeServices();
    this.executeOpenedDrawer();
    const changedIndexSub = this.#changedOpenDrawer.pipe(throttleTime(500, undefined, { leading: false, trailing: true })).subscribe(() => this.executeOpenedDrawer());
    this.subs.push(changedIndexSub);
    MenuMainLocalConfig.getValue(this.isDesktop).then(
      (value) => {
        if (value.openDrwawer) {
          this.toggleAction();
        }
      },
      (error) => console.error('Could not load menu main local config.', error)
    );
    this.#menuLoadedEvent = false;
    for (const item of this.moduleSearchItems) {
      this.translateService
        .get(`${item.value as string}`)
        .pipe(takeUntil(this.$destroy))
        .subscribe((lbl) => {
          item.text = lbl || item.text;
        });
    }
  }

  public ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.showPendings) {
      if (this.showPendings) {
        this.refreshPendingCount();
      }
    }
  }

  private refreshPendingCount() {
    this.api.get({ cancelableReq: this.$destroy, url: 'pendings/count' }).subscribe((count: number) => {
      this.menuService.changePendingCount(count);
    });
  }

  private getHomeUrl(): string {
    return `${RestApiModule.app()}qms/${this.getLang()}/welcome?ngsw-bypass=1`;
  }

  private hideWelcome(): void {
    this.api.get({ cancelableReq: this.$destroy, url: 'users/read-welcome' }).subscribe(() => Session.hideWelcome());
  }

  private showWelcome() {
    this.titleService.setTitle('Bnext QMS - Bienvenido');
    this.subscriptionsOnDestroy.push(
      this.translate.get('welcome-title').subscribe((title) => {
        let firstName = Session.getUserName().trim();
        if (firstName.indexOf(' ') > 0) {
          firstName = firstName.substring(0, firstName.indexOf(' '));
        }
        const welcomeMessage = ConfigApp.getWelcomeMessage();
        const showViewerAtStart = Session.hasService(ProfileServices.DOCUMENT_STARTED_AT_DOCUMENT_VIEWER);
        if (welcomeMessage === null || typeof welcomeMessage === 'undefined' || welcomeMessage.trim() === '') {
          this.hideWelcome();
          if (showViewerAtStart) {
            this.showDocumentViewer();
          } else {
            this.showTasks();
          }
          return;
        }
        this.dialogService.frame(welcomeMessage, this.translate.instant('acknowledge'), null, `${title}, ${firstName}`).then(() => {
          this.hideWelcome();
          if (showViewerAtStart) {
            this.showDocumentViewer();
          } else {
            this.showTasks();
          }
        });
      })
    );
  }

  private getLegacyFrameUrl(path: string): string {
    if (path.indexOf('/view/') !== -1) {
      return path.substring(path.indexOf('/view/') + 6);
    }
    if (path.indexOf('/DPMS/') !== -1) {
      return path.substring(path.indexOf('/DPMS/') + 6);
    }
    return path;
  }

  private onLoadLegacyFailed(item: HistoryWindow) {
    if (item?.menu) {
      const currentPath = item.frameSrc;
      item.frameSrc = DEFAULT_FRAME_SRC;
      this.loginExpiredService.askCredentials().then(() => {
        item.frameSrc = currentPath;
        this.changeWindowAction(item, true);
        this.changeDetector.detectChanges();
      });
    } else {
      this.showError404(item?.key);
    }
    this.loader.hide();
    this.changeDetector.detectChanges();
  }

  private showError404(url: string) {
    const itemMenu = this.basicModernMenu('menu/error-404', `menu/error-404/${url}`, 'Error 404', Module.NONE);
    this.navigateMenu(itemMenu);
  }

  private onLoadBaseFrm(e) {
    if (this.debug()) {
      console.log('BaseFrm loaded', e);
    }
    const location = e.target.contentWindow.location;
    const path = this.clearUniqueParameter(location.href.replace(/&amp;/g, '&'));
    const loaded = this.isBaseFromAlreadyLoaded(path, true);
    if (path !== 'about:blank' && loaded) {
      if (isFeatureAvailable(FeatureFlag.MENU_ENABLE_GESTURES)) {
        this.initializePanLegacyEvents();
      }
    }
    if (path === 'about:blank' || loaded) {
      return;
    }
    let search = this.clearUniqueParameter(location.search);
    if (search.length === 0 || path.indexOf('?') !== -1) {
      search = '';
    }
    const targetUrl = this.getLegacyFrameUrl(path);
    const windowLabel = targetUrl.replace(TARGET_URL_REGEX, '');
    if (windowLabel === PDF_VIEWER_SRC) {
      this.setFullScreen();
    }
    const view = targetUrl.indexOf('?') === -1 ? targetUrl + search : targetUrl;
    if (path === DEFAULT_FRAME_SRC || (this.currentWindow && this.currentWindow.key === view)) {
      return;
    }
    if (view.indexOf('/qms/') !== -1 || view.indexOf('/qms/login') !== -1) {
      this.onLoadLegacyFailed(this.currentWindow);
      return;
    }
    if (stringEndsWith(path, 'v.pendings.view')) {
      this.displayFullTask();
      return;
    }
    this.hideTasks(true);
    if (stringEndsWith(path, 'v-footer-close.view')) {
      this.legacyActive.show = false;
      this.clearLegacyUrl();
      this.#modernContainerScrollTicking = false;
      this.hideDocumentViewer();
      this.currentWindow = null;
      this.currentWindowCss = null;
      if (this.currentMenu && this.currentMenu.path === 'menu/legacy') {
        setTimeout(() => {
          // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
          const target = window.frames['basefrm'];
          const doc = target.contentDocument || target.contentWindow?.document || target.document;
          const win = target.contentWindow || doc.defaultView || doc.parentWindow;
          if (typeof win.reloadFooters === 'function') {
            win.reloadFooters();
          }
        }, 100);
      }
      return;
    }
  }

  private onErrorBaseFrm(e) {
    console.error(`Failed to load ${this.baseFrameSrc}.`, e);
    this.onLoadLegacyFailed(this.currentWindow);
  }

  private onLoadDocumentViewer(e) {
    const location = e.target.contentWindow.location;
    const path = this.clearUniqueParameter(location.href.replace(/&amp;/g, '&'));
    if (path === 'about:blank') {
      return;
    }
    const targetUrl = this.getLegacyFrameUrl(path);
    if (targetUrl.indexOf('/qms/login') !== -1) {
      this.clearGlobalViewFinder();
      this.legacyActive.hasDocumentViewer = false;
      this.loginExpiredService.askCredentials().then(() => {
        this.legacyActive.hasDocumentViewer = true;
      });
    } else if (targetUrl.indexOf('/qms/') !== -1) {
      console.error(`Failed to load ${DOCUMENT_VIEWER_SRC}.`);
      if (this.currentWindow) {
        this.onLoadLegacyFailed(this.currentWindow);
      } else {
        this.dialogService.error(this.translate.instant('document-viewer-not-access')).then(() => {
          this.navigate('pendings');
        });
      }
    }
  }

  private clearGlobalViewFinder() {
    if (window.top) {
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      window.top['viewfinder'] = null;
    }
  }

  private onErrorDocumentViewer(e) {
    console.error(`Failed to load ${DOCUMENT_VIEWER_SRC}.`, e);
    this.showError404(DOCUMENT_VIEWER_SRC);
  }

  private menuLoad(): Observable<WindowLabels> {
    const menuWindowLabels: WindowLabels = {};
    const menuTagsLoad = this.translate.get('menu');
    const subjectResult = new Subject<WindowLabels>();
    const resultLoad: Observable<WindowLabels> = subjectResult.asObservable();
    const services = Session.getServices();
    const modules = Session.getModules();
    const activityWorkflows = Session.getActivityWorkflows();
    if (services.length === 0 || modules.length === 0) {
      this.dialogService
        .info(`${this.translate.instant('no-position')}`, this.translate.instant('logout'), null, this.translate.instant('root.common.error.messageSystem'))
        .then(() => {
          this.authService.logout().subscribe(() => {
            location.replace(`../qms/${this.getLang()}`);
          });
        });
      this.loader.hide();
    }
    this.subs.push(
      menuTagsLoad.subscribe((menuTags) => {
        if (!hasAccessAdmon(services)) {
          this.menuBarHidden.activeDirectoryAdd = true;
        }
        if (modules.length > 0 || services.length > 0) {
          modules.push('TASK_ONLY');
        }
        this.menuBarHidden.viewfinder = modules.indexOf('DOCUMENT') === -1;
        const tempMenu: DataMap<Set<Menu>> = {};
        const unauthorizedMenu: DataMap<Set<Menu>> = {};
        const moduleKeys = [];
        for (const menu of MAIN) {
          let tags = menuTags;
          if (typeof tags === 'string') {
            tags = {};
          }
          let localModuleKeys: string[] = [menu.key];
          if (menu.key === Module.ACTIVITY) {
            localModuleKeys = buildWorkflowMenu(menu, activityWorkflows);
          }
          for (const key of localModuleKeys) {
            if (EXCLUDED_MODULES.indexOf(key) !== -1) {
              continue;
            }
            const m = Object.assign(cloneObject(menu), { key: key, icon: MODULE_ICON[key] || menu.icon });
            if (modules.indexOf(key) !== -1 || modules.indexOf(m.masterKey) !== -1) {
              if (!tempMenu[key]) {
                tempMenu[key] = new Set([]);
                moduleKeys.push(key);
              }
              if (!unauthorizedMenu[key]) {
                unauthorizedMenu[key] = new Set([]);
              }
              buildMenu(m, menuWindowLabels, services, tags, tempMenu[key], unauthorizedMenu[key], !this.isScreenLarge, this.validServices, key);
            } else {
              if (!unauthorizedMenu[key]) {
                unauthorizedMenu[key] = new Set([]);
              }
              buildMenu(m, menuWindowLabels, services, tags, null, unauthorizedMenu[key], !this.isScreenLarge, this.validServices, key);
            }
          }
        }
        for (const moduleKey in tempMenu) {
          if (!Object.hasOwn(tempMenu, moduleKey)) {
            continue;
          }
          // solo se consideran modulos con al menos 1 opción
          if (tempMenu[moduleKey].size > 1) {
            const array = Array.from(tempMenu[moduleKey]);
            this.navItems = this.navItems.concat(array);
            if (this.settingsLabelForShow[array[0].key] && this.settingsLabelForShow.ITEM_FOR_RENDER === '') {
              this.settingsLabelForShow.ITEM_FOR_RENDER = array[0].key;
              array[0].showSettingsLabel = true;
            }
          } else {
            moduleKeys.splice(moduleKeys.indexOf(moduleKey), 1);
          }
        }
        this.settingsLabelForShow.ITEM_FOR_RENDER = '';
        for (const moduleKey in unauthorizedMenu) {
          if (!Object.hasOwn(unauthorizedMenu, moduleKey)) {
            continue;
          }
          if (unauthorizedMenu[moduleKey].size > 1) {
            this.unauthorizedItems = this.unauthorizedItems.concat(Array.from(unauthorizedMenu[moduleKey]));
          }
        }
        if (moduleKeys.length === 0 || (moduleKeys.length === 1 && moduleKeys[0] === 'TASK_ONLY')) {
          this.menuBarHidden.help = true;
          this.isPendingsAvailable = false;
          this.changeDetector.detectChanges();
        } else {
          this.menuBarHidden.help = false;
          this.isPendingsAvailable = true;
        }
        interface MenuApi {
          api: string;
          index: number;
          level: number;
        }
        const menuApi: MenuApi[] = this.navItems
          .map((item: Menu, idx: number) => {
            return {
              api: item.api,
              index: idx,
              level: item.level
            };
          })
          .filter((item: MenuApi) => typeof item.api === 'string');
        if (menuApi.length > 0) {
          const forks: Observable<QualifiedMenuItem[]>[] = [];
          for (const m of menuApi) {
            forks.push(this.api.get({ cancelableReq: this.$destroy, url: m.api, handleFailure: false }));
          }
          this.menuApiSubscription = forkJoin(forks).subscribe({
            next: (eachForkStatus: QualifiedMenuItem[][]) => {
              // success
              let isEmpty = true;
              for (let idx = 0; idx < menuApi.length; idx++) {
                const api: MenuApi = menuApi[idx];
                if (eachForkStatus[idx]) {
                  const items: QualifiedMenuItem[] = eachForkStatus[idx];
                  isEmpty = isEmpty && items.length === 0;
                  for (let i = 0; i < items.length; i++) {
                    const q: QualifiedMenuItem = items[i];
                    this.navItems.splice(api.index + i, 0, this.menuFrom(q, api.level));
                  }
                } else {
                  console.error(`Could not retrieve "${api.api}" api data.`);
                }
              }
              this.navItems = this.navItems.filter((m: Menu) => {
                if (isEmpty) {
                  return !m.api && m.key !== 'TASK_ONLY';
                }
                return !m.api;
              });
              if (this.menuApiSubscription) {
                this.menuApiSubscription.unsubscribe();
              }
              this.solveMenu(menuWindowLabels, subjectResult);
            },
            error: () => {
              // failure
              if (this.menuApiSubscription) {
                this.menuApiSubscription.unsubscribe();
              }
              this.solveMenu(menuWindowLabels, subjectResult);
            }
          });
        } else {
          this.solveMenu(menuWindowLabels, subjectResult);
        }
      })
    );
    return resultLoad;
  }

  private solveMenu(menuWindowLabels: WindowLabels, subjectResult: Subject<WindowLabels>): void {
    if (this.navItems.length < 20) {
      for (const item of this.navItems) {
        item.show = true;
      }
      this.changeDetector.detectChanges();
    }
    this.navItemsMini = this.navItems.filter((item) => item.level === 0);
    subjectResult.next(menuWindowLabels);
  }

  private menuFrom(qualifiedMenuItem: QualifiedMenuItem, level: number): Menu {
    let menu = null;
    let urlParams = qualifiedMenuItem.urlParams || null;
    const key = `${new Date().getTime()}_qualifiedMenuItem`;
    const moduleName: Module = Module[(qualifiedMenuItem?.moduleName || Module.NONE).toUpperCase()];
    if (qualifiedMenuItem.name) {
      qualifiedMenuItem.name = qualifiedMenuItem.name.replace(/\//g, '\\');
    }
    switch (qualifiedMenuItem.type) {
      case FavoriteTaskType.FORM: {
        const documentMasterId = (qualifiedMenuItem as FormFillOnlyMenuItem).documentMasterId;
        menu = this.basicLegacyMenu(key, documentMasterId, qualifiedMenuItem.name, Module.FORMULARIE, null);
        menu.status = (qualifiedMenuItem as FormFillOnlyMenuItem).status;
        menu.id = qualifiedMenuItem.id;
        break;
      }
      case FavoriteTaskType.MENU_PATH: {
        const path = (qualifiedMenuItem as FavoriteMenuItem).menuPath;
        if (typeof urlParams === 'string') {
          urlParams += `&favoriteTaskId=${qualifiedMenuItem.id}`;
        } else {
          urlParams = `favoriteTaskId=${qualifiedMenuItem.id}`;
        }
        menu = this.basicModernMenu(key, path, qualifiedMenuItem.name, moduleName, urlParams);
        menu.id = qualifiedMenuItem.id;
        break;
      }
      case FavoriteTaskType.ACTIVITY:
        // Unsupported
        break;
      case FavoriteTaskType.ACTIVITY_TEMPLATE: {
        const templateActivityTemplateId = (qualifiedMenuItem as ActivityTemplateMenuItem).templateActivityTemplateId;
        menu = this.basicModernMenu(
          key,
          'menu/activities/favorite-add',
          qualifiedMenuItem.name,
          moduleName === Module.NONE ? Module.ACTIVITY : moduleName,
          `favUrl=2&templateActivityTemplateId=${templateActivityTemplateId}`
        );
        menu.id = qualifiedMenuItem.id;
        break;
      }
      case FavoriteTaskType.EXTERNAL_LINK: {
        const href = (qualifiedMenuItem as ExternalAppMenuItem).href;
        menu = this.basicLegacyMenu(key, href, qualifiedMenuItem.name, Module.NONE, urlParams);
        menu.blankTarget = true;
        menu.externalLink = true;
        menu.id = qualifiedMenuItem.id;
        break;
      }
      case FavoriteTaskType.REPORT: {
        const reportId = (qualifiedMenuItem as ReportMenuItem).reportId;
        let module: string;
        switch ((qualifiedMenuItem as ReportMenuItem)?.moduleName) {
          case Module.FORMULARIE:
            module = 'forms';
            break;
          case Module.ACTIVITY:
            module = 'activities';
            break;
          default:
            module = (qualifiedMenuItem as ReportMenuItem)?.moduleName.toLowerCase();
            break;
        }
        let urlReport = `menu/${module}/reports/${reportId}`;
        const masterId = (qualifiedMenuItem as FormFillOnlyMenuItem).documentMasterId;
        if (masterId) {
          urlReport += `/${masterId}`;
        }
        menu = this.basicModernMenu(key, urlReport, qualifiedMenuItem.name, Module[(qualifiedMenuItem as ReportMenuItem).moduleName || Module.NONE]);
        menu.id = qualifiedMenuItem.id;
        break;
      }
      case FavoriteTaskType.ACTIVITIES_QUERY_BD:
      case FavoriteTaskType.ACTIVITIES_QUERY_BD_CONTROL:
      case FavoriteTaskType.ACTIVITIES_REPORT_BD:
      case FavoriteTaskType.ACTIVITIES_REPORT_BD_CONTROL:
      case FavoriteTaskType.ACTIVITIES_CONNECTIONS_REPORTS:
      case FavoriteTaskType.ACTIVITIES_SOURCES_CONTROL:
      case FavoriteTaskType.ACTIVITIES_OBJETIVE_CONTROL:
      case FavoriteTaskType.ACTIVITIES_CACHE_BD_CONTROL:
      case FavoriteTaskType.ACTIVITIES_PRIORITIES_CONTROL:
      case FavoriteTaskType.DYNAMIC_FIELD_CONTROL: {
        const url = (qualifiedMenuItem as QualifiedGenericMenuItem).menuPath;
        menu = this.basicLegacyMenu(key, url, qualifiedMenuItem.name, Module.NONE, urlParams);
        menu.id = qualifiedMenuItem.id;
        break;
      }
      case FavoriteTaskType.USER_GROUP:
      case FavoriteTaskType.USER_GROUP_CONTROL:
      case FavoriteTaskType.ACTIVITY_PLANNED:
      case FavoriteTaskType.ACTIVITIES_RESOLUTION_CONTROL:
      case FavoriteTaskType.ACTIVITIES_TYPES_CONTROL: {
        const urlUserGroup = (qualifiedMenuItem as QualifiedGenericMenuItem).urlParams;
        menu = this.basicModernMenu(key, urlUserGroup, qualifiedMenuItem.name, Module[(qualifiedMenuItem as ReportMenuItem).moduleName || Module.NONE]);
        menu.id = qualifiedMenuItem.id;
        break;
      }
    }
    menu.icon = qualifiedMenuItem.icon;
    menu.isFavorite = true;
    this.translateService.getFrom(MENU_MAIN_LANG_CONFIG, 'menu.TASK_ONLY').subscribe((label) => {
      menu.breadcrumbs = `/${label}/${qualifiedMenuItem.name}`;
    });
    menu.level = level;
    return menu;
  }

  private showTasks(refreshPendings = true, pending?: BasicPendingData) {
    const tasksTitle = 'Bnext QMS - Pendientes';
    if (this.titleService.getTitle() !== tasksTitle) {
      this.titleService.setTitle(`Bnext QMS - ${this.translateService.instantFrom(WidgetPanelComponent.LANG_CONFIG, `i18n.widgets.${WidgetsHeader.PENDINGS}`)}`);
    }
    this.menuWidgetService.refresh(refreshPendings, pending);
    this.refreshPendingCount();
    this.showPendings = true;
    this.changeDetector.detectChanges();
  }

  private hideTasks(keepLoader = false) {
    this.showPendings = false;
    if (!keepLoader) {
      this.loader.hide();
    }
    this.changeDetector.detectChanges();
  }

  protected signOut() {
    window.top.disableBeforeunload = true;
    this.requestLocation();
    this.dialogService.info(`${this.translate.instant('logout-confirm')}`, this.translate.instant('yes'), this.translate.instant('no')).then(
      () => {
        this.authService.logout().subscribe(() => {
          location.replace(`../qms/${this.getLang()}`);
          LocalStorageSession.clearValue(LocalStorageItem.SHOWED_PREVISUALIZER_DOCUMENT);
        });
      },
      () => {
        // Do nothing
      }
    );
  }

  private activeDirectoryEdit() {
    this.navigateMenu(this.basicModernMenu('menu.activeDirectoryRead', 'menu/active-directory', '', Module.NONE));
  }

  protected activeDirectoryAdd() {
    this.translate.get('menu.activeDirectoryAdd').subscribe((label) => {
      this.navigateMenu(this.basicLegacyMenu('menu.activeDirectoryAdd', 'view/v.user.lookup.view', label, Module.CONFIGURATION));
    });
  }

  public ping() {
    setInterval(() => {
      const sub: Subscription = this.api.get({ cancelableReq: this.$destroy, url: 'users/ping', handleFailure: false }).subscribe(
        (args) => {
          console.log('ping result! ', args);
          sub.unsubscribe();
        },
        () => sub.unsubscribe()
      );
      this.subscriptionsOnDestroy.push(sub);
    }, 300000); // <---- cada 5 minutos hacemos ping
  }

  protected profile() {
    if (LoginAvailable.getValue() === 'SSO_ONLY') {
      this.activeDirectoryEdit();
      return;
    }
    this.translate.get('my-profile').subscribe((label) => {
      this.navigateMenu(this.basicLegacyMenu('my-profile', 'view/v.user.edit.view', label, Module.CONFIGURATION));
    });
  }
  protected qr() {
    this.translate.get('my-qr').subscribe((label) => {
      this.navigateMenu(this.basicLegacyMenu('my-qr', 'view/v-qr-generator.view', label, Module.CONFIGURATION));
    });
  }

  protected viewfinder() {
    if (this.legacyActive.shownDocumentViewer) {
      this.hideDocumentViewer();
    } else {
      this.showDocumentViewer();
    }
  }

  protected help() {
    this.requestLocation();
    this.translate.get('help').subscribe((label) => {
      this.navigateMenu(this.basicModernMenu('menu/help', 'menu/help', label, Module.NONE));
    });
  }

  private basicModernMenu(key: string, path: string, label: string, module: Module, urlParams: string = null): Menu {
    return this.menuService.getMenuInstanceModern(key, path, label, module, urlParams);
  }

  private basicLegacyMenu(key: string, link: string, label: string, module: Module, urlParams: string = null): Menu {
    return this.menuService.getMenuInstanceLegacy(key, link, label, module, urlParams);
  }

  private closeCurrentWindow() {
    this.currentWindow = null;
    this.currentWindowCss = null;
  }

  private moduleEquals(module1, module2): boolean {
    return EnumUtil.enumEquals(Module, module1, module2);
  }

  private moduleName(module): string {
    const upperCase = module.toUpperCase();
    return EnumUtil.getValue(Module, upperCase, 'string');
  }

  private resetParameterBuilder() {
    this.parameterBuilderMenu = null;
    this.parameterBuilderArray = [];
    this.parameterBuilderTitle = null;
    this.parameterBuilderRightButton = null;
    this.parameterBuilderLeftButton = null;
  }

  protected navigateRail(item: Menu): void {
    this.refreshMenuRail(item);
    if (this.selectedItemRail?.key === item.key) {
      const drawerRail = this.drawerRail();
      if (drawerRail.isOpen) {
        this.navNavigationRail = false;
        drawerRail.toggle();
        this.selectedItemRail = null;
      } else {
        this.navNavigationRail = true;
        drawerRail.toggle();
        this.solveMenuRail();
      }
    } else {
      this.navNavigationRail = true;
      this.drawerRail().open();
      this.changeDetector.detectChanges();
      this.selectedItemRail = item;
      this.solveMenuRail();
    }
    this.changeDetector.detectChanges();
  }

  private refreshMenuRail(selected: Menu) {
    if (selected.module === Module.TASK_ONLY) {
      this.navItemsRail = this.navItems.filter((m: Menu) => m.isFavorite === true);
    } else {
      this.navItemsRail = this.navItems.filter((m: Menu) => this.isItemSelected(m, selected));
    }
  }

  protected isItemSelected(item: Menu, selected: Menu): boolean {
    if (!item || !selected) {
      return false;
    }
    return item.module === selected.module && !item.isFavorite && item.breadcrumbs.startsWith(`${selected.breadcrumbs}`);
  }

  private solveMenuRail() {
    for (const item of this.navItemsRail) {
      if (item.level === 0 || item.level === 2) {
        item.show = true;
        item.open = this.currentMenu.module === item.module && this.currentMenu.text === item.text;
      }
    }
  }

  protected toggleAction(): void {
    const isOpen = this.drawerRail().isOpen;
    if (isOpen) {
      this.navNavigationRail = false;
      this.drawerRail().close();
    }
    this.#openedDrawer = !this.drawer().isOpen;
    this.drawer().toggle();
    this.#changedOpenDrawer.next(true);
    this.menuWidgetService.recalculateSizes();
    dispatchResize();
  }

  private executeOpenedDrawer(): void {
    MenuMainLocalConfig.setOpenDrawer(this.isDesktop, this.#openedDrawer);
  }

  private getNavTitle(item: Menu): AppBarTitle {
    let moduleName = null;
    let itemText = null;
    if (item.text && !item.text.match(NAV_TITLE_REGEX)) {
      itemText = item.text;
    }
    if (this.module) {
      moduleName = this.translate.instant(this.module);
    }
    const todayLabel = StringFormat.titleCase(this.datePipe.transform(new Date(), "EEEE d '{of}' MMMM ", this.getTimezone(), this.getLang())).replace(
      '{of}',
      this.translate.instant('root.common.date.of', true)
    );
    if (this.module && moduleName) {
      return {
        title: item.workflowName || moduleName,
        subtitle: this.breadcrumbs || itemText || todayLabel,
        isModuleTitle: true
      };
    }
    if (itemText) {
      return {
        title: itemText,
        subtitle: this.breadcrumbs || todayLabel,
        isModuleTitle: false
      };
    }
    if (item.breadcrumbs) {
      return {
        title: item.breadcrumbs,
        subtitle: todayLabel,
        isModuleTitle: false
      };
    }
    return {
      title: todayLabel,
      subtitle: StringFormat.titleCase(
        this.translate.instant('root.common.date.week', true) + this.datePipe.transform(new Date(), " w', ' y", this.getTimezone(), this.getLang())
      ),
      isModuleTitle: false
    };
  }

  private handleComponentsVisibility(path: string): void {
    if (!path) {
      return;
    }
    const hideHeader = path.includes('&nh=1');
    let hasChanges = false;
    if (hideHeader) {
      this.hideHeader = true;
      hasChanges = true;
    }
    const hideSideMenu = path.includes('&nm=1');
    if (hideSideMenu) {
      this.hideSideMenu = true;
      hasChanges = true;
    }
    if (hasChanges) {
      this.cdr.detectChanges();
    }
  }

  protected navigateMenu(item: Menu, frameLoaded = false, forceReload = false, isHiddenDocumentViewer = false): void {
    const isLegacy = this.currentWindow?.legacy;
    let iframe = null;
    if (isLegacy) {
      iframe = MenuMainUtil.getLegacyFrame(); // implementar canDeactivate.js para habilitar la funcionalidad en pantallas legacy
    } else {
      this.continueNavigateMenu(item, frameLoaded, forceReload, isHiddenDocumentViewer);
      return;
    }
    if (MenuMainUtil.hasItemAndIframeWithConfig(item, iframe)) {
      MenuMainUtil.evaluateCanDeactivate(iframe, this.translate, this.dialogService).then(
        () => {
          console.warn('[canDeactivate] - Navigate menu, The user continue with the navigation ');
          MenuMainUtil.resetCanDeactivate(iframe);
          this.continueNavigateMenu(item, frameLoaded, forceReload, isHiddenDocumentViewer);
        },
        () => {
          console.warn('[canDeactivate] - The user canceled the navigation');
        }
      );
    } else {
      this.continueNavigateMenu(item, frameLoaded, forceReload, isHiddenDocumentViewer);
    }
  }

  private continueNavigateMenu(item: Menu, frameLoaded = false, forceReload = false, isHiddenDocumentViewer = false): void {
    if (this.debug()) {
      console.log(`Continue navigate menu with frameLoaded: ${frameLoaded} and forceReload: ${forceReload} and isHiddenDocumentViewer: ${isHiddenDocumentViewer}`, item);
    }
    this.resetParameterBuilder();
    if (item.path) {
      if (!this.isUrlAlreadyLoaded(item.path)) {
        this.fabMenuIsAvailable = false;
      }
    }
    this.currentMenu = item;
    if (item.breadcrumbs && !item.isFavorite) {
      const open = item.open;
      this.breadcrumbs = item.breadcrumbs;
      this.updateSearchMenu(item.breadcrumbs.substring(1), open, item);
    }
    if (!item.text) {
      const moduleLbl = this.getModuleLabel(this.navLang.getCurrentComponentName(item.path));
      item.text = moduleLbl.label;
    }
    if (this.moduleEquals(item.module, Module.NONE)) {
      item.module = this.getModule();
    }
    if (this.moduleEquals(item.module, Module.NONE)) {
      this.module = null;
    } else {
      this.module = `menu.${this.moduleName(item.module)}`;
    }
    this.menuService.changeTitle(this.getNavTitle(item));
    if (item.path) {
      let path = item.path;
      const isLegacy = path?.startsWith('menu/legacy');
      if (isLegacy) {
        if (isHiddenDocumentViewer && !this.legacyActive.documentViewerClosing) {
          this.menuService.addFabOptionsMenu([]);
        }
        const link = item.link;
        if (item.link?.startsWith(path)) {
          path = link;
        } else if (typeof link === 'string') {
          path = path + link.substring(link.indexOf('/'));
        }
        if (item.blankTarget && !item.externalLink) {
          window.open(`../qms/${this.getLang()}/${path}`, '_blank');
        } else if (item.blankTarget && item.externalLink) {
          window.open(item.link, '_blank');
        } else {
          if (item.isFavorite && item.module === 'FORMULARIE') {
            this.translate.get('menu.confirmDraft').subscribe((msg) => {
              this.dialogMessage = msg;
            });
            this.api
              .get({
                cancelableReq: this.$destroy,
                url: `forms/getFormParams/${item.link}`,
                handleFailure: false
              })
              .subscribe(
                (result) => {
                  if (result.status === 0) {
                    this.documentNotAvilable();
                    return;
                  }
                  const formLink = `/v.request.survey.fill.view?id=${result.surveyId}&tabId=${TabUtils.getTabId()}&documentId=${result.documentId}&requestMode=REQUESTOR`;
                  path = item.path + formLink.substring(formLink.indexOf('/'));
                  const surveyId = result.surveyId;
                  this.api.get({ cancelableReq: this.$destroy, url: `users/hasIncompleteForm/${surveyId}`, handleFailure: false }).subscribe((result) => {
                    if (result.id) {
                      const draftDate = this.datePipe.transform(result.creationDate, 'dd/MM/yyyy');
                      const draftTime = this.datePipe.transform(result.creationDate, 'HH:mm');
                      this.dialogMessage = this.dialogMessage.replace('{draftDate}', draftDate).replace('{draftTime}', draftTime);
                      this.dialogService
                        .frame(this.dialogMessage, this.translate.instant('menu.continueDraft'), 'root.common.button.deleteForm', 'root.common.button.confirmation')
                        .then(
                          () => {
                            path = `${path}&outstandingSurveyId=${result.id}`;
                            this.changeWindow(path, isLegacy, item, frameLoaded).then(() => {
                              this.fixUrlLocation(path);
                              this.hideTasks(isLegacy);
                            });
                          },
                          () => {
                            this.api
                              .get({ cancelableReq: this.$destroy, url: `users/destroyFillForm/${result.id}/${result.requestId}`, handleFailure: false })
                              .subscribe(() => {
                                this.changeWindow(path, isLegacy, item, frameLoaded).then(() => {
                                  this.fixUrlLocation(path);
                                  this.hideTasks(isLegacy);
                                });
                              });
                          }
                        );
                    } else {
                      this.changeWindow(path, isLegacy, item, frameLoaded).then(() => {
                        this.fixUrlLocation(path);
                        this.hideTasks(isLegacy);
                      });
                    }
                  });
                },
                () => {
                  this.documentNotAvilable();
                }
              );
          } else {
            if (this.isPopState && path.indexOf('v-attender-redirect') !== -1) {
              this.navigate('pendings');
              return;
            }
            this.isPopState = false;
            this.changeWindow(path, isLegacy, item, frameLoaded, forceReload).then(() => {
              this.fixUrlLocation(path);
              this.hideTasks(isLegacy);
            });
          }
        }
        this.refreshPendingCount();
      } else {
        if (!this.isUrlAlreadyLoaded(item.path)) {
          this.validIsFabButtonImplemented();
        }
        if (path.match(PATH_REGEX) != null) {
          // si contiene algún parametro sin resolver
          this.parameterBuilderMenu = item;
          this.parameterBuilderTitle = this.navLang.queryParamFrom('inputTitle', path) || 'Capturar información';
          this.parameterBuilderRightButton = this.navLang.queryParamFrom('rightButton', path) || 'Cancelar';
          this.parameterBuilderLeftButton = this.navLang.queryParamFrom('leftButton', path) || 'Cancelar';
          const keyValues = path.split('&');
          for (const v of keyValues) {
            const keyValue = v.split('=');
            const key = keyValue[0].replace(KEY_VALE_REGEX, '');
            const value = keyValue[1];
            const fieldName = this.navLang.queryParamFrom(`${key}FieldName`, path) || null;
            if (value && value.indexOf('$') === 0) {
              const hasRegExp = value.match(PARAM_BUILDER_REGEX);
              if (hasRegExp) {
                const validRegExp = new RegExp(hasRegExp[1]);
                this.parameterBuilderArray.push({
                  fieldLabel: fieldName || key,
                  fieldName: key,
                  regExp: validRegExp,
                  value: null
                });
              } else {
                this.parameterBuilderArray.push({
                  fieldLabel: fieldName || key,
                  fieldName: key,
                  value: null
                });
              }
            }
          }
          this.paramBuilderDialogOpen = true;
          this.cdr.detectChanges();
          this.paramBuilderDialog().open();
        } else {
          this.solveModernLocation(item, forceReload);
          if (typeof item.menuService === 'undefined') {
            this.hideTasks();
          }
          this.refreshPendingCount();
        }
        this.loader.hide();
      }
      const drawerRail = this.drawerRail();
      if (drawerRail.isOpen) {
        this.navNavigationRail = false;
        drawerRail.toggle();
      }
      const drawer = this.drawer();
      if (!this.drawerState.miniTemplate && drawer.isOpen) {
        drawer.toggle();
      }
      this.selectedItemRail = item;
    }
  }

  private solveModernLocation(item: Menu, forceReload?: boolean): void {
    /**
     * Si llegaste aquí por que una opción del menú PATH te llevó a un lugar diferente puede
     * ser por que este metodo está pensado para que los PATHS no se repitan.
     *
     * Revisar la solución del Req #49518 (es el commit donde se subió este comentario)
     */
    let path = item.path;

    // Validación para navegar directamente al Alta avanzada de actividades sólo cuando es un dispositivo móvil
    if (this.isSmallTouchDevice && typeof item.mobilePath === 'string') {
      path = item.mobilePath;
    }

    if (item.blankTarget) {
      window.open(`../qms/${this.getLang()}/${path}`, '_blank');
    } else if (item.menuService) {
      if (typeof this.menuService[item.menuService] === 'function') {
        this.menuService[item.menuService]();
      } else {
        console.error('Invalid menuService call', item);
      }
    } else {
      this.changeWindow(path, false, item).then(() => {
        if (!forceReload && this.isUrlAlreadyLoaded(path)) {
          const routePath = this.removeLangAndMenuFromPath(path);
          this.setRouteLoad(routePath, 'no-reload');
          return;
        }
        const fixUrlLocation = () => {
          this.fixUrlLocation(path);
        };
        this.navigateByUrl(path).then(fixUrlLocation);
      });
    }
  }

  private removeLangAndMenuFromPath(path: string) {
    if (path === null || typeof path === 'undefined' || path === '') {
      return path;
    }
    return path.replace(REMOVE_LANG_REGEX, '');
  }

  private fixUrlLocation(path: string): void {
    if (this.debug()) {
      console.log(`Changing menu location to ${path}.`);
    }
    const loaded = this.isUrlAlreadyLoaded(path);
    if (loaded) {
      const routePath = this.removeLangAndMenuFromPath(path);
      this.setRouteLoad(routePath, 'no-reload');
      return;
    }
    const newPath = `${this.getLang()}/${path}`;
    if (this.debug()) {
      console.log(`Changing menu location to ${newPath}.`);
    }
    this.location.go(newPath);
  }

  private isBaseFromAlreadyLoaded(path: string, isLegacy: boolean): boolean {
    if (isLegacy) {
      const frameSrc = this.baseFrameSrc;
      if (!path || path === '' || path.length <= 2 || !frameSrc || frameSrc === '' || frameSrc.length <= 2) {
        return false;
      }
      const normalizedPath = `../view${path.substring(path.lastIndexOf('/'))}`;
      return frameSrc && frameSrc.indexOf(normalizedPath) !== -1;
    }
    return false;
  }

  private isUrlAlreadyLoaded(path: string): boolean {
    const currentPath = this.location.path();
    let newPath: string;
    if (path && typeof path !== 'undefined' && path?.startsWith(`/${this.getLang()}`)) {
      newPath = path;
    } else {
      newPath = `${this.getLang()}/${path}`;
    }
    const newPathSlashed = `/${newPath}`;
    const workflowName = `/${WORKFLOW_MENU_NAME}`;
    if (currentPath.includes(workflowName)) {
      const index = currentPath.indexOf(workflowName);
      const currPath = currentPath.substring(0, index);
      const currWorkflowId = this.getWorkflowIndex(currentPath, workflowName);
      const newWorkflowId = this.getWorkflowIndex(newPathSlashed, workflowName);
      const sameWorkflowPath = currPath === newPathSlashed.substring(0, newPathSlashed.indexOf(workflowName)) && currWorkflowId === newWorkflowId;
      const samePath = currPath === newPath;
      return sameWorkflowPath || samePath;
    }
    return currentPath === newPathSlashed || currentPath === newPath;
  }

  private getModule(): Module {
    const moduleCheck = location.search.match(MODULE_CHECK_REGEX);
    if (moduleCheck) {
      return Module[moduleCheck[2]] || Module.NONE;
    }
    return Module.NONE;
  }

  protected override afterWindowResized(event: Event) {
    super.afterWindowResized(event);
    if (!this.isScreenLarge) {
      this.drawerState.miniTemplate = false;
      this.drawerState.open = true;
      this.drawerState.pin = false;
      this.drawerState.pinThreshold = 5000;
    } else {
      this.drawerState.miniTemplate = true;
      this.drawerState.open = false;
      this.drawerState.pin = true;
      this.drawerState.pinThreshold = 0;
      const drawer = this.drawer();
      if (!this.drawerState.miniTemplate && drawer.isOpen) {
        drawer.toggle();
      }
    }
    if (this.debug()) {
      console.log('>> ', this.isScreenLarge, this.getDevideInfo());
    }
    this.changeDetector.detectChanges();
  }

  private clearUniqueParameter(search: string): string {
    if (typeof search !== 'string' || search === DEFAULT_FRAME_SRC || search.indexOf(UNIQUE_HASH_FRAME_PARAMETER) === -1) {
      return search;
    }
    let result = search.replace(UNIQUE_HASH_FRAME_REGEX, '');
    if (result.indexOf('?') === -1 && result.indexOf('&') !== -1) {
      result = result.replace('&', '?');
    }
    return result;
  }

  private generateRandomHash(): string {
    return randomUUID();
  }

  private generateUniqueSrc(src: string): string {
    let newSrc: string;
    if (src.indexOf('?') === -1) {
      newSrc = `${src}?${UNIQUE_HASH_FRAME_PARAMETER}=${this.generateRandomHash()}`;
    } else {
      newSrc = `${src}&${UNIQUE_HASH_FRAME_PARAMETER}=${this.generateRandomHash()}`;
    }
    return newSrc;
  }

  private changeLegacySrc(windowConfig: HistoryWindow, frameLoaded: boolean, forceReload?: boolean): Promise<boolean> {
    return new Promise<boolean>((resolve: (value: boolean) => void) => {
      if (this.debug()) {
        console.log(`Changing legacy src for ${windowConfig.path} with frameLoaded: ${frameLoaded} and forceReload: ${forceReload}`, windowConfig);
      }
      if (!this.basefrm?.nativeElement) {
        resolve(false);
        return;
      }
      const newSrc = `../view${windowConfig.path.substring(windowConfig.path.lastIndexOf('/'))}`;
      const isNewSrcUpdated = this.currentWindow && newSrc === this.currentWindow.currentSrc;
      const frameSrc = this.baseFrameSrc;
      const shouldAskForReload: boolean =
        this.currentWindow &&
        frameSrc &&
        frameSrc.indexOf('about:blank') === -1 &&
        !frameLoaded &&
        this.location.path().endsWith(newSrc.replace(REPLACE_VIEW_REGEX, '')) &&
        this.location.path().indexOf('v-attender-redirect.view') === -1 &&
        !forceReload;
      const mainBody = window.top.window.document.body;
      const shouldExitFullScreenViewer: boolean = mainBody?.classList.contains('full-screen') && windowConfig.path.indexOf('v-document-viewer') === -1;
      if (shouldExitFullScreenViewer) {
        mainBody.classList.remove('full-screen');
        dispatchResize();
      }
      if (shouldAskForReload) {
        this.dialogService
          .info(this.translate.instant('confirm'), 'root.common.button.reload', 'root.common.button.cancel', this.translate.instant('confirm-reload-title'))
          .then(() => {
            this.changeLegacySrcAction(windowConfig, newSrc, frameSrc, frameLoaded, false, false, resolve);
          });
      } else {
        this.changeLegacySrcAction(windowConfig, newSrc, frameSrc, frameLoaded, forceReload, isNewSrcUpdated, resolve);
      }
    });
  }

  private changeLegacySrcAction(
    windowConfig: HistoryWindow,
    newSrc: string,
    frameSrc: string,
    frameLoaded: boolean,
    forceReload: boolean,
    isNewSrcUpdated: boolean,
    resolve: (value: boolean) => void
  ) {
    if (!forceReload && (frameLoaded || isNewSrcUpdated)) {
      windowConfig.frameSrc = frameSrc;
    } else {
      const alreadyLoaded = this.isBaseFromAlreadyLoaded(newSrc, true);
      if (forceReload || alreadyLoaded) {
        windowConfig.frameSrc = this.generateUniqueSrc(newSrc);
      } else {
        windowConfig.frameSrc = newSrc;
      }
    }
    windowConfig.currentSrc = newSrc;
    this.changeDetector.detectChanges();
    resolve(true);
  }

  private hideDocumentViewer() {
    if (!this.legacyActive.shownDocumentViewer) {
      return;
    }
    this.legacyActive.documentViewerClosing = true;
    this.legacyActive.shownDocumentViewer = false;
    if (this.currentWindow?.isActive) {
      this.legacyActive.show = false;
      this.clearLegacyUrl();
      this.#modernContainerScrollTicking = false;
      if (this.currentWindow.menu) {
        this.navigateMenu(this.currentWindow.menu, false, false, true);
      } else {
        this.fixUrlLocation(this.currentWindow.path);
      }
    } else if (!this.currentWindow || (this.legacyActive.show && !this.currentWindow.legacy)) {
      this.legacyActive.show = false;
      this.clearLegacyUrl();
      this.#modernContainerScrollTicking = false;
      this.displayFullTask(false);
    } else if (this.legacyActive.show) {
      this.legacyActive.show = false;
      this.clearLegacyUrl();
      this.fixUrlLocation(this.currentWindow.path);
    }
    this.legacyActive.documentViewerClosing = false;
    if (this.isPendingsAvailable && this.#pendingsLoading) {
      this.showTasks();
    }
    this.changeDetector.detectChanges();
  }

  protected showDocumentViewer(path?: string) {
    this.legacyActive.show = true;
    this.#modernContainerScrollTicking = false;
    this.legacyActive.shownDocumentViewer = true;
    this.setFullScreen();
    this.hideTasks(true);
    if (!this.legacyActive.hasDocumentViewer) {
      this.clearGlobalViewFinder();
      this.legacyActive.hasDocumentViewer = true;
    }
    if (path) {
      if (path.indexOf('menu/legacy/') === -1) {
        this.fixUrlLocation(`menu/legacy/${path}`);
      } else {
        this.fixUrlLocation(path);
      }
    } else {
      this.fixUrlLocation(`menu/legacy/${DOCUMENT_VIEWER_SRC}`);
    }
    if (this.documentViewer?.nativeElement?.contentWindow?.show) {
      this.documentViewer.nativeElement.contentWindow.show();
    }
    this.changeDetector.detectChanges();
  }

  private documentNotAvilable() {
    this.dialogService.info(this.translate.instant('menu.inactiveForm'), 'root.common.button.close', null, 'root.common.error.messageSystem');
  }

  protected isFormActive(item) {
    if (item.status == null) {
      return true;
    }
    return item.status === 1;
  }

  private changeWindow(path: string, legacy: boolean, menu: Menu, frameLoaded?: boolean, forceReload?: boolean): Promise<boolean> {
    this.handleComponentsVisibility(path);
    const windowConfig: HistoryWindow = {
      path: path,
      legacy: legacy,
      menu: menu,
      isActive: true,
      key: menu.key,
      frameSrc: DEFAULT_FRAME_SRC,
      currentSrc: DEFAULT_FRAME_SRC
    };
    return new Promise<boolean>((resolve) => {
      if (legacy) {
        this.legacyActive.show = true;
        this.#modernContainerScrollTicking = false;
        this.destroyFabMenu(path);
        this.hideDocumentViewer();
        if (windowConfig.path.indexOf(DOCUMENT_VIEWER_SRC) === -1) {
          this.changeLegacySrc(windowConfig, frameLoaded, forceReload).then(() => {
            this.changeWindowAction(windowConfig, legacy);
            resolve(true);
          });
        } else {
          this.showDocumentViewer(path);
          resolve(true);
        }
      } else {
        this.legacyActive.show = false;
        this.clearLegacyUrl(path);
        this.#modernContainerScrollTicking = false;
        this.hideDocumentViewer();
        const routePath = this.removeLangAndMenuFromPath(path);
        const pathState = this.getRouteLoad(routePath);
        const shouldAskForReload: boolean = this.location.path().endsWith(routePath) && (pathState === null || pathState === 'reload');
        if (shouldAskForReload) {
          this.dialogService
            .info(this.translate.instant('confirm'), 'root.common.button.reload', 'root.common.button.cancel', this.translate.instant('confirm-reload-title'))
            .then(() => {
              this.setRouteLoad(routePath, 'reload');
              this.changeWindowAction(windowConfig, legacy);
              resolve(true);
            });
        } else {
          this.setRouteLoad(routePath, 'reload');
          this.changeWindowAction(windowConfig, legacy);
          resolve(true);
        }
      }
      if (this.touchDevice) {
        this.drawerState.open = false;
        this.drawer().close();
        this.loader.refreshWindowSize(777);
      }
    });
  }

  private clearLegacyUrl(path?: string): void {
    const frameNode = MenuMainUtil.getLegacyFrame();
    this.destroyFabMenu(path);
    if (frameNode) {
      const frameClone = frameNode.cloneNode();
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      frameClone['src'] = 'about:blank';
      this.bindLegacyFrameEvents(frameClone);
      if (typeof Event === 'function') {
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        frameNode['contentWindow'].dispatchEvent(new Event('beforeunload'));
      } else {
        const evt = document.createEvent('HTMLEvents');
        evt.initEvent('beforeunload');
        // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
        frameNode['contentWindow'].dispatchEvent(evt);
      }
      frameNode.parentNode.replaceChild(frameClone, frameNode);
    }
  }

  private changeWindowAction(windowConfig: HistoryWindow, legacy: boolean) {
    if (this.debug()) {
      console.log(`Changing window action for ${windowConfig.path} with legacy: ${legacy}`, windowConfig);
    }
    this.currentWindow = windowConfig;
    this.currentWindowCss = windowConfig.menu?.path
      ?.replace(/[\/\-]/g, '_')
      .replace(/[0-9]+/g, 'N')
      .toLowerCase();
    const frameNode = MenuMainUtil.getLegacyFrame();
    if (frameNode?.parentNode && windowConfig.legacy) {
      const frameClone = frameNode.cloneNode();
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      if (frameClone['src'] !== this.currentWindow.frameSrc) {
        this.loader.show();
        this.changeDetector.detectChanges();
      }
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      frameClone['src'] = this.currentWindow.frameSrc;
      this.bindLegacyFrameEvents(frameClone);
      frameNode.parentNode.replaceChild(frameClone, frameNode);
    }
    this.changeWindowTitle();
    this.changeDetector.detectChanges();
    this.menuService.afterChangedWindow(windowConfig);
    if (legacy) {
      this.loader.refreshWindowSize(777);
    }
  }

  private changeWindowTitle() {
    if (!this.currentWindow) {
      return;
    }
    const title = this.getCalculatedWindowTitle();
    if (title?.startsWith('modules.') || this.titleService.getTitle() === 'title') {
      return;
    }
    this.titleService.setTitle(title);
  }

  private getCalculatedWindowTitle(): string {
    if (this.currentWindow.menu.text && this.module) {
      const currentText = this.currentWindow.menu.text;
      if (currentText?.startsWith('modules.')) {
        return this.translate.instant(this.module);
      }
      return `${this.translate.instant(this.module)} - ${currentText}`;
    }
    if (this.module) {
      return this.translate.instant(this.module);
    }
    if (this.currentWindow.menu.text) {
      return this.currentWindow.menu.text;
    }
    return 'Bnext QMS - Inicio';
  }

  protected menuItemClass(item: Menu, navigationRail = false): string {
    if (item.icon === '') {
      return `actionMenu menu-item elevation-border-top level${item.level}`;
    }
    if (item.level === 3 && navigationRail === true) {
      return `actionMenu menu-item elevation-border-top display-menu level${item.level}`;
    }
    return `menu-item elevation-border-top level${item.level}`;
  }

  private updateSearchMenu(path: string, isPathAlreadyOpen: boolean, item?: Menu) {
    this.setPathToOpen(path);
    this.searchMenu = `"${path.trim().toLowerCase()}"`;
    this.filterChange(isPathAlreadyOpen);
    if (!item) {
      return;
    }
    if (item.level === 0 && item.open) {
      item.elevation = 2;
    } else if (item.level === 0) {
      item.elevation = 0;
    }
  }

  private setPathToOpen(path: string) {
    LocalStorageSession.setValue(LocalStorageItem.CURRENT_PATH_USER, `"${path}"`);
  }

  private setDefaultFolderIcon(item: Menu) {
    item.open = false;
    if (item.icon === 'expand_less') {
      item.icon = 'expand_more';
    }
    item.elevation = 0;
  }

  private setExpandedFolderIcon(item: Menu) {
    item.open = true;
    if (item.icon === 'expand_more') {
      item.icon = 'expand_less';
      item.elevation = 2;
    }
  }

  protected onFilterChange() {
    this.filterChange(false);
  }

  protected goHome(event: MouseEvent, ask = false) {
    if (event?.ctrlKey) {
      return;
    }
    event?.preventDefault();
    event?.stopPropagation();
    this.requestLocation();
    if (ask) {
      this.dialogService.info(this.translate.instant('confirm'), 'root.common.button.ok', 'root.common.button.cancel', this.translate.instant('homepage')).then(() => {
        this.displayFullTask();
        const drawer = this.drawer();
        if (!this.isScreenLarge && drawer.isOpen) {
          drawer.toggle();
        }
      });
    } else {
      this.displayFullTask();
    }
  }

  private filterChange(isPathAlreadyOpen: boolean) {
    let selectedMenu: Menu;
    const selectedMenuParent: Menu[] = [];
    const searchCriteria = this.searchMenu.trim().toLowerCase();
    const exactMatchRemove = /"/gi;
    const slashRegex = /\//g;
    const crit = `/${searchCriteria.replace(exactMatchRemove, '')}`;
    const crit2 = crit.substring(0, crit.lastIndexOf('/')) || '';
    const crit3 = crit2.substring(0, crit2.lastIndexOf('/')) || '';
    const crit4 = crit3.substring(0, crit3.lastIndexOf('/')) || '';
    const crit5 = crit4.substring(0, crit4.lastIndexOf('/')) || '';
    const crit6 = crit5.substring(0, crit5.lastIndexOf('/')) || '';
    const critLevel = (crit.match(slashRegex) || []).length + 1;
    const crit2Level = (crit2.match(slashRegex) || []).length + 1;
    const crit3Level = (crit3.match(slashRegex) || []).length + 1;
    const crit4Level = (crit4.match(slashRegex) || []).length + 1;
    const crit5Level = (crit5.match(slashRegex) || []).length + 1;
    const crit6Level = (crit6.match(slashRegex) || []).length + 1;
    // obtiene breadcrumbs relevantes "matches"
    const crumbs = this.navItems
      .filter((item) => {
        const searchTarget = item.breadcrumbs.trim().toLowerCase();
        item.open = false;
        item.show = false;
        item.elevation = 0;
        if (searchCriteria.length < 2) {
          this.setDefaultFolderIcon(item);
          return false;
        }
        if (searchCriteria.match(EXACT_MATCH_REGEX)) {
          // Si coincide exactamente
          const isTarget = crit === searchTarget;
          // o se encuentra exactamente en un nivel superior al mismo
          const inHigherLevel = crit.indexOf(searchTarget) === 0 && searchTarget.match(slashRegex).length < critLevel;
          // o se encuentra exactamente en un nivel inferior al mismo
          const inLowerLevel = searchTarget.indexOf(crit) === 0 && searchTarget.match(slashRegex).length === critLevel;
          // muestra los componentes del menu que estan al mismo nivel
          const sameLevel =
            (searchTarget.indexOf(crit2) === 0 && searchTarget.match(slashRegex).length === crit2Level) ||
            (searchTarget.indexOf(crit3) === 0 && searchTarget.match(slashRegex).length === crit3Level) ||
            (searchTarget.indexOf(crit4) === 0 && searchTarget.match(slashRegex).length === crit4Level) ||
            (searchTarget.indexOf(crit5) === 0 && searchTarget.match(slashRegex).length === crit5Level) ||
            (searchTarget.indexOf(crit6) === 0 && searchTarget.match(slashRegex).length === crit6Level);
          if (isTarget) {
            selectedMenu = item;
            if (isPathAlreadyOpen) {
              this.setDefaultFolderIcon(item);
            } else {
              this.setExpandedFolderIcon(item);
            }
          } else if (inHigherLevel) {
            selectedMenuParent.push(item);
            // Sólo se expande el submenú cuando los items no son del nivel 3 sólo para desktop.
            if (!this.isScreenLarge || (item.level !== 3 && this.isScreenLarge)) {
              this.setExpandedFolderIcon(item);
            }
          } else {
            this.setDefaultFolderIcon(item);
          }
          return isTarget || (!isPathAlreadyOpen && inLowerLevel) || sameLevel;
        }
        this.setExpandedFolderIcon(item);
        return searchTarget.indexOf(searchCriteria) !== -1;
      })
      .map((item) => item.breadcrumbs);
    const shouldMap = [];
    // Genera las ligas que se busca igualar
    for (const crumb of crumbs) {
      const split = crumb.split('/');
      let c = '';
      for (let i = 1, l = split.length; i < l; i++) {
        c = `${c}/${split[i]}`;
        shouldMap.push(c);
      }
    }
    let deepestLevel = 1;
    // Asigna open y show a las areas correspondientes
    this.selectedItem = [];
    for (const item1 of this.navItems.filter((item) => {
      if (this.touchDevice && selectedMenu && (selectedMenu.level > item.level || (selectedMenu.open && selectedMenu.icon !== '' && selectedMenu.level === item.level))) {
        return false;
      }
      const crumbSplit = item.breadcrumbs.split('/');
      if (crumbSplit.length === 2) {
        return true;
      }
      const mapIndex = shouldMap.indexOf(item.breadcrumbs);
      return mapIndex !== -1;
    })) {
      item1.show = true;
      if (item1.level > deepestLevel) {
        deepestLevel = item1.level;
      }
      if (item1.level === 4) {
        this.selectedItem.push(item1);
      }
    }
    if (selectedMenu) {
      for (const parent of selectedMenuParent) {
        if (
          (parent.level === selectedMenu.level - 1 || (selectedMenu.level === 2 && parent.level === 0)) &&
          (selectedMenu.icon === '' || (selectedMenu.icon !== '' && !selectedMenu.open))
        ) {
          parent.show = true;
          this.parentItem = parent;
        }
      }
      if (this.touchDevice) {
        this.mobileShowHelper = selectedMenu.level > 0 || (selectedMenu.level === 0 && selectedMenu.open);
        selectedMenu.show = true;
        selectedMenuParent.push(selectedMenu);
        this.isSettingsModule(selectedMenuParent);
      }
    }
  }

  protected clearSearch() {
    this.updateSearchMenu('', true);
    this.searchMenu = '';
    const entry = this.entry();
    if (entry) {
      entry.nativeElement.value = '';
    }
  }

  protected onParameterBuilderRightButtonSelect(): void {
    const isRequiredMissing = this.parameterBuilderArray.find((param: Param) => {
      return !param || !param.value || !`${param.value}`.trim();
    });
    if (isRequiredMissing) {
      this.noticeService.notice('Por favor llene todos los campos');
      return;
    }
    let url = this.parameterBuilderMenu.path;
    for (const p of this.parameterBuilderArray) {
      url = url.replace(new RegExp(`([?|&]${p.fieldName}=)(.*?)(&|$)`), `$1${p.value}$3`);
    }
    const menuItem = Object.assign({}, this.parameterBuilderMenu);
    menuItem.path = url;
    this.solveModernLocation(menuItem);
    const paramBuilderDialog = this.paramBuilderDialog();
    if (paramBuilderDialog) {
      paramBuilderDialog.close();
    }
    this.paramBuilderDialogOpen = false;
  }

  protected onParameterBuilderLeftButtonSelect(): void {
    const paramBuilderDialog = this.paramBuilderDialog();
    if (paramBuilderDialog) {
      paramBuilderDialog.close();
    }
    this.paramBuilderDialogOpen = false;
  }

  protected navigateDropDownMenu(selected, menu) {
    if (typeof selected.text !== 'undefined') {
      const menuItem = this.selectedItem.filter((item) => item.text === selected.text)[0];
      this.navigateMenu(menuItem);
      this.navigateMenu(this.parentItem);
    } else {
      this.navigateMenu(menu);
    }
  }

  protected onCloseDropdown(closed, item: Menu) {
    if (closed) {
      this.setDefaultFolderIcon(item);
    }
  }

  /**
   * Método que devuelve las opciones del menú de 4 nivel
   * @param items item de tercer nivel
   * @returns Lista de las opciones del menú de cuarto nivel
   */
  protected hasItems(items) {
    const menuOptions = [];
    // Sólo se realiza filtrado de los items cuando se abre el item de tercer nivel y no en todos los de tercer nivel
    if (items.level === 3 && items.elevation === 2) {
      for (const item1 of this.selectedItem.filter((item) => item.breadcrumbsKey.indexOf(items.breadcrumbsKey) !== -1)) {
        const menuItem = {
          hidden: false,
          text: item1.text,
          value: item1.link,
          iconName: null
        };
        menuOptions.push(menuItem);
      }
    }
    return menuOptions;
  }

  private isSettingsModule(items: Menu[]) {
    let settingsModule = false;
    for (const item of items) {
      if (item.key === 'CONFIGURATION' || item.masterKey === 'CONFIGURATION') {
        settingsModule = true;
      }
    }
    this.settingsModule = settingsModule;
  }

  protected get hiddenRoutes(): boolean {
    return this.showPendings;
  }

  @HostListener('window:popstate', ['$event'])
  protected onPopState() {
    this.isPopState = true;
  }

  protected onScrollModernContainer(event: EventInit): void {
    if (!this.#modernContainerScrollTicking) {
      window.requestAnimationFrame(() => {
        const bnextScroll = new Event('bnextScroll', event);
        window.document.dispatchEvent(bnextScroll);
        this.#modernContainerScrollTicking = false;
      });
      this.#modernContainerScrollTicking = true;
    }
  }

  protected onKey() {
    this.searchMenu = this.entry().nativeElement.value;
    this.search().nativeElement.select();
    this.onFilterChange();
  }

  protected menuContext(e, item: Menu) {
    if (item.isFavorite) {
      e.preventDefault();
      this.favoriteItemSelected = item;
      this.showContextualMenu = true;
      this.cdr.detectChanges();
      this.contextualMenu().nativeElement.style.setProperty('--top', `${e.y}px`);
      this.contextualMenu().nativeElement.style.setProperty('--left', `${e.x}px`);
    }
  }

  protected removeFavorite() {
    const idFavorite = this.favoriteItemSelected.id;
    this.api.post({ url: 'access/delete-my-links', cancelableReq: this.$destroy, postBody: [idFavorite] }).subscribe(() => {
      this.refreshMenu(false);
      this.showContextualMenu = false;
      const favIndex = this.navItemsRail.findIndex((i) => i.id === idFavorite);
      if (favIndex !== -1) {
        this.navItemsRail.splice(favIndex, 1);
      }
      this.cdr.detectChanges();
    });
  }

  protected favoriteInfo() {
    const id = this.favoriteItemSelected.id;
    this.showContextualMenu = false;
    this.api.get({ cancelableReq: this.$destroy, url: `access/get-favorite-link/${id}` }).subscribe((response) => {
      switch (response.type) {
        case FavoritesTasks.FORMULARIE:
          this.favoriteActivity = this.translate.instantFrom(BnextTranslateService.MODULE_COMMON, 'names.formsTitle');
          break;
        case FavoritesTasks.DOCUMENT:
          this.favoriteActivity = this.translate.instantFrom(BnextTranslateService.MODULE_COMMON, 'names.documentFavoriteTitle');
          break;
        case FavoritesTasks.TEMPLATE_ACTIVITY:
          this.favoriteActivity = this.translate.instantFrom(BnextTranslateService.MODULE_COMMON, 'names.templateActivityTitle');
          break;
        default:
          this.favoriteActivity = '';
          break;
      }
      this.favoriteUrl = response.menuPath;
      if (response.urlParams) {
        this.favoriteUrl += `?${response.urlParams}`;
      }
      this.favoriteDescription = response.description;
      this.favoriteReportId = Number(response.reportId);
      this.igxFavoriteUpdateOpen = true;
      this.cdr.detectChanges();
      this.igxFavoriteUpdate().open();
      this.changeDetector.detectChanges();
    });
  }

  protected updateFavorite() {
    const idFavorite = this.favoriteItemSelected.id;
    const data = {
      id: idFavorite,
      description: this.favoriteDescription,
      reportId: this.favoriteReportId
    };
    this.api.post({ url: 'access/update-my-links', cancelableReq: this.$destroy, postBody: new Array(data) }).subscribe(() => {
      this.refreshMenu(false);
      this.showContextualMenu = false;
      const igxFavoriteUpdate = this.igxFavoriteUpdate();
      if (igxFavoriteUpdate) {
        igxFavoriteUpdate.close();
      }
      this.igxFavoriteUpdateOpen = false;
      this.cdr.detectChanges();
    });
  }

  protected mouseleave(e) {
    if (!e.relatedTarget?.classList.contains('contextual-menu') && this.showContextualMenu === true) {
      this.showContextualMenu = false;
    }
  }

  protected navigateToUrl() {
    const igxFavoriteUpdate = this.igxFavoriteUpdate();
    if (igxFavoriteUpdate) {
      igxFavoriteUpdate.close();
    }
    this.navigateMenu(this.favoriteItemSelected);
  }

  protected cancelUpdateFavorite() {
    const igxFavoriteUpdate = this.igxFavoriteUpdate();
    if (igxFavoriteUpdate) {
      igxFavoriteUpdate.close();
    }
    this.igxFavoriteUpdateOpen = false;
    this.showContextualMenu = false;
    this.cdr.detectChanges();
  }

  private fabMenuOptions(config: FabButtonMenuItem[] | FabButtonConfig): void {
    let items: FabButtonMenuItem[];
    let fabMenuItemsAvailable: (string | number)[] = null;
    let float = false;
    if ((config as FabButtonConfig).items) {
      this.fabShowBackAvailable = (config as FabButtonConfig).showBackButton || false;
      items = (config as FabButtonConfig).items;
      fabMenuItemsAvailable = (config as FabButtonConfig).availableItemValues;
      // Se filtra la acción BACK para que no aparezca en modo Desktop
      if (this.fabShowBackAvailable && items.length > 0) {
        items = items.filter((i: FabButtonMenuItem) => i.value !== CommonAction.BACK);
      }
      float = !!(config as FabButtonConfig).float;
    } else {
      items = config as FabButtonMenuItem[];
    }
    if (items !== null && items.length > 0) {
      this.fabMenuIsAvailable = true;
      this.fabMenuItems = items;
      this.fabMenuItemsAvailable = fabMenuItemsAvailable;
    } else {
      this.destroyFabMenu();
    }
    this.fabMenuFloat = float;
    this.cdr.detectChanges();
  }

  private destroyFabMenu(url?: string): void {
    if (!url || !this.isUrlAlreadyLoaded(url)) {
      this.fabMenuIsAvailable = false;
      this.fabMenuItems = [];
      this.fabMenuItemsAvailable = null;
    }
  }

  protected doFabActionButton(optionSelected: FabButtonMenuItem | DropdownMenuItem): void {
    this.menuService.doActionFabOption(optionSelected);
  }
  protected fabBackAction(): void {
    this.menuService.doActionFabOption(this.itemBack);
    this.fabShowBackAvailable = false;
    this.cdr.detectChanges();
  }

  protected goToSearchActivity(): void {
    const selectedModule = this.selectModuleSearch().nativeElement.value;
    if (selectedModule !== 'default-option') {
      this.handlerSearchActivityType(selectedModule);
    } else {
      this.dialogService.info(this.translate.instant('warning-select-search-option'), null, null, this.translate.instant('root.common.message.information'));
    }
  }

  private handlerSearchActivityType(module: Module): void {
    let urlTo = '';
    this.api
      .get({
        url: `activities/activity/searchActivityId/${this.codeToSearch.trim()}`,
        cancelableReq: this.$destroy
      })
      .subscribe((activityId: { activityId: number }) => {
        if (activityId.activityId === 0) {
          this.dialogService.info(this.translate.instant('root.modules.menu.menu-main.codeNotExist') + this.codeToSearch);
        } else {
          switch (module) {
            case Module.ACTIVITY:
              urlTo = `menu/activities/ACTIVITY/${activityId.activityId}`;
              break;
            case Module.FORMULARIE:
              urlTo = `menu/activities/FORMULARIE/${activityId.activityId}`;
              break;
            case Module.AUDIT:
              urlTo = `menu/activities/AUDIT/${activityId.activityId}`;
              break;
            case Module.ACTION:
              urlTo = `menu/activities/ACTION/${activityId.activityId}`;
              break;
          }
        }
        this.menuService.navigate(urlTo);
        this.saveLocalStorageSearchModule(module);
      });
  }

  protected onKeyUp(event): void {
    if (event.code === 'Enter' || event.code === 'NumpadEnter') {
      this.goToSearchActivity();
    }
  }

  private saveLocalStorageSearchModule(module: Module): void {
    LocalStorageSession.setValue(LocalStorageItem.SEARCH_MODULE, module);
    LocalStorageSession.setValue(LocalStorageItem.SEARCH_MODULE_TEXT, this.codeToSearch);
  }

  protected setSelectModuleToSearch(value: string): boolean {
    const valueSaved = LocalStorageSession.getValue(LocalStorageItem.SEARCH_MODULE) !== null ? LocalStorageSession.getValue(LocalStorageItem.SEARCH_MODULE) : '';
    let selected = false;
    if (valueSaved.length > 0) {
      if (valueSaved === value) {
        selected = true;
      }
    }
    return selected;
  }

  private bindLegacyFrameEvents(frame: Node | ElementRef): void {
    if (frame instanceof Node) {
      frame.addEventListener('load', this.onLoadBaseFrm.bind(this));
      frame.addEventListener('error', this.onErrorBaseFrm.bind(this));
    } else if (frame instanceof ElementRef) {
      frame.nativeElement.addEventListener('load', this.onLoadBaseFrm.bind(this)) as ElementRef;
      frame.nativeElement.addEventListener('error', this.onErrorBaseFrm.bind(this)) as ElementRef;
    }
  }

  private deleteDocumentActivity(arg: DeleteDocumentService) {
    const documentId = arg.data.documentId;
    const recurrenceId = arg.data.recurrenceId || null;
    const id = arg.data.activityId;
    let url = `activities/activity/documents/delete/${id}/${documentId}`;
    if (recurrenceId) {
      url += `/${recurrenceId}`;
    }
    this.api
      .post({ url: url, postBody: null, handleFailure: false, cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe(
        (response) => {
          const result: DeleteDocumentDTO = {
            history: response.history,
            activityId: response.activityId,
            documentId: response.itemId,
            recurrenceId: response.recurrenceId
          };
          if (!response) {
            arg.def.reject(result);
          } else {
            arg.def.resolve(result);
          }
        },
        () => {
          arg.def.reject(arg.data);
        }
      );
  }

  private getWorkflowIndex(path: string, workflowName: string): string {
    const lowLimit = path.indexOf(`${workflowName}/`) + `${workflowName}/`.length;
    let workflowId = path.substring(lowLimit, path.length);
    const indexNextSlash = workflowId.indexOf('/');
    if (indexNextSlash !== -1) {
      workflowId = workflowId.substring(0, indexNextSlash);
    }
    return workflowId;
  }

  private getWindowLabelFromLink(link: string): string {
    const view = link.substring(link.indexOf('view/') + 5);
    return view.replace(TARGET_URL_REGEX, '');
  }

  private getWindowLabelFromUrl(url: string): string {
    const view = this.getWindowViewFromUrl(url);
    return view.replace(TARGET_URL_REGEX, '');
  }

  private getWindowViewFromUrl(url: string): string {
    return url.replace(CLEAR_WINDOW_URL_REGEX, '');
  }
}
