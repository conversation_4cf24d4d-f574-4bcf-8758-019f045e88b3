import { Component, type OnInit, inject, viewChild } from '@angular/core';
import { BnextCoreComponent, type i18n } from 'src/app/core/bnext-core.component';

import { DropdownSearchComponent } from '@/core/dropdown-search/dropdown-search.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { DatePipe } from '@angular/common';
import { FormBuilder, FormControl, type FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IgxAvatarComponent } from '@infragistics/igniteui-angular';
import { SharedModule } from 'primeng/api';
import { type OrganizationChart, OrganizationChartModule, type OrganizationChartNodeSelectEvent } from 'primeng/organizationchart';
import { takeUntil } from 'rxjs';
import { RestApiModule } from 'src/app/core/rest-api.module';
import { AppService } from 'src/app/core/services/app.service';
import { BnextLoaderActivationService } from 'src/app/core/services/bnext-loader-activation.service';
import type { TextHasValue } from 'src/app/core/utils/text-has-value';

@Component({
  selector: 'app-hierarchy',
  templateUrl: './hierarchy.component.html',
  styleUrls: ['./hierarchy.component.scss'],
  imports: [FormsModule, ReactiveFormsModule, DropdownSearchComponent, OrganizationChartModule, SharedModule, IgxAvatarComponent, BnextTranslatePipe]
})
export class HierarchyComponent extends BnextCoreComponent implements OnInit, i18n {
  datePipe = inject(DatePipe);

  service = inject(AppService);
  fb = inject(FormBuilder);
  loader = inject(BnextLoaderActivationService);

  public data = [];
  public users = new Array<TextHasValue>();
  private cacheUrlAvatar = {};

  public form: FormGroup;

  readonly orgChart = viewChild<OrganizationChart>('orgChart');

  ngOnInit() {
    this.service.get({ url: 'users/get-all-users', cancelableReq: this.$destroy }).subscribe((users: TextHasValue[]) => {
      users.unshift({ text: this.translate.instant('root.common.message.all-users'), value: 0 });
      this.users = users;
      this.generateHeriarchy(0).then(() => {
        this.form.patchValue({ userSelect: 0 }, { onlySelf: true, emitEvent: false });
      });
    });

    this.form = this.fb.group({
      userSelect: new FormControl()
    });
  }

  public langReady(): void {
    console.log('ready');
  }

  private generateHeriarchy(userId: number): Promise<void> {
    this.loader.show();
    return new Promise((succ, fail) => {
      let url = 'users/heriarchy';
      if (userId >= 1) {
        url = `users/heriarchy/${userId}`;
      }
      this.service
        .get({ url: url, cancelableReq: this.$destroy })
        .pipe(takeUntil(this.$destroy))
        .subscribe(
          (res: any) => {
            this.data = res;
            this.cdr.detectChanges();
            succ();
            this.loader.hide();
          },
          (e) => {
            fail(e);
            this.loader.hide();
          }
        );
    });
  }

  public onSelectionChanging(event: TextHasValue): void {
    this.generateHeriarchy(event.value as number);
  }

  getAvatarSrc(id: number): string {
    if (this.cacheUrlAvatar[id]) {
      return this.cacheUrlAvatar[id];
    }
    this.cacheUrlAvatar[id] = RestApiModule.avatar(id);
    return this.cacheUrlAvatar[id];
  }

  public onNodeSelect(nodeSelect: OrganizationChartNodeSelectEvent): void {
    nodeSelect.node.expanded = !nodeSelect.node.expanded;
    this.orgChart().selection = null;
  }
}
