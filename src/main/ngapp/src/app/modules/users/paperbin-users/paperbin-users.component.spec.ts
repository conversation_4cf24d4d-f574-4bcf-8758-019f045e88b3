import { type ComponentFixture, TestBed } from '@angular/core/testing';

import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { PaperbinUsersComponent } from './paperbin-users.component';

describe('PaperbinUsersComponent', () => {
  let component: PaperbinUsersComponent;
  let fixture: ComponentFixture<PaperbinUsersComponent>;

  configureTestSuite();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MockCoreModule, PaperbinUsersComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PaperbinUsersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
