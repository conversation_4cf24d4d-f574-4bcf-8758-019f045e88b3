import { type AfterViewInit, Component, type <PERSON><PERSON><PERSON><PERSON>, type OnInit, inject } from '@angular/core';
import { RegionAddComponent } from '../region-add/region-add.component';
import { RegionBaseModule } from '../region-base/region-base.module';

import * as NumberUtil from '@/core/utils/number-util';

import { ActivatedRoute } from '@angular/router';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import type { IRegion } from '../region-base/region-base.interfaces';

@Component({
  selector: 'app-region-edit',
  imports: [RegionBaseModule],
  templateUrl: '../region-base/region-base.component.html',
  styleUrls: ['../region-base/region-base.component.scss']
})
export class RegionEditComponent extends RegionAddComponent implements OnInit, AfterViewInit, OnDestroy {
  activatedRoute = inject(ActivatedRoute);

  /**
   * Utiliza el lenguaje de `RegionBaseComponent`
   **/
  id: number;

  get titleTag(): string {
    return 'region-edit-title';
  }
  get showGenerateCode() {
    return false;
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.navLang.getRouteParam(`${this.controller}/:id`, null, false, this.activatedRoute).subscribe({
      next: ([id]) => {
        if (!NumberUtil.isInteger(id)) {
          console.error('Invalid region id');
          return;
        }
        this.id = +id;
        this.api.get({ url: `${this.controller}/load/${id}`, cancelableReq: this.$destroy }).subscribe({
          next: (result) => {
            result.generateCode = false;
            this.mainForm.patchValue(result);
            this.busy = false;
            this.cdr.detectChanges();
          },
          error: (error) => {
            console.log(error);
            this.busy = false;
            this.cdr.detectChanges();
          }
        });
      },
      error: () => {
        throw new Error('Not defined  Id');
      }
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  protected override getEntityData(): IRegion {
    const data = super.getEntityData();
    data.id = this.id;
    return data;
  }

  protected openList() {
    this.menuService.navigate('menu/organization/region/list', Module.CONFIGURATION);
  }
}
