import { type ComponentFixture, TestBed } from '@angular/core/testing';

import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { RegionBaseComponent } from './region-base.component';

describe('RegionBaseComponent', () => {
  let component: RegionBaseComponent;
  let fixture: ComponentFixture<RegionBaseComponent>;

  configureTestSuite();

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, RegionBaseComponent],
      providers: MockProviders.PROVIDERS
    });
    fixture = TestBed.createComponent(RegionBaseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
