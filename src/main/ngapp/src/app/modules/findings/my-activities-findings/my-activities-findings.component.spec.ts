import { MockCoreModule } from '@/core/test/mock-core.module';
import { type ComponentFixture, TestBed } from '@angular/core/testing';
import { MockProviders } from 'src/app/core/test/mock-providers';
import { configureTestSuite } from 'src/app/core/test/utils/configure-test-suite';
import { MyActivitiesFindingsComponent } from './my-activities-findings.component';

describe('MyActivitiesFindingsComponent', () => {
  let component: MyActivitiesFindingsComponent;
  let fixture: ComponentFixture<MyActivitiesFindingsComponent>;

  configureTestSuite();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MockCoreModule, MyActivitiesFindingsComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MyActivitiesFindingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
