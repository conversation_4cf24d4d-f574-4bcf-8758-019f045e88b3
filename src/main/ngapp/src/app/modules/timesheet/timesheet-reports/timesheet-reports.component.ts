import { DatePipe } from '@angular/common';
import { Component, type OnInit, inject } from '@angular/core';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import { type ActionStrip, LinkColumn, TextColumn } from 'src/app/core/grid/utils/grid.interfaces';
import { CommonAction } from 'src/app/core/utils/enums';
import { FavoriteTaskType, Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';

import { BnextCoreComponent } from '@/core/bnext-core.component';
import type { GridColumn } from '@/core/grid/utils/grid-column';
import * as GridUtil from '@/core/grid/utils/grid-util';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';

import { Session } from '@/core/local-storage/session';

import { GridComponent } from '@/core/grid/grid.component';
import { Deferred } from '@/core/utils/deferred';
import type { MenuService } from '@/modules/menu/services/menu.service';
import type { RowType } from '@infragistics/igniteui-angular';
import type { GridDropDownItem } from 'src/app/core/grid/utils/grid-base.interfaces';
import type { FavoriteTaskSaveDTO, QuickAccessHolder } from '../../menu/menu-main/menu-main.interfaces';
import { TimesheetListComponent } from '../timesheet-list/timesheet-list.component';

@Component({
  selector: 'app-timesheet-reports',
  styleUrls: ['./timesheet-reports.component.scss'],
  templateUrl: './timesheet-reports.component.html',
  imports: [GridComponent]
})
export class TimesheetReportsComponent extends BnextCoreComponent implements OnInit {
  datePipe = inject(DatePipe);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.timesheet',
    componentName: 'timesheet-reports'
  };

  url = 'timesheet/reports';
  columns: GridColumn[];
  titleLabel: string;
  editAccess =
    Session.hasService(ProfileServices.IS_ADMIN) ||
    (Session.hasService(ProfileServices.USUARIO_CORPORATIVO) && Session.hasService(ProfileServices.TS_ADMON_REPORT_ACCESS));
  LangConfig = TimesheetReportsComponent.LANG_CONFIG;

  actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 6,
    render: {
      menuActionOptions: (row: RowType) => (row ? this.getData(row?.data) : []),
      rowIdentifierKey: 'id'
    }
  };

  override ngOnInit(): void {
    this.perPage = Session.getGridSize();
    const s = this.translate.getFrom(this.LangConfig, 'timesheetReport.title').subscribe({ next: (tag) => (this.titleLabel = tag) });
    if (s) {
      this.subs.push(s);
    }
    this.columns = [];
    GridUtil.columns(this.columns)
      .subs(this.subs)
      .push(
        'title',
        new LinkColumn({
          linkHref: `./${this.getLang()}/menu/timesheet/reports/{id}`,
          linkParams: ['id'],
          width: '450px'
        })
      )
      .push(
        'description',
        new TextColumn({
          width: '450px'
        })
      )
      .langAsync(this.translateService, this.LangConfig, 'reports.column');
    this.cdr.detectChanges();
  }

  private getData(row: any): DropdownMenuItem[] {
    return [
      {
        text: this.translateService.instant('root.common.button.open'),
        value: CommonAction.OPEN_DETAIL,
        iconName: 'open_in_browser',
        modernHref: `timesheet/reports/${row.id}`,
        hidden: false
      },
      {
        text: this.translateService.instant('root.common.button.accessSettings'),
        value: CommonAction.EDIT,
        legacyHref: `v-timesheet-report-access.view?id=${row.nodeId}`,
        iconName: 'settings',
        hidden: !this.editAccess
      },
      {
        text: this.translateService.instant('root.common.button.add-favorite'),
        value: CommonAction.FAVORITE,
        iconName: 'favorite_border',
        hidden: false
      }
    ];
  }

  toggleMenuAction(drop: GridDropDownItem) {
    switch (drop.item.value) {
      case CommonAction.OPEN_DETAIL:
        this.menuService.navigate(`menu/timesheet/reports/${drop.row.id}`, Module.TIMESHEET);
        break;
      case CommonAction.EDIT:
        this.menuService.navigateLegacy(`v-timesheet-report-access.view?id=${drop.row.nodeId}`, Module.TIMESHEET);
        break;
      case CommonAction.FAVORITE:
        this.addTimesheetReportToFavorite(drop.row.id, drop.row, this.menuService, drop.row.description);
        break;
      default:
        break;
    }
  }

  public addTimesheetReportToFavorite(reportId: number, holder: QuickAccessHolder, menuService: MenuService, description: string): void {
    const saved = new Deferred<FavoriteTaskSaveDTO>();
    menuService.addQuickAccess({
      def: saved,
      icon: 'done',
      urlParams: `menu/timesheet/reports/${reportId}`,
      menuPath: 'menu/timesheet/reports',
      type: FavoriteTaskType.REPORT,
      infoMessage: this.translate.instantFrom(TimesheetListComponent.LANG_CONFIG, 'add-to-favorite'),
      reportId: reportId,
      moduleName: String(Module.TIMESHEET).toLowerCase(),
      taskName: description
    });
    saved.promise.then((result) => {
      if (result.success) {
        holder.isFavorite = 1;
        holder.favoriteTaskId = result.id;
      }
    });
  }
}
