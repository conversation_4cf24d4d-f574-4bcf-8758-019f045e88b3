import type { i18n } from '@/core/bnext-core.component';
import { DropdownMenuComponent } from '@/core/dropdown-menu/dropdown-menu.component';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { ConfigApp } from '@/core/local-storage/config-app';
import { Session } from '@/core/local-storage/session';
import { NoticeService } from '@/core/services/notice.service';
import * as DateUtil from '@/core/utils/date-util';
import * as DomUtil from '@/core/utils/dom-util';
import { ErrorHandling } from '@/core/utils/error-handling';
import * as NumberUtil from '@/core/utils/number-util';
import { stringColors } from '@/core/utils/string-util';
import { DatePipe, NgClass } from '@angular/common';
import { type AfterViewInit, Component, ElementRef, Input, type OnDestroy, type OnInit, Renderer2, inject, input, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DomSanitizer, HammerModule, type SafeHtml } from '@angular/platform-browser';
import { RouterLink } from '@angular/router';
import {
  IgxButtonDirective,
  IgxCardActionsComponent,
  IgxCardComponent,
  IgxCardContentDirective,
  IgxCardHeaderComponent,
  IgxChipComponent,
  IgxFilterOptions,
  IgxFilterPipe,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLinearProgressBarComponent,
  IgxListComponent,
  IgxListItemComponent,
  IgxListLineSubTitleDirective,
  IgxListLineTitleDirective,
  IgxPrefixDirective,
  IgxRippleDirective,
  IgxSuffixDirective,
  PickerInteractionMode
} from '@infragistics/igniteui-angular';
import { type DxCalendarComponent, DxCalendarModule } from 'devextreme-angular/ui/calendar';
import { DxTooltipModule } from 'devextreme-angular/ui/tooltip';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BnextCoreComponent } from 'src/app/core/bnext-core.component';
import type { DropdownMenuItem } from 'src/app/core/dropdown-menu/dropdown-menu.interfaces';
import { AppService } from 'src/app/core/services/app.service';
import type { DataMap } from 'src/app/core/utils/data-map';
import { CommonAction } from 'src/app/core/utils/enums';
import { FeatureFlag, isFeatureAvailable } from 'src/app/core/utils/feature-util';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import { ProfileServices } from 'src/app/shared/roles/profiles/utils/profile-services.enums';
import type { LinkTaskDetail } from '../../menu/menu-widget/menu-widget.interfaces';
import { MenuWidgetService } from '../../menu/menu-widget/menu-widget.service';
import { TimesheetService } from '../../timework/services/timesheet.service';
import type { SizeWidget, WidgetConfig } from '../../widgets/widget-panel/widget-panel.interfaces';
import { TimesheetStopwatchComponent } from '../timesheet-stopwatch/timesheet-stopwatch.component';
import { PlannedType, TimesheetAction } from './timesheet-widget.enums';
import type { LocalValueChangedInfo, StopwatchEndTimeData, TimesheetDto, TimesheetWithStopwatchDto, UpdateUIStopwatch } from './timesheet-widget.interfaces';
import { TimesheetWidgetUtils } from './timesheet-widget.utils';

@Component({
  selector: 'app-timesheet-widget',
  templateUrl: './timesheet-widget.component.html',
  styleUrls: ['./timesheet-widget.component.scss'],
  imports: [
    DatePipe,
    RouterLink,
    FormsModule,
    NgClass,
    HammerModule,
    IgxCardComponent,
    IgxCardHeaderComponent,
    IgxIconButtonDirective,
    IgxButtonDirective,
    IgxIconComponent,
    IgxInputGroupComponent,
    IgxInputDirective,
    IgxPrefixDirective,
    IgxSuffixDirective,
    IgxCardContentDirective,
    IgxLinearProgressBarComponent,
    IgxListComponent,
    IgxListItemComponent,
    IgxListLineTitleDirective,
    IgxListLineSubTitleDirective,
    IgxRippleDirective,
    IgxFilterPipe,
    IgxChipComponent,
    IgxCardActionsComponent,
    DxTooltipModule,
    DxCalendarModule,
    TimesheetStopwatchComponent,
    DropdownMenuComponent,
    BnextTranslatePipe
  ]
})
export class TimesheetWidgetComponent extends BnextCoreComponent implements OnDestroy, OnInit, i18n, AfterViewInit {
  sanitizer = inject(DomSanitizer);
  service = inject(AppService);

  noticeService = inject(NoticeService);
  datePipe = inject(DatePipe);

  private timesheetService = inject(TimesheetService);
  private menuWidgetService = inject(MenuWidgetService);
  private renderer2 = inject(Renderer2);

  public get customTagName(): string {
    return TimesheetWidgetUtils.LANG_CONFIG.componentName;
  }
  public get componentPath(): string {
    return this._componentPath;
  }
  public set componentPath(value: string) {
    this._componentPath = value;
  }

  private _componentPath = TimesheetWidgetUtils.LANG_CONFIG.componentPath;

  readonly miniView = input(false);
  readonly showMoreAvailable = input(true);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public size: SizeWidget = {};
  public readonly header = input<string>(undefined);

  needPachBrowser = this.isAppleDevice() && this.isIPhoneNotInstallled();
  shouldScale = false;
  currentDate: Date;
  titleCurrentDate = '';
  titleTooltipDate = '';
  busy = false;
  dateFormat = 'dd/MM/yyyy';
  timeFormat = 'HH:mm';
  sumHoursAtDay = 0;
  sumMinAtDay = 0;
  colorCache: DataMap<{
    color: string;
    bgColor: string;
  }> = {};
  isTagChipVisible = false;
  timePickerMode: PickerInteractionMode = PickerInteractionMode.DropDown;
  items: TimesheetDto[] = [];
  menuOptions: DropdownMenuItem[] = this.defineMenuOptions();
  menuOptionsMainAction: DropdownMenuItem[] = [
    {
      text: 'Modificar',
      value: CommonAction.EDIT,
      iconName: 'create'
    },
    {
      text: 'Eliminar',
      value: CommonAction.DELETE,
      iconName: 'delete'
    }
  ];
  mainActionsAvailable: string[] = [CommonAction.EDIT, CommonAction.DELETE];

  readonly igxListItem = viewChild<IgxListItemComponent>('focusTarget');
  readonly dropdownTimesheet = viewChild('dropdownTimesheet', { read: ElementRef });
  readonly searchBar = viewChild('searchBar', { read: ElementRef });
  readonly tooltipCalenderOverlay = viewChild<ElementRef>('tooltipCalenderOverlay');
  readonly tooltipCalendar = viewChild<DxCalendarComponent>('tooltipCalendar');

  override watchResizeEvents = true;

  // filtro de texto
  isSearchBarHidden = true;
  searchValue = '';
  private _onSearchValueChanged = new Subject<string>();
  isStopwatcherRunning = false;
  isStopwatchRunningMessage = '';

  isOutsideTheLimit = true;
  visibleTooltipCalendar = false;

  constructor() {
    super();
    this.subscribeServices();
  }

  override langReady(): void {
    this.titleCurrentDate = this.translate.instant('pipe.today-label');
    this.refreshLang();
  }

  refreshLang() {
    for (const option of this.menuOptions) {
      switch (option.value) {
        case TimesheetAction.REGISTER_UNPLANNED:
          option.text = this.translate.instant('new_register');
          break;
        case TimesheetAction.OPEN_CALENDAR:
          option.text = this.translate.instant('calendar');
          break;
      }
    }
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
  }

  ngOnInit(): void {
    super.ngOnInit();
    // Se espera a que cargue la sesion para gregar permisos
    Session.getLazySession()
      .pipe(takeUntil(this.$destroy))
      .subscribe(() => {
        const isUnplannedAvaliable = Session.hasService(ProfileServices.TS_REGISTER_UNPLANNED);
        const featureAvailable = isFeatureAvailable(FeatureFlag.TIMESHEET_SCHEDULER) || this.isSmallTouchDevice;
        this.menuOptions = this.defineMenuOptions(isUnplannedAvaliable, featureAvailable);
        this.refreshLang();
        this.detectChanges();
      });
    this.menuWidgetService.requestRefreshWidgetSize.pipe(takeUntil(this.$destroy)).subscribe((refreshData) => {
      this.refreshSize(refreshData.widgets);
      this.detectChanges();
    });
    this.menuWidgetService.refreshPendingsRequest.pipe(takeUntil(this.$destroy)).subscribe(() => {
      this.refresh();
    });
    this.refresh();
  }

  private refreshSize(widgets: readonly WidgetConfig[]): void {
    const widget = widgets.find((item) => item.header === this.header());
    if (widget) {
      this.size = widget.sizeWidget;
    }
  }

  private setWidgetDate(date: Date): void {
    this.currentDate = date;
    this.timesheetService.timesheetWidgetDateChange(this.currentDate);
  }

  public refresh(): void {
    this.setWidgetDate(new Date());
    this.titleCurrentDate = this.translate.instant('pipe.today-label');
    this.setTitleTooltipDate(new Date());
    this.load(this.currentDate);
    if (this.px_ratio >= 1.5) {
      this.shouldScale = true;
    } else {
      this.shouldScale = false;
    }
    this.updateLabelsValidationStopwatch();
    this.detectChanges();
  }

  override ngAfterViewInit() {
    this.detectChanges();
  }

  private subscribeServices() {
    this.timesheetService.timesheetPush.subscribe((item: TimesheetDto) => {
      this.addItem(item);
    });
    this.timesheetService.replaceItemObs.subscribe((item: TimesheetDto) => {
      this.replaceItem(item);
    });
  }

  toggleMenu(item: DropdownMenuItem) {
    this.visibleTooltipCalendar = false;
    switch (item.value) {
      case TimesheetAction.OPEN_CALENDAR:
        this.openCalendar();
        break;
      case TimesheetAction.REGISTER_UNPLANNED:
        if (this.isStopwatcherRunning) {
          this.noticeService.notice(this.isStopwatchRunningMessage);
        } else {
          this.openNew();
        }
        break;
    }
  }

  toggleMenuMainAction(itemDropDown: DropdownMenuItem, item: TimesheetDto) {
    switch (itemDropDown.value) {
      case CommonAction.EDIT:
        this.modify(item);
        break;
      case CommonAction.DELETE:
        this.delete(item);
        break;
    }
  }

  modify(data: TimesheetDto): void {
    this.visibleTooltipCalendar = false;
    if (this.isStopwatcherRunning) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.stopwatch-running'));
      return;
    }
    const cloneData = structuredClone(data);
    this.openEdit(cloneData);
  }

  delete(data: TimesheetDto, haveStopwatch?: boolean): void {
    this.visibleTooltipCalendar = false;
    if (ConfigApp.getTimeLimitToModifyTimesheet() > DateUtil.safe(data.date)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'pipe.dateOutOfLimit'));
      return;
    }
    this.timesheetService.deleteRegistry(data.timesheetId).then((result) => {
      if (result) {
        this.removeItem(data.timesheetId);
        this.cdr.detectChanges();
        if (haveStopwatch) {
          this.updateTimeLabels({ hasEnableStopwatch: false, end: new Date(), timesheetId: data.timesheetId, date: data.date as Date });
        }
      }
    });
  }

  chipColor(str: string): string {
    if (typeof this.colorCache[str] === 'undefined') {
      this.colorCache[str] = this.stringColors(str);
    }
    return this.colorCache[str].bgColor;
  }

  chipColorBlack(str: string): boolean {
    if (typeof this.colorCache[str] === 'undefined') {
      this.colorCache[str] = this.stringColors(str);
    }
    return this.colorCache[str].color === '#000000';
  }

  registryWrapper(text: string): SafeHtml {
    return (
      text
        ?.replace(/\r\n|\r|\n/g, ' ')
        .replace('<', '&lt;')
        .replace('>', '&gt;') || ''
    );
  }

  focus(focus: boolean, elem: IgxListItemComponent): boolean {
    if (focus && elem.element?.scrollIntoView) {
      elem.element.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
      // Focus nuevo registro de timesheet.
      const dropdownTimesheet = this.dropdownTimesheet();
      const focusable = dropdownTimesheet?.nativeElement?.firstChild?.firstChild;
      if (focusable?.focus && typeof focusable?.focus === 'function') {
        dropdownTimesheet.nativeElement.firstChild.firstChild.focus();
      }
    }
    return focus;
  }

  tagsArray(item: TimesheetDto): string[] {
    const arr = TimesheetWidgetUtils.getTagsArray(item.tags);
    if ((this.size.responsiveClass === 'xsmallResponsive' || this.isSmallTouchDevice) && arr.length) {
      item.tagsNotShown = arr.length > 1 ? arr.length - 1 : null;
      const newArr: string[] = [];
      newArr.push(arr.shift());
      item.tagsNotShownTooltip = arr.join(', ');
      return newArr;
    }
    item.tagsNotShown = null;
    return arr.length ? arr : null;
  }

  fixTags(tags: string | string[]): string {
    return TimesheetWidgetUtils.fixTags(tags);
  }

  public openNew(pendingRecordId?: number): void {
    const plannedType = pendingRecordId !== null && typeof pendingRecordId !== 'undefined' ? PlannedType.PLANNED : PlannedType.UNPLANNED;
    this.timesheetService.openNew(plannedType, pendingRecordId, null, null, this.currentDate, null, true).then((value) => {
      // Enviamos el item
      this.addItem(value);
      this.cdr.detectChanges();
    });
  }

  private openEdit(data: TimesheetDto): void {
    this.timesheetService.openEdit(data).then((value: TimesheetDto) => {
      this.replaceItem(value);
      this.cdr.detectChanges();
    });
  }

  private stringColors(str: string): {
    color: string;
    bgColor: string;
  } {
    return stringColors(str, 175);
  }

  private openCalendar(): void {
    this.tooltipCalendar().instance.reset();
    this.menuService.navigate('menu/timesheet/add', Module.TIMESHEET);
  }

  private clearBusy(): void {
    this.busy = false;
    this.cdr.detectChanges();
  }

  private refreshItemsOrder(): void {
    this.items = this.items.sort((a, b) => {
      return DateUtil.safe(a.start).getTime() - DateUtil.safe(b.start).getTime();
    });
    for (const i of this.items) {
      TimesheetWidgetUtils.getDurationTime(i);
    }
    this.cdr.detectChanges();
  }

  private addItem(item: TimesheetDto): void {
    if (DateUtil.isSameDay(DateUtil.safe(item.date), this.currentDate)) {
      item.wasCreated = true;
      this.items.push(item);
      item.focused = true;
      this.refreshItemsOrder();
      this.calculateHoursWorkedDaily();
      this.updateLastEndFromItems(this.currentDate);
      this.timesheetService.hasStopwatchrunningService(item);
      setTimeout(() => {
        item.focused = false;
        this.cdr.detectChanges();
      }, 500);
    }
  }

  private replaceItem(item: TimesheetDto): void {
    if (!item) {
      return;
    }
    this.removeItem(item.timesheetId);
    this.addItem(item);
  }

  private updateLastEndFromItems(date: Date): void {
    if (this.items?.[this.items.length - 1]) {
      const lastEnd = DateUtil.safe(this.items[this.items.length - 1].end);
      TimesheetWidgetUtils.LAST_END = lastEnd;
      if (DateUtil.isToday(date)) {
        TimesheetWidgetUtils.LAST_END_TODAY = lastEnd;
      }
    } else {
      // Se hace reset de LAST_END_TODAY si es el dia de hoy y no existe ningún registro, por lo que no hay item del cual tomar la última fecha.
      if (this.items && this.items.length === 0 && DateUtil.isToday(date)) {
        TimesheetWidgetUtils.LAST_END_TODAY = null;
      }
      TimesheetWidgetUtils.LAST_END = date;
    }
  }

  private removeItem(timesheetId: number): void {
    this.items = this.items.filter((i) => i.timesheetId !== timesheetId);
    this.calculateHoursWorkedDaily();
    this.updateLastEndFromItems(this.currentDate);
  }

  public load(date?: Date, loadFromCalendar?: boolean): void {
    if (!Session.hasAccess(Module.TIMESHEET)) {
      return;
    }
    if (this.busy) {
      return;
    }
    this.busy = true;
    this.cdr.detectChanges();
    this.isOutsideTheLimit = ConfigApp.getTimeLimitToModifyTimesheet() > date;
    let valueService = 'timesheet/main';
    if (date) {
      valueService = `timesheet/main/${DateUtil.format(date, 'YYYYMMDD')}`;
    }
    if (loadFromCalendar) {
      this.handleTooltipCalendarOverlay(false);
    }
    this.service.get({ cancelableReq: this.$destroy, url: valueService, handleFailure: false }).subscribe({
      next: (items: TimesheetDto[]) => {
        for (const i of items) {
          TimesheetWidgetUtils.fixItem(i);
        }
        this.items = items;
        const pendingRunning = this.items.filter((p) => p.hasEnableStopwatch === true)[0];
        if (typeof pendingRunning !== 'undefined') {
          this.timesheetService.hasStopwatchrunningService(pendingRunning);
          if (!pendingRunning) {
            // Realiza peticion a la BD(async)
            this.checkPassDaysStopwatch();
          }
        } else {
          // Realiza peticion a la BD(async)
          this.checkPassDaysStopwatch();
        }
        this.refreshItemsOrder();
        this.calculateHoursWorkedDaily();
        if (date) {
          this.updateLastEndFromItems(date);
        }
        if (loadFromCalendar) {
          this.handleTooltipCalendarOverlay(true);
        }
        this.clearBusy();
      },
      error: (error) => {
        ErrorHandling.notifyError(error, this.navLang);
        if (loadFromCalendar) {
          this.handleTooltipCalendarOverlay(true);
        }
        this.clearBusy();
      }
    });
  }

  onSearchValueChange(searchValue: string) {
    this._onSearchValueChanged.next(searchValue);
  }

  clearSearchBar(): void {
    this.searchValue = '';
    this.onSearchValueChange('');
    this.cdr.detectChanges();
  }

  get filterItems(): IgxFilterOptions {
    const _fo = new IgxFilterOptions();
    _fo.key = 'registry';
    _fo.inputValue = this.searchValue;
    _fo.get_value = (item: TimesheetDto, _key: string) => {
      return `${this.handleHighlight(`${item.registry}`, false, true)}@${this.datePipe.transform(item.start, this.timeFormat)}@${this.datePipe.transform(item.end, this.timeFormat)}@${item.tags}@${item.clientDescription}@${item.plannerDescription}@${item.activityDescription}`;
    };
    return _fo;
  }

  highlight(text: SafeHtml): SafeHtml {
    return this.handleHighlight(text, true, false);
  }

  private handleHighlight(text: SafeHtml, highlightSearch: boolean, textOnly: boolean): SafeHtml {
    return DomUtil.highlight(this.sanitizer, this.searchValue, text, highlightSearch, textOnly).safeHtml;
  }

  isSearchValueEmpty(): boolean {
    return !this.searchValue.replace(/\r|\n/g, '').trim();
  }

  private calculateHoursWorkedDaily(): void {
    let sum = 0;
    for (const i of this.items) {
      const min = Math.abs(DateUtil.safe(i.end).getTime() - DateUtil.safe(i.start).getTime());
      sum += Math.floor(min / 1000 / 60);
    }
    this.sumHoursAtDay = Math.floor(sum / 60);
    this.sumMinAtDay = sum - this.sumHoursAtDay * 60;
    this.cdr.detectChanges();
  }

  detectChanges(): void {
    this.isSearchBarHidden = true;
    this.cdr.detectChanges();
  }

  get itemsListHeight(): string {
    if (!this.isSearchBarHidden) {
      return `${this.size.height - 160}px`;
    }
    return this.size !== null ? `${this.size.height - 117}px` : '100%';
  }

  getItemMaxWidth(hasEnableStopWatch: boolean): string {
    if (hasEnableStopWatch) {
      return '100%';
    }
    if (this.size !== null && this.size.responsiveClass === 'xsmallResponsive') {
      return `${this.size.width - 200}px`;
    }
    if (this.px_ratio >= 1.25 && this.size.responsiveClass === 'mediumResponsive') {
      return `${this.size.width - 202}px !important`;
    }
    return this.size !== null ? `${this.size.width - 202}px` : 'calc(100% - 140px)';
  }

  openSearch(): void {
    this.isSearchBarHidden = !this.isSearchBarHidden;
    this.visibleTooltipCalendar = false;
    this.cdr.detectChanges();
  }

  loadToday(): void {
    if (this.busy) {
      return;
    }
    this.setWidgetDate(new Date(Date.now()));
    this.titleCurrentDate = this.translate.instant('pipe.today-label');
    this.load(this.currentDate);
    this.selectDateTooltipCalendar();
  }

  loadPreviousDay(): void {
    this.setWidgetDate(DateUtil.subtractDays(this.currentDate, 1));
    this.setDateLabels(this.currentDate);
    this.load(this.currentDate);
    this.selectDateTooltipCalendar();
  }

  loadNextDay(): void {
    this.setWidgetDate(DateUtil.addOneDay(this.currentDate));
    this.setDateLabels(this.currentDate);
    this.load(this.currentDate);
    this.selectDateTooltipCalendar();
  }

  setDateLabels(currentDate: Date) {
    if (!DateUtil.isToday(currentDate)) {
      const newDate = new Date(currentDate);
      this.titleCurrentDate = this.datePipe.transform(newDate, 'dd/MM/yyyy').substring(0, 5);
    } else {
      this.titleCurrentDate = this.translate.instant('pipe.today-label');
    }
  }

  isActivityKey(tag: string): boolean {
    return !!tag.match(TimesheetWidgetUtils.regexCode);
  }

  goToDetailActivity(item: TimesheetDto, tag: string) {
    this.closeTooltipCalendar();
    if (item.pendingRecordId) {
      this.service.get({ cancelableReq: this.$destroy, url: `timesheet/record-activity/${item.pendingRecordId}` }).subscribe({
        next: (taskDetail: LinkTaskDetail) => {
          if (taskDetail.recordId) {
            const path = `menu/activities/${taskDetail.module}/${taskDetail.recordId}`;
            this.menuService.navigate(path, Module[taskDetail.module]);
          }
        },
        error: (error) => {
          console.error('Can´t obtain id from activity: ', error);
        }
      });
    } else {
      this.service.get({ cancelableReq: this.$destroy, url: `activities/${item.activityPlannedModule}/searchActivityId/${tag.trim()}` }).subscribe({
        next: (result: any) => {
          if (result.activityId === 0) {
            this.dialogService.info(this.translate.instant('root.modules.menu.menu-main.codeNotExist') + tag);
          } else {
            const path = `menu/activities/${item.activityPlannedModule}/${result.activityId}`;
            this.menuService.navigate(path, Module[item.activityPlannedModule]);
          }
        },
        error: (error) => {
          console.error('Can´t obtain id from activity: ', error);
        }
      });
    }
  }

  protected afterWindowResized(_event: Event) {
    if (this.px_ratio >= 1.5) {
      this.shouldScale = true;
    } else {
      this.shouldScale = false;
    }
    this.cdr.detectChanges();
  }

  updateTimeLabels(event: StopwatchEndTimeData) {
    if (event.hasEnableStopwatch) {
      this.isStopwatcherRunning = event.hasEnableStopwatch;
      this.isStopwatchRunningMessage = this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatch-running', {
        date: DateUtil.format(event.date, 'DD/MM/YYYY')
      });
      this.timesheetService.updateUIStopwatchService(event.hasEnableStopwatch);
    } else {
      event.end = TimesheetWidgetUtils.fixDate(event.end);
      this.service.post({ url: `timesheet/updateStopwatch/${event.timesheetId}`, cancelableReq: this.$destroy, postBody: event }).subscribe({
        next: (response) => {
          if (response) {
            this.isStopwatcherRunning = event.hasEnableStopwatch;
            this.load(this.currentDate);
            this.timesheetService.updateUIStopwatchService(event.hasEnableStopwatch);
          }
        },
        error: (error) => {
          console.error(error);
        }
      });
    }
  }

  isTodayTimesheet(): boolean {
    if (DateUtil.isToday(this.currentDate)) {
      return true;
    }
    return false;
  }

  showTooltipCalendar(): void {
    if (this.busy) {
      return;
    }
    this.visibleTooltipCalendar = !this.visibleTooltipCalendar;
    this.cdr.detectChanges();
  }

  private setTitleTooltipDate(date: Date) {
    this.titleTooltipDate = DateUtil.format(date, 'MMMM  YYYY', false, this.getLang());
  }

  onTooltipCalendarSelected(obj: LocalValueChangedInfo): void {
    this.load(obj.value, true);
    this.setWidgetDate(obj.value);
    this.setTitleTooltipDate(obj.value);
    if (DateUtil.isToday(obj.value)) {
      this.titleCurrentDate = this.translate.instant('pipe.today-label');
    } else {
      this.titleCurrentDate = this.datePipe.transform(new Date(this.currentDate), 'dd/MM/yyyy').substring(0, 5);
    }
  }

  private handleTooltipCalendarOverlay(remove: boolean): void {
    if (remove) {
      this.renderer2.removeClass(this.tooltipCalenderOverlay().nativeElement, 'tooltip-calendar-overlay');
    } else {
      this.renderer2.addClass(this.tooltipCalenderOverlay().nativeElement, 'tooltip-calendar-overlay');
    }
  }

  private selectDateTooltipCalendar(): void {
    this.tooltipCalendar().value = this.currentDate;
    this.cdr.detectChanges();
  }

  private updateLabelsValidationStopwatch(): void {
    this.timesheetService.updateUIStopwatch.pipe(takeUntil(this.$destroy)).subscribe({
      next: (response: UpdateUIStopwatch) => {
        if (!response.update) {
          this.isStopwatcherRunning = false;
        }
      }
    });
  }

  public closeTooltipCalendar(): void {
    this.visibleTooltipCalendar = false;
    this.cdr.detectChanges();
  }

  private checkPassDaysStopwatch(): void {
    const userId = Session.getUserId();
    if (userId === null || typeof userId === 'undefined' || userId <= 0 || !NumberUtil.isInteger(userId)) {
      return;
    }
    this.service.get({ url: `timesheet/timesheet-stopwatch/check/${userId}`, cancelableReq: this.$destroy }).subscribe((stopwatches: TimesheetWithStopwatchDto[]) => {
      if (stopwatches.length > 0) {
        this.isStopwatcherRunning = stopwatches[0]?.hasEnableStopwatch;
        if (this.isStopwatcherRunning) {
          this.isStopwatchRunningMessage = this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatch-running', {
            date: DateUtil.format(stopwatches[0]?.date, 'DD/MM/YYYY')
          });
        }
      }
    });
  }

  private defineMenuOptions(isUnplannedAvaliable = false, isFeatureAvailable = false) {
    return [
      {
        text: '',
        value: TimesheetAction.REGISTER_UNPLANNED,
        iconName: 'add',
        hidden: !isUnplannedAvaliable
      },
      {
        text: '',
        value: TimesheetAction.OPEN_CALENDAR,
        iconName: 'date_range',
        hidden: !isFeatureAvailable
      }
    ];
  }
}
