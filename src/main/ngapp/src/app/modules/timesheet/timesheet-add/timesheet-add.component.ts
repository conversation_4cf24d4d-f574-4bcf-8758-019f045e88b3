import { BnextCoreComponent, type i18n } from '@/core/bnext-core.component';
import { ConfigApp } from '@/core/local-storage/config-app';
import { LocalStorageItem } from '@/core/local-storage/local-storage-enums';
import { Session } from '@/core/local-storage/session';
import { NoticeService } from '@/core/services/notice.service';
import { randomUUID } from '@/core/utils/crypto-utils';
import * as DateUtil from '@/core/utils/date-util';
import * as DomUtil from '@/core/utils/dom-util';
import { cloneObject } from '@/core/utils/object';
import { ProfileServices } from '@/shared/roles/profiles/utils/profile-services.enums';
import { CommonModule, DatePipe } from '@angular/common';
import { type AfterViewInit, Component, type OnDestroy, type OnInit, type QueryList, ViewChildren, inject, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import {
  IgxButtonModule,
  type IgxDialogComponent,
  IgxDialogModule,
  IgxDividerModule,
  IgxIconModule,
  IgxInputGroupModule,
  IgxProgressBarModule,
  IgxRippleModule,
  IgxTabsModule,
  type IgxTimePickerComponent,
  IgxTimePickerModule,
  PickerInteractionMode
} from '@infragistics/igniteui-angular';
import { DxAccordionComponent, DxAccordionModule } from 'devextreme-angular/ui/accordion';
import { DxCheckBoxModule } from 'devextreme-angular/ui/check-box';
import { DxContextMenuModule } from 'devextreme-angular/ui/context-menu';
import { DxDraggableModule } from 'devextreme-angular/ui/draggable';
import { DxoAppointmentDraggingComponent, type DxoScrollingComponent } from 'devextreme-angular/ui/nested';
import { DxPopupModule } from 'devextreme-angular/ui/popup';
import { DxSchedulerComponent, DxSchedulerModule } from 'devextreme-angular/ui/scheduler';
import { DxScrollViewModule } from 'devextreme-angular/ui/scroll-view';
import { DxSliderModule } from 'devextreme-angular/ui/slider';
import { type DxSpeedDialActionComponent, DxSpeedDialActionModule } from 'devextreme-angular/ui/speed-dial-action';
import { DxTagBoxModule } from 'devextreme-angular/ui/tag-box';
import { DxTooltipModule } from 'devextreme-angular/ui/tooltip';
import Resizable from 'devextreme/ui/resizable';
import type {
  Appointment,
  AppointmentClickEvent,
  AppointmentDblClickEvent,
  AppointmentDeletedEvent,
  AppointmentDeletingEvent,
  AppointmentUpdatedEvent,
  AppointmentUpdatingEvent
} from 'devextreme/ui/scheduler';
import { FileUploadModule } from 'ng2-file-upload';
import { takeUntil } from 'rxjs/operators';
import { DropFileHandlerComponent } from 'src/app/core/drop-file/drop-file.component';
import type { FileSelectOptions, IFileData, LinkedFileEvt } from 'src/app/core/drop-file/drop-file.interfaces';
import { DropdownButtonComponent } from 'src/app/core/dropdown-button/dropdown-button.component';
import type { DropdownButtonItem } from 'src/app/core/dropdown-button/dropdown-button.interfaces';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';
import { BnextTranslateModule } from 'src/app/core/i18n/bnext-translate.module';
import { LocalStorageSession } from 'src/app/core/local-storage/local-storage-session';
import { SelectComponent } from 'src/app/core/select/select.component';
import { AppService } from 'src/app/core/services/app.service';
import { DateInterval, DayInMonth } from 'src/app/core/utils/date-util';
import { EnumUtil } from 'src/app/core/utils/enum-util';
import { ErrorHandling } from 'src/app/core/utils/error-handling';
import type { TextLongValue } from 'src/app/core/utils/text-has-value';
import type { PlannedActivityValue } from 'src/app/shared/activities/core/utils/activity-planner.interfaces';
import type { ClientValue, TextPlannerValue } from 'src/app/shared/planner/planner-task/planner-task.interfaces';
import { TimesheetStopwatchComponent } from '../timesheet-stopwatch/timesheet-stopwatch.component';
import { PlannedType } from '../timesheet-widget/timesheet-widget.enums';
import type { StopwatchEndTimeData, TimesheedAddedEvent, TimesheedRemovedEvent, TimesheetDto, UpdateUIStopwatch } from '../timesheet-widget/timesheet-widget.interfaces';
import { TimesheetWidgetUtils } from '../timesheet-widget/timesheet-widget.utils';
import { TimesheetService } from './../../timework/services/timesheet.service';
import { SchedulerZoom } from './timesheet-add-directive';
import type { TimesheetRegistry } from './timesheet-add.interfaces';
import type { TimesheetDataSourceDto } from './timesheet-datasource';

@Component({
  selector: 'app-timesheet-add',
  templateUrl: './timesheet-add.component.html',
  styleUrls: ['./timesheet-add.component.scss'],
  imports: [
    CommonModule,
    DxAccordionModule,
    DxCheckBoxModule,
    DxContextMenuModule,
    DxDraggableModule,
    DxSchedulerModule,
    DxScrollViewModule,
    DxSliderModule,
    DxTagBoxModule,
    IgxButtonModule,
    IgxRippleModule,
    DropdownButtonComponent,
    IgxDialogModule,
    IgxInputGroupModule,
    IgxProgressBarModule,
    IgxIconModule,
    SelectComponent,
    TimesheetStopwatchComponent,
    IgxTabsModule,
    IgxTimePickerModule,
    ReactiveFormsModule,
    FormsModule,
    DxSpeedDialActionModule,
    SchedulerZoom,
    DxTooltipModule,
    IgxDividerModule,
    DropFileHandlerComponent,
    FileUploadModule,
    DxPopupModule,
    BnextTranslateModule
  ]
})
export class TimesheetAddComponent extends BnextCoreComponent implements AfterViewInit, OnDestroy, OnInit, i18n {
  service = inject(AppService);
  noticeService = inject(NoticeService);

  datePipe = inject(DatePipe);

  fb = inject(UntypedFormBuilder);
  timesheetService = inject(TimesheetService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'modules.timesheet',
    componentName: 'timesheet-add'
  };

  hourFormat = 'HH:mm';
  LangConfig = TimesheetAddComponent.LANG_CONFIG;
  public get customTagName(): string {
    return TimesheetAddComponent.LANG_CONFIG.componentName;
  }

  private _componentPath = this.LangConfig.componentPath;

  set componentPath(value: string) {
    this._componentPath = value;
  }

  override get componentPath(): string {
    return this._componentPath;
  }

  get availableHeightPx(): number {
    return this.containerSizePx - 48;
  }

  //Para calcular el tamaño disponible que usara el Scheduler de ts/add
  get availableWidthTsAdd(): number {
    // ToDo: Agregar comentarios explicando porque se multiplica por `0.346`
    return this.containerSizeWidth - this.containerSizeWidth * 0.346;
  }

  busy = true;
  cellDuration = +LocalStorageSession.getValue(LocalStorageItem.TS_CELL_DURATION) || 10;
  cellDurations: TextLongValue[] = [
    {
      text: '05 minutos',
      value: 5
    },
    {
      text: '10 minutos',
      value: 10
    },
    {
      text: '15 minutos',
      value: 15
    },
    {
      text: '30 minutos',
      value: 30
    }
  ];
  items: TextPlannerValue[] = [];
  clients: ClientValue[] = [];
  currentDate = DateUtil.today();
  currentView: 'day' | 'week' | 'month' = 'day';
  dayCount = 3;
  dragItemsClone = true;
  draggingGroupName = 'Tareas';
  filteredPlanners = [];
  filteredTasks = [];
  initialized = false;
  alreadyHere = false; // <-- Se utiliza para no ejecutar "scrollTo" cada vez que se hacen cambios
  accordionInitialized = false; // <-- Se utiliza para no ejecutar "collapseItem" cada vez que se hacen cambios
  lastDayValue = DateUtil.today();
  planners: TextPlannerValue[] = [];
  rangeEnd: Date = DateUtil.add(this.currentDate, DateInterval.DAY, this.dayCount);
  rangeStart: Date = this.currentDate;
  tasks: TextPlannerValue[] = [];
  tasksActivities: TextPlannerValue[] = [];
  timesheetRegistries: TimesheetRegistry[] = [];
  // Menú contextual
  onContextMenuItemClick: any;
  contextMenuDataSource: any[] = [];
  contextMenuDisabled = true;
  contextMenuTarget: any;
  cellContextMenuItems: any[];
  appointmentContextMenuItems: any[];
  newRecordsActions: DropdownButtonItem[] = [];
  isOutsideTheLimit = true;
  isTooltipVisible = false;
  addingFile = false;
  files: IFileData[] = [];
  timesheetRegistrySelected: AppointmentClickEvent = null;
  closeOnOutsideClick = true;
  dataSource: TimesheetDataSourceDto = null;
  selectedMainItem = 0;
  selectedNewButton: string = null;
  public mode: PickerInteractionMode = PickerInteractionMode.Dialog;
  // total hours
  hoursWorked: string[] = [];
  private lastTimesheetRegistrySelected: TimesheetRegistry;
  visibleTooltipConfiguration = false;
  deletedFromStopwath = false;
  private onlyUpdateLocalAppointment = false;
  existRegisterWithStopwtach = false;
  private registerWithStopwatchDate: Date;
  timepickerClicked = false;
  showSubFloatting = false;
  startTimeLabel = 'Inicio';
  endTimeLabel = 'Fin';

  readonly settingsDialog = viewChild<IgxDialogComponent>('settingsDialog');

  // TODO: Skipped for migration because:
  //  There are references to this query that cannot be migrated automatically.
  @ViewChildren(DxAccordionComponent)
  public accordions: QueryList<DxAccordionComponent>;
  readonly scheduler = viewChild(DxSchedulerComponent);

  // TODO: Skipped for migration because:
  //  There are references to this query that cannot be migrated automatically.
  @ViewChildren(DxoAppointmentDraggingComponent)
  public dragging: QueryList<DxoAppointmentDraggingComponent>;

  readonly dropFile = viewChild('dropFile', { read: DropFileHandlerComponent });

  readonly speedDialAction = viewChild<DxSpeedDialActionComponent>('speedDialAction');

  readonly dialogInfoMobilDevice = viewChild<IgxDialogComponent>('dialogInfoMobilDevice');

  readonly dxoScrolling = viewChild<DxoScrollingComponent>('dxoScrolling');

  override ngOnInit(): void {
    super.ngOnInit();
    this.setup();
    this.timesheetService.updateUIStopwatch.pipe(takeUntil(this.$destroy)).subscribe((response: UpdateUIStopwatch) => {
      if (!response.update) {
        this.existRegisterWithStopwtach = false;
        this.registerWithStopwatchDate = null;
        if (this.lastTimesheetRegistrySelected !== null) {
          this.lastTimesheetRegistrySelected.endDate = DateUtil.safe(response.dto.end);
          this.lastTimesheetRegistrySelected.hasEnableStopwatch = false;
          this.lastTimesheetRegistrySelected.registry = response.dto.registry;
          this.lastTimesheetRegistrySelected.text = response.dto.registry;
          if (response.repaintScheduler) {
            this.repaintScheduler(this.lastTimesheetRegistrySelected);
          }
        }
      }
    });
    this.timesheetService.onRepaintScheduler.pipe(takeUntil(this.$destroy)).subscribe((registry: TimesheetRegistry) => {
      this.repaintScheduler(registry);
    });
  }

  private setDropdownButtonElementByServices() {
    if (!Session.getServices().includes(ProfileServices.TS_REGISTER_PLANNED)) {
      this.newRecordsActions.push({ value: PlannedType.UNPLANNED, text: this.translate.instant('new-unplanned'), iconName: 'add' });
    } else if (!Session.getServices().includes(ProfileServices.TS_REGISTER_UNPLANNED)) {
      this.newRecordsActions.push({ value: PlannedType.PLANNED, text: this.translate.instant('new-planned'), iconName: 'add' });
    } else {
      this.newRecordsActions.push(
        { value: PlannedType.PLANNED, text: this.translate.instant('new-planned'), iconName: 'add' },
        { value: PlannedType.UNPLANNED, text: this.translate.instant('new-unplanned'), iconName: 'add' }
      );
    }
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.dragging.changes.pipe(takeUntil(this.$destroy)).subscribe((dragElements: QueryList<DxoAppointmentDraggingComponent>) => {
      if (dragElements.length) {
        for (const d of dragElements) {
          this.patchDxAppointment(d);
        }
      }
    });
    this.accordions.changes.pipe(takeUntil(this.$destroy)).subscribe((accordion: QueryList<DxAccordionComponent>) => {
      if (!(accordion.length && !this.accordionInitialized)) {
        return;
      }
      const accordionArray = accordion.toArray();
      for (let acIndex = 0, l = accordionArray.length; acIndex < l; acIndex++) {
        if (acIndex <= 0) {
          continue;
        }
        const a = accordionArray[acIndex];
        for (const _item of a.items) {
          const index = a.items.indexOf(_item);
          a.instance.collapseItem(index);
        }
        this.accordionInitialized = true;
      }
    });
  }

  /**
   * Parches:
   * https://github.com/DevExpress/devextreme-angular/issues/1164
   */
  private patchDxAppointment(dragElement: DxoAppointmentDraggingComponent): void {
    if (dragElement) {
      if (dragElement.onAdd === null || typeof dragElement.onAdd === 'undefined') {
        dragElement.onAdd = this.onAdd;
      }
      if (dragElement.onRemove === null || typeof dragElement.onRemove === 'undefined') {
        dragElement.onRemove = this.onRemove;
      }
    } else {
      console.error('dragElement could not be patched! drag & drop will not work :(');
    }
  }

  override langReady() {
    this.setDropdownButtonElementByServices();
    this.startTimeLabel = this.tag('startTime');
    this.endTimeLabel = this.tag('endTime');
  }

  catalogReady() {
    // ToDo
  }

  /**
   * Evento que se dispara cuando se hace suelta una tarea del panel izquierdo en el scheduler
   * @param event Información de la tarea
   */

  private onAdd = (event: TimesheedAddedEvent): void => {
    if (this.busy) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'busyMessage'));
      return;
    }
    if (this.existRegisterWithStopwtach) {
      this.noticeService.notice(
        this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatch-running', { date: DateUtil.format(this.registerWithStopwatchDate, 'DD/MM/YYYY') })
      );
      return;
    }
    //Cuando se arrastra una actividad planeada solo con cliente
    if (
      event.fromData.clientId !== null &&
      (event.fromData.plannerId === null || typeof event.fromData.plannerId === 'undefined') &&
      (event.fromData.activityId === null || typeof event.fromData.activityId === 'undefined')
    ) {
      this.openTimesheetToFillPartialPlannerActivities(event);
      return;
    }
    //Cuando se arrastra una actividad planeada solo con cliente - proyecto
    if (
      event.fromData.clientId !== null &&
      (event.fromData.plannerId !== null || typeof event.fromData.plannerId !== 'undefined') &&
      (event.fromData.activityId === null || typeof event.fromData.activityId === 'undefined')
    ) {
      this.openTimesheetToFillPartialPlannerActivities(event);
      return;
    }
    this.onAddAppointment(event);
  };

  private onAddAppointment(event: TimesheedAddedEvent, sync = true): void {
    const registry = event.fromData;
    const isTask = registry.pendingRecordId === null || typeof registry.pendingRecordId === 'undefined';
    let taskIndex: number;
    if (isTask) {
      taskIndex = this.tasks.findIndex((t) => t.value === registry.value);
    } else {
      taskIndex = this.tasksActivities.findIndex((t) => t.value === registry.value);
    }
    if (taskIndex >= 0) {
      let t: TimesheetRegistry;
      if (isTask) {
        t = Object.assign(cloneObject(this.tasks[taskIndex]), event.itemData);
      } else {
        t = Object.assign(cloneObject(this.tasksActivities[taskIndex]), event.itemData);
        t.systemLinkId = registry.mainSystemId;
        t.systemLinkValue = registry.mainSystemLinkValue;
        t.systemLinkLabel = registry.mainSystemLinkLabel;
        t.clientRecordLocked = true;
        t.plannerRecordLocked = true;
        t.taskRecordLocked = true;
        t.value = registry.activityId;
        t.tags = [t.activityPlannedCode];
        t.registry = registry.text;
      }
      t.temporalId = this.getUuid();
      t.showAppointmentPickerTime = t.workedMinutes >= 30;
      if (t.systemLinkAvailable && (t.systemLinkValue === null || typeof t.systemLinkValue === 'undefined')) {
        t.systemLinkValue = '';
      }
      if (this.computeIsOutsideTheLimit(t.startDate)) {
        this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
        return;
      }
      if (this.timesheetRegistries.length >= 1 && this.isSplicedRecord(t)) {
        this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'splicedRecord'));
        return;
      }
      if (sync) {
        t.endDate = this.fixMinTime(DateUtil.safe(t.startDate), DateUtil.safe(t.endDate));
      }
      t.date = t.endDate as Date;
      if (!this.isValidEndDate(DateUtil.safe(t.startDate), DateUtil.safe(t.endDate))) {
        const endDate = DateUtil.safe(t.startDate);
        endDate.setMinutes(55);
        t.endDate = endDate;
      }
      //Cuando es un registro no planeado.
      if (typeof t.activityPlannedId === 'undefined' || t.activityPlannedId === null) {
        t.clientDescription = this.clients.find((t) => t.value === registry.clientId)?.text.toString() || '';
        t.plannerDescription = this.planners.find((t) => t.value === registry.plannerId)?.text.toString() || '';
        t.activityDescription = registry.text;
      }
      this.timesheetRegistries.push(t);
      if (sync) {
        this.syncRegistries(t);
      }
    }
  }

  /**
   * Método que valida el tiempo mínimo que debe tener un appointment (Parche cuando se cambia cellDuration)
   * @param s Hora inicio
   * @param e Hora fin
   */
  private fixMinTime(s: Date, e: Date): Date {
    const min = Math.abs(e.getTime() - s.getTime());
    const m = Math.floor(min / 1000 / 60);
    if (m < +this.cellDuration || m > +this.cellDuration) {
      const minToAdd = +this.cellDuration;
      const endD = new Date(s).setMinutes(s.getMinutes() + minToAdd);
      return DateUtil.safe(endD);
    }
    return e;
  }

  private onRemove = (event: TimesheedRemovedEvent) => {
    if (this.busy) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'busyMessage'));
      return;
    }
    const registry = event.fromData;
    const taskIndex = this.timesheetRegistries.findIndex((t) => t.temporalId === registry.temporalId);
    if (taskIndex >= 0 && registry.timesheetId) {
      this.timesheetRegistries.splice(taskIndex, 1);
      this.syncRegistriesRemove(registry.timesheetId);
    }
  };

  private getFileIds(registry: TimesheetRegistry): number[] {
    return (registry.fileData || []).map((f) => f.id) || [];
  }

  private getTimesheetDto(registry: TimesheetRegistry): TimesheetDto {
    return {
      systemLinkId: registry.mainSystemId || null,
      systemLinkValue: registry.systemLinkValue || null,
      systemLinkLabel: registry.mainSystemLinkLabel || null,
      clientId: registry.clientId || null,
      clientDescription: registry.clientDescription || null,
      plannerId: registry.plannerId || null,
      plannerDescription: registry.plannerDescription || null,
      timesheetId: registry.timesheetId || -1,
      fileIds: this.getFileIds(registry),
      activityId: registry.activityId || null,
      activityDescription: registry.plannerDescription || null,
      pendingRecordId: registry.pendingRecordId || null,
      registry: registry.description || registry.text,
      uid: registry.uid || null,
      //Se crea una nueva referencia de la propiedad para no afectar las fechas de start y end
      date: DateUtil.trunc(new Date(registry.startDate)),
      start: DateUtil.safe(registry.startDate as Date, true),
      end: DateUtil.safe(registry.endDate as Date, true),
      tags: registry.tags || [],
      clientRecordLocked: registry.clientRecordLocked,
      plannerRecordLocked: registry.plannerRecordLocked,
      taskRecordLocked: registry.plannerRecordLocked && registry.taskRecordLocked,
      activityPlannedId: registry.activityPlannedId || null,
      activityPlannedCode: registry.activityPlannedCode || null,
      activityPlannedDescription: registry.activityPlannedDescription || null,
      activityPlannedModule: registry.activityPlannedModule || null,
      hasEnableStopwatch: registry.hasEnableStopwatch || false
    };
  }

  /**
   * Método para remover un registro de timesheet
   * @param idTimesheet ID del registro a eliminar
   */
  private syncRegistriesRemove(idTimesheet: number): void {
    this.lastTimesheetRegistrySelected = null;
    if (idTimesheet === null) {
      return;
    }
    this.busy = true;
    this.alreadyHere = true;
    this.service
      .post({ url: `timesheet/add/sync-remove/${idTimesheet}`, cancelableReq: this.$destroy, postBody: null })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (error) => {
          this.busy = false;
          if (error) {
            if (error.status === 409 && error.error === 'outof-limit') {
              this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
            } else {
              this.noticeService.notice(error);
            }
          } else {
            this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'sucessSync'));
            this.calculateHoursWorkedDaily();
          }
          this.cdr.detectChanges();
        },
        error: () => {
          // fail
          this.busy = false;
          this.cdr.detectChanges();
        }
      });
  }

  /**
   * Método que sincroniza los registros (Agrega y actualiza)
   * @param registry Objeto con los datos a actualizar o agregar
   */
  private syncRegistries(registry: TimesheetRegistry): void {
    registry.businessUnitDescription = Session.getBusinessUnitDepartmentName();
    this.lastTimesheetRegistrySelected = registry;
    this.isTooltipVisible = false;
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
    if (registry.busy) {
      return;
    }
    registry.busy = true;
    this.busy = true;
    this.alreadyHere = true;
    const data = this.getTimesheetDto(registry);
    if (this.computeIsOutsideTheLimit(data.start)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
      this.busy = false;
      return;
    }
    this.service
      .post({ url: 'timesheet/add/sync-update', cancelableReq: this.$destroy, postBody: data })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (dataSource: TimesheetDto) => {
          registry.busy = false;
          // Actualizamos el id del Registro en el listado de registros
          const index = this.timesheetRegistries.findIndex((t) => t.timesheetId === registry.timesheetId);
          if (index >= 0) {
            this.timesheetRegistries[index].temporalId = dataSource.timesheetId.toString();
            this.timesheetRegistries[index].timesheetId = dataSource.timesheetId;
            this.timesheetRegistries[index].registryTime = TimesheetWidgetUtils.calculateDurationTime(dataSource.workedMinutes, dataSource.start, dataSource.end);
          }
          this.castAsModelTemplate(this.timesheetRegistries[index]);
          this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'sucessSync'));
          this.busy = false;
          this.calculateHoursWorkedDaily();
          registry.showAppointmentPickerTime = dataSource.workedMinutes >= 30;
          this.cdr.detectChanges();
        },
        error: (error) => {
          this.busy = false;
          if (registry) {
            registry.busy = false;
          }
          if (error.status === 409 && error.error === 'outof-limit') {
            this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
            return;
          }
          if (error.status === 409 && error.error === 'dates-spliced') {
            this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'splicedRecord'), true);
            //si hay un registro empalmado recarga el componente, ya que el metodo repaint no recarga los datos del scheduler.
            this.menuService.navigate('menu/timesheet/add');
            return;
          }
          if (error.status === 409 && error.error.stopwatch_running_message === 'stopwatch-dates-spliced') {
            const registryDate = error.error.registry[0].date;
            this.noticeService.notice(
              this.translate.instantFrom(TimesheetWidgetUtils.LANG_CONFIG, 'stopwatch-running', { date: DateUtil.format(registryDate, 'DD/MM/YYYY') })
            );
            this.busy = false;
            this.cdr.detectChanges();
            return;
          }
          if (ErrorHandling.isAppOffline(error)) {
            this.busy = false;
            this.cdr.detectChanges();
          } else if (error.error !== 'TIMEWATCHER_RUNNING') {
            ErrorHandling.notifyError(error, this.navLang);
            this.setup();
          }
        }
      });
  }

  /**
   * Coloca las tareas que le pertenecen a cada proyecto
   * @param dataSource
   */
  private setPlanners(dataSource: TimesheetDataSourceDto): void {
    this.planners = (dataSource.planners || []).map((planner: TextPlannerValue) => {
      planner.tasks = (dataSource.tasks || []).filter((task) => task.plannerId === planner.value);
      return planner;
    });
  }

  /**
   * Coloca los proyectos que le pertenecen a cada cliente
   * @param dataSource
   */
  private setClients(dataSource: TimesheetDataSourceDto): void {
    this.clients = (dataSource.clients || []).map((client: ClientValue) => {
      // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
      client['tasks'] = (dataSource.planners || []).filter((planner) => planner.clientId === client.value);
      return client;
    });
  }

  /**
   * Inicializa el 1er nivel del acordion con clientes y pendientes de activcidades
   * @param dataSource
   */
  private setItems(dataSource: TimesheetDataSourceDto): void {
    if (dataSource.tasksActivities) {
      for (const task of dataSource.tasksActivities) {
        task.tasks = [];
      }
    } else {
      dataSource.tasksActivities = [];
    }
    this.tasksActivities = dataSource.tasksActivities;
    const completeTasksActivities = dataSource.tasksActivities.filter((activity) => activity.activityId !== null);
    const plannedItemsConfig: PlannedActivityValue = {
      mainSystemId: null,
      mainSystemUrl: null,
      mainSystemRegExp: null,
      mainSystemLinkLabel: null,
      mainSystemLinkValue: null,
      systemLinkAvailable: false,
      activityId: null,
      clientId: null,
      plannerId: null,
      pendingRecordId: -1,
      uid: this.getUuid(),
      text: this.i18n[PlannedType.PLANNED] as string,
      value: PlannedType.PLANNED,
      tasks: completeTasksActivities
    };
    const unplannedItems: PlannedActivityValue = {
      mainSystemId: null,
      mainSystemUrl: null,
      mainSystemRegExp: null,
      mainSystemLinkLabel: null,
      mainSystemLinkValue: null,
      systemLinkAvailable: false,
      activityId: null,
      clientId: null,
      plannerId: null,
      pendingRecordId: -1,
      uid: this.getUuid(),
      text: this.i18n[PlannedType.UNPLANNED] as string,
      value: PlannedType.UNPLANNED,
      tasks: this.clients as TextPlannerValue[]
    };
    if (!Session.getServices().includes(ProfileServices.TS_REGISTER_PLANNED)) {
      this.items = [unplannedItems];
    } else if (!Session.getServices().includes(ProfileServices.TS_REGISTER_UNPLANNED)) {
      this.items = [plannedItemsConfig];
    } else {
      this.items = [plannedItemsConfig, unplannedItems];
    }
    if (completeTasksActivities && completeTasksActivities.length > 0) {
      this.selectedMainItem = 0;
      this.selectedNewButton = PlannedType.PLANNED;
    } else {
      this.selectedMainItem = 1;
      this.selectedNewButton = PlannedType.UNPLANNED;
    }
  }

  /**
   * Inicializa el arreglo que se utilizará para llenar "appointments"
   * @param dataSource
   */
  private setTasks(dataSource: TimesheetDataSourceDto): void {
    this.tasks = (dataSource.planners || []).concat(dataSource.tasks || []).map((t) => {
      t.uid = this.getUuid();
      return t;
    });
  }

  private getUuid(): string {
    return `${randomUUID()}`;
  }

  private setTimesheetRegistries(registries: TimesheetDto[]): void {
    const saved: TimesheetRegistry[] = (registries || []).map((t: TimesheetDto) => {
      const registryTime = TimesheetWidgetUtils.calculateDurationTime(t.workedMinutes, t.start, t.end) || null;
      return Object.assign(cloneObject(t), {
        systemLinkValue: t.systemLinkValue || null,
        systemLinkId: t.systemLinkId || null,
        systemLinkLabel: t.systemLinkLabel || null,
        mainSystemId: t.systemLinkId || null,
        mainSystemUrl: t.systemLinkLabel || null,
        mainSystemRegExp: t.systemLinkValue || null,
        systemLinkAvailable: !!t.systemLinkId,
        plannerId: t.plannerId || null,
        timesheetId: t.timesheetId,
        uid: t.uid,
        registryTime: registryTime,
        text: t.registry,
        description: t.registry,
        startDate: DateUtil.safe(t.start),
        endDate: DateUtil.safe(t.end),
        visible: true,
        pendingRecordId: t.pendingRecordId || null,
        systemLinkUrl: t.systemLinkUrl,
        date: t.date,
        showAppointmentPickerTime: t.workedMinutes >= 30,
        hasEnableStopwatch: t.hasEnableStopwatch
      });
    });
    let idx: number;
    if (!this.initialized) {
      this.timesheetRegistries = saved;
    } else {
      for (const r of saved) {
        idx = this.getRegistryIndex(r);
        if (idx === -1) {
          r.temporalId = this.getUuid();
          //Para no generar appointments duplicados
          if (!this.timesheetRegistries.find((f) => f.timesheetId === r.timesheetId)) {
            this.timesheetRegistries.push(r);
          }
        } else {
          Object.assign(this.timesheetRegistries[idx], r);
        }
      }
    }
    console.log('setTimesheetRegistries ', this.timesheetRegistries);
  }

  private getRegistryIndex(registry: TimesheetRegistry): number {
    if (!registry.uid) {
      return -1;
    }
    return this.timesheetRegistries.findIndex(
      (r) =>
        (r.uid === registry.uid && DateUtil.safe(r.startDate as Date).getTime() === DateUtil.safe(registry.startDate as Date).getTime()) ||
        (r.timesheetId === registry.timesheetId && r.timesheetId !== -1 && r.timesheetId)
    );
  }

  private updateRanges(d = DateUtil.today()): void {
    const date = new Date(DateUtil.safe(d).getTime());
    switch (this.currentView.toLowerCase()) {
      case 'week':
        // Se muestran los registros de acuerdo a los días mostrados en el scheduler (Lunes - Domingo)
        this.rangeStart = DateUtil.getDayInWeek(1, date); // Lunes
        this.rangeEnd = DateUtil.add(DateUtil.getDayInWeek(6, date), DateInterval.DAY, 1); // Domingo
        break;
      case 'day':
        this.currentDate = date;
        this.rangeStart = this.currentDate;
        this.rangeEnd = DateUtil.add(this.currentDate, DateInterval.DAY, this.dayCount - 1);
        break;
      case 'month':
        this.rangeStart = DateUtil.getDayInMonth(DayInMonth.FIRST, date);
        this.rangeEnd = DateUtil.getDayInMonth(DayInMonth.LAST, date);
        break;
    }
    this.cdr.detectChanges();
    console.log('updated ranges: ', this.rangeStart, ' -> ', this.rangeEnd);
  }

  private getRangesApi(date = DateUtil.today()): string {
    this.updateRanges(date);
    return `timesheet/add/data-source/${DateUtil.format(this.rangeStart, 'YYYYMMDD')}/${DateUtil.format(this.rangeEnd, 'YYYYMMDD')}`;
  }

  computeIsOutsideTheLimit(target: number | string | Date = this.currentDate): boolean {
    if (target === null || typeof target === 'undefined') {
      return false;
    }
    const targetDate = DateUtil.safe(target);
    if (targetDate === null || typeof targetDate === 'undefined') {
      return false;
    }
    return ConfigApp.getTimeLimitToModifyTimesheet() > targetDate;
  }
  setup(date = this.currentDate, updateCatalogs = true) {
    this.busy = true;
    this.isOutsideTheLimit = this.computeIsOutsideTheLimit(date);
    this.service
      .get({ url: this.getRangesApi(date), cancelableReq: this.$destroy })
      .pipe(takeUntil(this.$destroy))
      .subscribe({
        next: (dataSource: TimesheetDataSourceDto) => {
          this.dataSource = dataSource;
          this.existRegisterWithStopwtach = dataSource.registryWithStopwatchEnable?.length > 0;
          if (this.existRegisterWithStopwtach) {
            this.registerWithStopwatchDate = dataSource.registryWithStopwatchEnable[0].date as Date;
          } else {
            this.registerWithStopwatchDate = null;
          }
          this.timesheetService.setupFromDataSource(this.dataSource);
          this.setTimesheetRegistries(dataSource.timesheetRegistries);
          this.calculateHoursWorkedDaily();
          this.setTasks(dataSource);
          if (updateCatalogs) {
            this.setPlanners(dataSource);
            this.setClients(dataSource);
            this.setItems(dataSource);
            this.catalogReady();
          }

          const scheduler = this.scheduler();
          if (scheduler?.instance && this.currentView === 'week') {
            // El scroll se mueve en automatico a la hora del primer registro del día solo en vista semana
            const firstItem = this.timesheetRegistries.find((t: TimesheetRegistry) => DateUtil.isSameDay(new Date(), new Date(t.startDate)));
            if (firstItem) {
              scheduler.instance.scrollTo(new Date(firstItem.startDate));
            } else {
              scheduler.instance.scrollTo(new Date());
            }
          }
          this.busy = false;
          this.initialized = true;
          this.cdr.detectChanges();
          const dxoScrolling = this.dxoScrolling();
          if (dxoScrolling && this.isMobilDevice()) {
            //Se escucha el evento touchmove para ocultar los botones flotantes al relizar "scroll" en dispositivos moviles
            dxoScrolling.instance.element().addEventListener('touchmove', () => {
              if (this.showSubFloatting) {
                this.showSubFloatting = false;
                this.cdr.detectChanges();
              }
            });
          }
        },
        error: (error) => {
          this.busy = false;
          throw error;
        }
      });
  }

  private loadTimesheetFilesCount(appointment: TimesheetRegistry): Promise<void> {
    return new Promise((succ) => {
      if (appointment?.fileData !== null && appointment?.fileData?.length > 0) {
        succ();
        return;
      }
      this.service
        .get({ url: `timesheet/timesheet-add-files-count/${appointment.timesheetId}`, cancelableReq: this.$destroy })
        .pipe(takeUntil(this.$destroy))
        .subscribe(
          (timesheetFilesCount: number[]) => {
            appointment.fileData = [];
            for (const item of timesheetFilesCount) {
              appointment.fileData.push({ description: '', id: item });
            }
            succ();
          },
          () => {
            succ();
          }
        );
    });
  }

  onContentReady() {
    const scheduler = this.scheduler();
    if (!scheduler?.instance || this.alreadyHere || !this.initialized) {
      return;
    }
    // El scroll se mueve en automatico a la hora actual
    scheduler.instance.scrollTo(new Date());
  }

  onAppointmentDeleted(e: AppointmentDeletedEvent) {
    if (e.appointmentData.busy) {
      return;
    }
    if (this.computeIsOutsideTheLimit(e.appointmentData.startDate)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
      return;
    }
    this.closeTooltips();
    this.syncRegistriesRemove(e.appointmentData.timesheetId);
  }

  /**
   * Evento onAppointmentDeleting para mostrar el dialogo de confirmación antes de eliminar el registro
   * Contiene los datos del appointment y el parámetro para cancelar el evento
   * @param e evento
   */
  onAppointmentDeleting(e: AppointmentDeletingEvent) {
    if (this.tryBlockTimesheetRegistry(e.appointmentData as TimesheetRegistry)) {
      return;
    }
    if (this.computeIsOutsideTheLimit(e.appointmentData.startDate)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
      e.cancel = true;
      return;
    }
    // Se agrega esta validación para evitar borrar el registro con el evento onAppointmentDeleted
    e.cancel = true;
    e.appointmentData.busy = true;
    if (!this.deletedFromStopwath) {
      this.closeTooltips();
      this.dialogService.confirm(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'confirm-message')).then(
        () => {
          e.cancel = true;
          const index = this.timesheetRegistries.findIndex((f) => f.timesheetId === e.appointmentData.timesheetId);
          if (index >= 0) {
            this.timesheetRegistries.splice(index, 1);
          }
          e.appointmentData.busy = false;
          if (e.appointmentData.hasEnableStopwatch) {
            this.timesheetService.updateUIStopwatchService(false, this.addTimesheetRegistryToTimesheetDto(e.appointmentData as TimesheetRegistry), false);
          }
          this.syncRegistriesRemove(e.appointmentData.timesheetId);
        },
        () => {
          // Se agrega al listado porque se elimina en cuanto se da click en el icono eliminar
          e.cancel = true;
          e.appointmentData.busy = false;
          this.scheduler().instance.scrollTo(new Date(e.appointmentData.startDate));
        }
      );
    }
  }

  onAppointmentUpdated(e: AppointmentUpdatedEvent) {
    // Updating.. Se reemplaza el registro en al listado
    const index = this.timesheetRegistries.findIndex((t) => +t.timesheetId === e.appointmentData.timesheetId);
    if (index >= 0) {
      this.timesheetRegistries.splice(index, 1);
      this.timesheetRegistries.push(e.appointmentData as TimesheetRegistry);
    }
    //Se agrega cuando esta validacion cuando solo se requiere actualizar el front,
    // en este caso fue actualizado desde el dialogo de timesheet en el tooltip
    if (!this.onlyUpdateLocalAppointment) {
      this.syncRegistries(e.appointmentData as TimesheetRegistry);
    } else {
      //se resetea la bandera
      this.onlyUpdateLocalAppointment = false;
      this.cdr.detectChanges();
    }
  }

  private tryBlockTimesheetRegistry(e: TimesheetRegistry): boolean {
    if (e.busy) {
      this.noticeService.notice('El registro que modificó está bloqueado, por favor espere a que la barra de progreso termine de guardar');
      return true;
    }
    return false;
  }

  /**
   * Evento onAppointmentUpdating para realizar validaciones de registros empalmados antes de actualizar
   * Contiene los datos anteriores (e.oldData) y nuevos (e.newData) del appointment
   * @param e evento
   */
  onAppointmentUpdating(e: AppointmentUpdatingEvent) {
    if (this.tryBlockTimesheetRegistry(e.newData)) {
      e.cancel = true;
      this.cdr.detectChanges();
      return;
    }
    if (e.newData.stopwatchReStarted) {
      e.cancel = true;
      this.cdr.detectChanges();
      return;
    }
    if (e.newData.hasEnableStopwatch) {
      this.noticeService.notice(this.translate.instant('timesheet-add-edit-validation'));
      e.cancel = true;
      this.cdr.detectChanges();
      return;
    }
    if (this.computeIsOutsideTheLimit(e.newData.startDate)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
      e.cancel = true;
      this.cdr.detectChanges();
      return;
    }
    // Fix para ajustar las horas (BUG del scheduler que resta minutos en zoom 67%)
    const startD = this.fixDates(new Date(e.newData.startDate));
    const endD = this.fixDates(new Date(e.newData.endDate));
    e.newData.startDate = startD;
    e.newData.endDate = endD;
    // Validación para saber si se capturó una fecha fin que corresponde a un día diferente a la fecha de inicio
    if (!this.isValidEndDate(new Date(e.newData.startDate), new Date(e.newData.endDate))) {
      const endD: Date = new Date(e.newData.startDate);
      endD.setHours(23);
      endD.setMinutes(55);
      e.newData.endDate = DateUtil.safe(endD);
    }
    // Validación registros empalmados
    if (this.timesheetRegistries.length >= 1 && this.isSplicedRecord(e.newData)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'splicedRecord'));
      e.cancel = true;
    }
    const oldStartD = DateUtil.safe(e.oldData.startDate);
    const oldEndD = DateUtil.safe(e.oldData.endDate);
    if (startD < oldStartD) {
      this.scheduler().instance.scrollTo(startD);
    } else if (endD > oldEndD) {
      this.scheduler().instance.scrollTo(DateUtil.safe(e.newData.endDate));
    }
    this.cdr.detectChanges();
  }

  private getTextArea(e: AppointmentDblClickEvent): HTMLTextAreaElement {
    const containerNode: HTMLElement = e.appointmentElement;
    const contentNode = containerNode.querySelector('.dx-scheduler-appointment-content');
    let textarea: HTMLTextAreaElement = containerNode.querySelector('textarea');
    if (!textarea) {
      textarea = document.createElement('textarea');
      contentNode.appendChild(textarea);
      textarea.title = 'Para agregar saltos de linea presiona "(Alt o Shift) + Enter"';
      // Debe ser "keyup" para que la última tecla utilizada llegue al evento
      textarea.onkeyup = (k: KeyboardEvent) => {
        e.appointmentData.description = e.appointmentData.registry = e.appointmentData.text = textarea.value;
        const auxiliarKey = k.altKey || k.shiftKey;
        const isEnter = k.key === 'Enter' || k.key === 'NumpadEnter';
        if (isEnter && auxiliarKey) {
          textarea.value += '\r\n'; // <-- ToDo: El enter debe agregarse en la posición del cursor (no al final)
        }
      };
      // Solo se guarda al ser "keydown" para que el caractér de "Enter" no se guarde
      textarea.onkeydown = (k: KeyboardEvent) => {
        const auxiliarKey = k.altKey || k.shiftKey;
        const isEnter = k.key === 'Enter' || k.key === 'NumpadEnter';
        if (isEnter && !auxiliarKey) {
          this.saveTextArea(e, textarea, containerNode);
        }
      };
      textarea.onblur = () => {
        this.saveTextArea(e, textarea, containerNode);
      };
    }
    return textarea;
  }

  onNewRecordAction(action: DropdownButtonItem) {
    const plannedType = EnumUtil.getValue(PlannedType, action.value, 'string');
    this.openNewRegistry(plannedType);
  }

  systemLinkFocus(systemLinkContainer: HTMLDivElement) {
    DomUtil.addClass(systemLinkContainer, 'focused');
  }

  systemLinkSave(systemLinkContainer: HTMLDivElement, systemLinkValue: string, e: AppointmentClickEvent) {
    e.appointmentData.systemLinkValue = (systemLinkValue || '').trim() || null;
    DomUtil.removeClass(systemLinkContainer, 'focused');
    this.saveTextArea(e);
  }

  castAsModelTemplate(model: any): AppointmentClickEvent {
    return model;
  }

  private saveTextArea(e: AppointmentClickEvent, textarea?: HTMLTextAreaElement, containerNode?: HTMLElement): void {
    if (textarea?.value && textarea.value.trim() === '') {
      this.noticeService.notice('El registro no puede estar vacio');
      return;
    }
    if (this.tryBlockTimesheetRegistry(e.appointmentData as TimesheetRegistry)) {
      return;
    }
    if (containerNode) {
      DomUtil.removeClass(containerNode, 'appointment-edit-mode');
    }
    // Se utiliza "updateAppointment" en lugar de "repaint" para no mover el scroll
    this.scheduler().instance.updateAppointment(e.appointmentData, e.appointmentData);
    this.cdr.detectChanges();
  }

  /**
   * Evento para evitar que aparezca la ventana que utiliza por defecto
   * el Scheduler para editar valores
   * @param e
   */
  onAppointmentFormOpening(e) {
    this.lastTimesheetRegistrySelected = null;
    e.cancel = true;
  }

  onAppointmentDblClick(e: AppointmentDblClickEvent) {
    this.lastTimesheetRegistrySelected = e.appointmentData as TimesheetRegistry;
    console.log('onAppointmentDblClick: ', e);
    if (this.tryBlockTimesheetRegistry(e.appointmentData as TimesheetRegistry) || e.appointmentData.hasEnableStopwatch) {
      return;
    }
    if (this.computeIsOutsideTheLimit(e.appointmentData.startDate)) {
      this.noticeService.notice(this.translate.instantFrom(TimesheetAddComponent.LANG_CONFIG, 'dateOutOfLimit'));
      return;
    }
    const containerNode: HTMLDivElement = e.appointmentElement as HTMLDivElement;
    const textarea = this.getTextArea(e);
    textarea.value = e.appointmentData.text;
    // const titleDomNode = containerNode.querySelector('.dx-scheduler-appointment-title');
    DomUtil.addClass(containerNode, 'appointment-edit-mode');
    textarea.focus();
    textarea.select();
  }

  // Evento al seleccionar vista o rango de fechas en el calendario
  onOptionChanged(e) {
    console.log('onOptionChanged: ', e.name);
    switch (e.name) {
      case 'currentDate':
        this.lastTimesheetRegistrySelected = null;
        if (this.currentView === 'day') {
          const diffDays = (e.value.getTime() - e.previousValue.getTime()) / (1000 * 3600 * 24);
          if (e.value > this.currentDate && DateUtil.isSameDay(e.previousValue, this.currentDate)) {
            this.setup(DateUtil.add(new Date(this.currentDate), 'day', diffDays), false);
          } else if (e.value < this.currentDate && DateUtil.isSameDay(e.previousValue, this.currentDate)) {
            this.setup(DateUtil.add(new Date(this.currentDate), 'day', diffDays), false);
          }
        } else {
          this.setup(e.value, false);
        }
        break;
      case 'currentView':
        this.lastTimesheetRegistrySelected = null;
        // Se realiza esta validación debido a que el valor del currentView del scheduler cambia por el idioma configurado
        if (this.getLang().startsWith('es')) {
          if (e.value === 'Semana' || '') {
            this.currentView = 'week';
          } else {
            this.currentView = 'day';
          }
        } else {
          this.currentView = e.value;
        }
        this.setup(this.currentDate, false);
        break;
    }
  }

  onListDragStart(e) {
    e.cancel = true;
  }

  onItemDragStart(e) {
    e.itemData = e.fromData;
  }

  openSettings() {
    this.visibleTooltipConfiguration = true;
  }

  hideSettingsDialog() {
    this.visibleTooltipConfiguration = false;
  }

  cellDurationChange(value) {
    this.cellDuration = value;
    LocalStorageSession.setValue(LocalStorageItem.TS_CELL_DURATION, value);
    const scheduler = this.scheduler();
    scheduler.cellDuration = this.cellDuration;
    scheduler.instance.repaint();
    if (this.lastTimesheetRegistrySelected !== null && typeof this.lastTimesheetRegistrySelected !== 'undefined') {
      this.scrollToDate(this.lastTimesheetRegistrySelected);
    }
    this.noticeService.notice(this.translate.instant('applied-changes'), false, 2000);
  }

  onItemDragEnd(e) {
    if (e.toData) {
      e.cancel = true;
    }
  }

  isTask(value: any[]): boolean {
    if (!value || value.length === 0) {
      return false;
    }
    const firstTask = value[0];
    if (firstTask.value === PlannedType.UNPLANNED) {
      return true;
    }
    if (firstTask.value === PlannedType.PLANNED) {
      return false;
    }
    return firstTask.pendingRecordId === null || typeof firstTask.pendingRecordId === 'undefined';
  }

  castTextPlannerValue(value: any): TextPlannerValue {
    return value;
  }

  verifyTextTaskArray(value: TextPlannerValue): TextPlannerValue[] {
    if (value.tasks && value.tasks.length > 0) {
      return value.tasks;
    }
    return null;
  }

  /**
   * Método para abrir un cuadro de diálogo de captura nuevo
   */
  private openNewRegistry(plannedType: PlannedType): void {
    // Validación cuando es semana se muestra la fecha actual en el dialogo
    let captureDate = new Date();
    if (this.currentView === 'day') {
      captureDate = this.rangeStart;
    }
    this.timesheetService.openNew(plannedType, 0, null, null, captureDate, null, true).then(
      (value: TimesheetDto) => {
        const registryTime = TimesheetWidgetUtils.calculateDurationTime(value.workedMinutes, value.start, value.end);
        this.timesheetRegistries.push({
          systemLinkValue: value.systemLinkValue || null,
          systemLinkId: value.systemLinkId || null,
          systemLinkLabel: value.systemLinkLabel || null,
          mainSystemId: value.systemLinkId || null,
          mainSystemLinkValue: value.systemLinkValue || null,
          mainSystemUrl: null,
          mainSystemRegExp: null,
          systemLinkAvailable: !!value.systemLinkId,
          plannerId: value.plannerId || null,
          clientId: value.clientId || null,
          timesheetId: value.timesheetId,
          fileIds: value.fileIds,
          uid: value.uid,
          text: value.registry,
          description: value.registry,
          startDate: new Date(value.start),
          endDate: new Date(value.end),
          visible: true,
          registryTime: registryTime,
          temporalId: value.uid,
          value: value.activityId || null,
          pendingRecordId: value.pendingRecordId || null,
          activityId: value.activityId || null,
          clientRecordLocked: value.clientRecordLocked,
          plannerRecordLocked: value.plannerRecordLocked,
          taskRecordLocked: value.plannerRecordLocked && value.taskRecordLocked,
          hasEnableStopwatch: value.hasEnableStopwatch,
          clientDescription: value.clientDescription || this.clients.find((t) => t.value === value.clientId)?.text?.toString() || '',
          plannerDescription: value.plannerDescription || this.planners.find((t) => t.value === value.plannerId)?.text?.toString() || '',
          activityDescription: value.activityDescription || this.tasks.find((t) => t.value === value.activityId)?.text?.toString() || '',
          registry: value.registry || '',
          activityPlannedDescription: value.activityPlannedDescription,
          activityPlannedId: value.activityPlannedId,
          date: new Date(),
          stopwatchLocalTimeStart: value.stopwatchLocalTimeStart,
          stopwatchType: value.stopwatchType,
          workedMinutes: value.workedMinutes ? value.workedMinutes : 0,
          showAppointmentPickerTime: value.workedMinutes ? value.workedMinutes >= 30 : false
        });
        this.calculateHoursWorkedDaily();
        this.cdr.detectChanges();
      },
      () => {
        this.cdr.detectChanges();
      }
    );
  }

  /**
   * Método para validar si las horas de inicio y fin de un registro que se está actualizando o agregando,
   * coinciden con uno existente.
   * @param t Registro de Timesheet a agregar o actualizar
   */
  private isSplicedRecord(t: TimesheetRegistry): boolean {
    let result = false;
    for (const record of this.timesheetRegistries) {
      if (record.timesheetId !== t.timesheetId && record.endDate > t.startDate && record.startDate < t.endDate) {
        result = true;
      }
    }
    return result;
  }

  /**
   * Método para ajustar las fechas para el Bug del componente Scheduler (En zoom a 67% 0 75%, le resta minutos)
   * @param datetoFix Fecha a ajustar
   */
  private fixDates(datetoFix: Date): Date {
    // Se aplica fix solo en zoom menor a 75%
    if (this.px_ratio <= 0.75) {
      let min = datetoFix.getMinutes();
      const mod = min % 5;
      if (mod > 0) {
        const div = Math.round(min / this.cellDuration);
        min = div * this.cellDuration;
        datetoFix.setMinutes(min);
      }
    }
    return DateUtil.safe(datetoFix);
  }

  // Customizamos el texto del navigator
  customizeDateNavigatorText = (e) => {
    const formatOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    };
    const formattedStartDate = e.startDate.toLocaleString(this.getLang(), { month: 'short', day: 'numeric' });
    let formattedEndDate = e.endDate.toLocaleString(this.getLang(), formatOptions);
    if (this.currentView === 'day') {
      const date = new Date(DateUtil.safe(e.startDate).getTime());
      const end = DateUtil.add(date, DateInterval.DAY, 2);
      formattedEndDate = end.toLocaleString(this.getLang(), formatOptions);
      return `${formattedStartDate} - ${formattedEndDate}`;
    }
    return `${formattedStartDate} - ${formattedEndDate}`;
  };

  /**
   * Método para calcular el total de horas por día de acuerdo al rango de fechas (Vista día o semana)
   */
  private calculateHoursWorkedDaily(): void {
    this.hoursWorked = [];
    let current = this.rangeStart;
    const end = this.rangeEnd;
    while (current <= end) {
      let sum = 0;
      let hours = 0;
      let min = 0;
      for (const item of this.timesheetRegistries.filter((ti: TimesheetRegistry) => DateUtil.isSameDay(current, new Date(ti.endDate)))) {
        const min = Math.abs(new Date(item.endDate).getTime() - new Date(item.startDate).getTime());
        sum += Math.floor(min / 1000 / 60);
      }
      hours = Math.floor(sum / 60);
      min = sum - hours * 60;
      this.hoursWorked.push(`${hours}h ${min}m`);
      current = DateUtil.add(current, 'day', 1);
    }
    this.cdr.detectChanges();
  }

  /**
   * Método para validar si la fecha fin (en particular la hora) es de un día diferente a la fecha de inicio y son las 00 hrs
   * @param start Fecha inicio
   * @param end Fecha fin
   */
  private isValidEndDate(start: Date, end: Date): boolean {
    if (!DateUtil.isSameDay(start, end) && end.getHours() === 0 && end.getMinutes() >= 0) {
      return false;
    }
    return true;
  }

  safeDate(date: string | Date): Date {
    // ToDo: No se está tulizando mientras la bandera `isTimesheetSchedulerPickerAvailable` esté en FALSE
    return DateUtil.safe(date);
  }

  startUpdated(evt: string | Date, model: AppointmentClickEvent, timePickerStart: IgxTimePickerComponent): void {
    // ToDo: No se está tulizando mientras la bandera `isTimesheetSchedulerPickerAvailable` esté en FALSE
    if (!model.appointmentData.hasEnableStopwatch) {
      const fixedStart = TimesheetWidgetUtils.fixDate(DateUtil.safe(evt));
      const originalTime = new Date(model.appointmentData.startDate);
      if (fixedStart.getTime() >= new Date(model.appointmentData.endDate).getTime()) {
        this.noticeService.notice(this.translate.instant('invalid-time-greater'), true);
        timePickerStart.value = originalTime;
      } else if (Math.abs(DateUtil.elapsedMinutes(fixedStart, originalTime)) >= 5) {
        model.appointmentData.startDate = fixedStart;
        this.syncRegistries(model.appointmentData as TimesheetRegistry);
        this.refreshPickers(model);
      }
    }
  }

  endUpdated(evt: string | Date, model: AppointmentClickEvent, timePickerEnd: IgxTimePickerComponent): void {
    // ToDo: No se está tulizando mientras la bandera `isTimesheetSchedulerPickerAvailable` esté en FALSE
    if (!model.appointmentData.hasEnableStopwatch) {
      const fixedEnd = TimesheetWidgetUtils.fixDate(DateUtil.safe(evt));
      const originalTime = new Date(model.appointmentData.endDate);
      if (fixedEnd.getTime() <= new Date(model.appointmentData.startDate).getTime()) {
        this.noticeService.notice(this.translate.instant('invalid-time-less'), true);
        timePickerEnd.value = originalTime;
      } else if (Math.abs(DateUtil.elapsedMinutes(fixedEnd, originalTime)) >= 5) {
        model.appointmentData.endDate = fixedEnd;
        this.syncRegistries(model.appointmentData as TimesheetRegistry);
        this.refreshPickers(model);
      }
    }
  }
  refreshPickers(model: AppointmentClickEvent) {
    // ToDo: No se está tulizando mientras la bandera `isTimesheetSchedulerPickerAvailable` esté en FALSE
    this.scheduler().instance.repaint();
    this.scrollToDate(model.appointmentData as TimesheetRegistry);
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 1000);
  }

  private scrollToDate(data: TimesheetRegistry) {
    const startDate = DateUtil.safe(data.startDate);
    const endDate = DateUtil.safe(data.endDate);
    const dateToScroll = new Date(startDate.getTime() + endDate.getTime());
    this.scheduler().instance.scrollTo(new Date(dateToScroll.getTime() / 2));
  }

  deleteAppointment(deleteItem) {
    this.isTooltipVisible = false;
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
    this.scheduler().instance.deleteAppointment(deleteItem);
  }

  getTitleAppointment(model): string {
    if (model) {
      return `${model.clientDescription}/${model.plannerDescription}/${model.activityDescription}: ${model.text}`;
    }
    return '';
  }

  onClickNewRecord(_event) {
    this.toggleSubfloattingButtons();
    this.isTooltipVisible = false;
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
    this.cdr.detectChanges();
  }

  onClickFloattingPlanned() {
    const plannedType = EnumUtil.getValue(PlannedType, PlannedType.PLANNED, 'string');
    this.openNewRegistry(plannedType);
    this.toggleSubfloattingButtons();
    this.isTooltipVisible = false;
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
  }

  onClickFloattingUnplanned() {
    const unplannedType = EnumUtil.getValue(PlannedType, PlannedType.UNPLANNED, 'string');
    this.openNewRegistry(unplannedType);
    this.toggleSubfloattingButtons();
    this.isTooltipVisible = false;
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
  }

  private toggleSubfloattingButtons() {
    this.showSubFloatting = !this.showSubFloatting;
  }

  drawText(event) {
    this.noticeService.notice(`${event} % `, false);
  }

  onAppointmentClick(_e) {
    //Para ocultar el popup al seleccionar un Appointment desde time picker
    _e.cancel = true;
    this.loadTimesheetFilesCount(_e.appointmentData).then(() => {
      this.timesheetRegistrySelected = _e;
      this.timesheetRegistrySelected.appointmentData.date = DateUtil.trunc(new Date(_e.appointmentData.startDate));
      this.lastTimesheetRegistrySelected = this.timesheetRegistrySelected.appointmentData as TimesheetRegistry;
      if (!this.timepickerClicked && !this.isMobilDevice()) {
        this.isTooltipVisible = true;
      } else if (!this.timepickerClicked && this.isMobilDevice()) {
        this.cdr.detectChanges();
        const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
        if (dialogInfoMobilDevice) {
          dialogInfoMobilDevice.open();
        }
      }
      this.timepickerClicked = false;
      this.cdr.detectChanges();
    });
  }

  hidePopupDatePicker(_e) {
    //Para ocultar el popup al seleccionar un Appointment desde time picker
    this.timepickerClicked = true;
    this.isTooltipVisible = false;
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
    this.cdr.detectChanges();
    this.scheduler().onAppointmentClick.subscribe((r) => {
      r.cancel = true;
    });
  }

  redirectToReq(data: TimesheetRegistry) {
    return data.systemLinkUrl + data.systemLinkValue;
  }

  onFileSelected(fileList: File[] | FileList, attacher = null): void {
    this.files = [];
    if (this.busy) {
      return;
    }
    const options: FileSelectOptions = {
      holder: this,
      successMessage: this.tag('success-file'),
      unsupportedFormat: this.tag('unsupported-format')
    };
    this.dropFile().onFileSelected(fileList, options, attacher);
  }

  onLinked(event: LinkedFileEvt): void {
    this.service
      .get({
        cancelableReq: this.$destroy,
        url: `timesheet/addFileFromAppointment/${event.file.id}/${this.timesheetRegistrySelected.appointmentData.timesheetId}`
      })
      .subscribe((response) => {
        if (response) {
          if (typeof this.timesheetRegistrySelected.appointmentData.fileData === 'undefined' || this.timesheetRegistrySelected.appointmentData.fileData === null) {
            this.timesheetRegistrySelected.appointmentData.fileData = [];
          }
          this.timesheetRegistrySelected.appointmentData.fileData.push(event.file);
          this.noticeService.notice(this.translate.instant('file-upload-ok'), false);
          this.cdr.detectChanges();
        }
      });
  }

  openDialogModeUpdate(registry: Appointment): void {
    if (registry.hasEnableStopwatch) {
      this.noticeService.notice(this.translate.instant('timesheet-add-edit-validation'), false);
      return;
    }
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
    this.isTooltipVisible = false;
    const timesheet: TimesheetDto = this.addTimesheetRegistryToTimesheetDto(registry);
    this.timesheetService.openEdit(timesheet, true).then((response: TimesheetDto) => {
      if (response) {
        registry.stopwatchLocalTimeStart = response.stopwatchLocalTimeStart;
        console.warn('Opened dialog...');
      }
    });
  }

  public getIdAppointment(data: Appointment): string {
    return this.idAppointment(data);
  }

  public getIdAppointmentTarget(data: Appointment): string {
    return `#${this.idAppointment(data)}`;
  }

  private idAppointment(data: Appointment): string {
    return `appointment-container${data.timesheetId || data.temporalId}`;
  }

  public addTimesheetRegistryToTimesheetDto(registry: Appointment): TimesheetDto {
    const timeshetDto: TimesheetDto = {
      date: new Date(registry.date),
      pendingRecordId: registry.pendingRecordId,
      uid: registry.uid,
      tags: registry.tags,
      registry: registry.registry,
      fileData: registry.fileData,
      start: new Date(registry.startDate),
      end: new Date(registry.endDate),
      clientId: registry.clientId,
      clientDescription: registry.clientDescription,
      plannerId: registry.plannerId,
      plannerDescription: registry.plannerDescription,
      activityId: registry.activityId,
      activityDescription: registry.activityDescription,
      systemLinkValue: registry.systemLinkValue,
      systemLinkLabel: registry.systemLinkLabel,
      systemLinkId: registry.systemLinkId,
      activityPlannedDescription: registry.activityPlannedDescription,
      activityPlannedId: registry.activityPlannedId,
      fileIds: registry.fileIds,
      clientRecordLocked: registry.clientRecordLocked,
      plannerRecordLocked: registry.plannerRecordLocked,
      taskRecordLocked: registry.plannerRecordLocked && registry.taskRecordLocked,
      activityPlannedCode: registry.activityPlannedCode,
      activityPlannedModule: registry.activityPlannedModule,
      hasEnableStopwatch: registry.hasEnableStopwatch,
      timesheetId: registry.timesheetId,
      businessUnitDescription: registry.businessUnitDescription,
      stopwatchLocalTimeStart: registry.stopwatchLocalTimeStart,
      stopwatchType: registry.stopwatchType,
      plannerStatus: registry.plannerStatus || null
    };
    return timeshetDto;
  }

  focusDuration(value: number): boolean {
    if (LocalStorageSession.getValue(LocalStorageItem.TS_CELL_DURATION) !== null) {
      const result = value === +LocalStorageSession.getValue(LocalStorageItem.TS_CELL_DURATION);
      return result;
    }
    return false;
  }

  public repaintScheduler(_event: Appointment): void {
    if (_event) {
      this.isTooltipVisible = false;
      const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
      if (dialogInfoMobilDevice) {
        dialogInfoMobilDevice.close();
      }
      //Se establece bandera para que valida que no realize petición al backend, solo actualize appointment de front
      // ya que el registro ya fue actualizado en el backend desde el dialogo
      this.onlyUpdateLocalAppointment = true;
      //Se actualiza appointment con los nuevos datos
      _event.registryTime = TimesheetWidgetUtils.calculateDurationTime(null, DateUtil.safe(_event.startDate), DateUtil.safe(_event.endDate));
      _event.systemLinkAvailable = !!_event.systemLinkId;
      this.setup();
      this.scheduler().instance.updateAppointment(this.lastTimesheetRegistrySelected, _event);
      this.cdr.detectChanges();
    }
  }

  public updateAppointmentData(event: StopwatchEndTimeData, registry: Appointment) {
    if (event.timesheetId !== null) {
      this.service.post({ url: `timesheet/updateStopwatch/${event.timesheetId}`, cancelableReq: this.$destroy, postBody: event }).subscribe(
        (response) => {
          if (response) {
            registry.endDate = event.end;
            registry.end = registry.endDate;
            registry.hasEnableStopwatch = event.hasEnableStopwatch;
            this.existRegisterWithStopwtach = event.hasEnableStopwatch;
            this.repaintScheduler(registry);
            this.timesheetService.updateUIStopwatchService(event.hasEnableStopwatch, this.addTimesheetRegistryToTimesheetDto(registry));
          }
        },
        (error) => {
          console.error(error);
        }
      );
    }
  }

  public stopStopwatch(registry: TimesheetRegistry) {
    const cloneRegistry = cloneObject(registry);
    cloneRegistry.end = TimesheetWidgetUtils.fixDate(new Date());
    cloneRegistry.endDate = TimesheetWidgetUtils.fixDate(new Date());
    this.timesheetService.updateStopwatchRegistryEmptyService(this.addTimesheetRegistryToTimesheetDto(cloneRegistry)).then((dto: TimesheetDto) => {
      registry.registry = dto.registry;
      registry.endDate = DateUtil.safe(dto.end);
      registry.hasEnableStopwatch = false;
      this.repaintScheduler(registry);
    });
  }

  public closeTooltips() {
    this.isTooltipVisible = false;
    const dialogInfoMobilDevice = this.dialogInfoMobilDevice();
    if (dialogInfoMobilDevice) {
      dialogInfoMobilDevice.close();
    }
  }

  public showTotalHours() {
    return !this.isMobilDevice();
  }

  public appointmentRendered(event) {
    const instance = Resizable.getInstance(event.appointmentElement) as Resizable;
    if (instance) {
      instance.option('onResize', this.onResize.bind(this));
    }
  }

  onResize() {
    this.closeTooltips();
  }

  private timesheetDtoToTimesheetRegistry(timesheetDto: TimesheetDto): TimesheetRegistry {
    const activityPlannedInfo = this.tasksActivities.find((t) => t.activityPlannedId === timesheetDto.activityPlannedId);
    return {
      activityPlannedCode: timesheetDto?.activityPlannedCode || activityPlannedInfo?.activityPlannedCode || null,
      activityPlannedDescription: timesheetDto?.activityPlannedDescription || activityPlannedInfo?.activityPlannedDescription || null,
      activityPlannedId: timesheetDto?.activityPlannedId || null,
      activityPlannedModule: timesheetDto.activityPlannedModule,
      clientId: timesheetDto.clientId,
      clientDescription: timesheetDto.clientDescription || this.clients.find((c) => c.value === timesheetDto.clientId).text,
      pendingRecordId: timesheetDto.pendingRecordId,
      text: timesheetDto.registry,
      registry: timesheetDto.registry,
      fileIds: timesheetDto.fileIds,
      hasEnableStopwatch: timesheetDto.hasEnableStopwatch,
      stopwatchLocalTimeStart: timesheetDto.stopwatchLocalTimeStart,
      stopwatchReStarted: timesheetDto.stopwatchReStarted,
      plannerId: timesheetDto.plannerId,
      plannerDescription: timesheetDto.plannerDescription || this.planners.find((p) => p.value === timesheetDto.plannerId).text,
      activityId: timesheetDto.activityId,
      activityDescription: timesheetDto.activityDescription || this.tasks.find((a) => a.value === timesheetDto.activityId).text,
      clientRecordLocked: timesheetDto.clientRecordLocked,
      plannerRecordLocked: timesheetDto.plannerRecordLocked,
      taskRecordLocked: timesheetDto.taskRecordLocked,
      systemLinkId: timesheetDto.systemLinkId,
      systemLinkValue: timesheetDto.systemLinkValue,
      temporalId: null,
      mainSystemLinkValue: timesheetDto.systemLinkValue,
      mainSystemId: timesheetDto.systemLinkId,
      mainSystemRegExp: null,
      mainSystemUrl: null,
      systemLinkAvailable: null,
      value: timesheetDto.activityPlannedId || timesheetDto.activityId,
      startDate: timesheetDto.start as Date,
      endDate: timesheetDto.end as Date,
      tags: timesheetDto.tags ? (timesheetDto.tags as string[]) : [''],
      timesheetId: timesheetDto.timesheetId,
      workedMinutes: timesheetDto.workedMinutes || 0
    };
  }

  private openTimesheetToFillPartialPlannerActivities(event: TimesheedAddedEvent): void {
    const fillType = event.fromData.activityPlannedId !== null && typeof event.fromData.activityPlannedId !== 'undefined' ? PlannedType.PLANNED : PlannedType.UNPLANNED;
    this.timesheetService
      .openNew(
        fillType,
        event.fromData.pendingRecordId,
        '',
        event.fromData.tags,
        new Date(),
        { activityPlannedId: event.fromData.activityPlannedId, pendingRecordId: event.fromData.pendingRecordId, hasEnableStopwatch: event.fromData.hasEnableStopwatch },
        false,
        true
      )
      .then((value: TimesheetDto) => {
        const timesheetRegistry = this.timesheetDtoToTimesheetRegistry(value);
        this.onAddAppointment({ itemData: timesheetRegistry, fromData: timesheetRegistry }, false);
        this.calculateHoursWorkedDaily();
        this.cdr.detectChanges();
      });
  }
}
