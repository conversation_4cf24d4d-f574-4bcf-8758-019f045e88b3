import { ChangeDetectorRef, Component, HostBinding, Input, type OnChanges, type On<PERSON><PERSON>roy, type OnInit, type SimpleChang<PERSON>, inject, output } from '@angular/core';
import { GlobalPositionStrategy, HorizontalAlignment, IgxOverlayService, VerticalAlignment } from '@infragistics/igniteui-angular';
import { Subject, type Subscription } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { LoaderComponent } from 'src/app/core/loader/loader.component';
import type { ILoaderElement } from '../utils/bnext-config.interfaces';

let NEXT_NAME = 0;
let LOADER_OVERLAY_ID: string = null;

@Component({
  selector: 'app-loader-element',
  styleUrls: ['./bnext-loader-element.component.scss'],
  templateUrl: './bnext-loader-element.component.html'
})
export class BnextLoaderElementComponent implements OnDestroy, OnChanges, ILoaderElement, OnInit {
  private cdr = inject(ChangeDetectorRef);
  private overlayService = inject<IgxOverlayService>(IgxOverlayService);
  private destroy$ = new Subject<boolean>();
  readonly shown = output<{ isShown: boolean }>();
  readonly hidden = output<{ isShown: boolean }>();

  loaderSubscription: Subscription;

  @HostBinding('attr.id')
  @Input()
  public id = 'bnextLoader';

  @HostBinding('attr.name')
  @Input()
  public name = `bnextLoader-${NEXT_NAME++}`;

  @Input()
  public isShown = false;

  constructor() {
    this.overlayService.closed
      .pipe(
        filter((x) => x.id === LOADER_OVERLAY_ID),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        if (LOADER_OVERLAY_ID !== null && typeof LOADER_OVERLAY_ID !== 'undefined') {
          this.overlayService.detach(LOADER_OVERLAY_ID);
        }
        LOADER_OVERLAY_ID = null;
      });
  }

  public ngOnDestroy(): void {
    if (this.loaderSubscription) {
      this.loaderSubscription.unsubscribe();
    }
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes.isShown) {
      this.applyIsShownChanges(changes.isShown.currentValue, changes.isShown.previousValue);
    }
  }

  public ngOnInit(): void {
    this.applyIsShownChanges(this.isShown, !this.isShown);
  }

  private applyIsShownChanges(currentValue: boolean, previousValue: boolean): void {
    if ((currentValue === true && previousValue === true) || (currentValue === false && previousValue === false)) {
      return;
    }
    if (currentValue) {
      if (LOADER_OVERLAY_ID === null || typeof LOADER_OVERLAY_ID === 'undefined') {
        LOADER_OVERLAY_ID = this.overlayService.attach(LoaderComponent, {
          positionStrategy: new GlobalPositionStrategy({
            openAnimation: null,
            closeAnimation: null,
            horizontalStartPoint: HorizontalAlignment.Left,
            verticalStartPoint: VerticalAlignment.Top
          }),
          modal: true,
          closeOnOutsideClick: false
        });
      }
      this.overlayService.show(LOADER_OVERLAY_ID);
      const overlayInstance = this.overlayService.getOverlayById(LOADER_OVERLAY_ID);
      if (overlayInstance?.wrapperElement) {
        overlayInstance.wrapperElement.style.setProperty('z-index', '10010');
        overlayInstance.wrapperElement.style.display = '';
      }
      this.shown.emit({
        isShown: true
      });
    } else {
      if (LOADER_OVERLAY_ID === null || typeof LOADER_OVERLAY_ID === 'undefined') {
        this.hidden.emit({
          isShown: false
        });
      } else {
        const overlayInstance = this.overlayService.getOverlayById(LOADER_OVERLAY_ID);
        this.overlayService.hide(LOADER_OVERLAY_ID);
        if (overlayInstance?.wrapperElement) {
          overlayInstance.wrapperElement.style.display = 'none';
        }
        this.hidden.emit({
          isShown: false
        });
      }
    }
    this.cdr.detectChanges();
  }
}
