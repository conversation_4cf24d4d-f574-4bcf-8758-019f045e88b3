<div class="bnext-login-container">
  <igx-dialog
    #dialog
    [closeOnOutsideSelect]="false"
    [closeOnEscape]="false"
    [title]="oidcEnabled || enabledLandingPage || !enableLoginForm ? null : loginTitle + ' ' + getUserAccount()"
    (opening)="onOpen()"
    (opened)="onOpened()"
    (closed)="onClosed()"
  >
    @defer (on immediate) {
      @if (oidcEnabled) {
        <div class="external-auth">
          <a [href]="oidcAuthHref" class="igx-button" [igxButton]="'flat'" igxRipple (click)="openOidcAuth($event)">
            @switch (oidcProvider) {
              @case (EnumOidcProvider.OKTA) {
                <img alt="OpenID Connect (OIDC) provider logo" width="24px" height="24px" src="assets/images/okta-24.png" />
                <span>{{ externalAuthLabel[EnumOidcProvider.OKTA] }}</span>
              }
              @case (EnumOidcProvider.MICROSOFT) {
                <img alt="OpenID Connect (OIDC) provider logo" width="24px" height="24px" src="assets/images/microsoft-21.png" />
                <span>{{ externalAuthLabel[EnumOidcProvider.MICROSOFT] }}</span>
              }
            }
          </a>
        </div>
      }
      @if (enabledLandingPage) {
        <div class="external-auth">
          <a [href]="landingPageHref" class="igx-button" [igxButton]="'flat'" igxRipple (click)="openLandingPage($event)">
            <img alt="LDAP provider logo" width="24px" height="24px" src="assets/images/microsoft-21.png" />
            <span>{{ externalAuthLabel.landingPage }}</span>
          </a>
        </div>
      }
      @if (oidcEnabled || enabledLandingPage) {
        <div class="external-auth-divider">
          <igx-divider type="dashed"></igx-divider>
        </div>
        <div class="title-container">
          <div class="dialog-title igx-dialog__window-title">{{ loginTitle + ' ' + getUserAccount() }}</div>
        </div>
      }
      @if (enableLoginForm) {
        <form class="signInForm" [formGroup]="userForm">
          @if (!hasUserAccount) {
            <igx-input-group theme="material">
              <igx-prefix>
                <igx-icon>person</igx-icon>
              </igx-prefix>
              <input
                #userInput
                igxInput
                [disabled]="busy"
                name="username"
                autocapitalize="off"
                type="text"
                autocorrect="off"
                autocomplete="username"
                formControlName="username"
                tabindex="1"
              />
              <label igxLabel for="username">{{ usernameLabel }}</label>
            </igx-input-group>
          }
          <igx-input-group theme="material">
            <igx-prefix>
              <igx-icon>lock</igx-icon>
            </igx-prefix>
            <input
              #passwordInput
              igxInput
              [disabled]="busy"
              formControlName="password"
              autocomplete="current-password"
              tabindex="2"
              name="password"
              type="password"
              [required]="true"
              (keyup.enter)="onSignIn()"
            />
            <label igxLabel for="password">{{ passwordLabel }}</label>
          </igx-input-group>
          <div class="linearBarHolder">
            @if (busy) {
              <igx-linear-bar type="success" [striped]="false" [indeterminate]="true"> </igx-linear-bar>
            }
          </div>
        </form>
      }
    }
    <div igxDialogActions>
      <button igxButton (click)="signOut()">{{ leftButtonTitle }}</button>
      <button id="signInButton" [igxButton]="'contained'" (click)="onSignIn()">{{ righButtonTitle }}</button>
    </div>
  </igx-dialog>
</div>
