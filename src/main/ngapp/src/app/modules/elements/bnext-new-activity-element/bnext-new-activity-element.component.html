<igx-dialog #dialog>
  <div #elementContent class="activity-new-dialog-container" (dragenter)="dragEnter($event)">
    @defer (on immediate) {
      <app-activities-add-to-module
        class="activity-form"
        [linkedSource]="linkedSource"
        [linkedDataSource]="linkedDataSource"
        [showDisabledAsReadonly]="showDisabledAsReadonly"
        [isPlannedSwitchAvailable]="false"
        [isPlannedHoursAvailable]="false"
        [initializeData]="initializeData"
        [resetData]="resetData"
        (typesChanged)="checkIfAvailable($event)"
        (canceled)="onCanceledActivity($event)"
        (saved)="onSavedActivity($event)"
      ></app-activities-add-to-module>
    }
  </div>
</igx-dialog>
