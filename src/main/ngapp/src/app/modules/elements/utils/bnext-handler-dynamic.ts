import type { Injector, Ng<PERSON><PERSON> } from '@angular/core';
import { createCustomElement } from '@angular/elements';

export async function initDialog(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextDialogElementComponent } = await import('../bnext-dialog-element/bnext-dialog-element.component');
    const custom = createCustomElement(BnextDialogElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initLoader(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextLoaderElementComponent } = await import('../bnext-loader-element/bnext-loader-element.component');
    const custom = createCustomElement(BnextLoaderElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initLogin(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextLoginElementComponent } = await import('../bnext-login-element/bnext-login-element.component');
    const custom = createCustomElement(BnextLoginElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initNavigator(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextNavigatorElementComponent } = await import('../bnext-navigator-element/bnext-navigator-element.component');
    const custom = createCustomElement(BnextNavigatorElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initNotice(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextNoticeElementComponent } = await import('../bnext-notice-element/bnext-notice-element.component');
    const custom = createCustomElement(BnextNoticeElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initPendingCount(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextPendingCountComponent } = await import('../bnext-pending-count-element/bnext-pending-count-element.component');
    const custom = createCustomElement(BnextPendingCountComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initReport(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextReportElementComponent } = await import('../bnext-report-element/bnext-report-element.component');
    const custom = createCustomElement(BnextReportElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initFabMenu(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextFabButtonElementComponent } = await import('../bnext-fab-menu-element/bnext-fab-menu-element.component');
    const custom = createCustomElement(BnextFabButtonElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initTimesheetDialog(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextTimesheetDialogElementComponent } = await import('../bnext-timesheet-dialog-element/bnext-timesheet-dialog-element.component');
    const custom = createCustomElement(BnextTimesheetDialogElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initFolders(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextFoldersElementComponent } = await import('../bnext-folders-element/bnext-folders-element.component');
    const custom = createCustomElement(BnextFoldersElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initToast(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextToastElementComponent } = await import('../bnext-toast-element/bnext-toast-element.component');
    const custom = createCustomElement(BnextToastElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initSnackbar(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextSnackbarElementComponent } = await import('../bnext-snackbar-element/bnext-snackbar-element.component');
    const custom = createCustomElement(BnextSnackbarElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initDocumentOptions(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextDocumentOptionsElementComponent } = await import('../bnext-document-options-element/bnext-document-options-element.component');
    const custom = createCustomElement(BnextDocumentOptionsElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initChangeUserDefinedForm(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextChangeUserDefinedFormElementComponent } = await import('../bnext-change-user-defined-form-element/bnext-change-user-defined-form-element.component');
    const custom = createCustomElement(BnextChangeUserDefinedFormElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initChangeUserDefinedHistory(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextChangeUserDefinedHistoryElementComponent } = await import(
      '../bnext-change-user-defined-history-element/bnext-change-user-defined-history-element.component'
    );
    const custom = createCustomElement(BnextChangeUserDefinedHistoryElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initNewActivity(selector: string, _zone: NgZone, injector: Injector): Promise<void> {
  // TODO: Se quita runOutsideAngular debido a que existe un bug con el componente
  // de fecha en el componente legacy de Actividades.
  // Volver a revisar el escenario cuando se quiera volver a activar.
  if (customElements.get(selector)) {
    return;
  }
  const { BnextNewActivityElementComponent } = await import('../bnext-new-activity-element/bnext-new-activity-element.component');
  const custom = createCustomElement(BnextNewActivityElementComponent, {
    injector: injector
  });
  customElements.define(selector, custom);
}
export async function initDetailActivity(selector: string, _zone: NgZone, injector: Injector): Promise<void> {
  if (customElements.get(selector)) {
    return;
  }
  const { BnextDetailActivityElementComponent } = await import('../bnext-detail-activity-element/bnext-detail-activity-element.component');
  const custom = createCustomElement(BnextDetailActivityElementComponent, {
    injector: injector
  });
  customElements.define(selector, custom);
}

export async function initActivityAttender(selector: string, _zone: NgZone, injector: Injector): Promise<void> {
  if (customElements.get(selector)) {
    return;
  }
  const { BnextActivityAttenderElementComponent } = await import('../bnext-activity-attender/bnext-activity-attender-element.component');
  const custom = createCustomElement(BnextActivityAttenderElementComponent, {
    injector: injector
  });
  customElements.define(selector, custom);
}

export async function initCalendar(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextCalendarElementComponent } = await import('../bnext-calendar-element/bnext-calendar-element.component');
    const custom = createCustomElement(BnextCalendarElementComponent, {
      injector: injector
    });

    customElements.define(selector, custom);
  });
}
export async function initTimePicker(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextTimePickerElementComponent } = await import('../bnext-time-picker-element/bnext-time-picker-element.component');
    const custom = createCustomElement(BnextTimePickerElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}
export async function initOverlay(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextOverlayElementComponent } = await import('../bnext-overlay-element/bnext-overlay-element.component');
    const custom = createCustomElement(BnextOverlayElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initDefinitiveCancelationWithAuthorization(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextDefinitiveCancelationWithAuthorizationElementComponent } = await import(
      '../bnext-definitive-cancelation-with-authorization-element/bnext-definitive-cancelation-with-authorization-element.component'
    );
    const custom = createCustomElement(BnextDefinitiveCancelationWithAuthorizationElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initImageVisualizer(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextImageVisualizerComponent } = await import('../bnext-image-visualizer/bnext-image-visualizer.component');
    const custom = createCustomElement(BnextImageVisualizerComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initDuplicatePendingAttendant(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextDuplicatePendingAttendantElementComponent } = await import(
      '../bnext-duplicate-pending-attendant-element/bnext-duplicate-pending-attendant-element.component'
    );
    const custom = createCustomElement(BnextDuplicatePendingAttendantElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initGrid(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextGridElementComponent } = await import('../bnext-grid-element/bnext-grid-element.component');
    const custom = createCustomElement(BnextGridElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}

export async function initTimework(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextTimeworkElementComponent } = await import('../bnext-timework-element/bnext-timework-element.component');
    const custom = createCustomElement(BnextTimeworkElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}
export async function initGeolocation(selector: string, zone: NgZone, injector: Injector): Promise<void> {
  return zone.runOutsideAngular(async () => {
    if (customElements.get(selector)) {
      return;
    }
    const { BnextGeolocationElementComponent } = await import('../bnext-geolocation-element/bnext-geolocation-element.component');
    const custom = createCustomElement(BnextGeolocationElementComponent, {
      injector: injector
    });
    customElements.define(selector, custom);
  });
}
