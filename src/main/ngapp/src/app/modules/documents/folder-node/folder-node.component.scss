@use 'src/styles/immutable-colors' as *;

:host {
  display: block;
}

.rootNode > .folder-node-list,
.rootNode > .folder-node-list > div > app-folder-node > div > .folder-node-list {
  padding-left: 0px;
}

.no_children {
  visibility: hidden;
}

.item-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.folder {
  display: flex;
  flex: 1 0 15rem;
  align-items: center;
  position: relative;
  &__info {
    display: flex;
    flex-flow: column nowrap;
    margin-left: 1.5rem;
  }
}
igx-action-strip {
  padding: 0.5rem;
  justify-content: flex-start;
  margin-left: 35%;
  width: auto;
}
.folder-name {
  line-height: normal;
  margin-top: 0.25rem;
  cursor: pointer;
}
.description {
  font-weight: 600;
  margin-left: 0.5rem;
  margin-top: 0.25rem;
  position: absolute;
}

.folder-node-list {
  display: block;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  padding-left: 1rem;

  .igx-icon-button .material-icons,
  .igx-button .material-icons {
    margin-left: 0px;
  }

  .item-container:hover {
    color: rgba($fg-color, 0.74);
    background-color: rgba($fg-color, 0.04);
  }
}

:host ::ng-deep .highlight-search {
  background-color: yellow;
  outline: 1px solid #ffbf00;
}
