import { type FolderConfigDTO, MyRepositoryLocalConfig } from '@/core/indexed-db/my-repository-local-config';

import { BnextCoreComponent } from '@/core/bnext-core.component';
import { Location, NgClass } from '@angular/common';
import { type AfterViewInit, Component, type OnDestroy, type OnInit, inject, viewChild } from '@angular/core';
import { BehaviorSubject, Subject, type Subscription } from 'rxjs';
import { throttleTime } from 'rxjs/operators';

import { FolderNodeComponent } from '../folder-node/folder-node.component';
import type { FolderTreeItem } from '../utils/folder-tree-item';

import { AppService } from '@/core/services/app.service';
import type { DataMap } from 'src/app/core/utils/data-map';

import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import { equalsObject } from '@/core/utils/object';
import { FormsModule } from '@angular/forms';
import {
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxPrefixDirective,
  IgxRippleDirective,
  IgxSuffixDirective
} from '@infragistics/igniteui-angular';

@Component({
  selector: 'app-my-repository',
  templateUrl: './my-repository.component.html',
  styleUrls: ['./my-repository.component.scss'],
  imports: [
    IgxIconButtonDirective,
    IgxRippleDirective,
    IgxIconComponent,
    IgxInputGroupComponent,
    IgxPrefixDirective,
    FormsModule,
    NgClass,
    IgxInputDirective,
    IgxSuffixDirective,
    FolderNodeComponent,
    BnextTranslatePipe
  ]
})
export class MyRepositoryComponent extends BnextCoreComponent implements OnInit, OnDestroy, AfterViewInit {
  location = inject(Location);
  private service = inject(AppService);

  searchDebounceSubscription: Subscription;
  dirtySearch = false;

  dataSubscription: Subscription;

  override get componentPath(): string {
    return 'modules.documents';
  }

  readonly folderNode = viewChild<FolderNodeComponent>('folderNode');
  public nodeData: FolderTreeItem;
  public nodes: FolderTreeItem[];
  public tempFolders: FolderConfigDTO[] = null;

  private _changedFolder = new Subject<boolean>();
  private _searchValue = new BehaviorSubject<string>('');
  private _lastSearch = '';
  private _busySearch = false;
  private _searchValueDebunce = this._searchValue.asObservable().pipe(throttleTime(300, undefined, { leading: true, trailing: true }));

  get searchValue(): string {
    return this._searchValue.value;
  }

  set searchValue(value: string) {
    if (value?.length === 1) {
      return;
    }
    this._searchValue.next(value);
  }

  get searchActive(): boolean {
    const value = this._searchValue.value;
    return value !== null && value !== '' && typeof value !== 'undefined';
  }

  public expandAll = false;
  public notRepositoryFound = false;

  constructor() {
    super();

    this._searchValueDebunce.subscribe((value) => this.executeSearchValueChange(value));
    const folderSub = this._changedFolder.pipe(throttleTime(500, undefined, { leading: false, trailing: true })).subscribe(() => this.executeChangedFolder());
    this.subs.push(folderSub);
  }

  override ngOnInit(): void {
    super.ngOnInit();
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.searchDebounceSubscription = this._searchValueDebunce.subscribe((value) => {
      if (equalsObject(value, this._lastSearch)) {
        return;
      }
      if (this._busySearch) {
        this._searchValue.next(value);
        return;
      }
      this._busySearch = true;
      this._lastSearch = value;
      if (this.debug()) {
        console.warn(`search executed ${value}`);
      }
      this.filterNodeData(value);
      this._busySearch = false;
    });
    const data = this.service.get({ cancelableReq: this.$destroy, url: 'document/init-data/nodes' });
    this.dataSubscription = data.subscribe((nodes: FolderTreeItem[]) => {
      this.nodes = nodes;
      MyRepositoryLocalConfig.getValue().then(
        (value) => {
          if (value.searchValue !== null && value.searchValue !== '' && typeof value.searchValue !== 'undefined') {
            this.searchValue = value.searchValue;
          } else {
            const nodesMap: DataMap<FolderTreeItem> = {};
            for (const node of this.nodes) {
              nodesMap[node.path] = node;
            }
            for (const folder of value.folders) {
              const node = nodesMap[folder.path];
              if (node) {
                node.shown = folder.shown;
                node.expanded = folder.expanded;
                if (folder.expanded && node.children) {
                  for (const child of node.children) {
                    child.shown = true;
                  }
                }
              }
            }
            this.cdr.detectChanges();
          }
        },
        (error) => console.error('Could not load my repository local config.', error)
      );
      // Creamos una copia temporal de los nodos
      const nodesTemp: FolderTreeItem[] = [...this.nodes];
      // Agregamos nodo padre 0
      nodesTemp.push({
        id: 0,
        parent: null,
        description: '',
        title: '',
        path: '',
        rootNode: true,
        children: null,
        shown: false,
        expanded: false,
        readonly: false
      });
      // Creamos los nodos temporales que faltan (nodos a los que el usuario no tiene acceso)
      for (const node of nodesTemp) {
        this.fixMissingNodeTree(this.nodes, node);
      }
      // Construimos el álbol de carpetas
      const treeData = this.buildTreeData(this.nodes, undefined, undefined);
      for (const element of treeData) {
        element.shown = true;
      }
      this.nodeData = {
        id: null,
        description: null,
        title: null,
        parent: null,
        path: null,
        rootNode: true,
        children: treeData,
        shown: true,
        expanded: true,
        readonly: false
      };
      this.cdr.detectChanges();
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.searchDebounceSubscription) {
      this.searchDebounceSubscription.unsubscribe();
    }
    if (this.dataSubscription) {
      this.dataSubscription.unsubscribe();
    }
  }

  private executeSearchValueChange(searchValue: string): void {
    if (this.searchActive && this.tempFolders === null) {
      MyRepositoryLocalConfig.getValue().then((value) => {
        this.tempFolders = value.folders.length > 0 ? value.folders : null;
        MyRepositoryLocalConfig.setSearchValue(searchValue);
      });
    } else {
      MyRepositoryLocalConfig.setSearchValue(searchValue);
    }
  }

  private fixMissingNodeTree(data: FolderTreeItem[], node: FolderTreeItem): void {
    if (node.id === 0 || node.id === -1) {
      return;
    }
    if (node.parent !== null && typeof node.parent !== 'undefined') {
      const parentNodes: FolderTreeItem[] = data.filter((nodo) => nodo.id === node.parent);
      if (parentNodes.length > 0) {
        return;
      }
    }
    const safeNodePath = node.path.replace('\\\\', '\\');
    const pathParts = this.toPathArray(safeNodePath);
    if (pathParts.length <= 1) {
      if (node.parent === null) {
        node.parent = 0;
      }
      return;
    }
    const parentPathParts = pathParts.slice(0, pathParts.length - 1);
    const parentPath = `${parentPathParts.join('\\')}\\`;
    const parentNodes = data.filter((element) => element.path === parentPath);
    if (parentNodes.length > 0) {
      node.parent = parentNodes[0].id;
      return;
    }
    const parentDescription = parentPathParts[parentPathParts.length - 1];
    const tempParentNode: FolderTreeItem = {
      id: `NA-${node.id}`,
      description: parentDescription,
      title: parentDescription,
      children: null,
      shown: null,
      rootNode: null,
      expanded: null,
      parent: null,
      path: parentPath,
      readonly: true
    };

    node.parent = tempParentNode.id;
    data.push(tempParentNode);

    this.fixMissingNodeTree(data, tempParentNode);
  }

  private toPathArray(path: string): string[] {
    if (path[path.length - 1] === '\\') {
      path = path.substring(0, path.length - 1);
    }
    const pathParts = path.split('\\');
    if (pathParts.length <= 1) {
      return [];
    }
    return path.split('\\');
  }

  private executeChangedFolder(): void {
    let values: FolderConfigDTO[];
    if (this.searchActive) {
      values = [];
    } else {
      values = this.nodes
        .filter((node) => node.shown && node.expanded)
        .map((node) => {
          return {
            path: node.path,
            shown: node.shown,
            expanded: node.expanded
          };
        });
    }
    MyRepositoryLocalConfig.setFolders(values);
  }

  private toggleTree(value: boolean): void {
    if (!this.nodeData || !this.nodeData.children) {
      return;
    }
    if (value) {
      for (const child of this.nodeData.children) {
        this.expandTree(child);
      }
    } else {
      for (const child of this.nodeData.children) {
        this.collapseTree(child);
      }
    }
  }

  private expandTree(node: FolderTreeItem): void {
    if (!this.nodeData) {
      return;
    }
    if (!node.expanded) {
      node.expanded = true;
    }
    if (!node.children) {
      return;
    }
    for (const child of node.children) {
      if (!child.shown) {
        child.shown = true;
      }
      this.expandTree(child);
    }
    this.onChangedFolder();
  }

  private collapseTree(node: FolderTreeItem): void {
    if (!this.nodeData) {
      return;
    }
    if (node.expanded) {
      node.expanded = false;
    }
    if (!node.children) {
      return;
    }
    for (const child of node.children) {
      if (child.shown) {
        child.shown = false;
      }
      this.collapseTree(child);
    }
    this.onChangedFolder();
  }

  private buildTreeData(data: FolderTreeItem[], parent: FolderTreeItem, tree: FolderTreeItem[]): FolderTreeItem[] {
    if (tree === null || typeof tree === 'undefined') {
      tree = [];
    }
    if (parent === null || typeof parent === 'undefined') {
      parent = {
        id: 0,
        description: null,
        title: null,
        children: null,
        shown: null,
        rootNode: null,
        expanded: null,
        parent: null,
        path: null,
        readonly: null
      };
    }
    const children = data.filter((child) => child.parent === parent.id);

    if (children.length) {
      if (!parent.id) {
        tree = children;
      } else {
        parent.children = children;
      }
      for (const child of children) {
        this.buildTreeData(data, child, null);
      }
    }
    return tree;
  }

  private getSearchablePaths(path: string, searchPaths: string[]): void {
    const parts = path.split('\\');
    let searchPath = '';
    for (const part of parts) {
      if (part === '') {
        continue;
      }
      searchPath += `${part}\\`;
      if (searchPaths.indexOf(searchPath) === -1) {
        searchPaths.push(searchPath);
      }
    }
  }

  private resetTree(showRootNodes: boolean): void {
    for (const node of this.nodes) {
      if (node.id === 0) {
        if (!node.shown) {
          node.shown = true;
        }
        if (!node.expanded) {
          node.expanded = true;
        }
      } else if (showRootNodes && node.parent === 0) {
        if (!node.shown) {
          node.shown = true;
        }
        if (node.expanded) {
          node.expanded = false;
        }
      } else {
        if (node.shown) {
          node.shown = false;
        }
        if (node.expanded) {
          node.expanded = false;
        }
      }
    }
    this.onChangedFolder();
  }

  private softResetTree(): void {
    this.resetTree(true);
    if (!this.tempFolders) {
      return;
    }
    MyRepositoryLocalConfig.setFolders(this.tempFolders);
    const nodesMap: DataMap<FolderTreeItem> = {};
    for (const node of this.nodes) {
      nodesMap[node.path] = node;
    }
    for (const folder of this.tempFolders) {
      const node = nodesMap[folder.path];
      if (node) {
        node.shown = folder.shown;
        node.expanded = folder.expanded;
        if (folder.expanded && node.children) {
          for (const child of node.children) {
            child.shown = true;
          }
        }
      }
    }
    this.tempFolders = null;
    this.cdr.detectChanges();
  }

  private filterNodeData(value: string): void {
    if (!this.nodes) {
      return;
    }
    this.dirtySearch = false;
    const searchToken = value.trim().toLowerCase();
    if (searchToken === '') {
      this.notRepositoryFound = false;
      this.softResetTree();
      return;
    }
    this.resetTree(false);
    const searchPaths: string[] = [];
    if (searchToken !== '') {
      this.notRepositoryFound = false;
      for (const node of this.nodes) {
        if (node.id === 0) {
          continue;
        }
        const containsToken = node.title.toLowerCase().indexOf(searchToken) > -1;
        if (containsToken) {
          if (!node.path) {
            continue;
          }
          this.getSearchablePaths(node.path, searchPaths);
        }
      }
      if (searchPaths.length === 0) {
        this.notRepositoryFound = true;
      } else {
        for (const node of this.nodes) {
          const containsPath = searchPaths.indexOf(node.path) > -1;
          if (containsPath) {
            node.shown = true;
            node.expanded = true;
          }
        }
      }
    }
    this.onChangedFolder();
  }

  newSearch(): void {
    if (this.searchValue?.length === 1) {
      return;
    }
    this._searchValue.next(this.searchValue);
  }

  toggleExpandedFolder(): void {
    if (!this.searchActive) {
      return;
    }
    this.dirtySearch = true;
    this.onChangedFolder();
  }

  onChangedFolder(): void {
    this._changedFolder.next(true);
  }

  toggleNodes(): void {
    this.expandAll = !this.expandAll;
    this.toggleTree(this.expandAll);
    if (this.searchActive) {
      this.dirtySearch = true;
    }
  }
}
