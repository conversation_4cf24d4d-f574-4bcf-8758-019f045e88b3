import type { i18n } from '@/core/bnext-core.component';
import { BnextCoreComponent } from '@/core/bnext-core.component';
import type { IFileData, IFileUploadAdded } from '@/core/drop-file/drop-file.interfaces';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslatePipe } from '@/core/i18n/bnext-translate.pipe';
import type { ElementRef } from '@angular/core';
import { Component, input, output, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  type IDialogEventArgs,
  IgxButtonGroupModule,
  IgxButtonModule,
  IgxDialogModule,
  IgxIconModule,
  IgxRippleModule
} from '@infragistics/igniteui-angular';
import type { FileItem, FileLikeObject } from 'ng2-file-upload';
import { MultiFileUploadComponent } from 'src/app/core/multi-file-upload/multi-file-upload.component';
import { RestApiModule } from 'src/app/core/rest-api.module';
import { BulkTab } from './bulk-upload.enum';

@Component({
  selector: 'app-bulk-upload',
  templateUrl: './bulk-upload.component.html',
  styleUrls: ['./bulk-upload.component.scss'],
  standalone: true,
  imports: [
    FormsModule,
    MultiFileUploadComponent,
    IgxIconModule,
    IgxDialogModule,
    IgxButtonModule,
    IgxRippleModule,
    IgxButtonGroupModule,
    ReactiveFormsModule,
    BnextTranslatePipe
  ]
})
export class BulkUploadComponent extends BnextCoreComponent implements i18n {
  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'shared',
    componentName: 'bulk-upload'
  };
  LangConfig = BulkUploadComponent.LANG_CONFIG;
  allowedMimeType = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'];
  // outputs
  public readonly closed = output<IDialogEventArgs>();
  //inputs
  public readonly parent = input<any>(undefined);
  public readonly downloadAfterUpload = input<any>(true);
  public readonly bulkUploadController = input('');
  public readonly bulkTemplateDownloadController = input('');
  // viewchilds
  readonly fileuploader = viewChild<MultiFileUploadComponent>('fileuploader');
  readonly downloadMultiAnchor = viewChild<ElementRef>('downloadMultiAnchor');
  // vars
  isOpen = false;
  EnumBulkTab = BulkTab;
  selectedTab: BulkTab = BulkTab.UPLOAD;

  override get componentPath(): string {
    return BulkUploadComponent.LANG_CONFIG.componentPath;
  }

  override get tagName(): string {
    return BulkUploadComponent.LANG_CONFIG.componentName;
  }

  get uploadController(): string {
    return `${this.bulkUploadController()}`;
  }

  override langReady() {
    //Do nothing
  }

  public show(): void {
    this.isOpen = true;
    this.cdr.detectChanges();
  }

  onClosed(event: IDialogEventArgs) {
    this.isOpen = false;
    this.closed.emit(event);
  }

  uploadedFile(fileData: IFileUploadAdded): void {
    if (this.downloadAfterUpload()) {
      this.fileuploader().download(fileData.file);
    }
    this.loader.hide();
  }

  afterAddingFile(_item: FileItem): void {
    this.loader.show();
  }

  addingFailed(_item: FileLikeObject): void {
    this.loader.hide();
  }

  uploadFailed(_item: IFileData): void {
    this.loader.hide();
  }

  downloadTemplate(): void {
    if (this.bulkTemplateDownloadController() === '') {
      console.error('Misssing bulkTemplateDownloadController url');
      return;
    }
    const downloadAnchor = this.downloadMultiAnchor().nativeElement;
    const base = RestApiModule.url();
    downloadAnchor.href = `${base}${this.bulkTemplateDownloadController()}`;
    downloadAnchor.click();
    downloadAnchor.href = 'javascript: void(0);';
  }

  selectTab(tab: BulkTab) {
    this.selectedTab = tab;
    this.cdr.detectChanges();
  }
}
