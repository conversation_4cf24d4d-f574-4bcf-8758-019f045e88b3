import type { DynamicFieldsDTO } from '@/core/dynamic-field/field-handler/field-handler.interfaces';
import type { DataMap } from '@/core/utils/data-map';
import type { TimesheetDataSourceDto } from 'src/app/modules/timesheet/timesheet-add/timesheet-datasource';
import type { GroupRow } from './activities-add-many.interfaces';
import type { ActivityDataSourceDto } from './core/utils/activity-base.interfaces';

export interface TemplateLoaded {
  groups?: GroupRow[];
  showVariables?: boolean;
  createdBy: number;
  isPrivateTemplate: boolean;
  dynamicFieldData: DataMap<DynamicFieldsDTO>;
  typeData: DataMap<ActivityDataSourceDto>;
  timesheetDs: TimesheetDataSourceDto;
}

export interface TemplateCounterUpdate {
  businessUnitCount: number;
  businessUnitDepartmentCount: number;
}
