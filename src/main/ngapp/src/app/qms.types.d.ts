/* tslint:disable */
/* eslint-disable */
// Generated using typescript-generator version 3.2.1263 on 2025-05-12 16:07:44.

export interface AiChatResponse {
    message: string;
    esError: boolean;
    errorDescription: string;
}

export interface AiEsTestDao {
    esUrl: string;
    esUser: string;
    esPassword: string;
    esIndex: string;
    esCert: string;
}

export interface AiModelSettingsDao {
    summaryProvider: AiProvider;
    summaryModel: string;
    embeddingProvider: AiProvider;
    embeddingModel: string;
    questionOptimizationProvider: AiProvider;
    questionOptimizationModel: string;
    chatResponseProvider: AiProvider;
    chatResponseModel: string;
}

export interface AiSettingsDao {
    enabled: boolean;
    esUrl: string;
    esUser: string;
    esPassword: string;
    esIndex: string;
    esCert: string;
    ollamaUrl: string;
    openaiKey: string;
}

export interface OpenAiTestDao {
    openaiKey: string;
}

export interface ollamaTestDao {
    ollamaUrl: string;
}

export type AiProvider = "OPEN_AI" | "OLLAMA";
