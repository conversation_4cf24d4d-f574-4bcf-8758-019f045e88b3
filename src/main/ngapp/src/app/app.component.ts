import type { Timer } from '@/core/bnext-core.interfaces';
import { NavigateLanguageService } from '@/core/services/navigate-lang.service';
import { DEFAULT_DEVICE_INFO, dispatchResize } from '@/core/utils/DeviceUtil';
import { MenuService } from '@/modules/menu/services/menu.service';
import { swRegister } from '@/shared/updates/sw-utils';
import { HttpClient } from '@angular/common/http';
import type { AfterContentInit, AfterViewInit, OnDestroy, OnInit } from '@angular/core';
import { ApplicationRef, CUSTOM_ELEMENTS_SCHEMA, Component, Injector, NgZone, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { SwUpdate } from '@angular/service-worker';
import { IgxIconService } from 'igniteui-angular';
import { Subject, type Subscription, fromEvent, interval, merge } from 'rxjs';
import { debounce, takeUntil } from 'rxjs/operators';
import { setupIcons, setupLang } from './app.utils';
import { BnextTranslateService } from './core/i18n/bnext-translate.service';
import { RestApiModule } from './core/rest-api.module';
import { BnextLoaderActivationService } from './core/services/bnext-loader-activation.service';
import { DialogService } from './core/services/dialog.service';
import { ThemeService } from './core/theme-service';
import { devextremeConfig } from './devextreme/devextreme-config';
import { initCustomElements } from './modules/elements/utils/bnext-handler-utils';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  imports: [RouterOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppComponent implements OnInit, OnDestroy, AfterViewInit, AfterContentInit {
  private navLang = inject(NavigateLanguageService);
  private updates = inject(SwUpdate);
  private dialogService = inject(DialogService);
  private iconService = inject(IgxIconService);
  private translateService = inject(BnextTranslateService);
  protected menuService = inject(MenuService);
  private loader = inject(BnextLoaderActivationService);
  private httpClient = inject(HttpClient);

  private appRef = inject(ApplicationRef);
  private zone = inject(NgZone);
  private injector = inject(Injector);
  private $destroy = new Subject<unknown>();
  #resizeTimeout: Timer;
  #resizeSubs: Subscription;
  #deviceInfo = DEFAULT_DEVICE_INFO;

  protected get faviconSrc(): string {
    return `${RestApiModule.app()}favicon.ico?${new Date().getTime()}`;
  }

  public constructor() {
    this.initCustomElements();
    this.loader.show();
    devextremeConfig();
    ThemeService.setCurrentTheme();
    swRegister(this.httpClient, this.updates, this.translateService, this.loader, this.dialogService, this.appRef);
  }

  public ngOnInit() {
    this.updateFavicon();
    this.updateFavicon();
    const currentLang = this.navLang.getLang() || 'es';
    setupLang(currentLang, this.translateService, this.$destroy);
  }

  public ngAfterViewInit() {
    setupIcons(this.iconService);
  }

  public ngAfterContentInit() {
    if (!this.#resizeSubs) {
      this.#resizeSubs = merge(fromEvent(window, 'resize'), fromEvent(window, 'orientationChange'), fromEvent(window, 'fullscreenchange'))
        .pipe(debounce(() => interval(350)))
        .subscribe((value) => this.loader.onWidowsResized(value));
    }
    this.loader.requestRefreshWindowSize.pipe(takeUntil(this.$destroy)).subscribe((lastMs) => {
      this.refreshWindowSize(lastMs);
    });
    this.menuService
      .getMainAppBarHeight()
      .pipe(takeUntil(this.$destroy))
      .subscribe((appBarHeight) => this.onMainAppHeightUpdated(appBarHeight));
  }

  private onMainAppHeightUpdated(appBarHeight: number): void {
    if (typeof appBarHeight === 'number') {
      this.#deviceInfo.appBarHeight = appBarHeight || 0;
    }
    if (appBarHeight > 0) {
      dispatchResize();
    }
  }

  public ngOnDestroy(): void {
    this.clearResizeTimeout();
    if (this.#resizeSubs) {
      this.#resizeSubs.unsubscribe();
    }
    this.$destroy.next(null);
    this.$destroy.complete();
  }

  private clearResizeTimeout() {
    if (this.#resizeTimeout) {
      clearTimeout(this.#resizeTimeout);
      this.#resizeTimeout = null;
    }
  }

  protected refreshWindowSize(lastMs: number) {
    let lastForMs = lastMs;
    while (lastForMs) {
      // cada 10 milisegundos dispara un timeout
      if (--lastForMs % 150 !== 0) {
        continue;
      }
      this.clearResizeTimeout();
      this.#resizeTimeout = setTimeout(() => dispatchResize(), lastForMs);
    }
  }

  private initCustomElements() {
    if (this.injector) {
      initCustomElements(this.zone, this.injector);
    } else {
      console.warn('Invalid injector provided');
    }
  }

  private updateFavicon(): void {
    const link = document.createElement('link');
    const oldLink = document.getElementById('dynamic-favicon');
    link.id = 'dynamic-favicon';
    link.rel = 'shortcut icon';
    link.href = this.faviconSrc;
    if (oldLink) {
      document.head.removeChild(oldLink);
    }
    document.head.appendChild(link);
  }
}
