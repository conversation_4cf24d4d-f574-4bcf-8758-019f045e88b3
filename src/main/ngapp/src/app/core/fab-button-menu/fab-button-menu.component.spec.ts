import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from '../test/mock-core.module';
import { FabButtonMenuComponent } from './fab-button-menu.component';

import { MenuService } from '@/modules/menu/services/menu.service';
import { MockProviders } from '../test/mock-providers';
import { MockMenuService } from '../test/services/mock-menu.service';
import { configureTestSuite } from '../test/utils/configure-test-suite';

describe('FabButtonMenuComponent', () => {
  let component: FabButtonMenuComponent;
  let fixture: ComponentFixture<FabButtonMenuComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule],
      providers: [
        ...MockProviders.PROVIDERS,
        {
          provide: MenuService,
          useClass: MockMenuService
        }
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FabButtonMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
