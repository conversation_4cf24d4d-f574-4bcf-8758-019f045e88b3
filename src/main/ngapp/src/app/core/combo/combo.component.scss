//Los colores del componente se pueden configurar desde el archivo componentColors.scss en la carpeta src
@use 'src/styles/immutable-colors' as *;

.input-display {
  display: none;
}

:host ::ng-deep .igx-combo {
  .igx-input-group--placeholder ::placeholder {
    color: rgba($fg-color, 0.54);
  }
  .igx-input-group--placeholder.igx-input-group--invalid ::placeholder {
    color: red;
  }
}

:host ::ng-deep .igx-input-group--required .combo-container .igx-input-group__label::after {
  margin-left: -0.5rem;
}

.error-icon {
  position: absolute;
  bottom: 0;
  right: 2.4rem;
}
