<div class="combo-container">
  <igx-combo
    #combo
    width="100%"
    [allowCustomValues]="false"
    [data]="data"
    [disabled]="disabled"
    [ngClass]="displayDensityClass"
    [displayKey]="displayKey"
    [formControl]="inputCtrl"
    [overlaySettings]="overlaySettings"
    [required]="required"
    [searchPlaceholder]="searchPlaceholder()"
    [type]="type()"
    [valueKey]="valueKey"
    [groupKey]="groupKey()"
    (closed)="onClosed()"
    (closing)="onClosing()"
    (opening)="onOpening()"
    [(ngModel)]="value"
    (selectionChanging)="onSelectionChanging($event)"
  >
    <ng-container ngProjectAs="[igxLabel]">
      <label #inputLabel igxLabel [class.is-invalid]="invalid" (click)="toggleValue($event)"> {{ label() }} </label>
    </ng-container>
    <ng-template igxComboEmpty>
      <span class="empty-class">{{ emptyCombo }}</span>
    </ng-template>
    <ng-template igxComboToggleIcon let-collapsed>
      <igx-icon #toggleIcon (click)="onComboToggleIcon($event)">{{ collapsed ? collapsedIcon() : expandedIcon() }}</igx-icon>
    </ng-template>
  </igx-combo>
  @if (invalid && !value?.length) {
    <igx-suffix class="error-icon">
      <igx-icon>error</igx-icon>
    </igx-suffix>
  }
  @if (showHelp()) {
    <div class="help">
      <div class="general">
        <h4>{{ label() }}</h4>
        <span [innerHtml]="help()"></span>
      </div>
      @for (item of data; track item) {
        <div>
          <h5>{{ item[displayKey] }}</h5>
          <span [innerHtml]="item.help"></span>
        </div>
      }
    </div>
  }
</div>
