<div class="periodicity-container">
  <div class="grid-x grid-padding-x display-value">
    @if (!hidePeriodicity()) {
      <div class="cell small-12 display-form-cell" [formGroup]="displayForm">
        <igx-input-group type="line" style="width: 100%" [title]="shortDescription" theme="material" [ngClass]="displayDensityClass">
          <igx-prefix class="display-value-prefix">
            <igx-icon>schedule</igx-icon>
          </igx-prefix>
          <label igxLabel [for]="name()" (click)="openDialog()">{{ label }}</label>
          <input
            #displayValueInput
            igxInput
            [name]="name()"
            type="text"
            formControlName="displayValue"
            [disabled]="disabled"
            readonly="readonly"
            [required]="required"
            (keyup)="handleKeyUp($event)"
            (keydown)="handleKeyDown($event)"
            (click)="openDialog()"
          />
          <igx-suffix>
            @if (value && allowNever()) {
              <igx-icon (click)="clear()">clear</igx-icon>
            }
            <igx-icon (click)="openDialog()">{{ arrowSelect }}</igx-icon>
          </igx-suffix>
        </igx-input-group>
      </div>
    }
  </div>
  @if (!hidePeriodicity() && isOpen) {
    <igx-dialog
      #dialog
      rightButtonType="contained"
      [title]="dialogLabel"
      [rightButtonLabel]="tags.closeButton"
      (rightButtonSelect)="close()"
      (closed)="isOpen = false"
      [closeOnOutsideSelect]="true"
      [isOpen]="isOpen"
      (opening)="onOpen()"
      (closing)="onClose()"
    >
      <form [formGroup]="form" class="form grid-container">
        <div class="grid-x grid-padding-x grid-padding-y" [class.never-selected]="type === 'never'">
          @if (!sameDay()) {
            <div class="cell small-12 durationDays">
              <igx-input-group theme="material" [ngClass]="displayDensityClass">
                <input igxInput name="durationDays" formControlName="durationDays" type="number" [required]="true" maxlength="255" step="1" min="1" max="999" />
                <label igxLabel for="durationDays">{{ tags.durationDays }}</label>
              </igx-input-group>
            </div>
          }
          <div class="cell small-12 startDate" [hidden]="startDateHidden()">
            <igx-date-picker
              #startDatePicker
              name="startDate"
              formControlName="startDate"
              [ngClass]="displayDensityClass"
              [disabledDates]="enableDatesRestrictions() ? disabledDates() : []"
              [displayFormat]="dateFormat"
              [disabled]="!rangeEditable()"
              [todayButtonLabel]="tags.todayButton"
              [cancelButtonLabel]="tags.cancelButton"
              mode="dialog"
            >
              <label igxLabel>{{ tags.startDate }}</label>
            </igx-date-picker>
            @if (enableDatesRestrictions() && disabledDates() && !validStartDate) {
              <label class="disabledDates"> {{ disabledDatesInfo }} </label>
            }
          </div>
          <div class="cell small-12 endDate" [hidden]="!hasEnd">
            <igx-date-picker
              #endDatePicker
              name="endDate"
              formControlName="endDate"
              [displayFormat]="dateFormat"
              [ngClass]="displayDensityClass"
              [disabled]="!rangeEditable()"
              [todayButtonLabel]="tags.todayButton"
              [cancelButtonLabel]="tags.cancelButton"
              mode="dialog"
            >
              <label igxLabel>{{ tags.endDate }}</label>
            </igx-date-picker>
          </div>
          <div class="cell small-12 type" [class.medium-9]="type !== 'hybrid' && type !== 'never'">
            <app-select
              [displayDensity]="displayDensity"
              name="periodicityType"
              formControlName="periodicityType"
              [label]="tags.type"
              [data]="types"
              valueKey="value"
              displayKey="text"
            >
            </app-select>
          </div>
          <div class="cell small-12 medium-3 daily hide-on-never" [hidden]="type !== 'daily'">
            <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
              <label igxLabel for="dailyDays">{{ tags.every }}</label>
              <input igxInput name="dailyDays" formControlName="dailyDays" type="number" />
              <igx-suffix>
                @if (dailyDays === 1) {
                  <label>{{ tags.day }}</label>
                }
                @if (dailyDays !== 1) {
                  <label>{{ tags.days }}</label>
                }
              </igx-suffix>
            </igx-input-group>
          </div>
          <div class="cell small-12 medium-3 weekly hide-on-never" [hidden]="type !== 'weekly'">
            <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
              <label igxLabel for="weeklyWeeks">{{ tags.every }}</label>
              <input igxInput name="weeklyWeeks" formControlName="weeklyWeeks" type="number" maxlength="4" min="1" />
              <igx-suffix>
                @if (weeklyWeeks === 1) {
                  <label>{{ tags.week }}</label>
                }
                @if (weeklyWeeks !== 1) {
                  <label>{{ tags.weeks }}</label>
                }
              </igx-suffix>
            </igx-input-group>
          </div>
          <div class="cell small-12 weekly hide-on-never" [hidden]="type !== 'weekly'">
            <label>{{ tags.repeatOn }}</label>
            <div class="grid-x">
              @for (day of weeklyDays; track day) {
                <div class="cell small-12 medium-4 large-3">
                  <igx-switch [name]="day.id" [formControlName]="day.id" [checked]="false" (change)="selectDay($event, day)">
                    {{ tags.weekDays[day.index] | titlecase }}
                  </igx-switch>
                </div>
              }
            </div>
          </div>
          <div class="cell small-12 medium-3 monthly hide-on-never" [hidden]="type !== 'monthly'">
            <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
              <label igxLabel for="monthlyMonths">{{ tags.every }}</label>
              <input igxInput name="monthlyMonths" formControlName="monthlyMonths" type="number" maxlength="4" min="1" />
              <igx-suffix>
                @if (monthlyMonths === 1) {
                  <label>{{ tags.month }}</label>
                }
                @if (monthlyMonths !== 1) {
                  <label>{{ tags.months }}</label>
                }
              </igx-suffix>
            </igx-input-group>
          </div>
          <div class="cell small-12 monthly hide-on-never" [hidden]="type !== 'monthly'">
            <label>{{ tags.repeatBy }}</label>
            <igx-radio-group name="byMonthly" formControlName="byMonthly" class="grid-x">
              <div class="cell small-12 medium-4 large-3">
                <igx-radio value="dayOfMonth" [checked]="true">
                  <label>{{ tags.dayOfMonth }}</label>
                </igx-radio>
              </div>
              <div class="cell small-12 medium-4 large-3">
                <igx-radio value="dayOfWeek">
                  <label>{{ tags.dayOfWeek }}</label>
                </igx-radio>
              </div>
            </igx-radio-group>
          </div>
          <div class="cell small-12 medium-3 yearly hide-on-never" [hidden]="type !== 'yearly'">
            <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
              <label igxLabel for="yearlyYears">{{ tags.every }}</label>
              <input igxInput name="yearlyYears" formControlName="yearlyYears" type="number" maxlength="4" min="1" />
              <igx-suffix>
                @if (yearlyYears === 1) {
                  <label>{{ tags.year }}</label>
                }
                @if (yearlyYears !== 1) {
                  <label>{{ tags.years }}</label>
                }
              </igx-suffix>
            </igx-input-group>
          </div>
        </div>
        <div class="cell small-12 hybrid hide-on-never" [hidden]="type !== 'hybrid'">
          <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
            <label igxLabel for="hybridDays">{{ tags.every }}</label>
            <input igxInput name="hybridDays" formControlName="hybridDays" type="number" maxlength="4" min="1" />
            <igx-suffix>
              @if (hybridDays === 1) {
                <label>{{ tags.day }}</label>
              }
              @if (hybridDays !== 1) {
                <label>{{ tags.days }}</label>
              }
            </igx-suffix>
          </igx-input-group>
          <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
            <label igxLabel for="hybridWeeks">{{ tags.every }}</label>
            <input igxInput name="hybridWeeks" formControlName="hybridWeeks" type="number" maxlength="4" min="1" />
            <igx-suffix>
              @if (hybridWeeks === 1) {
                <label>{{ tags.week }}</label>
              }
              @if (hybridWeeks !== 1) {
                <label>{{ tags.weeks }}</label>
              }
            </igx-suffix>
          </igx-input-group>
          <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
            <label igxLabel for="hybridMonths">{{ tags.every }}</label>
            <input igxInput name="hybridMonths" formControlName="hybridMonths" type="number" maxlength="4" min="1" />
            <igx-suffix>
              @if (hybridMonths === 1) {
                <label>{{ tags.month }}</label>
              }
              @if (hybridMonths !== 1) {
                <label>{{ tags.months }}</label>
              }
            </igx-suffix>
          </igx-input-group>
          <igx-input-group type="line" theme="material" [ngClass]="displayDensityClass">
            <label igxLabel for="hybridYears">{{ tags.every }}</label>
            <input igxInput name="hybridYears" formControlName="hybridYears" type="number" maxlength="4" min="1" />
            <igx-suffix>
              @if (hybridYears === 1) {
                <label>{{ tags.year }}</label>
              }
              @if (hybridYears !== 1) {
                <label>{{ tags.years }}</label>
              }
            </igx-suffix>
          </igx-input-group>
        </div>
        <div class="cell small-12 description">
          <igx-input-group theme="material" [ngClass]="displayDensityClass">
            <textarea autoresize igxInput type="text" [readonly]="true" name="description" [value]="description"> </textarea>
            <label igxLabel for="description">{{ tags.description }}</label>
          </igx-input-group>
        </div>
        @if (sameDay()) {
          <div class="cell small-12 nextDate" [hidden]="type === 'never'">
            <igx-input-group theme="material" [ngClass]="displayDensityClass">
              <input igxInput type="text" [readonly]="true" name="nextDate" [value]="nextDateStart === null ? tags.notValidRange : (nextDateStart | date: dateFormat)" />
              <label igxLabel for="nextDate">{{ tags.nextDate }}</label>
            </igx-input-group>
          </div>
        }
        @if (!sameDay()) {
          <div class="cell small-12 nextDateStart" [hidden]="type === 'never'">
            <igx-input-group theme="material" [ngClass]="displayDensityClass">
              <input
                igxInput
                type="text"
                [readonly]="true"
                name="nextDateStart"
                [value]="nextDateStart === null ? tags.notValidRange : (nextDateStart | date: dateFormat)"
              />
              <label igxLabel for="nextDateStart">{{ tags.nextDateStart }}</label>
            </igx-input-group>
          </div>
        }
        @if (!sameDay()) {
          <div class="cell small-12 nextDateEnd" [hidden]="type === 'never'">
            <igx-input-group theme="material" [ngClass]="displayDensityClass">
              <input igxInput type="text" [readonly]="true" name="nextDateEnd" [value]="nextDateEnd === null ? tags.notValidRange : (nextDateEnd | date: dateFormat)" />
              <label igxLabel for="nextDateEnd">{{ tags.nextDateEnd }}</label>
            </igx-input-group>
          </div>
        }
      </form>
    </igx-dialog>
  }
</div>
