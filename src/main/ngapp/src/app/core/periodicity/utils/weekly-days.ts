import { keysObject } from '@/core/utils/object';
import { PeriodicityWeeklyDay, WeeklyDay } from './weekly-day';
import type { WeeklyDayEntity } from './weekly-day-entity';

export class WeeklyDays {
  public static get values(): WeeklyDayEntity[] {
    const values = [];
    for (const key1 of keysObject(WeeklyDay).filter((key) => !Number.isNaN(Number(WeeklyDay[key])))) {
      values.push({ id: key1, description: key1, index: WeeklyDay[key1] } as WeeklyDayEntity);
    }
    return values;
  }

  public static get legacyValues(): WeeklyDayEntity[] {
    const values = [];
    for (const key1 of keysObject(PeriodicityWeeklyDay).filter((key) => !Number.isNaN(Number(PeriodicityWeeklyDay[key])))) {
      values.push({ id: key1, description: key1, index: PeriodicityWeeklyDay[key1] } as WeeklyDayEntity);
    }
    return values;
  }
}
