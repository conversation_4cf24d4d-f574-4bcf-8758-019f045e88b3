@use "sass:list";

$bg-colors: (
  rgba(0, 128, 0, 0.5),
  rgba(0, 0, 255, 0.5),
  rgba(75, 0, 130, 0.5),
  rgba(255, 0, 0, 0.5),
  rgba(255, 165, 0, 0.5),
  rgba(255, 255, 0, 0.5)
);

:host {
  margin: 1rem;
  line-height: 1rem;
  font-size: 1rem;
  width: 100%;

  .field-rules-list > div {
    @for $i from 1 through 6 {
      &:nth-child(6n+#{$i}) > igx-list igx-list-item .field-rules .rule-chip {
        background-color: list.nth($bg-colors, $i);
      }
    }
  }

  .field-rules-list {

    .igx-list__header {
      font-weight: 700;
      font-size: 1rem;
    }

    .igx-list__item-content {
      min-height: 3rem;
    }

    igx-list-item:hover .field-rules-remove {
      display: block;
    }

    .field-rules-remove {
      display: none;
      position: absolute;
      right: 0;
    }
  }

  .info-banner {
    background-color: #faebd7;
    padding: 0.5rem;
  }

  .pl-1,
  .cell > .grid-x.grid-padding-x {
    padding-left: 1rem;
  }

  .field-rules-number {
    padding-left: 3rem;
    font-size: 0.85rem;
    font-weight: bold;
    padding-right: 0.25rem;
  }

  .field-rules {
    display: inline-block;
    margin-left: 0rem;
    text-transform: lowercase;
    color: black;
    font-style: italic;
    font-size: 0.85rem;

    &::first-letter {
      text-transform: uppercase;
    }

    > span {
      background-color: white;
      border-radius: 0.35rem;
      border: 1px solid lightgray;
      display: inline-block;
      padding: 0 0.35rem;
      font-style: normal;
      color: white;
      position: relative;

      &.rule-chip {
        text-transform: none;
        min-width: 5rem;
      }

      > .rule-buttons,
      > igx-icon {
        position: absolute;
        top: -0.5rem;
        display: none;
        left: 10%;
      }

      igx-icon {
        background-color: white;
        color: black;
        border-radius: 2rem;
        border: 1px solid;
        padding: 0.15rem;
        width: 2rem;
        height: 2rem;
        text-align: center;
      }

      &:hover {
        > .rule-buttons,
        > igx-icon {
          display: inline-block;
        }
      }
    }
  }
  .invalid-rule > .invalid-field {
    outline: 0.25rem red solid;
  }
}
