import type { DialogSelectorItem } from '@/core/services/custom-dialog.interfaces';
import type { IValid, Persistable } from '@/core/utils/interfaces';
import type {
  AggregateFunctionType,
  FieldSourceType,
  SlimReportValueType,
  TransformedFieldRuleType,
  TransformedTypeFinalValueType
} from '@/modules/forms/slim-report-base/slim-report-base.enums';
import type { HierarchyFieldDTO } from '../external-catalog/utils/hierarchy.interfaces';
import type { CommonAction } from '../utils/enums';
import type { TextHasValue } from '../utils/text-has-value';

export interface DatabaseQuery {
  status: number;
  deleted: number;
  code: string;
  description: string;
  createdDate: Date;
  lastModifiedDate: Date;
  createdBy: number;
  lastModifiedBy: number;
  source: string;
  databaseConnectionId: string;
  module: string;
  sessionStatement: string;
  readonly: boolean;
  enableCache: boolean;
  cacheSyncDate: Date;
  builtCache: boolean;
  cachePrimaryKeyId: number;
  cacheVersionKeyId: number;
  cacheSchedulerType: string;
  cacheSchema: string;
  cacheTable: string;
  cacheTableId: number;
  cacheTableName: string;
  lastTestDate: Date;
  computedData: string;
  hasColumnNamedId: boolean;
  rangeFilterType: number;
  rangeFilterField: string;
  rangeFilterName: string;
  computedFirstColumnCode: string;
  computedSelect: string;
  computedPrimaryKeyType: string;
  // Insert's
  computedCacheInsertAll: string;
  computedCacheInsertRecord: string;
  computedCacheInsertAllCount: string;
  computedCacheInsertRecordCount: string;
  // Update's
  computedCacheUpdateAll: string;
  computedCacheUpdateRecord: string;
  computedCacheUpdateAllCount: string;
  computedCacheUpdateRecordCount: string;
  // Delete's
  computedCacheDeleteAll: string;
  computedCacheDeleteAllCount: string;
  computedCacheDeleteRecord: string;
  computedCacheDeleteRecordCount: string;
}

export interface ReportsFieldDTO extends DialogSelectorItem {
  fieldCode: string;
  fieldSource: FieldSourceType;
  fieldValueType: SlimReportValueType;
}
export interface ReportsFieldRuleDTO {
  ruleDateValue?: Date;
  ruleFieldValue?: ReportsFieldDTO;
  ruleNumberValue?: number;
  ruleStringValue?: string;
  type: TransformedFieldRuleType;
  typeFinalValue: TransformedTypeFinalValueType;
}
export interface ReportColumnRuleDTO extends Persistable, IValid {
  config: ReportsFieldRuleDTO;
  evaluatedField: ReportsFieldDTO;
  finalValue: string;
  order: number;
}
export interface ReportColumnDTO {
  catalogValues: TextHasValue[];
  description: string;
  gridOrder: number;
  groupedValueKey?: string;
  groupedValueFunction?: AggregateFunctionType;
  groupingDirection: number;
  groupingPriority: number;
  hierarchyCode: string;
  hierarchyDescription: string;
  hierarchyId: number;
  id: number;
  onlyFilter: boolean;
  queryColumnCode: string;
  queryColumnId: number;
  rules: ReportColumnRuleDTO[];
  sortDirection: number;
  sortPriority: number;
  type: number;
  width: string;
}

export interface ReportHierarchyDTO {
  databaseQueryId: number;
  fields: HierarchyFieldDTO[];
}

export interface ReportDTO {
  actionStripOptions: CommonAction[];
  description: string;
  details: string;
  documentMasterId: string;
  excelUploadEnabled: boolean;
  hierarchies: ReportHierarchyDTO[];
  lastModifiedDate: Date;
  localTimeForDates: boolean;
  module?: string;
  queryId: number;
  rangeFilterField: string;
  rangeFilterName: string;
  rangeFilterType: number;
  reportColumns: ReportColumnDTO[];
  reportId: number;
  scripts: string;
  styles: string;
  slimReportCode: string;
}
