import { GlobalError<PERSON>and<PERSON> } from '@/core/global-error-handler';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, type ModuleWithProviders, NgModule } from '@angular/core';
import {
  DEFAULT_LANGUAGE,
  FakeMissingTranslationHandler,
  ISOLATE_TRANSLATE_SERVICE,
  MissingTranslationHandler,
  TranslateCompiler,
  TranslateDefaultParser,
  TranslateFakeCompiler,
  TranslateFakeLoader,
  TranslateLoader,
  TranslateModule,
  type TranslateModuleConfig,
  TranslateParser,
  TranslateStore,
  USE_DEFAULT_LANG,
  USE_EXTEND
} from '@ngx-translate/core';
import { BnextTranslateDirective } from './bnext-translate.directive';
import { BnextTranslatePipe } from './bnext-translate.pipe';
import { BnextTranslateService } from './bnext-translate.service';

@NgModule({
  imports: [BnextTranslatePipe, BnextTranslateDirective],
  exports: [BnextTranslatePipe, BnextTranslateDirective]
})
export class BnextTranslateModule extends TranslateModule {
  /**
   * Use this method in your root module to provide the TranslateService
   */
  static forRoot(config: TranslateModuleConfig = {}): ModuleWithProviders<BnextTranslateModule> {
    return {
      ngModule: BnextTranslateModule,
      providers: [
        config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },
        config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },
        config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },
        config.missingTranslationHandler || {
          provide: MissingTranslationHandler,
          useClass: FakeMissingTranslationHandler
        },
        TranslateStore,
        { provide: ISOLATE_TRANSLATE_SERVICE, useValue: config.isolate },
        { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },
        { provide: USE_EXTEND, useValue: config.extend },
        { provide: DEFAULT_LANGUAGE, useValue: config.defaultLanguage },
        BnextTranslateService,
        {
          provide: ErrorHandler,
          useClass: GlobalErrorHandler // <-- Agregar servicio a todos los módulos que usen lazy-load (loadchildren)
        }
      ]
    };
  }

  /**
   * Use this method in your other (non root) modules to import the directive/pipe
   */
  static forChild(config: TranslateModuleConfig = {}): ModuleWithProviders<BnextTranslateModule> {
    return {
      ngModule: BnextTranslateModule,
      providers: [
        config.loader || { provide: TranslateLoader, useClass: TranslateFakeLoader },
        config.compiler || { provide: TranslateCompiler, useClass: TranslateFakeCompiler },
        config.parser || { provide: TranslateParser, useClass: TranslateDefaultParser },
        config.missingTranslationHandler || {
          provide: MissingTranslationHandler,
          useClass: FakeMissingTranslationHandler
        },
        { provide: ISOLATE_TRANSLATE_SERVICE, useValue: config.isolate },
        { provide: USE_DEFAULT_LANG, useValue: config.useDefaultLang },
        BnextTranslateService
      ]
    };
  }
}
