import { Component, Input, type OnDestroy, type OnInit, forwardRef, output } from '@angular/core';
import { type ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import type { DropdownButtonItem } from '../dropdown-button/dropdown-button.interfaces';
import { GridSelectInputType, type IBnextRowSelectionEventArgs } from '../grid-base-select/grid-base-select.interfaces';
import { GridBaseSelectModule } from '../grid-base-select/grid-base-select.module';
import { GridSelectComponent } from '../grid-select/grid-select.component';

export enum ButtonSelectAction {
  OPEN_SEARCH = 'OPEN_SEARCH',
  ELSE = 'ELSE'
}

@Component({
  selector: 'app-grid-button-select',
  imports: [GridBaseSelectModule],
  styleUrls: ['./../grid-base-select/grid-base-select.directive.scss'],
  templateUrl: './../grid-base-select/grid-base-select.directive.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GridButtonSelectComponent),
      multi: true
    }
  ]
})
export class GridButtonSelectComponent extends GridSelectComponent implements ControlValueAccessor, OnInit, OnDestroy {
  private _actions: DropdownButtonItem[] = null;

  protected _dialogTitle = 'Seleccione una opción';

  public readonly dropdownButtonAction = output<DropdownButtonItem>();

  public readonly gridSelectionFinished = output<any | number>();

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public get dialogTitle(): string {
    return this._dialogTitle;
  }
  public set dialogTitle(_dialogTitle: string) {
    this._dialogTitle = _dialogTitle;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public get actions(): DropdownButtonItem[] {
    return this._actions;
  }
  public set actions(value: DropdownButtonItem[]) {
    this._actions = value;
  }

  get baseDialogRightButtonLabel(): string {
    if (this.isEmpty) {
      return null;
    }
    return this.okDialogButton;
  }

  get baseDialogLeftButtonLabel(): string {
    return 'Cancelar';
  }
  constructor() {
    super();
    this.hideEmpty = true;
  }

  public get inputType(): GridSelectInputType {
    return GridSelectInputType.DROPDOW_BUTTON;
  }

  baseDialogRightButtonAction(emitEvent: boolean) {
    if (emitEvent) {
      this.gridSelectionFinished.emit(this.value);
    }
    this.closeDialog();
  }

  baseDialogLeftButtonAction() {
    this.closeDialog();
  }

  onRowSelectionChange(rowSelectionEvent: IBnextRowSelectionEventArgs) {
    this.rowSelectionChangeAction(rowSelectionEvent);
  }

  onDropdownButtonAction(item: DropdownButtonItem) {
    switch (item.value) {
      case ButtonSelectAction.OPEN_SEARCH:
        this.openDialog();
        break;
    }
    this.dropdownButtonAction.emit(item);
  }
}
