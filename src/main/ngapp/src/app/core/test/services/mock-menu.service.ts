import { type ElementRef, Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import type { PrintReportConfig } from 'src/app/modules/elements/bnext-report-element/bnext-report-element.interfaces';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import type { Menu } from 'src/app/modules/menu/menu-definition/menu-definition.interfaces';
import type { QualifiedGenericMenuItem } from 'src/app/modules/menu/menu-main/menu-main.interfaces';
import type { DropdownMenuItem } from '../../dropdown-menu/dropdown-menu.interfaces';
import type { FabButtonMenuItem } from '../../fab-button-menu/fab-button-menu.interface';

@Injectable()
export class MockMenuService {
  public showPendings = new Subject();
  public quickAccess = new Subject();
  public quickAccessRemove = new Subject();
  public changeAppbarTitle = new Subject();
  public changeAppbarPendingCount = new Subject();
  public changeWindow = new Subject();
  public closeWindow = new Subject();
  public refreshMenuAccess = new Subject<boolean>();
  public refreshUserData = new Subject();
  public documentViewerUrl = new Subject();
  private mainAppBarHeight = new Subject<number>();
  public addFabMenu = new Subject();
  public toggleDisplayFabMenu = new Subject();
  public selectedFabOptionMenu = new Subject();
  public changedWindow = new Subject();
  public onNewPrintReportTemplate = new Subject();

  public navigate(_path: string, _module = Module.NONE): void {}

  public navigateLegacy(_link: string, _module = Module.NONE): void {}

  public navigateMenu(_item: Menu): void {}

  public closeCurrentWindow(): void {}

  public changeTitle(_appTitle: string): void {}

  public addQuickAccess(_item: QualifiedGenericMenuItem): void {}

  public back(): void {}

  public getMainAppBarHeight(): Subject<number> {
    this.mainAppBarHeight.next(0);
    return this.mainAppBarHeight;
  }

  public setMainAppBar(_value: ElementRef) {}

  public addFabOptionsMenu(_item: FabButtonMenuItem[]): void {}

  public doActionFabOption(_optionSelected: FabButtonMenuItem | DropdownMenuItem): void {}

  public printReportTemplate(_config: PrintReportConfig): void {}
}
