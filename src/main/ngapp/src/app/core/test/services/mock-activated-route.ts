import type { Params } from '@angular/router';
import { type Observable, of } from 'rxjs';

export class MockActivatedRoute {
  snapshot: {
    params: Params;
  };

  paramMap: Observable<Params>;

  constructor(params: Params) {
    const extendedParams = {
      ...params,
      get(paramName: string) {
        return params[paramName];
      }
    };
    this.snapshot = {
      params: extendedParams
    };
    this.paramMap = of(extendedParams);
  }
}
