import { Deferred } from '@/core/utils/deferred';
import { Subject } from 'rxjs';
import type { TimesheetRegistry } from 'src/app/modules/timesheet/timesheet-add/timesheet-add.interfaces';
import type { TimesheetDataSourceDto } from 'src/app/modules/timesheet/timesheet-add/timesheet-datasource';
import type {
  CaptureTimeEvent,
  ChangePlannerEvent,
  SetupTimesheetData,
  TimesheetDeleteEvent,
  TimesheetEditEvent
} from 'src/app/modules/timesheet/timesheet-dialog/timesheet-dialog.interfaces';
import type { PlannedType } from 'src/app/modules/timesheet/timesheet-widget/timesheet-widget.enums';
import type {
  TimesheetActivityPlanned,
  TimesheetDto,
  TimesheetServiceDto,
  UpdateUIStopwatch
} from 'src/app/modules/timesheet/timesheet-widget/timesheet-widget.interfaces';
export class MockTimesheetService {
  private _timesheetAdd = new Subject<TimesheetServiceDto>();
  public timesheetAdd = this._timesheetAdd.asObservable();

  private _timesheetPush = new Subject<TimesheetDto>();
  public timesheetPush = this._timesheetPush.asObservable();

  private _timesheetStartStopwatch = new Subject<TimesheetServiceDto>();
  public timesheetStartStopwatch = this._timesheetStartStopwatch.asObservable();

  private _timesheetStartStopwatchPush = new Subject<TimesheetDto>();
  public timesheetStartStopwatchPush = this._timesheetStartStopwatchPush.asObservable();

  private _hasStopwatchrunning = new Subject<TimesheetDto>();
  public hasStopwatchrunning = this._hasStopwatchrunning.asObservable();

  private _updateUIStopwatch = new Subject<UpdateUIStopwatch>();
  public updateUIStopwatch = this._updateUIStopwatch.asObservable();

  private _updateStopwatchRegistryEmpty = new Subject<TimesheetDto>();
  public updateStopwatchRegistryEmpty = this._updateStopwatchRegistryEmpty.asObservable();

  private _stopStopwatchFromPendingList = new Subject<boolean>();
  public stopStopwatchFromPendingList = this._stopStopwatchFromPendingList.asObservable();

  private _replaceItem = new Subject<TimesheetDto>();
  public replaceItemObs = this._replaceItem.asObservable();

  private _refreshDialogDataSourceCatalogs = new Subject<boolean>();
  public refreshDialogDataSourceCatalogs = this._refreshDialogDataSourceCatalogs.asObservable();

  private _onClosedDialog = new Subject<boolean>();
  public onClosedDialog = this._onClosedDialog.asObservable();

  private _onOpenEdit = new Subject<TimesheetEditEvent>();
  public onOpenEdit = this._onOpenEdit.asObservable();

  private _onDeleteRegistry = new Subject<TimesheetDeleteEvent>();
  public onDeleteRegistry = this._onDeleteRegistry.asObservable();

  private _onChangePlannerTask = new Subject<ChangePlannerEvent>();
  public onChangePlannerTask = this._onChangePlannerTask.asObservable();

  private _onCaptureTimesheet = new Subject<CaptureTimeEvent>();
  public onCaptureTimesheet = this._onCaptureTimesheet.asObservable();

  private _onModified = new Subject<TimesheetDto>();
  public onModified = this._onModified.asObservable();

  private _onSetupFromDataSource = new Subject<SetupTimesheetData>();
  public onSetupFromDataSource = this._onSetupFromDataSource.asObservable();

  private _onRepaintScheduler = new Subject<TimesheetRegistry>();
  public onRepaintScheduler = this._onRepaintScheduler.asObservable();

  private _onTimesheetWidgetDateChange = new Subject<Date>();
  public onTimesheetWidgetDateChange = this._onTimesheetWidgetDateChange.asObservable();

  public openNewTimesheet(_comment?: string, _tags?: string[], _dataActivityPlanned?: TimesheetActivityPlanned, _onOpenedWindow?: () => void): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    def.resolve();
    return def.promise;
  }

  public startNewStopwatch(_comment?: string, _tags?: string[], _date?: Date, _dataActivityPlanned?: TimesheetDto) {}

  public hasStopwatchrunningService(_dto: TimesheetDto) {}

  public updateStopwatchRegistryEmptyService(_dto: TimesheetDto): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    def.resolve();
    return def.promise;
  }

  public pushItemTimesheet(_item: TimesheetDto): void {}

  public updateUIStopwatchService(_update: boolean, _dto?: TimesheetDto, _repaintSchedular = true) {}

  public stopStopwatchFromPendingListService(_stop: boolean) {}

  public replaceItem(_item: TimesheetDto) {}

  public refreshDialogDataSourceCatalogsService(): void {}

  public closedDialog(): void {}

  public openEdit(_data: TimesheetDto, _isOpenedFromTooltip = false): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    def.resolve();
    return def.promise;
  }

  public deleteRegistry(_timesheetId: number): Promise<boolean> {
    const def = new Deferred<boolean>();
    def.resolve(true);
    return def.promise;
  }

  public setNewChangePlannerTask(_modifiedValue: any, _activityPlannedId: number): void {}

  public openNew(
    _plannedType: PlannedType,
    _pendingRecordId?: number,
    _comment?: string,
    _tags?: string[],
    _date?: Date,
    _dataActivityPlanned?: TimesheetActivityPlanned,
    _enableStopwatch?: boolean,
    _disablePlannedActivityField?: boolean,
    _onOpenedWindow?: () => void
  ): Promise<TimesheetDto> {
    const def = new Deferred<TimesheetDto>();
    def.resolve();
    return def.promise;
  }

  public modified(_item: TimesheetDto): void {}

  public setupFromDataSource(_dataSource: TimesheetDataSourceDto): void {}

  public repaintScheduler(_registry: TimesheetRegistry): void {}

  public timesheetWidgetDateChange(_date: Date): void {}
}
