import type { HttpContext, HttpHeaders, HttpParams } from '@angular/common/http';
import { type Observable, of } from 'rxjs';

export class MockHttpClient {
  get(
    url: string,
    _options?: {
      headers?:
        | HttpHeaders
        | {
            [header: string]: string | string[];
          };
      context?: HttpContext;
      observe?: 'body';
      params?:
        | HttpParams
        | {
            [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;
          };
      reportProgress?: boolean;
      responseType?: 'json';
      withCredentials?: boolean;
    }
  ): Observable<Object> {
    if (typeof url === 'string' && url.endsWith('/list')) {
      return of([]);
    }
    return of({});
  }

  public post(
    _url: string,
    _body: any | null,
    _options: {
      headers?:
        | HttpHeaders
        | {
            [header: string]: string | string[];
          };
      context?: HttpContext;
      observe?: 'body';
      params?:
        | HttpParams
        | {
            [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;
          };
      reportProgress?: boolean;
      responseType: 'arraybuffer';
      withCredentials?: boolean;
    }
  ): Observable<Object> {
    return of({});
  }
}
