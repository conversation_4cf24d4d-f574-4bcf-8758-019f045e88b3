import * as GridUtil from '@/core/grid/utils/grid-util';
import { Injectable } from '@angular/core';
import { type Observable, type Subject, of } from 'rxjs';
import {
  getDefaultAreaData,
  getDefaultDepartamentoData,
  getDefaultDummyData,
  getDefaultEmpresaData
} from 'src/app/test-utils/core/external-catalog/catalog-hierarchy-mock';
import { HierarchySearchService } from '../../external-catalog/services/hierarchy-search.service';
import type { GridSearchDTO, HierarchySearchOpen, HierarchySearchSelection } from '../../external-catalog/utils/external-catalog.interfaces';
import type { HierarchyValueEntity } from '../../external-catalog/utils/hierarchy.interfaces';
import type { RowSelection } from '../../grid-base-select/grid-base-select.interfaces';
import type { GridInfo } from '../../grid/utils/grid-data';

@Injectable()
export class MockHierarchySearchService extends HierarchySearchService {
  public override gridOpen: Observable<HierarchySearchOpen> = of({
    instanceName: 'test',
    config: {
      selectInstanceName: 'test',
      value: [],
      fieldValue: [],
      label: 'test',
      valueText: 'test',
      searchUrl: 'test',
      columns: [],
      primaryKey: ''
    }
  });
  public override gridSelection: Observable<HierarchySearchSelection> = of({
    instanceName: 'test',
    selection: {
      event: {
        oldSelection: [],
        newSelection: [],
        added: [],
        removed: [],
        event: null,
        cancel: false
      },
      value: []
    }
  });

  public override openSearch(_instanceName: string, _config: GridSearchDTO): void {}

  public override changeGridSelection(_instanceName: string, _selection: RowSelection<HierarchyValueEntity[]>): void {}

  public override refreshHierarchyLevelData(gridConfig: GridSearchDTO, _cancelableReq: Subject<boolean>): Observable<GridInfo<HierarchyValueEntity>> {
    let level: number = null;
    if (typeof gridConfig.searchUrl === 'string' && gridConfig.searchUrl !== '') {
      level = +gridConfig.searchUrl.substring(gridConfig.searchUrl.indexOf('level/') + 6);
    }
    let data: HierarchyValueEntity[];
    if (level === 0) {
      data = getDefaultEmpresaData();
    } else if (level === 1) {
      data = getDefaultDepartamentoData();
    } else if (level === 2) {
      data = getDefaultAreaData();
    } else {
      data = getDefaultDummyData();
    }
    if (level >= 0 && gridConfig.valueText) {
      const column = gridConfig?.columns?.[level];
      if (column) {
        data = data.filter((entity) => entity[column.column] === gridConfig.valueText);
      }
    }
    const gridInfo: GridInfo<HierarchyValueEntity> = {
      gridId: 'test',
      data: data,
      parseFromDynamicResults: false,
      count: 3000,
      status: GridUtil.GRID_INFO_STATUS_SUCCESS
    };
    return of(gridInfo);
  }
}
