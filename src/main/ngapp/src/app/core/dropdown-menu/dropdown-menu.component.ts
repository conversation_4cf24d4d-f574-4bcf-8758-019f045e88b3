import { Component, type ElementRef, Input, type OnChanges, type OnD<PERSON>roy, type OnInit, type SimpleChanges, inject, input, output, viewChild } from '@angular/core';
import {
  AutoPositionStrategy,
  HorizontalAlignment,
  type ISelectionEventArgs,
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxDropDownComponent,
  IgxDropDownItemComponent,
  IgxDropDownItemNavigationDirective,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxRippleDirective,
  type OverlaySettings,
  type PositionSettings,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import type { DisplayDensity } from 'src/app/core/utils/display-density';
import type { BnextComponentPath } from '../i18n/bnext-component-path';
import { NoticeService } from '../services/notice.service';
import { CommonAction } from '../utils/enums';
import {
  DropdownItemCheckType,
  type DropdownMenuActivable,
  type DropdownMenuFileLinked,
  type DropdownMenuFileSelected,
  type DropdownMenuItem
} from './dropdown-menu.interfaces';

import { BnextCloseScrollStrategy } from '../utils/bnext-scroll-strategy';

import { FileUploadModule } from 'ng2-file-upload';
import { DropFileHandlerComponent } from '../drop-file/drop-file.component';
import type { IDropFileHandler, LinkedFileEvt } from '../drop-file/drop-file.interfaces';

import { equalsObject } from '@/core/utils/object';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { HammerModule } from '@angular/platform-browser';
import { BnextCoreComponent } from '../bnext-core.component';

@Component({
  selector: 'app-dropdown-menu',
  templateUrl: './dropdown-menu.component.html',
  styleUrls: ['./dropdown-menu.component.scss'],
  imports: [
    HammerModule,
    NgClass,
    NgTemplateOutlet,
    IgxIconButtonDirective,
    IgxRippleDirective,
    IgxButtonDirective,
    IgxDropDownItemNavigationDirective,
    IgxIconComponent,
    IgxBadgeComponent,
    IgxDropDownComponent,
    IgxDropDownItemComponent,
    FileUploadModule,
    DropFileHandlerComponent
  ]
})
export class DropdownMenuComponent extends BnextCoreComponent implements OnInit, OnDestroy, OnChanges {
  noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core',
    componentName: 'dropdown-menu'
  };
  private _menuOptions: DropdownMenuItem[] = [];
  private _activateBy: DropdownMenuActivable | DropdownMenuActivable[];
  private _menuEvents: DropdownMenuActivable[] = [];
  private _iconName = 'more_vert';
  private _family = 'material';
  private _igxButtonLabel = '';
  private _href = '';
  private _igxButtonBackground = null;
  private _selectedItems: (string | number)[] = null;
  private _dropdownOpen = false;
  private initialized = false;
  private _positionSettings: PositionSettings = {
    horizontalStartPoint: HorizontalAlignment.Left,
    verticalStartPoint: VerticalAlignment.Bottom
  };

  readonly dropdown = viewChild(IgxDropDownComponent);
  readonly toogleButton = viewChild<IgxButtonDirective>('toogleButton');
  readonly dropFile = viewChild<IDropFileHandler>('dropFile');
  validOptions: DropdownMenuItem[] = [];

  readonly divContainer = viewChild<ElementRef>('dropdownContainer');

  readonly simplifiedCount = input(3);
  readonly simplify = input(false);

  /**
   * En caso de ser "true" se incluirá creará un INPUT[type=file] en la
   * opción del menú con valor de "ADD_ATTACHMENT"
   *
   * Para que funcione correctamente es necesario configurar también
   * los eventos "fileLinked", "fileSelected" y "fileReleased"
   */
  readonly addAttachmentAvailable = input(false);
  readonly round = input(false);
  readonly rippleDisabled = input(false);
  readonly igxButtonType = input<'flat' | 'icon' | 'fab' | 'contained'>('icon');
  readonly igxButtonRound = input(false);
  readonly selectable = input(true);
  readonly multipleSelection = input(false);
  readonly refreshOnToggle = input(false);
  readonly closeOnClick = input(true);
  readonly closeOnClickMultiple = input(false);
  readonly noMenuLabel = input<string>(null);

  // TODO: Skipped for migration because:
  //  This input is inherited from a superclass, but the parent cannot be migrated.
  @Input()
  public override displayDensity: DisplayDensity = 'comfortable';

  readonly blackListItems = input<(string | number)[]>([]);
  readonly navMenu = input(false);
  public readonly disabled = input(false);
  public readonly showLabel = input(true);
  public readonly toggleTarget = input<HTMLElement>(null);
  public readonly openedAtClickTarget = input(true);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set igxButtonBackground(_igxButtonBackground: string) {
    this._igxButtonBackground = _igxButtonBackground;
  }
  get igxButtonBackground(): string {
    const validOptions = this.validOptions;
    if (validOptions.length > 0 && validOptions.length <= this.simplifiedCount() && this.simplify()) {
      return validOptions[0].bgColor || this._igxButtonBackground;
    }
    return this._igxButtonBackground;
  }
  readonly igxButtonColor = input<string>(null);
  readonly iconColor = input<string>(null);
  readonly toggleButtonTitle = input<string>(null);
  /**
   * En caso de ser "true" el botón siempre se muestra, en caso de
   * ser "false" desapace cuando no hay opciones disponibles.
   *
   * El último caso puede ocurrir al utilizar el input "availableItemValues".
   */
  readonly alwaysVisible = input(false);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set igxButtonLabel(_igxButtonLabel: string) {
    this._igxButtonLabel = _igxButtonLabel;
  }

  get igxButtonLabel(): string {
    const validOptions = this.validOptions;
    if (validOptions.length > 0 && validOptions.length <= this.simplifiedCount() && this.simplify()) {
      return validOptions[0].text || this._igxButtonLabel;
    }
    return this._igxButtonLabel;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set iconName(_iconName: string) {
    this._iconName = _iconName;
  }

  get iconName(): string {
    const validOptions = this.validOptions;
    if (validOptions.length > 0 && validOptions.length <= this.simplifiedCount() && this.simplify()) {
      return validOptions[0].iconName || this._iconName;
    }
    return this._iconName;
  }

  get family(): string {
    const validOptions = this.validOptions;
    if (validOptions.length > 0 && validOptions.length <= this.simplifiedCount() && this.simplify()) {
      return validOptions[0].family || this._family;
    }
    return this._family;
  }

  get menuOptions(): DropdownMenuItem[] {
    return this._menuOptions;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set menuOptions(value: DropdownMenuItem[]) {
    if (value && this.dropdownId() && this.initialized) {
      value = this.filterMenuOptions(value);
    }
    if (this.debug()) {
      console.log('--> setted - menuOptions: ', value);
    }
    if (value === null) {
      this._menuOptions = [];
    } else {
      this._menuOptions = value;
    }
    this.addActivateByOptions();
  }

  public get activateBy(): DropdownMenuActivable | DropdownMenuActivable[] {
    return this._activateBy;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set activateBy(value: DropdownMenuActivable | DropdownMenuActivable[]) {
    this._activateBy = value;
    this.addActivateByOptions();
  }

  public readonly scrollContainer = input<HTMLElement>(undefined);

  public readonly dropdownId = input<string>(null); // <-- mandatory!

  public readonly availableItemValues = input<(string | number)[]>(null);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set selectedItems(value: (string | number)[]) {
    if (value) {
      this._selectedItems = value;
    } else {
      this._selectedItems = [];
    }
  }

  public readonly iconsStyle = input('material-icons-outlined');

  public get selectedItems(): (string | number)[] {
    return this._selectedItems;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set dropdownOpen(value: boolean) {
    if (value && !this._dropdownOpen && !this.open) {
      const toggleTarget = this.toggleTarget();
      if (toggleTarget) {
        this.toggleMenu({ target: toggleTarget });
      } else {
        this.toggleMenu({ target: this.toogleButton().nativeElement });
      }
    }
    this._dropdownOpen = value;
  }

  public get dropdownOpen(): boolean {
    return this._dropdownOpen;
  }

  public get positionSettings(): PositionSettings {
    return this._positionSettings;
  }

  public get currentSimplifiedCount(): number {
    if (this.validOptions && this.validOptions.length > this.simplifiedCount()) {
      return this.simplifiedCount() - 1;
    }
    return this.simplifiedCount();
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  @Input()
  set href(_href: string) {
    this._href = _href;
  }

  get href(): string {
    const validOptions = this.validOptions;
    if (validOptions?.length > 0 && validOptions.length <= this.simplifiedCount() && this.simplify()) {
      return this.buildItemHref(validOptions[0]) || this._href;
    }
    return this._href;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set enableRightPosition(value: boolean) {
    if (value) {
      this._positionSettings = {
        horizontalDirection: HorizontalAlignment.Right,
        horizontalStartPoint: HorizontalAlignment.Right,
        verticalStartPoint: VerticalAlignment.Top
      };
      this._overlaySettings.positionStrategy = new AutoPositionStrategy(this._positionSettings);
    }
  }

  public readonly checkType = input<DropdownItemCheckType>(DropdownItemCheckType.CHECKBOX);

  public readonly enableBlurOnMouseLeave = input(false);

  public readonly changed = output<DropdownMenuItem>();

  public readonly fileSelected = output<DropdownMenuFileSelected>();

  public readonly fileLinked = output<DropdownMenuFileLinked>();

  public readonly fileFailed = output<boolean>();

  public readonly dropdownOpening = output<boolean>();

  public readonly dropdownClosed = output<boolean>();

  public readonly dropdownOpened = output<boolean>();

  private _overlaySettings: OverlaySettings = {
    closeOnOutsideClick: true,
    modal: false,
    scrollStrategy: new BnextCloseScrollStrategy(),
    positionStrategy: new AutoPositionStrategy(this.positionSettings)
  };

  dropdownAlive = false;
  open = false;
  opening = false;
  closing = false;
  lastSelection: DropdownMenuItem = null;
  EnumCommonAction = CommonAction;
  checkedType = DropdownItemCheckType;

  ngOnInit(): void {
    this.initialized = true;
    if (!this.dropdownId()) {
      const options = this.menuOptions.map((m) => m.value);
      if (options && options.length > 0) {
        throw new Error(`Missing "dropdownId" of DropdownMenuComponent with following options [${options.reduce((a, v) => `${a}, ${v}`)}].`);
      }
    } else {
      this.menuOptions = this.filterMenuOptions(this.menuOptions);
      for (const temp of this.menuOptions) {
        if (temp.text?.startsWith('root.')) {
          temp.text = this.translateService.instant(temp.text);
        }
      }
      this.validOptions = this.filterValidOptions();
    }
    this.addActivateByOptions();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.hasChanges(changes)) {
      this.fixHiddenAndSelectedItems();
      this.validOptions = this.filterValidOptions();
    }
  }

  buildItemHref(value: DropdownMenuItem): string {
    if (typeof value.modernHref === 'string' && value.modernHref !== '') {
      return this.menuService.buildModernUrl(value.modernHref, this.translateService.currentLang);
    }
    if (typeof value.modernHrefRest === 'string' && value.modernHrefRest !== '') {
      return this.menuService.buildModernUrlRest(value.modernHrefRest);
    }
    if (typeof value.legacyHref === 'string' && value.legacyHref !== '') {
      return this.menuService.buildLegacyUrl(value.legacyHref, this.translateService.currentLang);
    }
    return null;
  }

  private hasChanges(changes: SimpleChanges): boolean {
    if (changes.availableItemValues && !equalsObject(changes.availableItemValues.currentValue, changes.availableItemValues.previousValue)) {
      return true;
    }
    if (changes.menuOptions && !equalsObject(changes.menuOptions.currentValue, changes.menuOptions.previousValue)) {
      return true;
    }
    if (changes.selectedItems && !equalsObject(changes.selectedItems.currentValue, changes.selectedItems.previousValue)) {
      return true;
    }
    if (changes.blackListItems && !equalsObject(changes.blackListItems.currentValue, changes.blackListItems.previousValue)) {
      return true;
    }
    return false;
  }

  filterMenuOptions(value: DropdownMenuItem[]): DropdownMenuItem[] {
    return value
      .filter((m) => !!m)
      .map((item) => {
        item.dropdownId = this.dropdownId();
        return item;
      });
  }

  public pushMenuOption(item: DropdownMenuItem): void {
    if (this._menuOptions.find((option) => option.value === item.value)) {
      return;
    }
    item.dropdownId = this.dropdownId();
    this._menuOptions.push(Object.assign({}, item));
  }

  addActivateByOptions(): void {
    if (!this.activateBy || !this.menuOptions || !this.initialized) {
      return;
    }
    if ((this.activateBy as DropdownMenuActivable[]).length) {
      for (const d of this.activateBy as DropdownMenuActivable[]) {
        this.pushMenuActivable(d);
      }
    } else {
      this.pushMenuActivable(this.activateBy as DropdownMenuActivable);
    }
  }

  ngOnDestroy(): void {
    this.cdr.detach();
  }

  private pushMenuActivable(activable: DropdownMenuActivable) {
    if (!this.menuOptions || !activable || activable.hidden || this.menuOptions.find((m) => m && m.value === activable.droppmenu.value)) {
      return;
    }
    const temp: DropdownMenuItem = Object.assign({}, activable.droppmenu);
    temp.dropdownId = this.dropdownId() || null;
    if (temp.text?.startsWith('root.')) {
      temp.text = this.translateService.instant(temp.text);
    }
    this._menuEvents.push(activable);
    this.menuOptions.push(temp);
    const availableItemValues = this.availableItemValues();
    if (availableItemValues && !availableItemValues.find((value) => value === temp.value)) {
      availableItemValues.push(temp.value);
    }
  }

  onSelection(eventArgs: ISelectionEventArgs) {
    if (!this.closeOnClick() || this.multipleSelection()) {
      eventArgs.cancel = true;
    }
  }

  onClickItemSelection(event) {
    if (event?.ctrlKey) {
      return;
    }
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  onTapItemSelection(event, item: DropdownMenuItem, delay = false) {
    if (event?.ctrlKey) {
      return;
    }
    this.onItemSelection(item, delay);
  }

  onItemSelection(item: DropdownMenuItem, delay = false) {
    const currentSelected = item.selected;
    const multipleSelection = this.multipleSelection();
    if (!multipleSelection) {
      for (const element of this.menuOptions) {
        element.selected = false;
      }
    }
    if (((this.closeOnClick() && !multipleSelection) || this.closeOnClickMultiple()) && item.value !== 'ADD_ATTACHMENT') {
      this.closeDropdown(undefined, delay);
    }
    item.selected = !currentSelected;
    if (this.selectedItems) {
      const idxSelectItem = this.selectedItems.indexOf(item.value);
      if (item.selected && idxSelectItem === -1) {
        this.selectedItems.push(item.value);
      } else if (!item.selected && idxSelectItem !== -1) {
        this.selectedItems.splice(idxSelectItem, 1);
      }
    }
    const evt = this._menuEvents.find((m: DropdownMenuActivable) => {
      return m.droppmenu.value === item.value;
    });
    if (evt) {
      evt.droppmenuAction.next(item);
    }
    this.changed.emit(item);
    this.lastSelection = item;
  }

  public toggleMenu(eventArgs: { target: any; ctrlKey?: any; pointerType?: any; preventDefault?: any; stopPropagation?: any }) {
    if (this.opening || this.closing) {
      return;
    }
    if (this.open || this.dropdownOpen) {
      this.closeDropdown();
      return;
    }
    if (eventArgs?.ctrlKey || eventArgs.pointerType === '') {
      return;
    }
    if (eventArgs && this.href) {
      eventArgs.preventDefault();
      eventArgs.stopPropagation();
    }
    const noMenuLabel = this.noMenuLabel();
    if (noMenuLabel !== null && (this.menuOptions === null || this.menuOptions.length === 0)) {
      this.noticeService.notice(noMenuLabel);
      return;
    }
    const validOptions = this.validOptions;
    if (validOptions.length > 0 && validOptions.length <= this.simplifiedCount() && this.simplify()) {
      return this.onItemSelection(validOptions[0]);
    }
    this.opening = true;
    if (this.fixHiddenAndSelectedItems() || this.refreshOnToggle()) {
      this.cdr.detectChanges();
    }
    if (this.openedAtClickTarget()) {
      this._overlaySettings.target = eventArgs.target as HTMLElement;
    } else {
      this._overlaySettings.target = this.elem.nativeElement;
    }
    this._overlaySettings.scrollStrategy = new BnextCloseScrollStrategy(this.scrollContainer());
    this.dropdownAlive = true;
    this.cdr.detectChanges();
    this.dropdown().toggle(this._overlaySettings);
  }

  private filterValidOptions(): DropdownMenuItem[] {
    let options = this.menuOptions.filter((item) => typeof item !== 'undefined' && !item.hidden && !item.header);
    if (this.blackListItems().length) {
      options = options.filter((_item: DropdownMenuItem) => {
        return !this.blackListItems().find((value) => value === _item.value);
      });
    }
    if (this.debug()) {
      console.log('--> validOptions', options);
    }
    return options;
  }

  fixHiddenAndSelectedItems(): boolean {
    if (!this.menuOptions || this.menuOptions.length === 0) {
      return false;
    }
    const availableItemValues = this.availableItemValues();
    const hasAvailableItemValues = availableItemValues !== null && typeof availableItemValues !== 'undefined';
    const hasSelectedItems = this.selectedItems !== null && typeof this.selectedItems !== 'undefined';
    if (!hasAvailableItemValues && !hasSelectedItems) {
      return false;
    }
    for (const item of this.menuOptions) {
      if (hasAvailableItemValues && typeof item !== 'undefined') {
        const availableItemValuesValue = this.availableItemValues();
        item.hidden = availableItemValuesValue && !availableItemValuesValue.find((value) => value === item.value);
      }
      if (hasSelectedItems && typeof item !== 'undefined') {
        const isSelected = this.selectedItems.indexOf(item.value) !== -1;
        if ((isSelected && !item.selected) || (!isSelected && item.selected)) {
          this.onItemSelection(item);
        }
      }
    }
    return true;
  }

  public closeDropdown(event?: any, delay = false) {
    if (this.open) {
      this.closing = true;
      if (delay) {
        setTimeout(() => {
          this.closeDropdown();
        }, 50);
      } else {
        const dropdown = this.dropdown();
        if (dropdown) {
          dropdown?.close();
          this.dropdownAlive = false;
          this.closing = false;
          this.cdr.detectChanges();
        }
      }
      if (event) {
        this.changed.emit(event);
      }
    }
  }

  public openDropdown(event, touchOnly?: boolean, targetElement?: HTMLElement) {
    if (this.opening || this.closing) {
      return;
    }
    if (this.open || this.dropdownOpen) {
      this.closeDropdown();
      return;
    }
    if (event.target?.tagName === 'TEXTAREA' || event.target?.tagName === 'BUTTON') {
      // El dropdown no se abre si se dio clic en un TEXTAREA o BUTTON
      return;
    }
    if (touchOnly && !this.isTouchDevice) {
      // Cuando la bandera de "solo touch" esté encendida y no se detecten eventos "touch", no hace nada.
      return;
    }
    this.opening = true;
    this._overlaySettings.target = targetElement || (event.target as HTMLElement);
    this._overlaySettings.scrollStrategy = new BnextCloseScrollStrategy(this.scrollContainer());
    this.dropdownAlive = true;
    this.cdr.detectChanges();
    this.dropdown().open(this._overlaySettings);
  }

  onOpening() {
    this.dropdownOpening.emit(true);
  }

  onOpened() {
    this.open = true;
    this.opening = false;
    this.closing = false;
    this.dropdownOpen = true;
    this.dropdownOpened.emit(true);
    this.cdr.detectChanges();
  }

  onClosing(event: any) {
    if (!this.open) {
      event.cancel = true;
      return;
    }
    this.closing = true;
  }

  onClosed(dropdown: IgxDropDownComponent) {
    if (this.lastSelection?.value === 'ADD_ATTACHMENT') {
      if (dropdown?.children?.length) {
        for (const item of dropdown.children) {
          item.selected = false;
        }
      }
    }
    this.open = false;
    this.opening = false;
    this.closing = false;
    this.dropdownOpen = false;
    this.dropdownClosed.emit(true);
    this.cdr.detectChanges();
  }

  onBlur(event: any) {
    if (!this.enableBlurOnMouseLeave()) {
      return;
    }
    const dropdown = this.dropdown();
    if (event && dropdown && dropdown.selectedItem === null) {
      setTimeout(() => {
        this.closeDropdown();
      }, 50);
    }
  }

  onFileSelected(fileList: File[] | FileList, item: DropdownMenuItem, dropHandler: IDropFileHandler, attacher = null): void {
    this.fileSelected.emit({
      dropHandler: dropHandler,
      fileList: fileList,
      attacher: attacher,
      text: item.text,
      value: item.value
    });
  }

  onFileLinked(fileLink: LinkedFileEvt): void {
    const item = this.lastSelection;
    this.fileLinked.emit({
      text: item?.text,
      value: item?.value,
      fileLink: fileLink
    });
  }

  onFileFailed(released: boolean): void {
    this.fileFailed.emit(released);
  }
}
