<label innerText="{{ 'targets' | translate: this }}"></label>
<br />
<div #mainContainer class="main-container fancy-scroll">
  @for (item of areaContent(); track item; let rowIndex = $index) {
    <div>
      @if (item.deleted === 0) {
        <div class="rows-wrapper">
          <div class="idx">{{ rowIndex + 1 }}</div>
          <input #descRow class="desc" type="text" (keyup)="onDescKeyUp(item.id, descRow, rowIndex)" [value]="item.targetName" />
          <button [igxIconButton]="'flat'" (click)="onRemoveItem(item.id, rowIndex)">
            <igx-icon>delete</igx-icon>
          </button>
        </div>
      }
    </div>
  }
  <div class="rows-wrapper">
    <div class="idx">~</div>
    <div #textZoneAdd contenteditable="true" class="text-zone-add" (keypress)="onTextZoneAdd($event, textZoneAdd)"></div>
    <button [igxIconButton]="'flat'" (click)="onAddItem(textZoneAdd)">
      <igx-icon>send</igx-icon>
    </button>
  </div>
</div>
