@use 'src/styles/immutable-colors' as *;

.row-counter {
  width: 2vw;
  display: flex;
  flex-direction: column;
  overflow: scroll;
  height: inherit;
  background-color: rgba($fg-color, 0.25);
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome, Edge, Opera and Firefox */
}

.row-counter::-webkit-scrollbar {
  display: none;
}

.textarea-lined {
  width: 48vw;
  height: 290;
  border: none;
  overflow: scroll;
  font-size: 1.063rem;
  outline: none;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  margin-left: 1rem;
  line-height: 1.749rem;
}

.main-container {
  border: 0.063rem solid $fg-color;
  height: 22.5rem;
  overflow-y: scroll;
}

.number-row {
  height: 1.2rem;
  width: 2.5rem;
  font-size: 1.063rem;
  margin-bottom: 0.063rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media print, screen and (max-width: 26.75rem) {
  .main-container,
  .textarea-lined {
    width: 100%;
  }
}

.text-zone-add {
  width: 90%;
  height: 2rem;
  outline: none;
  padding-left: 0.5rem;
}

.rows-wrapper {
  width: 100%;
  height: 2rem;
  display: flex;
  flex-direction: row;
}

.idx {
  width: 10%;
  height: inherit;
  background-color: rgba($fg-color, 0.25);
  display: inherit;
  justify-content: center;
  user-select: none;
}

.desc {
  width: 90%;
  height: inherit;
  padding-left: 0.5rem;
  outline: none;
  border: none;
}
