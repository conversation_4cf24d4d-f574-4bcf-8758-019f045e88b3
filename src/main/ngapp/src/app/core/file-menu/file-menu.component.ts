import { Component, type ElementRef, Input, type OnChanges, type OnInit, type SimpleChanges, forwardRef, input, output, viewChild } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { FileUploader } from 'ng2-file-upload';
import { BnextCoreComponent } from '../bnext-core.component';
import type { DropdownMenuItem } from '../dropdown-menu/dropdown-menu.interfaces';
import type { FileEntity } from '../grid/utils/grid.interfaces';
import type { BnextComponentPath } from '../i18n/bnext-component-path';
import { ImagePreviewerComponent } from '../image-previewer/image-previewer.component';
import { MultiFileUtils } from '../multi-file-upload/multi-file-utils';
import { RestApiModule } from '../rest-api.module';
import { FileMenuActions } from './file-menu.enums';

import { DropdownMenuComponent } from '../dropdown-menu/dropdown-menu.component';

@Component({
  selector: 'app-file-menu',
  styleUrls: ['./file-menu.component.scss'],
  templateUrl: './file-menu.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FileMenuComponent),
      multi: true
    }
  ],
  imports: [DropdownMenuComponent, ImagePreviewerComponent]
})
export class FileMenuComponent extends BnextCoreComponent implements OnInit, OnChanges {
  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core',
    componentName: 'file-menu'
  };

  uploader: FileUploader;

  options: DropdownMenuItem[] = [
    {
      text: 'Abrir',
      value: FileMenuActions.OPEN,
      iconName: 'open_in_new'
    },
    {
      text: 'Descargar',
      value: FileMenuActions.DOWNLOAD,
      iconName: 'download'
    },
    {
      text: 'Borrar',
      value: FileMenuActions.DELETE,
      iconName: 'delete'
    },
    {
      text: 'Agregar descripción',
      value: FileMenuActions.ADD_COMMENT,
      iconName: 'insert_comment'
    }
  ];

  public readonly baseController = input('files');
  public readonly simplify = input(false);
  public readonly simplifiedCount = input(3);

  optionsAvailable: string[] = [FileMenuActions.DOWNLOAD, FileMenuActions.DELETE];

  readonly downloadAnchor = viewChild<ElementRef>('downloadAnchor');

  readonly previewerImages = viewChild<ImagePreviewerComponent>('previewerImages');

  previewImgOpen = false;

  image: string = undefined;

  _images: FileEntity[];

  public imageToShow: number;

  public restApiController = `${RestApiModule.url() + this.baseController()}/`;

  public readonly isImage = input(true);

  public readonly fileId = input<number>(undefined);

  public readonly allowsAddComment = input(false);

  public readonly allowOpenFile = input(true);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set images(images: FileEntity[]) {
    this._images = images;
  }

  get images() {
    return this._images || [];
  }

  public readonly removed = output<DropdownMenuItem>();

  public readonly downloaded = output<DropdownMenuItem>();

  public readonly opened = output<DropdownMenuItem>();

  public readonly addedComment = output<DropdownMenuItem>();

  override ngOnInit(): void {
    super.ngOnInit();
    this.uploader = new FileUploader({
      url: `${RestApiModule.url()}files`,
      autoUpload: true
    });
    for (const item of this.options) {
      switch (item.value) {
        case FileMenuActions.DOWNLOAD:
          item.text = this.translate.instantFrom(MultiFileUtils.LANG_CONFIG, 'multiFileUploadDownload');
          break;
        case FileMenuActions.DELETE:
          item.text = this.translate.instantFrom(MultiFileUtils.LANG_CONFIG, 'multiFileUploadRemove');
          break;
        case FileMenuActions.OPEN:
          item.text = this.translate.instantFrom(MultiFileUtils.LANG_CONFIG, 'multiFileUploadPreview');
          break;
        case FileMenuActions.ADD_COMMENT:
          item.text = this.translate.instantFrom(MultiFileUtils.LANG_CONFIG, 'multiFileUploadAddComment');
          item.hidden = !this.allowsAddComment();
          break;
      }
    }
    if (this.allowsAddComment()) {
      this.optionsAvailable.push(FileMenuActions.ADD_COMMENT);
    }
    if (this.allowOpenFile()) {
      this.optionsAvailable.unshift(FileMenuActions.OPEN);
    }
    this.cdr.detectChanges();
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.baseController) {
      if (this.uploader) {
        this.uploader.options.url = RestApiModule.url() + this.baseController();
      }
    }
  }

  changedOptions(item: DropdownMenuItem) {
    switch (item.value) {
      case FileMenuActions.OPEN:
        this.open(item);
        break;
      case FileMenuActions.DOWNLOAD:
        this.download(item);
        break;
      case FileMenuActions.DELETE:
        this.remove(item);
        break;
      case FileMenuActions.ADD_COMMENT:
        this.addComment(item);
        break;
    }
  }

  remove(item: DropdownMenuItem) {
    this.removed.emit(item);
  }

  download(item: DropdownMenuItem) {
    const downloadAnchor = this.downloadAnchor().nativeElement;
    const fileId = this.fileId();
    if (typeof fileId !== 'undefined' && fileId !== null) {
      downloadAnchor.href = `${RestApiModule.url() + this.baseController()}/${fileId}`;
      downloadAnchor.click();
      downloadAnchor.href = 'javascript: void(0);';
      this.downloaded.emit(item);
    } else {
      console.error(`Error al descargar la imagen: ${fileId}`);
    }
  }

  open(item: DropdownMenuItem) {
    if (this.isImage()) {
      this.image = `${RestApiModule.url() + this.baseController()}/${this.fileId()}`;
      const imageToShow = this.images.findIndex((f) => f.id === this.fileId());
      const previewerImages = this.previewerImages();
      previewerImages.imageToShow = imageToShow;
      previewerImages.openDialog();
      this.opened.emit(item);
      this.cdr.detectChanges();
    } else {
      this.menuService.navigateBlankLegacy(`v-document-viewer.view?fileId=${this.fileId()}&simpleview=true`);
    }
  }

  addComment(item: DropdownMenuItem) {
    this.addedComment.emit(item);
  }
}
