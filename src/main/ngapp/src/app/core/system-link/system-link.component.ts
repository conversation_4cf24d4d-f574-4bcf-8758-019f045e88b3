import { Component, Input, type OnChanges, type On<PERSON><PERSON>roy, type OnInit, type SimpleChang<PERSON>, forwardRef, inject, input, output, viewChildren } from '@angular/core';
import {
  type ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormControl,
  type UntypedFormGroup
} from '@angular/forms';
import { BnextCoreComponent } from 'src/app/core/bnext-core.component';
import type { BnextComponentPath } from 'src/app/core/i18n/bnext-component-path';

import {
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxPrefixDirective,
  IgxRippleDirective
} from '@infragistics/igniteui-angular';
import type { DataMap } from 'src/app/core/utils/data-map';
import { EnumDisplayDensity } from 'src/app/core/utils/display-density';
import type { SystemLinkData } from './system-link.interfaces';

import { BnextTranslateService } from '@/core/i18n/bnext-translate.service';
import { cloneObject, equalsObject } from '@/core/utils/object';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-system-link',
  styleUrls: ['./system-link.component.scss'],
  templateUrl: './system-link.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SystemLinkComponent),
      multi: true
    }
  ],
  imports: [
    FormsModule,
    IgxIconButtonDirective,
    ReactiveFormsModule,
    NgClass,
    IgxInputGroupComponent,
    IgxLabelDirective,
    IgxPrefixDirective,
    IgxInputDirective,
    IgxRippleDirective,
    IgxIconComponent
  ]
})
export class SystemLinkComponent extends BnextCoreComponent implements ControlValueAccessor, OnInit, OnDestroy, OnChanges {
  fb = inject(UntypedFormBuilder);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core',
    componentName: 'system-link'
  };

  _labelNoData: string;
  _value: SystemLinkData[];
  form: UntypedFormGroup;
  public titleCopy = null;
  readonly EnumDisplayDensity = EnumDisplayDensity;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public disabled = false;

  public readonly autoFocus = input(false);
  public readonly name = input<string>(undefined);
  public readonly fieldCss = input<DataMap<any>>(null);
  public readonly mediumNgClass = input(6);
  public readonly paddingNgClassX = input(true);
  public readonly showAsLabelValue = input(false);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set value(value: SystemLinkData[]) {
    if (!equalsObject(value, this._value)) {
      if (this._value) {
        if (value?.length > this._value?.length) {
          this.deleteControls(this._value);
        }
      }
      this._value = cloneObject(value);
      this.addControls(value);
      this.writeNewValue(false);
    }
  }

  get value(): SystemLinkData[] {
    return this._value;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  get valid(): boolean {
    return this.form?.valid;
  }

  public readonly required = input(true);
  public readonly compactSizeMode = input(false);
  public readonly markRequiredFields = input(false);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set labelNoData(labelNoData: string) {
    this._labelNoData = labelNoData;
  }

  get labelNoData() {
    if (!this._labelNoData) {
      return this.translate.instantFrom(SystemLinkComponent.LANG_CONFIG, 'no-available-data');
    }
    return this._labelNoData;
  }

  public readonly noWrap = input(true);

  public readonly valueChange = output<SystemLinkData[]>();
  public readonly copied = output<string>();

  readonly fields = viewChildren(IgxInputDirective);

  onChange: any = () => {};
  onTouched: any = () => {};

  override ngOnInit() {
    super.ngOnInit();
    this.setDisabledState(this.disabled);
    if (!this.titleCopy) {
      this.titleCopy = this.translate.instantFrom(BnextTranslateService.BUTTON_COMMON, 'copy');
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.disabled) {
      this.setDisabledState(this.disabled);
    }
    if (changes.value && changes.value.previousValue !== changes.value.currentValue) {
      this.writeNewValue();
    }
  }

  get focusableNative(): HTMLElement {
    return this.elem?.nativeElement;
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.cdr.detach();
    this.onChange = null;
    this.onTouched = null;
  }

  valueChanged() {
    if (!this.valueChange) {
      return;
    }
    const currentValue = cloneObject(this.value);
    this.valueChange.emit(currentValue);
    this.onChange(currentValue);
    this.onTouched(currentValue);
  }

  controlId(index: number): string {
    return `system-link-${index}`;
  }

  writeValue(newValue: SystemLinkData[]): void {
    if (!newValue) {
      newValue = [];
    }
    if (!equalsObject(newValue, this.value)) {
      this.value = newValue;
      this.valueChanged();
    }
    this.writeNewValue();
  }

  private writeNewValue(emitNewValue = false) {
    if (!this.form) {
      return;
    }
    const currentValue = this.value;
    if (currentValue) {
      this.cdr.detectChanges();
      const data = {};
      for (let index = 0; index < currentValue.length; index++) {
        const item = currentValue[index];
        const fieldId = this.controlId(index);
        data[fieldId] = item.systemLinkValue;
      }
      this.form.patchValue(data, { onlySelf: true, emitEvent: false });
    } else {
      this.cdr.detectChanges();
      this.form.reset();
    }
    if (emitNewValue) {
      this.valueChanged();
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    if (isDisabled) {
      if (this.form) {
        this.deleteControls(this.value);
        this.form = null;
      }
    } else {
      this.form = this.fb.group({});
      this.addControls(this.value);
      this.writeNewValue();
    }
    this.disabled = isDisabled;
  }

  onChangeItem(item: SystemLinkData, systemInput: HTMLInputElement) {
    const newValue = item?.systemLinkValue !== systemInput.value;
    if (!newValue) {
      return;
    }
    item.systemLinkValue = systemInput.value;
    this.writeNewValue(true);
  }

  addControls(value: SystemLinkData[]) {
    if (!value || value.length === 0 || !this.form || this.disabled) {
      return;
    }
    for (let index = 0; index < value.length; index++) {
      const item = value[index];
      const controlName = this.controlId(index);
      if (!this.form.contains(controlName)) {
        this.form.addControl(this.controlId(index), new UntypedFormControl(item.systemLinkValue));
      }
    }
  }

  deleteControls(value: SystemLinkData[]) {
    if (!value || value.length === 0 || !this.form) {
      return;
    }
    for (const _item of value) {
      const index = value.indexOf(_item);
      const controlName = this.controlId(index);
      if (this.form.contains(controlName)) {
        this.form.removeControl(controlName);
      }
    }
  }

  invalid(fieldName: string): boolean {
    if (!this.required()) {
      return null;
    }
    const field = this.form.get(fieldName);
    if (field) {
      const isInvalid = !field.valid && (field.touched || this.markRequiredFields());
      return isInvalid;
    }
    console.error('Unable to validate system link: ', fieldName);
    return false;
  }

  focus(): boolean {
    return this.fields().some((field) => {
      if (field.value === null || typeof field.value === 'undefined') {
        field.focus();
        return true;
      }
      return false;
    });
  }

  parseUrl(item: SystemLinkData): string {
    if (
      !item ||
      item.systemLinkUrl === null ||
      typeof item.systemLinkUrl === 'undefined' ||
      item.systemLinkUrl === '' ||
      item.systemLinkValue === null ||
      typeof item.systemLinkValue === 'undefined' ||
      item.systemLinkValue === ''
    ) {
      return null;
    }
    const url = item.systemLinkUrl;
    if (url.indexOf('${value}') !== -1) {
      return url.replace('${value}', item.systemLinkValue);
    }
    return url + item.systemLinkValue;
  }

  public textCopied(item: SystemLinkData) {
    const url = this.parseUrl(item);
    this.copied.emit(url);
  }
}
