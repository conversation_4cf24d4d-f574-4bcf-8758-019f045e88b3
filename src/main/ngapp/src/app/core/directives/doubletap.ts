import { Directive, ElementRef, type OnChanges, type SimpleChanges, inject, input, output } from '@angular/core';
import { HammerConfigService } from '../services/hammer-config.service';

@Directive({
  /* eslint:disable-next-line */
  selector: '[doubleTap]',
  standalone: true
})
export class DoubleTapDirective implements OnChanges {
  protected element = inject(ElementRef);

  public readonly dblTapAction = output<any>();

  public readonly doubleTap = input(true);

  // biome-ignore lint/correctness/noUndeclaredVariables: Global use of HammerJS
  private hammerManager: HammerManager;

  public constructor() {
    const element = this.element;

    this.configureDoubleTap(element.nativeElement);
  }

  public ngOnChanges(changes: SimpleChanges): void {
    if (changes.doubleTap) {
      if (this.doubleTap()) {
        if (!this.hammerManager) {
          this.configureDoubleTap(this.element.nativeElement);
        }
      } else {
        if (this.hammerManager) {
          this.hammerManager.destroy();
          this.hammerManager = null;
        }
      }
    }
  }

  private configureDoubleTap(element: HTMLElement): void {
    if (!HammerConfigService.isHammerAvailable || !this.doubleTap() || this.hammerManager) {
      return;
    }
    // biome-ignore lint/correctness/noUndeclaredVariables: Global use of HammerJS
    this.hammerManager = new Hammer.Manager(element);
    // biome-ignore lint/correctness/noUndeclaredVariables: Global use of HammerJS
    const singleTap = new Hammer.Tap({ event: 'singletap' });
    // biome-ignore lint/correctness/noUndeclaredVariables: Global use of HammerJS
    const doubleTap = new Hammer.Tap({ event: 'doubletap', taps: 2, interval: 750 });
    this.hammerManager.add([singleTap, doubleTap]);
    doubleTap.recognizeWith(singleTap);
    this.hammerManager.on('doubletap', (e) => this.dblTapAction.emit(e));
  }
}
