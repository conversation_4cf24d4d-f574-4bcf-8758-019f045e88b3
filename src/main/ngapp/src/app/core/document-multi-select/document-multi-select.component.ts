import { BnextCoreComponent } from '@/core/bnext-core.component';
import { GridMultiSelectComponent } from '@/core/grid-multi-select/grid-multi-select.component';
import * as GridUtil from '@/core/grid/utils/grid-util';
import { cloneObject, isObject } from '@/core/utils/object';
import { MenuService } from '@/modules/menu/services/menu.service';
import { Component, HostBinding, Input, type OnDestroy, type OnInit, forwardRef, inject, input, output, viewChild } from '@angular/core';
import { type ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import type { IgxDialogComponent } from '@infragistics/igniteui-angular';
import { Subject, type Subscription } from 'rxjs';
import { Session } from 'src/app/core/local-storage/session';
import type { DisplayDensity } from 'src/app/core/utils/display-density';
import { Module } from 'src/app/modules/menu/menu-definition/menu-definition.enum';
import type { IDocumentEntity } from 'src/app/shared/activities/activities-document.interfaces';
import type { DropdownMenuActivable, DropdownMenuItem } from '../dropdown-menu/dropdown-menu.interfaces';
import type { RowSelection } from '../grid-base-select/grid-base-select.interfaces';
import type { GridComponent } from '../grid/grid.component';
import { GridDataType } from '../grid/utils/grid-data-type';
import type { BnextComponentPath } from '../i18n/bnext-component-path';
import type { DataMap } from '../utils/data-map';
import { CommonAction } from '../utils/enums';
import type { GridColumn } from './../grid/utils/grid-column';

export interface DocumentEntity {
  id?: number;
  description: string;
  lastModifiedDate: Date;
  lastModifiedBy: string;
  stage: string;
  activityCommitmentTask?: number;
  activityId?: number;
  createdBy?: number;
}

export interface SaveDocumentEvt {
  entity: DocumentEntity;
  grid: GridComponent;
  dropdownItem?: DropdownMenuItem;
}

@Component({
  selector: 'app-document-multi-select',
  styleUrls: ['./document-multi-select.component.scss'],
  templateUrl: './document-multi-select.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DocumentMultiSelectComponent),
      multi: true
    }
  ],
  imports: [GridMultiSelectComponent]
})
export class DocumentMultiSelectComponent extends BnextCoreComponent implements ControlValueAccessor, OnInit, DropdownMenuActivable, OnDestroy {
  private menu = inject(MenuService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core',
    componentName: 'document-multi-select'
  };
  private _clickedDropdownItem: DropdownMenuItem = null;
  private _stageLabelConfig: BnextComponentPath = null;

  stageLabelValues: DataMap<DataMap<string>> = { stage: {} };
  commitmentTaskLabelValues: DataMap<DataMap<string>> = { activityCommitmentTask: {} };
  valueColumns: GridColumn[];
  dataColumns: GridColumn[];

  dataEmptyGridMessage = null;
  dialogTitle = 'Agregar documento';
  public bannerEmptyMessage = '';

  readonly parentComponentPath = input<BnextComponentPath>(DocumentMultiSelectComponent.LANG_CONFIG);
  public readonly containerDialog = input<IgxDialogComponent>(undefined);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public label = '';

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public placeholder = '';
  public readonly name = input('');
  public readonly type = input('multiple');
  public readonly help = input('');

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public dataUrl = '';

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public disabled = false;

  // TODO: Skipped for migration because:
  //  This input overrides a field from a superclass, while the superclass field
  //  is not migrated.
  @Input()
  public hidden = false;
  public readonly hiddenInput = input(false);
  public readonly showHelp = input(false);

  // TODO: Skipped for migration because:
  //  This input is inherited from a superclass, but the parent cannot be migrated.
  @Input()
  public override displayDensity: DisplayDensity = 'comfortable';

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public deleteServices: string[] = [];

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set stageLabelConfig(_stageLabelConfig: BnextComponentPath) {
    this._stageLabelConfig = _stageLabelConfig;
    this.setTranslatedStageLabels();
  }
  public readonly previewNavigateModule = input<Module>(Module.DOCUMENT);
  public get stageLabelConfig() {
    return this._stageLabelConfig;
  }

  get id() {
    return `${this.name()}document-multi-select`;
  }

  get value() {
    return this.gridSelect().value;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set value(value: DataMap[]) {
    const gridSelect = this.gridSelect();
    gridSelect.value = value;
    const dataGrid = gridSelect.dataGrid();
    if (dataGrid) {
      const rowIDs = value.map((item) => item[this.gridSelect().primaryKey()]);
      dataGrid.selectedRows = rowIDs;
    } else {
      if (value) {
        gridSelect.data = [...value];
      } else {
        gridSelect.data = null;
      }
    }
    this.cdr.detectChanges();
  }

  @HostBinding('class.document-multi-select-container')
  public defaultClass = true;

  @HostBinding('class.document-multi-select-container--filled')
  get isFilled() {
    return this.gridSelect().isFilled;
  }

  @HostBinding('class.document-multi-select-container--empty')
  get isEmpty() {
    return this.gridSelect().isEmpty;
  }

  // TODO: Skipped for migration because:
  //  This input is used in combination with `@HostBinding` and migrating would
  //  break.
  @Input()
  @HostBinding('class.document-multi-select-container--required')
  public required = false;

  @HostBinding('class.document-multi-select-container--focused')
  public isFocused = false;

  @HostBinding('class.document-multi-select-container--search')
  public isSearch = false;

  readonly gridSelect = viewChild('gridSelect', { read: GridMultiSelectComponent });

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public hasDelete: () => boolean;

  // eslint-disable-next-line @angular-eslint/no-output-native
  public readonly change = output<DataMap[]>();
  public readonly saveSingle = output<SaveDocumentEvt>();
  public readonly removeSingle = output<IDocumentEntity>();
  public readonly rowSelectionChange = output<RowSelection>();
  public readonly data = input<DataMap[]>(undefined);
  readonly allowsAuthorDelete = input(false);
  public readonly enableBannerInfoEmpty = input(false);
  public droppmenu = {
    text: 'root.common.button.add-document',
    value: CommonAction.ADD_DOCUMENT,
    iconName: 'add'
  };
  public droppmenuAction = new Subject<DropdownMenuItem>();
  public subs: Subscription[] = [];

  onChange: any = () => {};
  onTouched: any = () => {};

  constructor() {
    super();
    if (!this.hasDelete) {
      this.hasDelete = () => {
        const userServices = Session.getServices();
        return this.deleteServices.length === 0 || userServices.some((item) => this.deleteServices.includes(item));
      };
    }
    const s = this.droppmenuAction.subscribe((item: DropdownMenuItem) => {
      this._clickedDropdownItem = item;
      this.gridSelect().openDialog();
    });
    if (s) {
      this.subs.push(s);
    }
    const columns: GridColumn[] = [
      {
        header: 'pageview',
        field: 'pageview',
        width: '70px',
        render: {
          action: { icon: 'preview' },
          callback: (cell, event) => this.viewRow(cell.row.data, event),
          isRendered: () => true
        },
        sortable: false,
        filterable: false,
        resizable: true,
        disableHiding: true,
        type: GridDataType.ACTION
      },
      { field: 'code', header: 'code', width: '150px', type: GridDataType.TEXT, searchable: true, resizable: true },
      { field: 'description', header: 'description', type: GridDataType.TEXT, searchable: true, resizable: true },
      { field: 'version', header: 'version', width: '80px', type: GridDataType.TEXT, searchable: true },
      {
        field: 'lastModifiedDate',
        header: 'lastModifiedDate',
        width: '155px',
        resizable: true,
        type: GridDataType.TIMESTAMP,
        searchable: true
      },
      { field: 'lastModifiedBy', header: 'lastModifiedBy', width: '200px', type: GridDataType.TEXT, searchable: true, resizable: true }
    ];
    this.valueColumns = cloneObject(columns).concat(
      {
        header: 'stage',
        field: 'stage',
        width: '125px',
        resizable: true,
        searchable: true,
        type: GridDataType.TEXT,
        render: {
          valueToText: true,
          labelValues: () => this.stageLabelValues
        }
      },
      {
        header: 'activityCommitmentTask',
        field: 'activityCommitmentTask',
        width: '140px',
        resizable: true,
        searchable: false,
        type: GridDataType.TEXT,
        allowEmptyValue: true,
        render: {
          valueToText: true,
          labelValues: () => this.commitmentTaskLabelValues
        }
      }
    );
    this.dataColumns = cloneObject(columns);
  }

  ngOnInit(): void {
    super.ngOnInit();
    const gridSelect = this.gridSelect();
    if (gridSelect) {
      if (this.hasDelete) {
        gridSelect.hasDelete = () => this.hasDelete();
      }
      gridSelect.detectChanges();
    }
    this.initializeLocale();
    this.cdr.detectChanges();
  }

  detectChanges(): void {
    this.cdr?.detectChanges();
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    if (this.subs?.length) {
      for (const s of this.subs) {
        try {
          if (s) {
            s.unsubscribe();
          }
        } catch (e) {
          console.error(e);
        }
      }
    }
    this.cdr.detach();
  }

  /**
   * Devuelve la cantidad de documentos
   */
  public getValueGridCount(): number {
    return this.gridSelect()?.value?.length || 0;
  }

  writeValue(obj: DataMap[]): void {
    this.value = obj;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  valueChanged() {
    this.onTouched();
    this.change.emit(this.value);
    this.onChange(this.value);
    this.onTouched(this.value);
  }

  private saveSingleAdd(document: DocumentEntity) {
    document.stage = '';
    document.lastModifiedDate = new Date();
    document.lastModifiedBy = Session.getUserName();
    this.saveSingle.emit({
      entity: document,
      dropdownItem: this._clickedDropdownItem,
      grid: this.gridSelect().valueGrid()
    });
    this.valueChanged();
  }

  private removeRow(rowData) {
    this.removeSingle.emit(rowData);
    this.valueChanged();
  }

  onRowSelectionChange(rowSelectionEvent: RowSelection<IDocumentEntity>) {
    const $event = rowSelectionEvent.event;
    if ($event.added && $event.added.length > 0) {
      for (const document of $event.added) {
        this.saveSingleAdd(document);
      }
    }
    if ($event.removed && $event.removed.length > 0) {
      const data = this.gridSelect().data;
      for (const item of $event.removed) {
        const document = data.find((dataItem) => dataItem[this.gridSelect().primaryKey()] === item[this.gridSelect().primaryKey()]) as IDocumentEntity;
        if (document) {
          this.removeRow(document);
        } else {
          // Parche, al agregar más de un documento y después eliminar el anterior se pierde el valor en data y value, por lo que se devuelve un entity con el Id
          this.removeRow({ id: item, description: '', lastModifiedDate: new Date(), lastModifiedBy: null, stage: '' });
        }
      }
    }
    this.rowSelectionChange.emit(rowSelectionEvent);
  }

  clearDropdownItem() {
    this._clickedDropdownItem = null;
  }

  initializeLocale() {
    if (!this.label) {
      this.subs.push(this.translate.getFrom(DocumentMultiSelectComponent.LANG_CONFIG, 'document-multi-select-label').subscribe((tag) => (this.label = tag)));
    }
    if (!this.placeholder) {
      this.subs.push(this.translate.getFrom(DocumentMultiSelectComponent.LANG_CONFIG, 'document-multi-select-placeholder').subscribe((tag) => (this.placeholder = tag)));
    }
    this.subs.push(
      this.translate.getFrom(DocumentMultiSelectComponent.LANG_CONFIG, 'documents-data-grid-empty-grid-message').subscribe((tag) => (this.dataEmptyGridMessage = tag))
    );
    this.subs.push(
      this.translate.getFrom(DocumentMultiSelectComponent.LANG_CONFIG, 'document-multi-select-grid-select-dialogTitle').subscribe((tag) => (this.dialogTitle = tag))
    );
    this.setTranslatedStageLabels();
    this.translate.getFrom(DocumentMultiSelectComponent.LANG_CONFIG, 'commitmentTasks').subscribe((tag) => {
      this.setValidCommitmentTaskLabels(tag, DocumentMultiSelectComponent.LANG_CONFIG);
    });
    GridUtil.columns(this.valueColumns).subs(this.subs).langAsync(this.translate, DocumentMultiSelectComponent.LANG_CONFIG, 'document-multi-select-baseGrid');
    GridUtil.columns(this.dataColumns).subs(this.subs).langAsync(this.translate, DocumentMultiSelectComponent.LANG_CONFIG, 'document-multi-select-dialogGrid');
  }

  private setTranslatedStageLabels(): void {
    if (this._stageLabelConfig) {
      this.subs.push(
        this.translate.getFrom(this._stageLabelConfig, 'stages').subscribe((tag) => {
          this.setValidStageLabels(tag, this._stageLabelConfig);
        })
      );
    } else {
      this.subs.push(
        this.translate.getFrom(DocumentMultiSelectComponent.LANG_CONFIG, 'stages').subscribe((tag) => {
          this.setValidStageLabels(tag, DocumentMultiSelectComponent.LANG_CONFIG);
        })
      );
    }
  }

  private setValidStageLabels(tag: any, lang: BnextComponentPath): void {
    if (isObject(tag)) {
      this.stageLabelValues.stage = tag.stage;
      return;
    }
    if (lang === null) {
      console.error(`Missing stage object for document component with id "${this.id}".`);
    } else {
      console.error(`Invalid stage object for document component with id "${this.id}", tags: `, tag, ', lang: ', lang);
    }
    this.stageLabelValues = {
      stage: {
        '': 'Guardando...',
        '0': 'Ninguna',
        '1': 'Primera',
        '2': 'Segunda',
        '3': 'Tercera',
        '4': 'Cuarta',
        '5': 'Quinta',
        '6': 'Sexta'
      }
    };
  }

  private setValidCommitmentTaskLabels(tag: any, lang: BnextComponentPath): void {
    if (isObject(tag)) {
      this.commitmentTaskLabelValues.activityCommitmentTask = tag.activityCommitmentTask;
      return;
    }
    if (lang === null) {
      console.error(`Missing commitmentTask object for document component with id "${this.id}".`);
    } else {
      console.error(`Invalid commitmentTask object for document component with id "${this.id}", tags: `, tag, ', lang: ', lang);
    }
    this.commitmentTaskLabelValues = {
      activityCommitmentTask: {
        '': 'Guardando...',
        '1': 'Implementación',
        '2': 'Verificación',
        '3': 'Actividad'
      }
    };
  }

  viewRow(row: any, event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    const url = `v-document-viewer.view?id=${row.id}&simpleview=true`;
    this.menu.navigateBlankLegacy(url, this.previewNavigateModule());
  }

  public openDialog() {
    this.gridSelect().openDialog();
  }

  public showBannerEmpty(): boolean {
    this.bannerEmptyMessage = this.translate.instant('root.common.message.empty-documents');
    return this.gridSelect().isEmpty && this.enableBannerInfoEmpty();
  }
}
