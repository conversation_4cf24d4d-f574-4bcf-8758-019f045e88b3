import { cloneObject } from '@/core/utils/object';
import { Component, type OnInit, forwardRef, input } from '@angular/core';
import { type ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { takeUntil } from 'rxjs';
import { GridMultiSelectComponent } from 'src/app/core/grid-multi-select/grid-multi-select.component';
import { GridSelectInputType, type IBnextRowSelectionEventArgs } from '../grid-base-select/grid-base-select.interfaces';
import { GridBaseSelectModule } from '../grid-base-select/grid-base-select.module';
import { GridBaseSelectUtils } from '../grid-base-select/grid-base-select.utils';
import type { GridDataLoadedEvent } from '../grid/utils/grid.interfaces';
import type { DataMap } from '../utils/data-map';

/**
 * ToDo: Separar este componente en contructores diferentes donde cada tipo tenga sus propios parametros obligatorios
 */
@Component({
  selector: 'app-grid-side-multi-select',
  imports: [GridBaseSelectModule],
  styleUrls: ['./../grid-base-select/grid-base-select.directive.scss', 'grid-side-multi-select.scss'],
  templateUrl: './../grid-base-select/grid-base-select.directive.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GridSideMultiSelectComponent),
      multi: true
    }
  ]
})
export class GridSideMultiSelectComponent<DataRowType = DataMap<any>> extends GridMultiSelectComponent<DataRowType> implements ControlValueAccessor, OnInit {
  override gridBaseClasses: DataMap<boolean> = {
    'side-by-side': true
  };

  override hasDelete = true;

  override get valueGridHeightValue(): string {
    return '100%';
  }
  override get allowValueFiltering() {
    return true;
  }
  override get valueShowSearch() {
    return true;
  }
  override get inputType() {
    return GridSelectInputType.DRAGGABLE_GRID;
  }
  override get instanceHasDelete(): boolean {
    return true;
  }
  override get isHidden(): boolean {
    return false;
  }

  override allowValueGrouping = false;

  override get allowValueMaximize(): boolean {
    return true;
  }
  override get allowDataMaximize(): boolean {
    return true;
  }

  get isGridReady(): boolean {
    const dataGrid = this.dataGrid();
    const valueGrid = this.valueGrid();
    if (!valueGrid || !dataGrid) {
      return false;
    }
    return valueGrid.isGridReady && dataGrid.isGridReady;
  }

  readonly showTopImportExportButton = input(true);

  override ngOnInit() {
    super.ngOnInit();
    this.valueAutoMinimumHeight = true;
  }
  override initializeLocale() {
    super.initializeLocale();
    if (!this.draggableLeftSideLabel) {
      this.translate
        .getFrom(GridBaseSelectUtils.LANG_CONFIG, 'grid-select-draggableLeftSideLabel')
        .pipe(takeUntil(this.$destroy))
        .subscribe((tag) => (this.draggableLeftSideLabel = tag));
    }
    if (!this.draggableRightSideLabel) {
      this.translate
        .getFrom(GridBaseSelectUtils.LANG_CONFIG, 'grid-select-draggableRightSideLabel')
        .pipe(takeUntil(this.$destroy))
        .subscribe((tag) => (this.draggableRightSideLabel = tag));
    }
  }

  override onRowSelectionChange(rowSelectionEvent: IBnextRowSelectionEventArgs) {
    if (this.debug()) {
      console.log(this.id(), ', side.onRowSelectionChange: ', rowSelectionEvent);
    }
    // Parche cuando se pierde la información de oldSelection y selectedRows (NOTA: Se pierde al seleccionar celdas porque es un evento diferente)
    // Tambien se pierde por que los renglones no existen en el grid de valores
    const valueGrid = this.valueGrid();
    if (valueGrid && valueGrid.data.length > 0 && rowSelectionEvent.oldSelection.length !== valueGrid.data.length) {
      const values = cloneObject(valueGrid.data);
      const newRows = cloneObject(rowSelectionEvent.added || []);
      const newRowIds = newRows.map((item) => item[this.primaryKey()]);
      for (const valeItem of values) {
        const id = valeItem[this.primaryKey()];
        if (!newRowIds.includes(id)) {
          newRowIds.push(id);
          newRows.push(valeItem);
        }
      }
      const event: IBnextRowSelectionEventArgs = {
        oldSelection: values,
        newSelection: newRows,
        added: rowSelectionEvent.added,
        removed: rowSelectionEvent.removed,
        cancel: false
      };
      super.onRowSelectionChange(event);
    } else {
      super.onRowSelectionChange(rowSelectionEvent);
    }
  }
  override loadValueGrid(): boolean {
    /**
     * Siempre carga el grid de valores
     */
    return true;
  }

  override onValueGridLoad(event: GridDataLoadedEvent) {
    super.onValueGridLoad(event);
  }

  protected override filterLoadedValues(): void {
    const dataGrid = this.dataGrid();
    if (dataGrid) {
      dataGrid.data = dataGrid.data.filter((item) => {
        return !this.valueGrid().data.find((val: any) => val[this.primaryKey()] === item[this.primaryKey()]);
      });
    }
  }
}
