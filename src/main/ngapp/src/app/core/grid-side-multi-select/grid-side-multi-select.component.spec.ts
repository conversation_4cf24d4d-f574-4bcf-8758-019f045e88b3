import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MockCoreModule } from '../test/mock-core.module';
import { GridSideMultiSelectComponent } from './grid-side-multi-select.component';

import { MockProviders } from '../test/mock-providers';
import { configureTestSuite } from '../test/utils/configure-test-suite';

describe('GridSideMultiSelectComponent', () => {
  let component: GridSideMultiSelectComponent;
  let fixture: ComponentFixture<GridSideMultiSelectComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(GridSideMultiSelectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
