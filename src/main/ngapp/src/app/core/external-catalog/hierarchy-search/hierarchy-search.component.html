@if (gridInstanceData) {
  <app-grid-multi-select
    #grid
    [id]="name"
    [name]="name"
    [dataColumns]="gridInstanceData.gridColumns"
    [valueColumns]="gridInstanceData.gridColumns"
    [dataUrl]="gridInstanceData.searchUrl"
    [value]="gridInstanceData.fieldValue"
    [escapeUnderscore]="false"
    [dialogTitle]="gridInstanceData.dialogTitle"
    [hiddenInput]="true"
    [showPagingGreaterRows]="100"
    [hasDelete]="true"
    [allowsAuthorDelete]="true"
    [label]="gridInstanceData.dialogTitle"
    [maxHeightLimit]="true"
    [required]="false"
    (rowSelectionChange)="onHierarchySelectionChange($event)"
    (baseDialogClose)="onHierarchySelectionClose($event)"
  >
  </app-grid-multi-select>
}
