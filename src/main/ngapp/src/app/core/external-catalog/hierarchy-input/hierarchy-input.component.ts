import { Component, HostBinding, Input, type OnChanges, type SimpleChanges, input, output, viewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BnextCoreComponent } from 'src/app/core/bnext-core.component';

import { HammerModule } from '@angular/platform-browser';
import { IgxIconModule, IgxInputDirective, IgxInputGroupComponent, IgxInputGroupModule, IgxRippleModule } from '@infragistics/igniteui-angular';

import { BnextTranslateModule } from '../../i18n/bnext-translate.module';

import { NgClass } from '@angular/common';
import { ExternalCatalogUtil } from '../utils/external-catalog-util';
import type { HierarchyInputChange } from '../utils/external-catalog.interfaces';
import { HierarchyUtils } from '../utils/hierarchy-utils';
import type { HierarchyValueEntity, HierarchyValueProperty } from '../utils/hierarchy.interfaces';

let NEXT_NAME = 0;

@Component({
  selector: 'app-hierarchy-input',
  imports: [NgClass, BnextTranslateModule, HammerModule, FormsModule, ReactiveFormsModule, IgxInputGroupModule, IgxRippleModule, IgxIconModule],
  templateUrl: 'hierarchy-input.component.html',
  styleUrls: ['hierarchy-input.component.scss']
})
export class HierarchyInputComponent extends BnextCoreComponent implements OnChanges {
  private _value: HierarchyValueEntity[] = [];
  protected inputValue: HierarchyValueProperty = null;

  // TODO: Skipped for migration because:
  //  This input is used in combination with `@HostBinding` and migrating would
  //  break.
  @Input()
  @HostBinding('attr.name')
  public name = `hierarchy-input-${NEXT_NAME++}`;

  readonly inputGroup = viewChild('inputGroup', { read: IgxInputGroupComponent });

  readonly input = viewChild('input', { read: IgxInputDirective });

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public level: number;
  public readonly fieldLabel = input<string>(undefined);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public autoFocus = false;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public column: string;
  public readonly label = input<string>(undefined);

  get value(): HierarchyValueEntity[] {
    return this._value;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set value(value: HierarchyValueEntity[]) {
    if (HierarchyUtils.hasValueChanged(value, this._value, this.column)) {
      this._value = value;
      this.syncInput();
    } else {
      this._value = value;
    }
  }

  public readonly changed = output<HierarchyInputChange>();

  public readonly shouldFocus = output<IgxInputGroupComponent>();

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.autoFocus && this.autoFocus) {
      this.autoFocus = false;
      this.shouldFocus.emit(this.inputGroup());
    }
  }

  private findIndex(): number {
    if (!this.value?.length) {
      return -1;
    }
    const itemIndex = this.value?.findIndex((item) => item?.readonly === 1 && item?.level === this.level);
    if (itemIndex === null || itemIndex === undefined) {
      return -1;
    }
    return itemIndex;
  }

  clearInput(propageChange = true): void {
    const itemIndex = this.findIndex();
    if (itemIndex === -1) {
      return;
    }
    this.value[itemIndex] = null;
    this.onChangeValue(false, propageChange);
  }

  onInputFieldBlur(inputValue: string): void {
    this.setValueByName(inputValue);
    this.onChangeValue();
  }

  onInputFieldKeyUp(event: KeyboardEvent, inputValue: string): void {
    if (event.code === 'Enter' || event.code === 'NumpadEnter') {
      this.setValueByName(inputValue);
      this.onChangeValue(true);
    }
  }

  private setValueByName(inputValue: string): void {
    if (inputValue === null || typeof inputValue === 'undefined' || `${inputValue}`.trim() === '' || inputValue.length === 0) {
      this.clearInput(false);
    } else {
      if (!this.value) {
        this.value = [];
      }
      const itemIndex = this.findIndex();
      if (itemIndex !== -1) {
        this.value[itemIndex][this.column] = inputValue;
      } else {
        const newValue: HierarchyValueEntity = {
          id: `[${this.column}]`
        };
        ExternalCatalogUtil.settHierarchyEntityValues(newValue, this.column, this.level, 1);
        newValue[this.column] = inputValue;
        this.value.push(newValue);
      }
    }
  }

  private onChangeValue(requestedSearch = false, propagateChange = true): void {
    if (!propagateChange) {
      return;
    }
    this.changed.emit({
      value: this.value,
      level: this.level,
      requestedSearch,
      propagateChange: true
    });
  }

  private syncInput(): void {
    const itemIndex = this.findIndex();
    if (itemIndex !== -1) {
      const newInputValue = this.value[itemIndex][this.column];
      const inputValue = this.input();
      if (inputValue) {
        inputValue.value = newInputValue;
      }
      this.inputValue = newInputValue;
    } else {
      this.inputValue = null;
    }
  }
}
