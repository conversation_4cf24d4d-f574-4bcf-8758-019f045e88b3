import { FilteringExpressionsTree, FilteringLogic, type IFilteringExpressionsTree, IgxStringFilteringOperand } from '@infragistics/igniteui-angular';
import type { GridColumn } from '../../grid/utils/grid-column';
import { GridDataType } from '../../grid/utils/grid-data-type';
import type { CatalogHierarchyColumn } from '../../grid/utils/grid-external-catalog.interfaces';
import type { IGridFilteringExpression } from '../../grid/utils/grid-filtering-expression';
import { type GridFilterConfig, getFilters } from '../../grid/utils/grid-filtering-util';
import type { GridFilter } from '../../grid/utils/grid.interfaces';
import type { DataMap } from '../../utils/data-map';
import type { GridSearchDTO } from './external-catalog.interfaces';
import type { HierarchyFieldDTO, HierarchyValueEntity } from './hierarchy.interfaces';

const STRING_CONTAINS_OPERAND = IgxStringFilteringOperand.instance().condition('contains');

export function getHiearchyLevelFilters(gridConfig: GridSearchDTO): GridFilter {
  const filteringExpressionsTree = getHierarchyExpressionTree(gridConfig);
  const columnMap: DataMap<GridColumn<HierarchyValueEntity>> = {};
  for (const column of gridConfig.columns) {
    columnMap[column.column] = {
      field: column.column,
      header: column.label,
      type: GridDataType.TEXT,
      searchable: true,
      dynamicFieldType: null
    };
  }
  const filterBuild: GridFilterConfig<HierarchyValueEntity> = {
    gridId: `${gridConfig.selectInstanceName}-grid`,
    componentName: gridConfig.selectInstanceName,
    index: null,
    dynamicSearchEnabled: false,
    perPage: 250,
    filteringExpressionsTree: filteringExpressionsTree,
    sortingExpressions: null,
    groupingExpressions: null,
    allowFiltering: true,
    columnMap: columnMap,
    datePipe: null,
    dateFormat: null,
    escapeUnderscore: false
  };
  return getFilters(filterBuild);
}

export function getHierarchyExpressionTree(config: GridSearchDTO): IFilteringExpressionsTree {
  const filteringTree = new FilteringExpressionsTree(FilteringLogic.Or);
  const maxColumnFilterIndex = config.columns.length - 1;
  for (let index = 0; index < config.columns.length; index++) {
    const column = config.columns[index];
    if (index >= maxColumnFilterIndex) {
      continue;
    }
    if (!config.value) {
      continue;
    }
    if (config.value?.length > 0) {
      const rowValues = config.value
        .map((row) => row[column.column])
        .filter((value) => value !== null && typeof value !== 'undefined' && value !== '')
        .map((value) => `${value}`);
      const columnValue = Array.from(new Set<string>(rowValues)).join(',');
      addFilteringExpression(column.column, columnValue, filteringTree);
    }
  }
  if (config.valueText !== null && typeof config.valueText !== 'undefined' && config.valueText !== '') {
    const column = config.columns[maxColumnFilterIndex];
    addFilteringExpression(column.column, config.valueText, filteringTree);
  }
  return filteringTree;
}

export function buildFilteringExpression(column: string, value: string): IGridFilteringExpression {
  return {
    fieldName: column,
    isCustomFiltering: true,
    filterType: 'likeCriteria', // <-- solo se debe settear si se quiere hacer override del default
    searchVal: value,
    ignoreCase: true,
    condition: {
      name: column,
      isUnary: true,
      iconName: 'contains',
      logic: (target: any, value?: any, ignCase?: boolean, fieldName?: string) => {
        if (!fieldName) {
          return false;
        }
        return STRING_CONTAINS_OPERAND.logic(target, value, ignCase);
      }
    },
    likeOrSentence: false
  };
}

export function addFilteringExpression(column: string, value: string, filteringTree: FilteringExpressionsTree): void {
  const configuration = buildFilteringExpression(column, value);
  filteringTree.filteringOperands.push(configuration);
}

export function configureFilteringTreeForHierarchy<DataRowType>(
  filteringTree: FilteringExpressionsTree,
  hierarchValue: HierarchyValueEntity[],
  column: GridColumn<DataRowType>
) {
  if (!hierarchValue?.length) {
    return;
  }
  const hierarchyColumn = column as unknown as CatalogHierarchyColumn;
  const fieldsIndex: DataMap<HierarchyFieldDTO> = {};
  for (const field of hierarchyColumn.hierarchyFields) {
    fieldsIndex[field.column] = field;
  }
  const filtersIndex: DataMap<string[]> = {};
  for (const columnValue of hierarchValue) {
    const field = fieldsIndex[columnValue.column];
    let searchValue = columnValue[field.column];
    if (searchValue === null || typeof searchValue === 'undefined' || searchValue === '') {
      continue;
    }
    searchValue = `${searchValue}`;
    if (!filtersIndex[field.column]) {
      filtersIndex[field.column] = [];
    }
    filtersIndex[field.column].push(searchValue);
  }
  const searchKeys = Object.keys(filtersIndex);
  if (!searchKeys.length) {
    return;
  }
  for (const key of searchKeys) {
    const searchValues = filtersIndex[key];
    if (!searchValues.length) {
      continue;
    }
    const columnName = `${hierarchyColumn.hierarchyCode}_${key}`;
    if (searchValues.length > 1) {
      const localFilteringTree = new FilteringExpressionsTree(FilteringLogic.Or);
      for (const searchVal of searchValues) {
        addFilteringExpression(columnName, searchVal, localFilteringTree);
      }
      filteringTree.filteringOperands.push(localFilteringTree);
    } else {
      addFilteringExpression(columnName, searchValues[0], filteringTree);
    }
  }
}
