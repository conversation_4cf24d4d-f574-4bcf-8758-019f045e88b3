import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MockCoreModule } from '../test/mock-core.module';
import { RichtextDisplayComponent } from './richtext-display.component';

import { MockProviders } from '../test/mock-providers';
import { configureTestSuite } from '../test/utils/configure-test-suite';

describe('RichtextDisplayComponent', () => {
  let component: RichtextDisplayComponent;
  let fixture: ComponentFixture<RichtextDisplayComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RichtextDisplayComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
