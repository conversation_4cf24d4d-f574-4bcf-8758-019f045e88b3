<div class="dropdown-button" [class.no-available-drowpdown-items]="!hasAvailableDrowpdownItems" [class.round-button]="round()" [class.no-shadow]="!buttonShadow()">
  @if (selectedItem) {
    <button
      igxRipple
      type="button"
      [igxButton]="igxButtonType()"
      [disabled]="disabled()"
      class="selected-item"
      [ngClass]="displayDensityClass"
      [class.round]="round()"
      [style.background-color]="igxButtonBackground"
      [style.color]="igxButtonColor"
      [title]="selectedItem.text"
      (click)="selectItem()"
    >
      @if (!!selectedItem.iconName || (lockMainDropDownIcon && mainDropDownIcon)) {
        <igx-icon family="material" [style.color]="igxButtonColor">
          {{ lockMainDropDownIcon && mainDropDownIcon ? mainDropDownIcon : selectedItem.iconName }}
        </igx-icon>
      }
      @if (showLabel()) {
        <label [class.no-available-items]="!hasAvailableDrowpdownItems"> {{ selectedItem.text }} </label>
      }
    </button>
  }
  @if (selectedItem && hasAvailableDrowpdownItems) {
    <button
      #toogleButton
      [disabled]="disabled()"
      igxRipple
      type="button"
      class="toggle-menu"
      [class.round]="round()"
      [igxButton]="igxButtonType()"
      [ngClass]="displayDensityClass"
      [style.background-color]="igxButtonBackground"
      [style.color]="igxButtonColor"
      [igxDropDownItemNavigation]="dropdown()"
      [title]="selectedItem.text"
      (click)="toggleMenu($event)"
    >
      <igx-icon [style.color]="igxButtonColor">{{ dropDownIcon() }}</igx-icon>
    </button>
  }
  @if (isOpen && hasAvailableDrowpdownItems) {
    <igx-drop-down
      #dropdown
      class="dropdown-button-menu"
      (opened)="onOpened()"
      (opening)="onOpening()"
      (closed)="onClosed()"
      [ngClass]="displayDensityClass"
      (selectionChanging)="onDropwdownSelection($event)"
    >
      @for (item of items(); track item) {
        <igx-drop-down-item
          class="drop-down-menu-button"
          [value]="item"
          [class.igx-drop-down__item--selected]="item.selected"
          [title]="item.title || item.text"
          [style.min-width]="optionMiniumumWidth + 'px'"
          [hidden]="item.hidden || (item.selected && currentOptionHidden())"
          [isHeader]="!!item.header"
        >
          <a href="javascript: void(0);" class="menu-button menu-item">
            @if (item.iconName) {
              <igx-icon family="material"> {{ item.iconName }} </igx-icon>
            }
            {{ item.text }}
          </a>
        </igx-drop-down-item>
      }
    </igx-drop-down>
  }
</div>
