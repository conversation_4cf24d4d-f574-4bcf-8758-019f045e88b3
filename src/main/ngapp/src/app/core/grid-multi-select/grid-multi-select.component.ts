import * as GridUtil from '@/core/grid/utils/grid-util';
import { cloneObject } from '@/core/utils/object';
import { Component, Input, type OnInit, forwardRef, input, output } from '@angular/core';
import { type ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import type { GridSelectionMode, ISortingExpression, IgxInputGroupType } from '@infragistics/igniteui-angular';
import type { DropdownButtonItem } from '../dropdown-button/dropdown-button.interfaces';
import { GridBaseSelectDirective } from '../grid-base-select/grid-base-select.directive';
import type { IBnextRowSelectionEventArgs, RowSelection } from '../grid-base-select/grid-base-select.interfaces';
import { GridBaseSelectModule } from '../grid-base-select/grid-base-select.module';
import type { GridSearchChange } from '../grid/grid-search/grid-search.interfaces';
import type { GridDataLoadedEvent, GridImportData, GridImportedError, GridSearchFilterChips } from '../grid/utils/grid.interfaces';
import type { BnextComponentPath } from '../i18n/bnext-component-path';
import type { DataMap } from '../utils/data-map';

/**
 * ToDo: Separar este componente en contructores diferentes donde cada tipo tenga sus propios parametros obligatorios
 */
@Component({
  selector: 'app-grid-multi-select',
  imports: [GridBaseSelectModule],
  styleUrls: ['./../grid-base-select/grid-base-select.directive.scss'],
  templateUrl: './../grid-base-select/grid-base-select.directive.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GridMultiSelectComponent),
      multi: true
    }
  ]
})
export class GridMultiSelectComponent<DataRowType = DataMap<any>> extends GridBaseSelectDirective implements ControlValueAccessor, OnInit {
  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core',
    componentName: 'grid-multi-select'
  };

  private _tempValueGridValue: DataMap<any>[] = null;

  get value(): DataRowType[] {
    const valueGrid = this.valueGrid();
    if (!valueGrid) {
      return [];
    }
    return (valueGrid.data as DataRowType[]) || [];
  }

  public readonly baseDialogClosed = output<RowSelection>();

  public readonly askConfirmationOnDelete = input(false);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public textConfirmation = '';

  public readonly failedImportLabel = input('');

  public readonly failedImportMissingKeyLabel = input('');

  public readonly importConfirmationLabel = input('');

  public readonly missingImportDataLabel = input('');

  // TODO: Skipped for migration because:
  //  This input overrides a field from a superclass, while the superclass field
  //  is not migrated.
  @Input()
  public missingImportDataFromDataSourceLabel = '';

  @Input()
  public override inputGroupType: IgxInputGroupType = 'line';

  public readonly clearRepeated = input(true);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set active(active: boolean) {
    this.activeGrid = active;
  }
  public get active(): boolean {
    return this.activeGrid;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set value(value: DataMap[]) {
    if (this.debug()) {
      console.log(this.id(), ', set: ', value);
    }
    /**
     * ToDo: Mover este código a `noOnChanges`, aquí se evalua demasiadas veces
     *
     * No agregar más código aquí.
     **/
    const valueGrid = this.valueGrid();
    if (!valueGrid && value?.length > 0) {
      this.setValueGridActive();
      this.cdr.detectChanges();
      this.totalCount = value.length;
    }
    if (!valueGrid) {
      this._tempValueGridValue = value;
      return;
    }
    if (valueGrid && valueGrid.data !== value) {
      valueGrid.data = value;
      this._tempValueGridValue = null;
      this.valueChanged();
      this.filterLoadedValues();
    }
  }

  private _selectionMode: GridSelectionMode = 'multiple';

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set selectionMode(mode: GridSelectionMode) {
    this._selectionMode = mode;
  }

  readonly entityDynamicTypeIds = input<number[]>([]);

  override ngOnInit() {
    super.ngOnInit();
    if (this.textConfirmation === '' && this.askConfirmationOnDelete()) {
      this.textConfirmation = this.translateService.instant('root.common.message.default-confirmation-message');
    }
  }

  override hasValue(): boolean {
    return this.value && this.value.length > 0;
  }

  override get selectionMode(): GridSelectionMode {
    return this._selectionMode;
  }

  override get valueArray(): any[] {
    return this.value;
  }

  override get actions(): DropdownButtonItem[] {
    return null; // Do nothing
  }

  public getDialogGridCount(): number {
    return this.dataGrid()?.data?.length || 0;
  }

  public getValueIds(): number[] {
    return this.value.map((item) => item[this.primaryKey()]);
  }

  override loadValueGrid(): boolean {
    const result = super.loadValueGrid();
    if (this._tempValueGridValue !== null && typeof this._tempValueGridValue !== 'undefined' && this._tempValueGridValue.length > 0 && this.valueGrid()) {
      this.value = this._tempValueGridValue;
      this.setValueGridActive();
    }
    return result;
  }

  override onClose() {
    super.onClose();
    this.baseDialogClosed.emit({
      value: this.value,
      event: null
    });
  }

  override onRowSelectionChange(rowSelectionEvent: IBnextRowSelectionEventArgs): void {
    if (this.debug()) {
      console.log(this.id(), ', multi.onRowSelectionChange: ', rowSelectionEvent);
    }
    const $event: IBnextRowSelectionEventArgs = rowSelectionEvent;
    const rowSelection: IBnextRowSelectionEventArgs = {
      oldSelection: null,
      newSelection: null,
      added: null,
      removed: null,
      event: rowSelectionEvent.event,
      cancel: rowSelectionEvent.cancel
    };
    const valueGrid = this.valueGrid();
    if (valueGrid?.data?.length > 0 && rowSelectionEvent.added?.length > 0) {
      // Parche cuando se pierde la información de oldSelection y selectedRows (NOTA: Se pierde al seleccionar celdas porque es un evento diferente)
      // Tambien se pierde por que los renglones no existen en el grid de valores
      const valueRows = valueGrid.data;
      const newRows = cloneObject(rowSelectionEvent.added || []);
      const newRowIds = newRows.map((item) => item[this.primaryKey()]);
      for (const valeItem of valueRows) {
        const id = valeItem[this.primaryKey()];
        if (!newRowIds.includes(id)) {
          newRowIds.push(id);
          newRows.push(valeItem);
        }
      }
      rowSelection.newSelection = newRows;
    } else {
      rowSelection.newSelection = $event.newSelection || null;
    }
    rowSelection.oldSelection = $event.oldSelection || null;
    rowSelection.removed = $event.removed || [];
    rowSelection.added = $event.added || [];
    const canAuthorEdit = rowSelection.removed.length && this.canAuthorEdit.findIndex((c) => c.primaryKey === rowSelection.removed[0][this.primaryKey()]) === -1;
    const allowsAuthorDelete = this.allowsAuthorDelete();
    if (!this.instanceHasDelete && !allowsAuthorDelete && rowSelection.removed?.length > 0 && canAuthorEdit) {
      $event.cancel = true;
      return;
    }
    const authorCanEdit = allowsAuthorDelete && $event?.removed.length > 0 ? canAuthorEdit : false;
    if ((this.instanceHasDelete || authorCanEdit) && this.askConfirmationOnDelete() && rowSelection.removed?.length > 0) {
      this.dialogService.confirm(this.textConfirmation).then(
        () => {
          if (rowSelection.newSelection) {
            const data: DataMap[] = this.valueGrid().data.filter((item) => !!rowSelection.newSelection.find((r) => r[this.primaryKey()] === item[this.primaryKey()]));
            const ids = data.map((item) => item[this.primaryKey()]);
            for (const item1 of this.dataGrid().data.filter(
              (item) => !!rowSelection.newSelection.find((row) => row[this.primaryKey()] === item[this.primaryKey()]) && ids.indexOf(item[this.primaryKey()]) === -1
            )) {
              data.push(item1);
            }
            const valueGridValue = this.valueGrid();
            valueGridValue.data = data;
            valueGridValue.refreshNewDataLoaded({
              gridId: valueGridValue.id(),
              data: data,
              parseFromDynamicResults: false,
              count: 0,
              status: GridUtil.GRID_INFO_STATUS_SUCCESS
            });
            this.updateValueCalcGridHeight(data);
            this.data = this.dataGrid().data;
            this.rowSelectionChange.emit({
              value: data,
              event: rowSelection
            });
          } else {
            this.rowSelectionChange.emit({
              value: this.value,
              event: rowSelection
            });
          }
        },
        () => {
          this.dataGrid().selectRows(rowSelection.removed, false);
          $event.cancel = true;
          return;
        }
      );
    } else {
      let tempRowId;
      let valueIds;
      const dataGrid = this.dataGrid();
      if (rowSelection.newSelection && dataGrid) {
        let valueData;
        const newSelection = rowSelection.newSelection || [];
        valueData = valueGrid.data.filter((item) => {
          return (
            !!newSelection.find((row) => row[this.primaryKey()] === item[this.primaryKey()]) ||
            !rowSelection.removed.find((row) => row[this.primaryKey()] === item[this.primaryKey()])
          );
        });
        valueIds = valueData.map((item) => item[this.primaryKey()]);
        for (const item of dataGrid.data) {
          tempRowId = item[this.primaryKey()];
          if (
            newSelection.find((row) => row[this.primaryKey()] === tempRowId) && // <-- Filas que deben agregarse
            !rowSelection.removed.find((row) => row[this.primaryKey()] === tempRowId) && // <-- Filas que no hayan sido borradas
            !valueIds.find((rowId) => rowId === tempRowId) // <-- Filas que no existan ya
          ) {
            valueData.push(item);
          }
        }
        // Se remueven valores repetidos
        const uniqueIds = [];
        valueData = this.setSwapGridValueData(valueData, uniqueIds, this.primaryKey());
        valueGrid.refreshNewDataLoaded({
          gridId: valueGrid.id(),
          data: valueData,
          parseFromDynamicResults: false,
          count: 0,
          status: GridUtil.GRID_INFO_STATUS_SUCCESS
        });
        this.updateValueCalcGridHeight(valueData);
        if (this.isTypeDraggableGrid) {
          // Sel eliminan los renglones del grid de BACKEND
          this.updateLeftGrid(this.primaryKey(), uniqueIds);
        } else {
          this.data = dataGrid.data;
        }
        this.rowSelectionChange.emit({
          value: valueData,
          event: rowSelection
        });
      } else if (this.isTypeDraggableGrid) {
        // Se devuelven las filas al grid de BACKEND
        const removedData = valueGrid.data.filter((item) => {
          tempRowId = item[this.primaryKey()];
          return rowSelection.removed.find((row) => row[this.primaryKey()] === tempRowId);
        });
        dataGrid.data.push(...removedData);
        this._clearRepeatedData();
        this.rowSelectionChange.emit({
          value: this.value,
          event: rowSelection
        });
        dataGrid.selectedRows = [];
      } else {
        this.rowSelectionChange.emit({
          value: this.value,
          event: rowSelection
        });
      }
    }
  }

  private _clearRepeatedData(): void {
    if (!this.clearRepeated()) {
      return;
    }
    const uniqueIds = [];
    let valueIds: any[];
    const valueGrid = this.valueGrid();
    if (valueGrid?.data?.length > 0) {
      valueIds = valueGrid.data.map((item) => item[this.primaryKey()]);
    } else {
      valueIds = [];
    }
    const dataGrid = this.dataGrid();
    if (dataGrid) {
      if (dataGrid?.data?.length > 0) {
        this.data = dataGrid.data.reduce((acc, row) => {
          const primaryKey = this.primaryKey();
          if (!uniqueIds.includes(row[primaryKey]) && !valueIds.includes(row[primaryKey])) {
            acc.push(row);
            uniqueIds.push(row[primaryKey]);
          }
          return acc;
        }, []) as DataRowType[];
      }
    }
  }

  override onDataGridLoad($event: GridDataLoadedEvent) {
    super.onDataGridLoad($event);
    this._clearRepeatedData();
  }

  onValueGridLoad(event: GridDataLoadedEvent) {
    if (event.data.length === 0) {
      return;
    }
    this.valueLoad.next(event.data);
    this.valueLoad.complete();
  }

  public sort(expression: ISortingExpression | ISortingExpression[]): void {
    const valueGrid = this.valueGrid();
    if (!valueGrid) {
      return;
    }
    valueGrid.sort(expression);
  }

  protected filterLoadedValues(): void {}

  /**
   * Limpia filas repetidas y limpia las filas del valueGrid que ya no existan en data grid
   * Nota: Usar solo cuando se necesite refrescar filas del dataGrid y ValueGrid mediante acciones que requieran eliminar u ocultar
   * filas del dataGrid.
   */
  public updateGridsData(): void {
    this._clearRepeatedData();
    const valueGrid = this.valueGrid();
    if (!valueGrid.data) {
      return;
    }
    const dateIds = this.dataGrid().data.map((item) => item[this.primaryKey()]);
    valueGrid.data = valueGrid.data.filter((row) => dateIds.includes(row[this.primaryKey()]));
  }

  private updateLeftGrid(column: string, uniqueIds: string[]) {
    const dataGrid = this.dataGrid();
    dataGrid.data = this.data = dataGrid?.data.filter((row) => !uniqueIds.includes(row[column]));
    dataGrid.selectedRows = [];
    dataGrid.detectChanges();
    this.cdr.detectChanges();
  }

  private setSwapGridValueData(valueData: any[], uniqueCodes: string[], columnName: string): any[] {
    return valueData.reduce((acc, row) => {
      if (!uniqueCodes.includes(row[columnName])) {
        acc.push(row);
        uniqueCodes.push(row[columnName]);
      }
      return acc;
    }, []);
  }

  private buildSearchConfig(data: GridImportData<DataRowType>): GridSearchChange<any> {
    if (data?.keys?.length > 0) {
      const values = data.keys.join(',');
      const filterValues: DataMap<GridSearchFilterChips<DataRowType>> = {};
      const searchConfig: GridSearchChange<DataRowType> = {
        isGlobalSearchByEnter: false,
        isGlobalSearch: false,
        searchText: '',
        filterValues: filterValues,
        refreshData: false,
        caseSensitive: false,
        filteringTree: null
      };
      const importKey = this.importSchema()[this.importColumnIdLabel()].prop;
      const importConfig: GridSearchFilterChips<DataRowType> = {
        value: values,
        column: undefined
      };
      filterValues[importKey] = importConfig;
      return searchConfig;
    }
    return null;
  }

  confirmImportExcel(data: GridImportData<DataRowType>): void {
    this.loader.hide();
    let anyIdErrors: GridImportedError[];
    if (data.errors?.length > 0) {
      anyIdErrors = data.errors.filter((error) => error.column === this.importColumnIdLabel());
    } else {
      anyIdErrors = [];
    }
    if (anyIdErrors.length > 0) {
      console.error(`File to import Excel records, please check the import schema ${JSON.stringify(this.importSchema())}`, data.errors);
      const anyRequired = anyIdErrors.filter((error) => error.error === 'required');
      if (anyRequired.length > 0) {
        this.dialogService.error(this.failedImportMissingKeyLabel().replace('{numberRecords}', `${anyRequired.length}`));
      } else {
        this.dialogService.error(this.failedImportLabel());
      }
      this.loader.hide();
    } else {
      this.dialogService.confirm(this.importConfirmationLabel().replace('{numberRecords}', `${data?.keys?.length ?? 0}`)).then(
        () => {
          this.loader.show();
          this.onImportedExcel(data);
        },
        () => {
          this.clearExcelSearchConfig();
        }
      );
    }
  }

  private onImportedExcel(data: GridImportData<DataRowType>): void {
    this.updateExcelSearchConfig(data);
    if (!data.keys?.length) {
      this.dialogService.error(this.failedImportLabel());
      this.loader.hide();
    } else {
      this.clearImportedData().then(
        () => this.applyImportedExcelData(data),
        (error) => {
          console.error(`File to import Excel records, please check the import schema ${JSON.stringify(this.importSchema())}`, error);
          this.dialogService.error(this.failedImportLabel());
          this.clearExcelSearchConfig();
          this.loader.hide();
        }
      );
    }
  }

  private updateExcelSearchConfig(data: GridImportData<DataRowType>): void {
    const searchComponent = this.dataGrid().searchComponent();
    const dataGrid = this.dataGrid();
    if (searchComponent) {
      const searchConfig = this.buildSearchConfig(data);
      if (searchConfig) {
        const config = searchComponent.updateLastSearchConfig(searchConfig);
        if (config) {
          dataGrid.searchConfig = config;
          dataGrid.newFilteredSearch(config);
        }
      } else {
        searchComponent.deleteFilters();
        dataGrid.detectChanges();
      }
    }
  }

  private applyImportedExcelData(data: GridImportData<DataRowType>): void {
    this.clearExcelSearchConfig();
    const importKey = this.importSchema()[this.importColumnIdLabel()].prop;
    const valueData = this.dataGrid()?.data?.filter((row) => data.keys.indexOf(row[importKey]) !== -1) || [];
    if (!valueData.length) {
      console.error('There are no records found in current data.', data.keys);
      const message = this.missingImportDataFromDataSourceLabel.replace('{numberRecords}', `${data.rows?.length ?? 0}`);
      this.dialogService.error(message);
      this.loader.hide();
      return;
    }
    const selectionIds = valueData.map((row) => row[this.primaryKey()]);
    const selectionKeys = valueData.map((row) => row[importKey]);
    const missingData = data.keys.filter((key) => selectionKeys.indexOf(key) === -1);
    if (missingData?.length > 0) {
      console.error('There are records not found in current data.', missingData);
      const message = this.missingImportDataLabel().replace('{numberRecords}', `${missingData?.length ?? 0}`);
      this.dialogService.error(message, missingData.join(', '));
      this.loader.hide();
    } else {
      this.onRowSelectionChange({
        oldSelection: [],
        newSelection: valueData,
        added: valueData,
        removed: [],
        cancel: false
      });
      this.updateValueCalcGridHeight(valueData);
      this.updateLeftGrid(importKey, selectionIds);
      this.dataGrid()
        .refreshData()
        .then(
          () => {
            this.loader.hide();
            this.importedExcel.emit(data);
          },
          (error) => {
            console.error('File to refresh data after selecting Excel records.', error);
          }
        );
    }
  }

  public clearExcelSearchConfig(): void {
    this.updateExcelSearchConfig({ errors: [], keys: [], rows: [] });
  }

  public clearImportedData(): Promise<void> {
    // Se resetean los grids al cargar un nuevo documento de excel al importar
    const valueGrid = this.valueGrid();
    if (valueGrid?.data.length > 0) {
      valueGrid.data = [];
      valueGrid?.refreshNewDataLoaded({
        gridId: valueGrid.id(),
        data: [],
        parseFromDynamicResults: false,
        count: 0,
        status: GridUtil.GRID_INFO_STATUS_SUCCESS
      });
    }
    return this.dataGrid().refreshData(0, 0);
  }
}
