import * as DomUtil from '@/core/utils/dom-util';
import * as NumberUtil from '@/core/utils/number-util';
import { equalsObject } from '@/core/utils/object';
import { nameInitials, removeDiacritics, sortArrayByAttribute, stringToColour, textColor } from '@/core/utils/string-util';
import { CommonModule, NgClass, NgStyle } from '@angular/common';
import {
  type AfterViewInit,
  Component,
  ElementRef,
  HostBinding,
  Injector,
  Input,
  type OnChanges,
  type OnDestroy,
  type OnInit,
  Output,
  type SimpleChanges,
  ViewChild,
  forwardRef,
  inject,
  input,
  output,
  viewChild
} from '@angular/core';
import {
  type AbstractControl,
  type ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  NgControl,
  ReactiveFormsModule,
  UntypedFormControl,
  type UntypedFormGroup
} from '@angular/forms';
import {
  AbsoluteScrollStrategy,
  AutoPositionStrategy,
  ElasticPositionStrategy,
  GlobalPositionStrategy,
  HorizontalAlignment,
  IgxAvatarComponent,
  IgxBadgeComponent,
  IgxDropDownComponent,
  IgxDropDownItemComponent,
  IgxForOfDirective,
  IgxHintDirective,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputDirective,
  IgxInputGroupComponent,
  IgxLabelDirective,
  IgxPrefixDirective,
  IgxRippleDirective,
  IgxSuffixDirective,
  type OverlaySettings,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import { Subject, type Subscription } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { BnextCoreComponent } from '../bnext-core.component';
import { RestApiModule } from '../rest-api.module';
import { NoticeService } from '../services/notice.service';
import { type OverlayId, OverlayService } from '../services/overlay.service';
import { BnextCloseScrollStrategy } from '../utils/bnext-scroll-strategy';
import { EnumDisplayDensity } from '../utils/display-density';
import type { OptionsInput } from '../utils/interfaces';
import type { TextHasValue } from '../utils/text-has-value'; /* eslint-disable brace-style */
import type { DataMap } from './../utils/data-map';
import {
  DROPDOWN_DEFAULT_HEIGHT_PX,
  DROPDOWN_DEFAULT_ITEM_HEIGHT_PX,
  DROPDOWN_DEFAULT_SCROLL_WIDTH_PX,
  DROPDOWN_FONT_CHAR_HEIGHT_PX,
  DROPDOWN_MIN_WIDTH_PX
} from './../utils/drop-down-constants';
import { BlackListPipe, type IgxAutocompleteFilter, IgxAutocompletePipeContains } from './dropdown-search-filters';
import type { DropdownHasValue, DropdownPrefixEvent, IDropdownSearchComponent } from './dropdown-search.interfaces';

let DROPDOWN_SEARCH_ID = 0;

const EXCLUED_OPEN_KEYS = ['Escape', 'Esc', 'Tab', 'ShiftLeft'];

@Component({
  selector: 'app-dropdown-search',
  templateUrl: './dropdown-search.component.html',
  styleUrls: ['./dropdown-search.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DropdownSearchComponent),
      multi: true
    }
  ],
  imports: [
    CommonModule,
    IgxAvatarComponent,
    IgxIconButtonDirective,
    IgxBadgeComponent,
    IgxInputGroupComponent,
    NgClass,
    FormsModule,
    IgxInputDirective,
    ReactiveFormsModule,
    IgxLabelDirective,
    IgxPrefixDirective,
    IgxIconComponent,
    IgxSuffixDirective,
    IgxRippleDirective,
    IgxHintDirective,
    IgxDropDownComponent,
    NgStyle,
    IgxForOfDirective,
    IgxDropDownItemComponent,
    IgxAutocompletePipeContains,
    BlackListPipe
  ]
})
export class DropdownSearchComponent
  extends BnextCoreComponent
  implements OnInit, OnDestroy, ControlValueAccessor, AfterViewInit, OnChanges, OverlayId, IDropdownSearchComponent
{
  noticeService = inject(NoticeService);
  overlayService = inject(OverlayService);
  private _injector = inject(Injector);

  private _randomId: string = null;
  private _options: DropdownHasValue[] = [];
  private _optionMap: DataMap<string> = null;
  private _blacklistValues = new Set<number | string>();
  private _text: string = null;
  private _avatarBackgroundColor = null;
  private _avatarInitialsColor = null;
  private _avatarSrc = null;
  private _avatarValue = null;
  private _showAll = false;
  private _required = false;
  private _invalid = false;
  private _lastFilter: string = null;
  private _controlValue: string = null;
  private _overlaySettings: OverlaySettings = null;
  private _ngControl: NgControl = null;
  private _destroy$ = new Subject<boolean>();
  private _destroyed = false;
  private controlChangesSub: Subscription;
  private _onRefreshVirtualScroll = new Subject<boolean>();
  private autoCompleteDisableId: string;
  private _dropdownVirtualStyle: DataMap<string> = {};
  private _badgeAvatar: ElementRef = null;
  private _forceFocus = false;
  private _valueFormControl: AbstractControl = new UntypedFormControl(null);

  readonly EnumDisplayDensity = EnumDisplayDensity;
  itemHeight = DROPDOWN_DEFAULT_ITEM_HEIGHT_PX;
  itemsMaxHeight = DROPDOWN_DEFAULT_HEIGHT_PX;

  textFormControl: UntypedFormControl;

  isOpen = false;
  isOpened = false;
  isOpening = false;
  isClosing = false;
  isFiltering = true;
  isArrowSearch = false;
  optionMiniumumWidth = DROPDOWN_MIN_WIDTH_PX;
  itemMinWidth = DROPDOWN_MIN_WIDTH_PX;
  fontSizePx = DROPDOWN_FONT_CHAR_HEIGHT_PX;
  targetWidth = null;

  readonly avatar = viewChild<IgxAvatarComponent>('avatar');

  // TODO: Skipped for migration because:
  //  Accessor queries cannot be migrated as they are too complex.
  @ViewChild('badgeAvatar', { read: ElementRef })
  set badgeAvatar(value: ElementRef) {
    this._badgeAvatar = value;
    this.updateAvatarOverlaySettings();
  }

  get badgeAvatar(): ElementRef {
    return this._badgeAvatar;
  }

  readonly dropdown = viewChild<IgxDropDownComponent>('dropdown');
  readonly textInput = viewChild('textInput', { read: IgxInputDirective });
  readonly inputGroup = viewChild('inputGroup', { read: IgxInputGroupComponent });
  readonly textInputDom = viewChild('textInput', { read: ElementRef });
  readonly labelInputDom = viewChild('labelInput', { read: ElementRef });
  readonly virtDirRemote = viewChild('virtDirRemote', { read: IgxForOfDirective });

  override watchResizeEvents = true;

  get text(): string {
    return this._text;
  }

  get controlValue(): string {
    return this._controlValue;
  }

  get suppressInputAutofocus(): boolean {
    return !this.openDropdownOnIconClick();
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  get valueFormControl(): AbstractControl {
    return this._valueFormControl;
  }
  set valueFormControl(_valueFormControl: AbstractControl) {
    this._valueFormControl = _valueFormControl || new UntypedFormControl(null);
  }

  // TODO: Skipped for migration because:
  //  This input is used in combination with `@HostBinding` and migrating would
  //  break.
  @HostBinding('attr.id')
  @Input()
  id = `app-dropdown-search-${DROPDOWN_SEARCH_ID++}`;
  readonly type = input<'line' | 'box' | 'border' | 'search'>('line');
  readonly help = input('');
  readonly scrollIntoView = input(true);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  miniView = false;

  readonly showHelp = input(false);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  iconName: string = null;
  readonly inputGroupClasses = input<DataMap>(null);
  readonly name = input<string>(null);
  readonly formGroup = input<UntypedFormGroup>(undefined);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set disabled(disabled) {
    if (this.control) {
      if (disabled) {
        this.control.disable();
      } else {
        this.control.enable();
      }
    }
  }
  get disabled() {
    if (this.control) {
      return this.control.disabled;
    }
    return false;
  }
  readonly showMinViewTitle = input(false);
  readonly tabindex = input(0);
  readonly dropdownWidth = input<number>(null);
  readonly hasClear = input(true);
  readonly sortable = input(true);
  readonly removeDuplicates = input(false);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set invalid(_invalid: boolean) {
    if (!!_invalid !== this._invalid) {
      if (this.control) {
        if (_invalid) {
          this.control.markAllAsTouched();
        } else {
          this.control.markAsPristine();
          this.control.markAsUntouched();
        }
        this.control.updateValueAndValidity();
      }
      this._invalid = _invalid;
    }
  }

  get invalid(): boolean {
    return this._invalid;
  }

  readonly label = input<string>(null);

  readonly scrollContainer = input<HTMLElement>(undefined);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set required(_required: boolean) {
    this._required = _required;
  }

  get required(): boolean {
    return this._required;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set blacklistValues(_blacklistValues: Set<number | string>) {
    if (!equalsObject(this._blacklistValues, _blacklistValues)) {
      this._blacklistValues = _blacklistValues || new Set();
      this.refreshOptions();
    }
  }

  get blacklistValues(): Set<number | string> {
    return this._blacklistValues;
  }

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  input: OptionsInput = null;

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set value(_value: number | string) {
    this.valueFormControl.setValue(_value);
  }

  get value(): number | string {
    if (this.invalid) {
      return null;
    }
    return this.valueFormControl.value;
  }

  get dropdownVirtualStyle(): DataMap {
    return this._dropdownVirtualStyle;
  }

  get avatarValue(): string {
    return this._avatarValue || null;
  }

  get avatarBackgroundColor(): string {
    if (this.avatarValue === null) {
      return null;
    }
    return this._avatarBackgroundColor;
  }

  get avatarInitialsColor(): string {
    if (this.avatarValue === null) {
      return null;
    }
    return this._avatarInitialsColor;
  }

  get avatarSrc(): string {
    if (this.avatarValue === null) {
      return '';
    }
    return this._avatarSrc || '';
  }

  readonly avatarIcon = input('person');

  get options(): DropdownHasValue[] {
    return this._options || [];
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set options(_options: TextHasValue[]) {
    this._options = _options as TextHasValue[];
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set optionMap(_optionMap: DataMap<string>) {
    this._optionMap = _optionMap;
  }

  get control(): UntypedFormControl {
    return this.textFormControl;
  }

  public get focusable(): ElementRef {
    if (this.miniView) {
      return this.avatar()?.elementRef;
    }
    return this.inputGroup()?.element;
  }
  public get focusableNative(): HTMLElement {
    return this.focusable?.nativeElement;
  }

  @Output()
  get focused(): boolean {
    if (this.disabled) {
      return false;
    }
    if (this.miniView) {
      const avatar = this.avatar();
      if (avatar) {
        if (this.showMinViewTitle()) {
          if (avatar.elementRef) {
            return avatar.elementRef.nativeElement === document.activeElement;
          }
          return false;
        }
        if (this.badgeAvatar) {
          return this.badgeAvatar.nativeElement !== document.activeElement;
        }
        return false;
      }
      return false;
    }
    const textInput = this.textInput();
    if (textInput) {
      return textInput.focused;
    }
    return false;
  }

  public readonly changed = output<TextHasValue>();
  public readonly prefixClick = output<DropdownPrefixEvent>();
  public readonly inputFocused = output<Event>();
  public readonly opened = output<any>();
  public readonly closed = output<any>();
  public readonly cleared = output<any>();
  public readonly empty = output<DropdownSearchComponent>();
  public readonly customAvatarFocus = output<boolean>();
  public readonly selectedItem = output<object>();

  public readonly openDropdownOnIconClick = input(true);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  public hint: string;

  constructor() {
    super();

    this.textFormControl = new UntypedFormControl(null);
    this.autoCompleteDisableId = new Date().getTime().toString();
    this.overlayService.subscriber.pipe(takeUntil(this.$destroy)).subscribe((object: OverlayId) => {
      if (object.id !== this.id) {
        this.dropdown()?.close();
      }
    });
  }

  onChange: (_) => void = (_) => {};
  onTouched: (_) => void = (_) => {};

  override ngOnInit() {
    super.ngOnInit();
    this._overlaySettings = {
      closeOnOutsideClick: true,
      modal: false,
      scrollStrategy: new BnextCloseScrollStrategy(this.scrollContainer()),
      positionStrategy: new ElasticPositionStrategy({
        horizontalStartPoint: HorizontalAlignment.Left,
        verticalStartPoint: VerticalAlignment.Bottom
      })
    };
    this._ngControl = this._injector.get<NgControl>(NgControl, null);
    this.subs.push(
      this._onRefreshVirtualScroll
        .pipe(throttleTime(250, undefined, { leading: true, trailing: true }), takeUntil(this._destroy$))
        .subscribe(() => this.defineScrollSize())
    );
    this.refreshVirtualScroll();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.debug()) {
      console.log('changed -> ', changes);
    }
    super.ngOnChanges(changes);
    if (changes.dropdownWidth && !equalsObject(changes.dropdownWidth.previousValue, changes.dropdownWidth.currentValue)) {
      this.refreshDropdownWidth();
    }
    if (changes.input && !equalsObject(changes.input.previousValue, changes.input.currentValue)) {
      this.refreshInput();
    }
    if (changes.optionMap && !equalsObject(changes.optionMap.previousValue, changes.optionMap.currentValue)) {
      this.refreshOptionMap();
    } else if (changes.options && !equalsObject(changes.options.previousValue, changes.options.currentValue)) {
      this.refreshOptions();
    }
    if (changes.value && !equalsObject(changes.value.previousValue, changes.value.currentValue)) {
      this.writeValue(changes.value.currentValue, true);
    }
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit();
    this.defineDropdownTarget();
    this.refreshVirtualScroll();
  }

  public focus(): boolean {
    if (this.miniView) {
      const avatar = this.avatar();
      if (avatar) {
        if (this.showMinViewTitle()) {
          if (avatar.elementRef) {
            if (avatar.elementRef.nativeElement !== document.activeElement) {
              this._forceFocus = true;
              avatar.elementRef.nativeElement.focus();
              return true;
            }
          } else {
            console.error('Failed to focus app-dropdown-search, avatar is not ready.');
          }
        } else {
          if (this.badgeAvatar) {
            if (this.badgeAvatar.nativeElement !== document.activeElement) {
              this._forceFocus = true;
              this.badgeAvatar.nativeElement.focus();
              return true;
            }
          } else {
            console.error('Failed to focus app-dropdown-search, badge avatar is not ready.');
          }
        }
      } else {
        console.error('Failed to focus app-dropdown-search, avatar is not available.');
      }
    } else {
      const textInput = this.textInput();
      if (textInput) {
        if (!textInput.focused) {
          this._forceFocus = true;
          textInput.focus();
          return true;
        }
      } else {
        console.error('Failed to focus app-dropdown-search, textinput is not available.');
      }
    }
    return false;
  }

  private defineDropdownTarget(): void {
    if (this._overlaySettings.target) {
      return;
    }
    const textInput = this.textInput();
    if (textInput && !this.miniView) {
      this._overlaySettings.target = textInput?.nativeElement as HTMLElement;
      this._overlaySettings.excludeFromOutsideClick = [];
      const labelInputDom = this.labelInputDom();
      if (labelInputDom?.nativeElement) {
        this._overlaySettings.excludeFromOutsideClick.push(labelInputDom?.nativeElement);
      }
      const inputGroup = this.inputGroup();
      if (inputGroup?.element?.nativeElement) {
        this._overlaySettings.excludeFromOutsideClick.push(inputGroup?.element?.nativeElement);
      }
    } else if (this.miniView && this.avatar()) {
      this.updateAvatarOverlaySettings();
    } else if (this.isOpen) {
      console.error('DropDownSearch element will not be displayed because there´s no target available');
    }
  }

  private updateAvatarOverlaySettings() {
    const avatar = this.avatar();
    if (!avatar) {
      return;
    }
    this._overlaySettings.target = avatar?.elementRef?.nativeElement as HTMLElement;
    this._overlaySettings.excludeFromOutsideClick = [];
    if (avatar?.elementRef?.nativeElement) {
      this._overlaySettings.excludeFromOutsideClick.push(avatar?.elementRef?.nativeElement);
    }
    if (this.badgeAvatar?.nativeElement) {
      this._overlaySettings.excludeFromOutsideClick.push(this.badgeAvatar?.nativeElement);
    }
  }

  override ngOnDestroy(): void {
    this._destroyed = true;
    super.ngOnDestroy();
    if (this.controlChangesSub && !this.controlChangesSub.closed) {
      this.controlChangesSub.unsubscribe();
    }
    this.onChange = null;
    this.onTouched = null;
    this._text = null;
    this.valueFormControl.setValue(null);
    this._avatarValue = null;
    this._avatarBackgroundColor = null;
    this._avatarInitialsColor = null;
    this._avatarSrc = null;
    this._destroy$.next(true);
    this._destroy$.complete();
    this._destroy$ = null;
  }

  getAvatarIcon(): string {
    if (this.avatarValue) {
      return null;
    }
    return this.avatarIcon();
  }

  onSelection(dropItem: {
    newSelection: {
      value: string | number; // <--- El tipo de dato recibido aquí debe coincidir con el de "writeValue"
    };
  }) {
    this.selectedItem.emit({ data: dropItem?.newSelection?.value });
    if (this.debug()) {
      console.log('>> onSelection:', dropItem, dropItem?.newSelection, dropItem?.newSelection?.value);
    }
    if (!dropItem || !dropItem.newSelection) {
      return;
    }
    this.isClosing = true;
    const result: TextHasValue = this.findValue(dropItem.newSelection.value);
    if (result !== null) {
      this.setControlValue(result.text);
    } else {
      this.setControlValue(null);
    }
    this.filterValid(result?.text || null);
    this.close();
    if (this.avatar() && this.miniView) {
      // Parche: Al usar la opción Enter y tab se duplica el avatar. Se limpia el componente para que al abrirlo no se cree otro avatar igual
      if (this.showMinViewTitle()) {
        this.textFormControl.patchValue(null);
        this.textInput().value = null;
        this._lastFilter = null;
      }
      this.focusAvatarDom();
    }
  }

  private focusAvatarDom(): void {
    const avatar = this.avatar();
    if (avatar && this.miniView) {
      // Parche: se posiciona el evento en el avatar para hacer foco en el siguiente elemento con TAB
      this.disabled = true;
      this.cdr.detectChanges();
      avatar.elementRef?.nativeElement.focus();
      avatar.elementRef?.nativeElement.blur();
      this.disabled = false;
      this.cdr.detectChanges();
    }
  }

  onAvatarSearchInputMousedown($event) {
    if ($event) {
      $event.preventDefault();
      $event.stopPropagation();
    }
    this.onInputMousedown($event);
  }

  onPrefixClick($event) {
    this.prefixClick.emit({
      currentValue: this.valueFormControl.value,
      event: $event
    });
  }

  onInputMousedown($event) {
    if (this.disabled || this.isOpen || (this.isMobilDevice() && this.options.length === 0)) {
      return;
    }
    if (this.debug()) {
      console.log('onInputMousedown: ', $event);
    }
    this.open(true);
  }

  onMainInputFocus($event, _elem?: HTMLInputElement | IgxAvatarComponent) {
    if ($event) {
      $event.preventDefault();
      $event.stopPropagation();
    }
    if (!this.disabled && !this.isOpen && $event.relatedTarget === null) {
      $event = {
        relatedTarget: true,
        preventDefault: () => {},
        stopPropagation: () => {}
      };
    }
    this.onInputFocus($event, _elem);
  }

  onAvatarInputFocus($event, _elem?: HTMLInputElement | IgxAvatarComponent) {
    if ($event) {
      $event.preventDefault();
      $event.stopPropagation();
    }
    if (this.debug()) {
      console.log('onAvatarInputFocus: ', $event);
    }
    this.onInputFocus($event, _elem);
  }

  onMiniViewInputFocus($event: FocusEvent, _elem?: HTMLInputElement | IgxAvatarComponent) {
    if ($event) {
      $event.preventDefault();
      $event.stopPropagation();
    }
    if (this.debug()) {
      console.log('onMiniViewInputFocus: ', $event);
    }
    this.onInputFocus($event, _elem);
  }

  onInputFocus($event: FocusEvent, _elem?: HTMLInputElement | IgxAvatarComponent) {
    if (this.debug()) {
      console.log('onInputFocus: ', $event);
    }
    if (this.disabled || this.isOpen || (!this._forceFocus && $event?.relatedTarget === null) || (this.isMobilDevice() && this.options.length === 0)) {
      // Para autoseleccionar el texto del input
      if (this.text && this.textInput().value && _elem && _elem instanceof HTMLInputElement) {
        const input = _elem as HTMLInputElement;
        this.selectText(input);
      }
      return;
    }
    if ($event) {
      $event.preventDefault();
      $event.stopPropagation();
    }
    const scrollIntoView = this.scrollIntoView();
    if (!this.miniView && scrollIntoView && _elem && _elem instanceof HTMLInputElement) {
      const input = _elem as HTMLInputElement;
      input.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'end' });
    } else if (this.miniView && scrollIntoView && _elem && _elem instanceof IgxAvatarComponent) {
      const avatar = _elem as IgxAvatarComponent;
      avatar.elementRef.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'end' });
    }
    this.inputFocused.emit($event);
    this.open(true);
  }

  selectText(input: HTMLInputElement): void {
    if (typeof input?.setSelectionRange === 'function') {
      input.setSelectionRange(0, this.text.length);
    } else {
      console.error('setSelectionRange is not a function');
    }
  }

  onAvatarInputMousedown($event) {
    if (this.debug()) {
      console.log('onAvatarInputMousedown: ', $event);
    }
    this.customAvatarFocus.emit(true);
    this.toggleOpenDropdown($event);
  }

  onBadgeClick($event) {
    if ($event) {
      $event.preventDefault();
      $event.stopPropagation();
    }
    if (this.debug()) {
      console.log('onBadgeClick: ', $event);
    }
    this.toggleOpenDropdown($event);
  }

  onArrowDownMouseDown($event) {
    if (this.debug()) {
      console.log('onArrowDownMouseDown: ', $event);
    }
    this.toggleOpenDropdown($event);
  }

  private toggleOpenDropdown($event) {
    if ($event) {
      $event.preventDefault();
      $event.stopPropagation();
    }
    if (this.disabled) {
      return;
    }
    if (this.debug()) {
      console.log('>> toggleOpenDropdown:', $event);
    }
    this._showAll = true;
    if (!this.isOpen) {
      this.open(true);
    } else {
      this.close();
    }
  }

  open(showAll = false) {
    if (this.disabled || this.isOpen) {
      return;
    }
    this.overlayService.hideAll(this);
    this.isOpen = true;
    this.isOpening = true;
    if (this.debug()) {
      console.log('>> open!', this._overlaySettings.target);
    }
    this.defineDropdownTarget();
    this.cdr.detectChanges();
    this.dropdown().clearSelection();
    this._showAll = showAll;
    this.refreshVirtualScroll();
    if (this.isScreenMedium) {
      let position: AutoPositionStrategy;
      if (this._overlaySettings.positionStrategy instanceof AutoPositionStrategy) {
        position = this._overlaySettings.positionStrategy as AutoPositionStrategy;
      } else {
        position = new AutoPositionStrategy();
        this._overlaySettings.positionStrategy = position;
      }
      this._overlaySettings.modal = false;
      this._overlaySettings.scrollStrategy = new BnextCloseScrollStrategy(this.scrollContainer());
    } else {
      let position: GlobalPositionStrategy;
      if (this._overlaySettings.positionStrategy instanceof GlobalPositionStrategy) {
        position = this._overlaySettings.positionStrategy as GlobalPositionStrategy;
      } else {
        position = new GlobalPositionStrategy();
        this._overlaySettings.positionStrategy = position;
      }
      position.settings.verticalDirection = VerticalAlignment.Middle;
      position.settings.verticalStartPoint = VerticalAlignment.Middle;
      position.settings.horizontalDirection = HorizontalAlignment.Center;
      position.settings.horizontalStartPoint = HorizontalAlignment.Center;
      this._overlaySettings.modal = true;
      this._overlaySettings.scrollStrategy = new AbsoluteScrollStrategy(this.scrollContainer());
    }
    this.dropdown().open(this._overlaySettings);
  }

  close() {
    if (this.disabled || !this.isOpen) {
      return;
    }
    this.isClosing = true;
    if (this.debug()) {
      console.log('>> close: ', this.isClosing);
    }
    this.dropdown()?.close();
  }

  private closeWithoutAnimation(): void {
    this.isOpening = false;
    this.isOpen = false;
    this.isOpened = false;
    this.cdr.detectChanges();
  }

  filterValue(): IgxAutocompleteFilter {
    return {
      term: this.currentFilter().trim(),
      originalTerm: this.currentFilter(true).trim(),
      showAll: this._showAll
    };
  }

  currentFilter(original = false): string {
    let filter = this._lastFilter || '';
    if (this.isFiltering && !this.isArrowSearch && this.control) {
      if (original) {
        filter = this.control.value || '';
      } else {
        filter = this.controlValue || '';
      }
    }
    if (this.debug()) {
      console.log('>> filter: ', filter);
    }
    return String(filter);
  }

  selectOption() {
    const options = this.dropdown().children;
    let value = null;
    const dropdown = this.dropdown();
    if (this.debug()) {
      console.log('>> selectOption: ', dropdown?.focusedItem?.itemIndex, dropdown?.focusedItem?.value);
    }
    if (options && options.length > 0) {
      if (dropdown.focusedItem && dropdown.focusedItem?.itemIndex !== -1) {
        value = dropdown.focusedItem?.value;
      } else {
        value = options.first.value;
      }
    }
    this.onSelection({ newSelection: { value: value } });
  }

  chooseDefaultOption() {
    if (!this.control) {
      this.onSelection({ newSelection: { value: null } });
      return;
    }
    const result = this.filterAutocompleteOptions(this.control.value);
    if (result !== null && result.length > 0) {
      if (result.length === 1) {
        this.onSelection({ newSelection: { value: result[0].value } });
      } else {
        this.open();
      }
    } else {
      this.onSelection({ newSelection: { value: null } });
    }
  }

  navigatePreviousOption() {
    const options = this.dropdown().children;
    if (!options || options.length === 0) {
      return;
    }
    this.dropdown().navigatePrev();
    this.setForegroundFocus();
    this.updateControlSearch();
  }

  navigateNextOption() {
    const options = this.dropdown().children;
    if (!options || options.length === 0) {
      return;
    }
    const value = this.value;
    if (value !== null) {
      const dropdown = this.dropdown();
      if (dropdown.focusedItem && dropdown.focusedItem?.itemIndex !== -1) {
        dropdown.navigateNext();
      } else {
        this.navigateOption(value);
      }
    } else {
      const dropdown = this.dropdown();
      if (dropdown.focusedItem && dropdown.focusedItem?.itemIndex === 0 && this._text === null) {
        dropdown.navigateFirst();
      } else {
        dropdown.navigateNext();
      }
    }
    this.setForegroundFocus();
    this.updateControlSearch();
  }

  setForegroundFocus() {
    const dropdown = this.dropdown();
    if (dropdown.focusedItem !== null) {
      for (const item of dropdown.items) {
        item.element.nativeElement.style.background = 'none';
      }
      dropdown.focusedItem.element.nativeElement.style.background = '#EBEDEF';
    }
  }

  navigateOption(value: string | number) {
    if (value) {
      const option = this.dropdown().children.find((item) => item.value === value);
      if (!option) {
        return;
      }
      if (this.debug()) {
        console.log('>> navigateOption -> idx: ', option.index);
      }
      this.dropdown().navigateItem(option.index);
      this.updateControlSearch();
    }
  }

  updateControlSearch() {
    const optionValue = this.options.find((option) => option.value === this.dropdown().focusedItem?.value);
    if (optionValue) {
      this.setControlValue(optionValue.text);
    }
  }

  setControlValue(text: string): void {
    if (this.debug()) {
      console.log(`>> setControlValue: "${text}"`);
    }
    this.isClosing = false;
    this._text = text?.trim() || null;
    if (this.control) {
      this.control.setValue(this._text);
      this._controlValue = removeDiacritics(this._text);
    }
    if (this.miniView) {
      this._avatarValue = this.avatarString(this._text);
      this._avatarSrc = RestApiModule.avatar(+this.value);
    }
  }

  keyUp(event: KeyboardEvent | { code: string }) {
    if (this.disabled) {
      return;
    }
    if (this.debug()) {
      console.log('>> keyUp:', event.code);
    }
    switch (event.code) {
      case 'ArrowDown':
      case 'Down':
      case 'ArrowUp':
      case 'Up':
        this.textInputDom().nativeElement.select();
        return;
      case 'NumpadEnter':
      case 'Enter':
      case 'Escape':
      case 'Esc':
        return;
      default:
        this._lastFilter = removeDiacritics(this.control.value || '');
        this._controlValue = this._lastFilter;
        this.activateFilter();
        this.dropdown().navigateFirst();
        this.refreshVirtualScroll();
        break;
    }
  }

  keyDown(event: KeyboardEvent | { code: string; key?: string }) {
    if (this.disabled) {
      return;
    }
    if (this.debug()) {
      console.log('>> keyDown:', event.code);
    }
    if (EXCLUED_OPEN_KEYS.indexOf(event.code) === -1 && !this.isOpen) {
      this.open();
    }
    this.isArrowSearch = false;
    switch (event.code) {
      case 'ArrowDown':
      case 'Down':
        this.isFiltering = false;
        this.isArrowSearch = true;
        this.navigateNextOption();
        break;
      case 'NumpadEnter':
      case 'Enter':
        this.isFiltering = true;
        this.selectOption();
        break;
      case 'ArrowUp':
      case 'Up':
        this.isFiltering = false;
        this.isArrowSearch = true;
        this.navigatePreviousOption();
        break;
      case 'Escape':
      case 'Esc':
        this.onSelection({
          newSelection: {
            value: this.valueFormControl.value
          }
        });
        break;
      default:
        this.isFiltering = true;
        break;
    }
    this._randomId = null;
  }

  onNavigateKeyboard(event: any | { relatedTarget: any }) {
    if (this.debug()) {
      console.log('>> onNavigateKeyboard:', event);
    }
    this.isOpening = false;
    this.close();
    this.focusAvatarDom();
  }

  onDropdownFilterBlur(event: FocusEvent | { relatedTarget: any }) {
    if (this.debug()) {
      console.log('>> onDropdownFilterBlur:', event.relatedTarget);
    }
    // Parche: se cierra el dropdown para hacer foco en el siguiente elemento con TAB cuando relatedTarget es null
    if (event.relatedTarget === null) {
      this.isOpening = false;
      this.close();
    } else {
      this.onBlur(event);
    }
  }

  onBlur(event: FocusEvent | { relatedTarget: any }) {
    if (this.disabled) {
      return;
    }
    if (this.debug()) {
      console.log('>> onBlur:', event.relatedTarget);
    }
    if (event.relatedTarget === null) {
      return;
    }
    this.filterValid();
    this.closeWithoutAnimation();
  }

  activateFilter() {
    if (this._showAll) {
      this._showAll = false;
    }
  }

  filterValid(singleValue?: string) {
    this.activateFilter();
    let result = this.filterAutocompleteOptions(this.currentFilter());
    if (typeof singleValue !== 'undefined') {
      result = [];
      for (const t of this.options) {
        if (t.text === singleValue || t.escapedText === singleValue) {
          result.push(t);
        }
      }
    }
    if (result.length <= 0) {
      if (this.miniView) {
        this.clear();
      } else {
        this.setValues(null);
      }
    } else if (result.length === 1) {
      this.setValues(result[0]);
    } else {
      if (this.currentFilter() !== '') {
        console.error(`
        Theres ${result.length} repeated values at ${this.id} dropdown-search field, the
        repeated value is "${result[0].text}" (only the one with value "${result[0].value}" may be used)`);
        if (this.value === null || typeof this.value === 'undefined' || this.value === '') {
          this.setValues(result[0]);
        }
      }
    }
  }

  public writeValue(value: number | string, internalCall = false): void {
    if ((!internalCall && !this._ngControl) || this._destroyed) {
      return;
    }
    let result: TextHasValue = this.findValue(value);
    if (result && result.value === this.valueFormControl.value) {
      this.invalid = false;
      this.setControlValue(result.text);
      return;
    }
    if (result) {
      this.invalid = false;
    } else {
      if (value !== null && typeof value !== 'undefined' && value !== '' && this.options.length > 0) {
        this.invalid = true;
      } else {
        this.invalid = false;
      }
      result = null;
      if (typeof this.value === 'number' && this.input?.value === this.value && this.options) {
        this.empty.emit(this);
        this._avatarValue = null;
        this._avatarBackgroundColor = null;
        this._avatarInitialsColor = null;
        this._avatarSrc = null;
      }
    }
    this.setValues(result);
  }

  onClickClear(): void {
    if (this.disabled) {
      return;
    }
    this.clear(false);
    this.open(true);
  }

  public clear(autoClose = true): void {
    if (this.disabled) {
      return;
    }
    this.invalid = false;
    this.setValues(null);
    if (autoClose) {
      this.close();
    }
    this.cleared.emit(true);
  }

  // Limpiar el campo sin remover la clase invalid
  public clearField(): void {
    if (this.disabled) {
      return;
    }
    this.setValues(null);
    this.close();
    this.cleared.emit(true);
  }

  public dropdownClear() {
    if (this.disabled) {
      return;
    }
    if (this.miniView) {
      this.clear();
    } else {
      this.invalid = false;
      this.setValues(null);
    }
  }

  protected override afterWindowResized(event: Event) {
    this.refreshVirtualScroll();
    super.afterWindowResized(event);
  }

  private refreshVirtualScroll() {
    if (!this.isOpen) {
      return;
    }
    this._onRefreshVirtualScroll.next(true);
  }

  private refreshOptionMap(): void {
    if (this._optionMap === null || typeof this._optionMap === 'undefined') {
      return;
    }
    this.options = [];
    for (const value1 of Object.keys(this._optionMap)) {
      this.options.push({
        value: value1,
        text: this._optionMap[value1]
      });
    }
    this.refreshOptions();
  }

  public refreshOptions(): void {
    if (this.options === null || typeof this.options === 'undefined') {
      return;
    }
    const options = [];
    for (const m of this.options) {
      if (m) {
        options.push(m);
      }
    }
    // Se homologan los valores a entero
    this.fixIntegerCast();
    this.setOptions(options);
    this.updateItemsScroll();
  }

  private setOptions(_options: TextHasValue[]) {
    let options: DropdownHasValue[] = [];
    if (_options?.length > 0) {
      options = _options.map((o) => {
        return Object.assign(
          {
            escapedText: removeDiacritics(o.text)
          },
          o
        );
      });
    }
    if (this.sortable()) {
      const lang = this.getLang();
      this._options = sortArrayByAttribute(options, 'escapedText', lang);
    } else {
      this._options = options;
    }
  }

  private fixIntegerCast(): boolean {
    const options = [];
    for (const item of this.options) {
      if (item) {
        options.push(item);
      }
    }
    // Homogenize values to integers
    let allIntegers = true;
    for (const item of options) {
      if (!NumberUtil.isInteger(item.value)) {
        allIntegers = false;
        break;
      }
    }
    if (allIntegers) {
      for (const item of options) {
        item.value = +item.value;
      }
      return true;
    }
    return false;
  }

  private readTargetWidth(): number {
    let element = null;
    const inputGroup = this.inputGroup();
    const labelInputDom = this.labelInputDom();
    if (inputGroup?.element?.nativeElement) {
      element = inputGroup?.element?.nativeElement;
    } else if (labelInputDom?.nativeElement) {
      element = labelInputDom?.nativeElement;
    }
    if (!element) {
      return 0;
    }
    const offsetWidth = element?.offsetWidth;
    if (offsetWidth && +offsetWidth > 0) {
      return +offsetWidth;
    }
    return 0;
  }

  private defineScrollSize() {
    if (!this.isOpen) {
      return;
    }
    const isScreenMedium = this.isScreenMedium;
    let newMinimumWidth = this.optionMiniumumWidth;
    if (isScreenMedium) {
      if (this.itemMinWidth && this.itemMinWidth > this.optionMiniumumWidth) {
        newMinimumWidth = this.itemMinWidth;
      } else if (newMinimumWidth < DROPDOWN_MIN_WIDTH_PX) {
        newMinimumWidth = DROPDOWN_MIN_WIDTH_PX;
      }
    } else {
      newMinimumWidth = this.windowInnerWidth;
    }
    if (newMinimumWidth > this.windowInnerWidth) {
      newMinimumWidth = this.windowInnerWidth;
    }
    const filteredOptions = this.filterOptions();
    const targetWidth = this.readTargetWidth();
    if (targetWidth > 0 && newMinimumWidth < targetWidth) {
      newMinimumWidth = targetWidth;
    }
    if (newMinimumWidth !== this.optionMiniumumWidth) {
      this.optionMiniumumWidth = newMinimumWidth;
    }
    let newMaxHeight: number = this.itemsMaxHeight;
    if (filteredOptions && filteredOptions.length > 0) {
      const newHeight = filteredOptions.length * DROPDOWN_DEFAULT_ITEM_HEIGHT_PX;
      if (newHeight <= newMaxHeight) {
        newMaxHeight = newHeight;
      } else if (isScreenMedium) {
        newMaxHeight = DROPDOWN_DEFAULT_HEIGHT_PX;
      } else {
        newMaxHeight = this.windowInnerHeight;
      }
    } else if (isScreenMedium) {
      newMaxHeight = DROPDOWN_DEFAULT_ITEM_HEIGHT_PX;
    } else {
      newMaxHeight = this.windowInnerHeight;
    }

    if (newMaxHeight > this.windowInnerHeight) {
      newMaxHeight = this.windowInnerHeight;
    }
    if (newMaxHeight !== this.itemsMaxHeight) {
      this.itemsMaxHeight = newMaxHeight;
    }
    this.detectChangesScroll();
  }

  public updateItemsScroll() {
    const options = this.options;
    if (options && options.length > 0) {
      if (this.debug()) {
        console.log('updateItemsScroll options len', options.length);
      }
      let maxLength = 0;
      // No cambiar a funcional optimización de consumo de memoria para arreglos grandes
      for (const item of options) {
        if (item.text) {
          const len = item.text.trim().length;
          if (len > maxLength) {
            maxLength = len;
          }
        }
      }
      this.itemMinWidth = DROPDOWN_DEFAULT_SCROLL_WIDTH_PX + maxLength * DROPDOWN_FONT_CHAR_HEIGHT_PX;
    }
    this.refreshVirtualScroll();
  }

  private filterAutocompleteOptions(text?: string): DropdownHasValue[] {
    let f: IgxAutocompleteFilter = this.filterValue();
    if (text) {
      f = {
        term: this.currentFilter().trim(),
        originalTerm: this.currentFilter(true).trim(),
        showAll: this._showAll
      };
    }
    const filter = new IgxAutocompletePipeContains();
    return filter.transform(this.options, f);
  }

  private filterBlackList(values: DropdownHasValue[]): DropdownHasValue[] {
    const filter = new BlackListPipe();
    return filter.transform(values, this.blacklistValues);
  }

  private filterOptions(): any[] {
    const values = this.filterAutocompleteOptions();
    return this.filterBlackList(values);
  }

  private detectChangesScroll() {
    const virtDirRemote = this.virtDirRemote();
    if (virtDirRemote) {
      virtDirRemote.igxForItemSize = DROPDOWN_DEFAULT_ITEM_HEIGHT_PX;
      virtDirRemote.cdr.detectChanges();
    }
  }

  private refreshDropdownWidth(): void {
    const dropdownWidth = this.dropdownWidth();
    if (dropdownWidth) {
      this._dropdownVirtualStyle.width = `${dropdownWidth}px`;
      this._dropdownVirtualStyle['max-width'] = `${dropdownWidth}px`;
      this._dropdownVirtualStyle['min-width'] = `${dropdownWidth}px`;
    } else {
      this._dropdownVirtualStyle.width = undefined;
      this._dropdownVirtualStyle['max-width'] = undefined;
      this._dropdownVirtualStyle['min-width'] = `${this.optionMiniumumWidth}px`;
    }
  }

  private refreshInput(): void {
    if (this.input === null) {
      return;
    }
    if ((!this.input.options || (!this.input.options.length && !this.input.resetOnEmpty)) && typeof this.input.defaultValue === 'undefined') {
      return;
    }
    let newOptions: any[];
    if (
      this.removeDuplicates() &&
      this.input.options.length > 0 &&
      typeof (this.input.options[0] as TextHasValue).text !== 'undefined' &&
      typeof (this.input.options[0] as TextHasValue).value !== 'undefined'
    ) {
      newOptions = [];
      for (let idx = 0, l = this.input.options.length; idx < l; idx++) {
        const item = (this.input.options as TextHasValue[])[idx];
        if (item.text) {
          // Los valores de texto repetidos se les agrega un número
          const repeatedIdx = (this.input.options as TextHasValue[]).findIndex((tv, i) => item.text === tv.text && i !== idx);
          if (repeatedIdx !== -1) {
            // Se agrega el orden 1, 2, 3, etc. en el que existen en el listado
            const firstIdx = (this.input.options as TextHasValue[]).findIndex((tv) => item.text === tv.text);
            if (firstIdx === repeatedIdx) {
              item.text = `${item.text} (${idx - firstIdx})`;
            } else {
              // El "firstIdx" nunca se modifica para poder restarlo y sacar el indice correcto
              (this.input.options as TextHasValue[])[repeatedIdx].text = `${item.text} (${repeatedIdx - firstIdx})`;
            }
          }
          newOptions.push(item);
        }
      }
    } else if (
      this.input.options.length > 0 &&
      typeof (this.input.options[0] as TextHasValue).text !== 'undefined' &&
      typeof (this.input.options[0] as TextHasValue).value !== 'undefined'
    ) {
      newOptions = [];
      for (const t of this.input.options as TextHasValue[]) {
        if (t.text) {
          newOptions.push(t);
        }
      }
    } else if (!this.input.resetOnEmpty && this.input.options.length > 0) {
      newOptions = (this.input.options as string[]).map((value) => ({ text: String(value), value: value }));
    } else {
      newOptions = [];
    }
    if (this.input.value !== null && (Array.isArray(this.input.value) || typeof this.input.value === 'object')) {
      throw Error("DropdownSearch doesn't support multiple values");
    }
    if (this.input.defaultValue && !newOptions.find((tv) => tv.value === this.input.defaultValue.value)) {
      newOptions.push(this.input.defaultValue);
    }
    if (!equalsObject(newOptions, this.options)) {
      this.options = newOptions;
      this.refreshOptions();
    }
    const newValue = (this.input.value as any) || null;
    if (!this.isArrowSearch && !equalsObject(this.value, newValue)) {
      const valueShoudBeNumber = this.fixIntegerCast();
      const valueIsNumber = NumberUtil.isNumber(newValue);
      if (valueShoudBeNumber && valueIsNumber) {
        this.value = +newValue;
      } else if (valueShoudBeNumber) {
        console.error(`Invalid value, the value ${this.valueFormControl.value} is not a valid option`);
        this.value = null;
      } else {
        this.value = newValue;
      }
      this.writeValue(this.value, true);
    }
  }

  private findValue(val: number | string): TextHasValue {
    let result: TextHasValue;
    let value = val;
    if (NumberUtil.isInteger(value)) {
      value = +value;
    } else if (value) {
      const valueTextValue = value as unknown as TextHasValue;
      if (valueTextValue?.value) {
        value = valueTextValue?.value.toString();
      } else {
        value = value.toString();
      }
    } else {
      value = null;
    }
    if (NumberUtil.isInteger(value) && this.options.length && NumberUtil.isInteger(this.options[0].value) && typeof this.options[0].value !== typeof value) {
      console.warn('Unnecesary TextHasValue overhead! number is expected but found string instead.');
      result = this.options.find((t) => +t.value === +value);
      if (result && result.text === '' && result.value === '') {
        return null;
      }
    } else {
      result = this.options.find((t) => t.value === value);
    }
    return result || null;
  }

  private setValues(o: TextHasValue): void {
    if (this._destroyed) {
      console.error(`Component dropdown-sarch[${this.id}] is destroyed, call to setValues is not allowed!`);
      return;
    }
    const debug = this.debug();
    if (debug) {
      console.log('>> setValues:', o, ' >> changed from', this.valueFormControl.value, ' to ', o?.value, '. emit: ', o);
    }
    let text = null;
    let value = null;
    if (o != null) {
      text = o.text;
      value = o.value;
    }
    const textInput = this.textInput();
    if (this._text !== text || (textInput && textInput.value !== text)) {
      this.setControlValue(text);
    }
    this._randomId = null;
    const sameValue = equalsObject(this.valueFormControl.value, value);
    if (!sameValue) {
      if (debug) {
        console.log('>> changed from', this.valueFormControl.value, ' to ', value, '. emit: ', o);
      }
      this.valueFormControl.setValue(value);
      this._text = text;
      this._avatarValue = this.avatarString(text);
      this._avatarBackgroundColor = this.stringToColour(text);
      this._avatarInitialsColor = this.textColor();
      this._avatarSrc = RestApiModule.avatar(value);
      if (typeof this.onChange === 'function') {
        this.onChange(this.valueFormControl.value);
      }
      if (typeof this.onTouched === 'function') {
        this.onTouched(this.valueFormControl.value);
      }
      this.changed.emit(o);
    }
    const formGroup = this.formGroup();
    const name = this.name();
    if (formGroup?.get(name)) {
      formGroup.get(name).patchValue(this.valueFormControl.value || null, { onlySelf: true, emitModelToViewChange: false });
      formGroup.get(name).updateValueAndValidity({ onlySelf: true });
    }
  }

  private avatarString(text: string) {
    return nameInitials(text, '').toUpperCase() || null;
  }

  private stringToColour(str: string): string {
    return stringToColour(str);
  }

  private textColor(hexColor = this._avatarBackgroundColor): string {
    return textColor(hexColor, 150);
  }

  public registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  autocompleteDisable(): string {
    if (this._randomId == null) {
      this._randomId = `dropdown-search-${this.autoCompleteDisableId}`;
    }
    return this._randomId;
  }

  onOpening() {
    if (this.debug()) {
      console.log('>> onOpening!', this.value);
    }
  }

  onOpened() {
    if (this.debug()) {
      console.log('>> onOpened!', this.value);
    }
    if (this.miniView && this.avatar()) {
      if (this.scrollIntoView()) {
        this.textInput().focus();
      } else {
        DomUtil.focusWithNoScroll(this.textInput());
      }
    }
    this.isOpened = true;
    this.isOpening = false;
    this.cdr.detectChanges();
    this.detectChangesScroll();
    this.navigateOption(this.value || null);
    this.refreshVirtualScroll();
    this.opened.emit(true);
  }

  onClosed() {
    if (this.debug()) {
      console.log('>> onClosed!', this.value);
    }
    this.closed.emit(true);
    this.isOpened = false;
    this.isOpening = false;
    this.isOpen = false;
    // Parche: Al usar la opción Shift-Tab se duplica el avatar. Se limpia el componente para que al abrirlo no se cree otro avatar igual
    if (this.avatar() && this.miniView) {
      if (this.showMinViewTitle()) {
        this.textFormControl.patchValue(null);
        this.textInput().value = null;
        this._lastFilter = null;
      }
    }
  }

  onClosing($event) {
    if (this.debug()) {
      console.log('>> onClosing!', this.value);
    }
    if (!this.isOpened) {
      $event.cancel = true;
      return;
    }
    if (!this.isClosing) {
      this.filterValid();
    }
    this.isClosing = false;
  }
}
