<div class="comment-multi-save-container">
  <app-grid-multi-select
    class="grid-comment-multi-save"
    #gridSelect
    name="comment-multi-save"
    [id]="id"
    [required]="required"
    [inputType]="2"
    [displayDensity]="displayDensity"
    [saveButton]="saveButton()"
    (dialogTextInputClose)="clearDropdownItem()"
    (dialogTextInputOpen)="clearDropdownItem()"
    [clickToCloseTextLabel]="clickToCloseTextLabel"
    [clickToSaveTextLabel]="clickToSaveTextLabel"
    [label]="showTitle() ? label : ''"
    [placeholder]="placeholder"
    [dialogTitle]="dialogTitle"
    [hideEmpty]="true"
    [help]="help()"
    [dataUrl]="dataUrl()"
    [showHelp]="showHelp()"
    [valueColumns]="columns"
    [dataColumns]="columns"
    (change)="valueChanged()"
    (inputSave)="saveSingleClick()"
    (rowSelectionChange)="onRowSelectionChange($event)"
    (showDetalilCell)="clickOnShowDetalil($event)"
    [busy]="saving()"
    [showPagingGreaterRows]="showPagingGreaterRows()"
    [allowsAuthorDelete]="allowsAuthorDelete()"
  >
  </app-grid-multi-select>
</div>
@if (dialogDetailOpen) {
  <igx-dialog #detailDialog [closeOnOutsideSelect]="true" [positionSettings]="positionSettings" (leftButtonSelect)="detailDialog.close()">
    <igx-dialog-title>
      <div class="title-container">
        <div class="dialog-title">{{ dialogTitle }}</div>
        <igx-icon (click)="detailDialog.close()">close</igx-icon>
      </div>
    </igx-dialog-title>
    <div class="detail-comment">
      <span>{{ comment }}</span>
    </div>
  </igx-dialog>
}
