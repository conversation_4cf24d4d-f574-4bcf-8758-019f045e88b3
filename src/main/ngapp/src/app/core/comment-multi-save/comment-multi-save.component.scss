@use 'src/styles/mediaQueries' as *;
@use 'src/styles/immutable-colors' as *;

::ng-deep {
  .comment-multi-save-container .main-grid-component-container {
    padding-left: 0px;
    padding-right: 0px;
  }

  .grid-comment-multi-save.grid-base-select-container--filled > .grid-base-select-container {
    padding-top: 1rem;
  }

  .igx-input-group.text-input-dialog {
    .igx-input-group__bundle {
      background-color: rgba($fg-color, 0.08);

      .igx-input-group__textarea.comment-textarea {
        padding-left: 0.5rem;
      }
    }
    textarea {
      min-height: 12rem;
    }
  }

  /* responsividad extra-small */
  @media print, screen and (max-width: $xsmall) {
    .igx-input-group.text-input-dialog {
      min-width: 20rem;
    }
  }
}
.title-container {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.detail-comment {
  max-width: 70rem;

  > span {
    white-space: pre-wrap;
  }
}
/* responsividad extra-small */
@media print, screen and (max-width: $xsmall) {
  .detail-comment {
    padding: 1rem;
    margin: auto;
    max-height: 100%;
    overflow: auto;
  }
}
