import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MockCoreModule } from 'src/app/core/test/mock-core.module';
import { DropFileHandlerComponent } from './drop-file.component';

import { MockProviders } from '../test/mock-providers';
import { configureTestSuite } from '../test/utils/configure-test-suite';

describe('DropFileHandlerComponent', () => {
  let component: DropFileHandlerComponent;
  let fixture: ComponentFixture<DropFileHandlerComponent>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule, DropFileHandlerComponent],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DropFileHandlerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
