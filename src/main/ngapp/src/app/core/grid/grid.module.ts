import { Date<PERSON>ip<PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { NgModule } from '@angular/core';

import {
  IgxActionStripModule,
  IgxAvatarComponent,
  IgxBadgeComponent,
  IgxButtonDirective,
  IgxDialogModule,
  IgxDragDropModule,
  IgxDropDownComponent,
  IgxDropDownItemComponent,
  IgxGridModule,
  IgxIconButtonDirective,
  IgxIconComponent,
  IgxInputGroupModule,
  IgxProgressBarModule,
  IgxRippleDirective,
  IgxSwitchComponent,
  IgxToggleActionDirective,
  IgxToggleDirective,
  IgxTreeGridModule
} from '@infragistics/igniteui-angular';

import { ToolbarMonthlySelectorComponent } from '@/core/toolbar-monthly-selector/toolbar-monthly-selector.component';
import { ToolbarYearlySelectorComponent } from '@/core/toolbar-yearly-selector/toolbar-yearly-selector.component';
import { HammerModule } from '@angular/platform-browser';
import { DxTooltipModule } from 'devextreme-angular/ui/tooltip';
import { ComboComponent } from '../combo/combo.component';
import { DropdownMenuComponent } from '../dropdown-menu/dropdown-menu.component';
import { DropdownSearchComponent } from '../dropdown-search/dropdown-search.component';
import { BnextTranslateModule } from '../i18n/bnext-translate.module';
import { ToolbarWeeklySelectorComponent } from '../toolbar-weekly-selector/toolbar-weekly-selector.component';
import { GridExportComponent } from './grid-export/grid-export.component';
import { GridImportComponent } from './grid-import/grid-import.component';
import { GridSearchComponent } from './grid-search/grid-search.component';

const exports = [
  BnextTranslateModule,
  ComboComponent,
  NgClass,
  NgTemplateOutlet,
  DropdownMenuComponent,
  DropdownSearchComponent,
  DxTooltipModule,
  GridExportComponent,
  GridImportComponent,
  GridSearchComponent,
  HammerModule,
  IgxActionStripModule,
  IgxAvatarComponent,
  IgxBadgeComponent,
  IgxToggleActionDirective,
  IgxButtonDirective,
  IgxDropDownItemComponent,
  IgxDialogModule,
  IgxDragDropModule,
  IgxIconButtonDirective,
  IgxDropDownComponent,
  IgxGridModule,
  IgxIconComponent,
  IgxInputGroupModule,
  IgxProgressBarModule,
  IgxRippleDirective,
  IgxSwitchComponent,
  IgxToggleDirective,
  IgxTreeGridModule,
  ToolbarWeeklySelectorComponent,
  ToolbarMonthlySelectorComponent,
  ToolbarYearlySelectorComponent
];

@NgModule({
  imports: [...exports],
  exports: [...exports],
  providers: [DatePipe]
})
export class GridModule {}
