import { BnextCoreComponent } from '@/core/bnext-core.component';
import { ComboComponent } from '@/core/combo/combo.component';
import type { DropdownButtonItem } from '@/core/dropdown-button/dropdown-button.interfaces';
import { CatalogHierarchyComponent } from '@/core/external-catalog/catalog-hierarchy/catalog-hierarchy.component';
import { HierarchyChipComponent } from '@/core/external-catalog/hierarchy-chip/hierarchy-chip.component';
import type { CatalogGridChange } from '@/core/external-catalog/utils/external-catalog.interfaces';
import type { HierarchyValueEntity } from '@/core/external-catalog/utils/hierarchy.interfaces';
import { GridDataType } from '@/core/grid/utils/grid-data-type';
import type { IGridFilteringExpression } from '@/core/grid/utils/grid-filtering-expression';
import type { GridSearchFilterChips } from '@/core/grid/utils/grid.interfaces';
import type { BnextComponentPath } from '@/core/i18n/bnext-component-path';
import { BnextTranslateModule } from '@/core/i18n/bnext-translate.module';
import { GridLocalConfig } from '@/core/indexed-db/grid-local-config';
import { SelectComponent } from '@/core/select/select.component';
import type { BnextElementPath } from '@/core/utils/bnext-element-path';
import type { DataMap } from '@/core/utils/data-map';
import * as DateUtil from '@/core/utils/date-util';
import * as NumberUtil from '@/core/utils/number-util';
import { CommonModule } from '@angular/common';
import { Component, type ElementRef, Input, type OnDestroy, type OnInit, inject, input, output, viewChild, viewChildren } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormControl, type UntypedFormGroup } from '@angular/forms';
import { HammerModule } from '@angular/platform-browser';
import { greaterThanOrEqual, lessThanOrEqual } from '@igniteui/material-icons-extended';
import {
  FilteringExpressionsTree,
  FilteringLogic,
  GridColumnDataType,
  type IFilteringExpression,
  type IFilteringExpressionsTree,
  type IFilteringOperation,
  type ISearchInfo,
  IgxButtonModule,
  IgxChipsModule,
  IgxDateFilteringOperand,
  IgxDatePickerModule,
  type IgxDateRangePickerComponent,
  IgxDateRangePickerModule,
  type IgxDialogComponent,
  IgxDialogModule,
  IgxDividerModule,
  IgxFilterCellTemplateDirective,
  IgxIconModule,
  IgxIconService,
  type IgxInputGroupComponent,
  IgxInputGroupModule,
  IgxNumberFilteringOperand,
  IgxRippleModule,
  IgxStringFilteringOperand,
  type IgxTimePickerComponent,
  IgxTimePickerModule,
  PickerInteractionMode
} from '@infragistics/igniteui-angular';
import { Subject, type Subscription } from 'rxjs';
import { debounceTime, takeUntil, throttleTime } from 'rxjs/operators';
import type { GridI18n } from '../utils/grid-i18n';

import { DropdownButtonComponent } from '@/core/dropdown-button/dropdown-button.component';
import { configureFilteringTreeForHierarchy } from '@/core/external-catalog/utils/external-search-util';
import { isMobileDevice } from '@/core/utils/DeviceUtil';
import { setFocus } from '@/core/utils/focus-util';
import { cloneObject, shallowClone } from '@/core/utils/object';
import { LocalStorageItem } from '../../local-storage/local-storage-enums';
import { LocalStorageSession } from '../../local-storage/local-storage-session';
import type { GridIcon } from '../utils/grid-base.interfaces';
import type { GridColumn } from '../utils/grid-column';
import { GridClearType, type GridSearchChange, GridSearchSide, GridSearchType, type IChangeFilterConfig } from './grid-search.interfaces';

@Component({
  selector: 'app-grid-search',
  imports: [
    BnextTranslateModule,
    ComboComponent,
    CommonModule,
    DropdownButtonComponent,
    FormsModule,
    HammerModule,
    IgxButtonModule,
    IgxChipsModule,
    IgxDatePickerModule,
    IgxDateRangePickerModule,
    IgxDividerModule,
    IgxIconModule,
    HierarchyChipComponent,
    IgxInputGroupModule,
    IgxRippleModule,
    IgxTimePickerModule,
    ReactiveFormsModule,
    SelectComponent,
    CatalogHierarchyComponent,
    IgxDialogModule,
    IgxFilterCellTemplateDirective
  ],
  templateUrl: './grid-search.component.html',
  styleUrls: ['./grid-search.component.scss']
})
export class GridSearchComponent<DataRowType = DataMap<any>> extends BnextCoreComponent implements OnDestroy, BnextElementPath, OnInit {
  private formBuilder = inject(UntypedFormBuilder);
  private iconService = inject(IgxIconService);

  public static LANG_CONFIG: BnextComponentPath = {
    componentPath: 'core.grid.grid-search',
    componentName: 'app-grid-search'
  };
  private _supportedGlobalFilterColumns = [
    GridDataType.ITEMS,
    GridDataType.TEXT_ITEMS,
    GridDataType.TEXT_MULTIPLE_ITEMS,
    GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS,
    GridDataType.LONG_ITEMS,
    GridDataType.BOOLEAN,
    GridDataType.NUMBER_BOOLEAN,
    GridDataType.ICONS,
    GridDataType.TEXT,
    GridDataType.CATALOG_HIERARCHY,
    GridDataType.DOUBLE,
    GridDataType.INTEGER,
    GridDataType.LONG,
    GridDataType.TIME,
    GridDataType.DATE,
    GridDataType.TIMESTAMP,
    GridDataType.BOOLEAN_ICONS,
    GridDataType.PERCENTAGE
  ];
  #searchText = new Subject<any>();
  private destroyCurrentLocalSearch = new Subject<any>();
  #subscriptionsOnDestroy: Subscription[] = [];
  #columnFilters: DataMap<IGridFilteringExpression> = {};
  private _onChangedSearch = new Subject<IChangeFilterConfig>();
  #columnOperations: DataMap<IFilteringOperation | IFilteringOperation[]> = {};
  #columnMap: DataMap<GridColumn<DataRowType>> = {};
  #columns: GridColumn<DataRowType>[] = [];
  #searchColumns: GridColumn<DataRowType>[] = [];
  public isMobileDevice = null;
  public isPwaInstalled = null;
  protected settingsForm: UntypedFormGroup;

  protected filterValues: DataMap<GridSearchFilterChips<DataRowType>> = {};
  protected timePickerMode: PickerInteractionMode = PickerInteractionMode.DropDown;
  protected EnumGridDataType = GridDataType;
  protected EnumGridSearchSide = GridSearchSide;
  protected customFilteringOpen = false;
  protected chipItems: GridSearchFilterChips<DataRowType>[] = [];
  protected clearItems: DropdownButtonItem[] = [
    { value: GridClearType.CLEAR_ONLY, text: 'clear_button' },
    { value: GridClearType.CLEAR_AND_APPLY, text: 'delete_filters_button' }
  ];

  private valueParseCache: DataMap<any> = {}; // Guarda cahé de las búsqueda

  public readonly urlAvailable = input(true);

  public readonly persistState = input(true);

  public readonly hidden = input(false);

  public readonly searchBusy = input(false);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public caseSensitive = false;

  public readonly searchLabel = input('');

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  public lastSearchInfo: ISearchInfo;

  public readonly name = input<any>(undefined);

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set columns(val: GridColumn<DataRowType>[]) {
    let value = val;
    if (!value) {
      return;
    }
    // Arreglo original de columnas
    this.#columns = value;
    value = shallowClone(value);
    // Se copia arreglo original de columnas para que el sort no mute el orden original
    if (value.length > 13) {
      this.#searchColumns = shallowClone(value).sort((a: GridColumn<DataRowType>, b: GridColumn<DataRowType>) => a.header.localeCompare(b.header));
    } else {
      this.#searchColumns = value;
    }
    const filtersToShow = LocalStorageSession.getValue(LocalStorageItem.FILTERS_TO_SHOW, this.name()) || (value.length > 50 ? 50 : value.length);
    if (filtersToShow) {
      this.#searchColumns = value.slice(0, +filtersToShow);
    }
    this.#columnMap = {};
    for (const column of this.#searchColumns) {
      this.#columnMap[column.field] = column;
      this.#columnFilters[column.field] = this.buildFilterGlobalOperand(column);
    }
  }

  public get columns() {
    return this.#columns;
  }

  public override get componentPath(): string {
    return GridSearchComponent.LANG_CONFIG.componentPath;
  }

  public get customTagName(): string {
    return GridSearchComponent.LANG_CONFIG.componentName;
  }

  public override get tagName(): string {
    return GridSearchComponent.LANG_CONFIG.componentName;
  }

  protected get searchColumns() {
    return this.#searchColumns;
  }

  protected get searchText(): string {
    return this.searchInput()?.nativeElement.value || '';
  }

  public get searchValueSafe(): string {
    let value = this?.searchText;
    if (value === null || typeof value === 'undefined') {
      value = '';
    } else {
      if (this.caseSensitive) {
        value = value.trim();
      } else {
        value = value.toLowerCase().trim();
      }
    }
    return value;
  }

  readonly dialogSettingsFilter = viewChild<IgxDialogComponent>('dialogSettingsFilter');
  readonly searchInput = viewChild<ElementRef>('searchInput');
  readonly comboFields = viewChildren<ComboComponent>('comboSearch');
  readonly hierarchyFields = viewChildren<CatalogHierarchyComponent>('hierarchySearch');
  readonly selectFields = viewChildren<SelectComponent>('selectSearch');
  readonly inputFields = viewChildren<IgxInputGroupComponent>('inputSearch');
  readonly dateFields = viewChildren<IgxDateRangePickerComponent>('dateSearch');

  // TODO: Skipped for migration because:
  //  This input overrides a field from a superclass, while the superclass field
  //  is not migrated.
  @Input()
  public i18n: GridI18n = {};

  public readonly changeFilter = output<GridSearchChange<DataRowType>>();
  public readonly navigatePrev = output<GridSearchChange<DataRowType>>();
  public readonly navigateNext = output<GridSearchChange<DataRowType>>();
  public readonly clearFilter = output<boolean>();
  public readonly clearSearchGlobalFilters = output<boolean>();

  #formGroups: DataMap<UntypedFormGroup> = {};

  public constructor() {
    super();

    const subs = this._onChangedSearch
      .pipe(throttleTime(500, undefined, { leading: true, trailing: true }), takeUntil(this.$destroy))
      .subscribe((config) => this.executeSearch(config));
    this.#subscriptionsOnDestroy.push(subs);
    this.buildOperationsCache();
  }

  public override ngOnInit(): void {
    this.iconService.addSvgIconFromText('after', greaterThanOrEqual.value, 'imx-icons');
    this.iconService.addSvgIconFromText('before', lessThanOrEqual.value, 'imx-icons');
    this.subscribeLocalSearchService();
    this.isMobileDevice = isMobileDevice();
    this.settingsForm = this.formBuilder.group({
      filtersToShow: new UntypedFormControl(null)
    });
    this.setDefaultFilters();
    this.initializeClearItems();
  }

  public setDefaultFilters() {
    const persistState = this.persistState();
    if (
      ((persistState && !GridLocalConfig.hasLocalValue(this.name())) || !persistState) &&
      this.searchColumns.filter((c) => {
        if (c.defaultFilterValue) {
          this.setFilterInputValue(c, c.defaultFilterValue);
          return true;
        }
        return false;
      }).length > 0
    ) {
      this.applySearchFieldsFilter(false);
    }
  }

  public override ngOnDestroy(): void {
    for (const subs of this.#subscriptionsOnDestroy) {
      if (subs && !subs.closed) {
        subs.unsubscribe();
      }
    }
    this.$destroy.next(true);
    this.$destroy.complete();
  }

  public updateLastSearchConfig(lastSearchConfig: GridSearchChange<DataRowType>): GridSearchChange<DataRowType> {
    if (lastSearchConfig?.filterValues) {
      for (const key in lastSearchConfig.filterValues) {
        if (!lastSearchConfig.filterValues.hasOwnProperty(key)) {
          continue;
        }
        const value = lastSearchConfig.filterValues[key]?.value;
        if (this.filterValues[key]) {
          this.filterValues[key].value = value;
        } else if (key.match(/^startTime/)) {
          if (!this.#columnMap[key.replace(/^startTime/, '')]) {
            continue;
          }
          this.filterValues[key] = {
            iconName: 'after',
            column: this.#columnMap[key.replace(/^startTime/, '')],
            value: value
          };
        } else if (key.match(/^endTime/)) {
          if (!this.#columnMap[key.replace(/^endTime/, '')]) {
            continue;
          }
          this.filterValues[key] = {
            iconName: 'before',
            column: this.#columnMap[key.replace(/^endTime/, '')],
            value: value
          };
        } else {
          const column = this.#columnMap[key];
          if (!column) {
            continue;
          }
          if ((column.type === GridDataType.DATE || column.type === GridDataType.TIMESTAMP) && value) {
            value.start = DateUtil.parse(value.start);
            value.end = DateUtil.parse(value.end);
          }
          this.filterValues[key] = {
            column: column,
            value: value
          };
        }
      }
      return this.getServerSideFilterBySearchFields();
    }
    return null;
  }

  private buildFilterGlobalOperand(column: GridColumn<DataRowType>): IGridFilteringExpression {
    const operation = this.getOperationFilterGlobal(column);
    if (!operation) {
      return null;
    }
    const operand: IGridFilteringExpression = {
      fieldName: column.field,
      isCustomFiltering: true,
      filterType: null, // <-- solo se debe settear si se quiere hacer override del default
      searchVal: null,
      ignoreCase: false,
      condition: {
        name: column.field,
        isUnary: true,
        iconName: operation.iconName,
        logic: (target: any, value?: any, ignCase?: boolean, fieldName?: string) => {
          return this.filterGlobalOperationLogic(target, value, ignCase, fieldName);
        }
      },
      likeOrSentence: false
    };
    if (column.dynamicFieldType) {
      operand.filterType = 'dynamicFieldCriteria';
    }
    return operand;
  }

  private buildOperationsCache() {
    this.#columnOperations = {};
    this.#columnOperations['date-equals'] = IgxDateFilteringOperand.instance().condition('equals');
    this.#columnOperations['date-range'] = [IgxDateFilteringOperand.instance().condition('after'), IgxDateFilteringOperand.instance().condition('before')];
    this.#columnOperations['number-in'] = IgxNumberFilteringOperand.instance().condition('in');
    this.#columnOperations['number-equals'] = IgxNumberFilteringOperand.instance().condition('equals');
    this.#columnOperations['string-contains'] = IgxStringFilteringOperand.instance().condition('contains');
    this.#columnOperations['string-in'] = IgxStringFilteringOperand.instance().condition('in');
  }

  private isSupportedGlobalFilter(column: GridColumn<DataRowType>): boolean {
    return this._supportedGlobalFilterColumns.indexOf(column.type) !== -1;
  }

  private getOperationFilterGlobal(column: GridColumn<DataRowType>): IFilteringOperation {
    switch (column.type) {
      case GridDataType.LONG_ITEMS:
      case GridDataType.ITEMS: {
        const itemExpression = this.#columnOperations['number-in'];
        return itemExpression as IFilteringOperation;
      }
      case GridDataType.TEXT_ITEMS:
      case GridDataType.TEXT_MULTIPLE_ITEMS: {
        const textInExpression = this.#columnOperations['string-in'];
        return textInExpression as IFilteringOperation;
      }
      case GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS: {
        const textInContainsExpression = this.#columnOperations['string-contains'];
        return textInContainsExpression as IFilteringOperation;
      }
      case GridDataType.ICONS: {
        const iconExpression = this.#columnOperations['number-in'];
        return iconExpression as IFilteringOperation;
      }
      case GridDataType.TEXT: {
        const textExpression = this.#columnOperations['string-contains'];
        return textExpression as IFilteringOperation;
      }
      case GridDataType.DOUBLE:
      case GridDataType.PERCENTAGE:
      case GridDataType.INTEGER:
      case GridDataType.LONG:
      case GridDataType.BOOLEAN:
      case GridDataType.NUMBER_BOOLEAN:
      case GridDataType.BOOLEAN_ICONS: {
        const numberExpression = this.#columnOperations['number-equals'];
        return numberExpression as IFilteringOperation;
      }
      case GridDataType.TIME:
      case GridDataType.DATE:
      case GridDataType.TIMESTAMP: {
        const dateRangeExpression = this.#columnOperations['date-range'];
        return dateRangeExpression as IFilteringOperation;
      }
    }
    return null;
  }

  private parseGlobalOperationValue(searchValue: any, ignoreCase: boolean, fieldName?: string): any {
    if (!fieldName) {
      return null;
    }
    const column = this.#columnMap[fieldName];
    if (!column || !this.isFilterVisible(column) || !this.isSupportedGlobalFilter(column)) {
      return null;
    }
    switch (column.type) {
      case GridDataType.ICONS:
        return this.parseValuesGlobalByIcon(column, searchValue, ignoreCase);
      case GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS:
      case GridDataType.TEXT:
        return this.parseValueGlobalByString(searchValue);
      case GridDataType.LONG_ITEMS:
      case GridDataType.TEXT_ITEMS:
      case GridDataType.TEXT_MULTIPLE_ITEMS:
      case GridDataType.ITEMS:
        return this.parseValuesGlobalByItems(column, searchValue);
      case GridDataType.DOUBLE:
      case GridDataType.INTEGER:
      case GridDataType.LONG:
      case GridDataType.PERCENTAGE:
        return this.parseValueGlobalByNumber(searchValue);
      case GridDataType.DATE:
        return this.parseValueGlobalByDate(searchValue);
      case GridDataType.TIME:
        return this.parseValueGlobalByTime(searchValue);
      case GridDataType.TIMESTAMP:
        return this.parseValueGlobalByTimestamp(searchValue);
      case GridDataType.BOOLEAN:
      case GridDataType.NUMBER_BOOLEAN:
        return this.parseValueGlobalByBoolean(column, searchValue);
      case GridDataType.BOOLEAN_ICONS:
        return this.parseValuesGlobalByBooleanIcon(column, searchValue);
    }
  }

  private filterGlobalOperationLogic(target: any, searchValue: any, ignoreCase: boolean, fieldName?: string): boolean {
    if (!fieldName) {
      return false;
    }
    const column = this.#columnMap[fieldName];
    if (!column || !this.isFilterVisible(column) || !this.isSupportedGlobalFilter(column)) {
      return false;
    }
    if (column?.onlyFilter) {
      return true;
    }
    const operation = this.getOperationFilterGlobal(column);
    if (Array.isArray(operation)) {
      switch (operation.length) {
        case 2:
          return operation[0] && operation[1];
        default:
          return false;
      }
    }
    if (!Array.isArray(operation)) {
      return operation.logic(target, searchValue, ignoreCase);
    }
    return false;
  }

  private parseValuesGlobalByBooleanIcon(column: GridColumn<DataRowType>, searchValue: string): boolean {
    if (searchValue === '') {
      return null;
    }
    let value;
    if (column.render !== null && column.render.icons !== null) {
      for (const icon of column.render.icons) {
        if (icon.label.toLowerCase().includes(searchValue)) {
          value = icon.value;
        }
      }
      return value;
    }
    return null;
  }

  private parseValuesGlobalByIcon(column: GridColumn<DataRowType>, searchValue: string, ignoreCase: boolean): Set<number | string> {
    const iconsValue = this.getIconsByName(column, searchValue, ignoreCase);
    if (iconsValue === null || typeof iconsValue === 'undefined') {
      return null;
    }
    return iconsValue;
  }

  private parseValuesGlobalByItems(column: GridColumn<DataRowType>, searchValue: string): any {
    const value = [];
    if (column.render !== null && column.render.items !== null) {
      for (const item of column.render.items) {
        const valueKey = item[column.render.labelKey];
        if (typeof valueKey === 'string') {
          if (valueKey.toLowerCase().includes(searchValue)) {
            value.push(item[column.render.valueKey]);
          } else {
            // Not found
          }
        } else {
          // ToDo: Dar soporte al tipo de columna

          console.warn("Unsupported column item, didn't tried to find results: ", item);
        }
      }
      return value;
    }
    return null;
  }

  private parseValueGlobalByString(searchValue: string): string {
    if (searchValue === '') {
      return null;
    }
    return searchValue;
  }

  private parseValueGlobalByBoolean(column: GridColumn<DataRowType>, searchValue: string): boolean {
    if (searchValue === '') {
      return null;
    }
    let value: boolean;
    if (column.render !== null && column.render.items !== null) {
      for (const item of column.render.items) {
        if (item.label.toLowerCase() === searchValue) {
          value = item.value;
        }
      }
      return value;
    }
    return null;
  }

  private parseValueGlobalByNumber(searchValue: string): number {
    const numberValue = +searchValue;
    if (Number.isNaN(numberValue)) {
      return null;
    }
    return numberValue;
  }

  private parseValueGlobalByDate(searchValue: string): Date {
    const dateValue = this.parseDate(searchValue);
    if (!dateValue) {
      return null;
    }
    return dateValue;
  }

  private parseValueGlobalByTimestamp(searchValue: string): Date {
    const timestampValue = this.parseTimestamp(searchValue);
    if (!timestampValue) {
      return null;
    }
    return timestampValue;
  }

  private parseValueGlobalByTime(searchValue: string): Date {
    const timestampValue = this.parseTime(searchValue);
    if (!timestampValue) {
      return null;
    }
    return timestampValue;
  }

  private getSearchData(): GridSearchChange<DataRowType> {
    const filteringTree = this.filterGlobal();
    const searchValueSafe = this.searchValueSafe;
    return {
      searchText: searchValueSafe,
      caseSensitive: this.caseSensitive,
      filteringTree: filteringTree,
      filterValues: this.filterValues,
      refreshData: true,
      isGlobalSearch: true,
      isGlobalSearchByEnter: false
    };
  }

  private executeSearch(config: IChangeFilterConfig) {
    const searchValueSafe = this.searchValueSafe;
    if (searchValueSafe === '') {
      this.changeFilter.emit({
        searchText: null,
        caseSensitive: false,
        filteringTree: null,
        refreshData: config ? config.refreshData : true,
        filterValues: null,
        isGlobalSearch: true,
        isGlobalSearchByEnter: false
      });
      this.valueParseCache = {};
    } else {
      this.valueParseCache = {};
      const data = this.getSearchData();
      this.changeFilter.emit(data);
    }
  }

  private configureGlobalFilterColumn(column: GridColumn<DataRowType>, value: any, ignoreCase: boolean): IGridFilteringExpression {
    const operand = cloneObject(this.#columnFilters[column.field]);
    if (!operand) {
      console.error(`No filter configuration found for column ${column.field} and type ${column.type}.`);
      return {
        fieldName: column.field,
        isCustomFiltering: true,
        likeOrSentence: false,
        filterType: null,
        searchVal: null,
        ignoreCase: false,
        condition: null
      };
    }
    switch (column.type) {
      case GridDataType.ICONS:
      case GridDataType.LONG_ITEMS:
      case GridDataType.TEXT_MULTIPLE_ITEMS:
      case GridDataType.TEXT_ITEMS:
      case GridDataType.ITEMS:
        if (NumberUtil.isInteger(value)) {
          operand.searchVal = new Set([value]);
        } else {
          operand.searchVal = new Set(value);
        }
        operand.ignoreCase = ignoreCase;
        break;
      default:
        operand.searchVal = value;
        operand.ignoreCase = ignoreCase;
        break;
    }
    return operand;
  }

  private filterGlobal(): FilteringExpressionsTree {
    const filteringTree = new FilteringExpressionsTree(FilteringLogic.Or);
    const ignoreCase = !this.caseSensitive;
    const searchValue = this.searchValueSafe;
    for (const column1 of this.searchColumns.filter((column) => this.isFilterVisible(column) && this.isSupportedGlobalFilter(column))) {
      if (column1.searchable) {
        let columnSearchvalue: any;
        if (this.valueParseCache[column1.field]) {
          columnSearchvalue = this.valueParseCache[column1.field];
        } else {
          columnSearchvalue = this.parseGlobalOperationValue(searchValue, ignoreCase, column1.field);
          if (columnSearchvalue === null || typeof columnSearchvalue === 'undefined') {
            continue;
          }
          this.valueParseCache[column1.field] = columnSearchvalue;
        }
        if (column1.type === GridDataType.TEXT && columnSearchvalue?.indexOf(',') !== -1) {
          for (const searchVal of columnSearchvalue.split(/\s*,\s*/g)) {
            filteringTree.filteringOperands.push(this.configureGlobalFilterColumn(column1, searchVal, true));
          }
        } else {
          filteringTree.filteringOperands.push(this.configureGlobalFilterColumn(column1, columnSearchvalue, true));
        }
      }
    }
    return filteringTree;
  }

  private transformValue(value: any, column: GridColumn<DataRowType>): any {
    if (column?.type?.dataType === GridColumnDataType.Number && NumberUtil.isInteger(value)) {
      return Number.parseFloat(value);
    }
    return value;
  }

  private setFilterInputValue(column: GridColumn<DataRowType>, value: any) {
    this.setFilterInputValueByName(column.field, column, value);
  }

  private setFilterInputValueByName(fieldName: string, column: GridColumn<DataRowType>, value: any, iconName?: 'before' | 'after') {
    if (typeof this.filterValues[fieldName] === 'undefined') {
      this.filterValues[fieldName] = {
        iconName: iconName || null,
        column: column,
        value: value
      };
    } else {
      this.filterValues[fieldName].value = value;
    }
    if (value === null || `${value}`.trim() === '' || value.length === 0) {
      delete this.filterValues[fieldName];
    }
  }

  private parseDate(value: string): Date {
    return DateUtil.parseDate(value, this.translateService.currentLang);
  }

  private parseTime(value: string): Date {
    return DateUtil.parseTime(value, this.translateService.currentLang);
  }

  private parseTimestamp(value: string): Date {
    return DateUtil.parseTimestamp(value, this.translateService.currentLang);
  }

  private getIconsByName(column: GridColumn<DataRowType>, value: string, ignoreCase: boolean): Set<number | string> {
    if (value === '' || value === null || typeof value === 'undefined') {
      return null;
    }
    if (ignoreCase) {
      const filterValue = value.toLowerCase();
      return new Set(column.render.icons.filter((icon: GridIcon) => icon.label.toLowerCase().indexOf(filterValue) !== -1).map((icon: GridIcon) => icon.value));
    }
    return new Set(column.render.icons.filter((icon: GridIcon) => icon.label.indexOf(value) !== -1).map((icon: GridIcon) => icon.value));
  }

  protected onTimeEndChange(_evt: string | Date, _startPicker: IgxTimePickerComponent, column: GridColumn): void {
    if (!_evt) {
      return;
    }
    const endTime = DateUtil.safe(_evt);
    if (!_startPicker.value) {
      _startPicker.value = endTime;
    } else {
      const start = DateUtil.safe(_startPicker.value);
      if (start.getTime() > endTime.getTime()) {
        _startPicker.value = endTime;
      }
    }
    const startTime = DateUtil.safe(_startPicker.value);
    this._setTimeRange(column, startTime, endTime);
  }

  protected onTimeStartChange(_evt: string | Date, _endPicker: IgxTimePickerComponent, column: GridColumn): void {
    if (!_evt) {
      return;
    }
    const startTime = DateUtil.safe(_evt);
    if (!_endPicker.value) {
      const filterEndDate = this.filterEndTimeValue(column);
      if (filterEndDate) {
        _endPicker.value = filterEndDate;
      } else {
        _endPicker.value = startTime;
      }
    } else {
      const end = DateUtil.safe(_endPicker.value);
      if (end.getTime() < startTime.getTime()) {
        _endPicker.value = startTime;
      }
    }
    const endTime = DateUtil.safe(_endPicker.value);
    this._setTimeRange(column, startTime, endTime);
  }

  private _setTimeRange(column: GridColumn, start: Date, end: Date) {
    this.setFilterInputValueByName(`startTime${column.field}`, column, start, 'after');
    this.setFilterInputValueByName(`endTime${column.field}`, column, end, 'before');
  }

  protected onNavigatePrev() {
    if (this.searchBusy()) {
      return;
    }
    const data = this.getSearchData();
    this.navigatePrev.emit(data);
  }

  protected onNavigateNext() {
    if (this.searchBusy()) {
      return;
    }
    const data = this.getSearchData();
    this.navigateNext.emit(data);
  }

  protected clearSearch() {
    if (this.searchBusy()) {
      return;
    }
    const searchInput = this.searchInput();
    if (searchInput) {
      searchInput.nativeElement.value = '';
    }
    this.clearFilter.emit(true);
    this.clearSearchGlobalFilters.emit(true);
  }

  protected searchKeyUp(ev) {
    if (this.searchBusy()) {
      return;
    }
    if (ev.key === 'ArrowDown' || ev.key === 'ArrowRight') {
      ev.preventDefault();
      this.onNavigateNext();
    } else if (ev.key === 'ArrowUp' || ev.key === 'ArrowLeft') {
      ev.preventDefault();
      this.onNavigatePrev();
    } else if (ev.key === 'Enter' || ev.key === 'NumPadEnter') {
      this.destroyCurrentLocalSearch.next(1);
      this.applySearchTextFilter(GridSearchSide.SERVER_SIDE, null);
      this.subscribeLocalSearchService();
    } else if (this.searchValueSafe === '') {
      this.#searchText.next({ refreshData: false });
    } else {
      this.#searchText.next({ refreshData: true });
    }
  }

  private _searchKeyUpExecute(config: IChangeFilterConfig): void {
    this.applySearchTextFilter(GridSearchSide.CLIENT_SIDE, config);
  }

  private getServerSideFilterBySearchText(refresh = true): GridSearchChange<DataRowType> {
    const filteringTree = new FilteringExpressionsTree(FilteringLogic.Or);
    const searchValue = this.searchValueSafe;
    if (!this.isNumberSearchValueInput(searchValue) && !this.isDateSearchValueInput(searchValue)) {
      for (const column1 of this.searchColumns.filter(
        (column) => this.isFilterVisible(column) && this.isSupportedGlobalFilter(column) && column.type !== GridDataType.DATE && column.type !== GridDataType.DOUBLE
      )) {
        const columnSearchvalue = this.parseGlobalOperationValue(searchValue, true, column1.field);
        if (column1.type === GridDataType.TEXT) {
          const configuration = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          configuration.likeOrSentence = true;
          filteringTree.filteringOperands.push(configuration);
        } else if (
          column1.type === GridDataType.ITEMS ||
          column1.type === GridDataType.LONG_ITEMS ||
          column1.type === GridDataType.TEXT_ITEMS ||
          column1.type === GridDataType.TEXT_MULTIPLE_ITEMS
        ) {
          const configuration = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          configuration.likeOrSentence = true;
          filteringTree.filteringOperands.push(configuration);
        } else if (column1.type === GridDataType.DOUBLE || column1.type === GridDataType.PERCENTAGE) {
          const configuration = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          configuration.likeOrSentence = true;
          filteringTree.filteringOperands.push(configuration);
        } else if (column1.type === GridDataType.ICONS) {
          const configuration = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          configuration.likeOrSentence = true;
          filteringTree.filteringOperands.push(configuration);
        } else if (column1.type === GridDataType.BOOLEAN || column1.type === GridDataType.NUMBER_BOOLEAN || column1.type === GridDataType.BOOLEAN_ICONS) {
          const configuration = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          configuration.likeOrSentence = true;
          filteringTree.filteringOperands.push(configuration);
        }
      }
    } else if (this.isNumberSearchValueInput(searchValue)) {
      for (const column1 of this.searchColumns.filter(
        (column) =>
          this.isFilterVisible(column) &&
          this.isSupportedGlobalFilter(column) &&
          (column.type === GridDataType.DOUBLE || column.type === GridDataType.PERCENTAGE || column.type === GridDataType.TEXT)
      )) {
        const columnSearchvalue = this.parseGlobalOperationValue(searchValue, true, column1.field);
        if (column1.type === GridDataType.DOUBLE || column1.type === GridDataType.PERCENTAGE) {
          const configuration = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          configuration.likeOrSentence = true;
          filteringTree.filteringOperands.push(configuration);
        } else if (column1.type === GridDataType.TEXT) {
          const configuration = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          configuration.likeOrSentence = true;
          filteringTree.filteringOperands.push(configuration);
        }
      }
    } else {
      for (const column1 of this.columns.filter((column) => column.type === GridDataType.DATE)) {
        const columnSearchvalue = this.parseGlobalOperationValue(searchValue, true, column1.field);
        if (column1.type === GridDataType.DATE) {
          if (columnSearchvalue === null || typeof columnSearchvalue === 'undefined') {
            continue;
          }
          const date = this.configureGlobalFilterColumn(column1, columnSearchvalue, true);
          date.condition.iconName = 'equal';
          filteringTree.filteringOperands.push(date);
        }
      }
    }
    const searchValueSafe = this.searchValueSafe;
    return {
      searchText: searchValueSafe,
      caseSensitive: this.caseSensitive,
      filteringTree: filteringTree,
      filterValues: null,
      isGlobalSearch: false,
      refreshData: refresh,
      isGlobalSearchByEnter: true
    };
  }

  private isDateSearchValueInput(searchInput: string): boolean {
    return this.parseValueGlobalByDate(searchInput) !== null;
  }

  private isNumberSearchValueInput(searchInput: string) {
    return this.parseValueGlobalByNumber(searchInput) !== null;
  }

  protected isFilterVisible(column: GridColumn<DataRowType>) {
    return !column.hidden || column.fixedFilter || column.onlyFilter;
  }

  public updateSearch() {
    if (this.searchBusy()) {
      return;
    }
    this.caseSensitive = !this.caseSensitive;
    this.applySearchTextFilter(GridSearchSide.CLIENT_SIDE, null);
  }

  protected applyFilters(side: GridSearchSide, type: GridSearchType, config: IChangeFilterConfig, refresh = true) {
    switch (side) {
      case GridSearchSide.CLIENT_SIDE:
        if (type === GridSearchType.SEARCH_FIELDS) {
          throw Error("ClientSide search is not supporte by searchFields, try a 'searchText' instead.");
        }
        this._onChangedSearch.next(config);
        break;
      case GridSearchSide.SERVER_SIDE:
        if (this.urlAvailable()) {
          this.changeFilter.emit(this.getServerSideFilterBy(type, refresh));
        }
        break;
    }
  }

  protected applySearchFieldsFilter(refresh = true) {
    if (this.searchBusy()) {
      return;
    }
    this.applyFilters(GridSearchSide.SERVER_SIDE, GridSearchType.SEARCH_FIELDS, null, refresh);
  }

  protected applySearchTextFilter(side: GridSearchSide, config: IChangeFilterConfig) {
    this.applyFilters(side, GridSearchType.SEARCH_TEXT, config);
  }

  private getServerSideFilterBy(type: GridSearchType, refresh = true): GridSearchChange<DataRowType> {
    switch (type) {
      case GridSearchType.SEARCH_FIELDS:
        this.clearSearch();
        return this.getServerSideFilterBySearchFields(refresh);
      case GridSearchType.SEARCH_TEXT:
        this.clearFilters();
        return this.getServerSideFilterBySearchText(refresh);
    }
  }

  private getServerSideFilterBySearchFields(refresh = true): GridSearchChange<DataRowType> {
    this.chipItems = [];
    const filteringTree = new FilteringExpressionsTree(FilteringLogic.And);
    let dateRangeTree: FilteringExpressionsTree;
    let beforeTime: Date;
    let afterTime: Date;
    let beforeConfig: IFilteringExpression | IFilteringExpressionsTree;
    let afterConfig: IFilteringExpression | IFilteringExpressionsTree;
    let config: IFilteringExpression | IFilteringExpressionsTree;
    for (const key in this.filterValues) {
      if (this.filterValues.hasOwnProperty(key)) {
        const column: GridColumn<DataRowType> = this.filterValues[key].column;
        let searchValue = this.transformValue(this.filterValues[key].value, column);
        this.getFilterChipItem(column, searchValue, this.filterValues[key].iconName);
        switch (column.type) {
          case GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS:
          case GridDataType.TEXT:
            if (column.type === GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS) {
              searchValue = searchValue.join(',');
            }
            if (searchValue?.indexOf(',') !== -1) {
              const localFilteringTree = new FilteringExpressionsTree(FilteringLogic.Or);
              for (const searchVal of searchValue.split(/\s*,\s*/g)) {
                localFilteringTree.filteringOperands.push(this.configureGlobalFilterColumn(column, searchVal, true));
              }
              filteringTree.filteringOperands.push(localFilteringTree);
            } else {
              filteringTree.filteringOperands.push(this.configureGlobalFilterColumn(column, searchValue, true));
            }
            break;
          case GridDataType.CATALOG_HIERARCHY: {
            const hierarchValue: HierarchyValueEntity[] = searchValue;
            configureFilteringTreeForHierarchy(filteringTree, hierarchValue, column);
            break;
          }
          case GridDataType.DATE:
            dateRangeTree = new FilteringExpressionsTree(FilteringLogic.And);
            beforeTime = searchValue.end;
            afterTime = searchValue.start;
            beforeConfig = this.configureGlobalFilterColumn(column, beforeTime, true);
            beforeConfig.condition.iconName = 'before';
            dateRangeTree.filteringOperands.push(beforeConfig);
            afterConfig = this.configureGlobalFilterColumn(column, afterTime, true);
            afterConfig.condition.iconName = 'after';
            dateRangeTree.filteringOperands.push(afterConfig);
            filteringTree.filteringOperands.push(dateRangeTree);
            break;
          case GridDataType.TIME:
            if (!this.filterValues[key].iconName) {
              console.error(`>> Invalid TIME filter config for ${key} whit value ${searchValue}`, column);
              break;
            }
            config = this.configureGlobalFilterColumn(column, searchValue, true);
            config.condition.iconName = this.filterValues[key].iconName;
            filteringTree.filteringOperands.push(config);
            break;
          case GridDataType.LONG:
          case GridDataType.DOUBLE:
          case GridDataType.PERCENTAGE: {
            const regex = /[0-9]/;
            const result = searchValue.toString().match(regex);
            if (result !== null) {
              filteringTree.filteringOperands.push(this.configureGlobalFilterColumn(column, +searchValue, false));
            }
            break;
          }
          default:
            filteringTree.filteringOperands.push(this.configureGlobalFilterColumn(column, searchValue, true));
            break;
        }
      }
    }
    this.customFilteringOpen = false;
    return {
      searchText: null,
      caseSensitive: false,
      filteringTree: filteringTree,
      filterValues: this.filterValues,
      isGlobalSearch: false,
      refreshData: refresh,
      isGlobalSearchByEnter: false
    };
  }

  // Método que construye el objeto del chip de acuerdo al tipo de columna
  private getFilterChipItem(column: GridColumn<DataRowType>, searchValue: any, iconName?: 'before' | 'after'): void {
    switch (column.type) {
      case GridDataType.TIMESTAMP:
      case GridDataType.DATE: {
        const startDate = DateUtil.parse(searchValue.start);
        const endDate = DateUtil.parse(searchValue.end);
        this.chipItems.push({
          column: column,
          value: `
        ${DateUtil.format(startDate, this.dayJsDateFormat)}
        -
        ${DateUtil.format(endDate, this.dayJsDateFormat)}`
        });
        break;
      }
      case GridDataType.TIME:
        this.chipItems.push({
          column: column,
          value: DateUtil.format(searchValue, column.dateFormat),
          iconName: iconName || null
        });
        break;
      case GridDataType.NUMBER_BOOLEAN:
      case GridDataType.BOOLEAN:
        if (typeof searchValue === 'number') {
          const value = column.render.items[+searchValue];
          this.chipItems.push({ column: column, value: value.label });
        } else {
          const value = column.render.items.find((i) => i.value === searchValue);
          this.chipItems.push({ column: column, value: value.label });
        }
        break;
      case GridDataType.ITEMS:
      case GridDataType.LONG_ITEMS:
      case GridDataType.TEXT_ITEMS:
      case GridDataType.TEXT_MULTIPLE_ITEMS:
        if (Array.isArray(searchValue)) {
          const descriptions = [];
          for (const item of searchValue) {
            const value = column.render.items.find((i) => i[column.render.valueKey] === item);
            if (value) {
              descriptions.push(value[column.render.labelKey]);
            }
          }
          this.chipItems.push({ column: column, value: descriptions.join(' , ') });
        }
        break;
      case GridDataType.ICONS:
        if (Array.isArray(searchValue)) {
          const descriptions = [];
          for (const item of searchValue) {
            const value = column.render.icons.find((i) => i.value === item);
            descriptions.push(value.label);
          }
          this.chipItems.push({ column: column, value: descriptions.join(' , ') });
        } else {
          const value = column.render.icons.find((i) => i.value === searchValue);
          this.chipItems.push({ column: column, value: value.label });
        }
        break;
      case GridDataType.BOOLEAN_ICONS: {
        const value = column.render.icons.find((i) => i.value === searchValue);
        this.chipItems.push({ column: column, value: value.label });
        break;
      }
      default:
        this.chipItems.push({ column: column, value: searchValue });
        break;
    }
  }

  protected onClearAction(item: DropdownButtonItem) {
    switch (item?.value) {
      case GridClearType.CLEAR_AND_APPLY:
        this.deleteFilters();
        break;
      case GridClearType.CLEAR_ONLY:
        this.clearFilters();
        break;
    }
  }

  protected clearFilters() {
    for (const key in this.filterValues) {
      if (this.filterValues.hasOwnProperty(key)) {
        this.clearInput(key);
        if (this.#formGroups[key]) {
          this.#formGroups[key].setValue({ [key]: null });
        }
      }
    }
    this.chipItems = [];
  }

  // Método que remueve el filtro que corresponde al chip en el evento (remove)
  protected removeFilter(keyFilter: string) {
    if (this.searchBusy()) {
      return;
    }
    let stringSubFix = '';
    for (let key in this.filterValues) {
      if (this.filterValues.hasOwnProperty(key)) {
        if (key.startsWith('startTime')) {
          stringSubFix = 'startTime';
          key = key.replace(/^startTime/, '');
        } else if (key.startsWith('endTime')) {
          stringSubFix = 'endTime';
          key = key.replace(/^endTime/, '');
        } else {
          stringSubFix = '';
        }
        if (key === keyFilter) {
          this.clearInput(stringSubFix + key);
          if (this.#formGroups[key]) {
            this.#formGroups[key].setValue({ [key]: null });
          }
        }
      }
    }
    this.applyFilters(GridSearchSide.SERVER_SIDE, GridSearchType.SEARCH_FIELDS, null);
  }

  protected removeHierarchyFilter(keyFilter: string, value: HierarchyValueEntity[]): void {
    if (this.searchBusy()) {
      return;
    }
    for (const key in this.filterValues) {
      if (this.filterValues.hasOwnProperty(key)) {
        if (key === keyFilter) {
          if (!value?.length) {
            this.clearInput(keyFilter);
          } else {
            this.filterValues[key].value = value;
          }
        }
      }
    }
    this.applyFilters(GridSearchSide.SERVER_SIDE, GridSearchType.SEARCH_FIELDS, null);
  }

  // Método que abre el panel de los filtros y hace focus en el campo del filtro seleccionado
  protected onChipFilterClick(keyFilter: string, columnType: GridDataType) {
    if (this.searchBusy()) {
      return;
    }
    this.customFilteringOpen = true;
    this.cdr.detectChanges();
    switch (columnType) {
      case GridDataType.DATE:
      case GridDataType.TIMESTAMP: {
        const dateInput: any = this.dateFields().find((el) => el.element.nativeElement.getAttribute('field-search-name') === keyFilter);
        setFocus(dateInput);
        break;
      }
      case GridDataType.NUMBER_BOOLEAN:
      case GridDataType.BOOLEAN:
      case GridDataType.BOOLEAN_ICONS: {
        const selectinput: any = this.selectFields().find((el) => el.elem.nativeElement.getAttribute('field-search-name') === keyFilter);
        setFocus(selectinput);
        break;
      }
      case GridDataType.ITEMS:
      case GridDataType.TEXT_ITEMS:
      case GridDataType.TEXT_MULTIPLE_ITEMS:
      case GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS:
      case GridDataType.LONG_ITEMS:
      case GridDataType.ICONS: {
        const comboinput: any = this.comboFields().find((el) => el.elem.nativeElement.getAttribute('field-search-name') === keyFilter);
        setFocus(comboinput);
        break;
      }
      default: {
        const input: any = this.inputFields().find((el) => el.element.nativeElement.getAttribute('field-search-name') === keyFilter);
        // Para este componente no se puede realizar focus con FormUtil, debido a que la directiva input no es accesible y el focus no se visualiza en el IgxInputGroup
        // https://github.com/IgniteUI/igniteui-angular/blob/301fb5332c/projects/igniteui-angular/src/lib/input-group/input-group.component.ts#L124
        const inputElement: any = input.element.nativeElement.querySelector('input');
        setTimeout(() => {
          if (inputElement) {
            inputElement.focus();
          }
        }, 150);
        break;
      }
    }
  }

  protected onHierarchyChipFilterClick(keyFilter: string, value: HierarchyValueEntity): void {
    if (this.searchBusy() || !keyFilter) {
      return;
    }
    this.customFilteringOpen = true;
    this.cdr.detectChanges();
    const comboinput: CatalogHierarchyComponent = this.hierarchyFields().find((el) => el.elem.nativeElement.getAttribute('field-search-name') === keyFilter);
    comboinput.setFocus(value);
  }

  protected castAsColumn(column: GridColumn): GridColumn {
    return column;
  }

  public deleteFilters() {
    if (this.searchBusy()) {
      return;
    }
    this.clearFilters();
    this.clearFilter.emit(true);
    this.applyFilters(GridSearchSide.SERVER_SIDE, GridSearchType.SEARCH_FIELDS, null);
  }

  protected filterStartTimeValue(column: GridColumn): any {
    const key = `startTime${column.field}`;
    if (!this.filterValues.hasOwnProperty(key)) {
      return null;
    }
    return this.filterValues[key].value;
  }

  protected filterEndTimeValue(column: GridColumn): any {
    const key = `endTime${column.field}`;
    if (!this.filterValues.hasOwnProperty(key)) {
      return null;
    }
    return this.filterValues[key].value;
  }

  protected filterValue(column: GridColumn): any {
    if (!this.filterValues.hasOwnProperty(column.field)) {
      return null;
    }
    return this.filterValues[column.field].value;
  }

  protected clearInput(columnName: string) {
    if (!this.filterValues.hasOwnProperty(columnName)) {
      return;
    }
    delete this.filterValues[columnName];
  }

  protected onFilterCombo(combo: any, column: GridColumn) {
    if (typeof column === 'undefined') {
      return;
    }
    if (column.type === GridDataType.TEXT_MULTIPLE_ITEMS_CONTAINS) {
      this.setFilterInputValue(column, combo.value);
    } else {
      this.setFilterInputValue(column, combo.value);
    }
  }

  protected onFilterHierarchy(change: CatalogGridChange, column: GridColumn): void {
    this.setFilterInputValue(column, change.value);
    if (change.requestedSearch) {
      this.applySearchFieldsFilter();
    }
  }

  protected onShouldFocus(target: ComboComponent | IgxInputGroupComponent): void {
    setFocus(target);
  }

  protected onFilterInput(input: any, column: GridColumn) {
    if (typeof column === 'undefined') {
      return;
    }
    this.setFilterInputValue(column, input.value);
  }

  protected onFilterDateSelected(event, column: GridColumn) {
    if (typeof column === 'undefined') {
      return;
    }
    this.setFilterInputValue(column, event);
  }

  protected getFromGroup(field: string) {
    if (this.#formGroups[field]) {
      return this.#formGroups[field];
    }
    const config: DataMap = {};
    config[field] = new UntypedFormControl('');
    const formGroup = this.formBuilder.group(config);
    this.#formGroups[field] = formGroup;
    return formGroup;
  }

  protected onKeyUpFilterInput(event: KeyboardEvent, input: any, column: GridColumn): void {
    if (this.applyKeyUpRules(input, column)) {
      if (event.code === 'Enter' || event.code === 'NumpadEnter') {
        this.applySearchFieldsFilter();
      }
    }
  }

  /**
   * Aplica las reglas necesarias al input para el evento keyUp
   * @param input El elemento <input> a verificar
   * @param column La configuración de la columna.
   * @returns Verdadero si no hay error en alguna regla.
   */
  private applyKeyUpRules(input: any, column: GridColumn): boolean {
    if (column.natural) {
      if (column.type.name === GridDataType.INTEGER.name) {
        if (!this.evalNaturalNumbersInput(input.value)) {
          input.value = '';
          return false;
        }
      }
    }
    return true;
  }

  /**
   * Verifica si un número es natural (entero >= 0).
   * @param value Valor a revisar
   * @returns True si es natural
   */
  private evalNaturalNumbersInput(value: number): boolean {
    const pattern = /^[0-9]\d*$/;
    return pattern.test(`${value}`);
  }

  private subscribeLocalSearchService(): void {
    this.#searchText
      .pipe(takeUntil(this.$destroy))
      .pipe(takeUntil(this.destroyCurrentLocalSearch))
      .pipe(debounceTime(500))
      .subscribe((config) => this._searchKeyUpExecute(config));
  }

  public setValidValueText(event, column: GridColumn) {
    if (column.type === GridDataType.DOUBLE || column.type === GridDataType.LONG || column.type === GridDataType.PERCENTAGE || column.type === GridDataType.INTEGER) {
      const regex = /[0-9.,]/;
      if (!event.key.match(regex)) {
        event.preventDefault();
      }
    }
  }

  public checkValidValueText(event, column: GridColumn): void {
    if (column.type === GridDataType.DOUBLE || column.type === GridDataType.LONG || column.type === GridDataType.PERCENTAGE || column.type === GridDataType.INTEGER) {
      const paste = event.clipboardData.getData('text');
      if (Number.isNaN(paste)) {
        event.preventDefault();
        event.stopPropagation();
      }
    }
  }

  protected onScrollModernContainer(event: EventInit): void {
    window.requestAnimationFrame(() => {
      const bnextScroll = new Event('bnextScroll', event);
      window.document.dispatchEvent(bnextScroll);
    });
  }

  protected openSettingsFilter(): void {
    this.dialogSettingsFilter().open();
    this.settingsForm.patchValue({
      filtersToShow: LocalStorageSession.getValue(LocalStorageItem.FILTERS_TO_SHOW, this.name()) || null
    });
  }

  protected applySettingsFilters(): void {
    const filtersToShow = this.settingsForm.get('filtersToShow').value;
    if (filtersToShow && filtersToShow > 0) {
      LocalStorageSession.setValue(LocalStorageItem.FILTERS_TO_SHOW, filtersToShow, this.name());
    } else {
      LocalStorageSession.clearValue(LocalStorageItem.FILTERS_TO_SHOW, this.name());
    }
    this.dialogSettingsFilter().close();
  }

  protected resetFilters(fieldConfig: string): void {
    if (fieldConfig === 'filtersToShow') {
      this.settingsForm.get('filtersToShow').reset(null);
    } else {
      //Anothers...
    }
  }

  initializeClearItems(): void {
    for (const item of this.clearItems) {
      item.text = (this.i18n[item.text] as string) || item.text;
    }
  }
}
