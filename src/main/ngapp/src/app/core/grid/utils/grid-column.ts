import * as NumberUtil from '@/core/utils/number-util';
import type { SafeHtml } from '@angular/platform-browser';
import { type IColumnPipeArgs, IgxNumberSummaryOperand, type IgxSummaryResult } from '@infragistics/igniteui-angular';
import type { DataMap } from '../../utils/data-map';
import type { GridCellType } from './grid-cell-type';
import type { GridDataType } from './grid-data-type';
import type { GridFilterType } from './grid-filter-type.enum';
import type { ColumnRender } from './grid-render';

export interface GridColumn<DataRowType = DataMap<any>> extends GridColumnState {
  allowCellClick?: boolean;
  allowEmptyValue?: boolean;
  allowManyAvatar?: boolean;
  allowSummaryClick?: boolean;
  avatar?: boolean;
  avatarColorized?: boolean;
  avatarIdKey?: string;
  avatarNameKey?: string;
  avatarRoundShape?: boolean;
  avatarSecundaryKey?: string;
  avatarSize?: string;
  cancel?: boolean;
  cellClasses?: any;
  cellStyles?: any;
  cellTextBox?: boolean;
  centered?: boolean;
  checked?: boolean;
  custom?: boolean;
  customCellEditor?: boolean;
  dateFormat?: string;
  searchDateFormat?: string;
  day?: Date;
  defaultFilterValue?: string | boolean | number;
  disableHiding?: boolean;
  dropdownActionLimit?: number;
  dropdownAlwaysVisible?: boolean;
  dynamicColumnController?: string;
  dynamicFieldType?: GridFilterType;
  editable?: boolean;
  editableClearable?: boolean;
  field: string;
  filterable?: boolean;
  filteringIgnoreCase?: boolean;
  filters?: any;
  fixedFilter?: boolean;
  formatter?: (value: any, rowData?: DataRowType) => any;
  groupable?: boolean;
  hasCustomWidth?: boolean;
  hasSummary?: boolean;
  header: string;
  headerClasses?: string;
  headerGroupClasses?: string;
  headerGroupStyles?: any;
  headerStyles?: any;
  hidden?: boolean;
  iconName?: string; // <-- Icono mostrado en el campo para editar
  isCustomRangeColumn?: boolean;
  itemsController?: string;
  linkHref?: string;
  linkParams?: string[];
  localizable?: boolean;
  mandatoryField?: boolean;
  maxWidth?: string;
  minWidth?: string;
  multipleValues?: boolean;
  natural?: boolean;
  pinned?: boolean;
  pipeArgs?: IColumnPipeArgs;
  positionIndex?: number;
  rangeColumn?: boolean;
  rangeColumns?: GridColumn<DataRowType>[];
  render?: ColumnRender;
  renderAsLink?: boolean;
  renderAsMarkdownText?: boolean;
  renderCell?: (cell: GridCellType<DataRowType>) => SafeHtml;
  resizable?: boolean;
  searchable?: boolean;
  onlyFilter?: boolean;
  selectable?: boolean;
  showAvatarNameKey?: boolean;
  showDetail?: boolean;
  skipExportFormatter?: boolean;
  sortable?: boolean;
  sortingIgnoreCase?: boolean;
  summaries?: IgxNumberSummaryOperand;
  switchPicker?: boolean;
  target?: string;
  textWhenEmpty?: string;
  tooltip?: boolean;
  type: GridDataType;
  typeDropDownValue?: GridDataType;
  visibleField?: (visible: boolean, cell: GridCellType<DataRowType>) => { visible: boolean };
  width?: string;
  hasImage?: boolean;
}

export class GridAvgSummary extends IgxNumberSummaryOperand implements IgxNumberSummaryOperand {
  public operate(data?: any[]): IgxSummaryResult[] {
    const result = [];
    const avg = IgxNumberSummaryOperand.average(data);
    result.push({
      key: 'avg',
      label: 'Average',
      summaryResult: data.length ? NumberUtil.round(avg, 2).toString() : '0.0'
    });
    return result;
  }
}
export class GridSumSummary extends IgxNumberSummaryOperand implements IgxNumberSummaryOperand {
  public operate(data?: any[]): IgxSummaryResult[] {
    const result = [];
    const sum = IgxNumberSummaryOperand.sum(data);
    result.push({
      key: 'sum',
      label: 'Sum',
      summaryResult: data.length ? NumberUtil.round(sum, 2).toString() : '0.0'
    });
    return result;
  }
}

export interface GridColumnState {
  field: string;
  positionIndex?: number;
  filterable?: boolean;
  pinned?: boolean;
  isCustomRangeColumn?: boolean;
  hasCustomWidth?: boolean;
  sortable?: boolean;
  hidden?: boolean;
  width?: string;
  header: string;
  columnGroup?: boolean;
  parentHeader?: string;
}
