import {
  GRID_STATE_FEATURES,
  buildColumnState,
  getColumnIndexKey,
  moveColumns,
  restoreColumnsState,
  verifyMinimumColumnWidth
} from '@/core/grid/utils/grid-state-util';
import * as GridUtil from '@/core/grid/utils/grid-util';
import type { igxButtonType } from '@/core/utils/button-util';
import * as DateUtil from '@/core/utils/date-util';
import type { DisplayDensity } from '@/core/utils/display-density';
import * as DomUtil from '@/core/utils/dom-util';
import * as NumberUtil from '@/core/utils/number-util';
import { arrayEquals, cloneObject, equalsObject, keysObject, shallowClone } from '@/core/utils/object';
import { encodeUrlParameter, nameInitials, stringColors } from '@/core/utils/string-util';
import { AggregateFunctionType } from '@/modules/forms/slim-report-base/slim-report-base.enums';
import { DatePipe } from '@angular/common';
import {
  type AfterContentInit,
  type AfterViewInit,
  Component,
  type ElementRef,
  HostBinding,
  Input,
  type OnChanges,
  type OnDestroy,
  type OnInit,
  type QueryList,
  type SimpleChanges,
  type TemplateRef,
  ViewChild,
  inject,
  input,
  output,
  viewChild
} from '@angular/core';
import { DomSanitizer, type SafeHtml } from '@angular/platform-browser';
import {
  AbsoluteScrollStrategy,
  AutoPositionStrategy,
  type CellType,
  ColumnPinningPosition,
  type DateRange,
  FilterMode,
  FilteringLogic,
  GlobalPositionStrategy,
  type GridSelectionMode,
  GridSummaryPosition,
  GroupedRecords,
  HorizontalAlignment,
  type IChangeCheckboxEventArgs,
  type IColumnExportingEventArgs,
  type IColumnMovingEndEventArgs,
  type IColumnMovingEventArgs,
  type IColumnMovingStartEventArgs,
  type IColumnResizeEventArgs,
  type IColumnVisibilityChangedEventArgs,
  type IFilteringExpressionsTree,
  type IFilteringStrategy,
  type IForOfState,
  type IGridCellEventArgs,
  type IGridEditEventArgs,
  type IGridKeydownEventArgs,
  type IGridScrollEventArgs,
  type IGridState,
  type IGridStateOptions,
  type IGridToolbarExportEventArgs,
  type IGroupingDoneEventArgs,
  type IGroupingExpression,
  type IPageEventArgs,
  type IPinColumnEventArgs,
  type IPinningConfig,
  type IRowDataEventArgs,
  type IRowDragEndEventArgs,
  type IRowDragStartEventArgs,
  type ISortingExpression,
  type ITreeGridAggregation,
  type ITreeGridRecord,
  type IgxColumnComponent,
  IgxDialogComponent,
  type IgxExporterOptionsBase,
  IgxGridComponent,
  IgxGridStateDirective,
  IgxGroupedTreeGridSorting,
  IgxToggleDirective,
  IgxTreeGridComponent,
  type PositionSettings,
  type RowType,
  type State,
  type ToggleViewEventArgs,
  type Transaction,
  type TransactionService,
  TransactionType,
  VerticalAlignment
} from '@infragistics/igniteui-angular';
import { scaleInCenter, scaleOutCenter } from '@infragistics/igniteui-angular/animations';
import type { Schema } from 'read-excel-file/types';
import { Subject, type Subscription, forkJoin } from 'rxjs';
import { first, takeUntil, throttleTime } from 'rxjs/operators';
import { BnextCoreComponent } from '../bnext-core.component';
import type { DropdownMenuItem } from '../dropdown-menu/dropdown-menu.interfaces';
import type { DynamicFieldsDTO } from '../dynamic-field/field-handler/field-handler.interfaces';
import { FieldDisplayComponent } from '../field-display/field-display.component';
import type { IBnextRowSelectionEventArgs, IGridComponent } from '../grid-base-select/grid-base-select.interfaces';
import type { BnextComponentPath } from '../i18n/bnext-component-path';
import { GridLocalConfig } from '../indexed-db/grid-local-config';
import type { GridConfigDTO } from '../indexed-db/grid-local-config.interfaces';
import { Session } from '../local-storage/session';
import { RestApiModule } from '../rest-api.module';
import { AppService } from '../services/app.service';
import { NoticeService } from '../services/notice.service';
import type { ToolbarSelectorBaseComponent } from '../toolbar-selector/toolbar-selector-base.component';
import { AspectUtil } from '../utils/aspect-util';
import type { DataMap } from '../utils/data-map';
import { Deferred } from '../utils/deferred';
import { ErrorHandling } from '../utils/error-handling';
import type { ITextHasValue, TextHasValue } from '../utils/text-has-value';
import { GridSearchComponent } from './grid-search/grid-search.component';
import type { GridSearchChange } from './grid-search/grid-search.interfaces';
import { GridModule } from './grid.module';
import type { GridDropDownItem, GridIcon } from './utils/grid-base.interfaces';
import type { GridCellType, GridRowType } from './utils/grid-cell-type';
import type { GridColumn, GridColumnState } from './utils/grid-column';
import type { GridInfo } from './utils/grid-data';
import { GridDataType } from './utils/grid-data-type';
import { ExportOptions, FormatOptions, type RangeSelectorType, RecordOptions } from './utils/grid-export-enum';
import { GridFilteringStrategy } from './utils/grid-filtering-strategy';
import type { GridFilterConfig } from './utils/grid-filtering-util';
import { getEmpyFilters, getFilters } from './utils/grid-filtering-util';
import type { GridI18n } from './utils/grid-i18n';
import type { ColumnRender } from './utils/grid-render';
import { BnextGridType, BnextGridWindowPosition, GridInfoStatus, InvalidRowChildType } from './utils/grid.enums';
import {
  type ActionStrip,
  type AvatarUserInfo,
  DynamicColumnChild,
  type GridDataLoadedEvent,
  GridDayColumn,
  type GridDynamicFieldsLoadedEvent,
  type GridEditDoneEventArgs,
  type GridExportResult,
  type GridFilter,
  type GridFiltersExport,
  type GridImportData,
  type ICellEditValueToTextChange,
  type ISwitchColumn,
  type RenderizableColumn,
  type RenderizableGroup,
  type RenderizableSummary
} from './utils/grid.interfaces';
import { TreeFilteringStrategy } from './utils/tree-filtering-strategy';

const MINIMUM_PANEL_LARGE_HEIGHT = 36 + 16 + 66 + 48 + 3;
const MINIMUM_PANEL_MEDIUM_HEIGHT = 36 + 66 + 5;
// Referencia:
// https://github.com/IgniteUI/igniteui-angular/blob/e05b38d13ab2bc530dbe81c9b688b9c5b1d123e7/projects/igniteui-angular/src/lib/grids/tree-grid/tree-grid.grouping.pipe.ts
const HIDDEN_FIELD_NAME = '_Igx_Hidden_Data_'; // {fieldKey: value} de la columna oculta por GroupBy - SOLO TREE GRID

@Component({
  selector: 'app-grid',
  standalone: true,
  imports: [GridModule],
  styleUrls: ['./grid.component.scss'],
  templateUrl: './grid.component.html'
})
export class GridComponent<DataRowType = DataMap<any>>
  extends BnextCoreComponent
  implements OnInit, OnDestroy, AfterViewInit, AfterContentInit, OnChanges, IGridComponent
{
  sanitizer = inject(DomSanitizer);

  datePipe = inject(DatePipe);
  protected api = inject(AppService);
  protected noticeService = inject(NoticeService);

  public static LANG_CONFIG: BnextComponentPath = GridUtil.LANG_CONFIG;

  pinning: IPinningConfig = { columns: ColumnPinningPosition.Start };
  cols: QueryList<IgxColumnComponent>;
  dateFilterFormat = 'dd/MM/yyyy HH:mm';
  dateFormat = 'dd/MM/yyyy'; // TODO: Reemplazar por this.displayFormat
  restDateFormat = 'YYYY-MM-DD';
  emptyFilteredGridMessage = '';
  filterStrategy: IFilteringStrategy;
  groupHeaderText = '';
  timestampFormat = 'DD/MM/YYYY H:mm:ss';
  lastUpdated = Date.now();
  searchUpdate = new Subject<any>();
  configReady = new Subject<any>();
  rangeParamReady = new Deferred<boolean>();
  EnumGridDataType = GridDataType;
  EnumBnextGridType = BnextGridType;
  EnumInvalidRowChildType = InvalidRowChildType;
  transactionsColumns: GridColumn<DataRowType>[] = [];
  transactionsData: Transaction[] = [];
  public sorting = IgxGroupedTreeGridSorting.instance();

  private _grid: IgxGridComponent | IgxTreeGridComponent;
  private _igxGrid: IgxGridComponent;
  private _igxTreeGrid: IgxTreeGridComponent;
  private _locale: string;
  private _data: DataRowType[];
  private _url: string;
  //se calcula el alto para dispositivos mobiles
  private _height = '';
  private _baseLoaded = false;
  private _dynamicSearchColumnsEnabled = false;
  private _configDynamicSearchColumns: GridColumn<DataRowType>[] = [];
  private _dynamicSearchColumns: GridColumn<DataRowType>[] = [];
  private _searchItemsEnabled = false;
  private _searchItemsColumns: GridColumn<DataRowType>[] = [];
  private _configReady = false;
  private _saveStateDebounce = new Subject<boolean>();
  private readySubscription: Subscription;
  private $cancelableSearch = new Subject<boolean>();
  private subscriptionsOnDestroy: Subscription[] = [];
  private _onDestroyCalled = false;
  private _groupingExpressions: IGroupingExpression[] = [];
  private _computedUrl: string = undefined;
  private _rangeSelect: ToolbarSelectorBaseComponent = null;
  #rangeSelectorColumnMap: DataMap<GridColumn<DataRowType>[]> = {};
  private _entityDynamicTypeIds: number[] = [];
  private tooltipRowSelected = null;
  private _columnsStateMap: GridColumnState[] = [];
  public tooltipColumnMessage = '';
  public tooltipTitle = '';
  public aggregations: ITreeGridAggregation[] = [];
  override watchResizeEvents = true;
  public addAvatarFixClass = false;
  /**
   *  -----    INPUTS  SECTION  ------
   */
  public readonly summaryPosition = input<GridSummaryPosition>(GridSummaryPosition.bottom);
  public gridClass = input('');
  public rowStyles = input<DataMap<string | ((row: RowType) => string)>>(null);
  public rowClasses = input<DataMap<(row: RowType) => boolean>>(null);

  public disabled = input(false);
  public localTimeForDates = input(true);
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public loading = false;
  public groupIdentLevel1 = input(true);
  public paging = input(true);
  public disabledDynamicSearchLookup = input(false);
  public allowShowMoreChilds = input(true);
  public allowValueToTextMultipleValues = input(true);
  public allowShowGroupByCount = input(true);
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public allowFiltering = true;
  public rowDraggable = input(false);
  public allowGrouping = input(true);
  public allowClearState = input(false);
  public allowMaximize = input(false);
  public readonly rowFieldId = input('entity_id');
  public readonly rowEditable = input(false);
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public page = 1;
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public perPage = 100;
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public searchConfig: GridSearchChange<DataRowType> = {
    caseSensitive: false,
    searchText: null,
    filterValues: null,
    filteringTree: null,
    refreshData: true,
    isGlobalSearch: false,
    isGlobalSearchByEnter: false
  };
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public currentDataPerPage = this.perPage;
  readonly deleteUrl = input<string>(undefined);
  readonly updateUrl = input<string>(undefined);
  public id = input('grid');
  public name = input('grid');
  public autoGenerate = input(false);
  public autoMinimumHeight = input(true);
  public autoMinimumWidth = input(true);
  public transactional = input(false);
  public persistState = input(true);
  public readonly emptyGridTemplate = input<TemplateRef<any>>(undefined);
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public filteringExpressionsTree: IFilteringExpressionsTree;

  public columnHiding = input(true);
  public rowSelectable = input(false);

  public showSearch = input(true);

  public showRefresh = input(true);
  public readonly noContentErrorMessage = input('La información está bloqueda, verifique si no existe otro proceso que la ocupe.');
  /**
   * Id de Columna (GridColumn)
   * Define la columna configurada cuyo valor será mostrado en el renglón
   * agrupado. Se utiliza cuando se agrupan columnas en grids tipo "app-grid-tree",
   */
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public groupKey = 'entity_description';
  /**
   * Atributo de rowData
   * Define un atributo de tipo "Array<rowData>" donde vienen todos los hijos para
   * formar el arbol.
   *
   * `IGridInfo.data[n].childs`
   */
  public childDataKey = input('childs');
  public readonly width = input('100%');
  public readonly rowHeight = input<string>(undefined);
  public readonly primaryKey = input<string>(undefined);
  public rowSelection = input<GridSelectionMode>('none');
  public filterMode = input(FilterMode.quickFilter);
  public allowAdvancedFiltering = input(false);
  public cellSelection = input<GridSelectionMode>(undefined);
  public columnSelection = input<GridSelectionMode>(undefined);
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public columnPinning = true;
  public sortingExpressions = input<ISortingExpression[]>([]);
  public showToolbar = input(true);
  public readonly clipboardOptions = input<{
    /**
     * Enables/disables the copy behavior
     */
    enabled: boolean;
    /**
     * Include the columns headers in the clipboard output.
     */
    copyHeaders: boolean;
    /**
     * Apply the columns formatters (if any) on the data in the clipboard output.
     */
    copyFormatters: boolean;
    /**
     * The separator used for formatting the copy output. Defaults to `\t`.
     */
    separator: string;
  }>({
    enabled: false,
    copyHeaders: false,
    copyFormatters: true,
    separator: '\t'
  });
  public exportExcel = input(true);
  public exportCsv = input(true);
  public showExportButtonLabel = input(true);
  public exportAutoConfig = input(true);
  public exportFormatOption = input(FormatOptions.EXCEL);
  public exportColumnsOption = input(ExportOptions.ONLY_VISIBLES);
  public exportRecordsOption = input(RecordOptions.CURRENT_PAGE);
  public showTitle = input(true);
  public titleLabel = input('');
  public searchLabel = input('');
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public emptyGridMessage = '';
  public escapeUnderscore = input(true);

  public stateOptions = input<IGridStateOptions>({
    columns: true,
    filtering: false,
    advancedFiltering: false,
    sorting: true,
    groupBy: true,
    paging: true,
    cellSelection: false,
    rowSelection: false,
    columnSelection: false,
    rowPinning: true,
    pinningConfig: true,
    expansion: true,
    rowIslands: false
  });

  public readonly stateUrl = input('users/grid-state');

  public readonly filter = input<(data: any[]) => void>(undefined);
  /**
   * Es el nombre de la columna dentro del arreglo `data` que contiene la información de fechas.
   * Para el siguiente ejemplo, el valor sería `datesRangeData`
   *
   * data = [
   *     {row: 1},
   *     {row: 2},
   *     {row: 3, datesRangeData: [{subRow: 1, subRow: 2, subRow: 3, dayRangeDate: 1649841365132 }]},
   *     {row: 4, datesRangeData: [{subRow: 1, subRow: 2, dayRangeDate: 1649841365132 }]},
   *     {row: 5}
   * ]
   **/
  public rangeSelectorDataKey = input('datesRangeData');

  /**
   * Es el nombre de la columna dentro del arreglo `datesRangeData` que contiene la fecha.
   * Para el siguiente ejemplo, el valor sería `dayRangeDate`
   *
   * data = [
   *     {row: 1},
   *     {row: 2},
   *     {row: 3, datesRangeData: [{subRow: 1, subRow: 2, subRow: 3, dayRangeDate: 1649841365132 }]},
   *     {row: 4, datesRangeData: [{subRow: 1, subRow: 2, dayRangeDate: 1649841365132 }]},
   *     {row: 5}
   * ]
   **/
  public rangeSelectorDataColumnKey = input('dayRangeDate');

  public allowRangeSelector = input(false);

  public rangeSelectorType = input<RangeSelectorType>('WeeklySelector');

  public allowRangeSelectorNoColumns = input(false);

  public rangeSelectorColumns = input<GridColumn<DataRowType>[]>([]);

  public readonly rangeFilterName = input('-');
  public readonly rangeFilterFrom = input<string>(null);
  public readonly rangeFilterTo = input<string>(null);
  public igxButtonToolbarButtonType = input<igxButtonType>('contained');
  public igxButtonToolbarButtonDensity = input<DisplayDensity>('comfortable');

  public showTopExportButton = input(false);

  public showTopImportButton = input(false);

  public importColumnIdLabel = input('');

  public importSchema = input<Schema>(null);

  public allowRemoveAll = input(false);

  actionsTopEnable = input<boolean>(undefined);

  topZoneEnable = input<boolean>(undefined);

  rowTooltipEnabled = input<boolean>(undefined);

  public tooltipTemplate = input<TemplateRef<any>>(undefined);

  public readonly groupable = input(true);

  // --- Funcionalidad para mostrar ActionStrip ------
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public showActionStrip = false;
  public readonly customMoreChildsName = input(null);

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input()
  public actionStripConfig: ActionStrip = {
    dropdownAlwaysVisible: false,
    dropdownActionLimit: 3,
    render: {
      rowIdentifierKey: 'entity_code',
      menuActionOptions: (_row: GridRowType) => []
    }
  };

  public showToolbarTitle = input(true);
  public allowMoving = input(true);
  public allowDayColumns = input(true);
  /**
   *  -----    OUTPUT  SECTION  ------
   */
  public readonly toggleMaximize = output<BnextGridWindowPosition>();
  public readonly cellClick = output<IGridCellEventArgs>();
  public readonly summaryClick = output<RenderizableSummary<DataRowType>>();
  public readonly cellDetalilClick = output<RenderizableColumn<DataRowType>>();
  public readonly clearCellClick = output<RenderizableColumn<DataRowType>>();
  public readonly cellEditValueToTextChange = output<ICellEditValueToTextChange<DataRowType>>();
  public readonly gridKeydown = output<IGridKeydownEventArgs>();
  public readonly rowDragStart = output<IRowDragStartEventArgs>();
  public readonly rowDragEnd = output<IRowDragEndEventArgs>();
  public readonly selection = output<IGridCellEventArgs>();
  public readonly rowSelectionChange = output<IBnextRowSelectionEventArgs<DataRowType>>();
  public readonly columnPin = output<IPinColumnEventArgs>();
  public readonly cellEdit = output<GridEditDoneEventArgs<DataRowType>>();
  public readonly columnInit = output<IgxColumnComponent>();
  public readonly sortingDone = output<ISortingExpression>();
  public readonly filteringDone = output<IFilteringExpressionsTree>();
  public readonly pagingDone = output<IPageEventArgs>();
  public readonly rowAdded = output<IRowDataEventArgs>();
  public readonly rowdeleted = output<IRowDataEventArgs>();
  public readonly dataPreLoad = output<IForOfState>();
  public readonly columnResized = output<IColumnResizeEventArgs>();
  public readonly contextMenu = output<IGridCellEventArgs>();
  public readonly doubleClick = output<IGridCellEventArgs>();
  public readonly columnVisibilityChanged = output<IColumnVisibilityChangedEventArgs>();
  public readonly columnMovingStart = output<IColumnMovingStartEventArgs>();
  public readonly columnMoving = output<IColumnMovingEventArgs>();
  public readonly columnMovingEnd = output<IColumnMovingEndEventArgs>();
  public readonly toolbarExporting = output<IGridToolbarExportEventArgs>();
  public readonly dataLoad = output<GridDataLoadedEvent>();
  public readonly dynamicFieldsLoaded = output<GridDynamicFieldsLoadedEvent>();
  public readonly urlLoaded = output<any>();
  public readonly dataRefresh = output<any>();
  public readonly dropdownMenu = output<GridDropDownItem>();
  public readonly changedFilter = output<any>();
  public readonly scrollChanged = output<any>();
  public readonly localConfigSaved = output<any>();
  public readonly stateCleared = output<any>();
  public readonly cellSwitchValueChange = output<ISwitchColumn<DataRowType>>();
  public readonly deleteAllRegistries = output<any>();
  public readonly importedExcel = output<GridImportData<any>>();
  public readonly showDetalilCell = output<RenderizableColumn<DataRowType>>();
  public readonly clearSearchGlobalFilters = output<void>();
  /**
   *  -----  END -  OUTPUT  SECTION  ------
   */
  public i18n: GridI18n = {};

  public columnsReady = false;
  public columnsSetted = false;
  private visibleColumnValueToText = true;

  public positionStrategyScaleCenter = new GlobalPositionStrategy({
    openAnimation: scaleInCenter,
    closeAnimation: scaleOutCenter
  });
  public overlaySettingsScaleCenter = {
    positionStrategy: this.positionStrategyScaleCenter,
    scrollStrategy: new AbsoluteScrollStrategy(),
    modal: true,
    closeOnEscape: true
  };
  public positionSettings: PositionSettings = {
    horizontalStartPoint: HorizontalAlignment.Right,
    verticalStartPoint: VerticalAlignment.Bottom,
    horizontalDirection: HorizontalAlignment.Left,
    verticalDirection: VerticalAlignment.Bottom
  };
  public positionStrategyAuto = new AutoPositionStrategy(this.positionSettings);
  public overlaySettingsAuto = {
    positionStrategy: this.positionStrategyAuto,
    scrollStrategy: new AbsoluteScrollStrategy(),
    modal: false,
    closeOnEscape: false
  };

  get isGridReady(): boolean {
    return this._configReady && this.columnsReady;
  }

  /**
   *  -----  VIEWCHILD  SECTION  ------
   */
  searchComponent = viewChild(GridSearchComponent);

  // TODO: Skipped for migration because:
  //  Accessor queries cannot be migrated as they are too complex.
  @ViewChild(IgxTreeGridComponent)
  set igxTreeGrid(value: IgxTreeGridComponent) {
    this._igxTreeGrid ||= value || undefined;
    this._grid ||= value;
    if (this._grid) {
      this.evaluateConfigReady();
    }
  }

  // TODO: Skipped for migration because:
  //  Accessor queries cannot be migrated as they are too complex.
  @ViewChild(IgxGridComponent)
  set igxGrid(value: IgxGridComponent) {
    this._igxGrid ||= value || undefined;
    this._grid ||= value;
    if (this._grid) {
      this.evaluateConfigReady();
    }
  }

  readonly stateDirective = viewChild(IgxGridStateDirective);

  readonly toggleRefPinning = viewChild<IgxToggleDirective>('toggleRefPinning');
  readonly hidingButton = viewChild<ElementRef>('hidingButton');
  readonly pinningButton = viewChild<ElementRef>('pinningButton');

  // TODO: Skipped for migration because:
  //  Accessor queries cannot be migrated as they are too complex.
  @ViewChild('rangeSelector')
  set rangeSelector(value: ToolbarSelectorBaseComponent) {
    this._rangeSelect = value;
    this.updateComputedUrl();
  }

  dialogHiddingColumns = viewChild('dialogHiddingColumns', { read: IgxDialogComponent });

  public toggleHiddenTreeGrid = viewChild('toggleRefHidingTreeGrid', { read: IgxToggleDirective });

  /**
   *  ----- END VIEWCHILD  SECTION  ------
   */

  /**
   * Se llena a partir del "set" correspondiente de "igxGrid" o "igxTreeGrid",
   * según el valor de "gridType"
   **/
  get grid(): IgxGridComponent | IgxTreeGridComponent {
    return this._grid;
  }

  get gridType(): BnextGridType {
    return BnextGridType.DATA_GRID;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set selectedRows(value: (string | number | DataRowType)[]) {
    if (!this.grid) {
      return;
    }
    this.grid.selectedRows = value;
  }

  get selectedRows(): (string | number | DataRowType)[] {
    if (!this.grid) {
      return null;
    }
    return this.grid.selectedRows;
  }

  /**
   * No utilizar directamente en el HTML, utilizar "selectedRows" en su lugar
   */
  get selectedRowIds(): (string | number)[] {
    if (!this.grid) {
      return null;
    }
    return this.grid.selectedRows
      .map((rowId) => {
        if (typeof rowId === 'string' || typeof rowId === 'number') {
          return rowId;
        }
        if (!rowId) {
          return null;
        }
        return rowId[this.grid.primaryKey];
      })
      .filter((rowId) => rowId !== null);
  }

  /**
   * No utilizar directamente en el HTML, utilizar "selectedRows" en su lugar
   */
  get selectedRowsData(): DataRowType[] {
    if (!this.grid) {
      return null;
    }
    return this.grid.selectedRows
      .map((rowId) => {
        if (typeof rowId === 'string' || typeof rowId === 'number') {
          return this.grid.getRowData(rowId);
        }
        if (!rowId) {
          return null;
        }
        return rowId;
      })
      .filter((rowId) => rowId !== null);
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set locale(locale: string) {
    this._locale = locale;
  }

  get locale(): string {
    if (this._locale === null || typeof this._locale === 'undefined') {
      return this.translate?.currentLang || 'es';
    }
    return this._locale;
  }

  get rangeSelector(): ToolbarSelectorBaseComponent {
    return this._rangeSelect;
  }

  columnMap: DataMap<GridColumn<DataRowType>> = {};
  isLargeScreen = true;
  private _columns: GridColumn<DataRowType>[] = [];
  private _dayColumns: GridColumn<DataRowType>[] = [];
  private columnsOriginalState = {};

  public isColumnDropdownHeight: {
    '500': boolean;
    '250': boolean;
  } = {
    '500': false,
    '250': false
  };
  public isFirstPage = true;
  public isLastPage = false;
  public totalPages: number;
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public totalCount = 0;
  public perPageValues = [5, 10, 15, 25, 50, 100, 500];

  @HostBinding('class.maximized')
  public maximized = false;

  getRangeSelectorColumns(dayColumn: GridColumn<DataRowType>): GridColumn<DataRowType>[] {
    const dateKey = DateUtil.format(dayColumn.day, 'YYYYMMDD');
    if (this.#rangeSelectorColumnMap[dateKey]) {
      return this.#rangeSelectorColumnMap[dateKey];
    }
    const filtersRange = cloneObject(this.rangeSelectorColumns(), ['filters']);
    let rangeColumn: GridColumn<DataRowType>[] = [];
    const hasFilters = filtersRange?.length > 0;
    if (hasFilters) {
      rangeColumn = filtersRange.map((column: GridColumn<DataRowType>) => {
        column.field = `${dateKey}_${column.field}`; // <--- El formato de las columnas generadas es `YYYYMMDD_field`
        const columnId = getColumnIndexKey(column);
        this.columnMap[columnId] = column;
        return column;
      });
    }
    this.#rangeSelectorColumnMap[dateKey] = rangeColumn;
    if (!dayColumn.rangeColumns) {
      dayColumn.rangeColumns = [];
    }
    if (hasFilters) {
      dayColumn.rangeColumns.push(...rangeColumn);
    }
    return rangeColumn;
  }

  get allowRangeSelectorColumns(): boolean {
    if (this.allowRangeSelectorNoColumns()) {
      return true;
    }
    return this.rangeSelectorColumns()?.length > 0;
  }

  get undoEnabled(): boolean {
    if (!this.grid || !this.grid.transactions) {
      return false;
    }
    return this.grid.transactions.canUndo;
  }

  get redoEnabled(): boolean {
    if (!this.grid || !this.grid.transactions) {
      return false;
    }
    return this.grid.transactions.canRedo;
  }

  get hasRangeSelectorColumns(): boolean {
    return this.allowRangeSelectorColumns && this.allowRangeSelector();
  }

  get hasTransactions(): boolean {
    if (!this.grid || !this.grid.transactions) {
      return false;
    }
    return this.grid.transactions.getAggregatedChanges(false).length > 0;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set columns(value: GridColumn<DataRowType>[]) {
    if (!value || value.length === 0 || this.columnsReady || this.columnsSetted) {
      return;
    }
    /**
     * ToDo: Mover este código a `ngOnChanges`, aquí se evalua demasiadas veces
     *
     * No agregar más código aquí.
     **/
    this._columns = value;
    this._fixColumnsDefaults();
    this.columnsSetted = true;
    this.evaluateConfigReady();
  }

  get columns(): GridColumn<DataRowType>[] {
    return this._columns;
  }

  get configDynamicSearchColumns(): GridColumn<DataRowType>[] {
    return this._configDynamicSearchColumns;
  }

  set configDynamicSearchColumns(value: GridColumn<DataRowType>[]) {
    this._configDynamicSearchColumns = value;
  }

  public get dynamicSearchColumns(): GridColumn<DataRowType>[] {
    return this._dynamicSearchColumns;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  public set dynamicSearchColumns(value: GridColumn<DataRowType>[]) {
    this._dynamicSearchColumns = value;
  }

  get dayColumns(): GridColumn<DataRowType>[] {
    return this._dayColumns;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set data(value: DataRowType[]) {
    if (value) {
      const datesColumns = [];
      for (const column of this.columns) {
        if (column.type === GridDataType.DATE || column.type === GridDataType.TIMESTAMP) {
          datesColumns.push(column.field);
        }
      }
      for (const element of value) {
        for (const column of datesColumns) {
          if (!element[column]) {
            continue;
          }
          element[column] = new Date(element[column]);
        }
      }
    }
    this.initializeLocale();
    this._data = value;
  }

  /**
   * No editar se utiliza para evaluar el estado del grid
   * Puede ocasionar problemas si se modifica como (ISSUE-6024) al agregar this._data ?? []:
   *
   * Uncaught RangeError: Maximum call stack size exceeded
   *     at OperatorSubscriber.error (Subscriber.js:36:39)
   *     at OperatorSubscriber._next (OperatorSubscriber.js:16:33)
   *     at OperatorSubscriber.next (Subscriber.js:31:18)
   *     at ConsumerObserver.next (Subscriber.js:91:33)
   *     at SafeSubscriber._next (Subscriber.js:60:26)
   *     at SafeSubscriber.next (Subscriber.js:31:18)
   *     at Subject.js:34:30
   *     at errorContext (errorContext.js:19:9)
   *     at EventEmitter_.next (Subject.js:27:21)
   *     at EventEmitter_.emit (core.mjs:6845:19)
   */
  get data(): DataRowType[] {
    return this._data;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set url(value: string) {
    const newUrl = this.url !== value;
    this._url = value;
    this.updateComputedUrl();
    this.evaluateConfigReady();
    if (newUrl) {
      this.evaluateDynamicFields();
    }
  }

  get url() {
    return this._url;
  }

  readonly masterId = input<string>(undefined); // Originalmente para formularios (reportes) utilizandose en la clase GridFilterByReportProcessingAccess

  get searchEnabled(): boolean {
    return ((!!this.url && this.showRefresh()) || (!this.url && !this.showRefresh())) && this.showSearch();
  }

  get groupingExpressions(): IGroupingExpression[] {
    return this._groupingExpressions;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set groupingExpressions(value: IGroupingExpression[]) {
    if (value === null || typeof value === 'undefined') {
      value = [];
    }
    for (const e of value) {
      if (e.strategy && keysObject(e.strategy).length === 0) {
        e.strategy = null;
      }
    }
    this._groupingExpressions = value;
    if (this.grid && !arrayEquals(this._groupingExpressions, this.gridExpressions)) {
      if (!this.isTreeGrid) {
        this._igxGrid.groupingExpressions = value;
      } else if (this._igxTreeGrid.groupArea) {
        this._igxTreeGrid.groupArea.expressions = value;
      }
    }
  }

  get gridExpressions(): IGroupingExpression[] {
    return this.grid.groupArea?.expressions || this.groupingExpressions;
  }

  get height(): string {
    return this._height;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set height(value: string) {
    let _heightPx;
    if (value) {
      if (value.indexOf('px') !== -1) {
        _heightPx = +value.replace('px', '');
      } else {
        _heightPx = 0;
      }
    }
    if (this.autoMinimumHeight() || _heightPx === 0) {
      const newHeight = _heightPx;
      const minimumPanelHeight: number = this.getMinimumHeightPx();
      if (newHeight - minimumPanelHeight > minimumPanelHeight) {
        value = `${newHeight - minimumPanelHeight}px`;
      } else {
        value = `${minimumPanelHeight}px`;
      }
    }
    this._height = value;
  }

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input()
  set entityDynamicTypeIds(value: number[]) {
    if (value !== null && value.length > 0) {
      this._entityDynamicTypeIds = value;
    }
  }

  get entityDynamicTypeIds() {
    return this._entityDynamicTypeIds;
  }

  public get transactions(): TransactionService<Transaction, State> {
    return this.grid.transactions;
  }

  private getMinimumHeightPx(): number {
    if (this.isLargeScreen && this.maximized) {
      return MINIMUM_PANEL_LARGE_HEIGHT - this.appBarSizePx + 5 /* margen inferior */ + 56 /* paginación */ + 12 /* reservado para hilera de filtros */;
    }
    if (this.isLargeScreen) {
      return MINIMUM_PANEL_LARGE_HEIGHT;
    }
    return MINIMUM_PANEL_MEDIUM_HEIGHT;
  }

  constructor() {
    super();

    if (this.gridType === BnextGridType.TREE_GRID) {
      this.filterStrategy = new TreeFilteringStrategy();
    } else {
      this.filterStrategy = new GridFilteringStrategy();
    }
    this.readySubscription = this.configReady.pipe(first()).subscribe(() => {
      this.addAvatarFixClass = this.columns.filter((f) => f.avatar === true)?.length > 0;
      this.columns.forEach((col) => {
        if (col.field.includes('_data_content')) {
          col.hasImage = true;
        } else {
          col.hasImage = false;
        }
      });
      this._evaluateLazyConfiguration();
      const configs: Promise<boolean>[] = [];
      if (this.allowClearState() && this.grid.groupArea) {
        /**
         * Se utilizan "aspectos" para guardar el estado al eliminar `groupingExpressions`.
         *
         * Esto es un parche hasta que se resuelva el issue:
         * https://github.com/IgniteUI/igniteui-angular/issues/11900
         */
        AspectUtil.advice().after(this.grid.groupArea, 'clearGrouping', (_name?: string) => {
          this.groupingExpressions = this.grid.groupArea?.expressions || this.groupingExpressions;
          if (this.persistState()) {
            this.saveGridState();
          }
        });
      }
      if (this.allowDynamicSearchColumns) {
        configs.push(this.loadDynamicFields());
      }
      if (this.hasRangeSelectorColumns) {
        configs.push(this.loadRangeFields());
      }
      if (this.allowSearchItemsColumns) {
        configs.push(this.loadSearchItems());
      }
      if (configs.length > 0) {
        Promise.all(configs).then(() => {
          this.columnsReady = true;
          this._ready();
        });
      } else {
        this.columnsReady = true;
        this._ready();
      }
    });
  }

  public addDynamicFields(columns: GridColumn[]): void {
    if (!columns.length) {
      return;
    }
    if (this.columns) {
      for (const column1 of this.columns.filter((column) => !!column.dynamicFieldType)) {
        delete this.columnMap[column1.field];
      }
      this._columns = this.columns.filter((column) => !column.dynamicFieldType);
    } else {
      this.columns = [];
    }
    for (const column of columns) {
      this.columns.push(column);
      this.columnMap[column.field] = column;
    }
    this._dynamicSearchColumns = this.columns.filter((column) => !!column.dynamicFieldType);
  }

  private loadRangeFields(): Promise<boolean> {
    this._dayColumns = [];
    return new Promise<boolean>((resolve, _reject) => {
      if (this.rangeSelector?.isParamReady) {
        this._createDayColumns();
        resolve(true);
      } else {
        this.rangeParamReady.promise.then(() => {
          this._createDayColumns();
          resolve(true);
        });
      }
    });
  }

  private _createDayColumns(): void {
    if (this.allowDayColumns()) {
      const dayColumnsUtil = GridUtil.columns(this._dayColumns);
      const range: DateRange = this.rangeSelector.range;
      let acumulatorDays = DateUtil.safe(range?.start);
      let columnDayCount = 1;
      while (acumulatorDays <= range?.end && columnDayCount <= GridUtil.MAX_COLUMN_DAYS) {
        const day = new Date(acumulatorDays.getTime());
        const header = DateUtil.format(day, 'dddd, DD/MM', true, this.getLang());
        dayColumnsUtil.push('datesRangeData', new GridDayColumn({ header, day }));
        acumulatorDays = DateUtil.add(acumulatorDays, 'day', 1);
        columnDayCount++;
      }
      this._fixColumnsDefaults();
      for (const column of this._dayColumns) {
        column.isCustomRangeColumn = true;
        if (!column.cellClasses) {
          column.cellClasses = {};
        }
        column.cellClasses['range-cell'] = true;
        const columnId = getColumnIndexKey(column);
        this.columnMap[columnId] = column;
      }
    }
  }

  private loadDynamicFields(): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      if (this.disabledDynamicSearchLookup()) {
        resolve(true);
        return;
      }
      const addedField: DataMap<boolean> = {};
      forkJoin(
        this.configDynamicSearchColumns.map((c) =>
          this.api.get({
            url: `${c.dynamicColumnController}/fields/grid/column/list${this.entityDynamicTypeIds.length > 0 ? `/${this.entityDynamicTypeIds}` : ''}`,
            handleFailure: true,
            cancelableReq: this.$cancelableSearch
          })
        )
      )
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: (responses: DynamicFieldsDTO[]) => {
            for (const f of responses) {
              if (!f.valid) {
                console.error('No se pudieron dibujar columnas dinámicas.');
                continue;
              }
              if (this.columns) {
                for (const column of this.columns.filter((col) => !!col.dynamicFieldType)) {
                  delete this.columnMap[column.field];
                }
                this._columns = this.columns.filter((col) => !col.dynamicFieldType);
              }
              const gridColumns = GridUtil.columns(this.columns);
              for (const d of f.dynamicFields) {
                if (typeof d.label === 'string') {
                  d.label = d.label.trim();
                }
                if (addedField[d.name]) {
                  console.warn(`Skipped dynamicField ${d.name} as it was already added.`);
                } else {
                  addedField[d.name] = true;
                  const i18n: DataMap<string> = {};
                  i18n[d.name] = d.label;
                  if (this.columns) {
                    if (this.columns.findIndex((col) => col.field === d.name) === -1) {
                      gridColumns.lang(i18n).push(d.name, new DynamicColumnChild(d));
                    }
                  } else {
                    gridColumns.lang(i18n).push(d.name, new DynamicColumnChild(d));
                  }
                }
              }
              this.saveColumnOriginalState(this.columns);
              this._dynamicSearchColumns = this.columns.filter((column) => !!column.dynamicFieldType);
              this.dynamicFieldsLoaded.emit({
                dynamicSearchColumns: this.dynamicSearchColumns,
                grid: this.grid
              });
            }
            if (keysObject(addedField).length > 0) {
              this._fixColumnsDefaults();
              this.autoDefineHeight();
            }
            resolve(true);
          },
          error: (e) => {
            reject(e);
          }
        });
    });
  }

  private loadSearchItems(): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      const loadSearchColumns = this._searchItemsColumns.filter((column) => !column.render.items?.length);
      if (!loadSearchColumns?.length) {
        resolve(true);
        return;
      }
      const searchControllers = Array.from(new Set<string>(loadSearchColumns.map((c) => c.itemsController)));
      forkJoin(
        searchControllers.map((c) =>
          this.api.get({
            url: `${c}/actives`,
            handleFailure: true,
            cancelableReq: this.$destroy
          })
        )
      )
        .pipe(takeUntil(this.$destroy))
        .subscribe({
          next: (responses: ITextHasValue<number>[][]) => {
            const dataResponses = {};
            for (let index = 0, l = responses.length; index < l; index++) {
              const items = responses[index];
              const searchController = searchControllers[index];
              dataResponses[searchController] = items;
            }
            for (const column of loadSearchColumns) {
              if (column?.render) {
                const items = dataResponses[column.itemsController];
                column.render.items = items || [];
                GridUtil.setRenderItemsCache(column);
              } else {
                if (this.debug) {
                  console.log(`Missing render config for column ${column.field}`);
                }
              }
            }
            resolve(true);
          },
          error: (e) => {
            reject(e);
          }
        });
    });
  }

  isClearCellAvailable(context: RenderizableColumn<DataRowType>): boolean {
    return context.column?.editableClearable && context.column.editable && context.value;
  }

  get isStateSaved(): 'SAVED' | 'UNPERSISTED' | 'UNSAVED' {
    if (!this.persistState()) {
      return 'UNPERSISTED';
    }
    if (GridLocalConfig.hasLocalValue(this.id())) {
      return 'SAVED';
    }
    return 'UNSAVED';
  }

  get allowSearchItemsColumns(): boolean {
    return this._searchItemsEnabled;
  }

  get allowDynamicSearchColumns(): boolean {
    return this._dynamicSearchColumnsEnabled;
  }

  private _evaluateLazyConfiguration(): void {
    this.configDynamicSearchColumns = this.columns.filter((c) => typeof c.dynamicColumnController === 'string');
    this._dynamicSearchColumnsEnabled = this.configDynamicSearchColumns?.length > 0;
    this._searchItemsColumns = this.columns.filter((c) => typeof c.itemsController === 'string');
    this._searchItemsEnabled = this._searchItemsColumns?.length > 0;
  }

  private _parseSafeJson(value: string): any {
    try {
      return JSON.parse(value);
    } catch (_e) {
      console.error(`Failed to parse value from json: ${value}`);
      return null;
    }
  }

  private _ready(): void {
    this.cdr.detectChanges();
    this.unsubscribeReadyConfig();
    this.subscriptionsOnDestroy.push(
      this._saveStateDebounce
        .pipe(throttleTime(300, undefined, { leading: false, trailing: true }), takeUntil(this.$destroy))
        .subscribe(() => this.executeSaveStateValue())
    );
    if (this.persistState() && Session.isLegacyFiltersOpen()) {
      if (this.allowMoving()) {
        this.api
          .get({
            url: `${this.stateUrl()}/get/${encodeUrlParameter(this.id())}`,
            handleFailure: true,
            cancelableReq: this.$cancelableSearch
          })
          .pipe(takeUntil(this.$destroy))
          .subscribe({
            next: (response: any) => {
              let gridState: GridConfigDTO = null;
              if (response?.gridConfig) {
                gridState = {
                  stateSerialized: response.gridConfig.stateSerialized,
                  resultsPerPage: response.gridConfig.resultsPerPage,
                  currentPage: response.gridConfig.currentPage,
                  searchConfig: this._parseSafeJson(response.gridConfig.searchConfig),
                  groupingExpressions: this._parseSafeJson(response.gridConfig.groupingExpressions),
                  columnsState: this._parseSafeJson(response.gridConfig.columnsState)
                };
                this.restoreState(gridState);
              } else {
                GridLocalConfig.getValue(this.id()).then(
                  (value) => {
                    this.restoreState(value);
                  },
                  (error) => {
                    console.error('Could not load local config.', error);
                  }
                );
              }
            },
            error: (e) => {
              console.error('Could not load backend config.', e);
            }
          });
      } else {
        GridLocalConfig.getValue(this.id()).then(
          (value) => {
            this.restoreState(value);
          },
          (error) => {
            console.error('Could not load local config.', error);
          }
        );
      }
    } else {
      this.refresh();
      this.initializeLocale();
    }
  }

  private restoreState(gridState: GridConfigDTO, saveState = false): void {
    this.restoreGridState(gridState).then(
      () => {
        if (this._onDestroyCalled) {
          return;
        }
        this.refresh();
        this.initializeLocale();
        if (saveState) {
          this.executeSaveStateValue();
        }
      },
      (e) => {
        if (this._onDestroyCalled) {
          return;
        }
        console.error('Failed to restore grid state.', e);
        this.refresh();
        this.initializeLocale();
      }
    );
  }

  public getRowByIndex(index: number): RowType {
    return this.grid.getRowByIndex(index);
  }

  public getRowByKey(keyValue: any): RowType {
    return this.grid.getRowByKey(keyValue);
  }

  public addRow(data: any): void {
    this.grid.addRow(data);
    this.initializeLocale();
  }

  public deleteRow(rowSelector: any): void {
    this.grid.deleteRow(rowSelector);
  }

  public markForCheck() {
    this.grid.markForCheck();
  }

  public refreshSearch(updateActiveInfo?: boolean): number {
    return this.grid.refreshSearch(updateActiveInfo);
  }

  override ngOnInit() {
    super.ngOnInit();
    if (this.isMobilDevice()) {
      this._height = `${this.windowInnerHeight - this.windowInnerHeight * 0.22}px`;
    } else {
      this._height = '100%';
    }
    this.autoDefineHeight();
    this.initializeLocale();
    if (this.isSmallTouchDevice) {
      this.columnPinning = false;
    }
    this.updateComputedUrl();
    this.tooltipColumnMessage = this.translate.instant('root.common.message.show-more');
    this.tooltipTitle = this.translate.instant('root.common.message.extra-information');
  }

  evaluateConfigReady() {
    if (
      this.grid &&
      this.columns &&
      this.columns.length > 0 &&
      ((this.allowRangeSelector() && this.rangeSelector?.isParamReady) || !this.allowRangeSelector()) &&
      (this.data || typeof this._computedUrl === 'string')
    ) {
      if (this._configReady) {
        return;
      }
      this.setGridAttributes();
      this.setGridLabels();
      this.setGridEvents();
      this.autoDefineHeight();
      this._configReady = true;
      this.cdr.detectChanges(); // <-- Necesario para inicializar nuevos valores del grid, va despues de "_configReady = true" para no llamar doble
      this.configReady.next(null);
      this.configReady.complete();
    }
  }

  avatarRoundShape(column): 'square' | 'rounded' | 'circle' {
    return column.avatarRoundShape ? 'circle' : 'square';
  }

  private autoDefineHeight(): void {
    this.isLargeScreen = this.isScreenLarge;
    if (this.autoMinimumHeight()) {
      let heightPx;
      const minimumPanelHeight: number = this.getMinimumHeightPx();
      if (this.maximized) {
        heightPx = this.containerSizePx + this.appBarSizePx;
      } else {
        heightPx = this.containerSizePx;
      }
      if (heightPx < minimumPanelHeight) {
        this.height = `${minimumPanelHeight}px`;
      } else {
        this.height = `${heightPx}px`;
      }
    }
    for (const column of this.columns) {
      GridUtil.updateMediumScreenColumn(column, this.groupable(), this.isLargeScreen);
    }
  }

  get isTreeGrid(): boolean {
    return typeof this._igxTreeGrid !== 'undefined';
  }

  /**
   * @deprecated
   * ToDo: Mover este código a `ngOnChanges`, en `set columns()` se evalua demasiadas veces
   *
   * No agregar más código aquí.
   */
  private _fixColumnsDefaults(): void {
    this.columnMap = {};
    for (const column of this._columns) {
      this._fixColumnsDefault(column);
      this.columnMap[column.field] = column;
    }
    for (const column of this.rangeSelectorColumns()) {
      this._fixColumnsDefault(column);
    }
  }

  private _fixColumnsDefault(column): void {
    if (column.type) {
      column.type = GridDataType.findInstance(column.type);
    }
    GridUtil.updateMediumScreenColumn(column, this.groupable(), this.isLargeScreen);
    if (typeof column.headerClasses === 'undefined' || column.headerClasses?.length === 0) {
      column.headerClasses = null;
    }
    if (typeof column.headerStyles === 'undefined') {
      column.headerStyles = null;
    }
    if (typeof column.headerGroupClasses === 'undefined') {
      column.headerGroupClasses = null;
    }
    if (typeof column.headerGroupStyles === 'undefined') {
      column.headerGroupStyles = null;
    }
    if (typeof column.cellClasses === 'undefined' || column.cellClasses?.length === 0) {
      column.cellClasses = null;
    }
    if (typeof column.cellStyles === 'undefined') {
      column.cellStyles = null;
    }
    /**
     * La virtualización de Ignite UI solo soporta tener elementos del mismo tamaño.
     * Al tener columnas de diferente ancho se calcula el ancho máximo de todas las columnas
     * y se toma ese para los elementos virutalizados
     * Se aplica un tamaño mínimo de 250px para tratar de reducir el número de elementos a tener
     * en la virtualización
     **/
    verifyMinimumColumnWidth(column, this.autoMinimumWidth());
    if (column.type === GridDataType.DATE && !column.formatter) {
      column.formatter = (val: any) => {
        if (val && val.value !== null && typeof val.value !== 'undefined') {
          return val;
        }
        return this.datePipe.transform(val, this.dateFormat, this.getTimezone());
      };
      column.custom = true;
    } else if (column.type === GridDataType.TIMESTAMP && !column.formatter) {
      column.formatter = (val: any) => {
        if (val && val.value !== null && typeof val.value !== 'undefined') {
          return val;
        }
        return DateUtil.format(val, this.timestampFormat);
      };
      column.custom = true;
    } else if (column.type === GridDataType.PERCENTAGE && !column.formatter) {
      column.formatter = (val: any) => {
        if (val && val.value !== null && typeof val.value !== 'undefined') {
          return val;
        }
        if (typeof val !== 'number') {
          return val;
        }
        return `${val}%`;
      };
      column.custom = true;
    } else if (column.type === GridDataType.DOUBLE && !column.formatter) {
      column.formatter = (value: any) => {
        if (NumberUtil.isNumber(value)) {
          if (typeof value === 'string') {
            value = Number(value);
          }
          return Number(NumberUtil.round(value, column.decimalCount).toFixed(column.decimalCount));
        }
        return value?.value || value || null;
      };
      column.custom = true;
    }
  }

  /**
   * IMPORTANTE: Se llama una sola vez! no agregar más llamadas.
   *
   * Llena los eventos del grid para poderse implementar desde
   * fuera de "grid.component".
   **/
  private setGridEvents(): void {
    if (!this.grid || typeof this.grid.cellClick === 'undefined') {
      return;
    }
    this.grid.cellClick.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onCellClickWrapper($event));
    this.grid.cellEdit.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onCellEdit($event));
    this.grid.columnInit.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onColumnInitWrapper($event));
    this.grid.columnMoving.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onColumnMovingWrapper($event));
    this.grid.columnMovingEnd.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onColumnMovingEndWrapper($event));
    this.grid.columnMovingStart.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onColumnMovingStartWrapper($event));
    this.grid.columnPin.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onColumnPinningWrapper($event));
    this.grid.columnPinned.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.limitPining($event));
    this.grid.columnResized.pipe(takeUntil(this.$destroy)).subscribe(($event) => {
      this.limitPining($event);
      this.onColumnResizedWrapper($event);
    });
    this.grid.columnVisibilityChanged.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onColumnVisibilityChangedWrapper($event));
    this.grid.contextMenu.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onContextMenuWrapper($event));
    this.grid.doubleClick.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onDoubleClickWrapper($event));
    this.grid.filteringDone.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onFilteringDoneWrapper($event));
    this.grid.gridKeydown.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onGridKeydownWrapper($event));
    this.grid.gridScroll.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onScroll($event));
    this.grid.rowAdded.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onRowAddedWrapper($event));
    this.grid.rowDeleted.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onRowDeletedWrapper($event));
    this.grid.rowDragEnd.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onRowDragEnd($event));
    this.grid.rowDragStart.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onRowDragStart($event));
    this.grid.rowSelectionChanging.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onRowSelectionChangeWrapper($event));
    this.grid.selected.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onSelectionWrapper($event));
    this.grid.sortingDone.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onSortingDoneWrapper($event));
    this.grid.toolbarExporting.pipe(takeUntil(this.$destroy)).subscribe(($event) => this.onToolbarExportingWrapper($event));
    this.transactionalEvents();
  }

  /**
   * Llena las etiquetas de los botones del grid, solo cuando
   * el grid y las etiquetas están cargadas.
   **/
  private setGridLabels(): void {
    if (!this.grid || !this._baseLoaded) {
      return;
    }
    this.groupHeaderText = this.i18n.groupHeaderText;
  }

  /**
   * IMPORTANTE: Se llama una sola vez! no agregar más llamadas.
   *
   * Se llenan todos los parametros iniciales de "grid", en este metodo
   * no debe ir ningún parametro que cambie por acciones del usuario
   * o código. Ese deben colocarse directamente en el template (Ej. [data]).
   *
   * ToDo: Hacer pruebas para encapsular los parametros "mutables" en un metoto y refrescarlos desde "ngOnChanges"
   *
   **/
  private setGridAttributes(): void {
    if (!this.grid) {
      return;
    }
    this.grid.allowAdvancedFiltering = this.allowAdvancedFiltering();
    this.grid.allowFiltering = this.allowFiltering;
    this.grid.autoGenerate = this.autoGenerate();
    this.grid.batchEditing = this.transactional();
    this.grid.cellSelection = this.cellSelection();
    this.grid.clipboardOptions = this.clipboardOptions();
    this.grid.columnSelection = this.columnSelection();
    this.grid.emptyGridMessage = this.emptyGridMessage;
    this.grid.emptyGridTemplate = this.emptyGridTemplate();
    this.grid.filterMode = this.filterMode();
    this.grid.filterStrategy = this.filterStrategy;
    this.grid.locale = this.locale;
    this.grid.primaryKey = this.primaryKey();
    this.grid.rowDraggable = this.rowDraggable();
    this.grid.rowEditable = this.rowEditable();
    this.grid.rowHeight = this.rowHeight();
    this.grid.rowSelection = this.rowSelection();
    this.grid.summaryPosition = this.summaryPosition();
    this.grid.width = this.width();
    this.grid.moving = this.allowMoving();
    if (this.isTreeGrid) {
      const gridRef = this._grid as IgxTreeGridComponent;
      gridRef.childDataKey = this.childDataKey();
      this.groupKey = '';
    }
    if (this.groupingExpressions?.length > 0) {
      this.groupBy(this.gridExpressions);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    super.ngOnChanges(changes);
    if (changes.allowRangeSelector) {
      this.updateComputedUrl();
    }
    if (changes.height?.currentValue) {
      this.height = changes.height.currentValue;
      this.cdr.detectChanges();
    }
    if (changes.columns?.currentValue) {
      this.saveColumnOriginalState(changes.columns.currentValue as unknown as GridColumn<DataRowType>[]);
    }
  }

  override ngAfterViewInit() {
    super.ngAfterViewInit();
    if (this.debug) {
      console.log(`Grid instance with ID: ${this.id()}.`);
    }
    if (this.columns?.length === 0) {
      console.error('Missing columns for grid: ', this.id());
    }
  }

  private transactionalEvents(): void {
    if (this.transactional() && this.grid) {
      this.transactionsData = this.grid.transactions.getAggregatedChanges(true);
      this.grid.transactions.onStateUpdate.subscribe(() => {
        this.transactionsData = this.grid.transactions.getAggregatedChanges(true);
      });
    }
  }

  override ngAfterContentInit() {
    super.ngAfterContentInit();
  }

  private restoreGridState(value: GridConfigDTO): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (!this.persistState() || (this._onDestroyCalled && !Session.isLegacyFiltersOpen())) {
        resolve();
        return;
      }
      if (!value || this._onDestroyCalled) {
        resolve();
        return;
      }
      if (value.resultsPerPage) {
        this.perPage = value.resultsPerPage;
      }
      if (value.currentPage) {
        this.page = value.currentPage;
      }
      if (value.groupingExpressions) {
        this.groupingExpressions = value.groupingExpressions;
      }
      if (value.columnsState) {
        this._columnsStateMap = value.columnsState;
        this.setStateGridColumns(value.columnsState);
      }
      if (value.stateSerialized) {
        try {
          const state: IGridState = this._parseSafeJson(value.stateSerialized as string);
          // TODO: Eliminar cuando ya no exista este valor en base de datos
          if (state.expansion) {
            state.expansion = undefined;
          }
          if (value.searchConfig !== null && typeof value.searchConfig !== 'undefined') {
            const config = this.searchComponent()?.updateLastSearchConfig(value.searchConfig);
            if (config) {
              this.searchConfig = config;
              this.newFilteredSearch(config);
            }
          }
          // ToDo: Cambiar este método al lifeCycle afterViewInit y asegurar que haya al menos un dato.
          // Hay un error al setear el state en los TreeGrid para el groupBy -- Error: TypeError: array is not iterable at IgxTreeGridGroupingPipe.groupBy
          this.stateDirective().setState(state);
          if (this.grid instanceof IgxGridComponent) {
            this.groupingExpressions = state?.groupBy?.expressions;
          }
          this.detectChanges();
        } catch (e) {
          if (this._onDestroyCalled) {
            reject(e);
            return;
          }
          console.error(`Failed to restore grid ${this.id()}. State:`, value);
          console.error(`Failed to restore grid ${this.id()}. Error:`, e);
          GridLocalConfig.clear(this.id());
          reject(e);
        }
      }
      resolve();
    });
  }

  private setStateGridColumns(columns: GridColumnState[]): void {
    restoreColumnsState(this.grid, columns, this.columnMap, this.groupable(), this.isLargeScreen, this.autoMinimumWidth());
    this.detectChanges();
  }

  private saveGridState() {
    if (!this.persistState() || !Session.isLegacyFiltersOpen()) {
      return;
    }
    this._saveStateDebounce.next(true);
  }

  private executeSaveStateValue() {
    if (!this.persistState() || !Session.isLegacyFiltersOpen()) {
      return;
    }
    const stateSerialized = this.stateDirective().getState(true, GRID_STATE_FEATURES);
    this.saveGridLocalConfig(stateSerialized);
  }

  saveGridSearch(config: GridSearchChange<DataRowType>) {
    if (equalsObject(this.searchConfig, config) || this._onDestroyCalled) {
      return;
    }
    this.saveGridState();
  }

  clearState() {
    if (!this.allowClearState() || !this.persistState() || this._onDestroyCalled || !this._configReady || !Session.isLegacyFiltersOpen()) {
      return;
    }
    this.groupingExpressions = [];
    this.page = 0;
    this.searchConfig = {
      caseSensitive: false,
      searchText: null,
      filterValues: null,
      filteringTree: null,
      refreshData: true,
      isGlobalSearch: false,
      isGlobalSearchByEnter: false
    };
    GridLocalConfig.clear(this.id());
    this.searchComponent().deleteFilters();
    this.restoreGridOrder();
    this.stateCleared.emit(true);
  }

  private saveGridLocalConfig(stateSerialized: string | IGridState) {
    if (!this.persistState() || this._onDestroyCalled || !this._configReady || !Session.isLegacyFiltersOpen()) {
      return;
    }
    this._columnsStateMap = this._grid.columns.map((c, index) => buildColumnState(c, this.columnMap, index));
    if (this.allowMoving()) {
      this.saveGridStateByUser(stateSerialized, this._columnsStateMap);
    }
    GridLocalConfig.setValue(
      {
        resultsPerPage: this.perPage,
        currentPage: this.page,
        stateSerialized: stateSerialized,
        searchConfig: this.searchConfig,
        groupingExpressions: this.groupingExpressions,
        columnsState: this._columnsStateMap
      },
      this.id()
    );
    this.localConfigSaved.emit(true);
  }

  /**
   * Guarda el estado del grid en backend por usuario, por el momento, solo está habilitado para los grids
   * que permiten reacomodar las columnas
   *
   * @param stateSerialized IGridState
   * @param columnsState Ordenamiento por columna
   */
  private saveGridStateByUser(stateSerialized: string | IGridState, columnsState: GridColumnState[]): void {
    // Se elimina referencia circular para guardar los filtros aplicados
    for (const key in this.searchConfig.filterValues) {
      if (!this.searchConfig.filterValues.hasOwnProperty(key)) {
        continue;
      }
      this.searchConfig.filterValues[key].column.filters = {};
    }
    const data = {
      gridId: this.id,
      gridConfig: {
        resultsPerPage: this.perPage,
        currentPage: this.page,
        stateSerialized: stateSerialized,
        searchConfig: JSON.stringify(this.searchConfig),
        groupingExpressions: JSON.stringify(this.groupingExpressions),
        columnsState: JSON.stringify(columnsState)
      }
    };
    this.api
      .post({
        url: `${this.stateUrl()}/sync/${encodeUrlParameter(this.id())}`,
        postBody: data,
        options: null,
        handleFailure: true,
        cancelableReq: this.$cancelableSearch
      })
      .subscribe({
        next: () => {},
        error: (result) => {
          console.error(`Could not save gridState ${this.id()} with data ${data}. Error: `, result);
        }
      });
  }

  public getTotalCount(): number {
    return this.totalCount;
  }

  private defineMaxDropdownHeight() {
    if (this.perPage < 10 || this.totalCount < 10) {
      this.isColumnDropdownHeight[250] = false;
      this.isColumnDropdownHeight[500] = true;
    } else if (this.perPage < 15 || this.totalCount < 15) {
      this.isColumnDropdownHeight[250] = true;
      this.isColumnDropdownHeight[500] = false;
    } else {
      this.isColumnDropdownHeight[250] = false;
      this.isColumnDropdownHeight[500] = false;
    }
  }

  override ngOnDestroy() {
    this._onDestroyCalled = true;
    super.ngOnDestroy();
    this.$cancelableSearch.next(true);
    this.$cancelableSearch.complete();
    for (const subs of this.subscriptionsOnDestroy) {
      if (subs && !subs.closed) {
        subs.unsubscribe();
      }
    }
    this.unsubscribeReadyConfig();
    this.cdr.detach();
  }

  initializeLocale() {
    if (this._baseLoaded) {
      return;
    }
    this.subscriptionsOnDestroy.push(
      this.translateService.getFrom(GridComponent.LANG_CONFIG, 'base').subscribe((grid) => {
        this.i18n = grid;
        this._baseLoaded = true;
        this.setGridLabels(); // <-- Se cargan las etiquetas cuando están listas
      })
    );
  }

  onCellEditValueToTextMultipleChange($event: (number | string)[], context: RenderizableColumn<DataRowType>): void {
    if ($event && Array.isArray($event)) {
      context.cell.value = new Set([...$event]);
    } else {
      context.cell.value = null;
    }
    this.cellEditValueToTextChange.emit({
      selected: context.cell.value,
      cell: context.cell
    });
  }

  onCellEditValueToTextSingleChange($event: TextHasValue, cell: CellType): void {
    if ($event?.value) {
      cell.value = new Set([$event?.value]);
    } else {
      cell.value = null;
    }
    this.cellEditValueToTextChange.emit({
      selected: cell.value,
      cell: cell
    });
  }

  onSwitchColumnChange($event: IChangeCheckboxEventArgs, cell: CellType): void {
    cell.value = +$event.checked || +false;
    this.cellSwitchValueChange.emit({
      checked: $event.checked,
      cell: cell
    });
  }

  evalValue(value: any): boolean {
    return typeof value === 'boolean' ? value : value === 1;
  }

  onCellClickWrapper($event: IGridCellEventArgs) {
    this.cellClick.emit($event);
  }

  onCellClick($event: RenderizableColumn<DataRowType>) {
    if (!$event.column.allowCellClick) {
      return;
    }
    this.cellDetalilClick.emit($event);
  }

  showDetail($event: RenderizableColumn<DataRowType>) {
    if (!$event.column.showDetail) {
      return;
    }
    this.showDetalilCell.emit($event);
  }

  onSummaryClick($event: RenderizableSummary<DataRowType>) {
    if (!$event.column.allowSummaryClick) {
      return;
    }
    this.summaryClick.emit($event);
  }

  onClearCellClick($event: RenderizableColumn<DataRowType>) {
    this.clearCellClick.emit($event);
  }

  onToggleMaximize() {
    this.maximized = !this.maximized;
    if (this.maximized) {
      this.toggleMaximize.emit(BnextGridWindowPosition.FIXED);
    } else {
      this.toggleMaximize.emit(BnextGridWindowPosition.STATIC);
    }
    this.autoDefineHeight();
    this.cdr.detectChanges();
  }

  onRowDragStart($event: IRowDragStartEventArgs) {
    this.rowDragStart.emit($event);
  }

  onRowDragEnd($event: IRowDragEndEventArgs) {
    this.rowDragEnd.emit($event);
  }

  onGridKeydownWrapper($event: IGridKeydownEventArgs) {
    this.gridKeydown.emit($event);
  }

  onSelectionWrapper($event: IGridCellEventArgs) {
    this.selection.emit($event);
  }

  onRowSelectionChangeWrapper($event: IBnextRowSelectionEventArgs<DataRowType>) {
    this.rowSelectionChange.emit($event);
    setTimeout(() => {
      if (this.selectedRows.length === 1 && typeof this.selectedRows[0] === 'undefined') {
        throw new Error(`Missing "primaryKey" configuration, current: "${this.primaryKey()}" at grid "${this.id()}"`);
      }
    }, 100);
    this.saveGridState();
  }

  onColumnPinningWrapper($event: IPinColumnEventArgs) {
    this.columnPin.emit($event);
    this.saveGridState();
  }

  onCellEdit($event: IGridEditEventArgs) {
    this.cellEdit.emit($event);
  }

  onColumnInitWrapper(column: IgxColumnComponent) {
    this.columnInit.emit(column);
  }

  onSortingDoneWrapper($event) {
    this.searchUpdate.next(null);
    this.sortingDone.emit($event);
    this.saveGridState();
  }

  onFilteringDoneWrapper($event: IFilteringExpressionsTree) {
    this.searchUpdate.next(null);
    this.filteringDone.emit($event);
  }

  onDataPreLoadWrapper($event: IForOfState) {
    this.dataPreLoad.emit($event);
  }

  onPagingDoneWrapper($event: IPageEventArgs) {
    this.pagingDone.emit($event);
    this.saveGridState();
  }

  onRowAddedWrapper($event: IRowDataEventArgs) {
    this.rowAdded.emit($event);
  }

  onRowDeletedWrapper($event: IRowDataEventArgs) {
    this.rowdeleted.emit($event);
  }

  onColumnResizedWrapper($event: IColumnResizeEventArgs) {
    const column = this.columnMap[$event?.column?.field];
    if (column) {
      column.hasCustomWidth = true;
    }
    this.columnResized.emit($event);
    this.saveGridState();
  }

  onContextMenuWrapper($event: IGridCellEventArgs) {
    this.contextMenu.emit($event);
  }

  onDoubleClickWrapper($event: IGridCellEventArgs) {
    this.doubleClick.emit($event);
  }

  onColumnVisibilityChangedWrapper($event: IColumnVisibilityChangedEventArgs) {
    const column = this.columnMap[$event.column.field];
    if (column) {
      column.hidden = $event.newValue;
    } else {
      console.error(`Falta la definición de la columna "${$event.column.field}" en \`columnMap\` del grid "${this.id()}".`);
    }
    const searchText = this.searchComponent()?.searchValueSafe;
    if (searchText !== null && typeof searchText !== 'undefined' && searchText !== '') {
      this.searchComponent().updateSearch();
    }
    this.columnVisibilityChanged.emit($event);
    this.saveGridState();
  }

  onColumnMovingStartWrapper($event: IColumnMovingStartEventArgs) {
    this.columnMovingStart.emit($event);
  }

  onColumnMovingWrapper($event: IColumnMovingEventArgs) {
    this.columnMoving.emit($event);
  }

  onColumnMovingEndWrapper($event: IColumnMovingEndEventArgs) {
    this._columnsStateMap = moveColumns($event, this._columnsStateMap, this.grid.columns, this.columnMap);
    this.columnMovingEnd.emit($event);
    this.saveGridState();
  }

  onScroll($event: IGridScrollEventArgs) {
    this.scrollChanged.emit($event);
  }

  onStartedExporting() {
    this.loading = true;
  }

  onEndedExporting(result: GridExportResult) {
    this.loading = false;
    if (result.fileDownloaded) {
      this.refreshData();
    }
  }

  onToolbarExportingWrapper(args: IGridToolbarExportEventArgs) {
    args.exporter.columnExporting.subscribe((a: IColumnExportingEventArgs) => {
      a.cancel = this.columnMap[a.field]?.cancel || false;
      a.skipFormatter = this.columnMap[a.field]?.skipExportFormatter || false;
    });
    const options: IgxExporterOptionsBase = args.options;
    options.fileName = `${this.titleLabel()} - ${DateUtil.format(new Date(), 'DD/MM/YYYY H:mm:ss')}`;
  }

  public refresh() {
    if (!this.showRefresh()) {
      if (!this._computedUrl) {
        this.emitDataLoad();
      }
      return;
    }
    this.paginate(this.page);
  }

  public restoreGridOrder() {
    const columnsState: GridColumnState[] = [];
    let idxShift = 0;
    if (this._columnsStateMap.length === 0) {
      return;
    }
    for (const col of this._columnsStateMap) {
      const column = shallowClone(col);
      const positionIndex = this.columnsOriginalState[col.field];
      if (typeof positionIndex !== 'undefined') {
        column.positionIndex = positionIndex + idxShift;
      } else if (col.positionIndex === 0) {
        //Cuando viene la columna Agrupacion (definida como la de indice 0) se hace un corrimiento para ajustar los indices.
        idxShift = 1;
      }
      columnsState.push(column);
    }
    const value: any = {
      columnsState: columnsState
    };
    this.restoreState(value, true);
  }

  public refreshAsync(): Promise<void> {
    return new Promise((succ, error) => {
      if (!this.showRefresh()) {
        if (!this._computedUrl) {
          this.emitDataLoad();
        }
        succ();
        return;
      }
      this.paginateAsync(this.page)
        .then(() => {
          succ();
        })
        .catch(() => error());
    });
  }

  castCell(cell: CellType) {
    return cell;
  }

  onToggleSummary(): void {
    this.grid.cdr.detectChanges();
    setTimeout(() => {
      this.reset();
    }, 100);
  }

  private updateComputedUrl(range?: DateRange): void {
    let newUrl: string;
    if (this._url === null || typeof this._url === 'undefined' || this._url.trim() === '') {
      newUrl = undefined;
    } else {
      if (this.allowRangeSelector()) {
        let searchRange: DateRange;
        if (range?.start && range?.end) {
          searchRange = range;
        } else if (this.rangeSelector?.range?.start && this.rangeSelector?.range?.end) {
          searchRange = this.rangeSelector.range;
        } else {
          const now = new Date();
          searchRange = {
            start: now,
            end: now
          };
          if (this.debug) {
            console.log(`Not defined range, using current date ${now} for filter.`);
          }
        }
        const start = DateUtil.format(searchRange.start, this.restDateFormat);
        const end = DateUtil.format(searchRange.end, this.restDateFormat);
        newUrl = `${this._url}/${start}/${end}`;
      } else {
        newUrl = this._url;
      }
    }
    this._computedUrl = newUrl;
  }

  onRangeParamReady(_paramsAvailable: boolean): void {
    if (!this.rangeSelector?.isParamReady && _paramsAvailable) {
      this.rangeParamReady.resolve(_paramsAvailable);
    }
  }

  onUpdatedRangeData(range: DateRange): void {
    if (this.debug) {
      console.log('Updated range', range);
    }
    this.updateComputedUrl(range);
    this.loadRangeFields();
    this.refreshData();
  }

  reset() {
    this.grid.resetCaches();
    this.grid.reflow();
    this.detectChanges();
  }

  unsubscribeReadyConfig() {
    if (this.readySubscription && !this.readySubscription.closed) {
      this.readySubscription.unsubscribe();
    }
  }

  emitDataLoad() {
    this.dataLoad.emit({
      data: this.data,
      grid: this.grid
    });
    if (this.isTreeGrid) {
      if (this._igxTreeGrid?.records?.size > 0) {
        for (const treeRow of this._igxTreeGrid.records.values()) {
          const row = treeRow as ITreeGridRecord;
          if (row.data.invalidRowChild && row.parent) {
            this._igxTreeGrid.gridAPI.set_row_expansion_state(row.parent.key, false);
          }
        }
      }
    }
    this.notifyChanges();
  }

  refreshData(index?: number, perPage?: number): Promise<void> {
    return new Promise((result, reject) => {
      if (!this.columnsReady) {
        // Entra aquí cuando hay parametros de `toolbar-weekly-selector`, el refresh se ejecuta de nuevo al terminar de cargar columnas
        this.loading = false;
        this.cdr.detectChanges(); // <-- Necesario antes de `evaluateConfigReady` para que llene `this.grid`
        this.evaluateConfigReady();
        result();
        return;
      }
      if (this.disabled() || this.loading || !this._computedUrl) {
        this.loading = false;
        this.emitDataLoad();
        result();
        return;
      }
      if (this.debug) {
        console.log(`refreshing data for ${this.id()} and index ${index}, perPage ${perPage}.`);
      }
      this.$cancelableSearch.next(true);
      this.loading = true;
      if (!NumberUtil.isNumber(perPage)) {
        if (NumberUtil.isNumber(this.perPage)) {
          perPage = this.perPage;
        } else {
          perPage = 15;
        }
      }
      const filterBuild: GridFilterConfig<DataRowType> = {
        gridId: this.id(),
        componentName: this.translateService.componentName,
        index: index,
        dynamicSearchEnabled: this.allowDynamicSearchColumns,
        perPage: perPage,
        filteringExpressionsTree: this.filteringExpressionsTree,
        sortingExpressions: this.grid ? this.grid.sortingExpressions : null,
        groupingExpressions: this.grid && this.gridType === BnextGridType.DATA_GRID ? (this.grid as IgxGridComponent).groupingExpressions : this.groupingExpressions,
        allowFiltering: this.allowFiltering,
        columnMap: this.columnMap,
        datePipe: this.datePipe,
        dateFormat: this.dateFormat,
        escapeUnderscore: this.escapeUnderscore(),
        masterId: this.masterId() || null
      };
      const filter = getFilters(filterBuild);
      if (!filter || !this._computedUrl) {
        this.loading = false;
        this.emitDataLoad();
        return;
      }
      this.getData(filter, result, reject);
    });
  }

  private getData(filter: GridFilter, result, reject) {
    this.api
      .post({
        url: this._computedUrl,
        postBody: filter,
        options: null,
        handleFailure: true,
        cancelableReq: this.$cancelableSearch
      })
      .pipe(takeUntil(this.$cancelableSearch))
      .subscribe({
        next: (gridInfo: GridInfo<DataRowType>) => {
          if (!gridInfo || !gridInfo.data || gridInfo.count === 0) {
            if (`${gridInfo.status}` === GridInfoStatus[GridInfoStatus.NO_ACCESS]) {
              ErrorHandling.notifyError(this.noContentErrorMessage(), this.navLang);
            }
            this.onDataEmpty(gridInfo);
            this.emitDataLoad();
            result();
            return;
          }
          this.refreshNewDataLoaded(gridInfo);
          if (this.data.length === 0 && this.page > 1) {
            this.noticeService.notice(
              this.translate.instantFrom(GridComponent.LANG_CONFIG, 'page-is-not-already-available', {
                page: this.page
              })
            );
            this.firstPage();
          }
          // Se agrega el agrupamiento (Parche)
          if (filter && filter.groupFields?.length > 0) {
            this.groupingExpressions = filter.groupFields;
          }
          this.dataRefresh.emit(true);
          this.emitDataLoad();
          result();
        },
        error: () => {
          this.onDataLoadFailed();
          reject();
        }
      });
  }

  public onExpressionsChange(event: IGroupingExpression[]) {
    const hasChanges = !equalsObject(this.groupingExpressions, event);
    if (!hasChanges) {
      return;
    }
    this.groupingExpressions = event;
    // Cuando es Tree Grid hay un error al setear el state para el groupBy
    // -- Error: TypeError: array is not iterable at IgxTreeGridGroupingPipe.groupBy
    if (this.persistState()) {
      this.saveGridState();
    }
  }

  public getTreeGridDefaultValue(renderizableColumn: RenderizableColumn<DataRowType>): SafeHtml {
    if (renderizableColumn.groupColumn && renderizableColumn.value) {
      const fieldKey = renderizableColumn.cell.row.data[HIDDEN_FIELD_NAME];
      const map = this.columnMap[keysObject(fieldKey)[0]];
      if (map?.render?.items) {
        const regex = /^(.*?)(\(\d+\))$/;
        const splittedValue: string[] = renderizableColumn.value.toString().match(regex);
        if (splittedValue) {
          const index = +splittedValue[1] || null;
          const key = map.render.labelKey;
          if (index) {
            const value = map.render.items.find((i) => i.value === index);
            const count = splittedValue[2];
            const text = value ? `${value[key]} ${count}` : `- ${count}`;
            return this.handleHighlightValue(text, renderizableColumn.column?.field, this.getRowDataId(renderizableColumn.cell), false);
          }
          if (splittedValue[1] && splittedValue[1].trim() === 'undefined') {
            splittedValue[1] = this.translate.instantFrom(FieldDisplayComponent.LANG_CONFIG, 'no-available-data-no-dot');
          }
          const text = splittedValue[1] && splittedValue[1].trim() !== '' ? `${splittedValue[1].trim()} ${splittedValue[2]}` : `- ${splittedValue[2]}`;
          return this.handleHighlightValue(text, renderizableColumn.column?.field, this.getRowDataId(renderizableColumn.cell), false);
        }
      }
    }
    return this.highlightDefaultText(renderizableColumn);
  }

  private onDataEmpty(gridInfo: GridInfo<DataRowType>) {
    this.emptyFilteredGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.empty_filtered_grid_message');
    this.emptyGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.empty_grid_message');
    this.loading = false;
    this.totalCount = gridInfo.count;
    this.data = gridInfo.data;
    this.updateCountInfo();
    this.buttonDeselection();
    this.lastUpdated = gridInfo.lastSyncDate || Date.now();
    this.detectChanges();
  }

  private isGlobalSearchEnabled(config = this.searchConfig) {
    return this.searchEnabled && config && !config.isGlobalSearch && config.searchText !== null && typeof config.searchText !== 'undefined' && config.searchText !== '';
  }

  /**
   * Entra aquí despues de traer información del backent desde `url`.
   *
   * @param gridInfo
   */
  public refreshNewDataLoaded(gridInfo: GridInfo<DataRowType>) {
    this.loading = false;
    this.totalCount = gridInfo.count;
    GridUtil.normalizeData({
      gridInfo,
      columns: this.columns,
      range: this.rangeSelector?.range,
      rangeSelectorColumns: this.rangeSelectorColumns(),
      rangeSelectorDataKey: this.rangeSelectorDataKey(),
      rangeSelectorDataColumnKey: this.rangeSelectorDataColumnKey(),
      localTimeForDates: this.localTimeForDates(),
      childDataKey: this.childDataKey(),
      allowShowMoreChilds: this.allowShowMoreChilds(),
      allowRangeSelectorColumns: this.allowRangeSelectorColumns
    });
    this.data = gridInfo.data;
    this.updateCountInfo();
    this.buttonDeselection();
    this.lastUpdated = gridInfo.lastSyncDate || Date.now();
    this.detectChanges();
    if (gridInfo.count > 0 && this.isGlobalSearchEnabled()) {
      this.grid.findNext(this.searchConfig.searchText, this.searchConfig.caseSensitive);
    }
  }

  private onDataLoadFailed() {
    this.emptyGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.error_grid_message');
    this.emptyFilteredGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.error_grid_message');
    this.loading = false;
    this.totalCount = 0;
    this.data = [];
    this.lastUpdated = Date.now();
    this.updateCountInfo();
    this.buttonDeselection();
    this.emitDataLoad();
    this.detectChanges();
    console.warn(`Could not load data for the grid ${this.id()} with url ${this._computedUrl}`);
  }

  undo() {
    this.grid.endEdit(false);
    this.grid.transactions.undo();
  }

  redo() {
    this.grid.transactions.redo();
  }

  onIconCellClick(cell: CellType): void {
    const column = this.columnMap[cell.column.field];
    if (!column.editable && !column.render?.callback) {
      return;
    }
    if (column.editable) {
      const nextIconValue = this.findNextIconValueEditable(cell.value, cell);
      this.grid.updateCell(nextIconValue, cell.id.rowID, column.field);
    } else if (column.render?.callback) {
      column.render?.callback(cell);
    }
  }

  renderLink(linkHref: string, linkParams: string[], rowData: DataRowType): string {
    let url = linkHref || 'javascript: void(0)';
    if (linkParams?.length > 0) {
      for (const param of linkParams) {
        let paramValue = rowData?.[param];
        if (paramValue === null || paramValue === 'undefined') {
          paramValue = '0';
        }
        const paramKey = `{${param}}`;
        url = url.replace(paramKey, encodeURIComponent(paramValue));
      }
    }
    return url;
  }

  private findNextIconValueEditable(value: number | string, cell: CellType): number | string {
    const column = this.columnMap[cell.column.field];
    const currentIconIndex = column.render.icons.findIndex((icon) => icon.value === value);
    let nextIndex: number;
    if (currentIconIndex === -1 || currentIconIndex + 1 === column.render.icons.length) {
      nextIndex = 0;
    } else {
      nextIndex = currentIconIndex + 1;
    }
    const nextIcon = column.render.icons[nextIndex];
    const nextIconValue = nextIcon.value;
    return nextIconValue;
  }

  getIconTitle(cell: GridCellType<DataRowType>, icon: GridIcon, column: GridColumn<DataRowType>, iconValue: string | number): string {
    const iconLabel = icon[column.render.labelKey];
    if (typeof column.render.getCustomIconTitle === 'function') {
      return column.render.getCustomIconTitle(cell.row.data, column as GridColumn<DataMap>, iconValue, iconLabel);
    }
    return iconLabel;
  }

  commit() {
    const changes: Transaction[] = this.grid.transactions.getAggregatedChanges(false);
    const editableFields = this.columns.filter((column) => column.editable).map((column) => column.field) || [];
    const mandatoryFields = this.columns.filter((column) => column.mandatoryField).map((column) => column.field) || [];
    const deleteIds = changes.filter((change: Transaction) => change.type === TransactionType.DELETE).map((change: Transaction) => change.id);
    const editItems = changes
      .filter((change) => change.type === TransactionType.UPDATE)
      .map((change) => {
        const recordData = this.grid.transactions.getAggregatedValue(change.id, true);
        const newValue: DataMap = {};
        newValue[this.primaryKey()] = recordData[this.primaryKey()];
        for (const field1 of editableFields.filter((field: string | number) => change.newValue[field] !== null && typeof change.newValue[field] !== 'undefined')) {
          newValue[field1] = change.newValue[field1];
        }
        for (const field1 of mandatoryFields.filter((field: string | number) => recordData[field] !== null && typeof recordData[field] !== 'undefined')) {
          newValue[field1] = recordData[field1];
        }
        return newValue;
      });
    if (deleteIds?.length > 0 && editItems?.length > 0) {
      forkJoin([
        this.api.post({
          url: this.deleteUrl(),
          postBody: deleteIds,
          options: null,
          handleFailure: true,
          cancelableReq: this.$destroy
        }),
        this.api.post({
          url: this.updateUrl(),
          postBody: editItems,
          options: null,
          handleFailure: true,
          cancelableReq: this.$destroy
        })
      ]).subscribe({
        next: () => {
          this.onSuccessCommit();
        },
        error: (result) => {
          console.error(`Could not commit data for the grid ${this.id()} with url [${this.deleteUrl()},${this.updateUrl()}]`, result);
          this.onFailedCommit();
        }
      });
    } else if (deleteIds?.length > 0) {
      this.api
        .post({
          url: this.deleteUrl(),
          postBody: deleteIds,
          options: null,
          handleFailure: true,
          cancelableReq: this.$destroy
        })
        .subscribe({
          next: () => {
            this.onSuccessCommit();
          },
          error: (result) => {
            console.error(`Could not delete data for the grid ${this.id()} with url ${this.deleteUrl()}`, result);
            this.onFailedCommit();
          }
        });
    } else if (editItems?.length > 0) {
      this.api
        .post({
          url: this.updateUrl(),
          postBody: editItems,
          options: null,
          handleFailure: true,
          cancelableReq: this.$destroy
        })
        .subscribe({
          next: () => {
            this.onSuccessCommit();
          },
          error: (result) => {
            console.error(`Could not update data for the grid ${this.id()} with url ${this.updateUrl()}`, result);
            this.onFailedCommit();
          }
        });
    }
    this.grid.transactions.commit(this.grid.data);
  }

  private onSuccessCommit() {
    this.grid.transactions.commit(this.grid.data);
    this.menuService.refreshMenu(true);
    this.refresh();
    this.noticeService.notice(this.i18n.success_save_message);
  }

  private onFailedCommit() {
    this.grid.transactions.clear();
    this.menuService.refreshMenu();
    this.dialogService.error(this.i18n.fail_save_message);
  }

  newGlobalSearch(config: GridSearchChange<DataRowType>) {
    if (config) {
      this.filteringExpressionsTree = config.filteringTree;
    } else {
      this.filteringExpressionsTree = null;
    }
    this.grid.filteringLogic = FilteringLogic.Or;
    this.allowFiltering = false;
  }

  public newFilteredSearch(config: GridSearchChange<DataRowType>) {
    if (config) {
      this.filteringExpressionsTree = config.filteringTree;
    } else {
      this.filteringExpressionsTree = null;
    }
    this.grid.filteringLogic = FilteringLogic.And;
    this.allowFiltering = true;
  }

  onChangeFilter(config: GridSearchChange<DataRowType>) {
    if (equalsObject(this.searchConfig, config)) {
      return;
    }
    if (config.isGlobalSearchByEnter) {
      this.resetGridSearch(false);
      this.newFilteredSearch(config);
      this.searchConfig = config;
      if (config.refreshData) {
        this.refreshData();
      }
    } else if (config.isGlobalSearch) {
      this.emptyFilteredGridMessage = this.translate.instantFrom(GridComponent.LANG_CONFIG, 'base.empty_filtered_grid_message_front');
      if (config.searchText === null || typeof config.searchText === 'undefined' || config.searchText === '') {
        if (config.refreshData) {
          this.resetGridSearch(true);
        }
        this.newFilteredSearch(config);
        this.searchConfig = config;
      } else {
        this.newGlobalSearch(config);
        this.searchConfig = config;
        this.grid.findNext(config.searchText, config.caseSensitive);
      }
    } else {
      this.resetGridSearch(false);
      this.newFilteredSearch(config);
      this.searchConfig = config;
      if (config.refreshData) {
        this.refreshData();
      }
    }
    this.detectChanges();
    this.saveGridSearch(config);
    if (this.persistState()) {
      this.saveGridState();
    }
    this.changedFilter.emit(true);
  }

  onNavigatePrev(search: GridSearchChange<DataRowType>) {
    if (search.searchText === '') {
      return;
    }
    this.grid.findPrev(search.searchText, search.caseSensitive);
  }

  onNavigateNext(search: GridSearchChange<DataRowType>) {
    if (search.searchText === '') {
      return;
    }
    this.grid.findNext(search.searchText, search.caseSensitive);
  }

  onOpenToolbarConfigUi(event: ToggleViewEventArgs) {
    const toggleRef: IgxToggleDirective = event.owner;
    if (toggleRef?.closed) {
      toggleRef.closed.pipe(first(), takeUntil(this.$destroy)).subscribe(() => this.detectChanges());
    }
  }

  resetGridSearch(refreshData = true) {
    this.grid.clearSearch();
    this.filteringExpressionsTree = null;
    this.grid.clearFilter();
    if (refreshData) {
      this.refreshData(1);
    } else {
      this.refreshNewDataLoaded({
        gridId: this.id(),
        data: this.data,
        parseFromDynamicResults: false,
        count: 0,
        status: GridUtil.GRID_INFO_STATUS_SUCCESS
      });
    }
  }

  public updateCountInfo() {
    if (this.totalCount <= this.perPage) {
      this.totalPages = 1;
    } else {
      this.totalPages = Math.abs(Math.ceil(this.totalCount / this.perPage));
    }
    this.defineMaxDropdownHeight();
  }

  getValueToText(
    fieldName: string,
    value: any,
    cell: CellType,
    labelValues: () => DataMap<DataMap<any>>,

    customFunction: Function,
    textWhenEmpty: string,
    allowEmptyValue: boolean
  ) {
    if (textWhenEmpty === null || typeof textWhenEmpty === 'undefined' || textWhenEmpty === '') {
      textWhenEmpty = '-';
    }
    if ((value === null || value === undefined) && !allowEmptyValue) {
      return textWhenEmpty;
    }
    if (cell && typeof customFunction !== 'undefined') {
      return customFunction(cell);
    }
    if (!labelValues || !labelValues()) {
      // Si no hay etiquetas en la función, devuelve una por defecto
      return this.translateService.instant(`${this.name()}.${fieldName}.${value}`);
    }
    const values = labelValues();
    if (Array.isArray(value)) {
      const seen: DataMap = {};
      return (value as any[])
        .map((v) => values[fieldName][v])
        .filter((item) => {
          // Se remueven duplicados
          return seen.hasOwnProperty(item) ? false : (seen[item] = true);
        })
        .join(', ');
    }
    if (value instanceof Set) {
      const seen: DataMap = {};
      return Array.from(value as Set<any>)
        .map((v) => values[fieldName][v])
        .filter((item) => {
          // Se remueven duplicados
          return seen.hasOwnProperty(item) ? false : (seen[item] = true);
        })
        .join(', ');
    }
    return values[fieldName][value] || '...';
  }

  getItemMapToText(render: ColumnRender, value: any) {
    let result;
    if (render?.itemsMap) {
      if (render.itemsMap[render.labelKey]) {
        return render.itemsMap[render.labelKey];
      }
      // Si no existe a nivel de itemsMap, se busca en concindencia con algun elemento interno
      for (const [_key, _value] of Object.entries(render.itemsMap)) {
        if (_value[render.valueKey] === value && _value[render.labelKey]) {
          result = _value[render.labelKey];
          break;
        }
      }
    }
    return result;
  }

  getRowData(rowID: string | number): DataRowType {
    return this.grid.getRowData(rowID);
  }

  private getRowDataId(cell: CellType): any {
    return cell?.row?.data[this.rowFieldId()] || null;
  }

  highlightValueToText(
    fieldName: string,
    value: any,
    cell: CellType,
    labelValues: () => DataMap<DataMap<any>>,

    customFunction: Function,
    textWhenEmpty: string,
    isTitle = false,
    allowEmptyValue = false
  ): SafeHtml {
    const text = this.getValueToText(fieldName, value, cell, labelValues, customFunction, textWhenEmpty, allowEmptyValue);
    return this.handleHighlightValue(text, fieldName, this.getRowDataId(cell), isTitle);
  }

  highlightDate(text: SafeHtml, cell: GridCellType<DataRowType>, col: GridColumn<DataRowType>, isTitle = false): SafeHtml {
    if (text === null || text === undefined) {
      return '';
    }
    let date: string;
    if (typeof col.formatter === 'function') {
      date = col.formatter(cell.value, cell.row.data);
    } else {
      date = DateUtil.format(cell.value, col.dateFormat);
    }
    return this.handleHighlightValue(date, col.field, this.getRowDataId(cell), isTitle);
  }

  highlightDefaultText(renderizableColumn: RenderizableColumn<DataRowType>, isTitle = false): SafeHtml {
    const val = renderizableColumn.value;
    let text: string;
    if (val !== null && typeof val !== 'undefined') {
      text = `${val}`;
      if (text === 'null') {
        text = text.replace('null', '');
      } else if (text === 'undefined') {
        text = text.replace('undefined', '');
      }
    } else {
      text = val ?? renderizableColumn.textWhenEmpty ?? '';
    }
    return this.handleHighlightValue(text, renderizableColumn.column?.field, this.getRowDataId(renderizableColumn.cell), isTitle);
  }

  highlightDefaultFormatter(renderizableColumn: RenderizableColumn<DataRowType>, isTitle = false): SafeHtml {
    const value = renderizableColumn.value;
    const text = renderizableColumn.column.formatter(value, renderizableColumn.cell.row.data) || renderizableColumn.textWhenEmpty;
    return this.handleHighlightValue(text, renderizableColumn.column?.field, this.getRowDataId(renderizableColumn.cell), isTitle);
  }

  highlightText(context: RenderizableColumn<DataRowType>, isTitle = false, isHtml = false): SafeHtml {
    const text: SafeHtml = context.value;
    const cell: GridCellType<DataRowType> = context.cell;
    const col: GridColumn<DataRowType> = context.column;
    if (typeof col.renderCell === 'function') {
      return this.handleHighlightValue(col.renderCell(cell), col.field, this.getRowDataId(cell), isTitle, isHtml);
    }
    if (text === null || text === undefined) {
      return '';
    }
    if (typeof col === 'undefined') {
      return this.handleHighlightValue(text, null, this.getRowDataId(cell), isTitle);
    }
    return this.handleHighlightValue(text, col.field, this.getRowDataId(cell), isTitle);
  }

  private handleHighlightValue(value: string | SafeHtml, fielName: string, rowId: any, isTitle = false, isHtml = false): SafeHtml {
    const searchText = this.searchComponent()?.searchValueSafe;
    if (this.searchEnabled && searchText !== null && typeof searchText !== 'undefined' && searchText !== '' && !isTitle && !isHtml) {
      return this.handleHighlight(value, searchText, fielName, rowId);
    }
    return value;
  }

  private handleHighlight(text: SafeHtml, value: any, _fielName: string, _rowId: any): SafeHtml {
    let highlightExpression: string;
    const lastSearchInfo = this._grid?.lastSearchInfo;
    // biome-ignore lint/complexity/useLiteralKeys: TODO: Fix this
    const matchInfoCache = this._grid?.['_matchInfoCache'];
    if (
      _fielName &&
      _rowId &&
      typeof lastSearchInfo?.activeMatchIndex === 'number' &&
      lastSearchInfo?.matchCount &&
      matchInfoCache?.[lastSearchInfo.activeMatchIndex]?.column === _fielName &&
      matchInfoCache?.[lastSearchInfo.activeMatchIndex]?.row?.[this.rowFieldId()] === _rowId
    ) {
      highlightExpression = '<span class="igx-highlight igx-highlight--active igx-highlight__active">$1</span>';
    } else {
      highlightExpression = '<span class="igx-highlight igx-highlight">$1</span>';
    }
    const searchRegex = DomUtil.buildSearchToken(value);
    text = this.sanitizer.bypassSecurityTrustHtml((text || '').toString().replace(searchRegex, highlightExpression));
    return text;
  }

  public nextPage() {
    if (this.page === this.totalPages) {
      return;
    }
    this.page++;
    const top = this.perPage;
    this.refreshData(this.page, top);
    //Para que la paginación funcione en el valueGrid cuando no se le paso una dataUrl
    this.buttonDeselection();
  }

  public lastPage() {
    this.paginate(this.totalPages);
  }

  public firstPage() {
    this.paginate(1);
  }

  public previousPage() {
    if (this.page === 1) {
      return;
    }
    this.page--;
    const top = this.perPage;
    this.refreshData(this.page, top);
    this.buttonDeselection();
  }

  private paginate(page: number) {
    const top = this.perPage;
    this.refreshData(page, top);
    const hasChanges = page !== this.page;
    this.page = page;
    if (hasChanges) {
      this.saveGridState();
    }
    this.buttonDeselection();
  }

  private paginateAsync(page: number): Promise<void> {
    return new Promise((succ, reject) => {
      const top = this.perPage;
      this.refreshData(page, top)
        .then(() => {
          const hasChanges = page !== this.page;
          this.page = page;
          if (hasChanges) {
            this.saveGridState();
          }
          this.buttonDeselection();
          succ();
        })
        .catch((e) => reject(e));
    });
  }

  public buttonDeselection() {
    if (this.totalPages === 1) {
      this.isLastPage = true;
      this.isFirstPage = true;
    } else if (this.page === this.totalPages) {
      this.isLastPage = true;
      this.isFirstPage = false;
    } else if (this.page === 1) {
      this.isLastPage = false;
      this.isFirstPage = true;
    } else {
      this.isLastPage = false;
      this.isFirstPage = false;
    }
  }

  public setPerPage(val: number) {
    this.page = 1;
    const hasChanges = val !== this.perPage;
    this.perPage = val;
    if (hasChanges) {
      this.saveGridState();
    }
    this.updateCountInfo();
    this.paginate(this.page);
    this.currentDataPerPage = val;
  }

  toggleMenu(item: DropdownMenuItem, cell: GridCellType<DataRowType>, render: ColumnRender) {
    const itemToggle: GridDropDownItem = {
      item: item,
      row: cell.row.data, // <-- Se usa una referencia intencionalmente (En lugar de un "clone")
      key: cell.row.key
    };
    if (typeof render.toggleMenuAction === 'function') {
      render.toggleMenuAction(itemToggle);
    }
    this.dropdownMenu.emit(itemToggle);
  }

  toggleMenuActionStrip(item: DropdownMenuItem, row: GridRowType<DataRowType>, render: ColumnRender) {
    const itemToggle: GridDropDownItem = {
      item: item,
      row: row.data, // <-- Se usa una referencia intencionalmente (En lugar de un "clone")
      key: row.key
    };
    if (typeof render.toggleMenuAction === 'function') {
      render.toggleMenuAction(itemToggle);
    }
    this.dropdownMenu.emit(itemToggle);
  }

  castRender(render: ColumnRender): ColumnRender {
    return render;
  }

  /**
   * Se utiliza para que el template (html) interprete correctamente el valor
   * de una variable como "GridColumn", necesario para refactorizar.
   **/
  castAsGridColumn(untypedGridColumn: any): GridColumn {
    if (untypedGridColumn) {
      console.log(untypedGridColumn);
    }
    return untypedGridColumn as GridColumn;
  }

  /**
   * Se utiliza para que el template (html) interprete correctamente el valor
   * de una variable como "RenderizableColumn", necesario para refactorizar.
   **/
  castAsRenderizableColumn(untypedRenderizableColumn: any): RenderizableColumn<DataRowType> {
    return untypedRenderizableColumn as RenderizableColumn<DataRowType>;
  }

  /*
  getAllowMultiple(values: any, values2: any) {
    return values && values2;
  }
  */
  /**
   * Se utiliza para que el template (html) interprete correctamente el valor
   * de una variable como "RenderizableSummary", necesario para refactorizar.
   **/
  castAsRenderizableSummary(untypedRenderizableSummary: any): RenderizableSummary<DataRowType> {
    return untypedRenderizableSummary as RenderizableSummary<DataRowType>;
  }

  /**
   * Se utiliza para que el template (html) interprete correctamente el valor
   * de una variable como "RenderizableGroup", necesario para refactorizar.
   **/
  castAsRenderizableGroup(untypedRenderizableGroup: any): RenderizableGroup {
    return untypedRenderizableGroup as RenderizableGroup;
  }

  /**
   * Se utiliza para que el template (html) interprete correctamente el valor
   * de una variable como "number", necesario para refactorizar.
   **/
  castAsNumber(untypedNumber: any): number {
    return untypedNumber as number;
  }

  getAvatarSrc(args: RenderizableColumn<DataRowType>): string {
    if (args.column.hasImage) {
      return args.value || '';
    }
    if (NumberUtil.isInteger(args.cell.row.data[args.column.avatarIdKey])) {
      return RestApiModule.avatar(+args.cell.row.data[args.column.avatarIdKey]) || '';
    }
    return '';
  }

  getAvatarSrcById(avatarId: string): string {
    return RestApiModule.avatar(+avatarId);
  }

  getAvatarInitials(args: RenderizableColumn<DataRowType>): string {
    if (args.cell.row.data[args.column.avatarNameKey]) {
      return nameInitials(args.cell.row.data[args.column.avatarNameKey]);
    }
    return null;
  }

  getAvatarInitialsByName(name: string) {
    return nameInitials(name);
  }

  getAvatarInfo(args: RenderizableColumn<DataRowType>): AvatarUserInfo[] {
    const avatarNames = args.cell.row.data[args.column.avatarNameKey]?.split(',');
    const avatarIds = args.cell.row.data[args.column.avatarIdKey]?.split(',');

    const avatarInfo: AvatarUserInfo[] = [];

    if (!avatarNames || !avatarIds) {
      return avatarInfo;
    }
    for (const id1 of avatarIds) {
      const index = avatarIds.indexOf(id1);
      avatarInfo.push({ avatarId: id1, avatarName: avatarNames[index] });
    }

    return avatarInfo;
  }

  getAvatarName(args: RenderizableColumn<DataRowType>): string {
    return args.cell.row.data[args.column.avatarNameKey] || null;
  }

  getAvatarBgColor(args: RenderizableColumn<DataRowType>): string {
    if (args.cell.row.data[args.column.avatarNameKey]) {
      return stringColors(args.cell.row.data[args.column.avatarNameKey]).bgColor;
    }
    return null;
  }

  getAvatarColor(args: RenderizableColumn<DataRowType>): string {
    if (args.cell.row.data[args.column.avatarNameKey]) {
      return stringColors(args.cell.row.data[args.column.avatarNameKey]).color;
    }
    return null;
  }

  getAvatarProperty(args: RenderizableColumn<DataRowType>, key: string): string {
    return args.cell.row.data[key] || null;
  }

  rowId(row: GridCellType<DataRowType>, column: GridColumn<DataRowType>, render: ColumnRender) {
    if (typeof render.rowIdentifierKey === 'undefined') {
      return `grid-dropdown-menu-${column.positionIndex}`;
    }
    return `grid-dropdown-menu-${render.rowIdentifierKey}-${row.row.data[render.rowIdentifierKey]}`.toLowerCase();
  }

  public selectRows(rowIDs: any[], clearCurrentSelection?: boolean) {
    this.grid.selectRows(rowIDs, clearCurrentSelection);
  }

  showMoreChilds(context: RenderizableColumn<DataRowType>, parentId: number, perPage?: number) {
    if (!context.cell.row.parent) {
      console.error(`Invalid row! missing parent row, parentId: ${parentId}`, ', row: ', context.cell.row);
      return;
    }
    if (this.debug) {
      console.log(`showing more childs for ${this.id()} and parentId ${parentId}, perPage ${perPage}.`);
    }
    this.$cancelableSearch.next(true);
    this.loading = true;
    const parent: DataMap = this.data.find((row) => +row[this.rowFieldId()] === parentId) || {};
    const filter = getEmpyFilters(this.id(), this.translateService.componentName, perPage, this.allowDynamicSearchColumns);
    if (!filter || !this._computedUrl) {
      this.loading = false;
      this.emitDataLoad();
      return;
    }
    filter.gridShowMore = {
      parentId: parentId,
      noopPageIds: [],
      currentPage: 0,
      showMoreAvailable: true
    };
    filter.gridShowMore.noopPageIds = (parent[this.childDataKey()] as any[])?.map((row) => row[this.rowFieldId()] as number) || [];
    this.api
      .post({
        url: this._computedUrl,
        postBody: filter,
        options: null,
        handleFailure: true,
        cancelableReq: this.$cancelableSearch
      })
      .pipe(takeUntil(this.$cancelableSearch))
      .subscribe({
        next: (gridInfo: GridInfo<DataRowType>) => {
          if (!gridInfo || !gridInfo.data || gridInfo.count === 0) {
            this.loading = false;
            this.detectChanges();
            return;
          }
          GridUtil.normalizeData({
            gridInfo,
            columns: this.columns,
            range: this.rangeSelector?.range,
            rangeSelectorColumns: this.rangeSelectorColumns(),
            rangeSelectorDataKey: this.rangeSelectorDataKey(),
            localTimeForDates: this.localTimeForDates(),
            rangeSelectorDataColumnKey: this.rangeSelectorDataColumnKey(),
            childDataKey: this.childDataKey(),
            allowShowMoreChilds: this.allowShowMoreChilds(),
            allowRangeSelectorColumns: this.allowRangeSelectorColumns
          });
          if (Array.isArray(parent[this.childDataKey()])) {
            // Se elimina el child que contiene el botón "SHOW_MORE_CHILDS"
            const arrayChilds = parent[this.childDataKey()] as DataMap[];
            const indexShowMore = arrayChilds.findIndex((i) => i.hasOwnProperty('invalidRowChild'));
            if (indexShowMore !== -1) {
              arrayChilds.splice(indexShowMore, 1);
            }
            // Se agregan la información de childs consultados
            arrayChilds.push(...gridInfo.data);
          }
          this.data = cloneObject(this.data); // <-- Necesario para que el grid refresque renglones
          this.refreshNewDataLoaded({
            count: this.totalCount + gridInfo.count,
            data: this.data,
            gridId: this.id(),
            parseFromDynamicResults: false,
            status: GridUtil.GRID_INFO_STATUS_SUCCESS
          });
          this.dataRefresh.emit(true);
          this.emitDataLoad();
          for (const row of this._igxTreeGrid.records.values()) {
            if (row.data[this.rowFieldId()] === parentId) {
              this._igxTreeGrid.gridAPI.set_row_expansion_state(row.key, true);
            }
          }
        },
        error: (e) => {
          console.error('Error al traer más registros!', e);
        }
      });
  }

  public deselectRows(rowIDs: any[]) {
    this.grid.deselectRows(rowIDs);
  }

  public deselectAllRows(onlyFilterData?: boolean): void {
    this.grid.deselectAllRows(onlyFilterData);
  }

  public navigateTo(rowIndex: number, visibleColIndex = -1, cb: (args: any) => void = null) {
    this.grid.navigateTo(rowIndex, visibleColIndex, cb);
  }

  public sort(expression: ISortingExpression | ISortingExpression[]): void {
    this.grid.sort(expression);
  }

  public endEdit(commit?: boolean, event?: Event): void {
    this.grid.endEdit(commit, event);
  }

  public reflow(): void {
    this.grid.reflow();
  }

  public collapseAll(): void {
    this.grid.collapseAll();
  }

  public expandAll(): void {
    this.grid.expandAll();
  }

  public expandGroups(): void {
    if (this.isTreeGrid) {
      console.error(`Expand unsupported of a 'grid-tree', try another way!`);
    } else {
      (this.grid as IgxGridComponent).groupsExpanded = true;
      this.notifyChanges();
    }
  }

  public collapseGroups(): void {
    if (this.isTreeGrid) {
      console.error(`Collapse unsupported! of a 'grid-tree', try another way!`);
    } else {
      (this.grid as IgxGridComponent).groupsExpanded = false;
      this.notifyChanges();
    }
  }

  public groupBy(groups: IGroupingExpression[]): void {
    if (this.isTreeGrid) {
      console.error(`GroupBy unsupported! of a 'grid-tree', try another way!`);
    } else if (this.grid) {
      (this.grid as IgxGridComponent).groupBy(groups);
      this.notifyChanges();
    }
  }

  /**
   * Este metodo replica la funcionalidad existente en la siguiente liga, se copió tal cual como resultado
   * de revisar los metodos `expandAll`y `collapseAll`.
   *
   * https://github.com/IgniteUI/igniteui-angular/blob/5a9e304cbce13aeb16e8aa7e41ae5f78b2e4c9ac/projects/igniteui-angular/src/lib/grids/grid-base.directive.ts#L3701
   */
  public notifyChanges(): void {
    if (!this.grid) {
      return;
    }
    this.grid.markForCheck();
    this.grid.notifyChanges(true);
    this.grid.cdr.detectChanges();
  }

  public detectChanges(): void {
    this.cdr.detectChanges();
    this.grid.cdr?.detectChanges();
  }

  public isDate(value: any) {
    if (value instanceof Date) {
      return true;
    }
    return false;
  }

  formatTimestamp(value: number) {
    return DateUtil.format(value, this.timestampFormat);
  }

  protected override afterWindowResized(event: Event) {
    super.afterWindowResized(event);
    this.displayDensity = this.isSmallTouchDevice ? 'cosy' : 'compact';
    this.autoDefineHeight();
    this.cdr.detectChanges();
  }

  getFilters(): GridFiltersExport {
    const filter = getFilters({
      gridId: this.id(),
      dynamicSearchEnabled: this.allowDynamicSearchColumns,
      componentName: this.translateService.componentName,
      index: 1,
      perPage: 0,
      filteringExpressionsTree: this.filteringExpressionsTree,
      sortingExpressions: this.grid ? this.grid.sortingExpressions : null,
      groupingExpressions: this.grid ? (this.grid as IgxGridComponent).groupingExpressions : null,
      allowFiltering: this.allowFiltering,
      columnMap: this.columnMap,
      datePipe: this.datePipe,
      dateFormat: this.dateFormat,
      escapeUnderscore: this.escapeUnderscore(),
      masterId: this.masterId() || null
    });
    filter.pageSize = 0;
    return {
      filter: filter,
      url: this._computedUrl
    };
  }

  columnName(key: string): string {
    if (!this.columnMap) {
      return null;
    }
    return this.columnMap[key]?.header || null;
  }

  groupedValueBy(records: GroupedRecords, groupedValueKey: string, agg: AggregateFunctionType): string {
    let value: number;
    switch (agg) {
      case 'COUNT':
        value = +records.length;
        break;
      case 'SUM':
        value = records.reduce((acc, record) => +acc + (+record[groupedValueKey] || 0), 0);
        break;
      case 'AVG':
        const sum = records.reduce((acc, record) => +acc + (+record[groupedValueKey] || 0), 0);
        value = sum / records.length || 0;
        break;
      case 'MIN':
        value = Math.min(...records.map((record) => +record[groupedValueKey]));
        break;
      case 'MAX':
        value = Math.max(...records.map((record) => +record[groupedValueKey]));
        break;
    }
    const headerName = this.columnName(groupedValueKey);
    if (typeof headerName === 'string') {
      return `${headerName}: ${value?.toLocaleString?.(this.locale || undefined) || '0'}`
    }
    return value?.toLocaleString?.(this.locale || undefined) || '0';
  }

  limitPining(event) {
    let columnsPinnedWidth;
    if (this.grid.pinnedColumnsCount > 0) {
      columnsPinnedWidth = this.grid.pinnedColumns.map((m) => m.calcPixelWidth).reduce((x, y) => x + y);
    }
    if (columnsPinnedWidth >= this.grid.calcWidth - 100) {
      this.grid.unpinColumn(event.column.field);
    }
  }

  getOverlaySettings() {
    if (this.isSmallTouchDevice) {
      return this.overlaySettingsScaleCenter;
    }
    return this.overlaySettingsAuto;
  }

  private evaluateDynamicFields(): void {
    if (!this._configReady) {
      return;
    }
    if (this.allowDynamicSearchColumns) {
      this.reloadGridWithDynamicFields();
    }
  }

  /**
   * Recarga las columnas de campos dinámicos y refresca el grid
   */
  private reloadGridWithDynamicFields(): void {
    this.loadDynamicFields().then(() => {
      this.refreshData();
      this.notifyChanges();
    });
  }

  public forceNewUrl(url: string) {
    this.url = url;
    if (this.allowDynamicSearchColumns) {
      this.evaluateDynamicFields();
    } else {
      this.refreshData();
      this.notifyChanges();
    }
  }

  toggleVisibleFieldValueText(context: RenderizableColumn<DataRowType>): boolean {
    if (typeof context.column?.visibleField === 'function') {
      return context.column?.visibleField(this.visibleColumnValueToText, context.cell).visible;
    }
    return true;
  }

  onImportedExcel(data: GridImportData<DataRowType>) {
    this.importedExcel.emit(data);
  }

  public showTooltipCell(_context: RenderizableColumn<DataRowType>): void {
    this.tooltipRowSelected = _context.cell.row.index;
  }

  public isVisibleTooltipCell(_context: RenderizableColumn<DataRowType>): boolean {
    return this.rowTooltipEnabled() && typeof this.tooltipRowSelected === 'number' && +_context.index === this.tooltipRowSelected;
  }

  get cellTooltipTargetId(): string {
    //Se le agrega la condicion de dataGrid para evitar que se dupliquen entre dataGrid y valueGrid
    if (!this.rowTooltipEnabled()) {
      return null;
    }
    return `#cellTooltip${this.tooltipRowSelected || 0}${this.id()}`;
  }

  public getCellTooltipId(context: RenderizableColumn<DataRowType>): string {
    //Se le agrega la condicion de dataGrid para evitar que se dupliquen entre dataGrid y valueGrid
    if (!this.rowTooltipEnabled()) {
      return null;
    }
    return `cellTooltip${context.cell.row?.index || 0}${this.id()}`;
  }

  hideTooltip() {
    this.tooltipRowSelected = null;
  }

  openDialogHiddenColumns() {
    this.dialogHiddingColumns()?.open();
  }

  openHiddenColumnsTreeGrid() {
    if (this.toggleHiddenTreeGrid()?.collapsed) {
      this.toggleHiddenTreeGrid().toggle();
    }
  }

  public showSubActionStrip(context): boolean {
    if (context) {
      return !context._data.invalidRowChild;
    }
    return false;
  }

  public markdownTextFormatter(value: string): string {
    try {
      if (value === '') {
        return '';
      }
      return this._parseSafeJson(value)?.text;
    } catch (error) {
      console.error(error);
      return '';
    }
  }

  public onGroupingExpressionsChange(_event: IGroupingDoneEventArgs) {
    this.saveGridState();
  }

  public onSingleValueCellTextChange(event, context): void {
    if (event.target.value) {
      context.cell.value = +event.target.value;
    } else {
      context.cell.value = null;
    }
    this.cellEditValueToTextChange.emit({
      selected: context.cell.value,
      cell: context.cell
    });
  }

  public onClearSearchGlobalFilters(): void {
    this.clearSearchGlobalFilters.emit();
  }

  public onGridScroll(_event) {
    if (this.grid?.actionStrip) {
      this.showActionStrip = false;
    }
  }

  public onGridMouseMove(_event) {
    if (this.grid) {
      if (!this.showActionStrip) {
        this.showActionStrip = true;
        this.cdr.detectChanges();
      }
    }
  }

  private saveColumnOriginalState(columns: GridColumn<DataRowType>[]) {
    this.columnsOriginalState = {};
    for (let i = 0, l = columns.length; i < l; i++) {
      this.columnsOriginalState[columns[i].field] = i;
    }
  }

  protected getMapsUrl(args: RenderizableColumn<DataRowType>) {
    if (!args?.value) {
      console.error('No se encontraron coordenadas para abrir el mapa');
      return;
    }
    const coords = args.value.replace('[GPS]:', '').split(',');
    if (!coords?.length || coords.length < 2) {
      console.error('Coordenadas inválidas para abrir el mapa', args.value);
      return;
    }
    const latitude = coords[0];
    const longitude = coords[1];
    const url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
    window.open(url, '_blank');
  }
}
