import type { IGridState, IGroupingExpression } from '@infragistics/igniteui-angular';
import type { GridSearchChange } from '../grid/grid-search/grid-search.interfaces';
import type { GridColumnState } from '../grid/utils/grid-column';
import type { DataMap } from '../utils/data-map';

export interface GridConfigDTO<DataRowType = DataMap<any>> {
  stateSerialized: string | IGridState;
  resultsPerPage: number;
  currentPage: number;
  searchConfig: GridSearchChange<DataRowType>;
  groupingExpressions: IGroupingExpression[];
  columnsState: GridColumnState[];
}
