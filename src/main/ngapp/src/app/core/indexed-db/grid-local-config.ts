import type { LocalStorageItem } from '../local-storage/local-storage-enums';
import { LocalStorageSession } from '../local-storage/local-storage-session';
import type { DataMap } from '../utils/data-map';
import { cloneObject, keysObject } from '../utils/object';
import type { GridConfigDTO } from './grid-local-config.interfaces';
import { IndexedDbItem, IndexedDbUtil } from './indexed-db-util';
export class GridLocalConfig {
  private static readonly type = IndexedDbItem.GRID;

  private static getDefaultValue(): GridConfigDTO {
    return {
      stateSerialized: null,
      resultsPerPage: null,
      currentPage: null,
      searchConfig: null,
      groupingExpressions: null,
      columnsState: null
    };
  }

  public static getValue(id: string): Promise<GridConfigDTO> {
    return new Promise<GridConfigDTO>((resolve, reject) => {
      const value = IndexedDbUtil.getValue(GridLocalConfig.type, id);
      value.then(
        (result) => {
          let safeResult: GridConfigDTO = result;
          if (keysObject(safeResult).length === 0) {
            safeResult = GridLocalConfig.getDefaultValue();
          }
          if (safeResult.resultsPerPage === null || typeof safeResult.resultsPerPage === 'undefined') {
            safeResult.resultsPerPage = null;
          }
          if (safeResult.searchConfig === null || typeof safeResult.searchConfig === 'undefined') {
            safeResult.searchConfig = null;
          }
          if (safeResult.currentPage === null || typeof safeResult.currentPage === 'undefined') {
            safeResult.currentPage = null;
          }
          if (safeResult.stateSerialized === null || typeof safeResult.stateSerialized === 'undefined') {
            safeResult.stateSerialized = null;
          }
          if (safeResult.groupingExpressions === null || typeof safeResult.groupingExpressions === 'undefined') {
            safeResult.groupingExpressions = null;
          }
          if (safeResult.columnsState === null || typeof safeResult.columnsState === 'undefined') {
            safeResult.columnsState = null;
          }
          resolve(safeResult);
        },
        () => reject(GridLocalConfig.getDefaultValue())
      );
    });
  }

  public static hasLocalValue(id: string): boolean {
    const storageKey = IndexedDbUtil.getStorageKey(GridLocalConfig.type, id);
    return LocalStorageSession.hasValue(`indexDb_${storageKey}` as LocalStorageItem);
  }

  public static hasValue(id: string): Promise<boolean> {
    return IndexedDbUtil.hasValue(GridLocalConfig.type, id);
  }

  public static setValue<DataRowType = DataMap<any>>(type: GridConfigDTO<DataRowType>, id: string): void {
    type = cloneObject(type, ['column', 'filteringTree']);
    IndexedDbUtil.setValue(GridLocalConfig.type, type, id).finally(() => {
      const storageKey = IndexedDbUtil.getStorageKey(GridLocalConfig.type, id);
      LocalStorageSession.setValue(`indexDb_${storageKey}` as LocalStorageItem, true);
    });
  }

  public static clear(id: string): void {
    const storageKey = IndexedDbUtil.getStorageKey(GridLocalConfig.type, id);
    LocalStorageSession.clearValue(`indexDb_${storageKey}` as LocalStorageItem);
    IndexedDbUtil.clearValue(GridLocalConfig.type, id).finally(() => {});
  }
}
