import { type ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { MockCoreModule } from '../test/mock-core.module';
import { SelectComponent } from './select.component';

import { MockProviders } from '../test/mock-providers';
import { configureTestSuite } from '../test/utils/configure-test-suite';

describe('SelectComponent', () => {
  let component: SelectComponent<number | string>;
  let fixture: ComponentFixture<SelectComponent<number | string>>;

  configureTestSuite();

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [MockCoreModule],
      providers: MockProviders.PROVIDERS
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SelectComponent<number | string>);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('datasource should advert of invalid values', () => {
    component.data = [null];
    expect(component.data.length).toEqual(1);
    expect(component.data[0] === null).toBeFalsy();
    expect(component.data[0][component.displayKey()]).toEqual(SelectComponent.DS_ERROR);
  });
});
