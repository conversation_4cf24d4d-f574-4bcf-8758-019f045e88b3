@use 'src/styles/immutable-colors' as *;
//Los colores del componente se pueden configurar desde el archivo componentColors.scss en la carpeta src

:host .multi-select-container {
  > label {
    padding: 1rem;
  }
  &.has-values {
    background: #f5f5f5;
    border-radius: 0.5rem;
    max-height: 40rem;
  }
}

igx-chips-area {
  max-height: 39rem;
  overflow: auto;
}

:host ::ng-deep {
  .chip-white-text {
    color: $bg-color;
  }
  .a-chip {
    position: relative;

    .igx-badge {
      position: absolute;
      top: 0;
      left: -0.25rem;
      background-color: $bg-color;
      border: 1px solid rgba($bg-color, 0.25);

      igx-icon {
        color: #676767;
        font-size: 1rem;
      }
    }
  }
}

::ng-deep {
  .igx-input-group--filled .multi-select-container igx-combo input[igxInput] {
    visibility: hidden;
  }
  .multi-select-container .igx-chip__item {
    max-width: 20rem;
    min-width: 3rem;
  }
  .igx-input-group--empty .multi-select-container .igx-input-group__label {
    display: none;
  }
}

:host {
  padding-top: 1.125em;
}

::ng-deep .igx-combo .igx-input-group__bundle {
  padding-top: 0px;
}
::ng-deep .multi-select-chip-area.igx-chip-area {
  .igx-chip {
    display: contents;

    > div {
      margin: 0.25rem;
      max-width: 15rem;
    }
  }
}

.multi-select-container .igx-input-group__label {
  cursor: pointer;
}

.multi-select-container .igx-input-group__hint {
  cursor: default;
}

.multi-select-item {
  .item__name {
    font-size: 0.95rem;
  }
  .item__secondary {
    font-size: 0.75rem;
    color: #808080;
    line-height: 0.75rem;
  }
}

.multi-select-item-buttons {
  padding: 0.5rem;
  display: flex;
  justify-content: flex-end;
  border-top: 1px dashed #0000001f;
}

.header-class {
  text-align: center;
  font-style: italic;
  font-weight: bold;

  .item__name {
    font-size: 0.95rem;
  }
}
