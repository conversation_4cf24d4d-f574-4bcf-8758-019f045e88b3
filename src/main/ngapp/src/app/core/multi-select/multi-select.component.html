<div class="multi-select-container igx-input-group" [class.has-values]="hasValues">
  <label #inputLabel class="igx-input-group__label" (click)="toggleValue()">{{ label() }}</label>
  <div class="igx-input-group__hint">
    <div class="igx-input-group__hint-item--start">
      <igx-chips-area #chipsArea class="chiparea multi-select-chip-area grid-x" (moveStart)="clickedOutside()" (reorder)="chipsOrderChanged($event)">
        @for (chipValue of chipValues; track chipValue) {
          <span class="a-chip">
            <igx-chip
              class="cell auto"
              (remove)="chipRemoved(chipValue)"
              [color]="chipColor(chipValue)"
              [ngClass]="displayDensityClass"
              [draggable]="draggable()"
              [id]="castAsString(chipValue)"
              [removable]="true"
            >
              <span #label class="igx-chip__text" [class.chip-white-text]="!chipColorBlack(chipValue)" [title]="chipText(chipValue)"> {{ chipText(chipValue) }} </span>
            </igx-chip>
            @if (isBadgeAvailable('chip' + chipValue)) {
              <igx-badge type="warning" [icon]="badgeIcon()['chip' + chipValue]"> </igx-badge>
            }
          </span>
        }
      </igx-chips-area>
    </div>
  </div>
  <igx-combo
    #combo
    (selectionChanging)="onSelectionChange($event)"
    displayKey="searchKey"
    type="line"
    [(ngModel)]="value"
    [allowCustomValues]="false"
    [data]="data | filterGeneric: filterItems : filterValue() : filterKey()"
    [disabled]="disabled"
    [ngClass]="displayDensityClass"
    [disableFiltering]="false"
    [groupKey]="groupKey()"
    [overlaySettings]="overlaySettings"
    [placeholder]="label() + (required ? '*' : '')"
    [required]="required"
    [searchPlaceholder]="searchPlaceholder"
    [valueKey]="valueKey()"
  >
    <ng-template igxComboHeader>
      @if (header) {
        <div class="ig-typography igx-drop-down__header">{{ header }}</div>
      }
      <ng-content select="[igxComboHeader]"></ng-content>
    </ng-template>
    <ng-template igxComboFooter>
      <div class="multi-select-item-buttons">
        <button [igxButton]="'contained'" type="button" igxRipple (click)="combo.close()">{{ closeLabel }}</button>
      </div>
    </ng-template>
    <ng-template igxComboItem let-item>
      <div class="multi-select-item">
        <div class="item__name">{{ item[displayMainKey()] }}</div>
        @if (displaySecondaryKey) {
          <div class="item__secondary">{{ item[displaySecondaryKey] }}</div>
        }
      </div>
    </ng-template>
    <igx-combo>
      <ng-template igxComboEmpty>
        <span class="empty-class">{{ emptyCombo }}</span>
      </ng-template>
    </igx-combo>
  </igx-combo>
  @if (showHelp()) {
    <div class="help">
      <div class="general">
        <h4>{{ label() }}</h4>
        <span [innerHtml]="help()"></span>
      </div>
      @for (item of data; track item) {
        <div>
          <h5>{{ item[displayMainKey()] }}</h5>
          @if (item['item']; as help) {
            <span [innerHtml]="help"></span>
          }
        </div>
      }
    </div>
  }
</div>
