@use 'src/styles/mediaQueries' as *;
//Los colores del componente se pueden configurar desde el archivo componentColors.scss en la carpeta src
.fix {
  &.igx-input-group {
    padding-top: 1.5rem;
  }
  &--dropdown-search-multiple {
    display: flex;
    flex-wrap: wrap;

    > .fix {
      padding-top: 0.5rem;
      padding-right: 0.5rem;
      padding-bottom: 0;
      padding-left: 0;
    }
  }
}
.grid-padding-x > .cell {
  &.input-double,
  &.igx-switch,
  &.input-number,
  &.input-textarea {
    padding-left: 0rem;
  }
  &.igx-switch {
    display: flex;
  }
}
