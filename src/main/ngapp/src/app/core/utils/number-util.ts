export function isNumber(value: any): boolean {
  if (value === null || typeof value === 'undefined') {
    return false;
  }
  if (typeof value === 'number') {
    return true;
  }
  if (typeof value === 'object') {
    return false;
  }
  if (typeof value === 'boolean') {
    return false;
  }
  if (+value === 0) {
    return true;
  }
  const n = +Number(value);
  return n > 0 || n < 0;
}
export function isInteger(value: any): boolean {
  if (value === null || typeof value === 'undefined') {
    return false;
  }
  if (typeof value === 'number') {
    return true;
  }
  if (value && typeof value === 'object') {
    return false;
  }
  if (typeof value === 'boolean') {
    return false;
  }
  if (Number.isInteger(value)) {
    return true;
  }
  if (Number.isInteger(+value)) {
    return true;
  }
  if (Array.isArray(value)) {
    return false;
  }
  return (
    value
      .toString()
      .trim()
      .match(/^[0-9]+$/) !== null
  );
}

function countDecimals(value: number): number {
  if (value === null || typeof value === 'undefined') {
    return 0;
  }
  if (value % 1 === 0) {
    return value;
  }
  const numberParts = value.toString().split('.');
  if (!numberParts || numberParts.length <= 1) {
    return value;
  }
  return numberParts[1].length || 0;
}

export function round(value: number, decimals = 2): number {
  if (decimals === null || typeof decimals === 'undefined' || decimals < 0) {
    return value;
  }
  const numberDecimals = countDecimals(value);
  if (numberDecimals === 0 || numberDecimals <= decimals) {
    return value;
  }
  const rounded = Number(`${Math.round(+`${value}e${decimals}`)}e-${decimals}`);
  return Number(rounded.toFixed(decimals));
}
