import { Component, Input, type OnInit, inject, input, output, viewChild } from '@angular/core';
import { Direction, type ISlideEventArgs, IgxCarouselComponent, IgxDialogComponent, IgxSlideComponent } from '@infragistics/igniteui-angular';
import { BnextCoreComponent } from '../bnext-core.component';
import type { FileEntity } from '../grid/utils/grid.interfaces';

import { DatePipe } from '@angular/common';
import { BnextTranslatePipe } from '../i18n/bnext-translate.pipe';
import { ImageVisualizerComponent } from '../image-visualizer/image-visualizer.component';
import { AppService } from '../services/app.service';

@Component({
  selector: 'app-image-previewer',
  templateUrl: './image-previewer.component.html',
  styleUrls: ['./image-previewer.component.scss'],
  imports: [IgxDialogComponent, IgxCarouselComponent, IgxSlideComponent, BnextTranslatePipe, ImageVisualizerComponent]
})
export class ImagePreviewerComponent extends BnextCoreComponent implements OnInit {
  datePipe = inject(DatePipe);

  service = inject(AppService);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public restApiController: string;
  public readonly images = input<FileEntity[]>([]);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public imageToShow: number;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public width: number;

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public height: number;
  public readonly castToRem = input<boolean>(undefined);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input()
  public titleDialog = null;
  public readonly slideOnChange = output<ISlideEventArgs>();
  public readonly slideClosed = output<boolean>();
  readonly carouselImages = viewChild<IgxCarouselComponent>('carouselImages');

  public previewImgOpen = false;
  public sizeType = 'px';

  public openDialog() {
    this.previewImgOpen = true;
    this.cdr.detectChanges();
    const carouselImages = this.carouselImages();
    if (typeof carouselImages !== 'undefined') {
      carouselImages.select(carouselImages.get(this.imageToShow), Direction.NEXT);
    }
  }

  getSlideUrl(slideId: number): string {
    return this.restApiController + slideId;
  }

  onClosedDialog() {
    this.slideClosed.emit(true);
    this.previewImgOpen = false;
    this.cdr.detectChanges();
  }

  ngOnInit(): void {
    if (this.castToRem()) {
      this.sizeType = 'rem';
      if (this.width !== null && this.height !== null) {
        this.width /= 16;
        this.height /= 16;
      }
    }
  }
}
