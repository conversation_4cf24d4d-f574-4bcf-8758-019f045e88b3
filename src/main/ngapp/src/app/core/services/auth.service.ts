import { BnextLoaderActivationService } from '@/core/services/bnext-loader-activation.service';
import { type ScreenResolution, getScreenResolution } from '@/core/utils/DeviceUtil';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import type { InterpolatableTranslationObject } from '@ngx-translate/core/lib/translate.service';
import { type Observable, type Subject, combineLatest, throwError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { Session } from 'src/app/core/local-storage/session';
import { AboutApp } from '../local-storage/about-app';
import { ConfigApp } from '../local-storage/config-app';
import { LocalStorageItem } from '../local-storage/local-storage-enums';
import { LocalStorageSession } from '../local-storage/local-storage-session';
import type { DataMap } from '../utils/data-map';
import { keysObject } from '../utils/object';
import { RestApiModule } from './../rest-api.module';
import { AppService } from './app.service';
import type { LoginPayload, UserDetail } from './login.interfaces';
import { NavigateLanguageService } from './navigate-lang.service';

let NEXT_CONFIG_CALL = 0;

declare global {
  interface Window {
    _angularCustomLang: Record<string, InterpolatableTranslationObject>;
  }
}

@Injectable()
export class AuthService {
  private http = inject(HttpClient);
  navLang = inject(NavigateLanguageService);
  private service = inject(AppService);
  private loader = inject(BnextLoaderActivationService);
  private readonly loginUrl = `${RestApiModule.url()}../login`;

  attemptAuth(user: string, password: string, token, position: GeolocationCoordinates): Promise<any> {
    return new Promise((resolve, reject) => {
      // Envía la información de login como una forma regular
      const headers = new HttpHeaders({
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'ngsw-bypass': '1'
      });
      const params: LoginPayload = {
        username: user,
        password: password
      };
      if (token !== null && typeof token !== 'undefined' && token !== '') {
        params.token = token;
      }
      const location = this.stringifyLocation(position);
      if (location !== null && typeof location !== 'undefined' && location !== '') {
        params.location = location;
      }
      const resolution = getScreenResolution();
      if (resolution !== null && typeof resolution !== 'undefined') {
        params.screenResolution = this.stringifyScreenResolution(resolution);
      }
      const body = new URLSearchParams(params).toString();
      this.http.post<any>(this.loginUrl, body, { headers: headers }).subscribe(
        () => {
          resolve(true);
        },
        (error) => reject(error)
      );
    });
  }

  private stringifyLocation(position: GeolocationCoordinates) {
    if (position === null || typeof position === 'undefined') {
      return '';
    }
    const json = JSON.stringify(position);
    if (json === null || typeof json === 'undefined' || json === '' || json === '{}') {
      return '';
    }
    return json;
  }

  private stringifyScreenResolution(resolution: ScreenResolution) {
    if (resolution === null || typeof resolution === 'undefined') {
      return '';
    }
    const json = JSON.stringify(resolution);
    if (json === null || typeof json === 'undefined' || json === '' || json === '{}') {
      return '';
    }
    return json;
  }

  logout(): Observable<any> {
    this.loader.show();
    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      'ngsw-bypass': '1'
    });
    return this.http.post<any>('../logout', null, { headers: headers });
  }

  sendRecoveryMailPassword(mail: string, user: string, cancelableReq: Subject<any>) {
    const data = {
      mail: mail,
      user: user
    };

    return this.service.post({ url: 'api/request-reset-password', cancelableReq, postBody: data, options: null, handleFailure: false });
  }
  restorePassword(data: any, cancelableReq: Subject<any>) {
    return this.service.post({ url: 'api/restore-password', cancelableReq, postBody: data, options: null, handleFailure: false });
  }

  public async fillUserData(loading: boolean, cancelableReq: Subject<any>, showInlineLogin = false): Promise<boolean> {
    await this.loadConfig(cancelableReq);
    return this.onlyFillUserData(loading, cancelableReq, showInlineLogin);
  }

  private onlyFillUserData(loading: boolean, cancelableReq: Subject<any>, showInlineLogin = false): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      const aboutObs = this.service.get({ cancelableReq, url: 'api/about', handleFailure: false });
      let myService: Observable<any>;
      if (showInlineLogin) {
        myService = this.service.get({ cancelableReq, url: 'users/me', handleFailure: false });
      } else {
        myService = this.http.get<any>(`${RestApiModule.url()}users/me`);
      }
      combineLatest([myService, aboutObs])
        .pipe(
          catchError((e: any) => {
            console.error('fill user data error: ', e);
            reject(loading);
            return throwError(() => e);
          }),
          timeout(30_000)
        )
        .subscribe({
          next: ([me, about]) => {
            if (me) {
              this.fillUserMe(me);
            } else {
              Session.clear();
            }
            if (about) {
              AboutApp.setValue(about);
            } else {
              AboutApp.clear();
            }
            resolve(loading);
          },
          error: (e) => {
            reject(e);
          }
        });
    });
  }

  public loadConfig(cancelableReq: Subject<any>): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      this.service
        .get({ cancelableReq, url: `api/config/${NEXT_CONFIG_CALL++}`, handleFailure: false })
        .pipe(
          catchError((e: any) => {
            console.error('load config error: ', e);
            reject('OFFLINE');
            return throwError(() => e);
          }),
          timeout(30_000)
        )
        .subscribe({
          next: (config) => {
            if (config) {
              ConfigApp.setValue(config);
            } else {
              ConfigApp.clear();
            }
            resolve('SUCCESS');
          },
          error: () => {
            resolve('OFFLINE');
          }
        });
    });
  }

  private fillUserMe(info: UserDetail) {
    const infoFullLanguage = `${info.language}-${info.country}`;
    const infoShortLanguage = info.language;
    this.navLang.setLang(infoFullLanguage);
    const translationsObject = this.navLang.translate.translations;
    const topWindow = window.top.window;
    if (translationsObject[infoShortLanguage]) {
      this.interpolateLabels(translationsObject[infoShortLanguage], info.businessUnitLabels);
      topWindow._angularCustomLang = translationsObject[infoShortLanguage];
      LocalStorageSession.setValue(LocalStorageItem.LAST_LANG, infoShortLanguage);
    } else {
      this.interpolateLabels(translationsObject[infoFullLanguage], info.businessUnitLabels);
      topWindow._angularCustomLang = translationsObject[infoFullLanguage];
      LocalStorageSession.setValue(LocalStorageItem.LAST_LANG, [infoFullLanguage]);
    }
    Session.setSession(info);
  }

  private interpolateLabels(obj: DataMap<any>, labels: DataMap<string>): void {
    if (!labels || !obj || typeof obj !== 'object' || typeof labels !== 'object') {
      return;
    }
    // Se recorren todas las keys en busca de etiquetas
    for (const key of keysObject(obj)) {
      if (typeof obj[key] === 'string') {
        for (const labelKey in labels) {
          if (labels.hasOwnProperty(labelKey) && String(obj[key]).indexOf(labelKey) !== -1) {
            obj[key] = obj[key].replace(new RegExp(`{${labelKey}}`, 'g'), labels[labelKey]);
          }
        }
      } else if (obj[key] && typeof obj[key] === 'object') {
        this.interpolateLabels(obj[key], labels);
      }
    }
  }

  public registerUser(data: any, cancelableReq: Subject<any>) {
    return this.service.post({ url: 'api/register', cancelableReq, postBody: data, options: null, handleFailure: false });
  }
}
