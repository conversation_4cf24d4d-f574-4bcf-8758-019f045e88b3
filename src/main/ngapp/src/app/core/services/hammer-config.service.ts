import { isTouchDevice } from '@/core/utils/DeviceUtil';
import { Injectable } from '@angular/core';
import { HammerGestureConfig } from '@angular/platform-browser';

@Injectable()
export class HammerConfigService extends HammerGestureConfig {
  private isTouchDevice = isTouchDevice();

  public static isHammerAvailable = !!Hammer?.defaults;

  overrides = {
    pan: {
      enable: this.isTouchDevice
    },
    swipe: {
      enable: this.isTouchDevice
    },
    pinch: {
      enable: this.isTouchDevice
    }
  } as any;

  public override buildHammer(element: HTMLElement): any {
    if (!HammerConfigService.isHammerAvailable) {
      return null;
    }
    if (!this.isTouchDevice && window?.Hammer?.defaults?.cssProps) {
      Hammer.defaults.cssProps.userDrag = undefined;
      Hammer.defaults.cssProps.userSelect = undefined;
    }
    return super.buildHammer(element);
  }
}
