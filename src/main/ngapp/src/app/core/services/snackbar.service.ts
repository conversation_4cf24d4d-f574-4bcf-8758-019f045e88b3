import type { SnackbarMessage } from '@/core/services/snackbar.interfaces';
import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SnackbarService {
  private _message = new Subject<SnackbarMessage>();
  public message = this._message.asObservable();

  notice(message: string, autoHide = true, timer = 3100, link = null) {
    if (message == null) {
      console.error('Mensaje "snackbar", sin valor... validar que el lenguaje esté bien configurado.', new Error().stack);
      return;
    }
    this.noticeMessage({
      message: message,
      autoHide: autoHide,
      link: link,
      timer: timer
    });
  }

  noticeMessage(notice: SnackbarMessage) {
    if (!notice || !notice.message) {
      return;
    }
    if (typeof notice.autoHide === 'undefined') {
      notice.autoHide = false;
    }
    this._message.next(notice);
  }
}
