import type { PlaningStateKey } from 'src/app/modules/activities/activities-add-plan/activities-add-plan.interfaces';
import { LocalStorageItem } from './local-storage-enums';
import { LocalStorageSession } from './local-storage-session';

export class ActivityPlainStateIndexDbDates {
  private static readonly item = LocalStorageItem.ACTIVITY_PLAN_STATE_INDEX_DB_DATES;

  public static getValue(workflowId: number): PlaningStateKey {
    if (workflowId) {
      return LocalStorageSession.getValue(ActivityPlainStateIndexDbDates.item, String(workflowId));
    }
    return LocalStorageSession.getValue(ActivityPlainStateIndexDbDates.item);
  }

  public static hasValue(workflowId: number): boolean {
    if (workflowId) {
      return LocalStorageSession.hasValue(ActivityPlainStateIndexDbDates.item, String(workflowId));
    }
    return LocalStorageSession.hasValue(ActivityPlainStateIndexDbDates.item);
  }

  public static setValue(value: PlaningStateKey, workflowId: number): void {
    if (workflowId) {
      LocalStorageSession.setValue(ActivityPlainStateIndexDbDates.item, value, String(workflowId));
    } else {
      LocalStorageSession.setValue(ActivityPlainStateIndexDbDates.item, value);
    }
  }

  public static clear(workflowId: number): void {
    if (workflowId) {
      LocalStorageSession.clearValue(ActivityPlainStateIndexDbDates.item, String(workflowId));
    } else {
      LocalStorageSession.clearValue(ActivityPlainStateIndexDbDates.item);
    }
  }
}
