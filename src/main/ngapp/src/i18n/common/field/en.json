{"actions": "Actions", "activity": "Activity", "additional": "Additional fields", "businessUnit": "{Facility}", "businessUnitDepartment": "{Department}", "client": "Client", "code": "ID", "comments": "Comments", "commitmentDate": "Implementation date", "createdDate": "Date created", "createdFrom": "Created from", "creationDate": "Date created", "currentReport": "Reporte actual", "departmentProcess": "Process", "description": "Description", "detail": "Detail", "documents": "Documents", "documentsAndFiles": "Attachments", "endDate": "End Date", "files": "Files", "generate-code": "Auto generate", "help": "Help information", "history": "History", "lastModificationDate": "Last modification date", "lastModifiedDate": "Last modification date", "name": "Name", "participant": "Member", "preview": "Preview", "process": "Process", "progressHistory": "Progress history", "project": "Project", "registry": "Registry", "reminder": "reminder", "search": "Search", "series": "Series", "stackedit-edit": "Modified from stackedit", "startDate": "Start Date", "status": "Status", "steps": "Steps", "task": "Task", "timesheetFiles": "Timesheet files", "title": "Title", "type": "Type", "user": "User"}