{"APE": {"AUDIT-TO-CONFIRM-DATE": "Pending task confirmation", "AUDIT-TO-CONFIRM-CHANGE-BY-LEADER": "To authorize date change", "AUDIT-TO-CONFIRM-CHANGE-BY-MANAGER": "To authorize date change (module manager)", "AUDIT-TO-FILL-BY-LEADER": "To be executed", "AUDIT-TO-FILL-BY-HELPER": "To be executed (Support auditor)", "AUDIT-TO-ACCEPT-RESULT": "To accept results", "AUDIT-ACTIVITY-TO-COMPLETE": "Activities to do", "AUDIT-ACTIVITY-TO-VERIFY": "Activities to verify", "AUDIT-ACTIVITY-TO-VERIFY-DELAYED": "Delayed activities to verify", "AUDIT-ACTIVITY-TO-VERIFY-NOT-APPLY": "Activities to accept cancellation", "ACC-FINDING-TO-ASSIGN": "Actions to assign", "ACC-FINDING-TO-START-IMPLEMENTATION": "Actions to add immediate correction action", "ACC-FINDING-TO-ANALYZE": "Actions to analyze", "ACC-FINDING-TO-ADD-PLAN": "Actions to choose an activity", "FINDING-ACTIVITY-TO-COMPLETE": "Activities to do", "FINDING-ACTIVITY-TO-VERIFY": "Activities to verify", "FINDING-ACTIVITY-TO-VERIFY-DELAYED": "Delayed activities to verify", "FINDING-ACTIVITY-TO-VERIFY-NOT-APPLY": "Actions to accept cancellation", "FORM-ACTIVITY-TO-COMPLETE": "Activities to do", "FORM-ACTIVITY-TO-VERIFY": "Activities to verify", "FORM-ACTIVITY-TO-VERIFY-DELAYED": "Delayed activities to verify", "FORM-ACTIVITY-TO-VERIFY-NOT-APPLY": "Actions to accept cancellation", "FORM-TO-VERIFY-CANCEL-REQUEST": "Approve form cancellation request", "FORM-TO-VERIFY-ADJUSTMENT-REQUEST": "Approve adjustment request", "FORM-TO-VERIFY-REOPEN-REQUEST": "Approve reopening of form request", "FORM-TO-AUTHORIZE-CANCEL-REQUEST": "Authorize form cancellation", "FORM-TO-AUTHORIZE-ADJUSTMENT-REQUEST": "Authorize form adjustment", "FORM-TO-AUTHORIZE-REOPEN-REQUEST": "Authorize reopening of form", "PLANNER-ACTIVITY-TO-COMPLETE": "Tasks to close", "ACC-FINDING-TO-EVALUATE": "Actions to accept", "CONFIGURATION-TO-ASSIGN-USER-POSITION": "Users to assign position", "CONFIGURATION-TO-ACTIVE-USER": "Users to activate", "COMPLAINT-TO-ASSIGN": "To assign responsible", "COMPLAINT-TO-RESPOND": "To answer", "COMPLAINT-TO-VERIFY": "To verify answers", "COMPLAINT-TO-EVALUATE": "Attended complaints to evaluate effectiveness", "MEETING-TO-ASSIST": "To attend", "MEETING-ACTIVITY-TO-COMPLETE": "Activities to do", "MEETING-ACTIVITY-TO-VERIFY": "Activities to verify", "MEETING-ACTIVITY-TO-VERIFY-DELAYED": "Delayed activities to verify", "MEETING-ACTIVITY-TO-VERIFY-NOT-APPLY": "Activities to accept cancellation", "DOCUMENT-TO-VERIFY-REQUEST": "To verify", "DOCUMENT-TO-AUTHORIZE-REQUEST": "To authorize", "DOCUMENT-TO-FILL-FORM": "Forms to complete", "DOCUMENT-TO-ASSIGN_READER": "To assign a reader", "DOCUMENT-TO-READ": "To read", "DOCUMENT-TO-DELIVER-PHYSICAL-COPY": "To deliver controlled copy", "DOCUMENT-TO-PICK-UP-PHYSICAL-COPY": "To pick up controlled copy", "DOCUMENT-TO-RENEW": "Expired documents", "ACTIVITY-TO-COMPLETE": "To do", "ACTIVITY-TO-VERIFY": "To verify", "ACTIVITY-TO-VERIFY-DELAYED": "Delayed activities to verify", "ACTIVITY-TO-VERIFY-NOT-APPLY": "To accept cancellation", "POLL-TO-ANSWER-IN-PROCESS": "To be applied", "DEVICE-TO-CHANGE": "To be replaced", "DEVICE-TO-SCHEDULE": "Services to be programed", "DEVICE-TO-APPROVE-SCHEDULING": "Scheduling to Approve", "DEVICE-TO-APPROVE-SERVICE": "Services to approve", "DEVICE-TO-REALIZE": "Services to be performed", "DEVICE-TO-REALIZE-PAST-SERVICE": "Due services"}, "ACTIVITY": {"HIDE": "<PERSON>de", "FINISH": "Finish", "PROGRESS": "Capture", "INCOMPLETE": "Return to \"To do\"", "UNDONE": "Not done", "NOT_APPLY_VERIFY": "Cancel", "ACCEPT_NOT_APPLY": "Accept", "FILL": "Fill form", "OPEN_FORM": "Open form", "OPEN_FLOW": "View fill flow", "APPLY_VERIFY": "Reject cancellation", "VERIFY": "Verify", "CAPTURE_TIME": "Capture timesheet", "VERIFY_IMPLEMENTATIONS": "Verify series", "OPEN_FORM_VERIFY": "Open form", "REASSIGN_VERIFY": "Reassign verify", "MULTY_REASSIGN_VERIFY": "Reassign multiple"}, "CONFIGURATION": {"ATTEND": "Open", "HIDE": "<PERSON>de"}, "DOCUMENT": {"ATTEND": "Open", "HIDE": "<PERSON>de", "VIEW_NEW_FILE": "View file", "VIEW_NEW_FORM": "View form", "VIEW_LAST_VERSION_FILE": "View last version", "VIEW_LAST_VERSION_FORM": "View last version"}, "FORMULARIE": {"AUTHORIZE": "Authorize", "REJECT": "Reject", "ATTEND": "Open", "HIDE": "<PERSON>de", "FINISH": "Finish", "PROGRESS": "Capture", "INCOMPLETE": "Unfulfilled", "UNDONE": "Not done", "NOT_APPLY_VERIFY": "Cancel", "ACCEPT_NOT_APPLY": "Accept", "APPLY_VERIFY": "Reject cancellation", "FILL": "Fill form", "OPEN_FORM": "Open form", "OPEN_FLOW": "View fill flow", "VERIFY": "Verify", "REASSIGN_VERIFY": "Reassign verify", "MULTY_REASSIGN_VERIFY": "Reassign multiple", "OPEN_FORM_VERIFY": "Open form"}, "AUDIT": {"ATTEND": "Open", "HIDE": "<PERSON>de", "FINISH": "Finish", "PROGRESS": "Capture", "INCOMPLETE": "Unfulfilled", "UNDONE": "Not done", "NOT_APPLY_VERIFY": "Cancel", "ACCEPT_NOT_APPLY": "Accept", "APPLY_VERIFY": "Reject cancellation", "FILL": "Fill form", "OPEN_FORM": "Open form", "OPEN_FLOW": "View fill flow", "VERIFY": "Verify", "REASSIGN_VERIFY": "Reassign verify", "MULTY_REASSIGN_VERIFY": "Reassign multiple"}, "ACTION": {"AUTHORIZE": "Authorize", "REJECT": "Reject", "ATTEND": "Open", "HIDE": "<PERSON>de", "FINISH": "Finish", "PROGRESS": "Capture", "INCOMPLETE": "Unfulfilled", "UNDONE": "Not done", "NOT_APPLY_VERIFY": "Cancel", "ACCEPT_NOT_APPLY": "Accept", "APPLY_VERIFY": "Reject cancellation", "FILL": "Fill form", "OPEN_FORM": "Open form", "OPEN_FORM_VERIFY": "Open form", "OPEN_FLOW": "View fill flow", "VERIFY": "Verify", "REASSIGN_VERIFY": "Reassign verify", "MULTY_REASSIGN_VERIFY": "Reassign multiple"}, "PLANNER": {"ATTEND": "Open", "HIDE": "<PERSON>de", "FINISH": "Close", "NOT_APPLY": "Cancel", "OPEN_DETAIL": "Open"}, "DEVICE": {"ATTEND": "Open", "HIDE": "<PERSON>de"}, "COMPLAINT": {"HIDE": "<PERSON>de", "ATTEND": "Attend"}, "FORM-TO-AUTHORIZE-ADJUSTMENT-REQUEST": {"message-AUTHORIZE": "Adjustment request approval", "message-REJECT": "Adjustment request rejection", "inputMinLimit": "The comment will be stored with the adjustment request and must be at least {inputMinLimit} characters", "inputLimitText": "Capture a comment", "labelInputLimitText": "Minimum 10 characters", "successVerifyDepartmentManager": "The pending item has been forwarded to the new department", "successVerifyRepeatedData": "Invalid information in the application, please correct and try again", "successVerifyApproved": "The authorization flow began to approve the adjustment", "success-AUTHORIZE": "The adjustment request was successfully authorized", "success-REJECT": "The adjustment request was successfully rejected"}, "FORM-TO-VERIFY-ADJUSTMENT-REQUEST": {"message-AUTHORIZE": "Adjustment request approval", "message-REJECT": "Adjustment request rejection", "inputMinLimit": "The comment will be stored with the adjustment request and must be at least {inputMinLimit} characters", "inputLimitText": "Capture a comment", "labelInputLimitText": "Minimum 10 characters", "successVerifyDepartmentManager": "The pending item has been forwarded to the new department", "successVerifyRepeatedData": "Invalid information in the application, please correct and try again", "successVerifyApproved": "The authorization flow began to approve the adjustment", "success-AUTHORIZE": "The adjustment was successfully authorized", "success-REJECT": "The adjustment request was successfully rejected"}, "FORM-TO-VERIFY-REOPEN-REQUEST": {"message-AUTHORIZE": "Reopen request approval", "message-REJECT": "Reopen request rejection", "inputMinLimit": "The comment will be stored with the reopen request and must be at least {inputMinLimit} characters", "inputLimitText": "Capture a comment", "labelInputLimitText": "Minimum 10 characters", "successVerifyDepartmentManager": "The pending item has been forwarded to the new department", "successVerifyRepeatedData": "Invalid information in the application, please correct and try again", "successVerifyApproved": "The authorization flow began to approve the reopening", "success-AUTHORIZE": "The reopening was successfully authorized", "success-REJECT": "Reopening request was successfully rejected"}, "FORM-TO-VERIFY-CANCEL-REQUEST": {"inputMinLimit": "The comment will be stored with the cancel request and must be at least {inputMinLimit} characters", "inputLimitText": "Capture a comment", "labelInputLimitText": "Minimum 10 characters", "message-AUTHORIZE": "Cancel request approval", "message-REJECT": "Cancel request rejection", "success-AUTHORIZE": "The definitive cancelation was successfully authorized", "success-REJECT": "Definitive cancelation request was successfully rejected"}, "pendings-title-description": "Pending tasks list", "pending-update-title-future-task": "future tasks", "pending-update-title-future-task-count": "There are {futurePendingCount}", "pending-update-title-future-task-end": ", the list includes tasks assigned until today at {lastPendingUpdate}", "pending-update-title": "The list includes pending tasks until today at {lastPendingUpdate}", "pendings-finished": "You don´t have pending tasks.", "pendings-finished-today": "You don´t have more pending tasks.", "pendings-finished-advise": "New tasks will appear here.", "pendings-not-found": "No tasks found with applied filters.", "pendings-not-found-advise": "Try a different search.", "pendings-search-description": "Search description", "calendar-search-title": "Active pending tasks on the selected dates will be displayed.", "calendar": "Dates", "reload": "Reload", "pending-tasks": "Pending tasks", "pending-date-today": "Today", "commitment-date": "Commitment date", "commitment-up-to-date": "Commitment up to date ", "module-of": "Module of ", "loading": "Please wait, in a few moments the information will be loaded......", "no-pending-tasks": "There are no pending tasks for this module", "not-finished": "Not finished", "open": "Open", "filter": "Filter", "showMoreBtn": "Show more", "showLessBtn": "Show less", "close": "Close", "pending-question-rigth-panel": "What do you want to do with this task?", "action": {"AUTHORIZE": "Authorize", "REJECT": "Reject", "ADD_ATTACHMENT": "Add file", "ADD_COMMENT": "Add comment", "ADD_DOCUMENT": "Add document", "ATTEND": "Open", "FILL": "Fill form", "FINISH": "<PERSON> as \"Finished\"", "HIDE": "<PERSON>de", "INCOMPLETE": "Update progress", "NOT_APPLY": "Cancel", "NOT_APPLY_VERIFY": "Verify as \"Cancelled\"", "ACCEPT_NOT_APPLY": "Verify cancellation", "APPLY_VERIFY": "Reject cancellation", "OPEN_DETAIL": "Open detail", "EDIT": "Modify my task", "EDIT_REQUEST": "Request changes", "OPEN_FORM": "Open form", "OPEN_FORM_VERIFY": "Open form", "OPEN_FLOW": "View fill flow", "PROGRESS": "Save progress", "REFRESH": "Refresh", "UNDONE": "Close as \"Not done\"", "VERIFY": "Verify as \"Done\"", "VIEW_ATTACHMENT": "View attachment", "VIEW_COMMENT": "View comments", "VIEW_DOCUMENT": "View related documents", "CANCEL_RECURRENCE": "Cancel series", "VIEW_NEW_FILE": "View file", "VIEW_NEW_FORM": "View form", "VIEW_LAST_VERSION_FILE": "View last version", "VIEW_LAST_VERSION_FORM": "View last version", "CAPTURE_TIME": "Capture timesheet", "START_STOPWATCH": "Start stopwatch", "STOP_STOPWATCH": "Stop stopwatch", "VERIFY_IMPLEMENTATIONS": "Verify series", "REASSIGN_VERIFY": "Reassign verify", "MULTY_REASSIGN_VERIFY": "Reassign multiple"}, "PLANNER-ACTIVITY-TO-COMPLETE": {"action": {"ATTEND": "Open", "HIDE": "<PERSON>de", "FINISH": "Close", "NOT_APPLY": "Cancel", "EDIT_REQUEST": "Ask for changes", "OPEN_DETAIL": "Open", "ADD_ATTACHMENT": "Add file"}}, "details_label": "Details", "pendings_by_module": "Pending tasks by module", "pendings_label": "pending tasks", "empty_advise": "Here you can see the distribution of pending tasks by module", "labels": {"code": "ID", "description": "Description", "implementer": "Responsible", "verifier": "Verifier", "progress": "Progress", "form": "Form", "fill_code": "Fill code", "activity_type": "Type", "implementation": "Implementation date", "no-owner": "Not defined yet", "verification": "Verification date", "department": "{Department}", "system": "System", "source": "Source", "priority": "Priority", "objective": "Objective", "client": "Client", "planner": "Project", "task": "Deliverable", "document": "Document", "document_type": "Document type", "reazon": "Reason for request", "bussines_unit": "{Facility}", "author": "Current document author", "requester": "Applicant", "audit_type": "Audit type", "area": "Area", "process": "Process", "auditor": "Lead audit", "audited": "Audited", "start_date": "Start date", "end_date": "End date", "start_hour": "Start time", "end_hour": "End time", "plannedWeek": "Planned week", "plannedImplementationWeek": "Implementacion week", "plannedVerificationWeek": "Verification week", "print_date": "Print date", "position_assigned_date": "Assigned at", "receipt_user": "User receiving the copy", "receipt_position": "Position receiving the copy", "progressStatus": "Progress status", "outsandingSurveyStage": "Stage", "reader": "Reader", "requestor": "User requesting that it be read", "progressLabel": "Progress", "noProgress": "No progress", "authorOnly": "Author", "reminder": "Implementation date", "implementationStart": "Start date", "daysToVerify": "Day(s) to verify", "isPlanned": "Is planned", "plannedHours": "Estimated hours", "cancellationReason": "Cancellation reason", "followUpImplementationDelay": "Follow up on implementation delay", "mustUpdateImplementationAtReturn": "Modify implementation date when returning to do", "categoryName": "Category", "reportedDate": "Reported date", "modelBrand": "Model/Brand", "changeDate": "Date of replacement", "serial": "Series number", "classification": "Classification", "deviceGroup": "Group", "creatorUserName": "Author", "activityOrder": "Order", "version": "Version"}, "activityCancellationReasons": {"CHANGE_PLANNED_HOURS": "Adjust the estimated hours of another activity", "NEW_ACTIVITY": "Other activity will be attended", "NONE": "No reason"}, "time": "Time", "comments": "Comments", "files": "Files", "added_comments": "Comments", "added_files": "Files", "added_documents": "Documents", "linkedFileSaved": "Relationship to the file has been added", "linkedDocumentSaved": "Relationship to the document has been added", "linkedDocumentRemoved": "Relationship to the document has been removed", "comment-required": "Comment is required", "ok": "Accept", "cancel": "Cancel", "noticeMessages": {"showingPendings": "All pending tasks with commitment dates until today are shown", "pendingsAfterToday": "Commitment dates are filtered to show future pending tasks"}, "pendingsDropdownOptions": {"REFRESH": "Refresh pending tasks", "SHOW_TIMEWORK": "Check Timework", "FILTER_UNTIL_TODAY": "Show everything up until today", "FILTER_FROM_TOMORROW": "Show future assignments", "FILTER_RANGE": "Show range of dates", "GROUP_PENDINGS": "Group by date", "EXACT_SEARCH": "Exact search", "SORT_ASC": "Order - Older first", "SORT_DESC": "Order - Recent first"}, "filterDatesNotes": {"noFilter": "no date filter", "beforeToday": "commitment dates are filtered up until today", "tillDate": "commitment dates are filtered up to ", "afterToday": "only commitment dates that have not yet arrived are filtered", "filterFromDate": "only commitment dates are filtered from the ", "fromDate": "commitment dates are filtered from the ", "toDate": " to "}, "pageMessages": {"showing": "Showing ", "pendings": " pending tasks", "commentToday": "Today", "commentYou": " you", "showingFuturesActivities": "Today's pending tasks are complete, future tasks are displayed."}, "documentsMessages": {"task": " assigned document to read: { description } with ID { code }", "copywith": " Collect controlled copy of document \"{ documentName }\" with { createdByUserName }"}, "detail": {"DOCUMENT-TO-DELIVER-PHYSICAL-COPY-USER": "Deliver controlled copy of the document {{ documentName }} to {{ userName }}", "DOCUMENT-TO-DELIVER-PHYSICAL-COPY-JOB": "Deliver controlled copy of the document {{ documentName }} to the users with {{ jobName }} job"}, "NEW": "New document \"{{ documentName }}\" requested by {{ currentVersionAuthorName }}", "MODIFY": "Document modification \"{{ documentName }}\" requested by {{ currentVersionAuthorName }}", "EDIT_DETAILS": "Edit document details \"{{ documentName }}\" requested by {{ currentVersionAuthorName }}", "CANCEL": "Document cancellation \"{{ documentName }}\" requested by {{ currentVersionAuthorName }}", "APROVE": "Document reapproval \"{{ documentName }}\" requested by {{ currentVersionAuthorName }}", "requestTypeDocument": {"NEW": "New document", "MODIFY": "Document modification", "EDIT_DETAILS": "Edit document details", "CANCEL": "Document cancellation", "APROVE": "Document reapproval"}, "dateTitleToday": "Until today", "todayActivitie": "Activity for today", "nearActivitie": "Next activity", "tomorrowActivitie": "Activity for tomorrow", "yesterdayActivitie": "Past activity", "from": "Since {{ day }} of {{ month }}", "between": "Since {{ startDate }} until {{ endDate }}", "moduleOf": "{ module } Module", "pendingsFutureCompleted": "Pending screen with current date will be displayed.", "until": "Until {{ endDate }}", "dateTitle": "{{ day }} of {{ month }}", "date-range-start": "Fecha de inicio", "date-range-end": "<PERSON><PERSON> de fin", "timeInterval": "Pending interval", "dateToday": "Today", "dateTodayWithObj": "Today - {{date}}", "added_timesheet_files": "Timesheet files", "addenOnPending-label": "Was added on pending:", "selector-commitmentTask-label": "Was added in:", "selector-commitmentTask": {"": "Saving...", "1": "Implementation", "2": "Verification", "3": "Main activity"}, "request": {"status": {"13": "Pending cancel approval", "11": "Pending return approval", "10": "Expired", "9": "Draft", "3": "Requested", "5": "Rejected", "4": "Returned", "6": "In process", "7": "Completed form", "2": "Pending cancel approval"}}, "lastActionDescription": "{{progress}}% progress was registered with the comment: {{comment}}", "pending-to-do": "Pending to attend ({{count}})", "order": "Orden: {{orderCount}}"}