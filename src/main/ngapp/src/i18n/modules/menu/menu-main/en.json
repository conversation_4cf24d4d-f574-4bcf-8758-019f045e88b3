{"yes-restore-my-active-status-message": "The current status of your user in the system is \"Away\", do you want to change it to active?", "yes-restore-my-active-status-fail": "Unable to uncheck \"Away\" status", "yes-restore-my-active-status": "Yes, restore me", "search_menu": "Search menu", "confirm-reload-title": "The window will be reloaded", "confirm": "Do you wish to continue?", "homepage": "Home page will be opened instead the current page", "user-contact": {"telephonenumber": "Phone", "othertelephone": "Extension", "mobile": "Celphone"}, "menu": {"CONFIGURATION": "Configuration", "MEETING": "<PERSON><PERSON><PERSON>", "DOCUMENT": "Documents", "FORMULARIE": "Forms", "AUDIT": "Audits", "ACTIVITY": "Activities", "ACTION": "Findings", "POLL": "Poll", "COMPLAINT": "<PERSON><PERSON><PERSON><PERSON>", "METER": "Meters", "PLANNER": "Projects", "PROJECT": "Projects", "DEVICE": "Devices", "TIMEWORK": "Timework", "TIMESHEET": "Timesheet", "HAPPYORNOT": "Happy or not", "FIVES": "5S+1", "TASK_ONLY": "Favorites", "ORGANIZATION": "Organization", "USERS": "Users", "QUERYS": "Querys", "CACHE": "Cache events", "CATALOGS": "Catalogs", "acciones": "Findings", "activities": "List", "activities_report": "Reports", "activity_recycle_bin": "Activities recycle bin", "activities_submenu": "Activities", "activityPriority": "Priority", "activitySource": "Source", "activityType": "Type", "activity_workflow": "View", "activity_category": "Category", "activityResolution": "Resolution", "alta": "Create", "alta_adv": "Create adv.", "planning": "Planning", "user-group": "Structure", "user-team": "Group", "alta_template": "Create template", "audit_program": "Audit program", "auditorias": "Audits", "automaticTasks": "Automatic tasks", "access_history": "Access history", "answers": "Answers settings", "building": "Building", "zone": "Zone", "region": "Region", "calendar": "Break date calendar", "cat_audits_clauses": "Standard clauses", "cat_audits_scores": "Scores", "cat_audits_type": "Audit type", "cat_clause_type": "Clauses", "cat_documentos": "Catalogs", "cat_forms_interno": "Internal catalog", "cat_forms_externo": "External catalog", "form-progress-state": "Progress statuses", "catalog_meeting": "Meetings", "catalogo": "Catalogs", "catalogo_activities": "Activities", "catalogo_quejas": "<PERSON><PERSON><PERSON><PERSON>", "catalogo_quejas_fuente": "Complaint source", "catalogos_fuente_acciones": "Source of findings", "client": "Client", "complaint_pendings": "Complaints - tasks", "config_interface_tress": "TRESS", "config_sistema": "System", "configuracion": "Configuration", "configuracion_encuestas": "Surveys", "configuracion_indicadores": "Indicators report", "configuracion_mis_encuestas": "My surveys", "configuracion_mis_indicadores": "My indicators", "configuracion_mis_quejas": "My complaints", "configuracion_objetivos": "Objectives", "configuracion_quejas": "<PERSON><PERSON><PERSON><PERSON>", "configuracion_quejas_control": "Control", "connections": "Connections", "consolidadoderespuestas": "Response repository", "links": "Links", "consolidated_activities": "Consolidated activities", "weekly_score": "Weekly score", "evaluations": "Evaluations", "planning_owner_dashboard": "Planning by owner", "Cumplimiento": "Activity score", "evaluation_activity": "Planning evaluation", "owner_dashboard": "Owner score", "mail-settings-mine": "My mail settings", "mail-settings-user": "User mail settings", "mail-settings-business-unit": "{facility} mail settings", "control": "Control", "control_activity": "Activities control", "activities_deleted": "Recycle bin", "control_fill_registries": "Controls to fill", "control_my": "My activities", "control_planned": "Schedule", "control_template": "Templates", "control_registros": "Records control", "control_verify_registries": "Controls to verify", "controlde_registros": "Record control", "cuestionarios": "Questionnaires", "scheduled_task": "Scheduled tasks", "database-settings": "Database", "daily_mail": "Daily mails", "departamento_proceso_unidades": "Areas", "departamentos": "{Departments}", "departamentos_list": "{Departments}", "departamentos_plantas": "{Departments} / {Facility}", "device_classification": "Classification", "device_master": "Device master", "dic_versioning": "Versioning dictionary control", "disenados_por_mi": "Designed by me", "disposed_devices": "Discarded", "documents": "Documents", "document_list": "History", "dynamic_field": "Dynamic field", "exchange_rate": "Currency converter", "currency_data_ws": "Providers", "exchange_rate_list": "Exchange rates", "exchange_rate_log": "Exchange rates history", "register_device": "Register", "equipos": "<PERSON><PERSON>", "escalation": "Escalation configuration", "estadode_acciones": "Finding's status", "estatusdeequipos": "Devices status", "file_type": "File type", "finding_pendings": "Pending Findings", "activity_pendings": "Activity - tasks", "finding_type": "Action type", "five_s_area": "Area", "five_s_area_mapping": "Area map", "five_s_building": "Building", "five_s_building_mapping": "Building map", "five_s_business_unit_department": "{Department}", "five_s_department_mapping": "{Department} map", "five_s_item": "<PERSON><PERSON>", "five_s_item_mapping": "Item map", "five_s_mapping": "Map definition", "five_s_my_printable": "My printables", "five_s_operation": "Project definition", "five_s_printable": "Printable", "five_s_printable_def": "Definition of printables", "five_s_printable_link": "Printables link", "five_s_printable_type": "Types of printables", "five_s_section": "Section", "five_s_section_mapping": "Section map", "foliosdeequipo": "Device reference number", "formulario": "Forms", "general": "General", "gruposdeequipos": "Groups of devices", "historial_de_llenado": "History of requests", "indicadores": "Indicators", "jerarquia": "Organizational hierarchy", "licensing_schemas": "Licensing schemas", "licensing_history": "Licensing history", "lista_de_distribucion": "Distribution list", "lista_de_distribucion_lista_completa": "Document distribution", "lista_de_distribucion_por_documento": "By document", "lista_de_distribucion_por_usuario": "By user", "lista_maestra": "Master list", "to_renew": "Documents to renew", "lista_relaciones": "Relationships list", "listadesolicitudes": "Request list", "llenar": "Available forms", "login_principal_documentos": "Documents", "lugares_almacenamiento": "Storage places", "clasificacion_informacion": "Information classification", "disposicion": "Disposition", "lista_maestra_registros": "Records master list", "meeting_recurring_recycle_bin": "Recurrence recycle bin", "meeting_type": "Meeting type", "share_report": "Sending reports", "menu_diagram": "Organizational hierarchy", "menu_immediateBoss": "Scalability hierarchy", "menu_profile": "Profiles", "mi_historial_de_llenado": "My history of requests", "mis_acciones": "My findings", "mis_actividades": "My activities", "mis_auditorias": "My audits", "timesheet": "Timesheet", "module_fives": "r5S+1", "my_activity": "My activities", "my-repository": "Repositories", "my_timesheet": "My Timesheet", "task_hours_dashboard": "Hours per deliverable", "my_timework": "My Timework", "objective": "Objective", "papelera": "Recycle bin", "papelera_audit": "Recycle bin", "papelera_indicator": "Indicators bin", "patronesdemedicion": "Measurement patterns", "pendientes": "Pendings", "activeDirectoryAdd": "Add users from Active Directory", "activeDirectoryRead": "Current Active Directory information", "pending_records": "Task summary", "perfil": "Profile", "bulk_list": "Create with Excel", "licensing": "Licensing", "planner": "Projects", "plantas": "{Facility}", "plantas_list": "{Facilities}", "plantilla": "Authorization flow", "preferencias": "Preferences", "priority": "Priorities", "procesos": "Processes", "procesos_list": "Processes", "procesos_departamentos": "Processes / {departments}", "proyectos": "Projects", "participant": "Member", "puesto": "Job", "querys": "Querys", "settings": "Settings", "help": "Help", "signOut": "Sing Out", "recurrent_meeting_control": "Recurrence list", "reporte_documentos": "Reports", "reporte_de_documentos": "Documents", "reportede_acciones": "Findings", "reportede_proyectos": "Projects", "reportes": "Reports", "reports": "Reports", "repositorios": "Repositories", "resultadodeservicio": "Service result", "reuniones": "Meetings", "roles": "Roles", "rubros": "Budgets", "scheduled": "Scheduled", "scheduled_services": "Scheduled services", "secuencia_list": "Sequences", "service_history": "Service history", "service_metric": "Services metric", "service_scheduler": "Scheduling", "services": "Services", "solicitudes": "Requests", "sincronizacion": "Synchronize", "timework_per_day": "Allowance per day", "task_type": "Tasks type", "tipodedocumento": "Document type", "tipodeservicio": "Service types", "tiposdeequipos": "Device types", "tiposquejas": "Clasification", "unidaddenegocio": "Business unit", "unidadesdemedicion": "Measurement units", "unscheduled": "Unscheduled", "unscheduled_service": "Unscheduled service", "user_agent": "User agent", "usuarios": "Users", "variablesdemedicion": "Measurement variables", "viewfinder": "Document viewer", "viewfinder-button": "Search document", "menuPdfExternalProcess": "External processes", "menuPdfConversionLog": "History", "menuPdfConversion": "PDF Conversion", "menuPdfFiles": "PDF files", "planner_type": "Project type", "tasks": "Deliverables", "task_category": "Task category", "task_category_type": "Task delivery type", "task_control": "Deliverable list", "confirmDraft": "There's a request for this form started on {draftDate} at {draftTime} hours, do you want to continue filling out that same request?", "continueDraft": "Yes, continue", "inactiveForm": "The document is not available at this time", "inactiveFormTitle": "Inactive form", "commitments-summary": "Activities meters", "escalationTask": "Escalation", "workload": "Calendar", "my_workload": "My calendar", "reporte": "Reports", "paperbin": "Paperbin", "welcome-carousel": "Schedule notification"}, "home": "Home", "pendings": "Pending tasks", "my-profile": "My profile", "my-qr": "QR Access", "no-position": "Your account does not have an associated position.<br>In order to access the system, ask the administrator to assign you the corresponding position.", "logout-confirm": "Do you want to leave?<br>Any unsaved information will be lost.", "document-viewer-not-access": "You do not have permission to view this document. The pending documents to read only grant temporary access.", "yes": "Yes", "no": "No", "help": "Help", "logout": "Logout", "pending_records": "TASKS", "paths": {"help": "Help", "profiles": "Profile", "activity-types": "Activity type", "activities": "Activity", "activities-list": "Activities"}, "welcome-title": "Welcome", "acknowledge": "Acknowledge", "views": {"audit.survey.view": "Survey handle", "Download.view": "Descar<PERSON>", "DownloadMedia.view": "Download", "ImageFile.view": "Image", "Pdf.view": "Viewer PDF", "poll.survey.view": "Survey handle", "report.audit.general.view": "Audit report", "v.access.history.view": "Access history", "v.area.list.view": "Area control", "v.area.map.list.view": "Control of area maps", "v.area.map.view": "Create area maps", "v.area.view": "Create areas", "v.audit.handle.view": "Create audit", "v.audit.individual.handle.view": "Audit", "v.audit.list.view": "Audit control", "v.audit.program.list.view": "Audit Program", "v.audit.survey.capture.view": "Fill audit questions", "v.audit.survey.list.view": "Questionnaires control", "v.audit.survey.preview.view": "Audit questions preview", "v.audit.type.list.view": "Audit types control", "v.audit.type.view": "Create audit types", "v.audits.clauses.handle.view": "Standard clauses", "v.audits.clauses.list.view": "Clauses list", "v.audits.score.handle.view": "Audit grades", "v.audits.score.list.view": "Score control", "v.autorization.view": "List of authorizing documents", "v.breaks.view": "Break date calendar", "v.building.list.view": "Building control", "v.building.map.list.view": "Control of department maps", "v.building.map.view": "Create building maps", "v.building.view": "Create Buildings", "v.business.unit.department.list.view": "Relationship between {facility} and {departments}", "v.business.unit.department.view": "Create relationship between {facility} and {department}", "v.business.unit.list.view": "{Facility} control", "v.business.unit.view": "Create {facility}", "v.chapterXIV.view": "Chapter XIV", "v.clause.type.list.view": "Standard control", "v.clause.type.view": "Create Standard", "v.common.actional.view": "Processing", "v.common.black.view": "Empty", "v.complaint.analisis.list.view": "Complaint analysis type control", "v.complaint.list.view": "Complaints Control", "v.complaint.reporte.view": "Complaints reports", "v.complaint.sources.list.view": "Complaint source type control", "v.complaint.view": "<PERSON><PERSON><PERSON><PERSON>", "v.configuracion.reporte.general.view": "Report", "v.controlled.document.list.by.deliver.view": "List of controlled copies of documents by deliver", "v.cuestionarios.reporte.general.view": "Report", "v.department.map.list.view": "Control {department} map", "v.department.list.view": "Control {department}", "v.department.map.view": "Create {department} maps", "v.department.process.list.view": "Relationship between {department} and processes", "v.department.process.view": "Create relationship between {department} and processes", "v.department.view": "New {department}", "v.device.classification.list.view": "Classification control", "v.device.classification.view": "Create classifications", "v.device.dispose.list.view": "Control of discarded devices", "v.device.dispose.view": "Discard devices", "v.device.folio.list.view": "Device reference number control", "v.device.folio.view": "Create device reference numbers", "v.device.group.list.view": "Device group control", "v.device.group.view": "Create device groups", "v.device.list.view": "Device control", "v.device.status.list.view": "Device status control", "v.device.status.view": "Create device status", "v.device.type.list.view": "Device type control", "v.device.type.view": "Create device type", "v.device.view": "Create Devices", "v.diagram.view": "Organizational hierarchy", "v.document.distribution.list.view": "List of document distribution", "v.document.imagenes.view": "Media", "v.document.list.ae.view": "Folder", "v.document.list.cp.view": "List of project documents and actions", "v.document.list.view": "Documents", "v.document.master.list.view": "Master list", "v.document.master.records.list.view": "Master records list", "v.document.papelera.view": "Document repository", "v.documentos.listae.view": "Folder", "v.documentos.reporte.general.view": "Document report", "v.documentos.secuencia.view": "Sequence", "v.documentos.view": "Document", "v.documents.by.reader.view": "Search of documents by viewer", "v.external.document.catalog.list.view": "External documents catalog search", "v.external.document.catalog.view": "Create external catalog for forms", "v.fill.form.view": "List of authorizing documents", "v.fill.out.history.view": "History of requests", "v.folder.access.view": "Folder options", "v.general-breaks.view": "Break date calendar", "v.indexManual.view": "Help", "v.indicador.thrash.view": "Indicators bin", "v.indicadores.levantamiento.view": "Fill meter", "v.indicadores.objetivo.view": "Objectives", "v.indicadores.reportes.view": "Indicators report", "v.indicadores.revision.view": "Review meter", "v.indicadores.view": "Indicators", "v.internal.document.catalog.list.view": "Internal documents catalog search", "v.internal.document.catalog.view": "Create internal catalog for forms", "v.item.list.view": "Item control", "v.item.map.list.view": "Control of item maps", "v.item.map.view": "Create item maps", "v.item.view": "Create items", "v.licensing.schemas.view": "Licensing schemas", "v.logoManual.view": "Logo", "v.menu.acciones.view": "Findings menu", "v.menu.activities.view": "Activities menu", "v.menu.auditorias.view": "Audit menu", "v.menu.configuration.view": "Configuration menu", "v.menu.documentos.view": "Document menu", "v.menu.encuestas.view": "Poll menu", "v.menu.equipos.view": "Device menu", "v.menu.fives.view": "5S menu", "v.menu.formularies.view": "Forms menu", "v.menu.indicadores.view": "Meter menu", "v.menu.proyectos.view": "Projects menu", "v.menu.quejas.view": "Complaint menu", "v.menu.reuniones.view": "Meetings menu", "v.menu.treedown.view": "<PERSON><PERSON>", "v.menu.user.view": "User menu", "v.menu.view": "<PERSON><PERSON>", "v.meter.list.view": "Meters control", "v.my.activity.view": "My activities", "v.my.audit.view": "My audits", "v.my.complaint.view": "My complaints", "v.my.fill.out.history.view": "My history of requests", "v.my.poll.view": "My surveys", "v.my.printable.view": "List of Printables", "v.my.request.survey.list.view": "Designed by me", "v.objective.list.view": "Objective control", "v.organizational.unit.list.view": "Business unit control", "v.organizational.unit.view": "Create business units", "v.pattern.list.view": "Measurement pattern control", "v.pattern.view": "Create measurement patterns", "v.poll.handle.view": "Create surveys", "v.poll.list.view": "Surveys", "v.poll.output.view": "Response repository", "v.poll.registry.list.view": "Records Control", "v.poll.survey.capture.view": "Fill poll", "v.poll.survey.list.view": "Questionnaires", "v.poll.survey.preview.view": "Poll preview", "v.position.list.view": "Job control", "v.position.view": "Create job", "v.preferences.view": "Preferences: system configurations", "v.print.viewer.view": "Print viewer", "v.printable.area.list.view": "Control of relationship between areas and printables", "v.printable.area.view": "Relationship between area and printables", "v.printable.building.list.view": "Control of relationship between buildings and printables", "v.printable.building.view": "Relationship between building and printables", "v.printable.business.unit.department.list.view": "Control of relationship between {department} and printables", "v.printable.business.unit.department.view": "Relationship between {department} and printables", "v.printable.item.list.view": "Control of relationship between item and printables", "v.printable.item.view": "Relationship between item and printables", "v.printable.list.view": "Control of printables", "v.printable.section.list.view": "Control of relationship between sections and printables", "v.printable.section.view": "Relationship between section and printables", "v.printable.type.list.view": "Control of printable types", "v.printable.type.view": "Types of printables", "v.printable.view": "Definition of printables", "v.printable.viewer.view": "Printable viewer", "v.priority.list.view": "Priority control", "v.priority.view": "Create priorities", "v.process.list.view": "Process control", "v.process.view": "Create processes", "v.profile.list.view": "Profile control", "v.project.report.view": "Project documents report", "v.proyect.list.view": "Project control", "v.proyectos.actividades.alta.view": "New project activity", "v.proyectos.actividades.avance.view": "Fill project activity", "v.proyectos.actividades.edit.view": "Edit project activity", "v.proyectos.actividades.verifica.view": "Verify project activity", "v.proyectos.actividades.view": "Project activities", "v.proyectos.list.contro.cambios.actividad.view": "Authorization of changes in activities", "v.proyectos.list.contro.cambios.presupuesto.view": "Budget authorization", "v.proyectos.list.contro.cambios.view": "Pending changes", "v.proyectos.metas.view": "Goal detail", "v.proyectos.reportes.avance.actividad.view": "Activity progress report", "v.proyectos.reportes.cambios.actividad.view": "Modification report in activities", "v.proyectos.reportes.peticiones.presupuesto.view": "Budget's request report in activities", "v.proyectos.reportes.view": "Project report", "v.proyectos.view": "Create project", "v.readers.by.document.view": "Search of viewers by document", "v.realize.service.view": "Answer service", "v.realized.services.view": "Services history", "v.registry.list.view": "Record control", "v.related.document.list.view": "List of relationships", "v.reporte.gantt.view": "Project gantt", "v.repositorio.list.view": "Repository control", "v.repositorio.view": "Create repository", "v.request.list.view": "List of requests", "v-request-detail.view": "Request detail", "v.request.survey.fill.list.view": "Available forms", "v.request.survey.fill.view": "Form fill", "v.request.survey.list.view": "Forms", "v.request.survey.mode.view": "Forms - New", "v.request.survey.preview.view": "Form preview", "v.request.survey.view": "Create new form", "v.request.view": "Create request", "v-to-renew-documents": "Documents to renew", "v-attender-wrapper.view": "Task", "v.rubro.list.view": "Section control", "v.rubro.view": "Create section", "v.scheduled.services.view": "Scheduled services", "v.scheduling.to.approve.view": "Scheduling to approve", "v.section.list.view": "Section control", "v.section.map.list.view": "Control of section maps", "v.section.map.view": "Create section maps", "v.section.view": "Create Sections", "v.secuencia.list.view": "Sequences", "v.secuencia.view": "Authorization flow", "v.sequence.detail.list.view": "Authorization flow", "v.sequence.flow.preview.view": "Sequence flow preview", "v.service.metric.view": "Service metrics", "v.service.result.list.view": "Service results control", "v.service.result.view": "Create service result", "v.service.scheduler.view": "Services scheduling", "v.service.type.list.view": "Service types control", "v.service.type.view": "Create service types", "v.services.to.approve.view": "Realized Services", "v.set.readers.view": "Documents", "v.storage.place.list.view": "Storage place control", "v.template.handle.view": "Create template", "v.template.list.view": "Template control", "v.to.read.documents.view": "Consultation documents for reading", "v.unit.list.view": "Measurement unit control", "v.unit.view": "Create measurement units", "v.unscheduled.services.view": "Unscheduled services", "v.user.edit.view": "Edit account", "v-edit-user-password.view": "Security settings", "v.user.list.view": "User control", "v.user.lookup.view": "Directory Search", "v.user.view": "Create User", "v.userManual.view": "Manual", "v.variable.list.view": "Measurement variable control", "v.variable.view": "Create measurement variables", "v.verification.view": "List of requests to be verified", "v-action-preferences.view": "Preferences: Findings", "v-activity-complete.view": "Activities to complete", "v-activity-objective.view": "Create activity objective", "v-activity-objective-list.view": "Dependency list", "v-activity-priority.view": "Register new activities's priority", "v-activity-priority-list.view": "Activities's priority list", "v-activity-source.view": "Register new activities's source", "v-activity-source-list.view": "Activities's source list", "v-activity-type.view": "Activity type", "v-activity-type-list.view": "Activities's type list", "v-activity-verify.view": "Activities to verify", "v-activity-verify-not-apply.view": "Activitities to verify as cancelled", "v-answer-summary.view": "Form responses", "v-audit-accept-result.view": "To accept results", "v-audit-activity-complete.view": "Activities to complete", "v-audit-activity-list.view": "Activities control", "v-audit-activity-verify.view": "Activities to verify", "v-audit-activity-verify-not-apply.view": "Activitities to verify as cancelled", "v-audit-confirm-change-by-leader.view": "To authorize date change", "v-audit-confirm-change-by-manager.view": "To authorize date change (module manager)", "v-audit-confirm-date.view": "Pending confirmation", "v-audit-fill-by-helper.view": "To be executed (Support auditor)", "v-audit-fill-by-leader.view": "To be executed", "v-audit-individual-list.view": "Audit records control", "v-audit-preferences.view": "Preferences: <PERSON><PERSON>", "v-backend-licensing.view": "Licensing", "v-bin-distribution-list.view": "Bin distribution list", "v-complaint.view": "<PERSON><PERSON><PERSON><PERSON>", "v-complaint-my-ape.view": "Complaint list", "v-complaint-preferences.view": "Preferences: <PERSON><PERSON><PERSON><PERSON>", "v-consolidated-activity.view": "Consolidated activities", "v-daily-mail.view": "Daily mails", "v-scheduled-task.view": "Scheduled tasks", "v-form-connection-list.view": "Form connections", "v-timesheet-connection-list.view": "Timesheet connections", "v-timework-connection-list.view": "Timework connections", "v-timework-query-list.view": "Timework queries", "v-timework-report-list.view": "Timework report", "v-timework-cache-list.view": "Timework cache list", "v-timework-preferences.view": "Timework preferences", "v-timework-list.view": "Timework records", "v-activity-connection-list.view": "Activity connections", "v-form-query-list.view": "Forms queries", "v-form-cache-list.view": "Forms events cache", "v-timesheet-query-list.view": "Timesheet queries", "v-timesheet-cache-list.view": "Timesheet events cache", "v-activity-query-list.view": "Activity queries", "v-activity-cache-list.view": "Activty events cache", "v-default-report-logo.view": "Logo", "v-default-visor-image.view": "Logo", "v-device-master.view": "Device plan", "v-device-preferences.view": "Preferences: Devices", "v-dictionary-index.view": "Create versioning dictionary", "v-dictionary-index-list.view": "Versioning dictionary control", "v-document-preferences.view": "Preferences: Documents", "v-document-type.view": "Create document type", "v-document-type-list.view": "Document type control", "v-document-viewer.view": "Document viewer", "v-document-viewer-link.view": "Document viewer", "v-download-attached.view": "Download", "v-dynamic-field.view": "Create dynamic field", "v-dynamic-field-list.view": "Dynamic field control", "v-edit-user.view": "Edit password", "v-file-type.view": "Create file type", "v-file-type-list.view": "File type control", "v-fill-out-bin.view": "Recycle bin", "v-form-report.view": "Form reports", "v-timesheet-report.view": "Timesheet reports", "v-activity-report.view": "Activity reports", "v-form-report-list.view": "Form reports configuration", "v-form-report-access.view": "Form report access", "v-timesheet-report-list.view": "Timesheet reports configuration", "v-timesheet-report-access.view": "Timesheet report access", "v-timework-report-access.view": "Timework report access", "v-activity-report-list.view": "Activity reports configuration", "v-activity-report-access.view": "Activity report access", "v-form-viewer-link.view": "Form viewer", "v-jasper-logo.view": "Logo", "v-my-activity.view": "My activities", "v-my-activity-report.view": "Report on pending activities.", "v-my-audit-activity.view": "My activities", "v-my-pending-polls.view": "My pending surveys", "v-pdf-conversion-log.view": "Conversion history to PDF", "v-pdf-external-process.view": "External processes of conversion to PDF", "v-pdf-files.view": "PDF files", "v-popup-loader.view": "Pop-Up loader", "v-print-viewer-config.view": "Print settings", "v-realize-service-history.view": "Answer service", "v-request-survey-read.view": "Form to read", "v-to-pick-up-physical-copy.view": "Controlled copies of documents to be collected list", "v-user-agent.view": "Create user agent", "v-user-agent-list.view": "User agent control", "v-footer.view": "Footer", "workload": "Calendar", "web.service.view": "WebService", "timework": "Timework", "reports": "Reports"}, "description": "Description", "activity-type": "Activity", "edit": "Edit", "remove": "Remove", "updateDescription": "Update description", "update": "Update", "cancel": "Cancel", "update-avatar-message": "You do not have a configured profile image, you can set one by clicking", "clickTextHere": "here", "go-to-profile": "Update", "search-code": "# Code", "warning-select-search-option": "Select the module to search", "default-option": "Select module", "ACTIVITY": "Activity", "AUDIT": "Audit activities", "ACTION": "Action activities", "FORMULARIE": "Formularie activities", "codeNotExist": "The activity with the code was not found: "}