{"i18n": {"access": "Control de acceso", "businessUnitAccess": "Permisos por {facility}", "businessUnitAccessHelp": "Todos los usuarios con acceso a {theFacilities} seleccionados podrán acceder a este reporte", "businessUnitDepartmentAccess": "Permisos por {department}", "businessUnitDepartmentAccessHelp": "Todos los usuarios con acceso a {theDepartments} seleccionados podrán acceder a este reporte", "columns": "Columnas que se mostrarán *", "defaultTitle": "A llenar por solicitante", "filters": "Filtros disponibles en el reporte", "filters-help": "Los campos seleccionados en la sección “Columnas que se mostrarán”, son automáticamente utilizados como filtros. No es necesario agregarlas de nuevo.", "general": "Datos generales del reporte", "localTimeForDates": "Mostrar fechas en hora local del usuario", "maxFormReportColumnsNotice": "El número máximo de columnas permitidas es de {maxColumns}. Actualmente tiene {currentColumns} columnas seleccionadas, favor de eliminar alguna columna para poder agregar otra.", "other-systems": "Valores de otros sistemas", "processAccess": "Permisos por proceso", "processAccessHelp": "Todos los usuarios que participen en los procesos seleccionados podrán acceder a este reporte", "requiredNotice": "Favor de llenar los campos requeridos", "restrictRecordsByDepartment": "Restringir permiso a registros por departamento", "save-success": "Se ha guardado la configuración del reporte exitosamente.", "settings": "Configuraciones", "userAccess": "Permisos por usuario", "profileAccess": "Permisos por perfil", "userAccessHelp": "Todos los usuarios seleccionados podrán acceder a este reporte", "profileAccessHelp": "Todos los perfiles seleccionados podrán acceder a este reporte", "whereFillerUserParticipate": "Restringir al usuario registros donde participa", "field": {"code": "Clave", "description": "Nombre", "details": "Descripción larga", "ghost-field": "Campo de otro sistema", "excelIdFields": "Identificador único", "excelUploadEnabled": "Habilitar subir Excel"}, "summaryGroupFields": {"group-by": "Campos de cálculo grupal", "group-by-field": "Campo de agrupación", "is-empty": "Utilice el botón de \"+\" para agregar un campo de agrupación", "add-field": "Campo", "field-description-title": "Captura el nombre de la columna que se creará para mostrar el resultado", "field-description": "Nombre del grupo", "field-groupValueFieldName": "Valor", "field-aggregateFunctionForResult": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "field-groupByFieldName": "Agrupar por", "field-groupByColumnName": "Columna", "field-aggregateFunctionForElseControl": "O<PERSON>s campos"}, "transformedFields": {"transformed-fields-is-empty": "Utilice el botón de \"+\" para agregar un campo basado en reglas", "transformed-fields": "Campos basados en reglas", "add-rule": "Regla", "add-field": "Campo", "field-rules-is-empty": "Utilice el botón de \"+\" para agregar al menos una regla", "when-the-field": "Cuando el campo ", "fullfills-rule": "cumpla la regla de ", "then-show": "entonces muestra", "or-else": " de lo contrario, ", "showRuleValueSelector": "Primero debe seleccionar el campo base", "please-capture-field-message": "Captura el nombre del campo", "please-capture-ruleFieldValue-title": "Campo a comparar", "please-capture-ruleFieldValue-tooltip": "El valor del campo seleccionado será utilizado para determinar el valor del campo transformado", "please-capture-ruleFieldValue-message": "Selecciona el campo contra el que se comparará el campo base", "please-capture-ruleStringValue-message": "Captura el texto contra el que se comparará el campo base", "please-capture-ruleNumberValue-message": "Captura el número contra el que se comparará el campo base", "please-capture-finalValue-message": "Captura el valor que se mostrará si se cumple la condición", "please-capture-typeFinalValue-message": "Selecciona el tipo de valor que se mostrará si se cumple la condición", "please-capture-typeFinalValue-tooltip": "El valor del campo seleccionado se mostrará en el campo transformado", "please-capture-typeFinalValue-title": "<PERSON><PERSON><PERSON> de valor", "please-capture-evaluatedField-message": "Selecciona el campo base para aplicar la regla", "please-capture-evaluatedField-title": "Campo base", "please-capture-evaluatedField-tooltip": "El valor del campo seleccionado será utilizado para determinar el valor del campo transformado", "please-capture-ruleConfigType-message": "Selecciona la condición que se aplicará al campo base", "please-capture-ruleConfigType-tooltip": "El valor del campo seleccionado será utilizado para determinar el valor del campo transformado", "please-capture-ruleConfigType-title": "Condición"}, "transformedFieldRules": {"double-click": "Doble clic para editar", "IS_EMPTY": "Estár vac<PERSON>", "IS_NOT_EMPTY": "No estár vacío", "STARTS_WITH": "<PERSON><PERSON><PERSON> con \"{value}\"", "CONTAINS": "<PERSON><PERSON>er \"{value}\"", "ENDS_WITH": "Terminar con \"{value}\"", "EQUALS": "Ser igual a \"{value}\"", "NOT_EQUALS": "No ser igual a \"{value}\"", "GREATER_THAN": "Ser mayor que \"{value}\"", "LESS_THAN": "Ser menor que \"{value}\"", "GREATER_THAN_OR_EQUALS": "Ser mayor o igual que \"{value}\"", "LESS_THAN_OR_EQUALS": "Ser menor o igual que \"{value}\""}, "typeFinalValues": {"CUSTOM_TEXT": "Texto personalizado", "EVALUATED_FIELD": "Valor del campo evaluado"}, "fieldHeader": {"FLEXI": "Campo del formulario", "FIXED": "Campo fijo", "GHOST": "Campo de otro sistema", "TRANSFORMED": "Campo basado en reglas", "SUMMARY_GROUP": "Campo de cálculo grupal"}, "fixedFields": {"header": "Campos fijos", "placeholder": "Seleccione campos fijos", "help": "Los campos mostrados son fijos. No son editables, pertenecen a la versión actual del formulario.", "label": "Clic para seleccionar los campos fijos que desea mostrar", "REQUESTOR": "Solicitante", "STAGE": "Etapa", "PROGRESS": "Avance", "CODE": "Clave", "STATUS_PROGRESS": "Estado + Avance", "LAST_MODIFIED_DATE": "Ultima fecha de modificación", "CREATED_DATE": "<PERSON><PERSON> c<PERSON>", "STATUS": "Estado", "FILLING_DATE": "<PERSON><PERSON>", "BUSINESS_UNIT": "{Facility}", "BUSINESS_UNIT_DEPARTMENT": "{Department}", "AREA": "{Area}", "REQUEST": "Razón", "OUTSTANDING_SURVEYS": "Descripción"}, "fixedFilters": {"header": "Filtros de campos fijos", "placeholder": "Seleccione filtros de campos fijos", "help": "Los campos mostrados son fijos. No son editables, pertenecen a la versión actual del formulario.", "label": "Clic para seleccionar los campos fijos para filtrar"}, "surveyFields": {"header": "Etapas del formulario", "placeholder": "Seleccione campos del formulario", "help": "Los campos mostrados pertenecen al formulario y son llenados por el usuario.", "label": "Clic para seleccionar los campos del formulario que desea mostrar"}, "surveyFilters": {"header": "Filtros de campos del formulario", "placeholder": "Seleccione filtros de campos del formulario", "help": "Los campos mostrados pertenecen al formulario y son llenados por el usuario.", "label": "Clic para seleccionar los campos del formulario para filtrar"}}, "columns": {"code": "Clave", "description": "Descripción", "account": "C<PERSON><PERSON>"}}