{"i18n": {"access": "Access control", "businessUnitAccess": "Access by {facility}", "businessUnitAccessHelp": "All users with access to selected {theFacilities} will be able to access this report", "businessUnitDepartmentAccess": "Access by {department}", "businessUnitDepartmentAccessHelp": "All users with access to selected {theDepartments} will be able to access this report", "columns": "Columns to display *", "defaultTitle": "To be filled by applicant", "filters": "Available report filters", "filters-help": "Fields selected in the section 'Columns to display' are automatically used as filters. It is not necessary to add them again.", "general": "General settings", "localTimeForDates": "Display dates in user's local time", "maxFormReportColumnsNotice": "The maximum number of columns allowed is {maxColumns}. You currently have {currentColumns} columns selected, please delete some columns so you can add another.", "other-systems": "Values from other systems", "processAccess": "Access by process", "processAccessHelp": "All users who participate in the selected processes will be able to access this report", "requiredNotice": "Please fill in all required fields", "restrictRecordsByDepartment": "Restrict permission to records by department", "save-success": "The report configuration has been saved successfully", "settings": "Settings", "userAccess": "Access by user", "profileAccess": "Access by profile", "userAccessHelp": "All selected users will be able to access this report", "profileAccessHelp": "All selected profiles will be able to access this report", "whereFillerUserParticipate": "Restrict user records where they participate", "field": {"code": "Id", "description": "Name", "details": "Long description", "ghost-field": "Field from other system", "excelIdFields": "Únique Identifier", "excelUploadEnabled": "Enable Excel"}, "summaryGroupFields": {"group-by": "Group calculation fields", "group-by-field": "Grouping field", "is-empty": "Use the \"+\" button to add a grouping field", "add-field": "Field", "field-description-title": "Description title", "field-description": "Group name", "field-groupValueFieldName": "Grouped value", "field-aggregateFunctionForResult": "Calculation", "field-groupByFieldName": "Group by", "field-aggregateFunctionForElseControl": "Method to get the rest of the fields"}, "transformedFields": {"transformed-fields-is-empty": "Use the \"+\" button to add a field based on rules", "transformed-fields": "Fields based on rules", "add-rule": "Rule", "add-field": "Field", "field-rules-is-empty": "Use the \"+\" button to add a at least one rule", "when-the-field": "When the field ", "fullfills-rule": "fulfills the rule of ", "then-show": "then show", "or-else": " otherwise ", "showRuleValueSelector": "You must first select the base field", "please-capture-field-message": "Enter the field name", "please-capture-ruleFieldValue-title": "Field to compare", "please-capture-ruleFieldValue-tooltip": "The value of the selected field will be used to determine the transformed field value", "please-capture-ruleFieldValue-message": "Select the field to compare against the base field", "please-capture-ruleStringValue-message": "Enter the text to compare against the base field", "please-capture-ruleNumberValue-message": "Enter the number to compare against the base field", "please-capture-finalValue-message": "Enter the value to display if the condition is met", "please-capture-typeFinalValue-message": "Select the type of value to display if the condition is met", "please-capture-typeFinalValue-tooltip": "The value of the selected field will be displayed in the transformed field", "please-capture-typeFinalValue-title": "Value type", "please-capture-evaluatedField-message": "Select the base field to apply the rule", "please-capture-evaluatedField-title": "Base field", "please-capture-evaluatedField-tooltip": "The value of the selected field will be used to determine the transformed field value", "please-capture-ruleConfigType-message": "Select the condition to apply to the base field", "please-capture-ruleConfigType-tooltip": "The value of the selected field will be used to determine the transformed field value", "please-capture-ruleConfigType-title": "Condition"}, "transformedFieldRules": {"double-click": "Double click to edit", "IS_EMPTY": "Is empty", "IS_NOT_EMPTY": "Is not empty", "STARTS_WITH": "Starts with \"{value}\"", "CONTAINS": "Contains \"{value}\"", "ENDS_WITH": "Ends with \"{value}\"", "EQUALS": "Equals \"{value}\"", "NOT_EQUALS": "Does not equal \"{value}\"", "GREATER_THAN": "Greater than \"{value}\"", "LESS_THAN": "Less than \"{value}\"", "GREATER_THAN_OR_EQUALS": "Greater than or equal to \"{value}\"", "LESS_THAN_OR_EQUALS": "Less than or equal to \"{value}\""}, "typeFinalValues": {"CUSTOM_TEXT": "Custom text", "EVALUATED_FIELD": "Evaluated field value"}, "fieldHeader": {"FLEXI": "Form field", "FIXED": "Fixed field", "GHOST": "Field from another system", "TRANSFORMED": "Field based on rules", "SUMMARY_GROUP": "Summary field"}, "fixedFields": {"header": "Fixed fields", "placeholder": "Enter a value", "help": "The fields shown here are fixed fields. They are not editable, they belong to the form current version.", "label": "Click to select the fixed fields your want to show", "REQUESTOR": "Requestor", "STAGE": "Stage", "PROGRESS": "Progress", "CODE": "Key", "STATUS_PROGRESS": "Status Progress", "LAST_MODIFIED_DATE": "Last modification date", "CREATED_DATE": "Creation date", "STATUS": "Status", "FILLING_DATE": "Filling Date", "BUSINESS_UNIT": "{Facility}", "BUSINESS_UNIT_DEPARTMENT": "{Department}", "AREA": "{Area}", "REQUEST": "Reason", "OUTSTANDING_SURVEYS": "Description"}, "fixedFilters": {"header": "Fixed fields filters", "placeholder": "Select fixed fields filters", "help": "The filters shown here are fixed. They are not editable, they belong to the form current version.", "label": "Click to select the fixed fields to filter"}, "surveyFields": {"header": "Form stages", "placeholder": "Enter a value", "help": "The fields shown here are fields that users filled.", "label": "Click to select the form fields your want to show"}, "surveyFilters": {"header": "Filters for form fields", "placeholder": "Select form fields filters", "help": "The fields shown here are fields that users filled.", "label": "Click to select the form fields to filter"}}, "columns": {"code": "Code", "description": "Description", "account": "Account"}}