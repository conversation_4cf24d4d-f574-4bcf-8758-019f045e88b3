{"profiles-add-title": "Profile add", "profile-description": "Description", "profile-licenseCode": "License scheme", "profile-scope": "Permission level", "dialog-save-title": "Notification", "dialog-save-message": "The activity type has been saved.", "dialog-save-button": "Add", "dialog-save-list-button": "List", "dialog-fail-title": "Notification", "dialog-fail-message": "There was an error saving the activity type. Please try saving again.", "dialog-fail-fail-button": "Ok", "profile-search": "Search...", "scope-intBUsuarioPlanta": "{Facility}", "scope-intBUsuarioCorporativo": "Corporate (by business unit)", "add": "Save", "return": "Cancel", "clear": "Clear", "control": "Control", "module": {"audit": "Audits", "activity": "Activities", "meeting": "Meetings", "action": "Findings", "configuration": "Configuration", "poll": "Surveys", "document": "Documents", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "meter": "Indicators", "planner": "Projects", "project": "Projects", "device": "Devices", "formularie": "Forms", "fiveS": "5S+1", "catalogs": "Catalogs", "reports": "Reports", "tasks": "Tasks", "happyornot": "Happy or not", "timework": "Timework", "timesheet": "Timesheet"}, "services": {"ACCION_EDITOR": "Editor", "ACCION_ENCARGADO": "Supervisor", "ACCION_LECTOR": "Viewer", "ACCION_POR_ORGANIZACION": "See findings by organization", "ACTIVITY_ADMON_REPORT_ACCESS": "Activities - Assign permissions to reports", "ACTIVITY_BULK_CREATOR": "Activities creation by Excel", "ACTIVITY_CREATOR": "Create activities", "ACTIVITY_DELETE_CONNECTION": "Activities - Delete connections", "ACTIVITY_DELETE_QUERY": "Activities - Delete queries", "ACTIVITY_DELETE_RECURRENCE": "Cancel the series from any activity", "ACTIVITY_DELETE_REPORT": "Activities - Delete reports", "ACTIVITY_ENABLE_CACHE": "Activities - Activate query cache", "ACTIVITY_MANAGER": "Manager", "ACTIVITY_MAX_OPEN_TIME_IGNORE": "Indicates if the maximum open activity time is ignored", "ACTIVITY_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID": "Modify the {department} of the activity", "ACTIVITY_MODIFY_CATEGORY_ID": "Modify the category of the activity", "ACTIVITY_MODIFY_DESCRIPTION": "Modify the description of any activity", "ACTIVITY_MODIFY_DYNAMIC_FIELDS": "Modify the additional fields of the activity", "ACTIVITY_MODIFY_IMPLEMENTATION": "Modify implementation date in activity", "ACTIVITY_MODIFY_IS_PLANNED": "Modify if the activity is planned", "ACTIVITY_MODIFY_OBJECTIVE_ID": "Modify the objective of the activity", "ACTIVITY_MODIFY_PLANNED_HOURS": "Modify the planned hours of the activity", "ACTIVITY_MODIFY_PRIORITY": "Modify the priority of the activity", "ACTIVITY_MODIFY_RECURRENCE": "Edit the series of any activity", "ACTIVITY_MODIFY_RESPONSIBLE": "Modify the activity owner", "ACTIVITY_MODIFY_SOURCE": "Modify the source of the activity", "ACTIVITY_MODIFY_SYSTEM_LINKS": "Modify the description of the activity", "ACTIVITY_MODIFY_VERIFICATION": "Modify verification date in activity", "ACTIVITY_MODIFY_VERIFIER": "Modify the activity supervisor", "ACTIVITY_REGISTER_CONNECTION": "Activities - Create connections", "ACTIVITY_REGISTER_QUERY": "Activities - Create queries", "ACTIVITY_REGISTER_REPORT": "Activities - Create reports", "ACTIVITY_REMOVE_OBJECT_LINKS": "Delete comments, documents and activity files", "ACTIVITY_REPORTS_ALL": "Show all activities", "ACTIVITY_REPORTS_BUSSINESUNIT": "By {Facility}", "ACTIVITY_REPORTS_DEPARTMENT": "By {Department}", "ACTIVITY_REPORTS_USER": "Personal", "ACTIVITY_SET_APPLY": "Cancel any activity", "ACTIVITY_SUPER_FILLER": "Attend any activity", "ACTIVITY_SUPER_VERIFIER": "Supervise any activity", "ACTIVITY_SYNC_CACHE": "Activities - Synchronize query cache", "ACTIVITY_TASK_ONLY": "Activities", "ACTIVITY_TEMPLATE_CREATOR": "Create public templates", "ACTIVITY_VIEWER": "See activities", "ACTIVITY_VIEW_CONNECTION": "Activities - See connections", "ACTIVITY_VIEW_QUERY": "Activities - See Queries", "ACTIVITY_VIEW_SYNC_EVENT": "Activities - See <PERSON><PERSON> Events", "ADMON_ACCESOS": "Access", "ADMON_SISTEMA": "System", "AUDIT_ACTIVITY_DELETE_RECURRENCE": "Cancel the series from any activity", "AUDIT_ACTIVITY_MAX_OPEN_TIME_IGNORE": "Indicates if the maximum open activity time is ignored", "AUDIT_ACTIVITY_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID": "Modify the {department} of the activity", "AUDIT_ACTIVITY_MODIFY_CATEGORY_ID": "Modify the category of the activity", "AUDIT_ACTIVITY_MODIFY_DESCRIPTION": "Modify the description of any activity", "AUDIT_ACTIVITY_MODIFY_DYNAMIC_FIELDS": "Modify the additional fields of the activity", "AUDIT_ACTIVITY_MODIFY_IMPLEMENTATION": "Modify implementation date in activity", "AUDIT_ACTIVITY_MODIFY_IS_PLANNED": "Modify if the activity is planned", "AUDIT_ACTIVITY_MODIFY_OBJECTIVE_ID": "Modify the objective of the activity", "AUDIT_ACTIVITY_MODIFY_PLANNED_HOURS": "Modify the planned hours of the activity", "AUDIT_ACTIVITY_MODIFY_PRIORITY": "Modify the priority of the activity", "AUDIT_ACTIVITY_MODIFY_RECURRENCE": "Edit the series of any activity", "AUDIT_ACTIVITY_MODIFY_RESPONSIBLE": "Modify the activity owner", "AUDIT_ACTIVITY_MODIFY_SOURCE": "Modify the source of the activity", "AUDIT_ACTIVITY_MODIFY_SYSTEM_LINKS": "Modify the description of the activity", "AUDIT_ACTIVITY_MODIFY_VERIFICATION": "Modify verification date in activity", "AUDIT_ACTIVITY_MODIFY_VERIFIER": "Modify the activity supervisor", "AUDIT_ACTIVITY_REMOVE_OBJECT_LINKS": "Delete comments, documents and activity files", "AUDIT_ACTIVITY_SET_APPLY": "Cancel any activity", "AUDIT_ACTIVITY_SUPER_FILLER": "Attend any activity", "AUDIT_ACTIVITY_SUPER_VERIFIER": "Supervise any activity", "AUDIT_HELPER": "Support auditor", "AUDIT_LIDER": "Leader auditor", "AUDIT_QUALITY_MANAGER": "Module Supervisor", "AUDIT_SURVEY_FORMULATION": "Questionnaires", "BULK_ACCESS_CREATOR": "Activities creation by Excel", "CATALOGS_ACTIONS": "Findings catalogs", "CATALOGS_ACTIVITIES": "Activities catalog", "CATALOGS_AUDITS": "Audit catalog", "CATALOGS_COMPLAINTS": "Complaint catalog", "CATALOGS_DEVICES": "Device catalog", "CATALOGS_DOCUMENTS": "Document catalog", "CATALOGS_FIVE_S": "5s+1 catalog", "CATALOGS_FORMULARIES": "Form - Manage Internal Catalogs and External Catalogs", "CATALOGS_GENERALS": "General catalog", "CATALOGS_MEETINGS": "Meeting catalog", "CATALOGS_METERS": "Indicator catalog", "CATALOGS_PROJECTS": "Project catalog", "CATALOGS_SURVEYS": "Survey catalog", "DEVICE_CREATOR": "Create device", "DEVICE_DISPOSE_VIEWER": "Consult deleted devices", "DEVICE_EDITOR": "Edit device", "DEVICE_MANAGER": "Module supervisor", "DEVICE_REALIZE_SERVICE": "Execute services", "DEVICE_SCHEDULED_SERVICES_VIEWER": "See scheduled services", "DEVICE_SERVICE_HISTORY_VIEWER": "See service history", "DEVICE_SERVICE_METRIC_VIEWER": "See variables measuring service", "DEVICE_SERVICE_SCHEDULER": "Schedule services", "DEVICE_VIEWER": "Consult device", "DOCUMENTO_EDITOR": "Editor", "DOCUMENTO_ENCARGADO": "Supervisor", "DOCUMENTO_LECTOR": "Viewer", "DOCUMENT_PRINT_UNCONTROLLED_COPIES": "Print uncontrolled copies", "DOCUMENT_SHARE": "Share documents", "DOCUMENT_STARTED_AT_DOCUMENT_VIEWER": "Viewfinder at home screen", "ESCALATION_MANAGER": "Escalation manager", "FILL_FORM": "Fill forms", "FILL_ONLY": "Forms", "FILL_OUT_HISTORY": "Fill out history / By form", "FILL_OUT_HISTORY_ALL": "Fill out history / All", "FILL_OUT_HISTORY_BUSINESS_UNIT": "Fill out history / Business unit", "FILL_OUT_HISTORY_DEPARTMENT": "Fill out history / Department", "FILL_OUT_HISTORY_MINE": "Fill out history / Owner", "FINDING_ACTIVITY_DELETE_RECURRENCE": "Cancel the series from any activity", "FINDING_ACTIVITY_MAX_OPEN_TIME_IGNORE": "Indicates if the maximum open activity time is ignored", "FINDING_ACTIVITY_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID": "Modify the {department} of the activity", "FINDING_ACTIVITY_MODIFY_CATEGORY_ID": "Modify the category of the activity", "FINDING_ACTIVITY_MODIFY_DESCRIPTION": "Modify the description of any activity", "FINDING_ACTIVITY_MODIFY_DYNAMIC_FIELDS": "Modify the additional fields of the activity", "FINDING_ACTIVITY_MODIFY_IMPLEMENTATION": "Modify implementation date in activity", "FINDING_ACTIVITY_MODIFY_IS_PLANNED": "Modify if the activity is planned", "FINDING_ACTIVITY_MODIFY_OBJECTIVE_ID": "Modify the objective of the activity", "FINDING_ACTIVITY_MODIFY_PLANNED_HOURS": "Modify the planned hours of the activity", "FINDING_ACTIVITY_MODIFY_PRIORITY": "Modify the priority of the activity", "FINDING_ACTIVITY_MODIFY_RECURRENCE": "Edit the series of any activity", "FINDING_ACTIVITY_MODIFY_RESPONSIBLE": "Modify the activity owner", "FINDING_ACTIVITY_MODIFY_SOURCE": "Modify the source of the activity", "FINDING_ACTIVITY_MODIFY_SYSTEM_LINKS": "Modify the link to another system of the activity", "FINDING_ACTIVITY_MODIFY_VERIFICATION": "Modify verification date in activity", "FINDING_ACTIVITY_MODIFY_VERIFIER": "Modify the activity supervisor", "FINDING_ACTIVITY_REMOVE_OBJECT_LINKS": "Delete comments, documents and activity files", "FINDING_ACTIVITY_SET_APPLY": "Cancel any activity", "FINDING_ACTIVITY_SUPER_FILLER": "Attend any activity", "FINDING_ACTIVITY_SUPER_VERIFIER": "Supervise any activity", "FIVES_CREATE": "Create and edit", "FIVES_ITEM": "Articles", "FIVES_LINK": "Link", "FIVES_MANAGER": "Supervisor", "FIVES_MAP": "Map", "FIVES_PRINT": "Print", "FIVES_SECTION": "Section", "FORMULARIO_ALTA": "New form", "FORMULARIO_CONTROL": "Form management", "FORM_ACTIVITY_DELETE_RECURRENCE": "Cancel the series from any activity", "FORM_ACTIVITY_MAX_OPEN_TIME_IGNORE": "Indicates if the maximum open activity time is ignored", "FORM_ACTIVITY_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID": "Modify the {department} of the activity", "FORM_ACTIVITY_MODIFY_CATEGORY_ID": "Modify the category of the activity", "FORM_ACTIVITY_MODIFY_DESCRIPTION": "Modify the description of any activity", "FORM_ACTIVITY_MODIFY_DYNAMIC_FIELDS": "Modify the additional fields of the activity", "FORM_ACTIVITY_MODIFY_IMPLEMENTATION": "Modify implementation date in activity", "FORM_ACTIVITY_MODIFY_IS_PLANNED": "Modify if the activity is planned", "FORM_ACTIVITY_MODIFY_OBJECTIVE_ID": "Modify the objective of the activity", "FORM_ACTIVITY_MODIFY_PLANNED_HOURS": "Modify the planned hours of the activity", "FORM_ACTIVITY_MODIFY_PRIORITY": "Modify the priority of the activity", "FORM_ACTIVITY_MODIFY_RECURRENCE": "Edit the series of any activity", "FORM_ACTIVITY_MODIFY_RESPONSIBLE": "Modify the activity owner", "FORM_ACTIVITY_MODIFY_SOURCE": "Modify the source of the activity", "FORM_ACTIVITY_MODIFY_SYSTEM_LINKS": "Modify the description of the activity", "FORM_ACTIVITY_MODIFY_VERIFICATION": "Modify verification date in activity", "FORM_ACTIVITY_MODIFY_VERIFIER": "Modify the activity supervisor", "FORM_ACTIVITY_REMOVE_OBJECT_LINKS": "Delete comments, documents and activity files", "FORM_ACTIVITY_SET_APPLY": "Cancel any activity", "FORM_ACTIVITY_SUPER_FILLER": "Attend any activity", "FORM_ACTIVITY_SUPER_VERIFIER": "Supervise any activity", "FORM_ADMON_REPORT_ACCESS": "Form - Assign permissions to reports", "FORM_CANCEL_ANY": "Cancel any form", "FORM_DELETE_CONNECTION": "Form - Delete connections", "FORM_DELETE_QUERY": "Form - Delete queries", "FORM_DELETE_REPORT": "Form - Delete reports", "FORM_ENABLE_CACHE": "Form - Activate query cache", "FORM_FILL_IMPERSONATION": "Fill in name of other user", "FORM_REGISTER_CONNECTION": "Form - Create connections", "FORM_REGISTER_QUERY": "Form - Create queries", "FORM_REGISTER_REPORT": "Form - Create reports", "FORM_STOPWATCH_LIST": "View Timework records in Forms", "FORM_STOPWATCH_REGISTER": "Register Timework in Forms", "FORM_SYNC_CACHE": "Form - Synchronize query cache", "FORM_VIEW_CONNECTION": "Form - See connections", "FORM_VIEW_QUERY": "Form - See Queries", "FORM_VIEW_SYNC_EVENT": "Form - See <PERSON>ache Events", "GEOLOCATION_ACCESS_HISTORY": "View geolocation data", "HAPPYORNOT_SURVEY_CREATOR": "Create surveys", "HAPPYORNOT_SURVEY_FILLER": "Answer", "HAPPYORNOT_SURVEY_MODIFIER": "Modify any survey", "HAPPYORNOT_SURVEY_RESTORE": "Restore surveys", "HAPPYORNOT_SURVEY_VIEW": "See reports", "HAPPYORNOT_SURVEY_VIEW_ALL": "See all surveys", "INDICADOR_EDITOR": "Editor", "INDICADOR_ENCARGADO": "Supervisor", "INDICADOR_LECTOR": "Viewer", "INTERFACE_TRESS": "Interface TRESS", "MEETING_ACTIVITY_DELETE_RECURRENCE": "Cancel the series from any activity", "MEETING_ACTIVITY_MAX_OPEN_TIME_IGNORE": "Indicates if the maximum open activity time is ignored", "MEETING_ACTIVITY_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID": "Modify the {department} of the activity", "MEETING_ACTIVITY_MODIFY_CATEGORY_ID": "Modify the category of the activity", "MEETING_ACTIVITY_MODIFY_DESCRIPTION": "Modify the description of any activity", "MEETING_ACTIVITY_MODIFY_DYNAMIC_FIELDS": "Modify the additional fields of the activity", "MEETING_ACTIVITY_MODIFY_IMPLEMENTATION": "Modify implementation date in activity", "MEETING_ACTIVITY_MODIFY_IS_PLANNED": "Modify if the activity is planned", "MEETING_ACTIVITY_MODIFY_OBJECTIVE_ID": "Modify the objective of the activity", "MEETING_ACTIVITY_MODIFY_PLANNED_HOURS": "Modify the planned hours of the activity", "MEETING_ACTIVITY_MODIFY_PRIORITY": "Modify the priority of the activity", "MEETING_ACTIVITY_MODIFY_RECURRENCE": "Edit the series of any activity", "MEETING_ACTIVITY_MODIFY_RESPONSIBLE": "Modify the activity owner", "MEETING_ACTIVITY_MODIFY_SOURCE": "Modify the source of the activity", "MEETING_ACTIVITY_MODIFY_SYSTEM_LINKS": "Modify the link to another system of the activity", "MEETING_ACTIVITY_MODIFY_VERIFICATION": "Modify verification date in activity", "MEETING_ACTIVITY_MODIFY_VERIFIER": "Modify the activity supervisor", "MEETING_ACTIVITY_REMOVE_OBJECT_LINKS": "Delete comments, documents and activity files", "MEETING_ACTIVITY_SET_APPLY": "Cancel any activity", "MEETING_ACTIVITY_SUPER_FILLER": "Attend any activity", "MEETING_ACTIVITY_SUPER_VERIFIER": "Supervise any activity", "PLANNER_ACTIVITY_DELETE_RECURRENCE": "[invalid] Remove the series from any activity", "PLANNER_ACTIVITY_MAX_OPEN_TIME_IGNORE": "[invalid] Indicates if the maximum open activity time is ignored", "PLANNER_ACTIVITY_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID": "Modify the {department} of the activity", "PLANNER_ACTIVITY_MODIFY_CATEGORY_ID": "Modify the category of the activity", "PLANNER_ACTIVITY_MODIFY_DESCRIPTION": "Modify the description of any project or task", "PLANNER_ACTIVITY_MODIFY_DYNAMIC_FIELDS": "Modify the additional fields of tasks and projects", "PLANNER_ACTIVITY_MODIFY_IMPLEMENTATION": "Modify end date in projects and tasks", "PLANNER_ACTIVITY_MODIFY_IS_PLANNED": "Modify if the activity is planned", "PLANNER_ACTIVITY_MODIFY_OBJECTIVE_ID": "Modify the objective of the activity", "PLANNER_ACTIVITY_MODIFY_PLANNED_HOURS": "Modify the planned hours of the activity", "PLANNER_ACTIVITY_MODIFY_PRIORITY": "Modify the priority of the activity", "PLANNER_ACTIVITY_MODIFY_RECURRENCE": "[invalid] Edit the series of any activity", "PLANNER_ACTIVITY_MODIFY_RESPONSIBLE": "Modify project leader", "PLANNER_ACTIVITY_MODIFY_SOURCE": "Modify the source of the activity", "PLANNER_ACTIVITY_MODIFY_SYSTEM_LINKS": "Modify the link to another system of the activity", "PLANNER_ACTIVITY_MODIFY_VERIFICATION": "[invalid] Modify verification date in activity", "PLANNER_ACTIVITY_MODIFY_VERIFIER": "[invalid] Modify supervisor in activity", "PLANNER_ACTIVITY_REMOVE_OBJECT_LINKS": "Delete comments, documents and files from projects and tasks", "PLANNER_ACTIVITY_SET_APPLY": "Cancel any project or task", "PLANNER_ACTIVITY_SUPER_FILLER": "Mark as done any project or task", "PLANNER_ACTIVITY_SUPER_VERIFIER": "[invalid] Supervise any activity", "PLANNER_ADD_ANY_TASKS": "Add tasks to any project", "PLANNER_CREATOR": "Create projects", "PLANNER_VIEW_ALL": "See all projects without restriction", "PLANNER_VIEW_BUSINESS_UNIT": "See all projects from its {facility}", "PLANNER_VIEW_BUSINESS_UNIT_DEP": "See all projects from its {department}", "PLANNER_VIEW_OWNED": "See projects where there's participation", "PROYECTO_CONTABILIDAD": "Accounting", "PROYECTO_EDITOR": "Editor", "PROYECTO_ENCARGADO": "Supervisor", "PROYECTO_LECTOR": "Viewer", "QUEJA_DAR_RESPUESTA_CUALQUIERA": "Respond to any complaint", "QUEJA_EDITOR": "Verifier", "QUEJA_ENCARGADO": "Supervisor", "QUEJA_LECTOR": "Viewer", "REPORTE_ACCION": "See findings reports", "REPORTE_AUDITORIA": "See Audit reports", "REPORTE_CONFIGURACION": "See Configuration Reports", "REPORTE_CUESTIONARIO": "See Survey Reports", "REPORTE_DOCUMENTO": "See Document reports", "REPORTE_ENCUESTA": "See Survey reports", "REPORTE_GERENCIAL": "See Management Reports", "REPORTE_INDICADOR": "See Meter reports", "REPORTE_PROYECTO": "See Project Reports", "REPORTE_QUEJA": "See Complaint reports", "REPORTE_REUNION": "See Meeting reports", "REPORTE_TIMESHEET": "See timesheet reports", "REPORT_ACTIVITY": "See Activity Reports", "REPORT_FORM": "See Forms Reports", "REUNION_EDITOR": "Editor", "REUNION_ENCARGADO": "Supervisor", "REUNION_LECTOR": "Viewer", "SURVEY_MANAGER": "Manager", "SURVEY_RESPONDENT": "Answer", "SURVEY_SUPERVISOR": "Supervisor", "TIMEWORK_ADMON_REPORT_ACCESS": "Manage access to timework reports", "TIMEWORK_CREATE_CONNECTION": "Create timework connection report", "TIMEWORK_CREATE_QUERY": "Create timework query report", "TIMEWORK_CREATE_REPORT": "Create timework report", "TIMEWORK_DELETE_CONNECTION": "Delete timework connection report", "TIMEWORK_DELETE_QUERY": "Delete timework query report", "TIMEWORK_DELETE_REPORT": "Delete timework report", "TIMEWORK_ENABLE_CACHE": "Enable timework reports cache", "TIMEWORK_GEOLOCATION_ACCESS": "View geolocation data", "TIMEWORK_LIST": "View Timework records in Forms", "TIMEWORK_MANUALLY_REGISTER": "Manually register and modify Timework records", "TIMEWORK_REGISTER": "Register Timework", "TIMEWORK_RESTORE": "Consult recycle bin", "TIMEWORK_SETTINGS": "Manage Timework preferences", "TIMEWORK_SYNC": "Synchronize Timework", "TIMEWORK_SYNC_CACHE": "Synchronize timework report cache", "TIMEWORK_VIEW_CONNECTION": "View timework connection report", "TIMEWORK_VIEW_QUERY": "View timework query report", "TIMEWORK_VIEW_REPORT": "View timework report", "TIMEWORK_VIEW_SYNC_CACHE_EVENT": "View timework cache events", "TS_ADMON_REPORT_ACCESS": "Timesheet - Assign permissions to reports", "TS_DELETE_CONNECTION": "Timesheet - Delete connections", "TS_DELETE_QUERY": "Timesheet - Delete queries", "TS_DELETE_REPORT": "Timesheet - Delete reports", "TS_EDIT_BUSINESS_UNIT": "Able to modify any report from its {facility}", "TS_EDIT_BUSINESS_UNIT_DEP": "Able to modify any report from its {department}", "TS_ENABLE_CACHE": "Timesheet - Enable query cache", "TS_READ_BUSINESS_UNIT": "See all reports from its {facility}", "TS_READ_BUSINESS_UNIT_DEP": "See all reports from its {department}", "TS_REGISTER_CONNECTION": "Timesheet - Create connections", "TS_REGISTER_PLANNED": "Report planned activities", "TS_REGISTER_QUERY": "Timesheet - Create queries", "TS_REGISTER_REPORT": "Timesheet - Create reports", "TS_REGISTER_UNPLANNED": "Report unplanned activities", "TS_SYNC_CACHE": "Timesheet - Synchronize query <PERSON>ache", "TS_VIEW_CONNECTION": "Timesheet - See connections", "TS_VIEW_QUERY": "Timesheet - See queries", "TS_VIEW_SYNC_EVENT": "Timesheet - See cache events", "UNRESTRICTED_DOCUMENT_ACCESS": "Access to all documents and Schedule notifications", "USUARIO_CORPORATIVO": "Corporative (by business unit)", "USUARIO_PLANTA": "{Facility}"}}