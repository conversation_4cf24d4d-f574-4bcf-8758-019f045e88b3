{"i18n": {"title": "Task delivery types", "title_TaskCategory": "Task categories", "action": "Details", "status-dialog-message": "Do you really want to change the status of this task delivery type?", "accept-dialog-message": "Task delivery type status has been changed successfully", "status-dialog-message_TaskCategory": "Do you really want to change the status of this task category?", "accept-dialog-message_TaskCategory": "Task category type status has been changed successfully", "businessUnitDepartmentName": "{Departments}", "entity_code": "ID", "entity_status": "Status", "entity_description": "Name", "entity_taskDeliveryTypeInfo": "Description", "entity_taskCategoryInfo": "Description", "createdDate": "Date created", "lastModifiedDate": "Last modified date", "taskDeliveryTypeName": "Task delivery type"}, "catalogLabel": "Assignment deliver"}