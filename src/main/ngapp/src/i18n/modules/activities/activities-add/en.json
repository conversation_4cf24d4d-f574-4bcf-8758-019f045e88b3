{"action.aci.activity-title": "Immediate corrective action", "action.aci.dialog-save-message": "The immediate corrective action has been successfully saved.", "action.att.activity-title": "New action to take", "action.att.dialog-save-message": "The action to take has been has been successfully saved.", "activity-title": "Advanced activity creation", "activity-businessUnit": "{Facility}", "activity-businessUnitDepartment": "Target {department}", "activity-cancel": "Cancel", "activity-code": "ID", "activity-daysToVerify": "Day(s) to verify", "activity-description": "Description", "activity-disableBelongSeriesTooltip": "Delete series", "activity-enableBelongSeriesTooltip": "Create series", "activity-enableDeliverySetUp": "Delivery set up", "activity-fillForm": "Form to fill", "activity-fillForm-label": "Form to fill", "activity-fillForm-placeholder": "Select the form here", "activity-fillType": "Fill mode", "activity-implementation": "Implementation date", "activity-implementationPeriodicity": "Implementation recurrence", "activity-implementationStartDate": "Start date", "activity-implementer": "Responsible", "activity-preImplementer": "Pre-assigned responsible", "activity-isPlanned": "Is planned", "activity-plannedHours": "Estimated hours", "activity-no-data-businessUnit": "No {facility} asociated", "activity-no-data-businessUnitDepartment": "No {department} asociated", "activity-objectiveId": "Objective", "activity-plannerTask": "Task", "activity-priority": "Priority", "activity-reminder": "Implementation date", "activity-sameDayImplementation": "The activity type indicates that this task will alert the responsible to initiate it right away.", "activity-categoryId": "Category", "activity-source": "Source", "activity-status": "Status", "activity-taskCategoryId": "task category", "activity-taskDeliveryTypeId": "Task delivery type", "activity-today": "Today", "activity-type": "Type", "activity-verification": "Verification date", "activity-verificationPeriodicity": "Verification recurrence", "activity-verifier": "Verifier", "activity-systemLinks": "Links to other systems", "activity-order": "Order", "auto-code": "The ID will be generated automatically", "cancelButtonLabel": "Cancel", "dialog-fail-fail-button": "Ok", "dialog-fail-load-message": "There was an error loading the activity. Please try to load again.", "dialog-fail-save-message": "There was an error saving the activity. Please try saving again.", "dialog-fail-title": "Notification", "dialog-save-button": "Add", "dialog-save-list-button": "List", "dialog-save-message": "The activity has been saved.", "activities-created-action-title": "What action do you want to carry out?", "dialog-save-title": "Notification", "fill-all-fields": "Remember to fill all the fields marked with an asterisk.", "invalidNumber": "The max value for the field is \"999\"", "savingWithInvalidHiddenFields": "There are hidden required fields. All fields are displayed so they can be completed.", "invalidVerifierSelection": "The verifier can not be the responsible", "planner-title": "New project", "quick-template-editor-title": "Configure activity template", "status-draft": "Draft", "subplanner-title": "New subproject", "todayButtonLabel": "Today", "stages": {"stage": {"": "Saving...", "0": "None", "1": "Implementation", "2": "Verification", "3": "Creation"}}, "columns": {"code": "Code", "description": "Description", "action": "Detail"}, "activity-excel-create": "Create with Excel", "i18n": {"dialog-redirect": "You will be sent to the list of activities<br>Are you sure you want to continue?", "redirectOptionsTextLabel": {"CREATE": "Create", "CONTROL": "Control", "MY_ACTIVITIES": "My activities", "PLANNING": "Planning", "PENDINGS": "Pendings"}}, "searchPlaceholder": "Enter a search term"}