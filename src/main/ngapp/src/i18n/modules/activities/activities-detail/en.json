{"no-available-types": "There are no activity types available, ask your administrator to configure the corresponding types or set a default value.", "activities-activity-hasChilds-implementation-subtitle": "Activity with {{childCount}} sub-tasks. The implementation date for the sub-tasks is {{commitmentDate}}", "activities-activity-hasChilds-implementation-belongSeries-subtitle": "Periodic activity with {{childCount}} sub-tasks. The implementation date for the sub-tasks is {{commitmentDate}}", "activities-activity-hasChilds-implementation-title": "Activity details with {{childCount}} sub-tasks", "activities-activity-hasChilds-implementation-belongSeries-title": "Periodic activity details with {{childCount}} sub-tasks", "activities-activity-hasChilds-withparent-implementation-subtitle": "Activity with {{childCount}} sub-tasks with implementation date on {{commitmentDate}}, which belongs to the parent activity {{parentDescription}}", "activities-activity-hasChilds-withparent-implementation-title": "Activity details with {{childCount}} sub-tasks wich belongs parent activity {{parentDescription}}", "activities-activity-hasChilds-program-subtitle": "The next implementation date wil be on {{nextImplementation}} and the next verification on {{nextVerification}}", "activities-activity-hasChilds-program-title": "Activity with {{childCount}} sub-tasks.", "activities-activity-hasChilds-program-belongSeries-title": "Activity details with {{childCount}} sub-tasks", "activities-activity-hasChilds-program-belongSeries-subtitle": "Activity with {{childCount}} sub-tasks. The next periodic implementation date wil be on {{nextImplementation}} and the next verification on {{nextVerification}}", "activities-activity-hasChilds-title": "Activity details with sub-tasks", "activities-activity-hasChilds-subtitle": "Activity details with sub-tasks", "activities-activity-hasChilds-belongSeries-title": "Periodic activity details with sub-tasks", "activities-activity-hasChilds-verification-subtitle": "Activity verification with {{childCount}} sub-tasks. The commitment date for the sub-tasks is {{commitmentDate}}", "activities-activity-hasChilds-verification-belongSeries-subtitle": "Periodic activity verification. The commitment date for the sub-tasks is {{commitmentDate}}", "activities-activity-hasChilds-verification-title": "Activity verification with {{childCount}} sub-tasks wich belongs parent activity {{parentDescription}}", "activities-activity-hasChilds-withparent-verification-subtitle": "Activity verification with {{childCount}} sub-tasks with commitment date on {{commitmentDate}}, which belongs to the parent activity {{parentDescription}}", "activities-activity-hasChilds-withparent-verification-title": "Activity verification with {{childCount}} sub-tasks", "activities-activity-hasChilds-verification-belongSeries-title": "Periodic activity verification with {{childCount}} sub-tasks", "activities-activity-hasSiblings-implementation-subtitle": "Implementation date {{commitmentDate}}, this activity belongs to a group of sub-tasks of parent activity {{parentActivityCode}}", "activities-activity-hasSiblings-implementation-belongSeries-subtitle": "Implementation date {{commitmentDate}}, this activity belongs to a periodic group of sub-tasks of task {{parentActivityCode}}", "activities-activity-hasSiblings-implementation-title": "Sub-task details", "activities-activity-hasSiblings-implementation-belongSeries-title": "Periodic sub-task details", "activities-activity-hasSiblings-title": "Sub-tasks details", "activities-activity-hasSiblings-subtitle": "Sub-tasks details", "activities-activity-hasSiblings-belongSeries-title": "Periodic sub-tasks details", "activities-activity-hasSiblings-verification-subtitle": "Implementation date {{commitmentDate}}, this activity belongs to a group of sub-tasks of parent activity {{parentActivityCode}}", "activities-activity-hasSiblings-verification-belongSeries-subtitle": "Periodic activity verification with commitment date {{commitmentDate}}, this activity belongs to a group of periodic sub-tasks of parent activity {{parentActivityCode}}", "activities-activity-hasSiblings-verification-title": "Sub-task verification details", "activities-activity-hasSiblings-verification-belongSeries-title": "Periodic sub-task verification details", "activities-activity-hasSiblings-program-belongSeries-title": "Periodic sub-tasks details", "activities-activity-hasSiblings-program-belongSeries-subtitle": "Periodic sub-tasks details", "activities-activity-hasSiblings-program-title": "Subtask summary", "activities-activity-hasSiblings-program-subtitle": "Subtask Summary", "activities-activity-implementation-subtitle": "Implementation date {{commitmentDate}}", "activities-activity-implementation-belongSeries-subtitle": "Periodic implementation date {{commitmentDate}}", "activities-activity-implementation-title": "Implementation description", "activities-activity-implementation-belongSeries-title": "Periodic activity implementation details", "activities-activity-program-subtitle": "The next implementation date wil be on {{nextImplementation}} and the next verification on {{nextVerification}}", "activities-activity-program-belongSeries-subtitle": "Periodic activity where the next implementation date will be on {{nextImplementation}} and the next periodic verification on {{nextVerification}}", "activities-activity-program-title": "Activity description", "activities-activity-program-belongSeries-title": "Activity series details", "activities-activity-subtitle": "Implementation date {{commitmentDate}}", "activities-activity-belongSeries-subtitle": "Implementation periodic date {{commitmentDate}}", "activities-activity-title": "Activity details", "activities-activity-belongSeries-title": "Activity series details", "activities-activity-parent-folio-title": "The progress of parent activity is", "activities-activity-other-subtask-title": "Other sub-tasks from parent", "activities-activity-subtask-title": "subtask", "activities-activity-commitment-subtask-title": "Implementation date ", "activities-activity-verification-subtitle": "Implementation date {{commitmentDate}}", "activities-activity-verification-title": "Verification description", "activities-planner-hasChilds-implementation-subtitle": "Project deliverable with {{childCount}} activities which starts on {{commitmentDate}}", "activities-planner-hasChilds-implementation-title": "Project deliverable with {{childCount}} activities", "activities-planner-hasChilds-program-subtitle": "Project parent deliverable summary where the next deliverable start date is at {{nextImplementation}}", "activities-planner-hasChilds-title": "Periodic deliverable details with sub-tasks", "activities-planner-hasSiblings-implementation-subtitle": "This deliverable is a sub-task of \"{{parentPlannerName}}\" and it should be started at {{commitmentDate}}", "activities-planner-hasSiblings-implementation-title": "Project deliverable", "activities-planner-hasSiblings-title": "Periodic sub-tasks details", "activities-planner-implementation-subtitle": "Project deliverable which starts on {{commitmentDate}}", "activities-planner-implementation-title": "Project deliverable", "activities-planner-program-subtitle": "The next deliverable start date is at {{nextImplementation}}", "activities-planner-program-title": "Project deliverable series details", "activities-planner-subtitle": "The deliverable start date is at {{commitmentDate}}", "activities-planner-title": "Project deliverable details", "activities-planner-parent-folio-title": "The progress of project is", "activities-planner-other-subtask-title": "Other deliverables from the planner", "activities-planner-subtask-title": "deliverable", "activities-planner-commitment-subtask-title": "Ends at", "confirm-new-date": "Do you really want to save the new date ${new-date}?", "editable-info": "You may only modify marked fields", "activity-status": "Status", "activity-code": "ID", "document-code": "ID", "planner-task-subtask": "Activity", "activity-description": "Description", "activity-type": "Type", "activity-objectiveId": "Goal", "activity-isPlanned": "Is planned", "activity-cancellationReason": "Cancellation reason", "activity-followUpImplementationDelay": "Follow up on delay", "activity-mustUpdateImplementationAtReturn": "Modify implementation date when returning to do", "activity-plannedHours": "Estimated hours", "activity-fillForm": "Form to fill", "activity-fillType": "Fill mode", "activity-categoryId": "Category", "activity-source": "Source", "activity-implementer": "Responsible", "activity-program-implementer": "Active user(s)", "activity-implementation": "Planned implementation date", "activity_plannedImplementationWeek": "Implementation week", "activity_plannedVerificationWeek": "Verification week", "activity-implementationStart": "Start date", "activity-verifier": "Verifier", "activity-verification": "Planned verification date", "activity-reminder": "Implementation date", "activity-priority": "Priority", "activity-businessUnit": "{Facility}", "activity-businessUnitDepartment": "Target {department}", "activity-plannerTask": "Deliverable", "activity-no-data-businessUnit": "No {facility} asociated", "activity-no-data-businessUnitDepartment": "No {department} asociated", "activity-implementationPeriodicity": "Implementation recurrence", "activity-verificationPeriodicity": "Verification recurrence", "activity-systemLinks": "Links to other systems", "activity-creator": "Author", "activityResolution": "Resolution", "activity_createdDate": "Creation datetime", "activity-order": "Order", "activity-preImplementer": "Pre-assigned responsible", "activity-main-code": "Parent activity", "activity-main-status": "Status parent activity", "activity-main-creator": "Author parent activity", "status-draft": "Draft", "partition": "Partition", "add": "Save", "return": "Cancel", "clear": "Clear", "empty-files-activity": "There's no attached files", "empty-comments-activity": "There's no comments yet", "empty-history-activity": "There's no history yet to show", "empty-documents-activity": "There's no related documents added", "empty-files-planner": "There's no attached files", "empty-comments-planner": "There's no comments yet", "empty-history-planner": "There's no history yet to show", "empty-documents-planner": "There's no related documents added", "empty-partitions": "There's no partitions added yet", "empty-resolutions": "There's no resolutions available, ask your system administrator to enable at least one.", "empty-belongSeries": "There's no events yet created", "date-todayButtonLabel": "Today", "date-cancelButtonLabel": "Cancel", "dialog-save-title": "Notification", "dialog-save-message": "The activity has been saved.", "dialog-save-button": "Add", "dialog-save-list-button": "List", "dialog-fail-title": "Notification", "dialog-fail-save-message": "There was an error saving the activity. Please try saving again.", "dialog-fail-load-message": "There was an error loading the activity. Please try to load again.", "dialog-fail-fail-button": "Ok", "comment-deleted-true": "The comments has been successfully removed.", "comment-deleted-false": "Unable to delete the comment, try again later.", "document-deleted-true": "The document has been successfully removed.", "document-deleted-false": "Unable to delete the document, try again later.", "invalidVerifierSelection": "The verifier can not be the responsible", "files": {"saved-file-result-true": "The file has been successfully added.", "saved-file-result-false": "There was an error saving the file. Please try again.", "saved-file-cancelled": "Transfer cancelled", "file-deleted-result-true": "The file has been successfully removed.", "file-deleted-result-false": "Unable to delete the file, try again later.", "maximum-file-size-exceeded": "The file you want to upload exceeds the maximum allowed size.", "file-storage-limit-exceeded": "Files storage limit exceeded. Please contact the administrator.", "file-comment-result-true": "The file description has been saved successfully.", "file-comment-result-false": "There was an error saving the file description."}, "save-fail": "There was an error with the field.", "save-succes": "Field saved", "pendingVerification": "unverified", "invalid-implementation-periodicity": "Periodic activity detail where the implementation is not repeated in the date period configured", "activity-events": "Events", "activity-belongSeries": "Events", "activity-event-belongSeries": "Implementations and verifications", "activity-partitions": "Partitions", "activities-planner-hasChilds-implementation-relationshipTab": "Deliverable", "activities-planner-hasChilds-implementation-childTab": "Deliverables", "activities-planner-hasSiblings-implementation-relationshipTab": "Other deliverables", "activities-activity-verification-relationshipTab": "Implementations", "activities-activity-verification-belongSeries-relationshipTab": "Events", "activities-activity-hasSiblings-program-relationshipTab": "Other subtasks", "activities-activity-hasSiblings-program-belongSeries-relationshipTab": "Other events", "activities-activity-hasSiblings-verification-relationshipTab": "Subtasks", "activities-activity-hasSiblings-verification-belongSeries-relationshipTab": "Events", "activities-activity-hasSiblings-implementation-relationshipTab": "Other subtasks", "activities-activity-hasSiblings-implementation-belongSeries-relationshipTab": "Other events", "activities-activity-hasChilds-verification-relationshipTab": "Implementations", "activities-activity-hasChilds-verification-childTab": "Subtasks", "activities-activity-hasChilds-implementation-childTab": "Subtasks", "activities-activity-hasChilds-withparent-verification-relationshipTab": "Subtasks", "activities-activity-hasChilds-verification-belongSeries-relationshipTab": "Events", "activities-activity-hasChilds-implementation-relationshipTab": "Subtasks", "activities-activity-hasChilds-implementation-belongSeries-relationshipTab": "Other subtasks", "cancel-series-dialog-message": "Do you really want to cancel the activity series?", "failed-auto-generated-quick-save": "The record could not be saved. Please review the information and try again.", "accept-dialog-message": "The activity series was cancelled", "menuOption": {"ADD_DOCUMENT": "Relate document", "ADD_IMPLEMENTATION": "Add implementation", "ADD_PARTICIPANT": "Add participant", "ADD_RELATION": "Add relation", "ADD_RESPONSIBLE": "Add responsible", "ADD_VERIFICATION": "Add verification", "APPLY_VERIFY": "Reject cancellation", "CANCEL_RECURRENCE": "Cancel series", "FILL": "Fill form", "FINISH": "Mark as finished", "INCOMPLETE": "Return to \"To do\"", "MARK_ACTIVE": "Relate document", "MARK_INACTIVE": "Relate document", "NOT_APPLY": "Cancel", "NOT_APPLY_VERIFY": "Verify as cancelled", "ACCEPT_NOT_APPLY": "Accept as cancelled", "OPEN_FLOW": "View fill flow", "OPEN_FORM": "Open form", "OPEN_FORM_VERIFY": "Open form", "PROGRESS": "Capture progress", "UNDONE": "Verify as \"Undone\"", "VERIFY": "Verify as \"Finished\"", "VERIFY_IMPLEMENTATIONS": "Verify series", "CLOSE": "Add resolution", "REASSIGN_VERIFY": "Reassign verify", "MULTY_REASSIGN_VERIFY": "Reassign multiple", "DUPLICATE": "Duplicate activity"}, "activities": {"title": "Controls", "column": {"entity_status": "Status", "action": "Action", "entity_code": "ID", "entity_description": "Description", "entity_type_description": "Type", "entity_type_module": "<PERSON><PERSON><PERSON>", "entity_parentCode": "Parent ID", "entity_objective_description": "Objective", "entity_singleImplementer_description": "Responsible", "entity_plannedImplementationDate": "Start date", "entity_reminder": "Planned implementation", "entity_implementationOn": "Implementation date", "entity_progress": "Progress", "entity_singleVerifier_description": "Verifier", "entity_plannedVerificationDate": "Planned verification", "entity_verificationOn": "Verification on", "entity_createdDate": "Created", "entity_lastModifiedDate": "Last modification", "entity_source_description": "Source"}}, "source_module": {"FORMULARIE": "Form", "ACTION": "Finding", "PLANNER": "Planner", "AUDIT": "Audit"}, "entity_type_module": {"audit": "Audits", "activity": "Activities", "meeting": "Meetings", "action": "Findings", "configuration": "Configuration", "poll": "Surveys", "document": "Documents", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "meter": "Indicators", "planner": "Projects", "project": "Projects", "device": "Devices", "formularie": "Forms", "fiveS": "5S+1"}, "stages": {"stage": {"": "Saving...", "0": "None", "1": "Implementation", "2": "Verification", "3": "Creation"}}, "commitmentTask": {"1": "Implementation", "2": "Verification", "3": "Series"}, "actualHours": "Hours consumed", "swapActivitiesTab": "Substitutes", "empty-swap-activity": "There is no substitures activities", "participants": "Participant(s)", "participantTitle": "Members of the deliverable are restricted, before adding it to a deliverable you must add it to your project.", "messages": {"remove-Participant": "Member removed", "add-Participant": "Member added"}, "i18n": {"optionalEditionFields": {"failedMessage": "Failed to save field as there is a conflict between start, implementation and verification date. Please change the date value or turn on the \"Keep days of anticipation to attend\".", "keepAnticipationAttendDays": {"name": "Keep days of anticipation to attend", "tooltip": "By activating this option, the implementation date will be automatically adjusted by editing the start date to keep the selected value of days to anticipation. In case this exceeds the verification date, it will also be modified automatically."}}, "activity-saveMessage": "Activity saved successfully.", "activity-saveMessageNoVerification": "Implementation successfully saved, but its verification date is greater than the current verification, activities are assigned to the new verification {verificationCode}.", "noDepartment": "No available data", "text-copied-clipboard": "Text copied to clipboard.", "UPDATE_ALL": "Yes, all", "UPDATE_THIS": "No, just this one", "UPDATE_COINCIDENCES": "Yes, edit the ones that matches"}, "planner-saveMessage": "Deliverable saved successfully.", "homepage": "Home page will be opened instead the current page. Do you wish to continue?", "notice-dynamic-field-empty": "There are no dynamic fields configured.", "confirmChangeVerificationMessage": "Verification date will be updated. Do you wish to continue?", "confirmChangeDatesMessage": "Verification and implementation dates will be updated. Do you wish to continue?", "showEvidences": "Show gallery images", "hideEvidences": "Hide gallery images", "activityClassification": "Classification", "planner-task-end-date-message": "To add deliverables, modify the end date of the project.", "planner-task-permission-message": "To add deliverables, add the correct permission.", "planner-participant-end-date-message": "To add participants, modify the end date of the project.", "creationDateLabel": "Created on {{ createdDate }} at {{ createdTime }} by {{ author }} of {theDepartment} {{ department }}.", "creationDateLabelImp": "Implementation created on {{ createdDate }} at {{ createdTime }} by {{ author }} of the {theDepartment} {{ department }}.", "creationDateLabelVer": "Verificación creada el {{ createdDate }} a las {{ createdTime }} por {{ author }} del {department} {{ department }}.", "periodicity-title": "Periodicity", "plannedHours-update-1": "Do you want to make this change to all activities or just this implementation?", "plannedHours-update-2": "Do you want to make this change to all activities or just this verification?", "plannedHours-update-3": "Do you want to make this change to all activities or just this main activity?", "activitiesRelationshipTab": "Relations", "confirm-same-user-autoassign": "There are {{ count }} implementation(s) ssigned to {{ user }} from {{ start }} to {{ end }}. Are you sure you want to assign activity again from {{ newStart }} to {{ newEnd }}?", "you": "yourself", "already-pre-assigned": "The user is already pre-assigned", "taskStartDateLessThanProjectStartDate": "The deliverable start date cannot be less than the project start date.", "timesheetHeader": "TIMESHEET"}