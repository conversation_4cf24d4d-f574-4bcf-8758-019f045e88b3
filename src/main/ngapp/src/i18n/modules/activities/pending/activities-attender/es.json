{"i18n": {"progressTitle": "Modificar avance", "progressLabel": "Comentario de avance", "comment": "Capture un comentario de avance para la actividad ", "planner-discardMessage": "Por favor capture un comentario para cancelar el entregable", "planner-discardTitle": "Cancelar entregable", "planner-cancelCommentLabel": "Comentario de cancelación", "generic-discardMessage": "Por favor indique la razón para cancelar la actividad", "generics-discardTitle": "Cancelar actividad", "generics-cancelReasonLabel": "Razón de cancelación", "generics-cancelCommentLabel": "Comentarios adicionales", "swapActivitiesRequired": "Se requiere indicar las actividades sustitutas.", "generics-discardVerificationTitle": "Verificar como cancelada", "generic-discardVerificationMessage": "Capture el motivo por el cual verifica la actividad como cancelada.", "generics-incompleteCommentLabel": "Motivo para regresar a \"Por realizar\"", "generics-discardVerificationAcceptTitle": "Aceptar cancelación", "generic-discardVerificationAcceptMessage": "Capture el motivo por el cual acepta la actividad como cancelada.", "activityCancellationReasons": {"CHANGE_PLANNED_HOURS": "Ajustar las horas estimadas de otra actividad", "NEW_ACTIVITY": "Se atenderá otra actividad", "NONE": "Ninguna razón"}, "verifiedSucess": "La actividad fue verificada.", "apply": "Capture el motivo por el cual rechaza la cancelación de la actividad", "undone": "Captura el motivo para marcar la actividad como \"No realizada\"", "verify": "Su verificación a la actividad será guardada, si desea continuar capture un comentario de verificación.", "activity-resolution": "Resolución de actividad", "implementsTitle": "Implementaciones de la serie", "multipleReassignGridTitle": "Seleccione todas las verificaciones a reasignar", "verifyManyGridTitle": "Seleccione las actividades a verificar", "activity-undoned": "Actividad no realizada", "activity-not-apply": "Verificar como cancelada", "not-apply": "Captura el motivo para marcar la actividad como \"Cancelada\"", "dataRequired": "<PERSON><PERSON> llenar todos los campos obligatorios", "activity-verified": "Actividad verificada", "activity-accept-not-apply": "Aceptar cancelación", "accept-not-apply": "Captura el motivo para aceptar la actividad como \"Cancelada\"", "reassignedSucess": "La verificación de la actividad fue reasignada correctamente.", "incompleteFormError": "La información esta incompleta por favor llene los campos de verificador y fecha.", "okDialogButton": "<PERSON><PERSON><PERSON><PERSON>", "confirmReassign": "Se reasignará la verificación de {count} actividades al usuario {userName} ¿Seguro de que desea continuar?", "verifier-same-as-implementer": "El verificador es el mismo que el implementador."}}