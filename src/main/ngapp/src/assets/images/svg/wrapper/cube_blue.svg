<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" >
    <defs>
        <linearGradient id="Gradient_2" x1="50" x2="50" y1="50" y2="0" gradientUnits="userSpaceOnUse" spreadMethod="pad">
            <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.7"/>
            <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0.1"/>
        </linearGradient>
        <linearGradient id="Gradient_3" x1="50" x2="50" y1="75" y2="0" gradientUnits="userSpaceOnUse" spreadMethod="pad">
            <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.1"/>
            <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0.7"/>
        </linearGradient>
        <g id="background">
            <path fill="url(#background-gradient)" stroke="none" d=" M 93.35 75 L 93.35 25 50 0 6.65 25 6.65 75 50 100 93.35 75 Z"/>
        </g>
        <mask id="mask">
            <path fill="#FFFFFF" stroke="none" d=" M 93.35 75 L 93.35 25 50 0 6.65 25 6.65 75 50 100 93.35 75 Z"/>
        </mask>
        <g id="gloss-top">
            <path fill="url(#Gradient_2)" stroke="none" d=" M 93.35 25.05 L 93.35 25 50 0 6.65 25 6.65 25.05 50 50 93.35 25.05 Z"/>
        </g>
        <g id="gloss-right">
            <path fill="url(#Gradient_3)" stroke="none" d=" M 93.35 75 L 93.35 25 50 0 6.65 25 93.35 75 Z"/>
        </g>
        <g id="gloss-left">
            <path fill="url(#Gradient_3)" stroke="none" d=" M 6.65 25 L 6.65 75 93.35 25 50 0 6.65 25 Z"/>
        </g>
        <linearGradient id="background-gradient" x1="0" x2="0" y1="0" y2="1">
            <stop offset="0%" stop-color="#2244dd"/>
            <stop offset="100%" stop-color="#0dc1ff"/>
        </linearGradient>
        <g id="picture" transform="scale(6.926167190907926) translate(-173.5310001373291 -20)">
            <path fill="#9acee6" stroke="#none" stroke-width="1.5" d="M176.5 34.438L185 20"/>
        </g>
    </defs>
    <use fill="url(#background-gradient)" xlink:href="#background"/>
    <g mask="url(#mask)">
        <g transform="
				        translate(
                                50 50
)
                            translate(0 0)  scale(0.5)
                            translate(
                                -50 -50
)">
            <use xlink:href="#picture"/>
        </g>
    </g>
    <use opacity="1" xlink:href="#gloss-top"/>
</svg>
