{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ngapp": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser-esbuild", "options": {"outputPath": "../webapp/qms/", "stylePreprocessorOptions": {"includePaths": ["node_modules", ""]}, "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js", "<PERSON><PERSON><PERSON>"], "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": [{"input": "src/loader.scss", "bundleName": "loader", "inject": true}, {"input": "src/materialFonts.scss", "bundleName": "materialFonts", "inject": true}, {"input": "src/foundation.scss", "bundleName": "foundation", "inject": true}, {"input": "src/styles.scss", "bundleName": "styles", "inject": true}, {"input": "src/componentColors.scss", "bundleName": "componentColors", "inject": true}, {"input": "src/devextreme.scss", "bundleName": "devextreme", "inject": true}], "scripts": ["node_modules/hammerjs/hammer.min.js"], "allowedCommonJsDependencies": ["ajv", "vscode-jsonrpc/lib/common/events.js", "vscode-jsonrpc/lib/common/cancellation.js", "stackedit-js", "qrcode-generator", "natural-compare-lite", "localforage", "json-source-map", "jmespath", "devextreme-quill", "debug", "dayjs", "dayjs/plugin/weekday", "dayjs/plugin/utc", "dayjs/plugin/timezone", "dayjs/plugin/relativeTime", "dayjs/plugin/localizedFormat", "dayjs/plugin/localeData", "dayjs/plugin/isoWeeksInYear", "dayjs/plugin/isoWeek.js", "dayjs/plugin/isLeapYear", "dayjs/plugin/isBetween", "dayjs/plugin/duration", "dayjs/plugin/customParseFormat.js", "dayjs/plugin/advancedFormat.js", "dayjs/locale/es", "cytoscape-fcose", "cytoscape-cose-bilkent", "@braintree/sanitize-url"], "extractLicenses": false, "buildOptimizer": false, "optimization": false, "sourceMap": true, "namedChunks": true}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "11mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "1024kb"}], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": {"inline": true}}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "buildOptimizer": true, "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "ngapp:build"}, "configurations": {"production": {"buildTarget": "ngapp:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "ngapp:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": ["zone.js", "<PERSON><PERSON><PERSON>"], "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": ["node_modules/hammerjs/hammer.min.js"], "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "stylePreprocessorOptions": {"includePaths": ["node_modules", ""]}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"eslintConfig": "eslint.config.js", "lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": "5f0fc719-20e4-4c31-9409-bff6de336c81", "cache": {"enabled": true, "environment": "all"}}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}