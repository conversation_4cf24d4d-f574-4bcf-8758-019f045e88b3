package mx.bnext.qms.web;

import bnext.licensing.LicenseUtil;
import java.io.IOException;
import mx.bnext.qms.configuration.util.BnextStrutsUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.UserLogin;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/licencing")
public class LicencingController {

    private final String CHANGE_LICENSE_PATH = "/administrator/unlicensed/change-license.jsp";
    private final String UNLICENSED_PATH = "/administrator/unlicensed/unlicensed.jsp";

    private final BnextStrutsUtils strutsUtils = new BnextStrutsUtils();

    private void setAttributes(final Model model) throws IOException {
        model.addAttribute("newLicenseClass", "invalidValue");
        model.addAttribute("licenseError", LicenseUtil.getConfiguration());
        model.addAttribute("sendButtonLabel", LicenseUtil.generateSubmitButtonLabel());
        model.addAttribute("copyConfig", LicenseUtil.generateCopyConfigButtonLabel());
        model.addAttribute("reloadLicense", LicenseUtil.generateReloadButtonLabel());
        model.addAttribute("updateSystemLicenseTitle", LicenseUtil.generateSystemLicenseTitle());
        model.addAttribute("cancelButtonLabel",LicenseUtil.generateCancelButtonLabel());
        strutsUtils.setSystemAttributes(model);
        strutsUtils.setLoggedUserAttributes(model);
    }

    @RequestMapping(method = RequestMethod.GET, path = "new")
    public String needsLicence(Model model) throws IOException {
        if (LicenseUtil.validLicense()) {
            return "redirect:/";
        }
        setAttributes(model);
        return UNLICENSED_PATH;
    }

    @RequestMapping(method = RequestMethod.POST, path = "new")
    public String saveNewLicence(@RequestParam("newLicense") String licence, Model model) throws IOException {
        final boolean result = LicenseUtil.updateLicenseFile(licence);
        if (result) {
            return "redirect:/";
        }
        setAttributes(model);
        return UNLICENSED_PATH;
    }

    @RequestMapping(method = RequestMethod.GET, path = "edit")
    public String editLicense(Model model) throws IOException {
        if (!SecurityUtils.isLoggedUserAdmin()) {
            return "redirect:/";
        }
        setAttributes(model);
        return CHANGE_LICENSE_PATH;
    }

    @RequestMapping(method = RequestMethod.POST, path = "edit")
    public String saveEditionLicense(@RequestParam("newLicense") String licence, Model model) throws IOException {
        final boolean result = LicenseUtil.updateLicenseFile(licence);
        UserLogin login = new UserLogin();
        login.removeAllSession();
        if (result) {
            return "redirect:/";
        }
        setAttributes(model);
        return CHANGE_LICENSE_PATH;
    }

}
