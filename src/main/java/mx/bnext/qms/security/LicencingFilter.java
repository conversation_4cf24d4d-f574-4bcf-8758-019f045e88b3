package mx.bnext.qms.security;

import Framework.Config.Utilities;
import bnext.licensing.LicenseUtil;
import bnext.util.PwaHelper;
import java.io.IOException;
import java.util.Objects;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;
import qms.framework.util.AboutApp;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Component
@Order(Integer.MIN_VALUE)
public class LicencingFilter extends GenericFilterBean {

    private static final Logger LOGGER = Loggable.getLogger(LicencingFilter.class);
    public static final String REST_LICENCING_NEW_PATH = "rest/licencing/new";
    private final PwaHelper helper = new PwaHelper();
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        final HttpServletRequest httpRequest = (HttpServletRequest) request;
        LOGGER.trace("Validating Licence");
        final Boolean askLicense = shouldAskLicense(httpRequest);
        if (askLicense) {
            final Boolean isAngularConfig = helper.isAngularConfigFile(httpRequest);
            if (isAngularConfig) {
                final HttpServletResponse httpResponse = (HttpServletResponse) response;
                httpResponse.sendError(404);
                return;
            }
            final String message = ""
                    + "La validación de licencias falló, verificar la configuración de licencias:"
                    + "\r/Detalles: Id del sistema: {}, Versión: {}"
                    + "\r/Error: {}"
                    + "\r/Licencia Actual: {}.";
            String errorDetails = LicenseUtil.getError();
            if (errorDetails != null && !errorDetails.isEmpty()) {
                errorDetails = errorDetails
                        .replaceAll("(?s)<[^>]*>(\\s*<[^>]*>)*", " ");
            }
            LOGGER.error(
                    message,
                    Utilities.getSettings().getSystemId(),
                    AboutApp.getProjectVersion(),
                    errorDetails,
                    Utilities.getSerializedObj(LicenseUtil.getCurrentLicense())
            );
            final HttpServletResponse httpResponse = (HttpServletResponse) response;
            final String licencingUri = getLicencingUri(httpRequest);
            httpResponse.sendRedirect(licencingUri);
        } else {
            LOGGER.trace("Valid Licence");
            chain.doFilter(request, response);
        }
    }


    private Boolean shouldAskLicense(final HttpServletRequest httpRequest) {
        final String uri = httpRequest.getRequestURI();
        if (uri == null || uri.contains(PwaHelper.MANIFEST_FILE_NAME)) {
            return false;
        }
        final String licencingUri = getLicencingUri(httpRequest);
        if (Objects.equals(licencingUri, uri)) {
            return false;
        }
        final Boolean askLicense = !LicenseUtil.validLicense();
        return askLicense;
    }

    private String getLicencingUri(final HttpServletRequest httpRequest) {
        final String servletPath = httpRequest.getContextPath();
        final String licencingUri;
        if (servletPath != null && servletPath.endsWith("/")) {
            licencingUri = servletPath + REST_LICENCING_NEW_PATH;
        } else {
            licencingUri = servletPath + "/" + REST_LICENCING_NEW_PATH;
        }
        return licencingUri;
    }

    public static String getBaseUrl(HttpServletRequest request) {
        String scheme = request.getScheme() + "://";
        String serverName = request.getServerName();
        String serverPort = (request.getServerPort() == 80) ? "" : ":" + request.getServerPort();
        String contextPath = request.getContextPath();
        return scheme + serverName + serverPort + contextPath;
    }

}
