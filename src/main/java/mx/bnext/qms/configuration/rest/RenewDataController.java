package mx.bnext.qms.configuration.rest;

import bnext.login.Login;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import mx.bnext.core.util.Loggable;
import mx.bnext.qms.configuration.util.BnextStrutsUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("renew-data")
public class RenewDataController extends Loggable {

    private final String RENEW_DATA_PATH = "/administrator/login/renew-data.jsp";
    
    private final BnextStrutsUtils strutsUtils = new BnextStrutsUtils();
    
    @GetMapping()
    public String renewData(final Model model, final HttpServletRequest request) throws IOException {
        strutsUtils.setSystemAttributes(model);
        strutsUtils.setLoggedUserAttributes(model);
        model.addAttribute(Login.RENEW_DATA_LOCATION, SecurityUtils.getRenewDataLocation(request));
        model.addAttribute(Login.RENEW_DATA_PASSWORD, SecurityUtils.getRenewPasswordAttribute(request));
        model.addAttribute(Login.RENEW_DATA_TIMEZONE, SecurityUtils.getRenewDataTimezone(request));
        return RENEW_DATA_PATH;
    }      
    
}