package mx.bnext.qms.configuration.rest;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import java.util.List;
import mx.bnext.core.util.Loggable;
import mx.bnext.qms.configuration.util.DynamicUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.configuration.dto.DynamicFieldHtmlConfig;
import qms.configuration.dto.DynamicFieldsFilesDTO;
import qms.configuration.entity.DynamicFieldsFiles;
import qms.configuration.util.GridStateUserHelper;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.dto.DynamicFieldLiteDTO;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Lazy
@RestController
@RequestMapping("dynamic-fields")
public class DynamicFieldController extends Loggable {

    @GetMapping()
    public List<DynamicFieldLiteDTO> all() {
        return Utilities.getBean(IDynamicFieldDAO.class).getActivesLite();
    }
    
    @PostMapping()
    @RequestMapping("dynamic-field-html-value")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsFilesDTO dynamicFieldHtmlValue(@RequestBody DynamicFieldHtmlConfig configInfo) {
        return DynamicUtils.dynamicFieldHtmlValue(configInfo);
    }
}
