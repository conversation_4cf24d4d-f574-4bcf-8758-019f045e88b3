package mx.bnext.qms.configuration.rest;

import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import bnext.reference.document.FileRef;
import com.google.common.collect.ImmutableMap;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.file.FileHandler;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.file.IFileData;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import qms.access.dto.ILoggedUser;
import qms.framework.file.BnextSpringConfiguration;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Controller
@RequestMapping("files") 
public class FilesController {
    private static final Logger LOGGER = Loggable.getLogger(FilesController.class);

    public static Boolean isValidFilesStorageMaxSize(Long size) throws IllegalStateException {
        Long filesStorageLimit = Utilities.limitFileStorageBytes();
        final Settings settings = Utilities.getSettings();
        if (filesStorageLimit != null && filesStorageLimit > 0) {
            Long uploadedFilesStorage = settings.getTotalUploadedBytes();
            long sizeUploaded = uploadedFilesStorage + size;

            if (sizeUploaded > filesStorageLimit) {
                LOGGER.error("The file can´t be uploaded limit {} Bytes exeeded (size: {})", sizeUploaded, filesStorageLimit);
                return false;
            }
        }
        return true;
    }

    public static Path getMultipartFile(final MultipartFile multipart, final FileManager fileManager) throws IllegalStateException, IOException {
        final String originalFilename = multipart.getOriginalFilename();
        final long size = multipart.getSize();
        final long maxBytes = BnextSpringConfiguration.getUploadFileMaxSizeBytes();
        if (size > maxBytes && maxBytes > 0) {
            LOGGER.error(
                    "The file {} exceeds the size limit of {} Bytes (size: {})",
                    originalFilename, maxBytes, size);
            return null;
        }
        final Path convFile = FileHandler.createTempFile(
                "TempMultipPart_",
                originalFilename,
                fileManager.getTempFolder()
        );
        multipart.transferTo(convFile.toFile());
        return convFile;
    }

    protected Boolean validFilesStorageMaxSize(Long size) throws IllegalStateException {
        return isValidFilesStorageMaxSize(size);
    }

    protected Path multipartToFile(final MultipartFile multipart, final FileManager fileManager) throws IllegalStateException, IOException {
        return getMultipartFile(multipart, fileManager);
    }

    @RequestMapping(method = RequestMethod.POST)
    public ResponseEntity<Map<String, Object>> handleFileUpload(final @RequestParam("file") MultipartFile multipart)
            throws IllegalStateException, IOException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FileManager fileManager = new FileManager();
        final Path file = multipartToFile(multipart, fileManager);
        if (file == null) {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(ImmutableMap.of("errorMessage", "maximum-file-size-exceeded"));
        }
        if (!validFilesStorageMaxSize(multipart.getSize())) {
            LOGGER.error("Files storage limit exceeded by filename {} with bytes {}", multipart.getOriginalFilename(), multipart.getSize());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(ImmutableMap.of("errorMessage", "file-storage-limit-exceeded"));
        }
        final GenericSaveHandle gsh = fileManager.persistFileContent(
                -1L,
                file,
                multipart.getOriginalFilename(),
                multipart.getContentType(),
                true, // para cambiar a false, es necesario modificar el FRONT en Angular
                loggedUser
        ).getGsh();
        if (gsh.getOperationEstatus() == 1) {
            prepareDocumentViewer(gsh, fileManager, loggedUser);
            return ResponseEntity.ok(gsh.getJsonEntityData());
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    private void prepareDocumentViewer(final GenericSaveHandle gsh, final FileManager fileManager, final ILoggedUser loggedUser) {
        final FileRef fileData = Utilities.getUntypedDAO().HQLT_findById(FileRef.class, gsh.getPersistentId());
        final IFileData metadata = fileManager.newMetadataInstance(fileData);
        final boolean isPdf = FileUtils.CONTENT_TYPE_PDF.equals(metadata.getContentType());
        if (FileUtils.isCommonImage(metadata.getExtension()) || isPdf) {
            fileManager.prepareFileForPdfViewer(metadata, loggedUser);
        }
    }
    
    @RequestMapping(value = "/{id}")
    public ResponseEntity<Void> load(
            final HttpServletResponse response,
            final @PathVariable(value = "id", required = true) Long id
    ) throws IOException {
        return FileManager.download(response, id, SecurityUtils.getLoggedUser());
    }


}
