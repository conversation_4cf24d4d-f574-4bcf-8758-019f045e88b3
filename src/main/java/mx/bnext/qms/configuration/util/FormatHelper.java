package mx.bnext.qms.configuration.util;

import DPMS.Mapping.Document;
import bnext.reference.document.FileRef;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.Survey;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.util.Loggable;
import qms.access.dto.ILoggedUser;
import qms.custom.dao.IBaseFormatDAO;
import qms.custom.dto.AnswerMetadataDTO;
import qms.custom.dto.AnswerMetadataFieldDTO;
import qms.custom.dto.DummyFlexiFieldsDTO;
import qms.form.dto.FormSlimReportDataSourceDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.util.SurveyUtil;
import qms.framework.dao.ISlimReportsDAO;
import qms.framework.file.FileManager;
import qms.framework.util.CacheRegion;
import qms.framework.util.DatabaseFieldNameComparator;
import qms.util.FormUtil;
import qms.util.QMSException;

public class FormatHelper extends Loggable {

    private final IBaseFormatDAO dao;

    private String title;
    private FormSlimReportDataSourceDTO fieldsDataSource;
    private Object format;
    private String surveyAnswersTable;
    private ILoggedUser loggedUser;
    private Boolean forceRefresh;
    private String version;

    public FormatHelper(IBaseFormatDAO dao) {
        this.dao = dao;
    }

    public boolean downloadFile(
            final Long fileId,
            final Long id,
            final Long outstandingSurveysId,
            final HttpServletResponse response,
            final ILoggedUser loggedUser
    ) {
        try {
            final FileRef file = this.getDao().HQLT_findById(FileRef.class, fileId);
            final FileManager fileManager = new FileManager();
            String description = file.getDescription();
            if (!description.endsWith(".html")) {
                description += ".html";
            }
            fileManager.writeHeaders(
                    response,
                    description,
                    file.getContentType(),
                    file.getContentSize(),
                    false
            );
            fileManager.writeFileContentToOutput(file, response.getOutputStream(), loggedUser);
            return true;
        } catch (final Exception ex) {
            getLogger().error(
                    "Failed to load saved HTML for [id: {}, outstandingSurveysId: {}]",
                    id,
                    outstandingSurveysId
            );
            return false;
        }
    }

    /**
     * Load the general info required to load the data
     * @param surveyId
     * @param outstandingSurveysId Just informative, null is possible
     * @param documentId
     * @param id
     * @param loggedUser
     * @param forceRefresh
     * @return True if loaded, else False
     * @throws IOException
     * @throws QMSException
     */
    public boolean loadGeneralInfo(
            final Long surveyId,
            final Long outstandingSurveysId,
            final Long documentId,
            final Long id,
            final ILoggedUser loggedUser,
            final Boolean forceRefresh
    ) throws IOException, QMSException {
        // Se evalúa y se refresca la información del formato
        final String surveyAnswersTable = dao.HQL_findSimpleString(" "
                        + " SELECT c.answersTable"
                        + " FROM " + Survey.class.getCanonicalName() + " c"
                        + " WHERE c.id = :id",
                ImmutableMap.of("id", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        if (surveyAnswersTable.isEmpty()) {
            getLogger().warn(
                    "Answer tables not avaialable for outstandingSurveysId {} and documentId {}.",
                    outstandingSurveysId,
                    documentId);
            return false;
        }
        final Map<String, Object> documentData = dao.HQL_findSimpleMap(" SELECT new map( "
                        + " d.masterId AS masterId "
                        + ",d.description AS title "
                        + ",d.version AS version "
                        + " )"
                        + " FROM " + Document.class.getCanonicalName() + " d "
                        + " WHERE d.id = :documentId",
                ImmutableMap.of(
                        "documentId", documentId
                )
        );
        if (documentData.isEmpty()) {
            getLogger().warn(
                    "Invalid document for refreshing printing templates for document {} and outstandingSurveysId {}.",
                    documentId,
                    outstandingSurveysId
            );
            return false;
        }
        final Object format = dao.fullLoadById(id);
        if (format == null) {
            getLogger().warn(
                    "There are not formats to refreshing printing templates for outstandingSurveysId {} and documentId {}.",
                    outstandingSurveysId,
                    documentId
            );
            return false;
        }
        final String masterId = (String) documentData.get("masterId");
        final FormSlimReportDataSourceDTO fieldsDataSource = FormUtil.getSurveyDataFields(masterId, true, loggedUser, true, false);
        if (fieldsDataSource == null || fieldsDataSource.getSurveyFields() == null || fieldsDataSource.getSurveyFields().isEmpty()) {
            getLogger().warn(
                    "There are not fields to refreshing printing templates for outstandingSurveysId {} and documentId {}.",
                    outstandingSurveysId,
                    documentId
            );
            return false;
        }
        final String title = (String) documentData.get("title");
        final String version = (String) documentData.get("version");
        setFields(title, fieldsDataSource, format, surveyAnswersTable, loggedUser, forceRefresh, version);
        return true;
    }

    private void setFields(String title, FormSlimReportDataSourceDTO fieldsDataSource, Object format, String surveyAnswersTable, ILoggedUser loggedUser, Boolean forceRefresh, String version) {
        setTitle(title);
        setFieldsDataSource(fieldsDataSource);
        setFormat(format);
        setSurveyAnswersTable(surveyAnswersTable);
        setLoggedUser(loggedUser);
        setForceRefresh(forceRefresh);
        setVersion(version);
    }

    /**
     * Retorna los campos disponibles, null si existe algun error
     * @param flexiFieldsList
     * @param fixedFieldsList
     * @param id
     * @param surveyId
     * @param masterId
     * @return
     */
    public List<String> getRealFieldsList(List<AnswerMetadataDTO> flexiFieldsList, List<AnswerMetadataFieldDTO> fixedFieldsList, Long id, Long surveyId, String masterId, Map<String, SurveyDataFieldDTO> fieldTypes) {
        final Set<String> flexiFields = flexiFieldsList.stream()
                .map(AnswerMetadataDTO::getDatabaseFieldName)
                .collect(Collectors.toSet());
        final List<String> realFieldsList = new ArrayList<>();
        final List<SurveyDataFieldDTO> surveyFields = new ArrayList<>(fieldsDataSource.getSurveyFields());
        final Map<String, Long> metaIds = SurveyUtil.getSurveyAnswerMetadataIds(surveyId);
        Collections.sort(surveyFields, new DatabaseFieldNameComparator());
        // FLEXI FIELDS
        surveyFields.forEach(f -> {
            if (!(flexiFields.contains(f.getName()) && metaIds.containsKey(f.getName()))) {
                return;
            }
            realFieldsList.add(f.getName());
            fieldTypes.put(f.getName(), f);
        });
        final boolean missingFixedFields = realFieldsList.isEmpty() && !fixedFieldsList.isEmpty();
        if ((realFieldsList.isEmpty() && fixedFieldsList.isEmpty()) || missingFixedFields) {
            // Vaciar el HTML??? falta revisar valores FIXED!!
            if (missingFixedFields) {
                getLogger().error("Error refreshing format with id {} for survey with id {}. "
                        + "The format of document with masterId {} does not have regular fields. Create an ISSUE to support only FIXED fields.",
                        id,
                        fieldsDataSource.getSurveyId(),
                        masterId
                );
            }
            return null;
        }
        return realFieldsList;
    }

    /**
     * Obtiene la respuestas desde la tabla SURVEY del llenado dado por outstandingSurveysId, masterId y considerando solo los campos de realFieldsList
     * @param documentMasterId
     * @param realFieldsList
     * @return
     */
    public Map<String, Object> getAnswers(
            String documentMasterId,
            List<String> realFieldsList,
            Long outstandingSurveysId
    ) throws QMSException {
        final String sql = ISlimReportsDAO.getParsedSlimReportQuery(
                    documentMasterId,
                    surveyAnswersTable,
                    new DummyFlexiFieldsDTO(realFieldsList),
                    false,
                    true,
                    false
                )
                + " AND o.outstanding_surveys_id = :outstandingSurveysId "
        ;
        return this.getDao().SQL_findSimpleMap(sql, ImmutableMap.of("outstandingSurveysId", outstandingSurveysId));
    }

    // SETTERS Y GETTERS
    public IBaseFormatDAO getDao() {
        return dao;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public FormSlimReportDataSourceDTO getFieldsDataSource() {
        return fieldsDataSource;
    }

    public void setFieldsDataSource(FormSlimReportDataSourceDTO fieldsDataSource) {
        this.fieldsDataSource = fieldsDataSource;
    }

    public Object getFormat() {
        return format;
    }

    public void setFormat(Object format) {
        this.format = format;
    }

    public String getSurveyAnswersTable() {
        return surveyAnswersTable;
    }

    public void setSurveyAnswersTable(String surveyAnswersTable) {
        this.surveyAnswersTable = surveyAnswersTable;
    }

    public ILoggedUser getLoggedUser() {
        return loggedUser;
    }

    public void setLoggedUser(ILoggedUser loggedUser) {
        this.loggedUser = loggedUser;
    }

    public Boolean getForceRefresh() {
        return forceRefresh;
    }

    public void setForceRefresh(Boolean forceRefresh) {
        this.forceRefresh = forceRefresh;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
