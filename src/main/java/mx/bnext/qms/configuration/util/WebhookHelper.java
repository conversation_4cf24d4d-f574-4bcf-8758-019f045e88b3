package mx.bnext.qms.configuration.util;

import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import isoblock.surveys.dao.hibernate.IOutstandingSurveys;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jsoup.select.Selector;
import qms.access.dto.ILoggedUser;
import qms.custom.dao.IWebhookDAO;
import qms.custom.util.FilePersistentUtil;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.entity.AnswerPartType;
import qms.form.entity.Webhook;
import qms.form.util.FixedField;
import qms.form.util.SurveyFieldAnswerType;
import qms.form.util.SurveyUtil;
import qms.util.FormUtil;
import qms.util.QMSException;
import qms.util.dto.FormatedAnswerConfig;

public class WebhookHelper extends FormatHelper {

    public WebhookHelper(IWebhookDAO dao) {
        super(dao);
    }

    public String refreshFormat(
            final Long surveyId,
            final IOutstandingSurveys o,
            final Long id,
            final ILoggedUser loggedUser,
            final Boolean forceRefresh
    ) throws IOException, QMSException {
        if (this.loadGeneralInfo(surveyId, o.getId(), o.getDocumentId(), id, loggedUser, forceRefresh)) {
            return refreshFormatWithData(this.getTitle(), (Webhook)this.getFormat(), surveyId, o, this.getLoggedUser(), this.getForceRefresh());
        };
        return null;
    }

    private String refreshFormatWithData(
            final String title,
            final Webhook format,
            final Long surveyId,
            final IOutstandingSurveys o,
            final ILoggedUser loggedUser,
            final Boolean forceRefresh
    ) throws QMSException {
        final Map<String, SurveyDataFieldDTO> fieldTypes = new HashMap<>();
        final List<String> realFieldsList = this.getRealFieldsList(format.getFlexiFields(), format.getFixedFields(), format.getId(), surveyId, format.getMasterId(), fieldTypes);
        if (realFieldsList == null) {
            return null;
        }
        final Map<String, Object> answers = this.getAnswers(format.getMasterId(), realFieldsList, o.getId());
        String str = FilePersistentUtil.writeDataToFile(format.getFileId(), loggedUser.getId());
        if (str == null) {
            return null;
        }
        // Convertir el string JSON a un JsonObject (Se hace como arreglo para poder iterar con forEach)
        final JsonObject[] jsonObject = {JsonParser.parseString(str).getAsJsonObject()};
        jsonObject[0].addProperty("version", getVersion());
        jsonObject[0].addProperty("id", o.getId());
        jsonObject[0].addProperty("code", o.getCode());
        jsonObject[0].addProperty("userId", loggedUser.getId());
        jsonObject[0].addProperty("userAccount", loggedUser.getAccount());
        jsonObject[0].addProperty("userCode", loggedUser.getCode());
        jsonObject[0].addProperty("timestamp", ZonedDateTime.now().toString());
        // Reemplazar valores en el JSON
        answers.forEach((key, value) -> {
            String fieldKey =  "${" + key + "}"; // En caso de reemplazar en cadena usar -> "\\$\\{" + key + "\\}";
            if (value == null && FixedField.fromAlias(key) != null) {
                jsonObject[0] = replaceValue(jsonObject[0], fieldKey, null);
                return;
            }
            try {
                if (FixedField.fromAlias(key) == null) {
                    // Valores de campos de SURVEY del formulario
                    final SurveyDataFieldDTO field = fieldTypes.get(key);
                    if (field == null) {
                        jsonObject[0] = replaceValue(jsonObject[0], fieldKey, replacement(value));
                    } else {
                        final AnswerPartType partType = SurveyUtil.getAnswerPartType(
                                field.getName(), field.getFieldCode(), field.getCatalogSubType()
                        );
                        final SurveyFieldAnswerType answerType = SurveyUtil.getSurveyFieldAnswerType(field);
                        FormatedAnswerConfig config = new FormatedAnswerConfig(
                                value,
                                field.getFieldType(),
                                field.getIncludeTime(),
                                "true",
                                "false",
                                answerType,
                                partType
                        );
                        final String answer = FormUtil.getFormatedAnswer(config);
                        if (answer != null && (answer.equals("true") || answer.equals("false")) ) {
                            fieldKey = "${" + key + "}";
                            jsonObject[0] = replaceValue(jsonObject[0], fieldKey, Boolean.parseBoolean(answer));
                        } else {
                            jsonObject[0] = replaceValue(jsonObject[0], fieldKey, replacement(answer));
                        }
                    }
                } else {
                    // Valores de campos FIJOS del formulario
                    jsonObject[0] = replaceValue(jsonObject[0], fieldKey, replacement(value));
                }
            } catch(Selector.SelectorParseException e) {
                getLogger().error("Failed replacing [{}:{}] at [formatId: {}, defaultHtmlId: {}] - REASON: {}", new Object[] {
                        key, value, format.getId(), format.getFileId(), e.getMessage()
                });
            } catch(Exception e) {
                jsonObject[0] = replaceValue(jsonObject[0], fieldKey, replacement(value));
                getLogger().warn("Invalid replacing format [{}:{}] at [formatId: {}, defaultHtmlId: {}] - REASON: {}", new Object[] {
                        key, value, format.getId(), format.getFileId(), e.getMessage()
                });
            }
        });
        return jsonObject[0].toString();
    }

    private String replacement(Object value) {
        return value == null || value.toString().isEmpty() ? null : value.toString();
    }

    /**
     * Reemplaza los valores que coincidan con el fieldKey en el json
     * @param object Objeto json a iterar
     * @param value El valor a fijar
     * @param fieldKey El nombre del campo a reemplazar
     * @return
     */
    private JsonObject replaceValue(JsonObject object, String fieldKey, Object value) {
        JsonObject result = new JsonObject();
        object.keySet().forEach(key -> {
            Object currentValue = object.get(key);
            if (currentValue instanceof JsonObject) { // Si el objeto a revisar tambien es un JSON se realiza recurrencia
                result.add(key, replaceValue((JsonObject) currentValue, fieldKey, value));
            } else if (currentValue instanceof JsonNull) { // Si el objeto es nulo se agrega como null
                result.add(key, JsonNull.INSTANCE);
            } else if (((JsonPrimitive) currentValue).getAsString().equals(fieldKey)) { // Se reemplaza por valor, no por key, ya que el valor trae el nombre del campo.
                if (value == null) {
                    result.add(key, JsonNull.INSTANCE);
                } else if (value instanceof Boolean) {
                    result.addProperty(key, (Boolean) value);
                } else {
                    result.addProperty(key, value.toString());
                }
            } else { // Se mantienen los datos
                if (((JsonPrimitive) currentValue).isBoolean()) {
                    result.addProperty(key, ((JsonPrimitive) currentValue).getAsBoolean());
                } else {
                    result.addProperty(key, ((JsonPrimitive) currentValue).getAsString());
                }
            }
        });
        return result;
    }

}
