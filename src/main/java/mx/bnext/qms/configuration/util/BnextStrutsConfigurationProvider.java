package mx.bnext.qms.configuration.util;

import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import com.opensymphony.xwork2.config.Configuration;
import com.opensymphony.xwork2.config.ConfigurationException;
import com.opensymphony.xwork2.config.ConfigurationProvider;
import com.opensymphony.xwork2.inject.ContainerBuilder;
import com.opensymphony.xwork2.util.location.LocatableProperties;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.framework.file.BnextSpringConfiguration;
import qms.framework.file.FileManager;

public class BnextStrutsConfigurationProvider implements ConfigurationProvider {

    private static final Logger LOGGER = Loggable.getLogger(BnextStrutsConfigurationProvider.class);
    
    private static boolean NEEDS_RELOAD_STRUTS_CONFIGURATION = false;
    
    public static void reloadConfig() {
        if (BnextSpringConfiguration.getUploadFileMaxSizeBytes() > 0) {
            NEEDS_RELOAD_STRUTS_CONFIGURATION = true;
        }
    }
    
    /**
     * Called before removed from the configuration manager
     */
    @Override
    public void destroy() {
        LOGGER.trace("Destroy Struts 2 Customizer");
    }

    /**
     * Initializes with the configuration
     *
     * @param configuration The configuration
     * @throws ConfigurationException If anything goes wrong
     */
    @Override
    public void init(Configuration configuration) throws ConfigurationException {
        LOGGER.trace("Init Struts 2 Customizer");

    }

    /**
     * Tells whether the ContainerProvider should reload its configuration
     *
     * @return <tt>true</tt>, whether the ContainerProvider should reload its configuration, <tt>false</tt>otherwise.
     */
    @Override
    public boolean needsReload() {
        if (NEEDS_RELOAD_STRUTS_CONFIGURATION) {
            NEEDS_RELOAD_STRUTS_CONFIGURATION = false;
            return true;
        } else {
            return false;
        }
    }

    /**
     * Registers beans and properties for the Container
     *
     * @param builder The builder to register beans with
     * @param props The properties to register constants with
     * @throws ConfigurationException If anything goes wrong
     */
    @Override
    public void register(ContainerBuilder builder, LocatableProperties props) throws ConfigurationException {
        if (BnextSpringConfiguration.getUploadFileMaxSizeBytes() > 0) {
            props.setProperty("struts.multipart.maxSize", Long.toString(BnextSpringConfiguration.getUploadFileMaxSizeBytes()));
            props.setProperty("struts.multipart.maxFileSize", Long.toString(BnextSpringConfiguration.getUploadFileMaxSizeBytes()));
        } else {
            props.setProperty("struts.multipart.maxSize", String.valueOf(FileManager.DEFAULT_MAX_FILE_SIZE));
            props.setProperty("struts.multipart.maxFileSize", String.valueOf(FileManager.DEFAULT_MAX_FILE_SIZE));
        }
    }

    /**
     * Loads the packages for the configuration.
     *
     * @throws ConfigurationException in case of configuration errors
     */
    @Override
    public void loadPackages() throws ConfigurationException {
        LOGGER.trace("Init Struts 2 load packages");

    }

}
