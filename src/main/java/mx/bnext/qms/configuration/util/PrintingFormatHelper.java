package mx.bnext.qms.configuration.util;

import com.google.common.collect.ImmutableMap;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import mx.bnext.core.security.CryptoUtils;
import mx.bnext.qms.configuration.dto.PrintingParsedHtmlDTO;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.select.Elements;
import org.jsoup.select.Selector;
import qms.access.dto.ILoggedUser;
import qms.custom.dao.IPrintingFormatDAO;
import qms.custom.entity.PrintingFormat;
import qms.custom.entity.PrintingFormatOutstandingSurveys;
import qms.custom.util.FilePersistentUtil;
import qms.custom.util.PrintFormatVariableTypes;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.entity.AnswerPartType;
import qms.form.util.FixedField;
import qms.form.util.SurveyFieldAnswerType;
import qms.form.util.SurveyUtil;
import qms.framework.util.CacheRegion;
import qms.util.FormUtil;
import qms.util.QMSException;
import qms.util.dto.FormatedAnswerConfig;

/**
 * Se agrega helper para no hacer operaciones de lectura 
 * como parte de una solo transacción y tener bloqueda la conexión a la base de datos
 * Solo las operaciones de escritura se dejan en PrintingFormatDAO
 * <AUTHOR>
 */
public class PrintingFormatHelper extends FormatHelper {

    public PrintingFormatHelper(IPrintingFormatDAO dao) {
        super(dao);
    }

    public boolean needsRefreshRecord(final PrintingParsedHtmlDTO record) {
        return record.getParsedFileId() == null
                || record.getLastFillDate() == null
                || record.getParsedLastModifiedDate() == null
                || record.getParsedLastModifiedDate().before(record.getLastFillDate());
    }
      
    public Long refreshFormat(
            final PrintingParsedHtmlDTO record,
            final ILoggedUser loggedUser,
            final Boolean forceRefresh
    ) throws IOException, QMSException {
        if(this.loadGeneralInfo(record.getSurveyId(), record.getOutstandingSurveysId(), record.getDocumentId(), record.getPrintingFormatId(), loggedUser, forceRefresh)) {
            return refreshFormatWithData(this.getTitle(), (PrintingFormat)this.getFormat(), record, this.getLoggedUser(), this.getForceRefresh());
        };
        return null;
    }
    
    private Long refreshFormatWithData(
        final String title,
        final PrintingFormat format,
        final PrintingParsedHtmlDTO recordDto,
        final ILoggedUser loggedUser,
        final Boolean forceRefresh
    ) throws IOException, QMSException {
        final Map<String, SurveyDataFieldDTO> fieldTypes = new HashMap<>();
        final List<String> realFieldsList = this.getRealFieldsList(format.getFlexiFields(), format.getFixedFields(), format.getId(), recordDto.getSurveyId(), format.getMasterId(), fieldTypes);
        if (realFieldsList == null) {
            return null;
        }
        final Map<String, Object> answers = this.getAnswers(format.getMasterId(), realFieldsList, recordDto.getOutstandingSurveysId());
        // CurrentHash
        final PrintingFormatOutstandingSurveys loadedRecord = this.getDao().HQLT_findSimple(PrintingFormatOutstandingSurveys.class, " SELECT pf "
            + " FROM " + PrintingFormatOutstandingSurveys.class.getCanonicalName() + " pf "
            + " WHERE "
                + " pf.id.printingFormatId = :printingFormatId "
                + " AND pf.id.outstandingSurveysId = :outstandingSurveysId ",
            ImmutableMap.of(
                "printingFormatId", recordDto.getPrintingFormatId(),
                "outstandingSurveysId", recordDto.getOutstandingSurveysId()
            ), true, CacheRegion.PRINTING_FORMAT, 0
        );
        final String currentHash = CryptoUtils.sha512(StringUtils.substring(StringUtils.join(answers.values(), "-"), 1));
        // final String currentHash = new Date().toString();                            // <-- descomentar para pruebas
        // Si no han cambiado el hash no es necesario actualizar el HTML
        if (loadedRecord != null && loadedRecord.getFieldValuesHash() != null && Objects.equals(currentHash, loadedRecord.getFieldValuesHash()) && !forceRefresh) {
            return loadedRecord.getFileId();
        }
        // Si no existe o no coincide con la última, se refresca.
        // Se carga el HTML del template al archivo
        String str = FilePersistentUtil.writeDataToFile(format.getFileId(), loggedUser.getId());
        if (str != null) {
            format.setHtml(str);
        }
        // Se obtiene el HTML
        final org.jsoup.nodes.Document doc = Jsoup.parse(format.getHtml());
        // Reemplazar valores en el HTML
        answers.forEach((key, value) -> {
            if (value == null) {
                getLogger().trace("Skipped NULL value [{}:{}] at [formatId: {}, defaultHtmlId: {}]", key, value, format.getId(), format.getFileId());
                return;
            }
            try {
                if (FixedField.fromAlias(key) == null) {
                    // Valores de campos de SURVEY del formulario
                    final SurveyDataFieldDTO field = fieldTypes.get(key);
                    // Para vistas previas configuradas con items, se transforma.
                    String alternativeKey = null;
                    String alternativeValue = null;
                    if (field.getDisableColumnPerOption() && field.getName().contains(AnswerPartType.CATALOG_VALUE.getSuffix())) {
                        alternativeKey = field.getFieldCode() + "_" + value;
                        alternativeValue = "1";
                    }
                    final AnswerPartType partType = SurveyUtil.getAnswerPartType(
                            field.getName(), field.getFieldCode(), field.getCatalogSubType()
                    );
                    final SurveyFieldAnswerType answerType = SurveyUtil.getSurveyFieldAnswerType(field);
                    Elements e = doc.select("[data-id=\"" + PrintFormatVariableTypes.FLEXI + "_" + key + "\"]");
                    if (e.isEmpty()) {
                        key = alternativeKey;
                        value = alternativeValue;
                    }
                    FormatedAnswerConfig config = new FormatedAnswerConfig(
                            value,
                            field.getFieldType(),
                            field.getIncludeTime(),
                            "Seleccionado",
                            null,
                            answerType,
                            partType
                    );
                    String answer = FormUtil.getFormatedAnswer(config);
                    if (answer == null) {
                        answer = "";
                    }
                    doc.select("[data-id=\"" + PrintFormatVariableTypes.FLEXI + "_" + key + "\"]").empty().append(answer);
                } else {
                    // Valores de campos FIJOS del formulario
                    doc.select("[data-id=\"" + PrintFormatVariableTypes.FIXED + "_" + key + "\"]").empty().append(value.toString());
                }
            } catch(Selector.SelectorParseException e) {
                getLogger().error("Failed replacing [{}:{}] at [formatId: {}, defaultHtmlId: {}] - REASON: {}", key, value, format.getId(), format.getFileId(), e.getMessage());
            } catch(Exception e) {
                if (FixedField.fromAlias(key) == null) {
                    doc.select("[data-id=\"" + PrintFormatVariableTypes.FLEXI + "_" + key + "\"]").empty().append(value.toString());
                } else {
                    doc.select("[data-id=\"" + PrintFormatVariableTypes.FIXED + "_" + key + "\"]").empty().append(value.toString());
                }
                getLogger().warn("Invalid replacing format [{}:{}] at [formatId: {}, defaultHtmlId: {}] - REASON: {}", key, value, format.getId(), format.getFileId(), e.getMessage());
            }
        });
        // Se limpian todos los CHIPS sin valor
        doc.select(".dx-mention > span").empty().append("");
        // Se guarda el HTML en un archivo temporal
        return ((IPrintingFormatDAO)this.getDao()).saveFormatWithData(loadedRecord, recordDto.getPrintingFormatId(), recordDto.getOutstandingSurveysId(), title, doc, currentHash, loggedUser);
    }
    
}
