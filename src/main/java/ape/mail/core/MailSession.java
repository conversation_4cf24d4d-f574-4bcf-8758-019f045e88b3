package ape.mail.core;

import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import java.util.Objects;
import javax.mail.Session;
import javax.servlet.ServletContext;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.framework.security.AuthenticationProtocol;

/**
 *
 * <AUTHOR>
 */
public class MailSession {

    private static final Logger LOGGER = Loggable.getLogger(MailSession.class);
    /**
     * Setup and creates a new mail session
     *
     */
    public static Session getInstance(
            String smtpHost,
            String smtpPort,
            String smtpProtocol,
            Boolean smtpAuthentication,
            Integer smtpUserset,
            Integer smtpPrintDebugInfo
    ) {
        LOGGER.trace(
                "Creating new SMTP session for host: {}, port: {}, protocol: {}, authentication: {}",
                smtpHost,
                smtpPort,
                smtpProtocol,
                smtpAuthentication
        );

        java.util.Properties props = new java.util.Properties();
        /*
          https://javaee.github.io/javamail/docs/api//com/sun/mail/smtp/package-summary.html
          https://stackoverflow.com/questions/18970409/why-javamail-connection-timeout-is-too-long

          The SMTP protocol provider supports the following properties, which may be set in the
          JavaMail Session object. The properties are always set as strings; the
          Type column describes how the string is interpreted.
         */
        props.setProperty("mail.host", smtpHost);
        if (Objects.equals(smtpPrintDebugInfo, 1)) {
            props.setProperty("mail.debug", "true");
            props.setProperty("mail.debug.auth", "true");
        }
        if (Objects.equals(smtpUserset, 1)) {
            props.setProperty("mail.smtp.userset", "true");
        }
        props.setProperty("mail.smtp.host", smtpHost);
        props.setProperty("mail.smtp.port", smtpPort);
        props.setProperty("mail.smtp.connectiontimeout", "5000");
        if (Utilities.getSettings().getMailSenderAliveTime() != null) {
            long mailSenderAliveTime = Utilities.getSettings().getMailSenderAliveTime() * 1000;
            props.setProperty("mail.smtp.timeout", Long.toString(mailSenderAliveTime));
        }
        if (AuthenticationProtocol.TLS.getCode().equals(smtpProtocol)) {
            props.put("mail.smtp.starttls.enable", "true");
        }
        if (smtpAuthentication) {
            props.setProperty("mail.smtp.auth", "true");
        } else {
            props.setProperty("mail.smtp.auth", "false");
        }
        final Session instance = Session.getInstance(props);
        LOGGER.trace(
                "SMTP session created for host: {}, port: {}, protocol: {}, authentication: {}",
                smtpHost,
                smtpPort,
                smtpProtocol,
                smtpAuthentication
        );
        return instance;
    }
    
    public static Session getInstance(ServletContext servletContext) {
        Settings settings = Utilities.getSettings(servletContext);
        return getInstance(
                settings.getSmtpHost(),
                settings.getSmtpPort().toString(),
                settings.getSmtpProtocol(),
                settings.getSmtpAuthentication().equals(1),
                settings.getSmtpUserset(),
                settings.getSmtpPrintDebugInfo()
        );
    }
}
