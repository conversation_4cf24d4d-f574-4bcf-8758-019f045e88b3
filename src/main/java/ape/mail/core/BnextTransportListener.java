package ape.mail.core;

import com.sun.mail.smtp.SMTPTransport;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import javax.annotation.Nonnull;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.event.TransportEvent;
import javax.mail.event.TransportListener;
import javax.mail.internet.MimeMessage;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

public class BnextTransportListener implements TransportListener {

    private static final Logger LOGGER = Loggable.getLogger(BnextTransportListener.class);

    @Nonnull
    private String messageToPlainText(final Message message) {
        try {
            if (message == null) {
                return "";
            }
            if (message instanceof MimeMessage) {
                final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                message.writeTo(outputStream);
                final String plainText = outputStream.toString();
                return StringUtils.substring(plainText, 0, Math.min(plainText.length(), 1000));
            } else {
                final String plainText = message.toString();
                return StringUtils.substring(plainText, 0, Math.min(plainText.length(), 1000));
            }
        } catch (IOException | MessagingException e) {
            LOGGER.error("Error converting MimeMessage to String", e);
            final String plainText = message.toString();
            return StringUtils.substring(plainText, 0, Math.min(plainText.length(), 1000));
        }
    }

    @Nonnull
    private String typeName(final int type) {
        if (type == TransportEvent.MESSAGE_DELIVERED) {
            return "MESSAGE_DELIVERED";
        } else if (type == TransportEvent.MESSAGE_NOT_DELIVERED) {
            return "MESSAGE_NOT_DELIVERED";
        } else if (type == TransportEvent.MESSAGE_PARTIALLY_DELIVERED) {
            return "MESSAGE_PARTIALLY_DELIVERED";
        } else {
            return "UNKNOWN";
        }
    }

    @Override
    public void messageDelivered(final TransportEvent event) {
        if (LOGGER.isDebugEnabled()) {
            if (event.getSource() != null && event.getSource() instanceof SMTPTransport) {
                final SMTPTransport transport = (SMTPTransport) event.getSource();
                LOGGER.debug(
                        "Message delivered, type: \"{}\", source: \"{}\", host: \"{}\", user name: \"{}\", message: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        transport.getURLName().getHost(),
                        transport.getURLName().getUsername(),
                        messageToPlainText(event.getMessage())
                );
            } else {
                LOGGER.debug(
                        "Message delivered, type: \"{}\", source: \"{}\", message: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        messageToPlainText(event.getMessage())
                );
            }
        }
    }

    @Override
    public void messageNotDelivered(final TransportEvent event) {
        if (LOGGER.isErrorEnabled()) {
            if (event.getSource() != null && event.getSource() instanceof SMTPTransport) {
                final SMTPTransport transport = (SMTPTransport) event.getSource();
                LOGGER.error(
                        "Message not delivered, type: \"{}\", source: \"{}\", host: \"{}\", user name: \"{}\", message: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        transport.getURLName().getHost(),
                        transport.getURLName().getUsername(),
                        messageToPlainText(event.getMessage())
                );
            } else {
                LOGGER.error(
                        "Message not delivered, type: \"{}\", source: \"{}\", message: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        messageToPlainText(event.getMessage())
                );
            }
        }
    }

    @Override
    public void messagePartiallyDelivered(final TransportEvent event) {
        if (LOGGER.isErrorEnabled()) {
            if (event.getSource() != null && event.getSource() instanceof SMTPTransport) {
                final SMTPTransport transport = (SMTPTransport) event.getSource();
                LOGGER.error(
                        "Message partially delivered, type: \"{}\", source: \"{}\", host: \"{}\", user name: \"{}\", message: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        transport.getURLName().getHost(),
                        transport.getURLName().getUsername(),
                        messageToPlainText(event.getMessage())
                );
            } else {
                LOGGER.error(
                        "Message partially delivered, type: \"{}\", source: \"{}\", message: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        messageToPlainText(event.getMessage())
                );
            }
        }
    }
}
