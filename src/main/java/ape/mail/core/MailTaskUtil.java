package ape.mail.core;

import DPMS.DAOInterface.IFilesDAO;
import DPMS.Mapping.IAddressableUser;
import DPMS.Mapping.Settings;
import Framework.Config.Mail;
import Framework.Config.Utilities;
import ape.mail.dto.IMailContentBuilder;
import ape.mail.dto.IMailMesageBuilder;
import ape.mail.dto.IMailRecipientBuilder;
import ape.mail.dto.IMailSubjectBuilder;
import ape.mail.util.MailPriority;
import biweekly.Biweekly;
import biweekly.ICalendar;
import biweekly.component.VEvent;
import biweekly.property.Summary;
import bnext.licensing.LicenseUtil;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CodingErrorAction;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;
import javax.activation.DataHandler;
import javax.mail.Address;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.Message.RecipientType;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.Session;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import javax.mail.util.ByteArrayDataSource;
import mx.bnext.core.file.FileHandler;
import mx.bnext.core.util.Loggable;
import mx.bnext.licensing.License;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import qms.access.util.MailSettingsHandler;
import qms.framework.core.Mailer;
import qms.framework.dto.EventDTO;
import qms.framework.util.LocaleUtil;
import qms.framework.util.MailTokenManager;
import qms.framework.util.SettingsUtil;
import qms.util.ColorLib;
import qms.util.HTMLEncoding;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class MailTaskUtil {

    private static int dummyIdx = 0;
    private static final org.slf4j.Logger LOGGER = Loggable.getLogger(MailTaskUtil.class);

    public static String getSubject(String subject) {
        return subject != null ? subject.replace("{systemId}", Utilities.getSettings().getSystemId()) : subject;
    }

    /**
     * Function that changes special tags in mail body
     *
     * @param template
     * @param header
     * @param title
     * @param link
     * @param footer
     * @param recipientName
     * @param remittent
     * @param data data to append in the body
     * @return
     */
    public static String replaceData(
            String template, String header, String title, String link, String footer, String recipientName, Mail remittent, String data
    ) {
        if (link == null) {
            link = Utilities.EMPTY_STRING;
        }
        if (footer == null) {
            footer = Utilities.EMPTY_STRING;
        }
        StringBuilder templateBuilder = new StringBuilder(template.length() + 400);
        templateBuilder.append(template);
        final License licence = LicenseUtil.getCurrentLicense();
        final Settings settings = Utilities.getSettings();
        if (licence != null && licence.getLicenseType() != null) {
            switch (licence.getLicenseType()) {
                case 0: // Development
                    replaceAll(templateBuilder, "{enviromentMessage}", settings.getLicencetypeLeyendDevelopment());
                    break;
                case 1: // Demo
                    replaceAll(templateBuilder, "{enviromentMessage}", settings.getLicencetypeLeyendDemo());
                    break;
                case 2: // QA
                    replaceAll(templateBuilder, "{enviromentMessage}", settings.getLicencetypeLeyendQA());
                    break;
                case 3: // Production
                    replaceAll(templateBuilder, "{enviromentMessage}", settings.getLicencetypeLeyendProduction());
                    break;
                default:
                    replaceAll(templateBuilder, "{enviromentMessage}", Utilities.EMPTY_STRING);
                    break;
            }
        } else {
            replaceAll(templateBuilder, "{enviromentMessage}", Utilities.EMPTY_STRING);
        }
        replaceAll(templateBuilder, "{header}", header);
        replaceAll(templateBuilder, "{title}", title);
        replaceAll(templateBuilder, "{link}", link);
        replaceAll(templateBuilder, "{footer}", footer);
        replaceAll(templateBuilder, "{data}", data);
        replaceAll(templateBuilder, "{systemId}", settings.getSystemId());
        replaceAll(templateBuilder, "{sendDate}", Utilities.todayDateBy("dd/MM/yyyy HH:mm"));
        replaceAll(templateBuilder, "{recipientName}", recipientName);
        replaceAll(templateBuilder, "{remittentName}", remittent.getName());
        replaceAll(templateBuilder, "{remittentMail}", remittent.getEmail());
        return encodeSystemData(templateBuilder.toString());
    }

    public static void replaceAll(StringBuilder builder, String from, String to) {
        if (to == null) {
            to = "";
        }
        if (from == null) {
            return;
        }
        int index = builder.indexOf(from);
        while (index != -1) {
            builder.replace(index, index + from.length(), to);
            index += to.length(); // Move to the end of the replacement
            index = builder.indexOf(from, index);
        }
    }

    public static String buildFooter(String footer, Set<Mail> recipients, ResourceBundle tags) {
        if (recipients == null || recipients.isEmpty()) {
            return footer;
        }
        final Set<Mail> publicRecipients = recipients.stream()
                .filter((recipient) -> 
                        recipient != null && Objects.equals(recipient.getTipo(), RecipientType.TO) 
                        || recipient != null  && Objects.equals(recipient.getTipo(), RecipientType.CC)
                )
                .collect(Collectors.toSet());
        if (publicRecipients != null && !publicRecipients.isEmpty() && publicRecipients.size() > 1) {
            StringBuilder notes = new StringBuilder(255);
            if (footer != null && footer.trim().equalsIgnoreCase(LocaleUtil.getTag("footer", tags).trim())) {
                notes.append(LocaleUtil.getTag("footer.recipients", tags));
            } else if (footer != null) {
                notes.append(footer).append("<br>").append(LocaleUtil.getTag("footer.recipients.custom", tags));
            } else {
                notes.append("<br>").append(LocaleUtil.getTag("footer.recipients.custom", tags));
            }
            notes.append("&nbsp;<strong>");
            for (Iterator<Mail> iterator = publicRecipients.iterator(); iterator.hasNext();) {
                Mail rec = iterator.next();
                notes
                        .append(rec.getName())
                        .append(" &lt;")
                        .append(rec.getEmail())
                        .append("&gt;");
                if (iterator.hasNext()) {
                    notes.append(",&nbsp;");
                } else {
                    notes.append(".");
                }
            }
            notes.append("</strong>");
            footer = notes.toString();
        }
        return footer;
    }

    public static Message buildMessage(
            Session sessionMail,
            String subject, 
            IMailMesageBuilder config
    ) throws UnsupportedEncodingException, MessagingException {
        Mail remittent = config.getRemittent();
        if (remittent == null
                || remittent.getName() == null
                || remittent.getName().isEmpty()
                || remittent.getEmail() == null
                || remittent.getEmail().isEmpty()) {
            remittent = new Mail(Utilities.getSettings().getSystemId(), Utilities.getSettings().getAdminMail());
        }
        Message message = new MimeMessage(sessionMail);
        if (!addRecipients(message, config)) {
            return null;
        }
        /*Se coloca un asunto del correo*/
        setMessageSubject(subject, message, config);
        InternetAddress fromAddress = new InternetAddress(Utilities.getSettings().getAdminMail());
        fromAddress.setPersonal(remittent.getName(), "UTF-8");
        message.setFrom(fromAddress);
        InternetAddress replyTo = new InternetAddress(remittent.getEmail());
        replyTo.setPersonal(remittent.getName(), "UTF-8");
        message.setReplyTo(new Address[]{replyTo});
        if (config.getPriority() != null && !MailPriority.NONE.equals(config.getPriority())) {
            message.addHeader("X-Priority", config.getPriority().getValue().toString());
        }
        message.setSentDate(new Date());
        return message;
    }

    private static synchronized boolean addRecipients(Message message, IMailRecipientBuilder config)
            throws AddressException, UnsupportedEncodingException, MessagingException {
        if (config.isInvidualSend()) {
            // Se enviaran individualmente cada correo por separado
            Mail recipient = config.getRecipient();
            if (recipient == null || recipient.getEmail() == null || recipient.getEmail().isEmpty()) {
                return false;
            }
            /*Se agregan los destinarios del correo*/
            if (recipient.getName() == null) {
                recipient.setName(recipient.getEmail());
            }
            InternetAddress recipientAddress;
            switch (Utilities.getSettings().getLicenceType()) {
                case 0: // Development
                case 2: // QA
                    recipientAddress = new InternetAddress(
                            getDummyMail()
                    );
                    break;
                default:
                    recipientAddress = new InternetAddress(recipient.getEmail());
            }
            recipientAddress.setPersonal(recipient.getName(), "UTF-8");
            message.addRecipient(recipient.getTipo(), recipientAddress);
        } else {
            // Se enviará solo 1 correo para todos los destinatarios
            for (Mail r : config.getRecipients()) {
                InternetAddress a;
                switch (Utilities.getSettings().getLicenceType()) {
                    case 0: // Development
                    case 2: // QA
                        a = new InternetAddress(
                                getDummyMail()
                        );
                        break;
                    default:
                        a = new InternetAddress(r.getEmail());
                }
                a.setPersonal(r.getName(), "UTF-8");
                message.addRecipient(
                        r.getTipo(),
                        a
                );
            }
        }
        return true;
    }

    private static synchronized String getDummyMail() {
        String dummyMail = Utilities.getSettings().getDummyMail();
        if (dummyMail.contains(";")) {
            String[] dummyMails = dummyMail.split(";");
            if (dummyIdx >= dummyMails.length) {
                dummyIdx = 0;
            }
            for (int i = 0; i < dummyMails.length; i++) {
                if (dummyMails[i].isEmpty()) {
                    dummyIdx++;
                    continue;
                }
                if (dummyIdx == i) {
                    dummyIdx++;
                    return dummyMails[i];
                }
            }
            return dummyMails[0];
        } else {
            return dummyMail;
        }
    }

    /**
     * Changes system variables in determined input
     *
     * @param input String with system variables
     * @return input with system variables changed
     */
    public static String encodeSystemData(String input) {
        String systemColor = Utilities.getSettings().getSystemColor();
        String fontColor = ColorLib.getContrastColor(systemColor);
        String systemId = Utilities.getSettings().getSystemId();
        input = input
                .replace("{systemColor}", systemColor)
                .replace("{systemColorContrast}", fontColor)
                .replace("{systemId}", systemId);
        return input;
    }

    public static void setMessageSubject(String localSubject, Message message, IMailSubjectBuilder config) throws MessagingException, UnsupportedEncodingException {
        StringBuilder subject = new StringBuilder(100);
        if (config.isCustomSubject()) {
            subject.append(config.getSubject());
        } else {
            /*Se coloca un asunto del correo*/
            String globalSubject = Utilities.getSettings().getMailSubject(),
                    particularSubject = localSubject == null || localSubject.isEmpty() ? null : localSubject;
            if (globalSubject == null || globalSubject.isEmpty()) {
                globalSubject = config.getDefaultMailSubject() + " BnextQMS";
            }
            if (config.getType() != null) {
                subject.append(config.getTypeString());
            }
            subject.append(" / ");
            subject.append(globalSubject);
            subject.append(": ");
            subject.append(particularSubject);
        }

        message.setSubject(
                MimeUtility.encodeText(subject.toString(), "UTF-8", "Q")
        );
    }

    /**
     * Function that encrypt mail link for the user
     *
     * @param template
     * @param conf
     * @param user
     * @param tokenManager
     * @return
     */
    public static String encodeMailURL(
            final String template,
            final IMailContentBuilder conf,
            final IAddressableUser user,
            final MailTokenManager tokenManager
    ) {
        String url = tokenManager.generateUrl(user, conf.getModuleUrl());
        return template.replace("{url}", url);
    }

    public static BodyPart getMeetingPart(EventDTO event) {
        final SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd'T'HHmm'00'");
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            String mail = Utilities.getSettings().getAdminMail();
            BodyPart bodyPart = new MimeBodyPart();
            String bodyHtml = event.getBodyHtml().replaceAll("[\n\r]", "\\\\n");
            String bodyPlainText = event.getBodyPlainText()
                    .replaceAll("[\n\r]", "\\\\n");
            StringBuilder buffer = new StringBuilder(300);
            buffer
                    .append("BEGIN:VCALENDAR\n")
                    .append("METHOD:").append(event.getMethod()).append("\n")
                    .append("PRODID: BCP - Meeting\n")
                    .append("VERSION:2.0\n")
                    .append("BEGIN:VEVENT\n")
                    .append("DTSTAMP:").append(formatter.format(new Date())).append("Z\n")
                    .append("DTSTART:").append(formatter.format(event.getStart())).append("Z\n")
                    .append("DTEND:").append(formatter.format(event.getEnd())).append("Z\n")
                    .append("SUMMARY:").append(event.getSubject()).append("\n")
                    .append("UID:").append(event.getId()).append("\n")
                    .append("ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:").append(mail).append("\n")
                    .append("LOCATION:").append(event.getLocation()).append("\n")
                    .append("X-ALT-DESC;FMTTYPE=text/html:").append(bodyHtml).append("\n")
                    .append("DESCRIPTION:").append(bodyPlainText).append("\n")
                    .append("SEQUENCE:").append(event.getSequence()).append("\n")
                    .append("PRIORITY:5\n")
                    .append("CLASS:PUBLIC\n")
                    .append("STATUS:").append(event.getStatus()).append("\n")
                    .append("TRANSP:OPAQUE\n")
                    .append("BEGIN:VALARM\n")
                    .append("ACTION:DISPLAY\n")
                    .append("DESCRIPTION:REMINDER\n")
                    .append("TRIGGER;RELATED=START:-PT00H15M00S\n")
                    .append("END:VALARM\n")
                    .append("END:VEVENT\n")
                    .append("END:VCALENDAR");
            bodyPart.addHeader("Content-Class", "urn:content-classes:calendarmessage");
            bodyPart.setContent(encodeUTF8(buffer.toString()), "text/calendar;charset=UTF-8;method=REQUEST");

            return bodyPart;
        } catch (MessagingException ex) {
            Loggable.getLogger(Mailer.class).error("error while creating meeting request", ex);
        }
        return null;
    }

    public static void attachCalendar(Multipart bodyParts, String title, String localSubject, Date startDate, Date endDate) throws MessagingException {
        if (startDate == null || endDate == null) {
            return;
        }
        ICalendar ical = new ICalendar();
        VEvent event = new VEvent();
        Summary summary;
        if (localSubject != null && !localSubject.isEmpty()) {
            summary = event.setSummary(localSubject);
        } else if (title != null && !title.isEmpty()) {
            summary = event.setSummary(title);
        } else {
            summary = event.setSummary("Evento - BnextQMS");
        }
        summary.setLanguage(Utilities.getLocaleString());

        event.setDateStart(startDate);
        event.setDateEnd(endDate);

        ical.addEvent(event);
        String str = Biweekly.write(ical).go();

        MimeBodyPart calendarBodyPart = new MimeBodyPart();
        DataHandler dh = new DataHandler(new ByteArrayDataSource(
                str.getBytes(), "text/calendar"
        ));
        calendarBodyPart.setDataHandler(dh);
        calendarBodyPart.setFileName("invite.ics");
        bodyParts.addBodyPart(calendarBodyPart);
    }

    public static void attachFileContent(Multipart bodyParts, Set<Long> attachmentFileIds) {
        if (attachmentFileIds != null && !attachmentFileIds.isEmpty()) {
            try {
                Utilities.getContextProvidedBean(IFilesDAO.class).attachFileContent(bodyParts, attachmentFileIds);
            } catch (Exception e) {
                String content;
                try {
                    content = bodyParts.getBodyPart(0).getContent().toString();
                } catch (IOException | MessagingException be) {
                    content = "Unable to get content... reason " + be.getMessage();
                }
                LOGGER.error("A mail was sent without {} attachements (IDS: {})! cause was {}. Content:{\r\n{}\r\n}", new Object[]{
                    attachmentFileIds.size(), attachmentFileIds.toString(), e.getMessage(), content
                });
            }
        }
    }

    public static boolean attachFiles(Multipart bodyParts, Set<File> attachmentFiles) {
        if (attachmentFiles != null && !attachmentFiles.isEmpty()) {
            try {
                if (attachmentFiles.stream().allMatch((f) -> f != null)) {
                    FileHandler.attachFiles(bodyParts, attachmentFiles);
                } else {
                    LOGGER.error("Se intetaron attachar archivos nulos, {}", StringUtils.substring(getBodyPartContent(bodyParts), 0, 100) + "...");
                    return false;
                }
            } catch (Exception e) {
                LOGGER.error("A mail was sent without {} attachements (IDS: {})! cause was {}. Content:{\r\n{}\r\n}", new Object[]{
                    attachmentFiles.size(), attachmentFiles.toString(), e.getMessage(), StringUtils.substring(getBodyPartContent(bodyParts), 0, 100) + "..."
                });
                return false;
            }
        }
        return true;
    }

    private static String getBodyPartContent(Multipart bodyParts) {
        try {
            return bodyParts.getBodyPart(0).getContent().toString();
        } catch (IOException | MessagingException be) {
            return "Unable to get content... reason " + be.getMessage() + " {" + bodyParts + "}";
        }
    }

    public static boolean isAppendLinkActivated(Mail recipient) {
        return recipient.isRecipientAvailable();
    }

    public static String encodeUTF8(String str) {
        try {
            byte[] gbBytes;
            CharsetEncoder enc = Charset.forName("UTF-8").newEncoder();
            enc.onMalformedInput(CodingErrorAction.REPORT);
            enc.onUnmappableCharacter(CodingErrorAction.REPLACE);
            gbBytes = enc.encode(CharBuffer.wrap(str)).array();
            return new String(gbBytes).trim();
        } catch (Exception ex) {
            return "Error: encodeUTF() - UTF-8 - Exception: " + ex.getMessage() + " - " + ex;
        }
    }

    public static boolean isMailingAvailableByType(Mailer.TYPE type) {
        if (Utilities.isMailSendingOn()) {
            return true;
        }

        return Mailer.TYPE.LICENSE.equals(type) || Mailer.TYPE.SUPPORT.equals(type);
    }

    private static String getValidName(Mail mail) {
        if (mail.getName() == null || mail.getName().trim().isEmpty() || mail.getName().contains("@")) {
            return WordUtils.capitalizeFully(mail.getEmail().replaceAll("@.+", "").replaceAll("[^a-zA-Z]", "@").trim().replaceAll("@.*", ""));
        }
        return mail.getName();
    }

    private static String getRecipientName(IMailContentBuilder config) {
        if (config.isInvidualSend()) {
            return getValidName(config.getRecipient());
        }
        if (config.getRecipients().stream().noneMatch((addres) -> (addres.getTipo() == Message.RecipientType.TO))) {
            return "";
        }
        List<Mail> to = config.getRecipients().stream().filter((mail) -> {
            return mail.getTipo() == Message.RecipientType.TO;
        }).collect(Collectors.toList());
        switch (to.size()) {
            case 1:
                return getValidName(to.get(0));
            case 2:
                return getValidName(to.get(0)) + ", " + getValidName(to.get(1));
            default:
                return "a todos";
        }
    }

    public static String buildMail(final IMailContentBuilder config, final MailTokenManager tokenManager) {
        String template;
        String content = config.getContent();
        if (config.isLegacy()) {
            template = config.getContent();
            content = "";
        } else {
            template = Mailer.getTemplate(Mailer.class.getCanonicalName());
            if (template == null || template.isEmpty()) {
                return null;
            }
        }
        try {
            String recipientName = getRecipientName(config);
            template = HTMLEncoding.escapeAccentsHTML(
                    replaceData(
                            template,
                            config.getHeader(),
                            config.getTitle(),
                            config.getLink(),
                            config.getFooter(),
                            recipientName,
                            config.getRemittent(),
                            content
                    )
            );
            if (config.isInvidualSend() && isAppendLinkActivated(config.getRecipient())) {
                template = encodeMailURL(template, config, config.getRecipient().getRecipient(), tokenManager);
            } else {
                String siteUrl = SettingsUtil.getAppUrl();
                template = template.replace("{url}", siteUrl + MailTokenManager.MAIL_SESSION_URL);
            }
            template = template.replace("{systemName}", Utilities.getSettings().getSystemId());
            return template;
        } catch (NumberFormatException ex) {
            LOGGER.error("Unable to create mail template.", ex);
            return null;
        }
    }

    public static boolean isMailingAvailableByUser(MailTaskConfiguration mailConfig) {
        if (!MailSettingsHandler.isMailingAvailableByUser(mailConfig)) {
            return false;
        }
        if (mailConfig.getRecipients() == null || mailConfig.getRecipients().isEmpty()) {
            return true;
        }
        final Set<Mail> enabledMailRecipients =  MailSettingsHandler.filterMailingAvailableByUser(mailConfig);
        if (enabledMailRecipients == null || enabledMailRecipients.isEmpty()) {
            return false;
        } else {
            mailConfig.setRecipients(enabledMailRecipients);
            return true;
        }
    }

    private MailTaskUtil() {
    }

}
