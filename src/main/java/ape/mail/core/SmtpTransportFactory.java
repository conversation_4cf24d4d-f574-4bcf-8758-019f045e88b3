package ape.mail.core;

import ape.mail.dto.BnextSmtpTransport;
import javax.mail.MessagingException;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import qms.framework.util.ExceptionUtils;

public class SmtpTransportFactory extends BasePooledObjectFactory<BnextSmtpTransport> {

    @Override
    public BnextSmtpTransport create() {
        try {
            final BnextSmtpTransport transport = SmtpTransport.createTransport();
            if (transport == null) {
                throw new RuntimeException("Can not create transport");
            }
            return transport;
        } catch (MessagingException e) {
            final String error = ExceptionUtils.getRootCauseMessage(e);
            throw new RuntimeException("Can not create transport " + error, e);
        }
    }
    
    @Override
    public PooledObject<BnextSmtpTransport> wrap(BnextSmtpTransport transport) {
        return new DefaultPooledObject<>(transport);
    }

    @Override
    public void destroyObject(PooledObject<BnextSmtpTransport> pooledObject) {
        final BnextSmtpTransport transport = pooledObject.getObject();
        if (transport == null) {
            return;
        }
        SmtpTransport.closeTransport(transport.getSmtpTransport());
    }
    
}
