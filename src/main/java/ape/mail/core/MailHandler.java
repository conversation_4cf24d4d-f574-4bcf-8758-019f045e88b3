package ape.mail.core;

import Framework.Config.Utilities;
import com.sun.mail.smtp.SMTPTransport;
import java.io.UnsupportedEncodingException;
import java.util.ResourceBundle;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.framework.core.Mailer;
import qms.framework.util.LocaleUtil;
import qms.framework.util.SettingsUtil;
import qms.util.ColorLib;
import qms.util.HTMLEncoding;

public class MailHandler {

    private static final Logger LOGGER = Loggable.getLogger(MailHandler.class);

    private static final ResourceBundle tags = LocaleUtil.getI18n("isoblock.common.languageBeans", LocaleUtil.getLocale());

    /**
     * Metodo que recibe como parametros una session de correo, el destinatario y el remitente
     * se encarga de crear el mensaje que mostrará un header, body un footer y una liga ne el correo de prueba.
     *
     * @param session sesscion iniciada con las propiedades para la conexion con el servido de correo
     * @param from    remitente
     * @param to      destinatario
     * @return regresa un mensaje que es el cuerpo del correo de prueba que se enviará
     * @throws UnsupportedEncodingException
     * <AUTHOR> Arce
     * @since ********
     */
    private MimeMessage getMessage(Session session, String from, String to) throws UnsupportedEncodingException {
        try {
            ResourceBundle mailTags = LocaleUtil.getSystemI18n(Mailer.class.getCanonicalName());
            String mensaje = tags.getString("isoblock.correo.Prueba.notificacion");
            String titulo = tags.getString("isoblock.correo.Configuracion.notificacion");
            String systemColor = Utilities.getSettings().getSystemColor();
            String admin = tags.getString("isoblock.configuracion.unidadorganizacional.comboRolAdministracion.Administrador");
            String fontColor = ColorLib.getContrastColor(systemColor);
            final String url = SettingsUtil.getAppUrl();
            mensaje = Framework.Config.Utilities.MailBuilder(
                            titulo, mensaje, Framework.Config.Utilities.EMPTY_LIST
                    )
                    .replace("{link}", mailTags.getString("link").replace("{url}", url))
                    .replace("{systemName}", Framework.Config.Utilities.getSettings().getSystemId())
                    .replace("{footer}", mailTags.getString("footer"))
                    .replace("{systemColor}", systemColor)
                    .replace("{systemColorContrast}", fontColor)
                    .replace("{systemId}", Framework.Config.Utilities.getSettings().getSystemId())
                    .replace("{recipientName}", to)
                    .replace("{systemName}", admin)

            ;
            MimeMessage msg = new MimeMessage(session);
            msg.setContent(MailTaskUtil.encodeUTF8(HTMLEncoding.escapeAccentsHTML(mensaje)), "text/plain");
            msg.setHeader("Content-Type", "text/html; charset=UTF-8");
            msg.saveChanges();
            msg.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
            msg.setSubject((tags.getString("testingEmail")));
            msg.setFrom(new InternetAddress(from, "Administrador"));
            return msg;
        } catch (MessagingException ex) {
            LOGGER.error("Error al crear mensaje de prueba", ex);
            return null;
        }
    }

    /**
     *
     * Metodo para enviar correo de Prueba en la configuracion del sistema
     * Recibe como parametros los valores de los campos de texto en la seccion
     * configuracion/Sistemas/Preferencias/Configuración avanzada de correos,
     * para construir los propiedades para el envio del correo.
     * @param host servidor smtp ingresado por el usuario
     * @param port puerto smtp ingresado por el usuario
     * @param recipient es el destinatario al cual se le enviará el correo de prueba y es ingresado por el usuario
     * @param user es el remitente el cual envia el correo de prueba y es ingresado por el usuario
     * @param pass en la contraseña del correo remitente y es inhresado por el usuario
     * @param protocol protocolo
     * @param auth si se usa autenticación en el envío de correo.
     * @return regresa un true si el envio del correo tuvo exito de lo contrario regresa un false
     * @since ********
     * <AUTHOR> Arce
     */
    public boolean sendTestMail(
            String user,
            String pass,
            String recipient,
            String host,
            String port,
            String protocol,
            Boolean auth,
            Integer smtpUserset,
            Integer smtpPrintDebugInfo
    ) {
        SMTPTransport transport = null;
        try {
            LOGGER.debug("sendTestMail {}", auth);
            Session session = MailSession.getInstance(
                    host,
                    port,
                    protocol,
                    auth,
                    smtpUserset,
                    smtpPrintDebugInfo
            );
            transport = (SMTPTransport) session.getTransport("smtp");
            if (transport == null) {
                LOGGER.error("Error al crear transporte de prueba");
                return false;
            }
            transport.addConnectionListener(new BnextConnectionListener());
            transport.addTransportListener(new BnextTransportListener());
            if (!transport.isConnected()) {
                if (auth) {
                    transport.connect(host, user, pass);
                } else {
                    transport.connect();
                }
            }
            if (!transport.isConnected()) {
                LOGGER.error("Error al conectar transporte de prueba");
                return false;
            }
            MimeMessage msj = getMessage(session, user, recipient);
            if (msj == null) {
                LOGGER.error("Error al crear mensaje de prueba");
                return false;
            }
            // Parche para utilizar javax.mail del JAR en lugar del JDK
            Thread.currentThread().setContextClassLoader(javax.mail.Message.class.getClassLoader());
            if (auth) {
                Transport.send(msj, user, pass);
            } else {
                Transport.send(msj);
            }
        } catch (UnsupportedEncodingException | MessagingException ex) {
            LOGGER.error(
                    "user: '{}', pass: '{}', recipient: '{}', host: '{}', port: '{}', protocol: '{}', auth: '{}'",
                    user, "*******", recipient, host, port, protocol, auth, ex
            );
            return false;
        } catch (Exception ex) {
            LOGGER.error("Error al enviar correo de prueba", ex);
            return false;
        } finally {
            if (transport != null) {
                SmtpTransport.closeTransport(transport);
            }
        }
        return true;
    }
}
