package ape.mail.core;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.IAddressableUser;
import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.mail.dto.EscalationTags;
import ape.mail.dto.ExtraMailConfigDTO;
import ape.mail.dto.ExtraMailUserDTO;
import ape.mail.dto.MailColumn;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.core.IPendingOperation;
import ape.pending.core.PendingMailerMode;
import ape.pending.core.PendingMailerType;
import ape.pending.util.SqlQueryParser;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.mail.Message;
import mx.bnext.access.Module;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import qms.access.dto.AddressableUserDTO;
import qms.access.dto.ILoggedUser;
import qms.escalation.core.IDailyMailExtraFields;
import qms.escalation.dto.EscalableDTO;
import qms.escalation.dto.IEscalableDTO;
import qms.framework.core.Mailer;
import qms.framework.dto.IMailable;
import qms.framework.mail.MailDTO;
import qms.framework.rest.SecurityRootUtils;
import qms.util.Helper;
import qms.util.MailUtil;


/**
 *
 * <AUTHOR>
 */
public class MailHelper extends Helper implements IMailHelper {

    public static final String[] DAILY_MAILS_FIXED_MAIL_URL_KEYS = new String[] {
            "${entityId}",
            "${recordId}",
            "${recipientId}",
            "${entityCode}",
            "${subEntityCode}"
    };
    public static final Pattern REMOVE_LINE_PATTERN = Pattern.compile(".+?\\[removeLineForMailAction=\".*?remove.*?\"].+");
    private static final String RECORD_HEADER_MATCH = "(<record-header>[\\s\\n\\r]*)(.*?)([\\s\\n\\r]*</record-header>)";
    private static final Pattern RECORD_HEADER_PATTERN = Pattern.compile(RECORD_HEADER_MATCH, Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
    private static final String RECORD_CELL_MATCH = "(<record-cell>[\\s\\n\\r]*)(.*?)([\\s\\n\\r]*</record-cell>)";
    private static final Pattern RECORD_CELL_PATTERN = Pattern.compile(RECORD_CELL_MATCH, Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
    public static final Pattern PARAM_PATTERN_NAME_FIELD = Pattern.compile("\\$\\{field}");
    public static final String TABLE_WITH_BORDER_TEMPLATE = "<table style="
            + "'"
            + "margin-left: 24px;"
            + "font-size: 12px;"
            + "border-color: #DDDDDD;"
            + "border-width: 1px;"
            + "border-style: solid;"
            + "border-spacing: 0px;"
            + "border-collapse: collapse;"
            + "text-indent: 12px;"
            + "font-family: Helvetica, Arial, Verdana, Trebuchet MS, sans-serif;"
            + "' border='1'"
            ;

    private static final Map<ApeOperationType, Pattern> operationTypePatterns = ImmutableMap.of(
            ApeOperationType.SOFT, Pattern.compile(".+?\\[removeLineForPendingOperation=\".*?" + ApeOperationType.SOFT.name().toLowerCase() + ".*?\"].+", Pattern.MULTILINE)
            ,ApeOperationType.STRONG, Pattern.compile(".+?\\[removeLineForPendingOperation=\".*?" + ApeOperationType.STRONG.name().toLowerCase() + ".*?\"].+", Pattern.MULTILINE)
            ,ApeOperationType.WEAK, Pattern.compile(".+?\\[removeLineForPendingOperation=\".*?" + ApeOperationType.WEAK.name().toLowerCase() + ".*?\"].+", Pattern.MULTILINE)
    );

    private static final Map<PendingMailerType, Pattern> mailTypePatterns = ImmutableMap.of(
            PendingMailerType.DEADLINE, Pattern.compile(".+?\\[removeLineForMailTypeAction=\".*?" + PendingMailerType.DEADLINE.name().toLowerCase() + ".*?\"].+", Pattern.MULTILINE)
            ,PendingMailerType.NOTICE, Pattern.compile(".+?\\[removeLineForMailTypeAction=\".*?" + PendingMailerType.NOTICE.name().toLowerCase() + ".*?\"].+", Pattern.MULTILINE)
            ,PendingMailerType.REMINDER, Pattern.compile(".+?\\[removeLineForMailTypeAction=\".*?" + PendingMailerType.REMINDER.name().toLowerCase() + ".*?\"].+", Pattern.MULTILINE)
            ,PendingMailerType.EXPIRED, Pattern.compile(".+?\\[removeLineForMailTypeAction=\".*?" + PendingMailerType.EXPIRED.name().toLowerCase() + ".*?\"].+", Pattern.MULTILINE)
    );

    private final SimpleDateFormat formatter;

    public MailHelper(IUntypedDAO dao) {
        super(dao);
        formatter = new SimpleDateFormat("dd/MM/yyyy");
    }

    /**
     * Converts a list of UserRef to a list of Mail
     *
     */
    @Override
    public Set<Mail> toMailSet(final Set<? extends IMailable> users) {
        final Set<Mail> mails = new HashSet<>(users.size() + 1);
        users.forEach(user -> mails.add(new Mail(user)));
        return mails;
    }

    public Set<Mail> toMailSet(final Set<? extends IMailable> users, final Message.RecipientType type) {
        final Set<Mail> mails = new HashSet<>(users.size() + 1);
        users.forEach(user -> mails.add(new Mail(user, type)));
        return mails;
    }

    public Set<Mail> toMailSet(IMailable ... user) {
        Set<Mail> mails = new HashSet<>(1);
        for (IMailable u : user) {
            mails.add(new Mail(u));
        }
        return mails;
    }

    public Set<Mail> toMail(List<? extends IMailable> users) {
        return toMailSet(new HashSet<>(users));
    }

    public Mail toMail(IMailable user) {
        return toMail(user, Message.RecipientType.TO);
    }

    public Mail toMail(IMailable user, Message.RecipientType type) {
        if (user == null || user.getCorreo() == null) {
            return null;
        }
        return new Mail(user);
    }

    /**
     * return owner of a finding
     *
     * @param user user to get the mail
     * @return Mail of owner
     */
    @Override
    public Mail toMail(IAddressableUser user) {
        if (user == null) {
            return null;
        }
        return new Mail(user);
    }

    public Mail toMail(String mail){
        return new Mail("", mail);
    }

    /**
     * Removes a determined mail from list
     *
     * @param except mail to be removed
     * @param source from where we want to remove it
     * @return List debugged
     */
    @Override
    public Set<Mail> except(Mail except, Set<Mail> source) {
        source.remove(except);
        return source;
    }

    /**
     * Removes mails from list
     *
     * @param excepts mails to be removed
     * @param source from where we want to remove it
     * @return List debugged
     */
    @Override
    public Set<Mail> except(Set<Mail> excepts, Set<Mail> source) {
        for (Mail except : excepts) {
            source.remove(except);
        }
        return source;
    }

    /**
     * Removes mails from list
     *
     * @param except mail to be removed
     * @param source from where we want to remove it
     * @return Mail debugged
     */
    @Override
    public Mail except(Mail except, Mail source) {
        if (except.equals(source)) {
            return null;
        }
        return source;
    }

    /**
     * Removes mails from list
     *
     * @param excepts mails to be removed
     * @param source from where we want to remove it
     * @return List debugged
     */
    @Override
    public Mail except(Set<Mail> excepts, Mail source) {
        if (excepts.contains(source)) {
            return null;
        }
        return source;
    }

    public IEscalableDTO getEscalableRecord(
            final Object[] temp,
            final LinkedHashMap<String, MailColumn> extraColumns,
            final ResourceBundle tags,
            final IPendingOperation pendingOperation,
            final ILoggedUser loggedUser
    ) {
        final EscalableDTO dto = new EscalableDTO();
        dto.setSubEntityCode((String) temp[2]);
        dto.setSubEntityDescription((String) temp[3]);
        dto.setEntityCode((String) temp[0]);
        dto.setEntityDescription((String) temp[1]);
        dto.setEntityTypeName((String) temp[4]);
        dto.setEntityDepartmentName((String) temp[5]);
        dto.setEntityBusinessUnitName((String) temp[6]);
        dto.setEntitySourceCode((String) temp[7]);
        dto.setEntitySourceName((String)temp[8]);
        dto.setCommitmentDate(Utilities.formatDate((Date) temp[9]));
        dto.setUnattendedDays(temp[10].toString());
        dto.setRecordId(((Number) temp[11]).longValue());
        dto.setRecipientId(((Number) temp[12]).longValue());
        dto.setRecipientMail((String) temp[13]);
        dto.setRecipientName((String) temp[14]);
        dto.setRecipientCode((String) temp[15]);
        dto.setRecipientLogin((String) temp[16]);
        dto.setRecipientVersion(((Number) temp[17]).intValue());
        final Date deadline = (Date) temp[18];
        dto.setEntityId(((Number) temp[19]).longValue());
        if (deadline != null) {
            dto.setDeadline(Utilities.formatDate(deadline));
        }
        final Integer initialIndex = 20;
        final List<String> extraMails = pendingOperation.getExtraMails();
        final int extraLength = extraMails.size();
        if (!extraMails.isEmpty()) {
            dto.setExtraMails(new HashSet<>(extraLength));
            for (int i = 0, idx = initialIndex; i < extraLength; i++) {
                final ExtraMailUserDTO user = new ExtraMailUserDTO();
                user.setId(((Number) temp[idx++]).longValue());
                user.setMail((String) temp[idx++]);
                user.setName((String) temp[idx++]);
                user.setField(extraMails.get(i));
                dto.getExtraMails().add(user);
            }
        } else {
            dto.setExtraMails(Utilities.EMPTY_SET);
        }

        if (extraColumns.isEmpty()) {
            return dto;
        }

        final LinkedHashMap<String, String> extraRows = new LinkedHashMap<>();
        addExtraColumns(initialIndex, extraLength, extraColumns, temp, dto, tags, extraRows);
        dto.setExtraFields(extraRows);
        return dto;
    }

    public void addExtraColumns(
            Integer initialIndex,
            Integer extraLength,
            LinkedHashMap<String, MailColumn> extraColumns,
            Object[] temp,
            IEscalableDTO dto,
            ResourceBundle tags,
            LinkedHashMap<String, String> extraRows
    ) {
        int columnsIndex = initialIndex + (extraLength * 3);
        final Set<Map.Entry<String, MailColumn>> entrySet = extraColumns.entrySet();
        for (final Map.Entry<String, MailColumn> entry : entrySet) {
            final String key = entry.getKey();
            final MailColumn column = entry.getValue();
            if (column.isQuery()) {
                addQueryRow(column, dto, extraRows);
            } else if (column.isLabel()) {
                addLabelRow(tags, key, temp[columnsIndex], extraRows);
                columnsIndex++;
            } else if (null != temp[columnsIndex] && temp[columnsIndex] instanceof Date) {
                addDateRow(temp[columnsIndex], key, extraRows);
                columnsIndex++;
            } else {
                addStringRow(temp[columnsIndex], key, extraRows);
                columnsIndex++;
            }
        }
    }

    private void addStringRow(
            final Object value, final String key, final LinkedHashMap<String, String> extraRows) {
        final String rowValue = value != null ? value.toString() : Utilities.EMPTY_STRING;
        extraRows.put(key, rowValue);
    }

    private void addDateRow(
            final Object value,
            final String key,
            final LinkedHashMap<String, String> extraRows) {
        final String rowValue = formatter.format(value);
        extraRows.put(key, rowValue);
    }

    private void addLabelRow(
            final ResourceBundle tags,
            final String key,
            final Object value,
            final LinkedHashMap<String, String> extraRows) {
        final String rowValue = getTag(tags, "label." + key + "_" + value.toString());
        extraRows.put(key, rowValue);
    }

    private void addQueryRow(
            final MailColumn column,
            final IEscalableDTO dto,
            final LinkedHashMap<String, String> extraRows
    ) {
        final String rowValue = dao.HQL_findSimpleString(column.getQueryString(), "id", dto.getRecordId());
        final String rowKey = column.getLabelName();
        extraRows.put(rowKey, rowValue);
    }

    private ExtraMailConfigDTO getExtraMailConfig(final IPendingOperation pendingOperation) {
        final ExtraMailConfigDTO extraMails = new ExtraMailConfigDTO();
        if (pendingOperation.getExtraMails().isEmpty()) {
            return extraMails;
        }
        final StringBuilder fieldsSb = new StringBuilder(50);
        final StringBuilder joinSb = new StringBuilder(50);
        Integer aliasCounter = 0;
        for (final String extraMail : pendingOperation.getExtraMails()) {
            final String field = " entity_" + extraMail;
            fieldsSb
                    .append(", owr").append(aliasCounter).append(".user_id as ").append(field).append("Id")
                    .append(", owr").append(aliasCounter).append(".mail as ").append(field).append("Mail")
                    .append(", owr").append(aliasCounter).append(".first_name as ").append(field).append("Name ");
            joinSb
                    .append(" inner join users owr").append(aliasCounter)
                    .append(" on apr.entity_").append(extraMail).append(" = owr").append(aliasCounter).append(".user_id");
            aliasCounter++;
        }
        extraMails.setFields(fieldsSb.toString());
        extraMails.setJoins(joinSb.toString());
        return extraMails;
    }

    private String buildQuery(
            final String sql,
            final String condition,
            final IPendingOperation pendingOperation,
            final LinkedHashMap<String, MailColumn> columns
    ) {
        final String extraColumns = getExtraColumns(sql, pendingOperation, columns);
        if (pendingOperation.getExtraMails().isEmpty()) {
            return pendingOperation.getDailyMailQuery()
                    .replaceAll("<extraColumns>", extraColumns)
                    .replaceAll("<sql>", sql)
                    .replaceAll("<ownerJoin>", "")
                    .replaceAll("<typeId>", pendingOperation.getTypeId().toString())
                    .replaceAll("<condition>", condition);
        }
        final ExtraMailConfigDTO extraMail = getExtraMailConfig(pendingOperation);
        return pendingOperation.getDailyMailQuery()
                .replaceAll("<extraColumns>", extraMail.getFields() + extraColumns)
                .replaceAll("<sql>", sql)
                .replaceAll("<ownerJoin>", extraMail.getJoins())
                .replaceAll("<condition>", condition)
                .replaceAll("<typeId>", pendingOperation.getTypeId().toString());
    }

    private Set<IEscalableDTO> getDailyMailRecords(
            final String sql,
            final String condition,
            final IPendingOperation pendingOperation
    ) {
        final LinkedHashMap<String, MailColumn> columns = new LinkedHashMap<>();
        final String hql = buildQuery(sql, condition, pendingOperation, columns);
        getLogger(LOGGER.APE).trace(hql);
        final List<Object[]> pendings = dao.SQL_findQuery(hql);
        final Set<IEscalableDTO> records = new HashSet<>(pendings.size());
        final ResourceBundle tags = pendingOperation.getEscalationMailerTags();
        final ILoggedUser loggedUser = SecurityRootUtils.getFirstAdminDto();
        for (final Object[] pending : pendings) {
            IEscalableDTO record = pendingOperation.getEscalableRecord(
                    pending,
                    columns,
                    tags,
                    pendingOperation,
                    loggedUser
            );
            records.add(record);
        }
        return records;
    }

    private String getExtraColumns(
            final String query,
            final IPendingOperation pendingOperation,
            final LinkedHashMap<String, MailColumn> extraColumns
    ) {
        final StringBuilder sb = new StringBuilder(200);
        final String queryColumns = query.substring(0, query.toLowerCase().indexOf(SqlQueryParser.FROM));
        final LinkedHashMap<String, MailColumn> columnsRequired = pendingOperation.getDailyMailColumns();
        if (columnsRequired != null) {
            final String lowerColumns = queryColumns.toLowerCase();
            for (final Map.Entry<String, MailColumn> entry : columnsRequired.entrySet()) {
                appendExtraColumn(entry, extraColumns, lowerColumns, sb);
            }
        }
        return sb.toString();
    }

    private void appendExtraColumn(
            final Map.Entry<String, MailColumn> entry,
            final LinkedHashMap<String, MailColumn> extraColumns,
            final String lowerColumns,
            final StringBuilder sb
    ) {
        final String key = entry.getKey();
        final MailColumn column = entry.getValue();
        final String field = "entity_" + key.toLowerCase();
        if (column.isQuery()) {
            extraColumns.put(key, column);
        } else if (column.isAllowedAnticipation()) {
            sb.append(", DATEADD (day, -3,")
                    .append("apr.")
                    .append(field)
                    .append(column.getName())
                    .append(")")
                    .append(" as _")
                    .append(field);
            extraColumns.put("_" + key, MailUtil.DEFAULT_MAIL_COLUMN);
        } else if (lowerColumns.contains(field)) {
            sb.append(", apr.")
                    .append(field)
                    .append(column.getName())
                    .append(" as ")
                    .append("extra")
                    .append(key);
            extraColumns.put(key, column);
        }
    }

    @Override
    public EscalationTags getEscalationTags(IPendingOperation pendingOperation, APE ape, PendingMailerType mailType) {
        ResourceBundle tags =  pendingOperation.getEscalationMailerTags();
        final Module subModule = pendingOperation.getImplementedModule();
        final Module module = subModule == null ? pendingOperation.getApe().module() : subModule;
        final String moduleKey = subModule == null ? module.getKey() : subModule.getKey();

        String pendingName = pendingOperation.getNameEscalateMailOverride(mailType);
        String subject = pendingOperation.getSubjectEscalateMailOverride(mailType);
        String singleRecordSubject = pendingOperation.getSubjectEscalateMailOverride(mailType, null);
        String singleRecordMessageTitle = pendingOperation.getMessageTitleEscalateMailOverride(mailType, null);
        if (subject == null) {
            subject = getTag(tags, "mail.escalated." + moduleKey + "." + pendingOperation.getClass().getCanonicalName());
        }
        if (pendingName == null) {
            pendingName = getTag(tags, "mail.escalated." + moduleKey + "." + pendingOperation.getClass().getCanonicalName());
        }
        if (singleRecordSubject == null) {
            singleRecordSubject = getTag(tags, "mail.escalated." + moduleKey + ".singleRecordSubject." + pendingOperation.getClass().getCanonicalName());
        }
        if (singleRecordMessageTitle == null) {
            singleRecordMessageTitle = getTag(tags, "mail." + mailType.getAction().toLowerCase() + ".singleRecordSubject.message");
        }
        final String moduleName = getTag(tags, "module." + moduleKey);
        final String messageTitle = getTag(tags, "mail." + mailType.getAction().toLowerCase() + ".message");
        final String title = getTag(tags, "mail." + mailType.getAction().toLowerCase() + ".messageModuleTitle");

        EscalationTags result = new EscalationTags();
        result.setTags(tags);
        result.setSubject(subject.replace("${pendingName}", pendingName));
        result.setSingleRecordSubject(
                singleRecordSubject.replace("${pendingName}", pendingName)
        ); // <-- El resto de los atributos se sustituyen al armar `MailDTO`
        result.setSingleRecordMessageTitle(
                singleRecordMessageTitle.replace("${pendingName}", pendingName)
        ); // <-- El resto de los atributos se sustituyen al armar `MailDTO`
        result.setMessageTitle(messageTitle.replace("${pendingName}", pendingName));
        if (!pendingOperation.getExtraMails().isEmpty()) {
            result.setExtraMails(new HashMap<>(pendingOperation.getExtraMails().size()));
            for (final String extraMail : pendingOperation.getExtraMails()) {
                final String extraTag = getTag(tags, "mail." + mailType.getAction().toLowerCase() + ".message." + extraMail);
                result.getExtraMails().put(extraMail, extraTag.replace("${pendingName}", pendingName));
            }
        } else {
            result.setExtraMails(Utilities.EMPTY_MAP);
        }

        result.setMessageModuleTitle(title.replace("${moduleName}", moduleName));
        result.setLink(getTag(tags, "link"));
        result.setInfoLink(getTag(tags, "infoLink"));
        result.setFooter(getTag(tags, "footer"));
        return result;
    }

    public String loadExtraFieldsTemplate(
            final IDailyMailExtraFields base,
            final String templateData,
            final String templateDetail
    ) {
        final LinkedHashMap<String, String> extraFields = base.getExtraFields();
        String data = templateData;
        if (extraFields != null && !extraFields.isEmpty()) {
            data = replaceRecordPattern(data);
            final String recordDetail = getRecordDetail(extraFields, templateDetail, true);
            base.setRecordDetail(recordDetail);
        } else {
            data = clearRecordPattern(data);
        }
        return data;
    }

    private String replaceRecordPattern(String templateData) {
        String data = templateData;
        data = replacePattern(data, RECORD_HEADER_PATTERN.matcher(data));
        data = replacePattern(data, RECORD_CELL_PATTERN.matcher(data));
        return data;
    }

    private String clearRecordPattern(final String templateData) {
        String data = templateData;
        data = clearPattern(data, RECORD_HEADER_PATTERN.matcher(data));
        data = clearPattern(data, RECORD_CELL_PATTERN.matcher(data));
        return data;
    }

    protected List<MailDTO> getDailyMail(
            final IPendingOperation pendingOperation,
            final String sql,
            final String condition,
            final PendingMailerType mailType
    ) {
        final Set<IEscalableDTO> records = getDailyMailRecords(sql, condition, pendingOperation);
        pendingOperation.handleResultRecords(records);
        if (records.isEmpty()) {
            return Utilities.EMPTY_LIST;
        }
        final PendingMailerMode mode = pendingOperation.getPendingMailerMode(mailType);
        final EscalationTags tag = getEscalationTags(pendingOperation, pendingOperation.getApe(), mailType);
        final Mail mailSystem = Utilities.getMailSystem();
        // Se inicializa template con tabla de contenido principal
        String template = getDailyMailTemplate(
                pendingOperation.getPathDailyMailTemplate(),
                mode,
                pendingOperation.getOperationType(),
                mailType,
                pendingOperation
        );
        // Se agregan `extraFields` en sección `recordDetail` al template
        final LinkedHashMap<String, String> extraFields = records.iterator().next().getExtraFields();
        if (extraFields != null && !extraFields.isEmpty()) {
            template = replaceRecordPattern(template);
            final String templateDetail = getDailyMailTemplateByMode(
                    EscalableDTO.class.getPackage().getName() + ".RecordDetail",
                    mode
            );
            for (final IEscalableDTO record : records) {
                final String recordDetail = getRecordDetail(record.getExtraFields(), templateDetail, false);
                record.setRecordDetail(recordDetail);
            }
        } else {
            template = clearRecordPattern(template);
        }

        final List<MailDTO> mails = new ArrayList<>(records.size());
        final Map<Long, Set<EscalableDTO>> recordsByRecipient = Mailer.groupByRecipientId(EscalableDTO.class, records);
        for (final Map.Entry<Long, Set<EscalableDTO>> recipient : recordsByRecipient.entrySet()) {
            final Set<EscalableDTO> userRecords = recipient.getValue();
            if (userRecords.isEmpty()) {
                continue;
            }

            final EscalableDTO firstRecord = userRecords.iterator().next();
            final Mail recipientUser = new Mail(new AddressableUserDTO(
                    firstRecord.getRecipientId(),
                    firstRecord.getRecipientCode(),
                    firstRecord.getRecipientLogin(),
                    firstRecord.getRecipientName(),
                    firstRecord.getRecipientMail(),
                    firstRecord.getRecipientVersion()
            ));
            switch (mode) {
                case BY_TYPE:
                    mails.add(getDailyMailByTypeInstance(mailSystem, recipientUser, pendingOperation, userRecords, template, tag));
                    mails.addAll(getDailyMailsExtraMailsByType(firstRecord, pendingOperation, userRecords, template, tag));
                    break;
                case BY_RECORD:
                    mails.addAll(getDailyMailByRecordInstance(mailSystem, recipientUser, mailType, pendingOperation, userRecords, template, tag));
                    mails.addAll(getDailyMailsExtraMailsByRecord(firstRecord, mailType, pendingOperation, userRecords, template, tag));
                    break;
            }
        }
        return mails;
    }

    private List<MailDTO> getDailyMailsExtraMailsByRecord(
            final EscalableDTO userRecord,
            final PendingMailerType mailType,
            final IPendingOperation pendingOperation,
            final Set<EscalableDTO> records,
            final String template,
            final EscalationTags tag
    ) {
        final List<MailDTO> mails = new ArrayList<>(userRecord.getExtraMails().size());
        final Mail mailSystem = Utilities.getMailSystem();
        for (final ExtraMailUserDTO extraUser : userRecord.getExtraMails()) {
            for (final MailDTO mail : getDailyMailByRecordInstance(
                    mailSystem,
                    extraUser.getMailInstance(),
                    mailType,
                    pendingOperation,
                    records,
                    template,
                    tag
            )
            ) {
                mail.setMessageTitle(tag.getExtraMails().get(extraUser.getField()));
                mail.setMessageLink(tag.getInfoLink());
                mail.setType(Mailer.TYPE.INFO);
                mails.add(mail);
            }
        }
        return mails;
    }

    private List<MailDTO> getDailyMailsExtraMailsByType(
            final EscalableDTO userRecord,
            final IPendingOperation pendingOperation,
            final Set<EscalableDTO> records,
            final String template,
            final EscalationTags tag
    ) {
        final List<MailDTO> mails = new ArrayList<>(userRecord.getExtraMails().size());
        final Mail mailSystem = Utilities.getMailSystem();
        for (final ExtraMailUserDTO extraUser : userRecord.getExtraMails()) {
            final MailDTO mail = getDailyMailByTypeInstance(
                    mailSystem,
                    extraUser.getMailInstance(),
                    pendingOperation,
                    records,
                    template,
                    tag
            );
            mail.setMessageTitle(
                    tag.getExtraMails().get(extraUser.getField()).replace("${counter}", String.valueOf(records.size()))
            );
            mail.setMessageLink(tag.getInfoLink());
            mail.setType(Mailer.TYPE.INFO);
            mails.add(mail);
        }
        return mails;
    }

    private String getDailyMailTemplateByMode(
            String defaultLocation,
            PendingMailerMode mode
    ) {
        String template = Mailer.getTemplate(defaultLocation);
        switch (mode) {
            case BY_RECORD:
                template = Mailer.getTemplate(defaultLocation + "_ByRecord");
                break;
            case BY_TYPE:
                template = Mailer.getTemplate(defaultLocation);
                break;
        }
        return template;
    }

    private String getDailyMailTemplate(
            final String defaultLocation,
            final PendingMailerMode mode,
            final ApeOperationType operationType,
            final PendingMailerType mailType,
            final IPendingOperation pendingOperation
    ) {
        String template = getDailyMailTemplateByMode(defaultLocation, mode);
        while (
                operationTypePatterns.get(operationType).matcher(template).find()
                        || mailTypePatterns.get(mailType).matcher(template).find()
        ) {
            template = operationTypePatterns.get(operationType).matcher(template).replaceAll("");
            template = mailTypePatterns.get(mailType).matcher(template).replaceAll("");
        }
        if (pendingOperation.getRemoveRows()) {
            template = REMOVE_LINE_PATTERN.matcher(template).replaceAll("");
        }
        return template;
    }

    private MailDTO getDailyMailInstance(
            final Mail remittent,
            final Mail recipient,
            final EscalationTags tag
    ) {
        final MailDTO mail = new MailDTO();
        mail.setMessageModuleTitle(tag.getMessageModuleTitle());
        mail.setMessageLink(tag.getLink());
        mail.setMessageFooter(tag.getFooter());
        mail.setRemittent(remittent);
        mail.getRecipients().add(recipient);
        mail.setSubject(tag.getSubject());
        mail.setMessageTitle(tag.getMessageTitle());
        return mail;
    }

    private String getMixedInSubject(
            final String template,
            final EscalableDTO record
    ) {
        /*
          De acuerdo al siguiente benchmark, para strings cortos
          el metodo `StringUtils.replaceEach` supera en performance a otros algoritmos.

          https://stackoverflow.com/a/40836618/1657465

         */

        final String[] defaultKeys = {
                "${entityCode}",
                "${entityDescription}",
                "${subEntityCode}",
                "${subEntityDescription}",
                "${entityTypeName}",
                "${entityDepartmentName}",
                "${entityBusinessUnitName}",
                "${entitySourceCode}",
                "${entitySourceName}",
                "${commitmentDate}",
                "${recipientName}",
                "${recipientCode}"
        };

        final String[] defaultValues = {
                record.getEntityCode(),
                record.getEntityDescription(),
                record.getSubEntityCode(),
                record.getSubEntityDescription(),
                record.getEntityTypeName(),
                record.getEntityDepartmentName(),
                record.getEntityBusinessUnitName(),
                record.getEntitySourceCode(),
                record.getEntitySourceName(),
                record.getCommitmentDate(),
                record.getRecipientName(),
                record.getRecipientCode()
        };

        if (record.getExtraFields() != null) {
            int newSize = defaultKeys.length + record.getExtraFields().size();
            String[] keys = new String[newSize];
            String[] values = new String[newSize];
            for (int i = 0; i < defaultKeys.length; i++) {
                keys[i] = defaultKeys[i];
                values[i] = defaultValues[i];
            }
            int index = defaultKeys.length;
            for (Map.Entry<String, String> entry : record.getExtraFields().entrySet()) {
                keys[index] = "${" + entry.getKey() + "}";
                values[index] = entry.getValue();
                index++;
            }
            return StringUtils.replaceEach( template, keys, values );
        }
        return StringUtils.replaceEach( template, defaultKeys, defaultValues );
    }

    private List<MailDTO> getDailyMailByRecordInstance(
            final Mail remittent,
            final Mail recipient,
            final PendingMailerType mailType,
            final IPendingOperation pendingOperation,
            final Set<EscalableDTO> records,
            final String template,
            final EscalationTags tag
    ) {
        final String mailUrl = pendingOperation.getMailRecordUrl();
        final List<MailDTO> mails = new ArrayList<>(records.size());
        for (final EscalableDTO record : records) {
            final MailDTO mail = getDailyMailInstance(remittent, recipient, tag);
            String subject = pendingOperation.getSubjectEscalateMailOverride(mailType, record);
            if (subject == null) {
                subject = tag.getSingleRecordSubject();
            }
            mail.setSubject(getMixedInSubject(subject, record));
            mail.setRecord(new HashSet<>(Collections.singletonList(record)));
            if (mailUrl != null) {
                final String[] values = {
                        record.getEntityId() + "",
                        record.getRecordId() + "",
                        record.getRecipientId() + "",
                        record.getEntityCode(),
                        record.getSubEntityCode()
                };
                mail.setModuleUrl(StringUtils.replaceEach(mailUrl, DAILY_MAILS_FIXED_MAIL_URL_KEYS, values));
            }
            mail.setMessage(Mailer.mapEntity(record, template, tag.getTags(), dao, pendingOperation.getApe().getModule()));
            final String mixedInSubject = getMixedInSubject(tag.getSingleRecordMessageTitle(), record);
            mail.setMessageTitle(
                    mixedInSubject
            );
            mails.add(mail);
        }
        // Información específica BY_RECORD
        return mails;
    }

    private MailDTO getDailyMailByTypeInstance(
            final Mail remittent,
            final Mail recipient,
            final IPendingOperation pendingOperation,
            final Set<EscalableDTO> records,
            final String template,
            final EscalationTags tag
    ) {
        final MailDTO mail = getDailyMailInstance(remittent, recipient, tag);
        // Información específica BY_TYPE
        final String counter = String.valueOf(records.size());
        mail.setSubject(tag.getSubject().replace("${counter}", counter));
        mail.setMessageTitle(tag.getMessageTitle().replace("${counter}", counter));
        mail.setMessage(Mailer.mapEntity(records, template, tag.getTags(), dao, pendingOperation.getApe().getModule()));
        mail.setRecord(records);
        return mail;
    }

    private String replacePattern(
            final String template,
            final Matcher matcher
    ) {
        final StringBuffer sb = new StringBuffer(template.length());
        if (matcher.find()) {
            final String row = matcher.group(2);
            matcher.appendReplacement(sb, Mailer.replaceEnterToBR(row));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private String clearPattern(final String template, final Matcher matcher) {
        final StringBuffer sb = new StringBuffer(template.length());
        if (matcher.find()) {
            matcher.appendReplacement(sb, Strings.EMPTY);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private String getConditionReminderMail(boolean hasDeadline) {
        String condition = " cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + "Date) >= cast(reminder " + SqlQueryParser.AS + "Date)";
        if (hasDeadline) {
            condition += " AND cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + "Date) <= cast(deadline" + SqlQueryParser.AS + "Date)";
        }
        return condition;
    }

    public List<MailDTO> getReminderMail(IPendingOperation pendingOperation, String sql, boolean hasDeadline) {
        String condition = getConditionReminderMail(hasDeadline);
        return getDailyMail(pendingOperation, sql, condition, PendingMailerType.REMINDER);
    }

    private String getConditionNoticeMail() {
        return " cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + "Date) < cast(commitmentDate " + SqlQueryParser.AS + "Date) "
                + " AND cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + "Date) >= cast(noticeDate" + SqlQueryParser.AS + "Date) ";
    }

    @Override
    public List<MailDTO> getNoticeMail(IPendingOperation pendingOperation, String sql) {
        String condition = getConditionNoticeMail();
        return getDailyMail(pendingOperation, sql, condition, PendingMailerType.NOTICE);
    }

    private String getConditionDeadlineMail() {
        return " cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + "Date) > cast(deadline" + SqlQueryParser.AS + "Date) ";
    }

    @Override
    public List<MailDTO> getDeadlineMail(IPendingOperation pendingOperation, String sql) {
        String condition = getConditionDeadlineMail();
        return getDailyMail(pendingOperation, sql, condition, PendingMailerType.DEADLINE);
    }

    private String getConditionExpiredMail() {
        return " "
                + " cast(expired" + SqlQueryParser.AS + "Date) >= cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + " Date)"
                + " AND sendingExpiredMail <> 1";
    }

    @Override
    public List<MailDTO> getExpiredMail(IPendingOperation pendingOperation, String sql) {
        String condition = getConditionExpiredMail();
        return getDailyMail(pendingOperation, sql, condition, PendingMailerType.EXPIRED);
    }

    @Override
    public Set<IEscalableDTO> getReminderRecords(IPendingOperation operation) {
        String sql = operation.getPendingSql();
        String condition = getConditionReminderMail(operation.getApe().isDeadline());
        return getDailyMailRecords(sql, condition, operation);
    }

    public Mail getUser(final Long userId) {
        final UserRef user = getUserRef(userId);
        if (null == user) {
            return null;
        }
        return toMail(user);
    }

    public UserRef getUserRef(final Long userId) {
        return dao.getBean(IUserDAO.class).getUserRef(userId, true);
    }

    private String getRecordDetail(
            final LinkedHashMap<String, String> extraFields,
            final String templateDetail,
            final Boolean replaceKey
    ) {
        final Matcher matcher = Mailer.REPEAT_PATTERN.matcher(templateDetail);
        final StringBuffer sb = new StringBuffer(templateDetail.length());
        if (matcher.find()) {
            final StringBuilder itemSb = new StringBuilder(extraFields.size() * 20);
            final String row = matcher.group(2);
            for (final Map.Entry<String, String> entrySet : extraFields.entrySet()) {
                String key = entrySet.getKey();
                if (entrySet.getValue() == null) {
                    continue;
                }
                String value = entrySet.getValue();
                if (value.isEmpty() || "0".equals(value))  {
                    continue;
                }
                String matched = PARAM_PATTERN_NAME_FIELD.matcher(Mailer.replaceEnterToBR(row)).replaceAll(key);
                if (replaceKey) {
                    matched = matched.replace("{label." + key + "}", key);
                }
                itemSb.append(matched);
            }
            matcher.appendReplacement(sb, Matcher.quoteReplacement(Mailer.replaceEnterToBR(itemSb.toString())));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
