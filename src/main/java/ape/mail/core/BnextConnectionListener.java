package ape.mail.core;

import com.sun.mail.smtp.SMTPTransport;
import javax.annotation.Nonnull;
import javax.mail.event.ConnectionEvent;
import javax.mail.event.ConnectionListener;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;

public class BnextConnectionListener implements ConnectionListener {

    private static final Logger LOGGER = Loggable.getLogger(BnextConnectionListener.class);

    @Nonnull
    private String typeName(final int type) {
        if (type == ConnectionEvent.OPENED) {
            return "OPENED";
        } else if (type == ConnectionEvent.DISCONNECTED) {
            return "DISCONNECTED";
        } else if (type == ConnectionEvent.CLOSED) {
            return "CLOSED";
        } else {
            return "UNKNOWN";
        }
    }

    @Override
    public void opened(final ConnectionEvent event) {
        if (LOGGER.isDebugEnabled()) {
            if (event.getSource() != null && event.getSource() instanceof SMTPTransport) {
                final SMTPTransport transport = (SMTPTransport) event.getSource();
                LOGGER.debug(
                        "Connection opened, type: \"{}\", source: \"{}\", host: \"{}\", user name: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        transport.getURLName().getHost(),
                        transport.getURLName().getUsername()
                );
            } else {
                LOGGER.debug(
                        "Connection opened, type: \"{}\", source: \"{}\"",
                        typeName(event.getType()),
                        event.getSource()
                );
            }
        }

    }

    @Override
    public void disconnected(final ConnectionEvent event) {
        if (LOGGER.isErrorEnabled()) {
            if (event.getSource() != null && event.getSource() instanceof SMTPTransport) {
                final SMTPTransport transport = (SMTPTransport) event.getSource();
                LOGGER.error(
                        "Connection disconnected, type: \"{}\", source: \"{}\", host: \"{}\", user name: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        transport.getURLName().getHost(),
                        transport.getURLName().getUsername()
                );
            } else {
                LOGGER.error("Connection disconnected, type: \"{}\", source: \"{}\"",
                        typeName(event.getType()),
                        event.getSource()
                );
            }
        }
    }

    @Override
    public void closed(final ConnectionEvent event) {
        if (LOGGER.isDebugEnabled()) {
            if (event.getSource() != null && event.getSource() instanceof SMTPTransport) {
                final SMTPTransport transport = (SMTPTransport) event.getSource();
                LOGGER.debug(
                        "Connection closed, type: \"{}\", source: \"{}\", host: \"{}\", user name: \"{}\", last server response: \"{}\"",
                        typeName(event.getType()),
                        event.getSource(),
                        transport.getURLName().getHost(),
                        transport.getURLName().getUsername(),
                        transport.getLastServerResponse()
                );
            } else {
                LOGGER.debug(
                        "Connection closed, type: \"{}\", source: \"{}\"",
                        typeName(event.getType()),
                        event.getSource()
                );
            }
        }
    }
}
