/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package ape.mail.core;

import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import ape.mail.dto.BnextSmtpTransport;
import com.sun.mail.smtp.SMTPTransport;
import java.time.Duration;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import javax.annotation.Nullable;
import javax.mail.MessagingException;
import javax.mail.Session;
import mx.bnext.cipher.Encryptor;
import mx.bnext.core.util.Loggable;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.slf4j.Logger;
import qms.framework.util.ExceptionUtils;

/**
 * <AUTHOR>
 */
public class SmtpTransport {

    private static final Logger LOGGER = Loggable.getLogger(SmtpTransport.class);

    private static GenericObjectPool<BnextSmtpTransport> TRANSPORTS;

    public static synchronized void clean() {
        try {
            if (TRANSPORTS == null) {
                return;
            }
            LOGGER.trace("Cleaning transport pool with {} elements", TRANSPORTS.getCreatedCount());
            TRANSPORTS.clear();
            LOGGER.trace("Transport pool has been cleared");
        } catch (final Exception e) {
            LOGGER.error("Failed to clean transport pool", e);
        }
    }

    public static synchronized void rebuild() {
        if (TRANSPORTS != null) {
            LOGGER.trace("Transport pool already initialized, cleaning it");
            clean();
        }
        try {
            final Settings settings = Utilities.getSettings();
            final GenericObjectPoolConfig<BnextSmtpTransport> config = new GenericObjectPoolConfig<>();
            config.setMaxTotal(settings.getMailSenderPoolSize());
            config.setMaxWait(Duration.ofSeconds(settings.getMailSenderAliveTime()));
            TRANSPORTS = new GenericObjectPool<>(new SmtpTransportFactory(), config);
            LOGGER.trace("Transport pool has been initialized with {} elements", settings.getMailSenderPoolSize());
        } catch (Exception e) {
            TRANSPORTS = null;
            throw e;
        }
    }

    private static String getSmtpPassword() {
        try {
            Settings s = Utilities.getSettings();
            String key = s.getSystemId() + "_" + s.getRcKey();
            return Encryptor.decrypt(s.getHashedSmtpPsw(), key);
        } catch (final Exception e) {
            final String msg = ExceptionUtils.getRootCauseMessage(e);
            LOGGER.error("Failed to decrypt SMTP password {} ", msg);
            return null;
        }
    }

    private static void connect(final SMTPTransport transport) throws MessagingException {
        final Settings settings = Utilities.getSettings();
        final String smtpHost = settings.getSmtpHost();
        if (isConnected(transport)) {
            LOGGER.trace("Transport is already connected to {}", smtpHost);
            return;
        }
        final boolean smtpAuthentication = Objects.equals(settings.getSmtpAuthentication(), 1);
        final String smtpPassword;
        if (smtpAuthentication) {
            smtpPassword = getSmtpPassword();
        } else {
            smtpPassword = null;
        }
        final Integer smtpPort = settings.getSmtpPort();
        final String smtpUid = settings.getSmtpUid();
        if (smtpAuthentication) {
            LOGGER.trace("Connecting transport with authentication to {}", smtpHost);
            transport.connect(smtpHost, smtpPort, smtpUid, smtpPassword);
            LOGGER.trace("Connected transport WITH authentication enabled to {}", smtpHost);
        } else {
            LOGGER.trace("Connecting to anonymous transport to {}", smtpHost);
            transport.connect();
        }
    }

    private static boolean isConnected(final SMTPTransport transport) {
        try {
            return transport.isConnected();
        } catch (final IllegalStateException ex) {
            // Ignore illegal state exception as it is expected in some cases
            return false;
        } catch (final Exception ex) {
            LOGGER.error("Failed to check if transport is connected", ex);
            return false;
        }
    }

    /**
     * Get transport from the pool it must return the transport to the pool
     */
    @Nullable
    public static BnextSmtpTransport getTransport(String transportId)
            throws ExecutionException, MessagingException {
        try {
            LOGGER.debug("Requesting transport for {}", transportId);
            if (TRANSPORTS == null) {
                LOGGER.trace("Transport pool is null, initializing it for {}", transportId);
                rebuild();
            }
            if (TRANSPORTS == null) {
                LOGGER.error("Failed to rebuild transport pool, the operation will not be retried");
                return null;
            }
            final Settings settings = Utilities.getSettings();
            final BnextSmtpTransport bnextTransport = TRANSPORTS.borrowObject(Duration.ofSeconds(settings.getMailSenderAliveTime()));
            if (bnextTransport == null || bnextTransport.getSmtpTransport() == null) {
                LOGGER.error("Failed to borrow transport, the operation will be retried");
                return createTransport();
            } else {
                if (!isConnected(bnextTransport.getSmtpTransport())) {
                    connect(bnextTransport.getSmtpTransport());
                }
                if (!isConnected(bnextTransport.getSmtpTransport())) {
                    LOGGER.error("Failed to connect transport, the operation will be retried");
                    TRANSPORTS.invalidateObject(bnextTransport);
                    returnTransport(bnextTransport);
                    return null;
                }
                return bnextTransport;
            }
        } catch (final NoSuchElementException ex) {
            final String error = ExceptionUtils.getRootCauseMessage(ex);
            LOGGER.error("Failed to acquire transport for {}, the operation will be retried {}", transportId, error, ex);
            return createTransport();
        } catch (final Exception ex) {
            final String error = ExceptionUtils.getRootCauseMessage(ex);
            LOGGER.error("Failed to create transport for {} {}", transportId, error, ex);
            return null;
        }
    }

    public static void returnTransport(final BnextSmtpTransport transport) {
        if (TRANSPORTS == null || transport == null) {
            LOGGER.error("Can not return transport, transport is null or transport pool is null");
            return;
        }
        try {
            TRANSPORTS.returnObject(transport);
            LOGGER.trace("Transport has been returned to the pool");
        } catch (final Exception e) {
            LOGGER.trace("-- Error trying to return transporter to queue", e);
        }
    }

    @Nullable
    public static BnextSmtpTransport createTransport() throws MessagingException {
        try {
            final Session smtpSession = Utilities.getSMTPSession(null);
            final SMTPTransport transport = (SMTPTransport) smtpSession.getTransport("smtp");
            transport.addConnectionListener(new BnextConnectionListener());
            transport.addTransportListener(new BnextTransportListener());
            if (!isConnected(transport)) {
                connect(transport);
            }
            if (!isConnected(transport)) {
                LOGGER.error("Failed to connect transport");
                return null;
            }
            return new BnextSmtpTransport(transport);
        } catch (final Exception ex) {
            LOGGER.error("Failed to create transport", ex);
            return null;
        }
    }

    public static void closeTransport(final SMTPTransport transport) {
        if (transport == null) {
            LOGGER.error("Can not close transport, transport is null");
            return;
        }
        try {
            transport.close();
            LOGGER.trace("-- Closed transport");
        } catch (final Exception ex) {
            LOGGER.trace("-- Error trying to close transporter", ex);
        }
    }

}
