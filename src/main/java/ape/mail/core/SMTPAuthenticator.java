package ape.mail.core;

import javax.mail.Authenticator;
import javax.mail.PasswordAuthentication;

public class <PERSON><PERSON>Authenticator extends Authenticator {

    private final String username;
    private final String password;

    public SMTPAuthenticator(String uid, String pwd) {
        username = uid;
        password = pwd;
    }

    @Override
    public PasswordAuthentication getPasswordAuthentication() {
        return new PasswordAuthentication(username, password);
    }
}
