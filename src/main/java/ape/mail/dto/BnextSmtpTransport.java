package ape.mail.dto;

import ape.mail.core.SmtpTransport;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sun.mail.smtp.SMTPTransport;
import java.io.Closeable;
import java.io.IOException;
import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BnextSmtpTransport implements Closeable, Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private SMTPTransport smtpTransport;

    public BnextSmtpTransport() {
        this.id = UUID.randomUUID().toString();
    }

    public BnextSmtpTransport(SMTPTransport smtpTransport) {
        this.smtpTransport = smtpTransport;
        this.id = UUID.randomUUID().toString();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public SMTPTransport getSmtpTransport() {
        return smtpTransport;
    }

    public void setSmtpTransport(SMTPTransport smtpTransport) {
        this.smtpTransport = smtpTransport;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BnextSmtpTransport other = (BnextSmtpTransport) obj;
        return Objects.equals(id, other.getId());
    }

    @Override
    public String toString() {
        return "BnextSmtpTransport{" + "id=" + id + '}';
    }

    /**
     * Return the transport to the pool
     *
     */
    @Override
    public void close() throws IOException {
        SmtpTransport.returnTransport(this);
    }
}
