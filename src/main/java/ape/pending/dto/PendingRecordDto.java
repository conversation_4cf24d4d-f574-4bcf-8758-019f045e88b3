
package ape.pending.dto;

import Framework.Config.ITextHasValue;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class PendingRecordDto implements Serializable {
    
    private static final long serialVersionUID = 1L;

    private List<String> advancedFields;
    private List<String> blacklistFields;
    private List<String> mutualExclusionFields;
    private Map<String, Object> data;
    private Map<String, List<? extends ITextHasValue>> catalogs;

    public PendingRecordDto() {
    }

    public List<String> getMutualExclusionFields() {
        return mutualExclusionFields;
    }

    public void setMutualExclusionFields(List<String> mutualExclusionFields) {
        this.mutualExclusionFields = mutualExclusionFields;
    }

    public List<String> getAdvancedFields() {
        return advancedFields;
    }

    public void setAdvancedFields(List<String> advancedFields) {
        this.advancedFields = advancedFields;
    }

    public List<String> getBlacklistFields() {
        return blacklistFields;
    }

    public void setBlacklistFields(List<String> blacklistFields) {
        this.blacklistFields = blacklistFields;
    }


    

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public Map<String, List<? extends ITextHasValue>> getCatalogs() {
        return catalogs;
    }

    public void setCatalogs(Map<String, List<? extends ITextHasValue>> catalogs) {
        this.catalogs = catalogs;
    }



    
}
