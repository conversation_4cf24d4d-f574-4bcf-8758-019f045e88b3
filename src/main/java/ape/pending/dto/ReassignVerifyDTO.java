/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package ape.pending.dto;

import java.util.Date;
import java.util.List;
import qms.activity.dto.ActivityPendingDto;

/**
 *
 * <AUTHOR>
 */
public class ReassignVerifyDTO {
    private List<ActivityPendingDto> implementations;
    private Long verifierId;
    private Date verificationDate;
    private String reasonChange;

    public Long getVerifierId() {
        return verifierId;
    }

    public void setVerifierId(Long verifierId) {
        this.verifierId = verifierId;
    }

    public Date getVerificationDate() {
        return verificationDate;
    }

    public void setVerificationDate(Date verificationDate) {
        this.verificationDate = verificationDate;
    }
    
    public List<ActivityPendingDto> getImplementations() {
        return implementations;
    }

    public void setImplementations(List<ActivityPendingDto> implementations) {
        this.implementations = implementations;
    }

    public String getReasonChange() {
        return reasonChange;
    }

    public void setReasonChange(String reasonChange) {
        this.reasonChange = reasonChange;
    }
    
}
