
package ape.pending.dto;

import DPMS.Mapping.Dual;
import ape.pending.core.PendingHelper;
import ape.pending.util.SqlQueryParser;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class BaseJoinDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    public static String JOIN_KEY = "{JOIN}", CONDITION_KEY = "{CONDITION}", SELECT_KEY = "{SELECT}";
    private final String columns, joins, conditions, crossConditions, baseConditions, typeConditions, 
        PENDING_RECORD_HQL = " "
            + SqlQueryParser.SELECT
            + " new map("
                + PendingHelper.PENDING_RECORD_SELECT_MAP
                + SELECT_KEY
            + " )"
            + SqlQueryParser.FROM
            + PendingHelper.PENDING_RECORD_FROM
            + JOIN_KEY
            + SqlQueryParser.WHERE
            + CONDITION_KEY;
    private final String 
        ENTITY_HQL = ""
            + SqlQueryParser.SELECT
            + " new map("
            + "record.dummy AS dummy"
            + SELECT_KEY
            + " ) "
            + SqlQueryParser.FROM
            + Dual.class.getCanonicalName() + " record "
            + "," + Dual.class.getCanonicalName() + " ptype "
            + JOIN_KEY
            + SqlQueryParser.WHERE
            + CONDITION_KEY
    ;
    private final ApeEntityConfig entityConfig;

    public BaseJoinDTO(
            String columns,
            String joins,
            String conditions, 
            String crossConditions,
            String baseConditions, 
            String typeConditions,
            ApeEntityConfig entityConfig
    ) {
        this.columns = columns;
        this.joins = joins;
        this.conditions = conditions;
        this.crossConditions = crossConditions;
        this.baseConditions = baseConditions;
        this.typeConditions = typeConditions;
        this.entityConfig = entityConfig;
    }

    public String getInjectedHQL() {
        //@ToDo: La condición 0=0 se ocupa en DynamicFieldDAO.addWhere
        final String condition = "0=0"
                + conditions
                + crossConditions
                + baseConditions
                + typeConditions;
        return PENDING_RECORD_HQL
                .replace(JOIN_KEY, joins)
                .replace(CONDITION_KEY, condition)
                .replace(SELECT_KEY, columns);
    }

    public String getHQL() {
        //@ToDo: La condición 0=0 se ocupa en DynamicFieldDAO.addWhere
        final String condition = "0=0"
                + conditions
                + crossConditions
                + baseConditions
                + typeConditions;
        return ENTITY_HQL
                .replace(JOIN_KEY, joins)
                .replace(CONDITION_KEY, condition)
                .replace(SELECT_KEY, columns);
    }
    
    public String getColumns() {
        return columns;
    }

    public String getJoins() {
        return joins;
    }

    public String getConditions() {
        return conditions;
    }

    public String getCrossConditions() {
        return crossConditions;
    }

    public String getBaseConditions() {
        return baseConditions;
    }

    public String getTypeConditions() {
        return typeConditions;
    }

    public ApeEntityConfig getEntityConfig() {
        return entityConfig;
    }
    

}
