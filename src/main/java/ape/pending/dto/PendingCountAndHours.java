/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package ape.pending.dto;

import ape.pending.core.APE;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import mx.bnext.access.Module;
import qms.util.ModuleUtil;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PendingCountAndHours {
    private APE ape;
    private Module module;
    private Long pendingCount;
    private Double pendingPlannedHours;
    private Integer pendingStatus;
    private Date pendingDate;
    private Date pendingEndDate;
    private Long pendingRecordUserId;
    private String description;
    private String code;
    private Long activityId;
    private Long implementationId;
    private Long createdBy;
    private String creatorUserName;
    private Long clientId;
    private String clientName;

    public PendingCountAndHours() {
    }
    
    public PendingCountAndHours(
        String pendingTypeCode, 
        String moduleKey, 
        Long pendingCount, 
        Double pendingPlannedHours,
        Integer pendingStatus,
        Long pendingRecordUserId, 
        Date pendingDate, 
        Date pendingEndDate, 
        String code, 
        String description,
        Long activityId,
        Long implementationId,
        Long createdBy,
        String creatorUserName,
        Long clientId,
        String clientName
    ) {
        this.ape = APE.fromCode(pendingTypeCode);
        this.module = ModuleUtil.fromKey(moduleKey);
        this.pendingCount = pendingCount;
        if (pendingPlannedHours != null) {
            this.pendingPlannedHours = pendingPlannedHours;
        } else {
            this.pendingPlannedHours = 0.0;
        }
        this.pendingStatus = pendingStatus;
        this.pendingRecordUserId = pendingRecordUserId;
        this.pendingDate = pendingDate;
        this.pendingEndDate = pendingEndDate;
        this.code = code;
        this.description = description;
        this.activityId = activityId;
        this.implementationId = implementationId;
        this.createdBy = createdBy;
        this.creatorUserName = creatorUserName;
        this.clientId = clientId;
        this.clientName = clientName;
    }

    public Date getPendingDate() {
        return pendingDate;
    }

    public void setPendingDate(Date pendingDate) {
        this.pendingDate = pendingDate;
    }

    public Long getPendingRecordUserId() {
        return pendingRecordUserId;
    }

    public void setPendingRecordUserId(Long pendingRecordUserId) {
        this.pendingRecordUserId = pendingRecordUserId;
    }

    public APE getApe() {
        return ape;
    }

    public void setApe(APE ape) {
        this.ape = ape;
    }

    public Module getModule() {
        return module;
    }

    public void setModule(Module module) {
        this.module = module;
    }

    public Long getPendingCount() {
        return pendingCount;
    }

    public void setPendingCount(Long pendingCount) {
        this.pendingCount = pendingCount;
    }
    
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }
    
    public Long getImplementationId() {
        return implementationId;
    }

    public void setImplementationId(Long implementationId) {
        this.implementationId = implementationId;
    }

    public Double getPendingPlannedHours() {
        return pendingPlannedHours;
    }

    public void setPendingPlannedHours(Double pendingPlannedHours) {
        this.pendingPlannedHours = pendingPlannedHours;
    }

    public Integer getPendingStatus() {
        return pendingStatus;
    }

    public void setPendingStatus(Integer pendingStatus) {
        this.pendingStatus = pendingStatus;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getPendingEndDate() {
        return pendingEndDate;
    }

    public void setPendingEndDate(Date pendingEndDate) {
        this.pendingEndDate = pendingEndDate;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatorUserName() {
        return creatorUserName;
    }

    public void setCreatorUserName(String creatorUserName) {
        this.creatorUserName = creatorUserName;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 13 * hash + Objects.hashCode(this.ape);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PendingCountAndHours other = (PendingCountAndHours) obj;
        return this.ape == other.ape;
    }
    
}
