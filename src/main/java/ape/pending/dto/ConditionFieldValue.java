/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package ape.pending.dto;

/**
 *
 * <AUTHOR>
 */
public class ConditionFieldValue {
    
    public enum ConditionOperator {
        EQUALS(" = "),
        SUBCONDITION(" SUBCONDITION "),
        BETWEEN_VALUES(" BETWEEN "),
        BETWEEN_COLUMNS(" BETWEEN_COLUMNS "),
        GREATER_OR_EQUALS(" >= "),
        SMALLER_OR_EQUALS(" <= ");
        private final String key;
        ConditionOperator(String key) {
            this.key = key;
        }
        public String getKey() {
            return this.key;
        }
    }

    private String methodWrap; // <-- El nombre un método, si vale `trunc`. La condición será `WHERE trunc(tableAlias.fieldName) >= :tableAlias_fieldName`
    private String fieldName;
    private String fieldAlias;
    private String tableAlias;
    private ConditionOperator conditionOperator;
    private Object value;
    private String betweenColumns;

    public ConditionFieldValue() {
    }

    public ConditionFieldValue(
            String tableAlias,
            String methodWrap,
            String fieldName,
            String fieldAlias,
            ConditionOperator conditionOperator,
            Object value
    ) {
        this.methodWrap = methodWrap;
        this.fieldAlias = fieldAlias;
        this.fieldName = fieldName;
        this.tableAlias = tableAlias;
        this.conditionOperator = conditionOperator;
        this.value = value;
    }

    public ConditionFieldValue(
            String fieldAlias,
            String betweenColumns,
            ConditionOperator conditionOperator,
            Object value
    ) {
        this.fieldAlias = fieldAlias;
        this.betweenColumns = betweenColumns;
        this.conditionOperator = conditionOperator;
        this.value = value;
    }

    public ConditionFieldValue(ConditionOperator conditionOperator, Object value) {
        this.conditionOperator = conditionOperator;
        this.value = value;
    }

    public String getFieldAlias() {
        return fieldAlias;
    }

    public void setFieldAlias(String fieldAlias) {
        this.fieldAlias = fieldAlias;
    }

    public String getMethodWrap() {
        return methodWrap;
    }

    public void setMethodWrap(String methodWrap) {
        this.methodWrap = methodWrap;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getTableAlias() {
        return tableAlias;
    }

    public void setTableAlias(String tableAlias) {
        this.tableAlias = tableAlias;
    }

    public ConditionOperator getConditionOperator() {
        return conditionOperator;
    }

    public void setConditionOperator(ConditionOperator conditionOperator) {
        this.conditionOperator = conditionOperator;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public String getBetweenColumns() {
        return betweenColumns;
    }

    public void setBetweenColumns(String betweenColumns) {
        this.betweenColumns = betweenColumns;
    }
}
