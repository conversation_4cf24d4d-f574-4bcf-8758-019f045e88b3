/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package ape.pending.core;

/**
 *
 * <AUTHOR>
 */
public enum PendingMailerType {

    /**
     * El "reminder" es la fecha de compromiso en la cual es necesario
     * recordarle al responsable que DEBE atender su pendiente a la brevedad.
     *
     * Ejemplo:
     * La fecha de recordatorios siempre se calcula en base a la fecha
     * de compromiso.
     */
    REMINDER("REMINDER", "Delayed"),
    /**
     * El "notice" es la fecha anticipada para mandar avisos.
     *
     * Ejemplo:
     * Para actividades se calcula a partir de la fecha de compromiso
     * menos los la configuración para mandar avisos del tipo de actividad.
     */
    NOTICE("NOTICE", "Notice"),
    /**
     * El "deadline" es la última fecha disponible para atender el
     * pendiente, una vez sobre pasada el pendiente se escala.
     *
     * Ejemplo:
     * Para actividades se calcula a partir de la fecha de creación más la
     * cantidad de días limite abiertos
     */
    DEADLINE("DEADLINE", "Deadline"),
    
    /**
     * El "expired" es cuando un pendiente ha caducado y ya no se encuentra
     * disponible para realizarse, a la fecha 28/02/2024, SOLO formularios tiene
     * esta caracteristica (ToFillForm).
     *
     * Ejemplo:
     * Para formularios por llenar cuando se cumple su fecha de expiración 
     * el pendiente se marca como realizado y su flujo de llenado se marca como
     * expirado.
     */
    EXPIRED("EXPIRED", "Expired");
    private final String value;
    private final String action;

    private PendingMailerType(String value, String action) {
        this.value = value;
        this.action = action;
    }

    public String getAction() {
        return action;
    }

    public String getValue() {
        return this.value;
    }
}
