package ape.pending.core;

import DPMS.Mapping.IBusinessUnit;
import DPMS.Mapping.IBusinessUnitDepartment;
import DPMS.Mapping.IUser;
import ape.mail.core.MailHelper;
import ape.mail.dto.MailColumn;
import ape.pending.dto.ColumnDTO;
import ape.pending.dto.PendingConditional;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.regex.Pattern;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import mx.bnext.core.security.ISecurityUser;
import qms.access.dto.ILoggedUser;
import qms.escalation.dto.IEscalableDTO;
import qms.framework.mail.MailDTO;

/**
 *
 * <AUTHOR>
 */
public interface IPendingOperation extends IPendingQueries {

    public static final Pattern PARAM_PATTERN_STATUS = Pattern.compile(":status");
    public static final Pattern PARAM_PATTERN_TYPE = Pattern.compile(":type");
    public static final Pattern PARAM_PATTERN_OWNER = Pattern.compile(":owner");
    public static final Pattern PARAM_PATTERN_SCOPE = Pattern.compile(":scope");
    public static final Pattern PARAM_PATTERN_RECORD_ID = Pattern.compile(":recordId");

    public APE getApe();

    public Long getTypeId();
        
    /**
     * Se realizan todas las operaciones de recalculo a un pendiente especifico:
     * 1) Mantiene correcta la fecha de compromiso, solo se utiliza cuando se recalculan pendientes
     * 2) Mantiene correcta la fecha de aviso, solo se utiliza cuando se recalculan pendientes
     * 3) Mantiene correcta la fecha de recordatorio, solo se utiliza cuando se envían correos diarios
     * 4) Incrementa contador de escalamiento
     * 5) Marca pendientes como escaladados
     * 6) Marca pendientes como atendidos
     * 7) Da de alta pendientes activos considerando si el mismo fue escalado
     * 8) Actualiza usuarios relacionados al pendiente reasignados
     * 9) Atiende dependencias
     * 
     * @param loggedUser
     * @param trigger
     * @param recordId
     */
    public void recalculate(@Nonnull ISecurityUser loggedUser, Class trigger, Long ... recordId);
    
    public void recalculate(@Nonnull ISecurityUser loggedUser, Class trigger, PendingConditional conditional, Long ... recordId);

    /**
     * Se recalculan solo los pendientes atendidos y activos:
     * 1) Marca pendientes como atendidos
     * 2) Da de alta pendientes activos considerando si el mismo fue escalado
     * 
     * @param loggedUser
     * @param trigger
     */
    public void recalculateActiveAndAttended(@Nonnull ISecurityUser loggedUser, Class trigger);
    
    public Integer updateCounterByUsers(Set<Long> users, @Nonnull ISecurityUser loggedUser);

    public void updateCountersByType(Long type, @Nonnull ISecurityUser loggedUser, Long ownerNewId);

    public Integer getCountByUser(Long user);
    
    public void dailyCheck(Class trigger, @Nonnull ILoggedUser loggedUser);

    public Module getImplementedModule();
    
    public boolean hasImplementedModule();

    public List<String> getStack();

    public void setStack(List<String> stack);
    public List<IUser> getPossibleResponsibleList(Long recordId);
    public List<IUser> getPossibleResponsibleList(
            Long recordId, 
            IBusinessUnitDepartment businessUnitDepartment
    );
    public List<IUser> getPossibleResponsibleList(Long recordId, IBusinessUnit businessUnit);
    public List<IUser> getPossibleResponsibleList(Long recordId, Long currentUserId);
    public List<IUser> getPossibleResponsibleList(
            Long recordId,
            Long currentUserId,
            IBusinessUnitDepartment businessUnitDepartment
    );
    public List<IUser> getPossibleResponsibleList(
            Long recordId, 
            Long currentUserId,
            IBusinessUnit businessUnit
    );

    public List<MailDTO> getReminderMail();

    public String getPendingSql();
    
    public String getPendingSql(String appendEndHql);

    public Class<? extends BaseAPE> getEntityBase();

    public LinkedHashMap<String, MailColumn> getDailyMailColumns();

    public void setExtraFieldsMap(LinkedHashMap<String, MailColumn> listExtraFields);

    public List<String> getExtraMails();

    public void setExtraMails(List<String> extraUsers);
    
    public void setExtraMails(String ... extraUsers);
    
    public Set<Long> updateRecord(Class trigger, PendingConditional conditional, @Nonnull ISecurityUser loggedUser, Long ... recordId);
    
    public Set<Long> updateRecord(Long recordId, PendingConditional conditional, Class trigger, @Nonnull ISecurityUser loggedUser);
    
    public ColumnDTO[] getBaseSummaryFields();
    
    public ColumnDTO[] getModuleSummaryFields();
    
    public SummaryTemplate getSummaryTemplate();
    
    public ApeOperationType getOperationType();

    public Set<ColumnDTO> getLocalizedFields();

    public boolean isPendingActive(Long recordId);
    
    public int updateActive(String base, Map<String, Object> fields, Long ... recordId);

    public Long getPendingRecordIdFromRecordId(@Nonnull ISecurityUser loggedUser, Long recordId, String ... typeCodes);
    
    public void handleResultRecords(Set<IEscalableDTO> records);
    
    public String getPathDailyMailTemplate();
    
    public String getDailyMailQuery();
    
    public MailHelper getMailHelper();
    
    public void setDailyMailQuery(String dailyMailQuery);
    
    public void setMailHelper(MailHelper mailHelper);

    public String getNameEscalateMailOverride(PendingMailerType type);

    /**
     * Variables disponibles:
     *   ${counter}
     * 
     * @param type
     * @return
     */
    public String getSubjectEscalateMailOverride(PendingMailerType type);
    
    /**
     * Variables disponibles:
     *   ${entityCode}
     *   ${entityDescription}
     *   ${subEntityCode}
     *   ${subEntityDescription}
     *   ${entityTypeName}
     *   ${entityDepartmentName}
     *   ${entityBusinessUnitName}
     *   ${entitySourceCode}
     *   ${entitySourceName}
     *   ${commitmentDate}
     *   ${recipientName}
     *   ${recipientCode}
     * 
     * @param type
     * @param record
     * @return 
     */
    public String getSubjectEscalateMailOverride(PendingMailerType type, IEscalableDTO record);
    
    /**
     * Variables disponibles:
     *   ${entityCode}
     *   ${entityDescription}
     *   ${subEntityCode}
     *   ${subEntityDescription}
     *   ${entityTypeName}
     *   ${entityDepartmentName}
     *   ${entityBusinessUnitName}
     *   ${entitySourceCode}
     *   ${entitySourceName}
     *   ${commitmentDate}
     *   ${recipientName}
     *   ${recipientCode}
     * 
     * @param type
     * @param record
     * @return 
     */
    public String getMessageTitleEscalateMailOverride(PendingMailerType type, IEscalableDTO record);
    
    public PendingMailerMode getPendingMailerMode(PendingMailerType type);

    /**
     * Variables disponibles:
     * ${entityId}
     * ${recordId}
     * ${recipientId}
     * ${entityCode}
     * ${subEntityCode}
     * 
     * @param type
     * @param record
     * @return 
     */
    public String getMailRecordUrl();
   
    public IEscalableDTO getEscalableRecord(
            final Object[] temp,
            final LinkedHashMap<String, MailColumn> extraColumns,
            final ResourceBundle tags,
            final IPendingOperation pendingOperation,
            final ILoggedUser loggedUser
    );
    
    public ResourceBundle getEscalationMailerTags();
    
    public boolean getRemoveRows();
    
    public void execute(Long record, Class trigger, @Nonnull ISecurityUser loggedUser);

}
