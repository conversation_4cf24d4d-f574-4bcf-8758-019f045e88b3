
package ape.pending.core;

import Framework.Action.DefaultAction;
import Framework.DAO.IUntypedDAO;

/**
 * Procesa una petición de un entity asociado a un PendingRecord para determinar
 * si el usuario en sessión tiene o no permiso para atender el pendiente,
 * 
 * La intención es validar en el JSP/JS la variable ${pendingOperationAccess}, de ser 
 * verdadera entonces el usuario en cuestión sí tiene acceso a atender el pendiente.
 * 
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class PendingOperationAction extends DefaultAction {

    private static final long serialVersionUID = -7126239496662885360L;
    
    private boolean pendingOperationAccess, pendingOperation = true;
    private Long pendingRecordId;

    @Override
    public String execute() throws Exception {
        String access = super.execute();
        if(!SUCCESS.equals(access)) {
            return access;
        }
        if(!pendingOperation) {
            return SUCCESS;
        }
        if(pendingRecordId == null) {
            getLogger().error("Missing pendingRecordId parameter value");
            return INVALID;
        }
        IUntypedDAO dao = getUntypedDAO();
        PendingHelper helper = new PendingHelper(dao);
        APE ape = helper.getPendingRecordAPE(pendingRecordId);
        this.pendingOperationAccess = new PendingHelper(dao).isPendingValidById(pendingRecordId,  ape, getLoggedUserId());
        return SUCCESS;
    }

    public boolean isPendingOperationAccess() {
        return pendingOperationAccess;
    }

    public Long getPendingRecordId() {
        return pendingRecordId;
    }

    public void setPendingRecordId(Long pendingRecordId) {
        this.pendingRecordId = pendingRecordId;
    }

    public void setPendingOperation(boolean pendingOperation) {
        this.pendingOperation = pendingOperation;
    }    
    
}
