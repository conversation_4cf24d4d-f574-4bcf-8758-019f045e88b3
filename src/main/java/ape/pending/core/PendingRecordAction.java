package ape.pending.core;

import Framework.Action.StatusListAction;
import ape.pending.entities.PendingRecord;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class PendingRecordAction extends StatusListAction {

    private static final long serialVersionUID = 1540444544516062424L;

    private String moduleName;

    @Override
    public String execute() throws Exception {
        if (moduleName == null || moduleName.isEmpty()) {
            getLogger().error("Please ser 'moduleName' parameter at struts.xml call.");
            return INVALID;
        }
        setSerializedStatusList(true);
        return super.execute();
    }

    @Override
    protected String getSerializableStatusEntity() {
        return PendingRecord.class.getCanonicalName();
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

}
