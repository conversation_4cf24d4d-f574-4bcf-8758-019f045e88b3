package ape.pending.core;

import ape.pending.util.ApeSelectType;

/**
 *
 * <AUTHOR>
 */
public interface IPendingQueries {
    
    public StringBuilder queryByUser(ApeSelectType type, Long user);

    public StringBuilder queryByRecord(Long ... record);
    public StringBuilder queryNoFilter();
    public String filterRecordsByUser(Long owner, String alias);
    public StringBuilder filterRecordsByUser(Long owner, String alias, String userField);
    public StringBuilder filterRecordsByRecordId(Long owner, String recordId);
    public StringBuilder filterSourceRecordsByUser(Long owner, String alias);
    
}