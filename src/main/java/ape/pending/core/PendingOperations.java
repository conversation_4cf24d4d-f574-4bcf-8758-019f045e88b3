package ape.pending.core;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.IBusinessUnit;
import DPMS.Mapping.IBusinessUnitDepartment;
import DPMS.Mapping.IUser;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.mail.core.MailHelper;
import ape.mail.dto.MailColumn;
import ape.pending.dto.ColumnDTO;
import ape.pending.dto.PendingConditional;
import ape.pending.entities.PendingCount;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import ape.pending.util.ApeConstants;
import ape.pending.util.ApeSelectType;
import ape.pending.util.SqlProperties;
import ape.pending.util.SqlQueryParser;
import bnext.reference.UserRef;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.security.ISecurityUser;
import mx.bnext.core.util.Loggable;
import qms.access.dto.ILoggedUser;
import qms.escalation.dto.EscalableDTO;
import qms.escalation.dto.IEscalableDTO;
import qms.framework.mail.MailDTO;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.util.CacheRegion;
import qms.framework.util.LocaleUtil;
import qms.util.BindUtil;
import qms.util.Translate;
import qms.util.interfaces.IUntypedDAOHolder;

/**
 *
 * <AUTHOR>
 */
public abstract class PendingOperations extends Loggable implements IPendingOperation, IUntypedDAOHolder, INoticeOperation {
   
    private APE ape;
    private PendingType _type;
    private PendingRecord.Scope _scope;
    private MailHelper _mailHelper = null;
    private String _module = null;
    private String _base;
    private Class<? extends BaseAPE> _entityBase;
    private LinkedHashMap<String,MailColumn> listExtraFields;    
    private String _whereFilter = null;
    private String _userField;
    private String _departmentField;
    private String _commitment = ApeConstants.HQL_CURRENT_DATE;
    private String _query;
    private String _alias;
    private Class<? extends IPendingOperation>[] dependencies = null;
    private ColumnDTO[] baseSummaryFields = new ColumnDTO[]{new ColumnDTO("code"), new ColumnDTO("description")};
    private ColumnDTO[] moduleSummaryFields = new ColumnDTO[]{};
    private final SummaryTemplate summaryTemplate = new SummaryTemplate("${code}, ${description}");
    private List<String> stack = null;
    private List<String> extraMails = Utilities.EMPTY_LIST;
    private ApeOperationType operationType = ApeOperationType.STRONG;
    
    private String _noticeDate = ApeConstants.HQL_CURRENT_DATE;
    private String _deadline = ApeConstants.HQL_CURRENT_DATE;
    private String _reminder = ApeConstants.HQL_CURRENT_DATE;
    private String _expired = ApeConstants.HQL_CURRENT_DATE;
    private String dailySendMailQuery = ApeConstants.DAILY_MAIL_QUERY;
    
    private Boolean isManuallyTraceEnabled = false;
    private boolean isEscalateEnabled = true;
    private boolean isUnattendedCountEnabled = true;

    protected final IUntypedDAO dao;
   

    @Nonnull
    @Override
    public IUntypedDAO getUntypedDAO() {
        return dao;
    }
    
    @Override
    public List<String> getStack() {
        if (stack == null) {
            stack = new ArrayList<>(3);
            stack.add(getClass().getCanonicalName());
        }
        return stack;
    }

    @Override
    public void setStack(List<String> stack) {
        this.stack = stack;
    }

    public PendingOperations(IUntypedDAO dao) {
        this.dao = dao;      
    }
   

    protected Map prepareCount(Long user) {
        final Map<String, Object> params = new HashMap<>(3);
        params.put("status", PendingCount.STATUS.ACTIVE.value());
        params.put("type", _type.getId());
        params.put("owner", user);
        return params;
    }

    /**
     * Saves a new counter
     *
     * @param count entity to be saved
     * @return true if was correctly saved
     */
    protected Boolean insertCounter(PendingCount count, @Nonnull ISecurityUser loggedUser) {
        if (count != null && count.getPendingCount() != 0) {
            final Long loggedUserId = SecurityRootUtils.getValidUserOfFirstAdmin(loggedUser.getId());
            count = getUntypedDAO().makePersistent(count, loggedUserId);
        }
        return count != null;
    }

    /**
     * updates counters of type
     *
     * @param type setPendingType to update counters
     * @param user
     * @param counter
     * @param module
     */
    protected void updateSpecificCounter(Long type, Long user, Integer counter, String module, @Nonnull ISecurityUser loggedUser) {
        disableCounter(user, type);
        PendingCount count = new PendingCount(module, counter, type, user);
        insertCounter(count, loggedUser);
    }

    /**
     * disable counter for determined type and user
     *
     * @param userId
     * @param typeId
     * @return
     */
    protected Integer disableCounter(Long userId, Long typeId) {
        String query = ""
                + " UPDATE " + PendingCount.class.getCanonicalName() + " c "
                + " SET c.status = :newStatus "
                + " WHERE c.status = :status "
                + " AND c.owner = :userId "
                + " AND c.type = :type";
        Map<String, Object> params = new HashMap<>();
        params.put("newStatus", PendingCount.STATUS.INACTIVE.value());
        params.put("status", PendingCount.STATUS.ACTIVE.value());
        params.put("userId", userId);
        params.put("type", typeId);
        return getUntypedDAO().HQL_updateByQuery(query, params, true, CacheRegion.APE_COUNT, 0);
    }

    /**
     * get type based on
     *
     * @param type
     * @return
     */
    protected final PendingType getType(APE type) {
        this.ape = type;
        PendingType t = Utilities.getApeInfo(type.getCode());
        if (t == null) {
            getLogger(LOGGER.APE).error("Invalid 'type' (code: '{}') configured to class {}", new Object[] {
                type.getCode(), this.getClass()
            });
        }
        return t;
    }

    /**
     * Get the list of users with pending related to a finding
     *
     * @param type
     * @return
     */
    protected Set<Long> involvedUsers(PendingType type) {
        Map params = new HashMap();
        params.put("status", PendingRecord.STATUS.ATTENDED.getValue());
        params.put("type", type.getId());
        List<PendingRecord> records = (List<PendingRecord>) getUntypedDAO().HQL_findByQuery(
                ApeConstants.GET_PENDING_BY_TYPE,
                params,
                false,
                null,
                0
        );
        Set<Long> users = new HashSet();
        for (PendingRecord record : records) {
            users.add(record.getOwner());
        }
        return users;
    }

    /**
     * updates counters of type
     *
     * @param type setPendingType to update counters
     */
    public void updateCountersByType(Long type, @Nonnull ISecurityUser loggedUser, Long ownerNewId) {
        disableCountersByType(type);
        insertCountersByType(type, loggedUser, ownerNewId);
    }

    /**
     * Saves a new counter
     *
     * @param type type of pending to insert counters
     */
    protected void insertCountersByType(Long type, @Nonnull ISecurityUser loggedUser, Long ownerNewId) {
        //Get a list of owners and counter for selected pending type
        String query = ""
                + " SELECT count(pnd) AS count_pnd"
                + ApeConstants.PENDING_RECORDS;
        Map params = new HashMap();
        params.put("status", PendingRecord.STATUS.ACTIVE.getValue()); // <-- ToDo: posible bug! no se están considerando pendientes escalados
        params.put("type", type);
        params.put("owner", ownerNewId);
        List<Long> pendings = getUntypedDAO().HQL_findByQuery(query, params);

        //iterate over list and insert pending count for each owner
        for (Long pending : pendings) {
            Integer count = pending.intValue();
            PendingCount counter = new PendingCount(this.getModuleKey(), count, type, ownerNewId);
            insertCounter(counter, loggedUser);
        }
    }

    /**
     * Disables counter by type
     *
     * @param typeId type of pending counter to be disabled
     * @return
     * @returnhow many counters were affected
     */
    protected Integer disableCountersByType(Long typeId) {
        String query = ApeConstants.UPDATE_DISABLE_COUNTERS_BY_TYPE;
        Map params = new HashMap();
        params.put("newStatus", PendingCount.STATUS.INACTIVE.value());
        params.put("status", PendingCount.STATUS.ACTIVE.value());
        params.put("type", typeId);
        return getUntypedDAO().HQL_updateByQuery(query, params, true, CacheRegion.APE_COUNT, 0);
    }

    @Override
    public ColumnDTO[] getBaseSummaryFields() {
        return baseSummaryFields;
    }

    @Override
    public ColumnDTO[] getModuleSummaryFields() {
        return moduleSummaryFields;
    }

    @Override
    public SummaryTemplate getSummaryTemplate() {
        return summaryTemplate;
    }

    @Override
    public ApeOperationType getOperationType() {
        return operationType;
    }

    public final void setPendingType(PendingType type) {
        this._type = type;
    }
   
    public final void setScope(PendingRecord.Scope scope) {
        this._scope = scope;
    }

    /**
     * @deprecated, no es necesario ya que el metodo "setType" lo genera automaticamente
     */
    public final void setModuleKey(String _module) {
        this._module = _module;
    }

    public String getModuleKey() {
        return _module;
    }

    public final void setBase(Class<? extends BaseAPE> _baseClass) {
        this._entityBase = _baseClass;
        this._base = _baseClass.getCanonicalName();
    }

    @Override
    public Class<? extends BaseAPE> getEntityBase() {
        return _entityBase;
    }

    public final void setOwnerField(String _userField) {
        this._userField = _userField;
        if (this._whereFilter == null) {
            this._whereFilter = " AND " + this._userField + " = :userId ";
        }
    }

    public final void setDeparmentField(String _departmentField) {
        this._departmentField = _departmentField;
    }

    public String getUserField() {
        return _userField;
    }

    public final void setCommitment(String _commitment) {
        this._commitment = _commitment;
    }
    
    public final void setBaseAlias(String _alias) {
        this._alias = _alias;
    }
    
    @Override
    public Integer updateCounterByUsers(Set<Long> users, @Nonnull ISecurityUser loggedUser) {
        if (users.isEmpty()) {
            return 0;
        }
        Integer total = 0;
        for (Long user : users) {
            Integer count = countByUser(user);
            total += count;
            updateSpecificCounter(_type.getId(), user, count, _module, loggedUser);
        }
        getLogger(LOGGER.APE).trace("counters of type {} were updated", _type.getCode());
        return total;
    }
    
    private Integer countByUser(Long ... userId) {
        final Map<String, Object> params = new HashMap<>(4);
        params.put("type", _type.getId());
        params.put("owner", Arrays.asList(userId));
        params.put("scope", _scope.getValue());
        Integer count = getUntypedDAO().HQL_findSimpleInteger(""
                + " SELECT COUNT(pnd.id) "
                + ApeConstants.PENDING_RECORDS
                + " AND pnd.scope = :scope ", 
                params,
                false,
                null,
                0
        );
        getLogger().info("User {} has {} pendings of type " + _type.getCode(), userId, count);
        return count;
    }

    @Override
    public Integer getCountByUser(Long user) {
        final Map<String, Object> params = prepareCount(user);
        Integer count = getUntypedDAO().HQL_findSimpleInteger(
                ApeConstants.SAVED_RECORDS_COUNT,
                params,
                true, 
                CacheRegion.APE_COUNT,
                0
        );
        getLogger().debug("User {} has {} saved pendings of type " + _type.getCode(), user, count);
        return count;
    }
    
    /**
     * @param loggedUser
     * @param trigger
     * @param recordId
     * @see IPendingOperation#recalculate(ISecurityUser loggedUser, Class trigger, Long recordId)
     */
    @Override
    public void recalculate(@Nonnull ISecurityUser loggedUser, Class trigger, Long ... recordId) {
        final PendingConditional conditional = new PendingConditional();
        conditional.setManuallyTraceEnabled(getIsManuallyTraceEnabled());
        conditional.setRefreshCommitment(true);
        conditional.setRefreshNoticeDate(getApe().isNotice());
        conditional.setRefreshReminderDate(getApe().isReminder());
        conditional.setRefreshDeadlineDate(getApe().isDeadline());
        conditional.setRefreshExpiredDate(getApe().isExpired());
        recalculate(loggedUser, trigger, conditional, recordId);
    }
    
    @Override
    public void recalculate(@Nonnull ISecurityUser loggedUser, Class trigger, PendingConditional conditional, Long ... recordId) {
        // genera los nuevos pendientes en la tabla
        Set<Long> users = new HashSet<>();
        SqlProperties sqlProps = getSqlProperties(recordId);
        updateRecord(loggedUser, users, trigger, sqlProps, conditional, recordId);
        // actualiza los contadores
        updateCounterByUsers(users, loggedUser);
        if (getLogger(LOGGER.APE).isDebugEnabled()) {
            getLogger(LOGGER.APE).debug("{} pendings of type {} were updated", new Object[] {users.size(), _type.getDescription()});
        }
    }
    
    /**
     * @see IPendingOperation#recalculateActiveAndAttended(ISecurityUser loggedUser, Class trigger)
     */
    @Override
    public void recalculateActiveAndAttended(@Nonnull ISecurityUser loggedUser, Class trigger) {
        final PendingConditional conditional = new PendingConditional();
        conditional.setManuallyTraceEnabled(getIsManuallyTraceEnabled());
        conditional.setSkipDependenciesUnchanged(true);
        conditional.setRefreshNoticeDate(getApe().isNotice());
        conditional.setRefreshReminderDate(getApe().isReminder());
        conditional.setRefreshDeadlineDate(getApe().isDeadline());
        conditional.setRefreshExpiredDate(getApe().isExpired());
        recalculate(loggedUser, trigger, conditional);
    }
    
    @Override
    public Set<Long> updateRecord(Class trigger, PendingConditional conditional, @Nonnull ISecurityUser loggedUser, Long ... recordId) {
        Set<Long> users = new HashSet<>();
        SqlProperties sqlProps = getSqlProperties(recordId);
        updateRecord(loggedUser, users, trigger, sqlProps, conditional, recordId);
        return users;
    }
    
    @Override
    public Set<Long> updateRecord(Long recordId, PendingConditional conditional, Class trigger, @Nonnull ISecurityUser loggedUser) {
        Set<Long> users = new HashSet<>();
        SqlProperties sqlProps = getSqlProperties(recordId);
        updateRecord(loggedUser, users, trigger, sqlProps, conditional, recordId);
        return users;
    }
    
    @Override
    public String getPendingSql() {
        return dao.getRowsSQL(_entityBase);
    }
    
    @Override
    public String getPendingSql(String appendEndSql) {
        return PendingHelper.getRowsSQL(dao.getEntityManager(), _entityBase, appendEndSql);
    }

    private SqlProperties getSqlProperties(Long ... recordId) {
        return getSqlPropertiesByRecord(recordId);
    }
    
    private SqlProperties getSqlPropertiesByRecord(Long ... recordId) {
        final String HQL = queryByRecord(recordId).toString();
        String sql = getUntypedDAO().hqlToSqlString(HQL);
        if (theresRecordId(recordId)) {
            sql = sql.replace("?", ":record");
        }
        final SqlProperties sqlProps = getSqlPropertiesBySQL(sql);
        if (theresRecordId(recordId)) {
            sqlProps.getParams().put("record", recordId);
        }
        return sqlProps;
    }
    
    private boolean theresNoRecordId(Long ... recordId) {
        return recordId == null || recordId.length == 0 || (
            recordId.length == 1 && recordId[0] == null
        );
    }
    
    private boolean theresRecordId(Long ... recordId) {
        return recordId != null && recordId.length > 0 && recordId[0] != null;
    }
    
    /**
     * Actualiza los registros de APE (ape_pending_records) a sus
     * valores correctos, realiza los siguientes pasos en este orden:
     *
     * 1.- Mantiene correcta la fecha de compromiso, solo se utiliza cuando se recalculan pendientes.
     * 2.- Incrementa contador de escalamiento, solo se utiliza cuando se recalculan pendientes desde el "daily run".
     * 3.- Marca pendientes como escaladados siempre y cuando se cumplan los días para escalarse.
     * 4.- Marca pendientes como atendidos cuando estos ya no aparecen en el query configurado para el pendiente.
     * 5.- Da de alta pendientes activos considerando si el mismo fue escalado.
     * 6.- Actualiza usuarios relacionados al pendiente reasignado.
     * 7.- Atiende dependencias ejecutando este mismo metodo "updateRecord" para cada una de las dependencias.
     *
     * @param loggedUser
     * @param users
     * @param recordId
     * @param trigger
     * @param sqlProps 
     */
    private void updateRecord(
            @Nonnull ISecurityUser loggedUser,
            Set<Long> users, 
            Class trigger,
            SqlProperties sqlProps, 
            PendingConditional conditional,
            Long ... recordId
    ) {
        String recordStringId;
        if (theresNoRecordId(recordId)) {
            recordStringId = "-7";
        }  else {
            recordStringId = ":record";
        }
        final PendingUpdater updater = new PendingUpdater(
                conditional,
                dependencies,
                ape,
                _type,
                _scope,
                _module,
                _base,
                getStack(),
                getImplementedModule(),
                this,
                dao
        );
        String recordExistsHQL = getRecordExistsHQL(recordStringId, String.valueOf(_type.getId()), String.valueOf(_scope.getValue())).toString();
        SqlQueryParser sqlHandler = new SqlQueryParser(loggedUser, trigger, sqlProps, dao, recordExistsHQL);
        //Mantiene correcta la fecha de compromiso, solo se utiliza cuando se recalculan pendientes
        updater.recordsRefreshCommitment(sqlHandler.getInsertPendingActiveFrom(), sqlProps);
        //Mantiene correcta la fecha de recordatorio, solo se utiliza cuando se envían correos diarios
        updater.recordsRefreshReminderDate(sqlHandler.getInsertPendingActiveFrom(), sqlProps);
        //Mantiene correcta la fecha de aviso, solo se utiliza cuando se recalculan pendientes
        updater.recordsRefreshNoticeDate(sqlHandler.getInsertPendingActiveFrom(), sqlProps);
        //Mantiene correcta la fecha limite, solo se utiliza cuando se recalculan pendientes
        updater.recordsRefreshDeadlineDate(sqlHandler.getInsertPendingActiveFrom(), sqlProps);
        updater.recordsRefreshExpiredDate(sqlHandler.getInsertPendingActiveFrom(), sqlProps);
        //Incrementa contador de escalamiento
        updater.recordsUnattended(sqlHandler.getUpdateToUnattendedFrom(), sqlProps);
        //Marca pendientes como escaladados
        updater.recordsEscalated(users, sqlHandler.getInsertPendingEscalatedFrom(), sqlProps, recordId);
        //Marca pendientes como atendidos
        int attended = updater.recordsAttended(loggedUser, users, sqlHandler.getUpdateToAttendedFrom(), sqlProps);
        //Da de alta pendientes activos considerando si el mismo fue escalado
        updater.recordsActive(trigger, users, sqlHandler.getInsertPendingActiveFrom(), sqlHandler.getExistsPendingSql(), sqlProps);
        //atiende dependencias
        updater.attendDependencies(loggedUser, attended, trigger, recordId);
    }
    
    private SqlProperties getSqlPropertiesBySQL(String sql) {
        SqlProperties sqlProps = new SqlProperties();
        fillSqlProperties(sqlProps, sql);
        sqlProps.setWith(Translate.getWith(sql));
        sqlProps.setFrom(" " + sql.substring(sql.indexOf(SqlQueryParser.FROM)));
        return sqlProps;
    }

    
    private void fillSqlProperties(SqlProperties sqlProps, String sql) {
        sqlProps.setSql(sql);
        String[] columns = Translate.getColumnsWithAliasTuples(sql);
        if (columns.length >= 6) {
            sqlProps.setOwnerColumn(columns[0].substring(0, columns[0].toLowerCase().indexOf(SqlQueryParser.AS)).trim());
            sqlProps.setRecordIdColumn(columns[1].substring(0, columns[1].toLowerCase().indexOf(SqlQueryParser.AS)).trim());  
            sqlProps.setCommitmentColumn(columns[2].substring(0, columns[2].toLowerCase().indexOf(SqlQueryParser.AS)).trim());   
            if (ape.isDeadline()) {
                sqlProps.setDeadlineColumn(columns[3].substring(0, columns[3].toLowerCase().indexOf(SqlQueryParser.AS)).trim());
            } else {
                sqlProps.setDeadlineColumn(SqlQueryParser.NULL);
            }
            if (ape.isNotice()) {
                sqlProps.setNoticeDateColumn(columns[4].substring(0, columns[4].toLowerCase().indexOf(SqlQueryParser.AS)).trim());
            } else {
                sqlProps.setNoticeDateColumn(SqlQueryParser.NULL);
            }
            if (ape.isReminder()) {
                sqlProps.setReminderColumn(columns[5].substring(0, columns[5].toLowerCase().indexOf(SqlQueryParser.AS)).trim());
            } else {
                sqlProps.setReminderColumn(SqlQueryParser.NULL);
            }
            if (ape.isExpired()) {
                sqlProps.setExpiredColumn(columns[6].substring(0, columns[6].toLowerCase().indexOf(SqlQueryParser.AS)).trim());
            } else {
                sqlProps.setExpiredColumn(SqlQueryParser.NULL);
            }
            sqlProps.setSuperOwnerColumn(SqlQueryParser.NULL);
        }
    }
    
    @Override
    public void dailyCheck(Class trigger, @Nonnull ILoggedUser loggedUser) {
        final PendingConditional conditional = new PendingConditional();
        conditional.setManuallyTraceEnabled(getIsManuallyTraceEnabled());
        conditional.setEscalate(getIsEscalateEnabled());
        conditional.setUnattended(getIsUnattendedCountEnabled());
        conditional.setRefreshNoticeDate(getApe().isNotice());
        conditional.setRefreshReminderDate(getApe().isReminder());
        conditional.setRefreshDeadlineDate(getApe().isDeadline());
        conditional.setRefreshExpiredDate(getApe().isExpired());
        conditional.setRefreshCommitment(true);
        final Set<Long> users = updateRecord(trigger, conditional, loggedUser, new Long[]{});
        updateCounterByUsers(users, loggedUser);
    }

    protected void filterOwner(StringBuilder query, Long owner) {
        if (owner != null) {
            query.append(_whereFilter);
            query.replace(0, query.length(), ApeConstants.PARAM_PATTERN_USER_ID.matcher(query).replaceAll(String.valueOf(owner)));
        }
    }

    protected PendingType getType() {
        return _type;
    }

    /**
     * @deprecated, no es necesario ya que el metodo "setType" lo genera automaticamente
     * @param _whereFilter
     */
    public final void setOwnerFieldFilter(String _whereFilter) {
        this._whereFilter = _whereFilter;
    }

    public final void setQuery(String query) {
        this._query = query;
    }

    @Override
    public StringBuilder queryByUser(ApeSelectType type, Long user) {
        StringBuilder query = select(type, _userField);
        query.append(_query);
        filterOwner(query, user);
        return query;
    }
    
    @Override
    public StringBuilder queryByRecord(Long ... record) {
        StringBuilder hql = new StringBuilder(" ");
        hql
            .append(SqlQueryParser.SELECT)      
            .append(_userField).append(SqlQueryParser.AS).append(SqlProperties.USER_ID_ALIAS).append(SqlQueryParser.COMMA)
            .append(_alias).append(".id").append(SqlQueryParser.AS).append(SqlProperties.ALIASES).append(SqlQueryParser.COMMA)
            .append(_commitment).append(SqlQueryParser.AS).append(SqlProperties.COMMITMENT_ALIAS).append(SqlQueryParser.COMMA)
            .append(_deadline).append(SqlQueryParser.AS).append(SqlProperties.DEADLINE_DATE_ALIAS).append(SqlQueryParser.COMMA)
            .append(_noticeDate).append(SqlQueryParser.AS).append(SqlProperties.NOTICE_DATE_ALIAS).append(SqlQueryParser.COMMA)
            .append(_reminder).append(SqlQueryParser.AS).append(SqlProperties.REMINDER_DATE_ALIAS).append(SqlQueryParser.COMMA)
            .append(_expired).append(SqlQueryParser.AS).append(SqlProperties.EXPIRED_DATE_ALIAS).append(SqlQueryParser.COMMA)
            .append(_userField)
            .append(SqlQueryParser.AS).append(SqlProperties.SUPER_OWNER_ALIAS)
            .append(_query)
        ;
        filterRecord(hql, record);
        return hql;
    }

    @Override
    public StringBuilder queryNoFilter() {
        StringBuilder hql = new StringBuilder(" ");
        hql
            .append(SqlQueryParser.SELECT)
            .append(_userField).append(SqlQueryParser.COMMA)
            .append(_alias).append(".id").append(SqlQueryParser.COMMA)
            .append(_commitment)
            .append(SqlQueryParser.COMMA).append(_deadline).append(" as ").append(SqlProperties.DEADLINE_DATE_ALIAS)
            .append(SqlQueryParser.COMMA).append(_noticeDate).append(" as ").append(SqlProperties.NOTICE_DATE_ALIAS)
            .append(SqlQueryParser.COMMA).append(_reminder).append(" as ").append(SqlProperties.REMINDER_DATE_ALIAS)
            .append(SqlQueryParser.COMMA).append(_expired).append(" as ").append(SqlProperties.EXPIRED_DATE_ALIAS)
            .append(_query)
        ;
        return hql;
    }
    
    @Override
    public String filterRecordsByUser(Long owner, String alias) {
        return filterRecordsByUser(owner, alias, null).toString();
    }
    
    @Override
    public StringBuilder filterRecordsByUser(Long owner, String alias, String userField) {
        StringBuilder query = getRecordExistsHQL(alias + ".id", String.valueOf(_type.getId()), String.valueOf(_scope.getValue()));
        if (userField != null) {
            query.append(" AND pnd.owner = ").append(userField).append(
                PARAM_PATTERN_OWNER.matcher(" AND pnd.owner = :owner").replaceAll(String.valueOf(owner))
            );
        } else {
            query.append(
                PARAM_PATTERN_OWNER.matcher(" AND pnd.owner = :owner").replaceAll(String.valueOf(owner))
            );
        }
        query.insert(0, " EXISTS (");
        query.append(")");
        return query;
    }
    
    @Override
    public StringBuilder filterSourceRecordsByUser(Long owner, String alias) {
        StringBuilder hql = new StringBuilder(" EXISTS (");
        hql.append(queryByUser(ApeSelectType.BY_ID, owner));
        hql.append(" AND ").append(alias).append(".id  = ").append(_alias).append(".id");
        hql.append(")");
        return hql;
    }

    @Override
    public StringBuilder filterRecordsByRecordId(Long owner, String recordId) {
        StringBuilder query = getRecordExistsHQL(recordId, String.valueOf(_type.getId()), String.valueOf(_scope.getValue()));
        query.append(
            PARAM_PATTERN_OWNER.matcher(" AND pnd.owner = :owner").replaceAll(String.valueOf(owner))
        );
        query.insert(0, " EXISTS (");
        query.append(")");
        return query;
    }
    
    private StringBuilder getRecordExistsHQL(String recordId, String typeId, String scopeValue) {
        StringBuilder query = new StringBuilder(ApeConstants.RECORDS_EXISTS);
        query.replace(0, query.length(), PARAM_PATTERN_TYPE.matcher(query.toString()).replaceAll(typeId));
        query.replace(0, query.length(), PARAM_PATTERN_SCOPE.matcher(query.toString()).replaceAll(scopeValue));
        query.replace(0, query.length(), PARAM_PATTERN_RECORD_ID.matcher(query.toString()).replaceAll(recordId));
        return query;
    }

    protected StringBuilder select(ApeSelectType type, String field) {
        StringBuilder query;
        switch (type) {
            case BY_ID:
                query = new StringBuilder(ApeConstants.PARAM_PATTERN_ALIAS.matcher(ApeConstants.SELECT_ID).replaceAll(_alias));
                break;
            case BY_DTO:
                query = new StringBuilder(ApeConstants.PARAM_PATTERN_USER_ID.matcher(ApeConstants.SELECT_DTO).replaceAll(field));
                query.replace(0, query.length(), ApeConstants.PARAM_PATTERN_ALIAS.matcher(query.toString()).replaceAll(_alias));
                break;
            case BY_ENTITY:
                query = new StringBuilder(ApeConstants.PARAM_PATTERN_ALIAS.matcher(ApeConstants.SELECT_ENTITY).replaceAll(_alias));
                break;
            default:
                query = new StringBuilder(30);
        }
        return query;
    }

    protected void filterRecord(StringBuilder query, Long ... recordId) {
        if (theresRecordId(recordId)) {
            if (recordId.length == 1) {
                query.append(" AND ").append(_alias).append(".id = :record");
            } else {
                query.append(" AND ").append(_alias).append(".id IN (:record)");
            }
        }
    }
    
    @Override
    public Module getImplementedModule() {
        return null;
    }

    @Override
    public boolean hasImplementedModule() {
        return getImplementedModule() != null;
    }
    /**
     * Se colocan los pendientes de los cuales se ejecutará su metodo "updateRecord"
     * justo despues de haber atendido el pendiente actual.
     *
     * @param dependencies
     */
    public final void setDependencies(Class<? extends IPendingOperation> ... dependencies) {
        this.dependencies = dependencies;
    }
    
    public final void clearDependencies() {
        this.dependencies = null;
    }
    
    public final void setBaseSummaryFields(ColumnDTO ... baseSummaryFields) {
        this.baseSummaryFields = baseSummaryFields;
    }
    
    public final void setModuleSummaryFields(ColumnDTO ... moduleSummaryFields) {
        this.moduleSummaryFields = moduleSummaryFields;
    }

    public final void setSummaryTemplate(String summaryTemplate) {
        this.summaryTemplate.setTemplate(summaryTemplate);
    }
    
    public final void setSummaryTemplate(String summaryTemplate, int count) {
        this.summaryTemplate.setTemplate(summaryTemplate);
        this.summaryTemplate.setCount(count);
    }

    public final void setOperationType(ApeOperationType operationType) {
        this.operationType = operationType;
    }
    
    private StringBuilder getPossibleResponsibleHQL(final Long recordId) {
        final String services = ape.getRequiredServicesCodes("pe");
        final String servicesFilter = IUserDAO.USER_BY_PROFILE.replace(":profileCodes", services);
        final String filter;
        if (_departmentField != null
                && ape.getRequiredServices().contains(ProfileServices.SPECIAL_DEPARTMENT_ATTENDANT)) {
            final String departmentAttendantFilter = ""
                    + " EXISTS ("
                        + " SELECT 1"
                        + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " bc "
                        + " WHERE bc.attendantId = c.id"
                        + " AND bc.id IN ("
                            + " SELECT bs." + _departmentField
                            + " FROM " + _base + " bs"
                            + " WHERE bs.id = " + recordId
                        + " )"
                    + " )";
            filter = servicesFilter + SqlQueryParser.OR  + departmentAttendantFilter;
        } else {
            filter = servicesFilter;
        }
        final StringBuilder sb = new StringBuilder(""
            + " SELECT c").append(""
            + " FROM ").append(UserRef.class.getCanonicalName()).append(" c "
            + " WHERE (").append(filter).append(")");
        return sb;
    }

    @Override
    public List<IUser> getPossibleResponsibleList(final Long recordId) {
        return dao.HQL_findByQuery(getPossibleResponsibleHQL(recordId).toString()+ " order by c.description ");
    }

    @Override
    public List<IUser> getPossibleResponsibleList(
            final Long recordId,
            final IBusinessUnitDepartment businessUnitDepartment
    ) {
        return dao.HQL_findByQuery(
            getPossibleResponsibleHQL(recordId).append(SqlQueryParser.AND).append(
                IUserDAO.USER_BY_BUSINESS_UNIT_DEPARTMENT.replace(":businessUnitDepartmentIds", businessUnitDepartment.getId().toString())
            ).toString()+ " order by c.description "
        );
    }

    @Override
    public List<IUser> getPossibleResponsibleList(final Long recordId, final IBusinessUnit businessUnit) {
        return dao.HQL_findByQuery(
            getPossibleResponsibleHQL(recordId).append(SqlQueryParser.AND).append(
                IUserDAO.USER_BY_BUSINESS_UNIT.replace(":businessUnitIds", businessUnit.getId().toString())
            ).toString()+ " order by c.description "
        );
    }
    
    @Override
    public List<IUser> getPossibleResponsibleList(final Long recordId, final Long currentUserId) {
        return addCurrentPossibleResponsible(
            currentUserId, 
            dao.HQL_findByQuery(getPossibleResponsibleHQL(recordId).toString()+ " order by c.description ")
        );
    }
    
    private List<IUser> addCurrentPossibleResponsible(Long currentUserId, List<IUser> userList) {
        if (currentUserId != null && currentUserId != -1L) {
            UserRef currentUser = new UserRef(currentUserId);
            if (!userList.contains(currentUser)) {
                currentUser.setDescription(dao.HQL_findSimpleString(""
                    + " SELECT c.description "
                    + " FROM " + UserRef.class.getCanonicalName() + " c"
                    + " WHERE c.id = :currentUserId", "currentUserId", currentUserId
                ));
                userList.add(currentUser);
            }
        }
        return userList;
    }

    @Override
    public List<IUser> getPossibleResponsibleList(
            final Long recordId, 
            final Long currentUserId, 
            final IBusinessUnitDepartment businessUnitDepartment
    ) {
        return addCurrentPossibleResponsible(
            currentUserId, 
            dao.HQL_findByQuery(
                getPossibleResponsibleHQL(recordId).append(SqlQueryParser.AND).append(
                    IUserDAO.USER_BY_BUSINESS_UNIT_DEPARTMENT.replace(":businessUnitDepartmentIds", businessUnitDepartment.getId().toString())
                ).toString()+ " order by c.description "
            )
        );
    }

    @Override
    public List<IUser> getPossibleResponsibleList(
            final Long recordId,
            final Long currentUserId, 
            final IBusinessUnit businessUnit
    ) {
        return addCurrentPossibleResponsible(
            currentUserId, 
            dao.HQL_findByQuery(
                getPossibleResponsibleHQL(recordId).append(SqlQueryParser.AND).append(
                    IUserDAO.USER_BY_BUSINESS_UNIT.replace(":businessUnitIds", businessUnit.getId().toString())
                ).toString()+ " order by c.description "
            )
        );
    }
    
    @Override
    public Long getTypeId() {
        if (_type == null) {
            getLogger(LOGGER.APE).error("Missing pending type!");
            return null;
        }
        return _type.getId();
    }
    
    @Override
    public APE getApe() {
        return this.ape;
    }
    
    @Override
    public List<MailDTO> getReminderMail() {
        if (Utilities.isMailSendingOff(this)) {
            getLogger(LOGGER.APE).error("The application is not sending mails (is turned off).");
            return Utilities.EMPTY_LIST;
        }
        String sql = getPendingSql(" AND record.status <> " + PendingRecord.STATUS.ATTENDED.getValue());
        return getMailHelper().getReminderMail(this, sql, ape.isDeadline());
    }
    
    @Override
    public List<MailDTO> getNoticeMail() {
        if (Utilities.isMailSendingOff(this)) {
            getLogger(LOGGER.APE).error("The application is not sending mails (is turned off).");
            return Utilities.EMPTY_LIST;
        }
        if (!ape.isNotice()) {
            getLogger(LOGGER.APE).error("The pending {} does not have notices mails (is turned off).", ape.getCode());
            return Utilities.EMPTY_LIST;
        }
        String sql = getPendingSql(" AND record.status <> " + PendingRecord.STATUS.ATTENDED.getValue());
        return getMailHelper().getNoticeMail(this, sql);
    }
    
    public List<MailDTO> getDeadlineMail() {
        if (Utilities.isMailSendingOff(this)) {
            getLogger(LOGGER.APE).error("The application is not sending mails (is turned off).");
            return Utilities.EMPTY_LIST;
        }
        if (!ape.isDeadline()) {
            getLogger(LOGGER.APE).error("The pending {} does not have deadline mails (is turned off).", ape.getCode());
            return Utilities.EMPTY_LIST;
        }
        String sql = getPendingSql(" AND record.status <> " + PendingRecord.STATUS.ATTENDED.getValue());
        return getMailHelper().getDeadlineMail(this, sql);
    }

    public final void setNoticeDate(String noticeDate) {
        this._noticeDate = noticeDate;
    }

    public final void setDeadline(String deadline) {
        this._deadline = deadline;
    }
    
    public String getExpired() {
        return _expired;
    }

    public final void setExpired(String _expired) {
        this._expired = _expired;
    }
    
     public final void setReminder(String reminder) {
        this._reminder = reminder;
    }

    public final void setType(APE type) {
        if (this._type == null) {
            this._type = getType(type);
        } else {
            PendingType t = getType(type);
            if (!t.equals(this._type)) {
                getLogger(LOGGER.APE).error("Incongruent TYPE setted! '{}' vs '{}'", new Object[] {
                    this._type, t
                });
            }
        }
        if (this._module == null) {
            this._module = type.getModule().getKey();
        } else if (!this._module.equals(type.getModule().getKey())) {
            getLogger(LOGGER.APE).error("Incongruent MODULE setted! '{}' vs '{}'", new Object[] {
                this._module, type.getModule().getKey()
            });
        }
    }    
    
    @Override
    public LinkedHashMap<String,MailColumn> getDailyMailColumns() {
        return listExtraFields;
    }
  
    @Override
    public final void setExtraFieldsMap(LinkedHashMap<String,MailColumn> listExtraFields) {
        this.listExtraFields = listExtraFields;
    }

    @Override
    public List<String> getExtraMails() {
        return extraMails;
    }

    @Override
    public final void setExtraMails(List<String> extraMails) {
        this.extraMails = extraMails;
    }
    
    @Override
    public final void setExtraMails(String ... extraUsers) {
        this.extraMails = Lists.newArrayList(extraUsers);
    }


    @Override
    public Set<ColumnDTO> getLocalizedFields() {
        final Set<ColumnDTO> localized = new HashSet<>();
        for (final ColumnDTO field : baseSummaryFields) {
            if (field.getLocaleKeys() != null) {
                localized.add(field);
            }
        }
        for (final ColumnDTO field : moduleSummaryFields) {
            if (field.getLocaleKeys() != null) {
                localized.add(field);
            }
        }
        return localized;
    }

    @Override
    public boolean isPendingActive(Long recordId) {
        return getUntypedDAO().HQL_findLong(""
            + " SELECT count(pnd) "
            + " FROM ape_pending_record pnd"
            + " WHERE "
                + " pnd.type = " + _type.getId()
                + " AND pnd.status IN ( "
                    + PendingRecord.STATUS.ACTIVE.getValue()
                    + "," + PendingRecord.STATUS.ESCALATED.getValue()
                    + "," + PendingRecord.STATUS.REASSIGNED.getValue()
                + " )"
                + " AND pnd.record_id = :id", "id", recordId
        ) > 0;
    }

    @Override
    public int updateActive(String _base, Map<String, Object> fields, Long ... recordId) {
        StringBuilder hqlBuilder = new StringBuilder(300);
        hqlBuilder.append(""
            + " UPDATE ").append(_base).append(""
            + " SET "
        );
        fields.entrySet().stream().map((field) -> field.getKey()).forEachOrdered((key) -> {
            hqlBuilder.append(key).append(" = :").append(key);
        });
        hqlBuilder.append(" WHERE ");
        if (recordId.length > 0) {
            hqlBuilder.append(Utilities.arrayToSelectInFullCondition(recordId, "id"));
        } else {
            hqlBuilder.append(""
                + " id IN ("
                    + " SELECT pending.recordId"
                    + " FROM ").append(PendingRecord.class.getCanonicalName()).append(" pending "
                    + " WHERE"
                        + " pending.type = ").append(_type.getId()).append(""
                        + " AND pending.status IN ( "
                            + "").append(PendingRecord.STATUS.ACTIVE.getValue()).append(""
                            + ",").append(PendingRecord.STATUS.ESCALATED.getValue()).append(""
                            + ",").append(PendingRecord.STATUS.REASSIGNED.getValue()).append(""
                        + " )"
                + " )"
            );
        }
        return getUntypedDAO().HQL_updateByQuery(hqlBuilder.toString(), fields);
    }
   
    @Override
    public Long getPendingRecordIdFromRecordId(@Nonnull ISecurityUser loggedUser, Long recordId, String... typeCodes) {
        final Map<String, Object> params = new HashMap<>(3);
        if (typeCodes.length == 0) {
            typeCodes = new String[]{this._type.getCode()};
        }
        final String query;
        List<String> typeCodesAr = Arrays.stream(typeCodes).filter(s -> (s != null && !s.equals(""))).collect(Collectors.toList());
        if (typeCodesAr.isEmpty()) {
            query = ApeConstants.PENDING_RECORDS;
        } else {
            query = ApeConstants.PENDING_RECORDS.replace(
                    ApeConstants.PENDING_RECORDS_TYPE_CONDITION,
                    " AND " + BindUtil.bindFilterList("ptype.code", typeCodesAr, false, params)
            );
        }
        params.put("owner", loggedUser.getId());
        params.put("recordId", recordId);
        return dao.HQL_findLong(""
                + " SELECT pnd.id "
                + query
                + " AND pnd.recordId = :recordId "
                + " AND pnd.deleted = 0 ",
                params,
                false,
                null,
                0
        );
    }

    @Override    
    public String getNameEscalateMailOverride(PendingMailerType type) {
        return null;
    }

    @Override
    public String getSubjectEscalateMailOverride(PendingMailerType type) {
        return null;
    }

    @Override
    public String getSubjectEscalateMailOverride(PendingMailerType type, IEscalableDTO record) {
        return null;
    }

    @Override
    public String getMessageTitleEscalateMailOverride(PendingMailerType type, IEscalableDTO record) {
        return null;
    }

    @Override
    public PendingMailerMode getPendingMailerMode(PendingMailerType type) {
        return PendingMailerMode.BY_TYPE;
    }
    
    @Override
    public String getMailRecordUrl() {
        return null;
    }
        
    @Override
    public IEscalableDTO getEscalableRecord(
            final Object[] temp,
            final LinkedHashMap<String, MailColumn> extraColumns,
            final ResourceBundle tags,
            final IPendingOperation pendingOperation,
            final ILoggedUser loggedUser
    ){
        return getMailHelper().getEscalableRecord(temp, extraColumns, tags, pendingOperation, loggedUser);
    }
    
    @Override
    public ResourceBundle getEscalationMailerTags() {
        return LocaleUtil.getSystemI18n("qms.escalation.mail.EscalationMailer");
    }
    
    @Override
    public boolean getRemoveRows() {
        return false;
    }

    public Boolean getIsManuallyTraceEnabled() {
        return isManuallyTraceEnabled;
    }

    public void setIsManuallyTraceEnabled(Boolean isManuallyTraceEnabled) {
        this.isManuallyTraceEnabled = isManuallyTraceEnabled;
    }

    public boolean getIsEscalateEnabled() {
        return isEscalateEnabled;
    }

    public void setEscalateEnabled(boolean escalateEnabled) {
        isEscalateEnabled = escalateEnabled;
    }

    public boolean getIsUnattendedCountEnabled() {
        return isUnattendedCountEnabled;
    }

    public void setUnattendedCountEnabled(boolean unattendedCountEnabled) {
        isUnattendedCountEnabled = unattendedCountEnabled;
    }

    @Override
    public String getPathDailyMailTemplate() {
        return EscalableDTO.class.getPackage().getName() + ".PendingMailerTemplate";
    }
    
    @Override
    public String getDailyMailQuery() {
        return dailySendMailQuery;
    }
    
    @Override
    public final void setDailyMailQuery(String dailyMailQuery) {
        this.dailySendMailQuery = dailyMailQuery;
    }

    @Override
    public MailHelper getMailHelper() {
        if (_mailHelper == null) {
            _mailHelper = new MailHelper(dao);
        }
        return _mailHelper;
    }

    @Override
    public final void setMailHelper(MailHelper _mailHelper) {
        this._mailHelper = _mailHelper;
    }

}
