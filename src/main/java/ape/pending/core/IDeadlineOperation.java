package ape.pending.core;

import java.util.List;
import qms.framework.mail.MailDTO;

/**
 *
 * <AUTHOR>
 */
public interface IDeadlineOperation {

    /**
     * El "deadline" es la última fecha disponible para atender el
     * pendiente, una vez sobre pasada el pendiente se escala.
     *
     * Ejemplo:
     * Para actividades se calcula a partir de la fecha de creación más la
     * cantidad de días limite abiertos
     */
    public List<MailDTO> getDeadlineMail();
    public APE getApe();
    
}
