
package ape.pending.core;

import Framework.DAO.IUntypedDAO;
import java.util.Map;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.util.ModuleUtil;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 * @param <T>
 */
public abstract class PendingHandlerService<T> extends PendingService<T> implements IPendingHandler, IPendingCountByType {

    private static final long serialVersionUID = 1L;

    @Override
    public abstract Map<String, Integer> getPendingMap();

    @SMDMethod
    @Override
    public Integer getPendingCount(String moduleName, String pendingCamelName) {
        APE pendingType = ModuleUtil.getApeFromModule(moduleName, pendingCamelName);
        return getPendingCount(this, pendingType, getUntypedDAO());
    }

    @Override
    public abstract Integer countTypeByUser(Long loggedUserId, APE pendingType, IUntypedDAO dao);
    

}
