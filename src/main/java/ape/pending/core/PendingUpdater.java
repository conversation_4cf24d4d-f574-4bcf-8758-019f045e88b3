package ape.pending.core;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.dto.PendingConditional;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import ape.pending.util.ApeConstants;
import ape.pending.util.SqlProperties;
import ape.pending.util.SqlQueryParser;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import mx.bnext.access.Module;
import mx.bnext.core.security.ISecurityUser;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import qms.escalation.bean.IGenericEscalationBean;

public class PendingUpdater extends Loggable {
    
    private final PendingConditional conditional;
    private final Class<? extends IPendingOperation>[] dependencies;
    private final APE ape;
    private final PendingType type;
    private final PendingRecord.Scope scope;
    private final String module;
    private final String base;
    private final List<String> stack;
    private final IUntypedDAO dao;
    private final Module implementedModule;
    private final IPendingOperation escalationBean;

    public PendingUpdater(
            PendingConditional conditional,
            Class<? extends IPendingOperation>[] dependencies,
            APE ape,
            PendingType type,
            PendingRecord.Scope scope,
            String module,
            String base,
            List<String> stack,
            Module implementedModule,
            IPendingOperation escalationBean,
            IUntypedDAO dao
    ) {
        this.conditional = conditional;
        this.dependencies = dependencies;
        this.ape = ape;
        this.type = type;
        this.scope = scope;
        this.module = module;
        this.base = base;
        this.stack = stack;
        this.implementedModule = implementedModule;
        this.escalationBean = escalationBean;
        this.dao = dao;
    }

    private Boolean isEscalableDisabled() {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", type.getId());
        Long valid = dao.SQL_findSingleLong(
                "SELECT COUNT(1) FROM ape_pending_type t WHERE t.escalation_enabled = 1 AND pending_type_id = :id",
                params
        );
        return valid == null || valid.equals(0L);
    }
    
    private boolean theresNoRecordId(Long ... recordId) {
        return recordId == null || recordId.length == 0 || (
            recordId.length == 1 && recordId[0] == null
        );
    }
    
    public void recordsEscalated(Set<Long> users, String insertPendingEscalatedFrom, SqlProperties sqlProps, Long ... recordId) {
        if (!conditional.isEscalate()) {
            return;
        }
        Boolean escalableDisabled = isEscalableDisabled(); 
        if (escalableDisabled) {
            getLogger(Loggable.LOGGER.APE).debug("Escalation is disabled here! {}", implementedModule);
            return;
        }
        getLogger(Loggable.LOGGER.APE).trace("Escalation is enabled here!");
        //ToDo: Solo funciona en SQL SERVER
        int insertedToEscalated = insertRecord(users, getClass(), ApeConstants.INSERT_SELECT_ESCALATE, insertPendingEscalatedFrom, sqlProps);
        if (insertedToEscalated > 0 || conditional.isManuallyTraceEnabled()) {
            if (conditional.isManuallyTraceEnabled() || getLogger(Loggable.LOGGER.APE).isDebugEnabled()) {
                getLogger(Loggable.LOGGER.APE).debug("{} pendings were inserted as ESCALATED status!, {}", new Object[] {
                    insertedToEscalated, implementedModule
                });
            }
        } else {
            //Si inserta pendientes como ESCALATED no tiene caso revisar si habrá historia debido a que no debe de haber
            return;
        }
        if (sqlProps.getWith() != null && !insertPendingEscalatedFrom.contains(sqlProps.getWith())) {
            sqlProps.startAppend(" WITH ").append(sqlProps.getWith()).append(" ");
        } else {
            sqlProps.startAppend("");
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with)
                .append(""
            + " UPDATE pending SET "
                + " pending.status = ").append(PendingRecord.STATUS.HISTORY.getValue()).append(" "
                + " ,pending.last_updated = current_timestamp "
                + " ,pending.escalated_on = current_timestamp "
                + " ,pending.escalation_mode = apt.escalation_mode "
                + " ,pending.escalated_to = boses.boss_id ").append(insertPendingEscalatedFrom);
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(
                sqlProps,
                "select pending.pending_record_id, pending.owner as owner_id, pending.record_id as record_id, pending.unattended_days, apt.escalation_days" + insertPendingEscalatedFrom
        );
        final Integer added = addUserSet(sqlProps, users, "pending.owner", insertPendingEscalatedFrom);
        int updatedToHistory;
        if (added > 0 || conditional.isManuallyTraceEnabled()) {
            updatedToHistory = dao.SQL_updateByQuery(
                    updatePendingSql,
                    sqlProps.getParams(),
                    0,
                    Arrays.asList("ape_pending_record")
            );
        } else {
            updatedToHistory = 0;
        }
        if (updatedToHistory > 0 || conditional.isManuallyTraceEnabled()) {
            if (conditional.isManuallyTraceEnabled() || getLogger(Loggable.LOGGER.APE).isDebugEnabled()) {
                getLogger(Loggable.LOGGER.APE).debug("{} pendings were setted to HISTORY status!, {}", new Object[] {
                    updatedToHistory, implementedModule
                });
            }
            Utilities.getBean(IGenericEscalationBean.class).triggerEscalationMails(escalationBean);
        }
        if (updatedToHistory != insertedToEscalated) {
            if (insertedToEscalated < updatedToHistory) {
                final Integer sameBossCount;
                if (theresNoRecordId(recordId)) {
                    final Map<String, Object> params = new HashMap<>(1);
                    params.put("typeId", type.getId());
                    sameBossCount = dao.SQL_updateByQuery(""
                        + " UPDATE apr"
                        + " SET apr.pre_owner_id = NULL"
                        + " FROM ape_pending_record apr"
                        + " WHERE "
                            + " apr.type = :typeId"
                            + " AND apr.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                            + " AND apr.escalation_from_count > 1", 
                        params,
                        0,
                        Arrays.asList("ape_pending_record")
                    );
                }  else {
                    final Map<String, Object> params = new HashMap<>(2);
                    params.put("recordId", StringUtils.join(recordId, ","));
                    params.put("typeId", type.getId());
                    sameBossCount = dao.SQL_updateByQuery(""
                        + " UPDATE apr"
                        + " SET apr.pre_owner_id = NULL"
                        + " FROM ape_pending_record apr"
                        + " WHERE "
                            + " apr.record_id IN (:recordId)"
                            + " AND apr.type = :typeId"
                            + " AND apr.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                            + " AND apr.escalation_from_count > 1", 
                        params,
                        0,
                        Arrays.asList("ape_pending_record")
                    );
                }
                getLogger(Loggable.LOGGER.APE).warn("[{} != {}] {} were setted to HISTORY status but {} were escalated as new pending records. {} has the same boss.", new Object[] {
                    updatedToHistory, insertedToEscalated, updatedToHistory, insertedToEscalated, sameBossCount
                });
            } else {
                getLogger(Loggable.LOGGER.APE).error("Incongruent escalation process! [{} < {}] {} pendings were setted to HISTORY status but {} were escalated as new pending records", new Object[] {
                    updatedToHistory, insertedToEscalated, updatedToHistory, insertedToEscalated
                });
            }
        }
    }
    
    public void recordsRefreshCommitment(String pendingFrom, SqlProperties sqlProps) {
        if (!conditional.isRefreshCommitment()) {
            return;
        }
        if (SqlQueryParser.CURRENT_DATE.equals(sqlProps.getCommitmentColumn())) {
            getLogger(Loggable.LOGGER.APE).trace("Commitment date changes are unavailabe for the pending.");
            return;
        }
        getLogger(Loggable.LOGGER.APE).trace("Refreshing commitment date.");
        //ToDo: Solo funciona en SQL SERVER
        sqlProps.startAppend(""
            + " WITH commitment_dates as ("
                + " SELECT "
                    + " pending.commitment_date new_commitment_date, "
                    + " apr.commitment_date old_commitment_date, "
                    + " apr.pending_record_id").append(pendingFrom).append(""
                + " JOIN ape_pending_record apr ON"
                    + " apr.STATUS in (")
                        .append(PendingRecord.STATUS.ACTIVE.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.REASSIGNED.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.ESCALATED.getValue()).append(""
                    + " )"
                    + " AND apr.record_id = pending.record_id "
                    + " AND apr.OWNER = pending.OWNER "
                    + " AND apr.type = ").append(type.getId()).append(""
                + " WHERE"
                    + " pending.").append(SqlProperties.COMMITMENT_DATE_ALIAS).append(" != apr.commitment_date"
                    + " OR ("
                            + " apr.commitment_date IS NULL"
                            + " AND pending.").append(SqlProperties.COMMITMENT_DATE_ALIAS).append(" IS NOT NULL "
                    + " )"
            + " )"
        );
        if (sqlProps.getWith() != null) {
            final String previousWith = sqlProps.getSbString();
            if (!previousWith.contains(sqlProps.getWith())) {
                sqlProps.startAppend(previousWith).append(",").append(sqlProps.getWith()).append(" ");
            }
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with).append(""
            + " UPDATE ape SET commitment_date = new_date.new_commitment_date"
            + " FROM ape_pending_record ape"
            + " JOIN commitment_dates new_date ON new_date.pending_record_id = ape.pending_record_id"
        );
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(
                sqlProps,
                with + "SELECT new_commitment_date, old_commitment_date, pending_record_id FROM commitment_dates"
        );
        int n = dao.SQL_updateByQuery(
            updatePendingSql,
            sqlProps.getParams(),
            0,
            Arrays.asList("ape_pending_record")
        );
        if (conditional.isManuallyTraceEnabled() || (n > 0 && getLogger(Loggable.LOGGER.APE).isDebugEnabled())) {
            getLogger(Loggable.LOGGER.APE).debug("{} pendings were updated their COMMITMENT date!, {}", new Object[] {
                n, implementedModule
            });
        }
        
    }
    
    public void recordsUnattended(String updateUnattendedFrom, SqlProperties sqlProps) {
        if (!conditional.isUnattended()) {
            return;
        }
        //ToDo: Solo funciona en SQL SERVER
        sqlProps.startAppend(ApeConstants.UNATTENDED_DAYS_WITH);
        if (sqlProps.getWith() != null && !updateUnattendedFrom.contains(sqlProps.getWith())) {
            final String previousWith = sqlProps.getSbString();
            sqlProps.startAppend(previousWith).append(",").append(sqlProps.getWith()).append(" ");
        }
        String with = sqlProps.getSbString();                
        sqlProps.startAppend(with).append(" "
                + " UPDATE pending SET unattended_days = case when u.unattended_days < 0 then 0 else u.unattended_days end"
                + " ").append(updateUnattendedFrom);
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(
                sqlProps, 
                ApeConstants.UNATTENDED_DAYS_WITH + "select pending.pending_record_id, pending.owner as owner_id, pending.record_id as record_id" + updateUnattendedFrom
        );
        int n = dao.SQL_updateByQuery(
            updatePendingSql,
            sqlProps.getParams(),
            0,
            Arrays.asList("ape_pending_record")
        );
        if (conditional.isManuallyTraceEnabled() || (n > 0 && getLogger(Loggable.LOGGER.APE).isDebugEnabled())) {
            getLogger(Loggable.LOGGER.APE).debug("{} pendings were increased their UNATTENDED days!, {}", new Object[] {
                n, implementedModule
            });
        }
    }
    
    public void recordsActive(Class trigger, Set<Long> users, String pendingFrom, String existsPendingSql, SqlProperties sqlProps) {
        //Agrega la condición para evitar realizar INSERTs repetidos
        sqlProps.startAppend(pendingFrom).append(""
            + " WHERE not exists (")
                .append(existsPendingSql)
                .append(SqlQueryParser.AND).append("pending.").append(SqlProperties.OWNER_ALIAS).append(" = apr.owner")
                .append(SqlQueryParser.AND).append("pending.").append(SqlProperties.RECORD_ID_ALIAS).append(" = apr.record_id").append(""
            + " ) ").append(SqlQueryParser.AND).append("pending.").append(SqlProperties.OWNER_ALIAS).append(" is not null")
        ;
        pendingFrom = sqlProps.getSbString();
        insertRecord(users, trigger, ApeConstants.INSERT_SELECT_ACTIVE, pendingFrom, sqlProps);
    }
    
    public int recordsAttended(ISecurityUser loggedUser, Set<Long> users, String updatePendingsFrom, SqlProperties sqlProps) {
        //ToDo: Solo funciona en SQL SERVER
        if (sqlProps.getWith() != null && !updatePendingsFrom.contains(sqlProps.getWith())) {
            sqlProps.startAppend(" WITH ").append(sqlProps.getWith()).append(" ");
        } else {
            sqlProps.startAppend("");
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with).append(""
            + " UPDATE apr SET "
                + " status = :status").append(""
                + " ,last_updated = current_timestamp"
                + " ,attended_on = current_timestamp");
        sqlProps.getParams().put("status", PendingRecord.STATUS.ATTENDED.getValue());
        if (loggedUser != null) {
            sqlProps.getParams().put("owner", loggedUser.getId());
            sqlProps.getSb().append(",attended_by = :owner");
        }
        sqlProps.getSb().append(updatePendingsFrom);
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(sqlProps, "select apr.pending_record_id, apr.owner as owner_id, apr.record_id as record_id" + updatePendingsFrom );
        final Integer added = addUserSet(sqlProps, users, Arrays.asList("apr.owner", "apr.super_owner_id"), updatePendingsFrom);
        if (added > 0 ||conditional.isManuallyTraceEnabled()) {
            //Ejecuta UPDATE!
            int n = dao.SQL_updateByQuery( 
                updatePendingSql,
                sqlProps.getParams(),
                0,
                Arrays.asList("ape_pending_record")
            );
            if (n > 0 || conditional.isManuallyTraceEnabled()) {
                getLogger(Loggable.LOGGER.APE).debug("{} pendings were updated to ATTENDED status!", n);
            }
            return n;
        } else {
            return 0;
        }
    }
    
    private int insertRecord(Set<Long> users, Class trigger, Map<String, String> selectMap, String pendingFrom, SqlProperties sqlProps) {
        boolean escalation = selectMap.equals(ApeConstants.INSERT_SELECT_ESCALATE);
        String impModule = module, select = getSelectColumns(selectMap);
        
        Module m = implementedModule;
        if (m != null) {
            impModule = m.getKey();
        }
        sqlProps.startAppend(SqlQueryParser.SELECT).append(" ")
            .append(type.getId()).append(SqlQueryParser.AS).append("type,")
            .append(scope.getValue()).append(SqlQueryParser.AS).append("scope,")
            .append("'").append(impModule).append("'").append(SqlQueryParser.AS).append("module,")
            .append("'").append(base).append("'").append(SqlQueryParser.AS).append("base,")
            .append("'").append(trigger.getCanonicalName()).append("'").append(SqlQueryParser.AS).append("created_at,")
            .append(select);//deleted, creation_date, last_updated, unattended_days, escalation_level
        sqlProps.appendDecode(escalation, SqlProperties.COMMITMENT_DATE_ALIAS, SqlQueryParser.CURRENT_DATE, SqlProperties.COMMITMENT_DATE_ALIAS)
            .append(SqlQueryParser.COMMA);
        sqlProps.appendDecode(ape.isNotice(), SqlProperties.NOTICE_DATE_ALIAS, SqlProperties.NOTICE_DATE_ALIAS, SqlQueryParser.NULL)
            .append(SqlQueryParser.COMMA);  
        sqlProps.appendDecode(ape.isDeadline(), SqlProperties.DEADLINE_DATE_ALIAS, SqlProperties.DEADLINE_DATE_ALIAS, SqlQueryParser.NULL) 
            .append(SqlQueryParser.COMMA);
        sqlProps.appendDecode(ape.isReminder(), SqlProperties.REMINDER_DATE_ALIAS, SqlProperties.REMINDER_DATE_ALIAS, SqlQueryParser.NULL)
            .append(SqlQueryParser.COMMA);
        sqlProps.appendDecode(ape.isExpired(), SqlProperties.EXPIRED_DATE_ALIAS, SqlProperties.EXPIRED_DATE_ALIAS, SqlQueryParser.NULL)
            .append(pendingFrom)
        ;
        if (escalation) {
            sqlProps.getSb()
                .append(SqlQueryParser.GROUP_BY)
                .append(getGroupByColumns(selectMap))
                .append(SqlQueryParser.COMMA)
                .append(SqlProperties.COMMITMENT_DATE_ALIAS)
            ;
            if (ape.isNotice()) {
                sqlProps.getSb()
                    .append(SqlQueryParser.COMMA)
                    .append(SqlProperties.NOTICE_DATE_ALIAS);
            }
            if (ape.isDeadline()) {
                sqlProps.getSb()
                    .append(SqlQueryParser.COMMA)
                    .append(SqlProperties.DEADLINE_DATE_ALIAS);
            }
            if (ape.isReminder()) {
                sqlProps.getSb()
                    .append(SqlQueryParser.COMMA)
                    .append(SqlProperties.REMINDER_DATE_ALIAS);
            }
            if (ape.isExpired()) {
                sqlProps.getSb()
                    .append(SqlQueryParser.COMMA)
                    .append(SqlProperties.EXPIRED_DATE_ALIAS);
            }
        }
        String selectSQL = sqlProps.getSbString();  
        if (sqlProps.getWith() != null && !selectSQL.contains(sqlProps.getWith())) {
            sqlProps.startAppend(" WITH ").append(sqlProps.getWith()).append(" ");
        } else {
            sqlProps.startAppend(" ");
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with).append(ApeConstants.INSERT_APE_RECORD_SQL).append(selectSQL);
        String updateSQL = sqlProps.getSbString();
        final Integer added;
        if (escalation) {
            //cuando inserta pending_records escalados
            added = addUserSet(sqlProps, users, "boses.boss_id", pendingFrom);
            if (added > 0 || conditional.isManuallyTraceEnabled()) {
                traceSqlSelect(sqlProps, selectSQL.replace("max(owner) as owner", "max(owner) as max_owner, min(owner) as min_owner, boses.boss_id as boses_id"));
            }
        } else {
            //cuando inserta pending_records activos
            added = addUserSet(sqlProps, users, SqlProperties.OWNER_ALIAS, pendingFrom);
            if (added > 0 || conditional.isManuallyTraceEnabled()) {
                traceSqlSelect(sqlProps, selectSQL);
            }
        }
        if (added > 0 || conditional.isManuallyTraceEnabled()) {
            //Ejecuta INSERT!
            int n = dao.SQL_updateByQuery(
                    updateSQL, 
                    sqlProps.getParams(),
                    0,
                    Arrays.asList("ape_pending_record")
            );
            if (n > 0 || conditional.isManuallyTraceEnabled()) {
                getLogger(Loggable.LOGGER.APE).debug("{} pendings were inserted!", n);
            }
            return n;
        } else {
            return 0;
        }
    }
    
    public void recordsRefreshNoticeDate(String pendingFrom, SqlProperties sqlProps) {
        if (!conditional.isRefreshNoticeDate()) {
            return;
        }
        if (SqlQueryParser.NULL.equals(sqlProps.getNoticeDateColumn())) {
            getLogger(Loggable.LOGGER.APE).trace("Notice date changes are unavailabe for the pending.");
            return;
        }
        getLogger(Loggable.LOGGER.APE).trace("Refreshing notice date.");
        //ToDo: Solo funciona en SQL SERVER
        
        sqlProps.startAppend(""
            + " WITH notice_dates as ("
                + " SELECT "
                    + " pending.notice_date new_notice_date, "
                    + " apr.notice_date old_notice_date, "
                    + " apr.pending_record_id").append(pendingFrom).append(""
                + " JOIN ape_pending_record apr ON"
                    + " apr.STATUS in (")
                        .append(PendingRecord.STATUS.ACTIVE.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.REASSIGNED.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.ESCALATED.getValue()).append(""
                    + " )"
                    + " AND apr.record_id = pending.record_id "
                    + " AND apr.OWNER = pending.OWNER "
                    + " AND apr.type = ").append(type.getId()).append(""
                + " WHERE"
                    + " pending.").append(SqlProperties.NOTICE_DATE_ALIAS).append(" != apr.notice_date"
                    + " OR ("
                            + " apr.notice_date IS NULL"
                            + " AND pending.").append(SqlProperties.NOTICE_DATE_ALIAS).append(" IS NOT NULL "
                    + " )"
            + " )"
        );
        if (sqlProps.getWith() != null) {
            final String previousWith = sqlProps.getSbString();
            if (!previousWith.contains(sqlProps.getWith())) {
                sqlProps.startAppend(previousWith).append(",").append(sqlProps.getWith()).append(" ");
            }
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with).append(""
            + " UPDATE ape SET notice_date = new_date.new_notice_date"
            + " FROM ape_pending_record ape"
            + " JOIN notice_dates new_date ON new_date.pending_record_id = ape.pending_record_id"
        );
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(sqlProps, with + "SELECT new_notice_date, old_notice_date, pending_record_id FROM notice_dates");
        int n = dao.SQL_updateByQuery(
            updatePendingSql,
            sqlProps.getParams(),
            0,
            Arrays.asList("ape_pending_record")
        );
        if (conditional.isManuallyTraceEnabled() || (n > 0 && getLogger(Loggable.LOGGER.APE).isDebugEnabled())) {
            getLogger(Loggable.LOGGER.APE).debug("{} pendings were updated their NOTICE date!, {}", new Object[] {
                n, implementedModule
            });
        }
        
    }
    
    public void recordsRefreshReminderDate(String pendingFrom, SqlProperties sqlProps) {
        if (!conditional.isRefreshReminderDate()) {
            return;
        }
        if (SqlQueryParser.NULL.equals(sqlProps.getReminderColumn())) {
            getLogger(Loggable.LOGGER.APE).trace("Remindder date changes are unavailabe for the pending.");
            return;
        }
        getLogger(Loggable.LOGGER.APE).trace("Refreshing reminder date.");
        //ToDo: Solo funciona en SQL SERVER
        
        sqlProps.startAppend(""
            + " WITH reminder_dates as ("
                + " SELECT "
                    + " pending.reminder new_reminder, "
                    + " apr.reminder old_reminder, "
                    + " apr.pending_record_id").append(pendingFrom).append(""
                + " JOIN ape_pending_record apr ON"
                    + " apr.STATUS in (")
                        .append(PendingRecord.STATUS.ACTIVE.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.REASSIGNED.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.ESCALATED.getValue()).append(""
                    + " )"
                    + " AND apr.record_id = pending.record_id "
                    + " AND apr.OWNER = pending.OWNER "
                    + " AND apr.type = ").append(type.getId()).append(""
                + " WHERE"
                    + " pending.").append(SqlProperties.REMINDER_DATE_ALIAS).append(" != apr.reminder"
                    + " OR ("
                            + " apr.reminder IS NULL"
                            + " AND pending.").append(SqlProperties.REMINDER_DATE_ALIAS).append(" IS NOT NULL "
                    + " )"
            + " )"
        );
        if (sqlProps.getWith() != null) {
            final String previousWith = sqlProps.getSbString();
            if (!previousWith.contains(sqlProps.getWith())) {
                sqlProps.startAppend(previousWith).append(",").append(sqlProps.getWith()).append(" ");
            }
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with).append(""
            + " UPDATE ape SET reminder = new_date.new_reminder"
            + " FROM ape_pending_record ape"
            + " JOIN reminder_dates new_date ON new_date.pending_record_id = ape.pending_record_id"
        );
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(sqlProps, with + "SELECT new_reminder, old_reminder, pending_record_id FROM reminder_dates");
        int n = dao.SQL_updateByQuery(
            updatePendingSql,
            sqlProps.getParams(),
            0,
            Arrays.asList("ape_pending_record")
        );
        if (conditional.isManuallyTraceEnabled() || (n > 0 && getLogger(Loggable.LOGGER.APE).isDebugEnabled())) {
            getLogger(Loggable.LOGGER.APE).debug("{} pendings were updated their REMINDER date!, {}", new Object[] {
                n, implementedModule
            });
        }
    }
    
    public void recordsRefreshDeadlineDate(String pendingFrom, SqlProperties sqlProps) {
        if (!conditional.isRefreshDeadlineDate()) {
            return;
        }
        if (SqlQueryParser.NULL.equals(sqlProps.getDeadlineColumn())) {
            getLogger(Loggable.LOGGER.APE).trace("Deadline date changes are unavailabe for the pending.");
            return;
        }
        getLogger(Loggable.LOGGER.APE).trace("Refreshing deadline date.");
        //ToDo: Solo funciona en SQL SERVER
        
        sqlProps.startAppend(""
            + " WITH deadline_dates as ("
                + " SELECT "
                    + " pending.deadline new_deadline, "
                    + " apr.deadline old_deadline, "
                    + " apr.pending_record_id").append(pendingFrom).append(""
                + " JOIN ape_pending_record apr ON"
                    + " apr.STATUS in (")
                        .append(PendingRecord.STATUS.ACTIVE.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.REASSIGNED.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.ESCALATED.getValue()).append(""
                    + " )"
                    + " AND apr.record_id = pending.record_id "
                    + " AND apr.OWNER = pending.OWNER "
                    + " AND apr.type = ").append(type.getId()).append(""
                + " WHERE"
                    + " pending.").append(SqlProperties.DEADLINE_DATE_ALIAS).append(" != apr.deadline"
                    + " OR ("
                            + " apr.deadline IS NULL"
                            + " AND pending.").append(SqlProperties.DEADLINE_DATE_ALIAS).append(" IS NOT NULL "
                    + " )"
            + " )"
        );
        if (sqlProps.getWith() != null) {
            final String previousWith = sqlProps.getSbString();
            if (!previousWith.contains(sqlProps.getWith())) {
                sqlProps.startAppend(previousWith).append(",").append(sqlProps.getWith()).append(" ");
            }
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with).append(""
            + " UPDATE ape SET deadline = new_date.new_deadline"
            + " FROM ape_pending_record ape"
            + " JOIN deadline_dates new_date ON new_date.pending_record_id = ape.pending_record_id"
        );
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(sqlProps, with + "SELECT new_deadline, old_deadline, pending_record_id FROM deadline_dates");
        int n = dao.SQL_updateByQuery(
            updatePendingSql,
            sqlProps.getParams(),
            0,
            Arrays.asList("ape_pending_record")
        );
        if (conditional.isManuallyTraceEnabled() || (n > 0 && getLogger(Loggable.LOGGER.APE).isDebugEnabled())) {
            getLogger(Loggable.LOGGER.APE).debug("{} pendings were updated their DEADLINE date!, {}", new Object[] {
                n, implementedModule
            });
        }
    }
    
    public void recordsRefreshExpiredDate(String pendingFrom, SqlProperties sqlProps) {
        if (!conditional.isRefreshExpiredDate()) {
            return;
        }
        if (SqlQueryParser.NULL.equals(sqlProps.getExpiredColumn())) {
            getLogger(Loggable.LOGGER.APE).trace("Expired date changes are unavailabe for the pending.");
            return;
        }
        getLogger(Loggable.LOGGER.APE).trace("Refreshing expired date.");
        //ToDo: Solo funciona en SQL SERVER
        
        sqlProps.startAppend(""
            + " WITH expired_dates as ("
                + " SELECT "
                    + " pending.expired new_expired, "
                    + " apr.expired old_expired, "
                    + " apr.pending_record_id").append(pendingFrom).append(""
                + " JOIN ape_pending_record apr ON"
                    + " apr.STATUS in (")
                        .append(PendingRecord.STATUS.ACTIVE.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.REASSIGNED.getValue()).append(SqlQueryParser.COMMA)
                        .append(PendingRecord.STATUS.ESCALATED.getValue()).append(""
                    + " )"
                    + " AND apr.record_id = pending.record_id "
                    + " AND apr.OWNER = pending.OWNER "
                    + " AND apr.type = ").append(type.getId()).append(""
                + " WHERE"
                    + " pending.").append(SqlProperties.EXPIRED_DATE_ALIAS).append(" != apr.expired"
                    + " OR ("
                            + " apr.expired IS NULL"
                            + " AND pending.").append(SqlProperties.EXPIRED_DATE_ALIAS).append(" IS NOT NULL "
                    + " )"
            + " )"
        );
        if (sqlProps.getWith() != null) {
            final String previousWith = sqlProps.getSbString();
            if (!previousWith.contains(sqlProps.getWith())) {
                sqlProps.startAppend(previousWith).append(",").append(sqlProps.getWith()).append(" ");
            }
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with).append(""
            + " UPDATE ape SET expired = new_date.new_expired"
            + " FROM ape_pending_record ape"
            + " JOIN expired_dates new_date ON new_date.pending_record_id = ape.pending_record_id"
        );
        String updatePendingSql = sqlProps.getSbString();
        traceSqlSelect(sqlProps, with + "SELECT new_expired, old_expired, pending_record_id FROM expired_dates");
        int n = dao.SQL_updateByQuery(
            updatePendingSql,
            sqlProps.getParams(),
            0,
            Arrays.asList("ape_pending_record")
        );
        if (conditional.isManuallyTraceEnabled() || (n > 0 && getLogger(Loggable.LOGGER.APE).isDebugEnabled())) {
            getLogger(Loggable.LOGGER.APE).debug("{} pendings were updated their EXPIRED date!, {}", new Object[] {
                n, implementedModule
            });
        }
    }
       
    public void attendDependencies(ISecurityUser loggedUser, int attended, Class trigger, Long ... recordId) {
        if (dependencies == null) {
            getLogger(Loggable.LOGGER.APE).trace("No dependencies!");
            return;
        }
        if (conditional.isSkipDependencies()) {
            getLogger(Loggable.LOGGER.APE).trace("This is a nested dependency!");
            return;
        }
        if (conditional.isSkipDependenciesUnchanged() && attended == 0) {
            return;
        }
        getLogger(Loggable.LOGGER.APE).debug("Running {} dependencies.", dependencies.length);
        try {
            if (implementedModule == null) {
                for (Class<? extends IPendingOperation> dependency : dependencies) {
                    if (stack.contains(dependency.getCanonicalName())) {
                        continue;
                    }
                    stack.add(dependency.getCanonicalName());
                    final IPendingOperation temp = dependency.getConstructor(IUntypedDAO.class).newInstance(dao);
                    getLogger(Loggable.LOGGER.APE).debug("Running dependency {}(1) {", dependency.getSimpleName());
                    final PendingConditional tempConditional = new PendingConditional(conditional);
                    tempConditional.setSkipDependencies(true);
                    temp.setStack(stack);
                    temp.updateCounterByUsers(
                        temp.updateRecord(trigger, tempConditional, loggedUser, recordId),
                        loggedUser
                    );
                    getLogger(Loggable.LOGGER.APE).debug("}");
                }
            } else {
                for (Class<? extends IPendingOperation> dependency : dependencies) {
                    if (stack.contains(dependency.getCanonicalName())) {
                        continue;
                    }
                    IPendingOperation temp = null;
                    stack.add(dependency.getCanonicalName());
                    try {
                        temp = dependency.getConstructor(Module.class, IUntypedDAO.class).newInstance(implementedModule, dao);
                        getLogger(Loggable.LOGGER.APE).debug("Running dependency {}(2) {", dependency.getSimpleName());
                        try {
                            final PendingConditional tempConditional = new PendingConditional(conditional);
                            tempConditional.setSkipDependencies(true);
                            temp.setStack(stack);
                            temp.updateCounterByUsers(
                                temp.updateRecord(trigger, tempConditional, loggedUser, recordId),
                                loggedUser
                            );
                        } catch(Exception e) {
                            getLogger().error(
                                    "There was an error updating dependency {} ", 
                                    new Object[] {
                                        dependency.getSimpleName(),
                                        e
                                    }
                            );
                            throw e;
                        }
                    } catch(Exception e) {
                        if (conditional.isManuallyTraceEnabled() || getLogger(Loggable.LOGGER.APE).isDebugEnabled()) {
                            getLogger(Loggable.LOGGER.APE).error("Cross dependency detected! parent: '{}', child: '{}'", new Object[] {
                                getClass().getSimpleName(), dependency.getSimpleName()
                            });
                            getLogger(Loggable.LOGGER.APE).error("Running cross dependency patch {}() {", dependency.getSimpleName());
                        }
                        if (temp == null) {
                            temp = dependency.getConstructor(IUntypedDAO.class).newInstance(dao);
                        }
                        if (temp == null) {
                            getLogger().error("There was an error ", e);
                        } else {
                            final PendingConditional tempConditional = new PendingConditional(conditional);
                            tempConditional.setSkipDependencies(true);
                            temp.setStack(stack);
                            temp.recalculate(loggedUser, trigger, tempConditional);
                        }
                    }
                    getLogger(Loggable.LOGGER.APE).debug("}");
                }
            }
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
            getLogger(Loggable.LOGGER.APE).error("Unable to update dependencies!", ex);
        }
    }
    
    public static IPendingOperation getInstance(
            final Class<? extends IPendingOperation> dependency,
            final Module implementedModule,
            final IUntypedDAO dao
    ) {
        IPendingOperation temp = null;
        try {
            try {
                temp = dependency.getConstructor(Module.class, IUntypedDAO.class).newInstance(implementedModule, dao);
                return temp;
            } catch (Exception e) {
                if (temp == null) {
                    temp = dependency.getConstructor(IUntypedDAO.class).newInstance(dao);
                }
                return temp;
            }
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException | SecurityException | IllegalArgumentException | InvocationTargetException ex) {
            return null;
        }
    }

    private Integer addUserSet(SqlProperties sqlProps, Set<Long> users, String columnOwner, String fromSql) {
        return addUserSet(sqlProps, users, Arrays.asList(columnOwner), fromSql);
    }
    
    private Integer addUserSet(SqlProperties sqlProps, Set<Long> users, List<String> columnsOwner, String fromSql) {
        final String columnOwner = StringUtils.join(columnsOwner, ",");
        //Ejecuta consulta que obtiene directamente todos los usuarios
        if (sqlProps.getWith() != null && !fromSql.contains(sqlProps.getWith())) {
            sqlProps.startAppend(" WITH ").append(sqlProps.getWith()).append(" ");
        } else {
            sqlProps.startAppend("");
        }
        String with = sqlProps.getSbString();
        sqlProps.startAppend(with)
                .append(SqlQueryParser.SELECT)
                .append(columnOwner)
                .append(fromSql)
                .append(SqlQueryParser.GROUP_BY).append(columnOwner);
        List usersData = dao.SQL_findByQuery(sqlProps.getSbString(), sqlProps.getParams());
        final Set<Long> newUsers = new HashSet<>();
        if (columnsOwner.size() == 1) {
            for (Object uId : usersData) {
                if (uId == null) {
                    continue;
                }
                newUsers.add(Long.valueOf(uId.toString()));
            }
        } else {
            for (Object userData : usersData) {
                final Object[] userList = (Object[]) userData;
                for (Object uId : userList) {
                    if (uId == null) {
                        continue;
                    }
                    newUsers.add(Long.valueOf(uId.toString()));
                }
            }
        }
        users.addAll(newUsers);
        return newUsers.size();
    }
    
    private void traceSqlSelect(
            SqlProperties sqlProps, 
            String SQL
    ) {
        if (conditional.isManuallyTraceEnabled() || getLogger(LOGGER.APE).isTraceEnabled()) {
            try {
                if (sqlProps.getWith() != null && !SQL.contains(sqlProps.getWith())) {
                    SQL  = " WITH " + sqlProps.getWith() + " " + SQL;
                }
                List<Object[]> pendings = dao.SQL_findByQuery(SQL, sqlProps.getParams());
                if (!pendings.isEmpty()) {
                    sqlProps.startAppend("\r\n-------\r\n- ").append(SQL);
                    for (Object[] pending : pendings) {
                        sqlProps.getSb().append("\r\n-");
                        for (Object object : pending) {
                            sqlProps.getSb().append(" ").append(object).append(',');
                        }
                    }
                    sqlProps.getSb().append("\r\n-------");
                    getLogger(LOGGER.APE).trace(sqlProps.getSb().toString());
                }
            } catch (Exception e) {
                getLogger(LOGGER.APE).error("Invalid logger configuration, {}", implementedModule, e);
            }
        }
    }
    
    private String getSelectColumns(Map<String, String> map) {
        StringBuilder selectColumnBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            selectColumnBuilder
                .append(value)
                .append(SqlQueryParser.AS)
                .append(key)
                .append(SqlQueryParser.COMMA)
            ;
        }
        return selectColumnBuilder.toString();
    }
    
    private String getGroupByColumns(Map<String, String> map) {
        StringBuilder groupByColumnBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String value = entry.getValue();
            switch(value) {
                case SqlQueryParser.ONE:
                case SqlQueryParser.ZERO:
                case SqlQueryParser.NULL:
                case SqlQueryParser.COUNT:
                case SqlQueryParser.CURRENT_DATE:
                continue;
            }
            if (ApeConstants.STATUS_ACTIVE.equals(value) 
                    || ApeConstants.STATUS_ESCALATED.equals(value) 
                    || ApeConstants.STATUS_ACTIVE_ESCALATED.equals(value)) {
                continue;
            }
            if (value.startsWith(SqlQueryParser.MAX)) {
                continue;
            }
            groupByColumnBuilder.append(SqlQueryParser.COMMA);
            if (value.endsWith(SqlQueryParser.PLUS_ONE)) {
                groupByColumnBuilder.append(value.replace(SqlQueryParser.PLUS_ONE, ""));
            } else {
                groupByColumnBuilder.append(value);
            }
        }
        return groupByColumnBuilder.deleteCharAt(0).toString();
    }

}
