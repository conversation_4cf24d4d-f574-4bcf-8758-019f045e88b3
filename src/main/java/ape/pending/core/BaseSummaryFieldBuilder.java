package ape.pending.core;

import ape.pending.dto.ColumnDTO;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BaseSummaryFieldBuilder {
    /**
     * Se utiliza para construir una lista de instancias tipo "ColumnsDTO", la
     * sintaxis debe cumplir el siguiente criterio:
     *
     *      "<columna del entity> = <alias>@<alias de la tabla>"
     *
     * Los valores de <alias> y <alias de la tabla> son opcionales,
     *
     * Algunos Ejemplos:
     *
     * 1) La siguiente configuración es equivalente a escribir "SELECT position.description AS positionDescription FROM ..."
     *      "description = positionDescription@position"
     *
     * 2) La siguiente configuración es equivalente a escribir "SELECT description AS positionDescription FROM ..."
     *      "description = positionDescription"
     *
     * 3) La siguiente configuración es equivalente a escribir "SELECT description FROM ..."
     *      "description"
     *
     * @param tuples
     * @return
     */
    public static ColumnDTO[] columns(String ... tuples) {
        List<ColumnDTO> result = new ArrayList<>(tuples.length);
        for(String tuple : tuples) {
            String[] keyVal;
            String tableAlias = null;
            int tableAliasIdx = tuple.indexOf('@');
            if (tableAliasIdx == -1) {
                keyVal = tuple.split("=");
            } else {
                keyVal = tuple.substring(0, tableAliasIdx).split("=");
                tableAlias = tuple.substring(tableAliasIdx + 1);
                if (tableAlias.isEmpty()) {
                    throw new RuntimeException("Invalid 'BaseSummaryField' sintax, invalid tuple is: '" + tuple + "', missing '@tableAlias'.");
                }
            }
            String key;
            String val;
            switch (keyVal.length) {
                case 1:
                    key = val = keyVal[0].trim();
                    break;
                case 2:
                    key = keyVal[0].trim();
                    val = keyVal[1].trim();
                    break;
                default:
                    throw new RuntimeException("Invalid 'BaseSummaryField' sintax, invalid tuple is: '" + tuple + "'.");
            }
            if (key.isEmpty()) {
                throw new RuntimeException("Invalid 'BaseSummaryField' sintax, invalid tuple is: '" + tuple + "', missing 'key'.");
            }
            result.add(new ColumnDTO(key, val));
            if (tableAlias != null) {
                result.get(result.size() - 1).setTableAlias(tableAlias);
            }
        }
        return result.toArray(new ColumnDTO[0]);
    }
}
