package ape.pending.core;

import java.util.List;
import qms.framework.mail.MailDTO;

/**
 *
 * <AUTHOR>
 */
public interface INoticeOperation {

    /**
     * Este aviso se envia para avisar con anticipación
     * de que la fecha compromiso está por llegar
     *
     * Ej. Revisar campos "notifyImplementTime" y "notifyVerifyTime"
     * @return
     */
    public List<MailDTO> getNoticeMail();
    public APE getApe();
    
}
