package ape.pending.util;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class TableConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private StringBuilder codeFrom;
    private StringBuilder subCodeFrom;
    private StringBuilder joins;
    private List<String> types = new ArrayList<>(1);

    public StringBuilder getCodeFrom() {
        return codeFrom;
    }

    public void setCodeFrom(StringBuilder codeFrom) {
        this.codeFrom = codeFrom;
    }

    public StringBuilder getSubCodeFrom() {
        return subCodeFrom;
    }

    public void setSubCodeFrom(StringBuilder subCodeFrom) {
        this.subCodeFrom = subCodeFrom;
    }

    public StringBuilder getJoins() {
        return joins;
    }

    public void setJoins(StringBuilder joins) {
        this.joins = joins;
    }

  
    public List<String> getTypes() {
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 47 * hash + Objects.hashCode(this.codeFrom);
        hash = 47 * hash + Objects.hashCode(this.subCodeFrom);
        hash = 47 * hash + Objects.hashCode(this.joins);
        hash = 47 * hash + Objects.hashCode(this.types);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TableConfig other = (TableConfig) obj;
        if (!Objects.equals(this.codeFrom, other.codeFrom)) {
            return false;
        }
        if (!Objects.equals(this.subCodeFrom, other.subCodeFrom)) {
            return false;
        }
        if (!Objects.equals(this.joins, other.joins)) {
            return false;
        }
        return Objects.equals(this.types, other.types);
    }

    @Override
    public String toString() {
        return "EntityConfig{"
                + "codeFrom=" + codeFrom
                + ", subCodeFrom=" + subCodeFrom
                + ", joins=" + joins
                + ", types=" + types + '}';
    }

}
