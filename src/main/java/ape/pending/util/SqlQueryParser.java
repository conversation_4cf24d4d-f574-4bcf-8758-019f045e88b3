
package ape.pending.util;

import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import ape.pending.entities.PendingRecord;
import mx.bnext.core.security.ISecurityUser;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class SqlQueryParser {
    private final String insertPendingActiveFrom, insertPendingEscalatedFrom, updateToAttendedFrom, updateToUnattendedFrom, existsPendingSql;
    public static final String
        SELECT = "select ",
        LEFT_JOIN = " left join ",
        JOIN = " join ",
        ON = " on ",
        GROUP_BY = " group by ", 
        CURRENT_DATE = "getdate()", 
        NULL = " NULL ",
        MAX = " max",
        ONE = " 1 ",
        PLUS_ONE = " + 1 ",
        ZERO = " 0 ",
        COUNT = "count(*)",
        AS = " as ",
        SPACE = " ",
        DOT = ".",
        AND = " and ",
        OR = " or ",
        FROM = " from ",
        WHERE = " where ",
        COMMA = ","
    ;

    public SqlQueryParser(ISecurityUser loggedUser, Class trigger, SqlProperties sqlProps, IUntypedDAO dao, String recordExistsHQL) {
        sqlProps.startAppend(FROM).append(" "
                + "(")
                .append(SELECT)
                .append(sqlProps.getCommitmentColumn()).append(AS).append(SqlProperties.COMMITMENT_DATE_ALIAS).append(SqlQueryParser.COMMA)
                .append(sqlProps.getOwnerColumn()).append(AS).append(SqlProperties.OWNER_ALIAS).append(SqlQueryParser.COMMA)
                .append(sqlProps.getRecordIdColumn()).append(AS).append(SqlProperties.RECORD_ID_ALIAS).append(SqlQueryParser.COMMA)
                .append(sqlProps.getDeadlineColumn()).append(AS).append(SqlProperties.DEADLINE_DATE_ALIAS).append(SqlQueryParser.COMMA)
                .append(sqlProps.getExpiredColumn()).append(AS).append(SqlProperties.EXPIRED_DATE_ALIAS).append(SqlQueryParser.COMMA)
                .append(sqlProps.getNoticeDateColumn()).append(AS).append(SqlProperties.NOTICE_DATE_ALIAS).append(SqlQueryParser.COMMA)
                .append(sqlProps.getReminderColumn()).append(AS).append(SqlProperties.REMINDER_DATE_ALIAS).append(SqlQueryParser.COMMA)
                .append(sqlProps.getSuperOwnerColumn()).append(AS).append(SqlProperties.SUPER_OWNER_ALIAS)
                .append(sqlProps.getFrom())
                .append(GROUP_BY);
        if (!CURRENT_DATE.equals(sqlProps.getCommitmentColumn())) {
            sqlProps.getSb().append(sqlProps.getCommitmentColumn()).append(',');
        }
        if (!NULL.equals(sqlProps.getNoticeDateColumn())) {
            sqlProps.getSb().append(sqlProps.getNoticeDateColumn()).append(',');
        }
        if (!NULL.equals(sqlProps.getDeadlineColumn())) {
            sqlProps.getSb().append(sqlProps.getDeadlineColumn()).append(',');
        }
        if (!NULL.equals(sqlProps.getReminderColumn()) && !CURRENT_DATE.equals(sqlProps.getReminderColumn())) {
            sqlProps.getSb().append(sqlProps.getReminderColumn()).append(',');
        }
        if (!NULL.equals(sqlProps.getSuperOwnerColumn())) {
            sqlProps.getSb().append(sqlProps.getSuperOwnerColumn()).append(',');
        }
        
        sqlProps.getSb()
                .append(sqlProps.getOwnerColumn()).append(',')
                .append(sqlProps.getRecordIdColumn()).append(" "
                + ") pending");
        insertPendingActiveFrom = sqlProps.getSbString();
        //ToDo: Solo funciona para SQL_SERVER, se deja así para que truene por completo en otras DB's!!
        sqlProps.startAppend(SELECT).append(""
            + " apr.pending_record_id")
                .append(FROM).append(""
            + " ape_pending_record apr"
            + " WHERE "
                + " apr.pending_record_id IN (").append(
                    dao.hqlToSqlString(
                        recordExistsHQL
                    ).replace("-7", "apr.record_id").replace("?", ":record")).append(""
                + " )");
        existsPendingSql = sqlProps.getSbString();
        //Se obtiene FROM de pendientes que seran marcados como ATTENDED
        sqlProps.startAppend(FROM).append(""
            + " ape_pending_record apr "
            + " WHERE apr.pending_record_id IN (")
                .append(existsPendingSql).append(""
            + " ) AND not exists (")
                .append(SELECT).append(" 1 ")
                .append(sqlProps.getFrom())
                .append(AND).append(sqlProps.getOwnerColumn()).append(" = apr.owner")
                .append(AND).append(sqlProps.getRecordIdColumn()).append(" = apr.record_id").append(""
            + " )")
        ;
        updateToAttendedFrom = sqlProps.getSbString();
        //Se obtiene FROM de pendientes que seran insertados como ESCALATED y HISTORY
        sqlProps.startAppend(FROM).append(""
            + " ape_pending_record pending "
            + " JOIN ape_pending_type apt on apt.pending_type_id = pending.type"
            + " JOIN ("
                + " SELECT b.user_id boss_id, u.user_id "
                + " FROM users u "
                + " LEFT JOIN escalation_exception ex ON u.user_id = ex.user_id AND ex.deleted = 0 AND ex.STATUS = 1 "
                + " LEFT JOIN users b "
                    + " ON (b.deleted = 0 AND (u.boss_id = b.user_id AND ex.boss_exception_id IS NULL))"
                    + " OR (b.deleted = 0 AND (ex.boss_exception_id = b.user_id)) "
                + " WHERE"
                    + " u.deleted = 0"
                    + " AND u.scalable = ").append(User.STATUS.ACTIVE).append(""
                    + " AND ( b.user_id IS NULL OR b.status = ").append(User.STATUS.ACTIVE).append(")"
            + " ) boses ON ("
                    + " boses.user_id = pending.owner"
                    + " AND pending.super_owner_id IS NULL"
                    + " AND pending.status = ").append(PendingRecord.STATUS.ACTIVE.getValue()).append(""
                    + " AND boses.boss_id IS NOT NULL"
                + ") OR ("
                    + " boses.user_id = pending.super_owner_id"
                    + " AND boses.boss_id IS NOT NULL"
                + " ) "
            + " WHERE"
                + " pending.scalable = 1"
                + " AND pending.pending_record_id IN (")
                    .append(existsPendingSql).append(""
                + " )"
                + " AND "
                    + " pending.owner IN (")
                        .append(SELECT).append(sqlProps.getOwnerColumn())
                        .append(sqlProps.getFrom()).append(""
                    + " )").append(""
                + " AND "
                    + " pending.record_id IN (")
                        .append(SELECT).append(sqlProps.getRecordIdColumn())
                        .append(sqlProps.getFrom()).append(""
                    + " )").append(""
                + " AND apt.escalation_enabled = 1 "
                + " AND pending.escalation_level < apt.escalation_levels"
                + " AND pending.unattended_days > apt.escalation_days"
            )
        ;
        insertPendingEscalatedFrom = sqlProps.getSbString();
        //Se obtiene FROM de pendientes que se les aumentará su numero de días sin atender
        sqlProps.startAppend(FROM).append(""
            + " ape_pending_record pending "
            + " JOIN ape_pending_type apt ON apt.pending_type_id = pending.type"
            + " JOIN unattended u ON u.pending_record_id = pending.pending_record_id"
            + " WHERE pending.pending_record_id IN (")
                .append(existsPendingSql).append(""
            + " )").append(AND).append(""
            + " pending.owner IN (")
                .append(SELECT).append(sqlProps.getOwnerColumn())
                .append(sqlProps.getFrom()).append(""
            + " )"
            + "").append(AND).append(""
            + " pending.record_id IN (")
                .append(SELECT).append(sqlProps.getRecordIdColumn())
                .append(sqlProps.getFrom()).append(""
            + " )"
            + " AND cast(commitment_date As Date) != cast(CURRENT_TIMESTAMP As Date) "
            + " AND pending.unattended_days != u.unattended_days "
            )
        ;
        updateToUnattendedFrom = sqlProps.getSbString();
    }

    public String getUpdateToUnattendedFrom() {
        return updateToUnattendedFrom;
    }

    public String getInsertPendingActiveFrom() {
        return insertPendingActiveFrom;
    }

    public String getExistsPendingSql() {
        return existsPendingSql;
    }

    public String getUpdateToAttendedFrom() {
        return updateToAttendedFrom;
    }

    public String getInsertPendingEscalatedFrom() {
        return insertPendingEscalatedFrom;
    }
    
}
