
package ape.pending.util;

import DPMS.Mapping.BusinessUnitDepartmentLoad;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.ApeOperationType;
import ape.pending.dto.ApeEntityConfig;
import ape.pending.dto.BaseJoinDTO;
import qms.framework.core.EntityModelCache;
import ape.pending.dto.SoftBaseDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import mx.bnext.access.Module;
import qms.framework.core.EntityHqlBuilderDTO;
import qms.util.BindUtil;
import qms.util.GridConfigHQL;
import qms.util.Translate;
import qms.util.interfaces.IGridConfigHQL;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class EntityHqlBuilder implements HqlBuilder {
    
    private StringBuilder columns = new StringBuilder(200);
    private StringBuilder joins = new StringBuilder(200);
    private final StringBuilder conditions = new StringBuilder("");
    private final StringBuilder crossConditions = new StringBuilder("");
    private final StringBuilder baseConditions = new StringBuilder("");
    private final StringBuilder typeConditions = new StringBuilder("");
    private final ApeEntityConfig entityConfig;

    public EntityHqlBuilder(ApeEntityConfig entityConfig) {
        this.entityConfig = entityConfig;
    }
    
    @Override
    public GridConfigHQL getGridConfig(
            Class entityClass, 
            String entityClassAlias,
            List<String> validColumnNames, 
            boolean concatAlias,
            Long colIndex, 
            IUntypedDAO dao
    ) {
        return Translate.getGridConfig(entityClass, entityClassAlias, validColumnNames, concatAlias, colIndex, dao);
    }
    
    @Override
    public void appendSoft(EntityHqlBuilderDTO dto) {
        final SoftBaseDTO softBase = dto.getSoftBase();
        Class entityStrong = softBase.getStrongEntityAPE(),
              entitySoft = softBase.getSoftEntityAPE();
        final String entityStrongAlias = dto.getEntityStrongAlias();
        final String entitySoftAlias = dto.getEntitySoftAlias();
        String baseAlias = entityStrongAlias + "_" + entitySoftAlias;
        final Class classEntity = dto.getClassEntity();
        //base
        joins.append(", ").append(classEntity.getCanonicalName()).append(" ").append(baseAlias);
        //strong
        Long colNameIdx;
        dto.setEntityStrong(entityStrong);
        final Module module = dto.getModule();
        if (softBase.getType() == ApeOperationType.WEAK) {
            colNameIdx = append(module, entityStrongAlias, dto, baseAlias);
        } else {
            colNameIdx = append(module, entityStrongAlias, dto, entityStrongAlias);
        }
        conditions.append(SqlQueryParser.AND).append(entityStrongAlias).append(".id = ").append(baseAlias).append(".").append(softBase.getRootStrongEntityId());
        final IUntypedDAO dao = dto.getDao();
        //soft
        IGridConfigHQL configSoft = getGridConfig(entitySoft, entitySoftAlias, null, true, colNameIdx, dao);
        columns.append(", ").append(configSoft.getColumnsString(entitySoftAlias, true));
        joins.append(", ").append(configSoft.getHqlFrom());
        conditions.append(SqlQueryParser.AND).append(entitySoftAlias).append(".id = ").append(baseAlias).append(".").append(softBase.getRootSoftEntityId());
        final Class type = dto.getSoftType();
        if (type != null) {
            columns.append(", t").append(entitySoftAlias).append(".code as ")
                    .append(entitySoftAlias).append("TypeCode, t").append(entitySoftAlias).append(".description as ")
                    .append(entitySoftAlias).append("TypeName");
            joins.append(", ").append(type.getCanonicalName()).append(" t").append(entitySoftAlias);
            conditions.append(SqlQueryParser.AND).append(entitySoftAlias).append(".typeId = t").append(entitySoftAlias).append(".id ");
        }
        final Class source = dto.getSoftSource();
        if (source != null) {
            columns.append(", s").append(entitySoftAlias).append(".code as ")
                    .append(entitySoftAlias).append("SourceCode, s").append(entitySoftAlias).append(".description as ")
                    .append(entitySoftAlias).append("SourceName");
            joins.append(" LEFT JOIN ").append(source.getCanonicalName()).append(" s").append(entitySoftAlias).append(" ON ").append(entitySoftAlias).append(".sourceId = s").append(entitySoftAlias).append(".id ");
        }
        joinBusinessUnitDepartment(entitySoft, entitySoftAlias);
    }

    private void joinBusinessUnitDepartment(final Class entity, final String alias) {
        if (EntityModelCache.isBusinessUnitLocation(entity) && EntityModelCache.isDepartmentLocation(entity)) {
            columns
                    .append(", dep").append(alias).append(".description as ").append(alias).append("DepartmentName")
                    .append(", bu").append(alias).append(".description as ").append(alias).append("BusinessUnitName");
            joins
                    .append(" LEFT JOIN ").append(alias).append(".department dep").append(alias)
                    .append(" LEFT JOIN ").append(alias).append(".businessUnit bu").append(alias);

        } else if (EntityModelCache.isBusinessUnitLocation(entity) && EntityModelCache.isBusinessUnitDepartmentLocation(entity)) {
            columns
                    .append(", dep").append(alias).append(".description as ").append(alias).append("DepartmentName")
                    .append(", bu").append(alias).append(".description as ").append(alias).append("BusinessUnitName");
            joins
                    .append(" LEFT JOIN ").append(alias).append(".businessUnitDepartment dep").append(alias)
                    .append(" LEFT JOIN ").append(alias).append(".businessUnit bu").append(alias);
        } else if (EntityModelCache.isBusinessUnitDepartmentId(entity)) {
            columns
                    .append(", bud").append(alias).append(".departmentDescription as ")
                    .append(alias).append("DepartmentName, bud").append(alias).append(".businessUnitDescription as ")
                    .append(alias).append("BusinessUnitName");
            joins
                    .append(" LEFT JOIN ").append(BusinessUnitDepartmentLoad.class.getCanonicalName())
                    .append(" bud").append(alias).append(" ON bud").append(alias).append(".id = ")
                    .append(alias).append(".businessUnitDepartmentId");
        }
    }

    @Override
    public Long appendStrong(EntityHqlBuilderDTO dto) {
        return append(dto.getModule(), dto.getEntitySoftAlias(), dto, dto.getEntitySoftAlias());
    }

    private Long append(Module module, String alias, EntityHqlBuilderDTO dto, String joinRecordAlias) {
        final Class entityStrong = dto.getEntityStrong();
        final IUntypedDAO dao = dto.getDao();
        GridConfigHQL config = getGridConfig(entityStrong, alias, null, true, 0L, dao);
        columns.append(", ").append(config.getColumnsString(alias, true));
        joins.append(", ").append(config.getHqlFrom());
        crossConditions.append(SqlQueryParser.AND).append(" ").append(joinRecordAlias).append(".id = record.recordId ");
        baseConditions.append(SqlQueryParser.AND).append(" '").append(module.getKey()).append("' = record.module ");
        final Class classEntity = dto.getClassEntity();
        final Set<String> types = new LinkedHashSet<>(ApeUtil.getCodes(module, classEntity, dao));
        final List<String> sortedTypes = new ArrayList<>();
        sortedTypes.addAll(types);
        Collections.sort(sortedTypes);
        //Valor de columna dummy de tabla dual para query BaseJoinDTO.ENTITY_HQL
        sortedTypes.add("X");
        baseConditions.append(SqlQueryParser.AND).append(BindUtil.parseFilterList("ptype.code", sortedTypes, false));
        final Class type = dto.getStrongType();
        if (type != null) {
            columns.append(", t").append(alias).append(".code as ")
                    .append(alias).append("TypeCode, t").append(alias).append(".description as ")
                    .append(alias).append("TypeName");
            joins.append(", ").append(type.getCanonicalName()).append(" t").append(alias);
            typeConditions.append(SqlQueryParser.AND).append(alias).append(".typeId = t").append(alias).append(".id ");
        }
        final Class source = dto.getStrongSource();
        if (source != null) {
            columns.append(", s").append(alias).append(".code as ")
                    .append(alias).append("SourceCode, s").append(alias).append(".description as ")
                    .append(alias).append("SourceName");
            joins.append(" LEFT JOIN ").append(source.getCanonicalName()).append(" s").append(alias).append(" ON ").append(alias).append(".sourceId = s").append(alias).append(".id ");
        }
        joinBusinessUnitDepartment(entityStrong, alias);
        return config.getColNameIdx();
    }
    @Override
    public BaseJoinDTO getBaseJoinDTO() {
        return new BaseJoinDTO(
                columns.toString(),
                joins.toString(), 
                conditions.toString(),
                crossConditions.toString(),
                baseConditions.toString(),
                typeConditions.toString(), 
                entityConfig
        );
    }

    public StringBuilder getColumns() {
        return columns;
    }

    public StringBuilder getJoins() {
        return joins;
    }

    public void setColumns(StringBuilder columns) {
        this.columns = columns;
    }

    public void setJoins(StringBuilder joins) {
        this.joins = joins;
    }
    
}
