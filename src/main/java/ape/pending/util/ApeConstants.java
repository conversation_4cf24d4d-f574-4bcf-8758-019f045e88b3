package ape.pending.util;

import ape.pending.entities.PendingCount;
import ape.pending.entities.PendingOperationDTO;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class ApeConstants {
     
    public static final String HQL_CURRENT_DATE = "current_date()";
    public static final String UNATTENDED_DAYS_WITH = ""
            + " WITH "
            /**
             * Se obtienen los días inhabiles reales por usuario de la tabla intermedia "user_break_date"
             **/
            + " break_days AS ( "
                + " SELECT u.break_date, u.user_id "
                + " FROM user_break_date u "
                + " WHERE u.status = 1 "
            + " ), "
            /**
             * Se calcula el valor real de dias transcurridos desde el compromiso
             *   1. Se obtienen la resta de la fecha de hoy contra el compromiso
             *   2. Se le resta la cantidad (count) de días inhabiles en el periodo del compromiso
             */
            + " unattended AS ("
                + " SELECT datediff(DAY, commitment_date, CURRENT_TIMESTAMP) - CASE WHEN max(ub.break_date) IS NULL THEN 0 ELSE count(1) END "
                    + " AS unattended_days, "
                    + " pending.pending_record_id "
                + " FROM ape_pending_record pending "
                + " JOIN ape_pending_type apt ON apt.pending_type_id = pending.type "
                + " LEFT JOIN break_days ub ON"
                    + " ub.break_date > pending.commitment_date "
                    + " AND ub.break_date < CURRENT_TIMESTAMP "
                    //Los días inhabiles se restan según los dias inhabiles del responsable actual
                    + " AND ( "
                        //Pendientes activos
                        + " ( "
                            + " ub.user_id = pending.OWNER "
                            + " AND pending.STATUS = " + PendingRecord.STATUS.ACTIVE.getValue()
                        + " ) "
                        //Pendientes reasignados
                        + " OR ( "
                            + " ub.user_id = pending.super_owner_id "
                            + " AND pending.STATUS = " + PendingRecord.STATUS.REASSIGNED.getValue()
                        + " ) "
                        //Pendientes con responsabilidad escalada
                        + " OR ( "
                            + " ub.user_id = pending.super_owner_id "
                            + " AND pending.STATUS = " + PendingRecord.STATUS.ESCALATED.getValue()
                            + " AND apt.escalation_mode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                        + " ) "
                        //Pendientes escalados donde solo se le notificó al jefe
                        + " OR ( "
                            + " ub.user_id = pending.OWNER "
                            + " AND pending.STATUS = " + PendingRecord.STATUS.ESCALATED.getValue()
                            + " AND apt.escalation_mode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                        + " ) "
                    + " ) "
                + " GROUP BY pending_record_id, commitment_date "
            + " ) ";
    public static final String CURRENT_DATE_NO_TIME =  "DATEADD(day, DATEDIFF(day, 0, current_date()), 0)";
    
    public static final Pattern 
        PARAM_PATTERN_USER_ID = Pattern.compile(":userId"),
        PARAM_PATTERN_ALIAS = Pattern.compile(":_alias")
    ;
    
    public static final String 
        SELECT_ID = "SELECT distinct :_alias.id ",
        SELECT_ENTITY = "SELECT distinct :_alias ",
        SELECT_DTO = ""
        + "SELECT new " + PendingOperationDTO.class.getCanonicalName() + "(:userId, :_alias.id) "
    ;
    
    public static final String 
            STATUS_ACTIVE = PendingRecord.STATUS.ACTIVE.getValue().toString(),
            STATUS_ESCALATED = PendingRecord.STATUS.ESCALATED.getValue().toString(),
            STATUS_ACTIVE_ESCALATED = ""
                    + " CASE "
                        + " WHEN pending." + SqlProperties.SUPER_OWNER_ALIAS + " IS NULL THEN " + PendingRecord.STATUS.ACTIVE.getValue() 
                        + " ELSE " + PendingRecord.STATUS.ESCALATED.getValue() 
                    + " END",
            INSERT_APE_RECORD_SQL = ""
                + " INSERT INTO ape_pending_record ("
                    + " type,"
                    + " scope,"
                    + " module,"
                    + " base,"
                    + " created_at,"
                    + " status,"
                    + " deleted,"
                    + " creation_date,"
                    + " last_updated,"
                    + " unattended_days,"
                    + " escalation_level,"
                    + " scalable,"
                    + " escalation_mode,"
                    + " escalated_to,"
                    + " escalation_from_count,"
                    + " super_owner_id,"
                    + " pre_owner_id,"
                    + " owner,"
                    + " record_id,"
                    + " commitment_date,"
                    + " notice_date,"
                    + " deadline,"
                    + " reminder,"
                    + " expired"
                + " ) ",
            UPDATE_DISABLE_COUNTERS_BY_TYPE = ""
                + " UPDATE " + PendingCount.class.getCanonicalName() + " c "
                + " SET c.status = :newStatus "
                + " WHERE c.status = :status AND c.type = :type";
    
    
    public static final Map<String, String> 
        INSERT_SELECT_ACTIVE = new LinkedHashMap<>(12),
        INSERT_SELECT_ESCALATE = new LinkedHashMap<>(12)
    ;
    
    /**
     * Must specify: newStatus, status, type, recordId, scope, lastUpdated,
     * attender
     */
    public static final String UPDATE_SPECIFIC_PENDING = ""
            + " UPDATE "
            + PendingRecord.class.getCanonicalName() + " "
            + " SET status = :newStatus, "
            + " lastUpdated = :lastUpdated, "
            + " attended_by = :attender, "
            + " attended_on = :lastUpdated, "
            + " created_at = :trigger, "
            + " scope = :scope "
            + " WHERE status = :status "
            + " AND type = :type "
            + " AND recordId = :recordId ";

    /**
     * Must set :type, :status, :owner, :scope
     */
    public static final String PENDING_RECORDS_TYPE_CONDITION = " AND pnd.type = :type ";
    public static final String PENDING_RECORDS = ""
            + " FROM  " + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " WHERE "
                + " ptype.id = pnd.type"
                + PENDING_RECORDS_TYPE_CONDITION
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner IN (:owner)) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId IN (:owner)) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId IN (:owner)"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner IN (:owner)"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " )";
    /**
     * Must set :type, :status, :owner, :scope
     */
    public static final String RECORDS_EXISTS = ""
            + " SELECT pnd.id AS pending_record"
            + " FROM  " + PendingRecord.class.getCanonicalName() + " pnd "
            + " WHERE "
                + " pnd.recordId IN (:recordId)"
                + " AND pnd.type IN (:type) "
                + " AND pnd.status IN ("
                    + PendingRecord.STATUS.ACTIVE.getValue()
                    + SqlQueryParser.COMMA + PendingRecord.STATUS.ESCALATED.getValue()
                    + SqlQueryParser.COMMA + PendingRecord.STATUS.REASSIGNED.getValue()
                + " )"
                + " AND pnd.scope = :scope "
    ;
    /**
     * Must set :type, :status, :owner
     */
    public static final String SAVED_RECORDS_COUNT = ""
            + " SELECT pc.pendingCount AS pending_count"
            + " FROM  " + PendingCount.class.getCanonicalName() + " pc "
            + " WHERE pc.type = :type "
            + " AND pc.status = :status "
            + " AND pc.owner = :owner ";
    
    /**
     * Must set :status, :owner
     */
    public static final String SAVED_RECORDS_TYPES = ""
            + " SELECT pc.type"
            + " FROM  " + PendingCount.class.getCanonicalName() + " pc "
            + " WHERE pc.status = :status "
            + " AND pc.owner = :owner ";
    /**
     * Must specify: type, status
     */
    public static final String GET_PENDING_BY_TYPE = ""
            + " SELECT pnd AS pendings_by_type"
            + " FROM  " + PendingRecord.class.getCanonicalName() + " pnd "
            + " WHERE type = :type "
            + " AND status = :status ";

    private static final String DAILY_MAIL_QUERY_COLUMNS =
            " apr.entity_code as entityCode,"
            + " apr.entity_description as entityDescription,"
            + " apr.subEntity_code as subEntityCode,"
            + " apr.subEntity_description as subEntityDescription,"
            + " apr.entityTypeName,"
            + " isnull(apr.subEntityDepartmentName, apr.entityDepartmentName) as entityDepartmentName,"         // <-- Se le da prioridad a `subEntity`
            + " isnull(apr.subEntityBusinessUnitName, apr.entityBusinessUnitName) as entityBusinessUnitName,"   // <-- Se le da prioridad a `subEntity`
            + " apr.entitySourceCode,"
            + " apr.entitySourceName,"
            + " apr.commitmentDate,"
            + " apr.unattendedDays,"
            + " apr.id,"
            + " u.user_id as userId,"
            + " u.mail as userMail,"
            + " u.first_name as userName,"
            + " u.code as userCode,"
            + " u.account as userLogin,"
            + " u.version as userVersion,"
            + " apr.deadline,"
            + " apr.entity_id"
            + " <extraColumns> ";
 
    public static final String DAILY_MAIL_QUERY 
            = SqlQueryParser.SELECT + DAILY_MAIL_QUERY_COLUMNS
            + " FROM "
                + " ("
                    + " <sql>"
                + " ) apr"
                + " <ownerJoin> "
                + " , users u"
            + SqlQueryParser.WHERE
                + " <condition> "
                + SqlQueryParser.AND + "pendingTypeId = <typeId>"
                + SqlQueryParser.AND + "u.status = 1"
                + SqlQueryParser.AND
                + " ("
                    + " ( "
                        + " apr.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND apr.ownerId = u.user_id"
                    + " ) OR ("
                        + " apr.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND apr.superOwnerId = u.user_id"
                    + " ) OR ("
                        + " apr.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                        + " AND apr.superOwnerId = u.user_id"
                        + " AND apr.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) OR ("
                        + " apr.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                        + " AND apr.ownerId = u.user_id "
                        + " AND apr.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " )"
           + " ORDER by u.user_id asc, apr.id desc";
    
    public static final String DAILY_MAIL_QUERY_EXPIRED
            = SqlQueryParser.SELECT + DAILY_MAIL_QUERY_COLUMNS
            + " FROM "
                + " ("
                    + " <sql>"
                + " ) apr"
                + " <ownerJoin> "
                + " , users u"
            + SqlQueryParser.WHERE
                + " <condition> "
                + SqlQueryParser.AND +
                    " apr.STATUS = " + PendingRecord.STATUS.ATTENDED.getValue()
                + SqlQueryParser.AND +
                    " (apr.ownerId = u.user_id OR apr.superOwnerId = u.user_id) "
                + SqlQueryParser.AND + "pendingTypeId = <typeId>"
                + SqlQueryParser.AND + "u.status = 1"
           + " ORDER by u.user_id asc, apr.id desc";

    public static final String MARK_EXPIRED_MAIL_AS_COMPLETED = " "
            + " UPDATE " + PendingRecord.class.getCanonicalName() + " ape "
            + " SET ape.sendingExpiredMail = 1 "
            + " WHERE ape.id = :pendingRecordId";

    static {
        /**
         * Recordar que el siguiente mapa debe coincidir con 
         * la variable estatica INSERT_APE_RECORD_SQL
         */
        INSERT_SELECT_ACTIVE.put("status", STATUS_ACTIVE);
        INSERT_SELECT_ACTIVE.put("deleted", SqlQueryParser.ZERO);
        INSERT_SELECT_ACTIVE.put("creation_date", SqlQueryParser.CURRENT_DATE);
        INSERT_SELECT_ACTIVE.put("last_updated", SqlQueryParser.CURRENT_DATE);
        INSERT_SELECT_ACTIVE.put("unattended_days", SqlQueryParser.ZERO);
        INSERT_SELECT_ACTIVE.put("escalation_level", SqlQueryParser.ZERO);
        INSERT_SELECT_ACTIVE.put("scalable", SqlQueryParser.ONE);
        INSERT_SELECT_ACTIVE.put("escalation_mode", SqlQueryParser.NULL);
        INSERT_SELECT_ACTIVE.put("escalated_to", SqlQueryParser.NULL);
        INSERT_SELECT_ACTIVE.put("escalation_from_count", SqlQueryParser.ONE);
        INSERT_SELECT_ACTIVE.put("super_owner_id", SqlQueryParser.NULL);
        INSERT_SELECT_ACTIVE.put("pre_owner_id", SqlQueryParser.NULL);
        INSERT_SELECT_ACTIVE.put("owner", SqlProperties.OWNER_ALIAS);
        INSERT_SELECT_ACTIVE.put("record_id", SqlProperties.RECORD_ID_ALIAS);
        for (Map.Entry<String, String> entry : INSERT_SELECT_ACTIVE.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            switch(key) {
                case "owner":
                    INSERT_SELECT_ESCALATE.put(key, SqlQueryParser.MAX + "(" + value + ")");
                    break;
                case "pre_owner_id":
                    INSERT_SELECT_ESCALATE.put(key, SqlQueryParser.MAX + "(boses.user_id)");
                    break;
                case "escalation_from_count":
                    INSERT_SELECT_ESCALATE.put(key, SqlQueryParser.COUNT);
                    break;
                case "status":
                    INSERT_SELECT_ESCALATE.put(key, STATUS_ESCALATED);
                    break;
                case "super_owner_id":
                    INSERT_SELECT_ESCALATE.put(key, "boses.boss_id");                    
                    break;
                case "escalation_level":
                    INSERT_SELECT_ESCALATE.put(key, "pending.escalation_level" + SqlQueryParser.PLUS_ONE);
                    break;
                default:
                    INSERT_SELECT_ESCALATE.put(key, value);
            }
        }
    }
   
}
