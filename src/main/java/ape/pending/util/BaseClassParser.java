package ape.pending.util;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.core.ApeTable;
import ape.pending.core.BaseAPE;
import ape.pending.core.IPendingOperation;
import ape.pending.core.RootSoftAPE;
import ape.pending.core.RootSoftAPEs;
import ape.pending.core.SoftBaseAPE;
import ape.pending.core.SoftEntityId;
import ape.pending.core.StrongBaseAPE;
import ape.pending.core.StrongEntityId;
import ape.pending.core.WeakBaseAPE;
import ape.pending.core.WeakEntityId;
import ape.pending.dto.ApeEntityConfig;
import ape.pending.dto.BaseJoinDTO;
import ape.pending.dto.SoftBaseDTO;
import bnext.exception.ApeConfigurationException;
import com.google.common.collect.Lists;
import java.lang.reflect.Method;
import java.text.Annotation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javassist.NotFoundException;
import javax.persistence.Column;
import javax.persistence.Table;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import qms.framework.core.EntityModelCache;
import qms.util.EntityCommon;
import qms.util.ReflectionUtil;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 */
public class BaseClassParser {

    private static String getTableName(final Class entity) {
        final ApeTable apeTable = (ApeTable) entity.getAnnotation(ApeTable.class);
        if (apeTable != null) {
            return apeTable.name();
        }
        final Table table = (Table) entity.getAnnotation(Table.class);
        return table.name();
    }

    private static String getColumnName(final Class entity, final String column) {
        try {
            final String methodName = EntityCommon.fieldToGetter(column);
            final Method method = entity.getDeclaredMethod(methodName);
            return getMethodColumnName(method, column);
        } catch (final NoSuchMethodException | SecurityException ex) {
            Loggable.getLogger(BaseClassParser.class).error(
                    "Invalid entity '{}'",
                    new Object[]{entity.getCanonicalName()}, ex
            );
            return null;
        }
    }

    private static String getMethodColumnName(final Method method, final String column) {
        final Column entityIdColumn = method.getAnnotation(Column.class);
        if (entityIdColumn == null) {
            return column;
        } else {
            return entityIdColumn.name();
        }
    }

    private static String getColumnName(final Class entity, final String column, final Class<?> annotation) {
        try {
            final Method method = ReflectionUtil.firstMethod(entity, (Class<? extends Annotation>) annotation);
            if (method == null) {
                Loggable.getLogger(BaseClassParser.class).error(
                        "Implemente la anotación @'{}' en entity '{}'",
                        new Object[]{
                            annotation.getCanonicalName(),
                            entity.getCanonicalName()
                        }
                );
                return null;
            }
            return getMethodColumnName(method, column);
        } catch (final NotFoundException ex) {
            Loggable.getLogger(BaseClassParser.class).error(
                    "Implemente la anotación @'{}' en entity '{}'",
                    new Object[]{
                        annotation.getCanonicalName(),
                        entity.getCanonicalName()
                    },
                    ex
            );
            return null;
        }
    }
    

    private static void loadSubRecordInfo(final ApeEntityConfig info, final Class base, final Class subRecordClass) {
        final String subRecordTable = getTableName(subRecordClass);
        final String subRecordId = getColumnName(subRecordClass, "id");
        final String subRecordCode = getColumnName(subRecordClass, "code");
        final String baseTable = getTableName(base);
        final Class<?> annotation = StrongEntityId.class;
        final String recordId = info.getRecordId();
        final String baseRecordId = getColumnName(base, recordId, annotation);
        info.setSubRecordTable(subRecordTable);
        info.setSubRecordId(subRecordId);
        info.setSubRecordCode(subRecordCode);
        info.setBaseTable(baseTable);
        info.setBaseRecordId(baseRecordId);
    }

    private static ApeEntityConfig loadSoftEntityInfo(
            final Class base,
            final Class recordClass,
            final Class subRecordClass
    ) {
        final ApeEntityConfig info = loadEntityInfo(recordClass);
        loadSubRecordInfo(info, base, subRecordClass);
        final Class<?> annotation = SoftEntityId.class;
        final String subRecordId = info.getSubRecordId();
        final String baseRecordId = getColumnName(base, subRecordId, annotation);
        info.setBaseSubRecordId(baseRecordId);
        return info;
    }

    private static ApeEntityConfig loadWeakEntityInfo(
            final Class base,
            final Class recordClass,
            final Class subRecordClass
    ) {
        final ApeEntityConfig info = loadEntityInfo(recordClass);
        loadSubRecordInfo(info, base, subRecordClass);
        final Class<?> annotation = WeakEntityId.class;
        final String subRecordId = info.getSubRecordId();
        final String baseRecordId = getColumnName(base, subRecordId, annotation);
        final String baseId = getColumnName(base, "id");
        info.setBaseId(baseId);
        info.setBaseSubRecordId(baseRecordId);
        return info;
    }

    public static ApeEntityConfig loadEntityInfo(final Class entity) {
        final ApeEntityConfig info = new ApeEntityConfig();
        final String recordTable = getTableName(entity);
        final String recordId = getColumnName(entity, "id");
        final String recordCode = getColumnName(entity, "code");
        info.setRecordTable(recordTable);
        info.setRecordId(recordId);
        info.setRecordCode(recordCode);
        return info;
    }

    public static SoftBaseDTO parseSoftDTO(boolean isWeakBase, final Class<? extends SoftBaseAPE> baseClass) {
        final SoftBaseDTO dto;
        final ApeOperationType type;
        final Class[] b;
        if (isWeakBase) {
            type = ApeOperationType.WEAK;
            b = ReflectionUtil.getInterfaceTypeParameters(baseClass, WeakBaseAPE.class);
        } else {
            type = ApeOperationType.SOFT;
            b = ReflectionUtil.getInterfaceTypeParameters(baseClass, SoftBaseAPE.class);
        }
        if (b.length == 2) {
            Class soft = b[0], strong = b[1];
            final ApeEntityConfig entityConfig;
            if (isWeakBase) {
                entityConfig = loadWeakEntityInfo(baseClass, soft, strong);
            } else {
                entityConfig = loadSoftEntityInfo(baseClass, soft, strong);
            }
            dto = new SoftBaseDTO(
                    soft,
                    (Class<? extends StrongBaseAPE>) strong,
                    getRootEntityId(baseClass, soft, (RootSoftAPEs) soft.getAnnotation(RootSoftAPEs.class)),
                    getRootEntityId(baseClass, strong, (RootSoftAPEs) strong.getAnnotation(RootSoftAPEs.class)),
                    type, entityConfig);
        } else {
            Loggable.getLogger(BaseClassParser.class).error("Invalid configuration at '{}' pending! ({})", new Object[]{baseClass.getCanonicalName(), b.length});
            dto = null;
        }
        return dto;
    }

    private static String getRootEntityId(
            final Class<? extends SoftBaseAPE> baseClass,
            final Class sideClass,
            final RootSoftAPEs roots
    ) {
        if (roots != null) {
            for (final RootSoftAPE root : roots.value()) {
                if (root.mappedByClass() == baseClass) {
                    return root.mappedBy();
                }
            }
        }
        final RootSoftAPE root = (RootSoftAPE) sideClass.getAnnotation(RootSoftAPE.class);
        if (root == null) {
            Loggable.getLogger(BaseClassParser.class).error("Missing annotation @RootSoftAPE at '{}' entity!", new Object[]{sideClass.getCanonicalName()});
        } else {
            return root.mappedBy();
        }
        return null;
    }

    public static StringBuilder buildEntitySummaryQuery(IUntypedDAO dao) {
        final List<APE> apes = Lists.newArrayList(APE.values());

        final Map<String, TableConfig> tables = new HashMap<>(apes.size());
        for (final APE ape : apes) {
            try {
                final IPendingOperation operation = ApeUtil.getOperationInstance(ape, dao);
                if (operation == null) {
                    continue;
                }
                final Class<? extends BaseAPE> entityBase = operation.getEntityBase();
                if (entityBase == null) {
                    throw new ApeConfigurationException("Entity base not provided for APE " + ape.getCode());
                }
                final BaseJoinDTO baseJoin = EntityModelCache.getRecordBaseJoinDTO(
                        ape.getModule(),
                        entityBase.getCanonicalName()
                );
                if (baseJoin == null) {
                    throw new ApeConfigurationException("Base could not be built for APE " + ape.getCode());
                }
                final ApeEntityConfig entity = baseJoin.getEntityConfig();

                final String cacheKey = operation.getOperationType() + "-" + entity.getRecordTable();
                if (tables.containsKey(cacheKey)) {
                    tables.get(cacheKey).getTypes().add(ape.getCode());
                    continue;
                } else {
                    tables.put(cacheKey, new TableConfig());
                    tables.get(cacheKey).getTypes().add(ape.getCode());
                }

                final TableConfig tableConfig = tables.get(cacheKey);
                final Integer aliasCount = tables.size();

                final StringBuilder joinSb = new StringBuilder(100);
                final StringBuilder codeSb = new StringBuilder(100);
                switch (operation.getOperationType()) {
                    case STRONG:
                        codeSb
                                .append(" WHEN")
                                .append(" entity").append(aliasCount).append(".").append(entity.getRecordCode())
                                .append(" IS NOT NULL THEN")
                                .append(" entity").append(aliasCount).append(".").append(entity.getRecordCode());
                        joinSb
                                .append(" LEFT JOIN ").append(entity.getRecordTable()).append(" entity").append(aliasCount)
                                .append(" ON apeType.code IN :codes").append(" AND entity").append(aliasCount).append(".").append(entity.getRecordId())
                                .append(" = ape.record_id");
                        tableConfig.setCodeFrom(codeSb);
                        tableConfig.setJoins(joinSb);
                        break;
                    case SOFT: {
                        codeSb
                                .append(" WHEN")
                                .append(" entity").append(aliasCount).append(".").append(entity.getRecordCode())
                                .append(" IS NOT NULL THEN")
                                .append(" entity").append(aliasCount).append(".").append(entity.getRecordCode());
                        final StringBuilder subCodeSb = new StringBuilder(100);
                        subCodeSb
                                .append(" WHEN")
                                .append(" subEntity").append(aliasCount).append(".").append(entity.getSubRecordCode())
                                .append(" IS NOT NULL THEN")
                                .append(" subEntity").append(aliasCount).append(".").append(entity.getSubRecordCode());

                        joinSb
                                .append(" LEFT JOIN ").append(entity.getSubRecordTable()).append(" subEntity").append(aliasCount)
                                .append(" ON apeType.code IN :codes")
                                .append(" AND subEntity").append(aliasCount).append(".").append(entity.getSubRecordId())
                                .append(" = ape.record_id")
                                .append(" LEFT JOIN ").append(entity.getBaseTable()).append(" base").append(aliasCount)
                                .append(" ON apeType.code IN :codes")
                                .append(" AND base").append(aliasCount).append(".").append(entity.getBaseSubRecordId())
                                .append(" =").append(" subEntity").append(aliasCount).append(".").append(entity.getSubRecordId())
                                .append(" LEFT JOIN ").append(entity.getRecordTable()).append(" entity").append(aliasCount)
                                .append(" ON apeType.code IN :codes").append(" AND entity").append(aliasCount).append(".").append(entity.getRecordId())
                                .append(" =").append("base").append(aliasCount).append(".").append(entity.getBaseRecordId())
                                ;

                        tableConfig.setCodeFrom(codeSb);
                        tableConfig.setSubCodeFrom(subCodeSb);
                        tableConfig.setJoins(joinSb);
                        break;
                    }
                    case WEAK: {
                        codeSb
                                .append(" WHEN")
                                .append(" entity").append(aliasCount).append(".").append(entity.getRecordCode())
                                .append(" IS NOT NULL THEN")
                                .append(" entity").append(aliasCount).append(".").append(entity.getRecordCode());
                        final StringBuilder subCodeSb = new StringBuilder(100);
                        subCodeSb
                                .append(" WHEN")
                                .append(" subEntity").append(aliasCount).append(".").append(entity.getSubRecordCode())
                                .append(" IS NOT NULL THEN")
                                .append(" subEntity").append(aliasCount).append(".").append(entity.getSubRecordCode());
                        joinSb
                                .append(" LEFT JOIN ").append(entity.getBaseTable()).append(" base").append(aliasCount)
                                .append(" ON").append(" base").append(aliasCount).append(".").append(entity.getBaseId())
                                .append(" = ape.record_id")
                                .append(" AND apeType.code IN :codes")
                                .append(" LEFT JOIN ").append(entity.getRecordTable()).append(" entity").append(aliasCount)
                                .append(" ON").append(" entity").append(aliasCount).append(".").append(entity.getRecordId())
                                .append(" =").append(" base").append(aliasCount).append(".").append(entity.getBaseRecordId())
                                .append(" AND apeType.code IN :codes")
                                .append(" LEFT JOIN ").append(entity.getSubRecordTable()).append(" subEntity").append(aliasCount)
                                .append(" ON").append(" subEntity").append(aliasCount).append(".").append(entity.getSubRecordId())
                                .append(" =").append("base").append(aliasCount).append(".").append(entity.getBaseSubRecordId())
                                .append(" AND apeType.code IN :codes");
                        tableConfig.setCodeFrom(codeSb);
                        tableConfig.setSubCodeFrom(subCodeSb);
                        tableConfig.setJoins(joinSb);
                        break;
                    }
                    default:
                        break;
                }
            } catch(ApeConfigurationException e) {
                throw new ApeConfigurationException(e);
            } catch(Exception e) {
                throw new ApeConfigurationException("Failed to build entity summary query for " + ape.getCode(), e);
            }
        }

        final StringBuilder entityCodeSb = new StringBuilder(100);
        final StringBuilder joinSb = new StringBuilder(100);
        final StringBuilder subEntityCodeSb = new StringBuilder(100);

        entityCodeSb.append(" CASE");
        subEntityCodeSb.append(" CASE");

        for (final TableConfig tableConfig : tables.values()) {
            entityCodeSb.append(tableConfig.getCodeFrom());
            final StringBuilder subCodeFrom = tableConfig.getSubCodeFrom();
            if (subCodeFrom != null) {
                subEntityCodeSb.append(subCodeFrom);
            }
            final String codes = StringUtils.join(tableConfig.getTypes(), "','");
            joinSb.append(tableConfig.getJoins().toString().replaceAll(":codes", "('" + codes + "')"));
        }

        entityCodeSb.append(" ELSE null END as entityCode");
        subEntityCodeSb.append(" ELSE null END as subEntityCode");

        final StringBuilder entityConfigSb = new StringBuilder(500);
        entityConfigSb
                .append(" SELECT")
                .append(" ape.pending_record_id as id,")
                .append(" ape.status as status,")
                .append(" ape.commitment_date as commitmentDate,")
                .append(" ape.escalation_mode as escalationMode,")
                .append(" ape.unattended_days as unattendedDays,")
                .append(" ape.escalation_level as escalationLevel,")
                .append(" ape.scalable as scalable,")
                .append(" ape.creation_date as creationDate,")
                .append(" ape.module as module,")
                .append(" apeType.escalation_enabled as escalationEnabled,")
                .append(" apeType.code as pendingTypeCode,")
                .append(" CASE WHEN ape.super_owner_id IS NOT NULL THEN superOwner.account ELSE owner.account END as ownerAccount,")
                .append(" CASE WHEN ape.super_owner_id IS NOT NULL THEN superOwner.user_id ELSE owner.user_id END as ownerId,")
                .append(" CASE WHEN ape.super_owner_id IS NOT NULL THEN superOwner.first_name ELSE owner.first_name END as ownerName,")
                .append(" owner.account as firstOwnerAccount,")
                .append(" owner.first_name as firstOwnerName,")
                .append(" historyOwner.account as escalatedToAccount,")
                .append(" historyOwner.first_name as escalatedToName,")
                .append(" reassigned.account as reassignedFromAccount,")
                .append(" reassigned.first_name as reassignedFromName,")
                .append(entityCodeSb).append(",")
                .append(subEntityCodeSb)
                .append(" FROM ape_pending_record ape")
                .append(" INNER JOIN ape_pending_type apeType")
                .append(" ON apeType.pending_type_id = ape.type")
                .append(" LEFT JOIN users owner")
                .append(" ON owner.user_id = ape.owner")
                .append(" LEFT JOIN users superOwner")
                .append(" ON superOwner.user_id = ape.super_owner_id")
                .append(" LEFT JOIN users historyOwner")
                .append(" ON historyOwner.user_id = ape.escalated_to")
                .append(" LEFT JOIN users reassigned")
                .append(" ON reassigned.user_id = ape.reassigned_from")
                .append(joinSb);
        return entityConfigSb;
    }

}
