
package ape.pending.util;

import Framework.Config.Utilities;
import ape.pending.core.APE;
import ape.pending.core.IPendingOperation;
import ape.pending.core.PendingHelper;
import ape.pending.dto.BaseJoinDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import mx.bnext.access.Module;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.util.BindUtil;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 *
 * Los valores "pendings" y "ape" son excluyentes donde el primero tiene prioridad
 * sobre el segundo, es decir que siempre se considera solo 1 de los dos valores.
 */
public class RecordRowsHQLBuilder {
    private static final Logger LOGGER = Loggable.getLogger(RecordRowsHQLBuilder.class);
    
    private final IPendingOperation[] pendings; // <--- este valor tiene prioridad sobre "ape"
    private final BaseJoinDTO b;
    private final RecordRowsType type;
    private final APE ape;                      // <-- este valor no se utiliza cuando "pendings" tenga un valor
    private final StringBuilder joinBuilder;
    private final Map<String, Object> params;
    private final Boolean defineParameters;
    private final Module module;
    private final List<Long> recordIds;
    private final boolean includeCompleted;
    private final String owner;
    private String hql;

    public RecordRowsHQLBuilder(
            Module module,
            IPendingOperation[] pendings,
            Long[] userIds, 
            BaseJoinDTO b, 
            Class entityClass,
            String owner,
            RecordRowsType type,
            APE ape,
            List<Long> recordIds,
            boolean includeCompleted,
            Boolean defineParameters,
            Map<String, Object> params
    ) {
        this.module = module;
        this.pendings = pendings;
        this.b = b;
        this.type = type;
        this.ape = ape;
        this.defineParameters = defineParameters;
        this.params = params;
        this.recordIds = recordIds;
        this.includeCompleted = includeCompleted;
        this.owner = owner;
        this.joinBuilder = new StringBuilder(500);
        
        switch (type) {
            case ONLY_APE:
            case CROSS_APE_ENTITY:
                //@ToDo: La condición 0=0 se ocupa en DynamicFieldDAO.addWhere
                joinBuilder
                    .append(SqlQueryParser.WHERE)
                        .append("0=0")
                        .append(SqlQueryParser.AND).append("ptype.id = record.typeId")
                ;        
                break;
            case ONLY_ENTITY: 
                //@ToDo: La condición 0=0 se ocupa en DynamicFieldDAO.addWhere
                joinBuilder
                    .append(SqlQueryParser.WHERE)
                        .append("0=0");
                break;
        }
        buildTypeFilter(entityClass);
        buildUserFilter(userIds);
        buildHql();
    }
    
    private void buildHql() {
        if (b == null) {
            this.hql = "";
            LOGGER.error("BaseJoinDTO is required for this type of query.");
            return;
        }
        if (b.getColumns() == null || b.getColumns().isEmpty()) {
            this.hql = "";
            LOGGER.error("Columns are required for this type of query [{}]. for {}", type, b);
            return;
        }
        StringBuilder hqlBuilder = new StringBuilder(joinBuilder.length());
        hqlBuilder.append(" SELECT new map(");
        switch (type) {
            case CROSS_APE_ENTITY:
                hqlBuilder.append(PendingHelper.PENDING_RECORD_SELECT_MAP).append(b.getColumns())
                    .append(") FROM ").append(PendingHelper.PENDING_RECORD_FROM);
                hqlBuilder.append(b.getJoins());
                if (!b.getConditions().isEmpty()) {
                    joinBuilder
                            .append(b.getConditions());
                }
                if (!b.getCrossConditions().isEmpty()) {
                    joinBuilder
                            .append(b.getCrossConditions());
                }
                if (!b.getBaseConditions().isEmpty()) {
                    joinBuilder
                            .append(b.getBaseConditions());
                }
                if (!b.getTypeConditions().isEmpty()) {
                    joinBuilder
                            .append(b.getTypeConditions())
                    ;    
                }                
                break;
            case ONLY_APE:
                hqlBuilder.append(PendingHelper.PENDING_RECORD_SELECT_MAP).append(b.getColumns())
                    .append(") FROM ").append(PendingHelper.ONLY_APE_FROM);
                if (pendings == null || pendings.length == 0) {
                    if (!b.getBaseConditions().isEmpty()) {
                        joinBuilder
                                .append(b.getBaseConditions());
                    }
                }
                break;
            case ONLY_ENTITY:
                String joins = b.getJoins();
                if (joins.startsWith(",")) {
                    joins = joins.substring(1);
                }
                hqlBuilder
                        .append(PendingHelper.PENDING_ENTITY_SELECT_MAP).append(b.getColumns())
                        .append(") FROM ").append(joins);
                if (!b.getConditions().isEmpty()) {
                    joinBuilder
                            .append(b.getConditions());
                }
                if (!b.getTypeConditions().isEmpty()) {
                    joinBuilder
                            .append(b.getTypeConditions());    
                }                
                break;
        }
        if (recordIds != null && !recordIds.isEmpty()) {
                joinBuilder
                        .append(SqlQueryParser.AND).append(
                                BindUtil.bindFilterList(owner + ".id", recordIds, true, defineParameters, params)
                        )
                ;
        }
        String HQL = hqlBuilder.toString();
        if (HQL.matches("(?i)^.+?\\sWHERE\\s.+")) {
            int whereIndex = HQL.toLowerCase().indexOf(SqlQueryParser.WHERE);
            hqlBuilder.delete(whereIndex, whereIndex + SqlQueryParser.WHERE.length());
            hqlBuilder.insert(whereIndex, joinBuilder.append(SqlQueryParser.AND).toString());
        } else {
            hqlBuilder.append(joinBuilder);
        }
        this.hql = applyNamedParameters(hqlBuilder.toString())
                .replaceAll("AND 0=0", "");
    }

    private void buildTypeFilter(Class entityClass) {
        switch (type) {
            case CROSS_APE_ENTITY:
            case ONLY_APE:
                if (pendings != null && pendings.length > 0) {
                    joinBuilder
                        .append(SqlQueryParser.AND).append("ptype.id IN (")
                            .append(PendingHelper.getTypeIdFilter(pendings, defineParameters, params))
                        .append(')')
                    ;
                } else {
                    if (ape == null) {
                        joinBuilder
                            .append(SqlQueryParser.AND).append("ptype.code IN (")
                                .append(PendingHelper.getTypeCodeFilter(entityClass, defineParameters, params))
                                .append(')')
                        ;
                    } else {
                        if (defineParameters) {
                            joinBuilder
                                .append(SqlQueryParser.AND).append("ptype.code = :typeCode")
                            ;
                            params.put("typeCode", ape.getCode());
                        } else {
                            joinBuilder
                                .append(SqlQueryParser.AND).append("ptype.code = '").append(ape.getCode()).append("'")
                            ;

                        }
                    }
                }
                break;
        }
    }

    private void buildUserFilter(Long... userIds) {
        switch (type) {
            case CROSS_APE_ENTITY:
            case ONLY_APE:
                if (userIds != null) {
                    if (userIds.length == 0 || (userIds.length == 1 && userIds[0] == null)) {
                        LOGGER.error("Tried to fill set a null  'LoggedUser' ID value.");
                        return;
                    }
                    joinBuilder
                            .append(
                                PendingHelper.prepareFilterRecordsByUser(
                                    userIds,
                                    includeCompleted,
                                    defineParameters,
                                    params,
                                    pendings
                                )
                            );
                    break;
                }
        }
    }

    public String getHql() {
        return hql;
    }

    private String applyNamedParameters(final String hql) {
        if (!defineParameters || module == null) {
            return hql;
        }
        String patchHql = hql;
        switch (type) {
            case ONLY_APE:
            case CROSS_APE_ENTITY:
                    if (module != null) {
                        final String moduleApeKey = "'" + module.getKey() + "' = record.module";
                        if (hql.contains(moduleApeKey)) {
                            patchHql = patchHql.replace(moduleApeKey, "record.module = :moduleApeKey");
                            params.put("moduleApeKey", module.getKey());
                        } else {
                            if (LOGGER.isTraceEnabled()) {
                                LOGGER.trace("APE module could not be replaced as parameter for [{}].", moduleApeKey);
                            }
                        }
                    }
                    if (pendings != null && pendings.length > 0) {
                        final Class classEntity = pendings[0].getEntityBase();
                        final Set<String> types = new LinkedHashSet<>(ApeUtil.getCodes(module, classEntity, Utilities.getUntypedDAO()));
                        final List<String> typesCodes = new ArrayList<>(types);
                        Collections.sort(typesCodes);
                        typesCodes.add("X");
                        final String apeTypesCodesKey = BindUtil.parseFilterList("ptype.code", typesCodes, false);
                        if (patchHql.contains(apeTypesCodesKey)) {
                            patchHql = patchHql.replace(apeTypesCodesKey, "ptype.code IN (:apeTypesCodesKey)");
                            params.put("apeTypesCodesKey", typesCodes);
                        } else {
                            if (LOGGER.isTraceEnabled()) {
                                LOGGER.trace("APE type codes could not be replaced as parameter [{}].", apeTypesCodesKey);
                            }
                        }
                    }
                break;
        }
        return patchHql;
    }
    
    
}
