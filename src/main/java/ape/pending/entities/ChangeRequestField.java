package ape.pending.entities;

import Framework.Config.DomainObject;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "ape_change_request_field")
public class ChangeRequestField extends DomainObject {

    private static final long serialVersionUID = -1504321047106252995L;

    private String fieldName;
    private String fieldValueOld;
    private String fieldValueNew;
    private String fieldValueNewReadOnly;
    private String fieldType;

    private ChangeRequest changeRequest;


    public ChangeRequestField() { }

    @Id
    @Column(name = "change_request_field_id", precision = 19)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "field_name")
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    @Column(name = "field_value_old")
    public String getFieldValueOld() {
        return fieldValueOld;
    }

    public void setFieldValueOld(String fieldValueOld) {
        this.fieldValueOld = fieldValueOld;
    }

    @Column(name = "field_value_new")
    public String getFieldValueNew() {
        return fieldValueNew;
    }

    public void setFieldValueNew(String fieldValueNew) {
        this.fieldValueNew = fieldValueNew;
    }

    @Column(name = "field_value_new_readonly")
    public String getFieldValueNewReadOnly() {
        return fieldValueNewReadOnly;
    }

    public void setFieldValueNewReadOnly(String fieldValueNewReadOnly) {
        this.fieldValueNewReadOnly = fieldValueNewReadOnly;
    }

    @Column(name = "field_type")
    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    @JoinColumn(name = "change_request_id")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    public ChangeRequest getChangeRequest() {
        return changeRequest;
    }

    public void setChangeRequest(ChangeRequest changeRequest) {
        this.changeRequest = changeRequest;
    }

}
