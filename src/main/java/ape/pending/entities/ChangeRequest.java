package ape.pending.entities;

import DPMS.Mapping.Workflow;
import Framework.Config.StandardEntity;
import ape.pending.dto.ChangeRequestDto;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "ape_change_request")
public class ChangeRequest extends StandardEntity<ChangeRequest> implements IAuditable {

    private static final long serialVersionUID = -1504321047106432995L;

    // Records
    private Long pendingRecordId;
    private Long recordId;
    private Long workflowId;
    private String moduleKey;
    private Workflow workflow;              // <-- updatable/insertable = false
    private List<ChangeRequestField> fields;
    // StandardEntity
    private Integer status;
    private Integer deleted;
    private String code;
    private String description;
    // IAuditable
    private Long createdBy;
    private Long lastModifiedBy;
    private Date createdDate;
    private Date lastModifiedDate;


    public ChangeRequest() { }

    public ChangeRequest(
        PendingRecord record,
        ChangeRequestDto changes
    ) {
        this.pendingRecordId = record.getId();
        this.recordId = record.getRecordId();
        this.moduleKey = record.getModule();
        this.description = changes.getComment();
        this.fields = changes.getChanges();
    }



    @Id
    @Column(name = "change_request_id", precision = 19, scale = 0)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "record_id")
    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    @Column(name = "module_key")
    public String getModuleKey() {
        return moduleKey;
    }

    public void setModuleKey(String moduleKey) {
        this.moduleKey = moduleKey;
    }

    @JoinColumn(name = "workflow_id", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public Workflow getWorkflow() {
        return workflow;
    }

    public void setWorkflow(Workflow workflow) {
        this.workflow = workflow;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(fetch = FetchType.EAGER, mappedBy = "changeRequest", cascade = {CascadeType.ALL})
    public List<ChangeRequestField> getFields() {
        return fields;
    }

    public void setFields(List<ChangeRequestField> fields) {
        this.fields = fields;
    }

    @Column(name = "pending_record_id")
    public Long getPendingRecordId() {
        return pendingRecordId;
    }

    public void setPendingRecordId(Long pendingRecordId) {
        this.pendingRecordId = pendingRecordId;
    }

    @Column(name = "workflow_id")
    public Long getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }
    

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            this.status = 1;
        } else {
            this.status = status;
        }
    }

    @Column(name = "deleted")
    @JsonIgnore
    public Integer getDeleted() {
        return this.deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Override
    @JsonIgnore
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_date", updatable = false)
    @Override
    @CreatedDate
    @JsonIgnore
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return this.createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @JsonIgnore
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return this.createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @JsonIgnore
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
