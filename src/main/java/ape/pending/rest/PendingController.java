package ape.pending.rest;

import Framework.Config.Utilities;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.PendingDataSource;
import ape.pending.dto.ChangeRequestDto;
import ape.pending.dto.PendingFeasibleDto;
import ape.pending.dto.PendingRecordDto;
import ape.pending.entities.ChangeRequest;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import ape.pending.exception.DuplicatedChangeRequestException;
import ape.pending.util.PendingRecordWorkload;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.ehcache.config.builders.ExpiryPolicyBuilder.TriFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.activity.core.ActivityUtil;
import qms.activity.core.ActivityUtil.ActivityDependency;
import qms.activity.entity.Activity;
import qms.activity.pending.ActivityPendingDataSource;
import qms.activity.util.CommitmentTask;
import qms.framework.entity.CalendarTable;
import qms.framework.entity.Owner;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.util.GridFilter;
import qms.util.ModuleUtil;
import qms.util.OwnerUtil;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Lazy
@RestController
@RequestMapping("pendings")
public class PendingController extends Loggable {


    @Autowired
    @Qualifier("PendingRecordDAO")
    private IPendingRecordDAO dao;

    @PostMapping()
    @RequestMapping({"change-request/{pendingRecordId}"})
    public ResponseEntity<String> changeRequest(
        final @PathVariable(value = "pendingRecordId", required = false) Long pendingRecordId,
        final @RequestBody ChangeRequestDto changes
    ) {
        final PendingRecord record = dao.HQLT_findById(pendingRecordId);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final Module module = ModuleUtil.fromKey(record.getModule());
        final String pendingTypeCode = record.getPendingType().getCode();
        if (module == null) {
            throw new RuntimeException("Invalid catalog module for pendingRecord: " + pendingRecordId);
        }
        final Module submodule = this.getSubModule(module, pendingTypeCode);
        // Validación de solicitudes de cambio existentes
        final Long existingRequestId = dao.HQL_findLong(""
            + " SELECT max(c.id) "
            + " FROM " + ChangeRequest.class.getCanonicalName() + " c "
            + " WHERE "
                + " c.createdBy = " + loggedUser.getId()
                + " AND c.recordId = " + record.getRecordId()
                + " AND c.moduleKey = :moduleKey "
                + " AND c.status = " + ChangeRequest.STATUS.ACTIVE.getValue()
        , "moduleKey", submodule.getKey()
        );
        if (existingRequestId != null) {
            throw new DuplicatedChangeRequestException(""
                + " Theres already an ACTIVE change request for " + submodule.getKey()
                + " from this user to this record: " + record.getRecordId()
                + " ${existingRequestId: " + existingRequestId + "}"
                + ". "
            );
        }
        // Generación de flujo de autorización
        final List<Owner> autorizators = new ArrayList<>(1);
        switch (submodule) {
            case ACTIVITY:
                UserRef createdBy = (UserRef) dao.HQL_findSimpleObject(""
                    + " SELECT u "
                    + " FROM "
                        + Activity.class.getCanonicalName() + " a "
                        + "," + UserRef.class.getCanonicalName() + " u "
                    + " WHERE "
                        + " a.id = :recordId"
                        + " AND u.id = a.createdBy ",
                    ImmutableMap.of("recordId", record.getRecordId()),
                    true,
                    CacheRegion.ACTIVITY,
                    0
                );
                autorizators.add(
                    OwnerUtil.asOwner(dao, createdBy, loggedUser)
                );
                break;
        }
        // Se inhabilita el pendiente
        if (dao.saveChangeRequest(module, changes, record, autorizators, loggedUser)) {
            return new ResponseEntity<>(HttpStatus.CREATED);
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @GetMapping()
    @RequestMapping({"data-source/{pendingRecordId}"})
    public PendingRecordDto dataSource(
        final @PathVariable(value = "pendingRecordId", required = true) Long pendingRecordId
    ) throws QMSException {
        final PendingRecord record = dao.HQLT_findById(pendingRecordId);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final Module module = ModuleUtil.fromKey(record.getModule());
        String pendingTypeCode = record.getPendingType().getCode();
        if (module == null) {
            throw new RuntimeException("Invalid catalog module for pendingRecord: " + pendingRecordId);
        }
        Module submodule = getSubModule(module, pendingTypeCode);
        // Catalogos del submodule
        switch (submodule) {
            case ACTIVITY:
                return ActivityPendingDataSource.getDataSource(
                    submodule, record.getRecordId(), loggedUser
                );
        }
        return null;
    }

    public static Module getSubModule(final Module module, String pendingTypeCode) {
        // Se revisan submodulos
        switch (module) {
            case PLANNER:
            case FORMULARIE:
            case AUDIT:
            case MEETING:
            case ACTION:
                APE ape;
                switch (module) {
                    case ACTION:
                        // parche para corregir el modulo de "finding" (action)
                        ape = APE.fromCode(pendingTypeCode.replace("FINDING-", ""));
                        break;
                    case FORMULARIE:
                        // parche para corregir el modulo de "formularie" (action)
                        ape = APE.fromCode(pendingTypeCode.replace("FORM-", ""));
                        break;
                    default:
                        ape = APE.fromCode(pendingTypeCode.replace(module.name() + "-", ""));
                        break;
                }
                if (ape != null) {
                    return ape.getModule();
                }
        }
        return module;
    }


    @GetMapping()
    @RequestMapping({"count"})
    public Integer dataSourcePendingCount() {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("loggedUserId", SecurityUtils.getLoggedUserId());
        final Integer result = dao.HQL_findSimpleInteger(""
            + " SELECT count(pnd.id) "
            + " FROM  " + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " WHERE "
                + " pnd.deleted = 0"
                + " AND ptype.id = pnd.type"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = :loggedUserId) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = :loggedUserId) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                        + " AND pnd.superOwnerId = :loggedUserId"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue()
                        + " AND pnd.owner = :loggedUserId"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " )",
            params
        );
        return result;
    }

    // Se executa sin transacción para no tener transacciones tan lentas
    @GetMapping()
    @RequestMapping({"data-source"})
    public PendingDataSource dataSourcePendingAll() {
        return PendingRecordWorkload.dataSourcePendingAll(SecurityUtils.getLoggedUser(), dao);
    }

    @GetMapping()
    @RequestMapping({"is-feasible/{pendingTypeCode}/{pendingRecordId}"})
    public PendingFeasibleDto isPendingFeasible(
        final @PathVariable(value = "pendingTypeCode", required = true) String pendingTypeCode,
        final @PathVariable(value = "pendingRecordId", required = true) Long pendingRecordId
    ) {
        return dao.isFeasible(pendingTypeCode, pendingRecordId, SecurityUtils.getLoggedUserId());
    }
    
    @GetMapping()
    @RequestMapping({"dataChart/{startDate}/{endDate}"})
    public List<Map<String, Object>> dataSourceChart(
        @PathVariable(value = "startDate", required = false) String startDate,
        @PathVariable(value = "endDate", required = false) String endDate
    ) {
        if (startDate == null || endDate == null) {
           return null;
        }
        Date pendingStartDate = Utilities.formatStrToDate(startDate, "yyyy-MM-dd");
        Date pendingEndDate = Utilities.formatStrToDate(endDate, "yyyy-MM-dd");
        
        if (pendingStartDate == null || pendingEndDate == null) {
            throw new RuntimeException("Input date '" + startDate + "' is invalid, format should be 'yyyy-MM-dd hh:mm:ss'.");
        }
        
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        Map params = new HashMap();
        params.put("startDate", pendingStartDate);
        params.put("endDate", pendingEndDate);
        params.put("userId", loggedUser.getId());
        
        String query = ""
                    + " SELECT new map ( "
                    + " d.dateValue AS dateValue, "
                    + " COUNT(pnd.recordId) AS countPendings, "
                    + " pnd.module AS modulo "
                    + " ) "
                    + " FROM " + CalendarTable.class.getCanonicalName() + " d "
                    + " LEFT OUTER JOIN " + PendingRecord.class.getCanonicalName() + " pnd " 
                    + " ON CONVERT(DATE, pnd.commitmentDate) <= :endDate AND CONVERT(DATE, pnd.commitmentDate) <= d.dateValue"
                    + " WHERE " 
                    + " d.dateValue >= :startDate AND d.dateValue <= :endDate "
                    + " AND pnd.deleted = 0"
                    + " AND pnd.owner = :userId AND pnd.deleted = 0 "
                    + " and CONVERT(DATE, pnd.commitmentDate) <= d.dateValue "
                    + " and (CONVERT(DATE, pnd.attendedOn) > d.dateValue OR pnd.attendedOn IS NULL) "
                    + " GROUP BY pnd.module, d.dateValue "
                    + " ORDER BY d.dateValue ";
                
        List<Map<String, Object>> results = dao.HQL_findByQuery(query, params);

        return results;
    }
    
    @GetMapping()
    @RequestMapping({"implementActivities/{activityId}"})
    public GridInfo implementActivities(
                    @PathVariable(value = "activityId", required = true) Long activityId
                    ,@RequestBody GridFilter filter ) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("a.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        final Function<Long, String> getCondition = var -> {
            return "a.id = :conditionId".replace(":conditionId", var.toString());
        };
        return  ActivityUtil.getActivityDependency(
                        ActivityDependency.IMPLEMENTATION,
                        activityId,
                        null,
                        null,
                        CommitmentTask.IMPLEMENTATION,
                        dao,
                        SecurityUtils.getLoggedUser(),
                        filter,
                        getCondition,
                        null
                );
    }
    
    @GetMapping()
    @RequestMapping({"verifiableActivities", "verifiableActivities/{selectedUser}"})
    public GridInfo verifiableActivities(
            @PathVariable(value = "selectedUser", required = false) Long selectedUser
            ,@RequestBody GridFilter filter ) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("a.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        final Function<Long, String> getImpCondition = var -> {
            return "1 = 1";
        };
        final TriFunction<Long, String, String, String> getInfoCondition = (var, actList, userId) -> {
            String condition = "verifier.userId = " + userId;
            if (var != null) {
                actList = actList.replace(":loggedUserId", var.toString());
                condition += " AND im.id NOT IN ( " + actList + " )";
            }
            return condition;
        };
        return ActivityUtil.getActivityDependency(
            ActivityDependency.IMPLEMENTATION,
            selectedUser,
            null,
            null,
            CommitmentTask.IMPLEMENTATION,
            dao,
            SecurityUtils.getLoggedUser(),
            filter,
            getImpCondition,
            getInfoCondition
        );
    }
}
