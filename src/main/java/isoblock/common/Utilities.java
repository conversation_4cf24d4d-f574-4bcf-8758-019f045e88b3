package isoblock.common;

//Declaracion de Imports

import bnext.licensing.LicenseUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CharsetEncoder;
import java.nio.charset.CodingErrorAction;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import mx.bnext.core.daemon.util.IBnextThread;
import mx.bnext.licensing.Schema;
import org.glowroot.agent.api.Instrumentation;
import org.owasp.encoder.Encode;
import org.slf4j.Logger;
import qms.framework.core.Mailer;
import qms.util.HTMLEncoding;

public class Utilities extends Properties {
    //Declaracion de Variables y Objetos
    private static java.text.SimpleDateFormat formatDate = new java.text.SimpleDateFormat("MM/dd/yyyy");
    private static java.text.SimpleDateFormat formatDate2 = new java.text.SimpleDateFormat("dd/MM/yyyy");
    private static java.text.SimpleDateFormat formatTime = new java.text.SimpleDateFormat("hh:mm a");
    private static java.text.SimpleDateFormat formatTimestamp = new java.text.SimpleDateFormat("dd/MM/yyyy hh:mm a");
    private static java.text.SimpleDateFormat formatMonth = new java.text.SimpleDateFormat("MMM - yyyy");
    private static java.text.SimpleDateFormat formatSql = new java.text.SimpleDateFormat("yyyy-MM-dd");
    private static java.text.SimpleDateFormat formatSqlTimestamp = new java.text.SimpleDateFormat("yyyyMMdd hh:mma");
    private java.text.NumberFormat formatoNumero;
    private static java.text.DecimalFormat formatId = new java.text.DecimalFormat("0000");
    private static java.text.DecimalFormat formatVer = new java.text.DecimalFormat("00");
    private HttpServletRequest requests;
    private String filepath = "";
    private Object bean = null;
    public String sesionusuarioid = "";
    public Logger logger = null;
    //Obtencion de un arreglo de parametros en el objeto request
    //Se lee el ResourceBundle para el idioma
    
    /**
     * variables para variables de params
     * - Se usa para pasar una cantidad dinamica N de parametros
     * - Se utilizan en conjunto con la funcion JavaScript
     * getListParamsKeyVal(list_id) ubicada en la ruta "../templates/superior.tem"
     */
    private ArrayList<String> paramsKeyHolder = new ArrayList<String>();
    /**
     * Valores
     */
    private ArrayList<String> paramsValues = new ArrayList<String>();
    /**
     * Llaves
     */
    private ArrayList<String> params = new ArrayList<String>();
    private String[] paramKeysHolder = new String[]{};
    private String[] paramKeys = new String[]{};
    private String[] paramValues = new String[]{};
    //SOLUCION BUG STRUTS
    private String strParamKeysHolder = "";
    private String strParamKeys = "";
    private String strParamValues = "";

    public Utilities() {
        logger = super.getLogger();
        formatoNumero = java.text.NumberFormat.getInstance();
        formatoNumero.setMaximumFractionDigits(2);
        formatoNumero.setMinimumFractionDigits(2);
    }

    public Utilities(boolean c) {
        if (bean == null) {
        } else {
            bean = null;
        }

    }

    public void validarConfiguracion() {
        getLogger().trace("isoblock.common.Utilities.validarConfiguracion()");
        //Licencias
        setLicences();
        //VARIABLES DE VALIDACION
        VISOR_PDF_OK = checkVisorPdf();
        //getLogger().trace("end validar....");
    }

    /**
     * Corrige cualqueir inconsistencia de parametros, desabilita la
     * funcionalidad si es necesario. (En caso de que no este bien configurada)
     *
     * @return Regreso vacio en caso de que todo este OK, regreso la falla en
     * caso contrario.
     */
    protected String checkVisorPdf() {
        getLogger().trace("isoblock.common.Utilities.checkVisorPdf()");
        String url = requests.getRequestURL().toString();
        String uri = "http://" + SITE_HOST + "/" + SITE_FOLDER_NAME + "/";
        if (!url.contains(uri)) {
            if ((url.contains("localhost") && !uri.contains("localhost"))
                    || (uri.contains("localhost") && url.contains("localhost"))) {
                return "LOCALHOST";
            } else {
                return "FAIL!";
            }
        }
        return "";
    }

    public String formatDate(java.util.Date fecha) {
        return formatDate.format(fecha);
    }
    
    public Date formatDate(String fecha) {
        try {
            return formatDate.parse(fecha);
        } catch (ParseException ex) {
            getLogger().error("{}",ex);
        }
        return null;
    }

    public String formatDate2(java.util.Date fecha) {
        String r = "";
        try {
            r = formatDate2.format(fecha);
            //getLogger().trace("Fecha bien"+fecha);
        } catch (Exception e) {
            getLogger().error("Stack trace: ",e);
        }
        return r;
    }
    
    /*La función para obtener las licencias*/
    protected void setLicences() {
        try {
            final List<Schema> schemas = LicenseUtil.getCurrentLicenseSchemas();
            if (schemas.isEmpty()) {
                LICENSES = 0;
            } else {
                LICENSES = Integer.parseInt(schemas.get(0).getSeats());
            }
        } catch (Exception e) {
            getLogger().warn("License file is not correctly setted up.");
            LICENSES = 0;
        }
    }

    /**
     * Formatea fechas en formato timestamp.
     *
     * Este método cambia el formato de timestamp a el siguiente: MM/DD/AAAA
     *
     * <AUTHOR> ISO
     * @param str Recibe un string con formato time stamp.
     * @return String Devuelve un String con el formato de fecha deseado.
     */
    public String getFechaFormateada(String str) {
        if(str.isEmpty()) {
            return Framework.Config.Utilities.EMPTY_STRING;
        }
        try {
            if(str.length() >= 19) {
                str = str.substring(0, 19) + ".000000-00";
            } else if(str.length() >= 10) {
                str = str.substring(0, 10) + " 00:00:00.000000-00";
            }
            if(str.length() >= 22) {
                str = str.substring(0, 22);
            }
            Date ddd = new Date(java.sql.Timestamp.valueOf(str).getTime());
            return this.formatDate2(ddd);
        } catch (Exception e) {
            getLogger().error("The date '{}' is invalid for parsing.", str, e);
        }
        return Framework.Config.Utilities.EMPTY_STRING;
    }

    public java.util.Date sqlToDate(String fecha) {
        java.util.Date date = null;
        try {
            date = formatSql.parse(fecha);
        } catch (Exception e) {
            getLogger().error("Ocurrió un error al convertir la fecha." ,e);
        }
        return date;
    }

    public String getTimeFormateada(String timeParam) {
        // Regresa la hora en formato AM|PM
        java.sql.Timestamp ttt = java.sql.Timestamp.valueOf("2004-06-28 " + timeParam + ".00000");
        java.util.Date ddd = new java.util.Date(ttt.getTime());
        return formatTime(ddd);
    }

    public static String formatSecClave(int numero) {
        return formatId.format(numero);
    }

    public String formatDocVersion(int num) {
        return formatVer.format(num);
    }

    public String formatDateString(java.util.Date fecha) {
        Calendar fechaTemp = Calendar.getInstance();
        fechaTemp.setTime(fecha);
        return fechaTemp.get(Calendar.DATE) + "-" + shortMonthDate(String.valueOf(fechaTemp.get(Calendar.MONTH) + 1)) + "-" + fechaTemp.get(Calendar.YEAR);
    }

    public String formatTime(java.util.Date fecha) {
        return formatTime.format(fecha);
    }

    public String formatMonth(java.util.Date fecha) {
        return formatMonth.format(fecha);
    }

    public String convertTimestampToSql(String fecha) {
        return formatSqlTimestamp.format(stringToCalendarTimestamp(fecha).getTime());
    }

    public String convertTimestampToSql(java.util.Date fecha) {
        return formatSqlTimestamp.format(fecha);
    }

    public String convertDateToSql(String fecha) {
        return formatSql.format(stringToCalendar(fecha).getTime());
    }

    public String convertDateToSql(java.util.Date fecha) {
        return formatSql.format(fecha);
    }

    public String[] parameters(String str, HttpServletRequest request) {
        String[] values = request.getParameterValues(str);
        if (values == null) {
            return new String[0];
        }
        return values;
    }

    //Convercion de tipo String a Entero.
    public int toInteger(String num) {
        try {
            return Integer.parseInt(num);
        } catch (Exception e) {
            getLogger().error("There was an error parsing {}", num);
            return 0;
        }
    }

    //Obtencion de un parametro en el objeto request
    public String parameter(String str, HttpServletRequest request) {
        String str_aux = "";
        try {
            str_aux = request.getParameter(str);
        } catch (Exception e) {
            getLogger().error("There was an error",e);
            str_aux = "";
        }
        if (str_aux == null) {
            str_aux = "";
        }
        return str_aux;
    }

    public String safeUrl(final String name, final HttpServletRequest request) {
        final String value = parameter(name, request);
        if (value == null || value.isEmpty()) {
            return value;
        }
        final String safeUrl = Encode.forUriComponent(value);
        return decodeURL8(safeUrl);
    }

    /**
     * regresa parametros del request que se coloco con "setRequest()" se agrego
     * para continuar con migracion de modulo de encuestas
     *
     * @param str
     * @return
     */
    public String[] parameters(String str) {
        String[] values = requests.getParameterValues(str);
        if (values == null) {
            return new String[0];
        }
        return values;
    }

    /**
     * regresa el parametro del request que se coloco con "setRequest()" se
     * agrego para continuar con migracion de modulo de encuestas
     *
     * @param str
     * @return
     */
    public String parameter(String str) {
        String str_aux = "";
        try {
            str_aux = requests.getParameter(str);
        } catch (Exception e) {
            getLogger().error("There was an error",e);
            str_aux = "";
        }
        if (str_aux == null) {
            str_aux = "";
        }
        return str_aux;
    }

    public static Date stringToDate(String fecha) {
      Date d = new Date();
      d.setTime(stringToCalendar(fecha).getTimeInMillis());
      return d;
    }
    public static Calendar stringToCalendar(String fecha) {

        Calendar regreso = null;
        try {
            regreso = Calendar.getInstance();
            regreso.setTime(formatDate.parse(fecha));
            regreso.set(Calendar.HOUR, 0);
            regreso.set(Calendar.MINUTE, 0);
            regreso.set(Calendar.SECOND, 0);
            regreso.set(Calendar.MILLISECOND, 0);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return regreso;
    }

    public static Calendar stringToCalendarTimestamp(String fecha) {

        Calendar regreso = null;
        try {
            regreso = Calendar.getInstance();
            regreso.setTime(formatTimestamp.parse(fecha));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return regreso;
    }

    public boolean esMenorAFechaActual(java.util.Date fecha) {
        GregorianCalendar gcActual = new GregorianCalendar();
        gcActual.set(GregorianCalendar.HOUR_OF_DAY, 0);
        gcActual.set(GregorianCalendar.MINUTE, 0);
        gcActual.set(GregorianCalendar.SECOND, 0);
        gcActual.set(GregorianCalendar.MILLISECOND, 0);
        GregorianCalendar gcComparar = new GregorianCalendar();
        gcComparar.set(GregorianCalendar.HOUR_OF_DAY, 0);
        gcComparar.set(GregorianCalendar.MINUTE, 0);
        gcComparar.set(GregorianCalendar.SECOND, 0);
        gcComparar.set(GregorianCalendar.MILLISECOND, 0);
        gcComparar.setTime(fecha);

        getLogger().trace("Comparar: " + gcComparar);
        getLogger().trace("Actual: " + gcActual);

        return !gcComparar.after(gcActual);
    }

    public String[] getSelection(int rol) {
        String[] selected = new String[15];
        //getLogger().trace("selected1");
        for (int i = 0; i < 15; i++) {
            selected[i] = "";
        }
        switch (rol) {
            case 1:
                selected[1] = " selected";
                break;
            case 2:
                selected[2] = " selected";
                break;
            case 3:
                selected[3] = " selected";
                break;
            case 4:
                selected[4] = " selected";
                break;
            case 5:
                selected[5] = " selected";
                break;
            case 6:
                selected[6] = " selected";
                break;
            case 7:
                selected[7] = " selected";
                break;
            case 8:
                selected[8] = " selected";
                break;
            case 9:
                selected[9] = " selected";
                break;
            case 10:
                selected[10] = " selected";
                break;
            case 11:
                selected[11] = " selected";
                break;
            case 12:
                selected[12] = " selected";
                break;
            case 13:
                selected[13] = " selected";
                break;
            case 14:
                selected[14] = " selected";
                break;
            case 15:
                selected[15] = " selected";
                break;
            default:
                selected[0] = " selected";
                break;

        }
        return selected;
    }

    public static int getDiasParaFinalizar(String fecha1) {

        int diferencia = 0;

        GregorianCalendar g1 = new GregorianCalendar();
        GregorianCalendar g2 = new GregorianCalendar();

        try {
            if (fecha1 != null) {
                g1.setTime(formatDate.parse(fecha1));
            }
        } catch (Exception e) {
            System.out.println("Las fechas no se encuentran en el formato correcto");
            return 0;
        }

        System.out.println(g1);
        System.out.println(g2);

        g1.clear(Calendar.MILLISECOND);
        g1.clear(Calendar.SECOND);
        g1.clear(Calendar.MINUTE);
        g1.clear(Calendar.HOUR_OF_DAY);
        g1.clear(Calendar.HOUR);
        g1.clear(Calendar.AM_PM);
        g2.clear(Calendar.MILLISECOND);
        g2.clear(Calendar.SECOND);
        g2.clear(Calendar.MINUTE);
        g2.clear(Calendar.HOUR_OF_DAY);
        g2.clear(Calendar.HOUR);
        g2.clear(Calendar.AM_PM);

        System.out.println(g1);
        System.out.println(g2);

        if (g1.equals(g2)) {
            return 0;
        }

        //Se detecta cual es primero para poder recorrer día por día
        GregorianCalendar gc1, gc2;
        if (g2.after(g1)) {
            gc2 = (GregorianCalendar) g2.clone();
            gc1 = (GregorianCalendar) g1.clone();
        } else {
            gc2 = (GregorianCalendar) g1.clone();
            gc1 = (GregorianCalendar) g2.clone();
        }

        while (gc1.before(gc2)) {
            gc1.add(Calendar.DATE, 1);
            diferencia++;
        }


        if (g1.before(g2)) {
            return -diferencia;
        } else {
            return diferencia;
        }
    }

    public static String comboYear(int cantidad, String selected) {
        return comboYear(cantidad, selected, 0);
    }

    public static String comboYear(int cantidad, String selected, int medio) {
        java.util.Calendar fechaActual = java.util.Calendar.getInstance();
        fechaActual.setTime(new java.util.Date());
        int anio = fechaActual.get(Calendar.YEAR);
        String regreso = "";
        int seleccionado = 0;
        if (selected != null && !selected.equals("")) {
            seleccionado = Integer.parseInt(selected);
        } else {
            seleccionado = anio;
        }
        if (cantidad > 0) {
            for (int i = cantidad - medio; i >= 0 - medio; i--) {
                if ((anio - i) == seleccionado) {
                    regreso += "<option value='" + (anio - i) + "' selected>" + (anio - i) + "\n";
                } else {
                    regreso += "<option value='" + (anio - i) + "'>" + (anio - i) + "\n";
                }
            }
        } else {
            for (int i = 0 - medio; i <= (cantidad * -1) - medio; i++) {
                if ((anio + i) == seleccionado) {
                    regreso += "<option value='" + (anio + i) + "' selected>" + (anio + i) + "\n";
                } else {
                    regreso += "<option value='" + (anio + i) + "'>" + (anio + i) + "\n";
                }
            }
        }
        return regreso;
    }

    public String comboWeeks(String mes, String anio, String semana) {
        String regreso = "";
        Calendar fecha = Calendar.getInstance();
        Calendar fechaFin = Calendar.getInstance();
        fecha.setTime(new java.util.Date());
        fechaFin.setTime(fecha.getTime());
        fecha.set(Calendar.DATE, 1);
        fechaFin.set(Calendar.DATE, 1);
        getLogger().trace("date {}",fecha.getTime());
        if (mes.equals("")) {
            mes = String.valueOf(fecha.get(Calendar.MONTH) + 1);
            anio = String.valueOf(fecha.get(Calendar.YEAR));
        }
        fecha.set(Calendar.MONTH, Integer.parseInt(mes) - 1);
        fecha.set(Calendar.YEAR, Integer.parseInt(anio));
        fechaFin.set(Calendar.MONTH, Integer.parseInt(mes));
        fechaFin.set(Calendar.YEAR, Integer.parseInt(anio));

        getLogger().trace("fecha {}",fecha.getTime());

        fecha.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        fecha.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);

        getLogger().trace("fecha {}",fecha.getTime());

        getLogger().trace("fecha {}",fecha.getTime());
        if (fecha.get(Calendar.DATE) > 1 && fecha.get(Calendar.DATE) < 9) {
            getLogger().trace("Entra");
            fecha.add(Calendar.WEEK_OF_MONTH, -1);
        }
        for (int i = 1; fecha.getTime().getTime() < fechaFin.getTime().getTime(); i++) {
            String seleccionado = "";
            if (semana.equals(String.valueOf(i))) {
                seleccionado = " selected";
            }
            regreso += "<option value='" + i + "'" + seleccionado + "> ";
            regreso += (fecha.get(Calendar.DATE) + 1) + " " + monthDate(String.valueOf(fecha.get(Calendar.MONTH) + 1)) + " - ";
            fecha.add(Calendar.WEEK_OF_MONTH, 1);
            regreso += (fecha.get(Calendar.DATE)) + " " + monthDate(String.valueOf(fecha.get(Calendar.MONTH) + 1)) + "\n";
        }
        return regreso;
    }

    public Calendar firstDayOfWeek(String mes, String anio, String semana) {
        Calendar fecha = Calendar.getInstance();
        fecha.setTime(new java.util.Date());
        fecha.set(Calendar.DATE, 1);
        if (mes.equals("")) {
            mes = String.valueOf(fecha.get(Calendar.MONTH) + 1);
            anio = String.valueOf(fecha.get(Calendar.YEAR));
        }
        fecha.set(Calendar.MONTH, Integer.parseInt(mes) - 1);
        fecha.set(Calendar.YEAR, Integer.parseInt(anio));

        fecha.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        fecha.set(Calendar.DAY_OF_WEEK_IN_MONTH, 1);

        if (fecha.get(Calendar.DATE) > 1 && fecha.get(Calendar.DATE) < 9) {
            fecha.add(Calendar.WEEK_OF_MONTH, -1);
        }

        fecha.add(Calendar.WEEK_OF_MONTH, Integer.parseInt(semana) - 1);

        return fecha;
    }

    public Calendar firstWeekOfMonth(String mes, String anio) {
        Calendar fecha = Calendar.getInstance();
        fecha.setTime(new java.util.Date());
        fecha.set(Calendar.DATE, 1);
        if (mes.equals("")) {
            mes = String.valueOf(fecha.get(Calendar.MONTH) + 1);
            anio = String.valueOf(fecha.get(Calendar.YEAR));
        }
        fecha.set(Calendar.MONTH, Integer.parseInt(mes) - 1);
        fecha.set(Calendar.YEAR, Integer.parseInt(anio));
        fecha.add(Calendar.DATE, -(fecha.get(Calendar.DAY_OF_WEEK) - Calendar.MONDAY));

        return fecha;
    }

    public String comboMonth(String selected) {
        String regreso = "";

        int seleccionado = 0;
        if (selected != null && !selected.equals("")) {
            seleccionado = Integer.parseInt(selected);
        } else {
            java.util.Calendar fechaActual = java.util.Calendar.getInstance();
            fechaActual.setTime(new java.util.Date());
            seleccionado = fechaActual.get(Calendar.MONTH) + 1;
        }
        String cero = "0";
        for (int i = 1; i <= 12; i++) {
            if (i > 10) {
                cero = "";
            }
            if (i == seleccionado) {
                regreso += "<option value='" + cero + i + "' selected>" + monthDate(Integer.toString(i)) + "\n";
            } else {
                regreso += "<option value='" + cero + i + "'>" + monthDate(Integer.toString(i)) + "\n";
            }
        }
        return regreso;

    }

    public static String[] removeElements(String[] from, String[] elements) {
        String[] temp = new String[from.length - elements.length];
        int quitados = 0;
        for (int i = 0; i < from.length; i++) {
            boolean quitar = false;
            for (int j = 0; j < elements.length; j++) {
                if (from[i].equals(elements[j])) {
                    quitar = true;
                }
            }
            if (!quitar) {
                temp[i - quitados] = from[i];
            } else {
                quitados++;
            }
        }
        return temp;
    }

    public static String[] joinArrays(String[] arr1, String[] arr2) {
        String[] regreso = new String[arr1.length + arr2.length];
        int i = 0;
        for (i = 0; i < arr1.length; i++) {
            regreso[i] = arr1[i];
        }
        for (int j = 0; i < arr1.length + arr2.length; i++, j++) {
            regreso[i] = arr2[j];
        }
        return regreso;
    }

    public String monthDate(String numMes) {
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(new java.util.Date());
        int mes = calendario.get(Calendar.MONTH) + 1;
        String mesS = "";
        if (!numMes.equals("")) {
            mes = Integer.parseInt(numMes);
        }
        if (mes == 1) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Enero");
        }
        if (mes == 2) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Febrero");
        }
        if (mes == 3) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Marzo");
        }
        if (mes == 4) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Abril");
        }
        if (mes == 5) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Mayo");
        }
        if (mes == 6) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Junio");
        }
        if (mes == 7) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Julio");
        }
        if (mes == 8) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Agosto");
        }
        if (mes == 9) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Septiembre");
        }
        if (mes == 10) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Octubre");
        }
        if (mes == 11) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Noviembre");
        }
        if (mes == 12) {
            mesS = tags.getString("isoblock.common.Utilities.monthDate.Diciembre");
        }
        return mesS;
    }

    public String shortMonthDate(String numMes) {
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(new java.util.Date());
        int mes = calendario.get(Calendar.MONTH) + 1;
        String mesS = "";
        if (!numMes.equals("")) {
            mes = Integer.parseInt(numMes);
        }
        if (mes == 1) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Ene");
        }
        if (mes == 2) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Feb");
        }
        if (mes == 3) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Mar");
        }
        if (mes == 4) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Abr");
        }
        if (mes == 5) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.May");
        }
        if (mes == 6) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Jun");
        }
        if (mes == 7) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Jul");
        }
        if (mes == 8) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Ago");
        }
        if (mes == 9) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Sep");
        }
        if (mes == 10) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Oct");
        }
        if (mes == 11) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Nov");
        }
        if (mes == 12) {
            mesS = tags.getString("isoblock.common.Utilities.shortMonthDate.Dic");
        }
        return mesS;
    }

    public String imageName(String name, String imagePath) {

        String letra = "";
        String image = "";

        for (int x = 0; x < name.length(); x++) {
            letra = name.substring(x, x + 1).toLowerCase();
            if (letra.equals("á")) {
                letra = "aacento";
            }
            if (letra.equals("é")) {
                letra = "eacento";
            }
            if (letra.equals("í")) {
                letra = "iacento";
            }
            if (letra.equals("ó")) {
                letra = "oacento";
            }
            if (letra.equals("ú")) {
                letra = "uacento";
            }
            if (letra.equals(" ")) {
                letra = "trans";
            }
            if (letra.equals(".")) {
                letra = "punto";
            }
            if (letra.equals("´")) {
                letra = "tilde";
            }

            image += "<img src='" + imagePath + "letras_" + letra + ".gif' border='0'>";

        }

        return image;

    }

    // Funciï¿½n encargada de armar el mensaje de correo electrï¿½nico
    public static String armarCorreo(String tipoCorreoParam, String[] datosCorreo, String message) {
        String tipoCorreo = "isoblock.correo." + tipoCorreoParam + ".variablesCorreo", header, temp;
        String template = Mailer.getTemplate(Mailer.class.getCanonicalName());
        int cantidadDatosCorreo = datosCorreo.length;
        StringBuilder data = new StringBuilder();
        Properties prop = new Properties();
        if (tipoCorreoParam.equals("accion") && datosCorreo[0].startsWith("QUE-")) {
            header = prop.tags.getString("isoblock.correo.quejas.notificacion",false);
        } else {
            header = prop.tags.getString("isoblock.correo." + tipoCorreoParam + ".notificacion",false);
        }
        for (int i = 1; i <= cantidadDatosCorreo; i++) {
            temp = datosCorreo[i - 1];
            if(temp != null && !temp.isEmpty()){
                data.append(Framework.Config.Utilities.Mail_Row(
                        prop.tags.getString(tipoCorreo + Integer.toString(i)), 
                        temp, "#ffffff"));
            }
        }
        template = template
            .replace("{header}", header)
            .replace("{title}", message.trim())
            .replace("{data}", Framework.Config.Utilities.getMailDataTemplate().replace("{data}", data.toString()))
        ;
        return template;
    }    

    /**
     * Crea el pie de página para las formas con las claves.
     *
     * <AUTHOR> Enrique Carro Prado
     * @param strForma La forma de donde se obtiene la clave
     * @return String El código de la tabla que contiene esos datos.
     */
    public String getClaveDeForma(String strForma) {

        StringBuilder code = new StringBuilder();
        code.append("<div id='divTable' name='divTable' align=center>")
                .append("  <table border='0' width='100%'>")
                .append("    <tr>")
                .append("      <td align=center>").append(formatProp.getString(strForma + ".Fecha")).append("</td>")
                .append("      <td align=center>").append(formatProp.getString(strForma + ".Revision")).append("</td>")
                .append("      <td align=center>").append(formatProp.getString(strForma + ".Clave")).append("</td>")
                .append("    </tr>")
                .append("  </table>")
                .append("</div>");

        return code.toString();
    }

    /**
     * Utilidad par borrar archivos fisicamente @autor Luis Limas 04/02/2010
     *
     * @param filepath La ruta completa del archivo
     * @return true si se pudo borrar correctamente
     */
    public boolean deleteFile(String filepath) {
        getLogger().trace("DELETEFILE:  '" + filepath + "'");
        final java.nio.file.Path archivo = java.nio.file.Paths.get(filepath);
        if (!java.nio.file.Files.isReadable(archivo)) {
            getLogger().trace("Error: El archivo no se pudo leer: '" + filepath + "'");
            return false;
        }
        try {
            if (!java.nio.file.Files.deleteIfExists(archivo)) {
            getLogger().trace("Error: El archivo no se pudo borrar: '" + filepath + "'");
            return false;
        }
        } catch (IOException ex) {
            getLogger().trace("Error: El archivo no se pudo borrar: '" + filepath + "'");
            return false;
        }
        return true;
    }

    public void setRequest(HttpServletRequest r) {
        this.requests = r;
    }
    
    public HttpServletRequest getRequest(){
        return this.requests;
    }
    
    public ServletContext getContext(){
        if(requests != null){
            return this.requests.getServletContext();
        }else{
            return null;
        }
    }
    public ServletContext getServletContext(){
        if(requests != null){
            return this.requests.getServletContext();
        }else{
            return null;
        }
    }

    /**
     * Funcion que sirve para cambiar la codificacion de caracteres a UTF-8
     *
     * @autor Luis Limas
     *
     * @param str
     * @return
     */
    public String encodeUTF(String str) {
        return str;
    }

    public String encodeUTF8(String str) {
        byte[] gbBytes = {};
        try {
            CharsetEncoder enc = Charset.forName("UTF-8").newEncoder();
            enc.onMalformedInput(CodingErrorAction.REPORT);
            enc.onUnmappableCharacter(CodingErrorAction.REPLACE);
            gbBytes = enc.encode(CharBuffer.wrap(str)).array();
        } catch (Exception ex) {
            return "Error: encodeUTF() - UTF-8 - Exception: " + ex.getMessage() + " - " + ex;
        }
        return new String(gbBytes).trim();
    }

    public String decodeUTF8(String str) {
        CharBuffer x;
        try {
            CharsetDecoder enc = Charset.forName("UTF-8").newDecoder();
            enc.onMalformedInput(CodingErrorAction.REPORT);
            enc.onUnmappableCharacter(CodingErrorAction.REPLACE);
            x = enc.decode(ByteBuffer.wrap(str.getBytes()));
        } catch (Exception ex) {
            return "Error: encodeUTF() - UTF-8 - Exception: " + ex.getMessage() + " - " + ex;
        }
        return x.toString();
    }

    public String encodeURL8(String dato) {

        try {
            dato = java.net.URLEncoder.encode(dato, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            getLogger().error("Error en utilities.java en el metodo encodeURL(String)",e);
        }
        //getLogger().trace(dato);
        while (dato.indexOf("+") != -1) {
            dato = dato.substring(0, dato.indexOf("+")) + " " + dato.substring(dato.indexOf("+") + 1, dato.length());
        }
        //*/
        return dato;
    }

    public String decodeURL8(String dato) {

        try {
            dato = java.net.URLDecoder.decode(dato, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            getLogger().error("Error en utilities.java en el metodo encodeURL(String)",e);
        }
        //getLogger().trace(dato);
        while (dato.indexOf("+") != -1) {
            dato = dato.substring(0, dato.indexOf("+")) + " " + dato.substring(dato.indexOf("+") + 1, dato.length());
        }
        //*/
        return dato;
    }

    public String arrayToHTMLGetParams(String nombreParam, String[] array) {
        String params = "";
        if (array.length > 0) {
            params = nombreParam + "=" + array[0];
            for (int i = 1; i < array.length; i++) {
                params += "&" + nombreParam + "=" + array[i];
            }
        }
        return params;
    }

    public String arrayToString(String[] array) {
        String params = "";
        if (array.length > 0) {
            params = array[0];
            for (int i = 1; i < array.length; i++) {
                params += "," + array[i];
            }
        }
        return params;
    }

    /**
     * Regresa el componente para mostrar y ocultar algun objeto con
     * surespectivo id sin una funcion de javascript definida
     *
     * @param id: el ID de ESTE componente, si solo se usara una ves en todo el
     * JSP se puede mandar vacio
     * @param idMostrarOcultar: es el ID del componente que se ocultara y
     * mostrara
     * @param etiqueta: es la etiqueta que aparecera en donde se dara clic
     * @return
     */
    public String getMostrarOcultarComponent(String id, String idMostrarOcultar, String etiqueta) {
        return getMostrarOcultarComponent(id, idMostrarOcultar, etiqueta, "", "");
    }

    /**
     * Regresa el componente para mostrar y ocultar algun objeto con
     * surespectivo id
     *
     * @param id: el ID de ESTE componente, si solo se usara una ves en todo el
     * JSP se puede mandar vacio
     * @param idMostrarOcultar: es el ID del componente que se ocultara y
     * mostrara
     * @param etiqueta: es la etiqueta que aparecera en donde se dara clic
     * @param onShowHide: es el nombre de una funcion JavaScript que se
     * ejecutara cuando se muestro u oculte el componente (opcional)
     * @param paramsOnShowHide: son los parametros que vas dentro de onShowHide
     * separados por comas (opcional)
     * @return
     */
    public String getMostrarOcultarComponent(String id, String idMostrarOcultar, String etiqueta, String onShowHide, String paramsOnShowHide) {
        onShowHide = onShowHide.trim();
        if (!onShowHide.isEmpty()) {
            onShowHide += "(" + paramsOnShowHide + "); ";
        }
        String codigo;
        codigo = "<a id='mostrar" + id + "' href=\"javascript: block('ocultar" + id + "'); block('mostrar" + id + "'); block('" + idMostrarOcultar + "'); " + onShowHide + " \"> <img align=bottom border='0' src=\"../images/control/ico_busquedaAvanzadaMostrar.GIF\" alt=\"" + tags.getString("isoblock.scorecards.selectorEntidad.Mostrar") + "\">" + etiqueta + "</a> \n";
        codigo += "<a id='ocultar" + id + "' href=\"javascript: block('ocultar" + id + "'); block('mostrar" + id + "'); block('" + idMostrarOcultar + "'); " + onShowHide + " \" style=\"display:none\"><img id='imgoculta" + id + "' align=bottom border='0' src=\"../images/control/ico_busquedaAvanzadaEsconder.GIF\" alt=\"" + tags.getString("isoblock.scorecards.selectorEntidad.Ocultar") + "\">" + etiqueta + "</a> \n";
        return codigo;
    }

    /**
     * Regresa la imagen para mostrar y ocultar algun objeto con surespectivo id
     * sin una funcion de javascript definida
     *
     * @param id: el ID de ESTE componente, si solo se usara una ves en todo el
     * JSP se puede mandar vacio
     * @param idMostrarOcultar: es el ID del componente que se ocultara y
     * mostrara
     * @param etiqueta: es la etiqueta que aparecera en donde se dara clic
     * @return
     */
    public String getMostrarOcultarComponentImage(String id, String idMostrarOcultar, String nombreImagen) {
        return getMostrarOcultarComponent(id, idMostrarOcultar, nombreImagen, "", "");
    }

    /**
     * Regresa la imagen para mostrar y ocultar algun objeto con surespectivo id
     *
     * @param id: el ID de ESTE componente, si solo se usara una ves en todo el
     * JSP se puede mandar vacio
     * @param idMostrarOcultar: es el ID del componente que se ocultara y
     * mostrara
     * @param etiqueta: es la etiqueta que aparecera en donde se dara clic
     * @param onShowHide: es el nombre de una funcion JavaScript que se
     * ejecutara cuando se muestro u oculte el componente (opcional)
     * @param paramsOnShowHide: son los parametros que vas dentro de onShowHide
     * separados por comas (opcional)
     * @return
     */
    public String getMostrarOcultarComponentImage(String id, String idMostrarOcultar, String nombreImagen, String tooltip, String onShowHide, String paramsOnShowHide) {
        onShowHide = onShowHide.trim();
        if (!onShowHide.isEmpty()) {
            onShowHide += "(" + paramsOnShowHide + "); ";
        }
        String codigo;
        codigo = "<a id='mostrar" + id + "' href=\"javascript: block('ocultar" + id + "'); block('mostrar" + id + "'); block('" + idMostrarOcultar + "'); " + onShowHide + " \"> <img align=bottom border='0' src=\"../../images/control/ico_busquedaAvanzadaMostrar.GIF\" alt=\"" + tags.getString("isoblock.scorecards.selectorEntidad.Mostrar") + "\"><img align=bottom border='0' src=\"../../images/common/" + nombreImagen + ".GIF\" title=\"" + tooltip + "\" alt=\"" + tags.getString("isoblock.scorecards.selectorEntidad.Mostrar") + "\"></a> \n";
        codigo += "<a id='ocultar" + id + "' href=\"javascript: block('ocultar" + id + "'); block('mostrar" + id + "'); block('" + idMostrarOcultar + "'); " + onShowHide + " \" style=\"display:none\"><img id='imgoculta" + id + "' align=bottom border='0' src=\"../../images/control/ico_busquedaAvanzadaEsconder.GIF\" alt=\"" + tags.getString("isoblock.scorecards.selectorEntidad.Ocultar") + "\"><img align=bottom border='0' src=\"../../images/common/" + nombreImagen + ".GIF\" title=\"" + tooltip + "\" alt=\"" + tags.getString("isoblock.scorecards.selectorEntidad.Mostrar") + "\"></a> \n";
        return codigo;
    }

    public boolean getMenuUser(String menus, String llave) {
        boolean bol = false;
        //getLogger().trace("permisos "+menus);
        //getLogger().trace("menu: "+llave);
        if (menus.contains(llave)) {
            bol = true;
            //getLogger().trace("boleana "+bol);
        }
        return bol;
    }

    /**
     * Regresa el componente para mostrar y ocultar algun objeto con
     * surespectivo id
     *
     * @return
     */
    public String getMostrarOcultarComponentCheck(String id, String idComp, String permisos) {
        String codigo;
        codigo = "<a id='mostrar" + id + "' " + (permisos.isEmpty() ? "style='display:none'" : "") + " href=\"javascript: block('ocultar" + id + "'); block('mostrar" + id + "'); block('div" + idComp + "');  \"> <img align=bottom border='0' src=\"../../images/control/ico_busquedaAvanzadaMostrar.GIF\" >permiso</a> \n";
        codigo += "<a id='ocultar" + id + "' href=\"javascript: block('ocultar" + id + "'); block('mostrar" + id + "'); block('div" + idComp + "'); \" style=\"display:none\"><img id='imgoculta" + id + "' align='bottom' border='0' src=\"../../images/control/ico_busquedaAvanzadaEsconder.GIF\" >permiso</a> \n";
        return codigo;
    }

    /**
     * @return the filepath
     */
    public String getFilepath() {
        return filepath;
    }

    /**
     * @param filepath the filepath to set
     */
    public void setFilepath(String filepath) {
        this.filepath = filepath;
    }

    public boolean isNumeric(String cadena) {
        try {
            Integer.parseInt(cadena);
            return true;
        } catch (NumberFormatException nfe) {
            return false;
        }
    }

    /**
     * @return the bean
     */
    public Object getBean() {
        return bean;
    }

    /**
     * @param bean the bean to set
     */
    public void setBean(Object bean) {
        this.bean = bean;
    }

    /**
     * @return the sesionusuarioid
     */
    public String getSesionusuarioid() {
        if(sesionusuarioid.isEmpty()) {
            sesionusuarioid = "0";
        }
        return sesionusuarioid;
    }

    /**
     * @param sesionusuarioid the sesionusuarioid to set
     */
    public void setSesionusuarioid(String sesionusuarioid) {
        this.sesionusuarioid = sesionusuarioid;
        
    }

    public String[] stringToArray(String str) {
        //getLogger().trace(" <>--<>--<> stringToArray:  "+str);
        String[] array = str.split(ARRAY_SEPARATOR);
        for (int i = 0; i < array.length; i++) {
            if ("null".equals(array[i])) {
                array[i] = "";
            }
        }
        return array;
    }
    /**
     * 
     * @param str
     * @return
     * @deprecated Función descontinuada, modifique directamente el properties.
     */
    @Deprecated
    public String ponerAcento(String str) {
        return HTMLEncoding.escapeAccentsHTML(str);
    }

    /**
     * Convierte un array en <option> para usarse en combos
     *
     * @param titulos_valor: cada valor en cada posicion debe tener esta forma
     * "[titulo][valor]" (1ero el "titulo" y separado por una coma el "valor")
     * @return
     */
    public String comboArray(String[] titulos_valor) {
        return comboArray(titulos_valor, "");
    }

    public String comboArray(String[] titulos_valor, String valor) {
        StringBuilder code = new StringBuilder("");
        String[] tv = {};
        try {
            for (int i = 0; i < titulos_valor.length; i++) {
                tv = titulos_valor[i].split("-");
                code.append("<option value='")
                        .append(tv[0]).append("' ")
                        .append((valor.equals(tv[0]) ? "selected" : ""))
                        .append(" title='").append(tv[1]).append("'>")
                        .append(tv[1]).append("</option>");
            }
        } catch (Exception e) {
            getLogger().error("Stack trace: ",e);
            
            code = new StringBuilder("<option value=''>-- SELECCIONE --</option>");
        }
        return code.toString();
    }

    public boolean printArray(String[] s) {
        getLogger().trace(getStringArray(s));
        return true;
    }

    public String getStringArray(String[] s) {
        try {
            if (s == null) {
                return "null";
            }
            if (s.length == 0) {
                return "empty";
            }
            StringBuilder str =new StringBuilder("printArray([0]:[").append(s[0]).append("]");
            for (int i = 1; i < s.length; i++) {
                str.append(" - [").append(i).append("]:[").append(s[i]).append("]");
            }
            return str.toString() + ")";
        } catch (Exception e) {
            getLogger().error("Stack trace: ",e);
            return "error";
        }
    }
    
    public String stringMMddYYYYToDDmmYYYY(String date){
        if(date.isEmpty()){
            return "";
        }else{
            String [] arr = date.split("/");
            if(arr.length==3){
                return arr[1] + "/" + arr[0] +"/" + arr[2];
            }else{
                return date;
            }
        }
    }
    public void printCPULog() {
        ThreadMXBean TMB = ManagementFactory.getThreadMXBean();
        long time = new Date().getTime() * 1000000;
        long cput = 0;
        double cpuperc = -1;
        do {
            if (TMB.isThreadCpuTimeSupported()) {
                if (new Date().getTime() * 1000000 - time > 1000000000) //Reset once per second
                {
                    time = new Date().getTime() * 1000000;
                    cput = TMB.getCurrentThreadCpuTime();
                }

                if (!TMB.isThreadCpuTimeEnabled()) {
                    TMB.setThreadCpuTimeEnabled(true);
                }

                if (new Date().getTime() * 1000000 - time != 0) {
                    cpuperc = (TMB.getCurrentThreadCpuTime() - cput) / (new Date().getTime() * 1000000.0 - time) * 100.0;
                    getLogger().trace("{}",(int) cpuperc);
                }
            } else {
                cpuperc = -2;
            }
        } while (cpuperc == -1);
    }

    public class StreamGobbler implements Runnable, IBnextThread, Serializable {

        String name;
        InputStream is;
        Thread thread;

        public StreamGobbler(String name, InputStream is) {
            this.name = name;
            this.is = is;
        }

        public void start() {
            thread = new Thread(this);
            thread.start();
        }

        @Instrumentation.Transaction(
                timer = "background thread",
                traceHeadline = "StreamGobbler {{this.id}}",
                transactionName = "StreamGobbler",
                transactionType = "Background",
                alreadyInTransactionBehavior = Instrumentation.AlreadyInTransactionBehavior.CAPTURE_NEW_TRANSACTION
        )
        
        @Override
        public void run() {
            try {
                InputStreamReader isr = new InputStreamReader(is);
                BufferedReader br = new BufferedReader(isr);

                while (true) {
                    String s = br.readLine();
                    if (s == null) {
                        break;
                    }
                    getLogger().trace("[" + name + "] " + s);
                }

                is.close();

            } catch (Exception ex) {
                getLogger().trace("Problem reading stream " + name + "... :" + ex);
                getLogger().error("StackTrace: ",ex);
            }
        }

        public String getName() {
            return name;
        }

        @Override
        public String getId() {
            return "StreamGobbler";
        }

        @Override
        public void interrupt() {
        }

        @Override
        public void submitQueue() {
        }

        @Override
        public String getTag() {
            return getClass().getSimpleName();
        }
        
    }
    /**
     * Obtiene el VALOR de un parametro para su llave especifica
     *
     * @param llave
     * @return
     */
    public String getParamValue(String llave) {
        int index = params.indexOf(llave);
        if(index != -1) {
            return paramsValues.get(index);
        }
        return "";
    }

    /**
     * Obtiene el valor de un parametro ARREGLO especificando su llave
     * (Que repite su LLAVE en mas de una ocacion)
     *
     * @autor Ing. Luis Carlos Limas
     * @param llave
     * @return
     */
    public String[] getParamArrayValue(String llave) {
        getLogger().trace("isoblock.common.Utilities.getParamArrayValue("+llave+")");
        try {
            String[] array;
            ArrayList<String> temp = new ArrayList<String>();
            ArrayList<String> temp_array = new ArrayList<String>();
            temp.addAll(params);
            
            while(temp.contains(llave)) {
                int index = temp.indexOf(llave);
                if(index != -1) {
                    temp_array.add(paramsValues.get(index));
                    temp.set(index, "");
                } else {
                    break;
                }
            }

            array = new String[temp_array.size()];
            getLogger().trace("array.length: "+array.length);
            if(array.length>0) {
                //temp_array.toArray(array);
                getLogger().trace("array: '"+array[0]+"'");
                array = temp_array.toArray(new String[0]);
                getLogger().trace("array: '"+array[0]+"'");
                return array;
            }
        } catch(Exception e) {
            getLogger().error("Error: exception: "+e.getLocalizedMessage());
        }
        return new String[]{};
    }

    /**
     * Obtiene un ARREGLO que contiene todos los VALORES dentro de un agrupador de llaves
     *
     * @autor Luis Carlos Limas
     * @param agrupadorLlave
     * @return
     */
    public String[] getParamArrayValuesFromKeyHolder(String agrupadorLlave) {
        try {
            String array = "";
            ArrayList<String> temp = new ArrayList<String>();
            temp.addAll(paramsKeyHolder);
            while(temp.contains(agrupadorLlave)) {
                int index = temp.indexOf(agrupadorLlave);
                if(index != -1) {
                    array += paramsValues.get(index) + ",";
                    temp.set(index, "");
                } else {
                    break;
                }
            }

            array = array.substring(0,(array.length()>0 ? (array.length()-1) : 0));
            if(array.length()>0) {
                return array.split(",");
            }
        } catch(Exception e) {
            getLogger().error("Error: exception: "+e.getLocalizedMessage());
        }
        return new String[]{};
    }

    /**
     * Obtiene un ARREGLO que contiene todos las LLAVES dentro de un agrupador de llaves
     *
     * @autor Luis Carlos Limas
     * @param agrupadorLlave
     * @return
     */
    public String[] getParamArrayKeysFromKeyHolder(String agrupadorLlave) {
        try {
            String array = "";
            ArrayList<String> temp = new ArrayList<String>();
            temp.addAll(paramsKeyHolder);
            while(temp.contains(agrupadorLlave)) {
                int index = temp.indexOf(agrupadorLlave);
                if(index != -1) {
                    array += params.get(index) + ",";
                    temp.set(index, "");
                } else {
                    break;
                }
            }

            array = array.substring(0,(array.length()>0 ? (array.length()-1) : 0));
            if(array.length()>0) {
                return array.split(",");
            }
        } catch(Exception e) {
            getLogger().error("Error: exception: "+e.getLocalizedMessage());
        }
        return new String[]{};
    }

    /**
     * 
     * Obtiene un ARREGLO de ARREGLOS que contiene todos los VALORES dentro de un agrupador de llaves
     * Es decir que el agrupador contiene mas de una vas una misma llave
     * 
     * @autor Luis Carlos Limas
     * @param agrupadorLlave
     * @return
     */
    public String[][] getParamBidimencionalArrayValuesFromKeyHolder(String agrupadorLlave) {
        try {
            String array = "";
            ArrayList<String> temp = new ArrayList<String>();
            temp.addAll(paramsKeyHolder);
            while(temp.contains(agrupadorLlave)) {
                int index = temp.indexOf(agrupadorLlave);
                if(index != -1) {
                    array += params.get(index) + ",";
                    temp.set(index, "");
                } else {
                    break;
                }
            }
            array = array.substring(0,(array.length()>0 ? (array.length()-1) : 0));
            if(array.length()>0) {
                ArrayList<String> tempX = new ArrayList<String>();
                String[] arre = array.split(",");
                int X = 1;
                int Y = 1;
                tempX.add(arre[0]);
                for(int i=0;i<arre.length;i++) {
                    if(!tempX.get(tempX.size()).equals(arre[i])) {
                        X++;
                        tempX.add(arre[i]);
                    }
                }
                for(int i=0;i<tempX.size();i++) {
                    String[] tempY = getParamArrayValue(tempX.get(i));
                    if(tempY.length>Y) {
                        Y = tempY.length;
                    }
                }
                getLogger().trace("X: "+X+", Y: "+Y+", tempX.size(): "+tempX.size());
                String[][] arreBi = new String[X][Y];
                for(int i=0;i<tempX.size();i++) {
                    //getLogger().trace(" - tempX.elementAt(i): "+tempX.elementAt(i));
                    String[] tempY = getParamArrayValue(tempX.get(i));
                    for(int j=0;j<tempY.length;j++) {
                        //getLogger().trace(" - tempY[j]: "+tempY[j]);
                        try {
                            arreBi[i][j] = tempY[j];
                        } catch (Exception e) {
                            arreBi[i][j] = "";
                            getLogger().error("There was an error",e);
                        }
                    }
                }
                return arreBi;
            }
        } catch(Exception e) {
            getLogger().error("Error: exception: "+e.getLocalizedMessage());
        }
        return new String[][]{};
    }


    /**
     * Agrega un parametro, la razon de ser de este metodo es la nesedad de
     * pasar una cantidad N de variables sin tener que declarar una variable para cada cosa
     *
     * @autor Luis Carlos Limas
     * @param agrupadorLlaves //guarda una relacion del entre las llaves
     * @param llave //guarda las llaves
     * @param valor //guarda el valor EN esa llave
     * @return
     */
    public boolean addParamValue(String agrupadorLlaves, String llave,String valor) {
        try {
            getLogger().trace("addParamValue("+agrupadorLlaves+", "+llave+", "+valor+");");
            paramsKeyHolder.add(agrupadorLlaves);//guarda una relacion del entre las llaves
            paramsValues.add(valor); //guarda el valor EN esa llave
            params.add(llave); //guarda las llaves
            return true;
        } catch(Exception e) {
            getLogger().trace("Error: isoblock.common.Utilities.addParamValue() e:"+e);
            return false;
        }
    }

    /**
     * Coloca un parametro, la razon de ser de este metodo es la nesedad de
     * pasar una cantidad N de variables sin tener que declarar una variable para cada cosa
     * 
     * - Este metodo reemplaza los valores si ya exisitian sus llaves
     *
     * @autor Luis Carlos Limas
     * @param agrupadorLlaves //guarda una relacion del entre las llaves
     * @param llave //guarda las llaves
     * @param valor //guarda el valor EN esa llave
     * @return
     */
    public boolean setParamValue(String agrupadorLlaves, String llave,String valor) {
        try {
            getLogger().trace("setParamValue("+agrupadorLlaves+", "+llave+", "+valor+");");
            int index = params.indexOf(llave);
            if(index == -1) {
                paramsKeyHolder.add(agrupadorLlaves);//guarda una relacion del entre las llaves
                params.add(llave); //guarda las llaves
                paramsValues.add(valor); //guarda el valor EN esa llave
            } else {
                paramsValues.set(index, valor);
            }
            return true;
        } catch(Exception e) {
            getLogger().trace("Error: isoblock.common.Utilities.setParamValue() e:"+e);
            return false;
        }
    }

    /**
     * Llena el vector de parametros con sus respectivas llaves
     *
     * @return: regresa true si los parametros son correctos
     */
    public boolean fillParams() {
        getLogger().trace("isoblock.common.Utilities.fillParams()");
        getLogger().trace(" - llaves = " + getParamKeys().length);
        getLogger().trace(" - valores = " + getParamValues().length);
        getLogger().trace(" - agrupadores de llaves = " + getParamKeysHolder().length);
        if (getParamKeys().length == getParamValues().length && getParamKeys().length == getParamKeysHolder().length) {
            try {
                for (int j = 0; j < getParamKeys().length; j++) {
                    addParamValue(getParamKeysHolder()[j],getParamKeys()[j], getParamValues()[j]);
                }
                return true;
            } catch (Exception e) {
                getLogger().trace("Error: excepcion: " + e);
            }
        } else {
            getLogger().trace("Error: la cantidad de parametros con la cantidad de llaves no coincide. llaves = " + getParamKeys().length + ", valores = " + getParamValues().length + ", agrupadores de llaves = " + getParamKeysHolder().length);
            int max = 0;
            if(getParamValues().length > max) { max = getParamValues().length; }
            if(getParamKeys().length > max) { max = getParamKeys().length; }
            if(getParamKeysHolder().length > max) { max = getParamKeysHolder().length; }
            for(int i =0;i<max;i++) {
                try { getLogger().trace((i+1)+" - <font color='red'>getParamValues: "+getParamValues()[i]+ "</font>"); } catch (Exception e) {
                getLogger().error("There was an error:",e);}
                try { getLogger().trace((i+1)+" - <font color='green'>getParamKeys: "+getParamKeys()[i]+ "</font>"); } catch (Exception e) {
                getLogger().error("There was an error:",e);}
                try { getLogger().trace((i+1)+" - <font color='blue'>getParamKeysHolder: "+getParamKeysHolder()[i]+ "</font>") ; } catch (Exception e) {
                getLogger().error("There was an error:",e);}
            }
        }
        return false;
    }
    
    /**
     * @return the paramKeysHolder
     */
    public String[] getParamKeysHolder() {
        return paramKeysHolder;
    }

    /**
     * @param paramKeysHolder the paramKeysHolder to set
     */
    public void setParamKeysHolder(String[] paramKeysHolder) {
        this.paramKeysHolder = paramKeysHolder;
    }

    /**
     * @return the paramKeys
     */
    public String[] getParamKeys() {
        return paramKeys;
    }

    /**
     * @param paramKeys the paramKeys to set
     */
    public void setParamKeys(String[] paramKeys) {
        this.paramKeys = paramKeys;
    }

    /**
     * @return the paramValues
     */
    public String[] getParamValues() {
        return paramValues;
    }

    /**
     * @param paramValues the paramValues to set
     */
    public void setParamValues(String[] paramValues) {
        this.paramValues = paramValues;
    }
}
