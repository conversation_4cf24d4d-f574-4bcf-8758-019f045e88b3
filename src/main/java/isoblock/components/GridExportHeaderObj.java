package isoblock.components;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class GridExportHeaderObj {

    private String title = "Sin titulo";
    private int shown = 1;
    private int notExportable = 0;

    public int getNotExportable() {
        return notExportable;
    }

    public void setNotExportable(int notExportable) {
        this.notExportable = notExportable;
    }

    /**
     * @return the title
     */
    public String getTitle() {
        return title;
    }

    /**
     * @param title the title to set
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * @return the shown
     */
    public int getShown() {
        return shown;
    }

    /**
     * @param shown the shown to set
     */
    public void setShown(int shown) {
        this.shown = shown;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 47 * hash + Objects.hashCode(this.title);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final GridExportHeaderObj other = (GridExportHeaderObj) obj;
        return Objects.equals(this.title, other.getTitle());
    }

    @Override
    public String toString() {
        return "GridExportHeaderObj{" + "title=" + title + ", shown=" + shown + ", notExportable=" + notExportable + '}';
    }

}
