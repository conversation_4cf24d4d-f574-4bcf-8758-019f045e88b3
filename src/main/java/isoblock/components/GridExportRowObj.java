package isoblock.components;

/**
 *
 * <AUTHOR>
 */
public class GridExportRowObj {

    private String[] row = new String[]{"1"};
    private RowMetadata[] metadata = new RowMetadata[]{null};

    /**
     * @return the row
     */
    public String[] getRow() {
        return row;
    }

    /**
     * @param row the row to set
     */
    public void setRow(String[] row) {
        this.row = row;
    }

    /**
     * @return the metadata
     */
    public RowMetadata[] getMetadata() {
        return metadata;
    }

    /**
     * @param metadata the metadata to set
     */
    public void setMetadata(RowMetadata[] metadata) {
        this.metadata = metadata;
    }
}
