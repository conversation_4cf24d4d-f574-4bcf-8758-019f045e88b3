package isoblock.surveys.struts2.action;

import DPMS.DAOInterface.IActionDAO;
import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.Mapping.ActionSources;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditType;
import DPMS.Mapping.Catalog;
import DPMS.Mapping.CatalogPK;
import DPMS.Mapping.ClauseType;
import DPMS.Mapping.Document;
import DPMS.Mapping.Priority;
import DPMS.Mapping.Settings;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import isoblock.common.Properties;
import isoblock.configuracion.catalogo;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.type.OutstandingSurveysLoggingType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import org.owasp.encoder.Encode;
import qms.access.dto.ILoggedUser;
import qms.activity.entity.Activity;
import qms.document.entity.DocumentTypeRef;
import qms.document.entity.SurveyTheme;
import qms.document.util.DocumentPublicationUtils;
import qms.finding.entity.FindingType;
import qms.form.core.ISurveyToHtml;
import qms.form.dto.OutstandingSurveyDTO;
import qms.form.dto.SurveyParseDTO;
import qms.form.util.IOutstandingSurveysLoadAnswers;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.CacheRegion;
import qms.framework.util.MeasureTime;
import qms.survey.dto.SurveyCaptureConfig;
import qms.survey.logic.ConditionalValidatorCache;
import qms.survey.logic.SurveyParser;
import qms.survey.logic.SurveytoHtmlRenderer;
import qms.survey.util.SurveyCaptureUtil;
import qms.util.EntityCommon;
import qms.workflow.util.WorkflowAuthRole;
import qms.workflow.util.WorkflowRequestStatus;

/**
 *
 * <AUTHOR> Limas
 */
public class SurveyCaptureAction extends DefaultAction {

    public static String TASK_GOING_TO_FILL = "fill";
    public static String TASK_GOING_TO_PREVIEW = "preview";
    public static String TASK_GOING_TO_READONLY_ANSWERS = "progress";
    private static final String PENDINGS_URL = "../DPMS/v.pendings.view";
    //submit de guardado /* para mostrar mensajes de exito y finalizacion */
    private boolean guardadoTotalmente = false;
    private boolean justLoad = false;
    private boolean audit = false;
    private boolean poll = false;
    private boolean auditedAccess = false;
    private boolean previewMode= false;
    private boolean disabled = false;
    private boolean exitButtonAvailable = true;
    private boolean printButtonAvailable = true;
    private boolean progressButtonAvailable = true;
    private boolean attachmentButtonAvailable = false;
    
    private String actionSourcesJSON;
    private String actionTypesJSON;
    private String priorityJSON;
    private String actionSystemJSON;
    private String partialProgressStatusesJSON;
    
    private String gotojsp = "http://fcportal.femcom.net:7777/AccessControl/login_form.jsp", surveyModule = "empty";
    private String endMessage = "Muchas gracias por tú participación en la encuesta de Índice de Satisfacción de Líder de tienda, tus respuestas nos ayudarán a trabajar mejor para ti.";
    //configuracion de la pagina
    private String loadAction = "";
    private String html="";
    private SurveyParser parser;
    private String resolution = "";
    private String id= "-1";
    private String autoSaveTime= "10";  //<--- este tiempo es en minutos
    private String requestMode;
    private String task = TASK_GOING_TO_PREVIEW;
    //variables requeridas para responder la encuesta
    private Long requestId;
    private Long documentId;
    private String documentMasterId;
    private String documentDescription;
    private String outstandingid= "-1";
    private String outstandingCode= "";
    private Long progressStateId = null;
    private String surveyid= "-1";
    private String bloqueid= "-1";
    private String tiendaid= "-1";      //<--- frozen id
    private String score= "-1";
    private String surveyType;
    private Date dteFechaInicio = null;

    private String auditIndividualId= "-1";
    private String auditIndividualStatus= "-1";
    private String auditIndividualCode= "-1";
    private String auditIndividualDepartment= "-1";
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private List<ITextHasValue> actionSources = new ArrayList<>();
    private List<ITextHasValue> actionTypes = new ArrayList<>();
    private List<ITextHasValue> actionSystem= new ArrayList<>();
    
    private String estatus = "-1";
    //indica si se pueden o no guardar respuestas
    private boolean responderEncuesta= false;
    private IOutstandingSurveysLoadAnswers pendiente = null;
    private Boolean showFormFindings = false;
    private Boolean showFormActivities = false;
    private Boolean showActivities = false;
    private Boolean activityManager = false;
    private String serializedStatusList;
    private String defaultSourceId = "-1";
    private String serializedOutstandingSurvey = "{}";
    private String surveyTheme = "surveyClassic";
    
    private ISurveyToHtml daoSurvey;
    
    // flujos de cancelación y reapertura:  - solicitante -
    private List<Long> stageLatestFillOutUserId = new ArrayList<>(3);
    private Long cancelationCurrentUserId;
    private Long adjustmentCurrentUserId;
    private Long reopenCurrentUserId;
    private String cancelationCurrentUserName;
    private String adjustmentCurrentUserName;
    private String reopenCurrentUserName;
    private Integer cancelationCurrentUserStatus;
    private Integer adjustmentCurrentUserStatus;
    private Integer reopenCurrentUserStatus;
    private Long flowBusinessUnitId;
    private Long flowBusinessUnitDepartmentId;
    private Long flowAreaId;
    // flujo de reapertura:                 - autorizador -
    private Integer activeCancelationStatus;
    private Integer activeAdjustmentStatus;
    private Integer activeReopenStatus;
    private Integer activeAdjustmentRejectedAutorizationPoolIndex;
    private Long activeCancelationFormRequestId;
    private Long activeAdjustmentFormRequestId;
    private Long activeReopenFormRequestId;
    private String activeCancelationReason;
    private String activeAdjustmentReason;
    private String activeReopenReason;
    private String cancelationRequestorUserName;
    private String adjustmentRequestorUserName;
    private String reopenRequestorUserName;
    // flujo de ajuste
    private boolean adjustmentVerifyAvailable = false; 
    private boolean adjustmentAuthorizationAvailable = false; 
    private Boolean signatureRejectApprovalRequired;    
    private Boolean isReopenAvailableByAdmin = false; 
    private Boolean isReopenAuthorizerAvailableByAdmin = false;
    private Boolean isCancelAuthorizerAvailableByAdmin = false;
    private Boolean isAdjustmentVerifyAvailableByAdmin = false;
    private Boolean isAdjustmentAuthorizationAvailableByAdmin = false;
    private Boolean hasAccessToDefitiveCancelWithAuth = false;
    private WorkflowAuthRole authRole;
    private String show_hide_buttons = "inline-flex";

    private boolean outstandingArchived = false;

    private String conditionalValidatorCacheId;

    protected String defaultActionExecute() throws Exception {
        return super.execute();
    }

    @Override
    public String execute() throws Exception {
        final SurveyCaptureConfig config = SurveyCaptureUtil.getDefaultConfig(getId(), getLoggedUserDto());
        return doExecute(config, null);
    }

    protected Long parseOutstandingSurveyId() {
        return SurveyCaptureUtil.parseOutstandingSurveyId(getId());
    }
    
    public String doExecute(final SurveyCaptureConfig config, @Nullable IOutstandingSurveysLoadAnswers pending) throws ExecutionException {
        ElapsedDataDTO tStart = MeasureTime.start(getClass());
        Settings settings = Utilities.getSettings();
        autoSaveTime = ""+ settings.getAutosaveTime();
        gotojsp = settings.getExitURL();
        authRole = config.getAuthRole();
        if (gotojsp == null || gotojsp.isEmpty()) {
            gotojsp = PENDINGS_URL;
        }
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(" --- SurveyCaptureAction --- ");
            getLogger().trace(" --- resolution : {}",resolution);
            getLogger().trace(" --- id : {}",getId());
            getLogger().trace(" --- endMessage : '{}'",endMessage);
            getLogger().trace(" --- audit : '{}'",audit);
            getLogger().trace(" --- Utilities.getSettings() : '{}'",settings);
            getLogger().trace(" --- autoSaveTime : '{}'", autoSaveTime);
            getLogger().trace(" --- gotojsp : '{}'", gotojsp);
        }
        final String masterId = getDocumentMasterId();
        final boolean canRetriveData = DocumentPublicationUtils.canRetrieveData(masterId);
        if (!canRetriveData) {
            getLogger().debug("Document is being published, masterId: {}", masterId);
            return BUSY;
        }
        final ILoggedUser loggedUser = getLoggedUserDto();
        final ISurveyCaptureDAO dao = getBean(ISurveyCaptureDAO.class);
        if (getTask() != null && getTask().equals(TASK_GOING_TO_PREVIEW)) {
            setRequestMode(SurveyRequestMode.PREVIEW.name());
            setDisabled(true);
        }
        /**
         * Para abrir el registor (llenado) se debe enviar el ID de OutstandingSurveys concatenado a una "O", Ej. O1323
         */
        if (!Utilities.isInteger(getId())) {
            if (getId().charAt(0) == 'O') {
                outstandingid = getId().substring(1);
                if (getLogger().isTraceEnabled()) {
                    getLogger().trace(" ---  outstandingid : {}", outstandingid);
                }
                OutstandingSurveyDTO outstandingSurveysDTO;
                if (pendiente == null || pendiente.getCuestionario() == null) {
                    // Se sustituye el uso del objeto pendiente por un DTO
                    outstandingSurveysDTO = dao.getOutstandingSurveyDTO(Long.valueOf(outstandingid));
                    if (outstandingSurveysDTO.getRequestorId() == null) {
                        outstandingSurveysDTO.setRequestorId(loggedUser.getId()); 
                    }
                } else {
                    outstandingSurveysDTO = new OutstandingSurveyDTO();
                    outstandingSurveysDTO.setEstatus(pendiente.getEstatus());
                    outstandingSurveysDTO.setOutstandingSurveyId(pendiente.getId());
                    outstandingSurveysDTO.setRequestId(pendiente.getRequestId());
                    outstandingSurveysDTO.setScore(pendiente.getScore());
                    outstandingSurveysDTO.setProgressStateId(pendiente.getProgressStateId());
                    outstandingSurveysDTO.setBusinessUnitDepartmentId(pendiente.getBusinessUnitDepartmentId());
                    outstandingSurveysDTO.setCode(pendiente.getCode());
                    outstandingSurveysDTO.setSurveyId(pendiente.getCuestionario().getId());
                    outstandingSurveysDTO.setDteFechaInicio(pendiente.getDteFechaInicio());
                    if (pendiente.getRequest() == null) {
                        outstandingSurveysDTO.setRequestorId(loggedUser.getId());
                    } else {
                        outstandingSurveysDTO.setRequestorId(pendiente.getRequest().getAutorId());
                    }
                }
                if (getRequestId() == null) {
                    setRequestId(outstandingSurveysDTO.getRequestId());
                }
                Long outstandingSurveyId = outstandingSurveysDTO.getOutstandingSurveyId();
                dteFechaInicio = pending != null ? pending.getDteFechaInicio(): null;
                outstandingCode = outstandingSurveysDTO.getCode();
                progressStateId = outstandingSurveysDTO.getProgressStateId();
                surveyid = "" + outstandingSurveysDTO.getSurveyId();
                score = ""+outstandingSurveysDTO.getScore();
                dao.createOutstandingSurveysLog(outstandingSurveyId, 
                        OutstandingSurveysLoggingType.READ_ANSWERS,
                        false,
                        getLoggedUserDto()
                );
                if (getLogger().isTraceEnabled()) {
                    getLogger().trace(" ---  surveyid : {}", surveyid);
                    getLogger().trace(" ---  bloqueid : {}", bloqueid);
                    getLogger().trace(" ---  tiendaid : {}", tiendaid);
                    getLogger().trace(" ---  score : {}", score);
                }
                switch (endMessage) {
                    case "<parcial>":
                        endMessage = ""
                                + "Sus respuestas se han guardado con éxito, recuerde que la encuesta no ha sido" 
                                + " llenada por completo y tiene una fecha limite"
                                + " para completarla, muchas gracias por su participación."
                                + "";
                        break;
                    case "<terminar>":
                        endMessage = ""
                                + "Muchas gracias por tú participación en la encuesta de "
                                + "Índice de Satisfacción de Líder de tienda, tus respuestas nos "
                                + "ayudarán a trabajar mejor para ti.";
                        break;
                }
                responderEncuesta = true;
                setId(outstandingSurveysDTO.getSurveyId() + "");// Por que no se utiliza este valor en las llamadas toHTML?
                resolution = "grid9"; //<-- para pendientes la resolucion siempre es 800x600
                if (getLogger().isTraceEnabled()) {
                    getLogger().trace(" ---  pendiente.getEstatus() : {}", outstandingSurveysDTO.getEstatus());
                }
                guardadoTotalmente = outstandingSurveysDTO.getEstatus() == OutstandingSurveys.ESTATUS_CERRADA;

                AuditType auditType = getAuditType(dao, outstandingSurveysDTO.getOutstandingSurveyId());

                DocumentTypeRef docType = getDocumentType(dao, documentId);

                Survey s = dao.HQLT_findById(
                        Survey.class,
                        outstandingSurveysDTO.getSurveyId(),
                        true,
                        CacheRegion.SURVEY,
                        Utilities.getSettings().getConnQueryTimeout()
                );
                if (docType == null) {
                    docType = new DocumentTypeRef();
                }
                if (auditType == null) {
                    auditType = new AuditType();
                }
                if (s == null) {
                    s = new Survey();
                } else {
                    setSurveyTheme(getSurveyTheme(dao, s.getSurveyTheme().getId()));
                }
                showActivities = auditType.getAddActivity().equals(1) || s.getAddActivitiesEnabled().equals(1) || docType.getAddActivitiesEnabled().equals(1);
                final boolean findingEnabled = auditType.getFindingCapable().equals(1) || s.getAddFindingsEnabled().equals(1) || docType.getAddFindingsEnabled().equals(1);
                activityManager = isAdmin() || getLoggedUserServices().contains(ProfileServices.ACTIVITY_MANAGER);
                SurveyParseDTO htmlParse;

                if (requestId != null) {
                    // Se respeta el valor que venga del front
                    outstandingSurveysDTO.setRequestId(requestId);
                }
                if (documentId != null) {
                    // Se respeta el valor que venga del front
                    outstandingSurveysDTO.setDocumentId(documentId);
                }
                if (previewMode) {
                    if (getLogger().isTraceEnabled()) {
                        getLogger().trace("Goes to PREVIEW!");
                    }
                    final ISurveyCaptureDAO captureDao = getBean(ISurveyCaptureDAO.class);
                    final Long authorizationPoolIndex = captureDao.getOutstandingAutorizationPoolIndex(outstandingSurveyId);
                    htmlParse = getSurveyToHtml().toHtml(
                            config.getAuthRole(),
                            outstandingSurveysDTO,
                            auditType.getQualifiedQuestion().equals(1), // scoreEnabled
                            findingEnabled,                             // findingEnabled
                            isAudit() && !auditedAccess,                // isAudit
                            showActivities,                             // activitiesEnabled
                            Module.ACTIVITY,
                            getTask(),
                            authorizationPoolIndex,
                            loggedUser,
                            this.getRequest(),
                            this.getResponse()
                    );
                    html  = htmlParse.getHtml();
                    parser = htmlParse.getParser();
                    responderEncuesta = false;
                    guardadoTotalmente = false;
                    justLoad = true;
                } else if(!guardadoTotalmente && parameter("equalsEmpty").isEmpty()) {
                    if (getLogger().isTraceEnabled()) {
                        getLogger().trace("Goes to CAPTURE!");
                    }
                    final Module module = getModule();
                    final ISurveyCaptureDAO captureDao = getBean(ISurveyCaptureDAO.class);
                    final Long authorizationPoolIndex = captureDao.getOutstandingAutorizationPoolIndex(outstandingSurveyId);
                    htmlParse = getSurveyToHtml().toHtml(
                            config.getAuthRole(),
                            outstandingSurveysDTO,
                            auditType.getQualifiedQuestion() != null && auditType.getQualifiedQuestion().equals(1),
                            findingEnabled,
                            isAudit(),
                            showActivities,                             // activitiesEnabled
                            module,
                            getTask(),
                            authorizationPoolIndex,
                            loggedUser,
                            this.getRequest(),
                            this.getResponse()
                    );
                    html  = htmlParse.getHtml();
                    parser = htmlParse.getParser();
                } else if (parameter("equalsEmpty").isEmpty() && !isAudit() && !isPoll()) {
                    if (getLogger().isTraceEnabled()) {
                        getLogger().trace("Goes to END!");
                    }
                    MeasureTime.stop(tStart, "Elapsed time in survey capture action END for ID " + getId());
                    return END;
                } else {
                    if (getLogger().isTraceEnabled()) {
                        getLogger().trace("Goes to SEE!");
                    }
                    final Module module = getModule();
                    final ISurveyCaptureDAO captureDao = getBean(ISurveyCaptureDAO.class);
                    final Long authorizationPoolIndex = captureDao.getOutstandingAutorizationPoolIndex(outstandingSurveyId);
                    htmlParse = getSurveyToHtml().toHtml(
                            config.getAuthRole(),
                            outstandingSurveysDTO,
                            auditType.getQualifiedQuestion().equals(1),
                            findingEnabled,
                            isAudit() && !auditedAccess,
                            showActivities,
                            module,
                            getTask(),
                            authorizationPoolIndex,
                            loggedUser,
                            this.getRequest(),
                            this.getResponse()
                    );
                    html  = htmlParse.getHtml();
                    parser = htmlParse.getParser();
                    responderEncuesta = false;
                    guardadoTotalmente = false;
                    justLoad = true;
                }
                if (htmlParse.getHtml() != null && !htmlParse.getHtml().isEmpty()) {
                    showFormActivities = s.getAddActivitiesEnabled().equals(1)
                            || docType.getAddActivitiesEnabled().equals(1)
                            || htmlParse.getParser().getSurveyData().getFields().stream()
                            .anyMatch((field) -> Boolean.TRUE.equals(field.getActivityEnabled()));
                    showFormFindings = s.getAddFindingsEnabled().equals(1)
                            || docType.getAddFindingsEnabled().equals(1)
                            || htmlParse.getParser().getSurveyData().getFields().stream()
                            .anyMatch((field) -> Boolean.TRUE.equals(field.getFindingEnabled()));
                }
                if (showActivities || showFormActivities) {
                    serializedStatusList = Utilities.getSerializedObj(EntityCommon.getStatusList(Activity.class.getCanonicalName()));
                }
                MeasureTime.stop(tStart, "Elapsed time in survey capture action for ID " + getId());
                final SurveyRequestMode mode = getSurveyRequestMode();
                final Long cacheOutsandingSurveyId = pending != null && pending.getId() != null ? pending.getId() : -1L;
                conditionalValidatorCacheId = ConditionalValidatorCache.getInstance().add(cacheOutsandingSurveyId, mode, getModule());
                return SUCCESS;
            }
            setId(getId().replaceAll("[^0-9\\-]", ""));
        }  else {
            final SurveyRequestMode mode = getSurveyRequestMode();
            conditionalValidatorCacheId = ConditionalValidatorCache.getInstance().add(-1L, mode, getModule());
        }
        final SurveyParseDTO htmlParse = getSurveyToHtml().toHtml(
                Long.valueOf(getId()),
                config.getAuthRole(),
                loggedUser.getId(),
                disabled,
                getModule(),
                getTask(),
                loggedUser,
                getRequest(),
                getResponse()
        );
        html  = htmlParse.getHtml();
        parser = htmlParse.getParser();
        MeasureTime.stop(tStart, "Elapsed time in survey capture action for ID " + getId());
        return SUCCESS;
    }

    @Nonnull
    private SurveyRequestMode getSurveyRequestMode() {
        try {
            final String cacheMode = getRequestMode();
            return cacheMode != null ? SurveyRequestMode.valueOf(cacheMode) : SurveyRequestMode.PREVIEW;
        } catch(Exception e) {
            getLogger().error("Error getting survey request mode {}", getRequestMode(), e);
            return SurveyRequestMode.PREVIEW;
        }
    }

    protected Module getModule() {
        if (audit) {
            return Module.AUDIT;
        } else if (poll) {
            return Module.POLL;
        } else  {
            return Module.FORMULARIE;
        }
    }
    protected ActionSources getDefaultSource(IUntypedDAO dao, int moduleId, Long catalogId) {
        Catalog c = new Catalog();
        Long nextCatalogId = catalogo.getNextId(catalogId, dao);
        c.setId(new CatalogPK(catalogId, nextCatalogId));
        c.setDescription(Utilities.getTag("module.audit"));
        c.setCode(Utilities.getTag("module.audit"));
        c.setModuleId(moduleId);
        c.setValue(nextCatalogId.toString());
        c.setIsRetainable(0);
        dao.makePersistent(c, getLoggedUserId());
        return dao.HQLT_findById(ActionSources.class, nextCatalogId);
    }

    protected void setFormsCatalogsJSON(final IOutstandingSurveysLoadAnswers pending, final Long currentAutorizationPoolIndex) {
        final List<TextLongValue> statuses;
        if (getRequestJustStarting()) {
            final ISurveysDAO dao = getBean(ISurveysDAO.class);
            statuses = dao.getRequestorPartialProgressStatuses(pending.getCuestionario().getId(), currentAutorizationPoolIndex);
        } else {
            final ISurveyCaptureDAO dao = getBean(ISurveyCaptureDAO.class);
            statuses = dao.getRequestPartialProgressStatuses(pending.getRequestId(), currentAutorizationPoolIndex);
        }
        if (statuses != null && !statuses.isEmpty()) {
            partialProgressStatusesJSON = Utilities.getSerializedObj(statuses);
        } else {
            partialProgressStatusesJSON = "[]";
        }
    }
    
    protected void setFindingCatalogsJSON(int moduleId) {
        ElapsedDataDTO tStart = MeasureTime.start(getClass());
        IUntypedDAO dao = getUntypedDAO();
        Long catalogId = Long.valueOf(Properties.CATALOGO_ACCIONES);
        setActionSources(dao.getStrutsComboList(ActionSources.class, "code", "c.moduleId = " + moduleId));
        if (getActionSources().isEmpty()) {
            ActionSources defaultSource = getDefaultSource(dao, moduleId, catalogId);
            getActionSources().add(new TextHasValue(defaultSource.getDescription(), defaultSource.getId()));
        }
        IActionDAO daoAction = Utilities.getBean(IActionDAO.class);
        actionSourcesJSON = daoAction.getSourceJSON(moduleId, catalogId.intValue());
        setDefaultSourceId(getActionSources().get(0).getValue().toString());
        actionTypesJSON = Utilities.getSerializedObj(dao.HQL_selectMapQuery(""
            + " SELECT "
                + " new map("
                    + " c.description AS text,"
                    + " c.id AS value "
                + " )"
            + " FROM " + FindingType.class.getCanonicalName() + " c "
            + " WHERE"
                + " c.status = 1"
                + " AND c.deleted = 0"
            + " ORDER BY c.description ASC",
            false,
            null,
            0
        ));
        priorityJSON = Utilities.getSerializedObj(dao.HQL_selectMapQuery(""
            + " SELECT "
                + " new map("
                    + " c.description AS text,"
                    + " c.id AS value "
                + " )"
            + " FROM " + Priority.class.getCanonicalName() + " c "
            + " WHERE"
                + " c.status = 1"
                + " AND c.deleted = 0"
            + "",
            false,
            null,
            0
        ))
        ;
        actionSystemJSON = Utilities.getSerializedObj(dao.HQL_selectMapQuery(""
            + " SELECT "
                + " new map("
                    + " c.description AS text,"
                    + " c.id AS value "
                + " )"
            + " FROM " + ClauseType.class.getCanonicalName() + " c "
            + " WHERE"
                + " c.status = 1"
                + " AND c.deleted = 0"
            + "",
            false,
            null,
            0
        ))
        ;
        MeasureTime.stop(tStart, "Loading findings catalogs for ID " + getId());
    }
    
    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public String getGotojsp() {
        return gotojsp;
    }

    public void setGotojsp(String gotojsp) {
        this.gotojsp = gotojsp;
    }

    public String getBloqueid() {
        return bloqueid;
    }

    public void setBloqueid(String bloqueid) {
        this.bloqueid = bloqueid;
    }

    public String getSurveyid() {
        return surveyid;
    }

    public void setSurveyid(String surveyid) {
        this.surveyid = surveyid;
    }

    public String getTiendaid() {
        return tiendaid;
    }

    public void setTiendaid(String tiendaid) {
        this.tiendaid = tiendaid;
    }

    public boolean isJustLoad() {
        return justLoad;
    }

    public void setJustLoad(boolean justLoad) {
        this.justLoad = justLoad;
    }

    public String getEstatus() {
        return estatus;
    }

    public void setEstatus(String estatus) {
        this.estatus = estatus;
    }

    public String getOutstandingid() {
        return outstandingid;
    }

    public void setOutstandingid(String outstandingid) {
        this.outstandingid = outstandingid;
    }

    
    
    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    /**
     * @return the html
     */
    public String getHtml() {
        return html;
    }

    public SurveyParser getParser() {
        return parser;
    }

    /**
     * @return the id
     */
    @Override
    public String getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    @Override
    public void setId(String id) {
        this.id = id;
    }


    public boolean isResponderEncuesta() {
        return responderEncuesta;
    }

    public void setResponderEncuesta(boolean responderEncuesta) {
        this.responderEncuesta = responderEncuesta;
    }

    public String getAutoSaveTime() {
        return autoSaveTime;
    }

    public void setAutoSaveTime(String autoSaveTime) {
        this.autoSaveTime = autoSaveTime;
    }

    public String getEndMessage() {
        return endMessage;
    }

    public void setEndMessage(String endMessage) {
        this.endMessage = endMessage;
    }

    public boolean isGuardadoTotalmente() {
        return guardadoTotalmente;
    }

    public void setGuardadoTotalmente(boolean guardadoTotalmente) {
        this.guardadoTotalmente = guardadoTotalmente;
    }

    public boolean isAudit() {
        return audit;
    }

    public void setAudit(boolean audit) {
        this.audit = audit;
    }

    public String getLoadAction() {
        return loadAction;
    }

    public void setLoadAction(String loadAction) {
        this.loadAction = loadAction;
    }

    public boolean isAuditedAccess() {
        return auditedAccess;
    }

    public String getAuditIndividualId() {
        return auditIndividualId;
    }

    public String getAuditIndividualStatus() {
        return auditIndividualStatus;
    }

    public String getAuditIndividualDepartment() {
        return auditIndividualDepartment;
    }

    public List<ITextHasValue> getActionSources() {
        return actionSources;
    }

    public String getAuditIndividualCode() {
        return auditIndividualCode;
    }

    public List<ITextHasValue> getActionTypes() {
        return actionTypes;
    }

    public boolean isPoll() {
        return poll;
    }

    public void setPoll(boolean poll) {
        this.poll = poll;
    }

    protected void setAuditedAccess(boolean auditedAccess) {
        this.auditedAccess = auditedAccess;
    }

    protected void setAuditIndividualId(String auditIndividualId) {
        this.auditIndividualId = auditIndividualId;
    }

    protected void setAuditIndividualStatus(String auditIndividualStatus) {
        this.auditIndividualStatus = auditIndividualStatus;
    }

    protected void setAuditIndividualCode(String auditIndividualCode) {
        this.auditIndividualCode = auditIndividualCode;
    }

    protected void setAuditIndividualDepartment(String auditIndividualDepartment) {
        this.auditIndividualDepartment = auditIndividualDepartment;
    }

    protected void setActionSources(List<ITextHasValue> actionSources) {
        this.actionSources = actionSources;
    }

    protected void setPendiente(IOutstandingSurveysLoadAnswers pendiente) {

        this.pendiente = pendiente;
    }

    protected IOutstandingSurveysLoadAnswers getPendiente() {
        return pendiente;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }
    
    public String getTask() {
        return task == null ? TASK_GOING_TO_PREVIEW : task;
    }

    public final void setTask(String task) {
        this.task = task;
    }

    public boolean isPreviewMode() {
        return previewMode;
    }

    public void setPreviewMode(boolean previewMode) {
        this.previewMode = previewMode;
    }
    
    public Long getRequestId() {
        return requestId;
}

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }
    
    public String getSurveyType() {
        if(this.surveyType == null && pendiente != null && pendiente.getCuestionario() != null) {
            this.surveyType = pendiente.getCuestionario().getType();
        }
        return this.surveyType;
    }

    public final void setSurveyType(String surveyType) {
        this.surveyType = surveyType;
    }

    public List<ITextHasValue> getActionSystem() {
        return actionSystem;
    }

    public void setActionSystem(List<ITextHasValue> actionSystem) {
        this.actionSystem = actionSystem;
    }

    /**
     * @return the showActivities
     */
    public Boolean getShowActivities() {
        return showActivities;
    }

    /**
     * @param showActivities the showActivities to set
     */
    public void setShowActivities(Boolean showActivities) {
        this.showActivities = showActivities;
    }

    public Boolean getShowFormActivities() {
        return showFormActivities;
    }

    public void setShowFormActivities(Boolean showFormActivities) {
        this.showFormActivities = showFormActivities;
    }

    public Boolean getShowFormFindings() {
        return showFormFindings;
    }

    public void setShowFormFindings(Boolean showFormFindings) {
        this.showFormFindings = showFormFindings;
    }

    /**
     * @return the serializedStatusList
     */
    public String getSerializedStatusList() {
        return serializedStatusList;
    }

    /**
     * @param serializedStatusList the serializedStatusList to set
     */
    public void setSerializedStatusList(String serializedStatusList) {
        this.serializedStatusList = serializedStatusList;
    }

    /**
     * @return the activityManager
     */
    public Boolean getActivityManager() {
        return activityManager;
    }

    /**
     * @param activityManager the activityManager to set
     */
    public void setActivityManager(Boolean activityManager) {
        this.activityManager = activityManager;
    }

    /**
     * @return the defaultSourceId
     */
    public String getDefaultSourceId() {
        return defaultSourceId;
    }

    /**
     * @param defaultSourceId the defaultSourceId to set
     */
    public void setDefaultSourceId(String defaultSourceId) {
        this.defaultSourceId = defaultSourceId;
    }

    public ISurveyToHtml getSurveyToHtml() {
        if (daoSurvey == null) {
            daoSurvey = new SurveytoHtmlRenderer(getBean(ISurveysDAO.class));
        }
        return daoSurvey;
    }

    public String getSurveyModule() {
        return surveyModule;
    }

    public void setSurveyModule(String surveyModule) {
        this.surveyModule = surveyModule;
    }

    public String getSerializedOutstandingSurvey() {
        return Encode.forJavaScript(serializedOutstandingSurvey);
    }

    public final String getUserLocale() {
        if (getSession().get("locale") != null) {
            return getSession().get("locale").toString();
        }
        return Utilities.getSettings().getLocale();
    }

    public void setSerializedOutstandingSurvey(String serializedOutstandingSurvey) {
        this.serializedOutstandingSurvey = serializedOutstandingSurvey;
    }

    public String getRequestMode() {
        return requestMode;
    }

    public void setRequestMode(String requestMode) {
        this.requestMode = requestMode;
    }

    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    public boolean getRequestJustStarting() {
        return requestMode.toUpperCase().equals("REQUESTOR");
    }

    public boolean isExitButtonAvailable() {
        return exitButtonAvailable;
    }

    public void setExitButtonAvailable(boolean exitButtonAvailable) {
        this.exitButtonAvailable = exitButtonAvailable;
    }

    public boolean isPrintButtonAvailable() {
        if (this.getOutstandingArchived()) {
            return false;
        }
        return printButtonAvailable;
    }

    public void setPrintButtonAvailable(boolean printButtonAvailable) {
        this.printButtonAvailable = printButtonAvailable;
    }

    public boolean isProgressButtonAvailable() {
        return progressButtonAvailable;
    }

    public void setProgressButtonAvailable(boolean progressButtonAvailable) {
        this.progressButtonAvailable = progressButtonAvailable;
    }

    public boolean isAttachmentButtonAvailable() {
        return attachmentButtonAvailable;
    }

    public void setAttachmentButtonAvailable(boolean attachmentButtonAvailable) {
        this.attachmentButtonAvailable = attachmentButtonAvailable;
    }

    public String getOutstandingCode() {
        return outstandingCode;
    }

    public void setOutstandingCode(String outstandingCode) {
        this.outstandingCode = outstandingCode;
    }

    public Long getProgressStateId() {
        return progressStateId;
    }
    
    public boolean isForm() {
        if (surveyType == null) {
            return false;
        }
        return "request".equals(surveyType);
    }

    /**
     * @return the actionSourcesJSON
     */
    public String getActionSourcesJSON() {
        return actionSourcesJSON;
    }

    /**
     * @param actionSourcesJSON the actionSourcesJSON to set
     */
    public void setActionSourcesJSON(String actionSourcesJSON) {
        this.actionSourcesJSON = actionSourcesJSON;
    }

    /**
     * @return the actionTypesJSON
     */
    public String getActionTypesJSON() {
        return actionTypesJSON;
    }

    /**
     * @param actionTypesJSON the actionTypesJSON to set
     */
    public void setActionTypesJSON(String actionTypesJSON) {
        this.actionTypesJSON = actionTypesJSON;
    }

    public String getPriorityJSON() {
        return priorityJSON;
    }

    public void setPriorityJSON(String priorityJSON) {
        this.priorityJSON = priorityJSON;
    }

    public String getActionSystemJSON() {
        return actionSystemJSON;
    }

    public void setActionSystemJSON(String actionSystemJSON) {
        this.actionSystemJSON = actionSystemJSON;
    }
    
    public String getSurveyTheme() {
        return surveyTheme;
    }

    public void setSurveyTheme(String surveyTheme) {
        this.surveyTheme = surveyTheme;
    }

    public String getPartialProgressStatusesJSON() {
        return partialProgressStatusesJSON;
    }
    
    public static AuditType getAuditType(IUntypedDAO dao, Long outstandingSurveyId) {
        return (AuditType) dao.HQL_findSimpleObject(""
            + " SELECT ind.audit.type"
            + " FROM " + AuditIndividual.class.getCanonicalName() + " ind "
            + " WHERE ind.fill.id = :outstandingSurveyId",
           "outstandingSurveyId", outstandingSurveyId
        );
    }
    
    public static DocumentTypeRef getDocumentType(IUntypedDAO dao, Long documentId) {
        return (DocumentTypeRef) dao.HQL_findSimpleObject(" "
            + " SELECT t "
            + " FROM " + DocumentTypeRef.class.getCanonicalName() + " t "
            + " JOIN " + Document.class.getCanonicalName() + " d "
            + " ON d.typeId = t.id"
            + " WHERE d.id = :documentId",
                "documentId", documentId
        );
    }   
  
    public static String getSurveyTheme(IUntypedDAO dao, Long themeId) {
        return dao.HQL_findSimpleString(""
                + " SELECT c.fileName"
                + " FROM " + SurveyTheme.class.getCanonicalName() + " c "
                + " WHERE"
                + " c.id = :themeId",
                ImmutableMap.of("themeId", themeId),
                true,
                CacheRegion.SURVEY,
                0
        );
    }
    
    protected boolean isReopenAuthorizerAvailableByAny() {
        if (!isReopenRequestDisabled()) {
            getLogger().debug("Not access to reopen authorization options, found status is {} should be APROVING or VERIFING ", this.activeReopenStatus);
            return false;
        }
        return true;
    }
    
    protected boolean isCancelAuthorizerAvailableByAny() {
        if (!isCancelRequestDisabled()) {
            getLogger().debug("Not access to cancel authorization options, found status is {}", this.activeReopenStatus);
            return false;
        }
        return true;
    }

    public boolean isReopenAuthorizerAvailable() {
        final boolean result = isReopenAuthorizerAvailableByAny();
        if (!result) {
            return false;
        }
        if (!Objects.equals(getLoggedUserId(), this.reopenCurrentUserId)) {
            getLogger().debug("Not access to reopen authorization options, expected userId: {}", this.reopenCurrentUserId);
            return false;
        }
        return true;
    }
    
    public boolean isCancelAuthorizerAvailable() {
        final boolean result = isCancelAuthorizerAvailableByAny();
        if (!result) {
            return false;
        }
        if (!Objects.equals(getLoggedUserId(), this.cancelationCurrentUserId)) {
            getLogger().debug("Not access to cancel authorization options, expected userId: {}", this.cancelationCurrentUserId);
            return false;
        }
        return true;
    }
    
    public Boolean getIsCancelAuthorizerAvailableByAdmin() {
        return isCancelAuthorizerAvailableByAdmin;
    }
    
    public void setIsCancelAuthorizerAvailableByAdmin(Boolean isCancelAuthorizerAvailableByAdmin) {
        this.isCancelAuthorizerAvailableByAdmin = isCancelAuthorizerAvailableByAdmin;
    }

    public Boolean getIsReopenAuthorizerAvailableByAdmin() {
        return isReopenAuthorizerAvailableByAdmin;
    }

    protected void setIsReopenAuthorizerAvailableByAdmin(Boolean isReopenAuthorizerAvailableByAdmin) {
        this.isReopenAuthorizerAvailableByAdmin = isReopenAuthorizerAvailableByAdmin;
    }

    public Boolean getIsAdjustmentVerifyAvailableByAdmin() {
        return isAdjustmentVerifyAvailableByAdmin;
    }

    public void setIsAdjustmentVerifyAvailableByAdmin(Boolean isAdjustmentVerifyAvailableByAdmin) {
        this.isAdjustmentVerifyAvailableByAdmin = isAdjustmentVerifyAvailableByAdmin;
    }

    public Boolean getIsAdjustmentAuthorizationAvailableByAdmin() {
        return isAdjustmentAuthorizationAvailableByAdmin;
    }

    public void setIsAdjustmentAuthorizationAvailableByAdmin(Boolean isAdjustmentAuthorizationAvailableByAdmin) {
        this.isAdjustmentAuthorizationAvailableByAdmin = isAdjustmentAuthorizationAvailableByAdmin;
    }

    public boolean isReopenRequestDisabled() {
        return WorkflowRequestStatus.isActive(activeReopenStatus) && this.reopenCurrentUserId != null;
    }
    
    public boolean isCancelRequestDisabled() {
        return WorkflowRequestStatus.isActive(activeCancelationStatus) && this.cancelationCurrentUserId != null;
    }

    protected void setSignatureRejectApprovalRequired(Boolean signatureRejectApprovalRequired) {
        this.signatureRejectApprovalRequired = signatureRejectApprovalRequired;
    }

    public boolean isSignatureRejectApprovalRequired() {
        if (this.flowBusinessUnitDepartmentId == null) {
            getLogger().debug("Reject approval is not available because there's no flowBusinessUnitDepartmentId configured");
            return false;
        }
        return Boolean.TRUE.equals(signatureRejectApprovalRequired);
    }
    
    protected boolean isReopenAvailableByAny() {
        if (this.getOutstandingArchived()) {
            return false;
        }
        if (this.flowBusinessUnitDepartmentId == null) {
            getLogger().debug("Reopen is not available because there's no flowBusinessUnitDepartmentId configured");
            return false;
        }
        if (this.stageLatestFillOutUserId == null) {
            getLogger().debug("Reopen is not available because there's no fill out user configured");
            return false;
        }
        if (!Objects.equals(OutstandingSurveys.STATUS.EXPIRED.getValue().toString(), this.estatus)) {
            getLogger().debug("Reopen is not available because status is not expired");
            return false;
        }
        if (isReopenAuthorizerAvailable()) {
            getLogger().debug("Reopen is not available because a reopen authorization is pending");
            return false;
        }
        return true;
    }

    public boolean isReopenAvailable() {
        if (this.getOutstandingArchived()) {
            return false;
        }
        final boolean result = isReopenAvailableByAny();
        if (!result) {
            return false;
        }
        if (!this.stageLatestFillOutUserId.contains(getLoggedUserId())) {
            getLogger().debug("Reopen is not available because logged user is not the authorized one");
            return false;
        }
        return true;
    }

    public boolean getAdjustmentAuthorizationAvailable() {
        return adjustmentAuthorizationAvailable;
    }

    protected void setAdjustmentAuthorizationAvailable(boolean adjustmentAuthorizationAvailable) {
        this.adjustmentAuthorizationAvailable = adjustmentAuthorizationAvailable;
    }

    public boolean isAdjustmentVerifyAvailable() {
        return adjustmentVerifyAvailable;
    }

    protected void setAdjustmentVerifyAvailable(boolean adjustmentVerifyAvailable) {
        this.adjustmentVerifyAvailable = adjustmentVerifyAvailable;
    }
    
    public Boolean getIsReopenAvailableByAdmin() {
        return isReopenAvailableByAdmin;
    }

    protected void setIsReopenAvailableByAdmin(Boolean isReopenAvailableByAdmin) {
        this.isReopenAvailableByAdmin = isReopenAvailableByAdmin;
    }
    
    public List<Long> getStageLatestFillOutUserId() {
        return stageLatestFillOutUserId;
    }
    
    public void setStageLatestFillOutUserId(List<Long> stageLatestFillOutUserId) {
        this.stageLatestFillOutUserId = stageLatestFillOutUserId;
    }

    public Long getCancelationCurrentUserId() {
        return cancelationCurrentUserId;
    }

    public Long getAdjustmentCurrentUserId() {
        return adjustmentCurrentUserId;
    }

    public Long getReopenCurrentUserId() {
        return reopenCurrentUserId;
    }

    public String getCancelationCurrentUserName() {
        return cancelationCurrentUserName;
    }

    public String getAdjustmentCurrentUserName() {
        return adjustmentCurrentUserName;
    }

    public String getReopenCurrentUserName() {
        return reopenCurrentUserName;
    }

    public void setCancelationCurrentUserId(Long cancelationCurrentUserId) {
        this.cancelationCurrentUserId = cancelationCurrentUserId;
    }

    public void setAdjustmentCurrentUserId(Long adjustmentCurrentUserId) {
        this.adjustmentCurrentUserId = adjustmentCurrentUserId;
    }

    public void setReopenCurrentUserId(Long reopenCurrentUserId) {
        this.reopenCurrentUserId = reopenCurrentUserId;
    }

    public void setCancelationCurrentUserName(String cancelationCurrentUserName) {
        this.cancelationCurrentUserName = cancelationCurrentUserName;
    }

    public void setAdjustmentCurrentUserName(String adjustmentCurrentUserName) {
        this.adjustmentCurrentUserName = adjustmentCurrentUserName;
    }

    public void setReopenCurrentUserName(String reopenCurrentUserName) {
        this.reopenCurrentUserName = reopenCurrentUserName;
    }

    public Long getFlowBusinessUnitId() {
        return flowBusinessUnitId;
    }

    public void setFlowBusinessUnitId(Long flowBusinessUnitId) {
        this.flowBusinessUnitId = flowBusinessUnitId;
    }

    public Long getFlowBusinessUnitDepartmentId() {
        return flowBusinessUnitDepartmentId;
    }

    public void setFlowBusinessUnitDepartmentId(Long flowBusinessUnitDepartmentId) {
        this.flowBusinessUnitDepartmentId = flowBusinessUnitDepartmentId;
    }

    public Long getFlowAreaId() {
        return flowAreaId;
    }

    public void setFlowAreaId(Long flowAreaId) {
        this.flowAreaId = flowAreaId;
    }

    public Long getActiveCancelationFormRequestId() {
        return activeCancelationFormRequestId;
    }

    protected void setActiveCancelationFormRequestId(Long activeCancelationFormRequestId) {
        this.activeCancelationFormRequestId = activeCancelationFormRequestId;
    }

    public Long getActiveAdjustmentFormRequestId() {
        return activeAdjustmentFormRequestId;
    }

    protected void setActiveAdjustmentFormRequestId(Long activeAdjustmentFormRequestId) {
        this.activeAdjustmentFormRequestId = activeAdjustmentFormRequestId;
    }

    public Integer getActiveAdjustmentRejectedAutorizationPoolIndex() {
        return activeAdjustmentRejectedAutorizationPoolIndex;
    }

    protected void setActiveAdjustmentRejectedAutorizationPoolIndex(Integer activeAdjustmentRejectedAutorizationPoolIndex) {
        this.activeAdjustmentRejectedAutorizationPoolIndex = activeAdjustmentRejectedAutorizationPoolIndex;
    }

    public Long getActiveReopenFormRequestId() {
        return activeReopenFormRequestId;
    }

    protected void setActiveReopenFormRequestId(Long activeReopenFormRequestId) {
        this.activeReopenFormRequestId = activeReopenFormRequestId;
    }

    public String getActiveCancelationReason() {
        return activeCancelationReason;
    }

    protected void setActiveCancelationReason(String activeCancelationReason) {
        this.activeCancelationReason = activeCancelationReason;
    }

    public String getActiveAdjustmentReason() {
        return activeAdjustmentReason;
    }

    protected void setActiveAdjustmentReason(String activeAdjustmentReason) {
        this.activeAdjustmentReason = activeAdjustmentReason;
    }

    public String getActiveReopenReason() {
        return activeReopenReason;
    }

    protected void setActiveReopenReason(String activeReopenReason) {
        this.activeReopenReason = activeReopenReason;
    }

    public Integer getActiveCancelationStatus() {
        return activeCancelationStatus;
    }

    protected void setActiveCancelationStatus(Integer activeCancelationStatus) {
        this.activeCancelationStatus = activeCancelationStatus;
    }

    public Integer getActiveAdjustmentStatus() {
        return activeAdjustmentStatus;
    }

    protected void setActiveAdjustmentStatus(Integer activeAdjustmentStatus) {
        this.activeAdjustmentStatus = activeAdjustmentStatus;
    }

    public Integer getActiveReopenStatus() {
        return activeReopenStatus;
    }

    protected void setActiveReopenStatus(Integer activeReopenStatus) {
        this.activeReopenStatus = activeReopenStatus;
    }

    public String getCancelationRequestorUserName() {
        return cancelationRequestorUserName;
    }

    protected void setCancelationRequestorUserName(String cancelationRequestorUserName) {
        this.cancelationRequestorUserName = cancelationRequestorUserName;
    }

    public String getAdjustmentRequestorUserName() {
        return adjustmentRequestorUserName;
    }

    protected void setAdjustmentRequestorUserName(String adjustmentRequestorUserName) {
        this.adjustmentRequestorUserName = adjustmentRequestorUserName;
    }

    public String getReopenRequestorUserName() {
        return reopenRequestorUserName;
    }

    protected void setReopenRequestorUserName(String reopenRequestorUserName) {
        this.reopenRequestorUserName = reopenRequestorUserName;
    }

    public Integer getCancelationCurrentUserStatus() {
        return cancelationCurrentUserStatus;
    }

    protected void setCancelationCurrentUserStatus(Integer cancelationCurrentUserStatus) {
        this.cancelationCurrentUserStatus = cancelationCurrentUserStatus;
    }

    public Integer getAdjustmentCurrentUserStatus() {
        return adjustmentCurrentUserStatus;
    }

    protected void setAdjustmentCurrentUserStatus(Integer adjustmentCurrentUserStatus) {
        this.adjustmentCurrentUserStatus = adjustmentCurrentUserStatus;
    }

    public Integer getReopenCurrentUserStatus() {
        return reopenCurrentUserStatus;
    }

    protected void setReopenCurrentUserStatus(Integer reopenCurrentUserStatus) {
        this.reopenCurrentUserStatus = reopenCurrentUserStatus;
    }

    public WorkflowAuthRole getAuthRole() {
        return authRole;
    }

    public Boolean getHasAccessToDefitiveCancelWithAuth() {
        return hasAccessToDefitiveCancelWithAuth;
    }

    public void setHasAccessToDefitiveCancelWithAuth(Boolean hasAccessToDefitiveCancelWithAuth) {
        this.hasAccessToDefitiveCancelWithAuth = hasAccessToDefitiveCancelWithAuth;
    }

    public boolean getOutstandingArchived() {
        return outstandingArchived && this.getArchived();
    }

    protected void setOutstandingArchived(Boolean outstandingArchived) {
        this.outstandingArchived = Boolean.TRUE.equals(outstandingArchived);
    }

    public String getConditionalValidatorCacheId() {
        return conditionalValidatorCacheId;
    }

    public String getShow_hide_buttons() {
        return show_hide_buttons;
    }

    public void setShow_hide_buttons(String show_hide_buttons) {
        this.show_hide_buttons = show_hide_buttons;
    }

    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        if (documentMasterId == null || documentMasterId.trim().isEmpty() || documentMasterId.trim().contains("{") || documentMasterId.trim().contains("%")) {
            this.documentMasterId = null;
        } else {
            this.documentMasterId = Encode.forHtmlAttribute(documentMasterId);
        }
    }

    public String getDocumentDescription() {
        return documentDescription;
    }

    public void setDocumentDescription(String documentDescription) {
        this.documentDescription = Encode.forHtmlAttribute(documentDescription);
    }

    public boolean getEnableOpenPdfFiles() {
        return true;
    }
    public Long getDocumentTypeId() {
        return null;
    }

    public Long getDteFechaInicio() {
        return dteFechaInicio.getTime();
    }
}
