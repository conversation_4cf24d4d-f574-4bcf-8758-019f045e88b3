package isoblock.surveys.dao.hibernate;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.form.util.IFieldEditable;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "survey_field_editable")
public class SurveyFieldEditable extends CompositeStandardEntity<SurveyFieldEditablePK>
        implements Serializable, ILinkedComposityGrid<SurveyFieldEditablePK>, IFieldEditable {

    private static final long serialVersionUID = 1L;

    private SurveyFieldEditablePK id;
    private String fieldId;
    private SurveyFieldObject fieldObject;

    public SurveyFieldEditable() {
    }

    public SurveyFieldEditable(SurveyFieldEditablePK id) {
        this.id = id;
    }

    public SurveyFieldEditable(Long fieldObjectId, Long editableSectionId, String fieldId) {
        this.id = new SurveyFieldEditablePK(fieldObjectId, editableSectionId);
        this.fieldId = fieldId;
    }

    @Override
    public SurveyFieldEditablePK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public SurveyFieldEditablePK getId() {
        return id;
    }

    @Override
    public void setId(SurveyFieldEditablePK id) {
        this.id = id;
    }

    @Column(name = "field_id")
    public String getFieldId() {
        return fieldId;
    }

    public void setFieldId(String fieldId) {
        this.fieldId = fieldId;
    }

    @JoinColumn(name = "field_object_id", referencedColumnName = "field_object_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public SurveyFieldObject getFieldObject() {
        return fieldObject;
    }

    public void setFieldObject(SurveyFieldObject fieldObject) {
        this.fieldObject = fieldObject;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyFieldEditable other = (SurveyFieldEditable) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.SurveyFieldEditable{" + "id=" + id + '}';
    }

}
