package isoblock.surveys.dao.hibernate;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;
import javax.annotation.Nonnull;
import javax.persistence.Cacheable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.form.util.ISurveyProgressStateReject;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.ILinkedComposityGrid;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "survey_progress_state_reject")
public class SurveyProgressStateReject extends CompositeStandardEntity<SurveyProgressStateRejectPK> 
        implements Serializable, ILinkedComposityGrid<SurveyProgressStateRejectPK>,
        ISurveyProgressStateReject, Comparable<SurveyProgressStateReject> {

    private static final long serialVersionUID = 1L;

    private SurveyProgressStateRejectPK id;
    private SurveyFieldObject fieldObject;

    public SurveyProgressStateReject() {
    }

    public SurveyProgressStateReject(SurveyProgressStateRejectPK id) {
        this.id = id;
    }

    public SurveyProgressStateReject(Long fieldObjectId, Long formProgressStateId) {
        this.id = new SurveyProgressStateRejectPK(fieldObjectId, formProgressStateId);
    }

    @Override
    public SurveyProgressStateRejectPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public SurveyProgressStateRejectPK getId() {
        return id;
    }

    @Override
    public void setId(SurveyProgressStateRejectPK id) {
        this.id = id;
    }

    @JoinColumn(name = "field_object_id", referencedColumnName = "field_object_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public SurveyFieldObject getFieldObject() {
        return fieldObject;
    }

    public void setFieldObject(SurveyFieldObject fieldObject) {
        this.fieldObject = fieldObject;
    }

    @Override
    public int compareTo(@Nonnull SurveyProgressStateReject other) {
        if (this.getId() == null) {
            return -1;
        }
        return this.getId().compareTo(other.getId());
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyProgressStateReject other = (SurveyProgressStateReject) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "SurveyProgressStateReject{" + "id=" + id + '}';
    }


}