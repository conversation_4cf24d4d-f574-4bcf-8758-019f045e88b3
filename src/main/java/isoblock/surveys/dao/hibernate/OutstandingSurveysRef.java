package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import bnext.reference.IAuditable;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.apache.struts2.json.annotations.JSON;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.interfaces.IBusinessUnitDepartmentId;

/**
 *
 * <AUTHOR> Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "OUTSTANDING_SURVEYS")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class OutstandingSurveysRef extends DomainObject
        implements Serializable, IAuditable, IBusinessUnitDepartmentId, IOutstandingSurveys {

    private static final long serialVersionUID = 1L;
    
    private String code;
    private Short estatus;
    private Integer status;
    private Integer deleted = 0;
    private Double score;
    private Long surveyId;		
    private Long documentId;
    private Long businessUnitDepartmentId;
    private UserRef creatorUser;
    private Long requestId;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private Long formCancelRequestId;
    private Boolean archived = false;

    private String conditionalValidatorCacheId;
    private String editedOutstandingidTitle;
    private String surveyRequestMode;
    private boolean signatureAsSaveBehavior = false;//Transient
    private Long nextUserToFill;
    private Long progressStateId;
    private List<OutstandingQuestion> preguntasRespondidas;
    private Date dteFechaInicio;
    private Integer saveAction;

    public OutstandingSurveysRef() {
    }

    public OutstandingSurveysRef(Long id) {
        this.id = id;
    }

    public OutstandingSurveysRef(Long id, Short estatus) {
        this.id = id;
        this.estatus = estatus;
    }

    @Override
    @Transient
    public DomainObject getCuestionario() {
        if (surveyId != null) {
            return new DomainObject(surveyId);
        }
        return null;
    }

    @OneToMany
    @JoinTable(
            name="OUTSTANDING_SURVEYS_QUESTION",
            joinColumns = {
                    @JoinColumn(name = "OUTSTANDING_SURVEYS_ID")},
            inverseJoinColumns = @JoinColumn( name="QUESTION_ID")
    )
    @LazyCollection(LazyCollectionOption.FALSE)
    @Fetch(value = FetchMode.SUBSELECT)
    @OrderBy(value="INT_ORDER")
    @BatchSize(size = 5)
    public List<OutstandingQuestion> getPreguntasRespondidas() {
        return preguntasRespondidas;
    }

    public void setPreguntasRespondidas(List<OutstandingQuestion> preguntasRespondidas) {
        this.preguntasRespondidas = preguntasRespondidas;
    }

    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Override
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "OUTSTANDING_SURVEYS_ID", nullable = false, precision = 19, scale = 0)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
    
    @Basic(optional = false)
    @Column(name = "status", nullable = false)
    public Short getEstatus() {
        return estatus;
    }

    public void setEstatus(Short estatus) {
        this.estatus = estatus;
    }
    
    @Column(name = "score")
    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    @Basic(optional = false)
    @Column(name = "status", nullable = false, insertable = false, updatable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    
    @JSON(serialize = false)
    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(referencedColumnName = "user_id", name = "creator_user_id")
    public UserRef getCreatorUser() {
        return creatorUser;
    }

    public void setCreatorUser(UserRef creatorUser) {
        this.creatorUser = creatorUser;
    }

    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }
    
    @Column(name = "business_unit_department_id")
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }
    
    @Column(name = "survey_id")
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Column(name = "form_cancel_request_id")
    public Long getFormCancelRequestId() {
        return formCancelRequestId;
    }

    public void setFormCancelRequestId(Long formCancelRequestId) {
        this.formCancelRequestId = formCancelRequestId;
    }
    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "form_progress_state_id")
    public Long getProgressStateId() {
        return progressStateId;
    }

    public void setProgressStateId(Long progressStateId) {
        this.progressStateId = progressStateId;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "archived")
    @Type(type = "numeric_boolean")
    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    @Override
    @Transient
    public Long getOutstandingSurveyId() {
        return this.id;
    }

    @Transient
    public String getConditionalValidatorCacheId() {
        return conditionalValidatorCacheId;
    }

    public void setConditionalValidatorCacheId(String conditionalValidatorCacheId) {
        this.conditionalValidatorCacheId = conditionalValidatorCacheId;
    }


    @Transient
    public String getSurveyRequestMode() {
        return surveyRequestMode;
    }

    public void setSurveyRequestMode(String surveyRequestMode) {
        this.surveyRequestMode = surveyRequestMode;
    }

    @Transient
    public Boolean isSignatureAsSaveBehavior() {
        return signatureAsSaveBehavior;
    }

    public void setSignatureAsSaveBehavior(boolean signaturAsSaveBehavior) {
        this.signatureAsSaveBehavior = signaturAsSaveBehavior;
    }

    @Transient
    public String getEditedOutstandingidTitle() {
        return editedOutstandingidTitle;
    }

    public void setEditedOutstandingidTitle(String editedOutstandingidTitle) {
        this.editedOutstandingidTitle = editedOutstandingidTitle;
    }

    @Transient
    public Long getNextUserToFill() {
        return nextUserToFill;
    }

    public void setNextUserToFill(Long nextUserToFill) {
        this.nextUserToFill = nextUserToFill;
    }

    @Column(name = "DTE_start_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getDteFechaInicio() {
        return dteFechaInicio;
    }

    public void setDteFechaInicio(Date dteFechaInicio) {
        this.dteFechaInicio = dteFechaInicio;
    }

    @Transient
    @Override
    public Integer getSaveAction() {
        return saveAction;
    }

    public void setSaveAction(Integer saveAction) {
        this.saveAction = saveAction;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof OutstandingSurveysRef)) {
            return false;
        }
        OutstandingSurveysRef other = (OutstandingSurveysRef) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.OutstandingSurveysRef[ id=" + id + " ]";
    }
}
