package isoblock.surveys.dao.hibernate;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;
import javax.annotation.Nonnull;
import javax.persistence.Cacheable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.form.util.ISurveyProgressStatePartial;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.ILinkedComposityGrid;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "survey_progress_state_partial")
public class SurveyProgressStatePartial extends CompositeStandardEntity<SurveyProgressStatePartialPK> 
        implements Serializable, ILinkedComposityGrid<SurveyProgressStatePartialPK>,
        ISurveyProgressStatePartial, Comparable<SurveyProgressStatePartial> {

    private static final long serialVersionUID = 1L;

    private SurveyProgressStatePartialPK id;
    private SurveyFieldObject fieldObject;

    public SurveyProgressStatePartial() {
    }

    public SurveyProgressStatePartial(SurveyProgressStatePartialPK id) {
        this.id = id;
    }

    public SurveyProgressStatePartial(Long fieldObjectId, Long formProgressStateId) {
        this.id = new SurveyProgressStatePartialPK(fieldObjectId, formProgressStateId);
    }

    @Override
    public SurveyProgressStatePartialPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public SurveyProgressStatePartialPK getId() {
        return id;
    }

    @Override
    public void setId(SurveyProgressStatePartialPK id) {
        this.id = id;
    }

    @JoinColumn(name = "field_object_id", referencedColumnName = "field_object_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public SurveyFieldObject getFieldObject() {
        return fieldObject;
    }

    public void setFieldObject(SurveyFieldObject fieldObject) {
        this.fieldObject = fieldObject;
    }

    @Override
    public int compareTo(@Nonnull SurveyProgressStatePartial other) {
        if (this.getId() == null) {
            return -1;
        }
        return this.getId().compareTo(other.getId());
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyProgressStatePartial other = (SurveyProgressStatePartial) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "SurveyProgressStatePartial{" + "id=" + id + '}';
    }


}