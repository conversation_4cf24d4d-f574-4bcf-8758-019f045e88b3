package isoblock.surveys.dao.hibernate;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.form.util.IFieldEditablePK;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class SurveyFieldEditablePK implements Serializable, IFieldEditablePK {

    private Long fieldObjectId;
    private Long editableSectionId;

    public SurveyFieldEditablePK(Long fieldObjectId, Long editableSectionId) {
        this.fieldObjectId = fieldObjectId;
        this.editableSectionId = editableSectionId;
    }

    public SurveyFieldEditablePK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "field_object_id")
    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }

    @Override
    @DialogId
    @Basic(optional = false)
    @Column(name = "editable_section_id")
    public Long getEditableSectionId() {
        return editableSectionId;
    }

    public void setEditableSectionId(Long editableSectionId) {
        this.editableSectionId = editableSectionId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.fieldObjectId);
        hash = 53 * hash + Objects.hashCode(this.editableSectionId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyFieldEditablePK other = (SurveyFieldEditablePK) obj;
        if (!Objects.equals(this.fieldObjectId, other.fieldObjectId)) {
            return false;
        }
        return Objects.equals(this.editableSectionId, other.editableSectionId);
    }

    @Override
    public String toString() {
        return "SurveyFieldEditablePK{" + "fieldObjectId=" + fieldObjectId + ", editableSectionId=" + editableSectionId + '}';
    }

}
