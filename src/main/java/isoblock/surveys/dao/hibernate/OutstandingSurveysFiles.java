/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package isoblock.surveys.dao.hibernate;

import DPMS.Mapping.FilesLite;
import Framework.Config.DomainObject;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "OUTSTANDING_SURVEYS")
public class OutstandingSurveysFiles extends DomainObject implements IAuditable {

    private Set<FilesLite> files;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    public OutstandingSurveysFiles() {
    }

    public OutstandingSurveysFiles(Long id) {
        this.id = id;
    }
    
    @Override
    @Id
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "outstanding_surveys_id", nullable = false, precision = 19, scale = 0)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "outstanding_surveys_files",
        joinColumns = {
            @JoinColumn(name = "outstanding_surveys_id")
        },
        inverseJoinColumns = @JoinColumn(name = "file_id", referencedColumnName = "id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<FilesLite> getFiles() {
        return files;
    }

    public void setFiles(Set<FilesLite> files) {
        this.files = files;
    }
    
    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }


}
