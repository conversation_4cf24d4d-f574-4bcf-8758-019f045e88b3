package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import bnext.aspect.IExcludeLogging;
import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.interfaces.IOutstandingAnswer;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "OUTSTANDING_ANSWER")
public class OutstandingAnswer extends DomainObject implements Serializable, IExcludeLogging, IOutstandingAnswer {

    private static final long serialVersionUID = 1L;


    private Boolean isCatalogHierarchyEntityKeyId;
    private Boolean isSummary;
    private Date exchangeRateDate;
    private Date fechaCaptura;
    private Long filledVersion;
    private Date rangeEnd;
    private Date rangeStart;
    private Double exchangeConversionRate;
    private Double exchangeRateSource;
    private Integer deleted = 0;
    private Long areaId;
    private Long businessUnitDepartmentId;
    private Long businessUnitId;
    private Long columnaAreaplazaId;
    private Long columnaId;
    private Long exchangeRateId;
    private Long fileId;
    private Long opcionId;
    private Long opcionMatrizId;
    private Long outstandingSurveyId;
    private Long rangeSeconds;
    private Long timeworkId;
    private OutstandingQuestion pregunta;
    private String catalogHierarchyColumn;
    private String catalogOptionLabel;
    private String catalogOptionValue;
    private String descripcion;
    private String descripcionPlainText;
    private String fileContentType;
    private String fileExtension;
    private String imageBase64;

    public OutstandingAnswer() {
    }

    public OutstandingAnswer(Long id) {
        this.id = id;
    }
    public OutstandingAnswer(IOutstandingAnswer entity) {
        this.areaId = entity.getAreaId();
        this.businessUnitDepartmentId = entity.getBusinessUnitDepartmentId();
        this.businessUnitId = entity.getBusinessUnitId();
        this.catalogHierarchyColumn = entity.getCatalogHierarchyColumn();
        this.catalogOptionLabel = entity.getCatalogOptionLabel();
        this.catalogOptionValue = entity.getCatalogOptionValue();
        this.columnaId = entity.getColumnaId();
        this.deleted = 0;
        this.descripcion = entity.getDescripcion();
        this.descripcionPlainText = entity.getDescripcionPlainText();
        this.exchangeConversionRate = entity.getExchangeConversionRate();
        this.exchangeRateDate = entity.getExchangeRateDate();
        this.exchangeRateId = entity.getExchangeRateId();
        this.exchangeRateSource = entity.getExchangeRateSource();
        this.fileContentType = entity.getFileContentType();
        this.fileExtension = entity.getFileExtension();
        this.fileId = entity.getFileId();
        this.imageBase64 = entity.getImageBase64();
        this.isCatalogHierarchyEntityKeyId = entity.getIsCatalogHierarchyEntityKeyId();
        this.isSummary = entity.getIsSummary();
        this.opcionId = entity.getOpcionId();
        this.outstandingSurveyId = entity.getOutstandingSurveyId();
        this.rangeEnd = entity.getRangeEnd();
        this.rangeSeconds = entity.getRangeSeconds();
        this.rangeStart = entity.getRangeStart();
        this.timeworkId = entity.getTimeworkId();
        this.pregunta = entity.getPregunta() != null ? new OutstandingQuestion(entity.getPregunta()) : null;
        //this.columnaAreaplazaId = entity.getColumnaAreaplazaId();
        this.fechaCaptura = entity.getFechaCaptura();
        this.filledVersion = entity.getFilledVersion();
        //this.opcionMatrizId = entity.getOpcionMatrizId();
    }
    public OutstandingAnswer(OutstandingQuestion pregunta) {
        this.pregunta = pregunta;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    @Column(name = "ANSWER_ID", nullable = false, precision = 19, scale = 0)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "AREA_DEPARTMENT_ID")
    public Long getColumnaAreaplazaId() {
        return columnaAreaplazaId;
    }

    public void setColumnaAreaplazaId(Long columnaAreaplazaId) {
        this.columnaAreaplazaId = columnaAreaplazaId;
    }

    @Column(name = "HEADER_ID")
    @Override
    public Long getColumnaId() {
        return columnaId;
    }

    public void setColumnaId(Long columnaId) {
        this.columnaId = columnaId;
    }

    @Column(name = "ITEM_ID")
    @Override
    public Long getOpcionId() {
        return opcionId;
    }

    public void setOpcionId(Long opcionId) {
        this.opcionId = opcionId;
    }

    @Column(name = "VCH_ANSWER")
    @Override
    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    @Column(name = "answer_plain_text")
    public String getDescripcionPlainText() {
        return descripcionPlainText;
    }

    public void setDescripcionPlainText(String descripcionPlainText) {
        this.descripcionPlainText = descripcionPlainText;
    }
    
    @JoinColumn(name = "QUESTION_ID", referencedColumnName = "QUESTION_ID", nullable = false)
    @ManyToOne(optional = false)
    @Override
    public OutstandingQuestion getPregunta() {
        return pregunta;
    }

    public void setPregunta(OutstandingQuestion pregunta) {
        this.pregunta = pregunta;
    }
    
    @Column(name = "MATRIX_OPTION_ID")
    public Long getOpcionMatrizId() {
        return opcionMatrizId;
    }

    public void setOpcionMatrizId(Long opcionMatrizId) {
        this.opcionMatrizId = opcionMatrizId;
    }

    @Override
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @Basic
    @Column(name = "TMP_CAPTURED_DATE")
    public Date getFechaCaptura() {
        return fechaCaptura;
    }

    public void setFechaCaptura(Date fechaCaptura) {
        this.fechaCaptura = fechaCaptura;
    }

    @Column(name = "filled_version")
    public Long getFilledVersion() {
        return filledVersion;
    }

    public void setFilledVersion(Long fillerUserId) {
        this.filledVersion = fillerUserId;
    }

    @Column(name = "CATALOG_OPTION_VALUE")
    @Override
    public String getCatalogOptionValue() {
        return catalogOptionValue;
    }

    public void setCatalogOptionValue(String catalogOptionValue) {
        if (catalogOptionValue == null) {
            this.catalogOptionValue = null;
        } else {
            this.catalogOptionValue = StringUtils.substring(catalogOptionValue, 0, 255);
        }
    }
    
    @Column(name = "CATALOG_OPTION_LABEL")
    @Override
    public String getCatalogOptionLabel() {
        return catalogOptionLabel;
    }

    public void setCatalogOptionLabel(String catalogOptionLabel) {
        if (catalogOptionLabel == null) {
            this.catalogOptionLabel = null;
        } else {
            this.catalogOptionLabel = StringUtils.substring(catalogOptionLabel, 0, 255);
        }
    }

    /**
     * @return the fileId
     */
    @Column(name = "file_id")
    @Override
    public Long getFileId() {
        return fileId;
    }

    /**
     * @param fileId the fileId to set
     */
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    /**
     * @return the fileContentType
     */
    @Column(name = "file_content_type")
    @Override
    public String getFileContentType() {
        return fileContentType;
    }

    /**
     * @param fileContentType the fileContentType to set
     */
    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    @Column(name = "file_extension")
    @Override
    public String getFileExtension() {
        return fileExtension;
    }

    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;

    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "catalog_hier_column")
    @Override
    public String getCatalogHierarchyColumn() {
        return catalogHierarchyColumn;
    }

    public void setCatalogHierarchyColumn(String catalogHierarchyColumn) {
        this.catalogHierarchyColumn = catalogHierarchyColumn;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "is_cat_hier_entity_key")
    @Override
    public Boolean getIsCatalogHierarchyEntityKeyId() {
        return isCatalogHierarchyEntityKeyId;
    }

    public void setIsCatalogHierarchyEntityKeyId(Boolean isCatalogHierarchyEntityKeyId) {
        this.isCatalogHierarchyEntityKeyId = isCatalogHierarchyEntityKeyId;
    }

    @Column(name = "image_base_64")
    public String getImageBase64() {
        return imageBase64;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    @Override
    @Column(name = "range_start")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getRangeStart() {
        return rangeStart;
    }

    public void setRangeStart(Date rangeStart) {
        this.rangeStart = rangeStart;
    }

    @Column(name = "range_end")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getRangeEnd() {
        return rangeEnd;
    }

    public void setRangeEnd(Date rangeEnd) {
        this.rangeEnd = rangeEnd;
    }

    @Column(name = "range_seconds")
    public Long getRangeSeconds() {
        return rangeSeconds;
    }

    public void setRangeSeconds(Long rangeSeconds) {
        this.rangeSeconds = rangeSeconds;
    }

    @Column(name = "timework_id")
    public Long getTimeworkId() {
        return timeworkId;
    }

    public void setTimeworkId(Long timeworkId) {
        this.timeworkId = timeworkId;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "is_summary")
    @Override
    public Boolean getIsSummary() {
        return isSummary;
    }

    public void setIsSummary(Boolean isSummary) {
        this.isSummary = isSummary;
    }
    
    @Column(name = "business_unit_id")
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id")
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "area_id")
    @Override
    public Long getAreaId() {
        return areaId;
    }

    @Override
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }
    
    @Column(name = "exchange_rate_id")
    @Override
    public Long getExchangeRateId() {
        return exchangeRateId;
    }

    public void setExchangeRateId(Long exchangeRateId) {
        this.exchangeRateId = exchangeRateId;
    }

    @Column(name = "exchange_rate_source")
    @Override
    public Double getExchangeRateSource() {
        return exchangeRateSource;
    }

    public void setExchangeRateSource(Double exchangeRateSource) {
        this.exchangeRateSource = exchangeRateSource;
    }

    @Column(name = "exchange_conversion_rate")
    @Override
    public Double getExchangeConversionRate() {
        return exchangeConversionRate;
    }

    public void setExchangeConversionRate(Double exchangeConversionRate) {
        this.exchangeConversionRate = exchangeConversionRate;
    }

    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @Column(name = "exchange_rate_date")
    @Override
    public Date getExchangeRateDate() {
        return exchangeRateDate;
    }

    public void setExchangeRateDate(Date exchangeRateDate) {
        this.exchangeRateDate = exchangeRateDate;
    }
    
    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    @Transient
    public SurveyItem getOpcion() {
        if (opcionId == null) {
            return null;
        }
        return new SurveyItem(opcionId);
    }

    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    public void setOpcion(SurveyItem opcion) {
        if (opcion != null) {
            this.opcionId = opcion.getId();
        }
    }
    
    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    @Transient
    public SurveyHeader getColumna() {
        if (columnaId == null) {
            return null;
        }
        return new SurveyHeader(columnaId);
    }

    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    public void setColumna(SurveyHeader columna) {
        if (columna != null) {
            this.columnaId = columna.getId();
        }
    }

    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    @Transient
    public AreaDepartment getColumnaAreaplaza() {
        if (columnaAreaplazaId == null) {
            return null;
        }
        return new AreaDepartment(columnaAreaplazaId);
    }

    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    public void setColumnaAreaplaza(AreaDepartment columnaAreaplaza) {
        if (columnaAreaplaza != null) {
            this.columnaAreaplazaId = columnaAreaplaza.getId();
        }
    }
    
    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    @Transient
    public SurveyMatrixOptions getOpcionMatriz() {
        if (opcionMatrizId == null) {
            return null;
        }
        return new SurveyMatrixOptions(opcionMatrizId);
    }

    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    public void setOpcionMatriz(SurveyMatrixOptions opcionMatriz) {
        if (opcionMatriz != null) {
            this.opcionMatrizId = opcionMatriz.getId();
        }
    }

    @Column(name = "outstanding_surveys_id")
    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof OutstandingAnswer)) {
            return false;
        }
        OutstandingAnswer other = (OutstandingAnswer) object;
        return !((this.id == null && other.id != null) 
                || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "OutstandingAnswer{" + "descripcion=" + descripcion +
                ", pregunta=" + pregunta + ", opcionId=" + opcionId + ", columnaId=" +
                columnaId + ", catalogOptionValue=" + catalogOptionValue +
                ", fileId=" + fileId + '}';
    }
}

