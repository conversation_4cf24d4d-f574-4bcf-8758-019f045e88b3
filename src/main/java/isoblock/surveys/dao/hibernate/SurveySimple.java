package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import jakarta.persistence.Transient;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.survey.interfaces.IPlainSurvey;

/**
 * Entity de solo lectura y sin caché, no hacer cambios o no se verán reflejados en el caché, ver Survey
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Table(name = "SURVEY")
public class SurveySimple extends DomainObject implements Serializable, IPlainSurvey {

    private static final long serialVersionUID = 1L;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    
    private Integer estatus;
    private Integer hasAnswers;
    private String type;
    private String code;
    private String answersTable;
    private String mailsToOnCompletedFillForm;
    private String mailsSubjectOnCompletedFill;
    private Integer isTemplateUseAvailable;
    private Integer isAddAttachmentEnabled = 0;
    private Integer isMailIncludeAnswers = 0;
    private Integer addFindingsEnabled = 0;
    private Integer addActivitiesEnabled = 0;
    private Long addAttachmentTotalLimit; // <-- bytes

    public SurveySimple() {
    }
    
    public SurveySimple(Long id) {
        this.id = id;
    }
    
    @Id
    @Basic(optional = false)
    @Column(name = "SURVEY_ID", nullable = false, precision = 19)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "vch_code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Transient
    @Override
    public String getDescription() {
        return code;
    }

    @Override
    public void setDescription(String description) {
        this.code = description;
    }

    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "INT_B_HAS_ANSWER")
    public Integer getHasAnswers() {
        return hasAnswers;
    }

    public void setHasAnswers(Integer hasAnswers) {
        if(hasAnswers == null) {
            hasAnswers = 0;
        }
        this.hasAnswers = hasAnswers;
    }
    
    @Column(name = "STATUS")
    public Integer getEstatus() {
        return estatus;
    }

    public void setEstatus(Integer estatus) {
        this.estatus = estatus;
    }

    @Column(name = "is_template_use_available")
    public Integer getIsTemplateUseAvailable() {
        return isTemplateUseAvailable;
    }

    public void setIsTemplateUseAvailable(Integer isTemplateUseAvailable) {
        this.isTemplateUseAvailable = isTemplateUseAvailable;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof SurveySimple)) {
            return false;
        }
        SurveySimple other = (SurveySimple) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "isoblock.surveys.dao.hibernate.SurveySimple[ id=" + id + " ]";
    }

    @Column(name = "answers_table")
    public String getAnswersTable() {
        return answersTable;
    }

    public void setAnswersTable(String answersTable) {
        this.answersTable = answersTable;
    }

    @Column(name = "mails_to_oncompletedfillform")
    public String getMailsToOnCompletedFillForm() {
        return mailsToOnCompletedFillForm;
    }

    public void setMailsToOnCompletedFillForm(String mailsToOnCompletedFillForm) {
        this.mailsToOnCompletedFillForm = mailsToOnCompletedFillForm;
    }

    @Column(name = "add_attachment_enabled")
    public Integer getIsAddAttachmentEnabled() {
        return isAddAttachmentEnabled;
    }

    public void setIsAddAttachmentEnabled(Integer isAddAttachmentEnabled) {
        this.isAddAttachmentEnabled = isAddAttachmentEnabled;
    }

    @Column(name = "mail_include_answers")
    public Integer getIsMailIncludeAnswers() {
        return isMailIncludeAnswers;
    }

    public void setIsMailIncludeAnswers(Integer isMailIncludeAnswers) {
        this.isMailIncludeAnswers = isMailIncludeAnswers;
    }

    @Column(name = "add_findings_enabled")
    public Integer getAddFindingsEnabled() {
        return addFindingsEnabled;
    }

    public void setAddFindingsEnabled(Integer addFindingsEnabled) {
        this.addFindingsEnabled = addFindingsEnabled;
    }

    @Column(name = "add_activities_enabled")
    public Integer getAddActivitiesEnabled() {
        return addActivitiesEnabled;
    }

    public void setAddActivitiesEnabled(Integer addActivitiesEnabled) {
        this.addActivitiesEnabled = addActivitiesEnabled;
    }

    @Column(name = "add_attachment_total_limit")
    public Long getAddAttachmentTotalLimit() {
        return addAttachmentTotalLimit;
    }

    public void setAddAttachmentTotalLimit(Long addAttachmentTotalLimit) {
        this.addAttachmentTotalLimit = addAttachmentTotalLimit;
    }

    @Column(name = "mails_subject_oncompletedfill")
    public String getMailsSubjectOnCompletedFill() {
        return mailsSubjectOnCompletedFill;
    }

    public void setMailsSubjectOnCompletedFill(String mailsSubjectOnCompletedFill) {
        this.mailsSubjectOnCompletedFill = mailsSubjectOnCompletedFill;
    }

    
}
