package isoblock.surveys.dao.hibernate;

/**
 *
 * <AUTHOR>
 */
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.IDepartmentProcess;
import Framework.Config.DomainObject;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "department_process_view")
@Immutable
public class DepartmentProcessViewLite extends DomainObject implements Serializable, IDepartmentProcess {

    public static final Integer INACTIVE_STATUS = 0;
    public static final Integer ACTIVE_STATUS = 1;
    private static final long serialVersionUID = 1L;

    private Integer status;
    private String code;
    private String description;
    private BusinessUnitDepartmentLite businessUnitDepartment;

    public DepartmentProcessViewLite() {
    }

    public DepartmentProcessViewLite(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "department_process_id", nullable = false)
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long validationId) {
        this.id = validationId;
    }

    @Column(name = "status", nullable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "code", nullable = false)
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof DepartmentProcessViewLite)) {
            return false;
        }
        DepartmentProcessViewLite other = (DepartmentProcessViewLite) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    @Column(name = "title", insertable = false, updatable = false)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @OneToOne
    @JoinColumn(referencedColumnName = "business_unit_department_id", name = "business_unit_department_id")
    public BusinessUnitDepartmentLite getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(BusinessUnitDepartmentLite department) {
        this.businessUnitDepartment = department;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.DepartmentProcessViewLite[ id=" + id + " ]";
    }
}
