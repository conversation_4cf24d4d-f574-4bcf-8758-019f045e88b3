package isoblock.surveys.dao.hibernate;

/**
 *
 * <AUTHOR>
 */
import DPMS.Mapping.IDepartmentProcess;
import Framework.Config.DomainObject;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "department_process_view")
@Immutable
public class DepartmentProcessViewRef extends DomainObject implements Serializable, IDepartmentProcess {

    private static final long serialVersionUID = 1L;

    private Long businessUnitDepartmentId;
    private Long processId;
    private Long attendantId;
    private Integer status;
    private String code;
    private String title, description;

    public DepartmentProcessViewRef() {
    }

    public DepartmentProcessViewRef(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "department_process_id", nullable = false)
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long validationId) {
        this.id = validationId;
    }

    @Column(name = "attendant_id", nullable = false)
    public Long getAttendantId() {
        return attendantId;
    }

    public void setAttendantId(Long attendantId) {
        this.attendantId = attendantId;
    }

    @Column(name = "business_unit_department_id", nullable = false)
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "process_id", nullable = false)
    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    @Column(name = "status", nullable = false)
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "code", nullable = false)
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "title", insertable = false, updatable = false)
    @Override
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Column(name = "title", nullable = false)
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof DepartmentProcessViewRef)) {
            return false;
        }
        DepartmentProcessViewRef other = (DepartmentProcessViewRef) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.DepartmentProcessViewRef[ Id=" + id + " ]";
    }
}
