package isoblock.surveys.dao.hibernate;


import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "DIM_CRITERIOS_EVALUACION_ISLT")
public class DimCriteriosEvaluacionIslt extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    
    private Integer temaId;
    private BigInteger tipocriterioevalId;
    private String temaDes;
    private String criterioevalDes;
    private String criterioevalDesCorta;
    private BigInteger criterioevalOrder;
    private String tipocriterioevalDes;
    private BigInteger criterioevalKey;
    private Date loaddate;
    private BigInteger criterioevalscore;
    private BigInteger cveencuesta;
    private String descencuesta;

    public DimCriteriosEvaluacionIslt() {
    }

    public boolean tranferToBI(String reason) {
        return false;
    }
    /*public boolean tranferToBI(String reason) {
        String insert = ""
            + " INSERT INTO DIM_CRITERIOS_EVALUACION_ISLT "
                + "("
                + " TEMA_ID "
                + ", CRITERIOEVAL_CVE "
                + ", TIPOCRITERIOEVAL_ID "
                + ", TEMA_DES "
                + ", CRITERIOEVAL_DES "
                + ", CRITERIOEVAL_ORDER "
                + ", LOADDATE "
                + ", CVEENCUESTA "
                + ", DESCENCUESTA "
                + ")"
            + " VALUES "
                + "("
                + getTemaId()
                + "," + getId()
                + "," + getTipocriterioevalId()
                + ",'" + getTemaDes() + "'"
                + ",'" + getCriterioevalDes() + "'"
                + "," + getCriterioevalOrder()
                + ",dbo.trunc(current_timestamp)"
                + "," + getCveencuesta()
                + ",'" + getDescencuesta() + "'"
                + ")"
                ;
        try {
            Database db = new Database();
            db.setDATABASE_SERVER(db.DATA_SOURCE_BI);
            db.updateNoVal(insert);
        } catch(Exception e) {
            getLogger().error("Stack trace: ",e);
            System.out.println("INSERT: "+insert);
            return false;
        }
        return true;
    }*/
    @Column(name = "TEMA_ID")
    public Integer getTemaId() {
        return temaId;
    }

    public void setTemaId(Integer temaId) {
        this.temaId = temaId;
    }

    @Column(name = "CRITERIOEVAL_CVE")
    @Override
    @Id
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "TIPOCRITERIOEVAL_ID")
    public BigInteger getTipocriterioevalId() {
        return tipocriterioevalId;
    }

    public void setTipocriterioevalId(BigInteger tipocriterioevalId) {
        this.tipocriterioevalId = tipocriterioevalId;
    }

    @Column(name = "TEMA_DES", length = 50)
    public String getTemaDes() {
        if(temaDes != null) {
            temaDes = /*StringEscapeUtils.escapeHtml*/(temaDes
                    .replace('á', 'a')
                    .replace('é', 'e')
                    .replace('í', 'i')
                    .replace('ó', 'o')
                    .replace('ú', 'u')
                    .replace('Á', 'e')
                    .replace('É', 'i')
                    .replace('Í', 'o')
                    .replace('Ó', 'u')
                    .replace('Ú', 'a')
                    ).replaceAll("[^\\x00-\\x7F]", "");
            if(temaDes.length() > 50) {
                temaDes = temaDes.substring(0, 47)+"...";
            }
        }
        return temaDes;
    }

    public void setTemaDes(String temaDes) {
        this.temaDes = temaDes;
    }

    @Column(name = "CRITERIOEVAL_DES", length = 4000)
    public String getCriterioevalDes() {
        if(criterioevalDes != null) {
            criterioevalDes = /*StringEscapeUtils.escapeHtml*/(criterioevalDes
                    .replace('á', 'a')
                    .replace('é', 'e')
                    .replace('í', 'i')
                    .replace('ó', 'o')
                    .replace('ú', 'u')
                    .replace('Á', 'e')
                    .replace('É', 'i')
                    .replace('Í', 'o')
                    .replace('Ó', 'u')
                    .replace('Ú', 'a')
                    ).replaceAll("[^\\x00-\\x7F]", "");
            if(criterioevalDes.length() > 4000) {
                criterioevalDes = criterioevalDes.substring(0, 3997)+"...";
            }
        }
        return criterioevalDes;
    }

    public void setCriterioevalDes(String criterioevalDes) {
        this.criterioevalDes = criterioevalDes;
    }

    @Column(name = "CRITERIOEVAL_DES_CORTA", length = 100)
    public String getCriterioevalDesCorta() {
        if(criterioevalDesCorta != null) {
            criterioevalDesCorta = /*StringEscapeUtils.escapeHtml*/(criterioevalDesCorta
                    .replace('á', 'a')
                    .replace('é', 'e')
                    .replace('í', 'i')
                    .replace('ó', 'o')
                    .replace('ú', 'u')
                    .replace('Á', 'e')
                    .replace('É', 'i')
                    .replace('Í', 'o')
                    .replace('Ó', 'u')
                    .replace('Ú', 'a')
                    ).replaceAll("[^\\x00-\\x7F]", "");
            if(criterioevalDesCorta.length() > 100) {
                criterioevalDesCorta = criterioevalDesCorta.substring(0, 97)+"...";
            }
        }
        return criterioevalDesCorta;
    }

    public void setCriterioevalDesCorta(String criterioevalDesCorta) {
        this.criterioevalDesCorta = criterioevalDesCorta;
    }

    @Column(name = "CRITERIOEVAL_ORDER")
    public BigInteger getCriterioevalOrder() {
        return criterioevalOrder;
    }

    public void setCriterioevalOrder(BigInteger criterioevalOrder) {
        this.criterioevalOrder = criterioevalOrder;
    }

    @Column(name = "TIPOCRITERIOEVAL_DES", length = 30)
    public String getTipocriterioevalDes() {
        if(tipocriterioevalDes != null) {
            tipocriterioevalDes = /*StringEscapeUtils.escapeHtml*/(tipocriterioevalDes
                    .replace('á', 'a')
                    .replace('é', 'e')
                    .replace('í', 'i')
                    .replace('ó', 'o')
                    .replace('ú', 'u')
                    .replace('Á', 'e')
                    .replace('É', 'i')
                    .replace('Í', 'o')
                    .replace('Ó', 'u')
                    .replace('Ú', 'a')
                    ).replaceAll("[^\\x00-\\x7F]", "");
            if(tipocriterioevalDes.length() > 30) {
                tipocriterioevalDes = tipocriterioevalDes.substring(0, 27)+"...";
            }
        }
        return tipocriterioevalDes;
    }

    public void setTipocriterioevalDes(String tipocriterioevalDes) {
        this.tipocriterioevalDes = tipocriterioevalDes;
    }

    @Column(name = "CRITERIOEVAL_KEY")
    public BigInteger getCriterioevalKey() {
        return criterioevalKey;
    }

    public void setCriterioevalKey(BigInteger criterioevalKey) {
        this.criterioevalKey = criterioevalKey;
    }

    @Column(name = "LOADDATE")
    @Temporal(TemporalType.DATE)
    public Date getLoaddate() {
        return loaddate;
    }

    public void setLoaddate(Date loaddate) {
        this.loaddate = loaddate;
    }

    @Column(name = "CRITERIOEVALSCORE")
    public BigInteger getCriterioevalscore() {
        return criterioevalscore;
    }

    public void setCriterioevalscore(BigInteger criterioevalscore) {
        this.criterioevalscore = criterioevalscore;
    }

    @Column(name = "CVEENCUESTA")
    public BigInteger getCveencuesta() {
        return cveencuesta;
    }

    public void setCveencuesta(BigInteger cveencuesta) {
        this.cveencuesta = cveencuesta;
    }

    @Column(name = "DESCENCUESTA", length = 50)
    public String getDescencuesta() {
        if(descencuesta != null) {
            descencuesta = /*StringEscapeUtils.escapeHtml*/(descencuesta
                    .replace('á', 'a')
                    .replace('é', 'e')
                    .replace('í', 'i')
                    .replace('ó', 'o')
                    .replace('ú', 'u')
                    .replace('Á', 'e')
                    .replace('É', 'i')
                    .replace('Í', 'o')
                    .replace('Ó', 'u')
                    .replace('Ú', 'a')
                    ).replaceAll("[^\\x00-\\x7F]", "");
            if(descencuesta.length() > 50) {
                descencuesta = descencuesta.substring(0, 47)+"...";
            }
        }
        return descencuesta;
    }

    public void setDescencuesta(String descencuesta) {
        this.descencuesta = descencuesta;
    }
}
