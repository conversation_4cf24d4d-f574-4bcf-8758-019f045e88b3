package isoblock.surveys.dao.hibernate;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Comparator;
import java.util.Objects;
import javax.annotation.Nonnull;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.form.util.ISurveyConditional;
import qms.framework.util.CacheConstants;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "survey_conditional")
public class SurveyConditional extends DomainObject
        implements Serializable, ISurveyConditional, Comparable<SurveyConditional> {

    private static final long serialVersionUID = 1L;

    private Long conditionalOrder;
    private Integer conditionalType;
    private Long fieldObjectId;
    private Long conditionalQuestionId;
    private Long conditionalAnswerId;
    private SurveyFieldObject fieldObject;
    private String conditionalAnswerSelector;
    private String conditionalQuestionCode;
    private String conditionalAnswerCode;
    private Integer hierarchyRow;
    private Integer hierarchyLevel;
    private String hierarchyColumn;
    private String hierarchyValue;
    private Integer type;                                   /**  <---- {@link  qms.form.util.SurveyConditionalType#getValue()} */
    private Integer daysToExpire = 0;                       /**  <---- {Si vale `0` es lo mismo que "desactivado"} */
    private Integer daysToNotifyBeforeExpiration = 0;
    private Double maxLim;
    private Double minLim;
    private Double eqLim;
    private Long surveyId;

    public SurveyConditional() {
    }
    
    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "survey_conditional_id", nullable = false, precision = 19, scale = 0)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "conditional_order")
    public Long getConditionalOrder() {
        return conditionalOrder;
    }

    public void setConditionalOrder(Long conditionalOrder) {
        this.conditionalOrder = conditionalOrder;
    }

    @Column(name = "conditional_type")
    public Integer getConditionalType() {
        return conditionalType;
    }

    public void setConditionalType(Integer conditionalType) {
        this.conditionalType = conditionalType;
    }

    @Override
    @Basic(optional = false)
    @Column(name = "field_object_id")
    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }

    @Override
    @Basic(optional = false)
    @Column(name = "conditional_question_id")
    public Long getConditionalQuestionId() {
        return conditionalQuestionId;
    }

    public void setConditionalQuestionId(Long conditionalQuestionId) {
        this.conditionalQuestionId = conditionalQuestionId;
    }
    
    @Override
    @Column(name = "conditional_answer_id")
    public Long getConditionalAnswerId() {
        return conditionalAnswerId;
    }

    public void setConditionalAnswerId(Long conditionalAnswerId) {
        this.conditionalAnswerId = conditionalAnswerId;
    }    

    @JoinColumn(name = "field_object_id", referencedColumnName = "field_object_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public SurveyFieldObject getFieldObject() {
        return fieldObject;
    }

    public void setFieldObject(SurveyFieldObject fieldObject) {
        this.fieldObject = fieldObject;
    }

    @Column(name = "conditional_answer_selector")
    public String getConditionalAnswerSelector() {
        return conditionalAnswerSelector;
    }

    public void setConditionalAnswerSelector(String conditionalAnswerSelector) {
        this.conditionalAnswerSelector = conditionalAnswerSelector;
    }

    @Column(name = "conditional_question_code")
    public String getConditionalQuestionCode() {
        return conditionalQuestionCode;
    }

    public void setConditionalQuestionCode(String conditionalQuestionCode) {
        this.conditionalQuestionCode = conditionalQuestionCode;
    }

    @Column(name = "conditional_answer_code")
    public String getConditionalAnswerCode() {
        return conditionalAnswerCode;
    }

    public void setConditionalAnswerCode(String conditionalAnswerCode) {
        this.conditionalAnswerCode = conditionalAnswerCode;
    }

    @Column(name = "hierarchy_row")
    public Integer getHierarchyRow() {
        return hierarchyRow;
    }

    public void setHierarchyRow(Integer hierarchyRow) {
        this.hierarchyRow = hierarchyRow;
    }    

    @Column(name = "hierarchy_level")
    public Integer getHierarchyLevel() {
        return hierarchyLevel;
    }

    public void setHierarchyLevel(Integer hierarchyLevel) {
        this.hierarchyLevel = hierarchyLevel;
    }

    @Override
    @Column(name = "hierarchy_column")
    public String getHierarchyColumn() {
        return hierarchyColumn;
    }

    public void setHierarchyColumn(String hierarchyColumn) {
        this.hierarchyColumn = hierarchyColumn;
    }

    @Override
    @Column(name = "hierarchy_value")
    public String getHierarchyValue() {
        return hierarchyValue;
    }

    public void setHierarchyValue(String hierarchyValue) {
        this.hierarchyValue = hierarchyValue;
    }

    /**
     * Contiene el tipo de condicional para el que se.
     *
     * @see  qms.form.util.SurveyConditionalType
     *
     * @return {{{@link  qms.form.util.SurveyConditionalType#getValue()}}}
     */
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "days_to_expire")
    public Integer getDaysToExpire() {
        return daysToExpire;
    }

    public void setDaysToExpire(Integer daysToExpire) {
        this.daysToExpire = daysToExpire;
    }

    @Column(name = "days_to_notify_before_exp")
    public Integer getDaysToNotifyBeforeExpiration() {
        return daysToNotifyBeforeExpiration;
    }

    public void setDaysToNotifyBeforeExpiration(Integer daysToNotifyBeforeExpiration) {
        this.daysToNotifyBeforeExpiration = daysToNotifyBeforeExpiration;
    }

    @Column(name = "max_lim")
    public Double getMaxLim() {
        return maxLim;
    }

    public void setMaxLim(Double maxLim) {
        this.maxLim = maxLim;
    }

    @Column(name = "min_lim")
    public Double getMinLim() {
        return minLim;
    }

    public void setMinLim(Double minLim) {
        this.minLim = minLim;
    }

    @Column(name = "eq_lim")
    public Double getEqLim() {
        return eqLim;
    }

    public void setEqLim(Double eqLim) {
        this.eqLim = eqLim;
    }

    @Column(name = "survey_id")
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 53 * hash + Objects.hashCode(this.conditionalOrder);
        hash = 53 * hash + Objects.hashCode(this.conditionalType);
        hash = 53 * hash + Objects.hashCode(this.conditionalAnswerSelector);
        hash = 53 * hash + Objects.hashCode(this.conditionalQuestionCode);
        hash = 53 * hash + Objects.hashCode(this.conditionalAnswerCode);
        hash = 53 * hash + Objects.hashCode(this.hierarchyColumn);
        hash = 53 * hash + Objects.hashCode(this.hierarchyValue);
        hash = 53 * hash + Objects.hashCode(this.hierarchyRow);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyConditional other = (SurveyConditional) obj;
        if (!Objects.equals(this.conditionalAnswerSelector, other.conditionalAnswerSelector)) {
            return false;
        }
        if (!Objects.equals(this.conditionalQuestionCode, other.conditionalQuestionCode)) {
            return false;
        }
        if (!Objects.equals(this.conditionalAnswerCode, other.conditionalAnswerCode)) {
            return false;
        }
        if (!Objects.equals(this.hierarchyColumn, other.hierarchyColumn)) {
            return false;
        }
        if (!Objects.equals(this.hierarchyValue, other.hierarchyValue)) {
            return false;
        }
        if (!Objects.equals(this.hierarchyRow, other.hierarchyRow)) {
            return false;
        }
        if (!Objects.equals(this.conditionalType, other.conditionalType)) {
            return false;
        }
        if (!Objects.equals(this.conditionalOrder, other.conditionalOrder)) {
            return false;
        }
        if (!Objects.equals(this.fieldObjectId, other.fieldObjectId)) {
            return false;
        }
        if (!Objects.equals(this.conditionalQuestionId, other.conditionalQuestionId)) {
            return false;
        }
        if (!Objects.equals(this.conditionalAnswerId, other.conditionalAnswerId)) {
            return false;
        }
        return Objects.equals(this.type, other.type);
    }

    @Override
    public String toString() {
        return "SurveyConditional{" 
                + "conditionalOrder=" + conditionalOrder
                + ", conditionalType=" + conditionalType
                + ", conditionalQuestionCode=" + conditionalQuestionCode 
                + ", conditionalAnswerCode=" + conditionalAnswerCode 
                + ", hierarchyColumn=" + hierarchyColumn 
                + ", hierarchyValue=" + hierarchyValue  
                + ", hierarchyRow=" + hierarchyRow 
                + '}';
    }

    @Override
    public int compareTo(@Nonnull SurveyConditional o) {
        return Comparator.comparing(SurveyConditional::getConditionalOrder, Comparator.nullsFirst(Long::compareTo))
                .thenComparing(SurveyConditional::getId, Comparator.nullsFirst(Long::compareTo))
                .thenComparing(SurveyConditional::getConditionalType, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(SurveyConditional::getConditionalQuestionCode, Comparator.nullsFirst(String::compareTo))
                .thenComparing(SurveyConditional::getConditionalAnswerCode, Comparator.nullsFirst(String::compareTo))
                .thenComparing(SurveyConditional::getHierarchyColumn, Comparator.nullsFirst(String::compareTo))
                .thenComparing(SurveyConditional::getHierarchyValue, Comparator.nullsFirst(String::compareTo))
                .thenComparing(SurveyConditional::getHierarchyRow, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(SurveyConditional::getHierarchyLevel, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(SurveyConditional::getType, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(SurveyConditional::getDaysToExpire, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(SurveyConditional::getDaysToNotifyBeforeExpiration, Comparator.nullsFirst(Integer::compareTo))
                .thenComparing(SurveyConditional::getMaxLim, Comparator.nullsFirst(Double::compareTo))
                .thenComparing(SurveyConditional::getMinLim, Comparator.nullsFirst(Double::compareTo))
                .thenComparing(SurveyConditional::getEqLim, Comparator.nullsFirst(Double::compareTo))
                .thenComparing(SurveyConditional::getSurveyId, Comparator.nullsFirst(Long::compareTo))
                .thenComparing(SurveyConditional::getFieldObjectId, Comparator.nullsFirst(Long::compareTo))
                .thenComparing(SurveyConditional::getConditionalQuestionId, Comparator.nullsFirst(Long::compareTo))
                .thenComparing(SurveyConditional::getConditionalAnswerId, Comparator.nullsFirst(Long::compareTo))
                .thenComparing(SurveyConditional::getConditionalAnswerSelector, Comparator.nullsFirst(String::compareTo))
                .compare(this, o);
    }
}
