package isoblock.surveys.dao.hibernate;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class SurveyFieldAreaPlazaPK implements Serializable {

    private Long areaPlazaId;
    private Long fieldObjectId;

    public SurveyFieldAreaPlazaPK() {
    }

    public SurveyFieldAreaPlazaPK(Long areaPlazaId, Long fieldObjectId) {
        this.areaPlazaId = areaPlazaId;
        this.fieldObjectId = fieldObjectId;
    }

    @Basic(optional = false)
    @Column(name = "AREA_DEPARTMENT_ID", nullable = false)
    public Long getAreaPlazaId() {
        return areaPlazaId;
    }

    public void setAreaPlazaId(Long areaPlazaId) {
        this.areaPlazaId = areaPlazaId;
    }

    @Basic(optional = false)
    @Column(name = "FIELD_OBJECT_ID", nullable = false)
    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 83 * hash + Objects.hashCode(this.areaPlazaId);
        hash = 83 * hash + Objects.hashCode(this.fieldObjectId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SurveyFieldAreaPlazaPK other = (SurveyFieldAreaPlazaPK) obj;
        if (!Objects.equals(this.areaPlazaId, other.areaPlazaId)) {
            return false;
        }
        if (!Objects.equals(this.fieldObjectId, other.fieldObjectId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "SurveyFieldAreaPlazaPK{" + "areaPlazaId=" + areaPlazaId + ", fieldObjectId=" + fieldObjectId + '}';
    }
}
