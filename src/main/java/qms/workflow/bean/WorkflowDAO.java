/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.workflow.bean;

import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.Mapping.AutorizationPool;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.Position;
import DPMS.Mapping.Request;
import DPMS.Mapping.User;
import DPMS.Mapping.WorkflowPool;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.SaveHandle;
import bnext.exception.ExplicitRollback;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.document.dto.AutomaticAuthorizationDTO;
import qms.document.entity.WorkflowSimple;
import qms.document.listeners.OnAutoAuthByInactiveUser;
import qms.document.listeners.OnAutomaticPositionAuthorization;
import qms.document.listeners.OnAutomaticUserAuthorization;
import qms.document.listeners.OnFillFormRecurrence;
import qms.form.conditional.ConditionalValidator;
import qms.form.dto.AutorizationPoolDetailsDTO;
import qms.form.dto.OutstandingSurveysAttendantInfo;
import qms.framework.entity.Owner;
import qms.framework.util.CacheRegion;
import qms.survey.logic.ConditionalValidatorCache;
import qms.util.OwnerUtil;
import qms.util.QMSException;
import qms.util.dto.UserPositionDTO;
import qms.workflow.dto.PoolCreatedDTO;
import qms.workflow.util.IAutorizationPoolDetails;
import qms.workflow.util.IWorkflowBaseDAO;
import qms.workflow.util.IWorkflowDAO;
import qms.workflow.util.IWorkflowRequest;
import qms.workflow.util.RequestAuthorizeResponse;
import qms.workflow.util.SequenceDetailGenerator;
import qms.workflow.util.WorkflowAuthRole;
import qms.workflow.util.WorkflowPreviewGenerator;
import qms.workflow.util.WorkflowRequestStatus;
import qms.workflow.util.WorkflowSupported;

/**
 *
 *   Este DAO contiene toda la funcionalidad necesaria para llenar las tablas de flujos, necerias para
 *   construir nuevos pendientes del tipo "Por verificar" y "Por autorizar".
 * <p>
 *   PASOS PARA IMPLEMENTAR:
 *	1. Definir el entity que pasará por el flujo de autorización,
 *		Ej. `Request.java` o `FormRequest.java`
 * <p>
 *		1.1) Es el entity debe guarará la información de la solicitud de autorización, comentarios, quien lo pidió, etc.
 * <p>
 *	2. Al entity elegido, implementar la interfaz `IWorkflowImplementationEntity`
 *	3. Agregar una columna de tipo ID con el nombre del entity a la interfaz y enum.
 *		Ej. `requestId`
 * <p>
 *		`qms.workflow.interfaces.ISupportedEntityIds` 
 *		`qms.workflow.util.WorkflowSupported` 
 * <p>
 *	4. Definir el entity para guardar información de flujos que implemente `IWorkflowPreview`,
 *		Ej. `WorkflowRequestData.java` y `WorkflowFormRequestData.java`
 * <p>
 *		4.1) Agregar scripts de tabla para implementación `IWorkflowPreview`
 *			- Duplicar el siguiente archivo y reemplazar las ocurrencias de `form_request`, `form_request_id` y `workflow_form_request_data` por el nuevo TABLA y ID correspondiente:
 * <p>
 *				\db\workflow\db.form_request_data.xml
 * <p>
 *		4.2) Es necesario para:
 *			- Especificar una clase propia para separar el uso de la columna de `entity_id` (Ej. `request_id`)
 *			- Especificar el ID, esta tabla maneja un ID compuesto de acuerdo a sus columnas,
 * <p>
 *				Ej. 1: `[request_id] 		+ "-" + [workflow_pool_id]+ "-" +[user_id]+ "-" +[position_id]+ "-" +[boss_id]`
 *				Ej. 2: `[form_request_id] 	+ "-" + [workflow_pool_id]+ "-" +[user_id]+ "-" +[position_id]+ "-" +[boss_id]`
 * <p>
 *
 *	5. Resolver todas las dependencias de entities que la implementan, a la fecha de escribir este manual, son las siguientes:
 * <p>
 *              5.1) AutorizationPool
 *			-> autorization_pool
 *              5.2) AutorizationPoolDetails
 *			-> autorization_pool_details
 * <p>
 *	6. Implementar nueva clase para `ISequenceDetailProvider` y agregarla en `EntityInterceptor`
 *		Ej. `SequenceDetailFormRequest.java`
 * <p>
 *	7. Implementar los pendientes de verificarión y autorización:
 *		Ej. `ToVerifyFormReopenRequest.java`, `ToVerifyRequest.java`, `ToAuthorizeRequest.java`
 * <p>
 *              7.1) Notese que dichos pendientes implementan una variación en SQL de `sequence_detail` y `sequence_detail_provider`
 * <p>
 *      8. Agregar el caso nuevo en `FormPendingMonitor` y `FormMailMonitor` para `OnCreatedNextFill`
 *      9. Agregar el pendiente `ToAuthorize...` que consulte al equivalente `ISequenceDetailProvider` (revisar el query de `ToAuthorizeAdjustmentRequest` a `SequenceDetailFormRequest`)
 *	   
 * <AUTHOR>
 */
@Lazy
@Repository(value = "WorkflowDAO")
@Scope(value = "singleton")
public class WorkflowDAO extends GenericDAOImpl<AutorizationPool, Long> implements IWorkflowDAO {


    /**
     * Función que maneja el siguiente flujo de autorización.
     * @param skipCheck bandera que señala si revisará si el bloque anterior fue completamente
     * autorizado.
     * @param holder      el DAO que realiza tareas específicas del flujo que se está trabajando.
     * @param entity      el objeto que contiene el flujo que se está trabajando.
     * @param loggedUser usuario que está logueado en el sistema.
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void workflowNextOrEnd(
            boolean skipCheck,
            IWorkflowBaseDAO holder,
            IWorkflowRequest entity,
            WorkflowAuthRole authRole,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        getLogger().trace("executeWorkflow");
        if (!isLastAutorizationPool(holder, entity.getId())) {
            if (skipCheck || isLastPoolFinished(
                    holder,
                    entity.getId(),
                    entity.getSurveyRequestMode(),
                    entity.getConditionalValidatorCacheId(),
                    loggedUser
            )) {
                PoolCreatedDTO poolCreatedDTO = createNextPool(holder, entity, loggedUser);
                /*
                  Las solicitudes de llenado nunca se autorizan
                  automáticamente
                  */
                if (isAutomaticallyAuthorized(holder, entity, authRole, loggedUser, poolCreatedDTO.getNextAPD())) {
                    // Autorización automática del flujo
                    workflowNextOrEnd(true, holder, entity, authRole, loggedUser);
                }
            }
        } else if (isLastPoolFinished(
                holder,
                entity.getId(),
                entity.getSurveyRequestMode(),
                entity.getConditionalValidatorCacheId(),
                loggedUser
        )) {
            holder.onWorkflowEnd(entity.getId(), loggedUser, true);
        }
    }
    
    /**
     * Obtiene los datos del siguiente pool de autorización
     *
     * @param request Request a procesar
     * @return Map con: status - Con valores de: usual: Comportamiendo de
     * aprovación normal. recurrrence: Comportamiento para aprovación con
     * rechazo e iteración de autorización. nextIndex - Index de autorización
     * siguiente lastActiveIndex - Último index autorizado recurrence - Detalle
     * del siguiente pool de autorización
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map getNextAuthorizationPoolData(IWorkflowBaseDAO holder, IWorkflowRequest request, ILoggedUser loggedUser) {
        Map nextPoolMap = new HashMap();
        nextPoolMap.put("status", "usual");
        AutorizationPool authPool = holder.getPool(request, loggedUser);
        //Se modifica por referencia (el makePersistent lo hace despues)
        request.setAutorizationPool(authPool);
        if (authPool != null) {
            request.setAutorizationPoolId(authPool.getId());
        }
        Set<AutorizationPoolDetails> authDetails = authPool.getAutorizationPoolDetailsList();
        Integer nextIndex = 1;
        if (authDetails != null && authDetails.size() > 0) {
            nextIndex = getNextIndexToAuthorize(authDetails);
            getLogger().trace("processNextAuthorizationPool@new index {}", nextIndex);
            List<AutorizationPoolDetails> apds = filterAuthorizationDetailsByIndex(authDetails, nextIndex);
            if (!apds.isEmpty()) {
                getLogger().debug("authorizationPoolIndex, an inactive pool exists", nextIndex);
                nextPoolMap.put("status", "recurrence");
                nextPoolMap.put("recurrence", apds);
            }
        } else {
            authPool.setAutorizationPoolDetailsList(new TreeSet<AutorizationPoolDetails>());
        }
        getLogger().trace("processNextAuthorizationPool@index: {}", nextIndex);
        nextPoolMap.put("nextIndex", nextIndex);
        nextPoolMap.put("lastActiveIndex", nextIndex - 1);
        return nextPoolMap;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateRequestAuthorizatorNames(IWorkflowBaseDAO holder, final Long requestId) {
        if (requestId == null || Objects.equals(-1L, requestId)) {
            return;
        }
        final String authorizatorNames = getRequestAuthorizatorNames(holder.getWorkflowSettings(), requestId);
        if (authorizatorNames == null || authorizatorNames.isEmpty()) {
            HQL_updateByQuery(" "
                + " UPDATE " + getEntityClazz(holder).getCanonicalName() + " c "
                + " SET c.authorizersNames = null "
                + " WHERE c.id = :id", "id", requestId
            );
        } else {
            final String newValue = StringUtils.truncate(authorizatorNames, 3999);
            HQL_updateByQuery(" "
                    + " UPDATE " + getEntityClazz(holder).getCanonicalName() + " c "
                    + " SET c.authorizersNames = :authorizatorNames "
                    + " WHERE c.id = :id",
                    ImmutableMap.of(
                        "id", requestId,
                        "authorizatorNames", newValue
                    )
            );
        }
    }
    
    @Override
    @OnAutoAuthByInactiveUser
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean skipAuthorization(
            IWorkflowBaseDAO holder,
            Long requestId,
            Long surveyId,
            String conditionalValidatorCacheId,
            Integer requestType,
            List<AutorizationPoolDetails> details,
            SurveyRequestMode mode,
            ILoggedUser loggedUser
    ) {
        String description;
        StringBuilder name = new StringBuilder();
        Date now = Utilities.getNow();
        for (AutorizationPoolDetails detail: details) {
            Long userWhoAuthorized = loggedUser.getId();
            detail.setAccepted(AutorizationPoolDetails.SKIPPED);
            detail.setFinishDate(now);
            List<String> descriptions;
            if(detail.getOwner() == null && detail.getOwnerId() != null) {
                detail.setOwner(new Owner(detail.getOwnerId()));
            }
            Owner.TYPE type = OwnerUtil.getType(detail.getOwner(), this);
            boolean omitted = omittedSection(
                    detail.getIndice(),
                    surveyId,
                    detail.getOutstandingSurveyId(),
                    requestId,
                    mode,
                    conditionalValidatorCacheId
            );
            if (omitted) {
                description = "Autorización omitida por sección condicionada.";
                descriptions = new ArrayList<>();
                AutomaticAuthorizationDTO validation = getAutomaticAuthorizationUser(holder, detail);
                if (validation != null) {
                    userWhoAuthorized = validation.getUser().getUserId();
                } else {
                    userWhoAuthorized = null;
                }
            } else {
                switch (type) {
                    case POSITION:
                        description = getTag("automaticAuthorizationByPosition");
                        descriptions = HQL_findByQuery(" "
                                        + " SELECT p.description"
                                        + " FROM " + Position.class.getCanonicalName() + " p "
                                        + " WHERE p.id IN ("
                                        + " SELECT pos.positionId "
                                        + " FROM " + Owner.class.getCanonicalName() + " o "
                                        + " JOIN o.positions pos"
                                        + " WHERE o.id = :ownerId"
                                        + ")",
                                "ownerId", detail.getOwnerId()
                        );
                        break;
                    case BOSS:
                    case REQUESTOR:
                    case USER:
                        description = getTag("automaticAuthorizationByInactiveUser");
                        descriptions = OwnerUtil.getCurrentUserNames(holder.getWorkflowSettings(), detail.getOwnerId(), requestId, this);
                        break;
                    case TEAM_BY_GROUP:
                    case GLOBAL_TEAM:
                    case USER_TO_BE_DEFINED:
                    default:
                        throw new RuntimeException("Automatic authorization is not available for type '" + detail.getOwner() + "'");
                }
            }
            name.setLength(0);
            if(descriptions.isEmpty()) {
                name.append("-Invalid-");
            } else {
                name.append(StringUtils.join(descriptions, ", "));
            }
            detail.setDescription(
                description.replace(":name:", name.toString())
            );
            detail.setUserId(userWhoAuthorized);
            makePersistent(detail, loggedUser.getId());
        }
        return true;
    }
    
    

    /**
     * Función que determina si el ultimo pool ya fue terminado.
     * @param requestId la solicitud que se está evaluando
     * @return true si ya fue completamente autorizado, false si no.
     */
    private boolean isLastPoolFinished(
            IWorkflowBaseDAO holder,
            Long requestId,
            String surveyRequestMode,
            String conditionalValidatorCache,
            ILoggedUser loggedUser
    ) {
        //TODO: this is a complex method 12/10
        IWorkflowRequest request = getRequestEntityFromId(
                holder.getWorkflowSettings().getEntityClazz(),
                requestId,
                surveyRequestMode,
                conditionalValidatorCache
        );
        AutorizationPool authPool = request.getAutorizationPool();
        if (authPool == null) {
            return true;
        }
        boolean isFullyAprovedLastIndex = false;
        List<AutorizationPoolDetails> authPDetails = new ArrayList<>(authPool.getAutorizationPoolDetailsList());
        List<WorkflowPool> flujoPoolItems = getFlujoPooItems(holder, requestId);
        WorkflowPool fp;
        if (authPDetails.isEmpty()) {
            return true;
        }

        //La función devuelve el último índice activo
        Integer lastActiveIndex = (Integer) getNextAuthorizationPoolData(holder, request, loggedUser).get("lastActiveIndex");

        if(flujoPoolItems.size() < lastActiveIndex) {
            return false;
        }
        
        fp = flujoPoolItems.get(lastActiveIndex - 1);
        if (fp.getType().equals(WorkflowPool.OR_TYPE)) {
            for (AutorizationPoolDetails authPDetail : authPDetails) {
                if (authPDetail.getIndice().equals(lastActiveIndex)
                        && (AutorizationPoolDetails.ACCEPTED.equals(authPDetail.getAccepted())
                          || AutorizationPoolDetails.SKIPPED.equals(authPDetail.getAccepted()))) {
                    isFullyAprovedLastIndex = true;
                    break;
                }
            }
        } else {
            //AND type
            isFullyAprovedLastIndex = true;
            for (AutorizationPoolDetails authPDetail : authPDetails) {
                if (authPDetail.getIndice().equals(lastActiveIndex)) {
                    isFullyAprovedLastIndex = isFullyAprovedLastIndex &&
                      (   AutorizationPoolDetails.ACCEPTED.equals(authPDetail.getAccepted()) 
                       || AutorizationPoolDetails.SKIPPED.equals(authPDetail.getAccepted()));
                }
            }
        }
        //Para los tipo OR, se cierra totalmente el nextIndex activo
        if (isFullyAprovedLastIndex) {
            String query = " "
                    + " SELECT c.id"
                    + " FROM " + AutorizationPoolDetails.class.getCanonicalName() + " c"
                    + " WHERE"
                    + " accepted IS NULL"
                    + " AND status = " + AutorizationPoolDetails.STATUS.ACTIVE.getValue()
                    + " AND "  + getEntityId(holder) + " = :" + getEntityId(holder) + " ";
            final List<Long> authPoolDetailsIds = HQL_findByQuery(query, getEntityId(holder), requestId);
            if (authPoolDetailsIds != null && !authPoolDetailsIds.isEmpty()) {
                HQL_updateByQuery(" "
                    + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName()
                    + " SET accepted = " + AutorizationPoolDetails.NOT_ALTERED + " "
                    + " WHERE id IN (:ids)",
                        "ids",
                        authPoolDetailsIds
                );
            } else {
                getLogger().warn("next index not found for requestId: {}", requestId);
            }
        }
        getLogger().trace("isFullyAprovedLastIndex: {}", isFullyAprovedLastIndex);
        return isFullyAprovedLastIndex;
    }

    /**
     * Función que indica si la solicitud fue automáticamente autorizada
     * @return Si se hizo la autorización automáticamente regresa el ID del usuario que lo hizo, 0L si no.
     */
    private boolean isAutomaticallyAuthorized(IWorkflowBaseDAO holder, IWorkflowRequest request,             WorkflowAuthRole authRole, ILoggedUser loggedUser, Integer currentIndex) {
        Long requestId = request.getId();
        String query = " "
                + " SELECT detail"
                + " FROM "
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + " WHERE "
                + " detail."
                + getEntityId(holder) + " = :" + getEntityId(holder) + " "
                + " AND detail.accepted IS NULL";
        List<AutorizationPoolDetails> details = HQL_findByQuery(query, getEntityId(holder), requestId);
        if (details.isEmpty()) {
            throw new RuntimeException("Invalid authorization pool.");
        }
        /*
          Si todos los usuarios del pool del indice actual
          estan inactivos se brinca este pool.
         */
        if (!isAuthorizationAvailable(holder, request, details, currentIndex)) {
            if(request.getType().equals(Request.FILL)) { // Cuando es por formulario, 1 auttorization pool detail corresponde a una seccion, por lo que solo se debe de afectar a esa.
                details = details.stream().filter(detail -> Objects.equals(detail.getIndice(), currentIndex)).collect(Collectors.toList());
            }
            return skipAuthorization(holder, request, details, loggedUser);
        } else if(request.getType().equals(Request.FILL)) { // Si el request es llenado de formulario, no aplica autorizaciones automáticas. Ya que esto saltaría el llenado de una sección si es que el usuario solicitante también está en el flujo del formulario.
            return false;
        }
        AutomaticAuthorizationDTO validation;
        /*
          Autorización automatica en caso de que las posiciones
          restantes (sin autorizarse) correspondan a un usuario que
          ya dio su VoBo.
          */
        for(AutorizationPoolDetails detail : details) {
            validation = getAutomaticAuthorizationValidator(holder, detail);
            if(!validation.isAutomaticAuthorization()) {
                continue;
            }
            switch(OwnerUtil.getType(validation.getOwnerType())) {
                case POSITION:
                    return acceptAuthorizationByPosition(holder, request, detail, validation.getUser(), loggedUser);
                case USER_TO_BE_DEFINED:
                case USER:
                case BOSS:
                case REQUESTOR:
                    return acceptAuthorizationByUser(holder, request, authRole, detail, validation.getUser(), loggedUser);
            }
        }
        return false;
    }
    
    /**
     * Función que guarda la autorización automática por puesto.
     * @param detail         el bloque de autorización
     * @return  true si fue autorizado y false si no.
     */  
    private boolean acceptAuthorizationByPosition(IWorkflowBaseDAO holder, IWorkflowRequest request, IAutorizationPoolDetails detail, UserPositionDTO user, ILoggedUser loggedUser) {
        //Recorre a todos los autorizadores siguientes
        //Revisa si el verificador puede hacer aprobación automática por usuario
        // public boolean positionAutomaticAuthorization(IWorkflowBaseDAO holder, IWorkflowRequest request, AutorizationPoolDetails apd, String positionDescription, Long userId, ILoggedUser loggedUser) {
        if(getAspectJAutoProxy().positionAutomaticAuthorization(holder, request, detail, user.getPositionName(), user.getUserId(), loggedUser)) {
            final  Map<String, Object> params = new HashMap<>();
            params.put(getEntityId(holder), holder.getEntityIdFromAutorizationPoolDetails(detail));
            params.put("indice", detail.getIndice());
            params.put("id", detail.getId());
            //se marcan cancelados los demas usuarios en la misma posicion de la autorizacion
            StringBuilder query = new StringBuilder(150);
            query.append(" "
                + " UPDATE ").append(AutorizationPoolDetails.class.getCanonicalName()).append(" "
                + " SET accepted = ").append(AutorizationPoolDetails.NOT_ALTERED).append(" "
                + " WHERE"
                    + " ").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)).append(" "
                    + " AND indice = :indice"
                    + " AND id != :id");
            HQL_updateByQuery(query.toString(), params);
            params.put("comment", detail.getDescription());
            //se marcan saltado los demas usuarios anteriores al flujo que se autorizó automaticamente
            query = new StringBuilder(100);
            query.append(" "
                + " UPDATE ").append(AutorizationPoolDetails.class.getCanonicalName()).append(" "
                + " SET"
                    + " accepted = ").append(AutorizationPoolDetails.SKIPPED ).append(" "
                    + ", description = :comment"
                + " WHERE ").append(" "
                    + " ").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)).append(" "
                    + " AND accepted IS NULL "
                    + " AND indice < :indice"
                    + " AND id != :id");
            HQL_updateByQuery(query.toString(), params);
            return true;
        }
        return false;
    }

    @Override
    @OnAutomaticPositionAuthorization
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean positionAutomaticAuthorization(
        IWorkflowBaseDAO holder, IWorkflowRequest request, IAutorizationPoolDetails apd, String positionDescription, Long userId, ILoggedUser loggedUser
    ) {
        apd.setAccepted(AutorizationPoolDetails.ACCEPTED);
        apd.setFinishDate(new Date());
        apd.setDescription("Autorización Automática del sistema por el puesto " + positionDescription);
        apd.setUserId(userId);
        apd = this.makePersistent(apd, loggedUser.getId());
        this.refreshWorkflowPreview(holder, request.getId());
        return apd != null;
    }
    
    @Override
    @OnAutomaticUserAuthorization
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean workflowRequestAutomaticApprove(
        IWorkflowBaseDAO holder, IWorkflowRequest request, WorkflowAuthRole authRole, IAutorizationPoolDetails detail, UserRef user, ILoggedUser loggedUser
    ) {
        String comment = "Autorización automática del sistema por " + user.getDescription();
        detail.setAccepted(AutorizationPoolDetails.ACCEPTED);
        detail.setFinishDate(new Date());
        detail.setUserId(user.getId());
        detail.setUser(user);
        detail.setDescription(comment);
        if (makePersistent(detail, loggedUser.getId()) != null) {
            //se marcan cancelados los demas usuarios en la misma posicion de la autorizacion
            final Map<String, Object> params = new HashMap<>();
            params.put(getEntityId(holder), holder.getEntityIdFromAutorizationPoolDetails(detail));
            params.put("indice", detail.getIndice());
            params.put("poolId", detail.getId());
            StringBuilder query = new StringBuilder(100);
            query.append(" "
                + " UPDATE ").append(AutorizationPoolDetails.class.getCanonicalName()).append(" "
                + " SET accepted = ").append(AutorizationPoolDetails.NOT_ALTERED).append(" "
                + " WHERE ").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)).append(" "
                + " AND indice = :indice"
                + " AND id != :poolId");
            HQL_updateByQuery(query.toString(), params);
            params.put("comment", comment);
            //se marcan saltado los demas usuarios anteriores al flujo que se autorizó automaticamente
            query = new StringBuilder(100);
            query.append(" "
                + " UPDATE ").append(AutorizationPoolDetails.class.getCanonicalName()).append(" "
                + " SET "
                    + " accepted = ").append(AutorizationPoolDetails.SKIPPED).append(" "
                    + ", description = :comment"
                + " WHERE ").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)).append(" "
                    + " AND accepted IS NULL "
                    + " AND indice < :indice"
                    + " AND id != :poolId");
            HQL_updateByQuery(query.toString(), params);
            return true;
        }
        return false;
    }


    /**
     * Función que guarda la autorización automática por usuario.
     * @return  true si fue autorizado y false si no.
     */
    private boolean skipAuthorization(
            IWorkflowBaseDAO holder,
            IWorkflowRequest request,
            List<AutorizationPoolDetails> details,
            ILoggedUser loggedUser
    ) {
        getLogger().info("Automatic approval req:{} ", request);
        return getAspectJAutoProxy().skipAuthorization(
                holder,
                request.getId(),
                request.getSurveyId(),
                request.getConditionalValidatorCacheId(),
                request.getType(),
                details,
                SurveyRequestMode.parse(request.getSurveyRequestMode()),
                loggedUser
        );
    }

    
    /**
     * Filter authorization pool details by index
     *
     * @param authDetails Set con los authorization pool details
     * @param index Index to filter
     * @return Filtered authorizations pool details
     */
    private List<AutorizationPoolDetails> filterAuthorizationDetailsByIndex(
            Set<AutorizationPoolDetails> authDetails, Integer index) {
        List<AutorizationPoolDetails> apds = new ArrayList<>();
        for (AutorizationPoolDetails authDetail : authDetails) {
            if (Objects.equals(index, authDetail.getIndice())) {
                apds.add(authDetail);
            }
        }
        return apds;
    }

    /**
     * Obtiene los datos del siguiente pool de autorización
     *
     * @param authDetails Set con los authorization pool details
     * @return El siguiente indice a autorizar
     */
    private Integer getNextIndexToAuthorize(Set<AutorizationPoolDetails> authDetails) {
        int nextIndex = 1;
        //Obtiene el siguiente indice valido
        for (AutorizationPoolDetails authDetail : authDetails) {
            if (authDetail.getStatus() == 0 && (authDetail.getAccepted() == null || authDetail.getAccepted() == -1)) {
                break;
            }
            nextIndex = authDetail.getIndice() + 1;
        }
        return nextIndex;
    }

    @Override
    @OnFillFormRecurrence
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest createNextFillRecurrence(
        IWorkflowRequest request, AutorizationPoolDetails apd, ILoggedUser loggedUser
    ) {
        HQL_updateByQuery(" "
                + " UPDATE  " + AutorizationPoolDetails.class.getCanonicalName() + " c"
                + " SET c.status = " + AutorizationPoolDetails.STATUS.ACTIVE.getValue()
                + " WHERE c.id = :id", "id", apd.getId());
        return request;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest createNextPoolDetails(
            IWorkflowBaseDAO holder,
            IWorkflowRequest request,
            WorkflowPool workPool,
            ILoggedUser loggedUser
    ) {
        AutorizationPool pool = request.getAutorizationPool();
        if (pool == null) {
            getLogger().error("Invalid createNextPool call, the Request entity needs an AutorizationPool object to be set {}", request);
            return null;
        }

        Set<AutorizationPoolDetails> details = new HashSet<>();
        for (final Owner owner : workPool.getOwners()) {
            final AutorizationPoolDetails detail = holder.getAutorizationPoolDetailsInstance(request, workPool, owner, pool);
            details.add(
                    makePersistent(detail, loggedUser.getId())
            );
        }
        pool.setAutorizationPoolDetailsList(details);

        request.setAutorizationPool(
                makePersistent(pool, loggedUser.getId())
        );
        if (request.getAutorizationPool() == null) {
            throw new ExplicitRollback("Unable to create autorizationPool object to be set " + request);
        } else {
            request.setAutorizationPoolId(request.getAutorizationPool().getId());
        }
        final IWorkflowRequest savedRequest = makePersistent(request, loggedUser.getId());
        updateRequestAuthorizatorNames(holder, savedRequest.getId());
        return savedRequest;
    }    
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public SaveHandle workflowRequestStart(
            IWorkflowBaseDAO holder,
            IWorkflowRequest request, 
            Long userId, 
            Boolean skipAutomaticApproval, 
            WorkflowAuthRole authRole,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        final Long requestId = request.getId();
        final SaveHandle<IWorkflowRequest> respuesta = SaveHandle.getSuccessInstance(request);
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(" "
                + "verifyRequest: ["
                    + "requestId=" + request.getId() + ", userId=" + userId + ", skipAutomaticApproval=" + skipAutomaticApproval + ", loggedUserId=" + loggedUser.getId() 
                + "]"
            );
        } else if (getLogger().isDebugEnabled()) {
            getLogger().info("verifyRequest:{}",requestId);        
        }
        if (!holder.isVerifyRequestValid(request, respuesta, loggedUser)) {
            respuesta.getSaveHandle().setOperationEstatus(0);
            respuesta.setLogicResponse(RequestAuthorizeResponse.VERIFY_REPEATED_DATA.getValue());
            return respuesta;
        }
        
        IWorkflowRequest oRequest = getEntityFromClone(holder.getWorkflowSettings(), request);
        
        holder.onStartWorkflowValidation(oRequest, request, userId, loggedUser);
            
        if (request.getBusinessUnitDepartment() != null
                && !request.getBusinessUnitDepartment().equals(oRequest.getBusinessUnitDepartment())) {
            // Éste código hace la intercepción para el cambio de departamento y por tanto cambio de verificador
            getLogger().debug("Cambio de departamento");
            // request.getDepartment() llega con el puro Id, por lo tanto, es necesario refrescarlo
            request = getRequestEntityFromId(
                    holder.getWorkflowSettings().getEntityClazz(),
                    request.getId(),
                    request.getSurveyRequestMode(),
                    request.getConditionalValidatorCacheId()
            );
            holder.onBusinessUnitDepartmentChange(request, userId, loggedUser); 
            respuesta.setLogicResponse(RequestAuthorizeResponse.VERIFY_DEPARTMENT_MANAGER.getValue());
            return respuesta;
        }

        this.makePersistent(request, loggedUser.getId());
        this.refreshWorkflowPreview(holder, request.getId());
        this.updateRequestAuthorizatorNames(holder, request.getId());
        // Ahora revisa si debe de proseguirse al siguiente estado la solicitud
        if (holder.isStartWorkflowAvailable(request, authRole, loggedUser)) {
            /*
              Dentro de `isStartWorkflowAvailable` se debió iniciar el flujo con `executeWorkflow`:
               1) Por defecto, ejecutará el metodo solo si hay u `workflowId` disponible
               2) Los mails se envían desde el monitor de `OnCreatedNextFill` en `FormPendingMonitor.java`
               3) Los pendientes se envían desde el monitor de `OnCreatedNextFill` en `FormMailMonitor.java`

             */
            respuesta.setLogicResponse(RequestAuthorizeResponse.VERIFY_APPROVED.getValue());
        }
        if (holder.isRequestEndedImmediately(request, loggedUser)) {
            /*
              Dentro de `isRequestEndedImmediately` se debió llamar a `onWorkflowEnd` y enviar el mail desde ahí.
             */
            respuesta.setLogicResponse(RequestAuthorizeResponse.VERIFY_FINAL.getValue());
        }
        return respuesta;
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GenericSaveHandle workflowRequestApproveForceCreateNextPool(
            IWorkflowBaseDAO holder,
            IWorkflowRequest request,
            WorkflowAuthRole authRole,
            String comment,
            Long nextUserId,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        boolean skipCheck = true;
        return workflowRequestApprove(holder, request, authRole, comment, nextUserId, loggedUser, skipCheck, null, null, null);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GenericSaveHandle workflowRequestApprove(
            IWorkflowBaseDAO holder,
            IWorkflowRequest request,
            WorkflowAuthRole authRole,
            String comment,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        Long userId = loggedUser.getId();
        boolean skipCheck = false;
        return workflowRequestApprove(holder, request, authRole, comment, userId, loggedUser, skipCheck, null, null, null);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GenericSaveHandle workflowRequestApprove(
            final IWorkflowBaseDAO holder,
            final IWorkflowRequest request,
            final WorkflowAuthRole authRole,
            final String comment,
            final Long userId,
            final ILoggedUser loggedUser,
            final boolean skipCheck,
            final Long outstandingSurveyId,
            final Long surveyFieldObjId,
            final Long dataFilledAutorizationPoolIndex
    ) throws IOException, QMSException {
        GenericSaveHandle result = new GenericSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("approveAutorization: [ent={}, authRole={}, comment={}, userId={}, loggedUser={}]",
                    request,
                    authRole,
                    comment,
                    userId,
                    loggedUser
            );
        }
        StringBuilder hqlBuilder = new StringBuilder(150);
        Integer status = HQL_findSimpleInteger(" "
                + " SELECT c.status"
                + " FROM " + getEntityClazz(holder).getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", request.getId()
        );
        if (WorkflowRequestStatus.RETURNED.getValue().equals(status)) {
            request.setStatus(WorkflowRequestStatus.APROVING.getValue());
            HQL_updateByQuery(" "
                + " UPDATE " + getEntityClazz(holder).getCanonicalName() + " c "
                + " SET c.status = :status "
                + " WHERE c.id = :id", ImmutableMap.of(
                        "id", request.getId(),
                        "status", WorkflowRequestStatus.APROVING.getValue()
                )
            );
        }
        final Map<String, Object> params = new HashMap<>();
        params.put(getEntityId(holder), request.getId());
        if (WorkflowAuthRole.ASSIGNED.equals(authRole) || WorkflowAuthRole.REQUESTOR.equals(authRole)) {
            params.put("userId", userId);
        }
        hqlBuilder.append(" "
            + " SELECT"
                + " new ").append(AutorizationPoolDetailsDTO.class.getCanonicalName()).append(" (c.id, wp.ownerId) "
            + " FROM "
                + " ").append(getWorkflowPreviewClazz(holder).getCanonicalName()).append(" wp "
                + ",").append(AutorizationPoolDetails.class.getCanonicalName()).append(" c "
            + " JOIN ").append(getEntityClazz(holder).getCanonicalName()).append(" request ON request.id = c.").append(getEntityId(holder)).append(" "
            + " WHERE"
                + " c.accepted IS NULL"
                + " AND ("
                    + " c.status = ").append(AutorizationPoolDetails.ACCEPTED).append(" "
                    + " OR c.status = ").append(AutorizationPoolDetails.SKIPPED).append(" "
                + " )"
                + " AND request.id = wp.").append(getEntityId(holder)).append(" "
                + " AND c.indice = wp.workflowIndex "
                + " AND request.id = :").append(getEntityId(holder))
                    .append(
                        WorkflowAuthRole.ADMIN.equals(authRole) ? Utilities.EMPTY_STRING : " "
                        + " AND wp.userId = :userId "
                    )
                    .append(
                        dataFilledAutorizationPoolIndex == null ? Utilities.EMPTY_STRING : " "
                        + " AND c.indice = :dataFilledAutorizationPoolIndex"
                    )
            .append(" ORDER BY c.indice")
        ;
        if (dataFilledAutorizationPoolIndex != null) {
            params.put("dataFilledAutorizationPoolIndex", dataFilledAutorizationPoolIndex.intValue());
        }
        List<AutorizationPoolDetailsDTO> details = HQL_findByQuery(hqlBuilder.toString(), params);
        Map<Long, AutorizationPoolDetailsDTO> detailIndex = new HashMap<>();
        for (AutorizationPoolDetailsDTO detail : details) {
            detailIndex.put(detail.getId(),detail);
        }
        Date now = Utilities.getNow();
        AutorizationPool autorizationPool  = request.getAutorizationPool();
        if (autorizationPool == null) {
            hqlBuilder = new StringBuilder(150);
            hqlBuilder.append(" "
                + " SELECT c from ").append(AutorizationPool.class.getCanonicalName()).append(" c "
                + " WHERE c.").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)
            );
            autorizationPool = (AutorizationPool) HQL_findSimpleObject(hqlBuilder.toString(), getEntityId(holder), request.getId());
        }
        for (AutorizationPoolDetails detail : autorizationPool.getAutorizationPoolDetailsList()) {
            AutorizationPoolDetailsDTO dto = detailIndex.get(detail.getId());
            if (dto == null) {
                continue;
            }
            detail.setFinishDate(now);
            detail.setDescription(comment);
            detail.setAccepted(AutorizationPoolDetails.ACCEPTED);
            switch(OwnerUtil.getTypeConstant(dto.getOwnerId(), this)) {
                case BOSS:
                case REQUESTOR:
                case USER:
                case USER_TO_BE_DEFINED:
                    //No behaviour defined yet
                    break;
                case POSITION:
                    detail.setPositionId(
                        OwnerUtil.getPositionId(dto.getOwnerId(), userId, this)
                    );
                    break;
            }
            detail.setUserId(userId);
            detail.setSurveyFieldObjId(surveyFieldObjId);
            detail.setOutstandingSurveyId(outstandingSurveyId);
            makePersistent(detail, loggedUser.getId());
        }
        workflowNextOrEnd(skipCheck, holder, request, authRole, loggedUser);
        
        if (HQL_findSimpleLong(" "
                + " SELECT c.id"
                + " FROM " + getEntityClazz(holder).getCanonicalName() + " c"
                + " WHERE"
                    + " c.status = " + WorkflowRequestStatus.CLOSED.intValue()
                    + " AND c.id = :requestId",
                "requestId", request.getId()
            ) > 0) {
            result.getJsonEntityData().put("published", true);
            result.getJsonEntityData().put("requestClosed", true);
            if (getEntityClazz(holder).equals(Request.class)) {
                result.getJsonEntityData().put("documentId", HQL_findSimpleLong(" "
                    + " SELECT d.id"
                    + " FROM " + Request.class.getCanonicalName() + " c"
                    + " JOIN c.document d"
                    + " WHERE c.id = :requestId ", "requestId",  request.getId()
                ));
            }
        } else {
            result.getJsonEntityData().put("published", false);
            result.getJsonEntityData().put("requestClosed", false);
            
        }
        result.getJsonEntityData().put("authorized", true);
        return result;
    }
    
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public boolean workflowRequestRejectAndRestart(
        IWorkflowBaseDAO holder, IWorkflowRequest request, WorkflowAuthRole authRole, String comment, ILoggedUser loggedUser
    ) {
        return workflowRequestReject(holder, request, authRole, comment, true, loggedUser);
    }
    
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public boolean workflowRequestReject(
        IWorkflowBaseDAO holder, IWorkflowRequest request, WorkflowAuthRole authRole, String comment, ILoggedUser loggedUser
    ) {
        return workflowRequestReject(holder, request, authRole, comment, false, loggedUser);
    }
    
    
    private boolean workflowRequestReject(
            IWorkflowBaseDAO holder, 
            IWorkflowRequest request, 
            WorkflowAuthRole authRole,
            String comment, 
            boolean restartWorkflow, 
            ILoggedUser loggedUser
    ) {
        final Long requestId = request.getId();
        
        holder.setRequestOnWorkflowReject(request, loggedUser);
        
        request.setStatus(WorkflowRequestStatus.CANCELED.getValue());
        request.setRejectionDate(new Date());
        
        
        if (makePersistent(request, loggedUser.getId()) == null) {
            return false;
        }
        this.refreshWorkflowPreview(holder, requestId);
        
        Integer accepted = AutorizationPoolDetails.REJECTED;
        if (restartWorkflow) {
            // Cuando se rechaza un formulario se deja en NULL debido a que la sección se llenará de nuevo
            accepted = AutorizationPoolDetails.TO_ACCEPT; // <-- La constante `TO_ACCEPT` vale NULL
        }
        if (
            HQL_updateByQuery(" "
                + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c"
                + " SET"
                    + " c.accepted = " + accepted + "," // <-- Vale NULL!, no parametrizar en el MAP
                    + " c.modificationDate = current_date(),"
                    + " c.finishDate = current_date(),"
                    + " c.description = :comment,"
                    + " c.userId = :cancellerId"
                + " WHERE"
                    + " c.accepted IS NULL"
                    + " AND c." + getEntityId(holder) + " = :requestId "
                    + " AND ("
                        + " c.userId = :cancellerId "
                        //solicitante
                        + " OR exists ("
                            + " SELECT o.id"
                            + " FROM c.owner o"
                            + " WHERE "
                                + " c." + getEntityId(holder) + " IN ("
                                    + " SELECT r.id"
                                    + " FROM " + getEntityClazz(holder).getCanonicalName() + " r "
                                    + " WHERE "
                                        + " r.createdBy = :cancellerId "
                                        + " AND r.id = :requestId "
                                + " )"
                                + " AND o.type = " + Owner.TYPE.REQUESTOR.getValue()
                        + " )"
                        //usuario
                        + " OR exists ("
                            + " SELECT o.id"
                            + " FROM c.owner o"
                            + " JOIN o.users us  "
                            + " JOIN " + User.class.getCanonicalName() + " u"
                            + " ON u.id = us.userId"
                            + " WHERE "
                                + " u.id = :cancellerId "
                                + " AND o.type = " + Owner.TYPE.USER.getValue()
                        + " )"
                        //puesto
                        + " OR exists ("
                            + " SELECT o.id"
                            + " FROM c.owner o "
                            + " JOIN o.positions ps "
                            + " JOIN " + Position.class.getCanonicalName() + " p"
                            + " ON o.id = ps.positionId"
                            + " WHERE "
                                + " o.type = " + Owner.TYPE.POSITION.getValue()
                                + " AND p.id IN ("
                                    + " SELECT pu.id "
                                    + " FROM " + User.class.getCanonicalName() + " user "
                                    + " JOIN user.puestos pu"
                                    + " WHERE user.id = :cancellerId "
                                + " )"
                        + " )"
                    + " ) ", 
                    ImmutableMap.of(
                        /* No parametrizar `accepted` */
                        "comment", comment,
                        "requestId", requestId,
                        "cancellerId", loggedUser.getId()
                    )
        ) == 0) {
            return false;
        }
        if (
            HQL_updateByQuery(" "
                + " UPDATE " + AutorizationPool.class.getCanonicalName() + " c"
                + " SET c.status = " + AutorizationPool.STATUS_CANCELLED
                + " WHERE c." + getEntityId(holder) + " = :requestId", 
                "requestId", requestId
            ) == 0
        ) {
            return false;
        }
        // Este update solo se aplica cuando hay otras posiciones del flujo por autorizar
        HQL_updateByQuery(" "
            + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c "
            + " SET "
                + " c.finishDate = current_date()  " 
                + ", c.accepted = " + AutorizationPoolDetails.NOT_ALTERED /* No parametrizar `accepted` */
            + " WHERE"
                + " c.accepted IS NULL"
                + " AND c.status = " + AutorizationPoolDetails.STATUS.ACTIVE.getValue()
                + " AND c." + getEntityId(holder) + " = :requestId",
                    "requestId", requestId
        );
        holder.setRequestReleasedOnWorkflowReject(requestId, authRole, loggedUser);
        return true;
    }    
    
    /**
     * Función que crea el siguiente pool de autorización.
     * @param loggedUser el usuario que está autenticado
     * @return true si crea un nuevo, false si no.
     */
    private PoolCreatedDTO createNextPool(
            IWorkflowBaseDAO holder,
            IWorkflowRequest entity,
            ILoggedUser loggedUser
    ) {
        getLogger().trace("creating next pool");
        PoolCreatedDTO poolCreatedDTO = new PoolCreatedDTO();
        entity = getRequestEntityFromId(
                holder.getWorkflowSettings().getEntityClazz(),
                entity.getId(),
                entity.getSurveyRequestMode(),
                entity.getConditionalValidatorCacheId()
        );
        Map nextAPD = getNextAuthorizationPoolData(holder, entity, loggedUser);
        Integer nextIndex = (Integer) nextAPD.get("nextIndex");
        Integer lastActiveIndex = (Integer) nextAPD.get("lastActiveIndex");
        poolCreatedDTO.setNextAPD(nextIndex);
        poolCreatedDTO.setCurrentAPD(lastActiveIndex);
        if (nextAPD.get("status").equals("recurrence")) {
            List<AutorizationPoolDetails> apds = (List<AutorizationPoolDetails>) nextAPD.get("recurrence");
            for (AutorizationPoolDetails apd : apds) {
                if (holder.isRecurrenceEnabled(entity)) {
                    getAspectJAutoProxy().createNextFillRecurrence(entity, apd, loggedUser);
                } else {
                    getLogger().error("Invalid call to recurrence without outstandingSurveysId with request {}", entity);
                }
            }
            poolCreatedDTO.setStatus(true);
            return poolCreatedDTO;
        }
        Long workflowPoolId = null;
        if (entity.getWorkflowId() != null) {
            List<WorkflowPool> workflowPools = getFlujoPooItems(holder, entity.getId());
            if (workflowPools.size() < nextIndex) {
                getLogger().info("All authorizations for request {} are done", entity.getId());
                poolCreatedDTO.setStatus(false);
                return poolCreatedDTO;
            }
            WorkflowPool currentPool = workflowPools.get(nextIndex - 1);
            if(currentPool != null) {
                workflowPoolId = currentPool.getId();
            }
        }
        poolCreatedDTO.setStatus(createNextPoolDetails(holder, entity, loggedUser, workflowPoolId));
        return poolCreatedDTO;
    }
    
    private List<WorkflowPool> getFlujoPooItems(IWorkflowBaseDAO holder, final Long entityId) {
        String query = " "
                + " SELECT f.flujoPoolList"
                + " FROM "
                + getEntityClazz(holder).getCanonicalName() + " c "
                + " JOIN "
                + WorkflowSimple.class.getCanonicalName() + " f ON f.id = c.workflowId "
                + " WHERE c.id = :"
                + getEntityId(holder);
        return HQL_findByQuery(query, getEntityId(holder), entityId);
    }

    private boolean createNextPoolDetails(IWorkflowBaseDAO holder, IWorkflowRequest entity, ILoggedUser loggedUser, Long workflowPoolId) {
        WorkflowPool currentPool = null;
        if(workflowPoolId != null) {
            currentPool = HQLT_findById(WorkflowPool.class, workflowPoolId);
        }
        if (currentPool != null) {
            if (holder.isRecurrenceEnabled(entity)) {
                Set<Owner> owners = currentPool.getOwners();
                if (owners.size() == 1 && currentPool.getIndex() == 1) {
                    return holder.requestFillForm(holder, entity, loggedUser, currentPool) != null;
                } else {
                    return holder.createNextFill(holder, entity, loggedUser, currentPool) != null;
                }
            }
        }
        return holder.onCreateNextPoolDetails(entity, currentPool, loggedUser);
    }
    
    private boolean acceptAuthorizationByUser(IWorkflowBaseDAO holder, IWorkflowRequest request, WorkflowAuthRole authRole, AutorizationPoolDetails detail, UserPositionDTO authorizer, ILoggedUser loggedUser) {
        return getAspectJAutoProxy().workflowRequestAutomaticApprove(holder, request, authRole, detail, new UserRef(authorizer.getUserId(), authorizer.getUserName()), loggedUser);
    }

    /**
     * Obtiene al autorizador encargado del pool detail
     */
    private AutomaticAuthorizationDTO getAutomaticAuthorizationUser(IWorkflowBaseDAO holder, AutorizationPoolDetails detail) {
        Long requestId = holder.getEntityIdFromAutorizationPoolDetails(detail);
        List<AutomaticAuthorizationDTO> users;
        final Map<String, Object> params = new HashMap<>();
        params.put(getEntityId(holder), requestId);
        params.put("indice", detail.getIndice().longValue());
        String query = " "
                + " SELECT new " + AutomaticAuthorizationDTO.class.getCanonicalName() + "("
                    + "u.id as userId"
                    + ", u.description as userName"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " u "
                + "," + getWorkflowPreviewClazz(holder).getCanonicalName() + " wp "
                + " WHERE wp.userId = u.id "
                + " AND wp." + getEntityId(holder) + " = :" + getEntityId(holder) + " "
                + " AND wp.workflowIndex = :indice "
                + " GROUP BY u.id, u.description";
        users = HQL_findByQueryLimit(query, params, 1);
        if(!users.isEmpty()) {
            return users.get(0);
        } else {
            return null;
        }
    }
    
    private AutomaticAuthorizationDTO getAutomaticAuthorizationValidator(IWorkflowBaseDAO holder, AutorizationPoolDetails detail) {
        Long requestId = holder.getEntityIdFromAutorizationPoolDetails(detail);
        List<AutomaticAuthorizationDTO> users;
        
        final Map<String, Object> params = new HashMap<>();
        params.put(getEntityId(holder), requestId);
        params.put("indice", detail.getIndice().longValue());
        //Es el verificador
        StringBuilder query = new StringBuilder(150);
        if (getEntityVerificationClazz(holder) != null) {
            query.append(" "
                + " SELECT"
                    + " new ").append(AutomaticAuthorizationDTO.class.getCanonicalName()).append("("
                        + "u.id as userId"
                        + ", u.description as userName"
                    + " )"
                + " FROM " 
                    + " ").append(User.class.getCanonicalName()).append(" u "
                    + ",").append(getEntityVerificationClazz(holder).getCanonicalName()).append(" v "
                    + ",").append(getWorkflowPreviewClazz(holder).getCanonicalName()).append(" wp "
                + " JOIN v.verificator vu "
                + " WHERE "
                    + " vu.id = u.id "
                    + " AND v.").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)).append(" "
                    + " AND wp.").append(getEntityId(holder)).append(" = v.").append(getEntityId(holder)).append(" "
                    + " AND wp.workflowIndex = :indice "
                    + " AND wp.userId = u.id "
                + " GROUP BY u.id, u.description");
            users = HQL_findByQueryLimit(query.toString(), params, 1);
            if(!users.isEmpty()) {
                return users.get(0);
            }
        }
        // Previos autorizadores en el flujo
        query = new StringBuilder(150);
        query.append(" "
            + " SELECT"
                + " new ").append(AutomaticAuthorizationDTO.class.getCanonicalName()).append("("
                    + "u.id as userId"
                    + ", u.description as userName"
                    + ", position.id as positionId"
                    + ", position.description as positionName"
                    + ", owner.type as ownerType"
                + " )"
            + " FROM ").append(" "
                + " ").append(User.class.getCanonicalName()).append(" u "
                + ",").append(AutorizationPoolDetails.class.getCanonicalName()).append(" detail"
                + ",").append(getWorkflowPreviewClazz(holder).getCanonicalName()).append(" wp "
                + ",").append(Owner.class.getCanonicalName()).append(" owner "
            + " LEFT JOIN u.puestos position"
            + " WHERE "
                + " detail.").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)).append(" "
                + " AND owner.id = wp.ownerId "
                + " AND detail.accepted = ").append(AutorizationPoolDetails.ACCEPTED).append(" "
                + " AND detail.userId IS NOT NULL "
                + " AND ("
                    + " wp.").append(getEntityId(holder)).append(" = detail.").append(getEntityId(holder)).append(" "
                    + " AND wp.workflowIndex = :indice "
                    + " AND wp.userId = u.id "
                + " ) "
                + " AND ("
                    + " detail.userId = u.id"
                    + " OR "
                    + " detail.positionId IN ( "
                        + " SELECT p.id"
                        + " FROM u.puestos p"
                    + " ) "
                + " ) "
            + " GROUP BY u.id, u.description, position.id, position.description, owner.type"
            + " ORDER BY max(detail.indice) ASC ");
        users = HQL_findByQueryLimit(query.toString(), params, 1);
        if(!users.isEmpty()) {
            return users.get(0);
        }
        AutomaticAuthorizationDTO invalid = new AutomaticAuthorizationDTO(); 
        invalid.setAutomaticAuthorization(false);
        return invalid;
    }

    /**
     * Función que regresa si la siguiente autorización se puede realizar.
     */
    private boolean isAuthorizationAvailable(
            IWorkflowBaseDAO holder,
            IWorkflowRequest request,
            List<AutorizationPoolDetails> details,
            Integer currentIndex
    ) {
        if (Objects.equals(request.getType(), Request.FILL)) {
            AutorizationPoolDetails currentDetail = details.stream()
                    .filter(detail -> Objects.equals(detail.getIndice(), currentIndex))
                    .findFirst()
                    .orElse(null);
            if (currentDetail != null) {
                boolean omit = omittedSection(
                        currentDetail.getIndice(),
                        request.getSurveyId(),
                        request.getOutstandingSurveysId(),
                        request.getId(),
                        SurveyRequestMode.parse(request.getSurveyRequestMode()),
                        request.getConditionalValidatorCacheId()
                );
                return !omit;
            }
        } else {
            return isAuthorizerAvailable(holder, request);
        }
        return true;
    }

    /**
     * Función regresar true cuando existe el proximo autorizante
     * 
     * @param request Request que contiene al autorizante
     * @return  true cuando el siguinte autorizante está activo
     */
    private boolean isAuthorizerAvailable(IWorkflowBaseDAO holder, IWorkflowRequest request) {
        Long requestId = request.getId();
        String query = " "
                + " SELECT count(*)"
                + " FROM "
                + " "
                + " "
                + User.class.getCanonicalName() + " user "
                + ","
                + AutorizationPoolDetails.class.getCanonicalName() + " apd "
                + ","
                + getWorkflowPreviewClazz(holder).getCanonicalName() + " wp "
                + " WHERE"
                + " user.status = "
                + User.STATUS.ACTIVE.getValue() + " "
                + " AND wp."
                + getEntityId(holder) + " = apd." + getEntityId(holder) + " "
                + " AND wp.workflowIndex = apd.indice "
                + " AND wp.userId = user.id "
                + " AND apd."
                + getEntityId(holder) + " = :" + getEntityId(holder) + " "
                + " AND apd.accepted IS NULL ";
        final Integer count = HQL_findSimpleInteger(query,getEntityId(holder), requestId);
        return !Objects.equals(count, 0);
    }

    /**
     * Función que determina si es el último flujo de autorización
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isLastAutorizationPool(IWorkflowBaseDAO holder, Long requestId) {
        getLogger().trace("DPMS.DAO.HibernateDAO_Request @ isLastAutorizationPool: [requestId=" + requestId + "]");
        StringBuilder query = new StringBuilder(100);
        query.append(" "
            + " SELECT max(workflowPool.workflowPoolIndex) "
            + " FROM ").append(getEntityClazz(holder).getCanonicalName()).append(" c "
            + " JOIN ").append(WorkflowSimple.class.getCanonicalName()).append(" f ON f.id = c.workflowId "
            + " JOIN f.flujoPoolList workflowPool"
            + " WHERE c.id = :").append(getEntityId(holder)
        );
        Integer maxIndex = HQL_findSimpleInteger(query.toString(), getEntityId(holder), requestId);

        final Map<String, Object> params = new HashMap<>();
        params.put(getEntityId(holder), requestId);
        params.put("indice", maxIndex);
        //Se verifica si el indice es el último dentro del flujo de autorizació
         query = new StringBuilder(100);
        query.append(" "
            + " SELECT count(c.id) "
            + " FROM ").append(AutorizationPoolDetails.class.getCanonicalName()).append(" c "
            + " WHERE"
                + " c.").append(getEntityId(holder)).append(" = :").append(getEntityId(holder)).append(" "
                + " AND c.").append(getEntityId(holder)).append(" IS NOT NULL"
                + " AND c.indice = :indice"
                + " AND c.status = ").append(AutorizationPoolDetails.STATUS.ACTIVE.getValue()
        );
        long count = HQL_findSimpleLong(query.toString(), params);
        getLogger().trace("[requestId={}, maxIndex={}, count={}]", requestId, maxIndex, count);
        getLogger().trace("isLastAutorizationPool: {}", count != 0L);
        return count != 0L;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean omittedSection(
            Integer authorizationPoolIndex,
            Long surveyId,
            Long outstandingSurveyId,
            Long requestId,
            SurveyRequestMode mode,
            String conditionalValidatorCacheId
    ) {
        if (authorizationPoolIndex == null || surveyId == null || outstandingSurveyId == null ||  conditionalValidatorCacheId == null) {
            return false;
        }
        final ISurveyCaptureDAO captureDao = Utilities.getBean(ISurveyCaptureDAO.class);
        final OutstandingSurveysAttendantInfo attendantsInfo = captureDao.getPoolAttendantsInfo(surveyId, outstandingSurveyId, requestId);
        if (attendantsInfo != null) {
            final List<OutstandingSurveysAttendant> fillers = attendantsInfo.getAttendants();
            final List<OutstandingSurveysAttendant> flow = captureDao.getFilteredAttendants(fillers);
            final ConditionalValidatorCache instance = ConditionalValidatorCache.getInstance();
            final ConditionalValidator conditionalValidator = instance.get(outstandingSurveyId, mode, conditionalValidatorCacheId, Module.FORMULARIE);
            final OutstandingSurveysAttendant out = flow.stream().filter(f -> Objects.equals(f.getFillAutorizationPoolIndex().intValue(), authorizationPoolIndex)).findFirst().orElse(null);
            if (out != null) {
                final Long fieldObjectId = out.getFieldObjectId();
                return conditionalValidator.isConditioned(fieldObjectId) && !conditionalValidator.isFieldShownByAnyAnswer(fieldObjectId);
            }
        }
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getNextFillFormStage(
            final Long surveyId,
            final Long outstandingSurveyId,
            final Long requestId,
            final SurveyRequestMode mode,
            final Long currentAutorizationPoolIndex,
            final String conditionalValidatorCacheId
    ) {
        final List<Map<String, Object>> stages = HQL_findByQuery(" "
                        + " SELECT new map(c.stage as stage, c.id as fieldObjectId)"
                        + " FROM " + SurveyField.class.getCanonicalName() + " d "
                        + " JOIN d.obj c "
                        + " WHERE"
                        + " c.surveyId = :surveyId"
                        + " AND d.type IN ( "
                        + "'" + SurveyField.TYPE_SECCION + "',"
                        + "'" + SurveyField.TYPE_SIGNATURE + "'"
                        + " )"
                        + " ORDER BY c.order ASC",
                ImmutableMap.of(
                        "surveyId", surveyId
                ),
                true,
                CacheRegion.SURVEY,
                0
        );
        if (stages == null || stages.isEmpty()) {
            return null;
        }
        boolean omitted;
        int nextOrder;
        if (currentAutorizationPoolIndex == null || Objects.equals(currentAutorizationPoolIndex,0L )) {
            nextOrder = 1;
        } else {
            nextOrder = currentAutorizationPoolIndex.intValue();
        }
        do {
            nextOrder += 1;
            omitted = Utilities.getBean(IWorkflowDAO.class).omittedSection(
                    nextOrder,
                    surveyId,
                    outstandingSurveyId,
                    requestId,
                    mode,
                    conditionalValidatorCacheId
            );
        } while(omitted);
        if (nextOrder > stages.size()) { // Se alcanzó la última etapa
            return null;
        }
        // Dado que las etapas estan ordenadas, se asume el indice de nextOrder corresponde a la etapa siguiente
        // Se reduce en uno para empalmar con la lista.
        Map<String, Object> stagesByIndex = stages.get(nextOrder - 1);
        if (stagesByIndex == null) {
            return null;
        }
        if (stagesByIndex.get("stage") == null) {
            return null;
        }
        return stagesByIndex.get("stage").toString();
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IWorkflowDAO.class);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void refreshWorkflowPreview(IWorkflowBaseDAO holder, Long requestId) {
        WorkflowPreviewGenerator.updateByRequestRecords(holder.getWorkflowSettings(), requestId, this);
    }
    
    private String getEntityId(IWorkflowBaseDAO holder) {
        return holder.getWorkflowSettings().getEntityId();
    }
    
    private Class<?> getEntityClazz(IWorkflowBaseDAO holder) {
        return holder.getWorkflowSettings().getEntityClazz();
    }
    
    private Class<?> getEntityVerificationClazz(IWorkflowBaseDAO holder) {
        return holder.getWorkflowSettings().getEntityVerificationClazz();
    }
    
    private Class<?> getWorkflowPreviewClazz(IWorkflowBaseDAO holder) {
        return holder.getWorkflowSettings().getWorkflowPreviewClazz();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest getRequestEntityFromId(
            Class cls,
            Long id,
            String surveyRequestMode,
            String conditionalValidatorCache
    ) {
        IWorkflowRequest request = (IWorkflowRequest) HQLT_findById(cls, id);
        // Non-persistent additional fields
        request.setSurveyRequestMode(surveyRequestMode);
        request.setConditionalValidatorCacheId(conditionalValidatorCache);
        return request;
    }
    
    private IWorkflowRequest getEntityFromClone(WorkflowSupported settings, IWorkflowRequest original) {
        return (IWorkflowRequest) HQLT_findById(settings.getEntityClazz(), original.getId());
    }
    
    private String getRequestAuthorizatorNames(WorkflowSupported settings, final Long requestId) {
        return SequenceDetailGenerator.getRequestAuthorizatorNames(settings, this, requestId);
    }
}
