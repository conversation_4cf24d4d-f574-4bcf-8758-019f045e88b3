/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.workflow.util;

import DPMS.Mapping.Request;
import DPMS.Mapping.Verification;
import Framework.Config.CompositeStandardEntity;
import java.util.regex.MatchResult;
import java.util.regex.Pattern;
import javax.persistence.Table;
import qms.form.entity.FormRequest;
import qms.workflow.entity.SequenceDetailFormRequest;
import qms.workflow.entity.WorkflowFormRequestData;
import qms.workflow.entity.WorkflowRequestData;

/**
 *
 * <AUTHOR>
 */
public enum WorkflowSupported {
    REQUEST(
        Request.class,                              // databaseTable                = request
        Verification.class,               
        WorkflowRequestData.class,           // databaseWorkflowPreviewTable = workflow_preview_data
        SequenceDetailFormRequest.class,     
        "requestId",                                   // entityId                     = requestId
        "workflowId",                           // entityWorkflowId             = workflowId
        "request_id",                                // databaseId                   = request_id
        "flujo_id",                           // databaseWorkflowId           = flujo_id
        "autor_id",                                 // createdById                  = autor_id
        "int_borrado"                              // deletedColumn                = int_borrado
    ),
    FORM_REQUEST(
        FormRequest.class,                          // databaseTable                = form_request
        null, 
        WorkflowFormRequestData.class,       // databaseWorkflowPreviewTable = workflow_form_request_data
        SequenceDetailFormRequest.class,
        "formRequestId",                               // entityId                     = formRequestId
        "workflowId",                           // entityWorkflowId             = workflowId
        "form_request_id",                           // databaseId                   = form_request_id
        "workflow_id",                        // databaseWorkflowId           = workflow_id
        "created_by",                               // createdById                  = created_by
        "deleted"                                  // deletedColumn                = deleted
    );

    private final Class<?> entityClazz;
    private final Class<?> workflowPreviewClazz;
    private final Class<? extends CompositeStandardEntity> entityVerificationClazz;
    private final Class<? extends ISequenceDetailProvider> sequenceDetailClazz;
    
    private final String entityId;
    private final String entityWorkflowId;
    private final String databaseId;
    private final String databaseWorkflowId;
    private final String createdById;
    private final String deletedColumn;

    private final String databaseWorkflowPreviewTable;
    private final String databaseTable;
    private final Pattern entityClazzPattern;
    private final Pattern workflowPreviewClazzPattern;
    private final Pattern databaseIdPattern;
    private final Pattern databaseTablePattern;
    private final Pattern databaseWorkflowPreviewTablePattern;
    private final Pattern databaseWorkflowIdPattern;
    private final Pattern createdByIdPattern;
    private final Pattern deletedColumnPattern;
    
    private WorkflowSupported(
            Class<?> entityClazz,
            Class<? extends CompositeStandardEntity> entityVerificationClazz,
            Class<?> workflowPreviewClazz,
            Class<? extends ISequenceDetailProvider> sequenceDetailClazz,
            String entityId, 
            String entityWorkflowId, 
            String databaseId,
            String databaseWorkflowId,
            String createdById,
            String deletedColumn
    ) {
        this.entityClazz = entityClazz;
        this.entityVerificationClazz = entityVerificationClazz;
        this.workflowPreviewClazz = workflowPreviewClazz;
        this.sequenceDetailClazz = sequenceDetailClazz;
        this.entityId = entityId;
        this.entityWorkflowId = entityWorkflowId;
        this.databaseId = databaseId;
        this.databaseWorkflowId = databaseWorkflowId;
        this.createdById = createdById;
        this.deletedColumn = deletedColumn;
        
        this.databaseWorkflowPreviewTable = workflowPreviewClazz.getAnnotation(Table.class).name();
        this.databaseTable = entityClazz.getAnnotation(Table.class).name();
        this.databaseIdPattern = Pattern.compile("\\$\\{databaseId\\}");
        this.entityClazzPattern = Pattern.compile("\\$\\{entityClazz\\}");
        this.workflowPreviewClazzPattern = Pattern.compile("\\$\\{workflowPreviewClazz\\}");
        this.databaseTablePattern = Pattern.compile("\\$\\{databaseTable\\}");
        this.databaseWorkflowPreviewTablePattern = Pattern.compile("\\$\\{databaseWorkflowPreviewTable\\}");
        this.databaseWorkflowIdPattern = Pattern.compile("\\$\\{databaseWorkflowId\\}");
        this.createdByIdPattern = Pattern.compile("\\$\\{createdById\\}");
        this.deletedColumnPattern = Pattern.compile("\\$\\{deletedColumn\\}");
    }

    public Class<?> getEntityClazz() {
        return entityClazz;
    }

    public Class<? extends CompositeStandardEntity> getEntityVerificationClazz() {
        return entityVerificationClazz;
    }

    public Class<?> getWorkflowPreviewClazz() {
        return workflowPreviewClazz;
    }

    public Class<? extends ISequenceDetailProvider> getSequenceDetailClazz() {
        return sequenceDetailClazz;
    }

    public String getDatabaseWorkflowPreviewTable() {
        return databaseWorkflowPreviewTable;
    }
    
    public String replaceDeletedColumn(String str) {
        return this.deletedColumnPattern.matcher(str).replaceAll(deletedColumn);
    }
    
    public String replaceCreatedById(String str) {
        return this.createdByIdPattern.matcher(str).replaceAll(createdById);
    }
    
    public String replaceEntityClazz(String str) {
        return this.entityClazzPattern.matcher(str).replaceAll(entityClazz.getCanonicalName());
    }
    
    public String replaceWorkflowPreviewClazz(String str) {
        return this.workflowPreviewClazzPattern.matcher(str).replaceAll(workflowPreviewClazz.getCanonicalName());
    }
    
    public String replaceDatabaseId(String str) {
        return this.databaseIdPattern.matcher(str).replaceAll(databaseId);
    }
    
    public String replaceDatabaseTable(String str) {
        return this.databaseTablePattern.matcher(str).replaceAll(databaseTable);
    }
    
    public String replaceDatabaseWorkflowPreviewTable(String str) {
        return this.databaseWorkflowPreviewTablePattern.matcher(str).replaceAll(databaseWorkflowPreviewTable);
    }
    
    public String replaceDatabaseWorkflowId(String str) {
        return this.databaseWorkflowIdPattern.matcher(str).replaceAll(databaseWorkflowId);
    }

    public String getEntityId() {
        return entityId;
    }

    public String getEntityWorkflowId() {
        return entityWorkflowId;
    }

    public String getDatabaseWorkflowId() {
        return databaseWorkflowId;
    }

    public String getDatabaseTable() {
        return databaseTable;
    }

    public String getDatabaseId() {
        return databaseId;
    }

    public String getSequenceDetailProvider(MatchResult t) {
        return new StringBuilder(t.end())
                .append(SequenceDetailGenerator.getSequenceDetailProviderQuerySQL(this))
                .append(t.group(1))                     // <-- select ... from ... join
                .append(" sequence_detail_provider ")     // <-- sequence_detail_form_request
                .append(t.group(3)).append(" ")     // <-- sequencede[0-9]+_
                .append(t.group(4))                     // <-- where's
                .toString();
    }    
    
}
