package qms.configuration.core;

import DPMS.Mapping.User;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.IPendingCountByType;
import ape.pending.core.IPendingHandler;
import ape.pending.core.PendingHandlerService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.configuration.DAOInterface.IRelationshipUserPositionDAO;
import qms.configuration.entity.RelationshipUserPosition;
import qms.configuration.pending.imp.ToAssignJob;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class UserPositionService extends PendingHandlerService<RelationshipUserPosition> implements IPendingHandler, IPendingCountByType {

    private static final long serialVersionUID = 7281781045605464857L;
    
    public static final String USER_TO_ACTIVATE_FILTER = "c.deleted = 0 AND c.status = " + User.STATUS.TO_ACTIVATE.getValue();

    @SMDMethod
    public GenericSaveHandle saveUserPositions(Long userId, List<Long> listRelationshipUserPosition) {
        IRelationshipUserPositionDAO dao = getBean(IRelationshipUserPositionDAO.class);

        List<RelationshipUserPosition> listToSave = new ArrayList<>();
        for (Long relationshipUserPosition : listRelationshipUserPosition) {
            RelationshipUserPosition rup = new RelationshipUserPosition(userId, relationshipUserPosition);
            listToSave.add(rup);
        }
        GenericSaveHandle gsh = dao.saveListRelationshipUserPosition(listToSave, getLoggedUserDto());
        return gsh;
    }
    
    @Override
    @SMDMethod
    public Map<String, Integer> getPendingMap() {
        Map pendings = getPendingMap(this, Module.CONFIGURATION, getUntypedDAO());
        return pendings;
    }

    @Override
    public Integer countTypeByUser(Long loggedUserId, APE pendingType, IUntypedDAO dao) {
        setLoggedUserId(loggedUserId);
        switch (pendingType) {
            case CONFIGURATION_TO_ASSIGN_USER_POSITION:
                return new ToAssignJob(dao).getCountByUser(loggedUserId);
            case CONFIGURATION_TO_ACTIVE_USER:
                return getPending_ToActiveUser(dao);
        }
        return 0;
    }

    public Integer getPending_ToActiveUser(IUntypedDAO dao) {

        if (!(getLoggedUserServices().contains(ProfileServices.ADMON_ACCESOS)
                || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA))) {
            return 0;
        }
        
        String sql = ""
                + " SELECT COUNT(c.id)"
                + " FROM "+ User.class.getCanonicalName() + " c"
                + " WHERE " + USER_TO_ACTIVATE_FILTER;
        return dao.HQL_findSimpleInteger(sql);
    }

    
}
