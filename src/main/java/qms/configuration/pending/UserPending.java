/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package qms.configuration.pending;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.IPending;
import ape.pending.core.IPendingOperation;
import javax.annotation.Nonnull;
import qms.access.dto.ILoggedUser;
import qms.configuration.pending.imp.ToAssignJob;
import qms.document.pending.imp.ToFillForm;
import qms.util.Helper;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class UserPending extends Helper implements IPending {

    public UserPending(IUntypedDAO dao) {
        super(dao);
    }

    public void toAssignJob(Long recordId, Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToAssignJob(dao).execute(recordId, trigger, loggedUser);
    }
    
    private void normalize(Class trigger, @Nonnull ILoggedUser loggedUser, Long pendingRecordId) {
        new ToAssignJob(dao).recalculate(loggedUser, trigger, pendingRecordId);
        getLogger().debug("User pending {} were normalized", pendingRecordId);
    }
    
    public void normalize(Class trigger, @Nonnull ILoggedUser loggedUser) {
        normalize(trigger, loggedUser, null);
    }
    
    /**
     * @see IPendingOperation#recalculateActiveAndAttended(ILoggedUser loggedUser, Class trigger)
     */
    public void normalizeActiveAndAttended(Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToAssignJob(dao).recalculateActiveAndAttended(loggedUser, trigger);
        new ToFillForm(dao).recalculateActiveAndAttended(loggedUser, trigger);
    }
    
    @Override
    public void normalize(@Nonnull ILoggedUser loggedUser) {
        normalize(this.getClass(), loggedUser, null);
    }

    @Override
    public void normalize(@Nonnull ILoggedUser loggedUser, Long pendingRecordId) {
        normalize(this.getClass(), loggedUser, pendingRecordId);
    }

    @Override
    public void dailyCheck(@Nonnull ILoggedUser loggedUser) {
        new ToAssignJob(dao).dailyCheck(this.getClass(), loggedUser);
    }
}
