package qms.configuration.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;

/**
 *
 * <AUTHOR> @Block Networks
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "usuario_puesto")
public class RelationshipUserPosition extends CompositeStandardEntity<RelationshipUserPositionId> implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private RelationshipUserPositionId id;
    public RelationshipUserPosition(Long userId, Long positionId) {
        id = new RelationshipUserPositionId(userId, positionId);
    }

    public RelationshipUserPosition() {
    }

    @Override
    @EmbeddedId
    public RelationshipUserPositionId getId() {
        return id;
    }

    @Override
    public void setId(RelationshipUserPositionId id) {
        this.id = id;
    }

    @Override
    public RelationshipUserPositionId identifuerValue() {
        return getId();
    }
    
}
