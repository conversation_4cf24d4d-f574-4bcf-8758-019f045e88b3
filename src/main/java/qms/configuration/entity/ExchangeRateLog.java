package qms.configuration.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.configuration.util.IExchangeRate;
import qms.framework.util.CacheConstants;
import qms.util.CodePrefix;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.CATALOGS_SYSTEM, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "exchange_rate_log")
@CodePrefix("RATE-LOG-")
@JsonPropertyOrder(alphabetic = true)
public class ExchangeRateLog extends DomainObject implements IExchangeRate {

    private Integer status;
    private Integer deleted = 0;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;    
    private Integer fromCurrency;
    private Integer toCurrency;
    private Double conversionRate;
    private Date syncDate;
    private Long exchangeRateId;
    private String errorMessage;
    private Long currencyDataWsId;
    
    public ExchangeRateLog() {
    }

    public ExchangeRateLog(Long id) {
        this.id = id;
    }
    
    @Id
    @Column(name = "exchange_rate_log_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = 0;
        }
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "from_currency")
    @Override
    public Integer getFromCurrency() {
        return fromCurrency;
    }

    @Override
    public void setFromCurrency(Integer fromCurrency) {
        this.fromCurrency = fromCurrency;
    }

    @Column(name = "to_currency")
    @Override
    public Integer getToCurrency() {
        return toCurrency;
    }

    @Override
    public void setToCurrency(Integer toCurrency) {
        this.toCurrency = toCurrency;
    }
    
    @Column(name = "conversion_rate")
    @Override
    public Double getConversionRate() {
        return conversionRate;
    }

    @Override
    public void setConversionRate(Double conversionRate) {
        this.conversionRate = conversionRate;
    }

    @Column(name = "sync_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getSyncDate() {
        return syncDate;
    }

    @Override
    public void setSyncDate(Date syncDate) {
        this.syncDate = syncDate;
    }

    @Column(name = "exchange_rate_id")
    public Long getExchangeRateId() {
        return exchangeRateId;
    }

    public void setExchangeRateId(Long exchangeRateId) {
        this.exchangeRateId = exchangeRateId;
    }

    @Column(name = "error_message")
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Column(name = "currency_data_ws_id")
    public Long getCurrencyDataWsId() {
        return currencyDataWsId;
    }

    public void setCurrencyDataWsId(Long currencyDataWsId) {
        this.currencyDataWsId = currencyDataWsId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ExchangeRateLog other = (ExchangeRateLog) obj;
        return Objects.equals(this.id, other.id);
    }


    @Override
    public String toString() {
        return "ExchangeRateLogLog{fromCurrency=" + fromCurrency + ", toCurrency=" + toCurrency + '}';
    }

    

}
