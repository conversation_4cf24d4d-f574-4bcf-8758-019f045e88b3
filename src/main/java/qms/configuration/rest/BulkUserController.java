package qms.configuration.rest;

import DPMS.DAOInterface.IUserDAO;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.configuration.dto.BulkUserDTO;
import qms.configuration.util.IBulkUser;
import qms.framework.entity.BulkLoad;
import qms.framework.entity.BulkLoadRow;
import qms.framework.entity.BulkUser;
import qms.framework.entity.BulkUserRecord;
import qms.framework.enums.BulkLoadType;
import qms.util.GridFilter;

@Lazy
@RestController
@RequestMapping("bulk-user")
public class BulkUserController {

    private final String HAS_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'BULK_ACCESS_CREATOR'"
    + ")";

    @Autowired
    @Qualifier("HibernateDAO_User")
    private IUserDAO dao;
    
    @PostMapping()
    @RequestMapping("list")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<IBulkUser> list(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.setDirection((byte)2);
            filter.getField().setOrderBy("entity.startDate");
        }
        return dao.HQL_getRows(" "
                + " SELECT new " + BulkUserDTO.class.getCanonicalName() + "( "
                    + " entity.id AS id "
                    + ",entity.code AS code "
                    + ",entity.hasSourceFile AS hasSourceFile "
                    + ",entity.startDate AS startDate "
                    + ",entity.endDate AS endDate "
                    + ",entity.totalRecords AS totalRecords "
                    + ",entity.elapsedMinutes AS elapsedMinutes "
                    + ",entity.activeUsersCount AS activeUsersCount "
                    + ",entity.inactiveUsersCount AS inactiveUsersCount "
                    + ",entity.requireLicenseCount AS requireLicenseCount "
                + " )"
                + " FROM " + BulkUser.class.getCanonicalName() + " entity "
                + " WHERE "
                    + " entity.deleted = 0 ",
                 filter
        );
    }
    
    @GetMapping()
    @RequestMapping({"load/{id}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<IBulkUser> load(
            @PathVariable(value = "id") Long id
    ) {
        final IBulkUser data =  dao.HQLT_findSimple(BulkUserDTO.class, " "
                + " SELECT new " + BulkUserDTO.class.getCanonicalName() + "( "
                    + " entity.id AS id "
                    + ",entity.code AS code "
                    + ",entity.hasSourceFile AS hasSourceFile "
                    + ",entity.startDate AS startDate "
                    + ",entity.endDate AS endDate "
                    + ",entity.totalRecords AS totalRecords "
                    + ",entity.elapsedMinutes AS elapsedMinutes "
                    + ",entity.activeUsersCount AS activeUsersCount "
                    + ",entity.inactiveUsersCount AS inactiveUsersCount "
                    + ",entity.requireLicenseCount AS requireLicenseCount "
                + " )"
                + " FROM " + BulkUser.class.getCanonicalName() + " entity "
                + " WHERE "
                    + " entity.id = :id",
                "id",
                id
        );
        return ResponseEntity.ok(data);
    }
    
    @GetMapping()
    @RequestMapping({"record-profile/{id}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> recordProfile(
            @PathVariable(value = "id") Long id,
            @RequestBody GridFilter filter
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        filter.getCriteria().put("<condition>", " "
                    + " entity.deleted = 0 "
                    + " AND load.type = " + BulkLoadType.SAVE_PROFILE.getValue()
                    + " AND entity.id = " + id
        );
        return getLoadRows(filter);
    }
    
    @GetMapping()
    @RequestMapping({"record-job/{id}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> recordJob(
            @PathVariable(value = "id") Long id,
            @RequestBody GridFilter filter
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        filter.getCriteria().put("<condition>", " "
                    + " entity.deleted = 0 "
                    + " AND load.type = " + BulkLoadType.SAVE_POSITION.getValue()
                    + " AND entity.id = " + id
        );
        return getLoadRows(filter);
    }
    
    @GetMapping()
    @RequestMapping({"record-user/{id}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> recordUser(
            @PathVariable(value = "id") Long id,
            @RequestBody GridFilter filter
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        filter.getCriteria().put("<condition>", " "
                    + " entity.deleted = 0 "
                    + " AND load.type = " + BulkLoadType.SAVE_USER.getValue()
                    + " AND entity.id = " + id
        );
        return getLoadRows(filter);
    }
     
    private GridInfo<Map<String, Object>> getLoadRows(GridFilter filter) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.setDirection((byte)1);
            filter.getField().setOrderBy("row.rowNumber");
        }
        return dao.HQL_getRows(" "
                + " SELECT new map( "
                    + " row.id AS id "
                    + ", row.rowNumber AS rowNumber "
                    + ", row.description AS description "
                    + ", row.type AS type "
                    + ", row.errorType AS errorType "
                    + ", row.errorValue AS errorValue "
                + " )"
                + " FROM " + BulkUserRecord.class.getCanonicalName() + " record "
                + " JOIN " + BulkLoad.class.getCanonicalName() + " load "
                + " ON record.id.bulkLoadId = load.id"
                + " JOIN " + BulkUser.class.getCanonicalName() + " entity "        
                + " ON record.id.bulkUserId = entity.id"
                + " JOIN " + BulkLoadRow.class.getCanonicalName() + " row "        
                + " ON load.id = row.bulkLoadId",
                 filter
        );
    }

}
