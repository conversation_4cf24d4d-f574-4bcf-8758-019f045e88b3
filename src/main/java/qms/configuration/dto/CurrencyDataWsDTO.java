package qms.configuration.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CurrencyDataWsDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long id;
    private Integer fromCurrency;
    private Integer toCurrency;
    private Integer apiProvider;
    private String apiEndpoint;
    private String apiKey;

    public CurrencyDataWsDTO() {
    }

    public CurrencyDataWsDTO(
            Long id, 
            Integer fromCurrency, 
            Integer toCurrency, 
            Integer apiProvider, 
            String apiEndpoint,
            String apiKey
    ) {
        this.id = id;
        this.fromCurrency = fromCurrency;
        this.toCurrency = toCurrency;
        this.apiProvider = apiProvider;
        this.apiEndpoint = apiEndpoint;
        this.apiKey = apiKey;
    }

    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getFromCurrency() {
        return fromCurrency;
    }

    public void setFromCurrency(Integer fromCurrency) {
        this.fromCurrency = fromCurrency;
    }

    public Integer getToCurrency() {
        return toCurrency;
    }

    public void setToCurrency(Integer toCurrency) {
        this.toCurrency = toCurrency;
    }

    public Integer getApiProvider() {
        return apiProvider;
    }

    public void setApiProvider(Integer apiProvider) {
        this.apiProvider = apiProvider;
    }

    public String getApiEndpoint() {
        return apiEndpoint;
    }

    public void setApiEndpoint(String apiEndpoint) {
        this.apiEndpoint = apiEndpoint;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CurrencyDataWsDTO other = (CurrencyDataWsDTO) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "CurrencyDataWsDTO{" + "fromCurrency=" + fromCurrency + ", toCurrency=" + toCurrency + ", apiProvider=" + apiProvider + '}';
    }

    
    
}
