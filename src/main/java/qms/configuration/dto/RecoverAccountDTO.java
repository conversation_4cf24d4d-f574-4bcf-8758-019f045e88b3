package qms.configuration.dto;

import DPMS.Mapping.IAddressableUser;
import java.util.Objects;
import qms.document.mail.IRecipientId;

/**
 *
 * <AUTHOR>
 */
public class RecoverAccountDTO implements IAddressableUser, IRecipientId {

    private static final long serialVersionUID = 1L;
    
    private String code;
    private String correo;
    private String login;
    private Long id;
    private String description;
    private Integer version;

    public RecoverAccountDTO(String code, String correo, String login, Long id, String description, Integer version) {
        this.code = code;
        this.correo = correo;
        this.login = login;
        this.id = id;
        this.description = description;
        this.version = version;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getCorreo() {
        return correo;
    }

    @Override
    public String getLogin() {
        return login;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public Long getRecipientId() {
        return this.id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setCorreo(String correo) {
        this.correo = correo;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
    
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.code);
        hash = 97 * hash + Objects.hashCode(this.correo);
        hash = 97 * hash + Objects.hashCode(this.login);
        hash = 97 * hash + Objects.hashCode(this.id);
        hash = 97 * hash + Objects.hashCode(this.description);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RecoverAccountDTO other = (RecoverAccountDTO) obj;
        if (!Objects.equals(this.code, other.getCode())) {
            return false;
        }
        return Objects.equals(this.id, other.getId());
    }
    
    @Override
    public String toString() {
        return "RecoverAccountDTO{" + "code=" + code + ", correo=" + correo + ", account=" + login + ", id=" + id + ", description=" + description + '}';
    }
}
