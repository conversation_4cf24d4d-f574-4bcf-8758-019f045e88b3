
package qms.util.interfaces;

/**
 *
 * <AUTHOR> @Block Networks
 * Se utiliza para tener una base con la cual comparar los pendientes
 * declarados en APE que su base declarada es distinta entre el pendiente y APE,
 * es decir dentro de el pendiente se decalra como base, por ejemplo User pero dentro de
 * APE esta declarado que se utiliza UserRef, al crear una interfaz que herede
 * de ICommonEntity e implmentarla en las entidades esta se comparara y al coincidir
 * sera tomada en cuenta.
 * 
 */
public interface ICommonEntity {

}
