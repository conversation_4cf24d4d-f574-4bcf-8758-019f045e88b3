package qms.util.interfaces;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @param <EntityIdentifierType> id type
 * Agregar esta interface si se utilizará  {@link qms.util.LinkedSelector LinkedSelector}
 * Configurar la anotación {@link org.springframework.data.annotation.LastModifiedDate @LastModifiedDate}
 *
 * <AUTHOR>
 */
public interface IAuditableCompositeEntity<EntityIdentifierType extends Serializable> extends ILinkedCompositeEntity<EntityIdentifierType> {

    public Date getCreatedDate();

    public void setCreatedDate(Date createdDate);

    public Date getLastModifiedDate();

    public void setLastModifiedDate(Date lastModifiedDate);

    public Long getCreatedBy();

    public void setCreatedBy(Long createdBy);

    public Long getLastModifiedBy();

    public void setLastModifiedBy(Long lastModifiedBy);
}
