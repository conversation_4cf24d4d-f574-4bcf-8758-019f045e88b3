package qms.util.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import qms.framework.dto.EntityDTO;
import qms.framework.dto.IEntityDTO;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MultiFileDTO extends EntityDTO implements Serializable, IEntityDTO {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Long contentSize;
    private String description;
    private Date lastModifiedDate;
    private String lastModifiedBy;
    private Integer stage;
    private String extension;
    private String contentType;
    private String code;
    private Integer activityCommitmentTask;
    private String commentFile;
    private Long activityId;
    private Long createdBy; 

    public MultiFileDTO() {
    }

    /**
     * Constructor utilizado en la consulta de {@link qms.document.util.MultiFileSelector#getGroundValues getGroundValues}
     **/
    public MultiFileDTO(
            Long id, 
            Long size, 
            String description, 
            Date lastModifiedDate, 
            String lastModifiedBy, 
            Integer stage,
            String extension,
            String contentType
    ) {
        this.id = id;
        this.contentSize = size;
        this.description = description;
        this.lastModifiedDate = lastModifiedDate;
        this.lastModifiedBy = lastModifiedBy;
        this.stage = stage;
        this.extension = extension;
        this.contentType = contentType;
    }
    
    public MultiFileDTO(
            Long id, 
            Long size, 
            String description,
            Date lastModifiedDate,
            String lastModifiedBy, 
            Integer stage, 
            String extension,
            String contentType,
            String code
    ) {
        this.id = id;
        this.contentSize = size;
        this.description = description;
        this.lastModifiedDate = lastModifiedDate;
        this.lastModifiedBy = lastModifiedBy;
        this.stage = stage;
        this.extension = extension;
        this.contentType = contentType;
        this.code = code;
    }
    
    /**
     * Constructor utilizado en la consulta de {@link qms.timesheet.dao.TimesheetDAO#getTimesheetFiles getTimesheetFiles}
     * @param id
     * @param size
     * @param description
     * @param lastModifiedDate
     * @param lastModifiedBy
     * @param stage
     * @param extension
     * @param contentType
     * @param code
     * @param activityCommitmentTask 
     */
    public MultiFileDTO(
            Long id, 
            Long size, 
            String description,
            Date lastModifiedDate,
            String lastModifiedBy, 
            Integer stage, 
            String extension,
            String contentType,
            String code,
            Integer activityCommitmentTask
    ) {
        this.id = id;
        this.contentSize = size;
        this.description = description;
        this.lastModifiedDate = lastModifiedDate;
        this.lastModifiedBy = lastModifiedBy;
        this.stage = stage;
        this.extension = extension;
        this.contentType = contentType;
        this.code = code;
        this.activityCommitmentTask = activityCommitmentTask;
    }
    
    /**
     * Constructor utilizado en la consulta de {@link qms.activity.util.ActivityMultiFileSelector#getGroundValues getGroundValues}
     * @param id
     * @param size
     * @param description
     * @param lastModifiedDate
     * @param lastModifiedBy
     * @param stage
     * @param extension
     * @param contentType
     * @param activityCommitmentTask
     * @param commentFile
     * @param activityId
     * @param createdBy
     **/
    public MultiFileDTO(
        Long id, 
        Long size, 
        String description, 
        Date lastModifiedDate, 
        String lastModifiedBy, 
        Integer stage, 
        String extension, 
        String contentType, 
        Integer activityCommitmentTask, 
        String commentFile,
        Long activityId,
        Long createdBy
    ) {
        this.id = id;
        this.contentSize = size;
        this.description = description;
        this.lastModifiedDate = lastModifiedDate;
        this.lastModifiedBy = lastModifiedBy;
        this.stage = stage;
        this.extension = extension;
        this.contentType = contentType;
        this.activityCommitmentTask = activityCommitmentTask;
        this.commentFile = commentFile;
        this.activityId = activityId;
        this.createdBy = createdBy;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public Long getContentSize() {
        return contentSize;
    }

    public void setContentSize(Long contentSize) {
        this.contentSize = contentSize;
    }
    
    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getActivityCommitmentTask() {
        return activityCommitmentTask;
    }

    public void setActivityCommitmentTask(Integer activityCommitmentTask) {
        this.activityCommitmentTask = activityCommitmentTask;
    }

    public String getCommentFile() {
        return commentFile;
    }

    public void setCommentFile(String commentFile) {
        this.commentFile = commentFile;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 83 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MultiFileDTO other = (MultiFileDTO) obj;
        return Objects.equals(this.id, other.getId());
    }

    @Override
    public String toString() {
        return "MultiFileDTO{" + "id=" + id + ", contentSize=" + contentSize + ", description=" + description + '}';
    }
    

    
}
