/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.util.dto;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class FormCurrentApprover {
    private String code;
    private Integer status;
    private Integer businessUnitDepartmentStatus;
    private Long outstandingSurveyId;
    private Long stageLatestFillOutUserId;
    private Long requestId;
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Long areaId;
    private Long formRequestId;
    private String formRequestReason;
    private Integer formRequestStatus;
    private Integer formRejectedAutorizationPoolIndex;
    private Date createdDate;
    private Long createdById;
    private String createdByName;
    private String createdByMail;
    // autorizadores responsables
    private Long cancelationCurrentUserId;
    private String cancelationCurrentUserName;
    private Integer cancelationCurrentUserStatus;
    private String cancelationCurrentUserMail;
    private String cancelationCurrentUserAccount;
    private Integer cancelationCurrentUserVersion;
    private Long adjustmentCurrentUserId;
    private String adjustmentCurrentUserName;
    private Integer adjustmentCurrentUserStatus;
    private String adjustmentCurrentUserMail;
    private String adjustmentCurrentUserAccount;
    private Integer adjustmentCurrentUserVersion;
    private Long reopenCurrentUserId;
    private String reopenCurrentUserName;
    private Integer reopenCurrentUserStatus;
    private String reopenCurrentUserMail;
    private String reopenCurrentUserAccount;
    private Integer reopenCurrentUserVersion;

    public FormCurrentApprover() {
        // empty!
    }

    public FormCurrentApprover(
        String code
        ,Integer status
        ,Long outstandingSurveyId
        ,Integer businessUnitDepartmentStatus
        ,Long stageLatestFillOutUserId
        ,Long requestId
        ,Long businessUnitId
        ,Long businessUnitDepartmentId
        ,Long areaId
        ,Long cancelationCurrentUserId
        ,String cancelationCurrentUserName
        ,Integer cancelationCurrentUserStatus
        ,String cancelationCurrentUserMail
        ,String cancelationCurrentUserAccount
        ,Integer cancelationCurrentUserVersion
        ,Long adjustmentCurrentUserId
        ,String adjustmentCurrentUserName
        ,Integer adjustmentCurrentUserStatus
        ,String adjustmentCurrentUserMail
        ,String adjustmentCurrentUserAccount
        ,Integer adjustmentCurrentUserVersion
        ,Long reopenCurrentUserId
        ,String reopenCurrentUserName
        ,Integer reopenCurrentUserStatus
        ,String reopenCurrentUserMail
        ,String reopenCurrentUserAccount
        ,Integer reopenCurrentUserVersion
        /* 
         * ¡IMPORTANTE!
         *   
         * Los parametros agregados en este CONSTRUCTOR, deben ser 
         * agregados en ambos constructores: JUSTO DESPUES DE `reopenCurrentUserMail`
         *
         * Para más contexto, ver: `FormUtil.getApproverData()`
         **/
        ,Long formRequestId
        ,String formRequestReason
        ,Integer formRequestStatus
        ,Integer formRejectedAutorizationPoolIndex
        ,Date createdDate
        ,Long createdById
        ,String createdByName
        ,String createdByMail
    ) {
        this.code = code;
        this.status = status;
        this.outstandingSurveyId = outstandingSurveyId;
        this.businessUnitDepartmentStatus = businessUnitDepartmentStatus;
        this.stageLatestFillOutUserId = stageLatestFillOutUserId;
        this.requestId = requestId;
        this.businessUnitId = businessUnitId;
        this.businessUnitDepartmentId = businessUnitDepartmentId;
        this.areaId = areaId;
        this.cancelationCurrentUserId = cancelationCurrentUserId;
        this.cancelationCurrentUserName = cancelationCurrentUserName;
        this.cancelationCurrentUserStatus = cancelationCurrentUserStatus;
        this.cancelationCurrentUserMail = cancelationCurrentUserMail;
        this.cancelationCurrentUserAccount = cancelationCurrentUserAccount;
        this.cancelationCurrentUserVersion = cancelationCurrentUserVersion;
        this.adjustmentCurrentUserId = adjustmentCurrentUserId;
        this.adjustmentCurrentUserName = adjustmentCurrentUserName;
        this.adjustmentCurrentUserStatus = adjustmentCurrentUserStatus;
        this.adjustmentCurrentUserMail = adjustmentCurrentUserMail;
        this.adjustmentCurrentUserAccount = adjustmentCurrentUserAccount;
        this.adjustmentCurrentUserVersion = adjustmentCurrentUserVersion;
        this.reopenCurrentUserId = reopenCurrentUserId;
        this.reopenCurrentUserName = reopenCurrentUserName;
        this.reopenCurrentUserStatus = reopenCurrentUserStatus;
        this.reopenCurrentUserMail = reopenCurrentUserMail;
        this.reopenCurrentUserAccount = reopenCurrentUserAccount;
        this.reopenCurrentUserVersion = reopenCurrentUserVersion;
        this.formRequestId = formRequestId;
        this.formRequestReason = formRequestReason;
        this.formRequestStatus = formRequestStatus;
        this.formRejectedAutorizationPoolIndex = formRejectedAutorizationPoolIndex;
        this.createdDate = createdDate;
        this.createdById = createdById;
        this.createdByName = createdByName;
        this.createdByMail = createdByMail;
    }
    
    public FormCurrentApprover(
        String code
        ,Integer status
        ,Long outstandingSurveyId
        ,Integer businessUnitDepartmentStatus
        ,Long stageLatestFillOutUserId
        ,Long requestId
        ,Long businessUnitId
        ,Long businessUnitDepartmentId
        ,Long areaId
        ,Long cancelationCurrentUserId
        ,String cancelationCurrentUserName
        ,Integer cancelationCurrentUserStatus
        ,String cancelationCurrentUserMail
        ,String cancelationCurrentUserAccount
        ,Integer cancelationCurrentUserVersion
        ,Long adjustmentCurrentUserId
        ,String adjustmentCurrentUserName
        ,Integer adjustmentCurrentUserStatus
        ,String adjustmentCurrentUserMail
        ,String adjustmentCurrentUserAccount
        ,Integer adjustmentCurrentUserVersion
        ,Long reopenCurrentUserId
        ,String reopenCurrentUserName
        ,Integer reopenCurrentUserStatus
        ,String reopenCurrentUserMail
        ,String reopenCurrentUserAccount
        ,Integer reopenCurrentUserVersion
        /* 
         * ¡IMPORTANTE!
         *   
         * Los parametros agregados en este CONSTRUCTOR, deben ser 
         * agregados en ambos constructores: JUSTO DESPUES DE `reopenCurrentUserMail`
         *
         * Para más contexto, ver: `FormUtil.getApproverData()`
         **/
    ) {
        this.code = code;
        this.status = status;
        this.outstandingSurveyId = outstandingSurveyId;
        this.businessUnitDepartmentStatus = businessUnitDepartmentStatus;
        this.stageLatestFillOutUserId = stageLatestFillOutUserId;
        this.requestId = requestId;
        this.businessUnitId = businessUnitId;
        this.businessUnitDepartmentId = businessUnitDepartmentId;
        this.areaId = areaId;
        this.cancelationCurrentUserId = cancelationCurrentUserId;
        this.cancelationCurrentUserName = cancelationCurrentUserName;
        this.cancelationCurrentUserStatus = cancelationCurrentUserStatus;
        this.cancelationCurrentUserMail = cancelationCurrentUserMail;
        this.cancelationCurrentUserAccount = cancelationCurrentUserAccount;
        this.cancelationCurrentUserVersion = cancelationCurrentUserVersion;
        this.adjustmentCurrentUserId = adjustmentCurrentUserId;
        this.adjustmentCurrentUserName = adjustmentCurrentUserName;
        this.adjustmentCurrentUserStatus = adjustmentCurrentUserStatus;
        this.adjustmentCurrentUserMail = adjustmentCurrentUserMail;
        this.adjustmentCurrentUserAccount = adjustmentCurrentUserAccount;
        this.adjustmentCurrentUserVersion = adjustmentCurrentUserVersion;
        this.reopenCurrentUserId = reopenCurrentUserId;
        this.reopenCurrentUserName = reopenCurrentUserName;
        this.reopenCurrentUserStatus = reopenCurrentUserStatus;
        this.reopenCurrentUserMail = reopenCurrentUserMail;
        this.reopenCurrentUserAccount = reopenCurrentUserAccount;
        this.reopenCurrentUserVersion = reopenCurrentUserVersion;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreatedByMail() {
        return createdByMail;
    }

    public void setCreatedByMail(String createdByMail) {
        this.createdByMail = createdByMail;
    }

    public Long getFormRequestId() {
        return formRequestId;
    }

    public void setFormRequestId(Long formRequestId) {
        this.formRequestId = formRequestId;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Long getCreatedById() {
        return createdById;
    }

    public void setCreatedById(Long createdById) {
        this.createdById = createdById;
    }

    public String getCreatedByName() {
        return createdByName;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public Integer getBusinessUnitDepartmentStatus() {
        return businessUnitDepartmentStatus;
    }

    public void setBusinessUnitDepartmentStatus(Integer businessUnitDepartmentStatus) {
        this.businessUnitDepartmentStatus = businessUnitDepartmentStatus;
    }

    public Long getStageLatestFillOutUserId() {
        return stageLatestFillOutUserId;
    }

    public void setStageLatestFillOutUserId(Long stageLatestFillOutUserId) {
        this.stageLatestFillOutUserId = stageLatestFillOutUserId;
    }

    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    public Long getCancelationCurrentUserId() {
        return cancelationCurrentUserId;
    }

    public void setCancelationCurrentUserId(Long cancelationCurrentUserId) {
        this.cancelationCurrentUserId = cancelationCurrentUserId;
    }

    public String getCancelationCurrentUserName() {
        return cancelationCurrentUserName;
    }

    public void setCancelationCurrentUserName(String cancelationCurrentUserName) {
        this.cancelationCurrentUserName = cancelationCurrentUserName;
    }

    public Long getAdjustmentCurrentUserId() {
        return adjustmentCurrentUserId;
    }

    public void setAdjustmentCurrentUserId(Long adjustmentCurrentUserId) {
        this.adjustmentCurrentUserId = adjustmentCurrentUserId;
    }

    public String getAdjustmentCurrentUserName() {
        return adjustmentCurrentUserName;
    }

    public void setAdjustmentCurrentUserName(String adjustmentCurrentUserName) {
        this.adjustmentCurrentUserName = adjustmentCurrentUserName;
    }

    public Long getReopenCurrentUserId() {
        return reopenCurrentUserId;
    }

    public void setReopenCurrentUserId(Long reopenCurrentUserId) {
        this.reopenCurrentUserId = reopenCurrentUserId;
    }

    public String getReopenCurrentUserName() {
        return reopenCurrentUserName;
    }

    public void setReopenCurrentUserName(String reopenCurrentUserName) {
        this.reopenCurrentUserName = reopenCurrentUserName;
    }

    public String getFormRequestReason() {
        return formRequestReason;
    }

    public void setFormRequestReason(String formRequestReason) {
        this.formRequestReason = formRequestReason;
    }

    public Integer getFormRequestStatus() {
        return formRequestStatus;
    }

    public void setFormRequestStatus(Integer formRequestStatus) {
        this.formRequestStatus = formRequestStatus;
    }

    public Integer getFormRejectedAutorizationPoolIndex() {
        return formRejectedAutorizationPoolIndex;
    }

    public void setFormRejectedAutorizationPoolIndex(Integer formRejectedAutorizationPoolIndex) {
        this.formRejectedAutorizationPoolIndex = formRejectedAutorizationPoolIndex;
    }

    public String getCancelationCurrentUserMail() {
        return cancelationCurrentUserMail;
    }

    public void setCancelationCurrentUserMail(String cancelationCurrentUserMail) {
        this.cancelationCurrentUserMail = cancelationCurrentUserMail;
    }

    public String getAdjustmentCurrentUserMail() {
        return adjustmentCurrentUserMail;
    }

    public void setAdjustmentCurrentUserMail(String adjustmentCurrentUserMail) {
        this.adjustmentCurrentUserMail = adjustmentCurrentUserMail;
    }

    public String getAdjustmentCurrentUserAccount() {
        return adjustmentCurrentUserAccount;
    }

    public void setAdjustmentCurrentUserAccount(String adjustmentCurrentUserAccount) {
        this.adjustmentCurrentUserAccount = adjustmentCurrentUserAccount;
    }
    
    public Integer getAdjustmentCurrentUserVersion() {
        return adjustmentCurrentUserVersion;
    }

    public void setAdjustmentCurrentUserVersion(Integer adjustmentCurrentUserVersion) {
        this.adjustmentCurrentUserVersion = adjustmentCurrentUserVersion;
    }

    public String getReopenCurrentUserMail() {
        return reopenCurrentUserMail;
    }

    public void setReopenCurrentUserMail(String reopenCurrentUserMail) {
        this.reopenCurrentUserMail = reopenCurrentUserMail;
    }

    public Integer getCancelationCurrentUserStatus() {
        return cancelationCurrentUserStatus;
    }

    public void setCancelationCurrentUserStatus(Integer cancelationCurrentUserStatus) {
        this.cancelationCurrentUserStatus = cancelationCurrentUserStatus;
    }

    public Integer getAdjustmentCurrentUserStatus() {
        return adjustmentCurrentUserStatus;
    }

    public void setAdjustmentCurrentUserStatus(Integer adjustmentCurrentUserStatus) {
        this.adjustmentCurrentUserStatus = adjustmentCurrentUserStatus;
    }

    public Integer getReopenCurrentUserStatus() {
        return reopenCurrentUserStatus;
    }

    public void setReopenCurrentUserStatus(Integer reopenCurrentUserStatus) {
        this.reopenCurrentUserStatus = reopenCurrentUserStatus;
    }

    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }

    public String getCancelationCurrentUserAccount() {
        return cancelationCurrentUserAccount;
    }

    public void setCancelationCurrentUserAccount(String cancelationCurrentUserAccount) {
        this.cancelationCurrentUserAccount = cancelationCurrentUserAccount;
    }

    public String getReopenCurrentUserAccount() {
        return reopenCurrentUserAccount;
    }

    public void setReopenCurrentUserAccount(String reopenCurrentUserAccount) {
        this.reopenCurrentUserAccount = reopenCurrentUserAccount;
    }

    public Integer getCancelationCurrentUserVersion() {
        return cancelationCurrentUserVersion;
    }

    public void setCancelationCurrentUserVersion(Integer cancelationCurrentUserVersion) {
        this.cancelationCurrentUserVersion = cancelationCurrentUserVersion;
    }

    public Integer getReopenCurrentUserVersion() {
        return reopenCurrentUserVersion;
    }

    public void setReopenCurrentUserVersion(Integer reopenCurrentUserVersion) {
        this.reopenCurrentUserVersion = reopenCurrentUserVersion;
    }
}
