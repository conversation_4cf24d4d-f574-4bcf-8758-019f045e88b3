package qms.util;

import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import javax.annotation.Nullable;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.HibernateException;
import org.hibernate.type.LongType;
import org.hibernate.type.NumericBooleanType;
import org.hibernate.type.StringType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qms.util.interfaces.IGridFilter;
import qms.util.interfaces.IParameterHandler;

/**
 *
 * <AUTHOR>
 */
public class HQLHandler {

    private HQLHandler() {
    }

    private static final java.text.DateFormat FORMAT_TMSTP = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
    private static final java.text.DateFormat FORMAT_DTE = new SimpleDateFormat("dd/MM/yyyy");
    public static final NumericBooleanType NUMERIC_BOOLEAN_TYPE = new NumericBooleanType();
    private static final LongType LONG_TYPE = new LongType();
    private static final StringType STRING_TYPE = new StringType();
     private static final Logger LOGGER = LoggerFactory.getLogger(HQLHandler.class);

    private static String wrapTableName(String table){
        return table.trim().contains(" ") ? "("+table+")" : table;
    }

    public static String makeCountHQL(String table) throws QMSException, Exception {
        return makeCountHQL(table, null, null);
    }

    public static String makeCountHQL(Class<?> clazz, IGridFilter filter, Boolean like) throws QMSException, Exception {
        return makeCountHQL(clazz.getName(), filter, like);
    }

    public static String makeCountHQL(String table, IGridFilter filter, Boolean like) throws QMSException, Exception {
        String base = "SELECT COUNT(1) FROM " + wrapTableName(table) + " c ";
        if (filter == null) {
            return base;
        }
        return base + QueryHandler.appendNotEmpty(HQLHandler.parseFilterWhere(filter, like, "c"), " WHERE ");
    }

    public static String makeGroupedCountHQL(Class<?> clazz, List<String> group) throws QMSException, Exception {
        return makeGroupedCountHQL(clazz.getName(), group);
    }

    public static String makeGroupedCountHQL(String table, List<String> group) throws QMSException, Exception {
        return makeGroupedCountHQL(table, group, null, null);
    }

    public static String makeGroupedCountHQL(Class<?> clazz, List<String> group, IGridFilter filter, Boolean like) throws QMSException, Exception {
        return makeGroupedCountHQL(clazz.getName(), group, filter, like);
    }
    
    public static String makeGroupedCountHQL(String table, String group, IGridFilter filter, Boolean like) throws QMSException, Exception {
        return makeGroupedCountHQL(table, Collections.singletonList(group), filter, like);
    }

    public static String makeGroupedCountHQL(String table, List<String> group, IGridFilter filter, Boolean like) throws QMSException, Exception {
        StringJoiner joiner = new StringJoiner(",c.", "c.", "");
        group.forEach(joiner::add);
        String groupStr = joiner.toString().replaceAll("(?i)c.trunc\\(\\s*([a-z]+)\\s*\\)", "trunc(c.$1)");
        String base;
        if (table.contains("SELECT ")) {
            base = "SELECT COUNT(1)," + groupStr + " FROM " + table.trim().replaceAll("(?i)^select\\s+.+?\\s+FROM[\\s\\r\\n]+", "").replaceAll("(?i)ORDER BY.*", "");
        } else {
            base = "SELECT COUNT(1)," + groupStr + " FROM " + wrapTableName(table) + " c " + QueryHandler.appendNotEmpty(HQLHandler.parseFilterWhere(filter, like, null), " WHERE ");
        }
        String groupByClause = " GROUP BY " + groupStr;
        if (filter == null) {
            return base + groupByClause;
        }
        String replaceAll = (base + groupByClause);
        LOGGER.trace(
                "Grouped count hql of {} {}",
                table,
                replaceAll
        );
        return replaceAll;
    }

    public static String parseFilterWhere(IGridFilter filter, boolean like, String entityAlias) {
        return parseFilterWhere(filter, like, entityAlias, false);
    }

    public static String parseFilterWhere(IGridFilter filter, boolean like, String entityAlias, boolean binded) {    
        filter.getAllConditions();
        return parseMapFilter(filter, like, entityAlias, binded).get(QueryHandler.CRITERIA_WHERE);
    }

    public static Map<String, String> parseMapFilter(IGridFilter filter, boolean like, String entityAlias, boolean binded) {
        return parseMapFilter(filter, like, entityAlias, null, binded);
    }

    public static Map<String, String> parseMapFilter(
            IGridFilter filter,
            boolean like,
            String entityAlias,
            String parentField,
            boolean binded
    ) {
        if (entityAlias == null) {
            entityAlias = "c";
        }
        if (filter.isShowMoreQuery() && parentField == null) {
            throw new RuntimeException("parentField must be specified! you might be using the wrong implementation of HQL_getRows! (try one named 'HQL_getTreeRows' or 'getTreeLightRows')");
        }
        Map<String, String> m = new HashMap<>(), temp;
        MapStringBuilder f = new MapStringBuilder();
        final boolean asumerNumberAsDot = filter.isAsumerNumberAsDot();
        String where, having, str = null;
        /*Elimina la llave de OR*/
        if (filter.getCriteria().containsKey("<or>")) {
            str = filter.getCriteria().get("<or>").toString();
            filter.getCriteria().remove("<or>");
            f.append(QueryHandler.CRITERIA_WHERE, "(");
        }

        //Parsea los criterios que se reciben en el hashMap criteria
        if (filter.getCriteria() != null) {
            Set<Map.Entry<String, String>> keys = filter.getCriteria().entrySet();
            Iterator<Map.Entry<String, String>> it = keys.iterator();
            Map.Entry<String, String> renglon;
            //LOGGER.trace(" - 1.1. Son " + keys.size() + " criterios... ");
            while (it.hasNext()) {
                renglon = it.next();
                temp = parseMapCriteria(renglon.getKey(), getValueCriteria(renglon.getValue()), like, entityAlias, filter);
                f.append(QueryHandler.CRITERIA_WHERE, temp.get(QueryHandler.CRITERIA_WHERE));
                f.append(QueryHandler.CRITERIA_HAVING, temp.get(QueryHandler.CRITERIA_HAVING));               
            }            
        }

        //Parsea los criterios que se recibien en el hashMap likeCriteria
        if (filter.getLikeCriteria() != null) {
            Set<Map.Entry<String, String>> keys = filter.getLikeCriteria().entrySet();
            Iterator<Map.Entry<String, String>> it = keys.iterator();
            Map.Entry<String, String> renglon;
            //LOGGER.trace(" - 1.2. Son " + keys.size() + " criterios... ");
             while (it.hasNext()) {
                renglon = it.next();
                temp = parseMapCriteria(renglon.getKey(), getValueCriteria(renglon.getValue()), true, entityAlias, filter);
                f.append(QueryHandler.CRITERIA_WHERE, temp.get(QueryHandler.CRITERIA_WHERE));
                f.append(QueryHandler.CRITERIA_HAVING, temp.get(QueryHandler.CRITERIA_HAVING));               
            }      
        }

        //Parsea los criterios que se reciben en el hashMap LowerLimit
        if (filter.getLowerLimit() != null) {
            Set<Map.Entry<String, String>> keys = filter.getLowerLimit().entrySet();
            Iterator<Map.Entry<String, String>> it = keys.iterator();
            Map.Entry<String, String> renglon;
            //LOGGER.trace(" - 1.3. Son " + keys.size() + " criterios 'LowerLimit'... ");
            while (it.hasNext()) {
                renglon = it.next();
                f.append(QueryHandler.CRITERIA_WHERE, parseLimit(renglon.getKey(), renglon.getValue(), ">=", entityAlias, filter.isAsumeAlias(), asumerNumberAsDot));
            }
        }

        //Parsea los criterios que se reciben en el hashMap UpperLimit
        if (filter.getUpperLimit() != null) {
            Set<Map.Entry<String, String>> keys = filter.getUpperLimit().entrySet();
            Iterator<Map.Entry<String, String>> it = keys.iterator();
            Map.Entry<String, String> renglon;
            //LOGGER.trace(" - 1.4. Son " + keys.size() + " criterios 'UpperLimit'... ");
            while (it.hasNext()) {
                renglon = it.next();
                f.append(QueryHandler.CRITERIA_WHERE, parseLimit(renglon.getKey(), renglon.getValue(), "<=", entityAlias, filter.isAsumeAlias(), asumerNumberAsDot));
            }
        }

        /*si habia un or, lo agrega al final*/
        if (str != null) {
            f.append(QueryHandler.CRITERIA_WHERE, ")").append(parseMapCriteria("<or>", str, like, entityAlias, filter).get(QueryHandler.CRITERIA_WHERE));
            filter.getCriteria().put("<or>", str);
        }
        StringBuilder chain = new StringBuilder(100);
        if (filter.getCondition() == null && filter.getFilteredEntity() == null && filter.getLinkedDialogGrid() == null && !filter.isShowMoreQuery()) {
            where = f.getCriteria(QueryHandler.CRITERIA_WHERE).trim();
        } else if (filter.getCondition() == null && filter.getFilteredEntity() == null && filter.getLinkedDialogGrid() == null && filter.isShowMoreQuery()) {
            where = parentField.replace("_", ".") + " = " + filter.getGridShowMore().getParentId() + " AND ( " + f.getCriteria(QueryHandler.CRITERIA_WHERE).substring(4) + " ) ";
            if (filter.isShowMoreNoopIds()) {
                chain.append(" AND ").append(entityAlias).append(".id NOT IN (").append(StringUtils.join(filter.getGridShowMore().getNoopPageIds(), ", ")).append(")");
            }
        } else {
            if (filter.getCondition() != null && !filter.getCondition().isEmpty()) {
                chain.append(" (").append(filter.getCondition()).append(") AND ");
            }
            if (filter.getFilteredEntity() != null && !filter.getFilteredEntity().isEmpty()) {
                chain.append(" (").append(filter.getFilteredEntity()).append(") AND ");
            }
            if (filter.getLinkedDialogGrid() != null && !filter.getLinkedDialogGrid().isEmpty()) {
                chain.append(" (").append(filter.getLinkedDialogGrid()).append(") AND ");
            }
            if (filter.isShowMoreQuery()) {
                chain.append(" (").append(parentField.replace("_", ".")).append(" = ").append(filter.getGridShowMore().getParentId());
                if (filter.isShowMoreNoopIds()) {
                    chain.append(" AND ").append(entityAlias).append(".id NOT IN (").append(StringUtils.join(filter.getGridShowMore().getNoopPageIds(), ", ")).append(")");
                }
                chain.append(" ) AND ");
            }
            if (chain.length() > 0 && !f.getCriteria(QueryHandler.CRITERIA_WHERE).isEmpty()) {
                where = chain + " ( " + f.getCriteria(QueryHandler.CRITERIA_WHERE).substring(4) + " ) ";
            } else if (chain.length() > 0) {
                where = chain.substring(0, chain.toString().length() - 4);
            } else {
                where = f.getCriteria(QueryHandler.CRITERIA_WHERE).trim();
            }
        }
        having = f.getCriteria(QueryHandler.CRITERIA_HAVING).trim();
        if (binded) {
            where = bindParamsCriteria(null, filter, like, where);
            having = bindParamsCriteria(null, filter, like, having);
        }
        m.put(QueryHandler.CRITERIA_WHERE, where.replaceFirst(filter.getLikeOrSentence() ? "^OR" : "^AND", ""));
        m.put(QueryHandler.CRITERIA_HAVING, having.replaceFirst(filter.getLikeOrSentence() ? "^OR" : "^AND", ""));
        return m;
    }
    
    private static String parseLimit(
            String property,
            String value, 
            String limit,
            String entityAlias,
            boolean asumeAlias,
            boolean asumerNumberAsDot
    ) {
        String filtros = "";
        if (!value.isEmpty()) {
            StringBuilder b = new StringBuilder(25);
            String llave = property;
            if (asumerNumberAsDot) {
                llave = llave.replace('#', '.');
            }
            if (value.contains("<zero>")) {
                b.append(" AND 1=0 ");
            } else {
                if(asumeAlias) {
                    b.append(" AND ").append(entityAlias).append(".");
                } else {
                    b.append(" AND ");
                }
                if (value.contains("<time>")) {
                    b.append("convert(datetime, '1900-01-01 ' + convert(nvarchar(8), ").append(llave).append(", 108), 120) ");
                } else {
                    b.append(llave).append(" ");
                }
                b.append(" ").append(limit).append(" :").append(property.replace("#", "").replace(".","_")).append(limit.equals(">=") ? "L" : "U").append(" ");
            }
            filtros = b.toString();
        }
        return filtros;
    }

    private static String getValueCriteria(Object value) {
        if (value instanceof HashMap) {
            return (String) (((HashMap) value).get("value"));
        }
        return value.toString();
    }

    /**
     *
     * @param property
     * @param value
     * @param like
     * @param entityAlias
     * @param filter
     * @return
     */
    public static Map<String, String> parseMapCriteria(
            final String property,
            final String value, 
            final boolean like,
            String entityAlias,
            final IGridFilter filter
    ) {
        if (entityAlias == null) {
            entityAlias = "c";
        }
        //LOGGER.trace("---> parseCriteria ... " + property + ", " + value + ", " + like);
        MapStringBuilder hqlCriteria = new MapStringBuilder();
        String[] textIn;
        final boolean asumerNumberAsDot = filter.isAsumerNumberAsDot();
        if (!value.isEmpty() && !property.isEmpty()) {
            String llave = property;
            if (asumerNumberAsDot) {
                llave = llave.replace('#', '.');
            }
            String bindKey1 = HQLHandler.getSafeParameterName(property);
            String criteriaType = parseCriteriaType(llave);
            //Define si el like aplica sentencia OR o AND
            hqlCriteria.append(criteriaType, filter.getLikeOrSentence() ?  " OR (( " :  " AND (( ");                        
            String[] searchedValue = singleIterateParamsCriteria(llave, value) ? new String[]{value} : value.split(",");
            for (int i = 0; i < searchedValue.length; i++) {
                String bindKey;
                if (i > 0) {
                    bindKey = bindKey1 + i;
                    hqlCriteria.append(criteriaType, ") OR (");
                } else {
                    bindKey = bindKey1;
                }
                if (value.equals("<zero>")) {
                    hqlCriteria.append(criteriaType, llave + " = 0 ");
                } else if (value.equals("<not-zero>")) {
                    hqlCriteria.append(criteriaType, llave + " > 0 ");
                } else if (value.equals("<is-null>")) {
                    hqlCriteria.append(criteriaType, llave + " IS NULL ");
                } else if (value.equals("<not-null>")) {
                    hqlCriteria.append(criteriaType, llave + " IS NOT NULL ");
                } else if (value.equals("<not-empty>")) {
                    hqlCriteria.append(criteriaType, llave + " != '' ");
                } else if (value.equals("<not-null-and-not-zero>")) {
                    hqlCriteria.append(criteriaType, llave + " IS NOT NULL AND " + llave + " > 0 ");
                } else if (value.equals("<is-null-or-zero>")) {
                    hqlCriteria.append(criteriaType, llave + " IS NULL OR " + llave + " = 0 ");
                } else if (value.contains("<,>")) {
                    //busqueda para combos multiples
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, " exists (select 1 from " + entityAlias + ".");
                    } else {
                        hqlCriteria.append(criteriaType, " exists (select 1 from ");
                    }
                    if (llave.contains("@")) {
                        String[] llaveArr = llave.split("@");
                        int j = 0;
                        for (; j < llaveArr.length; j++) {
                            String tabla = llaveArr[j];
                            if (j > 0) {
                                hqlCriteria.append(criteriaType, " join p").append(j - 1).append(".");
                            }
                            hqlCriteria.append(criteriaType, tabla).append(" as p").append(j);
                        }
                        hqlCriteria.append(criteriaType, " where p").append(j - 1).append(".id in (:")
                                .append(bindKey.replaceAll("#", "").replaceAll("\\.", "").replaceAll("@", "")).append("))  ");
                    } else {
                        hqlCriteria.append(criteriaType, llave).append(" as p where p.id in (:")
                                .append(bindKey.replaceAll("#", "").replaceAll("\\.", "").replaceAll("@", "")).append("))  ");
                    }
                } else if (value.contains("<text-in>")) {
                    //busqueda para combos multiples
                    textIn = value.split("<text-in>");
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, " exists (select 1 from " + entityAlias + ".");
                    } else {
                        hqlCriteria.append(criteriaType, " exists (select 1 from ");
                    }
                    String key = llave;
                    if(llave.contains("@excludedFromKey")) {
                        key = llave.substring(0, llave.indexOf("@excludedFromKey"));
                    }
                    if(bindKey.contains("@excludedFromKey")) {
                        bindKey = bindKey.replaceAll("@excludedFromKey[0-9]*", "");
                    }
                    String keyIn = textIn[0];
                    if (asumerNumberAsDot) {
                        keyIn = keyIn.replace('#', '.');
                    }
                    hqlCriteria.append(criteriaType, key)
                            .append(" as p where p.")
                            .append(keyIn)
                            .append(" LIKE :")
                            .append(bindKey).append(textIn[0].replaceAll("#", "").replaceAll("\\.", "").replaceAll("@", ""))
                            .append(") ");

                    Loggable.getLogger(HQLHandler.class).trace("-------------------------->> goes to text-in! key: " + textIn[0] + " val : " + textIn[1]);
                } else if (value.contains("<owner>")) {
                    //busqueda para combos multiples
                    textIn = value.split("<owner>");
                    hqlCriteria.append(criteriaType, " (");
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, " exists (select 1 from " + entityAlias + ".");
                    } else {
                        hqlCriteria.append(criteriaType, " exists (select 1 from ");
                    }
                    hqlCriteria.append(criteriaType, llave).append(".users as ou where ou.user.description")
                            .append(" LIKE :")
                            .append(bindKey).append(textIn[0].replaceAll("#", "").replaceAll("\\.", "").replaceAll("@", ""))
                            .append(") ");
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, " or exists (select 1 from " + entityAlias + ".");
                    } else {
                        hqlCriteria.append(criteriaType, " or exists (select 1 from ");
                    }
                    hqlCriteria.append(criteriaType, llave).append(".positions as op join op.position.users u where u.description")
                            .append(" LIKE :")
                            .append(bindKey).append(textIn[0].replaceAll("#", "").replaceAll("\\.", "").replaceAll("@", ""))
                            .append(") ")
                            .append(" )");

                    Loggable.getLogger(HQLHandler.class).trace("-------------------------->> goes to owner-key! key: " + textIn[0]);
                } else if (value.contains("<long-in>")) {
                    textIn = value.split("<long-in>");
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, " exists (select 1 from " + entityAlias + ".");
                    } else {
                        hqlCriteria.append(criteriaType, " exists (select 1 from ");
                    }
                    String keyIn = textIn[0];
                    if (asumerNumberAsDot) {
                        keyIn = keyIn.replace('#', '.');
                    }
                    hqlCriteria.append(criteriaType, llave).append(" as p where p.")
                            .append(keyIn)
                            .append(" = :")
                            .append(bindKey).append(textIn[0].replaceAll("#", "").replaceAll("\\.", "").replaceAll("@", ""))
                            .append(") ");

                    Loggable.getLogger(HQLHandler.class).trace("-------------------------->> goes to long-in! key: " + textIn[0] + " val : " + textIn[1]);
                } else if (value.contains("<!>")) {
                    String[] li = value.split("<!>");
                    if (filter.isFilterInPatch()) {
                        hqlCriteria.append(criteriaType, Utilities.arrayToSelectInFullCondition(li, llave, true));
                    } else if (li.length > 1000) {
                        //Esta funcionalidad esta implementada en todos los LINKGRID
                        int len = li.length / 1000;
                        hqlCriteria.append(criteriaType, "(");
                        for (int j = 0; j < len; j++) {
                            if (j > 0) {
                                hqlCriteria.append(criteriaType, ") AND (")
                                        .append(llave).append(" not in (:")
                                        .append(bindKey).append(j).append(") ");
                            } else {
                                hqlCriteria.append(criteriaType, llave).append(" not in (:")
                                        .append(bindKey).append(") ");
                            }
                        }
                        hqlCriteria.append(criteriaType, ")");
                    } else {
                        hqlCriteria.append(criteriaType, llave).append(" not in (:")
                                .append(bindKey).append(") ");
                    }
                } else if (llave.contains("<!Composite>")) {
                    //Se agrega para filtrar por columnas de tipo composite
                    List<Map> criteriaList = (List<Map>) Utilities.parse(value);
                    llave = llave.replaceAll("<!Composite>", "");
                    hqlCriteria.append(criteriaType, " (");
                    Integer counter = 0;
                    for (Iterator<Map> itCl = criteriaList.iterator(); itCl.hasNext();) {
                        Map<String, Object> criteria = itCl.next();
                        hqlCriteria.append(criteriaType, " (");
                        for (Iterator<Map.Entry<String, Object>> it = criteria.entrySet().iterator(); it.hasNext();) {
                            Map.Entry<String, Object> propertyColumn = it.next();
                            String key = llave + "." + propertyColumn.getKey();
                            hqlCriteria.append(criteriaType, key).append(" <> :").append(key.replaceAll("\\.", "")).append(counter);
                            if (it.hasNext()) {
                                hqlCriteria.append(criteriaType, " OR ");
                            }
                        }
                        hqlCriteria.append(criteriaType, " )");
                        if (itCl.hasNext()) {
                            hqlCriteria.append(criteriaType, " AND ");
                        }
                        counter++;
                    }
                    hqlCriteria.append(criteriaType, " ) ");
                } else if (value.contains("<:>")) {
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, " " + entityAlias + ".");
                    }
                    //parece ser funcionalidad no implementada del gridComponent
                    hqlCriteria.append(criteriaType, llave).append(" in (:")
                            .append(bindKey).append(") "); 
                } else if (llave.equals("<condition>") || llave.equals("<filtered-entity>") || llave.equals("<linked-dialog-grid>")) {
                    //http://172.20.1.89/Bnext/QMS_3.0.0/pulls/2300
                    LOGGER.error("La llave " + llave + " ya no es soportada en este metodo, ahora se maneja en IGridFilter.get");
                } else if (like) {
                    //busquedas de texto
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, entityAlias + ".");
                    } else {
                        hqlCriteria.append(criteriaType, "");
                    }
                    String key = llave;
                    if (bindKey.contains("@excludedFromKeyNotLike")) {
                        bindKey = bindKey.replaceAll("@excludedFromKeyNotLike", "");
                    }
                    if (bindKey.contains("gridDataTypeString")) {
                        bindKey = bindKey.replaceAll("gridDataTypeString", "");
                    }
                    if (llave.contains("@gridDataTypeString")) {
                        llave = llave.replaceAll("@gridDataTypeString", "");
                    }
                    if (key.contains("@gridDataTypeString")) {
                        key = key.replaceAll("@gridDataTypeString", "");
                    }
                    if (llave.contains("@excludedFromKeyNotLike")) {
                        key = llave.substring(0, llave.indexOf("@excludedFromKeyNotLike"));
                        hqlCriteria.append(criteriaType, key).append(" NOT LIKE ").append(":").append(bindKey).append(" ");
                    } else {
                        hqlCriteria.append(criteriaType, key).append(" LIKE ").append(":").append(bindKey).append(" ");
                    }
                } else if (llave.equals("<or>")) {
                    hqlCriteria.append(criteriaType, ")) OR (").append(value).append(") AND ((");
                } else {
                    //igualdad
                    if (filter.isAsumeAlias()) {
                        hqlCriteria.append(criteriaType, " " + entityAlias + ".");
                    }
                    String key = llave;
                    if (bindKey.contains("@excludedFromKeyNotLike")) {
                        bindKey = bindKey.replaceAll("@excludedFromKeyNotLike", "");
                    }
                    if (bindKey.contains("gridDataTypeString")) {
                        bindKey = bindKey.replaceAll("gridDataTypeString", "");
                    }
                    if (llave.contains("@gridDataTypeString")) {
                        llave = llave.replaceAll("@gridDataTypeString", "");
                    }
                    if (key.contains("@gridDataTypeString")) {
                        key = key.replaceAll("@gridDataTypeString", "");
                    }
                    if (llave.contains("@excludedFromKeyNotLike")) {
                        key = llave.substring(0, llave.indexOf("@excludedFromKeyNotLike"));
                        hqlCriteria.append(criteriaType, key).append(" != ").append(":").append(bindKey).append(" ");
                    } else {
                        hqlCriteria.append(criteriaType, key).append(" = ").append(":").append(bindKey).append(" ");
                    }
                }
            }
           hqlCriteria.append(criteriaType, "))");     
        }
        Map<String, String> m = new HashMap<>();
        m.put(QueryHandler.CRITERIA_WHERE, hqlCriteria.getCriteria(QueryHandler.CRITERIA_WHERE));
        m.put(QueryHandler.CRITERIA_HAVING, hqlCriteria.getCriteria(QueryHandler.CRITERIA_HAVING));
        return m;
    }

    public static String getSafeParameterName(String name) {
        name = name.replaceAll("\\[|]|\\+|:|,|\\s+|=| |'|\\.|#|@|-", "").replaceAll("\\(|\\)", "_");
        if (name.length() > 50) {
            name = name.substring(0, 50);
        }
        return name;
    }

    private static String parseCriteriaType(String key) {
        if (key.matches("(max\\s*\\(|count\\s*\\(|SUM\\s*\\(|sum\\s*\\(|min\\s*\\(|avg\\s*\\().+")) {
            // ToDo: Modificar la consulta where cuando se agrega having
            return QueryHandler.CRITERIA_HAVING;
        }
        return QueryHandler.CRITERIA_WHERE;
    }

    private static Boolean singleIterateParamsCriteria(String key, String value) {
        return value.contains("<,>")
                || key.equals("<condition>")
                || key.equals("<filtered-entity>")
                || key.equals("<linked-dialog-grid>")
                || key.equals("<or>")
                || key.contains("<!Composite>");
    }

    public static void bindParamsCriteria(IParameterHandler q, IGridFilter filter, boolean like) {
        bindParamsCriteria(q, filter, like, null);
    }

    public static String bindParamsCriteria(@Nullable IParameterHandler query, IGridFilter filter, boolean like, String HQL) {
        Set<Map.Entry<String, String>> keys;
        Iterator<Map.Entry<String, String>> it;
        Map.Entry<String, String> r;
        String key, localKey;
        String bindValue = "", bindValue1;
        BindHandler q = new BindHandler(query, HQL);
        if (filter.getRawCriteria() != null) {
            Set<String> keysParams = filter.getRawCriteria().keySet();
            if (query == null) {
                keysParams.forEach(k -> LOGGER.error("No se puede hacer el bind de la clave {} sin un query for {}", k, HQL));
            } else {
                keysParams.forEach(k -> query.setParameter(k, filter.getRawCriteria().get(k)));
            }
        }
        if (filter.getCriteria() != null) {
            String typeValue = "";
            Set<Map.Entry<String, String>> keysC;
            Iterator<Map.Entry<String, String>> itC;
            Map.Entry<String, String> rC;
            keysC = filter.getCriteria().entrySet();
            itC = keysC.iterator();
            while (itC.hasNext()) {
                rC = itC.next();
                bindValue1 = getValueCriteria(rC.getValue());
                typeValue = getTypeCriteria(rC.getValue());
                key = rC.getKey()
                        .replaceAll("@excludedFromKeyNotLike", "")
                        .replaceAll("@excludedFromKey[0-9]*", "")
                        .replaceAll("@gridDataTypeString[0-9]*", "");
                localKey = key = getSafeParameterName(key);
                String[] searchedValue = singleIterateParamsCriteria(key, bindValue1) ? new String[]{bindValue1} : bindValue1.split(",");
                for (int i = 0; i < searchedValue.length; i++) {
                    bindValue = searchedValue[i].replace("\u2063", ",").trim();
                    if (isNonBindableKey(key) || isNonBindableValue(bindValue)) {
                        continue;
                    }
                    if (i > 0) {
                        key = localKey + i;
                    }
                    if (bindValue.contains("<,>")) {
                        handleBindComma(q, key, bindValue);
                    } else if (bindValue.contains("<!>")) {
                        handleBindExclamationMark(q, key, bindValue, filter);
                    } else if (bindValue.contains("<:>")) {
                        handleBindColon(q, key, bindValue);
                    } else if (key.contains("<!Composite>")) {
                        handleBindComposite(q, key, bindValue);
                    } else if (bindValue.contains("<long-in>")) {
                        handleBindLongIn(q, key, bindValue);
                    } else if (rC.getKey().contains("@gridDataTypeString")) {
                        handleBind(q, key, bindValue, "string");
                    } else {
                        handleBind(q, key, bindValue, typeValue);
                    }
                }
            }
        }

        if (filter.getLikeCriteria() != null) {
            keys = filter.getLikeCriteria().entrySet();
            it = keys.iterator();
            String bVal = "";
            while (it.hasNext()) {
                r = it.next();
                bindValue1 = r.getValue();
                key = r.getKey()
                        .replaceAll("@excludedFromKeyNotLike", "")
                        .replaceAll("@gridDataTypeString[0-9]*", "")
                        .replaceAll("@excludedFromKey[0-9]*", "");
                localKey = key = getSafeParameterName(key);
                String[] searchedValue = singleIterateParamsCriteria(key, bindValue1)
                        ? new String[]{bindValue1} : bindValue1.split(",");
                for (int i = 0; i < searchedValue.length; i++) {
                    if (i > 0) {
                        bindValue = bVal + searchedValue[i].trim();
                    } else {
                        bindValue = searchedValue[i].trim();
                        bVal = bindValue.substring(0, bindValue.indexOf(">") + 1);
                    }
                    if (isNonBindableKey(key) || isNonBindableValue(bindValue)) {
                        continue;
                    }
                    if (i > 0) {
                        key = localKey + i;
                    }
                    if (bindValue.contains("<,>")) {
                        handleBindComma(q, key, bindValue);
                    } else if (bindValue.contains("<!>")) {
                        handleBindExclamationMark(q, key, bindValue, filter);
                    } else if (bindValue.contains("<:>")) {
                        handleBindColon(q, key, bindValue);
                    } else if (bindValue.contains("<text-in>")) {
                        handleBindTextIn(q, key, bindValue);
                    } else if (bindValue.contains("<owner>")) {
                        handleBindOwner(q, key, bindValue);
                    } else {
                        handleBindLike(q, filter, key, bindValue);
                    }
                }
            }

        }

        if (filter.getLowerLimit() != null) {
            keys = filter.getLowerLimit().entrySet();
            it = keys.iterator();
            while (it.hasNext()) {
                r = it.next();
                key = r.getKey()
                        .replaceAll("@excludedFromKeyNotLike", "")
                        .replaceAll("@gridDataTypeString[0-9]*", "")
                        .replaceAll("@excludedFromKey[0-9]*", "") + "L";
                key = getSafeParameterName(key);
                bindValue = r.getValue();
                if (key.isEmpty() || isNonBindableValue(bindValue)) {
                    //Empty
                } else if (bindValue.contains("<date>")) {
                    handleBindLowerDate(q, key, bindValue);
                } else if (bindValue.contains("<timestamp>")) {
                    handleBindTimestamp(q, key, bindValue);
                } else if (bindValue.contains("<time>")) {
                    handleBindTime(q, key, bindValue);
                } else {
                    handleBind(q, key, bindValue, "string");
                }
            }
        }

        if (filter.getUpperLimit() != null) {
            keys = filter.getUpperLimit().entrySet();
            it = keys.iterator();
            while (it.hasNext()) {
                r = it.next();
                key = r.getKey() + "U";
                key = getSafeParameterName(key);
                bindValue = r.getValue();
                if (key.isEmpty() || isNonBindableValue(bindValue)) {
                    //Empty
                } else if (bindValue.contains("<date>")) {
                    handleBindUpperDate(q, key, bindValue);
                } else if (bindValue.contains("<timestamp>")) {
                    handleBindTimestamp(q, key, bindValue);
                } else if (bindValue.contains("<time>")) {
                    handleBindTime(q, key, bindValue);
                } else {
                    handleBind(q, key, bindValue, "string");
                }
            }
        }
        return q.getHqlFilter();
    }

    private static String getTypeCriteria(Object value) {
        if (value instanceof HashMap) {
            return (String) (((HashMap) value).get("type"));
        }
        return "";
    }

    private static void handleBindColon(BindHandler q, String key, String bindValue) {
        try {
            //requiere probar para los combos multiples con negacion
            q.setParameterStrList(
                key,
                Utilities.convertStringArraytoLongArray(bindValue.split("<:>")),
                sanatizeStringParameter(Utilities.convertStrArrayToString(bindValue.split("<:>"))),
                LONG_TYPE
            );
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
    }
    private static void handleBindComma(BindHandler q, String key, String bindValue) {
        try {
            //requiere probar para los combos multiples
            q.setParameterStrList(
                key,
                Utilities.convertStringArraytoLongArray(bindValue.split("<,>")),
                sanatizeStringParameter(Utilities.convertStrArrayToString(bindValue.split("<,>"))),
                LONG_TYPE
            );
        } catch (Exception ex) {
            stackTraceHandle(ex);
        }
    }

    private static void handleBindComposite(BindHandler q, String key, String bindValue) {
        //Se agrega para filtrar por columnas de tipo composite 
        List<Map> criteriaList = (List<Map>) Utilities.parse(bindValue);
        key = key.replaceAll("<!Composite>", "");
        Integer counter = 0;
        for (Map<String, Object> criteria : criteriaList) {
            for (Map.Entry<String, Object> propertyColumn : criteria.entrySet()) {
                String property = key + propertyColumn.getKey() + counter;
                q.setParameter(property, Long.parseLong(propertyColumn.getValue().toString()), sanatizeStringParameter(propertyColumn.getValue().toString()));
            }
            counter++;
        }
    }

    private static void handleBindLongIn(BindHandler q, String key, String bindValue) {
        //busqueda para combos multiples
        String[] textIn = bindValue.split("<long-in>");
        q.setParameter(
                (key + textIn[0]).replaceAll("#", "").replaceAll("\\.", ""),
                Long.parseLong(textIn[1].replace("L", "")),
                sanatizeStringParameter(textIn[1].replace("L", ""))
        );
    }

    private static void handleBindTextIn(BindHandler q, String key, String bindValue) {
        //busqueda para combos multiples
        String[] textIn = bindValue.split("<text-in>");
        textIn[1] = parseWildcards(textIn[1]);
        q.setParameter(
                (key + textIn[0])
                        .replaceAll("#", "")
                        .replaceAll("\\.", "")
                        .replaceAll("@excludedFromKeyNotLike", "")
                        .replaceAll("@gridDataTypeString[0-9]*", "")
                        .replaceAll("@excludedFromKey[0-9]*", "")
                        .replaceAll("@", ""),
                "%" + textIn[1] + "%",
                "'%" + sanatizeStringParameter(textIn[1]) + "%'"
        );
    }

    private static void handleBindOwner(BindHandler q, String key, String bindValue) {
        //busqueda para combos multiples
        String[] textIn = bindValue.split("<owner>");
        textIn[1] = parseWildcards(textIn[1]);
        q.setParameter(
                (key + textIn[0]).replaceAll("#", "").replaceAll("\\.", "").replaceAll("@", ""),
                "%" + textIn[1] + "%",
                "'%" + sanatizeStringParameter(textIn[1]) + "%'"
        );
    }

    private static void handleBindLike(BindHandler q, IGridFilter filter, String key, String bindValue) {
        Loggable.getLogger(HQLHandler.class).trace(key + ":" + bindValue);
        bindValue = parseWildcards(bindValue);
        if (filter.isLegacyMode()) {
            q.setParameter(key, "%" + bindValue + "%", "'%" + sanatizeStringParameter(bindValue) + "%'");
        } else {
            q.setParameter(key, bindValue, "'" + sanatizeStringParameter(bindValue) + "'");
        }
    }

    private static String parseWildcards(String s) {
        return s.replaceAll("\\?", "_").replaceAll("\\*", "%");
    }

    private static void handleBindLowerDate(BindHandler q, String key, String bindValue) {
        try {
            Date date = FORMAT_DTE.parse(bindValue.replace("<date>", ""));
            date.setHours(0);
            date.setMinutes(0);
            date.setSeconds(0);
            if(!GenericDAOImpl.IS_SQL_SERVER) {
                throw new QMSException("El filtro de fecha no funciona en la base de datos actual, agregar comaptibilidad.");
            }
            q.setTimestamp(key, date, "CONVERT(datetime, '" + Utilities.formatDateBy(date, "yyyy-MM-dd HH:mm:ss") + "', 120)");
        } catch (ParseException | QMSException e) {
            stackTraceHandle(e);
        }
    }

    private static void handleBindUpperDate(BindHandler q, String key, String bindValue) {
        try {
            Date date = FORMAT_DTE.parse(bindValue.replace("<date>", ""));
            date.setHours(23);
            date.setMinutes(59);
            date.setSeconds(59);
            if(!GenericDAOImpl.IS_SQL_SERVER) {
                throw new QMSException("El filtro de fecha no funciona en la base de datos actual, agregar comaptibilidad.");
            }
            q.setTimestamp(key, date, "CONVERT(datetime, '" + Utilities.formatDateBy(date, "yyyy-MM-dd HH:mm:ss") + "', 120)");
        } catch (ParseException | QMSException e) {
            stackTraceHandle(e);
        }
    }

    private static void handleBindTimestamp(BindHandler q, String key, String bindValue) {
        try {
            Date date = FORMAT_TMSTP.parse(bindValue.replace("<timestamp>", ""));
            if(!GenericDAOImpl.IS_SQL_SERVER) {
                throw new QMSException("El filtro de fecha no funciona en la base de datos actual, agregar comaptibilidad.");
            }
            q.setTimestamp(key, date, "CONVERT(datetime, '" + Utilities.formatDateBy(date, "yyyy-MM-dd HH:mm:ss") + "', 120)");
        } catch (ParseException | QMSException e) {
            stackTraceHandle(e);
        }
    }

    private static void handleBindTime(BindHandler q, String key, String bindValue) {
        try {
            /*
             * @ToDo: Utilizar BETWEEN en el HQL_findByQuery para cuando el campo en la Base de Datos
             *        sea tipo TIME. Y asi evitar la necesidad de crear un campo de tipo
             *        DATETIME en casos que solamente se requiere guardar la Hora
             **/
            if(!GenericDAOImpl.IS_SQL_SERVER) {
                throw new QMSException("El filtro de fecha no funciona en la base de datos actual, agregar comaptibilidad.");
            }
            Date time = FORMAT_TMSTP.parse("01/01/1900 " + bindValue.replace("<time>", ""));
            Loggable.getLogger(HQLHandler.class).trace("Time: " + time);
            q.setTimestamp(key, time, "CONVERT(datetime, '1900-01-01 " + Utilities.formatDateBy(time, "HH:mm:ss") + "', 120)");
        } catch (ParseException | QMSException e) {
            stackTraceHandle(e);
        }
    }

    public static String sanatizeStringParameter(final String parameter) {
        return parameter.replaceAll("'", "''");        
    }

    private static void handleBind(BindHandler q, String key, String bindValue, String typeValue) {
        if ("string".equals(typeValue)) {
            q.setParameter(key, bindValue, "'" + sanatizeStringParameter(bindValue) + "'");
        } else {
            try {
                if (bindValue.matches("^[+-]?([0-9]*)?\\.?[0-9]+[L]$")) {
                    q.setParameter(key, Long.parseLong(bindValue.replace("L", "")), bindValue.replace("L", ""));
                } else if (bindValue.matches("^[+-]?([0-9]*)?\\.?[0-9]+[D]$")) {
                    q.setParameter(key, Double.parseDouble(bindValue.replace("D", "")), bindValue.replace("D", ""));
                } else if (bindValue.matches("^true|false$")) {
                    q.setBoolean(key, bindValue.equals("true"), bindValue.equals("true") ? "1" : "0");
                } else if (bindValue.matches("^[+-]?([0-9]*)?\\.?[0-9]+[S]$")) {
                    q.setParameter(key, Short.parseShort(bindValue.replace("S", "")), bindValue.replace("S", ""));
                } else if (Utilities.isInteger(bindValue)) {
                    q.setParameter(key, Integer.parseInt(bindValue), bindValue);
                } else {
                    q.setParameter(key, bindValue, "'" + sanatizeStringParameter(bindValue) + "'");
                }
            } catch (NumberFormatException | HibernateException e) {
                Loggable.getLogger(HQLHandler.class).error("key {}, value {} ",key, bindValue);
                q.setParameter(key, bindValue, "'" + sanatizeStringParameter(bindValue) + "'");
            }
        }
    }

    private static void handleBindExclamationMark(BindHandler q, String key, String bindValue, IGridFilter filter) {
        if(filter.isFilterInPatch()) {
            //do nothing, el parche concatena los valores desde el momento de mapear el WHERE
        } else {
            try {
                //requiere probar para los combos multiples con negacion
            String[] li = sanatizeStringParameter(bindValue).split("<!>");
                if(li.length > 1000) {
                    int len = li.length / 1000;
                    List<String> lisli = Arrays.asList(li);
                    for (int j = 0; j < len; j++) {
                        if(j > 0) {
                            q.setParameterStrList(
                                key + j,
                                Utilities.convertStringListToLongArray(lisli.subList(j * 1000, (j * 1000) + 1000)),
                                Utilities.convertStrListToString(lisli.subList(j * 1000, (j * 1000) + 1000)),
                                LONG_TYPE
                            );
                        } else {
                            q.setParameterStrList(
                                key,
                                Utilities.convertStringListToLongArray(lisli.subList(0, 1000)),
                                Utilities.convertStrListToString(lisli.subList(0, 1000)),
                                LONG_TYPE
                            );
                        }
                    }
                } else {
                    try {
                        q.setParameterStrList(
                            key,
                            Utilities.convertStringArraytoLongArray(li),
                            Utilities.convertStrArrayToString(li),
                            LONG_TYPE
                        );
                    } catch(NumberFormatException e) {
                        q.setParameterStrList(
                            key,
                            li,
                            "'" + Utilities.convertStrArrayToString(li, "-,-").replaceAll("-,-", "','") + "'",
                            STRING_TYPE
                        );
                    }
                }
            } catch (Exception ex) {
                stackTraceHandle(ex);
            }
        }
    }

    private static boolean isNonBindableValue(String bindValue) {
        return bindValue.isEmpty() ||
                bindValue.equals("<zero>") ||
                bindValue.contains("<not-null>") ||
                bindValue.contains("<is-null>") ||
                bindValue.contains("<not-empty>") ||
                bindValue.equals("<not-zero>") || 
                bindValue.equals("<not-null-and-not-zero>") ||
                bindValue.equals("<is-null-or-zero>");
    }

    private static boolean isNonBindableKey(String key) {
        return key.isEmpty() || key.equals("<condition>") || key.equals("<filtered-entity>") || key.equals("<linked-dialog-grid>") || key.equals("<or>");
    }

    private static void stackTraceHandle(Throwable e) {
        stackTraceHandle("-- catched by genericDaoImpl --", e);
    }
    private static void stackTraceHandle(String msg, Throwable e) {
        Loggable.getLogger(HQLHandler.class).error("Framework.DAO.GenericDAOImpl.stackTraceHandle() ... " + msg, e);
    }
}
