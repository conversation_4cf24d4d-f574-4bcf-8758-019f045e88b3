/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.util;

import bnext.exception.InvalidCipherDecryption;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.springframework.http.ResponseEntity;
import qms.framework.dto.ReportDTO;
import qms.framework.util.DataSourceCredentialError;

/**
 *
 * <AUTHOR>
 */
public abstract class ReportBaseController extends Loggable {
        
    public abstract GridInfo<Map<String, Object>> reports(GridFilter filter);
    
    public abstract ResponseEntity<ReportDTO> reportColumn(Long reportId, String masterId, String reportCode) throws SQLException, DataSourceCredentialError, InvalidCipherDecryption;
    
    public abstract ResponseEntity<GridInfo<Map<String, Object>>> report(
            GridFilterByReportProcessingAccess filter,
            Long queryId,
            Date start,
            Date end
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException;
    
    public abstract GridInfo<Map<String, Object>> hierarchLevelRows(GridFilter filter, Long queryId,  Integer level) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException;
    
    public abstract List<Map<String, Object>> reportListAvailables(String module);
}
