package qms.util;

import Framework.Config.SortedPagedFilter;
import qms.util.interfaces.IGridFilter;

/**
 *
 * <AUTHOR>
 */
public class GridFilter extends SortedPagedFilter {

    private final boolean skipCount;

    public GridFilter() {
        this.skipCount = false;
    }

    public GridFilter(boolean skipCount) {
        this.skipCount = skipCount;
    }

    public GridFilter(Integer pageSize) {
        this.pageSize = pageSize;
        this.skipCount = false;
    }

    public GridFilter(IGridFilter filter, Integer pageSize) {
        super(
            filter.getGridShowMore(),
            filter.getDirection(),
            filter.getField(),
            filter.isEnableStatistics(),
            filter.getStatisticsFields(),
            filter.getLikeOrSentence(),
            filter.getCondition(),
            filter.getFilteredEntity(),
            filter.getLinkedDialogGrid(),
            filter.getDynamicFieldCriteria(),
            filter.getPage(),
            filter.getGridId(),
            filter.isEmptyCriterias(),
            filter.getSearchStringChainIds()
        );
        this.pageSize = pageSize;
        this.skipCount = false;
    }

    @Override
    public boolean isLegacyMode() {
        return false;
    }

    @Override
    public boolean skipCount() {
        return this.skipCount;
    }
}
