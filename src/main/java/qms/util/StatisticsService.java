package qms.util;

import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import mx.bnext.core.util.StringLongPair;
import org.hibernate.CacheMode;
import org.hibernate.Session;
import org.hibernate.query.Query;
import qms.util.interfaces.IGridFilter;
import qms.util.interfaces.IParameterHandler;

/**
 *
 * <AUTHOR>
 */
public class StatisticsService {

    private StatisticsService() {}

    public static Map<String, List<StringLongPair>> getSimpleStatistics(
            Session s,
            Class<?> clazz,
            IGridFilter filter,
            Boolean like,
            Boolean cacheable,
            Integer queryTimeoutSeconds
    ) throws QMSException, Exception {
        return getSimpleStatistics(s, clazz.getName(), filter, like, cacheable, queryTimeoutSeconds);
    }

    private static Map<String,List<StringLongPair>> getSimpleStatistics(
            Session s, 
            String table,
            IGridFilter filter,
            Boolean like,
            Boolean cacheable, 
            Integer queryTimeoutSeconds
    ) throws QMSException, Exception {
        final Map<String,List<StringLongPair>> result = new HashMap<>(filter.getStatisticsFields().size());
        for(String field : filter.getStatisticsFields()){
            final String hql = HQLHandler.makeGroupedCountHQL(table, field, filter, like);
            final Query q = s.createQuery(hql);
            configureCache(q, cacheable);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            final IParameterHandler parameterHandler = new QueryParameterHandler(q);
            HQLHandler.bindParamsCriteria(parameterHandler, filter, true);
            final List<Object[]> list = q.list();
            result.put(field, toStringLongPair(list));
        }
        return result;
    }
    
    private static CacheMode getCacheMode(boolean cacheable) {
        final boolean mode = getCacheable(cacheable);
        if (mode) {
            return CacheMode.NORMAL;
        } else {
            return CacheMode.IGNORE;
        }
    }
    
    private static boolean getCacheable(boolean cacheable) {
        final Settings settings = Utilities.getSettingsWithoutReload();
        if (settings == null) {
            return false;
        }
        if (settings.getUseSecondLevelCache() != null && settings.getUseSecondLevelCache() == 1) {
            return cacheable;
        } else {
            return false;
        }
    }
    
    private static void configureCache(final Query q, final boolean cacheable) {
        q.setCacheable(getCacheable(cacheable));
        q.setCacheMode(getCacheMode(cacheable));
    }
    
    public static Map<String,List<StringLongPair>> getHQLStatistics(
            Session s,
            String HQL,
            IGridFilter filter,
            Boolean like,
            Boolean cacheable, 
            Integer queryTimeoutSeconds
    ) throws QMSException, Exception {

        final Map<String,List<StringLongPair>> result = new HashMap<>(filter.getStatisticsFields().size());
        for (String field : filter.getStatisticsFields()) {
            final String hql = HQLHandler.makeGroupedCountHQL(HQL, field, filter, like);
            final Query q = s.createQuery(hql);
            final IParameterHandler parameterHandler = new QueryParameterHandler(q);
            HQLHandler.bindParamsCriteria(parameterHandler, filter, true);
            configureCache(q, cacheable);
            if (queryTimeoutSeconds != null && queryTimeoutSeconds > 0) {
                q.setTimeout(queryTimeoutSeconds);
            }
            final List<Object[]> list = q.list();
            result.put(field, toStringLongPair(list));
        }
        return result;
    }

    private static List<StringLongPair> toStringLongPair(List<Object[]> list) {
        return list.stream()
                .map((Object[] o) -> new StringLongPair(String.valueOf(o[1]), (Long) o[0]))
                .collect(Collectors.toList());
    }
    
}
