package qms.util;

import Framework.Config.Utilities;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import qms.framework.util.AboutAppUtils;

public class FilePathUtils extends Loggable {

    private static final Logger LOGGER = getLogger(FilePathUtils.class);

    public static String loadFile(final String fileName) throws IOException, QMSException {
        InputStream fileStream = null;
        try {
            if (fileName.startsWith("/")) {
                fileStream = FilePathUtils.class.getResourceAsStream(fileName);
            } else {
                fileStream = FilePathUtils.class.getResourceAsStream("/" + fileName);
            }
            String content = "";
            if (fileStream != null) {
                content = FileUtils.inputStreamToString(fileStream);
            }
            if (content == null || content.isEmpty()) {
                fileStream = ClassLoader.getSystemResourceAsStream(fileName);
                if (fileStream != null) {
                    content = FileUtils.inputStreamToString(fileStream);
                }
            }
            if (fileStream != null) {
                fileStream.close();
            }
            if (content == null || content.isEmpty()) {
                // TODO: En ocasiones la compilación aun no ha copiados el archivo `avatar-initials.svg`.
                //   La solución podría ser hacer algo similar a lo que se hace
                //   en FilePathUtils.getFilePathFromProyectFiles
                LOGGER.error("Missing file {}", fileName);
                return null;
            }
            return content;
        } catch (Exception e) {
            if (fileStream != null) {
                fileStream.close();
            }
            LOGGER.error("Failed to read {}", fileName, e);
            throw new QMSException("Failed to read " + fileName, e);
        }
    }

    public String getFilePathFromProyectFiles(String filePath, String fileName) {
        // Ambiente de producción
        String path = Utilities.getRealPath() + filePath + fileName;
        path = path.replace("\\", "/");
        LOGGER.debug("Trying to find report path (1): {}", path);
        File r = new File(path);
        if (r.exists()) {
            return path;
        } else {
            LOGGER.debug("Invalid report path found (1): {}", path);
        }
        // Ambiente de desarrollo
        String appFolder = StringUtils.substringAfterLast(Utilities.getRealPath().replace("\\src\\main\\webapp\\", ""), "\\");
        path = Utilities.getRealPath().replace("\\src\\main\\webapp", "")
                + "target/" + appFolder
                + "/" + filePath + fileName;
        path = path.replace("\\", "/");
        r = new File(path);
        LOGGER.debug("Trying to find report path (2):{}", path);
        if (r.exists()) {
            return path;
        } else {
            LOGGER.debug("Invalid report path found (2): {}", path);
        }
        // Ambiente de desarrollo 2
        appFolder = Utilities.getSettings().getFolderName();

        path = Utilities.getRealPath().replace("\\src\\main\\webapp", "")
                + "target/" + appFolder
                + "/" + filePath + fileName;
        path = path.replace("\\", "/");
        r = new File(path);
        LOGGER.debug("Trying to find report path (3): {}", path);
        if (r.exists()) {
            return path;
        } else {
            LOGGER.debug("Invalid report path found (3): {}", path);
        }
        LOGGER.debug("Report path couln't be found:");
        return null;
    }
}