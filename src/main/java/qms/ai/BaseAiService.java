package qms.ai;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.document.Document;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.transformer.KeywordMetadataEnricher;
import org.springframework.ai.transformer.SummaryMetadataEnricher;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * No usar directamente solo se usa para initialization de servicios de AI de soporte
 */
@Component
public class BaseAiService {

    private static final Logger log = LoggerFactory.getLogger(BaseAiService.class);
    public static final String METADATA_CONTEXT_KEY = "context";
    private final TokenTextSplitter splitter;
    private final KeywordMetadataEnricher metadataEnricher;
    private final SummaryMetadataEnricher summaryEnricher;

    public BaseAiService(@Qualifier("summaryDelegatingChatModel") ChatModel chatModel) {
        this.splitter = new TokenTextSplitter(1500, 500, 20, 3000, true);
        this.summaryEnricher = new SummaryMetadataEnricher(chatModel, List.of(
                SummaryMetadataEnricher.SummaryType.PREVIOUS,
                SummaryMetadataEnricher.SummaryType.CURRENT,
                SummaryMetadataEnricher.SummaryType.NEXT),
                """
			Aquí esta el contenido de la sección:
			{context_str}

			Realiza un resumen de entidades y temas clave.

			Resumen:""", MetadataMode.ALL
        );
        this.metadataEnricher = new KeywordMetadataEnricher(chatModel, 5);
    }

    public List<Document> split(List<Document> documents) {
        if (log.isDebugEnabled()) {
            log.debug("Splitting {} documents", documents.size());
        }
        List<Document> documentList = splitter.split(documents);
        if (log.isDebugEnabled()) {
            log.debug("Split into {} documents", documentList.size());
        }
        return documentList;
    }

    public List<Document> summary(List<Document> documentList) {
        if (log.isDebugEnabled()) {
            log.debug("Summarizing {} documents", documentList.size());
        }
        return summaryEnricher.apply(documentList);
    }

    public List<Document> keywordMetadata(List<Document> documentList) {
        if (log.isDebugEnabled()) {
            log.debug("Adding keyword metadata to {} documents", documentList.size());
        }
        return metadataEnricher.apply(documentList);
    }
}
