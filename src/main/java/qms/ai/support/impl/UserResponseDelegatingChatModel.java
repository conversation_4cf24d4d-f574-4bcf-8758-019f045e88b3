package qms.ai.support.impl;

import DPMS.Mapping.Settings;
import com.google.common.base.Function;
import java.util.Objects;
import org.springframework.stereotype.Service;
import qms.ai.AiProvider;
import qms.ai.support.AbstractDelegatingChatModel;

@Service
public class UserResponseDelegatingChatModel extends AbstractDelegatingChatModel {
    @Override
    public Task getTask() {
        return Task.USER_RESPONSE;
    }

    @Override
    protected Function<Settings, AiProvider> getAiProviderFunction() {
        return Settings::getChatResponseProvider;
    }

    @Override
    protected Function<Settings, String> getModelFunction() {
        return Settings::getChatResponseModel;
    }

}
