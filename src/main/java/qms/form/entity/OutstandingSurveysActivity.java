package qms.form.entity;

import Framework.Config.CompositeStandardEntity;
import ape.pending.core.SoftBaseAPE;
import ape.pending.core.SoftEntityId;
import ape.pending.core.StrongEntityId;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import qms.activity.entity.Activity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "outstanding_surveys_activity")
public class OutstandingSurveysActivity extends CompositeStandardEntity<OutstandingSurveysActivityPK>
        implements SoftBaseAPE<OutstandingSurveys, Activity>, Serializable, ILinkedComposityGrid<OutstandingSurveysActivityPK> {

    private static final long serialVersionUID = 1L;

    private OutstandingSurveysActivityPK id;
    private Long outstandingSurveysId;
    private Long fieldId;
    private Long activityId;

    public OutstandingSurveysActivity() {
    }

    public OutstandingSurveysActivity(OutstandingSurveysActivityPK id) {
        this.id = id;
    }

    public OutstandingSurveysActivity(Long outstandingSurveysId, Long fieldId, Long activityId) {
        this.id = new OutstandingSurveysActivityPK(outstandingSurveysId, fieldId, activityId);
    }

    @Override
    public OutstandingSurveysActivityPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public OutstandingSurveysActivityPK getId() {
        return id;
    }

    @Override
    public void setId(OutstandingSurveysActivityPK id) {
        this.id = id;
    }

    @Basic(optional = false)
    @StrongEntityId
    @Column(name = "outstanding_surveys_id", insertable = false, updatable = false)
    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long auditIndividualId) {
        this.outstandingSurveysId = auditIndividualId;
    }

    @SoftEntityId
    @Basic(optional = false)
    @Column(name = "activity_id", insertable = false, updatable = false)
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name = "field_id", insertable = false, updatable = false)
    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OutstandingSurveysActivity other = (OutstandingSurveysActivity) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.form.entity.OutstandingSurveysActivity{" + "id=" + id + '}';
    }

}
