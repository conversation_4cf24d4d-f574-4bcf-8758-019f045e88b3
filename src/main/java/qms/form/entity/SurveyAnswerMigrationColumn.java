/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.form.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.core.LocalizedField;
import qms.framework.util.CacheConstants;
import qms.util.CodePrefix;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Entity
@Cacheable
@Cache(region = CacheConstants.PRINTING_FORMAT, usage = CacheConcurrencyStrategy.READ_WRITE)
@CodePrefix("SCOL-")
@Table(name = "survey_answer_migration_column")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class SurveyAnswerMigrationColumn extends StandardEntity<SurveyAnswerMigrationColumn> implements IAuditableEntity {
    
    private String code;                                                        // <-- nombre 
    private String description;                                                 // <-- nombre
    private Integer status = 1;
    private Integer deleted = 0;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private Long surveyAnswerMigrationId;
    private String surveyAnswerColumnName;
    private String migrationColumnName;
    
    private SurveyAnswerMigration migration;

    // Constructors, getters, and setters

    public SurveyAnswerMigrationColumn() {}

    @Id
    @Basic(optional = false)   
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Column(name = "survey_answer_migration_column_id", nullable = false, precision = 19, scale = 0)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @LocalizedField
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Column(name = "survey_answer_migration_id", nullable = false)
    public Long getSurveyAnswerMigrationId() {
        return surveyAnswerMigrationId;
    }

    public void setSurveyAnswerMigrationId(Long surveyAnswerMigrationId) {
        this.surveyAnswerMigrationId = surveyAnswerMigrationId;
    }

    @Column(name = "survey_answer_column_name", length = 128)
    public String getSurveyAnswerColumnName() {
        return surveyAnswerColumnName;
    }

    public void setSurveyAnswerColumnName(String surveyAnswerColumnName) {
        this.surveyAnswerColumnName = surveyAnswerColumnName;
    }

    /**
     * 
     * @return 
     */
    @Column(name = "migration_column_name", nullable = false, length = 128)
    public String getMigrationColumnName() {
        return migrationColumnName;
    }

    public void setMigrationColumnName(String migrationColumnName) {
        this.migrationColumnName = migrationColumnName;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "survey_answer_migration_id", insertable = false ,updatable = false)
    public SurveyAnswerMigration getMigration() {
        return migration;
    }

    public void setMigration(SurveyAnswerMigration migration) {
        this.migration = migration;
    }
    
    
}
