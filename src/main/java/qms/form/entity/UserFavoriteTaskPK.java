package qms.form.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

@Embeddable
public class UserFavoriteTaskPK implements Serializable {

    private Long favoriteTaskId;
    private Long userId;

    public UserFavoriteTaskPK(Long favoriteTaskId, Long userId) {
        this.favoriteTaskId = favoriteTaskId;
        this.userId = userId;
    }

    public UserFavoriteTaskPK() {
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "favorite_task_id")
    public Long getFavoriteTaskId() {
        return favoriteTaskId;
    }

    public void setFavoriteTaskId(Long favoriteTaskId) {
        this.favoriteTaskId = favoriteTaskId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.favoriteTaskId);
        hash = 97 * hash + Objects.hashCode(this.userId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final UserFavoriteTaskPK other = (UserFavoriteTaskPK) obj;
        if (!Objects.equals(this.favoriteTaskId, other.favoriteTaskId)) {
            return false;
        }
        return Objects.equals(this.userId, other.userId);
    }

    @Override
    public String toString() {
        return "UserFavoriteTaskPK{" + "favoriteTaskId=" + favoriteTaskId + ", userId=" + userId + '}';
    }


}