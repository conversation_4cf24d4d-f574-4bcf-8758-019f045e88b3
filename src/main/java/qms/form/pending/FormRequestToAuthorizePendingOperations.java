package qms.form.pending;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.dto.ColumnDTO;
import ape.pending.entities.PendingRecord;
import qms.escalation.dto.IEscalableDTO;
import qms.form.entity.FormRequest;
import qms.form.util.FormRequestType;
import qms.workflow.entity.SequenceDetailFormRequest;
import qms.workflow.util.SequenceDetailStatus;
import qms.workflow.util.WorkflowRequestStatus;

import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public abstract class FormRequestToAuthorizePendingOperations extends FormRequestPendingOperations {

    protected static final String ALIAS_REQUEST = "request";

    private final String toAuthorizeRequestQuery = ""
        + " FROM " + SequenceDetailFormRequest.class.getCanonicalName() + " sequence"
        + " JOIN sequence.request request"
        + " JOIN request.outstandingSurvey o "
        + " WHERE request.deleted = 0"
            + " AND sequence.status = " + SequenceDetailStatus.PENDING.getValue()
            + " AND request.formRequestType = " + getFormRequestType().getValue()
            + " AND request.status = " + WorkflowRequestStatus.APROVING.getValue()
            + " AND o.archived = 0 "
            + " AND o.deleted = 0 "
      ;

    private static final String BY_AUTHORIZER = " AND sequence.userId = :userId ";
    
    public abstract FormRequestType getFormRequestType();
    
    public FormRequestToAuthorizePendingOperations(IUntypedDAO dao, APE ape) {
        super(dao);
        setBaseAlias(ALIAS_REQUEST);
        setQuery(toAuthorizeRequestQuery);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("sequence.userId");
        setPendingType(getType(ape));
        setModuleKey(MODULE);
        setBase(FormRequest.class);
        setOwnerFieldFilter(BY_AUTHORIZER);
        setSummaryTemplate("${type}: ${code}, ${description}");
        setBaseSummaryFields(
                new ColumnDTO("requestType", "type", FormRequestType.getValues()),
                new ColumnDTO("code", "code"),
                new ColumnDTO("description", "description"));
        setOperationType(ApeOperationType.STRONG);
    }

    @Override
    public void handleResultRecords(Set<IEscalableDTO> records) {}

}