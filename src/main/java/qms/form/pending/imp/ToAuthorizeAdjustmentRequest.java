package qms.form.pending.imp;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import qms.form.pending.FormRequestToAuthorizePendingOperations;
import qms.form.util.FormRequestType;

/**
 *
 * <AUTHOR>
 */
public class ToAuthorizeAdjustmentRequest extends FormRequestToAuthorizePendingOperations {
    
    /**
     * El query está en la herencia de `FormRequestToAuthorizePendingOperations` 
     * 
     * Dicho query se utiliza el mismo al menos en 3 pendientes: 
     *      - ToAuthorizeReopenRequest
     *      - ToAuthorizeAdjustmentRequest
     *      - ToAuthorizeCancelRequest
     * 
     * @param dao 
     */
    public ToAuthorizeAdjustmentRequest(IUntypedDAO dao) {
        super(dao, APE.FORM_TO_AUTHORIZE_ADJUSTMENT_REQUEST);
        setDependencies(
                ToVerifyFormAdjustmentRequest.class
        );
    }

    @Override
    public FormRequestType getFormRequestType() {
        return FormRequestType.ADJUSTMENT;
    }


}