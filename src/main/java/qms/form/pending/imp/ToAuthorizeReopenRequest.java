package qms.form.pending.imp;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import qms.form.pending.FormRequestToAuthorizePendingOperations;
import qms.form.util.FormRequestType;

/**
 *
 * <AUTHOR>
 */
public class ToAuthorizeReopenRequest extends FormRequestToAuthorizePendingOperations {
    
    
    /**
     * El query está en la herencia de `FormRequestToAuthorizePendingOperations` 
     * 
     * Dicho query se utiliza el mismo al menos en 3 pendientes: 
     *      - ToAuthorizeReopenRequest
     *      - ToAuthorizeAdjustmentRequest
     *      - ToAuthorizeCancelRequest
     * 
     * @param dao 
     */
    public ToAuthorizeReopenRequest(IUntypedDAO dao) {
        super(dao, APE.FORM_TO_AUTHORIZE_REOPEN_REQUEST);
        setDependencies(
            ToVerifyFormReopenRequest.class
        );
    }

    @Override
    public FormRequestType getFormRequestType() {
        return FormRequestType.REOPEN;
    }


}