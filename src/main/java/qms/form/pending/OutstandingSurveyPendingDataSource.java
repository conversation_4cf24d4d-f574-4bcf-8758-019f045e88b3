package qms.form.pending;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.CommonAction;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.BaseAPE;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.IPendingDto;
import ape.pending.util.PendingUtil;
import ape.pending.util.RecordRowsCache;
import ape.pending.util.SqlQueryParser;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import qms.access.dto.ILoggedUser;
import qms.document.util.RequestType;
import qms.form.dto.FormPendingDto;
import qms.form.entity.FormProgressState;
import qms.form.entity.FormRequest;
import qms.form.pending.imp.ToVerifyFormAdjustmentRequest;
import qms.form.pending.imp.ToVerifyFormCancelRequest;
import qms.form.pending.imp.ToVerifyFormReopenRequest;
import qms.form.util.FormRequestType;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
public class OutstandingSurveyPendingDataSource {
    
    private OutstandingSurveyPendingDataSource() {
        // S1118 - SonarRule: Utility classes should not have public constructors
    }
    
    private static final Map<APE, Function<IUntypedDAO, IPendingOperation>> pendings = ImmutableMap.of(
        APE.FORM_TO_VERIFY_ADJUSTMENT_REQUEST, ToVerifyFormAdjustmentRequest::new,
        APE.FORM_TO_VERIFY_REOPEN_REQUEST, ToVerifyFormReopenRequest::new,
        APE.FORM_TO_VERIFY_CANCEL_REQUEST, ToVerifyFormCancelRequest::new
    );
    
    public static IPendingOperation[] getPendingOperations(IPendingRecordDAO dao) {
        final Collection<Function<IUntypedDAO, IPendingOperation>> values = pendings.values();
        return values.stream().map(f -> f.apply(dao)).collect(Collectors.toList()).toArray(new IPendingOperation[values.size()]);
    }
    
    public static Class<? extends BaseAPE> getBaseEntity() {
        return OutstandingSurveys.class;
    }
   
    public static Collection<IPendingDto> getPendingRecords(IPendingRecordDAO dao) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final IPendingOperation[] operations = OutstandingSurveyPendingDataSource.getPendingOperations(dao);
        final Integer countPendings = dao.getPendingCount(loggedUser, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return Utilities.EMPTY_SET;
        }
        LinkedHashSet<String> commonActions =
            PendingUtil.commonActions(
                FormRequestAction.AUTHORIZE, 
                FormRequestAction.REJECT, 
                CommonAction.OPEN_DETAIL, 
                CommonAction.HIDE // <--- Agregar funcionalidad general de pendientes (APE): REASIGNAR.
            );
        Collection<IPendingDto> result = new ArrayList<>(countPendings);
        // FormRequest: REOPEN!
        result.addAll(getPendings(FormRequestType.REOPEN, dao, loggedUser, commonActions));
        // FormRequest: ADJUSTMENT!
        result.addAll(getPendings(FormRequestType.ADJUSTMENT, dao, loggedUser, commonActions));
        
        result.addAll(getPendings(FormRequestType.CANCEL, dao, loggedUser, commonActions));
        return result;
    }
    
    private static Collection<IPendingDto> getPendings(FormRequestType formRequestType, IPendingRecordDAO dao, ILoggedUser loggedUser, LinkedHashSet<String> actions) {
        final Class<? extends BaseAPE> base = OutstandingSurveyPendingDataSource.getBaseEntity();
        IPendingOperation operation = null;
        String type = null;
        switch (formRequestType) {
            case ADJUSTMENT:
                type = "formAdjustRequest";
                operation = pendings.get(APE.FORM_TO_VERIFY_ADJUSTMENT_REQUEST).apply(dao);
                break;
            case CANCEL:
                type = "formCancelRequest";
                operation = pendings.get(APE.FORM_TO_VERIFY_CANCEL_REQUEST).apply(dao);
                break;
            case REOPEN:
                type = "formReopenRequest";
                operation = pendings.get(APE.FORM_TO_VERIFY_REOPEN_REQUEST).apply(dao);
                break;
        }
        if (operation == null) {
            return Utilities.EMPTY_LIST;
        }
        final Collection<IPendingDto> results = dao.getPendingRecords(
                loggedUser,
                base,
                "entity",
                Arrays.asList(BaseSummaryFieldBuilder.columns(
                    "code = requestCode",
                    "description = requestReason@fr",
                    "requestId = requestId@entity",
                    "id = formRequestId@fr",
                    "outstandingSurveysId = outstandingSurveysId@fr",
                    "status = requestStatus",
                    "description = businessUnitDepartmentName@budentity",
                    "description = progressStateDescription@progressState",
                    "stage = outsandingSurveyStage@entity"
                )), " "
                // JOINS
                    + SqlQueryParser.LEFT_JOIN + FormRequest.class.getCanonicalName() + " fr "
                        + " ON fr.id = entity." + type
                    + SqlQueryParser.LEFT_JOIN + FormProgressState.class.getCanonicalName() + " progressState"
                        + " ON progressState.id = entity.progressStateId"
                    ,
                RecordRowsCache.ENABLED,
                new IPendingOperation[] {operation}
            ).stream().map((Map<String, Object> pending) -> {
                APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
                if (ape == null) {
                    throw new RuntimeException(" "
                        + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                        + " value must exist as a 'code' inside APE.java."
                    );
                }
                FormPendingDto value = new FormPendingDto();
                // valores de la solicitud
                value.setRequestType(
                    RequestType.fromValue((Integer) pending.get("requestType"))
                );
                // valores requeridos para pendientes
                value.setPendingRecordId((Long) pending.get("pendingRecordId"));
                value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
                value.setRecordId((Long) pending.get("recordId"));
                value.setRequestId((Long) pending.get("requestId"));
                value.setFormRequestId((Long) pending.get("formRequestId"));
                value.setSurveyId((Long) pending.get("surveyId"));
                value.setDocumentId((Long) pending.get("documentId"));
                value.setDocumentMasterId((String) pending.get("documentMasterId"));
                value.setOutstandingSurveysId((Long) pending.get("outstandingSurveysId"));
                value.setDto(FormPendingDto.class.getCanonicalName());
                value.setCode((String) pending.get("requestCode"));
                value.setBase((String) pending.get("base"));
                value.setPendingTypeCode(ape.getCode());
                value.setModule(mx.bnext.access.Module.FORMULARIE);
                value.setDescription((String) pending.get("requestReason"));
                value.setCommitmentDate((Date) pending.get("commitmentDate"));
                value.setCommitmentStartDate(value.getCommitmentDate());
                value.setCommitmentEndDate(value.getCommitmentDate());
                value.setProgressStateDescription((String) pending.get("progressStateDescription"));
                value.setOutsandingSurveyStage((String) pending.get("outsandingSurveyStage"));
                value.setRequestStatus((Integer) pending.get("requestStatus"));
                value.setAvailableActions(actions);
                return value;
            }).collect(Collectors.toList());
        final FormPendingAnswersHandler answersHandler = new FormPendingAnswersHandler();
        return answersHandler.loadAnswers(results, loggedUser);
    }
}
