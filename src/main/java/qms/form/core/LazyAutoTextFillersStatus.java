package qms.form.core;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.util.Date;
import java.util.List;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.form.dto.FieldAutoTextDTO;
import qms.form.dto.FillerStatusDTO;
import qms.form.util.ILazyAutoTextHelper;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;

public class LazyAutoTextFillersStatus implements ILazyAutoTextHelper<List<FillerStatusDTO>> {
    
    private static final Logger LOGGER = Loggable.getLogger(LazyAutoTextFillersStatus.class);

    private List<FillerStatusDTO> lazyValue;

    private final Long outstandingSurveyId;
    private final Long documentId;
    private final Long requestId;

    public LazyAutoTextFillersStatus(Long outstandingSurveyId, Long documentId, Long requestId) {
        this.outstandingSurveyId = outstandingSurveyId;
        this.documentId = documentId;
        this.requestId = requestId;
    }

    @Override
    public void checkLazyValue() {
        if (this.lazyValue == null) {
            this.buildLazyValue();
        }
    }

    @Override
    public void buildLazyValue() {
        final ElapsedDataDTO fillerStart = MeasureTime.start(getClass());
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        List<FillerStatusDTO> result = this.outstandingSurveyId == null ? Utilities.EMPTY_LIST : dao.HQL_findByQuery(""
                + " SELECT new " + FillerStatusDTO.class.getCanonicalName() + "("
                    + " q.fieldId AS fieldId,"
                    + " q.filledAutorizationPoolIndex AS fillOutIndex, "
                    + " q.fillOutDate AS fillOutDate, "
                    + " c.dteFechaInicio AS fillStartDate "
                + " )"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " JOIN c.preguntasRespondidas q"
                + " WHERE c.id = :outstandingSurveyId",
                "outstandingSurveyId", outstandingSurveyId
        );
        MeasureTime.stop(
                fillerStart,
                "Loading fillers status for outstanding " + outstandingSurveyId
                + " and document " + documentId
                + " and request " + requestId
        );
        this.lazyValue = result;
    }

    @Override
    public List<FillerStatusDTO> getLazyValue() {
        this.checkLazyValue();
        return lazyValue;
    }
    
    public String getFillOutTimestamp(
            final FieldAutoTextDTO field,
            final String autoTextKey
    ) {
        final Date fillOutDate = getFillOutFieldDate(field.getFilledPoolIndex(), field.getFieldId());
        if (fillOutDate != null) {
            return Utilities.formatDateBy(fillOutDate, "dd-MMMM-yyyy HH:mm:ss");
        }
        LOGGER.trace("Requesting key:[{}] not available because Field is not fill out.", autoTextKey);
        return null;
    }

    private Date getFillOutFieldDate(final Long fillOutIndex, final Long fieldId) {
        this.checkLazyValue();
        if (fillOutIndex == null || fieldId == null) {
            return null;
        }
        for (final FillerStatusDTO fillDetail : this.getLazyValue()) {
            if (fillOutIndex.equals(fillDetail.getFillOutIndex())
                    && fieldId.equals(fillDetail.getFieldId())) {
                return fillDetail.getFillOutDate();
            }
        }
        return null;
    }

}
