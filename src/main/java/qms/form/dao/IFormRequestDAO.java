/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.form.dao;

import Framework.DAO.IUntypedDAO;
import Framework.DAO.Implementation;
import Framework.DAO.SaveHandle;
import java.io.IOException;
import java.util.List;
import javax.annotation.Nonnull;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.form.entity.FormRequest;
import qms.util.QMSException;
import qms.workflow.util.IWorkflowBaseDAO;
import qms.workflow.util.WorkflowAuthRole;

/**
 *
 * <AUTHOR>
 */
@Implementation(name = "FormRequestDAO")
public interface IFormRequestDAO extends IUntypedDAO, IWorkflowBaseDAO {

    ResponseEntity<SaveHandle> workflowFormRequestAdjustApproved(
            FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    ResponseEntity<SaveHandle> workflowFormRequestAdjustRejected(
            FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    ResponseEntity<SaveHandle> verifyFormRequestAdjustApproved(
            FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    ResponseEntity<SaveHandle> verifyFormRequestAdjustRejected(
            FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    ResponseEntity<SaveHandle> verifyFormRequestReopenApproved(
            FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    ResponseEntity<SaveHandle> verifyFormRequestReopenRejected(
            FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;
    
    WorkflowAuthRole getRequestAuthRole(FormRequest request, ILoggedUser loggedUser);
    
    ResponseEntity<SaveHandle> verifyFormRequestCancelApproved(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;
    
    ResponseEntity<SaveHandle> verifyFormRequestCancelRejected(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    @Nonnull
    List<Long> cancelFormRequestByOutstandingSurveysId(
            Long outstandingSurveysId,
            String reason,
            Boolean sendMail,
            ILoggedUser loggedUser
    );

}
