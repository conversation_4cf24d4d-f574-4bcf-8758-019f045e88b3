/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.form.dao;

import DPMS.Mapping.AutorizationPool;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.WorkflowPool;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.SaveHandle;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.document.listeners.OnCreatedNextFill;
import qms.form.entity.FormRequest;
import qms.form.listeners.OnFormAdjustmentRejected;
import qms.form.listeners.OnFormAdjustmentStart;
import qms.form.listeners.OnFormCancelApproved;
import qms.form.listeners.OnFormCancelRejected;
import qms.form.listeners.OnFormReopenRejected;
import qms.form.listeners.OnFormRequestFillCancelled;
import qms.form.pending.imp.ToAuthorizeAdjustmentRequest;
import qms.form.pending.imp.ToVerifyFormAdjustmentRequest;
import qms.form.util.FormRequestType;
import qms.framework.entity.Owner;
import qms.util.FormUtil;
import qms.util.QMSException;
import qms.workflow.bean.WorkflowBaseDAO;
import qms.workflow.util.IAutorizationPoolDetails;
import qms.workflow.util.IWorkflowBaseDAO;
import qms.workflow.util.IWorkflowDAO;
import qms.workflow.util.IWorkflowRequest;
import qms.workflow.util.RequestAuthorizeResponse;
import qms.workflow.util.WorkflowAuthRole;
import qms.workflow.util.WorkflowRequestStatus;
import qms.workflow.util.WorkflowSupported;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "FormRequestDAO")
@Scope(value = "singleton")
public class FormRequestDAO extends WorkflowBaseDAO<FormRequest, Long> implements IFormRequestDAO {
 
    private static final List<Integer> VALID_EDIT_ADMIN_STATUS = Arrays.asList(
            WorkflowRequestStatus.VERIFING.getValue(),
            WorkflowRequestStatus.APROVING.getValue()
    );

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AutorizationPool getPool(IWorkflowRequest request, ILoggedUser loggedUser) {
        AutorizationPool authorizationPool = request.getAutorizationPool(); 
        if (authorizationPool == null) {
            authorizationPool = new AutorizationPool(-1L);
            authorizationPool.setStatus(AutorizationPool.STATUS_ACTIVE);
            authorizationPool.setCreationDate(new Date());
            authorizationPool.setFormRequestId(request.getId());
        }
        return this.makePersistent(authorizationPool, loggedUser.getId());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public WorkflowSupported getWorkflowSettings() {
        return WorkflowSupported.FORM_REQUEST;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getEntityIdFromAutorizationPoolDetails(IAutorizationPoolDetails apd) {
        return apd.getFormRequestId();
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AutorizationPoolDetails getAutorizationPoolDetailsInstance(IWorkflowRequest entity, WorkflowPool workPool, Owner owner, AutorizationPool pool)  {
        AutorizationPoolDetails detail = new AutorizationPoolDetails(-1L);
        detail.setIndice(workPool.getIndex());
        detail.setOwnerId(owner.getId());
        detail.setAutorizationPool(pool);
        detail.setFormRequestId(entity.getId());
        detail.setFinishDate(null);
        detail.setUserId(null);
        detail.setDocumentId(null);
        return detail;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnFormCancelApproved
    public ResponseEntity<SaveHandle> verifyFormRequestCancelApproved(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException {
        request.setStatus(WorkflowRequestStatus.CLOSED.getValue());
        ResponseEntity<SaveHandle> response = this.verifyFormRequest(request, authRole, reason, loggedUser);
        if (response.getStatusCode().is2xxSuccessful()){
            this.HQL_updateByQuery("UPDATE " + FormRequest.class.getName()
                            + " SET status = " + WorkflowRequestStatus.CLOSED.getValue()
                            + " WHERE id = (SELECT formReopenRequestId FROM "+OutstandingSurveys.class.getName()+" WHERE id = :outstandingSurveyId) "
                            + " OR id = (SELECT formAdjustRequestId FROM "+OutstandingSurveys.class.getName()+" WHERE id = :outstandingSurveyId) ",
                    ImmutableMap.of("outstandingSurveyId", request.getOutstandingSurveysId()));
        }
        return response;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnFormCancelRejected
    public ResponseEntity<SaveHandle> verifyFormRequestCancelRejected(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException {
        request.setStatus(WorkflowRequestStatus.CANCELED.getValue());
        final ResponseEntity<SaveHandle> r = this.verifyFormRequest(request, authRole, reason, loggedUser);
        if (setOutsdandingSurveysInProgressFromCancel(request)) {
            return new ResponseEntity(r, HttpStatus.OK);
        }
        return new ResponseEntity(r, HttpStatus.CONFLICT);
    }

    private boolean setOutsdandingSurveysInProgressFromCancel(FormRequest request) {
        return HQL_updateByQuery(""
                        + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " o "
                        + " SET "
                        + " o.formCancelRequestId = null "
                        + ", o.status = :status "
                        + " WHERE o.id = :outstandingSurveysId", ImmutableMap.of(
                        "status", OutstandingSurveys.STATUS.IN_PROGRESS.getValue(),
                        "outstandingSurveysId", request.getOutstandingSurveysId()
                )
        ) > 0;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<SaveHandle> verifyFormRequestReopenApproved(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException {
        request.setStatus(WorkflowRequestStatus.CLOSED.getValue());
        return this.verifyFormRequest(request, authRole, reason, loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnFormReopenRejected
    public ResponseEntity<SaveHandle> verifyFormRequestReopenRejected(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException {
        request.setStatus(WorkflowRequestStatus.CANCELED.getValue());
        return this.verifyFormRequest(request, authRole, reason, loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnFormAdjustmentStart
    public ResponseEntity<SaveHandle> verifyFormRequestAdjustApproved(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) throws IOException, QMSException {
        final Long outstandingSurveysId = request.getOutstandingSurveysId();
        final Long rejectedAutorizationPoolIndex = request.getRejectedAutorizationPoolIndex().longValue();
        final boolean isSignatureRejectionApprovalRequired = FormUtil.isSignatureRejectionApprovalRequired(outstandingSurveysId, rejectedAutorizationPoolIndex);
        if (isSignatureRejectionApprovalRequired) {
            request.setStatus(WorkflowRequestStatus.APROVING.getValue());
        } else {
            request.setStatus(WorkflowRequestStatus.CLOSED.getValue());
        }
        return this.verifyFormRequest(request, authRole, reason, loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnFormAdjustmentRejected
    public ResponseEntity<SaveHandle> verifyFormRequestAdjustRejected(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) {
        final SaveHandle r = SaveHandle.getSuccessInstance(request);
        r.setLogicResponse(RequestAuthorizeResponse.REJECTED.getValue());
        if (
            setFormRequestCanceled(request)
            && setOutsdandingSurveysInProgressFromAdjustment(request)
        ) {
            return new ResponseEntity(r, HttpStatus.OK);
        }
        return new ResponseEntity(r, HttpStatus.CONFLICT);
    }
    
    private ResponseEntity<SaveHandle> verifyFormRequest(
            FormRequest request,
            WorkflowAuthRole authRole, 
            String reason,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        request.setVerifyComment(reason);
        final SaveHandle<FormRequest> result = workflowDao.workflowRequestStart(
            this.getAspectJAutoProxy(),
                request,
                loggedUser.getId(),
                false, 
                authRole, 
                loggedUser
        );
        RequestAuthorizeResponse response = RequestAuthorizeResponse.fromValue(result.getLogicResponse());
        switch (response) {
            case VERIFY_REPEATED_DATA:      // <-- información invalida en la solicitud
                return new ResponseEntity(result, HttpStatus.CONFLICT);
            case VERIFY_DEPARTMENT_MANAGER: // <-- reenviar al nuevo responsable
                return new ResponseEntity(result, HttpStatus.RESET_CONTENT);
            default:
                return new ResponseEntity(result, HttpStatus.OK);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean onCreateNextPoolDetails(IWorkflowRequest entity, WorkflowPool currentPool, ILoggedUser loggedUser) {
        FormRequest request = (FormRequest) entity;
        FormRequestType type = request.getRequestType();
        switch (type) {
            case ADJUSTMENT:
                // ToDo: Enviar el mail correspondiente para autorizar
                break;
            case CANCEL:
                // ToDo: Enviar el mail correspondiente para autorizar
                break;
            case REOPEN:
                // ToDo: Enviar el mail correspondiente para reabrir
                break;
        }
        return getAspectJAutoProxy().createNextFill(this, entity, loggedUser, currentPool) != null;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean onBusinessUnitDepartmentChange(IWorkflowRequest entity, Long userId, ILoggedUser loggedUser) {
        /**
         * Enviar mails, ya que en caso de que cambie el departamneto, tipicamente deberá recalcularse el pendiente y su responsable. 
         */
        FormRequest request = (FormRequest) entity;
        request.setStatus(WorkflowRequestStatus.VERIFING.getValue());
        makePersistent(request, loggedUser.getId());
        return true;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IFormRequestDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IFormRequestDAO.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest onWorkflowEnd(Long formRequestId, ILoggedUser loggedUser, Boolean readers) throws IOException, QMSException {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.DAO.HibernateDAO_Request @ finalizeRequest [requestId={}, loggedUser={}, readers={}]", new Object[] {
                formRequestId, loggedUser, readers
            });
        }
        Map<String, Object> r = HQL_findSimpleMap(""
            + " SELECT new map("
                + " r.autorizationPoolId AS autorizationPoolId "
                + ",r.outstandingSurveysId AS outstandingSurveysId "
                + ",r.formRequestType AS formRequestType "
                + ",r.rejectedAutorizationPoolIndex AS rejectedIndex "
                + ",r.rejectorAutorizationPoolIndex AS rejectorIndex "
                + ",r.description AS formReason "
                + ",r.status AS formRequestStatus "
                + ",o.requestId AS requestId "
            + " ) "
            + " FROM " + FormRequest.class.getCanonicalName() + " r "
            + " JOIN r.outstandingSurvey o "
            + " WHERE r.id = :formRequestId", "formRequestId", formRequestId
        );
        Integer rejectedIndex = (Integer) r.get("rejectedIndex");
        Integer rejectorIndex = (Integer) r.get("rejectorIndex");
        Integer formRequestStatus = (Integer) r.get("formRequestStatus");
        String formReason = (String) r.get("formReason");
        Long requestId = (Long) r.get("requestId");
        Long autorizationPoolId = (Long) r.get("autorizationPoolId");
        Long outstandingSurveysId = (Long) r.get("outstandingSurveysId");
        FormRequestType requestType = FormRequestType.fromValue((Integer) r.get("formRequestType"));
        if (autorizationPoolId != null) {
            // Se cierra el pool
            HQL_updateByQuery(""
                + " UPDATE " + AutorizationPool.class.getCanonicalName() + " a "
                + " SET a.status = :newStatus"
                + " WHERE a.id = :autorizationPoolId", ImmutableMap.of(
                    "newStatus", AutorizationPool.STATUS_CLOSED,
                    "autorizationPoolId", autorizationPoolId
                )
            );
        }
        if (WorkflowRequestStatus.APROVING.equals(formRequestStatus)) {
            // Se cierra la solicitud
            formRequestStatus = WorkflowRequestStatus.CLOSED.getValue();
            HQL_updateByQuery(""
                + " UPDATE " + FormRequest.class.getCanonicalName() + " r "
                + " SET r.status = :newStatus"
                + " WHERE r.id = :requestId", ImmutableMap.of(
                    "newStatus", formRequestStatus,
                    "requestId", formRequestId
                )
            );
        }
        IFormCaptureDAO dao = getBean(IFormCaptureDAO.class);
        switch (requestType) {
            case ADJUSTMENT:
                if (WorkflowRequestStatus.CLOSED.equals(formRequestStatus)) {
                    // AUTORIZADO: Se ejecuta proceso para retornar la sección
                    dao.adjustApproved(formReason, outstandingSurveysId, rejectedIndex, rejectorIndex, loggedUser);
                } else if (WorkflowRequestStatus.CANCELED.equals(formRequestStatus)) {
                    /**
                     * RECHAZADO: Se ejecuta proceso para devolver el pendiente al que solicitó el ajuste
                     * 
                     * >> Se ejecuta dentro de `setRequestReleasedOnWorkflowReject`
                     * 
                     * */
                }
                break;
            case CANCEL:
                // ToDo: Autorizar cancelar!!
                break;
            case REOPEN:
                if (WorkflowRequestStatus.CLOSED.equals(formRequestStatus)) {
                    // Se reabre el formulario
                    dao.reopenFillForm(outstandingSurveysId, requestId, loggedUser);
                }
                break;
            default:
        }
        return null;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<SaveHandle> workflowFormRequestAdjustApproved(FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser) throws IOException, QMSException {
        HQL_updateByQuery(""
            + " UPDATE " + FormRequest.class.getCanonicalName() + " r "
            + " SET r.status = :newStatus"
            + " WHERE r.id = :requestId", ImmutableMap.of(
                "newStatus", request.getStatus(),
                "requestId", request.getId()
            )
        );
        final GenericSaveHandle result = workflowDao.workflowRequestApprove(
            getAspectJAutoProxy(),
            request, 
            authRole,
            reason,
            loggedUser
        );
        final SaveHandle r = SaveHandle.getSuccessInstance(request);
        r.setSaveHandle(result);
        r.setLogicResponse(RequestAuthorizeResponse.AUTHORIZED.getValue());
        /**
         * En caso de autorizarse, se ejecutará `onWorkflowEnd`
         */
        if (Boolean.TRUE.equals(result.getJsonEntityData().get("authorized"))) {
            return new ResponseEntity(r, HttpStatus.OK);
        } else {
            return new ResponseEntity( r, HttpStatus.CONFLICT);
        }
    }

    @Override
    @OnCreatedNextFill
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest createNextFill(IWorkflowBaseDAO holder, IWorkflowRequest request, ILoggedUser loggedUser, WorkflowPool poolDetails) {
        return getBean(IWorkflowDAO.class).createNextPoolDetails(holder, request, poolDetails, loggedUser);
    }
    
    private boolean setOutsdandingSurveysInProgressFromAdjustment(FormRequest request) {
        return HQL_updateByQuery(""
            + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " o "
            + " SET "
                    + " o.formAdjustRequestId = null "
                    + ", o.surveyProgressStateReopenId = null "
                    + ",o.status = :status "
            + " WHERE o.id = :outstandingSurveysId", ImmutableMap.of(
                "status", OutstandingSurveys.STATUS.IN_PROGRESS.getValue(),
                "outstandingSurveysId", request.getOutstandingSurveysId()
            )
        ) > 0;
    }

    private boolean setFormRequestCanceled(FormRequest request) {
        final Integer status = cancelFormRequest(request.getId());
        return status != null;
    }

    @Nullable
    private Integer cancelFormRequest(final Long formRequestId) {
        final int status = WorkflowRequestStatus.CANCELED.getValue();
        final Integer result = HQL_updateByQuery(" "
            + " UPDATE " + FormRequest.class.getCanonicalName() + " r "
            + " SET r.status = :newStatus"
            + " WHERE r.id = :requestId", ImmutableMap.of(
                "newStatus", status,
                "requestId", formRequestId
            )
        );
        if (result != null && result > 0) {
            return status;
        } else {
            return null;
        }
    }

    @Nonnull
    @Override
    @OnFormRequestFillCancelled
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> cancelFormRequestByOutstandingSurveysId(
            final Long outstandingSurveysId,
            final String reason,
            final Boolean sendMail,
            final ILoggedUser loggedUser
    ) {
        final List<Long> formRequestIds = HQL_findByQuery(" "
            + " SELECT fr.id "
            + " FROM " + FormRequest.class.getCanonicalName() + " fr "
            + " WHERE fr.outstandingSurveysId = :outstandingSurveysId"
            + " AND fr.status IN ( "
                +  WorkflowRequestStatus.VERIFING.getValue()
                + ", " + WorkflowRequestStatus.RETURNED.getValue()
                + ", " + WorkflowRequestStatus.APROVING.getValue()
            + " )",
            ImmutableMap.of("outstandingSurveysId", outstandingSurveysId)
        );
        if (formRequestIds == null || formRequestIds.isEmpty()) {
            return new ArrayList<>(0);
        }
        final List<Long> savedFormRequestIds = new ArrayList<>(formRequestIds.size());
        for(final Long formRequestId : formRequestIds) {
            final Integer cancelled = cancelFormRequest(formRequestId);
            if (cancelled != null) {
                savedFormRequestIds.add(formRequestId);
            }
        }
        return savedFormRequestIds;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnFormAdjustmentRejected
    public ResponseEntity<SaveHandle> workflowFormRequestAdjustRejected(
        FormRequest request, WorkflowAuthRole authRole, String reason, ILoggedUser loggedUser
    ) {
        setFormRequestCanceled(request);
        final SaveHandle r = SaveHandle.getSuccessInstance(request);
        r.setLogicResponse(RequestAuthorizeResponse.REJECTED.getValue());
        if (!workflowDao.workflowRequestReject(
            getAspectJAutoProxy(),
            request, 
            authRole,
            reason,
            loggedUser
        )) {
            getLogger().warn("FormRequest might was corrupted, marked as rejected anyways");
        }
        if (workflowDao.isLastAutorizationPool(this.getAspectJAutoProxy(), request.getId())) {
            setOutsdandingSurveysInProgressFromAdjustment(request);
        }
        return new ResponseEntity(r, HttpStatus.OK);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setRequestReleasedOnWorkflowReject(Long formRequestId, WorkflowAuthRole authRole, ILoggedUser loggedUser) {
       /**
        * El propósito de este metodo es que el registro (Ej. `Document`, `OutstandingSurveys`, etc) se 
        * habiliten como si nunca se hubiese realizado la solicitud.
        */
        final IFormCaptureDAO dao = getBean(IFormCaptureDAO.class);
        final FormRequest formRequest = HQLT_findById(formRequestId);
        dao.adjustRejected(formRequest, authRole, formRequest.getDescription(), loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public WorkflowAuthRole getRequestAuthRole(final FormRequest request, final ILoggedUser loggedUser) {
        return getRequestAuthRole(request.getId(), request.getOutstandingSurveysId(), loggedUser);
    }
    
    private WorkflowAuthRole getRequestAuthRole(final Long formRequestId, final Long outstandingSurveysId, final ILoggedUser loggedUser) {
        final Long verificationPendingRecordId = new ToVerifyFormAdjustmentRequest(this).getPendingRecordIdFromRecordId(
            loggedUser, outstandingSurveysId
        );
        if (verificationPendingRecordId != null) {
            return WorkflowAuthRole.ASSIGNED;
        }
        final Long authorizationPendingRecordId = new ToAuthorizeAdjustmentRequest(this).getPendingRecordIdFromRecordId(
            loggedUser, formRequestId
        );
        if (authorizationPendingRecordId != null) {
            return WorkflowAuthRole.ASSIGNED;
        }
        return getAdminAuthRole(formRequestId, loggedUser);
    }
    
    private WorkflowAuthRole getAdminAuthRole(final Long formRequestId, final ILoggedUser user) {
        if (!user.isAdmin()) {
            return WorkflowAuthRole.NONE;
        }
        final Integer status = HQL_findSimpleInteger(""
            + " SELECT status "
            + " FROM " + FormRequest.class.getCanonicalName() + " c "
            + " WHERE c.id = :requestId",
                "requestId", formRequestId
        ); 
        if (VALID_EDIT_ADMIN_STATUS.contains(status)) {
            return WorkflowAuthRole.ADMIN;
        } else {
            return WorkflowAuthRole.NONE;
        }
    }
}
