package qms.form.util;

import DPMS.Mapping.Request;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import java.util.Objects;
import java.util.Set;
import javax.servlet.ServletContext;
import qms.access.dto.ILoggedUser;
import qms.framework.rest.SecurityRootUtils;

/**
 *
 * <AUTHOR>
 */
public abstract class FormSetup {

    public synchronized static void initialize(final ServletContext servletContext) {
        final ILoggedUser userAdmin = SecurityRootUtils.getFirstAdminDto();
        final SurveySetup setup = new SurveySetup();
        IUntypedDAO dao = Utilities.getUntypedDAO();
        Integer n = dao.HQL_updateByQuery("" +
                " UPDATE " + Request.class.getCanonicalName() +
                " SET isBusy = 0 " +
                " WHERE isBusy = 1"
        );
        setup.initializeSlimReports(servletContext, userAdmin);
        setup.initializeAnwers(servletContext, false, userAdmin);
        setup.initializeWorkflowPreviewData(servletContext, userAdmin);
        setup.initializeWorkflowFormRequestData(servletContext, userAdmin);
        setup.initializeSurveyDataAnswerColumns(servletContext, userAdmin);
        setup.initializeAnwersMigration(servletContext, false, userAdmin);
    }

    public synchronized static GenericSaveHandle customInitializeAnswers(
            final ServletContext servletContext,
            final Boolean forceReset, 
            final ILoggedUser userAdmin
    ) {
        final SurveySetup setup = new SurveySetup();
        GenericSaveHandle result = setup.initializeAnwers(servletContext, forceReset, userAdmin);
        if (!Objects.equals(result.getOperationEstatus(), 1)) {
            return result;
        }
        return result;
    }
    
    public synchronized static GenericSaveHandle customInitializeAnswers(
            final ServletContext servletContext,
            final Set<Long> surveyIds, 
            final ILoggedUser userAdmin
    ) {
        final SurveySetup setup = new SurveySetup();
        GenericSaveHandle result = setup.initializeAnwers(servletContext, surveyIds, userAdmin);
        return result;
    }

    public synchronized static GenericSaveHandle customInitializeAnswersMigration(final ServletContext servletContext, final Boolean forceReset, final ILoggedUser userAdmin) {
        final SurveySetup setup = new SurveySetup();
        return setup.initializeAnwersMigration(servletContext, forceReset, userAdmin);
    }
}
