package qms.form.mail;

import DPMS.Mapping.Request;
import DPMS.Mapping.WorkflowPool;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.Map;
import javax.annotation.Nonnull;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.document.mail.FormMailer;
import qms.document.mail.IFormRuntimeMailer;
import qms.form.dao.IFormCaptureDAO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.listeners.BnextMonitor;
import qms.framework.listeners.MailMonitor;
import qms.framework.mail.MailDTO;
import qms.framework.util.MeasureTime;

/**
 * ¡IMPORTANTE!
 *       Evitar agregar nuevos mails aquí, en su lugar. Usar directamente el monitor ya existente de `FormPendingMonitor`,
 *       lo anterior, ya que el orden de ejecución de los aspectos no está garantizado.
 *
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Aspect
public class FormMailMonitor extends MailMonitor {

    private static final Class<FormMailMonitor> CLAZZ = FormMailMonitor.class;

    @Override
    @Around(BnextMonitor.MAIL_DAEMON_IS_TRIGGERED)
    public Object onMailDaemonFired(ProceedingJoinPoint pjp) {
        ElapsedDataDTO tStart = MeasureTime.start(CLAZZ);
        IUntypedDAO dao = getUntypedDAO(pjp);
        FormMailer formMailer = new FormMailer(dao);
        Object result = handleMailDaemonFired(pjp, formMailer);
        if (isMailOff()) {
            return result;
        }
        MeasureTime.stop(tStart, "Elapsed time sending onMailDaemonFired's mail");
        return result;
    }

    @Around(value = "@annotation(qms.form.listeners.OnFormAdjustmentRequestDone) && args(reason, outstandingSurveysId, rejectedIndex, rejectorIndex, loggedUser)")
    public ResponseEntity onFormAdjustmentRequestDone(
            ProceedingJoinPoint pjp, String reason, Long outstandingSurveysId, Integer rejectedIndex, Integer rejectorIndex, ILoggedUser loggedUser
    ) throws Throwable {
        final IUntypedDAO dao = getUntypedDAO(pjp);
        final IFormRuntimeMailer mailer = new FormMailer(dao);
        MailDTO mail = null;
        if (!isMailOff()) {
            mail = mailer.getMailToOnFormAdjustmentRequestDone(outstandingSurveysId, loggedUser);
        }
        final ResponseEntity result = (ResponseEntity) pjp.proceed();
        if (isMailOff()) {
            return result;
        }
        if (mail != null && result != null && result.getStatusCode() == HttpStatus.OK) {
            mailer.onFormAdjustmentRequestDone(mail);
        }
        return result;
    }

    @Around(value = "@annotation(qms.form.listeners.OnFormReopenedRequest) && args(outstandingSurveysId, requestId, loggedUser)")
    public Object onFormReopenedRequest(
            ProceedingJoinPoint pjp, Long outstandingSurveysId, Long requestId, ILoggedUser loggedUser
    ) throws Throwable {
        IFormRuntimeMailer mailer = new FormMailer(getUntypedDAO(pjp));
        MailDTO mail = null;
        if (!isMailOff()) {
            mail = mailer.getMailToOnFormReopenedRequest(outstandingSurveysId, requestId, loggedUser);
        }
        final Boolean result = (Boolean) pjp.proceed();
        if (isMailOff()) {
            return result;
        }
        if (Boolean.TRUE.equals(result)) {
            mailer.onFormReopenedRequest(mail);
            sendNextFormFillerAfterReopened(requestId, loggedUser, mailer);
        }
        return result;
    }

    private void sendNextFormFillerAfterReopened(final Long requestId, final ILoggedUser loggedUser, IFormRuntimeMailer mailer) {
        IFormCaptureDAO formCaptureDAO = Utilities.getBean(IFormCaptureDAO.class);
        Map<String, Object> currentAuthPoolIndexes = formCaptureDAO.getCurrentAuthPoolSimpleData(requestId);
        if (!currentAuthPoolIndexes.isEmpty()) {
            Request request = (Request) formCaptureDAO.HQL_findSimpleObject("" +
                            "SELECT new " + Request.class.getCanonicalName() + " (" +
                            "r.id," +
                            " r.description," +
                            " r.outstandingSurveysId," +
                            " r.workflowId" +
                            ") FROM " + Request.class.getCanonicalName() + " r" +
                            " WHERE  r.id = :requestId ",
                    ImmutableMap.of("requestId", requestId));
            WorkflowPool workflowPool = (WorkflowPool) formCaptureDAO.HQL_findSimpleObject("" +
                            "SELECT" +
                            " wf " +
                            " FROM " + WorkflowPool.class.getCanonicalName() + " wf " +
                            "WHERE wf.workflowId = :workflowId AND " +
                            "wf.workflowPoolIndex = :currentPoolIndex",
                    ImmutableMap.of(
                            "workflowId", request.getWorkflowId(),
                            "currentPoolIndex", currentAuthPoolIndexes.get("currentAutorizationPoolIndex")
                    )
            );
            mailer.nextFormFiller(request, workflowPool, loggedUser);
        }
    }

    @Around(BnextMonitor.DAILY_FORM_MAIL_FIRED)
    public Object onDailyFormMailTaskFired(ProceedingJoinPoint pjp, @Nonnull ILoggedUser loggedUser) throws Throwable {
        Object result = null;
        if (pjp != null) {
            result = pjp.proceed();
        }
        return result;
    }
}
