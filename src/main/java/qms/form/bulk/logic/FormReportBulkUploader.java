package qms.form.bulk.logic;

import Framework.Config.Language;
import Framework.Config.Utilities;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.interfaces.ISurveyData;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import qms.access.dto.ILoggedUser;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.util.SurveyFieldAnswerType;
import qms.form.util.SurveyUtil;
import qms.framework.bulk.dto.IBulkLocalizedRow;
import qms.framework.bulk.entity.BulkLog;
import qms.framework.bulk.entity.BulkLogRegistry;
import qms.framework.bulk.entity.IBulkLog;
import qms.framework.bulk.imp.IReportBulkConfig;
import qms.framework.bulk.imp.ReportBulkConfig;
import qms.framework.bulk.imp.ReportBulkPlainRow;
import qms.framework.bulk.imp.ReportBulkSaveResultRow;
import qms.framework.bulk.imp.ReportBulkUploader;
import qms.framework.bulk.logic.BulkWriter;
import qms.framework.bulk.util.BuilkUploadType;
import qms.framework.dto.IReportColumnDTO;
import qms.framework.dto.ReportColumnDTO;
import qms.framework.entity.Report;
import qms.framework.entity.SlimReports;
import qms.framework.util.CacheRegion;
import qms.framework.util.ReportColumnType;
import qms.util.QMSException;

@Language(module = "qms.form.bulk.logic.FormReportBulkUploader")
public class FormReportBulkUploader extends ReportBulkUploader {

    private final FormReportBulkWriter writer;
    private final Map<String, SurveyFieldAnswerType> fields;
    private List<ReportColumnDTO> defaultHeaders;

    public FormReportBulkUploader(
            Locale locale,
            Long databaseQueryId,
            Long reportId,
            @Nonnull Long surveyId,
            String url,
            String entityName,
            String schema,
            String appName,
            String documentMasterId,
            String excelIdFields,
            Long slimReportId,
            String slimReportCode,
            ILoggedUser loggedUser)
            throws QMSException {
        super(
                Module.FORMULARIE,
                locale,
                documentMasterId,
                surveyId,
                entityName,
                schema,
                url,
                appName,
                new ReportBulkConfig(databaseQueryId, reportId, excelIdFields, slimReportId, slimReportCode),
                loggedUser
        );
        this.defaultHeaders = getDefaultHeaders(reportId, slimReportId);
        // El writer se crea al final, ya que requiere a `bulkUploader` esté completamente inicializado
        this.writer = new FormReportBulkWriter(this, getFileManager(), url, appName); // <-- Debe crearse al final
        // información para determinar tipos de campos
        this.fields = getInitializedTypes(surveyId);
        this.getHeaders().addAll(defaultHeaders);
    }

    public FormReportBulkUploader(
            Locale locale,
            Long reportId,
            @Nonnull Long surveyId,
            String url,
            String entityName,
            String schema,
            String appName,
            String documentMasterId,
            ILoggedUser loggedUser)
            throws QMSException {
        super(
                Module.FORMULARIE,
                locale,
                documentMasterId,
                surveyId,
                entityName,
                schema,
                url,
                appName,
                getConfig(documentMasterId, reportId),
                loggedUser
        );
        this.defaultHeaders = getDefaultHeaders(reportId, getSlimReportId());
        // El writer se crea al final, ya que requiere a `bulkUploader` esté completamente inicializado
        this.writer = new FormReportBulkWriter(this, getFileManager(), url, appName); // <-- Debe crearse al final
        // información para determinar tipos de campos
        this.fields = getInitializedTypes(surveyId);
        this.getHeaders().addAll(defaultHeaders);
    }

    private Map<String, SurveyFieldAnswerType> getInitializedTypes(Long surveyId) {
        final IFormCaptureDAO dao = Utilities.getBean(IFormCaptureDAO.class);
        // TO-DO: Llenar `fieldsSet` solo con los campos que se utlizan en lugar de todos (cambiar `null` por los ids)
        final Set<ISurveyData> fieldsSet = dao.getFreezedDatas(surveyId, false, false, null, 0);
        return fieldsSet.stream()
                .collect(Collectors.toMap(
                        field -> SurveyUtil.getGuessFieldName(surveyId, field),
                        field -> SurveyUtil.getSurveyFieldAnswerType(field, SurveyFieldAnswerType.ALPHA_NUMERIC),
                        (existing, replacement) -> existing
                ));
    }

    @Override
    public List<ReportColumnDTO> getDefaultFields(Long reportId, Long slimReportId) {
        // Se agrega el ID para identificar el registro en la tabla de carga masiva
        return ImmutableList.of(
                new ReportColumnDTO(reportId, slimReportId, "outstanding_surveys_id", "Internal ID", true, ReportColumnType.BIGINT, 0)
        );
    }

    @Override
    public List<IReportColumnDTO> getFields() {
        final List<IReportColumnDTO> fields = new ArrayList<>(super.getFields());
        if (defaultHeaders == null) {
            defaultHeaders = getDefaultHeaders(getReportId(), getSlimReportId());
        }
        if (defaultHeaders != null && !defaultHeaders.isEmpty()) {
            fields.addAll(defaultHeaders);
        }
        return fields;
    }

    private List<ReportColumnDTO> getDefaultHeaders(Long reportId, Long slimReportId) {
        if (defaultHeaders != null) {
            return defaultHeaders;
        }
        if (getSurveyId() == null) {
            return Collections.emptyList();
        }
        if (hasHeadersMatchingExcelIdFields(this.getExcelIdFieldList())) {
            return Collections.emptyList();
        }
        List<ReportColumnDTO> excelHeaders = new ArrayList<>(this.getExcelIdFieldList().size());
        List<String> seen = getRowHeadersLabels();
        // Siempre se agregan columnas de `excelIdFieldList`
        Set<SurveyDataFieldDTO> surveyFields = SurveyUtil.getFields(getSurveyId(), this.getExcelIdFieldList(), getLoggedUser());
        for (SurveyDataFieldDTO field : surveyFields) {
            String excelIdFieldLabel = field.getTitle();
            if (seen.contains(excelIdFieldLabel)) {
                continue;
            }
            excelHeaders.add(
                    new ReportColumnDTO(
                            reportId,
                            slimReportId,
                            field.getName(),
                            excelIdFieldLabel,
                            true,
                            ReportColumnType.GHOST_FIELD,
                            field.getFieldOrder()
                    )
            );
        }
        return excelHeaders;
    }

    private boolean hasHeadersMatchingExcelIdFields(List<String> excelIdFieldList) {
        return this.getHeaders().stream().anyMatch(c -> excelIdFieldList.contains(c.getQueryColumnCode()))
                && this.getHeaders().stream().allMatch(c -> excelIdFieldList.contains(c.getQueryColumnCode()));
    }

    private static IReportBulkConfig getConfig(String documentMasterId, Long reportId) throws QMSException {
        IReportBulkConfig config = Utilities.getUntypedDAO().HQLT_findSimple(ReportBulkConfig.class, " "
                    + " SELECT new " + ReportBulkConfig.class.getCanonicalName() + "("
                        + " r.queryId AS databaseQueryId "
                        + ",s.reportId AS reportId "
                        + ",s.excelIdFields AS excelIdFields "
                        + ",s.id AS slimReportId "
                        + ",s.code AS slimReportCode "
                    + " ) "
                    + " FROM " + SlimReports.class.getCanonicalName() + " s "
                    + " JOIN " + Report.class.getCanonicalName() + " r ON r.id = s.reportId "
                    + " WHERE "
                        + " s.documentMasterId = :documentMasterId"
                        + " AND s.reportId = :reportId"
                , ImmutableMap.of(
                        "reportId", reportId,
                        "documentMasterId", documentMasterId
                ), true, CacheRegion.REPORT, 0
        );
        if (config == null) {
            throw new QMSException("No se encontró la configuración de carga masiva para el reporte");
        }
        return config;
    }

    @Override
    public BulkWriter<ReportBulkPlainRow, IReportColumnDTO, ReportBulkSaveResultRow, Map<String, Object>> getWritter() {
        return writer;
    }

    @Override
    public boolean isBulkRegistryLogEnabled() {
        return true;
    }

    @Override
    public BulkLogRegistry getNewBulkLogRegistryInstance(ReportBulkSaveResultRow saved) {
        if (saved.getValues().get("outstanding_surveys_id") == null) {
            return null;
        }
        BulkLogRegistry log = new BulkLogRegistry();
        log.setOutstandingSurveysId((Long) saved.getValues().get("outstanding_surveys_id"));
        log.setId(-1L);
        return log;
    }

    @Override
    public IBulkLog getNewBulkLogInstance(BuilkUploadType type) {
        BulkLog bulkLog = new BulkLog(-1L, type);
        bulkLog.setMasterId(this.getDocumentMasterId());
        bulkLog.setSlimReportId(this.getSlimReportId());
        return bulkLog;
    }

    @Override
    public boolean skipErrors() {
        return true;
    }

    @Override
    public void setCrossReference(Map<String, Object> crossReference, IBulkLocalizedRow<ReportBulkPlainRow> row) {
        final StringBuilder query = new StringBuilder();
        // valores de la tabla de respuestas
        final String surveyAnswersTable = SurveyUtil.ANSWERS_TABLE_PREFFIX + getDocumentMasterId().replaceAll("-", "");
        query.append(" "
            + " SELECT "
                + " max(outstanding_surveys_id1) AS outstanding_surveys_id "
            + " FROM ").append(surveyAnswersTable).append(" ").append(SurveyUtil.SURVEY_FIELD_ALIAS).append(" "
            + " WHERE ").append(SurveyUtil.SURVEY_FIELD_ALIAS).append(".deleted = 0 ");
        final Map<String, Object> params = new HashMap<>();
        for (String fieldName : this.getExcelIdFieldList()) {
            SurveyFieldAnswerType type = fields.get(fieldName);
            if (type == null) {
                type = SurveyFieldAnswerType.ALPHA_NUMERIC;
            }
            Object obj = crossReference.get(fieldName);
            if (obj == null) {
                getLogger().debug("El campo {} no tiene valor asignado para el row {}", fieldName, row);
                row.setValid(false);
                return;
            }
            if (obj instanceof String && ((String) obj).isEmpty()) {
                getLogger().debug("El campo {} no tiene una cadena vacía para el row {}", fieldName, row);
                row.setValid(false);
                return;
            }
            switch (type) {
                case NUMERIC:
                case DECIMAL:
                case CURRENCY_USD_TO_MXN:
                case CURRENCY:
                    query.append(" AND TRY_CONVERT(FLOAT, ").append(fieldName).append(") = :").append(fieldName);
                    Double value = parseDouble(obj);
                    if (value == null) {
                        getLogger().debug("El campo {} tiene un número inválido {} para el row {}", fieldName, obj, row);
                        row.setValid(false);
                        return;
                    }
                    params.put(fieldName, value);
                    break;
                case MAIL:
                case ALPHA_NUMERIC:
                default:
                    query.append(" AND ").append(fieldName).append(" = :").append(fieldName);
                    params.put(fieldName, obj);
            }
        }
        final Long outstandingSurveysId = getDynamicTableDAO().SQL_findSimpleLong(query.toString(), params, 0);
        if (outstandingSurveysId == 0L) {
            row.setValid(false);
        } else {
            crossReference.put("outstanding_surveys_id", outstandingSurveysId);
        }
        // valores de la tabla de carga masiva
        if (getDynamicTable().isNewTable()) {
            getLogger().error("La tabla de carga masiva {} se acaba de crear, no se puede guardar en ella aún.", getDynamicTable().getTableName());
            row.setValid(false);
            return;
        }
        query.setLength(0);
        query.append(" "
            + " SELECT "
                + " max(id) AS id "
            + " FROM [").append(getDynamicTable().getSchemaName()).append("].[").append(getDynamicTable().getTableName()).append("] b "
            + " WHERE "
                + " b.outstanding_surveys_id = :outstanding_surveys_id ");
        final Long id = getDynamicTableDAO().SQL_findSimpleLong(query.toString(), ImmutableMap.of("outstanding_surveys_id", outstandingSurveysId), 0);
        crossReference.put(
                "id", id == 0L ? null : id
        );
    }

    private Double parseDouble(Object obj) {
        try {
            return Double.parseDouble(String.valueOf(obj));
        } catch(Exception e) {
            return null;
        }
    }
}
