package qms.form.conditional;

import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.DAOInterface.ISurveysDAO;
import Framework.Config.Utilities;
import isoblock.surveys.dao.hibernate.Survey;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import mx.bnext.access.Module;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.form.dto.FormWeightingItemDTO;
import qms.form.dto.OutstandingSurveyDTO;
import qms.form.dto.OutstandingSurveyLoadAnswersDTO;
import qms.framework.rest.SecurityRootUtils;
import qms.survey.dto.SurveyCaptureConfig;
import qms.survey.dto.SurveyParserData;
import qms.survey.logic.ConditionalCacheKey;
import qms.survey.logic.SurveyParser;
import qms.survey.logic.SurveytoHtmlRenderer;
import qms.survey.util.SurveyCaptureUtil;

public class OutstandingSurveysValidatorCache {

    private static final Logger LOGGER = Loggable.getLogger(OutstandingSurveysValidatorCache.class);

    private final ISurveyCaptureDAO dao;

    private OutstandingSurveyLoadAnswersDTO answers = null;
    private Boolean archived = null;
    private Long authPoolIndex = null;
    private OutstandingSurveyDTO outstandingSurvey = null;
    private SurveyParser surveyParser = null;
    private String masterId = null;
    private Map<Long, Double> weightingItems = null;
    private Map<Long, Double> weightingHeaders = null;

    public OutstandingSurveysValidatorCache() {
        this.dao = Utilities.getBean(ISurveyCaptureDAO.class);
    }

    public boolean isArchived(final Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return false;
        }
        if (archived != null) {
            return archived;
        }
        final boolean systemArchivedEnabled = Boolean.TRUE.equals(Utilities.getSettings().getArchived());
        if (!systemArchivedEnabled) {
            archived = false;
            return false;
        }
        archived = dao.isArchived(outstandingSurveyId);
        return archived;
    }

    @Nullable
    public OutstandingSurveyLoadAnswersDTO getAnswers(final Long outstandingSurveyId, final String masterId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0 || masterId == null) {
            return null;
        }
        if (answers != null) {
            return answers;
        }
        answers = dao.loadDataFromSurveyAnswers(outstandingSurveyId, masterId, SecurityRootUtils.getFirstAdminDto());
        return answers;
    }

    @Nonnull
    public Map<Long, Double> getWeightsBySurveyItemId(
            final ConditionalCacheKey cacheKey,
            final Long authorizationPoolIndex
    ) {
        final SurveyParser surveyParser = getSurveyParser(cacheKey, authorizationPoolIndex);
        if (surveyParser == null) {
            LOGGER.error("Can not load weights by survey item id, survey parser is missing in cache");
            return new LinkedHashMap<>();
        }
        if (weightingItems != null) {
            return weightingItems;
        }
        final List<FormWeightingItemDTO> items = surveyParser.getWeightsBySurveyItemId();
        weightingItems = items.stream()
                .collect(
                        LinkedHashMap::new,
                        (map, item) -> map.put(item.getItemId(), item.getWeight()),
                        Map::putAll
                );
        return weightingItems;
    }

    @Nonnull
    public Map<Long, Double> getWeightsBySurveyHeaderId(
            final ConditionalCacheKey cacheKey,
            final Long authorizationPoolIndex
    ) {
        final SurveyParser surveyParser = getSurveyParser(cacheKey, authorizationPoolIndex);
        if (surveyParser == null) {
            LOGGER.error("Can not load weights by survey header id, survey parser is missing in cache");
            return new LinkedHashMap<>();
        }
        if (weightingHeaders != null) {
            return weightingHeaders;
        }
        final List<FormWeightingItemDTO> items = surveyParser.getWeightsBySurveyHeaderId();
        weightingHeaders = items.stream()
                .collect(
                        LinkedHashMap::new,
                        (map, item) -> map.put(item.getHeaderId(), item.getWeight()),
                        Map::putAll
                );
        return weightingHeaders;
    }

    @Nullable
    public Long getAuthPoolIndex(final Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return null;
        }
        if (authPoolIndex != null) {
            return authPoolIndex;
        }
        authPoolIndex = dao.getOutstandingAutorizationPoolIndex(outstandingSurveyId);
        return authPoolIndex;
    }

    @Nullable
    private OutstandingSurveyDTO getOutstandingSurvey(final Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return null;
        }
        if (outstandingSurvey != null) {
            return outstandingSurvey;
        }
        outstandingSurvey = dao.getOutstandingSurveyDTO(outstandingSurveyId);
        return outstandingSurvey;
    }

    @Nullable
    public SurveyParser getSurveyParser(
            final ConditionalCacheKey cacheKey,
            final Long authorizationPoolIndex
    ) {
        if (surveyParser != null) {
            return surveyParser;
        }
        OutstandingSurveyDTO outstandingSurveys = getOutstandingSurvey(cacheKey.getOutstandingSurveyId());
        if (outstandingSurveys == null) {
            return null;
        }
        final ILoggedUser loggedUser = SecurityRootUtils.getFirstAdminDto();
        final SurveyCaptureConfig config = SurveyCaptureUtil.getDefaultConfig(
                outstandingSurveys.getOutstandingSurveyId(),
                loggedUser
        );
        final ISurveysDAO dao = Utilities.getBean(ISurveysDAO.class);
        final Survey survey = SurveytoHtmlRenderer.getSurveyToHtml(outstandingSurveys.getSurveyId(), dao);
        final SurveyParserData data = new SurveyParserData(
                survey,
                config.getAuthRole(),
                outstandingSurveys,
                false,
                false,
                false,
                false,
                false,
                cacheKey.getMode().name(),
                Module.FORMULARIE,
                null,
                authorizationPoolIndex
        );
        surveyParser = new SurveyParser(
                data,
                null,
                null,
                loggedUser
        );
        return surveyParser;
    }

    @Nullable
    public String getMasterId(Long outstandingSurveyId) {
        if (masterId != null) {
            return masterId;
        }
        final OutstandingSurveyDTO outstandingSurveys = getOutstandingSurvey(outstandingSurveyId);
        if (outstandingSurveys == null) {
            return null;
        }
        masterId = outstandingSurveys.getDocumentMasterId();
        return masterId;
    }

}
