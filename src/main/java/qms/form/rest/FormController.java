/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.form.rest;

import DPMS.DAO.HibernateDAO_Document;
import DPMS.DAO.HibernateDAO_Request;
import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IBusinessUnitLiteDAO;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IOutstandingSurveysDAO;
import DPMS.DAOInterface.IProcessDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.DAOInterface.IUserIdNameDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.Department;
import DPMS.Mapping.Document;
import DPMS.Mapping.Files;
import DPMS.Mapping.NodeAccess;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.User;
import DPMS.Mapping.UserDefinedToFillHistory;
import Framework.Config.Language;
import Framework.Config.TextHasValue;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import Framework.DAO.SaveHandle;
import bnext.exception.InvalidCipherDecryption;
import bnext.exception.MakePersistentException;
import bnext.reference.UserRef;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sun.star.auth.InvalidArgumentException;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyProgressStateCancel;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.BatchUpdateException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.GridInfoStatus;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qms.access.dto.ILoggedUser;
import qms.configuration.util.LoggingHandler;
import qms.form.conditional.ConditionalValidator;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dao.IFormPublishMigrationDAO;
import qms.form.dao.IFormRequestDAO;
import qms.form.dto.ChangeDefinedUserResult;
import qms.form.dto.FavoriteTaskDto;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.FormConfigFieldType;
import qms.form.dto.FormMailConfigurationDTO;
import qms.form.dto.FormPendingConfigDTO;
import qms.form.dto.FormPublishMigrationColumnDataSourceDTO;
import qms.form.dto.FormRequestDTO;
import qms.form.dto.FormRequestResponseDTO;
import qms.form.dto.FormSaveDTO;
import qms.form.dto.FormSlimReportDTO;
import qms.form.dto.FormSlimReportDataSourceDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.dto.conditional.FieldValueChangeDTO;
import qms.form.dto.conditional.ResultDTO;
import qms.form.dto.conditional.WeightingResultDTO;
import qms.form.entity.AnswerPartType;
import qms.form.entity.FavoriteTask;
import qms.form.entity.FormPendingConfig;
import qms.form.entity.FormProgressState;
import qms.form.entity.FormRequest;
import qms.form.entity.IFavoriteTask;
import qms.form.entity.SurveyMailConfiguration;
import qms.form.entity.SurveySubjectConfiguration;
import qms.form.entity.UserFavoriteTask;
import qms.form.util.CatalogFieldType;
import qms.form.util.SurveyParserHelper;
import qms.form.util.SurveyUtil;
import qms.framework.bulk.entity.BulkLog;
import qms.framework.bulk.entity.BulkLogRegistry;
import qms.framework.bulk.imp.FormBulkHandler;
import qms.framework.bulk.util.BulkError;
import qms.framework.dao.IReportDAO;
import qms.framework.dao.ISlimReportsDAO;
import qms.framework.dto.ReportDTO;
import qms.framework.entity.Report;
import qms.framework.entity.SlimReports;
import qms.framework.entity.SlimReportsTransformedFieldHistory;
import qms.framework.entity.SlimReportsTransformedFieldRule;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.UserLogin;
import qms.framework.util.AboutApp;
import qms.framework.util.CacheRegion;
import qms.framework.util.DataSourceCredentialError;
import qms.framework.util.DatabaseQueryHandler;
import qms.framework.util.LocaleUtil;
import qms.framework.util.ReportHandler;
import qms.framework.util.SettingsUtil;
import qms.survey.dto.ChangeDefinedUserDto;
import qms.survey.logic.ConditionalValidatorCache;
import qms.task.dto.IQualifiedMenuItem;
import qms.task.enums.FavoriteTaskType;
import qms.task.util.QualifiedMenuItemUtil;
import qms.util.FormUtil;
import qms.util.GridFilter;
import qms.util.GridFilterByReportProcessingAccess;
import qms.util.GridOrderBy;
import qms.util.ModuleUtil;
import qms.util.QMSException;
import qms.util.ReportBaseController;
import qms.workflow.util.WorkflowAuthRole;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Lazy
@RestController("FormController")
@RequestMapping("forms")
@Language(module = "qms.form.rest.FormController")
public class FormController extends ReportBaseController {

    private static final Logger LOGGER = Loggable.getLogger(FormController.class);

    private final String HAS_REPORTS_VIEW_ACCESS = "hasAnyAuthority('IS_ADMIN', 'FORMULARIO_CONTROL', 'REPORT_FORM', 'FORMULARIO_CONTROL')"
                                    + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('FORM_REGISTER_REPORT'))"
                                    + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('FORM_ADMON_REPORT_ACCESS'))"
                                    + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('FORM_DELETE_REPORT'))";
    
    private final String HAS_ANSWERS_ACCESS = "hasAnyAuthority('IS_ADMIN', 'FORMULARIO_CONTROL')";

    private final String IS_ADMIN = "hasAnyAuthority('IS_ADMIN')";

    private final String HAS_EDIT_FORM_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'DOCUMENTO_ENCARGADO', "
            + "'FORMULARIO_ALTA', 'SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT',"
            + "'SPECIAL_DOCUMENT_MANAGER_BY_ORGANIZATIONAL_UNIT',"
            + "'SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT'"
        + ")";
    
    private final String HAS_CHANGE_DEFINED_USER_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'FILL_OUT_HISTORY', 'FILL_FORM'"
            + ")";

    @Autowired
    @Qualifier("ReportDAO")
    private IReportDAO reportDao;
    
    @Autowired
    @Qualifier("FormRequestDAO")
    private IFormRequestDAO formRequestDAO;
    
    @Autowired
    @Qualifier("FormCaptureDAO")
    private IFormCaptureDAO formCaptureDAO;
    
    private static final LoadingCache<Long, Object> OUTSTANDING_SAVE_LOCKS = CacheBuilder.newBuilder()
        .expireAfterAccess(60, TimeUnit.MINUTES)
        .maximumSize(5000)
        .build(CacheLoader.from(Object::new));
    
    private ResourceBundle tags;
    private final Locale locale;
    
    public FormController() {
        this.locale = Utilities.getLocale();
        try {
            this.tags = LocaleUtil.getI18n(this.getClass(), this.locale);
        } catch (MissingResourceException e) {
            getLogger().error("Language {} does not exists", this.getClass().getAnnotation(Language.class));
        }
    }

    /**
     * Used in Spring ACCESS for example
     */
    @SuppressWarnings("unused")  
    public Boolean isCancelAvailable(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return false;
        }
        return formCaptureDAO.isCancelAvailable(outstandingSurveyId, SecurityUtils.getLoggedUser());
    }

    /**
     * Used in Spring ACCESS for example
     */
    @SuppressWarnings("unused")  
    public Boolean hasFormPending(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return false;
        }
        return formCaptureDAO.hasFormPending(outstandingSurveyId, SecurityUtils.getLoggedUser());
    }

    private Long getSurveyId(Long documentId) {
        final IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
        return dao.HQL_findLong(" "
                        + " SELECT d.surveyId"
                        + " FROM " + Document.class.getCanonicalName() + " d "
                        + " WHERE d.id = :id",
                "id", documentId
        );
    }

    @Nonnull
    private FieldConfigDTO getFields(Long surveyId, ILoggedUser loggedUser) {
        return SurveyUtil.getFields(
                surveyId,
                true,
                false,
                false,
                null,
                0,
                loggedUser,
                true,
                true,
                true
        );
    }

    private boolean isHierarchy(SurveyDataFieldDTO field) {
        final SurveyField.TYPE type = SurveyField.TYPE.fromValue(field.getFieldType());
        return (type == SurveyField.TYPE.EXTERNAL_CATALOG
                && Objects.equals(field.getCatalogSubType(), CatalogFieldType.CATALOG_HIERARCHY.toString()));
    }


    @GetMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', 'FILL_ONLY', 'DOCUMENTO_ENCARGADO', "
            + "'DOCUMENTO_EDITOR', 'DOCUMENTO_LECTOR', 'FORMULARIO_ALTA',"
            + "'FORMULARIO_CONTROL', 'FILL_OUT_HISTORY', 'SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT',"
            + "'SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT', 'SPECIAL_DOCUMENT_READER', 'FILL_FORM'"
        + ")")
    @RequestMapping("fill-only")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<IQualifiedMenuItem>> formList() {
        final List<IQualifiedMenuItem> result = new ArrayList<>();
        final List<IFavoriteTask> favorites = Utilities.getBean(IFormCaptureDAO.class).HQL_findByQuery(" SELECT "
                + " new " + FavoriteTaskDto.class.getCanonicalName() + "("
                    + "c.id"
                    + ",c.status"
                    + ",c.deleted"
                    + ",c.type"
                    + ",c.menuPath"
                    + ",c.code"
                    + ",c.description"
                    + ",c.urlParams"
                    + ",c.href"
                    + ",c.icon"
                    + ",c.documentMasterId"
                    + ",c.isSystemGenerated"
                    + ",c.createdBy"
                    + ",c.lastModifiedBy"
                    + ",c.createdDate"
                    + ",c.lastModifiedDate"
                + " )"
            + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
            + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                + " ON uf.id.favoriteTaskId = c.id"
            + " JOIN " + UserRef.class.getCanonicalName() + " u "
                + " ON uf.id.userId = u.id"
            + " WHERE"
                + " u.id = :loggedUserId"
                + " AND c.status IN ("
                        + Document.ACTIVE_STATUS
                        + "," + Document.IN_EDITION_STATUS
                + " )"
                + " AND c.isSystemGenerated = false"
                + " AND c.type = " + FavoriteTaskType.FORM.getValue()
                + " AND c.deleted = 0",
            ImmutableMap.of(
                "loggedUserId", SecurityUtils.getLoggedUserId()
            ),
            true, 
            CacheRegion.FAVORITE,
            0
        );
        String account = SecurityUtils.getLoggedUserAccount();
        favorites.forEach((IFavoriteTask fav) -> {
            IQualifiedMenuItem task = null;
            try {
                task = QualifiedMenuItemUtil.fromUserFavoriteTask(account, fav, Utilities.getBean(IFormCaptureDAO.class));
            } catch (UnsupportedEncodingException ex) {
                getLogger().error("Invalid userFavoriteTask, account ''", account, ex);
            }
            if (task != null) {
                result.add(task);
            }
        });
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    

    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_REOPEN_APPROVER'"
        + ")")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @RequestMapping("reopen/authorize")
    public ResponseEntity<SaveHandle> verifyFormRequestReopenApproved(
        final @RequestBody TextLongValue dto
    ) throws IOException, QMSException, MakePersistentException, BatchUpdateException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        return formRequestDAO.verifyFormRequestReopenApproved(entity, authRole, reason, loggedUser);
    }

    
    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_REOPEN_APPROVER'"
        + ")")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @RequestMapping("reopen/reject")
    public ResponseEntity<SaveHandle> verifyFormRequestReopenRejected(
        final @RequestBody TextLongValue dto
    ) throws IOException, QMSException, MakePersistentException, BatchUpdateException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        return formRequestDAO.verifyFormRequestReopenRejected(entity, authRole, reason, loggedUser);
    }

    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_ADJUSTMENT_APPROVER'"
        + ")")
    @RequestMapping("adjust/authorize")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<SaveHandle> verifyFormRequestAdjustApproved(
        final @RequestBody TextLongValue dto
    ) throws MakePersistentException, BatchUpdateException, IOException, QMSException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        return formRequestDAO.verifyFormRequestAdjustApproved(entity, authRole, reason, loggedUser);
    }

    
    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_ADJUSTMENT_APPROVER'"
        + ")")
    @RequestMapping("adjust/reject")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<SaveHandle> verifyFormRequestAdjustRejected(
        final @RequestBody TextLongValue dto
    ) throws IOException, QMSException, MakePersistentException, BatchUpdateException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        return formRequestDAO.verifyFormRequestAdjustRejected(entity, authRole, reason, loggedUser);
    }
    

    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', 'FILL_FORM', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_ADJUSTMENT_AUTHORIZER'"
        + ")")
    @RequestMapping("workflow/adjust/authorize")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<SaveHandle> workflowFormRequestAdjustApproved(
        final @RequestBody TextLongValue dto
    ) throws IOException, QMSException, MakePersistentException, BatchUpdateException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        return formRequestDAO.workflowFormRequestAdjustApproved(entity, authRole, reason, loggedUser);
    }

    
    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', 'FILL_FORM', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_ADJUSTMENT_AUTHORIZER'"
        + ")")
    @RequestMapping("workflow/adjust/reject")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<SaveHandle> workflowFormRequestAdjustRejected(
        final @RequestBody TextLongValue dto
    ) throws IOException, QMSException, MakePersistentException, BatchUpdateException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        return formRequestDAO.workflowFormRequestAdjustRejected(entity, authRole, reason, loggedUser);
    }
    
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', 'FILL_ONLY', 'DOCUMENTO_ENCARGADO', "
            + "'DOCUMENTO_EDITOR', 'DOCUMENTO_LECTOR', 'FORMULARIO_ALTA',"
            + "'FORMULARIO_CONTROL', 'FILL_OUT_HISTORY', 'SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT',"
            + "'SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT', 'SPECIAL_DOCUMENT_READER', 'FILL_FORM'"
        + ")")
    @GetMapping("getFormParams/{documentMasterId}")
    public ResponseEntity<Map<String, Long>> getFormParams(
        final @PathVariable(value = "documentMasterId", required = false) String documentMasterId) {
        Map<String, Long> result = Utilities.getBean(IFormCaptureDAO.class).HQL_findSimpleMap(" SELECT new map("
                            + "d.surveyId as surveyId,"
                            + "d.id as documentId,"
                            + "CAST(d.status AS long) as status"
                        + " )"
                        + " FROM " + Document.class.getCanonicalName() + " d "
                        + " WHERE"
                            + " d.masterId = :documentMasterId"
                            + " AND d.deleted = 0 "
                            + " AND d.status IN ("
                                + Document.ACTIVE_STATUS
                                + "," + Document.IN_EDITION_STATUS
                            + " )", "documentMasterId", documentMasterId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    
    @PreAuthorize(HAS_EDIT_FORM_ACCESS)
    @GetMapping("{id}")
    public ResponseEntity<FormSaveDTO> load(
        final @PathVariable(value = "id", required = false) Long id
    ) {
        final IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
        final FormSaveDTO result = dao.loadForm(id);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    
    @PreAuthorize(HAS_EDIT_FORM_ACCESS)
    @GetMapping("restrict-record-fields/{id}")
    public ResponseEntity<List<TextHasValue>> restrictRecordFieldsValues(
        final @PathVariable(value = "id", required = false) Long id
    ) {
        final IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
        final Long surveyId = getSurveyId(id);
        final List<TextHasValue> result = dao.getRestrictRecordsFieldValues(surveyId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize(HAS_EDIT_FORM_ACCESS)
    @GetMapping("mail-fields/{id}")
    public ResponseEntity<List<FormMailConfigurationDTO>> mailFields(
            final @PathVariable(value = "id", required = false) Long id
    ) {
        final Long surveyId = getSurveyId(id);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FieldConfigDTO data = getFields(surveyId, loggedUser);
        final List<SurveySubjectConfiguration> info = Utilities.getBean(ISurveysDAO.class).HQLT_findByQuery(
                SurveySubjectConfiguration.class, " "
                        + " SELECT ssc"
                        + " FROM " + SurveySubjectConfiguration.class.getCanonicalName() + " ssc "
                        + " WHERE ssc.surveyId = :surveyId",
                "surveyId",
                surveyId
        );
        final List<SurveyMailConfiguration> config = Utilities.getBean(ISurveysDAO.class).HQLT_findByQuery(
                SurveyMailConfiguration.class," "
                        + " SELECT ssc"
                        + " FROM " + SurveyMailConfiguration.class.getCanonicalName() + " ssc "
                        + " WHERE ssc.surveyId = :surveyId",
                "surveyId",
                surveyId
        );
        final int fieldsSize = info.size() + config.size() + (data.getFields() != null ? data.getFields().size() : 0);
        final List<FormMailConfigurationDTO> result = new ArrayList<>(fieldsSize);
        if (data.getFields() != null) {
            final Map<Long, SurveyDataFieldDTO> addedFields = new HashMap<>();
            data.getFields().forEach(field -> buildMailConfig(field, addedFields, result));
        }
        info.forEach(field -> {
            final FormMailConfigurationDTO mailConfig = new FormMailConfigurationDTO();
            mailConfig.setId(field.getId());
            mailConfig.setTitle(field.getFieldDescription());
            mailConfig.setIncludeInMail(field.getIncludeInSubject());
            mailConfig.setFieldType(FormConfigFieldType.INFO);
            result.add(mailConfig);
        });
        config.forEach(field -> {
            final FormMailConfigurationDTO mailConfig = new FormMailConfigurationDTO();
            mailConfig.setId(field.getId());
            mailConfig.setTitle(field.getConfigDescription());
            mailConfig.setIncludeInMail(field.getStatus());
            mailConfig.setFieldType(FormConfigFieldType.CONFIGURATION);
            result.add(mailConfig);
        });
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    private void buildMailConfig(
            final SurveyDataFieldDTO field,
            final Map<Long, SurveyDataFieldDTO> addedFields,
            final List<FormMailConfigurationDTO> result
    ) {
        // No se necesita mostrar el valor, sino la descripción.
        if (field.getDisableColumnPerOption()
                && Objects.equals(field.getAnswerPartType(), AnswerPartType.CATALOG_VALUE.getValue())) {
            return;
        }
        final FormMailConfigurationDTO config = new FormMailConfigurationDTO();
        // Only SUPPORT additional data in mail for EXTERNAL_CATALOG.
        boolean isHierarchy = isHierarchy(field);
        // TODO: Remover condicional cuando el campo DAY_FIELD de TEXT_DATE sea soportado.
        //  Revisar si existe algún otro caso de AnswerPartType.java.
        if (addedFields.containsKey(field.getId()) && !isHierarchy) { // Campo de jerarquía contiene mismo ID para todos los campos de los niveles.
            return;
        }
        addedFields.put(field.getId(), field);
        config.setTitle(field.getTitle());
        config.setId(field.getId());
        final List<Long> associatedFields;
        if (field.getAssociatedFields() != null) {
            associatedFields = field.getAssociatedFields().stream()
                    .map(SurveyDataFieldDTO::getOptionId)
                    .collect(Collectors.toList());
        } else {
            associatedFields = null;
        }
        config.setAssociatedFields(associatedFields);
        if (field.getSectionId() != null) {
            config.setFieldType(FormConfigFieldType.FIELD);
            config.setIncludeInMail(field.getIncludeInSubject());
            config.setSeccionId(field.getSectionId());
            config.setName(field.getName());
            config.setIsAdditionalData(isHierarchy);
        } else {
            config.setFieldType(FormConfigFieldType.SECCION);
            config.setIncludeInMail(field.getIncludeInMail());
            config.setSeccionId(field.getFieldId());
            config.setStage(field.getFieldStage());
        }
        result.add(config);
    }

    @PreAuthorize(HAS_EDIT_FORM_ACCESS)
    @GetMapping("pending-info-fields/{id}")
    public ResponseEntity<List<FormPendingConfigDTO>> pendingInfoFields(
            final @PathVariable(value = "id", required = false) Long id
    ) {
        final Long surveyId = getSurveyId(id);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();

        final FieldConfigDTO data = getFields(surveyId, loggedUser);
        if (data.getFields() == null || data.getFields().isEmpty()) {
            getLogger().error("Fields not found for survey {}", surveyId);
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }

        final Map<String, SurveyDataFieldDTO> addedFields = new HashMap<>();
        final List<FormPendingConfigDTO> fields = data.getFields().stream()
                .filter((field) -> isValidPendingConfig(field, addedFields))
                .map(this::buildPendingConfig)
                .collect(Collectors.toList());
        if (fields.isEmpty()) {
            getLogger().error("Not found any valid field for survey {}", surveyId);
            return new ResponseEntity<>(HttpStatus.CONFLICT);

        }

        final List<FormPendingConfig> savedFields = Utilities.getBean(ISurveysDAO.class).HQLT_findByQuery(
                FormPendingConfig.class, " "
                        + " SELECT ssc"
                        + " FROM " + FormPendingConfig.class.getCanonicalName() + " ssc "
                        + " WHERE ssc.surveyId = :surveyId",
                "surveyId",
                surveyId
        );
        final Map<String, FormPendingConfigDTO> resultMap = fields.stream()
                .collect(Collectors.toMap(FormPendingConfigDTO::getName, (FormPendingConfigDTO f) -> f));
        savedFields.forEach(savedField -> {
            final FormPendingConfigDTO config = resultMap.get(savedField.getFieldName());
            if (config == null) {
                getLogger().error("Field config of {} not found in survey {}", savedField.getFieldName(), surveyId);
                return;
            }
            config.setId(savedField.getId());
            config.setShowInChips(savedField.getShowInChips());
            config.setShowInDetails(savedField.getShowInDetails());
        });
        return new ResponseEntity<>(fields, HttpStatus.OK);
    }

    private boolean isValidPendingConfig(
            final SurveyDataFieldDTO field,
            final Map<String, SurveyDataFieldDTO> addedFields
    ) {
        // No se necesita mostrar el valor, sino la descripción.
        if (field.getDisableColumnPerOption()
                && Objects.equals(field.getAnswerPartType(), AnswerPartType.CATALOG_VALUE.getValue())) {
            return false;
        }
        if (CatalogFieldType.CATALOG_HIERARCHY.getName().equals(field.getCatalogSubType())
                && SurveyUtil.isCatalogHierarchyOrSystemColumn(field)) {
            return false;
        }
        final String id;
        if (field.getAnswerPartType() != null) {
            id = field.getAnswerPartType() + "-" + field.getId();
        } else {
            id = field.getId() + "";
        }
        final boolean isHierarchy = isHierarchy(field);
        // Campo de jerarquía contiene mismo ID para todos los campos de los niveles.
        if (addedFields.containsKey(id) && !isHierarchy) {
            return false;
        }
        addedFields.put(id, field);
        return true;
    }

    private FormPendingConfigDTO buildPendingConfig(final SurveyDataFieldDTO field) {
        final FormPendingConfigDTO config = new FormPendingConfigDTO();
        config.setTitle(field.getTitle());
        config.setId(-1L);
        if (field.getSectionId() != null) {
            config.setFieldType(FormConfigFieldType.FIELD);
            config.setSeccionId(field.getSectionId());
            config.setName(field.getName());
        } else {
            config.setFieldType(FormConfigFieldType.SECCION);
            config.setSeccionId(field.getFieldId());
            config.setStage(field.getFieldStage());
            config.setName(field.getName());
        }
        config.setShowInChips(0);
        config.setShowInDetails(0);
        return config;
    }

    @PreAuthorize(HAS_EDIT_FORM_ACCESS)
    @PostMapping("mail/save/{id}")
    public ResponseEntity<String> mailSave(
        final @PathVariable(value = "id", required = false) Long id,
        final @RequestBody List<FormMailConfigurationDTO> dto
    ) {
        final Long surveyId = getSurveyId(id);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ISurveysDAO dao = Utilities.getBean(ISurveysDAO.class);
        final Integer updated = dao.saveMailConfigurationForm(dto, surveyId, loggedUser);
        if (updated != null && updated > 0) {
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PreAuthorize(HAS_EDIT_FORM_ACCESS)
    @PostMapping("pending-info/save/{id}")
    public ResponseEntity<String> pendingConfigSave(
            final @PathVariable(value = "id", required = false) Long id,
            final @RequestBody List<FormPendingConfigDTO> dto
    ) {
        final Long surveyId = getSurveyId(id);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ISurveysDAO dao = Utilities.getBean(ISurveysDAO.class);
        final Integer updated = dao.savePendingConfigs(dto, surveyId, loggedUser);
        if (updated != null && updated > 0) {
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PostMapping()
    @PreAuthorize(HAS_EDIT_FORM_ACCESS)
    @RequestMapping("save")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<String> save(
        final @RequestBody FormSaveDTO dto
    ) throws InvalidArgumentException, MakePersistentException {
        final IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final Integer updated = dao.saveForm(dto, loggedUser);
        if (dto.getRestrictRecordsByDepartment() == null) {
            dto.setRestrictRecordsByDepartment(false);
        }
        if (dto.getValidateAccessFormDepartment() == null) {
            dto.setValidateAccessFormDepartment(false);
        }
        if (updated != null && updated > 0) {
            return new ResponseEntity(HttpStatus.OK);
        } else {
            return new ResponseEntity(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @PostMapping()
    @RequestMapping("reports")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> reports(@RequestBody GridFilter filter) {
        return reportDao.reports(filter, getModule(), SecurityUtils.getLoggedUser());
    }
    

    @PostMapping({
        "form-log-lists/{documentMasterId}/{slimReportCode}/{outstandingSurveysId}",
        "form-log-lists/{documentMasterId}/{slimReportCode}"
    })
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> formLogLists(
            @RequestBody GridFilter filter,
            @PathVariable(value = "documentMasterId", required = true) String documentMasterId,
            @PathVariable(value = "slimReportCode", required = true) String slimReportCode,
            @PathVariable(value = "outstandingSurveysId", required = false) Long outstandingSurveysId
    ) {
        final Long slimReportId = ISlimReportsDAO.getSlimReportId(formCaptureDAO, documentMasterId, slimReportCode);

        if (filter == null) {
            filter = new GridFilter();
            filter.setField(new GridOrderBy(""));
        }
        StringBuilder query = new StringBuilder();
        query.append(" "
            + " SELECT new map( "
                + " c.description AS bulkDescription "
                + ",u.description AS bulkUserName "
                + ",c.createdDate AS bulkDate "
                + ",fo.description AS originalFileName "
                + ",fo.id AS originalFileId "
                + ",fr.description AS resultFileName "
                + ",fr.id AS resultFileId "
                + ",c.id AS bulkLogId "
                + ",c.errorCount AS errorCount "
            + " ) "
            + " FROM ").append(BulkLog.class.getCanonicalName()).append(" c "
            + " JOIN ").append(User.class.getCanonicalName()).append(" u ON u.id = c.createdBy "
            + " JOIN ").append(SlimReports.class.getCanonicalName()).append(" sr ON sr.id = c.slimReportId "
            + " LEFT JOIN ").append(Files.class.getCanonicalName()).append(" fo ON fo.id = c.sourceFileId "
            + " LEFT JOIN ").append(Files.class.getCanonicalName()).append(" fr ON fr.id = c.resultFileId "
            + " WHERE "
                + " c.deleted = 0 "
                + " AND sr.id = :slimReportId "
        );
        if (outstandingSurveysId != null) {
            query.append(" "
                + " AND c.id IN ("
                    + " SELECT log.id"
                    + " FROM ").append(BulkLogRegistry.class.getCanonicalName()).append(" lo "
                    + " JOIN lo.log log "
                    + " WHERE lo.outstandingSurveysId = :outstandingSurveysId "
                + " ) ");
            filter.getRawCriteria().put("outstandingSurveysId", outstandingSurveysId);
        }
        filter.getRawCriteria().put("slimReportId", slimReportId);
        return reportDao.HQL_getRows(query.toString(), filter);
    }


    @PostMapping({
            "transformed-field-history-lists/{documentMasterId}/{slimReportCode}",
            "transformed-field-history-lists/{documentMasterId}/{slimReportCode}/{outstandingSurveysId}"
    })
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> transformedFieldHistoryLists(
            @RequestBody GridFilter filter,
            @PathVariable(value = "documentMasterId") String documentMasterId,
            @PathVariable(value = "slimReportCode") String slimReportCode,
            @PathVariable(value = "outstandingSurveysId", required = false) Long outstandingSurveysId
    ) {
        final Long slimReportId = ISlimReportsDAO.getSlimReportId(formCaptureDAO, documentMasterId, slimReportCode);

        if (filter == null) {
            filter = new GridFilter();
            filter.setField(new GridOrderBy("createdAt"));
        }
        String query = " "
                + " SELECT new map( "
                    + " c.id AS id "
                    + ",u.description AS createdBy "
                    + ",c.createdAt AS createdAt "
                    + ",c.previousRuleCount AS previousRuleCount "
                    + ",c.currentRuleCount AS currentRuleCount "
                    + ",c.ruleDiffCountCount AS ruleDiffCountCount "
                    + ",c.sameRuleCount AS sameRuleCount "
                    + ",c.changeType AS changeType "
                    + ",o.id AS oldTransformedFieldId"
                    + ",n.id AS newTransformedFieldId"
                    + ",o.description AS oldFieldDescription"
                    + ",n.description AS newFieldDescription"
                + " ) "
                + " FROM " + SlimReportsTransformedFieldHistory.class.getCanonicalName() + " c "
                + " JOIN " + User.class.getCanonicalName() + " u ON u.id = c.createdBy "
                + " LEFT JOIN c.oldTransformedField o "
                + " LEFT JOIN c.newTransformedField n "
                + " WHERE "
                    + " c.slimReport.id = :slimReportId ";
        filter.getRawCriteria().put("slimReportId", slimReportId);
        if (outstandingSurveysId != null) {
            Map<String, Date> dates = reportDao.HQL_findSimpleMap(" SELECT new Map("
                    + " max(lo.createdDate) AS maxCreatedDate "
                    + " ,min(lo.createdDate) AS minCreatedDate"
                    + ") "
                    + " FROM " + BulkLogRegistry.class.getCanonicalName() + " lo "
                    + " JOIN lo.log log "
                    + " WHERE lo.outstandingSurveysId = :outstandingSurveysId ", "outstandingSurveysId", outstandingSurveysId
            );
            Date minDate = dates.get("minCreatedDate");
            Date maxDate = dates.get("maxCreatedDate");
            if (minDate == null || maxDate == null) {
                return Utilities.EMPTY_GRID_INFO;
            } else {
                filter.getRawCriteria().put("minDate", minDate);
                filter.getRawCriteria().put("maxDate", maxDate);
                query += " AND c.createdAt BETWEEN :minDate AND :maxDate ";
            }
        }
        return reportDao.HQL_getRows(query, filter);
    }

    @GetMapping({
            "transformed-fields-rules/{transformedFieldIds}"
    })
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String,Object>> transformedFields(
            @PathVariable(value = "transformedFieldIds") List<Long> transformedFieldIds
    ) {
        return reportDao.HQL_findByQuery("SELECT new Map("
                + "c.id as id,"
                + "c.transformedField.id as transformedFieldId,"
                // SlimReportsTransformedField.getConfig()
                + "c.ruleFieldText as text,"
                + "c.ruleFieldValue as value,"
                + "c.ruleFieldCode as fieldCode,"
                + "c.ruleFieldSource as fieldSource,"
                + "c.ruleFieldType as fieldType,"
                // SlimReportsTransformedField.getEvaluatedField()
                + "c.evalFieldCode as evalFieldCode,"
                + "c.evalFieldSource as evalFieldSource,"
                + "c.evalFieldText as evalFieldText,"
                + "c.evalFieldValue as evalFieldValue,"
                + "c.evalFieldType as evalFieldType,"
                + "c.typeName as typeName,"
                + "c.orderValue as orderValue,"
                + "c.typeFinalValue as typeFinalValue,"
                + "c.finalValue as finalValue,"
                + "c.ruleDateValue as ruleDateValue,"
                + "c.ruleNumberValue as ruleNumberValue,"
                + "c.ruleStringValue as ruleStringValue"
                + ") FROM " + SlimReportsTransformedFieldRule.class.getCanonicalName() + " c "
                + " WHERE c.transformedField.id IN (:transformedFieldIds)", "transformedFieldIds", transformedFieldIds);
    }

    @GetMapping("slim-report/data-source/{masterId}")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<FormSlimReportDataSourceDTO> slimReportDataSource(
        @PathVariable(name = "masterId", required = true) String documentMasterId
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        FormSlimReportDataSourceDTO dto = FormUtil.getSurveyDataFields(documentMasterId, false, 0, formCaptureDAO, loggedUser, true, false, false);
        return new ResponseEntity(dto, HttpStatus.OK);
    }
    
    @PostMapping()
    @RequestMapping("slim-report/save")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity newSlimReport(
        @RequestBody FormSlimReportDTO options
    ) throws QMSException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ISlimReportsDAO slimDao = Utilities.getBean(ISlimReportsDAO.class);
        return slimDao.newSlimReport(options, loggedUser);
    }
    
    @PostMapping()
    @RequestMapping("slim-report/update")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity updateSlimReport(
        @RequestBody FormSlimReportDTO options
    ) throws QMSException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ISlimReportsDAO slimDao = Utilities.getBean(ISlimReportsDAO.class);
        return slimDao.updateSlimReport(options, loggedUser);
    }
    
    @GetMapping()
    @RequestMapping("slim-report/load/{slimReportId}")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<FormSlimReportDTO> loadSlimReport(
        @PathVariable(name = "slimReportId", required = true) Long slimReportId
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final ISlimReportsDAO slimDao = Utilities.getBean(ISlimReportsDAO.class);
        return slimDao.loadSlimReport(slimReportId, loggedUser);
    }
    
    @GetMapping()
    @RequestMapping("slim-report/delete/{reportId}")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity deleteSlimReport(
        @PathVariable(name = "reportId", required = true) Long reportId
    ) {
        final ISlimReportsDAO slimDao = Utilities.getBean(ISlimReportsDAO.class);
        return slimDao.deleteSlimReport(reportId, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping("migrations/data-source/{masterId}")
    @PreAuthorize(IS_ADMIN)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<FormPublishMigrationColumnDataSourceDTO> migrationsDataSource(
        @PathVariable(value = "masterId", required = false) String masterId
    ) {
        IFormPublishMigrationDAO dao = Utilities.getBean(IFormPublishMigrationDAO.class);
        return ResponseEntity.ok(dao.getDataSource(masterId));
    }

    @PostMapping()
    @RequestMapping("slim-reports/{documentMasterId}")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> slimReports(
        @RequestBody GridFilter filter,
        @PathVariable(value = "documentMasterId", required = false) String documentMasterId
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        StringBuilder query = new StringBuilder();
        query.append(" "
                + "SELECT new map("
                    + " c.id AS id "
                    + ",c.code AS reportCode "
                    + ",c.status AS status "
                    + ",c.description AS title "
                    + ",c.details AS description "
                    + ",c.nodeId AS nodeId "
                    + ",createByUser.description AS createdBy "
                    + ",modifiedByUser.description AS lastModifiedBy "
                    + ",c.lastModifiedDate AS lastModifiedDate "
                    + ",c.createdDate AS createdDate "
                    + ",c.documentMasterId AS documentMasterId "
                    + ",document.id AS documentId "
                    + ",document.code AS documentCode "
                    + ",document.description AS documentDescription"
                    + ",sr.id AS slimReportId "
                + " )"
                + " FROM ").append(Report.class.getCanonicalName()).append(" c "
                + " JOIN ").append(SlimReports.class.getCanonicalName()).append(" sr on sr.reportId = c.id AND sr.deleted = 0 "
                + " LEFT JOIN ").append(User.class.getCanonicalName()).append(" createByUser ON createByUser.id = c.createdBy "
                + " LEFT JOIN ").append(User.class.getCanonicalName()).append(" modifiedByUser ON modifiedByUser.id = c.lastModifiedBy "
                + " LEFT JOIN ").append(Document.class.getCanonicalName()).append(" document"
                        + " ON "
                            + " document.masterId = c.documentMasterId"
                            + " AND document.status IN (").append(
                                Document.STATUS.ACTIVE.getValue()).append(" "
                                + ",").append(Document.STATUS.IN_EDITION.getValue()).append(" "
                            + " )"
                + " WHERE "
        );
        if (documentMasterId == null) {
            query.append(" c.documentMasterId IS NOT NULL ");
        } else {
            query.append(" c.documentMasterId = :documentMasterId ");
            filter.getRawCriteria().put("documentMasterId", documentMasterId);
        }
        return reportDao.reports(filter, getModule(), query.toString(), SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping("printing-format-list/{documentMasterId}")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> printingFormatList(
        @RequestBody GridFilter filter,
        @PathVariable(value = "documentMasterId", required = false) String documentMasterId
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        StringBuilder query = new StringBuilder();
        query.append(" "
                + " SELECT new map("
                    + " c.id AS id "
                    + ",c.status AS status "
                    + ",c.description AS title "
                    + ",c.details AS description "
                    + ",c.nodeId AS nodeId "
                    + ",createByUser.description AS createdBy "
                    + ",modifiedByUser.description AS lastModifiedBy "
                    + ",c.lastModifiedDate AS lastModifiedDate "
                    + ",c.createdDate AS createdDate "
                    + ",c.documentMasterId AS documentMasterId "
                    + ",document.id AS documentId "
                    + ",document.code AS documentCode "
                    + ",document.description AS documentDescription"
                    + ",sr.id AS slimReportId "
                + " )"
                + " FROM ").append(Report.class.getCanonicalName()).append(" c "
                + " JOIN ").append(SlimReports.class.getCanonicalName()).append(" sr on sr.reportId = c.id "
                + " LEFT JOIN ").append(User.class.getCanonicalName()).append(" createByUser ON createByUser.id = c.createdBy "
                + " LEFT JOIN ").append(User.class.getCanonicalName()).append(" modifiedByUser ON modifiedByUser.id = c.lastModifiedBy "
                + " LEFT JOIN ").append(Document.class.getCanonicalName()).append(" document"
                        + " ON "
                            + " document.masterId = c.documentMasterId"
                            + " AND document.status IN ( ").append(
                                Document.STATUS.ACTIVE.getValue()).append(" "
                                + ",").append(Document.STATUS.IN_EDITION.getValue()).append(" "
                        + " )");
        if (documentMasterId == null) {
            query.append(" c.documentMasterId IS NOT NULL ");
        } else {
            query.append(" c.documentMasterId = :documentMasterId ");
            filter.getRawCriteria().put("documentMasterId", documentMasterId);
        }
        return reportDao.reports(filter, getModule(), query.toString(), SecurityUtils.getLoggedUser());
    }
    
    @GetMapping()
    @RequestMapping("slim-report/business-unit-access/list/{slimReportId}")
    public List<Map<String, Object>> businessUnitAccessController(
            @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        Long nodeId = getNodeIdBySlimReportId(slimReportId);
        IBusinessUnitLiteDAO daoBuLite = Utilities.getBean(IBusinessUnitLiteDAO.class);
        if (nodeId == null) {
            return Utilities.EMPTY_LIST;
        }
        return daoBuLite.HQL_findByQuery("SELECT new map("
                    + "bun.id as id,"
                    + " bun.code as code,"
                    + " bun.description as description"
                + ") "
                + "FROM " + NodeAccess.class.getCanonicalName() + " n "
                + "JOIN n.businessUnits bun "
                + "WHERE n.id = :nodeId ",
                ImmutableMap.of("nodeId", nodeId));
    }
    
    @PostMapping()
    @RequestMapping( "slim-report/business-unit-access/list")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> businessUnitAccessController(
        @RequestBody GridFilter filter,
        @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        IBusinessUnitDAO.setBusinessUnitActiveFilter(loggedUser, filter);
        return reportDao.HQL_getRows(" SELECT new map( "
                + " c.id AS id "
                + ",c.code AS code "
                + ",c.description AS description "
            + " )"
            + " FROM " + BusinessUnit.class.getCanonicalName() + " c "
            , filter
        );
    }
    
    @GetMapping()
    @RequestMapping("slim-report/business-unit-department-access/list/{slimReportId}")
    public List<Map<String, Object>> businessUnitDepartmentAccessController(
            @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        Long nodeId = getNodeIdBySlimReportId(slimReportId);
        IUntypedDAO dao = Utilities.getUntypedDAO();
        return dao.HQL_findByQuery("SELECT new map "
                    + "("
                        + "dept.id as id,"
                        + "dept.code as code,"
                        + "dept.description as description"
                    + ")"
                + "FROM " + NodeAccess.class.getCanonicalName() + " n "
                + "JOIN n.departments dept "
                + "WHERE n.id = :nodeId", ImmutableMap.of("nodeId", nodeId)
        );
    }
    
    @PostMapping()
    @RequestMapping("slim-report/business-unit-department-access/list")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> businessUnitDepartmentAccessController(
        @RequestBody GridFilter filter,
        @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (slimReportId != null) {
            // TODO
        }
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        IBusinessUnitDepartmentLoadDAO dao = Utilities.getBean(IBusinessUnitDepartmentLoadDAO.class);
        dao.setValidEntitiesFilter(filter, loggedUser.getId().toString(), new ProfileServices[] {
            /* Son los mismos que en `Folder.BusinessUnitDepartment.action` */
            ProfileServices.DOCUMENTO_EDITOR, ProfileServices.DOCUMENTO_ENCARGADO
        }, loggedUser.isAdmin());
        return dao.HQL_getRows(" SELECT new map( "
                + " c.id AS id "
                + ",c.code AS code "
                + ",c.description AS description"
            + " )"
            + " FROM " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " c "
            + " WHERE c.departmentStatus = " + Department.ACTIVE_STATUS
            + " AND c.deleted = 0 "    
            , filter
        );
    }
    
    @GetMapping()
    @RequestMapping("slim-report/user-access/list/{slimReportId}")
    public List<Map<String, Object>> userAccessController(
            @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        Long nodeId = getNodeIdBySlimReportId(slimReportId);
        IUserIdNameDAO userDao = Utilities.getBean(IUserIdNameDAO.class);
        return userDao.HQL_findByQuery("SELECT new map "
                    + "("
                        + "user.id as id,"
                        + "user.code as code,"
                        + "user.cuenta as account,"
                        + "user.description as description"
                    + ")"
                + "FROM " + NodeAccess.class.getCanonicalName() + " n "
                + "JOIN n.users user "
                + "WHERE n.id = :nodeId " 
                + "AND user.status = :userStatus",
                ImmutableMap.of("nodeId", nodeId, "userStatus",  User.STATUS.ACTIVE.getValue()));
    }
    
    @PostMapping()
    @RequestMapping("slim-report/user-access/list")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> userAccessController(
        @RequestBody GridFilter filter,
        @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (slimReportId != null) {
            // TODO
        }
        ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        IUserDAO dao = Utilities.getBean(IUserDAO.class);
        dao.setValidEntitiesFilter(filter, loggedUser.getId().toString(), new ProfileServices[] {
            /* Son los mismos que en `Folder.User.action` */
            ProfileServices.DOCUMENTO_EDITOR, ProfileServices.DOCUMENTO_ENCARGADO
        }, loggedUser.isAdmin());
        return dao.HQL_getRows(" SELECT new map( "
                + " c.id AS id "
                + ",c.code AS code "
                + ",c.description AS description "
                + ",c.cuenta AS account "
                + ",c.correo AS correo "
            + " )"
            + " FROM " + User.class.getCanonicalName() + " c "
            + " WHERE "
                + " c.status = " + User.ACTIVE_STATUS
                + " AND ("
                    + " SELECT count(*)  "
                    + " FROM c.puestos p "
                + " ) > 0 "
            , filter
        );
    }
    
    @GetMapping()
    @RequestMapping("slim-report/process-access/list/{slimReportId}")
    public List<Map<String, Object>> processAccessColumnsController(
            @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        IProcessDAO dao = Utilities.getBean(IProcessDAO.class);
        Long nodeId = getNodeIdBySlimReportId(slimReportId);
        return dao.HQL_findByQuery("SELECT new map "
                    + "( "
                        + "pro.id as id,"
                        + "pro.code as code,"
                        + "pro.description as description "
                    + ")"
                + "FROM " + NodeAccess.class.getCanonicalName() + " n "
                + "JOIN n.process pro "
                + "WHERE n.id = :nodeId", ImmutableMap.of("nodeId", nodeId));
    }
    
    @PostMapping()
    @RequestMapping("slim-report/process-access/list")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> processAccessColumnsController(
        @RequestBody GridFilter filter,
        @PathVariable(value = "slimReportId", required = false) Long slimReportId
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        if (slimReportId != null) {
            // TODO
        }
        return reportDao.HQL_getRows(" SELECT new map( "
                + " c.id AS id "
                + ",c.code AS code "
                + ",c.description AS description "
            + " )"
            + " FROM " + DPMS.Mapping.Process.class.getCanonicalName() + " c "
            , filter
        );
    }

    @PostMapping()
    @RequestMapping("slim-report/profile-access/list")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> profileAccessColumnsController(
        @RequestBody GridFilter filter
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        return reportDao.HQL_getRows(" SELECT new map( "
                + " c.id AS id "
                + ",c.code AS code "
                + ",c.description AS description "
            + " )"
            + " FROM " + DPMS.Mapping.Profile.class.getCanonicalName() + " c "
            + " WHERE c.deleted = 0 "
            , filter
        );
    }

    @PostMapping()
    @RequestMapping("slim-report/profile-access/list/{slimReportId}")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> profileAccessColumnsController(
        @PathVariable(value = "slimReportId") Long slimReportId
    ) {
        Long nodeId = getNodeIdBySlimReportId(slimReportId);
        return reportDao.HQL_findByQuery(" SELECT new map( "
                + " p.id AS id "
                + ",p.code AS code "
                + ",p.description AS description "
            + " )"
            + " FROM " + NodeAccess.class.getCanonicalName() + " c "
            + " JOIN c.profiles p "
            + " WHERE p.deleted = 0 AND c.id = :nodeId",
                ImmutableMap.of("nodeId", nodeId)
        );
    }


    @GetMapping({
        "reportConfig/g/i/{reportCode}"
    })
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    public ResponseEntity<Long> reportId(
        @PathVariable(value = "reportCode", required = true) String reportCode
    ) {
        return new ResponseEntity<>(ReportHandler.getPresetReportId(formCaptureDAO, null, reportCode), HttpStatus.OK);
    }

    @Override
    @GetMapping({
            "reportConfig/c/{reportCode}",
            "reportConfig/c/{reportCode}/{masterId}",
            "reportConfig/{reportId}",
            "reportConfig/{reportId}/{masterId}"
    })
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    public ResponseEntity<ReportDTO> reportColumn(
            @PathVariable(value = "reportId", required = false) Long reportId,
            final @PathVariable(value = "masterId", required = false) String masterId,
            final @PathVariable(value = "reportCode", required = false) String reportCode
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        reportId = ReportHandler.getPresetReportId(formCaptureDAO, reportId, reportCode);
        final ReportHandler handler = new ReportHandler();
        final ReportDTO config = handler.reportColumn(reportId, getModule(), loggedUser);
        String documentMasterId;
        if (config == null) {
            getLogger().error(
                    "Report config not found for reportId {} and masterId {}",
                    reportId,
                    masterId
            );
            return new ResponseEntity(HttpStatus.CONFLICT);
        }
        if (masterId == null) {
            documentMasterId = config.getDocumentMasterId();
        } else {
            documentMasterId = masterId;
        }
        if (documentMasterId != null) {
            // validar que sean de tipo slim-report
            if (loggedUser.isAdmin()) {
                config.setActionStripOptions(ImmutableList.of(
                        "OPEN_DETAIL", "PRINTING_OPTIONS", "LOGGING"
                ));
            } else {
                config.setActionStripOptions(ImmutableList.of(
                        "OPEN_DETAIL", "PRINTING_OPTIONS"
                ));
            }
        }
        return new ResponseEntity<>(config, HttpStatus.OK);
    }

    @Override
    @PostMapping({"report/{queryId}", "report/{queryId}/{start}/{end}"})
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    public ResponseEntity<GridInfo<Map<String, Object>>> report(
            @RequestBody GridFilterByReportProcessingAccess filter,
            final @PathVariable(value = "queryId", required = false) Long databaseQueryId,
            final @PathVariable(value = "start", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
            final @PathVariable(value = "end", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") Date end
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException {
        if (filter == null) {
            filter = new GridFilterByReportProcessingAccess();
        }
        if(!filter.canRetriveData()) {
            final GridInfo<Map<String, Object>> error = new GridInfo<>();
            error.setCount(-1L);
            error.setData(Utilities.EMPTY_LIST);
            error.setStatus(GridInfoStatus.NO_ACCESS);
            return new ResponseEntity<>(error, HttpStatus.OK);
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();
        Map<String, Boolean> isSlimReportQuery = queryHandler.getSlimReportData(databaseQueryId);
        if (!isSlimReportQuery.isEmpty() && (
                filter.getField() == null || filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()
        )) {
            // Filtro por defecto, únicamente cuando es un SLIM_REPORT
            filter.setField(new GridOrderBy());
            filter.getField().setOrderBy("o.outstanding_surveys_id");
            filter.setDirection((byte)2);
        }
        GridInfo<Map<String, Object>> result = queryHandler.getRowsById(databaseQueryId, filter, start, end, isSlimReportQuery, loggedUser, null);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    
    @Override
    @PostMapping()
    @RequestMapping(path = "report-hierarchy/{queryId}/{level}", method = RequestMethod.POST)
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> hierarchLevelRows(
        @RequestBody GridFilter filter,
        final @PathVariable(value = "queryId", required = true) Long queryId,
        final @PathVariable(value = "level", required = true) Integer level
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException {
        if (filter == null) {
            filter = new GridFilter();
        }
        final SurveyParserHelper helper = new SurveyParserHelper();
        return helper.getDatabaseQueryHierarchyByLevelRows(filter, queryId, level, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping("answers")
    @PreAuthorize(HAS_ANSWERS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Report> answers(@RequestBody GridFilter filter){
        return answers(filter, getModule());
    }
    
    protected GridInfo<Report> answers(GridFilter filter, Module module){
        if (filter == null) {
            filter = new GridFilter();
        }
        final IDocumentDAO dao = Utilities.getBean(IDocumentDAO.class);
        boolean managerAccess = SecurityUtils.isLoggedUserAdmin();
        String condition =  " c.surveyId IS NOT NULL "
            + " AND c.deleted = 0 "
            + " AND c.status IN ( " 
                + Document.STATUS.ACTIVE.getValue()
                + "," + Document.STATUS.IN_EDITION.getValue()
            + ")"
        ;
        if (!managerAccess) {
            condition += HibernateDAO_Document.FOLDER_PERMISSION
                .replaceAll(":userId", SecurityUtils.getLoggedUserId().toString())
                .replaceAll(":isDocMgr", "")
                .replaceAll(":nodeId", "c.nodoId");
        }
        filter.getCriteria().put("<condition>", condition);
        return dao.HQL_getRows(" SELECT new map("
                    + " c.id AS id"
                    + ", c.status AS status"
                    + ", c.code AS title"
                    + ", c.description AS description"
                    + ", c.surveyId AS surveyId"
                    + ", c.masterId AS masterId"
                    + ", c.answersNodeId AS answersNodeId"
                    + ", c.restrictRecordsByDepartment as restrictRecordsByDepartment"
                    + ", c.validateAccessFormDepartment as validateAccessFormDepartment"
                    + ", c.restrictRecordsField as restrictRecordsField"
                    + ", c.restrictRecordsObjId as restrictRecordsObjId"
                    + ", c.version AS documentVersion"
                    + ", c.restrictRecordsField AS restrictRecordsField"
                + " )"
                + " FROM " + Document.class.getCanonicalName() + " c "
            , filter);
    }
    
    @GetMapping("report/reportListAvailables/{module}")
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<Map<String, Object>> reportListAvailables(@PathVariable(name = "module", required = true) String module) {
        return reportDao.reportListAvailables(ModuleUtil.fromKey(module), SecurityUtils.getLoggedUser());
    }
    
    @PostMapping("change-defined-user")
    @PreAuthorize(HAS_CHANGE_DEFINED_USER_ACCESS)
    public ResponseEntity<Boolean> changeDefinedUser(@RequestBody ChangeDefinedUserDto data) {
        try {
            final ChangeDefinedUserResult result = formCaptureDAO.changeDefinedUser(data, SecurityUtils.getLoggedUser());
            if (result != null && result.getDetails() != null && result.getDetails().getOldUserIds() != null && !result.getDetails().getOldUserIds().isEmpty()) {
                Set<Long> uniquesOldUserIds = new HashSet<>(result.getDetails().getOldUserIds());
                uniquesOldUserIds.forEach(id -> {
                    UserLogin loginInfo = new UserLogin(id);
                    loginInfo.removeSession(true);
                });
            }
            return new ResponseEntity<>(result != null && result.getSuccess(), HttpStatus.OK);
        } catch (Exception e) {
            getLogger().error("Failed to cahnge defined user for  outstandingSurveyId {}", data.getSectionInfo().getOutstandingSurveyId(), e);
            return new ResponseEntity<>(false, HttpStatus.BAD_REQUEST);
        }
    }
    
    
    @PostMapping()
    @RequestMapping("change-user-defined-history/{requestId}/{fieldObjectId}")
    @PreAuthorize(HAS_CHANGE_DEFINED_USER_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> changeUserDefinedHistory (
            @RequestBody GridFilter filter,
            @PathVariable(name = "requestId", required = true) Long requestId,
            @PathVariable(name = "fieldObjectId", required = true) Long fieldObjectId
    ) {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        filter.getCriteria().put("<condition>", "h.requestId = " + requestId + " AND h.fieldObjectId = " + fieldObjectId);
        filter.getField().setOrderBy("h.id");
        filter.setDirection(Byte.parseByte("1"));
        return dao.HQL_getRows("SELECT new map ("
                    + " oldUser.description AS previousUserDescription "
                    + ",newUser.description AS newUserDescription "
                    + ",createdBy.description AS createdByDescription "
                    + ",lastModifiedBy.description AS lastModifiedByDescription "
                    + ",h.createdDate AS changeDate"
                + " ) "
                + "FROM " + UserDefinedToFillHistory.class.getCanonicalName() + " h "
                    + " JOIN " + UserRef.class.getCanonicalName() + " oldUser ON oldUser.id = h.oldUserId "
                    + " JOIN " + UserRef.class.getCanonicalName() + " newUser ON newUser.id = h.newUserId "
                    + " JOIN " + UserRef.class.getCanonicalName() + " createdBy ON createdBy.id = h.createdBy "
                    + " JOIN " + UserRef.class.getCanonicalName() + " lastModifiedBy ON lastModifiedBy.id = h.lastModifiedBy "
                , filter);
    }
    
    private Module getModule() {
        return Module.FORMULARIE;
    }
    
    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_CANCEL_AUTHORIZER', 'SPECIAL_FORM_CANCELATION_APPROVER'"
        + ")")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @RequestMapping("cancel/authorize")
    public ResponseEntity<SaveHandle> verifyFormRequestCancelApproved(
        final @RequestBody TextLongValue dto
    ) throws IOException, QMSException, MakePersistentException, BatchUpdateException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        Long outstandingSurveysId = entity.getOutstandingSurveysId();
        Long requestId = entity.getOutstandingSurvey().getRequestId();
        ResponseEntity<SaveHandle> sh = formRequestDAO.verifyFormRequestCancelApproved(entity, authRole, reason, loggedUser);
        if (sh.getStatusCode().equals(HttpStatus.OK)) {
            IRequestDAO daoRequest = Utilities.getBean(IRequestDAO.class);
            IOutstandingSurveysDAO daoSurveys = Utilities.getBean(IOutstandingSurveysDAO.class);
            String requestorName = StringUtils.trim(entity.getCreatedByUser().getDescription());
            String requestCancelReazon;
            if (daoRequest.isUserTheAuthor(requestId, loggedUser.getId())) {
                requestCancelReazon = LocaleUtil.getTag("reazonCancelFillByAuthor", tags).replace(":user", requestorName);
            } else {
                requestCancelReazon = LocaleUtil.getTag("reazonCancelFillGeneric", tags).replace(":user", requestorName);
            }
            requestCancelReazon = requestCancelReazon.replace(":comment", entity.getDescription()).replace(":user", requestorName);
            String authorizatorName = StringUtils.trim(loggedUser.getDescription());
            String authCancelReazon;
            if (daoRequest.isUserTheAuthor(requestId, loggedUser.getId())) {
                authCancelReazon = LocaleUtil.getTag("authCancelFillByAuthor", tags).replace(":user", authorizatorName);
            } else {
                authCancelReazon = LocaleUtil.getTag("authCancelFillGeneric", tags).replace(":user", authorizatorName);
            }
            authCancelReazon = authCancelReazon.replace(":comment", reason).replace(":user", authorizatorName);
            SurveyUtil.cancelFillForm(daoRequest, daoSurveys, outstandingSurveysId, requestId, reason, requestCancelReazon + "\n" + authCancelReazon, loggedUser);
        }
        return sh;
    }

    @PostMapping()
    @PreAuthorize("hasAnyAuthority("
            + "'IS_ADMIN', "
            + "'FORMULARIO_CONTROL', 'SPECIAL_FORM_CANCEL_AUTHORIZER', 'SPECIAL_FORM_CANCELATION_APPROVER'"
        + ")")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @RequestMapping("cancel/reject")
    public ResponseEntity<SaveHandle> verifyFormRequestCancelRejected(
        final @RequestBody TextLongValue dto
    ) throws IOException, QMSException, MakePersistentException, BatchUpdateException {
        final Long formRequestId = dto.getValue();
        final String reason = dto.getText();
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final FormRequest entity = formRequestDAO.HQLT_findById(FormRequest.class, formRequestId);
        final WorkflowAuthRole authRole = formRequestDAO.getRequestAuthRole(entity, loggedUser);
        ResponseEntity<SaveHandle> sh =  formRequestDAO.verifyFormRequestCancelRejected(entity, authRole, reason, loggedUser);
        if (sh.getStatusCode().equals(HttpStatus.OK)) {
            IUntypedDAO dao = Utilities.getUntypedDAO();
            Long outstandingId = entity.getOutstandingSurveysId();
            dao.HQL_updateByQuery("UPDATE " + OutstandingSurveys.class.getCanonicalName() + " os "
                    + " SET os.surveyProgressStateCancelId = null "
                + " WHERE os.id = :id", 
                ImmutableMap.of("id", outstandingId)
            );
        }
        return sh;
    }
    
    @PostMapping()
    @RequestMapping("request-form-cancel/{cancelStatusId}")
    @PreAuthorize(" ("
                + "hasAnyAuthority("
                    + "'IS_ADMIN' "
                + ") OR @FormController.hasFormPending(#dto.outstandingSurveysId)"
            + " ) "
            + " AND @FormController.isCancelAvailable(#dto.outstandingSurveysId)"
    )
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<FormRequestResponseDTO> requestFormCancel(
        @RequestBody FormRequestDTO dto,
        @PathVariable (name = "cancelStatusId", required = true) Long cancelStatusId
    ) throws MakePersistentException, BatchUpdateException, QMSException {
        final Long outstandingSurveysId = dto.getOutstandingSurveysId();
        // Solo se permite una solicitud a la vez por registro de formulario
        synchronized(OUTSTANDING_SAVE_LOCKS.getUnchecked(outstandingSurveysId)) {
            return formCaptureDAO.requestFormCancel(dto, cancelStatusId, SecurityUtils.getLoggedUser());
        }
    }


    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @GetMapping({
        "slim-report/bulk/upload-template/{documentMasterId}/c/{reportCode}/{bulkLogId}",
        "slim-report/bulk/upload-template/{documentMasterId}/i/{reportId}/{bulkLogId}"
    })
    public ResponseEntity<Void> downloadBulkResult(
            final HttpServletResponse response,
            final @PathVariable(value = "documentMasterId", required = false) String documentMasterId,
            final @PathVariable(value = "reportCode", required = false) String reportCode,
            final @PathVariable(value = "bulkLogId", required = true) Long bulkLogId,
            @PathVariable(value = "reportId", required = false) Long reportId
    ) throws IllegalStateException, IOException, QMSException {
        reportId = ReportHandler.getPresetReportId(formCaptureDAO, reportId, reportCode);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (reportId == null || reportId == -1L) {
            return downloadBulkTemplate(response, documentMasterId, reportCode, reportId);
        } else {
            final Long resultFileId = reportDao.HQL_findLong(" "
                + " SELECT c.resultFileId "
                + " FROM " + BulkLog.class.getCanonicalName() + " c"
                + " WHERE c.id = :bulkLogId", ImmutableMap.of("bulkLogId", bulkLogId), false, null, 0
            );
            return FileManager.download(response, resultFileId, loggedUser);
        }
    }

    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @GetMapping({
        "slim-report/bulk/original-template/{documentMasterId}/c/{reportCode}/{bulkLogId}",
        "slim-report/bulk/original-template/{documentMasterId}/i/{reportId}/{bulkLogId}"
    })
    public ResponseEntity<Void> downloadBulkOriginal(
            final HttpServletResponse response,
            final @PathVariable(value = "documentMasterId", required = false) String documentMasterId,
            final @PathVariable(value = "reportCode", required = false) String reportCode,
            final @PathVariable(value = "bulkLogId", required = true) Long bulkLogId,
            @PathVariable(value = "reportId", required = false) Long reportId
    ) throws IllegalStateException, IOException, QMSException {
        reportId = ReportHandler.getPresetReportId(formCaptureDAO, reportId, reportCode);
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (reportId == null || reportId == -1L) {
            return downloadBulkTemplate(response, documentMasterId, reportCode, reportId);
        } else {
            final Long sourceFileId = reportDao.HQL_findLong(" "
                + " SELECT c.sourceFileId "
                + " FROM " + BulkLog.class.getCanonicalName() + " c"
                + " WHERE c.id = :bulkLogId", ImmutableMap.of("bulkLogId", bulkLogId), false, null, 0
            );
            return FileManager.download(response, sourceFileId, loggedUser);
        }
    }

    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @PostMapping({
        "slim-report/bulk/upload-template/{documentMasterId}/c/{reportCode}",
        "slim-report/bulk/upload-template/{documentMasterId}/i/{reportId}"
    })
    public ResponseEntity<Map<String, Object>> uploadBulkTemplate(
            final HttpServletResponse response,
            final @RequestParam("file") MultipartFile multipart,
            final @PathVariable(value = "documentMasterId", required = false) String documentMasterId,
            final @PathVariable(value = "reportCode", required = false) String reportCode,
            @PathVariable(value = "reportId", required = false) Long reportId
    ) throws IllegalStateException, IOException, QMSException, BulkError {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return getBulkHandler(loggedUser, documentMasterId, reportCode, reportId).uploadTemplate(response, multipart, loggedUser);
    }

    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    @GetMapping({
    "slim-report/bulk/download-template/{documentMasterId}/c/{reportCode}",
    "slim-report/bulk/download-template/{documentMasterId}/i/{reportId}"
    })
    public ResponseEntity<Void> downloadBulkTemplate(
            final HttpServletResponse response,
            final @PathVariable(value = "documentMasterId", required = true) String documentMasterId,
            final @PathVariable(value = "reportCode", required = false) String reportCode,
            @PathVariable(value = "reportId", required = false) Long reportId
    ) throws IllegalStateException, IOException, QMSException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return getBulkHandler(loggedUser, documentMasterId, reportCode, reportId).downloadTemplate(response, loggedUser);
    }

    private FormBulkHandler getBulkHandler(
            final ILoggedUser loggedUser,
            final String documentMasterId,
            final String reportCode,
            Long reportId
    ) throws IllegalStateException, QMSException {
        reportId = ReportHandler.getPresetReportId(formCaptureDAO, reportId, reportCode);
        final Locale locale = SecurityUtils.getLoggedUserLocaleObject();
        final String url = SettingsUtil.getAppUrl();
        final String appName = AboutApp.getAppName();
        final Long surveyId = Utilities.getBean(ISlimReportsDAO.class).getActiveSurveyId(documentMasterId);
        return new FormBulkHandler(locale, reportId, surveyId,  documentMasterId, url, appName, loggedUser);
    }

    @GetMapping
    @RequestMapping("survey-progress-state-cancel/{requestId}/{outstandingSurveyId}")
    @PreAuthorize(" ("
                + "hasAnyAuthority("
                    + "'IS_ADMIN', 'FORM_CANCEL_ANY' "
                + ") OR @FormController.hasFormPending(#outstandingSurveyId)"
            + " ) "
            + " AND @FormController.isCancelAvailable(#outstandingSurveyId)"
    )
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> surveyProgressStateCancelList(
        @PathVariable(name = "requestId", required = true) Long requestId,
        @PathVariable(name = "outstandingSurveyId", required = true) Long outstandingSurveyId
    ) {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        
        Long authIndex = dao.HQL_findSimpleLong(" SELECT min(detail.indice) "
                    + HibernateDAO_Request.HQL_FILL_FORMS_DETAIL_DTO
                            .substring(HibernateDAO_Request.HQL_FILL_FORMS_DETAIL_DTO.toLowerCase().indexOf(" from "))
                + " WHERE"
                    + " request.deleted = 0"
                    + " AND request.id = :requestId"
                    + " AND detail.accepted is null ",
                "requestId", requestId
        );
        
        
        List<Map<String, Object>> statusList = dao.HQL_findByQuery("SELECT new map("
                + " fps.id AS value "
                + ", fps.description AS text "
            + ") FROM " + OutstandingSurveysAttendant.class.getCanonicalName() + " ousa "
                + " JOIN " + SurveyProgressStateCancel.class.getCanonicalName() + " sp ON sp.fieldObject = ousa.fieldObjectId "
                + " JOIN " + FormProgressState.class.getCanonicalName() + " fps ON fps.id = sp.id.formProgressStateId "
            + " WHERE ousa.requestId = :requestId AND "
                + " ousa.outstandingSurveyId = :outstandingSurveyId AND "
                + " ousa.fillAutorizationPoolIndex = :fillAutorizationPoolIndex " ,
            ImmutableMap.of(
                    "requestId", requestId,
                    "outstandingSurveyId", outstandingSurveyId,
                    "fillAutorizationPoolIndex", authIndex
            )
        );

        // Configuration does not exist and by default loads all
        if (statusList.isEmpty()) {
            statusList = dao.HQL_findByQuery("SELECT new map("
                    + " fps.id AS value "
                    + ", fps.description AS text "
                + ") FROM " + FormProgressState.class.getCanonicalName() + " fps ");
        }
        
        return statusList;
    }

    private Long getNodeIdBySlimReportId(Long slimReportId) {
        Long reportIdFromSlim = reportDao.HQL_findLong(" "
                + "SELECT "
                    + "sr.reportId "
                + "FROM " + SlimReports.class.getCanonicalName() + " sr "
                + "WHERE sr.id = :slimReportId ", 
                ImmutableMap.of("slimReportId", slimReportId)
        );
        return reportDao.HQL_findLong(" "
                + "SELECT "
                    + "r.nodeId "
                + "FROM " + Report.class.getCanonicalName() + " r "
                + "WHERE r.id = :reportId ",
                ImmutableMap.of("reportId", reportIdFromSlim),
                true, CacheRegion.REPORT, 0
        );
    }

    @PostMapping(
            "conditional-setup/{mode}/{surveyType}/{outstandingSurveyId}/{conditionalValidatorCacheId}"
    )
    public List<ResultDTO> conditionalSetUp (
            final @PathVariable(name = "mode") String mode,
            final @PathVariable(name = "surveyType") String surveyType,
            final @PathVariable(name = "outstandingSurveyId") Long outstandingSurveyId,
            final @PathVariable(name = "conditionalValidatorCacheId") String conditionalValidatorCacheId,
            final @RequestBody List<FieldValueChangeDTO> fields
    ) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(
                    "conditionalSetUp: mode={}, surveyType={}, outstandingSurveyId={}, conditionalValidatorCacheId={}, field={}",
                    mode, surveyType, outstandingSurveyId, conditionalValidatorCacheId, fields
            );
        }
        final Module module = Module.AUDIT.getKey().equals(surveyType) ? Module.AUDIT : Module.FORMULARIE;
        final SurveyRequestMode requestMode = SurveyRequestMode.parse(mode);
        final ConditionalValidatorCache instance = ConditionalValidatorCache.getInstance();
        final ConditionalValidator validator = instance.get(
                outstandingSurveyId,
                requestMode,
                conditionalValidatorCacheId,
                module
        );
        return validator.conditionalSetUp(fields);
    }

    @PostMapping(
            "conditional-list/{mode}/{surveyType}/{outstandingSurveyId}/{conditionalValidatorCacheId}"
    )
    public List<ResultDTO> conditionalList(
            final @PathVariable(name = "mode") String mode,
            final @PathVariable(name = "surveyType") String surveyType,
            final @PathVariable(name = "outstandingSurveyId") Long outstandingSurveyId,
            final @PathVariable(name = "conditionalValidatorCacheId") String conditionalValidatorCacheId,
            final @RequestBody List<FieldValueChangeDTO> fields
    ) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(
                    "conditionalList: mode={}, surveyType={}, outstandingSurveyId={}, conditionalValidatorCacheId={}, fieldsValueChangeDTO={}",
                    mode, surveyType, outstandingSurveyId, conditionalValidatorCacheId, fields
            );
        }
        final ConditionalValidator validator = ConditionalValidatorCache.loadInstance(
                mode,
                surveyType,
                outstandingSurveyId,
                conditionalValidatorCacheId
        );
        return validator.getFieldValueChanges(fields);
    }

    @PostMapping(
            "weighting/{mode}/{surveyType}/{outstandingSurveyId}/{conditionalValidatorCacheId}"
    )
    public WeightingResultDTO weighting(
            final @PathVariable(name = "mode") String mode,
            final @PathVariable(name = "surveyType") String surveyType,
            final @PathVariable(name = "outstandingSurveyId") Long outstandingSurveyId,
            final @PathVariable(name = "conditionalValidatorCacheId") String conditionalValidatorCacheId,
            final @RequestBody List<FieldValueChangeDTO> fields
    ) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(
                    "weighting: mode={}, surveyType={}, outstandingSurveyId={}, conditionalValidatorCacheId={}, fieldsValueChangeDTO={}",
                    mode, surveyType, outstandingSurveyId, conditionalValidatorCacheId, fields
            );
        }
        final ConditionalValidator validator = ConditionalValidatorCache.loadInstance(
                mode,
                surveyType,
                outstandingSurveyId,
                conditionalValidatorCacheId
        );
        return validator.getWeighting(fields);
    }

    @PostMapping(
            "conditional-remove/{mode}/{surveyType}/{outstandingSurveyId}/{conditionalValidatorCacheId}"
    )
    public List<String> conditionalListRemoved(
            final @PathVariable(name = "mode") String mode,
            final @PathVariable(name = "surveyType") String surveyType,
            final @PathVariable(name = "outstandingSurveyId") Long outstandingSurveyId,
            final @PathVariable(name = "conditionalValidatorCacheId") String conditionalValidatorCacheId,
            final @RequestBody List<String> fieldsToRemove
    ) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(
                    "conditionalListRemoved: mode={}, surveyType={}, outstandingSurveyId={}, conditionalValidatorCacheId={}, fieldsToRemove={}",
                    mode, surveyType, outstandingSurveyId, conditionalValidatorCacheId, fieldsToRemove
            );
        }
        final ConditionalValidator validator = ConditionalValidatorCache.loadInstance(
                mode,
                surveyType,
                outstandingSurveyId,
                conditionalValidatorCacheId
        );
        return validator.conditionalListRemoved(fieldsToRemove);
    }

    @PostMapping({
            "fill-out-logging/{outstandingSurveysId}",
    })
    @PreAuthorize(HAS_REPORTS_VIEW_ACCESS)
    public GridInfo<Map<String, Object>> formLogLists(
            @RequestBody GridFilter filter,
            @PathVariable(value = "outstandingSurveysId") Long outstandingSurveysId
    ) {
        final LoggingHandler logginHandler = new LoggingHandler();
        return logginHandler.getOutstandingLogSaveRows(outstandingSurveysId, filter);
    }

}
