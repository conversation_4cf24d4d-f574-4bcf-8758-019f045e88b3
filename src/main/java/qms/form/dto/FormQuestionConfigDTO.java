package qms.form.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.interfaces.IOutstandingQuestion;
import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import javax.annotation.Nonnull;
import qms.form.dto.conditional.FieldValueChangeDTO;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class FormQuestionConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Nonnull
    private List<FieldValueChangeDTO> changes;
    @Nonnull
    private LinkedHashMap<String, Object> params;
    private Set<SurveyDataFieldDTO> fields;
    private IOutstandingQuestion outstandingQuestion;
    private SurveyField.TYPE type;
    private Long surveyId;
    private Long outstandingSurveysId;

    public FormQuestionConfigDTO() {
    }

    public FormQuestionConfigDTO(
            final List<FieldValueChangeDTO> changes,
            final LinkedHashMap<String, Object> params,
            final Set<SurveyDataFieldDTO> fields,
            final IOutstandingQuestion outstandingQuestion,
            final SurveyField.TYPE type,
            final Long surveyId,
            final Long outstandingSurveysId
    ) {
        this.changes = changes;
        this.params = params;
        this.fields = fields;
        this.outstandingQuestion = outstandingQuestion;
        this.type = type;
        this.surveyId = surveyId;
        this.outstandingSurveysId = outstandingSurveysId;
    }

    public List<FieldValueChangeDTO> getChanges() {
        return changes;
    }

    public void setChanges(List<FieldValueChangeDTO> changes) {
        this.changes = changes;
    }

    public LinkedHashMap<String, Object> getParams() {
        return params;
    }

    public void setParams(LinkedHashMap<String, Object> params) {
        this.params = params;
    }

    public Set<SurveyDataFieldDTO> getFields() {
        return fields;
    }

    public void setFields(Set<SurveyDataFieldDTO> fields) {
        this.fields = fields;
    }

    public IOutstandingQuestion getOutstandingQuestion() {
        return outstandingQuestion;
    }

    public void setOutstandingQuestion(IOutstandingQuestion outstandingQuestion) {
        this.outstandingQuestion = outstandingQuestion;
    }

    public SurveyField.TYPE getType() {
        return type;
    }

    public void setType(SurveyField.TYPE type) {
        this.type = type;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

}
