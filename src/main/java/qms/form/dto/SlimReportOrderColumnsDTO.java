/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.form.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SlimReportOrderColumnsDTO {
    private String id;
    private String description;
    private Integer order;
    private Boolean fixedField;

    public SlimReportOrderColumnsDTO() {
    }

    public SlimReportOrderColumnsDTO(String id, String description, Integer order, Boolean fixedField) {
        this.id = id;
        this.description = description;
        this.order = order;
        this.fixedField = fixedField;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Boolean getFixedField() {
        return fixedField;
    }

    public void setFixedField(Boolean fixedField) {
        this.fixedField = fixedField;
    }
    
    
}
