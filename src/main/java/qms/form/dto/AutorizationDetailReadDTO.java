package qms.form.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AutorizationDetailReadDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String description;
    private Integer status;
    private Long userId;

    public AutorizationDetailReadDTO(
            String description, 
            Integer status,
            Long userId) {
        this.description = description;
        this.status = status;
        this.userId = userId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
}
