/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.form.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import qms.custom.dto.IFlexiFields;
import qms.framework.entity.SlimReportsGhostField;
import qms.framework.entity.SlimReportsTransformedField;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class FormSlimReportDTO implements IFlexiFields {
    private Long id;
    private String code;
    private String description;
    private String details;
    private String documentMasterId;
    private List<String> surveyFields;      // <--- Son los nombres de las columnas de FORM_ANSWERS_## - (Es un `List` para mantener el orden)
    private List<String> surveyFilters;      // <--- Son los nombres de las columnas de FORM_ANSWERS_## - (Es un `List` para mantener el orden)
    private Set<Integer> fixedFields;       // <--- Son los valores del ENUM de `SlimReportFixedField`
    private Set<Integer> fixedFilters;      // <--- Son los valores del ENUM de `SlimReportFixedField`
    private List<String> relatedFields;
    private Set<Long> businessUnitAccessValues;
    private Set<Long> businessUnitDepartmentAccessValues;
    private Set<Long> userAccessValues;
    private Set<Long> processAccessValues;
    private Boolean restrictRecordsByDepartment;
    private Boolean whereFillerUserParticipate;
    private boolean truncateColumnsToMax = false;
    private List<SlimReportOrderColumnsDTO> orderColumns;
    private Integer countPrintFormats;
    private String json;
    private Boolean localTimeForDates = false;
    private Boolean excelUploadEnabled;
    private String excelIdFields;
    private Set<SlimReportsGhostField> ghostFields = new HashSet<>();
    private Set<SlimReportsTransformedField> transformedFields = new HashSet<>();

    public FormSlimReportDTO() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    public Set<Integer> getFixedFields() {
        return fixedFields;
    }

    public void setFixedFields(Set<Integer> fixedFields) {
        this.fixedFields = fixedFields;
    }

    public Set<Integer> getFixedFilters() {
        return fixedFilters;
    }

    public void setFixedFilters(Set<Integer> fixedFilters) {
        this.fixedFilters = fixedFilters;
    }

    public List<String> getSurveyFields() {
        return surveyFields;
    }

    public void setSurveyFields(List<String> surveyFields) {
        this.surveyFields = surveyFields;
    }

    public List<String> getSurveyFilters() {
        return surveyFilters;
    }

    public void setSurveyFilters(List<String> surveyFilters) {
        this.surveyFilters = surveyFilters;
    }

    @Override
    public List<String> getRelatedFields() {
        return relatedFields;
    }

    public void setRelatedFields(List<String> relatedFields) {
        this.relatedFields = relatedFields;
    }

    public Set<Long> getBusinessUnitAccessValues() {
        return businessUnitAccessValues;
    }

    public void setBusinessUnitAccessValues(Set<Long> businessUnitAccessValues) {
        this.businessUnitAccessValues = businessUnitAccessValues;
    }

    public Set<Long> getBusinessUnitDepartmentAccessValues() {
        return businessUnitDepartmentAccessValues;
    }

    public void setBusinessUnitDepartmentAccessValues(Set<Long> businessUnitDepartmentAccessValues) {
        this.businessUnitDepartmentAccessValues = businessUnitDepartmentAccessValues;
    }

    public Set<Long> getUserAccessValues() {
        return userAccessValues;
    }

    public void setUserAccessValues(Set<Long> userAccessValues) {
        this.userAccessValues = userAccessValues;
    }

    public Set<Long> getProcessAccessValues() {
        return processAccessValues;
    }

    public void setProcessAccessValues(Set<Long> processAccessValues) {
        this.processAccessValues = processAccessValues;
    }

    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    public Boolean getWhereFillerUserParticipate() {
        return whereFillerUserParticipate;
    }

    public void setWhereFillerUserParticipate(Boolean whereFillerUserParticipate) {
        this.whereFillerUserParticipate = whereFillerUserParticipate;
    }

    public List<SlimReportOrderColumnsDTO> getOrderColumns() {
        return orderColumns;
    }

    public void setOrderColumns(List<SlimReportOrderColumnsDTO> orderColumns) {
        this.orderColumns = orderColumns;
    }

    public boolean isTruncateColumnsToMax() {
        return truncateColumnsToMax;
    }

    public void setTruncateColumnsToMax(boolean truncateColumnsToMax) {
        this.truncateColumnsToMax = truncateColumnsToMax;
    }
    
    @Override
    @JsonIgnore
    public List<String> getFlexiFields() {
        return surveyFields;
    }

    @Override
    @JsonIgnore
    public List<String> getFlexiFilters() {
        return surveyFilters;
    }

    public Integer getCountPrintFormats() {
        return countPrintFormats;
    }

    public void setCountPrintFormats(Integer countPrintFormats) {
        this.countPrintFormats = countPrintFormats;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public Boolean getLocalTimeForDates() {
        return localTimeForDates;

    }

    public void setLocalTimeForDates(Boolean localTimeForDates) {
        this.localTimeForDates = localTimeForDates;
    }

    public Boolean getExcelUploadEnabled() {
        return excelUploadEnabled;
    }

    public void setExcelUploadEnabled(Boolean excelUploadEnabled) {
        this.excelUploadEnabled = excelUploadEnabled;
    }

    public String getExcelIdFields() {
        return excelIdFields;
    }

    public void setExcelIdFields(String excelIdFields) {
        this.excelIdFields = excelIdFields;
    }

    public Set<SlimReportsGhostField> getGhostFields() {
        return ghostFields;
    }

    public void setGhostFields(Set<SlimReportsGhostField> ghostFields) {
        this.ghostFields = ghostFields;
    }


    public Set<SlimReportsTransformedField> getTransformedFields() {
        return transformedFields;
    }

    public void setTransformedFields(Set<SlimReportsTransformedField> transformedFields) {
        this.transformedFields = transformedFields;
    }
}
