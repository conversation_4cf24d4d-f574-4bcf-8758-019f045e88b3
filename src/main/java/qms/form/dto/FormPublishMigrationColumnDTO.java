package qms.form.dto;

public class FormPublishMigrationColumnDTO {

    private Long id;
    private Integer status;
    private String code;
    private String description;

    private Long rootDocumentId;
    private Long targetDocumentId;
    private String rootDocumentMasterId;
    private String rootDocumentVersion;
    private String rootFieldColumnName;
    private String targetDocumentVersion;
    private String targetFieldColumnName;

    public FormPublishMigrationColumnDTO() {
    }

    public FormPublishMigrationColumnDTO(
            Long id, Integer status, String code, String description, Long rootDocumentId, Long targetDocumentId, String rootDocumentMasterId, String rootDocumentVersion, String rootFieldColumnName, String targetDocumentVersion, String targetFieldColumnName
    ) {
        this.id = id;
        this.status = status;
        this.code = code;
        this.description = description;
        this.rootDocumentId = rootDocumentId;
        this.targetDocumentId = targetDocumentId;
        this.rootDocumentMasterId = rootDocumentMasterId;
        this.rootDocumentVersion = rootDocumentVersion;
        this.rootFieldColumnName = rootFieldColumnName;
        this.targetDocumentVersion = targetDocumentVersion;
        this.targetFieldColumnName = targetFieldColumnName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getRootDocumentId() {
        return rootDocumentId;
    }

    public void setRootDocumentId(Long rootDocumentId) {
        this.rootDocumentId = rootDocumentId;
    }

    public Long getTargetDocumentId() {
        return targetDocumentId;
    }

    public void setTargetDocumentId(Long targetDocumentId) {
        this.targetDocumentId = targetDocumentId;
    }

    public String getRootDocumentMasterId() {
        return rootDocumentMasterId;
    }

    public void setRootDocumentMasterId(String rootDocumentMasterId) {
        this.rootDocumentMasterId = rootDocumentMasterId;
    }

    public String getRootDocumentVersion() {
        return rootDocumentVersion;
    }

    public void setRootDocumentVersion(String rootDocumentVersion) {
        this.rootDocumentVersion = rootDocumentVersion;
    }

    public String getRootFieldColumnName() {
        return rootFieldColumnName;
    }

    public void setRootFieldColumnName(String rootFieldColumnName) {
        this.rootFieldColumnName = rootFieldColumnName;
    }

    public String getTargetDocumentVersion() {
        return targetDocumentVersion;
    }

    public void setTargetDocumentVersion(String targetDocumentVersion) {
        this.targetDocumentVersion = targetDocumentVersion;
    }

    public String getTargetFieldColumnName() {
        return targetFieldColumnName;
    }

    public void setTargetFieldColumnName(String targetFieldColumnName) {
        this.targetFieldColumnName = targetFieldColumnName;
    }
}
