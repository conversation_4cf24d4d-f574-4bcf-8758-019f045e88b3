package qms.form.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.annotation.Nonnull;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SequenceDetailDTO implements Serializable, Comparable<SequenceDetailDTO> {

    private static final long serialVersionUID = 1L;

    private Integer sequenceIndex;
    private Integer status;
    private String userDescription;
    private Long userId;

    public SequenceDetailDTO(Integer sequenceIndex, Integer status, String userDescription, Long userId) {
        this.sequenceIndex = sequenceIndex;
        this.status = status;
        this.userDescription = userDescription;
        this.userId = userId;
    }
    
    public Integer getSequenceIndex() {
        return sequenceIndex;
    }

    public void setSequenceIndex(Integer sequenceIndex) {
        this.sequenceIndex = sequenceIndex;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getUserDescription() {
        return userDescription;
    }

    public void setUserDescription(String userDescription) {
        this.userDescription = userDescription;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public int compareTo(@Nonnull SequenceDetailDTO other) {
        if (this.sequenceIndex == null || this.userDescription == null) {
            return -1;
        }
        int compare = this.sequenceIndex.compareTo(other.getSequenceIndex());
        if (compare != 0) {
            return compare;
        }
        return this.userDescription.compareTo(other.getUserDescription());
    }

    
}
