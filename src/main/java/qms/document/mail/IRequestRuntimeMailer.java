package qms.document.mail;

import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.Request;
import DPMS.Mapping.WorkflowPool;
import java.util.List;
import qms.access.dto.ILoggedUser;
import qms.workflow.util.WorkflowSupported;

/**
 *
 * <AUTHOR>
 */
public interface IRequestRuntimeMailer {

    void requestNewDocument(Request sol, ILoggedUser loggedUser);

    void requestDocumentModification(Request sol, ILoggedUser loggedUser);

    void requestDocumentApproval(Request sol, ILoggedUser loggedUser);
    
    void requestDocumentCancellation(Request sol, ILoggedUser loggedUser);

    void requestNewManager(Request sol, ILoggedUser loggedUser);

    void requestVerified(Request sol, ILoggedUser loggedUser);
    
    void requestVerifiedPublished(Request sol, ILoggedUser loggedUser);

    void newDocumentPublished(Request sol, ILoggedUser loggedUser);

    void documentModified(Request sol, ILoggedUser loggedUser);

    void documentDetailsEdited(Request sol, ILoggedUser loggedUser);

    void documentReapproved(Request sol, ILoggedUser loggedUser);

    void documentCancelled(Request sol, String originalCode, ILoggedUser loggedUser);

    void rejectAuthorization(Request sol, String originalCode, ILoggedUser loggedUser, String comment);

    void rejectedVerification(Long requestId, ILoggedUser loggedUser);

    void nextAuthorizeNewDocument(Request sol, ILoggedUser loggedUser, WorkflowPool poolDetails);

    void nextAuthorizeModification(Request sol, ILoggedUser loggedUser, WorkflowPool poolDetails);

    void nextAuthorizeCancellation(Request sol, ILoggedUser loggedUser, WorkflowPool poolDetails);

    void nextAuthorizeReapproval(Request sol, ILoggedUser loggedUser, WorkflowPool poolDetails);

    void changeBusinessDocumentManager(Long newDocumentManagerId, ILoggedUser loggedUser);
    
    void changeOrganizationDocumentManager(Long newDocumentManagerId, ILoggedUser loggedUser);
    
    void changeDepartmentDocumentManager(Long newDocumentManagerId, ILoggedUser loggedUser);

    void jobAdded(Long userId, List<Long> jobs, ILoggedUser loggedUser);
    
    void automaticPositionAuthorization(Request request, AutorizationPoolDetails apd, String positionDescription, ILoggedUser loggedUser);

    void automaticUserAuthorization(Request request, AutorizationPoolDetails apd, ILoggedUser loggedUser);
    
    void authorizationByInactiveUser(WorkflowSupported target, List<AutorizationPoolDetails> set, Long requestId, ILoggedUser loggedUser);
    
    void toDeliverPhysicalCopy(Long documentId, Long requestId, ILoggedUser loggedUser);
    
}
