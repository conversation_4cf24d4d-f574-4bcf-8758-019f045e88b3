package qms.document.mail;

import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.text.Normalizer;
import java.util.ResourceBundle;
import javax.mail.Message;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import qms.access.dto.LoggedUser;
import qms.document.dto.ShareDocumentDTO;
import qms.framework.core.Mailer;
import qms.framework.mail.MailDTO;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class ShareDocumentMailer extends Mailer implements IShareDocumentMailer {

    private final String _template;
    private final ResourceBundle _tags;

    public ShareDocumentMailer(IUntypedDAO dao) {
        super(dao);
        super.loadTags(getClass().getCanonicalName());
        super.setHeader(getTag("header"));
        super.setSubject(getTag("subject"));
        super.setGenericPath(getClass().getCanonicalName());
        _template = getTemplate(getGenericPath());
        _tags = getTags();
    }

    private String getThisTemplate(ShareDocumentDTO shareDocument) {
        shareDocument.setEmailMessage(normalizingText(shareDocument.getEmailMessage()));
        return mapEntity(shareDocument, _template, _tags);
    }

    @Override
    public Boolean sendSharedDocument(ShareDocumentDTO shareDocument, LoggedUser loggedUser) {
        if (!loggedUser.isAdmin() && !loggedUser.getServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            getLogger().error("User is not authorized with DOCUMENT_SHARE");
            return false;
        }
        try {
            MailDTO mail = getMail(shareDocument, loggedUser);
            send(mail, Module.DOCUMENT.getKey(), TYPE.SHARE, getMailTags());
            return true;
        } catch (Exception ex) {
            getLogger().error(ex.getMessage(), ex);
            return false;
        }
    }

    private MailDTO getMail(ShareDocumentDTO shareDocument, LoggedUser loggedUser) {

        String template = getThisTemplate(shareDocument);
        String urlSended = shareDocument.getEmailAttachType().getValue() == 1 ? shareDocument.getShareLink() : shareDocument.getViewerLink();
        String urlDescription = (shareDocument.getEmailAttachType().getValue() == 1 ? getTag("label.urlDownload") : getTag("label.urlViewer"));        
        template = template.replace("{urlSended}", urlSended);
        template = template.replace("{urlDescription}", urlDescription);
        template = template.replace("{systemColorLight}", Utilities.getSystemColorLight6());
        
        MailDTO mailDTO = new MailDTO();
        mailDTO.setMessage(template);
        mailDTO.setMessageFooter("");
        mailDTO.setMessageTitle(getTag("label.userShare").replace("{user}", loggedUser.getDescription()).replace("{documentDescription}", shareDocument.getDocumentDescription()).replace("{documentCode}", shareDocument.getDocumentCode()));        
        mailDTO.setMessageModuleTitle(getTag("header"));
        mailDTO.setSubject(shareDocument.getEmailSubjectText());
        String[] arrEmailsTO = shareDocument.getEmailToText().split(";");
        String[] arrEmailsCC = shareDocument.getEmailCCText().split(";");

        for (String email : arrEmailsTO) {
            mailDTO.getRecipients().add(new Mail(email, email, Message.RecipientType.TO));
        }

        for (String email : arrEmailsCC) {
            mailDTO.getRecipients().add(new Mail(email, email, Message.RecipientType.CC));
        }
        return mailDTO;
    }

    private String normalizingText(String chain) {
        String remove = "áàäéèëíìïóòöúùuñÁÀÄÉÈËÍÌÏÓÒÖÚÙÜÑçÇ";
        String ascii = "aaaeeeiiiooouuunAAAEEEIIIOOOUUUNcC";
        for (int i = 0; i < remove.length(); i++) {
            chain = chain.replace(remove.charAt(i), ascii.charAt(i));
        }
        return Normalizer.normalize(chain, java.text.Normalizer.Form.NFKD);
    }
}
