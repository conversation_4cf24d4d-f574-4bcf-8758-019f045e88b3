package qms.document.mail;

import DPMS.DAOInterface.ISurveysDAO;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.OutstandingSurveysAttendantLoad;
import DPMS.Mapping.Request;
import DPMS.Mapping.User;
import Framework.Config.ITextHasValue;
import Framework.Config.Mail;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.IPendingOperation;
import ape.pending.core.PendingMailerType;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import ape.pending.util.SqlQueryParser;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.SurveyField;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import qms.access.dto.ILoggedUser;
import qms.document.dto.FormAnswerDTO;
import qms.document.dto.FormAnswerForMailDTO;
import qms.document.entity.RequestAuthorization;
import qms.document.logic.FormHelper;
import qms.form.dto.FormCompleteDTO;
import qms.form.entity.FormProgressState;
import qms.form.entity.SurveyMailConfiguration;
import qms.form.entity.SurveySubjectConfiguration;
import qms.framework.mail.MailDTO;
import qms.framework.util.CacheRegion;
import qms.framework.util.LocaleUtil;
import qms.util.HTMLEncoding;
import qms.util.HTMLUtil;

/**
 *
 * <AUTHOR> Guadalupe Alemán Reyes
 */
public class FormMailHelper extends RequestMailHelper {
    
    private static final String FORM_IN_TIME_AUTHORIZER = " "
            + " SELECT DISTINCT new " + FormCompleteDTO.class.getCanonicalName() + "("
                + " request.status,"
                + " request.code, request.documentCode, "
                + " request.description, autor.description,"
                + " request.version, user.description,"
                + " o.stage, ps.description,"
                + " user.id, pool.delay, detail.modificationDate"
            + " )"
            + " FROM " 
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + "," + RequestAuthorization.class.getCanonicalName() + " request "
                + "," + User.class.getCanonicalName() + " user "
                + "," + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " JOIN request.flujo flow"
            + " JOIN flow.flujoPoolList pool"
            + " JOIN request.author autor"
            + " JOIN " + OutstandingSurveys.class.getCanonicalName() + " o "
            + " ON o.id = request.outstandingSurveysId"
            + " LEFT JOIN " + FormProgressState.class.getCanonicalName() + " ps "
            + " ON ps.id = o.progressStateId"
            + " WHERE "
                + " detail.requestId = request.id"
                + " AND detail.accepted IS NULL"
                + " AND detail.description IS NULL"
                + " AND pnd.recordId = request.id"
                + " AND ptype.code = :apeCode"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = user.id) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = user.id) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " ) "
                + " AND dateadd(day, pool.delay * -1, current_date()) <= detail.modificationDate";
    
    private static final String FORM_DELAYED_AUTHORIZER = " "
            + " SELECT DISTINCT new " + FormCompleteDTO.class.getCanonicalName() + "("
                + " request.status,"
                + " request.code, request.documentCode, "
                + " request.description, autor.description,"
                + " request.version, user.description,"
                + " o.stage, ps.description,"
                + " user.id, pool.delay, detail.modificationDate"
            + " )"
            + " FROM " 
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + "," + RequestAuthorization.class.getCanonicalName() + " request "
                + "," + User.class.getCanonicalName() + " user "
                + "," + PendingRecord.class.getCanonicalName() + " pnd "
            + " JOIN pnd.pendingType ptype "
            + " JOIN request.flujo flow"
            + " JOIN flow.flujoPoolList pool"
            + " JOIN request.author autor"
            + " JOIN " + OutstandingSurveys.class.getCanonicalName() + " o "
            + " ON o.id = request.outstandingSurveysId"
            + " LEFT JOIN " + FormProgressState.class.getCanonicalName() + " ps "
            + " ON ps.id = o.progressStateId"
            + " WHERE "
                + " detail.requestId = request.id"
                + " AND detail.accepted IS NULL"
                + " AND detail.description IS NULL"
                + " AND pnd.deleted = 0"
                + " AND pnd.recordId = request.id"
                + " AND ptype.code = :apeCode"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = user.id) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = user.id) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " ) "
                + " AND dateadd(day, pool.delay * -1, current_date()) > detail.modificationDate";

    
    private static final String FORM_ESCALATE = " "
            + " SELECT DISTINCT new " + FormCompleteDTO.class.getCanonicalName() + "("
                + " request.status,"
                + " request.code, request.documentCode, "
                + " request.description, autor.description,"
                + " request.version, user.description,"
                + " o.stage, ps.description,"
                + " boss.id, pool.delay, detail.modificationDate"
            + " )"
            + " FROM " 
                + AutorizationPoolDetails.class.getCanonicalName() + " detail "
                + "," +  RequestAuthorization.class.getCanonicalName() + " request "
                + "," + User.class.getCanonicalName() + " user "
                + "," + User.class.getCanonicalName() + " boss "
                + "," + PendingRecord.class.getCanonicalName() + " pnd "
                + "," + BusinessUnit.class.getCanonicalName() + " planta"
            + " JOIN pnd.pendingType ptype "
            + " JOIN request.flujo flow"
            + " JOIN flow.flujoPoolList pool"
            + " JOIN request.author autor"
            + " JOIN " + OutstandingSurveys.class.getCanonicalName() + " o "
            + " ON o.id = request.outstandingSurveysId"
            + " LEFT JOIN " + FormProgressState.class.getCanonicalName() + " ps "
            + " ON ps.id = o.progressStateId"
            + " WHERE "
                + " detail.requestId = request.id"
                + " AND detail.accepted IS NULL"
                + " AND detail.description IS NULL"
                + " AND pnd.recordId = request.id"
                + " AND ptype.code = :apeCode"
                + " AND ("
                    + " (pnd.status = " + PendingRecord.STATUS.ACTIVE.getValue() + " AND pnd.owner = user.id) "
                    + " OR (pnd.status = " + PendingRecord.STATUS.REASSIGNED.getValue() + " AND pnd.superOwnerId = user.id) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.superOwnerId = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue()
                    + " ) "
                    + " OR ("
                        + " pnd.status = " + PendingRecord.STATUS.ESCALATED.getValue() 
                        + " AND pnd.owner = user.id"
                        + " AND ptype.escalationMode = " + PendingType.EscalationMode.NOTIFY_ONLY.getValue()
                    + " ) "
                + " ) "
                + " AND ("
                    + " planta.id = request.businessUnit.id"
                    + " OR planta.organizationalUnit.id = request.organizationalUnit.id"
                + " )"
                + " AND dateadd(day, pool.delay * -1, current_date()) > detail.modificationDate"
                + " AND dateadd(day, planta.businessUnitSettings.daysToEscalate * -1, current_date()) > pnd.commitmentDate"
                + " AND boss.status = " + User.STATUS.ACTIVE.getValue()
                + " AND boss.id = user.bossId";
    
    
    private final FormHelper _helper;

    public FormMailHelper(IUntypedDAO dao) {
        super(dao);
        this._helper = new FormHelper(dao);
    }
    

    private String getSurveyVersion(String  documentCode) {
        String[] arr = documentCode.split("V.");
        String[] versionArr = arr[arr.length - 1].split("-");
        return versionArr[0];
    }
    
    public Set<Mail> getSetMailVerifier(Long requestId) {
        Set<Mail> set = toMail(_helper.getVerifier(requestId));
        return set;
    }
    
    public Set<Mail> getNextAuthorizationUsers(final Long outstandingSurveyId, final Long autorizationPoolDetailsId) {
        return toMailSet(_helper.getNextAuthorizationUsers(outstandingSurveyId, autorizationPoolDetailsId));
    }
    
    public boolean isNextFillSignature(final Long outstandingSurveyId, final Integer currentAutorizationPoolIndex) {
        final Map<String, Object> params = new HashMap<>();
        params.put("outstandingSurveyId", outstandingSurveyId);
        params.put("currentAutorizationPoolIndex", currentAutorizationPoolIndex);                
        final Integer count = dao.HQL_findSimpleInteger(" "
                    + " SELECT count(DISTINCT c.fieldObjectId)"
                    + " FROM " + OutstandingSurveysAttendantLoad.class.getCanonicalName() + " c "
                    + " JOIN c.outstandingSurvey o  "
                    + " JOIN " + SurveyField.class.getCanonicalName() + " field "
                        + " ON field.fieldObjectId = c.fieldObjectId"
                    + " JOIN field.obj fieldObj"
                    + " JOIN " + AutorizationPoolDetails.class.getCanonicalName() + " d "
                        + " ON o.requestId = d.requestId "
                        + " AND c.workflowIndex = d.indice "
                    + " WHERE "
                        + " o.id = :outstandingSurveyId"
                        + " AND d.indice = :currentAutorizationPoolIndex"
                        + " AND c.fieldType = 'signature'",
                    params
            );
        return count > 0;
    }
    
    public Integer getSeccionsToSign(final Long outstandingSurveyId, final Integer currentAutorizationPoolIndex) {
        final Map<String, Object> params = new HashMap<>();
        params.put("outstandingSurveyId", outstandingSurveyId);
        params.put("currentAutorizationPoolIndex", currentAutorizationPoolIndex);
        return dao.HQL_findSimpleInteger(" "
                    + " SELECT count(DISTINCT c.fieldObjectId)"
                    + " FROM "  + OutstandingSurveysAttendantLoad.class.getCanonicalName() + " c "
                    + " JOIN c.outstandingSurvey o"
                    + " JOIN " + SurveyField.class.getCanonicalName() + " field "
                    + " ON field.fieldObjectId = c.fieldObjectId"
                    + " JOIN field.obj fieldObj "
                    + " JOIN " + AutorizationPoolDetails.class.getCanonicalName() + " d "
                        + " ON o.requestId = d.requestId "
                        + " AND c.workflowIndex = d.indice "
                    + " WHERE "
                        + " o.id = :outstandingSurveyId"
                        + " AND d.accepted is not null "
                        + " AND d.indice <= :currentAutorizationPoolIndex"
                        + " AND c.fieldType = 'seccion'",
                params
            );
    }
    
    public FormAnswersDTO getFormAnswersDTO(
            Long requestId,
            Long surveyId,
            List<ITextHasValue<Long>> files,
            ResourceBundle tags,
            ILoggedUser loggedUser
    ) {
        FormAnswersDTO result = new FormAnswersDTO();
        if (!files.isEmpty()) {
            result.setAttachmentCount(String.valueOf(files.size()));
            StringBuilder filesBuilder = new StringBuilder(files.size() * 400);
            filesBuilder.append("<ul>");
            files.forEach((ITextHasValue<Long> fileName) -> filesBuilder
                    .append("<li><strong>")
                    .append(fileName.getText())
                    .append("</strong></li>"));
            filesBuilder.append("</ul>");
            result.setAttachmentList(filesBuilder.toString());
        } else {
            result.setAttachmentCount("0");
            result.setAttachmentList("&nbsp;");
        }
        List<FormAnswerDTO> answers = _helper.getFullTextAnswers(requestId, surveyId, tags, loggedUser);
        if (!answers.isEmpty()) {
            StringBuilder answerBuilder = new StringBuilder(answers.size() * 400);
            
            answerBuilder.append(TABLE_WITH_BORDER_TEMPLATE).append("<tbody>");
            answers.forEach((answer) -> {
                String answerValue = answer.getAnswerValue();
                answerBuilder.append(" "
                    + "<tr>"
                        + "<td>").append(
                            answer.getAnswerLabel()).append(" "
                        + "</td>"
                        + "<td>").append(answerValue).append(" "
                        + "</td>"
                    + "</tr>"
                );
            });
            answerBuilder.append("</tbody></table>");
            result.setSurveyAnswersTable(answerBuilder.toString());
        } else {
            result.setSurveyAnswersTable("- No se registraron registró información -");
        }
        return result;
    }

    public FormDTO getFormDTO(Long requestId, ResourceBundle tags, ILoggedUser loggedUser) {
        final FormDTO result = dao.HQLT_findSimple(FormDTO.class, " "
            + " SELECT "
                + " new " + FormDTO.class.getCanonicalName() + "("
                    + " o.surveyId,"
                    + " r.status,"
                    + " r.type,"
                    + " r.code,"
                    + " r.documentCode,"
                    + " o.code,"
                    + " r.description," //documentTitle
                    + " r.creationDate,"
                    + " r.reazon,"
                    + " a.description," //solicitante
                    + " o.stage,"//etapa
                    + " ps.description,"//estado de avance
                    + " b.description," //planta
                    + " d.description,"  //departamento
                    + " o.stageExpirationDate"  //expiracion
                + " )"
            + " FROM " + Request.class.getCanonicalName() + " r "
            + " LEFT JOIN " + OutstandingSurveys.class.getCanonicalName() + " o "
            + " ON o.id = r.outstandingSurveysId"
            + " LEFT JOIN " + FormProgressState.class.getCanonicalName() + " ps "
            + " ON ps.id = o.progressStateId"
            + " LEFT JOIN r.author a "
            + " LEFT JOIN r.businessUnit b "
            + " LEFT JOIN r.department d "
            + " WHERE r.id = :requestId", "requestId", requestId
        );
        if (result.getRecordCode() == null || result.getRecordCode().isEmpty())  {
            result.setRecordCode("[LLENADO SIN RESPUESTAS - SOLICITUD: " + result.getCode() + "]");
        }
        result.setProgressStatus(getStatusString(Integer.valueOf(result.getStatus()), tags));
        result.setReason(HTMLEncoding.stripBold(result.getReason()));
        result.setVersion(
            getSurveyVersion(result.getDocumentCode())
        );
        result.setStatus(
            getStatusString(result.getStatus(), tags)
        );
        if (result.getBusinessUnit()== null) {
            result.setBusinessUnit(LocaleUtil.getTag("unassigned", tags));
        }
        if(result.getDepartment() == null) {
            result.setDepartment(LocaleUtil.getTag("unassigned", tags));
        }
        final FormAnswerForMailDTO answersHtml = getMailAnswersData(requestId, tags, loggedUser, result.getSurveyId());
        result.setSurveyAnswersTable(answersHtml.getAnswersTableHtml());
        result.setSurveyAnswersSubject(answersHtml.getAnswersSubject());
        return result;
    }

    public Set<Mail> getSetMailAuthor(Long requestId) {
        final Mail toMail = toMail(_helper.getAuthor(requestId));
        if (toMail == null) {
            return null;
        }
        final Set<Mail> set = new HashSet<>(1);
        set.add(toMail);
        return set;
    }

    public Set<Mail> getMailsOnCompleted(Long requestId) {
        return new HashSet(
            toMailSet(_helper.getMailsOnCompleted(requestId))
        );
    }

    public String getMailSubjectOnCompletedFill(Long requestId, FormDTO data, ResourceBundle tags) {
        String extraSubject = getFormSubjectFields(data, tags);
        String subject = _helper.getMailSubjectOnCompletedFill(requestId);
        TextHasValue[] keys = new TextHasValue[] {
            new TextHasValue(Utilities.getSettings().getSystemId(), "systemId"),
            new TextHasValue(_helper.getAuthor(requestId).getDescription(), "author")
        };
        for(TextHasValue tv : keys) {
            if (subject.contains("${" + tv.getValue() + "}")) {
                subject = subject.replace("${" + tv.getValue() + "}", tv.getText());
            }
        }
        return subject.concat(extraSubject);
    }

    public Long getAutorizationPoolDetailsRequestId (Long autorizationPoolDetailsId) {
        return _helper.getAutorizationPoolDetailsRequestId(autorizationPoolDetailsId);
    }

    public Set<FormCompleteDTO> getFillInTimeAuthorizer() {
        return new HashSet<>(
            dao.HQL_findByQuery(
                    FORM_IN_TIME_AUTHORIZER + " AND (pnd.reminder IS NULL OR pnd.reminder > trunc(current_date()))",
                    "apeCode", APE.DOCUMENT_TO_FILL_FORM.getCode()
            )
        );
    }
    
    public Set<FormCompleteDTO> getFillDelayedAuthorizer() {
        return new HashSet<>(
            dao.HQL_findByQuery(
                    FORM_DELAYED_AUTHORIZER + " AND (pnd.reminder IS NULL OR pnd.reminder < trunc(current_date()))" ,
                    "apeCode", APE.DOCUMENT_TO_FILL_FORM.getCode()
            )
        );
    }
    
    public Set<FormCompleteDTO> getFillEscalateBoss() {
        return new HashSet<>(
            dao.HQL_findByQuery(
                    FORM_ESCALATE + " AND (pnd.reminder IS NULL OR pnd.reminder < trunc(current_date()))",
                    "apeCode", APE.DOCUMENT_TO_FILL_FORM.getCode()
            )
        );
    }
    
    public FormHelper getFormHelper() {
        return _helper;
    }
    
    private List<SurveySubjectConfiguration> getSurveySujectConfig(final Long surveyId) {
        return Utilities.getBean(ISurveysDAO.class).HQLT_findByQuery(
                SurveySubjectConfiguration.class, " "
                + " SELECT ssc"
                + " FROM " + SurveySubjectConfiguration.class.getCanonicalName() + " ssc "
                + " WHERE ssc.surveyId = :surveyId",
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
    }
    
    private void buildFormSubjectFields(FormDTO data, final StringBuilder sb, ResourceBundle tags) {
        if (data == null) {
            return;
        }
        final Long surveyId = data.getSurveyId();
        final List<SurveySubjectConfiguration> info = getSurveySujectConfig(surveyId);
        info.forEach(field-> buildFormSubjectField(field, sb, tags, data, surveyId));
    }
    
    private void buildFormSubjectField(SurveySubjectConfiguration field, final StringBuilder sb, ResourceBundle tags, FormDTO data, Long surveyId) {
        if (!Objects.equals(field.getIncludeInSubject(), 1)) {
            return;
        }
        boolean hideLabelInSubject = getMailConfiguration(surveyId, "hideLabelInSubject");
        switch (field.getFieldDescription()) {
            case "stage":
                sb.append(" ");
                if (!hideLabelInSubject) {
                    sb.append(getTag(tags, "label.stage")).append(": ");
                }
                String stage = data.getStage();
                if (stage == null) {
                    stage = "";
                }
                sb.append(stage).append(" -");
                break;
            case "creationDate":
                sb.append(" ");
                if (!hideLabelInSubject) {
                    sb.append(getTag(tags, "label.creationDate")).append(": ");
                }
                sb.append(Utilities.formatDateBy(data.getCreationDate(), "yyyy-MM-dd HH:mm:ss")).append(" -");
                break;
            case "status":
                sb.append(" ");
                if (!hideLabelInSubject) {
                    sb.append(getTag(tags, "label.status")).append(": ");
                }
                sb.append(data.getStatus()).append(" -");
                break;
            case "documentTitle":
                sb.append(" ");
                if (!hideLabelInSubject) {
                    sb.append(getTag(tags, "label.documentTitle")).append(": ");
                }
                sb.append(data.getDocumentTitle()).append(" -");
                break;
        }

    }
    
    public String getFormSubjectFields(final FormDTO data, final ResourceBundle tags) {
        if (data == null) {
            return null;
        }
        final StringBuilder sb = new StringBuilder();
        buildFormSubjectFields(data, sb, tags);
        
        sb.append(data.getSurveyAnswersSubject());
        
        return " " + sb;
    }
    
    private FormAnswerForMailDTO getMailAnswersData(
            Long requestId,
            ResourceBundle tags,
            ILoggedUser loggedUser,
            Long surveyId
    ) {
        final List<FormAnswerDTO> answers = _helper.getFullTextAnswers(requestId, surveyId, tags, loggedUser);
        final FormAnswerForMailDTO result = new FormAnswerForMailDTO();
        if (answers.isEmpty()) {
            return result;
        } 
        final StringBuilder tableHtml = new StringBuilder(answers.size() * 400);
        final StringBuilder mailSubject = new StringBuilder(answers.size() * 400);
        boolean hasMailAnswers = answers.stream().anyMatch(item -> item.getIncludeInMail() == 1);
        boolean hasSubjectAnswers = answers.stream().anyMatch(item -> item.getIncludeInSubject() == 1);
        if (hasMailAnswers) {
            tableHtml.append("</br><p style='font-size: 12px; font-family: Helvetica, Arial, Verdana, Trebuchet MS, sans-serif; margin:0; padding-bottom: 10px;'>")
                    .append(getTag(tags, "title.answers")).append("</p>");

            tableHtml.append(TABLE_WITH_BORDER_TEMPLATE).append("<tbody>");
            answers.forEach((answer) -> {
                if (Boolean.TRUE.equals(answer.getHidden())) {
                    return;
                }
                String answerValue = answer.getAnswerValue();
                if (answerValue == null) {
                    answerValue = "";
                }
                tableHtml.append(" "
                        + "<tr>"
                        + "<td style=\"width:30%;\"><strong>").append(
                                answer.getAnswerLabel()).append(" "
                                        + "</strong></td>"
                                        + "<td style=\"padding-right: .5rem\">").append(answerValue)
                                                .append(" "
                                                        + "</td>"
                                                        + "</tr>"
                                                );
            });
            tableHtml.append("</tbody></table>");
        }
        if (hasSubjectAnswers) {
            boolean hideLabelInSubject = getMailConfiguration(surveyId, "hideLabelInSubject");
            answers.forEach((answer) -> {
                if (Boolean.TRUE.equals(answer.getHidden()) || answer.getIncludeInSubject() == 0) {
                    return;
                }
                String answerValue = answer.getAnswerValue();
                if (hideLabelInSubject) {
                    if (answerValue == null) {
                        answerValue = "";
                    }
                    mailSubject.append(answerValue).append(" - ");
                } else {
                    mailSubject.append(answer.getAnswerLabel()).append(": ").append(answerValue).append(" - ");
                }
            });
        }
        result.setAnswersTableHtml(tableHtml.toString());
        result.setAnswersSubject(mailSubject.toString());
        return result;
    }
    
    private String getConditionNoticeMail() {
        return " cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + "Date) >= cast(noticeDate" + SqlQueryParser.AS + "Date) "
                + " AND  apr.STATUS = " + PendingRecord.STATUS.ACTIVE.getValue();
    }
    
    @Override
    public List<MailDTO> getNoticeMail(IPendingOperation pendingOperation, String sql) {
        String condition = getConditionNoticeMail();
        return getDailyMail(pendingOperation, sql, condition, PendingMailerType.NOTICE);
    }
    
    private String getConditionDeadlineMail() {
        return " cast(" + SqlQueryParser.CURRENT_DATE + SqlQueryParser.AS + "Date) >= cast(deadline" + SqlQueryParser.AS + "Date) ";
    }
       
    @Override
    public List<MailDTO> getDeadlineMail(IPendingOperation pendingOperation, String sql) {
        String condition = getConditionDeadlineMail();
        return getDailyMail(pendingOperation, sql, condition, PendingMailerType.DEADLINE);
    }
    
    public List<SurveyMailConfiguration> getSurveyMailConfig(final Long surveyId) {
        return Utilities.getBean(ISurveysDAO.class).HQLT_findByQuery(
                SurveyMailConfiguration.class, " "
                + " SELECT ssc"
                + " FROM " + SurveyMailConfiguration.class.getCanonicalName() + " ssc "
                + " WHERE ssc.surveyId = :surveyId",
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
    }
    
    public boolean getMailConfiguration(Long surveyId, String configuration) {
        final List<SurveyMailConfiguration> config = getSurveyMailConfig(surveyId);
        if (configuration == null || config == null) {
            return false;
        }
        SurveyMailConfiguration configObj = config.stream()
                .filter(e -> configuration.equals(e.getConfigDescription()))
                .findAny()
                .orElse(null);
        return configObj != null && configObj.getStatus() == 1;
    }
}
