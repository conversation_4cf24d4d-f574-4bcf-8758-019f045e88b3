package qms.document.mail;

import DPMS.Mapping.AutorizationPoolDetailComment;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.Request;
import DPMS.Mapping.WorkflowPool;
import java.util.List;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.form.dto.FillInfoDTO;
import qms.form.dto.FormRequestDTO;
import qms.form.dto.FormRequestorDTO;
import qms.form.entity.FormRequest;
import qms.framework.mail.MailDTO;
import qms.survey.dto.ChangeDefinedUserDto;
import qms.workflow.util.WorkflowSupported;

/**
 *
 * <AUTHOR>
 */
public interface IFormRuntimeMailer {

    void cancelledForm(FormRequestorDTO cancelledFiller, String cancelByUser, String cancelByReason, ILoggedUser loggedUser);

    MailDTO getMailToOnFormReopenedRequest(Long outstandingSurveysId, Long requestId, ILoggedUser loggedUser);

    void onFormReopenedRequest(
            MailDTO mail
    ) throws Throwable;

    void onFormReopenRequest(
            FormRequestDTO dto, ILoggedUser user
    ) throws Throwable;
    
    void onFormReopenRejected(
            FormRequest request, String reason, ILoggedUser loggedUser
    );

    void onFormAdjustmentRequest(
            FormRequestDTO dto, ILoggedUser user
    );
    void onFormAdjustmentRejected(
            FormRequest request, String reason, ILoggedUser loggedUser
    );
    void onFormAdjustmentRequestDone(
            MailDTO mail
    ) throws Throwable;
    
    void onFormCancelRequest(
            FormRequestDTO dto, ILoggedUser user
    );
    
    void onFormCancelApproved(
            FormRequest request, String reason, ILoggedUser loggedUser
    );
    
    void onFormCancelRejected(
            FormRequest request, String reason, ILoggedUser loggedUser
    );
    
    MailDTO getMailToOnFormAdjustmentRequestDone(Long outstandingSurveysId, ILoggedUser loggedUser);
    
    void authorizationByInactiveUser(WorkflowSupported target, List<AutorizationPoolDetails> set, Long requestId, LoggedUser loggedUser);
    
    void formToCancel(Request request, LoggedUser loggedUser);

    void completedFillForm(Request sol, LoggedUser loggedUser);

    void completedFillFormFromField(Long requestId, ILoggedUser loggedUser);

    void fillFormRecurrence(Request sol, AutorizationPoolDetails apd, LoggedUser loggedUser);

    void nextFormFiller(Request sol, WorkflowPool poolDetails, ILoggedUser loggedUser);
    
    void nextFormFiller(FormRequest sol, WorkflowPool poolDetails, ILoggedUser loggedUser);

    void rejectFilledSection(
            Long outstandingSurveyId,
            Long autorizationPoolDetailsId,
            AutorizationPoolDetailComment comment,
            ILoggedUser loggedUser
    );

    void formRequested(Long requestId, LoggedUser loggedUser);
    
    void fillFormRequested(Request sol, LoggedUser loggedUser, WorkflowPool poolDetails);

    void fillFormRejected(Request sol, LoggedUser loggedUser, String comment);

    void cancelledFillForm(Long requestId, String reason, LoggedUser loggedUser);
    
    void cancelledFillForm(List<FillInfoDTO> fills, String reason, LoggedUser loggedUser);
    
    void destroyedFillForm(Long requestId, LoggedUser loggedUser);
    
    void updatedDefinedUsers(ChangeDefinedUserDto data, Long requestId, ILoggedUser loggedUser);
    
    Integer fillsInTimeAuthorizer();
    
    Integer fillsDelayedAuthorizer();
    
    Integer fillsEscalate();
    
    FormMailHelper getMailHelper();

}
