package qms.document.DAOInterface;

import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.util.List;
import qms.document.entity.DictionaryIndex;

/**
 * <AUTHOR>
 */
@Implementation(name = "DictionaryIndexDAO")
public interface IDictionaryIndexDAO extends IGenericDAO<DictionaryIndex, Long> {
    public boolean saveDocumentVersionObjects(List <String> registros, Long dictionaryIndexId, String firstDigit);
    public List<String> getDocumentVersionById(Long dictionaryIndexId);
    
}
