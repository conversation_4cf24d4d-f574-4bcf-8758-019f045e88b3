
package qms.document.dto;

import DPMS.Mapping.DocumentType;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import java.util.Date;
import java.util.Objects;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class FillFormDTO {
    
    private String code;
    private Date creationDate;
    private String documentCode;
    private UserRef author;
    private DocumentRef fillOutDocument;
    private BusinessUnitDepartmentRef department;
    private DocumentType documentType;
    private String reazon;
    private Long outstandingSurveysId;
    private Long id;
    private Integer currentAutorizationPoolIndex;
    private Long currentAutorizationPoolDetailId;
    private Integer currentRecurrence;
    private Integer requestStatus;
    private Long surveyId;

    public FillFormDTO() {
    }
    
    public FillFormDTO(
            String code, 
            Date creationDate,
            String documentCode,
            UserRef author, 
            DocumentRef fillOutDocument,
            BusinessUnitDepartmentRef department,
            DocumentType documentType, 
            String reazon, 
            Long outstandingSurveysId, Long id, 
            Integer requestStatus,
            Long surveyId
    ) {
        
        this.documentType = documentType;
        this.code = code;
        this.creationDate = creationDate;
        this.documentCode = documentCode;
        this.author = author;
        this.fillOutDocument = fillOutDocument;
        this.department = department;
        this.reazon = reazon;
        this.outstandingSurveysId = outstandingSurveysId;
        this.id = id;
        this.currentAutorizationPoolIndex = null;
        this.currentAutorizationPoolDetailId = null;
        this.currentRecurrence = null;
        this.requestStatus = requestStatus;
        this.surveyId = surveyId;
    }
    public FillFormDTO(
            String code, 
            Date creationDate,
            String documentCode,
            UserRef author, 
            DocumentRef fillOutDocument,
            BusinessUnitDepartmentRef department,
            DocumentType documentType, 
            String reazon, 
            Long outstandingSurveysId, Long id, 
            Integer currentAutorizationPoolIndex,
            Long currentAutorizationPoolDetailId, 
            Integer currentRecurrence,
            Integer requestStatus,
            Long surveyId
    ) {
        
        this.documentType = documentType;
        this.code = code;
        this.creationDate = creationDate;
        this.documentCode = documentCode;
        this.author = author;
        this.fillOutDocument = fillOutDocument;
        this.department = department;
        this.reazon = reazon;
        this.outstandingSurveysId = outstandingSurveysId;
        this.id = id;
        this.currentAutorizationPoolIndex = currentAutorizationPoolIndex;
        this.currentAutorizationPoolDetailId = currentAutorizationPoolDetailId;
        this.currentRecurrence = currentRecurrence;
        this.requestStatus = requestStatus;
        this.surveyId = surveyId;
    }
    
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }

    public DocumentRef getFillOutDocument() {
        return fillOutDocument;
    }

    public void setFillOutDocument(DocumentRef fillOutDocument) {
        this.fillOutDocument = fillOutDocument;
    }

    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

    public String getReazon() {
        return reazon;
    }

    public void setReazon(String reazon) {
        this.reazon = reazon;
    }

    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getCurrentAutorizationPoolIndex() {
        return currentAutorizationPoolIndex;
    }

    public void setCurrentAutorizationPoolIndex(Integer currentAutorizationPoolIndex) {
        this.currentAutorizationPoolIndex = currentAutorizationPoolIndex;
    }

    public Long getCurrentAutorizationPoolDetailId() {
        return currentAutorizationPoolDetailId;
    }

    public void setCurrentAutorizationPoolDetailId(Long currentAutorizationPoolDetailId) {
        this.currentAutorizationPoolDetailId = currentAutorizationPoolDetailId;
    }

    public Integer getCurrentRecurrence() {
        return currentRecurrence;
    }

    public void setCurrentRecurrence(Integer currentRecurrence) {
        this.currentRecurrence = currentRecurrence;
    }

    public DocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(DocumentType documentType) {
        this.documentType = documentType;
    }

    public Integer getRequestStatus() {
        return requestStatus;
    }

    public void setRequestStatus(Integer requestStatus) {
        this.requestStatus = requestStatus;
    }

    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 83 * hash + Objects.hashCode(this.outstandingSurveysId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final FillFormDTO other = (FillFormDTO) obj;
        if (!Objects.equals(this.code, other.getCode())) {
            return false;
        }
        if (!Objects.equals(this.outstandingSurveysId, other.getOutstandingSurveysId())) {
            return false;
        }
        if (!Objects.equals(this.id, other.getId())) {
            return false;
        }
        if (!Objects.equals(this.currentAutorizationPoolIndex, other.getCurrentAutorizationPoolIndex())) {
            return false;
        }
        if (!Objects.equals(this.currentAutorizationPoolDetailId, other.getCurrentAutorizationPoolDetailId())) {
            return false;
        }
        if (!Objects.equals(this.currentRecurrence, other.getCurrentRecurrence())) {
            return false;
        }
        return Objects.equals(this.surveyId, other.getSurveyId());
    }

    @Override
    public String toString() {
        return "FillFormDTO{" 
                + "code=" + code 
                + ", outstandingSurveysId=" + outstandingSurveysId 
                + ", id=" + id 
                + ", currentRecurrence=" + currentRecurrence
                + ", surveyId=" + surveyId 
                + '}';
    }
    
    
}
