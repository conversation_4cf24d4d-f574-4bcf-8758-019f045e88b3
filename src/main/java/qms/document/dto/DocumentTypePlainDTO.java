package qms.document.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import qms.document.interfaces.IPlainDocumentType;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DocumentTypePlainDTO implements IPlainDocumentType, Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String code;
    private Integer status;
    private Integer deleted;
    private String description;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Long dictionaryIndexId;
    private Integer isRetainable;
    private String documentControlledType;
    private String registryCode;
    private Integer registryCodeSequenceLength;
    private Integer shareDocuments;
    private Long shareMaxDownloads;
    private Long shareMaxViews;
    private Long shareLife;
    private Integer shareRestrictUserAgent;
    private Integer shareRestrictFileType;
    private Integer externalLink;
    private Long externalMaxDownloads;
    private Long externalLife;
    private Integer externalRestrictUserAgent;
    private Integer externalRestrictFileType;
    private Integer downloadFiles;
    private Integer creationType;
    private Integer mustRead;
    private Integer mustAssignReaders;
    private Integer addFindingsEnabled;
    private Integer addActivitiesEnabled;
    private Integer quickPrintEnabled;
    private Integer collectingAndStoreResponsible;
    private Integer informationClassification;
    private Integer disposition;
    private Integer documentExpiration;
    private Integer enablePdfViewer;
    private Integer pdfViewerFitDocumentScreen;
    private Integer readOnlyEnabled;
    private Integer canPrintControlledCopies;

    public DocumentTypePlainDTO() {
    }

    public DocumentTypePlainDTO(
            Long id,
            String code,
            Integer status,
            Integer deleted,
            String description,
            Date createdDate,
            Date lastModifiedDate,
            Long createdBy,
            Long lastModifiedBy,
            Long dictionaryIndexId,
            Integer isRetainable,
            String documentControlledType,
            String registryCode,
            Integer registryCodeSequenceLength,
            Integer shareDocuments,
            Long shareMaxDownloads,
            Long shareMaxViews,
            Long shareLife,
            Integer shareRestrictUserAgent,
            Integer shareRestrictFileType,
            Integer externalLink,
            Long externalMaxDownloads,
            Long externalLife,
            Integer externalRestrictUserAgent,
            Integer externalRestrictFileType,
            Integer downloadFiles,
            Integer creationType,
            Integer mustRead,
            Integer mustAssignReaders,
            Integer addFindingsEnabled,
            Integer addActivitiesEnabled,
            Integer quickPrintEnabled,
            Integer collectingAndStoreResponsible,
            Integer informationClassification,
            Integer disposition,
            Integer documentExpiration,
            Integer enablePdfViewer,
            Integer pdfViewerFitDocumentScreen,
            Integer readOnlyEnabled,
            Integer canPrintControlledCopies
    ) {
        this.id = id;
        this.code = code;
        this.status = status;
        this.deleted = deleted;
        this.description = description;
        this.createdDate = createdDate;
        this.lastModifiedDate = lastModifiedDate;
        this.createdBy = createdBy;
        this.lastModifiedBy = lastModifiedBy;
        this.dictionaryIndexId = dictionaryIndexId;
        this.isRetainable = isRetainable;
        this.documentControlledType = documentControlledType;
        this.registryCode = registryCode;
        this.registryCodeSequenceLength = registryCodeSequenceLength;
        this.shareDocuments = shareDocuments;
        this.shareMaxDownloads = shareMaxDownloads;
        this.shareMaxViews = shareMaxViews;
        this.shareLife = shareLife;
        this.shareRestrictUserAgent = shareRestrictUserAgent;
        this.shareRestrictFileType = shareRestrictFileType;
        this.externalLink = externalLink;
        this.externalMaxDownloads = externalMaxDownloads;
        this.externalLife = externalLife;
        this.externalRestrictUserAgent = externalRestrictUserAgent;
        this.externalRestrictFileType = externalRestrictFileType;
        this.downloadFiles = downloadFiles;
        this.creationType = creationType;
        this.mustRead = mustRead;
        this.mustAssignReaders = mustAssignReaders;
        this.addFindingsEnabled = addFindingsEnabled;
        this.addActivitiesEnabled = addActivitiesEnabled;
        this.quickPrintEnabled = quickPrintEnabled;
        this.collectingAndStoreResponsible = collectingAndStoreResponsible;
        this.informationClassification = informationClassification;
        this.disposition = disposition;
        this.documentExpiration = documentExpiration;
        this.enablePdfViewer = enablePdfViewer;
        this.pdfViewerFitDocumentScreen = pdfViewerFitDocumentScreen;
        this.readOnlyEnabled = readOnlyEnabled;
        this.canPrintControlledCopies = canPrintControlledCopies;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public Long getDictionaryIndexId() {
        return dictionaryIndexId;
    }

    @Override
    public void setDictionaryIndexId(Long dictionaryIndexId) {
        this.dictionaryIndexId = dictionaryIndexId;
    }

    @Override
    public Integer getIsRetainable() {
        return isRetainable;
    }

    @Override
    public void setIsRetainable(Integer isRetainable) {
        this.isRetainable = isRetainable;
    }

    @Override
    public String getDocumentControlledType() {
        return documentControlledType;
    }

    @Override
    public void setDocumentControlledType(String documentControlledType) {
        this.documentControlledType = documentControlledType;
    }

    @Override
    public String getRegistryCode() {
        return registryCode;
    }

    @Override
    public void setRegistryCode(String registryCode) {
        this.registryCode = registryCode;
    }

    @Override
    public Integer getRegistryCodeSequenceLength() {
        return registryCodeSequenceLength;
    }

    @Override
    public void setRegistryCodeSequenceLength(Integer registryCodeSequenceLength) {
        this.registryCodeSequenceLength = registryCodeSequenceLength;
    }

    @Override
    public Integer getShareDocuments() {
        return shareDocuments;
    }

    @Override
    public void setShareDocuments(Integer shareDocuments) {
        this.shareDocuments = shareDocuments;
    }

    @Override
    public Long getShareMaxDownloads() {
        return shareMaxDownloads;
    }

    @Override
    public void setShareMaxDownloads(Long shareMaxDownloads) {
        this.shareMaxDownloads = shareMaxDownloads;
    }

    @Override
    public Long getShareMaxViews() {
        return shareMaxViews;
    }

    @Override
    public void setShareMaxViews(Long shareMaxViews) {
        this.shareMaxViews = shareMaxViews;
    }

    @Override
    public Long getShareLife() {
        return shareLife;
    }

    @Override
    public void setShareLife(Long shareLife) {
        this.shareLife = shareLife;
    }

    @Override
    public Integer getShareRestrictUserAgent() {
        return shareRestrictUserAgent;
    }

    @Override
    public void setShareRestrictUserAgent(Integer shareRestrictUserAgent) {
        this.shareRestrictUserAgent = shareRestrictUserAgent;
    }

    @Override
    public Integer getShareRestrictFileType() {
        return shareRestrictFileType;
    }

    @Override
    public void setShareRestrictFileType(Integer shareRestrictFileType) {
        this.shareRestrictFileType = shareRestrictFileType;
    }

    @Override
    public Integer getExternalLink() {
        return externalLink;
    }

    @Override
    public void setExternalLink(Integer externalLink) {
        this.externalLink = externalLink;
    }

    @Override
    public Long getExternalMaxDownloads() {
        return externalMaxDownloads;
    }

    @Override
    public void setExternalMaxDownloads(Long externalMaxDownloads) {
        this.externalMaxDownloads = externalMaxDownloads;
    }

    @Override
    public Long getExternalLife() {
        return externalLife;
    }

    @Override
    public void setExternalLife(Long externalLife) {
        this.externalLife = externalLife;
    }

    @Override
    public Integer getExternalRestrictUserAgent() {
        return externalRestrictUserAgent;
    }

    @Override
    public void setExternalRestrictUserAgent(Integer externalRestrictUserAgent) {
        this.externalRestrictUserAgent = externalRestrictUserAgent;
    }

    @Override
    public Integer getExternalRestrictFileType() {
        return externalRestrictFileType;
    }

    @Override
    public void setExternalRestrictFileType(Integer externalRestrictFileType) {
        this.externalRestrictFileType = externalRestrictFileType;
    }

    @Override
    public Integer getDownloadFiles() {
        return downloadFiles;
    }

    @Override
    public void setDownloadFiles(Integer downloadFiles) {
        this.downloadFiles = downloadFiles;
    }

    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Override
    public Integer getMustRead() {
        return mustRead;
    }

    @Override
    public void setMustRead(Integer mustRead) {
        this.mustRead = mustRead;
    }

    @Override
    public Integer getMustAssignReaders() {
        return mustAssignReaders;
    }

    @Override
    public void setMustAssignReaders(Integer mustAssignReaders) {
        this.mustAssignReaders = mustAssignReaders;
    }

    @Override
    public Integer getAddFindingsEnabled() {
        return addFindingsEnabled;
    }

    @Override
    public void setAddFindingsEnabled(Integer addFindingsEnabled) {
        this.addFindingsEnabled = addFindingsEnabled;
    }

    @Override
    public Integer getAddActivitiesEnabled() {
        return addActivitiesEnabled;
    }

    @Override
    public void setAddActivitiesEnabled(Integer addActivitiesEnabled) {
        this.addActivitiesEnabled = addActivitiesEnabled;
    }

    @Override
    public Integer getQuickPrintEnabled() {
        return quickPrintEnabled;
    }

    @Override
    public void setQuickPrintEnabled(Integer quickPrintEnabled) {
        this.quickPrintEnabled = quickPrintEnabled;
    }

    @Override
    public Integer getCollectingAndStoreResponsible() {
        return collectingAndStoreResponsible;
    }

    @Override
    public void setCollectingAndStoreResponsible(Integer collectingAndStoreResponsible) {
        this.collectingAndStoreResponsible = collectingAndStoreResponsible;
    }

    @Override
    public Integer getInformationClassification() {
        return informationClassification;
    }

    @Override
    public void setInformationClassification(Integer informationClassification) {
        this.informationClassification = informationClassification;
    }

    @Override
    public Integer getDisposition() {
        return disposition;
    }

    @Override
    public void setDisposition(Integer disposition) {
        this.disposition = disposition;
    }

    @Override
    public Integer getDocumentExpiration() {
        return documentExpiration;
    }

    @Override
    public void setDocumentExpiration(Integer documentExpiration) {
        this.documentExpiration = documentExpiration;
    }

    @Override
    public Integer getEnablePdfViewer() {
        return enablePdfViewer;
    }

    @Override
    public void setEnablePdfViewer(Integer enablePdfViewer) {
        this.enablePdfViewer = enablePdfViewer;
    }

    @Override
    public Integer getPdfViewerFitDocumentScreen() {
        return pdfViewerFitDocumentScreen;
    }

    @Override
    public void setPdfViewerFitDocumentScreen(Integer pdfViewerFitDocumentScreen) {
        this.pdfViewerFitDocumentScreen = pdfViewerFitDocumentScreen;
    }

    @Override
    public Integer getReadOnlyEnabled() {
        return readOnlyEnabled;
    }

    @Override
    public void setReadOnlyEnabled(Integer readOnlyEnabled) {
        this.readOnlyEnabled = readOnlyEnabled;
    }

    @Override
    public Integer getCanPrintControlledCopies() {
        return canPrintControlledCopies;
    }

    @Override
    public void setCanPrintControlledCopies(Integer canPrintControlledCopies) {
        this.canPrintControlledCopies = canPrintControlledCopies;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        DocumentTypePlainDTO that = (DocumentTypePlainDTO) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "DocumentTypeEntityDTO{" +
                "id=" + id +
                '}';
    }
}
