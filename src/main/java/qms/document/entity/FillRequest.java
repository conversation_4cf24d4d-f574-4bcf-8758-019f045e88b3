package qms.document.entity;

import DPMS.Mapping.BusinessUnitLite;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.OrganizationalUnitLite;
import Framework.Config.StandardEntity;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.hibernate.OutstandingSurveysLite;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR> Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "request")
@Immutable
public class FillRequest extends StandardEntity<FillRequest> implements Serializable {

    private static final long serialVersionUID = 1L;
   
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private String documentCode;
    private Date creationDate;
    private Integer type;
    private DocumentType documentType;
    private Long typeId;
    private String version;
    private Long fileId;
    private Long surveyId;
    private UserRef author;
    private UserRef impersonatedBy;
    private BusinessUnitLite businessUnit;
    private OrganizationalUnitLite organizationalUnit;
    private Long nodoId;
    private BusinessUnitDepartmentRef department;

    private DocumentRef fillOutDocument;
    private Integer scope;
    private OutstandingSurveysLite outstandingSurveys;
    private String authorizersNames;
    private Boolean restrictRecordsByDepartment = false;
    private Boolean validateAccessFormDepartment = false;
    private String restrictRecordsField;
    private Long restrictRecordsObjId;
    
    public FillRequest() {
    }

    public FillRequest(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Basic(optional = false)
    @Column(name = "document_code")
    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Basic(optional = false)
    @Column(name = "creation_date", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Basic(optional = false)
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @ManyToOne
    @JoinColumn(name = "document_type", referencedColumnName = "document_type_id")
    public DocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(DocumentType documentType) {
        this.documentType = documentType;
    }

    @Column(name = "document_type", updatable = false, insertable = false)
    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }
    
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "autor_id", referencedColumnName = "user_id")
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }
    
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "impersonated_by", referencedColumnName = "user_id")
    public UserRef getImpersonatedBy() {
        return impersonatedBy;
    }

    public void setImpersonatedBy(UserRef impersonatedBy) {
        this.impersonatedBy = impersonatedBy;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id")
    public BusinessUnitLite getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitLite businessUnit) {
        this.businessUnit = businessUnit;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "organizational_unit_id", referencedColumnName = "organizational_unit_id")
    public OrganizationalUnitLite getOrganizationalUnit() {
        return organizationalUnit;
    }

    public void setOrganizationalUnit(OrganizationalUnitLite organizationalUnit) {
        this.organizationalUnit = organizationalUnit;
    }

    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "survey_id")
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Column(name = "node_id", updatable = false, insertable = false)
    public Long getNodoId() {
        return nodoId;
    }

    public void setNodoId(Long nodoId) {
        this.nodoId = nodoId;
    }

    @ManyToOne
    @JoinColumn(name = "business_unit_department_id")
    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

    @JoinColumn(name = "fill_document_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER)
    public DocumentRef getFillOutDocument() {
        return fillOutDocument;
    }

    public void setFillOutDocument(DocumentRef fillOutDocument) {
        this.fillOutDocument = fillOutDocument;
    }

    /**
     * @return the scope
     */
    @Column(name = "scope")
    public Integer getScope() {
        return scope;
    }

    /**
     * @param scope the scope to set
     */
    public void setScope(Integer scope) {
        this.scope = scope;
    }

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "OUTSTANDING_SURVEYS_ID")
    public OutstandingSurveysLite getOutstandingSurveys() {
        return outstandingSurveys;
    }

    public void setOutstandingSurveys(OutstandingSurveysLite outstandingSurveys) {
        this.outstandingSurveys = outstandingSurveys;
    }

    @Column(name = "authorizers_names")
    public String getAuthorizersNames() {
        return authorizersNames;
    }

    public void setAuthorizersNames(String authorizersNames) {
        this.authorizersNames = authorizersNames;
    }
    
    @Type(type = "numeric_boolean")
    @Column(name = "restrict_records_department")
    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "validate_access_department")
    public Boolean getValidateAccessFormDepartment() {
        return validateAccessFormDepartment;
    }

    public void setValidateAccessFormDepartment(Boolean validateAccessFormDepartment) {
        this.validateAccessFormDepartment = validateAccessFormDepartment;
    }

    @Column(name = "restrict_records_field")
    public String getRestrictRecordsField() {
        return restrictRecordsField;
    }

    public void setRestrictRecordsField(String restrictRecordsField) {
        this.restrictRecordsField = restrictRecordsField;
    }

    @Column(name = "restrict_records_obj_id")
    public Long getRestrictRecordsObjId() {
        return restrictRecordsObjId;
    }

    public void setRestrictRecordsObjId(Long restrictRecordsObjId) {
        this.restrictRecordsObjId = restrictRecordsObjId;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof FillRequest)) {
            return false;
        }
        FillRequest other = (FillRequest) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
        }

    @Override
    public String toString() {
        return "qms.document.entity.FillRequest[ id=" + id + " ]";
    }

}
