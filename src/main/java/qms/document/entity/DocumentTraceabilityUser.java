package qms.document.entity;

import Framework.Config.CompositeStandardEntity;
import bnext.reference.UserRef;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "document_traceability_user")
public class DocumentTraceabilityUser extends CompositeStandardEntity<DocumentTraceabilityUserPK> 
        implements Serializable, ILinkedComposityGrid<DocumentTraceabilityUserPK> {

    private static final long serialVersionUID = 1L;

    private DocumentTraceabilityUserPK id;
    private DocumentTraceability documentTraceability;
    private UserRef authorizedBy;

    public DocumentTraceabilityUser() {
    }

    public DocumentTraceabilityUser(DocumentTraceabilityUserPK id) {
        this.id = id;
    }

    public DocumentTraceabilityUser(Long documentTraceabilityId, Long authorizedBy, Integer authorizedIndex) {
        this.id = new DocumentTraceabilityUserPK(documentTraceabilityId, authorizedBy, authorizedIndex);
    }

    @Override
    public DocumentTraceabilityUserPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public DocumentTraceabilityUserPK getId() {
        return id;
    }

    @Override
    public void setId(DocumentTraceabilityUserPK id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
            name = "document_traceability_id",
            referencedColumnName = "document_traceability_id",
            insertable = false,
            updatable = false)
    public DocumentTraceability getDocumentTraceability() {
        return documentTraceability;
    }

    public void setDocumentTraceability(DocumentTraceability documentTraceability) {
        this.documentTraceability = documentTraceability;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
            name = "authorized_by",
            referencedColumnName = "user_id",
            insertable = false,
            updatable = false
    )
    public UserRef getAuthorizedBy() {
        return authorizedBy;
    }

    public void setAuthorizedBy(UserRef authorizedBy) {
        this.authorizedBy = authorizedBy;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DocumentTraceabilityUser other = (DocumentTraceabilityUser) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.document.entity.DocumentTraceabilityUser{" + "id=" + id + '}';
    }

}
