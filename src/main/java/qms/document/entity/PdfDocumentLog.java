package qms.document.entity;

import Framework.Config.CompositeStandardEntity;
import bnext.reference.document.DocumentRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import java.io.Serializable;
import java.util.Objects;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import qms.util.interfaces.ILinkedCompositeEntity;


/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Subselect(value = PdfDocumentLog.PDF_DOCUMENT_LOG_SQL)
public class PdfDocumentLog extends CompositeStandardEntity<PdfDocumentLogPK>
        implements Serializable, ILinkedCompositeEntity<PdfDocumentLogPK> {

    private static final long serialVersionUID = 1L;
    
    public static final String PDF_DOCUMENT_LOG_SQL = ""
            + " SELECT log.pdf_conversion_log_id, doc.id as document_id"
            + " FROM pdf_conversion_log log"
            + " INNER JOIN files f"
            + " ON f.id = log.file_id"
            + " INNER JOIN document doc"
            + " ON doc.file_id = f.id";

    private PdfDocumentLogPK id;
    private PdfConversionLog log;
    private DocumentRef document;

    @Override
    public PdfDocumentLogPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public PdfDocumentLogPK getId() {
        return id;
    }

    @Override
    public void setId(PdfDocumentLogPK id) {
        this.id = id;
    }
    
    @JoinColumn(name = "pdf_conversion_log_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public PdfConversionLog getLog() {
        return log;
    }

    public void setLog(PdfConversionLog log) {
        this.log = log;
    }

    @JoinColumn(name = "document_id", insertable = false, updatable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public DocumentRef getDocument() {
        return document;
    }

    public void setDocument(DocumentRef document) {
        this.document = document;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PdfDocumentLog other = (PdfDocumentLog) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PdfDocumentLog{" + "id=" + id + '}';
    }
    
}
