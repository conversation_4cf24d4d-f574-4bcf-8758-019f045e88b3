
package qms.document.entity;

import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.ReceiptAcknowledgment;
import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "document")
@Immutable
public class DocumentPending extends StandardEntity<DocumentPending> implements IDocument, Serializable {

    private static final long serialVersionUID = -6298738639562326432L;

    private String masterId;
    private String code;
    private String description;
    private Integer status;
    private Integer deleted;
    private Integer readers;
    private BusinessUnitDepartment businessUnitDepartment;
    private BusinessUnit businessUnit;
    private ReceiptAcknowledgment receiptAcknowledgment;

    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Integer enablePdfViewer;
    private Boolean restrictRecordsByDepartment = false;
    private Boolean validateAccessFormDepartment = false;
    private String restrictRecordsField;
    private Long restrictRecordsObjId;

    @Id
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Basic(optional = false)
    @Column(name = "master_id", nullable = false, length = 36)
    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }

    @Column(name = "vch_clave")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Column(name = "vch_descripcion")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @ManyToOne
    @JoinColumn(name = "business_unit_id", insertable = false, updatable = false)
    public BusinessUnit getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnit businessUnit) {
        this.businessUnit = businessUnit;
    }

    @ManyToOne
    @JoinColumn(name = "business_unit_department_id", insertable = false, updatable = false)
    public BusinessUnitDepartment getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(BusinessUnitDepartment businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }

    @ManyToOne
    @JoinColumns({
        @JoinColumn(name = "id", referencedColumnName = "document_id", insertable = false, updatable = false)
    })
    public ReceiptAcknowledgment getReceiptAcknowledgment() {
        return receiptAcknowledgment;
    }

    public void setReceiptAcknowledgment(ReceiptAcknowledgment receiptAcknowledgment) {
        this.receiptAcknowledgment = receiptAcknowledgment;
    }

    public Integer getReaders() {
        return readers;
    }

    public void setReaders(Integer readers) {
        this.readers = readers;
    }

    @Column(name = "business_unit_id")
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id")
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }
    
    @Column(name = "enable_pdf_viewer")
    public Integer getEnablePdfViewer() {
        return enablePdfViewer;
    }

    public void setEnablePdfViewer(Integer enablePdfViewer) {
        this.enablePdfViewer = enablePdfViewer;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "restrict_records_department")
    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "validate_access_department")
    public Boolean getValidateAccessFormDepartment() {
        return validateAccessFormDepartment;
    }

    public void setValidateAccessFormDepartment(Boolean validateAccessFormDepartment) {
        this.validateAccessFormDepartment = validateAccessFormDepartment;
    }

    @Column(name = "restrict_records_field")
    public String getRestrictRecordsField() {
        return restrictRecordsField;
    }

    public void setRestrictRecordsField(String restrictRecordsField) {
        this.restrictRecordsField = restrictRecordsField;
    }

    @Column(name = "restrict_records_obj_id")
    public Long getRestrictRecordsObjId() {
        return restrictRecordsObjId;
    }

    public void setRestrictRecordsObjId(Long restrictRecordsObjId) {
        this.restrictRecordsObjId = restrictRecordsObjId;
    }
    

}
