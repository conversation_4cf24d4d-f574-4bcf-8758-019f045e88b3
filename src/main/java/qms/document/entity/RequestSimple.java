package qms.document.entity;

import DPMS.Mapping.NodeSimple;
import Framework.Config.StandardEntity;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Type;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "request")
@Immutable
public class RequestSimple extends StandardEntity<RequestSimple> implements IRequest{

    private static final long serialVersionUID = 1L;
    
    private String documentMasterId;
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private Long autorId;
    private String documentCode;
    private Date creationDate;
    private Integer type;
    private String version;
    private Long fileId;
    private Long surveyId;
    private Long outstandingSurveysId;

    private Long templateSurveyId;
    private Boolean restrictRecordsByDepartment = false;
    private Boolean validateAccessFormDepartment = false;
    private String restrictRecordsField;
    private Long restrictRecordsObjId;
    private Integer scope;
    private UserRef author;
    private BusinessUnitDepartmentRef department;
    private String reazon;
    private NodeSimple node;
    private Long documentId;

    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Integer enablePdfViewer;
    private String authorizersNames;

    private Long collectingAndStoreResponsible;
    private String collectingAndStoreResponsibleDescription;
    private Long informationClassification;
    private Long disposition;
    
    public RequestSimple() {
    }

    public RequestSimple(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Basic
    @Column(name = "document_master_id", length = 36)
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Basic(optional = false)
    @Column(name = "document_code")
    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Basic(optional = false)
    @Column(name = "creation_date", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Basic(optional = false)
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "survey_id")
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }
    
    @Column(name = "outstanding_surveys_id")
    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    @Column(name = "template_survey_id")
    public Long getTemplateSurveyId() {
        return templateSurveyId;
    }

    public void setTemplateSurveyId(Long templateSurveyId) {
        this.templateSurveyId = templateSurveyId;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "restrict_records_department")
    public Boolean getRestrictRecordsByDepartment() {
        return restrictRecordsByDepartment;
    }

    public void setRestrictRecordsByDepartment(Boolean restrictRecordsByDepartment) {
        this.restrictRecordsByDepartment = restrictRecordsByDepartment;
    }

    @Type(type = "numeric_boolean")
    @Column(name = "validate_access_department")
    public Boolean getValidateAccessFormDepartment() {
        return validateAccessFormDepartment;
    }

    public void setValidateAccessFormDepartment(Boolean validateAccessFormDepartment) {
        this.validateAccessFormDepartment = validateAccessFormDepartment;
    }

    @Column(name = "restrict_records_field")
    public String getRestrictRecordsField() {
        return restrictRecordsField;
    }

    public void setRestrictRecordsField(String restrictRecordsField) {
        this.restrictRecordsField = restrictRecordsField;
    }

    @Column(name = "restrict_records_obj_id")
    public Long getRestrictRecordsObjId() {
        return restrictRecordsObjId;
    }

    public void setRestrictRecordsObjId(Long restrictRecordsObjId) {
        this.restrictRecordsObjId = restrictRecordsObjId;
    }

    /**
     * @return the scope
     */
    @Column(name = "scope")
    public Integer getScope() {
        return scope;
    }

    /**
     * @param scope the scope to set
     */
    public void setScope(Integer scope) {
        this.scope = scope;
    }
    

    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "autor_id", referencedColumnName = "user_id")
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }
    
    @ManyToOne
    @JoinColumn(name = "business_unit_department_id", updatable = false, insertable = false)
    public BusinessUnitDepartmentRef getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentRef department) {
        this.department = department;
    }

    @Basic(optional = false)
    public String getReazon() {
        return reazon;
    }

    public void setReazon(String reazon) {
        this.reazon = reazon;
    }
    
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = "node_id", referencedColumnName = "intnodoid")
    public NodeSimple getNodo() {
        return node;
    }

    public void setNodo(NodeSimple node) {
        this.node = node;
    }
    
    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 73 * hash + Objects.hashCode(this.id);
        hash = 73 * hash + Objects.hashCode(this.code);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RequestSimple other = (RequestSimple) obj;
        return Objects.equals(this.id, other.id) && Objects.equals(this.code, other.code);
    }

    @Override
    public String toString() {
        return "RequestSimple{" + "id=" + id + ",code=" + code + '}';
    }

    @Column(name = "autor_id", updatable = false, insertable = false)
    @Override
    public Long getAutorId() {
        return autorId;
    }

    public void setAutorId(Long autorId) {
        this.autorId = autorId;
    }

    @Column(name = "business_unit_id")
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id")
    @Override
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }
    
    @Column(name = "enable_pdf_viewer")
    public Integer getEnablePdfViewer() {
        return enablePdfViewer;
    }

    public void setEnablePdfViewer(Integer enablePdfViewer) {
        this.enablePdfViewer = enablePdfViewer;
    }

    @Column(name = "authorizers_names")
    public String getAuthorizersNames() {
        return authorizersNames;
    }

    public void setAuthorizersNames(String authorizersNames) {
        this.authorizersNames = authorizersNames;
    }

    @Column(name = "collecting_store_responsible")
    public Long getCollectingAndStoreResponsible() {
        return collectingAndStoreResponsible;
    }

    public void setCollectingAndStoreResponsible(Long collectingAndStoreResponsible) {
        this.collectingAndStoreResponsible = collectingAndStoreResponsible;
    }
    
    @Column(name = "collecting_store_resp_desc")
    public String getCollectingAndStoreResponsibleDescription() {
        return collectingAndStoreResponsibleDescription;
    }

    public void setCollectingAndStoreResponsibleDescription(String collectingAndStoreResponsibleDescription) {
        this.collectingAndStoreResponsibleDescription = collectingAndStoreResponsibleDescription;
    }
    
    @Column(name = "information_classification")
    public Long getInformationClassification() {
        return informationClassification;
    }

    public void setInformationClassification(Long informationClassification) {
        this.informationClassification = informationClassification;
    }
    
    @Column(name = "disposition")
    public Long getDisposition() {
        return disposition;
    }

    public void setDisposition(Long disposition) {
        this.disposition = disposition;
    }
    
}
