package qms.document.entity;

import DPMS.Mapping.PositionRef;
import Framework.Config.DomainObject;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Immutable;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Table(name = "document_reader")
public class ReadersByDocument extends DomainObject {

    private static final long serialVersionUID = 1L;
    
    private Long documentId;
    private Long readerId;
    private Integer readed;
    private Date readedOn;
    private UserRef reader;
    private DocumentRef document;
    private UserRef responsible;
    private Date lastModifiedDate;
    private Date createdDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Set<PositionRef> positions;

    public ReadersByDocument() {
    }

    public ReadersByDocument(Long id) {
        this.id = id;
    }

    @Id
    @Column(name = "document_reader_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Basic(optional = false)
    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Basic(optional = false)
    @Column(name = "reader_id")
    public Long getReaderId() {
        return readerId;
    }

    public void setReaderId(Long readerId) {
        this.readerId = readerId;
    }

    @Column(name = "readed")
    public Integer getReaded() {
        return readed;
    }

    public void setReaded(Integer readed) {
        this.readed = readed;
    }

    @Column(name = "readed_on")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getReadedOn() {
        return readedOn;
    }

    public void setReadedOn(Date readedOn) {
        this.readedOn = readedOn;
    }
    
    @ManyToOne
    @JoinColumn(name = "reader_id", referencedColumnName = "user_id", insertable = false, updatable = false)
    public UserRef getReader() {
        return reader;
    }

    public void setReader(UserRef reader) {
        this.reader = reader;
    }
    
    @ManyToOne
    @JoinColumn(name = "document_id", referencedColumnName = "id", insertable = false, updatable = false)
    public DocumentRef getDocument() {
        return document;
    }

    public void setDocument(DocumentRef document) {
        this.document = document;
    }
    
    @ManyToOne
    @JoinColumn(name = "responsible_id", referencedColumnName = "user_id")
    public UserRef getResponsible(){
        return  responsible;
    }
    
    public void setResponsible(UserRef responsible){
        this.responsible = responsible;
    }

    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }
    
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "usuario_puesto",
            joinColumns = @JoinColumn(name = "usuario_id", referencedColumnName = "reader_id", insertable = false, updatable = false),
            inverseJoinColumns = @JoinColumn(name = "puesto_id", insertable = false, updatable = false))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<PositionRef> getPositions() {
        return positions;
    }

    public void setPositions(Set<PositionRef> positions) {
        this.positions = positions;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3356
        if (!(object instanceof ReadersByDocument)) {
            return false;
        }
        ReadersByDocument other = (ReadersByDocument) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "qms.document.entity.ReadersByDocument[ id=" + id + " ]";
    }
}
