
package qms.document.dao;

import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.io.Serializable;
import qms.document.entity.DocumentHistoryModifyed;

/**
 *
 * <AUTHOR> @Block Networks
 */
@Implementation(name = "DocumentHistoryModifyedDAO")
public interface IDocumentHistoryModifyedDAO extends IGenericDAO<DocumentHistoryModifyed, Serializable>{

    public void save(Long documentId, Long documentIdRelated);
    
    public String getLastDepartmentForDocument(Long documentId);
    
}
