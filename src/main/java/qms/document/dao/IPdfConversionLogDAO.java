package qms.document.dao;

import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import mx.bnext.core.pdf.util.ILogManager;
import qms.document.dto.PdfConversionInfoDTO;
import qms.document.entity.PdfConversionLog;

/**
 *
 * <AUTHOR>
 */
@Implementation(name = "PdfConversionLogDAO")
public interface IPdfConversionLogDAO extends IGenericDAO<PdfConversionLog, Long>, ILogManager {

    public PdfConversionInfoDTO getConversionInfo(final Long fileId);
}
