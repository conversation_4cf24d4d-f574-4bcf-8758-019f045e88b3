package qms.document.dao;

import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.Request;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import java.util.Calendar;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.document.entity.RequestHistory;

/**
 *
 * <AUTHOR> @Block Networks
 */
@Lazy
@Repository(value = "RequestHistoryDAO")
public class RequestHistoryDAO extends GenericDAOImpl<RequestHistory, Long> implements IRequestHistoryDAO {

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(Request reques, ILoggedUser loggedUser) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.setOperationEstatus(0);
        gsh.setErrorMessage("msgSaveError");
        try {
            String sQuery = ""
                    + " SELECT MAX(r.id.version) "
                    + " FROM " + RequestHistory.class.getCanonicalName() + " r "
                    + " WHERE r.id.requestId = " + reques.getId();

            Long version = HQL_findSimpleLong(sQuery);
            if (version != null) {
                version += 1;
            } else {
                version = 1L;
            }

            RequestHistory requestHistory = new RequestHistory(version, reques.getId());
            if (reques.getDepartment() != null) {
                requestHistory.setBusinessUnitDepartmentId(reques.getDepartment().getId());
            }
            requestHistory.setCreatedDate(Calendar.getInstance().getTime());
            requestHistory.setLastModifiedDate(Calendar.getInstance().getTime());
            requestHistory.setCreatedBy(loggedUser.getId());
            requestHistory.setLastModifiedBy(loggedUser.getId());
            makePersistent(requestHistory, loggedUser.getId());
            gsh.setOperationEstatus(1);
        } catch (Exception ex) {
            getLogger().error(ex.getMessage(), ex);
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getLastDepartmentsForRequest(Long requestId) {

        String sQuery = "";

        sQuery = "Select count(1) "
                + " From " + RequestHistory.class.getCanonicalName() + " rh "
                + " Where rh.id.requestId = " + requestId;

        Long maxVersion = HQL_findSimpleLong(sQuery);

        if (maxVersion > 1) {

            sQuery = " Select budl.description "
                    + " From " + RequestHistory.class.getCanonicalName() + " rh "
                    + " CROSS JOIN " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " budl "
                    + " Where rh.businessUnitDepartmentId = budl.id "
                    + "   and rh.id.version = " + (maxVersion - 1)
                    + "   and rh.id.requestId = " + requestId;
            return HQL_findSimpleString(sQuery);
        }
        return "";
    }
}
