package qms.document.core;

import DPMS.DAOInterface.IDocumentTypeDAO;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.Files;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingAnswer;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.entity.DynamicField;
import qms.document.entity.DocumentTypeDynamicField;
import qms.framework.entity.ExternalFileType;
import qms.framework.entity.ExternalUserAgent;
import qms.framework.entity.FileType;
import qms.framework.entity.ShareFileType;
import qms.framework.entity.ShareUserAgent;
import qms.framework.entity.UserAgent;
import qms.util.LinkedGridConfig;

/**
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 */
public class DocumentTypeService extends CRUD_Generic<DocumentType> {

    private static final long serialVersionUID = 1L;

    @Override
    protected GridInfo getDialogRows(SortedPagedFilter filter, LinkedGridConfig config) {
        if (Objects.equals(DocumentTypeDynamicField.class, config.getCompositeEntity())) {
            // Campos dinamicos agregados: "document-type.jsp"
            filter.getCriteria().put("<condition>", ""
                + " c.dynamicFieldType IN ("
                    + "'" + DynamicField.DYNAMIC_FIELD_TYPE.CATALOG.toString() + "'"
                    + "," + "'" + DynamicField.DYNAMIC_FIELD_TYPE.CATALOG_MULTIPLE.toString() + "'"
                    + "," + "'" + DynamicField.DYNAMIC_FIELD_TYPE.TEXT.toString() + "'"
                + " )"
            );
        }
        return super.getDialogRows(filter, config);
    }
    
    @Override
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        switch (linkedGridId) {
            case "document_type_dynamic_fields": // Campos dinamicos agregados: "document-type.jsp"
                return new LinkedGridConfig(
                        DynamicField.STATUS.ACTIVE,
                        IDynamicFieldDAO.class,
                        new String[]{"description"}, //etiqueta
                        groundId,
                        DocumentTypeDynamicField.class
                );
            case "shareUserAgents":
                return new LinkedGridConfig(
                        UserAgent.STATUS.ACTIVE,
                        IUntypedDAO.class,
                        UserAgent.class, 
                        new String[]{
                            "description", "code", "status"
                        },
                        groundId,
                        ShareUserAgent.class
                );
            case "shareFileTypes":
                return new LinkedGridConfig(
                        FileType.STATUS.ACTIVE,
                        IUntypedDAO.class, 
                        FileType.class,
                        new String[]{
                            "description", "code", "status"
                        },
                        groundId,
                        ShareFileType.class
                );
            case "externalUserAgents":
                return new LinkedGridConfig(
                        UserAgent.STATUS.ACTIVE,
                        IUntypedDAO.class,
                        UserAgent.class,
                        new String[]{
                            "description", "code", "status"
                        },
                        groundId,
                        ExternalUserAgent.class
                );
            case "externalFileTypes":
                return new LinkedGridConfig(
                        FileType.STATUS.ACTIVE,
                        IUntypedDAO.class, 
                        FileType.class,
                        new String[]{
                            "description", "code", "status"
                        },
                        groundId,
                        ExternalFileType.class
                );
            default:
                return super.getLinkedGridConfig(linkedGridId, groundId);
        }
    }

    @SMDMethod
    public GenericSaveHandle save(DocumentType entity) throws Exception {
        getLogger().trace(">> Guardando {}", className);
        if (!hasSaveAccess(entity)) {
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setErrorMessage("Usted no tiene permiso para guardar");
            return gsh;
        }
        final IDocumentTypeDAO dao = getBean(IDocumentTypeDAO.class);
        final GenericSaveHandle gsh = dao.save(entity, getLoggedUserDto());
        if (gsh.getOperationEstatus().equals(1)) {
            Utilities.resetDocumentTypes();
            Utilities.resetShareFileTypes();
            Utilities.resetExternalFileTypes();
            // Actualiza en BATCHS de 1000, los archivos que no tienen habilitada la conversión a PDF
            boolean pdfConversionEnabled = IDocumentTypeDAO.isPdfImagesEnabled(entity);
            Integer limit = Utilities.getSettings().getFilePdfPagesEnabledCount();
            List<Long> files = getFilesFromForms(entity.getId(), pdfConversionEnabled, limit);
            while (!files.isEmpty()) {
                // De 1000 en 1000 hasta terminar
                dao.refreshFormFilesConversion(files, pdfConversionEnabled, getLoggedUserDto());
                files = getFilesFromForms(entity.getId(), pdfConversionEnabled, limit);
            }
        }
        return gsh;
    }

    private List<Long> getFilesFromForms(Long documentTypeId, boolean pdfConversionEnabled, Integer limit) {
        return getUntypedDAO().HQL_findByQueryLimit(" "
                        + " SELECT c.id "
                        + " FROM " + Files.class.getCanonicalName() + " c "
                        + " JOIN " + OutstandingAnswer.class.getCanonicalName() + " oa ON c.id = oa.fileId "
                        + " JOIN " + OutstandingSurveys.class.getCanonicalName() + " o ON o.id = oa.outstandingSurveyId "
                        + " JOIN " + Document.class.getCanonicalName() + " d ON d.surveyId = o.surveyId "
                        + " WHERE "
                        + " d.typeId = :typeId "
                        + " AND c.pdfConversionEnabled = :pdfConversionEnabled ", ImmutableMap.of(
                        "pdfConversionEnabled", !pdfConversionEnabled,
                        "typeId", documentTypeId
                ), limit
        );
    }
    
    @SMDMethod
    public Map<String, Object> documentsNameByType(Long documentTypeId) {
        
        Map<String, Object> result = new HashMap<>();
        String where = "WHERE doc.typeId = " + documentTypeId + " AND doc.status = 1 AND doc.expirationDate > GETDATE()";
        
        List<String> documentNames = getUntypedDAO().HQL_findByQueryLimit(""
                + "SELECT doc.description "
                + "FROM " + Document.class.getCanonicalName() + " doc "
                + where, 10);
        
        Long documentAffectedCount = getUntypedDAO().HQL_findSimpleLong(""
                + "SELECT count(doc.description) "
                + "FROM " + Document.class.getCanonicalName() + " doc "
                + where);
        
        result.put("documentNames", documentNames);
        result.put("documentAffectedCount", documentAffectedCount);
        
        return result;
    }
}
