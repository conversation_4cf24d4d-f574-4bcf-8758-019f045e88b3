package qms.document.core;

import DPMS.Mapping.Request;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;

/**
 *
 * <AUTHOR>
 */
public class SequenceDetailAction extends DefaultAction {

    private Long requestId;
    private Long outstandingSurveyId;
    
    @Override
    public String execute() throws Exception {
        if (this.requestId == null && this.outstandingSurveyId != null) {
            this.requestId = Utilities.getUntypedDAO().HQL_findLong(""
                + " SELECT r.id "
                + " FROM " + Request.class.getCanonicalName() + " r "
                + " WHERE r.outstandingSurveysId = " + this.outstandingSurveyId
            );
        }
        return super.execute();
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }

    
    
}
