package qms.document.core;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.User;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import com.google.common.collect.ImmutableMap;
import mx.bnext.access.ProfileServices;
import qms.framework.player.VideoCompatibilityHTML5;

/**
 *
 * <AUTHOR>
 */
public class DocumentSimpleAction extends DefaultAction {

    public String getSerializedVideoCompatibilityTypes() {
        return Utilities.getSerializedObj(VideoCompatibilityHTML5.getContentTypes());
    }

    public String getSerializedShareFileTypes() {
        if (!isAdmin() && !getLoggedUserServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            return "[]";
        }
        return Utilities.getSerializedObj(Utilities.getShareFileTypes());
    }

    public String getSerializedExternalFileTypes() {
        if (!isAdmin() && !getLoggedUserServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            return "[]";
        }
        return Utilities.getSerializedObj(Utilities.getExternalFileTypes());
    }

    public Integer getCanShareDocument() {
        final Long loggedUserId = getLoggedUserId();
        if (loggedUserId == null) {
            return 0;
        }
        if (!isAdmin() && !getLoggedUserServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            return 0;
        }
        return Utilities.getSettings().getShareDocuments();
    }

    public Integer getExternalLink() {
        if (!isAdmin() && !getLoggedUserServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            return 0;
        }
        return Utilities.getSettings().getExternalLink();
    }

    public String getSerializedDocumentTypes() {
        return Utilities.getSerializedObj(Utilities.getDocumentTypes());
    }

    public Integer getShowExternalDialog() {
        if (!isAdmin() && !getLoggedUserServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            return 0;
        }
        IUserDAO dao = getBean(IUserDAO.class);
        return dao.HQL_findSimpleInteger(""
                + " SELECT c.showExternalDialog "
                + " FROM " + User.class.getCanonicalName() + " c "
                + " WHERE id = :id",
                ImmutableMap.of("id", getLoggedUserId())
        );
    }

    public Boolean getOnlyDocumentManagerReapprove() {
        return Utilities.getSettings().getReapproveByDocumentManager() == 1;
    }
}
