package qms.document.core;

import DPMS.DAOInterface.IFilesDAO;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import bnext.dto.DownloadDto;
import java.io.FileNotFoundException;
import java.util.List;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.errors.ClientAbortException;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.pdf.dto.PdfConversionDTO;
import org.apache.commons.lang3.StringUtils;
import qms.framework.entity.DocumentLink;
import qms.framework.file.FileManager;
import qms.framework.util.DocumentLinkUtil;
import qms.framework.util.SettingsUtil;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class DocumentViewerLinkAction extends DefaultAction {

    private static final long serialVersionUID = 1L;

    private String token;
    private String error = null;
    private Long fileId;
    private Long documentId;
    private Integer pageNum;
    private boolean attendant = false;
    private Integer downloadAttempt = 0;
    private Integer downloadAttemptsLimit;
    private boolean uncontrolled_copy_access = false;
    private boolean bnextLogo = false;
    private final boolean documentCopyAvailable = false;
    private final Boolean documentControlledCopyAvailable = false;
    private boolean reading = false;
    private boolean isForm = false;
    private final IFilesDAO dao;
    private final boolean existsDefaultFileId = false;
    private final Long defaultFileId = null;

    private String downloadResult;
    private String pdfPagesSerialized;
    private String pdfSha512 = "-";
    private Boolean pdfViewerFitDocumentScreen = true;
    private Integer pdfImageDpi;
    private Integer pagesGenerated;
    private boolean generatingPages = false;
    private Integer rotationDegrees = 0;
    private boolean converting = false;
    private Integer queueSize;
    private String contentType;
    private Integer versionMessage = 0;

    private boolean validSession;
    private final FileManager fileManager = new FileManager();

    public DocumentViewerLinkAction() {
        downloadAttemptsLimit = Utilities.getSettings().getDownloadAttemptsLimit();
        dao = getBean(IFilesDAO.class);
    }

    @Override
    protected boolean isNewEntity() {
        return false;
    }
    
    @Override
    @SuppressWarnings("empty-statement")
    public String execute() throws Exception {
        setValidSession(isValidSession());
        boolean result = initializeSharedDocument();
        if (!result) {
            return "error";
        }
        if (isForm) {
            return "is_form";
        }
        if (reading) {
            try {
                final Long pdfPageId = dao.getPdfPageId(fileId, pageNum);
                if (null != pdfPageId && pdfPageId > 0) {
                    fileManager.writePageToResponse(
                            pdfPageId,
                            fileId,
                            rotationDegrees,
                            response,
                            getLoggedUserId()
                    );
                } else {
                    fileManager.writePageToResponse(
                            fileId,
                            pageNum,
                            rotationDegrees,
                            response,
                            getLoggedUserId()
                    );
                }
            } catch (final ClientAbortException ex) {
                getLogger().debug("Client cancelled request for view file with id {}",
                        new Object[]{fileId, ex.getMessage()});
                return null;
            }
            return null;
        } else {
            try {
                return prepareViewer(fileId);
            } catch (final ClientAbortException ex) {
                getLogger().debug("Client cancelled request for prepare viewer for file with id {}",
                        new Object[]{fileId, ex.getMessage()});
                return null;
            }
        }
    }

    public String getDownloadResult() {
        return downloadResult;
    }

    private String prepareViewer(Long fileId) throws FileNotFoundException {
        DownloadDto dto = new DownloadDto(
                fileId,
                documentId,
                0,
                0,
                getPageNum(),
                isAttendant(),
                getDownloadAttempt(),
                getDownloadAttemptsLimit(),
                false);
        uncontrolled_copy_access = getLoggedUserServices().contains(ProfileServices.DOCUMENT_PRINT_UNCONTROLLED_COPIES);
        final PdfConversionDTO conversion = fileManager.generateFilePdfAndDownload(
                dto, isAdmin(),
                getLoggedUserServices(),
                response,
                FileUtils.COLUMN_FILES_CONTENT,
                getLoggedUserDto()
        );
        if (FileManager.CONTEXT_TYPE_DWG.equals(conversion.getContentType())) {
            pdfImageDpi = Utilities.getSettings().getDwgImageDpi();
        } else {
            pdfImageDpi = Utilities.getSettings().getPdfImageDpi();
        }
        downloadResult = conversion.getMessage();
        queueSize = conversion.getQueueSize();
        converting = conversion.isConverting();
        pageNum = conversion.getNumberPages();
        pagesGenerated = conversion.getPagesGenerated();
        generatingPages = conversion.isGeneratingPages();
        pdfSha512 = conversion.getPdfSha512();
        contentType = conversion.getContentType();
        if (conversion.getHasPdfPages() != null && conversion.getHasPdfPages().equals(1)) {
            final List<Long> pdfPages = dao.getPdfPageIds(fileId);
            pdfPagesSerialized = "[" + StringUtils.join(pdfPages, ",") + "]";
        } else {
            pdfPagesSerialized = "[]";
        }
        attendant = dto.isAttendant();
        downloadAttempt = dto.getDownloadAttempt();
        return downloadResult;

    }

    private boolean initializeSharedDocument() {
        final DocumentLink link = DocumentLinkUtil.loadDocumentLink(getToken(), dao);
        if (link == null) {
            error = getTag("invalid_link");
            return false;
        }
        final DocumentLinkUtil linkUtil = new DocumentLinkUtil(link, dao, DocumentLink.TYPE.VIEWER_LINK);
        final String tokenError = StringUtils.isBlank(linkUtil.getError()) ? "tokenError": linkUtil.getError();
        if (reading) {
            if (!linkUtil.isValidSession(request)) {
                error = getTag(tokenError);
                getLogger().error(error + " Link: {}, Session: {}", link.getCode(), request.getSession().getId());
                return false;
            }
        } else {
            if (!linkUtil.validLink(request)) {
                error = getTag(tokenError);
                getLogger().error(error + " Link: {}, Session: {}", link.getCode(), request.getSession().getId());
                return false;
            }
            linkUtil.putSessionToken(request.getSession());
            
        }
        documentId = link.getDocumentId();
        pdfViewerFitDocumentScreen = linkUtil.getPdfViewerFitDocumentScreen();
        versionMessage = DocumentLinkUtil.isEarlierVersionDoc(documentId, dao);
        
        boolean hasFile = linkUtil.hasFile(link.getDocumentId());
        if (hasFile) {
            this.fileId = linkUtil.loadFile().getId();
        } else {
            isForm = true;
        }
        if (getLoggedUserId() == 0L) {
            setLoggedUserId(link.getCreatedBy());
        }
        return true;
    }

    /**
     * @return the token
     */
    public String getToken() {
        return token;
    }

    /**
     * @param token the token to set
     */
    public void setToken(String token) {
        this.token = token;
    }
    
    /**
     * @return the error
     */
    public String getError() {
        return error;
    }

    /**
     * @return the pageNum
     */
    public Integer getPageNum() {
        return pageNum;
    }

    /**
     * @param pageNum the pageNum to set
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * @return the attendant
     */
    public boolean isAttendant() {
        return attendant;
    }

    /**
     * @param attendant the attendant to set
     */
    public void setAttendant(boolean attendant) {
        this.attendant = attendant;
    }

    /**
     * @return the downloadAttempt
     */
    public Integer getDownloadAttempt() {
        return downloadAttempt;
    }

    /**
     * @param downloadAttempt the downloadAttempt to set
     */
    public void setDownloadAttempt(Integer downloadAttempt) {
        this.downloadAttempt = downloadAttempt;
    }

    /**
     * @return the uncontrolled_copy_access
     */
    public boolean isUncontrolled_copy_access() {
        return uncontrolled_copy_access;
    }

    /**
     * @return the downloadAttemptsLimit
     */
    public Integer getDownloadAttemptsLimit() {
        return downloadAttemptsLimit;
    }

    /**
     * @param downloadAttemptsLimit the downloadAttemptsLimit to set
     */
    public void setDownloadAttemptsLimit(Integer downloadAttemptsLimit) {
        this.downloadAttemptsLimit = downloadAttemptsLimit;
    }

    public String getDocumentIdParam() {
        return documentId != null ? "&documentId=" + documentId : "";
    }

    public Long getFileId() {
        return fileId;
    }

    public String getBnextLogoParam() {
        return bnextLogo ? "bnextLogo=" + bnextLogo : "";
    }

    /**
     * @return the bnextLogo
     */
    public boolean isBnextLogo() {
        return bnextLogo;
    }

    /**
     * @param bnextLogo the bnextLogo to set
     */
    public void setBnextLogo(boolean bnextLogo) {
        this.bnextLogo = bnextLogo;
    }

    /**
     * @return the reading
     */
    public boolean isReading() {
        return reading;
    }

    /**
     * @param reading the reading to set
     */
    public void setReading(boolean reading) {
        this.reading = reading;
    }

    /**
     * @return the documentCopyAvailable
     */
    public boolean isDocumentCopyAvailable() {
        return documentCopyAvailable;
    }

    public Boolean getDocumentControlledCopyAvailable() {
        return documentControlledCopyAvailable;
    }
    
    public boolean isViewerToken() {
        return true;
    }

    public boolean isExistsDefaultFileId() {
        return existsDefaultFileId;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public Long getDefaultFileId() {
        return defaultFileId;
    }

    public String getUrl() {
        return SettingsUtil.getAppUrlNoSlash();
    }

    public String getPdfPagesSerialized() {
        return pdfPagesSerialized;
    }

    public Integer getPagesGenerated() {
        return pagesGenerated;
    }

    public boolean isGeneratingPages() {
        return generatingPages;
    }

    public void setRotationDegrees(Integer rotationDegrees) {
        this.rotationDegrees = rotationDegrees;
    }

    public boolean isViewFinder() {
        return false;
    }

    public boolean isConverting() {
        return converting;
    }

    public Integer getQueueSize() {
        return queueSize;
    }

    public String getContentType() {
        return contentType;
    }
        
    public boolean isPrintableDocument() {
        return false;
    }

    public String getPdfSha512() {
        return pdfSha512;
    }

    public Boolean getPdfViewerFitDocumentScreen() {
        return pdfViewerFitDocumentScreen;
    }

    public Integer getPdfImageDpi() {
        return pdfImageDpi;
    }


    
    public boolean getValidSession() {
        return validSession;
    }


    public void setValidSession(boolean validSession) {
        this.validSession = validSession;
    }
    public Integer getVersionMessage() {
        return versionMessage;
    }

}
