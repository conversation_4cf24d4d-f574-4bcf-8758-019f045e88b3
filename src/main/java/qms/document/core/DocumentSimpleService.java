
package qms.document.core;

import DPMS.Document.DocumentViewerDTO;
import DPMS.Mapping.Catalog;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentSimple;
import DPMS.Mapping.RelatedDocumentView;
import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import com.google.common.base.CaseFormat;
import isoblock.common.Properties;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.document.entity.DocumentMaster;
import qms.document.entity.DocumentReference;
import qms.util.LinkedGridConfig;
import qms.util.interfaces.LinkedGridType;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class DocumentSimpleService extends CRUD_Generic<DocumentSimple> {

    @SMDMethod
    public DocumentViewerDTO getDocumentSimple(Long id) {
        if(id == -1L) {
            return new DocumentViewerDTO();
        }
        DocumentViewerDTO documentViewer = new DocumentViewerDTO();
        DocumentSimple document = (DocumentSimple) getUntypedDAO().HQL_findSimpleObject("SELECT c FROM " + DocumentSimple.class.getCanonicalName() + " c WHERE c.id = " + id); 
        if (document.getCollectingAndStoreResponsible() != null) {
            document.setCollectingAndStoreResponsibleDescription(document.getCollectingAndStoreResponsible().getDescription());
        }
        documentViewer.setDocument(document);

        if (document.getDisposition() != null) {
            String disposition = getUntypedDAO().HQL_findSimpleString(""
                + " SELECT c.code "
                + " FROM " + Catalog.class.getCanonicalName() + " c "
                + " WHERE c.id.catalogoId = " + Properties.CATALOGO_DISPOSICION
                + " AND c.id.tipoId = :id", "id", document.getDisposition()
            );
            documentViewer.setDisposition(disposition);
        }
        if (document.getInformationClassification() != null) {
            String informationClassification = getUntypedDAO().HQL_findSimpleString(""
                + " SELECT c.code "
                + " FROM " + Catalog.class.getCanonicalName() + " c "
                + " WHERE c.id.catalogoId = " + Properties.CATALOGO_CLASIFICACION_INFORMACION
                + " AND c.id.tipoId = :id", "id", document.getInformationClassification()
            );
            documentViewer.setInformationClassification(informationClassification);
        }
        
        Boolean uncontrolled = document.getDocumentType().getDescription().equals("UNCONTROLLED");
        Boolean isDocumentAuthor = getLoggedUserDto().getId().equals(document.getAuthorId());
        Boolean isDocumentManager = getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO);
        Boolean isDocumentEditor = getLoggedUserServices().contains(ProfileServices.DOCUMENTO_EDITOR);
        Boolean isDepartmentManager = getLoggedUserDto().getId().equals(document.getDepartment().getAttendantId());

        if ( 
                document.getFileId() != null
            && (isDocumentAuthor || isDocumentManager || isDocumentEditor || isDepartmentManager)
            && (
                ( uncontrolled && document.getDocumentType().getDownloadFiles().equals(1))
                || ( !uncontrolled && ((document.getDocumentType().getReadOnlyEnabled().equals(1) && isDocumentManager) || !document.getDocumentType().getReadOnlyEnabled().equals(1) ))
            )) {
            documentViewer.setCanDownloadDocument(true);
        } else {
            documentViewer.setCanDownloadDocument(false);
        }
        return documentViewer;
    }

    @SMDMethod
    public String getDocumentCode(Long id) {
        if(id == null || id == -1L) {
            return null;
        }
        return getUntypedDAO().HQL_findSimpleString("SELECT c.code FROM " + DocumentSimple.class.getCanonicalName() + " c WHERE c.id = " + id);
    }

    @SMDMethod
    public List<DocumentSimple> getRelatedDocuments(Long id) {
        if(id == null || id == -1L) {
            return Utilities.EMPTY_LIST;
        }
        return getUntypedDAO().HQLT_findByQueryFilter(DocumentSimple.class, ""
            + " c.id IN ("
                + " SELECT x.id.documentIdB "
                + " FROM " + RelatedDocumentView.class.getCanonicalName() + " x"
                + " WHERE x.id.documentIdA = " + id
            + " )"
            + " AND c.status = " + Document.ACTIVE_STATUS
        );
    }

    @Override
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        getLogger().trace("linkedGridId: {}, groundId: {}", linkedGridId, groundId);
        linkedGridId = linkedGridId.split("_")[0];
        LinkedGridType linkedGridEnum =
                LinkedGridType.valueOf(CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, linkedGridId));
        switch (linkedGridEnum) {
            case DOCUMENTS:
                String[] columns = null;
                switch(Settings.CODE_SCOPE.fromValue(Utilities.getSettings().getDocumentCodeScope())) {
                    case LEVEL_APPLICATION:
                        columns = new String[] {
                            "code", "description", "path"
                        };
                        break;
                    case LEVEL_BUSINESS_UNIT:
                        columns = new String[] {
                            "code", "description", "path", "businessUnit.description"
                        };
                        break;
                }
                return new LinkedGridConfig(
                    "c.status = " + Document.STATUS.ACTIVE.getValue(),
                    IUntypedDAO.class, 
                    DocumentMaster.class,
                    columns,
                    groundId, DocumentReference.class
                );
        }
        return super.getLinkedGridConfig(linkedGridId, groundId);
    }

    @SMDMethod
    public GenericSaveHandle saveReferences(Long nodeId, List<String> masterIds) {
        final IUntypedDAO dao = getUntypedDAO();
        dao.saveLinkedItems(DocumentReference.class, nodeId, masterIds, true, getLoggedUserId());
        return dao.successGSH();
    }


    @SMDMethod
    public Settings.CODE_SCOPE getDocumentCodeScope() {
        return Settings.CODE_SCOPE.fromValue(Utilities.getSettings().getDocumentCodeScope());
    }
}
