package qms.document.core;

import DPMS.Action.Action_RequestSurveyCapture;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IFilesDAO;
import Framework.Action.BnextAction;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import isoblock.common.Properties;
import java.util.List;
import qms.form.core.ISurveyToHtml;
import qms.framework.entity.DocumentLink;
import qms.framework.util.DocumentLinkUtil;
import qms.survey.dto.SurveyCaptureConfig;

/**
 *
 * <AUTHOR>
 */
public class FormViewerLinkAction extends BnextAction implements ISurveyCaptureAction {
    
    private String token;
    private final ISurveyCaptureAction capture;
    
    public FormViewerLinkAction() {
        capture = new Action_RequestSurveyCapture();
    }
    
    
    @SuppressWarnings("empty-statement")
    @Override
    public String execute() throws Exception {      
        final SurveyCaptureConfig config = new SurveyCaptureConfig();
        config.setNoSessionMode(true);
        return doExecute(config);
    }

    @Override
    public String doExecute(final SurveyCaptureConfig config) throws Exception {  
        capture.setTask("preview");
        capture.setRequestMode("PREVIEW");
        capture.setSurveyType("request");
        capture.setViewFinder(true);
        loadSurveyData(capture);
        return capture.doExecute(config);  
    }

    private void loadSurveyData(final ISurveyCaptureAction capture) {
        final IFilesDAO dao = getBean(IFilesDAO.class);
        final DocumentLink link = DocumentLinkUtil.loadDocumentLink(token, dao);
        final DocumentLinkUtil linkUtil = new DocumentLinkUtil(link, dao, DocumentLink.TYPE.VIEWER_LINK);
        if (!linkUtil.validLink(getRequest())) {
            return;
        }
        capture.setDocumentId(link.getDocumentId());
        final IDocumentDAO documentDao = getBean(IDocumentDAO.class);
        final Long surveyId = documentDao.getSurveyId(link.getDocumentId());
        capture.setId(surveyId.toString());
    }

    @Override
    public String getLoggedUserLocale() {
        return Utilities.getSettings().getLocale();
    }
    
    @Override
    public String getApplicationInfo() {
        return Properties.ApplicationData() + " - " + getLoggedUserLang() + "-" + getLoggedUserLocale();
    }
    
    @Override
    public String getLoggedUserLang() {
        return Utilities.getSettings().getLang();
    }
    
    public void setToken(String token) {
        this.token = token;
    }    
    
    @Override
    public String getSerializedOutstandingSurvey() {
        return capture.getSerializedOutstandingSurvey();
    }
    
    @Override
    public String getSurveyModule() {
        return capture.getSurveyModule();
    }
    
    @Override
    public ISurveyToHtml getSurveyToHtml() {
        return capture.getSurveyToHtml();
    }
    
    @Override
    public String getDefaultSourceId() {
        return capture.getDefaultSourceId();
    }
    
    @Override
    public Boolean getActivityManager() {
        return capture.getActivityManager();
    }
    
    @Override
    public String getSerializedStatusList() {
        return capture.getSerializedStatusList();
    }
    
    @Override
    public Boolean getShowActivities() {
        return capture.getShowActivities();
    }
    
    @Override
    public List<ITextHasValue> getActionSystem() {
        return capture.getActionSystem();
    }
    
    @Override
    public String getSurveyType() {
        return capture.getSurveyType();
    }
    
    @Override
    public Long getDocumentId() {
        return capture.getDocumentId();
    }
    
    @Override
    public Long getRequestId() {
        return capture.getRequestId();
    }
    
    @Override
    public boolean isPreviewMode() {
        return capture.isPreviewMode();
    }
    
    @Override
    public String getTask() {
        return capture.getTask();
    }
    
    @Override
    public String getScore() {
        return capture.getScore();
    }
    
    @Override
    public List<ITextHasValue> getActionTypes() {
        return capture.getActionTypes();
    }
    
    @Override
    public String getAuditIndividualCode() {
        return capture.getAuditIndividualCode();
    }
    
    @Override
    public List<ITextHasValue> getActionSources() {
        return capture.getActionSources();
    }
    
    @Override
    public String getAuditIndividualDepartment() {
        return capture.getAuditIndividualDepartment();
    }
    
    @Override
    public String getAuditIndividualStatus() {
        return capture.getAuditIndividualStatus();
    }
    
    @Override
    public String getAuditIndividualId() {
        return capture.getAuditIndividualId();
    }
    
    @Override
    public boolean isAuditedAccess() {
        return capture.isAuditedAccess();
    }
    
    @Override
    public String getLoadAction() {
        return "../view/viewfinder-form-link.view";
    }
    
    @Override
    public boolean isAudit() {
        return capture.isAudit();
    }
    
    @Override
    public boolean isGuardadoTotalmente() {
        return capture.isGuardadoTotalmente();
    }
    
    @Override
    public String getEndMessage() {
        return capture.getEndMessage();
    }
    
    @Override
    public String getAutoSaveTime() {
        return capture.getAutoSaveTime();
    }
    
    @Override
    public boolean isResponderEncuesta() {
        return capture.isResponderEncuesta();
    }
    
    @Override
    public String getId() {
        return capture.getId();
    }
    
    @Override
    public String getHtml() {
        return capture.getHtml();
    }
    
    @Override
    public String getResolution() {
        return capture.getResolution();
    }
    
    @Override
    public String getOutstandingid() {
        return capture.getOutstandingid();
    }
    
    @Override
    public String getEstatus() {
        return capture.getEstatus();
    }
    
    @Override
    public boolean isJustLoad() {
        return capture.isJustLoad();
    }
    
    @Override
    public String getTiendaid() {
        return capture.getTiendaid();
    }
    
    @Override
    public String getSurveyid() {
        return capture.getSurveyid();
    }
    
    @Override
    public String getBloqueid() {
        return capture.getBloqueid();
    }
    
    @Override
    public String getGotojsp() {
        return capture.getGotojsp();
    }
    
    @Override
    public String getRequestMode() {
        return capture.getRequestMode();
    }
    
    @Override
    public Integer getInvalidAutorizationPoolIndex() {
        return -1;
    }

    @Override
    public void setTask(String preview) {
        throw new UnsupportedOperationException("Not supported for unlogged users.");
    }

    @Override
    public void setRequestMode(String preview) {
        throw new UnsupportedOperationException("Not supported for unlogged users.");
    }

    @Override
    public void setSurveyType(String request) {
        throw new UnsupportedOperationException("Not supported for unlogged users.");
    }

    @Override
    public void setViewFinder(Boolean viewFinder) {
        throw new UnsupportedOperationException("Not supported for unlogged users.");
    }

    @Override
    public void setDocumentId(Long documentId) {
        throw new UnsupportedOperationException("Not supported for unlogged users.");
    }

    @Override
    public void setId(String toString) {
        throw new UnsupportedOperationException("Not supported for unlogged users.");
    }
}
