package qms.document.core;

import Framework.Action.SessionViewer;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import com.opensymphony.xwork2.Action;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.document.dao.IPdfConversionLogDAO;
import qms.document.dto.PdfConversionInfoDTO;
import qms.document.entity.PdfConversionLog;
import qms.document.entity.PdfDocumentLog;
import qms.document.entity.PdfRequestLog;
 
/**
 *
 * <AUTHOR>
 */
public class PdfConversionLogService extends SessionViewer {

    private static final long serialVersionUID = 1L;
 
    public String smd() {
        return Action.SUCCESS;
    }
    
    @SMDMethod
    public GridInfo<PdfConversionLog> getAllRows(SortedPagedFilter filter) {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return Utilities.EMPTY_GRID_INFO;
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("lastModifiedDate");
            filter.setDirection(Byte.parseByte("2"));
        }
        return getUntypedDAO().getRows(PdfConversionLog.class, filter);
    }
    
    @SMDMethod
    public PdfConversionInfoDTO getConversionInfo(final Long fileId) {
        return getBean(IPdfConversionLogDAO.class).getConversionInfo(fileId);
    }
    
    @SMDMethod
    public GridInfo<PdfDocumentLog> getDocumentRows(SortedPagedFilter filter) {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return Utilities.EMPTY_GRID_INFO;
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("log.lastModifiedDate");
            filter.setDirection(Byte.parseByte("2"));
        }
        return getUntypedDAO().getRows(PdfDocumentLog.class, filter);
    }
    
    @SMDMethod
    public GridInfo<PdfRequestLog> getRequestRows(SortedPagedFilter filter) {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return Utilities.EMPTY_GRID_INFO;
        }
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("log.lastModifiedDate");
            filter.setDirection(Byte.parseByte("2"));
        }
        return getUntypedDAO().getRows(PdfRequestLog.class, filter);
    }
}