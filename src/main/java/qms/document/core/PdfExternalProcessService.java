package qms.document.core;

import Framework.Action.SessionViewer;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import mx.bnext.core.pdf.util.IPdfDaemonConfig;
import mx.bnext.core.pdf.util.IPdfManager;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.windows.dto.ProcessInfo;
import mx.bnext.core.windows.dto.ProcessResult;
import mx.bnext.core.windows.util.ProcessUtil;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.framework.pdf.PdfProgramUtil;

/**
 *
 * <AUTHOR>
 */
public class PdfExternalProcessService extends SessionViewer {

    private static final long serialVersionUID = 1L;
 
    public String smd() {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_SISTEMA)) {
            return NO_ACCESS;
        }
        return SUCCESS;
    }
        
    
    @SMDMethod
    public GridInfo<ProcessInfo> getRows(SortedPagedFilter filter) {
        final boolean closeProcess = Utilities.getSettings().getCloseOfficePrograms().equals(1);
        if (!closeProcess) {
            return Utilities.EMPTY_GRID_INFO;
        } 
        final IPdfDaemonConfig config = PdfProgramUtil.getPdfConfig();
        final IPdfManager pdfManager = config.getPdfManager();
        return pdfManager.getProcesses();
    }
    
    @SMDMethod
    public GenericSaveHandle endTask(final String pid) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final boolean closeProcess = Utilities.getSettings().getCloseOfficePrograms().equals(1);
        if (!closeProcess) {
            gsh.setErrorMessage("not_enabled_close_office_programs");
            return gsh;
        }
        final ProcessResult result = ProcessUtil.killProcess(pid);
        if (result.getSuccess()) {
            gsh.setOperationEstatus(1);
        } else {
            gsh.setErrorMessage(result.getResult());
        }
        return gsh;
    }
    
}
