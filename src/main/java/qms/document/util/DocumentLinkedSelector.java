package qms.document.util;

import DPMS.DAOInterface.IDocumentDAO;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import mx.bnext.core.util.GridInfo;
import qms.access.dto.ILoggedUser;
import qms.document.dto.DocumentLinkedSelectorDTO;
import qms.util.GridFilter;
import qms.util.LinkedSelector;
import qms.util.interfaces.ILinkedSelector;

/**
 *
 * <AUTHOR>
 */
public class DocumentLinkedSelector extends LinkedSelector implements ILinkedSelector {
 
    @Override
    public GridInfo<DocumentLinkedSelectorDTO> getDialogRows(final IUntypedDAO dao, final GridFilter filter, ILoggedUser loggedUser) {
        final IDocumentDAO documentDao = dao.getBean(IDocumentDAO.class);
        final String condition = documentDao.filterRowsForCurrentUser(loggedUser);
        final Object previousCondition = filter.getCriteria().get("<condition>");
        if (previousCondition == null || previousCondition.toString().trim().isEmpty()) {
            filter.getCriteria().put("<condition>", condition);
        } else {
            filter.getCriteria().put("<condition>", previousCondition + " AND " + condition);
        }
        if (!filter.getCriteria().containsKey("deleted")) {
            filter.getCriteria().put("deleted", "0");
        }
        filter.getField().setOrderBy("id");
        filter.setDirection((byte) 2);
        return dao.HQL_getRows(new StringBuilder(""
                + " SELECT new " + DocumentLinkedSelectorDTO.class.getCanonicalName() + " ( "
                    + DocumentLinkedSelectorDTO.HQL_DIALOG_DOCUMENT_LINKED_SELECTOR
                + " )"
                + " FROM " + Document.class.getCanonicalName() + " c "
                + " WHERE c.status = " + Document.STATUS.ACTIVE.getValue() + ""
                + " AND c.documentType.documentControlledType = '" + DocumentType.documentControlledType.CONTROLLED.toString() + "'"), 
                filter, 
                getCacheable(),
                getCacheRegion(),
                0
        );
    }
    
    @Override
    public List<DocumentLinkedSelectorDTO> getGroundValues(final Long baseId, final IUntypedDAO dao, ILoggedUser loggedUser) {
        final List<DocumentLinkedSelectorDTO> values = dao.HQL_findByQuery(""
                + " SELECT new " + DocumentLinkedSelectorDTO.class.getCanonicalName() + " ("
                    + DocumentLinkedSelectorDTO.HQL_GROUND_DOCUMENT_LINKED_SELECTOR
                + " )"
                + " FROM " + getBaseClazzName() + " a"
                + " , " + Document.class.getCanonicalName() + " c"
                + " , " + User.class.getCanonicalName() + " u"
                + " WHERE a.id.documentId = c.id"
                + " AND u.id = a.lastModifiedBy"
                + " AND a.id." + getGroundIdField() + " = :baseId",
                ImmutableMap.of("baseId", baseId),
                getCacheable(),
                getCacheRegion(),
                0
        );
        return values;
    }

}
