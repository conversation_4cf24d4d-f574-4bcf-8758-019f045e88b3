package qms.document.pending;

import DPMS.Mapping.Document;
import DPMS.Mapping.IUser;
import DPMS.Mapping.Request;
import Framework.Config.Utilities;
import ape.pending.CommonAction;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.BaseAPE;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.ConditionFieldValue;
import ape.pending.dto.IPendingDto;
import ape.pending.util.PendingUtil;
import ape.pending.util.RecordRowsCache;
import ape.pending.util.SqlQueryParser;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import qms.access.dto.ILoggedUser;
import qms.document.DocumentRequestPendingDto;
import qms.document.dto.DocumentPendingDto;
import qms.document.dto.ReaderDocumentPendingDto;
import qms.document.dto.ReceiptDocumentPendingDto;
import qms.document.entity.DocumentReader;
import qms.document.entity.ReceiptAcknowledgmentType;
import qms.document.pending.imp.ToAssignReader;
import qms.document.pending.imp.ToAuthorizeRequest;
import qms.document.pending.imp.ToDeliverPhysicalCopy;
import qms.document.pending.imp.ToPickUpPhysicalCopy;
import qms.document.pending.imp.ToRead;
import qms.document.pending.imp.ToRenew;
import qms.document.pending.imp.ToVerifyRequest;
import qms.document.util.DocumentUtil;
import qms.document.util.RequestType;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
public class DocumentPendingDataSource {
        
    private static void addDocumentPendings(
            Collection<IPendingDto> pendings,
            IPendingRecordDAO dao,
            IUser user
    ) {
        // pendientes del entity de "Document.class"
        final Class<? extends BaseAPE> base = Document.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
            new ToAssignReader(dao),
            new ToRenew(dao)
        };
        final Integer countPendings = dao.getPendingCount(user, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return;
        }
        dao.getPendingRecords(
            user, 
            base,
            null,
            Arrays.asList(BaseSummaryFieldBuilder.columns(
                "recordId = recordId@record",
                "code = documentCode",
                "description = businessUnitDepartmentName@department",
                "description = businessUnitName@businessUnit",
                "description = <EMAIL>",
                "description = <EMAIL>",
                "requestId = requestId",
                "id = documentId",
                "fileId",
                "description = documentDescription",
                "code = pendingTypeCode@ptype",
                "id = pendingRecordId@record",
                "owner = pendingRecordUserId@record",
                "commitmentDate = commitmentDate@record"
            )),
            SqlQueryParser.LEFT_JOIN + "entity.department department" +
            SqlQueryParser.LEFT_JOIN + "entity.businessUnit businessUnit",
            RecordRowsCache.DISABLED,
            operations
        ).stream().forEach((Map pending) -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            DocumentPendingDto value = new DocumentPendingDto();
            // valores de documentos
            value.setDocumentName((String) pending.get("documentDescription"));
            value.setDocumentTypeName((String) pending.get("documentTypeName"));
            value.setDocumentId((Long) pending.get("documentId"));
            value.setRequestId((Long) pending.get("requestId"));
            value.setFileId((Long) pending.get("fileId"));
            value.setDocumentCode((String) pending.get("documentCode"));
            value.setBusinessUnitDepartmentName((String) pending.get("businessUnitDepartmentName"));
            value.setBusinessUnitName((String) pending.get("businessUnitName"));
            value.setCurrentVersionAuthorName((String) pending.get("currentVersionAuthorName"));
            // valores requeridos para pendientes
            value.setDto(DocumentPendingDto.class.getCanonicalName());
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setCode((String) pending.get("documentCode"));
            value.setBase((String) pending.get("base"));
            value.setPendingTypeCode(ape.getCode());
            value.setModule(mx.bnext.access.Module.DOCUMENT);
            value.setDescription((String) pending.get("documentDescription"));
            value.setCommitmentDate((Date) pending.get("commitmentDate"));
            if (ILoggedUser.class.isInstance(user)) {
                value.setAvailableActions(
                    DocumentUtil.getAvailableActions(value, (ILoggedUser) user)
                );
            }
            value.setCommitmentStartDate(value.getCommitmentDate());
            value.setCommitmentEndDate(value.getCommitmentDate());
            // Se agrega acción para visualizar los documentos en los tipos to_renew y to_asign_readers (Como se definió en el req 28286)
            value.getAvailableActions().add(CommonAction.ADD_DOCUMENT.value());
            pendings.add(value);
        });          
    }
    
    private static void addRequestPendings(
            Collection<IPendingDto> pendings,
            IPendingRecordDAO dao,
            IUser user
    ) {
        // pendientes del entity de "Request.class"
        final Class<? extends BaseAPE> base = Request.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
            new ToAuthorizeRequest(dao),
            new ToVerifyRequest(dao)
        };
        final Integer countPendings = dao.getPendingCount(user, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return;
        }
        dao.getPendingRecords(
            user,
            base,
            null,
            Arrays.asList(BaseSummaryFieldBuilder.columns(
                "recordId = recordId@record",
                "id = pendingRecordId@record",
                "owner = pendingRecordUserId@record",
                "commitmentDate = commitmentDate@record",
                "description = businessUnitDepartmentName@department",
                "description = businessUnitName@businessUnit",
                "description = <EMAIL>",
                "description = <EMAIL>",
                "id = requestId",
                "id = <EMAIL>",
                "fileId",
                "requestId = lastVersionRequestId@document",
                "documentCode = requestCode",
                "reazon = reazon",
                "documentCode",
                "type = requestType", // <-- RequestType.getValues()
                "description = requestDescription",
                "code = pendingTypeCode@ptype"
            )),
            SqlQueryParser.LEFT_JOIN + "entity.department department" +
            SqlQueryParser.LEFT_JOIN + "entity.businessUnit businessUnit" +
            SqlQueryParser.LEFT_JOIN + "entity.document document",
            RecordRowsCache.DISABLED,
            operations
        ).stream().forEach((Map pending) -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            DocumentRequestPendingDto value = new DocumentRequestPendingDto();
            // valores de documentos
            value.setDocumentName((String) pending.get("requestDescription"));
            value.setDocumentTypeName((String) pending.get("documentTypeName"));
            value.setReazon((String) pending.get("reazon")); 
            value.setLastVersionRequestId((Long) pending.get("lastVersionRequestId"));
            value.setDocumentId((Long) pending.get("documentId"));
            value.setRequestId((Long) pending.get("requestId"));
            value.setFileId((Long) pending.get("fileId"));
            value.setDocumentCode((String) pending.get("documentCode"));
            value.setBusinessUnitDepartmentName((String) pending.get("businessUnitDepartmentName"));
            value.setBusinessUnitName((String) pending.get("businessUnitName"));
            value.setCurrentVersionAuthorName((String) pending.get("currentVersionAuthorName"));
            // valores de la solicitud
            value.setRequestType(
                RequestType.fromValue((Integer) pending.get("requestType"))
            );
            // valores requeridos para pendientes
            value.setDto(DocumentPendingDto.class.getCanonicalName());
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setCode((String) pending.get("requestCode"));
            value.setPendingTypeCode(ape.getCode());
            value.setModule(mx.bnext.access.Module.DOCUMENT);
            value.setDescription((String) pending.get("requestDescription"));
            value.setCommitmentDate((Date) pending.get("commitmentDate"));
            if (ILoggedUser.class.isInstance(user)) {
                value.setAvailableActions(
                    DocumentUtil.getAvailableActions(value, (ILoggedUser) user)
                );
            }
            value.setCommitmentStartDate(value.getCommitmentDate());
            value.setCommitmentEndDate(value.getCommitmentDate());
            pendings.add(value);
        });        
    }
    
    private static void addPrintPendings(
            Collection<IPendingDto> pendings,
            LinkedHashSet<String> commonActions, 
            IPendingRecordDAO dao,
            IUser user
    ) {   
        // pendientes del entity de "ReceiptAcknowledgmentType.class"
        final Class<? extends BaseAPE> base = ReceiptAcknowledgmentType.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
            new ToDeliverPhysicalCopy(dao),
            new ToPickUpPhysicalCopy(dao)
        };
        final Integer countPendings = dao.getPendingCount(user, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return;
        }
        dao.getPendingRecords(
            user,
            base,
            null,
            Arrays.asList(BaseSummaryFieldBuilder.columns(
                "recordId = recordId@record",
                "id = pendingRecordId@record",
                "owner = pendingRecordUserId@record",
                "documentCode = codeValue",
                "documentCode",
                "userDescription = userDescription@reader",
                "description = positionDescription@position",
                "description = businessUnitDepartmentName@department",
                "description = createdByUserName@createdByUser",
                "description = businessUnitName@businessUnit",
                "description = currentVersionAuthorName@author",
                "description = <EMAIL>",
                "description = documentDescription@document",
                "printDate",
                "positionAssignedDate",
                "documentPrintingId",
                "documentId",
                "fileId = fileId@document",
                "requestId = requestId@document",
                "readerId = readerId@entity",
                "code = pendingTypeCode@ptype",
                "commitmentDate = commitmentDate@record"
            )),
            SqlQueryParser.JOIN + "entity.createdByUser createdByUser" +
            SqlQueryParser.JOIN + "entity.document document" +
            SqlQueryParser.JOIN + "document.author author" +
            SqlQueryParser.JOIN + "document.department department" +
            SqlQueryParser.LEFT_JOIN + "entity.reader reader" +
            SqlQueryParser.JOIN + "entity.position position" +
            SqlQueryParser.JOIN + "document.businessUnit businessUnit",
            RecordRowsCache.DISABLED,
            operations
        ).stream().forEach((Map pending) -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            ReceiptDocumentPendingDto value = new ReceiptDocumentPendingDto();
            // valores del acuse de recibo
            value.setDeliver(ape.equals(APE.DOCUMENT_TO_DELIVER_PHYSICAL_COPY));
            value.setCreatedByUserName((String) pending.get("createdByUserName"));
            value.setReceiptUserName((String) pending.get("userDescription"));
            value.setReceiptPositionName((String) pending.get("positionDescription"));
            if (pending.get("readerId") == null) {
                value.setReceiptType("positionValue");
            } else {
                value.setReceiptType("userValue");
            }
            value.setPrintDate((Date) pending.get("printDate"));
            value.setPositionAssignedDate((Date) pending.get("positionAssignedDate"));
            value.setDocumentPrintingId((Long) pending.get("documentPrintingId"));
            // valores del documento
            value.setDocumentName((String) pending.get("documentDescription"));
            value.setDocumentTypeName((String) pending.get("documentTypeName"));
            value.setDocumentId((Long) pending.get("documentId"));
            value.setFileId((Long) pending.get("fileId"));
            value.setDocumentCode((String) pending.get("documentCode"));
            value.setBusinessUnitDepartmentName((String) pending.get("businessUnitDepartmentName"));
            value.setBusinessUnitName((String) pending.get("businessUnitName"));
            value.setCurrentVersionAuthorName((String) pending.get("currentVersionAuthorName"));
            // valores requeridos para pendientes
            value.setDto(ReceiptDocumentPendingDto.class.getCanonicalName());
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setCode((String) pending.get("codeValue"));
            value.setPendingTypeCode(ape.getCode());
            value.setModule(mx.bnext.access.Module.DOCUMENT);
            switch (value.getReceiptType()) {
                case "userValue":
                    value.setDescription((String) pending.get("userDescription"));
                    break;
                case "positionValue":
                    value.setDescription((String) pending.get("positionDescription"));
                    break;
            }
            value.setCommitmentDate((Date) pending.get("commitmentDate"));
            value.setCommitmentStartDate(value.getCommitmentDate());
            value.setCommitmentEndDate(value.getCommitmentDate());
            value.setAvailableActions(commonActions);
            pendings.add(value);
        });
    }
    
    
    private static void addReaderPendings(
            Collection<IPendingDto> pendings,
            LinkedHashSet<String> commonActions, 
            IPendingRecordDAO dao, 
            IUser user
    ) {
        // pendientes del entity de "DocumentReader.class"
        final Class<? extends BaseAPE> base = DocumentReader.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
            new ToRead(dao)
        };
        final Integer countPendings = dao.getPendingCount(user, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return;
        }
        dao.getPendingRecords(
            user, 
            base,
            null,
            Arrays.asList(BaseSummaryFieldBuilder.columns(
                "recordId = recordId@record",
                "id = pendingRecordId@record",
                "owner = pendingRecordUserId@record",
                "code = documentCode@document",
                "code = pendingTypeCode@ptype",
                "description = businessUnitDepartmentName@department",
                "description = businessUnitName@businessUnit",
                "description = currentVersionAuthorName@author",
                "description = <EMAIL>",
                "description = documentDescription@document",
                "id = documentId@document",
                "fileId = fileId@document",
                "requestId = requestId@document",
                "readed@subEntity_entity",
                "readedOn@subEntity_entity",
                "readerId@subEntity_entity",
                "id = responsibleId@responsible",
                "description = readerUserName@reader",
                "description = responsibleUserName@responsible",
                "commitmentDate = commitmentDate@record"
            )),
            SqlQueryParser.LEFT_JOIN + "subEntity_entity.reader reader" +
            SqlQueryParser.LEFT_JOIN + "subEntity_entity.responsible responsible" +
            SqlQueryParser.LEFT_JOIN + "subEntity_entity.document document" +
            SqlQueryParser.LEFT_JOIN + "document.author author" +
            SqlQueryParser.LEFT_JOIN + "document.department department" +
            SqlQueryParser.LEFT_JOIN + "document.businessUnit businessUnit",
            RecordRowsCache.DISABLED,
            operations
        ).stream().forEach((Map pending) -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            ReaderDocumentPendingDto value = new ReaderDocumentPendingDto();
            // valores de la lectura
            value.setReaderId((Long) pending.get("readerId"));
            value.setResponsibleId((Long) pending.get("responsibleId"));
            value.setReadedOn((Date) pending.get("readedOn"));
            value.setReaded((Integer) pending.get("readed"));
            value.setReaderUserName((String) pending.get("readerUserName"));
            value.setResponsibleUserName((String) pending.get("responsibleUserName"));
            // valores del documento
            value.setDocumentName((String) pending.get("documentDescription"));
            value.setDocumentTypeName((String) pending.get("documentTypeName"));
            value.setDocumentId((Long) pending.get("documentId"));
            value.setFileId((Long) pending.get("fileId"));
            value.setDocumentCode((String) pending.get("documentCode"));
            value.setBusinessUnitDepartmentName((String) pending.get("businessUnitDepartmentName"));
            value.setBusinessUnitName((String) pending.get("businessUnitName"));
            value.setCurrentVersionAuthorName((String) pending.get("currentVersionAuthorName"));
            // valores requeridos para pendientes
            value.setDto(ReaderDocumentPendingDto.class.getCanonicalName());
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setCode((String) pending.get("documentCode"));
            value.setPendingTypeCode(ape.getCode());
            value.setModule(mx.bnext.access.Module.DOCUMENT);
            value.setDescription((String) pending.get("documentDescription"));
            value.setCommitmentDate((Date) pending.get("commitmentDate"));
            value.setCommitmentStartDate(value.getCommitmentDate());
            value.setCommitmentEndDate(value.getCommitmentDate());
            value.setAvailableActions(commonActions);
            pendings.add(value);
        });        
    }
      
    public static Collection<IPendingDto> getPendingRecords(
            final IPendingRecordDAO dao,
            final IUser user
    ) {
        LinkedHashSet<String> commonActions =
            PendingUtil.commonActions(
                CommonAction.ATTEND, CommonAction.HIDE // <--- Agregar funcionalidad general de pendientes (APE): REASIGNAR.
            );
        Collection<IPendingDto> pendings = new ArrayList<>();
        addDocumentPendings(pendings, dao, user);
        addRequestPendings(pendings, dao, user);
        addPrintPendings(pendings, commonActions, dao, user);
        addReaderPendings(pendings, commonActions, dao, user);
        return pendings;
    }
    
     public static List<Map<String, Object>> getDocumentPendingCountAndHours(
        Long[] userIds, 
        IPendingRecordDAO dao, 
        Date start, 
        Date end
    ) {
        final List<ConditionFieldValue> conditions = new ArrayList<>(2);
        if (start != null && end != null) {
            conditions.add(new ConditionFieldValue("record", "trunc", "commitmentDate", "start", ConditionFieldValue.ConditionOperator.GREATER_OR_EQUALS, Utilities.truncDate(start)));
            conditions.add(new ConditionFieldValue("record", "trunc", "commitmentDate", "end", ConditionFieldValue.ConditionOperator.SMALLER_OR_EQUALS, Utilities.truncDate(end)));
        }
        List<Map<String, Object>> results = dao.getActivePendingRecordCount(
                userIds,
                Document.class,
                Arrays.asList(BaseSummaryFieldBuilder.columns(
                    "code = pendingTypeCode@ptype",
                    "trunc(record.commitmentDate) = pendingDate@NONE",
                    "module = moduleKey@ptype",
                    "code",
                    "description"
                )), 
                conditions, /* conditions */
                null, /* joins */
                new ToAssignReader(dao),
                new ToRenew(dao)
        );
        results.addAll(dao.getActivePendingRecordCount(
                userIds,
                Request.class,
                Arrays.asList(BaseSummaryFieldBuilder.columns(
                    "code = pendingTypeCode@ptype",
                    "trunc(record.commitmentDate) = pendingDate@NONE",
                    "module = moduleKey@ptype",
                    "code",
                    "description"
                )), 
                conditions, /* conditions */
                null, /* joins */
                new ToAuthorizeRequest(dao),
                new ToVerifyRequest(dao)
        ));
        results.addAll(dao.getActivePendingRecordCount(
                userIds,
                ReceiptAcknowledgmentType.class,
                Arrays.asList(BaseSummaryFieldBuilder.columns(
                    "code = pendingTypeCode@ptype",
                    "trunc(record.commitmentDate) = pendingDate@NONE",
                    "module = moduleKey@ptype",
                    "code",
                    "description"
                )), 
                conditions, /* conditions */
                null, /* joins */
                new ToDeliverPhysicalCopy(dao),
                new ToPickUpPhysicalCopy(dao)
        ));
        results.addAll(dao.getActivePendingRecordCount(
                userIds,
                DocumentReader.class,
                Arrays.asList(BaseSummaryFieldBuilder.columns(
                    "code = pendingTypeCode@ptype",
                    "trunc(record.commitmentDate) = pendingDate@NONE",
                    "module = moduleKey@ptype",
                    "code",
                    "description"
                )), 
                conditions, /* conditions */
                null, /* joins */
                new ToRead(dao)
        ));
        return results;
    }
}
