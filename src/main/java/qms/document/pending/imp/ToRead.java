package qms.document.pending.imp;

import DPMS.Mapping.Document;
import DPMS.Mapping.Settings;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.dto.ColumnDTO;
import ape.pending.entities.PendingRecord;
import qms.document.entity.DocumentReader;
import qms.document.pending.DocumentPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToRead extends DocumentPendingOperations {
    
  private static final String TO_READ =  ""
        + " FROM " + DocumentReader.class.getCanonicalName() + " dr"
        + " , " + Settings.class.getCanonicalName() + " s"
        + " , " + Document.class.getCanonicalName() + " doc"
        + " JOIN doc.documentType type"
        + " WHERE s.id = 1 AND s.readers = 1"
        + " AND doc.id = dr.documentId"
        + " AND type.mustRead = 1"
        + " AND dr.deleted = 0 "
        + " AND dr.document.status IN (" + Document.STATUS.ACTIVE.getValue() + "," + Document.STATUS.IN_EDITION.getValue() + ")"
        + " AND dr.readed = " + DocumentReader.NOT_READED
        + " AND dr.document.deleted = 0";
  
    private final String READER = " AND dr.readerId = :userId ";
    
    public ToRead(IUntypedDAO dao) {
        super(dao);
        setBaseAlias("dr");
        setQuery(TO_READ);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("dr.readerId");
        setPendingType(getType(APE.DOCUMENT_TO_READ));
        setModuleKey(MODULE);
        setBase(DocumentReader.class);
        setOwnerFieldFilter(READER);
        setSummaryTemplate("${documentCode}, ${documentDescription}");
        setBaseSummaryFields(
            new ColumnDTO("code", "documentCode"),
            new ColumnDTO("description", "documentDescription")
        );
        setOperationType(ApeOperationType.WEAK);
    }

}