/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.document;

import qms.document.dto.DocumentPendingDto;

/**
 *
 * <AUTHOR>
 */
public class DocumentRequestPendingDto extends DocumentPendingDto {
    
    private String reazon;
    private Long lastVersionRequestId;


    public String getReazon() {
        return reazon;
    }

    public void setReazon(String reazon) {
        this.reazon = reazon;
    }

    public Long getLastVersionRequestId() {
        return lastVersionRequestId;
    }

    public void setLastVersionRequestId(Long lastVersionRequestId) {
        this.lastVersionRequestId = lastVersionRequestId;
    }
    
}
