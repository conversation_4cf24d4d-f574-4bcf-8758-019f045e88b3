package qms.audit.pending.imp;

import DPMS.Mapping.Audit;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditIndividualList;
import DPMS.Mapping.AuditType;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import qms.audit.pending.AuditPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToConfirmChangeByManager extends AuditPendingOperations {
    
  public static final String AUDIT_TO_CONFIRM_CHANGE_BY_MANAGER =  ""
        + " FROM " + AuditIndividual.class.getCanonicalName() + " a "
        + " , " + User.class.getCanonicalName() + " u"
        + " LEFT JOIN u.puestos pst "
        + " LEFT JOIN pst.une une "
        + " LEFT JOIN pst.perfil prf "
        + " LEFT JOIN a.departmentProcess dept "
        + " LEFT JOIN a.businessUnitDepartment bu "
        + " JOIN a.audit au "
        + " LEFT JOIN a.area area "
        + " LEFT JOIN area.department area_dept "
        + " WHERE a.status = " + AuditIndividual.STATUS.WAITNG_FOR_DATE_CHANGE_BY_MANAGER
        + " AND a.deleted = " + AuditIndividual.IS_NOT_DELETED
        + " AND prf.intBAuditQuality = 1"
        + " AND au.deleted = " + Audit.IS_NOT_DELETED
        + " AND ("
            + " ("
                + " au.type.scope = " + AuditType.PROCESS_SCOPE
                + " AND une.id = bu.businessUnitId"
            + " )"
            + " OR ("
                + " au.type.scope = " + AuditType.AREA_SCOPE
                + " AND une.id = area_dept.businessUnitId"
            + " )"
        + ")";
  
    public static final String LEADER_FILTER = " AND u.id = :userId ";
    
    public ToConfirmChangeByManager(IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS_AUDIT);
        setQuery(AUDIT_TO_CONFIRM_CHANGE_BY_MANAGER);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("u.id");
        setPendingType(getType(APE.AUDIT_TO_CONFIRM_CHANGE_BY_MANAGER));
        setModuleKey(MODULE);
        setBase(AuditIndividualList.class);
        setOwnerFieldFilter(LEADER_FILTER);
        setDependencies(ToConfirmDate.class);
        setOperationType(ApeOperationType.STRONG);
    }

}