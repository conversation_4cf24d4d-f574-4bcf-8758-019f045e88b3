package qms.audit.pending;

import DPMS.Mapping.Area;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditIndividualPending;
import DPMS.Mapping.DepartmentProcess;
import DPMS.Mapping.Profile;
import java.util.List;
import javax.annotation.Nonnull;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.access.dto.UserCountPendingDTO;
import qms.audit.listeners.OnAcceptedAuditResult;
import qms.audit.listeners.OnAcceptedAuditResultAutomatically;
import qms.audit.listeners.OnCanceledAudit;
import qms.audit.listeners.OnCanceledAudits;
import qms.audit.listeners.OnChangeDeniedByLeader;
import qms.audit.listeners.OnChangeDeniedByManager;
import qms.audit.listeners.OnConfirmedAudit;
import qms.audit.listeners.OnDeletedAllAudits;
import qms.audit.listeners.OnDeletedAudit;
import qms.audit.listeners.OnDeletedAudits;
import qms.audit.listeners.OnFinishedAuditFill;
import qms.audit.listeners.OnPlannedToConfirmAudit;
import qms.audit.listeners.OnRequestedChangeToLeader;
import qms.audit.listeners.OnRequestedChangeToManager;
import qms.audit.listeners.OnStartedAuditFill;
import qms.configuration.listeners.OnChangeAreaManager;
import qms.configuration.listeners.OnChangeDepartmentProcessManager;
import qms.framework.listeners.BnextDaemonMonitor;
import qms.framework.listeners.BnextMonitor;
import qms.framework.listeners.OnJobAdded;
import qms.framework.listeners.OnJobRemoved;
import qms.framework.listeners.OnProfileEdited;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Aspect
public class AuditPendingMonitor extends BnextDaemonMonitor {

    @Around(BnextMonitor.DAILY_AUDIT_PENDING_IS_FIRED)
    public Object onDailyAuditPendingFired(ProceedingJoinPoint pjp, @Nonnull ILoggedUser loggedUser) throws Throwable {
        Object result = null;
        if (pjp != null) {
            result = pjp.proceed();
        }
        int count = 0;
        AuditPending pending = new AuditPending(getUntypedDAO(pjp));
        count += pending.setAuditsToConfirm(loggedUser);
        count += pending.setAuditsStarted();
        count += pending.setAuditsFilled();
        if (count > 0) {
            pending.normalize(loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnPlannedToConfirmAudit
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnPlannedToConfirmAudit) && args(audit, comment, loggedUser)")
    public Object onPlannedToConfirmAudit(ProceedingJoinPoint pjp, AuditIndividual audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividual ent = (AuditIndividual) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.confirmAudit(ent.getId(), OnPlannedToConfirmAudit.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnChangeDeniedByLeader
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnChangeDeniedByLeader) && args(audit, comment, loggedUser)")
    public Object onChangeDeniedByLeader(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending ent = (AuditIndividualPending) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.confirmAudit(ent.getId(), OnChangeDeniedByLeader.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnChangeDeniedByManager
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnChangeDeniedByManager) && args(audit, comment, loggedUser)")
    public Object onChangeDeniedByManager(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending ent = (AuditIndividualPending) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.confirmAudit(ent.getId(), OnChangeDeniedByManager.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnAcceptedAuditResult
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnAcceptedAuditResult) && args(audit, comment, loggedUser)")
    public Object onAcceptedAuditResult(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending result = (AuditIndividualPending) pjp.proceed();
        if (result != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.acceptResult(audit.getId(), OnAcceptedAuditResult.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnAcceptedAuditResultAutomatically
     *
     * @param pjp
     * @param auditIndividualId
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnAcceptedAuditResultAutomatically) && args(auditIndividualId, comment, loggedUser)")
    public Object onAcceptedAuditResultAutomatically(ProceedingJoinPoint pjp, Long auditIndividualId, String comment, LoggedUser loggedUser) throws Throwable {
        final Integer result = (Integer) pjp.proceed();
        if (result > 0) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.acceptResult(auditIndividualId, OnAcceptedAuditResultAutomatically.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnAcceptedAuditResult
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnAcceptedAuditResult) && args(audit, comment, loggedUser)")
    public Object onAcceptedAuditPendingResult(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending ent = (AuditIndividualPending) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.acceptResult(ent.getId(), OnAcceptedAuditResult.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnRequestedChangeToLeader
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnRequestedChangeToLeader) && args(audit, comment, loggedUser)")
    public Object onRequestedChangeToLeader(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment,  LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending ent = (AuditIndividualPending) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.authorizeChangeByLeader(ent.getId(), OnRequestedChangeToLeader.class, loggedUser);
        }
        return ent;
    }
    
    
    /**
     * Catches methods annotated with OnRequestedChangeToManager
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnRequestedChangeToManager) && args(audit, comment, loggedUser)")
    public Object onRequestedChangeToManager(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment,  LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending ent = (AuditIndividualPending) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.authorizeChangeByManager(ent.getId(), OnRequestedChangeToManager.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnCanceledAudits
     *
     * @param pjp
     * @param auditId
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnCanceledAudits) && args(auditId, comment, loggedUser)")
    public Object onCanceledAudits(ProceedingJoinPoint pjp, Long auditId, String comment, LoggedUser loggedUser) throws Throwable {
        final List<Long> auditIndividualIds = (List<Long>) pjp.proceed();
        if (auditIndividualIds != null && auditIndividualIds.size() > 0) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.normalize(auditIndividualIds, loggedUser, OnCanceledAudits.class);
        }
        return auditIndividualIds;
    }
    
    
    /**
     * Catches methods annotated with OnCanceledAudit
     *
     * @param pjp
     * @param auditIndividualId
     * @param auditId
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnCanceledAudit) && args(auditIndividualId, auditId, loggedUser)")
    public Object onCanceledAudit(ProceedingJoinPoint pjp, Long auditIndividualId, Long auditId, LoggedUser loggedUser) throws Throwable {
        final Integer result = (Integer) pjp.proceed();
        if (result > 0) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.normalize(auditIndividualId, loggedUser, OnCanceledAudit.class);
        }
        return result;
    }
    
    
    /**
     * Catches methods annotated with OnDeletedAllAudits
     *
     * @param pjp
     * @param auditId
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnDeletedAllAudits) && args(auditId, comment, loggedUser)")
    public Object onDeletedAllAudits(ProceedingJoinPoint pjp, Long auditId, String comment, LoggedUser loggedUser) throws Throwable {
        final List<Long> auditIndividualIds = (List<Long>) pjp.proceed();
        if (auditIndividualIds != null && auditIndividualIds.size() > 0) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.normalize(auditIndividualIds, loggedUser, OnDeletedAllAudits.class);
        }
        return auditIndividualIds;
    }
    
    /**
     * Catches methods annotated with OnDeletedAudit
     *
     * @param pjp
     * @param auditIndividualId
     * @param auditId
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnDeletedAudit) && args(auditIndividualId, auditId, loggedUser)")
    public Object onDeletedAudit(ProceedingJoinPoint pjp, Long auditIndividualId, Long auditId, LoggedUser loggedUser) throws Throwable {
        final Integer result = (Integer) pjp.proceed();
        if (result > 0) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.normalize(auditIndividualId, loggedUser, OnDeletedAudit.class);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnConfirmedAudit
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnConfirmedAudit) && args(audit, comment, loggedUser)")
    public Object onConfirmedAudit(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending ent = (AuditIndividualPending) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.confirmAudit(ent.getId(), OnConfirmedAudit.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnDeletedAudits
     *
     * @param pjp
     * @param auditIndividualIds
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnDeletedAudits) && args(auditIndividualIds, comment, loggedUser)")
    public Object onDeletedAudits(ProceedingJoinPoint pjp, List<Long> auditIndividualIds, String comment, LoggedUser loggedUser) throws Throwable {
        final Integer result = (Integer) pjp.proceed();
        if (result > 0) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.normalize(auditIndividualIds, loggedUser, OnDeletedAudits.class);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnFinishedAuditFill
     *
     * @param pjp
     * @param auditIndividualId
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnFinishedAuditFill) && args(auditIndividualId, comment, loggedUser)")
    public Object onFinishedAuditFill(ProceedingJoinPoint pjp, Long auditIndividualId, String comment, LoggedUser loggedUser) throws Throwable {
        final Integer result = (Integer) pjp.proceed();
        if (result != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.acceptResult(auditIndividualId, OnFinishedAuditFill.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnStartedAuditFill
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnStartedAuditFill) && args(audit, comment, loggedUser)")
    public Object onStartedAuditPendingFill(ProceedingJoinPoint pjp, AuditIndividualPending audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividualPending ent = (AuditIndividualPending) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.fillByHelper(ent.getId(), OnStartedAuditFill.class, loggedUser);
            pending.fillByLeader(ent.getId(), OnStartedAuditFill.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnStartedAuditFill
     *
     * @param pjp
     * @param audit
     * @param comment
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.audit.listeners.OnStartedAuditFill) && args(audit, comment, loggedUser)")
    public Object onStartedAuditFill(ProceedingJoinPoint pjp, AuditIndividual audit, String comment, LoggedUser loggedUser) throws Throwable {
        final AuditIndividual ent = (AuditIndividual) pjp.proceed();
        if (ent != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.fillByHelper(ent.getId(), OnStartedAuditFill.class, loggedUser);
            pending.fillByLeader(ent.getId(), OnStartedAuditFill.class, loggedUser);
        }
        return ent;
    }
    
    /**
     * Catches methods annotated with OnJobAdded
     *
     * @param pjp
     * @param userId
     * @param jobs
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnJobAdded) && args(userId, countPending, jobs, loggedUser)")
    public Object onJobAdded(
            ProceedingJoinPoint pjp,
            Long userId,
            UserCountPendingDTO countPending,
            List<Long> jobs,
            ILoggedUser loggedUser
    ) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.changeManager(OnJobAdded.class, loggedUser);            
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnJobRemoved
     *
     * @param pjp
     * @param userId
     * @param jobs
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnJobRemoved) && args(userId, countPending, jobs, loggedUser)")
    public Object onJobRemoved(
            ProceedingJoinPoint pjp,
            Long userId, 
            UserCountPendingDTO countPending,
            List<Long> jobs,
            ILoggedUser loggedUser
    ) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.changeManager(OnJobRemoved.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnProfileEdited
     *
     * @param pjp
     * @param ent
     * @param previousEnt
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnProfileEdited) && args(ent, previousEnt, loggedUser)")
    public Object onProfileEdited(ProceedingJoinPoint pjp, Profile ent, Profile previousEnt, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.changeManager(OnProfileEdited.class, loggedUser);
        }
        return result;
    } 
    
    /**
     * Catches methods annotated with OnChangeDepartmentProcessManager
     *
     * @param pjp
     * @param ent
     * @param newMangerId
     * @param oldManagerId
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.configuration.listeners.OnChangeDepartmentProcessManager) && args(ent, newMangerId, oldManagerId, loggedUser)")
    public Object onChangeDepartmentProcessManager(ProceedingJoinPoint pjp, DepartmentProcess ent, Long newMangerId, Long oldManagerId, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.changeDepartmentProcessManager(OnChangeDepartmentProcessManager.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnChangeAreaManager
     *
     * @param pjp
     * @param ent
     * @param newMangerId
     * @param oldManagerId
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.configuration.listeners.OnChangeAreaManager) && args(ent, newMangerId, oldManagerId, loggedUser)")
    public Object onChangeAreaManager(ProceedingJoinPoint pjp, Area ent, Long newMangerId, Long oldManagerId, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            AuditPending pending = new AuditPending(getUntypedDAO(pjp));
            pending.changeAreaManager(OnChangeAreaManager.class, loggedUser);
        }
        return result;
    }
        
}
