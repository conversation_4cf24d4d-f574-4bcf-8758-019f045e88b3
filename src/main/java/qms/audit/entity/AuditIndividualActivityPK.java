package qms.audit.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class AuditIndividualActivityPK implements Serializable {

    private Long auditIndividualId;
    private Long fieldId;
    private Long activityId;
    private Integer commitmentTask;

    public AuditIndividualActivityPK(Long auditIndividualId, Long fieldId, Long activityId, Integer commitmentTask) {
        this.auditIndividualId = auditIndividualId;
        this.fieldId = fieldId;
        this.activityId = activityId;
        this.commitmentTask = commitmentTask;
    }

    public AuditIndividualActivityPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "audit_individual_id")
    public Long getAuditIndividualId() {
        return auditIndividualId;
    }

    public void setAuditIndividualId(Long auditIndividualId) {
        this.auditIndividualId = auditIndividualId;
    }

    @Basic(optional = false)
    @Column(name = "field_id")
    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Column(name = "commitment_task")
    public Integer getCommitmentTask() {
        return commitmentTask;
    }

    public void setCommitmentTask(Integer commitmentTask) {
        this.commitmentTask = commitmentTask;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 61 * hash + Objects.hashCode(this.auditIndividualId);
        hash = 61 * hash + Objects.hashCode(this.fieldId);
        hash = 61 * hash + Objects.hashCode(this.activityId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AuditIndividualActivityPK other = (AuditIndividualActivityPK) obj;
        if (!Objects.equals(this.auditIndividualId, other.auditIndividualId)) {
            return false;
        }
        if (!Objects.equals(this.fieldId, other.fieldId)) {
            return false;
        }
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "AuditIndividualActivityPK{" + "auditIndividualId=" + auditIndividualId + ", fieldId=" + fieldId + ", activityId=" + activityId + '}';
    }

}
