package qms.access.util;

import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.IAddressableUser;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import qms.access.dto.AddressableUserDTO;
import qms.configuration.core.UserPositionService;
import qms.framework.entity.Owner;
import qms.framework.entity.OwnerTeam;
import qms.framework.entity.ShareReportBusinessUnit;
import qms.framework.entity.ShareReportDepartment;
import qms.framework.entity.ShareReportProcess;
import qms.framework.entity.ShareReportUser;
import qms.framework.entity.ShareReportUserTeam;
import qms.util.Helper;

public class AddressableUserHelper extends Helper {

    private static final String BUSINESS_UNIT_PERMISSION = ""
            + " SELECT 1 "
            + " FROM " + User.class.getCanonicalName() + " bup "
            + " LEFT JOIN bup.puestos p "
            + " JOIN " + ShareReportBusinessUnit.class.getCanonicalName() + " nb "
                + " ON ("
                    + " nb.id.businessUnitId = p.businessUnitId "
                    + " OR nb.id.businessUnitId = bup.businessUnitId "
                + " )"
            + " WHERE"
                + " bup.id = u.id"
                + " AND nb.id.shareReportId = :shareReportId";

    private static final String USER_TEAM_PERMISSION = ""
            + " SELECT 1"
            + " FROM " + OwnerTeam.class.getCanonicalName() + " entity "
            + " JOIN " + ShareReportUserTeam.class.getCanonicalName() + " nb "
                + " ON entity.id = nb.id.userTeamId"
            + " JOIN entity.users ownerUser "
            + " JOIN ownerUser.user ou "
            + " JOIN entity.groups g "
            + " WHERE "
                + " entity.deleted = 0"
                + " AND entity.type = " + Owner.TYPE.TEAM_BY_GROUP.getValue()
                + " AND g.id = ownerUser.groupId "
                + " AND nb.id.shareReportId = :shareReportId"
                + " AND ou.id = u.id";

    private static final String PROCESS_PERMISSION = ""
            + " SELECT 1 "
            + " FROM " + User.class.getCanonicalName() + " up "
            + " JOIN up.puestos p "
            + " JOIN p.departmentProcess dp "
            + " JOIN " + ShareReportProcess.class.getCanonicalName() + " nb "
            + " ON dp.processId = nb.id.processId"
            + " WHERE"
                + " up.id = u.id"
                + " AND nb.id.shareReportId = :shareReportId";

    private static final String DEPARTMENT_PERMISSION = ""
            + " SELECT ud.id "
            + " FROM " + User.class.getCanonicalName() + " ud "
            + " LEFT JOIN ud.puestos p "
            + " LEFT JOIN p.departamentos d "
            + " JOIN " + ShareReportDepartment.class.getCanonicalName() + " nb "
                + " ON ("
                    + " d.id = nb.id.departmentId "
                    + " OR ud.businessUnitDepartmentId = nb.id.departmentId "
                + " ) "
            + " WHERE"
                + " ud.id = u.id"
                + " AND nb.id.shareReportId = :shareReportId";
    
    private static final String USER_PERMISSION = ""
            + " SELECT 1"
            + " FROM " + ShareReportUser.class.getCanonicalName() + " nb "
            + " WHERE"
                + " nb.id.userId = u.id"
                + " AND nb.id.shareReportId = :shareReportId";
    
    public static final String ADDRESSABLE_USER_BY_ID = ""
            + " SELECT new " + AddressableUserDTO.class.getCanonicalName() + " ("
                + " u.id AS id"
                + ", u.code AS code"
                + ", u.login AS login"
                + ", u.description AS description"
                + ", u.correo AS correo"
                + ", u.version AS version"
            + " )"
            + " FROM " + User.class.getCanonicalName() + " u"
            + " WHERE u.id = :id";

    public static final String ADDRESSABLE_USER_BY_CODE = ""
            + " SELECT new " + AddressableUserDTO.class.getCanonicalName() + " ("
                + " u.id AS id"
                + ", u.code AS code"
                + ", u.login AS login"
                + ", u.description AS description"
                + ", u.correo AS correo"
                + ", u.version AS version"
            + " )"
            + " FROM " + User.class.getCanonicalName() + " u"
            + " WHERE u.code = :code";
    
    public static final String INTENAL_RECIPIENTS_SELECT = ""
            + " SELECT new " + AddressableUserDTO.class.getCanonicalName() + " ("
                + " u.id AS id"
                + ", u.code AS code"
                + ", u.login AS login"
                + ", u.description AS description"
                + ", u.correo AS correo"
                + ", u.version AS version"
                + " )"
            + " FROM " + User.class.getCanonicalName() + " u"
            + " WHERE u.deleted = 0"
                + " AND u.status IN ("
                + User.STATUS.ACTIVE.getValue() + ","
                + User.STATUS.LOCKED.getValue()
                + " ) "
                + " AND ("
                    /*Es destinatario por grupo */
                    + " EXISTS (" + USER_TEAM_PERMISSION + " ) "
                    /*Es destinatario por proceso*/
                    + " OR EXISTS (" + PROCESS_PERMISSION + " ) "
                    /*Es destinatario por planta*/
                    + " OR EXISTS (" + BUSINESS_UNIT_PERMISSION + " ) "
                    /*Es destinatario por usuario*/
                    + " OR EXISTS (" + USER_PERMISSION + " )"
                    /*Es destinatario por departamento*/
                    + " OR EXISTS (" + DEPARTMENT_PERMISSION + " )"
                + " )";
    public static final String USERS_TO_ACTIVATE = ""
                + " SELECT new " + AddressableUserDTO.class.getCanonicalName() + " ("
                    + " c.id AS id"
                    + ", c.code AS code"
                    + ", c.login AS login"
                    + ", c.description AS description"
                    + ", c.correo AS correo"
                    + ", c.version AS version"
                    + " )"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE " + UserPositionService.USER_TO_ACTIVATE_FILTER;

    public static final String ESCALATION_RESPONSIBLE_ADDRESSABLE_USER = ""
            + " SELECT new " + AddressableUserDTO.class.getCanonicalName() + " ("
                + " u.id AS id"
                + ", u.code AS code"
                + ", u.login AS login"
                + ", u.description AS description"
                + ", u.correo AS correo"
                + ", u.version AS version"
            + " )"
            + " FROM " + User.class.getCanonicalName() + " u"
            + " JOIN u.puestos p"
            + " JOIN p.perfil pe"
            + " WHERE pe.escalationManager = 1"
            + " AND u.status = " + User.STATUS.ACTIVE.getValue();

    public static final String COMPLAINT_RESPONSIBLE_ADDRESSABLE_USER = ""
            + " SELECT new " + AddressableUserDTO.class.getCanonicalName() + " ("
                + " u.id AS id"
                + ", u.code AS code"
                + ", u.login AS login"
                + ", u.description AS description"
                + ", u.correo AS correo"
                + ", u.version AS version"
            + " )"
            + " FROM " + User.class.getCanonicalName() + " u "
            + " JOIN " + BusinessUnitDepartment.class.getCanonicalName() + " bd"
            + " ON u.id = bd.attendantId"
            + " WHERE  bd.id = :businessUnitDepartmentId";

    public static final String COMPLAINT_MANAGER_ADDRESSABLE_USER = ""
            + " SELECT new " + AddressableUserDTO.class.getCanonicalName() + " ("
                + " u.id AS id"
                + ", u.code AS code"
                + ", u.login AS login"
                + ", u.description AS description"
                + ", u.correo AS correo"
                + ", u.version AS version"
            + " )"
            + " FROM " + User.class.getCanonicalName() + " u"
            + " JOIN u.puestos pu"
            + " JOIN pu.perfil pe"
            + " JOIN " + BusinessUnitDepartment.class.getCanonicalName() + " bd "
            + " ON bd.businessUnitId = pu.businessUnitId"
            + " WHERE pe.intBEncargadoQueja = 1"
            + " AND bd.id = :businessUnitDepartmentId"
            + " GROUP BY"
                + " u.id"
                + ", u.code"
                + ", u.login"
                + ", u.description"
                + ", u.correo"
                + ", u.version";

    public AddressableUserHelper(IUntypedDAO dao) {
        super(dao);
    }
    
    public IAddressableUser findUser(final Long userId) {
        return dao.HQLT_findSimple(AddressableUserDTO.class,
                ADDRESSABLE_USER_BY_ID,
                ImmutableMap.of("id", userId),
                false,
                null,
                0
        );
    }
    
    public IAddressableUser findUserByCode(final String userCode) {
        return dao.HQLT_findSimple(AddressableUserDTO.class,
                ADDRESSABLE_USER_BY_CODE,
                ImmutableMap.of("code", userCode),
                false,
                null,
                0
        );
    }

    
    public Set<IAddressableUser> getComplaintManagers(final Long businessUnitDepartmentId) {
        final List<AddressableUserDTO> managers = dao.HQL_findByQuery(
                COMPLAINT_MANAGER_ADDRESSABLE_USER,
                ImmutableMap.of("businessUnitDepartmentId", businessUnitDepartmentId)
        );
        return new HashSet<>(managers);
    }

    public Set<IAddressableUser> getCompaintToAssignResponsibles(final Long businessUnitDepartmentId) {
        //El responsable de asignar un responsable de la queja es el encargado del departamento
        final List<AddressableUserDTO> responsibles = dao.HQL_findByQuery(
                COMPLAINT_RESPONSIBLE_ADDRESSABLE_USER,
                ImmutableMap.of("businessUnitDepartmentId", businessUnitDepartmentId)
        );
        return new HashSet<>(responsibles);
    }
    
    public Set<AddressableUserDTO> getEscalationMailManagers() {
        final Set<AddressableUserDTO> users = new HashSet<>(
                dao.HQL_findByQuery(
                    ESCALATION_RESPONSIBLE_ADDRESSABLE_USER,
                    Utilities.EMPTY_MAP
                )
        );
        return new HashSet<>(users);
    }
    
    public Set<IAddressableUser> getInternalRecipients(final Long shareReportId) {
        final List<AddressableUserDTO> users = dao.HQL_findByQuery(
                INTENAL_RECIPIENTS_SELECT,
                ImmutableMap.of("shareReportId", shareReportId),
                false, null, 0
        );
        return new HashSet<>(users);
    }

    public List<AddressableUserDTO> getUsersToActivate() {
        final List<AddressableUserDTO> users = dao.HQL_findByQuery(USERS_TO_ACTIVATE, Utilities.EMPTY_MAP);
        return users;
    }

}
