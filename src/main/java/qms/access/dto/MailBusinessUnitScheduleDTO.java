package qms.access.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import qms.access.util.IMailSettingsSchedule;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MailBusinessUnitScheduleDTO implements Serializable, Comparable<MailBusinessUnitScheduleDTO>, IMailSettingsSchedule {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String module;
    private Long businessUnitId;
    private Integer monday;
    private Integer tuesday;
    private Integer wednesday;
    private Integer thursday;
    private Integer friday;
    private Integer saturday;
    private Integer sunday;

    public MailBusinessUnitScheduleDTO() {
    }

    public MailBusinessUnitScheduleDTO(
            Long id, 
            String module,
            Long businessUnitId,
            Integer monday, 
            Integer tuesday,
            Integer wednesday, 
            Integer thursday, 
            Integer friday, 
            Integer saturday,
            Integer sunday
    ) {
        this.id = id;
        this.module = module;
        this.businessUnitId = businessUnitId;
        this.monday = monday;
        this.tuesday = tuesday;
        this.wednesday = wednesday;
        this.thursday = thursday;
        this.friday = friday;
        this.saturday = saturday;
        this.sunday = sunday;
    }
    
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getModule() {
        return module;
    }

    @Override
    public void setModule(String module) {
        this.module = module;
    }

    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }
    
    @Override
    public Integer getMonday() {
        return monday;
    }

    @Override
    public void setMonday(Integer monday) {
        this.monday = monday;
    }

    @Override
    public Integer getTuesday() {
        return tuesday;
    }

    @Override
    public void setTuesday(Integer tuesday) {
        this.tuesday = tuesday;
    }

    @Override
    public Integer getWednesday() {
        return wednesday;
    }

    @Override
    public void setWednesday(Integer wednesday) {
        this.wednesday = wednesday;
    }

    @Override
    public Integer getThursday() {
        return thursday;
    }

    @Override
    public void setThursday(Integer thursday) {
        this.thursday = thursday;
    }

    @Override
    public Integer getFriday() {
        return friday;
    }

    @Override
    public void setFriday(Integer friday) {
        this.friday = friday;
    }

    @Override
    public Integer getSaturday() {
        return saturday;
    }

    @Override
    public void setSaturday(Integer saturday) {
        this.saturday = saturday;
    }

    @Override
    public Integer getSunday() {
        return sunday;
    }

    @Override
    public void setSunday(Integer sunday) {
        this.sunday = sunday;
    }

    @Override
    public int compareTo(MailBusinessUnitScheduleDTO other) {
        return StringUtils.compare(this.module, other.getModule(), true);
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.module);
        hash = 59 * hash + Objects.hashCode(this.businessUnitId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MailBusinessUnitScheduleDTO other = (MailBusinessUnitScheduleDTO) obj;
        if (!Objects.equals(this.module, other.module)) {
            return false;
        }
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return Objects.equals(this.businessUnitId, other.businessUnitId);
    }
    
    @Override
    public String toString() {
        return "MailBusinessUnitScheduleDTO{" + "module=" + module + ", businessUnitId=" + businessUnitId + '}';
    }


}
