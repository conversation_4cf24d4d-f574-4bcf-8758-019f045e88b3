package qms.access.entity;

import Framework.Config.DomainObject;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.access.util.IMailSettingsSchedule;
import qms.custom.core.DynamicSearchColumn;
import qms.framework.util.CacheConstants;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.MAIL_SETTINGS, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "mail_bu_schedule")
public class MailBusinessUnitSchedule extends DomainObject implements IAuditable, IMailSettingsSchedule {

    private static final long serialVersionUID = 1L;

    private Integer deleted = 0;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private String module;
    private Long businessUnitId;
    private Integer monday;
    private Integer tuesday;
    private Integer wednesday;
    private Integer thursday;
    private Integer friday;
    private Integer saturday;
    private Integer sunday;

    public MailBusinessUnitSchedule() {
        this.deleted = 0;
    }

    public MailBusinessUnitSchedule(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "mail_bu_schedule_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @DynamicSearchColumn
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            this.deleted = 0;
        } else {
            this.deleted = borrado;
        }
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Column(name = "module")
    @Override
    public String getModule() {
        return module;
    }

    @Override
    public void setModule(String module) {
        this.module = module;
    }

    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "monday")
    @Override
    public Integer getMonday() {
        return monday;
    }

    @Override
    public void setMonday(Integer monday) {
        this.monday = monday;
    }

    @Column(name = "tuesday")
    @Override
    public Integer getTuesday() {
        return tuesday;
    }

    @Override
    public void setTuesday(Integer tuesday) {
        this.tuesday = tuesday;
    }

    @Column(name = "wednesday")
    @Override
    public Integer getWednesday() {
        return wednesday;
    }

    @Override
    public void setWednesday(Integer wednesday) {
        this.wednesday = wednesday;
    }

    @Column(name = "thursday")
    @Override
    public Integer getThursday() {
        return thursday;
    }

    @Override
    public void setThursday(Integer thursday) {
        this.thursday = thursday;
    }

    @Column(name = "friday")
    @Override
    public Integer getFriday() {
        return friday;
    }

    @Override
    public void setFriday(Integer friday) {
        this.friday = friday;
    }

    @Column(name = "saturday")
    @Override
    public Integer getSaturday() {
        return saturday;
    }

    @Override
    public void setSaturday(Integer saturday) {
        this.saturday = saturday;
    }

    @Column(name = "sunday")
    @Override
    public Integer getSunday() {
        return sunday;
    }

    @Override
    public void setSunday(Integer sunday) {
        this.sunday = sunday;
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 59 * hash + Objects.hashCode(this.module);
        hash = 59 * hash + Objects.hashCode(this.businessUnitId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MailBusinessUnitSchedule other = (MailBusinessUnitSchedule) obj;
        if (!Objects.equals(this.module, other.module)) {
            return false;
        }
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return Objects.equals(this.businessUnitId, other.businessUnitId);
    }
    
    @Override
    public String toString() {
        return "MailBusinessUnitSchedule{" + "module=" + module + ", businessUnitId=" + businessUnitId + '}';
    }


}
