package qms.activity.entity;

import DPMS.Mapping.IAuditableEntity;
import DPMS.Mapping.IOwnerEntity;
import DPMS.Mapping.Periodicity;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import ape.pending.core.RootSoftAPE;
import ape.pending.core.RootSoftAPEs;
import ape.pending.core.StrongBaseSourceAPE;
import ape.pending.core.StrongBaseTypedAPE;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.BusinessUnitRef;
import jakarta.persistence.Convert;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.hibernate.OutstandingSurveysRef;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Generated;

import org.hibernate.annotations.SQLRestriction;

import org.hibernate.generator.EventType;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.activity.core.ActivityCodeValidator;
import qms.activity.util.CommitmentTask;
import qms.activity.util.IActivity;
import qms.activity.util.IActivityEvent;
import qms.activity.util.IActivityType;
import qms.audit.entity.AuditIndividualActivity;
import qms.custom.core.DynamicSearch;
import qms.custom.core.PersistableDynamicEntity;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.finding.entity.FindingActivity;
import qms.form.entity.OutstandingSurveysActivity;
import qms.framework.entity.Owner;
import qms.framework.entity.SingleUserOwner;
import qms.framework.periodicity.util.ICommitmentTaskName;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.framework.util.CacheConstants;
import qms.meeting.entity.MeetingActivity;
import qms.planner.entity.Client;
import qms.planner.entity.Planner;
import qms.planner.entity.PlannerActivity;
import qms.util.CodePrefix;
import qms.util.TimezoneActivityColumns;
import qms.util.annotations.UserDefinedCode;
import qms.util.interfaces.IBusinessUnitDepartmentId;
import qms.util.interfaces.IBusinessUnitDepartmentLocation;
import qms.util.interfaces.IBusinessUnitId;
import qms.util.interfaces.IBusinessUnitLocation;
import qms.util.interfaces.IPersistableStatus;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "activity")
@CodePrefix("ACT-")
@EnableJpaAuditing
@EntityListeners(value = {AuditingEntityListener.class, ActivityListeners.class})
@RootSoftAPEs({
    @RootSoftAPE( 
        mappedBy = "id.activityId",
        mappedByClass = FindingActivity.class
    ),
    @RootSoftAPE( 
        mappedBy = "id.activityId",
        mappedByClass = PlannerActivity.class
    ),
    @RootSoftAPE( 
        mappedBy = "id.activityId",
        mappedByClass = OutstandingSurveysActivity.class
    ),
    @RootSoftAPE(
        mappedBy = "id.activityId",
        mappedByClass = MeetingActivity.class
    ),
    @RootSoftAPE( 
        mappedBy = "id.activityId",
        mappedByClass = AuditIndividualActivity.class
    )
})
@DynamicSearch
@UserDefinedCode(
    mappedBy = "type",
    mappableAttributes = {
        "objective", "source", "priority", "category", "businessUnit", "businessUnitDepartment", "type"
    },
    codeValidator = ActivityCodeValidator.class
)
@TimezoneActivityColumns
public class Activity extends StandardEntity<Activity> implements 
        IActivity, PersistableDynamicEntity<ActivityType>, IPeriodicEntity, ICommitmentTaskName, IPersistableStatus,
        IBusinessUnitDepartmentId, IBusinessUnitId, IBusinessUnitLocation, IBusinessUnitDepartmentLocation,
        StrongBaseTypedAPE<ActivityType>, StrongBaseSourceAPE<ActivitySource>,
        IAuditableEntity, IOwnerEntity, IActivityEvent<ActivitySystemLink> {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    @Transient
    public String getCommitmentTaskName() {
        if (commitmentTask == null) {
            return Utilities.EMPTY_STRING;
        }
        switch (CommitmentTask.fromValue(this)) {
            case IMPLEMENTATION:
                return "IMP";
            case VERIFICATION:
                return "VER";
            default:
                return Utilities.EMPTY_STRING;
        }
    }

    @Override
    @Transient
    public String getPlannedVerificationCode() {
        return plannedVerificationCode;
    }

    @Override
    public void setPlannedVerificationCode(String plannedVerificationCode) {
        this.plannedVerificationCode = plannedVerificationCode;
    }

    
    public enum STATUS implements IStatusEnum {
        // Al agregar un nuevo estado recordar afectar el método "getOperation" de la clase ActivityHelper pues genera el "operation" necesario para el "attender"
        REPORTED(1, IStatusEnum.COLOR_RED),             // <--- En proceso / En tiempo
        RETURNED(2, IStatusEnum.COLOR_ORANGE),          // <--- En proceso / Validado para proceder
        IN_PROCESS(3, IStatusEnum.COLOR_YELLOW),        // <--- En proceso / Con avances
        IMPLEMENTED(4, IStatusEnum.COLOR_GREEN),        // <--- Realizada
        VERIFIED(5, IStatusEnum.COLOR_CYAN),            // <--- Realizada / Verificada
        NOT_APPLY(6, IStatusEnum.COLOR_GRAY),           // <--- Cancelada / Por verificarse               - No aplica (por verificar)
        NOT_APPLY_VERIFIED(7, IStatusEnum.COLOR_BEIGE), // <--- Cancelada / Verificada                    - No aplica (verificada)
        UNDONE(8, IStatusEnum.COLOR_DEEP_BLUE),         // <--- No realizada / Por verificarse            - No se realizará
        MAIN_OPEN(9, IStatusEnum.COLOR_LIGHT_GREEN),    // <--- Activa, se cerrarán hasta que finalice la recurrencia
        UNDONE_VERIFIED(10, IStatusEnum.COLOR_GRAY),    // <--- No realizada / Verificada                 - Estatus para identificar que verificador esta enterado que no se realizo la actividad.
        MAIN_CLOSED(11, IStatusEnum.COLOR_GRAY);          // <--- Inactiva, ya no se generarán eventos                - Estatus para identificar que se cancela la serie

        private final Integer value;
        private final String gridCube; 
        STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public IStatusEnum getActiveStatus() {
            return REPORTED;
        }
        public boolean equals(Integer value) {
            return this.value.equals(value);
        }
        public boolean equals(IPersistableStatus activity) {
            return this.value.equals(activity.getStatus());
        }
        public static STATUS getStatus(Integer status) {
            for(STATUS s : values()) {
                if (Objects.equals(s.value, status)) {
                    return s;
                }
            }
            return null;
        }
    }
    
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer rescheduled = 0;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long parentPlannerId;
    private Long plannerId;
    private Long clientId;
    private Long createdBy;
    private UserRef createdByUser;
    private Client client;
    private Long activityResolutionId;
    private ActivityResolution activityResolution;
    private Long lastModifiedBy;
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Long parentPartitionActivityId;
    private Long parentTreeActivityId;
    private Long parentActivityId;                  // <-- Guarda referencia a "recurrenceId"
    private Long parentActivityTaskId;              // <-- Es la que se selecciona al elegir cliente/proyecto/tarea (es la tarea, un activityId)
    private Long parentActivityImplementationId;
    private Long parentActivityVerificationId;
    private BusinessUnitRef businessUnit;
    private BusinessUnitDepartmentRef businessUnitDepartment;
    private Set<UserRef> participants;
    private Set<Activity> childs;
    private Set<Activity> implementationSubTasks;
    private Set<Activity> implementationSubTasksOnly;
    private Set<Activity> verificationSubTasks;
    private Set<Activity> plannerSubTasks;
    private Planner planner;
    private Planner parentPlanner;
    private Activity singleImplementation;
    private Activity parentActivity;
    private Activity parentPartitionActivity;
    private Activity parentTreeActivity;
    private Activity parentActivityTask;            // <-- Es la que se selecciona al elegir cliente/proyecto/tarea (es la tarea, un activityId)
    private Activity parentActivityImplementation;
    private Activity parentActivityVerification;
    private ActivityType type;
    private ActivityObjective objective;
    private ActivitySource source;
    private Long sourceId;
    private ActivityPriority priority;
    private ActivityCategory category;
    private SingleUserOwner singleVerifier;
    private Owner implementer;
    private Owner verifier;
    private Owner preImplementer;
    private Long countComments;
    private Long countFiles;
    private Long countDocument;
    private String createdDateTimezone;
    private String lastModifiedDateTimezone;
    private String plannedImplementationTimezone;
    private String plannedVerificationTimezone;
    private String startImplementationOnTimezone;
    private String startVerificationOnTimezone;
    private String verificationOnTimezone;
    private String implementationOnTimezone;
    private String finishVerificationOnTimezone;
    private String finishImplementationOnTimezone;

    /**
     * Semana compromiso -> commitment_week
     *      2.1) Cuando commimentTask === 1 (IMPLEMENTATION) será igual a `planned_implementation_week`
     *      2.2) Cuando commimentTask === 2 (VERIFICATION) será igual a planned_verification_week
     *      2.3) Cuando commimentTask === 3 (PROGRAM) será NULL
     */
    private Integer createdWeek;                    // <-- Semana creación
    private Integer commitmentWeek;                 // <-- Semana creación
    private Integer plannedImplementationWeek;      // <-- Semana implementación
    private Integer plannedVerificationWeek;        // <-- Semana verificación
    private Integer createdYear;                    // <-- Semana creación
    private Integer commitmentYear;                 // <-- Semana creación
    private Integer plannedImplementationYear;      // <-- Semana implementación
    private Integer plannedVerificationYear;        // <-- Semana verificación
    private Integer programPlannedWeek;
    private String plannedWeek;                     // <-- Semana(s) de las implementacion(es)/verificación
    private String verificationPlannedWeek;         // <-- Semana(s) de las implementacion(es) de una verificación
    private String programImplementerNames;         // Nombres de los Usuario(s) Activo(s) responsables y/o verificadores
    private String programImplementerIds;           // Id´s de los Usuario(s) Activo(s) responsables y/o verificadores
    
    
    private Integer verificationCount;              // <-- solo contiene información en registros de RECURRENCIA
    private Integer implementationCount;            // <-- solo contiene información en registros de VERIFICACIÓN y RECURRENCIA
    private Integer activeImplementationCount;      // <-- solo contiene información en registros de VERIFICACIÓN
    private Long singleImplementationId;            // <-- solo contiene información en registros de VERIFICACIÓN con 1 sola implementación activa
    private Set<Activity> events;                   // <-- contiene registros de IMPLEMENTACIÓN y VERIFICACIÓN
    private Set<Activity> partitions;               // <-- contiene registros de PROGRAM, IMPLEMENTACIÓN y VERIFICACIÓN
    private Set<Activity> plannedImplementations;   // <-- solo contiene información en registros de VERIFICACIÓN
    private Long plannedVerificationId;             // <-- solo contiene información en registros de IMPLEMENTACIÓN
    private Activity plannedVerification;

    private Date commitmentDate;
    private Integer commitmentTask;
    private Date implementationOn;
    private Date verificationOn;
    private UserRef implementedBy;
    private UserRef verifiedBy;
    private Double progress;
    private Integer apply;
    private Integer delayedByImplementer;
    private Integer delayedByVerifier;
    private Integer childCount;                     // <-- solo contiene información en registros de PADRE
    private Long formId; // <-- Es el ID del entity "Document"
    private DocumentRef form;
    private Long typeId;
    private Long outstandingSurveyId;
    private OutstandingSurveysRef outstandingSurvey;
    private Integer formFillIsRequested = 0;
    private Integer formFillIsClosed = 0;
    private String parentCode;
    
    private Date deadline;
    private Date notice;
    private Date reminder;
    private Integer daysToVerify;

    // corresponde al tipo de llenado en ActivityType.FILL_TYPE
    private Integer fillType;

    // configuración de periodicida para la verificación
    private Periodicity verificationPeriodicity;
    private Long verificationPeriodicityId;
    private Date nextVerificationDate;
    private Date finishVerificationOn;
    private Date startVerificationOn;

    // configuración de periodicida para la implementación
    private Periodicity implementationPeriodicity;
    private Long implementationPeriodicityId;
    private Date nextImplementationDate;
    private Date finishImplementationOn;
    private Date startImplementationOn;

    // configuración de periodicida para identificar el registro padre
    private Integer recurrent = IPeriodicEntity.RECURRENT.NO.getValue();
    private Long recurrenceId;
    // configuración de periodicidad del registro padre
    private Integer recurrenceStatus;
    private Activity recurrence;

    // configuración de campos dinamicos
    private Map<String, Object> dynamicFieldData;
    private Long dynamicTableNameId;
    private String dynamicTableName;
    
    // transient
    private String dynamicTableNameLatest;              // <-- Se llena a la par con `dynamicTableName`, pero contiene la última versión publicada de la tabla
    private String plannedVerificationCode;             // <-- solo contiene información en registros de IMPLEMENTACIÓN

    private Long objectiveId;
    private Long priorityId;
    private Long categoryId;
    // Implementer - verifiers owners
    private Long implementerOwnerId;
    private Long preImplementerOwnerId;
    private Long verifierOwnerId;
    private Long implementedByUserId;
    private Long verifiedByUserId;
    
    private Date plannedImplementationDate;
    private Date plannedVerificationDate;
    private Boolean pastPlannedImplementation;
    private Boolean pastPlannedVerification;
    
    // información redundante respecto al tipo de actividad
    private Integer showImplementerUser;
    private Integer showVerifierUser;
    private Integer showSource;
    private Integer showObjective;
    private Integer showPriority;
    private Integer belongSeries;
    private Integer restrictAnticipationAttend;
    private Integer anticipationAttendDays;
    private String groupId;
    private String groupName;

    // información redundate respecto a la entidad-relación de la actividad
    private String implementerNames;
    private String implementerIds;
    private String preImplementerNames;
    private String preImplementerIds;
    
    // última acción realizada en la actividad
    private Long lastActionUserId;
    private UserRef lastActionUser;
    private String lastActionDescription;
    private Date lastActionTimestamp;
   
    private Boolean isPlanned = true;
    private Double plannedHours;
    private Double actualHours = 0.0;
    private Boolean enableDeliverySetUp = false;
    private Long taskDeliveryTypeId;
    private Long taskCategoryId;
    private String cancellationReason;
    
    // Configuración de Planeación y creación de actividades
    private Boolean addImplementationOnCreate;
    private Boolean addVerificationOnCreate;
    private Boolean verificationReqOnPlanning;
    private Boolean addVerificationAvailable;
    private Boolean allowsUseOnPlanning;
    
    // Configuración de pre-asignación
    private String activityOrder;
    
    private Set<ActivitySystemLink> systemLinks;
    /**
     * @see IActivityType followUpImplementationDelay
     */
    private Boolean followUpImplementationDelay = true;
    private Boolean mustUpdateImplementationAtReturn = true;
    private Boolean tzAutoMigrated = false;
            
    public Activity() {
    }

    public Activity(Long id) {
        this.id = id;
    }

    public Activity(Long id, String code) {
        this.id = id;
        this.code = code;
    }
      
    public Activity(
        Long id,
        String code,
        String description,
        Integer status,
        Integer deleted,
        Date createdDate,
        Date lastModifiedDate,
        Long createdBy,
        Long lastModifiedBy,
        Long objectiveId,
        Long sourceId,
        Long priorityId,
        Long implementerOwnerId,
        Long verifierOwnerId,
        Long preImplementerOwnerId,
        Date commitmentDate,
        Date implementationOn,
        Date verificationOn,
        Long implementedByUserId,
        Long verifiedByUserId,
        Double progress,
        Long formId,
        Long typeId,
        ActivityType type,
        Long outstandingSurveyId,
        Integer formFillIsRequested,
        Integer formFillIsClosed,
        String parentCode,
        Long recurrenceId,
        Integer recurrenceStatus,
        Long plannedVerificationId,
        Integer commitmentTask,
        Date deadline,
        Date notice,
        Date reminder,
        Date plannedVerificationDate,
        Integer apply,
        Integer delayedByImplementer,
        Integer delayedByVerifier,
        Integer daysToVerify,
        Long businessUnitId,
        Long businessUnitDepartmentId,
        Date finishImplementationOn,
        Date finishVerificationOn,
        Date startImplementationOn,
        Date startVerificationOn,
        Long verificationPeriodicityId,
        Long implementationPeriodicityId,
        Long singleImplementationId,
        Integer showImplementerUser,
        Integer showVerifierUser,
        Integer showSource,
        Integer showObjective,
        Integer showPriority,
        Integer restrictAnticipationAttend,
        Integer recurrent,
        Integer activeImplementationCount,
        Integer verificationCount,
        Integer implementationCount,
        Integer anticipationAttendDays,
        Integer belongSeries,
        Integer childCount,
        String dynamicTableName,
        Long dynamicTableNameId,
        Integer fillType,
        String groupId,
        String groupName,
        String implementerNames,
        String implementerIds,
        String preImplementerNames,
        String preImplementerIds,
        String lastActionDescription,
        Date lastActionTimestamp,
        Date nextVerificationDate,
        Long parentPartitionActivityId,
        Long parentActivityId,
        Long parentActivityImplementationId,
        Long parentActivityVerificationId,
        Long parentActivityTaskId,
        Long parentTreeActivityId,
        Date plannedImplementationDate,
        Long lastActionUserId,
        Date nextImplementationDate,
        Owner implementer,
        Owner verifier,
        Owner preImplementer,
        Double plannedHours,
        Double actualHours,
        Periodicity verificationPeriodicity,
        Periodicity implementationPeriodicity,
        Long taskDeliveryTypeId,
        Long taskCategoryId,
        Boolean addImplementationOnCreate,
        Boolean addVerificationOnCreate,
        Boolean verificationReqOnPlanning,
        Boolean mustUpdateImplementationAtReturn,
        Boolean addVerificationAvailable,
        Boolean allowsUseOnPlanning,
        String activityOrder,
        Long categoryId,
        Long clientId
    ) {
        this.id = id;
        this.code = code;
        this.description = description;
        this.status = status;
        this.deleted = deleted;
        this.createdDate = createdDate;
        this.lastModifiedDate = lastModifiedDate;
        this.createdBy = createdBy;
        this.lastModifiedBy = lastModifiedBy;
        this.objectiveId = objectiveId;
        if (objectiveId != null) {
            this.objective = new ActivityObjective(objectiveId);
        }
        this.sourceId = sourceId;
        if (sourceId != null) {
            this.source = new ActivitySource(sourceId);
        }
        this.priorityId = priorityId;
        if (priorityId != null) {
            this.priority = new ActivityPriority(priorityId);
        }
        this.implementerOwnerId = implementerOwnerId;
        this.verifierOwnerId = verifierOwnerId;
        this.preImplementerOwnerId = preImplementerOwnerId;
        this.commitmentDate = commitmentDate;
        this.implementationOn = implementationOn;
        this.verificationOn = verificationOn;
        this.implementedByUserId = implementedByUserId;
        this.verifiedByUserId = verifiedByUserId;
        this.progress = progress;
        this.formId = formId;
        this.typeId = typeId;
        this.type = type;
        this.outstandingSurveyId = outstandingSurveyId;
        this.formFillIsRequested = formFillIsRequested;
        this.formFillIsClosed = formFillIsClosed;
        this.parentCode = parentCode;
        this.recurrenceId = recurrenceId;
        this.recurrenceStatus = recurrenceStatus;
        this.plannedVerificationId = plannedVerificationId;
        this.commitmentTask = commitmentTask;
        this.deadline = deadline;
        this.notice = notice;
        this.reminder = reminder;
        this.plannedVerificationDate = plannedVerificationDate;
        this.apply = apply;
        this.delayedByImplementer = delayedByImplementer;
        this.delayedByVerifier = delayedByVerifier;
        this.daysToVerify = daysToVerify;
        this.businessUnitId = businessUnitId;
        this.businessUnitDepartmentId = businessUnitDepartmentId;
        this.finishImplementationOn = finishImplementationOn;
        this.finishVerificationOn = finishVerificationOn;
        this.startImplementationOn = startImplementationOn;
        this.startVerificationOn = startVerificationOn;
        this.verificationPeriodicityId = verificationPeriodicityId;
        this.implementationPeriodicityId = implementationPeriodicityId;
        this.singleImplementationId = singleImplementationId;
        this.showImplementerUser = showImplementerUser;
        this.showVerifierUser = showVerifierUser;
        this.showSource = showSource;
        this.showObjective = showObjective;
        this.showPriority = showPriority;
        this.restrictAnticipationAttend = restrictAnticipationAttend;
        this.recurrent = recurrent;
        this.activeImplementationCount = activeImplementationCount;
        this.verificationCount = verificationCount;
        this.implementationCount = implementationCount;
        this.anticipationAttendDays = anticipationAttendDays;
        this.belongSeries = belongSeries;
        this.childCount = childCount;
        this.dynamicTableName = dynamicTableName;
        this.dynamicTableNameId = dynamicTableNameId;
        this.fillType = fillType;
        this.groupId = groupId;
        this.groupName = groupName;
        this.implementerNames = implementerNames;
        this.implementerIds = implementerIds;
        this.preImplementerNames = preImplementerNames;
        this.preImplementerIds = preImplementerIds;
        this.lastActionDescription = lastActionDescription;
        this.lastActionTimestamp = lastActionTimestamp;
        this.nextVerificationDate = nextVerificationDate;
        this.parentPartitionActivityId = parentPartitionActivityId;
        this.parentActivityId = parentActivityId;
        this.parentActivityImplementationId = parentActivityImplementationId;
        this.parentActivityVerificationId = parentActivityVerificationId;
        this.parentActivityTaskId = parentActivityTaskId;
        this.parentTreeActivityId = parentTreeActivityId;
        this.plannedImplementationDate = plannedImplementationDate;
        this.lastActionUserId = lastActionUserId;
        this.nextImplementationDate = nextImplementationDate;
        this.implementer = implementer;
        this.verifier = verifier;
        this.preImplementer = preImplementer;
        this.plannedHours = plannedHours;
        this.actualHours = actualHours;
        this.verificationPeriodicity = verificationPeriodicity;
        this.implementationPeriodicity = implementationPeriodicity;
        this.taskDeliveryTypeId = taskDeliveryTypeId;
        this.taskCategoryId = taskCategoryId;
        this.addImplementationOnCreate = addImplementationOnCreate;
        this.addVerificationOnCreate = addVerificationOnCreate;
        this.verificationReqOnPlanning = verificationReqOnPlanning;
        this.mustUpdateImplementationAtReturn = mustUpdateImplementationAtReturn;
        this.addVerificationAvailable = addVerificationAvailable;
        this.allowsUseOnPlanning = allowsUseOnPlanning;
        this.activityOrder = activityOrder;
        this.categoryId = categoryId;
        if (categoryId != null) {
            this.category = new ActivityCategory(categoryId);
        }
        this.clientId = clientId;
    }
    
    @Id
    @Column(name = "activity_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }
    
    @Column(name = "rescheduled")
    public Integer getRescheduled() {
        return rescheduled;
    }

    public void setRescheduled(Integer rescheduled) {
        this.rescheduled = rescheduled;
    }
    
    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "last_action_user_id")
    @Override
    public Long getLastActionUserId() {
        return lastActionUserId;
    }

    @Override
    public void setLastActionUserId(Long lastActionUserId) {
        this.lastActionUserId = lastActionUserId;
    }

    @Column(name = "last_action_description")
    @Override
    public String getLastActionDescription() {
        return lastActionDescription;
    }

    @Override
    public void setLastActionDescription(String lastActionDescription) {
        this.lastActionDescription = lastActionDescription;
    }

    @Column(name = "last_action_timestamp")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getLastActionTimestamp() {
        return lastActionTimestamp;
    }

    @Override
    public void setLastActionTimestamp(Date lastActionTimestamp) {
        this.lastActionTimestamp = lastActionTimestamp;
    }
    
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "user_id",
        name = "created_by", updatable = false, insertable = false
    )
    public UserRef getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(UserRef createdByUser) {
        this.createdByUser = createdByUser;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }
    
    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    @Column(name = "activity_type_id", updatable = false, insertable = false)
    public Long getTypeId() {
        return typeId;
    }

    @Override
    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    /**
     * @return the objective
     */
    @Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "activity_objective_id")
    public ActivityObjective getObjective() {
        return objective;
    }

    /**
     * @param objective the objective to set
     */
    public void setObjective(ActivityObjective objective) {
        this.objective = objective;
    }

    /**
     * @return the source
     */
    @Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "activity_source_id")
    public ActivitySource getSource() {
        return source;
    }

    /**
     * @param source the source to set
     */
    public void setSource(ActivitySource source) {
        this.source = source;
    }

    @Override
    @Column(name = "activity_source_id", updatable = false, insertable = false)
    public Long getSourceId() {
        return sourceId;
    }

    @Override
    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * @return the priority
     */
    @Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "activity_priority_id")
    public ActivityPriority getPriority() {
        return priority;
    }

    /**
     * @param priority the priority to set
     */
    public void setPriority(ActivityPriority priority) {
        this.priority = priority;
    }

    @Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "activity_category_id")
    public ActivityCategory getCategory() {
        return category;
    }

    public void setCategory(ActivityCategory category) {
        this.category = category;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "verifier_id", updatable = false, insertable = false)
    public SingleUserOwner getSingleVerifier() {
        return singleVerifier;
    }

    public void setSingleVerifier(SingleUserOwner singleVerifier) {
        this.singleVerifier = singleVerifier;
    }
    
    /**
     * @return the implementer
     */
    @Cache(region = CacheConstants.OWNER, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "implementer_id")
    @Override
    public Owner getImplementer() {
        return implementer;
    }

    /**
     * @param implementer the implementer to set
     */
    @Override
    public void setImplementer(Owner implementer) {
        this.implementer = implementer;
    }

    @Override
    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "pre_implementer_id")
    public Owner getPreImplementer() {
        return preImplementer;
    }

    @Override
    public void setPreImplementer(Owner preImplementer) {
        this.preImplementer = preImplementer;
    }

    /**
     * @return the verifier
     */
    @Cache(region = CacheConstants.OWNER, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "verifier_id")
    @Override
    public Owner getVerifier() {
        return verifier;
    }

    /**
     * @param verifier the verifier to set
     */
    @Override
    public void setVerifier(Owner verifier) {
        this.verifier = verifier;
    }

    /**
     * @return the commitmentDate
     */
    @Column(name = "commitment_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getCommitmentDate() {
        return commitmentDate;
    }

    /**
     * @param commitmentDate the commitmentDate to set
     *
     */
    @Override
    public void setCommitmentDate(Date commitmentDate) {
        this.commitmentDate = commitmentDate;
    }

    @Override
    @Column(name = "planned_verification_id")
    public Long getPlannedVerificationId() {
        return plannedVerificationId;
    }

    @Override
    public void setPlannedVerificationId(Long plannedVerificationId) {
        this.plannedVerificationId = plannedVerificationId;
    }

    @Override
    @Column(name = "parent_partition_activity_id")
    public Long getParentPartitionActivityId() {
        return parentPartitionActivityId;
    }

    @Override
    public void setParentPartitionActivityId(Long parentPartitionActivityId) {
        this.parentPartitionActivityId = parentPartitionActivityId;
    }
    
    @Column(name = "parent_tree_activity_id")
    @Override
    public Long getParentTreeActivityId() {
        return parentTreeActivityId;
    }

    @Override
    public void setParentTreeActivityId(Long parentTreeActivityId) {
        this.parentTreeActivityId = parentTreeActivityId;
    }

    @Column(name = "parent_activity_id")
    @Override
    public Long getParentActivityId() {
        return parentActivityId;
    }

    @Override
    public void setParentActivityId(Long parentActivityId) {
        this.parentActivityId = parentActivityId;
    }

    @Column(name = "parent_activity_imp_id")
    @Override
    public Long getParentActivityImplementationId() {
        return parentActivityImplementationId;
    }

    @Override
    public void setParentActivityImplementationId(Long parentActivityImplementationId) {
        this.parentActivityImplementationId = parentActivityImplementationId;
    }

    @Column(name = "parent_activity_task_id")
    public Long getParentActivityTaskId() {
        return parentActivityTaskId;
    }

    public void setParentActivityTaskId(Long parentActivityTaskId) {
        this.parentActivityTaskId = parentActivityTaskId;
    }
    
    @Column(name = "parent_activity_ver_id")
    @Override
    public Long getParentActivityVerificationId() {
        return parentActivityVerificationId;
    }

    @Override
    public void setParentActivityVerificationId(Long parentActivityVerificationId) {
        this.parentActivityVerificationId = parentActivityVerificationId;
    }
    
    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "planned_verification_id", updatable = false, insertable = false
    )
    public Activity getPlannedVerification() {
        return plannedVerification;
    }

    public void setPlannedVerification(Activity plannedVerification) {
        this.plannedVerification = plannedVerification;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "parent_activity_id", updatable = false, insertable = false
    )
    public Activity getParentActivity() {
        return parentActivity;
    }

    public void setParentActivity(Activity parentActivity) {
        this.parentActivity = parentActivity;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "parent_tree_activity_id", updatable = false, insertable = false
    )
    public Activity getParentTreeActivity() {
        return parentTreeActivity;
    }

    public void setParentTreeActivity(Activity parentTreeActivity) {
        this.parentTreeActivity = parentTreeActivity;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "parent_partition_activity_id", updatable = false, insertable = false
    )
    public Activity getParentPartitionActivity() {
        return parentPartitionActivity;
    }

    public void setParentPartitionActivity(Activity parentPartitionActivity) {
        this.parentPartitionActivity = parentPartitionActivity;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "parent_activity_imp_id", updatable = false, insertable = false
    )
    public Activity getParentActivityImplementation() {
        return parentActivityImplementation;
    }

    public void setParentActivityImplementation(Activity parentActivityImplementation) {
        this.parentActivityImplementation = parentActivityImplementation;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "parent_activity_task_id", updatable = false, insertable = false
    )
    public Activity getParentActivityTask() {
        return parentActivityTask;
    }

    public void setParentActivityTask(Activity parentActivityTask) {
        this.parentActivityTask = parentActivityTask;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "parent_activity_ver_id", updatable = false, insertable = false
    )
    public Activity getParentActivityVerification() {
        return parentActivityVerification;
    }

    public void setParentActivityVerification(Activity parentActivityVerification) {
        this.parentActivityVerification = parentActivityVerification;
    }
    
    
    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "recurrence", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<Activity> getEvents() {
        return events;
    }

    public void setEvents(Set<Activity> events) {
        this.events = events;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "parentPartitionActivity", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<Activity> getPartitions() {
        return partitions;
    }

    public void setPartitions(Set<Activity> partitions) {
        this.partitions = partitions;
    }
    
    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "plannedVerification", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<Activity> getPlannedImplementations() {
        return plannedImplementations;
    }

    public void setPlannedImplementations(Set<Activity> plannedImplementations) {
        this.plannedImplementations = plannedImplementations;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "parentActivity", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<Activity> getChilds() {
        return childs;
    }

    public void setChilds(Set<Activity> childs) {
        this.childs = childs;
    }
    

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "parentActivityImplementation", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<Activity> getImplementationSubTasks() {
        return implementationSubTasks;
    }

    public void setImplementationSubTasks(Set<Activity> implementationSubTasks) {
        this.implementationSubTasks = implementationSubTasks;
    }

    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "parentActivityImplementation", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    @SQLRestriction( "commitment_task = 1")
    public Set<Activity> getImplementationSubTasksOnly() {
        return implementationSubTasksOnly;
    }

    public void setImplementationSubTasksOnly(Set<Activity> implementationSubTasksOnly) {
        this.implementationSubTasksOnly = implementationSubTasksOnly;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "parentActivityVerification", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<Activity> getVerificationSubTasks() {
        return verificationSubTasks;
    }

    public void setVerificationSubTasks(Set<Activity> verificationSubTasks) {
        this.verificationSubTasks = verificationSubTasks;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "parentActivityTask", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<Activity> getPlannerSubTasks() {
        return plannerSubTasks;
    }

    public void setPlannerSubTasks(Set<Activity> plannerSubTasks) {
        this.plannerSubTasks = plannerSubTasks;
    }

    @Override
    @Column(name = "commitment_task")
    public Integer getCommitmentTask() {
        return commitmentTask;
    }

    @Override
    public void setCommitmentTask(Integer commitmentTask) {
        this.commitmentTask = commitmentTask;
    }

    /**
     * @return the implementationOn
     */
    @Column(name = "implementation_on")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getImplementationOn() {
        return implementationOn;
    }

    /**
     * @param implementationOn the implementationOn to set
     */
    @Override
    public void setImplementationOn(Date implementationOn) {
        this.implementationOn = implementationOn;
    }

    /**
     * @return the verificationOn
     */
    @Override
    @Column(name = "verification_on")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getVerificationOn() {
        return verificationOn;
    }

    /**
     * @param verificationOn the verificationOn to set
     */
    @Override
    public void setVerificationOn(Date verificationOn) {
        this.verificationOn = verificationOn;
    }
    
    /**
     * @return the progress
     */
    @Override
    @Column(name = "progress")
    public Double getProgress() {
        return progress;
    }

    /**
     * @param progress the progress to set
     */
    @Override
    public void setProgress(Double progress) {
        this.progress = progress;
    }
    
    /**
     * @return the apply
     */
    @Override
    @Column(name = "apply")
    public Integer getApply() {
        return apply;
    }

    /**
     * @param apply the apply to set
     */
    @Override
    public void setApply(Integer apply) {
        this.apply = apply;
    }

    @Column(name = "child_count")
    @Override
    public Integer getChildCount() {
        return childCount;
    }

    @Override
    public void setChildCount(Integer childCount) {
        this.childCount = childCount;
    }
    
    @Override
    @Column(name = "delayed_by_implementer")
    public Integer getDelayedByImplementer() {
        return delayedByImplementer;
    }

    @Override
    public void setDelayedByImplementer(Integer delayedByImplementer) {
        this.delayedByImplementer = delayedByImplementer;
    }

    @Override
    @Column(name = "delayed_by_verifier")
    public Integer getDelayedByVerifier() {
        return delayedByVerifier;
    }

    @Override
    public void setDelayedByVerifier(Integer delayedByVerifier) {
        this.delayedByVerifier = delayedByVerifier;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "implemented_by")
    public UserRef getImplementedBy() {
        return implementedBy;
    }
    
    public void setImplementedBy(UserRef implementedBy) {
        this.implementedBy = implementedBy;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "last_action_user_id", insertable = false, updatable = false)
    public UserRef getLastActionUser() {
        return lastActionUser;
    }

    public void setLastActionUser(UserRef lastActionUser) {
        this.lastActionUser = lastActionUser;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "verified_by")
    public UserRef getVerifiedBy() {
        return verifiedBy;
    }
    
    public void setVerifiedBy(UserRef verifiedBy) {
        this.verifiedBy = verifiedBy;
    }
    
    /**
     * Es el ID del entity "Document"
     *
     */
    @Override
    @Column(name = "form_id")
    public Long getFormId() {
        return formId;
    }

    @Override
    public void setFormId(Long formId) {
        this.formId = formId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "id",
        name = "form_id", updatable = false, insertable = false
    )
    public DocumentRef getForm() {
        return form;
    }

    public void setForm(DocumentRef form) {
        this.form = form;
    }

    @Override
    @Column(name = "outstanding_survey_id")
    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    @Override
    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "outstanding_surveys_id",
        name = "outstanding_survey_id", updatable = false, insertable = false
    )
    public OutstandingSurveysRef getOutstandingSurvey() {
        return outstandingSurvey;
    }

    public void setOutstandingSurvey(OutstandingSurveysRef outstandingSurvey) {
        this.outstandingSurvey = outstandingSurvey;
    }

    @Column(name = "form_fill_is_requested")
    @Override
    public Integer getFormFillIsRequested() {
        return formFillIsRequested;
    }

    @Override
    public void setFormFillIsRequested(Integer formFillIsRequested) {
        if (formFillIsRequested == null) {
            this.formFillIsRequested = 0;
        } else {
            this.formFillIsRequested = formFillIsRequested;
        }
    }
    
    @Override
    @Column(name = "form_fill_is_closed")
    public Integer getFormFillIsClosed() {
        return formFillIsClosed;
    }

    @Override
    public void setFormFillIsClosed(Integer formFillIsClosed) {
        if (formFillIsClosed == null) {
            this.formFillIsClosed = 0;
        } else {
            this.formFillIsClosed = formFillIsClosed;
        }
    }

    @Override
    @Column(name = "deadline")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getDeadline() {
        return deadline;
    }

    @Override
    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    @Override
    @Column(name = "notice")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getNotice() {
        return notice;
    }

    @Override
    public void setNotice(Date notice) {
        this.notice = notice;
    }

    @Override
    @Column(name = "reminder")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getReminder() {
        return reminder;
    }
    
    @Override
    public void setReminder(Date reminder) {
        this.reminder = reminder;
    }
    
    @Column(name = "days_to_verify")
    @Override
    public Integer getDaysToVerify() {
        return daysToVerify;
    }

    @Override
    public void setDaysToVerify(Integer daysToVerify) {
        this.daysToVerify = daysToVerify;
    }

    @Override
    @Column(name = "parent_code")
    public String getParentCode() {
        return parentCode;
    }

    @Override
    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    @Override
    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Override
    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "business_unit_department_id",
        name = "business_unit_department_id", updatable = false, insertable = false
    )
    @Override
    public BusinessUnitDepartmentRef getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(
            BusinessUnitDepartmentRef businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "business_unit_id",
        name = "business_unit_id", updatable = false, insertable = false
    )
    @Override
    public BusinessUnitRef getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitRef businessUnit) {
        this.businessUnit = businessUnit;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 17 * hash + Objects.hashCode(this.code);
        hash = 17 * hash + Objects.hashCode(this.description);
        hash = 17 * hash + Objects.hashCode(this.plannerId);
        hash = 17 * hash + Objects.hashCode(this.clientId);
        hash = 17 * hash + Objects.hashCode(this.typeId);
        hash = 17 * hash + Objects.hashCode(this.id);
        return hash;
    }


    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Activity other = (Activity) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.description, other.description)) {
            return false;
        }
        if (!Objects.equals(this.parentCode, other.parentCode)) {
            return false;
        }
        if (!Objects.equals(this.groupId, other.groupId)) {
            return false;
        }
        if (!Objects.equals(this.groupName, other.groupName)) {
            return false;
        }
        if (!Objects.equals(this.implementerIds, other.implementerIds)) {
            return false;
        }
        if (!Objects.equals(this.preImplementerIds, other.preImplementerIds)) {
            return false;
        }
        if (!Objects.equals(this.parentPlannerId, other.parentPlannerId)) {
            return false;
        }
        if (!Objects.equals(this.plannerId, other.plannerId)) {
            return false;
        }
        if (!Objects.equals(this.clientId, other.clientId)) {
            return false;
        }
        if (!Objects.equals(this.activityResolutionId, other.activityResolutionId)) {
            return false;
        }
        if (!Objects.equals(this.parentPartitionActivityId, other.parentPartitionActivityId)) {
            return false;
        }
        if (!Objects.equals(this.parentTreeActivityId, other.parentTreeActivityId)) {
            return false;
        }
        if (!Objects.equals(this.parentActivityId, other.parentActivityId)) {
            return false;
        }
        if (!Objects.equals(this.parentActivityTaskId, other.parentActivityTaskId)) {
            return false;
        }
        if (!Objects.equals(this.parentActivityImplementationId, other.parentActivityImplementationId)) {
            return false;
        }
        if (!Objects.equals(this.sourceId, other.sourceId)) {
            return false;
        }
        if (!Objects.equals(this.commitmentDate, other.commitmentDate)) {
            return false;
        }
        if (!Objects.equals(this.commitmentTask, other.commitmentTask)) {
            return false;
        }
        if (!Objects.equals(this.typeId, other.typeId)) {
            return false;
        }
        if (!Objects.equals(this.outstandingSurveyId, other.outstandingSurveyId)) {
            return false;
        }
        if (!Objects.equals(this.fillType, other.fillType)) {
            return false;
        }
        if (!Objects.equals(this.objectiveId, other.objectiveId)) {
            return false;
        }
        if (!Objects.equals(this.priorityId, other.priorityId)) {
            return false;
        }
        if (!Objects.equals(this.categoryId, other.categoryId)) {
            return false;
        }
        if (!Objects.equals(this.plannedHours, other.plannedHours)) {
            return false;
        }
        return Objects.equals(this.id, other.id);
    }


    @Override
    public String toString() {
        return "qms.activity.entity.Activity[ id=" + id + " ]";
    }

    @Override
    @Column(name = "dynamic_table_name")
    public String getDynamicTableName() {
        return dynamicTableName;
    }

    @Override
    public void setDynamicTableName(String dynamicTableName) {
        this.dynamicTableName = dynamicTableName;
    }

    @Override
    @Column(name = "dynamic_table_name_id")
    public Long getDynamicTableNameId() {
        return dynamicTableNameId;
    }

    @Override
    public void setDynamicTableNameId(Long dynamicTableNameId) {
        this.dynamicTableNameId = dynamicTableNameId;
    }

    /**
     * @return the type
     */
    @Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "activity_type_id")
    @Override
    public ActivityType getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    @Override
    public void setType(ActivityType type) {
        this.type = type;
    }

    /**
     * ActivityType.FILL_TYPE
     *
     * @return
     */
    @Column(name = "fill_type")
    @Override
    public Integer getFillType() {
        return fillType;
    }

    @Override
    public void setFillType(Integer fillType) {
        this.fillType = fillType;
    }

    @Override
    @Transient
    public Map<String, Object> getDynamicFieldData() {
        return dynamicFieldData;
    }

    @Override
    public void setDynamicFieldData(Map dynamicFieldData) {
        this.dynamicFieldData = dynamicFieldData;
    }

    @Override
    public void setDynamicFieldInsertDTO(DynamicFieldInsertDTO dynamicFieldInsertDTO) {}

    @Override
    @Transient
    public DynamicFieldInsertDTO getDynamicFieldInsertDTO() { return null; }

    @Cache(region = CacheConstants.PERIODICITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToOne
    @Cascade(CascadeType.ALL)
    @JoinColumn(referencedColumnName = "intperiodicidadid", name = "implementation_periodicity_id")
    @Override
    public Periodicity getImplementationPeriodicity() {
        return implementationPeriodicity;
    }

    @Override
    public void setImplementationPeriodicity(
            Periodicity implementationPeriodicity) {
        this.implementationPeriodicity = implementationPeriodicity;
    }

    @Cache(region = CacheConstants.PERIODICITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToOne
    @Cascade(CascadeType.ALL)
    @JoinColumn(referencedColumnName = "intperiodicidadid", name = "verification_periodicity_id")
    @Override
    public Periodicity getVerificationPeriodicity() {
        return verificationPeriodicity;
    }

    @Override
    public void setVerificationPeriodicity(Periodicity verificationPeriodicity) {
        this.verificationPeriodicity = verificationPeriodicity;
    }

    @Override
    @Column(name = "recurrent")
    public Integer getRecurrent() {
        return recurrent;
    }

    @Override
    public void setRecurrent(Integer recurrent) {
        this.recurrent = recurrent;
    }

    @Override
    @Column(name = "recurrence_id")
    public Long getRecurrenceId() {
        return recurrenceId;
    }

    @Override
    public void setRecurrenceId(Long recurrenceId) {
        this.recurrenceId = recurrenceId;
    }
    
    @Override
    @Column(name = "recurrence_status")
    public Integer getRecurrenceStatus() {
        return recurrenceStatus;
    }

    @Override
    public void setRecurrenceStatus(Integer recurrenceStatus) {
        this.recurrenceStatus = recurrenceStatus;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "recurrence_id", updatable = false, insertable = false)
    public Activity getRecurrence() {
        return recurrence;
    }

    public void setRecurrence(Activity recurrence) {
        this.recurrence = recurrence;
    }

    @Column(name = "finish_implementation_on")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getFinishImplementationOn() {
        return this.finishImplementationOn;
    }

    @Override
    public void setFinishImplementationOn(Date finishImplementationOn) {
        this.finishImplementationOn = finishImplementationOn;
    }

    @Column(name = "start_implementation_on")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getStartImplementationOn() {
        return this.startImplementationOn;
    }

    @Override
    public void setStartImplementationOn(Date startImplementationOn) {
        this.startImplementationOn = startImplementationOn;
    }

    @Column(name = "next_implementation_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getNextImplementationDate() {
        return this.nextImplementationDate;
    }

    @Override
    public void setNextImplementationDate(Date nextImplementationDate) {
        this.nextImplementationDate = nextImplementationDate;
    }

    @Column(name = "implementation_periodicity_id", updatable = false, insertable = false)
    @Override
    public Long getImplementationPeriodicityId() {
        return implementationPeriodicityId;
    }

    @Override
    public void setImplementationPeriodicityId(Long implementationPeriodicityId) {
        this.implementationPeriodicityId = implementationPeriodicityId;
    }

    @Column(name = "verification_periodicity_id", updatable = false, insertable = false)
    @Override
    public Long getVerificationPeriodicityId() {
        return verificationPeriodicityId;
    }

    @Override
    public void setVerificationPeriodicityId(Long verificationPeriodicityId) {
        this.verificationPeriodicityId = verificationPeriodicityId;
    }

    @Column(name = "finish_verification_on")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getFinishVerificationOn() {
        return this.finishVerificationOn;
    }

    @Override
    public void setFinishVerificationOn(Date finishVerificationOn) {
        this.finishVerificationOn = finishVerificationOn;
    }

    @Column(name = "start_verification_on")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getStartVerificationOn() {
        return this.startVerificationOn;
    }

    @Override
    public void setStartVerificationOn(Date startVerificationOn) {
        this.startVerificationOn = startVerificationOn;
    }

    @Column(name = "next_verification_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getNextVerificationDate() {
        return this.nextVerificationDate;
    }

    @Override
    public void setNextVerificationDate(Date nextVerificationDate) {
        this.nextVerificationDate = nextVerificationDate;
    }

    @Column(name = "active_implementation_count")
    @Override
    public Integer getActiveImplementationCount() {
        return activeImplementationCount;
    }

    @Override
    public void setActiveImplementationCount(Integer activeImplementationCount) {
        this.activeImplementationCount = activeImplementationCount;
    }

    @Column(name = "verification_count")
    @Override
    public Integer getVerificationCount() {
        return verificationCount;
    }

    @Override
    public void setVerificationCount(Integer verificationCount) {
        this.verificationCount = verificationCount;
    }

    @Column(name = "implementation_count")
    @Override
    public Integer getImplementationCount() {
        return implementationCount;
    }

    @Override
    public void setImplementationCount(Integer implementationCount) {
        this.implementationCount = implementationCount;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_id",
        name = "single_implementation_id", updatable = false, insertable = false
    )
    public Activity getSingleImplementation() {
        return singleImplementation;
    }

    public void setSingleImplementation(Activity singleImplementation) {
        this.singleImplementation = singleImplementation;
    }

    @Column(name = "single_implementation_id")
    @Override
    public Long getSingleImplementationId() {
        return singleImplementationId;
    }

    @Override
    public void setSingleImplementationId(Long singleImplementationId) {
        this.singleImplementationId = singleImplementationId;
    }

    @Column(name = "activity_objective_id", insertable = false, updatable = false)
    @Override
    public Long getObjectiveId() {
        return objectiveId;
    }

    /**
     * @param objectiveId the objectiveId to set
     */
    @Override
    public void setObjectiveId(Long objectiveId) {
        this.objectiveId = objectiveId;
    }

    @Column(name = "activity_priority_id", insertable = false, updatable = false)
    @Override
    public Long getPriorityId() {
        return priorityId;
    }

    @Override
    public void setPriorityId(Long priorityId) {
        this.priorityId = priorityId;
    }

    @Column(name = "implementer_id", insertable = false, updatable = false)
    @Override
    public Long getImplementerOwnerId() {
        return implementerOwnerId;
    }

    @Override
    public void setImplementerOwnerId(Long implementerOwnerId) {
        this.implementerOwnerId = implementerOwnerId;
    }

    @Override
    @Column(name = "pre_implementer_id", insertable = false, updatable = false)
    public Long getPreImplementerOwnerId() {
        return preImplementerOwnerId;
    }

    @Override
    public void setPreImplementerOwnerId(Long preImplementerOwnerId) {
        this.preImplementerOwnerId = preImplementerOwnerId;
    }

    @Column(name = "verifier_id", insertable = false, updatable = false)
    @Override
    public Long getVerifierOwnerId() {
        return verifierOwnerId;
    }

    @Override
    public void setVerifierOwnerId(Long verifierOwnerId) {
        this.verifierOwnerId = verifierOwnerId;
    }
    
    @Column(name = "implemented_by", insertable = false, updatable = false)
    @Override
    public Long getImplementedByUserId() {
        return implementedByUserId;
    }
    
    @Override
    public void setImplementedByUserId(Long implementedByUserId) {
        this.implementedByUserId = implementedByUserId;
    }
    
    @Column(name = "verified_by", insertable = false, updatable = false)
    @Override
    public Long getVerifiedByUserId() {
        return verifiedByUserId;
    }
    
    @Override
    public void setVerifiedByUserId(Long verifiedByUserId) {
        this.verifiedByUserId = verifiedByUserId;
    }
    
    @Column(name = "planned_implementation")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getPlannedImplementationDate() {
        return plannedImplementationDate;
    }
    
    @Override
    public void setPlannedImplementationDate(Date plannedImplementationDate) {
        this.plannedImplementationDate = plannedImplementationDate;
    }
    
    @Column(name = "planned_verification")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getPlannedVerificationDate() {
        return plannedVerificationDate;
    }
    
    @Override
    public void setPlannedVerificationDate(Date plannedVerificationDate) {
        this.plannedVerificationDate = plannedVerificationDate;
    }
    
    @Column(name = "show_objective")
    @Override
    public Integer getShowObjective() {
        return showObjective;
    }

    @Override
    public void setShowObjective(Integer showObjective) {
        this.showObjective = showObjective;
    }

    @Column(name = "show_priority")
    @Override
    public Integer getShowPriority() {
        return showPriority;
    }

    @Override
    public void setShowPriority(Integer showPriority) {
        this.showPriority = showPriority;
    }
    
    @Override
    @Column(name = "belong_series")
    public Integer getBelongSeries() {
        return belongSeries;
    }

    @Override
    public void setBelongSeries(Integer belongSeries) {
        this.belongSeries = belongSeries;
    }
    
    @Column(name = "show_implementer_user")
    @Override
    public Integer getShowImplementerUser() {
        return showImplementerUser;
    }

    @Override
    public void setShowImplementerUser(Integer showImplementerUser) {
        this.showImplementerUser = showImplementerUser;
    }

    @Column(name = "show_verifier_user")
    @Override
    public Integer getShowVerifierUser() {
        return showVerifierUser;
    }

    @Override
    public void setShowVerifierUser(Integer showVerifierUser) {
        this.showVerifierUser = showVerifierUser;
    }

    @Column(name = "show_source")
    @Override
    public Integer getShowSource() {
        return showSource;
    }

    @Override
    public void setShowSource(Integer showSource) {
        this.showSource = showSource;
    }
    
    @Column(name = "restrict_anticipation_attend")
    @Override
    public Integer getRestrictAnticipationAttend() {
        return restrictAnticipationAttend;
    }

    @Override
    public void setRestrictAnticipationAttend(Integer restrictAnticipationAttend) {
        this.restrictAnticipationAttend = restrictAnticipationAttend;
    }

    @Column(name = "anticipation_attend_days")
    @Override
    public Integer getAnticipationAttendDays() {
        return anticipationAttendDays;
    }

    @Override
    public void setAnticipationAttendDays(Integer anticipationAttendDays) {
        this.anticipationAttendDays = anticipationAttendDays;
    }

    @Column(name = "group_id")
    @Override
    public String getGroupId() {
        return groupId;
    }

    @Override
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    @Column(name = "group_name")
    @Override
    public String getGroupName() {
        return groupName;
    }

    @Override
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    @Column(name = "implementer_names")
    @Override
    public String getImplementerNames() {
        return implementerNames;
    }

    @Override
    public void setImplementerNames(String implementerNames) {
        this.implementerNames = implementerNames;
    }

    @Override
    @Column(name = "implementer_ids")
    public String getImplementerIds() {
        return implementerIds;
    }

    @Override
    public void setImplementerIds(String implementerIds) {
        this.implementerIds = implementerIds;
    }

    @Column(name = "pre_implementer_names")
    public String getPreImplementerNames() {
        return preImplementerNames;
    }

    public void setPreImplementerNames(String preImplementerNames) {
        this.preImplementerNames = preImplementerNames;
    }

    @Column(name = "pre_implementer_ids")
    public String getPreImplementerIds() {
        return preImplementerIds;
    }

    public void setPreImplementerIds(String preImplementerIds) {
        this.preImplementerIds = preImplementerIds;
    }
    
    @Column(name = "planner_id")
    public Long getPlannerId() {
        return plannerId;
    }

    public void setPlannerId(Long plannerId) {
        this.plannerId = plannerId;
    }
    
    @Column(name = "client_id")
    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    @Cache(region = CacheConstants.PLANNER, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "client_id",
        name = "client_id", updatable = false, insertable = false
    )
    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    @Column(name = "parent_planner_id")
    public Long getParentPlannerId() {
        return parentPlannerId;
    }

    public void setParentPlannerId(Long parentPlannerId) {
        this.parentPlannerId = parentPlannerId;
    }

    @Cache(region = CacheConstants.PLANNER, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "planner_id",
        name = "parent_planner_id", updatable = false, insertable = false
    )
    public Planner getParentPlanner() {
        return parentPlanner;
    }

    public void setParentPlanner(Planner parentPlanner) {
        this.parentPlanner = parentPlanner;
    }

    @Cache(region = CacheConstants.PLANNER, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "planner_id",
        name = "planner_id", updatable = false, insertable = false
    )
    public Planner getPlanner() {
        return planner;
    }

    public void setPlanner(Planner planner) {
        this.planner = planner;
    }

    @OneToMany(cascade = {jakarta.persistence.CascadeType.ALL}, fetch = FetchType.EAGER)
    @JoinTable(
            name = "activity_participant",
            joinColumns = @JoinColumn(name = "activity_id"),
            inverseJoinColumns = @JoinColumn(name = "user_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<UserRef> getParticipants() {
        return participants;
    }

    public void setParticipants(Set<UserRef> participants) {
        this.participants = participants;
    }

    @Override
    @Column(name = "is_planned")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getIsPlanned() {
        return isPlanned;
    }

    @Override
    public void setIsPlanned(Boolean isPlanned) {
        if (isPlanned == null) {
            this.isPlanned = false;
        } else {
            this.isPlanned = isPlanned;
        }
    }
    
    @Override
    @Column(name = "planned_hours", precision = 4, length = 18)
    public Double getPlannedHours() {
        return plannedHours;
    }
    
    @Override
    public void setPlannedHours(Double plannedHours) {
        this.plannedHours = plannedHours;
    }

    @Override
    @Column(name = "actual_hours", precision = 4, length = 18)
    public Double getActualHours() {
        return actualHours;
    }
    
    @Override
    public void setActualHours(Double actualHours) {
        if (actualHours == null) {
            this.actualHours = 0.0;
        } else {
            this.actualHours = actualHours;
        }
    }

    @Override
    @Column(name = "enable_delivery_setup")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getEnableDeliverySetUp() {
        return enableDeliverySetUp;
    }

    @Override
    public void setEnableDeliverySetUp(Boolean enableDeliverySetUp) {
        this.enableDeliverySetUp = enableDeliverySetUp;
    }

    @Override
    @Column(name = "delivery_type_id")
    public Long getTaskDeliveryTypeId() {
        return taskDeliveryTypeId;
    }

    @Override
    public void setTaskDeliveryTypeId(Long taskDeliveryTypeId) {
        this.taskDeliveryTypeId = taskDeliveryTypeId;
    }

    @Override
    @Column(name = "task_category_id")
    public Long getTaskCategoryId() {
        return taskCategoryId;
    }

    @Override
    public void setTaskCategoryId(Long taskCategoryId) {
        this.taskCategoryId = taskCategoryId;
    }
    
    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Override
    @OneToMany(cascade = jakarta.persistence.CascadeType.ALL, mappedBy = "activity", fetch = FetchType.EAGER)
    @Fetch(FetchMode.JOIN)
    @BatchSize(size = 5)
    public Set<ActivitySystemLink> getSystemLinks() {
        return systemLinks;
    }

    @Override
    public void setSystemLinks(Set<ActivitySystemLink> systemLinks) {
        this.systemLinks = systemLinks;
    }
    
    /**
     * @see IActivityType#getFollowUpImplementationDelay()
     */
    @Override
    @Column(name = "follow_up_implementation_delay")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getFollowUpImplementationDelay() {
        return followUpImplementationDelay;
    }

    @Override
    public void setFollowUpImplementationDelay(Boolean followUpImplementationDelay) {
        if (followUpImplementationDelay == null) {
            this.followUpImplementationDelay = false;
        } else {
            this.followUpImplementationDelay = followUpImplementationDelay;
        }
    }
    
    @Column(name = "must_update_implementation_at_return")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    @Override
    public Boolean getMustUpdateImplementationAtReturn() {
        return mustUpdateImplementationAtReturn;
    }

    @Override
    public void setMustUpdateImplementationAtReturn(Boolean mustUpdateImplementationAtReturn) {
        if (mustUpdateImplementationAtReturn == null) {
            this.mustUpdateImplementationAtReturn = true;
        } else {
            this.mustUpdateImplementationAtReturn = mustUpdateImplementationAtReturn;
        }
    }

    @Column(name = "tz_auto_migrated", insertable = false, updatable = false)
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getTzAutoMigrated() {
        return tzAutoMigrated;
    }

    public void setTzAutoMigrated(Boolean tzAutoMigrated) {
        this.tzAutoMigrated = tzAutoMigrated;
    }
    
    @Column(name = "cancellation_reason")
    public String getCancellationReason() {
        return cancellationReason;
    }

    public void setCancellationReason(String cancellationReason) {
        this.cancellationReason = cancellationReason;
    }
  
    @Override
    @Column(name = "add_verification_on_create")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAddVerificationOnCreate() {
        return addVerificationOnCreate;
    }

    @Override
    public void setAddVerificationOnCreate(Boolean addVerificationOnCreate) {
        this.addVerificationOnCreate = addVerificationOnCreate;
    }

    /**
     * @see IActivityType#getVerificationReqOnPlanning()
     */
    @Override
    @Column(name = "verification_req_on_planning")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getVerificationReqOnPlanning() {
        return verificationReqOnPlanning;
    }

    @Override
    public void setVerificationReqOnPlanning(Boolean verificationReqOnPlanning) {
        this.verificationReqOnPlanning = verificationReqOnPlanning;
    }

    @Override
    @Column(name = "add_verification_available")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAddVerificationAvailable() {
        return addVerificationAvailable;
    }

    @Override
    public void setAddVerificationAvailable(Boolean addVerificationAvailable) {
        this.addVerificationAvailable = addVerificationAvailable;
    }

    @Override
    @Column(name = "add_implementation_on_create")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAddImplementationOnCreate() {
        return addImplementationOnCreate;
    }

    @Override
    public void setAddImplementationOnCreate(Boolean addImplementationOnCreate) {
        this.addImplementationOnCreate = addImplementationOnCreate;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "created_year", insertable = false, updatable = false)
    public Integer getCreatedYear() {
        return createdYear;
    }

    public void setCreatedYear(Integer createdYear) {
        this.createdYear = createdYear;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "commitment_year", insertable = false, updatable = false)
    public Integer getCommitmentYear() {
        return commitmentYear;
    }

    public void setCommitmentYear(Integer commitmentYear) {
        this.commitmentYear = commitmentYear;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "planned_implementation_year", insertable = false, updatable = false)
    public Integer getPlannedImplementationYear() {
        return plannedImplementationYear;
    }

    public void setPlannedImplementationYear(Integer plannedImplementationYear) {
        this.plannedImplementationYear = plannedImplementationYear;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "planned_verification_year", insertable = false, updatable = false)
    public Integer getPlannedVerificationYear() {
        return plannedVerificationYear;
    }

    public void setPlannedVerificationYear(Integer plannedVerificationYear) {
        this.plannedVerificationYear = plannedVerificationYear;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "created_week", insertable = false, updatable = false)
    public Integer getCreatedWeek() {
        return createdWeek;
    }

    public void setCreatedWeek(Integer createdWeek) {
        this.createdWeek = createdWeek;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "commitment_week", insertable = false, updatable = false)
    public Integer getCommitmentWeek() {
        return commitmentWeek;
    }

    public void setCommitmentWeek(Integer commitmentWeek) {
        this.commitmentWeek = commitmentWeek;
    }

    @Column(name = "activity_resolution_id")
    public Long getActivityResolutionId() {
        return activityResolutionId;
    }

    public void setActivityResolutionId(Long activityResolutionId) {
        this.activityResolutionId = activityResolutionId;
    }

    @Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "activity_resolution_id",
        name = "activity_resolution_id", updatable = false, insertable = false
    )
    public ActivityResolution getActivityResolution() {
        return activityResolution;
    }

    public void setActivityResolution(ActivityResolution activityResolution) {
        this.activityResolution = activityResolution;
    }
    
    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "planned_implementation_week", insertable = false, updatable = false)
    public Integer getPlannedImplementationWeek() {
        return plannedImplementationWeek;
    }

    public void setPlannedImplementationWeek(Integer plannedImplementationWeek) {
        this.plannedImplementationWeek = plannedImplementationWeek;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "planned_verification_week", insertable = false, updatable = false)
    public Integer getPlannedVerificationWeek() {
        return plannedVerificationWeek;
    }

    public void setPlannedVerificationWeek(Integer plannedVerificationWeek) {
        this.plannedVerificationWeek = plannedVerificationWeek;
    }

    @Column(name = "program_planned_week")
    public Integer getProgramPlannedWeek() {
        return programPlannedWeek;
    }

    public void setProgramPlannedWeek(Integer programPlannedWeek) {
        this.programPlannedWeek = programPlannedWeek;
    }

    @Column(name = "program_imp_names")
    public String getProgramImplementerNames() {
        return programImplementerNames;
    }

    public void setProgramImplementerNames(String programImplementerNames) {
        this.programImplementerNames = programImplementerNames;
    }

    @Column(name = "program_imp_ids")
    public String getProgramImplementerIds() {
        return programImplementerIds;
    }

    public void setProgramImplementerIds(String programImplementerIds) {
        this.programImplementerIds = programImplementerIds;
    }
    
    @Column(name = "count_comments")
    public Long getCountComments() {
        return countComments;
    }

    public void setCountComments(Long countComments) {
        this.countComments = countComments;
    }

    @Column(name = "activity_category_id", insertable = false, updatable = false)
    @Override
    public Long getCategoryId() {
        return categoryId;
    }

    @Override
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    @Override
    @Column(name = "allows_use_on_planning")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getAllowsUseOnPlanning() {
        return allowsUseOnPlanning;
    }

    @Override
    public void setAllowsUseOnPlanning(Boolean allowsUseOnPlanning) {
        this.allowsUseOnPlanning = allowsUseOnPlanning;
    }

    @Column(name = "activity_order")
    @Override
    public String getActivityOrder() {
        return activityOrder;
    }

    @Override
    public void setActivityOrder(String activityOrder) {
        this.activityOrder = activityOrder;
    }

    @Generated(event = {EventType.INSERT, EventType.UPDATE})
    @Column(name = "planned_week", insertable = false, updatable = false)
    public String getPlannedWeek() {
        return plannedWeek;
    }

    public void setPlannedWeek(String plannedWeek) {
        this.plannedWeek = plannedWeek;
    }

    @Column(name = "verification_planned_week")
    public String getVerificationPlannedWeek() {
        return verificationPlannedWeek;
    }

    public void setVerificationPlannedWeek(String verificationPlannedWeek) {
        this.verificationPlannedWeek = verificationPlannedWeek;
    }

    @Column(name = "count_files")
    public Long getCountFiles() {
        return countFiles;
    }

    public void setCountFiles(Long countFiles) {
        this.countFiles = countFiles;
    }

    @Column(name = "count_documents")
    public Long getCountDocument() {
        return countDocument;
    }

    public void setCountDocument(Long countDocument) {
        this.countDocument = countDocument;
    }

    @Column(name = "past_planned_implementation", updatable = false, insertable = false)
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getPastPlannedImplementation() {
        return pastPlannedImplementation;
    }

    public void setPastPlannedImplementation(Boolean pastPlannedImplementation) {
        this.pastPlannedImplementation = pastPlannedImplementation;
    }

    @Column(name = "past_planned_verification", updatable = false, insertable = false)
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getPastPlannedVerification() {
        return pastPlannedVerification;
    }

    public void setPastPlannedVerification(Boolean pastPlannedVerification) {
        this.pastPlannedVerification = pastPlannedVerification;
    }

    @Column(name = "created_date_timezone")
    public String getCreatedDateTimezone() {
        return createdDateTimezone;
    }

    public void setCreatedDateTimezone(String createdDateTimezone) {
        this.createdDateTimezone = createdDateTimezone;
    }

    @Column(name = "last_modified_date_timezone")
    public String getLastModifiedDateTimezone() {
        return lastModifiedDateTimezone;
    }

    public void setLastModifiedDateTimezone(String lastModifiedDateTimezone) {
        this.lastModifiedDateTimezone = lastModifiedDateTimezone;
    }
    
    @Column(name = "planned_implementation_timezone")
    public String getPlannedImplementationTimezone() {
        return plannedImplementationTimezone;
    }

    public void setPlannedImplementationTimezone(String plannedImplementationTimezone) {
        this.plannedImplementationTimezone = plannedImplementationTimezone;
    }

    @Column(name = "planned_verification_timezone")
    public String getPlannedVerificationTimezone() {
        return plannedVerificationTimezone;
    }

    public void setPlannedVerificationTimezone(String plannedVerificationTimezone) {
        this.plannedVerificationTimezone = plannedVerificationTimezone;
    }

    @Column(name = "start_implementation_on_timezone")
    public String getStartImplementationOnTimezone() {
        return startImplementationOnTimezone;
    }

    public void setStartImplementationOnTimezone(String startImplementationOnTimezone) {
        this.startImplementationOnTimezone = startImplementationOnTimezone;
    }

    @Column(name = "start_verification_on_timezone")
    public String getStartVerificationOnTimezone() {
        return startVerificationOnTimezone;
    }

    public void setStartVerificationOnTimezone(String startVerificationOnTimezone) {
        this.startVerificationOnTimezone = startVerificationOnTimezone;
    }

    @Column(name = "verification_on_timezone")
    public String getVerificationOnTimezone() {
        return verificationOnTimezone;
    }

    public void setVerificationOnTimezone(String verificationOnTimezone) {
        this.verificationOnTimezone = verificationOnTimezone;
    }

    @Column(name = "implementation_on_timezone")
    public String getImplementationOnTimezone() {
        return implementationOnTimezone;
    }

    public void setImplementationOnTimezone(String implementationOnTimezone) {
        this.implementationOnTimezone = implementationOnTimezone;
    }

    @Column(name = "finish_verification_on_timezone")
    public String getFinishVerificationOnTimezone() {
        return finishVerificationOnTimezone;
    }

    public void setFinishVerificationOnTimezone(String finishVerificationOnTimezone) {
        this.finishVerificationOnTimezone = finishVerificationOnTimezone;
    }

    @Column(name = "finish_implementation_on_timezone")
    public String getFinishImplementationOnTimezone() {
        return finishImplementationOnTimezone;
    }

    public void setFinishImplementationOnTimezone(String finishImplementationOnTimezone) {
        this.finishImplementationOnTimezone = finishImplementationOnTimezone;
    }

    @Override
    @Transient
    public String getDynamicTableNameLatest() {
        return dynamicTableNameLatest;
    }

    public void setDynamicTableNameLatest(String dynamicTableNameLatest) {
        this.dynamicTableNameLatest = dynamicTableNameLatest;
    }
}
