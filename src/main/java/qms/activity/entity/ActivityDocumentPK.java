package qms.activity.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;
import qms.util.interfaces.ILinkedDocumentPK;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ActivityDocumentPK implements Serializable, ILinkedDocumentPK {
                                                         
    private Long activityId;
    private Long documentId;

    public ActivityDocumentPK(Long activityId, Long documentId) {
        this.activityId = activityId;
        this.documentId = documentId;
    }

    public ActivityDocumentPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "document_id")
    @Override
    public Long getDocumentId() {
        return documentId;
    }

    @Override
    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + Objects.hashCode(this.activityId);
        hash = 97 * hash + Objects.hashCode(this.documentId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityDocumentPK other = (ActivityDocumentPK) obj;
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        if (!Objects.equals(this.documentId, other.documentId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ActivityDocumentPK{" + "activityId=" + activityId + ", documentId=" + documentId + '}';
    }

}
