/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.entity;

import Framework.Config.CompositeStandardEntity;
import qms.framework.util.CacheConstants;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.util.interfaces.IAuditableCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.CATALOGS_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "activity_type_department")
public class ActivityTypeDepartments extends CompositeStandardEntity<ActivityTypeDepartmentPK>
        implements Serializable, IAuditableCompositeEntity<ActivityTypeDepartmentPK>, ILinkedComposityGrid<ActivityTypeDepartmentPK>{

    private ActivityTypeDepartmentPK id;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    public ActivityTypeDepartments() {
    }

    public ActivityTypeDepartments(ActivityTypeDepartmentPK activityTypeDepartmentPK) {
        this.id = activityTypeDepartmentPK;
    }

    public ActivityTypeDepartments(Long activityTypeDepartmentPK, Long activityTypeId) {
        this.id = new ActivityTypeDepartmentPK(activityTypeDepartmentPK, activityTypeId);
    }

    @Override
    public ActivityTypeDepartmentPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public ActivityTypeDepartmentPK getId() {
        return id;
    }

    @Override
    public void setId(ActivityTypeDepartmentPK activityTypeDepartmentPK) {
        this.id = activityTypeDepartmentPK;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
}
