package qms.activity.entity;

import Framework.Config.CompositeStandardEntity;
import qms.framework.util.CacheConstants;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.util.interfaces.ILinkedStagable;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "swap_activity")
public class SwapActivity extends CompositeStandardEntity<SwapActivityPK> implements ILinkedStagable<SwapActivityPK> { 

    private static final long serialVersionUID = 1L;
    
    private SwapActivityPK id;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Integer stage;

    public SwapActivity() {
    }

    public SwapActivity(SwapActivityPK id) {
        this.id = id;
    }

    public SwapActivity(Long oldId, Long newId) {
        this.id = new SwapActivityPK(oldId, newId);
    }

    @Override
    public SwapActivityPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public SwapActivityPK getId() {
        return id;
    }

    @Override
    public void setId(SwapActivityPK id) {
        this.id = id;
    }
    

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public Integer getStage() {
        return this.stage;
    }

    @Override
    public void setStage(Integer stage) {
        this.stage = stage;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SwapActivity other = (SwapActivity) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.activity.entity.SwapActivity{" + "id=" + id + '}';
    }

}
