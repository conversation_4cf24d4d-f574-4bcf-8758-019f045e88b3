/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ActivityRelationshipPK implements Serializable {
                                                         
    private Long activityId;
    private Long relationActivityId;

    public ActivityRelationshipPK(Long activityId, Long relationActivityId) {
        this.activityId = activityId;
        this.relationActivityId = relationActivityId;
    }

    public ActivityRelationshipPK() {
    }
    
    @Basic(optional = false)
    @Column(name = "activity_id")
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Basic(optional = false)
    @Column(name = "relation_activity_id")
    public Long getRelationActivityId() {
        return relationActivityId;
    }

    public void setRelationActivityId(Long relationActivityId) {
        this.relationActivityId = relationActivityId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 37 * hash + Objects.hashCode(this.activityId);
        hash = 37 * hash + Objects.hashCode(this.relationActivityId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityRelationshipPK other = (ActivityRelationshipPK) obj;
        if (!Objects.equals(this.activityId, other.activityId)) {
            return false;
        }
        return Objects.equals(this.relationActivityId, other.relationActivityId);
    }

    @Override
    public String toString() {
        return "ActivityRelationshipPK{" + "activityId=" + activityId + ", relationActivityId=" + relationActivityId + '}';
    }
    
}
