package qms.activity.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

@Embeddable
public class ActivityWorkflowTypePK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long activityWorkflowId;
    private Long activityTypeId;

    public ActivityWorkflowTypePK(Long activityWorkflowId, Long activityTypeId) {
        this.activityWorkflowId = activityWorkflowId;
        this.activityTypeId = activityTypeId;
    }

    public ActivityWorkflowTypePK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "activity_workflow_id")
    public Long getActivityWorkflowId() {
        return activityWorkflowId;
    }

    public void setActivityWorkflowId(Long activityWorkflowId) {
        this.activityWorkflowId = activityWorkflowId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "activity_type_id")
    public Long getActivityTypeId() {
        return activityTypeId;
    }

    public void setActivityTypeId(Long activityTypeId) {
        this.activityTypeId = activityTypeId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.activityWorkflowId);
        hash = 97 * hash + Objects.hashCode(this.activityTypeId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityWorkflowTypePK other = (ActivityWorkflowTypePK) obj;
        if (!Objects.equals(this.activityWorkflowId, other.activityWorkflowId)) {
            return false;
        }
        return Objects.equals(this.activityTypeId, other.activityTypeId);
    }

    @Override
    public String toString() {
        return "ActivityWorkflowTypePK{" + "activityWorkflowId=" + activityWorkflowId + ", activityTypeId=" + activityTypeId + '}';
    }
}
