/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ActivityTypeResolutionPK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long activityResolutionId;
    private Long activityTypeId;

    public ActivityTypeResolutionPK(Long activityResolutionId, Long activityTypeId) {
        this.activityResolutionId = activityResolutionId;
        this.activityTypeId = activityTypeId;
    }

    public ActivityTypeResolutionPK() {
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "activity_resolution_id")
    public Long getActivityResolutionId() {
        return activityResolutionId;
    }

    public void setActivityResolutionId(Long activityResolutionId) {
        this.activityResolutionId = activityResolutionId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "activity_type_id")
    public Long getActivityTypeId() {
        return activityTypeId;
    }

    public void setActivityTypeId(Long activityTypeId) {
        this.activityTypeId = activityTypeId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.activityResolutionId);
        hash = 97 * hash + Objects.hashCode(this.activityTypeId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityTypeResolutionPK other = (ActivityTypeResolutionPK) obj;
        if (!Objects.equals(this.activityResolutionId, other.activityResolutionId)) {
            return false;
        }
        if (!Objects.equals(this.activityTypeId, other.activityTypeId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ActivityTypeResolutionPK{" + "activityResolutionId=" + activityResolutionId + ", activityTypeId=" + activityTypeId + '}';
    }
}
