package qms.activity.core;

import Framework.Action.AuditableEntityAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.List;
import mx.bnext.access.Module;
import qms.access.util.ProfileServicesUtil;
import qms.activity.entity.ActivityType;

/**
 *
 * <AUTHOR>
 */
public class ActivityTypeAction extends AuditableEntityAction {

    private static final long serialVersionUID = 1L;

    private List<ITextHasValue> modules;
    private String serializedScopeList;
    private String serializedScopeVerifier = "";
    private String serializedScopeImplementer = "";

    @Override
    public String execute() throws Exception {
        modules = new ArrayList<>(1);
        modules.add(new TextHasValue(getTag("selectAnOption"), ""));
        modules.add(new TextHasValue(getTag(Module.AUDIT.getKey()), Module.AUDIT.getKey()));
        modules.add(new TextHasValue(getTag(Module.ACTIVITY.getKey()), Module.ACTIVITY.getKey()));
        modules.add(new TextHasValue(getTag(Module.ACTION.getKey()), Module.ACTION.getKey()));
        modules.add(new TextHasValue(getTag(Module.MEETING.getKey()), Module.MEETING.getKey()));
        List<ITextHasValue> scopes = new ProfileServicesUtil().getServicesScopes(getLocale());
        Gson gson = new Gson();
        serializedScopeList = gson.toJson(scopes);
        if (!isNewEntity()) {
            ActivityType activityType = (ActivityType) getEntity();

            List<ITextHasValue> items = new ArrayList<>(1);
            items.add(new TextHasValue(null, activityType.getScopeVerifier().toString()));
            serializedScopeVerifier = gson.toJson(items);

            items.clear();
            items.add(new TextHasValue(null, activityType.getScopeImplementer().toString()));
            serializedScopeImplementer = gson.toJson(items);
        }
        return super.execute();
    }


    public List<ITextHasValue> getModules() {
        return modules;
    }

    public String getSerializedScopeList() {
        return serializedScopeList;
    }
    /**
     * @return the serializedScopeVerifier
     */
    public String getSerializedScopeVerifier() {
        return serializedScopeVerifier;
    }

    /**
     * @param serializedScopeVerifier the serializedScopeVerifier to set
     */
    public void setSerializedScopeVerifier(String serializedScopeVerifier) {
        this.serializedScopeVerifier = serializedScopeVerifier;
    }

    /**
     * @return the serializedScopeImplementer
     */
    public String getSerializedScopeImplementer() {
        return serializedScopeImplementer;
    }

    /**
     * @param serializedScopeImplementer the serializedScopeImplementer to set
     */
    public void setSerializedScopeImplementer(String serializedScopeImplementer) {
        this.serializedScopeImplementer = serializedScopeImplementer;
    }

}
