package qms.activity.core;

import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.ArrayList;
import java.util.List;
import mx.bnext.access.Module;
import qms.activity.logic.ActivityHelper;

/**
 *
 * <AUTHOR>
 */
public class ActivityReportFiltersAction extends DefaultAction {
    
    @Override
    public String execute() throws Exception {
        return super.execute();
    }

    public List<ITextHasValue> getSupportedModules() {
        List<ITextHasValue> supportedModules = new ArrayList<>(3);
        supportedModules.add(new TextHasValue(Utilities.EMPTY_STRING, Utilities.EMPTY_STRING));
        for(Module m : ActivityHelper.SUPPORTED_MODULES) {
            supportedModules.add(new TextHasValue(m.name(), m.getKey()));
        }
        return supportedModules;
    }
    
}
