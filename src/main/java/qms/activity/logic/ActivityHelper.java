package qms.activity.logic;

import DPMS.Mapping.Action;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.PendingHelper;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.core.ActivityOperation;
import qms.activity.core.ActivityUtil;
import qms.activity.dto.ActivityInfoDTO;
import qms.activity.dto.ActivityOperationDTO;
import qms.activity.dto.ActivityPendingDto;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityCategory;
import qms.activity.entity.ActivityObjective;
import qms.activity.entity.ActivityPriority;
import qms.activity.entity.ActivitySource;
import qms.activity.entity.ActivityType;
import qms.activity.mail.ActivityParentDTO;
import qms.activity.pending.imp.ToVerifyDelayed;
import qms.activity.util.CommitmentTask;
import qms.activity.util.IActivity;
import qms.activity.util.IPersistableActivity;
import qms.activity.util.ReportedActivities;
import qms.activity.util.ReportedEntity;
import qms.audit.entity.AuditIndividualActivity;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.core.DynamicFieldHandler;
import qms.custom.dto.DynamicFieldsDTO;
import qms.finding.entity.FindingActivity;
import qms.form.entity.OutstandingSurveysActivity;
import qms.framework.dao.bean.OwnerHelper;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.framework.util.CacheRegion;
import qms.framework.util.LocaleUtil;
import qms.framework.util.TimeUnit;
import qms.meeting.entity.Meeting;
import qms.meeting.entity.MeetingActivity;
import qms.planner.entity.Client;
import qms.planner.entity.Planner;
import qms.planner.entity.PlannerActivity;
import qms.util.ModuleUtil;
import qms.util.QMSException;
import qms.util.interfaces.ICreatedBy;

/**
 *
 * <AUTHOR> Germán Lares Lares
 */
public class ActivityHelper extends PendingHelper {

    private static final Logger LOGGER = getLogger(ActivityHelper.class);
    
    public final static String FILTER_DELAYED = ""
            + " (("
                + " aType.maxOpenTimeUnit = '" + TimeUnit.HOUR + "'"
                + " AND DATEDIFF(hour, c.createdDate, getdate()) > aType.maxOpenTime"
            + " ) OR ("
                + " aType.maxOpenTimeUnit = '" + TimeUnit.DAY + "'"
                + " AND DATEDIFF(day, c.createdDate, getdate()) > aType.maxOpenTime"
            + " ))";
    public final static String FILTER_IN_TIME = ""
            + " (("
                + " aType.maxOpenTimeUnit = '" + TimeUnit.HOUR + "'"
                + " AND DATEDIFF(hour, c.createdDate, getdate()) < aType.maxOpenTime"
            + " ) OR ("
                + " aType.maxOpenTimeUnit = '" + TimeUnit.DAY + "'"
                + " AND DATEDIFF(day, c.createdDate, getdate()) < aType.maxOpenTime"
            + " ))";
    public final static String FILTER_NOTIFY_IMPLEMENT = ""
            + " (("
                + " aType.notifyImplementTimeUnit = '" + TimeUnit.HOUR + "'"
                + " AND DATEDIFF(hour, getdate(), c.commitmentDate) < aType.notifyImplementTime"
            + " ) OR ("
                + " aType.notifyImplementTimeUnit = '" + TimeUnit.DAY + "'"
                + " AND DATEDIFF(day, getdate(), c.commitmentDate) < aType.notifyImplementTime"
            + " ))"
            + " AND c.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
            + "";
    public final static String FILTER_NOTIFY_VERIFY = ""
            + " (("
                + " aType.notifyVerifyTimeUnit = '" + TimeUnit.HOUR + "'"
                + " AND DATEDIFF(hour, getdate(), c.commitmentDate) < aType.notifyVerifyTime"
            + " ) OR ("
                + " aType.notifyVerifyTimeUnit = '" + TimeUnit.DAY + "'"
                + " AND DATEDIFF(day, getdate(), c.commitmentDate) < aType.notifyVerifyTime"
            + " AND c.commitmentTask = " + CommitmentTask.VERIFICATION.getValue()
            + " ))";


    public static final Module[] SUPPORTED_MODULES = new Module[]{Module.ACTIVITY, Module.AUDIT, Module.ACTION};
    
    private static final String ACTIVITY_PARTICIPANT = ""
            + " SELECT new Map(c.implementer, c.verifier) "
            + " FROM " + Activity.class.getCanonicalName() + " c"
            + " WHERE c.id = :id";
   
    private static final String ACTIVITY_VERIFIER = ""
            + " SELECT c.verifier.id "
            + " FROM " + Activity.class.getCanonicalName() + " c"
            + " WHERE c.id = :id";

    private static final String ACTIVITY_IMPLEMENTER = ""
            + " SELECT c.implementer.id "
            + " FROM " + Activity.class.getCanonicalName() + " c"
            + " WHERE c.id = :id";

    public ActivityHelper(IUntypedDAO dao) {
        super(dao);
    }
    
    private Integer getDefaultTimeoutSeconds() {
        final Integer queryTimeoutSeconds = Utilities.getSettings().getConnQueryTimeout();
        return queryTimeoutSeconds;
    }

    public Set<UserRef> getParticipants(ActivityInfoDTO ent, Long loggedUserId) {
        final Set<UserRef> participants = getImplementers(ent)
            .stream()
            .filter(implementer -> !loggedUserId.equals(implementer.getId()))
            .collect(Collectors.toSet());
        if (ent.getVerifierUser() != null && !Objects.equals(loggedUserId, ent.getVerifierUser())){
           participants.add(getVerifier(ent));
        }
        if (!Objects.equals(loggedUserId, ent.getCreatedBy())) {
            final UserRef author = getAuthor(ent);
            if (User.STATUS.ACTIVE.getValue().equals(author.getStatus()) && !participants.contains(author)) {
                participants.add(author);
            }
        }
        return participants;
    }

    public String getPlannerDescription(Long plannerId) {
        return dao.HQL_findSimpleString(""
                + " SELECT projDes.description"
                + " FROM " + Planner.class.getCanonicalName() + " c"
                + " JOIN c.activity projDes "
                + " WHERE c.id = :id", 
                ImmutableMap.of("id", plannerId),
                true,
                CacheRegion.PLANNER,
                0
        );   
    }
    
    public String getClientDescription(Long clientId) {
        return dao.HQL_findSimpleString(""
            + " SELECT c.description"
            + " FROM " + Client.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id", clientId);   
    }
    
    public String getActivityDescription(Long activityId) {
        return dao.HQL_findSimpleString(""
                + " SELECT c.description"
                + " FROM " + Activity.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );   
    }

    public UserRef getUser(final Long userId) {
        final UserRef user = dao.HQLT_findById(UserRef.class, userId);
        return user;
    }
    
    public UserRef getVerifier(ActivityInfoDTO activity) {
        return new UserRef(
            activity.getVerifierUser(),
            activity.getVerifierLogin(),
            activity.getVerifierUserName(),
            activity.getVerifierUserMail(),
            activity.getVerifierVersion()
        );
    }
    
    public UserRef getAuthor(ICreatedBy activity) {
        return dao.HQLT_findById(UserRef.class, activity.getCreatedBy());
    }
    
    /**
     * return the current activity implementer
     *
     * @param ent finding in which we want to know who is the activity
     * implementer
     * @return Set of UserRef of activity implementer
     */
    public Set<UserRef> getImplementers(IActivity ent) {
        final Long implementer = ent.getImplementerOwnerId();
        if (implementer == null || implementer == -1) {
            return Utilities.EMPTY_SET;
        }
        return getOwnerUsers(implementer);
    }

    /**
     * return the current activity implementer
     *
     * @param ent finding in which we want to know who is the activity
     * implementer
     * @return Set of UserRef of activity implementer
     */
    public Set<UserRef> getImplementers(ActivityInfoDTO ent) {
        final Long implementer = ent.getImplementer();
        if (implementer == null || implementer == -1) {
            return Utilities.EMPTY_SET;
        }
        return getOwnerUsers(implementer);
    }

    /**
     * return the current activity implementer
     *
     * @param ent Activity in which we want to know who is the verifier
     * @return List of mail of finding implementer
     */
    public Set<UserRef> getVerifiers(IActivity ent) {
        final Long verifier = ent.getVerifierOwnerId();
        if (verifier == null || verifier == -1) {
            return Utilities.EMPTY_SET;
        }
        return getOwnerUsers(verifier);
    }

    /**
     * return the current activity implementer
     *
     * @param ent Activity in which we want to know who is the verifier
     * @return List of mail of finding implementer
     */
    public Set<UserRef> getVerifiers(ActivityInfoDTO ent) {
        final Long verifier = ent.getVerifier();
        if (verifier == null || verifier == -1) {
            return Utilities.EMPTY_SET;
        }
        return getOwnerUsers(verifier);
    }

    /**
     * Used to get the module of the activity
     *
     * @param reportedEntity
     * @return the module of the activity
     */
    public Module getModule(ReportedEntity reportedEntity) {
        if (reportedEntity.getSource() != null && reportedEntity.getSource().getModule() != null) {
            return reportedEntity.getSource().getModule();
        }
        if (reportedEntity.getImplementation() != null) {
            return getModule(reportedEntity.getImplementation());
        }
        if (reportedEntity.getVerification() != null) {
            return getModule(reportedEntity.getVerification());
        }
        return getModule(reportedEntity.getBase());
    }

    /**
     * Used to get the module of the activity
     *
     * @param activity
     * @return the module of the activity
     */
    public Module getModule(IActivity activity) {
        if (activity == null) {
            LOGGER.error("Null activity! module cannot be obtained from activity, default value used: Module.ACTIVITY.", new RuntimeException());
            return Module.ACTIVITY;
        }
        if (activity.getType() != null && activity.getTypeId() == null) {
            activity.setTypeId(activity.getType().getId());
        }
        Module m = getModule(dao, new ActivityType(activity.getTypeId()));
        return m;
    }
    
    /**
     * Used to get the module of the activity
     *
     * @param activity
     * @return the module of the activity
     */
    public Module getModule(IPersistableActivity activity) {
        if (activity == null) {
            LOGGER.error("Null activity! module cannot be obtanined from activity, default id used: Module.ACTIVITY.", new RuntimeException());
            return Module.ACTIVITY;
        }
        Module m = getModule(dao, new ActivityType(activity.getTypeId()));
        return m;
    }
    
    
    /**
     * Used to get the module of the activity
     *
     * @param activity
     * @return the module of the activity
     */
    public Module getModule(ActivityInfoDTO activity) {
        final Module m = getModule(dao, activity.getType());
        return m;
    }

    public Module getModule(Long activityId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", activityId);
        final Long activityTypeId =  dao.HQL_findLong(""
                + " SELECT t.id"
                + " FROM " + Activity.class.getCanonicalName() + " c "
                + " JOIN c.type t "
                + " WHERE c.id = :id", params,
                true,
                CacheRegion.ACTIVITY,
                getDefaultTimeoutSeconds()
        );
        Module m = getModule(dao, activityTypeId);
        return m;
    }

    public Module getModuleByActivityId(Long activityId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", activityId);
        Module m = getModule(dao, dao.HQL_findLong(""
                + " SELECT t.id"
                + " FROM " + Activity.class.getCanonicalName() + " c "
                + " JOIN c.type t "
                + " WHERE c.id = :id", 
                params,
                true,
                CacheRegion.ACTIVITY,
                getDefaultTimeoutSeconds()
        ));
        return m;
    }

    public Long getVerificationIdByRecurrenceId(Long recurrenceId) {
        return dao.HQL_findLong(""
                + " SELECT c.id"
                + " FROM " + Activity.class.getCanonicalName() + " c "
                + " WHERE c.recurrenceId = :id"
                + " AND c.commitmentTask = " + CommitmentTask.VERIFICATION.getValue(),
                ImmutableMap.of("id", recurrenceId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
    }

    
    public Module getModule(IUntypedDAO dao, ActivityType type) {
        Module m = null;
        if (type == null) {
            LOGGER.error("Null type! module cannot be obtanined from activity, default is used: Module.ACTIVITY.", new RuntimeException());
            return Module.ACTIVITY;
        }
        if (type.getModule() != null) {
            try {
                m = ModuleUtil.fromKey(type.getModule());
            } catch (Exception e) {
                LOGGER.error("Invalid module at activity [1] {}", new Object[]{
                    type, e
                });
            }
        }
        if (m == null) {
            m = getModule(dao, type.getId());
        }
        if (m == null) {
            LOGGER.error("Invalid module at activity [3] {}", new Object[]{
                type
            });
            return Module.ACTIVITY;
        }
        return m;
    }

    public Module getModule(IUntypedDAO dao, Long activityTypeId) {
        if (activityTypeId == null) {
            return null;
        }
        Module m = null;
        try {
            final Map<String, Object> params = new HashMap<>(1);
            params.put("id", activityTypeId);
            m = ModuleUtil.fromKey(dao.HQL_findSimpleString(""
                + " SELECT c.module"
                + " FROM " + ActivityType.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                params, 
                true,
                CacheRegion.CATALOGS_ACTIVITY,
                getDefaultTimeoutSeconds()
            ));
        } catch (Exception e) {
            LOGGER.error("Invalid module at activity [2] activityTypeId: {}", new Object[]{
                activityTypeId, e
            });
        }
        return m;
    }

    /**
     * Used to get the pending type ids of the supported activities
     *
     * @return List with the id of the pending types
     */
    public List<Long> getAllActivitiesTypeIds() {
        List<Long> typeIds = new ArrayList<>();
        for (Module module : SUPPORTED_MODULES) {
            for (ActivityOperation operation : ActivityOperation.values()) {
                final Long typeId = getTypeId(module, operation);
                if (typeId != null) {
                    typeIds.add(typeId);
                }
            }
        }
        return typeIds;
    }

    /**
     * Used to get the pending type ids of the activities by module
     *
     * @param module Module
     * @return List with the id of the pending types
     */
    public List<Long> getTypeIds(Module module) {
        List<Long> typeIds = new ArrayList<>();
        for (ActivityOperation operation : ActivityOperation.values()) {
            final Long typeId = getTypeId(module, operation);
            if (typeId != null) {
                typeIds.add(getTypeId(module, operation));
            }
        }
        return typeIds;
    }

    /**
     * Used to get the type id of pending to assign
     *
     * @param module Module of activity
     * @param operation Operation
     * @return PendingType Id of activity to assign
     */
    public Long getTypeId(Module module, ActivityOperation operation) {
        switch (operation) {
            case COMPLETE:
                switch (module) {
                    case PLANNER:
                        return getTypeId(APE.PLANNER_ACTIVITY_TO_COMPLETE);
                    case FORMULARIE:
                        return getTypeId(APE.FORM_ACTIVITY_TO_COMPLETE);
                    case AUDIT:
                        return getTypeId(APE.AUDIT_ACTIVITY_TO_COMPLETE);
                    case ACTION:
                        return getTypeId(APE.FINDING_ACTIVITY_TO_COMPLETE);
                    case MEETING:
                        return getTypeId(APE.MEETING_ACTIVITY_TO_COMPLETE);
                    case ACTIVITY:
                        return getTypeId(APE.ACTIVITY_TO_COMPLETE);
                }
                break;
            case VERIFY:
                switch (module) {
                    case AUDIT:
                        return getTypeId(APE.AUDIT_ACTIVITY_TO_VERIFY);
                    case FORMULARIE:
                        return getTypeId(APE.FORM_ACTIVITY_TO_VERIFY);
                    case ACTION:
                        return getTypeId(APE.FINDING_ACTIVITY_TO_VERIFY);
                    case MEETING:
                        return getTypeId(APE.MEETING_ACTIVITY_TO_VERIFY);
                    case ACTIVITY:
                        return getTypeId(APE.ACTIVITY_TO_VERIFY);
                }
                break;
            case VERIFY_DELAYED:
                switch (module) {
                    case AUDIT:
                        return getTypeId(APE.AUDIT_ACTIVITY_TO_VERIFY_DELAYED);
                    case FORMULARIE:
                        return getTypeId(APE.FORM_ACTIVITY_TO_VERIFY_DELAYED);
                    case ACTION:
                        return getTypeId(APE.FINDING_ACTIVITY_TO_VERIFY_DELAYED);
                    case MEETING:
                        return getTypeId(APE.MEETING_ACTIVITY_TO_VERIFY_DELAYED);
                    case ACTIVITY:
                        return getTypeId(APE.ACTIVITY_TO_VERIFY_DELAYED);
                }
                break;
            case VERIFY_NOT_APPLY:
                switch (module) {
                    case AUDIT:
                        return getTypeId(APE.AUDIT_ACTIVITY_TO_VERIFY_NOT_APPLY);
                    case FORMULARIE:
                        return getTypeId(APE.FORM_ACTIVITY_TO_VERIFY_NOT_APPLY);
                    case ACTION:
                        return getTypeId(APE.FINDING_ACTIVITY_TO_VERIFY_NOT_APPLY);
                    case MEETING:
                        return getTypeId(APE.MEETING_ACTIVITY_TO_VERIFY_NOT_APPLY);
                    case ACTIVITY:
                        return getTypeId(APE.ACTIVITY_TO_VERIFY_NOT_APPLY);
                }
        }
        return null;
    }

    public String getSourceDescription(IActivity activity) {
        String query = ""
                + " SELECT c.description "
                + " FROM " + ActivitySource.class.getCanonicalName() + " c "
                + " WHERE c.id = :id";
        return dao.HQL_findSimpleString(query, ImmutableMap.of("id", activity.getSourceId()), true, CacheRegion.CATALOGS_ACTIVITY, 0);
    }
    
    public String getPriorityDescription(IActivity activity) {
        String query = ""
                + " SELECT c.description "
                + " FROM " + ActivityPriority.class.getCanonicalName() + " c "
                + " WHERE c.id = :id";
        return dao.HQL_findSimpleString(query, ImmutableMap.of("id", activity.getPriorityId()), true, CacheRegion.CATALOGS_ACTIVITY, 0);
    }
    
    public List<ActivityInfoDTO> getCancelledActivitiesByDocument(final Long documentId, final LoggedUser loggedUser) {
        final List<Long> actiivtyIds = dao.HQL_findByQuery(""
                + " SELECT c.id "
                + " FROM " + Activity.class.getCanonicalName() + " c "
                + " WHERE c.status IN (" 
                        + Activity.STATUS.REPORTED.getValue()
                        + "," + Activity.STATUS.RETURNED.getValue()
                        + "," + Activity.STATUS.IN_PROCESS.getValue()
                    + ")"
                + " AND c.formId = :documentId"
                + " AND c.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                + " AND c.deleted = 0",
                ImmutableMap.of("documentId", documentId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
        final List<ActivityInfoDTO> activities = new ArrayList<>(actiivtyIds.size());
        final IActivityDAO activityDao = dao.getBean(IActivityDAO.class);
        actiivtyIds.forEach(activityId -> {
            activities.add(activityDao.getActivityInfoFromId(activityId, loggedUser));
        });
        return activities;
    }

    public String getObjectiveDescription(IActivity activity) {
        String query = ""
                + " SELECT c.description "
                + " FROM " + ActivityObjective.class.getCanonicalName() + " c "
                + " WHERE c.id = :id";
        return dao.HQL_findSimpleString(query, ImmutableMap.of("id", activity.getObjectiveId()), true, CacheRegion.CATALOGS_ACTIVITY, 0);
    }
    
    public String getCategoryDescription(IActivity activity) {
        String query = ""
                + " SELECT c.description "
                + " FROM " + ActivityCategory.class.getCanonicalName() + " c "
                + " WHERE c.id = :id";
        return dao.HQL_findSimpleString(query, ImmutableMap.of("id", activity.getCategoryId()), true, CacheRegion.CATALOGS_ACTIVITY, 0);
    }
    
    public Long getActivityIdOfSurvey(Long outstandingSurveyId) {
        final Long activityId =  dao.HQL_findLong(""
                + " SELECT c.id"
                + " FROM " + Activity.class.getCanonicalName() + " c"
                + " WHERE c.outstandingSurveyId = :id"
                + " AND c.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue(),
                ImmutableMap.of("id", outstandingSurveyId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
        return activityId;
    }
    
    public Integer getActivityFormFillIsRequested(Long activityId) {
        return dao.HQL_findSimpleInteger(""
                + " SELECT c.formFillIsRequested"
                + " FROM " + Activity.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", 
                ImmutableMap.of("id", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
    }
    
    public Integer getActivityFormFillIsClosed(Long activityId) {
        return dao.HQL_findSimpleInteger(""
                + " SELECT c.formFillIsClosed"
                + " FROM " + Activity.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
    }

    public String getOutstandingSurveyCode(Long outstandingSurveyId) {
        return dao.HQL_findSimpleString(""
                + " SELECT c.code"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", outstandingSurveyId),
                false,
                null,
                0
        );
    }

    public boolean isActivityParticipant(ActivityInfoDTO activity, Long loggedUserId) {
        OwnerHelper helper = new OwnerHelper(dao);
        return helper.hasOwnerUser(activity.getImplementer(), loggedUserId)
                || helper.hasOwnerUser(activity.getVerifier(), loggedUserId);
    }

    public boolean isActivityParticipant(Long activityId, Long loggedUserId) {
        OwnerHelper helper = new OwnerHelper(dao);
        Map<String, Long> users = dao.HQL_findSimpleMap(
                ACTIVITY_PARTICIPANT, 
                ImmutableMap.of("id", activityId),
                true,
                CacheRegion.PLANNER,
                0
        );
        return helper.hasOwnerUser(users.get("implementer"), loggedUserId)
                || helper.hasOwnerUser(users.get("verifier"), loggedUserId);
    }

    public boolean isActivityVerifier(Long activityId, Long loggedUserId) {
        OwnerHelper helper = new OwnerHelper(dao);
        Long verifier = dao.HQL_findLong(
                ACTIVITY_VERIFIER, 
                ImmutableMap.of("id", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
        return helper.hasOwnerUser(verifier, loggedUserId);
    }

    public boolean isActivityImplementer(Long activityId, Long loggedUserId) {
        OwnerHelper helper = new OwnerHelper(dao);
        Long implementer = dao.HQL_findLong(
                ACTIVITY_IMPLEMENTER, 
                ImmutableMap.of("id", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );
        return helper.hasOwnerUser(implementer, loggedUserId);
    }

    public ActivityParentDTO getParentDataActivity(Module module, Long activityId) {
        String hql;
        switch (module) {
            case AUDIT:
                hql = ""
                    + " SELECT"
                        + " new " + ActivityParentDTO.class.getCanonicalName() + "("
                            + " au.id,"
                            + " au.code,"
                            + " au.description,"
                            + " t.maxOpenTimeUnit"
                        + " ) "
                    + " FROM "
                        + AuditIndividualActivity.class.getCanonicalName() + " aui "
                        + " CROSS JOIN " + AuditIndividual.class.getCanonicalName() + " au "
                        + " CROSS JOIN " + Activity.class.getCanonicalName() + " ac "
                    + " JOIN ac.type t "
                    + " WHERE"
                        + " ac.id = aui.id.activityId"
                        + " AND aui.id.auditIndividualId = au.id"
                        + " AND ac.id = :id"
                ;
                break;
            case FORMULARIE:
                hql = ""
                    + " SELECT"
                        + " new " + ActivityParentDTO.class.getCanonicalName() + "("
                            + " au.id,"
                            + " au.code,"
                            + " au.code,"
                            + " t.maxOpenTimeUnit,"
                            + " au.requestId"
                        + " ) "
                    + " FROM "
                        + OutstandingSurveysActivity.class.getCanonicalName() + " aui "
                        + " CROSS JOIN " + OutstandingSurveys.class.getCanonicalName() + " au "
                        + " CROSS JOIN " + Activity.class.getCanonicalName() + " ac "
                    + " JOIN ac.type t "
                    + " WHERE"
                        + " ac.id = aui.id.activityId"
                        + " AND aui.id.outstandingSurveysId = au.id"
                        + " AND ac.id = :id"
                ;
                break;
            case PLANNER:
                hql = ""
                    + " SELECT"
                        + " new " + ActivityParentDTO.class.getCanonicalName() + "("
                            + " p.id,"
                            + " p.code,"
                            + " i.description,"
                            + " t.maxOpenTimeUnit,"
                            + " p.startDate"
                        + " ) "
                    + " FROM "
                        + PlannerActivity.class.getCanonicalName() + " pa "
                        + " CROSS JOIN " + Planner.class.getCanonicalName() + " p "
                        + " CROSS JOIN " + Activity.class.getCanonicalName() + " ac "
                    + " LEFT JOIN ac.type t "
                    + " LEFT JOIN p.activity a "
                    + " LEFT JOIN a.events i "
                    + " WHERE"
                        + " ac.id = pa.id.activityId"
                        + " AND pa.id.plannerId = p.id"
                        + " AND ac.id = :id"
                        + " AND i.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                ;
                break;
            case ACTION:
                hql = ""
                    + " SELECT"
                        + " new " + ActivityParentDTO.class.getCanonicalName() + "("
                            + " act.id,"
                            + " act.code,"
                            + " act.situacion,"
                            + " t.maxOpenTimeUnit"
                        + " ) "
                    + " FROM "
                        + FindingActivity.class.getCanonicalName() + " fa "
                        + " CROSS JOIN " + Action.class.getCanonicalName() + " act "
                        + " CROSS JOIN " + Activity.class.getCanonicalName() + " ac "
                    + " JOIN ac.type t "
                    + " WHERE "
                        + " ac.id = fa.id.activityId"
                        + " AND fa.id.findingId = act.id"
                        + " AND ac.id = :id"
                ;
                break;
            case MEETING:
                hql = ""
                    + " SELECT"
                        + " new " + ActivityParentDTO.class.getCanonicalName() + "("
                            + " m.id,"
                            + " m.code,"
                            + " m.description,"
                            + " t.maxOpenTimeUnit"
                        + " ) "
                    + " FROM "
                        + MeetingActivity.class.getCanonicalName() + " ma "
                        + " CROSS JOIN " + Meeting.class.getCanonicalName() + " m "
                        + " CROSS JOIN " + Activity.class.getCanonicalName() + " ac "
                    + " JOIN ac.type t "
                    + " WHERE"
                        + " ac.id = ma.id.activityId"
                        + " AND m.deleted = 0"
                        + " AND m.recurrent = " + IPeriodicEntity.RECURRENT.NO
                        + " AND ma.id.meetingId = m.id"
                        + " AND ac.id = :id"
                    ;
                break;
            default:
                return null;
        }
        return (ActivityParentDTO) dao.HQL_findSimpleObject(hql, "id", activityId, true, CacheRegion.ACTIVITY);
    }

    public ActivityOperationDTO getOperation(Activity.STATUS activityStatus) {
        return getOperation(activityStatus, null, null);
    }

    public ActivityOperationDTO getOperation(Activity.STATUS activityStatus, Long activityId, Module module) {
        Integer operation = ActivityOperation.NEW.getValue();
        String methodName = "getMyActivityAllRows";
        switch(activityStatus) {
            case REPORTED:
            case IN_PROCESS:
            case RETURNED:
                boolean delayed = false;
                if (activityId != null && module != null) {
                    delayed = new ToVerifyDelayed(module, Utilities.getUntypedDAO()).isPendingActive(activityId);
                }
                if (delayed) {
                    operation = ActivityOperation.VERIFY_DELAYED.getValue();
                    methodName = "getActivityCompleteRows";
                } else if (activityStatus == Activity.STATUS.REPORTED){
                    operation = ActivityOperation.COMPLETE.getValue();
                    methodName = "getActivityStartRows";
                } else {
                    operation = ActivityOperation.COMPLETE.getValue();
                    methodName = "getActivityCompleteRows";
                }
                break;
            case IMPLEMENTED:
                operation = ActivityOperation.VERIFY.getValue();
                methodName = "getActivityVerifyRows";
                break;
            case NOT_APPLY:
                operation = ActivityOperation.VERIFY_NOT_APPLY.getValue();
                methodName = "getActivityVerifyNotApplyRows";
                break;
        }
        return new ActivityOperationDTO(operation, methodName);
    }
    
    public String getDynamicFields(Long activityId, Long loggedUserId) throws QMSException {
        IDynamicFieldDAO bean = Utilities.getBean(IDynamicFieldDAO.class);
        Map<String, Object> info = dao.HQL_findSimpleMap(""
            + " SELECT new map("
                + " a.typeId AS activityTypeId,"
                + " a.dynamicTableName AS dynamicTableName"
            + " )"
            + " FROM " + Activity.class.getCanonicalName() + " a "
            + " WHERE a.id = :id",
            ImmutableMap.of("id", activityId),
            true,
            CacheRegion.ACTIVITY,
            0
        );
        if (info.get("activityTypeId") == null || info.get("dynamicTableName") == null) {
            return "<tr style=\"display:none;\"></tr> ";
        }
        DynamicFieldHandler handler = new DynamicFieldHandler(
            ActivityType.class /*dynamicEntity*/, Activity.class /*custom*/, bean
        );
        String dynamicTableName = (String) info.get("dynamicTableName");
        Long activityTypeId = (Long) info.get("activityTypeId");
        final Map<String, Object> params = new HashMap<>();
        Map dynamicFieldData = handler.getDynamicFieldData(activityId, activityTypeId, dynamicTableName, null, params, loggedUserId);
        DynamicFieldsDTO dynamicFieldInfo = bean.getDynamicFields(
                ActivityType.class.getCanonicalName(), 
                null, 
                Activity.class.getCanonicalName(),
                params,
                loggedUserId, 
                activityTypeId
        );
        // Se genera TABLE de estatus
        StringBuilder activityBuilder = new StringBuilder(dynamicFieldData.size() * 400);
        dynamicFieldInfo.getDynamicFields().stream().forEach(field -> {
            String value;
            if (dynamicFieldData.get(field.getName()) == null || dynamicFieldData.get(field.getName()) == "null"){
                value = "-";
            } else {
                value = dynamicFieldData.get(field.getName()).toString();
            }
            activityBuilder.append(""
                + "<tr style=\"width:100%;margin:1px\">"
                    + "<td style=\"width:30%; text-align: right;border-top:solid 1px #DDDDDD;\">"
                        + "<strong>").append(field.getLabel()).append(""
                        + ":</strong>"
                    + "</td>"
                    + "<td style=\"border-top:solid 1px #DDDDDD;\">").append(
                        value).append(""
                    + "</td>"
                + "</tr>"
            );
        });
        return activityBuilder.toString();
    }
    
    private String getImplementationDescription(final ActivityPendingDto pending, final ResourceBundle tags) {
        final Activity.STATUS status = Activity.STATUS.getStatus(pending.getStatus());
        final StringBuilder result = new StringBuilder(pending.getCode());
        switch (status) {
            case VERIFIED:
            case NOT_APPLY_VERIFIED:
            case UNDONE_VERIFIED:
                result
                        .append(", ")
                        .append(LocaleUtil.getTag("entity_status." + pending.getStatus(), tags))
                        .append(".");
                break;
            default:
                result
                        .append(", ")
                        .append(LocaleUtil.getTag("entity_status." + pending.getStatus(), tags))
                        .append(", ")
                        .append(LocaleUtil.getTag("unverified", tags))
                        .append(".");
                break;
        }
        final String commitmentDetails = LocaleUtil.getTag("commitmentDetails", tags)
                    .replace("{commitmentDate}", Utilities.formatDate(pending.getCommitmentDate()));
        result.append(commitmentDetails);
        return result.toString();
    }
    
    public String getImplementations(
            final ActivityInfoDTO activity, 
            final ResourceBundle tags,
            final ILoggedUser loggedUser
    ) {
        final List<ActivityPendingDto> implementations = ActivityUtil.getVerificationPendings(
                loggedUser, activity.getPlannedVerificationActivityId(), dao
        );
        final List<String> descriptions = implementations.stream()
            .map(imp -> getImplementationDescription(imp, tags))
            .collect(Collectors.toList());
        return ""
            + "<table>" 
                + "<tbody>" 
                    + "<tr>"
                        + "<td>"
                        + StringUtils.join(descriptions, "</td></tr><tr><td>") 
                        + "</td>"
                    + "<tr>"
                + "</tbody>"
            + "</table>";
    }
    
    public String getVerificationCode(final ActivityInfoDTO activity) {
        return dao.HQL_findSimpleString(""
                + " SELECT c.code"
                + " FROM " + Activity.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", activity.getPlannedVerificationActivityId()),
                true,                
                CacheRegion.ACTIVITY,
                0
        );
    }
    public Set<Long> getActivityReportedIds(List<ReportedActivities> reportedEntities) {
        final List<ReportedEntity> entities = reportedEntities.stream()
                .map((reportedEntity) -> reportedEntity.getEntity())
                .collect(Collectors.toList());
        return getActivityEntityIds(entities);
    }

    private Set<Long> getActivityEntityIds(List<ReportedEntity> reportedEntities) {
        final Set<Long> result = new HashSet<>();
        reportedEntities.forEach((reportedEntity) -> {
            if (reportedEntity.getImplementation() != null) {
                final IActivity implementation = reportedEntity.getImplementation();
                final Long implementationActivityId = implementation.getId();
                result.add(implementationActivityId);
            }
            if (reportedEntity.getVerification() != null) {
                final IActivity verification = reportedEntity.getVerification();
                final Long verificationActivityId = verification.getId();
                result.add(verificationActivityId);
            }
            if (reportedEntity.getChilds() != null && !reportedEntity.getChilds().isEmpty()) {
                result.addAll(getActivityEntityIds(reportedEntity.getChilds()));
            }
        });
        return result;
    }

}
