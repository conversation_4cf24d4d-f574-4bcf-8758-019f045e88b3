package qms.activity.periodicity;

import DPMS.Mapping.Periodicity;
import Framework.Config.PagedQuery;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.PendingMailerType;
import java.util.Date;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.core.ActivityUtil;
import qms.activity.dto.ActivityLinkedDto;
import qms.activity.entity.Activity;
import qms.activity.util.CommitmentTask;
import qms.activity.util.IActivityEvent;
import qms.activity.util.IActivitySystemLink;
import qms.activity.util.IActivityType;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.periodicity.util.PeriodicityMode;
import qms.framework.util.CacheRegion;
import qms.framework.util.MeasureTime;
import qms.util.QMSException;
import qms.util.interfaces.IPagedQuery;

/**
 *
 * <AUTHOR>
 * @param <TYPE> TYPE class of the entity
 */
public class ActivityVerificationManager<TYPE extends IActivityEvent<? extends IActivitySystemLink>> extends ActivityEventManager<TYPE> {

    private final static Double RECORDS_PER_PAGE = 100.0;
    
    public ActivityVerificationManager(
            final PeriodicityMode mode, 
            final Class<TYPE> mainPersistenceClazz,
            final Class<? extends IActivityEvent> codePersistenceClazz,
            final CacheRegion cacheRegion
    ) {
        super(mode, mainPersistenceClazz, codePersistenceClazz, cacheRegion);
    }
    
    public ActivityVerificationManager(
            PeriodicityMode mode, 
            final Class<TYPE> mainPersistenceClazz,
            final CacheRegion cacheRegion
    ) {
        super(mode, mainPersistenceClazz, cacheRegion);
    }
    
    @Override
    public String getPeriodicityFieldName() {
        return "verificationPeriodicity";
    }

    @Override
    public String getNextDateFieldName() {
        return "nextVerificationDate";
    }
    
    @Override
    public String getCustomEventsFilter(final String lastEntityAlias) {
        return lastEntityAlias + ".commitmentTask = " + CommitmentTask.VERIFICATION.getValue();
        }

    @Override
    protected Integer getActiveStatus() {
        return Activity.STATUS.MAIN_OPEN.getValue();
    }      
    
    @Override
    protected void updateParent(final TYPE activityVerification, final TYPE activityRecurrence) {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class);
        final ILoggedUser createdBy = getLoggedUserById(activityRecurrence.getCreatedBy());
        dao.refreshActivityImplementationCount(activityVerification.getId(), null, createdBy);
        dao.refreshRecurrenceCounts(activityRecurrence.getId(), activityRecurrence.getCreatedBy());
        activityRecurrence.setNextVerificationDate(activityVerification.getStartVerificationOn());
        dao.makePersistent(activityVerification, activityRecurrence.getCreatedBy());
    }
    
    @Override
    public TYPE customizeRecurrenceInstance(
            final IUntypedDAO dao,
            final TYPE recurrence,
            final TYPE parentEvent
    )  throws QMSException {
        /**
         * Fue necesario hacer "override" de este metodo para
         * limpiar valores propios de la periodicidad de "implementación
         */
        recurrence.setPlannedImplementationDate(null);
        recurrence.setStartImplementationOn(null);
        recurrence.setFinishImplementationOn(null);
        // se coloca en proceso por que ya hay implementaciones en espera de llenarse
        recurrence.setStatus(Activity.STATUS.REPORTED.getValue());
        resetPeriodicityFromEntity(recurrence);
        Date commitmentDate = recurrence.getCommitmentDate();
        // Se sumán los días limite para verificar
        if (recurrence.getDaysToVerify() != null && recurrence.getDaysToVerify() > 0) {
            final Date finishVerification = Utilities.sumarFechasDias(commitmentDate,
                    recurrence.getDaysToVerify()
            );
            recurrence.setCommitmentDate(finishVerification);
            recurrence.setPlannedVerificationDate(finishVerification);
            recurrence.setStartVerificationOn(commitmentDate);
            recurrence.setFinishVerificationOn(finishVerification);
        } else {
            recurrence.setPlannedVerificationDate(commitmentDate);
            recurrence.setStartVerificationOn(commitmentDate);
            recurrence.setFinishVerificationOn(commitmentDate);
        }
        saveOwners(parentEvent, recurrence, dao);
        
        saveDynamicData(recurrence, parentEvent);
        final IActivityType type = getType(recurrence);
        if (IActivityType.isVerificationAvailable(type)) {
            // Se restan los días para notificar antes de la fecha de verificación, avisos anticipados
            Date notice = ActivityUtil.getNotificationDateTime(
                commitmentDate,
                type.getNotifyVerifyTime().intValue(),
                type.getNotifyVerifyTimeUnit(),
                PendingMailerType.NOTICE
            );
            recurrence.setNotice(notice);
        }

        // Se suman los días para notificar despues de la fecha de verificación, deadline
        Date openImplementation = ActivityUtil.getNotificationDateTime(
            commitmentDate,
            type.getMaxOpenTime().intValue(),
            type.getMaxOpenTimeUnit(),
            PendingMailerType.DEADLINE
        );
        recurrence.setDeadline(openImplementation);
        
        if (recurrence.getAnticipationAttendDays() != null && recurrence.getAnticipationAttendDays() > 0) {
            // Se suman los días de AnticipationAttendDays para notificar despues de la fecha de implementación, reminder
            Date reminderImplementation = ActivityUtil.getNotificationDateTime(
                recurrence.getCommitmentDate(),
                recurrence.getAnticipationAttendDays(),
                null,
                PendingMailerType.REMINDER
            );
            recurrence.setReminder(reminderImplementation);
        } else {
            recurrence.setReminder(recurrence.getCommitmentDate());
        }
        recurrence.setActualHours(0D);
        return recurrence;
    }

    @Override
    public TYPE saveNewEvent(final TYPE activity, final TYPE parentEvent) throws QMSException {
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class);
        final ILoggedUser createdBy = getLoggedUserById(parentEvent.getCreatedBy());
        final ActivityLinkedDto linkedData = getLinkedData(activity, parentEvent, dao, createdBy);
        return (TYPE) dao.saveVerificactionEvent(activity, linkedData, createdBy);
    }

    @Override
    protected Periodicity getPeriodicityFromEntity(final TYPE entity, final IUntypedDAO dao) {
        return entity.getVerificationPeriodicity();
    }
    
    @Override
    protected Integer getCommitmentTaskRecurrenceValue() {
        return CommitmentTask.VERIFICATION.getValue();
    }

    @Override
    protected Integer getRecurrencesCreationStatus() {
        return Activity.STATUS.REPORTED.getValue();
    }
    
    @Override
    protected Date getStartOnFromEntity(TYPE entity) {
        if (entity.getDaysToVerify() != null && entity.getDaysToVerify() > 0) {
            return entity.getStartImplementationOn();
        } else {
            return entity.getStartVerificationOn();
        }
    }

    @Override
    protected Date getEndOnFromEntity(TYPE entity) {
        return entity.getFinishVerificationOn();
    }

    public Long deleteVerificationWithoutImplementations(final Module module, @Nonnull final ILoggedUser loggedUser) {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        final IActivityDAO dao = Utilities.getBean(IActivityDAO.class);
        final Long count = dao.countVerificationWithoutImplementations(module, loggedUser);
        if (count == null) {
            return 0l;
        }
        final Double numberPages = Math.ceil(count > RECORDS_PER_PAGE ? count / RECORDS_PER_PAGE : 1.0);
        Long updatedRows = 0l;
        final IPagedQuery page = new PagedQuery();
        page.setPageSize(RECORDS_PER_PAGE.intValue());
        for (Integer currentPage = 0; currentPage < numberPages; currentPage++) {
            final ElapsedDataDTO pStart = MeasureTime.start(getClass());
            page.setPage(currentPage);
            updatedRows += dao.deleteVerificationWithoutImplementations(module, page, loggedUser).size();
            MeasureTime.stop(
                    pStart,
                    "Elapsed time for delete verifications without implementations in module " + module + ". Page " + currentPage + " of " + numberPages
            );
        }
        MeasureTime.stop(
                tStart, 
                "Elapsed time for delete verifications without implementations in module " + module
        );
        return updatedRows;        
    }


}
