package qms.activity.periodicity;

import DPMS.Mapping.Periodicity;
import Framework.Config.Language;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.util.ApeConstants;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.core.ActivityStage;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityHistory;
import qms.activity.util.CommitmentTask;
import qms.activity.util.IPersistableActivity;
import qms.framework.periodicity.core.PeriodicityPersistenceManager;
import qms.framework.periodicity.core.PeriodicityScheduler;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.util.CacheRegion;
import qms.util.EntityCommon;
import qms.util.QMSException;

@Language(module = "qms.activity.periodicity.ActivityPeriodicityScheduler")
public class ActivityPeriodicityScheduler<T extends IPeriodicEntity> extends PeriodicityScheduler<T> {

    public ActivityPeriodicityScheduler(Class<T> mainPersistenceClazz, CacheRegion cacheRegion) {
        super(mainPersistenceClazz, cacheRegion);
    }

    public ActivityPeriodicityScheduler(
            Class<T> mainPersistenceClazz, 
            Class<? extends IPersistableActivity> codePersistenceClazz,
            CacheRegion cacheRegion
    ) {
        super(mainPersistenceClazz, codePersistenceClazz, cacheRegion);
    }

    @Override
    protected T loadEvent(Long eventId, IUntypedDAO dao) {
        // Se carga por un constructor de Activity, como si fuera DTO
        return (T)dao.getBean(IActivityDAO.class).loadSaveActvityById(eventId);
    }
    
    @Override
    public Integer closeMainEvents(final IUntypedDAO dao, final PeriodicityPersistenceManager<T> manager) throws QMSException {
        String HQL = "SELECT new map(mainActivity.id AS id, pImp.endDate AS implementationEndDate, pVer.endDate AS verificationEndDate, "
                + " TRUNC(max(recurrenceImp.createdDate)) AS lastestImpRecurrence, "
                + " TRUNC(max(recurrenceVer.createdDate)) AS lastestVerRecurrence, "
                + " CASE "
                //+ " -- Ambos deben de haber concluido "
                + " WHEN pImp.endDate IS NOT NULL AND pVer.endDate IS NOT NULL "
                + " THEN CASE WHEN TRUNC(max(recurrenceImp.createdDate)) = TRUNC(pImp.endDate) AND TRUNC(max(recurrenceVer.createdDate)) = TRUNC(pVer.endDate) THEN 1 ELSE 0 END "
                //+ " -- Solo existe IMP "
                + " WHEN pImp.endDate IS NOT NULL AND pVer.endDate IS NULL "
                + " THEN CASE WHEN TRUNC(max(recurrenceImp.createdDate)) = TRUNC(pImp.endDate) THEN 1 ELSE 0 END "
                //+ " -- Solo existe VER "
                + " WHEN pVer.endDate IS NOT NULL AND pImp.endDate IS NULL "
                + " THEN CASE WHEN TRUNC(max(recurrenceVer.createdDate)) = TRUNC(pVer.endDate) THEN 1 ELSE 0 END "
                + " ELSE 0 END AS finished) "
                + " FROM " + Activity.class.getCanonicalName() + " mainActivity "
                + " LEFT JOIN " + Activity.class.getCanonicalName() + " recurrenceImp on recurrenceImp.recurrenceId = mainActivity.id and recurrenceImp.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                + " LEFT JOIN " + Activity.class.getCanonicalName() + " recurrenceVer on recurrenceVer.recurrenceId = mainActivity.id and recurrenceVer.commitmentTask = " + CommitmentTask.VERIFICATION.getValue()
                + " LEFT JOIN " + Periodicity.class.getCanonicalName() + " pImp on pImp.id = mainActivity.implementationPeriodicityId "
                + " LEFT JOIN " + Periodicity.class.getCanonicalName() + "  pVer on pVer.id = mainActivity.verificationPeriodicityId "
                + " WHERE mainActivity.deleted = 0 "
                + " AND mainActivity.status = " + Activity.STATUS.MAIN_OPEN.getValue()
                + " AND mainActivity.belongSeries = " + IPeriodicEntity.BELONG_SERIES.YES
                + " AND mainActivity.recurrent = " + IPeriodicEntity.RECURRENT.YES
                + " AND ISNULL(pVer.vchtipoperiodicidad, 'never') <> '" + Periodicity.NEVER + "'"
                + " AND ISNULL(pImp.vchtipoperiodicidad, 'never') <> '" + Periodicity.NEVER + "'"
                + " GROUP BY mainActivity.id, pImp.endDate, pVer.endDate";
        final List<Map<String, Object>> mainRecurrences = dao.HQL_findByQuery(HQL);
        List<Long> ids = mainRecurrences.stream().filter(rec -> Integer.parseInt(rec.get("finished").toString()) == 1).map(rec -> Long.parseLong(rec.get("id").toString())).collect(Collectors.toList());
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        Integer updatedEvents = 0;
        for (Long id : ids) {
            final String queryCloseEvents = ""
                    + "UPDATE " + Activity.class.getCanonicalName() + " a "
                    + "SET a.status = " + Activity.STATUS.MAIN_CLOSED.getValue() + ", "
                    + " a.lastModifiedDate = :lastModifiedDate "
                    + "WHERE a.id IN (:id)";
            
            Map params = new HashMap();
            params.put("lastModifiedDate", new Date());
            params.put("id", id);
            
            final Integer closeEvent = dao.HQL_updateByQuery(queryCloseEvents, params);
            if (closeEvent > 0) {
                updatedEvents += closeEvent;
            }
            saveHistoryClosedEvent(id, dao);
        }
        return updatedEvents;
    }
    
    private void saveHistoryClosedEvent(final Long activityId, final IUntypedDAO dao) throws QMSException {
        final Long USER_ROOT_ID = SecurityRootUtils.getFirstAdminUserId();
            
        ActivityHistory history = new ActivityHistory(-1L);

        final String historyMessage = getTag("closedMainActivityEvent");

        history.setActivity(new Activity(activityId));
        history.setCode(EntityCommon.getNextCode(history));
        history.setDescription(historyMessage);
        history.setCreatedDate(new Date());
        history.setLastModifiedDate(new Date());
        history.setStage(ActivityStage.NONE.getValue());
        history.setApply(1);
        history.setCreatedBySystem(1);
        history.setFormFillIsRequested(0);
        history.setFormFillIsClosed(0);
        history.setCreatedBy(USER_ROOT_ID);
        history.setUserComment(historyMessage);
        history.setRole(0);
        history.setActivityStatus(Activity.STATUS.MAIN_CLOSED.getValue());

        dao.makePersistent(history, USER_ROOT_ID);
    }

}
