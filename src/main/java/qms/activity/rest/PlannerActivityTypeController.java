package qms.activity.rest;

import Framework.Config.Utilities;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity; 
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.activity.dao.ActivityTypeDTO;
import qms.activity.entity.ActivityType;
import qms.framework.rest.SecurityUtils;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> <PERSON>
 */
@Lazy
@RestController
@RequestMapping("planner/types")
public class PlannerActivityTypeController extends ActivityTypeController {

    private static final String REQUIRED_AUTHORITIES = ""
        + " hasAuthority('IS_ADMIN')"
        + " or hasAuthority('PLANNER_CREATOR')"
    ;

    @Override
    @PreAuthorize(REQUIRED_AUTHORITIES)
    @GetMapping("init-data/{scopeVerifier}")
    public Map all(
        @PathVariable(value = "scopeVerifier", required = true) Integer scopeVerifier
    ) {
        return super.all(scopeVerifier);
    }

    @GetMapping
    @RequestMapping("toggle-system-link/{id}")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    @Override
    public ResponseEntity toogleSystemLinkStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return ActivityTypeController.toogleSystemLinkStatus(id, loggedUser, Utilities.getUntypedDAO());
    }

    @GetMapping
    @RequestMapping("toggle-status/{id}")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    @Override
    public ResponseEntity toogleStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        return super.toogleStatus(id);
    }
    
    @PostMapping()
    @PreAuthorize(REQUIRED_AUTHORITIES)
    @RequestMapping("save")
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<String> save(
        final @RequestBody ActivityTypeDTO dto,
        final HttpServletRequest request
    )  throws QMSException {
        dto.setFillTypes(ActivityType.FILL_TYPE.PLANNER.getValue().toString());
        dto.setModule(Module.PLANNER.getKey());
        return super.save(dto, request);
    }
    
    @PostMapping()
    @RequestMapping("list")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    @Override
    public GridInfo<Map<String, Object>> all(@RequestBody GridFilter filter) {
        if (filter == null) {
            filter = new GridFilter();
        }
        filter.getCriteria().put("<condition>", ""
            + " c.fillTypes = '" + ActivityType.FILL_TYPE.PLANNER.getValue() + "'"
            + " AND c.module = '" + Module.PLANNER.getKey() + "'"
        );
        return super.allPlain(filter);
    }

    @RequestMapping(value = "/{id}")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    @Override
    public ResponseEntity load(@PathVariable(value = "id", required = true) Long id) {
        return super.load(id);
    }

}
