package qms.activity.rest;

import Framework.Config.Utilities;
import bnext.exception.ExplicitRollback;
import bnext.exception.MakePersistentException;
import com.sun.star.auth.InvalidArgumentException;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.entity.ActivityCategory;
import qms.activity.entity.ActivityType;
import qms.activity.entity.ActivityTypeCategory;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.util.EntityCommon;
import qms.util.GridFilter;
import qms.util.QMSException;

public class ActivityCategoryBaseController extends Loggable {
    
    private static final String WORKFLOW_LIST = ""
            + " SELECT new map("
                + " category.id AS category_id "
                + ",category.code AS category_code "
                + ",category.status AS category_status "
                + ",category.description AS category_description "
                + ",category.details AS category_details "
                + ",category.createdDate AS category_createdDate "
                + ",category.lastModifiedDate AS category_lastModifiedDate "
                + ",string_agg(type.description) as type_description"
            + " )"
            + " FROM " + ActivityCategory.class.getCanonicalName() + " category "
            + " LEFT JOIN  " + ActivityTypeCategory.class.getCanonicalName() + " ct"
                + " ON ct.id.activityCategoryId = category.id"
            + " LEFT JOIN  " + ActivityType.class.getCanonicalName() + " type"
                + " ON type.id = ct.id.activityTypeId"
            + " WHERE"
                + " category.deleted = 0"
            + " GROUP BY "
                + " category.id "
                + " ,category.code "
                + " ,category.status "
                + " ,category.description "
                + " ,category.details "
                + " ,category.createdDate "
                + " ,category.lastModifiedDate ";

    @Autowired
    @Qualifier("ActivityDAO")
    private IActivityDAO dao;
    
    protected GridInfo<Map<String, Object>> list(GridFilter filter) {
        return dao.HQL_getRows(new StringBuilder(WORKFLOW_LIST), filter, "category", true, CacheRegion.CATALOGS_ACTIVITY, 0);
    }

    protected ResponseEntity<Map<String, Object>> load(Long id) {
        final ActivityCategory entity = dao.HQLT_findById(ActivityCategory.class, id, true, CacheRegion.CATALOGS_ACTIVITY, 0);
        final Map<String, Object> data = Utilities.entityToMap(entity);
        return ResponseEntity.ok(data);
    }

    protected ResponseEntity<Map<String, Object>> toogleStatus(Long id, ILoggedUser loggedUser) {
        return dao.toggleStatus(ActivityCategory.class, id, true, CacheRegion.CATALOGS_ACTIVITY, 0, loggedUser);
    }

    protected ResponseEntity save(
        ActivityCategory entity, ILoggedUser loggedUser
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        boolean isNew = entity.getId() == null || entity.getId().equals(-1L);
        if (isNew) {
            entity.setId(-1L);
            if (entity.getCode() == null || entity.getCode().isEmpty()) {
                entity.setCode(EntityCommon.getNextCode(entity));
            }
        }
        final ActivityCategory savedEntity = dao.makePersistent(
            entity,
            SecurityUtils.getLoggedUserId()
        );
        if (savedEntity == null) {
            throw new ExplicitRollback("Could't save ActivityCategoryBaseController: " + entity);
        }
        return new ResponseEntity<>(savedEntity.getId(), HttpStatus.OK);
    }

}
