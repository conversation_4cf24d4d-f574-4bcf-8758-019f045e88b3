package qms.activity.rest;

import DPMS.DAOInterface.IDocumentDataExchangeDAO;
import Framework.Config.Utilities;
import com.google.common.collect.ImmutableMap;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.util.Loggable;
import mx.bnext.qms.configuration.rest.FilesController;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.dto.ActivityBulkFileDTO;
import qms.activity.entity.ActivityType;
import qms.framework.bulk.imp.ActivityHandler;
import qms.framework.bulk.util.BulkError;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.AboutApp;
import qms.framework.util.CacheRegion;
import qms.framework.util.CsvParser;
import qms.framework.util.ExcelParser;
import qms.framework.util.SettingsUtil;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.util.QMSException;

@Controller
@RequestMapping("activities-bulk-upload")
public class ActivitiesBulkUploadController extends FilesController {
    
    private static final Logger LOGGER = Loggable.getLogger(ActivitiesBulkUploadController.class);
    
    private static final List<String> ALLOWED_CONTENT_TYPES = Arrays.asList(
        ExcelParser.EXCEL_2003_CONTENT_TYPE,
        ExcelParser.EXCEL_2007_CONTENT_TYPE,
        CsvParser.CSV_CONTENT_TYPE
    );
    
    private final String HAS_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ACTIVITY_BULK_CREATOR')";

    @Autowired
    @Qualifier("ActivityDAO")
    private IActivityDAO dao;
    
    @PreAuthorize(HAS_ACCESS)
    @RequestMapping(method = RequestMethod.POST, path = "{conditionalFields}")
    public ResponseEntity<Map<String, Object>> handleFileUpload(
            @PathVariable(value = "conditionalFields", required = true) Integer conditionalFields,
            final @RequestParam("file") MultipartFile multipart
    ) throws IllegalStateException, IOException, QMSException, BulkError {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final boolean enableConditionalFields = Objects.equals(conditionalFields, 1);
        final TimesheetDataSourceDto tsDs = dao.getProjectCatalogs(loggedUser);
        final ActivityBulkConfig config = new ActivityBulkConfig(
                AboutApp.getAppName(),
                SettingsUtil.getAppUrl(),
                dao,
                Utilities.getBean(IDocumentDataExchangeDAO.class),
                Utilities.getLocale(),
                tsDs,
                new FileManager()
        );
        final Locale locale = SecurityUtils.getLoggedUserLocaleObject();
        final ActivityHandler handler = new ActivityHandler(locale, config, null, enableConditionalFields, true, loggedUser);
        return handler.uploadTemplate(multipart, loggedUser);
    }
        
    @PreAuthorize(HAS_ACCESS)
    @RequestMapping(value = "{conditionalFields}/{id}")
    public ResponseEntity<Void> load(
            final HttpServletResponse response,
            final @PathVariable(value = "conditionalFields") Integer conditionalFields,
            final @PathVariable(value = "id") Long id
    ) throws QMSException, IOException {
        return loadFile(id, response);
    }

    @PreAuthorize(HAS_ACCESS)
    @RequestMapping(value = "template/{activityTypeId}")
    public ResponseEntity<Void> template(
            final HttpServletResponse response,
            final @PathVariable(value = "activityTypeId") Long activityTypeId
    ) throws QMSException {
        return writeTemplate(response, activityTypeId);
    }
    
    private ResponseEntity<Void> loadFile(
            final Long id,
            final HttpServletResponse response
    ) throws QMSException, IOException {
        if (id == null || id == -1L) {
            return writeTemplate(response, null);
        } else {
            final ActivityBulkFileDTO bulkFile = dao.getBulkUpload(id);
            return super.load(response, bulkFile.getResultFileId());
        }
    }

    private ResponseEntity<Void> writeTemplate(
            final HttpServletResponse response,
            final Long activityTypeId
    ) throws QMSException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final TimesheetDataSourceDto tsDs = dao.getProjectCatalogs(SecurityUtils.getLoggedUser());
        final ActivityBulkConfig config = new ActivityBulkConfig(
                AboutApp.getAppName(),
                SettingsUtil.getAppUrl(),
                dao,
                dao.getBean(IDocumentDataExchangeDAO.class),
                Utilities.getLocale(),
                tsDs,
                new FileManager()
        );
        final String activityTypeDescription;
        if (activityTypeId != null && activityTypeId > 0) {
            activityTypeDescription = dao.HQL_findSimpleString(" "
                + " SELECT c.description "
                + " FROM "+ ActivityType.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", activityTypeId), true, CacheRegion.CATALOGS_ACTIVITY, 0
            );
        } else {
            activityTypeDescription = "";
        }
        final String filename = config.getTags().getTag("templateName") + "_" + activityTypeDescription;
        final Locale locale = SecurityUtils.getLoggedUserLocaleObject();
        final ActivityHandler uploader = new ActivityHandler(locale, config, activityTypeId, false, true, loggedUser);
        uploader.downloadTemplate(response, filename, loggedUser);
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }
}
