package qms.activity.rest;

import Framework.Config.Utilities;
import bnext.exception.ExplicitRollback;
import bnext.exception.MakePersistentException;
import com.google.common.collect.ImmutableMap;
import com.sun.star.auth.InvalidArgumentException;
import java.util.List;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.core.ActivityWorkflowLinkedItem;
import qms.activity.dto.ActivityTypeFilllTypeDto;
import qms.activity.entity.ActivityType;
import qms.activity.entity.ActivityWorkflow;
import qms.activity.entity.ActivityWorkflowType;
import qms.framework.core.LinkedManager;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.util.EntityCommon;
import qms.util.GridFilter;
import qms.util.LinkedCompositeConfiguration;
import qms.util.QMSException;

public class ActivityWorkflowBaseController extends Loggable {
    
    private static final String WORKFLOW_LIST = ""
            + " SELECT new map("
                + " workflow.id AS workflow_id "
                + ",workflow.code AS workflow_code "
                + ",workflow.status AS workflow_status "
                + ",workflow.description AS workflow_description "
                + ",workflow.details AS workflow_details "
                + ",string_agg(type.description) as type_description"
                + ",workflow.createdDate AS workflow_createdDate "
                + ",workflow.lastModifiedDate AS workflow_lastModifiedDate "
                + ",workflow.iconName AS workflow_iconName "
            + " )"
            + " FROM " + ActivityWorkflow.class.getCanonicalName() + " workflow "
            + " LEFT JOIN  " + ActivityWorkflowType.class.getCanonicalName() + " wt"
                + " ON wt.id.activityWorkflowId = workflow.id"
            + " LEFT JOIN  " + ActivityType.class.getCanonicalName() + " type"
                + " ON type.id = wt.id.activityTypeId"
            + " WHERE"
                + " workflow.deleted = 0"
            + " GROUP BY "
                + " workflow.id "
                + " ,workflow.code "
                + " ,workflow.status "
                + " ,workflow.description "
                + " ,workflow.details "
                + " ,workflow.createdDate "
                + " ,workflow.lastModifiedDate "
                + " ,workflow.iconName ";

    @Autowired
    @Qualifier("ActivityDAO")
    private IActivityDAO dao;
    
    protected GridInfo<Map<String, Object>> list(GridFilter filter) {
        return dao.HQL_getRows(new StringBuilder(WORKFLOW_LIST), filter, "workflow", true, CacheRegion.CATALOGS_ACTIVITY, 0);
    }

    protected ResponseEntity<Map<String, Object>> load(Long id) {
        final ActivityWorkflow entity = dao.HQLT_findById(ActivityWorkflow.class, id, true, CacheRegion.CATALOGS_ACTIVITY, 0);
        final Map<String, Object> data = Utilities.entityToMap(entity);
        final LinkedCompositeConfiguration linkedConfig = ActivityWorkflowLinkedItem.newInstance();
        data.put("linked", linkedConfig.getGround(dao).getAllValues(SecurityUtils.getLoggedUser(), id));
        return ResponseEntity.ok(data);
    }

    protected ResponseEntity<Map<String, Object>> toogleStatus(Long id, ILoggedUser loggedUser) {
        return dao.toggleStatus(ActivityWorkflow.class, id, true, CacheRegion.CATALOGS_ACTIVITY, 0, loggedUser);
    }

    protected ResponseEntity save(
        ActivityWorkflow entity, ILoggedUser loggedUser
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        boolean isNew = entity.getId() == null || entity.getId().equals(-1L);
        if (isNew) {
            entity.setId(-1L);
            if (entity.getCode() == null || entity.getCode().isEmpty()) {
                entity.setCode(EntityCommon.getNextCode(entity));
            }
        }
        final LinkedCompositeConfiguration linkedConfig = ActivityWorkflowLinkedItem.newInstance();
        final LinkedManager linkedManager = new LinkedManager(
            linkedConfig,
            entity.getLinked(),
            SecurityUtils.getLoggedUserServices()
        );
        final ActivityWorkflow savedEntity = dao.makeCustomPersistent(
            entity,
            true,
            SecurityUtils.getLoggedUser(),
            linkedManager
        );
        if (savedEntity == null) {
            throw new ExplicitRollback("Could't save ActivityWorkflowBaseController: " + entity);
        }
        return new ResponseEntity<>(savedEntity.getId(), HttpStatus.OK);
    }

    protected Map<String, Object> initData(Module module, ILoggedUser loggedUser) {
        final List<ActivityTypeFilllTypeDto> types = dao.getTextValueTypes(module, 0l, null, loggedUser);
        return ImmutableMap.of("types", types);
    }

}
