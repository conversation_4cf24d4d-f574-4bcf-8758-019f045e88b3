package qms.activity.rest;

import DPMS.DAOInterface.IDocumentDAO;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.core.ActivityTypeLinkedItem;
import qms.activity.dao.ActivityTypeDTO;
import qms.activity.dto.ActivityDataSourceDto;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityCategory;
import qms.activity.entity.ActivityObjective;
import qms.activity.entity.ActivityPriority;
import qms.activity.entity.ActivityResolution;
import qms.activity.entity.ActivitySource;
import qms.activity.entity.ActivityType;
import qms.activity.entity.ActivityTypeCategory;
import qms.activity.entity.ActivityTypeConditionalField;
import qms.activity.entity.ActivityTypeDynamicField;
import qms.activity.entity.ActivityTypeResolution;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.core.DynamicFieldHandler;
import qms.custom.dto.DynamicFieldsDTO;
import qms.custom.entity.DynamicField;
import qms.document.dto.DocumentLinkedSelectorDTO;
import qms.framework.core.EntityModelCache;
import qms.framework.dto.ConditionalFieldDTO;
import qms.framework.entity.ConditionalField;
import qms.framework.entity.SystemLink;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.util.GridFilter;
import qms.util.LinkedCompositeConfiguration;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Lazy
@RestController
@RequestMapping("activity-types")
public class ActivityTypeController extends Loggable {

    private static final String REQUIRED_AUTHORITIES = ""
        + " hasAuthority('IS_ADMIN')"
        + " or ("
            + " hasAuthority('ADMON_ACCESOS')"
            + " and hasAuthority('USUARIO_CORPORATIVO')"
        + " )"
        + " or ("
            + " hasAuthority('CATALOGS_ACTIVITIES')"
            + " and hasAuthority('USUARIO_CORPORATIVO')"
        + " )";
    
     private final String HAS_ACCESS = "hasAnyAuthority("
        + "'ACTIVITY_CREATOR', 'ACTIVITY_VIEWER', 'ACTIVITY_MANAGER', 'IS_ADMIN',"
        + "'ACTIVITY_TASK_ONLY', 'ACTIVITY_MODIFY_RESPONSIBLE', "
        + "'ADMON_ACCESOS', 'CATALOGS_ACTIVITIES',"
        + "'ACTIVITY_MODIFY_IMPLEMENTATION', 'ACTIVITY_MODIFY_VERIFIER', "
        + "'ACTIVITY_MODIFY_VERIFICATION', 'ACTIVITY_SUPER_FILLER', 'ACTIVITY_SUPER_VERIFIER', "
        + "'ACTIVITY_SET_APPLY', 'ACTIVITY_MAX_OPEN_TIME_IGNORE', 'ACTIVITY_REMOVE_OBJECT_LINKS', "
        + "'ACTIVITY_MODIFY_DESCRIPTION', 'SPECIAL_ACTIVITY_ATTENDANT', "
        + "'SPECIAL_ACTIVITY_VERIFIER', 'SPECIAL_ACTIVITY_RESPONSIBLE',"
        + "'ACTIVITY_BULK_CREATOR', 'ACTIVITY_REPORTS_USER'"
    + ")";

    @Autowired
    @Qualifier("ActivityDAO")
    private IActivityDAO dao;
    
    @GetMapping
    @RequestMapping("toggle-system-link/{id}")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    public ResponseEntity toogleSystemLinkStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return ActivityTypeController.toogleSystemLinkStatus(id, loggedUser, dao);
    }

    public static ResponseEntity toogleSystemLinkStatus(Long id, ILoggedUser loggedUser, IUntypedDAO dao) {
        Integer status = dao.HQL_findSimpleInteger(""
            + " SELECT t.status"
            + " FROM " + SystemLink.class.getCanonicalName() + " t"
            + " WHERE t.id = :id",
            ImmutableMap.of("id", id),
            true,
            CacheRegion.CATALOGS_ACTIVITY,
            0
        );
        status = status == 1 ? 0 : 1;
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + SystemLink.class.getCanonicalName() + " t "
                + " SET"
                        + " t.status = :status"
                        + ",t.lastModifiedDate = CURRENT_DATE()"
                        + ",t.lastModifiedBy = :user"
                + " WHERE t.id = :id",
                ImmutableMap.of("status", status, "user", loggedUser.getId(), "id", id),
                true,
                CacheRegion.CATALOGS_ACTIVITY,
                0
            ) == 1
        ) {
            return new ResponseEntity<>(
                ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @PreAuthorize(REQUIRED_AUTHORITIES)
    @GetMapping("init-data/{scopeVerifier}")
    public Map<String, Object> all(
            @PathVariable(value = "scopeVerifier", required = true) Integer scopeVerifier
    ) {
        final IDynamicFieldDAO dynamicsDao = Utilities.getBean(IDynamicFieldDAO.class);
        final DynamicFieldsDTO fields = dynamicsDao.getAllValues(true);
        if (!fields.isValid()) {
            getLogger().error("Could not load dynamic fields for activity type init-data.");
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        ActivityDataSourceDto ds = dao.getCatalogTypeDataSource(scopeVerifier, loggedUser);
        TimesheetDataSourceDto tsDs = dao.getProjectCatalogs(loggedUser);
        String nomeclature = "";
        try {
            nomeclature = Utilities.getSerializedObj(EntityModelCache.getMappableAttributes(ActivityType.class), false);
        } catch (QMSException e) {
            getLogger().error("Cannot getMappableAttributes: " + Utilities.getSerializedObj(ActivityType.class));
        }
        final IDocumentDAO docDao = Utilities.getBean(IDocumentDAO.class);
        final List<DocumentLinkedSelectorDTO> forms = docDao.getForms(null, loggedUser).getData();
        final Map<String, Object> data = new HashMap<String, Object>() {
            {
                put("dynamicFields", fields.getDynamicFields());
                put("objectives", ds.getObjectives());
                put("resolutions", ds.getResolutions());
                put("categories", ds.getCategories());
                put("verifiers", ds.getVerifiers());
                put("implementers", ds.getImplementers());
                put("fillForms", forms);
                put("clients", tsDs.getClients());
                put("planners", tsDs.getPlanners());
                put("tasks", tsDs.getTasks());
                put("groupsValues", ds.getGroups());
                put("priorities", ds.getPriorities());
                put("sources", ds.getSources());
                put("businessUnitDepartments", ds.getBusinessUnitDepartments());
                put("preAssignGroupsValues", ds.getPreAssignGroups());
            }
        };
        data.put("nomeclature", nomeclature);
        return data;
    }

    @GetMapping
    @RequestMapping("toggle-status/{id}")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    public ResponseEntity toogleStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        Integer status = dao.HQL_findSimpleInteger(""
            + " SELECT t.status"
            + " FROM " + ActivityType.class.getCanonicalName() + " t"
            + " WHERE t.id = :id",
            ImmutableMap.of("id", id),
            true,
            CacheRegion.CATALOGS_ACTIVITY,
            0
        );
        status = status == 1 ? 0 : 1;
        if (
            dao.HQL_updateByQuery(""
                + " UPDATE " + ActivityType.class.getCanonicalName() + " t "
                + " SET t.status = :status"
                    + ",t.lastModifiedDate = CURRENT_DATE()"
                    + ",t.lastModifiedBy = :user"
                + " WHERE t.id = :id",
                ImmutableMap.of("id", id, "status", status, "user", SecurityUtils.getLoggedUserId())
            ) == 1
        ) {
            return new ResponseEntity<>(
                ImmutableMap.of("newStatus", status), HttpStatus.OK
            );
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @PostMapping()
    @PreAuthorize(REQUIRED_AUTHORITIES)
    @RequestMapping("save")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<String> save(
        final @RequestBody ActivityTypeDTO dto,
        final HttpServletRequest request
    ) throws QMSException {
        try {
            return dao.saveActivityType(dto, request, SecurityUtils.getLoggedUser());
        } catch (DataIntegrityViolationException e) {
            getLogger().error("DataIntegrityViolationException with: " +Utilities.getSerializedObj(dto));
            if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                return new ResponseEntity<>("exist_record", HttpStatus.CONFLICT);
            } 
        }
        return new ResponseEntity<>(HttpStatus.CONFLICT);
    }

    @PostMapping()
    @RequestMapping("list")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    public GridInfo<Map<String, Object>> all(@RequestBody GridFilter filter){
        if (filter == null) {
            filter = new GridFilter();
        }
        filter.getCriteria().put("<condition>", ""
            + " c.fillTypes NOT IN (" 
                + "'" + ActivityType.FILL_TYPE.PLANNER.getValue() + "'"
                + ",'" + ActivityType.FILL_TYPE.PLANNER_TASK.getValue() + "'"
            + " )"
        );
        return this.allPlain(filter);
    }

    protected GridInfo<Map<String, Object>> allPlain(@RequestBody GridFilter filter){
        filter.getField().setOrderBy("id");
        filter.setDirection((byte) 2);
        return Utilities.getUntypedDAO().HQL_getRows(""
            + " SELECT new map("
                + "c.id as id,"
                + "c.status as status,"
                + "c.code as code,"
                + "c.description as description,"
                + "c.module as module,"
                + "c.maxOpenTime as maxOpenTime,"
                + "c.maxOpenTimeUnit as maxOpenTimeUnit,"
                + "c.notifyImplementTime as notifyImplementTime,"
                + "c.notifyImplementTimeUnit as notifyImplementTimeUnit,"
                + "c.dynamicFieldViewName as dynamicFieldViewName,"
                + "c.notifyVerifyTime as notifyVerifyTime,"
                + "c.notifyVerifyTimeUnit as notifyVerifyTimeUnit,"                
                + "c.showGroupName as showGroupName,"
                + "c.showType as showType,"
                + "c.showBusinessUnitDepartment as showBusinessUnitDepartment,"
                + "c.showCode as showCode,"
                + "c.showDescription as showDescription,"
                + "c.showStartDate as showStartDate,"
                + "c.showImplementerUser as showImplementerUser,"
                + "c.showImplementation as showImplementation,"
                + "c.showVerifierUser as showVerifierUser,"
                + "c.showVerification as showVerification,"
                + "c.defaultDaysToVerify as defaultDaysToVerify,"
                + "c.showFillType as showFillType,"
                + "c.showFillForm as showFillForm,"
                + "c.showDaysToVerify as showDaysToVerify,"
                + "c.showDynamicFields as showDynamicFields,"
                + "c.showObjective as showObjective,"
                + "c.showPriority as showPriority,"
                + "c.showSource as showSource,"
                + "c.showImplementationPeriodicity as showImplementationPeriodicity,"
                + "c.showVerificactionPeriodicity as showVerificactionPeriodicity,"
                + "c.showIsPlanned as showIsPlanned,"
                + "c.showPlannedHours as showPlannedHours,"
                + "c.customImplementationDate as customImplementationDate,"
                + "c.verificationType as verificationType,"
                + "c.enablePeriodicity as enablePeriodicity,"
                + "c.authorEditImplementer as authorEditImplementer,"
                + "c.authorEditBusinessUnitDepartment as authorEditBusinessUnitDepartment,"
                + "c.verifierEditBusinessUnitDepartment as verifierEditBusinessUnitDepartment,"
                + "c.implementerEditBusinessUnitDeparment as implementerEditBusinessUnitDeparment,"
                + "c.authorEditDynamicFields as authorEditDynamicFields,"
                + "c.defaultLoggedUserDepartment as defaultLoggedUserDepartment,"
                + "c.mustUpdateImplementationAtReturn as mustUpdateImplementationAtReturn,"
                + "c.followUpImplementationDelay as followUpImplementationDelay,"
                + "c.authorEditVerifier as authorEditVerifier,"
                + "c.authorEditImplementation as authorEditImplementation,"
                + "c.authorEditVerification as authorEditVerification,"
                + "c.authorEditApply as authorEditApply,"
                + "c.authorEditRecurrence as authorEditRecurrence,"
                + "c.authorDeleteRecurrence as authorDeleteRecurrence,"
                + "c.workingHoursAvailable as workingHoursAvailable,"
                + "c.projectsAvailable as projectsAvailable,"
                + "c.addSubtaskByDepartment as addSubtaskByDepartment,"
                + "c.addImplementationOnCreate as addImplementationOnCreate,"
                + "c.allowsUseOnPlanning as allowsUseOnPlanning,"
                + "c.allowsPreAssignment as allowsPreAssignment,"
                + "c.addVerificationOnCreate as addVerificationOnCreate,"
                + "c.verificationReqOnPlanning as verificationReqOnPlanning,"
                + "c.addVerificationAvailable as addVerificationAvailable,"
                + "c.implementerEditApply as implementerEditApply,"
                + "c.verifierEditApply as verifierEditApply,"
                + "c.scopeVerifier as scopeVerifier,"
                + "c.restrictAnticipationAttend as restrictAnticipationAttend,"
                + "c.anticipationAttendDays as anticipationAttendDays,"
                + "c.defaultImplementerAuthor as defaultImplementerAuthor,"
                + "c.defaultVerifierAuthor as defaultVerifierAuthor,"
                + "isnull(ap.description, '') as defaultPriority,"
                + "isnull(ao.description, '') as defaultObjective,"
                + "isnull(ats.description, '') as defaultSource,"
                + "c.fillTypeReport as fillTypeReport,"
                + "c.fillTypeForm as fillTypeForm,"
                + "c.fillTypeDone as fillTypeDone,"
                + "string_agg(dyn.description) as dynamicFieldDescriptions,"
                + "string_agg(res.name) as resolutionDescriptions,"
                + "string_agg(cat.description) as categoryDescriptions,"
                + "c.groupDescriptions as groupDescriptions,"
                + "isnull(max(cf.fieldOrder) + 1, 0) as countConditionalFields,"
                + "c.createdDate as createdDate,"
                + "c.defaultCustomVerifierName as defaultCustomVerifierName,"
                + "c.lastModifiedDate as lastModifiedDate,"
                + "c.defaultIsPlanned as defaultIsPlanned,"
                + "c.defaultPlannedHours as defaultPlannedHours,"
                + "isnull(aty_res.name, '') as defaultResolution,"
                + "c.showActivityOrder as showActivityOrder,"
                + "c.preAssignGroupDescriptions as preAssignGroupDescriptions"
            + " )"
            + " FROM " + ActivityType.class.getCanonicalName() + " c "
            + " LEFT JOIN " + ActivityPriority.class.getCanonicalName() + " ap "
                + " ON c.defaultPriorityId = ap.id "
            + " LEFT JOIN " + ActivityObjective.class.getCanonicalName() + " ao "
                + " ON c.defaultActivityObjectiveId = ao.id "
            + " LEFT JOIN " + ActivitySource.class.getCanonicalName() + " ats "
                + " ON c.defaultActivitySourceId = ats.id "
            + " LEFT JOIN " + ActivityTypeDynamicField.class.getCanonicalName() + " a_dyn"
                + " ON c.id = a_dyn.id.activityTypeId"
            + " LEFT JOIN " + DynamicField.class.getCanonicalName() + " dyn"
                + " ON a_dyn.id.dynamicFieldId = dyn.id"
            + " LEFT JOIN " + ActivityTypeResolution.class.getCanonicalName() + " a_res"
                + " ON c.id = a_res.id.activityTypeId"
            + " LEFT JOIN " + ActivityResolution.class.getCanonicalName() + " res"
                + " ON a_res.id.activityResolutionId = res.id"
            + " LEFT JOIN " + ActivityTypeCategory.class.getCanonicalName() + " a_cat"
                + " ON c.id = a_cat.id.activityTypeId"
            + " LEFT JOIN " + ActivityCategory.class.getCanonicalName() + " cat"
                + " ON a_cat.id.activityCategoryId = cat.id"
            + " LEFT JOIN " + ActivityResolution.class.getCanonicalName() + " aty_res"
                + " ON c.defaultResolutionId = aty_res.id"
            + " LEFT JOIN " + ActivityTypeConditionalField.class.getCanonicalName() + " a_condf"
                + " ON c.id = a_condf.id.activityTypeId"
            + " LEFT JOIN " + ConditionalField.class.getCanonicalName() + " cf"
                + " ON a_condf.id.conditionalFieldId = cf.id"
            + " WHERE "
                + " c.deleted = 0"
            + " GROUP BY" 
                    + " c.id,"
                    + " c.status,"
                    + " c.code,"
                    + " c.description,"
                    + " c.module,"
                    + " c.maxOpenTime,"
                    + " c.maxOpenTimeUnit,"
                    + " c.notifyImplementTime,"
                    + " c.notifyImplementTimeUnit,"
                    + " c.dynamicFieldViewName,"
                    + " c.notifyVerifyTime,"
                    + " c.notifyVerifyTimeUnit,"                
                    + " c.showGroupName,"
                    + " c.showType,"
                    + " c.showBusinessUnitDepartment,"
                    + " c.showCode,"
                    + " c.showDescription,"
                    + " c.showStartDate,"
                    + " c.showImplementerUser,"
                    + " c.showImplementation,"
                    + " c.showVerifierUser,"
                    + " c.showVerification,"
                    + " c.defaultDaysToVerify,"
                    + " c.showFillType,"
                    + " c.showFillForm,"
                    + " c.showDaysToVerify,"
                    + " c.showDynamicFields,"
                    + " c.showObjective,"
                    + " c.showPriority,"
                    + " c.showSource,"
                    + " c.showImplementationPeriodicity,"
                    + " c.showVerificactionPeriodicity,"
                    + " c.showIsPlanned,"
                    + " c.showPlannedHours,"
                    + " c.customImplementationDate,"
                    + " c.verificationType,"
                    + " c.enablePeriodicity,"
                    + " c.authorEditImplementer,"
                    + " c.authorEditBusinessUnitDepartment,"
                    + " c.verifierEditBusinessUnitDepartment,"
                    + " c.implementerEditBusinessUnitDeparment,"
                    + " c.authorEditDynamicFields,"
                    + " c.defaultLoggedUserDepartment,"
                    + " c.mustUpdateImplementationAtReturn,"
                    + " c.followUpImplementationDelay,"
                    + " c.authorEditVerifier,"
                    + " c.authorEditImplementation,"
                    + " c.authorEditVerification,"
                    + " c.authorEditApply,"
                    + " c.authorEditRecurrence,"
                    + " c.authorDeleteRecurrence,"
                    + " c.workingHoursAvailable,"
                    + " c.projectsAvailable,"
                    + " c.addSubtaskByDepartment,"
                    + " c.addImplementationOnCreate,"
                    + " c.allowsUseOnPlanning,"
                    + " c.allowsPreAssignment,"
                    + " c.addVerificationOnCreate,"
                    + " c.verificationReqOnPlanning,"
                    + " c.addVerificationAvailable,"
                    + " c.implementerEditApply,"
                    + " c.groupDescriptions,"
                    + " c.verifierEditApply,"
                    + " c.scopeVerifier,"
                    + " c.restrictAnticipationAttend,"
                    + " c.anticipationAttendDays,"
                    + " c.defaultImplementerAuthor,"
                    + " c.defaultVerifierAuthor,"
                    + " ap.description,"
                    + " ao.description,"
                    + " ats.description,"
                    + " c.fillTypeReport,"
                    + " c.fillTypeForm,"
                    + " c.fillTypeDone,"
                    + " c.createdDate,"
                    + " c.defaultCustomVerifierName,"
                    + " c.lastModifiedDate,"
                    + " c.defaultIsPlanned,"
                    + " c.defaultPlannedHours ,"
                    + " aty_res.name,"
                    + " c.showActivityOrder,"
                    + " c.preAssignGroupDescriptions",
            filter);
    }
    
    @RequestMapping(value = "/{id}")
    @PreAuthorize(REQUIRED_AUTHORITIES)
    public ResponseEntity load(@PathVariable(value = "id", required = true) Long id) {
        ActivityType a = dao.HQLT_findById(ActivityType.class, id, true, CacheRegion.CATALOGS_ACTIVITY, 0);
        if (a == null) {
            getLogger().error("ActivityType not found with id: {}", id);
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        final Map<String, Object> type = Utilities.entityToMap(a);
        final LinkedCompositeConfiguration linkedConfig = ActivityTypeLinkedItem.newInstance();
        // Transformación de boleanos a entero
        type.put("showGroupName", isTrue(a.getShowGroupName() ));
        type.put("showType", isTrue(a.getShowType()));
        type.put("showBusinessUnitDepartment", isTrue(a.getShowBusinessUnitDepartment()));
        type.put("showCode", isTrue(a.getShowCode()));
        type.put("showDescription", isTrue(a.getShowDescription()));
        type.put("showStartDate", isTrue(a.getShowStartDate()));
        type.put("showImplementerUser", isTrue(a.getShowImplementerUser()));
        type.put("showImplementation", isTrue(a.getShowImplementation()));
        type.put("showVerifierUser", isTrue(a.getShowVerifierUser()));     
        type.put("showVerification", isTrue(a.getShowVerification()));
        type.put("showFillType", isTrue(a.getShowFillType()));
        type.put("showFillForm", isTrue(a.getShowFillForm()));
        type.put("showDaysToVerify", isTrue(a.getShowDaysToVerify()));
        type.put("showDynamicFields", a.getShowDynamicFields());
        type.put("showObjective", isTrue(a.getShowObjective()));
        type.put("showPriority", isTrue(a.getShowPriority()));
        type.put("showImplementationPeriodicity", isTrue(a.getShowImplementationPeriodicity()));
        type.put("showVerificactionPeriodicity", isTrue(a.getShowVerificactionPeriodicity()));
        type.put("showSource", isTrue(a.getShowSource()));
        type.put("authorEditImplementer", isTrue(a.getAuthorEditImplementer()));
        type.put("authorEditBusinessUnitDepartment", isTrue(a.getAuthorEditBusinessUnitDepartment()));
        type.put("verifierEditBusinessUnitDepartment", isTrue(a.getVerifierEditBusinessUnitDepartment()));
        type.put("implementerEditBusinessUnitDeparment", isTrue(a.getImplementerEditBusinessUnitDeparment()));
        type.put("followUpImplementationDelay", a.getFollowUpImplementationDelay());
        type.put("mustUpdateImplementationAtReturn", a.getMustUpdateImplementationAtReturn());
        type.put("authorEditVerifier", isTrue(a.getAuthorEditVerifier()));
        type.put("authorEditImplementation", isTrue(a.getAuthorEditImplementation()));
        type.put("authorEditVerification", isTrue(a.getAuthorEditVerification()));
        type.put("authorEditApply", isTrue(a.getAuthorEditApply()));
        type.put("authorEditRecurrence", isTrue(a.getAuthorEditRecurrence()));
        type.put("authorDeleteRecurrence", isTrue(a.getAuthorDeleteRecurrence()));
        type.put("authorEditDynamicFields", isTrue(a.getAuthorEditDynamicFields()));
        type.put("implementerEditDynamicFields", isTrue(a.getImplementerEditDynamicFields()));
        type.put("verifierEditDynamicFields", isTrue(a.getVerifierEditDynamicFields()));
        type.put("implementerEditApply", isTrue(a.getImplementerEditApply()));
        type.put("verifierEditApply", isTrue(a.getVerifierEditApply()));
        type.put("restrictAnticipationAttend", isTrue(a.getRestrictAnticipationAttend()));
        // Campos dinamicos
        type.put("linked", linkedConfig.getGround(dao).getAllValues(SecurityUtils.getLoggedUser(), id));
        type.put("showIsPlanned", a.getShowIsPlanned());
        type.put("defaultIsPlanned", a.getDefaultIsPlanned());
        type.put("showPlannedHours", a.getShowPlannedHours()); 
        type.put("defaultPlannedHours", a.getDefaultPlannedHours());
        type.put("defaultCustomVerifier", a.getDefaultCustomVerifier());
        type.put("dynamicFieldsOrder", dao.getDynamicFielsOrderByActivityType(0L, id));
        return ResponseEntity.ok(type);
    }
    
    private boolean isTrue(Integer value) {
        return value != null && value == 1;
    }

    public static void updateDynamicFields(final Long activityTypeId, final ActivityTypeDTO dto) throws QMSException {
        if (dto.getLinked() == null || dto.getLinked().getDynamicFields() == null || dto.getLinked().getDynamicFields().isEmpty()) {
            return;
        }
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        dto.getLinked().getDynamicFields().forEach(linked -> {
            if (linked.getOrder() != null) {
                try {
                    int updateResult = dao.HQL_updateByQuery(""
                            + " UPDATE " + ActivityTypeDynamicField.class.getCanonicalName() + " atdf "
                            + " SET atdf.orderField = :orderField"
                            + " WHERE atdf.id.dynamicFieldId = :dynamicFieldId"
                            + " AND atdf.id.activityTypeId = :activityTypeId",
                            ImmutableMap.of(
                                    "activityTypeId", activityTypeId,
                                    "dynamicFieldId", linked.getId(),
                                    "orderField", linked.getOrder()
                            ),
                            true,
                            CacheRegion.CATALOGS_ACTIVITY,
                            0
                    );
                    if (updateResult == 0) {
                        throw new Exception("No se pudo actualizar el orden del campo dinamico " + linked.getId() + ", " + activityTypeId);
                    }
                } catch (Exception e) {
                    getLogger(ActivityTypeController.class).error("Cannot update dynamic field order " + e.getMessage());
                }
            }
        });
        final DynamicFieldHandler dynamicFieldHandler = new DynamicFieldHandler(
                ActivityType.class /*dynamicEntity*/, 
                Activity.class /*custom*/, 
                Utilities.getBean(IDynamicFieldDAO.class)
        );
        dynamicFieldHandler.generateDatabaseObjects(activityTypeId, SecurityUtils.getLoggedUser().getId());
    }

    public static void persistConditionalFields(final List<ConditionalFieldDTO> conditionalFields) {
        if (conditionalFields == null || conditionalFields.isEmpty()) {
            return;
        }
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        conditionalFields.stream().forEach((dto) -> persistConditionalField(dto, loggedUserId));
    }

    public static void persistConditionalField(ConditionalFieldDTO dto, final Long loggedUserId) {
        final ConditionalField field;
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        if (dto.getId() == null || Objects.equals(dto.getId(), -1l)) {
            field = new ConditionalField(-1l);
        } else {
            field = dao.HQLT_findById(ConditionalField.class, dto.getId(), true, CacheRegion.CATALOGS_ACTIVITY, 0);
        }
        field.setType(dto.getType());
        field.setFieldOrder(dto.getFieldOrder());
        field.setFieldName(dto.getFieldName());
        field.setValue(dto.getValue());
        final ConditionalField saved = dao.makePersistent(field, loggedUserId);
        dto.setId(saved.getId());
    }
}
