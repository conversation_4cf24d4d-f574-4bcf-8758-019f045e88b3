package qms.activity.rest;

import bnext.exception.MakePersistentException;
import com.sun.star.auth.InvalidArgumentException;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.activity.entity.ActivityWorkflow;
import qms.framework.rest.SecurityUtils;
import qms.util.GridFilter;
import qms.util.QMSException;

@Lazy
@RestController
@RequestMapping("rest/activity-workflow")
public class ActivityWorkflowController extends ActivityWorkflowBaseController {

    private static final String HAS_SAVE_ACCESS = ""
            + " hasAuthority('IS_ADMIN')"
            + " or ("
            + " hasAuthority('ADMON_ACCESOS')"
            + " and hasAuthority('USUARIO_CORPORATIVO')"
            + " )"
            + " or ("
            + " hasAuthority('CATALOGS_ACTIVITIES')"
            + " and hasAuthority('USUARIO_CORPORATIVO')"
            + " )";


    @PostMapping("data-source/list")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GridInfo<Map<String, Object>> list(@RequestBody GridFilter filter) {
        return super.list(filter);
    }


    @GetMapping({"load/{activityWorkflowId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity<Map<String, Object>> load(
            @PathVariable(value = "activityWorkflowId") Long activityWorkflowId
    ) {
        return super.load(activityWorkflowId);
    }

    @GetMapping({"toggle-status/{activityWorkflowId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, Object>> toogleStatus(
            @PathVariable(value = "activityWorkflowId") Long activityWorkflowId
    ) {
        return super.toogleStatus(activityWorkflowId, SecurityUtils.getLoggedUser());
    }

    @PostMapping({"save"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity save(
            @RequestBody ActivityWorkflow entity
    ) throws InvalidArgumentException, MakePersistentException, QMSException {
        return super.save(entity, SecurityUtils.getLoggedUser());
    }

    @GetMapping("init-data")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Object> initData() {
        return super.initData(Module.ACTIVITY, SecurityUtils.getLoggedUser());
    }

}
