package qms.activity.rest;

import Framework.Config.ITextHasValue;
import Framework.Config.UserHasValue;
import Framework.Config.Utilities;
import ape.pending.dto.ActivitiesChangeImplementerDTO;
import ape.pending.dto.ActivityAddPlanSaveDto;
import ape.pending.dto.ReassignVerifyDTO;
import ape.pending.dto.UserPendingCountAndHours;
import bnext.exception.InvalidOptionalDataException;
import bnext.exception.MakePersistentException;
import bnext.login.util.LoginError;
import com.google.common.collect.ImmutableMap;
import com.sun.star.auth.InvalidArgumentException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.cipher.HexUtil;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.access.util.AccessUtil;
import qms.activity.dto.ActivityAddPlanDataSource;
import qms.activity.dto.ActivityAssignDataSourceDTO;
import qms.activity.dto.ActivityDTO;
import qms.activity.dto.ActivityDataSourceDto;
import qms.activity.dto.ActivityDiff;
import qms.activity.dto.ActivityPendingDto;
import qms.activity.dto.ActivityProgressTreeDto;
import qms.activity.dto.ActivityRelationshipDTO;
import qms.activity.dto.ActivityRelationshipItemDTO;
import qms.activity.dto.ActivitySingleLinkedDto;
import qms.activity.dto.ActiviyDetails;
import qms.activity.dto.FileCommentDTO;
import qms.activity.dto.ModifiedFieldRequestDTO;
import qms.activity.dto.ModifiedFieldResponseDTO;
import qms.activity.dto.ModifiedPlannedImplementationDTO;
import qms.activity.dto.ParticipantDto;
import qms.activity.dto.PlanningTabLoadDto;
import qms.activity.dto.PreWorkLoadSaveDTO;
import qms.activity.dto.TextHasUserValue;
import qms.activity.dto.TreeScoreDataDto;
import qms.activity.dto.load.ActivityHistoryDTO;
import qms.activity.entity.Activity;
import qms.activity.entity.AddPlanUserData;
import qms.activity.util.ActivityTreeMetric;
import qms.activity.util.ReportedActivities;
import qms.framework.dto.IEntityDTO;
import qms.framework.dto.SystemLinkDTO;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.SettingsUtil;
import qms.planner.dto.PlannerClientDto;
import qms.planner.dto.PlannerTaskDTO;
import qms.task.dto.IQualifiedMenuItem;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 * <AUTHOR> Guadalupe Quintanilla Flores
 */

@Lazy
@RestController("ActivityController")
@RequestMapping("rest/activities/activity")
public class ActivityController extends ActivityBaseController {
    
    private static final String HAS_ACCESS = "hasAnyAuthority("
        + "'ACTIVITY_CREATOR', 'ACTIVITY_VIEWER', 'ACTIVITY_MANAGER', 'IS_ADMIN',"
        + "'ACTIVITY_TASK_ONLY', 'ACTIVITY_MODIFY_RESPONSIBLE', "
        + "'ACTIVITY_MODIFY_IMPLEMENTATION', 'ACTIVITY_MODIFY_VERIFIER', "
        + "'ACTIVITY_MODIFY_VERIFICATION', 'ACTIVITY_SUPER_FILLER', 'ACTIVITY_SUPER_VERIFIER', "
        + "'ACTIVITY_SET_APPLY', 'ACTIVITY_MAX_OPEN_TIME_IGNORE', 'ACTIVITY_REMOVE_OBJECT_LINKS', "
        + "'ACTIVITY_MODIFY_DESCRIPTION', 'SPECIAL_ACTIVITY_ATTENDANT', "
        + "'SPECIAL_ACTIVITY_VERIFIER', 'SPECIAL_ACTIVITY_RESPONSIBLE',"
        + "'ACTIVITY_BULK_CREATOR', "
        + "'ACTIVITY_REPORTS_ALL', 'ACTIVITY_REPORTS_USER'"
    + ")";

    private static final String HAS_SAVE_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_CREATOR', 'ACTIVITY_BULK_CREATOR')";
    
    private static final String HAS_PLAN_SAVE_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_CREATOR', 'ACTIVITY_BULK_CREATOR') "
            + " OR @ActivityController.hasGroupAccess(#data)";
    
    private static final String HAS_SAVE_MANY_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_CREATOR', 'ACTIVITY_BULK_CREATOR')"
            + " OR ("
                + " hasAnyAuthority('SPECIAL_ACTIVITY_SUBTASK_CREATOR') AND @ActivityController.canCreateActivities(#entities)"
            + ")";
    
    private static final String HAS_DASHBOARD_ACCESS = "hasAnyAuthority('IS_ADMIN', 'ACTIVITY_MANAGER'," 
        + "'ACTIVITY_REPORTS_BUSSINESUNIT', 'ACTIVITY_REPORTS_DEPARTMENT', 'ACTIVITY_REPORTS_USER'"
    + ")";
    
    private static final String HAS_SUPER_CONSOLIDATED_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_SUPER_VERIFIER', 'ACTIVITY_SUPER_FILLER', 'ACTIVITY_VIEWER', 'ACTIVITY_REPORTS_ALL'"
    + ")";
    private static final String HAS_CANCEL_SERIES_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_DELETE_RECURRENCE'"
    + ")";
    private static final String HAS_REMOVE_LINKS_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_REMOVE_OBJECT_LINKS'"
    + ")";
    private static final String HAS_REMOVE_FILE_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_REMOVE_OBJECT_LINKS'"
    + ") OR @ActivityController.canDeleteFileActivity(#activityId, #fileId)";
    private static final String HAS_REMOVE_COMMENT_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_REMOVE_OBJECT_LINKS'"
    + ") OR @ActivityController.canDeleteCommentActivity(#activityId, #commentId)";
    private static final String HAS_REMOVE_DOCUMENT_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_REMOVE_OBJECT_LINKS'"
    + ") OR @ActivityController.canDeleteDocumentActivity(#activityId, #documentId)";
    private static final String HAS_MODIFY_RESPONSIBLE_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RESPONSIBLE', 'ACTIVITY_MODIFY_RECURRENCE'"
    + ")";
    private static final String HAS_MODIFY_VERIFIER_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_VERIFIER', 'ACTIVITY_MODIFY_RECURRENCE'"
    + ") OR @ActivityController.canAuthorEditVerificator(#activityId)";
    private static final String HAS_MODIFY_IMPLEMENTATION_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_IMPLEMENTATION'"
    + ")";
    private static final String HAS_MODIFY_VERIFICATION_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_VERIFICATION'"
    + ")";
    private static final String HAS_MODIFY_DESCRIPTION_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_DESCRIPTION', 'SPECIAL_ACTIVITY_ATTENDANT', 'ACTIVITY_MODIFY_RECURRENCE'"
    + ")";
    private static final String HAS_VIEWER_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_MANAGER', 'ACTIVITY_CREATOR', 'ACTIVITY_VIEWER', 'SPECIAL_ACTIVITY_RESPONSIBLE'"
    + ")";
    private static final String HAS_TASK_ONLY_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_TASK_ONLY'"
    + ")";
    private static final String HAS_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID'"
    + ") OR @ActivityController.canEditBusinessunitDepartment(#activityId)";
    private static final String HAS_MODIFY_IS_PLANNED_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_IS_PLANNED'"
    + ")";
    private static final String HAS_MODIFY_OBJECTIVE_ID_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_OBJECTIVE_ID'"
    + ")";
    private static final String HAS_MODIFY_PLANNED_HOURS_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_PLANNED_HOURS'"
    + ")";
    private static final String HAS_MODIFY_PRIORITY_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_PRIORITY'"
    + ")";
    private static final String HAS_MODIFY_SOURCE_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_SOURCE'"
    + ")";
    private static final String HAS_MODIFY_CATEGORY_ID_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_CATEGORY_ID'"
    + ")";
    private static final String HAS_MODIFY_SYSTEM_LINKS_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE', 'ACTIVITY_MODIFY_SYSTEM_LINKS'"
    + ")";
    private static final String HAS_MODIFY_COMMON_FIELD_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER', 'ACTIVITY_MODIFY_RECURRENCE'"
    + ") OR @ActivityController.isAuthor(#activityId)";
    private static final String HAS_DELETE_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'ACTIVITY_MANAGER'"
    + ") OR @ActivityController.canDeleteActivity(#activityId)";
    private static final String HAS_MODIFY_PRE_RESPONSIBLE_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'ACTIVITY_CREATOR', 'ACTIVITY_MANAGER'"
    + ") OR @ActivityController.canEditPreImplementer(#activityId)";

    @PostMapping("{activityWorkflowId}/list")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Activity> list(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @RequestBody GridFilter filter
    ) {
        boolean managerAccess = (
            !SecurityUtils.isLoggedUserAdmin()
            && !SecurityUtils.hasLoggedUserService(ProfileServices.ACTIVITY_MANAGER)
        );
        return super.list(filter, getModule(), activityWorkflowId, managerAccess);
    }

    @PreAuthorize(HAS_ACCESS)
    @PostMapping("{activityWorkflowId}/mine")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> mine(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @RequestBody GridFilter filter
    ){
        super.handleFilterHasRescheduled(filter);
        return super.mine(filter, getModule(), activityWorkflowId, "mine");
    }
    
    @PreAuthorize(HAS_ACCESS)
    @PostMapping("{activityWorkflowId}/mine/implementation")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> mineImplementation(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @RequestBody GridFilter filter
    ){
        return super.mine(filter, getModule(), activityWorkflowId, "implementations");
    }
    
    @PreAuthorize(HAS_ACCESS)
    @PostMapping("{activityWorkflowId}/mine/verification")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> mineVerification(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @RequestBody GridFilter filter
    ){
        return super.mine(filter, getModule(), activityWorkflowId, "verifications");
    }
    
    @PreAuthorize(HAS_ACCESS)
    @PostMapping("{activityWorkflowId}/mine/preAssigned")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> minePreassigned(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @RequestBody GridFilter filter
    ){
        return super.mine(filter, getModule(), activityWorkflowId, "preAssigned");
    }
    
    @PreAuthorize(HAS_ACCESS)
    @PostMapping("{activityWorkflowId}/deleted")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> deleted(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @RequestBody GridFilter filter
    ){
        return super.allRows(filter, getModule(), activityWorkflowId, true);
    }
    
    @PreAuthorize(HAS_SUPER_CONSOLIDATED_ACCESS)
    @PostMapping("{activityWorkflowId}/consolidated")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> allRows(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @RequestBody GridFilter filter
    ) {
        super.handleFilterHasRescheduled(filter);
        return super.allRows(filter, getModule(), activityWorkflowId, false);
    }

    @PreAuthorize(HAS_ACCESS)
    @PostMapping("available-subtasks/{activityId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<GridInfo<ActivityPendingDto>> availableSubtasks(
        @PathVariable(value = "activityId") Long activityId,
        @RequestBody GridFilter filter
    ) {
        return super.availableSubtasks(getModule(), activityId, filter);
    }
    
    @PreAuthorize(HAS_ACCESS)
    @GetMapping("update-subtask/{childId}/{parentId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity updateSubtasks(
      @PathVariable(value = "childId") Long childId,
      @PathVariable(value = "parentId") Long parentId) {
        return super.updateSubtasks(getModule(), childId, parentId);
    }
    

    @PreAuthorize(HAS_CANCEL_SERIES_ACCESS)
    @GetMapping("cancel-series/{recurrenceId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Integer> cancelSeries(
        @PathVariable(value = "recurrenceId") Long recurrenceId
    ) {
        return super.cancelSeries(recurrenceId, getModule());
    }
    
    @PostMapping({"save-data/save", "save"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ReportedActivities> save(
        final @RequestBody ActivityDTO dto
    ) throws InvalidArgumentException, MakePersistentException {
        return super.save(dto);
    }
    
    @PostMapping({
            "add-plan/activity-list",
            "add-plan/activity-list/{activityTypeIds}",
            "add-plan/activity-list/{activityTypeIds}/{participantIds}"
    })
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> addPlanActivityList(
        @RequestBody GridFilter filter,
        final @PathVariable(value = "activityTypeIds", required = false) Long[] activityTypeIds,
        final @PathVariable(value = "participantIds", required = false) Long[] participantIds
    ) {
        return super.addPlanActivityList(filter, getModule(), activityTypeIds, participantIds);
    }

    @PostMapping("add-plan/save")
    @PreAuthorize(HAS_PLAN_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity addPlanSave(
        @RequestBody ActivityAddPlanSaveDto data
    ) throws InvalidArgumentException, MakePersistentException {
        return super.addPlanSave(data, getModule());
    }
    
    @PostMapping("add-plan/sync/tab-states")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<AddPlanUserData>> syncTabStates(
        @RequestBody List<PlanningTabLoadDto> states
    ) throws InvalidArgumentException, MakePersistentException {
        return super.syncTabStates(states, getModule());
    }
    

    @GetMapping("add-plan/my-tab-states/{activityWorkflowId}")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<PlanningTabLoadDto>> getMyTabStates(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId
    ) {
        return super.getMyTabStates(getModule(), activityWorkflowId); 
    }

    @GetMapping({"user-team/{ownerId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextHasUserValue> userTeamList(
        @PathVariable(value = "ownerId") Long ownerId
    ) {
        return super.userTeamList(ownerId);
    }

    @PostMapping("user-team/workload/{includeCompleted}/{start}/{end}")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserPendingCountAndHours> userTeamWorkload(
            @PathVariable(value = "includeCompleted") boolean includeCompleted,
            @PathVariable(value = "start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
            @PathVariable(value = "end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
            @RequestBody List<Long> userIds
    ) {
        return super.userTeamWorkload(includeCompleted, start, end, userIds);
    }

    @PostMapping("user-team/my-workload/{includeCompleted}/{start}/{end}")
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserPendingCountAndHours> myWorkload(
            @PathVariable(value = "includeCompleted") boolean includeCompleted,
            @PathVariable(value = "start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
            @PathVariable(value = "end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end
    ) {
        return super.myWorkload(includeCompleted, start, end);
    }
   
    @GetMapping("resolutions/{workflowId}/actives")
    @PreAuthorize(HAS_ACCESS)
    @Override
    public List<ITextHasValue> resolutionsActives(final @PathVariable(value = "workflowId") Long workflowId) {
        return super.resolutionsActives(workflowId);
    }
    
    @Override

    @GetMapping("priorities/actives")
    @PreAuthorize(HAS_ACCESS)
    public List<ITextHasValue> prioritiesActives() {
        return super.prioritiesActives();
    }
    
    @Override

    @GetMapping("categories/{workflowId}/actives")
    @PreAuthorize(HAS_ACCESS)
    public List<ITextHasValue> categoriesActives(final @PathVariable(value = "workflowId") Long workflowId) {
        return super.categoriesActives(workflowId);
    }
    
    @Override

    @GetMapping("sources/actives")
    @PreAuthorize(HAS_ACCESS)
    public List<ITextHasValue> sourcesActives() {
        return super.sourcesActives();
    }
    
    @Override

    @GetMapping("objectives/actives")
    @PreAuthorize(HAS_ACCESS)
    public List<ITextHasValue> objectivesActives() {
        return super.objectivesActives();
    }
    
    @Override

    @GetMapping("activity-types/{workflowId}/actives")
    @PreAuthorize(HAS_ACCESS)
    public List<ITextHasValue> activityTypesActives(final @PathVariable(value = "workflowId") Long workflowId) {
        return super.activityTypesAll(workflowId);
    }
    
    @GetMapping({"user-team/data-source/{activityWorkflowId}/{ownerId}", "user-team/data-source/{activityWorkflowId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityAddPlanDataSource addPlanDataSource(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "ownerId", required = false) Long ownerId
    ) {
        return super.addPlanDataSource(activityWorkflowId, ownerId);
    }

    @GetMapping({"workload/data-source/{activityWorkflowId}/{ownerId}", "workload/data-source/{activityWorkflowId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityAddPlanDataSource workloadDataSource(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "ownerId", required = false) Long ownerId
    ) {
        return super.workloadDataSource(activityWorkflowId, ownerId);
    }

    @GetMapping("my-workload/data-source/{activityWorkflowId}")
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityAddPlanDataSource myWorkloadDataSource(
            @PathVariable(value = "activityWorkflowId") Long activityWorkflowId
    ) {
        return super.myWorkloadDataSource(activityWorkflowId);
    }
    
    @PostMapping({"workload/data-source-user/{activityWorkflowId}/{userId}"})
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityAddPlanDataSource addPlanDataSourceUser(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "userId") Long userId
    ) {
        return super.addPlanDataSourceUser(activityWorkflowId, userId);
    }
    
    @PostMapping({"save-data/save-many", "save-many"})
    @PreAuthorize(HAS_SAVE_MANY_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<ReportedActivities>> saveMany(
        final @RequestBody List<ActivityDTO> entities
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveMany(entities);
    }
    
    @PostMapping({"comments/save", "comments/save/{activityId}", "comments/save/{activityId}/{commentId}"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ActivitySingleLinkedDto> saveComment(
        final @PathVariable(value = "activityId", required = false) Long activityId,
        final @PathVariable(value = "commentId", required = false) Long commentId,
        final @RequestBody String comment
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveComment(activityId, commentId, comment);
    }

    @PostMapping({"participants/save/{activityId}"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity saveParticipants(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody List<Long> participants
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveParticipants(activityId, participants);
    }


    @GetMapping({"participants-planner/delete/{activityId}/{removedUserId}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity deleteParticipants(
        final @PathVariable(value = "activityId") Long activityId,
        final @PathVariable(value = "removedUserId") Long removedUserId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.deleteParticipants(activityId, removedUserId, "", null);
    }
    

    @GetMapping({"participants-planner-task/delete/{activityId}/{removedUserId}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity deleteParticipantsPlannerTask(
        final @PathVariable(value = "activityId") Long activityId,
        final @PathVariable(value = "removedUserId") Long removedUserId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.deleteParticipantsPlannerTask(activityId, removedUserId);
    }
    
    @PostMapping({"participants/list", "participants/list/{parentActivityId}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<ParticipantDto> participantsList(
        @PathVariable(value = "parentActivityId", required = false) Long parentActivityId,
        @RequestBody GridFilter filter
    ) {
        return super.participantsList(parentActivityId, filter, getModule());
    }

    @PostMapping({"files/save/{activityId}"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ActivitySingleLinkedDto> addFile(
        final @PathVariable(value = "activityId", required = false) Long activityId,
        final @RequestBody Long fileId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.addFile(activityId, fileId);
    }

    @PostMapping({"files/delete/{activityId}"})
    @PreAuthorize(HAS_REMOVE_FILE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity deleteFile(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody Long fileId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.deleteFile(activityId, fileId);
    }
    
    @PostMapping({"timesheet/files/delete/{activityId}"})
    @PreAuthorize(HAS_REMOVE_LINKS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity deleteFileTimesheet(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody Long fileId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.deleteFileTimesheet(activityId, fileId);
    }

    @PostMapping({"comments/delete/{activityId}"})
    @PreAuthorize(HAS_REMOVE_COMMENT_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity deleteComment(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody Long commentId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.deleteComment(activityId, commentId);
    }

    @PostMapping({"documents/save/{activityId}/{documentId}"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ActivitySingleLinkedDto> saveDocument(
        final @PathVariable(value = "activityId", required = false) Long activityId,
        final @PathVariable(value = "documentId", required = false) Long documentId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveDocument(activityId, documentId);
    }

    @PostMapping({
        "documents/delete/{activityId}/{documentId}",
        "documents/delete/{activityId}/{documentId}/{recurrenceId}"
    })
    @PreAuthorize(HAS_REMOVE_DOCUMENT_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity deleteDocument(
        final @PathVariable(value = "activityId") Long activityId,
        final @PathVariable(value = "documentId", required = false) Long documentId,
        final @PathVariable(value = "recurrenceId", required = false) Long recurrenceId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.deleteDocument(activityId, documentId, recurrenceId);
    }

    @PostMapping("save-field/implementer/{activityId}")
    @PreAuthorize(HAS_MODIFY_RESPONSIBLE_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveImplementer(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> implementer
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveImplementer(activityId, implementer);
    }

    @PostMapping("save-field/verifier/{activityId}")
    @PreAuthorize(HAS_MODIFY_VERIFIER_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveVerifier(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> verifier
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveVerifier(activityId, verifier);
    }
    
    @PostMapping("save-field/implementation/{activityId}")
    @PreAuthorize(HAS_MODIFY_IMPLEMENTATION_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveImplementation(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Date> implementation
    ) throws InvalidArgumentException, MakePersistentException, InvalidOptionalDataException {
        return super.saveImplementation(activityId, implementation);
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @PostMapping("save-field/reminder/{activityId}")
    @PreAuthorize(HAS_MODIFY_IMPLEMENTATION_ACCESS)
    @Override
    public ResponseEntity<ModifiedFieldResponseDTO> saveReminder(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Date> reminder
    ) throws InvalidArgumentException, MakePersistentException, InvalidOptionalDataException {
        return super.saveReminder(activityId, reminder);
    }

    @PostMapping("save-field/verification/{activityId}")
    @PreAuthorize(HAS_MODIFY_VERIFICATION_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveVerification(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Date> verification
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveVerification(activityId, verification);
    }    
    
    @PostMapping("save-field/daysToVerify/{activityId}")
    @PreAuthorize(HAS_MODIFY_VERIFICATION_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveDaysToVerify(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Integer> daysToVerify
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveDaysToVerify(activityId, daysToVerify);
    }
    
    @PostMapping("save-field/businessUnitDepartmentId/{activityId}")
    @PreAuthorize(HAS_MODIFY_BUSINESS_UNIT_DEPARTMENT_ID_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveBusinessUnitDepartmentId(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> businessUnitDepartmentId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveBusinessUnitDepartmentId(activityId, businessUnitDepartmentId);
    }
    
    @PostMapping("save-field/source/{activityId}")
    @PreAuthorize(HAS_MODIFY_SOURCE_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveSource(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> source
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveSource(activityId, source);
    }
    
    @PostMapping("save-field/categoryId/{activityId}")
    @PreAuthorize(HAS_MODIFY_CATEGORY_ID_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveCategory(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> categoryId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveCategory(activityId, categoryId);
    }
    
    @PostMapping("save-field/objectiveId/{activityId}")
    @PreAuthorize(HAS_MODIFY_OBJECTIVE_ID_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveObjectiveId(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> objectiveId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveObjectiveId(activityId, objectiveId);
    }
    
    @PostMapping("save-field/isPlanned/{activityId}")
    @PreAuthorize(HAS_MODIFY_IS_PLANNED_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveIsPlanned(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Boolean> isPlanned
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveIsPlanned(activityId, isPlanned);
    }
    
    @PostMapping("save-field/plannedHours/{activityId}")
    @PreAuthorize(HAS_MODIFY_PLANNED_HOURS_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> savePlannedHours(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Double> plannedHours
    ) throws InvalidArgumentException, MakePersistentException {
        return super.savePlannedHours(activityId, plannedHours);
    }
    
    @PostMapping("save-field/plannerTask/{activityId}")
    @PreAuthorize(HAS_MODIFY_COMMON_FIELD_ACCESS)
    @Override
    public ResponseEntity<ModifiedFieldResponseDTO> savePlannerTask(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<PlannerTaskDTO> plannerTask
    ) throws InvalidArgumentException, MakePersistentException {
        return super.savePlannerTask(activityId, plannerTask);
    }
    
    @PostMapping("save-field/systemLinks/{activityId}")
    @PreAuthorize(HAS_MODIFY_SYSTEM_LINKS_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveSystemLinks(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<List<SystemLinkDTO>> systemLinks
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveSystemLinks(activityId, systemLinks);
    }
    
    @PostMapping("save-field/priority/{activityId}")
    @PreAuthorize(HAS_MODIFY_PRIORITY_FIELD_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> savePriority(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> priority
    ) throws InvalidArgumentException, MakePersistentException {
        return super.savePriority(activityId, priority);
    }
    
    @PostMapping("save-field/description/{activityId}")
    @PreAuthorize(HAS_MODIFY_DESCRIPTION_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> saveDescription(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<String> description
    ) throws InvalidArgumentException, MakePersistentException {
        return super.saveDescription(activityId, description);
    }

    @PreAuthorize(HAS_VIEWER_ACCESS)
    @PostMapping("linked/{name}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<? extends IEntityDTO> linked(
            @PathVariable(value = "name")  String name,
            @RequestBody GridFilter filter
    ) {
        return super.linked(name, filter, SecurityUtils.getLoggedUser());
    }


    @GetMapping({
        "init-data/data-source/{activityWorkflowId}", 
        "init-data/data-source/{activityWorkflowId}/{activityId}", 
        "init-data/data-source/{activityWorkflowId}/{activityId}/{showImplementation}"
    })
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityDataSourceDto dataSource(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "activityId", required = false) Long activityId,
        @PathVariable(value = "showImplementation", required = false) Boolean showImplementation
    ) throws QMSException {
        return super.dataSource(getModule(), activityWorkflowId, activityId, showImplementation);
    }


    @GetMapping({"init-data/users"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> users() {
        return super.users();
    }


    @GetMapping("dashboard/{start}/{end}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ActivityProgressTreeDto> dashboard(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end
    ) {
        return super.dashboard(getModule(), start, end);
    }


    @GetMapping("dashboard/tree/score/{start}/{end}/{activityId}/{parentTreeActivityId}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public TreeScoreDataDto dashboardTreeScore(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("activityId") Long activityId,
        @PathVariable("parentTreeActivityId") Long parentTreeActivityId
    ) {
        return super.dashboardTreeScore(getModule(), activityId, parentTreeActivityId, start, end, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping("dashboard/tree/score/numerator/{start}/{end}/{activityId}/{parentTreeActivityId}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> dashboardTreeScoreNumerator(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("activityId") Long activityId,
        @PathVariable("parentTreeActivityId") Long parentTreeActivityId,
        @RequestBody GridFilter filter
    ) {
        return super.dashboardTreeScoreNumerator(filter, getModule(), activityId, parentTreeActivityId, start, end);
    }
    
    @PostMapping("dashboard/tree/score/denominator/{start}/{end}/{activityId}/{parentTreeActivityId}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> dashboardTreeScoreDenominator(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("activityId") Long activityId,
        @PathVariable("parentTreeActivityId") Long parentTreeActivityId,
        @RequestBody GridFilter filter
    ) {
        return super.dashboardTreeScoreDenominator(filter, getModule(), activityId, parentTreeActivityId, start, end);
    }
   
    @GetMapping({
            "dashboard/implementer/score/{start}/{end}/{implementerId}",
            "dashboard/implementer/{metric}/{start}/{end}/{implementerId}"
    })
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public TreeScoreDataDto dashboardImplementerScore(
        @PathVariable("metric") String metric,
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("implementerId") Long implementerId
    ) {
        return super.dashboardImplementerScore(getModule(), implementerId, start, end, SecurityUtils.getLoggedUser(), ActivityTreeMetric.valueOf(metric.toUpperCase()));
    }
    
    @PostMapping({
            "dashboard/implementer/score/numerator/{start}/{end}/{implementerId}",
            "dashboard/implementer/{metric}/numerator/{start}/{end}/{implementerId}"
    })
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> dashboardImplementerScoreNumerator(
        @PathVariable("metric") String metric,
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("implementerId") Long implementerId,
        @RequestBody GridFilter filter
    ) {
        return super.dashboardSummaryScoreNumerator(filter, getModule(), null, implementerId, start, end, ActivityTreeMetric.valueOf(metric.toUpperCase()));
    }
    
    @PostMapping({
            "dashboard/implementer/score/denominator/{start}/{end}/{implementerId}",
            "dashboard/implementer/{metric}/denominator/{start}/{end}/{implementerId}"
    })
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> dashboardImplementerScoreDenominator(
        @PathVariable("metric") String metric,
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("implementerId") Long implementerId,
        @RequestBody GridFilter filter
    ) {
        return super.dashboardSummaryScoreDenominator(filter, getModule(), null, implementerId, start, end, ActivityTreeMetric.valueOf(metric.toUpperCase()));
    }
    

    @GetMapping("dashboard/week/score/{start}/{end}/{weekNumber}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public TreeScoreDataDto dashboardWeekScore(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("weekNumber") Integer weekNumber
    ) {
        return super.dashboardWeekScore(getModule(), weekNumber, start, end, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping("dashboard/week/score/numerator/{start}/{end}/{weekNumber}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> dashboardWeekScoreNumerator(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("weekNumber") Integer weekNumber,
        @RequestBody GridFilter filter
    ) {
        return super.dashboardSummaryScoreNumerator(filter, getModule(), weekNumber, null, start, end, ActivityTreeMetric.valueOf("SCORE"));
    }
    
    @PostMapping("dashboard/week/score/denominator/{start}/{end}/{weekNumber}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> dashboardWeekScoreDenominator(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable("weekNumber") Integer weekNumber,
        @RequestBody GridFilter filter
    ) {
        return super.dashboardSummaryScoreDenominator(filter, getModule(), weekNumber, null, start, end, ActivityTreeMetric.valueOf("SCORE"));
    }


    @GetMapping("evaluation-dashboard/{start}/{end}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ActivityProgressTreeDto> evaluationDashboard(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end
    ) {
        return super.evaluationDashboard(getModule(), start, end);
    }
    
    @PostMapping("owner-dashboard/{start}/{end}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> ownerDashboard(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @RequestBody GridFilter filter
    ) {
        return super.ownerDashboard(getModule(), start, end, filter);
    }   
    

    @GetMapping("planning-owner-dashboard/{start}/{end}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ActivityProgressTreeDto> planningOwnerDashboard(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end
    ) {
        return super.planningOwnerDashboard(getModule(), start, end);
    }
    
    @GetMapping({"{linkedSelector:documents|files|comments|swapActivities}/data-source/{activityId}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<? extends IEntityDTO> linkedSelectorDataSource(
        @PathVariable(value = "linkedSelector") String linkedSelector,
        @PathVariable(value = "activityId") Long activityId
    ) {
        return super.linkedSelectorDataSource(linkedSelector, activityId, SecurityUtils.getLoggedUser());
    }
    

    @PreAuthorize(HAS_VIEWER_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @GetMapping(DATA_SOURCE_ACTIVITY_BY_TYPE) // data-source/type/{typeId}/{businessUnitDepartmentId}
    public ActivityDataSourceDto dataSourceActivityByType(
            @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
            @PathVariable(value = "typeId") Long typeId,
            @PathVariable(value = "businessUnitDepartmentId") Long businessUnitDepartmentId
    ) throws QMSException {
        return dataSourceByType(getModule(), activityWorkflowId, typeId, businessUnitDepartmentId, null);
    }
    

    @GetMapping({INIT_DATA_SOURCE_ACTIVITY_BY_TYPE})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityDataSourceDto dataSourceType(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "type", required = false) Long typeId,
        @PathVariable(value = "businessUnitDepartmentId", required = false) Long businessUnitDepartmentId
    ) throws QMSException {
        return super.dataSourceType(getModule(), activityWorkflowId, typeId, businessUnitDepartmentId, null);
    }
    

    @GetMapping({"timesheet/data-source"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public TimesheetDataSourceDto timesheetDataSource() {
        return super.timesheetDataSource();
    }


    @GetMapping({INIT_DATA_SOURCE_ACTIVITY_BY_TYPE_IMPLEMENTATION})
    // init-data/data-source/implementation/type/{activityWorkflowId}/{type}
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityDataSourceDto dataSourceTypeImplementation(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "type", required = false) Long typeId
    ) throws QMSException {
        return super.dataSourceTypeImplementation(getModule(), activityWorkflowId, typeId);
    }


    @GetMapping({INIT_DATA_SOURCE_ACTIVITY_BY_TYPE_VERIFICATION})
    // "init-data/data-source/verification/type/{activityWorkflowId}/{type}"
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityDataSourceDto dataSourceTypeVerification(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "type", required = false) Long typeId
    ) throws QMSException {
        return super.dataSourceTypeVerification(getModule(), activityWorkflowId, typeId);
    }


    @PreAuthorize(HAS_VIEWER_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @GetMapping(DATA_SOURCE_ACTIVITY_BY_TYPE_IMPLEMENTATION)
    // data-source/implementation/type/{typeId}/{businessUnitDepartmentId}
    public ActivityDataSourceDto dataSourceActivityByTypeImplementation(
            @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
            @PathVariable(value = "typeId") Long typeId,
            @PathVariable(value = "businessUnitDepartmentId") Long businessUnitDepartmentId
    ) throws QMSException{
        return dataSourceByTypeImplementation(getModule(), activityWorkflowId, typeId, businessUnitDepartmentId);
    }
    

    @PreAuthorize(HAS_VIEWER_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @GetMapping(DATA_SOURCE_ACTIVITY_BY_TYPE_VERIFICATION)
    // data-source/verification/type/{activityWorkflowId}/{typeId}/{businessUnitDepartmentId}
    public ActivityDataSourceDto dataSourceActivityByTypeVerification(
            @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
            @PathVariable(value = "typeId") Long typeId,
            @PathVariable(value = "businessUnitDepartmentId") Long businessUnitDepartmentId
    ) throws QMSException {
        return dataSourceByTypeVerification(getModule(), activityWorkflowId, typeId, businessUnitDepartmentId);
    }
    

    @PreAuthorize(HAS_VIEWER_ACCESS)
    @GetMapping("history/{activityId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity<List<ActivityHistoryDTO>> history(@PathVariable(value = "activityId") Long activityId) {
        return super.history(activityId);
    }


    @PreAuthorize(HAS_ACCESS)
    @GetMapping("implementation/{activityId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity<List<ActivityPendingDto>> implementation(@PathVariable(value = "activityId") Long activityId) {
        return super.implementation(activityId);
    }
    

    @PreAuthorize(HAS_TASK_ONLY_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @GetMapping("activity-task-only")
    public ResponseEntity<List<IQualifiedMenuItem>> formList() {
        List<IQualifiedMenuItem> result = new ArrayList<>();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @GetMapping("activity-favorite-templates")
    public ResponseEntity<List<IQualifiedMenuItem>> favoriteTemplates() {
        List<IQualifiedMenuItem> result = super.favoriteTemplates(getModule());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
    
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @PostMapping({"commitments-summary/{start}/{end}", "commitments-summary/{userId}/{start}/{end}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> commitmentsSummary(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @PathVariable(value = "userId", required = false) Long userId,
        @RequestBody GridFilter filter
    ) {
        return super.getCommitmentsSummaryRows(start, end, userId, getModule(), filter);
    }
    
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @PostMapping("score-query/{start}/{end}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> scoreQueryData(
        @PathVariable("start") @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
        @PathVariable("end") @DateTimeFormat(pattern="yyyy-MM-dd") Date end,
        @RequestBody GridFilter filter
    ) {
        return super.getScoreQueryData(start, end, getModule(), filter);
    }
    
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @PostMapping("timesheet/{start}/{end}/{activityId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> timesheetByRange(
        @PathVariable("start") Long start,
        @PathVariable("end") Long end,
        @PathVariable("activityId") Long activityId,
        @RequestBody GridFilter filter
    ) {
        Date startDate = new Date(start);
        Date endDate = new Date(end);

        return super.timesheetByRange(filter, activityId, startDate, endDate);
    }
    
    @Override
    public Module getActivityModule() {
        return getModule();
    }

    private Module getModule() {
        return Module.ACTIVITY;
    }
    

    @PreAuthorize(HAS_ACCESS)
    @GetMapping({"users-commitments-summary/{userId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> dataSourceCommitmentsWidget(
        @PathVariable(value = "userId", required = false) Long userId
    ) {
        return super.userCommitmentsWidget(userId);
    }
    

    @PreAuthorize(HAS_ACCESS)
    @GetMapping({"dataSource-commitments-summary/{startDate}/{endDate}/{userId}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Object> dataSourceCommitmentsWidget(
        @PathVariable(value = "startDate", required = false) String startDate,
        @PathVariable(value = "endDate", required = false) String endDate,
        @PathVariable(value = "userId", required = false) Long userId
    ) {
         if (startDate == null || endDate == null) {
           return null;
        }
        Date start = Utilities.formatStrToDate(startDate, "yyyy-MM-dd");
        Date end = Utilities.formatStrToDate(endDate, "yyyy-MM-dd");
        
        if (start == null || end == null) {
            throw new RuntimeException("Input date '" + startDate + "' is invalid, format should be 'yyyy-MM-dd hh:mm:ss'.");
        }
        return super.dataSourceCommitmentsWidget(Module.ACTIVITY, start, end, userId);
    }
    

    @PreAuthorize(HAS_ACCESS)
    @GetMapping({"id/{activityCode}"})
    public ResponseEntity<ActiviyDetails> activiyDetails(@PathVariable(value = "activityCode") String activityCode) throws URISyntaxException {
        return new ResponseEntity<>(
            super.activiyDetailsCode(activityCode), HttpStatus.OK
        );
    }
    
    
    @GetMapping({"redirect/{activityCode}"})
    public ResponseEntity<?> activityByCode(@PathVariable(value = "activityCode") String activityCode) throws URISyntaxException {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId == null || !AccessUtil.hasAccess(HAS_ACCESS, SecurityUtils.getLoggedUserServices(), SecurityUtils.isLoggedUserAdmin())) {
            final String activityCodeUrl = "rest/activities/activity/redirect/" + activityCode;
            final String encodedUrl = HexUtil.hexEncoder(activityCodeUrl);
            final ResponseEntity result = AccessUtil.redirectError("-", encodedUrl, LoginError.MISSING_ACCOUNT_PASSWORD.getError(), null);
            return result;
        } else {
            final Long activityId = super.activiyIdByCode(activityCode);
            String url = SettingsUtil.getAppUrl();
            if (activityId != null) {
                url += "qms/" + Utilities.getSettings().getLang() + "/menu/activities/" + activityId + "?isRedirect=true";
            } else {
                url += "qms/" + Utilities.getSettings().getLang() + "/menu/error-404/redirect-" + activityCode;
            }
            final HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setLocation(new URI(url));
            return new ResponseEntity<>(httpHeaders, HttpStatus.SEE_OTHER);
        }
    }
    

    @GetMapping({"searchActivityId/{activityCode}"})
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, Object>> searchActivityId(@PathVariable(value = "activityCode") String activityCode) {
        final Long activiyId = super.activiyIdByCode(activityCode);
        if (activiyId == null) {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        } else {
            return new ResponseEntity<>(ImmutableMap.of("activityId", activiyId), HttpStatus.OK);
        }
    }
    

    @GetMapping("score-query/history/{start}/{end}/{activityId}")
    @PreAuthorize(HAS_DASHBOARD_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> scoreQueryActivityHistory(
        @PathVariable("start") Long start,
        @PathVariable("end") Long end,
        @PathVariable("activityId") Long activityId,
        @RequestBody GridFilter filter
    ) {
        return super.activityHistoryByDateRange(filter, activityId, new Date(start), new Date(end));
    }
    
    @Override

    @PreAuthorize(HAS_ACCESS)
    @GetMapping("business-unit-departments/actives")
    public List<ITextHasValue> getBusinessUnitDepartmentActivity() {
        return super.getBusinessUnitDepartmentActivity();
    }   
    

    @PreAuthorize(HAS_DELETE_ACCESS)
    @GetMapping("delete-activity/{activityId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Integer> deleteActivity(
        @PathVariable(value = "activityId") Long activityId
    ) {
        return super.deleteActivity(activityId, getModule());
    }
    

    @PreAuthorize(HAS_ACCESS)
    @GetMapping("data-linked/{activityId}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Map<String, Set<? extends IEntityDTO>>> dataLinked(@PathVariable(name = "activityId") Long activityId) {
        return super.dataLinked(activityId, SecurityUtils.getLoggedUser());
    }
    

    @PreAuthorize(HAS_ACCESS)
    @GetMapping(GET_IMPLEMENTERS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getImplementersData(
        @PathVariable(value = "businessUnitId") Long businessUnitId
    ) {
        return super.getImplementers(businessUnitId);
    }
    
    @PostMapping(GET_ACTIVITIES_LIST)
    @PreAuthorize(HAS_MODIFY_RESPONSIBLE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo activitiesImplementerChangeList(
        @PathVariable(value = "businessUnitId") Long businessUnitId,
        @RequestBody(required = false) GridFilter filter
    ) {
        return super.getActivitiesImplementerChange(filter, businessUnitId, getModule());
    }
    
    @PostMapping("save-many-implementers")
    @PreAuthorize(HAS_MODIFY_RESPONSIBLE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity saveManyImplementers(
        final @RequestBody ActivitiesChangeImplementerDTO dto
    ) throws InvalidArgumentException, MakePersistentException {
        return super.updateImplementerFromActivities(dto);
    }
    
    @PostMapping("verifier/list/{activityId}")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> verifiersList(
        @PathVariable(value = "activityId", required = false) Long activityId,
        @RequestBody GridFilter filter
    ) {
        return super.verifiersList(activityId, filter, getModule());
    }
    
    @PostMapping("verifiers/list")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> completeVerifiersList(
        @RequestBody GridFilter filter
    ) {
        return super.completeVerifiersList(filter, getModule());
    }
    
    @PostMapping("reassing-verifier/{activityId}")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ActivityDiff> reassignVerifier(
        @PathVariable(value = "activityId", required = false) Long activityId,
        @RequestBody ReassignVerifyDTO data
    ) throws InvalidArgumentException, MakePersistentException {
        return super.reassignVerify(activityId, data.getVerifierId(), data.getVerificationDate(), data.getReasonChange());
    }
    
    @PostMapping("reassing-verifier-many")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ActivityDiff> reassignManyVerify(
        @RequestBody ReassignVerifyDTO data
    ) throws InvalidArgumentException, MakePersistentException {
        return super.reassignVerifyMany(data);
    }
    
    @Override
    @PostMapping("save-field/preImplementer/{activityId}")
    @PreAuthorize(HAS_MODIFY_PRE_RESPONSIBLE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO> savePreImplementer(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedFieldRequestDTO<Long> preImplementer
    ) throws InvalidArgumentException, MakePersistentException {
        return super.savePreImplementer(activityId, preImplementer);
    }
    
    @Override()
    @PostMapping("planned-implementation-change/{activityId}")
    @PreAuthorize(HAS_PLAN_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean savePlannedImplementation(
        final @PathVariable(value = "activityId") Long activityId,
        final @RequestBody ModifiedPlannedImplementationDTO data
    ) throws InvalidArgumentException, MakePersistentException, InvalidOptionalDataException {
        return super.savePlannedImplementation(activityId, data);
    }
    
    @Override
    @PostMapping("add-plan/save/pre-workload-changes")
    @PreAuthorize(HAS_SAVE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> savePreWorloadChanges(
        @RequestBody List<PreWorkLoadSaveDTO> data
    ) throws InvalidArgumentException, MakePersistentException {
        return super.savePreWorloadChanges(data);
    }
    

    @GetMapping("navigator-codes/{activityTypeId}/{activityId}")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<String>> navigatorCodes(
        @PathVariable(value = "activityTypeId") Long activityTypeId,
        @PathVariable(value = "activityId", required = false) Long activityId
    ) throws InvalidArgumentException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return super.navigatorCodes(getModule(), activityTypeId, activityId, loggedUser);
    }
    

    @GetMapping("duplicate-data/data-source/{activityWorkflowId}/{activityId}")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ActivityDataSourceDto duplicateDataSource(
        @PathVariable(value = "activityWorkflowId") Long activityWorkflowId,
        @PathVariable(value = "activityId", required = false) Long activityId
    ) throws InvalidArgumentException, QMSException {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return super.duplicateDataSource(getModule(), activityWorkflowId, activityId, loggedUser);
    }
    
    @PostMapping({"files/save-comment/{activityId}"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> addCommentFile(
        final @PathVariable(value = "activityId", required = false) Long activityId,
        final @RequestBody FileCommentDTO comment
    ) throws InvalidArgumentException, MakePersistentException {
        return super.addCommentFile(activityId, comment);
    }
    
    @PostMapping({"relationship-activity/save/{activityId}"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity relationshipActivitySave(
        final @PathVariable(value = "activityId", required = false) Long activityId,
        final @RequestBody ActivityRelationshipItemDTO relation
    ) throws InvalidArgumentException, MakePersistentException {
        return super.relationshipActivitySave(activityId, relation);
    }
    
    @PostMapping({"relationship-activity/delete/{activityId}"})
    @PreAuthorize(HAS_ACCESS)
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> relationshipActivityDelete(
        final @PathVariable(value = "activityId", required = false) Long activityId,
        final @RequestBody Long relationActivityId
    ) throws InvalidArgumentException, MakePersistentException {
        return super.relationshipActivityDelete(activityId, relationActivityId);
    }
    
    @Override

    @GetMapping("relationship-activity/data-source/{activityId}")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<ActivityRelationshipDTO> relationshipActivityDataSource(
        @PathVariable(value = "activityId", required = false) Long activityId
    ) throws InvalidArgumentException {
        return super.relationshipActivityDataSource(activityId);
    }
    
    @Override

    @GetMapping("relationship-activity/count/{activityId}")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Long> relationshipActivitiesCount(
            @PathVariable(value = "activityId") final Long activityId
    ) throws InvalidArgumentException {
        return super.relationshipActivitiesCount(activityId);
    }
    
    @Override

    @GetMapping("clients-planner-active-by-user")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<List<PlannerClientDto>> getClientsOfPlannersActiviteByUser() {
        return super.getClientsOfPlannersActiviteByUser();
    }
    
    @Override

    @PreAuthorize(HAS_VIEWER_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @GetMapping("has-active-implementations/{activityId}/{userId}")
    public ResponseEntity<List<Map<String, Object>>> userHasActiveImplementations(
        @PathVariable(value = "activityId") Long activityId,
        @PathVariable(value = "userId") Long userId
    ) throws InvalidArgumentException {
        return super.userHasActiveImplementations(activityId, userId);
    }
    
    @Override

    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @GetMapping("data-source-assign/{activityId}")
    public ResponseEntity<ActivityAssignDataSourceDTO> dataSourceAssign(
        @PathVariable(value = "activityId") Long activityId
    ) throws InvalidArgumentException {
        return super.dataSourceAssign(activityId);
    }
    
    @Override

    @GetMapping("data-source/users-assigned-to-activities/actives")
    @PreAuthorize(HAS_ACCESS)
    public List<ITextHasValue> getUsersAssignedToActivities() {
        return super.getUsersAssignedToActivities();
    }
}
