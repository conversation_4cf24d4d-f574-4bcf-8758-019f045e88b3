package qms.activity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PendingVerifyNotApplyDto {
    private Integer apply;
    private String comment;
    private List<ActivityPendingDto> implementations;

    public Integer getApply() {
        return apply;
    }

    public void setApply(Integer apply) {
        this.apply = apply;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public List<ActivityPendingDto> getImplementations() {
        return implementations;
    }

    public void setImplementations(List<ActivityPendingDto> implementations) {
        this.implementations = implementations;
    }

}
