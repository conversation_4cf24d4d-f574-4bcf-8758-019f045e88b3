package qms.activity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import mx.bnext.access.Module;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 *
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ActivityLinkedSourceDto {

    private Module module;
    private String parentCode;
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Long auditIndividualId;
    private Long plannerId;
    private Long outstandingSurveysId;
    private Long fieldId;
    private Long findingId;
    private Integer type;

    public Module getModule() {
        return module;
    }

    public void setModule(Module module) {
        this.module = module;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    public Long getAuditIndividualId() {
        return auditIndividualId;
    }

    public void setAuditIndividualId(Long auditIndividualId) {
        this.auditIndividualId = auditIndividualId;
    }

    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    public Long getFindingId() {
        return findingId;
    }

    public void setFindingId(Long findingId) {
        this.findingId = findingId;
    }

    public Integer getType() {
        return type;
    }
    public void setType(Integer type) {
        this.type = type;
    }

    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    public Long getPlannerId() {
        return plannerId;
    }

    public void setPlannerId(Long plannerId) {
        this.plannerId = plannerId;
    }


}
