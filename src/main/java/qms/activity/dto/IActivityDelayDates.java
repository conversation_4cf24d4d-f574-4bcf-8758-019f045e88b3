/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.activity.dto;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public interface IActivityDelayDates extends IAvailableActionsByValue {
    
    Integer getDelayedByImplementer();

    Integer getDelayedByVerifier();

    Date getImplementationOn();

    Date getPlannedImplementationDate();

    Date getPlannedVerificationDate();

    Date getVerificationOn();

}
