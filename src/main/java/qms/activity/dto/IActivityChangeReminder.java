package qms.activity.dto;

import java.io.Serializable;
import java.util.Date;
import mx.bnext.access.Module;
import qms.activity.core.ActivityRole;
import qms.activity.dto.load.ISetImplementerUsers;
import qms.activity.entity.ActivityType;

/**
 *
 * <AUTHOR>
 */
public interface IActivityChangeReminder extends Serializable, IActivityHistoryInfo, ISetImplementerUsers, IActivityRoleFrom, IActivityStage {

    Date getPlannedImplementation();

    Date getPlannedVerificationDate();

    Date getReminder();

    Integer getAnticipationAttendDays();

    Integer getBelongSeries();

    Long getNotifyImplementTime();

    Long getNotifyVerifyTime();

    Module getModule();
    
    Integer getDaysToVerify();

    void setAnticipationAttendDays(Integer anticipationAttendDays);

    void setLoggedUserRole(ActivityRole loggedUserRole);

    void setPlannedImplementation(Date plannedImplementation);

    void setPlannedVerificationDate(Date plannedVerification);

    void setReminder(Date reminder);
    
    void setDaysToVerify(Integer daysToVerify);

    Long getRecurrenceActivityId();
    
    ActivityType getType();
}
