package qms.activity.dto.load;

import java.util.Date;
import qms.activity.entity.ActivityType;
import qms.planner.dto.PlannerTaskDTO;

/**
 *
 * <AUTHOR>
 */
public interface IActivityProgramInfo extends ISetImplementerUsers, IActivitySystemDTOLink {

    Date getReminder();

    PlannerTaskDTO getPlannerTask();

    Long getBusinessUnitDepartmentId();

    String getBusinessUnitDepartmentName();

    Long getBusinessUnitId();

    String getBusinessUnitName();

    String getCode();

    Long getCreatedBy();

    Date getCreatedDate();

    Integer getDaysToVerify();

    Integer getDelayedByImplementer();

    Integer getDelayedByVerifier();

    Integer getDeleted();
    
    Integer getRescheduled();
    
    String getDescription();

    Long getDynamicTableNameId();

    Integer getBelongSeries();

    Integer getFillType();

    Date getFinishImplementationOn();

    Date getFinishVerificationOn();

    String getFormCode();

    Integer getFormDeleted();

    String getFormDescription();

    Long getFormId();

    String getFormRequestor();

    Integer getFormStatus();

    Long getFormSurveyId();

    String getFormVersion();

    Date getFormLastModifiedDate();

    @Override
    Long getId();

    Long getImplementationPeriodicityId();
    
    Long getCategoryId();
    
    String getCategoryName();
     
    Long getImplementerOwnerId();

    String getImplementerNames();
    
    String getImplementerIds();

    String getModuleName();
    
    Integer getDefaultVerifierAuthor();
    
    Long getDefaultCustomVerifier();

    Date getNextImplementationDate();

    Date getNextVerificationDate();

    String getObjectiveDescription();

    Long getObjectiveId();

    String getPriorityDescription();

    Long getPriorityId();

    String getSourceDescription();

    Long getSourceId();

    Date getStartImplementationOn();

    Date getStartVerificationOn();

    Integer getStatus();

    ActivityType getType();

    Long getVerificationPeriodicityId();

    Long getVerifierOwnerId();

    Long getVerifierUserId();

    String getVerifierUserMail();

    String getVerifierUserName();
    
    String getVerifierLogin();
    
    Integer getVerifierVersion();

    Long getParentActivityImplementationId();
    
    String getCancellationReason();
    
    Double getParentActivityImplementationProgress();

    Long getParentActivityVerificationId();

    Long getParentActivityId();
    
    Double getParentActivityProgress();

    Long getParentTreeActivityId();

    String getParentActivityCode();
    
    String getParentActivityDescription();

    String getParentTreeActivityCode();

    Long getParentPlannerId();
    
    String getParentPlannerCode();
    
    String getParentPlannerName();

    Integer getChildCount();
    
    Integer getImplementationCount();

    Integer getApply();

    Integer getRecurrent();

    Date getPlannedVerification();

    Integer getAnticipationAttendDays();

    Boolean getIsPlanned();

    Double getPlannedHours();
    
    Boolean getFollowUpImplementationDelay();
    
    Boolean getMustUpdateImplementationAtReturn();
    
    Double getActualHours();
    
    Long getRecurrenceActivityId();

    Integer getCreatedWeek();

    Integer getCommitmentWeek();

    Integer getPlannedImplementationWeek();

    Integer getPlannedVerificationWeek();  
    
    String getCreatorUserName();
    
    String getCreatorUserDepartment();
    
    String getActivityResolution();
    
    Long getResolutionId();
    
    String getActivityOrder();
    
    Long getPreImplementerOwnerId();
    
    String getPreImplementerNames();
    
    String getPreImplementerIds();
    
    Long getCreatorUserDepartmentId();
}
