package qms.activity.dto.load;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.Objects;
import qms.activity.core.ActivityStage;
import qms.activity.dto.ActivityFormDTO;
import qms.activity.dto.ActivityFormPendingDTO;
import qms.activity.dto.ActivityPendingDto;
import qms.activity.dto.IActivityInfo;
import qms.activity.dto.TextHasUserValue;
import qms.activity.mail.ActivityParentDTO;
import qms.framework.dto.IEntityDTO;
import qms.framework.dto.SystemLinkDTO;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ActivityLoadDTO extends ActivityPendingDto implements IActivityLoadDTO {

    private static final long serialVersionUID = 1L;

    private ActivityFormDTO fillForm;
    private ActivityFormPendingDTO formPending;
    private ActivityStage stage;
    private Date finishImplementationOn;
    private Date finishVerificationOn;
    private Date implementation;
    private Date nextImplementationDate;
    private Date nextVerificationDate;
    private Date plannedImplementation;
    private Date startImplementationOn;
    private Date startVerificationOn;
    private Date verification;
    private Double plannedVerificationProgress;
    private Integer plannedVerificationStatus;
    private List<ActivityHistoryDTO> history;
    private List<ActivityPendingDto> childs;
    private List<ActivityPendingDto> events;
    private List<ActivityPendingDto> partitions;
    private List<ActivityPendingDto> plannedImplementations;
    private List<ActivityPendingDto> siblings;
    private List<SystemLinkDTO> systemLinks;
    private Set<TextHasUserValue> implementerUsers;
    private Long dynamicTableNameId;
    private String dynamicTableName;
    private Long implementedByUserId;
    private Long implementer;
    private Long verifiedByUserId;
    private Long verifier;
    private Map<String, Set<? extends IEntityDTO>> linked;  // <-- Configuración de documentos, archivos, comentarios
    private ActivityParentDTO linkedSource;                 // <-- Configuración de enlace a otros módulos
    private String typeName;
    private String error;
    private Long plannedVerificationActivityId;
    private Integer deleted;
    private Date createdDate;
    // Configuración de pre-asignación
    private Long preImplementer;
    private String preImplementerNames;
    private String preImplementerIds;
    private Set<TextHasUserValue> preImplementerUsers;
    // Program info
    private String mainCode;
    private Integer mainStatus;
    private Long mainCreatedBy;
    private String mainCreatedUserName;
    private Long plannedImplementationZoned;
    private Long plannedVerificationZoned;
    private Long createdDateZoned;
    private Long lastModifiedDateZoned;
    private Long startImplementationOnZoned;
    private Long startVerificationOnZoned;

    public ActivityLoadDTO() {
    }

    public ActivityLoadDTO(IActivityInfo info) {
        super(
                info.getId(),
                info.getPlannerTask() != null ? info.getPlannerTask().getPlannerId() : null,
                info.getPlannerTask() != null ? info.getPlannerTask().getClientId() : null,
                info.getPlannerTask() != null ? info.getPlannerTask().getValue() : null,
                info.getOutstandingSurveyId(),
                info.getAddImplementationOnCreate(),
                info.getAddVerificationOnCreate(),
                info.getVerificationReqOnPlanning(),
                info.getAddVerificationAvailable(),
                info.getFollowUpImplementationDelay(),
                info.getMustUpdateImplementationAtReturn(),
                info.getFormFillIsRequested(),
                info.getFormFillIsClosed(),
                null, // info.getDocumentId(),
                info.getParentTreeActivityId(),
                info.getParentActivityId(),
                info.getParentActivityProgress(),
                info.getParentActivityImplementationId(),
                info.getCancellationReason(),
                info.getParentActivityImplementationProgress(),
                info.getRecurrent(),
                info.getRecurrenceActivityId(),
                info.getLastActionUserId(),
                info.getParentTreeActivityCode(),
                info.getParentActivityCode(),
                info.getParentActivityDescription(),
                info.getLastActionDescription(),
                info.getLastActionUserName(),
                info.getLastActionTimestamp(),
                info.getReminder(),
                info.getCreatedWeek(),
                info.getCommitmentWeek(),
                info.getPlannedImplementationWeek(),
                info.getPlannedVerificationWeek(),
                info.getChildCount(),
                info.getImplementationCount(),
                info.getDelayedByImplementer(),
                info.getDelayedByVerifier(),
                info.getStatus(),
                info.getApply(),
                info.getCommitmentTask(),
                info.getFillType(),
                null, // authorDeleteRecurrence
                null, // authorEditApply
                null, // authorEditDynamicFields
                null, // authorEditImplementation
                null, // authorEditImplementer
                null, // implementerEditDynamicFields
                null, // verifierEditDynamicFields
                null, // authorEditBusinessUnitDepartment
                null, // verifierEditBusinessUnitDepartment
                null, // implementerEditBusinessUnitDeparment
                null, // authorEditRecurrence
                null, // authorEditVerification
                null, // authorEditVerifier
                info.getImplementerEditApply(),
                info.getVerifierEditApply(),
                info.getDefaultVerifierAuthor(),
                info.getDefaultCustomVerifier(),
                info.getCreatedBy(),
                info.getVerifierUser(),
                info.getProgress(),
                info.getCode(),
                info.getDescription(),
                null, // info.getCommitmentDate(),
                info.getImplementerNames(),
                info.getImplementerIds(),
                info.getVerifierUserName(),
                info.getBusinessUnitDepartmentId(),
                info.getBusinessUnitDepartmentName(),
                info.getType() == null ? null : info.getType().getDescription(),
                info.getType() == null ? null : info.getType().getId(),
                info.getModuleName(),
                info.getAnticipationAttendDays(),
                info.getBelongSeries(),
                info.getIsPlanned(),
                info.getParentActivityVerificationId(),
                info.getCategoryId(),
                info.getCategoryName(),
                info.getPlannedHours(),
                info.getActualHours(),
                info.getResolutionId(),
                info.getPlannedVerificationActivityId()
        );
        this.implementerUsers = info.getImplementerUsers();
        this.systemLinks = info.getSystemLinks();
        if (info.getType() != null) {
            this.authorDeleteRecurrence = info.getType().getAuthorDeleteRecurrence();
            this.authorEditApply = info.getType().getAuthorEditApply();
            this.authorEditDynamicFields = info.getType().getAuthorEditDynamicFields();
            this.authorEditImplementation = info.getType().getAuthorEditImplementation();
            this.authorEditImplementer = info.getType().getAuthorEditImplementer();
            this.implementerEditDynamicFields = info.getType().getImplementerEditDynamicFields();
            this.verifierEditDynamicFields = info.getType().getVerifierEditDynamicFields();
            this.authorEditBusinessUnitDepartment = info.getType().getAuthorEditBusinessUnitDepartment();
            this.verifierEditBusinessUnitDepartment = info.getType().getVerifierEditBusinessUnitDepartment();
            this.implementerEditBusinessUnitDeparment = info.getType().getImplementerEditBusinessUnitDeparment();
            this.authorEditRecurrence = info.getType().getAuthorEditRecurrence();
            this.authorEditVerification = info.getType().getAuthorEditVerification();
            this.authorEditVerifier = info.getType().getAuthorEditVerifier();
            this.addImplementationOnCreate = info.getType().getAddImplementationOnCreate();
            this.addVerificationOnCreate = info.getType().getAddVerificationOnCreate();
            this.verificationReqOnPlanning = info.getType().getVerificationReqOnPlanning();
            this.addVerificationAvailable = info.getType().getAddVerificationAvailable();
            this.followUpImplementationDelay = info.getType().getFollowUpImplementationDelay();
            this.mustUpdateImplementationAtReturn = info.getType().getMustUpdateImplementationAtReturn();
        }
    }
    
    @Override
    public Long getDynamicTableNameId() {
        return dynamicTableNameId;
    }

    @Override
    public void setDynamicTableNameId(Long dynamicTableNameId) {
        this.dynamicTableNameId = dynamicTableNameId;
    }

    @Override
    public ActivityFormDTO getFillForm() {
        return fillForm;
    }

    @Override
    public void setFillForm(ActivityFormDTO fillForm) {
        this.fillForm = fillForm;
    }
    
    @Override
    public Date getFinishImplementationOn() {
        return finishImplementationOn;
    }

    @Override
    public void setFinishImplementationOn(Date finishImplementationOn) {
        this.finishImplementationOn = finishImplementationOn;
    }

    @Override
    public Date getFinishVerificationOn() {
        return finishVerificationOn;
    }

    @Override
    public void setFinishVerificationOn(Date finishVerificationOn) {
        this.finishVerificationOn = finishVerificationOn;
    }

    @Override
    public Date getImplementation() {
        return implementation;
    }

    @Override
    public void setImplementation(Date implementation) {
        this.implementation = implementation;
    }

    @Override
    public Long getImplementer() {
        return implementer;
    }

    @Override
    public void setImplementer(Long implementer) {
        this.implementer = implementer;
    }

    @Override
    public Map<String, Set<? extends IEntityDTO>> getLinked() {
        return linked;
    }

    @Override
    public void setLinked(Map<String, Set<? extends IEntityDTO>> linked) {
        this.linked = linked;
    }

    @Override
    public ActivityParentDTO getLinkedSource() {
        return linkedSource;
    }

    @Override
    public void setLinkedSource(ActivityParentDTO linkedSource) {
        this.linkedSource = linkedSource;
    }
    
    @Override
    public List<ActivityHistoryDTO> getHistory() {
        return history;
    }

    @Override
    public void setHistory(List<ActivityHistoryDTO> history) {
        this.history = history;
    }

    @Override
    public Date getNextImplementationDate() {
        return nextImplementationDate;
    }

    @Override
    public void setNextImplementationDate(Date nextImplementationDate) {
        this.nextImplementationDate = nextImplementationDate;
    }

    @Override
    public Date getNextVerificationDate() {
        return nextVerificationDate;
    }

    @Override
    public void setNextVerificationDate(Date nextVerificationDate) {
        this.nextVerificationDate = nextVerificationDate;
    }
    
    @Override
    public List<ActivityPendingDto> getEvents() {
        return events;
    }

    @Override
    public void setEvents(List<ActivityPendingDto> events) {
        this.events = events;
    }

    @Override
    public List<ActivityPendingDto> getPartitions() {
        return partitions;
    }

    @Override
    public void setPartitions(List<ActivityPendingDto> partitions) {
        this.partitions = partitions;
    }

    @Override
    public List<ActivityPendingDto> getPlannedImplementations() {
        return plannedImplementations;
    }

    @Override
    public void setPlannedImplementations(List<ActivityPendingDto> plannedImplementations) {
        this.plannedImplementations = plannedImplementations;
    }
    
    @Override
    public Date getStartImplementationOn() {
        return startImplementationOn;
    }

    @Override
    public void setStartImplementationOn(Date startImplementationOn) {
        this.startImplementationOn = startImplementationOn;
    }

    @Override
    public Date getStartVerificationOn() {
        return startVerificationOn;
    }

    @Override
    public void setStartVerificationOn(Date startVerificationOn) {
        this.startVerificationOn = startVerificationOn;
    }

    @Override
    public String getTypeName() {
        return typeName;
    }

    @Override
    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public Date getVerification() {
        return verification;
    }

    @Override
    public void setVerification(Date verification) {
        this.verification = verification;
    }

    @Override
    public Long getVerifier() {
        return verifier;
    }

    @Override
    public void setVerifier(Long verifier) {
        this.verifier = verifier;
    }

    @Override
    public Integer getPlannedVerificationStatus() {
        return plannedVerificationStatus;
    }

    @Override
    public void setPlannedVerificationStatus(Integer plannedVerificationStatus) {
        this.plannedVerificationStatus = plannedVerificationStatus;
    }

    @Override
    public Double getPlannedVerificationProgress() {
        return plannedVerificationProgress;
    }

    @Override
    public void setPlannedVerificationProgress(Double plannedVerificationProgress) {
        this.plannedVerificationProgress = plannedVerificationProgress;
    }

    @Override
    public Long getVerifiedByUserId() {
        return verifiedByUserId;
    }

    @Override
    public void setVerifiedByUserId(Long verifiedByUserId) {
        this.verifiedByUserId = verifiedByUserId;
    }

    @Override
    public Long getImplementedByUserId() {
        return implementedByUserId;
    }

    @Override
    public void setImplementedByUserId(Long implementedByUserId) {
        this.implementedByUserId = implementedByUserId;
    }

    @Override
    public ActivityStage getStage() {
        return stage;
    }

    @Override
    public void setStage(ActivityStage stage) {
        this.stage = stage;
    }
    
    @Override
    public Date getPlannedImplementation() {
        return plannedImplementation;
    }

    @Override
    public void setPlannedImplementation(Date plannedImplementation) {
        this.plannedImplementation = plannedImplementation;
    }
    
    @Override
    public List<ActivityPendingDto> getChilds() {
        return childs;
    }

    @Override
    public void setChilds(List<ActivityPendingDto> childs) {
        this.childs = childs;
    }

    @Override
    public List<ActivityPendingDto> getSiblings() {
        return siblings;
    }

    @Override
    public void setSiblings(List<ActivityPendingDto> siblings) {
        this.siblings = siblings;
    }

    @Override
    public ActivityFormPendingDTO getFormPending() {
        return formPending;
    }

    @Override
    public void setFormPending(ActivityFormPendingDTO formPending) {
        this.formPending = formPending;
    }

    @Override
    public Set<TextHasUserValue> getImplementerUsers() {
        return implementerUsers;
    }

    @Override
    public void setImplementerUsers(Set<TextHasUserValue> implementerUsers) {
        this.implementerUsers = implementerUsers;
    }
    
    @Override
    public List<SystemLinkDTO> getSystemLinks() {
        return systemLinks;
    }
    
    @Override
    public void setSystemLinks(List<SystemLinkDTO> systemLinks) {
        this.systemLinks = systemLinks;
    }
    
    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }
    
    @Override
    public Long getPlannedVerificationActivityId() {
        return plannedVerificationActivityId;
    }
    @Override
    public void setPlannedVerificationActivityId(Long plannedVerificationActivityId) {
        this.plannedVerificationActivityId = plannedVerificationActivityId;
    }
    
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Long getPreImplementer() {
        return preImplementer;
    }

    public void setPreImplementer(Long preImplementer) {
        this.preImplementer = preImplementer;
    }

    public String getPreImplementerNames() {
        return preImplementerNames;
    }

    public void setPreImplementerNames(String preImplementerNames) {
        this.preImplementerNames = preImplementerNames;
    }

    public String getPreImplementerIds() {
        return preImplementerIds;
    }

    public void setPreImplementerIds(String preImplementerIds) {
        this.preImplementerIds = preImplementerIds;
    }

    public Set<TextHasUserValue> getPreImplementerUsers() {
        return preImplementerUsers;
    }

    public void setPreImplementerUsers(Set<TextHasUserValue> preImplementerUsers) {
        this.preImplementerUsers = preImplementerUsers;
    }

    @Override
    public String getMainCode() {
        return mainCode;
    }

    @Override
    public void setMainCode(String mainCode) {
        this.mainCode = mainCode;
    }

    @Override
    public Integer getMainStatus() {
        return mainStatus;
    }

    @Override
    public void setMainStatus(Integer mainStatus) {
        this.mainStatus = mainStatus;
    }

    @Override
    public Long getMainCreatedBy() {
        return mainCreatedBy;
    }

    @Override
    public void setMainCreatedBy(Long mainCreatedBy) {
        this.mainCreatedBy = mainCreatedBy;
    }

    @Override
    public String getMainCreatedUserName() {
        return mainCreatedUserName;
    }

    @Override
    public void setMainCreatedUserName(String mainCreatedUserName) {
        this.mainCreatedUserName = mainCreatedUserName;
    }

    @Override
    public String getDynamicTableName() {
        return dynamicTableName;
    }

    @Override
    public void setDynamicTableName(String dynamicTableName) {
        this.dynamicTableName = dynamicTableName;
    }

    public Long getPlannedImplementationZoned() {
        return plannedImplementationZoned;
    }

    public void setPlannedImplementationZoned(Long plannedImplementationZoned) {
        this.plannedImplementationZoned = plannedImplementationZoned;
    }

    public Long getPlannedVerificationZoned() {
        return plannedVerificationZoned;
    }

    public void setPlannedVerificationZoned(Long plannedVerificationZoned) {
        this.plannedVerificationZoned = plannedVerificationZoned;
    }

    public Long getCreatedDateZoned() {
        return createdDateZoned;
    }

    public void setCreatedDateZoned(Long createdDateZoned) {
        this.createdDateZoned = createdDateZoned;
    }

    public Long getLastModifiedDateZoned() {
        return lastModifiedDateZoned;
    }

    public void setLastModifiedDateZoned(Long lastModifiedDateZoned) {
        this.lastModifiedDateZoned = lastModifiedDateZoned;
    }

    public Long getStartImplementationOnZoned() {
        return startImplementationOnZoned;
    }

    public void setStartImplementationOnZoned(Long startImplementationOnZoned) {
        this.startImplementationOnZoned = startImplementationOnZoned;
    }

    public Long getStartVerificationOnZoned() {
        return startVerificationOnZoned;
    }

    public void setStartVerificationOnZoned(Long startVerificationOnZoned) {
        this.startVerificationOnZoned = startVerificationOnZoned;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActivityLoadDTO other = (ActivityLoadDTO) obj;
        if (!Objects.equals(this.getId(), other.getId())) {
            return false;
        }
        if (!Objects.equals(this.getProgress(), other.getProgress())) {
            return false;
        }
        return Objects.equals(this.getStatus(), other.getStatus());
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.getId());
        hash = 53 * hash + Objects.hashCode(this.getProgress());
        hash = 53 * hash + Objects.hashCode(this.getStatus());
        return hash;
    }

    @Override
    public String toString() {
        return "ActivityLoadDTO{" + "code=" + code + ", id=" + getId() + ", status=" + getStatus() + '}';
    }

}
