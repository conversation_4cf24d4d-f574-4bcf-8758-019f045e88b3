package qms.activity.dto.load;

import java.util.Date;
import qms.activity.entity.ActivityType;

/**
 *
 * <AUTHOR>
 */
public class ActivityImplementationInfo extends ActivityPendingInfo implements IActivityPendingInfo {

    private final Date plannedImplementation;
    private final String plannedImplementationTimezone;
    /*
     * Constructor utilizado en la consulta de {@link qms.activity.dao.ActivityDAO#getActivityImplementationInfo getActivityImplementationInfo}
     **/
    public ActivityImplementationInfo(
            final Date plannedImplementation,
            final Date plannedVerification,
            final Long plannedVerificationActivityId,
            final Long plannedVerificationImplementerId,
            final Long plannedVerificationVerifierId,
            final Long singleImplementationId,
            final Long singleImplementationImplementerId,
            final Long recurrenceActivityId,
            final Long parentTreeActivityId,
            final Long parentActivityId,
            final Double parentActivityProgress,
            final Long parentActivityImplementationId,
            final String cancellationReason,
            final Double parentActivityImplementationProgress,
            final Long parentActivityVerificationId,
            final String parentTreeActivityCode,
            final String parentActivityCode,
            final String parentActivityDescription,
            final Long parentPlannerId,
            final String parentPlannerCode,
            final String parentPlannerName,
            final Integer createdWeek,
            final Integer commitmentWeek,
            final Integer plannedImplementationWeek,
            final Integer plannedVerificationWeek,
            final Integer childCount,
            final Integer implementationCount,
            final String verificationPlannedWeek,
            final Long id,
            final Long plannerId,
            final String plannerName,
            final Long clientId,
            final String clientName,
            final Long parentActivityTaskId,
            final String parentActivityTaskName,
            final Long dynamicTableNameId,
            final Long businessUnitId,
            final Long businessUnitDepartmentId,
            final Long implementationPeriodicityId,
            final Long verificationPeriodicityId,
            final String businessUnitName,
            final String businessUnitDepartmentName,
            final ActivityType type,
            final Integer belongSeries,
            final Integer fillType,
            final Integer daysToVerify,
            final String code,
            final String description,
            final Integer status,
            final Long createdBy,
            final Long implementerOwnerId,
            final Long verifierOwnerId,
            final Integer commitmentTask,
            final Date implementationOn,
            final Date verificationOn,
            final Date createdDate,
            final Integer deleted,
            final Integer rescheduled,
            final Integer delayedByImplementer,
            final Integer delayedByVerifier,
            final Long verifierUserId,
            final String verifierUserName,
            final String verifierLogin,
            final Integer verifierVersion,
            final String implementerNames,
            final String implementerIds,
            final Long sourceId,
            final String sourceDescription,
            final Long objectiveId,
            final String objectiveDescription,
            final Long priorityId,
            final String priorityDescription,
            final Boolean isPlanned,
            final Double plannedHours,
            final Boolean followUpImplementationDelay,
            final Boolean mustUpdateImplementationAtReturn,
            final Long formId,
            final Long formSurveyId,
            final Integer formStatus,
            final Integer formDeleted,
            final String formCode,
            final String formDescription,
            final String formVersion,
            final String formRequestor,
            final Date formLastModifiedDate,
            final String verifierUserMail,
            final Long outstandingSurveyId,
            final Long documentId,
            final Integer formFillIsRequested,
            final Integer formFillIsClosed,
            final Integer apply,
            final Double progress,
            final String moduleName,
            final Integer defaultVerifierAuthor,
            final Long defaultCustomVerifier,
            final Date nextVerificationDate,
            final Date finishVerificationOn,
            final Date startVerificationOn,
            final Date nextImplementationDate,
            final Date finishImplementationOn,
            final Date startImplementationOn,
            final Integer recurrent,
            final Integer anticipationAttendDays,
            final Date reminder,
            final Double actualHours,
            final String creatorUserName,
            final String creatorUserDepartment,
            final Long creatorUserDepartmentId,
            final String activityResolution,
            final Long resolutionId,
            final Long categoryId,
            final String categoryName,
            final String activityOrder,
            final Long preImplementerOwnerId,
            final String preImplementerNames,
            final String preImplementerIds,
            final String mainCode,
            final Integer mainStatus,
            final Long mainCreatedBy,
            final String mainCreatedUserName,
            final Integer plannerStatus,
            final String plannedImplementationTimezone,
            final String plannedVerificationTimezone,
            final String createdDateTimezone,
            final String lastModifiedDateTimezone,
            final String startImplementationOnTimezone,
            final String startVerificationOnTimezone,
            final String verificationOnTimezone,
            final String implementationOnTimezone,
            final String finishVerificationOnTimezone,
            final String finishImplementationOnTimezone
    ) {
        super(
            plannedVerification,
            plannedVerificationActivityId,
            plannedVerificationImplementerId,
            plannedVerificationVerifierId,
            singleImplementationId,
            singleImplementationImplementerId,
            recurrenceActivityId,
            parentTreeActivityId,
            parentActivityId,
            parentActivityProgress,
            parentActivityImplementationId,
            cancellationReason,
            parentActivityImplementationProgress,
            parentActivityVerificationId,
            parentTreeActivityCode,
            parentActivityCode,
            parentActivityDescription,
            parentPlannerId,
            parentPlannerCode,
            parentPlannerName,
            createdWeek,
            commitmentWeek,
            plannedImplementationWeek,
            plannedVerificationWeek,
            childCount,
            implementationCount,
            verificationPlannedWeek,
            id,
            plannerId,
            plannerName,
            clientId,
            clientName,
            parentActivityTaskId,
            parentActivityTaskName,
            dynamicTableNameId,
            businessUnitId,
            businessUnitDepartmentId,
            implementationPeriodicityId,
            verificationPeriodicityId,
            businessUnitName,
            businessUnitDepartmentName,
            type,
            belongSeries,
            fillType,
            daysToVerify,
            code,
            description,
            status,
            createdBy,
            implementerOwnerId,
            verifierOwnerId,
            commitmentTask,
            implementationOn,
            verificationOn,
            createdDate,
            deleted,
            rescheduled,
            delayedByImplementer,
            delayedByVerifier,
            verifierUserId,
            verifierUserName,
            verifierLogin,
            verifierVersion,
            // implementerUsers,                                                // <-- Su valor se debe llenar desde un "setImplementerUsers()"
            implementerNames,
            implementerIds,
            sourceId,
            sourceDescription,
            objectiveId,
            objectiveDescription,
            priorityId,
            priorityDescription,
            isPlanned,
            plannedHours,
            followUpImplementationDelay,
            mustUpdateImplementationAtReturn,
            formId,
            formSurveyId,
            formStatus,
            formDeleted,
            formCode,
            formDescription,
            formVersion,
            formRequestor,
            formLastModifiedDate,
            verifierUserMail,
            outstandingSurveyId,
            documentId,
            formFillIsRequested,
            formFillIsClosed,
            apply,
            progress,
            moduleName,
            defaultVerifierAuthor,
            defaultCustomVerifier,
            nextVerificationDate,
            finishVerificationOn,
            startVerificationOn,
            nextImplementationDate,
            finishImplementationOn,
            startImplementationOn,
            recurrent,
            anticipationAttendDays,
            reminder,
            actualHours,
            creatorUserName,
            creatorUserDepartment,
            creatorUserDepartmentId,
            activityResolution,
            resolutionId,
            categoryId,
            categoryName,
            activityOrder,
            preImplementerOwnerId,
            preImplementerNames,
            preImplementerIds,
            mainCode,
            mainStatus,
            mainCreatedBy,
            mainCreatedUserName,
            plannedVerificationTimezone,
            createdDateTimezone,
            lastModifiedDateTimezone,
            startImplementationOnTimezone,
            startVerificationOnTimezone,
            verificationOnTimezone,
            implementationOnTimezone,
            finishVerificationOnTimezone,
            finishImplementationOnTimezone
        );
        if (super.getPlannerTask() != null) {
            super.getPlannerTask().setPlannerStatus(plannerStatus);
        }
        this.plannedImplementation = plannedImplementation;
        this.plannedImplementationTimezone = plannedImplementationTimezone;
    }

    public Date getPlannedImplementation() {
        return plannedImplementation;
    }

    public String getPlannedImplementationTimezone() {
        return plannedImplementationTimezone;
    }
}
