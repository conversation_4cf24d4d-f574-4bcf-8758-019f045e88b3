package qms.activity.bulk.util;

public enum ActivityBulkSheetType {
    IMPLEMENTERS(ActivityBulkRowColumn.IMPLEMENTERS),
    VERIFIERS(ActivityBulkRowColumn.VERIFIER),
    CLIENTS(ActivityBulkRowColumn.CLIENT),
    PLANNERS(ActivityBulkRowColumn.PLANNER),
    TASKS(ActivityBulkRowColumn.TASK),
    CATEGORIES(ActivityBulkRowColumn.CATEGORY),
    OBJECTIVES(ActivityBulkRowColumn.OBJECTIVE),
    SOURCES(ActivityBulkRowColumn.SOURCE),
    PRIORITIES(ActivityBulkRowColumn.PRIORITY),
    FORMS(ActivityBulkRowColumn.FILL_FORM),
    BUSINESS_UNIT_DEPARTMENTS(ActivityBulkRowColumn.BUSINESS_UNIT_DEPARTMENT),
    PRE_IMPLEMENTERS(ActivityBulkRowColumn.PRE_IMPLEMENTER),
    TYPES(ActivityBulkRowColumn.TYPE);

    private final ActivityBulkRowColumn field;

    ActivityBulkSheetType(ActivityBulkRowColumn field) {
        this.field = field;
    }

    public ActivityBulkRowColumn getField() {
        return field;
    }

    @Override
    public String toString() {
        return "ActivityBulkSheetType{" +
                "field=" + field +
                '}';
    }
}