package qms.activity.bulk.validation;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import qms.activity.bulk.dto.ActivityBulkConfig;
import qms.activity.bulk.dto.ActivityBulkPlainRow;
import qms.activity.bulk.dto.ActivityBulkWorkBook;
import qms.activity.bulk.logic.ActivityBulkTags;
import qms.activity.bulk.util.ActivityBulkErrorHelper;
import qms.activity.bulk.util.ActivityBulkRowColumn;
import qms.framework.bulk.dto.BulkVariable;
import qms.framework.bulk.dto.IBulkWorkBook;
import qms.framework.bulk.util.BulkConstants;
import qms.framework.bulk.util.BulkFileError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.MeasureTime;

public class ActivityBulkSchemaValidator {
    
    private final ActivityBulkTags tags;
    private final ActivityBulkErrorHelper errors;

    public ActivityBulkSchemaValidator(final ActivityBulkConfig config) {
        this.tags = config.getTags();
        this.errors = config.getErrors();
    }

    public Boolean valid(final IBulkWorkBook<ActivityBulkPlainRow, ActivityBulkRowColumn> w) throws BulkFileError {
        ActivityBulkWorkBook wb = (ActivityBulkWorkBook) w;
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            if (wb == null || wb.getRowsSheet().getValues() == null) {
                errors.logEmptyContentError(wb);
                return false;
            }
            boolean valid = true;
            final Boolean validRows = validationRows(wb);
            if (!validRows) {
                valid = false;
            }
            final Boolean validVariables = validationVariables(wb);
            if (!validVariables) {
                valid = false;
            }
            final Boolean validData = validationData(wb);
            if (!validData) {
                valid = false;
            }
            final Boolean validFlag = validRowFlag(wb);
            if (!validFlag) {
                valid = false;
            }
            return valid;
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in activity bulk schema validator.");
        }
    }

    private Boolean validRowFlag(final ActivityBulkWorkBook wb) {
        final List<ActivityBulkPlainRow> rows = wb.getRowsSheet().getValues();
        if (rows == null || rows.isEmpty()) {
            return false;
        }
        return rows.stream().allMatch(ActivityBulkPlainRow::getValid);
    }

    private Boolean validationRows(final ActivityBulkWorkBook wb) throws BulkFileError {
        final List<ActivityBulkPlainRow> rows = wb.getRowsSheet().getValues();
        if (rows == null || rows.isEmpty()) {
            errors.logEmptyContentError(wb);
            return false;
        }
        boolean valid = true;
        final boolean rowsWithValue = rows.stream()
                .noneMatch(row -> isRowEmpty(wb, row));
        if (!rowsWithValue) {
            valid = false;
        }
        final Boolean validIds = validRowsIds(wb, rows);
        if (!validIds) {
            valid = false;
        }
        final Boolean invalidParents = invalidParents(wb, rows);
        if (invalidParents) {
            valid = false;
        }
        final Boolean validFillTypes = validFillTypes(wb, rows);
        if (!validFillTypes) {
            valid = false;
        }
        final Boolean validLength = validLength(wb, rows);
        if (!validLength) {
            valid = false;
        }
        return valid;
    }

    private Boolean validationVariables(final ActivityBulkWorkBook wb) {
        final List<BulkVariable> variables = wb.getVariablesSheet().getValues();
        if (variables == null || variables.isEmpty()) {
            return true;
        }
        boolean valid = true;
        final Boolean validValues = validVariablesValues(wb);
        if (!validValues) {
            valid = false;
        }
        final Boolean repeated = repeatedVariablesName(wb, variables);
        if (repeated) {
            valid = false;
        }
        return valid;
    }

    private Boolean validVariablesValues(final ActivityBulkWorkBook wb) {
        final List<BulkVariable> variables = wb.getVariablesSheet().getValues();
        if (variables.isEmpty()) {
            return true;
        }
        final AtomicBoolean valid = new AtomicBoolean(true);
        variables
                .forEach((variable) -> {
                    final Boolean isEmpty = isVariableEmpty(wb, variable);
                    if (isEmpty) {
                        valid.set(false);
                    }
                });
        return valid.get();
    }

    private Boolean repeatedVariablesName(final ActivityBulkWorkBook wb, final List<BulkVariable> variables) {
        final Set<String> ids = new HashSet<>(variables.size());
        final AtomicBoolean valid = new AtomicBoolean(false);
        variables.stream()
                .filter(row -> !StringUtils.isAllBlank(row.getName()))
                .forEach((row) -> {
                    if (ids.contains(row.getName())) {
                        errors.logRepeatedVariableError(wb, row);
                        valid.set(true);
                    } else {
                        ids.add(row.getName());
                    }
                });
        return valid.get();
    }

    private Boolean isVariableEmpty(final ActivityBulkWorkBook wb, final BulkVariable variable) {
        final boolean nameBlank = StringUtils.isAllBlank(variable.getName());
        final boolean valueBlank = StringUtils.isAllBlank(variable.getValue());
        if (nameBlank && valueBlank) {
            //Solo se considera error en variables si unos de los campos está vacío, no ambos
            return false;
        }
        if (nameBlank) {
            errors.logVariableNameError(wb, variable);
        }
        if (valueBlank) {
            errors.logVariableValueError(wb, variable);
        }
        return nameBlank || valueBlank;
    }

    private Boolean validationData(final ActivityBulkWorkBook wb) {
        return wb.getRowsSheet().getValues() != null;
    }

    private Boolean isRowEmpty(final ActivityBulkWorkBook wb, final ActivityBulkPlainRow row) {
        final boolean isBlank = StringUtils.isAllBlank(row.getType());
        if (isBlank) {
            errors.logRequiredField(ActivityBulkRowColumn.TYPE, row, wb);
        }
        final boolean descriptionBlank = StringUtils.isAllBlank(row.getDescription());
        if (descriptionBlank) {
            errors.logRequiredField(ActivityBulkRowColumn.DESCRIPTION, row, wb);
        }
        return isBlank || descriptionBlank;
    }

    private Boolean validRowsIds(final ActivityBulkWorkBook wb, final List<ActivityBulkPlainRow> rows) {
        final Set<String> ids = new HashSet<>(rows.size());
        final AtomicBoolean valid = new AtomicBoolean(true);
        rows.stream()
                .filter(row -> !StringUtils.isAllBlank(row.getId()))
                .forEach((row) -> {
                    if (ids.contains(row.getId())) {
                        errors.logRepeatedIdError(wb, row);
                        valid.set(false);
                    } else {
                        ids.add(row.getId());
                    }
                });
        return valid.get();
    }

    private Boolean invalidParents(final ActivityBulkWorkBook wb, final List<ActivityBulkPlainRow> rows) {
        final List<String> ids = getRowsIds(rows);
        final AtomicBoolean valid = new AtomicBoolean(true);
        rows.stream()
                .filter(row -> !StringUtils.isAllBlank(row.getParent()))
                .filter(row -> !ids.contains(row.getParent()))
                .forEach(row -> {
                    errors.logInvalidParent(wb, row);
                    valid.set(false);
                });
        return !valid.get();
    }

    private List<String> getRowsIds(final List<ActivityBulkPlainRow> rows) {
        return rows.stream()
                .map(ActivityBulkPlainRow::getId)
                .filter(id -> !StringUtils.isAllBlank(id))
                .collect(Collectors.toList());
    }

    private Boolean validFillTypes(final ActivityBulkWorkBook wb, final List<ActivityBulkPlainRow> rows) {
        final AtomicBoolean valid = new AtomicBoolean(true);
        rows.stream()
                .filter((row) -> row.getFillType() != null && !row.getFillType().isEmpty())
                .filter((row) -> tags.getFillTypesByValue(row.getFillType()) == null)
                .forEach((row) -> {
                    errors.logInvalidFillType(wb, row);
                    valid.set(false);
                });
        return valid.get();
    }
    
    private Boolean validLength(final ActivityBulkWorkBook wb, final List<ActivityBulkPlainRow> rows) {
        return rows.stream().allMatch((row) -> validLength(wb, row));
    }
    
    private Boolean validLength(final ActivityBulkWorkBook wb, final ActivityBulkPlainRow row) {
        boolean valid = true;
        if (row.getCode() != null && row.getCode().length() > BulkConstants.MAX_CODE_LENGTH) {
            errors.logMaximumCharactersExceeded(wb, row, ActivityBulkRowColumn.CODE, BulkConstants.MAX_CODE_LENGTH);
            valid = false;
        }
        if (row.getDescription() != null && row.getDescription().length() > BulkConstants.MAX_DESCRIPTION_LENGTH) {
            errors.logMaximumCharactersExceeded(wb, row, ActivityBulkRowColumn.DESCRIPTION, BulkConstants.MAX_DESCRIPTION_LENGTH);
            valid = false;            
            
        }
        if (row.getDaysToVerify() != null && row.getDaysToVerify().length() > BulkConstants.MAX_DAYS_TO_VERIFY_LENGTH) {
            errors.logMaximumCharactersExceeded(wb, row, ActivityBulkRowColumn.DAYS_TO_VERIFY, BulkConstants.MAX_DAYS_TO_VERIFY_LENGTH);
            valid = false;                  
        }
        if (row.getPlannedHours() != null && row.getPlannedHours().length() > BulkConstants.MAX_PLANNED_HOURS_LENGTH) {
            errors.logMaximumCharactersExceeded(wb, row, ActivityBulkRowColumn.DAYS_TO_VERIFY, BulkConstants.MAX_PLANNED_HOURS_LENGTH);
            valid = false;                  
        }
        if (row.getActivityOrder()!= null && row.getActivityOrder().length() > BulkConstants.MAX_ACTIVITY_ORDER_LENGTH) {
            errors.logMaximumCharactersExceeded(wb, row, ActivityBulkRowColumn.ACTIVITY_ORDER, BulkConstants.MAX_ACTIVITY_ORDER_LENGTH);
            valid = false;                  
        }
        return valid;
    }
}
