package qms.survey.logic;

import DPMS.DAOInterface.IAuditScoreDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentDAO;
import DPMS.DAOInterface.IExternalDocumentCatalogDAO;
import DPMS.DAOInterface.IInternalDocumentCatalogItemDAO;
import DPMS.Mapping.AuditScore;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.Request;
import DPMS.Mapping.SequenceDetail;
import Framework.Config.ThymeleafEngineMaster;
import Framework.Config.Utilities;
import bnext.exception.InvalidCipherDecryption;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Table;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyConditional;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyFieldEditable;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import isoblock.surveys.dao.hibernate.SurveyHeader;
import isoblock.surveys.dao.hibernate.SurveyItem;
import isoblock.surveys.dao.hibernate.SurveyTextItem;
import isoblock.surveys.dao.interfaces.IOutstandingQuestion;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.owasp.encoder.Encode;
import qms.access.dto.ILoggedUser;
import qms.configuration.dto.ExchangeRateDTO;
import qms.document.dto.ExternalCatalogValue;
import qms.form.core.FillHelper;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.FormWeightingItemDTO;
import qms.form.util.CatalogFieldType;
import qms.form.util.FieldComparator;
import qms.form.util.FillHelperUtils;
import qms.form.util.QrUrlType;
import qms.form.util.SurveyConditionalType;
import qms.form.util.SurveyFieldAnswerType;
import qms.form.util.SurveyParserHelper;
import qms.form.util.SurveyUtil;
import qms.form.util.SurveyUtilCache;
import qms.framework.dao.IDatabaseQueryDAO;
import qms.framework.dto.ExternalDocumentCatalogDTO;
import qms.framework.dto.HierarchyFieldDTO;
import qms.framework.security.ITokenProvider;
import qms.framework.util.BusinessUnitUtil;
import qms.framework.util.DataSourceCredentialError;
import qms.framework.util.IDatabaseQuery;
import qms.framework.util.MailTokenManager;
import qms.framework.util.SurveyFillEntity;
import qms.survey.dto.ChangeUserDefinedUtilDataDTO;
import qms.survey.dto.ExternalCatalogDataDTO;
import qms.survey.dto.ExternalFieldDTO;
import qms.survey.dto.FieldDTO;
import qms.survey.dto.HeaderDTO;
import qms.survey.dto.ItemDTO;
import qms.survey.dto.SurveyConditionalDTO;
import qms.survey.dto.SurveyDTO;
import qms.survey.dto.SurveyParserData;
import qms.survey.util.ICurrencyConvertable;
import qms.util.GridFilter;
import qms.util.interfaces.IPagedQuery;
import static Framework.Config.Utilities.getBean;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SurveyParser extends Loggable implements Serializable {

    @Nonnull
    public static final ImmutableList<String> SUPPORTED_PONDERACION_TYPES = ImmutableList.of(
            SurveyField.TYPE.EXCLUSIVE_SELECT_YESNO.getValue(),
            SurveyField.TYPE.MENU_SELECT.getValue(),
            SurveyField.TYPE.MULTI_SELECT.getValue(),
            SurveyField.TYPE.MULTI_SELECT_ARRAY.getValue(),
            SurveyField.TYPE.EXCLUSIVE_SELECT_ARRAY.getValue(),
            SurveyField.TYPE.EXCLUSIVE_SELECT.getValue()
    );

    private static final long serialVersionUID = 1L;
    public static final Long MAX_CATALOG_ITEMS = 250L;
    public static final Integer MAX_LABEL_COLUMN_LENGTH = 255;
    private static final String FORM_AREA = "formArea";
    private static final String HEADER_AREA = "headerArea";
    private static final String FOOTER_AREA = "footerArea";
    public static final String UNAVAILABLE_FIELD = "unavailable-field";
    public static final String AVAILABLE_FIELD = "available-field";
    public static final String OMITTED_SECTION = "omitted-section";

    public static final String CONDITIONAL_FIELD_HIDDEN = "conditional-field-hidden";

    @Nullable
    private IExternalDocumentCatalogDAO daoExternal;
    @Nullable
    private IInternalDocumentCatalogItemDAO daoInternal;
    @Nullable
    private IFormCaptureDAO formDao;
    @Nullable
    private IDatabaseQueryDAO queryDao;

    @Nonnull
    private final ILoggedUser loggedUser;

    @Nonnull
    private final SurveyParserData data;

    @Nonnull
    private List<UserRef> responsiblesUsersOf = new ArrayList<>();

    @Nullable
    private IAuditScoreDAO daoScore;

    @Nonnull
    private final SurveyDTO surveyData;

    @Nullable
    private final HttpServletRequest request;
    @Nullable
    private final HttpServletResponse response;

    @Nonnull
    private final SurveyParserHelper parserHelper = new SurveyParserHelper();
    @Nonnull
    private Boolean evaluateShowChangeDefinedUserOnlyAdmin = false;
    @Nullable
    private List<ChangeUserDefinedUtilDataDTO> userDefinedUtilData;

    public SurveyParser(
            @Nonnull final SurveyParserData data,
            @Nullable final HttpServletRequest request,
            @Nullable final HttpServletResponse response,
            @Nonnull final ILoggedUser loggedUser
    ) {
        this.data = data;
        this.request = request;
        this.response = response;
        this.loggedUser = loggedUser;
        this.surveyData = parseSurvey();
    }

    public String toHtml() {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("survey", surveyData);
        return ThymeleafEngineMaster.render("surveys/surveyBody", params, request, response);
    }

    private SurveyDTO parseSurvey() {
        final Survey survey = data.getSurvey();
        final List<SurveyField> sortedFields = sortFields(survey.getFields());
        final SurveyDTO surveyDto = new SurveyDTO();
        if (formDao == null) {
            formDao = getBean(IFormCaptureDAO.class);
        }
        final FillHelper validator = new FillHelper(
                FillHelperUtils.getFillData(this.data),
                formDao,
                loggedUser
        );
        if (survey.getGlobals().getObj().getTitle() != null) {
            String locale = this.getLocale();
            surveyDto.setTitle((survey.getGlobals().getObj().getTitle().get(0).getTitle() +
            (locale.isEmpty() || locale.equals("es-mx") ? ", por " : ", by ") +
            loggedUser.getDescription()).trim());
        } else {
            surveyDto.setTitle("");
        }
        surveyDto.setEditableTitle(survey.getGlobals().getObj().isEditableTitle());
        //Este bloque de código es para la funcionalidad de cambiar usuario definido
        final Long requestId = data.getOutstandingSurveys().getRequestId();
        surveyDto.setRequestId(requestId);
        final Long outstandingSurveyId = data.getOutstandingSurveys().getOutstandingSurveyId();
        surveyDto.setOutstandingSurveyId(outstandingSurveyId);
        if (surveyDto.getOutstandingSurveyId() != null && requestId != null) {
            setUserDefinedUtilData(loadChangeUserDefinedUtilData(validator));
            if (getUserDefinedUtilData() != null && !getUserDefinedUtilData().isEmpty()) {
                surveyDto.setOutstandingSurveyBusinessUnitDepartmentId(getUserDefinedUtilData().get(0).getBusinessUnitDepartamentId());
                evaluateShowChangeDefinedUserOnlyAdmin = surveyDto.getOutstandingSurveyBusinessUnitDepartmentId().equals(0L);
                if (surveyDto.getOutstandingSurveyBusinessUnitDepartmentId() != null && surveyDto.getOutstandingSurveyBusinessUnitDepartmentId() > 0) {
                    surveyDto.setResponsibleOfUsers(responsibleOfUsers(surveyDto.getOutstandingSurveyBusinessUnitDepartmentId()));
                }
            }
        }
        //
        List<FieldDTO> fields = getSurveyFields(sortedFields, validator);
        surveyDto.setSignRejectApproval(survey.getSignRejectApproval());
        final String task = data.getTask();
        surveyDto.setTask(task);
        surveyDto.setUrl(Utilities.getSettings().getUrl());
        surveyDto.setRequestStatus(getRequestStatus());
        surveyDto.setFields(fields);
        surveyDto.setSections(fields.stream().filter(f -> f.getType().equals("seccion")).collect(Collectors.toList()));
        surveyDto.setHeaderFields(filterHeaderFields(surveyDto));
        surveyDto.setFormFields(filterFormFields(surveyDto));
        surveyDto.setFooterFields(filterFooterFields(surveyDto));
        final boolean disabled = data.isDisabled();
        surveyDto.setDisabled(disabled);
        final boolean isAudit = data.isAudit();
        surveyDto.setAudit(isAudit);
        surveyDto.setPoll(Survey.TYPE_POLL.equals(survey.getType()));
        surveyDto.setForms(Survey.TYPE_REQUEST.equals(survey.getType()));
        final boolean scoreEnabled = data.isScoreEnabled();
        surveyDto.setScoreEnabled(scoreEnabled);
        final boolean findingEnabled = data.isFindingEnabled();
        surveyDto.setFindingEnabled(findingEnabled);
        final boolean isActivityEnabled = data.isActivityEnabled();
        surveyDto.setActivityEnabled(isActivityEnabled);
        surveyDto.setHasAdjustmentRequestInProgress(validator.getHasAdjustmentRequestInProgress());
        surveyDto.setHasFormApproverRole(validator.getHasFormApproverRole());
        if (isAudit) {
            surveyDto.setScores(loadScores());
        }
        surveyDto.setAvailability(loadAvailability());
        surveyDto.setLocale(getLocale());
        surveyDto.setArchived(Utilities.getSettings().getArchived());
        surveyDto.setOutstandingSurveyArchived(validator.getOutstandingSurveyArchived());
        surveyDto.setOutstandingSurveyDeleted(validator.getOutstandingSurveyDeleted());
        return surveyDto;
    }

    private List<AuditScore> loadScores() {
        if (daoScore == null) {
            daoScore = getBean(IAuditScoreDAO.class);
        }
        return daoScore.HQLT_findByQueryFilter("c.status = " + AuditScore.STATUS.ACTIVE.getValue());
    }
    
    private List<ChangeUserDefinedUtilDataDTO> loadChangeUserDefinedUtilData(FillHelper validator) {
        String extraWhere = "";
        if (Utilities.getSettings().getArchived() && validator.getOutstandingSurveyArchived()) {
            extraWhere = " AND ou.archived = 0 ";
        }
        final Long requestId = data.getOutstandingSurveys().getRequestId();
        if (formDao == null) {
            formDao = getBean(IFormCaptureDAO.class);
        }
        List<ChangeUserDefinedUtilDataDTO> v = formDao.HQL_findByQuery(" "
            + "SELECT new " + ChangeUserDefinedUtilDataDTO.class.getCanonicalName() + "("
                + " isnull(ou.businessUnitDepartmentId, 0) "
                + ",out.fieldObjectId "
                + ",out.userDefinedToFillId "
                + ",ou.id "
            + " ) "
            + " FROM " + SequenceDetail.class.getCanonicalName() + " sq "
            + " JOIN " + OutstandingSurveysAttendant.class.getCanonicalName() + " out "
                + " ON "
                    + " out.requestId = sq.id.requestId "
                    + " AND out.fillAutorizationPoolIndex = sq.id.indice "
                    + " AND out.fieldType IN ('" + SurveyField.TYPE_SECCION + "','" + SurveyField.TYPE_SIGNATURE + "') "
            + " JOIN " + OutstandingSurveys.class.getCanonicalName() + " ou "
                + " ON ou.id = out.outstandingSurveyId "
            + " WHERE "
                + " sq.id.requestId = :requestId "
                + " AND ou.status != :noStatus "
                + " AND sq.status = 1 "
                + " AND out.userDefinedToFillId IS NOT NULL "
                + extraWhere,
            ImmutableMap.of(
                "noStatus", OutstandingSurveys.STATUS.REJECTED.getValue(),
                "requestId", requestId
            )
        );
        if (getLogger().isDebugEnabled()) {
            getLogger().debug("loadChangeUserDefinedUtilData [requestId: {}]: {}", requestId, v.toString());
        }
        return v;
    }

    private double parseWeighting(final String ponderacion) {
        if (ponderacion == null || ponderacion.isEmpty()) {
            return 0.0;
        }
        try {
            return Utilities.parseDouble(ponderacion);
        } catch (NumberFormatException ex) {
            return 0.0;
        }
    }


    private boolean skipWeights() {
        final SurveyDTO survey = surveyData;
        if (survey.getFields() == null || survey.getFields().isEmpty()) {
            return true;
        }
        final Short hasWeight = data.getSurvey().getGlobals().getObj().getHasWeight();
        if (hasWeight == null) {
            return true;
        }
        return hasWeight != 1;
    }

    @Nonnull
    public List<FormWeightingItemDTO> getWeightsBySurveyItemId() {
        if (skipWeights()) {
            return new ArrayList<>(0);
        }
        final List<FormWeightingItemDTO> results = new ArrayList<>();
        for (final SurveyField field : data.getSurvey().getFields()) {
            final SurveyFieldObject obj = field.getObj();
            if (!SUPPORTED_PONDERACION_TYPES.contains(field.getType())) {
                continue;
            }
            if (obj.getItems() == null || obj.getItems().isEmpty()) {
                continue;
            }
            for (final SurveyItem item : obj.getItems()) {
                final FormWeightingItemDTO result = new FormWeightingItemDTO();
                result.setItemId(item.getId());
                final double weighting = parseWeighting(item.getPonderacion());
                result.setWeight(weighting);
                results.add(result);
            }
        }
        return results;
    }

    @Nonnull
    public List<FormWeightingItemDTO> getWeightsBySurveyHeaderId() {
        if (skipWeights()) {
            return new ArrayList<>(0);
        }
        final List<FormWeightingItemDTO> results = new ArrayList<>();
        for (final SurveyField field : data.getSurvey().getFields()) {
            final SurveyFieldObject obj = field.getObj();
            if (!SUPPORTED_PONDERACION_TYPES.contains(field.getType())) {
                continue;
            }
            if (obj.getHeaderTitles() == null || obj.getHeaderTitles().isEmpty()) {
                continue;
            }
            for (final SurveyHeader header : obj.getHeaderTitles()) {
                final FormWeightingItemDTO result = new FormWeightingItemDTO();
                result.setHeaderId(header.getId());
                final double weighting = parseWeighting(header.getPonderacion());
                result.setWeight(weighting);
                results.add(result);
            }
        }
        return results;
    }

    private List<UserRef> responsibleOfUsers(Long businessUnitDepartmentId) {
        final IBusinessUnitDepartmentDAO dao = getBean(IBusinessUnitDepartmentDAO.class);
        final List<UserRef> users = BusinessUnitUtil.getResponsiblesUserOf(dao, businessUnitDepartmentId);
        this.setResponsiblesUsersOf(users);
        return users;
    }

    private List<FieldDTO> filterHeaderFields(SurveyDTO surveyDto) {
        return filterFields(surveyDto.getFields(), HEADER_AREA);
    }

    private List<FieldDTO> filterFormFields(SurveyDTO surveyDto) {
        return filterFields(surveyDto.getFields(), FORM_AREA);
    }

    private List<FieldDTO> filterFooterFields(SurveyDTO surveyDto) {
        return filterFields(surveyDto.getFields(), FOOTER_AREA);
    }

    private String loadQuestionNumber(
            final Collection<SurveyField> sortedFields,
            final SurveyField field
    ) {
        final String type = field.getType();
        if (SurveyField.TYPE_SECCION.equals(type) || SurveyField.TYPE_INSTRUCCIONES.equals(type)) {
            return "";
        }
        Integer number = 0;
        for (final SurveyField localField : sortedFields) {
            final String localType = localField.getType();
            if (SurveyField.TYPE_SECCION.equals(localType)
                    || SurveyField.TYPE_INSTRUCCIONES.equals(localType)
                    || (localField.getObj().getFormArea() != null
                    && !localField.getObj().getFormArea().equals(FORM_AREA))) {
                continue;
            }
            number++;
            if (localField.equals(field)) {
                return number + ". ";
            }
        }
        return number + ". ";
    }

    private String loadAvailability() {
        if (Survey.TYPE_REQUEST.equals(data.getSurvey().getType())) {
            return UNAVAILABLE_FIELD;
        }
        return AVAILABLE_FIELD;
    }

    /**
     * Devuelve TRUE en caso de que se trate de un campo en el que 
     * el usuario pueda capturar información.
     *
     */
    private Boolean loadCaptureField(final SurveyField field) {
        final String type = field.getType();
        return !SurveyField.TYPE_SECCION.equals(type)
                && !SurveyField.TYPE_PAGEBREAK.equals(type)
                && !SurveyField.TYPE_INSTRUCCIONES.equals(type)
                && !SurveyField.TYPE_SIGNATURE.equals(type);
    }

    private String loadTypeRules(final SurveyField field) {
        if (SurveyField.ATTENDANT_FIELDS.contains(field.getType())) {
            String rules = "authorizableField";
            if (Objects.equals("t", field.getObj().getConditionalQuestion())) {
                rules += " " + OMITTED_SECTION;
            }
            return rules;
        } else if (Objects.equals("t", field.getObj().getConditionalQuestion())) {
            return CONDITIONAL_FIELD_HIDDEN;
        }
        return Strings.EMPTY;
    }

    private String loadReadOnlyClass(FieldDTO field) {
        if (!field.getAutoSelectSingleAnswer()) {
            return "";
        }
        if (SurveyField.TYPE_EXTERNAL_CATALOG.equals(field.getType())) {
            final ExternalCatalogDataDTO catalogData = field.getCatalogData();
            if (catalogData == null) {
                return "";
            }
            final GridInfo<Map<String, Object>> catalogContents = catalogData.getCatalogContents();
            return catalogContents != null && catalogContents.getData().size() == 1 ? " readonly " : "";
        } else {
            return "";
        }
    }

    private String loadHtmlClasses(final SurveyField field) {
        return "${r} ${c} ${h}"
                .replace("${h}", loadHelpedIcon(field))
                .replace("${r}", loadRequiredField(field))
                .replace("${c}", loadExtraClassName(field));
    }

    private String loadHelpedIcon(final SurveyField field) {
        return field.getObj().getHelpUrl() == null ? "" : "helped-url-field";
    }
    
    private String loadRequiredField(final SurveyField field) {
        return isFieldRequired(field) ? "required" : "";
    }
    
    private boolean loadFindingField(final SurveyField field) {
        if (field.getObj().getAddFindingsEnabled() == null) {
            return false;
        }
        return field.getObj().getAddFindingsEnabled() == 1;
    }
    
    private boolean loadActivityField(final SurveyField field) {
        if (field.getObj().getAddActivitiesEnabled() == null) {
            return false;
        }
        return field.getObj().getAddActivitiesEnabled() == 1;
    }
    
    private boolean loadAutoSelectSingleAnswer(final SurveyField field) {
        if (field.getObj().getAutoSelectSingleAnswer() == null) {
            return false;
        }
        return field.getObj().getAutoSelectSingleAnswer() == 1;
    }
    
    private List<String> getTitles(final Collection<SurveyTextItem> items) {
        final List<String> results = new ArrayList<>(items.size());
        for (final SurveyTextItem text : items) {
            results.add(text.getTitle());
        }
        return results;
    }

    private Boolean showInput(final SurveyField field, final List<String> titles, final Integer x, final Integer y) {
        final String itemId = field.getObj().getField_id() + "_uiObject_array_" + x + "_col" + y;
        return !titles.contains(itemId);
    }

    private Map<String, FieldDTO> getCellsStash(
            final Collection<SurveyField> items, 
            final Collection<SurveyField> sortedFields,
            final FillHelper validator
    ) {
        final Map<String, FieldDTO> stash = new HashMap<>(items.size());
        for (final SurveyField item : items) {
            stash.put(item.getObj().getField_id(), getSurveyField(item, sortedFields, validator));
        }
        return stash;
    }

    private FieldDTO getCell(
            final FieldDTO field,
            final Map<String, FieldDTO> stash,
            final Integer x,
            final Integer y
    ) {
        final String itemId = field.getFieldId() + "_uiObject_array_" + x + "_" + y;
        return stash.get(itemId);
    }

    private Map<String, FieldDTO> getSubFieldStash(
            final Collection<SurveyField> items,
            final Collection<SurveyField> sortedFields,
            final FillHelper validator
    ) {
        final Map<String, FieldDTO> stash = new HashMap<>(items.size());
        for (final SurveyField item : items) {
            if (item.getObj().getLocation() != null) {
                stash.put(item.getObj().getLocation(), getSurveyField(item, sortedFields, validator));
            }
        }
        return stash;
    }

    private FieldDTO getSubField(
            final Map<String, FieldDTO> stash,
            final Integer x,
            final Integer y
    ) {
        final String compareable = x + "_" + y;
        return stash.get(compareable);
    }

    private String loadColumnClass(final SurveyField field) {
        return "columns small-" + (12 / (field.getObj().getHeaderTitles().size() + 1));
    }

    private String loadUnencodedTitle(final SurveyField field) {
        if (field == null 
                || field.getObj() == null 
                || field.getObj().getTitle() == null 
                || field.getObj().getTitle().isEmpty()
                || field.getObj().getTitle().get(0) == null
                || field.getObj().getTitle().get(0).getTitle() == null
                || field.getObj().getTitle().get(0).getTitle().isEmpty()) {
            return "";
        }
        final String title = field.getObj().getTitle().get(0).getTitle();
        if (title != null && !title.isEmpty()) {
            final String val;
            try {
                val = Utilities.URLDecoderDecode(title);
                return val.trim().equals("Clic para editar") ? "" : val;
            } catch (UnsupportedEncodingException ex) {
                getLogger().error("Invalid encoding for title {}", title, ex);
                return "INVALID-ENCODING-ERROR";
            }
        }
        return title;
    }

    private String loadEmptyCssTitle(final SurveyField field) {
        if (loadUnencodedTitle(field).isEmpty()) {
            return "displayNone";
        } else {
            return "";
        }
    }

    private String loadEmptyCssSubTitle(final SurveyField field) {
        if (loadUnencodedSubTitle(field).isEmpty()) {
            return "displayNone";
        } else {
            return "";
        }
    }

    private String loadUnencodedSubTitle(final SurveyField field) {
        if (field == null 
                || field.getObj() == null 
                || field.getObj().getSubTitle() == null 
                || field.getObj().getSubTitle().isEmpty()
                || field.getObj().getSubTitle().get(0) == null
                || field.getObj().getSubTitle().get(0).getTitle() == null
                || field.getObj().getSubTitle().get(0).getTitle().isEmpty()) {
            return "";
        }
        try {
            final String val;
            val = Utilities.URLDecoderDecode(field.getObj().getSubTitle().get(0).getTitle());
            return val.trim().equals("Clic para editar") ? "" : val;
        } catch (UnsupportedEncodingException ex) {
            getLogger().error("Invalid encoding for title {}", field.getObj().getSubTitle(), ex);
            return "INVALID-ENCODING-ERROR";
        }
    }
  

    private void defineCatalogData(
            final SurveyField field,
            final FillHelper validator, 
            final FieldDTO fieldDto
    ) {
        if (!SurveyField.TYPE_EXTERNAL_CATALOG.equals(field.getType())) {
            return;
        }
        final ExternalCatalogDataDTO catalogData = loadCatalogData(field, validator);
        if (Objects.equals(field.getObj().getCatalogSubType(), CatalogFieldType.CATALOG_HIERARCHY.toString())) {
            fieldDto.setSerializedCatalogData(Encode.forJavaScript(Utilities.getSerializedObj(catalogData, true, null)));
        }
        fieldDto.setCatalogData(catalogData);
    }
    
    private ExternalCatalogDataDTO loadCatalogData(
            final SurveyField field,
            final FillHelper validator
    ) {
        final ExternalCatalogDataDTO externalData = new ExternalCatalogDataDTO();
        final SurveyUtilCache cache = validator.getSurveyUtilCache();
        if (Objects.equals(field.getObj().getCatalogSubType(), CatalogFieldType.CATALOG_HIERARCHY.toString())) {
            final ExternalDocumentCatalogDTO externalCatalog = cache.getExternalCatalog(field.getObj().getExternalCatalogId());
            try {
                final IDatabaseQuery query = externalCatalog.getQuery();
                final List<HierarchyFieldDTO> fields = cache.getHierarchFields(field.getObj().getExternalCatalogId());
                final List<ExternalFieldDTO> hierarchyFields = fields.stream()
                        .map((column) -> mapHierarchyFieldDTO(field, column, query, validator, fields))
                        .collect(Collectors.toList());
                externalData.setHierarchyFields(hierarchyFields);
            } catch (final Exception ex) {
                getLogger().error("Failed load hierarcy fields {}", externalCatalog.getId(), ex);
                final GridInfo<Map<String, Object>> contents = parserHelper.getCatalogError(ex);
                final List<ExternalFieldDTO> levelFields = new ArrayList<>();
                final ExternalFieldDTO externalFieldDTO = new ExternalFieldDTO();
                externalFieldDTO.setCatalogContents(contents);
                levelFields.add(externalFieldDTO);
                externalData.setHierarchyFields(levelFields);
            }
        } else {
            final String requestMode = data.getRequestMode();
            if (SurveyRequestMode.PREVIEW.toString().equals(requestMode)) {
                externalData.setCatalogContents(Utilities.EMPTY_GRID_INFO);
            } else {
                final ExternalDocumentCatalogDTO externalCatalog = cache.getExternalCatalog(field.getObj().getExternalCatalogId());
                if (externalCatalog.getLabelColumn() != null) {
                    externalData.setPropertyLabel(externalCatalog.getLabelColumn().getCode());
                } else {
                    externalData.setPropertyLabel("label");
                }
                if (externalCatalog.getValueColumn() != null) {
                    externalData.setPropertyValue(externalCatalog.getValueColumn().getCode());
                } else {
                    externalData.setPropertyValue("value");
                }
                try {
                    final GridInfo<Map<String, Object>> contents = loadCatalogContents(field, externalCatalog, validator);
                    externalData.setCatalogContents(contents);
                } catch (final Exception ex) {
                    getLogger().error("Failed load external catalog {}", externalCatalog.getId(), ex);
                    final GridInfo<Map<String, Object>> contents = parserHelper.getCatalogError(ex);
                    externalData.setCatalogContents(contents);
                }
            }
        }
        return externalData;
    }
    
    private ExternalFieldDTO mapHierarchyFieldDTO(
            final SurveyField field,
            final HierarchyFieldDTO config,
            final IDatabaseQuery query,
            final FillHelper validator,
            final List<HierarchyFieldDTO> fields
    ) {
        final ExternalFieldDTO result = parserHelper.mapHierarchyFieldDTO(config);
        if (Objects.equals(config.getReadonly(), 1)) {
            return result;
        }
        boolean isFiller = validator.isFillAvailable(field, this.loggedUser);
        if (!isFiller) {
            result.setCatalogContents(Utilities.EMPTY_GRID_INFO);
            return result;
        }
        final GridFilter filter = new GridFilter();
        filter.setPage(0);
        filter.setPageSize(IPagedQuery.MAX_PAGED_RECORDS);
        filter.setDisableLoadThreshold(MAX_CATALOG_ITEMS);
        if (daoExternal == null) {
            daoExternal = getBean(IExternalDocumentCatalogDAO.class);
        }
        final ExternalCatalogValue catalogValue = parserHelper.getExternalCatalogValue(
                field,
                data.getOutstandingSurveys().getOutstandingSurveyId(),
                data.getOutstandingSurveys().getRequestorId(),
                loggedUser,
                daoExternal
        );
        if (queryDao == null) {
            queryDao = getBean(IDatabaseQueryDAO.class);
        }
        final GridInfo<Map<String, Object>> contents = parserHelper.loadHierarchyContents(
                filter,
                catalogValue,
                config.getLevel(), 
                query,
                fields,
                daoExternal,
                queryDao,
                loggedUser
        );
        result.setCatalogContents(contents);
        return result;
    }
    
    

    private GridInfo<Map<String, Object>> loadCatalogContents(
            final SurveyField field, 
            final ExternalDocumentCatalogDTO catalog,
            final FillHelper validator
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException {
        boolean isFiller = validator.isFillAvailable(field, this.loggedUser);
        if (!isFiller) {
            return Utilities.EMPTY_GRID_INFO;
        }
        final String catalogType = field.getObj().getCatalogType();
        switch (catalogType) {
            case SurveyParserHelper.INTERNAL_CATALOG_ID:
                if (daoInternal == null) {
                    daoInternal = getBean(IInternalDocumentCatalogItemDAO.class);
                }
                return daoInternal.getCatalogContents(field.getObj().getInternalCatalogId());
            case SurveyParserHelper.EXTERNAL_CATALOG_ID:
                return loadExternalCatalogContents(field, catalog);
        }
        return null;
    }

    private GridInfo<Map<String, Object>> loadExternalCatalogContents(
            final SurveyField field, 
            final ExternalDocumentCatalogDTO catalog
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException {
        final GridFilter filter = new GridFilter();
        filter.setDirection((byte) 1);
        filter.setPage(0);
        filter.setDisableLoadThreshold(MAX_CATALOG_ITEMS);
        if (daoExternal == null) {
            daoExternal = getBean(IExternalDocumentCatalogDAO.class);
        }
        return parserHelper.loadExternalCatalogContents(
                field,
                catalog,
                filter,
                data.getOutstandingSurveys().getOutstandingSurveyId(),
                data.getOutstandingSurveys().getRequestorId(),
                daoExternal, 
                loggedUser
        );
    }

    private String loadClassLayout(final SurveyField field) {
        if (SurveyField.TYPE.EXTERNAL_CATALOG.getValue().equals(field.getType())) {
            return "vertical";
        }
        return "vertical".equals(field.getObj().getLayout()) ? "vertical" : "horizontal";
    }

    private Boolean scoreContainerInside(final SurveyField field) {
        switch (field.getType()) {
            case SurveyField.TYPE_UNA_RESPUESTA:
            case SurveyField.TYPE_OPCION_MULTIPLE:
                return field.getObj().getLayout().equals("horizontal");
            case SurveyField.TYPE_ABIERTA_RENGLONES:
                return field.getObj().getLayout().equals("vertical");
            default:
                return false;
        }
    }

    
    private String loadDefaultValue(final SurveyField field) {
        if (SurveyField.DEFAULT_DATE_TYPE.APPROVAL_FORM_DATE.equals(field)
                || SurveyField.DEFAULT_DATE_TYPE.FILLING_REQUEST_DATE.equals(field)) {
            return "defaultValue";
        }
        return Utilities.EMPTY_STRING;
    }

    private String loadFillEntityName(final SurveyField field) {
        if (field.getObj().getFillEntity() == null) {
            return "invalid";
        }
        switch (field.getObj().getFillEntity()) {
            case "business-unit-position":
                return field.getObj().getBusinessUnitPosition();
            case "organizational-unit-position":
                return field.getObj().getOrganizationalUnitPosition();
            case "user":
                return field.getObj().getUser();
            case "boss":
                return "boss";
        }
        return "invalid";

    }

    private String loadEditableSections(final SurveyField field) {
        final List<SurveyFieldEditable> editables = field.getObj().getSurveyFieldObjectEditables();
        if (editables == null || editables.isEmpty()) {
            return "";
        }
        final List<String> fields = new ArrayList<>(editables.size());
        for (final SurveyFieldEditable editable : editables) {
            fields.add(editable.getId().getEditableSectionId().toString());
        }
        return StringUtils.join(fields, ",");
    }

    private String loadExtraClassName(final SurveyField field) {
        return this.getAnswerTypeClassName(field.getObj().getAnswerType());
    }
   
    private String getAnswerTypeClassName(Integer answerType) {
        final SurveyFieldAnswerType fieldAnswerType = SurveyFieldAnswerType.fromValue(answerType);
        return fieldAnswerType == null ? "" : fieldAnswerType.getName();
    }

    private List<FieldDTO> filterFields(final List<FieldDTO> fields, final String formArea) {
        final List<FieldDTO> surveyFields = new ArrayList<>(fields.size());
        for (final FieldDTO field : fields) {
            final String fArea = field.getFormArea();
            //condicion para documentos electronicos
            if (formArea.equals(fArea)
                    //condicion para cuestionarios regulares
                    || (fArea == null && FORM_AREA.equals(formArea))) {
                surveyFields.add(field);
            }
        }
        return surveyFields;
    }

    private List<FieldDTO> getSurveyFields(final List<SurveyField> sortedFields, final FillHelper validator) {
        if (sortedFields != null && !sortedFields.isEmpty()) {
            int currentAuthIndex = 0;
            final List<FieldDTO> surveyFields = new ArrayList<>(sortedFields.size());
            for (final SurveyField field : sortedFields) {
                final FieldDTO surveyField = getSurveyField(field, sortedFields, validator);
                if (surveyField.getType().equals(SurveyField.TYPE_SECCION) || surveyField.getType().equals(SurveyField.TYPE_SIGNATURE)) {
                    currentAuthIndex++;
                }
                surveyField.setAuthorizationPoolIndex(currentAuthIndex);
                // Si no tiene outstanding business unit solo el admin puede editar el usuario definido
                if (evaluateShowChangeDefinedUserOnlyAdmin) {
                    surveyField.setCanChangeUserReponsibleDefined(evaluateCanChangeUserDefinedLikeAdmin(field, surveyField));
                } else {
                    // si tiene outstanding business unit el admin o los usuarios responsables del businessUnitDepartmentId pueden cambiar el usuario definido
                    surveyField.setCanChangeUserReponsibleDefined(evaluateCanChangeUserDefinedLikeAdminOrResponsibleOf(field, surveyField));
                }
                surveyFields.add(surveyField);
            }
            return surveyFields;
        }
        return Utilities.EMPTY_LIST;
    }

    @Nonnull
    private List<SurveyField> sortFields(final Collection<SurveyField> fields) {
        if (fields == null) {
            return new ArrayList<>(0);
        }
        final List<SurveyField> sortedFields = new ArrayList<>(fields);
        sortedFields.sort(new FieldComparator());
        return sortedFields;
    }

    private FieldDTO getSurveyField(
            final SurveyField field, 
            final Collection<SurveyField> sortedFields,
            final FillHelper validator
    ) {
        final FieldDTO fieldDto = new FieldDTO();        
        if (Objects.equals("preview", this.getTask()) && SurveyField.TYPE_SECCION.equals(field.getType()) && field.getObj().getRequestAdjust()) {
            this.evaluateCanRequestAdjust(fieldDto, validator, field);
        } else {
            fieldDto.setCanRequestAdjust(false);
        }
        fieldDto.setId(field.getId());
        fieldDto.setObjId(field.getObj().getId());
        fieldDto.setType(field.getType());
        fieldDto.setCatalogSubType(field.getObj().getCatalogSubType());
        fieldDto.setParamUrlFiller(field.getObj().getParamUrlFiller());
        fieldDto.setStage(field.getObj().getStage());
        if (field.getObj().getHelpUrl() != null && !field.getObj().getHelpUrl().trim().isEmpty()) {
            fieldDto.setHelpIcon(field.getObj().getHelpIcon());
            fieldDto.setHelpUrl(field.getObj().getHelpUrl());
        }
        fieldDto.setOrder(field.getObj().getOrder());
        fieldDto.setFieldId(field.getObj().getField_id());
        fieldDto.setFormArea(field.getObj().getFormArea());
        fieldDto.setQuestionNumber(loadQuestionNumber(sortedFields, field));
        fieldDto.setCaptureField(loadCaptureField(field));
        fieldDto.setFindingEnabled(loadFindingField(field));
        fieldDto.setActivityEnabled(loadActivityField(field));
        fieldDto.setAutoSelectSingleAnswer(loadAutoSelectSingleAnswer(field));
        
        fieldDto.setConditionalQuestion(field.getObj().getConditionalQuestion());
        updateConditionals(field.getObj().getConditionals(), fieldDto, SurveyConditionalType.CONDITIONAL.getValue());
        
        fieldDto.setSummationQuestion(field.getObj().getSummationFields() != null && !field.getObj().getSummationFields().isEmpty() ? "t" : "f");
        updateConditionals(field.getObj().getSummationFields(), fieldDto, SurveyConditionalType.SUMMATION.getValue());

        fieldDto.setSelectedWeightedField(field.getObj().getSelectedWeightedField());
        updateConditionals(field.getObj().getWeightedFields(), fieldDto, SurveyConditionalType.WEIGHTS.getValue());

        fieldDto.setConditionalExpirationQuestion(field.getObj().getConditionalExpirationQuestion());
        updateConditionals(field.getObj().getConditionalsExpiration(), fieldDto, SurveyConditionalType.EXPIRATION.getValue());
        
        fieldDto.setActivitiesTemplateEnabled(field.getObj().getActivitiesTemplateEnabled());
        fieldDto.setActivitiesTemplateId(field.getObj().getActivitiesTemplateId());
        
        fieldDto.setActivitiesTemplateImplementerEntity(field.getObj().getActivitiesTemplateImplementerEntity());        
        fieldDto.setActivityImplementerBusinessUnitPositionId(field.getObj().getActivityImplementerBusinessUnitPositionId());
        fieldDto.setActivityImplementerBusinessUnitPositionLabel(field.getObj().getActivityImplementerBusinessUnitPositionLabel());
        fieldDto.setActivityImplementerOrganizationalUnitPositionId(field.getObj().getActivityImplementerOrganizationalUnitPositionId());
        fieldDto.setActivityImplementerOrganizationalUnitPositionLabel(field.getObj().getActivityImplementerOrganizationalUnitPositionLabel());
        fieldDto.setActivityImplementerUserId(field.getObj().getActivityImplementerUserId());
        fieldDto.setActivityImplementerUserLabel(field.getObj().getActivityImplementerUserLabel());
        
        fieldDto.setActivitiesTemplateVerifierEntity(field.getObj().getActivitiesTemplateVerifierEntity());
        fieldDto.setActivityVerifierBusinessUnitPositionId(field.getObj().getActivityVerifierBusinessUnitPositionId());
        fieldDto.setActivityVerifierBusinessUnitPositionLabel(field.getObj().getActivityVerifierBusinessUnitPositionLabel());
        fieldDto.setActivityVerifierOrganizationalUnitPositionId(field.getObj().getActivityVerifierOrganizationalUnitPositionId());
        fieldDto.setActivityVerifierOrganizationalUnitPositionLabel(field.getObj().getActivityVerifierOrganizationalUnitPositionLabel());
        fieldDto.setActivityVerifierUserId(field.getObj().getActivityVerifierUserId());
        fieldDto.setActivityVerifierUserLabel(field.getObj().getActivityVerifierUserLabel());
        
        
        fieldDto.setTypeRules(loadTypeRules(field));
        fieldDto.setHtmlClasses(loadHtmlClasses(field));
        fieldDto.setRequiredField(loadRequiredField(field));
        fieldDto.setColumnClass(loadColumnClass(field));
        fieldDto.setUnencodedTitle(loadUnencodedTitle(field));
        fieldDto.setEmptyCssTitle(loadEmptyCssTitle(field));
        fieldDto.setEmptyCssSubTitle(loadEmptyCssSubTitle(field));
        fieldDto.setUnencodedSubTitle(loadUnencodedSubTitle(field));
        fieldDto.setFieldRequired(isFieldRequired(field));
        defineCatalogData(field, validator, fieldDto);
        fieldDto.setReadonlyClass(loadReadOnlyClass(fieldDto));
        fieldDto.setClassLayout(loadClassLayout(field));
        fieldDto.setScoreContainerInside(scoreContainerInside(field));
        fieldDto.setClassDefaultValue(loadDefaultValue(field));
        fieldDto.setFillEntityName(loadFillEntityName(field));
        fieldDto.setFillUserId(field.getObj().getUserId());
        fieldDto.setEditableSections(loadEditableSections(field));
        fieldDto.setAnswerType(field.getObj().getAnswerType());
        fieldDto.setTypeNumber(isAnswerTypeNumberic(field));
        fieldDto.setExtraClassName(loadExtraClassName(field));
        fieldDto.setOtherOptionLabel(loadOtherOptionLabel(field));
        SurveyTextItem otherOptionsInfo = field.getObj().getOtherOptionText().stream().findAny().orElse(null);
        if (otherOptionsInfo != null) {
            fieldDto.setOtherOptionDefaultValue(otherOptionsInfo.getDefaultValue());
            fieldDto.setOtherOptionAnswerType(otherOptionsInfo.getAnswerType());
            fieldDto.setOtherExtraClassName(getAnswerTypeClassName(otherOptionsInfo.getAnswerType()));
        }
        fieldDto.setDefaultValue(field.getObj().getDefaultValue());
        fieldDto.setLayout(field.getObj().getLayout());
        fieldDto.setOtherOption(field.getObj().getOtherOption());
        fieldDto.setFillEntity(field.getObj().getFillEntity());
        fieldDto.setCanCancel(field.getObj().getCanCancel());
        fieldDto.setSignRejectApproval(field.getObj().getSignRejectApproval());
        fieldDto.setAllowDeleteStopwatchRecord(field.getObj().getAllowDeleteStopwatchRecord());
        fieldDto.setDaysToExpire(field.getObj().getDaysToExpire());
        fieldDto.setDaysToNotifyBeforeExpiration(field.getObj().getDaysToNotifyBeforeExpiration());
        fieldDto.setIncludeInMail(field.getObj().getIncludeInMail());
        fieldDto.setIncludeInSubject(field.getObj().getIncludeInSubject());
        fieldDto.setAttendProgressStateId(field.getObj().getAttendProgressStateId());
        fieldDto.setImageSizePreview(field.getObj().getImageSizePreview());
        fieldDto.setSize(field.getObj().getSize());
        fieldDto.setWeightingType(field.getObj().getWeightingType());
        fieldDto.setDefaultDateValue(field.getObj().getDefaultDateValue());
        fieldDto.setIncludeTime(Objects.equals("t", field.getObj().getIncludeTime()));
        fieldDto.setRestrictPastDates(Objects.equals("t", field.getObj().getRestrictPastDates()));
        fieldDto.setMaxPastHours(field.getObj().getMaxPastHours());
        fieldDto.setPageNumber(field.getObj().getPageNumber());
        fieldDto.setSectionFieldId(field.getObj().getSectionFieldId());
        fieldDto.setPivotTableOnMobile(field.getObj().getPivotTableOnMobile());
        fieldDto.setFixedQrUrl(field.getObj().getFixedQrUrl());
        fieldDto.setQrUrlType(field.getObj().getQrUrlType());
        fieldDto.setHidden(field.getObj().getHidden());
        fieldDto.setAllowNegativeValues(field.getObj().getAllowNegativeValues());
        fieldDto.setAllowMobileUploadingFrom(field.getObj().getAllowMobileUploadingFrom());
        if (QrUrlType.DYNAMIC.equals(QrUrlType.fromValue(field.getObj().getQrUrlType()))) {
            final MailTokenManager tokenManager = new MailTokenManager(getBean(ITokenProvider.class));
            if (formDao == null) {
                formDao = getBean(IFormCaptureDAO.class);
            }
            String url = Encode.forUri("v.request.survey.fill.view?requestMode=REQUESTOR&documentMasterId=" + formDao.findMasterIdBySurveyId(field.getObj().getSurveyId()));
            url = tokenManager.generateUrl(loggedUser, url);
            fieldDto.setFixedQrUrl(url);
        }
        fieldDto.setConfigureShowSummationField(field.getObj().getConfigureShowSummationField());
        if(field.getObj().getTema() != null && field.getObj().getTema().getType() != null) {
            fieldDto.setTema(field.getObj().getTema().getType().getDescription() + " - " + field.getObj().getTema().getDescription());
        }
        updateArrayItems(field, fieldDto, validator);
        addItems(field, fieldDto, validator);
        addHeaders(field, fieldDto);
        defineFieldCurrencyConversion(field, fieldDto, validator);
        return fieldDto;
    }

    private void addItems(final SurveyField field, final FieldDTO fieldDto, final FillHelper validator) {
        final List<SurveyItem> items = field.getObj().getItems();
        if (items.isEmpty()) {
            return;
        }
        fieldDto.setItems(new ArrayList<>(items.size()));
        for (final SurveyItem surveyItem : items) {
            final ItemDTO itemDto = new ItemDTO();
            itemDto.setId(surveyItem.getId());
            itemDto.setCode(surveyItem.getCode());
            final String title = surveyItem.getItemText().get(0).getTitle();
            itemDto.setTitle(title);
            itemDto.setWeight(surveyItem.getPonderacion());
            itemDto.setTableTitle(emptyTableTitle(title));
            itemDto.setZeroLabel(loadZeroLabel(surveyItem.getItemText()));
            itemDto.setReportActivityCsss(loadReportActivityCss(surveyItem));
            itemDto.setDefaultValue(surveyItem.getDefaultValue());
            itemDto.setHtmlClasses(getAnswerTypeClassName(surveyItem.getAnswerType()));
            itemDto.setAnswerType(surveyItem.getAnswerType());
            itemDto.setTypeNumber(isAnswerTypeNumberic(surveyItem.getAnswerType()));
            itemDto.setSummation(surveyItem.getSummation());
            defineItemCurrencyConversion(field, surveyItem, itemDto, validator);
            itemDto.setAllowNegativeValues(surveyItem.getAllowNegativeValues());
            fieldDto.getItems().add(itemDto);
        }
        final boolean countItemsWithSummation = items.stream().anyMatch(item -> item.getSummation() != null && item.getSummation() == 1);
        if (countItemsWithSummation) {
            fieldDto.setSummation(1);
        }
    }
    
    private String emptyTableTitle(final String title) {
        if (title == null || title.trim().isEmpty()) {
            return "displayNone";
        } else {
            return "";
        }
    }

    private String loadZeroLabel(final List<SurveyTextItem> itemText) {
        return SurveyUtil.getZeroLabel(itemText);
    }

    private String loadReportActivityCss(final SurveyItem surveyItem) {
        final Long reportActivity = surveyItem.getReportActivity();
        return reportActivity != null && reportActivity == 1L ? " ReportActivity " : "";
    }

    private String loadOtherOptionLabel(final SurveyField field) {
        return SurveyUtil.getPlainLabel(field.getObj().getOtherOptionText(), null);
    }

    private void updateArrayItems(
            final SurveyField field,
            final FieldDTO fieldDto,
            final FillHelper validator
    ) {
        final List<SurveyItem> items = field.getObj().getItems();
        final List<SurveyHeader> headerTitles = field.getObj().getHeaderTitles();
        if (headerTitles.isEmpty() || items.isEmpty()) {
            return;
        }

        updateShowItems(field, fieldDto, items, headerTitles);
        updateCells(field, fieldDto, items, headerTitles, validator);
        updateSubFields(field, fieldDto, items, headerTitles, validator);
    }
    
    private void updateConditionals(final Set<SurveyConditional> surveyConditionals, final FieldDTO fieldDto, Integer type) {
        if (surveyConditionals == null || surveyConditionals.isEmpty()) {
            return;
        }
        final List<SurveyConditionalDTO> conditionals = surveyConditionals.stream()
                .map((conditional) -> new SurveyConditionalDTO(
                        conditional.getConditionalType(),
                        data.getSurvey().getId(),
                        conditional.getConditionalAnswerSelector(),
                        conditional.getConditionalQuestionId(),
                        conditional.getConditionalAnswerId(),
                        conditional.getHierarchyRow(),
                        conditional.getHierarchyColumn(),
                        conditional.getHierarchyValue(),
                        conditional.getDaysToExpire(),
                        conditional.getDaysToNotifyBeforeExpiration(),
                        conditional.getMaxLim(),
                        conditional.getMinLim(),
                        conditional.getEqLim()
                ))
                .collect(Collectors.toList());
        if (Objects.equals(type, SurveyConditionalType.CONDITIONAL.getValue())){
            fieldDto.setConditionals(conditionals);
            fieldDto.setSerializedConditionals(Encode.forJavaScript(Utilities.getSerializedObj(conditionals, true, null)));    
        }
        if (Objects.equals(type, SurveyConditionalType.SUMMATION.getValue())){
            fieldDto.setSummationFields(conditionals);
            fieldDto.setSerializedConditionalsSummation(Encode.forJavaScript(Utilities.getSerializedObj(conditionals, true, null)));
        }
        if (Objects.equals(type, SurveyConditionalType.WEIGHTS.getValue())){
            fieldDto.setWeightedFields(conditionals);
            fieldDto.setSerializedConditionalWeightedFields(Encode.forJavaScript(Utilities.getSerializedObj(conditionals, true, null)));
        }
        if (Objects.equals(type, SurveyConditionalType.EXPIRATION.getValue())){
            fieldDto.setConditionalsExpiration(conditionals);
            fieldDto.setSerializedConditionalsExpiration(Encode.forJavaScript(Utilities.getSerializedObj(conditionals, true, null)));
        }
    }


    private void updateShowItems(
            final SurveyField field,
            final FieldDTO fieldDto,
            final Collection<SurveyItem> items,
            final Collection<SurveyHeader> headerTitles
    ) {

        final String type = fieldDto.getType();
        final boolean hasInputs = SurveyField.TYPE_MATRIZ_MULTIPLE.equals(type)
                || SurveyField.TYPE_MATRIZ.equals(type);
        if (!hasInputs) {
            return;
        }

        final Table<Integer, Integer, Boolean> showInputs = HashBasedTable.create();
        final List<String> titles = getTitles(field.getObj().getHiddenMatrixItems());

        final int rowsSize = items.size();
        final int columnsSize = headerTitles.size();
        for (int x = 0; x < rowsSize; x++) {
            for (int y = 0; y < columnsSize; y++) {
                final Boolean showInput = showInput(field, titles, x, y);
                showInputs.put(x, y, showInput);
            }
        }
        fieldDto.setShowInputs(showInputs);
    }

    private void updateCells(
            final SurveyField field,
            final FieldDTO fieldDto,
            final Collection<SurveyItem> items,
            final Collection<SurveyHeader> headerTitles,
            final FillHelper validator
    ) {
        final String type = fieldDto.getType();
        final boolean hasCells = SurveyField.TYPE_TABLE_FIELD.equals(type);
        if (!hasCells) {
            return;
        }

        final Table<Integer, Integer, FieldDTO> cells = HashBasedTable.create();
        final List<SurveyField> sortedFields = sortFields(field.getObj().getItemStash());
        final Map<String, FieldDTO> cellStash = getCellsStash(field.getObj().getItemStash(), sortedFields, validator);

        final int rowsSize = items.size();
        final int columnsSize = headerTitles.size();
        for (int x = 0; x < rowsSize; x++) {
            for (int y = 0; y < columnsSize; y++) {
                final FieldDTO cell = getCell(fieldDto, cellStash, x, y);
                if (cell != null) {
                    cells.put(x, y, cell);
                }
            }
        }
        fieldDto.setCells(cells);
    }

    private void updateSubFields(
            final SurveyField field,
            final FieldDTO fieldDto,
            final Collection<SurveyItem> items,
            final Collection<SurveyHeader> headerTitles,
            final FillHelper validator
    ) {
        final String type = field.getType();
        final boolean hasSubFields = SurveyField.TYPE_FIELD_ARRAY.equals(type);
        if (!hasSubFields) {
            return;
        }

        final Table<Integer, Integer, FieldDTO> subFields = HashBasedTable.create();
        final List<SurveyField> sortedFields = sortFields(field.getObj().getItemStash());
        final Map<String, FieldDTO> subFieldStash = getSubFieldStash(field.getObj().getItemStash(), sortedFields, validator);

        final int rowsSize = items.size();
        final int columnsSize = headerTitles.size();
        for (int x = 0; x < rowsSize; x++) {
            for (int y = 0; y < columnsSize; y++) {
                final FieldDTO subField = getSubField(subFieldStash, x, y);
                if (subField != null) {
                    subFields.put(x, y, subField);
                }
            }
        }
        fieldDto.setSubFields(subFields);
    }

    private String getLocale() {
        final String originalLocale = Utilities.getLocaleString();
        final String[] localeParts = originalLocale.split("-");
        if (localeParts.length == 2) {
            final String localeLang = localeParts[0];
            return localeLang + "-mx";
        }
        return originalLocale;
    }

    private void addHeaders(final SurveyField field, final FieldDTO fieldDto) {
        final List<SurveyHeader> headers = field.getObj().getHeaderTitles();
        if (headers.isEmpty()) {
            return;
        }
        fieldDto.setHeaders(new ArrayList<>(headers.size()));
        for (final SurveyHeader surveyHeader : headers) {
            final HeaderDTO header = new HeaderDTO();
            header.setId(surveyHeader.getId());
            header.setCode(surveyHeader.getCode());
            header.setWeight(surveyHeader.getPonderacion());
            if (surveyHeader.getTitle() != null && !surveyHeader.getTitle().isEmpty()) {
                header.setTitle(surveyHeader.getTitle().get(0).getTitle());
            } else {
                header.setTitle("");
            }
            fieldDto.getHeaders().add(header);
        }
    }

    @Nonnull
    public SurveyDTO getSurveyData() {
        return surveyData;
    }

    private Boolean evaluateCanChangeUserDefinedLikeAdminOrResponsibleOf(
            final SurveyField field,
            final FieldDTO surveyField
    ) {
        Long currentObjectId = field.getObj().getId();
        boolean requestOpen = Boolean.TRUE.equals(isRequestOpen());
        if (field.getObj().getFillEntity() == null
                || !SurveyFillEntity.USER_TO_BE_DEFINED.getName().equals(field.getObj().getFillEntity())
                || getUserDefinedUtilData() == null
                || getUserDefinedUtilData().isEmpty()
                || getResponsiblesUsersOf().isEmpty()
                || hasAnyFormRequestInProgress()
                || hasAnyFormCancelInProgress()
                || !requestOpen
        ) {
            return false;
        }
        List<ChangeUserDefinedUtilDataDTO> currentUserDefinedToFillData = 
                getUserDefinedUtilData().stream().filter(f -> f.getFieldObjectId().equals(currentObjectId)).collect(Collectors.toList());

        if (currentUserDefinedToFillData.isEmpty()) {
            return false;
        }
        
        Long userDefinedForFill = currentUserDefinedToFillData.get(0).getUserDefinedToFillId();
        if (userDefinedForFill == null || userDefinedForFill == 0) {
            return false;
        }
        surveyField.setCurrentUserImageDefinedToFill("./../view/v-application-avatar.view?id=" + userDefinedForFill);
        surveyField.setUserDefinedToFill(userDefinedForFill);
        if (loggedUser.isAdmin() || loggedUser.getServices().contains(ProfileServices.ADMON_SISTEMA)) {
            return currentObjectId.equals(currentUserDefinedToFillData.get(0).getFieldObjectId());
        } else {
            return !loggedUser.getId().equals(userDefinedForFill)
                    && this.getResponsiblesUsersOf().stream().anyMatch(p -> p.getId().equals(loggedUser.getId()))
                    && currentObjectId.equals(currentUserDefinedToFillData.get(0).getFieldObjectId());
        }
    }

    private Boolean isRequestOpen() {
        final Integer requestStatus = data.getRequestStatus();
        return Objects.equals(requestStatus, Request.STATUS_STANDBY)
                || Objects.equals(requestStatus, Request.STATUS_RETURNED)
                || Objects.equals(requestStatus, Request.STATUS_APROVING)
                || Objects.equals(requestStatus, Request.STATUS_VERIFING);
    }
    
    private Boolean evaluateCanChangeUserDefinedLikeAdmin(
            final SurveyField field,
            final FieldDTO surveyField
    ) {
        Long currentObjectId = field.getObj().getId();
        boolean requestOpen = Boolean.TRUE.equals(isRequestOpen());
        if (field.getObj().getFillEntity() == null 
                || !SurveyFillEntity.USER_TO_BE_DEFINED.getName().equals(field.getObj().getFillEntity())
                || getUserDefinedUtilData() == null
                || getUserDefinedUtilData().isEmpty()
                || hasAnyFormRequestInProgress()
                || hasAnyFormCancelInProgress()
                || !requestOpen
        ) {
            return false;
        }
        List<ChangeUserDefinedUtilDataDTO> currentUserDefinedToFillData = 
                getUserDefinedUtilData().stream().filter(f -> f.getFieldObjectId().equals(currentObjectId)).collect(Collectors.toList());
        
        if (currentUserDefinedToFillData.isEmpty()) {
            return false;
        }
        
        Long userDefinedForFill = currentUserDefinedToFillData.get(0).getUserDefinedToFillId();
        if (userDefinedForFill == null || userDefinedForFill == 0) {
            return false;
        }
        surveyField.setCurrentUserImageDefinedToFill("./../view/v-application-avatar.view?id=" + userDefinedForFill);
        surveyField.setUserDefinedToFill(userDefinedForFill);
        if (!currentObjectId.equals(currentUserDefinedToFillData.get(0).getFieldObjectId())) {
            return false;
        }
        return loggedUser.isAdmin() || loggedUser.getServices().contains(ProfileServices.ADMON_SISTEMA);
    }

    private void evaluateCanRequestAdjust(final FieldDTO fieldDto, final FillHelper validator, final SurveyField field) {
        if (validator.getOutstandingQuestions() == null) {
            fieldDto.setCanRequestAdjust(false);
            return;
        }
        final Long sectionFilledAuthPoolIndex = validator.getOutstandingQuestions()
                .stream()
                .filter(f -> f.getFieldId().equals(field.getId()) && Objects.nonNull(f.getFilledAutorizationPoolIndex()))
                .map(IOutstandingQuestion::getFilledAutorizationPoolIndex)
                .findAny()
                .orElse(0L);
        final Long currentAutorizationPoolIndex = data.getCurrentAutorizationPoolIndex();
        if (currentAutorizationPoolIndex != null && sectionFilledAuthPoolIndex <= currentAutorizationPoolIndex) {
            final String fieldId = field.getObj().getField_id();
            final Long fillerUserId = validator.getFilledUserByFieldId(fieldId);
            final boolean currentUserIsFiller = Objects.equals(fillerUserId, loggedUser.getId());
            fieldDto.setCanRequestAdjust(currentUserIsFiller || (loggedUser.isAdmin() && fillerUserId != null));
        } else {
            fieldDto.setCanRequestAdjust(false);
        }
    }

    private boolean isFieldRequired(final SurveyField field) {
        if (SurveyField.TYPE_FORM_WEIGHTING_RESULT.equals(field.getType())) {
            return false;
        }
        return "t".equals(field.getObj().getRequired());
    }

    private Boolean isAnswerTypeNumberic(final SurveyField field) {
        if (SurveyField.TYPE_FORM_WEIGHTING_RESULT.equals(field.getType())) {
            return true;
        }
        return isAnswerTypeNumberic(field.getObj().getAnswerType());
    }

    private Boolean isAnswerTypeNumberic(final Integer answerType) {
        return SurveyFieldAnswerType.DECIMAL.getValue().equals(answerType)
                || SurveyFieldAnswerType.NUMERIC.getValue().equals(answerType)
                || SurveyFieldAnswerType.CURRENCY.getValue().equals(answerType)
                || SurveyFieldAnswerType.CURRENCY_USD_TO_MXN.getValue().equals(answerType);
    }


    private Boolean isCurencyConversion(final Integer answerType) {
        return SurveyFieldAnswerType.CURRENCY_USD_TO_MXN.getValue().equals(answerType);
    }

    private void updateCurrencyConversionData(
            final ICurrencyConvertable record,
            final FillHelper validator
    ) {
        record.setInputType("hidden");
        record.setTypeCurrency(true);
        record.setTypeCurrencyConversion(true);
        final ExchangeRateDTO exchangeRate = validator.getCurrentExchangeRate();
        record.setExchangeRateId(exchangeRate.getId());
        if (exchangeRate.getSyncDate() != null) {
            record.setExchangeRateDate(Utilities.formatDate(exchangeRate.getSyncDate()));
        }
        if (exchangeRate.getConversionRate() != null) {
            Double v = Utilities.formatDouble(exchangeRate.getConversionRate(), 2);
            if (v != null) {
                record.setExchangeRateValue(v.toString());
            } else {
                record.setExchangeRateValue("0.00");
            }
        }
    }

    private void defineFieldCurrencyConversion(final SurveyField field, final FieldDTO fieldDto, final FillHelper validator) {
        if (SurveyFieldAnswerType.CURRENCY.getValue().equals(field.getObj().getAnswerType())) {
            fieldDto.setTypeCurrency(true);
            fieldDto.setTypeCurrencyConversion(false);
            fieldDto.setInputType("hidden");
            return;
        } else if (!isCurencyConversion(field.getObj().getAnswerType())) {
            fieldDto.setTypeCurrency(false);
            fieldDto.setTypeCurrencyConversion(false);
            fieldDto.setInputType("number");
            return;
        }
        try {
            updateCurrencyConversionData(fieldDto, validator);
            final FieldDTO currencyConversion = (FieldDTO) fieldDto.clone();
            fieldDto.setCurrencyConversion(currencyConversion);
        } catch (final CloneNotSupportedException e) {
            getLogger().error(
                    "Failed to clone field {} for currency conversion",
                    field.getId()
            );
            fieldDto.setTypeCurrency(false);
            fieldDto.setTypeCurrencyConversion(false);
        }

    }

    private void defineItemCurrencyConversion(final SurveyField field, final SurveyItem item, final ItemDTO itemDto, final FillHelper validator) {
        if (SurveyFieldAnswerType.CURRENCY.getValue().equals(item.getAnswerType())) {
            itemDto.setTypeCurrency(true);
            itemDto.setTypeCurrencyConversion(false);
            itemDto.setInputType("hidden");
            return;
        } else if (!isCurencyConversion(item.getAnswerType())) {
            itemDto.setTypeCurrency(false);
            itemDto.setTypeCurrencyConversion(false);
            itemDto.setInputType("number");
            return;
        }
        try {
            updateCurrencyConversionData(itemDto, validator);
            final ItemDTO currencyConversion = (ItemDTO) itemDto.clone();
            itemDto.setCurrencyConversion(currencyConversion);
        } catch (final CloneNotSupportedException e) {
            getLogger().error(
                    "Failed to clone field {} item {} for currency conversion",
                    field.getId(),
                    item.getId()
            );
            itemDto.setTypeCurrency(false);
            itemDto.setTypeCurrencyConversion(false);
        }
    }

    @Nonnull
    public List<UserRef> getResponsiblesUsersOf() {
        return responsiblesUsersOf;
    }

    public void setResponsiblesUsersOf(@Nonnull List<UserRef> responsiblesUsersOf) {
        this.responsiblesUsersOf = responsiblesUsersOf;
    }

    @Nullable
    public List<ChangeUserDefinedUtilDataDTO> getUserDefinedUtilData() {
        return userDefinedUtilData;
    }

    public void setUserDefinedUtilData(@Nullable List<ChangeUserDefinedUtilDataDTO> userDefinedUtilData) {
        this.userDefinedUtilData = userDefinedUtilData;
    }

    public Integer getRequestStatus() {
        return data.getRequestStatus();
    }

    public String getTask() {
        return data.getTask();
    }


    public Long getCurrentAutorizationPoolIndex() {
        return data.getCurrentAutorizationPoolIndex();
}


    private Boolean hasAnyFormRequestInProgress() {
        final Short outstandingSurveyStatus = data.getOutstandingSurveys().getEstatus();
        return outstandingSurveyStatus != null && OutstandingSurveys.STATUS.RETURN_APPROVAL.getValue().equals(outstandingSurveyStatus.intValue());
    }

    private Boolean hasAnyFormCancelInProgress() {
        final Short outstandingSurveyStatus = data.getOutstandingSurveys().getEstatus();
        return outstandingSurveyStatus != null && OutstandingSurveys.STATUS.CANCEL_APPROVAL.getValue().equals(outstandingSurveyStatus.intValue());
    }

}
    
