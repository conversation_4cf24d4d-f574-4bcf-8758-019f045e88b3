package qms.survey.logic;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import mx.bnext.access.Module;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.form.conditional.ConditionalValidator;

public class ConditionalValidatorCache {

    private static final Logger LOGGER = Loggable.getLogger(ConditionalValidatorCache.class);

    private static final ConditionalValidatorCache INSTANCE = new ConditionalValidatorCache();

    private final LoadingCache<ConditionalCacheKey, ConditionalInfoForSession> cache = CacheBuilder.newBuilder()
            .expireAfterAccess(24, TimeUnit.HOURS)
            .maximumSize(1000)
            .build(CacheLoader.from(this::createInstance));

    private ConditionalValidatorCache() {
    }

    @Nonnull
    public static ConditionalValidatorCache getInstance() {
        if (INSTANCE == null) {
            LOGGER.warn("Conditional validator cache instance is null, creating a new one");
            return new ConditionalValidatorCache();
        }
        return INSTANCE;
    }

    @Nonnull
    public static ConditionalValidator loadInstance(
            final String mode,
            final String surveyType,
            final Long outstandingSurveyId,
            final String conditionalValidatorCacheId
    ) {
        final ConditionalValidatorCache instance = ConditionalValidatorCache.getInstance();
        final SurveyRequestMode request = SurveyRequestMode.parse(mode);
        final ConditionalValidator validator = instance.get(
                outstandingSurveyId,
                request,
                conditionalValidatorCacheId,
                Module.AUDIT.getKey().equals(surveyType) ? Module.AUDIT : Module.FORMULARIE
        );
        validator.printInfo();
        return validator;
    }

    public static void reset() {
        INSTANCE.cache.invalidateAll();
    }

    @Nonnull
    private ConditionalInfoForSession createInstance(final ConditionalCacheKey key) {
        if (key == null) {
            throw new IllegalArgumentException("Conditional cache key cannot be null");
        }
        return new ConditionalInfoForSession(key);
    }

    @Nonnull
    private ConditionalCacheKey buildNewKey(@Nonnull final Long outstandingSurveyId, @Nonnull final SurveyRequestMode mode, @Nonnull Module module) {
        final String currentId = UUID.randomUUID().toString();
        return new ConditionalCacheKey(outstandingSurveyId, currentId, mode, module);
    }

    @Nullable
    public String add(@Nonnull final Long outstandingSurveyId, @Nullable SurveyRequestMode mode, @Nullable Module module) throws ExecutionException {
        if (outstandingSurveyId <= 0) {
            LOGGER.warn("Missing outstandingSurveyId for conditional Validator Cache, add support for surveyId, masterId to load survey data");
        }
        if (mode == null) {
            LOGGER.warn("Missing mode for conditional Validator Cache, defaulting to PREVIEW");
            mode = SurveyRequestMode.PREVIEW;
        }
        final ConditionalCacheKey key = buildNewKey(outstandingSurveyId, mode, module);
        try {
            final ConditionalInfoForSession conditionalSession = this.cache.get(key);
            if (LOGGER.isTraceEnabled()) {
                LOGGER.trace(
                        "Adding conditional validator to cache with id: {} and details {}",
                        key.getSessionId(),
                        conditionalSession
                );
            }
            return key.getSessionId();
        } catch (Exception e) {
            LOGGER.error("Error adding conditional validator to cache", e);
            return key.getSessionId();
        }
    }

    @Nonnull
    public ConditionalValidator get(
            @Nonnull final Long outstandingSurveyId,
            @Nullable SurveyRequestMode mode,
            @Nonnull final String sessionId,
            @Nonnull final Module module
    ) {
        if (mode == null) {
            LOGGER.warn("Missing mode for conditional Validator Cache, defaulting to PREVIEW");
            mode = SurveyRequestMode.PREVIEW;
        }
        final ConditionalCacheKey key = new ConditionalCacheKey(outstandingSurveyId, sessionId, mode, module);
        try {
            return cache.get(key).getConditionalValidator();
        } catch (Exception e) {
            LOGGER.trace("Conditional validator not found for session id: {}", sessionId, e);
            return createInstance(key).getConditionalValidator();
        }
    }


}
