package qms.survey.logic;

import DPMS.DAOInterface.ISurveysDAO;
import DPMS.Mapping.Request;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyField;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import mx.bnext.access.Module;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.form.core.ISurveyToHtml;
import qms.form.dto.OutstandingSurveyDTO;
import qms.form.dto.SurveyParseDTO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.util.CacheRegion;
import qms.framework.util.MeasureTime;
import qms.survey.dto.SurveyParserData;
import qms.workflow.util.WorkflowAuthRole;

public class SurveytoHtmlRenderer implements ISurveyToHtml {
    
    private static final Logger LOGGER = Loggable.getLogger(SurveytoHtmlRenderer.class);
    
    private final ISurveysDAO dao;

    public SurveytoHtmlRenderer(ISurveysDAO dao) {
        this.dao = dao;
    }
        
    public static Survey getSurveyToHtml(@Nonnull Long surveyId, @Nonnull ISurveysDAO dao) {
        LOGGER.debug("[Loading survey metadata] for {}", surveyId);
        final ElapsedDataDTO tStart = MeasureTime.start(SurveytoHtmlRenderer.class);
        final IUntypedDAO untypedDAO = dao.getUntypedDAO();
        final Survey survey = untypedDAO.HQLT_findById(
                Survey.class,
                surveyId,
                true,
                CacheRegion.SURVEY,
                Utilities.getSettings().getConnQueryTimeout()
        );
        if (survey == null) {
            LOGGER.error("Could not get HTML from surveyId: {}, survey is null ", surveyId, new RuntimeException());
            return null;
        }
        remapRenderSurvey(survey);
        MeasureTime.stop(tStart, "[Elapsed time loading survey metadata]");
        return survey;
    }
    
    @Nonnull
    @Override
    public SurveyParseDTO toHtml(
            @Nonnull final WorkflowAuthRole authRole,
            @Nonnull final OutstandingSurveyDTO outstandingSurveys,
            @Nonnull final Boolean scoreEnabled,
            @Nonnull final Boolean findingCapable,
            @Nonnull final Boolean isAudit,
            @Nonnull final Boolean isActivityEnabled,
            @Nonnull final Module module,
            @Nullable final String task,
            @Nullable final Long currentAutorizationPoolIndex,
            @Nonnull final ILoggedUser loggedUser,
            @Nullable final HttpServletRequest request,
            @Nullable final HttpServletResponse response
    ) {
        final Survey survey = getSurveyToHtml(outstandingSurveys.getSurveyId(), dao);
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        final SurveyParserData data = new SurveyParserData(
                survey,
                authRole,
                outstandingSurveys,
                false,
                isAudit,
                scoreEnabled,
                findingCapable,
                isActivityEnabled,
                null,
                module,
                task,
                currentAutorizationPoolIndex
        );
        final SurveyParser parser = new SurveyParser(
                data,
                request,
                response,
                loggedUser
        );
        MeasureTime.stop(tStart, "Elapsed time initializing survey parser preview");
        final String html = parser.toHtml();
        MeasureTime.stop(tStart, "Elapsed time GENERATING survey HTML preview");
        return new SurveyParseDTO(html, parser);
    }

    @Nonnull
    @Override
    public SurveyParseDTO toHtml(
            @Nonnull final Long surveyId,
            @Nonnull final WorkflowAuthRole authRole,
            @Nonnull final Long requestorId,
            @Nonnull final Boolean disabled,
            @Nonnull final Module module,
            @Nullable final String task,
            @Nonnull final ILoggedUser loggedUser,
            @Nullable final HttpServletRequest request,
            @Nullable final HttpServletResponse response
    ) {
        final Survey survey = getSurveyToHtml(surveyId, dao);
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        final SurveyParserData data = new SurveyParserData(
                survey,
                authRole,
                new OutstandingSurveyDTO(), // <-- Valor DUMMY, se requiere por que el metodo `toHtml`
                disabled,
                Module.AUDIT.equals(module),
                false,
                false,
                false,
                null,
                module,
                task,
                null
        );
        final SurveyParser parser = new SurveyParser(
                data,
                request,
                response,
                loggedUser
        );
        MeasureTime.stop(tStart, "Elapsed time initializing survey parser.");
        final String html = parser.toHtml();
        MeasureTime.stop(tStart,"Elapsed time GENERATING survey HTML.");
        return new SurveyParseDTO(html, parser);
    }   
    
    public static Integer requestStatus(Long requestId, IUntypedDAO dao) {
        if (requestId == null) {
            return null;
        }
        return dao.HQL_findSimpleInteger(" "
            + " SELECT "
                + " r.status "
            + " FROM " + Request.class.getCanonicalName() + " r "
            + " WHERE r.id = :requestId ",
            ImmutableMap.of("requestId", requestId)
        );
    }

    /**
     * Quita las preguntas que pertenecen a un CROSSTAB del arreglo completo Agrega a CROSSTAB's las preguntas que le
     * pertenecen al mismo
     *
     * @param survey
     */
    public static void remapRenderSurvey(@Nonnull final Survey survey) {
        final List<SurveyField> fields = survey.getFields();
        final List<SurveyField> surveyFieldsList = new ArrayList<>(fields.size());
        //Se buscan todas las preguntas que pertenezcan a un crosstab
        for (final SurveyField field : fields) {
            final String location = field.getObj().getLocation();
            if (location != null && !location.isEmpty()) {
                surveyFieldsList.add(field);
            }
        }
        //Se remueven dichas preguntas del arreglo
        if (surveyFieldsList.isEmpty()) {
            return;
        }
        fields.removeAll(surveyFieldsList);
        final Map<Long, SurveyField> fieldsMap = getSurveyRenderMap(fields);
        
        for (final SurveyField field : surveyFieldsList) {
            final Long parentFieldId = field.getObj().getParentFieldId();
            try {
                if (parentFieldId == null) {
                    if (LOGGER.isWarnEnabled()) {
                        LOGGER.warn(
                                "El field '{}', '{}' no tiene parentFieldId",
                                field.getType(),
                                field
                        );
                    }
                } else {
                    final SurveyField fieldMap = fieldsMap.get(parentFieldId);
                    fieldMap.getObj().getItemStash().add(field);
                }
            } catch (final Exception e) {
                LOGGER.error(
                        "Falla en field {} - {} - {}",
                        field.getType(),
                        field,
                        parentFieldId, e);
            }
        }
    }
    private static Map<Long, SurveyField> getSurveyRenderMap(final List<SurveyField> list) {
        final Map<Long, SurveyField> result = new HashMap<>(list.size());
        for (final SurveyField field : list) {
            result.put(field.getObj().getId(), field);
        }
        return result;
    }

}
