package qms.survey.util;

public interface ICurrencyConvertable extends Cloneable {

    Boolean getTypeNumber();

    void setTypeNumber(Boolean typeNumber);

    Boolean getTypeCurrency();

    void setTypeCurrency(Boolean typeCurrency);

    Boolean getTypeCurrencyConversion();

    void setTypeCurrencyConversion(Boolean typeCurrencyConversion);

    Long getExchangeRateId();

    void setExchangeRateId(Long exchangeRateId);

    String getExchangeRateValue();

    void setExchangeRateValue(String exchangeRateValue);

    String getExchangeRateDate();

    void setExchangeRateDate(String exchangeRateDate);

    ICurrencyConvertable getCurrencyConversion();

    void setCurrencyConversion(ICurrencyConvertable currencyConversion);

    String getInputType();

    void setInputType(String inputType);

}
