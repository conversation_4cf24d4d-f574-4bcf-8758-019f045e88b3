package qms.survey.dto;

import java.io.Serializable;

public class SurveyConditionalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer conditionalType;
    private Long fieldObjectId;
    private String conditionalAnswerSelector;
    private Long conditionalQuestionId;
    private Long conditionalAnswerId;
    private Integer hierarchyRow;
    private String hierarchyColumn;
    private String hierarchyValue;
    private Integer daysToExpire;
    private Integer daysToNotifyBeforeExpiration;
    private Double maxLim;
    private Double minLim;
    private Double eqLim;
    
    public SurveyConditionalDTO() {
    }

    public SurveyConditionalDTO(
            Integer conditionalType,
            Long fieldObjectId,
            String conditionalAnswerSelector,
            Long conditionalQuestionId,
            Long conditionalAnswerId,
            Integer hierarchyRow,
            String hierarchyColumn,
            String hierarchyValue,
            Integer daysToExpire,
            Integer daysToNotifyBeforeExpiration,
            Double maxLim,
            Double minLim,
            Double eqLim
    ) {
        this.conditionalType = conditionalType;
        this.fieldObjectId = fieldObjectId;
        this.conditionalAnswerSelector = conditionalAnswerSelector;
        this.conditionalQuestionId = conditionalQuestionId;
        this.conditionalAnswerId = conditionalAnswerId;
        this.hierarchyRow = hierarchyRow;
        this.hierarchyColumn = hierarchyColumn;
        this.hierarchyValue = hierarchyValue;
        this.daysToExpire = daysToExpire;
        this.daysToNotifyBeforeExpiration = daysToNotifyBeforeExpiration;
        this.maxLim = maxLim;
        this.minLim = minLim;
        this.eqLim = eqLim;
    }

    public Long getFieldObjectId() {
        return fieldObjectId;
    }

    public void setFieldObjectId(Long fieldObjectId) {
        this.fieldObjectId = fieldObjectId;
    }

    public Integer getConditionalType() {
        return conditionalType;
    }

    public void setConditionalType(Integer conditionalType) {
        this.conditionalType = conditionalType;
    }

    public String getConditionalAnswerSelector() {
        return conditionalAnswerSelector;
    }

    public void setConditionalAnswerSelector(String conditionalAnswerSelector) {
        this.conditionalAnswerSelector = conditionalAnswerSelector;
    }

    public Long getConditionalQuestionId() {
        return conditionalQuestionId;
    }

    public void setConditionalQuestionId(Long conditionalQuestionId) {
        this.conditionalQuestionId = conditionalQuestionId;
    }

    public Long getConditionalAnswerId() {
        return conditionalAnswerId;
    }

    public void setConditionalAnswerId(Long conditionalAnswerId) {
        this.conditionalAnswerId = conditionalAnswerId;
    }

    public Integer getHierarchyRow() {
        return hierarchyRow;
    }

    public void setHierarchyRow(Integer hierarchyRow) {
        this.hierarchyRow = hierarchyRow;
    }

    public String getHierarchyColumn() {
        return hierarchyColumn;
    }

    public void setHierarchyColumn(String hierarchyColumn) {
        this.hierarchyColumn = hierarchyColumn;
    }

    public String getHierarchyValue() {
        return hierarchyValue;
    }

    public void setHierarchyValue(String hierarchyValue) {
        this.hierarchyValue = hierarchyValue;
    }

    public Integer getDaysToExpire() {
        return daysToExpire;
    }

    public void setDaysToExpire(Integer daysToExpire) {
        this.daysToExpire = daysToExpire;
    }

    public Integer getDaysToNotifyBeforeExpiration() {
        return daysToNotifyBeforeExpiration;
    }

    public void setDaysToNotifyBeforeExpiration(Integer daysToNotifyBeforeExpiration) {
        this.daysToNotifyBeforeExpiration = daysToNotifyBeforeExpiration;
    }

    public Double getMaxLim() {
        return maxLim;
    }

    public void setMaxLim(Double maxLim) {
        this.maxLim = maxLim;
    }

    public Double getMinLim() {
        return minLim;
    }

    public void setMinLim(Double minLim) {
        this.minLim = minLim;
    }

    public Double getEqLim() {
        return eqLim;
    }

    public void setEqLim(Double eqLim) {
        this.eqLim = eqLim;
    }

    @Override
    public String toString() {
        return "SurveyConditionalDTO{"
                + "conditionalType=" + conditionalType
                + ", conditionalQuestionId=" + conditionalQuestionId 
                + ", conditionalAnswerId=" + conditionalAnswerId + '}';
    }
    
    
}
