
package qms.tress.entity;

import Framework.Config.DomainObject;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@Table(name = "tress_user_info_view")
@Immutable
public class TRESSUserInfoView extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Integer status;
    private Integer positionsCount;
    
    private String account;
    private String mail;
    private String fullUserDescription;
    private String positionDescription;
    private String businessUnitDescription;
    private String physicalLocation;
    private String businessUnitId;

    @Id
    @Basic(optional = false)
    @Column(name = "user_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "positions_count")
    public Integer getPositionsCount() {
        return positionsCount;
    }

    public void setPositionsCount(Integer positionsCount) {
        this.positionsCount = positionsCount;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    @Column(name = "full_user_description")
    public String getFullUserDescription() {
        return fullUserDescription;
    }

    public void setFullUserDescription(String fullUserDescription) {
        this.fullUserDescription = fullUserDescription;
    }

    @Column(name = "position_description")
    public String getPositionDescription() {
        return positionDescription;
    }

    public void setPositionDescription(String positionDescription) {
        this.positionDescription = positionDescription;
    }

    @Column(name = "business_unit_description")
    public String getBusinessUnitDescription() {
        return businessUnitDescription;
    }

    public void setBusinessUnitDescription(String businessUnitDescription) {
        this.businessUnitDescription = businessUnitDescription;
    }

    @Column(name = "physical_location")
    public String getPhysicalLocation() {
        return physicalLocation;
    }

    public void setPhysicalLocation(String physicalLocation) {
        this.physicalLocation = physicalLocation;
    }

    @Column(name = "business_unit_id")
    public String getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(String businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 31 * hash + Objects.hashCode(this.account);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TRESSUserInfoView other = (TRESSUserInfoView) obj;
        if (!Objects.equals(this.account, other.account)) {
            return false;
        }
        return true;
    }

    
}
