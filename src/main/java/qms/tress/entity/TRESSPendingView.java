
package qms.tress.entity;

import Framework.Config.DomainObject;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.util.interfaces.IPersistableCode;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@Table(name = "tress_pending_view")
@Immutable
public class TRESSPendingView extends DomainObject implements Serializable, IPersistableCode {

    private static final long serialVersionUID = 1L;

    
    private String processId;
    private String code;            //<-- cuenta de usuario
    private Integer status;    
    
    private String statusTress;         
    private Integer statusBnext;         
    
    private String oldBusinessUnitDescription;
    private String oldMail;
    private String oldFullUserDescription;
    private String oldPositionDescription;
    private String oldPhysicalLocation;
    
    private String newBusinessUnitDescription;
    private String newMail;
    private String newFullUserDescription;
    private String newPositionDescription;
    private String newPhysicalLocation;
    
    private String employeeType;
    
    private Long newBusinessUnitId;
    private Long oldPositionsCount;

    @Id
    @Basic(optional = false)
    @Column(name = "id")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof TRESSPendingView)) {
            return false;
        }
        TRESSPendingView other = (TRESSPendingView) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.TRESSPendingView[ id=" + id + " ]";
    }

    @Column(name = "old_business_unit_description")
    public String getOldBusinessUnitDescription() {
        return oldBusinessUnitDescription;
    }

    public void setOldBusinessUnitDescription(String oldBusinessUnitDescription) {
        this.oldBusinessUnitDescription = oldBusinessUnitDescription;
    }

    @Column(name = "old_mail")
    public String getOldMail() {
        return oldMail;
    }

    public void setOldMail(String oldMail) {
        this.oldMail = oldMail;
    }

    @Column(name = "old_full_user_description")
    public String getOldFullUserDescription() {
        return oldFullUserDescription;
    }

    public void setOldFullUserDescription(String oldFullUserDescription) {
        this.oldFullUserDescription = oldFullUserDescription;
    }

    @Column(name = "old_position_description")
    public String getOldPositionDescription() {
        return oldPositionDescription;
    }

    public void setOldPositionDescription(String oldPositionDescription) {
        this.oldPositionDescription = oldPositionDescription;
    }

    @Column(name = "new_business_unit_description")
    public String getNewBusinessUnitDescription() {
        return newBusinessUnitDescription;
    }

    public void setNewBusinessUnitDescription(String newBusinessUnitDescription) {
        this.newBusinessUnitDescription = newBusinessUnitDescription;
    }

    @Column(name = "new_mail")
    public String getNewMail() {
        return newMail;
    }

    public void setNewMail(String newMail) {
        this.newMail = newMail;
    }

    @Column(name = "new_full_user_description")
    public String getNewFullUserDescription() {
        return newFullUserDescription;
    }

    public void setNewFullUserDescription(String newFullUserDescription) {
        this.newFullUserDescription = newFullUserDescription;
    }

    @Column(name = "new_position_description")
    public String getNewPositionDescription() {
        return newPositionDescription;
    }

    public void setNewPositionDescription(String newPositionDescription) {
        this.newPositionDescription = newPositionDescription;
    }

    @Column(name = "new_business_unit_id")
    public Long getNewBusinessUnitId() {
        return newBusinessUnitId;
    }

    public void setNewBusinessUnitId(Long newBusinessUnitId) {
        this.newBusinessUnitId = newBusinessUnitId;
    }

    @Column(name = "old_positions_count")
    public Long getOldPositionsCount() {
        return oldPositionsCount;
    }

    public void setOldPositionsCount(Long oldPositionsCount) {
        this.oldPositionsCount = oldPositionsCount;
    }

    @Column(name = "new_physical_location")
    public String getNewPhysicalLocation() {
        return newPhysicalLocation;
    }

    public void setNewPhysicalLocation(String newPhysicalLocation) {
        this.newPhysicalLocation = newPhysicalLocation;
    }

    @Column(name = "old_physical_location")
    public String getOldPhysicalLocation() {
        return oldPhysicalLocation;
    }

    public void setOldPhysicalLocation(String oldPhysicalLocation) {
        this.oldPhysicalLocation = oldPhysicalLocation;
    }

    @Column(name = "process_id")
    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    @Column(name = "status_tress")
    public String getStatusTress() {
        return statusTress;
    }

    public void setStatusTress(String statusTress) {
        this.statusTress = statusTress;
    }

    @Column(name = "status_bnext")
    public Integer getStatusBnext() {
        return statusBnext;
    }

    public void setStatusBnext(Integer statusBnext) {
        this.statusBnext = statusBnext;
    }

    @Column(name = "employee_type")
    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }
    
    
    
}
