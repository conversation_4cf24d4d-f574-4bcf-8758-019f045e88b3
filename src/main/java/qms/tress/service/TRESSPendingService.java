
package qms.tress.service;

import DPMS.DAOInterface.IUserDAO;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import mx.bnext.core.util.GridInfo;
import java.util.List;
import java.util.Map;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.tress.DAOInterface.ITRESSPendingDAO;
import qms.tress.entity.TRESSPending;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class TRESSPendingService extends CRUD_Generic<TRESSPending> {
    
    @SMDMethod 
    public GridInfo getCurrentPendings(SortedPagedFilter filter) {
        ITRESSPendingDAO dao = getBean(ITRESSPendingDAO.class);
        filter.getCriteria().put("deleted", "0");
        return dao.HQL_getRowsByQuery(""
            + " SELECT"
                + " new map("
                    + " c.id AS id"
                    + " ,c.code AS code"
                    + " ,c.status AS status"
                    + " ,c.createdDate AS createdDate"
                    + " ,c.lastModifiedDate AS lastModifiedDate"
                    + " ,c.newBusinessUnitDescription AS newBusinessUnitDescription"
                    + " ,c.newMail AS newMail"
                    + " ,c.newFullUserDescription AS newFullUserDescription"
                    + " ,c.newPositionDescription AS newPositionDescription"
                    + " ,c.newPhysicalLocation AS newPhysicalLocation"
                    + " ,c.employeeType AS employeeType"
                    + " ,d.fullUserDescription AS oldFullUserDescription"
                    + " ,d.mail AS oldMail"
                    + " ,d.businessUnitDescription AS oldBusinessUnitDescription"
                    + " ,d.positionDescription AS oldPositionDescription"
                    + " ,d.physicalLocation AS oldPhysicalLocation"
                    + " ,d.positionsCount AS oldPositionsCount"
                + " ) "
            + " FROM " + TRESSPending.class.getCanonicalName() + " c "
            + " LEFT JOIN c.userInfo d ", filter)
        ;
    }
    @SMDMethod
    public GenericSaveHandle confirm(Long[] tressPendingIds) {
        ITRESSPendingDAO dao = getBean(ITRESSPendingDAO.class);
        List<TRESSPending> results = dao.HQLT_findByQueryFilter(""
            + Utilities.arrayToSelectInFullCondition(tressPendingIds, "c.id")
        );
        Map<String, Long> 
            toAdd = dao.HQL_findSimpleMap(""
                + " SELECT new map("
                    + " c.status AS status,"
                    + " count(*) AS count"
                + " )"
                + " FROM " + TRESSPending.class.getCanonicalName() + " c "
                + " WHERE " 
                    + Utilities.arrayToSelectInFullCondition(tressPendingIds, "c.id")
                    + " AND c.status = " + TRESSPending.STATUS.CREATE_USER.getValue()
                + " GROUP BY c.status"
            ), 
            toDrop = dao.HQL_findSimpleMap(""
                + " SELECT new map("
                    + " c.status AS status,"
                    + " count(*) AS count"
                + " )"
                + " FROM " + TRESSPending.class.getCanonicalName() + " c "
                + " WHERE " 
                    + Utilities.arrayToSelectInFullCondition(tressPendingIds, "c.id")
                    + " AND c.status = " + TRESSPending.STATUS.DEACTIVATE_USER.getValue()
                + " GROUP BY c.status"
            )
        ;
        IUserDAO daoUser = Utilities.getBean(IUserDAO.class);
        Integer availableCount = Utilities.getLicenses(), currentActiveUsersCount = daoUser.getActiveUsers();
        long usersToAddCount = Utilities.asPrimitive(toAdd.get("count")), usersToDropCount = Utilities.asPrimitive(toDrop.get("count"));
        
        Long requiredLicences = currentActiveUsersCount + usersToAddCount - usersToDropCount;
        GenericSaveHandle callback;
        if(requiredLicences > availableCount) {
            callback = new GenericSaveHandle();
            callback.setOperationEstatus(0);
            callback.getJsonEntityData().put("noLicences", true);
            callback.getJsonEntityData().put("count", requiredLicences - availableCount);
        } else {
            callback = dao.processTRESSPendings(results);
        }
        return callback;
    }
}
