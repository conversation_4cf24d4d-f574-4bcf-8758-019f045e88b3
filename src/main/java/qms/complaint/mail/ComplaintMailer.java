package qms.complaint.mail;

import DPMS.Mapping.Complaint;
import DPMS.Mapping.PositionSave;
import DPMS.Mapping.Profile;
import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import javax.mail.Message;
import mx.bnext.access.Module;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.complaint.pending.imp.ToAssign;
import qms.complaint.pending.imp.ToEvaluate;
import qms.complaint.pending.imp.ToRespond;
import qms.complaint.pending.imp.ToVerify;
import qms.document.mail.IRecipientId;
import qms.framework.core.PendingMailer;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR>
 */
public class ComplaintMailer extends PendingMailer implements IComplaintDaemonMailer, IComplaintRuntimeMailer {

    private final ComplaintMailHelper helper;
    private final ResourceBundle _tags;
    private final IUntypedDAO dao;
    private String templateNotApply = null;
    private String templateReported = null;
    private String templateUnsatisfactory = null;
    private String templateAttended = null;
    private String templateAssigned = null;
    private String templateSatisfacted = null;
    private String templateVerified = null;
    private String templateComplaintRow = null;

    public ComplaintMailer(IUntypedDAO dao) {
        super(dao);
        this.dao = dao;
        helper = new ComplaintMailHelper(dao);
        super.loadTags(this.getClass().getCanonicalName());
        _tags = getTags();
        super.setHeader(super.getTag("header"));
        super.setSubject(super.getTag("subject"));
        super.setGenericPath(ComplaintMailer.class.getCanonicalName());
    }

    private String getTemplateNotApply(Complaint complaint) {
        if (templateNotApply == null) {
            this.templateNotApply = getTemplate(ComplaintDTO.class.getCanonicalName() + "_notapply");
        }
        return mapEntity(helper.loadDto(complaint, _tags, dao), templateNotApply, _tags);
    }

    private String getTemplateReported(Complaint complaint) {
        if (templateReported == null) {
            this.templateReported = getTemplate(ComplaintDTO.class.getCanonicalName() + "_reported");
        }
        return mapEntity(helper.loadDto(complaint, _tags, dao), templateReported, _tags);
    }

    private String getTemplateUnsatisfactory(Complaint complaint) {
        if (templateUnsatisfactory == null) {
            this.templateUnsatisfactory = getTemplate(ComplaintDTO.class.getCanonicalName() + "_unsatisfactory");
        }
        return mapEntity(helper.loadDto(complaint, _tags, dao), templateUnsatisfactory, _tags);
    }

    private String getTemplateAttended(Complaint complaint) {
        if (templateAttended == null) {
            this.templateAttended = getTemplate(ComplaintDTO.class.getCanonicalName() + "_attended");
        }
        return mapEntity(helper.loadDto(complaint, _tags, dao), templateAttended, _tags);
    }

    private String getTemplateAssigned(Complaint complaint) {
        if (templateAssigned == null) {
            this.templateAssigned = getTemplate(ComplaintDTO.class.getCanonicalName() + "_assigned");
        }
        return mapEntity(helper.loadDto(complaint, _tags, dao), templateAssigned, _tags);
    }

    private String getTemplateSatisfacted(Complaint complaint) {
        if (templateSatisfacted == null) {
            this.templateSatisfacted = getTemplate(ComplaintDTO.class.getCanonicalName() + "_satisfacted");
        }
        return mapEntity(helper.loadDto(complaint, _tags, dao), templateSatisfacted, _tags);
    }

    private String getTemplateVerified(Complaint complaint) {
        if (templateVerified == null) {
            this.templateVerified = getTemplate(ComplaintDTO.class.getCanonicalName() + "_verified");
        }
        return mapEntity(helper.loadDto(complaint, _tags, dao), templateVerified, _tags);
    }

    private String getComplaintRow(Set<? extends IRecipientId> reqs) {
        if (templateComplaintRow == null) {
            templateComplaintRow = getTemplate(ComplaintRowDTO.class.getCanonicalName());
        }
        return mapEntity(reqs, templateComplaintRow, _tags);
    }
    
    
    private void sendEvaluatedUnsatisfactoryAuthor(Complaint complaint) {
        setTitle(getTag("title.evaluatedUnsatisfactoryAuthor"));
        Mail recipient = helper.getAuthor(complaint);
        String data = getTemplateUnsatisfactory(complaint);
        send(recipient, data, Module.COMPLAINT.getKey(), TYPE.INFO);
    }

    private void sendEvaluatedUnsatisfactoryCopy(Complaint complaint) {
        if (!complaint.getMailTo().isEmpty()) {
            setTitle(getTag("title.evaluatedUnsatisfactoryCopy"));
            Mail copyMail = helper.toMail(complaint.getMailTo(), Message.RecipientType.TO);
            String data = getTemplateUnsatisfactory(complaint);
            send(copyMail, data, Module.COMPLAINT.getKey(), TYPE.INFO);
        }
    }

    private void sendEvaluatedUnsatisfactoryManager(Complaint complaint) {
        setTitle(getTag("title.evaluatedUnsatisfactoryManager"));
        Set<Mail> recipients = helper.getToAssignResponsible(complaint);
        String data = getTemplateUnsatisfactory(complaint);
        for (Mail recipient : recipients) {
            send(recipient, data, Module.COMPLAINT.getKey(), TYPE.ACTION);
        }
    }

    private void sendEvaluatedUnsatisfactoryDepartment(Complaint complaint) {
        setTitle(getTag("title.evaluatedUnsatisfactoryDepartment"));
        Set<Mail> recipients = helper.getToAssignResponsible(complaint);
        String data = getTemplateUnsatisfactory(complaint);
        for (Mail recipient : recipients) {
            send(recipient, data, Module.COMPLAINT.getKey(), TYPE.ACTION);
        }
    }

    private void sendReportedManagerAction(Complaint complaint) {
        setTitle(getTag("title.reportedManagerAction"));
        Set<Mail> managers = helper.getManagers(complaint);
        String data = getTemplateReported(complaint);
        for (Mail manager : managers) {
            send(manager, data, Module.COMPLAINT.getKey(), TYPE.ACTION);
        }
    }

    private void sendReportedManagerNotify(Complaint complaint) {
        setTitle(getTag("title.reportedManagerNotify"));
        Set<Mail> managers = helper.getManagers(complaint);
        String data = getTemplateReported(complaint);
        for (Mail manager : managers) {
            send(manager, data, Module.COMPLAINT.getKey(), TYPE.INFO);
        }
    }

    private void sendReportedDepartmentAction(Complaint complaint) {
        setTitle(getTag("title.reportedDepartmentAction"));
        Set<Mail> recipients = helper.getToAssignResponsible(complaint);
        String data = getTemplateReported(complaint);
        String redirectUrl = "v.complaint.view?intQuejaId=" + complaint.getId() + "&vchState=edit";
        for (Mail recipient : recipients) {
            send(recipient, data, Module.COMPLAINT, TYPE.ACTION, redirectUrl);
        }
    }

    private void sendReportedAuthor(Complaint complaint) {
        setTitle(getTag("title.reportedAuthor"));
        Mail author = helper.getAuthor(complaint);
        String data = getTemplateReported(complaint);
        send(author, data, Module.COMPLAINT.getKey(), TYPE.INFO);
    }

    private void sendReportedCopy(Complaint complaint) {
        if (!complaint.getMailTo().isEmpty()) {
            setTitle(getTag("title.reportedCopy"));
            Mail copyMail = helper.toMail(complaint.getMailTo(), Message.RecipientType.TO);
            String data = getTemplateReported(complaint);
            send(copyMail, data, Module.COMPLAINT.getKey(), TYPE.INFO);
        }
    }

    private void sendAssignedResponsible(Complaint complaint, String title) {
        setTitle(title);
        Mail responsible = helper.getResponsible(complaint);
        String data = getTemplateAssigned(complaint);
        String redirectUrl = "v.complaint.view?intQuejaId=" + complaint.getId() + "&vchState=edit";
        send(responsible, data, Module.COMPLAINT, TYPE.ACTION, redirectUrl);
    }


    private void sendVerifiedAuthor(Complaint complaint) {
        setTitle(getTag("title.verifiedAuthor"));
        Mail author = helper.getAuthor(complaint);
        String data = getTemplateVerified(complaint);
        send(author, data, Module.COMPLAINT.getKey(), TYPE.ACTION);
    }

    private void sendVerifiedCopy(Complaint complaint) {
        if (!complaint.getMailTo().isEmpty()) {
            setTitle(getTag("title.verifiedCopy"));
            Mail copyMail = helper.toMail(complaint.getMailTo(), Message.RecipientType.TO);
            String data = getTemplateVerified(complaint);
            send(copyMail, data, Module.COMPLAINT.getKey(), TYPE.INFO);
        }
    }

    private void evaluatedSatisfactedDepartment(Complaint complaint) {
        setTitle(getTag("title.evaluatedSatisfactedDepartment"));
        Set<Mail> recipients = helper.getToAssignResponsible(complaint);
        String data = getTemplateSatisfacted(complaint);
        TYPE type = complaint.getStatus().equals(Complaint.STATUS.EVALUATED.getValue()) ? TYPE.INFO : TYPE.ACTION;
        for (Mail recipient : recipients) {
            send(recipient, data, Module.COMPLAINT.getKey(), type);
        }
    }

    private void evaluatedSatisfactedManager(Complaint complaint) {
        setTitle(getTag("title.evaluatedSatisfactedManager"));
        Set<Mail> managers = helper.getManagers(complaint);
        String data = getTemplateSatisfacted(complaint);
        TYPE type = complaint.getStatus().equals(Complaint.STATUS.EVALUATED.getValue()) ? TYPE.INFO : TYPE.ACTION;
        for (Mail manager : managers) {
            send(manager, data, Module.COMPLAINT.getKey(), type);
        }
    }

    private void sendNotApplyCopy(Complaint complaint) {
        if (complaint.getMailTo().isEmpty()) {
            return;
        }
        setTitle(getTag("title.notApplyCopy").replace("{user}", SecurityUtils.getLoggedUser().getDescription()));
        Mail copyMail = helper.toMail(complaint.getMailTo(), Message.RecipientType.TO);
        String data = getTemplateNotApply(complaint);
        send(copyMail, data, Module.COMPLAINT.getKey(), TYPE.ACTION);
    }

    private void sendNotApplyAuthor(Complaint complaint) {
        setTitle(getTag("title.notApplyAuthor").replace("{user}", SecurityUtils.getLoggedUser().getDescription()));
        Mail author = helper.getAuthor(complaint);
        String data = getTemplateNotApply(complaint);
        send(author, data, Module.COMPLAINT.getKey(), TYPE.INFO);
    }
       
    private void sendToAssignByDepartmentAttendant(Long newAttendantId, LoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toAssignByDepartmentAttendant(newAttendantId, _tags);
        if (complaints.isEmpty()) {
            return;
        }  
        setTitle(getTag("title.sendToAssignByDepartmentAttendant"));
        Mail attendant = helper.getUser(newAttendantId);
        Mail remittent = helper.toMail(loggedUser.getUser());
        send(remittent, attendant, getComplaintRow(complaints), Module.COMPLAINT.getKey(), TYPE.ACTION);
    }

    private void sendToVerifyByDepartmentAttendant(Long newAttendantId, LoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toVerifyByDepartmentAttendant(newAttendantId, _tags);
        if (complaints.isEmpty()) {
            return;
        }  
        setTitle(getTag("title.sendToVerifyByDepartmentAttendant"));
        Mail attendant = helper.getUser(newAttendantId);
        Mail remittent = helper.toMail(loggedUser.getUser());
        send(remittent, attendant, getComplaintRow(complaints), Module.COMPLAINT.getKey(), TYPE.ACTION);
    }

    
    private void sendToAssignByManager(Long userId, List<Long> jobs, ILoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toAssignResponsibleByJobs(userId, jobs, _tags);
        Mail recipient = helper.getUser(userId);
        if (recipient == null || complaints.isEmpty()) {
            return;
        }
        setTitle(getTag("title.sendToAssignByManager"));
        Mail remittent = helper.toMail(loggedUser);
        String template = getComplaintRow(complaints);
        send(remittent, recipient, template, "", Module.COMPLAINT.getKey(), TYPE.ACTION);
    }

    private void sendToVerifyByManager(Long userId, List<Long> jobs, ILoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toVerifyByJobs(userId, jobs, _tags);
        Mail recipient = helper.getUser(userId);
        if (recipient == null || complaints.isEmpty()) {
            return;
        }
        setTitle(getTag("title.sendToVerifyByManager"));
        Mail remittent = helper.toMail(loggedUser);
        String template = getComplaintRow(complaints);
        send(remittent, recipient, template, "", Module.COMPLAINT.getKey(), TYPE.ACTION);
    }
    
    private void sendToAssignByManager(PositionSave ent, Long businessUnitId, Long profileId, LoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toAssignResponsibleByPosition(ent, businessUnitId, profileId, _tags);
        if (complaints.isEmpty()) {
            return;
        }
        setTitle(getTag("title.sendToAssignByManager"));
        sendComplaintsToAssign(complaints, loggedUser);
    }

    private void sendToVerifyByManager(PositionSave ent, Long businessUnitId, Long profileId,  LoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toVerifyByPosition(ent, businessUnitId, profileId,  _tags);
        if (complaints.isEmpty()) {
            return;
        }
        setTitle(getTag("title.sendToVerifyByManager"));
        sendComplaintsToVerify(complaints, loggedUser);
    }
    
    private void sendToAssignByManager(Profile ent, LoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toAssignResponsibleByProfile(ent, _tags);
        if (complaints.isEmpty()) {
            return;
        }
        setTitle(getTag("title.sendToAssignByManager"));
        sendComplaintsToAssign(complaints, loggedUser);
    }

    private void sendToVerifyByManager(Profile ent, LoggedUser loggedUser) {
        Set<ComplaintRowDTO> complaints = helper.toVerifyByProfile(ent, _tags);
        if (complaints.isEmpty()) {
            return;
        }
        setTitle(getTag("title.sendToVerifyByManager"));
        sendComplaintsToVerify(complaints, loggedUser);
    }
    
    private void sendComplaintsToAssign(Set<ComplaintRowDTO> complaints, LoggedUser loggedUser) {
        Mail remittent = helper.toMail(loggedUser.getUser());
        Map<Long, Set<IRecipientId>> complaintsByUser = groupByRecipientId(complaints);
        for (Map.Entry<Long, Set<IRecipientId>> entry : complaintsByUser.entrySet()) {
            Mail recipient = helper.getUser(entry.getKey());
            send(remittent, recipient, getComplaintRow(entry.getValue()), Module.COMPLAINT.getKey(), TYPE.ACTION);
        }
    }
    
    private void sendComplaintsToVerify(Set<ComplaintRowDTO> complaints, LoggedUser loggedUser) {
        Mail remittent = helper.toMail(loggedUser.getUser());
        Map<Long, Set<IRecipientId>> complaintsByUser = groupByRecipientId(complaints);
        for (Map.Entry<Long, Set<IRecipientId>> entry : complaintsByUser.entrySet()) {
            Mail recipient = helper.getUser(entry.getKey());
            send(remittent, recipient, getComplaintRow(entry.getValue()), Module.COMPLAINT.getKey(), TYPE.ACTION);
        }
    }
    
    @Override
    public void reportedComplaint(Complaint complaint) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(1)) {
            sendReportedManagerAction(complaint);
        } else {
            if (Utilities.getSettings().getComplaintManagerNotification().equals(1)) {
                sendReportedManagerNotify(complaint);
            }
            sendReportedDepartmentAction(complaint);
        }
        sendReportedAuthor(complaint);
        sendReportedCopy(complaint);
    }

    @Override
    public void notApply(Complaint complaint) {
        sendNotApplyCopy(complaint);
        sendNotApplyAuthor(complaint);
    }

    @Override
    public void evaluatedUnsatisfactoryComplaint(Complaint complaint) {
        sendEvaluatedUnsatisfactoryAuthor(complaint);
        sendEvaluatedUnsatisfactoryCopy(complaint);
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(1)) {
            sendEvaluatedUnsatisfactoryManager(complaint);
        } else {
            sendEvaluatedUnsatisfactoryDepartment(complaint);
        }
    }

    @Override
    public void assignedComplaint(Complaint complaint) {
        sendAssignedResponsible(complaint, getTag("title.asignedResponsible"));
    }

    @Override
    public void rejectedComplaintAnswer(Complaint complaint) {
        sendAssignedResponsible(complaint, getTag("title.rejectedComplaintAnswer"));
    }

    @Override
    public void attendedComplaint(Complaint complaint) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(1)) {
            setTitle(getTag("title.attendedManager"));
        } else {
            setTitle(getTag("title.attendedDepartment"));
        }
        Set<Mail> recipients = helper.getToAssignResponsible(complaint);
        String data = getTemplateAttended(complaint);
        for (Mail recipient : recipients) {
            send(recipient, data, Module.COMPLAINT, TYPE.ACTION);
    }
    }

    @Override
    public void verifiedComplaint(Complaint complaint) {
        sendVerifiedAuthor(complaint);
        sendVerifiedCopy(complaint);
    }

    @Override
    public void evaluatedComplaintSatisfacted(Complaint complaint) {
        if (Utilities.getSettings().getComplaintManagerNotification() == 1) {
            evaluatedSatisfactedManager(complaint);
        }
            evaluatedSatisfactedDepartment(complaint);
    }

    @Override
    public void changeDepartmentManager(Long newAttendantId, LoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(1)) {
            return;
        }
        sendToAssignByDepartmentAttendant(newAttendantId, loggedUser);
        sendToVerifyByDepartmentAttendant(newAttendantId, loggedUser);
    }

    @Override
    public void jobAdded(Long userId, List<Long>jobs, ILoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)) {
            return;
        }
        sendToAssignByManager(userId, jobs, loggedUser);
        sendToVerifyByManager(userId, jobs, loggedUser);
    }

    @Override
    public void changeManager(PositionSave ent, LoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)) {
            return;
        }
        sendToAssignByManager(ent, null, null, loggedUser);
        sendToVerifyByManager(ent, null, null, loggedUser);
    }

    @Override
    public void changeManager(Profile ent, LoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)) {
            return;
        }
        sendToAssignByManager(ent, loggedUser);
        sendToVerifyByManager(ent, loggedUser);
    }
    

    @Override
    public void editProfile(Profile ent, Profile previousEnt, LoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)
                || Objects.equals(ent.getIntBEncargadoQueja(), previousEnt.getIntBEncargadoQueja())) {
            return;
        }
        sendToAssignByManager(ent, loggedUser);
        sendToVerifyByManager(ent, loggedUser);
    }
    
    @Override
    public void changeBusinessUnit(PositionSave ent, Long businessUnitId, LoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)) {
            return;
        }
        sendToAssignByManager(ent, businessUnitId, null, loggedUser);
        sendToVerifyByManager(ent, businessUnitId, null, loggedUser);
    }

    @Override
    public void changeProfile(PositionSave ent, Long profileId, LoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)) {
            return;
        }
        sendToAssignByManager(ent, null, profileId, loggedUser);
        sendToVerifyByManager(ent, null, profileId, loggedUser);
    }
    
    @Override
    public void sendReminders() {
        handleReminders(dao, Module.COMPLAINT.getKey(),
                new ToAssign(dao),
                new ToEvaluate(dao),
                new ToRespond(dao),
                new ToVerify(dao));
    }

    @Override
    public void sendNotices() {
    }

    @Override
    public void sendDeadlines() {
    }


}
