
package qms.complaint.core;

import DPMS.Mapping.Complaint;
import Framework.Config.SortedPagedFilter;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.IPendingCountByType;
import ape.pending.core.IPendingHandler;
import ape.pending.core.PendingHandlerService;
import mx.bnext.core.util.GridInfo;
import java.util.Map;
import mx.bnext.access.Module;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.complaint.pending.imp.ToAssign;
import qms.complaint.pending.imp.ToEvaluate;
import qms.complaint.pending.imp.ToRespond;
import qms.complaint.pending.imp.ToVerify;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class ComplaintService extends PendingHandlerService<Complaint> implements IPendingHandler, IPendingCountByType {

    private static final long serialVersionUID = -7452847315787690600L;
    
    @Override
    @SMDMethod
    public Map<String, Integer> getPendingMap() {
        return getPendingMap(this, Module.COMPLAINT, getUntypedDAO());
    }

    @Override
    public Integer countTypeByUser(Long loggedUserId, APE pendingType, IUntypedDAO dao) {
        setLoggedUserId(loggedUserId);
        switch (pendingType) {
            case COMPLAINT_TO_ASSIGN:
                return new ToAssign(dao).getCountByUser(loggedUserId);
            case COMPLAINT_TO_RESPOND:
                return new ToRespond(dao).getCountByUser(loggedUserId);
            case COMPLAINT_TO_VERIFY:
                return new ToVerify(dao).getCountByUser(loggedUserId);
            case COMPLAINT_TO_EVALUATE:
                return new ToEvaluate(dao).getCountByUser(loggedUserId);
        }
        return 0;
    }

    @SMDMethod
    public GridInfo getRowsToAssign(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.getActiveRecordRows(getLoggedUserDto(), filter, Complaint.class, new ToAssign(dao));
    }
    
    @SMDMethod
    public GridInfo getRowsToRespond(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.getActiveRecordRows(getLoggedUserDto(), filter, Complaint.class, new ToRespond(dao));
    }
    
    @SMDMethod
    public GridInfo getRowsToVerify(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.getActiveRecordRows(getLoggedUserDto(), filter, Complaint.class, new ToVerify(dao));
    }
    
    @SMDMethod
    public GridInfo getRowsToEvaluate(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.getActiveRecordRows(getLoggedUserDto(), filter, Complaint.class, new ToEvaluate(dao));
    }
    
}
