
package qms.complaint.pending;

import DPMS.Mapping.Profile;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.IPending;
import java.util.Objects;
import javax.annotation.Nonnull;
import mx.bnext.core.security.ISecurityUser;
import qms.access.dto.ILoggedUser;
import qms.complaint.pending.imp.ToAssign;
import qms.complaint.pending.imp.ToEvaluate;
import qms.complaint.pending.imp.ToRespond;
import qms.complaint.pending.imp.ToVerify;
import qms.framework.listeners.OnProfileEdited;
import qms.util.Helper;

/**
 *
 * <AUTHOR>
 */
public class ComplaintPending extends Helper implements IPending {

    public ComplaintPending(IUntypedDAO dao) {
        super(dao);
    }

    public void toAssign(Long complaintId, Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToAssign(dao).execute(complaintId, trigger, loggedUser);
    }
    
    public void toRespond(Long complaintId, Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToRespond(dao).execute(complaintId, trigger, loggedUser);
    }
    
    public void toRespond(Long complaintId, Class trigger, @Nonnull ILoggedUser loggedUser, boolean isDownGrade) {
        new ToRespond(dao).execute(complaintId, trigger, loggedUser);
    }

    public void toVerify(Long complaintId, Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToVerify(dao).execute(complaintId, trigger, loggedUser);
    }

    public void toEvaluate(Long complaintId, Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToEvaluate(dao).execute(complaintId, trigger, loggedUser);
    }
    
    public void toNotApplyComplaint(Long complaintId, Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToAssign(dao).execute(complaintId, trigger, loggedUser);
    }

    @Override
    public void normalize(@Nonnull ILoggedUser loggedUser) {
        final Class trigger = this.getClass();
        new ToAssign(dao).recalculate(loggedUser, trigger);
        new ToVerify(dao).recalculate(loggedUser, trigger);
        new ToRespond(dao).recalculate(loggedUser, trigger);
        new ToEvaluate(dao).recalculate(loggedUser, trigger);
    }

    @Override
    public void normalize(@Nonnull ILoggedUser loggedUser, Long pendingRecordId) {
        final Class trigger = this.getClass();
        new ToAssign(dao).recalculate(loggedUser, trigger, pendingRecordId);
        new ToVerify(dao).recalculate(loggedUser, trigger, pendingRecordId);
        new ToRespond(dao).recalculate(loggedUser, trigger, pendingRecordId);
        new ToEvaluate(dao).recalculate(loggedUser, trigger, pendingRecordId);
    }
    
    @Override
    public void dailyCheck(@Nonnull ILoggedUser loggedUser) {
        final Class trigger = this.getClass();
        new ToAssign(dao).dailyCheck(trigger, loggedUser);
        new ToVerify(dao).dailyCheck(trigger, loggedUser);
        new ToRespond(dao).dailyCheck(trigger, loggedUser);
        new ToEvaluate(dao).dailyCheck(trigger, loggedUser);
    }

    public void changeDepartment(Long complaintId, Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToAssign(dao).execute(complaintId, trigger, loggedUser);
        new ToVerify(dao).execute(complaintId, trigger, loggedUser);
}
    
    public void changeManager(Class trigger, @Nonnull ILoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)) {
            return;
        }
        new ToAssign(dao).recalculate(loggedUser, trigger);
        new ToVerify(dao).recalculate(loggedUser, trigger);  
    }
    
    public void changeManagerLite(Class trigger, @Nonnull ILoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)) {
            return;
        }
        new ToAssign(dao).recalculateActiveAndAttended(loggedUser, trigger);
        new ToVerify(dao).recalculateActiveAndAttended(loggedUser, trigger);
    }
    
    public void editProfile(Profile ent, Profile previousEnt, Class<OnProfileEdited> aClass, @Nonnull ILoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(0)
                || Objects.equals(ent.getIntBEncargadoQueja(), previousEnt.getIntBEncargadoQueja())) {
            return;
        }
        changeManagerLite(aClass, loggedUser);
    }
    
    public void changeDepartmentManager(Class trigger, @Nonnull ILoggedUser loggedUser) {
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(1)) {
            return;
        }
        new ToAssign(dao).recalculateActiveAndAttended(loggedUser, trigger);
        new ToVerify(dao).recalculateActiveAndAttended(loggedUser, trigger);
    }

    public void editSettingsManager(Class trigger, @Nonnull ILoggedUser loggedUser) {
        new ToAssign(dao).recalculate(loggedUser, trigger);
        new ToVerify(dao).recalculate(loggedUser, trigger);  
    }
    
    public void complaintReturned(Long complaintId, Class trigger, @Nonnull ISecurityUser loggedUser) {
        new ToRespond(dao).execute(complaintId, trigger, loggedUser);
        new ToAssign(dao).execute(complaintId, trigger, loggedUser);
    }
}
