/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.complaint.pending;

import DPMS.Mapping.Catalog;
import DPMS.Mapping.Complaint;
import DPMS.Mapping.Priority;
import Framework.Config.Utilities;
import ape.pending.CommonAction;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.BaseAPE;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.IPendingDto;
import ape.pending.util.PendingUtil;
import ape.pending.util.RecordRowsCache;
import ape.pending.util.SqlQueryParser;
import bnext.reference.UserRef;
import isoblock.common.Properties;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import qms.access.dto.ILoggedUser;
import qms.complaint.dto.ComplaintPendingDto;
import qms.complaint.pending.imp.ToAssign;
import qms.complaint.pending.imp.ToEvaluate;
import qms.complaint.pending.imp.ToRespond;
import qms.complaint.pending.imp.ToVerify;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR>
 */
public class ComplainPendingDataSource {
       
    public static Collection<IPendingDto> getPendingRecords(IPendingRecordDAO dao) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        if (loggedUser == null) {
            return Utilities.EMPTY_LIST;
        }
        final Class<? extends BaseAPE> base = Complaint.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
            new ToAssign(dao),
            new ToEvaluate(dao),
            new ToRespond(dao),
            new ToVerify(dao)
        };
        final Integer countPendings = dao.getPendingCount(loggedUser, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return Utilities.EMPTY_SET;
        }
        LinkedHashSet<String> commonActions =
            PendingUtil.commonActions(
                CommonAction.ATTEND, CommonAction.HIDE // <--- Agregar funcionalidad general de pendientes (APE): REASIGNAR.
            );
        String owner = "entity";
        Collection<IPendingDto> results = dao.getPendingRecords(
                loggedUser,
                base,
                owner,
                Arrays.asList(BaseSummaryFieldBuilder.columns(
                    "code = code@" + owner,
                    "description = description@" + owner,
                    "businessUnitDepartmentId = ubicacionId@" + owner,
                    "author.description = authorDescription@" + owner,
                    "description = <EMAIL>",
                    "description = <EMAIL>",
                    "description = complaintSourceDescription@fuente",
                    "description = priorityDescription@priority",
                    "code = catalogCode@catalog"
                )),
                SqlQueryParser.LEFT_JOIN + owner + ".department department " +
                SqlQueryParser.LEFT_JOIN + owner + ".fuente fuente " + 
                SqlQueryParser.LEFT_JOIN + Priority.class.getCanonicalName() + " priority ON priority.id = " + owner + ".priorityId" +
                SqlQueryParser.LEFT_JOIN + Catalog.class.getCanonicalName() + " catalog ON catalog.id.catalogoId = " + Properties.CATALOGO_TEXTO_QUEJA + " AND catalog.id.tipoId = " + owner + ".clasificacion",
                RecordRowsCache.ENABLED,
                operations
        ).stream().map((Map pending) -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            ComplaintPendingDto value = new ComplaintPendingDto();
            // valores de la auditoria
            value.setStatus((Integer) pending.get("status"));
            value.setId((Long) pending.get("complaintId"));
            value.setCode((String) pending.get("code"));
            value.setFechaCreacion((Date) pending.get("fechaCreacion"));
            value.setDescription((String) pending.get("description"));
            value.setBusinessUnitDepartmentId((Long) pending.get("ubicacionId"));
            value.setModule(mx.bnext.access.Module.COMPLAINT);
            //
            value.setDto(ComplaintPendingDto.class.getCanonicalName());
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setBase(UserRef.class.getCanonicalName());
            value.setPendingTypeCode(ape.getCode());
            value.setCommitmentDate(pending.get("commitmentDate") != null ? (Date) pending.get("commitmentDate") : Utilities.getNow());
            value.setCommitmentStartDate(pending.get("commitmentDate") != null ? (Date) pending.get("commitmentDate") : Utilities.getNow());
            value.setCommitmentEndDate(pending.get("commitmentDate") != null ? (Date) pending.get("commitmentDate") : Utilities.getNow());

            value.setAvailableActions(commonActions);
            
            value.setBusinessUnitDepartmentDescription((String) pending.get("departmentDescription") + " - " + (String) pending.get("unitDescription"));
            value.setComplaintSourceDescription((String) pending.get("complaintSourceDescription"));
            value.setPriorityDescription((String) pending.get("priorityDescription"));
            value.setCatalogCode((String) pending.get("catalogCode"));
            value.setAuthorDescription((String) pending.get("authorDescription"));
            return value;
        }).collect(Collectors.toList());
        return results;
    }
       
    public static IPendingOperation[] getPendingOperations(IPendingRecordDAO dao) {
        return new IPendingOperation[] {
            new ToAssign(dao),
            new ToEvaluate(dao),
            new ToRespond(dao),
            new ToVerify(dao)
        };
    }
    
    public static Class<? extends BaseAPE> getBaseEntity(Module module) {
        return Complaint.class;
    }
}
