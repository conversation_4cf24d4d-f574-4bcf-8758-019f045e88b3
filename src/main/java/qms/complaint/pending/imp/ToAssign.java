
package qms.complaint.pending.imp;

import DPMS.Mapping.Complaint;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import qms.complaint.pending.ComplainPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToAssign extends ComplainPendingOperations {
    public static final String TO_ASSIGN_BY_MODULE_MANAGER = ""
            + " FROM " + Complaint.class.getCanonicalName() + " que "
            + " , " + User.class.getCanonicalName() + " usr "
            + " JOIN usr.puestos pst "
            + " JOIN pst.perfil prf "
            + " WHERE que.status = " + Complaint.STATUS.REPORTED.getValue() + " "
            + " AND prf.intBEncargadoQueja = 1 "
            + " AND pst.une.id = que.department.businessUnitId ";
    
    public static final String TO_ASSIGN_BY_DEPARTMENT_MANAGER = ""
            + " FROM " + Complaint.class.getCanonicalName() + " que "
            + " WHERE que.status = " + Complaint.STATUS.REPORTED.getValue() + " ";
    
    public ToAssign(IUntypedDAO dao) {
        super(dao);
        setModuleKey(MODULE);
        setBaseAlias(ALIAS);
        setPendingType(getType(APE.COMPLAINT_TO_ASSIGN));
        setScope(PendingRecord.Scope.USER);
        if (Utilities.getSettings().getComplaintManagerWhoAccept().equals(1)) {
            // Encargado del Modulo de quejas
            setQuery(TO_ASSIGN_BY_MODULE_MANAGER);
            setOwnerField("usr.id");// Encargado del departamento
            setOwnerFieldFilter(BY_MODULE_MANAGER);
        } else {
            // Encargado del depto donde se realiza la queja
            setQuery(TO_ASSIGN_BY_DEPARTMENT_MANAGER);
            setOwnerField("que.department.attendantId");// Por validar que sea el lugar para buscar el usuario
            setOwnerFieldFilter(BY_DEPARTMENT_MANAGER);
        }
        setBase(Complaint.class);
        setOperationType(ApeOperationType.STRONG);
    }
    
}
