
package qms.complaint.pending;

import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.Complaint;
import DPMS.Mapping.PositionSave;
import DPMS.Mapping.Profile;
import DPMS.Mapping.Settings;
import ape.pending.listeners.OnSettingsEditedComplaints;
import java.util.List;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.access.dto.UserCountPendingDTO;
import qms.complaint.listeners.OnAssignedComplaint;
import qms.complaint.listeners.OnAttendedComplaint;
import qms.complaint.listeners.OnChangeDepartment;
import qms.complaint.listeners.OnEvaluatedComplaintSatisfacted;
import qms.complaint.listeners.OnEvaluatedUnsatisfactoryComplaint;
import qms.complaint.listeners.OnNotApplyComplaint;
import qms.complaint.listeners.OnRejectedComplaintAnswer;
import qms.complaint.listeners.OnReportedComplaint;
import qms.complaint.listeners.OnReturnedComplaint;
import qms.complaint.listeners.OnVerifyComplaint;
import qms.framework.listeners.BnextMonitor;
import qms.framework.listeners.OnJobAdded;
import qms.framework.listeners.OnJobRemoved;
import qms.framework.listeners.OnPositionBusinessUnitChanged;
import qms.framework.listeners.OnPositionProfileChanged;
import qms.framework.listeners.OnProfileEdited;


/**
 *
 * <AUTHOR>
 */
@Aspect
public class ComplaintPendingMonitor extends BnextMonitor {
    
    @Around(value = "@annotation(qms.complaint.listeners.OnReportedComplaint) && args(complaintEnt, idDocs, loggedUser)")
    public Object onReportedFinding(ProceedingJoinPoint pjp, Complaint complaintEnt, String idDocs, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toAssign(complaint.getId(), OnReportedComplaint.class, loggedUser);
        }
        return complaint;
    }
    
    @Around(value = "@annotation(qms.complaint.listeners.OnAssignedComplaint) && args(complaintEnt, loggedUser)")
    public Object onAssignedComplaint(ProceedingJoinPoint pjp, Complaint complaintEnt, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toRespond(complaint.getId(), OnAssignedComplaint.class, loggedUser);
        }
        return complaint;
    }
    
    @Around(value = "@annotation(qms.complaint.listeners.OnAttendedComplaint) && args(complaintEnt, loggedUser)")
    public Object onAttendedComplaint(ProceedingJoinPoint pjp, Complaint complaintEnt, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toVerify(complaint.getId(), OnAttendedComplaint.class, loggedUser);
        }
        return complaint;
    }
    
    @Around(value = "@annotation(qms.complaint.listeners.OnVerifyComplaint) && args(complaintEnt, loggedUser)")
    public Object onVerifyComplaint(ProceedingJoinPoint pjp, Complaint complaintEnt, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toEvaluate(complaint.getId(), OnVerifyComplaint.class, loggedUser);
        }
        return complaint;
    }
    
    @Around(value = "@annotation(qms.complaint.listeners.OnEvaluatedComplaintSatisfacted) && args(complaintEnt, loggedUser)")
    public Object onEvaluatedComplaint(ProceedingJoinPoint pjp, Complaint complaintEnt, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toEvaluate(complaint.getId(), OnEvaluatedComplaintSatisfacted.class, loggedUser);
        }
        return complaint;
    }
    
    @Around(value = " @annotation(qms.complaint.listeners.OnEvaluatedUnsatisfactoryComplaint) && args(oldComplaint, loggedUser)")
    public Object onEvaluatedUnsatisfactoryComplaint(ProceedingJoinPoint pjp, Complaint oldComplaint, LoggedUser loggedUser) throws Throwable {
        final Complaint newComplaint = (Complaint) pjp.proceed();
        if (newComplaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toEvaluate(newComplaint.getParentComplaintId(), OnEvaluatedUnsatisfactoryComplaint.class, loggedUser);
            pending.toAssign(newComplaint.getId(), OnEvaluatedUnsatisfactoryComplaint.class, loggedUser);
        }
        return newComplaint;
    }
    
    @Around(value = "@annotation(qms.complaint.listeners.OnNotApplyComplaint) && args(ent, loggedUser)")
    public Object onNotApplyComplaint(ProceedingJoinPoint pjp, Complaint ent, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toNotApplyComplaint(complaint.getId(), OnNotApplyComplaint.class, loggedUser);
        }
        return complaint;
    }
    
    @Around(value= "@annotation(qms.complaint.listeners.OnChangeDepartment) && args(complaintId, departmentId, loggedUser)")
    public Object onChangeDepartment(ProceedingJoinPoint pjp, Long complaintId, Long departmentId, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if(complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.changeDepartment(complaint.getId(), OnChangeDepartment.class, loggedUser);
        }
        return complaint;
    }
    
    /**
     * Catches methods annotated with OnChangeDepartmentAttendant
     *
     * @param pjp
     * @param ent
     * @param newAttendantId
     * @param oldAttendantId
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.configuration.listeners.OnChangeDepartmentAttendant) && args(ent, newAttendantId, oldAttendantId, loggedUser)")
    public Object onChangeDepartmentAttendant(ProceedingJoinPoint pjp, BusinessUnitDepartment ent, Long newAttendantId, Long oldAttendantId, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.changeDepartmentManager(OnJobAdded.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnJobAdded
     *
     * @param pjp
     * @param userId
     * @param jobs
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnJobAdded) && args(userId, countPending, jobs, loggedUser)")
    public Object onJobAdded(
            ProceedingJoinPoint pjp,
            Long userId,
            UserCountPendingDTO countPending,
            List<Long> jobs, 
            ILoggedUser loggedUser
    ) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.changeManager(OnJobAdded.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnJobRemoved
     *
     * @param pjp
     * @param userId
     * @param jobs
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnJobRemoved) && args(userId, countPending, jobs, loggedUser)")
    public Object onJobRemoved(
            ProceedingJoinPoint pjp, 
            UserCountPendingDTO countPending,
            Long userId,
            List<Long> jobs, 
            ILoggedUser loggedUser
    ) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.changeManager(OnJobRemoved.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnPositionProfileChanged
     *
     * @param pjp
     * @param ent
     * @param newProfileId
     * @param loggedUser
     * @param oldProfileId
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnPositionProfileChanged) && args(ent, newProfileId, oldProfileId, loggedUser)")
    public Object onPositionProfileChanged(ProceedingJoinPoint pjp, PositionSave ent, Long newProfileId, Long oldProfileId, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.changeManager(OnPositionProfileChanged.class, loggedUser);
        }
        return result;
    }    
    
    /**
     * Catches methods annotated with OnPositionBusinessUnitChanged
     *
     * @param pjp
     * @param ent
     * @param newBusinessUnitId
     * @param oldBusinessUnitId
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnPositionBusinessUnitChanged) && args(ent, newBusinessUnitId, oldBusinessUnitId, loggedUser)")
    public Object onPositionBusinessUnitChanged(ProceedingJoinPoint pjp, PositionSave ent, Long newBusinessUnitId, Long oldBusinessUnitId, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.changeManager(OnPositionBusinessUnitChanged.class, loggedUser);
        }
        return result;
    } 
    
    /**
     * Catches methods annotated with OnProfileEdited
     *
     * @param pjp
     * @param ent
     * @param previousEnt
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(qms.framework.listeners.OnProfileEdited) && args(ent, previousEnt, loggedUser)")
    public Object onProfileEdited(ProceedingJoinPoint pjp, Profile ent, Profile previousEnt, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.editProfile(ent, previousEnt, OnProfileEdited.class, loggedUser);
        }
        return result;
    }
    
    /**
     * Catches methods annotated with OnSettingsEditedComplaints
     *
     * @param pjp
     * @param ent
     * @param currentComplaintManagerWhoAccept
     * @param oldComplaintManagerWhoAccept
     * @param loggedUser
     * @return
     * @throws Throwable
     */
    @Around(value = "@annotation(ape.pending.listeners.OnSettingsEditedComplaints) && args(ent, currentComplaintManagerWhoAccept, oldComplaintManagerWhoAccept, loggedUser)")
    public Object onSettingsEditedComplaints(ProceedingJoinPoint pjp, Settings ent, Integer currentComplaintManagerWhoAccept, Integer oldComplaintManagerWhoAccept, LoggedUser loggedUser) throws Throwable {
        final Object result = pjp.proceed();
        if (result != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.editSettingsManager(OnSettingsEditedComplaints.class, loggedUser);
        }
        return result;
    }
    
    @Around(value = "@annotation(qms.complaint.listeners.OnRejectedComplaintAnswer) && args(complaintEnt, loggedUser)")
    public Object onRejectedComplaintAnswer(ProceedingJoinPoint pjp, Complaint complaintEnt, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            pending.toRespond(complaint.getId(), OnRejectedComplaintAnswer.class, loggedUser);
        }
        return complaint;
    }
    
    @Around(value = "@annotation(qms.complaint.listeners.OnReturnedComplaint) && args(complaintEnt, newStatus, loggedUser)")
    public Object OnReturnedComplaint(ProceedingJoinPoint pjp, Complaint complaintEnt, String newStatus, LoggedUser loggedUser) throws Throwable {
        final Complaint complaint = (Complaint) pjp.proceed();
        if (complaint != null) {
            ComplaintPending pending = new ComplaintPending(getUntypedDAO(pjp));
            switch(newStatus){
            case "reportedToAssign":
                pending.complaintReturned(complaint.getId(), OnReturnedComplaint.class, loggedUser);
                break;
            }
        }
        return complaint;
    }
    
}
