package qms.project.mail;

import DPMS.Mapping.ActivityLegacy;
import Framework.Config.Utilities;
import java.util.ResourceBundle;
import qms.framework.util.LocaleUtil;
import qms.project.logic.ProjectHelper;

/**
 *
 * <AUTHOR>
 */
public class ActivityDTO {
    
    private String activityName;
    private String status;
    private String description;
    private String dateCreation;
    private String dateStart;
    private String dateEnd;
    private String dateVerification;
    private Double hoursPlanned;
    private String verifier;
    private String attendants;
    
    public ActivityDTO(ActivityLegacy activity, ResourceBundle tags, ProjectHelper helper){
        this.activityName = activity.getCode();
        this.status = this.getStringStatus(activity.getStatus(), tags);
        this.description = activity.getDescription();
        this.dateCreation = Utilities.formatDate(activity.getTspfechahoracreacion());
        this.dateStart = Utilities.formatDate(activity.getDtefechainicio());
        this.dateEnd = Utilities.formatDate(activity.getDtefechafin());
        this.dateVerification = Utilities.formatDate(activity.getDtefechaverificacion());
        this.hoursPlanned = activity.getHorasPlaneadas();
        this.verifier = activity.getVerificador().getDescription();
        this.attendants = helper.getAttendantsActivity(activity);
    }
    
    private String getStringStatus(int i, ResourceBundle tags) {
        switch (i) {
            case 1:
                return LocaleUtil.getTag("status.activity.created", tags);
            case 2:
                return LocaleUtil.getTag("status.activity.in_process", tags);
            case 3:
                return LocaleUtil.getTag("status.activity.concluded", tags);
            case 4:
                return LocaleUtil.getTag("status.activity.verified", tags);
            default:
                return LocaleUtil.getTag("status.undefined", tags);
        }
    }

    /**
     * @return the activityName
     */
    public String getActivityName() {
        return activityName;
    }

    /**
     * @param activityName the activityName to set
     */
    public void SetNameActivity(String activityName) {
        this.activityName = activityName;
    }

    /**
     * @return the status
     */
    public String getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * @param description the description to set
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return the dateCreation
     */
    public String getDateCreation() {
        return dateCreation;
    }

    /**
     * @param dateCreation the dateCreation to set
     */
    public void setDateCreation(String dateCreation) {
        this.dateCreation = dateCreation;
    }

    /**
     * @return the dateStart
     */
    public String getDateStart() {
        return dateStart;
    }

    /**
     * @param dateStart the dateStart to set
     */
    public void setDateStart(String dateStart) {
        this.dateStart = dateStart;
    }

    /**
     * @return the dateEnd
     */
    public String getDateEnd() {
        return dateEnd;
    }

    /**
     * @param dateEnd the dateEnd to set
     */
    public void setDateEnd(String dateEnd) {
        this.dateEnd = dateEnd;
    }

    /**
     * @return the dateVerification
     */
    public String getDateVerification() {
        return dateVerification;
    }

    /**
     * @param dateVerification the dateVerification to set
     */
    public void setDateVerification(String dateVerification) {
        this.dateVerification = dateVerification;
    }

    /**
     * @return the hoursPlanned
     */
    public Double getHoursPlanned() {
        return hoursPlanned;
    }

    /**
     * @param hoursPlanned the hoursPlanned to set
     */
    public void setHoursPlanned(Double hoursPlanned) {
        this.hoursPlanned = hoursPlanned;
    }

    /**
     * @return the verifier
     */
    public String getVerifier() {
        return verifier;
    }

    /**
     * @param verifier the verifier to set
     */
    public void setVerifier(String verifier) {
        this.verifier = verifier;
    }

    /**
     * @return the attendants
     */
    public String getAttendants() {
        return attendants;
    }

    /**
     * @param attendants the attendants to set
     */
    public void setAttendants(String attendants) {
        this.attendants = attendants;
    }
    
}
