/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.dto;

import java.util.List;
import qms.template.entity.TemplateCommentEntity;
import qms.template.entity.TemplateDocumentEntity;
import qms.template.entity.TemplateFileDataTemplate;

/**
 *
 * <AUTHOR>
 */
public class LinkedDataDTO {
    private List<TemplateCommentEntity> comments;
    private List<TemplateDocumentEntity> documents;
    private List<TemplateFileDataTemplate> files;

    public LinkedDataDTO(List<TemplateCommentEntity> comments, List<TemplateDocumentEntity> documents, List<TemplateFileDataTemplate> files) {
        this.comments = comments;
        this.documents = documents;
        this.files = files;
    }

    public LinkedDataDTO() {
    }

    public List<TemplateCommentEntity> getComments() {
        return comments;
    }

    public void setComments(List<TemplateCommentEntity> comments) {
        this.comments = comments;
    }

    public List<TemplateDocumentEntity> getDocuments() {
        return documents;
    }

    public void setDocuments(List<TemplateDocumentEntity> documents) {
        this.documents = documents;
    }

    public List<TemplateFileDataTemplate> getFiles() {
        return files;
    }

    public void setFiles(List<TemplateFileDataTemplate> files) {
        this.files = files;
    }
    
    
}
