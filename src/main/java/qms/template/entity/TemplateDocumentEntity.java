/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import qms.framework.util.CacheConstants;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.template.dto.TemplateGenericEntityDTO;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_generic_entity")
@JsonPropertyOrder(alphabetic = true)
public class TemplateDocumentEntity extends TemplateGenericEntitySuperclass implements IAuditable, IDocumentEntity {

    private String version;
    private String code;
    private String stage;
    private String masterId;

    public TemplateDocumentEntity() {
        this.id = -1L;
    }

    public TemplateDocumentEntity(TemplateGenericEntityDTO generic) {
        this.id = generic.getId();
        this.createdDate = generic.getCreatedDate();
        this.lastModifiedDate = generic.getLastModifiedDate();
        this.createdBy = generic.getCreatedBy();
        this.lastModifiedBy = generic.getLastModifiedBy();
        this.code = generic.getCode();
        this.description = generic.getDescription();
        this.masterId = generic.getMasterId();
        this.stage = generic.getStage();
        this.version = generic.getVersion();
    }
    @Column(name = "master_id")
    @Override
    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "version")
    @Override
    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Column(name = "stage")
    @Override
    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    @Override
    @Column(name = "generic_entity_type")
    @JsonIgnore
    public String getGenericEntityType() {
        return "IDocumentEntity";
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 67 * hash + Objects.hashCode(this.version);
        hash = 67 * hash + Objects.hashCode(this.code);
        hash = 67 * hash + Objects.hashCode(this.id);
        hash = 67 * hash + Objects.hashCode(this.masterId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TemplateDocumentEntity other = (TemplateDocumentEntity) obj;
        if (!Objects.equals(this.version, other.version)) {
            return false;
        }
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return Objects.equals(this.masterId, other.masterId);
    }
    
    
}
