/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import Framework.Config.DomainObject;
import qms.framework.util.CacheConstants;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.activity.dto.DisabledFields;
import qms.activity.dto.HiddenRules;
import qms.template.dto.LinkedDataDTO;
import qms.template.dto.TemporaryDTO;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_activity_row")
@JsonPropertyOrder(alphabetic = true)
public class TemplateActivityRow extends DomainObject implements IActivityRow, IGroupRow, IHiddenRulesEntity, IAuditable, IDisabledFieldsEntity {

    // GroupRow
    private Boolean markRequiredFields;
    private String color;
    private String childColor;
    private String uniqueColor;
    private String customGroupName;
    private String defaultGroupName;
    private String headerSerialized;
    private String selectedActionItem;
    private String selectedNewItem;
    private String temporaryFileNamesSerialized;
    private Set<TemplateDropdownMenuItem> menuOptions;
    private Set<TemplateDropdownMenuItem> hiddenMenuOptions;
    private Set<TemplateDropdownMenuItem> actionItems;
    private Set<TemplateDropdownMenuItem> newItems;
    // Serialized
    private String menuAvailableOptionsSerialized;                              // <-- @JsonIgnore
    private String hiddenMenuAvailableOptionsSerialized;                        // <-- @JsonIgnore
    // Template
    private TemplateActivityTemplate template;                                  // <-- @JsonIgnore
    // ActivityRow
    private Date createdDate;
    private Date lastModifiedDate;
    private Date temporaryStartDate;
    private Boolean startVerificationOn;
    private Boolean finishVerificationOn;
    private Boolean deleted;
    private Boolean hasChild;
    private Boolean isGroup;
    private Boolean isMainParent;
    private Boolean miniView;
    private Boolean showComments;
    private Boolean showDocuments;
    private Boolean showFiles;
    private Integer status;
    private Integer level;
    private Long createdBy;
    private Long lastModifiedBy;
    private String rowId;
    private String rowCount;
    private String code;
    private String description;
    // ParentRow & Childs
    private TemplateActivityRow parentRow;
    @JsonPropertyOrder(alphabetic = true)
    private Set<TemplateActivityRow> rows;
    // ActivityTemplate
    @JsonPropertyOrder(alphabetic = true)
    private TemplateActivity activity;
    // DisabledFields
    private Boolean hasDisabledFields;
    private Boolean disabledAnticipationAttendDay;
    private Boolean disabledBudId;
    private Boolean disabledBusinessUnitId;
    private Boolean disabledCode;
    private Boolean disabledDaysToVerify;
    private Boolean disabledFillType;
    private Boolean disabledFillForm;
    private Boolean belongSeries;
    private Boolean disabledImplementation;
    private Boolean disabledImplementationPeriodic;
    private Boolean disabledImplementer;
    private Boolean disabledPreImplementer = false;
    private Boolean disabledObjectiveId;
    private Boolean disabledPriority;
    private Boolean disabledSource;
    private Boolean disabledTypeId;
    private Boolean disabledVerification;
    private Boolean disabledVerificationPeriodic;
    private Boolean disabledVerifier;
    private Boolean disabledStartDate;
    private Boolean disabledIsPlanned = false;
    private Boolean disabledPlannedHours = false;
    private Boolean disabledPlannerTask = false;
    private Boolean disabledCategoryId;
    private Boolean disabledActivityOrder = false;
    // HiddenRules
    private Boolean hasHiddenRules;
    private Boolean hiddenAnticipationAttendDay;                                // <-- @JsonIgnore
    private Boolean hiddenBudId;                                                // <-- @JsonIgnore
    private Boolean hiddenBusinessUnitId;                                       // <-- @JsonIgnore
    private Boolean hiddenCode;                                                 // <-- @JsonIgnore
    private Boolean hiddenDaysToVerify;                                         // <-- @JsonIgnore
    private Boolean hiddenFillType;                                             // <-- @JsonIgnore
    private Boolean hiddenFillForm;                                             // <-- @JsonIgnore
    private Boolean hiddenBelongSeries;                                         // <-- @JsonIgnore
    private Boolean hiddenImplementation;                                       // <-- @JsonIgnore
    private Boolean hiddenImplementationPeriodic;                               // <-- @JsonIgnore
    private Boolean hiddenImplementer;                                          // <-- @JsonIgnore
    private Boolean hiddenPreImplementer = true;                                       // <-- @JsonIgnore
    private Boolean hiddenPlannerTask;                                          // <-- @JsonIgnore
    private Boolean hiddenObjectiveId;                                          // <-- @JsonIgnore
    private Boolean hiddenPriority;                                             // <-- @JsonIgnore
    private Boolean hiddenSource;                                               // <-- @JsonIgnore
    private Boolean hiddenTypeId;                                               // <-- @JsonIgnore
    private Boolean hiddenVerification;                                         // <-- @JsonIgnore
    private Boolean hiddenVerificationPeriodic;                                 // <-- @JsonIgnore
    private Boolean hiddenVerifier;                                             // <-- @JsonIgnore
    private Boolean hiddenDocuments;                                            // <-- @JsonIgnore
    private Boolean hiddenStartDate;                                            // <-- @JsonIgnore
    private Boolean hiddenIsPlanned = true;                                     // <-- @JsonIgnore
    private Boolean hiddenPlannedHours = true;                                  // <-- @JsonIgnore
    private Boolean hiddenEnableDeliverySetUp;                                  // <-- @JsonIgnore
    private Boolean hiddenTaskDeliveryTypeId;                                   // <-- @JsonIgnore  
    private Boolean hiddenTaskCategoryId;                                       // <-- @JsonIgnore
    private Boolean hiddenParticipants;                                         // <-- @JsonIgnore  
    private Boolean hiddenCategoryId;                                           // <-- @JsonIgnore 
    private Boolean hiddenActivityOrder = true;                                        // <-- @JsonIgnore
    // Campos dinamicos
    private Set<TemplateDataMapVariableDrop> isVariableDroppableList;
    private Set<TemplateDataMapVariableItem> variablesList;
    private Set<TemplateDataMap> implementerList;
    // RowLinkedData
    private Boolean fileGroup;
    private Set<TemplateFileDataTemplate> files = new HashSet<>();                              // <-- @JsonIgnore
    private Set<TemplateDocumentEntity> documents = new HashSet<>();                            // <-- @JsonIgnore
    private Set<TemplateCommentEntity> comments = new HashSet<>();                              // <-- @JsonIgnore
    // DisabledDatesHolder
    private Set<TemplateDateRange> implementationDisabledDates;
    private Set<TemplateDateRange> implementationPeriodicityDisabledDates;
    private Set<TemplateDateRange> startDisabledDates;
    private Set<TemplateDateRange> verificationDisabledDates;
    private Set<TemplateDateRange> verificationPeriodicityDisabledDates;

    public TemplateActivityRow() {
        this.id = -1L;
        this.deleted = false;
        this.status = 1;
    }

    @Id
    @Column(name = "template_activity_row_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    @JsonIgnore
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToOne(cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "template_activity_row_parentid")
    @JsonIgnore
    public TemplateActivityRow getParentRow() {
        return parentRow;
    }

    @Override
    public void setParentRow(TemplateActivityRow parentRow) {
        this.parentRow = parentRow;
    }

    @OneToMany(mappedBy = "parentRow", cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    public Set<TemplateActivityRow> getRows() {
        return rows;
    }

    @Override
    public void setRows(Set<TemplateActivityRow> rows) {
        this.rows = rows;
        if (this.id != null && this.id > 0) {
            return;
        }
        if (rows == null) {
            return;
        }
        this.rows.forEach((row) -> {
            row.setParentRow(this);
        });
    }

    @Column(name = "row_id")
    @Override
    public String getRowId() {
        return rowId;
    }

    @Override
    public void setRowId(String rowId) {
        this.rowId = rowId;
    }

    @Column(name = "row_count")
    @Override
    public String getRowCount() {
        return rowCount;
    }

    @Override
    public void setRowCount(String rowCount) {
        this.rowCount = rowCount;
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "level")
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    @Column(name = "mini_view")
    @Type(type = "numeric_boolean")
    @Override
    public Boolean getMiniView() {
        return miniView;
    }

    @Override
    public void setMiniView(Boolean miniView) {
        this.miniView = miniView;
    }

    @Column(name = "deleted")
    @Type(type = "numeric_boolean")
    @Override
    public Boolean getDeleted() {
        return this.deleted;
    }

    @Override
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Column(name = "hidden_menu_available_options")
    @JsonIgnore
    public String getHiddenMenuAvailableOptionsSerialized() {
        return this.hiddenMenuAvailableOptionsSerialized;
    }

    public void setHiddenMenuAvailableOptionsSerialized(String hiddenMenuAvailableOptionsSerialized) {
        this.hiddenMenuAvailableOptionsSerialized = hiddenMenuAvailableOptionsSerialized;
    }

    @Column(name = "has_hidden_rules")
    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getHasHiddenRules() {
        return hasHiddenRules;
    }

    @Override
    public void setHasHiddenRules(Boolean hasHiddenRules) {
        this.hasHiddenRules = hasHiddenRules;
    }
    

    @Column(name = "hidden_anticipation_attend_day")
    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getHiddenAnticipationAttendDay() {
        return this.hiddenAnticipationAttendDay;
    }

    @Override
    public void setHiddenAnticipationAttendDay(Boolean hiddenAnticipationAttendDay) {
        this.hiddenAnticipationAttendDay = hiddenAnticipationAttendDay;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_bud_id")
    public Boolean getHiddenBudId() {
        return this.hiddenBudId;
    }

    @Override
    public void setHiddenBudId(Boolean hiddenBudId) {
        this.hiddenBudId = hiddenBudId;
    }

    @JsonIgnore
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_business_unit_id")
    @Override
    public Boolean getHiddenBusinessUnitId() {
        return this.hiddenBusinessUnitId;
    }

    @Override
    public void setHiddenBusinessUnitId(Boolean hiddenBusinessUnitId) {
        this.hiddenBusinessUnitId = hiddenBusinessUnitId;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_code")
    public Boolean getHiddenCode() {
        return this.hiddenCode;
    }

    @Override
    public void setHiddenCode(Boolean hiddenCode) {
        this.hiddenCode = hiddenCode;
    }

    @JsonIgnore
    @Column(name = "hidden_days_to_verify")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getHiddenDaysToVerify() {
        return this.hiddenDaysToVerify;
    }

    @Override
    public void setHiddenDaysToVerify(Boolean hiddenDaysToVerify) {
        this.hiddenDaysToVerify = hiddenDaysToVerify;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_fill_type")
    public Boolean getHiddenFillType() {
        return this.hiddenFillType;
    }

    @Override
    public void setHiddenFillType(Boolean hiddenFillType) {
        this.hiddenFillType = hiddenFillType;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_fill_form")
    public Boolean getHiddenFillForm() {
        return this.hiddenFillForm;
    }

    @Override
    public void setHiddenFillForm(Boolean hiddenFillForm) {
        this.hiddenFillForm = hiddenFillForm;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_belong_series")
    public Boolean getHiddenBelongSeries() {
        return hiddenBelongSeries;
    }

    @Override
    public void setHiddenBelongSeries(Boolean hiddenBelongSeries) {
        this.hiddenBelongSeries = hiddenBelongSeries;
    }
    
    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_implementation")
    public Boolean getHiddenImplementation() {
        return this.hiddenImplementation;
    }

    @Override
    public void setHiddenImplementation(Boolean hiddenImplementation) {
        this.hiddenImplementation = hiddenImplementation;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_implementation_periodic")
    public Boolean getHiddenImplementationPeriodic() {
        return this.hiddenImplementationPeriodic;
    }

    @Override
    public void setHiddenImplementationPeriodic(Boolean hiddenImplementationPeriodic) {
        this.hiddenImplementationPeriodic = hiddenImplementationPeriodic;
    }

    @JsonIgnore
    @Column(name = "hidden_implementer")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getHiddenImplementer() {
        return this.hiddenImplementer;
    }

    @Override
    public void setHiddenImplementer(Boolean hiddenImplementer) {
        this.hiddenImplementer = hiddenImplementer;
    }

    @JsonIgnore
    @Column(name = "hidden_pre_implementer")
    @Type(type = "numeric_boolean")
    @Override
    public Boolean getHiddenPreImplementer() {
        return hiddenPreImplementer;
    }

    @Override
    public void setHiddenPreImplementer(Boolean hiddenPreImplementer) {
        this.hiddenPreImplementer = hiddenPreImplementer;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_objective_id")
    public Boolean getHiddenObjectiveId() {
        return this.hiddenObjectiveId;
    }

    @Override
    public void setHiddenObjectiveId(Boolean hiddenObjectiveId) {
        this.hiddenObjectiveId = hiddenObjectiveId;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_planner_task_id")
    public Boolean getHiddenPlannerTask() {
        return hiddenPlannerTask;
    }

    @Override
    public void setHiddenPlannerTask(Boolean hiddenPlannerTask) {
        this.hiddenPlannerTask = hiddenPlannerTask;
    }

    @JsonIgnore
    @Column(name = "hidden_priority")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getHiddenPriority() {
        return this.hiddenPriority;
    }

    @Override
    public void setHiddenPriority(Boolean hiddenPriority) {
        this.hiddenPriority = hiddenPriority;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_source")
    public Boolean getHiddenSource() {
        return this.hiddenSource;
    }

    @Override
    public void setHiddenSource(Boolean hiddenSource) {
        this.hiddenSource = hiddenSource;
    }

    @JsonIgnore
    @Type(type = "numeric_boolean")
    @Override
    @Column(name = "hidden_type_id")
    public Boolean getHiddenTypeId() {
        return this.hiddenTypeId;
    }

    @Override
    public void setHiddenTypeId(Boolean hiddenTypeId) {
        this.hiddenTypeId = hiddenTypeId;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_verification")
    public Boolean getHiddenVerification() {
        return this.hiddenVerification;
    }

    @Override
    public void setHiddenVerification(Boolean hiddenVerification) {
        this.hiddenVerification = hiddenVerification;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_verification_periodic")
    public Boolean getHiddenVerificationPeriodic() {
        return this.hiddenVerificationPeriodic;
    }

    @Override
    public void setHiddenVerificationPeriodic(Boolean hiddenVerificationPeriodic) {
        this.hiddenVerificationPeriodic = hiddenVerificationPeriodic;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_verifier")
    public Boolean getHiddenVerifier() {
        return this.hiddenVerifier;
    }

    @Override
    public void setHiddenVerifier(Boolean hiddenVerifier) {
        this.hiddenVerifier = hiddenVerifier;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_documents")
    public Boolean getHiddenDocuments() {
        return this.hiddenDocuments;
    }

    @Override
    public void setHiddenDocuments(Boolean hiddenDocuments) {
        this.hiddenDocuments = hiddenDocuments;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_start_date")
    public Boolean getHiddenStartDate() {
        return hiddenStartDate;
    }

    @Override
    public void setHiddenStartDate(Boolean hiddenStartDate) {
        this.hiddenStartDate = hiddenStartDate;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_is_planned")
    public Boolean getHiddenIsPlanned() {
        return hiddenIsPlanned;
    }

    @Override
    public void setHiddenIsPlanned(Boolean hiddenIsPlanned) {
        this.hiddenIsPlanned = hiddenIsPlanned;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_planned_hours")
    public Boolean getHiddenPlannedHours() {
        return hiddenPlannedHours;
    }

    @Override
    public void setHiddenPlannedHours(Boolean hiddenPlannedHours) {
        this.hiddenPlannedHours = hiddenPlannedHours;
    }
    
    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "has_disabled_fields")
    public Boolean getHasDisabledFields() {
        return hasDisabledFields;
    }

    @Override
    public void setHasDisabledFields(Boolean hasDisabledFields) {
        this.hasDisabledFields = hasDisabledFields;
    }
        
    @Override
    public void setDisabledAnticipationAttendDay(Boolean disabledAnticipationattendday) {
        this.disabledAnticipationAttendDay = disabledAnticipationattendday;
    }

    @Override
    @JsonIgnore
    @Column(name = "disabled_anticipationattendday")
    public Boolean getDisabledAnticipationAttendDay() {
        return this.disabledAnticipationAttendDay;
    }

    @Override
    public void setDisabledBudId(Boolean disabledBudId) {
        this.disabledBudId = disabledBudId;
    }

    @Column(name = "disabled_bud_id")
    @Override
    @JsonIgnore
    public Boolean getDisabledBudId() {
        return this.disabledBudId;
    }

    @Override
    public void setDisabledBusinessUnitId(Boolean disabledBusinessUnitId) {
        this.disabledBusinessUnitId = disabledBusinessUnitId;
    }

    @Override
    @JsonIgnore
    @Column(name = "disabled_business_unit_id")
    public Boolean getDisabledBusinessUnitId() {
        return this.disabledBusinessUnitId;
    }

    @Override
    public void setDisabledCode(Boolean disabledCode) {
        this.disabledCode = disabledCode;
    }

    @Override
    @Column(name = "disabled_code")
    @JsonIgnore
    public Boolean getDisabledCode() {
        return this.disabledCode;
    }

    @Override
    public void setDisabledDaysToVerify(Boolean disabledDaysToVerify) {
        this.disabledDaysToVerify = disabledDaysToVerify;
    }

    @Override
    @Column(name = "disabled_days_to_verify")
    @JsonIgnore
    public Boolean getDisabledDaysToVerify() {
        return this.disabledDaysToVerify;
    }

    @Override
    public void setDisabledFillType(Boolean disabledFillType) {
        this.disabledFillType = disabledFillType;
    }

    @Override
    @Column(name = "disabled_fill_type")
    @JsonIgnore
    public Boolean getDisabledFillType() {
        return this.disabledFillType;
    }

    @Override
    public void setDisabledFillForm(Boolean disabledFillForm) {
        this.disabledFillForm = disabledFillForm;
    }

    @Override
    @Column(name = "disabled_fill_form")
    @JsonIgnore
    public Boolean getDisabledFillForm() {
        return this.disabledFillForm;
    }

    @Override
    @Column(name = "disabled_belong_series")
    @JsonIgnore
    public Boolean getBelongSeries() {
        return belongSeries;
    }

    @Override
    public void setBelongSeries(Boolean belongSeries) {
        this.belongSeries = belongSeries;
    }
    
    @Override
    public void setDisabledImplementation(Boolean disabledImplementation) {
        this.disabledImplementation = disabledImplementation;
    }

    @Override
    @Column(name = "disabled_implementation")
    @JsonIgnore
    public Boolean getDisabledImplementation() {
        return this.disabledImplementation;
    }

    @Override
    public void setDisabledImplementationPeriodic(Boolean disabledImplementationPeriod) {
        this.disabledImplementationPeriodic = disabledImplementationPeriod;
    }

    @Override
    @Column(name = "disabled_implementation_period")
    @JsonIgnore
    public Boolean getDisabledImplementationPeriodic() {
        return this.disabledImplementationPeriodic;
    }

    @Override
    public void setDisabledImplementer(Boolean disabledImplementer) {
        this.disabledImplementer = disabledImplementer;
    }

    @JsonIgnore
    @Column(name = "disabled_pre_implementer")
    @Type(type = "numeric_boolean")
    @Override
    public Boolean getDisabledPreImplementer() {
        return disabledPreImplementer;
    }

    @Override
    public void setDisabledPreImplementer(Boolean disabledPreImplementer) {
        this.disabledPreImplementer = disabledPreImplementer;
    }

    @Override
    @Column(name = "disabled_implementer")
    @JsonIgnore
    public Boolean getDisabledImplementer() {
        return this.disabledImplementer;
    }

    @Override
    public void setDisabledObjectiveId(Boolean disabledObjectiveId) {
        this.disabledObjectiveId = disabledObjectiveId;
    }

    @Override
    @Column(name = "disabled_objective_id")
    @JsonIgnore
    public Boolean getDisabledObjectiveId() {
        return this.disabledObjectiveId;
    }

    @Override
    public void setDisabledPriority(Boolean disabledPriority) {
        this.disabledPriority = disabledPriority;
    }

    @Override
    @Column(name = "disabled_priority")
    @JsonIgnore
    public Boolean getDisabledPriority() {
        return this.disabledPriority;
    }

    @Override
    public void setDisabledSource(Boolean disabledSource) {
        this.disabledSource = disabledSource;
    }

    @Override
    @Column(name = "disabled_source")
    @JsonIgnore
    public Boolean getDisabledSource() {
        return this.disabledSource;
    }

    @Override
    public void setDisabledTypeId(Boolean disabledTypeId) {
        this.disabledTypeId = disabledTypeId;
    }

    @Override
    @Column(name = "disabled_type_id")
    @JsonIgnore
    public Boolean getDisabledTypeId() {
        return this.disabledTypeId;
    }

    @Override
    public void setDisabledVerification(Boolean disabledVerification) {
        this.disabledVerification = disabledVerification;
    }

    @Override
    @Column(name = "disabled_verification")
    @JsonIgnore
    public Boolean getDisabledVerification() {
        return this.disabledVerification;
    }

    @Override
    public void setDisabledVerificationPeriodic(Boolean disabledVerificationPeriodic) {
        this.disabledVerificationPeriodic = disabledVerificationPeriodic;
    }

    @Override
    @Column(name = "disabled_verification_periodic")
    @JsonIgnore
    public Boolean getDisabledVerificationPeriodic() {
        return this.disabledVerificationPeriodic;
    }

    @Override
    public void setDisabledVerifier(Boolean disabledVerifier) {
        this.disabledVerifier = disabledVerifier;
    }

    @Override
    @Column(name = "disabled_verifier")
    @JsonIgnore
    public Boolean getDisabledVerifier() {
        return this.disabledVerifier;
    }

    @Override
    public void setDisabledStartDate(Boolean disabledStartDate) {
        this.disabledStartDate = disabledStartDate;
    }

    @Override
    @Column(name = "disabled_start_date")
    @JsonIgnore
    public Boolean getDisabledStartDate() {
        return disabledStartDate;
    } 
    

    @Override
    public void setDisabledIsPlanned(Boolean disabledIsPlanned) {
        this.disabledIsPlanned = disabledIsPlanned;
    }

    @Override
    @Column(name = "disabled_is_planned")
    @Type(type = "numeric_boolean")
    @JsonIgnore
    public Boolean getDisabledIsPlanned() {
        return disabledIsPlanned;
    } 

    @Override
    @Column(name = "disabled_planned_hours")
    @Type(type = "numeric_boolean")
    @JsonIgnore
    public Boolean getDisabledPlannedHours() {
        return disabledPlannedHours;
    }

    @Override
    public void setDisabledPlannedHours(Boolean disabledPlannedHours) {
        this.disabledPlannedHours = disabledPlannedHours;
    }

    @Override
    @Column(name = "disabled_planner_task")
    @Type(type = "numeric_boolean")
    @JsonIgnore
    public Boolean getDisabledPlannerTask() {
        return disabledPlannerTask;
    }

    @Override
    public void setDisabledPlannerTask(Boolean disabledPlannerTask) {
        this.disabledPlannerTask = disabledPlannerTask;
    }
        
    @Override
    @LastModifiedDate
    @Column(name = "last_modified_date")
    @JsonIgnore
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_date", updatable = false)
    @Override
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @JsonIgnore
    public Date getCreatedDate() {
        return this.createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "created_by")
    @JsonIgnore
    @Override
    public Long getCreatedBy() {
        return this.createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @Override
    @JsonIgnore
    public Long getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "temporary_start_date")
    @Override
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @JsonIgnore
    public Date getTemporaryStartDate() {
        return this.temporaryStartDate;
    }

    @Override
    public void setTemporaryStartDate(Date temporaryStartDate) {
        this.temporaryStartDate = temporaryStartDate;
    }

    @Column(name = "is_group")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getIsGroup() {
        return this.isGroup;
    }

    @Override
    public void setIsGroup(Boolean isGroup) {
        this.isGroup = isGroup;
    }

    @Column(name = "is_main_parent")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getIsMainParent() {
        return isMainParent;
    }

    @Override
    public void setIsMainParent(Boolean isMainParent) {
        this.isMainParent = isMainParent;
    }

    @Column(name = "show_comments")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getShowComments() {
        return showComments;
    }

    @Override
    public void setShowComments(Boolean showComments) {
        this.showComments = showComments;
    }

    @Column(name = "show_documents")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getShowDocuments() {
        return showDocuments;
    }

    @Override
    public void setShowDocuments(Boolean showDocuments) {
        this.showDocuments = showDocuments;
    }

    @Column(name = "show_files")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getShowFiles() {
        return showFiles;
    }

    @Override
    public void setShowFiles(Boolean showFiles) {
        this.showFiles = showFiles;
    }

    @Override
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @OneToOne(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @JoinColumn(name = "template_activity_id")
    public TemplateActivity getActivity() {
        return activity;
    }

    @Override
    public void setActivity(TemplateActivity activity) {
        this.activity = activity;
    }

    @Override
    @Column(name = "file_group")
    @Type(type = "numeric_boolean")
    public Boolean getFileGroup() {
        return fileGroup;
    }

    @Override
    public void setFileGroup(Boolean fileGroup) {
        this.fileGroup = fileGroup;
    }

    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "has_child")
    public Boolean getHasChild() {
        return hasChild;
    }

    @Override
    public void setHasChild(Boolean hasChild) {
        this.hasChild = hasChild;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "template_activity_row_file",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_generic_entity_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonIgnore
    public Set<TemplateFileDataTemplate> getFiles() {
        return files;
    }

    @Override
    public void setFiles(Set<TemplateFileDataTemplate> files) {
        if (files == null) {
            files = new HashSet<>();
        }
        this.files = files;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "template_activity_row_document",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_generic_entity_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonIgnore
    public Set<TemplateDocumentEntity> getDocuments() {
        return documents;
    }

    @Override
    public void setDocuments(Set<TemplateDocumentEntity> documents) {
        if (documents == null) {
            documents = new HashSet<>();
        }
        this.documents = documents;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "template_activity_row_comment",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_generic_entity_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonIgnore
    public Set<TemplateCommentEntity> getComments() {
        return comments;
    }

    @Override
    public void setComments(Set<TemplateCommentEntity> comments) {
        if (comments == null) {
            comments = new HashSet<>();
        }
        this.comments = comments;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "tem_activity_row_variable_drop",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_data_map_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonIgnore
    public Set<TemplateDataMapVariableDrop> getIsVariableDroppableList() {
        return isVariableDroppableList;
    }

    @Override
    public void setIsVariableDroppableList(Set<TemplateDataMapVariableDrop> isVariableDroppableList) {
        this.isVariableDroppableList = isVariableDroppableList;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "tem_activity_row_variable",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_data_map_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonIgnore
    public Set<TemplateDataMapVariableItem> getVariablesList() {
        return variablesList;
    }

    @Override
    public void setVariablesList(Set<TemplateDataMapVariableItem> variablesList) {
        this.variablesList = variablesList;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "tem_activity_row_implementer",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_data_map_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonIgnore
    public Set<TemplateDataMap> getImplementerList() {
        return implementerList;
    }

    @Override
    public void setImplementerList(Set<TemplateDataMap> implementerList) {
        this.implementerList = implementerList;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "tem_activity_row_disabled_impl",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_date_range_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    public Set<TemplateDateRange> getImplementationDisabledDates() {
        return implementationDisabledDates;
    }

    @Override
    public void setImplementationDisabledDates(Set<TemplateDateRange> implementationDisabledDates) {
        this.implementationDisabledDates = implementationDisabledDates;
    }
    
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
        name = "tem_activity_row_dis_impl_rec",
        joinColumns = @JoinColumn(name = "template_activity_row_id"),
        inverseJoinColumns = @JoinColumn(name = "template_date_range_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    public Set<TemplateDateRange> getImplementationPeriodicityDisabledDates() {
        return implementationPeriodicityDisabledDates;
    }

    @Override
    public void setImplementationPeriodicityDisabledDates(Set<TemplateDateRange> implementationPeriodicityDisabledDates) {
        this.implementationPeriodicityDisabledDates = implementationPeriodicityDisabledDates;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "tem_activity_row_disabled_strt",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_date_range_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    public Set<TemplateDateRange> getStartDisabledDates() {
        return startDisabledDates;
    }

    @Override
    public void setStartDisabledDates(Set<TemplateDateRange> startDisabledDates) {
        this.startDisabledDates = startDisabledDates;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "tem_activity_row_disabled_verf",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_date_range_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    public Set<TemplateDateRange> getVerificationDisabledDates() {
        return verificationDisabledDates;
    }

    @Override
    public void setVerificationDisabledDates(Set<TemplateDateRange> verificationDisabledDates) {
        this.verificationDisabledDates = verificationDisabledDates;
    }
    
    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "tem_activity_row_dis_ver_rec",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_date_range_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    public Set<TemplateDateRange> getVerificationPeriodicityDisabledDates() {
        return verificationPeriodicityDisabledDates;
    }

    @Override
    public void setVerificationPeriodicityDisabledDates(Set<TemplateDateRange> verificationPeriodicityDisabledDates) {
        this.verificationPeriodicityDisabledDates = verificationPeriodicityDisabledDates;
    }

    @Column(name = "color")
    @Override
    public String getColor() {
        return this.color;
    }

    @Override
    public void setColor(String color) {
        this.color = color;
    }

    @Column(name = "child_color")
    @Override
    public String getChildColor() {
        return childColor;
    }

    @Override
    public void setChildColor(String childColor) {
        this.childColor = childColor;
    }

    @Column(name = "unique_color")
    @Override
    public String getUniqueColor() {
        return uniqueColor;
    }

    @Override
    public void setUniqueColor(String uniqueColor) {
        this.uniqueColor = uniqueColor;
    }

    @Column(name = "custom_group_name")
    @Override
    public String getCustomGroupName() {
        return this.customGroupName;
    }

    @Override
    public void setCustomGroupName(String customGroupName) {
        this.customGroupName = customGroupName;
    }

    @Column(name = "mark_required_fields")
    @Override
    @Type(type = "numeric_boolean")
    public Boolean getMarkRequiredFields() {
        return this.markRequiredFields;
    }

    @Override
    public void setMarkRequiredFields(Boolean markRequiredFields) {
        this.markRequiredFields = markRequiredFields;
    }

    @Column(name = "default_group_name")
    @Override
    public String getDefaultGroupName() {
        return this.defaultGroupName;
    }

    @Override
    public void setDefaultGroupName(String defaultGroupName) {
        this.defaultGroupName = defaultGroupName;
    }

    @Column(name = "header")
    @JsonIgnore
    public String getHeaderSerialized() {
        return this.headerSerialized;
    }

    public void setHeaderSerialized(String headerSerialized) {
        this.headerSerialized = headerSerialized;
    }

    @Column(name = "selected_action_item")
    public String getSelectedActionItem() {
        return selectedActionItem;
    }

    public void setSelectedActionItem(String selectedActionItem) {
        this.selectedActionItem = selectedActionItem;
    }

    @Column(name = "selected_new_item")
    public String getSelectedNewItem() {
        return selectedNewItem;
    }

    public void setSelectedNewItem(String selectedNewItem) {
        this.selectedNewItem = selectedNewItem;
    }

    @Column(name = "temporary_file_names")
    @JsonIgnore
    public String getTemporaryFileNamesSerialized() {
        return temporaryFileNamesSerialized;
    }

    public void setTemporaryFileNamesSerialized(String temporaryFileNamesSerialized) {
        this.temporaryFileNamesSerialized = temporaryFileNamesSerialized;
    }

    @Column(name = "menu_available_options")
    @JsonIgnore
    public String getMenuAvailableOptionsSerialized() {
        return this.menuAvailableOptionsSerialized;
    }

    public void setMenuAvailableOptionsSerialized(String menuAvailableOptionsSerialized) {
        this.menuAvailableOptionsSerialized = menuAvailableOptionsSerialized;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "template_activityrow_menu",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_dropdown_menu_item_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonPropertyOrder(alphabetic = true)
    public Set<TemplateDropdownMenuItem> getMenuOptions() {
        return menuOptions;
    }

    @Override
    public void setMenuOptions(Set<TemplateDropdownMenuItem> menuOptions) {
        this.menuOptions = menuOptions;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "template_activityrow_hidemenu",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_dropdown_menu_item_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonPropertyOrder(alphabetic = true)
    public Set<TemplateDropdownMenuItem> getHiddenMenuOptions() {
        return hiddenMenuOptions;
    }

    @Override
    public void setHiddenMenuOptions(Set<TemplateDropdownMenuItem> hiddenMenuOptions) {
        this.hiddenMenuOptions = hiddenMenuOptions;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "template_activityrow_actionopt",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_dropdown_menu_item_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonPropertyOrder(alphabetic = true)
    public Set<TemplateDropdownMenuItem> getActionItems() {
        return actionItems;
    }

    @Override
    public void setActionItems(Set<TemplateDropdownMenuItem> actionItems) {
        this.actionItems = actionItems;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
            name = "template_activityrow_newitems",
            joinColumns = @JoinColumn(name = "template_activity_row_id"),
            inverseJoinColumns = @JoinColumn(name = "template_dropdown_menu_item_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    @Override
    @JsonPropertyOrder(alphabetic = true)
    public Set<TemplateDropdownMenuItem> getNewItems() {
        return newItems;
    }

    @Override
    public void setNewItems(Set<TemplateDropdownMenuItem> newItems) {
        this.newItems = newItems;
    }

    @JsonIgnore
    @OneToOne(cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinColumn(name = "template_activity_template_id")
    public TemplateActivityTemplate getTemplate() {
        return template;
    }

    public void setTemplate(TemplateActivityTemplate template) {
        this.template = template;
    }

    @Override
    @Transient
    public HiddenRules getHiddenRules() {
        if (this.hasHiddenRules != null && this.hasHiddenRules) {
            return new HiddenRules(this);
        } else {
            return null;
        }
    }

    @Override
    public void setHiddenRules(HiddenRules hiddenRules) {
        if (hiddenRules == null) {
            this.hasHiddenRules = false;
        } else {
            this.hasHiddenRules = true;
            IHiddenRulesEntity.fillFromHiddenRules(this, hiddenRules);
        }
    }
    
    @Override
    @Transient
    public DisabledFields getDisabledFields() {
        if (hasDisabledFields != null && this.hasDisabledFields) {
            return new DisabledFields(this);
        } else {
            return null;
        }
    }

    @Override
    public void setDisabledFields(DisabledFields disabledFields) {
        if (disabledFields == null) {
            this.hasDisabledFields = false;
        } else {
            this.hasDisabledFields = true;
            IDisabledFieldsEntity.fillFromDisabledFields(this, disabledFields);
        }
    }

    @Override
    @Transient
    public String[] getHiddenMenuAvailableOptions() {
        if (this.hiddenMenuAvailableOptionsSerialized == null) {
            return null;
        }
        return this.hiddenMenuAvailableOptionsSerialized.split("\\s*,\\s*");
    }

    @Override
    public void setHiddenMenuAvailableOptions(String[] hiddenMenuAvailableOptions) {
        if (hiddenMenuAvailableOptions == null) {
            this.hiddenMenuAvailableOptionsSerialized = null;
        } else {
            this.hiddenMenuAvailableOptionsSerialized = StringUtils.join(hiddenMenuAvailableOptions, ",");
        }
    }

    @Override
    @Transient
    public String[] getMenuAvailableOptions() {
        if (this.menuAvailableOptionsSerialized == null) {
            return null;
        }
        return this.menuAvailableOptionsSerialized.split("\\s*,\\s*");
    }

    @Override
    public void setMenuAvailableOptions(String[] menuAvailableOptions) {
        if (menuAvailableOptions == null) {
            this.menuAvailableOptionsSerialized = null;
        } else {
            this.menuAvailableOptionsSerialized = StringUtils.join(menuAvailableOptions, ",");
        }
    }

    @Override
    @Transient
    public String[] getHeader() {
        if (this.headerSerialized == null) {
            return null;
        }
        return this.headerSerialized.split("\\s*,\\s*");
    }

    @Override
    public void setHeader(String[] header) {
        if (header == null) {
            this.headerSerialized = null;
        } else {
            this.headerSerialized = StringUtils.join(header, ",");
        }
    }

    @Override
    @Transient
    public LinkedDataDTO getLinkedData() {
        final List<TemplateCommentEntity> commentsArray;
        if (this.comments != null) {
             commentsArray = new ArrayList<>(this.comments);
        } else {
             commentsArray = new ArrayList<>();
        }
        final List<TemplateDocumentEntity> documentsArray;
        if (this.documents != null) {
             documentsArray = new ArrayList<>(this.documents);
        } else {
             documentsArray = new ArrayList<>();
        }
        final List<TemplateFileDataTemplate> filesArray;
        if (this.files != null) {
             filesArray = new ArrayList<>(this.files);
        } else {
             filesArray = new ArrayList<>();
        }
        return new LinkedDataDTO(
                commentsArray,
                documentsArray,
                filesArray
        );
    }

    @Override
    public void setLinkedData(LinkedDataDTO linkedData) {
        if (linkedData == null) {
            this.comments = null;
            this.documents = null;
            this.files = null;
        } else {
            this.comments = new HashSet<>(linkedData.getComments());
            this.documents = new HashSet<>(linkedData.getDocuments());
            this.files = new HashSet<>(linkedData.getFiles());
        }
    }

    @Override
    @Transient
    public TemporaryDTO getTemporary() {
        final List<TemplateDataMap> implementerArray;
        if (this.implementerList != null) {
            implementerArray = new ArrayList<>(this.implementerList);
        } else {
            implementerArray = new ArrayList<>();            
        }
        return new TemporaryDTO(
            this.temporaryStartDate,
            this.temporaryFileNamesSerialized,
            implementerArray
        );
    }

    @Override
    public void setTemporary(TemporaryDTO temporary) {
        if (temporary == null) {
            this.temporaryStartDate = null;
            this.temporaryFileNamesSerialized = null;
        } else {
            this.temporaryStartDate = temporary.getStartDate();
            // FileNames
            if (temporary.getFileNames() == null) {
                this.temporaryFileNamesSerialized = null;
            } else {
                this.temporaryFileNamesSerialized = StringUtils.join(temporary.getFileNames(), ",");
            }
            // Implementers
            if (temporary.getImplementer() == null) {
                this.implementerList = null;
            } else {
                this.implementerList = new HashSet<>();
                temporary.getImplementer().entrySet().forEach((i) -> {
                    if (i.getValue() == null) {
                        this.implementerList.add(new TemplateDataMap(0, i.getKey(), null));
                    } else {
                        this.implementerList.add(new TemplateDataMap(0, i.getKey(), i.getValue().toString()));
                    }
                });
            }
        }
    }

    @Transient
    public Map<String, TemplateVariableItem> getVariables() {
        if (this.variablesList == null) {
            return null;
        }
        Map<String, TemplateVariableItem> variables = new HashMap<>(this.variablesList.size());
        this.variablesList.forEach((templateDataMap) -> {
            variables.put(templateDataMap.getFieldName(), templateDataMap.getVariableItem());
        });
        return variables;
    }

    public void setVariables(Map<String, TemplateVariableItem> variables) {
        if (variables == null) {
            this.variablesList = null;
        } else {
            this.variablesList = new HashSet<>(variables.size());
            variables.entrySet().forEach((variable) -> {
                this.variablesList.add(new TemplateDataMapVariableItem(0, variable.getKey(), "variable-item", variable.getValue()));
            });
        }
    }

    @Transient
    public Map<String, Boolean> getIsVariableDroppable() {
        if (this.isVariableDroppableList == null) {
            return null;
        }
        Map<String, Boolean> isVariableDroppable = new HashMap<>(this.variablesList.size());
        this.isVariableDroppableList.forEach((templateDataMap) -> {
            isVariableDroppable.put(templateDataMap.getFieldName(), Boolean.TRUE.equals(templateDataMap.getFieldValue()));
        });
        return isVariableDroppable;
    }

    public void setIsVariableDroppable(Map<String, Boolean> isVariableDroppable) {
        if (isVariableDroppable == null) {
            this.isVariableDroppableList = null;
        } else {
            this.isVariableDroppableList = new HashSet<>(isVariableDroppable.size());
            isVariableDroppable.entrySet().forEach((variable) -> {
                this.isVariableDroppableList.add(new TemplateDataMapVariableDrop(0, variable.getKey(), variable.getValue()));
            });
        }
    }
    
    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_enable_delivery_setup")
    public Boolean getHiddenEnableDeliverySetUp() {
        return this.hiddenEnableDeliverySetUp;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_task_delivery_type_id")
    public Boolean getHiddenTaskDeliveryTypeId() {
        return this.hiddenTaskDeliveryTypeId;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_task_category_id")
    public Boolean getHiddenTaskCategoryId() {
        return this.hiddenTaskCategoryId;
    }

    @Override
    public void setHiddenEnableDeliverySetUp(Boolean hiddenEnableDeliverySetUp) {
        this.hiddenEnableDeliverySetUp = hiddenEnableDeliverySetUp;
    }

    @Override
    public void setHiddenTaskDeliveryTypeId(Boolean hiddenTaskDeliveryTypeId) {
        this.hiddenTaskDeliveryTypeId = hiddenTaskDeliveryTypeId;
    }

    @Override
    public void setHiddenTaskCategoryId(Boolean hiddenTaskCategoryId) {
        this.hiddenTaskCategoryId = hiddenTaskCategoryId;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_participants")
    public Boolean getHiddenParticipants() {
        return hiddenParticipants;
    }

    @Override
    public void setHiddenParticipants(Boolean hiddenParticipants) {
        this.hiddenParticipants = hiddenParticipants;
    }
    
    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_category_id")
    public Boolean getHiddenCategoryId() {
        return hiddenCategoryId;
    }

    @Override
    public void setHiddenCategoryId(Boolean hiddenCategoryId) {
        this.hiddenCategoryId = hiddenCategoryId;
    }

    @Override
    @Column(name = "disabled_finish_ver_on")
    @Type(type = "numeric_boolean")
    @JsonIgnore
    public Boolean getDisabledFinishVerificationOn() {
        return finishVerificationOn;
    }

    @Override
    public void setDisabledFinishVerificationOn(Boolean finishVerificationOn) {
        this.finishVerificationOn = finishVerificationOn;
    }

    @Override
    @Column(name = "disabled_start_ver_on")
    @Type(type = "numeric_boolean")
    @JsonIgnore
    public Boolean getDisabledStartVerificationOn() {
        return startVerificationOn;
    }

    @Override
    public void setDisabledStartVerificationOn(Boolean startVerificationOn) {
        this.startVerificationOn = startVerificationOn;
    }
    
    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "disabled_category_id")
    public Boolean getDisabledCategoryId() {
        return disabledCategoryId;
    }

    @Override
    public void setDisabledCategoryId(Boolean disabledCategoryId) {
        this.disabledCategoryId = disabledCategoryId;
    }

    @JsonIgnore
    @Override
    @Type(type = "numeric_boolean")
    @Column(name = "hidden_activity_order")
    public Boolean getHiddenActivityOrder() {
        return hiddenActivityOrder;
    }

    @Override
    public void setHiddenActivityOrder(Boolean hiddenActivityOrder) {
        this.hiddenActivityOrder = hiddenActivityOrder;
    }

    @Override
    @JsonIgnore
    @Type(type = "numeric_boolean")
    @Column(name = "disabled_activity_order")
    public Boolean getDisabledActivityOrder() {
        return disabledActivityOrder;
    }

    @Override
    public void setDisabledActivityOrder(Boolean disabledActivityOrder) {
        this.disabledActivityOrder = disabledActivityOrder;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 17 * hash + Objects.hashCode(this.color);
        hash = 17 * hash + Objects.hashCode(this.level);
        hash = 17 * hash + Objects.hashCode(this.rowId);
        hash = 17 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TemplateActivityRow other = (TemplateActivityRow) obj;
        if (!Objects.equals(this.color, other.color)) {
            return false;
        }
        if (!Objects.equals(this.rowId, other.rowId)) {
            return false;
        }
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return Objects.equals(this.level, other.level);
    }
    
     
}
