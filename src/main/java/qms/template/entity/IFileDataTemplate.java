/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public interface IFileDataTemplate {

    public String getCode();

    public String getContentSha512();

    public Long getContentSize();

    public String getContentType();

    public Long getCreatedBy();

    public Date getCreatedDate();

    public String getDescription();

    public String getExtension();

    public Long getId();

    public Long getLastModifiedBy();

    public Date getLastModifiedDate();

    public String getStage();

    public Long getThumbnailSize();
}
