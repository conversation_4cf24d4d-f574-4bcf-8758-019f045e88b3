/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import Framework.Config.DomainObject;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.BusinessUnitRef;
import bnext.reference.IAuditable;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Convert;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.Transient;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.activity.dto.ActivityDataSourceDto;
import qms.custom.dto.DynamicFieldsDTO;
import qms.framework.util.CacheConstants;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.util.CodePrefix;
import qms.util.interfaces.IPersistableDeleted;
import qms.util.interfaces.IPersistableStatus;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_activity_template")
@CodePrefix("AT")
@JsonPropertyOrder(alphabetic = true)
public class TemplateActivityTemplate extends DomainObject implements IAuditable, IPersistableDeleted, IPersistableStatus {

    private Boolean isPublic;
    private Date createdDate;
    private Date lastModifiedDate;
    private Integer activityCount;
    private Integer businessUnitCount;
    private Integer businessUnitDepartmentCount;
    private Integer deleted;
    private Integer groupCount;
    private Integer status;
    private String moduleName;
    private String code;
    private String description;
    private Long createdBy;
    private UserRef createdByUser;
    private Long lastModifiedBy;
    private Long plannerId;
    private Long parentActivityTaskId;
    private Long templateFromId;
    private Long masterActivityTemplateId;
    private Long masterPreactivityTemplateId;
    private TemplateActivityTemplate templateFrom;
    @JsonPropertyOrder(alphabetic = true)
    private Set<TemplateActivityRow> groups;
    @JsonPropertyOrder(alphabetic = true)
    private Set<TemplateVariableItem> variables;
    private Set<BusinessUnitRef> businessUnits;
    private Set<BusinessUnitDepartmentRef> businessUnitDepartments;
    private Map<Long, DynamicFieldsDTO> dynamicFieldData;
    private Map<String, ActivityDataSourceDto> typeData;
    private TimesheetDataSourceDto timesheetDs;

    public TemplateActivityTemplate() {
        this.id = -1L;
        this.deleted = 0;
        this.status = 1;
    }

    @Id
    @Column(name = "template_activity_template_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "module_name")
    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    @Override
    @Column(name = "status")
    public Integer getStatus() {
        return this.status;
    }
    
    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    @Column(name = "deleted")
    public Integer getDeleted() {
        return this.deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "last_modified_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    @LastModifiedDate
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_date", updatable = false)
    @Override
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return this.createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "created_by")
    @Override
    public Long getCreatedBy() {
        return this.createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(
        referencedColumnName = "user_id",
        name = "created_by", updatable = false, insertable = false
    )
    public UserRef getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(UserRef createdByUser) {
        this.createdByUser = createdByUser;
    }

    @Column(name = "last_modified_by")
    @Override
    public Long getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    @Column(name = "is_public")
    public Boolean getIsPublic() {
        return this.isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    @Column(name = "business_unit_count")
    public Integer getBusinessUnitCount() {
        return this.businessUnitCount;
    }

    public void setBusinessUnitCount(Integer businessUnitCount) {
        this.businessUnitCount = businessUnitCount;
    }

    @Column(name = "business_unit_department_count")
    public Integer getBusinessUnitDepartmentCount() {
        return this.businessUnitDepartmentCount;
    }

    public void setBusinessUnitDepartmentCount(Integer businessUnitDepartmentCount) {
        this.businessUnitDepartmentCount = businessUnitDepartmentCount;
    }

    @Column(name = "group_count")
    public Integer getGroupCount() {
        return this.groupCount;
    }

    public void setGroupCount(Integer groupCount) {
        this.groupCount = groupCount;
    }

    @Column(name = "activity_count")
    public Integer getActivityCount() {
        return this.activityCount;
    }

    public void setActivityCount(Integer activityCount) {
        this.activityCount = activityCount;
    }

    @Column(name = "template_from_id")
    public Long getTemplateFromId() {
        return this.templateFromId;
    }

    public void setTemplateFromId(Long templateFromId) {
        this.templateFromId = templateFromId;
    }

    @Column(name = "planner_id")
    public Long getPlannerId() {
        return plannerId;
    }

    public void setPlannerId(Long plannerId) {
        this.plannerId = plannerId;
    }

    @Column(name = "parent_activity_task_id")
    public Long getParentActivityTaskId() {
        return parentActivityTaskId;
    }

    public void setParentActivityTaskId(Long parentActivityTaskId) {
        this.parentActivityTaskId = parentActivityTaskId;
    }

    @Column(name = "master_activity_template_id")
    public Long getMasterActivityTemplateId() {
        return masterActivityTemplateId;
    }

    public void setMasterActivityTemplateId(Long masterActivityTemplateId) {
        this.masterActivityTemplateId = masterActivityTemplateId;
    }

    @Column(name = "master_preactivity_template_id")
    public Long getMasterPreactivityTemplateId() {
        return masterPreactivityTemplateId;
    }

    public void setMasterPreactivityTemplateId(Long masterPreactivityTemplateId) {
        this.masterPreactivityTemplateId = masterPreactivityTemplateId;
    }

    @JsonIgnore
    @OneToOne
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinColumn(
        name = "template_from_id", referencedColumnName = "template_activity_template_id",
        insertable = false, updatable = false
    )
    public TemplateActivityTemplate getTemplateFrom() {
        return templateFrom;
    }

    public void setTemplateFrom(TemplateActivityTemplate templateFrom) {
        this.templateFrom = templateFrom;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
        name = "template_activity_template_bu",
        joinColumns = @JoinColumn(name = "template_activity_template_id"),
        inverseJoinColumns = @JoinColumn(name = "business_unit_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<BusinessUnitRef> getBusinessUnits() {
        return businessUnits;
    }

    public void setBusinessUnits(Set<BusinessUnitRef> businessUnits) {
        this.businessUnits = businessUnits;
    }

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JoinTable(
        name = "template_activity_template_bud",
        joinColumns = @JoinColumn(name = "template_activity_template_id"),
        inverseJoinColumns = @JoinColumn(name = "business_unit_department_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<BusinessUnitDepartmentRef> getBusinessUnitDepartments() {
        return businessUnitDepartments;
    }

    public void setBusinessUnitDepartments(Set<BusinessUnitDepartmentRef> businessUnitDepartments) {
        this.businessUnitDepartments = businessUnitDepartments;
    }

    @OneToMany(mappedBy = "template", cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SUBSELECT)
    @JsonPropertyOrder(alphabetic = true)
    public Set<TemplateActivityRow> getGroups() {
        return groups;
    }

    public void setGroups(Set<TemplateActivityRow> groups) {
        this.groups = groups;
        if (this.id != null && this.id > 0) {
            return;
        }
        if (groups == null) {
            return;
        }
        this.groups.forEach((group) -> {
            group.setTemplate(this);
        });
    }

    @OneToMany(mappedBy = "template", cascade = {CascadeType.ALL}, fetch = FetchType.EAGER)
    @Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SUBSELECT)
    @JsonPropertyOrder(alphabetic = true)
    public Set<TemplateVariableItem> getVariables() {
        return variables;
    }

    public void setVariables(Set<TemplateVariableItem> variables) {
        this.variables = variables;
        if (this.id != null && this.id > 0) {
            return;
        }
        if (variables == null) {
            return;
        }
        this.variables.forEach((group) -> {
            group.setTemplate(this);
        });
    }

    @Transient
    public Map<Long, DynamicFieldsDTO> getDynamicFieldData() {
        return dynamicFieldData;
    }

    public void setDynamicFieldData(Map<Long, DynamicFieldsDTO> dynamicFieldData) {
        this.dynamicFieldData = dynamicFieldData;
    }
    
    @Transient
    public Map<String, ActivityDataSourceDto> getTypeData() {
        return typeData;
    }

    public void setTypeData(Map<String, ActivityDataSourceDto> typeData) {
        this.typeData = typeData;
    }

    @Transient
    public TimesheetDataSourceDto getTimesheetDs() {
        return timesheetDs;
    }

    public void setTimesheetDs(TimesheetDataSourceDto timesheetDs) {
        this.timesheetDs = timesheetDs;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 19 * hash + Objects.hashCode(this.id);
        hash = 19 * hash + Objects.hashCode(this.code);
        hash = 19 * hash + Objects.hashCode(this.description);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TemplateActivityTemplate other = (TemplateActivityTemplate) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.description, other.description)) {
            return false;
        }
        return Objects.equals(this.id, other.id);
    }


}
