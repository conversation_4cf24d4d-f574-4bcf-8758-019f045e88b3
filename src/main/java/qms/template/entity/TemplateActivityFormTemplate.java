/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import qms.framework.util.CacheConstants;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Objects;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_generic_entity")
@JsonPropertyOrder(alphabetic = true)
public class TemplateActivityFormTemplate extends TemplateGenericEntitySuperclass implements IAuditable, IActivityFormTemplate {

    private Long surveyId;
    private Integer deleted;
    private Integer status;
    private String version;
    private String code;
    private String masterId;
    private String requestor;

    public TemplateActivityFormTemplate() {
        this.id = -1L;
        this.deleted = 0;
        this.status = 1;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    @JsonIgnore
    public Integer getDeleted() {
        return this.deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "master_id")
    @Override
    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }


    @Column(name = "survey_id")
    @Override
    public Long getSurveyId() {
        return this.surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "version")
    @Override
    public String getVersion() {
        return this.version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
    
    @Column(name = "requestor")
    @Override
    public String getRequestor() {
        return this.requestor;
    }

    public void setRequestor(String requestor) {
        this.requestor = requestor;
    }

    @Override
    @Column(name = "generic_entity_type")
    @JsonIgnore
    public String getGenericEntityType() {
        return "IActivityFormTemplate";
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.surveyId);
        hash = 89 * hash + Objects.hashCode(this.id);
        hash = 89 * hash + Objects.hashCode(this.code);
        hash = 89 * hash + Objects.hashCode(this.masterId);
        hash = 89 * hash + Objects.hashCode(this.requestor);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TemplateActivityFormTemplate other = (TemplateActivityFormTemplate) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.masterId, other.masterId)) {
            return false;
        }
        if (!Objects.equals(this.requestor, other.requestor)) {
            return false;
        }
        if (!Objects.equals(this.surveyId, other.surveyId)) {
            return false;
        }
        return Objects.equals(this.id, other.id);
    }

}
