/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import Framework.Config.DomainObject;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

/**
 *
 * <AUTHOR>
 * @param <TYPE>
 */
@MappedSuperclass
public abstract class TemplateDataMapSuperclass<TYPE> extends DomainObject implements IAuditable  {

    protected Date createdDate;
    protected Date lastModifiedDate;
    protected Integer deleted;
    protected Long createdBy;
    protected Long lastModifiedBy;
    protected String fieldName;
    protected String fieldValue;

    public TemplateDataMapSuperclass() {
        this.id = -1L;
        this.deleted = 0;
    }

    public TemplateDataMapSuperclass(
        Integer deleted, String fieldName, String fieldValue
    ) {
        this.id = -1L;
        this.deleted = deleted;
        this.fieldName = fieldName;
        this.fieldValue = fieldValue;
    }

    @Id
    @Column(name = "template_data_map_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    @JsonIgnore
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "code")
    public String getFieldName() {
        return this.fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    @Column(name = "description")
    public abstract TYPE getFieldValue();

    public abstract void setFieldValue(TYPE fieldValue);

    @Column(name = "deleted")
    @JsonIgnore
    public Integer getDeleted() {
        return this.deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @JsonIgnore
    @Override
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_date", updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @Override
    @JsonIgnore
    @CreatedDate
    public Date getCreatedDate() {
        return this.createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "created_by")
    @Override
    @JsonIgnore
    public Long getCreatedBy() {
        return this.createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @Override
    @JsonIgnore
    public Long getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 79 * hash + Objects.hashCode(this.id);
        hash = 79 * hash + Objects.hashCode(this.fieldName);
        hash = 79 * hash + Objects.hashCode(this.fieldValue);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TemplateDataMapSuperclass<?> other = (TemplateDataMapSuperclass<?>) obj;
        if (!Objects.equals(this.fieldName, other.fieldName)) {
            return false;
        }
        if (!Objects.equals(this.fieldValue, other.fieldValue)) {
            return false;
        }
        return Objects.equals(this.id, other.id);
    }
    
    

}
