/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.template.entity;

import Framework.Config.DomainObject;
import qms.framework.util.CacheConstants;
import bnext.reference.IAuditable;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TEMPLATE_ACTIVITY, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "template_date_range")
@JsonPropertyOrder(alphabetic = true)
public class TemplateDateRange extends DomainObject implements IAuditable  {

    private Date createdDate;
    private Date lastModifiedDate;
    private Integer deleted;
    private Integer type;
    private Long createdBy;
    private Long lastModifiedBy;
    private String dateRangeSerialized;
    private String genericEntityType;

    public TemplateDateRange() {
        this.id = -1L;
        this.deleted = 0;
    }

    @Id
    @Column(name = "template_date_range_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    @JsonIgnore
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "deleted")
    @JsonIgnore
    public Integer getDeleted() {
        return this.deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Override
    @JsonIgnore
    public Date getLastModifiedDate() {
        return this.lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_date", updatable = false)
    @Override
    @CreatedDate
    @JsonIgnore
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return this.createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @JsonIgnore
    @Column(name = "created_by")
    public Long getCreatedBy() {
        return this.createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @JsonIgnore
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "date_range_type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "date_range")
    @JsonIgnore
    public String getDateRangeSerialized() {
        return dateRangeSerialized;
    }

    public void setDateRangeSerialized(String dateRangeSerialized) {
        this.dateRangeSerialized = dateRangeSerialized;
    }

    @Column(name = "generic_entity_type")
    @JsonIgnore
    public String getGenericEntityType() {
        return genericEntityType;
    }

    public void setGenericEntityType(String genericEntityType) {
        this.genericEntityType = genericEntityType;
    }

    @Transient
    public String[] getDateRange() {
        if (this.dateRangeSerialized == null) {
            return null;
        }
        return this.dateRangeSerialized.split("\\s*,\\s*");
    }

    public void setDateRange(String[] dateRange) {
        if (dateRange == null) {
            this.dateRangeSerialized = null;
        } else {
            this.dateRangeSerialized = StringUtils.join(dateRange, ",");
        }
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 37 * hash + Objects.hashCode(this.id);
        hash = 37 * hash + Objects.hashCode(this.type);
        hash = 37 * hash + Objects.hashCode(this.dateRangeSerialized);
        hash = 37 * hash + Objects.hashCode(this.genericEntityType);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final TemplateDateRange other = (TemplateDateRange) obj;
        if (!Objects.equals(this.dateRangeSerialized, other.dateRangeSerialized)) {
            return false;
        }
        if (!Objects.equals(this.genericEntityType, other.genericEntityType)) {
            return false;
        }
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return Objects.equals(this.type, other.type);
    }
    
    

}
