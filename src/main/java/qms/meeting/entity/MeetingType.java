package qms.meeting.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.custom.core.DynamicFields;
import qms.custom.entity.DynamicField;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.util.CodePrefix;
import qms.util.interfaces.EntityType;
import qms.util.interfaces.IUserDefinedCodeConfig;


/**
 *
 * <AUTHOR> Germán Lares Lares @ Bnext
 * @since September 20, 2016
 */

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "meeting_type")
@CodePrefix("MTYP-")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@DynamicFields(
    compositeEntityClass = MeetingTypeDynamicField.class,
    entityMappedBy = "meetingTypeId",
    createViewPreffix = "",
    dynamicFieldMappedBy = "dynamicFieldId"
)
public class MeetingType extends StandardEntity<MeetingType> implements IAuditableEntity, IUserDefinedCodeConfig, Serializable, EntityType {

    private static final long serialVersionUID = 1L;
    
    public static enum STATUS implements IStatusEnum {
        ACTIVE(1, IStatusEnum.COLOR_GREEN),
        INACTIVE(0, IStatusEnum.COLOR_GRAY)
        ;
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public String toString() { 
            return this.value.toString();
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return ACTIVE;
        }
    }
    
    private String code;
    private Integer status = 1;
    private Integer deleted = 0;
    private String description;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private String registryCode;
    private Integer registryCodeSequenceLength;
    private Integer daysToClose;
    
    
    private String documentCode;
    private String documentMasterId;
    private String documentDescription;
    private Long documentId;
    private Long minuteId;
    
    
    //Transient
    private List<DynamicField> dynamicFields;
    
    public MeetingType() {
    }

    public MeetingType(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "meeting_type_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date dt) {
        this.createdDate = dt;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date dt) {
        this.lastModifiedDate = dt;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }   

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @LocalizedField
    @Column(name = "description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }


    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof MeetingType)) {
            return false;
        }
        MeetingType other = (MeetingType) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.MeetingType[ id=" + id + " ]";
    }

    
    @Column(name = "registry_code")
    public String getRegistryCode() {
        return registryCode;
    }

    public void setRegistryCode(String registryCode) {
        this.registryCode = registryCode;
    }

    @Column(name = "registry_code_sequence_length")
    public Integer getRegistryCodeSequenceLength() {
        return registryCodeSequenceLength;
    }

    public void setRegistryCodeSequenceLength(Integer registryCodeSequenceLength) {
        this.registryCodeSequenceLength = registryCodeSequenceLength;
    }

    /**
     * Se utilizan solo para guardar datos en el Service (CRUD), para traer
     * datos utilizar el entity individual "DocumentTypeDynamicField"
     * 
     * @return 
     */
    @Transient
    public List<DynamicField> getDynamicFields() {
        return dynamicFields;
    }

    public void setDynamicFields(List<DynamicField> dynamicFields) {
        this.dynamicFields = dynamicFields;
    }
    
    
    @Override
    public String configCode() {
        return registryCode;
    }
    
    
    @Column(name = "days_to_close")
    public Integer getDaysToClose() {
        return daysToClose;
    }

    public void setDaysToClose(Integer daysToClose) {
        this.daysToClose = daysToClose;
    }

    @Column(name = "minute_id")
    public Long getMinuteId() {
        return minuteId;
    }

    public void setMinuteId(Long minuteId) {
        this.minuteId = minuteId;
    }
    
    
    @Column(name = "document_description")
    public String getDocumentDescription() {
        return documentDescription;
    }

    public void setDocumentDescription(String documentDescription) {
        this.documentDescription = documentDescription;
    }
    
    @Column(name = "document_code")
    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Column(name = "document_master_id")
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }
    
    
}
