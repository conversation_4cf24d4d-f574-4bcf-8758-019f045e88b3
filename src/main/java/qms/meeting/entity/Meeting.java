package qms.meeting.entity;

import DPMS.Mapping.IAuditableEntity;
import DPMS.Mapping.IOwnerEntity;
import DPMS.Mapping.Periodicity;
import Framework.Config.StandardEntity;
import ape.pending.core.RootSoftAPE;
import ape.pending.core.StrongBaseTypedAPE;
import bnext.reference.MeetingRef;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.custom.core.DynamicSearch;
import qms.custom.core.EntityDynamicFields;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.meeting.dto.IMeeting;
import qms.meeting.logic.MeetingCodeValidator;
import qms.util.annotations.UserDefinedCode;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "meeting")
@EnableJpaAuditing
@DynamicSearch
@EntityListeners(AuditingEntityListener.class)
@RootSoftAPE(
        mappedBy = "id.meetingId",
        mappedByClass = MeetingActivity.class
)
@UserDefinedCode(
        mappedBy = "type",
        mappableAttributes = {
            "host", "type"
        },
        codeValidator = MeetingCodeValidator.class
)
public class Meeting extends StandardEntity<Meeting> implements IAuditableEntity, IOwnerEntity, Serializable, EntityDynamicFields, StrongBaseTypedAPE<MeetingType>, IMeeting {

    private static final long serialVersionUID = 1L;
    private String dynamicTableName;
    private DynamicFieldInsertDTO dynamicFieldInsertDTO;

    public static enum STATUS implements IStatusEnum {
        CANCELLED(0, IStatusEnum.COLOR_BLACK),
        REPORTED(1, IStatusEnum.COLOR_RED),
        READY(2, IStatusEnum.COLOR_ORANGE),
        IN_PROCESS(3, IStatusEnum.COLOR_GREEN),
        CONCLUDED(4, IStatusEnum.COLOR_CYAN),
        CLOSED(5, IStatusEnum.COLOR_GRAY),
        RECURRING(6, IStatusEnum.COLOR_LIGHT_GREEN);

        private final Integer value;
        private final String gridCube;

        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }

        @Override
        public Integer getValue() {
            return this.value;
        }

        @Override
        public String getGridCube() {
            return this.gridCube;
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return REPORTED;
        }

        public static STATUS getStatus(Integer status) {
            for (STATUS s : values()) {
                if (Objects.equals(s.value, status)) {
                    return s;
                }
            }
            return null;
        }
    }
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private MeetingType type;
    private Long typeId;
    private UserRef host;
    private String body;
    private String bodyPlain;
    private String location;
    private Date startOn;
    private Date finishOn;
    private Periodicity periodicity;
    private Integer classified;
    private String guest;
    private Long outstandingSurveyId;
    private Integer recurrent = IPeriodicEntity.RECURRENT.NO.getValue();
    private Long recurrenceId;
    private MeetingRef recurrence;
    private Integer updateSequence = 0;
    private Integer lastHashCode = 0;
    private Integer currentHashCode = 0;

    private Date nextDate;

    public Meeting() {
    }

    public Meeting(Long id) {
        this.id = id;
    }

    @Id
    @Column(name = "meeting_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "meeting_type_id")
    public MeetingType getType() {
        return type;
    }

    public void setType(MeetingType type) {
        this.type = type;
    }

    @Column(name = "meeting_type_id", updatable = false, insertable = false)
    @Override
    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "host", referencedColumnName = "user_id")
    @Override
    public UserRef getHost() {
        return host;
    }

    public void setHost(UserRef host) {
        this.host = host;
    }

    @Column(name = "body")
    @Override
    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    @Column(name = "body_plain")
    public String getBodyPlain() {
        return bodyPlain;
    }

    public void setBodyPlain(String bodyPlain) {
        this.bodyPlain = bodyPlain;
    }
    
    
    
    @Column(name = "start_on")
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getStartOn() {
        return startOn;
    }

    public void setStartOn(Date startOn) {
        this.startOn = startOn;
    }

    @Column(name = "finish_on")
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getFinishOn() {
        return finishOn;
    }

    public void setFinishOn(Date finishOn) {
        this.finishOn = finishOn;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "periodicity_id")
    public Periodicity getPeriodicity() {
        return periodicity;
    }

    public void setPeriodicity(Periodicity periodicity) {
        this.periodicity = periodicity;
    }

    @Column(name = "private")
    public Integer getClassified() {
        return classified;
    }

    public void setClassified(Integer classified) {
        this.classified = classified;
    }

    @Column(name = "location")
    @Override
    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    @Column(name = "guests")
    public String getGuest() {
        return guest;
    }

    public void setGuest(String guest) {
        this.guest = guest;
    }

    @Column(name = "outstanding_survey_id")
    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }

    @Column(name = "recurrent")
    public Integer getRecurrent() {
        return recurrent;
    }

    public void setRecurrent(Integer recurrent) {
        this.recurrent = recurrent;
    }

    @Column(name = "recurrence_id")
    public Long getRecurrenceId() {
        return recurrenceId;
    }

    public void setRecurrenceId(Long recurrenceId) {
        this.recurrenceId = recurrenceId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "recurrence_id", referencedColumnName = "meeting_id", insertable = false, updatable = false)
    public MeetingRef getRecurrence() {
        return recurrence;
    }

    public void setRecurrence(MeetingRef recurrence) {
        this.recurrence = recurrence;
    }

    @Column(name = "next_date", updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getNextDate() {
        return nextDate;
    }

    public void setNextDate(Date nextDate) {
        this.nextDate = nextDate;
    }
    
    @Override
    @Column(name = "update_sequence")
    public Integer getUpdateSequence() {
        return updateSequence;
    }

    public void setUpdateSequence(Integer updateSequence) {
        this.updateSequence = updateSequence;
    }
    
    @Override
    @Column(name = "last_hash_code")
    public Integer getLastHashCode() {
        return lastHashCode;
    }

    public void setLastHashCode(Integer lastHashCode) {
        this.lastHashCode = lastHashCode;
    }
    
    @Override
    @Column(name = "current_hash_code")
    public Integer getCurrentHashCode() {
        return currentHashCode;
    }

    public void setCurrentHashCode(Integer currentHashCode) {
        this.currentHashCode = currentHashCode;
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 11 * hash + Objects.hashCode(this.id);
        hash = 11 * hash + Objects.hashCode(this.code);
        hash = 11 * hash + Objects.hashCode(this.description);
        hash = 11 * hash + Objects.hashCode(this.location);
        hash = 11 * hash + Objects.hashCode(this.startOn);
        hash = 11 * hash + Objects.hashCode(this.finishOn);
        hash = 11 * hash + Objects.hashCode(this.body);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
    }
        final Meeting other = (Meeting) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.description, other.description)) {
            return false;
        }
        if (!Objects.equals(this.location, other.location)) {
            return false;
        }
        if (!Objects.equals(this.body, other.body)) {
            return false;
        }
        if (!Objects.equals(this.startOn, other.startOn)) {
            return false;
        }
        return Objects.equals(this.finishOn, other.finishOn);
    }

    @Override
    public String toString() {
        return "Meeting{"
                + "code=" + code
                + ", type=" + type
                + ", host=" + host
                + ", location=" + location
                + ", startOn=" + startOn
                + ", recurrent=" + recurrent
                + '}';
    }

    @Override
    @Column(name = "dynamic_table_name")
    public String getDynamicTableName() {
        return dynamicTableName;
    }

    @Override
    public void setDynamicTableName(String dynamicTableName) {
        this.dynamicTableName = dynamicTableName;
    }

    @Override
    public void setDynamicFieldInsertDTO(DynamicFieldInsertDTO dynamicFieldInsertDTO) {
        this.dynamicFieldInsertDTO = dynamicFieldInsertDTO;
    }

    @Override
    @Transient
    public DynamicFieldInsertDTO getDynamicFieldInsertDTO() {
        return dynamicFieldInsertDTO;
    }
}
