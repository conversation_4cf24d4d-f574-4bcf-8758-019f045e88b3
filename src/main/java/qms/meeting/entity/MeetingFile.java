package qms.meeting.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR> @ Bnext
 * @since September 22, 2016
 *
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "meeting_files")
public class MeetingFile extends CompositeStandardEntity<MeetingFilePK> 
        implements Serializable, ILinkedComposityGrid<MeetingFilePK> {

    private static final long serialVersionUID = 1L;
    private MeetingFilePK id;

    public MeetingFile() {
    }

    public MeetingFile(MeetingFilePK id) {
        this.id = id;
    }

    public MeetingFile(Long meetingId, Long fileId) {
        this.id = new MeetingFilePK(meetingId, fileId);
    }

    @Override
    public MeetingFilePK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public MeetingFilePK getId() {
        return id;
    }

    @Override
    public void setId(MeetingFilePK id) {
        this.id = id;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MeetingFile other = (MeetingFile) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "MeetingFile{" + "id=" + id + '}';
    }
 
}
