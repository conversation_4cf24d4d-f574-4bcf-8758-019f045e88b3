package qms.meeting.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class MeetingFilePK implements Serializable {

    private Long meetingId;
    private Long fileId;

    public MeetingFilePK(Long meetingId, Long fileId) {
        this.meetingId = meetingId;
        this.fileId = fileId;
    }

    public MeetingFilePK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "meeting_id")
    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.meetingId);
        hash = 97 * hash + Objects.hashCode(this.fileId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MeetingFilePK other = (MeetingFilePK) obj;
        if (!Objects.equals(this.meetingId, other.meetingId)) {
            return false;
        }
        if (!Objects.equals(this.fileId, other.fileId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "MeetingFilePK{" + "meetingId=" + meetingId + ", fileId=" + fileId + '}';
    }

}
