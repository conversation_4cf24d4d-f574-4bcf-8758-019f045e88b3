package qms.meeting.logic;

import Framework.DAO.IUntypedDAO;
import ape.pending.core.PendingHelper;
import bnext.reference.UserRef;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import qms.escalation.dto.IEscalableDTO;
import qms.meeting.entity.Meeting;
import qms.meeting.entity.MeetingParticipant;
import qms.meeting.entity.MeetingType;
import qms.meeting.mail.MeetingDTO;

/**
 *
 * <AUTHOR> @ Bnext
 * @since October 19, 2016
 */
public class MeetingHelper extends PendingHelper {
    
    
    private final String GET_PARTICIPANTS = ""
                + " SELECT distinct usr:attribute "
                + " FROM " + MeetingParticipant.class.getCanonicalName() + " part "
                + " CROSS JOIN " + UserRef.class.getCanonicalName() + " usr "
                + " WHERE part.id.meetingId= :meetingId "
                + " AND usr.id = part.id.participantId";

    
    public MeetingHelper(IUntypedDAO dao) {
        super(dao);
    }

    public Set<UserRef> getParticipants(Long meetingId) {
        String query = GET_PARTICIPANTS.replace(":attribute", "");
        return  new HashSet<>(dao.HQL_findByQuery(query, "meetingId", meetingId));
    }
    
    public String getTypeDescription(Long typeId) {
        return dao.HQL_findSimpleString("SELECT c.description FROM " + MeetingType.class.getCanonicalName() + " c WHERE c.id = " + typeId);
    }

    public List<String> getParticipantsName(Long meetingId) {
        String query = GET_PARTICIPANTS.replace(":attribute", ".description");
        return dao.HQL_findByQuery(query, "meetingId", meetingId);
    }
    
    public MeetingDTO getMeetingDTO(IEscalableDTO record) {
        String hql = ""
                + " SELECT new " + MeetingDTO.class.getCanonicalName() + "("
                    + " c.id as id,"
                    + " c.code as code,"
                    + " c.description as description,"
                    + " t.description as type,"
                    + " c.status as statusValue,"
                    + " c.location as location,"
                    + " c.startOn as startOn,"
                    + " c.finishOn as finishOn,"
                    + " h as host,"
                    + " c.body as body,"
                    + " c.bodyPlain as bodyPlain,"
                    + " c.updateSequence as updateSequence,"
                    + " c.lastHashCode as lastHashCode,"
                    + " c.currentHashCode as currentHashCode"
                + " )"
                + " FROM " + Meeting.class.getCanonicalName()+ " c"
                + " INNER JOIN c.host as h"
                + " INNER JOIN c.type as t"
                + " WHERE c.id = :id";
        return (MeetingDTO) dao.HQL_findSimpleObject(hql, "id", record.getEntityId());
    }

}
