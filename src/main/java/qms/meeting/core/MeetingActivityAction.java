package qms.meeting.core;

import Framework.Action.AuditableEntityAction;

/**
 *
 * <AUTHOR>
 */
public class MeetingActivityAction extends AuditableEntityAction {
    
    /**
     * @return the activityCreator
     */
    public Boolean getActivityCreator() {
        return false;
    }

    /**
     * @return the activityViewer
     */
    public Boolean getActivityViewer() {
        return false;
    }
    
    /**
     * @return the activityManager
     */
    public Boolean getActivityManager() {
        return false;
    }

    public String getServiceStore() {
        return "../DPMS/Meeting.Activity.action";
    }
    
    public String getActivityGridPath() {
        return "bnext/administrator/meeting/meeting-activity-grid";
    }
}