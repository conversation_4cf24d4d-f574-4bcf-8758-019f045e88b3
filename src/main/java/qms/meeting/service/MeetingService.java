package qms.meeting.service;

import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.DAOInterface.IUserRefDAO;
import DPMS.Mapping.FilesLite;
import DPMS.Mapping.Periodicity;
import DPMS.Mapping.UserPosition;
import Framework.Config.SortedPagedFilter;
import Framework.Config.StandardEntity;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.IPendingCountByType;
import ape.pending.core.IPendingHandler;
import ape.pending.core.IPendingQueries;
import ape.pending.core.PendingHandlerService;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.activity.entity.Activity;
import qms.activity.pending.imp.ToComplete;
import qms.activity.pending.imp.ToVerify;
import qms.activity.pending.imp.ToVerifyDelayed;
import qms.activity.pending.imp.ToVerifyNotApply;
import qms.framework.entity.Comment;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.framework.util.CacheRegion;
import qms.meeting.DAOInterface.IMeetingDAO;
import qms.meeting.dto.MeetingLinkedDTO;
import qms.meeting.entity.Meeting;
import qms.meeting.entity.MeetingActivity;
import qms.meeting.entity.MeetingComment;
import qms.meeting.entity.MeetingFile;
import qms.meeting.entity.MeetingParticipant;
import qms.meeting.pending.imp.ToAssist;
import qms.util.EntityCommon;
import qms.util.LinkedGridConfig;
import qms.util.QMSException;
import qms.util.interfaces.ILinkedGrid;

/**
 *
 * <AUTHOR> Germán Lares Lares @ Bnext
 * @since September 22,2016
 */
public class MeetingService extends PendingHandlerService<Meeting> implements ILinkedGrid, IPendingHandler, IPendingCountByType {

    private static final long serialVersionUID = 1L;
    private static final List<Meeting.STATUS> CURRENT_EVENT_STATUS = Arrays.asList(new Meeting.STATUS[]{
        Meeting.STATUS.REPORTED,
        Meeting.STATUS.READY,
        Meeting.STATUS.IN_PROCESS
    });
    
    private static final String SIMPLE_ROWS = ""
            + " SELECT new map("
                + " c.id as id,"
                + " c.status as status,"
                + " c.code as code,"
                + " c.description as description,"
                + " c.outstandingSurveyId as outstandingSurveyId,"
                + " h.description as host,"
                + " t.description as type,"
                + " c.startOn as startOn,"
                + " c.finishOn as finishOn,"
                + " c.createdDate as createdDate,"
            + " c.lastModifiedDate as lastModifiedDate,"
            + " c.location as location,"
            + " c.classified as classified,"
                + " r.code as codeRecurrence"
            + " ) "
            + " FROM " + Meeting.class.getCanonicalName() + " c"
            + " LEFT JOIN c.recurrence r"
            + " LEFT JOIN c.host h"
            + " LEFT JOIN c.type t";
    
    private static final String RECURRENCE_ROWS = ""
            + " SELECT new map("
                + " c.id as id,"
                + " c.status as status,"
                + " c.code as code,"
                + " c.description as description,"
                + " h.description as host,"
                + " t.description as type,"
                + " c.startOn as startOn,"
                + " c.finishOn as finishOn,"
                + " c.createdDate as createdDate,"
                + " c.lastModifiedDate as lastModifiedDate,"
            + " p as periodicity,"
                + " c.nextDate as nextDate,"
                + " c.location as location,"
                + " c.classified as classified"
            + " )"
            + " FROM " + Meeting.class.getCanonicalName() + " c"
            + " LEFT JOIN c.periodicity p"
            + " LEFT JOIN c.host h"
            + " LEFT JOIN c.type t";
    

    private void addClassifiedCondition(StringBuilder condition) {
        if (isAdmin()  || getLoggedUserServices().contains(ProfileServices.REUNION_ENCARGADO)) {
            return;
        }
        final Long loggedUserId = getLoggedUserId();
        condition
            .append(" AND (")
                .append(" c.classified = 0 OR (")
                    .append(" c.host.id = ").append(loggedUserId)
                    .append(" OR c.createdBy = ").append(loggedUserId)
                    .append(" OR EXISTS (")
                        .append(" SELECT 1")
                        .append(" FROM ").append(MeetingParticipant.class.getCanonicalName()).append(" part")
                        .append(" WHERE part.id.meetingId = c.id")
                        .append(" AND part.id.participantId = ").append(loggedUserId)
                    .append(" )")
                .append(" )")
            .append(" )");
    }
    
    @Override
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        switch (linkedGridId) {
            case "meeting_participants":      //meeting.jsp: participantes de la reunión
                String activeStatusCondition = "(c.deleted is null OR c.deleted = 0) AND c.status = " + StandardEntity.STATUS.ACTIVE.getValue()
                        + " AND EXISTS (SELECT 1 FROM " + UserPosition.class.getCanonicalName() + "up WHERE up.userId = c.id)"; 
                return new LinkedGridConfig(
                        activeStatusCondition,
                        IUserRefDAO.class,
                        new String[]{"code", "description"},
                        groundId,
                        MeetingParticipant.class
                );
            case "meeting_comments":      //meeting.jsp: comentarios de la reunión
                return new LinkedGridConfig(
                        Comment.STATUS.ACTIVE,
                        IUntypedDAO.class,
                        Comment.class,
                        new String[]{
                            "description", "createdDate", "author.description"
                        },
                        groundId,
                        MeetingComment.class
                );
        }
        return super.getLinkedGridConfig(linkedGridId, groundId);
    }

    /**
     * Metodo para reportar una reunión
     *
     * @param meeting Reunion que se va a reportar
     * @param linked Dto con items a relacionar a la reunión
     * @return GenericSaveHandle con los datos de la operación
     */
    @SMDMethod
    public GenericSaveHandle reportMeeting(Meeting meeting, MeetingLinkedDTO linked) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        IPeriodicityDAO periodicityDAO = getBean(IPeriodicityDAO.class);
        Periodicity p = meeting.getPeriodicity();
        p.setId(-1L);
        periodicityDAO.makePersistent(p);
        if (meeting.getId() > 0) {
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("invalid_call");
            return gsh;
        }
        meeting = dao.reportMeeting(meeting, linked, getLoggedUserDto());
        return evaluateResult(meeting, dao);
    }

    @SMDMethod
    public GenericSaveHandle readyMeeting(Meeting meeting, MeetingLinkedDTO linked) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        meeting = dao.readyMeeting(meeting, linked, getLoggedUserDto());
        return evaluateResult(meeting, dao);
    }

    @SMDMethod
    public GenericSaveHandle inProcessMeeting(Meeting meeting, MeetingLinkedDTO linked) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        meeting = dao.inProcessMeeting(meeting, linked, getLoggedUserDto());
        return evaluateResult(meeting, dao);
    }

    @SMDMethod
    public GenericSaveHandle concludeMeeting(Meeting meeting, MeetingLinkedDTO linked) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        meeting = dao.concludeMeeting(meeting, linked, getLoggedUserDto());
        return evaluateResult(meeting, dao);
    }

    @SMDMethod
    public GenericSaveHandle closeMeeting(Meeting meeting, MeetingLinkedDTO linked) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        meeting = dao.closeMeeting(meeting, linked, getLoggedUserDto());
        return evaluateResult(meeting, dao);
    }

    @SMDMethod
    public GenericSaveHandle cancelMeeting(Long meetingId, Long outstandingSurveyId, String commentValue) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        Meeting meeting = dao.cancelMeeting(meetingId, outstandingSurveyId, commentValue, getLoggedUserDto());
        return evaluateResult(meeting, dao);
    }

    @SMDMethod
    public GenericSaveHandle deleteMeetingRecurrence(Long meetingId) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        GenericSaveHandle gsh = new GenericSaveHandle();
        dao.deleteMeetingRecurrence(meetingId, getLoggedUserDto());
        gsh.setOperationEstatus(1);
        return gsh;
    }

    @SMDMethod
    public GenericSaveHandle updateMeeting(Meeting meeting, MeetingLinkedDTO linked) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        final Meeting.STATUS status = Meeting.STATUS.getStatus(meeting.getStatus());
        if (CURRENT_EVENT_STATUS.contains(status)) {
            meeting = dao.updateCurrentMeeting(meeting, linked, getLoggedUserDto());
        } else {
            meeting = dao.updatePastMeeting(meeting, linked, getLoggedUserDto());
        }
        return evaluateResult(meeting, dao);
    }

    @SMDMethod
    public GenericSaveHandle updateMeetingRecurrence(Meeting meeting, MeetingLinkedDTO linked) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        meeting = dao.updateMeetingRecurrence(meeting, linked, getLoggedUserDto());
        return evaluateResult(meeting, dao);
    }

       /**
     * Metodo para guardar commentarios
     *
     * @param description Comentario que se va a guardar
     * @param meeting Reunión
     * @return GenericSaveHandle con los datos de la operación
     */
    @SMDMethod
    public GenericSaveHandle saveComment(String description, Meeting meeting) throws QMSException {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        Comment comment = new Comment(-1L);
            comment.setCode(EntityCommon.getNextCode(comment));
        comment.setDescription(description);
        comment.setAuthor(getLoggedUserDto().getUser());
        comment = dao.saveComment(comment, meeting, getLoggedUserDto());
        GenericSaveHandle result = evaluateResult(comment, dao);
        Map jsonData = new HashMap();
        jsonData.put("entity", comment);

        result.setJsonEntityData(jsonData);
        return evaluateResult(comment, dao);
    }

    @SMDMethod
    public GridInfo<FilesLite> getDocuments(SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT meetingFile.fileId "
                + "FROM " + MeetingFile.class.getCanonicalName() + " s "
                + "JOIN s.id meetingFile "
                + "WHERE meetingFile.meetingId = " + firstParamCurrentEntityId()
                + ")");
        return getUntypedDAO().getRows(FilesLite.class, filter);
    }

    @SMDMethod
    public GridInfo getActivities(SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
                + "entity.id IN ("
                    + "SELECT meetingActivity.activityId "
                    + "FROM " + MeetingActivity.class.getCanonicalName() + " s "
                    + "JOIN s.id meetingActivity "
                    + "WHERE meetingActivity.meetingId = " + firstParamCurrentEntityId()
                + ")");
        return getUntypedDAO().getLightRows(filter, Activity.class, true, CacheRegion.ACTIVITY, 0);
    }

    @Override
    public Integer countTypeByUser(Long loggedUserId, APE pendingType, IUntypedDAO dao) {
        setLoggedUserId(loggedUserId);
        switch (pendingType) {
            case MEETING_TO_ASSIST:
                return new ToAssist(dao).getCountByUser(loggedUserId);
            case MEETING_ACTIVITY_TO_COMPLETE:
                return new ToComplete(Module.MEETING, dao).getCountByUser(loggedUserId);
            case MEETING_ACTIVITY_TO_VERIFY:
                return new ToVerify(Module.MEETING, dao).getCountByUser(loggedUserId);
            case MEETING_ACTIVITY_TO_VERIFY_DELAYED:
                return new ToVerifyDelayed(Module.MEETING, dao).getCountByUser(loggedUserId);
            case MEETING_ACTIVITY_TO_VERIFY_NOT_APPLY:
                return new ToVerifyNotApply(Module.MEETING, dao).getCountByUser(loggedUserId);
        }
        return 0;
    }
    
    @Override
    @SMDMethod
    public Map<String, Integer> getPendingMap() {
        IUntypedDAO dao = getUntypedDAO();
        Map pendings = getPendingMap(this, Module.MEETING, dao);
        return pendings;
    }
    
    @SMDMethod
    public GridInfo<Meeting> getRowsToAssist(SortedPagedFilter filter) {
        IMeetingDAO dao = getBean(IMeetingDAO.class);
        IPendingQueries participant = new ToAssist(dao);
        filter.getCriteria().put("<condition>", participant.filterRecordsByUser(getLoggedUserId(), "c"));
        return super.getRows(filter, dao);
    }
  
    @SMDMethod
    @Override
    public GridInfo getRows(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        StringBuilder condition = new StringBuilder(""
                + "  c.recurrent = " + IPeriodicEntity.RECURRENT.NO);
        addClassifiedCondition(condition);
        filter.getCriteria().put("<condition>", condition.toString());
        return dao.HQL_getRowsByQuery(SIMPLE_ROWS, filter);
        
    }
    
    @SMDMethod
    public String getMeetingCodePreview(Meeting r) throws QMSException {
        return EntityCommon.getNextCodePreview(r);
    }
    
    @SMDMethod
    public String getMeetingCode(Meeting r) throws QMSException {
        r.setCode(null);
        EntityCommon.setNextCode(r);
        return r.getCode();
    }
    
    @SMDMethod
    public GridInfo getReurrenceRows(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        filter.getCriteria().put("<condition>", ""
            + "  c.recurrenceId = " + Long.parseLong(firstParamCurrentEntityId())
            + "  AND c.recurrent = " + IPeriodicEntity.RECURRENT.NO);
        return dao.HQL_getRowsByQuery(SIMPLE_ROWS, filter);
    }
    
    @SMDMethod
    public GridInfo getRecurrentRows(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        StringBuilder condition = new StringBuilder(""
            + "  c.recurrent = " + IPeriodicEntity.RECURRENT.YES
            + "  AND c.deleted = 0 "
            + "  AND p.vchtipoperiodicidad != 'never'");
        addClassifiedCondition(condition);
        filter.getCriteria().put("<condition>", condition.toString());
        return dao.HQL_getRowsByQuery(RECURRENCE_ROWS, filter);
        
    }
    
    @SMDMethod
    public GridInfo getRecurrentRecycleBinRows(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        filter.getCriteria().put("<condition>", ""
            + "  c.recurrent = " + IPeriodicEntity.RECURRENT.YES
            + "  AND c.deleted = 1 ");
        return dao.HQL_getRowsByQuery(RECURRENCE_ROWS, filter);
    }
    
    
}
