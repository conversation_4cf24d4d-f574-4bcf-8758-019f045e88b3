package qms.meeting.mail;

import bnext.reference.UserRef;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import qms.meeting.dto.IMeeting;

/**
 *
 * <AUTHOR>
 */
public class MeetingDTO implements IMeeting {

    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String code = "";
    private String description = "";
    private Integer status;
    private String statusLabel;
    private String type;
    private String location;
    private Date startOn;
    private Date finishOn;
    private String participants;
    private UserRef host;
    private String body;
    private String bodyPlain;
    private Integer updateSequence = 0;
    private Integer lastHashCode = 0;
    private Integer currentHashCode = 0;

    public MeetingDTO(Long id, String code, String description, String type, Integer status, String location,
            Date startOn, Date finishOn, UserRef host, String body, String bodyPlain, Integer updateSequence,
            Integer lastHashCode, Integer currentHashCode) {
        this.id = id;
        this.code = code;
        this.description = description;
        this.type = type;
        this.status = status;
        this.location = location;
        this.startOn = startOn;
        this.finishOn = finishOn;
        this.host = host;
        this.body = body;
        this.bodyPlain = bodyPlain;
        this.updateSequence = updateSequence;
        this.lastHashCode = lastHashCode;
        this.currentHashCode = currentHashCode;
    }

    public MeetingDTO() {
    }
    
    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }
    
    @Override
    public String getBodyPlain() {
        return bodyPlain;
    }

    public void setBodyPlain(String bodyPlain) {
        this.bodyPlain = bodyPlain;
    }
    
    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }
    
    public String getParticipants() {
        return participants;
    }

    public void setParticipants(String participants) {
        this.participants = participants;
    }

    @Override
    public UserRef getHost() {
        return host;
    }

    public void setHost(UserRef host) {
        this.host = host;
    }

    @Override
    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getStartOn() {
        return startOn;
    }

    public void setStartOn(Date startOn) {
        this.startOn = startOn;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getFinishOn() {
        return finishOn;
    }

    public void setFinishOn(Date finishOn) {
        this.finishOn = finishOn;
    }

    @Override
    public Integer getUpdateSequence() {
        return updateSequence;
    }

    public void setUpdateSequence(Integer updateSequence) {
        this.updateSequence = updateSequence;
    }

    public String getStatusLabel() {
        return statusLabel;
    }

    public void setStatusLabel(String statusLabel) {
        this.statusLabel = statusLabel;
    }
    
    @Override
    @Column(name = "last_hash_code")
    public Integer getLastHashCode() {
        return lastHashCode;
    }

    public void setLastHashCode(Integer lastHashCode) {
        this.lastHashCode = lastHashCode;
    }
    
    @Override
    @Column(name = "current_hash_code")
    public Integer getCurrentHashCode() {
        return currentHashCode;
    }

    public void setCurrentHashCode(Integer currentHashCode) {
        this.currentHashCode = currentHashCode;
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.code);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final MeetingDTO other = (MeetingDTO) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        return Objects.equals(this.code, other.code);
    }

    @Override
    public String toString() {
        return "MeetingDTO{"
                + "code=" + code
                + ", type=" + type
                + ", host=" + host
                + ", location=" + location
                + ", startOn=" + startOn
                + '}';
    } 
    
}
