package qms.meeting.dao;

import DPMS.DAOInterface.IOutstandingSurveysDAO;
import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.Mapping.Request;
import Framework.Config.Language;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.framework.entity.Comment;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.meeting.DAOInterface.IMeetingDAO;
import qms.meeting.dto.MeetingLinkedDTO;
import qms.meeting.entity.Meeting;
import qms.meeting.entity.MeetingComment;
import qms.meeting.entity.MeetingFile;
import qms.meeting.entity.MeetingParticipant;
import qms.meeting.listeners.OnCancelledMeeting;
import qms.meeting.listeners.OnConcludedMeeting;
import qms.meeting.listeners.OnMeetingCommentSaved;
import qms.meeting.listeners.OnReportedMeeting;
import qms.meeting.listeners.OnUpdatedCurrentMeeting;
import qms.meeting.listeners.OnUpdatedPastMeeting;
import qms.util.EntityCommon;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Germán Lares Lares
 */
@Lazy
@Repository(value = "MeetingDAO")
@Scope(value = "singleton")
@Language(module = "qms.meeting.dao.MeetingDAO")
public class MeetingDAO extends GenericDAOImpl<Meeting, Long> implements IMeetingDAO {

    private Meeting saveMeeting(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        final boolean nuevo = meeting.getId() == -1l;
            meeting.setCurrentHashCode(meeting.hashCode());
            if (nuevo) {
                meeting.setLastHashCode(0);
                meeting.setUpdateSequence(0);
            } else {
                meeting.setLastHashCode(getCurrentHashCode(meeting.getId()));
                if (!meeting.getLastHashCode().equals(meeting.getCurrentHashCode())) {
                meeting.setUpdateSequence(meeting.getUpdateSequence() + 1);
            }
            }
        meeting = makePersistent(meeting, user.getId());
        if (meeting != null) {
            saveLinkedItems(meeting, linked, user);
            getLogger().debug("Meeting reported with id: '{}'", meeting.getId());
        }
        return meeting;
    }

    private Meeting saveLinkedItems(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        if (linked == null) {
            return meeting;
        }
        getLogger().trace("Saving linked items to meeting '{}' ", meeting.getDescription());
        Boolean deleteLinkedItemsAccess = user.isAdmin() || user.getServices().contains(ProfileServices.REUNION_ENCARGADO);
        super.saveLinkedItems(MeetingFile.class, meeting.getId(), linked.getFiles(), deleteLinkedItemsAccess, user.getId());
        super.saveLinkedItems(MeetingComment.class, meeting.getId(), linked.getComments(), deleteLinkedItemsAccess, user.getId());
        super.saveLinkedItems(MeetingParticipant.class, meeting.getId(), linked.getParticipants(), deleteLinkedItemsAccess, user.getId());
        getLogger().debug("Linked items saved for meeting with id: '{}'", meeting.getId());
        return meeting;
    }

    @OnReportedMeeting
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting reportMeeting(Meeting recurrence, MeetingLinkedDTO linked, LoggedUser user) {
        recurrence.setStatus(Meeting.STATUS.REPORTED.getValue());
        return saveRecurrentMeeting(recurrence, linked, user);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting readyMeeting(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        meeting.setStatus(Meeting.STATUS.READY.getValue());
        return this.saveMeeting(meeting, linked, user);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting inProcessMeeting(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        meeting.setStatus(Meeting.STATUS.IN_PROCESS.getValue());
        return saveMeeting(meeting, linked, user);
    }

    @Override
    @OnConcludedMeeting
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting concludeMeeting(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        meeting.setStatus(Meeting.STATUS.CONCLUDED.getValue());
        return saveMeeting(meeting, linked, user);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting closeMeeting(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        meeting.setStatus(Meeting.STATUS.CLOSED.getValue());
        return saveMeeting(meeting, linked, user);
    }

    @OnUpdatedPastMeeting
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting updatePastMeeting(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        return saveMeeting(meeting, linked, user);
    }

    @OnUpdatedCurrentMeeting
    @Override 
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting updateCurrentMeeting(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        return saveMeeting(meeting, linked, user);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting updateMeetingRecurrence(Meeting meeting, MeetingLinkedDTO linked, LoggedUser user) {
        return saveMeeting(meeting, linked, user);
    }

    @Override 
    @OnMeetingCommentSaved
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Comment saveComment(Comment comment, Meeting meeting, LoggedUser user) {
        if (comment.getCode().isEmpty()) {
            try {
                comment.setCode(EntityCommon.getNextCode(comment));
            } catch (final QMSException ex) {
                throw new RuntimeException(ex);
            }
        }
        comment = makePersistent(comment, user.getId());
        super.saveLinkedItem(MeetingComment.class, meeting.getId(), comment.getId(), user.getId());
        return comment;
    }

    @OnCancelledMeeting
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Meeting cancelMeeting(Long meetingId, Long outstandingSurveyId, String commentValue, LoggedUser user) {
        String query = "UPDATE " + Meeting.class.getCanonicalName() + " set status = :status where id = :id";
        Map params = new HashMap();
        params.put("status",Meeting.STATUS.CANCELLED.getValue());
        params.put("id",meetingId);
        HQL_updateByQuery(query, params);
        if (outstandingSurveyId != null) {
            final IRequestDAO daoRequest = getBean(IRequestDAO.class);
            IOutstandingSurveysDAO daoSurveys = getBean(IOutstandingSurveysDAO.class);
            final Long requestId = HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.outstandingSurveysId = " + outstandingSurveyId);
            String razon;
            if (daoRequest.isUserTheAuthor(requestId, user.getId())) {
                razon = getTag("cancelMeetingCancelFillByAuthor").replace(":user", user.getUser().getDescription().trim());
            } else {
                razon = getTag("cancelMeetingCancelFillGeneric").replace(":user", user.getUser().getDescription().trim());
            }
            razon = razon.replace(":comment", commentValue);
            if (daoRequest.cancelRequest(requestId, razon, user)) {
                daoSurveys.cancelOutstandingSurvey(
                        requestId,
                        razon,
                        outstandingSurveyId,
                        true,
                        user
                );
            } 
        }
        return HQLT_findById(Meeting.class, meetingId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void deleteMeetingRecurrence(Long meetingId, LoggedUser user) {
        String query = "UPDATE " + Meeting.class.getCanonicalName() + " set deleted = 1 where id = :id";
        Map params = new HashMap(1);
        params.put("id", meetingId);
        HQL_updateByQuery(query, params);
}

    private Meeting saveRecurrentMeeting(Meeting recurrence, MeetingLinkedDTO linked, LoggedUser user) {
        Meeting firstMeeting = SerializationUtils.clone(recurrence);
        recurrence.setStatus(Meeting.STATUS.RECURRING.getValue());
        recurrence.setRecurrent(IPeriodicEntity.RECURRENT.YES.getValue());
        IPeriodicityDAO periodicityDao = getBean(IPeriodicityDAO.class);
        Date next = Utilities.truncDate(periodicityDao.getNextOccurence(recurrence.getFinishOn(), recurrence.getPeriodicity().getId()));
        recurrence.setNextDate(next);
        saveMeeting(recurrence, linked, user);
        Long recurrenceId = recurrence.getId();
        firstMeeting.setId(-1L);
        firstMeeting.setCode(recurrence.getCode() + "-01");
        firstMeeting.setPeriodicity(null);
        firstMeeting.setRecurrent(IPeriodicEntity.RECURRENT.NO.getValue());
        firstMeeting.setRecurrenceId(recurrenceId);
        return saveMeeting(firstMeeting, linked, user);
    }

    private Integer getCurrentHashCode(Long meetingId) {
        return HQL_findSimpleInteger(""
                + " SELECT c.currentHashCode"
                + " FROM " + Meeting.class.getCanonicalName() + " c"
                + " WHERE c.id = " + meetingId);
}
    
    

}
