/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.timesheet.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TimesheetWithStopwatchDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long timesheetId;
    private Boolean hasEnableStopwatch;
    private Date date;

    public TimesheetWithStopwatchDto() {
    }

    public TimesheetWithStopwatchDto(Long timesheetId, Boolean hasEnableStopwatch, Date date) {
        this.timesheetId = timesheetId;
        this.hasEnableStopwatch = hasEnableStopwatch;
        this.date = date;
    }

    public Long getTimesheetId() {
        return timesheetId;
    }

    public void setTimesheetId(Long timesheetId) {
        this.timesheetId = timesheetId;
    }

    public Boolean getHasEnableStopwatch() {
        return hasEnableStopwatch;
    }

    public void setHasEnableStopwatch(Boolean hasEnableStopwatch) {
        this.hasEnableStopwatch = hasEnableStopwatch;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }
    
}
