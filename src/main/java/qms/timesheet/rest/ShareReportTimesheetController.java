package qms.timesheet.rest;

import Framework.Config.TextHasValue;
import bnext.exception.InvalidCipherDecryption;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.activity.dto.ShareReportDataSourceDTO;
import qms.configuration.rest.ShareReportBaseController;
import qms.framework.dao.IShareReportDAO;
import qms.framework.entity.ShareReport;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.DataSourceCredentialError;
import qms.framework.util.ShareReportHandler;
import qms.util.GridFilter;
import qms.util.QMSException;

@RestController
@Lazy
@RequestMapping("share-report-timesheet")
public class ShareReportTimesheetController extends ShareReportBaseController {

    private static final Module IMPLEMENTED_MODULE = Module.TIMESHEET;
    
    private final String HAS_LIST_ACESS = ""
            + " hasAnyAuthority("
                + "'IS_ADMIN', 'ADMON_SISTEMA'"
            + ")"
            +" || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('TS_REGISTER_REPORT'))";

    @Autowired
    @Qualifier("ShareReportDAO")
    private IShareReportDAO dao;

    @Override
    @PostMapping()
    @RequestMapping("list")
    @PreAuthorize(HAS_LIST_ACESS)
    public GridInfo list(@RequestBody GridFilter filter) {
        return dao.getMainList(filter, IMPLEMENTED_MODULE, SecurityUtils.getLoggedUser());
    }
    
    @Override
    @GetMapping("data-source/{id}")
    @PreAuthorize(HAS_LIST_ACESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ShareReportDataSourceDTO dataSource(
        @PathVariable(value = "id", required = true) Long id
    ) {
        final ShareReportDataSourceDTO result = dao.getDataSource(id, IMPLEMENTED_MODULE, SecurityUtils.getLoggedUser());
        return result;
    }

    @Override
    @GetMapping()
    @RequestMapping("columns/{id}")
    @PreAuthorize(HAS_LIST_ACESS)
    public List<TextHasValue> columns(
        @PathVariable(value = "id", required = true) Long reportId
    ) {
        final List<TextHasValue> result = dao.getColumns(reportId, SecurityUtils.getLoggedUser());
        return result;
    }
    
    @Override
    @PostMapping()
    @RequestMapping("save")
    @PreAuthorize(HAS_LIST_ACESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity save(
        @RequestBody ShareReport shareReport
    )  throws QMSException {
        return dao.save(shareReport, IMPLEMENTED_MODULE, SecurityUtils.getLoggedUser());
    }
    
    @Override
    @GetMapping
    @RequestMapping("toggle-status/{id}")
    @PreAuthorize(HAS_LIST_ACESS)
    public ResponseEntity toggleStatus(
        @PathVariable(value = "id", required = true) Long id
    ) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return dao.toggleStatus(id, loggedUser);
    }
    
    @Override
    @GetMapping
    @RequestMapping("send/{id}")
    @PreAuthorize(HAS_LIST_ACESS)
    public ResponseEntity<Boolean> send(
        @PathVariable(value = "id", required = true) Long id
    ) throws SQLException, DataSourceCredentialError, InvalidCipherDecryption {
        final ShareReportHandler handler = new ShareReportHandler();
        return handler.send(id, SecurityUtils.getLoggedUser());
    }
    
    @Override
    @GetMapping()
    @PreAuthorize(HAS_LIST_ACESS)
    @RequestMapping("load/{id}")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Object> load(
        @PathVariable(value = "id", required = true) Long id
    ) {
        return dao.load(id, SecurityUtils.getLoggedUser());
    }
    
}
