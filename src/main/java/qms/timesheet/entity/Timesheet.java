package qms.timesheet.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import ape.pending.core.StrongBaseAPE;
import bnext.dto.FileDataDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.util.CacheConstants;
import qms.timesheet.dto.ITimesheetFileDataEntityDto;
import qms.timesheet.util.ITimesheetEntity;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.TIMESHEET, usage = CacheConcurrencyStrategy.READ_WRITE)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "timesheet")
public class Timesheet extends StandardEntity<Timesheet> implements IAuditableEntity, StrongBaseAPE, ITimesheetEntity, ITimesheetFileDataEntityDto {

    private static final long serialVersionUID = 1L;

    private String code;
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private String systemLinkValue;
    private Long systemLinkId;
    private Long plannerId;
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Long pendingRecordId;
    private Long userId;
    private Date timesheetDate;
    private Date timesheetStart;
    private Date timesheetEnd;
    private String fillType;
    private String uid;
    private String tags;

    private Boolean recordLocked = false; // Este campo es para evitar modificar los datos de Cliente, Proyecto y Tarea
    private Double workedMinutes;
    private Boolean hasEnableStopwatch;
    private Integer stopwatchType;
    private String updateReason;
    private Date stopwatchLocalTimeStart;
    private Boolean stopwatchReStarted;

    /**
     * Se guarda el ID de la actividad planeada
     */
    private Long activityPlannedId;  // Solo para registros PLANNED se inserta este registro
 
    //Transient
    private List<FileDataDto> fileData;

    public Timesheet() {
    }

    public Timesheet(Long id, Boolean hasEnableStopwatch) {
        this.id = id;
        this.hasEnableStopwatch = hasEnableStopwatch;
    }
    
    public Timesheet(Long id, Boolean hasEnableStopwacth, Date startDate) {
        this.id = id;
        this.hasEnableStopwatch = hasEnableStopwacth;
        this.timesheetStart = startDate;
    }

    @Id
    @Column(name = "timesheet_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            this.status = 1;
        } else {
            this.status = status;
        }
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            this.deleted = 0;
        } else {
            this.deleted = deleted;
        }
    }

    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Override
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @CreatedBy
    @Column(name = "created_by", updatable = false)
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @LastModifiedBy
    @Override
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    @Column(name = "user_id")
    public Long getUserId() {
        return this.userId;
    }

    @Override
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    @Column(name = "pending_record_id")
    public Long getPendingRecordId() {
        return pendingRecordId;
    }

    @Override
    public void setPendingRecordId(Long pendingRecordId) {
        this.pendingRecordId = pendingRecordId;
    }

    @Override
    @Column(name = "planner_id")
    public Long getPlannerId() {
        return plannerId;
    }

    @Override
    public void setPlannerId(Long plannerId) {
        this.plannerId = plannerId;
    }

    @Column(name = "business_unit_id")
    @Override
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Override
    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Override
    @Column(name = "timesheet_date")
    @Temporal(javax.persistence.TemporalType.DATE)
    public Date getTimesheetDate() {
        return timesheetDate;
    }

    @Override
    public void setTimesheetDate(Date timesheetDate) {
        this.timesheetDate = timesheetDate;
    }

    @Override
    @Column(name = "timesheet_start")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getTimesheetStart() {
        return timesheetStart;
    }

    @Override
    public void setTimesheetStart(Date timesheetStart) {
        this.timesheetStart = timesheetStart;
    }

    @Override
    @Column(name = "timesheet_end")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getTimesheetEnd() {
        return timesheetEnd;
    }

    @Override
    public void setTimesheetEnd(Date timesheetEnd) {
        this.timesheetEnd = timesheetEnd;
    }

    @Override
    @Column(name = "fill_type")
    public String getFillType() {
        return fillType;
    }

    @Override
    public void setFillType(String fillType) {
        this.fillType = fillType;
    }

    @Override
    @Column(name = "tags")
    public String getTags() {
        return tags;
    }

    @Override
    public void setTags(String tags) {
        this.tags = tags;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.id);
        hash = 97 * hash + Objects.hashCode(this.userId);
        hash = 97 * hash + Objects.hashCode(this.timesheetDate);
        hash = 97 * hash + Objects.hashCode(this.timesheetStart);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Timesheet other = (Timesheet) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        if (!Objects.equals(this.userId, other.userId)) {
            return false;
        }
        if (!Objects.equals(this.timesheetDate, other.timesheetDate)) {
            return false;
        }
        return Objects.equals(this.timesheetStart, other.timesheetStart);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Timesheet{id=").append(id);
        sb.append(", code=").append(code);
        sb.append(", description=").append(description);
        sb.append(", fillType=").append(fillType);
        sb.append(", tags=").append(tags);
        sb.append(", status=").append(status);
        sb.append(", deleted=").append(deleted);
        sb.append(", userId=").append(userId);
        sb.append(", timesheetDate=").append(timesheetDate);
        sb.append(", timesheetStart=").append(timesheetStart);
        sb.append('}');
        return sb.toString();
    }

    @Column(name = "uid")
    @Override
    public String getUid() {
        return uid;
    }

    @Override
    public void setUid(String uid) {
        this.uid = uid;
    }

    @Column(name = "system_link_value")
    @Override
    public String getSystemLinkValue() {
        return systemLinkValue;
    }

    @Override
    public void setSystemLinkValue(String systemLinkValue) {
        this.systemLinkValue = systemLinkValue;
    }

    @Override
    @Column(name = "system_link_id")
    public Long getSystemLinkId() {
        return systemLinkId;
    }

    @Override
    public void setSystemLinkId(Long systemLinkId) {
        this.systemLinkId = systemLinkId;
    }
    
    @Column(name = "activity_planned_id")
    @Override
    public Long getActivityPlannedId() {
        return activityPlannedId;
    }

    @Override
    public void setActivityPlannedId(Long activityPlannedId) {
        this.activityPlannedId = activityPlannedId;
    }

    @Override
    @Column(name = "record_locked")
    @Type(type = "numeric_boolean")
    public Boolean getRecordLocked() {
        return Boolean.TRUE.equals(recordLocked);
    }

    @Override
    public void setRecordLocked(Boolean recordLocked) {
        this.recordLocked = recordLocked;
    }

    @Generated(value = GenerationTime.ALWAYS)
    @Column(name = "worked_minutes", insertable = false, updatable = false)
    @Override
    public Double getWorkedMinutes() {
        return workedMinutes;
    }

    public void setWorkedMinutes(Double workedMinutes) {
        this.workedMinutes = workedMinutes;
    }

    @Override
    @Transient
    public List<FileDataDto> getFileData() {
        return fileData;
    }

    @Override
    public void setFileData(List<FileDataDto> fileData) {
        this.fileData = fileData;
    }

    @Override
    public void setHasEnableStopwatch(Boolean hasEnableStopwatch) {
        this.hasEnableStopwatch = hasEnableStopwatch;
    }

    @Override
    @Column(name = "has_enable_stopwatch")
    @Type(type = "numeric_boolean")
    public Boolean getHasEnableStopwatch() {
        return Boolean.TRUE.equals(hasEnableStopwatch);
    }

    @Override
    public void setStopwatchType(Integer stopwatchType) {
        this.stopwatchType = stopwatchType;
    }

    @Override
    @Column(name = "stopwatch_type")
    public Integer getStopwatchType() {
        return stopwatchType;
    }

    @Column(name = "update_reason")
    public String getUpdateReason() {
        return updateReason;
    }

    public void setUpdateReason(String updateReason) {
        this.updateReason = updateReason;
    }

    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @Column(name = "stopwatch_local_time_start")
    @Override
    public Date getStopwatchLocalTimeStart() {
        return stopwatchLocalTimeStart;
    }

    @Override
    public void setStopwatchLocalTimeStart(Date stopwatchLocalTimeStart) {
        this.stopwatchLocalTimeStart = stopwatchLocalTimeStart;
    }
    

    @Transient
    @Override
    public Boolean getStopwatchReStarted() {
        return stopwatchReStarted;
    }

    @Override
    public void setStopwatchReStarted(Boolean stopwatchReStarted) {
        this.stopwatchReStarted = stopwatchReStarted;
    }
    
}
