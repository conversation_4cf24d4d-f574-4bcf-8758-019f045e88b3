package qms.framework.pdf.rendering.icepdf;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.icepdf.core.pobjects.Document;
import qms.framework.pdf.rendering.IPdfDocument;

/**
 *
 * <AUTHOR>
 */
public class IcePdfDocument implements IPdfDocument<Document> {

    private final Document renderer;
    private final Integer numberOfPages;
    private final Path source;

    public IcePdfDocument(final Document renderer, final Path source) {
        this.numberOfPages = renderer.getNumberOfPages();
        this.renderer = renderer;
        this.source = source;
    }

    @Override
    public Integer getNumberOfPages() {
        return numberOfPages;
    }

    @Override
    public void close() throws IOException {
        Files.delete(Paths.get(renderer.getDocumentOrigin()));
        renderer.dispose();
    }

    @Override
    public Document getRenderer() {
        return renderer;
    }

    @Override
    public Path getSource() {
        return source;
    }

}
