package qms.framework.dao;

import Framework.Config.TextHasValue;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.util.List;
import java.util.Map;
import javax.annotation.Nonnull;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.activity.dto.ShareReportDataSourceDTO;
import qms.framework.dto.ShareReportDTO;
import qms.framework.entity.ShareReport;
import qms.util.GridFilter;
import qms.util.QMSException;

@Implementation(name = "ShareReportDAO")
public interface IShareReportDAO extends IGenericDAO<ShareReport, Long> {
    
    GridInfo getMainList(GridFilter filter, Module module, @Nonnull final ILoggedUser loggedUser);
    
    @Override
    ResponseEntity toggleStatus(Long shareReportId, @Nonnull final ILoggedUser loggedUser);
    
    ResponseEntity save(ShareReport shareReport, Module module, @Nonnull final ILoggedUser loggedUser) throws QMSException;

    Integer updateExpiredEvent(@Nonnull final ILoggedUser loggedUser);
    
    Integer updateSentEvent(ShareReportDTO report, @Nonnull final ILoggedUser loggedUser);
    
    Integer updateSentFailed(ShareReportDTO report, @Nonnull final ILoggedUser loggedUser);

    ShareReportDataSourceDTO getDataSource(Long shareReportId, Module module, @Nonnull final ILoggedUser loggedUser);

    Map<String, Object> load(Long shareReportId, @Nonnull final ILoggedUser loggedUser);

    List<TextHasValue> getColumns(Long reportId, @Nonnull final ILoggedUser loggedUser);

    List<Long> getConfiguredColumns(Long shareReportId);

    ShareReport saveNewEvent(ShareReport entity, Long createdBy);

    ShareReport updateParent(ShareReport entity, Long createdBy);

    Integer deleteReportColumns(Long reportId, List<Long> reportColumnsIds, @Nonnull final ILoggedUser loggedUser);

    
}
