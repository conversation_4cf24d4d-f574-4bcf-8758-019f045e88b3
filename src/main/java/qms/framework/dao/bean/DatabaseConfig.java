package qms.framework.dao.bean;

import java.util.Optional;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import qms.framework.rest.SecurityUtils;

@Lazy
@Configuration
@EnableJpaRepositories()
@EnableJpaAuditing
public class DatabaseConfig {
    @Bean
    AuditorAware<Long> auditorAware() {
        return () -> Optional.ofNullable(SecurityUtils.getLoggedUserId());
    }
}