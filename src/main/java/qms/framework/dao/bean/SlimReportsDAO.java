package qms.framework.dao.bean;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.INodeAccessDAO;
import DPMS.DAOInterface.IUserRefDAO;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.BusinessUnitLite;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Document;
import DPMS.Mapping.ExternalDocumentCatalog;
import DPMS.Mapping.NodeAccess;
import DPMS.Mapping.Process;
import DPMS.Mapping.Request;
import Framework.Config.Language;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.exception.ExplicitRollback;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import isoblock.surveys.dao.hibernate.SurveyField;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import mx.bnext.access.Module;
import org.hibernate.internal.SessionImpl;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.core.ITableFactory;
import qms.custom.dao.IPrintingFormatDAO;
import qms.custom.dto.IFlexiFields;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.FormSlimReportDTO;
import qms.form.dto.SlimReportOrderColumnsDTO;
import qms.form.dto.SurveyDataFieldDTO;
import qms.form.entity.AnswerPartType;
import qms.form.util.CatalogFieldType;
import qms.form.util.FixedField;
import qms.form.util.RangeFilterType;
import qms.form.util.SurveyFieldAnswerType;
import qms.form.util.SurveyUtil;
import qms.framework.bulk.imp.FormBulkHandler;
import qms.framework.bulk.imp.IReportBulkBaseHandler;
import qms.framework.dao.IDatabaseQueryDAO;
import qms.framework.dao.IReportDAO;
import qms.framework.dao.ISlimReportsDAO;
import qms.framework.dto.IReportColumnDTO;
import qms.framework.dto.QueryTestDto;
import qms.framework.dto.ReportColumnDTO;
import qms.framework.dto.SlimReportsToRegenerateDTO;
import qms.framework.entity.DatabaseQuery;
import qms.framework.entity.QueryColumn;
import qms.framework.entity.Report;
import qms.framework.entity.ReportColumn;
import qms.framework.entity.SlimReports;
import qms.framework.entity.SlimReportsGhostField;
import qms.framework.entity.SlimReportsRelatedField;
import qms.framework.entity.SlimReportsSurveyFields;
import qms.framework.entity.SlimReportsSurveyFieldsFixed;
import qms.framework.entity.SlimReportsTransformedField;
import qms.framework.entity.SlimReportsTransformedFieldHistory;
import qms.framework.entity.SlimReportsTransformedFieldRule;
import qms.framework.util.CacheRegion;
import qms.framework.util.DatabaseQueryHandler;
import qms.framework.util.DatabaseQueryType;
import qms.framework.util.ReportColumnType;
import qms.framework.util.ReportType;
import qms.framework.util.SlimReportsSurveyFieldsFixedType;
import qms.framework.util.SlimReportsSurveyFieldsType;
import qms.util.FormUtil;
import qms.util.QMSException;
import qms.util.SQLHandler;

@Lazy
@Repository(value = "SlimReportsDAO")
@Language(module = "qms.framework.dao.bean.SlimReportsDAO")
@Scope(value = "singleton")
public class SlimReportsDAO extends GenericDAOImpl<SlimReports, Long> implements ISlimReportsDAO {

    public static final String CONNECTION_TYPE = "bnext-qms";

    private List<Report> getReportsByMasterId(String masterId) {
        final List<Report> allReportsByMasterId = HQLT_findByQuery(Report.class, ""
                + " SELECT "
                + " r "
                + " FROM " + Report.class.getCanonicalName() + " r "
                        + " WHERE r.documentMasterId = :masterId "
                        + " AND r.deleted = 0 ",
                ImmutableMap.of("masterId", masterId),
                true, CacheRegion.REPORT, 0
        );
        return allReportsByMasterId;
    }
    
    private SlimReports getSlimReportByReport(Report report) {
        final SlimReports slimReportByReport = (SlimReports) HQLT_findSimple(SlimReports.class, ""
                + " SELECT "
                + " slr "
                + " FROM " + SlimReports.class.getCanonicalName() + " slr "
                        + " WHERE slr.reportId = :reportId ",
                ImmutableMap.of("reportId", report.getId()),
                true, CacheRegion.REPORT, 0
        );
        return slimReportByReport;
    }

    /**
     * Regenera los reportes asociados al masterId eliminando las columnas del reporte que no existan en commonColumns.
     * @param masterId
     * @param commonColumns Columnas que se deben conservar.
     * @param loggedUser
     * @return
     * @throws QMSException
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer checkSlimReportForRegenerate(
            String masterId,
            List<String> commonColumns,
            ILoggedUser loggedUser
    ) throws QMSException {
        final Long surveyId = getActiveSurveyId(masterId);
        final List<Report> reports = getReportsByMasterId(masterId);
        final List<SlimReportsToRegenerateDTO> pendingSlimReports = new ArrayList<>();
        for (final Report report : reports) {
            final List<SlimReportsSurveyFields> fieldsDeleted = new ArrayList<>();
            SlimReports slimReport = getSlimReportByReport(report);
            for (final SlimReportsSurveyFields surveyField : slimReport.getSurveysFields()) {
                boolean anyMatch = commonColumns.contains(surveyField.getFieldName());
                if (!anyMatch) {
                    fieldsDeleted.add(surveyField);
                }
            }
            if (!fieldsDeleted.isEmpty()) {
                fieldsDeleted.forEach(slimReport.getSurveysFields()::remove);
                reordenateFieldsAfterRemoveFields(slimReport);
                final SlimReportsToRegenerateDTO pending = new SlimReportsToRegenerateDTO(
                        report,
                        slimReport,
                        fieldsDeleted
                );
                pendingSlimReports.add(pending);
            }
        }
        if (!pendingSlimReports.isEmpty()) {
            try {
                regenarateSlimReports(surveyId, pendingSlimReports, loggedUser);
            } catch (QMSException ex) {
                throw new QMSException("Cannot regenerate report for document " + masterId, ex);
            }
        }
        return pendingSlimReports.size();
    }

    /**
     * Regenera los reportes asociados al masterId con su estructura actual.
     * @param masterId
     * @param loggedUser
     * @return
     * @throws QMSException
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer regenerateSlimReport(String masterId, ILoggedUser loggedUser) throws QMSException {
        final Long surveyId = getActiveSurveyId(masterId);
        final List<Report> reports = getReportsByMasterId(masterId);
        final List<SlimReportsToRegenerateDTO> pendingSlimReports = new ArrayList();
        for (final Report report : reports) {
            final SlimReports slimReport = getSlimReportByReport(report);
            final List<SlimReportsSurveyFields> fieldsDeleted = calculateFieldsToDelete(masterId, loggedUser, slimReport);
            if (!fieldsDeleted.isEmpty()) {
                slimReport.getSurveysFields().removeAll(fieldsDeleted);
                reordenateFieldsAfterRemoveFields(slimReport);
            }
            final SlimReportsToRegenerateDTO pending = new SlimReportsToRegenerateDTO(
                    report,
                    slimReport,
                    fieldsDeleted
            );
            pendingSlimReports.add(pending);
        }
        try {
            regenarateSlimReports(surveyId, pendingSlimReports, loggedUser);
        } catch (QMSException ex) {
            throw new QMSException("Cannot recalculate slim-report for document " + masterId, ex);
        }
        return pendingSlimReports.size();
    }

    /**
     * Obtiene las columnas que se deben eliminar por reducir las columnas de items a una sola
     * y actualiza el nombre de la columna de SlimReport que quedará como principal.
     */
    private List<SlimReportsSurveyFields> calculateFieldsToDelete(
            String masterId,
            ILoggedUser loggedUser,
            @Nullable SlimReports slimReport
    ) {
        // Se gestiona la actualización de campos cuando disabledColumnPerOption está activa,
        // actualizando solo un campo y removiendo los adicionales.
        final Long surveyId = FormUtil.getSurveyId(masterId);
        final FieldConfigDTO fieldsConfig = SurveyUtil.getFields(
                surveyId,
                false,
                false,
                false,
                null,
                0,
                loggedUser,
                true,
                true,
                false
        );
        Set<SurveyDataFieldDTO> currentFields = fieldsConfig.getFields();
        if (slimReport == null || currentFields == null || currentFields.isEmpty()) {
            return new ArrayList<>();
        }
        final List<SlimReportsSurveyFields> fieldsDeleted = new ArrayList<>();
        for (SurveyDataFieldDTO fieldDTO : currentFields) {
            if (fieldDTO == null || !fieldDTO.getDisableColumnPerOption()) {
                continue;
            }
            // Por comodidad solo se gestiona la descripción, que es la que importa visualmente en el reporte
            if (!fieldDTO.getName().contains(AnswerPartType.CATALOG_DESCRIPTION.getSuffix())) {
                continue;
            }
            // Se obtienen todos los items asociados al campo
            Set<SlimReportsSurveyFields> surveysFields = slimReport.getSurveysFields();
            if (surveysFields == null || surveysFields.isEmpty()) {
                continue;
            }
            Set<SlimReportsSurveyFields> slimFields = surveysFields.stream()
                    .filter(s -> s.getFieldName() != null
                            && s.getFieldName().contains(fieldDTO.getFieldCode() + "_item"))
                    .collect(Collectors.toSet());
            if (slimFields.isEmpty()) {
                continue;
            }
            // Se usa el primer elemento para actualizar y los de más se desechan
            SlimReportsSurveyFields firstField = slimFields.stream().findFirst().get();
            firstField.setFieldName(fieldDTO.getName());
            fieldsDeleted.addAll(slimFields.stream()
                    .filter(f-> !f.getFieldName().equals(fieldDTO.getName()))
                    .collect(Collectors.toList()));
        }
        return fieldsDeleted;
    }

    private void reordenateFieldsAfterRemoveFields(SlimReports slimReportByReport) {
        //Fusiona las listas de campos en una sola
        List<SlimReportOrderColumnsDTO> allFields = new ArrayList<>();
        allFields.addAll(
                slimReportByReport.getSurveysFilters().stream().map(
                        (item) -> new SlimReportOrderColumnsDTO(
                                item.getFieldName(),
                                item.getFieldName(),
                                item.getOrder(),
                                SlimReportOrderColumnsDTO.FieldType.FLEXI,
                                item.getAlias()
                        )
                ).collect(Collectors.toList())
        );
        allFields.addAll(
                slimReportByReport.getSurveyFiltersFixed().stream().map(
                        (item) -> new SlimReportOrderColumnsDTO(
                                item.getFixedFieldEnumValue().toString(),
                                item.getFixedFieldEnumValue().toString(),
                                item.getOrder(),
                                SlimReportOrderColumnsDTO.FieldType.FIXED,
                                item.getAlias()
                        )
                ).collect(Collectors.toList())
        );
        allFields.addAll(
                slimReportByReport.getSurveysFields().stream().map(
                        (item) -> new SlimReportOrderColumnsDTO(
                                item.getFieldName(), 
                                item.getFieldName(), 
                                item.getOrder(),
                                SlimReportOrderColumnsDTO.FieldType.FLEXI,
                                item.getAlias()
                        )
                ).collect(Collectors.toList())
        );
        allFields.addAll(
                slimReportByReport.getSurveyFieldsFixed().stream().map(
                        (item) -> new SlimReportOrderColumnsDTO(
                                item.getFixedFieldEnumValue().toString(),
                                item.getFixedFieldEnumValue().toString(),
                                item.getOrder(),
                                SlimReportOrderColumnsDTO.FieldType.FIXED,
                                item.getAlias()
                        )
                ).collect(Collectors.toList())
        );
        //Ordena la lista global de campos con su orden actual
        allFields = allFields.stream().sorted((n1, n2) -> {
            if (n1.getOrder() == null) {
                n1.setOrder(0);
            }
            if (n2.getOrder() == null) {
                n2.setOrder(0);
            }
            return Integer.compare(n1.getOrder(), n2.getOrder());
        }).collect(Collectors.toList());
        //Resetea los indices de orden respetando el ordenamiento previo
        int index = 1;
        for (SlimReportOrderColumnsDTO allField : allFields) {
            allField.setOrder(index);
            index++;
        }
        for (SlimReportsSurveyFields surveyField : slimReportByReport.getSurveysFilters()) {
            List<SlimReportOrderColumnsDTO> itemOrderFixed = allFields.stream().filter(
                    f -> f.getId().equals(surveyField.getFieldName())
                            && f.getDescription().equals(surveyField.getFieldName())
            ).collect(Collectors.toList());
            if (itemOrderFixed.isEmpty()) {
                surveyField.setOrder(null);
                continue;
            }
            surveyField.setOrder(itemOrderFixed.get(0).getOrder());
        }
        for (SlimReportsSurveyFieldsFixed surveyField : slimReportByReport.getSurveyFiltersFixed()) {
            List<SlimReportOrderColumnsDTO> itemOrderFixed = allFields.stream().filter(
                    f -> f.getId().equals(surveyField.getFixedFieldEnumValue().toString())
                            && f.getDescription().equals(surveyField.getFixedFieldEnumValue().toString())
            ).collect(Collectors.toList());
            if (itemOrderFixed.isEmpty()) {
                surveyField.setOrder(null);
                continue;
            }
            surveyField.setOrder(itemOrderFixed.get(0).getOrder());
        }
        //Aplica los nuevos ordenes correspondientes a cada campo del reporte
        for (SlimReportsSurveyFields surveyField : slimReportByReport.getSurveysFields()) {
            List<SlimReportOrderColumnsDTO> itemOrderFixed = allFields.stream().filter(
                    f -> f.getId().equals(surveyField.getFieldName())
                    && f.getDescription().equals(surveyField.getFieldName())
            ).collect(Collectors.toList());
            if (itemOrderFixed.isEmpty()) {
                surveyField.setOrder(null);
                continue;
            }
            surveyField.setOrder(itemOrderFixed.get(0).getOrder());
        }
        for (SlimReportsSurveyFieldsFixed surveyField : slimReportByReport.getSurveyFieldsFixed()) {
            List<SlimReportOrderColumnsDTO> itemOrderFixed = allFields.stream().filter(
                    f -> f.getId().equals(surveyField.getFixedFieldEnumValue().toString())
                    && f.getDescription().equals(surveyField.getFixedFieldEnumValue().toString())
            ).collect(Collectors.toList());
            if (itemOrderFixed.isEmpty()) {
                surveyField.setOrder(null);
                continue;
            }
            surveyField.setOrder(itemOrderFixed.get(0).getOrder());
        }
    }

    private void regenarateSlimReports(
            Long surveyId,
            final List<SlimReportsToRegenerateDTO> pendingSlimReports,
            final ILoggedUser loggedUser
    ) throws QMSException {
        for (final SlimReportsToRegenerateDTO pendingSlimReport : pendingSlimReports) {
            try {
                // Elimina el reporte y consulta de la version anterior
                final Report report = pendingSlimReport.getReport();
                deleteReportAndQuery(report.getId(), report.getQuery().getId(), loggedUser);
                // Elimina el campo del catalogo de surveys fields de slim reports
                final List<SlimReportsSurveyFields> slimSurveyFieldsDeleted = pendingSlimReport.getSlimSurveyFieldsDeleted();
                HQL_updateByQuery(""
                        + "DELETE FROM " + SlimReportsSurveyFields.class.getCanonicalName() + " srsf "
                        + "WHERE srsf.id IN (:surveyFieldsIds)",
                        ImmutableMap.of(
                                "surveyFieldsIds", slimSurveyFieldsDeleted.stream()
                                        .map(SlimReportsSurveyFields::getId)
                                        .collect(Collectors.toList())
                        ));
                // Se construyen de nuevo los reportes
                final FormSlimReportDTO options = new FormSlimReportDTO();
                final SlimReports slimReport = pendingSlimReport.getSlimReport();
                options.setDocumentMasterId(slimReport.getDocumentMasterId());
                options.setDescription(slimReport.getDescription());
                options.setLocalTimeForDates(slimReport.getLocalTimeForDates());
                options.setGhostFields(slimReport.getGhostFields());
                options.setCountPrintFormats(report.getCountPrintFormats());

                final Set<SlimReportsSurveyFieldsFixed> slimSurveyFiltersFixed = slimReport.getSurveyFiltersFixed();
                options.setFixedFilters(slimSurveyFiltersFixed.stream()
                        .map(SlimReportsSurveyFieldsFixed::getFixedFieldEnumValue)
                        .collect(Collectors.toSet()));

                final Set<SlimReportsSurveyFields> slimSurveyFilters = slimReport.getSurveysFilters();
                options.setSurveyFilters(slimSurveyFilters.stream()
                        .map(SlimReportsSurveyFields::getFieldName)
                        .collect(Collectors.toList()));

                final Set<SlimReportsSurveyFieldsFixed> slimSurveyFieldsFixed = slimReport.getSurveyFieldsFixed();
                options.setFixedFields(slimSurveyFieldsFixed.stream()
                        .map(SlimReportsSurveyFieldsFixed::getFixedFieldEnumValue)
                        .collect(Collectors.toSet()));

                final Set<SlimReportsSurveyFields> slimSurveyFields = slimReport.getSurveysFields();
                options.setSurveyFields(slimSurveyFields.stream()
                        .map(SlimReportsSurveyFields::getFieldName)
                        .collect(Collectors.toList()));
                
                final Set<SlimReportsRelatedField> relatedFieldNames = FormUtil.getSlimRelatedFields(
                        pendingSlimReport.getSlimReport(),
                        getBean(IFormCaptureDAO.class),
                        loggedUser
                );
                slimReport.setRelatedFields(relatedFieldNames);
                
                final Set<SlimReportsRelatedField> relatedFields = slimReport.getRelatedFields();
                if (relatedFields != null && !relatedFields.isEmpty()) {
                    options.setRelatedFields(relatedFields.stream()
                            .map(SlimReportsRelatedField::getFieldName)
                            .collect(Collectors.toList()));
                }
                
                options.setDetails(slimReport.getDetails());
                options.setWhereFillerUserParticipate(slimReport.getWhereFillerUserParticipate());
                options.setRestrictRecordsByDepartment(slimReport.getRestrictRecordsByDepartment());
                if (pendingSlimReport.getReport().getNodeId() != null) {
                    NodeAccess node = (NodeAccess) HQLT_findById(NodeAccess.class, pendingSlimReport.getReport().getNodeId());
                    if (node.getBusinessUnits() != null) {
                        final Set<Long> businessUnitIds = node.getBusinessUnits().stream()
                                .map(BusinessUnitLite::getId
                                ).collect(Collectors.toSet());
                        options.setBusinessUnitAccessValues(businessUnitIds);
                    }
                    if (node.getDepartments() != null) {
                        final Set<Long> departmenentIds = node.getDepartments().stream()
                                .map(BusinessUnitDepartmentLite::getId)
                                .collect(Collectors.toSet());
                        options.setBusinessUnitDepartmentAccessValues(departmenentIds);
                    }
                    if (node.getProcess() != null) {
                        final Set<Long> processIds = node.getProcess().stream()
                                .map(Process::getId)
                                .collect(Collectors.toSet());
                        options.setProcessAccessValues(processIds);
                    }
                    if (node.getUsers() != null) {
                        final Set<Long> userIds = node.getUsers().stream()
                                .map(UserRef::getId)
                                .collect(Collectors.toSet());
                        options.setUserAccessValues(userIds);
                    }
                }
                final List<SlimReportOrderColumnsDTO> columOrders = new ArrayList<>();

                slimReport.getSurveysFields().forEach(f -> {
                    columOrders.add(new SlimReportOrderColumnsDTO(f.getFieldName(), f.getFieldName(), f.getOrder(), SlimReportOrderColumnsDTO.FieldType.FLEXI,f.getAlias()));
                });
                slimReport.getSurveyFieldsFixed().forEach(f -> {
                    columOrders.add(
                            new SlimReportOrderColumnsDTO(
                                    f.getFixedFieldEnumValue().toString(),
                                    f.getFixedFieldEnumValue().toString(),
                                    f.getOrder(),
                                    SlimReportOrderColumnsDTO.FieldType.FIXED,
                                    f.getAlias()
                            )
                    );
                });
                options.setOrderColumns(columOrders);
                
                options.setTruncateColumnsToMax(true);

                generateNewReportVersion(surveyId, options, loggedUser, slimReport);
            } catch (QMSException ex) {
                throw new QMSException("Cannot recalculate report for document " + pendingSlimReport.getReport().getDocumentMasterId(), ex);
            }
        }
    }

    private void deleteReportAndQuery(Long reportId, Long queryId, ILoggedUser loggedUser) {
        final IReportDAO dao = getBean(IReportDAO.class);
        dao.delete(reportId, loggedUser);
        final IDatabaseQueryDAO queryDao = getBean(IDatabaseQueryDAO.class);
        queryDao.delete(queryId, loggedUser);
    }

    private void generateNewReportVersion(
            Long surveyId,
            FormSlimReportDTO options,
            ILoggedUser loggedUser,
            SlimReports slimReport
    ) throws QMSException {
        final Report report = new Report(-1L);
        report.setType(ReportType.SLIM_REPORT.getValue());
        report.setCountPrintFormats(options.getCountPrintFormats());
        final GenericSaveHandle gsh = generateReportForSlimReport(surveyId, report, options, loggedUser);
        if (gsh == null || !Objects.equals(gsh.getOperationEstatus(), 1)) {
            String error = "Failed to save";
            if (gsh != null) {
                error = gsh.getErrorMessage();
            }
            throw new QMSException("Cannot generate new report version: " + slimReport.getCode() + ". Details " + error);
        }
        slimReport.setReportId(report.getId());
        makePersistent(slimReport, loggedUser.getId());
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity<String> updateSlimReport(FormSlimReportDTO options, ILoggedUser loggedUser) throws QMSException {
        Map<String, Object> reportSlimReportInfo = HQL_findSimpleMap(" "
                + " SELECT new map("
                    + " d.surveyId AS surveyId "
                    + ",dq.id AS queryId "
                    + ",r.id AS reportId "
                    + ",r.nodeId AS nodeId "
                    + ",r.countPrintFormats AS countPrintFormats "
                + " )"
                + " FROM " + SlimReports.class.getCanonicalName() + " sr "
                + " LEFT JOIN " + Document.class.getCanonicalName() + " d"
                        + " ON "
                            + " d.masterId = sr.documentMasterId "
                            + " AND d.deleted = 0 "
                            + " AND d.status IN ("
                                + Document.STATUS.ACTIVE.getValue()
                                + "," + Document.STATUS.IN_EDITION.getValue()
                            + " ) "
                + " LEFT JOIN " + Report.class.getCanonicalName() + " r on r.id = sr.reportId "
                + " LEFT JOIN " + DatabaseQuery.class.getCanonicalName() + " dq ON dq.id = r.query.id "
                + " WHERE sr.id = :slimReportId ",
                ImmutableMap.of("slimReportId", options.getId()),
                true,
                CacheRegion.REPORT,
                0
        );

        //Se elimina la consulta previa y su reporte
        Long surveyId = (Long) reportSlimReportInfo.get("surveyId");
        Long oldReportId = (Long) reportSlimReportInfo.get("reportId");
        Long oldQueryId = (Long) reportSlimReportInfo.get("queryId");
        Long oldNodeId = (Long) reportSlimReportInfo.get("nodeId");
        Integer oldCountPrintFormats = (Integer) reportSlimReportInfo.get("countPrintFormats");
        deleteReportAndQuery(oldReportId, oldQueryId, loggedUser);
        //Se genera nueva consulta y reporte
        Report report = new Report(-1L);
        report.setCountPrintFormats(oldCountPrintFormats);
        report.setType(ReportType.SLIM_REPORT.getValue());
        report.setLocalTimeForDates(options.getLocalTimeForDates());
        //Se consultan los accesos de la version anterior y se establecen al nuevo reporte
        if (oldNodeId != null) {
            report.setNodeId(oldNodeId);
            saveNodeAccess(oldNodeId, options, loggedUser);
        }
        GenericSaveHandle gsh = generateReportForSlimReport(surveyId, report, options, true, loggedUser);
        if (gsh.isSuccess()) {
            //Se actualiza el slim report
            SlimReports slimReport = HQLT_findById(SlimReports.class, options.getId());
            deleteSlimReportSurveyFields(slimReport.getId());
            saveSlimReportData(options, loggedUser, report.getId(), slimReport);
            return new ResponseEntity<>(HttpStatus.CREATED);
        }
        return new ResponseEntity<>(gsh.getErrorMessage(), HttpStatus.CONFLICT);
    }

    private void saveNewSlimReportTableColumns(
           @Nonnull final Long databaseQueryId,
           @Nonnull final Long reportId,
           @Nonnull final Long surveyId,
           @Nonnull final Long slimReportId,
           @Nonnull final String slimReportCode,
           @Nonnull final String documentMasterId,
           @Nonnull final String excelIdFields,
           @Nonnull final Set<SlimReportsGhostField> changedTo,
           @Nonnull final ILoggedUser loggedUser
    ) throws QMSException {
        final Set<SlimReportsGhostField> changedFrom = new HashSet<>(HQLT_findByQuery(SlimReportsGhostField.class, " "
            + " SELECT f"
            + " FROM " +  SlimReportsGhostField.class.getCanonicalName() + " f "
            + " WHERE f.slimReport.id = :slimReportId"
                , ImmutableMap.of("slimReportId", slimReportId)
                , true, CacheRegion.REPORT, 0
            )
        );
        final Set<SlimReportsGhostField> newFields = changedTo.stream().filter(to ->
            changedFrom.stream().noneMatch(from -> from.getCode().equals(to.getCode()))
        ).collect(Collectors.toSet());
        final String tableName = FormBulkHandler.getNewBulkTableName(documentMasterId, slimReportCode);
        final IDynamicTableDAO<Integer, IReportColumnDTO> dao = getBean(IDynamicTableDAO.class);
        final ITableFactory<Integer, IReportColumnDTO> factory = getBulkReportTableFactory(databaseQueryId, reportId, surveyId, slimReportId, documentMasterId, excelIdFields, slimReportCode, loggedUser);
        final List<String> columns = dao.getDynamicTableColumns(factory.getInstanceSchema() + "." + factory.getInstanceTableName());
        newFields.forEach(f -> {
            if (columns.contains(f.getCode())) {
                // Se excluye crear columnas que ya existen (ISSUE-7406)
                return;
            }
            ReportColumnDTO column = new ReportColumnDTO(
                    reportId,
                    slimReportId,
                    f.getCode(),
                    f.getDescription(),
                    true,
                    ReportColumnType.TEXT,f.getOrder()
            );
            dao.addColumn(factory, tableName, column);
        });
    }

    private void saveNodeAccess(Long nodeId, FormSlimReportDTO options, ILoggedUser loggedUser) {
        NodeAccess node = (NodeAccess) HQLT_findById(NodeAccess.class, nodeId);
        if (options.getBusinessUnitAccessValues() != null) {
            node.setBusinessUnits(options.getBusinessUnitAccessValues().stream()
                    .map(BusinessUnitLite::new)
                    .collect(Collectors.toSet()));
        }
        if (options.getBusinessUnitDepartmentAccessValues() != null) {
            node.setDepartments(options.getBusinessUnitDepartmentAccessValues().stream()
                    .map(BusinessUnitDepartmentLite::new)
                    .collect(Collectors.toSet()));
        }
        if (options.getProcessAccessValues() != null) {
            node.setProcess(options.getProcessAccessValues().stream()
                    .map(DPMS.Mapping.Process::new)
                    .collect(Collectors.toSet()));
        }
        if (options.getUserAccessValues() != null) {
            node.setUsers(options.getUserAccessValues().stream()
                    .map(UserRef::new)
                    .collect(Collectors.toSet()));
        }
        makePersistent(node, loggedUser.getId());
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity deleteSlimReport(
            Long reportId,
            ILoggedUser loggedUser
    ) {
        Map<String, Integer> updateResults = new HashMap<>();
        final Integer deletedSlimReport = HQL_updateByQuery("" +
                "UPDATE " + SlimReports.class.getCanonicalName() + " sr" +
                " SET sr.deleted = 1 " +
                " WHERE sr.reportId = :reportId",
                ImmutableMap.of("reportId", reportId)
        );
        updateResults.put("deletedSlimReport", deletedSlimReport);
        final Integer deletedReport = HQL_updateByQuery(" "
                + " UPDATE " + Report.class.getCanonicalName() + " r  "
                + " set r.deleted = 1 "
                + " WHERE r.id = :reportId ",
                ImmutableMap.of("reportId", reportId),
                true, CacheRegion.REPORT, 0
        );
        if (deletedReport > 0) {
            IUserRefDAO links = getBean(IUserRefDAO.class);
            links.cancelFavoritesByReportId(reportId, loggedUser);
        }
        updateResults.put("deletedReport", deletedReport);
        final Integer deletedQuery = HQL_updateByQuery(""
                + " UPDATE " + DatabaseQuery.class.getCanonicalName() + " q  "
                + " set q.deleted = 1 "
                + " WHERE q.id IN (" +
                    " SELECT " +
                        " r.queryId " +
                    " FROM " + Report.class.getCanonicalName() +
                    " r WHERE r.id = :reportId" +
                ") ",
                ImmutableMap.of("reportId", reportId),
                true, CacheRegion.REPORT, 0
        );
        updateResults.put("deletedQuery", deletedQuery);
        if (deletedReport > 0 && deletedSlimReport > 0 && deletedQuery > 0) {
            return new ResponseEntity(updateResults, HttpStatus.OK);
        }
        return new ResponseEntity(updateResults, HttpStatus.NOT_FOUND);
    }

    private ITableFactory<Integer, IReportColumnDTO> getBulkReportTableFactory(
            final Long databaseQueryId,
            final Long reportId,
            final Long surveyId,
            final Long slimReportId,
            final String documentMasterId,
            final String excelIdFields,
            final String slimReportCode,
            final ILoggedUser loggedUser) throws QMSException {
        return IReportBulkBaseHandler.getTableFactory(new FormBulkHandler(
                loggedUser.getLocale(),
                databaseQueryId,
                reportId,
                surveyId,
                documentMasterId,
                excelIdFields,
                slimReportId,
                slimReportCode,
                loggedUser
        ).getUploader(), loggedUser.getId());

    }

    private boolean createDynamicTableIfNotExists(
            final Long databaseQueryId,
            final Long reportId,
            final Long surveyId,
            final Long slimReportId,
            final String documentMasterId,
            final String excelIdFields,
            final String slimReportCode,
            final ILoggedUser loggedUser) throws QMSException {
        ITableFactory<Integer, IReportColumnDTO> factory = getBulkReportTableFactory(databaseQueryId, reportId, surveyId, slimReportId, documentMasterId, excelIdFields, slimReportCode, loggedUser);
        return IReportBulkBaseHandler.createDynamicTableIfNotExists(
                factory,
                loggedUser.getId()
        ) != null;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity newSlimReport(
            FormSlimReportDTO options,
            ILoggedUser loggedUser
    ) throws QMSException {
        final Long surveyId = getActiveSurveyId(options.getDocumentMasterId());
        final INodeAccessDAO nodeDao = getBean(INodeAccessDAO.class);
        final Report report = new Report(-1L);
        final boolean isNewSlimReport = options.getId() == null || options.getId() == -1L;
        report.setType(ReportType.SLIM_REPORT.getValue());
        SlimReports slimReport = new SlimReports();

        if (isNewSlimReport) {
            slimReport.setId(-1L);
            slimReport.setDeleted(true);
            saveSlimReportData(options, loggedUser, null, slimReport);
            report.setCode(slimReport.getCode());
        }

        GenericSaveHandle saved = generateReportForSlimReport(surveyId, report, options, loggedUser);
        if (saved.getOperationEstatus() == 1) {
            if (report.getId() != -1L && isNewSlimReport) {
                slimReport.setDeleted(false);
                slimReport.setReportId(report.getId());
                makePersistent(slimReport, loggedUser.getId());
                if (createDynamicTableIfNotExists(
                        report.getQueryId(),
                        report.getId(),
                        surveyId,
                        slimReport.getId(),
                        options.getDocumentMasterId(),
                        options.getExcelIdFields(),
                        options.getCode(),
                        loggedUser)) {
                    // Probar query con tabla dinamica
                    final String connectionType = "bnext-qms";
                    final SessionImpl sessionImpl = (SessionImpl) getSession();
                    final Connection connection = sessionImpl.connection();
                    final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();
                    final QueryTestDto queryTest = queryHandler.testQueryWithConnection(
                            connectionType,
                            connection,
                            report.getQuery().getQueryStatement(),
                            null,
                            loggedUser
                    );
                    if (queryTest.getColumns() == null) {
                        throw new QMSException("Invalid dynamic table for bulk load");
                    }
                }
            }
            // Se guardan permisos
            NodeAccess node = nodeDao.HQLT_findById(report.getNodeId());
            node.setBusinessUnits(options.getBusinessUnitAccessValues().stream()
                    .map(BusinessUnitLite::new)
                    .collect(Collectors.toSet()));
            node.setDepartments(options.getBusinessUnitDepartmentAccessValues().stream()
                    .map(BusinessUnitDepartmentLite::new)
                    .collect(Collectors.toSet()));
            node.setProcess(options.getProcessAccessValues().stream()
                    .map(DPMS.Mapping.Process::new).
                    collect(Collectors.toSet()));
            node.setUsers(options.getUserAccessValues().stream()
                    .map(UserRef::new)
                    .collect(Collectors.toSet()));
            makePersistent(node, loggedUser.getId());
            IPrintingFormatDAO dao = Utilities.getBean(IPrintingFormatDAO.class);
            dao.updateCountEntity(options.getDocumentMasterId(), loggedUser);
        }
        if (options.getDocumentMasterId() != null) {
            IPrintingFormatDAO printDao = getBean(IPrintingFormatDAO.class);
            printDao.updateCountEntity(options.getDocumentMasterId(), loggedUser);
        }
        return new ResponseEntity(HttpStatus.OK);
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Long getActiveSurveyId(String documentMasterId) {
        return HQL_findLong(" "
                + " SELECT d.surveyId "
                + " FROM " + Document.class.getCanonicalName() + " d "
                + " WHERE "
                    + " d.masterId = :documentMasterId "
                    + " AND d.deleted = 0 "
                    + " AND d.status IN ("
                            + Document.STATUS.ACTIVE.getValue()
                            + "," + Document.STATUS.IN_EDITION.getValue()
                        + " ) ",
                ImmutableMap.of("documentMasterId", documentMasterId)
        );
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Long migrateSlimReportDefault(FormSlimReportDTO options, Long nodeId, ILoggedUser loggedUser) throws QMSException {
        final Report report = new Report(-1L);
        report.setType(ReportType.SLIM_REPORT.getValue());
        final Long surveyId = getActiveSurveyId(options.getDocumentMasterId());
        GenericSaveHandle saved = generateReportForSlimReport(surveyId, report, options, loggedUser);
        if (nodeId != null && report.getNodeId() != null && saved.getOperationEstatus() == 1) {
            NodeAccess referenceMigratedNodeAccess = (NodeAccess) HQLT_findById(NodeAccess.class, nodeId);
            NodeAccess newReportNodeAccess = (NodeAccess) HQLT_findById(NodeAccess.class, report.getNodeId());
            newReportNodeAccess.setBusinessUnits(referenceMigratedNodeAccess.getBusinessUnits());
            newReportNodeAccess.setDepartments(referenceMigratedNodeAccess.getDepartments());
            newReportNodeAccess.setUsers(referenceMigratedNodeAccess.getUsers());
            newReportNodeAccess.setProcess(referenceMigratedNodeAccess.getProcess());
            makePersistent(newReportNodeAccess, loggedUser.getId());
        }
        if (saved.getOperationEstatus() == 1) {
            if (report.getId() != -1L) {
                SlimReports slimReports = new SlimReports();
                slimReports.setId(-1L);
                return saveSlimReportData(options, loggedUser, report.getId(), slimReports);
            } else {
                return 0L;
            }
        } else {
            getLogger().error(
                    "Failed to migrate access to slim report for document {}, Error: {}",
                    options.getDocumentMasterId(), saved.getErrorMessage());
            return 0L;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean repeatedSlimReportName(String slimReportName) {
        if (slimReportName == null || slimReportName.trim().isEmpty()) {
            return false;
        }
        Map params = new HashMap();
        params.put("slimReportName", slimReportName);
        return HQL_findSimpleInteger(""
                + "SELECT "
                + "COUNT(sl.id) "
                + "FROM " + SlimReports.class.getCanonicalName() + " sl "
                + "WHERE sl.description = :slimReportName",
                params
        ) >= 1;
    }

    private GenericSaveHandle generateReportForSlimReport(
            Long surveyId,
            Report reportInstance,
            FormSlimReportDTO options,
            ILoggedUser loggedUser
    ) throws QMSException {
        return generateReportForSlimReport(surveyId, reportInstance, options, false, loggedUser);
    }

    private GenericSaveHandle generateReportForSlimReport(
            Long surveyId,
            Report reportInstance,
            FormSlimReportDTO options,
            boolean alterTableNewColumns,
            ILoggedUser loggedUser
    ) throws QMSException {
        final IReportDAO reportDao = getBean(IReportDAO.class);
        reportInstance.setCode(options.getCode());
        reportInstance.setDocumentMasterId(options.getDocumentMasterId());
        reportInstance.setDetails(options.getDetails());
        reportInstance.setDescription(options.getDescription());
        reportInstance.setModule(Module.FORMULARIE.getKey());
        final Set<ReportColumn> columns = getParsedSlimReportColumns(options, reportInstance, loggedUser);
        GenericSaveHandle saved = reportDao.newReport(reportInstance, columns, loggedUser);
        if (saved.getOperationEstatus() != 1) {
            return saved;
        }
        if (reportInstance.getNodeId() != null) {
            saveNodeAccess(reportInstance.getNodeId(), options, loggedUser);
        }
        if (alterTableNewColumns && options.getId() != null && options.getId() != -1L) {
            saveNewSlimReportTableColumns(
                    reportInstance.getQueryId(),
                    reportInstance.getId(),
                    surveyId,
                    options.getId(),
                    options.getCode(),
                    options.getDocumentMasterId(),
                    options.getExcelIdFields(),
                    options.getGhostFields(),
                    loggedUser
            );
        }
        return saved;
    }

    private Long saveSlimReportData(
            FormSlimReportDTO options,
            ILoggedUser loggedUser,
            Long reportId,
            SlimReports slimReport
    ) throws QMSException {
        final String masterId = options.getDocumentMasterId();
        final Long surveyId = FormUtil.getSurveyId(masterId);
        final IReportDAO reportDao = getBean(IReportDAO.class);

        if (options.getId() == -1L) {
            final ICodeSequenceDAO sq = (ICodeSequenceDAO) getBean("CodeSequence");
            options.setCode("SREPORT-" + Utilities.todayDateBy("yy") + sq.next(CodeSequence.type.NONE));
            slimReport.setCreatedDate(new Date());
            slimReport.setCreatedBy(loggedUser.getId());
        }
        Set<SlimReportsTransformedField> oldTransformedFields = slimReport.getTransformedFields();
        slimReport.setCode(options.getCode());
        slimReport.setDocumentMasterId(masterId);
        slimReport.setDescription(options.getDescription());
        slimReport.setLastModifiedBy(loggedUser.getId());
        slimReport.setLastModifiedDate(new Date());
        slimReport.setReportId(reportId);
        slimReport.setDetails(options.getDetails());
        slimReport.setLocalTimeForDates(options.getLocalTimeForDates());
        slimReport.setRestrictRecordsByDepartment(options.getRestrictRecordsByDepartment());
        slimReport.setWhereFillerUserParticipate(options.getWhereFillerUserParticipate());
        slimReport.setDeleted(false);
        slimReport.setExcelUploadEnabled(Boolean.TRUE.equals(options.getExcelUploadEnabled()));
        slimReport.setExcelIdFields(options.getExcelIdFields());
        // order
        final Map<String, SlimReportOrderColumnsDTO> orderColumns = mapSlimReportColumns(options.getOrderColumns());
        final Optional<Integer> maxOrderOptional = orderColumns.values().stream().map(SlimReportOrderColumnsDTO::getOrder).max(Comparator.naturalOrder());
        final AtomicInteger maxOrder = maxOrderOptional
                .map(integer -> new AtomicInteger(integer + 1))
                .orElseGet(() -> new AtomicInteger(0));
        // survey filters
        slimReport.setSurveysFilters(
                getSurveyFields(
                        SlimReportsSurveyFieldsType.FILTER,
                        options.getSurveyFilters(),
                        surveyId,
                        reportDao,
                        orderColumns,
                        maxOrder,
                        slimReport
                )
        );
        // fixed filters
        slimReport.setSurveyFiltersFixed(
                getSurveyFieldsFixed(
                        SlimReportsSurveyFieldsFixedType.FILTER,
                        options.getFixedFilters(),
                        orderColumns,
                        maxOrder,
                        slimReport
                )
        );
        // survey fields
        slimReport.setSurveysFields(
                getSurveyFields(
                        SlimReportsSurveyFieldsType.FIELD,
                        options.getSurveyFields(),
                        surveyId,
                        reportDao,
                        orderColumns,
                        maxOrder,
                        slimReport
                )
        );
        // ghosts fields
        slimReport.setGhostFields(
            getGhostFields(options.getGhostFields(), orderColumns, loggedUser)
        );
        // ghosts fields
        slimReport.setTransformedFields(
            getTransformedFields(options.getTransformedFields(), orderColumns, loggedUser)
        );
        // related fields
        slimReport.setRelatedFields(
            getRelatedFields(options, surveyId, reportDao, slimReport)
        );
        // fixed fields
        slimReport.setSurveyFieldsFixed(
                getSurveyFieldsFixed(
                        SlimReportsSurveyFieldsFixedType.FIELD,
                        options.getFixedFields(),
                        orderColumns,
                        maxOrder,
                        slimReport
                )
        );
        // save!
        makePersistent(slimReport, loggedUser.getId());
        saveTransformedFieldDiff(slimReport,oldTransformedFields, loggedUser);
        return 1L;
    }

    private void saveTransformedFieldDiff(SlimReports slimReport, Set<SlimReportsTransformedField> oldTransformedFields, ILoggedUser loggedUser) {
        Set<SlimReportsTransformedField> currentTransformedFields = slimReport.getTransformedFields();
        Set<SlimReportsTransformedFieldHistory> history = new LinkedHashSet<>();
        Long userId = loggedUser.getId();
        for (SlimReportsTransformedField currentTransformedField : currentTransformedFields) {
            for (SlimReportsTransformedField oldTransformedField : oldTransformedFields) {
                if (currentTransformedField.getCode().equals(oldTransformedField.getCode())) {
                    boolean titleChanged = !currentTransformedField.getDescription().equals(oldTransformedField.getDescription());
                    List<SlimReportsTransformedFieldRule> currentRules = currentTransformedField.getRules();
                    List<SlimReportsTransformedFieldRule> oldRules = oldTransformedField.getRules();
                    int previousRuleCount = oldRules.size(), currentRuleCount = currentRules.size(), ruleDiffCountCount, sameRuleCount = 0;
                    for (int i = 0; i < currentRules.size(); i++) {
                        if (oldRules.size() > i && currentRules.get(i).changelogEquals(oldRules.get(i))) {
                            sameRuleCount++;
                        }
                    }
                    ruleDiffCountCount = Math.abs(sameRuleCount - currentRuleCount);
                    if (titleChanged || ruleDiffCountCount > 0 || previousRuleCount != currentRuleCount) {
                        SlimReportsTransformedFieldHistory fieldHistory = new SlimReportsTransformedFieldHistory(
                                slimReport, oldTransformedField, currentTransformedField, previousRuleCount,
                                currentRuleCount, ruleDiffCountCount, sameRuleCount, SlimReportsTransformedFieldHistory.UPDATED,userId);
                        history.add(fieldHistory);
                    }
                }
            }
        }
        for (SlimReportsTransformedField oldTransformedField : oldTransformedFields) {
            if (currentTransformedFields.stream().noneMatch(currentTransformedField -> currentTransformedField.getCode().equals(oldTransformedField.getCode()))){
                // Field has been deleted
                SlimReportsTransformedFieldHistory fieldHistory = new SlimReportsTransformedFieldHistory(
                        slimReport, null, oldTransformedField, oldTransformedField.getRules().size(),
                        0,  oldTransformedField.getRules().size(), 0, SlimReportsTransformedFieldHistory.DELETED,userId);
                history.add(fieldHistory);
            }
        }
        for (SlimReportsTransformedField currentTransformedField : currentTransformedFields) {
            if (oldTransformedFields.stream().noneMatch(oldTransformedField -> currentTransformedField.getCode().equals(oldTransformedField.getCode()))) {
                // Field has been added
                SlimReportsTransformedFieldHistory fieldHistory = new SlimReportsTransformedFieldHistory(
                        slimReport, null, currentTransformedField, 0, currentTransformedField.getRules().size(), 0, 0, SlimReportsTransformedFieldHistory.CREATED,userId);
                history.add(fieldHistory);
            }
        }
        for (SlimReportsTransformedFieldHistory fieldHistory : history) {
            makePersistent(fieldHistory, loggedUser.getId());
        }
    }

    private Set<SlimReportsRelatedField> getRelatedFields(
            FormSlimReportDTO options,
            Long surveyId,
            IReportDAO reportDao,
            SlimReports slimReport) {
        final Set<SlimReportsRelatedField> relatedFields = new LinkedHashSet<>();
        if (options.getRelatedFields() != null && !options.getRelatedFields().isEmpty()) {
            options.getRelatedFields().forEach(field -> {
                final Long fieldId = FormUtil.getSlimReportSurveyFieldsId(field, surveyId, reportDao);
                relatedFields.add(new SlimReportsRelatedField(-1L, fieldId, field, slimReport));
            });
        }
        return relatedFields;
    }
    private Set<SlimReportsSurveyFieldsFixed> getSurveyFieldsFixed(
            SlimReportsSurveyFieldsFixedType type,
            Set<Integer> fixedFields,
            Map<String, SlimReportOrderColumnsDTO> orderColumns,
            AtomicInteger maxOrder,
            SlimReports slimReport
    ) {
        if (fixedFields == null || fixedFields.isEmpty()) {
            return new LinkedHashSet<>();
        }
        Set<SlimReportsSurveyFieldsFixed> surveyFieldsFixed = new LinkedHashSet<>();
        fixedFields.forEach(field -> {
            SlimReportOrderColumnsDTO dto = orderColumns.get(field.toString());
            Integer fieldOrder = null;
            String alias = null;
            if (dto != null) {
                fieldOrder = dto.getOrder();
                alias = dto.getAlias();
            }
            if (fieldOrder == null) {
                fieldOrder = maxOrder.getAndIncrement();
            }
            surveyFieldsFixed.add(
                    new SlimReportsSurveyFieldsFixed(
                            -1L,
                            slimReport,
                            field,
                            fieldOrder,
                            type.getValue(),
                            alias
                    )
            );
        });
        return surveyFieldsFixed;
    }

    private Set<SlimReportsGhostField> getGhostFields(Set<SlimReportsGhostField> ghostFields, Map<String, SlimReportOrderColumnsDTO> orderColumns, ILoggedUser loggedUser) {
        Set<SlimReportsGhostField> ghostFieldSet = new LinkedHashSet<>();
        ghostFields.forEach(field -> {
            SlimReportOrderColumnsDTO col = orderColumns.get(field.getCode());
            ghostFieldSet.add(new SlimReportsGhostField(field, -1L, loggedUser.getId(), new Date(), col.getAlias(), col.getOrder()));
        });
        return ghostFieldSet;
    }

    private Set<SlimReportsTransformedField> getTransformedFields(Set<SlimReportsTransformedField> transformedFields, Map<String, SlimReportOrderColumnsDTO> orderColumns, ILoggedUser loggedUser) {
        Set<SlimReportsTransformedField> transformedFieldsSet = new LinkedHashSet<>();
        transformedFields.forEach(field -> {
            SlimReportOrderColumnsDTO orderAlias = orderColumns.get(field.getCode());
            String alias = null;
            Integer order = null;
            if (orderAlias != null) {
                alias = orderAlias.getAlias();
                order = orderAlias.getOrder();
            }
            transformedFieldsSet.add(new SlimReportsTransformedField(
                    field,
                    -1L,
                    loggedUser.getId(),
                    new Date(),
                    alias,
                    order
            ));
        });
        return transformedFieldsSet;
    }

    private Set<SlimReportsSurveyFields> getSurveyFields(
            SlimReportsSurveyFieldsType type,
            List<String> surveyFieldsList,
            Long surveyId,
            IReportDAO reportDao,
            Map<String, SlimReportOrderColumnsDTO> orderColumns,
            AtomicInteger maxOrder,
            SlimReports slimReport
    ) {
        if (surveyFieldsList == null || surveyFieldsList.isEmpty()) {
            return new LinkedHashSet<>();
        }
        Set<SlimReportsSurveyFields> surveyFields = new LinkedHashSet<>();
        surveyFieldsList.forEach(field -> {
            final Long fieldId = FormUtil.getSlimReportSurveyFieldsId(field, surveyId, reportDao);
            SlimReportOrderColumnsDTO dto = orderColumns.get(field);
            Integer fieldOrder = null;
            String alias = null;
            if (dto != null) {
                fieldOrder = dto.getOrder();
                alias = dto.getAlias();
            }
            if (fieldOrder == null) {
                fieldOrder = maxOrder.getAndIncrement();
            }
            surveyFields.add(
                    new SlimReportsSurveyFields(
                            -1L,
                            fieldId,
                            field,
                            fieldOrder,
                            type.getValue(),
                            slimReport,
                            alias
                    )
            );
        });
        return surveyFields;
    }

    private String getParsedOtherSystemColumnsQuery(
            String sql,
            @Nonnull String documentMasterId,
            @Nonnull String slimReportCode,
            FormSlimReportDTO options
    ) throws QMSException {
        StringBuilder selectToken = new StringBuilder();
        for (SlimReportsGhostField ghostField : options.getGhostFields()) {
            if (SQLHandler.isValidSqlColumnName(ghostField.getCode())) {
                selectToken.append(", fbh.").append(ghostField.getCode()).append(" AS ").append(ghostField.getCode());
            } else {
                throw new QMSException("Invalid column name: " + ghostField.getCode());
            }
        }
        selectToken.append(SurveyUtil.EXTRA_COLUMNS_TOKEN);
        String joinToken = " LEFT JOIN " + FormBulkHandler.SCHEMA + "." + FormBulkHandler.getNewBulkTableName(documentMasterId, slimReportCode) + " fbh "
                + " ON fbh.outstanding_surveys_id = " + SurveyUtil.SURVEY_FIELD_ALIAS + ".outstanding_surveys_id1 "
                + SurveyUtil.EXTRA_JOINS_TOKEN;
        return sql
                .replace(SurveyUtil.EXTRA_COLUMNS_TOKEN, selectToken.toString())
                .replace(SurveyUtil.EXTRA_JOINS_TOKEN, joinToken);
    }

    @Nonnull
    private String getParsedTransformedColumnsQuery(
            String sql,
            FormSlimReportDTO options
    ) throws QMSException {
        final StringBuilder selectToken = new StringBuilder();
        final Set<String> uniques = new HashSet<>();
        for (SlimReportsTransformedField transformedField : options.getTransformedFields()) {
            if (!SQLHandler.isValidSqlColumnName(transformedField.getCode())) {
                throw new QMSException("Invalid column name: " + transformedField.getCode());
            }
            // columna fija donde se aplicará la regla
            selectToken.append(", '").append(transformedField.getCode()).append("' AS ").append(transformedField.getCode());
            // columnas necesarias para evaluar la regla
            for (SlimReportsTransformedFieldRule rule : transformedField.getRules()) {
                if (uniques.contains(rule.getEvalFieldCode())) {
                    continue;
                }
                uniques.add(rule.getEvalFieldCode());
                switch (rule.getEvalFieldSourceType()) {
                    case FIXED:
                        selectToken.append(", o.").append(rule.getEvalFieldCode()).append(" AS ").append(rule.getEvalFieldCode());
                        break;
                    case TRANSFORMED:
                        // empty
                        break;
                    case GHOST:
                        selectToken.append(", fbh.").append(rule.getEvalFieldCode()).append(" AS ").append(rule.getEvalFieldCode());
                        break;
                    case FLEXI:
                        selectToken.append(", ")
                                .append(SurveyUtil.SURVEY_FIELD_ALIAS)
                                .append(".")
                                // TO-DO: Revisar que funcionen los alias correctos `FixedField.OUTSTANDING_SURVEYS.getAlias()`
                                .append(rule.getEvalFieldCode()).append(" AS ").append(rule.getEvalFieldCode());
                        break;
                }
            }
        }
        selectToken.append(SurveyUtil.EXTRA_COLUMNS_TOKEN);
        return sql.replace(SurveyUtil.EXTRA_COLUMNS_TOKEN, selectToken.toString());
    }

    @Nonnull
    private List<String> getSlimSurveyFields(FormSlimReportDTO options) {
        List<String> surveyFields = options.getSurveyFields();
        final Integer maxResults = Utilities.getSettings().getFormReportMaxColumns();
        final int columnsSize = options.getFixedFields().size() + surveyFields.size();
        if (!options.isTruncateColumnsToMax() && maxResults > 0 && columnsSize > maxResults) {
            final String error = "Max number of columns exceed. Currently there are " + columnsSize + " columns of " + maxResults + " allowed";
            throw new ExplicitRollback(error);
        } else if (maxResults > 0 && columnsSize > maxResults) {
            final int maxSurveyFields = maxResults - options.getFixedFields().size();
            if (maxSurveyFields > 0) {
                surveyFields = surveyFields.subList(0, maxSurveyFields);
            } else {
                surveyFields = surveyFields.subList(0, maxResults);
            }
        }
        return surveyFields;
    }

    @Nonnull
    private List<String> getSlimSurveyFilters(FormSlimReportDTO options) {
        final Integer maxResults = Utilities.getSettings().getFormReportMaxColumns();
        List<String> surveyFilters = options.getSurveyFilters();
        if (surveyFilters == null) {
            surveyFilters = new ArrayList<>();
        }
        Set<Integer> fixedFilters = options.getFixedFilters();
        if (fixedFilters == null) {
            fixedFilters = new LinkedHashSet<>();
        }
        final int filtersSize = fixedFilters.size() + surveyFilters.size();
        if (!options.isTruncateColumnsToMax() && maxResults > 0 && filtersSize > maxResults) {
            final String error = "Max number of filters exceed."
                    + " Currently there are " + filtersSize + " filters of " + maxResults + " allowed";
            throw new ExplicitRollback(error);
        } else if (maxResults > 0 && filtersSize > maxResults) {
            final int maxSurveyFilters = maxResults - fixedFilters.size();
            if (maxSurveyFilters > 0) {
                surveyFilters = surveyFilters.subList(0, maxSurveyFilters);
            } else {
                surveyFilters = surveyFilters.subList(0, maxResults);
            }
        }
        return surveyFilters;
    }

    private Set<ReportColumn> getParsedSlimReportColumns(
            FormSlimReportDTO options,
            Report report,
            ILoggedUser loggedUser
    ) throws QMSException {
        final Integer maxResults = Utilities.getSettings().getFormReportMaxColumns();
        final String masterId = options.getDocumentMasterId();
        final String surveyAnswersTable = SurveyUtil.ANSWERS_TABLE_PREFFIX + masterId.replaceAll("-", "");
        // se construye consulta de prueba
        final QueryTestDto queryTest = getTestData(
                masterId,
                surveyAnswersTable,
                options,
                loggedUser
        );
        Set<QueryColumn> testColumns = queryTest.getColumns();
        if (testColumns == null) {
            return new LinkedHashSet<>();
        }
        final List<String> surveyFields = getSlimSurveyFields(options);
        final int columnsSize = options.getFixedFields().size() + surveyFields.size();
        final List<ReportColumn> columns = new ArrayList<>(columnsSize);
        final Long surveyId = FormUtil.getSurveyId(masterId);
        final IReportDAO reportDao = getBean(IReportDAO.class);
        final List<Long> fieldIds = FormUtil.getSlimReportSurveyFieldsIds(surveyId, surveyFields, maxResults, reportDao);
        List<String> surveyFilters = getSlimSurveyFilters(options);
        final List<Long> filterIds = FormUtil.getSlimReportSurveyFieldsIds(surveyId, surveyFilters, maxResults, reportDao);
        final DatabaseQuery query = new DatabaseQuery();
        final ICodeSequenceDAO sq = (ICodeSequenceDAO) getBean("CodeSequence");
        query.setCode("QRY-" + Utilities.todayDateBy("yy") + sq.next(CodeSequence.type.NONE));
        query.setType(DatabaseQueryType.SLIM_REPORT.getValue());
        query.setEnableHierarchy(false);
        query.setColumns(testColumns);
        query.setRangeFilterType(RangeFilterType.NONE.getValue());
        query.setSource(CONNECTION_TYPE);
        query.setDatabaseConnection(null);
        query.setDescription(options.getDescription() + "' " + new Date().getTime());
        query.setModule(Module.FORMULARIE.getKey());
        // se agregan tokens de valores de otros sistemas
        String sql = ISlimReportsDAO.getParsedSlimReportQuery(
                masterId,
                surveyAnswersTable,
                options,
                true,
                true,
                false
        );
        if (!options.getGhostFields().isEmpty() && options.getCode() != null) {
            sql = getParsedOtherSystemColumnsQuery(sql, options.getDocumentMasterId(), options.getCode(), options);
            query.setQueryStatement(sql.replace("TOP(1)", " "));
        } else {
            query.setQueryStatement(sql.replace("TOP(1)", " "));
        }
        if (!options.getTransformedFields().isEmpty()) {
            sql = getParsedTransformedColumnsQuery(sql, options);
            query.setQueryStatement(sql.replace("TOP(1)", " "));
        } else {
            query.setQueryStatement(sql.replace("TOP(1)", " "));
        }
        query.setEditableHierarchy(true);
        query.setSurveyId(surveyId);
        // business_unit
        query.setHasBusinessUnitKey(false);
        query.setBusinessUnitKeyCode(null);
        query.setBusinessUnitKeyId(null);
        query.setBusinessUnitLabelCode(null);
        query.setBusinessUnitLabelId(null);
        // business_unit_department
        query.setHasBusinessUnitDepartmentKey(false);
        query.setBusinessUnitDepartmentKeyCode(null);
        query.setBusinessUnitDepartmentKeyId(null);
        query.setBusinessUnitDepartmentLabelCode(null);
        query.setBusinessUnitDepartmentLabelId(null);
        // area
        query.setHasAreaKey(false);
        query.setAreaKeyCode(null);
        query.setAreaKeyId(null);
        query.setAreaLabelCode(null);
        query.setAreaLabelId(null);
        query.setComputedData(false);
        query.setReadonly(true);
        for (QueryColumn qColumn : query.getColumns()) {
            qColumn.setQuery(query);
        }
        DatabaseQuery querySaved = makePersistent(query, loggedUser.getId());
        DatabaseQuery databaseQueryRef = new DatabaseQuery(querySaved.getId(), querySaved.getQueryStatement());
        report.setQuery(databaseQueryRef);
        // se construye reporte
        final FieldConfigDTO fieldsConfig = SurveyUtil.getFields(
                surveyId, 
                false, 
                false, 
                false, 
                fieldIds, 
                maxResults, 
                loggedUser,
                true,
                true,
                true
        );
        final Set<SurveyDataFieldDTO> fields = fieldsConfig.getFields();
        if (fields == null || fields.isEmpty()) {
            return new LinkedHashSet<>();
        }
        final FieldConfigDTO filtersConfig = SurveyUtil.getFields(
                surveyId,
                false,
                false,
                false,
                filterIds,
                maxResults,
                loggedUser,
                true,
                true,
                true
        );
        final Set<SurveyDataFieldDTO> filters = filtersConfig.getFields();
        final List<String> fixedFieldsAliases = Lists.reverse(FixedField.getAliases(options.getFixedFields()));
        final List<String> fixedFiltersAliases = Lists.reverse(FixedField.getAliases(options.getFixedFilters()));
        Map<String, SlimReportOrderColumnsDTO> mappedSlimReportColumns = mapSlimReportColumns(options.getOrderColumns().stream()
                .filter(Objects::nonNull)
                .filter(column -> column.getOrder() != null)
                .collect(Collectors.toList()));
        final Map<String, Integer> orderColumns = mappedSlimReportColumns.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, c -> c.getValue().getOrder()));
        final Optional<Integer> maxOrderOptional = orderColumns.values().stream()
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder());
        final AtomicInteger maxOrder = maxOrderOptional
                .map(integer -> new AtomicInteger(integer + 1)).orElseGet(() -> new AtomicInteger(0));
        final List<ReportColumn> mandatoryColumns = new ArrayList<>(6);
        final Set<String> addedColumns = new LinkedHashSet<>();
        for (QueryColumn column : testColumns) {
            if (SurveyUtil.isCatalogHierarchyOrSystemColumn(column)) {
                ReportColumn idColumn = new ReportColumn(-1L);
                final String description = getTag("fixedField." + column.getCode());
                if (description != null && !description.isEmpty() && !description.startsWith("fixedField.")) {
                    idColumn.setDescription(description);
                } else {
                    idColumn.setDescription(column.getCode());
                }
                idColumn.setOnlyFilter(fixedFiltersAliases.contains(column.getCode()));
                idColumn.setGridOrder(maxOrder.getAndIncrement());
                idColumn.setType(ReportColumnType.SKIP.getValue());
                idColumn.setQueryColumn(
                        new QueryColumn(column.getId(), column.getCode())
                );
                if (FixedField.CREATED_DATE.getAlias().equals(column.getCode())) {
                    idColumn.setSortPriority(0);
                    idColumn.setSortDirection(2);
                    idColumn.setType(ReportColumnType.DATE_TIME.getValue());
                }
                mandatoryColumns.add(0, idColumn);
            } else {
                // surveyFields
                for (SurveyDataFieldDTO field : fields) {
                    createReportColumn(column, field, orderColumns, maxOrder, columns, false, addedColumns, options);
                }
                // fixedFields
                if (fixedFieldsAliases.contains(column.getCode())) {
                    createFixedReportColumn(column, orderColumns, maxOrder, columns, false, addedColumns);
                }
                // filters
                for (SurveyDataFieldDTO filter : filters) {
                    createReportColumn(column, filter, orderColumns, maxOrder, columns, true, addedColumns, options);
                }
                // fixedFilters
                if (fixedFiltersAliases.contains(column.getCode())) {
                    createFixedReportColumn(column, orderColumns, maxOrder, columns, true, addedColumns);
                }
            }
        }
        final LinkedHashSet<ReportColumn> columnsWithLimit = new LinkedHashSet<>(columns);
        if (!mandatoryColumns.isEmpty()) {
            columnsWithLimit.addAll(mandatoryColumns);
        }
        return columnsWithLimit;
    }

    private void createReportColumn(
            final QueryColumn column,
            final SurveyDataFieldDTO field,
            final Map<String, Integer> orderColumns,
            final AtomicInteger maxOrder,
            final List<ReportColumn> columns,
            Boolean onlyFilter,
            final Set<String> addedColumns,
            final FormSlimReportDTO options
    ) {
        if (!Objects.equals(column.getCode(), field.getName()) || addedColumns.contains(field.getName())) {
            return;
        }
        addedColumns.add(field.getName());
        ReportColumn reportColumn = new ReportColumn(-1L);
        reportColumn.setDescription(field.getTitle());
        if (!onlyFilter && !options.getSurveyFields().contains(field.getName())) {
            onlyFilter = true;
        }
        reportColumn.setOnlyFilter(onlyFilter);
        reportColumn.setQueryColumn(
                new QueryColumn(column.getId(), column.getCode())
        );
        Integer gridOrder = orderColumns.get(field.getName());
        if (gridOrder == null) {
            gridOrder = maxOrder.getAndIncrement();
        }
        reportColumn.setGridOrder(gridOrder);
        if (field.getExternalCatalogId() != null) {
            reportColumn.setHierarchyCode(field.getFieldCode() + "_value");
            reportColumn.setHierarchyDescription(field.getCatalogLabel());
            reportColumn.setHierarchyId(hierarchyIdByExternalCatalog(field.getExternalCatalogId()));
        }
        final SurveyFieldAnswerType answerType = SurveyUtil.getSurveyFieldAnswerType(field);
        defineReportColumnType(
                field.getType(),
                field.getSubType(),
                reportColumn,
                answerType,
                field.getAnswerPartType(),
                field.getIncludeTime()
        );
        columns.add(reportColumn);
    }

    private QueryTestDto getTestData(
            String masterId,
            String surveyAnswersTable,
            IFlexiFields options,
            ILoggedUser loggedUser
    ) throws QMSException {
        String sql = ISlimReportsDAO.getParsedSlimReportQuery(
                masterId,
                surveyAnswersTable,
                options,
                true,
                true,
                true
        );
        // se ejecuta consulta de prueba
        final SessionImpl sessionImpl = (SessionImpl) getSession();
        final Connection connection = sessionImpl.connection();
        final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();
        return queryHandler.testQueryWithConnection(
                CONNECTION_TYPE,
                connection,
                sql,
                null,
                loggedUser
        );
    }

    private void createFixedReportColumn(
            final QueryColumn column,
            final Map<String, Integer> orderColumns,
            final AtomicInteger maxOrder,
            final List<ReportColumn> columns,
            final Boolean onlyFilter,
            final Set<String> addedColumns
    ) {
        if (addedColumns.contains(column.getCode())) {
            return;
        }
        addedColumns.add(column.getCode());
        ReportColumn reportColumn = new ReportColumn(-1L);
        if (column.getCode().startsWith("area_custom_field")) {
            // Campos personalizados de areas
            int areaCustomFieldIndex = Integer.parseInt(column.getCode().replace("area_custom_field", ""));
            String areaCustomFieldTag = FormUtil.getAreaCustomFieldTag(areaCustomFieldIndex);
            if (areaCustomFieldTag == null || areaCustomFieldTag.isEmpty()) {
                getLogger().debug("No se encontró el tag para el campo personalizado de area {}", column.getCode());
                return;
            }
            reportColumn.setDescription(areaCustomFieldTag);
        } else {
            reportColumn.setDescription(getTag("fixedField." + column.getCode()));
        }
        reportColumn.setOnlyFilter(onlyFilter);
        Integer gridOrder = orderColumns.get(String.valueOf(FixedField.fromAlias(column.getCode()).getValue()));
        if (gridOrder == null) {
            gridOrder = maxOrder.getAndIncrement();
        }
        reportColumn.setGridOrder(gridOrder);
        reportColumn.setQueryColumn(
                new QueryColumn(column.getId(), column.getCode())
        );
        defineReportColumnType(
                FixedField.fromAlias(column.getCode()).getType(),
                null,
                reportColumn,
                null,
                null,
                null
        );
        columns.add(0, reportColumn);
    }


    private Map<String, SlimReportOrderColumnsDTO> mapSlimReportColumns(List<SlimReportOrderColumnsDTO> slimReportOrderColumns) {
        return slimReportOrderColumns.stream()
                .collect(Collectors.toMap(
                        SlimReportOrderColumnsDTO::getId,
                        Function.identity(),
                        (prev, next) -> next // Keep the new value in case of duplicates
                ));
    }

    private void defineReportColumnType(
            final String type,
            final String subType,
            final ReportColumn reportColumn,
            final SurveyFieldAnswerType answerType,
            final Integer answerPartType,
            final Boolean includeTime
    ) {
        if (type == null) {
            reportColumn.setType(ReportColumnType.TEXT.getValue());
        } else {
            switch (type) {
                case SurveyField.TYPE_TEXT_DATE:
                    final AnswerPartType datePartType = AnswerPartType.fromValue(answerPartType);
                    if (datePartType != null) {
                        switch (datePartType) {
                            case DAY_FIELD:
                                reportColumn.setType(ReportColumnType.WEEK_DAY.getValue());
                                break;
                            case TIMEZONE:
                                reportColumn.setType(ReportColumnType.TEXT.getValue());
                                break;
                            case REGULAR_FIELD:
                                if (Boolean.TRUE.equals(includeTime)) {
                                    reportColumn.setType(ReportColumnType.DATE_TIME.getValue());
                                } else {
                                    reportColumn.setType(ReportColumnType.DATE.getValue());
                                }
                                break;
                        }
                    } else {
                        reportColumn.setType(ReportColumnType.DATE_TIME.getValue());
                    }
                    break;
                case SurveyField.TYPE_TEXT_TIME:
                    reportColumn.setType(ReportColumnType.TIME.getValue());
                    break;
                case SurveyField.TYPE_ABIERTA:
                case SurveyField.TYPE_ABIERTA_RENGLONES:
                    final AnswerPartType textPartType = AnswerPartType.fromValue(answerPartType);
                    if (SurveyFieldAnswerType.CURRENCY.equals(answerType)
                            || (AnswerPartType.REGULAR_FIELD.equals(textPartType) && SurveyFieldAnswerType.CURRENCY_USD_TO_MXN.equals(answerType))
                            || AnswerPartType.EXCHANGE_RATE_SOURCE.equals(textPartType)
                            || AnswerPartType.EXCHANGE_CONVERSION_RATE.equals(textPartType)) {
                        reportColumn.setType(ReportColumnType.CURRENCY.getValue());
                    } else {
                        reportColumn.setType(ReportColumnType.TEXT.getValue());
                    }
                    break;
                case SurveyField.TYPE_EXTERNAL_CATALOG:
                    final CatalogFieldType catalogSubType = CatalogFieldType.fromValue(subType);
                    if (CatalogFieldType.CATALOG_HIERARCHY.equals(catalogSubType)) {
                        reportColumn.setType(ReportColumnType.CATALOG_HIERARCHY.getValue());
                    } else {
                        reportColumn.setType(ReportColumnType.CATALOG_TEXT.getValue());
                    }
                    break;
                case SurveyField.TYPE_STOPWATCH:
                    final AnswerPartType stopwatchPartType = AnswerPartType.fromValue(answerPartType);
                    if (AnswerPartType.TIMEWORK_ID.equals(stopwatchPartType)) {
                        reportColumn.setType(ReportColumnType.HIDDEN.getValue());
                    } else if (AnswerPartType.RANGE_START.equals(stopwatchPartType)) {
                        reportColumn.setType(ReportColumnType.DATE_TIME.getValue());
                    } else if (AnswerPartType.RANGE_END.equals(stopwatchPartType)) {
                        reportColumn.setType(ReportColumnType.DATE_TIME.getValue());
                    } else if (AnswerPartType.RANGE_SECONDS.equals(stopwatchPartType)) {
                        reportColumn.setType(ReportColumnType.HIDDEN.getValue());
                    } else {
                        reportColumn.setType(ReportColumnType.TEXT.getValue());
                    }
                    break;
                case SurveyField.TYPE_FORM_WEIGHTING_RESULT:
                    reportColumn.setType(ReportColumnType.DOUBLE.getValue());
                    break;
                case SurveyField.TYPE_MENU:
                    reportColumn.setType(ReportColumnType.CATALOG_TEXT.getValue());
                    break;
                default:
                    reportColumn.setType(ReportColumnType.TEXT.getValue());
                    break;
            }
        }
    }

    private Long hierarchyIdByExternalCatalog(Long externalCatalogId) {
        Long queryId;

        queryId = HQL_findSimpleLong(""
                + "SELECT "
                    + "ext.query.id "
                + "FROM " + ExternalDocumentCatalog.class.getCanonicalName() + " ext "
                + "WHERE ext.id = :externalId ",
                ImmutableMap.of("externalId", externalCatalogId),
                true,
                CacheRegion.SURVEY,
                0
        );
        return queryId;
    }

    private void deleteSlimReportSurveyFields(Long slimReportId) {
        HQL_updateByQuery(" "
                + "DELETE FROM " + SlimReportsSurveyFields.class.getCanonicalName() + " slsf "
                + "WHERE slsf.slimReport.id = :slimReportId", ImmutableMap.of("slimReportId", slimReportId),
                true,
                CacheRegion.REPORT,
                0
        );
        HQL_updateByQuery(" "
                + "DELETE FROM " + SlimReportsSurveyFieldsFixed.class.getCanonicalName() + " slsfx "
                + "WHERE slsfx.slimReport.id = :slimReportId", ImmutableMap.of("slimReportId", slimReportId),
                true,
                CacheRegion.REPORT,
                0
        );
        HQL_updateByQuery(" "
                        + " DELETE FROM " + SlimReportsGhostField.class.getCanonicalName() + " g "
                        + " WHERE g.slimReport.id = :slimReportId", ImmutableMap.of("slimReportId", slimReportId),
                true,
                CacheRegion.REPORT,
                0
        );
        HQL_updateByQuery(" "
                        + " UPDATE " + SlimReportsTransformedFieldRule.class.getCanonicalName() + " SET deleted = 1, lastModifiedDate = current_date() "
                        + " WHERE slimReport.id = :slimReportId", ImmutableMap.of("slimReportId", slimReportId),
                true,
                CacheRegion.REPORT,
                0
        );
        HQL_updateByQuery(" "
                        + " UPDATE " + SlimReportsTransformedField.class.getCanonicalName() + " SET deleted = 1, lastModifiedDate = current_date() "
                        + " WHERE slimReport.id = :slimReportId", ImmutableMap.of("slimReportId", slimReportId),
                true,
                CacheRegion.REPORT,
                0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<FormSlimReportDTO> loadSlimReport(Long slimReportId, ILoggedUser loggedUser) {
        SlimReports slimReport = HQLT_findById(SlimReports.class, slimReportId);

        if (slimReport == null) {
            return new ResponseEntity<>(new FormSlimReportDTO(), HttpStatus.NOT_FOUND);
        }

        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
        FormSlimReportDTO slimReportDTO = new FormSlimReportDTO();
        modelMapper.map(slimReport, slimReportDTO);
        slimReportDTO.setSurveyFields(
                slimReport.getSurveysFields().stream()
                        .map(SlimReportsSurveyFields::getFieldName)
                        .collect(Collectors.toList())
        );
        slimReportDTO.setSurveyFilters(
                slimReport.getSurveysFilters().stream()
                        .map(SlimReportsSurveyFields::getFieldName)
                        .collect(Collectors.toList())
        );
        if (slimReport.getRelatedFields() != null && !slimReport.getRelatedFields().isEmpty()) {
            slimReportDTO.setRelatedFields(
                    slimReport.getRelatedFields().stream()
                    .map(SlimReportsRelatedField::getFieldName)
                    .collect(Collectors.toList())
            );
        }
        slimReportDTO.setFixedFields(
                slimReport.getSurveyFieldsFixed().stream()
                        .map(SlimReportsSurveyFieldsFixed::getFixedFieldEnumValue)
                        .collect(Collectors.toSet())
        );
        slimReportDTO.setFixedFilters(
                slimReport.getSurveyFiltersFixed().stream()
                        .map(SlimReportsSurveyFieldsFixed::getFixedFieldEnumValue)
                        .collect(Collectors.toSet())
        );
        TreeSet<SlimReportOrderColumnsDTO> columOrders = new TreeSet<>();
        slimReport.getSurveysFields().forEach(f -> {
            columOrders.add(new SlimReportOrderColumnsDTO(
                    f.getFieldId().toString(),
                    f.getFieldName(),
                    f.getOrder(),
                    SlimReportOrderColumnsDTO.FieldType.FLEXI,
                    f.getAlias()
            ));
        });
        slimReport.getSurveyFieldsFixed().forEach(f -> {
            columOrders.add(new SlimReportOrderColumnsDTO(
                    f.getFixedFieldEnumValue().toString(),
                    f.getFixedFieldEnumValue().toString(),
                    f.getOrder(),
                    SlimReportOrderColumnsDTO.FieldType.FIXED,
                    f.getAlias()
            )
            );
        });
        slimReport.getGhostFields().forEach(f -> {
            columOrders.add(new SlimReportOrderColumnsDTO(
                    f.getCode(),
                    f.getDescription(),
                    f.getOrder(),
                    SlimReportOrderColumnsDTO.FieldType.GHOST,
                    f.getAlias()
            ));
        });
        slimReport.getTransformedFields().forEach(f -> {
            columOrders.add(new SlimReportOrderColumnsDTO(
                    f.getCode(),
                    f.getDescription(),
                    f.getOrder(),
                    SlimReportOrderColumnsDTO.FieldType.TRANSFORMED,
                    f.getAlias()
            ));
        });
        slimReportDTO.setOrderColumns(columOrders.stream().collect(Collectors.toList()));
        return new ResponseEntity<>(slimReportDTO, HttpStatus.OK);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void newSlimReportFromDocument(Request request, Document document, ILoggedUser loggedUser) throws RuntimeException {
        FormSlimReportDTO slimReport = new FormSlimReportDTO();
        slimReport.setId(-1L);
        if (request.getSlimReportName() == null) {
            request.setSlimReportName(document.getDescription() + " " + new Date().getTime());
        }
        slimReport.setDescription(request.getSlimReportName());
        slimReport.setDetails(document.getDescription());
        slimReport.setDocumentMasterId(document.getMasterId());
        List<SlimReportOrderColumnsDTO> slimReportOrderColumns = new ArrayList<>();
        slimReport.setFixedFields(FormUtil.getFixedFields().stream().map(m -> {
            final SlimReportOrderColumnsDTO column = new SlimReportOrderColumnsDTO(
                    String.valueOf(m.getValue()),
                    String.valueOf(m.getValue()),
                    slimReportOrderColumns.size() + 1,
                    SlimReportOrderColumnsDTO.FieldType.FIXED,
                    m.getAlias()
            );
            slimReportOrderColumns.add(column);
            return m.getValue();
        }).collect(Collectors.toSet()));
        final Integer maxResults = Utilities.getSettings().getFormReportMaxColumns();
        final FieldConfigDTO fieldConfig = SurveyUtil.getFields(
                document.getSurveyId(),
                false,
                false,
                false,
                null,
                0,
                loggedUser,
                true,
                true,
                true
        );
        final Set<SurveyDataFieldDTO> allFields = fieldConfig.getFields();
        final Set<SurveyDataFieldDTO> fields;
        if (!Objects.equals(maxResults, 0) && allFields.size() > maxResults) {
            fields = new LinkedHashSet<>(new ArrayList<>(allFields).subList(0, maxResults));
        } else {
            fields = new LinkedHashSet<>(allFields);
        }
        final List<String> fieldNames = fields.stream()
                .filter(field -> !Objects.equals(AnswerPartType.CATALOG_VALUE.getValue(), field.getAnswerPartType()))
                .map(field -> {
                    slimReportOrderColumns.add(new SlimReportOrderColumnsDTO(
                            String.valueOf(field.getName()),
                            String.valueOf(field.getTitle()),
                            slimReportOrderColumns.size() + 1,
                            SlimReportOrderColumnsDTO.FieldType.FLEXI,
                            ""
                    ));
                    return field.getName();
                })
                .collect(Collectors.toList());
        slimReport.setSurveyFields(fieldNames);
        final List<String> relatedFieldNames = FormUtil.getRelatedFields(fields, allFields);
        slimReport.setRelatedFields(relatedFieldNames);
        slimReport.setOrderColumns(slimReportOrderColumns);
        slimReport.setBusinessUnitAccessValues(new LinkedHashSet<>());
        slimReport.setBusinessUnitDepartmentAccessValues(new LinkedHashSet<>());
        slimReport.setProcessAccessValues(new LinkedHashSet<>());
        slimReport.setUserAccessValues(new LinkedHashSet<>());
        slimReport.setTruncateColumnsToMax(true);
        try {
            newSlimReport(slimReport, loggedUser);
        } catch (final QMSException ex) {
            throw new RuntimeException(ex);
        }
    }

}
