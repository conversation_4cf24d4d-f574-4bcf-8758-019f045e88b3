package qms.framework.rest;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.User_old;
import Framework.Config.TextIconValue;
import Framework.Config.Utilities;
import bnext.dto.old.TblUsuarioDTO;
import bnext.login.DAOInterface.IUserDetailsService;
import bnext.login.Login;
import bnext.login.config.CustomWebAuthenticationDetails;
import bnext.login.core.IUserDetail;
import bnext.login.dto.ScreenResolutionDTO;
import bnext.login.dto.ServicesDTO;
import bnext.login.util.AuthenticationUtils;
import bnext.login.util.LoginError;
import bnext.login.util.OidcHelper;
import bnext.reference.UserRef;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import mx.bnext.access.IBnextModule;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.Assert;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.framework.entity.DocumentLink;
import qms.framework.entity.GeolocationCoordinates;
import qms.framework.util.LocaleUtil;
import static bnext.login.Login.RENEW_DATA_TIMEZONE;

/**
 *
 * <AUTHOR>
 */
public class SecurityUtils {
    
    private static final Logger LOGGER = Loggable.getLogger(SecurityUtils.class);    

    /**
     * ToDo: Reemplazar la variable FILTER_EQUAL_EXCEPTIONS por un
     * <security-constraint/> en web.xml
     */
    //Si agregas una excepción aquí, muy seguramente la necesites en core.js - FILL_FOOTER_EXCEPTIONS
    private static final Set<String> FILTER_EXCEPTIONS = new HashSet<>(Arrays.asList(new String[]{
        "/style/s-welcome-bg.style",
        "/view/web.service.action",
        "/view/web-service-document.action",
        "/view/v-default-report-logo.view",
        "/view/v-application-logo.view",
        "/view/v-report-logo.view",
        "/view/v-jasper-logo.view",
        DocumentLink.TYPE.SHARED_LINK.getActionName() + ".dwg",
        DocumentLink.TYPE.SHARED_LINK.getAction(),
        DocumentLink.TYPE.EXTERNAL_LINK.getAction(),
        DocumentLink.TYPE.VIEWER_LINK.getAction(),
        DocumentLink.TYPE.FORM_LINK.getAction(),
        DocumentLink.TYPE.FORM_SERVICE.getAction(),
        "/DPMS/LabelInterpolationService.action",
        "/view/HappyornotSurvey.Answer.action",
        "/view/v-happyornot-survey.view",
        "/menu/legacy/v-happyornot-survey.view",
        "/view/v-happyornot-survey-secure.view",
        "/menu/legacy/v-happyornot-survey-secure.view",
        "/view/v-nosession-download-video.action",
        "/view/v-login.view",
        "/view/v-session.view",
        "/view/v-logout.view",
        "/view/v-ldap-session.view",
        "/view/v-isotype-logo.view",
        "/view/v-survey-answered.view",       
        "/view/v-application-welcome-carousel.view"       
    }));
    
    public static final String PARAMETERS_DELIMITER = "?";
    public static final String LDAP_TOKEN_PREFIX = "ldap_";

    public static boolean isException(final String servletPath) {
        if (servletPath == null || servletPath.isEmpty()) {
            return false;
        }
        if (servletPath.length() > 1 && servletPath.contains(PARAMETERS_DELIMITER)) {
            final String pathNoParameters = servletPath.substring(0, servletPath.indexOf(PARAMETERS_DELIMITER));
            return FILTER_EXCEPTIONS.contains(pathNoParameters);
        }
        return FILTER_EXCEPTIONS.contains(servletPath);
    }

    private SecurityUtils() {
    }

    @Nullable
    public static Authentication getLoggedUserAuth() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    @Nullable
    public static CustomWebAuthenticationDetails getLoggedUserAuthDetails() {
        final Authentication loggedUserAuth = getLoggedUserAuth();
        if (loggedUserAuth == null) {
            return null;
        }
        return (CustomWebAuthenticationDetails) loggedUserAuth.getDetails();
    }

    @Nonnull
    public static Boolean isOidcLogin() {
        final Authentication loggedUserAuth = getLoggedUserAuth();
        return OidcHelper.isOidcLogin(loggedUserAuth);
    }

    @Nullable
    public static IUserDetail getLoggedUserDetails() {
        final Authentication loggedUserAuth = getLoggedUserAuth();
        if (loggedUserAuth == null) {
            return null;
        }
        Object user = loggedUserAuth.getPrincipal();
        if (user == null || user.toString().equals("anonymousUser")) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Invalid session");
            }
            return null;
        }
        return (IUserDetail) loggedUserAuth.getPrincipal();
    }

    @Nonnull
    public static List<ProfileServices> getLoggedUserServices() {
        return UserUtils.getUserServices(getLoggedUserDetails(), isLoggedUserAdmin());
    }

    public static boolean hasLoggedUserService(final ProfileServices ... service) {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return false;
        }
        for (ProfileServices s : service) {
            if (loggedUserDetails.getAuthorities().contains(new SimpleGrantedAuthority(s.name()))) {
                return true;
            }
        }
        return false;
    }
        
    public static boolean hasLoggedUserService(final String serviceName) {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return false;
        }
        return loggedUserDetails.getAuthorities().contains(new SimpleGrantedAuthority(serviceName));
    }
    
    public static boolean isLoggedUserAdmin() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return false;
        }
        return loggedUserDetails.getAuthorities().contains(UserUtils.IS_ADMIN_AUTHORITY);
    }

    @Nullable
    public static Long getLoggedUserId() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return null;
        }
        return loggedUserDetails.getUserId();
    }

    @Nullable
    public static String getLoggedUserName() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return null;
        }
        return loggedUserDetails.getName();
    }
    
    /**
     * 
     * @return "es" o "en"
     */
    @Nonnull
    public static String getLoggedUserLang() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        String lang;
        if (loggedUserDetails == null) {
            lang = null;
        } else {
            lang = loggedUserDetails.getLang();
        }
        if (lang == null || lang.isEmpty()) {
            lang = Utilities.getSettings().getLang();
        }
        return lang;
    }

    /**
     * 
     * @return "MX"
     */
    @Nullable
    public static String getLoggedUserLocale() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return null;
        }
        return loggedUserDetails.getLocale();
    }

    @Nullable
    public static Locale getLoggedUserLocaleObject() {
        String lang = getLoggedUserLang();
        if (lang == null) {
            return LocaleUtil.getLocale();
        }
        String country = getLoggedUserLocale();
        if (country == null) {
            return LocaleUtil.getLocale();
        }
        return new Locale(lang, country);
    }


    @Nullable
    public static String getLoggedUserAccount() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return null;
        }
        return loggedUserDetails.getAccount();
    }

    @Nullable
    public static String getLoggedUserMail() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return null;
        }
        return loggedUserDetails.getMail();
    }

    @Nonnull
    public static Collection<IBnextModule> getLoggedUserModules() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return new ArrayList<>(0);
        }
        return loggedUserDetails.getModules();
    }

    @Nonnull
    public static Collection<? extends GrantedAuthority> getLoggedUserAuthorities() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return new ArrayList<>(0);
        }
        return loggedUserDetails.getAuthorities();
    }
 
    public static void initiLegacySessionVariables(
            Authentication authentication,
            HttpServletRequest request,
            Boolean ldap
    ) {
        try {
            final IUserDetail principal = (IUserDetail) authentication.getPrincipal();
            final TblUsuarioDTO userData = getUserData(principal.getUserId());
            final GeolocationCoordinates location = getLocation(authentication);
            final ScreenResolutionDTO screenResolution = getScreenResolution(authentication);
            Login.initializeSession(authentication, request, userData, location, screenResolution, ldap);
        } catch (final Exception e) {
            AuthenticationUtils.throwFailedLegacySessionException(authentication, e);
        }
    }

    private static GeolocationCoordinates getLocation(Authentication authentication) {
        final GeolocationCoordinates location;
        if (authentication.getDetails() != null) {
            final CustomWebAuthenticationDetails details = (CustomWebAuthenticationDetails) authentication.getDetails();
            if (details != null) {
                location = details.getLocation();
            } else {
                location = null;
            }
        } else {
            location = null;
        }
        return location;
    }

    private static ScreenResolutionDTO getScreenResolution(Authentication authentication) {
        final ScreenResolutionDTO screenResolution;
        if (authentication.getDetails() != null) {
            final CustomWebAuthenticationDetails details = (CustomWebAuthenticationDetails) authentication.getDetails();
            if (details != null) {
                screenResolution = details.getScreenResolution();
            } else {
                screenResolution = null;
            }
        } else {
            screenResolution = null;
        }
        return screenResolution;
    }


    @Nonnull
    private static TblUsuarioDTO getUserData(Long id){
        final IUserDAO dao = Utilities.getBean(IUserDAO.class);
        final User_old usr = dao.HQLT_findById(User_old.class, id);
        final Boolean isAdmin = Objects.equals(usr.getRoot(), 1);
        final ServicesDTO services = dao.getUserServices(id, isAdmin);
        final List<ProfileServices> userServices = new ArrayList<>(services.getUserServices());
        final List<ProfileServices> userServicesAddedBySystem = new ArrayList<>(services.getUserServicesAddedBySystem());
        final UserRef userSimple = dao.HQLT_findById(UserRef.class, id);
        return new TblUsuarioDTO(userSimple, usr, userServices, userServicesAddedBySystem);
    }
    
    public static boolean hasSession() {
        final IUserDetail loggedUser = getLoggedUserDetails();
        return loggedUser != null;
    }
    
    public static void forceLogin(AbstractAuthenticationToken authenticationToken, HttpServletRequest request, Boolean ldap) {
        Assert.notNull(authenticationToken.getPrincipal(),"Principal must not be null");
        Assert.isInstanceOf(IUserDetail.class, authenticationToken.getPrincipal(),"Principal must be an instance of IUserDetail");
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        initiLegacySessionVariables(authenticationToken, request, ldap);
    }

    @Nonnull
    public static UsernamePasswordAuthenticationToken authenticationToken(String username){
        IUserDetailsService bean = Utilities.getBean(IUserDetailsService.class);
        IUserDetail details = bean.loadUserByUsername(username);
        return new UsernamePasswordAuthenticationToken(details, null, details.getAuthorities());
    }

    @Nonnull
    public static ILoggedUser getLoggedUser() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            final Long invalidId = null;
            //noinspection ConstantValue
            return new LoggedUser(invalidId);
        }
        final LoggedUser data = new LoggedUser(
            isLoggedUserAdmin(),
            getLoggedUserId(),
            loggedUserDetails.getBusinessUnitId(),
            loggedUserDetails.getBusinessUnitDepartmentId(),
            loggedUserDetails.getAccount(),
            loggedUserDetails.getMail(),
            loggedUserDetails.getName(),
            getLoggedUserServices(),
            loggedUserDetails.getTimezone(),
            loggedUserDetails.getVersion()
        );
        data.setLocale(UserUtils.parseLocale(loggedUserDetails));
        return data;
    }

    @Nullable
    public static Collection<TextIconValue> getLoggedUserActivityWorkflows() {
        final IUserDetail loggedUserDetails = getLoggedUserDetails();
        if (loggedUserDetails == null) {
            return null;
        }
        final Collection<IBnextModule> modules = loggedUserDetails.getModules();
        if (!modules.contains(Module.ACTIVITY)) {
            return null;
        }
        return Utilities.getBean(IActivityDAO.class).getActiveWorkflows(Module.ACTIVITY);
    }

    @Nullable
    public static String getRenewDataLocation(final HttpServletRequest request) {
        final Object renewData = request.getSession().getAttribute(Login.RENEW_DATA_LOCATION);
        if (renewData == null) {
            return null;
        }
        return renewData.toString();
    }

    @Nullable
    public static String getRenewPasswordAttribute(final HttpServletRequest request) {
        final Object renewDataPassword = request.getSession().getAttribute(Login.RENEW_DATA_PASSWORD);
        if (renewDataPassword == null) {
            return null;
        }
        return renewDataPassword.toString();
    }

    @Nullable
    public static String getRenewDataTimezone(final HttpServletRequest request) {
        final Object renewDataTimezone = request.getSession().getAttribute(RENEW_DATA_TIMEZONE);
        if (renewDataTimezone == null) {
            return null;
        } 
        return renewDataTimezone.toString();
    }
    
    public static boolean isRedirectUrlFromLdapSession(HttpServletRequest request, String token) {
        if (request == null || token == null || token.isEmpty()) {
            return false;
        }
        try {
            if (!token.startsWith(SecurityUtils.LDAP_TOKEN_PREFIX)) {
                return false;
            }
            final String plainToken = token.replace(SecurityUtils.LDAP_TOKEN_PREFIX, "");
            final String decodedToken = URLDecoder.decode(plainToken, "UTF-8");
            if (decodedToken == null || decodedToken.isEmpty()) {
                return false;
            }
            final Login ldapLogin = new Login(decodedToken, request, Utilities.getBean(IUserDAO.class));
            final boolean result = ldapLogin.validate();
            if (result) {
                return true;
            } else {
                return LoginError.EXPIRED_TOKEN.equals(ldapLogin.getError());
            }
        } catch (Exception ex) {
            LOGGER.error("Failed to validate if the token is from LDAP session", ex);
            return false;
        }
    }
    

}
