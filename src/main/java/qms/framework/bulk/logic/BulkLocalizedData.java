package qms.framework.bulk.logic;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import java.util.Objects;
import qms.framework.bulk.dto.IBulkLocalizedRow;
import qms.framework.bulk.dto.IBulkWorkBook;
import qms.framework.bulk.imp.IBulkPlainRow;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BulkLocalizedData<ROW_ENTRY extends IBulkPlainRow, ROW_HEADERS, LOCAL_ROW extends IBulkLocalizedRow<ROW_ENTRY>> implements IBulkLocalizedData<ROW_ENTRY, ROW_HEADERS, LOCAL_ROW> {

    private static final long serialVersionUID = 1L;

    private String name;
    private List<LOCAL_ROW> rows;
    private IBulkWorkBook<ROW_ENTRY, ROW_HEADERS> workbook;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public List<LOCAL_ROW> getRows() {
        return rows;
    }

    @Override
    public void setRows(List<LOCAL_ROW> localRows) {
        this.rows = localRows;
    }

    public IBulkWorkBook<ROW_ENTRY, ROW_HEADERS> getWorkbook() {
        return workbook;
    }

    public void setWorkbook(IBulkWorkBook<ROW_ENTRY, ROW_HEADERS> workbook) {
        this.workbook = workbook;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + Objects.hashCode(this.name);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BulkLocalizedData other = (BulkLocalizedData) obj;
        return Objects.equals(this.name, other.name);
    }

    @Override
    public String toString() {
        return "BulkLocalizedData{"
                + "name=" + name
                + ", rows=" + (rows != null ? rows.size() : 0) + '}';
    }
}
