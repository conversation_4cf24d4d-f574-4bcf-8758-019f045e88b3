package qms.framework.bulk.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.custom.core.DynamicSearchColumn;
import qms.framework.bulk.util.BuilkUploadType;
import qms.util.CodePrefix;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@CodePrefix("BULK-")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "bulk_log")
public class BulkLog extends StandardEntity<BulkLog> implements DomainObjectInterface, IAuditableEntity, IBulkLog {

    private static final long serialVersionUID = 1L;

    private String code = "";
    private String description = "";
    private Integer status = BulkLogStatus.SUCCESS.getValue();
    private Integer deleted = 0;
    private Integer type;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private String masterId;
    private Integer errorCount = 0;
    private Long slimReportId;
    private Long sourceFileId;
    private Long resultFileId;

    public BulkLog(BuilkUploadType type) {
        this.deleted = IS_NOT_DELETED;
        this.status = ACTIVE_STATUS;
        this.type = type.getValue();
    }

    public BulkLog(Long id, BuilkUploadType type) {
        this.id = id;
        this.type = type.getValue();
    }

    public BulkLog() {

    }

    @Id
    @Basic
    @Column(name = "bulk_log_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 10)
    @DynamicSearchColumn
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if (estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if (borrado == null) {
            this.deleted = IS_DELETED;
        } else {
            this.deleted = borrado;
        }
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "master_id")
    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }

    @Column(name = "slim_report_id")
    public Long getSlimReportId() {
        return slimReportId;
    }

    public void setSlimReportId(Long slimReportId) {
        this.slimReportId = slimReportId;
    }

    @Column(name = "source_file_id")
    public Long getSourceFileId() {
        return sourceFileId;
    }

    public void setSourceFileId(Long sourceFileId) {
        this.sourceFileId = sourceFileId;
    }

    @Column(name = "result_file_id")
    public Long getResultFileId() {
        return resultFileId;
    }

    public void setResultFileId(Long resultFileId) {
        this.resultFileId = resultFileId;
    }

    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "error_count")
    @Override
    public Integer getErrorCount() {
        return errorCount;
    }

    @Override
    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BulkLog other = (BulkLog) obj;
        return Objects.equals(this.id, other.id);
    }


    @Override
    public int hashCode() {
        int hash = 7;
        hash = 47 * hash + Objects.hashCode(this.code);
        return hash;
    }

    @Override
    public String toString() {
        return "qms.activity.entity.BulkLog[ id=" + id + " ]";
    }
}
