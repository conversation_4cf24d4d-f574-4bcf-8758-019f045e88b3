package qms.framework.bulk.dto;

import java.io.Serializable;
import qms.framework.bulk.imp.IBulkPlainRow;

public class BulkPlainRow implements Serializable, IBulkPlainRow {
    private static final long serialVersionUID = 1L;
    private Integer rowNum;
    private Boolean valid;

    @Override
    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }
}
