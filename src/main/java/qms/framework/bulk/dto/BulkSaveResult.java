package qms.framework.bulk.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import qms.framework.bulk.imp.IBulkPlainRow;
import qms.framework.bulk.util.ExcelErrorCoordinateXY;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BulkSaveResult<ROW_ENTRY extends IBulkPlainRow, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> implements IBulkSaveResult<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> {

    private Boolean success;
    private IBulkActivityIndex<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> index;
    private List<REPORTED_ENTITIES> resultRecords;
    private Integer countRows;
    private Map<ExcelErrorCoordinateXY, String> rowsErrors;
    private Map<ExcelErrorCoordinateXY, String> variablesErrors;

    public BulkSaveResult() {
    }

    public BulkSaveResult(
            IBulkActivityIndex<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> index,
            List<REPORTED_ENTITIES> saved
    ) {
        this(index, saved, index.getRows() == null ? 0 : index.getRows().size());
    }

    public BulkSaveResult(
            IBulkActivityIndex<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> index,
            List<REPORTED_ENTITIES> saved,
            Integer countRows
    ) {
        this.success = saved != null && !saved.isEmpty();
        this.resultRecords = saved;
        this.index = index;
        if (index.getWorkbook() != null) {
            this.rowsErrors = index.getWorkbook().getRowsSheet().getErrors();
            if (index.getWorkbook().getVariablesSheet() == null) {
                this.variablesErrors = new HashMap<>(0);
            } else {
                this.variablesErrors = index.getWorkbook().getVariablesSheet().getErrors();
            }
        } else {
            this.rowsErrors = new HashMap<>(0);
            this.variablesErrors = new HashMap<>(0);
        }
        this.countRows = countRows;
    }


    public List<REPORTED_ENTITIES> getResultRecords() {
        return resultRecords;
    }

    public void setResultRecords(List<REPORTED_ENTITIES> resultRecords) {
        this.resultRecords = resultRecords;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public IBulkActivityIndex<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> getIndex() {
        return index;
    }

    public void setIndex(IBulkActivityIndex<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> index) {
        this.index = index;
    }

    public Integer getCountRows() {
        return countRows;
    }

    public void setCountRows(Integer countRows) {
        this.countRows = countRows;
    }

    @Nullable
    public Map<ExcelErrorCoordinateXY, String> getRowsErrors() {
        return rowsErrors;
    }

    public void setRowsErrors(Map<ExcelErrorCoordinateXY, String> rowsErrors) {
        this.rowsErrors = rowsErrors;
    }

    public Map<ExcelErrorCoordinateXY, String> getVariablesErrors() {
        return variablesErrors;
    }

    public void setVariablesErrors(Map<ExcelErrorCoordinateXY, String> variablesErrors) {
        this.variablesErrors = variablesErrors;
    }
        
}
