package qms.framework.bulk;

import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Date;
import java.util.Map;
import javax.annotation.Nonnull;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.file.IFileManager;
import mx.bnext.core.util.Loggable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import qms.access.dto.ILoggedUser;
import qms.framework.bulk.dto.BulkUploadResult;
import qms.framework.bulk.dto.IBulkSaveResult;
import qms.framework.bulk.dto.IBulkUploadResult;
import qms.framework.bulk.entity.IBulkLog;
import qms.framework.bulk.imp.IBulkPlainRow;
import qms.framework.bulk.imp.IBulkUploader;
import qms.framework.bulk.logic.BulkWriter;
import qms.framework.bulk.util.BuilkUploadType;
import qms.framework.bulk.util.BulkError;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.dto.UploadFileResult;
import qms.framework.file.FileManager;
import qms.framework.util.ExcelParser;
import qms.framework.util.MeasureTime;
import qms.util.QMSException;

public class BulkHandler<ROW_ENTRY extends IBulkPlainRow, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO, BULK_UPLOADER extends IBulkUploader<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO>> implements IBulkUploadHandler<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO, BULK_UPLOADER> {

    private static final Logger LOGGER = Loggable.getLogger(BulkHandler.class);
    private final BULK_UPLOADER uploader;

    public BulkHandler(BULK_UPLOADER uploader) {
        this.uploader = uploader;
    }


    public BuilkUploadType getBuilkUploadType() {
        return this.uploader.getBuilkUploadType();
    }

    public ResponseEntity<Void> downloadTemplate(
        @Nonnull HttpServletResponse response,
        @Nonnull ILoggedUser loggedUser
    ) throws QMSException, IOException {
        final String filename = getUploader().getTag("templateName") ;
        return downloadTemplate(response, filename, loggedUser);
    }

    public ResponseEntity<Void> downloadTemplate(
        @Nonnull HttpServletResponse response,
        @Nonnull String filename,
        @Nonnull ILoggedUser loggedUser
    ) throws QMSException {
        try (final ServletOutputStream output = response.getOutputStream()) {
            getWritter().writeTemplate(filename, response, output, loggedUser);
        } catch (final IOException ex) {
            LOGGER.error("Failed to write upload template", ex);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }

    @Nonnull
    public ResponseEntity<Map<String, Object>> uploadTemplate(
        @Nonnull MultipartFile multipart,
        @Nonnull ILoggedUser loggedUser
    ) throws QMSException, IOException, BulkError {
        final BulkUpload<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO, BULK_UPLOADER> bulkUpload = new BulkUpload<>();
        return bulkUpload.handleFileUpload(multipart, this, loggedUser);
    }

    /**
     * Utilizar solo para TESTS
     */
    @Override
    public BULK_UPLOADER getUploader() {
        return uploader;
    }

    @Override
    public IFileManager getFileManager() {
        return uploader.getFileManager();
    }
    /**
     * Utilizar solo para TESTS
     *
     * @deprecated utilizar solo en TESTS
     */
    @Override
    public BulkWriter<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> getWritter() {
        if (getUploader() == null) return null;
        return getUploader().getWritter();
    }

    /**
     * Utilizar metodo publico solo para TESTS
     *
     * @deprecated utilizar solo en TESTS
     */
    @Nonnull
    public IBulkUploadResult<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> uploadAction(
        @Nonnull final String filename,
        @Nonnull final Path file,
        @Nonnull final IBulkLog log,
        @Nonnull final ILoggedUser loggedUser
    ) throws QMSException, BulkError {
        final IBulkSaveResult<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> saveResult = this.uploader.getSaveResult(
                filename,
                file,
                loggedUser
        );
        this.uploader.generateBulkRegistryLog(saveResult, log, loggedUser);
        final Path resultFile = getWritter().writeSaveResult(filename, saveResult);
        return new BulkUploadResult<>(resultFile, saveResult);
    }

    /**
     * Utilizar solo para TESTS
     *
     * @deprecated utilizar solo en TESTS
     */
    @Nonnull
    public IBulkUploadResult<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> upload(
        @Nonnull final String filename,
        @Nonnull final Path file,
        @Nonnull IBulkLog log,
        @Nonnull final ILoggedUser loggedUser
    ) throws QMSException, BulkError {
        IBulkUploadResult<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> result;
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            result = this.uploadAction(filename, file, log, loggedUser);
        } catch (final BulkError ex) {
            result = this.uploader.writeCustomError(filename, file, ex.getWorkBook(), ex);
        } catch (final Exception ex) {
            result = this.uploader.writeGenericError(filename, ex, loggedUser);
        } finally {
            MeasureTime.stop(tStart, "Elapsed time in bulk upload.");
        }
        return result;
    }

    public GenericSaveHandle executeBulkUpload(
            final String filename,
            final String contentType,
            final Path file,
            final BuilkUploadType type,
            @Nonnull final ILoggedUser loggedUser
    ) throws QMSException, BulkError {
        final FileManager fileManager = new FileManager();
        final IBulkUploader<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> uploader = getUploader();
        final UploadFileResult fsh = fileManager.persistFileContent(
                -1L,
                file,
                filename,
                contentType,
                false,
                loggedUser
        );
        final GenericSaveHandle gsh = fsh.getGsh();
        if (gsh.getOperationEstatus() == 1) {
            final Long sourceFileId = fsh.getData().getId();
            // genera historial de carga de exceles
            final IBulkLog savedFile = uploader.generateBulkLog(sourceFileId, filename, type, loggedUser);
            // inicia carga
            if (savedFile != null) {
                IBulkUploadResult<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> result = this.upload(filename, file, savedFile, loggedUser);
                final String resultFilename;
                final String resultDescription;
                if (result.getError() != null) {
                    String error = qms.framework.util.ExceptionUtils.getRootCauseMessage(result.getError());
                    resultDescription = getBulkUploadDescriptionError(filename, error);
                    resultFilename = getBulkUploadFilenameError(filename);
                    if (result.getSaveResult() != null && result.getSaveResult().getRowsErrors() != null) {
                        savedFile.setErrorCount(result.getSaveResult().getRowsErrors().size());
                    } else {
                        savedFile.setErrorCount(0);
                    }
                    savedFile.setStatus(IBulkLog.BulkLogStatus.SUCCESS.getValue());
                    this.uploader.makePersistent(savedFile, loggedUser.getId());
                } else {
                    final IBulkSaveResult<ROW_ENTRY, ROW_HEADERS, REPORTED_ENTITIES, ENTITY_DTO> saveResult = result.getSaveResult();
                    if (saveResult != null && saveResult.getSuccess()) {
                        final Integer count = saveResult.getCountRows();
                        resultDescription = getBulkUploadDescriptionSuccess(filename, count);
                        resultFilename = getBulkUploadFileNameSuccess(filename);
                    } else {
                        final String error;
                        if (saveResult == null) {
                            error = "-";
                        } else if (saveResult.getRowsErrors() != null && !saveResult.getRowsErrors().isEmpty()) {
                            error = StringUtils.join(
                                    saveResult.getRowsErrors().values(),
                                    ","
                            );
                        } else if (!saveResult.getVariablesErrors().isEmpty()) {
                            error = StringUtils.join(
                                    saveResult.getVariablesErrors().values(),
                                    ","
                            );
                        } else {
                            error = "-";
                        }
                        resultDescription = getBulkUploadDescriptionError(filename, error);
                        resultFilename = getBulkUploadFilenameError(filename);
                    }
                }
                savedFile.setDescription(StringUtils.substring(resultDescription, 0, 240));
                final GenericSaveHandle gshResult = fileManager.persistFileContent(
                        -1L,
                        result.getFile(),
                        resultFilename,
                        ExcelParser.EXCEL_2007_CONTENT_TYPE,
                        false,
                        loggedUser
                ).getGsh();
                final Long resultFileId = gshResult.getPersistentId();
                savedFile.setResultFileId(resultFileId);
                final IUntypedDAO dao = Utilities.getUntypedDAO();
                dao.makePersistent(savedFile, loggedUser.getId());
                gsh.getJsonEntityData().put("id", savedFile.getId());
                gsh.getJsonEntityData().put("description", resultFilename);
                gsh.getJsonEntityData().put("sourceFileId", sourceFileId);
                gsh.getJsonEntityData().put("resultFileId", resultFileId);
                return gsh;
            } else {
                return gsh;
            }
        } else {
            return gsh;
        }
    }

    private String getBulkUploadFileNameSuccess(final String filename) {
        final String resultFilename = this.uploader.getTag("bulkUploadFilenameSuccess")
                .replace("{filename}", StringUtils.substring(filename, 0, 240))
                .replace("{date}", Utilities.formatDateWithTime(new Date()))
                .replaceAll(":", "-")
                .replaceAll("\\.", "_")
                .replaceAll("xlsx", "")
                .replaceAll("xls", "")
                .replaceAll("csv", "");
        return resultFilename + ".xlsx";
    }

    private String getBulkUploadDescriptionSuccess(final String filename, final Integer count) {
        return this.uploader.getTag("bulkUploadDescriptionSuccess")
                .replace("{filename}", StringUtils.substring(filename, 0, 240))
                .replace("{count}", count.toString());
    }

    private String getBulkUploadDescriptionError(final String filename, final String error) {
        return this.uploader.getTag("bulkUploadDescriptionError")
                .replace("{filename}", StringUtils.substring(filename, 0, 240))
                .replace("{error}", error);
    }

    private String getBulkUploadFilenameError(final String filename) {
        final String resultFilename = this.uploader.getTag("bulkUploadFilenameError")
                .replace("{filename}", StringUtils.substring(filename, 0, 240))
                .replace("{date}", Utilities.formatDateWithTime(new Date()))
                .replaceAll(":", "-")
                .replaceAll("\\.", "_")
                .replaceAll("xlsx", "")
                .replaceAll("xls", "")
                .replaceAll("csv", "");
        return resultFilename + ".xlsx";
    }

}
