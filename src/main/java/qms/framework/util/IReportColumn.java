package qms.framework.util;

public interface IReportColumn {

    String getDescription();

    Integer getGridOrder();

    Integer getGroupingDirection();

    Integer getGroupingPriority();

    String getHierarchyCode();

    String getHierarchyDescription();

    Long getHierarchyId();

    Long getId();

    IQueryColumn getQueryColumn();

    Long getReportId();

    Integer getSortDirection();

    Integer getSortPriority();

    /**
     *
     * @return Devuelve un valor de tipo {@link qms.framework.util.ReportColumnType}
     */
    Integer getType();

    String getWidth();

    Boolean getOnlyFilter();

    Long getQueryColumnId();
}
