package qms.framework.util;

import java.sql.Connection;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import qms.util.interfaces.IGridFilter;

public interface ISqlDAO {

    NamedParameterJdbcTemplate getJdbcTemplate(Connection connection);

    /**
     * Utilizado para llenar datos de gridComponent.js desde algún Service
     *
     * @param sqlSelect        Consulta sql la cual puede tener solo el estamento del WHERE,
     *                         este método genera automaticamente la parte del SELECT utilizando
     *                         las columnas guardadas en SortedPagedFilter
     * @param connection       Database connection
     * @param sessionStatement SET statements that change the current session handling of specific information.
     * @param filter           : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * @since : 2.13.0.9
     */
    GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String sqlSelect,
            final IGridFilter filter,
            final Connection connection,
            final String sessionStatement
    );


    /**
     * Utilizado para llenar datos de gridComponent.js desde algún Service
     *
     * @param sqlSelect        Consulta sql la cual puede tener solo el estamento del WHERE,
     *                         este método genera automaticamente la parte del SELECT utilizando
     *                         las columnas guardadas en SortedPagedFilter
     * @param connection       Database connection
     * @param sessionStatement SET statements that change the current session handling of specific information.
     * @param filter           : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * @since : 2.13.0.9
     */
    GridInfo<Map<String, Object>> SQL_getRowsByQuery(
            final String withSelect,
            final String sqlSelect,
            final IGridFilter filter,
            final Connection connection,
            final String sessionStatement
    );
}
