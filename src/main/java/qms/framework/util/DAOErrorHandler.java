package qms.framework.util;

import Framework.Config.Utilities;
import java.util.Map;
import mx.bnext.core.errors.ClientAbortException;
import mx.bnext.core.util.KnownExceptions;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.util.DAOException;

public class DAOErrorHandler {

    private static final Logger LOGGER = Loggable.getLogger(DAOErrorHandler.class);

    /**
     * Se agrego solo para no ver el "warning" en todos los catch's
     */
    public void stackTraceHandle(Throwable e) {
        stackTraceHandle("-- catched by genericDaoImpl --", e);
    }

    public void stackTraceHandle(String msg, Throwable e) {
        final boolean clientAborted = KnownExceptions.hasClientAborted(e);
        if (clientAborted) {
            return;
        }
        LOGGER.error("stackTraceHandle() ... {}", msg, e);
        throw new DAOException("Framework.DAO.GenericDAOImpl.stackTraceHandle() ... '" + msg + "'", e);
    }

    public void stackTraceHandle(String msg, Map m, Exception e) {
        final boolean clientAborted = KnownExceptions.hasClientAborted(e);
        if (clientAborted) {
            throw new ClientAbortException(e);
        }
        if (m != null) {
            LOGGER.error("stackTraceHandle() ... {} {}", msg, Utilities.getSerializedObj(m), e);
        } else {
            LOGGER.error("stackTraceHandle() ... {}", msg, e);
        }
        throw new DAOException("stackTraceHandle() ... " + msg, e);
    }

    public void stackTraceHandle(String msg, Object[] m, Exception e) {
        final boolean clientAborted = KnownExceptions.hasClientAborted(e);
        if (clientAborted) {
            throw new ClientAbortException(e);
        }
        if (m != null) {
            LOGGER.error("stackTraceHandle() ... {} {}", msg, Utilities.getSerializedObj(m), e);
        } else {
            LOGGER.error("stackTraceHandle() ... {}", msg, e);
        }
        throw new DAOException("stackTraceHandle() ... " + msg, e);
    }

}
