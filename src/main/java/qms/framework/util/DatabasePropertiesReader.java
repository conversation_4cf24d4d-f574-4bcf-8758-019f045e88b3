package qms.framework.util;

import java.io.Serializable;
import qms.framework.dto.ConnectionConfigDTO;

public class DatabasePropertiesReader implements Serializable, IDatabaseConnectionProperties {

    private static final long serialVersionUID = 1L;

    private final ConnectionConfigDTO config;

    public DatabasePropertiesReader() {
        this.config = DatabaseUtil.getQmsConfig();
    }

    @Override
    public String getHibDialect() {
        return config.getHibDialect();
    }
    
    @Override
    public String getDriverClassName() {
        return config.getDriverClassName();
    }

    @Override
    public String getJdbcUrl() {
        return config.getJdbcUrl();
    }

    @Override
    public String getUsername() {
        return config.getUsername();
    }

    @Override
    public String getPassword() {
        return config.getPassword();
    }

    @Override
    public String getTransactionIsolation() {
        return config.getTransactionIsolation();
    }

    @Override
    public String getMaximumPoolSize() {
        return config.getMaximumPoolSize();
    }

}
