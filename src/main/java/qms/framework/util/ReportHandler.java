package qms.framework.util;

import DPMS.DAO.HibernateDAO_Document;
import Framework.Config.TextIntegerValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.exception.InvalidCipherDecryption;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.form.util.FixedField;
import qms.form.util.SurveyParserHelper;
import qms.framework.dto.HierarchyFieldDTO;
import qms.framework.dto.IReportColumnDTO;
import qms.framework.dto.ReportColumnDTO;
import qms.framework.dto.ReportDTO;
import qms.framework.dto.ReportHierarchyDTO;
import qms.framework.dto.SlimReportTransformedFieldColumnDTO;
import qms.framework.dto.SlimReportsTransformedFieldRuleDTO;
import qms.framework.entity.DatabaseQuery;
import qms.framework.entity.Report;
import qms.framework.entity.ReportColumn;
import qms.framework.entity.SlimReports;
import qms.framework.entity.SlimReportsGhostField;
import qms.framework.entity.SlimReportsSummaryGroupField;
import qms.framework.entity.SlimReportsSurveyFields;
import qms.framework.entity.SlimReportsSurveyFieldsFixed;
import qms.framework.entity.SlimReportsTransformedField;
import qms.framework.entity.SlimReportsTransformedFieldRule;
import qms.survey.dto.ExternalFieldDTO;
import qms.survey.logic.SurveyParser;
import qms.util.BindUtil;
import qms.util.GridFilter;

public class ReportHandler {

    private static final Logger LOGGER = Loggable.getLogger(ReportHandler.class);

    private final SurveyParserHelper parserHelper = new SurveyParserHelper();
    private final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();

    public static Long getLatestReportId(IUntypedDAO dao, String reportCode) {
        if (reportCode == null) {
            throw new IllegalArgumentException("reportId and reportCode are both null");
        }
        return dao.HQL_findLong(" "
            + " SELECT max(id)"
            + " FROM " + Report.class.getCanonicalName() + " r "
            + " WHERE r.code = :reportCode ", ImmutableMap.of("reportCode", reportCode), true, CacheRegion.REPORT, 0
        );
    }

    public static Long getPresetReportId(IUntypedDAO dao, Long reportId, String reportCode) {
        if (reportId == null && reportCode == null) {
            throw new IllegalArgumentException("reportId and reportCode are both null");
        } if (reportId == null) {
            reportId = ReportHandler.getLatestReportId(dao, reportCode);
        }
        return reportId;
    }

    public ReportDTO reportColumn(
            Long reportId,
            Module module,
            ILoggedUser loggedUser
    ) {
        return
            reportColumn(
                reportId,
                true,
                false,
                module,
                loggedUser,
                null
            );
    }

    @Nullable
    public ReportDTO reportColumn(
            Long reportId,
            boolean includeCatalogs,
            boolean skiFolderPermission,
            Module module,
            ILoggedUser loggedUser,
            Predicate<IReportColumnDTO> filterColumns
    ) {

        boolean managerAccess = loggedUser.isAdmin();
        String condition =  "";
        final Map<String, Object> params = new HashMap<>();
        if (!managerAccess && !skiFolderPermission) {
            params.put("userId", loggedUser.getId());
            condition += HibernateDAO_Document.FOLDER_PERMISSION
                    .replaceAll(":isDocMgr", "")
                    .replaceAll(":nodeId", "f.nodeId");
        }
        params.put("module", module.getKey());
        params.put("reportId", reportId);

        final IUntypedDAO dao = Utilities.getUntypedDAO();
        final ReportDTO result = dao.HQLT_findSimple(ReportDTO.class, " "
                + " SELECT new "+ ReportDTO.class.getCanonicalName() + " ("
                    + " f.id "
                    + ",f.lastModifiedDate "
                    + ",f.query.id "
                    + ",f.description "
                    + ",f.details "
                    + ",f.styles "
                    + ",f.scripts "
                    + ",f.documentMasterId "
                    + ",qry.rangeFilterType "
                    + ",qry.rangeFilterField "
                    + ",qry.rangeFilterName "
                    + ",sr.id "
                    + ",sr.code "
                    + ",sr.excelUploadEnabled "
                    + ",f.localTimeForDates "
                + " )"
                + " FROM " + Report.class.getCanonicalName() + " f"
                + " JOIN f.query qry"
                + " LEFT JOIN " + SlimReports.class.getCanonicalName() + " sr ON sr.reportId = f.id "
                + " WHERE "
                    + " f.id = :reportId"
                + " AND f.module = :module"
                + condition,
                params,
                true, CacheRegion.REPORT, 0
        );
        if (result == null) {
            return null;
        }
        List<IReportColumnDTO> columns = dao.HQL_findByQuery(" "
                        + " SELECT new "+ ReportColumnDTO.class.getCanonicalName() + " ("
                            + " c.id"
                            + ", c.type"
                            + ", CASE WHEN srf.alias IS NOT NULL AND srf.alias <> '' THEN srf.alias ELSE c.description END"
                            + ", c.gridOrder"
                            + ", c.width"
                            + ", c.hierarchyId"
                            + ", c.hierarchyCode"
                            + ", c.hierarchyDescription"
                            + ", c.sortPriority"
                            + ", c.sortDirection"
                            + ", c.groupingPriority"
                            + ", c.groupingDirection"
                            + ", c.reportId"
                            + ", c.onlyFilter"
                            + ", qc.id"
                            + ", qc.code"
                            + ", qc.description"
                            + ", qc.hierarchyLevelLabel"
                            + ", qc.hierarchyLevelOrder"
                            + ", qc.hierarchyReadonlyOrder"
                            + ", qc.hierarchyReadonlyLabel"
                            + ", qc.hierarchyReadonlyLevel"
                            + ", qc.hierarchyReadonlyVisible"
                            + ", qc.dataType"
                            + ", qc.queryId"
                            + ", sr.id"
                            + ", CASE WHEN sr.id IS NOT NULL AND srf.fieldName IS NOT NULL THEN 1 ELSE 0 END"
                        + " )"
                        + " FROM " + ReportColumn.class.getCanonicalName() + " c"
                        + " JOIN " + Report.class.getCanonicalName() + " f ON f.id = c.reportId"
                        + " JOIN c.queryColumn qc"
                        + " LEFT JOIN " + SlimReports.class.getCanonicalName() + " sr ON sr.reportId = :reportId "
                        + " LEFT JOIN " + SlimReportsSurveyFields.class.getCanonicalName() + " srf ON srf.slimReport.id = sr.id AND qc.code = srf.fieldName "
                        + " WHERE f.deleted = 0"
                        + " AND f.module = :module"
                        + " AND f.id = :reportId"
                        + condition,
                params,
                false,
                CacheRegion.REPORT,
                0
        );
        if (result.getSlimReportId() != null) {
            // Si se trata de un SLIM-REPORT, se limpia
            columns = getMissingSlimReportValues(columns, result.getSlimReportId(), Boolean.TRUE.equals(result.getExcelUploadEnabled()));
        }
        if (filterColumns == null) {
            result.setUnfilteredReportColumns(ImmutableList.of());
        } else {
            // Se respaldan columnas originales para usos posteriores
            result.setUnfilteredReportColumns(columns);
            // Se excluyen columnas que no cumplan con el filtro
            columns = columns.stream().filter(filterColumns).collect(Collectors.toList());
        }
        if (includeCatalogs) {
            configureColumnsCatalogData(result.getQueryId(), columns, loggedUser);
        }
        result.setReportColumns(columns);
        result.setModule(module.name());
        final Set<Long> hierarchyIds = columns.stream()
                .filter((column) -> Objects.equals(column.getType(), ReportColumnType.CATALOG_HIERARCHY.getValue()) && column.getHierarchyId() != null)
                .map(IReportColumnDTO::getHierarchyId)
                .collect(Collectors.toSet());
        if (!hierarchyIds.isEmpty() && includeCatalogs) {
            final List<Map<String, Object>> config = dao.HQL_findByQuery(" "
                + " SELECT new map("
                    + " q.id AS id"
                    + " ,q.computedHierarchyConfigJson as computedHierarchyConfigJson"
                + " )"
                + " FROM " + DatabaseQuery.class.getCanonicalName() + " q"
                + " WHERE " + BindUtil.parseFilterList("q.id", hierarchyIds),
                params,
                true,
                CacheRegion.REPORT,
                0
            );
            final Gson gson = new Gson();
            final List<ReportHierarchyDTO> hierarchies = config.stream()
                    .map((hierarchy) -> {
                        final Long queryId = (Long) hierarchy.get("id");
                        if (hierarchy.get("computedHierarchyConfigJson") != null) {
                            final List<HierarchyFieldDTO> fields = gson.fromJson(
                                    hierarchy.get("computedHierarchyConfigJson").toString(),
                                    new TypeToken<List<HierarchyFieldDTO>>() {
                                    }.getType()
                            );
                            final List<ExternalFieldDTO> hierarchyFields = fields.stream()
                                    .map((field) -> mapHierarchyFieldDTO(queryId, field, loggedUser))
                                    .collect(Collectors.toList());
                            return new ReportHierarchyDTO(queryId, hierarchyFields);
                        } else {
                            return new ReportHierarchyDTO(queryId);
                        }
                    }).collect(Collectors.toList());
            result.setHierarchies(hierarchies);
        }
        return result;
    }

    @Nonnull
    private List<IReportColumnDTO> getMissingSlimReportValues(List<IReportColumnDTO> columns, Long slimReportId, boolean excelUploadEnabled) {
        IUntypedDAO dao = Utilities.getUntypedDAO();

        // Se obtienen los campos FIXED de los reportes SLIM
        List<TextIntegerValue> fixedFields = dao.HQLT_findByQuery(TextIntegerValue.class, " "
            + " SELECT new " + TextIntegerValue.class.getCanonicalName() + "("
                    + " f.alias "
                    + ",f.fixedFieldEnumValue "
                + " ) "
            + " FROM " + SlimReportsSurveyFieldsFixed.class.getCanonicalName() + " f "
            + " WHERE "
                + " f.slimReport.id = :slimReportId",
            ImmutableMap.of("slimReportId", slimReportId), true, CacheRegion.REPORT, 0
        );

        final Set<FixedField> fixedFieldSet = fixedFields.stream()
                .map(TextIntegerValue::getValue)
                .map(FixedField::fromValue)
                .collect(Collectors.toSet());
        final Map<FixedField, String> fixedFieldMap = fixedFields.stream().filter(f -> !f.getText().startsWith("ID: "))
                .collect(Collectors.toMap(
                        c -> FixedField.fromValue(c.getValue()),
                        TextIntegerValue::getText
                ));

        // Se limpian valores invalidos de Slim-Reports
        columns = columns.stream().filter(c -> (
            // columnas normales
            c.getSlimReportId() == null
                    // columnas explicitas de slim-reports
                    || Objects.equals(c.getInSlimReportColumn(), 1)
                    || (
                    // columnas fijas de slim-reports
                    fixedFieldSet.contains(FixedField.fromAlias(c.getQueryColumnCode()))
            )
        )).peek(c -> {
            FixedField fixedField = FixedField.fromAlias(c.getQueryColumnCode());
            if (fixedFieldSet.contains(fixedField) && fixedFieldMap.get(fixedField) != null) {
                c.setDescription(
                    fixedFieldMap.get(fixedField)
                );
            }
        }).collect(Collectors.toList());

        // Se construyen columnas DUMMY para el excel upload
        if (excelUploadEnabled) {
            columns.addAll(
                this.getGhostFieldColumns(dao, slimReportId)
            );
        }

        // Se construyen columnas DUMMY para campos basados en reglas
        columns.addAll(
            this.getTransformedFieldColumns(dao, slimReportId)
        );

        // Se construyen columnas DUMMY para campos basados en reglas
        columns.addAll(
            this.getSummaryFieldColumns(dao, slimReportId)
        );
        return columns;
    }

    @Nonnull
    private List<? extends IReportColumnDTO> getTransformedFieldColumns(IUntypedDAO dao, Long slimReportId) {
        // Columnas sin valor que se calcularán en FRONT a partir de la configuración de `SlimReportsTransformedFieldRule`
        List<SlimReportTransformedFieldColumnDTO> columns =  dao.HQLT_findByQuery(SlimReportTransformedFieldColumnDTO.class, " "
                + " SELECT new "+ SlimReportTransformedFieldColumnDTO.class.getCanonicalName() + " ("
                    + " srf.id "
                    + ",sr.reportId "
                    + ",sr.id "
                    + ",srf.code "
                    + ",CASE WHEN srf.alias IS NOT NULL AND srf.alias <> '' THEN srf.alias ELSE srf.description END "
                    + ",srf.order "
                + " )"
                + " FROM " + SlimReportsTransformedField.class.getCanonicalName() + " srf"
                + " JOIN srf.slimReport sr"
                + " WHERE"
                    + " sr.id = :slimReportId"
                    + " AND srf.deleted = 0"
                    + " AND sr.deleted = 0"
                ,
                ImmutableMap.of(
                    "slimReportId", slimReportId
                ),
                true,
                CacheRegion.REPORT,
                0
        ).stream().peek(c -> {
            c.setType(ReportColumnType.TRANSFORMED_FIELD.getValue());
            c.setRules(new TreeSet<SlimReportsTransformedFieldRuleDTO>());
        }).collect(Collectors.toList());
        if (columns.isEmpty()) {
            return columns;
        }
        List<SlimReportsTransformedFieldRuleDTO> rules =  dao.HQLT_findByQuery(SlimReportsTransformedFieldRuleDTO.class, " "
                    + " SELECT new " + SlimReportsTransformedFieldRuleDTO.class.getCanonicalName() + " ("
                        + " srf.ruleDateValue "
                        + ",srf.ruleNumberValue "
                        + ",srf.orderValue "
                        + ",srf.evalFieldValue "
                        + ",srf.ruleFieldValue "
                        + ",srf.transformedFieldId "
                        + ",srf.evalFieldCode "
                        + ",srf.evalFieldSource "
                        + ",srf.evalFieldText "
                        + ",srf.evalFieldType "
                        + ",srf.finalValue "
                        + ",srf.ruleFieldCode "
                        + ",srf.ruleFieldSource "
                        + ",srf.ruleFieldText "
                        + ",srf.ruleFieldType "
                        + ",srf.ruleStringValue "
                        + ",srf.typeName "
                        + ",srf.typeFinalValue "
                    + " )"
                    + " FROM " + SlimReportsTransformedFieldRule.class.getCanonicalName() + " srf"
                    + " JOIN srf.transformedField t "
                    + " JOIN srf.slimReport sr"
                    + " WHERE"
                        + " sr.id = :slimReportId"
                        + " AND t.deleted = 0"
                        + " AND sr.deleted = 0"
                ,
                ImmutableMap.of(
                        "slimReportId", slimReportId
                ),
                true,
                CacheRegion.REPORT,
                0
        );
        if (rules.isEmpty()) {
            return columns;
        }
        for (SlimReportsTransformedFieldRuleDTO rule : rules) {
            columns.stream().filter(c -> Objects.equals(c.getTransformedFieldId(), rule.getTransformedFieldId())).findFirst().ifPresent(c -> {
                if (c.getRules() instanceof TreeSet) {
                    ((TreeSet<SlimReportsTransformedFieldRuleDTO>)c.getRules()).add(rule);
                }
            });
        }
        return columns;
    }

    @Nonnull
    private List<ReportColumnDTO> getGhostFieldColumns(IUntypedDAO dao, Long slimReportId) {
        // Columnas sin valor que se cargarán desde EXCEL
        return dao.HQLT_findByQuery(ReportColumnDTO.class, " "
                + " SELECT new "+ ReportColumnDTO.class.getCanonicalName() + " ("
                    + " sr.reportId "
                    + ",sr.id "
                    + ",srf.code "
                    + ",CASE WHEN srf.alias IS NOT NULL AND srf.alias <> '' THEN srf.alias ELSE srf.description END "
                    + ",srf.order "
                + " )"
                + " FROM " + SlimReportsGhostField.class.getCanonicalName() + " srf"
                + " JOIN srf.slimReport sr"
                + " WHERE"
                    + " sr.id = :slimReportId"
                    + " AND srf.deleted = 0"
                    + " AND sr.deleted = 0"
                ,
                ImmutableMap.of(
                    "slimReportId", slimReportId
                ),
                true,
                CacheRegion.REPORT,
                0
        ).stream().peek(c -> c.setType(ReportColumnType.GHOST_FIELD.getValue())).collect(Collectors.toList());
    }
    @Nonnull
    private List<ReportColumnDTO> getSummaryFieldColumns(IUntypedDAO dao, Long slimReportId) {
        // Columnas sin valor que se cargarán desde EXCEL
        return dao.HQLT_findByQuery(ReportColumnDTO.class, " "
                + " SELECT new "+ ReportColumnDTO.class.getCanonicalName() + " ("
                    + " sr.reportId "
                    + ",sr.id "
                    + ",srf.code "
                    + ",CASE WHEN srf.alias IS NOT NULL AND srf.alias <> '' THEN srf.alias ELSE srf.description END "
                    + ",srf.order "
                    + ",srf.groupValueFieldName "
                    + ",srf.aggregateFunctionForResultValue "
                + " )"
                + " FROM " + SlimReportsSummaryGroupField.class.getCanonicalName() + " srf"
                + " JOIN srf.slimReport sr"
                + " WHERE"
                    + " sr.id = :slimReportId"
                    + " AND srf.deleted = 0"
                    + " AND sr.deleted = 0"
                ,
                ImmutableMap.of(
                    "slimReportId", slimReportId
                ),
                true,
                CacheRegion.REPORT,
                0
        ).stream().peek(c -> {
            c.setType(ReportColumnType.SUMMARY_FIELD.getValue());
            c.setGroupingPriority(979);
        }).collect(Collectors.toList());
    }

    private ExternalFieldDTO mapHierarchyFieldDTO(final Long queryId, final HierarchyFieldDTO field, final ILoggedUser loggedUser) {
        final ExternalFieldDTO externalDto = parserHelper.mapHierarchyFieldDTO(field);
        if (Objects.equals(field.getReadonly(), 1)) {
            return externalDto;
        }
        final GridFilter filter = new GridFilter();
        filter.setPage(0);
        filter.setDisableLoadThreshold(SurveyParser.MAX_CATALOG_ITEMS);
        final GridInfo<Map<String, Object>> contents = parserHelper.getDatabaseQueryHierarchyByLevelRows(filter, queryId, field.getLevel(), loggedUser);
        externalDto.setCatalogContents(contents);
        return externalDto;
    }

    private void configureColumnsCatalogData(
            final Long databaseQueryId,
            final List<IReportColumnDTO> columns,
            final ILoggedUser loggedUser
    ) {
        columns.stream()
                .filter((column) -> Objects.equals(column.getType(), ReportColumnType.CATALOG_TEXT.getValue()))
                .forEach((column) -> {
                    try {
                        loadColumnCatalogValues(databaseQueryId, column, loggedUser);
                    } catch (SQLException | InvalidCipherDecryption ex) {
                        LOGGER.error(
                                "Failed to load catalog values for query columns {} and column {}.",
                                databaseQueryId,
                                column.getQueryColumnId(),
                                ex);
                    }
                });
    }

    private void loadColumnCatalogValues(
            final Long databaseQueryId,
            final IReportColumnDTO column,
            final ILoggedUser loggedUser
    ) throws InvalidCipherDecryption, SQLException {
        try {
            final List<Map<String, Object>> catalogValues = queryHandler.getCatalogValues(
                    databaseQueryId,
                    column.getQueryColumnCode(),
                    loggedUser
            );
            if (catalogValues != null) {
                final List<Object> values = catalogValues.stream()
                        .map(item -> item.get(column.getQueryColumnCode()))
                        .collect(Collectors.toList());
                column.setCatalogValues(values);
            }
        } catch (Exception ex) {
            LOGGER.error(
                    "Failed to load catalog values for query {} and column {}.",
                    databaseQueryId,
                    column.getQueryColumnId(),
                    ex);
        }
    }

}
