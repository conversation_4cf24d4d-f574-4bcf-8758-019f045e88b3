package qms.framework.util;

import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IFilesDAO;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentSimple;
import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.login.dto.ClientBrowserDTO;
import bnext.login.util.ClientBrowserUtils;
import bnext.reference.document.FileRef;
import com.google.common.collect.ImmutableMap;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import mx.bnext.cipher.Encryptor;
import mx.bnext.core.file.IFileData;
import mx.bnext.core.security.ISecurityUser;
import mx.bnext.core.util.Loggable;
import org.apache.commons.io.FilenameUtils;
import qms.access.dto.LoggedUser;
import qms.document.dto.ShareDocumentDTO;
import qms.document.entity.ShareDocument;
import qms.framework.entity.DocumentLink;
import qms.framework.entity.DocumentTypeLink;
import qms.framework.entity.ExternalFileType;
import qms.framework.entity.ExternalUserAgent;
import qms.framework.entity.ShareFileType;
import qms.framework.entity.ShareUserAgent;
import qms.framework.file.FileManager;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class DocumentLinkUtil extends Loggable {

    private static final String HQL_SELECT_LINK = ""
            + " SELECT c"
            + " FROM " + DocumentLink.class.getCanonicalName() + " c"
            + " WHERE c.code = :token";

    public static final String HQL_SELECT_TYPE = ""
            + " SELECT c"
            + " FROM " + DocumentTypeLink.class.getCanonicalName() + " c"
            + " WHERE EXISTS ("
            + "     SELECT 1"
            + "     FROM " + Document.class.getCanonicalName() + " d"
            + "     WHERE d.id = :id"
            + "     AND d.documentType.id = c.id"
            + " )";

    private static final String HQL_VALID_SHARE_USER_AGENT = ""
            + " SELECT c.userAgent.id"
            + " FROM " + ShareUserAgent.class.getCanonicalName() + " c"
            + " WHERE c.id.documentTypeId = :documentTypeId"
            + " AND c.userAgent.code like '%:userAgent%'";

    private static final String HQL_VALID_EXTERNAL_USER_AGENT = ""
            + " SELECT c.userAgent.id"
            + " FROM " + ExternalUserAgent.class.getCanonicalName() + " c"
            + " WHERE c.id.documentTypeId = :documentTypeId"
            + " AND c.userAgent.code like '%:userAgent%'";

    private static final String HQL_VALID_SHARED_FILE = ""
            + " SELECT c.fileType.id"
            + " FROM " + ShareFileType.class.getCanonicalName() + " c"
            + " WHERE c.id.documentTypeId = :documentTypeId"
            + " AND c.fileType.code = :mimeType OR c.fileType.description = :extension";

    private static final String HQL_VALID_EXTERNAL_FILE = ""
            + " SELECT c.fileType.id"
            + " FROM " + ExternalFileType.class.getCanonicalName() + " c"
            + " WHERE c.id.documentTypeId = :documentTypeId"
            + " AND c.fileType.code = :mimeType OR c.fileType.description = :extension";

    private static final String HQL_SELECT_FILE = ""
            + " SELECT c.fileContent"
            + " FROM " + DocumentSimple.class.getCanonicalName() + " c"
            + " WHERE c.id = :id";
    

    private static final String HQ_FIT_DOCUMENT_SCREEN_TYPE = ""
            + " SELECT c.documentType.pdfViewerFitDocumentScreen"
            + " FROM " + DocumentSimple.class.getCanonicalName() + " c"
            + " WHERE c.id = :id";
    
    private static final String DOCUMENT_LINK_CODES_KEY = "DOCUMENT_LINK_CODES";

    private final Settings settings;
    private final DocumentLink link;
    private final DocumentTypeLink documentType;
    private final boolean isSharedLink;
    private final boolean isExternalLink;
    private final boolean isViewerLink;
    private final IFilesDAO dao;
    private String error = "";

    public DocumentLinkUtil(DocumentLink link, IFilesDAO dao, DocumentLink.TYPE typeLink) {
        this.settings = Utilities.getSettings();
        this.link = link;
        final DocumentTypeLink type = loadDocumentType(link.getDocumentId(), dao);
        this.documentType = type;
        this.dao = dao;
        isSharedLink = Objects.equals(DocumentLink.TYPE.SHARED_LINK, typeLink);
        isExternalLink = Objects.equals(DocumentLink.TYPE.EXTERNAL_LINK, typeLink);
        isViewerLink = Objects.equals(DocumentLink.TYPE.VIEWER_LINK, typeLink);
    }

    public static DocumentLink loadDocumentLink(final String token, IFilesDAO dao) {
        final String plainToken = Encryptor.decrypt(token, Utilities.getSettings().getSystemId());
        return (DocumentLink) dao.HQL_findSimpleObject(HQL_SELECT_LINK, "token", plainToken);
    }

    public static DocumentTypeLink loadDocumentType(Long documentId, IFilesDAO dao) {
        return (DocumentTypeLink) dao.HQL_findSimpleObject(HQL_SELECT_TYPE, "id", documentId);
    }

    public static void prepareShareLink(Long documentId, IUntypedDAO dao, ShareDocumentDTO shareDocument, LoggedUser loggedUser) {
        DocumentLink link = generateShareToken(shareDocument, dao, loggedUser);
        prepareLink(link, shareDocument);
    }

    public static void prepareExternalLink(Long documentId, IUntypedDAO dao, ShareDocumentDTO shareDocument, LoggedUser loggedUser) {
        DocumentLink link = generateExternalToken(shareDocument, dao, loggedUser);
        prepareLink(link, shareDocument);
    }

    public void executeDownload(HttpServletResponse response, ISecurityUser user) throws IOException, SQLException {
        link.update("downloads", link.getDownloads() + 1, dao);
        final FileRef file = loadFile();        
        final FileManager fileManager = new FileManager();
        fileManager.writeHeaders(response, file.getDescription(), file.getContentType(), file.getContentSize(), false);
        final IFileData metadata = fileManager.newMetadataInstance(file);
        try (final ServletOutputStream output = response.getOutputStream()) {
            fileManager.writeFileContentToOutput(metadata, output, user);
        }
    }

    public Boolean getPdfViewerFitDocumentScreen() {
        return dao.HQL_findSimpleInteger(HQ_FIT_DOCUMENT_SCREEN_TYPE, "id", link.getDocumentId()).equals(1);
    }

    public boolean hasFile(Long documentId) {
        return dao.getBean(IDocumentDAO.class).hasDocumentFile(documentId);
    }

    public FileRef loadFile() {
        final Long documentId = link.getDocumentId();
        final String masterId = dao.HQL_findSimpleString(" "
                + " SELECT c.masterId"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", documentId)
        );
        final Long latestDocumentId = dao.HQL_findLong(" "
                + " SELECT c.id"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0 "
                + " AND c.masterId = :masterId"
                + " AND c.status IN ("
                        + Document.STATUS.ACTIVE.getValue()
                        + ", " + Document.STATUS.IN_EDITION.getValue()
                + " )",
                ImmutableMap.of("masterId", masterId)
        );
        return (FileRef) dao.HQL_findSimpleObject(HQL_SELECT_FILE, "id", latestDocumentId);
    }

    public boolean isValidSession(final HttpServletRequest request) {
        final boolean valid = validLink(request, true, false);
        if (!valid) {
            return false;
        }
        final HttpSession session = request.getSession();
        final Map<String, String> linkCodes = ((Map)session.getAttribute(DOCUMENT_LINK_CODES_KEY));
        if (linkCodes == null || !linkCodes.containsKey(link.getCode())) {
            return false;
        }     
        final String viewToken = linkCodes.get(link.getCode());
        final String id = Encryptor.decrypt(viewToken, Utilities.getSettings().getSystemId() + "-" + link.getCode());
        return session.getId().equals(id);
    }

    public void putSessionToken(final HttpSession session) {
        if (session.getAttribute(DOCUMENT_LINK_CODES_KEY) == null) {
            session.setAttribute(DOCUMENT_LINK_CODES_KEY, new ConcurrentHashMap<String, String>(1));
        }
        if (((Map)session.getAttribute(DOCUMENT_LINK_CODES_KEY)).containsKey(link.getCode())) {
            return;
        }
        link.update("views", link.getViews() + 1, dao);
        final String viewToken = Encryptor.encrypt(session.getId(), Utilities.getSettings().getSystemId() + "-" + link.getCode());
        getLogger().trace("Persisting to session link: {} to session: {}", link.getCode(), session.getId());
        ((Map)session.getAttribute(DOCUMENT_LINK_CODES_KEY)).putIfAbsent(link.getCode(), viewToken);
    }

    public boolean validLink(HttpServletRequest request) {
        return validLink(request, false, false);
    }

    public boolean validLink(HttpServletRequest request, boolean skipDownloads, boolean ignoreDownloadLimit) {
        if (!canShare()) {
            error = "disabled_shared_document";
            return false;
        } else if (!allowedExternalLink()) {
            error = "disabled_external_link";
            return false;
        }
        if (!validUserAgent(request)) {
            error = "invalid_browser";
            return false;
        }
        if (!skipDownloads && !validNumberDownloads() && !ignoreDownloadLimit) {
            error = "exceeded_download_limit";
            return false;
        }
        if (!validLinkLife()) {
            error = "expired_shared_document";
            return false;
        }

        if (!validFileType()) {
            error = "forbiden_shared_document";
            return false;
        }
        return true;
    }

    public String getError() {
        return error;
    }

    private static DocumentLink getLink(ShareDocumentDTO shareDocument, Date expiration, IUntypedDAO dao, LoggedUser loggedUser) {
        DocumentLink link = new DocumentLink(-1L);
        ShareDocument sd = new ShareDocument(shareDocument.getId());
        link.setCode(UUID.randomUUID().toString());
        link.setDocumentId(shareDocument.getDocumentId());
        link.setShareDocument(sd);
        link.setDownloads(0L);
        link.setExpiration(expiration);
        dao.makePersistent(link, loggedUser.getId());
        return link;
    }

    private static DocumentLink generateExternalToken(ShareDocumentDTO shareDocument, IUntypedDAO dao, LoggedUser loggedUser) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        DocumentTypeLink type = (DocumentTypeLink) dao.HQL_findSimpleObject(DocumentLinkUtil.HQL_SELECT_TYPE, "id", shareDocument.getDocumentId());
        c.add(Calendar.DATE, type.getExternalLife().intValue());
        return getLink(shareDocument, c.getTime(), dao, loggedUser);
    }

    private static DocumentLink generateShareToken(ShareDocumentDTO shareDocument, IUntypedDAO dao, LoggedUser loggedUser) {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, shareDocument.getShareLife().intValue());
        return getLink(shareDocument, c.getTime(), dao, loggedUser);
    }

    private static void prepareLink(DocumentLink link, ShareDocumentDTO shareDocument) {
        final Settings settings = Utilities.getSettings();
        final String token = Encryptor.encrypt(link.getCode(), settings.getSystemId());
        shareDocument.setValid(true);
        final String url = SettingsUtil.getAppUrlNoSlash();
        shareDocument.setShareLink(url + DocumentLink.TYPE.SHARED_LINK.getAction() + "?token=" + token);
        shareDocument.setExternalLink(url + DocumentLink.TYPE.EXTERNAL_LINK.getAction() + "?token=" + token);
        shareDocument.setViewerLink(url + DocumentLink.TYPE.VIEWER_LINK.getAction() + "?token=" + token);
    }

    private boolean canShare() {
        if (isSharedLink
                && (settings.getShareDocuments().equals(0) || documentType.getShareDocuments().equals(0))) {
            getLogger().error("System does not allow to share files. Token used: '{}'", link.getCode());
            return false;
        }
        return true;
    }

    private boolean allowedExternalLink() {
        if (isExternalLink
                && (settings.getExternalLink().equals(0) || documentType.getExternalLink().equals(0))) {
            getLogger().error("System does not allow to open document in external app. Token used: '{}'", link.getCode());
            return false;
        }
        return true;
    }

    private boolean validUserAgent(HttpServletRequest request) {
        final ClientBrowserDTO browser = ClientBrowserUtils.parseBrowser(request, false);
        final String userAgent = browser.getUserAgent();
        if (userAgent == null || userAgent.trim().isEmpty()) {
            getLogger().error("System does not allow empty User Agent", link.getCode());
            return false;
        }
        String hql = null;
        if (isSharedLink && documentType.getShareRestrictUserAgent().equals(1)) {
            hql = HQL_VALID_SHARE_USER_AGENT.replaceAll(":userAgent", userAgent);
        } else if (isExternalLink && documentType.getShareRestrictUserAgent().equals(1)) {
            hql = HQL_VALID_EXTERNAL_USER_AGENT.replaceAll(":userAgent", userAgent);

        }
        if (hql != null) {
            Long validUserAgent = dao.HQL_findSimpleLong(hql, "documentTypeId", documentType.getId());
            if (validUserAgent == 0) {
                getLogger().error("System does not allow this User Agent: '{}' for shared file. Token used: '{}'", userAgent, link.getCode());
                return false;
            }
        }
        return true;
    }

    private boolean validNumberDownloads() {
        if(link.getShareDocument() == null && (isSharedLink || isViewerLink)) {
            getLogger().error("Token provided '{}' has expired.", link.getCode());
            return false;
        }
        if (isSharedLink && link.getDownloads() >= link.getShareDocument().getShareMaxDownloads()) {
            getLogger().error("Maximum download({}) reached for token: '{}'", link.getShareDocument().getShareMaxDownloads(), link.getCode());
            return false;
        } else if (isViewerLink && link.getViews() >= link.getShareDocument().getShareMaxViews()) {
            getLogger().error("Maximum views({}) reached for token: '{}'", link.getShareDocument().getShareMaxViews(), link.getCode());
            return false;
        } else if (isExternalLink && link.getDownloads() >= documentType.getExternalMaxDownloads()) {
            getLogger().error("Maximum external download reached for token: '{}'", link.getCode());
        }
        return true;
    }

    private boolean validLinkLife() {
        if (link.getExpiration().before(new Date())) {
            getLogger().error("Token provided '{}' has expired on {}", link.getCode(), link.getExpiration());
            return false;
        }
        return true;
    }

    private boolean validFileType() {
        FileRef file = loadFile();
        Map<String, Object> params = new HashMap<>(3);
        params.put("extension", FilenameUtils.getExtension(file.getDescription()));
        params.put("mimeType", file.getContentType());
        params.put("documentTypeId", documentType.getId());
        String hql = null;
        if (isSharedLink && documentType.getShareRestrictFileType().equals(1)) {
            hql = HQL_VALID_SHARED_FILE;
        } else if (isExternalLink && documentType.getExternalRestrictFileType().equals(1)) {
            hql = HQL_VALID_EXTERNAL_FILE;
        }
        if (hql != null) {
            Long validFileType = dao.HQL_findSimpleLong(hql, params);
            if (validFileType == 0) {
                getLogger().error("System does not allow download of this mime type: '{}'. Token used: ''", file.getContentType(), link.getCode());
                return false;
            }
        }
        return true;
    }
    
     public static Integer isEarlierVersionDoc (Long documentId, IUntypedDAO dao) {
        return dao.HQL_findSimpleInteger(""
            +  "SELECT c.status"
            + " FROM " + DocumentSimple.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id", documentId);
    }

}
