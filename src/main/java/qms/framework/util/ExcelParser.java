package qms.framework.util;

import Framework.Config.Utilities;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.ResourceBundle;
import java.util.Set;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.util.Loggable;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import qms.framework.dto.DataExchangeDTO;
import qms.framework.dto.ElapsedDataDTO;

public class ExcelParser extends Loggable implements IDataParser, IExcelParser {

    private static final Logger LOGGER = getLogger(ExcelParser.class);
    public static final String XLS_EXTENSION = "xls";
    private static final String OPTIONAL_VALUE = "-";
    public static final String XLSX_EXTENSION = "xlsx";
    
    public static final String EXCEL_2003_CONTENT_TYPE = "application/vnd.ms-excel";
    public static final String EXCEL_2007_CONTENT_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    public static final Integer MAX_CHARACTERS_CELL = 4000;

    public static final List<String> VALID_CONTENT_TYPES = Arrays.asList(new String[]{
        EXCEL_2003_CONTENT_TYPE,
        EXCEL_2007_CONTENT_TYPE
    });

    private final Locale locale;
    private final ResourceBundle tags;

    public ExcelParser(final Locale loc) {
        locale = loc;
        tags = LocaleUtil.getI18n(ExcelParser.class.getCanonicalName(), loc);
    }

    public String getTag(final String key) {
        return LocaleUtil.getTag(key, tags, locale);
    }
    
    @Override
    public DataExchangeDTO parse(final Path file, final String originalFilename, final String contentType, final Integer page)
            throws DataExchangeParseError {
        final String filename;
        if (originalFilename != null && !originalFilename.isEmpty()) {
            filename = originalFilename;
        } else {
            filename = file.toFile().getName();
        }
        String validContentType;
        try {
            validContentType = FileUtils.validContentType(file, contentType);
        } catch (IOException ex) {
            validContentType = null;
        }
        if (!VALID_CONTENT_TYPES.contains(validContentType)) {
            return throwInvalidContentType(contentType, filename);
        }
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        Workbook wb = null;
        OPCPackage opcPackage = null;
        try {
            final String extension = FilenameUtils.getExtension(filename);
            if (extension == null || extension.isEmpty()) {
                return throwUnsupportedExtension(extension, filename);
            }
            switch (extension) {
                case XLSX_EXTENSION:
                    opcPackage = OPCPackage.open(file.toFile(), PackageAccess.READ);
                    wb = new XSSFWorkbook(opcPackage);
                    break;
                case XLS_EXTENSION:
                    wb = WorkbookFactory.create(file.toFile(), null, true);
                    break;
                default:
                    return throwUnsupportedExtension(extension, filename);
            }
            if (wb == null) {
                return throwUnsupportedExtension(extension, filename);
            }
            MeasureTime.stop(tStart, "Reading Workbook of file " + filename);
            if (page >= wb.getNumberOfSheets()) {
                return throwInvalidSheetNumber(page, wb, filename);
            }
            final ElapsedDataDTO sStart = MeasureTime.start(getClass());
            final Sheet sheet = wb.getSheetAt(page);
            MeasureTime.stop(sStart, "Reading excel sheet " + page + " of file " + filename);
            LOGGER.info("Parsing sheet {} of file {}", new Object[]{
                page, filename
            });
            final ElapsedDataDTO dStart = MeasureTime.start(getClass());
            final DataExchangeDTO data = processSheet(sheet, filename);
            MeasureTime.stop(dStart, "Processing sheet " + page + " of file " + filename);
            return data;
        } catch (final DataExchangeDuplicatedHeaderError | InvalidSheetNumberError | MaximumCharactersError ex) {
            throw ex;
        } catch (final Exception ex) {
            return logException(ex, filename);
        } finally {
            final ElapsedDataDTO cStart = MeasureTime.start(getClass());
            if (opcPackage != null) {
                opcPackage.revert();
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException ex) {
                    LOGGER.error("There was an error closng workbook {} {}", filename, ex);
                }
            }
            MeasureTime.stop(cStart, "Closing excel file " + filename);
            MeasureTime.stop(tStart, "Parsing excel file " + filename);
        }
    }

    private DataExchangeDTO processSheet(
            final Sheet sheet,
            final String filename
    ) throws InvalidFormatException, DataExchangeDuplicatedHeaderError, MaximumCharactersError {
        final List<List<String>> sheetValues = new ArrayList<>();
        Integer countRows = 0;
        for (final Row row : sheet) {
            final short lastCellIndex = row.getLastCellNum();
            if (lastCellIndex <= 0) {
                continue;
            }
            final List<String> rowValues = new ArrayList<>(lastCellIndex);
            for (int i = 0; i < (int) lastCellIndex; i++) {
                final String value = getCellStringValue(filename, row.getCell(i));
                rowValues.add(value);
            }
            sheetValues.add(rowValues);
            if (row.getRowNum() == 0) {
                final Set<String> uniqueHeaders = new HashSet<>(rowValues);
                if (!Objects.equals(uniqueHeaders.size(), rowValues.size())) {
                    final String message = getTag("duplicatedHeaders");
                    throw new DataExchangeDuplicatedHeaderError(filename, message);
                }
            }
            countRows++;
        }
        final DataExchangeDTO dto = new DataExchangeDTO();
        if (sheetValues.size() > 1) {
            final int headerSize = sheetValues.get(0).size();
            for (final List<String> values : sheetValues) {
                if (values.size() >= headerSize) {
                    continue;
                }
                while (values.size() < headerSize) {
                    values.add("");
                }
            }
        }
        dto.setValues(sheetValues);
        dto.setNumberLines(countRows);
        return dto;
    }

    public String getCellStringValue(final String filename, final Cell cell) throws MaximumCharactersError {
        String r = null;
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case BLANK:
                r = ("");
                break;
            case NUMERIC:
                double numericValue = cell.getNumericCellValue();
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date = DateUtil.getJavaDate(numericValue);
                    return Utilities.formatDateBy(date, "dd/MM/yyyy");
                }
                if (numericValue % 1 == 0) {
                    r = String.valueOf(Math.round(numericValue));
                } else {
                    r = String.valueOf(numericValue);
                }
                break;
            case STRING:
                r = (cell.getStringCellValue());
                break;
            case FORMULA:
                r = (cell.getCellFormula());
                break;
            case BOOLEAN:
                r = (String.valueOf(cell.getBooleanCellValue()));
                break;
            case ERROR:
                r = (String.valueOf(cell.getErrorCellValue()));
                break;
            default:
                try {
                Date dte = getDateCellValue(filename, cell);
                if (dte != null) {
                    r = Utilities.formatDateBy(dte, "yyyy-MM-dd HH:mm:ss");
                }
                if (r == null) {
                    r = (cell.getStringCellValue());
                }
            } catch (Exception e) {
                try {
                    r = (cell.getStringCellValue());
                } catch (Exception e2) {
                    LOGGER.error("/Invalid data... Parsing excel ... " + e2.getLocalizedMessage());
                    return "";
                }
                LOGGER.error("There was an error ", e);
            }
            break;
        }
        final String result = StringUtils.trimToEmpty(r);
        if (OPTIONAL_VALUE.equals(result)) {
            return StringUtils.EMPTY;
        } else {
            if (result != null && result.length() > MAX_CHARACTERS_CELL) {
                final String message = getTag("maximumCharactersExceeded")
                        .replace("{maxCharacters}", MAX_CHARACTERS_CELL.toString());
                throw new MaximumCharactersError(filename, message);
            }
            return result;
        }
    }

    private Date getDateCellValue(final String filename, final Cell c) throws MaximumCharactersError {
        Date dte = null;
        try {
            dte = c.getDateCellValue();
        } catch (Exception e) {
            String t = getCellStringValue(filename, c);
            switch (t.length()) {
                case 16:
                    dte = Utilities.parseDateBy(t, "yyyy-MM-dd HH:mm");
                    break;
                case 19:
                    dte = Utilities.parseDateBy(t, "yyyy-MM-dd HH:mm:ss");
                    break;
                case 10:
                    if (t.matches("^[0-9][0-9][0-9][0-9].*")) {
                        dte = Utilities.parseDateBy(t, "yyyy-MM-dd");
                    } else if (t.matches(".*[0-9][0-9][0-9][0-9]$")) {
                        dte = Utilities.parseDateBy(t, "dd-MM-yyyy");
                    }
                    break;
                default:
                    break;
            }
            LOGGER.error("There was an error ", e);
        }
        return dte;
    }

    private DataExchangeDTO logException(final Exception ex, final String filename)
            throws DataExchangeParseError {
        String error = null;
        if (ex instanceof DataExchangeParseError) {
            error = ex.getMessage();
        }
        if (error == null || error.isEmpty()) {
            error = qms.framework.util.ExceptionUtils.getRootCauseMessage(ex);
        }
        final String message = getTag("parsingError")
                .replace("{error}", error);
        throw new DataExchangeParseError(filename, message, ex);
    }

    private DataExchangeDTO throwInvalidSheetNumber(final Integer page, Workbook wb, final String filename)
            throws InvalidSheetNumberError {
        final String message = getTag("unsupportedSheetNumber")
                .replace("{sheetNumber}", page == null ? "null" : Integer.toString(page + 1))
                .replace("{totalSheetNumber}", Integer.toString(wb.getNumberOfSheets()));
        throw new InvalidSheetNumberError(filename, message);
    }

    private DataExchangeDTO throwInvalidContentType(final String contentType, final String filename)
            throws DataExchangeParseError {
        final String message = getTag("unsupportedContentType")
                .replace("{contentType}", contentType);
        throw new DataExchangeParseError(filename, message);
    }

    private DataExchangeDTO throwUnsupportedExtension(final String extension, final String filename)
            throws DataExchangeParseError {
        final String message = getTag("unsupportedExtension")
                .replace("{extension}", extension);
        throw new DataExchangeParseError(filename, message);
    }

}
