package qms.framework.util;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.exception.InvalidCipherDecryption;
import com.google.common.collect.ImmutableMap;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import javax.annotation.Nonnull;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.GridInfoStatus;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.document.dto.ExternalCatalogValue;
import qms.form.util.RangeFilterType;
import qms.framework.dao.IDatabaseConnectionDAO;
import qms.framework.dao.IDatabaseQueryDAO;
import qms.framework.dto.QueryTestDto;
import qms.framework.entity.DatabaseConnection;
import qms.framework.entity.DatabaseQuery;
import qms.framework.entity.DatabaseQueryLink;
import qms.framework.entity.QueryColumn;
import qms.framework.entity.Report;
import qms.framework.entity.SlimReports;
import qms.util.interfaces.IGridFilter;

public class DatabaseQueryHandler {
    
    private static final Logger LOGGER = Loggable.getLogger(DatabaseQueryHandler.class);   
    
    private final DatabaseQueryCacheHandler cacheHandler = new DatabaseQueryCacheHandler();   
    private final DatabaseSourcePool connectionPool = new DatabaseSourcePool();

    private static final String RANGE_FILTER = " "
            + " CONVERT(DATE, :filterFieldDate) BETWEEN :filterStartDate AND :filterEndDate";

    @Nonnull
    private GridInfo<Map<String, Object>> defaultErrorNoData() {
        return defaultErrorNoData(GridInfoStatus.NO_DATA_FROM_SOURCE);
    }

    @Nonnull
    private GridInfo<Map<String, Object>> defaultErrorNoData(GridInfoStatus status) {
        final GridInfo<Map<String, Object>> error = new GridInfo<>();
        error.setCount(-1L);
        error.setData(Utilities.EMPTY_LIST);
        error.setStatus(status);
        return error;
    }
    
    public GridInfo<Map<String, Object>> getRowsBySql(
            final String sql,
            final IGridFilter filter,
            final IDatabaseQuery query,
            final ExternalCatalogValue catalogValue,
            final ClosableConnection closeConn
    ) throws DataSourceCredentialError {
        return getRowsBySql(query, sql, filter, catalogValue, closeConn, null);
    }

    private GridInfo<Map<String, Object>> getRowsBySql(
            final IDatabaseQuery query,
            final String sql,
            final IGridFilter filter,
            final ExternalCatalogValue catalogValue,
            final ClosableConnection closeConn,
            Function<String, String> queryStatmentModfier
    ) throws DataSourceCredentialError {
        final Long databaseQueryId = query.getId();
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            final Boolean hasColumnId = query.getHasColumnNamedId();
            if (hasColumnId != null && hasColumnId) {
                String idColumn = "id";
                if (filter.getAliasColumns() != null && filter.getAliasColumns().containsKey(idColumn)) {
                    idColumn = filter.getAliasColumns().get(idColumn);
                } else {
                    idColumn = "c." + idColumn;
                }
                filter.getField().setOrderBy(idColumn);
            } else {
                String computedFirstColumnCode = query.getComputedFirstColumnCode();
                if (Objects.equals(query.getEnableHierarchy(), false) && computedFirstColumnCode != null) {
                    String orderBy;
                    if (filter.getAliasColumns() != null && filter.getAliasColumns().containsKey(computedFirstColumnCode)) {
                        orderBy = filter.getAliasColumns().get(computedFirstColumnCode);
                    } else {
                        orderBy = "c." + computedFirstColumnCode;
                    }
                    filter.getField().setOrderBy(orderBy);
                }
            }
        }
        if (!filter.canRetriveData()) {
            LOGGER.error(
                "Can't retrieve data, verify the implementation of canRetrieveData()."
            );
            return defaultErrorNoData(GridInfoStatus.NO_ACCESS);
        }
        try {
            if (closeConn == null || closeConn.getConnection() == null) {
                LOGGER.error(
                        "There was a configuration error with query id {}. Could not get connection.",
                        databaseQueryId
                );
                return defaultErrorNoData();
            }
            final Connection connection = closeConn.getConnection();
            connection.setReadOnly(true);
            connection.setAutoCommit(false);
            String sqlStatement = sql
                    .replaceAll("&loggedUserId", catalogValue.getLoggedUserId().toString())
                    .replaceAll("&fillerUserId", catalogValue.getFillerUserId().toString())
                    .replaceAll("&outstandingSurveysId", catalogValue.getOutstandingSurveysId().toString());
            if (queryStatmentModfier != null) {
                sqlStatement = queryStatmentModfier.apply(sqlStatement);
            }
            if (LOGGER.isTraceEnabled()) {
                LOGGER.trace(
                        "Executing getRows for query id {} and SQL: [{}]",
                        databaseQueryId,
                        sqlStatement
                );
            }
            final ISqlDAO sqlDao = new SqlDAOImpl();
            if (filter.getAliasColumns() == null || filter.getAliasColumns().isEmpty()) {
                final Map<String, String> aliasColumns = cacheHandler.buildAliasColumns(query.getEnableCache(), query.getComputedColumnJson());
                filter.setAliasColumns(aliasColumns);
            }
            final GridInfo<Map<String, Object>> rows = sqlDao.SQL_getRowsByQuery(
                    sqlStatement,
                    filter,
                    connection,
                    query.getSessionStatement()
            );
            rows.setLastSyncDate(getLastSyncDate(query));
            return rows;
        } catch (final DatabaseQueryError ex) {
            LOGGER.error(
                    "There was a configuration error with query id {}. Details: {}",
                    databaseQueryId, ex.getMessage(), ex);
            return defaultErrorNoData();
        } catch (final SQLException ex) {
            LOGGER.error("There was an error with query {}.", databaseQueryId, ex);
            return defaultErrorNoData();
        }
    }
    
    private void handleSlimReportsExtraControlAccess(
            Map<String, Boolean> slimReport,
            IGridFilter filter,
            Long queryId,
            @Nonnull ILoggedUser loggedUser
    ) {
        String condition = "";
        Boolean restrictRecordsByDepartment  = slimReport.get("restrictRecordsByDepartment");
        Boolean whereFillerUserParticipate  = slimReport.get("whereFillerUserParticipate");
        if (restrictRecordsByDepartment != null && restrictRecordsByDepartment) {
            condition += " "
                    + " o.business_unit_department_id = " + loggedUser.getBusinessUnitDepartmentId()
                    + " OR o.business_unit_department_id IN (" +
                    " SELECT pd.ubicacion_id " +
                    " FROM users u " +
                    " JOIN usuario_puesto up on u.user_id = up.usuario_id " +
                    " JOIN puesto_departamento  pd on pd.puesto_id = up.puesto_id " +
                    " WHERE u.user_id =" + loggedUser.getId()
                    + " )";
        }
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        List<String> columns = dao.HQL_findByQuery(" "
                        + " SELECT qc.description "
                        + " FROM " + QueryColumn.class.getCanonicalName() + " qc "
                        + " WHERE  qc.query.id = :queryId", ImmutableMap.of("queryId", queryId),
                true,
                CacheRegion.REPORT,
                0
        );
        if (whereFillerUserParticipate != null && whereFillerUserParticipate && columns.contains("request_id")) {
            if (!condition.isEmpty()) {
                condition += " OR ";
            }
            condition += " o.request_id IN (" +
                    " SELECT request_id " +
                    " FROM request r" +
                    " INNER JOIN autorization_pool_details ap ON r.id = ap.request_id " +
                    " WHERE ap.user_id = " + loggedUser.getId()
                    + " )";
        }

        filter.getCriteria().put("<condition>", condition);
    }


    public GridInfo<Map<String, Object>> getRowsById(
            final Long databaseQueryId,
            final IGridFilter filter,
            final Date start,
            final Date end,
            @Nonnull final ILoggedUser loggedUser
    ) throws DataSourceCredentialError, SQLException, InvalidCipherDecryption {
        return getRowsById(databaseQueryId, filter, start, end, loggedUser, null);
    }


    public Map<String, Boolean> getSlimReportData(
            final Long databaseQueryId
    )  {
        final IDatabaseQueryDAO dao = Utilities.getBean(IDatabaseQueryDAO.class);
        return dao.HQL_findSimpleMap(" "
                        + "SELECT new map ( "
                        + " sr.restrictRecordsByDepartment as restrictRecordsByDepartment "
                        + ",sr.whereFillerUserParticipate as whereFillerUserParticipate "
                        + ",r.type as type "
                        + " ) FROM " + Report.class.getCanonicalName() + " r "
                        + " LEFT JOIN r.query q "
                        + " LEFT JOIN " + SlimReports.class.getCanonicalName() + " sr  ON r.id = sr.reportId"
                        + " WHERE q.id = :queryId AND r.type = :reportType",
                ImmutableMap.of(
                        "queryId", databaseQueryId,
                        "reportType", DatabaseQueryType.SLIM_REPORT.getValue()
                ),
                true,
                CacheRegion.REPORT,
                0
        );
    }


    public GridInfo<Map<String, Object>> getRowsById(
            final Long databaseQueryId,
            final IGridFilter filter,
            final Date start,
            final Date end,
            @Nonnull final ILoggedUser loggedUser,
            Function<String, String> queryStatmentModfier
    ) throws DataSourceCredentialError, SQLException, InvalidCipherDecryption {
        Map<String, Boolean> isSlimReportQuery = getSlimReportData(databaseQueryId);
        return getRowsById(
                databaseQueryId,
                filter,
                start,
                end,
                isSlimReportQuery,
                loggedUser,
                queryStatmentModfier
        );
    }


    public GridInfo<Map<String, Object>> getRowsById(
            final Long databaseQueryId,
            final IGridFilter filter,
            final Date start,
            final Date end,
            final Map<String, Boolean> isSlimReportQuery,
            @Nonnull final ILoggedUser loggedUser,
            Function<String, String> queryStatmentModfier
    ) throws DataSourceCredentialError, SQLException, InvalidCipherDecryption {
        final IDatabaseQueryDAO dao = Utilities.getBean(IDatabaseQueryDAO.class);
        final IDatabaseQuery query = dao.loadQuery(databaseQueryId);
        String computedSelect = getComputedSelect(query);
        if (!isSlimReportQuery.isEmpty()) {
            // control de accesos de slim reports
            LOGGER.debug("is slim report! \r\n{}", computedSelect);
            if (!loggedUser.isAdmin()) {
                // el admin no entra por que ve todo
                handleSlimReportsExtraControlAccess(isSlimReportQuery, filter, databaseQueryId, loggedUser);
            }
        }
        if (computedSelect == null || !query.getComputedData()) {
            final DatabaseQuery saved = cacheHandler.buildPendingComputedData(databaseQueryId, loggedUser);
            query.setComputedSelect(saved.getComputedSelect());
            query.setComputedColumnJson(saved.getComputedColumnJson());
            query.setComputedFirstColumnCode(saved.getComputedFirstColumnCode());
            query.setHasColumnNamedId(saved.getHasColumnNamedId());
            computedSelect = getComputedSelect(saved);
        }
        final RangeFilterType rangeType = RangeFilterType.getType(query.getRangeFilterType());
        if (start != null && end != null) {
            defineRangeFilters(databaseQueryId, filter, start, end, rangeType, query);
        }
        if (filter.getAliasColumns() == null || filter.getAliasColumns().isEmpty()) {
            final Map<String, String> aliasColumns = cacheHandler.buildAliasColumns(query.getEnableCache(), query.getComputedColumnJson());
            filter.setAliasColumns(aliasColumns);
        }
        filter.parseCriteriaMap(query.getQueryStatement(), true, true);
        try (final ClosableConnection closeConn = connectionPool.getConnection(query)) {
            final ExternalCatalogValue catalogValue = new ExternalCatalogValue(loggedUser.getId(), loggedUser.getId(), -1L);
            return getRowsBySql(query, computedSelect, filter, catalogValue, closeConn, queryStatmentModfier);
        } catch (DataSourceCredentialError ex) {
            LOGGER.error("There was an credential error with query {}.", databaseQueryId);
            throw ex;
        }
    }

    private String getComputedSelect(IDatabaseQuery query) {
        final String computedSelect;
        if (Boolean.FALSE.equals(query.getEnableCache())
                && query.getComputedColumnJson() != null
                && !query.getComputedColumnJson().isEmpty()) {
            computedSelect = query.getQueryStatement();
        } else {
            computedSelect = query.getComputedSelect();
        }
        return computedSelect;
    }

    private static void defineRangeFilters(
            Long databaseQueryId,
            IGridFilter filter,
            Date start,
            Date end,
            RangeFilterType rangeType,
            IDatabaseQuery query
    ) {
        if (RangeFilterType.NONE.equals(rangeType)) {
            if (start != null || end != null) {
                LOGGER.error("Provided date range for query id {} but range filter type is NONE", databaseQueryId);
            }
            return;
        }
        if (query.getRangeFilterType() == null) {
            LOGGER.error("Missing rangeFilterType for query id {}. Range filter type: {}", databaseQueryId, rangeType);
            return;
        }
        final String startDate = Utilities.formatDateBy(start, "yyyy-MM-dd");
        final String endDate = Utilities.formatDateBy(end, "yyyy-MM-dd");
        boolean validRangesDates = startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty();
        if (!validRangesDates) {
            LOGGER.error("Invalid date range for query id {}. Start date: {} End date: {}", databaseQueryId, start, end);
            return;
        }
        final Object previousCondition = filter.getCriteria().get("<condition>");
        String condition;
        if (previousCondition == null) {
            condition = "";
        } else {
            condition = previousCondition.toString();
        }
        final String rangeCondition = RANGE_FILTER
                .replaceAll(":filterFieldDate", query.getRangeFilterField())
                .replaceAll(":filterStartDate", "'" + startDate + "'")
                .replaceAll(":filterEndDate", "'" + endDate + "'");
        if (condition.isEmpty()) {
            condition = rangeCondition;
        } else {
            condition = " (" + condition + ") AND (" + rangeCondition + ")";
        }
        filter.getCriteria().put("<condition>", condition);
    }

    public QueryTestDto testQuery(final Long databaseQueryId, @Nonnull final ILoggedUser loggedUser) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        final DatabaseQuery query = dao.HQLT_findById(DatabaseQuery.class, databaseQueryId, true, CacheRegion.REPORT, 0);
        return testQuery(query, loggedUser);

    }

    public QueryTestDto testQuery(final DatabaseQuery query, @Nonnull final ILoggedUser loggedUser) {
        return testQuery(query, query.getQueryStatement(), loggedUser);
    }

    public QueryTestDto testQuery(DatabaseQuery query, String sqlSelect, @Nonnull final ILoggedUser loggedUser) {
        final DatabaseQuerySource source = DatabaseQuerySource.getEnum(query);
        if (DatabaseQuerySource.CONFIGURABLE.equals(source)
                && (query.getDatabaseConnection() == null || query.getDatabaseConnection().getId() <= 0)) {
            throw new DatabaseQueryError(
                    "Misconfigured query " + query.getId() + ".  Is has not valid connection id.",
                    sqlSelect
            );
        }
        final DatabaseConnection connection;
        if (DatabaseQuerySource.CONFIGURABLE.equals(source)) {
            final IUntypedDAO dao = Utilities.getUntypedDAO();
            connection = dao.HQLT_findById(DatabaseConnection.class, query.getDatabaseConnection().getId(), true, CacheRegion.REPORT, 0);
        } else {
            connection = null;
        }
        // Se valida que la columna "deleted" exista y sea de tipo valido
        testDeletedColumn(query.getColumns(), query.getEnableCache() != null && query.getEnableCache());
        return testQuery(query.getSource(), connection,  sqlSelect, query.getSessionStatement(), loggedUser);
    }

    public void testDeletedColumn(Set<QueryColumn> columns, boolean cacheEnabled) {
        if (columns == null || !cacheEnabled) {
            return;
        }
        final boolean hasColumnDeleted = columns.stream()
                .anyMatch(column -> column.getCode().equals("deleted"));
        if (hasColumnDeleted) {
            String dataType = columns.stream()
                    .filter(column -> column.getCode().equals("deleted"))
                    .findFirst().get().getDataType();
            if (!"smallint".equals(dataType)) {
                throw new DatabaseQueryError("deleted_column_must_be_smallint");
            }
        } else {
            throw new DatabaseQueryError("deleted_column_must_be_smallint_and_is_required");
        }
    }

    public QueryTestDto testQuery(
            final String connectionType,
            final DatabaseConnection databaseConnection,
            final String sqlSelect,
            final String sessionStatement,
            @Nonnull final ILoggedUser loggedUser
    ) {
        try (final ClosableConnection closeConn = connectionPool.getConnection(connectionType, databaseConnection)) {
            if (closeConn == null || closeConn.getConnection() == null) {
                final QueryTestDto result = new QueryTestDto();
                result.setErrorMessage("Failed to establish connection to database " + databaseConnection.getConnectionUrl());
                return result;
            } else {
                return testQueryWithConnection(connectionType, closeConn.getConnection(), sqlSelect, sessionStatement, loggedUser);
            }
        } catch (DataSourceCredentialError ex) {
            final QueryTestDto result = new QueryTestDto();
            result.setErrorMessage("Failed to decrypt connection password to database " + databaseConnection.getConnectionUrl());
            return result;
        } catch (final Exception ex) {
            LOGGER.error("There was an error ", ex);
            final QueryTestDto result = new QueryTestDto();
            final Throwable rootCause = ExceptionUtils.getRootCause(ex);
            if (rootCause == null) {
                result.setErrorMessage(ex.getMessage());
            } else {
                result.setErrorMessage(rootCause.getMessage());
            }
            return result;
        }
    }

    public QueryTestDto testQueryWithConnection(
            final String connectionType,
            final Connection connection,
            final String sqlSelect,
            final String sessionStatement,
            @Nonnull final ILoggedUser loggedUser
    ) {
        try {
            final IDatabaseConnectionDAO dao = Utilities.getBean(IDatabaseConnectionDAO.class);
            final String qmsStatement = sqlSelect
                    .replaceAll("&loggedUserId", loggedUser.getId().toString())
                    .replaceAll("&fillerUserId", loggedUser.getId().toString())
                    .replaceAll("&outstandingSurveysId", "-1");
            return dao.testQuery(connection, qmsStatement, sessionStatement);
        } catch (final Exception ex) {
            LOGGER.error("Invalid query test: " + sqlSelect, ex);
            final QueryTestDto result = new QueryTestDto();
            final Throwable rootCause = ExceptionUtils.getRootCause(ex);
            if (rootCause == null) {
                result.setErrorMessage(ex.getMessage());
            } else {
                result.setErrorMessage(rootCause.getMessage());
            }
            return result;
        }
    }

    public List<Map<String, Object>> getCatalogValues(
            final Long databaseQueryId,
            final String columnCode,
            @Nonnull final ILoggedUser loggedUser
    ) throws DataSourceCredentialError, SQLException, InvalidCipherDecryption {
        final IDatabaseQueryDAO dao = Utilities.getBean(IDatabaseQueryDAO.class);
        final IDatabaseQuery query = dao.loadQuery(databaseQueryId);
        try (final ClosableConnection closeConn = connectionPool.getConnection(query)) {
            if (closeConn == null || closeConn.getConnection() == null) {
                final List<Map<String, Object>> result = new ArrayList<>(1);
                final Map<String, Object> error = new HashMap<>(1);
                error.put("error", "Failed to establish connection to database " + query.getDatabaseConnectionId());
                result.add(error);
                return result;
            } else {
                final Connection connection = closeConn.getConnection();
                connection.setReadOnly(true);
                connection.setAutoCommit(false);
                final String sqlStatement = cacheHandler.buildCatalogValuesQuery(query, columnCode);
                LOGGER.trace(
                        "Get catalog values for query {} and column {} and SQL {}",
                        databaseQueryId, columnCode, sqlStatement);
                return dao.SQL_findByQuery(
                        sqlStatement,
                        ImmutableMap.of("userId", loggedUser.getId()),
                        connection,
                        query.getSessionStatement()
                );
            }
        }
    }

    private Date getLastSyncDate(final IDatabaseQuery query) {
        if (query.getEnableCache() == null ||  !query.getEnableCache()) {
            final Map<String, Object> params = new HashMap<>(1);
            params.put("databaseQueryId", query.getId());
            final IUntypedDAO dao = Utilities.getUntypedDAO();
            final Date syncDate = (Date) dao.HQL_findSimpleObject(" "
                            + " SELECT MAX(t.cacheSyncDate)"
                            + " FROM " + DatabaseQueryLink.class.getCanonicalName() + " c"
                            + " JOIN c.target t"
                            + " JOIN c.source s"
                            + " WHERE t.deleted = 0"
                            + " AND s.id = :databaseQueryId",
                    params,
                    true,
                    CacheRegion.REPORT,
                    0
            );
            if (syncDate != null) {
                return syncDate;
            } else  {
                return Utilities.getNow();
            }
        } else {
            final Date syncDate = query.getCacheSyncDate();
            if (syncDate != null) {
                return syncDate;
            } else  {
                return Utilities.getNow();
            }
        }
    }

}
