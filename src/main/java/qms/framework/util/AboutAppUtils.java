package qms.framework.util;

import Framework.Config.Utilities;
import java.util.Date;
import java.util.regex.Pattern;
import mx.bnext.core.util.BuildInfo;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.springconfig.QMSConstants;

public class AboutAppUtils {

    private static final Logger LOGGER = Loggable.getLogger(Loggable.LOGGER.STARTUP, AboutAppUtils.class);

    private static final Pattern BUILD_INFO_VERSION_CLEAN_REGEX = Pattern.compile("[^0-9a-zA-Z,.-]");
    private static final String GIT_COMMIT_ID_DESCRIBE = "git.commit.id.describe";
    private static final String GIT_COMMIT_ID_ABBREV = "git.commit.id.abbrev";
    private static final String VALUE_NONE = "-";


    private static BuildInfo BUILD_INFO = null;
    private static String BUILD_ANGULAR_VERSION = "-";

    public static BuildInfo getBuildInfo() {
        if (BUILD_INFO != null) {
            return BUILD_INFO;
        }
        final BuildInfo buildInfo = loadBuildInfo();
        if (BUILD_INFO != buildInfo) {
            BUILD_INFO = buildInfo;
        }
        return buildInfo;
    }

    private static synchronized BuildInfo loadBuildInfo() {
        if (BUILD_INFO != null) {
            return BUILD_INFO;
        }
        final BuildInfo buildInfo = (BuildInfo) Utilities.getBean("buildInfo");
        initializeMavenProperties(buildInfo);
        return buildInfo;
    }

    private static void initializeMavenProperties(final BuildInfo buildInfo) {
        final String buildProfile = parseBuildProfile(buildInfo);
        buildInfo.setBuildProfile(buildProfile);
        final String buildVersion = parseBuildVersion(buildInfo);
        buildInfo.setBuildVersion(buildVersion);
        final String buildNumber = parseBuildNumber(buildInfo);
        buildInfo.setBuildNumber(buildNumber);
    }


    private static String parseBuildVersion(final BuildInfo buildInfo) {
        final String buildVersion = buildInfo.getBuildVersion();
        if (buildVersion != null && !buildVersion.isEmpty()) {
            String cleanBuildVersion = cleanPropertyMavenName(buildVersion);
            if (cleanBuildVersion == null
                    || cleanBuildVersion.isEmpty()
                    || VALUE_NONE.equals(cleanBuildVersion)
                    || GIT_COMMIT_ID_DESCRIBE.equals(cleanBuildVersion)
                    || ("@" + GIT_COMMIT_ID_DESCRIBE + "@").equals(cleanBuildVersion)
                    || GIT_COMMIT_ID_ABBREV.equals(cleanBuildVersion)
                    || ("@" + GIT_COMMIT_ID_ABBREV + "@").equals(cleanBuildVersion)
            ) {
                cleanBuildVersion = cleanPropertyMavenName(parseProjectVersion(buildInfo) + VALUE_NONE + parseBuildNumber(buildInfo));
            }
            return cleanBuildVersion;
        }
        return null;
    }

    private static String parseBuildNumber(final BuildInfo buildInfo) {
        final String buildNumber = buildInfo.getBuildNumber();
        final String cleanBuildNumber = cleanPropertyMavenName(buildNumber);
        if (cleanBuildNumber == null
                || cleanBuildNumber.isEmpty()
                || VALUE_NONE.equals(cleanBuildNumber)
                || GIT_COMMIT_ID_DESCRIBE.equals(cleanBuildNumber)
                || ("@" + GIT_COMMIT_ID_DESCRIBE + "@").equals(cleanBuildNumber)
                || GIT_COMMIT_ID_ABBREV.equals(cleanBuildNumber)
                || ("@" + GIT_COMMIT_ID_ABBREV + "@").equals(cleanBuildNumber)
                || "app.buildNumber".equals(cleanBuildNumber)
                || "@app.buildNumber@".equals(cleanBuildNumber)
        ) {
            return "run-" + new Date().getTime();
        }
        return cleanBuildNumber;
    }

    private static String parseProjectVersion(final BuildInfo buildInfo) {
        final String projectVersion = buildInfo.getProjectVersion();
        final String cleanProjectVersion = cleanPropertyMavenName(projectVersion);
        if (cleanProjectVersion == null
                || cleanProjectVersion.isEmpty()
                || VALUE_NONE.equals(cleanProjectVersion)
                || GIT_COMMIT_ID_DESCRIBE.equals(cleanProjectVersion)
                || ("@" + GIT_COMMIT_ID_DESCRIBE + "@").equals(cleanProjectVersion)
                || GIT_COMMIT_ID_ABBREV.equals(cleanProjectVersion)
                || ("@" + GIT_COMMIT_ID_ABBREV + "@").equals(cleanProjectVersion)
                || "app.projectVersion".equals(cleanProjectVersion)
                || "@app.projectVersion@".equals(cleanProjectVersion)
        ) {
            return "ver-" + new Date().getTime();
        }
        return cleanProjectVersion;
    }

    public static String cleanPropertyMavenName(String value) {
        final String cleanValue = BUILD_INFO_VERSION_CLEAN_REGEX.matcher(value).replaceAll("");
        if (GIT_COMMIT_ID_DESCRIBE.equals(cleanValue)
                || ("@" + GIT_COMMIT_ID_DESCRIBE + "@").equals(cleanValue)
                || GIT_COMMIT_ID_ABBREV.equals(cleanValue)
                || ("@" + GIT_COMMIT_ID_ABBREV + "@").equals(cleanValue)
        ) {
            return VALUE_NONE;
        }
        return cleanValue;
    }

    public static String parseBuildProfile(final BuildInfo buildInfo) {
        final String rawBuildProfile = buildInfo.getBuildProfile();
        return parseBuildProfile(rawBuildProfile);
    }

    public static String parseBuildProfile(final String rawBuildProfile) {
        try {
            if (rawBuildProfile == null || rawBuildProfile.isEmpty()) {
                return VALUE_NONE;
            }
            final String buildProfile = cleanPropertyMavenName(rawBuildProfile);
            if (GIT_COMMIT_ID_DESCRIBE.equals(buildProfile)
                    || ("@" + GIT_COMMIT_ID_DESCRIBE + "@").equals(buildProfile)
                    || GIT_COMMIT_ID_ABBREV.equals(buildProfile)
                    || ("@" + GIT_COMMIT_ID_ABBREV + "@").equals(buildProfile)
            ) {
                return VALUE_NONE;
            }
            return buildProfile;
        } catch (IllegalArgumentException e) {
            LOGGER.trace("Not found {}", rawBuildProfile, e);
            return VALUE_NONE;
        } catch (Exception e) {
            LOGGER.error("Failed to read {}", rawBuildProfile, e);
            return VALUE_NONE;
        }
    }

    public static String getBuildAngularVersion() {
        return BUILD_ANGULAR_VERSION;
    }

    public static void setBuildAngularVersion(String buildAngularVersion) {
        BUILD_ANGULAR_VERSION = buildAngularVersion;
    }

    public static boolean skipLiquibase(final BuildInfo buildInfo) {
        final String buildProfile = parseBuildProfile(buildInfo);
        if (buildProfile == null || buildProfile.isEmpty() || VALUE_NONE.equals(buildProfile)) {
            LOGGER.error("Can not load Maven Profile Settings, Run-Liquibase is enabled by default.");
            return false;
        }
        return !buildProfile.contains(QMSConstants.MAVEN_PROFILE_LIQUIBASE);
    }

    public static Boolean isAspectJCompiled() {
        final BuildInfo buildInfo = (BuildInfo) Utilities.getBean("buildInfo");
        final String buildProfile = parseBuildProfile(buildInfo);
        if (buildProfile == null || buildProfile.isEmpty() || VALUE_NONE.equals(buildProfile)) {
            LOGGER.error("Can not load Maven Profile Settings, AspectJ runtime is enabled by default.");
            return false;
        }
        return buildProfile.contains(QMSConstants.ASPECTJ_MAVEN_PROFILE);
    }
}
