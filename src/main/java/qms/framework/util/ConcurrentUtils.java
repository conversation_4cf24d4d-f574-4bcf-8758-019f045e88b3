package qms.framework.util;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class ConcurrentUtils {

    public static <T> CompletableFuture<List<T>> allOf(final List<CompletableFuture<T>> futures) {
        if (futures == null || futures.isEmpty()) {
            return null;
        }
        final CompletableFuture<Void> results = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return results.thenApply(v -> futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList())
        );
    }
}
