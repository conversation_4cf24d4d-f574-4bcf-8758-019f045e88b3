package qms.framework.util;

import javax.annotation.Nonnull;

/**
 *
 * <AUTHOR>
 */
public abstract class TimeUtils {
    
    private static final double NANOSECONDS_BY_MILLISECOND = 1000000.0;

    /**
     * Obtiene el tiempo transcurrido desde el inicio de la medición
     * @param timeStartNs Tiempo de inicio de la medición en nanosegundos
     * @return Tiempo transcurrido desde el inicio de la medición
     */
    @Nonnull
    public static ElapsedTime elapsedTime(final Double timeStartNs) {
        final ElapsedTime time = new ElapsedTime();
        //TODO: Utilizar System.currentTimeMillis() por que System.nanoTime() es más costoso
        final double timeDeltaNs = System.nanoTime() - timeStartNs;
        final double elapsedMilliseconds = timeDeltaNs / NANOSECONDS_BY_MILLISECOND;
        time.setMilliseconds(elapsedMilliseconds);
        return time;
    }
    
}
