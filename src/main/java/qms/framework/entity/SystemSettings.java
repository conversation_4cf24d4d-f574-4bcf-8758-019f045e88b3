package qms.framework.entity;

import Framework.Config.DomainObject;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;

/**
 *
 * <AUTHOR>
 */@Entity
@Table(name = "settings")
public class SystemSettings extends DomainObject  {
    
    private String availableModules;
    
    @Id
    @Basic
    @Column(name = "id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "available_modules")
    public String getAvailableModules() {
        return availableModules;
    }

    public void setAvailableModules(String availableModules) {
        this.availableModules = availableModules;
    }
    
    
}
