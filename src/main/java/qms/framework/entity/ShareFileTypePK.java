package qms.framework.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ShareFileTypePK implements Serializable {

    private Long documentTypeId;
    private Long fileTypeId;

    public ShareFileTypePK(Long documentTypeId, Long fileTypeId) {
        this.documentTypeId = documentTypeId;
        this.fileTypeId = fileTypeId;
    }

    public ShareFileTypePK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "document_type_id")
    public Long getDocumentTypeId() {
        return documentTypeId;
    }

    public void setDocumentTypeId(Long documentTypeId) {
        this.documentTypeId = documentTypeId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "file_type_id")
    public Long getFileTypeId() {
        return fileTypeId;
    }

    public void setFileTypeId(Long fileTypeId) {
        this.fileTypeId = fileTypeId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 37 * hash + Objects.hashCode(this.documentTypeId);
        hash = 37 * hash + Objects.hashCode(this.fileTypeId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ShareFileTypePK other = (ShareFileTypePK) obj;
        if (!Objects.equals(this.documentTypeId, other.documentTypeId)) {
            return false;
        }
        if (!Objects.equals(this.fileTypeId, other.fileTypeId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ShareFileTypePK{" + "documentTypeId=" + documentTypeId + ", fileTypeId=" + fileTypeId + '}';
    }

}
