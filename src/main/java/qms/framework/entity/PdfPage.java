package qms.framework.entity;

import Framework.Config.DomainObject;
import java.io.Serializable;
import java.sql.Blob;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.TableGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "file_pdf_pages")
public class PdfPage extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long fileId;
    private Integer page;
    private Blob image;
    private Blob thumbnail;
    private Long imageSize;
    private String imageSha512;
    private Long thumbnailSize;
    private String thumbnailSha512;
    private Integer hasImage = 0;
    private Integer hasThumbnail = 0;

    public PdfPage() {
    }

    public PdfPage(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Lob
    @Column(name = "image")
    public Blob getImage() {
        return image;
    }

    public void setImage(Blob image) {
        this.image = image;
    }

    @Lob
    @Column(name = "thumbnail")
    public Blob getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(Blob thumbnail) {
        this.thumbnail = thumbnail;
    }
    
    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "page")
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    @Column(name = "image_size")
    public Long getImageSize() {
        return imageSize;
    }

    public void setImageSize(Long imageSize) {
        this.imageSize = imageSize;
    }

    @Column(name = "image_sha512")
    public String getImageSha512() {
        return imageSha512;
    }

    public void setImageSha512(String imageSha512) {
        this.imageSha512 = imageSha512;
    }
    
    @Column(name = "thumbnail_size")
    public Long getThumbnailSize() {
        return thumbnailSize;
    }

    public void setThumbnailSize(Long thumbnailSize) {
        this.thumbnailSize = thumbnailSize;
    }

    @Column(name = "thumbnail_sha512")
    public String getThumbnailSha512() {
        return thumbnailSha512;
    }

    public void setThumbnailSha512(String thumbnailSha512) {
        this.thumbnailSha512 = thumbnailSha512;
    }

    @Column(name = "has_image")
    public Integer getHasImage() {
        return hasImage;
    }

    public void setHasImage(Integer hasImage) {
        this.hasImage = hasImage;
    }   

    @Column(name = "has_thumbnail")
    public Integer getHasThumbnail() {
        return hasThumbnail;
    }

    public void setHasThumbnail(Integer hasThumbnail) {
        this.hasThumbnail = hasThumbnail;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 41 * hash + Objects.hashCode(this.fileId);
        hash = 41 * hash + Objects.hashCode(this.page);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PdfPage other = (PdfPage) obj;
        if (!Objects.equals(this.fileId, other.fileId)) {
            return false;
        }
        if (!Objects.equals(this.page, other.page)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PdfPage{" + "fileId=" + fileId + ", page=" + page + '}';
    }

}
