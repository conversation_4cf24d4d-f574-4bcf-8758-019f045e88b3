package qms.framework.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Convert;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import org.hibernate.annotations.Type;
import qms.framework.util.IReportColumn;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "report_column")
public class ReportColumn extends DomainObject implements Serializable, Comparable<ReportColumn>, IReportColumn {

    private static final long serialVersionUID = 1L;
   
    private QueryColumn queryColumn;
    private String description;
    private Integer type;                       /** <-- Devuelve un valor de tipo {@link qms.framework.util.ReportColumnType} **/
    private Integer gridOrder;
    private String width;
    private Integer sortPriority;
    private Integer sortDirection;
    private Integer groupingPriority;
    private Integer groupingDirection;
    private Long reportId;
    private Long hierarchyId;
    private String hierarchyCode;
    private String hierarchyDescription;
    private Boolean onlyFilter = false;

    public ReportColumn() {}
    
    public ReportColumn(Long id) {
        this.id = id;
    }
    
    @Id
    @Basic(optional = false)
    @Column(name = "report_column_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "query_column_id")
    @Override
    public QueryColumn getQueryColumn() {
        return queryColumn;
    }

    public void setQueryColumn(QueryColumn queryColumn) {
        this.queryColumn = queryColumn;
    }
    
    @Column(name = "description")
    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return Devuelve un valor de tipo {@link qms.framework.util.ReportColumnType}
     */
    @Column(name = "type")
    @Override
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "grid_order")
    @Override
    public Integer getGridOrder() {
        return gridOrder;
    }

    public void setGridOrder(Integer gridOrder) {
        this.gridOrder = gridOrder;
    }
    
    @Column(name = "report_id")
    @Override
    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    @Column(name = "hierarchy_id")
    @Override
    public Long getHierarchyId() {
        return hierarchyId;
    }

    public void setHierarchyId(Long hierarchyId) {
        this.hierarchyId = hierarchyId;
    }

    @Column(name = "hierarchy_code")
    @Override
    public String getHierarchyCode() {
        return hierarchyCode;
    }

    public void setHierarchyCode(String hierarchyCode) {
        this.hierarchyCode = hierarchyCode;
    }

    @Column(name = "hierarchy_description")
    @Override
    public String getHierarchyDescription() {
        return hierarchyDescription;
    }

    public void setHierarchyDescription(String hierarchyDescription) {
        this.hierarchyDescription = hierarchyDescription;
    }
    
    @Column(name = "width")
    @Override
    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    @Column(name = "sort_priority")
    @Override
    public Integer getSortPriority() {
        return sortPriority;
    }

    public void setSortPriority(Integer sortPriority) {
        this.sortPriority = sortPriority;
    }

    @Column(name = "sort_direction")
    @Override
    public Integer getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(Integer sortDirection) {
        this.sortDirection = sortDirection;
    }

    @Column(name = "grouping_priority")
    @Override
    public Integer getGroupingPriority() {
        return groupingPriority;
    }

    public void setGroupingPriority(Integer groupingPriority) {
        this.groupingPriority = groupingPriority;
    }

    @Column(name = "grouping_direction")
    @Override
    public Integer getGroupingDirection() {
        return groupingDirection;
    }

    public void setGroupingDirection(Integer groupingDirection) {
        this.groupingDirection = groupingDirection;
    }

    @Override
    @Column(name = "only_filter")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getOnlyFilter() {
        return onlyFilter;
    }

    public void setOnlyFilter(Boolean onlyFilter) {
        this.onlyFilter = onlyFilter;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 67 * hash + Objects.hashCode(this.queryColumn);
        hash = 67 * hash + Objects.hashCode(this.gridOrder);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ReportColumn other = (ReportColumn) obj;
        if (!Objects.equals(this.queryColumn, other.queryColumn)) {
            return false;
        }
        return Objects.equals(this.gridOrder, other.gridOrder);
    }

    @Override
    public String toString() {
        return "ReportColumn{" + "column=" + queryColumn  + '}';
    }
    
    @Override
    public int compareTo(ReportColumn other) {
        if (other == null) {
            return 1;
        }
        if (this.getId() == null) {
            return -1;
        }
        int comparedTo = this.getId().compareTo(other.getId());
        if (comparedTo == 0) {
            comparedTo = this.getDescription().compareTo(other.getDescription());
        }
        if (comparedTo == 0) {
            comparedTo = this.getType().compareTo(other.getType());
        }
        if (comparedTo == 0) {
            comparedTo = this.getQueryColumn().compareTo(other.getQueryColumn());
        }
        return comparedTo;
    }
}
