package qms.framework.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

@Embeddable
public class BulkUserRecordPK implements Serializable {

    private Long bulkUserId;
    private Long bulkLoadId;

    public BulkUserRecordPK(Long bulkUserId, Long bulkLoadId) {
        this.bulkUserId = bulkUserId;
        this.bulkLoadId = bulkLoadId;
    }

    public BulkUserRecordPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "bulk_user_id")
    public Long getBulkUserId() {
        return bulkUserId;
    }

    public void setBulkUserId(Long bulkUserId) {
        this.bulkUserId = bulkUserId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "bulk_load_id")
    public Long getBulkLoadId() {
        return bulkLoadId;
    }

    public void setBulkLoadId(Long bulkLoadId) {
        this.bulkLoadId = bulkLoadId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.bulkUserId);
        hash = 97 * hash + Objects.hashCode(this.bulkLoadId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BulkUserRecordPK other = (BulkUserRecordPK) obj;
        if (!Objects.equals(this.bulkUserId, other.bulkUserId)) {
            return false;
        }
        return Objects.equals(this.bulkLoadId, other.bulkLoadId);
    }

    @Override
    public String toString() {
        return "BulkUserRecordPK{" + "activityId=" + bulkUserId + ", fileId=" + bulkLoadId + '}';
    }

}
