package qms.framework.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.springframework.data.annotation.CreatedBy;
import qms.framework.enums.BulkLoadType;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "bulk_load")
public class BulkLoad extends DomainObject {

    private Date loadDate;
    private Long createdBy;
    private Long noCreations;
    private Long noUpdates;
    private Long noFailures;
    private Integer type;
    private Long logId;

    public BulkLoad(Long createdBy, BulkLoadType type) {
        this.noCreations = 0L;
        this.noUpdates = 0L;
        this.noFailures = 0L;
        this.loadDate = new Date();
        this.createdBy = createdBy;
        if (type != null) {
            this.type = type.getValue();
        }
        this.id = -1L;
    }

    public BulkLoad() {
    }

    @Id
    @Column(name = "bulk_load_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "load_date", updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLoadDate() {
        return loadDate;
    }

    public void setLoadDate(Date loadId) {
        this.loadDate = loadId;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long userId) {
        this.createdBy = userId;
    }

    @Column(name = "no_creations")
    public Long getNoCreations() {
        return noCreations;
    }

    public void setNoCreations(Long noCreations) {
        this.noCreations = noCreations;
    }

    @Column(name = "no_updates")
    public Long getNoUpdates() {
        return noUpdates;
    }

    public void setNoUpdates(Long noUpdates) {
        this.noUpdates = noUpdates;
    }

    @Column(name = "no_failures")
    public Long getNoFailures() {
        return noFailures;
    }

    public void setNoFailures(Long noFailures) {
        this.noFailures = noFailures;
    }

    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Column(name = "log_id")
    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }


}
