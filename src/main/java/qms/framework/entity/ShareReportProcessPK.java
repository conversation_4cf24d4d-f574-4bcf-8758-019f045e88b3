package qms.framework.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

@Embeddable
public class ShareReportProcessPK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long processId;
    private Long shareReportId;

    public ShareReportProcessPK(Long processId, Long shareReportId) {
        this.processId = processId;
        this.shareReportId = shareReportId;
    }

    public ShareReportProcessPK() {
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "process_id")
    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "share_report_id")
    public Long getShareReportId() {
        return shareReportId;
    }

    public void setShareReportId(Long shareReportId) {
        this.shareReportId = shareReportId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.processId);
        hash = 97 * hash + Objects.hashCode(this.shareReportId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ShareReportProcessPK other = (ShareReportProcessPK) obj;
        if (!Objects.equals(this.processId, other.processId)) {
            return false;
        }
        return Objects.equals(this.shareReportId, other.shareReportId);
    }

    @Override
    public String toString() {
        return "ShareReportProcessPK{" + "processId=" + processId + ", shareReportId=" + shareReportId + '}';
    }
}
