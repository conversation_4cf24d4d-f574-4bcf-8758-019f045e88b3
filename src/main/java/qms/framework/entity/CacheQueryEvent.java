package qms.framework.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "cache_query_event")
public class CacheQueryEvent extends StandardEntity<CacheQueryEvent> implements IAuditableEntity { 

    private static final long serialVersionUID = 1L;
    
    public static enum STATUS implements IStatusEnum {
        TO_GENERATE(1, IStatusEnum.COLOR_GREEN),
        INACTIVE(0, IStatusEnum.COLOR_GRAY),
        BUILD_FAILED(2, IStatusEnum.COLOR_RED),
        BUILD_SUCCESS(3, IStatusEnum.COLOR_RED),
        SYNC_FAILED(4, IStatusEnum.COLOR_ORANGE),
        SYNC_SUCCESS(5, IStatusEnum.COLOR_ORANGE)
        ;
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public IStatusEnum getActiveStatus() {
            return TO_GENERATE;
        }
        
        public static STATUS getStatus(Integer status) {
            for (STATUS s : values()) {
                if (Objects.equals(s.value, status)) {
                    return s;
                }
            }
            return null;
        }
    }
    
    
    private Integer status = 1;
    private Integer deleted = 0;
    private String code;
    private String description;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private String module;
    private Long queryId;
    private DatabaseQuery query;
    private Long primaryKeyId;
    private String primaryKeyCode;
    private Long versionKeyId;
    private String versionKeyCode;    
    private DatabaseConnection connection;
    private String source;
    private Integer schedulerType;
    private SystemEventEntity systemEvent;
    private String databaseSchema;
    private String databaseTable;
    private String primaryKeyValue;
    private String primaryKeyType;
    private Long numberRecordsUpdated;
    private Long numberRecordsDeleted;
    private Long numberRecordsInserted;
    private Boolean builtCache;
    private Date startDate;
    private Date endDate;
    private UserRef lastModifiedByUser;
    private Boolean errorMailSend;
    private Date errorMailSendDate;
    private String errorMailSendTo;

    @Id
    @Basic
    @Column(name = "cache_query_event_id", precision = 19, scale = 0)
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            this.status = 1;
        } else {
            this.status = status;
        }
    }

    @Override
    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            this.deleted = IS_NOT_DELETED;
        } else {
            this.deleted = deleted;
        }
    }

    @Override
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }
   
    @Column(name = "source")
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
    
    @Column(name = "database_query_id", insertable = false, updatable = false)
    public Long getQueryId() {
        return queryId;
    }

    public void setQueryId(Long queryId) {
        this.queryId = queryId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "database_query_id")
    public DatabaseQuery getQuery() {
        return query;
    }

    public void setQuery(DatabaseQuery query) {
        this.query = query;
    }

    @Column(name = "primary_key_id")
    public Long getPrimaryKeyId() {
        return primaryKeyId;
    }

    public void setPrimaryKeyId(Long primaryKeyId) {
        this.primaryKeyId = primaryKeyId;
    }

    @Column(name = "primary_key_code")
    public String getPrimaryKeyCode() {
        return primaryKeyCode;
    }

    public void setPrimaryKeyCode(String primaryKeyCode) {
        this.primaryKeyCode = primaryKeyCode;
    }

    @Column(name = "version_key_id")
    public Long getVersionKeyId() {
        return versionKeyId;
    }

    public void setVersionKeyId(Long versionKeyId) {
        this.versionKeyId = versionKeyId;
    }

    @Column(name = "version_key_code")
    public String getVersionKeyCode() {
        return versionKeyCode;
    }

    public void setVersionKeyCode(String versionKeyCode) {
        this.versionKeyCode = versionKeyCode;
    }
    
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH})
    @JoinColumn(name = "connection_id")
    public DatabaseConnection getConnection() {
        return connection;
    }

    public void setConnection(DatabaseConnection connection) {
        this.connection = connection;
    }
    
    @Column(name = "scheduler_type")
    public Integer getSchedulerType() {
        return schedulerType;
    }

    public void setSchedulerType(Integer schedulerType) {
        this.schedulerType = schedulerType;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "system_event_id")
    public SystemEventEntity getSystemEvent() {
        return systemEvent;
    }

    public void setSystemEvent(SystemEventEntity systemEvent) {
        this.systemEvent = systemEvent;
    }

    @Column(name = "database_schema")
    public String getDatabaseSchema() {
        return databaseSchema;
    }

    public void setDatabaseSchema(String databaseSchema) {
        this.databaseSchema = databaseSchema;
    }

    @Column(name = "database_table")
    public String getDatabaseTable() {
        return databaseTable;
    }

    public void setDatabaseTable(String databaseTable) {
        this.databaseTable = databaseTable;
    }

    @Column(name = "primary_key_value")
    public String getPrimaryKeyValue() {
        return primaryKeyValue;
    }

    public void setPrimaryKeyValue(String primaryKeyValue) {
        this.primaryKeyValue = primaryKeyValue;
    }
    
    @Column(name = "primary_key_type")
    public String getPrimaryKeyType() {
        return primaryKeyType;
    }

    public void setPrimaryKeyType(String primaryKeyType) {
        this.primaryKeyType = primaryKeyType;
    }
    
    @Column(name = "number_records_updated")
    public Long getNumberRecordsUpdated() {
        return numberRecordsUpdated;
    }

    public void setNumberRecordsUpdated(Long numberRecordsUpdated) {
        this.numberRecordsUpdated = numberRecordsUpdated;
    }

    @Column(name = "number_records_deleted")
    public Long getNumberRecordsDeleted() {
        return numberRecordsDeleted;
    }

    public void setNumberRecordsDeleted(Long numberRecordsDeleted) {
        this.numberRecordsDeleted = numberRecordsDeleted;
    }

    @Column(name = "number_records_inserted")
    public Long getNumberRecordsInserted() {
        return numberRecordsInserted;
    }

    public void setNumberRecordsInserted(Long numberRecordsInserted) {
        this.numberRecordsInserted = numberRecordsInserted;
    }
    
    @Column(name = "built_cache")
    @Type(type = "numeric_boolean")
    public Boolean getBuiltCache() {
        return builtCache;
    }

    public void setBuiltCache(Boolean builtCache) {
        this.builtCache = builtCache;
    }

    @Column(name = "start_date", updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "end_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "last_modified_by", insertable = false, updatable = false)
    public UserRef getLastModifiedByUser() {
        return lastModifiedByUser;
    }

    public void setLastModifiedByUser(UserRef lastModifiedByUser) {
        this.lastModifiedByUser = lastModifiedByUser;
    }    

    @Column(name = "error_mail_send")
    @Type(type = "numeric_boolean")
    public Boolean getErrorMailSend() {
        return errorMailSend;
    }

    public void setErrorMailSend(Boolean errorMailSend) {
        this.errorMailSend = errorMailSend;
    }

    @Column(name = "error_mail_send_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getErrorMailSendDate() {
        return errorMailSendDate;
    }

    public void setErrorMailSendDate(Date errorMailSendDate) {
        this.errorMailSendDate = errorMailSendDate;
    }

    @Column(name = "error_mail_send_to")
    public String getErrorMailSendTo() {
        return errorMailSendTo;
    }

    public void setErrorMailSendTo(String errorMailSendTo) {
        this.errorMailSendTo = errorMailSendTo;
    }
    
    @Override
    public boolean equals(Object object) {
        if (!(object instanceof CacheQueryEvent)) {
            return false;
        }
        CacheQueryEvent other = (CacheQueryEvent) object;
        return (this.id != null || other.id == null) && (this.id == null || this.id.equals(other.id));
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public String toString() {
        return "qms.framework.entity.QueryCacheEvent[ id=" + id + " ]";
    }

}