package qms.framework.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import qms.framework.util.CacheConstants;
import qms.util.interfaces.IAuditableCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "share_report_business_unit")
public class ShareReportBusinessUnit extends CompositeStandardEntity<ShareReportBusinessUnitPK>
        implements Serializable, IAuditableCompositeEntity<ShareReportBusinessUnitPK>, ILinkedComposityGrid<ShareReportBusinessUnitPK> {

    private static final long serialVersionUID = 1L;

    private ShareReportBusinessUnitPK id;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    public ShareReportBusinessUnit() {
    }

    public ShareReportBusinessUnit(ShareReportBusinessUnitPK id) {
        this.id = id;
    }

    public ShareReportBusinessUnit(Long businessUnitId, Long shareReportId) {
        this.id = new ShareReportBusinessUnitPK(businessUnitId, shareReportId);
    }

    @Override
    public ShareReportBusinessUnitPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public ShareReportBusinessUnitPK getId() {
        return id;
    }

    @Override
    public void setId(ShareReportBusinessUnitPK id) {
        this.id = id;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ShareReportBusinessUnit other = (ShareReportBusinessUnit) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "ShareReportBusinessUnit{" + "id=" + id + '}';
    }

}
