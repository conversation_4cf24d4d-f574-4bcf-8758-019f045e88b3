package qms.framework.entity;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "share_user_agent")
public class ShareUserAgent extends CompositeStandardEntity<ShareUserAgentPK> 
        implements Serializable, ILinkedComposityGrid<ShareUserAgentPK> {

    private static final long serialVersionUID = 1L;
    
    private ShareUserAgentPK id;
    private UserAgent userAgent;

    public ShareUserAgent() {
    }

    public ShareUserAgent(ShareUserAgentPK id) {
        this.id = id;
    }

    public ShareUserAgent(Long documentTypeId, Long userAgentId) {
        this.id = new ShareUserAgentPK(documentTypeId, userAgentId);
    }

    @Override
    public ShareUserAgentPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public ShareUserAgentPK getId() {
        return id;
    }

    @Override
    public void setId(ShareUserAgentPK id) {
        this.id = id;
    }

    /**
     * @return the userAgent
     */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_agent_id", insertable = false, updatable = false)
    public UserAgent getUserAgent() {
        return userAgent;
    }

    /**
     * @param userAgent the userAgent to set
     */
    public void setUserAgent(UserAgent userAgent) {
        this.userAgent = userAgent;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ShareUserAgent other = (ShareUserAgent) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "ShareUserAgent{" + "id=" + id + '}';
    }

}
