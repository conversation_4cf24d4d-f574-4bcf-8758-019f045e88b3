package qms.framework.entity;

import DPMS.Mapping.Periodicity;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.dto.ShareReportLinkedDTO;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.framework.util.CacheConstants;
import qms.util.CodePrefix;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.REPORT, usage = CacheConcurrencyStrategy.READ_WRITE)
@CodePrefix("SHARE-REPORT")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "share_report")
public class ShareReport extends StandardEntity<ShareReport> implements IPeriodicEntity {

    private static final long serialVersionUID = 1L;
  
    public static enum STATUS implements IStatusEnum {
        MAIN_OPEN(1, IStatusEnum.COLOR_GREEN),
        MAIN_CLOSED(0, IStatusEnum.COLOR_BLACK),
        EVENT_SCHEDULED(2, IStatusEnum.COLOR_YELLOW),
        EVENT_SENT(3, IStatusEnum.COLOR_GRAY),
        EVENT_EXPIRED(4, IStatusEnum.COLOR_BEIGE),
        EVENT_FAILED(5, IStatusEnum.COLOR_RED);
        ;
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public IStatusEnum getActiveStatus() {
            return MAIN_OPEN;
        }
        
        public static STATUS getStatus(Integer status) {
            for(STATUS s : values()) {
                if (Objects.equals(s.value, status)) {
                    return s;
                }
            }
            return null;
        }
    }
    
    private Integer status = 1;
    private Integer deleted = 0;
    private String code;
    private String description;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Long reportId;
    private String recipients;
    private String plainRecipients;
    private String module;
    private UserRef createdByUser;
    private UserRef lastModifiedByUser;
    private Long maxRecords;
    private Periodicity periodicity;
    private Integer recurrent;
    private Long recurrenceId;
    private Date startDate;
    private Date endDate;
    private Date commitmentDate;
    private Integer commitmentStatus;
    private String queryConditions;
    private String details;
    private Date nextDate;
    private Integer belongSeries;
    private ShareReportLinkedDTO linked;

    public ShareReport() {
    }

    public ShareReport(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "share_report_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Override
    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Override
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "report_id")
    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    @Column(name = "recipients")
    public String getRecipients() {
        return recipients;
    }

    public void setRecipients(String recipients) {
        this.recipients = recipients;
    }

    @Column(name = "plain_recipients")
    public String getPlainRecipients() {
        return plainRecipients;
    }

    public void setPlainRecipients(String plainRecipients) {
        this.plainRecipients = plainRecipients;
    }

    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "created_by", updatable = false, insertable = false)
    public UserRef getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(UserRef createdByUser) {
        this.createdByUser = createdByUser;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "last_modified_by", updatable = false, insertable = false)
    public UserRef getLastModifiedByUser() {
        return lastModifiedByUser;
    }

    public void setLastModifiedByUser(UserRef lastModifiedByUser) {
        this.lastModifiedByUser = lastModifiedByUser;
    }

    @Column(name = "max_records")
    public Long getMaxRecords() {
        return maxRecords;
    }

    public void setMaxRecords(Long maxRecords) {
        this.maxRecords = maxRecords;
    }

    @OneToOne
    @Cascade(CascadeType.ALL)
    @JoinColumn(referencedColumnName = "intperiodicidadid", name = "periodicity_id")
    public Periodicity getPeriodicity() {
        return periodicity;
    }

    public void setPeriodicity(Periodicity periodicity) {
        this.periodicity = periodicity;
    }

    @Override
    @Column(name = "recurrent")
    public Integer getRecurrent() {
        return recurrent;
    }

    @Override
    public void setRecurrent(Integer recurrent) {
        this.recurrent = recurrent;
    }

    @Override
    @Column(name = "recurrence_id")
    public Long getRecurrenceId() {
        return recurrenceId;
    }

    @Override
    public void setRecurrenceId(Long recurrenceId) {
        this.recurrenceId = recurrenceId;
    }

    @Column(name = "start_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "end_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Override
    @Column(name = "commitment_date")
    @Temporal(javax.persistence.TemporalType.DATE)
    public Date getCommitmentDate() {
        return commitmentDate;
    }

    @Override
    public void setCommitmentDate(Date commitmentDate) {
        this.commitmentDate = commitmentDate;
    }

    @Column(name = "commitment_status")
    public Integer getCommitmentStatus() {
        return commitmentStatus;
    }

    public void setCommitmentStatus(Integer commitmentStatus) {
        this.commitmentStatus = commitmentStatus;
    }

    @Column(name = "query_conditions")
    public String getQueryConditions() {
        return queryConditions;
    }

    public void setQueryConditions(String queryConditions) {
        this.queryConditions = queryConditions;
    }

    @Column(name = "details")
    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    @Column(name = "next_date")
    @Temporal(javax.persistence.TemporalType.DATE)
    public Date getNextDate() {
        return nextDate;
    }

    public void setNextDate(Date nextDate) {
        this.nextDate = nextDate;
    }
    
    @Override
    @Column(name = "belong_series")
    public Integer getBelongSeries() {
        return belongSeries;
    }

    public void setBelongSeries(Integer belongSeries) {
        this.belongSeries = belongSeries;
    }

    @Transient
    public ShareReportLinkedDTO getLinked() {
        return linked;
    }

    public void setLinked(ShareReportLinkedDTO linked) {
        this.linked = linked;
    }
    
    @Override
    public boolean equals(Object object) {
        if (!(object instanceof ShareReport)) {
            return false;
        }
        ShareReport other = (ShareReport) object;
        return (this.id != null || other.id == null) && (this.id == null || this.id.equals(other.id));
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public String toString() {
        return "ShareReport{" + "id=" + id + '}';
    }

}
