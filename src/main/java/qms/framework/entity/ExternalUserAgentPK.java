package qms.framework.entity;

import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ExternalUserAgentPK implements Serializable {

    private Long documentTypeId;
    private Long userAgentId;

    public ExternalUserAgentPK(Long documentTypeId, Long userAgentId) {
        this.documentTypeId = documentTypeId;
        this.userAgentId = userAgentId;
    }

    public ExternalUserAgentPK() {
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "document_type_id")
    public Long getDocumentTypeId() {
        return documentTypeId;
    }

    public void setDocumentTypeId(Long documentTypeId) {
        this.documentTypeId = documentTypeId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "user_agent_id")
    public Long getUserAgentId() {
        return userAgentId;
    }

    public void setUserAgentId(Long userAgentId) {
        this.userAgentId = userAgentId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 59 * hash + Objects.hashCode(this.documentTypeId);
        hash = 59 * hash + Objects.hashCode(this.userAgentId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ExternalUserAgentPK other = (ExternalUserAgentPK) obj;
        if (!Objects.equals(this.documentTypeId, other.documentTypeId)) {
            return false;
        }
        if (!Objects.equals(this.userAgentId, other.userAgentId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ExternalUserAgentPK{" + "documentTypeId=" + documentTypeId + ", userAgentId=" + userAgentId + '}';
    }

}
