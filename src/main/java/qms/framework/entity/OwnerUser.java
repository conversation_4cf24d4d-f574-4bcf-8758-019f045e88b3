package qms.framework.entity;

import DPMS.Mapping.Persistable;
import DPMS.Mapping.User;
import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Transient;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.OWNER, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "owner_user")
public class OwnerUser extends DomainObject implements Persistable {

    private static final long serialVersionUID = 1L;
    
    private Owner owner;
    private Long userId;

    public OwnerUser() {
    }
    
    public OwnerUser(OwnerUser other) {
        this.id = -1L;
        this.userId = other.getUserId();
        this.owner = other.getOwner(); 
    }
    
    public OwnerUser(Long id, Long userId) {
        this.id = id;
        this.userId = userId;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "owner_user_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return this.id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public Long identifuerValue() {
        return id;
    }

    @JsonIgnore
    @Cache(region = CacheConstants.OWNER, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "owner_id")
    public Owner getOwner() {
        return owner;
    }

    public void setOwner(Owner owner) {
        this.owner = owner;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.userId);
        return hash;
    }
    
    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    @Transient
    public User getUser() {
        if (userId == null) {
            return null;
        }
        return new User(userId);
    }

    /**
     * No utilizar solo tiene el ID
     * @deprecated No utilizar solo tiene el ID
     */
    @Deprecated
    public void setUser(User user) {
        if (user != null) {
            this.userId = user.getId();
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OwnerUser other = (OwnerUser) obj;
        return Objects.equals(this.userId, other.userId);
    }



    @Override
    public String toString() {
        return "qms.framework.entity.OwnerUser{" + "id=" + id + '}';
    }

}
