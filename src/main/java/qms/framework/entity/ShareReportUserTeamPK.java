package qms.framework.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

@Embeddable
public class ShareReportUserTeamPK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userTeamId;
    private Long shareReportId;

    public ShareReportUserTeamPK(Long userTeamId, Long shareReportId) {
        this.userTeamId = userTeamId;
        this.shareReportId = shareReportId;
    }

    public ShareReportUserTeamPK() {
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "user_team_id")
    public Long getUserTeamId() {
        return userTeamId;
    }

    public void setUserTeamId(Long userTeamId) {
        this.userTeamId = userTeamId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "share_report_id")
    public Long getShareReportId() {
        return shareReportId;
    }

    public void setShareReportId(Long shareReportId) {
        this.shareReportId = shareReportId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.userTeamId);
        hash = 97 * hash + Objects.hashCode(this.shareReportId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ShareReportUserTeamPK other = (ShareReportUserTeamPK) obj;
        if (!Objects.equals(this.userTeamId, other.userTeamId)) {
            return false;
        }
        return Objects.equals(this.shareReportId, other.shareReportId);
    }

    @Override
    public String toString() {
        return "ShareReportUserTeamPK{" + "userTeamId=" + userTeamId + ", shareReportId=" + shareReportId + '}';
    }
}
