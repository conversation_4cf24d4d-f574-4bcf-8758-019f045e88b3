package qms.framework.entity;

import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class TagRegistryTimesheetPK implements Serializable {

    private Long tagRegistryId;
    private Long timesheetId;

    public TagRegistryTimesheetPK() {
    }

    public TagRegistryTimesheetPK(Long tagRegistryId, Long timesheetId) {
        this.tagRegistryId = tagRegistryId;
        this.timesheetId = timesheetId;
    }

    @Basic(optional = false)
    @Column(name = "tag_registry_id")
    public Long getTagRegistryId() {
        return tagRegistryId;
    }

    public void setTagRegistryId(Long tagRegistryId) {
        this.tagRegistryId = tagRegistryId;
    }

    @Basic(optional = false)
    @Column(name = "timesheet_id")
    public Long getTimesheetId() {
        return timesheetId;
    }

    public void setTimesheetId(Long timesheetId) {
        this.timesheetId = timesheetId;
    }


}
