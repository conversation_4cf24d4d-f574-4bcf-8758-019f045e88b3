package qms.framework.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

@Embeddable
public class ShareReportBusinessUnitPK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long businessUnitId;
    private Long shareReportId;

    public ShareReportBusinessUnitPK(Long businessUnitId, Long shareReportId) {
        this.businessUnitId = businessUnitId;
        this.shareReportId = shareReportId;
    }

    public ShareReportBusinessUnitPK() {
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "share_report_id")
    public Long getShareReportId() {
        return shareReportId;
    }

    public void setShareReportId(Long shareReportId) {
        this.shareReportId = shareReportId;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.businessUnitId);
        hash = 97 * hash + Objects.hashCode(this.shareReportId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ShareReportBusinessUnitPK other = (ShareReportBusinessUnitPK) obj;
        if (!Objects.equals(this.businessUnitId, other.businessUnitId)) {
            return false;
        }
        return Objects.equals(this.shareReportId, other.shareReportId);
    }

    @Override
    public String toString() {
        return "ShareReportBusinessUnitPK{" + "businessUnitId=" + businessUnitId + ", shareReportId=" + shareReportId + '}';
    }
}
