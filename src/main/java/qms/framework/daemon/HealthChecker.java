package qms.framework.daemon;

import Framework.Config.Utilities;
import java.util.Date;
import java.util.concurrent.ScheduledFuture;
import mx.bnext.core.daemon.util.IBnextExecutor;
import mx.bnext.core.daemon.util.IDaemonConfig;
import mx.bnext.core.util.Loggable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import qms.framework.dto.MainSchedulerTypeDto;
import qms.framework.enums.MainSchedulerType;
import qms.framework.util.ExceptionUtils;
import qms.util.ReflectionUtil;

public class HealthChecker extends Loggable implements IBnextExecutor<IDaemonConfig> {

    private IDaemonConfig config;
    private ThreadPoolTaskScheduler taskScheduler;
    private final MainSchedulerTypeDto cronTrigger = new MainSchedulerTypeDto(
            new CronTrigger(MainSchedulerType.DAILY.getCronTriggerExp()),
            new HealthThread()
    );
    private ScheduledFuture<?> scheduledTask;


    @Override
    public void start() {
        try {
            shutdown();
        } catch (Exception e) {
            getLogger().error(" -- Could not shutdown schedule health task. Error: {}", ExceptionUtils.getRootCauseMessage(e));
        }
        try {
            taskScheduler = new ThreadPoolTaskScheduler();
            if (scheduledTask != null) {
                scheduledTask.cancel(true);
            }
            taskScheduler.setThreadNamePrefix(getFactoryId() + "-");
            taskScheduler.initialize();
            scheduledTask = taskScheduler.schedule(
                    cronTrigger.getThread(), cronTrigger.getTrigger()
            );
        } catch (Exception e) {
            getLogger().error(" -- Could not start schedule health task. Error: {}", ExceptionUtils.getRootCauseMessage(e));
        }
    }

    @Override
    public void shutdown() {
        if (scheduledTask != null) {
            scheduledTask.cancel(true);
        }
        if (taskScheduler != null) {
            taskScheduler.destroy();
        }
    }

    @Override
    public void logInfoExecutor() {
        if (taskScheduler != null && cronTrigger != null) {
            final String nextSchedule = Utilities.formatDateWithTime(getScheduledExecutionTime());
            getLogger(LOGGER.DAEMON).info(
                    "Health checker active tasks: [{}], next execution: [{}]",
                    new Object[] {
                        taskScheduler.getScheduledThreadPoolExecutor().getQueue().size(),
                        nextSchedule
                    }
            );
        } else {
            getLogger().error("Health Checker is: [not running]");
        }
    }

    private Date getScheduledExecutionTime() {
        return ReflectionUtil.getValue(scheduledTask, "scheduledExecutionTime");
    }

    @Override
    public void setConfig(final IDaemonConfig config) {
        this.config = config;
    }

    @Override
    public IDaemonConfig getConfig() {
        return this.config;
    }

    @Override
    public Boolean updateConfig() {
        logInfoExecutor();
        return true;
    }

    public Integer getTaskCounts() {
        return taskScheduler.getActiveCount();
    }

    @Override
    public String getFactoryId() {
        return config.getSettings().getSystemId() + "-" + DaemonThread.HEALTH_CHECKER.getName()
                + "-" + config.getBuildInfo().getBuildVersion();
    }

}
