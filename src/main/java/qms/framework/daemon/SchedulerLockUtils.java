package qms.framework.daemon;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.util.concurrent.TimeUnit;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.framework.enums.MainSchedulerType;

public class SchedulerLockUtils {

    private static final Logger LOGGER = Loggable.getLogger(SchedulerLockUtils.class);

    private static final LoadingCache<MainSchedulerType, Object> LOCKS = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.DAYS)
            .maximumSize(10)
            .build(CacheLoader.from(Object::new));

    private static final LoadingCache<Long, Object> MAIL_LOCK = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.DAYS)
            .maximumSize(10)
            .build(CacheLoader.from(Object::new));

    private static final long MAIL_ID = -1L;

    public static Boolean isLocked(MainSchedulerType type) {
        return LOCKS.getIfPresent(type) != null;
    }

    public static synchronized Object lock(MainSchedulerType type) {
        if (isLocked(type)) {
            return null;
        }
        return LOCKS.getUnchecked(type);
    }

    public static synchronized void unlock(MainSchedulerType type) {
        try {
            if (LOCKS.getIfPresent(type) == null) {
                return;
            }
            LOCKS.invalidate(type);
        } catch (final Exception e) {
            LOGGER.error(
                    "Failed to release schedule task lock",
                    e
            );
        }
    }

    public static boolean isMailLocked() {
        return MAIL_LOCK.getIfPresent(MAIL_ID) != null;
    }

    public static synchronized Object lockMail() {
        if (isMailLocked()) {
            return null;
        }
        return MAIL_LOCK.getUnchecked(MAIL_ID);
    }

    public static synchronized void unlockMail() {
        try {
            if (MAIL_LOCK.getIfPresent(MAIL_ID) == null) {
                return;
            }
            MAIL_LOCK.invalidate(MAIL_ID);
        } catch (final Exception e) {
            LOGGER.error(
                    "Failed to release mail lock",
                    e
            );
        }

    }

    public static void reset() {
        try {
            MAIL_LOCK.invalidateAll();
        } catch (final Exception e) {
            LOGGER.error(
                    "Failed to release mail lock",
                    e
            );
        }
        try {
            LOCKS.invalidateAll();
        } catch (final Exception e) {
            LOGGER.error(
                    "Failed to release schedule task lock",
                    e
            );
        }
    }
}
