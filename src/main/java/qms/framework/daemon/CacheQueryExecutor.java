package qms.framework.daemon;

import DPMS.Mapping.Settings;
import java.util.concurrent.RejectedExecutionException;
import mx.bnext.core.daemon.BnextThreadExecutor;
import mx.bnext.core.daemon.util.IBnextThread;
import mx.bnext.core.daemon.util.IDaemonConfig;

/**
 *
 * <AUTHOR>
 */
public class CacheQueryExecutor extends BnextThreadExecutor<IDaemonConfig> {

    public Boolean process(final CacheQueryFactory factory) {
        getLogger().trace("Processing query cache.");
        try {
            logInfoExecutor();
            runAsyncOrTimeout(factory);
            return true;
        } catch (final RejectedExecutionException ex) {
            getLogger().error("@ send RejectedExecutionException {}{}", factory.toString(), ex);
            return false;
        }
    }
    
    public Boolean inProcess(final Long databaseQueryId, final Long recordId) {
        final String id = databaseQueryId.toString() + "-" + recordId.toString();
        return getControlPool().hasTask(id);
    }
    
    @Override
    public Long getAliveTime() {
        final Settings settings = (Settings) getConfig().getSettings();
        return settings.getCacheQuerySyncAliveTime();
    }

    @Override
    public Integer getPoolSize() {
        final Settings settings = (Settings) getConfig().getSettings();
        return settings.getCacheQuerySyncImagePoolSize();
    }

    @Override
    public Integer getMaxWaitingTasks() {
        final Settings settings = (Settings) getConfig().getSettings();
        return settings.getCacheQuerySyncImageMaxWaitingTasks(); 
    }

    @Override
    public void onThreadShutdown(final IBnextThread thread) {
        if (thread instanceof CacheQueryFactory) {
            final CacheQueryFactory factory = (CacheQueryFactory) thread;
            factory.shutdown();
            factory.interrupt();
        }
    }

    @Override
    public String getFactoryId() {
        final IDaemonConfig config = getConfig();
        return config.getSettings().getSystemId()
                + "-" + DaemonThread.CACHE_QUERY_EXECUTOR.getName()
                + "-" + config.getBuildInfo().getBuildVersion();
    }
    
}
