package qms.framework.daemon;

import DPMS.Mapping.Settings;
import Framework.Config.PagedQuery;
import Framework.Config.Utilities;
import bnext.licensing.LicenseUtil;
import isoblock.common.MainThread;
import java.util.Date;
import java.util.List;
import javax.annotation.Nonnull;
import mx.bnext.core.daemon.BnextDaemon;
import mx.bnext.core.daemon.util.IBnextExecutor;
import mx.bnext.core.daemon.util.IDaemonConfig;
import mx.bnext.core.daemon.util.IDaemonThread;
import mx.bnext.core.pdf.util.IPdfDaemonConfig;
import mx.bnext.core.util.Loggable;
import mx.bnext.licensing.License;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.form.util.SurveyUtil;
import qms.framework.dto.MainSchedulerDTO;
import qms.framework.entity.ScheduledTask;
import qms.framework.mail.BnextStaffMailer;
import qms.framework.pdf.PdfProgramUtil;
import qms.framework.util.ExceptionUtils;
import qms.util.interfaces.IPagedQuery;

/**
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class BnextDaemonUtil {

    private static final Logger LOGGER = Loggable.getLogger(SurveyUtil.class);
    private final static Double ERROR_PER_PAGE = 1000.0;

    public static void contextDestroyed() {
        final List<IDaemonThread> threads = DaemonThread.getValues();
        final IPdfDaemonConfig config = PdfProgramUtil.PDF_CONFIG.get();
        BnextDaemon.contextDestroyed(threads, config);
    }

    public static void contextInitialized() {
        final IPdfDaemonConfig config = PdfProgramUtil.getPdfConfig();
        List<IDaemonThread> threads = DaemonThread.getValues();
        BnextDaemon.contextInitialized(config, threads);
    }

    public static void shutdown(final IBnextExecutor<IDaemonConfig> executor) {
        BnextDaemon.shutdown(executor);
    }

    public static <T extends mx.bnext.core.daemon.util.IBnextExecutor<IDaemonConfig>> T getRunningInstance(final Class<T> interfaceClass) {
        return BnextDaemon.getRunningInstance(interfaceClass);
    }

    public static <T extends mx.bnext.core.daemon.util.IBnextExecutor<IDaemonConfig>> T getRunningInstance(final DaemonThread daemon) {
        return BnextDaemon.getRunningInstance((Class<T>) daemon.getExecutorClass());
    }

    public static IBnextExecutor<IDaemonConfig> newInstance(
            final Class<? extends IBnextExecutor<IDaemonConfig>> clazz
    ) {
        return BnextDaemon.newInstance(clazz);
    }

    public static void start() {
        final MainThread mainThreadInstance = getRunningInstance(MainThread.class);
        if (!mainThreadInstance.isAvailable()) {
            mainThreadInstance.start();
        }
    }

    public static void updateConfiguration(final Settings newSettings) {
        final IPdfDaemonConfig config = PdfProgramUtil.updatePdfConfig(newSettings);
        BnextDaemon.updateConfiguration(config);
    }

    public static void configureSendMailError(final ScheduledTask task, final Exception e) {
        if (task == null) {
            return;
        }
        String errorMessage = getMessageError(e);
        if (errorMessage == null) {
            return;
        }
        task.setErrorMailSend(false);
        String error = task.getError();
        if (error == null) {
            error = "";
        }
        String taskError = errorMessage + "\n" + error;
        if (taskError.length() > 1999) {
            taskError = error.substring(0, 1999);
        }
        task.setError(taskError);
        task.setRunEnd(new Date());
    }

    public static String getMessageError(final Exception e) {
        String errorMessage;
        if (e != null) {
            final Throwable cause = ExceptionUtils.getRootCause(e);
            if (cause != null) {
                errorMessage = StringUtils.truncate(cause.getLocalizedMessage(), 3999);
            } else {
                errorMessage = StringUtils.truncate(e.getLocalizedMessage(), 3999);
            }
        } else {
            errorMessage = null;
        }
        if (errorMessage == null) {
            errorMessage = "";
        }
        return errorMessage;
    }

    public static void notifyFailedScheduledTasks(@Nonnull final ILoggedUser admin) {
        double numberPages = 1.0;
        int currentPage = 0;
        try {
            final IScheduledTaskBean taskBean = Utilities.getBean(IScheduledTaskBean.class);
            final Long count = taskBean.getNotifyFailedScheduledTasksCount();
            if (count == null || count <= 0) {
                return;
            }
            numberPages = Math.ceil(count > ERROR_PER_PAGE ? count / ERROR_PER_PAGE : 1.0);
            final IPagedQuery pageConfig = new PagedQuery();
            pageConfig.setPageSize(ERROR_PER_PAGE.intValue());
            for (currentPage = 0; currentPage < numberPages; currentPage++) {
                pageConfig.setPage(currentPage);
                try {
                    final Boolean result = taskBean.sendNotifyFailedScheduledTasks(pageConfig, admin);
                    LOGGER.debug("Failed scheduled tasks fired: {}", result);
                } catch (final Exception ex) {
                    final String error = getMessageError(ex);
                    LOGGER.error(
                            "Failed to send failed scheduled tasks for page {} of {}", new Object[]{currentPage, numberPages, error},
                            ex
                    );
                }
            }
        } catch (final Exception ex) {
            final String error = getMessageError(ex);
            LOGGER.error(
                    "Failed to send failed scheduled taskss for page {} of {}", new Object[]{currentPage, numberPages, error},
                    ex
            );
        }
    }

    public static void notifyDesynchronizedScheduledTasks(final List<MainSchedulerDTO> records, @Nonnull final ILoggedUser admin) {
        final License lic = LicenseUtil.getCurrentLicense();
        if (lic == null) {
            LOGGER.error("License not configured, could not notify desynchronized scheduled tasks.");
            return;
        }
        final BnextStaffMailer mailer = new BnextStaffMailer(Utilities.getUntypedDAO());
        if (records != null && !records.isEmpty()) {
            mailer.sendNotifyDesynchronizedScheduledTasks(records, admin);
        }
    }

    private BnextDaemonUtil() {
    }

}
