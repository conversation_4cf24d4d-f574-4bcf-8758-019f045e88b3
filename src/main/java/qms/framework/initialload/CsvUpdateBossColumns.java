package qms.framework.initialload;

public enum CsvUpdateBossColumns implements ICsvIndex {
    
    USER_CODE(0),
    BOSS_CODE(1);
    
    public static final String PREFIX = "UPDATE-BOSS";
    
    private final Integer index;

    private CsvUpdateBossColumns(Integer index) {
        this.index = index;
    }

    @Override
    public String getName() {
        return PREFIX + "_" + this.name();
    }   
    
    @Override
    public Integer getIndex() {
        return this.index;
    }

}
