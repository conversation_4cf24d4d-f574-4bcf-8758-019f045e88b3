package qms.framework.initialload;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.sql.Timestamp;
import java.util.List;
import mx.bnext.core.file.FileHandler;
import org.apache.commons.lang3.StringUtils;
import qms.framework.enums.BulkLoadType;

public class BackendLoadWriter {

    private static final Integer BUFFER_MAX_SIZE = 100_000;

    private final Path file;
    private final StringBuilder sb;
    private Integer totalLines = 1;
    private Integer currentLine = 1;
    private static final String TEMP_DEB_FILE_PREFFIX = "backend-file-";

    public BackendLoadWriter() throws IOException {
        final BackendUploaderHelper helper = new BackendUploaderHelper();
        final Path tempFolder = helper.getTempFolder();
        this.file = FileHandler.createTempFile(TEMP_DEB_FILE_PREFFIX, tempFolder);
        this.sb = new StringBuilder();
    }

    private BackendLoadWriter writeToFile() throws IOException {
        if (sb.length() == 0) {
            return this;
        }
        try (BufferedWriter writer = Files.newBufferedWriter(file, StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.APPEND)) {
            writer.write(sb.toString());
            writer.flush();
        }
        return this;
    }

    private BackendLoadWriter appendSb(Object value) throws IOException {
        if (sb.length() > BUFFER_MAX_SIZE) {
            writeToFile();
            sb.setLength(0);
        }
        sb.append(value);
        sb.append('\n');
        return this;
    }

    public BackendLoadWriter appendWrapperStart() throws IOException {
        append("<li>");
        append("<h3>").append(currentLine).append("/").append(totalLines).append("</h3>");
        return this;
    }

    public BackendLoadWriter appendWrapperEnd() throws IOException {
        append("</li>");
        return this;
    }

    public BackendLoadWriter appendBackButton() throws IOException {
        append("<div class=\"button-container\"><button type=\"button\" onclick=\"history.back()\">Back</button></div>");
        return this;
    }

    public BackendLoadWriter append(List<String> value) throws IOException {
        appendSb(StringUtils.join(value, "','"));
        return this;
    }

    public BackendLoadWriter append(BulkLoadType value) throws IOException {
        appendSb(value);
        return this;
    }

    public BackendLoadWriter append(String value) throws IOException {
        appendSb(value);
        return this;
    }

    public BackendLoadWriter append(Timestamp value) throws IOException {
        appendSb(value);
        return this;
    }

    public BackendLoadWriter append(Double value) throws IOException {
        appendSb(value);
        return this;
    }

    public BackendLoadWriter append(Long value) throws IOException {
        appendSb(value);
        return this;
    }

    public BackendLoadWriter append(Integer value) throws IOException {
        appendSb(value);
        return this;
    }

    public BackendLoadWriter append(Object value) throws IOException {
        appendSb(value);
        return this;
    }

    public BackendLoadWriter flush() throws IOException {
        writeToFile();
        return this;
    }

    public String getPath() {
        return file.getFileName().toString();
    }

    public Path getFile() {
        return file;
    }

    public Integer getTotalLines() {
        return totalLines;
    }

    public BackendLoadWriter setTotalLines(Integer totalLines) {
        this.totalLines = totalLines;
        return this;
    }

    public Integer getCurrentLine() {
        return currentLine;
    }

    public BackendLoadWriter setCurrentLine(Integer currentLine) {
        this.currentLine = currentLine;
        return this;
    }

}
