package qms.framework.initialload;

/**
 *
 * <AUTHOR>
 */
public enum CsvArea implements ICsvIndex {
    
    CODE(0),
    MANAGER_ACCOUNT(1);
    
    public static final String PREFIX = "AREA";
    private final Integer index;

    private CsvArea(Integer index) {
        this.index = index;
    }

    @Override
    public Integer getIndex() {
        return this.index;
    }   

    @Override
    public String getName() {
        return PREFIX + "_" + this.name();
    }  
    
    public static String getGenericErrorKey() {
        return "GENERIC_ERROR_" + PREFIX;
    }
    
    public static String getSuccessKey() {
        return "OPERATION_SUCCESS_" + PREFIX;
    }
}
