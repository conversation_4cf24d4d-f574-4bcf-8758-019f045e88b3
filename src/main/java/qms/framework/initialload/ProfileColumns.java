package qms.framework.initialload;

/**
 *
 * <AUTHOR>
 */
public enum ProfileColumns implements ICsvIndex {
    BUSINESS_UNIT(0),
    POSITION(1),
    ADMINISTRATION(2),
    DOCUMENTS(3),
    FORMS(4),
    AUDITS(5),
    FINDINGS(6),
    DEVICES(7),
    MEETINGS(8),
    POLLS(9),
    COMPLAINTS(10),
    METERS(11),
    PROJECTS(12),
    FIVES(13),
    ACTIVITIES(14),
    CLAVE(15);

    public static final String PREFIX = "PROFILE";
    
    private final Integer index;

    private ProfileColumns(Integer index) {
        this.index = index;
    }

    @Override
    public String getName() {
        return PREFIX + "_" + this.name();
    }   

    @Override
    public Integer getIndex() {
        return this.index;
    }
    

}
