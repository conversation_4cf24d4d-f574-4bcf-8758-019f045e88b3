package qms.framework.mail;

import DPMS.DAOInterface.IUserDAO;
import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.licensing.LicenseUtil;
import java.util.Optional;
import java.util.ResourceBundle;
import mx.bnext.access.Module;
import mx.bnext.licensing.License;
import mx.bnext.licensing.Schema;
import qms.access.dto.ILoggedUser;
import qms.configuration.util.IBulkUser;
import qms.framework.core.Mailer;
import qms.framework.util.LocaleUtil;

public class DatabaseUpdatesMailer extends Mailer {

    private static final String BULK_USER_DETAIL_URL = "menu/settings/roles/user/bulk-detail/recordId";

    private String _bulkUserTemplate;
    private final ResourceBundle _tagsBulkUser;

    public DatabaseUpdatesMailer(IUntypedDAO dao) {
        super(dao);
        final String name = this.getClass().getCanonicalName();
        loadTags(name);
        _tagsBulkUser = LocaleUtil.getSystemI18n(IBulkUser.class.getCanonicalName());
        super.setSubject(getTag("subject"));
    }

    private String getBulkUserTemplate(IBulkUser bulkUser) {
        if (_bulkUserTemplate == null) {
            _bulkUserTemplate = getTemplate(IBulkUser.class.getCanonicalName());
        }
        return mapEntity(bulkUser, _bulkUserTemplate, _tagsBulkUser);
    }


    public Boolean sendSuccessProcessBulkUserFile(
            final IBulkUser bulkUser,
            final GenericSaveHandle gsh,
            final ILoggedUser loggedUser
    ) {
        final License lic = LicenseUtil.getCurrentLicense();
        if (lic == null) {
            getLogger().error("Failed to send success process bulk user file email for bulkUser: "
                    + "{} because there is no current license", bulkUser.getId());
            return false;
        }
        try {
            final Optional<Schema> firstSchema = LicenseUtil.getCurrentLicenseSchemas().stream().findFirst();
            if (!firstSchema.isPresent()) {
                getLogger().error("Failed to send success process bulk user file email for bulkUser: "
                        + "{} because there is no current license", bulkUser.getId());
                return false;
            }
            final String fileName = gsh.getJsonEntityData().get("description").toString();
            final Schema schema = firstSchema.get();
            final String messageContent = getTag("message.successProcessBulkUserFile")
                    .replace(":activeLicenses", Utilities.getBean(IUserDAO.class).getActiveUsers(schema.getCode()) + "")
                    .replace(":totalLicenses", schema.getSeats())
                    .replace(":fileName", fileName);
            setTitle(messageContent);
            final Mail from = Utilities.getMailSystem();
            final String subject = getTag("subject.successProcessBulkUserFile");
            final String message = getBulkUserTemplate(bulkUser);
            // Send email to qms support
            final Mail toLicSupport = new Mail(
                    getTag("to.isInSupport").replace(":systemId", lic.getSystemId()), lic.getSupportEmail()
            );
            final String link = BULK_USER_DETAIL_URL.replace("recordId", bulkUser.getId() + "");
            send(subject, from, toLicSupport, message, link, Module.CONFIGURATION.getKey(), TYPE.LICENSE);
            // Send email to logged user
            send(subject, from, new Mail(loggedUser), message, link, Module.CONFIGURATION.getKey(), TYPE.LICENSE);
            return true;
        } catch(final Exception e) {
            getLogger().error("Failed to send success process bulk user file email for bulkUser: {}", bulkUser.getId(), e);
            return false;
        }
    }
}
