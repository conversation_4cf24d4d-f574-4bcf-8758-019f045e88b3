package qms.framework.service;

import DPMS.Mapping.User;
import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.login.util.AuthenticationUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.access.dto.ILoggedUser;
import qms.framework.core.PasswordEncoder;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.ITokenProvider;
import qms.framework.util.MailTokenManager;

/**
 *
 * <AUTHOR> @ Bnext
 * @since Nov 23, 2016
 */
public class EditPasswordService extends SessionViewer {

    private static final long serialVersionUID = 1L;

    public String smd() {
        return SUCCESS;
    }
    
    @SMDMethod
    public GenericSaveHandle changeMyPassword(String currentPassword, String newPassword) {
        final GenericSaveHandle gsh = AuthenticationUtils.validateCurrentPassword(getRequest(), currentPassword);
        if (Objects.equals(gsh.getOperationEstatus(), 0)) {
            return gsh;
        }
        final Map<String, Object> params = new HashMap<>(2);
        final String newHashed = PasswordEncoder.encode(newPassword);
        params.put("hashed", newHashed);
        final String account = SecurityUtils.getLoggedUserAccount();
        params.put("account", account);
        
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        
        final String queryHash = ""
                + " UPDATE " + User.class.getCanonicalName() 
                + " SET"
                + " hashedPassword = :hashed,"
                + " version = version + 1"
                + " WHERE cuenta = :account";
        
        final Integer updated = dao.HQL_updateByQuery(queryHash, params);
        gsh.setSavedId("0");
        gsh.setOperationEstatus(1);
        gsh.getJsonEntityData().put("updated", updated);
        return gsh;
    }
    
    @SMDMethod
    public GenericSaveHandle generateLoginQrUrl(String currentPassword) {
        final GenericSaveHandle gsh = AuthenticationUtils.validateCurrentPassword(getRequest(), currentPassword);
        if (Objects.equals(gsh.getOperationEstatus(), 0)) {
            return gsh;
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();     
        final MailTokenManager tokenManager = new MailTokenManager(getBean(ITokenProvider.class)); 
        final String url = tokenManager.generateQrUrl(loggedUser, "-");
        gsh.getJsonEntityData().put("url", url);
        gsh.setSavedId("0");
        gsh.setOperationEstatus(1);
        return gsh;
    }
}
