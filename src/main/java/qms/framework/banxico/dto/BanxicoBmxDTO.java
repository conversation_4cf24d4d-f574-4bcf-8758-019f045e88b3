package qms.framework.banxico.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.List;

/**
 * Ver https://www.banxico.org.mx/SieAPIRest/service/v1/doc/consultaDatosSerieOp
 *
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BanxicoBmxDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<BanxicoSerieDTO> series;

    public List<BanxicoSerieDTO> getSeries() {
        return series;
    }

    public void setSeries(List<BanxicoSerieDTO> series) {
        this.series = series;
    }

}
