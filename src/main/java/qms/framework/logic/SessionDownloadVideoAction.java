package qms.framework.logic;

import Framework.Action.DefaultAction;
import bnext.dto.DownloadDto;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.pdf.dto.PdfConversionDTO;
import qms.framework.file.FileManager;

public class SessionDownloadVideoAction extends DefaultAction {

    private static final long serialVersionUID = 1L;

    private String contentType;
    private String downloadResult; 
    
    @Override
    @SuppressWarnings("empty-statement")
    public String execute() throws Exception {
        super.execute();
        final DownloadDto dto = new DownloadDto();
        dto.setFileId(Long.valueOf(getId()));
        dto.setIdMode(1);
        dto.setOriginalForce(1);
        dto.setIncludeContentDisposition(false);
        final FileManager fileManager = new FileManager();
        final PdfConversionDTO result = fileManager.generateFilePdfAndDownload(
                dto,
                isAdmin(), 
                null,
                response,
                FileUtils.COLUMN_FILES_CONTENT,
                getLoggedUserDto()
        );
        this.contentType = result.getContentType();
        return result.getMessage();
    }
    
    public String getDownloadResult() {
        return downloadResult;
    }

    public String getContentType() {
        return contentType;
    }

   
}
