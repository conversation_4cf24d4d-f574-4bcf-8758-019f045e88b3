package qms.framework.enums;

/**
 *
 * <AUTHOR>
 */
public enum CacheSchedulerType {
    
    EVERY_FIVE_MINUTES(9, MainSchedulerType.EVERY_FIVE_MINUTES),
    EVERY_FIVETEEN_MINUTES(1, MainSchedulerType.EVERY_FIVETEEN_MINUTES),
    THREE_TIMES_A_DAY(2, MainSchedulerType.THREE_TIMES_A_DAY),
    DAILY(3, MainSchedulerType.DAILY),
    HOURLY(4, MainSchedulerType.HOURLY),
    MANUAL(5, null),
    STARTUP(6, null),
    RECORD_INSERT(7, null),
    RECORD_UPDATE(8, null),
    RECORD_DELETE(9, null)
    ;

    private final Integer value;
    private final MainSchedulerType schedulerType;

    CacheSchedulerType(Integer value, MainSchedulerType schedulerType) {
        this.value = value;
        this.schedulerType = schedulerType;
    }

    public MainSchedulerType getSchedulerType() {
        return schedulerType;
    }

    public Integer getValue() {
        return this.value;
    }

}
