/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.core;

import Framework.Action.DefaultAction;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import qms.util.ModuleUtil;

/**
 *
 * <AUTHOR>
 */
public class DatabaseQueryAction extends DefaultAction {

    private static final long serialVersionUID = 1L;

    private Module module;

    public String getModule() {
        if (module == null) {
            return null;
        }
        return module.getKey();
    }

    public void setModule(String module) {
        if (module == null || module.trim().isEmpty()) {
            this.module = null;
        } else {
            this.module = ModuleUtil.fromKey(module);
        }
    }

    public Boolean getCanUpdateDatabaseConnection() {
        if (isAdmin() || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)) {
            return true;
        }
        return (Module.FORMULARIE.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.FORM_REGISTER_CONNECTION)
                ) || (Module.TIMESHEET.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TS_REGISTER_CONNECTION))
                || (Module.ACTIVITY.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.ACTIVITY_REGISTER_CONNECTION))
                || (Module.TIMEWORK.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TIMEWORK_CREATE_CONNECTION))
                ;
    }

    public Boolean getCanDeleteDatabaseConnection() {
        if (isAdmin() || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)) {
            return true;
        }
        return (Module.FORMULARIE.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.FORM_DELETE_CONNECTION)
                ) || (Module.TIMESHEET.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TS_DELETE_CONNECTION))
                 || (Module.ACTIVITY.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.ACTIVITY_DELETE_CONNECTION))
                || (Module.TIMEWORK.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TIMEWORK_DELETE_CONNECTION));
    }

    public Boolean getCanUpdateDatabaseQuery() {
        if (isAdmin() || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)) {
            return true;
        }
        return (Module.FORMULARIE.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.FORM_REGISTER_QUERY)
                ) || (Module.TIMESHEET.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TS_REGISTER_QUERY))
                 || (Module.ACTIVITY.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.ACTIVITY_REGISTER_QUERY))
                || (Module.TIMEWORK.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TIMEWORK_CREATE_QUERY));
    }

    public Boolean getCanDeleteDatabaseQuery() {
        if (isAdmin() || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)) {
            return true;
        }
        return (Module.FORMULARIE.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.FORM_DELETE_QUERY)
                ) || (Module.TIMESHEET.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TS_DELETE_QUERY))
                 || (Module.ACTIVITY.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.ACTIVITY_DELETE_QUERY))
                || (Module.TIMEWORK.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TIMEWORK_DELETE_QUERY));
    }

    public Boolean getCanSyncCache() {
        if (isAdmin() || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)) {
            return true;
        }
        return (Module.FORMULARIE.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.FORM_SYNC_CACHE)) || (Module.TIMESHEET.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TS_SYNC_CACHE))
                 || (Module.ACTIVITY.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.ACTIVITY_SYNC_CACHE))
                || (Module.TIMEWORK.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TIMEWORK_SYNC_CACHE));
    }

    public Boolean getCanEnableCache() {
        if (isAdmin() || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)) {
            return true;
        }
        return (Module.FORMULARIE.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.FORM_ENABLE_CACHE)
                ) || (Module.TIMESHEET.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TS_ENABLE_CACHE))
                 || (Module.ACTIVITY.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.ACTIVITY_ENABLE_CACHE))
                || (Module.TIMEWORK.equals(module)
                && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && getLoggedUserServices().contains(ProfileServices.TIMEWORK_ENABLE_CACHE));
    }

}
