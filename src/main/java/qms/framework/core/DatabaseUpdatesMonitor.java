/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.core;

import DPMS.DAOInterface.IFilesStorageDAO;
import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.DAOInterface.IPendingRecordDAO;
import javax.annotation.Nonnull;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.configuration.util.IBulkUser;
import qms.framework.listeners.BnextMonitor;
import qms.framework.mail.DatabaseUpdatesMailer;

/**
 *
 * <AUTHOR>
 */
@Aspect
public class DatabaseUpdatesMonitor extends BnextMonitor {
        
    @After(BnextMonitor.DAILY_DATABASES_UPDATED_IS_FIRED)
    public void onDailyDatabaseUpdatedFired(@Nonnull final ILoggedUser loggedUser) {
        Settings settings = Utilities.getSettings();
        if (settings.getPendingHistory() > 0) {
            try {
                IPendingRecordDAO dao = Utilities.getBean(IPendingRecordDAO.class);
                int movedRecords = dao.saveHistory(settings);
                dao.rebuildIndex(settings, movedRecords);
            } catch (Exception e) {
                getLogger().error("Failed to complete daily database updates on daily updated fired:{}", e.getMessage(), e);
            }
        }
         // Recalculate Files Uploaded Size
        IFilesStorageDAO dao = Utilities.getBean(IFilesStorageDAO.class);
        dao.calculateFilesUploadedSize();
    }

    /**
     * Catches methods annotated with OnSuccessProcessBulkUserFile
     */
    @Around(
            value = "@annotation(qms.framework.listeners.OnSuccessProcessBulkUserFile)"
                    + " && args(bulkUser, gsh, loggedUser)"
    )
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object onSuccessProcessBulkUserFile(
            final ProceedingJoinPoint pjp,
            final IBulkUser bulkUser,
            final GenericSaveHandle gsh,
            final ILoggedUser loggedUser
    ) throws Throwable {
        final Boolean result = (Boolean) pjp.proceed();
        if (result) {
            IUntypedDAO dao = Utilities.getBean(IUntypedDAO.class);
            DatabaseUpdatesMailer mailer = new DatabaseUpdatesMailer(dao);
            return mailer.sendSuccessProcessBulkUserFile(bulkUser, gsh, loggedUser);
        }
        return false;
    }

}
