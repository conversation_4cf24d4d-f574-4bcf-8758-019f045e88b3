/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.core;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import javax.servlet.ServletContext;
import mx.bnext.cipher.Encryptor;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.util.SettingsUtil;
import static qms.happyornot.dao.HappyornotDAO.URL_BASE_TOKEN;
import qms.happyornot.entity.HappyornotSurvey;
import qms.happyornot.entity.HappyornotUpdateLink;

/**
 *
 * <AUTHOR>
 */
public abstract class HappyornotTokenUtil {

    public static void initialize(final ServletContext servletContext) {
        IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);

        List<Long> surveysInsecure = (List<Long>) dao.HQL_findByQuery(""
                + " SELECT t.id"
                + " FROM " + HappyornotSurvey.class.getCanonicalName()  + " s"
                + " JOIN s.targetNames t"
                + " LEFT JOIN t.happyornotLinks l"
                + " WHERE l.id IS NULL"
        );

        Long loggedUserId = SecurityRootUtils.getFirstAdminUserId();
        surveysInsecure.forEach(targetId -> {
            HappyornotUpdateLink link = new HappyornotUpdateLink();
            link.setId(-1L);
            link.setCode(UUID.randomUUID().toString());
            final String token = Encryptor.encrypt(link.getCode(), Utilities.getSettings().getSystemId());
            link.setToken(token);
            link.setUrl(SettingsUtil.getAppUrlNoSlash() + URL_BASE_TOKEN + link.getToken());
            link.setCreatedBy(loggedUserId);
            link.setLastModifiedBy(loggedUserId);
            link.setCreatedDate(new Date());
            link.setLastModifiedDate(new Date());
            link.setStatus(1);
            link.setDeleted(false);
            link.setHappyornotTargetId(targetId);
            link.setUrlName("survey-" + link.getCode());
            dao.makePersistent(link, loggedUserId);

        });

    }
}
