package qms.framework.core;

import DPMS.DAOInterface.IFilesDAO;
import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import isoblock.common.Properties;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import qms.document.logic.DocumentHelper;
import qms.framework.entity.DocumentLink;
import qms.framework.util.DocumentLinkUtil;
import qms.framework.util.SettingsUtil;

/**
 *
 * <AUTHOR>
 */
public class DocumentLinkAction extends SessionViewer {

    private static final long serialVersionUID = 1L;

    private String token;
    private String error = null;

    public String downloadDocumentExternalLink() throws IOException, SQLException {
        IFilesDAO dao = getBean(IFilesDAO.class);
        DocumentLink link = DocumentLinkUtil.loadDocumentLink(token, dao);
        if (link == null) {
            error = getTag("invalid_link");
            return "error";
        }
        final DocumentLinkUtil linkUtil = new DocumentLinkUtil(link, dao, DocumentLink.TYPE.EXTERNAL_LINK);
        if (!linkUtil.validLink(getRequest())) {
            error = getTag(linkUtil.getError());
            return "error";
        }
        linkUtil.executeDownload(getResponse(), getLoggedUserDto());
        return null;
    }

    public String downloadDocumentShareLink() throws IOException, SQLException {
        final IFilesDAO dao = getBean(IFilesDAO.class);
        final DocumentLink link = DocumentLinkUtil.loadDocumentLink(token, dao);
        if (link == null) {
            error = getTag("invalid_link");
            return "error";
        }
        final DocumentLinkUtil linkUtil = new DocumentLinkUtil(link, dao, DocumentLink.TYPE.SHARED_LINK);
        final List<String> accessPermissionReasons = new DocumentHelper().getAccessPermissionReasons(
                link.getDocumentId(), 
                getLoggedUserDto().getId(), 
                getLoggedUserDto().isAdmin(), 
                getLoggedUserDto().getServices(), 
                false);
        if (!linkUtil.validLink(getRequest(), false, !accessPermissionReasons.isEmpty())) {
            error = getTag(linkUtil.getError());
            return "error";
        }
        linkUtil.executeDownload(getResponse(), getLoggedUserDto());
        return null;
    }

    public String getApplicationInfo() {
        return Properties.ApplicationData() + " - " + Utilities.getSettings().getLang() + "-" + Utilities.getSettings().getLocale();
    }

    public String getUrl() {
        return SettingsUtil.getAppUrlNoSlash();
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getToken() {
        return token;
    }

    public String getError() {
        return error;
    }

}
