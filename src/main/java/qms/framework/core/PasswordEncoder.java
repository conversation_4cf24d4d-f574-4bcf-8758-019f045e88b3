package qms.framework.core;

import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import java.util.Map;
import javax.servlet.ServletContext;
import mx.bnext.cipher.Encryptor;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 *
 * <AUTHOR> @ Bnext
 * @since Nov 16, 2016
 */
public class PasswordEncoder {

    public final static BCryptPasswordEncoder ENCODER = new BCryptPasswordEncoder();

    public static String encode(final String password) {
        if (password == null) {
            return null;
        }
        return ENCODER.encode(password);
    }

    public static boolean matches(final String raw, final String encoded) {
        return ENCODER.matches(raw, encoded);
    }

    public static void initialize(final ServletContext servletContext) {
        final IUntypedDAO dao = Utilities.getUntypedDAO(servletContext);
        hashPasswordUsers(dao);
        hashPasswordMailSettings(dao);
    }
    
    private static void hashPasswordMailSettings(final IUntypedDAO dao) {
        final String hql = " "
                + " SELECT new map( "
                    + " s.systemId as systemId, "
                    + " s.smtpPsw as password, "
                    + " s.systemId + '_' + s.rcKey as key "
                + " )"
                + " FROM " + Settings.class.getCanonicalName() + " s "
                + " WHERE s.hashedSmtpPsw is null ";
        final List<Map<String, Object>> settings = dao.HQL_findByQuery(hql);
        for (final Map<String, Object> setting : settings) {
            final String hash = Encryptor.encrypt(setting.get("password").toString(), setting.get("key").toString());
            final String update = " "
                    + " UPDATE " + Settings.class.getCanonicalName() + " "
                    + " SET "
                        + " hashedSmtpPsw = :hash,"
                        + " smtpPsw = ''"
                    + " WHERE systemId = :systemId";
            setting.put("hash", hash);
            dao.HQL_updateByQuery(update, setting);
        }
    }

    private static void hashPasswordUsers(final IUntypedDAO dao) {
        final String hql = " "
                + " SELECT  new map("
                    + " u.id as id,"
                    + " u.contrasena as password"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " u "
                + " WHERE u.hashedPassword IS NULL ";
        final String update = " "
                + " UPDATE " + User.class.getCanonicalName() + " "
                + " SET "
                    + " hashedPassword = :hash,"
                    + " contrasena = null"
                + " WHERE id = :id";
        final List<Map<String, Object>> users = dao.HQL_findByQuery(hql);
        for (final Map<String, Object> user : users) {
            final String hash = encode((String)user.get("password"));
            if (hash != null) {
                Long id = (Long)user.get("id");
                dao.HQL_updateByQuery(update, ImmutableMap.of("hash", hash, "id", id));
            }
        }
    }

}
