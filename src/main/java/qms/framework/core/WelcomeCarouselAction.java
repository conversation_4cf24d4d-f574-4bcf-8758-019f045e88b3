/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.core;

import DPMS.Mapping.FilesLite;
import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.io.IOException;
import java.sql.SQLException;
import javax.servlet.ServletOutputStream;
import mx.bnext.core.file.IFileData;
import qms.framework.file.FileManager;
import qms.preferences.entity.CarouselImages;

/**
 *
 * <AUTHOR>
 */
public class WelcomeCarouselAction extends SessionViewer {

    private Long id;

    @Override
    public String execute() throws Exception {
        try {
            downloadImageCarousel();
        } catch (SQLException ex) {
            getLogger().error("Failed to load image {}", new Object[]{ex.getSQLState(), ex});
        }
        return null;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    private void downloadImageCarousel() throws IOException, SQLException {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        final FilesLite file = dao.HQLT_findSimple(FilesLite.class, ""
                + " SELECT f"
                + " FROM " + FilesLite.class.getCanonicalName() + " f "
                + " JOIN " + CarouselImages.class.getCanonicalName() + " c "
                + " ON c.fileId = f.id"
                + " WHERE f.id = :id", 
                "id", 
                this.id
        );
        
        if (file == null) {
            getLogger().error("Invalid image '{}' not according to carousel image", id);
            return;
        }
        
        if (!ReportLogoAction.IMAGE_PATTERN.matcher(file.getContentType()).matches()) {
            getLogger().error("Invalid content type '{}' for file with id: {}", file.getContentType(), id);
            return;
        }

        final FileManager fileManager = new FileManager();
        final IFileData metadata = fileManager.newMetadataInstance(file);
        fileManager.writeHeadersWithDailyCache(response, file.getDescription(), file.getContentType(), file.getContentSize());
        try (final ServletOutputStream output = getResponse().getOutputStream()) {
            fileManager.writeFileContentToOutput(metadata, true, output, getLoggedUserDto());
        }
    }

}
