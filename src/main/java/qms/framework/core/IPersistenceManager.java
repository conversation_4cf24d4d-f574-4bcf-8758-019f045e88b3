package qms.framework.core;

import DPMS.Mapping.Persistable;
import Framework.DAO.IUntypedDAO;
import javax.annotation.Nonnull;
import qms.access.dto.ILoggedUser;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 * @param <TYPE> TYPE class of the entity
 */
public interface IPersistenceManager<TYPE extends Persistable> {
    
    public void persistBefore(IUntypedDAO dao, TYPE entity, Boolean useCodePrefix, @Nonnull ILoggedUser loggedUser) throws QMSException;
    
    public void persistAfter(IUntypedDAO dao, TYPE entity, @Nonnull ILoggedUser loggedUser) throws QMSException;
       
    
}
