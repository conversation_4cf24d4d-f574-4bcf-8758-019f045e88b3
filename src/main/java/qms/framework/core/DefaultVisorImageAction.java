package qms.framework.core;

import DPMS.DAOInterface.IFilesDAO;
import Framework.Config.Utilities;
import bnext.reference.document.FileRef;
import com.opensymphony.xwork2.ActionSupport;
import java.io.IOException;
import java.sql.SQLException;
import java.util.regex.Pattern;
import javax.servlet.ServletContext;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.file.IFileData;
import org.apache.struts2.action.ServletContextAware;
import org.apache.struts2.action.ServletResponseAware;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import qms.framework.file.FileManager;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR>
 */
public class DefaultVisorImageAction extends ActionSupport implements ServletResponseAware, ServletContextAware {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultVisorImageAction.class);
    private static final String IMAGE_REGEX = "image\\/bmp|image\\/jpeg|image\\/gif|image\\/png";
    private static final Pattern IMAGE_PATTERN = Pattern.compile(IMAGE_REGEX, Pattern.CASE_INSENSITIVE);

    private HttpServletResponse response;
    private ServletContext servletContext = null;

    @Override
    public String execute() throws IOException, SQLException {
        downloadDefaultFile();
        return null;
    }

    private void downloadDefaultFile() throws IOException, SQLException {
        final IFilesDAO dao = Utilities.getBean(IFilesDAO.class, this.servletContext);
        final Long defaultFileId = Utilities.getSettings().getDefaultFileId();
        final FileRef file = dao.HQLT_findById(FileRef.class, defaultFileId);
        if (file == null) {
            LOGGER.error("Not defined default visor file");
            return;
        }
        if (!IMAGE_PATTERN.matcher(file.getContentType()).matches()) {
            LOGGER.error("Invalid content type '{}' for file with id: {}", file.getContentType(), defaultFileId);
            return;
        }
        final FileManager fileManager = new FileManager();
        fileManager.writeHeadersWithDailyCache(getResponse(), file.getDescription(), file.getContentType(), file.getContentSize());
        final IFileData metadata = fileManager.newMetadataInstance(file);
        try (final ServletOutputStream output = getResponse().getOutputStream()) {
            fileManager.writeFileContentToOutput(metadata, output, SecurityUtils.getLoggedUser());
        }
    }
    
    @Override
    public void withServletResponse(HttpServletResponse response) {
        this.response = response;
    }

    public HttpServletResponse getResponse() {
        return response;
    }

    @Override
    public void withServletContext(ServletContext context) {
        this.servletContext = context;
    }

 }
