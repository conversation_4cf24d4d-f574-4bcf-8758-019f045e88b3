package qms.framework.core;

import Framework.Config.Mail;
import java.util.Date;
import java.util.Set;
import qms.framework.dto.EventDTO;
/**
 *
 * <AUTHOR>
 */
public interface IMailer {
    public boolean send(Mail sender, Mail recipient, String message, String module, Mailer.TYPE type);
    public boolean send(Mail remittent, Mail recipient, String message, EventDTO event, String module, Mailer.TYPE type);
    public boolean send(Mail remittent, Mail recipient, String message, Set<Long> attachmentFileIds, String module, Mailer.TYPE type);
    public boolean send(String subject, Mail remittent, Mail recipient, String message, EventDTO event, String module, Mailer.TYPE type);
    public boolean send(Mail remittent, Mail recipient, String message, Date startDate, Date endDate, String module, Mailer.TYPE type);
    public boolean send(String subject, Mail sender, Mail recipient, String message, String module, Mailer.TYPE type);
    public boolean send(String subject, Mail remittent, Mail recipient, String message, Set<Long> attachmentFileIds, String module, Mailer.TYPE type);
    public boolean send(String subject, Mail remittent, Mail recipient, String message, Date startDate, Date endDate, String module, Mailer.TYPE type);
    public boolean send(Mail remittent, Mail recipient, String message, String moduleUrl, String module, Mailer.TYPE type);
    public boolean send(String subject, Mail remittent, Mail recipient, String message, String moduleUrl, String module, Mailer.TYPE type);
}
