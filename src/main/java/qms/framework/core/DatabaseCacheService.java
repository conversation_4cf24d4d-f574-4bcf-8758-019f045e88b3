package qms.framework.core;

import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.IUntypedDAO;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.framework.entity.CacheQueryEvent;
import qms.framework.entity.DatabaseQuery;
import qms.util.ModuleUtil;

/**
 *
 * <AUTHOR>
 */
public class DatabaseCacheService extends CRUD_Generic<DatabaseQuery> {

    private static final long serialVersionUID = 1L;
    
    private Module module;

    public String getModule() {
        if (module == null) {
            return null;
        }
        return module.getKey();
    }
    
    public void setModule(String module) {
        if (module == null || module.trim().isEmpty()) {
            this.module = null;
        } else {
            this.module = ModuleUtil.fromKey(module);
        }
    }
    
    @Override
    public String smd() {
        if (canView()) {
            return SUCCESS;
        } else {
            return NO_ACCESS;
        }
    }
    
    private Boolean canView() {
        final Boolean isAdmin = isAdmin() || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA);
        if (isAdmin) {
            return true;
        }
        final Boolean canView = getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && (
                    Module.TIMESHEET.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.TS_VIEW_SYNC_EVENT)
                    || Module.FORMULARIE.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.FORM_VIEW_SYNC_EVENT)
                    || Module.ACTIVITY.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.ACTIVITY_VIEW_SYNC_EVENT)
                    || Module.TIMESHEET.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.TS_ENABLE_CACHE)
                    || Module.FORMULARIE.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.FORM_ENABLE_CACHE)
                    || Module.ACTIVITY.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.ACTIVITY_ENABLE_CACHE)
                    || Module.TIMESHEET.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.TS_SYNC_CACHE)
                    || Module.FORMULARIE.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.FORM_SYNC_CACHE)
                    || Module.ACTIVITY.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.ACTIVITY_SYNC_CACHE)
                    || Module.TIMEWORK.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.TIMEWORK_SYNC_CACHE)
                    || Module.ACTIVITY.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.TIMEWORK_VIEW_SYNC_CACHE_EVENT)
                    || Module.ACTIVITY.equals(module)
                    && getLoggedUserServices().contains(ProfileServices.TIMEWORK_ENABLE_CACHE)
                );
        return canView;
    }

    
    @SMDMethod 
    public GridInfo<Map<String, Object>> getCacheRows(final SortedPagedFilter filter) {
        if (canView()) {
            if (module == null) {
                getLogger().error(
                        "Invalid module to get reports {}.", 
                        module
                );
                return Utilities.EMPTY_GRID_INFO;
            }
            filter.getCriteria().put("<condition>", "c.deleted = 0 AND c.module = '" + module.getKey() + "'");
            final IUntypedDAO dao = getUntypedDAO();
            return dao.HQL_getRows(""
                    + " SELECT new map("
                        + " c.id AS id,"
                        + " c.status AS status,"
                        + " c.code AS code,"
                        + " c.description AS description,"
                        + " c.createdDate AS createdDate,"
                        + " c.lastModifiedDate AS lastModifiedDate,"
                        + " c.source AS source,"
                        + " c.schedulerType AS schedulerType,"
                        + " c.databaseSchema AS databaseSchema,"
                        + " c.databaseTable AS databaseTable,"
                        + " c.primaryKeyValue AS primaryKeyValue,"
                        + " c.primaryKeyType AS primaryKeyType,"
                        + " c.builtCache as builtCache,"
                        + " c.numberRecordsUpdated AS numberRecordsUpdated,"
                        + " c.numberRecordsInserted AS numberRecordsInserted,"
                        + " c.startDate AS startDate,"
                        + " c.endDate AS endDate,"
                        + " c.primaryKeyCode AS primaryKeyCode,"
                        + " c.versionKeyCode AS versionKeyCode,"
                        + " q.id AS queryId,"
                        + " q.code AS queryCode,"
                        + " q.description AS queryDescription,"
                        + " con.id as connectionId," 
                        + " con.description as connectionDescription," 
                        + " se.id as systemEventId," 
                        + " se.description as systemEventDescription," 
                        + " lu.description as lastModifiedByUser" 
                    + " )"
                    + " FROM " + CacheQueryEvent.class.getCanonicalName() + " c "
                    + " LEFT JOIN c.query q"
                    + " LEFT JOIN c.connection con "
                    + " LEFT JOIN c.systemEvent se "
                    + " LEFT JOIN c.lastModifiedByUser lu "
                    + " WHERE c.deleted = 0 AND c.module = '" + module.getKey() + "'", filter);
        } else {
            return Utilities.EMPTY_GRID_INFO;
        }
    }
    
    
    
}