package qms.framework.core;

import DPMS.Mapping.AuditIndividualLite;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class AuditProgramDto implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long businessUnitDepartmentId;
    private List<AuditIndividualLite> audits = new ArrayList<>();

    public AuditProgramDto() {

    }

    public AuditProgramDto(Long id) {
        this.businessUnitDepartmentId = id;
    }

    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }
    
    public List<AuditIndividualLite> getAudits() {
        return audits;
    }

    public void setAudits(List<AuditIndividualLite> audits) {
        this.audits = audits;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + Objects.hashCode(this.getBusinessUnitDepartmentId());
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final AuditProgramDto other = (AuditProgramDto) obj;
        return Objects.equals(this.getBusinessUnitDepartmentId(), other.getBusinessUnitDepartmentId());
    }

    @Override
    public String toString() {
        return "AuditProgram{" + "id=" + this.getBusinessUnitDepartmentId() + '}';
    }

}
