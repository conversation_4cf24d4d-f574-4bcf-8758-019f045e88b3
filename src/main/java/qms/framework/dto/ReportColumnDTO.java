/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.framework.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.apache.struts2.json.annotations.JSON;
import qms.form.dto.SurveyDataFieldDTO;
import qms.framework.enums.AggregateFunctionType;
import qms.framework.util.ReportColumnType;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportColumnDTO implements IReportColumnDTO {

    private Boolean isFromOtherSystem;
    private boolean nullable = true;
    private Boolean onlyFilter;
    private Integer gridOrder;
    private Integer groupingDirection;
    private Integer groupingPriority;
    private String groupedValueKey;
    private String groupedValueFunction;
    private Integer inSlimReportColumn;
    private Integer sortDirection;
    private Integer sortPriority;
    /**
     * Devuelve un valor de tipo {@link qms.framework.util.ReportColumnType}
     */
    private Integer type;
    private List<Object> catalogValues;
    private Set<? extends IReportColumnRuleDTO> rules;
    private Long hierarchyId;
    private Long id;
    private Long queryColumnId;
    private Long reportId;
    private Long slimReportId;
    private QueryColumnDTO queryColumn;
    private String description;
    private String hierarchyCode;
    private String hierarchyDescription;
    private String queryColumnCode;
    private String width;

    public ReportColumnDTO(
            Long id,
            Integer type,
            String description,
            Integer gridOrder,
            String width,
            Long hierarchyId,
            String hierarchyCode,
            String hierarchyDescription,
            Integer sortPriority,
            Integer sortDirection,
            Integer groupingPriority,
            Integer groupingDirection,
            Long reportId,
            Boolean onlyFilter,
            Long columnId, 
            String columnCode,
            String columnDescription,
            String columnHierarchyLevelLabel,
            Integer columnHierarchyLevelOrder,
            Integer columnHierarchyReadonlyOrder,
            String columnHierarchyReadonlyLabel,
            Integer columnHierarchyReadonlyLevel,
            Integer columnHierarchyReadonlyVisible,
            String columnDataType,
            Long columnQueryId,
            Long slimReportId,
            Integer inSlimReportColumn
    ) {
        this.id = id;
        this.type = type;
        this.description = description;
        this.queryColumnId = columnId;
        this.queryColumnCode = columnCode;
        this.gridOrder = gridOrder;
        this.width = width;
        this.hierarchyId = hierarchyId;
        this.hierarchyCode = hierarchyCode;
        this.hierarchyDescription = hierarchyDescription;
        this.sortPriority = sortPriority;
        this.sortDirection = sortDirection;
        this.groupingPriority = groupingPriority;
        this.groupingDirection = groupingDirection;
        this.reportId = reportId;
        this.onlyFilter = onlyFilter;
        this.queryColumn = new QueryColumnDTO(
                columnId,
                columnCode,
                columnDescription,
                columnHierarchyLevelLabel,
                columnHierarchyLevelOrder,
                columnHierarchyReadonlyOrder,
                columnHierarchyReadonlyLabel,
                columnHierarchyReadonlyLevel,
                columnHierarchyReadonlyVisible,
                columnDataType,
                columnQueryId
        );
        this.slimReportId = slimReportId;
        this.inSlimReportColumn = inSlimReportColumn;
    }

    /**
     * Se usa desde un constructor en un HQL.
     * Constructor para columnas de tipo `isFromOtherSystem` y `SlimReportsGhostField`.
     */
    public ReportColumnDTO(
            Long reportId,
            Long slimReportId,
            String columnCode,
            String description,
            Integer gridOrder
    ) {
        this(reportId, slimReportId, columnCode, description, true, ReportColumnType.TEXT, gridOrder);
    }

    /**
     * Se usa desde un constructor en un HQL.
     * Constructor para columnas de tipo `SlimReportsSummaryGroupField`.
     */
    public ReportColumnDTO(
            Long reportId,
            Long slimReportId,
            String columnCode,
            String description,
            Integer gridOrder,
            String groupedValueKey,
            Integer groupedValueFunction
    ) {
        this(reportId, slimReportId, columnCode, description, true, ReportColumnType.TEXT, gridOrder);
        this.groupedValueFunction = AggregateFunctionType.fromValue(groupedValueFunction, AggregateFunctionType.SUM).name();
        this.groupedValueKey = groupedValueKey;
    }

    /**
     * Se usa desde constructores en HQL.
     * Constructor para columnas de tipo `isFromOtherSystem` y `SlimReportsGhostField`.
     */
    public ReportColumnDTO(
            Long reportId,
            Long slimReportId,
            String columnCode,
            String description,
            boolean nullable,
            ReportColumnType type,
            Integer gridOrder
    ) {
        this.nullable = nullable;
        this.type = type.getValue();
        this.description = description;
        this.queryColumnCode = columnCode;
        this.reportId = reportId;
        this.slimReportId = slimReportId;
        this.inSlimReportColumn = 0;
        this.isFromOtherSystem = true;
        this.gridOrder = gridOrder;
    }

    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getQueryColumnId() {
        return queryColumnId;
    }

    public void setQueryColumnId(Long queryColumnId) {
        this.queryColumnId = queryColumnId;
    }

    public String getQueryColumnCode() {
        return queryColumnCode;
    }

    public void setQueryColumnCode(String queryColumnCode) {
        this.queryColumnCode = queryColumnCode;
    }

    @Override
    public String getHierarchyDescription() {
        return hierarchyDescription;
    }

    public void setHierarchyDescription(String hierarchyDescription) {
        this.hierarchyDescription = hierarchyDescription;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     *
     * @return Devuelve un valor de tipo {@link qms.framework.util.ReportColumnType}
     */
    @Override
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public Integer getGridOrder() {
        return gridOrder;
    }

    public void setGridOrder(Integer gridOrder) {
        this.gridOrder = gridOrder;
    }

    public List<Object> getCatalogValues() {
        return catalogValues;
    }

    public void setCatalogValues(List<Object> catalogValues) {
        this.catalogValues = catalogValues;
    }

    @Override
    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    @Override
    public Integer getSortPriority() {
        return sortPriority;
    }

    public void setSortPriority(Integer sortPriority) {
        this.sortPriority = sortPriority;
    }

    @Override
    public Integer getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(Integer sortDirection) {
        this.sortDirection = sortDirection;
    }

    @Override
    public Integer getGroupingPriority() {
        return groupingPriority;
    }

    public void setGroupingPriority(Integer groupingPriority) {
        this.groupingPriority = groupingPriority;
    }

    @Override
    public Integer getGroupingDirection() {
        return groupingDirection;
    }

    public void setGroupingDirection(Integer groupingDirection) {
        this.groupingDirection = groupingDirection;
    }

    @Override
    public Long getHierarchyId() {
        return hierarchyId;
    }

    public void setHierarchyId(Long hierarchyId) {
        this.hierarchyId = hierarchyId;
    }

    @Override
    public String getHierarchyCode() {
        return hierarchyCode;
    }

    public void setHierarchyCode(String hierarchyCode) {
        this.hierarchyCode = hierarchyCode;
    }

    @Override
    public Long getReportId() {
        return reportId;
    }

    public void setReportId(Long reportId) {
        this.reportId = reportId;
    }

    @Override
    public QueryColumnDTO getQueryColumn() {
        return queryColumn;
    }

    public void setQueryColumn(QueryColumnDTO queryColumn) {
        this.queryColumn = queryColumn;
    }

    @Override
    public int compareTo(IReportColumnDTO o) {
        if (gridOrder == null) {
            return -1;
        }
        if (o == null || o.getGridOrder() == null) {
            return 1;
        }
        if (gridOrder == null) {
            return 0;
        }
        return gridOrder.compareTo(o.getGridOrder());
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 67 * hash + Objects.hashCode(this.queryColumnCode);
        hash = 67 * hash + Objects.hashCode(this.gridOrder);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (!(obj instanceof IReportColumnDTO)) {
            return false;
        }
        final IReportColumnDTO other = (IReportColumnDTO) obj;
        if (!Objects.equals(this.queryColumnCode, other.getQueryColumnCode())) {
            return false;
        }
        return Objects.equals(this.gridOrder, other.getGridOrder());
    }

    @Override
    public String toString() {
        return "ReportColumn{" + "column=" + queryColumnCode + '}';
    }

    public Long getSlimReportId() {
        return slimReportId;
    }

    public void setSlimReportId(Long slimReportId) {
        this.slimReportId = slimReportId;
    }

    public Integer getInSlimReportColumn() {
        return inSlimReportColumn;
    }

    public void setInSlimReportColumn(Integer inSlimReportColumn) {
        this.inSlimReportColumn = inSlimReportColumn;
    }

    public Boolean getFromOtherSystem() {
        return isFromOtherSystem;
    }

    public void setFromOtherSystem(Boolean fromOtherSystem) {
        isFromOtherSystem = fromOtherSystem;
    }

    public Set<? extends IReportColumnRuleDTO> getRules() {
        return rules;
    }

    public void setRules(Set<? extends IReportColumnRuleDTO> rules) {
        this.rules = rules;
    }

    @JSON(serialize = false)
    @JsonIgnore
    @Override
    public String getName() {
        return this.queryColumnCode;
    }

    @JSON(serialize = false)
    @JsonIgnore
    @Override
    public Boolean getAllowNulls() {
        return nullable;
    }

    @JSON(serialize = false)
    @JsonIgnore
    @Override
    public Boolean getIsSystem() {
        return false;
    }

    @JSON(serialize = false)
    @JsonIgnore
    @Override
    public Set<SurveyDataFieldDTO> getAssociatedFields() {
        return Collections.emptySet();
    }

    @Override
    public Boolean getOnlyFilter() {
        return onlyFilter;
    }

    public void setOnlyFilter(Boolean onlyFilter) {
        this.onlyFilter = onlyFilter;
    }

    public String getGroupedValueKey() {
        return groupedValueKey;
    }

    public void setGroupedValueKey(String groupedValueKey) {
        this.groupedValueKey = groupedValueKey;
    }

    public String getGroupedValueFunction() {
        return groupedValueFunction;
    }

    public void setGroupedValueFunction(String groupedValueFunction) {
        this.groupedValueFunction = groupedValueFunction;
    }
}
