package qms.framework.dto;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import qms.access.interfaces.IPlainUser;
import qms.document.interfaces.IJoinableDocument;
import qms.document.interfaces.IPlainDocument;
import qms.document.interfaces.IPlainDocumentType;
import qms.document.interfaces.IPlainNode;
import qms.document.interfaces.IPlainRequest;
import qms.document.interfaces.IJoinableRequest;
import qms.framework.interfaces.IPlainBusinessUnit;
import qms.framework.interfaces.IPlainFile;
import qms.framework.interfaces.IPlainOrganizationalUnit;
import qms.framework.interfaces.IPlainSearchBusinessUnitDepartment;
import qms.survey.interfaces.IPlainSurvey;

public class DocumentDataIndex {

    private final Map<Long, IPlainDocument> plainDocuments = new ConcurrentHashMap<>();
    private final Map<Long, IJoinableDocument> joinableDocuments = new ConcurrentHashMap<>();
    private final Map<Long, IPlainBusinessUnit> businessUnits = new ConcurrentHashMap<>();
    private final Map<Long, IPlainDocumentType> documentTypes = new ConcurrentHashMap<>();
    private final Map<Long, IPlainFile> files = new ConcurrentHashMap<>();
    private final Map<Long, IPlainNode> nodes = new ConcurrentHashMap<>();
    private final Map<Long, IPlainOrganizationalUnit> organizationalUnits = new ConcurrentHashMap<>();
    private final Map<Long, IPlainSearchBusinessUnitDepartment> businessUnitDepartments = new ConcurrentHashMap<>();
    private final Map<Long, IPlainSurvey> surveys = new ConcurrentHashMap<>();
    private final Map<Long, IPlainUser> users = new ConcurrentHashMap<>();
    private final Map<Long, IPlainRequest> plainRequests = new ConcurrentHashMap<>();
    private final Map<Long, IJoinableRequest> joinableRequests = new ConcurrentHashMap<>();

    public IPlainDocument getPlainDocument(Long id) {
        if (id == null) {
            return null;
        }
        return plainDocuments.get(id);
    }

    public void setPlainDocument(Long id, IPlainDocument document) {
        if (id == null || document == null) {
            return;
        }
        plainDocuments.put(id, document);
    }

    public IJoinableDocument getJoinableDocument(Long id) {
        if (id == null) {
            return null;
        }
        return joinableDocuments.get(id);
    }

    public void setJoinableDocument(Long id, IJoinableDocument document) {
        if (id == null || document == null) {
            return;
        }
        joinableDocuments.put(id, document);
    }

    public IPlainBusinessUnit getBusinessUnit(Long id) {
        if (id == null) {
            return null;
        }
        return businessUnits.get(id);
    }

    public void setBusinessUnit(Long id, IPlainBusinessUnit businessUnit) {
        if (id == null || businessUnit == null) {
            return;
        }
        businessUnits.put(id, businessUnit);
    }


    public IPlainDocumentType getDocumentType(Long id) {
        if (id == null) {
            return null;
        }
        return documentTypes.get(id);
    }

    public void setDocumentType(Long id, IPlainDocumentType documentType) {
        if (id == null || documentType == null) {
            return;
        }
        documentTypes.put(id, documentType);
    }

    public IPlainFile getFile(Long id) {
        if (id == null) {
            return null;
        }
        return files.get(id);
    }

    public void setFile(Long id, IPlainFile file) {
        if (id == null || file == null) {
            return;
        }
        files.put(id, file);
    }

    public IPlainNode getNode(Long id) {
        if (id == null) {
            return null;
        }
        return nodes.get(id);
    }

    public void setNode(Long id, IPlainNode node) {
        if (id == null || node == null) {
            return;
        }
        nodes.put(id, node);
    }

    public IPlainOrganizationalUnit getOrganizationalUnit(Long id) {
        if (id == null) {
            return null;
        }
        return organizationalUnits.get(id);
    }

    public void setOrganizationalUnit(Long id, IPlainOrganizationalUnit organizationalUnit) {
        if (id == null || organizationalUnit == null) {
            return;
        }
        organizationalUnits.put(id, organizationalUnit);
    }

    public IPlainSearchBusinessUnitDepartment getBusinessUnitDepartment(Long id) {
        if (id == null) {
            return null;
        }
        return businessUnitDepartments.get(id);
    }

    public void setBusinessUnitDepartment(Long id, IPlainSearchBusinessUnitDepartment businessUnitDepartment) {
        if (id == null || businessUnitDepartment == null) {
            return;
        }
        businessUnitDepartments.put(id, businessUnitDepartment);
    }

    public IPlainSurvey getSurvey(Long id) {
        if (id == null) {
            return null;
        }
        return surveys.get(id);
    }

    public void setSurvey(Long id, IPlainSurvey survey) {
        if (id == null || survey == null) {
            return;
        }
        surveys.put(id, survey);
    }

    public IPlainUser getUser(Long id) {
        if (id == null) {
            return null;
        }
        return users.get(id);
    }

    public void setUser(Long id, IPlainUser user) {
        if (id == null || user == null) {
            return;
        }
        users.put(id, user);
    }

    public IPlainRequest getPlainRequest(Long id) {
        return plainRequests.get(id);
    }

    public void setPlainRequest(Long id, IPlainRequest request) {
        if (id == null || request == null) {
            return;
        }
        plainRequests.put(id, request);
    }

    public IJoinableRequest getJoinableRequest(Long id) {
        return joinableRequests.get(id);
    }

    public void setJoinableRequest(Long id, IJoinableRequest request) {
        if (id == null || request == null) {
            return;
        }
        joinableRequests.put(id, request);
    }

    public boolean containsPlainDocument(Long id) {
        if (id == null) {
            return false;
        }
        return plainDocuments.containsKey(id);
    }

    public boolean containsJoinableDocument(Long id) {
        if (id == null) {
            return false;
        }
        return joinableDocuments.containsKey(id);
    }
    public boolean containsBusinessUnit(Long id) {
        if (id == null) {
            return false;
        }
        return businessUnits.containsKey(id);
    }

    public boolean containsDocumentType(Long id) {
        if (id == null) {
            return false;
        }
        return documentTypes.containsKey(id);
    }

    public boolean containsFile(Long id) {
        if (id == null) {
            return false;
        }
        return files.containsKey(id);
    }

    public boolean containsNode(Long id) {
        if (id == null) {
            return false;
        }
        return nodes.containsKey(id);
    }

    public boolean containsOrganizationalUnit(Long id) {
        if (id == null) {
            return false;
        }
        return organizationalUnits.containsKey(id);
    }

    public boolean containsBusinessUnitDepartment(Long id) {
        if (id == null) {
            return false;
        }
        return businessUnitDepartments.containsKey(id);
    }

    public boolean containsSurvey(Long id) {
        if (id == null) {
            return false;
        }
        return surveys.containsKey(id);
    }

    public boolean containsUser(Long id) {
        if (id == null) {
            return false;
        }
        return users.containsKey(id);
    }

    public boolean containsPlainRequest(Long id) {
        if (id == null) {
            return false;
        }
        return plainRequests.containsKey(id);
    }

    public boolean containsJoinableRequest(Long id) {
        if (id == null) {
            return false;
        }
        return joinableRequests.containsKey(id);
    }

}
