package qms.framework.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import qms.custom.core.IDynamicTableField;
import qms.custom.dto.BaseCustomField;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class QueryColumnDynDto extends BaseCustomField implements IDynamicTableField<String>  {

    private static final long serialVersionUID = 1L;
    
    private Long databaseQueryId;

    public QueryColumnDynDto() {
        super(null, null);
    }

    public QueryColumnDynDto(String name, String type) {
        super(name, type);
    }

    public Long getDatabaseQueryId() {
        return databaseQueryId;
    }

    public void setDatabaseQueryId(Long databaseQueryId) {
        this.databaseQueryId = databaseQueryId;
    }

    @Override
    public String toString() {
        return "QueryColumnDto{" + "databaseQueryId=" + databaseQueryId + '}';
    }

    
}
