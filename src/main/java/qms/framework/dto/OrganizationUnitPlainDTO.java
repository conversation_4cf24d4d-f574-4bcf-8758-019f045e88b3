package qms.framework.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;
import qms.framework.interfaces.IPlainOrganizationalUnit;

/**
 * Solo atributos de tipo primitivo, no agregar joins
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OrganizationUnitPlainDTO implements IPlainOrganizationalUnit, Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Long predecessorId;
    private Long documentManagerId;
    private String code;
    private String description;
    private Integer status;
    private Integer deleted;

    public OrganizationUnitPlainDTO() {
    }

    public OrganizationUnitPlainDTO(
            Long id,
            Long predecessorId,
            Long documentManagerId,
            String code,
            String description,
            Integer status,
            Integer deleted
    ) {
        this.id = id;
        this.predecessorId = predecessorId;
        this.documentManagerId = documentManagerId;
        this.code = code;
        this.description = description;
        this.status = status;
        this.deleted = deleted;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public Long getPredecessorId() {
        return predecessorId;
    }

    @Override
    public void setPredecessorId(Long predecessorId) {
        this.predecessorId = predecessorId;
    }

    @Override
    public Long getDocumentManagerId() {
        return documentManagerId;
    }

    @Override
    public void setDocumentManagerId(Long documentManagerId) {
        this.documentManagerId = documentManagerId;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        OrganizationUnitPlainDTO that = (OrganizationUnitPlainDTO) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "OrganizationUnitEntityDTO{" +
                "id=" + id +
                '}';
    }
}
