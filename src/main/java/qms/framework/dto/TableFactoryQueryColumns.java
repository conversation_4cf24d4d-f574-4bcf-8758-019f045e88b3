package qms.framework.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.core.ITableFactory;
import qms.custom.core.TableFactoryBase;
import qms.custom.dto.BaseCustomField;
import qms.custom.dto.TableReferenceIdDTO;
import qms.custom.entity.DynamicFieldTable;
import qms.custom.entity.DynamicMetadata;
import qms.form.entity.DatabaseQueryMetadata;
import qms.framework.entity.DatabaseQuery;
import qms.util.QMSException;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TableFactoryQueryColumns extends TableFactoryBase<String, BaseCustomField> implements ITableFactory<String, BaseCustomField> {

    private String entityName;
    private String primaryKeyName;
    private String schema;
    private String sessionStatement;
    private List<BaseCustomField> fields;
    private Boolean allowComputedColumns = false;
    public TableFactoryQueryColumns() {
    }

    public TableFactoryQueryColumns(final DatabaseQuery query,  final List<BaseCustomField> fields) {
        this.entityName =  query.getCacheTable();
        this.schema = query.getCacheSchema();
        this.primaryKeyName = query.getCachePrimaryKeyCode();
        this.sessionStatement = query.getSessionStatement();
        fields.stream().filter(field -> field.getName().equals(query.getCachePrimaryKeyCode())).forEach(field -> {
            field.setAllowNulls(false);
        });
        this.fields = fields;
    }

    @Override
    public Set<TableReferenceIdDTO> getJoinableEntities() throws ClassNotFoundException, NoSuchMethodException {
        final Set<TableReferenceIdDTO> tableIdMap = new HashSet<>(0);
        return tableIdMap;
    }

    @Override
    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    @Override
    public String getInstanceSchema() {
        return schema;
    }

    @Override
    public List<BaseCustomField> getFields() {
        return fields;
    }

    @Override
    public String getInstanceTableName() throws QMSException {
        return entityName;
    }

    @Override
    public boolean persistMetadata(
            final DynamicFieldTable table,
            final BaseCustomField field,
            final String columnType,
            final Long loggedUserId,
            final IDynamicTableDAO dao
    )  throws QMSException {
        final DatabaseQueryMetadata metadata = new DatabaseQueryMetadata(-1L);
        metadata.setCode("SA" + dao.getCodeSequence().next(DatabaseQueryMetadata.class));
        metadata.setDescription(columnType);
        metadata.setStatus(DynamicMetadata.STATUS.ACTIVE.getValue());
        metadata.setDeleted(0);
        metadata.setDatabaseQueryColumnId(field.getId());
        metadata.setColumnCode(field.getName());
        metadata.setDynamicFieldTableId(table.getId());
        metadata.setDynamicFieldTableName(table.getDescription());
        if (field instanceof QueryColumnDynDto) {
            metadata.setDatabaseQueryId(((QueryColumnDynDto)field).getDatabaseQueryId());
        }
        return dao.makePersistent(metadata, loggedUserId) != null;
    }

    @Override
    public Boolean getAllowComputedColumns() {
        return allowComputedColumns;
    }

    @Override
    public String getParsedColumnType(BaseCustomField field) {
        switch(field.getType().toLowerCase()) {
            case "varchar":
                return TableFactoryColumnType.VARCHAR4000_COLUMN_TYPE.getValue();
            case "nvarchar":
                return TableFactoryColumnType.NVARCHAR4000_COLUMN_TYPE.getValue();
            case "numeric":
                return TableFactoryColumnType.NUMERIC.getValue();
            case "decimal":
                return TableFactoryColumnType.DECIMAL.getValue();
            case "smallint":
                return TableFactoryColumnType.SMALLINT.getValue();
            default:
                return field.getType();
        }
    }
    @Override
    public String getPrimayKeyMame() {
        return this.primaryKeyName;
    }
    
    @Override
    public Boolean getHasAuditColumns() {
        return false;
    }
    
    @Override
    public String getSessionStatement() {
        return sessionStatement;
    }

}
