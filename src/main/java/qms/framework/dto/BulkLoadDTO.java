package qms.framework.dto;

import java.io.Serializable;
import java.util.Objects;

public class BulkLoadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Long noCreations = 0L;
    private Long noUpdates = 0L;
    private Long noFailures = 0L;
    private Long activeUsersCount = 0L;
    private Long inactiveUsersCount = 0L;
    private Long requireLicenseCount = 0L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNoCreations() {
        return noCreations;
    }

    public void setNoCreations(Long noCreations) {
        this.noCreations = noCreations;
    }

    public Long getNoUpdates() {
        return noUpdates;
    }

    public void setNoUpdates(Long noUpdates) {
        this.noUpdates = noUpdates;
    }

    public Long getNoFailures() {
        return noFailures;
    }

    public void setNoFailures(Long noFailures) {
        this.noFailures = noFailures;
    }

    public Long getActiveUsersCount() {
        return activeUsersCount;
    }

    public void setActiveUsersCount(Long activeUsersCount) {
        this.activeUsersCount = activeUsersCount;
    }

    public Long getInactiveUsersCount() {
        return inactiveUsersCount;
    }

    public void setInactiveUsersCount(Long inactiveUsersCount) {
        this.inactiveUsersCount = inactiveUsersCount;
    }

    public Long getRequireLicenseCount() {
        return requireLicenseCount;
    }

    public void setRequireLicenseCount(Long requireLicenseCount) {
        this.requireLicenseCount = requireLicenseCount;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 11 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BulkLoadDTO other = (BulkLoadDTO) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "BulkLoadDTO{" + "id=" + id + '}';
    }

}
