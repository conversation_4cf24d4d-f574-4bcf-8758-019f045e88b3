package qms.framework.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SynonymDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String sourceTable;
    private String masterId;
    private String version;

    public SynonymDTO(String sourceTable, String masterId, String version) {
        this.sourceTable = sourceTable;
        this.masterId = masterId;
        this.version = version;
    }

    public String getSourceTable() {
        return sourceTable;
    }

    public void setSourceTable(String sourceTable) {
        this.sourceTable = sourceTable;
    }

    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 79 * hash + Objects.hashCode(this.sourceTable);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SynonymDTO other = (SynonymDTO) obj;
        if (!Objects.equals(this.sourceTable, other.sourceTable)) {
            return false;
        }
        if (!Objects.equals(this.masterId, other.masterId)) {
            return false;
        }
        return Objects.equals(this.version, other.version);
    }


    @Override
    public String toString() {
        return "SynonymDTO{" + "sourceTable=" + sourceTable + ", targetName=" + masterId + '}';
    }

    
}
