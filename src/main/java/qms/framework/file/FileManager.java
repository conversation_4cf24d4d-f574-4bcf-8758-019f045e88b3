package qms.framework.file;

import DPMS.DAOInterface.IDocumentTypeDAO;
import DPMS.DAOInterface.IFilesDAO;
import DPMS.DAOInterface.ISettingsDAO;
import DPMS.Mapping.FilePdfImage;
import DPMS.Mapping.FilesLite;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import bnext.dto.DownloadDto;
import bnext.reference.document.FileRef;
import com.google.common.net.HttpHeaders;
import java.awt.image.BufferedImage;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nonnull;
import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.errors.ClientAbortException;
import mx.bnext.core.file.FileCacheManager;
import mx.bnext.core.file.FileHandler;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.file.IFileData;
import mx.bnext.core.file.IFileManager;
import mx.bnext.core.file.IFileRepository;
import mx.bnext.core.file.IPdfConversionEnabled;
import mx.bnext.core.file.PdfViewerStatus;
import mx.bnext.core.file.TempPath;
import mx.bnext.core.file.cache.CacheType;
import mx.bnext.core.file.dto.FileData;
import mx.bnext.core.file.dto.PdfPageId;
import mx.bnext.core.file.dto.PdfViewerInfo;
import mx.bnext.core.file.dto.RotationId;
import mx.bnext.core.i18n.LocaleManager;
import mx.bnext.core.pdf.PdfProgram;
import mx.bnext.core.pdf.dto.PdfConversionDTO;
import mx.bnext.core.pdf.errors.InvalidPdfGeneration;
import mx.bnext.core.pdf.errors.PdfExecutorNotAvailableException;
import mx.bnext.core.pdf.errors.RotationImageException;
import mx.bnext.core.pdf.rotation.RotationDegrees;
import mx.bnext.core.pdf.rotation.RotationImageExecutor;
import mx.bnext.core.pdf.rotation.RotationImageThread;
import mx.bnext.core.pdf.util.IPdfDaemonConfig;
import mx.bnext.core.pdf.util.IPdfManager;
import mx.bnext.core.pdf.util.PdfGeneratorSetup;
import mx.bnext.core.security.CryptoUtils;
import mx.bnext.core.security.ISecurityUser;
import mx.bnext.core.util.KnownExceptions;
import mx.bnext.core.util.Loggable;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.http.CacheControl;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.framework.daemon.BnextDaemonUtil;
import qms.framework.dto.UploadFileResult;
import qms.framework.mail.FeedbackMailer;
import qms.framework.pdf.PdfProgramUtil;
import qms.framework.pdf.rendering.PdfPageExecutor;
import qms.framework.pdf.rendering.PdfPageFactory;
import qms.framework.pdf.rendering.PdfPageThumbnailManager;
import qms.framework.pdf.rendering.PdfPageVolatileExecutor;
import qms.framework.pdf.rendering.PdfPageVolatileFactory;
import qms.framework.pdf.rendering.dto.PdfPageResult;
import qms.framework.player.VideoCompatibilityHTML5;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.FileLoggingType;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
public class FileManager implements IFileManager {

    private static final String FILE_PDF_PREFFIX = "file-pdf-";
    private static final String FILE_PDF_PAGE_PREFFIX = "file-pdg-page-";
    private static final String FILE_CONTENT_PREFFIX = "file-content-";

    private static final Logger LOGGER = Loggable.getLogger(FileManager.class);
    private static final String ROTATION_SOURCE_PREFFIX = "rotation-source-";
    private static final String THUMBNAIL_PREFFIX = "thumbnail-";
    public final static String PDF_GENERATOR_NOT_AVAILABLE = "PDF_GENERATOR_NOT_AVAILABLE";
    private static final int THUMBNAIL_SIZE = 334;
    public static final String WEBP_IMAGE_EXTENSION = "webp";
    public static final String CONTEXT_TYPE_WEBP = "image/webp";
    public static final String CONTEXT_TYPE_DWG = "image/vnd.dwg";
    // Tamaño máximo predeterminado 52428800 son 52MB;     
    public static final long DEFAULT_MAX_FILE_SIZE = 52428800L;

    private final FileCacheManager cache;
    private IPdfDaemonConfig config;
    private final LocaleManager localeManager;
    private Path tempFolder;

    public FileManager() {
        this.config = PdfProgramUtil.getPdfConfig();
        this.cache = new FileCacheManager(config);
        this.tempFolder = FileHandler.createTempFolder("temp-files", config.getBuildInfo().getAppName());
        this.localeManager = LocaleManager.buildLocaleManager(config.getSettings(), getClass().getCanonicalName());
    }

    public FileManager(final IPdfDaemonConfig config) {
        this.cache = new FileCacheManager(config);
        this.config = config;
        this.tempFolder = FileHandler.createTempFolder("temp-files", config.getBuildInfo().getAppName());
        this.localeManager = LocaleManager.buildLocaleManager(config.getSettings(), getClass().getCanonicalName());
    }

    @Override
    public void setTempFolder(Path tempFolder) throws IOException {
        this.tempFolder = tempFolder;
    }
    
    @Override
    public void changeToMsOffice(final Exception exception) {
        LOGGER.error("Changing office program to ms_office.", exception);
        final ISettingsDAO settingsDao = Utilities.getBean(ISettingsDAO.class);
        settingsDao.update("programConvertToPDF", PdfProgram.MS_OFFICE.getName());
        try {
            Utilities.getSettings().setProgramConvertToPDF(PdfProgram.MS_OFFICE.getName());
            config = PdfProgramUtil.getPdfConfig();
            config.getPdfManager().initialize();
        } catch (final Exception ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            LOGGER.error("Fail while reconfiguring Office connection. StackTrace: ", ex, exception);
            throw new RuntimeException("Fail while reconfiguring Office connection", exception);
        }
    }

    public PdfConversionDTO generateFilePdfAndDownload(
            final DownloadDto downloadDto,
            final boolean isAdmin,
            final List<ProfileServices> loggedUserServices,
            final HttpServletResponse response,
            final String column,
            final ISecurityUser loggedUser
    ) throws FileNotFoundException {
        return generateFilePdfAndDownload(downloadDto, isAdmin, loggedUserServices, Utilities.EMPTY_LIST, response, column, loggedUser, false, null);
    }

    public PdfConversionDTO generateFilePdfAndDownload(
            final DownloadDto downloadDto,
            final boolean isAdmin,
            final List<ProfileServices> loggedUserServices,
            final List<ProfileServices> loggedUserServicesAddedBySystem,
            final HttpServletResponse response,
            final String column,
            final ISecurityUser loggedUser
    ) throws FileNotFoundException {
        return generateFilePdfAndDownload(downloadDto, isAdmin, loggedUserServices, loggedUserServicesAddedBySystem, response, column, loggedUser, false, null);
    }

    public PdfConversionDTO generateFilePdfAndDownload(
            final DownloadDto downloadDto,
            final boolean isAdmin,
            final List<ProfileServices> loggedUserServices,
            final List<ProfileServices> loggedUserServicesAddedBySystem,
            final HttpServletResponse response,
            final String column,
            final ISecurityUser loggedUser,
            final String password
    ) throws FileNotFoundException {
        return generateFilePdfAndDownload(downloadDto, isAdmin, loggedUserServices, loggedUserServicesAddedBySystem, response, column, loggedUser, false, password);
    }

    /**
     * Devuelve TRUE en caso de que las banderas para generar PDF e IMÁGENES estén encendidas.
     *
     * @return
     */
    private boolean isPdfImagesEnabled(IPdfConversionEnabled fileData) {
        return fileData != null && Boolean.TRUE.equals(fileData.getPdfImagesEnabled()) && Boolean.TRUE.equals(fileData.getPdfConversionEnabled());
    }

    public PdfConversionDTO generateFilePdfAndDownload(
            final DownloadDto downloadDto,
            final boolean isAdmin,
            final List<ProfileServices> loggedUserServices,
            final List<ProfileServices> loggedUserServicesAddedBySystem,
            final HttpServletResponse response,
            final String column,
            final ISecurityUser user,
            final boolean skipGeneratePDf,
            final String password
    ) throws FileNotFoundException{
        final PdfConversionDTO conversion = new PdfConversionDTO();
        if (downloadDto.getDownloadAttempt() > config.getViewerSettings().getDownloadAttemptsLimit()) {
            conversion.setMessage(FileUtils.NO_FILE);
            return conversion;
        } else {
            downloadDto.setDownloadAttempt(downloadDto.getDownloadAttempt() + 1);
        }
        if (downloadDto.getFileId() == null) {
            conversion.setMessage(FileUtils.ERROR);
            return conversion;
        }
        if (downloadDto.getFileId() == -1L) {
            conversion.setMessage(FileUtils.READY);
            return conversion;
        }
        try {
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            final FileRef file = dao.HQLT_findById(FileRef.class, downloadDto.getFileId());
            if (file == null) {
                final Map<String, Object> tags = new HashMap<>(2);
                tags.put("fileId", downloadDto.getFileId());
                final String message = localeManager.getTag("fileNotFound", tags);
                throw new FileNotFoundException(message);
            }
            final IFileData metadata = newMetadataInstance(file);
            conversion.setContentType(metadata.getContentType());
            conversion.setPdfSha512(metadata.getPdfSha512());
            conversion.setHasPassword(metadata.getHasPassword());
            if (!downloadDto.getEnablePdfViewer() || skipGeneratePDf || !this.isPdfImagesEnabled(file)) {
                if (downloadDto.getEnableFileConfirmation() && !Objects.equals(downloadDto.getOriginalForce(), 1)) {
                    conversion.setMessage(FileUtils.FILE_CONFIRMATION);
                    return conversion;
                } else {
                    conversion.setMessage(FileUtils.STARTED);
                    writeDownloadHeaders(downloadDto, response, file);
                    writeFileToResponse(metadata, column, response, user);
                    return conversion;
                }
            } else if (!Objects.equals(downloadDto.getOriginalForce(), 1) && VideoCompatibilityHTML5.isVideoCompatibility(metadata.getContentType())) {
                conversion.setMessage(VideoCompatibilityHTML5.IS_HTML5_VIDEO);
                if (downloadDto.getIdMode().equals(1)) {
                    writeDownloadHeaders(downloadDto, response, file);
                    writeFileToResponse(metadata, column, response, user);
                }
                return conversion;
            }
            final IPdfManager pdfManager = config.getPdfManager();
            final PdfGeneratorSetup setup = pdfManager.getConversionSetup(metadata);
            if (downloadDto.getEnableFileConfirmation() && !Objects.equals(downloadDto.getOriginalForce(), 1)) {
                if (!FileUtils.COLUMN_FILES_PDF.equals(metadata.getExtension()) && !setup.isSupported()) {
                    conversion.setMessage(FileUtils.FILE_CONFIRMATION);
                    return conversion;
                }
            }
            if (downloadDto.getIdMode().equals(1)) {
                conversion.setMessage(FileUtils.STARTED);
                writeDownloadHeaders(downloadDto, response, file);
                writeFileToResponse(metadata, column, response, user);
                return conversion;
            } else if (downloadDto.getOriginalForce().equals(0)) {
                LOGGER.trace(">> originalForce : {}", downloadDto.getOriginalForce());
                final boolean pdfConverting = isConvertingToPdf(metadata);
                if (pdfConverting) {
                    conversion.setMessage(FileUtils.NO_FILE);
                    conversion.setConverting(true);
                    return conversion;
                }
                final boolean pdfPageGenerating = isPdfPageGenerating(metadata.getId());
                if (pdfPageGenerating) {
                    return getPdfGenerationInfo(conversion, metadata);
                }
                if (metadata.getHasPdf().equals(0)
                        && !cache.existsFilePdf(metadata)) {
                    LOGGER.trace(">> pdfExists : {}", true);
                    if (setup.isSupported()) {
                        final boolean converting = pdfManager.isConverting(setup.getProgram(), metadata);
                        if (converting) {
                            conversion.setMessage(FileUtils.NO_FILE);
                            conversion.setConverting(true);
                            return conversion;
                        }
                        return pdfManager.generatePdf(setup, user);
                    } else {
                        conversion.setMessage(FileUtils.STARTED);
                        writeDownloadHeaders(downloadDto, response, file);
                        writeFileToResponse(metadata, column, response, user);
                        return conversion;
                    }
                } else if (metadata.getHasPdf().equals(1) && (metadata.getHasPdfPages().equals(0) || (metadata.getHasPassword() && password != null))) {
                    startPdfPageGeneration(metadata, password);
                    return getPdfGenerationInfo(conversion, metadata);
                } else {
                    LOGGER.trace(">> pdfExists : {}", false);
                }
                if (downloadDto.getPrepareDownload()) {
                    conversion.setMessage(FileUtils.READY);
                    return conversion;
                }
                if (downloadDto.getDocumentId() != null && downloadDto.getDocumentId() != 0
                        && (isAdmin
                        || loggedUserServices.contains(ProfileServices.DOCUMENTO_ENCARGADO)
                        || (loggedUserServices.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT) && !loggedUserServicesAddedBySystem.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT))
                        || loggedUserServices.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT))) {
                    downloadDto.setAttendant(true);
                }
                conversion.setMessage(FileUtils.READY);
                conversion.setHasPdfPages(metadata.getHasPdfPages());
                conversion.setNumberPages(getPdfNumberPages(metadata));
                conversion.setPagesGenerated(getPdfPageGenerated(metadata));
                return conversion;
            } else {
                conversion.setMessage(FileUtils.STARTED);
                writeDownloadHeaders(downloadDto, response, file);
                writeFileToResponse(metadata, column, response, user);
            }  
        } catch(final PdfExecutorNotAvailableException ex) {
            LOGGER.error("Failed while intialzing PDF Executor for document {}. {}", downloadDto.getDocumentId(), ex);
            conversion.setMessage(FileManager.PDF_GENERATOR_NOT_AVAILABLE);
            return conversion;
        } catch (final ClientAbortException | IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            LOGGER.error("Download execute() ... {}", new Object[]{
                downloadDto.getDocumentId(), downloadDto.getOriginalForce(), downloadDto.isAttendant()
            }, ex);

            conversion.setMessage(FileUtils.ERROR);
            return conversion;
        }
        conversion.setMessage(FileUtils.STARTED);
        return conversion;
    }

    public void generatePageThumbnail(
            final FilePdfImage page,
            final Path pdfPage,
            final IFilesDAO dao
    ) throws IOException, InterruptedException {
        if (pdfPage == null) {
            return;
        }
        final BufferedImage thumbnail = generateThumbnailAsBufferedImage(
                page.getFilePDF().getId(),
                page.getPage(),
                pdfPage
        );
        if (thumbnail == null) {
            return;
        }
        final PdfPageThumbnailManager thumbnailManager = new PdfPageThumbnailManager();
        thumbnailManager.persistPageThumbnail(page, thumbnail, dao);
    }
    
    public void generatePageThumbnail(
            final FilePdfImage page,
            final PdfPageResult pdfPage,
            final IFilesDAO dao
    ) throws IOException, InterruptedException {
        if (page == null || !Boolean.TRUE.equals(page.getFilePDF().getPdfImagesEnabled())) {
            return;
        }
        final BufferedImage thumbnail = generateThumbnailAsBufferedImage(
                page.getFilePDF().getId(),
                page.getPage(),
                pdfPage.getImage()
        );
        final PdfPageThumbnailManager thumbnailManager = new PdfPageThumbnailManager();
        thumbnailManager.persistPageThumbnail(page, thumbnail, dao);
    }

    private void generateThumbnail(
            final IFileData metadata, 
            final Path file
    ) throws IOException, SQLException {
        if (cache.existsFileThumbnail(metadata)) {
            return;
        }
        Path thumbnail = generateThumbnailToFile(metadata.getId(), 0, file);
        if (thumbnail == null) {
            return;
        }
        try (final TempPath thumbnailFile = new TempPath(thumbnail)) {
            persistFileThumbnail(metadata, thumbnailFile.getPath());
        }
    }

    private BufferedImage generateThumbnailAsBufferedImage(final Long fileId, final Integer pageNumber, final Path source) throws IOException {
        if (source == null) {
            return null;
        }
        final BufferedImage pageImage = ImageIO.read(source.toFile());
        return generateThumbnailAsBufferedImage(fileId, pageNumber, pageImage);
    }

    private BufferedImage generateThumbnailAsBufferedImage(final Long fileId, final Integer pageNumber, final BufferedImage source) throws IOException {
        if (source.getWidth() == 0) {
            final Map<String, Object> tags = new HashMap<>(2);
            tags.put("fileId", fileId);
            tags.put("pageNumber", pageNumber);
            final String message = localeManager.getTag("invalidPdfPageWidth", tags);
            throw new InvalidPdfGeneration(message);
        }
        if (source.getHeight() == 0) {
            final Map<String, Object> tags = new HashMap<>(2);
            tags.put("fileId", fileId);
            tags.put("pageNumber", pageNumber);
            final String message = localeManager.getTag("invalidPdfPageHeight", tags);
            throw new InvalidPdfGeneration(message);
        }
        final Double width = Double.valueOf(source.getWidth());
        final Double height = (source.getHeight() / width) * THUMBNAIL_SIZE;
        final BufferedImage resizedImage = Thumbnails.of(source)
                .size(THUMBNAIL_SIZE, height.intValue())
                .outputFormat(FileUtils.PNG_IMAGE_EXTENSION)
                .asBufferedImage();
        return resizedImage;
    }

    private Path generateThumbnailToFile(final Long fileId, final Integer pageNumber, final Path source) throws IOException {
        final BufferedImage pageImage = ImageIO.read(source.toFile());
        BufferedImage resizedImage;
        if (pageImage == null) {
            LOGGER.warn("Thumbnail wasn't generated because there's no registered ImageReader to claim to be able to read the stream.");
            return null;
        } else {
            resizedImage = generateThumbnailAsBufferedImage(fileId, pageNumber, pageImage);
        }
        final Path thumbnailFile = FileHandler.createTempFile(THUMBNAIL_PREFFIX, tempFolder);
        try (final OutputStream output = Files.newOutputStream(thumbnailFile)) {
            ImageIO.write(resizedImage, FileUtils.PNG_IMAGE_EXTENSION, output);
        }
        return thumbnailFile;
    }

    private void generateVolatilePage(final Long fileId, final Integer pageNumber) {
        final IFileData metadata = newMetadataInstance(Utilities.getUntypedDAO().HQLT_findById(FileRef.class, fileId));
        final PdfPageVolatileFactory pageFactory = new PdfPageVolatileFactory(metadata, pageNumber, config);
        final PdfPageVolatileExecutor executor = BnextDaemonUtil.getRunningInstance(PdfPageVolatileExecutor.class);
        executor.generate(pageFactory);
    }
    

    @Override
    public byte[] getFileContent(final Long fileId, final ISecurityUser user) {
        final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
        final FileRef file = dao.HQLT_findById(FileRef.class, fileId);
        return getFileContent(file, false, user);
    }
    
    public byte[] getFileContent(final FileRef file, final Boolean skipLog, final ISecurityUser user) {
        try (final TempPath tempPath = new TempPath(FileHandler.createTempFile(FILE_CONTENT_PREFFIX, tempFolder))) {
            cache.writeFileContentToFile(file, tempPath.getPath());
            final IFilesDAO filesDao = Utilities.getBean(IFilesDAO.class);
            filesDao.insertFileLogging(file.getId(), FileLoggingType.DOWNLOAD_CONTENT, skipLog, user);
            return org.apache.commons.io.FileUtils.readFileToByteArray(tempPath.getPath().toFile());
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            throw new RuntimeException(ex);
        }
    }


    @Override
    public Path getFilePdf(final IFileData file, final ISecurityUser user) {
        return getFilePdf(file, false, user);
    }
    
    public Path getFilePdf(final IFileData file, final Boolean skipUser, final ISecurityUser user) {
        try {
            final Path content = FileHandler.createTempFile(FILE_PDF_PREFFIX, tempFolder);
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            // Las imagenes son convertidas a páginas de READ_PDF en el metodo PdfPageFactory.executeGeneration(...)
            if (FileUtils.isCommonImage(file.getExtension()) || FileUtils.CONTENT_TYPE_PDF.equals(file.getContentType())) {
                cache.writeFileContentToFile(file, content);
                dao.insertFileLogging(file.getId(), FileLoggingType.DOWNLOAD_CONTENT, skipUser, user);
            } else {
                cache.writeFilePdfToFile(file, content);
                dao.insertFileLogging(file.getId(), FileLoggingType.DOWNLOAD_PDF, skipUser, user);
            }
            return content;
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            throw new RuntimeException(ex);
        }
    }

    @Override
    public Path getFilePdf(final Long fileId, final ISecurityUser user) {
        return getFilePdf(fileId, false, user);
    }
    
    public Path getFilePdf(final Long fileId, final Boolean skipLog, final ISecurityUser user) {
        try {
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            final FileRef file = dao.HQLT_findById(FileRef.class, fileId);
            final Path content = FileHandler.createTempFile(FILE_PDF_PREFFIX, tempFolder);
            // Las imagenes son convertidas a páginas de READ_PDF en el metodo PdfPageFactory.executeGeneration(...)
            if (FileUtils.isCommonImage(file.getExtension()) || FileUtils.CONTENT_TYPE_PDF.equals(file.getContentType())) {
                cache.writeFileContentToFile(file, content);
                dao.insertFileLogging(file.getId(), FileLoggingType.DOWNLOAD_CONTENT, skipLog, user);
            } else {
                cache.writeFilePdfToFile(file, content);
                dao.insertFileLogging(file.getId(), FileLoggingType.DOWNLOAD_PDF, skipLog, user);
            }
            return content;
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            throw new RuntimeException(ex);
        }
    }

    public Path getFilePdfPage(final Long pdfPageId)
            throws IOException, SQLException {
        final Path content = FileHandler.createTempFile(
                FILE_PDF_PAGE_PREFFIX,
                FileUtils.BNEXT_SUFFIX,
                tempFolder
        );
        cache.writeFilePdfPageToFile(pdfPageId, content);
        return content;
    }

    @Override
    public IFileRepository getFileRepository() {
        return Utilities.getBean(IFilesDAO.class);
    }

    private PdfConversionDTO getPdfGenerationInfo(final PdfConversionDTO conversion, final IFileData metadata) {
        conversion.setGeneratingPages(true);
        conversion.setHasPdfPages(0);
        conversion.setNumberPages(getPdfNumberPages(metadata));
        conversion.setPagesGenerated(getPdfPageGenerated(metadata));
        conversion.setMessage(FileUtils.NO_FILE);
        conversion.setPdfSha512(metadata.getPdfSha512());
        conversion.setConverting(false);
        return conversion;
    }

    private Integer getPdfNumberPages(final IFileData metadata) {
        if (metadata.getNumberPages() != null && metadata.getNumberPages() > 0) {
            return metadata.getNumberPages();
        }
        final PdfPageExecutor pdfPageExecutor = BnextDaemonUtil.getRunningInstance(PdfPageExecutor.class);
        return pdfPageExecutor.getNumberPages(metadata.getId());
    }

    private Integer getPdfPageGenerated(final IFileData metadata) {
        if (metadata.getHasPdfPages() != null && metadata.getHasPdfPages().equals(1)) {
            return metadata.getNumberPages();
        }
        final PdfPageExecutor pdfPageExecutor = BnextDaemonUtil.getRunningInstance(PdfPageExecutor.class);
        return pdfPageExecutor.getGeneratedPagesNumber(metadata.getId());
    }

    private PdfViewerInfo getPdfViewerReadyResponse(final IFileData metadata, final PdfViewerInfo result) {
        result.setMessage(FileUtils.STARTED);
        result.setNumberPages(getPdfNumberPages(metadata));
        result.setPagesGenerated(getPdfPageGenerated(metadata));
        result.setStatus(PdfViewerStatus.THUMBNAIL_GENERATION_SUCCESS);
        return result;
    }

    private RotationId getRotationId(final Long pdfPageId, final Integer rotationDegrees, final CacheType type) {
        final PdfPageId pageId = new PdfPageId(pdfPageId);
        final RotationDegrees degrees = RotationDegrees.parseValue(rotationDegrees);
        return new RotationId(pageId, type, degrees);
    }

    private RotationId getRotationId(final Long fileId, final Integer pageNumber, final Integer rotationDegrees, final CacheType type) {
        final PdfPageId pageId = new PdfPageId(fileId, pageNumber);
        final RotationDegrees degrees = RotationDegrees.parseValue(rotationDegrees);
        return new RotationId(pageId, type, degrees);
    }

    @Override
    public Path getRotationSource(final RotationId id) throws IOException, FileNotFoundException, SQLException {
        final Path source = FileHandler.createTempFile(ROTATION_SOURCE_PREFFIX, tempFolder);
        cache.writeRotatedImageSourceToFile(id, source);
        if (!FileUtils.exists(source) || FileUtils.getSize(source) == 0) {
            throw new RotationImageException("Failed to write rotation source to for rotation " + id.getIdentifier());
        }
        return source;
    }

    @Override
    public Path getTempFolder() {
        return tempFolder;
    }

    public Boolean isConvertingToPdf(final Long fileId, final String fileExtension, final String contentType) {
        final FileData data = new FileData();
        data.setId(fileId);
        data.setContentType(contentType);
        data.setExtension(fileExtension);
        return isConvertingToPdf(data);
    }

    public Boolean isConvertingToPdf(final IFileData data) {
        final IPdfManager pdfManager = config.getPdfManager();
        final PdfGeneratorSetup setup = pdfManager.getConversionSetup(data);
        if (setup.isSupported()) {
            return pdfManager.isConverting(setup.getProgram(), data);
        }
        return false;
    }

    private boolean isPageVolatileGenerating(final Long fileId, final Integer pageNumber) {
        final PdfPageVolatileExecutor imageExecutor = BnextDaemonUtil.getRunningInstance(PdfPageVolatileExecutor.class);
        return imageExecutor.inProcess(fileId, pageNumber);
    }

    private boolean isPdfPageGenerating(final Long fileId) {
        final PdfPageExecutor imageExecutor = BnextDaemonUtil.getRunningInstance(PdfPageExecutor.class);
        if (imageExecutor == null) {
            return false;
        }
        return imageExecutor.inProcessByFileId(fileId);
    }

    public Boolean isPdfPageGenerating(final Long fileId, final Long pdfPageId) {
        final PdfPageExecutor pdfPageExecutor = BnextDaemonUtil.getRunningInstance(PdfPageExecutor.class);
        return pdfPageExecutor.inProcessByPdfImageId(fileId, pdfPageId);
    }

    private boolean isRotatedImageGenerating(final RotationId id) {
        final RotationImageExecutor executor = BnextDaemonUtil.getRunningInstance(RotationImageExecutor.class);
        return executor.inProcess(id);
    }

    public FileData newMetadataInstance(final IFileData source) {
        final FileData target = new FileData();
        target.setId(source.getId());
        target.setBusy(source.getBusy());
        target.setContentType(source.getContentType());
        target.setDescription(source.getDescription());
        target.setCode(source.getCode());
        target.setExtension(source.getExtension());
        target.setNumberPages(source.getNumberPages());
        target.setContentSize(source.getContentSize());
        target.setPdfSize(source.getPdfSize());
        target.setThumbnailSize(source.getThumbnailSize());
        target.setContentSha512(source.getContentSha512());
        target.setPdfSha512(source.getPdfSha512());
        target.setThumbnailSha512(source.getThumbnailSha512());
        target.setHasPdf(source.getHasPdf());
        target.setHasPdfPages(source.getHasPdfPages());
        target.setNumberSavedPages(source.getNumberSavedPages());
        Boolean hasHasPassword = source.getHasPassword();
        if (hasHasPassword == null) {
            hasHasPassword = false;
        }
        target.setHasPassword(hasHasPassword);
        return target;
    }

    private PdfViewerInfo notSupportedPdfViewer() {
        final PdfViewerInfo result = new PdfViewerInfo();
        result.setStatus(PdfViewerStatus.NOT_SUPPORTED_THUMBNAIL);
        return result;
    }

    @Override
    public IFileData persistFile(
            final Path file,
            final String fileName,
            final String contentType,
            final boolean filePdfPagesEnabled,
            final ISecurityUser loggedUser
    ) throws IOException, SQLException {
        return persistFile(-1L, file, fileName, contentType, filePdfPagesEnabled, loggedUser);
    }
           
    private IFileData persistFile(
            final Long fileId,
            final Path file,
            final String fileName,
            final String contentType,
            final boolean filePdfPagesEnabled,
            final ISecurityUser user
    ) throws IOException, SQLException {
        final String ext = FileUtils.getExtension(fileName);
        final IFileData savedFile;
        if (Objects.equals(fileId, -1L)) {
            final String name = FileUtils.getFileName(fileName);
            final FilesLite fileData = new FilesLite(fileId, name);
            fileData.setPdfImagesEnabled(filePdfPagesEnabled);
            fileData.setPdfConversionEnabled(filePdfPagesEnabled);
            fileData.setContentType(FileUtils.validContentType(file, contentType));
            fileData.setExtension(ext);
            final int hasPdf;
            // Las imagenes son convertidas a páginas de READ_PDF en el metodo PdfPageFactory.executeGeneration(...)
            if (FileUtils.isCommonImage(ext) || FileUtils.CONTENT_TYPE_PDF.equals(contentType)) {
                hasPdf = 1;
            } else {
                hasPdf = 0;
            }
            fileData.setHasPdf(hasPdf);
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            savedFile = dao.makePersistent(fileData, user.getId());
            savedFile.setCode(dao.getFileCode(fileData.getId()));
        } else {
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            savedFile = dao.HQLT_findById(FilesLite.class, fileId);
        }
        if (FileUtils.isSupportedImage(ext)) {
            generateThumbnail(savedFile, file);
        }
        savedFile.setContentSize(Files.size(file));
        savedFile.setContentSha512(CryptoUtils.sha512(file));
        cache.persistFileContent(savedFile, file);
        return savedFile;
    }

    public UploadFileResult persistFileContent(
            final Long fileId,
            final Path file,
            final String fileName,
            final String contentType,
            final boolean filePdfPagesEnabled,
            final ISecurityUser loggedUser
    ) {
        if (file == null) {
            throw new RuntimeException("Fail while persisting null file.");
        }
        if (!Files.isReadable(file)) {
            throw new RuntimeException("Fail while persisting not readable file.");
        }
        try {
            final IFileData savedFile = persistFile(fileId, file, fileName, contentType, filePdfPagesEnabled, loggedUser);
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setOperationEstatus(1);
            gsh.setSavedId(savedFile.getId().toString());
            gsh.setJsonEntityData(Utilities.entityToMap(savedFile));
            return new UploadFileResult(gsh, savedFile);
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            final UploadFileResult uploadResult = dao.parseUploadException(fileName, file, ex);
            LOGGER.error("Error uploading file {}{}", uploadResult.getGsh().getErrorMessage(), ex);
            return uploadResult;
        }
    }

    @Override
    public void persistFilePdf(final IFileData data, final Path pdf, final ISecurityUser user)
            throws IOException, SQLException {
        if (!IDocumentTypeDAO.isPdfImagesEnabled(data)) {
            return;
        }
        data.setPdfSize(Files.size(pdf));
        cache.persistFilePdf(data, pdf);
        data.setHasPdf(1);
        startPdfPageGeneration(data);
    }

    private void persistFileThumbnail(final IFileData metadata, final Path file)
            throws IOException {
        metadata.setThumbnailSize(Files.size(file));
        cache.persistFileThumbnail(metadata, file);
    }

    @Override
    public void persistImageRotated(final RotationId id, final Path image) throws IOException {
        cache.persistImageRotated(id, image);
    }

    /**
     * Persist a volatile generated READ_PDF page
     *
     * @param fileId Id of file id
     * @param pageNumber Page number
     * @param page Content to persist
     * @throws java.io.IOException If the image fails
     */
    @Override
    public void persistPdfPageVolatile(final Long fileId, final Integer pageNumber, final Path page) throws IOException {
        cache.persistPageVolatile(fileId, pageNumber, page);
        Path thumbnail = generateThumbnailToFile(fileId, pageNumber, page);
        if (thumbnail == null) {
            return;
        }
        try (final TempPath thumbnailFile = new TempPath(thumbnail)) {
            cache.persistPageThumbnailVolatile(fileId, pageNumber, thumbnailFile.getPath());
        }
    }

    public PdfViewerInfo prepareFileForPdfViewer(
            final IFileData metadata,
            final ISecurityUser loggedUser
    ) {
        final Boolean isPdf = FileUtils.CONTENT_TYPE_PDF.equals(metadata.getContentType());
        // Las imagenes son convertidas a páginas de READ_PDF en el metodo PdfPageFactory.executeGeneration(...)
        if (FileUtils.isCommonImage(metadata.getExtension())) {
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            dao.updateHasPdf(metadata.getId());
            return preparePdfForPdfViewer(metadata);
        } else if (isPdf) {
            return preparePdfForPdfViewer(metadata);
        }
        final IPdfManager pdfManager = config.getPdfManager();
        try {
            final PdfGeneratorSetup setup = pdfManager.getConversionSetup(metadata);
            if (!setup.isSupported()) {
                return notSupportedPdfViewer();
            }
            return startPdfGeneration(metadata, setup, loggedUser);
        } catch (final PdfExecutorNotAvailableException ex) {
            final PdfViewerInfo result = new PdfViewerInfo();
            LOGGER.error("Failed while intialzing PDF Executor preparing file {} for pdf viewer.", metadata, ex);
            result.setStatus(PdfViewerStatus.PDF_CONVERSION_NOT_AVAILABLE);
            return result;
        } catch (final Exception ex) {
            final PdfViewerInfo result = new PdfViewerInfo();
            LOGGER.error("Failed while preparing file {} for pdf viewer.", metadata, ex);
            result.setStatus(PdfViewerStatus.PDF_CONVERSION_FAILED);
            result.setMessage(FileUtils.ERROR);
            return result;
        }
    }
    
    private PdfViewerInfo preparePdfForPdfViewer(final IFileData metadata) {
        final PdfViewerInfo result = new PdfViewerInfo();
        try {
            if (isPdfPageGenerating(metadata.getId())) {
                if (cache.existsFileThumbnail(metadata)) {
                    return getPdfViewerReadyResponse(metadata, result);
                }
                result.setNumberPages(getPdfNumberPages(metadata));
                result.setPagesGenerated(getPdfPageGenerated(metadata));
                result.setStatus(PdfViewerStatus.PAGE_GENERATION_STARTED);
                return result;
            }
            if (metadata.getHasPdfPages() == null || metadata.getHasPdfPages().equals(0)) {
                startPdfPageGeneration(metadata);
                result.setStatus(PdfViewerStatus.PAGE_GENERATION_STARTED);
                return result;
            }
            return getPdfViewerReadyResponse(metadata, result);
        } catch (final Exception ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            LOGGER.error("Failed while generating prepare file for PdfViewer of pdf {}", metadata, ex);
            result.setMessage(FileUtils.ERROR);
        }
        return result;
    }

    private void rotateImage(final RotationId id) {
        final RotationImageExecutor executor = BnextDaemonUtil.getRunningInstance(RotationImageExecutor.class);
        executor.rotate(new RotationImageThread(id, config));
    }

    private void send500Error(final HttpServletResponse response, final String message) {
        try {
            response.sendError(500, message);
        } catch (final IOException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            LOGGER.error("Failed to send 500 error", ex);
        }
    }

    private void send503Error(final HttpServletResponse response, final String message) {
        try {
            //TODO: Convetir error 503 a un estado que no represente error, para fines de consulta de errores en Glowroot
            response.sendError(503, message);
        } catch (final IOException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            LOGGER.error("Failed to send 503 error", ex);
        }
    }

    @Override
    public void sendFeedbackError(final String title, final Exception exception, final ISecurityUser loggedUser) {
        final FeedbackMailer mailer = new FeedbackMailer(Utilities.getUntypedDAO());
        mailer.send(title, exception.toString(), loggedUser);
    }

    private PdfViewerInfo startPdfGeneration(
            final IFileData metadata,
            final PdfGeneratorSetup setup,
            final ISecurityUser loggedUser
    ) {
        final PdfViewerInfo result = new PdfViewerInfo();
        final IPdfManager pdfManager = config.getPdfManager();
        try {
            final PdfViewerStatus generated = verifyPdfGeneration(metadata);
            switch (generated) {
                case PDF_CONVERSION_NOT_STARTED:
                    final PdfConversionDTO conversion = pdfManager.generatePdf(setup, loggedUser);
                    final boolean started = conversion.isStarted();
                    if (!started) {
                        result.setMessage(conversion.getMessage());
                        result.setQueueSize(conversion.getQueueSize());
                        if (conversion.isConverting()) {
                            result.setStatus(PdfViewerStatus.PDF_CONVERSION_STARTED);
                        } else {
                            result.setStatus(PdfViewerStatus.PDF_CONVERSION_FAILED);
                        }
                        return result;
                    }
                    result.setStatus(PdfViewerStatus.PDF_CONVERSION_STARTED);
                    return result;
                case PDF_CONVERSION_STARTED:
                    result.setMessage(FileUtils.NO_FILE);
                    final Integer queueSize = pdfManager.getQueueSize(setup.getProgram(), metadata);
                    result.setQueueSize(queueSize);
                    if (pdfManager.isConverting(setup.getProgram(), metadata)) {
                        result.setStatus(PdfViewerStatus.PDF_CONVERSION_STARTED);
                    } else if (isPdfPageGenerating(metadata.getId())) {
                        result.setStatus(PdfViewerStatus.PAGE_GENERATION_STARTED);
                    }
                    return result;
                case PDF_CONVERSION_SUCCESS:
                    return preparePdfForPdfViewer(metadata);
            }
            result.setStatus(PdfViewerStatus.THUMBNAIL_GENERATION_FAILED);
            return result;
        } catch (IOException | InterruptedException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            LOGGER.error("Failed while generating starting pdf generation for file {}", metadata, ex);
            result.setMessage(FileUtils.ERROR);
            return result;
        }
    }

    /**
     * Overloaded implementation of startPdfPageGeneration doesn't support password protected files
     * @param metadata
     */
    private void startPdfPageGeneration(final IFileData metadata) {
        startPdfPageGeneration(metadata, null);
    }

    private void startPdfPageGeneration(final IFileData metadata, String password) {
        if (!IDocumentTypeDAO.isPdfImagesEnabled(metadata) || metadata.getHasPdf().equals(0) || (metadata.getHasPdfPages().equals(1) && password == null && !metadata.getHasPassword())) {
            return;
        }
        final PdfPageExecutor pdfPageExecutor = BnextDaemonUtil.getRunningInstance(PdfPageExecutor.class);
        if (!isPdfPageGenerating(metadata.getId())) {
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            dao.lockFile(metadata.getId());
            pdfPageExecutor.generate(new PdfPageFactory(metadata, config, password));
        }
    }

    private PdfViewerStatus verifyPdfGeneration(final IFileData metadata)
            throws InterruptedException, IOException {
        final boolean pdfConverting = isConvertingToPdf(metadata);
        if (pdfConverting) {
            return PdfViewerStatus.PDF_CONVERSION_STARTED;
        }
        final boolean pdfExists = cache.existsFilePdf(metadata);
        if (pdfExists) {
            return PdfViewerStatus.PDF_CONVERSION_SUCCESS;
        }
        return PdfViewerStatus.PDF_CONVERSION_NOT_STARTED;
    }

    private void writeDownloadHeaders(
            final DownloadDto downloadDto,
            final HttpServletResponse response,
            final FileRef file
    ) {
        if (downloadDto.getPrepareDownload()) {
            return;
        }
        final String contentType = file.getContentType();
        final Long contentSize = file.getContentSize();
        final String fileName = file.getDescription();
        writeHeaders(response, fileName, contentType, contentSize, false);
    }

    @Override
    public void writeFileContentToFile(final IFileData data, final Path content, final ISecurityUser user)
            throws IOException, SQLException {
        writeFileContentToFile(data, false, content, user);
    }

    public void writeFileContentToFile(final IFileData data, final Boolean skipLog, final Path content, final ISecurityUser user)
            throws IOException, SQLException {
        cache.writeFileContentToFile(data, content);
        final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
        dao.insertFileLogging(data.getId(), FileLoggingType.DOWNLOAD_CONTENT, skipLog, user);
    }

    @Override
    public void writeFileContentToOutput(final IFileData data, final OutputStream output, final ISecurityUser user)
            throws IOException, SQLException {
        writeFileContentToOutput(data, false, output, user);
    }
    
    public void writeFileContentToOutput(
            final IFileData data, 
            final Boolean skipLog,
            final OutputStream output, 
            final ISecurityUser user
    ) throws IOException, SQLException {
        cache.writeFileContentToOutput(data, output);
        final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
        dao.insertFileLogging(data.getId(), FileLoggingType.DOWNLOAD_CONTENT, skipLog, user);
    }

    public String writeFileThumbnailToResponse(
            final Long fileId,
            final HttpServletResponse response,
            final ISecurityUser loggedUser
    ) throws IOException {
        final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
        final FileRef fileRef = dao.HQLT_findById(FileRef.class, fileId);
        final IFileData metadata = newMetadataInstance(fileRef);
        try (final OutputStream output = response.getOutputStream()) {
            if (cache.existsFileThumbnail(metadata)) {
                final String imageName = fileId + "-preview." + FileUtils.PNG_IMAGE_EXTENSION;
                writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, metadata.getThumbnailSize(), true);
                cache.writeFileThumbnailToOutput(metadata, output);
                return null;
            } else {
                final String message = "File thumbnail does not exists for file " + metadata.getCode() + ".";
                LOGGER.error(message);
                send500Error(response, message);
            }
        } catch (final Exception ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            final String message = "Failed while generating thumbnail for file " + metadata.getCode();
            LOGGER.error(message, ex);
            send500Error(response, message);
        }
        return null;
    }

    private void writeFileToResponse(
            final IFileData metadata,
            final String column,
            final HttpServletResponse response,
            final ISecurityUser user
    ) throws IOException, SQLException {
        try (final ServletOutputStream output = response.getOutputStream()) {
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            if (FileUtils.COLUMN_FILES_PDF.equals(column)) {
                cache.writeFilePdfToOutput(metadata, output);
                dao.insertFileLogging(metadata.getId(), FileLoggingType.DOWNLOAD_PDF, false, user);
            } else {
                cache.writeFileContentToOutput(metadata, output);
                dao.insertFileLogging(metadata.getId(), FileLoggingType.DOWNLOAD_CONTENT, false, user);
            }
        }
    }
    
    @Override
    public void writeHeaders(
            final HttpServletResponse response,
            final String fileName,
            final String contentType,
            final Long contentSize,
            final Boolean enableCache
    ) {
        writeHeaders(response, fileName, contentType, contentSize, enableCache, 365, TimeUnit.DAYS);
    }
    
    @Override
    public void writeHeadersCacheShort(
            final HttpServletResponse response,
            final String fileName,
            final String contentType,
            final Long contentSize
    ) {     
        writeHeaders(response, fileName, contentType, contentSize, true, 5, TimeUnit.MINUTES);
    }
    
    @Override
    public void writeHeadersWithDailyCache(
            final HttpServletResponse response,
            final String fileName,
            final String contentType,
            final Long contentSize
    ) {
        writeHeaders(response, fileName, contentType, contentSize, true, 1, TimeUnit.DAYS);
    }

    @Override
    public void writeHeaders(
            final HttpServletResponse response,
            final String fileName,
            final String contentType,
            final Long contentSize,
            final Boolean enableCache,
            final Integer cacheLifeValue,
            final TimeUnit cacheLifeUnit
    ) {
        response.setContentType(contentType);
        final ContentDisposition contentHeader = ContentDisposition
                .builder("attachment")
                .filename(fileName)
                .build();
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, contentHeader.toString());
        if (contentSize != null && contentSize > 0) {
            response.setHeader("Content-Length", contentSize.toString());
        }
        if (enableCache) {
            final CacheControl cacheControl = CacheControl.maxAge(cacheLifeValue, cacheLifeUnit)
                .noTransform()
                .mustRevalidate();
            response.setHeader(HttpHeaders.CACHE_CONTROL, cacheControl.getHeaderValue());
        }
    }

    public void writePageThumbnailToResponse(
            final Long pdfPageId,
            final Long fileId,
            final Integer rotationDegrees,
            final HttpServletResponse response,
            final Long loggedUser
    ) throws IOException, InterruptedException, SQLException {
        final String imageName = pdfPageId + "-preview." + FileUtils.PNG_IMAGE_EXTENSION;
        if (isPdfPageGenerating(fileId, pdfPageId)) {
            final String message = "Trying to write page thumbnail " + pdfPageId + " to response while pdf pages area generating for file " + fileId + ".";
            send503Error(response, message);
        } else if (cache.existsPdfPageThumbnail(pdfPageId)) {
            if (RotationDegrees.NONE.getAngle().equals(rotationDegrees)) {
                writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
                cache.writePdfPageThumbnailToResponse(pdfPageId, response);
            } else {
                writeRotatedPageThumbnailToResponse(pdfPageId, rotationDegrees, response);
            }
        } else {
            final String message = "Failed to generate page thumbnail " + pdfPageId + " for file " + fileId + ".";
            send503Error(response, message);
        }
    }

    public void writePageThumbnailToResponse(
            final Long fileId,
            final Integer pageNumber,
            final Integer rotationDegrees,
            final HttpServletResponse response,
            final Long loggedUser
    ) throws IOException, InterruptedException, SQLException {
        if (isPageVolatileGenerating(fileId, pageNumber)) {
            final String message = "Thumbnail of page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        } else if (cache.existsPageVolatile(fileId, pageNumber)
                && cache.existsPageThumbnailVolatile(fileId, pageNumber)) {
            if (RotationDegrees.NONE.getAngle().equals(rotationDegrees)) {
                final String imageName = fileId + "-" + pageNumber + "." + FileUtils.PNG_IMAGE_EXTENSION;
                writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
                cache.writePageThumbnailVolatileToResponse(fileId, pageNumber, response);
            } else {
                writeRotatedPageThumbnailToResponse(fileId, pageNumber, rotationDegrees, response);
            }
        } else {
            generateVolatilePage(fileId, pageNumber);
            final String message = "Thumbnail of page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        }
    }

    public void writePageToResponse(
            final Long fileId,
            final Integer pageNumber,
            final Integer rotationDegrees,
            final HttpServletResponse response,
            final Long loggedUser
    ) throws IOException, InterruptedException, SQLException {
        if (null != fileId && fileId > 0 && null != pageNumber) {
            writePageVolatileToResponse(fileId, pageNumber, rotationDegrees, response);
        } else {
            final String message = "Trying to write page to response with invalid page " + pageNumber + " for file " + fileId + ".";
            send500Error(response, message);
        }
    }

    public void writePageToResponse(
            final Long pdfPageId,
            final Long fileId,
            final Integer rotationDegrees,
            final HttpServletResponse response,
            final Long loggedUser
    ) throws IOException, InterruptedException, SQLException {
        if (cache.existsPdfPage(pdfPageId)) {
            if (RotationDegrees.NONE.getAngle().equals(rotationDegrees)) {
                final String imageName = pdfPageId + "." + FileUtils.PNG_IMAGE_EXTENSION;
                writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
                cache.writePageToResponse(pdfPageId, response);
            } else {
                writeRotatedPageToResponse(pdfPageId, rotationDegrees, response);
            }
        } else {
            final IFilesDAO dao = Utilities.getBean(IFilesDAO.class);
            final IFileData metadata = newMetadataInstance(dao.HQLT_findById(FileRef.class, fileId));
            startPdfPageGeneration(metadata);
            final String message = "Trying to write not existent page to response for file " + fileId + ".";
            send500Error(response, message);
        }
    }

    private void writePageVolatileToResponse(
            final Long fileId,
            final Integer pageNumber,
            final Integer rotationDegrees,
            final HttpServletResponse response
    ) throws IOException, InterruptedException, SQLException {
        if (isPageVolatileGenerating(fileId, pageNumber)) {
            final String message = "Page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        } else if (cache.existsPageVolatile(fileId, pageNumber)) {
            if (RotationDegrees.NONE.getAngle().equals(rotationDegrees)) {
                final String imageName = fileId + "-" + pageNumber + "." + FileUtils.PNG_IMAGE_EXTENSION;
                writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
                cache.writePageVolatileToResponse(fileId, pageNumber, response);
            } else {
                writeRotatedPageToResponse(fileId, pageNumber, rotationDegrees, response);
            }
        } else {
            generateVolatilePage(fileId, pageNumber);
            final String message = "Page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        }
    }

    private void writeRotatedPageThumbnailToResponse(
            final Long pdfPageId,
            final Integer rotationDegrees,
            final HttpServletResponse response
    ) throws IOException, SQLException {
        final RotationId id = getRotationId(pdfPageId, rotationDegrees, CacheType.PAGE_THUMBNAIL);
        if (isRotatedImageGenerating(id)) {
            final String message = "Rotated page " + pdfPageId + " not ready.";
            send503Error(response, message);
        } else if (cache.existsRotatedImage(id)) {
            final String imageName = id.getIdentifier() + "." + FileUtils.PNG_IMAGE_EXTENSION;
            writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
            cache.writeRotatedImage(id, response);
        } else {
            rotateImage(id);
            final String message = "Rotated page " + pdfPageId + " not ready.";
            send503Error(response, message);
        }
    }

    private void writeRotatedPageThumbnailToResponse(
            final Long fileId,
            final Integer pageNumber,
            final Integer rotationDegrees,
            final HttpServletResponse response
    ) throws IOException, SQLException {
        final RotationId id = getRotationId(fileId, pageNumber, rotationDegrees, CacheType.PAGE_THUMBNAIL_VOLATILE);
        if (isRotatedImageGenerating(id)) {
            final String message = "Volatile rotated page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        } else if (cache.existsRotatedImage(id)) {
            final String imageName = id.getIdentifier() + "." + FileUtils.PNG_IMAGE_EXTENSION;
            writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
            cache.writeRotatedImage(id, response);
        } else {
            rotateImage(id);
            final String message = "Volatile rotated page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        }
    }

    private void writeRotatedPageToResponse(
            final Long pdfPageId,
            final Integer rotationDegrees,
            final HttpServletResponse response
    ) throws IOException, SQLException {
        final RotationId id = getRotationId(pdfPageId, rotationDegrees, CacheType.PDF_PAGE);
        if (isRotatedImageGenerating(id)) {
            final String message = "Rotated Page " + pdfPageId + " not ready.";
            send503Error(response, message);
        } else if (cache.existsRotatedImage(id)) {
            final String imageName = id.getIdentifier() + "." + FileUtils.PNG_IMAGE_EXTENSION;
            writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
            cache.writeRotatedImage(id, response);
        } else {
            rotateImage(id);
            final String message = "Rotated Page " + pdfPageId + " not ready.";
            send503Error(response, message);
        }
    }

    private void writeRotatedPageToResponse(
            final Long fileId,
            final Integer pageNumber,
            final Integer rotationDegrees,
            final HttpServletResponse response
    ) throws IOException, SQLException {
        final RotationId id = getRotationId(fileId, pageNumber, rotationDegrees, CacheType.PDF_PAGE_VOLATILE);
        if (isRotatedImageGenerating(id)) {
            final String message = "Volatile rotated page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        } else if (cache.existsRotatedImage(id)) {
            final String imageName = id.getIdentifier() + "." + FileUtils.PNG_IMAGE_EXTENSION;
            writeHeaders(response, imageName, FileUtils.CONTENT_TYPE_PNG, null, true);
            cache.writeRotatedImage(id, response);
        } else {
            generateVolatilePage(fileId, pageNumber);
            final String message = "Volatile rotated page " + pageNumber + " of file " + fileId + " not ready.";
            send503Error(response, message);
        }
    }

    @Nonnull
    public static ResponseEntity<Void> download(
            @Nonnull final HttpServletResponse response,
            @Nonnull final Long id,
            @Nonnull final ILoggedUser loggedUser
    ) throws IOException {
        final DownloadDto dto = new DownloadDto();
        dto.setFileId(id);
        dto.setIdMode(1);
        final boolean isAdmin = loggedUser.isAdmin();
        final FileManager fileManager = new FileManager();
        fileManager.generateFilePdfAndDownload(
                dto,
                isAdmin,
                null,
                response,
                FileUtils.COLUMN_FILES_CONTENT,
                SecurityUtils.getLoggedUser()
        );
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }


    public String sanitizeMultipartFileName(String originalFilename) {
        final String ext = FileUtils.getExtension(originalFilename);
        int idx = originalFilename.lastIndexOf("." + ext);
        return StringUtils.substring(originalFilename, 0, idx > 0 ? idx : 50) + "." + ext;
    }
}
