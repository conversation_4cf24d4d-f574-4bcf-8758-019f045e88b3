package qms.poll.logic;

import DPMS.DAOInterface.IPollDAO;
import DPMS.Mapping.PollLite;
import java.util.List;
import mx.bnext.core.util.Loggable;
import qms.access.dto.ILoggedUser;

/**
 *
 * <AUTHOR> @Block Networks
 */
public class PollHelper extends Loggable {

    private final IPollDAO dao;

    public PollHelper(IPollDAO dao) {
        this.dao = dao;
    }

    public void closePolls(final ILoggedUser admin) {
        final List<PollLite> polls = dao.getPollsInProcess();
        polls.forEach(poll -> {
            try {
                dao.closePoll(poll, admin);
            } catch (final Exception e) {
                getLogger().error("Error: no se pudo cambiar el estado de la encuesta a CERRADA. {}", poll, e);
            }
        });
    }

    public void startPolls(final ILoggedUser admin) {
        final List<PollLite> polls = dao.getPollsPlanned();
        polls.forEach(poll -> {
            try {
                dao.startPoll(poll, admin);
            } catch (final Exception e) {
                getLogger().error("Error: no se pudo cambiar el estado de la encuesta {} a PROCESO. {}", poll, e);
            }
        });
    }

    public List<Long> getOutstandingSurveyIds(final Long pollId) {
        return dao.getOutstandingSurveyIds(pollId);
    }

}
