
package qms.escalation.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.CodePrefix;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "escalation_exception")
@CodePrefix("EEX-")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class EscalationException extends StandardEntity<Escalation> implements IAuditableEntity, Serializable {

    private static final long serialVersionUID = 1L;
    
    private String code;
    private String description;
    private Integer status = ACTIVE_STATUS;
    private Integer deleted = 0;
    
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private Long userId;
    private String userCode;
    private String userName;
    private Long bossExceptionId;
    private String bossExceptionCode;
    private String bossExceptionName;

    public EscalationException() {
    }

    public EscalationException(long id, UserRef user, UserRef boss) {
        this.id = id;
        this.userId = user.getId();
        this.userCode = user.getCode();
        this.userName = user.getDescription();
        this.description = user.getDescription();
        this.bossExceptionId = boss.getId();
        this.bossExceptionCode = boss.getCode();
        this.bossExceptionName = boss.getDescription();
    }
    
    @Id
    @Basic(optional = false)
    @Column(name = "escalation_exception_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Column(name = "user_code")
    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    @Column(name = "user_description")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Column(name = "boss_exception_id")
    public Long getBossExceptionId() {
        return bossExceptionId;
    }

    public void setBossExceptionId(Long bossExceptionId) {
        this.bossExceptionId = bossExceptionId;
    }

    @Column(name = "boss_exception_code")
    public String getBossExceptionCode() {
        return bossExceptionCode;
    }

    public void setBossExceptionCode(String bossExceptionCode) {
        this.bossExceptionCode = bossExceptionCode;
    }

    @Column(name = "boss_exception_description")
    public String getBossExceptionName() {
        return bossExceptionName;
    }

    public void setBossExceptionName(String bossExceptionName) {
        this.bossExceptionName = bossExceptionName;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date dt) {
        this.createdDate = dt;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date dt) {
        this.lastModifiedDate = dt;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }       
}
