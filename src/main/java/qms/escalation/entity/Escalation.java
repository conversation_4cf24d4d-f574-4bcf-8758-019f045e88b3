
package qms.escalation.entity;

import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import java.io.Serializable;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Subselect(value = Escalation.ESCALATION_VIEW_SQL)
@Immutable
public class Escalation extends StandardEntity<Escalation> implements Serializable {

    public static final String ESCALATION_VIEW_SQL = ""
            + " SELECT  "
                + " u.user_id id"
                + " ,u.code"
                + " ,u.first_name description"
                + " ,u.status"
                + " ,u.deleted"
                + " ,u.scalable"
                + " ,boss.user_id boss_id"
                + " ,boss.code boss_code"
                + " ,boss.first_name boss_name "
                + " ,bossEx.user_id boss_exception_id"
                + " ,bossEx.code boss_exception_code"
                + " ,bossEx.first_name boss_exception_name "
            + " FROM users u "
            + " LEFT JOIN users boss ON u.boss_id = boss.user_id "
            + " LEFT JOIN escalation_exception ex ON u.user_id = ex.user_id AND ex.deleted = 0 AND ex.status = 1"
            + " LEFT JOIN users bossEx ON bossEx.user_id = ex.boss_exception_id ";
    
    private static final long serialVersionUID = 1L; 
    /**
     * Se utiliza como ID exclusivo para determinar que la 
     * excepcion que se está guardando es de tipo "exclusión"
     * es decir que se guarda el valor 
     * cero "0" en la columna "scalable" de la tabla "users"
     */
    public static final Long BOSS_EXCEPTION_ID = -7L;
    
    private String code;
    private String description;
    private Integer status;
    private Integer deleted;
    private Integer scalable;

    private Long bossId;
    private String bossCode;
    private String bossName;
    
    private Long bossExceptionId;
    private String bossExceptionCode;
    private String bossExceptionName;
    
    @Id
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "boss_id")
    public Long getBossId() {
        return bossId;
    }

    public void setBossId(Long bossId) {
        this.bossId = bossId;
    }

    @Column(name = "boss_code")
    public String getBossCode() {
        return bossCode;
    }

    public void setBossCode(String bossCode) {
        this.bossCode = bossCode;
    }

    @Column(name = "boss_name")
    public String getBossName() {
        return bossName;
    }

    public void setBossName(String bossName) {
        this.bossName = bossName;
    }

    @Column(name = "boss_exception_id")
    public Long getBossExceptionId() {
        return bossExceptionId;
    }

    public void setBossExceptionId(Long bossExceptionId) {
        this.bossExceptionId = bossExceptionId;
    }

    @Column(name = "boss_exception_code")
    public String getBossExceptionCode() {
        return bossExceptionCode;
    }

    public void setBossExceptionCode(String bossExceptionCode) {
        this.bossExceptionCode = bossExceptionCode;
    }

    @Column(name = "boss_exception_name")
    public String getBossExceptionName() {
        return bossExceptionName;
    }

    public void setBossExceptionName(String bossExceptionName) {
        this.bossExceptionName = bossExceptionName;
    }

    public Integer getScalable() {
        return scalable;
    }

    public void setScalable(Integer scalable) {
        this.scalable = scalable;
    }

    
}
