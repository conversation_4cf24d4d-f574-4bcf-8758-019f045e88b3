
package qms.escalation.mail;

import DPMS.DAOInterface.IFilesDAO;
import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.IPendingOperation;
import ape.pending.core.PendingHelper;
import ape.pending.entities.PendingRecord;
import ape.pending.entities.PendingType;
import ape.pending.util.ApeUtil;
import com.google.common.collect.ImmutableMap;
import java.util.ArrayList;
import java.util.List;
import mx.bnext.access.Module;
import qms.access.util.AddressableUserHelper;
import qms.escalation.dto.EscalatedOperationDTO;
import qms.framework.core.Mailer;
import qms.framework.core.MailerFactory;
import qms.framework.mail.MailDTO;
import qms.framework.util.CacheRegion;
import qms.framework.util.SettingsUtil;
import qms.framework.util.URLUtils;
import qms.jasper.ReportGenerator;
import qms.util.ModuleUtil;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public abstract class EscalationMailerFactory extends MailerFactory {
    
    private final EscalationMailHelper helper;
    private final IFilesDAO fileDao;
    
    public enum MAIL_TYPE {
        PENDING_RECORDS_ESCALATED, PENDING_RECORD_ESCALATED, PENDING_RECORDS_TO_BE_ESCALATED, PENDING_RECORDS_REASIGNED
    }

    public EscalationMailerFactory(IUntypedDAO dao) {
        super(dao);
        helper = new EscalationMailHelper(dao);
        fileDao = dao.getBean(IFilesDAO.class);
    }
    
    public List<MailDTO> getEmailBuild(MAIL_TYPE type) {
        return getEmailBuild(type, null);
    }
    
    public List<MailDTO> getEmailBuild(MAIL_TYPE type, IPendingOperation pendingOperation) {
        if (!helper.isEscalationOn()) {
            getLogger().debug("Escalation is disabled for all modules.");
            return Utilities.EMPTY_LIST;
        }
        switch(type) {
            case PENDING_RECORDS_ESCALATED:
                return asList(getMailDTOEscalatedToday());
            case PENDING_RECORDS_TO_BE_ESCALATED:
                return asList(getMailDTOEscalatedTomorrow());
            case PENDING_RECORD_ESCALATED:
                return getMailDTOEscalatedOperation(pendingOperation); 
        }
        throw new UnsupportedOperationException(type + " is not supported by 'EscalationMailerFactory'.");
    }
    
    public List<MailDTO> getEmailBuild(PendingRecord pendingRecord) {
        return getMailDTOReassigned(pendingRecord);
    }
    
    private String rowsPendingEscalated(String filter, Module module){
        return  " SELECT "
                + " entity_code, "
                + " entity_description, "
                + " subEntity_code, "
                + " subEntity_description, "
                + " entityTypeName, "
                + " entityDepartmentName, "
                + " entityBusinessUnitName, "
                + " entitySourceCode,"
                + " CONVERT(varchar(10), commitmentDate, 103) commitmentDate, "
                + " unattendedDays, "
                + " ownerCode, "
                + " ownerName, "
                + " superOwnerCode, "
                + " superOwnerName, "
                + " ownerMail, "
                + " superOwnerMail, "
                + " reassignedFromName, "
                + " reassignedFromMail "
            + " FROM ( " 
                + PendingHelper.getRecordRowsSQL(dao.getEntityManager(), module)
            + " ) records"
            + " WHERE "
            + filter
            + " ORDER BY entity_code ";
    }
    
    private List<MailDTO> getMailDTOReassigned(final PendingRecord pendingRecord) {
        final Module module = ModuleUtil.fromKey(pendingRecord.getModule());
        final String moduleName = getTag("module." + pendingRecord.getModule());
        final String sql = rowsPendingEscalated(" id = :recordId", module);
        final Long pendingRecordId = pendingRecord.getId();
        final Long recordId = pendingRecord.getRecordId();
        final List<Object[]> records = dao.SQL_findByQuery(sql, ImmutableMap.of("recordId", pendingRecordId));
        final Object[] temp = records.get(0);
        APE ape = APE.fromCode(Utilities.getApeIndexDataId().get(pendingRecord.getType()));
        return getMailDTO(records, 0, moduleName, String.valueOf(temp[0]), MAIL_TYPE.PENDING_RECORDS_REASIGNED, ape, pendingRecordId, recordId);
    }
    
    private List<MailDTO> getMailDTOEscalatedOperation(final IPendingOperation pendingOperation) {
        final Integer escalationMode = dao.HQL_findSimpleInteger(""
            + " SELECT c.escalationMode"
            + " FROM " + PendingType.class.getCanonicalName() + " c "
            + " WHERE c.id = :typeId",
            ImmutableMap.of("typeId",  pendingOperation.getTypeId()),
            true,
            CacheRegion.CATALOGS_SYSTEM,
            0
        );
        final Module 
            subModule = pendingOperation.getImplementedModule(),
            module = subModule == null ? pendingOperation.getApe().module() : subModule
        ;
        final String 
            moduleKey = subModule == null ? module.getKey() : subModule.getKey(),
            pendingName = getTag("mail.escalated." + moduleKey + "." + pendingOperation.getClass().getCanonicalName()), 
            moduleName = getTag("module." + moduleKey)
        ;
        final String filter = ""
                + " status = " + PendingRecord.STATUS.ESCALATED.getValue()
                + " AND CAST(creationDate as DATE) = CAST(CURRENT_TIMESTAMP as DATE) "
                + " AND pendingTypeId = :typeId"
                + " AND escalationEnabled = 1 " 
        ;
        final String sql = rowsPendingEscalated(filter, module);
        final List<Object[]> records = dao.SQL_findByQuery(sql, ImmutableMap.of("typeId", pendingOperation.getTypeId()));
        return getMailDTO(records, escalationMode, moduleName, pendingName, MAIL_TYPE.PENDING_RECORDS_TO_BE_ESCALATED, null, null, null);
    }
    
    private List<MailDTO> getMailDTO(List<Object[]> records, Integer escalationMode, String moduleName, String pendingName, MAIL_TYPE type, APE ape, Long pendingRecordId, Long recordId) {
        final List<MailDTO> mails = new ArrayList<>();
        if (records.isEmpty()) {
            return mails;
        }
        final String template = Mailer.getTemplate(this.getClass().getCanonicalName() + ".EscalatedOperation");
        final AddressableUserHelper userHelper = new AddressableUserHelper(dao);
        for (int i = 0; i < records.size(); i++) {
            final EscalatedOperationDTO dto = getEscalatedOperationDTO(records.get(i));
            String dataTemplate = Mailer.mapEntity(dto, template, getTags(), dao);
            final MailDTO tempMail = getEscalatedMailDTO(
                    escalationMode,
                    moduleName,
                    pendingName, 
                    dataTemplate, 
                    type, 
                    ape,
                    pendingRecordId, 
                    recordId
            );
            tempMail.getRecipients().add(new Mail(userHelper.findUserByCode(dto.getSuperOwnerCode())));
            mails.add(tempMail);
            mails.add(setMailPreviousAttendant(escalationMode, moduleName, pendingName, dataTemplate, type, dto));
        }
        return mails;
    }

    public MailDTO setMailPreviousAttendant(Integer escalationMode, String moduleName, String pendingName, String template, MAIL_TYPE type, EscalatedOperationDTO dto) {
        MailDTO mailDTO;
        mailDTO = getEscalatedMailDTO(escalationMode, moduleName, pendingName, template, type, null, null, null);
        if (type == MAIL_TYPE.PENDING_RECORDS_TO_BE_ESCALATED) {
            mailDTO.setMessage(
                    getTag("mail.escalated.message.scalatedNotification")
                            .replace("${moduleName}", moduleName)
                            .replace("${pendingName}", pendingName)
                    + "<br><br>"
                    + template
            );
            mailDTO.getRecipients().add(new Mail(dto.getOwnerName(), dto.getOwnerMail()));
        } else {
            mailDTO.setMessage(
                    getTag("mail.reassign.messageReassigned")
                            .replace("${moduleName}", moduleName)
                            .replace("${clave}", pendingName)
                    + "<br><br>"
                    + template
            );
            mailDTO.getRecipients().add(new Mail(dto.getReassignedFromName(), dto.getReassignedFromMail()));
    }
        return mailDTO;
    }

    private EscalatedOperationDTO getEscalatedOperationDTO(Object[] temp) {
        EscalatedOperationDTO dto = new EscalatedOperationDTO();
        dto.setEntityCode(String.valueOf(temp[0]));
        dto.setEntityDescription(String.valueOf(temp[1]));
        dto.setSubEntityCode(String.valueOf(temp[2]));
        dto.setSubEntityDescription(String.valueOf(temp[3]));
        dto.setEntityTypeName(String.valueOf(temp[4]));
        dto.setEntityDepartmentName(String.valueOf(temp[5]));
        dto.setEntityBusinessUnitName(String.valueOf(temp[6]));
        dto.setEntitySourceCode(String.valueOf(temp[7]));
        dto.setCommitmentDate(String.valueOf(temp[8]));
        dto.setUnattendedDays(String.valueOf(temp[9]));
        dto.setOwnerCode(String.valueOf(temp[10]));
        dto.setOwnerName(String.valueOf(temp[11]));
        dto.setSuperOwnerCode(String.valueOf(temp[12]));
        dto.setSuperOwnerName(String.valueOf(temp[13]));
        dto.setOwnerMail(String.valueOf(temp[14]));
        dto.setSuperOwnerMail(String.valueOf(temp[15]));
        dto.setReassignedFromName(String.valueOf(temp[16]));
        dto.setReassignedFromMail(String.valueOf(temp[17]));
        return dto;
    }
    
    private MailDTO getEscalatedMailDTO(Integer escalationMode, String moduleName, String pendingName, String template, MAIL_TYPE type, APE ape, Long pendingRecordId, Long recordId) {
        MailDTO mail = new MailDTO();
        mail.setMessageModuleTitle(getTag("mail.escalated.messageModuleTitle"));
        mail.setMessageLink(getTag("link"));
        mail.setMessageFooter(getTag("footer"));
        if(type == MAIL_TYPE.PENDING_RECORDS_TO_BE_ESCALATED){
            if(PendingType.EscalationMode.YIELD_RESPONSIBILITY.getValue() == escalationMode) {
                mail.setSubject(getTag("mail.escalated.subject.attendant").replace("${moduleName}", moduleName));
                mail.setMessage(getTag("mail.escalated.message.attendant").replace("${moduleName}", moduleName).replace("${pendingName}", pendingName) + "<br><br>" + template);
                mail.setMessageTitle(getTag("mail.escalated.messageTitle.attendant"));
            } else {
                mail.setSubject(getTag("mail.escalated.subject.notification").replace("${moduleName}", moduleName));
                mail.setMessage(getTag("mail.escalated.message.notification").replace("${moduleName}", moduleName).replace("${pendingName}", pendingName)+ "<br><br>" + template);
                mail.setMessageTitle(getTag("mail.escalated.messageTitle.notification"));
            }
        }else if(type == MAIL_TYPE.PENDING_RECORDS_REASIGNED){
            mail.setMessageModuleTitle(getTag("mail.reassign.messageModuleTitle").replace("${moduleName}", moduleName));
            mail.setSubject(getTag("mail.reassign.subject.notification").replace("${moduleName}", moduleName));
            mail.setMessage(getTag("mail.reassign.message").replace("${moduleName}", moduleName).replace("${clave}", pendingName) + "<br><br>" + template);
            mail.setMessageTitle(getTag("mail.reassign.messageTitle"));
        }
        if (ape != null && pendingRecordId != null && recordId != null) {
            final String link = ApeUtil.getPendingLink(ape, recordId) + "&pendingRecordId=" + pendingRecordId;
            mail.setModuleUrl(link);
        }
        return mail;
    }
    
    private List<MailDTO> asList(MailDTO mail) {
        List<MailDTO> mails = new ArrayList<>();
        mails.add(mail);
        return mails;
    }
    
    
    private MailDTO getMailDTOEscalatedTomorrow() {
        MailDTO mail = new MailDTO();
        setMailDTOEscalationMailManagers("qms.jasper.reports.escalation_tomorrow", mail, "fillPendingRecordsToBeEscalated");
        return mail;
    }

    private MailDTO getMailDTOEscalatedToday() {
        MailDTO mail = new MailDTO();
        setMailDTOEscalationMailManagers("qms.jasper.reports.escalation_today", mail, "fillPendingRecordsEscalated");
        return mail;
    }
    
    private void setMailDTOEscalationMailManagers(String reportPath, MailDTO mail, String reportKey) {
        ReportGenerator reportFinding = getInitializedReportGenerator(reportPath),
                        reportComplaint = getInitializedReportGenerator(reportPath);
        reportFinding.setReportName(getTag("module." + Module.ACTION.getKey()));
        reportComplaint.setReportName(getTag("module." + Module.COMPLAINT.getKey()));
        reportFinding.addParam("SQL", PendingHelper.getRecordRowsSQL(dao.getEntityManager(), Module.ACTION));
        reportComplaint.addParam("SQL", PendingHelper.getRecordRowsSQL(dao.getEntityManager(), Module.COMPLAINT));
        mail.setMessageModuleTitle(getTag("mail.escalation.messageModuleTitle"));
        mail.setMessageLink(getTag("link"));
        mail.setMessageFooter(getTag("footer"));
        mail.setMessageTitle(getTag("mail.escalation.messageTitle"));
        mail.setRecipients(helper.getEscalationMailManagers());
        if(reportFinding.isEmpty() && reportComplaint.isEmpty()) {
            mail.setSubject(getTag("mail." + reportKey + ".subject.no"));
            mail.setMessage(getTag("mail." + reportKey + ".message.no"));
        } else {
            mail.setSubject(getTag("mail." + reportKey + ".subject.yes"));
            mail.setMessage(getTag("mail." + reportKey + ".message.yes"));
            if(!reportFinding.isEmpty()) {
                mail.getAttachmentFiles().add(reportFinding.getReportAsFile());
            }
            if(!reportComplaint.isEmpty()) {
                mail.getAttachmentFiles().add(reportComplaint.getReportAsFile());
            }
        }
    }
    
    private ReportGenerator getInitializedReportGenerator(String reportPath){
        Long logo = Utilities.getSettings().getReportLogoId();
        ReportGenerator reportParams = new ReportGenerator(fileDao, reportPath, ReportGenerator.REPORT_EXTENSION.PDF);
        final String appUrl = SettingsUtil.getAppUrlNoSlash();
        final String fullUrl = appUrl + "/view/v-default-report-logo.view";
         if (logo != null && logo > 0 && URLUtils.isReachable(fullUrl)) {
            reportParams.addParam("SYSTEM_LOGO", fullUrl);
        } else if (URLUtils.isHttps(appUrl)) {
            getLogger().error(""
                    + "Error al cargar el logo de los reportes, no se soportan certificados inválidos"
                    + " o autofirmados en la URL del sitio. {}",
                    appUrl
            );
        }
        reportParams.addParam("SYSTEM_COLOR", Utilities.getSettings().getSystemColor());
        return reportParams;
    }
}
