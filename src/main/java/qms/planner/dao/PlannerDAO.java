/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.planner.dao;

import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.User;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import bnext.exception.ExplicitRollback;
import bnext.exception.InvalidOptionalDataException;
import bnext.exception.MakePersistentException;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.sun.star.auth.InvalidArgumentException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.core.ActivityUtil;
import qms.activity.dto.ActivityDTO;
import qms.activity.dto.ActivityInfoDTO;
import qms.activity.dto.DummyActivityChangeReminder;
import qms.activity.dto.ModifiedFieldRequestDTO;
import qms.activity.dto.ModifiedFieldResponseDTO;
import qms.activity.dto.ModifyFieldDTO;
import qms.activity.dto.load.ActivityHistoryDTO;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityParticipant;
import qms.activity.entity.ActivityType;
import qms.activity.pending.ActivityPending;
import qms.activity.util.CommitmentTask;
import qms.activity.util.ReportedActivities;
import qms.framework.dto.MailContactDataDTO;
import qms.framework.entity.Owner;
import qms.framework.entity.SystemLink;
import qms.framework.util.CacheRegion;
import qms.planner.dto.PlannerClientDto;
import qms.planner.dto.PlannerSaveDto;
import qms.planner.entity.Client;
import qms.planner.entity.Planner;
import qms.timesheet.dto.TextPlannerValue;
import qms.timesheet.dto.TimesheetDataSourceDto;
import qms.timesheet.entity.TaskCategory;
import qms.timesheet.entity.TaskDeliveryType;
import qms.timesheet.entity.Timesheet;
import qms.util.EntityCommon;
import qms.util.GridFilter;
import qms.util.OwnerUtil;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Lazy
@Repository(value = "PlannerDAO")
@Scope(value = "singleton")
@Language(module = "qms.planner.dao.PlannerDAO")
public class PlannerDAO extends GenericDAOImpl<Planner, Long> implements IPlannerDAO {

    public static String PLANNER_TASKS_SELECT_MAP =  ""
            + " new map( "
                + " t.description AS text "                                     // text
                + ",t.id AS value"                                              // value
                + ",t.code AS code"                                             // code
                + ",entity.clientId AS clientId "                               // clientId
                + ",entity.id AS plannerId "                                    // plannerId
                + ",entity.code AS plannerCode "                                // plannerCode
                + ",t.id AS activityId"                                         // activityId
                + ",t.code AS activityCode"                                     // activityCode
                + ",tSystemLink.id AS mainSystemId "                            // mainSystemId
                + ",tSystemLink.status AS mainSystemStatus "                    // mainSystemStatus
                + ",tSystemLink.label AS mainSystemLinkLabel "                  // mainSystemLinkLabel
                + ",tSystemLink.url AS mainSystemUrl "                          // mainSystemUrl
                + ",tSystemLink.regexp AS mainSystemRegExp "                    // mainSystemRegExp
                + ",tType.systemLinkAvailable AS systemLinkAvailable"           // systemLinkAvailable
            + " ) "
       ;
    public static String PLANNER_PLANNER_SELECT_MAP =  ""
            + " new map( "
                + " i.description AS text "                                     // text
                + ",entity.id AS value "                                        // value
                + ",entity.code AS code "                                       // value
                + ",entity.clientId AS clientId "                               // clientId
                + ",entity.id AS plannerId "                                    // plannerId
                + ",entity.code AS plannerCode "                                // plannerCode
                + ",entity.activityId AS activityId "                           // activityId
                + ",entitySystemLink.id AS mainSystemId "                       // mainSystemId
                + ",entitySystemLink.status AS mainSystemStatus "               // mainSystemStatus
                + ",entitySystemLink.url AS mainSystemUrl "                     // mainSystemUrl
                + ",entitySystemLink.regexp AS mainSystemRegExp "               // mainSystemRegExp
                + ",entitySystemLink.label AS mainSystemLinkLabel"              // mainSystemLinkLabel
                + ",entityType.systemLinkAvailable AS systemLinkAvailable"      // systemLinkAvailable
            + " ) "
        ;
    public static String PLANNER_CATALOGS_SELECT_MAP = ""
            + " new map( "
                + "c.id as valueClient "                                        // clientid
                + ",c.description AS clientDescription "                        // client description
                + ",c.code AS clientCode "                                      // client code
                + ",t.description AS taskDescription "                          // taskDescription
                + ",t.code AS taskCode "                                        // taskCode
                + ",t.id AS taskId"                                             // taskId
                + ",entity.clientId AS clientId "                               // clientId
                + ",entity.id AS plannerId "                                    // plannerId
                + ",entity.code AS plannerCode "                                // plannerCode
                + ",t.id AS tactivityId "                                       // tactivityId
                + ",t.code AS tactivityCode "                                   // tactivityCode
                + ",tSystemLink.id AS tmainSystemId "                           // tmainSystemId
                + ",tSystemLink.status AS tmainSystemStatus "                   // tmainSystemStatus
                + ",tSystemLink.url AS tmainSystemUrl "                         // tmainSystemUrl
                + ",tSystemLink.regexp AS tmainSystemRegExp "                   // tmainSystemRegExp
                + ",tSystemLink.label AS tmainSystemLinkLabel "                 // tmainSystemLinkLabel
                + ",tType.systemLinkAvailable AS tsystemLinkAvailable "         // tsystemLinkAvailable
                + ",i.description AS plannerDescription "                       // plannerDescription
                + ",entity.activityId AS activityId "                           // activityId
            + " ) "
        ;

    private Planner getPlannerFrom(Planner base, ActivityDTO activity, Long loggedUserId) {
        final Owner owner = OwnerUtil.getOwner(activity.getImplementer());
        base.setOwnerId(
            makePersistent(owner, loggedUserId).getId()
        );
        setPlanned(activity);
        StringBuilder names = new StringBuilder();
        List<String> owners = HQL_findByQuery(""
            + " SELECT u.description"
            + " FROM " + User.class.getCanonicalName() + " u "
            + " WHERE u.id IN (:userIds)"
        ,"userIds", activity.getImplementer());
        if (owners.isEmpty()) {
            throw new RuntimeException("Missing or invalid implementer ID's, received: " + activity.getImplementer());
        }
        owners.forEach((name) -> {
            names.append(", ").append(name);
        });
        base.setOwnerNames(names.substring(2));
        if (base.getStatus() == null) {
            base.setStatus(Planner.STATUS.DRAFT.getValue());
        }
        return base;
    }
    
    private void setPlanned(ActivityDTO dto) {
        if (dto != null) {
            dto.setIsPlanned(true);
            if (dto.getChilds() != null && !dto.getChilds().isEmpty()) {
                dto.getChilds().forEach(child -> setPlanned(child));
            }
        }
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity saveProject(PlannerSaveDto dto, ILoggedUser loggedUser) throws InvalidArgumentException, MakePersistentException {
        if (dto == null) {
            return new ResponseEntity(HttpStatus.BAD_REQUEST);
        }
        final IActivityDAO dao = getBean(IActivityDAO.class);
        final ActivityDTO activity = dto.getActivity();
        final Planner planner = this.getPlannerFrom(dto.getProject(), activity, loggedUser.getId());
        switch (dto.getStrategy()) {
            case DRAFT:
                planner.setStatus(Planner.STATUS.DRAFT.getValue());
                break;
            case RELEASE:
                planner.setStatus(Planner.STATUS.ACTIVE.getValue());
                break;
            case INACTIVE:
                planner.setStatus(Planner.STATUS.INACTIVE.getValue());
                break;
        }
        try {
            if (planner.getId() == null || planner.getId().equals(-1L)) {
                try {
                    planner.setCode(
                            EntityCommon.getPrefixNextCode(planner.getClass())
                    );
                } catch (final QMSException ex) {
                    throw new RuntimeException(ex);
                }
                planner.setId(-1L);
                if (makePersistent(planner, loggedUser.getId()) == null) {
                    return new ResponseEntity(HttpStatus.CONFLICT);
                }
                // Despues de crear el PLANNER se coloca referencia del ID
                final ReportedActivities result = ActivityUtil.save(Module.PLANNER, loggedUser, dao, activity, planner);
                if (result.getRecurrenceId() != null) {
                    HQL_updateByQuery(""
                        + " UPDATE " + Planner.class.getCanonicalName() + " p "
                        + " SET p.activityId = " + result.getRecurrenceId()
                        + " WHERE p.id = :plannerId",
                        ImmutableMap.of("plannerId", planner.getId()),
                        true,
                        CacheRegion.PLANNER,
                        0
                    );
                }
                return new ResponseEntity(HttpStatus.CREATED);
            } else {
                if (makePersistent(planner, loggedUser.getId()) != null) {
                    return new ResponseEntity(HttpStatus.CONFLICT);
                }
            }
        } catch (DataIntegrityViolationException e) {
            getLogger().error("DataIntegrityViolationException with: " +Utilities.getSerializedObj(planner));
            if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                return new ResponseEntity("exist_record", HttpStatus.CONFLICT);
            }
        }
        return new ResponseEntity(HttpStatus.OK);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public ResponseEntity saveClient(Client client, ILoggedUser loggedUser) {

        if (client == null || client.getDescription() == null || client.getBusinessUnits() == null) {
            return new ResponseEntity(HttpStatus.BAD_REQUEST);
        }
        final List<Long> businessUnitIds = client.getBusinessUnits().stream().map((b) -> b.getId()).collect(Collectors.toList());
        final Map<String, Boolean> repeated = new HashMap<>(2);
        final boolean isNew = client.getId() == null || client.getId().equals(-1L);
        StringBuilder hql = new StringBuilder(800);
        // Si la descripción está repetida, agrega "description = true"
        hql.append(""
            + " SELECT count(c) "
            + " FROM ").append(Client.class.getCanonicalName()).append(" c "
            + " JOIN c.businessUnits bu "
            + " WHERE "
                + " bu.id IN (:businessUnitIds)"
                + " AND lower(c.description) = :description");
        if (!isNew) {
            hql.append(" AND c.id != ").append(client.getId());
        }
        if (HQL_findSimpleInteger(hql.toString(), ImmutableMap.of(
            "businessUnitIds", businessUnitIds,
            "description", client.getDescription().trim().toLowerCase()
        ), true, CacheRegion.CLIENT, 0) > 0) {
            repeated.put("description", true);
        }
        // Si la clave está repetida, agrega "code = true"
        hql = new StringBuilder(800);
        hql.append(""
            + " SELECT count(c) "
            + " FROM ").append(Client.class.getCanonicalName()).append(" c "
            + " WHERE lower(c.code) = :code");
        if (!isNew) {
            hql.append(" AND c.id != ").append(client.getId());
        }
        if (client.getCode() != null && HQL_findSimpleInteger(
                hql.toString(),
                ImmutableMap.of("code", client.getCode().trim().toLowerCase()),
                true,
                CacheRegion.CLIENT,
                0
        ) > 0) {
            repeated.put("code", true);
        }
        if (Boolean.TRUE.equals(repeated.get("description")) || Boolean.TRUE.equals(repeated.get("code"))) {
            return new ResponseEntity(new Gson().toJson(repeated), HttpStatus.ALREADY_REPORTED);
        }
        try {
            if (isNew) {
                try {
                    if (client.getCode() == null) {
                        client.setCode(
                                EntityCommon.getPrefixNextCode(client.getClass())
                        );
                    }
                } catch (final QMSException ex) {
                    throw new RuntimeException(ex);
                }
                client.setId(-1L);
                if (makePersistent(client, loggedUser.getId()) == null) {
                    return new ResponseEntity(HttpStatus.CONFLICT);
                }
                return new ResponseEntity(HttpStatus.CREATED);
            } else {
                if (makePersistent(client, loggedUser.getId()) == null) {
                    return new ResponseEntity(HttpStatus.CONFLICT);
                }
            }
        } catch (DataIntegrityViolationException e) {
            getLogger().error("DataIntegrityViolationException with: " +Utilities.getSerializedObj(client));
            if (e.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                return new ResponseEntity("exist_record", HttpStatus.CONFLICT);
            }
        }
        return new ResponseEntity(HttpStatus.OK);
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<TextPlannerValue> getTasksByLoggedUser(ILoggedUser loggedUser) {
        List<Map<String, Object>> results = IPlannerDAO.getPlannerAccessResult(
                this, 
                loggedUser,
                true, 
                false,
                true, 
                false, 
                true, 
                false, 
                PLANNER_TASKS_SELECT_MAP
        );
        Set<TextPlannerValue> result = results.stream().map((r) -> {
            if (r.get("mainSystemId") == null || Objects.equals(r.get("mainSystemStatus"), SystemLink.STATUS.INACTIVE.getValue())) {
                r.put("mainSystemId", null);
                r.put("mainSystemUrl", null);
                r.put("mainSystemRegExp", null);
                r.put("mainSystemLinkLabel", null);
                r.put("systemLinkAvailable", false);
            }
            return new TextPlannerValue(
                (String) r.get("text")
                ,(Long) r.get("value")
                ,(String) r.get("code")
                ,(Long) r.get("clientId")
                ,(String) r.get("clientCode")
                ,(Long) r.get("plannerId")
                ,(String) r.get("plannerCode")
                ,(Long) r.get("activityId")
                ,(String) r.get("activityCode")
                ,(Long) r.get("mainSystemId")
                ,(String) r.get("mainSystemUrl")
                ,(String) r.get("mainSystemRegExp")
                ,(Boolean) r.get("systemLinkAvailable")
            );
        }).collect(Collectors.toSet());
        return new ArrayList<>(result).stream().sorted((a, b) -> {
            return a.getText().compareToIgnoreCase(b.getText());
        }).collect(Collectors.toList());
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<TextPlannerValue> getPlannersByLoggedUser(ILoggedUser loggedUser) {
        List<Map<String, Object>> results = IPlannerDAO.getPlannerAccessResult(
                this,
                loggedUser, 
                false,
                false, 
                true,
                true,
                true,
                true,
                PLANNER_PLANNER_SELECT_MAP
        );
        Set<TextPlannerValue> result = results.stream().map((r) -> {
            if (r.get("mainSystemId") == null || Objects.equals(r.get("mainSystemStatus"), SystemLink.STATUS.INACTIVE.getValue())) {
                r.put("mainSystemId", null);
                r.put("mainSystemUrl", null);
                r.put("mainSystemRegExp", null);
                r.put("mainSystemLinkLabel", null);
                r.put("systemLinkAvailable", false);
            }
            return new TextPlannerValue(
                (String) r.get("text")
                ,(Long) r.get("value"),
                (String) r.get("code")
                ,(Long) r.get("clientId")
                ,(String) r.get("clientCode")
                ,(Long) r.get("plannerId")
                ,(String) r.get("plannerCode")
                ,(Long) r.get("activityId")
                ,(String) r.get("activityCode")
                ,(Long) r.get("mainSystemId")
                ,(String) r.get("mainSystemUrl")
                ,(String) r.get("mainSystemRegExp")
                ,(String) r.get("mainSystemLinkLabel")
                ,(Boolean) r.get("systemLinkAvailable")
            );
        }).collect(Collectors.toSet());
        return new ArrayList<>(result).stream().sorted((a, b) -> {
            return a.getText().compareToIgnoreCase(b.getText());
        }).collect(Collectors.toList());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<PlannerClientDto> getClientsByLoggedUser(final ILoggedUser loggedUser, final Boolean filterActivePlanners, final Boolean byBusinessUnit, Integer status) {
        String filters = "";
        final Map<String, Object> params = new HashMap<>();
        if (byBusinessUnit) {
            if (loggedUser.isAdmin()) {
                filters += " entity.deleted = 0 " + (status != null ? " AND entity.status = " + status : "")  + " ";
            } else {
                filters += " entity.deleted = 0 " + (status != null ? " AND entity.status = " + status : "") + " AND businessUnit.id = :businnesUnitId ";
                params.put("businnesUnitId", loggedUser.getBusinessUnitId());
            }
        } else {
            filters = IPlannerDAO.getWhereAccess(this, true, Client.STATUS.ACTIVE.getValue(), loggedUser);
            if (filters == null || filters.isEmpty()) {
                getLogger().error("{} user does not have acces to clients.", loggedUser.getAccount());
                return new ArrayList<>(0);
            }
        }
        if (filterActivePlanners) {
            final Date dateToday = Utilities.today();
            params.put("dateToday", dateToday);
            filters +=  ""
                    + " AND planner.status = " + Planner.STATUS.ACTIVE.getValue()
                    + " AND planner.endDate >= :dateToday"; // <-- Solo proyectos vigentes
        }
        final List<Map> clientBusinessUnits = HQL_findByQuery(""
            + " SELECT new map("
                    + " businessUnit.id AS businessUnitId "
                    + ",entity.description AS clientName "
                    + ",entity.id AS clientId "
                    + ",entity.code AS clientCode "
                + " ) "
            + " FROM " + Client.class.getCanonicalName() + " entity "
            + " LEFT JOIN " + Planner.class.getCanonicalName() + " planner "
            + " ON planner.clientId = entity.id"
            + " LEFT JOIN entity.businessUnits businessUnit "
            + " LEFT JOIN entity.createdByUser createdBy "
            + " LEFT JOIN entity.lastModifiedByUser lastModifiedBy "
            + " WHERE " + filters
            + " GROUP BY businessUnit.id, entity.description, entity.id, entity.code, planner.endDate",
            params,
            true,
            CacheRegion.PLANNER,
            0
        );
        List<PlannerClientDto> clients = new ArrayList<>(clientBusinessUnits.size());
        PlannerClientDto ref = new PlannerClientDto(), temp;
        Long clientId, businessUnitId;
        String clientName;
        String clientCode;
        int indexOfClientId;
        for (Map value : clientBusinessUnits) {
            clientId = (Long) value.get("clientId");
            businessUnitId = (Long) value.get("businessUnitId");
            clientName = (String) value.get("clientName");
            clientCode = (String) value.get("clientCode");
            ref.setValue(clientId);
            indexOfClientId = clients.indexOf(ref);
            if (indexOfClientId == -1) {
                // Si no se ha agregado
                temp = new PlannerClientDto(clientName, clientId, clientCode);
                temp.setBusinessUnitIds(new ArrayList<>(1));
                temp.getBusinessUnitIds().add(businessUnitId);
                clients.add(temp);
            } else {
                clients.get(indexOfClientId).getBusinessUnitIds().add(businessUnitId);
            }
        }
        return clients;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<PlannerClientDto> getClientsByLoggedUser(final ILoggedUser loggedUser, final Boolean filterActivePlanners) {
        return getClientsByLoggedUser(loggedUser, filterActivePlanners, false, null);
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public TimesheetDataSourceDto geCatalogsByByLoggedUser(ILoggedUser loggedUser, Boolean showImp) {
        final TimesheetDataSourceDto catalog = new TimesheetDataSourceDto();
        List<Map<String, Object>> results = IPlannerDAO.getPlannerAccessResult(
                this, 
                loggedUser,
                true, 
                true, 
                true,
                true,
                true,
                false,
                PLANNER_CATALOGS_SELECT_MAP
        );
        // Clients
        Set<PlannerClientDto> resultClient = results.stream().map((r) -> {
            return new PlannerClientDto(
                (String) r.get("clientDescription")
                ,(Long) r.get("valueClient")
                ,(String) r.get("clientCode")
            );
        }).collect(Collectors.toSet());
        catalog.setClients(new ArrayList<>(resultClient).stream().sorted((a, b) -> {
            return a.getText().compareToIgnoreCase(b.getText());
        }).collect(Collectors.toList()));
        // Planners
        Set<TextPlannerValue> resultPlanner = results.stream().map((r) -> {
            return new TextPlannerValue(
                (String) r.get("plannerDescription")
                ,(Long) r.get("plannerId")
                ,(String) r.get("plannerCode")
                ,(Long) r.get("clientId")
                ,(String) r.get("clientCode")
                ,(Long) r.get("plannerId")
                ,(String) r.get("plannerCode")
                ,(Long) r.get("activityId")
                ,(String) r.get("activityCode")
            );
        }).collect(Collectors.toSet());
        catalog.setPlanners(new ArrayList<>(resultPlanner).stream().sorted((a, b) -> {
            return a.getText().compareToIgnoreCase(b.getText());
        }).collect(Collectors.toList()));
        //Task
        Set<TextPlannerValue> resultTask = results.stream().map((Map<String, Object> r) -> {
            if (r.get("tmainSystemId") == null || Objects.equals(r.get("tmainSystemStatus"), SystemLink.STATUS.INACTIVE.getValue())) {
                r.put("tmainSystemId", null);
                r.put("tmainSystemUrl", null);
                r.put("tmainSystemRegExp", null);
                r.put("tmainSystemLinkLabel", null);
                r.put("tsystemLinkAvailable", false);
            }
            return new TextPlannerValue(
                (String) r.get("taskDescription")
                ,(Long) r.get("taskId")
                ,(String) r.get("taskCode")
                ,(Long) r.get("clientId")
                ,(String) r.get("clientCode")
                ,(Long) r.get("plannerId")
                ,(String) r.get("plannerCode")
                ,(Long) r.get("tactivityId")
                ,(String) r.get("tactivityCode")
                ,(Long) r.get("tmainSystemId")
                ,(String) r.get("tmainSystemUrl")
                ,(String) r.get("tmainSystemRegExp")
                ,(String) r.get("tmainSystemLinkLabel")
                ,(Boolean) r.get("tsystemLinkAvailable")
            );
        }).collect(Collectors.toSet());
        catalog.setTasks(new ArrayList<>(resultTask).stream().sorted((a, b) -> {
            return a.getText().compareToIgnoreCase(b.getText());
        }).collect(Collectors.toList()));
        final IActivityDAO activityDao = Utilities.getBean(IActivityDAO.class);
        catalog.setTasksActivities(new ArrayList<>(activityDao.getTaskActivitiesByLoggedUser(loggedUser, showImp)).stream().sorted((a, b) -> {
            return a.getText().compareToIgnoreCase(b.getText());
        }).collect(Collectors.toList()));
        return catalog;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public TimesheetDataSourceDto getProjectCatalogs(ILoggedUser loggedUser) {
        TimesheetDataSourceDto dataSource = new TimesheetDataSourceDto();
        dataSource.setClients(
                getClientsByLoggedUser(loggedUser, true)
        );
        dataSource.setPlanners(
                getPlannersByLoggedUser(loggedUser)
        );
        dataSource.setTasks(
                getTasksByLoggedUser(loggedUser)
        );
        dataSource.setTaskDeliveryTypes(
                getTasksDeliveryByLoggedUser(loggedUser)
        );
        dataSource.setTaskCategories(
                getTasksCategoriesByLoggedUser(loggedUser)
        );
        final Map<Long, String> clientCodes;
        if (dataSource.getClients() != null && !dataSource.getClients().isEmpty()) {
            clientCodes = dataSource.getClients().stream()
                    .collect(Collectors.toMap(PlannerClientDto::getValue, PlannerClientDto::getCode));
        } else {
            clientCodes = new HashMap<>(0);
        }
        final Map<Long, String> plannerCodes;
        if (dataSource.getPlanners() != null && !dataSource.getPlanners().isEmpty()) {
            plannerCodes = dataSource.getPlanners().stream()
                    .collect(Collectors.toMap(TextPlannerValue::getValue, TextPlannerValue::getCode));
        } else {
            plannerCodes = new HashMap<>(0);

        }
        if (!clientCodes.isEmpty() || !plannerCodes.isEmpty()) {
            dataSource.getTasks().forEach(task -> {
                if (task.getClientCode() == null || task.getClientCode().isEmpty()) {
                    String clientCode = clientCodes.get(task.getClientId());
                    task.setClientCode(clientCode);
                }
                if (task.getPlannerCode() == null || task.getPlannerCode().isEmpty()) {
                    String plannerCode = plannerCodes.get(task.getPlannerId());
                    task.setPlannerCode(plannerCode);
                }
            });
            dataSource.getPlanners().forEach(planner -> {
                if (planner.getClientCode() == null || planner.getClientCode().isEmpty()) {
                    String clientCode = clientCodes.get(planner.getClientId());
                    planner.setClientCode(clientCode);
                }
            });
        }
        return dataSource;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getClientActives() {
        return Utilities.getUntypedDAO().getStrutsComboList(Client.class, true, CacheRegion.CATALOGS_PLANNER, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getPlannersActives() {
        String hql = " "
                + " SELECT new " + TextLongValue.class.getCanonicalName() + "("
                    + " planner.description AS entity_description "
                    + ", entity.id AS entity_id "
                + ")"
                + " FROM " + Planner.class.getCanonicalName() + " entity "
                + " JOIN " + Activity.class.getCanonicalName() + " ac"
                + " ON ac.id = entity.activityId"
                + " JOIN ac.events planner"
                + " WHERE entity.deleted = 0 "
                + " AND planner.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                + " AND entity.status = " + Planner.STATUS.ACTIVE.getValue();
        return HQL_findByQuery(hql, Utilities.EMPTY_MAP, true, CacheRegion.CATALOGS_PLANNER, 0);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getTaskActives() {
        String hql = " "
                + " SELECT new " + TextLongValue.class.getCanonicalName() + "("
                    + " task.description AS entity_description "
                    + ", task.id AS entity_id "
                + ")"
                + " FROM " + Planner.class.getCanonicalName() + " entity "
                + " LEFT JOIN entity.tasks task "
                + " WHERE entity.deleted = 0 "
                + " AND task.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                + " AND task.status IN ( " +
                    + Activity.STATUS.REPORTED.getValue() + ", "
                    + Activity.STATUS.RETURNED.getValue() + ", "
                    + Activity.STATUS.IN_PROCESS.getValue()
                + " )";
        return HQL_findByQuery(hql, Utilities.EMPTY_MAP, true, CacheRegion.CATALOGS_PLANNER, 0);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<TextLongValue> getTasksDeliveryByLoggedUser(ILoggedUser loggedUser) {
        final boolean showAll = loggedUser.isAdmin() || loggedUser.getServices().contains(
                ProfileServices.PLANNER_VIEW_ALL
        );
        String filter = " entity.deleted = 0 AND entity.status = " + TaskDeliveryType.STATUS.ACTIVE.getValue();
        if (!showAll) {
            filter += ""
                + " AND bud.businessUnitId IN ("
                    + IBusinessUnitDAO.getLoggedUserBusinessUnitIdsQuery(loggedUser.getId(), loggedUser.isAdmin())
                + " )";
        }
        return HQL_findByQuery(""
            + " SELECT DISTINCT new " + TextLongValue.class.getCanonicalName() + "("
                + " entity.description " // text
                + ",entity.id "          // value
            + " )"
            + " FROM " + TaskDeliveryType.class.getCanonicalName() + " entity "
            + " LEFT JOIN entity.businessUnitDepartments bud "
            + " WHERE " + filter,
            Utilities.EMPTY_MAP,
            true, CacheRegion.CATALOGS_PLANNER,
            0
        );
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<TextLongValue> getTasksCategoriesByLoggedUser(ILoggedUser loggedUser) {
        return HQL_findByQuery(""
            + " SELECT DISTINCT new " + TextLongValue.class.getCanonicalName() + "("
                + " entity.description "                 // text
                + ",entity.id "                          // value
            + " )"
            + " FROM " + TaskCategory.class.getCanonicalName() + " entity "
            + " WHERE "
                + " entity.deleted = 0"
                + " AND entity.status = " + TaskCategory.STATUS.ACTIVE.getValue(),
            Utilities.EMPTY_MAP,
            true, CacheRegion.CATALOGS_PLANNER,
            0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<MailContactDataDTO>> saveContactMailList(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<MailContactDataDTO> newMailContact, 
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(3);
        if (newMailContact.getValue() != null && newMailContact.getValue().getValue() != null) {
            params.put("contactMailList", newMailContact.getValue().getValue());
            params.put("plainContactMailList", newMailContact.getValue().getPlainValue());
        } else {
            params.put("contactMailList", "");
            params.put("plainContactMailList", "");            
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        if (
            HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.contactMailList = :contactMailList"
                        + " ,t.plainContactMailList = :plainContactMailList"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            ) == 1
        ) {
            final ModifiedFieldResponseDTO<MailContactDataDTO> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("contactMailList");
            response.setModifiedValue(newMailContact.getValue());
            response.setPreviousValue(newMailContact.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newMailContact.getOldValue() == null || newMailContact.getOldValue().getValue() == null) {
                previous = "";
            } else {
                previous = StringUtils.trim(newMailContact.getOldValue().getValue());
            }
            final String newValue;
            if (newMailContact.getValue() == null || newMailContact.getValue().getValue() == null) {
                newValue = "";
            } else {
                newValue = StringUtils.trim(newMailContact.getValue().getValue());
            }
            final String message = getTag("contactMailListUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous);
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Double>> saveSoldHours(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Double> newSoldHours, 
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(3);
        final boolean hasNewValue = !newSoldHours.isDeleteValue() 
                && newSoldHours.getValue() != null;
        if (hasNewValue) {
            params.put("soldHours", newSoldHours.getValue());
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        final Integer updatedRecords;
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.soldHours = :soldHours"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.soldHours = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );            
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Double> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("soldHours");
            response.setModifiedValue(newSoldHours.getValue());
            response.setPreviousValue(newSoldHours.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newSoldHours.getOldValue() == null) {
                previous = "";
            } else {
                previous = Utilities.formatDouble(newSoldHours.getOldValue(), 2).toString() + "%";
            }
            final String newValue;
            if (newSoldHours.getValue() == null) {
                newValue = "";
            } else {
                newValue = Utilities.formatDouble(newSoldHours.getValue(), 2).toString() + "%";
            }
            final String message = getTag("soldHoursUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", newSoldHours.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Double>> saveApprovedHours(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Double> newApprovedHours, 
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(3);
        final boolean hasNewValue = !newApprovedHours.isDeleteValue() 
                && newApprovedHours.getValue() != null 
                && newApprovedHours.getValue() != null;
        if (hasNewValue) {
            params.put("approvedHours", newApprovedHours.getValue());
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        final Integer updatedRecords;
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.approvedHours = :approvedHours"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.approvedHours = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );       
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Double> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("approvedHours");
            response.setModifiedValue(newApprovedHours.getValue());
            response.setPreviousValue(newApprovedHours.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newApprovedHours.getOldValue() == null) {
                previous = "";
            } else {
                previous = Utilities.formatDouble(newApprovedHours.getOldValue(), 2).toString() + "%";
            }
            final String newValue;
            if (newApprovedHours.getValue() == null) {
                newValue = "";
            } else {
                newValue = Utilities.formatDouble(newApprovedHours.getValue(), 2).toString() + "%";
            }
            final String message = getTag("approvedHoursUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", newApprovedHours.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Date>> saveStartDate(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Date> newStartDate, 
            final ILoggedUser loggedUser
    ) throws InvalidOptionalDataException {
        final Map<String, Object> params = new HashMap<>(3);
        final boolean hasNewValue = !newStartDate.isDeleteValue() 
                && newStartDate.getValue() != null;
        if (hasNewValue) {
            params.put("startDate", newStartDate.getValue());
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        final Integer updatedRecords;
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.startDate = :startDate"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.startDate = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );       
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Date> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("startDate");
            response.setModifiedValue(newStartDate.getValue());
            response.setPreviousValue(newStartDate.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newStartDate.getOldValue() == null) {
                previous = "";
            } else {
                previous = Utilities.formatDateBy(newStartDate.getOldValue(), "dd/MM/yyyy");
            }
            final String newValue;
            if (newStartDate.getValue() == null) {
                newValue = "";
            } else {
                newValue = Utilities.formatDateBy(newStartDate.getOldValue(), "dd/MM/yyyy");
                ModifyFieldDTO<Date> changeImplementationResult = Utilities.getBean(IActivityDAO.class).changeImplementation(
                        activity,
                        newStartDate.getValue(), 
                        newStartDate.getComment(), 
                        false,
                        true,
                        loggedUser
                );
                if (changeImplementationResult.isDeleted() || changeImplementationResult.isModified()) {
                    // Se recalculan los pendientes del proyecto
                    new ActivityPending(this).recalculate(activity.getId(), activity.getModule(), loggedUser, PlannerDAO.class);
                }
            }
            final String message = getTag("startDateUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", newStartDate.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Date>> saveEndDate(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Date> endDate, 
            final ILoggedUser loggedUser
    ) {
        final boolean hasNewValue = !endDate.isDeleteValue() 
                && endDate.getValue() != null;
        final Integer updatedRecords;
        Long activityId = HQL_findLong(""
            + " SELECT c.activityId"
            + " FROM " + Planner.class.getCanonicalName() + " c "
            + " WHERE c.id = :id", 
            ImmutableMap.of("id", plannerId),
            true,
            CacheRegion.PLANNER,
            0
        );
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(" "
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.endDate = :endDate"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId", 
                ImmutableMap.of(
                    "endDate", endDate.getValue(),
                    "loggedUserId", loggedUser.getId(),
                    "plannerId", plannerId
                ),
                true,
                CacheRegion.PLANNER,
                0
            );
            if (HQL_updateByQuery(" "
                + " UPDATE " + Activity.class.getCanonicalName() + " a "
                + " SET"
                        + " a.reminder = :reminder"
                        + ",a.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",a.lastModifiedBy = :loggedUserId"
                + " WHERE a.recurrenceId = :recurrenceId",
                ImmutableMap.of(
                    "reminder", endDate.getValue(),
                    "loggedUserId", loggedUser.getId(),
                    "recurrenceId", activityId
                ),
                true,
                CacheRegion.PLANNER,
                0
            ) == 0) {
                throw new ExplicitRollback("Project endDate could not be modified");
            }
            if (endDate.getOptionalData() != null && endDate.getOptionalData().get("taskIds") != null) {
                final IActivityDAO activityDao = getBean(IActivityDAO.class);
                final List<Long> ids = Utilities.asListLong(
                    (List) endDate.getOptionalData().get("taskIds")
                );
                final List<DummyActivityChangeReminder> activities = activityDao.getDummyActivityChangeReminder(
                    loggedUser, CommitmentTask.IMPLEMENTATION, ids
                );
                if (activities.size() != ids.size()) {
                    throw new ExplicitRollback(
                        "Project endDate could not be modified because there's invalid task actitivyId values"
                    );
                }
                activities.forEach(a -> {
                    try {
                        activityDao.changeReminder(
                            a, 
                            endDate.getValue(),
                            endDate.getComment(),
                            true, // keepAnticipationAttendDays
                            loggedUser
                        );
                    } catch (InvalidOptionalDataException ex) {
                        getLogger().error("Couldn't save task endDate change, activityId: " + a.getId());
                    }
                });
            }
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.endDate = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                ImmutableMap.of(
                    "loggedUserId", loggedUser.getId(),
                    "plannerId", plannerId
                ),
                true,
                CacheRegion.PLANNER,
                0
            );       
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Date> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("endDate");
            response.setModifiedValue(endDate.getValue());
            response.setPreviousValue(endDate.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (endDate.getOldValue() == null) {
                previous = "";
            } else {
                previous = Utilities.formatDateBy(endDate.getOldValue(), "dd/MM/yyyy");
            }
            final String newValue;
            if (endDate.getValue() == null) {
                newValue = "";
            } else {
                newValue = Utilities.formatDateBy(endDate.getValue(), "dd/MM/yyyy");
            }
            final String message = getTag("endDateUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", endDate.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            response.setDisabledFields(activityDao.disabledFields(activity));
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Long>> saveTaskTypeId(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Long> newTaskTypeId, 
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(3);
        final boolean hasNewValue = !newTaskTypeId.isDeleteValue() 
                && newTaskTypeId.getValue() != null;
        if (hasNewValue) {
            params.put("taskTypeId", newTaskTypeId.getValue());
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        final Integer updatedRecords;
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.taskTypeId = :taskTypeId"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.taskTypeId = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );       
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Long> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("taskTypeId");
            response.setModifiedValue(newTaskTypeId.getValue());
            response.setPreviousValue(newTaskTypeId.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newTaskTypeId.getOldValue() == null) {
                previous = "";
            } else {
                previous = HQL_findSimpleString(""
                        + " SELECT c.description "
                        + " FROM " + ActivityType.class.getCanonicalName() + " c "
                        + " WHERE c.id = :taskTypeId",
                        ImmutableMap.of(
                            "taskTypeId", newTaskTypeId.getOldValue()
                        ),
                        true,
                        CacheRegion.CATALOGS_ACTIVITY,
                        0
                );
            }
            final String newValue;
            if (newTaskTypeId.getValue() == null) {
                newValue = "";
            } else {
                newValue = HQL_findSimpleString(""
                        + " SELECT c.description "
                        + " FROM " + ActivityType.class.getCanonicalName() + " c "
                        + " WHERE c.id = :taskTypeId",
                        ImmutableMap.of(
                            "taskTypeId", newTaskTypeId.getValue()
                        ),
                        true,
                        CacheRegion.CATALOGS_ACTIVITY,
                        0
                );
            }
            final String message = getTag("taskTypeIdUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", newTaskTypeId.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Long>> saveClientId(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Long> newClientId, 
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(3);
        final boolean hasNewValue = !newClientId.isDeleteValue() 
                && newClientId.getValue() != null;
        if (hasNewValue) {
            params.put("clientId", newClientId.getValue());
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        final Integer updatedRecords;
        final Integer updatedAllReferencesActivities;
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.clientId = :clientId"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
            updatedAllReferencesActivities = HQL_updateByQuery(""
                + " UPDATE " + Activity.class.getCanonicalName() + " a "
                + " SET "
                        + " a.clientId = :clientId "
                        + ",a.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",a.lastModifiedBy = :loggedUserId"
                + " WHERE a.plannerId = :plannerId",
                ImmutableMap.of("clientId", newClientId.getValue(), "plannerId", plannerId, "loggedUserId", loggedUser.getId()),
                true,
                CacheRegion.ACTIVITY, 
                0
            );
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.clientId = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
            updatedAllReferencesActivities = HQL_updateByQuery(""
                + " UPDATE " + Activity.class.getCanonicalName() + " a "
                + " SET "
                        + " a.clientId = :clientId "
                        + ",a.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",a.lastModifiedBy = :loggedUserId"
                + " WHERE a.plannerId = :plannerId",
                ImmutableMap.of("clientId", newClientId.getValue(), "plannerId", plannerId, "loggedUserId", loggedUser.getId()),
                true,
                CacheRegion.ACTIVITY, 
                0
            );
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Long> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("clientId");
            response.setModifiedValue(newClientId.getValue());
            response.setPreviousValue(newClientId.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newClientId.getOldValue() == null) {
                previous = "";
            } else {
                previous = HQL_findSimpleString(""
                        + " SELECT c.description "
                        + " FROM " + Client.class.getCanonicalName() + " c "
                        + " WHERE c.id = :clientId",
                        ImmutableMap.of(
                            "clientId", newClientId.getOldValue()
                        ),
                        true,
                        CacheRegion.CLIENT,
                        0
                );
            }
            final String newValue;
            if (newClientId.getValue() == null) {
                newValue = "";
            } else {
                newValue = HQL_findSimpleString(""
                        + " SELECT c.description "
                        + " FROM " + Client.class.getCanonicalName() + " c "
                        + " WHERE c.id = :clientId",
                        ImmutableMap.of(
                            "clientId", newClientId.getValue()
                        ),
                        true,
                        CacheRegion.CLIENT,
                        0
                );
            }
            final String message = getTag("clientIdUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", newClientId.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Double>> savePlannedProgress(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Double> newPlannedProgress, 
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(3);
        final boolean hasNewValue = !newPlannedProgress.isDeleteValue() 
                && newPlannedProgress.getValue() != null;
        if (hasNewValue) {
            params.put("plannedProgress", newPlannedProgress.getValue());
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        final Integer updatedRecords;
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.plannedProgress = :plannedProgress"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.plannedProgress = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Double> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("plannedProgress");
            response.setModifiedValue(newPlannedProgress.getValue());
            response.setPreviousValue(newPlannedProgress.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newPlannedProgress.getOldValue() == null || newPlannedProgress.getOldValue() == null) {
                previous = "";
            } else {
                previous = Utilities.formatPercentage(newPlannedProgress.getOldValue(), 2).toString();
            }
            final String newValue;
            if (newPlannedProgress.getValue() == null || newPlannedProgress.getValue() == null) {
                newValue = "";
            } else {
                newValue = Utilities.formatPercentage(newPlannedProgress.getValue(), 2).toString();
            }
            final String message = getTag("plannedProgressUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", newPlannedProgress.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<ModifiedFieldResponseDTO<Double>> saveRealProgress(
            final Long plannerId, 
            final ModifiedFieldRequestDTO<Double> newRealProgress, 
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(3);
        final boolean hasNewValue = !newRealProgress.isDeleteValue() 
                && newRealProgress.getValue() != null 
                && newRealProgress.getValue() != null;
        if (hasNewValue) {
            params.put("realProgress", newRealProgress.getValue());
        }
        params.put("loggedUserId", loggedUser.getId());
        params.put("plannerId", plannerId);
        final Integer updatedRecords;
        if (hasNewValue) {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.realProgress = :realProgress"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );
        } else {
            updatedRecords = HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.realProgress = null"
                        + ",t.lastModifiedDate = CURRENT_TIMESTAMP"
                        + ",t.lastModifiedBy = :loggedUserId"
                + " WHERE t.id = :plannerId",
                params,
                true,
                CacheRegion.PLANNER,
                0
            );  
        }
        if (updatedRecords == 1) {
            final ModifiedFieldResponseDTO<Double> response = new ModifiedFieldResponseDTO<>();
            response.setModified(true);
            response.setFieldName("realProgress");
            response.setModifiedValue(newRealProgress.getValue());
            response.setPreviousValue(newRealProgress.getOldValue());
            response.setUnnecessary(false);
            response.setHistory(new ArrayList<>(0));
            final Long activityId = getPlannerActivityId(plannerId);
            final IActivityDAO activityDao = getBean(IActivityDAO.class);
            final ActivityInfoDTO activity = activityDao.getActivityInfoFromId(activityId, loggedUser);
            final String previous;
            if (newRealProgress.getOldValue() == null || newRealProgress.getOldValue() == null) {
                previous = "";
            } else {
                previous = Utilities.formatPercentage(newRealProgress.getOldValue(), 2).toString();
            }
            final String newValue;
            if (newRealProgress.getValue() == null || newRealProgress.getValue() == null) {
                newValue = "";
            } else {
                newValue = Utilities.formatPercentage(newRealProgress.getValue(), 2).toString();
            }
            final String message = getTag("realProgressUpdated")
                        .replace("{current}", newValue)
                        .replace("{previous}", previous)
                        .replace("{comment}", newRealProgress.getComment());
            final ActivityHistoryDTO history = activityDao.saveHistoryMessageBySystem(activity, message, loggedUser);
            response.getHistory().add(history);
            return new ResponseEntity<>(response, HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.CONFLICT);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer saveImplementerByPlannerActivityId(
            final Long activityId, 
            final ModifiedFieldRequestDTO<Long> implementer, 
            final ILoggedUser loggedUser
    ) {
        final List<Long> taskIds = HQL_findByQuery(""
                + " SELECT imp.id"
                + " FROM " + Activity.class.getCanonicalName() + " act"
                + " JOIN " + Activity.class.getCanonicalName() + " imp"
                + " ON imp.parentActivityImplementationId = act.id"
                + " WHERE act.id = :activityId"
                + " AND imp.deleted = 0"
                + " AND imp.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue(),
                ImmutableMap.of("activityId", activityId),
                true, CacheRegion.ACTIVITY,
                0
        );
        final List<UserRef> oldImplementer = implementer.getOldValue() == null ? null : ImmutableList.of(getUserRef(implementer.getOldValue()));
        final List<UserRef> newImplementer = implementer.getValue() == null ? null : ImmutableList.of(getUserRef(implementer.getValue()));
        final Long modifiedCount = taskIds.stream().map(taskId -> saveTaskImplementer(taskId, implementer, oldImplementer, newImplementer, loggedUser))
                .filter(taskResult -> taskResult.isModified())
                .count();
        return modifiedCount.intValue();
    }

    private UserRef getUserRef(final Long userId) {
        return Utilities.getBean(IUserDAO.class).getUserRef(userId, false);
    }
    
    private ModifyFieldDTO<List<UserRef>> saveTaskImplementer(
            final Long taskId, 
            final ModifiedFieldRequestDTO<Long> implementer,
            final List<UserRef> oldImplementer,
            final List<UserRef> newImplementer,
            final ILoggedUser loggedUser) {
        final IActivityDAO dao = getBean(IActivityDAO.class);
        final ActivityInfoDTO task = dao.getActivityInfoFromId(taskId, loggedUser);
        final ModifyFieldDTO<List<UserRef>> saved;
        if (Activity.STATUS.REPORTED.equals(task.getStatus())) {
            saved = dao.changeImplementerToStart(
                task, oldImplementer, newImplementer, implementer.getComment(), implementer.isDeleteValue(), loggedUser
            );
        } else if (
            Activity.STATUS.RETURNED.equals(task.getStatus())
            || Activity.STATUS.IN_PROCESS.equals(task.getStatus())
        ) {
            saved = dao.changeImplementerToComplete(
                task, oldImplementer, newImplementer, implementer.getComment(), implementer.isDeleteValue(), loggedUser
            );
        } else {
            getLogger().error("Invalid project leader modification, status " + task.getStatus() + " is not valid.");
            saved = new ModifyFieldDTO<>(null, false, null, null);
        }
        if (saved == null || !saved.isModified()) {
            getLogger().warn("Failed to update task leader for task. {}", task);
        }
        return saved;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveBusinessUnitDepartmentId(
            final ActivityInfoDTO current,
            final Long newBusinessUnitDepartmentId
    ) {
        final Long businessUnitId = HQL_findSimpleLong(""
                + " SELECT c.businessUnitId"
                + " FROM " + BusinessUnitDepartmentRef.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", newBusinessUnitDepartmentId),
                false,
                null,
                0
        );
        if (businessUnitId != null && current.getParentPlannerId() != null) {
            HQL_updateByQuery(""
                + " UPDATE " + Planner.class.getCanonicalName() + " t "
                + " SET"
                        + " t.businessUnitDepartmentId = :newBusinessUnitDepartmentId"
                        + ",t.businessUnitId = :businessUnitId"
                + " WHERE t.id = :plannerId",
                ImmutableMap.of(
                        "plannerId", current.getParentPlannerId(),
                        "businessUnitId", businessUnitId,
                        "newBusinessUnitDepartmentId", newBusinessUnitDepartmentId
                ),
                true,
                CacheRegion.PLANNER,
                0
            );
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> projectList(
            final Long participantId,
            final GridFilter filter,
            final Boolean onlyActives,
            final ILoggedUser loggedUser
    ) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("entity.id");
            filter.setDirection(Byte.parseByte("2"));
        }
        final String filters = IPlannerDAO.getWhereAccess(this, false, Planner.STATUS.ACTIVE.getValue(), loggedUser);
        if (filters == null || filters.isEmpty()) {
            getLogger().error("{} user does not have acces to planners.", loggedUser.getAccount());
            return Utilities.EMPTY_GRID_INFO;
        }
        StringBuilder HQL = new StringBuilder(100);
        HQL.append(""
            + " SELECT new map("
                + " entity.id AS entity_id "
                + ",entity.status AS entity_status "
                + ",entity.code AS entity_code "
                + ",entity.tags AS entity_tags "
                + ",entity.contactMailList AS entity_contactMailList "
                + ",entity.soldHours AS entity_soldHours "
                + ",entity.approvedHours AS entity_approvedHours "
                + ",entity.plannedProgress AS entity_plannedProgress "
                + ",entity.realProgress AS entity_realProgress "
                + ",a.plannedHours AS a_plannedHours "
                + ",i.actualHours AS i_actualHours "
                + ",i.description AS i_description "
                + ",entity.startDate AS entity_startDate "
                + ",entity.endDate AS entity_endDate "
                + ",client.description AS client_description "
                + ",i.implementerNames AS i_implementerNames "
                + ",i.implementerIds AS i_implementerIds "
                + ",businessUnit.description AS businessUnit_description "
                + ",businessUnitDepartment.description AS businessUnitDepartment_description "
                + ",entity.createdDate AS entity_createdDate "
                + ",entity.lastModifiedDate AS entity_lastModifiedDate "
                + ",createdBy.description AS createdByName "
                + ",lastModifiedBy.description AS lastModifiedByName "
                + ",MAX(t.lastModifiedDate) AS t_lastModifiedDate"
            + " )"
            + " FROM ").append(Planner.class.getCanonicalName()).append(" entity "
            + " LEFT JOIN entity.client client "
            + " LEFT JOIN entity.businessUnit businessUnit "
            + " LEFT JOIN entity.businessUnitDepartment businessUnitDepartment "
            + " LEFT JOIN entity.createdByUser createdBy "
            + " LEFT JOIN entity.lastModifiedByUser lastModifiedBy "
            + " LEFT JOIN entity.activity a "
            + " LEFT JOIN ").append(Timesheet.class.getCanonicalName()).append(" t ON t.plannerId = entity.id AND t.deleted = 0"
            + " JOIN a.events i "
            + " WHERE ").append(filters)
            .append(" "
            + " AND i.commitmentTask = ").append(CommitmentTask.IMPLEMENTATION.getValue());
        if (participantId != null) {
            HQL.append(""
                + " AND entity.activityId IN ("
                    + " SELECT at.recurrenceId FROM ").append(Activity.class.getCanonicalName()).append(" at "
                        + " WHERE at.id IN ( "
                            + " SELECT ap.id.activityId "
                            + " FROM ").append(ActivityParticipant.class.getCanonicalName()).append(" ap "
                            + " WHERE ap.id.userId = ").append(participantId).append(""
                        + " ) "
                + " )");
        }
        if (onlyActives) {
            HQL.append(" "
                + "AND entity.status = ").append(Planner.STATUS.ACTIVE.getValue());
        }
        HQL.append(""
            + " GROUP BY "
                + " entity.id "
                + ",entity.status "
                + ",entity.code "
                + ",entity.tags "
                + ",entity.contactMailList"
                + ",entity.soldHours"
                + ",entity.approvedHours"
                + ",entity.plannedProgress"
                + ",entity.realProgress"
                + ",entity.startDate "
                + ",entity.endDate "
                + ",i.implementerNames "
                + ",i.implementerIds "
                + ",i.description "
                + ",a.plannedHours"
                + ",i.actualHours"
                + ",client.description "
                + ",businessUnit.description "
                + ",businessUnitDepartment.description "
                + ",entity.createdDate "
                + ",entity.lastModifiedDate "
                + ",createdBy.description "
                + ",lastModifiedBy.description "
            );
        return HQL_getRows(HQL, filter, true, CacheRegion.TEMPLATE_ACTIVITY, 0);  
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getPlannerActivityId(Long plannerId) {
        return HQL_findLong(" "
                        + " SELECT a.parentActivityImplementationId"
                        + " FROM " + Planner.class.getCanonicalName() + " c "
                        + " LEFT JOIN " + Activity.class.getCanonicalName() + " a ON a.parentActivityId = c.activityId "
                        + " WHERE c.id = :id "
                        + " GROUP BY a.parentActivityImplementationId ",
                ImmutableMap.of("id", plannerId),
                true,
                CacheRegion.PLANNER,
                0
        );
    }

}
