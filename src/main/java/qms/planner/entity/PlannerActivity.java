package qms.planner.entity;

import Framework.Config.CompositeStandardEntity;
import ape.pending.core.SoftBaseAPE;
import ape.pending.core.SoftEntityId;
import ape.pending.core.StrongEntityId;
import qms.framework.util.CacheConstants;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.activity.entity.Activity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.PLANNER, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "planner_activity")
public class PlannerActivity extends CompositeStandardEntity<PlannerActivityPK>
        implements SoftBaseAPE<PlannerWithActivity, Activity>, Serializable, ILinkedComposityGrid<PlannerActivityPK> {

    private static final long serialVersionUID = 1L;

    private PlannerActivityPK id;
    private Long plannerId;
    private Long activityId;

    public PlannerActivity() {
    }

    public PlannerActivity(PlannerActivityPK id) {
        this.id = id;
    }

    public PlannerActivity(Long plannerId, Long activityId) {
        this.id = new PlannerActivityPK(plannerId, activityId);
    }

    @Override
    public PlannerActivityPK identifuerValue() {
        return id;
    }

    @EmbeddedId
    @Override
    public PlannerActivityPK getId() {
        return id;
    }

    @Override
    public void setId(PlannerActivityPK id) {
        this.id = id;
    }

    @Basic(optional = false)
    @StrongEntityId
    @Column(name = "planner_id", insertable = false, updatable = false)
    public Long getPlannerId() {
        return plannerId;
    }

    public void setPlannerId(Long auditIndividualId) {
        this.plannerId = auditIndividualId;
    }

    @SoftEntityId
    @Basic(optional = false)
    @Column(name = "activity_id", insertable = false, updatable = false)
    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PlannerActivity other = (PlannerActivity) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.planner.entity.PlannerActivity{" + "id=" + id + '}';
    }

}
