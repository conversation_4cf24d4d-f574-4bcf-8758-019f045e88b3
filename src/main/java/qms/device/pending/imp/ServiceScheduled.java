package qms.device.pending.imp;

import DPMS.Mapping.ScheduledServices;
import DPMS.Mapping.SchedulingApproval;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.DAO.IUntypedDAO;
import ape.mail.dto.MailColumn;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.core.DaemonPending;
import ape.pending.core.INoticeOperation;
import ape.pending.entities.PendingRecord;
import ape.pending.util.ApeConstants;
import java.util.LinkedHashMap;
import qms.device.pending.DevicePendingOperations;
import qms.util.MailUtil;

/**
 *
 * <AUTHOR>
 */
public class ServiceScheduled extends DaemonPending implements INoticeOperation {
    
    private static final String SERVICE_SCHEDULED = ""
            + " FROM " + ScheduledServices.class.getCanonicalName() + " ss "
            + " CROSS JOIN " + User.class.getCanonicalName() + " usr "
            + " CROSS JOIN " + Settings.class.getCanonicalName() + " se "
            + " WHERE ss.responsible_id = usr.id "
            + " AND ss.status = 1 "
            + " AND ss.next_service > " + ApeConstants.CURRENT_DATE_NO_TIME + " + 3 day "
            + " AND ss.device_deleted = 0 "
            + " AND ("
                + " se.schedulingToApprove = 0 "
                + " OR ("
                    + " se.schedulingToApprove = 1 "
                    + " AND ss.id in ("
                        + " SELECT sa.scheduleId"
                        + " FROM " + SchedulingApproval.class.getCanonicalName() + " sa "
                        + " WHERE sa.approvedBy IS NOT NULL"
                        + " AND sa.approvedOn IS NOT NULL"
                        + " AND sa.status =  " + SchedulingApproval.APPROVED + ""
                    + " )"
                + " ) "
            + " ) ";

    public ServiceScheduled(final IUntypedDAO dao) {
        super(dao);
        setBaseAlias(DevicePendingOperations.ALIAS_SCHEDULE_SERVICE);
        setQuery(SERVICE_SCHEDULED);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("usr.id");
        setPendingType(getType(APE.DEVICE_SERVICE_SCHEDULED));
        setModuleKey(DevicePendingOperations.MODULE);
        setBase(ScheduledServices.class);
        setNoticeDate(DevicePendingOperations.FIELD_NOTICE_DATE);
        setCommitment(DevicePendingOperations.FIELD_COMMITMENT);
        setReminder(DevicePendingOperations.FIELD_COMMITMENT);
        setExtraMails("owner_id");
        final LinkedHashMap<String, MailColumn> extraFields = new LinkedHashMap<>(4);
        extraFields.put("device_description", MailUtil.DEFAULT_MAIL_COLUMN);
        extraFields.put("tipoperiodicidad", MailUtil.getLabelMailColumn());
        extraFields.put("responsible", MailUtil.DEFAULT_MAIL_COLUMN);
        extraFields.put("next_service", MailUtil.getAllowedAnticipationMailColumn());
        setExtraFieldsMap(extraFields);
        setOperationType(ApeOperationType.STRONG);
    }
}
