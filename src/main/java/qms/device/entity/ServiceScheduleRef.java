package qms.device.entity;

import Framework.Config.DomainObject;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.device.interfaces.IServiceSchedule;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Immutable
@Table(name = "service_schedule")
public class ServiceScheduleRef extends DomainObject implements Serializable, IServiceSchedule {

    private static final long serialVersionUID = 1L;
    
    private Long responsibleId;
    private Long deviceId;

    public ServiceScheduleRef() {
    }
    
    public ServiceScheduleRef(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "service_schedule_id", precision = 19)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "responsible_id")
    @Override
    public Long getResponsibleId() {
        return responsibleId;
    }

    public void setResponsibleId(Long responsibleId) {
        this.responsibleId = responsibleId;
    }
    
    @Column(name = "device_id")
    @Override
    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

}
