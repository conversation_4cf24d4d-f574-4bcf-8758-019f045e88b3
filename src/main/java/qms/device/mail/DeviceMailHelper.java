package qms.device.mail;

import DPMS.Mapping.Area;
import DPMS.Mapping.Device;
import DPMS.Mapping.DeviceStatus;
import DPMS.Mapping.DeviceType;
import DPMS.Mapping.Service;
import DPMS.Mapping.ServiceSchedule;
import DPMS.Mapping.ServiceType;
import DPMS.Mapping.User;
import Framework.Config.Mail;
import Framework.DAO.IUntypedDAO;
import ape.mail.core.MailHelper;
import bnext.reference.UserRef;
import java.util.HashSet;
import java.util.Set;
import mx.bnext.access.ProfileServices;

/**
 *
 * <AUTHOR>
 */
public final class DeviceMailHelper extends MailHelper {

    public DeviceMailHelper(final IUntypedDAO dao) {
        super(dao);
    }

    public DeviceDTO getDeviceDTO(final Long deviceId) {
        String hql = ""
                + " SELECT new " + DeviceDTO.class.getCanonicalName() + "("
                    + " c.code,"
                    + " c.description,"
                    + " s.description,"
                    + " c.modelBrand,"
                    + " c.serial,"
                    + " c.localization,"
                    + " c.purchaseDate,"
                    + " c.useDate,"
                    + " c.changeDate,"
                    + " c.disposeDate,"
                    + " c.operationRange,"
                    + " c.precision,"
                    + " c.treshold,"
                    + " c.uncertainty,"
                    + " c.calibrationMethod,"
                    + " c.calibrationMethodText,"
                    + " c.disposeComment,"
                    + " c.department.description,"
                    + " c.deviceGroup.description,"
                    + " c.attendant.description,"
                    + " t.description,"
                    + " a.description,"
                    + " COUNT(doc.id),"
                    + " c.responsibleId"
                + ")"
                + " FROM " + Device.class.getCanonicalName() + " c "
                + " , " + DeviceStatus.class.getCanonicalName() + " s"
                + " , " + DeviceType.class.getCanonicalName() + " t "
                + " , " + Area.class.getCanonicalName() + " a "
                + " LEFT JOIN c.documents doc "
                + " WHERE c.id = " + deviceId
                + "   AND c.status =  s.id"
                + "   AND c.deviceTypeId = t.id"
                + "   AND c.areaId = a.id "
                + " GROUP BY "
                + " c.code,"
                + " c.description,"
                + " s.description,"
                + " c.modelBrand,"
                + " c.serial,"
                + " c.localization,"
                + " c.purchaseDate,"
                + " c.useDate,"
                + " c.changeDate,"
                + " c.disposeDate,"
                + " c.operationRange,"
                + " c.precision,"
                + " c.treshold,"
                + " c.uncertainty,"
                + " c.calibrationMethod,"
                + " c.calibrationMethodText,"
                + " c.disposeComment,"
                + " c.department.description,"
                + " c.deviceGroup.description,"
                + " c.attendant.description,"
                + " t.description,"
                + " a.description,"
                + " c.responsibleId";
        return (DeviceDTO) dao.HQL_findSimpleObject(hql);
    }

    public ServiceScheduleDTO getServiceSchedule(final Long serviceScheduleId) {
        final String hql = ""
                + " SELECT new " + ServiceScheduleDTO.class.getCanonicalName() + "("
                    + " d.description,"
                    + " st.description,"
                    + " u.description,"
                    + " ss.nextService "
                + ")"
                + " FROM " + ServiceSchedule.class.getCanonicalName() + " ss "
                + " , " + Device.class.getCanonicalName() + " d "
                + " , " + ServiceType.class.getCanonicalName() + " st "
                + " , " + UserRef.class.getCanonicalName() + " u "
                + "  WHERE ss.deviceId = d.id "
                + "    AND ss.serviceTypeId = st.id "
                + "    AND ss.responsibleId = u.id "
                + "    AND ss.id = " + serviceScheduleId;
        return (ServiceScheduleDTO) dao.HQL_findSimpleObject(hql);
    }

    public ServiceDTO getService(final Long serviceId) {
        String hql = ""
                + " SELECT new " + ServiceDTO.class.getCanonicalName() + "( "
                    + " s.company,"
                    + " s.certificateNumber,"
                    + " s.certificateNumber,"
                    + " s.result.description,"
                    + " s.attendant.description,"
                    + " s.validTo,"
                    + " s.planned,"
                    + " COUNT(doc.id),"
                    + " u.id "
                + " )"
                + " FROM " + Service.class.getCanonicalName() + " s "
                + " , " + ServiceSchedule.class.getCanonicalName() + " ss "
                + " , " + Device.class.getCanonicalName() + " d "
                + " , " + UserRef.class.getCanonicalName() + " u "
                + " LEFT JOIN d.documents doc "
                + " WHERE s.scheduleId = ss.id "
                + "   AND ss.deviceId = d.id"
                + "   AND d.responsibleId = u.id "
                + "   AND s.id = " + serviceId
                + " GROUP BY"
                + " s.company, "
                + " s.certificateNumber,"
                + " s.certificateNumber,"
                + " s.result.description,"
                + " s.attendant.description,"
                + " s.validTo,"
                + " s.planned,"
                + " u.id ";
        return (ServiceDTO) dao.HQL_findSimpleObject(hql);
    }


    public Set<Mail> getUsersWithService(final ProfileServices service) {
        final String hql = ""
                + " SELECT ur "
                + " FROM " + User.class.getCanonicalName() + " u "
                + " , " + UserRef.class.getCanonicalName() + " ur "
                + " JOIN u.puestos p"
                + " JOIN p.perfil perf"
                + " WHERE ur.id = u.id"
                + " AND u.status = " + User.STATUS.ACTIVE.getValue()
                + " AND perf." + ProfileServices.USUARIO_PLANTA.getCode() + " = 1 "        
                + " AND perf." + service.getCode() + " = 1 ";
        final Set<UserRef> users = new HashSet<>(dao.HQL_findByQuery(hql));
        return toMailSet(users);
    }
}
