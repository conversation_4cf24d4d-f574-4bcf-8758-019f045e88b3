package qms.custom.dao;

import Framework.Config.Utilities;
import Framework.DAO.EntityInterceptor;
import Framework.DAO.GenericDAOImpl;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.persistence.Table;
import mx.bnext.qms.configuration.util.DynamicUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.configuration.dto.DynamicFieldHtmlConfig;
import qms.configuration.dto.DynamicFieldsFilesDTO;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.core.DynamicFieldHandler;
import qms.custom.core.DynamicFields;
import qms.custom.core.DynamicTableHelper;
import qms.custom.core.EntityDynamicFields;
import qms.custom.core.ITableFactory;
import qms.custom.core.TableFactoryDynamicFields;
import qms.custom.dto.DynamicFieldColumnDTO;
import qms.custom.dto.DynamicFieldDTO;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicFieldLiteDTO;
import qms.custom.dto.DynamicFieldsDTO;
import qms.custom.dto.DynamicGridConfigSQL;
import qms.custom.dto.DynamicTableBuildDTO;
import qms.custom.dto.IDynamicGridConfigHQL;
import qms.custom.dto.ValidationDTO;
import qms.custom.entity.DynamicField;
import qms.custom.entity.DynamicFieldFkMapping;
import qms.custom.entity.DynamicMetadata;
import qms.framework.util.CacheRegion;
import qms.util.EntityCommon;
import qms.util.GridOrderBy;
import qms.util.QMSException;
import qms.util.Translate;
import qms.util.interfaces.EntityType;
import qms.util.interfaces.IGridFilter;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 */
@Lazy
@Repository(value = "DynamicFieldDAO")
@Scope(value = "singleton")
public class DynamicFieldDAO extends GenericDAOImpl<DynamicField, Long> implements IDynamicFieldDAO {
    
    /**
     * Actualiza la relación de valores dinamicos 
     * guardados con el ID de su referencia en la otra tabla
     * 
     * @param dto
     * @param entity
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateDynReference(DynamicFieldInsertDTO dto, EntityDynamicFields entity) {
        new DynamicTableHelper(this).updateDynReference(dto, entity);
    }
    /**
     * Obtiene un MAP donde la llave es el "code" del campo dinamico y 
     * su valor es el valor correspondiente guardado para la referencia
     * 
     * @param referenceTableId: Es el ID del entity principal (Ej. activityId, documentId)
     * @param customEntity
     * @param dynamicFields
     * @param dynamicTableName
     * @return 
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, String> selectDynReference(
            Long referenceTableId, 
            Class<? extends EntityDynamicFields> customEntity, 
            List<DynamicFieldDTO> dynamicFields, 
            String dynamicTableName
    ) {
        return selectDynReference(referenceTableId, customEntity, dynamicFields, dynamicTableName, null);
    }

    /**
     * @param referenceTableId: Es el ID del entity principal (Ej. activityId, documentId)
     * @param customEntity
     * @param dynamicFields
     * @param dynamicTableName
     * @param dynamicTableNameId: Es el ID del registro en la tabla dinamica (Ej. la columna "id" en la tabla "DYTABLE_#")
     * @return 
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, String> selectDynReference(
            Long referenceTableId,
            Class<? extends EntityDynamicFields> customEntity,
            List<DynamicFieldDTO> dynamicFields,
            String dynamicTableName,
            Long dynamicTableNameId
    ) {
        Map<String, String> result = new LinkedHashMap<>();
        if (dynamicTableName == null || dynamicTableName.isEmpty()) {
            result.put("dynamicTableName", null);
            return result;
        }
        StringBuilder selectStatement = new StringBuilder("SELECT id");
        for (DynamicFieldDTO dynamicField : dynamicFields) {
            selectStatement.append(',').append(dynamicField.getName());
        }
        String referenceTableName = customEntity.getAnnotation(Table.class).name();
        String idName = DynamicTableHelper.getReferenceIdName(this, dynamicTableName, referenceTableName);
        selectStatement
                .append(" FROM ").append(dynamicTableName)
                .append(" WHERE ").append(idName).append(" = :").append(idName);
        final Map<String, Object> params = new HashMap<>(1);
        params.put(idName, referenceTableId);
        if (dynamicTableNameId != null) {
            selectStatement.append(" AND id = :id");
            params.put("id", dynamicTableNameId);
        }
        Object[] data = SQL_findSimpleQuery(selectStatement.toString(), params);
        int n = 1;
        if (data.length == 0) {
            result.put("dynamicTableName", null);
            return result;
        }
        result.put("id", data[0].toString());
        DynamicTableHelper helper = new DynamicTableHelper(this);
        for (DynamicFieldDTO dynamicField : dynamicFields) {
            String value = data[n] != null ? data[n].toString() : null;
            migrateMarkdownFields(dynamicField, helper, value, dynamicTableName, result);
            result.put(dynamicField.getName(), value);
            if (DynamicField.DYNAMIC_FIELD_TYPE.MARKDOWN.getValue().equals(dynamicField.getType())) { // Look for html value
                DynamicFieldHtmlConfig configInfo = new DynamicFieldHtmlConfig(dynamicField.getName(), dynamicTableName, dynamicTableNameId);
                DynamicFieldsFilesDTO dynamicInfo = DynamicUtils.dynamicFieldHtmlValue(configInfo);
                result.put(dynamicField.getName() + "Html", dynamicInfo.getResult());
            }
            if (data.length > n) {
                n++;
            }
        }
        result.put("dynamicTableName", dynamicTableName);
        return result;
    }
    /**
     * Este metodo es para migrar los datos de los campos dinamicos de tipo markdown que ya existente al nuevo aproach que guarda su contraparte en HTML dentro
     * de files, no utilizar en ningun lado, solo en donde esta implementado originalmente.
     */
    private void migrateMarkdownFields(DynamicFieldDTO dynamicField, DynamicTableHelper helper, String value, String dynamicTableName, Map<String, String> result) {
        Gson gson = new Gson();
        if (DynamicField.DYNAMIC_FIELD_TYPE.MARKDOWN.getValue().equals(dynamicField.getType())
                && value != null && value.contains("\"html\":") && value.contains("\"text\":")) {
            Map parsenData = gson.fromJson(value, Map.class);
            final DynamicFieldInsertDTO dynamicFieldInsertDTO = new DynamicFieldInsertDTO(dynamicTableName, Long.valueOf(result.get("id")));
            final ImmutableMap<String, Object> params = ImmutableMap.of(dynamicField.getName() + "Html", parsenData.get("html"));
            helper.saveHtmlFiles(dynamicFieldInsertDTO, params);
            SQL_updateByQuery(""
                    + " UPDATE " + dynamicTableName 
                    + " SET " + dynamicField.getName() + " = :dynamicFieldValue"
                    + " WHERE id = :id", 
                    ImmutableMap.of(
                        "dynamicFieldValue", parsenData.get("text"),
                        "id", result.get("id")
                    ),
                    0,
                    Arrays.asList(dynamicTableName)
            );
        }
    }

    
    private void addLeftDynamicJoins(DynamicGridConfigSQL config) {
        String 
            SQL = config.getSqlFinal().toString(), 
            lowerSql = SQL.toLowerCase(), 
            firstFrom = "from " + config.getMainTableName().toLowerCase() + " "
        ;
        int entityIndex = lowerSql.indexOf(firstFrom);
        int fromIndex = SQL.indexOf(" left outer join");
        if (entityIndex == -1 || entityIndex > fromIndex) {
            firstFrom = "cross join " + config.getMainTableName().toLowerCase() + " ";
            entityIndex = lowerSql.indexOf(firstFrom);
        }
        String fromTableSpaceAlias = SQL.substring(entityIndex, SQL.indexOf(" left outer join"));
        if (firstFrom.contains("cross") && StringUtils.countMatches(fromTableSpaceAlias, "cross") > 1) {
            final Pattern fromAliasPattern = Pattern.compile("(.+?" + config.getMainTableName().toLowerCase() +"[0-9]_).+"); //(.+?activity[0-9]_).+
            fromTableSpaceAlias = fromAliasPattern.matcher(fromTableSpaceAlias).replaceFirst("$1");
        } 
        if(config.getMainTableAliasName() == null) {
            config.setMainTableAliasName(fromTableSpaceAlias.substring(firstFrom.length()).trim());
        }
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                .trace("[{}] Se detecta FROM principal '{}'.", new Object[]{
                    config.getMainTableName(), fromTableSpaceAlias});
       
        int whereIndex = SQL.indexOf(" where");
        if (whereIndex != 1) {
            StringBuilder SQLfinal = new StringBuilder();
            String replaceTable = SQL.substring(0, whereIndex);
            String whereExpression = SQL.substring(whereIndex);

            SQLfinal.append(replaceTable.replace(
                    fromTableSpaceAlias, 
                    fromTableSpaceAlias + " " 
                        + config.getSqlLeftJoin().toString().replaceAll(
                                config.getMainTableAliasNameTemp(), config.getMainTableAliasName()
                        ) + " "
                )).append(" ").append(whereExpression);
            config.setSqlFinal(SQLfinal.toString());
        } else {
            config.setSqlFinal(
                SQL.replace(
                    fromTableSpaceAlias, 
                    fromTableSpaceAlias + " " 
                        + config.getSqlLeftJoin().toString().replaceAll(
                                config.getMainTableAliasNameTemp(), config.getMainTableAliasName()
                        ) + " "
                )
            );
        }
    }
    private void addWhere(DynamicGridConfigSQL config, boolean likeOrSentence) {
        String SQL = config.getSqlFinal().toString();
        StringBuilder WHERE = new StringBuilder();
        if (config.getSqlWhereCondition().isEmpty()) {
            getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                    .trace("[{}] No se injectaron condiciones WHERE.", new Object[]{
                        config.getMainTableName()});
        } else {
            //@ToDo: !!! Mejorar este replace!!, aqui se depende de que exista la condición 0=0 para que funcione, se debe quitar esa dependencia            
            for (final Map.Entry<String, StringBuilder> entry : config.getSqlWhereCondition().entrySet()) {
                final StringBuilder whereCondition = entry.getValue();
                if (likeOrSentence){
                    final String value = StringUtils.substringBetween(whereCondition.toString(), "'%", "%'");
                    final Pattern valuePattern = Pattern.compile(Pattern.quote(" like '%" + value + "%'"), Pattern.CASE_INSENSITIVE);
                    SQL = valuePattern
                            .matcher(SQL)
                            .replaceFirst(" like '%" + value + "%' " + whereCondition);
                } else {
                    whereCondition.delete(0, 4); //<--- borra el primer " OR "
                    WHERE.append(" AND (").append(whereCondition).append(')');
                }
            }
            if (likeOrSentence && WHERE.length() == 0) {
                config.setSqlFinal(SQL.replace("where 0=0 and", " WHERE "));
            } else {
                WHERE.delete(0, 5); //<--- borra el primer " AND "
                config.setSqlFinal(SQL.replace("where 0=0", " WHERE " + WHERE));
            }
            getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                    .trace("[{}] Se injectaron condiciones WHERE despues de 0=0: {}.", new Object[]{
                        config.getMainTableName(), config.getSqlWhereCondition()});
        }
    }
    /**
     * Obtiene la configuracion de LEFT JOIN'S, WHERE y COLUMNAS para filtrar SQL
     * 
     * @param filter
     * @param referenceEntityClassName
     * @param hqlConfig
     * @param sqlConfig 
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setDynamicGridConfigSQL(
            final IGridFilter filter,
            final String referenceEntityClassName, 
            final IDynamicGridConfigHQL hqlConfig,
            final DynamicGridConfigSQL sqlConfig
    ) {
        ValidationDTO v = DynamicFieldHandler.isDynamicSearchValid(referenceEntityClassName);
        if (!v.isValid()) {
            return;
        }
        sqlConfig.setMainTableName(v.getEntityTableName());
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                .trace("[{}] La tabla SQL principal es '{}'.", new Object[]{referenceEntityClassName, sqlConfig.getMainTableName()});
        if (sqlConfig.getMainTableAliasNameTemp() == null) {
            sqlConfig.setMainTableAliasNameTemp("t" + v.getEntityTableName().hashCode());
            getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                    .trace("[{}] Se crea alias temporal para la tabla principal: {}.", new Object[]{referenceEntityClassName, sqlConfig.getMainTableAliasNameTemp()});
        }
        HashMap<String, String> dynamicFieldCriteria = filter.getDynamicFieldCriteria();
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                .trace("[{}] Hay {} valores de busqueda para campos dinamicos.", new Object[]{referenceEntityClassName, dynamicFieldCriteria.size()});
        List<String> aliasesAdded = new ArrayList<>();
        for (Map.Entry<String, String> entry : dynamicFieldCriteria.entrySet()) {
            String dynamicFieldCode = entry.getKey();
            String dynamicFieldFilterValue = entry.getValue();
            setDynamicGridConfigSQL(
                    sqlConfig.getMainTableName(),
                    dynamicFieldCode, 
                    dynamicFieldFilterValue,
                    true, 
                    sqlConfig, 
                    aliasesAdded, 
                    referenceEntityClassName
            );
        }
        addLeftDynamicSelect(sqlConfig, dynamicFieldCriteria, referenceEntityClassName, aliasesAdded);
        addLeftDynamicJoins(sqlConfig);
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                .trace("[{}] Se injectaron LEFT JOINS despues del FROM.", new Object[]{
                    sqlConfig.getMainTableName()});
        if (!dynamicFieldCriteria.isEmpty()) {
            addWhere(sqlConfig, filter.getLikeOrSentence());
        }
        setColumnNames(hqlConfig, sqlConfig);
        setDynamicColumns(sqlConfig);
        if (GenericDAOImpl.IS_SQL_SERVER && !sqlConfig.getSqlFinal().toString().contains(" collate ") ) {
            sqlConfig.setSqlFinal(
                EntityInterceptor.COLLATION_PATTERN.matcher(sqlConfig.getSqlFinal().toString()).replaceAll(EntityInterceptor.COLLATION_REPL)
            );
        }
        appendDynamicFieldOrder(filter, sqlConfig);
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                .trace("[{}] Termina configuración de SQL.", new Object[]{
                    sqlConfig.getMainTableName()});
    }
    
    private void appendDynamicFieldOrder(final IGridFilter filter, final DynamicGridConfigSQL sqlConfig) {
        final Integer startRemove = sqlConfig.getSqlFinal().lastIndexOf(" order by ") + 10;
        final Integer endRemove = sqlConfig.getSqlFinal().length();
        final String subOrder = sqlConfig.getSqlFinal().substring(startRemove, endRemove);
        sqlConfig.getSqlFinal().delete(startRemove, endRemove);
        final List<String> orderParts = new ArrayList<>(Arrays.asList(subOrder.split(",")));
        setDynamicFieldOrder(filter.getField(), filter, sqlConfig, orderParts);
        sqlConfig.getSqlFinal().append(StringUtils.join(orderParts, ", "));
    }
    
    private void setDynamicFieldOrder(
            final GridOrderBy orderField, 
            final IGridFilter filter,
            final DynamicGridConfigSQL sqlConfig,
            final List<String> orderParts
    ) {
        if (GridOrderBy.TYPE_SQL.equals(orderField.getType())) {
            String order = " DESC ";
            String field = orderField.getOrderBy().replaceAll("^c\\.", "");
            if (sqlConfig.isDynamicFieldNameValid(field)) {
                switch (orderField.getDirection()) {
                    case 1:
                        order = " ASC";
                        break;
                    case 2:
                        order = " DESC";
                        break;
                }
                final Integer indexField = getDynamicFieldOrderIndex(orderParts, field, order);
                final String dynamicOrder = sqlConfig.getCommaSeparatedDynamicField(field).replaceAll(",", " " + order + ",") + order;
                if (indexField != -1) {
                    orderParts.set(indexField, dynamicOrder);
                } else {
                    orderParts.add(dynamicOrder);
                }
            } else {
                getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                    .error("[{}] Se intentó ordenar por una columna invalida : '{}'.", new Object[]{
                        sqlConfig.getMainTableName(), field});
            }
        }
        if (orderField.getOrderByMany() != null && !orderField.getOrderByMany().isEmpty()) {
            orderField.getOrderByMany().stream().forEach((orderByItem) -> 
                    setDynamicFieldOrder(orderByItem, filter, sqlConfig, orderParts)
            );
        }
    }

    private Integer getDynamicFieldOrderIndex(final List<String> orderParts, String field, String order) {
        final String partialName = field + order;
        if (orderParts.indexOf(partialName) != -1) {
            return orderParts.indexOf(partialName);
        } else if (orderParts.indexOf(" " + partialName) != -1) {
            return orderParts.indexOf(" " + partialName);
        } else if (orderParts.indexOf(partialName+ " ") != -1) {
            return orderParts.indexOf(partialName+ " ");
        } else if (orderParts.indexOf(" " + partialName + " ") != -1) {
            return orderParts.indexOf(" " + partialName + " ");
        } else {
            return -1;
        }
    }
    
    private void setDynamicColumns(DynamicGridConfigSQL sqlConfig) {
        String SQL = sqlConfig.getSqlFinal().toString();
        int whereIndex = SQL.toUpperCase().indexOf(" WHERE");
        if (whereIndex != -1) {
            StringBuilder SQLfinal = new StringBuilder();
            String replaceTable = SQL.substring(0, whereIndex);
            String whereExpression = SQL.substring(whereIndex);

            SQLfinal.append(replaceTable.replace(" from ", sqlConfig.getSqlDynSelect() + " from ")).append(" ").append(whereExpression);
            sqlConfig.setSqlFinal(SQLfinal.toString());
        } else {
            sqlConfig.setSqlFinal(SQL.replace(" from ", sqlConfig.getSqlDynSelect() + " from "));
        }
    }
   
    private void addLeftDynamicSelect(
            DynamicGridConfigSQL sqlConfig, 
            HashMap<String, String> dynamicFieldCriteria,
            String referenceEntityClassName, 
            List<String> aliasesAdded
    ) {
        final DynamicFieldsDTO dynamicFields = getDynamicFieldsByTableName(
                null,
                referenceEntityClassName, 
                true,
                false,
                null,
                new HashMap<>()
        );
        if (dynamicFields.isValid()) {
            for (DynamicFieldDTO dynamicFieldDTO : dynamicFields.getDynamicFields()) {
                if (dynamicFieldCriteria.containsKey(dynamicFieldDTO.getName())) {
                    continue;
                }
                setDynamicGridConfigSQL(sqlConfig.getMainTableName(), dynamicFieldDTO.getName(), Utilities.EMPTY_STRING, false, sqlConfig, aliasesAdded, referenceEntityClassName);
            }
        }
    }

    private void setColumnNames(IDynamicGridConfigHQL hqlConfig, DynamicGridConfigSQL sqlConfig) {
        String SQL = sqlConfig.getSqlFinal().toString();
        for (Map.Entry<String, String> entry : hqlConfig.getColumnNames().entrySet()) {
            String aliasName = entry.getKey();
            String colNumberName = entry.getValue();
            SQL = SQL.replace(" as " + colNumberName, " as " + aliasName);
        }
        sqlConfig.setSqlFinal(SQL);
    }
	
    private void setDynamicGridConfigSQL(
            String entityTableName,
            String dynamicFieldCode,
            String dynamicFieldFilterValue,
            boolean addFilter,
            DynamicGridConfigSQL sqlConfig,
            List<String> aliasesAdded,
            String referenceEntityClassName
    ) {
        
        List<Map<String,Object>> mr = HQL_selectMapQuery(""
                + " SELECT new map("
                    + " c.dynamicFieldTableName as tableName,"
                    + " d.description as idColumName,"
                    + " d.referenceIdName as referenceIdName"
                + " ) "
                + " FROM " + DynamicMetadata.class.getCanonicalName() + " c"
                + " JOIN " + DynamicFieldFkMapping.class.getCanonicalName() + " d"
                    + " ON c.dynamicFieldTableId = d.dynamicFieldTableId"
                + " JOIN " + referenceEntityClassName + " ce"
                    + " ON ce.dynamicTableName = c.dynamicFieldTableName"
                + " WHERE "
                    + " c.dynamicFieldCode = ?0"
                    + " AND d.referenceTableName = ?1"
                + " GROUP BY "
                    + " c.dynamicFieldTableName,"
                    + " d.description,"
                    + " d.referenceIdName",
                true,
                CacheRegion.DYNAMIC_FIELD,
                0,
                dynamicFieldCode, 
                entityTableName
        );
        if (getLogger(GenericDAOImpl.LOGGER.DynamicSearch).isTraceEnabled()) {
            getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                    .trace("[{}] Se busca el campo de busqueda '{}' por el valor '{}'.", new Object[]{
                entityTableName, dynamicFieldCode, dynamicFieldFilterValue});
            getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                    .trace("[{}] Hay {} tabla(s) de campos dinamicos relacionadas a la busqueda.", new Object[]{
                    entityTableName, mr.size()});
        }
        String[] multipleSearch;
        String whereKey, tableName, aliasTable, idColumnName, aliasColumn, referenceIdName;
        boolean addTableAlias, addColumnAlias;
        for (Map<String, Object> ma : mr) {
            tableName = ma.get("tableName").toString();
            aliasTable = tableName.replaceAll("_", "");
            idColumnName = ma.get("idColumName").toString();
            addTableAlias = !aliasesAdded.contains(aliasTable);
            referenceIdName = ma.get("referenceIdName").toString();
            if (addTableAlias) {
                aliasesAdded.add(aliasTable);
            }
            if (addTableAlias) {
                if (getLogger(GenericDAOImpl.LOGGER.DynamicSearch).isTraceEnabled()) {
                    getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                        .trace("[{}] LEFT JOIN -> Tabla '{}', Alias '{}', columna join '{}'.", new Object[]{
                                entityTableName, tableName, aliasTable, idColumnName});
                }
                sqlConfig.getSqlLeftJoin()
                        .append(" LEFT JOIN ")
                            .append(tableName)
                        .append(" ")
                            .append(aliasTable)
                        .append(" ON ")
                            .append(aliasTable)
                        .append('.')
                            .append(idColumnName)
                        .append(" = ")
                        .append(sqlConfig.getMainTableAliasNameTemp())
                        .append(".").append(referenceIdName)
                        ;
            }
            aliasColumn = dynamicFieldCode + "_" + aliasTable;
            addColumnAlias = !aliasesAdded.contains(aliasColumn);
            if (addColumnAlias) {
                aliasesAdded.add(aliasColumn);
            }
            if (addColumnAlias) {
                sqlConfig.getSqlDynSelect()
                        .append(",")
                        .append(aliasTable)
                    .append('.')
                    .append(dynamicFieldCode)
                    .append(" as ")
                        .append(aliasColumn)
                    ;
                sqlConfig.saveDynamicFieldAliasTableMap(dynamicFieldCode, aliasTable);
                sqlConfig.addColumn(aliasTable, dynamicFieldCode);
            }
            if (addFilter) {
                multipleSearch = dynamicFieldFilterValue.replaceAll("'", "''").split(",");
                for (String value : multipleSearch) {
                    whereKey = dynamicFieldCode;// + "@" + value;
                    if (!sqlConfig.getSqlWhereCondition().containsKey(whereKey)) {
                        sqlConfig.getSqlWhereCondition().put(whereKey, new StringBuilder(50));
                    }
                    sqlConfig.getSqlWhereCondition().get(whereKey)
                            .append(" OR ")
                            .append(aliasTable)
                            .append('.')
                            .append(dynamicFieldCode)
                            .append(" like '%")
                            .append(value.replaceAll("\\?", "_").replaceAll("\\*", ""))
                            .append("%' ")
                            ;
                }
            }
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO getDynamicFields(
            String dynamicEntity,
            String extraCondition, 
            String customEntity,
            Map<String, Object> params,
            Long loggedUserId,
            Long entityTypeId
    ) throws QMSException {
        return generateDynamicFieldsByTypeId(
                dynamicEntity, 
                extraCondition,
                customEntity,
                params, 
                loggedUserId, 
                entityTypeId
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO getDynamicFields(
            String dynamicEntity,
            String extraCondition,
            String customEntity,
            Map<String, Object> params,
            Long loggedUserId,
            List<Long> typeIds
    ) {
        // filtrado de nulos
        List<Long> entityTypeIds = (typeIds != null && !typeIds.isEmpty()) ? typeIds.stream().filter(s -> (s != null)).collect(Collectors.toList()) : new ArrayList<>();
        Set<DynamicFieldDTO> configs = entityTypeIds.stream()
                .map((field) -> {
                    try {
                        return generateDynamicFieldsByTypeId(
                                dynamicEntity,
                                extraCondition,
                                customEntity,
                                params,
                                loggedUserId,
                                field
                        ).getDynamicFields();
                    } catch (QMSException ex) {
                        throw new RuntimeException(ex);
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toSet());
        final DynamicFieldsDTO fields = new DynamicFieldsDTO(true);
        fields.setDynamicFields(new ArrayList<>(configs));
        return fields;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO getDynamicFields(
            String dynamicEntity, String entityTypeCode, String customEntity, Long loggedUserId
    )  throws QMSException {
        final Long entityTypeId = findTypeByCode(loggedUserId, dynamicEntity);
        return generateDynamicFieldsByTypeId(dynamicEntity, null, customEntity, new HashMap<>(), loggedUserId, entityTypeId);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO generateDatabaseObjects(
            Long entityTypeId,
            String dynamicEntity,
            String customEntity, 
            Long loggedUserId
    ) throws QMSException {
        return generateDynamicFieldsByTypeId(
                dynamicEntity, 
                null,
                customEntity,
                new HashMap<>(), 
                loggedUserId, 
                entityTypeId
        );
    }

    /**
     *
     * @param dynamicEntity <-- Debe implementar `IPersistDynamicViewAndTable`
     * @param extraCondition
     * @param customEntity
     * @param params
     * @param loggedUserId
     * @param entityTypeId
     * @return
     * @throws QMSException
     */
    private DynamicFieldsDTO generateDynamicFieldsByTypeId(
            final String dynamicEntity, 
            final String extraCondition,
            final String customEntity,
            final Map<String, Object> params,
            final Long loggedUserId,
            final Long entityTypeId
    )  throws QMSException {
        ValidationDTO v = DynamicFieldHandler.isDynamicEntityValid(dynamicEntity);
        DynamicFieldsDTO r = new DynamicFieldsDTO(false);
        if (!v.isValid()) {
            return r;
        }
        Class c = v.getEntityClass();
        DynamicFields a = (DynamicFields) c.getAnnotation(DynamicFields.class);
        StringBuilder hql = new StringBuilder(250);
        r.setValid(true);
        hql.append(" "
            + " SELECT new ").append(DynamicFieldDTO.class.getCanonicalName()).append("("
                + " d.id "
                + ",d.code "
                + ",d.description "
                + ",d.status "
                + ",d.mandatory "
                + ",d.searchAlwaysVisible "
                + ",d.dynamicFieldType"
                + ",max(m.description)"
            + " )"
            + " FROM ").append(a.compositeEntityClass().getCanonicalName()).append(" c");
        if (extraCondition != null && !extraCondition.isEmpty()) {
             hql.append(""
                + ",").append(v.getEntityClass().getCanonicalName()).append(" t ");
        }
        hql.append(""
           + ",").append(DynamicField.class.getCanonicalName()).append(" d ").append(""
           + " LEFT JOIN ").append(DynamicMetadata.class.getCanonicalName()).append(" m ON m.dynamicFieldId = d.id ");
        params.put("status", EntityCommon.getActiveStatus(DynamicField.class).getValue());
        hql.append(""
            + " WHERE"
                + " d.id = c.id.").append(a.dynamicFieldMappedBy()).append(""
                + " AND d.status = :status");
        if (extraCondition != null && !extraCondition.isEmpty()) {
            hql.append(""
                + " AND c.id.").append(a.entityMappedBy()).append(" = t.id");
        }
        if (entityTypeId != null && entityTypeId > 0) {
            hql.append(""
                + " AND c.id.").append(a.entityMappedBy()).append(" = :entityTypeId");
            params.put("entityTypeId", entityTypeId);
        }
        if (extraCondition != null && !extraCondition.isEmpty()) {
            hql.append(" AND (")
                .append(extraCondition)
                .append(" )");
        }
        hql.append(" GROUP BY ").append(" "
            + " d.id "
            + ",d.code "
            + ",d.description "
            + ",d.status "
            + ",d.mandatory "
            + ",d.searchAlwaysVisible "
            + ",d.dynamicFieldType"
        );
        r.setDynamicFields(HQL_findByQuery(hql.toString(), params, true, CacheRegion.DYNAMIC_FIELD, 0));
        generateDatabaseObjects(entityTypeId, dynamicEntity, r, a, customEntity, loggedUserId); 
        setDynamicFieldOptions(r);
        return r;
    }
    
    private Boolean existsView(Long entityTypeId, String dynamicEntity) {
        final String dynamicFieldViewName = findViewByType(entityTypeId, dynamicEntity);
        final Boolean existInType = dynamicFieldViewName != null && !dynamicFieldViewName.isEmpty();
        if (!existInType) {
            return false;
        }
        final Boolean existsDb = getBean(IDynamicTableDAO.class).existsDatabaseObject("dbo", dynamicFieldViewName, null);
        return existsDb;
    }
    
    private String findViewByType(Long entityTypeId, String dynamicEntity) {
        final String dynamicFieldViewName = HQL_findSimpleString(""
                + " SELECT c.dynamicFieldViewName"
                + " FROM " + dynamicEntity + " c"
                + " WHERE c.id = :entityTypeId", 
                ImmutableMap.of("entityTypeId", entityTypeId),
                true,
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        return dynamicFieldViewName;
    }
    
    private Long findTypeByCode(Long entityTypeCode, String dynamicEntity) {
        final Long entityTypeId = HQL_findLong(""
                + " SELECT c.id"
                + " FROM " + dynamicEntity + " c"
                + " WHERE c.code = :code",
                ImmutableMap.of("code", entityTypeCode),
                true,
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        return entityTypeId;
    }

    /**
     *
     * @param entityTypeId
     * @param dynamicEntity <-- Debe implementar `IPersistDynamicViewAndTable`
     * @param dto
     * @param a
     * @param customEntity
     * @param loggedUserId
     * @throws QMSException
     */
    private void generateDatabaseObjects(
            Long entityTypeId,
            String dynamicEntity,
            DynamicFieldsDTO dto,
            DynamicFields a,
            String customEntity,
            Long loggedUserId
    )  throws QMSException {
        if (dto.getDynamicFields().isEmpty()) {
            return;
        }
        final ITableFactory factory = new TableFactoryDynamicFields(dynamicEntity, dto.getDynamicFields());
        final DynamicTableBuildDTO builtTable = getBean(IDynamicTableDAO.class).getFactoryBuildedName(factory, loggedUserId);
        dto.setDynamicTableName(builtTable.getTableName());
        if (customEntity != null && a.createViewPreffix() != null && !a.createViewPreffix().isEmpty() && entityTypeId != null && entityTypeId > 0) {
            final boolean buildView = Boolean.TRUE.equals(builtTable.getNewTable()) || Boolean.TRUE.equals(existsView(entityTypeId, dynamicEntity));
            if (buildView) {
                final String viewName = buildDynamicView(factory, entityTypeId, a, customEntity);
                persistDynamicViewAndTable(
                    builtTable.getTableName(),
                    viewName,
                    entityTypeId,
                    dynamicEntity,
                    loggedUserId
                );
            } else {
                refreshDynamicTableName(entityTypeId, dynamicEntity, builtTable.getTableName(), loggedUserId);
            }
        }
    }
    
    private int refreshDynamicTableName(
        Long entityTypeId,
        String dynamicEntity,
        String tableName,
        Long loggedUserId
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("entityTypeId", entityTypeId);
        params.put("tableName", tableName);
        params.put("lastModifiedBy", loggedUserId);
        params.put("lastModifiedDate", new Date());
        return HQL_updateByQuery(" "
                        + " UPDATE " + dynamicEntity + " c"
                        + " SET"
                        + " c.dynamicFieldTableName = :tableName,"
                        + " c.lastModifiedBy = :lastModifiedBy,"
                        + " c.lastModifiedDate = :lastModifiedDate"
                        + " WHERE c.id = :entityTypeId", params,
                true, CacheRegion.DYNAMIC_FIELD,
                0
        );
    }

    private void setDynamicFieldOptions(DynamicFieldsDTO dto) {
        if (!dto.getDynamicFields().isEmpty()) {
            dto.getDynamicFields().forEach((field) -> {
                switch(field.getType()) {
                    case "catalog":
                    case "catalog-multiple":
                        final List<String> options = DynamicFieldHandler.getDynamicFieldValues(this, field.getId());
                        field.setOptions(options.toArray(new String[0]));
                }
            });
            final List<DynamicFieldDTO> fields = dto.getDynamicFields().stream()
                    .filter(field -> !field.getIsSystem() && !field.getName().endsWith("Html"))
                    .collect(Collectors.toList());
            dto.setDynamicFields(fields);
        }         
    }
    
    private String buildViewSelect(
            Long entityTypeId,
            String entityTableName,
            DynamicFields a,
            String customEntity
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("entityTypeId", entityTypeId);
        params.put("entityTableName", entityTableName);
        final List<DynamicFieldColumnDTO> customConfigs = HQL_findByQuery(""
                + " SELECT new " + DynamicFieldColumnDTO.class.getCanonicalName() + "("
                    + " c.dynamicFieldTableName as tableName,"
                    + " c.dynamicFieldCode as columnName,"
                    + " d.description as referenceIdName"
                + " ) "
                + " FROM " + DynamicMetadata.class.getCanonicalName() + " c"
                + " JOIN " + DynamicFieldFkMapping.class.getCanonicalName() + " d"
                    + " ON c.dynamicFieldTableId = d.dynamicFieldTableId"
                + " JOIN " + customEntity + " ce"
                    + " ON ce.dynamicTableName = c.dynamicFieldTableName"
                + " WHERE "
                    + " ce.typeId = :entityTypeId"
                    + " AND d.referenceTableName = :entityTableName"
                + " GROUP BY "
                    + " c.dynamicFieldTableName,"
                    + " c.dynamicFieldCode,"
                    + " d.description",
                params,
                true,
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        final List<DynamicFieldColumnDTO> entityConfigs = HQL_findByQuery(""
                + " SELECT new " + DynamicFieldColumnDTO.class.getCanonicalName() + "("
                    + " c.dynamicFieldTableName as tableName,"
                    + " c.dynamicFieldCode as columnName,"
                    + " d.description as referenceIdName"
                + " ) "
                + " FROM " + DynamicMetadata.class.getCanonicalName() + " c"
                + " JOIN " + DynamicFieldFkMapping.class.getCanonicalName() + " d"
                    + " ON c.dynamicFieldTableId = d.dynamicFieldTableId"
                + " JOIN " + a.compositeEntityClass().getCanonicalName() + " de"
                    + " ON de.id.dynamicFieldId = c.id"
                + " WHERE "
                    + " de.id." + a.entityMappedBy() + " = :entityTypeId"
                    + " AND d.referenceTableName = :entityTableName"
                + " GROUP BY "
                    + " c.dynamicFieldTableName,"
                    + " c.dynamicFieldCode,"
                    + " d.description",
                params,
                true, 
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        final List<DynamicFieldColumnDTO> configs = new ArrayList<>(customConfigs);
        configs.addAll(entityConfigs);
        if (configs.isEmpty()) {
            return null;
        }
        final Set<String> allColumns = configs.stream().map((config) -> config.getDynamicFieldCode()).collect(Collectors.toSet());
        final Set<String> referenceIdNames = configs.stream().map((config) -> config.getReferenceIdName()).collect(Collectors.toSet());
        allColumns.addAll(referenceIdNames);
        final Set<String> tables = configs.stream().map((config) -> config.getDynamicFieldTableName()).collect(Collectors.toSet());
        final Map<String, List<String>> columnsByTable = configs.stream().collect(
                Collectors.groupingBy(
                        DynamicFieldColumnDTO::getDynamicFieldTableName,
                        Collectors.mapping(DynamicFieldColumnDTO::getDynamicFieldCode, Collectors.toList())
                )
        );
        final String viewSql = tables.stream().map((table) -> {
            final List<String> columns = columnsByTable.get(table);
            final String select = allColumns.stream().map((item) -> {
                if (columns.contains(item) || referenceIdNames.contains(item)) {
                    return item + " AS " + item + " \r\n   ";
                } else {
                    return "null AS " + item + " \r\n   ";
                }
            }).collect(Collectors.joining(", "));
            return " SELECT \r\n   " + select + " FROM " + table + " c ";
        }).collect(Collectors.joining("\r\n\r\n UNION ALL \r\n\r\n"));
        return viewSql;
    }
    
    private String buildDynamicView(
            final ITableFactory factory, 
            final Long entityTypeId,
            final DynamicFields a,
            final String customEntity
    ) {
        final ValidationDTO v = DynamicFieldHandler.isDynamicSearchValid(customEntity);
        if (!v.isValid()) {
            getLogger().error("Invalid configuration to create type dynamic fields view for type {}.", new Object[] {
                entityTypeId
            });
            return null;
        }
        final String viewSql = buildViewSelect(entityTypeId, v.getEntityTableName(), a, customEntity); 
        if (viewSql == null) {
            return null;
        }
        final String viewName = a.createViewPreffix() + "_" + entityTypeId;
        getBean(IDynamicTableDAO.class).createDbView(factory, "dbo", viewName, viewSql);
        return viewName;
    }

    /**
     * Persiste el nombre de la vista y la tabla en la entidad dinamica.
     *
     * @see qms.custom.core.IPersistDynamicViewAndTable
     *
     * @param tableName
     * @param viewName
     * @param entityTypeId
     * @param dynamicEntity -> Debe implementar `IPersistDynamicViewAndTable`
     * @param loggedUserId
     * @return
     */
    private Integer persistDynamicViewAndTable(
            String tableName,
            String viewName,
            Long entityTypeId, String dynamicEntity, Long loggedUserId
    ) {
        final String oldViewName = findViewByType(entityTypeId, dynamicEntity);
        if (Objects.equals(oldViewName, viewName)) {
            return 0;
        }
        final Map<String, Object> params = new HashMap<>();
        params.put("entityTypeId", entityTypeId);
        params.put("viewName", viewName);
        params.put("tableName", tableName);
        params.put("lastModifiedBy", loggedUserId);
        params.put("lastModifiedDate", new Date());
        final Integer updated = HQL_updateByQuery(""
                + " UPDATE " + dynamicEntity + " c"
                + " SET"
                    + " c.dynamicFieldViewName = :viewName,"
                    + " c.dynamicFieldTableName = :tableName,"
                    + " c.lastModifiedBy = :lastModifiedBy,"
                    + " c.lastModifiedDate = :lastModifiedDate"
                + " WHERE c.id = :entityTypeId", params,
                true,
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        return updated;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IDynamicGridConfigHQL getDynamicGridConfig(Class clazz) {                
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch).trace("[{}] Obteniendo configuración HQL del entity.", clazz.getCanonicalName());
        IDynamicGridConfigHQL config = Translate.getGridConfig(clazz, "c", this);
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                .trace("[{}] SELECT HQL: {}", new Object[]{clazz.getCanonicalName(), config.getHqlSelect()});
        return config;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IDynamicGridConfigHQL getDynamicGridConfig(Class entityClass, String entityAlias, String HQL) {
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch).trace("[{}] Obteniendo configuración HQL del entity.", entityClass.getCanonicalName());
        IDynamicGridConfigHQL config = Translate.getGridConfig(HQL, entityClass, entityAlias, false, this);
        getLogger(GenericDAOImpl.LOGGER.DynamicSearch)
                .trace("[{}] SELECT HQL: {}", new Object[]{entityClass.getCanonicalName(), config.getHqlSelect()});
        return config;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName,
            String customEntity, 
            Boolean searchAlwaysVisible,
            Boolean loadOptions,
            String extraCondition,
            Map<String, Object> params
    ) {
        return getDynamicFieldsByTableName(dynamicTableName, null, customEntity, searchAlwaysVisible, loadOptions, extraCondition, params, null);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName,
            Class<? extends EntityType> dynamicEntity, 
            Class<? extends EntityDynamicFields> customEntity,
            Boolean searchAlwaysVisible,
            String extraCondition,
            Map<String, Object> params,
            Long loggedUserId
    ) {
        return getDynamicFieldsByTableName(
                dynamicTableName,
                dynamicEntity.getCanonicalName(),
                customEntity.getCanonicalName(),
                searchAlwaysVisible,
                dynamicEntity.getCanonicalName() != null,
                extraCondition,
                params,
                loggedUserId
        );
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName,
            String dynamicEntity,
            String customEntity,
            Boolean searchAlwaysVisible,
            Boolean loadOptions,
            String extraCondition,
            Map<String, Object> params,
            Long loggedUserId
    ) {
        DynamicFieldsDTO r = new DynamicFieldsDTO(false);
        r.setValid(true);
        if (dynamicTableName != null) {
            params.put("tableName", dynamicTableName);
        }
        params.put("status", EntityCommon.getActiveStatus(DynamicField.class).getValue());
        final StringBuilder hql = new StringBuilder();
        hql.append(""
                + " SELECT new ").append(DynamicFieldDTO.class.getCanonicalName()).append("("
                    + " d.id "
                    + ",d.code "
                    + ",d.description "
                    + ",d.status "
                    + ",d.mandatory "
                    + ",d.searchAlwaysVisible "
                    + ",d.dynamicFieldType"
                    + ",max(m.description)"
                + " )"
                + " FROM ").append(DynamicMetadata.class.getCanonicalName()).append(" m "
                + " JOIN m.dynamicField d "
                + " WHERE m.status = :status")
                .append(
                    dynamicTableName != null ? " AND m.dynamicFieldTableName = :tableName" : ""
                )
                .append(
                    searchAlwaysVisible ? " AND d.searchAlwaysVisible = 1" : ""
                );
        if (customEntity != null && dynamicTableName == null) {
            hql.append(" AND EXISTS (");
            hql.append(" SELECT 1")
                .append(" FROM ").append(customEntity).append(" ce");
            if (dynamicEntity != null) {
                hql.append(" JOIN ").append(dynamicEntity).append(" t")
                        .append(" ON t.id = ce.typeId");                
            }
            hql.append(" WHERE ce.dynamicTableName = m.dynamicFieldTableName");
            if (extraCondition != null) {
                hql.append(" AND (").append(extraCondition).append(" )");
            }
            hql.append(" )");
        }
        hql.append(" GROUP BY "
            + " d.id "
            + ",d.code "
            + ",d.description "
            + ",d.status "
            + ",d.mandatory "
            + ",d.searchAlwaysVisible "
            + ",d.dynamicFieldType"
        );
              
        final List<DynamicFieldDTO> fields = HQL_findByQuery(hql.toString(), 
                params, 
                true,
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        r.setDynamicFields(fields);
        r.setDynamicTableName(dynamicTableName);
        if (loadOptions) {
            setDynamicFieldOptions(r);
        }
        return r;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldsDTO getAllValues(Boolean loadOptions) {
        DynamicFieldsDTO r = new DynamicFieldsDTO(false);
        r.setValid(true);
        final StringBuilder hql = new StringBuilder();
        hql.append(""
            + " SELECT new ").append(DynamicFieldDTO.class.getCanonicalName()).append("("
                + " d.id "
                + ",d.code "
                + ",d.description "
                + ",d.status "
                + ",d.mandatory "
                + ",d.searchAlwaysVisible "
                + ",d.dynamicFieldType "
                + ",max(m.description)"
            + " )"
            + " FROM ").append(DynamicField.class.getCanonicalName()).append(" d"
            + " LEFT JOIN ").append(DynamicMetadata.class.getCanonicalName()).append(" m ON m.dynamicFieldId = d.id ").append(""
            + " WHERE "
                + " d.status = :status"
            + " GROUP BY "
                + " d.id "
                + ",d.code "
                + ",d.description "
                + ",d.status "
                + ",d.mandatory "
                + ",d.searchAlwaysVisible "
                + ",d.dynamicFieldType"
        );
        final List<DynamicFieldDTO> fields = HQL_findByQuery(hql.toString(), 
                ImmutableMap.of(
                    "status",
                    EntityCommon.getActiveStatus(DynamicField.class).getValue()
                ),
                true, 
                CacheRegion.DYNAMIC_FIELD,
                0
        );
        r.setDynamicFields(fields);
        if (loadOptions) {
            setDynamicFieldOptions(r);
        }
        return r;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<DynamicFieldLiteDTO> getActivesLite() {
        return HQL_findByQuery(""
            + " SELECT new " + DynamicFieldLiteDTO.class.getCanonicalName() + "("
                + " c.id,"
                + " c.code,"
                + " c.description,"
                + " c.dynamicFieldType"
            + " )"
            + " FROM " + DynamicField.class.getCanonicalName() + " c"
            + " WHERE"
                + " (c.deleted IS NULL OR c.deleted = 0)"
                + " AND c.status = " + DynamicField.STATUS.ACTIVE.getValue(),
            Utilities.EMPTY_MAP,
            true,
            CacheRegion.DYNAMIC_FIELD,
            0
        );
    }

}
