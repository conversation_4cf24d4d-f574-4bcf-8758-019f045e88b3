package qms.custom.dao;

import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sun.star.auth.InvalidArgumentException;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.core.DynamicTableHelper;
import qms.custom.core.DynamicTableUtil;
import qms.custom.core.IDynamicTableField;
import qms.custom.core.ITableFactory;
import qms.custom.core.IdFactory;
import qms.custom.dto.DynamicTableBuildDTO;
import qms.custom.dto.TableReferenceIdDTO;
import qms.custom.entity.DynamicField;
import qms.custom.entity.DynamicFieldFkMapping;
import qms.custom.entity.DynamicFieldTable;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.MigrationColumnsDto;
import qms.form.entity.AnswerPartType;
import qms.framework.util.CacheRegion;
import qms.framework.util.ConectionUtil;
import qms.util.MetadataSqlException;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Lazy
@Repository(value = "DynamicTableDAO")
@Scope(value = "singleton")
public class DynamicTableDAO<FieldType, FieldDTO extends IDynamicTableField<FieldType>> extends GenericDAOImpl<DynamicField, Long> implements IDynamicTableDAO<FieldType, FieldDTO> {

    private static final String TABLE_DESC = ":tableDesc";
    private static final String FULL_TABLE_DESC = ":fullTableDesc";
    private static final String IDX_REFERENCE = ":idxRefernce";
    private static final String FIELDS = ":fields";
    private static final String ID_NAME = ":idName";
    private static final String NONCLUSTERED_IDX = " "
            + " CREATE NONCLUSTERED INDEX idx_" + TABLE_DESC + "_" + IDX_REFERENCE 
            + " ON " + FULL_TABLE_DESC + " (" + ID_NAME + " ASC); ";
    private static final String NONCLUSTERED_INCLUDE_ALL_IDX = " "
            + " CREATE NONCLUSTERED INDEX idx_" + TABLE_DESC + "_" + IDX_REFERENCE + "_all"
            + " ON " + FULL_TABLE_DESC + " (" + ID_NAME + " ASC) INCLUDE(" + FIELDS + "); ";
    private static final Integer MAX_TABLE_FIELDS = 1024;
    private static final Integer DEFAULT_TABLE_FIELDS = 5;


    private DynamicFieldTable getInstance(final ITableFactory<FieldType, FieldDTO> factory, final Long loggedUserId) throws QMSException {
        final String md5 = factory.getMd5();
        final String dynamicEntity = factory.getEntityName();
        DynamicFieldTable table = new DynamicFieldTable();
        table.setCode(md5);
        table.setOwnerClassName(dynamicEntity);
        table.setStatus(DynamicFieldTable.STATUS.ACTIVE.getValue());
        table.setDescription(factory.getInstanceTableName());
        if (factory.getInstanceSchema() == null || !factory.getInstanceSchema().isEmpty()) {
            table.setDynamicSchema("dbo");
        } else {
            table.setDynamicSchema(factory.getInstanceSchema());
        }
        return makePersistent(table, loggedUserId);
    }

    private void checkForSchema(ITableFactory<FieldType, FieldDTO> factory) throws QMSException {
        final boolean schemaAvailable = SQL_findSimpleInteger(" "
            + " SELECT COUNT(*) FROM sys.schemas WHERE name = :name", "name", factory.getInstanceSchema()
        ) > 0;
        if (!schemaAvailable) {
            final Connection connection = factory.getConnection() != null ? factory.getConnection().getConnection() : null;
            if (SQL_externalUpdateByQuery(" "
                + " CREATE SCHEMA [" + factory.getInstanceSchema() + "]", Collections.emptyMap(), connection, factory.getSessionStatement(), -1
            ) == 0) {
                String message = " "
                        + "No se pudo crear el esquema " + factory.getInstanceSchema() + " para el factory " + factory.toString() + " del entity " + factory.getEntityName();
                getLogger().error(message);
                throw new QMSException(message);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicTableBuildDTO getFactoryBuildedName(final ITableFactory<FieldType, FieldDTO> factory, final Long loggedUserId) {
        try {
            final String dynamicEntity = factory.getEntityName();
            final List<FieldDTO> dynamicFields = factory.getFields();
            final String md5 = factory.getTableNameMD5(dynamicEntity, dynamicFields);
            final String dynamicTableName = HQL_findSimpleString(" "
                    + " SELECT c.description"
                    + " FROM " + DynamicFieldTable.class.getCanonicalName() + " c "
                    + " WHERE c.code = :code", 
                    ImmutableMap.of("code", md5),
                    true, 
                    CacheRegion.DYNAMIC_FIELD,
                    0
            );
            if (dynamicTableName.isEmpty()) {
                factory.setMd5(md5);
                final String tableName = createDbTable(factory, loggedUserId).getDescription();
                return new DynamicTableBuildDTO(factory.getInstanceSchema(), tableName, true);
            } else {
                return new DynamicTableBuildDTO(factory.getInstanceSchema(), dynamicTableName, false);
            }
            
        } catch (InvalidArgumentException | QMSException | ClassNotFoundException | NoSuchMethodException | MetadataSqlException ex) {
            getLogger(LOGGER.DYNAMIC_TABLE).error("No se pudo obtener la tabla generica para el entity '{}'", factory.getEntityName(), ex);
        }
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DynamicFieldTable getFactoryBuilded(final ITableFactory<FieldType, FieldDTO> factory, Long loggedUserId) {
        try {
            return createDbTable(factory, loggedUserId);
        } catch (InvalidArgumentException | ClassNotFoundException | NoSuchMethodException | QMSException | MetadataSqlException ex) {
            getLogger(LOGGER.DYNAMIC_TABLE).error("No se pudo crear la tabla generica para el entity '{}'", factory.getEntityName(), ex);
            return null;
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer importValues(
        final String sourceTable,
        final String targetTable,
        final List<String> columns
    ) throws QMSException {
        final List<String> uniqueColumns = ImmutableList.copyOf(new LinkedHashSet<>(columns));
        return importValues(sourceTable, targetTable, uniqueColumns, uniqueColumns);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer importValues(
        final String sourceTable,
        final String targetTable,
        final List<String> selectColumnNames,
        final List<String> insertColumnNames
    ) throws QMSException {
        if (selectColumnNames.size() != insertColumnNames.size()) {
            throw new QMSException("The number of columns in source and target tables must be the same");
        }
        final String insertColumns = "[" + StringUtils.join(insertColumnNames, "],[") + "]";
        final String selectColumns = "[" + StringUtils.join(selectColumnNames, "],[") + "]";
        final Integer currentTargetRows = SQL_findSimpleInteger(" "
                + " SELECT "
                + " CASE WHEN MAX(id) >= 1 THEN MAX(id) ELSE 0 END AS numberRows"
                + " FROM " +  targetTable);
        final String insertSql = " "
                + " INSERT INTO " + targetTable
                + " (id, " + insertColumns + ")"
                + " SELECT "
                    + " ROW_NUMBER() OVER(ORDER BY id) + " + currentTargetRows
                + " as id"
                + " , " + selectColumns
                + " FROM " + sourceTable;
        getLogger().trace("Import values with sql: {}", insertSql);
        final Integer insertedRows = SQL_updateByQuery(insertSql, Collections.emptyMap(), 0, Collections.singletonList(targetTable));
        final Integer newSequence = insertedRows + currentTargetRows + 1;
        IdFactory.setNextId(targetTable, newSequence);
        return insertedRows;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long updateValues(
            Long referenceCommonTableId,
            String referenceCommonTable,
            String sourceTable,
            String targetTable,
            List<String> columns
    ) {
        final List<String> uniqueColumns = ImmutableList.copyOf(new LinkedHashSet<>(columns));
        return updateValues(referenceCommonTableId, referenceCommonTable, sourceTable, targetTable, uniqueColumns, uniqueColumns);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long updateImportValues(
            final Long sourceSurveyId,
            final Long referenceCommonTableId, 
            final String referenceCommonTable,
            final String sourceTable, 
            final String targetTable,
            final List<String> commonColumns
    ) {
        Long result;
        // Se agregan columnas configuradas como migración
        final MigrationColumnsDto migrationColumns = Utilities.getBean(IFormCaptureDAO.class).getMigrationColumns(sourceSurveyId, ImmutableList.copyOf(new HashSet<>(commonColumns)));
        // Se realizan updates
        final IDynamicTableDAO<FieldType, FieldDTO> tableDao = getBean(IDynamicTableDAO.class);
        if (migrationColumns.getSelectColumns().size() > commonColumns.size() && migrationColumns.getInsertColumns().size() == migrationColumns.getSelectColumns().size()) {
            result = tableDao.updateValues(
                referenceCommonTableId, referenceCommonTable, sourceTable, targetTable, migrationColumns.getSelectColumns(), migrationColumns.getInsertColumns()
            );
        } else {
            if (migrationColumns.getSelectColumns().size() > commonColumns.size()) {
                getLogger().error(" "
                                + " No fue posible actualizar las columnas migradas, se actualizarán únicamente las comunes."
                                + " sourceSurveyId: {} "
                                + ", insertColumns.size(): {} "
                                + ", selectColumns.size(): {} "
                                + ", insertColumns: {}"
                                + ", selectColumns: {}",
                                sourceSurveyId,
                                migrationColumns.getInsertColumns().size(),
                                migrationColumns.getSelectColumns().size(),
                                migrationColumns.getInsertColumns(),
                                migrationColumns.getSelectColumns()
                );
            }
            result = tableDao.updateValues(
                referenceCommonTableId, referenceCommonTable, sourceTable, targetTable, commonColumns
            );
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<String> getDynamicTableColumns(final String dynamicTableName) {
        final List<Map<String, Object>> results = SQL_findMap(" "
                        + " SELECT name"
                        + " FROM sys.columns"
                        + " WHERE object_id = OBJECT_ID(:dynamicTableName)",
                ImmutableMap.of("dynamicTableName", dynamicTableName)
        );
        if (results == null || results.isEmpty()) {
            return new ArrayList<>(0);
        }
        return results.stream()
                .map(row -> (String) row.get("name"))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long updateValues(
            final Long referenceCommonTableId,
            final String referenceCommonTable,
            final String sourceTable,
            final String targetTable,
            List<String> sourceColumns,
            List<String> targetColumns
    ) {
        if (sourceColumns.isEmpty()) {
            return 0L;
        }
        final String targetColumnId = DynamicTableHelper.getReferenceIdName(
                this,
                targetTable,
                referenceCommonTable
        );
        final String sourceColumnId = DynamicTableHelper.getReferenceIdName(
                this,
                sourceTable,
                referenceCommonTable
        );
        final StringBuilder updateHql = new StringBuilder();
        updateHql.append(" "
                + " UPDATE ").append(targetTable).append(" "
                + " SET last_modified_date = CURRENT_TIMESTAMP ");
        for (int i = 0; i < sourceColumns.size(); i++) {
            updateHql.append(", ")
                    .append(targetTable).append(".").append(targetColumns.get(i))
                    .append(" = ")
                    .append(sourceTable).append(".").append(sourceColumns.get(i))
            ;
        }
        updateHql.append(" "
                        + " FROM ").append(sourceTable).append(" "
                        + " WHERE ")
                .append(sourceTable).append(".").append(sourceColumnId).append(" = :id")
                .append(" AND ")
                .append(targetTable).append(".").append(targetColumnId).append(" = ").append(sourceTable).append(".").append(sourceColumnId);
        return SQL_updateByQuery(
                updateHql.toString(),
                ImmutableMap.of("id", referenceCommonTableId),
                0,
                Collections.singletonList(targetTable)
        ).longValue();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateSynonym(final String tableName, final String synonym) throws QMSException {
        Boolean result = SQL_execute(DynamicTableUtil.dropSynonym(synonym));
        getLogger(LOGGER.DYNAMIC_TABLE).trace("Se elimina {} sinónimo de Base de Datos: {}", 
            result, synonym
        );
        createSynonym(tableName, synonym);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void createSynonym(final String tableName, final String synonym) throws QMSException {
        final String synonymDdl = DynamicTableUtil.buildSynonym(tableName, synonym);
        SQL_execute(synonymDdl);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void addColumn(final ITableFactory<FieldType, FieldDTO> factory, final String tableName, final FieldDTO column) {
        StringBuilder alterStatement = new StringBuilder();
        String columnType = factory.getParsedColumnType(column);
        alterStatement.append(" "
                + " ALTER TABLE [").append(factory.getInstanceSchema()).append("].[").append(tableName).append("] ADD ")
                .append("[").append(column.getName()).append("]")
                .append(" ").append(columnType);
        if (column.getAllowNulls()) {
            if (factory.isSparseColumnsEnabled() && !ITableFactory.TableFactoryColumnType.VARCHARMAX_COLUMN_TYPE.getValue().equals(columnType)) {
                alterStatement.append(" SPARSE ");
            }
        } else {
            alterStatement.append(" NOT NULL ");
        }
        //se crea la tabla
        final Connection connection = factory.getConnection() != null ? factory.getConnection().getConnection() : null;
        SQL_externalUpdateByQuery(alterStatement.toString(), Collections.emptyMap(), connection, factory.getSessionStatement(), -1);
    }

    

    /**
     * Crea una tabla en base de datos para guardar los datos 
     * relacionados a la combinación de columnas 
     *
     * @return devuelve la información de la tabla creada
     */
    private DynamicFieldTable createDbTable(final ITableFactory<FieldType, FieldDTO> factory, final Long loggedUserId)
            throws InvalidArgumentException, ClassNotFoundException, NoSuchMethodException, QMSException {
        final List<FieldDTO> dynamicFields = factory.getFields();
        DynamicFieldTable table = getInstance(factory, loggedUserId);
        final String tableDescription = table.getDescription();
        final String schema;
        if (factory.getInstanceSchema() == null) {
            schema = "dbo";
        } else {
            schema = factory.getInstanceSchema();
            checkForSchema(factory);
        }
        final String fullTableName = "[" + schema + "].[" + tableDescription + "]";
        StringBuilder 
            createStatement = new StringBuilder(dynamicFields.size() * 30)
                    .append("CREATE TABLE [")
                    .append(schema).append("].[").append(tableDescription)
                    .append("] ("),
            indexStatement = new StringBuilder(dynamicFields.size() * 50),
            fkStatement = new StringBuilder(50),
            pkStatement = new StringBuilder(50)
        ;
        List<String> fields = new ArrayList<>();
        for (FieldDTO f : dynamicFields) {
            fields.add(f.getName());
            String columnType = factory.getParsedColumnType(f);
            createStatement
                    .append("[").append(f.getName()).append("]")
                    .append(" ").append(columnType);
            if (f.getAllowNulls()) {
                if(factory.isSparseColumnsEnabled() && !ITableFactory.TableFactoryColumnType.VARCHARMAX_COLUMN_TYPE.getValue().equals(columnType)) {
                    createStatement.append(" SPARSE ");
                }
            } else {
                createStatement.append(" NOT NULL ");
            }
            createStatement
                    .append(",");
            // Se agregan columnas calculadas debido al cambio de múltiples items a solo dos columnas. Aplica para surveys ya existentes.
            if (Boolean.TRUE.equals(factory.getAllowComputedColumns()) && f.getAssociatedFields() != null && !f.getAssociatedFields().isEmpty() && f.getName().contains(AnswerPartType.CATALOG_VALUE.getSuffix())) {
                f.getAssociatedFields().stream()
                        .filter(field -> !Objects.equals(AnswerPartType.OTHER_FIELD.getValue(), field.getAnswerPartType()))
                        .forEach(associatedField -> {
                            createStatement
                                    .append("[").append(associatedField.getName()).append("]")
                                    .append(" AS ( CASE WHEN ").append(f.getName()).append(" = '").append(associatedField.getOptionCode()).append("' THEN 1 END)")
                                    .append(",");
                        });
            }
            if (!factory.persistMetadata(table, f, columnType, loggedUserId, this)) {
                throw new QMSException("No se pudo guardar la metadata de la columna " + f.getName());
            };
        }
        if (createStatement.length() > 0) {
           createStatement.setLength(createStatement.length() - 1);
        }        
        String fkTableName, referenceIdName, idName;
        Long fkId;
        Set<TableReferenceIdDTO> tableIdMap = factory.getJoinableEntities();
        if (tableIdMap == null) {
            tableIdMap = Collections.emptySet();
        }
        DynamicFieldFkMapping fkMapping;
        for (TableReferenceIdDTO entry : tableIdMap) {
            fkTableName = entry.getFkTableName();
            referenceIdName = entry.getReferenceIdName();
            fkId = IdFactory.getNextId(tableDescription + "_FK");
            idName = referenceIdName + fkId;
            fkMapping = new DynamicFieldFkMapping(-1L);
            fkMapping.setCode("FK_" + tableDescription + fkId + "_" + fkTableName);
            fkMapping.setDescription(idName); //<-- es el nombre de la nueva columna de ID
            fkMapping.setReferenceTableName(fkTableName);
            fkMapping.setReferenceIdName(referenceIdName);
            fkMapping.setDynamicFieldTableId(table.getId());
            fkMapping.setDynamicFieldTableName(tableDescription);
            makePersistent(fkMapping, loggedUserId);
            createStatement
                .append(",")
                .append(idName)
                .append(" bigint ")
            ;
            fkStatement
                .append(" ALTER TABLE [").append(schema).append("].[").append(tableDescription).append("]")
                .append(" ADD CONSTRAINT ").append(fkMapping.getCode())
                .append(" FOREIGN KEY ([").append(idName).append("])")
                .append(" REFERENCES ").append(fkMapping.getReferenceTableName())
                    .append(" (").append(fkMapping.getReferenceIdName()).append("); ")
            ;
            // Validar si existe el indice antes de crearlo
            final String nonClusteredIdx = NONCLUSTERED_IDX
                    .replaceAll(TABLE_DESC, tableDescription)
                    .replaceAll(FULL_TABLE_DESC, fullTableName)
                    .replaceAll(IDX_REFERENCE, idName)
                    .replaceAll(ID_NAME, "[" + idName + "]");
            final String nonClusteredAllIdx = NONCLUSTERED_INCLUDE_ALL_IDX
                    .replaceAll(TABLE_DESC, tableDescription)
                    .replaceAll(FULL_TABLE_DESC, fullTableName)
                    .replaceAll(IDX_REFERENCE, idName)
                    .replaceAll(ID_NAME, "[" + idName + "]")
                    .replaceAll(FIELDS, "[" + StringUtils.join(fields, "],[") + "]");
            if(!indexStatement.toString().contains(nonClusteredIdx)) {
                indexStatement
                    .append(nonClusteredIdx)
                ;
            }
            if(!indexStatement.toString().contains(nonClusteredAllIdx)) {
                indexStatement
                    .append(nonClusteredAllIdx)
                ;
            }
        }
        createStatement.append(")");
        final int numberFields = dynamicFields.size() + tableIdMap.size() + DEFAULT_TABLE_FIELDS;
        if (numberFields > MAX_TABLE_FIELDS) {
            final String error = " "
                    + "Failed to create table " + fullTableName + " with " + numberFields + " columns "
                    + "as exceeds the SQL Server " + MAX_TABLE_FIELDS + " columns limit.";
            throw new MetadataSqlException(error);
        }
        pkStatement
            .append(" ALTER TABLE [").append(schema).append("].[").append(tableDescription).append("]")
            .append(" ADD CONSTRAINT [pk_").append(tableDescription).append("]")
            .append(" PRIMARY KEY (").append(factory.getPrimayKeyMame()).append(");");
        final Connection connection = factory.getConnection() != null ? factory.getConnection().getConnection() : null;
        //se crea la tabla
        SQL_externalUpdateByQuery(createStatement.toString(), Collections.emptyMap(), connection, factory.getSessionStatement(), -1);

        Connection connectionReplication = null;

        boolean enabledDbReplication = Objects.equals(Utilities.getSettings().getEnableDbReplication(), 1);
        if (enabledDbReplication) {
            try {
                connectionReplication = new ConectionUtil().getReplicationDatabaseConection();
            } catch (Exception ex) {
                getLogger().error("Error al obtener la conexión a la base de datos de respaldo", ex);
            }
        }
        if (enabledDbReplication) {
            try {
                //se crea la tabla bkp
                if (connectionReplication != null) {
                    SQL_externalUpdateByQuery(createStatement.toString(), Collections.emptyMap(), connectionReplication, factory.getSessionStatement(), -1);
                } else {
                    getLogger().error("No se pudo obtener la conexión a la base de datos de respaldo para crear la tabla de respaldo");
                }
            } catch (Exception ex) {
                getLogger().error("Error al crear la tabla de respaldo", ex);
            }
        }
        /*
         * Se crean llaves foraneas de la tabla
         *
         * EY! Se apagan llaves foraneas debido a que en el makePersistent los
         * IDs aun no se reflejan en la tabla por lo que marca error de que los
         * IDs aun no existen como FK SQL_externalUpdateByQuery(fkStatement.toString());
         */
        SQL_externalUpdateByQuery(pkStatement.toString(), Collections.emptyMap(), connection, factory.getSessionStatement(), -1);

        if (enabledDbReplication) {
            try {
                //Se crean llaves foraneas de la tabla bkp
                if (connectionReplication != null) {
                    SQL_externalUpdateByQuery(pkStatement.toString(), Collections.emptyMap(), connectionReplication, factory.getSessionStatement(), -1);
                } else {
                    getLogger().error("No se pudo obtener la conexión a la base de datos de respaldo para crear la llave primaria de la tabla de respaldo");
                }
            } catch (Exception ex) {
                getLogger().error("Error al crear la llave primaria de la tabla de respaldo", ex);
            }
        }
        
        //se crean indices
        final String indexesSql = indexStatement.toString();
        if (!indexesSql.isEmpty()) {
            SQL_externalUpdateByQuery(indexesSql, Collections.emptyMap(), connection, factory.getSessionStatement(), -1);
            try {
                //se crean indices bkp
                if (connectionReplication != null) {
                    SQL_externalUpdateByQuery(indexesSql, Collections.emptyMap(), connectionReplication, factory.getSessionStatement(), -1);
                }
            } catch (Exception ex) {
                getLogger().error("Error al crear los indices de la tabla de respaldo", ex);
            } finally {
                try {
                    if (connectionReplication != null) {
                        connectionReplication.close();
                    }
                } catch (Exception ex) {
                    getLogger().error("Error al cerrar la conexión a la base de datos de respaldo", ex);
                }
            }
        
        } 
        return table;
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer createDbView(
            final ITableFactory<FieldType, FieldDTO> factory,
            final String schemaName, 
            final String viewName,
            final String viewSql 
    )  {
        final String fullViewName = "[" + schemaName + "].[" + viewName + "]";
        final Connection connection = factory.getConnection() != null ? factory.getConnection().getConnection() : null;
        final Boolean existsView = existsDatabaseObject(schemaName, viewName, connection);
        final String ddl;
        if (Boolean.TRUE.equals(existsView)) {
            ddl = "ALTER VIEW " + fullViewName + " AS \r\n\r\n " + viewSql;
        } else {
            ddl = "CREATE VIEW " + fullViewName + " AS \r\n\r\n " + viewSql;
        }
        Integer res = SQL_externalUpdateByQuery(ddl, Collections.emptyMap(), connection, factory.getSessionStatement(), -1);
        if (Objects.equals(Utilities.getSettings().getEnableDbReplication(), 1)) {
            try {
                // creando bakups
                final Connection replicationConection = new ConectionUtil().getReplicationDatabaseConection();
                if (replicationConection != null) {
                    SQL_externalUpdateByQuery(
                            ddl, Collections.emptyMap(),
                            replicationConection,
                            factory.getSessionStatement(),
                            -1
                    );
                } else {
                    getLogger().error("No se pudo obtener la conexión a la base de datos de respaldo para crear la vista de respaldo");
                }
            } catch (Exception ex) {
                getLogger().error("Error al crear la vista de respaldo", ex);
            }
        }
        
        return res;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean existsDatabaseObject(
            final String schemaName, 
            final String viewName, 
            final Connection connection
    ) {
        final String fullViewName = "[" + schemaName + "].[" + viewName + "]";
        final Integer objectId = SQL_findSimpleInteger(" "
                + " SELECT OBJECT_ID('" + fullViewName + "')", 
                Collections.emptyMap(), 
                connection, 
                null,
                -1
        );
        return objectId > 0;
    }

}
