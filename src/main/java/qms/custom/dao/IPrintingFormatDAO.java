/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.dao;

import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.io.IOException;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import mx.bnext.qms.configuration.dto.PrintingParsedHtmlDTO;
import org.springframework.http.ResponseEntity;
import qms.access.dto.ILoggedUser;
import qms.custom.dto.FormatDataSourceDTO;
import qms.custom.entity.PrintingFormat;
import qms.custom.entity.PrintingFormatOutstandingSurveys;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
@Implementation(name = "PrintingFormatDAO")
public interface IPrintingFormatDAO extends IBaseFormatDAO<FormatDataSourceDTO, PrintingFormat> {

    PrintingParsedHtmlDTO getPrintingFormatRecord(Long printingFormatId, Long requestId);
        
    Long saveFormatWithData(
            PrintingFormatOutstandingSurveys loadedRecord,
            Long id,
            Long outstandingSurverysId,
            String title,
            org.jsoup.nodes.Document doc,
            String currentHash,
            ILoggedUser loggedUser
    ) throws IOException;

    void regeneratePrintingFormats(ILoggedUser loggedUser);
}
