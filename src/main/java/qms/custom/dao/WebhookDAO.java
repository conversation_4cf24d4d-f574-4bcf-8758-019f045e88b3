package qms.custom.dao;

import DPMS.Mapping.Document;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.exception.ExplicitRollback;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.hibernate.Hibernate;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.custom.dto.DataSourceDTO;
import qms.custom.dto.WebhookDataSourceDTO;
import qms.custom.util.FilePersistentUtil;
import qms.form.entity.Webhook;
import qms.framework.util.CacheRegion;
import qms.util.EntityCommon;
import qms.util.FormUtil;
import qms.util.GridFilter;
import qms.util.QMSException;

@Lazy
@Repository(value = "WebhookDAO")
@Scope(value = "singleton")
public class WebhookDAO extends GenericDAOImpl<Webhook, Long> implements IWebhookDAO {

    private static final String WEBHOOK_FILE_PREFIX = "Webhook_";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> list(
            GridFilter filter,
            Module module,
            String masterId
    ) {
        String hqlBuilder = " "
                + " SELECT new map("
                    + " c.id AS id "
                    + ",c.code AS code "
                    + ",c.description AS description "
                    + ",c.details AS details "
                    + ",c.status AS status "
                    + ",c.documentVersion AS documentVersion "
                    + ",c.surveyId AS surveyId "
                    + ",c.createdDate AS createdDate "
                    + ",c.lastModifiedDate AS lastModifiedDate "
                    + ",u.description AS createdByUserName "
                + " ) "
                + " FROM " + Webhook.class.getCanonicalName() + " c "
                + " JOIN c.createdByUser u "
                + " WHERE  c.moduleName = '" + module.getKey() + "'"
                + " AND c.deleted = 0 ";
        // Para solo permitir UUID válidos
        final UUID masterIdUuid = UUID.fromString(masterId);
        filter.getCriteria().put("<filtered-entity>", "c.masterId = '" +  masterIdUuid.toString() + "'");
        return HQL_getRows(
                hqlBuilder,
                filter,
                true,
                CacheRegion.WEBHOOK,
                0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> webhookBySection(
            Long stageFieldObjectId
    ) {
        String hqlBuilder = " "
                + " SELECT new map("
                    + " c.id AS id "
                    + ",c.url AS url "
                    + ",c.headers AS headers "
                    + ",c.documentVersion AS documentVersion "
                    + ",c.surveyId AS surveyId "
                    + ",c.fileId AS fileId "
                + " ) "
                + " FROM " + Webhook.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.status = " + StandardEntity.STATUS.ACTIVE.getValue()
                + " AND c.stageFieldObjectId = :stageFieldObjectId"
                + " AND c.deleted = 0 ";
        return HQL_findByQuery(
                hqlBuilder,
                ImmutableMap.of(
                        "stageFieldObjectId", stageFieldObjectId
                ),
                true,
                CacheRegion.WEBHOOK,
                0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public WebhookDataSourceDTO getDataSource(Module module, String masterId, Long id, ILoggedUser loggedUser) {
        WebhookDataSourceDTO data = new WebhookDataSourceDTO();
        // Se cargan catálogos para altas nuevas (campos disponibles, metadata y titulo)
        DataSourceDTO data_ = FormUtil.fillTitleAndFlexiFields(module, masterId, loggedUser, false, true);
        data.setTitle(data_.getTitle());
        data.setFlexiFields(data_.getFlexiFields());
        data.setSectionFields(data_.getSectionFields());
        // En caso de contar con un ID, se carga la información del formato a editar
        // TODO
        this.fillLoad(data, id, loggedUser);
        return data;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<GenericSaveHandle> save(Module module, Webhook entity, ILoggedUser loggedUser) throws IOException, QMSException {
        final GenericSaveHandle result = new GenericSaveHandle();
        final boolean isNew = entity.getId() == null || entity.getId().equals(-1L);

        // Se genera clave
        if (isNew) {
            entity.setId(-1L);
            if (entity.getCode() == null || entity.getCode().isEmpty()) {
                entity.setCode(EntityCommon.getNextCode(entity));
            }
        }

        // Se guarda JSON
        final Long fileId = FilePersistentUtil.saveHtmlToFile(entity.getFileId(), entity.getJson(), loggedUser, WEBHOOK_FILE_PREFIX);
        if (fileId == null) {
            throw new ExplicitRollback("Couldn't save HTML for " + entity.getDescription());
        } else {
            entity.setFileId(fileId);
        }

        Long stageFieldObjectId = entity.getStageFieldObjectId();
        if (stageFieldObjectId != null) {
            final Map<String, Object> documentData = HQL_findSimpleMap(
                    " SELECT new map("
                            + " doc.version AS documentVersion"
                            + ", doc.surveyId AS surveyId"
                        + " )"
                        + " FROM " + SurveyFieldObject.class.getCanonicalName() + " obj"
                        + " JOIN " + Document.class.getCanonicalName() + " doc"
                        + " ON doc.surveyId = obj.surveyId"
                        + " WHERE obj.id = :id",
                    ImmutableMap.of("id", stageFieldObjectId),
                    true,
                    CacheRegion.WEBHOOK,
                    0
            );
            if (!documentData.isEmpty()) {
                entity.setDocumentVersion((String) documentData.get("documentVersion"));
                entity.setSurveyId((Long) documentData.get("surveyId"));
            }
        }
        // Modulo QMS
        entity.setModuleName(module.getKey());

        // Se guarda ENTITY
        if (makePersistent(entity, loggedUser.getId()) == null) {
            throw new ExplicitRollback("Couldn't save at all " + entity.getDescription());
        } else {
            result.setOperationEstatus(1);
            result.setSavedId(entity.getId().toString());
        }
        if (isNew) {
            updateCountEntity(entity.getMasterId(), loggedUser);
        }
        return new ResponseEntity(result, HttpStatus.OK);
    }

    private void fillLoad(WebhookDataSourceDTO data, Long webhookId, ILoggedUser loggedUser) {
        Webhook result = getEntity(webhookId, loggedUser);
        // Información para el front
        data.setLoad(result);
    }

    private Webhook getEntity(Long webhookId, ILoggedUser loggedUser) {
        if (webhookId == null) {
            return null;
        }
        Webhook result = HQLT_findById(Webhook.class, webhookId, true, CacheRegion.WEBHOOK, 0);
        // Se carga los campos lazy del entity
        lazyLoadSurveyFieldValues(result);
        // Se carga el JSON del la data al archivo
        String str = FilePersistentUtil.writeDataToFile(result.getFileId(), loggedUser.getId());
        if (str != null) {
            result.setJson(str);
        }
        return result;
    }

    private void lazyLoadSurveyFieldValues(Webhook result) {
        if (result == null) {
            return;
        }

        // Se cargan datos del entity
        Hibernate.initialize(result.getSurveyFieldsValues()); // <-- Se llama para cargar valres LAZY!

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getDocumentMasterId(final Long mainEntityId) {
        return FormUtil.getDocumentMasterId(mainEntityId, Utilities.getBean(IWebhookDAO.class), Webhook.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateCountEntity(final String documentMasterId, final ILoggedUser loggedUser) {
        return FormUtil.updateCountEntity(documentMasterId, loggedUser, Utilities.getBean(IWebhookDAO.class), Webhook.class, "countWebhooks");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Webhook fullLoadById(Long id) {
        final Webhook format = HQLT_findById(Webhook.class, id);
        // Se carga los campos lazy del entity
        lazyLoadSurveyFieldValues(format);
        return format;
    }
}
