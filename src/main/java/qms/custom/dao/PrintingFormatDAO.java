/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.dao;

import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.exception.ExplicitRollback;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import mx.bnext.qms.configuration.dto.PrintingParsedHtmlDTO;
import mx.bnext.qms.configuration.util.PrintingFormatHelper;
import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.custom.dto.DataSourceDTO;
import qms.custom.dto.FormatDataSourceDTO;
import qms.custom.entity.PrintingFormat;
import qms.custom.entity.PrintingFormatOutstandingSurveys;
import qms.custom.util.FilePersistentUtil;
import qms.framework.util.CacheRegion;
import qms.util.EntityCommon;
import qms.util.FormUtil;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "PrintingFormatDAO")
@Scope(value = "singleton")
public class PrintingFormatDAO extends GenericDAOImpl<PrintingFormat, Long> implements IPrintingFormatDAO {
    private static final String PRINT_FORMAT_FILE_PREFIX = "PrintFormat_";
    private static final String PRINT_FORMAT_FILE_EXTENSION = "html";
    private static final String PRINT_FORMAT_FILE_CONTENT_TYPE = "text/html";

    private static final Class<PrintingFormatDAO> CLAZZ = PrintingFormatDAO.class;
    private static final Logger LOGGER = Loggable.getLogger(CLAZZ);

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> list(
            GridFilter filter,
            Module module,
            String masterId
    ) {
        String hqlBuilder = " "
                + " SELECT new map("
                    + " c.id AS id "
                    + ",c.code AS code "
                    + ",c.description AS description "
                    + ",c.details AS details "
                    + ",c.status AS status "
                    + ",c.createdDate AS createdDate "
                    + ",c.lastModifiedDate AS lastModifiedDate "
                    + ",u.description AS createdByUserName "
                + " ) "
                + " FROM " + PrintingFormat.class.getCanonicalName() + " c "
                + " JOIN c.createdByUser u "
                + " WHERE  c.moduleName = '" + module.getKey() + "'"
                + " AND c.deleted = 0 ";
        // Para solo permitir UUID válidos
        final UUID masterIdUuid = UUID.fromString(masterId);
        filter.getCriteria().put("<filtered-entity>", "c.masterId = '" +  masterIdUuid.toString() + "'");
        return HQL_getRows(
                hqlBuilder,
                filter,
                true,
                CacheRegion.PRINTING_FORMAT,
                0
        );
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public FormatDataSourceDTO getDataSource(
            Module module, 
            String masterId, 
            Long id,
            ILoggedUser loggedUser
    ) {
        FormatDataSourceDTO data = new FormatDataSourceDTO();
        // Se cargan catalogos para altas nuevas (campos disponibles, metadata y titulo)
        DataSourceDTO data_ = FormUtil.fillTitleAndFlexiFields(module, masterId, loggedUser, false, false);
        data.setTitle(data_.getTitle());
        data.setFlexiFields(data_.getFlexiFields());
        // En caso de contar con un ID, se carga la información del formato a editar
        this.fillLoad(data, id, loggedUser);
        return data;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PrintingParsedHtmlDTO getPrintingFormatRecord(final Long printingFormatId, final Long requestId) {
        final Map<String, Object> outstandingSurveyData = HQL_findSimpleMap(""
                + " SELECT new map("
                    + " o.id AS outstandingSurveysId"
                    + ", o.lastModifiedDate AS lastFillDate"
                    + ", o.documentId AS documentId"
                    + ", o.surveyId AS surveyId"
                + " )"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " o "
                + " WHERE o.requestId = :requestId", "requestId", requestId
        );
        if (outstandingSurveyData == null || outstandingSurveyData.isEmpty()) {
            throw new ExplicitRollback("No format available for rquest id " + requestId);
        }
        final Long outstandingSurveysId = (Long) outstandingSurveyData.get("outstandingSurveysId");
        if (outstandingSurveysId == null) {
            throw new ExplicitRollback("No format available for id " + printingFormatId + " for request id " + requestId);
        }
        final Long documentId = (Long) outstandingSurveyData.get("documentId");
        final Date lastFillDate = (Date) outstandingSurveyData.get("lastFillDate");
        
        final PrintingParsedHtmlDTO record = HQLT_findSimple(PrintingParsedHtmlDTO.class, ""
                + " SELECT new " + PrintingParsedHtmlDTO.class.getCanonicalName() + " ("
                    + " p.id AS printingFormatId "
                    + ", p.fileId AS defaultFileId "
                    + ", pf.fileId AS parsedFileId"
                    + ", pf.lastModifiedDate AS parsedLastModifiedDate"
                + " )"
                + " FROM " + PrintingFormat.class.getCanonicalName() + " p "
                + " LEFT JOIN " + PrintingFormatOutstandingSurveys.class.getCanonicalName() + " pf "
                    + " ON p.id = pf.id.printingFormatId"
                    + " AND pf.id.outstandingSurveysId = :outstandingSurveysId "
                + " WHERE p.id = :printingFormatId ",
                ImmutableMap.of(
                        "printingFormatId", printingFormatId,
                        "outstandingSurveysId", outstandingSurveysId
                ), true,
                CacheRegion.PRINTING_FORMAT,
                0
        );
        if (record == null) {
            throw new ExplicitRollback("No format available for id " + printingFormatId + " of outstandingSurveys " + outstandingSurveysId);
        }
        record.setDocumentId(documentId);
        record.setOutstandingSurveysId(outstandingSurveysId);
        record.setSurveyId((Long) outstandingSurveyData.get("surveyId"));
        record.setLastFillDate(lastFillDate);
        return record;
    }
    
  
    /**
    * Se agrega helper PrintingFormatHelper para no hacer operaciones de lectura 
    * como parte de una solo transacción y tener bloqueda la conexión a la base de datos
    * Solo las operaciones de escritura se dejan en PrintingFormatDAO
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long saveFormatWithData(
            final PrintingFormatOutstandingSurveys loadedRecord, 
            final Long printingFormatId,
            final Long outstandingSurveysId,
            final String title, 
            final org.jsoup.nodes.Document doc, 
            final String currentHash,
            final ILoggedUser loggedUser
    ) throws IOException {
        if (loadedRecord != null) {
            HQL_updateByQuery(""
                    + " DELETE FROM " + PrintingFormatOutstandingSurveys.class.getCanonicalName() + " pf "
                            + " WHERE "
                            + " pf.id.printingFormatId = :printingFormatId "
                            + " AND pf.id.outstandingSurveysId = :outstandingSurveysId "
                    ,
                    ImmutableMap.of(
                            "printingFormatId", printingFormatId,
                            "outstandingSurveysId", outstandingSurveysId
                    ), true, CacheRegion.PRINTING_FORMAT, 0);
        }
        // Se guarda y se obtiene ID del HTML parseado
        final Long saveFileId = FilePersistentUtil.saveHtmlToFile(-1L, title, doc.html(), loggedUser, PRINT_FORMAT_FILE_PREFIX);

        final PrintingFormatOutstandingSurveys newRecord = new PrintingFormatOutstandingSurveys(
                printingFormatId,
                outstandingSurveysId
        );
        newRecord.setFileId(saveFileId);
        newRecord.setFieldValuesHash(currentHash);
        newRecord.setCreatedBy(loggedUser.getId());
        newRecord.setCreatedDate(new Date());
        newRecord.setLastModifiedBy(loggedUser.getId());
        newRecord.setLastModifiedDate(new Date());
        makePersistent(newRecord, loggedUser.getId(), false);
        return saveFileId;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<GenericSaveHandle> save(Module module, PrintingFormat entity, ILoggedUser loggedUser) throws IOException, QMSException {
        final GenericSaveHandle result = new GenericSaveHandle();
        final boolean isNew = entity.getId() == null || entity.getId().equals(-1L);

        // Se generae clave
        if (isNew) {
            entity.setId(-1L);
            if (entity.getCode() == null || entity.getCode().isEmpty()) {
                entity.setCode(EntityCommon.getNextCode(entity));
            }
        } else {
            HQL_updateByQuery(""
                + " DELETE FROM " + PrintingFormatOutstandingSurveys.class.getCanonicalName() + " p "
                + " WHERE p.id.printingFormatId = :printingFormatId ", ImmutableMap.of("printingFormatId", entity.getId()
            ), true, CacheRegion.PRINTING_FORMAT, 0);
        }
        
        // Se guarda HTML
        final Long fileId = FilePersistentUtil.saveHtmlToFile(entity.getFileId(), entity.getHtml(), loggedUser, PRINT_FORMAT_FILE_PREFIX);
        if (fileId == null) {
            throw new ExplicitRollback("Couldn't save HTML for " + entity.getDescription());
        } else {
            entity.setFileId(fileId);
        }
        
        // Modulo QMS
        entity.setModuleName(module.getKey());

        // Se guarda ENTITY
        if (makePersistent(entity, loggedUser.getId()) == null) {
            throw new ExplicitRollback("Couldn't save at all " + entity.getDescription());
        } else {
            result.setOperationEstatus(1);
            result.setSavedId(entity.getId().toString());
        }
        if (isNew) {
            updateCountEntity(entity.getMasterId(), loggedUser);
        }
        return new ResponseEntity(result, HttpStatus.OK);
    }

    private void fillLoad(FormatDataSourceDTO data, Long printingFormatId, ILoggedUser loggedUser) {
        PrintingFormat result = getEntity(printingFormatId, loggedUser);
        // Información para el front
        data.setLoad(result);
    }
    
    private PrintingFormat getEntity(Long printingFormatId, ILoggedUser loggedUser) {
        if (printingFormatId == null) {
            return null;
        }
        PrintingFormat result = HQLT_findById(PrintingFormat.class, printingFormatId, true, CacheRegion.PRINTING_FORMAT, 0);
        // Se carga los campos lazy del entity
        lazyLoadSurveyFieldValues(result);
        // Se carga el HTML del template al archivo
        // Se carga el JSON del la data al archivo
        String str = FilePersistentUtil.writeDataToFile(result.getFileId(), loggedUser.getId());
        if (str != null) {
            result.setHtml(str);
        }
        return result;
    }
    
    private void lazyLoadSurveyFieldValues(PrintingFormat result) {
        if (result == null) {
            return;
        }
        
        // Se cargan datos del entity
        Hibernate.initialize(result.getSurveyFieldsValues()); // <-- Se llama para cargar valres LAZY!
        
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getDocumentMasterId(final Long mainEntityId) {
        return FormUtil.getDocumentMasterId(mainEntityId, Utilities.getBean(IPrintingFormatDAO.class), PrintingFormat.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateCountEntity(final String documentMasterId, final ILoggedUser loggedUser) {
        return FormUtil.updateCountEntity(documentMasterId, loggedUser, Utilities.getBean(IPrintingFormatDAO.class), PrintingFormat.class, "countPrintFormats");
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PrintingFormat fullLoadById(Long id) {
        final PrintingFormat format = HQLT_findById(PrintingFormat.class, id);
        // Se carga los campos lazy del entity
        lazyLoadSurveyFieldValues(format);
        return format;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void regeneratePrintingFormats(final ILoggedUser admin) {
        // Recalcular vistas de impresion
        List<Map<String, Object>> printingFormats = HQL_findByQuery(""
                + " SELECT new map( "
                    + " pout.id.printingFormatId AS printingFormatId "
                    + ",os.requestId AS requestId "
                + " )"
                + " FROM " + PrintingFormatOutstandingSurveys.class.getCanonicalName() + " pout "
                + " INNER JOIN " + OutstandingSurveys.class.getCanonicalName() + " os ON os.id = pout.id.outstandingSurveysId "
        );
        final PrintingFormatHelper helper = new PrintingFormatHelper(this);
        printingFormats.forEach(p -> {
            Long printingFormatId = Long.parseLong(p.get("printingFormatId").toString());
            Long requestId = Long.parseLong(p.get("requestId").toString());
            final PrintingParsedHtmlDTO record = this.getPrintingFormatRecord(printingFormatId, requestId);
            try {
                helper.refreshFormat(record, admin, true);
            } catch (Exception e) {
                LOGGER.error("Ocurrió un error al regenerar la vista de impresión con id {} y request {}, error {}", printingFormatId, requestId, e);
            }
        });
    }
    
}
