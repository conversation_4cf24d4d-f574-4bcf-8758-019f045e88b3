package qms.custom.dao;

import Framework.DAO.Implementation;
import java.util.List;
import java.util.Map;
import qms.custom.dto.WebhookDataSourceDTO;
import qms.form.entity.Webhook;

@Implementation(name = "WebhookDAO")
public interface IWebhookDAO extends IBaseFormatDAO<WebhookDataSourceDTO, Webhook>  {

    String WEBHOOK_DATA = "webhookData";

    List<Map<String, Object>> webhookBySection(Long sectionFieldObjectId);

}
