package qms.custom.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class DynamicTableBuildDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String schemaName;
    private String tableName;
    private Boolean newTable;

    public DynamicTableBuildDTO() {
    }

    public DynamicTableBuildDTO(String schemaName, String tableName, Boolean newTable) {
        this.schemaName = schemaName;
        this.tableName = tableName;
        this.newTable = newTable;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Boolean getNewTable() {
        return newTable;
    }

    public boolean isNewTable() {
        return Boolean.TRUE.equals(newTable);
    }

    public void setNewTable(Boolean newTable) {
        this.newTable = newTable;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 67 * hash + Objects.hashCode(this.tableName);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DynamicTableBuildDTO other = (DynamicTableBuildDTO) obj;
        return Objects.equals(this.tableName, other.tableName);
    }

    @Override
    public String toString() {
        return "DynamicTableBuildDTO{" + "tableName=" + tableName + ", newTable=" + newTable + '}';
    }

}
