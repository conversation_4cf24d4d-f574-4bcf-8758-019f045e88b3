/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.dto;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DummyFlexiFieldsDTO implements IFlexiFields {

    private List<String> flexiFields;
    private List<String> flexiFilters;
    private List<String> relatedFields;

    public DummyFlexiFieldsDTO() {
    }

    public DummyFlexiFieldsDTO(List<String> flexiFields) {
        this.flexiFields = flexiFields;
    }

    @Override
    public List<String> getFlexiFields() {
        return flexiFields;
    }

    public void setFlexiFields(List<String> flexiFields) {
        this.flexiFields = flexiFields;
    }

    @Override
    public List<String> getFlexiFilters() {
        return flexiFilters;
    }

    public void setFlexiFilters(List<String> flexiFilters) {
        this.flexiFilters = flexiFilters;
    }

    @Override
    public List<String> getRelatedFields() {
        return relatedFields;
    }

    public void setRelatedFields(List<String> relatedFields) {
        this.relatedFields = relatedFields;
    }

}
