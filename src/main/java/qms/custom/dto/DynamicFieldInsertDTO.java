
package qms.custom.dto;

import java.io.Serializable;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class DynamicFieldInsertDTO implements Serializable{
    
    private static final long serialVersionUID = 8799656478674716638L;
    private boolean success;
    private String previousValue;
    private String tableName;
    private String schemaName;
    private Long id;

    public DynamicFieldInsertDTO() {
    }

    public DynamicFieldInsertDTO(QMSException e) {
        this.success = false;
        this.previousValue = e.getMessage();
    }

    public DynamicFieldInsertDTO(String tableName, Long id) {
        this.tableName = tableName;
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getPreviousValue() {
        return previousValue;
    }

    public void setPreviousValue(String previousValue) {
        this.previousValue = previousValue;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }
}
