package qms.custom.dto;

import qms.custom.entity.PrintingFormat;

public class FormatDataSourceDTO extends DataSourceDTO {

    private PrintingFormat load;

    public FormatDataSourceDTO() {
        super();
    }

    public FormatDataSourceDTO(String message) {
        super(message);
    }

    public static FormatDataSourceDTO instance(String message) {
        return new FormatDataSourceDTO(message);
    }

    public static FormatDataSourceDTO instance() {
        return new FormatDataSourceDTO();
    }

    public PrintingFormat getLoad() {
        return load;
    }

    public void setLoad(PrintingFormat load) {
        this.load = load;
    }


}
