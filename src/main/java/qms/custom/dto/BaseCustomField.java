package qms.custom.dto;

import java.util.Collections;
import java.util.Objects;
import java.util.Set;
import javax.persistence.Transient;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.core.IDynamicTableField;
import qms.form.dto.SurveyDataFieldDTO;

/**
 *
 * <AUTHOR>
 */
public class BaseCustomField implements IDynamicTableField<String>  {

    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String type;
    private String name;
    private Boolean allowNulls = true;
    private Boolean isSystem = false;

    public BaseCustomField(String name, String type) {
        this.id = null;
        this.name = name;
        this.type = type;
        this.isSystem = false;
    }
    
    public BaseCustomField(String name, String type, Boolean allowNulls) {
        this.id = null;
        this.name = name;
        this.type = type;
        this.allowNulls = allowNulls;
        this.isSystem = false;
    }
    
    public BaseCustomField(String name, String type, Boolean allowNulls, Boolean isSystem) {
        this.id = null;
        this.name = name;
        this.type = type;
        this.allowNulls = allowNulls;
        this.isSystem = isSystem;
    }
    
    public BaseCustomField(Long id, String name, String type, Boolean allowNulls) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.allowNulls = allowNulls;
        this.isSystem = false;
    }
    
    @Override
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
    
    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Boolean getAllowNulls() {
        return allowNulls;
    }

    public void setAllowNulls(Boolean allowNulls) {
        this.allowNulls = allowNulls;
    }

    @Override
    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }

    @Override
    public Set<SurveyDataFieldDTO> getAssociatedFields() {
        return Collections.emptySet();
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 83 * hash + Objects.hashCode(this.name);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BaseCustomField other = (BaseCustomField) obj;
        return Objects.equals(this.name, other.getName());
    }

    @Override
    public String toString() {
        return "DynamicTableField{" + "name=" + name + '}';
    }

    @Transient
    public Integer getMaxLength() {
        return IDynamicFieldDAO.getMaxLength(type);
    }    
}
