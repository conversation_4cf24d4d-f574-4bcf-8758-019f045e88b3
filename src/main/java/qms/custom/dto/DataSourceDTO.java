package qms.custom.dto;

import java.util.List;

public class DataSourceDTO {
    private List<IFormatFieldDTO> flexiFields;
    private List<IFormatFieldDTO> sectionFields;
    private String title;
    private String message;

    public DataSourceDTO() {}

    public DataSourceDTO(String message) {
        this.message = message;
    }

    public List<IFormatFieldDTO> getFlexiFields() {
        return flexiFields;
    }

    public void setFlexiFields(List<IFormatFieldDTO> flexiFields) {
        this.flexiFields = flexiFields;
    }

    public List<IFormatFieldDTO> getSectionFields() {
        return sectionFields;
    }

    public void setSectionFields(List<IFormatFieldDTO> sectionFields) {
        this.sectionFields = sectionFields;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


}
