package qms.custom.core;

import Framework.Config.Utilities;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.metamodel.Attribute;
import javax.persistence.metamodel.EntityType;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.dto.DynamicFieldDTO;
import qms.custom.dto.TableReferenceIdDTO;
import qms.custom.entity.DynamicField;
import qms.custom.entity.DynamicFieldTable;
import qms.custom.entity.DynamicMetadata;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
public class TableFactoryDynamicFields extends TableFactoryBase<String, DynamicFieldDTO> implements ITableFactory<String, DynamicFieldDTO> {

    private Long instanceTableId = null;
    private String entityName;
    private List<DynamicFieldDTO> fields;
    private Boolean allowComputedColumns = false;
    public TableFactoryDynamicFields() {
    }

    public TableFactoryDynamicFields(String entityName, List<DynamicFieldDTO> fields) {
        this.entityName = entityName;
        if ( !fields.stream().anyMatch(field -> Objects.equals(field.getName(), "id"))) {
            fields.add(0, new DynamicFieldDTO("id", "bigint", false, true));
        }
        if( !fields.stream().anyMatch(field -> Objects.equals(field.getName(), "status"))) {
            fields.add(0, new DynamicFieldDTO("status", "smallint", true, true));
        }
        if( !fields.stream().anyMatch(field -> Objects.equals(field.getName(), "deleted"))) {
            fields.add(0, new DynamicFieldDTO("deleted", "smallint", true, true));
        }
        if( !fields.stream().anyMatch(field -> Objects.equals(field.getName(), "created_date"))) {
            fields.add(0, new DynamicFieldDTO("created_date", "datetime", true, true)); 
        }
        if( !fields.stream().anyMatch(field -> Objects.equals(field.getName(), "last_modified_date"))) {
            fields.add(0, new DynamicFieldDTO("last_modified_date", "datetime", true,  true));        
        }
        
        this.fields = fields;
    }


    /**
     * Obtiene todos loe entities que utilizan el entity relacionado a los
     * campos dinamicos
     *
     * @return
     * @throws ClassNotFoundException
     * @throws NoSuchMethodException
     */
    @Override
    public Set<TableReferenceIdDTO> getJoinableEntities() throws ClassNotFoundException, NoSuchMethodException {
        final String dynamicEntity = getEntityName();
        Class dynamicEntityClass = Class.forName(dynamicEntity);
        Set<EntityType<?>> m = Utilities.getUntypedDAO().getEntityManager().getEntityManagerFactory().getMetamodel().getEntities();
        Set<TableReferenceIdDTO> tableIdMap = new HashSet<>();
        String idName;
        for (EntityType<?> object : m) {
            if (!Long.class.isAssignableFrom(object.getIdType().getJavaType())) {
                continue;
            }
            for (Attribute a : object.getDeclaredAttributes()) {
                if (a.getJavaType().isAssignableFrom(dynamicEntityClass)) {
                    Column an = object.getJavaType().getDeclaredMethod("getId").getAnnotation(Column.class);
                    if (an == null) {
                        idName = "id";
                    } else {
                        idName = an.name();
                    }
                    tableIdMap.add(new TableReferenceIdDTO(object.getJavaType().getAnnotation(Table.class).name(), idName));
                    break;
                }
            }
        }
        return tableIdMap;
    }

    @Override
    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    @Override
    public List<DynamicFieldDTO> getFields() {
        return fields;
    }

    public void setFields(List<DynamicFieldDTO> fields) {
        this.fields = fields;
    }

    @Override
    public String getInstanceTableName() throws QMSException {
        if (instanceTableId == null) {
            instanceTableId = IdFactory.getNextId("dynamic_field_table");
        }
        return "DYNTABLE_" + instanceTableId;
    }

    @Override
    public boolean persistMetadata(
            DynamicFieldTable table, DynamicFieldDTO field, String dataType, Long loggedUserId, IDynamicTableDAO dao
    )  throws QMSException {
        DynamicMetadata metadata = new DynamicMetadata(-1L);
        metadata.setCode(dao.getCodeSequence().next(DynamicMetadata.class));
        metadata.setDeleted(0);
        metadata.setDescription(dataType);
        metadata.setDynamicFieldCode(field.getName());
        metadata.setDynamicFieldId(field.getId());
        metadata.setDynamicFieldTableId(table.getId());
        metadata.setDynamicFieldTableName(table.getDescription());
        metadata.setStatus(DynamicMetadata.STATUS.ACTIVE.getValue());
        return dao.makePersistent(metadata) != null;
    }

    @Override
    public Boolean getAllowComputedColumns() {
        return allowComputedColumns;
    }

    @Override
    public String getParsedColumnType(DynamicFieldDTO field) {
        if( "bigint".equals(field.getType())
            || "smallint".equals(field.getType())
            || "datetime".equals(field.getType())
            || "varchar(MAX)".equals(field.getType())){
            return field.getType();
        }
        switch(DynamicField.DYNAMIC_FIELD_TYPE.fromField(field)) {
            case MARKDOWN:
                return TableFactoryColumnType.VARCHAR8000_COLUMN_TYPE.getValue();
            case CATALOG_MULTIPLE:
            case BIG_TEXT:
                return TableFactoryColumnType.VARCHAR4000_COLUMN_TYPE.getValue();
            case TIMESTAMP:
                return TableFactoryColumnType.TIMESTAMP.getValue();
            default:
                return TableFactoryColumnType.DEFAULT_COLUMN_TYPE.getValue();
        }
    }

    @Override
    public String getInstanceSchema() {
        return null;
    }

    @Override
    public String getPrimayKeyMame() {
        return "id";
    }

    @Override
    public Boolean getHasAuditColumns() {
        return true;
    }
    
    @Override
    public String getSessionStatement() {
        return null;
    }
}
