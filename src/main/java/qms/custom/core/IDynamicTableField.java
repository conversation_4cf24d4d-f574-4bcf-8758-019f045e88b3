package qms.custom.core;

import java.io.Serializable;
import java.util.Set;
import qms.form.dto.SurveyDataFieldDTO;

/**
 *
 * <AUTHOR>
 */
public interface IDynamicTableField<FieldType> extends Serializable {

    /**
     * El nombre del campo que se
     * guardará en base de datos
     *
     * @return
     */
    String getName();

    /**
     * El tipo de dato que tendra la
     * columna en base de datos
     *
     * @return
     */
    FieldType getType();
    

    /**
     * El id del objeto a partir del cual se configuró
     * esta interfaz.
     *
     * @return 
     */
    Long getId();
    
    /**
     * Si la columna acepta nulos
     *
     * @return 
     */
    Boolean getAllowNulls();
    
    /**
     * Si la columna es de sistema
     *
     * @return 
     */
    Boolean getIsSystem();


    Set<SurveyDataFieldDTO> getAssociatedFields();
}
