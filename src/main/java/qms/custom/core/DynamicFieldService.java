
package qms.custom.core;

import DPMS.Mapping.Persistable;
import Framework.Config.ITextHasValue;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.IUntypedDAO;
import com.sun.star.auth.InvalidArgumentException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicFieldsDTO;
import qms.custom.entity.DynamicField;
import qms.custom.entity.DynamicFieldValue;
import qms.util.EntityCommon;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 *
 * @deprecated Solo se utiliza en compoenentes LEGACY, para componentes creados en ANGULAR generar una instacia de "DynamicFieldController"
 */
public class DynamicFieldService extends CRUD_Generic<DynamicField> {

    public static final String RECALCULATE_TABLE_NAME = "recalculate";
    private DynamicFieldHandler handler = null;
    
    private String dynamicEntity = null;
    private String customEntity = null;

    private LinkedHashSet<String> values = null;
    
    /**
     * Ejemplo de configuración en el entity:
     * 
     *       @DynamicFields(
     *           compositeEntityClass = DocumentTypeDynamicField.class,
     *           entityMappedBy = "documentTypeId",,
     *           createViewPreffix = "",
     *           dynamicFieldMappedBy = "dynamicFieldId"
     *       )
     * 
     * @param dynamicEntityId: ID del entity que se relaciona con campos dinamicos
     * 
     * @return devuelve un arreglo de campos dinamicos en JSON: 
     *      {
     *          id: 'id de base de datos'
     *          name: 'codigo autogenerado unico',
     *          label: 'Etiqueta del campo',
     *          status: 1,
     *          mandatory: 1,
     *          type: 'catalog'
     *      }
     * */
    @SMDMethod
    public DynamicFieldsDTO getDynamicFields(Long dynamicEntityId) throws QMSException {
        return this.getHandler().getDynamicFields(
                null, 
                new HashMap<>(),
                getLoggedUserId(),
                dynamicEntityId
        );
    }
    
    @SMDMethod
    public DynamicFieldsDTO getDynamicFieldsByTableName(String dynamicTableName) {
        final Map<String, Object> params = new HashMap<>();
        return this.getHandler().getDynamicFieldsByTableName(dynamicTableName, null, params);
    }
    
    @SMDMethod
    public DynamicFieldsDTO getAllDynamicFields() {
        final Map<String, Object> params = new HashMap<>();
        return this.getHandler().getAllDynamicFields(false, null, params);
    }
    @SMDMethod
    public DynamicFieldsDTO getDynamicEntitySearchColumns() {
        final Map<String, Object> params = new HashMap<>();
        return this.getHandler().getDynamicEntitySearchColumns(null, params);
    }
    @SMDMethod
    public List<ITextHasValue> getDynamicEntityCatalog() {
        final Map<String, Object> params = new HashMap<>();
        return this.getHandler().getDynamicEntityCatalog(null, params);
    }
    @SMDMethod
    public Map getDynamicFieldData(Long referenceTableId, Long dynamicEntityId, String dynamicTableName) throws QMSException {
        final Map<String, Object> params = new HashMap<>();
        return this.getHandler().getDynamicFieldData(referenceTableId, dynamicEntityId, dynamicTableName, null, params, getLoggedUserId());
    }

    /**
     * Obtiene los valores dentro del SELECT para campos tipo "catalog"
     *
     * @param dynamicFieldId
     * @return
     */
    @SMDMethod
    public Map getDynamicFieldValues(Long dynamicFieldId) {
        return this.getHandler().getDynamicFieldValues(dynamicFieldId);
    }
    
    @SMDMethod
    public Map refresh(Long dataId, Long refreshId, String dynamicTableName) {
        return this.getHandler().refresh(dataId, refreshId, dynamicTableName);
    }
    
    
    
    /**
     * Inserta un valor de tabla dinamica
     * 
     * @param dataId    : Si vale -1 indica un nuevo renglon, caso contrario un UPDATE
     * @param data
     * @param dynamicTableName: Nombre de la tabla de los campos dinámicos
     * @return
     *      ID insertado
     *      TABLA donde se insertó
     * @throws NoSuchAlgorithmException
     * @throws InvalidArgumentException 
     */
    @SMDMethod
    public DynamicFieldInsertDTO persist(
        Long dataId, LinkedHashMap<String, Object> data, String dynamicTableName
    ) throws NoSuchAlgorithmException, InvalidArgumentException, QMSException {
        return this.getHandler().persist(dataId, data, dynamicTableName);
    }
    
        @Override
    protected void beforeSave(Persistable backup, boolean isNewObject, IUntypedDAO dao) {
        DynamicField entity = (DynamicField) backup;
        if (isNewObject) {
            try {
                entity.setCode(EntityCommon.getSimpleNextCode(entity));
            } catch (final QMSException ex) {
                throw new RuntimeException(ex);
            }
        } else {
            dao.HQL_updateByQuery("DELETE FROM " + DynamicFieldValue.class.getCanonicalName() + " c WHERE c.dynamicFieldId = " + entity.getId());
        }
        values = entity.getDynamicFieldValues();
    }

    @Override
    protected void afterSave(Persistable backup, boolean isNewObject, IUntypedDAO dao) {
        if (values == null) {
            return;
        }
        DynamicField b = (DynamicField) backup;
        DynamicFieldValue v;
        for (String s : values) {
            v = new DynamicFieldValue(-1L, b.getId(), s, DynamicFieldValue.STATUS.ACTIVE, DynamicFieldValue.IS_NOT_DELETED);
            try {
                v.setCode(EntityCommon.getNextCode(v));
            } catch (final QMSException ex) {
                throw new RuntimeException(ex);
            }
            dao.makePersistent(v);
        }
    }
    
    public String getDynamicEntity() {
        return dynamicEntity;
    }

    /**
     * @return the handler
     */
    public DynamicFieldHandler getHandler() {
        if (handler == null) {
            this.handler = new DynamicFieldHandler(dynamicEntity, customEntity, getBean(IDynamicFieldDAO.class));
        }
        return handler;
    }

    public void setDynamicEntity(String dynamicEntity) {
        this.dynamicEntity = dynamicEntity;
    }

    public String getCustomEntity() {
        return customEntity;
    }

    public void setCustomEntity(String customEntity) {
        this.customEntity = customEntity;
    }
        
}
