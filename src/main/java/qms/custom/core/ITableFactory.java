package qms.custom.core;

import java.util.List;
import java.util.Set;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.dto.TableReferenceIdDTO;
import qms.custom.entity.DynamicFieldTable;
import qms.framework.util.ClosableConnection;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 * @param <FieldDTO>
 */
public interface ITableFactory<FieldType, FieldDTO extends IDynamicTableField<FieldType>> {

    enum TableFactoryColumnType {
        DEFAULT_COLUMN_TYPE("varchar(4000)"), 
        VARCHAR4000_COLUMN_TYPE("varchar(4000)"),
        NVARCHAR4000_COLUMN_TYPE("nvarchar(4000)"),
        TIMESTAMP("datetime"),
        SMALLINT("smallint"),
        NUMERIC("numeric(18)"),
        BIGINT("bigint"),
        DECIMAL("decimal(18,4)"),
        VARCHARMAX_COLUMN_TYPE("varchar(MAX)"),
        VARCHAR255_COLUMN_TYPE("varchar(255)"),
        VARCHAR8000_COLUMN_TYPE("varchar(8000)");
        private final String value;
        private TableFactoryColumnType(String type) {
            this.value = type;
        }
        public String getValue() {
            return value;
        }
    }


    public String getTableNameMD5(String dynamicEntity, List<FieldDTO> dynamicFields);

    /**
     * Las columnas SPARSE, consumen menos espacio en disco, pero son más lentas de llenar.
     *
     * @return true si las columnas se crearan como SPARSE
     */
    public boolean isSparseColumnsEnabled();

    Set<TableReferenceIdDTO> getJoinableEntities() throws ClassNotFoundException, NoSuchMethodException;

    String getEntityName();

    String getParsedColumnType(FieldDTO dynamicField);

    List<FieldDTO> getFields();

    String getInstanceTableName() throws QMSException;
    
    String getPrimayKeyMame();
    
    Boolean getHasAuditColumns();

    String getInstanceSchema();
    
    String getMd5();

    void setMd5(String md5);
    
    ClosableConnection getConnection();
    
    void setConnection(ClosableConnection connection);
    
    String getSessionStatement();
    
    boolean persistMetadata(
            DynamicFieldTable table, FieldDTO field, String columnType, Long loggedUserId, IDynamicTableDAO dao
    )  throws QMSException;

    Boolean getAllowComputedColumns();
}
