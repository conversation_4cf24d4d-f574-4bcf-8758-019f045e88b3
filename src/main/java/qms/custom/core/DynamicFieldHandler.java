
package qms.custom.core;

import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.google.common.collect.ImmutableMap;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.Table;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityType;
import qms.activity.rest.ActivityDynamicFieldController;
import qms.activity.util.CommitmentTask;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.dto.DynamicFieldInfoDTO;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicFieldsDTO;
import qms.custom.dto.ValidationDTO;
import qms.custom.entity.DynamicFieldValue;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.CacheRegion;
import qms.util.EntityCommon;
import qms.util.QMSException;
import qms.util.interfaces.EntityType;
import qms.util.interfaces.IPersistableCode;
import qms.util.interfaces.IPersistableDescription;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 * @param <T>
 */
public class DynamicFieldHandler<T extends EntityDynamicFields> extends Loggable {

    private static final Logger LOGGER = Loggable.getLogger(DynamicFieldHandler.class);
        
    public static final String RECALCULATE_TABLE_NAME = "recalculate";
    
    private final String dynamicEntity;
    private final String customEntity;
    private final Class<? extends EntityType> dynamicEntityClass;
    private final Class<T> customEntityClass;
    private final DynamicTableHelper helper;
    private final IDynamicFieldDAO dao;

    /**
     * Debe utilizarse solo en contructores LEGACY para implementación en
     * pantallas de ANGULAR utilizar el del ejemplo:
     *
     *      DynamicFieldHandler(
     *          Class<? extends EntityType> dynamicEntity,
     *          Class<? extends EntityDynamicFields>customEntity,
     *          IDynamicFieldDAO dao
     *      )
     *
     * @deprecated Utilizar el que recibe <Class, Class, IDynamicFieldDAO>
     * @param dynamicEntity
     * @param customEntity
     * @param dao
     */
    public DynamicFieldHandler(
        String dynamicEntity, String customEntity,
        IDynamicFieldDAO dao
    ) {
        this.dynamicEntity = dynamicEntity;
        this.customEntity = customEntity;
        this.dao = dao;
        this.helper = new DynamicTableHelper(dao);
        this.dynamicEntityClass = null;
        this.customEntityClass = null;
    }

    public DynamicFieldHandler(
        Class<? extends EntityType> dynamicEntity,
        Class<T> customEntity,
        IDynamicFieldDAO dao
    ) {
        this.dynamicEntity = dynamicEntity.getCanonicalName();
        this.customEntity = customEntity.getCanonicalName();
        this.dao = dao;
        this.helper = new DynamicTableHelper(dao);
        this.dynamicEntityClass = dynamicEntity;
        this.customEntityClass = customEntity;
    }
    /**
     * Ejemplo de configuración en el entity:
     * 
     * @param loggedUserId
     *       @DynamicFields(
     *           compositeEntityClass = DocumentTypeDynamicField.class,
     *           entityMappedBy = "documentTypeId",,
     *           createViewPreffix = "",
     *           dynamicFieldMappedBy = "dynamicFieldId"
     *       )
     * 
     * @return devuelve un arreglo de campos dinamicos en JSON:
     *      {
     *          id: 'id de base de datos'
     *          name: 'codigo autogenerado unico',
     *          label: 'Etiqueta del campo',
     *          status: 1,
     *          mandatory: 1,
     *          type: 'catalog'
     *      }
     * */
    public DynamicFieldsDTO getDynamicFields(String extraCondition, Map<String, Object> params, Long loggedUserId, List<Long> dynamicEntityIds) {
        return dao.getDynamicFields(
                dynamicEntity, 
                extraCondition,
                customEntity,
                params,
                loggedUserId, 
                dynamicEntityIds
        );
    }
    
    public DynamicFieldsDTO getDynamicFields(
            String extraCondition, Map<String, Object> params, Long loggedUserId, Long dynamicEntityId
    ) throws QMSException {
        return dao.getDynamicFields(
                dynamicEntity, 
                extraCondition,
                customEntity,
                params,
                loggedUserId, 
                dynamicEntityId
        );
    }

    public DynamicFieldsDTO getDynamicFieldsByCode(String dynamicEntityCode, Long loggedUserId) throws QMSException  {
        return dao.getDynamicFields(dynamicEntity, dynamicEntityCode, customEntity, loggedUserId);
    }
    
    public DynamicFieldsDTO generateDatabaseObjects(Long dynamicEntityId, Long loggedUserId) throws QMSException  {
        return dao.generateDatabaseObjects(
                dynamicEntityId, 
                dynamicEntity, 
                customEntity,
                loggedUserId
        );
    }
    
    /**
     * Devuelve un arreglo de campos dinamicos en JSON: 
     *      {
     *          id: 'id de base de datos'
     *          name: 'codigo autogenerado unico',
     *          label: 'Etiqueta del campo',
     *          status: 1,
     *          mandatory: 1,
     *          type: 'catalog'
     *      }
     * 
     * Este metodo NO devuelve información de catalogos para modificar 
     * campos dinamicos de tipo CATALOG o CATALOG_MULTIPLE
     * 
     * @param dynamicTableName
     * @return 
     */
    public DynamicFieldsDTO getDynamicFieldsByTableName(String dynamicTableName, String extraCondition, Map<String, Object> params) {
        if (dynamicTableName == null) {
            return new DynamicFieldsDTO(false);
        }
        return dao.getDynamicFieldsByTableName(
                dynamicTableName, 
                customEntity,
                false,
                false,
                extraCondition,
                params
        );
    }
    
    /**
     * Devuelve un arreglo de campos dinamicos en JSON: 
     *      {
     *          id: 'id de base de datos'
     *          name: 'codigo autogenerado unico',
     *          label: 'Etiqueta del campo',
     *          status: 1,
     *          mandatory: 1,
     *          type: 'catalog'
     *      }
     * 
     * Este metodo SI devuelve información de catalogos para modificar 
     * campos dinamicos de tipo CATALOG o CATALOG_MULTIPLE
     * 
     * @param dynamicTableName
     * @return 
     */
    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName, 
            String extraCondition, 
            Map<String, Object> params, 
            Long loggedUserId
    ) {
        if (dynamicTableName == null) {
            return new DynamicFieldsDTO(false);
        }
        return dao.getDynamicFieldsByTableName(dynamicTableName, dynamicEntity, customEntity, false, dynamicEntity != null, extraCondition, params, loggedUserId);
    }

    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName,
            String customEntity,
            String extraCondition,
            Map<String, Object> params,
            Long loggedUserId
    ) {
        if (dynamicTableName == null) {
            return new DynamicFieldsDTO(false);
        }
        return dao.getDynamicFieldsByTableName(dynamicTableName, dynamicEntity, customEntity, false, dynamicEntity != null, extraCondition, params, loggedUserId);
    }
    
    public DynamicFieldsDTO getAllDynamicFields(
            Boolean loadOptions, 
            String extraCondition, 
            Map<String, Object> params
    ) {
        return dao.getDynamicFieldsByTableName(
                null, 
                customEntity,
                false,
                loadOptions,
                extraCondition,
                params
        );
    }

    public DynamicFieldsDTO getDynamicEntitySearchColumns(String extraCondition, Map<String, Object> params) {
        if (
            (dynamicEntityClass == null && dynamicEntity != null)
            || (customEntityClass == null && customEntity != null)
        ) {
            return dao.getDynamicFieldsByTableName(null, dynamicEntity, customEntity, true, dynamicEntity != null, extraCondition, params, null);
        }
        return dao.getDynamicFieldsByTableName(null, dynamicEntityClass, customEntityClass, true, extraCondition, params, null);
    }

    public DynamicFieldsDTO getDynamicEntitySearchColumns(String extraCondition, Map<String, Object> params, Long loggedUserId) {
        if (
            (dynamicEntityClass == null && dynamicEntity != null)
            || (customEntityClass == null && customEntity != null)
        ) {
            return dao.getDynamicFieldsByTableName(null, dynamicEntity, customEntity, true, dynamicEntity != null, extraCondition, params, loggedUserId);
        }
        return dao.getDynamicFieldsByTableName(null, dynamicEntityClass, customEntityClass, true, extraCondition, params, loggedUserId);
    }

    public List<ITextHasValue> getDynamicEntityCatalog(String extraCondition, Map<String, Object> params) {
        ValidationDTO v = isDynamicEntityValid(dynamicEntity);
        if(!v.isValid()) {
            return Utilities.EMPTY_LIST;
        }
        params.put("status", EntityCommon.getActiveStatus(dynamicEntity).getValue());
        if (extraCondition == null) {
            extraCondition = "";
        }
        final List<ITextHasValue> list = dao.HQL_findByQuery(""
            + " SELECT new " + TextHasValue.class.getCanonicalName() + "("
                + " t.code,"
                + " t.description,"
                + " t.id"
            + " )"
            + " FROM " + dynamicEntity + " t "
            + " WHERE"
                + " t.deleted = 0"
                + " AND t.status = :status"
                + extraCondition,
            params,
            true,
            CacheRegion.DYNAMIC_FIELD,
            0
        );
        return list;
    }
    /**
     * @param referenceTableId: Es el ID del entity principal (Ej. activityId, documentId)
     * @param dynamicEntityId: Es el ID del tipo del entity que configura los campos dinamicos (Ej. activityTypeId, documentTypeId)
     * @param dynamicTableName
     * @param extraCondition
     * @param params
     * @param loggedUserId
     * @return 
     */
    public Map<String, Object> getDynamicFieldData(
            Long referenceTableId, 
            Long dynamicEntityId, 
            String dynamicTableName, 
            String extraCondition,
            Map<String, Object> params,
            Long loggedUserId
    ) throws QMSException {
        return getDynamicFieldInfo(referenceTableId, dynamicEntityId, dynamicTableName, null, extraCondition, params, loggedUserId).getData();
    }

    /**
     * @param referenceTableId: Es el ID del entity principal (Ej. activityId, documentId)
     * @param dynamicEntityId: Es el ID del tipo del entity que configura los campos dinamicos (Ej. activityTypeId, documentTypeId)
     * @param dynamicTableName
     * @param dynamicTableNameId: Es el ID del registro en la tabla dinamica (Ej. la columna "id" en la tabla "DYTABLE_#")
     * @param extraCondition
     * @param params
     * @param loggedUserId
     * @return 
     */
    public Map<String, Object> getDynamicFieldData(
            final Long referenceTableId,
            final Long dynamicEntityId,
            final String dynamicTableName,
            final Long dynamicTableNameId,
            final String extraCondition,
            final Map<String, Object> params,
            final Long loggedUserId
    ) throws QMSException {
        return getDynamicFieldInfo(referenceTableId, dynamicEntityId, dynamicTableName, dynamicTableNameId, extraCondition, params, loggedUserId).getData();
    }

    public DynamicFieldInfoDTO getDynamicFieldInfo(
            Long referenceTableId,
            Long dynamicEntityId,
            String dynamicTableName,
            String extraCondition,
            Map<String, Object> params,
            Long loggedUserId
    ) throws QMSException {
        return getDynamicFieldInfo(referenceTableId, dynamicEntityId, dynamicTableName, null, extraCondition, params, loggedUserId);
    }

    public DynamicFieldInfoDTO getDynamicFieldInfo(
            final Long referenceTableId,
            final Long dynamicEntityId,
            final String dynamicTableName,
            final Long dynamicTableNameId,
            final String extraCondition,
            final Map<String, Object> params,
            final Long loggedUserId
    ) throws QMSException {
        final DynamicFieldInfoDTO info = new DynamicFieldInfoDTO();
        info.setData(Utilities.EMPTY_MAP);
        ValidationDTO dynVal = isDynamicEntityValid(dynamicEntity);
        if(!dynVal.isValid() || dynamicTableName == null || "null".equals(dynamicTableName)) {
            return info;
        }
        ValidationDTO cusVal = isCustomEntityValid(customEntity);
        if(!cusVal.isValid()) {
            return info;
        }
        
        DynamicFieldsDTO m;
        if (RECALCULATE_TABLE_NAME.equals(dynamicTableName)) {  
            m = dao.getDynamicFields(
                    dynamicEntity,
                    null,
                    customEntity,
                    params,
                    loggedUserId,
                    dynamicEntityId
            );   
        } else { 
            m = dao.getDynamicFieldsByTableName(dynamicTableName, dynamicEntity, customEntity, false, dynamicEntity != null, extraCondition, params, loggedUserId);   
        }
        if (m.getDynamicTableName()==null) {
            return info;
        }
        final Map<String, Object> data = dao.selectDynReference(
                referenceTableId,
                cusVal.getEntityClass(),
                m.getDynamicFields(), 
                m.getDynamicTableName(),
                dynamicTableNameId
        );
        info.setData(data);
        info.setFields(m);
        return info;
    }
    

    public Map<String, String> getDynamicFieldDataWithLabels(
            final Long referenceTableId, 
            final Long dynamicEntityId,
            final String dynamicTableName,
            final String extraCondition, 
            final Map<String, Object> params,
            final Long loggedUserId
    ) throws QMSException  {
        final DynamicFieldInfoDTO info = getDynamicFieldInfo(
            referenceTableId,
            dynamicEntityId,
            dynamicTableName,
            extraCondition,
            params,
            loggedUserId
        );
        final Map<String, Object> data = info.getData();
        if (data == null || data.isEmpty()) {
            return Utilities.EMPTY_MAP;
        }
        if (data.containsKey("dynamicTableName")) {
            data.remove("dynamicTableName");
        }
        final DynamicFieldsDTO dynamicFieldInfo = info.getFields();
        final LinkedHashMap<String, String> result = new LinkedHashMap<>(dynamicFieldInfo.getDynamicFields().size());
        dynamicFieldInfo.getDynamicFields().forEach(field -> {
            final Object valueObject = data.get(field.getName());
            if (valueObject == null) {
                result.put(field.getLabel(), "-");
            } else {
                final String value = valueObject.toString();
                result.put(field.getLabel(), value);
            }
        });
        return result;
    }
    
    /**
     * Obtiene los valores dentro del SELECT para campos tipo "catalog"
     * 
     * @param dao
     * @param dynamicFieldId
     * @return 
     */
    public Map getDynamicFieldValues(Long dynamicFieldId) {
        Map m = new HashMap();
        m.put("domType", "select");
        m.put("selectDomId", null); // <--- se realiza "SET" desde el JS (legacy)
        m.put("values", getDynamicFieldValues(dao, dynamicFieldId));
        return m;
    }

    public static List<String> getDynamicFieldValues(IUntypedDAO dao, Long dynamicFieldId) {
        return dao.HQL_findByQuery(""
            + " SELECT c.description "
            + " FROM " + DynamicFieldValue.class.getCanonicalName() + " c "
            + " WHERE c.dynamicFieldId = :dynamicFieldId",
            ImmutableMap.of(
                "dynamicFieldId",
                dynamicFieldId
            ),
            true, 
            CacheRegion.DYNAMIC_FIELD,
            0
        );
    }

    /**
     * Actualiza la "fila" que tenga el "id" (dataId) con el id de la tabla "dueña" de ese renglón,
     * por ejemplo para "document" con tabla "DYNTABLE_1":
     *
     * La tabla DYNTABLE_1 tiene los ids "id1" y "id2", un id pertenece a "document" y otro a "request", este
     * metodo coloca el "document_id" en el "id1" de la tabla "DYNTABLE_1"
     *
     * @param dataId Es el valor de la columna "id" dentro de la tabla "dynamicTableName"
     * @param refreshId Es el "id" del entity (Ej. "activity.activity_id", "document.id", "request.id", "meeting.meeting_id", etc.)
     * @param dynamicTableName Es la tabla dinamica (Ej. DYNTABLE_1, DYNTABLE_2, DYNTABLE_3, etc.)
     *
     * @return
     */
    public Map refresh(Long dataId, Long refreshId, String dynamicTableName) {
        ValidationDTO cusVal = isCustomEntityValid(customEntity);
        if (!cusVal.isValid()) {
            return Utilities.EMPTY_MAP;
        }
        if (dynamicTableName == null || dynamicTableName.trim().isEmpty() || "null".equals(dynamicTableName)) {
            return Utilities.EMPTY_MAP;
        }
        try {
            Map m = new HashMap();
            DynamicFieldInsertDTO dto = new DynamicFieldInsertDTO();
            
            dto.setId(dataId);
            dto.setTableName(dynamicTableName);
            EntityDynamicFields entity = (EntityDynamicFields) cusVal.getEntityClass().getConstructor().newInstance();
            entity.setId(refreshId);
            dao.updateDynReference(dto, entity);
            return m;
        } catch (InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException | SecurityException ex) {
            LOGGER.error("Falla al tratar de refrescar la nueva relacion del entity '" + cusVal.getEntityClass().getCanonicalName() 
                    + "', dataId: " + dataId + ", dynamicTableName: " + dynamicTableName, ex);
        }
        return Utilities.EMPTY_MAP;
    }
    
    
    
    /**
     * Inserta un valor de tabla dinamica
     * 
     * @param dataId    : Si vale -1 indica un nuevo renglon, caso contrario un UPDATE
     * @param data
     * @param dynamicTableName: Nombre de la tabla de los campos dinámicos
     * @return
     *      ID insertado
     *      TABLA donde se insertó 
     */
    public DynamicFieldInsertDTO persist(
            Long dataId, Map<String, Object> data, String dynamicTableName
    ) throws QMSException {
        return helper.persist(dataId, data, dynamicTableName);
    }
        
    /**
     * Devuelve un objeto "DynamicEntityValidator" que dice 
     * si el parametro "dynamicEntity" es valido
     * 
     * @param dynamicEntity
     * @return 
     */
    public static ValidationDTO isDynamicEntityValid(String dynamicEntity) {
        ValidationDTO r = new ValidationDTO(false);
        if (dynamicEntity == null) {
            LOGGER.error("QMS: "
                + "Invalid configuration, 'dynamicEntity' is NULL. "
                + "Please add <param name='dynamicEntity'>qms.module.entity.EntityName</param> to the proper struts.xml configuration.", 
                new Exception()
            );
            return r;
        }
        Class c;
        try {
            c = Class.forName(dynamicEntity);
            r.setEntityClass(c);
        } catch (ClassNotFoundException ex) {
            LOGGER.error("QMS: Invalid entity '" + dynamicEntity + "', 'dynamicEntity' does not exists.", ex);
            return r;
        }
        DynamicFields a = (DynamicFields) c.getAnnotation(DynamicFields.class);
        if(a == null) {
            LOGGER.error("QMS: Missing 'DynamicFields' annotation at '" + dynamicEntity + "'", new Exception());
            return r;
        }
        if (!IPersistableCode.class.isAssignableFrom(c)) {
            LOGGER.warn("QMS:"
                    + " Invalid configuration for entity '" + dynamicEntity + "', 'dynamicEntity' should implement interface"
                    + " '" + IPersistableCode.class.getCanonicalName() + "'.", new Exception());
        }
        if (!IPersistableDescription.class.isAssignableFrom(c)) {
            LOGGER.warn("QMS:"
                    + " Invalid configuration for entity '" + dynamicEntity + "', 'dynamicEntity' should implement interface"
                    + " '" + IPersistableDescription.class.getCanonicalName() + "'.", new Exception());
        }
        r.setValid(true);
        return r;
    }
    
    public static ValidationDTO isCustomEntityValid(String customEntity) {
        ValidationDTO r = new ValidationDTO(false);
        if (customEntity == null) {
            LOGGER.error("QMS: "
                + "Invalid configuration, 'customEntity' is NULL. "
                + "Please add <param name='customEntity'>qms.module.entity.EntityName</param> to the proper struts.xml configuration.", 
                new Exception()
            );
            return r;
        }
        Class c;
        try {
            c = Class.forName(customEntity);
            r.setEntityClass(c);
        } catch (ClassNotFoundException ex) {
            LOGGER.error("QMS: Invalid entity '" + customEntity + "', 'customEntity' does not exists.", ex);
            return r;
        }
        if(!EntityDynamicFields.class.isAssignableFrom(c)) {
            LOGGER.error("QMS:"
                    + " Invalid configuration for entity '" + customEntity + "', 'customEntity' must implement interface"
                    + " '" + EntityDynamicFields.class.getCanonicalName() + "'.");
            return r;
        }
        r.setValid(true);
        return r;
    }

    public static ValidationDTO isDynamicSearchValid(String entityClassName) {
        ValidationDTO r = new ValidationDTO(false);
        if (entityClassName == null) {
            LOGGER.error("QMS: "
                + "Invalid configuration, 'entity' is NULL. ",
                new Exception()
            );
            return r;
        }
        Class c;
        try {
            c = Class.forName(entityClassName);
            r.setEntityClass(c);
        } catch (ClassNotFoundException ex) {
            LOGGER.error("QMS: Invalid entity name '" + entityClassName + "', entity name does not exists.", ex);
            return r;
        }
        return isDynamicSearchValid(c);
    }

    public static ValidationDTO isDynamicSearchValid(Class entityClass) {
        ValidationDTO r = new ValidationDTO(false);
        r.setEntityClass(entityClass);
        DynamicSearch a = (DynamicSearch) entityClass.getAnnotation(DynamicSearch.class);
        if (a == null) {
            LOGGER.trace("QMS: Missing 'DynamicSearch' annotation at '" + entityClass.getCanonicalName() + "', this entity canot be filtered by dynamic fields ");
            return r;
        }
        if (!populateTableName(entityClass, r)) {
            LOGGER.trace("QMS: Missing 'Table' annotation at '" + entityClass.getCanonicalName() + "', this entity canot be filtered by dynamic fields ");
            return r;
        }
        r.setValid(true);
        return r;
    }

    private static boolean populateTableName(Class c, ValidationDTO r) {
        Table b = (Table) c.getAnnotation(Table.class);
        if(b == null) {
            return false;
        }
        r.setEntityTableName(b.name());
        return true;
    }
    
    private Map<String, Object> getActivityDynamicFieldData(Long activityId) throws QMSException {
        return dao.HQL_findSimpleMap(" "
            + " SELECT new map("
                + " a.typeId AS activityTypeId,"
                + " a.dynamicTableName AS dynamicTableName,"
                + " a.dynamicTableNameId AS dynamicTableNameId,"
                + " t.dynamicFieldTableName AS dynamicTableNameLatest,"
                + " a.commitmentTask AS commitmentTask,"
                + " a.recurrenceId AS recurrenceId"
            + " )"
            + " FROM " + Activity.class.getCanonicalName() + " a "
            + " JOIN a.type t "
            + " WHERE a.id = :id",
            ImmutableMap.of("id", activityId),
            true,
            CacheRegion.ACTIVITY,
            0
        );
    }

    public PersistableDynamicEntity getDynamicFieldData(Long workflowId, Long activityId) throws QMSException {
        Map<String, Object> info = getActivityDynamicFieldData(activityId);
        if (info.get("activityTypeId") == null || info.get("dynamicTableName") == null || info.get("recurrenceId") != null) {
            Long recurrenceId = null;
            // Se quitó la clonación de los campos dinámcos de las actividades desde planeaión por eso se agrega esta validación
            if (!((Integer)info.get("commitmentTask")).equals(CommitmentTask.PROGRAM.getValue()) && info.get("recurrenceId") != null) {
                recurrenceId = (Long) info.get("recurrenceId");
                info = getActivityDynamicFieldData(recurrenceId);
            }
            if (info.get("activityTypeId") == null || info.get("dynamicTableName") == null) {
                return null;
            }
            if (recurrenceId != null) {
                activityId = recurrenceId;
            }
        }
        final String dynamicTableName = (String) info.get("dynamicTableName");
        final String dynamicTableNameLatest = (String) info.get("dynamicTableNameLatest");
        Long dynamicTableNameId = (Long) info.get("dynamicTableNameId");
        final Long activityTypeId = (Long) info.get("activityTypeId");
        final Map<String, Object> params = new HashMap<>();
        final String workflowCondtion;
        if (workflowId != null && workflowId > 0) {
            params.put("workflowId", workflowId);
            workflowCondtion = ActivityDynamicFieldController.WORKFLOW_CONDITION;
        } else {
            workflowCondtion = null;
        }
        Map<String, Object> dynamicFieldData = getDynamicFieldData(
            activityId,
            activityTypeId,
            dynamicTableName,
            dynamicTableNameId,
            workflowCondtion,
            params,
            SecurityUtils.getLoggedUserId()
        );
        PersistableDynamicEntity<ActivityType> data = new DummyPersistableDynamicEntity();
        data.setId(activityId);
        data.setType(new ActivityType(activityTypeId));
        data.setDynamicTableName(dynamicTableName);
        data.setDynamicTableNameLatest(dynamicTableNameLatest);
        Object rawId = dynamicFieldData.remove("id");
        if (rawId == null) {
            return null;
        } else {
            dynamicTableNameId = Long.parseLong(rawId.toString());
        }
        data.setDynamicTableNameId(dynamicTableNameId);
        data.setDynamicFieldData(dynamicFieldData);
        return data;
    }

    public IDynamicFieldDAO getDynamicFieldDAO() {
        return dao;
    }

    public DynamicTableHelper getHelper() {
        return helper;
    }


}
