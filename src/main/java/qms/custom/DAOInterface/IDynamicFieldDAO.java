package qms.custom.DAOInterface;

import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.util.List;
import java.util.Map;
import qms.custom.core.EntityDynamicFields;
import qms.custom.dto.DynamicFieldDTO;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicFieldLiteDTO;
import qms.custom.dto.DynamicFieldsDTO;
import qms.custom.dto.DynamicGridConfigSQL;
import qms.custom.dto.IDynamicGridConfigHQL;
import qms.custom.entity.DynamicField;
import qms.framework.bulk.util.BulkConstants;
import qms.util.QMSException;
import qms.util.interfaces.EntityType;
import qms.util.interfaces.IGridFilter;

/**
 * Interfaz para obtener BEAN del DAO
 *
 * <AUTHOR>
 */
@Implementation(name = "DynamicFieldDAO")
public interface IDynamicFieldDAO extends IGenericDAO<DynamicField, Long> {
    
    public static Integer getMaxLength(String type) {
        if (type == null) {
            return null;
        }
        return getMaxLength(DynamicField.DYNAMIC_FIELD_TYPE.fromType(type));
    }
    
    public static Integer getMaxLength(DynamicField.DYNAMIC_FIELD_TYPE type) {
        if (type == null) {
            return null;
        }
        switch (type) {
            case TEXT:
            case LINK:
                return BulkConstants.MAX_DYNAMIC_FIELD_TEXT_CHARACTERS;     // <-- 255
            case CATALOG:
            case BIG_TEXT:
            case CATALOG_MULTIPLE:
                return BulkConstants.MAX_DYNAMIC_FIELD_BIG_TEXT_CHARACTERS; // <-- 4000
            case MARKDOWN:
                // ToDo: Partir los valores de tipo MARKDOWN en 2 de 4000
                return BulkConstants.MAX_DYNAMIC_FIELD_VARCHAR_8000;        // <-- 8000
            default:
                return null;
        }
    }

    public void updateDynReference(DynamicFieldInsertDTO dto, EntityDynamicFields entity);

    public Map<String, String> selectDynReference(Long referenceTableId, Class<? extends EntityDynamicFields> customEntity, List<DynamicFieldDTO> dynamicFields, String dynamicTableName);
    public Map<String, String> selectDynReference(Long referenceTableId, Class<? extends EntityDynamicFields> customEntity, List<DynamicFieldDTO> dynamicFields, String dynamicTableName, Long dynamicTableNameId);

    public void setDynamicGridConfigSQL(IGridFilter filter, String referenceEntityClassName, IDynamicGridConfigHQL hqlConfig, DynamicGridConfigSQL sqlConfig);

    public DynamicFieldsDTO getDynamicFields(
            String dynamicEntity, 
            String extraCondition,
            String customEntity,
            Map<String, Object> params,
            Long loggedUserId, 
            Long dynamicEntityId
    ) throws QMSException;
    
    public DynamicFieldsDTO getDynamicFields(
            String dynamicEntity, 
            String extraCondition,
            String customEntity,
            Map<String, Object> params,
            Long loggedUserId, 
            List<Long> dynamicEntityIds
    );
    
    public DynamicFieldsDTO getDynamicFields(
            String dynamicEntity, 
            String dynamicEntityCode,
            String customEntity,
            Long loggedUserId
    ) throws QMSException;
    
    public DynamicFieldsDTO generateDatabaseObjects(
            Long dynamicEntityId, 
            String dynamicEntity, 
            String customEntity, 
            Long loggedUserId
    ) throws QMSException;

    public IDynamicGridConfigHQL getDynamicGridConfig(Class clazz);

    public IDynamicGridConfigHQL getDynamicGridConfig(Class entityClass, String entityAlias, String HQL);

    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName,
            String customEntity, 
            Boolean searchAlwaysVisible,
            Boolean loadOptions,
            String extraCondition,
            Map<String, Object> params
    );
    
    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName,
            Class<? extends EntityType> dynamicEntity, 
            Class<? extends EntityDynamicFields> customEntity,
            Boolean searchAlwaysVisible,
            String extraCondition,
            Map<String, Object> params,
            Long loggedUserId
    );
    
    public DynamicFieldsDTO getDynamicFieldsByTableName(
            String dynamicTableName,
            String dynamicEntity, 
            String customEntity, 
            Boolean searchAlwaysVisible,
            Boolean loadOptions,
            String extraCondition,
            Map<String, Object> params,
            Long loggedUserId
    );
    
    public DynamicFieldsDTO getAllValues(Boolean loadOptions);
    
    public List<DynamicFieldLiteDTO> getActivesLite();
 
}
