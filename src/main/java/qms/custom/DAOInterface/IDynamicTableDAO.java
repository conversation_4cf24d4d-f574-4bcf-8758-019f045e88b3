package qms.custom.DAOInterface;

import Framework.DAO.IUntypedDAO;
import Framework.DAO.Implementation;
import java.sql.Connection;
import java.util.List;
import qms.custom.core.IDynamicTableField;
import qms.custom.core.ITableFactory;
import qms.custom.dto.DynamicTableBuildDTO;
import qms.custom.entity.DynamicFieldTable;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
@Implementation(name = "DynamicTableDAO")
public interface IDynamicTableDAO<FieldType, FieldDTO extends IDynamicTableField<FieldType>> extends IUntypedDAO {

    DynamicTableBuildDTO getFactoryBuildedName(ITableFactory<FieldType, FieldDTO> factory, Long loggedUserId) throws QMSException;

    DynamicFieldTable getFactoryBuilded(ITableFactory<FieldType, FieldDTO> factory, Long loggedUserId);

    Long updateImportValues(
            Long sourceSurveyId,
            Long referenceCommonTableId,
            String referenceCommonTable,
            String sourceTable,
            String targetTable,
            List<String> columns
    );

    List<String> getDynamicTableColumns(String dynamicTableName);

    /**
     *  Genera y ejecuta un INSERT / SELECT desde `sourceTable` a `targetTable`
     *
     * ¡IMPORTANTE!
     *      Asume que `columns` contiene SOLO columnas en común entre ambas tablas.
     *
     * @param sourceTable
     * @param targetTable
     * @param columns
     * @return
     * @throws QMSException
     */
    Integer importValues(String sourceTable, String targetTable, List<String> columns) throws QMSException;

    /**
     *
     * @param sourceTable
     * @param targetTable
     * @param sourceColumns
     * @param targetColumns
     * @return
     * @throws QMSException
     */
    Integer importValues(
            final String sourceTable,
            final String targetTable,
            final List<String> sourceColumns,
            final List<String> targetColumns
    ) throws QMSException;

    void updateSynonym(String tableName, String synonym) throws QMSException;

    void createSynonym(String tableName, String synonym) throws QMSException;

    void addColumn(final ITableFactory<FieldType, FieldDTO> factory, final String tableName, final FieldDTO column);

    Integer createDbView(
            ITableFactory<FieldType, FieldDTO> factory,
            String schemaName,
            String viewName,
            String viewSql
    );

    Boolean existsDatabaseObject(
            String schemaName,
            String viewName,
            Connection connection
    );

    Long updateValues(
            Long referenceCommonTableId,
            String referenceCommonTable,
            String sourceTable,
            String targetTable,
            List<String> sourceColumns,
            List<String> targetColumns
    );

    Long updateValues(
            Long referenceCommonTableId,
            String referenceCommonTable,
            String sourceTable,
            String targetTable,
            List<String> columns
    );


}
