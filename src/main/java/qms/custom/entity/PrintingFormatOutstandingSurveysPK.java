/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class PrintingFormatOutstandingSurveysPK implements Serializable {    

    private Long printingFormatId;
    private Long outstandingSurveysId;

    public PrintingFormatOutstandingSurveysPK() {
    }

    public PrintingFormatOutstandingSurveysPK(Long printingFormatId, Long outstandingSurveysId) {
        this.printingFormatId = printingFormatId;
        this.outstandingSurveysId = outstandingSurveysId;
    }
    
    @GroundId
    @Basic(optional = false)
    @Column(name = "printing_format_id")
    public Long getPrintingFormatId() {
        return printingFormatId;
    }

    public void setPrintingFormatId(Long printingFormatId) {
        this.printingFormatId = printingFormatId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "outstanding_surveys_id")
    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 67 * hash + Objects.hashCode(this.printingFormatId);
        hash = 67 * hash + Objects.hashCode(this.outstandingSurveysId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PrintingFormatOutstandingSurveysPK other = (PrintingFormatOutstandingSurveysPK) obj;
        return true;
    }

    @Override
    public String toString() {
        return "PrintingFormatOutstandingSurveysPK{" + "printingFormatId=" + printingFormatId + ", outstandingSurveysId=" + outstandingSurveysId + '}';
    }
    
}
