/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.entity;

import Framework.Config.CompositeStandardEntity;
import javax.persistence.Cacheable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Entity
@Cacheable
@Cache(region = CacheConstants.PRINTING_FORMAT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "printing_format_survey_field")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class PrintingFormatSurveyField extends CompositeStandardEntity<PrintingFormatSurveyFieldPK> {    

    private PrintingFormatSurveyFieldPK id;

    public PrintingFormatSurveyField() {
    }

    public PrintingFormatSurveyField(Long printingFormatId, Long surveyAnswerMetadataId) {
        this.id = new PrintingFormatSurveyFieldPK(printingFormatId, surveyAnswerMetadataId);
    }

    @Override
    public PrintingFormatSurveyFieldPK identifuerValue() {
        return id;
    }

    @Override
    @EmbeddedId
    public PrintingFormatSurveyFieldPK getId() {
        return id;
    }

    @Override
    public void setId(PrintingFormatSurveyFieldPK id) {
        this.id = id;
    }

        
}
