
package qms.custom.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import java.util.Date;
import java.util.LinkedHashSet;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import mx.bnext.core.util.Loggable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.custom.dto.DynamicFieldDTO;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.initialload.ICreationTypeAware;
import qms.framework.util.CacheConstants;
import qms.util.CodePrefix;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 */

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.DYNAMIC_FIELD, usage = CacheConcurrencyStrategy.READ_WRITE)
@LocalizedEntity
@Table(name = "dynamic_field")
@CodePrefix("dynamicField")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class DynamicField extends StandardEntity<DynamicField> implements IAuditableEntity, ICreationTypeAware {
    public static enum DYNAMIC_FIELD_TYPE {
        CATALOG, CATALOG_MULTIPLE, TEXT, BIG_TEXT, TIMESTAMP, LINK, HTML, MARKDOWN;
        @Override
        public String toString() {
            return this.getValue();
        }
        public String getValue() {
            return name().toLowerCase().replaceAll("_", "-");
        }
        public static DYNAMIC_FIELD_TYPE fromField(DynamicFieldDTO field) {
            DYNAMIC_FIELD_TYPE type =  fromType(field.getType());
            if(type == null) {
                Loggable.getLogger(DynamicField.class).trace("Invalid FIELD_TYPE for '{}' ", field);
            }
            return type;
        }
        public static DYNAMIC_FIELD_TYPE fromType(String fieldType) {
            try {
                return DYNAMIC_FIELD_TYPE.valueOf(fieldType.toUpperCase().replaceAll("-", "_"));
            } catch (IllegalArgumentException e) {
                Loggable.getLogger(DynamicField.class).trace("Invalid FIELD_TYPE value '{}' ", fieldType);
                return null;
            }
        }
    }
    private String code;//nombre
    private String description;//etiqueta
    private Integer status;
    private Integer deleted;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Integer mandatory;
    private Integer searchAlwaysVisible;
    private String dynamicFieldType;
    private Integer creationType;
    
    //@Transient - dynamicFields 
    private LinkedHashSet<String> dynamicFieldValues;

    public DynamicField() {
    }

    public DynamicField(Long id, DYNAMIC_FIELD_TYPE s) {
        this.id = id;
        this.dynamicFieldType = s.toString();
    }
    
    public DynamicField(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "dynamic_field_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @LocalizedField
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @CreatedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Integer getMandatory() {
        return mandatory;
    }

    public void setMandatory(Integer mandatory) {
        this.mandatory = mandatory;
    }

    @Column(name = "dynamic_field_type")
    public String getDynamicFieldType() {
        return dynamicFieldType;
    }

    public void setDynamicFieldType(String dynamicFieldType) {
        this.dynamicFieldType = dynamicFieldType;
    }

    @Transient
    public LinkedHashSet<String> getDynamicFieldValues() {
        return dynamicFieldValues;
    }

    public void setDynamicFieldValues(LinkedHashSet<String> dynamicFieldValues) {
        this.dynamicFieldValues = dynamicFieldValues;
    }

    @Column(name = "search_always_visible")
    public Integer getSearchAlwaysVisible() {
        return searchAlwaysVisible;
    }

    public void setSearchAlwaysVisible(Integer searchAlwaysVisible) {
        this.searchAlwaysVisible = searchAlwaysVisible;
    }
    
}
