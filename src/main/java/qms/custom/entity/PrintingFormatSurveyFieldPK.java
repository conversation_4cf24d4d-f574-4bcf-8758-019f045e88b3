/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.entity;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class PrintingFormatSurveyFieldPK implements Serializable {    

    private Long printingFormatId;
    private Long surveyAnswerMetadataId;

    public PrintingFormatSurveyFieldPK() {
    }

    public PrintingFormatSurveyFieldPK(Long printingFormatId, Long surveyAnswerMetadataId) {
        this.printingFormatId = printingFormatId;
        this.surveyAnswerMetadataId = surveyAnswerMetadataId;
    }
    
    @GroundId
    @Basic(optional = false)
    @Column(name = "printing_format_id")
    public Long getPrintingFormatId() {
        return printingFormatId;
    }

    public void setPrintingFormatId(Long printingFormatId) {
        this.printingFormatId = printingFormatId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "survey_answer_metadata_id")
    public Long getSurveyAnswerMetadataId() {
        return surveyAnswerMetadataId;
    }

    public void setSurveyAnswerMetadataId(Long surveyAnswerMetadataId) {
        this.surveyAnswerMetadataId = surveyAnswerMetadataId;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 67 * hash + Objects.hashCode(this.printingFormatId);
        hash = 67 * hash + Objects.hashCode(this.surveyAnswerMetadataId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PrintingFormatSurveyFieldPK other = (PrintingFormatSurveyFieldPK) obj;
        if (!Objects.equals(this.printingFormatId, other.printingFormatId)) {
            return false;
        }
        return Objects.equals(this.surveyAnswerMetadataId, other.surveyAnswerMetadataId);
    }

    @Override
    public String toString() {
        return "PrintingFormatSurveyFieldPK{" + "printingFormatId=" + printingFormatId + ", surveyAnswerMetadataId=" + surveyAnswerMetadataId + '}';
    }
  
    
}
