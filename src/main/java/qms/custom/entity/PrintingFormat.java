/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.json.annotations.JSON;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.custom.dto.AnswerMetadataDTO;
import qms.custom.dto.AnswerMetadataFieldDTO;
import qms.form.entity.SurveyAnswerMetadata;
import qms.form.util.FixedField;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.util.CacheConstants;
import qms.util.CodePrefix;

/**
 *
 * <AUTHOR>
 */
@Entity
@Cacheable
@Cache(region = CacheConstants.PRINTING_FORMAT, usage = CacheConcurrencyStrategy.READ_WRITE)
@LocalizedEntity
@CodePrefix("PRINT-FORMAT-")
@Table(name = "printing_format")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class PrintingFormat extends StandardEntity<PrintingFormat> implements IAuditableEntity {
    
    private String code;                                                        // <-- clave
    private String description;                                                 // <-- nombre
    private String details;                                                     // <-- descripción larga
    private Integer status = 1;
    private Integer deleted = 0;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private Long fileId;                                                        // <-- El html se guarda ahí para BD
    private Long documentId;
    private String moduleName;
    private String masterId;
    
    // @JsonIgnore's
    private String fixedFieldsValues;                                           // <-- CAMPOS: valores separados por comas del enum de campos `FixedField`
    private String flexiFieldsValues;                                           // <-- CAMPOS: valores separados por comas del enum de campos `FixedField`
    private List<SurveyAnswerMetadata> surveyFieldsValues;                      // <-- CAMPOS: columnas de la tabla de respuestas
    private UserRef createdByUser;
    
    // @Transient 
    private final List<AnswerMetadataFieldDTO> fixedFields = new ArrayList<>(); // <-- CAMPOS FIJOS: del enum de FixedFields
    private final List<AnswerMetadataDTO> flexiFields= new ArrayList<>();       // <-- CAMPOS: columnas de la tabla de respuestas
    private String html = "";                                                   // <-- El HTML desde FRONT cae aquí y se transforma en un `fileId`
    
    
    @Id
    @Basic(optional = false)
    @Column(name = "printing_format_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 10)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @LocalizedField
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "details")
    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }
    
    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "module_name")
    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }


    @JSON(serialize = false)
    @Column(name = "fixed_fields_values")
    @JsonIgnore
    public String getFixedFieldsValues() {
        return fixedFieldsValues;
    }

    @Column(name = "master_id")
    public String getMasterId() {
        return masterId;
    }

    public void setMasterId(String masterId) {
        this.masterId = masterId;
    }

    @JSON(serialize = false)
    @Column(name = "flexi_fields_values")
    @JsonIgnore
    public String getFlexiFieldsValues() {
        return flexiFieldsValues;
    }

    public void setFlexiFieldsValues(String flexiFieldsValues) {
        this.flexiFieldsValues = flexiFieldsValues;
    }

    public void setFixedFieldsValues(String fixedFieldsValues) {
        this.fixedFieldsValues = fixedFieldsValues;
        if (fixedFieldsValues != null) {
            final String[] values = fixedFieldsValues.split("\\s*,\\s*");
            FixedField temp;
            for (String value : values) {
                temp = FixedField.fromValue(value);
                if (temp == null) {
                    continue;
                }
                this.fixedFields.add(new AnswerMetadataFieldDTO(
                    temp.getValue(), 
                    temp.getAlias(),
                    temp.name()
                ));
            }
        }
    }

    @JSON(serialize = false)
    @ManyToOne(fetch = FetchType.LAZY) // <-- LAZY!! se apaga la serialización
    @JoinColumn(
        referencedColumnName = "user_id",
        name = "created_by", updatable = false, insertable = false
    )
    @Cache(region = CacheConstants.PRINTING_FORMAT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnore
    public UserRef getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(UserRef createdByUser) {
        this.createdByUser = createdByUser;
    }

    
    @ManyToMany(fetch = FetchType.LAZY) // <-- LAZY!! se apaga la serialización
    @JoinTable(
        name = "printing_format_survey_field", 
        joinColumns = {
            @JoinColumn(name = "printing_format_id")
        }, 
        inverseJoinColumns = {
            @JoinColumn(name = "survey_answer_metadata_id")
        }
    )
    @JSON(serialize = false)
    @JsonIgnore
    @Cache(region = CacheConstants.PRINTING_FORMAT, usage = CacheConcurrencyStrategy.READ_WRITE)
    @Fetch(value = FetchMode.SUBSELECT)
    public List<SurveyAnswerMetadata> getSurveyFieldsValues() {
        return surveyFieldsValues;
    }

    public void setSurveyFieldsValues(List<SurveyAnswerMetadata> surveyFieldsValues) {
        this.surveyFieldsValues = surveyFieldsValues;
        if (surveyFieldsValues != null) {
            for (SurveyAnswerMetadata flexiFieldsValue : surveyFieldsValues) {
                this.flexiFields.add(new AnswerMetadataDTO(
                    flexiFieldsValue.getId(), 
                    flexiFieldsValue.getSurveyAnswerFieldName(),
                    flexiFieldsValue.getSurveyAnswerFieldName()
                ));
            }
        }
    }

    @Transient
    public String getHtml() {
        return html;
    }

    @Transient
    public List<AnswerMetadataFieldDTO> getFixedFields() {
        return fixedFields;
    }

    @Transient
    public List<AnswerMetadataDTO> getFlexiFields() {
        return flexiFields;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public void setFixedFields(List<AnswerMetadataFieldDTO> fixedFields) {
        if (fixedFields == null || fixedFields.isEmpty()) {
            return;
        }
        setFixedFieldsValues(
            StringUtils.substring(fixedFields.stream().map(m -> String.valueOf(m.getEnum().getValue())).reduce("", (a, b) -> a + ", " + b), 1)
        );
    }

    public void setFlexiFields(List<AnswerMetadataDTO> flexiFields) {
        if (flexiFields == null || flexiFields.isEmpty()) {
            return;
        }
        setSurveyFieldsValues(
            flexiFields
            .stream()
            .filter(p -> p.getSurveyAnswerMetadataId() != null)                 // <-- Campos de SURVEYS
            .map(p -> new SurveyAnswerMetadata(p.getSurveyAnswerMetadataId()))
            .collect(Collectors.toList()))
        ;
        setFlexiFieldsValues(
            StringUtils.substring(
                flexiFields
                .stream()
                .filter(p -> p.getSurveyAnswerMetadataId() != null)             // <-- Campos de SURVEYS
                .map(m -> m.getCode())                                          // <-- Nombre del campo en BD
                .reduce("", (a, b) -> a + ", " + b)
            , 1
        ));
    }

    @Override
    public String toString() {
        return "PrintingFormat{" + "id=" + id + '}';
    }
    
    
    
}
