/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.custom.entity;

import Framework.Config.CompositeStandardEntity;
import bnext.reference.IAuditable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.util.CacheConstants;

/**
 *
 * <AUTHOR>
 */
@Entity
@Cacheable
@Cache(region = CacheConstants.PRINTING_FORMAT, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "printing_format_outsurveys")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class PrintingFormatOutstandingSurveys extends CompositeStandardEntity<PrintingFormatOutstandingSurveysPK> implements IAuditable {

    private PrintingFormatOutstandingSurveysPK id;
    private Long fileId;
    private String fieldValuesHash;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    public PrintingFormatOutstandingSurveys() {
    }

    public PrintingFormatOutstandingSurveys(Long printingFormatId, Long outstandingSurveysId) {
        this.id = new PrintingFormatOutstandingSurveysPK(printingFormatId, outstandingSurveysId);
    }

    public PrintingFormatOutstandingSurveys(Long printingFormatId, Long outstandingSurveysId, Long fileId, String fieldValuesHash) {
        this.id = new PrintingFormatOutstandingSurveysPK(printingFormatId, outstandingSurveysId);
        this.fileId = fileId;
        this.fieldValuesHash = fieldValuesHash;
    }

    public PrintingFormatOutstandingSurveys(PrintingFormatOutstandingSurveysPK id) {
        this.id = id;
    }

    @Override
    public PrintingFormatOutstandingSurveysPK identifuerValue() {
        return id;
    }

    @Override
    @EmbeddedId
    public PrintingFormatOutstandingSurveysPK getId() {
        return id;
    }

    @Override
    public void setId(PrintingFormatOutstandingSurveysPK id) {
        this.id = id;
    }

    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "field_values_hash")
    public String getFieldValuesHash() {
        return fieldValuesHash;
    }

    public void setFieldValuesHash(String fieldValuesHash) {
        this.fieldValuesHash = fieldValuesHash;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PrintingFormatOutstandingSurveys other = (PrintingFormatOutstandingSurveys) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "PrintingFormatOutstandingSurveys{" + "id=" + id + ", fileId=" + fileId + '}';
    }

}
