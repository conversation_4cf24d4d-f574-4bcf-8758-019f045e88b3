
package qms.custom.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import qms.framework.util.CacheConstants;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Cacheable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.util.CodePrefix;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.DYNAMIC_FIELD, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "dynamic_metadata")
@CodePrefix("dynamicMetadata")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class DynamicMetadata extends StandardEntity<DynamicMetadata> implements IAuditableEntity {
   
    private String code;
    private String description;
    private Integer status;
    private Integer deleted;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    
    private Long dynamicFieldTableId;
    private Long dynamicFieldId;
    private String dynamicFieldTableName;
    private String dynamicFieldCode;
    private DynamicField dynamicField;

    public DynamicMetadata() {
    }
    
    
    public DynamicMetadata(Long id) {
        this.id = id;
    }
    
    @Id
    @Basic(optional = false)
    @Column(name = "dynamic_metadata_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @CreatedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "dynamic_field_table_id")
    public Long getDynamicFieldTableId() {
        return dynamicFieldTableId;
    }

    public void setDynamicFieldTableId(Long dynamicFieldTableId) {
        this.dynamicFieldTableId = dynamicFieldTableId;
    }

    @Column(name = "dynamic_field_id")
    public Long getDynamicFieldId() {
        return dynamicFieldId;
    }

    public void setDynamicFieldId(Long dynamicFieldId) {
        this.dynamicFieldId = dynamicFieldId;
    }

    @Column(name = "dynamic_field_table_name")
    public String getDynamicFieldTableName() {
        return dynamicFieldTableName;
    }

    public void setDynamicFieldTableName(String dynamicFieldTableName) {
        this.dynamicFieldTableName = dynamicFieldTableName;
    }

    @Column(name = "dynamic_field_code")
    public String getDynamicFieldCode() {
        return dynamicFieldCode;
    }

    public void setDynamicFieldCode(String dynamicFieldCode) {
        this.dynamicFieldCode = dynamicFieldCode;
    }
    
    @Cache(region = CacheConstants.DYNAMIC_FIELD, usage = CacheConcurrencyStrategy.READ_WRITE)
    @ManyToOne
    @JoinColumn(name = "dynamic_field_id", insertable = false, updatable = false)
    public DynamicField getDynamicField() {
        return dynamicField;
    }

    public void setDynamicField(DynamicField dynamicField) {
        this.dynamicField = dynamicField;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.code);
        hash = 89 * hash + Objects.hashCode(this.dynamicFieldId);
        hash = 89 * hash + Objects.hashCode(this.dynamicFieldTableName);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DynamicMetadata other = (DynamicMetadata) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.dynamicFieldTableName, other.dynamicFieldTableName)) {
            return false;
        }
        return Objects.equals(this.dynamicFieldId, other.dynamicFieldId);
    }

    @Override
    public String toString() {
        return "DynamicMetadata{" + "code=" + code + ", dynamicFieldTableName=" + dynamicFieldTableName + ", dynamicFieldCode=" + dynamicFieldCode + '}';
    }
    
    

    
}
