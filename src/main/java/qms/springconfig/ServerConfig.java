package qms.springconfig;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class ServerConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private String protocol;
    private Integer serverPort;
    private String contextPath;
    private String hostAddress;
    private List<String> activeProfiles;
    private String appName;

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public Integer getServerPort() {
        return serverPort;
    }

    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }

    public String getContextPath() {
        return contextPath;
    }

    public void setContextPath(String contextPath) {
        this.contextPath = contextPath;
    }

    public String getHostAddress() {
        return hostAddress;
    }

    public void setHostAddress(String hostAddress) {
        this.hostAddress = hostAddress;
    }

    public List<String> getActiveProfiles() {
        return activeProfiles;
    }

    public void setActiveProfiles(List<String> activeProfiles) {
        this.activeProfiles = activeProfiles;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 29 * hash + Objects.hashCode(this.protocol);
        hash = 29 * hash + Objects.hashCode(this.serverPort);
        hash = 29 * hash + Objects.hashCode(this.contextPath);
        hash = 29 * hash + Objects.hashCode(this.hostAddress);
        hash = 29 * hash + Objects.hashCode(this.appName);
        hash = 29 * hash + Objects.hashCode(this.activeProfiles);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ServerConfig other = (ServerConfig) obj;
        if (!Objects.equals(this.protocol, other.getProtocol())) {
            return false;
        }
        if (!Objects.equals(this.serverPort, other.getServerPort())) {
            return false;
        }
        if (!Objects.equals(this.appName, other.getAppName())) {
            return false;
        }
        if (!Objects.equals(this.contextPath, other.getContextPath())) {
            return false;
        }
        return Objects.equals(this.hostAddress, other.getHostAddress());
    }

    @Override
    public String toString() {
        return "ServerConfig{"
                + "protocol=" + protocol
                + ", serverPort=" + serverPort
                + ", contextPath=" + contextPath
                + ", hostAddress=" + hostAddress
                + ", profiles=" + StringUtils.join(activeProfiles, ",")
                + '}';
    }

}
