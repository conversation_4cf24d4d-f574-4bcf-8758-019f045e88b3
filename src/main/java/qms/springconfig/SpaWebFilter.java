package qms.springconfig;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.springframework.web.filter.OncePerRequestFilter;

public class Spa<PERSON>ebFilter extends OncePerRequestFilter {


    /**
     * Forwards any unmapped paths (except those containing a period) to the client {@code index.html}.
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        // Request URI includes the contextPath if any, removed it.
        String path = request.getRequestURI().substring(request.getContextPath().length());
        if ("/".equals(path)  || "/qms/".equals(path) || (path.startsWith("/qms") && (
                !path.contains(".") ||
                        path.endsWith(".view") ||
                        path.endsWith(".action") ||
                        path.endsWith(".style") ||
                        path.endsWith(".download") ||
                        path.endsWith(".controller")
        ) && path.matches("/(.*)"))) {
            request.getRequestDispatcher("/qms/index.html").forward(request, response);
            return;
        }

        filterChain.doFilter(request, response);
    }
}
