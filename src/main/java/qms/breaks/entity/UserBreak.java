package qms.breaks.entity;

import DPMS.Mapping.IAuditableEntity;
import DPMS.Mapping.Periodicity;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR> Leal Ramírez @Block Networks
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "user_break")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class UserBreak extends StandardEntity<UserBreak> implements IAuditableEntity {

    private static final long serialVersionUID = 1L;

    private Long userId;
    private Date breakDate;
    private Long periodicityId;
    private String code;
    private String description;
    private Integer status = 1;
    private Integer deleted = 0;
    private Date lastModifiedDate;
    private Date createdDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Integer type;
    private String periodicityDescription;
    private UserRef user;
    private UserRef creatorUser;    
    private Periodicity periodicity;

    public UserBreak() {
    }

    public UserBreak(Long id) {
        this.id = id;
    }

    public UserBreak(Long id, Long userId, Date breakDate, Long periodicityId, Integer status, Integer deleted, Date createdDate, Long createdBy) {
        this.id = id;
        this.userId = userId;
        this.breakDate = breakDate;
        this.periodicityId = periodicityId;
        this.status = status;
        this.deleted = deleted;
        this.createdDate = createdDate;
        this.createdBy = createdBy;
    }
    
    public static enum BREAK_TYPE {
        BREAK_DATE_USER(1),
        BREAK_DATE_GENERIC(2);

        BREAK_TYPE(Integer value) {
            this.value = value;
        }
        
        private final Integer value;

        public Integer getValue() {
            return this.value;
        }
        
        @Override
        public String toString() {
            return this.value.toString();
        }
    }

    @Id
    @Basic(optional = false)
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic(optional = false)
    @Column(name = "break_date")
    @Temporal(TemporalType.DATE)
    public Date getBreakDate() {
        return breakDate;
    }

    public void setBreakDate(Date breakDate) {
        this.breakDate = breakDate;
    }

    @Column(name = "periodicity_id")
    public Long getPeriodicityId() {
        return periodicityId;
    }

    public void setPeriodicityId(Long periodicityId) {
        this.periodicityId = periodicityId;
    }

    @Override
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @LocalizedField
    @Override
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic(optional = false)
    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @LastModifiedDate
    @Column(name = "last_modified_date")
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @CreatedDate
    @Basic(optional = false)
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @CreatedBy
    @Basic(optional = false)
    @Column(name = "created_by")
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @LastModifiedBy
    @Column(name = "last_modified_by")
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Basic(optional = false)
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    
    @Column(name = "periodicity_description")
    public String getPeriodicityDescription() {
        return periodicityDescription;
    }

    public void setPeriodicityDescription(String periodicityDescription) {
        this.periodicityDescription = periodicityDescription;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof UserBreak)) {
            return false;
        }
        UserBreak other = (UserBreak) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "qms.breaks.entity.UserBreak[ id=" + id + " ]";
    }

    @ManyToOne(optional = true)
    @JoinColumn(referencedColumnName = "user_id", name = "user_id", insertable = false, updatable = false, nullable = true)
    public UserRef getUser() {
        return user;
    }

    public void setUser(UserRef user) {
        this.user = user;
    }
    
    @ManyToOne
    @JoinColumn(referencedColumnName = "intperiodicidadid", name = "periodicity_id", insertable = false, updatable = false)
    public Periodicity getPeriodicity() {
        return periodicity;
    }

    public void setPeriodicity(Periodicity periodicity) {
        this.periodicity = periodicity;
    }

    @ManyToOne(optional = true)
    @JoinColumn(referencedColumnName = "user_id", name = "created_by", insertable = false, updatable = false, nullable = true)
    public UserRef getCreatorUser() {
        return creatorUser;
    }

    public void setCreatorUser(UserRef creatorUser) {
        this.creatorUser = creatorUser;
    }
}
