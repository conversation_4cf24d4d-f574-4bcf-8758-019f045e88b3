
package qms.breaks.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

/**
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblperiodicidad")
public class UserBreakPeriodicity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private Set<UserBreakSave> breaks;

    public UserBreakPeriodicity() {
    }

    public UserBreakPeriodicity(Long id) {
        this.id = id;
    }

    public UserBreakPeriodicity(Long id, Set<UserBreakSave> breaks) {
        this.id = id;
        this.breaks = breaks;
    }
    
    @Id
    @Basic(optional = false)
    @Column(name = "intperiodicidadid")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "periodicity", fetch = FetchType.EAGER)
    public Set<UserBreakSave> getBreaks() {
      return breaks;
    }

    public void setBreaks(Set<UserBreakSave> breaks) {
        this.breaks = breaks;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!UserBreakPeriodicity.class.isInstance(object)) {
            return false;
        }
        UserBreakPeriodicity other = (UserBreakPeriodicity) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "UserBreakPeriodicity[ " + id + " - " + id + " ]";
    }

}
