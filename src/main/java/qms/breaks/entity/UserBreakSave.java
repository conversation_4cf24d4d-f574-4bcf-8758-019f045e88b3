package qms.breaks.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR> @Block Networks
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "user_break")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class UserBreakSave extends StandardEntity<UserBreakSave> implements IAuditableEntity {

    private static final long serialVersionUID = 1L;

    private Date breakDate;
    private String code;
    private String description;
    private Integer status = 1;
    private Integer deleted = 0;
    private Date lastModifiedDate;
    private Date createdDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Long userId;
    private Integer type;
    private String periodicityDescription;
    private UserBreakPeriodicity periodicity;

    public UserBreakSave() {
    }

    public UserBreakSave(Long id) {
        this.id = id;
    }

    public UserBreakSave(Long id, Date breakDate, Integer status, Integer deleted, Date createdDate, Long createdBy) {
        this.id = id;
        this.breakDate = breakDate;
        this.status = status;
        this.deleted = deleted;
        this.createdDate = createdDate;
        this.createdBy = createdBy;
    }
    
    public static enum BREAK_TYPE {
        BREAK_DATE_USER(1),
        BREAK_DATE_GENERIC(2);

        BREAK_TYPE(Integer value) {
            this.value = value;
        }
        
        private final Integer value;

        public Integer getValue() {
            return this.value;
        }
    }

    @Id
    @Basic(optional = false)
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "break_date")
    @Temporal(TemporalType.DATE)
    public Date getBreakDate() {
        return breakDate;
    }

    public void setBreakDate(Date breakDate) {
        this.breakDate = breakDate;
    }

    @Override
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @LocalizedField
    @Override
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic(optional = false)
    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "last_modified_date")
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    @LastModifiedDate
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Basic(optional = false)
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    @Override
    @CreatedDate
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Basic(optional = false)
    @Column(name = "created_by")
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Basic(optional = false)
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    
    @Column(name = "periodicity_description")
    public String getPeriodicityDescription() {
        return periodicityDescription;
    }

    public void setPeriodicityDescription(String periodicityDescription) {
        this.periodicityDescription = periodicityDescription;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @JoinColumn(name = "periodicity_id", referencedColumnName = "intperiodicidadid")
    @ManyToOne
    public UserBreakPeriodicity getPeriodicity() {
        return periodicity;
    }

    public void setPeriodicity(UserBreakPeriodicity periodicity) {
        this.periodicity = periodicity;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 29 * hash + Objects.hashCode(this.breakDate);
        hash = 29 * hash + Objects.hashCode(this.code);
        hash = 29 * hash + Objects.hashCode(this.description);
        hash = 29 * hash + Objects.hashCode(this.status);
        hash = 29 * hash + Objects.hashCode(this.deleted);
        hash = 29 * hash + Objects.hashCode(this.lastModifiedDate);
        hash = 29 * hash + Objects.hashCode(this.createdDate);
        hash = 29 * hash + Objects.hashCode(this.createdBy);
        hash = 29 * hash + Objects.hashCode(this.lastModifiedBy);
        hash = 29 * hash + Objects.hashCode(this.type);
        hash = 29 * hash + Objects.hashCode(this.periodicityDescription);
        hash = 29 * hash + Objects.hashCode(this.periodicity);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final UserBreakSave other = (UserBreakSave) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.description, other.description)) {
            return false;
        }
        if (!Objects.equals(this.periodicityDescription, other.periodicityDescription)) {
            return false;
        }
        if (!Objects.equals(this.breakDate, other.breakDate)) {
            return false;
        }
        if (!Objects.equals(this.status, other.status)) {
            return false;
        }
        if (!Objects.equals(this.deleted, other.deleted)) {
            return false;
        }
        if (!Objects.equals(this.lastModifiedDate, other.lastModifiedDate)) {
            return false;
        }
        if (!Objects.equals(this.createdDate, other.createdDate)) {
            return false;
        }
        if (!Objects.equals(this.createdBy, other.createdBy)) {
            return false;
        }
        if (!Objects.equals(this.lastModifiedBy, other.lastModifiedBy)) {
            return false;
        }
        if (!Objects.equals(this.type, other.type)) {
            return false;
        }
        if (!Objects.equals(this.periodicity, other.periodicity)) {
            return false;
        }
        return true;
    }
    
    @Override
    public String toString() {
        return "qms.breaks.entity.UserBreakSave[ id=" + id + " ]";
    }

}
