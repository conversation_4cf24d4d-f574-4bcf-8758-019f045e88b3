package qms.finding.entity;

import DPMS.Mapping.ActionSources;
import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.Transient;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.document.entity.DocumentMaster;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.util.interfaces.EntityType;


/**
 *
 * <AUTHOR> Germán Lares Lares
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "finding_type")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class FindingType extends StandardEntity<FindingType> implements EntityType, IAuditableEntity, Serializable {

    private static final long serialVersionUID = 1L;
        
    private String code;
    private Integer status;
    private Integer deleted = 0;
    private String description;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private String documentCode;
    private String documentMasterId;
    private DocumentMaster documentMaster;
    
    //Transient
    private List<ActionSources> actionSources;
    
    public FindingType() {
    }
    
    public FindingType(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "finding_type_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date dt) {
        this.createdDate = dt;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date dt) {
        this.lastModifiedDate = dt;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }   

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @LocalizedField
    @Column(name = "description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }


    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof FindingType)) {
            return false;
        }
        FindingType other = (FindingType) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.FindingType[ id=" + id + " ]";
    }

    @Transient
    public List<ActionSources> getActionSources() {
        if(actionSources == null) {
            return Utilities.EMPTY_LIST;
        }
        return actionSources;
    }

    public void setActionSources(List<ActionSources> actionSources) {
        this.actionSources = actionSources;
    }

    @Column(name = "document_code")
    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }


    @Column(name = "document_master_id")
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @ManyToOne
    @JoinColumn(name = "document_master_id", referencedColumnName = "master_id", updatable = false, insertable = false)
    public DocumentMaster getDocumentMaster() {
        return documentMaster;
    }

    public void setDocumentMaster(DocumentMaster documentMaster) {
        this.documentMaster = documentMaster;
    }
  
}
