package qms.finding.pending;

import DPMS.Mapping.Action;
import Framework.Config.Utilities;
import ape.pending.CommonAction;
import ape.pending.DAOInterface.IPendingRecordDAO;
import ape.pending.core.APE;
import ape.pending.core.BaseAPE;
import ape.pending.core.BaseSummaryFieldBuilder;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.IPendingDto;
import ape.pending.util.PendingUtil;
import ape.pending.util.RecordRowsCache;
import ape.pending.util.SqlQueryParser;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import qms.access.dto.ILoggedUser;
import qms.finding.dto.FindingPendingDto;
import qms.finding.pending.imp.ToAddPlan;
import qms.finding.pending.imp.ToAnalyze;
import qms.finding.pending.imp.ToAssign;
import qms.finding.pending.imp.ToEvaluate;
import qms.finding.pending.imp.ToStartImplementation;
import qms.framework.rest.SecurityUtils;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
public class FindingPendingDataSource {
    
    public static Collection<IPendingDto> getPendingRecords(IPendingRecordDAO dao) {
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final Class<? extends BaseAPE> base = Action.class;
        final IPendingOperation[] operations = new IPendingOperation[] {
            new ToStartImplementation(dao),
            new ToEvaluate(dao),
            new ToAssign(dao),
            new ToAnalyze(dao),
            new ToAddPlan(dao)
        };
        final Integer countPendings = dao.getPendingCount(loggedUser, operations);
        if (countPendings == null || Objects.equals(countPendings, 0)) {
            return Utilities.EMPTY_SET;
        }
        LinkedHashSet<String> commonActions =
            PendingUtil.commonActions(
                CommonAction.ATTEND, CommonAction.HIDE // <--- Agregar funcionalidad general de pendientes (APE): REASIGNAR.
            );
        String owner = "entity";
        return dao.getPendingRecords(
            loggedUser,
            base,
            null,
            Arrays.asList(BaseSummaryFieldBuilder.columns(
                "recordId = recordId@record",
                "id = pendingRecordId@record",
                "owner = pendingRecordUserId@record",
                "code",
                "description",
                "createdDate",
                "code = pendingTypeCode@ptype",
                "description = pendingTypeDescription@ptype",
                "commitmentDate = commitmentDate@record",
                "description = author@author",
                "description = attendant@attendant",
                "description = departmentDescription@budentity",
                "description = system@system",
                "code = source@fuente",
                "description = priority@priority"
            )),            
            SqlQueryParser.LEFT_JOIN + owner + ".author author " +
            SqlQueryParser.LEFT_JOIN + owner + ".attendant attendant " +
            SqlQueryParser.LEFT_JOIN + owner + ".system system " +
            SqlQueryParser.LEFT_JOIN + owner + ".fuente fuente " +
            SqlQueryParser.LEFT_JOIN + owner + ".priority priority ",
            RecordRowsCache.DISABLED,
            operations
        ).stream().map((Map pending) -> {
            APE ape = APE.fromCode((String) pending.get("pendingTypeCode"));
            if (ape == null) {
                throw new RuntimeException(""
                    + " Invalid pendingTypeCode '" + pending.get("pendingTypeCode") + "',"
                    + " value must exist as a 'code' inside APE.java."
                );
            }
            FindingPendingDto value = new FindingPendingDto();
            // valores requeridos para pendientes
            value.setPendingRecordId((Long) pending.get("pendingRecordId"));
            value.setPendingRecordUserId((Long) pending.get("pendingRecordUserId"));
            value.setRecordId((Long) pending.get("recordId"));
            value.setCode((String) pending.get("code"));
            value.setBase((String) pending.get("base"));
            value.setPendingTypeCode(ape.getCode());
            value.setModule(mx.bnext.access.Module.ACTION);
            value.setDescription((String) pending.get("description"));
            value.setCommitmentDate((Date) pending.get("commitmentDate"));
            value.setAvailableActions(commonActions);
            value.setCreatedDate((Date) pending.get("createdDate"));
            value.setPendingTypeDescription((String) pending.get("pendingTypeDescription"));
            value.setAuthor((String) pending.get("author"));
            value.setAttendant((String) pending.get("attendant"));
            value.setDepartmentDescription((String) pending.get("departmentDescription"));
            value.setSystem((String) pending.get("system"));
            value.setSource((String) pending.get("source"));
            value.setPriority((String) pending.get("priority"));
            value.setDto(FindingPendingDto.class.getCanonicalName());
            value.setCommitmentStartDate(value.getCommitmentDate());
            value.setCommitmentEndDate(value.getCommitmentDate());
            
            return value;
        }).collect(Collectors.toList());
    }
    
    public static IPendingOperation[] getPendingOperations(IPendingRecordDAO dao) {
        return new IPendingOperation[] {
            new ToStartImplementation(dao),
            new ToEvaluate(dao),
            new ToAssign(dao),
            new ToAnalyze(dao),
            new ToAddPlan(dao)
        };
    }
    
    public static Class<? extends BaseAPE> getBaseEntity(Module module) {
        return Action.class;
    }
}
