package qms.finding.pending.imp;

import DPMS.Mapping.Action;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.ApeOperationType;
import ape.pending.entities.PendingRecord;
import qms.finding.entity.FindingActivity;
import qms.finding.entity.FindingActivityType;
import qms.finding.pending.FindingPendingOperations;

/**
 *
 * <AUTHOR>
 */
public class ToAnalyze extends FindingPendingOperations {

    private static final String TO_ANALYZE = ""
            + " FROM " + Action.class.getCanonicalName() + " fnd "
            + " WHERE "
            + " fnd.status = " + Action.STATUS.ASSIGNED.getValue() + " "
                + " AND fnd.deleted = 0"
                + " AND fnd.requireAnalysis = " + Action.REQUIRED_ANALYSIS.REQUIRE_YES.getValue()
                + " AND exists ("
                    + " SELECT 1"
                    + " FROM " + FindingActivity.class.getCanonicalName() + " act "
                    + " WHERE act.id.findingId = fnd.id"
                    + " AND act.id.type = " + FindingActivityType.ACI.getValue()
                + " ) "
            + "";

    public ToAnalyze(IUntypedDAO dao) {
        super(dao);
        setBaseAlias(ALIAS);
        setQuery(TO_ANALYZE);
        setScope(PendingRecord.Scope.USER);
        setOwnerField("fnd.responsableId");
        setPendingType(getType(APE.FINDING_TO_ANALYZE));
        setModuleKey(MODULE);
        setBase(Action.class);
        setOwnerFieldFilter(BY_OWNER);
        setDependencies(
            ToStartImplementation.class
        );
        setOperationType(ApeOperationType.STRONG);
        setReminder("dayadd(1, CAST(isnull(fnd.analysisDate, CURRENT_DATE) as int))");
    }
    
}
