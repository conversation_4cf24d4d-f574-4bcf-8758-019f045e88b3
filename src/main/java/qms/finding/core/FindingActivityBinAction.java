package qms.finding.core;

import Framework.Action.AuditableEntityAction;

/**
 *
 * <AUTHOR>
 */
public class FindingActivityBinAction extends AuditableEntityAction {
    
    /**
     * @return the activityCreator
     */
    public Boolean getActivityCreator() {
        return false;
    }

    /**
     * @return the activityViewer
     */
    public Boolean getActivityViewer() {
        return false;
    }
    
    /**
     * @return the activityManager
     */
    public Boolean getActivityManager() {
        return false;
    }

    public String getServiceStore() {
        return "../DPMS/Finding-Activity-Bin.action";
    }
    
    public String getActivityGridPath() {
        return "bnext/administrator/finding/finding-activity-bin";
    }
    
}