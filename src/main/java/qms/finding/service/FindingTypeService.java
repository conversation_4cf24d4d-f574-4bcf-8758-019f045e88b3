
package qms.finding.service;

import DPMS.Mapping.ActionSources;
import DPMS.Mapping.Persistable;
import Framework.Config.ITextHasValue;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.IUntypedDAO;
import java.util.List;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.finding.DAOInterface.IFindingType;
import qms.finding.entity.FindingType;
import qms.finding.entity.FindingTypeActionSources;
import qms.util.LinkedGridConfig;

/**
 * <AUTHOR>
 */
public class FindingTypeService extends CRUD_Generic<FindingType> {
    
    private List<ActionSources> actionSources;

    @Override
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        switch(linkedGridId) {
            case "finding_type_action_sources":      //finding-type.jsp, Fuentes de acciones agregadas
                return 
                    new LinkedGridConfig(
                        ActionSources.STATUS.ACTIVE, 
                        IUntypedDAO.class, 
                        ActionSources.class,
                        new String[] { "code"}, 
                        groundId, 
                        FindingTypeActionSources.class
                    );
        }
        return super.getLinkedGridConfig(linkedGridId, groundId);
    }
    

    @Override
    protected void beforeSave(Persistable backup, boolean isNewObject, IUntypedDAO dao) {
        FindingType bean = (FindingType) backup;
        actionSources = bean.getActionSources();
    }
    
    @Override
    protected void afterSave(Persistable backup, boolean isNewObject, IUntypedDAO dao) {
        FindingType bean = (FindingType) backup;
        if(!isNewObject) {
            dao.HQL_updateByQuery("DELETE FROM " + FindingTypeActionSources.class.getCanonicalName() + " c WHERE c.id.findingTypeId = " + bean.getId());
        }
        for(ActionSources f : actionSources) {
            dao.makePersistent(new FindingTypeActionSources(bean.getId(), f.getId()));
        }
    }
    
    @SMDMethod
    public List<ITextHasValue> getActionSources(Long findingTypeId) {
        return getUntypedDAO().getStrutsComboList(
                ActionSources.class,
                "code", ""
                + " c.id IN ("
                    + " SELECT d.id.actionSourceId "
                    + " FROM " + FindingTypeActionSources.class.getCanonicalName() + " d "
                    + " WHERE d.id.findingTypeId = " + findingTypeId
                + " ) AND c.deleted = 0");
    }
    
    @SMDMethod
    public GridInfo getRows(SortedPagedFilter filter) {
        try {
            IFindingType dao = getBean(IFindingType.class);
            return dao.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id,"
                    + " c.status AS status,"
                    + " c.code AS code,"
                    + " c.description AS description,"
                    + " c.deleted AS deleted,"
                    + " c.createdDate AS createdDate,"
                    + " c.createdBy AS createdBy,"
                    + " c.lastModifiedBy AS lastModifiedBy,"
                    + " c.documentCode AS documentCode,"
                    + " c.lastModifiedDate AS lastModifiedDate,"
                    + " b.description AS businessUnitName"
                + " )"
                + " FROM " + FindingType.class.getCanonicalName() + " c "
                + " LEFT JOIN c.documentMaster m"
                + " LEFT JOIN m.businessUnit b"
                + " WHERE c.deleted = 0", filter
            );
        } catch (Exception ex) {
            getLogger().error("FindingTypes error", ex);
        }
        return Utilities.EMPTY_GRID_INFO;
    }
    
}
