package qms.finding.service;

import DPMS.Mapping.Action;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.SimpleAction;
import DPMS.Mapping.User;
import Framework.Config.SortedPagedFilter;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.core.IPendingCountByType;
import ape.pending.core.IPendingHandler;
import ape.pending.core.PendingHandlerService;
import ape.pending.core.PendingHelper;
import ape.pending.entities.PendingRecord;
import ape.pending.util.ApeUtil;
import java.util.List;
import java.util.Map;
import java.util.Set;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.activity.pending.imp.ToComplete;
import qms.activity.pending.imp.ToVerify;
import qms.activity.pending.imp.ToVerifyDelayed;
import qms.activity.pending.imp.ToVerifyNotApply;
import qms.activity.util.CommitmentTask;
import qms.finding.entity.FindingActivity;
import qms.finding.entity.FindingActivityView;
import qms.finding.pending.imp.ToAddPlan;
import qms.finding.pending.imp.ToAnalyze;
import qms.finding.pending.imp.ToAssign;
import qms.finding.pending.imp.ToEvaluate;
import qms.finding.pending.imp.ToStartImplementation;
import qms.util.BindUtil;

/**
 * <AUTHOR> Germán Lares Lares
 */
public class FindingService extends PendingHandlerService<SimpleAction> implements IPendingHandler, IPendingCountByType {

    private static final long serialVersionUID = -3447783471501839014L;

    private static final String HQL_ACTIVITIES_ROWS = ""
            + " SELECT c "
            + " FROM " 
                + FindingActivityView.class.getCanonicalName() + " c "
                + " CROSS JOIN " + FindingActivity.class.getCanonicalName() + " fa"
            + " WHERE"
                + " fa.id.activityId = c.id"
                + " AND c.deleted = 0"
                + " AND fa.id.commitmentTask = " + CommitmentTask.IMPLEMENTATION.getValue()
                + " AND fa.id.findingId = "
            ;
    
    @Override
    public Class getParameterizedType() {
        return Action.class;
    }
    
    @SMDMethod
    public GridInfo getRowsToAssign(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return getActiveRecordRows(filter, dao, new ToAssign(dao));
    }

    @SMDMethod
    public GridInfo getRowsToStartImplementation(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return getActiveRecordRows(filter, dao, new ToStartImplementation(dao));
    }

    @SMDMethod
    public GridInfo getRowsToAnalyze(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return getActiveRecordRows(filter, dao, new ToAnalyze(dao));
    }

    @SMDMethod
    public GridInfo getRowsToAddPlan(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return getActiveRecordRows(filter, dao, new ToAddPlan(dao));
    }

    @SMDMethod
    public GridInfo getRowsToEvaluate(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return getActiveRecordRows(filter, dao, new ToEvaluate(dao));
    }
    
    @SMDMethod
    public GridInfo getRowsMyFindings(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        PendingRecord.STATUS[] statuses = {
            PendingRecord.STATUS.ACTIVE, PendingRecord.STATUS.ESCALATED, PendingRecord.STATUS.ATTENDED, PendingRecord.STATUS.REASSIGNED
        };
        final PendingHelper helper = new PendingHelper(dao);
        final Set<String> findingApes = ApeUtil.getCodes(Module.ACTION, Action.class, dao);
        final Set<String> activityApes = ApeUtil.getCodes(Module.ACTION, FindingActivity.class, dao);
        List<Long> findingTypeIds = helper.getTypeIdsByCodes(findingApes);
        List<Long> activityTypeIds = helper.getTypeIdsByCodes(activityApes);
        String condition = ""
                + " EXISTS ("
                    + PendingHelper.getFilterRecordsByUser(getLoggedUserId(), statuses) 
                    + " AND ( "
                        + " ( "
                            + " pnd.recordId = entity.id"
                            + " AND " + BindUtil.parseFilterList("pnd.type", findingTypeIds)
                        + " ) OR ("
                            + " pnd.recordId IN ("
                                + " SELECT fa.id.activityId "
                                + " FROM " + FindingActivity.class.getCanonicalName() + " fa "
                                + " WHERE fa.id.findingId = entity.id"
                            + " )"
                            + " AND " + BindUtil.parseFilterList("pnd.type", activityTypeIds) + ")" 
                    + " )"
                + " )"
                + " OR entity.createdBy = " + getLoggedUserId();
        filter.getCriteria().put("<condition>", condition);
        GridInfo<Map<String, Object>> result = dao.getLightRows(filter, Action.class, false, null, 0);
        result.getData().forEach((row) -> {
            if (row.get("entity_aprobacion") != null) {
                String approval = row.get("entity_aprobacion").toString();
                String status = row.get("entity_status").toString();
                if (approval.equals("1") && status.equals(Action.STATUS.CLOSED.getValue().toString())) {
                    row.put("entity_status", Action.STATUS.CLOSED_EFFECTIVE.getValue());
                } else if (approval.equals("2") && status.equals(Action.STATUS.CLOSED.getValue().toString())) {
                    row.put("entity_status", Action.STATUS.CLOSED_UNEFFECTIVE.getValue());
                }
            }
            row.put("activities", dao.HQL_findByQuery(HQL_ACTIVITIES_ROWS + row.get("entity_id")));
        });
        return result;
    }
    
    @Override
    @SMDMethod
    public GridInfo getRows(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        String condition = "";
        Map<String, Object> userBU = dao.HQL_findSimpleMap(""
                + " SELECT new Map( "
                    + " us.businessUnitDepartment.businessUnitId AS businessUnitId, "
                    + " us.businessUnitDepartment.businessUnit.organizationalUnitId AS organizationalUnitId"
                + " ) "
                + " FROM " + User.class.getCanonicalName() + " us "
                + " WHERE us.id = " + getLoggedUserId());
        
        if (!isAdmin() && getLoggedUserServices().contains(ProfileServices.ACCION_POR_ORGANIZACION)) {
            condition = ""
                + " c.businessUnitId IN ("
                    + " SELECT bu.id FROM " + BusinessUnit.class.getCanonicalName() + " bu WHERE bu.organizationalUnitId IN ("
                        + " SELECT corp.id "
                        + " FROM " + User.class.getCanonicalName() + " us "
                        + " JOIN us.puestos pu "
                        + " JOIN pu.corp corp "
                        + " WHERE us.id = :userId "
                    + ") "
                    + " OR bu.organizationalUnitId = " + userBU.get("organizationalUnitId")
                + " )";
            
        } else if(!isAdmin()){
            condition = ""
                + " c.businessUnitId IN ("
                    + " SELECT pu.une.id "
                    + " FROM " + User.class.getCanonicalName() + " us "
                    + " JOIN us.puestos pu "
                    + " WHERE us.id = :userId "
                + " ) "
                + " OR c.businessUnitId = " + userBU.get("businessUnitId");
        }
         filter.getCriteria().put("<condition>", condition.replaceAll(":userId", getLoggedUserId().toString()));
        return getRows(filter, true);
    }
    
    @Override
    @SMDMethod
    public Map<String, Integer> getPendingMap() {
        Map pendings = getPendingMap(this, Module.ACTION, getUntypedDAO());
        return pendings;
    }

    @Override
    public Integer countTypeByUser(Long loggedUserId, APE pendingType, IUntypedDAO dao) {
        setLoggedUserId(loggedUserId);
        switch (pendingType) {
            case FINDING_TO_ASSIGN:
                return new ToAssign(dao).getCountByUser(getLoggedUserId());
            case FINDING_TO_START_IMPLEMENTATION:
                return new ToStartImplementation(dao).getCountByUser(getLoggedUserId());
            case FINDING_TO_ANALYZE:                
                return new ToAnalyze(dao).getCountByUser(getLoggedUserId());
            case FINDING_TO_ADD_PLAN:
                return new ToAddPlan(dao).getCountByUser(getLoggedUserId());
            case FINDING_TO_EVALUATE:
                return new ToEvaluate(dao).getCountByUser(getLoggedUserId());
            case FINDING_ACTIVITY_TO_COMPLETE:
                return new ToComplete(Module.ACTION, dao).getCountByUser(getLoggedUserId());
            case FINDING_ACTIVITY_TO_VERIFY:
                return new ToVerify(Module.ACTION, dao).getCountByUser(getLoggedUserId());
            case FINDING_ACTIVITY_TO_VERIFY_DELAYED:
                return new ToVerifyDelayed(Module.ACTION, dao).getCountByUser(getLoggedUserId());
            case FINDING_ACTIVITY_TO_VERIFY_NOT_APPLY:
                return new ToVerifyNotApply(Module.ACTION, dao).getCountByUser(getLoggedUserId());
        }
        return 0;
    }

}
