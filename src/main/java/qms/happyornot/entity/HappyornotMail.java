/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.happyornot.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.Transient;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "happyornot_mail")
public class HappyornotMail extends StandardEntity<HappyornotMail> implements IAuditableEntity, Serializable {

    private Integer status;
    private Integer deleted;
    private Date lastModifiedDate;
    private Date createdDate;
    private String mail;

    private Long createdBy;
    private Long lastModifiedBy;

    private HappyornotSurvey happyornotSurvey;
    private UserRef user;

    public HappyornotMail() {
        this.id = -1L;
        this.status = HappyornotMail.STATUS.ACTIVE.getValue();
        this.deleted = 0;
    }

    @Id
    @Column(name = "happyornot_mail_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Transient
    public Long getHappyornotSurveyId() {
        if (this.happyornotSurvey == null) {
            return null;
        }
        return this.happyornotSurvey.getId();
    }

    public void setHappyornotSurveyId(Long happyornotSurveyId) {
        if (this.happyornotSurvey == null && happyornotSurveyId != null) {
            this.happyornotSurvey = new HappyornotSurvey(happyornotSurveyId);
        }
    }

    @Transient
    public Long getUserId() {
        if (this.user == null) {
            return null;
        }
        return this.user.getId();
    }

    public void setUserId(Long userId) {
        if (this.user == null && userId != null) {
            this.user = new UserRef(userId);
        }
    }

    @JoinColumn(name = "happyornot_survey_id")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    public HappyornotSurvey getHappyornotSurvey() {
        return happyornotSurvey;
    }

    public void setHappyornotSurvey(HappyornotSurvey happyornotSurvey) {
        this.happyornotSurvey = happyornotSurvey;
    }

    @JoinColumn(name = "user_id")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    public UserRef getUser() {
        return user;
    }

    public void setUser(UserRef user) {
        this.user = user;
    }

    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    @CreatedDate
    @Column(name = "created_date", updatable = false)
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    @LastModifiedDate
    @Column(name = "last_modified_date")
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @CreatedBy
    @Override
    @Column(name = "created_by", updatable = false)
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }


    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    @Transient
    public String getCode() {
        return this.mail;
    }

    @Override
    public void setCode(String clave) {
        // empty
    }

    @Override
    @Transient
    public String getDescription() {
        return this.mail;
    }

    @Override
    public void setDescription(String descripcion) {
        // empty
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 17 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final HappyornotMail other = (HappyornotMail) obj;
        return Objects.equals(this.id, other.id);
    }

    @Override
    public String toString() {
        return "qms.happyornot.entity.HappyornotMail[ id=" + id + " ]";
    }



}
