/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package qms.happyornot.entity;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import org.hibernate.annotations.Type;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 *
 * <AUTHOR>
 */

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "happyornot_fonts")
public class HappyornotFonts extends DomainObject implements Serializable {
    
    private String fontName;
    private HappyornotFontGroup fontGroup;
    private Boolean header;
    
    @Id
    @Column(name = "font_id", updatable = false)
    @Basic(optional = false)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "font_name", updatable = false)
    public String getFontName() {
        return fontName;
    }

    public void setFontName(String fontName) {
        this.fontName = fontName;
    }

    @JoinColumn(name = "font_group")
    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    public HappyornotFontGroup getFontGroup() {
        return fontGroup;
    }

    public void setFontGroup(HappyornotFontGroup fontGroup) {
        this.fontGroup = fontGroup;
    }
    
    @Column(name = "header", updatable = false)
    @Type(type = "numeric_boolean")
    public Boolean getHeader() {
        return header;
    }
    
    public void setHeader(Boolean header){
        this.header = header;
    }
}
