package qms.timework.mail;

import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.io.IOException;
import javax.annotation.Nonnull;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import qms.access.dto.ILoggedUser;
import qms.framework.listeners.BnextMonitor;
import qms.framework.listeners.MailMonitor;
import qms.framework.mail.BnextStaffMailer;

/**
 *
 * <AUTHOR>
 */
@Aspect
public class TimeworkMailMonitor extends MailMonitor {

    @Around(BnextMonitor.DAILY_TIMEWORK_MAIL_IS_FIRED)
    public Object onDailyTimeworkMailFired(ProceedingJoinPoint pjp, @Nonnull ILoggedUser loggedUser) throws Throwable {
        Object result = null;
        if (pjp != null) {
            result = pjp.proceed();
        }
        executeTimeworkMails();
        return result;
    }
    
    @Override
    @Around(BnextMonitor.MAIL_DAEMON_IS_TRIGGERED)
    public Object onMailDaemonFired(ProceedingJoinPoint pjp) {
        try {
            Object result = null;
            if (pjp != null) {
                result = pjp.proceed();
            }
            if (isMailOff()) {
                return result;
            }
            IUntypedDAO dao = getUntypedDAO(pjp);
            TimeworkMailer mailer = new TimeworkMailer(dao);
            mailer.hasConflicted();
            return result;
        } catch (Throwable ex) {
            getLogger(LOGGER.APE).error("Invalid remider call.", ex);
            return null;
        } 
    }

    private void executeTimeworkMails() throws IOException {
        try {
            final BnextStaffMailer mailer = new BnextStaffMailer(Utilities.getUntypedDAO());
            mailer.isTimeworkSyncing();
        } catch (final Exception ex) {
            getLogger().error("No license mails failed", ex);
        }
    }
    
    
    
}
