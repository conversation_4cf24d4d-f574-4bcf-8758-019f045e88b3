package qms.timework.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import qms.timework.util.ITimework;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TimeworkDTO implements Serializable, ITimework<GeolocationCoordinatesDTO> {

    private static final long serialVersionUID = 1L;

    private Long timeworkId;
    private Integer mode;
    private Long userId;

    private Date checkDate;
    private Date localCheckDate;

    private Date checkIn;
    private Date checkOut;
    private String checkInTz;
    private String checkOutTz;

    private Date localCheckIn;
    private Date localCheckOut;

    private String localCheckInTz;
    private String localCheckOutTz;

    private GeolocationCoordinatesDTO checkInLocation;
    private GeolocationCoordinatesDTO checkOutLocation;

    public TimeworkDTO() {
    }

    public TimeworkDTO(
            Long timeworkId,
            Integer mode,
            Long userId,
            Date checkDate,
            Date checkIn,
            Date checkOut,
            String checkInTz,
            String checkOutTz,
            Date localCheckIn,
            Date localCheckOut,
            String localCheckInTz,
            String localCheckOutTz,
            Date localCheckDate,
            Double checkInLatitude,
            Double checkInLongitude,
            Double checkOutLatitude,
            Double checkOutLongitude
    ) {
        this.timeworkId = timeworkId;
        this.mode = mode;
        this.userId = userId;
        this.checkDate = checkDate;
        this.checkIn = checkIn;
        this.checkOut = checkOut;
        this.checkInTz = checkInTz;
        this.checkOutTz = checkOutTz;
        this.localCheckIn = localCheckIn;
        this.localCheckOut = localCheckOut;
        this.localCheckInTz = localCheckInTz;
        this.localCheckOutTz = localCheckOutTz;
        this.localCheckDate = localCheckDate;
        this.checkInLocation = new GeolocationCoordinatesDTO(checkInLatitude, checkInLongitude);
        this.checkOutLocation = new GeolocationCoordinatesDTO(checkOutLatitude, checkOutLongitude);
    }

    @Override
    public Long getId() {
        return timeworkId;
    }

    public Long getTimeworkId() {
        return timeworkId;
    }

    public void setTimeworkId(Long timeworkId) {
        this.timeworkId = timeworkId;
    }

    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public Date getLocalCheckDate() {
        return localCheckDate;
    }

    public void setLocalCheckDate(Date localCheckDate) {
        this.localCheckDate = localCheckDate;
    }

    @Override
    public Date getCheckIn() {
        return checkIn;
    }

    public void setCheckIn(Date checkIn) {
        this.checkIn = checkIn;
    }

    @Override
    public Date getCheckOut() {
        return checkOut;
    }

    public void setCheckOut(Date checkOut) {
        this.checkOut = checkOut;
    }

    @Override
    public String getCheckInTz() {
        return checkInTz;
    }

    public void setCheckInTz(String checkInTz) {
        this.checkInTz = checkInTz;
    }

    @Override
    public String getCheckOutTz() {
        return checkOutTz;
    }

    public void setCheckOutTz(String checkOutTz) {
        this.checkOutTz = checkOutTz;
    }

    @Override
    public String getLocalCheckInTz() {
        return localCheckInTz;
    }

    public void setLocalCheckInTz(String localCheckInTz) {
        this.localCheckInTz = localCheckInTz;
    }

    @Override
    public String getLocalCheckOutTz() {
        return localCheckOutTz;
    }

    public void setLocalCheckOutTz(String localCheckOutTz) {
        this.localCheckOutTz = localCheckOutTz;
    }

    @Override
    public Date getLocalCheckIn() {
        return localCheckIn;
    }

    public void setLocalCheckIn(Date localCheckIn) {
        this.localCheckIn = localCheckIn;
    }

    @Override
    public Date getLocalCheckOut() {
        return localCheckOut;
    }

    public void setLocalCheckOut(Date localCheckOut) {
        this.localCheckOut = localCheckOut;
    }

    @Override
    public GeolocationCoordinatesDTO getCheckInLocation() {
        return checkInLocation;
    }

    public void setCheckInLocation(GeolocationCoordinatesDTO checkInLocation) {
        this.checkInLocation = checkInLocation;
    }

    @Override
    public GeolocationCoordinatesDTO getCheckOutLocation() {
        return checkOutLocation;
    }

    public void setCheckOutLocation(GeolocationCoordinatesDTO checkOutLocation) {
        this.checkOutLocation = checkOutLocation;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TimeworkDTO that = (TimeworkDTO) o;
        return Objects.equals(timeworkId, that.timeworkId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(timeworkId);
    }

    @Override
    public String toString() {
        return "TimeworkDTO{" +
                "timeworkId=" + timeworkId +
                ", localCheckIn=" + localCheckIn +
                ", localCheckOut=" + localCheckOut +
                '}';
    }
}
