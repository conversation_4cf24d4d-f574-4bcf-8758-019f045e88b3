package qms.timework.dto;

import com.google.gson.annotations.SerializedName;

/**
 *
 * <AUTHOR>
 */
public class TimeworkTestSyncDto {

    @SerializedName("ExternalCode")
    private String externalCode;
    @SerializedName("UserExternalCode")
    private String userExternalCode;
    @SerializedName("RecordType")
    private String recordType;
    @SerializedName("RecordDate")
    private String recordDate;
    @SerializedName("RecordTime")
    private String recordTime;
    @SerializedName("Deleted")
    private String deleted;

    public TimeworkTestSyncDto() {
    }

    public String getExternalCode() {
        return externalCode;
    }

    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    public String getUserExternalCode() {
        return userExternalCode;
    }

    public void setUserExternalCode(String userExternalCode) {
        this.userExternalCode = userExternalCode;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(String recordDate) {
        this.recordDate = recordDate;
    }

    public String getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(String recordTime) {
        this.recordTime = recordTime;
    }

    public String getDeleted() {
        return deleted;
    }

    public void setDeleted(String deleted) {
        this.deleted = deleted;
    }   
}
