package qms.timework.service;

import DPMS.Mapping.Settings;
import Framework.Action.SessionViewer;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import com.opensymphony.xwork2.Action;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.timework.dao.ITimeworkDAO;
import qms.timework.dto.TimeworkDTO;
import qms.timework.dto.TimeworkListDto;
import qms.timework.dto.TimeworkPreferencesDto;
import qms.timework.entity.Timework;
import qms.timework.util.TimeworkHandler;
import qms.util.DAOException;

/**
 *
 * <AUTHOR>
 */
public class TimeworkService extends SessionViewer {

    private static final long serialVersionUID = 1L;
 
    public String smd() {
        return Action.SUCCESS;
    }


    private boolean invalidRegisterManuallyAccess() {
        if (!Utilities.isTimeworkAvailable()) {
            return true;
        }
        if (isAdmin()) {
            return false;
        }
        List<ProfileServices> services = getLoggedUserServices();
        return !services.contains(ProfileServices.TIMEWORK_MANUALLY_REGISTER);
    }

    private boolean invalidDeleteAccess() {
        if (!Utilities.isTimeworkAvailable()) {
            return true;
        }
        if (isAdmin()) {
            return false;
        }
        List<ProfileServices> services = getLoggedUserServices();
        return !services.contains(ProfileServices.TIMEWORK_MANUALLY_REGISTER)
                && !services.contains(ProfileServices.TIMEWORK_SYNC);
    }

    private boolean invalidListAccess() {
        if (!Utilities.isTimeworkAvailable()) {
            return true;
        }
        if (isAdmin()) {
            return false;
        }
        List<ProfileServices> services = getLoggedUserServices();
        return !services.contains(ProfileServices.TIMEWORK_LIST)
                && !services.contains(ProfileServices.FORM_STOPWATCH_LIST);
    }

    private boolean invalidSyncAccess() {
        if (!Utilities.isTimeworkAvailable()) {
            return true;
        }
        if (isAdmin()) {
            return false;
        }
        List<ProfileServices> services = getLoggedUserServices();
        return !services.contains(ProfileServices.TIMEWORK_SYNC);
    }

    @SMDMethod
    public GenericSaveHandle add(final TimeworkDTO saveData, final Long userId) {
        if (invalidRegisterManuallyAccess()) {
            return new GenericSaveHandle("no-access");
        }
        final ILoggedUser loggedUserDto = getLoggedUserDto();
        final TimeworkHandler handler = new TimeworkHandler();
        final TimeworkDTO newData = handler.mergeMapWithCurrentData(saveData, loggedUserDto);
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        if (dao.hasRegistryOpen(newData.getCheckDate(), userId)) {
            return new GenericSaveHandle("registry_open");
        }
        if (dao.checkSplicedHours(newData, userId, false, loggedUserDto)) {
            return new GenericSaveHandle("spliced_hours");
        } else {
            if  (newData.getCheckIn().getTime() == newData.getCheckOut().getTime()) {
                newData.getCheckOut().setMinutes(newData.getCheckOut().getMinutes() + 5);
            }
            final List<Timework> records = new ArrayList<>();
            return dao.add(records, newData, userId, loggedUserDto);
        }
    }


    @SMDMethod
    public GenericSaveHandle modifyCheckOut(final Long timeworkId, final Date checkOut) {
        if (invalidRegisterManuallyAccess()) {
            return new GenericSaveHandle("no-access");
        }
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        return dao.modifyCheckOut(timeworkId, checkOut, getLoggedUserDto());
    }
    
    @SMDMethod
    public GenericSaveHandle delete(final Long timeworkId) {
        if (invalidDeleteAccess()) {
            return new GenericSaveHandle("no-access");
        }
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        return dao.delete(timeworkId, getLoggedUserId());
    }
    
    @SMDMethod
    public GenericSaveHandle modifyCheckIn(final Long timeworkId, final Date checkIn) {
        if (invalidRegisterManuallyAccess()) {
            return new GenericSaveHandle("no-access");
        }
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        return dao.modifyCheckIn(timeworkId, checkIn, getLoggedUserDto());
    }
    
    @SMDMethod
    public GenericSaveHandle save(final Map<String, TimeworkDTO> saveData) {
        try {   
            final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
            final LoggedUser loggedUser = getLoggedUserDto();
            final TimeworkHandler handler = new TimeworkHandler();
            final Map<String, TimeworkDTO> newData = handler.mergeMapWithCurrentData(saveData, loggedUser);
            return dao.save(newData, loggedUser);
        } catch (DAOException e) {
            getLogger().error("There was an error ", e);
            return new GenericSaveHandle();
        }
    }
    
    @SMDMethod
    public GridInfo<TimeworkListDto> getRecords(final SortedPagedFilter filter) {
        if (invalidListAccess()) {
            return Utilities.EMPTY_GRID_INFO;
        }
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        return dao.getListRecords(filter, getLoggedUserDto());
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getConflictedRecords(final SortedPagedFilter filter) {
        if (invalidSyncAccess()) {
            return Utilities.EMPTY_GRID_INFO;
        }
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        return dao.getConflictedRecords(filter);
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getNotSyncedRecords(final SortedPagedFilter filter) {
        if (invalidSyncAccess()) {
            return Utilities.EMPTY_GRID_INFO;
        }
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        return dao.getNotSyncedRecords(filter);
    }
    
    @SMDMethod
    public GenericSaveHandle sync() {
        try {
            final TimeworkHandler handler = new TimeworkHandler();
            return handler.sync();
        } catch(DAOException e) {
            getLogger().error("There was an error ", e);
            return new GenericSaveHandle();
        }
    }
    
    @SMDMethod
    public GenericSaveHandle saveAndSync(final Map<String, TimeworkDTO> savedata) {
        if (invalidSyncAccess()) {
            return new GenericSaveHandle("no-access");
        }
        GenericSaveHandle gsh = new GenericSaveHandle();
        GenericSaveHandle sync;
        try {
            final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
            final ILoggedUser loggedUser = getLoggedUserDto();
            final TimeworkHandler handler = new TimeworkHandler();
            final Map<String, TimeworkDTO> newData = handler.mergeMapWithCurrentData(savedata, loggedUser);
            gsh = dao.save(newData, loggedUser);
            sync = handler.sync();
            if (sync.getOperationEstatus() != 1) {
                gsh.setErrorMessage(sync.getErrorMessage());
                gsh.setOperationEstatus(sync.getOperationEstatus());
                gsh.setStatusCode(sync.getStatusCode());
            }
            return gsh;
        } catch(DAOException e) {
            getLogger().error("There was an error ", e);
            return gsh;
        }
    }

    @SMDMethod
    public TimeworkDTO load(Long timeworkId) {
        getLogger().debug("load: [timeworkId={}]", timeworkId);
        if (invalidRegisterManuallyAccess()) {
            return null;
        }
        final ITimeworkDAO dao = getBean(ITimeworkDAO.class);
        return dao.getRecordDto(timeworkId);
    }
    
    @SMDMethod
    public Boolean savePreferences(TimeworkPreferencesDto preferences) {
        
        TimeworkPreferencesDto oldPreferences;
        
        oldPreferences = (TimeworkPreferencesDto) getUntypedDAO().HQL_findSimpleObject(" "
                + "SELECT new " + TimeworkPreferencesDto.class.getCanonicalName() 
                + "("
                    + "s.enableTimework,"
                    + " s.timeworkUrl,"
                    + " s.enableTimeworkSyncTest,"
                    + " s.timeworkMails,"
                    + " s.timeworkSyncType,"
                    + " s.maxConnectionTimeout,"
                    + " s.maxRequestTimeout"
                + ") "
                + "FROM " + Settings.class.getCanonicalName() + " s "
                + "WHERE s.id = " + Utilities.getSettings().getId());

        if (oldPreferences == null) {
            oldPreferences = new TimeworkPreferencesDto();
        }
        
        Map<String, Object> params = new HashMap<>();
        String query = "";
        
        if (!Objects.equals(preferences.getEnableTimework(), oldPreferences.getEnableTimework())) {
            params.put("enableTimework", preferences.getEnableTimework());
            query += "s.enableTimework = :enableTimework, ";
        }
        if(!Objects.equals(preferences.getEnableTimeworkSyncTest(), oldPreferences.getEnableTimeworkSyncTest())){
             params.put("enableTimeworkSyncTest", preferences.getEnableTimeworkSyncTest());
             query += "s.enableTimeworkSyncTest = :enableTimeworkSyncTest, ";
        }
        if(!preferences.getTimeworkMails().equals(oldPreferences.getTimeworkMails())){
            params.put("timeworkMails", preferences.getTimeworkMails());
            query += "s.timeworkMails = :timeworkMails, ";
        }
        if(!preferences.getTimeworkSyncType().equals(oldPreferences.getTimeworkSyncType())){
            params.put("timeworkSyncType", preferences.getTimeworkSyncType());
            query += "s.timeworkSyncType = :timeworkSyncType, ";
        }
        if(!preferences.getTimeworkUrl().equals(oldPreferences.getTimeworkUrl())){
            params.put("timeworkUrl", preferences.getTimeworkUrl());
            query += "s.timeworkUrl = :timeworkUrl, ";
        }
        if(!preferences.getMaxConnectionTimeout().equals(oldPreferences.getMaxConnectionTimeout())){
            params.put("maxConnectionTimeout", preferences.getMaxConnectionTimeout());
            query += "s.maxConnectionTimeout = :maxConnectionTimeout, ";
        } 
        if(!preferences.getMaxRequestTimeout().equals(oldPreferences.getMaxRequestTimeout())){
            params.put("maxRequestTimeout", preferences.getMaxRequestTimeout());
            query += "s.maxRequestTimeout = :maxRequestTimeout, ";
        } 
        params.put("id", Utilities.getSettings().getId());
        
        int result = 0;
        if (!query.isEmpty()) {
            query = query.trim();
            query = query.substring(0, query.length() - 1);
            result = getUntypedDAO().HQL_updateByQuery(" "
                    + "UPDATE " + Settings.class.getCanonicalName() + " s "
                    + "SET " + query + " WHERE s.id = :id", params);
        }
       
        Utilities.resetSettings(getServletContext());
        return result > 0;
        
    }

}
