package qms.timework.dao;

import DPMS.Mapping.User;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import com.google.common.collect.ImmutableMap;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.file.util.UuidUtils;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.form.util.FormHandler;
import qms.framework.entity.GeolocationCoordinates;
import qms.framework.util.IGeolocationCoordinates;
import qms.timework.dto.TimeworkActionResponse;
import qms.timework.dto.TimeworkDTO;
import qms.timework.dto.TimeworkDoCheckData;
import qms.timework.dto.TimeworkListDto;
import qms.timework.dto.TimeworkRangeDTO;
import qms.timework.entity.Timework;
import qms.timework.entity.Timework.ERP_STATUS;
import qms.timework.entity.UserTimeworkPerDay;
import qms.timework.util.IExternalFormData;
import qms.timework.util.ITimework;
import qms.timework.util.TimeworkHandler;
import qms.timework.util.TimeworkMode;
import qms.util.BindUtil;
import qms.util.DateUtil;
import qms.util.interfaces.IGridFilter;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Lazy
@Repository(value = "TimeworkDAO")
@Scope(value = "singleton")
public class TimeworkDAO extends GenericDAOImpl<Timework, Long> implements ITimeworkDAO {

    private static final String SELECT_CONFLICTED_DETAIL_HQL = " "
            + " SELECT new map("
                + " c.lastModifiedDate as lastModifiedDate"
                + ", c.erpStatus as erpStatus"
                + ", c.erpError as erpError"
            + " )"
            + " FROM " + Timework.class.getCanonicalName() + " c"
            + " WHERE c.id = :id";

    private static final String UPDATE_CONFLICTED_HQL = " "
            + " UPDATE " + Timework.class.getCanonicalName() + " t "
            + " SET "
                + " t.lastModifiedDate = current_timestamp(),"
                + " t.erpStatus = :status,"
                + " t.erpError = :error"
            + " WHERE t.id = :id";

    private static final String JSON_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

    private static final String TIMEWORK_RECORD_LIST_SELECT = " "
            + " SELECT new " + TimeworkListDto.class.getCanonicalName() + " ("
                + " t.id as id"
                + ", u.id as userId"
                + ", u.code as userCode"
                + ", u.description as userName"
                + ", t.code as code"
                + ", t.mode as mode"
                + ", t.workedSeconds as workedSeconds"
                + ", t.workedHours as workedHours"
                + ", t.createdDate as createdDate"
                + ", t.erpStatus as erpStatus"
                + ", t.erpError as erpError"
                + ", t.lastModifiedDate as lastModifiedDate"
                + ", t.checkIn as checkIn"
                + ", checkInLocation.latitude as checkInLatitude"
                + ", checkInLocation.longitude as checkInLongitude"
                + ", checkInLocation.accuracy as checkInAccuracy"
                + ", t.checkOut as checkOut"
                + ", t.localCheckIn as localCheckIn"
                + ", t.localCheckOut as localCheckOut"
                + ", t.localCheckIn as localRegisterDate"
                + ", checkOutLocation.latitude as checkOutLatitude"
                + ", checkOutLocation.longitude as checkOutLongitude"
                + ", checkOutLocation.accuracy as checkOutAccuracy"
                + ", bud.description as businessUnitDepartment"
                + ", humanize_time_seconds(t.workedSeconds, ':lang') as workedTime"
                + ", t.checkIn as registerDate"
                + ", t.checkInTz as checkInTz"
                + ", t.checkOutTz as checkOutTz"
                + ", t.localCheckInTz as localCheckInTz"
                + ", t.localCheckOutTz as localCheckOutTz"
                + ", t.documentDescription as documentDescription"
                + ", t.outstandingSurveyCode as outstandingSurveyCode"
            + ")"
            + " FROM " + Timework.class.getCanonicalName() + " t "
            + " JOIN " + User.class.getCanonicalName() + " u "
            + " ON t.userId = u.id "
            + " LEFT JOIN t.checkInLocation checkInLocation"
            + " LEFT JOIN t.checkOutLocation checkOutLocation"
            + " LEFT JOIN u.businessUnitDepartment bud ";

    private static final String ALREADY_CHECKED_IN_COUNT_HQL = " "
            + " SELECT COUNT(c.id)"
            + " FROM " + Timework.class.getCanonicalName() + " c"
            + " WHERE DAY(:now) = DAY(c.localCheckIn)"
            + " AND MONTH(:now) = MONTH(c.localCheckIn)"
            + " AND YEAR(:now) = YEAR(c.localCheckIn) "
            + " AND c.mode = " + TimeworkMode.SEQUENTIAL_TIME.getValue()
            + " AND c.status = " + Timework.STATUS.CHECK_IN.getValue()
            + " AND c.deleted = 0 "
            + " AND c.userId = :userId"
            + " AND c.mode = :mode";

    private static final String SELECT_TIMEWORK_LAST_CHECK_IN_HQL = " "
            + " SELECT c"
            + " FROM " + Timework.class.getCanonicalName() + " c"
            + " WHERE DAY(:now) = DAY(c.localCheckIn)"
            + " AND MONTH(:now) = MONTH(c.localCheckIn)"
            + " AND YEAR(:now) = YEAR(c.localCheckIn) "
            + " AND c.mode = " + TimeworkMode.SEQUENTIAL_TIME.getValue()
            + " AND c.status = " + Timework.STATUS.CHECK_IN.getValue()
            + " AND c.deleted = 0 "
            + " AND c.userId = :userId";

    private static final String SELECT_TIMEWORK_HQL = " "
            + " SELECT c"
            + " FROM " + Timework.class.getCanonicalName() + " c"
            + " WHERE c.id = :id";

    private static final String WIDGET_MINE_ROWS_SELECT_HQL = " "
            + " SELECT new Map("
                + " c.id as id"
                + ", c.code as code"
                + ", c.checkIn as checkIn"
                + ", c.checkOut as checkOut"
                + ", c.localCheckIn as localCheckIn"
                + ", c.localCheckOut as localCheckOut"
                + ", c.checkInTz as checkInTz"
                + ", c.checkOutTz as checkOutTz"
                + ", c.localCheckInTz as localCheckInTz"
                + ", c.localCheckOutTz as localCheckOutTz"
            + ")"
            + " FROM " + Timework.class.getCanonicalName() + " c"
            + " WHERE c.userId = :userId"
            + " AND c.deleted = 0 "
            + " AND DAY(:now) = DAY(c.localCheckIn)"
            + " AND MONTH(:now) = MONTH(c.localCheckIn)"
            + " AND YEAR(:now) = YEAR(c.localCheckIn)"
            + " AND c.mode = " + TimeworkMode.SEQUENTIAL_TIME.getValue()
            + " ORDER BY c.localCheckIn ASC";

    private final TimeworkHandler handler = new TimeworkHandler();

    private Date getLoggedUserNowWithTz(final ILoggedUser loggedUser) {
        final String systemTimezone = getSystemTimezone();
        final Date now = new Date();
        return DateUtil.convertDateToSystemTz(now, loggedUser.getTimezone(), systemTimezone);
    }

    private String getSystemTimezone() {
        return Utilities.getSettings().getTimeZone().trim();
    }

    private boolean alreadyCheckedIn(
            final Long timeworkId,
            final TimeworkMode mode,
            final ILoggedUser loggedUser
    ) {
        if (TimeworkMode.SEQUENTIAL_TIME.equals(mode)) {
            if (timeworkId != null && timeworkId > 0) {
                getLogger().error(
                        "Can not check in with sequential mode for timeworkId {} for user {}.",
                        timeworkId,
                        loggedUser.getId()
                );
                return false;
            }
            final Date now = getLoggedUserNowWithTz(loggedUser);
            return HQL_findLong(
                    ALREADY_CHECKED_IN_COUNT_HQL,
                    ImmutableMap.of(
                            "userId", loggedUser.getId(),
                            "now", now,
                            "mode", mode.getValue()
                    )
            ) > 0;
        } else if (TimeworkMode.CONCURRENT_TIME.equals(mode)) {
            return timeworkId != null && timeworkId > 0;
        } else {
            return false;
        }
    }

    @Nullable
    private Timework lastCheckIn(
            final Long timeworkId,
            final TimeworkMode mode,
            final ILoggedUser loggedUser
    ) {
        if (TimeworkMode.SEQUENTIAL_TIME.equals(mode)) {
            final Date now = getLoggedUserNowWithTz(loggedUser);
            return (Timework) HQL_findSimpleObject(SELECT_TIMEWORK_LAST_CHECK_IN_HQL,
                    ImmutableMap.of(
                            "userId", loggedUser.getId(),
                            "now", now
                    )
            );
        } else if (TimeworkMode.CONCURRENT_TIME.equals(mode)) {
            if (timeworkId == null || timeworkId <= 0) {
                getLogger().error(
                        "Can not check out without a timeworkId for concurrent mode for user {}",
                        loggedUser.getId()
                );
                return null;
            }
            final Timework timework = (Timework) HQL_findSimpleObject(
                    SELECT_TIMEWORK_HQL,
                    ImmutableMap.of(
                            "id", timeworkId
                    )
            );
            if (timework != null && !Objects.equals(timework.getUserId(), loggedUser.getId())) {
                getLogger().error(
                        "Can not check out with concurrent mode for timeworkId {}"
                                + " for user {} as he doe not match record user {}.",
                        timeworkId,
                        loggedUser.getId(),
                        timework.getUserId()
                );
                return null;
            }
            return timework;
        } else {
            getLogger().error(
                    "Can not check out with {} mode for timeworkId {} for user {}.",
                    mode,
                    timeworkId,
                    loggedUser.getId()
            );
            return null;
        }
    }

    private Map<String, Object> getMultipleRecordsResponseData(
            final List<Timework> records,
            final IGeolocationCoordinates checkInLocation,
            final IGeolocationCoordinates checkOutLocation
    ) {
        if (records == null || records.isEmpty()) {
            return Collections.emptyMap();
        }
        final List<TimeworkActionResponse> result = records.stream()
                .map(timework -> getTimeworkActionResponse(timework, checkInLocation, checkOutLocation))
                .collect(Collectors.toList());
        return ImmutableMap.of("data", result);
    }

    private TimeworkActionResponse getTimeworkActionResponse(
            final ITimework<?> timework,
            final IGeolocationCoordinates checkInLocation,
            final IGeolocationCoordinates checkOutLocation
    ) {
        final TimeworkActionResponse result = new TimeworkActionResponse();
        result.setId(timework.getId());
        result.setCheckOut(formatJsonDate(timework.getCheckOut()));
        result.setCheckOutTz(timework.getCheckInTz());
        result.setCheckIn(formatJsonDate(timework.getCheckIn()));
        result.setCheckInTz(timework.getCheckOutTz());
        result.setLocalCheckIn(formatJsonDate(timework.getLocalCheckIn()));
        result.setLocalCheckInTz(timework.getLocalCheckInTz());
        result.setLocalCheckOut(formatJsonDate(timework.getLocalCheckOut()));
        result.setLocalCheckOutTz(timework.getLocalCheckOutTz());
        if (timework.getCheckInLocation() != null) {
            result.setCheckInLatitude(timework.getCheckInLocation().getLatitude());
            result.setCheckInLongitude(timework.getCheckInLocation().getLongitude());
        } else if (checkInLocation != null) {
            result.setCheckInLatitude(checkInLocation.getLatitude());
            result.setCheckInLongitude(checkInLocation.getLongitude());
        }
        if (timework.getCheckOutLocation() != null) {
            result.setCheckOutLatitude(timework.getCheckOutLocation().getLatitude());
            result.setCheckOutLongitude(timework.getCheckOutLocation().getLongitude());
        } else if (checkOutLocation != null) {
            result.setCheckOutLatitude(checkOutLocation.getLatitude());
            result.setCheckOutLongitude(checkOutLocation.getLongitude());
        }
        return result;
    }

    private Map<String, Object> getResponseData(final ITimework<?> timework) {
        final TimeworkActionResponse result = getTimeworkActionResponse(timework, timework.getCheckInLocation(), timework.getCheckOutLocation());
        return Utilities.entityToMap(result);
    }

    private Map<String, Object> getResponseCheckIn(
            final Date checkIn,
            final Date localCheckIn,
            final String checkInTz,
            final String localCheckInTz
    ) {
        final TimeworkActionResponse result = new TimeworkActionResponse();
        result.setCheckIn(formatJsonDate(checkIn));
        result.setCheckInTz(checkInTz);
        result.setLocalCheckIn(formatJsonDate(localCheckIn));
        result.setLocalCheckInTz(localCheckInTz);
        return Utilities.entityToMap(result);
    }

    private Map<String, Object> getResponseCheckOut(
            final Date checkOut,
            final Date localCheckOut,
            final String userTimezone,
            final String systemTimezone
    ) {
        final TimeworkActionResponse result = new TimeworkActionResponse();
        result.setCheckOut(formatJsonDate(checkOut));
        result.setCheckOutTz(systemTimezone);
        result.setLocalCheckOut(formatJsonDate(localCheckOut));
        result.setLocalCheckOutTz(userTimezone);
        return Utilities.entityToMap(result);
    }

    private String formatJsonDate(final Date date) {
        if (date == null) {
            return null;
        }
        final Locale defaultLocale = new Locale("es", "MX");
        final DateFormat formatter = new SimpleDateFormat(JSON_DATE_FORMAT, defaultLocale);
        return formatter.format(date);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(
            final Map<String, TimeworkDTO> data,
            final ILoggedUser loggedUser
    ) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        for (final Map.Entry<String, TimeworkDTO> entry : data.entrySet()) {
            final Long timeworkId = Long.parseLong(entry.getKey());
            final TimeworkDTO dto = entry.getValue();
            final TimeworkDTO registry = getRecordDto(timeworkId);
            if (TimeworkMode.CONCURRENT_TIME.getValue() == registry.getMode()) {
                getLogger().error(
                        "Can not modify concurrent time record for timeworkId {} for user {}.",
                        timeworkId,
                        loggedUser.getId()
                );
                gsh.setOperationEstatus(0);
                gsh.setErrorMessage("not_allowed");
                return gsh;
            }
            dto.setCheckDate(registry.getCheckIn());
            dto.setTimeworkId(timeworkId);
            if (dto.getCheckIn() != null) {
                if (dto.getCheckOut() == null) {
                   dto.setCheckOut(registry.getCheckOut());
                }
                if (checkSplicedHours(dto, registry.getUserId(), true, loggedUser)) {
                    gsh.setOperationEstatus(0);
                    gsh.setSuccessMessage("spliced_hours");
                    gsh.setErrorMessage("spliced_hours");
                    return gsh;
                }
                modifyCheckIn(timeworkId, dto.getCheckIn(), loggedUser);
            }
            if (dto.getCheckOut() != null) {
                if (dto.getCheckIn() == null) {
                   dto.setCheckIn(registry.getCheckIn());
                }
                if (checkSplicedHours(dto, registry.getUserId(), true, loggedUser)) {
                    gsh.setOperationEstatus(0);
                    gsh.setSuccessMessage("spliced_hours");
                    gsh.setErrorMessage("spliced_hours");
                    return gsh;
                }
                modifyCheckOut(timeworkId, dto.getCheckOut(), loggedUser);
            }
        }
        gsh.setOperationEstatus(1);
        return gsh;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle delete(final Long timeworkId, final Long loggedUserId) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (timeworkId == null) {
            return gsh;
        }
        final Integer mode = HQL_findSimpleInteger(" "
                + " SELECT t.mode "
                + " FROM " + Timework.class.getCanonicalName() + " t "
                + " WHERE t.id = :id",
                ImmutableMap.of("id", timeworkId)
        );
        if (TimeworkMode.CONCURRENT_TIME.getValue() == mode) {
            getLogger().error(
                    "Can not delete concurrent time record for timeworkId {} for user {}.",
                    timeworkId,
                    loggedUserId
            );
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("not_allowed");
            return gsh;
        }
        int updateResult = HQL_updateByQuery(" "
            + " UPDATE " + Timework.class.getCanonicalName() + " t "
            + " SET "
                + " t.deleted = 1"
                + " ,t.lastModifiedDate = current_timestamp()"
                + " ,t.lastModifiedBy = :lastModifiedBy"
                + " ,t.erpStatus = " + ERP_STATUS.MODIFIED.getValue()
            + " WHERE "
                + " t.id = :id",
                ImmutableMap.of(
                        "id", timeworkId,
                        "lastModifiedBy", loggedUserId
                )
        );
        if (updateResult > 0) {
            gsh.setOperationEstatus(1);
            gsh.setSavedId(timeworkId.toString());
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle modifyCheckOut(final Long timeworkId, final Date checkOut, final ILoggedUser loggedUser) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (timeworkId == null || checkOut == null) {
            return gsh;
        }

        final String systemTimezone = getSystemTimezone();
        final String userTimezone = loggedUser.getTimezone();

        final Map<String, Object> props = new HashMap<>();

        props.put("checkOut", checkOut);
        props.put("checkOutTz", systemTimezone);

        final Date localCheckOut = DateUtil.convertDateToSystemTz(checkOut, userTimezone, systemTimezone);
        props.put("localCheckOut", localCheckOut);
        props.put("localCheckOutTz", userTimezone);

        props.put("lastModifiedDate", new Date());
        props.put("lastModifiedBy", loggedUser.getId());
        props.put("erpStatus", ERP_STATUS.MODIFIED.getValue());
        props.put("status", Timework.STATUS.CHECK_OUT.getValue());

        final int updateResult = HQL_updateByQuery(
                Timework.class,
                props,
                loggedUser.getId(),
                timeworkId,
                false,
                null,
                0,
                null
        );

        if (updateResult > 0) {
            final Map<String, Object> jsonEntityData = getResponseCheckOut(checkOut, localCheckOut, userTimezone, systemTimezone);
            gsh.setJsonEntityData(jsonEntityData);
            gsh.setOperationEstatus(1);
            gsh.setSavedId(timeworkId.toString());
        }
        return gsh;
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle modifyCheckIn(
            final Long timeworkId,
            final Date checkIn,
            final ILoggedUser loggedUser
    ) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (timeworkId == null || checkIn == null) {
            return gsh;
        }

        final String systemTimezone = getSystemTimezone();
        final String userTimezone = loggedUser.getTimezone();

        final Map<String, Object> props = new HashMap<>(5);

        props.put("checkIn", checkIn);
        props.put("checkInTz", systemTimezone);

        final Date localCheckIn = DateUtil.convertDateToSystemTz(checkIn, userTimezone, systemTimezone);
        props.put("localCheckIn", localCheckIn);
        props.put("localCheckInTz", userTimezone);

        props.put("lastModifiedDate", new Date());
        props.put("lastModifiedBy", loggedUser.getId());
        props.put("erpStatus", ERP_STATUS.MODIFIED.getValue());
        
        final int updateResult = HQL_updateByQuery(
                Timework.class,
                props,
                loggedUser.getId(),
                timeworkId,
                true,
                null,
                0,
                null
        );
        
        if (updateResult > 0) {
            final Map<String, Object> jsonEntityData = getResponseCheckIn(checkIn, localCheckIn, systemTimezone, userTimezone);
            gsh.setJsonEntityData(jsonEntityData);

            gsh.setOperationEstatus(1);
            gsh.setSavedId(timeworkId.toString());
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle doCheckIn(
            final TimeworkMode mode,
            final ILoggedUser loggedUser,
            final Optional<TimeworkDoCheckData> optionalData
    ) {
        final TimeworkDoCheckData data = optionalData.orElse(new TimeworkDoCheckData());
        final Long timeworkId = data.getTimeworkId();
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final Long loggedUserId = loggedUser.getId();
        if (alreadyCheckedIn(timeworkId, mode, loggedUser)) {
            final Timework last = lastCheckIn(timeworkId, mode, loggedUser);
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("already_checked_in");
            gsh.setJsonEntityData(Utilities.entityToMap(last));
            return gsh;
        }

        final String systemTimezone = getSystemTimezone();
        final String userTimezone = loggedUser.getTimezone();

        final Timework timework = new Timework();

        timework.setCode(UuidUtils.toString(UUID.randomUUID()));
        timework.setStatus(Timework.STATUS.CHECK_IN.getValue());

        timework.setUserId(loggedUserId);
        timework.setCreatedBy(loggedUserId);
        timework.setLastModifiedBy(loggedUserId);

        final Date currentDate = new Date();
        timework.setCreatedDate(currentDate);
        timework.setLastModifiedDate(currentDate);

        timework.setMode(mode.getValue());
        timework.setCheckIn(currentDate);
        timework.setCheckInTz(systemTimezone);

        final Date localCheckIn = DateUtil.convertDateToSystemTz(currentDate, userTimezone, systemTimezone);
        timework.setLocalCheckIn(localCheckIn);
        timework.setLocalCheckInTz(userTimezone);

        configureOptionalData(timework, data);

        final GeolocationCoordinates location = data.getLocation();

        if (location != null) {
            location.setId(-1L);
            final GeolocationCoordinates savedLocation = makePersistent(location, loggedUserId);
            timework.setCheckInLocationId(savedLocation.getId());
        }

        if (checkSpliceHour(timework.getCheckIn(),loggedUserId)) {
            return new GenericSaveHandle("spliced_hours");
        }

        // guardar
        final Timework savedTimework = makePersistent(timework, loggedUserId);

        final List<Timework> records = Collections.singletonList(savedTimework);
        final Map<String, Object> jsonEntityData = getMultipleRecordsResponseData(records, location, null);
        gsh.setJsonEntityData(jsonEntityData);

        gsh.setOperationEstatus(1);
        gsh.setSavedId(savedTimework.getId().toString());
        return gsh;
    }

    private void configureOptionalData(final Timework timework, final IExternalFormData data) {
        timework.setDocumentId(data.getDocumentId());
        timework.setDocumentMasterId(data.getDocumentMasterId());
        timework.setDocumentDescription(data.getDocumentDescription());
        timework.setSurveyId(data.getSurveyId());
        timework.setSurveyFieldId(data.getSurveyFieldId());
        timework.setOutsandingSurveyId(data.getOutsandingSurveyId());
        timework.setOutstandingSurveyCode(data.getOutstandingSurveyCode());
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle doCheckOut(
            final TimeworkMode mode,
            final ILoggedUser loggedUser,
            final Optional<TimeworkDoCheckData> optionalData
    ) {
        final TimeworkDoCheckData data = optionalData.orElse(new TimeworkDoCheckData());
        final Long timeworkId = data.getTimeworkId();
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final Long loggedUserId = loggedUser.getId();
        if (!alreadyCheckedIn(timeworkId, mode, loggedUser)) {
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("already_checked_out");
            return gsh;
        }

        final String systemTimezone = getSystemTimezone();
        final String userTimezone = loggedUser.getTimezone();

        final Timework timework = lastCheckIn(timeworkId, mode, loggedUser);
        if (timework == null) {
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("already_checked_out");
            return gsh;
        }

        timework.setStatus(Timework.STATUS.CHECK_OUT.getValue());
        timework.setErpStatus(ERP_STATUS.READY.getValue());

        Date currentDate = new Date();

        final Date checkOut = currentDate;

        final Date checkIn = timework.getCheckIn();

        boolean sameDay = DateUtils.isSameDay(checkIn, currentDate);
        if (!sameDay) {
            currentDate = handler.getStartOfNextDay(checkIn);
        }

        timework.setLastModifiedDate(checkOut);

        timework.setCheckOut(currentDate);
        timework.setCheckOutTz(systemTimezone);

        Date localCheckOut = DateUtil.convertDateToSystemTz(currentDate, userTimezone, systemTimezone);
        timework.setLocalCheckOut(localCheckOut);
        timework.setLocalCheckOutTz(userTimezone);

        final GeolocationCoordinates location = data.getLocation();
        if (location != null) {
            location.setId(-1L);
            final GeolocationCoordinates savedLocation = makePersistent(location, loggedUserId);
            if (savedLocation != null) {
                timework.setCheckOutLocationId(savedLocation.getId());
            }
            timework.setCheckOutLocation(savedLocation);
        }

        final Timework savedTimework = makePersistent(timework, loggedUserId);

        final List<Timework> records = new ArrayList<>();
        records.add(savedTimework);

        if (!sameDay) {
            final List<TimeworkRangeDTO> dateRanges = handler.splitDateRangeByDay(currentDate, checkOut);
            dateRanges.stream().map(range -> createExtraRecord(
                    timework,
                    range.getStart(),
                    range.getEnd(),
                    userTimezone,
                    systemTimezone,
                    location,
                    loggedUserId
            )).forEach(records::add);
        }

        final Map<String, Object> jsonEntityData = getMultipleRecordsResponseData(records, null, location);
        gsh.setJsonEntityData(jsonEntityData);

        gsh.setSavedId(savedTimework.getId().toString());


        gsh.setOperationEstatus(1);

        return gsh;
    }

    @Nonnull
    private Timework createExtraRecord(
            @Nonnull Timework previous,
            @Nonnull Date checkIn,
            @Nonnull Date checkOut,
            @Nonnull String userTimezone,
            @Nonnull String systemTimezone,
            @Nullable final GeolocationCoordinates location,
            @Nonnull Long loggedUserId
    ) {
        final Timework timework = new Timework();

        timework.setCode(UuidUtils.toString(UUID.randomUUID()));

        timework.setMode(previous.getMode());
        timework.setUserId(previous.getUserId());
        timework.setMode(previous.getMode());

        configureOptionalData(timework, previous);

        timework.setStatus(Timework.STATUS.CHECK_OUT.getValue());
        timework.setErpStatus(ERP_STATUS.READY.getValue());
        timework.setCreatedBy(loggedUserId);
        timework.setLastModifiedBy(loggedUserId);
        final Date now = new Date();
        timework.setCreatedDate(now);
        timework.setLastModifiedDate(now);

        timework.setCheckIn(checkIn);
        timework.setCheckInTz(systemTimezone);
        final Date localCheckIn = DateUtil.convertDateToSystemTz(checkIn, userTimezone, systemTimezone);
        timework.setLocalCheckIn(localCheckIn);
        timework.setLocalCheckInTz(userTimezone);

        timework.setCheckOut(checkOut);
        timework.setCheckOutTz(systemTimezone);
        final Date localCheckOut = DateUtil.convertDateToSystemTz(checkOut, userTimezone, systemTimezone);
        timework.setLocalCheckOut(localCheckOut);
        timework.setLocalCheckOutTz(userTimezone);
        if (location != null) {
            timework.setCheckInLocationId(location.getId());
        }
        timework.setCheckInLocation(location);
        timework.setCheckOutLocation(location);
        if (location != null) {
            timework.setCheckOutLocationId(location.getId());
        }

        return makePersistent(timework, loggedUserId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle add(
            final List<Timework> records,
            final TimeworkDTO saveData,
            final Long userId,
            final ILoggedUser loggedUser
    ) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (saveData.getCheckIn() == null || saveData.getCheckOut() == null || userId == null) {
            return gsh;
        }

        final Date currentDate = new Date();
        final String systemTimezone = getSystemTimezone();
        final String userTimezone = loggedUser.getTimezone();

        final Timework timework = new Timework();

        // datos del registro
        timework.setId(-1L);
        timework.setCode(UuidUtils.toString(UUID.randomUUID()));
        timework.setStatus(Timework.STATUS.CHECK_OUT.getValue());
        timework.setMode(TimeworkMode.SEQUENTIAL_TIME.getValue());
        timework.setCheckIn(saveData.getCheckIn());
        if (saveData.getCheckInTz() != null) {
            timework.setCheckInTz(systemTimezone);
            saveData.setCheckInTz(systemTimezone);
        } else {
            timework.setCheckIn(null);
            saveData.setCheckInTz(null);
        }

        timework.setCheckOut(saveData.getCheckOut());
        if (saveData.getCheckOut() != null) {
            timework.setCheckOutTz(systemTimezone);
            saveData.setCheckOutTz(systemTimezone);
        } else {
            timework.setCheckOutTz(null);
            saveData.setCheckOutTz(null);
        }

        if (saveData.getCheckIn() != null) {
            final Date localCheckIn = DateUtil.convertDateToSystemTz(saveData.getCheckIn(), userTimezone, systemTimezone);
            timework.setLocalCheckIn(localCheckIn);
            timework.setLocalCheckInTz(userTimezone);
            saveData.setLocalCheckIn(localCheckIn);
            saveData.setLocalCheckDate(localCheckIn);
            saveData.setLocalCheckInTz(userTimezone);
        } else {
            timework.setLocalCheckIn(null);
            timework.setLocalCheckInTz(null);
            saveData.setLocalCheckIn(null);
            saveData.setLocalCheckDate(null);
            saveData.setLocalCheckInTz(null);
        }

        if (saveData.getCheckOut() != null) {
            final Date localCheckOut = DateUtil.convertDateToSystemTz(saveData.getCheckOut(), userTimezone, systemTimezone);
            timework.setLocalCheckOut(localCheckOut);
            timework.setLocalCheckOutTz(userTimezone);
            saveData.setLocalCheckOut(localCheckOut);
            saveData.setLocalCheckOutTz(userTimezone);
        } else {
            timework.setLocalCheckOut(null);
            timework.setLocalCheckOutTz(null);
            saveData.setLocalCheckOut(null);
            saveData.setLocalCheckOutTz(null);
        }

        timework.setUserId(userId);

        // datos de auditoria
        final Long loggedUserId = loggedUser.getId();
        timework.setCreatedBy(loggedUserId);
        timework.setLastModifiedBy(loggedUserId);
        timework.setCreatedDate(currentDate);
        timework.setLastModifiedDate(currentDate);

        // guardar
        final Timework savedTimework = makePersistent(timework, loggedUserId);

        records.add(savedTimework);

        final Map<String, Object> jsonEntityData = getResponseData(savedTimework);
        gsh.setJsonEntityData(jsonEntityData);

        gsh.setOperationEstatus(1);
        gsh.setSavedId(savedTimework.getId().toString());
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> getWidgetMineRows(final ILoggedUser loggedUser) {
        final Date now = getLoggedUserNowWithTz(loggedUser);
        return HQL_findByQuery(
                WIDGET_MINE_ROWS_SELECT_HQL,
                ImmutableMap.of(
                    "userId", loggedUser.getId(),
                    "now", now
                )
        );
    }

    private boolean invalidListAccess(final ILoggedUser loggedUser) {
        if (!Utilities.isTimeworkAvailable()) {
            return true;
        }
        if (loggedUser.isAdmin()) {
            return false;
        }
        final List<ProfileServices> services = loggedUser.getServices();
        return !services.contains(ProfileServices.TIMEWORK_LIST)
                && !services.contains(ProfileServices.TIMEWORK_REGISTER)
                && !services.contains(ProfileServices.FORM_STOPWATCH_LIST)
                && !services.contains(ProfileServices.FORM_STOPWATCH_REGISTER)
                && !services.contains(ProfileServices.TIMEWORK_SYNC);
    }

    private boolean setListRecordsFilters(final IGridFilter filter, final ILoggedUser loggedUser) {
        if (invalidListAccess(loggedUser)) {
            getLogger().error("User {} has no access to timework list", loggedUser.getId());
            return false;
        }
        if (loggedUser.isAdmin()) {
            return true;
        }
        final List<ProfileServices> services = loggedUser.getServices();
        final List<String> newConditions = new ArrayList<>();
        if (services.contains(ProfileServices.TIMEWORK_LIST) || services.contains(ProfileServices.TIMEWORK_SYNC)) {
            newConditions.add( " t.mode = " + TimeworkMode.SEQUENTIAL_TIME.getValue());
        }
        if (services.contains(ProfileServices.TIMEWORK_REGISTER)) {
            newConditions.add( " t.mode = " + TimeworkMode.SEQUENTIAL_TIME.getValue() + " AND t.userId = " + loggedUser.getId());
        }
        if (services.contains(ProfileServices.FORM_STOPWATCH_LIST)) {
            final FormHandler formHandler = new FormHandler();
            //TODO: Optimizar, en el QMS Interno en algunos usuarios (NO ADMIN) cada consulta se tarda como 5 segundos
            final String fillOutFilters = formHandler.buildTimeworkFillOuHistoryFilters(filter, loggedUser);
            if (fillOutFilters != null && !fillOutFilters.isEmpty()) {
                newConditions.add(fillOutFilters);
            }
        }
        if (newConditions.isEmpty()) {
            return true;
        }
        final String previousCondition = (String) filter.getCriteria().get("<condition>");
        final String newCondition;
        if (previousCondition != null && !previousCondition.isEmpty()) {
            newCondition = "(" + previousCondition  + ") AND " + getFilterForAnyCondition(newConditions);
        } else {
            newCondition = getFilterForAnyCondition(newConditions);
        }
        filter.getCriteria().put("<condition>", newCondition);
        return true;
    }

    private String getFilterForAnyCondition(List<String> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return "";
        }
        if (conditions.size() == 1) {
            return "(" + conditions.get(0) + ")";
        }
        final String join = String.join(") OR (", conditions);
        return " ((" + join + "))";
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<TimeworkListDto> getListRecords(final IGridFilter filter, final ILoggedUser loggedUser) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("t.lastModifiedDate");
            filter.setDirection(Byte.parseByte("2"));
        }
        final boolean filtered = setListRecordsFilters(filter, loggedUser);
        if (!filtered) {
            return new GridInfo<>();
        }
        final String hql = TIMEWORK_RECORD_LIST_SELECT
                .replace(":lang", loggedUser.getLocale().getLanguage())
                + " WHERE t.deleted = 0 ";
        return HQL_getRows(hql, filter);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getConflictedRecords(final SortedPagedFilter filter) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("c.lastModifiedDate");
            filter.setDirection(Byte.parseByte("2"));
        }
        return HQL_getRows(" "
            + " SELECT new map("
                + " c.id as id"
                + ", u.code as userCode"
                + ", u.description as userName"
                + ", c.code as code"
                + ", c.checkIn as checkIn"
                + ", c.checkOut as checkOut"
                + ", c.localCheckIn as localCheckIn"
                + ", c.localCheckOut as localCheckOut"
                + ", c.erpError as erpError"
                + ", c.checkInTz as checkInTz"
                + ", c.checkOutTz as checkOutTz"
                + ", c.localCheckInTz as localCheckInTz"
                + ", c.localCheckOutTz as localCheckOutTz"
            + ")"
            + " FROM " + Timework.class.getCanonicalName() + " c "
            + " JOIN " + User.class.getCanonicalName() + " u "
            + " ON c.userId = u.id "
            + " WHERE c.deleted = 0 "
                + " AND c.erpStatus IN ( " 
                    + ERP_STATUS.CONFLICT.getValue()
                + " ) ",
                filter
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getNotSyncedRecords(final SortedPagedFilter filter) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("c.RecordDate");
            filter.setDirection(Byte.parseByte("2"));
        }
        return SQL_getRowsByQuery(" "
            + " SELECT "
                + " c.ExternalCode AS ExternalCode, "
                + " c.UserExternalCode AS UserExternalCode, "
                + " u.first_name AS UserName, "
                + " c.RecordType AS RecordType,"
                + " c.RecordDate AS RecordDate, "
                + " c.RecordTime AS RecordTime, "
                + " c.Deleted AS Deleted,"
                + " c.SyncError AS SyncError"
            + " FROM timework_to_sync c "
            + " JOIN users u "
            + " ON u.user_id = c.UserId ",
            filter
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getTimeworkUsersList(final SortedPagedFilter filter, final Long loggedUserId) {
        return HQL_getRows( " "
            + " SELECT new map("
                + " c.id as id,"
                + " c.code AS code,"
                + " c.description AS description,"
                + " c.cuenta AS cuenta,"
                + " businessUnit.id AS businessUnitId,"
                + " businessUnit.description AS businessUnit,"
                + " businessUnitDepartment.id AS businessUnitDepartmentId,"
                + " businessUnitDepartment.description AS businessUnitDepartment,"
                + " u.monday as monday,"
                + " u.tuesday as tuesday,"
                + " u.wednesday as wednesday,"
                + " u.thursday as thursday,"
                + " u.friday as friday,"
                + " u.saturday as saturday,"
                + " u.sunday as sunday"
            + ")"
            + " FROM " + User.class.getCanonicalName() + " c "
            + " LEFT JOIN " + UserTimeworkPerDay.class.getCanonicalName() + " u ON u.id = c.id "
            + " JOIN c.puestos job "
            + " JOIN job.perfil prof "
            + " LEFT JOIN c.businessUnitDepartment businessUnitDepartment "
            + " LEFT JOIN businessUnitDepartment.businessUnit businessUnit "
            + " WHERE"
                + " c.deleted = 0 "
                + " AND prof.timeworkRegister = 1 "
            + " GROUP BY"
                + " c.id,"
                + " c.code,"
                + " c.description,"
                + " c.cuenta,"
                + " businessUnit.id,"
                + " businessUnit.description,"
                + " businessUnitDepartment.id,"
                + " businessUnitDepartment.description,"
                + " u.monday,"
                + " u.tuesday,"
                + " u.wednesday,"
                + " u.thursday,"
                + " u.friday,"
                + " u.saturday,"
                + " u.sunday",
                filter
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle userConfigSave(final UserTimeworkPerDay u, final Long loggedUserId) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        Integer user = HQL_findSimpleInteger(" "
                + " SELECT COUNT(c.id)"
                + " FROM " + UserTimeworkPerDay.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                "id",  u.getId()
        );
        try {
            if (user == 0) {
                String sql = " "
                    + " INSERT INTO user_timework_per_day ("
                    + " user_id, monday, tuesday, wednesday, thursday, friday, saturday, sunday)"
                    + " VALUES (" 
                            + u.getId() + ", "
                            + u.getMonday() + ", "
                            + u.getTuesday() + ", "
                            + u.getWednesday() + ", "
                            + u.getThursday() + ", "
                            + u.getFriday() + ", "
                            + u.getSaturday() + ", "
                            + u.getSunday()
                    +")";
                SQL_updateByQuery(
                        sql,
                        Utilities.EMPTY_MAP,
                        0,
                        Collections.singletonList("user_timework_per_day")
                );
            } else {
                makePersistent(u, loggedUserId);
            }
            gsh.setOperationEstatus(1);
            return gsh;
        } catch (Exception e) {
            gsh.setOperationEstatus(0);
            return gsh;
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean userHasTodayPermission(final Long userId) {
        final Map<String, Object> userInfo = userHasPermission(userId);
        boolean result;
        final String dayNumber = userInfo.get("today").toString();
        switch (dayNumber) {
            case "1":
                result = userInfo.get("sunday").equals(1);
                break;
            case "2":
                result = userInfo.get("monday").equals(1);
                break;
            case "3":
                result = userInfo.get("tuesday").equals(1);
                break;
            case "4":
                result = userInfo.get("wednesday").equals(1);
                break;
            case "5":
                result = userInfo.get("thursday").equals(1);
                break;
            case "6":
                result = userInfo.get("friday").equals(1);
                break;
            case "7":
                result = userInfo.get("saturday").equals(1);
                break;
            default:
                result = false;
                break;
        }
        return result;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Object> userHasPermission(final Long userId) {
        Map<String, Object> userInfo = HQL_findSimpleMap(" "
            + " SELECT new map("
                + " c.monday as monday, "
                + " c.tuesday as tuesday, "
                + " c.wednesday as wednesday, "
                + " c.thursday as thursday, "
                + " c.friday as friday, "
                + " c.saturday as saturday, "
                + " c.sunday as sunday, "
                + " datepartWeekday(getdate()) as today "
            + ") FROM " + UserTimeworkPerDay.class.getCanonicalName() + " c"
            + " WHERE c.id = :id",
                "id",  userId
        );
        if (userInfo.isEmpty()) {
            userInfo = new HashMap<>();
            userInfo.put("monday", 1);
            userInfo.put("tuesday", 1);
            userInfo.put("wednesday", 1);
            userInfo.put("thursday", 1);
            userInfo.put("friday", 1);
            userInfo.put("saturday", 1);
            userInfo.put("sunday", 1);
            userInfo.put("today", 1);
        }
        return userInfo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public TimeworkDTO getRecordDto(final Long timeworkId) {
        return (TimeworkDTO) HQL_findSimpleObject(" "
                        + " SELECT new " + TimeworkDTO.class.getCanonicalName() + " ( "
                            + " t.id AS id "
                            + ", t.mode AS mode"
                            + ", t.userId AS userId "
                            + ", t.checkIn AS checkDate "
                            + ", t.checkIn AS checkIn "
                            + ", t.checkOut AS checkOut "
                            + ", t.checkInTz AS checkInTz"
                            + ", t.checkOutTz AS checkOutTz "
                            + ", t.localCheckIn AS localCheckIn "
                            + ", t.localCheckOut AS localCheckOut "
                            + ", t.localCheckInTz AS localCheckInTz "
                            + ", t.localCheckOutTz AS localCheckOutTz "
                            + ", t.localCheckIn AS localCheckIn "
                            + ", locIn.latitude AS checkInLatitude"
                            + ", locIn.longitude AS checkInLongitude"
                            + ", locOut.latitude AS checkOutLatitude"
                            + ", locOut.longitude AS checkOutLongitude"
                        + ") "
                        + " FROM " + Timework.class.getCanonicalName() + " t"
                        + " LEFT JOIN t.checkInLocation locIn"
                        + " LEFT JOIN t.checkOutLocation locOut"
                        + " WHERE t.id = :id",
                ImmutableMap.of("id", timeworkId)
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date getLastSyncWithOneDayDelay() {
        return HQL_findDate(" "
            + " SELECT min(c.lastModifiedDate)"
            + " FROM " + Timework.class.getCanonicalName() + " c "
            + " WHERE c.deleted = 0 "
                + " AND c.lastModifiedDate <= dateadd(day, -1, current_date())"
                + " AND c.erpStatus = " + ERP_STATUS.READY.getValue()
        );        
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveConflictedRecord(final Long timeworkId, final String error) {
        if (timeworkId == null) {
            getLogger().error("Cannot save timework as conflicted, timeworkId is null");
            return;
        }
        final Map<String, Object> result = HQL_findSimpleMap(
                SELECT_CONFLICTED_DETAIL_HQL,
                ImmutableMap.of("id", timeworkId)
        );
        if (result.isEmpty()) {
            getLogger().error("Cannot save timework as conflicted, timeworkId: {} not found", timeworkId);
            return;
        }
        if (Objects.equals(result.get("erpStatus"), ERP_STATUS.CONFLICT.getValue())
                && Objects.equals(result.get("erpError"), error)) {
            getLogger().trace("Timework with timeworkId: {} already saved as conflicted", timeworkId);
            return;
        }
        final Map<String, Object> params = new HashMap<>(3);
        params.put("error", error);
        params.put("status", ERP_STATUS.CONFLICT.getValue());
        params.put("id", timeworkId);
        HQL_updateByQuery(UPDATE_CONFLICTED_HQL, params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateSavedTimework(final String timeworkIds) {
        if (timeworkIds == null || timeworkIds.isEmpty()) {
            return 0;
        }
        final Map<String, Object> params = new HashMap<>();
        final List<Long> ids = Arrays.stream(timeworkIds.split(","))
                .filter(Objects::nonNull)
                .filter((id) -> !id.isEmpty())
                .map(Long::parseLong).collect(Collectors.toList());
        return HQL_updateByQuery(" "
            + " UPDATE " + Timework.class.getCanonicalName() + " t "
            + " SET "
                + " t.lastModifiedDate = current_timestamp(),"
                + " t.erpStatus = " + ERP_STATUS.SAVED.getValue() + ","
                + " t.erpError = ''"
            + " WHERE " + BindUtil.bindFilterList("t.id", ids, true, params),
            params
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateConflictedDeleted() {
        //TODO: Cargar los IDS de los registros que se van a eliminar y actualizar por ID
        return HQL_updateByQuery(" "
            + " UPDATE " + Timework.class.getCanonicalName() + " t "
            + " SET "
                + " t.lastModifiedDate = current_timestamp(),"
                + " t.erpStatus = " + ERP_STATUS.SAVED.getValue()
            + " WHERE "
                + " t.deleted = 1 "
                + " AND t.erpStatus = " + ERP_STATUS.CONFLICT.getValue()
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<TimeworkListDto> getDeletedRecords(final IGridFilter filter, final ILoggedUser loggedUser) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("t.lastModifiedDate");
            filter.setDirection(Byte.parseByte("2"));
        }
        final boolean filtered = setListRecordsFilters(filter, loggedUser);
        if (!filtered) {
            return new GridInfo<>();
        }
        return HQL_getRows(
                TIMEWORK_RECORD_LIST_SELECT.replace(":lang", loggedUser.getLocale().getLanguage()) + " WHERE t.deleted = 1 ",
                filter
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> restoreTimerwork(final Long id) {
        if (id == null) {
            return new ResponseEntity<>(false, HttpStatus.CONFLICT);
        }
        Integer result = HQL_updateByQuery(" "
                        + " UPDATE " + Timework.class.getCanonicalName() + " q "
                        + " SET q.deleted = 0"
                        + " WHERE q.id = :id",
                ImmutableMap.of("id", id)
        );
        return Objects.equals(result, 1)
                ? new ResponseEntity<>(true, HttpStatus.OK) :
                new ResponseEntity<>(false, HttpStatus.CONFLICT);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean checkSplicedHours(
            final TimeworkDTO data,
            final Long userId,
            final Boolean excludeSelf,
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>();

        String extraWhere = "";
        if (excludeSelf) {
            extraWhere = " AND t.id != :excludeId";
            params.put("excludeId", data.getTimeworkId());
        }
        params.put("localCheckIn", data.getLocalCheckIn());
        params.put("localCheckOut", data.getLocalCheckOut());

        params.put("localCheckDate", data.getLocalCheckIn());
        params.put("userId", userId);

        final Long splicedIds = HQL_findSimpleLong(" "
            + " SELECT COUNT(t.id) "
            + " FROM " + Timework.class.getCanonicalName() + " t "
            + " WHERE "
                + " t.userId = :userId "
                + extraWhere    
                + " AND t.deleted = 0 "
                + " AND trunc(t.localCheckIn) = trunc(:localCheckDate)"
                + " AND ( "
                        + " ( t.localCheckIn < :localCheckOut AND t.localCheckOut > :localCheckIn )"
                        + " OR ( :localCheckOut IS NULL AND :localCheckIn < t.localCheckOut ) "
                        + " OR ( t.localCheckIn = :localCheckIn AND t.localCheckOut = :localCheckOut ) "
                        + " OR ( t.localCheckIn = :localCheckIn)"
                        + " OR ( t.localCheckOut IS NULL AND t.localCheckIn < :localCheckOut)"
                + " )",
                params
        );

        return splicedIds > 0;
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean checkSpliceHour(final Date checkHour, final Long userId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("checkHour", checkHour);
        params.put("userId", userId);
        
        final Long splicedIds = HQL_findSimpleLong(" "
                + " SELECT count(t.id) "
                + " FROM " + Timework.class.getCanonicalName() + " t "
                + " WHERE "
                + " format(:checkHour, 'yyyy-MM-dd HH:mm:ss') BETWEEN t.localCheckIn AND t.localCheckOut "
                + " AND trunc(t.createdDate) = format(:checkHour, 'yyyy-MM-dd') "
                + " AND t.userId = :userId "
                + " AND t.deleted = 0 ",
                params
        );

        return splicedIds > 0;
    } 
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Boolean hasRegistryOpen(final Date date, final Long userId) {
    
        final Map<String, Object> params = new HashMap<>();
        params.put("date", date);
        params.put("userId", userId);
        
        final Long countIds = HQL_findSimpleLong(" "
            + " SELECT COUNT(t.id)"
            + " FROM " + Timework.class.getCanonicalName() + " t "
            + " WHERE t.checkOut IS NULL "
            + " AND t.workedSeconds IS NULL "
            + " AND trunc(t.createdDate) = :date "
            + " AND t.userId = :userId "
            + " AND t.deleted = 0",
                params
        );
        return countIds > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<Long> getTimeworkIds(final TimeworkDoCheckData data, final ILoggedUser loggedUser) {
        final List<Long> timeworkIds = HQL_findByQuery(" "
                        + " SELECT c.id "
                        + " FROM " + Timework.class.getCanonicalName() + " c "
                        + " WHERE c.outsandingSurveyId = :outsandingSurveyId"
                        + " AND c.surveyFieldId = :surveyFieldId"
                        + " AND c.userId = :userId"
                        + " AND c.deleted = 0"
                        + " AND c.mode = " + TimeworkMode.CONCURRENT_TIME.getValue(),
                ImmutableMap.of(
                        "outsandingSurveyId", data.getOutsandingSurveyId(),
                        "surveyFieldId", data.getSurveyFieldId(),
                        "userId", loggedUser.getId()
                )
        );
        if (timeworkIds == null || timeworkIds.isEmpty()) {
            return Collections.emptySet();
        }
        return new LinkedHashSet<>(timeworkIds);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Timework> getTimeworks(List<Long> timeworkIds) {
        if (timeworkIds == null || timeworkIds.isEmpty()) {
            return Collections.emptyList();
        }
        final Map<String, Object> params = new HashMap<>();
        return HQL_findByQuery(" "
                    + " SELECT c "
                    + " FROM " + Timework.class.getCanonicalName() + " c "
                    + " WHERE  " + BindUtil.bindFilterList("c.id", timeworkIds, true, params),
                params
        );
    }
}
