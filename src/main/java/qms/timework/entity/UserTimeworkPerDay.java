/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package qms.timework.entity;

import Framework.Config.DomainObject;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;

/**
 *
 * <AUTHOR>
 */


@Entity
@Table(name = "user_timework_per_day")
public class UserTimeworkPerDay extends DomainObject implements Serializable {
    
    private Long id;
    private UserRef user;
    
    private Integer monday;
    private Integer tuesday;
    private Integer wednesday;
    private Integer thursday;
    private Integer friday;
    private Integer saturday;
    private Integer sunday;
    
    public UserTimeworkPerDay() {
    }

    public UserTimeworkPerDay(Long id) {
        this.id = id;
    }
    
    public UserTimeworkPerDay(Long id, Integer monday, Integer tuesday, Integer wednesday, Integer thursday, Integer friday, Integer saturday, Integer sunday) {
        this.id = id;
        this.monday = monday;
        this.tuesday = tuesday;
        this.wednesday = wednesday;
        this.thursday = thursday;
        this.friday = friday;
        this.saturday = saturday;
        this.sunday = sunday;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "user_id", nullable = false, precision = 19)
    @Override
    public Long getId() {
          return id;
    }

    @Override
    public void setId(Long id) {
          this.id = id;
    }
    
    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "user_id",
            insertable = false, updatable = false)
    public UserRef getUser() {
        return user;
    }
    
    public void setUser(UserRef user) {
        this.user = user;
    }
    
    @Column(name = "monday")
    public Integer getMonday() {
        return monday;
    }

    public void setMonday(Integer monday) {
        this.monday = monday;
    }

    @Column(name = "tuesday")
    public Integer getTuesday() {
        return tuesday;
    }

    public void setTuesday(Integer tuesday) {
        this.tuesday = tuesday;
    }

    @Column(name = "wednesday")
    public Integer getWednesday() {
        return wednesday;
    }

    public void setWednesday(Integer wednesday) {
        this.wednesday = wednesday;
    }

    @Column(name = "thursday")
    public Integer getThursday() {
        return thursday;
    }

    public void setThursday(Integer thursday) {
        this.thursday = thursday;
    }

    @Column(name = "friday")
    public Integer getFriday() {
        return friday;
    }

    public void setFriday(Integer friday) {
        this.friday = friday;
    }

    @Column(name = "saturday")
    public Integer getSaturday() {
        return saturday;
    }

    public void setSaturday(Integer saturday) {
        this.saturday = saturday;
    }

    @Column(name = "sunday")
    public Integer getSunday() {
        return sunday;
    }

    public void setSunday(Integer sunday) {
        this.sunday = sunday;
    }
    
}
