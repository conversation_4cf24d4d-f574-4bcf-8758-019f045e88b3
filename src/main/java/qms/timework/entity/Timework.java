package qms.timework.entity;

import DPMS.Mapping.IAuditableEntity;
import Framework.Config.StandardEntity;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.GenerationTime;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.entity.GeolocationCoordinates;
import qms.timework.util.IExternalFormData;
import qms.timework.util.ITimework;
import qms.timework.util.TimeworkMode;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "timework")
public class Timework extends StandardEntity<Timework> implements IAuditableEntity, ITimework<GeolocationCoordinates>, IExternalFormData {

    private static final long serialVersionUID = 1L;

    private String code;
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer erpStatus = 0;
    private String erpError = "";

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;

    private Integer mode = TimeworkMode.SEQUENTIAL_TIME.getValue();
    private Integer workedSeconds;
    private Integer workedHours;
    private Long userId;
    private Date checkIn;
    private Date checkOut;
    
    private Long checkInLocationId;
    private Long checkOutLocationId;
    
    private GeolocationCoordinates checkInLocation;
    private GeolocationCoordinates checkOutLocation;

    private String checkInTz;
    private String checkOutTz;

    private Date localCheckIn;
    private Date localCheckOut;
    private String localCheckInTz;
    private String localCheckOutTz;

    private Long documentId;
    private String documentMasterId;
    private String documentDescription;
    private Long surveyId;
    private Long surveyFieldId;
    private Long outsandingSurveyId;
    private String outstandingSurveyCode;

    public Timework() {
    }

    public enum STATUS implements IStatusEnum {
        CHECK_IN(1, IStatusEnum.COLOR_RED),
        CHECK_OUT(2, IStatusEnum.COLOR_GREEN);
        private final Integer value;
        private final String gridCube;

        STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }

        @Override
        public Integer getValue() {
            return this.value;
        }

        @Override
        public String getGridCube() {
            return this.gridCube;
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return CHECK_OUT;
        }

        public static STATUS getStatus(Integer status) {
            for (STATUS s : values()) {
                if (Objects.equals(s.value, status)) {
                    return s;
                }
            }
            return null;
        }
    }

    public enum ERP_STATUS {
        READY(0),
        SAVED(1),
        MODIFIED(2),
        CONFLICT(3);
        private final Integer value;
        ERP_STATUS(Integer value) {
            this.value = value;
        }
        public Integer getValue() {
            return this.value;
        }
    }

    @Id
    @Column(name = "timework_id", nullable = false)
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "mode")
    public Integer getMode() {
        return mode;
    }

    public void setMode(Integer mode) {
        this.mode = mode;
    }

    @Generated(value = GenerationTime.ALWAYS)
    @Column(name = "worked_seconds", insertable = false, updatable = false)
    public Integer getWorkedSeconds() {
        return workedSeconds;
    }

    public void setWorkedSeconds(Integer workedSeconds) {
        this.workedSeconds = workedSeconds;
    }

    @Basic(optional = false)
    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            this.status = 1;
        } else {
            this.status = status;
        }
    }

    @Column(name = "erp_status")
    public Integer getErpStatus() {
        return erpStatus;
    }

    public void setErpStatus(Integer erpStatus) {
        if (erpStatus == null) {
            this.erpStatus = 0;
        } else {
            this.erpStatus = erpStatus;
        }
    }

    @Column(name = "erp_error")
    public String getErpError() {
        return erpError;
    }

    public void setErpError(String erpError) {
        this.erpError = erpError;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            this.deleted = 0;
        } else {
            this.deleted = deleted;
        }
    }

    @Column(name = "created_date", updatable = false)
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @CreatedDate
    @Override
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Column(name = "last_modified_date")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @CreatedBy
    @Override
    @Column(name = "created_by", updatable = false)
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @LastModifiedBy
    @Column(name = "last_modified_by")
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return this.userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Column(name = "check_in")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCheckIn() {
        return checkIn;
    }

    public void setCheckIn(Date checkIn) {
        this.checkIn = checkIn;
    }

    @Column(name = "check_out")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCheckOut() {
        return checkOut;
    }

    public void setCheckOut(Date checkOut) {
        this.checkOut = checkOut;
    }
    
    @Column(name = "check_in_location_id")
    public Long getCheckInLocationId() {
        return checkInLocationId;
    }

    public void setCheckInLocationId(Long checkInLocationId) {
        this.checkInLocationId = checkInLocationId;
    }
    @Column(name = "check_out_location_id")
    public Long getCheckOutLocationId() {
        return checkOutLocationId;
    }

    public void setCheckOutLocationId(Long checkOutLocationId) {
        this.checkOutLocationId = checkOutLocationId;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "check_in_location_id", insertable = false ,updatable = false)
    public GeolocationCoordinates getCheckInLocation() {
        return checkInLocation;
    }

    public void setCheckInLocation(GeolocationCoordinates checkInLocation) {
        this.checkInLocation = checkInLocation;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "check_out_location_id", insertable = false ,updatable = false)
    public GeolocationCoordinates getCheckOutLocation() {
        return checkOutLocation;
    }

    public void setCheckOutLocation(GeolocationCoordinates checkOutLocation) {
        this.checkOutLocation = checkOutLocation;
    }

    @Column(name = "check_in_tz")
    public String getCheckInTz() {
        return checkInTz;
    }

    public void setCheckInTz(String checkInTz) {
        this.checkInTz = checkInTz;
    }

    @Column(name = "check_out_tz")
    public String getCheckOutTz() {
        return checkOutTz;
    }

    public void setCheckOutTz(String checkOutTz) {
        this.checkOutTz = checkOutTz;
    }

    @Column(name = "local_check_in")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLocalCheckIn() {
        return localCheckIn;
    }

    public void setLocalCheckIn(Date localCheckIn) {
        this.localCheckIn = localCheckIn;
    }

    @Column(name = "local_check_out")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLocalCheckOut() {
        return localCheckOut;
    }

    public void setLocalCheckOut(Date localCheckOut) {
        this.localCheckOut = localCheckOut;
    }

    @Column(name = "local_check_in_tz")
    public String getLocalCheckInTz() {
        return localCheckInTz;
    }

    public void setLocalCheckInTz(String localCheckInTz) {
        this.localCheckInTz = localCheckInTz;
    }

    @Column(name = "local_check_out_tz")
    public String getLocalCheckOutTz() {
        return localCheckOutTz;
    }

    public void setLocalCheckOutTz(String localCheckOutTz) {
        this.localCheckOutTz = localCheckOutTz;
    }

    @Generated(value = GenerationTime.ALWAYS)
    @Column(name = "worked_hours", insertable = false, updatable = false)
    public Integer getWorkedHours() {
        return workedHours;
    }

    public void setWorkedHours(Integer workedHours) {
        this.workedHours = workedHours;
    }

    @Column(name = "document_id")
    @Override
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Column(name = "document_master_id")
    @Override
    public String getDocumentMasterId() {
        return documentMasterId;
    }

    public void setDocumentMasterId(String documentMasterId) {
        this.documentMasterId = documentMasterId;
    }

    @Column(name = "document_description")
    @Override
    public String getDocumentDescription() {
        return documentDescription;
    }

    public void setDocumentDescription(String documentDescription) {
        this.documentDescription = documentDescription;
    }

    @Column(name = "survey_id")
    @Override
    public Long getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(Long surveyId) {
        this.surveyId = surveyId;
    }

    @Column(name = "survey_field_id")
    @Override
    public Long getSurveyFieldId() {
        return surveyFieldId;
    }

    public void setSurveyFieldId(Long surveyFieldId) {
        this.surveyFieldId = surveyFieldId;
    }

    @Column(name = "outstanding_surveys_id")
    @Override
    public Long getOutsandingSurveyId() {
        return outsandingSurveyId;
    }

    public void setOutsandingSurveyId(Long outsandingSurveyId) {
        this.outsandingSurveyId = outsandingSurveyId;
    }

    @Column(name = "outstanding_surveys_code")
    @Override
    public String getOutstandingSurveyCode() {
        return outstandingSurveyCode;
    }

    public void setOutstandingSurveyCode(String outstandingSurveyCode) {
        this.outstandingSurveyCode = outstandingSurveyCode;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 17 * hash + Objects.hashCode(this.code);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Timework other = (Timework) obj;
        if (!Objects.equals(this.code, other.code)) {
            return false;
        }
        if (!Objects.equals(this.userId, other.userId)) {
            return false;
        }
        if (!Objects.equals(this.checkIn, other.checkIn)) {
            return false;
        }
        return Objects.equals(this.checkOut, other.checkOut);
    }

    @Override
    public String toString() {
        return "Timework{" + "userId=" + userId + ", checkIn=" + checkIn + ", checkOut=" + checkOut + '}';
    }


}
