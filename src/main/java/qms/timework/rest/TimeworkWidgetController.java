package qms.timework.rest;

import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import com.google.api.client.util.Objects;
import com.google.common.collect.ImmutableMap;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import qms.access.dto.ILoggedUser;
import qms.framework.entity.GeolocationCoordinates;
import qms.framework.rest.SecurityUtils;
import qms.timework.dao.ITimeworkDAO;
import qms.timework.dto.TimeworkDoCheckData;
import qms.timework.dto.TimeworkListDto;
import qms.timework.entity.Timework;
import qms.timework.entity.UserTimeworkPerDay;
import qms.timework.util.TimeworkMode;
import qms.util.DateUtil;
import qms.util.GridFilter;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Lazy
@RestController
@RequestMapping("timework-widget")
public class TimeworkWidgetController extends Loggable {

    private static final String HAS_RESTORE_ACCESS = "hasAnyAuthority("
            + "'IS_ADMIN', 'TIMEWORK_RESTORE'"
            + ")";

    private static final String HAS_SETTINGS_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'TIMEWORK_SETTINGS'"
    + ")";
    
    private static final String HAS_REGISTER_ACCESS = "hasAnyAuthority("
        + "'IS_ADMIN', 'TIMEWORK_REGISTER', 'FORM_STOPWATCH_REGISTER'"
    + ")";

    private final ITimeworkDAO dao;

    public TimeworkWidgetController(@Qualifier("TimeworkDAO") ITimeworkDAO dao) {
        this.dao = dao;
    }

    @GetMapping()
    @RequestMapping("/mine")
    @PreAuthorize(HAS_REGISTER_ACCESS)
    public List<Map<String, Object>> mine() {
        return dao.getWidgetMineRows(SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping("/mine/list")
    @PreAuthorize(HAS_REGISTER_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<TimeworkListDto> mineList(@RequestBody GridFilter filter){
        filter.getCriteria().put("<condition>", " "
            + " t.userId = " + SecurityUtils.getLoggedUserId()
        );
        return dao.getListRecords(filter, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping("/users/list")
    @PreAuthorize(HAS_SETTINGS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> usersList(@RequestBody GridFilter filter){
        return dao.getTimeworkUsersList(filter, SecurityUtils.getLoggedUserId());
    }

    @PostMapping()
    @RequestMapping("/user/save")
    @PreAuthorize(HAS_SETTINGS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle userConfigSave(@RequestBody UserTimeworkPerDay u){
        return dao.userConfigSave(u, SecurityUtils.getLoggedUserId());
    }
    
    @GetMapping()
    @RequestMapping("/user")
    @PreAuthorize(HAS_REGISTER_ACCESS)
    public Map<String, Object> userHasPermission() {
        return dao.userHasPermission(SecurityUtils.getLoggedUserId());
    }

    private GenericSaveHandle validateCheck(Optional<TimeworkDoCheckData> optionalData) {
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (!dao.userHasTodayPermission(loggedUserId)) {
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("failed-permission");
            return gsh;
        }
        final TimeworkDoCheckData data = optionalData.orElse(null);
        final GeolocationCoordinates location = data != null ? data.getLocation() : null;
        if (Objects.equal(Utilities.getSettings().getTrackGeolocation(), 1) && location == null) {
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("failed-geolocation");
            return gsh;
        }
        final GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.setOperationEstatus(1);
        return gsh;
    }

    /**
     * Supported URLS:
     * - timework-widget/1/check-in
     * - timework-widget/2/check-in
     */
    @PostMapping()
    @RequestMapping({"/{mode:[12]}/check-in"})
    @PreAuthorize(HAS_REGISTER_ACCESS)
    public GenericSaveHandle doCheckIn(
            final @PathVariable(value = "mode") Integer mode,
            final @RequestBody Optional<TimeworkDoCheckData> optionalData
    ) {
        final GenericSaveHandle gsh = validateCheck(optionalData);
        if (gsh.getOperationEstatus() == 0) {
            return gsh;
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final TimeworkMode timeworkMode = TimeworkMode.parseValue(mode);
        return dao.doCheckIn(timeworkMode, loggedUser, optionalData);
    }

    @GetMapping()
    @RequestMapping("/now")
    @PreAuthorize(HAS_REGISTER_ACCESS)
    public Map<String, Object> now() {
        final String userTimezone = SecurityUtils.getLoggedUser().getTimezone();
        final String localNow = DateUtil.formatNowWithSystemTz(userTimezone);
        return ImmutableMap.of("now", localNow, "timezone", Utilities.getSettings().getTimeZone());
    }

    /**
     * Supported URLS:
     * - timework-widget/1/check-out
     * - timework-widget/2/check-out
     */
    @PostMapping()
    @RequestMapping({"/{mode:[12]}/check-out"})
    @PreAuthorize(HAS_REGISTER_ACCESS)
    public GenericSaveHandle checkOut(
            final @PathVariable(value = "mode") Integer mode,
            final @RequestBody Optional<TimeworkDoCheckData> optionalData
    ) {
        final GenericSaveHandle gsh = validateCheck(optionalData);
        if (gsh.getOperationEstatus() == 0) {
            return gsh;
        }
        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        final TimeworkMode timeworkMode = TimeworkMode.parseValue(mode);
        return dao.doCheckOut(timeworkMode, loggedUser, optionalData);
    }

    @GetMapping()
    @PreAuthorize(HAS_REGISTER_ACCESS)
    @RequestMapping({"recordsByDateRange/{startDate}/{endDate}"})
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> recordsByDateRange(
            @PathVariable(value = "startDate", required = false) String startDate,
            @PathVariable(value = "endDate", required = false) String endDate
    ) {
        if (startDate == null || endDate == null) {
            getLogger().error(
                    "Input dates startdate: {}, endDate: {} are empty",
                    startDate, endDate
            );
            return Utilities.EMPTY_LIST;
        }

        Date timeworkStartDate = Utilities.formatStrToDate(startDate, "yyyy-MM-dd");
        Date timeworkEndDate = Utilities.formatStrToDate(endDate, "yyyy-MM-dd");

        if (timeworkStartDate == null || timeworkEndDate == null) {
            throw new RuntimeException("Input date '" + startDate + "' is invalid, format should be 'yyyy-MM-dd hh:mm:ss'.");
        }

        timeworkStartDate = Utilities.truncDate(timeworkStartDate);
        timeworkEndDate = Utilities.endTimeDate(timeworkEndDate);

        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return dao.HQL_findByQuery(" "
                + " SELECT new map ("
                        + " t.localCheckIn as day"
                        + ", t.localCheckIn as startDate"
                        + ", t.localCheckOut as endDate"
                        + ", datediffMin(t.localCheckIn, t.localCheckOut) as duration "
                + " )"
                + " FROM " + Timework.class.getCanonicalName() + " t "
                + " WHERE "
                    + " t.userId = :userId"
                    + " AND t.deleted = 0 "
                    + " AND ( "
                        + " t.localCheckIn >= :startDate"
                        + " AND t.localCheckOut <= :endDate"
                    + " )"
                + " ORDER BY t.localCheckIn desc ",
                ImmutableMap.of(
                        "userId", loggedUser.getId(),
                        "startDate", timeworkStartDate,
                        "endDate", timeworkEndDate
                ));
    }
  
    @GetMapping()
    @RequestMapping({"hoursByDateRange/{startDate}/{endDate}"})
    @PreAuthorize(HAS_REGISTER_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer hoursByDateRange(
            @PathVariable(value = "startDate", required = false) String startDate,
            @PathVariable(value = "endDate", required = false) String endDate
    ) {
        if (startDate == null || endDate == null) {
            return null;
        }

        Date timeworkStartDate = Utilities.formatStrToDate(startDate, "yyyy-MM-dd");
        Date timeworkEndDate = Utilities.formatStrToDate(endDate, "yyyy-MM-dd");

        if (timeworkStartDate == null || timeworkEndDate == null) {
            throw new RuntimeException("Input date '" + startDate + "' is invalid, format should be 'yyyy-MM-dd hh:mm:ss'.");
        }

        timeworkStartDate = Utilities.truncDate(timeworkStartDate);
        timeworkEndDate = Utilities.endTimeDate(timeworkEndDate);

        final ILoggedUser loggedUser = SecurityUtils.getLoggedUser();
        return dao.HQL_findSimpleInteger(" "
                + " SELECT "
                    + " SUM(datediffMin(t.checkIn, t.checkOut))"
                + " FROM " + Timework.class.getCanonicalName() + " t "
                + " WHERE "
                + " t.userId = :userId"
                + " AND t.deleted = 0 "
                + " AND ( "
                    + " t.localCheckIn >= :startDate"
                    + " AND t.localCheckIn <= :endDate"
                + " )",
                ImmutableMap.of(
                        "userId",  loggedUser.getId(),
                        "startDate", timeworkStartDate,
                        "endDate", timeworkEndDate
                ));
    }
    
    @GetMapping()
    @PreAuthorize(HAS_REGISTER_ACCESS)
    @RequestMapping("brief-description/{timeworkId}")
    public Map<String, Object> briefDescription(
        final @PathVariable(value = "timeworkId") Long timeworkId
    ){
       String query = " SELECT new map( "
                        + "u.description AS userName "
                        + ",CAST(t.localCheckIn as time) AS localCheckIn "
                        + ",CAST(t.localCheckOut as time) AS localCheckOut "
                    + " )"
                    + " FROM " + Timework.class.getCanonicalName() + " t "
                    + " JOIN "+ User.class.getCanonicalName() + " u "
                        + " ON u.id = t.userId"
                    + " WHERE "
                        + " t.id = :timeworkId";
       
       return dao.HQL_findSimpleMap(query, ImmutableMap.of("timeworkId", timeworkId));
    }
    
    @PostMapping()
    @RequestMapping({"/deleted/list"})
    @PreAuthorize(HAS_RESTORE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<TimeworkListDto> deletedList(@RequestBody GridFilter filter) {
        return dao.getDeletedRecords(filter, SecurityUtils.getLoggedUser());
    }
    
    @PostMapping()
    @RequestMapping({"/deleted/restore"})
    @PreAuthorize(HAS_RESTORE_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ResponseEntity<Boolean> restoreTimework(@RequestBody Long id){
        return dao.restoreTimerwork(id);
    }
}
