package qms.timework.rest;

import bnext.exception.InvalidCipherDecryption;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import mx.bnext.access.Module;
import mx.bnext.core.util.GridInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import qms.form.util.SurveyParserHelper;
import qms.framework.dao.IReportDAO;
import qms.framework.dto.ReportDTO;
import qms.framework.rest.SecurityUtils;
import qms.framework.util.DataSourceCredentialError;
import qms.framework.util.DatabaseQueryHandler;
import qms.framework.util.ReportHandler;
import qms.util.GridFilter;
import qms.util.GridFilterByReportProcessingAccess;
import qms.util.ModuleUtil;
import qms.util.ReportBaseController;

/**
 *
 * <AUTHOR>
 */
@Lazy
@RestController
@RequestMapping("timework")
public class TimeworkController extends ReportBaseController{
    
    private final String HAS_ACCESS = "hasAnyAuthority('IS_ADMIN', 'TIMEWORK_VIEW_REPORT')"
                                   + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('TIMEWORK_CREATE_REPORT'))"
                                   + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('TIMEWORK_ADMON_REPORT_ACCESS'))"
                                   + " || (hasAuthority('USUARIO_CORPORATIVO') && hasAuthority('TIMEWORK_DELETE_REPORT'))";
    
    @Autowired
    @Qualifier("ReportDAO")
    private IReportDAO reportDao;
    
    @Override
    @PostMapping()
    @RequestMapping("reports")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> reports(@RequestBody GridFilter filter) {
        return reportDao.reports(filter, Module.TIMEWORK, SecurityUtils.getLoggedUser());
    }

    @Override
    @PostMapping({"report/{queryId}", "report/{queryId}/{start}/{end}"})
    @PreAuthorize(HAS_ACCESS)
    public ResponseEntity<GridInfo<Map<String, Object>>> report(
            @RequestBody GridFilterByReportProcessingAccess filter,
            final @PathVariable(value = "queryId", required = false) Long queryId,
            final @PathVariable(value = "start", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") Date start,
            final @PathVariable(value = "end", required = false) @DateTimeFormat(pattern="yyyy-MM-dd") Date end
    ) throws DataSourceCredentialError, InvalidCipherDecryption, SQLException {
        if (filter == null) {
            filter = new GridFilterByReportProcessingAccess();
        }
        final DatabaseQueryHandler queryHandler = new DatabaseQueryHandler();
        GridInfo<Map<String, Object>> result = queryHandler.getRowsById(queryId, filter, start, end, SecurityUtils.getLoggedUser());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }
       
    @Override
    @PostMapping()
    @RequestMapping(path = "report-hierarchy/{queryId}/{level}", method = RequestMethod.POST)
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> hierarchLevelRows(
        @RequestBody GridFilter filter,
        final @PathVariable(value = "queryId") Long queryId,
        final @PathVariable(value = "level") Integer level
    ) {
        if (filter == null) {
            filter = new GridFilter();
        }
        final SurveyParserHelper helper = new SurveyParserHelper();
        return helper.getDatabaseQueryHierarchyByLevelRows(filter, queryId, level, SecurityUtils.getLoggedUser());
    }
    
    @Override
    @GetMapping("report/reportListAvailables/{module}")
    @PreAuthorize(HAS_ACCESS)
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map<String, Object>> reportListAvailables(@PathVariable(name = "module") String module) {
        GridInfo<Map<String, Object>> report = reportDao.reports(null, ModuleUtil.fromKey(module), SecurityUtils.getLoggedUser());
        return report.getData();
    }
    
    @Override
    @GetMapping({
            "reportConfig/c/{reportCode}",
            "reportConfig/c/{reportCode}/{masterId}",
            "reportConfig/{reportId}",
            "reportConfig/{reportId}/{masterId}"
    })
    @PreAuthorize(HAS_ACCESS)
    public ResponseEntity<ReportDTO> reportColumn(
        @PathVariable(value = "reportId", required = false) Long reportId,
        final @PathVariable(value = "masterId", required = false) String masterId,
        final @PathVariable(value = "reportCode", required = false) String reportCode
    ) {
        reportId = ReportHandler.getPresetReportId(reportDao, reportId, reportCode);
        final ReportHandler handler = new ReportHandler();
        final ReportDTO config = handler.reportColumn(reportId,  Module.TIMEWORK, SecurityUtils.getLoggedUser());
        if (config == null) {
            getLogger().error(
                    "Report config not found for reportId {} and masterId {}",
                    reportId,
                    masterId);
            return new ResponseEntity(HttpStatus.CONFLICT);
        }
        return new ResponseEntity<>(config, HttpStatus.OK);
    }
    
}
