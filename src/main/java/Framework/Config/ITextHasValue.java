/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package Framework.Config;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
public interface ITextHasValue<T> {
    public String getText();
    public T getValue();
    public void setText(String text);
    public void setValue(T value);
}
