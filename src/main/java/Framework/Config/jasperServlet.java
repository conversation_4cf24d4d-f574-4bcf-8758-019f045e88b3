package Framework.Config;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.util.ClassLoaderUtil;
import mx.bnext.core.util.Loggable;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperRunManager;
import net.sf.jasperreports.engine.export.HtmlExporter;
import net.sf.jasperreports.engine.export.JRXlsExporter;
import net.sf.jasperreports.engine.export.JRXlsExporterParameter;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleHtmlExporterOutput;
import net.sf.jasperreports.web.util.WebHtmlResourceHandler;
import org.slf4j.Logger;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import qms.framework.util.SettingsUtil;
import qms.framework.util.URLUtils;

/**
 *
 * <AUTHOR> Luis Carlos Limas Álvarez
 */
public class jasperServlet extends HttpServlet {

    private jasperreport jasperreport;
    private String reportType = "";
    private String reportFile = "";
    private String downloadLink = "";

    static Logger logger = Loggable.getLogger(jasperServlet.class);
    public jasperServlet() {
        jasperreport = new jasperreport();
        
    }
    /** 
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code> methods.
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        try {
        } finally {
            out.close();
        }
    }

    @Override
    public void finalize() throws Throwable {
        getJasperreport().finalize();
        super.finalize();
    }

    private boolean isValidParam(String name, HttpServletRequest request) {
        return 
                request.getParameterMap().get(name) != null
                && request.getParameterMap().get(name).length > 0
                && !request.getParameterMap().get(name)[0].isEmpty()
                ;
    }
    
    public boolean jasperReport(HttpServletRequest request, HttpServletResponse response) throws IOException, JRException {
        boolean r = false;
        try (final ServletOutputStream ouputStream = response.getOutputStream()){
            final String classPath = ClassLoaderUtil.getClassPathFolder();
            String rutaArchivo = classPath + getJasperreport().parameter("ruta", request);
            logger.debug("/La ruta del archivo es =" + rutaArchivo);
            Map parameters = new HashMap();
            org.apache.commons.beanutils.BeanUtils.populate(getJasperreport(), request.getParameterMap());
            getJasperreport().fillParams();
            String[] llaves = getJasperreport().getParamArrayKeysFromKeyHolder("jasperReport");
            java.util.Date fecha = new java.util.Date();
            logger.debug("------ llaves.length: {}", llaves.length);
            for (int i = 0; i < llaves.length; i++) {
                logger.debug("------ llaves[i]: {}, {}", llaves[i], getJasperreport().getParamValue(llaves[i]));
                parameters.put(llaves[i], getJasperreport().getParamValue(llaves[i]));
            }
            try {
                parameters.put("LEYENDA", "Powered by Bnext QMS, Block Networks S.A. de C.V");
                parameters.put("FECHA_GENERADA", jasperreport.formatDate2(fecha));
            } catch (Exception e) {
                logger.error("There was an error ",e);
            }
            String bnext_filter = "1=1";
            if(isValidParam("dteStart",request)) {
                bnext_filter += " AND dte_start >= CONVERT(DATE, '"+Framework.Config.Utilities.formatDateBy(
                        Framework.Config.Utilities.parseDateBy(request.getParameterMap().get("dteStart")[0], "yyyy-MM-dd")
                        , "dd/MM/yyyy")+"', 103)";
            }
            if(isValidParam("dteEnd",request)) {
                bnext_filter += " AND dte_start <= CONVERT(DATE, '"+Framework.Config.Utilities.formatDateBy(
                        Framework.Config.Utilities.parseDateBy(request.getParameterMap().get("dteEnd")[0], "yyyy-MM-dd")
                        , "dd/MM/yyyy")+"', 103)";
            }
            if(isValidParam("businessUnitId",request)) {
                bnext_filter += " AND business_unit_id = "+request.getParameterMap().get("businessUnitId")[0];
            }
            if(isValidParam("processId",request)) {
                bnext_filter += " AND process_id = "+request.getParameterMap().get("processId")[0];
            }
            parameters.put("bnext_filter", bnext_filter);
            final String appUrl = SettingsUtil.getAppUrlNoSlash();
            final String bnextUrl = appUrl + "/images/logo/bnext.png";
            /**QUITAR EL IF AL RESOLVER PROBLEMA DE: opening input stream from URL***/
            if (URLUtils.isReachable(bnextUrl)) {
                final Long reportLogoId = Utilities.getSettings().getReportLogoId();
                if (reportLogoId != null && reportLogoId > 0) {
                    parameters.put("MAIN_LOGO_URL", appUrl + "/view/v-default-report-logo.view");
                } else {
                    parameters.put("MAIN_LOGO_URL", appUrl + "/qms/assets/images/logo-dark-hd.webp");
                }
                parameters.put("BNEXT_LOGO_URL", bnextUrl);
            } else if (URLUtils.isHttps(appUrl)) {
                logger.error(""
                        + "Error al cargar el logo de los reportes, no se soportan certificados inválidos"
                        + " o autofirmados en la URL del sitio. {}",
                        appUrl
                );
            } else {
                logger.error("Error al abrir el logo");
            }
            logger.debug("Conecta bdd... < {} >", bnext_filter);
            if (!getJasperreport().connect1()) {
                return false;
            }
            
            JasperPrint jasperPrint = JasperFillManager.fillReport(rutaArchivo, parameters, getJasperreport().getConnection());
            jasperPrint.setProperty("net.sf.jasperreports.awt.ignore.missing.font","true");
            try {
                reportType = getJasperreport().parameter("intTipoExtencionReporte", request);
                if (reportType.equals("HTML")) {
                    response.setContentType("text/html");
                    response.setCharacterEncoding("UTF-8");
                    final HtmlExporter exporter = new HtmlExporter();
                    exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
                    final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    final SimpleHtmlExporterOutput output = new SimpleHtmlExporterOutput(byteArrayOutputStream);	
                    output.setImageHandler(new WebHtmlResourceHandler("../jasper-servlets/image?image={0}&uuid=" + UUID.randomUUID().toString()));
                    exporter.setExporterOutput(output);
                    exporter.exportReport();
                    exporter.reset();
                    
                    byte[] bytes = byteArrayOutputStream.toByteArray();
                    logger.debug("bytes.length: {}",bytes.length);
                    if(bytes.length < 1001) {
                        return false;
                    }
                    response.setContentLength(bytes.length);
                    response.resetBuffer();
                    ouputStream.write(bytes,0,bytes.length);
                    ouputStream.flush();//*/
                } else if (reportType.equals("PDF")) {
                    logger.debug("JasperRunManager.runReportToPdf(rutaArchivo, parameters, jasperreport.getConnection()); INIT");
                    byte[] bytes = JasperRunManager.runReportToPdf(rutaArchivo, parameters, getJasperreport().getConnection());
                    logger.debug("bytes.length: {}" , bytes.length);
                    if(bytes.length < 1001) {
                        return false;
                    }
                    logger.debug("JasperRunManager.runReportToPdf(rutaArchivo, parameters, jasperreport.getConnection()); END");
                    /*Indicamos que la respuesta va a ser en formato PDF*/
                    response.setContentType(FileUtils.CONTENT_TYPE_PDF);
                    response.setContentLength(bytes.length);
                    response.resetBuffer();
                    ouputStream.write(bytes, 0, bytes.length);
                    ouputStream.flush();
                } else if (reportType.equals("EXCEL")) {

                    //Exportando el xls
                    JRXlsExporter exporterXLS = new JRXlsExporter();
                    exporterXLS.setParameter(JRXlsExporterParameter.JASPER_PRINT, jasperPrint);
                    exporterXLS.setParameter(JRXlsExporterParameter.IS_DETECT_CELL_TYPE, Boolean.TRUE);
                    exporterXLS.setParameter(JRXlsExporterParameter.IS_WHITE_PAGE_BACKGROUND, Boolean.FALSE);
                    exporterXLS.setParameter(JRXlsExporterParameter.IS_REMOVE_EMPTY_SPACE_BETWEEN_ROWS, Boolean.TRUE);

                    // If you want to export the XLS report to physical file.
                    String filename = "Report_" + getJasperreport().getComboTipoReporteId() + "_" + (new Date()).toString().replaceAll(" ", "_") + ".xls";
                    downloadLink = "excelReports/"+filename;
                    logger.debug("downloadLink: " + downloadLink);
                    reportFile = classPath + "/" + downloadLink;
                    logger.debug("reportFile: " + reportFile);
                    downloadLink = "../" + downloadLink;
                    logger.debug("downloadLink: " + downloadLink);
                    // This'll allows users to directly download the XLS report without
                    // having to save XLS report on server.
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    exporterXLS.setParameter(JRXlsExporterParameter.OUTPUT_STREAM,byteArrayOutputStream);
                    exporterXLS.exportReport();
                    exporterXLS.reset();
                    
                    byte[] bytes = byteArrayOutputStream.toByteArray();
                    logger.debug("bytes.length: {}",bytes.length);
                    if(bytes.length < 1001) {
                        return false;
                    }
                    response.setContentLength(bytes.length);
                    response.setContentType("application/vnd.ms-excel");
                    final ContentDisposition contentHeader = ContentDisposition
                            .builder("attachment")
                            .filename(filename)
                            .build();
                    response.addHeader(HttpHeaders.CONTENT_DISPOSITION, contentHeader.toString());
                    
                    response.resetBuffer();
                    ouputStream.write(bytes,0,bytes.length);
                    ouputStream.flush();
                }
            } catch (Exception e) {
                logger.error("There was an error ",e);
                getJasperreport().closeEmergencia();
            } finally {
                getJasperreport().close();
            }
            if(!response.isCommitted()){
                response.resetBuffer();
            }
            r = true;
        } catch (Exception ex) {
            getJasperreport().closeEmergencia();
            if (response.isCommitted()) {
                r = true;
            } else {
                logger.error("StackTrace: ",ex);
            }
        } finally {
            try {
                destroy();
            } catch (Exception e) {
                logger.error("There was an error ",e);
            }
            getJasperreport().close();
        }
        return r;
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /** 
     * Handles the HTTP <code>GET</code> method.
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /** 
     * Handles the HTTP <code>POST</code> method.
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /** 
     * Returns a short description of the servlet.
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

    /**
     * @return the reportType
     */
    public String getReportType() {
        return reportType;
    }

    /**
     * @param reportType the reportType to set
     */
    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    /**
     * @return the reportFile
     */
    public String getReportFile() {
        return reportFile;
    }

    /**
     * @param reportFile the reportFile to set
     */
    public void setReportFile(String reportFile) {
        this.reportFile = reportFile;
    }

    /**
     * @return the downloadLink
     */
    public String getDownloadLink() {
        return downloadLink;
    }

    /**
     * @param downloadLink the downloadLink to set
     */
    public void setDownloadLink(String downloadLink) {
        this.downloadLink = downloadLink;
    }

    /**
     * @return the jasperreport
     */
    public jasperreport getJasperreport() {
        return jasperreport;
    }

    /**
     * @param jasperreport the jasperreport to set
     */
    public void setJasperreport(jasperreport jasperreport) {
        this.jasperreport = jasperreport;
    }
}
