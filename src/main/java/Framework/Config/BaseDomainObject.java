package Framework.Config;

import Framework.DAO.IUntypedDAO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;
import javax.persistence.Transient;
import mx.bnext.core.util.Loggable;
import org.apache.commons.beanutils.PropertyUtils;
import org.hibernate.HibernateException;
import org.slf4j.Logger;

/**
 *
 * <AUTHOR>   <PERSON>
 * @param <EntityIdentifierType>
 * @since   :   2.3.2.58
 */
public abstract class BaseDomainObject<EntityIdentifierType extends Serializable> implements Serializable {

    
    protected static final String DOMAIN_OBJECT_WHERE_CLAUSE = "c.id = :id";
    protected static final String NONE_UPDATE_WHERE_CLAUSE = "1=0";
    private transient Logger logger = null;
    
    @Transient
    private transient boolean localized = false;

    public BaseDomainObject() {}
    
    protected final Logger getLogger() {
        if(logger == null) {
            logger = Loggable.getLogger(this.getClass());
        }
        return logger;
    }
    
    public abstract EntityIdentifierType identifuerValue();
    
    protected String updateWhereClause() {
        return DOMAIN_OBJECT_WHERE_CLAUSE;
    }
    protected Map<String,Object> updateWhereClauseParameters() {
        Map<String,Object> o = new HashMap<>();
        o.put("id", identifuerValue());
        return o;
    }
    
    /**
     *
     * Hace update (HQL) de la misma entidad extendida
     *
     * @param key   : el nombre del campo (get)
     * @param value : el nuevo valor
     * @return      : la cantidad de registros actualizados
     * @since       : 2.3.2.39
     * <AUTHOR> Luis Limas
     * @deprecated  Pasar el dao
     */
    @Deprecated
    public final int update(String key, Object value) {
        Map<String, Object> u = new HashMap<>();
        u.put(key, value);
        return update(u);
    }
    
    /**
     *
     * Hace update (HQL) de la misma entidad extendida
     *
     * @param key   : el nombre del campo (get)
     * @param value : el nuevo valor
     * @param dao
     * @return      : la cantidad de registros actualizados
     * @since       : 2.3.2.39
     * <AUTHOR> Luis Limas
     *
     */
    public final int update(String key, Object value, IUntypedDAO dao) {
        Map<String, Object> u = new HashMap<>();
        u.put(key, value);
        return update(u, dao);
    }

    /**
     * Hace update (HQL) de la misma entidad extendida
     *
     * @param up : Mapa de campos campo-valor
     * @return : la cantidad de registros actualizados
     * @since : 2.3.2.39
     * <AUTHOR> Luis Limas
     * @deprecated  Pasar el dao
     */
    @Deprecated
    public final int update(Map<String, Object> up) {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        return update(up, dao);
    }
    
    /**
     * Hace update (HQL) de la misma entidad extendida
     *
     * @param up    : Mapa de campos campo-valor
     * @param dao
     * @return      : la cantidad de registros actualizados
     * @since       : 2.3.2.39
     * <AUTHOR> Luis Limas
     */
    public final int update(Map<String, Object> up, IUntypedDAO dao) {
        up.remove("id");
        if (identifuerValue() == null || identifuerValue().equals(-1L) || up.isEmpty()) {
            getLogger().debug(">> Debe guardar el objeto antes de actualizarlo, el ID no es valido : {}", identifuerValue());
            return 0;
        }
        int r = 0;
        try {
            StringBuilder query = new StringBuilder("UPDATE  " + this.getClass().getName() + " c SET ");
            boolean _1sth = true;
            for (Map.Entry<String, Object> entry : up.entrySet()) {
                String string = entry.getKey();
                query.append(_1sth ? "" : ", ").append(" ").append(string).append(" = :").append(string);
                _1sth = false;
            }
            if (this instanceof CompositeStandardEntity
                    && (
                    updateWhereClause().equals(NONE_UPDATE_WHERE_CLAUSE)
            )
            ) {
                getLogger().error("Error! se esta intentando hacer un UPDATE de un Entity compuesto");
                return 0;
            }
            getLogger().debug("HQL: {} WHERE {}", query, updateWhereClause());
            Map<String, Object> params = new HashMap<>();
            params.putAll(up);
            params.putAll(updateWhereClauseParameters());
            r = dao.HQL_updateByQuery(query + " WHERE " + updateWhereClause(), params);
            if (r > 0) {
                for (Map.Entry<String, Object> entry : up.entrySet()) {
                    PropertyUtils.setProperty(this, entry.getKey(), entry.getValue());
                }
            }
        } catch (HibernateException
                 | IllegalAccessException
                 | NoSuchMethodException
                 | InvocationTargetException e) {
            getLogger().error("Stack trace: ", e);
            return 0;
        }
        return r;
    }
    
    @Transient
    @JsonIgnore
    public boolean isLocalized() {
        return localized;
    }

    public void setLocalized(boolean localized) {
        this.localized = localized;
    }
    
}
