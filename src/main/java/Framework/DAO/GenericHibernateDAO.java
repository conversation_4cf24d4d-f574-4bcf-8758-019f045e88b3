package Framework.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.Persistable;
import Framework.Config.ITextHasValue;
import Framework.Config.TextCodeValue;
import Framework.Config.Utilities;
import ape.pending.core.BaseAPE;
import ape.pending.core.IPendingOperation;
import ape.pending.dto.ColumnDTO;
import ape.pending.entities.PendingRecord;
import ape.pending.util.RecordRowsType;
import bnext.exception.MakePersistentException;
import isoblock.common.beanGeneric;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.servlet.ServletContext;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import mx.bnext.core.util.Loggable;
import org.hibernate.Session;
import org.hibernate.criterion.Criterion;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.ServletContextAware;
import org.springframework.web.context.support.WebApplicationContextUtils;
import qms.access.dto.ILoggedUser;
import qms.framework.core.IPersistenceManager;
import qms.framework.dao.bean.daoFactory;
import qms.framework.util.CacheRegion;
import qms.util.QMSException;
import qms.util.ReflectionUtil;
import qms.util.interfaces.IGridFilter;
import qms.util.interfaces.ILinkedCompositeEntity;
import qms.util.interfaces.IPagedQuery;
import qms.util.interfaces.IPersistableDescription;

/**
 * @param <T>
 * @param <ID>
 * <AUTHOR> Networks
 * <p>
 * DAO principal que contiene todos los metodos requeridos para buscar y actualizar datos en la base de datos
 * las nomenclaturas generales son las sigueintes
 * <p>
 * HQL_* .- Se refieren a aquellos que ejecutan codigo HQL y regresan tipos de datos especificos (Object, Integer, String, etc)
 * SQL_* .- Se refieren a aquellos que ejecutan codigo SQL y regresan tipos de datos especificos (Object, Integer, String, etc)
 * HQLT_*.- Se refieren a metodos HQL que regresan SOLO objectos del Entity especificado en la declaracion del DAO (User, Document, Process, etc)
 * @deprecated Utilizar DAOs que extienden de Framework.DAO.GenericDAOImpl
 */
@SuppressWarnings({"unchecked", "rawtypes"})
public class GenericHibernateDAO<T, ID extends Serializable> extends Loggable implements ServletContextAware, ApplicationContextAware, ICRUD_GenericDAO<T> {

    private IGenericDAO<T, ID> springBeanDao = null;
    private ServletContext servletContext = null;
    public static boolean IS_SQL_SERVER = false;
    private Class<T> persistentClass;
    ApplicationContext beanFactory;

    /**
     * Metodo para crear una instancia de la interfaz IGenericDAO y accesar a las implementaciones
     * de los metodos.
     *
     * @return IGenericDAO con los metodos implementados
     */
    public final IGenericDAO<T, ID> getSpringBeanDao() {
        if (this.springBeanDao == null) {
            daoFactory factory = (daoFactory)
                    getApplicationContext().getBean("daoFactory");
            this.springBeanDao = factory.getSpringBeanDao();
            this.springBeanDao.setPersistentClass(getPersistentClass());
        }
        return springBeanDao;
    }

    /**
     *
     */
    public void setSpringBeanDao(IGenericDAO springBeanDao) {
        this.springBeanDao = springBeanDao;
    }

    private ApplicationContext getApplicationContext() {
        if (this.beanFactory == null) {
            if (getServletContext() == null) {
                //patch for legacy compatibility (jsp:useBean & beans created outside servlet context)
                this.beanFactory = Utilities.getApplicationContext();
            } else {
                //going here is the normal/correct behavior
                this.beanFactory = WebApplicationContextUtils.getRequiredWebApplicationContext(getServletContext());
            }
        }
        return beanFactory;
    }

    @Override
    public ServletContext getServletContext() {
        if (this.servletContext == null) {
            try {
                this.servletContext = Utilities.getServletContext();
            } catch (Exception e) {
                getLogger().trace("getServletContext(), returns null, is it ok? {}", e.getLocalizedMessage());
                this.servletContext = null;
                return null;
            }
        }
        return this.servletContext;
    }


    @Override
    public final void setServletContext(@Nonnull ServletContext servletContext) {
        this.servletContext = servletContext;
    }

    /**
     * Este metodo debe ser @Overriden para cada entity (Hibernate_DAO) con los filtros correspondientes
     */
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        filter.getCriteria().put("<filtered-entity>", "1=1");
    }

    /**
     * Utilizado para obtener un listado de Entities que cumplen con un filtro "String"
     * especifico, el Entity tiene como alias "c"
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}.HQLT_findByQueryFilter("c.cuenta = 'admin'");
     *
     * @param filtros {String}: es el filtro que va despues del WHERE en un query HQL
     * @return listado de entities
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    public List<T> HQLT_findByQueryFilter(String filtros) {
        return HQLT_findByQueryFilter_NEntityFilter(filtros, null);
    }

    /**
     * Utilizado para obtener un listado de Entities que cumplen con un filtro "String"
     * especifico, el Entity tiene como alias "c"
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findByQueryFilter(
     * "c.code like '%0001'",
     * new String[]{"not_children"}
     * );
     *
     * @param filtro  {String}    : es el filtro que va despues del WHERE en un query HQL
     * @param filtros {String[]} : es el filtro que va despues del WHERE en un query HQL
     * @return : listado de entities
     * <AUTHOR> Llimas
     * @see GenericDAOImpl#HQLT_findByQueryFilter_NEntityFilter(java.lang.String, java.lang.String[])
     * @since : *********
     */
    public List<T> HQLT_findByQueryFilter_NEntityFilter(String filtro, String[] filtros) {
        return getSpringBeanDao().HQLT_findByQueryFilter_NEntityFilter(filtro, filtros);
    }

    /**
     * Constructor
     */
    public GenericHibernateDAO() {
        try {
            this.persistentClass = (Class<T>) ReflectionUtil.getParametrizedType(getClass());
        } catch (Exception e) {
            getLogger().error("There was an error", e);
        }
    }

    /**
     * Constructor
     *
     * @param t clase con la que se inicializará el constructor
     */
    public GenericHibernateDAO(Class t) {
        //this.setSession((Session) s); //<--- session is allways the same
        getSpringBeanDao().setPersistentClass(t);
    }

    /**
     * Constructor
     */
    public GenericHibernateDAO(HashMap<String, Object> shareableDaoSessionData) {
        if (shareableDaoSessionData != null) {
            shareableDaoSessionData.get("obj_session");
            shareableDaoSessionData.get("obj_transaction");
        }
        try {
            this.persistentClass = (Class<T>) ((ParameterizedType) getClass()
                    .getGenericSuperclass()).getActualTypeArguments()[0];
        } catch (Exception e) {
            getLogger().error("There was an error", e);
        }
    }

    private void stackTraceHandle(Exception e) {
        getLogger().error("Stack trace: ", e);
    }

    @Override
    public Class<T> getPersistentClass() {
        return persistentClass;
    }

    @Override
    public void setPersistentClass(Class<T> t) {
        persistentClass = t;
    }

    /**
     * Metodo para eliminar fisicamente un objeto de la base de datos
     *
     * @param entity objeto a eliminar de la base de datos
     * @return Boolean true/false para saber si la operación se completo correctamente
     * <AUTHOR> Staff
     * @see GenericDAOImpl#deleteEntity(java.lang.Object)
     */
    @Override
    public boolean deleteEntity(Object entity) {
        return getSpringBeanDao().deleteEntity(entity);
    }


    @Override
    public GridInfo<T> getRows(IGridFilter filter) {
        return getSpringBeanDao().getRows(filter);
    }

    @Override
    public GridInfo<T> getRows(
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getSpringBeanDao().getRows(filter, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    /**
     * Utilizado para llenar datos de gridComponent.js desde algun CRUD (CRUD_Generic.java)
     *
     * @param filter : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : informacion requerida por gridComponent.js para funcionar
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    public <TYPE> GridInfo<TYPE> getRows(Class<TYPE> TYPE, IGridFilter filter) {
        return getSpringBeanDao().getRows(TYPE, filter);
    }

    @Override
    public <TYPE> GridInfo<TYPE> getRows(
            Class<TYPE> TYPE,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            IGridFilter filter
    ) {
        return getSpringBeanDao().getRows(TYPE, cacheable, cacheRegion, queryTimeoutSeconds, filter);
    }

    /**
     * Utilizado para obtener un Entity por su ID en especifico
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findById(
     * 1L,
     * false
     * );
     *
     * @param id   : Id del entity, generalmente un Long
     * @param lock : bandera para trabar la tabla
     * @return entity Typed con los datos obtenidos en base al id del entity
     * <AUTHOR> BnextDPMS_Staff
     * @see GenericDAOImpl#HQLT_findById(java.io.Serializable, boolean)
     * @since : *********
     */
    public T HQLT_findById(ID id, boolean lock) {
        return getSpringBeanDao().HQLT_findById(id, lock);
    }

    /**
     * Utilizado para obtener solo un Object en especifico por un query HQL
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQL_findSimpleObject(
     * "SELECT c FROM DPMS.Mapping.Node c WHERE c.code like '%0001'"
     * );
     *
     * @param HQL : el query del cual se espera obtener un dato
     * @return Object
     * <AUTHOR> Llimas
     * @see GenericHibernateDAO#HQL_findSimpleObject(java.lang.String, java.util.Map)
     * @since : *********
     */
    @Nullable
    @Override
    public Object HQL_findSimpleObject(String HQL) {
        return HQL_findSimpleObject(HQL, Utilities.EMPTY_MAP, false, null, 0);
    }

    /**
     * Utilizado para obtener solo un Object en especifico por un query HQL
     * filtrando por un parametro
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQL_findSimpleObject(
     * "SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * "code",
     * "0001"
     * );
     *
     * @param HQL       : query HQL
     * @param paramName : el nombre de un paramtro contenido en el HQL
     * @param value     : el valor del parametro
     * @return Object
     * <AUTHOR> Llimas
     * @see GenericHibernateDAO#HQL_findSimpleObject(java.lang.String, java.util.Map)
     * @since : *********
     */
    @Nullable
    @Override
    public Object HQL_findSimpleObject(String HQL, String paramName, Object value) {
        return HQL_findSimpleObject(HQL, paramName, value, false, null);
    }

    @Nullable
    @Override
    public Object HQL_findSimpleObject(
            String HQL,
            String paramName,
            Object value,
            Boolean cacheable,
            CacheRegion cacheRegion
    ) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_findSimpleObject(HQL, params, cacheable, cacheRegion, 0);
    }

    /**
     * Utilizado para obtener solo un Object en especifico por un query HQL
     * filtrando por varios parametros
     * <p>
     * Ejemplo :
     * HashMap<String,Object> mapa = new HashMap<String,Object>();
     * mapa.put("code","0001");
     * new GenericHibernateDAO<Node,Long>(){}.HQL_findSimpleObject(
     * "SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * map
     * );
     *
     * @param HQL    : query HQL
     * @param params : Grupo de parametros
     * @return Object
     * <AUTHOR> Llimas
     * @see GenericDAOImpl#HQL_findSimpleObject(java.lang.String, java.util.Map)
     * @since : *********
     */
    @Nullable
    @Override
    public Object HQL_findSimpleObject(String HQL, Map params) {
        return getSpringBeanDao().HQL_findSimpleObject(HQL, params);
    }

    @Nullable
    @Override
    public Object HQL_findSimpleObject(
            String HQL,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getSpringBeanDao().HQL_findSimpleObject(HQL, params, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    /**
     * Utilizado para obtener un Entity por su ID en especifico
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findById(
     * 1L
     * );
     *
     * @param id : Id del entity, generalmente un Long
     * @return entity Typed con los datos obtenidos en base al id del entity
     * <AUTHOR> BnextDPMS_Staff
     * @see GenericHibernateDAO#HQLT_findById(java.io.Serializable, boolean)
     * @since : *********
     */
    public T HQLT_findById(ID id) {
        return this.HQLT_findById(id, false);
    }

    /**
     * @param criterion criterios para el filtro de datos
     * @return Typed List con los datos obtenidos deacuerdo a los filtros especificados
     * <AUTHOR> Staff
     * @see GenericDAOImpl#HQLT_findByCriteria(org.hibernate.criterion.Criterion...)
     */
    public List<T> HQLT_findByCriteria(Criterion... criterion) {
        return getSpringBeanDao().HQLT_findByCriteria(criterion);
    }


    /**
     * Utilizado para obtener un listado de Entities
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.HQLT_findAll();
     *
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    public List<T> HQLT_findAll() {
        return HQLT_findByCriteria();
    }

    /**
     * Busquedas de datos del Entity especificado en la declaracion del DAO,
     * utilizado en gridComponent.js
     *
     * @param filter : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : listado de entities
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Deprecated
    public List<T> HQLT_findByPagedFilter(IGridFilter filter) {
        return HQLT_findByPagedFilter(filter, false);
    }

    @Override
    public <TYPE> List<TYPE> HQLT_findByPagedFilter(Class<TYPE> cls, IGridFilter filter) {
        return getSpringBeanDao().HQLT_findByPagedFilter(cls, filter);
    }

    /**
     * Cantidad de datos del Entity especificado en la declaracion del DAO,
     * utilizado en gridComponent.js
     *
     * @param filter : Utilizado para cambiar datos del WHERE en un query HQL, datos por pagina, etc.
     * @return : cantidad de renglondes
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    public Long HQLT_countByPagedFilter(IGridFilter filter) {
        return HQLT_countByPagedFilter(filter, false);
    }

    /**
     * Busquedas de datos del Entity especificado en la declaracion del DAO,
     * utilizado para busquedas por query directo
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}.HQLT_findByQuery("SELECT c FROM DPMS.Mapping.Users c");
     *
     * @param query : El query, debe regressar entities tipo "T"
     * @return : listado de entities
     * <AUTHOR> BnextDPMS_Staff
     * @see GenericDAOImpl#HQLT_findByQuery(java.lang.String)
     * @since : *********
     */
    public List<T> HQLT_findByQuery(String query) {
        return getSpringBeanDao().HQLT_findByQuery(query);
    }

    /**
     * Busquedas de datos
     * utilizado para busquedas por query directo
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}.HQLT_findByQuery("SELECT c FROM DPMS.Mapping.Node c");
     *
     * @param HQL : El query HQL, puede utilizarse para cualquier tipo de dato
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    public List HQL_findByQuery(String HQL) {
        return this.getSpringBeanDao().HQL_findByQuery(HQL);
    }

    /**
     * Busquedas de datos
     * utilizado para busquedas por query directo con un solo filtro
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQLT_findByQuery("SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * "code",
     * "0001"
     * );
     *
     * @param query     : El query HQL, puede utilizarse para cualquier tipo de dato
     * @param paramName : El nombre parametro
     * @param value     : El valor del parametro a sustituir
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Override
    public List HQL_findByQuery(String query, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_findByQuery(query, params);
    }

    @Nonnull
    @Override
    public List HQL_findByQuery(
            final String hql,
            final Map<String, Object> params,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        return getSpringBeanDao().HQL_findByQuery(hql, params, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    /**
     * Busquedas de datos
     * utilizado para busquedas por query directo con un mapa de filtros
     * <p>
     * Ejemplo :
     * HashMap<String,Object> map = new HashMap<String,Object>();
     * map.put("code","001");
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQLT_findByQuery(
     * "SELECT c FROM DPMS.Mapping.Node c WHERE c.code = :code",
     * map
     * );
     *
     * @param query  : El query HQL, puede utilizarse para cualquier tipo de dato
     * @param params : Map con todos los paramtros incluidos
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @see GenericDAOImpl#HQL_findByQuery(java.lang.String, java.util.Map)
     * @since : *********
     */
    @Override
    public List HQL_findByQuery(String query, Map params) {
        return getSpringBeanDao().HQL_findByQuery(query, params);
    }

    /**
     * Utilizado para obtener solo un String en especifico por un query SQL
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.SQL_findSimpleString(
     * "SELECT c.code FROM tblnodo c WHERE c.code = '0001'"
     * );
     *
     * @param SQL : query SQL
     * <AUTHOR> Llimas
     * @see GenericDAOImpl#SQL_findSimpleString(java.lang.String)
     * @since : *********
     */
    @Nonnull
    @Override
    public String SQL_findSimpleString(String SQL) {
        return getSpringBeanDao().SQL_findSimpleString(SQL);
    }

    @Nonnull
    @Override
    public String SQL_findSimpleString(String SQL, Map params) {
        return getSpringBeanDao().SQL_findSimpleString(SQL, params);
    }

    @Nonnull
    @Override
    public String SQL_findSimpleString(String SQL, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public String SQL_findSimpleString(String SQL, Map params, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }


    /**
     * Utilizado para listado de renglones por un query SQL
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Node,Long>(){}.SQL_findByQuery(
     * "SELECT c.code FROM tblnodo c WHERE c.code = '0001'"
     * );
     *
     * @param SQL : query SQL
     * <AUTHOR> Llimas
     * @see GenericDAOImpl#SQL_findByQuery(java.lang.String)
     * @since : *********
     */
    @Override
    public List SQL_findByQuery(String SQL) {
        return getSpringBeanDao().SQL_findByQuery(SQL);
    }

    @Override
    public <X> X SQL_findUniqueResult(String SQL, Class<X> cls) {
        return getSpringBeanDao().SQL_findUniqueResult(SQL, cls);
    }

    /**
     * Utilizar HQLT_findByQueryFilter... y eliminar todos sus usos
     * <p>
     * Esta funcion es suceptible a fallas debido a que el entity utilizado en el "Example"
     * puede ser modificado involuntariamente por procesos desconocidos al desarrollador...
     * ...
     * Ya se han realizado correcciones relacionadas, por lo tanto es mejor utilizar directamente
     * el filtro que requerimos usando "HQLT_findByQueryFilter"
     *
     * @see GenericDAOImpl#findByExample(java.lang.Object, java.lang.String...)
     */
    @Deprecated
    public List<T> findByExample(T exampleInstance, String... excludeProperty) {
        return getSpringBeanDao().findByExample(exampleInstance, excludeProperty);
    }

    /**
     * Actualizacion de datos por query HQL, UPDATE, DELETE, INSERT
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQL_updateByQuery(
     * "DELETE FROM DPMS.Mapping.Node c WHERE c.code like 'A%'"
     * );
     *
     * @param HQL : El query HQL
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Nonnull
    @Override
    public Integer HQL_updateByQuery(String HQL) {
        return this.getSpringBeanDao().HQL_updateByQuery(HQL);
    }

    /**
     * Actualizacion de datos por query HQL, UPDATE, DELETE, INSERT
     * con un solo parametro
     * <p>
     * Ejemplo :
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQL_updateByQuery(
     * "DELETE FROM DPMS.Mapping.Node c WHERE c.code like :code "
     * ,"code","0001"
     * );
     *
     * @param HQL        : El query HQL
     * @param paramName: parametro
     * @param value      : valor del parametro
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @since : *********
     */
    @Nonnull
    @Override
    public Integer HQL_updateByQuery(String HQL, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return this.HQL_updateByQuery(HQL, params);
    }

    @Override
    public List<ITextHasValue> getActives(Class<? extends IPersistableDescription> cls) {
        return getSpringBeanDao().getActives(cls);
    }

    @Override
    public List<ITextHasValue> getActives() {
        List<ITextHasValue> list;

        String activeFilter = "c." + this.getStatusField();
        String deletedFilter = "1=1";//"c." + this.getDeletedField();
        String filter = activeFilter + " AND " + deletedFilter;
        String HQL = "SELECT new Framework.Config.TextHasValue(c.description,c.id) FROM "
                + this.getPersistentClass().getName()
                + " c WHERE " + filter;
        list = (List<ITextHasValue>) this.HQLT_findByQuery(HQL);
        return list;
    }

    /**
     * Actualizacion de datos por query HQL, UPDATE, DELETE, INSERT
     * con varios parametros
     * <p>
     * Ejemplo :
     * HashMap<String, Object> map = new HashMap<String, Object>();
     * map.put("code","001");
     * new GenericHibernateDAO<Users,Long>(){}
     * .HQL_updateByQuery(
     * "DELETE FROM DPMS.Mapping.Node c WHERE c.code like :code "
     * ,map
     * );
     *
     * @param HQL : El query HQL
     * @param m   : parametros
     * @return : renglones
     * <AUTHOR> BnextDPMS_Staff
     * @see GenericDAOImpl#HQL_updateByQuery(java.lang.String, java.util.Map)
     * @since : *********
     */
    @Nonnull
    @Override
    public Integer HQL_updateByQuery(String HQL, Map m) {
        return getSpringBeanDao().HQL_updateByQuery(HQL, m);
    }

    @Nonnull
    @Override
    public Integer HQL_updateByQuery(
            String HQL,
            Map m,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getSpringBeanDao().HQL_updateByQuery(HQL, m, cacheable, cacheRegion, queryTimeoutSeconds);
    }


    public Long getRowCount(IGridFilter filter) {
        return HQLT_countByPagedFilter(filter);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList() {
        return getStrutsComboList("description", "0=0", -1L, true);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(Boolean withStatus) {
        return getStrutsComboList("description", "0=0", -1L, withStatus);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(Long id) {
        return getStrutsComboList("description", "0=0", id, true);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(String filtros) {
        return getStrutsComboList("description", filtros, -1L, true);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(String filtros, Boolean withStatus) {
        return getStrutsComboList("description", filtros, -1L, withStatus);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(String description, String filtros) {
        return getStrutsComboList(description, filtros, -1L, true);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(String filtros, Long id) {
        return getStrutsComboList("description", filtros, id, true);
    }

    /**
     * Regresa una lista de objetos para usarse en struts directamente
     *
     * @param mappedDescription texto que se va a mostrar
     * @param filtros           filtros en hql
     * @param id                para edicion
     */
    @Override
    public List<ITextHasValue> getStrutsComboList(String mappedDescription, String filtros, Long id, Boolean withStatus) {
        return getStrutsComboList(null, mappedDescription, filtros, id, withStatus);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription, String filtros) {
        return getStrutsComboList(clazz, mappedDescription, filtros, -1L, true);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getStrutsComboList(clazz, mappedDescription, filtros, -1L, true);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(Class clazz, String mappedDescription) {
        return getStrutsComboList(clazz, mappedDescription, "1=1", -1L, true);
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(
            Class clazz,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getSpringBeanDao().getStrutsComboList(clazz, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    /**
     * Metodo que obtiene una lista de tipo TextHasValue para utilizar en un combo
     *
     * @param clazz             clase de la cual se obtendrán los datos
     * @param mappedDescription valor que aparecerá en el combo
     * @param filtros           filtros para los datos
     * @param id                valor especifico para mostrar
     * @return List<ITextHasValue> con los datos para ser utilizados en un combo
     * <AUTHOR> Staff
     * @see GenericDAOImpl#getStrutsComboList(java.lang.Class, java.lang.String, java.lang.String, java.lang.Long, java.lang.Boolean)
     * @since 2.3.2.XXX
     */
    @Override
    public List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            Long id,
            Boolean withStatus
    ) {
        return getSpringBeanDao().getStrutsComboList(clazz, mappedDescription, filtros, id, withStatus);
    }

    @Override
    public <TEXT extends ITextHasValue> List<TEXT> getStrutsComboList(
            final Class<TEXT> typeList,
            final Class typedClazz,
            final String mappedColumns,
            final String filtros,
            final List<Long> ids,
            final Boolean withStatus,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<TextCodeValue> getStrutsTextCodeComboList(
            final Class clazz,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public List<TextCodeValue> getStrutsTextCodeComboList(
            final Class clazz,
            final String filtros,
            final List<Long> ids,
            final Boolean withStatus,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet.");
    }


    @Nonnull
    @Override
    public Long HQL_findSimpleLong(String HQL) {
        return HQL_findSimpleLong(HQL, null);
    }

    @Nonnull
    @Override
    public Long HQL_findSimpleLong(String HQL, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_findSimpleLong(HQL, params);
    }

    @Nonnull
    @Override
    public Long HQL_findSimpleLong(String HQL, Map params) {
        return HQL_findSimpleLong(HQL, params, false, null, 0);
    }

    @Nonnull
    @Override
    public Long HQL_findSimpleLong(
            String HQL,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        long amt = 0L;
        String temp;
        try {
            temp = this.HQL_findSimpleString(HQL, params, cacheable, cacheRegion, queryTimeoutSeconds);
            amt = Long.parseLong(temp.isEmpty() ? "0" : temp);
        } catch (Exception e) {
            System.err.println("ERROR: " + HQL);
            stackTraceHandle(e);
        }
        return amt;
    }

    @Nonnull
    @Override
    public Date HQL_findSimpleDate(String HQL) {
        return HQL_findSimpleDate(HQL, null);
    }

    @Nonnull
    @Override
    public Date HQL_findSimpleDate(String HQL, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_findSimpleDate(HQL, params);
    }

    @Nonnull
    @Override
    public Date HQL_findSimpleDate(String HQL, Map params) {
        Date amt = new Date();
        try {
            amt = (Date) (this.HQL_findSimpleObject(HQL, params));
        } catch (Exception e) {
            System.err.println("ERROR: " + HQL);
            stackTraceHandle(e);
        }
        return amt;
    }

    @Nonnull
    @Override
    public Integer HQL_findSimpleInteger(String HQL) {
        return HQL_findSimpleInteger(HQL, null);
    }

    @Nonnull
    @Override
    public Integer HQL_findSimpleInteger(String HQL, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_findSimpleInteger(HQL, params);
    }

    @Nonnull
    @Override
    public Integer HQL_findSimpleInteger(String HQL, Map params) {
        return HQL_findSimpleInteger(HQL, params, false, null, 0);
    }

    @Nonnull
    @Override
    public Integer HQL_findSimpleInteger(
            String HQL,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        int amt = 0;
        try {
            amt = Integer.parseInt(String.valueOf(this.HQL_findSimpleObject(HQL, params, cacheable, cacheRegion, queryTimeoutSeconds)));
        } catch (NumberFormatException e) {
            System.err.println("ERROR: " + HQL);
            stackTraceHandle(e);
        }
        return amt;
    }

    @Nonnull
    @Override
    public String HQL_findSimpleString(String HQL) {
        return HQL_findSimpleString(HQL, null);
    }

    @Nonnull
    @Override
    public String HQL_findSimpleString(String HQL, String paramName, Object value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(paramName, value);
        return HQL_findSimpleString(HQL, params);
    }

    @Nonnull
    @Override
    public String HQL_findSimpleString(String HQL, Map params) {
        return HQL_findSimpleString(HQL, params, false, null, 0);
    }

    @Nonnull
    @Override
    public String HQL_findSimpleString(
            String HQL,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        String amt = "";
        try {
            amt = String.valueOf(this.HQL_findSimpleObject(HQL, params));
        } catch (Exception e) {
            System.err.println("ERROR: " + HQL);
            stackTraceHandle(e);
        }
        return amt;
    }

    @Override
    public Long HQLT_countByPagedFilter(IGridFilter filter, boolean like) {
        return getSpringBeanDao().HQLT_countByPagedFilter(filter, like);
    }

    @Override
    public GridInfo HQL_getRowsByQuery(String query, IGridFilter filter) {
        return HQL_getRowsByQuery(query, filter, false);
    }

    @Override
    public GridInfo HQL_getRowsByQuery(
            String HQL,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getRowsByQuery(String query, IGridFilter filter, boolean like) {
        return getSpringBeanDao().HQL_getRowsByQuery(query, filter, like);
    }

    @Deprecated
    public List<T> HQLT_findByPagedFilter(IGridFilter filter, boolean like) {
        return getSpringBeanDao().HQLT_findByPagedFilter(filter, like);
    }

    /**
     * @see GenericDAOImpl#refresh(java.lang.Object)
     */
    @Override
    public void refresh(Object entity) {
        getSpringBeanDao().refresh(entity);
    }

    /**
     * Implementacion del metodo para guardar/actualizar un entity
     *
     * @param <TYPE> generico
     * @param entity objeto generico
     * @return objeto generico
     * <AUTHOR> Staff
     * @see Framework.DAO.GenericDAOImpl#makePersistent(java.lang.Object)
     */
    @Override
    public <TYPE> TYPE makePersistent(TYPE entity) {
        return getSpringBeanDao().makePersistent(entity);
    }

    /**
     * Implementacion del metodo para guardar/actualizar un entity
     *
     * @param <TYPE> generico
     * @param entity objeto generico
     * @return objeto generico
     * <AUTHOR> Staff
     * @see Framework.DAO.GenericDAOImpl#makePersistent(java.lang.Object)
     */
    @Override
    public <TYPE> TYPE makePersistent(TYPE entity, Long loggedUserId) {
        return getSpringBeanDao().makePersistent(entity, loggedUserId);
    }

    @Override
    public <TYPE, TYPE_ID> TYPE_ID getId(TYPE instancia) {
        try {
            if (instancia instanceof Persistable) {
                return (TYPE_ID) ((Persistable) instancia).getId();
            } else if (instancia instanceof ILinkedCompositeEntity) {
                return (TYPE_ID) ((ILinkedCompositeEntity) instancia).getId();
            }
            Object r = beanGeneric.callMethodLong(instancia, "getId");
            if (r == null) {
                return (TYPE_ID) Long.valueOf(-1);
            }
            return (TYPE_ID) r;
        } catch (Exception e) {
            getLogger().warn("no existe el id");
            return (TYPE_ID) Long.valueOf(-1);
        }
    }

    @Override
    public String getDescription(T instancia) {
        try {
            return beanGeneric.callMethodString(instancia, "getDescription");
        } catch (Exception e) {
            getLogger().warn("no hay descripción");
            return "NA";
        }
    }

    /**
     * <AUTHOR> Staff
     * @see GenericDAOImpl#makeTransient(java.lang.Object)
     */
    @Override
    public <TYPE> void makeTransient(TYPE entity) {
        getSpringBeanDao().makeTransient(entity);
    }

    public void flush() {
        //empty!
        //spring automatically manages flushing!
    }

    public void clear() {
        //empty!
        //spring automatically manages clearness!
    }

    @SuppressWarnings({"PointlessBooleanExpression", "CStyleArrayDeclaration", "ConstantValue"})
    public String getStatusField() {
        String result = "";
        try {
            Class cls = getPersistentClass();
            String activeStatus = "1";
            Field fieldlist[] = cls.getDeclaredFields();
            int contador = 0;
            boolean encontrado = false;
            Field fld = null;
            while (contador < fieldlist.length && encontrado == false) {
                fld = fieldlist[contador];
                if (fld.getName().equals("ACTIVE_STATUS")) {
                    activeStatus = fld.get(null) + " ";
                }
                if (fld.getName().equals("status")
                        || fld.getName().equals("estatus")
                        || fld.getName().equals("intestado")
                        || fld.getName().equals("intstatususuario")) {
                    encontrado = true;
                    break;
                }
                contador++;
            }
            if (encontrado) {
                result = fld.getName() + " = " + activeStatus;
            }
        } catch (IllegalAccessException | SecurityException | IllegalArgumentException e) {
            getLogger().error(String.valueOf(e));
        }
        return result;/**/
    }

    @Override
    public List HQL_getListResultsByQuery(
            String query,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return this.getSpringBeanDao().HQL_getListResultsByQuery(query, filter, like, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    public List HQL_getListResultsByQuery(String query, IGridFilter filter) {
        return this.getSpringBeanDao().HQL_getListResultsByQuery(query, filter);

    }

    @Deprecated
    public List getFilteredEntity(boolean isAdmin, IGridFilter filter, String intusuarioid, ProfileServices[] servicio) {
        return getFilteredEntity(isAdmin, filter, intusuarioid, servicio, null);
    }

    /**
     * Activa un filtro en una transaccion
     *
     * @param filterName nombre del filtro de hibernate
     * <AUTHOR> Garza Verastegui
     * @see GenericDAOImpl#enableFilter(java.lang.String)
     * @since 2.3.2.40
     */
    @Override
    public void enableFilter(String filterName) {
        getSpringBeanDao().enableFilter(filterName);
    }

    /**
     * Desactiva un filtro en una transaccion
     *
     * @param filterName nombre del filtro de hibernate
     * <AUTHOR> Garza Verastegui
     * @see GenericDAOImpl#disableFilter(java.lang.String)
     * @since 2.3.2.40
     */
    @Override
    public void disableFilter(String filterName) {
        this.getSpringBeanDao().disableFilter(filterName);
    }

    @Deprecated
    public List<T> getFilteredEntity(boolean isAdmin, IGridFilter filter, String intusuarioid, ProfileServices[] servicio, String order) {
        return getSpringBeanDao().getFilteredEntity(isAdmin, filter, intusuarioid, servicio, order);
    }

    @Override
    public void setApplicationContext(@Nonnull ApplicationContext ac) throws BeansException {
        this.beanFactory = ac;
    }

    @Override
    public EntityManager getEntityManager() {
        throw new UnsupportedOperationException("Not meant to be used"); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Session getSession() {
        throw new UnsupportedOperationException("Not meant to be used"); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GenericSaveHandle save(T entity) {
        throw new UnsupportedOperationException("Not supported yet. this method is meant to be overridden");
    }

    @Override
    public GenericSaveHandle successGSH(boolean nuevo, Object id) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (id != null) {
            gsh.setSavedId(id.toString());
        }
        gsh.setOperationEstatus(1);
        gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
        return gsh;
    }

    @Override
    public GenericSaveHandle successGSH(boolean nuevo) {
        return this.successGSH(nuevo, null);
    }

    @Override
    public GenericSaveHandle successGSH() {
        return this.successGSH(true);
    }

    @Override
    public <TYPE> TYPE HQLT_findById(Class<TYPE> TYPE, Long id) {
        return this.getSpringBeanDao().HQLT_findById(TYPE, id);
    }

    @Override
    public <TYPE> TYPE HQLT_findById(
            Class<TYPE> TYPE,
            Long id,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return this.getSpringBeanDao().HQLT_findById(TYPE, id, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Nonnull
    @Override
    public Map HQL_findSimpleMap(String hql) {
        return HQL_findSimpleMap(hql, null);
    }

    @Nonnull
    @Override
    public Map HQL_findSimpleMap(String hql, String paramName, Object value) {
        Map m = new HashMap();
        m.put(paramName, value);
        return HQL_findSimpleMap(hql, m);
    }

    @Nonnull
    @Override
    public Map HQL_findSimpleMap(String hql, Map params) {
        return this.getSpringBeanDao().HQL_findSimpleMap(hql, params);
    }

    @Nonnull
    @Override
    public Map HQL_findSimpleMap(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return this.getSpringBeanDao().HQL_findSimpleMap(hql, params, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @SuppressWarnings("ConstantValue")
    @Override
    public String generateCode(String prefix, Long id) {
        Long proxyId = id;
        String codeNum;
        String code;
        Map<String, Object> params = new HashMap<>(1);
        Object retValue;
        String query = "SELECT id FROM " + this.getPersistentClass().getName() + " c WHERE c.code = :code";
        do {
            codeNum = Framework.Config.Utilities.formatConsecutivo(proxyId++);
            code = prefix + "-" + codeNum;
            params.put("code", code);
            retValue = this.HQL_findSimpleObject(query, params);
        } while (retValue != null);
        return code;
    }

    /**
     * @return the IS_SQL_SERVER
     */
    @SuppressWarnings("unused")
    public static boolean isIS_SQL_SERVER() {
        return IS_SQL_SERVER;
    }

    /**
     * @param aIS_SQL_SERVER the IS_SQL_SERVER to set
     */
    public static void setIS_SQL_SERVER(boolean aIS_SQL_SERVER) {
        IS_SQL_SERVER = aIS_SQL_SERVER;
    }

    @Override
    public List HQL_selectQuery(String HQL, Object... params) {
        return this.getSpringBeanDao().HQL_findByQuery(HQL, params);
    }

    @Override
    public List<Map<String, Object>> HQL_selectMapQuery(
            String HQL,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            Object... params
    ) {
        return this.getSpringBeanDao().HQL_selectMapQuery(HQL, cacheable, cacheRegion, queryTimeoutSeconds, params);
    }

    @Nonnull
    @Override
    public Integer HQL_updateQuery(String HQL, Object... params) {
        return this.getSpringBeanDao().HQL_updateByQuery(HQL, params);
    }

    @Override
    public Object HQL_uniqueObject(String HQL, Object[] params) {
        return this.getSpringBeanDao().HQL_uniqueObject(HQL, params);
    }

    @Override
    public Long HQL_uniqueLong(String HQL, Object... params) {
        long _long = 0L;
        Object container;
        try {
            container = this.getSpringBeanDao().HQL_uniqueObject(HQL, params);
            if (container != null) {
                _long = Long.parseLong(String.valueOf(container));
            }
        } catch (NumberFormatException e) {
            getLogger().error(HQL, e);
        }
        return _long;
    }

    @Override
    public List<Object[]> SQL_findQuery(String SQL) {
        return this.getSpringBeanDao().SQL_findQuery(SQL);
    }

    @Override
    public <TYPE> TYPE getBean(Class<TYPE> interfaceClass) {
        return this.getSpringBeanDao().getBean(interfaceClass);
    }

    @Override
    public Object getBean(String beanName) {
        return this.getSpringBeanDao().getBean(beanName);
    }

    @Override
    public ICodeSequenceDAO getCodeSequence() {
        return this.getSpringBeanDao().getCodeSequence();
    }

    @Nonnull
    @Override
    public Integer HQL_updateByQuery(String HQL, Object[] params) {
        return this.getSpringBeanDao().HQL_updateByQuery(HQL, params);
    }

    @Override
    public List HQL_findByQuery(String HQL, Object[] params) {
        return this.getSpringBeanDao().HQL_findByQuery(HQL, params);
    }

    @Override
    public List HQL_findByQuery(
            String HQL,
            Object[] params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return this.getSpringBeanDao().HQL_findByQuery(HQL, params, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    public Boolean SQL_createView(String nameView, String sql) throws QMSException {
        return this.getSpringBeanDao().SQL_createView(nameView, sql);
    }

    @Override
    public String SQL_convertDate(String tipoDatoDestino, String fecha, String formato) {
        return this.getSpringBeanDao().SQL_convertDate(tipoDatoDestino, fecha, formato);
    }

    public boolean isNewEntity(Persistable ent) {
        return isNewEntity(ent.getId());
    }

    public boolean isNewEntity(Long id) {
        return -1L == id;
    }

    @Override
    public <TYPE> List<TYPE> HQLT_findByQueryFilter(Class<TYPE> TYPE, String filtros) {
        return this.getSpringBeanDao().HQLT_findByQueryFilter(TYPE, filtros);
    }

    @Override
    public <TYPE> List<TYPE> HQLT_findByQueryFilter_NEntityFilter(Class<TYPE> TYPE, String filtro, String[] filtros) {
        return this.getSpringBeanDao().HQLT_findByQueryFilter_NEntityFilter(TYPE, filtro, filtros);
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String SQL) {
        return this.getSpringBeanDao().SQL_findSimpleLong(SQL);
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String SQL, String key, Object value) {
        return this.getSpringBeanDao().SQL_findSimpleLong(SQL, key, value);
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String SQL, Map<String, Object> params) {
        return this.getSpringBeanDao().SQL_findSimpleLong(SQL, params);
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String SQL, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String SQL, String key, Object value, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String SQL, Map<String, Object> params, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Integer SQL_updateByQuery(String SQL, Map params, List<String> synchronizeTables) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Integer SQL_updateByQuery(String SQL, Map m, Integer queryTimeoutSeconds, List<String> synchronizeTables) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List SQL_findByQuery(String SQL, Map m) {
        return this.getSpringBeanDao().SQL_findByQuery(SQL, m);
    }

    @Override
    public List SQL_findByQuery(String SQL, String key, Object value) {
        return this.getSpringBeanDao().SQL_findByQuery(SQL, key, value);
    }

    @Nonnull
    @Override
    public Object[] SQL_findSimpleQuery(String SQL, Map params) {
        return this.getSpringBeanDao().SQL_findSimpleQuery(SQL, params);
    }

    @Override
    public void saveLinkedItems(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final List dialogIds,
            final Boolean deleteLinkedItems,
            final Long loggedUserId
    ) {
        this.getSpringBeanDao().saveLinkedItems(compositeEntity, groundId, dialogIds, deleteLinkedItems, loggedUserId);
    }

    @Override
    public void saveLinkedItems(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final List dialogIds,
            final Boolean deleteLinkedItems,
            final Integer stage,
            final Boolean cacheable,
            final CacheRegion cacheRegion,
            final Integer queryTimeoutSeconds,
            final Long loggedUserId
    ) {
        this.getSpringBeanDao().saveLinkedItems(
                compositeEntity,
                groundId,
                dialogIds,
                deleteLinkedItems,
                stage,
                cacheable,
                cacheRegion,
                queryTimeoutSeconds,
                loggedUserId
        );
    }


    @Override
    public GridInfo HQL_getRows(String query, IGridFilter filter) {
        return this.getSpringBeanDao().HQL_getRows(query, filter);
    }

    @Override
    public GridInfo HQL_getRows(
            String query,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return this.getSpringBeanDao().HQL_getRows(query, filter, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    public GridInfo HQL_getRows(String query, IGridFilter filter, String alias) {
        return this.getSpringBeanDao().HQL_getRows(query, filter, alias);
    }

    @Override
    public GridInfo HQL_getRows(String query, IGridFilter filter, boolean like) {
        return this.getSpringBeanDao().HQL_getRows(query, filter, like);
    }

    @Override
    public Boolean SQL_execute(String sql) throws QMSException {
        return this.getSpringBeanDao().SQL_execute(sql);
    }

    @Override
    public Boolean SQL_execute(String sql, Integer timeoutSecounds) throws QMSException {
        return this.getSpringBeanDao().SQL_execute(sql, timeoutSecounds);
    }
    
    @Override
    public Boolean SQL_execute(String sql, Connection connection) throws QMSException {
        return this.getSpringBeanDao().SQL_execute(sql, connection); 
    }

    @Override
    public List HQL_findByQueryIdx(String hql, Map<String, Object> params) {
        return this.getSpringBeanDao().HQL_findByQueryIdx(hql, params);
    }

    @Override
    public List HQL_findByQueryIdx(String hql, String paramName, Object value) {
        return this.getSpringBeanDao().HQL_findByQueryIdx(hql, paramName, value);
    }

    @Nonnull
    @Override
    public Integer HQL_updateByQueryIdx(String hql, Map m) {
        return this.getSpringBeanDao().HQL_updateByQueryIdx(hql, m);
    }

    @Nonnull
    @Override
    public Integer HQL_updateByQueryIdx(String hql, String paramName, Object value) {
        return this.getSpringBeanDao().HQL_updateByQueryIdx(hql, paramName, value);
    }

    @Nonnull
    @Override
    public Integer HQL_findSimpleIntegerIdx(String hql, Map params) {
        return this.getSpringBeanDao().HQL_findSimpleIntegerIdx(hql, params);
    }

    @Nullable
    @Override
    public Object HQL_findSimpleObjectIdx(String hql, Map params) {
        return this.getSpringBeanDao().HQL_findSimpleObjectIdx(hql, params);
    }

    @Nonnull
    @Override
    public Long HQL_findSimpleLongIdx(String hql, Map params) {
        return this.getSpringBeanDao().HQL_findSimpleLongIdx(hql, params);
    }

    @Nonnull
    @Override
    public Integer SQL_findSimpleInteger(String SQL) {
        return this.getSpringBeanDao().SQL_findSimpleInteger(SQL);
    }

    @Override
    public Integer SQL_findSimpleInteger(String SQL, String key, Object value) {
        return this.getSpringBeanDao().SQL_findSimpleInteger(SQL, key, value);
    }

    @Nonnull
    @Override
    public Integer SQL_findSimpleInteger(String SQL, Map<String, Object> params) {
        return this.getSpringBeanDao().SQL_findSimpleInteger(SQL, params);
    }

    @Nonnull
    @Override
    public Integer SQL_findSimpleInteger(String SQL, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Nonnull
    @Override
    public Integer SQL_findSimpleInteger(String SQL, String key, Object value, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Nonnull
    @Override
    public Integer SQL_findSimpleInteger(String SQL, Map<String, Object> params, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Override
    public <TYPE> List<TYPE> HQL_findAll(Class<TYPE> TYPE) {
        return this.getSpringBeanDao().HQL_findAll(TYPE);
    }

    @Override
    public <TYPE> Long HQL_countByPagedFilter(Class<TYPE> TYPE, IGridFilter filter, boolean like) {
        return this.getSpringBeanDao().HQL_countByPagedFilter(TYPE, filter, like);
    }

    @Override
    public <TYPE> Long HQL_countByPagedFilter(
            Class<TYPE> TYPE,
            IGridFilter filter,
            boolean like,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return this.getSpringBeanDao().HQL_countByPagedFilter(TYPE, filter, like, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    public <TYPE> Long HQL_countByPagedFilter(Class<TYPE> TYPE, IGridFilter filter) {
        return this.getSpringBeanDao().HQL_countByPagedFilter(TYPE, filter);
    }

    @Override
    public <TYPE> List<TYPE> HQL_getListResultsByQuery(
            Class<TYPE> TYPE,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return this.getSpringBeanDao().HQL_getListResultsByQuery(TYPE, filter, cacheable, cacheRegion, queryTimeoutSeconds);
    }

    @Override
    public void saveLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final Long dialogId,
            final Long loggedUserId
    ) {
        this.getSpringBeanDao().saveLinkedItem(compositeEntity, groundId, dialogId, loggedUserId);
    }

    @Override
    public void saveLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final Long groundId,
            final Long dialogId,
            final Integer stage,
            final Long loggedUserId
    ) {
        this.getSpringBeanDao().saveLinkedItem(compositeEntity, groundId, dialogId, stage, loggedUserId);
    }

    @Override
    public GridInfo getRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, RecordRowsType type, IPendingOperation... pendings) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> GridInfo<TYPE> getRowsByProjection(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            Class<TYPE> projectionClass,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo getLightRows(
            IGridFilter filter,
            Class entityClass,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }


    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     */
    @Override
    public IUntypedDAO getAspectJAutoProxy() {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, IPendingOperation... pendings) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, IPendingOperation... pendings) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo getActiveRecordRows(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, IPendingOperation... pendings) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List HQL_findByQueryLimit(String HQL, Integer limit) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List HQL_findByQueryLimit(String HQL, Integer limit, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    public ResourceBundle getTags() {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getTag(String key) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<Map<String, Object>> SQL_findMap(String SQL, Map params) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   

    }

    @Override
    public List<Map<String, Object>> SQL_findMap(String SQL, String key, String value) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Override
    public GridInfo<Map<String, Object>> SQL_getRowsByQuery(String query, IGridFilter filter) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Override
    public GridInfo<Map<String, Object>> SQL_getRowsByQuery(String withSelect, String sqlSelect, IGridFilter filter) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Override
    public Long SQL_findSingleLong(String query, Map<String, Object> params) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Override
    public String SQL_findSingleString(String query, Map<String, Object> params) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Override
    public Object SQL_findSingleObject(String SQL, Map<String, Object> params) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.   
    }

    @Override
    public List HQL_findByQueryLimit(String hql, Map params, Integer maxResults) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.  
    }

    @Override
    public List HQL_findByQueryLimit(
            String hql,
            Map params,
            Integer maxResults,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.  
    }

    @Override
    public GridInfo HQL_getRowsByQuery(String query, IGridFilter filter, String alias) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.  
    }

    @Override
    public Map SQL_findSimpleMap(String SQL) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Map SQL_findSimpleMap(String SQL, String key, String value) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Map SQL_findSimpleMap(String SQL, Map params) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> List<TYPE> HQLT_findByQuery(Class<TYPE> type, String HQL) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> List<TYPE> HQLT_findByQuery(Class<TYPE> type, String HQL, String paramName, Object value) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> List<TYPE> HQLT_findByQuery(Class<TYPE> type, String HQL, Map<String, Object> params) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> List<TYPE> HQLT_findByQuery(
            Class<TYPE> type,
            String HQL,
            Map<String, Object> params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> TYPE HQLT_findSimple(Class<TYPE> type, String HQL) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> TYPE HQLT_findSimple(
            Class<TYPE> type,
            String HQL,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nullable
    @Override
    public <TYPE> TYPE HQLT_findSimple(Class<TYPE> type, String HQL, String paramName, Object value) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Long HQL_findLong(String hql) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Long HQL_findLong(String hql, Map params) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Long HQL_findLong(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Long HQL_findLong(String hql, String paramName, Object value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Date HQL_findDate(String hql) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Date HQL_findDate(String hql, Map params) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE extends Persistable> TYPE makeCustomPersistent(
            final TYPE entity,
            final Boolean useCodePrefix,
            final ILoggedUser loggedUser,
            final IPersistenceManager... managers
    ) throws QMSException {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }


    @Override
    public <TYPE extends Persistable> TYPE saveSingleLinkedItem(
            final Class<? extends ILinkedCompositeEntity> compositeEntity,
            final TYPE singleLinkedItem,
            final Long parentId,
            final Integer stage,
            final Long loggedUserId
    ) {
        throw new UnsupportedOperationException("Not supported yet. Utilizar getBean()."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Object HQL_findSingleObject(String hql, Map params) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Object HQL_findSingleObject(
            String hql,
            Map params,
            Boolean cacheable,
            CacheRegion cacheRegion
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String HQL_findSingleString(String hql) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String HQL_findSingleString(String hql, Map params) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String HQL_findSingleString(String hql, String paramName, Object value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> ResponseEntity<Map<String, Object>> toggleStatus(
            Class<TYPE> cls,
            Long id,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            ILoggedUser loggedUser
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> ResponseEntity<Map<String, Object>> toggleStatus(Class<TYPE> cls, Long id, Integer ACTIVE, Integer INACTIVE, ILoggedUser loggedUser) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String getRowsSQL(Class entityClass) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String hqlToSqlString(String HQL) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List HQL_findByQueryPaged(String hql, IPagedQuery page) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List HQL_findByQueryPaged(
            String hql,
            Map<String, Object> params,
            IPagedQuery page,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List HQL_findByQueryPaged(String hql, String paramName, Object value, IPagedQuery page) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Integer SQL_externalUpdateByQuery(String SQL, Map<String, Object> params, Connection connection, String sessionStatement, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Integer SQL_findSimpleInteger(String sqlSelect, String key, String value, Connection connection, String sessionStatement, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Integer SQL_findSimpleInteger(String sqlSelect, Map<String, Object> params, Connection connection, String sessionStatement, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String sqlSelect, String key, String value, Connection connection, String sessionStatement, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Long SQL_findSimpleLong(String sqlSelect, Map<String, Object> params, Connection connection, String sessionStatement, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<Map<String, Object>> SQL_findByQuery(String SQL, Map<String, Object> params, Connection connection, String sessionStatement) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo<Map<String, Object>> getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo<Map<String, Object>> getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo getTreeLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Integer HQL_updateByQuery(
            Class<? extends Persistable> entity,
            Map<String, Object> properties,
            Long loggedUserId,
            Long recordId,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds,
            String observation
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Integer HQL_updateByQuery(
            Class<?> entity,
            Map<String, Object> properties,
            ILoggedUser loggedUser,
            Long recordId,
            String customPropId,
            String alias,
            String extraWhere,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo getLightRows(
            IGridFilter filter,
            Class<? extends BaseAPE> entityClass,
            List<ColumnDTO> fields,
            String join,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public GridInfo HQL_getRowsByQuery(StringBuilder HQL, IGridFilter filter) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getRowsByQuery(StringBuilder HQL, IGridFilter filter, boolean like) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getRows(
            StringBuilder HQL,
            IGridFilter filter,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String entityAlias,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Consumer<Map<String, Object>> peekProcessParentRowConsumer,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getTreeRows(
            StringBuilder HQL,
            IGridFilter filter,
            String entityAlias,
            String rowIdFieldName,
            String rowParentIdFieldName,
            String rowChildListFieldName,
            Predicate<Map<String, Object>> filterParentIdsPredicate,
            Function<Map<String, Object>, Integer> childCountParentRowFunction,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getRowsByQuery(
            StringBuilder query,
            IGridFilter filter,
            String alias
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo HQL_getRows(
            StringBuilder HQL,
            IGridFilter filter,
            String entityAlias,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List HQL_findByQuery(String HQL, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Boolean HQL_findSimpleBoolean(String hql) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Boolean HQL_findSimpleBoolean(
            String hql, Map params,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Nonnull
    @Override
    public Boolean HQL_findSimpleBoolean(String hql, String paramName, Object value) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<ITextHasValue> getStrutsComboList(
            Class clazz,
            String mappedDescription,
            String filtros,
            Boolean withStatus,
            Boolean cacheable,
            CacheRegion cacheRegion,
            Integer queryTimeoutSeconds
    ) {
        return getStrutsComboList(clazz, mappedDescription, filtros, -1L, withStatus);
    }

    @Override
    public <TYPE> TYPE makePersistent(TYPE entity, Long loggedUserId, Boolean generateLogging) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public GridInfo getRecordRowsWithNewTx(ILoggedUser user, IGridFilter filter, Class entityClass, PendingRecord.STATUS[] statuses, RecordRowsType type, IPendingOperation... pendings) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public <TYPE> TYPE saveOrUpdate(TYPE entity, Long loggedUserId) throws MakePersistentException, BatchUpdateException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public <TYPE> TYPE saveOrUpdate(TYPE entity, Long loggedUserId, boolean generateLogging) throws MakePersistentException, BatchUpdateException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public String HQL_findString(String hql, Map params, Boolean cacheable, CacheRegion cacheRegion, Integer queryTimeoutSeconds) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Map<String, Object>> SQL_findMap(String SQL) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

}
