/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package Framework.DAO;

import java.sql.Types;
import org.hibernate.dialect.Oracle10gDialect;
import org.hibernate.dialect.function.SQLFunctionTemplate;
import org.hibernate.type.StandardBasicTypes;

/**
 *
 * <AUTHOR>
 */
public class BnextOracleDialect extends Oracle10gDialect{

    public BnextOracleDialect() {
        registerFunction("days", new SQLFunctionTemplate(StandardBasicTypes.DATE, "?1 + interval ?2 day"));
        registerHibernateType(Types.NVARCHAR, org.hibernate.type.StringType.INSTANCE.getName());
        registerHibernateType(-101, org.hibernate.type.TimestampType.INSTANCE.getName());
    }
    
}
