/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package Framework.Action;

import DPMS.ActiveDirectoryInterface.ActiveDirectoryUtil;
import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.pending.entities.PendingType;
import bnext.licensing.LicenseUtil;
import com.opensymphony.xwork2.ActionSupport;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import mx.bnext.access.Module;
import mx.bnext.cipher.HexUtil;
import mx.bnext.core.util.Loggable;
import mx.bnext.licensing.License;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.struts2.action.ServletRequestAware;
import org.slf4j.Logger;
import qms.access.util.AccessUtil;
import qms.framework.dao.ILicenseUserDAO;
import qms.framework.entity.SystemSettings;
import qms.framework.util.AboutApp;
import qms.framework.util.BusinessUnitUtil;
import qms.framework.util.DatabaseUtil;
import qms.util.interfaces.ExcludeFromAOP;

/**
 *
 * <AUTHOR> Carlos Limas
 */
public class BackEndAction extends ActionSupport implements ServletRequestAware, ExcludeFromAOP {

    private static final long serialVersionUID = 1L;
    
    private static final Logger LOGGER = Loggable.getLogger(BackEndAction.class);

    private HttpServletRequest request;
    private String availableModules = null;
    private String token = null;
    private String saveResult = null;

    @Override
    public String execute() throws Exception {
        final SessionViewer session = new SessionViewer();
        final boolean hasAccess = AccessUtil.hasAccess("hasAuthority('ADMON_SISTEMA')", session.getLoggedUserServices(), session.isAdmin());
        if (!hasAccess) {
            return SessionViewer.INVALID;
        }
        final String result = super.execute();
        if (!SUCCESS.equals(result)) {
            return result;
        } else {
            return this.saveChanges();
        }
    }
    
    public final String getSystemVersion() {
        return AboutApp.getBuildVersion();
    }

    public String getSystemLang() {
        return Utilities.getSettings().getLang();
    }

    public String getSystemLocale() {
        return Utilities.getSettings().getLocale();
    }
    
    public boolean getLocalizedEntities() {
        return Utilities.areEntitiesLocalized();
    }
    
    private String saveChanges() {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        if (this.availableModules != null) {
            final Boolean validToken = validateToken();
            if (validToken) {
                dao.HQL_updateByQuery(""
                    + " UPDATE " + SystemSettings.class.getCanonicalName() + " s "
                    + " SET s.availableModules = :availableModules"
                    + " WHERE id = 1", "availableModules", availableModules
                );
                Utilities.resetAvailableModules();
                saveResult = SUCCESS;
                return SUCCESS;
            } else {
                LOGGER.error("Invalid token provided for changing available modules, please generate the token again. Token:.", token);
                saveResult = "INVALID_TOKEN";
                return SUCCESS;
            }
        } else {
            return SUCCESS;
        }
    }
    
    

    /**
     * Funciona para utilizar CSS solo en uso de STRUTS
     *
     * @return
     */
    public boolean isValid() {
        return true;
    }

    public String getAppInfo() {
        StringBuilder info = new StringBuilder();
        info.append("<br><br><label>GENERAL</label><br><table>"
            + "<tr><td>ApplicationName</td><td>").append(Utilities.getApplicationName()).append("</td></tr>"
                + "<tr><td>ApplicationVersion</td><td>").append(AboutApp.getProjectVersion()).append("</td></tr>"
                + "<tr><td>BuildVersion</td><td>").append(AboutApp.getBuildVersion()).append("</td></tr>"
            + "<tr><td>BuildNumber</td><td>").append(AboutApp.getBuildNumber()).append("</td></tr>"
            + "<tr><td>BuildDate</td><td>").append(AboutApp.getBuildDate()).append("</td></tr>"
            + "<tr><td>RevisionDate</td><td>").append(AboutApp.getRevisionDate()).append("</td></tr>"
            + "<tr><td>BuildProfile</td><td>").append(AboutApp.getBuildProfile()).append("</td></tr>"
            + "<tr><td>ReachableUrl</td><td>").append(Utilities.isReachableUrlSettings()).append("</td></tr>"
            + "<tr><td>SSO Status</td><td>").append(ActiveDirectoryUtil.status()).append("</td></tr>"
            + "<tr><td>BusinessUnitLabel</td><td>").append(BusinessUnitUtil.getBusinessUnitLabel()).append("</td></tr>"
            + "<tr><td>BusinessUnitLabelGenre</td><td>").append(BusinessUnitUtil.getBusinessUnitLabelGenre()).append("</td></tr>"
            + "<tr><td>businessUnitLabelMany</td><td>").append(BusinessUnitUtil.getBusinessUnitLabelMany()).append("</td></tr>"
            + "<tr><td>isMailSendingOn</td><td>").append(Utilities.isMailSendingOn()).append("</td></tr>"
            + "<tr><td>mailSystem</td><td>").append(Utilities.getMailSystem()).append("</td></tr>"
            + "<tr><td>documentCodeScope</td><td>").append(Settings.CODE_SCOPE.fromValue(Utilities.getSettings().getDocumentCodeScope())).append("</td></tr>"
        );
        
        info.append("</table><br><br><label>DATABASE</label><br><table>"
            + "<tr><td>Driver class</td><td>").append(DatabaseUtil.getQmsConfig().getDriverClassName()).append("</td></tr>"
            + "<tr><td>URL</td><td>").append(DatabaseUtil.getQmsConfig().getJdbcUrl()).append("</td></tr>"
        );

        License currentLicense = LicenseUtil.getCurrentLicense();
        if (currentLicense == null) {
            currentLicense = new License();
        }
        info.append("</table><br><br><label>LICENCE TYPE</label><br><table>"
            + "<tr><td>licenceType DB</td><td>").append(Settings.LICENCE_TYPE.fromValue(Utilities.getSettings().getLicenceType())).append("</td></tr>"
            + "<tr><td>licenceType APP</td><td>").append(Settings.LICENCE_TYPE.fromValue(currentLicense.getLicenseType())).append("</td></tr>"
        );

        info.append("</table><br><br><label>LICENCE</label><br><table>"
                + "<tr><td>licence daysOfGrace</td><td>").append(LicenseUtil.daysOfGrace()).append("</td></tr>"
                + "<tr><td>licence daysToEndSupport</td><td>").append(LicenseUtil.daysToEndSupport()).append("</td></tr>");

        try {
            final Map<String, String> licenseValues = BeanUtils.describe(currentLicense);
            licenseValues.forEach((key, value) -> info.append("<tr><td>licence ")
                    .append(key).append("</td><td>").append(value == null ? "-" : value).append("</td></tr>"));
        } catch (Exception ex) {
            LOGGER.error("Error getting license", ex);
        }
                        
        info.append("</table><br><br><label>SETTINGS</label><br><table>");
        final Settings settings = Utilities.getSettings();
        try {
            final Map<String, String> settingsValues = BeanUtils.describe(settings);
            settingsValues.forEach((key, value) -> info.append("<tr><td>")
                    .append(key).append("</td><td>").append(value == null ? "-" : value).append("</td></tr>"));
        } catch (Exception ex) {
            LOGGER.error("Error getting settings", ex);
        }
        Map<String, PendingType> apeData = Utilities.getApeData();
        info.append("</table><br><br><label>APE_DATA</label><br><table>");
        apeData.forEach((key, value) -> info
                .append("<tr><td>code</td><td>").append(value.getCode()).append("</td></tr>")
                .append("<tr><td>escDays</td><td>").append(value.getEscalationDays()).append("</td></tr>")
                .append("<tr><td>escEnabled</td><td>").append(value.getEscalationEnabled()).append("</td></tr>")
                .append("<tr><td>escMode</td><td>").append(value.getEscalationLevels()).append("</td></tr>")
                .append("<tr><td>escMode</td><td>").append(value.getEscalationMode()).append("</td></tr>")
                .append("<tr><td>flex-attendant</td><td>").append(value.getFlexibleAttendant()).append("</td></tr>")
                .append("<tr><td>module</td><td>").append(value.getModule()).append("</td></tr>"));
        info.append("</table>");
        return info.toString();
    }

    @Override
    public void withServletRequest(HttpServletRequest request) {
        this.request = request;
    }
    
    public void setAvailableModules(String availableModules) {
        this.availableModules = availableModules;
    }
    
    public void setToken(String token) {
        this.token = token;
    }

    public String getSaveResult() {
        return saveResult;
    }
        
    public String getAvailableModulesTable() {
        StringBuilder availableModulesTable = new StringBuilder();
        Module.allModules().forEach((Module m) -> {
            availableModulesTable
                    .append(""
                        + "<div class=\"small-12 cell\">"
                            + "<input "
                                + " class='available-modules'"
                                + " type='checkbox'");
            if (Utilities.isModuleAvailable(m)) {
                availableModulesTable.append(" checked ");
            }
            availableModulesTable.append(""
                                + " id='").append(m.name()).append("'"
                                + " name='").append(m.name()).append("'"
                            + "/>"
                            + "<label for='").append(m.name()).append("'>")
                                    .append(m.getKey()).append(""
                            + "</label>"
                        + "</div>")
                    
                    ;
        });
        return availableModulesTable.toString();
    }

    private Boolean validateToken() {
        try {
            if (token == null || token.trim().isEmpty()) {
                LOGGER.error("Invalid token provided for changing available modules: {}", token);
                return false;
            }
            final String plainToken = HexUtil.hexDecoder(token);
            if (plainToken == null || plainToken.trim().isEmpty()) {
                LOGGER.error("Invalid encoded token provided for changing available modules: {}", token);
                saveResult = "INVALID_TOKEN";
                return false;
            }
            final License lic = LicenseUtil.getCurrentLicense();
            if (lic == null) {
                LOGGER.error("Not configured license for changing available modules: {}", token);
                saveResult = "INVALID_TOKEN";
                return false;
            }
            final ILicenseUserDAO licenseDao = Utilities.getBean(ILicenseUserDAO.class);
            final String tokenSecret = lic.getSystemId() + "-" + AboutApp.getProjectVersion() + "-" + AboutApp.getBuildNumber();
            final Boolean valid = licenseDao.validateCertificateStatus(tokenSecret, plainToken);
            return valid;
        } catch (final Exception ex) {
            LOGGER.error("Invalid token provided {}", token, ex);
            return false;
        }
    }
}
