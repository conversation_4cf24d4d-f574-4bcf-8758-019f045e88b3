package Framework.Action;

import java.util.Date;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class ActionThreadInstance {

    private Date timestamp;
    private final String id;
    private final String key;
    private final String tabId;

    public ActionThreadInstance(String key) {
        this.key = key;
        this.timestamp = null;
        this.id = null;
        this.tabId = null;
    }

    public ActionThreadInstance(String key, Date timestamp, String id, String tabId) {
        this.timestamp = timestamp;
        this.id = id;
        this.key = key;
        this.tabId = tabId;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public String getId() {
        return id;
    }

    public String getKey() {
        return key;
    }

    public String getTabId() {
        return tabId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.getId());
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ActionThreadInstance other = (ActionThreadInstance) obj;
        return Objects.equals(this.key, other.getId());
    }

    @Override
    public String toString() {
        return "ActionThreadInstance{" + "timestamp=" + timestamp + ", id="
                + id + ", key=" + key + '}';
    }

}
