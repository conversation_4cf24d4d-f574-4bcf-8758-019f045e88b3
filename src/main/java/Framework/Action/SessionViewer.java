package Framework.Action;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.User;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.licensing.LicenseUtil;
import bnext.login.Login;
import bnext.login.config.CustomWebAuthenticationDetails;
import bnext.login.logic.RenewDataStatus;
import bnext.reference.UserRef;
import com.opensymphony.xwork2.ActionContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.access.ProfileServices.ServicesType;
import mx.bnext.core.util.Loggable;
import org.apache.struts2.action.ServletContextAware;
import org.apache.struts2.action.ServletRequestAware;
import org.apache.struts2.action.ServletResponseAware;
import org.apache.struts2.action.SessionAware;
import org.slf4j.Logger;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.access.logic.SessionHelper;
import qms.access.util.AccessUtil;
import qms.access.util.ProfileServicesUtil;
import qms.framework.rest.SecurityUtils;
import qms.framework.rest.UserUtils;
import qms.framework.util.AboutApp;
import qms.util.ModuleUtil;
import qms.util.interfaces.ContextView;

/**
 *
 * <AUTHOR> Limas
 */
public class SessionViewer extends BnextAction implements SessionAware, ServletRequestAware, ServletResponseAware, ServletContextAware, ContextView {
    
    public static final String NO_MULTI_THREAD = "no_multi_thread";
    public static final String NO_ACCESS = "noaccess";
    public static final String BUSY = "busy";
    public static final String INVALID = "invalid";
    public static final String NO_SUPPORT = "nosupport";
    public static final String RELOADED = "reloaded";
    public static final String NO_SESSION = "nosession";
    public static final String RENEW_DATA = "renew-data";

    private String[] currentEntityId = {};
    private Map session = new HashMap();
    private Long loggedUserId = 0L; 
    private String loggedUserName = null;
    private String loggedUserAccount = null;
    private List<ProfileServices> loggedUserServices = null;
    private List<ProfileServices> loggedUserServicesAddedBySystem = null;
    private String requiredServices = "none";
    private String requiredAccess = "none";
    private User usuario;
    private UserRef userRef;
    private boolean admin = false;
    private String rolAdmin = null;
    public static String END = "end";
    public static final String ADD_SUCCESS = "{\"tipo\":\"add_success\"}";
    public static final String EDIT_SUCCESS = "{\"tipo\":\"edit_success\"}";
    private Boolean isCorp = null;
    private SessionHelper sessionHelper = null; 
    private LoggedUser loggedUserDto;
    private Boolean reloadLicense = false;
    private String newLicenseClass;
    private Boolean renewDataLocation = null;
    private Boolean renewPassword = false;
    private Long impersonatingUserId = null;
    private Boolean geolocationAccessHistory;
    private String detailError = "";

    /*
     * OPCIONAL:
     *      ** Esto es un parametro que se recibe desde el struts.xml **
     *      ** Se utiliza para limitar el permiso segun servicios (perfiles) desde struts.xml **
     *
     *      Ej. intBLectorAuditoria,intBEncargadoEncuesta,intBEncargadoDocumento
     *      (Son los valores declarados en el objeto Profile.Servicios)
     */
    private String activeServices;
    private ProfileServices[] serviciosActivos;

    /**
     * TODO:
     *      - Llenar cada variable con su valor correspondiente (permiso, nueva implementacion)
     *      - Dentro de los action's y CRUD's de cada modulo validar que su variable correspondiente sea 'true'
     *        (si valen falso enviar a NONE)
     *        "return NONE;"
     *              - Para CRUD_'s en el metodo SMD()
     *              - Para Action_'s en el metodo execute()
     *
     */
    private boolean configurationAccess;
    private boolean meetingAccess;
    private boolean documentAccess;
    private boolean formularieAccess;
    private boolean auditAccess;
    private boolean activityAccess;
    private boolean actionAccess;
    private boolean pollAccess;
    private boolean complaintAccess;
    private boolean meterAccess;
    private boolean projectAccess;
    private boolean plannerAccess;
    private boolean deviceAccess;
    private boolean fiveSAccess;
    private boolean timeworkAccess;
    private boolean timesheetAccess;
    
    //Variables para el acceso al menu
    private boolean documentMenuAccess;
    private boolean accesMenuAudits;
    private boolean accessMenuMeter;
    private boolean accessMenuForms;
    private final boolean accessMenuActivities;
    
    public SessionViewer() {
        sessionHelper = new SessionHelper();
        //permiso al modulo de auditorias
        auditAccess = hasAuditAccess();
        activityAccess = hasActivityAccess();
        configurationAccess = hasConfigurationAccess();
        pollAccess = hasPollAccess();
        meetingAccess = hasMeetingAccess();
        documentAccess = hasDocumentAccess();
        formularieAccess = hasFormulariesAccess();
        actionAccess = hasActionAcess();
        complaintAccess = hasComplaintAccess();
        meterAccess = hasMeterAccess();
        projectAccess = hasProjectAccess();
        plannerAccess = hasPlannerAccess();
        deviceAccess = hasDeviceAccess();
        fiveSAccess = hasFiveSAccess();
        timeworkAccess = hasTimeworkAccess();
        documentMenuAccess = hasDocumentMenuAccess();
        accesMenuAudits = hasAccesMenuAudits();
        accessMenuMeter = hasAccessMenuMeter();
        accessMenuForms = hasAccessMenuForms();
        accessMenuActivities = hasAccessMenuActivities();
        timesheetAccess = hasTimesheetAccess();
    }
    
    public SessionViewer(final Boolean moduleAccess) {
        sessionHelper = new SessionHelper();
        auditAccess = moduleAccess;
        activityAccess = moduleAccess;
        configurationAccess = moduleAccess;
        pollAccess = moduleAccess;
        meetingAccess = moduleAccess;
        documentAccess = moduleAccess;
        formularieAccess = moduleAccess;
        actionAccess = moduleAccess;
        complaintAccess = moduleAccess;
        meterAccess = moduleAccess;
        projectAccess = moduleAccess;
        plannerAccess = moduleAccess;
        deviceAccess = moduleAccess;
        fiveSAccess = moduleAccess;
        timeworkAccess = moduleAccess;
        documentMenuAccess = moduleAccess;
        accesMenuAudits = moduleAccess;
        accessMenuMeter = moduleAccess;
        accessMenuForms = moduleAccess;
        accessMenuActivities = moduleAccess;
        timesheetAccess = moduleAccess;
    }

    public boolean isValidSession() {
        Login util = new Login(request, getLoggedUserAccount());
        return util.validateSession();
    }

    @Override
    public String execute() throws Exception {
        final String r = super.execute();
        if(!SUCCESS.equals(r)) {
            return r;
        }
        if (!this.isValidSession()) {
            return NO_SESSION;
        }
        if (reloadLicense != null && reloadLicense) {
            if (!isAdmin()) {
                return "reloaded";
            }
            LicenseUtil.clearCurrent();
            if (getNewLicense() != null && !getNewLicense().trim().isEmpty()) {
                final boolean resultUpdate = LicenseUtil.updateLicenseFile(getNewLicense());
                if (!resultUpdate) {
                    final String licenseError = LicenseUtil.getConfiguration();
                    setLicenseError(licenseError + "<label class='error'>* Could not update licence.</label>");
                    newLicenseClass = "invalidValue";
                    return NO_SUPPORT;
                }
            }
            if (LicenseUtil.isLicenseReloaded(getNewLicense(), reloadLicense)) {
                return "reloaded";
                
            } else if (reloadLicense != null && reloadLicense) {
                final String licenseError = LicenseUtil.getConfiguration();
                Loggable.getLogger(SessionViewer.class).error("\r/Verificar licencias; systemId: '{}', version: '{}', actual: '{}', error:'{}'", new Object[]{
                    Utilities.getSettings().getSystemId(), AboutApp.getProjectVersion(),
                    Utilities.getSerializedObj(LicenseUtil.getCurrentLicense()), licenseError
                });
                setLicenseError(licenseError);
                return NO_SUPPORT;
            }

        }
        if (isRenewDataNeeded()) {
            return RENEW_DATA;
        }
        geolocationAccessHistory = getLoggedUserServices().contains(ProfileServices.GEOLOCATION_ACCESS_HISTORY) || isAdmin();
        return r;
    }



    private boolean hasDocumentMenuAccess() {
        if(!Utilities.isModuleAvailable(Module.DOCUMENT)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.DOCUMENTO_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_LECTOR)
                || getLoggedUserServices().contains(ProfileServices.REPORTE_DOCUMENTO)
                || getLoggedUserServices().contains(ProfileServices.DOCUMENT_PRINT_UNCONTROLLED_COPIES)
                || getLoggedUserServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS)
                || getLoggedUserServices().contains(ProfileServices.FILL_OUT_HISTORY)
                || getLoggedUserServices().contains(ProfileServices.FORMULARIO_CONTROL);
    }
    
    private boolean hasMeetingMenuAccess() {
        if(!Utilities.isModuleAvailable(Module.MEETING)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.REUNION_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.REUNION_ENCARGADO)
                || getLoggedUserServices().contains(ProfileServices.REUNION_LECTOR);
    }
    
    private boolean hasAccesMenuAudits() {
        if(!Utilities.isModuleAvailable(Module.AUDIT)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.AUDIT_HELPER)
                || getLoggedUserServices().contains(ProfileServices.AUDIT_LIDER)
                || getLoggedUserServices().contains(ProfileServices.AUDIT_QUALITY_MANAGER)
                || getLoggedUserServices().contains(ProfileServices.AUDIT_SURVEY_FORMULATION);
    }
    private boolean hasAccessMenuActivities() {
        if (!Utilities.isModuleAvailable(Module.ACTIVITY)) {
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.ACTIVITY_CREATOR)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_TEMPLATE_CREATOR)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_BULK_CREATOR)
                || getLoggedUserServices().contains(ProfileServices.REPORT_ACTIVITY)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_MANAGER)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_VIEWER);
    }
    
    private boolean hasAccessMenuMeter() {
        if(!Utilities.isModuleAvailable(Module.METER)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.INDICADOR_LECTOR)
                || getLoggedUserServices().contains(ProfileServices.INDICADOR_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.INDICADOR_ENCARGADO);
    }
    
    private boolean hasTimesheetAccess() {
        if (!Utilities.isModuleAvailable(Module.TIMESHEET)){
            return false;
        }
        return hasAccessTimesheetCatalog()
                || getLoggedUserServices().contains(ProfileServices.TS_EDIT_BUSINESS_UNIT)
                || getLoggedUserServices().contains(ProfileServices.TS_EDIT_BUSINESS_UNIT_DEP)
                || getLoggedUserServices().contains(ProfileServices.TS_READ_BUSINESS_UNIT)
                || getLoggedUserServices().contains(ProfileServices.TS_READ_BUSINESS_UNIT_DEP)
                || getLoggedUserServices().contains(ProfileServices.TS_REGISTER_PLANNED)
                || getLoggedUserServices().contains(ProfileServices.TS_REGISTER_UNPLANNED)
                || getLoggedUserServices().contains(ProfileServices.REPORTE_TIMESHEET);
    }
    
    private boolean hasAccessTimesheetCatalog() {
        if (!getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)) {
            return false;
        }
        return  getLoggedUserServices().contains(ProfileServices.TS_REGISTER_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.TS_DELETE_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.TS_VIEW_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.TS_REGISTER_QUERY)
                || getLoggedUserServices().contains(ProfileServices.TS_DELETE_QUERY)
                || getLoggedUserServices().contains(ProfileServices.TS_ENABLE_CACHE)
                || getLoggedUserServices().contains(ProfileServices.TS_SYNC_CACHE)
                || getLoggedUserServices().contains(ProfileServices.TS_VIEW_QUERY)
                || getLoggedUserServices().contains(ProfileServices.TS_REGISTER_REPORT)
                || getLoggedUserServices().contains(ProfileServices.TS_DELETE_REPORT)
                || getLoggedUserServices().contains(ProfileServices.TS_VIEW_SYNC_EVENT)
                || getLoggedUserServices().contains(ProfileServices.TS_ADMON_REPORT_ACCESS);
    }
    
    private boolean hasAccessMenuForms() {
        if (!Utilities.isModuleAvailable(Module.FORMULARIE)) {
            return false;
        }
        return hasAccessFormulariesCatalog()
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_LECTOR)
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)
                || getLoggedUserServices().contains(ProfileServices.FORMULARIO_ALTA)
                || getLoggedUserServices().contains(ProfileServices.REPORT_FORM)
                || getLoggedUserServices().contains(ProfileServices.FORMULARIO_CONTROL)
                || getLoggedUserServices().contains(ProfileServices.FILL_OUT_HISTORY)
                || getLoggedUserServices().contains(ProfileServices.FORMULARIO_CONTROL);
    }
    
    private boolean hasFiveSAccess() {
        if(!Utilities.isModuleAvailable(Module.FIVES)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER)
                || getLoggedUserServices().contains(ProfileServices.FIVES_PRINT)
                || getLoggedUserServices().contains(ProfileServices.FIVES_LINK)
                || getLoggedUserServices().contains(ProfileServices.FIVES_CREATE)
                || getLoggedUserServices().contains(ProfileServices.FIVES_MAP)
                || getLoggedUserServices().contains(ProfileServices.FIVES_SECTION)
                || getLoggedUserServices().contains(ProfileServices.FIVES_ITEM);
    }

    private boolean hasTimeworkAccess() {
        if (!Utilities.isModuleAvailable(Module.TIMEWORK)) {
            return false;
        }
        return Utilities.isTimeworkAvailable() && (
                getLoggedUserServices().contains(ProfileServices.TIMEWORK_LIST)
                        || getLoggedUserServices().contains(ProfileServices.TIMEWORK_SETTINGS)
                        || getLoggedUserServices().contains(ProfileServices.TIMEWORK_MANUALLY_REGISTER)
                        || getLoggedUserServices().contains(ProfileServices.FORM_STOPWATCH_LIST)
                        || hasAccessTimeworkCatalog()
                        || getLoggedUserServices().contains(ProfileServices.TIMEWORK_SYNC)
        );
    }
    
    private boolean hasDeviceAccess() {
        if(!Utilities.isModuleAvailable(Module.DEVICE)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.DEVICE_CREATOR)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_DISPOSE_VIEWER)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_MANAGER)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_REALIZE_SERVICE)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_SCHEDULED_SERVICES_VIEWER)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_SERVICE_HISTORY_VIEWER)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_SERVICE_METRIC_VIEWER)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_SERVICE_SCHEDULER)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_VIEWER)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_EDITOR);
    }

    private boolean hasProjectAccess() {
        if(!Utilities.isModuleAvailable(Module.PROJECT)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.PROYECTO_LECTOR)
                || getLoggedUserServices().contains(ProfileServices.PROYECTO_CONTABILIDAD)
                || getLoggedUserServices().contains(ProfileServices.PROYECTO_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.PROYECTO_ENCARGADO);
    }

    private boolean hasPlannerAccess() {
        if(!Utilities.isModuleAvailable(Module.PLANNER)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.PLANNER_CREATOR)
                || getLoggedUserServices().contains(ProfileServices.PLANNER_VIEW_ALL)
                || getLoggedUserServices().contains(ProfileServices.PLANNER_VIEW_OWNED)
                || getLoggedUserServices().contains(ProfileServices.PLANNER_VIEW_BUSINESS_UNIT)
                || getLoggedUserServices().contains(ProfileServices.PLANNER_VIEW_BUSINESS_UNIT_DEP);
    }

    private boolean hasMeterAccess() {
        if(!Utilities.isModuleAvailable(Module.METER)){
            return false;
        }
        return hasAccessMenuMeter()
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DEPARTMENT_ATTENDANT)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_METER_REVIEW_RESPONDENT);
    }

    private boolean hasComplaintAccess() {
        if(!Utilities.isModuleAvailable(Module.COMPLAINT)){
            return false;
        }
        return Utilities.ParseInteger("" + getSession().get("introlquejas")) != 0
                || getLoggedUserServices().contains(ProfileServices.QUEJA_LECTOR)
                || getLoggedUserServices().contains(ProfileServices.QUEJA_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.QUEJA_ENCARGADO)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_COMPLAINT_DEPARTMENT_ATTENDANT)
                ;
    }
    
    private boolean hasActionAcess() {
        if(!Utilities.isModuleAvailable(Module.ACTION)){
            return false;
        }
        boolean access = 
                getLoggedUserServices().contains(ProfileServices.ACCION_LECTOR)
                || getLoggedUserServices().contains(ProfileServices.ACCION_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.ACCION_ENCARGADO)
                || getLoggedUserServices().contains(ProfileServices.ACCION_POR_ORGANIZACION)
            ;
        if(Utilities.getSettings().getActionManagerWhoAccept() == 0 || Utilities.getSettings().getActionManagerWhoEvaluate()== 0) {
            access = access || getLoggedUserServices().contains(ProfileServices.SPECIAL_DEPARTMENT_ATTENDANT);
        }
        return access;
    }

    private boolean hasFormulariesAccess() {
        if (!Utilities.isModuleAvailable(Module.FORMULARIE)) {
            return false;
        }
        return hasAccessMenuForms() 
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_READER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_ADJUSTMENT_APPROVER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_ADJUSTMENT_AUTHORIZER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_ANALYST_APPROVER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_CANCELATION_APPROVER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_CANCEL_AUTHORIZER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_REOPEN_APPROVER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_REOPEN_AUTHORIZER)
                || getLoggedUserServices().contains(ProfileServices.FILL_ONLY)
                || getLoggedUserServices().contains(ProfileServices.FILL_FORM)
                || getLoggedUserServices().contains(ProfileServices.REPORT_FORM)                
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_AUTHORIZER);
    }
    
    private boolean hasAccessFormulariesCatalog() {
        if (!getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)) {
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.FORM_REGISTER_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.FORM_DELETE_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.FORM_VIEW_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.FORM_REGISTER_QUERY)
                || getLoggedUserServices().contains(ProfileServices.FORM_DELETE_QUERY)
                || getLoggedUserServices().contains(ProfileServices.FORM_ENABLE_CACHE)
                || getLoggedUserServices().contains(ProfileServices.FORM_SYNC_CACHE)
                || getLoggedUserServices().contains(ProfileServices.FORM_VIEW_QUERY)
                || getLoggedUserServices().contains(ProfileServices.FORM_REGISTER_REPORT)
                || getLoggedUserServices().contains(ProfileServices.FORM_DELETE_REPORT)
                || getLoggedUserServices().contains(ProfileServices.FORM_VIEW_SYNC_EVENT)
                || getLoggedUserServices().contains(ProfileServices.FORM_ADMON_REPORT_ACCESS);
    }
    
    private boolean hasDocumentAccess() {
        if(!Utilities.isModuleAvailable(Module.DOCUMENT)){
            return false;
        }
        return hasDocumentMenuAccess()
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_READER)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_AUTHORIZER);
    }

    private boolean hasMeetingAccess() {
        if(!Utilities.isModuleAvailable(Module.MEETING)){
            return false;
        }
        return hasMeetingMenuAccess() || getLoggedUserServices().contains(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_MEETING);
    }
    
    private boolean hasPollAccess() {
        if(!Utilities.isModuleAvailable(Module.POLL)){
            return false;
        }
        return getLoggedUserServices().contains(ProfileServices.SURVEY_MANAGER)
                || getLoggedUserServices().contains(ProfileServices.SURVEY_RESPONDENT)
                || getLoggedUserServices().contains(ProfileServices.SURVEY_SUPERVISOR)
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_SURVEY_RESPONDENT)
                || getLoggedUserServices().contains(ProfileServices.REPORTE_ENCUESTA);
    }

    private boolean hasConfigurationAccess() {
        if (!Utilities.isModuleAvailable(Module.CONFIGURATION)) {
            return false;
        }
        return 
                // acceso solo corporativo
                (getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && (
                    getLoggedUserServices().contains(ProfileServices.AUDIT_QUALITY_MANAGER)
                    || getLoggedUserServices().contains(ProfileServices.ACCION_ENCARGADO)
                    || getLoggedUserServices().contains(ProfileServices.QUEJA_ENCARGADO)
                    || getLoggedUserServices().contains(ProfileServices.INDICADOR_ENCARGADO)
                    || getLoggedUserServices().contains(ProfileServices.PROYECTO_ENCARGADO)
                ))
                // accesos desde planta
                //desde otros modulos
                || (getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO) && getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO))
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT)
                || getLoggedUserServices().contains(ProfileServices.DEVICE_MANAGER)
                || getLoggedUserServices().contains(ProfileServices.INTERFACE_TRESS)
                // directo a configuracion
                || getLoggedUserServices().contains(ProfileServices.ADMON_ACCESOS)
                || getLoggedUserServices().contains(ProfileServices.BULK_ACCESS_CREATOR)
                || getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)
                || getLoggedUserServices().contains(ProfileServices.ESCALATION_MANAGER)
                // acceso a módulos de catalogos de Timesheet
                || hasAccessTimesheetCatalog()
                // acceso a módulos de catalogos de Formularios
                || hasAccessFormulariesCatalog()
                // acceso a módulos de catalogos de Actividades
                || hasAccessActivityCatalog()
                // acceso a módulos de catalogos de Timework
                || hasAccessTimeworkCatalog()
                // acceso a módulos de catalogos
                || ProfileServices.servicesByType(getLoggedUserServices(), ServicesType.CATALOG);
    }
    
    private boolean hasAuditAccess() {
        if(!Utilities.isModuleAvailable(Module.AUDIT)){
            return false;
        }
        return hasAccesMenuAudits()
            || getLoggedUserServices().contains(ProfileServices.SPECIAL_AUDIT_AUDITED)
            || getLoggedUserServices().contains(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_AUDIT);
    }

    private boolean hasActivityAccess() {
        if (!Utilities.isModuleAvailable(Module.ACTIVITY)) {
            return false;
        }
        return hasAccessMenuActivities()
                || hasAccessActivityCatalog()
                || getLoggedUserServices().contains(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT);
    }
    
    private boolean hasAccessActivityCatalog() {
        if (!getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)) {
            return false;
        }
        return  getLoggedUserServices().contains(ProfileServices.ACTIVITY_REGISTER_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_DELETE_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_VIEW_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_REGISTER_QUERY)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_DELETE_QUERY)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_ENABLE_CACHE)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_SYNC_CACHE)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_VIEW_QUERY)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_REGISTER_REPORT)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_DELETE_REPORT)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_VIEW_SYNC_EVENT)
                || getLoggedUserServices().contains(ProfileServices.ACTIVITY_ADMON_REPORT_ACCESS);
    }

    protected final Map<String,Boolean> getModuleAccessMap() {
        Map<String,Boolean> m = new HashMap<>();
        m.put(Module.AUDIT.getKey(), auditAccess);
        m.put(Module.ACTIVITY.getKey(), activityAccess);
        m.put(Module.CONFIGURATION.getKey(), configurationAccess);
        m.put(Module.POLL.getKey(), pollAccess);
        m.put(Module.MEETING.getKey(), meetingAccess);
        m.put(Module.DOCUMENT.getKey(), documentAccess);
        m.put(Module.ACTION.getKey(), actionAccess);
        m.put(Module.COMPLAINT.getKey(), complaintAccess);
        m.put(Module.METER.getKey(), meterAccess);
        m.put(Module.PROJECT.getKey(), projectAccess);
        m.put(Module.PLANNER.getKey(), plannerAccess);
        m.put(Module.DEVICE.getKey(), deviceAccess);
        m.put(Module.FORMULARIE.getKey(), formularieAccess);
        m.put(Module.FIVES.getKey(), fiveSAccess);
        m.put(Module.TIMEWORK.getKey(), timeworkAccess);
        m.put(Module.TIMESHEET.getKey(), timesheetAccess);
        return m;
    }
    
     protected boolean hasAnyModuleAccess(final String[] modules) {
        final Map<String, Boolean> moduleAccess = getModuleAccessMap();
        for (final String module : modules) {
            if (moduleAccess.containsKey(module.trim()) && moduleAccess.get(module.trim())) {
                return true;
            }
        }
        return false;
    }

    public static Logger getLogger(Class<?> c) {
        return Loggable.getLogger(c);
    }
    
    public static Logger getLogger(Loggable.LOGGER s, Class<?> c) {
        return Loggable.getLogger(s, c);
    }
    
    /**
     * @return the actualProfile
     */
    public ProfileServices[] getLoggedUserServicesArray() {
        return getLoggedUserServices().toArray(new ProfileServices[0]);
    }
    
    /**
     *
     */
    public final List<ProfileServices> getLoggedUserServices() {
        if (loggedUserServices == null) {
            loggedUserServices = getLoggedUserServices(
                getHttpSession(), getLoggedUserId(), isAdmin()
            );
        }
        return loggedUserServices;
    }
    
    public ILoggedUser getLoggedUserOfFirstAdmin() {
        final Long userId = getLoggedUserId();
        if (userId == null || userId.equals(0L)) {
            return getAdminDto();
        } else {
            return getLoggedUserDto();
        }
    }

    public ILoggedUser getAdminDto() {
        final IUserDAO dao = Utilities.getBean(IUserDAO.class);
        final Long adminId = dao.HQL_findLong(" "
                    + " SELECT MIN(c.id)"
                    + " FROM " + User.class.getCanonicalName() + " c"
                    + " WHERE c.root = 1"
        );
        return getLoggedUserDto(dao, adminId, true);
    }
    
    public final List<ProfileServices> getLoggedUserServices(HttpSession session, Long loggedUserId, boolean isAdmin) {
        return SessionHelper.getLoggedUserServices(session, loggedUserId, isAdmin);
    }
    
    public final List<ProfileServices> getLoggedUserServicesByUne(Long businessUnitId) {
        //Primero busca los servicios en la session del usuario
        if (this.getSession() != null && this.getSession().get("services_une_" + businessUnitId) != null) {
            return (List<ProfileServices>) this.getSession().get("services_une_" + businessUnitId);
        }
        getLogger().info("Framework.Action.SessionViewer.getLoggedUserServicesByUne() ... " + getLoggedUserId());
        //Si no lo encuentra carga los servicios de la base de datos
        try {
            IUserDAO daoU = getBean(IUserDAO.class);
            List<ProfileServices> services;
            if (isAdmin()) {
                services = ProfileServices.getAll();
            } else {
                services = new ArrayList<>(daoU.getUserServicesByUne(getLoggedUserId(), businessUnitId));
            }
            services.remove(ProfileServices.NONE);
            this.getSession().put("services_une_" + businessUnitId, new ArrayList(
                ProfileServicesUtil.getRemovedUnavailableModulesServices(services)
            ));
            return (List<ProfileServices>) this.getSession().get("services_une_" + businessUnitId);
        } catch (Exception e) {
            getLogger().error("Error: no se pudieron obtener los servicios a los que tiene acceso el usuario", e);
        }
        return Utilities.EMPTY_LIST;
    }
    
    public final List<ProfileServices> getLoggedUserServicesAddedBySystem() {
        if (loggedUserServicesAddedBySystem == null) {
            loggedUserServicesAddedBySystem = getLoggedUserServicesAddedBySystem(
                getSession(), getLoggedUserId(), isAdmin()
            );
        }
        return loggedUserServicesAddedBySystem;
    }
    
    public final List<ProfileServices> getLoggedUserServicesAddedBySystem(Map session, Long loggedUserId, boolean isAdmin) {
        if (session != null && session.get("servicesAddedBySystem") != null){
            List<ProfileServices> loggedUserServicesAddedBySystem = (List<ProfileServices>) session.get("servicesAddedBySystem");
            loggedUserServicesAddedBySystem.remove(ProfileServices.NONE);
            return loggedUserServicesAddedBySystem;
        }
        return Utilities.EMPTY_LIST;
    }
    
    /**
     * Lista de folders a los cuales el usuario tiene acceso el usuari logueado
     * NOTA: no se implemento como un exists por que habia casos en los que se multiplicaba el tiempo del query
     * @param dao
     * @return Lista de carpetas a las que tiene accesos el usuario logeado
    */
    public List<Long> getFolderAccess(IUntypedDAO dao){
        return SessionHelper.getFolderAccess(getLoggedUserId(), dao);
    }
    
    public List<Long> getFolderAccess(Long currentLoggedUserId, IUntypedDAO dao){
        return SessionHelper.getFolderAccess(currentLoggedUserId, dao);
    }
    

    public String getRolAdmin() {

        if(this.rolAdmin == null || this.rolAdmin.equals("-1")) {
            try {
               String rol = getSession().get("admin_general")+"";
               if("null".equals(rol)) {
                   final Long userId = getLoggedUserId();
                   if (userId == null || userId.equals(-1L) || userId.equals(0L)) {
                      this.setRolAdmin("-1");
                   } else if (Utilities.getBean(IUserDAO.class).isAdmin(userId)) {
                      this.setRolAdmin("1");
                   } else {
                      this.setRolAdmin("-1");
                   }
               } else {
                  this.setRolAdmin(rol);
               }
            } catch (IllegalStateException e) {
                this.setRolAdmin("-1");
           }
        }
        return this.rolAdmin;
    }
    
    public boolean isRenewDataNeeded() {
        Boolean renewData = RenewDataStatus.DATA_RENEWED.value();
        Boolean newRenewPassword = false;
        try {
            renewData = (Boolean) getSession().get(Login.RENEW_DATA_LOCATION);
            newRenewPassword = (Boolean) getSession().get(Login.RENEW_DATA_PASSWORD);
        } catch (Exception e) {
            getLogger().error("No fue posible comprobar si el usuario requiere renovar información", e);
        }
        if (renewData == null) {
            return true;
        }
        return renewData.equals(RenewDataStatus.RENEW_DATA.value()) || newRenewPassword;
    }

    public String getLoggedUserBusinessUnitName() {
        String businessUnitName = null;
        try {
            businessUnitName = (String) getSession().get(Login.BUSINESS_UNIT_NAME_ATTRIBUTE);
        } catch (Exception e) {
            getLogger().error("No fue posible obtener la planta a la que pertece el usuario ", e);
        }
        if(businessUnitName == null) {
            return "";
        }
        return businessUnitName;
    }
    
    protected String hasRequiredAccessByXml() {
        if (!isAdmin() && requiredAccess != null && !requiredAccess.equals("none")) {
            final String[] modules = requiredAccess.split(",");
            final Boolean hasAnyModuleAccess = hasAnyModuleAccess(modules);
            if (!hasAnyModuleAccess) {
                this.detailError = ModuleUtil.getModulesDetails(modules, getLocale());
                getLogger().error("No cuenta con acceso al modulo! {} ", requiredAccess);
                return NO_ACCESS;
            }
        }
        if (!isAdmin() && requiredServices != null && !requiredServices.equals("none")) {
            final boolean hasAccess = AccessUtil.hasAccess(requiredServices, getLoggedUserServices(), isAdmin());
            if (!hasAccess) {
                return INVALID;
            }
        }
        return SUCCESS;
    }


    public void setRolAdmin(String rolAdmin) {
        this.rolAdmin = rolAdmin;
    }

    public final boolean isAdmin() {
        this.setAdmin(getRolAdmin() == null || getRolAdmin().equals("-1") ? false : getRolAdmin().equals("1"));
        return admin;
    }

    private void setAdmin(boolean admin) {
        this.admin = admin;
    }

    private String savedObjName = "misDatos";

    public final Long getSavedLoggedUserId() {
        return loggedUserId;
    }
        
    public final Long getLoggedUserId() {
        if (loggedUserId == 0L && impersonatingUserId == null) {
            Map s = getSession();
            if (s != null) {
                String usuarioId = s.get("intusuarioid")+"";
                this.setLoggedUserId(Long.valueOf("null".equals(usuarioId) ? "0" : usuarioId));
            } else {
                loggedUserId = 0L;
            }
        } else if (impersonatingUserId != null) {
            return impersonatingUserId;
        }
        return loggedUserId;
    }
    
    public final void setLoggedUserName(String loggedUserName) {
        this.loggedUserName = loggedUserName;
    }
    public final String getLoggedUserName() {
        if(loggedUserName == null) {
            String userName = getSession().get("User") == null ? "Expired session" : getSession().get("User").toString();
            this.setLoggedUserName(userName.trim());
        }
        return this.loggedUserName;
    }

    public final String getLoggedUserMailAdress() {
        return getLoggedUserDto().getCorreo();
    }

    public boolean isCorporative(){
      if (isCorp != null){
        return isCorp;
      }
      if (this.isAdmin()) {
        //Si es un usuario con el permiso de administrador se toma como usuario corporativo
        return true;
      }
      isCorp = getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO);
      return isCorp;
    }

    @Override
    public boolean isFormularieAccess() {
        return formularieAccess;
    }

    public void setFormularieAccess(boolean formularieAccess) {
        this.formularieAccess = formularieAccess;
    }

    public void setLoggedUserId(Long loggedUserId) {
        this.loggedUserId = loggedUserId;
    }

    public boolean isModernTheme() {
        return Utilities.isModernTheme();
    }

    private UserRef getLoggedUserRef(IUntypedDAO dao) {
        if (userRef == null || userRef.getId() == 0 || !Objects.equals(getLoggedUserId(), userRef.getId())) {
            userRef = dao.HQLT_findById(UserRef.class, getLoggedUserId());
        }
        return userRef;
    }
    
    /**
     * @duplicated -duplicada-
     * @deprecated  utilizar 'getLoggedUser()' (solo es preferencia del nombre de la funcion [aquella es mas explicita])
     * @return
     */
    public User getUser() {
        if(usuario == null || !Objects.equals(getLoggedUserId(), usuario.getId())) {
            IUserDAO DAO = getBean(IUserDAO.class);
            usuario = DAO.HQLT_findById(getLoggedUserId(), true);
        }
        return usuario;
    }
    public User getUser(Long id) {
        IUserDAO DAO = getBean(IUserDAO.class);
        return DAO.HQLT_findById(id, false);
    }

    /**
     * @return : Regresa el 1er valor almacenado en "currentEntityId" y si currentEntityId no es valido regresa un "-1"
     */
    public String firstParamCurrentEntityId() {
        return (getCurrentEntityId() == null 
                || getCurrentEntityId().length == 0 
                || getCurrentEntityId()[0] == null 
                || getCurrentEntityId()[0].isEmpty() 
                ? "-1" 
                : getCurrentEntityId()[0]);
    }

    public String[] getCurrentEntityId() {
        if(currentEntityId == null || currentEntityId.length == 0){
            this.currentEntityId = Utilities.parameters("currentEntityId", getRequest());
        }
        if(currentEntityId == null){
            this.currentEntityId = new String[]{};
        }
        return currentEntityId;
    }

    public void setCurrentEntityId(String[] currentEntityId) {
        this.currentEntityId = currentEntityId;
    }

    public String getSavedObjName() {
        if(savedObjName == null || savedObjName.isEmpty() || savedObjName.equals("misDatos")){
            this.savedObjName = parameter("savedObjName", getRequest());
        }
        if(savedObjName == null || savedObjName.isEmpty()){
            this.savedObjName = "misDatos";
        }
        return savedObjName;
    }

    public void setSavedObjName(String savedObjName) {
        this.savedObjName = savedObjName;
    }
    
    @Override
    public void withSession(Map<String, Object> session) {
        this.session = session;
    }
    
    @Override
    public void withServletRequest(HttpServletRequest request) {
        this.request = request;
    }
    
    @Override
    public void withServletResponse(HttpServletResponse response) {
        this.response = response;
    }

    @Override
    public void withServletContext(ServletContext context) {
        this.servletContext = context;
    }
    
    @Override
    public void setServletContext(ServletContext context) {
        this.servletContext = context;
    }
    
    public final Map getSession() {
        if(ActionContext.getContext() != null && ActionContext.getContext().getSession() != null) {
            this.session = ActionContext.getContext().getSession();
        }
        return session;
    }

    public final HttpSession getHttpSession() {
        if(request == null && ActionContext.getContext() != null) {
            // Aquí entra normalmente desde STRUTS
            this.request = ActionContext.getContext().getServletRequest();
        }
        if (request != null) {
            return request.getSession();
        }
        // Aqui entra cuando se mezcla con Spring
        CustomWebAuthenticationDetails loggedUserAuthDetails = SecurityUtils.getLoggedUserAuthDetails();
        if (loggedUserAuthDetails == null) {
            return null;
        }
        return loggedUserAuthDetails.getSession();
    }

    @Override
    public boolean isAuditAccess() {
        getLogger().trace("has access to audit {}",auditAccess);
        return auditAccess;
    }

    public void setAuditAccess(boolean auditAccess) {
        this.auditAccess = auditAccess;
    }
    
    @Override
    public boolean isActivityAccess() {
        getLogger().trace("has access to activity {}",activityAccess);
        return activityAccess;
    }

    public void setActivityAccess(boolean activityAccess) {
        this.activityAccess = activityAccess;
    }

    @Override
    public boolean isConfigurationAccess() {
        getLogger().trace("has access to configuration {}",configurationAccess);
        return configurationAccess;
    }

    public void setConfigurationAccess(boolean configurationAccess) {
        this.configurationAccess = configurationAccess;
    }

    @Override
    public boolean isMeetingAccess() {
        
        getLogger().trace("has access to meetings {}",meetingAccess);
        return meetingAccess;
    }

    public void setMeetingAccess(boolean meetingAccess) {
        this.meetingAccess = meetingAccess;
    }

    @Override
    public boolean isDocumentAccess() {
        getLogger().trace("has access to document {}",documentAccess);
        return documentAccess;
    }

    public void setDocumentAccess(boolean documentAccess) {
        this.documentAccess = documentAccess;
    }

    @Override
    public boolean isActionAccess() {
        getLogger().trace("has access to actions {}",actionAccess);
        return actionAccess;
    }

    public void setActionAccess(boolean actionAccess) {
        this.actionAccess = actionAccess;
    }

    @Override
    public boolean isPollAccess() {
        getLogger().trace("has access to polls {}",pollAccess);
        return pollAccess;
    }

    public void setPollAccess(boolean pollAccess) {
        this.pollAccess = pollAccess;
    }

    @Override
    public boolean isComplaintAccess() {
        getLogger().trace("has access to complaints {}",complaintAccess);
        return complaintAccess;
    }

    public void setComplaintAccess(boolean complaintAccess) {
        this.complaintAccess = complaintAccess;
    }

    @Override
    public boolean isTimesheetAccess() {
        getLogger().trace("has access to timeheet {}",timesheetAccess);
        return timesheetAccess;
    }

    public void setTimesheetAccess(boolean timesheetAccess) {
        this.timesheetAccess = timesheetAccess;
    }    

    @Override
    public boolean isMeterAccess() {
        getLogger().trace("has access to metrics {}",meterAccess);
        return meterAccess;
    }

    public void setMeterAccess(boolean meterAccess) {
        this.meterAccess = meterAccess;
    }

    @Override
    public boolean isProjectAccess() {
        getLogger().trace("has access to project {}",projectAccess);
        return projectAccess;
    }

    public void setProjectAccess(boolean projectAccess) {
        this.projectAccess = projectAccess;
    }

    @Override
    public boolean isPlannerAccess() {
        return plannerAccess;
    }

    public void setPlannerAccess(boolean plannerAccess) {
        this.plannerAccess = plannerAccess;
    }

    @Override
    public boolean isDeviceAccess() {
        getLogger().trace("has access to devices {}",deviceAccess);
        return deviceAccess;
    }

    public void setDeviceAccess(boolean deviceAccess) {
        this.deviceAccess = deviceAccess;
    }
    @Override
    public boolean isFiveSAccess() {
        getLogger().trace("has access to 5S {}",fiveSAccess);
        return fiveSAccess;
    }

    public void setFiveSAccess(boolean fiveSAccess) {
        this.fiveSAccess = fiveSAccess;
    }

    @Override
    public boolean isTimeworkAccess() {
        return timeworkAccess;
    }

    public void setTimeworkAccess(boolean timeworkAccess) {
        this.timeworkAccess = timeworkAccess;
    }

    public String getActiveServices() {
        return activeServices;
    }

    public void setActiveServices(String activeServices) {
        setServiciosActivos(Utilities.getActiveServices(activeServices));
        this.activeServices = activeServices;
    }

    public ProfileServices[] getServiciosActivos() {
        if(serviciosActivos == null) {
            serviciosActivos = new ProfileServices[]{ProfileServices.NONE};
        }
        getLogger().trace("Framework.Action.SessionViewer.getServiciosActivos() ... "+serviciosActivos.length+" - "+getActiveServices());
        return serviciosActivos;
    }

    public void setServiciosActivos(ProfileServices[] serviciosActivos) {
        this.serviciosActivos = serviciosActivos;
    }

    @Override
    public final Locale getLocale() {
        if (getSession() != null 
                && getSession().get("lang") != null
                && getSession().get("locale") != null) {
            return new Locale(getSession().get("lang").toString(), getSession().get("locale").toString());
        }
        return Utilities.getLocale();
    }
    public String getLoggedUserLocale() {
        if (getSession().get("locale") != null) {
            return getSession().get("locale").toString();
        }
        return Utilities.getSettings().getLocale();
    }
    
    public String getLoggedUserLang() {
        if (getSession().get("lang") != null) {
            return getSession().get("lang").toString();
        }
        return Utilities.getSettings().getLang();
    }

    public boolean isAccesMenuAudits() {
        getLogger().trace("has access to audit {}",accesMenuAudits);
        return accesMenuAudits;
    }

    public void setAccesMenuAudits(boolean accesMenuAudits) {
        this.accesMenuAudits = accesMenuAudits;
    }

    public boolean isAccessMenuMeter() {
        getLogger().trace("has access to meter {}",accessMenuMeter);
        return accessMenuMeter;
    }

    public void setAccessMenuMeter(boolean accessMenuMeter) {
        this.accessMenuMeter = accessMenuMeter;
    }

    public boolean isAccessMenuForms() {
        getLogger().trace("has access to Formularie {}",accessMenuForms);
        return accessMenuForms;
    }

    public void setAccessMenuForms(boolean accessMenuForms) {
        this.accessMenuForms = accessMenuForms;
    }

    public boolean isAccessMenuActivities() {
        return accessMenuActivities;
    }

    public boolean isDocumentMenuAccess() {
        getLogger().trace("has access to Document {}",documentMenuAccess);
        return documentMenuAccess;
    }

    public void setDocumentMenuAccess(boolean documentMenuAccess) {
        this.documentMenuAccess = documentMenuAccess;
    }
    
    public final LoggedUser getLoggedUserDto() {
        IUntypedDAO dao = getUntypedDAO();
        return getLoggedUserDto(dao);
    }
    
    public final LoggedUser getLoggedUserDto(IUntypedDAO dao) {
        if (loggedUserDto == null || loggedUserDto.getId() == 0 || !Objects.equals(getLoggedUserId(), loggedUserDto.getId())) {
            loggedUserDto = getLoggedUserDto(dao, getHttpSession(), getLoggedUserId(), isAdmin());
        }
        return loggedUserDto;
    }

    public final LoggedUser getLoggedUserDto(IUntypedDAO dao, Long loggedUserId, boolean isAdmin) {
        if (ActionContext.getContext() == null || ActionContext.getContext().getSession() == null) {
            return getLoggedUserDto(dao, null, loggedUserId, isAdmin);
        }
        return getLoggedUserDto(dao, getHttpSession(), loggedUserId, isAdmin);
    }

    public final LoggedUser getLoggedUserDto(IUntypedDAO dao, HttpSession session, Long loggedUserId, boolean isAdmin) {
        LoggedUser userDto = new LoggedUser();
        userDto.setAdmin(isAdmin);
        UserRef user = getLoggedUserRef(dao);
        if (user != null) {
            userDto.setUser(user);
            userDto.setCorreo(user.getCorreo());
            userDto.setAccount(user.getAccount());
            userDto.setDescription(user.getDescription());
            userDto.setBusinessUnitDepartmentId(user.getBusinessUnitDepartmentId());
            userDto.setLocale(UserUtils.parseLocale(user));
            userDto.setVersion(user.getVersion());
            userDto.setLogin(user.getLogin());
            if (user.getTimezone() != null && !user.getTimezone().isEmpty()) {
                userDto.setTimezone(user.getTimezone());
            } else {
                userDto.setTimezone(Utilities.getSettings().getTimeZone());
            }
            userDto.setCode(user.getCode());
        } else {
            userDto.setLocale(Utilities.getLocale());
        }
        userDto.setId(loggedUserId);
        userDto.setServices(
            getLoggedUserServices(session, loggedUserId, isAdmin)
        );
        return userDto;
    }

    public SessionHelper getSessionHelper() {
        return sessionHelper;
    }

    public boolean isEscalationManager() {
        return getLoggedUserServices().contains(ProfileServices.ESCALATION_MANAGER);
    }

    public boolean isSpecialDepartmenAttendant() {
        return getLoggedUserServices().contains(ProfileServices.SPECIAL_DEPARTMENT_ATTENDANT);
    }
    
    public boolean isActionsBoss() {
        return getLoggedUserServices().contains(ProfileServices.ACCION_ENCARGADO);
    }
    
    public void setReloadLicense(Boolean reloadLicense) {
        this.reloadLicense = reloadLicense;
    }
    
    public String getNewLicenseClass() {
        return newLicenseClass;
    }   

    public String getLoggedUserAccount() {
        if(loggedUserAccount == null) {
            String userName = getSession().get("accountName") == null ? "Expired session" : getSession().get("vchnombre").toString();
            this.loggedUserAccount = userName.trim();
        }
        return loggedUserAccount;
    }

    public void setLoggedUserAccount(String loggedUserAccount) {
        this.loggedUserAccount = loggedUserAccount;
    }

   public String getRequiredServices() {
        return requiredServices;


    }



    public String getRequiredAccess() {
        return requiredAccess;
    }    

    public void setRequiredAccess(String requiredAccess) {
        this.requiredAccess = requiredAccess;
    }

    public void setRequiredServices(String requiredServices) {
        this.requiredServices = requiredServices;
    }
    
    public Boolean getRenewDataLocation() {
        if(this.renewDataLocation == null) {
            Boolean renewData = (Boolean) getSession().get(Login.RENEW_DATA_LOCATION);
            this.renewDataLocation = renewData;
        }
        return this.renewDataLocation;
    }

    public Boolean getRenewPassword() {
        this.renewPassword = (Boolean) getSession().get(Login.RENEW_DATA_PASSWORD);
        return this.renewPassword;
    }
    
    public void setImpersonatingUserId(Long impersonatingUserId) {
        this.impersonatingUserId = impersonatingUserId;
    }
    
    public Long getImpersonatingUserId() {
        return this.impersonatingUserId;
    }
    public void resetUserValues(){
        this.impersonatingUserId = null;
        this.userRef = null;
        this.loggedUserDto = null;
    }
    
    private boolean hasAccessTimeworkCatalog() {
        if (!getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)) {
            return false;
        }
        return  getLoggedUserServices().contains(ProfileServices.TIMEWORK_VIEW_REPORT)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_CREATE_REPORT)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_DELETE_REPORT)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_DELETE_QUERY)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_VIEW_QUERY)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_VIEW_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_DELETE_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_CREATE_CONNECTION)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_ADMON_REPORT_ACCESS)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_ENABLE_CACHE)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_SYNC_CACHE)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_VIEW_SYNC_CACHE_EVENT)
                || getLoggedUserServices().contains(ProfileServices.TIMEWORK_CREATE_QUERY);
    }
    
    public Boolean getGeolocationAccessHistory() {
        return geolocationAccessHistory;
    }

    public void setGeolocationAccessHistory(Boolean geolocationAccessHistory) {
        this.geolocationAccessHistory = geolocationAccessHistory;
    }

    public String getDetailError() {
        return detailError;
    }

    public void setDetailError(String detailError) {
        this.detailError = detailError;
    }
}
