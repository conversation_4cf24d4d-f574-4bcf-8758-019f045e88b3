package liquibase.integration.spring;

import java.sql.Connection;
import java.sql.SQLException;
import java.text.DateFormat;
import liquibase.Liquibase;
import liquibase.command.core.ListLocksCommandStep;
import liquibase.database.Database;
import liquibase.database.DatabaseConnection;
import liquibase.database.DatabaseFactory;
import liquibase.database.jvm.JdbcConnection;
import liquibase.exception.LiquibaseException;
import liquibase.lockservice.DatabaseChangeLogLock;
import liquibase.resource.ClassLoaderResourceAccessor;
import mx.bnext.core.util.BuildInfo;
import mx.bnext.core.util.Loggable;

/**
 *
 * <AUTHOR>
 */
public class BnextSpringLiquibase extends SpringLiquibase implements ISpringLiquibase {

    private static final org.slf4j.Logger LOGGER = Loggable.getLogger(BnextSpringLiquibase.class);
    private Boolean databaseReady = false;
    
    private BuildInfo buildInfo;

    @Override
    public void afterPropertiesSet() throws LiquibaseException {
        synchronized (this) {
            if (shouldRun) {
                releasePreviousLocks();
            }
            super.afterPropertiesSet();
            databaseReady = true;
            this.notifyAll();
        }
    }

    public Boolean getDatabaseReady() {
        return databaseReady;
    }

    private void releasePreviousLocks() throws LiquibaseException {
        try (final Connection connection = getDataSource().getConnection()) {
            final Liquibase liquibase = createLiquibase(connection);
            if (liquibase == null) {
                LOGGER.error("Could not verify if liquidbase is locked as could not establish a database connection.");
                return;
            }
            final DatabaseChangeLogLock[] locks = ListLocksCommandStep.listLocks(liquibase.getDatabase());
            if (locks != null && locks.length > 0) {
                LOGGER.error("Bnext QMS was not shutdown properly. Liquidbase is locked.");
                DatabaseConnection dbConnection = liquibase.getDatabase().getConnection();
                LOGGER.error(
                        "Bnext QMS database is locked  for login [{}] and URL [{}].",
                        dbConnection.getConnectionUserName(),
                        dbConnection.getURL());
                for (DatabaseChangeLogLock lock : locks) {
                    LOGGER.error(
                            " Releasing locked of user [{}] that was created at [{}].",
                            lock.getLockedBy(),
                            DateFormat.getDateTimeInstance().format(lock.getLockGranted()));
                }
                liquibase.forceReleaseLocks();
            }
        } catch (SQLException e) {
            LOGGER.error("Failed to validate lock for Liquidbase.", e);
        }

    }

    /**
     * Ejemplo db/db.incremental2.12.1.xml
     * Nota: JRebel no actualiza en build estos archivos
     * @param xmlPath XML file path
     */
    @Override
    public void runUpdates(String xmlPath) {
        try (
                Connection connection = getDataSource().getConnection();
                Database database = DatabaseFactory.getInstance().findCorrectDatabaseImplementation(new JdbcConnection(connection))
        ) {
            Liquibase liquibase = new liquibase.Liquibase(xmlPath, new ClassLoaderResourceAccessor(), database);
            performUpdate(liquibase);
        } catch (LiquibaseException | SQLException ex) {
            LOGGER.error("Failed to run Liquidbase updates for {}.", xmlPath, ex);
        }
    }

    public BuildInfo getBuildInfo() {
        return buildInfo;
    }

    public void setBuildInfo(BuildInfo buildInfo) {
        this.buildInfo = buildInfo;
    }
    
}
