/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package liquibase.sqlgenerator.ext;

import liquibase.database.Database;
import liquibase.database.core.OracleDatabase;
import liquibase.sql.Sql;
import liquibase.sql.UnparsedSql;
import liquibase.sqlgenerator.SqlGeneratorChain;
import liquibase.sqlgenerator.core.ModifyDataTypeGenerator;
import liquibase.statement.core.ModifyDataTypeStatement;

/**
 *
 * <AUTHOR>
 */
public class ModifyDataTypeSqlGenerator extends ModifyDataTypeGenerator {

    public ModifyDataTypeSqlGenerator() {
    }

    @Override
    public int getPriority() {
        return HelperMethods.B_NEXT_PRIORITY;
    }

    @Override
    public Sql[] generateSql(ModifyDataTypeStatement statement, Database database, SqlGenerator<PERSON>hain sqlGeneratorChain) {
        Sql[] sqls = super.generateSql(statement, database, sqlGeneratorChain); 
        if(database instanceof OracleDatabase){
            this.OracleParse(sqls);
        }
        return sqls;
    }
    
    public void OracleParse(Sql[] s){
        String sql;
        for (int i = 0; i < s.length; i++) {
            sql = s[i].toSql();
            sql = HelperMethods.varcharToOracle(sql);
            if (!sql.equals(s[i].toSql())) {
                s[i] = new UnparsedSql(
                        sql,
                        HelperMethods.getDbObjects(s[i].getAffectedDatabaseObjects())
                );
            }

        }
    }
    
}
