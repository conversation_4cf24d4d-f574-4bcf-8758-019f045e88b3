package bnext.reference.device;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "service_result")
@Immutable
public class ServiceResultRef implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String description;

    public ServiceResultRef() {
    }

    @Id
    @Column(name = "service_result_id")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @LocalizedField
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String descripcion) {
        this.description = descripcion;
    }
}
