package bnext.licensing;

import Framework.Config.Utilities;
import java.util.List;
import mx.bnext.licensing.License;
import mx.bnext.licensing.Schema;

class CurrentLicense {

    private static final LicenseInfo INFO = new LicenseInfo();

    public static void reset() {
        INFO.reset();
    }

    public static void invalidate() {
        INFO.setValid(false);
    }

    public static void renew(License newLicense) {
        INFO.setValid(true);
        INFO.setLicense(newLicense);
    }

    public static String getError() {
        return INFO.getError();
    }

    public static void setError(String error) {
        INFO.setError(error);
    }

    public static License getValue() {
        return INFO.getLicense();
    }

    public static boolean isValid() {
        return INFO.getValid();
    }

    public static List<Schema> getSchemas() {
        if (INFO.getLicense() == null) {
            return Utilities.EMPTY_LIST;
        }
        final List<Schema> schemas = INFO.getLicense().getSchemas();
        if (schemas == null) {
            return Utilities.EMPTY_LIST;
        }
        return schemas;
    }

    private CurrentLicense() {
    }

}
