package bnext.licensing;

import java.io.Serializable;
import java.util.Objects;
import mx.bnext.licensing.License;

/**
 *
 * <AUTHOR>
 */
public class LicenseInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private License license;
    private Boolean valid;
    private String error;
    
    /**
     * @return the license
     */
    public License getLicense() {
        return license;
    }

    /**
     * @param license the license to set
     */
    public void setLicense(License license) {
        this.license = license;
    }

    /**
     * @return the valid
     */
    public Boolean getValid() {
        return valid;
    }

    /**
     * @param valid the valid to set
     */
    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    /**
     * @return the error
     */
    public String getError() {
        return error;
    }

    /**
     * @param error the error to set
     */
    public void setError(String error) {
        this.error = error;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + Objects.hashCode(this.license);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final LicenseInfo other = (LicenseInfo) obj;
        return Objects.equals(this.license, other.license);
    }

    @Override
    public String toString() {
        return "LicenseInfo{" + "license=" + license + ", valid=" + valid + ", error=" + error + '}';
    }

    public void reset() { 
        this.license = null;
        this.valid = false;
        this.error = "";
    }
    
}
