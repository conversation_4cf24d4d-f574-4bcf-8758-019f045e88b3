package bnext.util;

import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import bnext.resources.DynamicCssService;
import bnext.resources.PwaService;
import javax.servlet.ServletContext;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import qms.framework.util.AboutApp;
import qms.framework.util.AboutAppUtils;
import qms.framework.util.SettingsUtil;

/**
 * <AUTHOR>
 */
public class AngularAppService {

    private static final Logger LOGGER = Loggable.getLogger(Loggable.LOGGER.STARTUP, AngularAppService.class);

    public static boolean initialize(final ServletContext servletContext) {
        try {
            final boolean appResult = initializeAppName(servletContext);
            final boolean dynResult = initializeDynamicCssService(servletContext);
            //TODO; Corregir sha512 de manifest.webmanifest en ngsw.json
            final boolean pwaResult = initializePwaService(servletContext);
            boolean result = appResult && dynResult && pwaResult;
            if (!result) {
                LOGGER.error("\033[31m Could not initialize angular app service configs,"
                        + " colors, icons and fonts from Angular wont works as expected ----- \r\n");
            }
            return result;
        } catch (Exception ex) {
            LOGGER.error("\033[31m Could not rebuild angular config ifles ----- \r\n", ex);
            return false;
        }
    }

    private static boolean initializeAppName(ServletContext servletContext) {
        if (servletContext == null) {
            LOGGER.error(
                    "\033[31m Could not initlaize app name for angular configs,"
                            + " servlet context not defined ----- \r\n"
            );
            return false;
        }
        final String appMame = servletContext.getContextPath();
        if (appMame == null || appMame.isEmpty()) {
            return false;
        }
        final String safeAppName = appMame
                .replaceAll("\\\\", "")
                .replaceAll("/", "");
        AboutApp.setAppName(safeAppName);
        return true;
    }


    private static boolean initializeDynamicCssService(final ServletContext context) {
        final Settings settings = Utilities.getSettings(context);
        final String systemColor = settings.getSystemColor();
        final String systemSecondaryColor = settings.getSystemSecondaryColor();
        final DynamicCssService dynamicCssService = Utilities.getBeanFactory(context).getBean(DynamicCssService.class);
        return dynamicCssService.generateAndOverwriteAngularCSS(systemColor, systemSecondaryColor);
    }

    private static boolean initializePwaService(final ServletContext context) {
        final Settings settings = Utilities.getSettings(context);
        final PwaService service = Utilities.getBeanFactory(context).getBean(PwaService.class);
        final boolean pwaResult = service.overridePwaManifest(
                settings.getSystemId(),
                SettingsUtil.getAppUrlNoSlash(),
                settings.getSystemColor(),
                settings.getLang()
        );
        final boolean ngswResult = service.overrideNsgw(AboutApp.getAppName());
        return pwaResult && ngswResult;
    }

    public static String cleanPropertyMavenName(String buildVersion) {
        return AboutAppUtils.cleanPropertyMavenName(buildVersion);
    }
}
