package bnext.login.config;

import bnext.login.dto.ClientBrowserDTO;
import bnext.login.dto.ScreenResolutionDTO;
import bnext.login.util.ClientBrowserUtils;
import com.google.gson.Gson;
import java.net.URLDecoder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import mx.bnext.core.util.Loggable;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import qms.framework.entity.GeolocationCoordinates;

/**
 *
 * <AUTHOR>
 */
public class CustomWebAuthenticationDetails extends WebAuthenticationDetails {

    private final GeolocationCoordinates location;
    private final ScreenResolutionDTO screenResolution;
    private final String token;
    private final ClientBrowserDTO browser;
    private final HttpSession session;

    public CustomWebAuthenticationDetails(final HttpServletRequest request) {
        super(request);
        token = request.getParameter("token");
        location = loadLocation(request);
        screenResolution = loadScreenResolution(request);
        this.browser = ClientBrowserUtils.parseBrowser(request, true);
        this.session = request.getSession();
    }

    private GeolocationCoordinates loadLocation(final HttpServletRequest request) {
        try {
            final String locationJson = request.getParameter("location");
            if (locationJson != null && !locationJson.isEmpty()) {
                return new Gson().fromJson(URLDecoder.decode(locationJson, "UTF-8"), GeolocationCoordinates.class);
            } else {
                return null;
            }
        } catch (final Exception e) {
            Loggable.getLogger(CustomWebAuthenticationDetails.class).error("Failed to parse location {}.", request.getParameter("location"));
            return null;
        }
    }

    private ScreenResolutionDTO loadScreenResolution(final HttpServletRequest request) {
        try {
            final String screenResolutionJson = request.getParameter("screenResolution");
            if (screenResolutionJson != null && !screenResolutionJson.isEmpty()) {
                return new Gson().fromJson(URLDecoder.decode(screenResolutionJson, "UTF-8"), ScreenResolutionDTO.class);
            } else {
                return null;
            }
        } catch (final Exception e) {
            Loggable.getLogger(CustomWebAuthenticationDetails.class).error("Failed to parse screen resolution {}.", request.getParameter("screenResolution"));
            return null;
        }
    }


    public HttpSession getSession() {
        return session;
    }

    public String getToken() {
        return token;
    }

    public ClientBrowserDTO getBrowser() {
        return browser;
    }

    public GeolocationCoordinates getLocation() {
        return location;
    }

    public ScreenResolutionDTO getScreenResolution() {
        return screenResolution;
    }
}
