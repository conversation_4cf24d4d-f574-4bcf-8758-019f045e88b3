package bnext.login.ldap;

import com.sun.jna.platform.win32.Win32Exception;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import mx.bnext.core.util.Loggable;
import waffle.servlet.spi.NegotiateSecurityFilterProvider;
import waffle.servlet.spi.SecurityFilterProvider;
import waffle.windows.auth.IWindowsAuthProvider;
import waffle.windows.auth.IWindowsIdentity;

/**
 *
 * <AUTHOR>
 */
public class BnextSecurityFilterProvider extends NegotiateSecurityFilterProvider implements SecurityFilterProvider {
    private static final Logger LOGGER = Loggable.getLogger(NegotiateSecurityFilterProvider.class);
    private static final String BHEAD = "n";

    public BnextSecurityFilterProvider(IWindowsAuthProvider newAuthProvider) {
        super(newAuthProvider);
    }

    @Override
    public boolean isPrincipalException(final HttpServletRequest request) {
        if (super.isPrincipalException(request)) {
            LOGGER.warn("Skipped principal exception! RequestURI: {}, QueryString: {}", new Object[] {
                request.getRequestURI(), request.getQueryString()
            });
        }
        return false;
    }
    
    
    @Override
    public IWindowsIdentity doFilter(final HttpServletRequest request, final HttpServletResponse response) throws IOException {
        try {
            LOGGER.debug("Provider authentication test {} {}", new Object[] {
                request.getRequestURI(), request.getQueryString()
            });
            return super.doFilter(request, response);
        } catch (Win32Exception | IOException e) {
            String again = request.getParameter(BHEAD);
            LOGGER.debug("Provider authentication test failed again: '{}' '{}'", new Object[] {
                again, e.getClass(), e.getMessage()
            });
            if (e instanceof Win32Exception || e.getMessage().contains("Win32Exception")) {
                if ("1".equals(again)) {
                    throw new RuntimeException("LDAP ERROR INVALID_CREDENTIALS " + e.getMessage());
                }
            }
            throw new IOException(e);
        }
    }
    
    
}
