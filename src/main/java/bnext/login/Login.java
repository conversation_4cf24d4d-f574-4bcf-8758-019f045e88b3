package bnext.login;

import DPMS.ActiveDirectoryInterface.ActiveDirectory;
import DPMS.ActiveDirectoryInterface.ActiveDirectoryUtil;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.AccessHistory;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.IUserLdap;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import DPMS.Mapping.User_old;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.dto.LocaleDTO;
import bnext.dto.old.TblUsuarioDTO;
import bnext.licensing.LicenseUtil;
import bnext.login.core.IUserDetail;
import bnext.login.dto.ClientBrowserDTO;
import bnext.login.dto.LoginDTO;
import bnext.login.dto.ScreenResolutionDTO;
import bnext.login.dto.ServicesDTO;
import bnext.login.ldap.BnextNegotiateSecurityFilter;
import bnext.login.ldap.BnextPrincipal;
import bnext.login.util.ClientBrowserUtils;
import bnext.login.util.LoginError;
import bnext.login.util.OidcHelper;
import bnext.login.util.OidcProvider;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import com.opensymphony.xwork2.Action;
import isoblock.common.Properties;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchResult;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.cipher.RC4;
import mx.bnext.core.util.Loggable;
import org.slf4j.Logger;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import qms.access.logic.FilterUserHelper;
import qms.access.logic.SessionHelper;
import qms.framework.dao.ILicenseUserDAO;
import qms.framework.dto.UserCertificateDTO;
import qms.framework.entity.GeolocationCoordinates;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.ITokenProvider;
import qms.framework.security.TokenDetails;
import qms.framework.security.UserLogin;
import qms.framework.util.LoginSource;
import qms.framework.util.SessionFilterHandler;
import qms.framework.util.UserCreationSource;

/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
public class Login {

    public static final String LOGGED = "logged";
    public static final String SUCCESS = Action.SUCCESS;
    public static final String DUPLICATE = "duplicate";
    public static final String RENEW_DATA_LOCATION = "renewDataLocation";
    public static final String RENEW_DATA_PASSWORD = "renewDataPassword";
    public static final String RENEW_DATA_TIMEZONE = "renewDataTimezone";
    public static final String DEPARTMENT_ID_ATTRIBUTE = "departmentId";
    public static final String BUSINESS_UNIT_ID_ATTRIBUTE = "businessUnitId";
    public static final String BUSINESS_UNIT_NAME_ATTRIBUTE = "businessUnitName";
    public static final String BUSINESS_UNIT_DEPARTMENT_ID_ATTRIBUTE = "businessUnitDepartmentId";
    public static final String LEGACY_OPEN_FILTERS_ATTRIBUTE = "legacyOpenFilters";
    public static final String BROWSER_ATTRIBUTE = "browser";
    public static final String BROWSER_OS_ATTRIBUTE = "browserOs";
    public static final String BROWSER_OS_VERSION_ATTRIBUTE = "browserOsVersion";
    public static final String IP_ADDRESS_ATTRIBUTE = "ipAddress";
    public static final String ANONYMOUS_ATTRIBUTE = "anonymous";
    private static final String ACCOUNT_ATTRIBUTE = "vchnombre";
    private static final long MAXMIUM_LIFE_LDAP_TOKEN_MS = 1000 * 60 * 15L;
    public static final String TIMESHEET_DATASOURCE_REFRESH = "timesheetDataSourceRefresh";

    public enum LOGIN_STATUS {
        LOGGED(Login.LOGGED),
        NEW(Login.LOGGED),
        SUCCESS(Login.SUCCESS),
        ERROR(Action.ERROR),
        DUPLICATE(Login.DUPLICATE);
        private final String value;

        LOGIN_STATUS(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    private static final Logger LOGGER = Loggable.getLogger(Login.class);

    private final String url;    
    private final HttpServletRequest request;
    private final IUserDAO dao;
    private final boolean ldapEncryption;
    private final LoginDTO userInfo;
    private final UserLogin loginInfo;
    private LoginError error = null;
    private boolean valid = false;
    private boolean confirmCloseSession = false;
    private String token = "";
    private LOGIN_STATUS loginStatus = LOGIN_STATUS.SUCCESS;
    private static final SessionHelper SESSION_HELPER = new SessionHelper();
    private Long expiredLdapTokenMs;

    public Login(String url, LoginDTO userInfo, String token, HttpServletRequest request, IUserDAO dao) {
        LOGGER.trace("Login instance (url, userInfo, token, request, dao) by uri: '{}'", request.getRequestURI());
        this.url = url;
        this.userInfo = userInfo;
        this.dao = dao;
        this.request = request;
        this.token = token;
        this.ldapEncryption = false;
        this.loginInfo = new UserLogin();
    }

    public Login(HttpServletRequest request, String account) {
        LOGGER.trace("Login instance (request, account) by uri: '{}'", request.getRequestURI());
        this.request = request;
        this.ldapEncryption = false;
        this.userInfo = new LoginDTO(account);
        this.url = "";
        this.dao = null;
        this.loginInfo = new UserLogin();
    }
    
    public Login(HttpServletRequest request, IUserDAO dao, BnextPrincipal principal) {
        LOGGER.trace("Login instance (request, dao, principal) by uri: '{}'", request.getRequestURI());
        this.request = request;
        this.ldapEncryption = false;
        this.userInfo = new LoginDTO(principal);
        this.url = "";
        this.dao = dao;
        this.loginInfo = new UserLogin();
    }

    public Login(HttpServletRequest request, IUserDAO dao) {
        LOGGER.trace("Login instance (request, dao) by uri: '{}'", request.getRequestURI());
        this.dao = dao;
        this.request = request;
        this.userInfo = new LoginDTO();
        this.ldapEncryption = false;
        this.url = "";
        this.loginInfo = new UserLogin();
    }

    public Login(String token, HttpServletRequest request, IUserDAO dao) {
        LOGGER.trace("Login instance (token, request, dao) by uri: '{}'", request.getRequestURI());
        this.token = token;
        this.dao = dao;
        this.request = request;
        this.userInfo = new LoginDTO();
        this.ldapEncryption = false;
        this.url = "";
        this.loginInfo = new UserLogin();
    }
    
    public Login(String token, HttpServletRequest request, boolean ldapEncryption, IUserDAO dao) {
        LOGGER.trace("Login instance (token, request, dao) by uri: '{}'", request.getRequestURI());
        this.token = token;
        this.dao = dao;
        this.request = request;
        this.userInfo = new LoginDTO();
        this.ldapEncryption = ldapEncryption;
        this.url = "";
        this.loginInfo = new UserLogin();
    }

    public Login(String url, String token, HttpServletRequest request, IUserDAO dao) {
        LOGGER.trace("Login instance (url, token, request, dao) by uri: '{}'", request.getRequestURI());
        this.token = token;
        this.dao = dao;
        this.request = request;
        this.userInfo = new LoginDTO();
        this.ldapEncryption = false;
        this.url = "";
        this.loginInfo = new UserLogin();
    }

    public Login(String url, String token, HttpServletRequest request, boolean ldapEncryption, IUserDAO dao) {
        LOGGER.trace("Login instance (url, token, request, legacyEncription, dao) by uri: '{}'", request.getRequestURI());
        this.token = token;
        this.dao = dao;
        this.request = request;
        this.userInfo = new LoginDTO();
        this.ldapEncryption = ldapEncryption;
        this.url = "";
        this.loginInfo = new UserLogin();
    }

    public String getUser() {
        return userInfo.getAccount();
    }

    public boolean validate() throws IOException {
        if (confirmCloseSession) {
            return processToken(true);
        } else if (ldapEncryption) {
            processLdapToken();
            final boolean validLdap = error == null || LoginError.NONE.equals(error);
            if (validLdap) {
                return validateConcurrentSessions();
            }
            return false;
        } else if (token != null && !token.isEmpty()) {
            return processToken(false);
        } else {
            return false;
        }
    }

    private boolean validateConcurrentSessions() throws IOException {
        if (confirmCloseSession) {
            return true;
        }
        loadLoginInfo();
        final boolean previousValidSession = hasPreviousValidSession(loginInfo.getActiveSession());
        final boolean activeSession = loginInfo.hasActiveSession();
        if (activeSession && previousValidSession) {
            return requestConfirmationCloseSession();
        }
        return true;
    }

    private void loadLoginInfo() {
        if (loginInfo.getId() == null) {
            final Long userId = getUserId();
            loginInfo.setId(userId);
        }
    }

    private Long getUserId() {
        return dao.HQL_findSimpleLong(" "
                + " SELECT c.id "
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE c.cuenta = :usr",
                "usr", userInfo.getAccount());
    }
    
    public UserRef initializeSession() {
        HttpSession session;
        if (ActiveDirectoryUtil.isSsoActive()) {
            session = request.getSession();
        } else {
            loadLoginInfo();
            final boolean previousValidSession = hasPreviousValidSession(loginInfo.getActiveSession());
            final boolean activeSession = loginInfo.hasActiveSession();
            if (activeSession && previousValidSession) {
                SESSION_HELPER.closeSession(loginInfo.getActiveSession(), dao);
            } else {
                loginInfo.removeSession(previousValidSession);
            }
            SESSION_HELPER.resetSession(request, dao);
            session = request.getSession(true);   
        }
        TblUsuarioDTO userData = getUserData();
        createLoginHistory(null, request, session, loginInfo, null, null, this.ldapEncryption, dao);
        return initializeSessionLegacyAttributes(session, userData, this.userInfo, dao, this.ldapEncryption);
    }
    
    public static void initializeSession(
            final Authentication authentication,
            final HttpServletRequest request,
            final TblUsuarioDTO userData,
            final GeolocationCoordinates location,
            final ScreenResolutionDTO screenResolution,
            final Boolean ldap
    ) {
        final Long userId = Long.valueOf(userData.getUsuarioid());
        final UserLogin loginInfo = new UserLogin(userId);
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        HttpSession session = request.getSession();
        initializeSessionLegacyAttributes(session, userData, null, dao, ldap);
        createLoginHistory(authentication, request, session, loginInfo, location, screenResolution, ldap, dao);
    }

    private static void createLoginHistory(
            final Authentication authentication,
            final HttpServletRequest request,
            final HttpSession session, 
            final UserLogin loginInfo, 
            final GeolocationCoordinates location,
            final ScreenResolutionDTO screenResolution,
            final Boolean ldap,
            final IUntypedDAO dao
    ) {
        final LoginSource loginSource;
        final OidcProvider oidcProvider;
        final String oidcId;
        if (ldap) {
            loginSource = LoginSource.LDAP;
            oidcProvider = null;
            oidcId = null;
        } else if (OidcHelper.isOidcLogin(authentication)) {
            loginSource = LoginSource.OIDC;
            oidcProvider = OidcProvider.parseValue(Utilities.getSettings().getOidcProvider());
            oidcId = ((OidcUser)authentication.getPrincipal()).getAttribute("sub");
        } else {
            loginSource = LoginSource.QMS_LOGIN;
            oidcProvider = null;
            oidcId = null;
        }
        final ClientBrowserDTO browser = ClientBrowserUtils.parseBrowser(request, true);
        final IUserDetail principal = (IUserDetail) authentication.getPrincipal();
        final AccessHistory accessHistory = SESSION_HELPER.insertHistory(
                principal,
                loginInfo.getId(),
                browser,
                loginInfo.getToken(),
                location,
                screenResolution,
                loginSource,
                oidcProvider,
                oidcId,
                dao
        );
        if (accessHistory != null) {
            loginInfo.setAccessHistoryId(accessHistory.getId());
        }
        session.setAttribute(SessionHelper.SESSION_ATTRIBUTE_LOGIN_INFO, loginInfo);
        if (browser.getBrowser() != null && !browser.getBrowser().isEmpty()) {
            session.setAttribute(BROWSER_ATTRIBUTE, browser.getBrowser());
        }
        if (browser.getIpAddress() != null && !browser.getIpAddress().isEmpty()) {
            session.setAttribute(IP_ADDRESS_ATTRIBUTE, browser.getIpAddress());
        }
        if (browser.getBrowserOs() != null && !browser.getBrowserOs().isEmpty()) {
            session.setAttribute(BROWSER_OS_ATTRIBUTE, browser.getBrowserOs());
        }
        if (browser.getBrowserOsVersion() != null && !browser.getBrowserOsVersion().isEmpty()) {
            session.setAttribute(BROWSER_OS_VERSION_ATTRIBUTE, browser.getBrowserOsVersion());
        }
        loginInfo.setAnonymous(principal.getIsAnonymous());
        session.setAttribute(ANONYMOUS_ATTRIBUTE, principal.getIsAnonymous());
        loginInfo.setProvidedLocation(location != null);
        loginInfo.setProvidedScreenResolution(screenResolution != null);
    }
    
    private static UserRef initializeSessionLegacyAttributes(
            final HttpSession session,
            final TblUsuarioDTO userData,
            final BnextPrincipal principal,
            final IUntypedDAO dao,
            final boolean ldap
    ) {
        Long userId = Long.valueOf(userData.getUsuarioid());
        // No borrar, carga los colores del usuario a memoria
        LOGGER.debug("Loading {} user data", userId);
        Properties props = new Properties(userId);
        LOGGER.debug("Loaded {} user data", Properties.isXmlLoaded());
        UserRef userInfo = Properties.usersCofig.get(userId);
        LOGGER.debug("Loaded {} user data is {}", userId, userInfo);
        if (Utilities.isInteger(userData.getStatususuario()) && userInfo != null) {
            userInfo.setStatus(Integer.valueOf(userData.getStatususuario()));
        }
        Settings settings = Utilities.getSettings();
        String lang, locale;
        boolean notSupported = false;
        String userLang = userData.getLang() + "-" + userData.getLocale(); // <-- Cuando se tiene configurado el idioma del 
                                                                           // sistema en usuario, en las columnas de lang-locale las guarda como empty;("")
        if (userLang.equals("-")) {
            userLang = "system"; 
        }
        if (!LicenseUtil.hasSupportedLanguages(userLang) && !"system".equals(userLang)) {
            final LocaleDTO systemLocale = LicenseUtil.getDefaultLicenseLocale();
            final IUserDAO userDao = dao.getBean(IUserDAO.class);
            userDao.updateUserLocale(userId, systemLocale);
            notSupported = true;
        }
        if (userData.getLang().isEmpty() || notSupported) {
            lang = settings.getLang();
            locale = settings.getLocale();
        } else {
            lang = userData.getLang();
            locale = userData.getLocale();
        }
        session.setAttribute("lang", lang);
        session.setAttribute("locale", locale);
        if (principal == null) {
            session.setAttribute(BnextNegotiateSecurityFilter.MOCKPRINCIPALSESSIONKEY, new BnextPrincipal(userData));
        } else {
            session.setAttribute(BnextNegotiateSecurityFilter.MOCKPRINCIPALSESSIONKEY, principal);
        }
        session.setAttribute("intusuarioid", userId.toString());
        final FilterUserHelper filterHelper = new FilterUserHelper(dao);
        session.setAttribute(
                SessionFilterHandler.SORTED_PAGED_FILTERS,
               filterHelper.load(userId)
         ); 
        Integer c = dao.HQL_findSimpleInteger(" SELECT count(p)"
                + " FROM " + User.class.getCanonicalName() + " u "
                + " JOIN u.puestos p"
                + " JOIN p.perfil pe"
                + " WHERE"
                    + " pe.onlyTasksAvaliable = 1 "
                    + " AND u.id = :userId",
                "userId", userId
         );
        session.setAttribute("onlyTasks", c > 0 ? "1" : "0");
        Map result = dao.HQL_findSimpleMap(" SELECT new map("
                + " u.askToRenewLocation AS " + RENEW_DATA_LOCATION
                + ",u.askToRenewPassword AS " + RENEW_DATA_PASSWORD
                + ",u.askToRenewTimezone AS " + RENEW_DATA_TIMEZONE
                + ",u.departmentId AS " + DEPARTMENT_ID_ATTRIBUTE
                + ",u.businessUnitId AS " + BUSINESS_UNIT_ID_ATTRIBUTE
                + ",u.businessUnitDepartmentId AS " + BUSINESS_UNIT_DEPARTMENT_ID_ATTRIBUTE
                + ",u.legacyOpenFilters AS " + LEGACY_OPEN_FILTERS_ATTRIBUTE
            + " )"
            + " FROM " + User.class.getCanonicalName() + " u "
            + " WHERE "
            + " u.id = :userId",
              "userId", userId
        );
        session.setAttribute("onlyTasks", c > 0 ? "1" : "0");
        session.setAttribute(RENEW_DATA_LOCATION, result.get(RENEW_DATA_LOCATION));
        session.setAttribute(RENEW_DATA_TIMEZONE, result.get(RENEW_DATA_TIMEZONE));
        session.setAttribute(DEPARTMENT_ID_ATTRIBUTE, result.get(DEPARTMENT_ID_ATTRIBUTE));
        final Long businessUnitId = (Long) result.get(BUSINESS_UNIT_ID_ATTRIBUTE);
        session.setAttribute(BUSINESS_UNIT_ID_ATTRIBUTE, businessUnitId);
        session.setAttribute(BUSINESS_UNIT_DEPARTMENT_ID_ATTRIBUTE, result.get(BUSINESS_UNIT_DEPARTMENT_ID_ATTRIBUTE));
        final Object name = result.get(LEGACY_OPEN_FILTERS_ATTRIBUTE) == null ? 0 : result.get(LEGACY_OPEN_FILTERS_ATTRIBUTE);
        session.setAttribute(LEGACY_OPEN_FILTERS_ATTRIBUTE, name);
        if (ldap) {
            session.setAttribute(RENEW_DATA_PASSWORD, false);
        } else {
            session.setAttribute(RENEW_DATA_PASSWORD, result.get(RENEW_DATA_PASSWORD));
        }
        
        if (businessUnitId != null) {
            // Nombre de la planta
            String businessUnitName = dao.HQL_findSimpleString(" SELECT bu.description "
                + " FROM " + BusinessUnit.class.getCanonicalName() + " bu "
                + " WHERE id = :businessUnitId",
                ImmutableMap.of("businessUnitId", businessUnitId)
            );
            session.setAttribute(Login.BUSINESS_UNIT_NAME_ATTRIBUTE, businessUnitName);
        }
            
        session.setAttribute("vchnombre", userData.getNombre());
        session.setAttribute("vchnombrecompleto", userData.getNombrecompleto());
        session.setAttribute("systemColor", Properties.COLOR_SISTEMA);
        session.setAttribute("systemLanguage", lang);
        session.setAttribute("systemLocalization", locale);

        session.setAttribute("User", userData.getNombrecompleto());
        session.setAttribute("accountName", userData.getNombre());
        session.setAttribute("scope", userData.getScope());

        session.setAttribute("intstatususuario", userData.getStatususuario());

        session.removeAttribute("error");

        session.setAttribute("admin_general", userData.getAdmin_general());
        session.setAttribute(SessionHelper.SESSION_ATTRIBUTE_SERVICES, userData.getUserServices());
        session.setAttribute("servicesAddedBySystem", userData.getUserServicesAddedBySystem());

        session.setAttribute("introlencuestas", "0");
        session.setAttribute("intpermisoencuestas", "0");
        session.setAttribute("introlindicadores", "0");
        session.setAttribute("intpermisoindicadores", "0");
        session.setAttribute("introldocumentos", "0");
        session.setAttribute("intpermisodocumentos", "0");
        session.setAttribute("introladministracion", "0");
        session.setAttribute("intpermisoconfiguracion", "0");
        session.setAttribute("introlreuniones", "0");
        session.setAttribute("intpermisoreuniones", "0");
        session.setAttribute("introlacciones", "0");
        session.setAttribute("intpermisoacciones", "0");
        session.setAttribute("introlauditorias", "0");
        session.setAttribute("intpermisoauditorias", "0");
        session.setAttribute("introlproyectos", "0");
        session.setAttribute("intpermisoproyectos", "0");
        session.setAttribute("introlproyectos", "0");
        session.setAttribute("intpermisoquejas", "0");
        if (userData.getAdmin_general().equals("1")) {
            if (Utilities.isModuleAvailable(Module.POLL)) {
                session.setAttribute("introlencuestas", Properties.ENCUESTAS_MANAGER);
                session.setAttribute("intpermisoencuestas", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.METER)) {
                session.setAttribute("introlindicadores", Properties.INDICADORES_MANAGER);
                session.setAttribute("intpermisoindicadores", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.DOCUMENT)) {
                session.setAttribute("introldocumentos", Properties.DOCUMENTOS_MANAGER);
                session.setAttribute("intpermisodocumentos", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.CONFIGURATION)) {
                session.setAttribute("introladministracion", Properties.ADMINISTRADOR_MAESTRO);
                session.setAttribute("intpermisoconfiguracion", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.MEETING)) {
                session.setAttribute("introlreuniones", Properties.REUNIONES_MANAGER);
                session.setAttribute("intpermisoreuniones", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.ACTION)) {
                session.setAttribute("introlacciones", Properties.ACCIONES_MANAGER);
                session.setAttribute("intpermisoacciones", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.AUDIT)) {
                session.setAttribute("introlauditorias", Properties.AUDITORIAS_MANAGER);
                session.setAttribute("intpermisoauditorias", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.PROJECT)) {
                session.setAttribute("introlproyectos", Properties.PROYECTOS_MANAGER);
                session.setAttribute("intpermisoproyectos", Properties.ADMINISTRADOR_MAESTRO);
            }
            if (Utilities.isModuleAvailable(Module.COMPLAINT)) {
                session.setAttribute("introlquejas", Properties.QUEJAS_LEADER);
                session.setAttribute("intpermisoquejas", Properties.ADMINISTRADOR_MAESTRO);
            }
            session.setAttribute("validationsRole", Properties.VALIDATIONS_MANAGER);
            //atributos de permisos para reportes
            session.setAttribute("intpermisocuestionarios", Properties.ADMINISTRADOR_MAESTRO);
            session.setAttribute("intpermisogerencial", Properties.ADMINISTRADOR_MAESTRO);
            //sabe donde o como se usan
            session.setAttribute("introlrepositorios", Properties.ADMINISTRADOR_MAESTRO);
            session.setAttribute("introlreportes", Properties.ADMINISTRADOR_MAESTRO);
            session.setAttribute("gridSize", Utilities.getValidInteger(userData.getGridSize()));
            session.setAttribute("detailGridSize", Utilities.getValidInteger(userData.getDetailGridSize()));
            session.setAttribute("introlrepositorios", Properties.ADMINISTRADOR_MAESTRO);
            session.setAttribute("introlreportes", Properties.ADMINISTRADOR_MAESTRO);
            session.setAttribute("gridSize", Utilities.getValidInteger(userData.getGridSize()));
            session.setAttribute("detailGridSize", Utilities.getValidInteger(userData.getDetailGridSize()));
            session.setAttribute("floatingGridSize", Utilities.getValidInteger(userData.getFloatingGridSize()));
        } else {
            if (Utilities.isModuleAvailable(Module.POLL)) {
                session.setAttribute("introlencuestas", Utilities.getValidInteger(userData.getPermisoencuestas()));
                session.setAttribute("intpermisoencuestas", Utilities.getValidInteger(userData.getPermisoencuestas()));
            }
            if (Utilities.isModuleAvailable(Module.METER)) {
                session.setAttribute("introlindicadores", Utilities.getValidInteger(userData.getRolindicadores()));
                session.setAttribute("intpermisoindicadores", Utilities.getValidInteger(userData.getPermisoindicadores()));
            }
            if (Utilities.isModuleAvailable(Module.DOCUMENT)) {
                session.setAttribute("introldocumentos", Utilities.getValidInteger(userData.getRoldocumentos()));
                session.setAttribute("intpermisodocumentos", Utilities.getValidInteger(userData.getPermisodocumentos()));
            }
            if (Utilities.isModuleAvailable(Module.CONFIGURATION)) {
                session.setAttribute("introladministracion", Utilities.getValidInteger(userData.getAdmin_general()));
                session.setAttribute("intpermisoconfiguracion", Utilities.getValidInteger(userData.getPermisoconfiguracion()));
            }
            if (Utilities.isModuleAvailable(Module.MEETING)) {
                session.setAttribute("introlreuniones", Utilities.getValidInteger(userData.getRolreuniones()));
                session.setAttribute("intpermisoreuniones", Utilities.getValidInteger(userData.getPermisoreuniones()));
            }
            if (Utilities.isModuleAvailable(Module.ACTION)) {
                session.setAttribute("introlacciones", Utilities.getValidInteger(userData.getRolacciones()));
                session.setAttribute("intpermisoacciones", Utilities.getValidInteger(userData.getPermisoacciones()));
            }
            if (Utilities.isModuleAvailable(Module.AUDIT)) {
                session.setAttribute("introlauditorias", Utilities.getValidInteger(userData.getAdmin_general()));
                session.setAttribute("intpermisoauditorias", Utilities.getValidInteger(userData.getPermisoauditorias()));
            }
            if (Utilities.isModuleAvailable(Module.PROJECT)) {
                session.setAttribute("introlproyectos", Utilities.getValidInteger(userData.getRolproyectos()));
                session.setAttribute("intpermisoproyectos", Utilities.getValidInteger(userData.getPermisoproyectos()));
            }
            if (Utilities.isModuleAvailable(Module.COMPLAINT)) {
                session.setAttribute("introlquejas", Utilities.getValidInteger(userData.getRolquejas()));
                session.setAttribute("intpermisoquejas", Utilities.getValidInteger(userData.getPermisoquejas()));
            }
            session.setAttribute("introlrepositorios", Utilities.getValidInteger(userData.getRolrepositorios()));
            session.setAttribute("introlreportes", Utilities.getValidInteger("0"));
            session.setAttribute("validationsRole", Utilities.getValidInteger("0"));
            //atributos de permisos para reportes
            session.setAttribute("intpermisocuestionarios", Utilities.getValidInteger(userData.getPermisocuestionarios()));
            session.setAttribute("intpermisogerencial", Utilities.getValidInteger(userData.getPermisogerencial()));
            session.setAttribute("gridSize", Utilities.getValidInteger(userData.getGridSize()));
            session.setAttribute("detailGridSize", Utilities.getValidInteger(userData.getDetailGridSize()));
            session.setAttribute("floatingGridSize", Utilities.getValidInteger(userData.getFloatingGridSize()));
        }
        session.setMaxInactiveInterval(settings.getSessionMaxInactiveTime() * 60);
        
        return userInfo;
    }

    public boolean validateSession() {
        try {
            HttpSession session = request.getSession(false);
            boolean validSession
                    = session != null
                    && request.isRequestedSessionIdValid()
                    && session.getAttribute("intusuarioid") != null
                    && !session.getAttribute("intusuarioid").toString().isEmpty();
            if (!validSession || session == null) {
                return false;
            }
            session.getCreationTime();
            return true;
        } catch (IllegalStateException ise) {
            LOGGER.error("There was an error {}", ise);
            return false;
        }
    }

    private boolean hasPreviousValidSession(final HttpSession activeSession) {
        if (activeSession == null) {
            return false;
        }
        try {
            final Object account = activeSession.getAttribute(ACCOUNT_ATTRIBUTE);
            return account != null && !account.toString().isEmpty();
        } catch(final Exception ex) {
            LOGGER.error("User {} with invalid active session. {}", userInfo.getAccount(), ex);
            return false;
        }
    }
   
    private boolean validateCertificate() {
        final String hql = " SELECT new " + UserCertificateDTO.class.getCanonicalName() + "("
                    + " u.id,"
                    + " u.cuenta,"
                    + " u.code,"
                    + " u.description,"
                    + " u.certificate,"
                    + " u.licenseCode,"
                    + " u.status,"
                    + " u.inactiveBySystem"
                + " ) "
                + " FROM " + User.class.getCanonicalName() + " u "
                + " WHERE u.deleted = 0"
                + " AND u.status IN (" 
                    + User.STATUS.ACTIVE.getValue() + ","
                    + User.STATUS.LOCKED.getValue()
                + " )"
                + " AND u.cuenta = :account";
        final UserCertificateDTO certificate = (UserCertificateDTO) dao.HQL_findSimpleObject(hql, "account", userInfo.getAccount());
        if (certificate == null) {
            return false;
        }
        return dao.getBean(ILicenseUserDAO.class).validateCertificateStatus(certificate.getCuenta(), certificate.getCertificate());
    }

    public void resetSession() {
        SESSION_HELPER.resetSession(request, dao);
    }
    
    public static void resetSession(final HttpServletRequest request, final IUntypedDAO dao) {
        SESSION_HELPER.resetSession(request, dao);
    }

    public void closeSession() {
        SESSION_HELPER.closeSession(request.getSession(), dao);
        final Long loggedUserId = SecurityUtils.getLoggedUserId();
        if (loggedUserId != null && loggedUserId != -1 && loggedUserId != 0) {
            final UserLogin userLogin = new UserLogin(loggedUserId);
            userLogin.removeSession(request.getSession(), true);
        }
    }

    public void closeSession(final Long userId) {
        final UserLogin userLogin = new UserLogin(userId);
        final HttpSession activeSession = userLogin.getActiveSession();
        if (activeSession != null) {
            SESSION_HELPER.closeSession(activeSession, dao);
            userLogin.removeSession(activeSession, true);
        }
    }

    public boolean isValid() {
        return valid;
    }

    public String getUrl() {
        return url;
    }

    public boolean hasBasefrm() {
        return url != null;
    }

    private TblUsuarioDTO getUserData() {
        final String hql = " SELECT c.id"
                + " FROM " + User_old.class.getCanonicalName() + " c "
                + " WHERE c.account = :account";
        final Long id = dao.HQL_findSimpleLong(
                hql, 
                ImmutableMap.of("account", userInfo.getAccount())
        );
        if (id != null) {
            final User_old usr = dao.HQLT_findById(User_old.class, id);
            final Boolean isAdmin = Objects.equals(usr.getRoot(), 1);
            final ServicesDTO services = dao.getUserServices(id, isAdmin);
            final List<ProfileServices> userServices = new ArrayList<>(services.getUserServices());
            final List<ProfileServices> userServicesAddedBySystem = new ArrayList<>(services.getUserServicesAddedBySystem());
            LOGGER.debug("Servicios --> " + userServices);
            final UserRef userSimple = dao.HQLT_findById(UserRef.class, id);
            return new TblUsuarioDTO(userSimple, usr, userServices, userServicesAddedBySystem);
        }
        return null;
    }

    private boolean isUserInactive(final String account) {
        String hql = " SELECT c.status "
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.cuenta = :account";
        LOGGER.trace(hql);
        final Integer status = dao.HQL_findSimpleInteger(hql, "account", account);
        if (status.equals(User.STATUS.INACTIVE.getValue())
                || status.equals(User.STATUS.TO_ACTIVATE.getValue())) {
            LOGGER.warn("User {} is disabled", getUserName(account));
            return true;
        }
        return false;
    }
    
    public boolean isUserAuthTypeLdapAvailable() {
        final String account = userInfo.getAccount();
        String hql = " SELECT c.authTypeLdap"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.cuenta = :account";
        final Integer authTypeLdap = dao.HQL_findSimpleInteger(hql, "account", account);
        final Boolean available = Objects.equals(authTypeLdap, 1);
        return available;
    }
    
    private boolean isUserAuthTypeLandingPageAvailable(final String account) {
        String hql = " SELECT c.authTypeLandingPage"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.cuenta = :account";
        final Integer authTypeLandingPage = dao.HQL_findSimpleInteger(hql, "account", account);
        final Boolean available = Objects.equals(authTypeLandingPage, 1);
        return available;
    }
    
    private boolean isUserAuthTypeIntegratedAvailable(final String account) {
        String hql = " SELECT c.authTypeIntegrated"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.cuenta = :account";
        final Integer authTypeIntegrated = dao.HQL_findSimpleInteger(hql, "account", account);
        final Boolean available = Objects.equals(authTypeIntegrated, 1);
        return available;
    }


    private String getUserName(final String account) {
        String hql = " SELECT c.description "
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.status IN (" 
                    + User.STATUS.ACTIVE.getValue() + ","
                    + User.STATUS.LOCKED.getValue()
                + " )"
                + " AND c.cuenta = :account";
        return dao.HQL_findSimpleString(hql, "account", account);
    }

    public LoginError getError() {
        return error;
    }
    
    private static TokenDetails parseToken(final String token, final HttpServletRequest request) {
        try {
            final boolean ldapEncryption = SecurityUtils.isRedirectUrlFromLdapSession(request, token);
            if (ldapEncryption) {
                return parseLdapToken(token);
            } else {
                final ITokenProvider tokenProvider = Utilities.getBean(ITokenProvider.class);
                final TokenDetails details = tokenProvider.getAuthentication(token);
                if (details.isValid()) {
                    final Integer userVersion = Utilities.getBean(IUserDAO.class).getUserVersion(details.getSubject());
                    details.setValid(userVersion != null && userVersion.equals(details.getVersion()));
                }
                return details;
            }
        } catch (final Exception ex) {
            LOGGER.error("Failed to validate token {}", token, ex);
            final TokenDetails tokenUser = new TokenDetails();
            tokenUser.setValid(false);
            return tokenUser;
        }
    }

    
    private boolean processToken(final boolean closeSessionConfirmed) throws IOException {
        if (token == null || token.isEmpty()) {
            error = LoginError.INVALID_TOKEN;
            valid = false;
            return false;
        }
        final TokenDetails tokenUser = parseToken(token, request);
        if (!tokenUser.isValid()) {
            valid = false;
            error = LoginError.EXPIRED_TOKEN;
            loginStatus = LOGIN_STATUS.ERROR;
            return false;
        }
        userInfo.setAccount(tokenUser.getSubject());
        if (isUserInactive(userInfo.getAccount())) {
            error = LoginError.INACTIVE_ACCOUNT;
            loginStatus = LOGIN_STATUS.ERROR;
            return false;
        } else if (!validateCertificate()) {
            error = LoginError.INVALID_CERTIFICATE;
            loginStatus = LOGIN_STATUS.ERROR;
            return false;
        }
        valid = true;
        if (closeSessionConfirmed) {
            return true;
        }
        if (!isUserAuthTypeIntegratedAvailable(userInfo.getAccount())) {
            error = LoginError.UNAVAILABLE_WINDOWS_AUTH;
            loginStatus = LOGIN_STATUS.ERROR;
        }
        return validateConcurrentSessions();
    }

    private void processLdapToken() {
        if (token == null || token.isEmpty()) {
            error = LoginError.INVALID_TOKEN;
            loginStatus = LOGIN_STATUS.ERROR;
            return;
        }
        final String query = dencryptLdapToken(token);
        if ("".equals(query)) {
            error = LoginError.INVALID_TOKEN;
            loginStatus = LOGIN_STATUS.ERROR;
            return;
        }
        final Map<String, String> params = Utilities.getQueryUrlParams(query);
        if (params.isEmpty()) {
            error = LoginError.INVALID_TOKEN;
            loginStatus = LOGIN_STATUS.ERROR;
            return;
        }
        if (!params.containsKey("u") || !params.containsKey("n") || !params.containsKey("c")) {
            error = LoginError.INVALID_TOKEN;
            loginStatus = LOGIN_STATUS.ERROR;
            return;
        }
        final String life = params.get("l");
        if (life == null || life.isEmpty()) {
            error = LoginError.EXPIRED_TOKEN;
            loginStatus = LOGIN_STATUS.ERROR;
            return;
        }
        final long dif = Math.abs(new Date().getTime() - new Date(Long.parseLong(life)).getTime());
        if (dif <= MAXMIUM_LIFE_LDAP_TOKEN_MS) {
            userInfo.setAccount(params.get("u"));
        } else {
            error = LoginError.EXPIRED_TOKEN;
            loginStatus = LOGIN_STATUS.ERROR;
            expiredLdapTokenMs  = dif;
        }
        userInfo.setAccount(params.get("u"));
        userInfo.setName(params.get("n"));
        userInfo.setMail(params.get("c"));
        if (!existsUser()) {
            return;
        }
        if (isUserInactive(userInfo.getAccount())) {
            error = LoginError.INACTIVE_ACCOUNT;
            loginStatus = LOGIN_STATUS.ERROR;
        } else if (!validateCertificate()) {
            error = LoginError.INVALID_CERTIFICATE;
            loginStatus = LOGIN_STATUS.ERROR;
        } else if (!isUserAuthTypeLandingPageAvailable(userInfo.getAccount())) {
            error = LoginError.UNAVAILABLE_WINDOWS_AUTH;
            loginStatus = LOGIN_STATUS.ERROR;
        }
    }
    
    private static TokenDetails parseLdapToken(final String token1) throws NumberFormatException {
        final TokenDetails details = new TokenDetails();
        final String query = dencryptLdapToken(token1);
        if ("".equals(query)) {
            details.setValid(false);
            return details;
        }
        final Map<String, String> params = Utilities.getQueryUrlParams(query);
        if (params.isEmpty()) {
            details.setValid(false);
            return details;
        }
        if (!params.containsKey("u") || !params.containsKey("n") || !params.containsKey("c")) {
            details.setValid(false);
            return details;
        }
        final String life = params.get("l");
        if (life == null || life.isEmpty()) {
            details.setValid(false);
            return details;
        }
        final long dif = Math.abs(new Date().getTime() - new Date(Long.parseLong(life)).getTime());
        if (dif > MAXMIUM_LIFE_LDAP_TOKEN_MS) {
            details.setValid(false);
            return details;
        }
        details.setSubject(params.get("u"));
        details.setValid(true);
        return details;
    }

    private static String dencryptLdapToken(String token) {
        try {
            final String plainToken;
            if (token.startsWith(SecurityUtils.LDAP_TOKEN_PREFIX)) {
                plainToken = token.replace(SecurityUtils.LDAP_TOKEN_PREFIX, "");
            } else {
                plainToken = token;
            }
            String query = new RC4().GetDecryptedString(plainToken, Utilities.getSettings().getRcKey());
            return query;
        } catch (Exception e) {
            LOGGER.error("Fail while dencrypting token '{}'", token, e);
        }
        return "";
    }

    /**
     * Llena la información de sesiones duplicadas para mostrar el dialogo en pantalla
     *
     * @return
     * @throws IOException
     */
    private boolean requestConfirmationCloseSession() throws IOException {
        final HttpSession activeSession = loginInfo.getActiveSession();
        final boolean validSession = SESSION_HELPER.validSession(activeSession);
        if (!validSession || request.getSession().equals(activeSession) ) {
            return true;
        }

        // Los usuarios anonimos no devuelven datos
        if (Utilities.isAnonymous(userInfo.getAccount())) {
            error = LoginError.DUPLICATE_ANONYMOUS_SESSION;
            loginStatus = LOGIN_STATUS.DUPLICATE;
            return false;
        }

        final Object browser = activeSession.getAttribute(BROWSER_ATTRIBUTE);
        if (browser != null) {
            loginInfo.setBrowser(browser.toString());
        } else {
            loginInfo.setBrowser("-");
        }
        final Object broswerOs = activeSession.getAttribute(BROWSER_OS_ATTRIBUTE);
        if (broswerOs != null) {
            loginInfo.setBrowserOs(broswerOs.toString());
        } else {
            loginInfo.setBrowserOs("-");
        }
        final Object browserOsVersion = activeSession.getAttribute(BROWSER_OS_VERSION_ATTRIBUTE);
        if (browserOsVersion != null) {
            loginInfo.setBrowserOsVersion(browserOsVersion.toString());
        } else {
            loginInfo.setBrowserOsVersion("-");
        }
        final Object ipAddress = activeSession.getAttribute(IP_ADDRESS_ATTRIBUTE);
        if (ipAddress != null) {
            loginInfo.setIpAddress(ipAddress.toString());
        } else {
            loginInfo.setIpAddress("-");
        }
        loginInfo.setAccount(userInfo.getAccount());
        final Boolean anonymous = activeSession.getAttribute(ANONYMOUS_ATTRIBUTE) != null;
        loginInfo.setAnonymous(anonymous);
        loginInfo.setCreatedDate(Utilities.formatDateWithTime(new Date(activeSession.getCreationTime())));
        if (ldapEncryption) {
            loginInfo.setToken(token);
        } else {
            final String newToken = generateToken(userInfo.getAccount());
            loginInfo.setToken(newToken);
        }
        valid = false;
        error = LoginError.DUPLICATE_SESSION;
        loginStatus = LOGIN_STATUS.DUPLICATE;
        return valid;
    }

    public boolean existsUser() {
        Long exists = dao.HQL_findSimpleLong(" SELECT count(c.cuenta)"
                + " FROM " + UserRef.class.getCanonicalName() + " c "
                + " WHERE c.cuenta = :account", "account", userInfo.getAccount());
        return exists > 0;
    }

    public boolean registerLdapUser() {
        // Se colocan datos por defecto
        User usr = new User();
        usr.setId(-1L);
        usr.setCode(this.userInfo.getAccount());
        usr.setContrasena(this.userInfo.getAccount());
        usr.setCorreo(this.userInfo.getMail());
        usr.setCuenta(this.userInfo.getAccount());
        usr.setDescription(this.userInfo.getName());
        usr.setDefaultWorkflowPosition(null);
        usr.setDeleted(0);
        usr.setPuestos(null);
        usr.setGridSize(0);
        usr.setDetailGridSize(0);
        usr.setAuthTypeIntegrated(0);
        usr.setAuthTypeLdap(1);
        usr.setAuthTypeLandingPage(1);
        usr.setAuthTypeOidc(0);
        usr.setFloatingGridSize(0);
        // Se colocan datos consultados desde AD
        if (this.domainUserData(usr)) {
            GenericSaveHandle gsh = dao.registerExternalUser(usr, UserCreationSource.LDAP);
            LicenseUtil.updateLicencesCount();
            this.loginInfo.setId(gsh.getPersistentId());
            if (!User.STATUS.ACTIVE.getValue().equals(usr.getStatus())) {
                error = LoginError.EXCEEDED_LICENCES;
                loginStatus = LOGIN_STATUS.ERROR;
                return false;
            } else {
                return gsh.getOperationEstatus() == 1;
            }
        } else {
            LOGGER.error("Register LDAP failed, could not create account {}. Domian data not available.", this.userInfo.getAccount());
            return false;
        }
    }
    
    public boolean updateDomainData() {
        User user = new User(userInfo.getAccount());
        if (!this.domainUserData(user)) {
            return false;
        }
        return updateDomainData(user, userInfo.getAccount(), dao);
    }
    public static boolean updateDomainData(String account) {
        if (account == null) {
            LOGGER.error("Invalid LDAP update call, user is NULL");
            return false;
        }
        IUserLdap user = getUserData(account);
        IUntypedDAO dao = Utilities.getUntypedDAO();
        Map<String, String> params = new HashMap<>();
        params.put("correo", user.getCorreo());
        params.put("cuenta", user.getCuenta());
        params.put("description", user.getDescription());
        params.put("contacto", user.getContacto());
        Integer result = dao.HQL_findSimpleInteger(" SELECT count(*)"
                + " FROM " + User.class.getCanonicalName() + " u "
                + " WHERE "
                    + " u.correo = :correo"
                    + " AND u.cuenta = :cuenta"
                    + " AND u.description = :description"
                    + " AND u.contacto = :contacto",
                params
        );
        if (result == 1) {
            return true;
        }
        return updateDomainData(user, account, dao);
    }
    public static boolean updateDomainData(IUserLdap user, String account, IUntypedDAO dao) {
        HashMap<String, String> params = new HashMap<>();
        params.put("description", user.getDescription());
        params.put("contacto", user.getContacto());
        params.put("correo", user.getCorreo());
        params.put("cuenta", account);
        dao.HQL_updateByQuery(" UPDATE " + User.class.getCanonicalName() + " u "
            + " SET "
                + " u.description = :description,"
                + " u.contacto = :contacto,"
                + " u.correo = :correo"
            + " WHERE u.cuenta = :cuenta", params
        );
        
        return true;
    }
    
    private boolean isUserDomainValid() { 
        if (Utilities.getSettings().getSsoDomainValidation() == 0) {
            // si la validación está apagada siempre devolverá "true"
            return true;
        }
        SearchResult rs;
        Attributes attrs;
        String userDomain = this.userInfo.getDomain();
        try {
            NamingEnumeration<SearchResult> foundUserDomain = ActiveDirectoryUtil.searchForDomain(userDomain);
            if (foundUserDomain == null) {
                LOGGER.debug(" User domain is not found, userAccount: '" + this.userInfo.getAccount() + "', userDomain: '" + userDomain + "'."
                );
                return false;
            }
            rs = foundUserDomain.next(); // <--- Solo debe haber uno!
            attrs = rs.getAttributes();
            userDomain = (String) attrs.get("cn").get();
            this.userInfo.setDomain(userDomain);
        } catch (NamingException ex) {
            LOGGER.error(" User domain is not confirmed '" + this.userInfo.getAccount() + "'."
            );
        } 
        LOGGER.error(" User domain is not confirmed '" + this.userInfo.getAccount() + "'."
        );
        if (ActiveDirectoryUtil.getActiveDirectoryDomain().equalsIgnoreCase(userDomain)) {
            return true;
        }
        /**
         * Si el dominio del usuario es diferente al de la aplicación no llenamos sus datos personalizados.
         * BnextDomain != UserDomain
         **/
         LOGGER.error(" User '" + this.userInfo.getAccount() + "' invalid domain,"
             + " current-user-domain: " + this.userInfo.getDomain() + ","
             + " app-domain: " + ActiveDirectoryUtil.getActiveDirectoryDomain()
         );
         return false;
    }
    
    private boolean domainUserData(User user) {
        if (!ActiveDirectoryUtil.isSsoActive() || !isUserDomainValid()) {
            LOGGER.error("User domain invalid for {}.", user.getCuenta());
            return false;
        }
        if (!fillUserData(user.getCuenta(), user)) {
            LOGGER.error("Can not obtain LDAP user data for {}.", user.getCuenta());
            return false;
        }
        userInfo.setMail(user.getCorreo());
        userInfo.setContact(user.getContacto());
        return true;
    }

    public static IUserLdap getUserData(String account) {
        User user = new User(account);
        fillUserData(account, user);
        return user;
    }
    private static boolean fillUserData(String account, User user) {
        // si la integración con ldap integrada en tomcat no está activa
        if (!ActiveDirectoryUtil.isSsoActive()) {
            return true;
        }
        try {
            NamingEnumeration<SearchResult> foundUser = ActiveDirectoryUtil.searchForUser(user.getCuenta());
            if (foundUser == null) {
                LOGGER.error(" User '" + account + "' could not be found, domain " + ActiveDirectoryUtil.getActiveDirectoryDomain()
                );
                return false;
            }
            user.setCorreo(null);
            SearchResult rs = foundUser.next(); // <--- Solo debe haber uno!
            Attributes attrs = rs.getAttributes();
            StringBuilder phones = new StringBuilder(200);
            String key, value;
            for (String attr : ActiveDirectory.RETURN_ATTRIBUTES) {
                key = attr.toLowerCase();
                if (attrs.get(key) == null) {
                    LOGGER.error("Missing LDAP value '" + attr + "'. account: {}", account);
                    value = "Sin información";
                } else {
                    Attribute attrValue = attrs.get(key);
                    value = (String) attrValue.get();
                }
                switch(key) { 
                    case "samaccountname":
                        user.setCuenta(value);
                        user.setCode(value);
                        break;
                    case "userprincipalname":
                        if (user.getCorreo() == null) {
                            user.setCorreo(value);
                        }
                        break;
                    case "mail":
                        user.setCorreo(value);
                        break;
                    case "displayname":
                        user.setDescription(value);
                        break;
                    case "telephonenumber":
                    case "othertelephone":
                    case "mobile":
                        phones.append(key).append(":").append(value).append("\\n");
                        break;
                    default:
                        if (key.contains("phone")) {
                            phones.append(value).append("\\n");
                            break;
                        }
                        if (LOGGER.isWarnEnabled()) {
                            LOGGER.warn("Unused attribute '" + key + "' with value '" + value + "', ad LDAP user data. account: {}", account);
                        }
                }
            }
            if (phones.length() >= 2000) {
                user.setContacto(phones.substring(0, 1999));
            } else {
                user.setContacto(phones.toString());
            }
            return true;
        } catch (NamingException ex) {
            LOGGER.error("Unable attributes at  LDAP user data. account: {}", new Object[] {
                account
            }, ex);
        }
        return false;
    }
    
    

    public LOGIN_STATUS getLoginStatus() {
        return loginStatus;
    }

    public void setLoginStatus(LOGIN_STATUS loginStatus) {
        this.loginStatus = loginStatus;
    }

    public UserLogin getLoginInfo() {
        return loginInfo;
    }

    public LoginDTO getUserInfo() {
        return userInfo;
    }

    public Long getExpiredLdapTokenMs() {
        return expiredLdapTokenMs;
    }

    public void setConfirmCloseSession(boolean confirmCloseSession) {
        this.confirmCloseSession = confirmCloseSession;
    }

    public static boolean validSession(final HttpSession session) {
        return SESSION_HELPER.validSession(session);
    }
    
    private static TokenDetails newInvalidToken() {
        final TokenDetails details = new TokenDetails();
        details.setValid(false);
        return details;
    }
    
    public static TokenDetails validateToken(final String token) {
        if (token == null 
                || token.trim().isEmpty()
                || "undefined".equals(token)
                || "null".equals(token)) {
            return newInvalidToken();
        }
        try {
            final TokenDetails details = parseToken(token, null);
            return details;
        } catch (Exception e) {
            LOGGER.error("Failed process due to invalid close previous session token {}", token, e);
            return newInvalidToken();
        }
    }

    public static String generateToken(final String userAccount) {
        final ITokenProvider tokenProvider = Utilities.getBean(ITokenProvider.class);
        final TokenDetails details = new TokenDetails();
        details.setSubject(userAccount);
        final Integer userVersion = Utilities.getBean(IUserDAO.class).getUserVersion(details.getSubject());
        details.setVersion(userVersion);
        final String newToken = tokenProvider.createToken(details, 300L).getAccessToken();
        return newToken;
    }
}
