package bnext.login.util;

import java.util.Objects;

public enum OidcaAuthenticationType {

    SINGLE_SIGN_ON(1),
    IDENTITY_PLATTAFORM(2);

    private final Integer value;

    OidcaAuthenticationType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static OidcaAuthenticationType parseValue(Integer value) {
        for (final OidcaAuthenticationType instance : values()) {
            if (Objects.equals(instance.getValue(), value)) {
                return instance;
            }
        }
        return null;
    }

    
    
}
