/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package bnext.login.error;

import bnext.login.dto.UserDetailDTO;
import org.springframework.security.authentication.InternalAuthenticationServiceException;

/**
 *
 * <AUTHOR>
 */
public class UserAgentException extends InternalAuthenticationServiceException {
    
    private final UserDetailDTO UserInfo;
    
    public UserAgentException(final String message, final Throwable cause) {
        super(message, cause);
        this.UserInfo = null;
    }
    
    public UserAgentException(final String message, final UserDetailDTO UserInfo) {
        super(message);
        this.UserInfo = UserInfo;
    }
    
    public UserDetailDTO getUserInfo() {
        return UserInfo;
    }
    
}
