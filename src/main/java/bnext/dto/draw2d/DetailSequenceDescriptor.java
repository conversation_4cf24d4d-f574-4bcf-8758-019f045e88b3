/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package bnext.dto.draw2d;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DetailSequenceDescriptor {
    private List<Element> headers = new ArrayList<>();
    private Integer maxIndex;
    private List<Connection> connections = new ArrayList<>();
    private List<RecurrenceDescription> recurenceDescriptions = new ArrayList<>();

    public List<Element> getHeaders() {
        return headers;
    }

    public void setHeaders(List<Element> headers) {
        this.headers = headers;
    }

    public Integer getMaxIndex() {
        return maxIndex;
    }

    public void setMaxIndex(Integer maxIndex) {
        this.maxIndex = maxIndex;
    }

    public List<Connection> getConnections() {
        return connections;
    }

    public void setConnections(List<Connection> connections) {
        this.connections = connections;
    }

    public List<RecurrenceDescription> getRecurenceDescriptions() {
        return recurenceDescriptions;
    }

    public void setRecurenceDescriptions(List<RecurrenceDescription> recureDescriptions) {
        this.recurenceDescriptions = recureDescriptions;
    }
    
    
    
}
