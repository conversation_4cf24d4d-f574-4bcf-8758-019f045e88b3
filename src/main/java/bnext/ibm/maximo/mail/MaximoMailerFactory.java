package bnext.ibm.maximo.mail;

import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.ibm.maximo.entity.IActivity;
import bnext.ibm.maximo.entity.IMaximoBaseDetailTicket;
import bnext.ibm.maximo.entity.IMaximoTicketActivitiesUpdated;
import bnext.ibm.maximo.entity.IUpdatedActivity;
import bnext.ibm.maximo.util.MaximoHelper;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.ResourceBundle;
import qms.framework.core.Mailer;
import qms.framework.core.MailerFactory;
import qms.framework.dto.IMailable;
import qms.framework.mail.MailDTO;
import qms.framework.util.LocaleUtil;

/**
 *
 * <AUTHOR>
 */
public class MaximoMailerFactory extends MailerFactory {

    private final ResourceBundle tags;
    private final MaximoHelper helper;
    private final String OnNewTicketDaemonTemplate;
    private final String OnCloseActivityTicketTemplate;
    private final String OnUpdateTicketDaemonTemplate;

    public MaximoMailerFactory(ResourceBundle tags, MaximoHelper helper, String OnNewTicketDaemonTemplate, String OnCloseActivityTicketTemplate, String OnUpdateTicketDaemonTemplate, IUntypedDAO dao) {
        super(dao);
        this.tags = tags;
        this.helper = helper;
        this.OnNewTicketDaemonTemplate = OnNewTicketDaemonTemplate;
        this.OnCloseActivityTicketTemplate = OnCloseActivityTicketTemplate;
        this.OnUpdateTicketDaemonTemplate = OnUpdateTicketDaemonTemplate;
    }

    
    
    public MaximoMailerFactory(IUntypedDAO dao) {
        super(dao);
        helper = new MaximoHelper(dao);
        tags = LocaleUtil.getSystemI18n(MaximoMailer.class.getCanonicalName());
        OnNewTicketDaemonTemplate = Mailer.getTemplate(
            buildTemplatePath("OnNewTicketDaemon")
        );
        OnCloseActivityTicketTemplate = Mailer.getTemplate(
            buildTemplatePath("OnCloseActivityTicketDaemon")
        );
        OnUpdateTicketDaemonTemplate = Mailer.getTemplate(
            buildTemplatePath("OnUpdateTicketDaemon")
        );
    }
    
    private String buildTemplatePath(String key) {
        /** Ruta.del.paquete.KEY **/
        return MaximoMailer.class.getPackage().getName() + "." + key;
    }

    protected MailDTO buildOnNewTicketDaemonMail(IMaximoBaseDetailTicket ticket) throws UnsupportedEncodingException {
        // FR011 - Notificación de nuevos Tickets de WF HR
        MailDTO mail = new MailDTO();
        
        // Destinatario - El usuario guardado en el Ticket en el campo “Owner”.
        IMailable recipient = ticket.getPropietaryOwner();
        mail.setRecipients(
            helper.toMailSet(recipient)
        );
        
        // Asunto
        mail.setCustomSubject(true);
        mail.setSubject("Asignación de #Ticket - " + ticket.getSrId());
        // Remitente - Nombre de la aplicación
        mail.setRemittent(Utilities.getMailSystem());
        // Contenido
        mail.setMessageTitle("Asignación de #Ticket");
        mail.setMessage(
            Mailer.mapEntity(
                helper.getTicketBasicMailDto(null, ticket),
                OnNewTicketDaemonTemplate, 
                tags,
                dao
            )
        );
        return mail;
    }

    protected MailDTO buildOnUpdateTicketDaemonMail(String activityNo, IMaximoTicketActivitiesUpdated ticket) throws UnsupportedEncodingException {
        // FR007 - Detalle de nuevo Ticket enviado a Service Desk desde WP
        MailDTO mail = new MailDTO();
        
        // Destinatario - El configurado como usuario de Service Desk dentro del formulario
        mail.setRecipients(helper.toMailSet(
            ticket.getReportedBy(),
            ticket.getPropietaryOwner()
        ));
        ticket.getActivities().forEach((activity) -> {
            mail.getRecipients().add(
                new Mail(activity.getPropietaryOwner())
            );
        });
        // Remitente - Nombre de la aplicación
        mail.setRemittent(Utilities.getMailSystem());
        // Contenido
        List<IUpdatedActivity> activities = ticket.getActivities();
        mail.setMessageTitle("Estado de actividades del ticket");
        mail.setMessage(
            Mailer.mapEntity(
                helper.getUpdatedTicketMailDto(null, activityNo, ticket, activities),
                OnUpdateTicketDaemonTemplate, 
                tags,
                dao
            )
        );
        // Asunto
        mail.setCustomSubject(true);
        if (activities == null || activities.isEmpty()) {
            mail.setSubject("Actualización de Actividad - " + activityNo + " en " + ticket.getSrId());
        } else {
            if (activities.stream().allMatch((IUpdatedActivity t) -> {
                if (t.getNewStatus() == null) {
                    return false;
                }
                return IUpdatedActivity.ActivityStatus.COMPLETED.equals(t.getNewStatus());
            })) {
                mail.setSubject("Actividades del ticket " + ticket.getSrId() + " completadas");
            } else {
                mail.setSubject("Actualización de Actividad - " + activityNo + " en " + ticket.getSrId());
            }
            
        }
        return mail;
    }

    protected MailDTO buildOnCloseActivityTicketMail(IActivity activity, IMaximoBaseDetailTicket ticket) throws UnsupportedEncodingException {
        // FR007 - Detalle de nuevo Ticket enviado a Service Desk desde WP
        MailDTO mail = new MailDTO();
        
        // Destinatario - El configurado como usuario de Service Desk dentro del formulario
        mail.setRecipients(
            helper.toMailSet(ticket.getReportedBy())
        );
        // Asunto
        mail.setCustomSubject(true);
        mail.setSubject("Actividad completada " + activity.getActivityId() + " en " + ticket.getSrId());
        // Remitente - Nombre de la aplicación
        mail.setRemittent(Utilities.getMailSystem());
        // Contenido
        mail.setMessageTitle("Actividad completada");
        mail.setMessage(
            Mailer.mapEntity(
                helper.getActivityBasicMailDto(null, activity, ticket),
                OnCloseActivityTicketTemplate, 
                tags,
                dao
            )
        );
        return mail;
    }
    
}
