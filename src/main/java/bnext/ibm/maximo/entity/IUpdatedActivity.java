/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package bnext.ibm.maximo.entity;

/**
 *
 * <AUTHOR>
 */
public interface IUpdatedActivity extends IActivity {
    public enum ActivityStatus {
        COMPLETED, IN_PROGRESS;

        @Override
        public String toString() {
            return this.name().replaceAll("_", "-");
        }
        
    }
    public ActivityStatus getNewStatus();
    public ActivityStatus getPreviousStatus();
    public boolean isNew();
    public void setNew(boolean updated);
    public boolean isUpdated();
    public void setUpdated(boolean updated);
}
