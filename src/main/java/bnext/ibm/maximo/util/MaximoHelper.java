/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package bnext.ibm.maximo.util;

import DPMS.ActiveDirectoryInterface.ActiveDirectoryUtil;
import Framework.Config.Mail;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import ape.mail.core.MailHelper;
import bnext.ibm.maximo.dto.ActivityBasicMailDTO;
import bnext.ibm.maximo.dto.TicketBasicMailDTO;
import bnext.ibm.maximo.dto.UpdatedTicketMailDTO;
import bnext.ibm.maximo.entity.IActivity;
import bnext.ibm.maximo.entity.IMaximoBaseDetailTicket;
import bnext.ibm.maximo.entity.IMaximoTicketActivitiesUpdated;
import bnext.ibm.maximo.entity.IUpdatedActivity;
import com.google.common.collect.ImmutableMap;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Set;
import qms.form.dto.FavoriteTaskDto;
import qms.form.entity.FavoriteTask;
import qms.form.entity.IFavoriteTask;
import qms.framework.dto.IMailable;
import qms.framework.util.CacheRegion;
import qms.framework.util.SettingsUtil;
import qms.util.Helper;

/**
 *
 * <AUTHOR> Carlos Limas
 */
public class MaximoHelper extends Helper {
    
    private final MailHelper helper;

    public MaximoHelper(IUntypedDAO dao) {
        super(dao);
        helper = new MailHelper(dao);
    }
    
    public Set<Mail> toMailSet(IMailable ... user) {
        return helper.toMailSet(user);
    }

    private String ticketUrl(String ticketNo, String userAccount) throws UnsupportedEncodingException {
        String maximoFavoriteTaskCode = Utilities.getSettings().getMaximoFavoriteTaskCode();
        IFavoriteTask favorite = (IFavoriteTask) dao.HQL_findSimpleObject(""
            + " SELECT "
                + " new " + FavoriteTaskDto.class.getCanonicalName() + "("
                    + "c.id"
                    + ",c.status"
                    + ",c.deleted"
                    + ",c.type"
                    + ",c.menuPath"
                    + ",c.code"
                    + ",c.description"
                    + ",c.urlParams"
                    + ",c.icon"
                    + ",c.documentMasterId"
                    + ",c.isSystemGenerated"
                    + ",c.createdBy"
                    + ",c.lastModifiedBy"
                    + ",c.createdDate"
                    + ",c.lastModifiedDate"
                + " )"
            + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
            + " WHERE c.code = :code",
            ImmutableMap.of("code", maximoFavoriteTaskCode),
            true,
            CacheRegion.FAVORITE,
            0
        );
        StringBuilder url = new StringBuilder(200);
        String menuPath = "welcome", urlParams = null;
        if (favorite != null) {
            menuPath = favorite.getMenuPath().replaceAll("/*$|^/*", "");
            urlParams = favorite.getUrlParams();
        }
        url
            .append(SettingsUtil.getAppUrlNoSlash())
            .append("/qms/es/")
            .append(menuPath)
            .append("?")
        ;
        if (urlParams != null && !urlParams.isEmpty()) {
            String parsedUrlParams = ActiveDirectoryUtil.getParsedUrlParams(userAccount, favorite);
            url.append(parsedUrlParams).append("&");
        }
        url.append("ticketNo=").append(
            URLEncoder.encode(ticketNo, "UTF-8")
        );
        return url.toString();
    }
    
    private void fillTicketBasicMailDTO(IMaximoBaseDetailTicket ticket, TicketBasicMailDTO data, String recipientAccount) throws UnsupportedEncodingException {
        data.setTicketCreationDate(
            Utilities.formatDateBy(ticket.getCreationDate(), "dd/MM/yyyy")
        );
        data.setTicketDescription(ticket.getDetail());
        data.setTicketNo(ticket.getSrId());
        data.setTicketSummary(ticket.getSummary());
        data.setTicketUrl(
            ticketUrl(ticket.getSrId(), recipientAccount)
        );
    }
    
    public TicketBasicMailDTO getTicketBasicMailDto(String recipientAccount, IMaximoBaseDetailTicket ticket) throws UnsupportedEncodingException {
        TicketBasicMailDTO data = new TicketBasicMailDTO();
        this.fillTicketBasicMailDTO(ticket, data, recipientAccount);
        return data;
    }
    
    public UpdatedTicketMailDTO getUpdatedTicketMailDto(
        String recipientAccount,
        String activityNo,
            IMaximoTicketActivitiesUpdated ticket,
        List<IUpdatedActivity> activities
    ) throws UnsupportedEncodingException {
        UpdatedTicketMailDTO data = new UpdatedTicketMailDTO(activityNo);
        this.fillTicketBasicMailDTO(ticket, data, recipientAccount);
        data.setTicketStatus(ticket.getStatus());
        data.setTicketOwner(ticket.getPropietaryOwner().getDescription());
        // Se genera TABLE de estatus
        if (activities == null || activities.isEmpty()) {
            data.setTicketActivityStatusTable(" - Aún no se han agregado actividades -");
        } else {
            StringBuilder activityBuilder = new StringBuilder(activities.size() * 400);
            activityBuilder
                .append(MailHelper.TABLE_WITH_BORDER_TEMPLATE)
                .append(""
                + "<thead>"
                    + "<tr>"
                        + "<td valign='top' style='padding: 4px; text-indent: 0;'>Actividad</td>"
                        + "<td valign='top' style='padding: 4px; text-indent: 0;'>Responsable</td>"
                        + "<td valign='top' style='padding: 4px; text-indent: 0;'>Estado anterior</td>"
                        + "<td valign='top' style='padding: 4px; text-indent: 0;'>Estado actual</td>"
                    + "</tr>"
                + "</thead>")
                .append("<tbody>");
            activities.forEach((activity) -> {
                activityBuilder.append(""
                    + "<tr>"
                        + "<td valign='top' style='padding: 4px; text-indent: 0;'>"
                            + "<strong>").append(
                                activity.getActivityId()).append(""
                            + "</strong>:").append(
                                activity.getDescription()).append(""
                        + "</td>"
                        + "<td valign='top' style='padding: 4px; text-indent: 0;'>").append(
                            activity.getPropietaryOwner().getDescription()).append(""
                        + "</td>"
                        + "<td ").append(activityStatusStyle(activity.getPreviousStatus())).append("><i>").append(
                            activityStatusText(activity.getPreviousStatus())).append("</i>"
                        + "</td>"
                        + "<td ").append(activityStatusStyle(activity.getNewStatus())).append("><i>").append(
                            activityStatusText(activity.getNewStatus())).append("</i>"
                        + "</td>"
                    + "</tr>"
                );
            });
            activityBuilder.append("</tbody></table>");
            data.setTicketActivityStatusTable(activityBuilder.toString());
        }
        return data;
    }
    
    private String activityStatusStyle(IUpdatedActivity.ActivityStatus status) {
        if (IUpdatedActivity.ActivityStatus.COMPLETED.equals(status)) {
            return "style='background-color: #90ee90'";
        }
        return "style='background-color: #ffc0cb'";
    }
    
    private String activityStatusText(IUpdatedActivity.ActivityStatus status) {
        if (status == null) {
            return "-";
        }
        return status.toString();
    }

    public ActivityBasicMailDTO getActivityBasicMailDto(String recipientAccount, IActivity activity, IMaximoBaseDetailTicket ticket) throws UnsupportedEncodingException {
        ActivityBasicMailDTO data = new ActivityBasicMailDTO();
        // Datos de la actividad
        data.setActivityNo(activity.getActivityId());
        data.setActivityDescription(activity.getDescription());
        // Datos del Ticket
        data.setTicketCreationDate(
            Utilities.formatDateBy(ticket.getCreationDate(), "dd/MM/yyyy")
        );
        data.setTicketDescription(ticket.getDetail());
        data.setTicketNo(ticket.getSrId());
        data.setTicketSummary(ticket.getSummary());
        data.setTicketUrl(
            ticketUrl(ticket.getSrId(), recipientAccount)
        );
        return data;
    }
}
