package DPMS.FiveS;

import DPMS.DAOInterface.IAreaDAO;
import DPMS.DAOInterface.IPrintableBusinessUnitDepartmentDAO;
import DPMS.Mapping.Area;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.PrintableBusinessUnitDepartment;
import DPMS.Mapping.PrintableBusinessUnitDepartmentPK;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import mx.bnext.core.util.GridInfo;
import java.util.HashMap;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.json.annotations.SMDMethod;

/**
 *
 * <AUTHOR>
 */
public class CRUD_PrintableBusinessUnitDepartment extends CRUD_Generic<PrintableBusinessUnitDepartment> {

    @Override
    public String smd() {
        if (!isFiveSAccess()) {
            return NONE;//<---- Si no tiene ningun acceso al modulo no hace nada
        }
        return SUCCESS;
    }

    /**
     * Ejecucion de la implementacion del metodo para guardar una lista de relación de imprimible y planta-departamento
     *
     * @param businessUnitDepartmentId Planta-Departamento a relacionar
     * @param printableIds Lista de imprimibles a relacionar
     * @return GenericSaveHandle con los resultados de la operacion
     * <AUTHOR> Guadalupe Quintanilla Flores
     * @since *******
     */
    @SMDMethod
    public GenericSaveHandle bulkUpdate(Long businessUnitDepartmentId, List<Long> printableIds) {
            IPrintableBusinessUnitDepartmentDAO dao = Utilities.getBean(IPrintableBusinessUnitDepartmentDAO.class);
        return dao.save(businessUnitDepartmentId, printableIds, getLoggedUserId());
                    }
    
    @SMDMethod
    public PrintableBusinessUnitDepartment load(PrintableBusinessUnitDepartmentPK id) {
        IPrintableBusinessUnitDepartmentDAO dao = Utilities.getBean(IPrintableBusinessUnitDepartmentDAO.class);
        return dao.HQLT_findById(id);
    }
    
    @SMDMethod
    public BusinessUnitDepartmentLoad loadBusinessUnitDepartment(Long businessUnitDepartmentId) {
        IPrintableBusinessUnitDepartmentDAO dao = Utilities.getBean(IPrintableBusinessUnitDepartmentDAO.class);
        HashMap<String,Object> params = new HashMap<>();
        params.put("businessUnitDepartmentId", businessUnitDepartmentId);
        return (BusinessUnitDepartmentLoad) dao.HQL_findSimpleObject("SELECT c"
                + " FROM DPMS.Mapping.BusinessUnitDepartmentLoad c"
                + " WHERE c.id = :businessUnitDepartmentId", params);
    }
    
    
    @SMDMethod
    public GenericSaveHandle toggleStatus(PrintableBusinessUnitDepartmentPK id) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        IPrintableBusinessUnitDepartmentDAO dao = Utilities.getBean(IPrintableBusinessUnitDepartmentDAO.class);
        HashMap<String,Object> params = new HashMap<>();
        PrintableBusinessUnitDepartment entity = dao.HQLT_findById(id);
        Integer status = PrintableBusinessUnitDepartment.ACTIVE_STATUS.equals(entity.getStatus())
                ? PrintableBusinessUnitDepartment.INACTIVE_STATUS
                : PrintableBusinessUnitDepartment.ACTIVE_STATUS;
        params.put("status", status);
        params.put("id", id);
        Integer result = dao.HQL_updateByQuery("UPDATE DPMS.Mapping.PrintableBusinessUnitDepartment c SET c.status = :status WHERE id = :id",params);
        if (result == 0) {
            gsh.setOperationEstatus(0);
        } else {
            gsh.setOperationEstatus(1);
            gsh.setSavedId(entity.getId().getBusinessUnitDepartmentId() + "");
            gsh.setSuccessMessage("La informacion \"" + entity.getId().getBusinessUnitDepartmentId() + "\" ha sido modificada exitosamente.");
        }
        return gsh;
    }
    
    
    @SMDMethod
    public List<Area> loadAreaIndex(Long departmentId) {
        IAreaDAO dao = Utilities.getBean(IAreaDAO.class);
        HashMap<String,Object> params = new HashMap<>();
        params.put("departmentId",departmentId);
        return dao.HQL_findByQuery("SELECT c FROM DPMS.Mapping.Area c WHERE c.departmentId=:departmentId", params);
    }
    
    @SMDMethod
    @Override
    public GridInfo<PrintableBusinessUnitDepartment> getRows(SortedPagedFilter filter) {
        IPrintableBusinessUnitDepartmentDAO dao = Utilities.getBean(IPrintableBusinessUnitDepartmentDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        dao.setValidEntitiesFilter(filter, getLoggedUserId(), getServiciosActivos(), isAdmin(), isFiveSManager);
        return dao.getRows(filter);
    }
    
    @SMDMethod
    public GridInfo<PrintableBusinessUnitDepartment> getGroundRows(final SortedPagedFilter filter) {
        final IPrintableBusinessUnitDepartmentDAO dao = getBean(IPrintableBusinessUnitDepartmentDAO.class);
        final Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        final String departmentId = firstParamCurrentEntityId();
        if ("-1".equals(departmentId)) {
            return Utilities.EMPTY_GRID_INFO;
        } else if (departmentId != null && !departmentId.isEmpty()) {
            filter.getCriteria().put("<condition>", ""
                    + " c.id.businessUnitDepartmentId = " + Long.decode(departmentId));
        }
        dao.setValidEntitiesFilter(filter, getLoggedUserId(), getServiciosActivos(), isAdmin(), isFiveSManager);
        return dao.getRows(filter);
    }
    
    @SMDMethod
    public GridInfo<PrintableBusinessUnitDepartment> getRowsActive(SortedPagedFilter filter) {
        IPrintableBusinessUnitDepartmentDAO dao = Utilities.getBean(IPrintableBusinessUnitDepartmentDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        filter.getCriteria().put("<condition>", ""
                + "c.status = " + PrintableBusinessUnitDepartment.ACTIVE_STATUS);
        dao.setValidEntitiesFilter(filter, getLoggedUserId(), getServiciosActivos(), isAdmin(), isFiveSManager);
        return dao.getRows(filter);
    }
}
