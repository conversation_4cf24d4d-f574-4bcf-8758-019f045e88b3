package DPMS.Catalog;

import DPMS.Mapping.Measurement;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import mx.bnext.core.util.GridInfo;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.struts2.json.annotations.SMDMethod;

public class CRUD_Measurement extends CRUD_Generic<Measurement> {

    /**
     *
     * @param entity
     * @return
     */
    @SMDMethod
    public GenericSaveHandle save(final Measurement entity) {
        Measurement ent = entity;
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Catalog.CRUD_Measurement @ Save:" + Utilities.getSerializedObj(ent));
        }
        return saveEntity(ent);
    }

    public GenericSaveHandle saveEntity(Measurement ent) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        IUntypedDAO DAO = getUntypedDAO();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("GenericSaveHandle:" + ent);
        }
        //Se intenta guardar en la base de datos
        ent = DAO.makePersistent(ent);
        if (ent != null) {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage("{\"tipo\":\"add_success\"}");
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    @SMDMethod
    @Override
    public GridInfo<Measurement> getRows(SortedPagedFilter filter) {
        IUntypedDAO DAO = getUntypedDAO();
        Long count = 0L;
        List<Measurement> listResult = new ArrayList<>();
        GridInfo<Measurement> result = new GridInfo<>();

        //Primero se obtiene el número de resultados
        count = getRowCount(filter);
        result.setCount(count);

        if (count > 0) {
            listResult = DAO.HQLT_findByPagedFilter(Measurement.class, filter);
        }
        result.setData(listResult);
        result.setGridId(filter.getGridId());
        return result;
    }

    @SMDMethod
    @Override
    public Long getRowCount(SortedPagedFilter filter) {
        return getUntypedDAO().HQL_countByPagedFilter(Measurement.class, filter);
    }

    @SMDMethod
    public Set<Measurement> insertMeasurements(List<Measurement> measurements) {
        IUntypedDAO dao = getUntypedDAO();
        Set<Measurement> results = new HashSet<>();
        for (Measurement me : measurements) {
            me.setId(-1L);
            me = dao.makePersistent(me);
            results.add(me);
        }
        return results;
    }
}
