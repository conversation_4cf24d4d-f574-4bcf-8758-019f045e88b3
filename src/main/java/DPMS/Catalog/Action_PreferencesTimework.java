/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Catalog;

import Framework.Action.DefaultAction;
import Framework.Config.Utilities;

/**
 *
 * <AUTHOR>
 */
public class Action_PreferencesTimework extends DefaultAction{
    
    private Integer enableTimework = Utilities.getSettings().getEnableTimework();
    private String timeworkUrl = Utilities.getSettings().getTimeworkUrl();
    private Integer enableTimeworkSyncTest = Utilities.getSettings().getEnableTimeworkSyncTest();
    private String timeworkMails = Utilities.getSettings().getTimeworkMails();
    private String timeworkSyncType = Utilities.getSettings().getTimeworkSyncType();
    private Long maxRequestTimeout = Utilities.getSettings().getMaxRequestTimeout();
    private Long maxConnectionTimeout = Utilities.getSettings().getMaxConnectionTimeout();

    @Override
    public String execute() throws Exception {
        return super.execute();
    }

    public Integer getEnableTimework() {
        return enableTimework;
    }

    public void setEnableTimework(Integer enableTimework) {
        this.enableTimework = enableTimework;
    }

    public String getTimeworkUrl() {
        return timeworkUrl;
    }

    public void setTimeworkUrl(String timeworkUrl) {
        this.timeworkUrl = timeworkUrl;
    }

    public Integer getEnableTimeworkSyncTest() {
        return enableTimeworkSyncTest;
    }

    public void setEnableTimeworkSyncTest(Integer enableTimeworkSyncTest) {
        this.enableTimeworkSyncTest = enableTimeworkSyncTest;
    }

    public String getTimeworkMails() {
        return timeworkMails;
    }

    public void setTimeworkMails(String timeworkMails) {
        this.timeworkMails = timeworkMails;
    }

    public String getTimeworkSyncType() {
        return timeworkSyncType;
    }

    public void setTimeworkSyncType(String timeworkSyncType) {
        this.timeworkSyncType = timeworkSyncType;
    }

    public Long getMaxRequestTimeout() {
        return maxRequestTimeout;
    }

    public void setMaxRequestTimeout(Long maxRequestTimeout) {
        this.maxRequestTimeout = maxRequestTimeout;
    }

    public Long getMaxConnectionTimeout() {
        return maxConnectionTimeout;
    }

    public void setMaxConnectionTimeout(Long maxConnectionTimeout) {
        this.maxConnectionTimeout = maxConnectionTimeout;
    }
    
}
