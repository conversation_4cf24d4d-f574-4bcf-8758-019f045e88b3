package DPMS.Catalog;


import DPMS.DAOInterface.IMeterScoreDAO;
import DPMS.Mapping.Indicador;
import DPMS.Mapping.IndicadorCalificacion;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;

/**
 * 
 * <AUTHOR>
 */
public class CRUD_IndicadorCalificacion extends CRUD_Generic<IndicadorCalificacion> {
    @Override
    public String smd() {
        if(!isMeterAccess()) {
            return NONE;
        }
        return SUCCESS;
    }

    /**
     * 
     * @param entity
     * @return 
     */
    @SMDMethod
    public GenericSaveHandle save(final IndicadorCalificacion entity) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        IndicadorCalificacion ent = entity;
        IUntypedDAO DAO = getUntypedDAO();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Catalog.CRUD_IndicadorCalificacion @ Save: {}", Utilities.getSerializedObj(ent));
        }
        boolean nuevo = (ent.getId() == -1);
        //Se intenta guardar en la base de datos
        IndicadorCalificacion re = DAO.makePersistent(ent);
        if (re==null) {
            gsh.setOperationEstatus(0);
        } else {
            gsh.setOperationEstatus(1);
            System.out.println("id: "+gsh.getSavedId()+" - id - " + ent.getId());
            if(nuevo) {
                gsh.setSuccessMessage("La informacion del perfil \"" + ent.getId() + "\" ha sido guardada exitosamente.");
            } else {
                gsh.setSuccessMessage("La informacion del perfil \"" + ent.getId() + "\" ha sido modificada exitosamente.");
            }
        }
        return gsh;
    }

    @SMDMethod
    @Override
    public GridInfo<IndicadorCalificacion> getRows(SortedPagedFilter filter) {
        if(!filter.getCriteria().containsKey("deleted")){
            filter.getCriteria().put("deleted","0");
        }
        return super.getRows(filter);
    }
    
    @SMDMethod
    public GridInfo<IndicadorCalificacion> getThrashRows(SortedPagedFilter filter) {
        filter.getCriteria().put("deleted", "1");
        return super.getRows(filter);
    }
    
    @SMDMethod
    public GridInfo<IndicadorCalificacion> getRowsByUser(SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", "c.auditado.id = "+getLoggedUserId()
                    + " AND ( "
                        + " c.indicador.status  = " + Indicador.STATUS.ACTIVE.getValue()
                            + " OR ( "
                                + " c.status = " + IndicadorCalificacion.STATUS_REVISADO
                                + " AND c.indicador.status = " + Indicador.STATUS.INACTIVE.getValue()
                            + " ) "
                        + " ) "
                    + " AND c.indicador.deleted = 0 ");
        filter.getCriteria().put("deleted", "0");
        IMeterScoreDAO DAO = getBean(IMeterScoreDAO.class);
        return DAO.getRows(filter); //<--- se pone asi para que no pase por "setValidEntitiesFilter" (en este caso el filtro de usuario es sufiente)
    }
    
}