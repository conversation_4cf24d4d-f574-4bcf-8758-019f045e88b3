package DPMS.Catalog;

import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.Mapping.Periodicity;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import java.util.Date;
import org.apache.struts2.json.annotations.SMDMethod;

public class CRUD_Periodicity extends CRUD_Generic<Periodicity> {

    public static final String PREFIX = "PER-";

    /**
     *
     * @param entity
     * @return
     */
    @SMDMethod
    public GenericSaveHandle save(final Periodicity entity) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        Periodicity ent = entity;
        IPeriodicityDAO dao = getBean(IPeriodicityDAO.class);
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Catalog.CRUD_Periodicity @ Save:" + Utilities.getSerializedObj(ent));
        }
        boolean nuevo = (ent.getId() == null || ent.getId() == -1);
        //Se intenta guardar en la base de datos
        ent = dao.makePersistent(ent);
        if (ent != null) {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    /**
     * Función que obtiene la siguiente coincidencia mensual basado en los
     * ordinales semanales (tercer lunes del mes) Este método puede ser invocado
     * por ajax
     *
     * @param d la recha de incio
     * @param months el numero de meses de separación
     * @return la fecha de la siguiente coincidencia mensual
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    @SMDMethod
    public Date getNextMonthlyOccurrence(Date d, Integer months) {
        IPeriodicityDAO dao = Utilities.getBean(IPeriodicityDAO.class);
        return dao.getNextMonthlyOccurrence(d, months);
    }

}
