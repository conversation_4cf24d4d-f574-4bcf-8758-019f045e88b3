package DPMS.Catalog;

import DPMS.DAOInterface.IAreaDAO;
import DPMS.DAOInterface.ISectionDAO;
import DPMS.Mapping.Area;
import DPMS.Mapping.Section;
import DPMS.Mapping.SectionMap;
import DPMS.Mapping.SectionMapZone;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import mx.bnext.core.util.GridInfo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.json.annotations.SMDMethod;

public class CRUD_Section extends CRUD_Generic<Section> {

    /**
     * 
     * @param entity
     * @return 
     */
    @SMDMethod
    public GenericSaveHandle save(final Section entity) {
        Section ent = entity; 
        ISectionDAO dao = Utilities.getBean(ISectionDAO.class);
        return dao.save(ent);
    }

    @SMDMethod
    public Area getAreaBySectionId(Long sectionId) {
        ISectionDAO dao = Utilities.getBean(ISectionDAO.class);
        HashMap<String,Object> params = new HashMap<>();
        params.put("sectionId", sectionId);
        return (Area) dao.HQL_findSimpleObject("SELECT c.area"
                + " FROM DPMS.Mapping.Section c"
                + " WHERE c.id = :sectionId", params);
    }
    @SMDMethod
    public Map getMapBySectionId(Long sectionId) {
        ISectionDAO dao = Utilities.getBean(ISectionDAO.class);
        HashMap<String,Object> params = new HashMap<>();
        params.put("sectionId", sectionId);
        Map returnable = new HashMap();
        SectionMap map = (SectionMap) dao.HQL_findSimpleObject("SELECT c FROM "
                + SectionMap.class.getCanonicalName() + " c"
                + " WHERE c.sectionId = :sectionId", params);
        if (map != null) {   
            returnable.put("sectionMap", map);
            returnable.put("zones", dao.HQL_findByQuery("SELECT c FROM "
                    + SectionMapZone.class.getCanonicalName() + " c"
                    + " WHERE c.sectionMapId = "+map.getId()));
        }
        returnable.put("sectionId", sectionId);
        return returnable;
    }
    
    @SMDMethod
    public List<ITextHasValue> getSectionInAreas(Long id) {
        ISectionDAO DAO = Utilities.getBean(ISectionDAO.class);
        return DAO.getStrutsComboList("c.area.id = " + id);
    }
    
    @SMDMethod
    public Long getAreaId(Long sectionId) {
        ISectionDAO dao = Utilities.getBean(ISectionDAO.class);
        HashMap<String, Object> params = new HashMap<>();
        params.put("sectionId", sectionId);
        return dao.HQL_findSimpleLong("SELECT c.areaId"
                + " FROM  " + Section.class.getCanonicalName() + "  c"
                + " WHERE c.id = :sectionId", params);        
    }
    
    @SMDMethod
    public String getAreaDescription(Long areaId){
        IAreaDAO dao = Utilities.getBean(IAreaDAO.class);
        HashMap<String, Object> params = new HashMap<>();
        params.put("areaId", areaId);
        return dao.HQL_findSimpleString(""
                + " SELECT c.description + ' - ' + c.department.businessUnit.description "
                + " FROM " + Area.class.getCanonicalName() + " c "
                + " WHERE c.id = :areaId", params);
    }
    
    @SMDMethod
    public List<ITextHasValue> getSubsections(Long id) {
        ISectionDAO DAO = Utilities.getBean(ISectionDAO.class);
        return DAO.getStrutsComboList("c.parentId = " + id);
    }
    
    @Override  
    @SMDMethod
    public GridInfo getRows(SortedPagedFilter filter) {
        ISectionDAO dao = Utilities.getBean(ISectionDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        dao.setValidEntitiesFilter(filter, getLoggedUserId(), getServiciosActivos(), isAdmin(), isFiveSManager);   
        return dao.getRows(filter);
    }
}
