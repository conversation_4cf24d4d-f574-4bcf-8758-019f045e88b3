package DPMS.Catalog;

import DPMS.DAOInterface.IFilesDAO;
import DPMS.Mapping.Item;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import bnext.reference.document.FileRef;
import java.io.IOException;
import java.sql.SQLException;
import org.apache.commons.codec.binary.Base64;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.framework.file.FileManager;

public class CRUD_Temp extends CRUD_Generic<Item> {

    @SMDMethod
    public String createTemp(Long fileId) throws IOException, SQLException {
        IFilesDAO DAO = Utilities.getBean(IFilesDAO.class);
        final FileRef file = DAO.HQLT_findById(FileRef.class, fileId);
        final FileManager fileManager = new FileManager();
        byte[] content = fileManager.getFileContent(file, false, getLoggedUserDto());
        String magicUrl = new String(Base64.encodeBase64(content)); 
        return "data:image/png;base64,"+magicUrl;
    }

}
