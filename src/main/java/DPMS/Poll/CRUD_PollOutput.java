package DPMS.Poll;

import DPMS.Mapping.PollOutput;
import DPMS.Mapping.User;
import Framework.Config.SortedPagedFilter;
import Framework.DAO.CRUD_Generic;
import static com.opensymphony.xwork2.Action.NONE;
import static com.opensymphony.xwork2.Action.SUCCESS;
import isoblock.surveys.dao.hibernate.Survey;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;

/**
 *
 * <AUTHOR>
 */
public class CRUD_PollOutput extends CRUD_Generic<PollOutput> {

    private static final long serialVersionUID = 1L;

    @Override
    public String smd() {
        if (isAdmin() || isPollAccess()) {
            return SUCCESS;
        }
            return NONE;
    }

    @Override
    @SMDMethod
    public GridInfo<PollOutput> getRows(SortedPagedFilter filter) {
        super.setClassName(PollOutput.class.getCanonicalName());
        if (!isAdmin()) {
            filter.getCriteria().put("<condition>", ""
                    + " exists ( "
                        + " SELECT pu.une.id "
                        + " FROM " + User.class.getCanonicalName() + " u,"
                        + "     " + Survey.class.getCanonicalName() + " survey "
                        + " JOIN u.puestos pu "
                        + " JOIN survey.globals.obj.unes survey_une "
                        + " WHERE survey_une.id = pu.une.id "
                        + " AND survey.id = c.surveyId"
                        + " AND u.id = " + getLoggedUserId()
                    + " ) ");
        }
        return super.getRows(filter);
    }

}
