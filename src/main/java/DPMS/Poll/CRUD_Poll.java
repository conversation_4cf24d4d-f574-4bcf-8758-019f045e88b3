package DPMS.Poll;

import DPMS.DAOInterface.IPollDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Persistable;
import DPMS.Mapping.Poll;
import DPMS.Mapping.PollList;
import DPMS.Mapping.Position;
import DPMS.Mapping.User;
import DPMS.Mapping.UserPosition;
import Framework.Action.SessionViewer;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import static com.opensymphony.xwork2.Action.NONE;
import static com.opensymphony.xwork2.Action.SUCCESS;
import isoblock.surveys.dao.hibernate.Survey;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.poll.core.IPoll;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
public class CRUD_Poll extends CRUD_Generic<Poll> {

    private static final long serialVersionUID = 1L;

    @Override
    public String smd() {
        if(!isPollAccess()) {
            return NONE;//<---- Si no tiene acceso al modulo no hace nada
        }
        return SUCCESS;
    }

    
    @Override
    protected boolean hasSaveAccess(Persistable backup) {
        return this.isAdmin()
                || getLoggedUserServices().contains(ProfileServices.SURVEY_MANAGER);
    }
    
    @SMDMethod
    public GenericSaveHandle save(final Poll poll) throws QMSException {
        getLogger().trace(">> Guardando {}", className);
        final IPollDAO pollDao = getBean(IPollDAO.class);
        final GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.setErrorMessage("");

        if (poll == null) {
            gsh.setErrorMessage("El objeto que se va a guardar no esta correctamente configurado (revisar struts.xml)");
            return gsh;
        }

        boolean nuevo = poll.getId().equals(-1L);
        if (!hasSaveAccess(poll)) {
            gsh.setErrorMessage("Usted no tiene permiso para guardar");
            return gsh;
        }
        final IPoll savedPoll;
        if (Utilities.truncDate(poll.getDteStart()).after(Utilities.today())) {
            savedPoll = pollDao.savePlannedPoll(poll, nuevo, getLoggedUserDto());
        } else if (nuevo) {
            savedPoll = pollDao.savePollInProcess(poll, true, getLoggedUserDto());
        } else {
            final Poll loadPoll = pollDao.HQLT_findById(poll.getId());
            if (poll.getDteStart() != null) {
                loadPoll.setDteStart(poll.getDteStart());
            }
            if (poll.getDteEnd() != null) {
                loadPoll.setDteEnd(poll.getDteEnd());
            }
            if (poll.getDteAnticipation() != null) {
                loadPoll.setDteAnticipation(poll.getDteAnticipation());
            }
            savedPoll = pollDao.savePollInProcess(loadPoll, false, getLoggedUserDto());
        }
        
        if (savedPoll != null && gsh.getErrorMessage().isEmpty()) {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage(nuevo ? SessionViewer.ADD_SUCCESS : SessionViewer.EDIT_SUCCESS);
        } else {
            gsh.setOperationEstatus(0);
            gsh.setSuccessMessage(" Ocurrio un error ");
        }
        return gsh;
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsRespondents(final SortedPagedFilter filter) {
        IUserDAO dao = getBean(IUserDAO.class);
        String condition;
        final String[] currentEntityId = getCurrentEntityId();
        if (currentEntityId.length > 1 
                && !Utilities.isInteger(currentEntityId[0])
                && !Utilities.isInteger(currentEntityId[1])
                ) {
            //si no es un numero... extraigo los numeros
            Long id = Long.parseLong(currentEntityId[0].replaceAll("[^0-9\\-]", ""));   //<--- obtengo el id de la encuesta;
            currentEntityId[0] = "0";
            currentEntityId[1] = "0";
            condition = ""
                    + " exists ("
                        + " SELECT resp.id"
                        + " FROM " + Poll.class.getCanonicalName() + " enc "
                        + " JOIN enc.respondents resp "
                        + " WHERE enc.id = " + id
                        + " AND resp.id = c.id "
                    + " )"
                    + " OR " + Utilities.arrayToSelectInFullCondition(currentEntityId, "c.id");
        } else {
            condition = "1=0";
        }
        return dao.HQL_getRows(""
                + " SELECT new Map("
                    + " c.id as id,"
                    + " c.description as description,"
                    + " c.cuenta as account,"
                    + " def_org.description as organizationalUnit,"
                    + " def_bu.description as businessUnit"
                + ")"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " JOIN c.defaultWorkflowPosition.corp def_org"
                + " JOIN c.defaultWorkflowPosition.une def_bu"
                + " WHERE " + condition
                + " GROUP BY"
                    + " c.id "
                    + " ,c.description "
                    + " ,c.description "
                    + " ,c.cuenta "
                    + " ,def_org.description "
                    + " ,def_bu.description ", filter);
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsRespondentsSearch(SortedPagedFilter filter) {
        IUserDAO dao = getBean(IUserDAO.class);
        final String[] currentEntityId = getCurrentEntityId();
        if (currentEntityId.length > 1
                && !Utilities.isInteger(currentEntityId[1])) {
            //si no es un numero... extraigo los numeros
            Long surveyId = Long.parseLong(currentEntityId[1].replaceAll("[^0-9\\-]", ""));   //<--- obtengo el id del cuestionarios
            currentEntityId[0] = "0";
            currentEntityId[1] = "0";
            filter.getCriteria().put("<condition>",
                (isAdmin()
                    ? ""
                    //si soy admin obtengo todos los usuarios que se pueden encuestar con el cuestionario dado (en relacion a la UNE)
                    + " exists ( "
                        + " SELECT 1 "
                        + " FROM " + Survey.class.getCanonicalName() + " survey "
                        + " JOIN survey.globals.obj.unes survey_une "
                        + " WHERE survey_une.id = bu.id "
                        + " AND survey.id = " + surveyId
                    + " ) "
                    : ""//
                    //  Si NO soy admin obtengo todos los usuarios que se pueden encuestar con el cuestionario dado 
                    //      >> En relacion a la UNE del cuestionario dado
                    //      >> Con cruze de las UNE's a las que mi usuario tiene acceso
                    + " ( "
                        + " exists ( "
                            + " SELECT 1 "
                            + " FROM " 
                                + User.class.getCanonicalName() + " u"
                                + "," + Survey.class.getCanonicalName() + " survey "
                            + " JOIN u.puestos upu "
                            + " JOIN survey.globals.obj.unes survey_une "
                            + " WHERE"
                                + " bu.id = upu.une.id"
                                + " AND survey_une.id = upu.une.id "
                                + " AND survey.id = " + surveyId
                                + " AND u.id = " + getLoggedUserId()
                        + " ) "
                        + " OR exists ( "
                            + " SELECT 1 "
                            + " FROM " 
                                + User.class.getCanonicalName() + " u"
                                + "," + Survey.class.getCanonicalName() + " survey "
                            + " JOIN survey.globals.obj.unes survey_une "
                            + " JOIN u.puestos upu "
                            + " WHERE"
                                + " bu.id = upu.une.id"
                                + " AND survey_une.id = u.businessUnitId  "
                                + " AND survey.id = " + surveyId
                                + " AND u.id = " + getLoggedUserId()
                        + " ) "
                    + " ) ")
                    + " AND c.status=1 " //Solo los que esten activas
            );
        }
        ProfileServices[] services = new ProfileServices[] {
                ProfileServices.SURVEY_RESPONDENT,
                ProfileServices.SURVEY_MANAGER,
                ProfileServices.SURVEY_SUPERVISOR
        };
        return dao.HQL_getRows(""
                + " SELECT new Map("
                    + " c.id as id,"
                    + " c.description as description,"
                    + " c.cuenta as account,"
                    + " def_org.description as organizationalUnit,"
                    + " def_bu.description as businessUnit"
                + ")"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " , " + UserPosition.class.getCanonicalName() + " user_pos"
                + " ," + Position.class.getCanonicalName() + " pos"
                + " JOIN c.defaultWorkflowPosition.corp def_org"
                + " JOIN c.defaultWorkflowPosition.une def_bu"
                + " JOIN pos.perfil prof"
                + " JOIN pos.corp org"
                + " JOIN pos.une bu"
                + " WHERE c.id = user_pos.userId"
                + " AND user_pos.positionId = pos.id"
                + " AND 1 IN (" +  ProfileServices.getCodedServices("prof", services) + ")"
                + " GROUP BY"
                    + " c.id "
                    + " ,c.description "
                    + " ,c.cuenta "
                    + " ,def_org.description "
                    + " ,def_bu.description ", filter);
    }

    @Override
    @SMDMethod
    public GridInfo<PollList> getRows(SortedPagedFilter filter) {
        super.setClassName(PollList.class.getCanonicalName());
        if (!isAdmin()) {
            filter.getCriteria().put("<condition>", ""
                + " exists ( "
                    + " SELECT pu.une.id "
                    + " FROM " + User.class.getCanonicalName() + " u,"
                    + "     " + Survey.class.getCanonicalName() +" survey "
                    + " JOIN u.puestos pu "
                    + " JOIN survey.globals.obj.unes survey_une "
                    + " WHERE survey_une.id = pu.une.id "
                    + " AND survey.id = c.surveyId"
                    + " AND u.id = " + getLoggedUserId()
                + " ) ");     
        }
        return super.getRows(filter);
    }
    
}