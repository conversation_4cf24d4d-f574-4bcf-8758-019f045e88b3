package DPMS.ActiveDirectoryInterface;

/**
 *
 * <AUTHOR>
 */
public class LdapException extends RuntimeException {

    private static final long serialVersionUID = 1L;
   
    public LdapException(String message) {
        super(message);
    }

    public LdapException(String message, Throwable cause) {
        super(message, cause);
    }

    @Override
    public String toString() {
        return "LdapException{" + "message=" + getMessage() + '}';
    }
    
}
