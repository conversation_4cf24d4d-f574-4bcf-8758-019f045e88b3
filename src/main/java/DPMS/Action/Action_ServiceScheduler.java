package DPMS.Action;

import DPMS.DAOInterface.IDeviceDAO;
import DPMS.DAOInterface.IPatternDAO;
import DPMS.DAOInterface.IServiceScheduleDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Pattern;
import DPMS.Mapping.ServiceSchedule;
import DPMS.Mapping.ServiceType;
import DPMS.Mapping.Unit;
import DPMS.Mapping.Variable;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.UserHasValue;
import bnext.reference.UserRef;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

public class Action_ServiceScheduler extends DefaultAction {
    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> devices;
    private List<ITextHasValue> serviceTypes;
    private List<UserHasValue> attendant;
    private List<ITextHasValue> variable;
    private List<ITextHasValue> unit;
    private List<ITextHasValue> pattern;
    private boolean unsheduledService = false;
    private String task;
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        IDeviceDAO dao = getBean(IDeviceDAO.class);
        ProfileServices[] mine = {ProfileServices.ALL};
        ProfileServices[] theirs = {ProfileServices.ALL};
        devices = dao.getDevicesInMyBusinessUnit(-1L, isAdmin(), getLoggedUserId(), theirs);
        devices.add(0, new TextHasValue(SELECCIONE, "")); 
        serviceTypes = dao.getStrutsComboList(ServiceType.class, false, null, 0);
        serviceTypes.add(0, new TextHasValue(SELECCIONE, ""));
        IUserDAO usrDAO = getBean(IUserDAO.class);
        ProfileServices[] serviceRealizer = {ProfileServices.DEVICE_REALIZE_SERVICE};        
        attendant = usrDAO.getUsersInBusinessUnitOf(false, this.getLoggedUserId(), mine, serviceRealizer);
        if (getId() != null) {
            Long responsibleId = getBean(IServiceScheduleDAO.class).HQLT_findById(Long.parseLong(getId())).getResponsibleId();
            Boolean addUserToList = true;
            for (ITextHasValue user : attendant) {
                if (user.getValue().toString().equals(responsibleId.toString())) {
                    addUserToList = false;
                    break;
                }
            }
            if (addUserToList) {
                UserRef user = getBean(IServiceScheduleDAO.class).HQLT_findById(UserRef.class, responsibleId);
                attendant.add(new UserHasValue("" + user.getDescription(), user.getId()));
            }
        }
        attendant.add(0, new UserHasValue(SELECCIONE, "")); 
        variable = dao.getStrutsComboList(Variable.class, false, null, 0);
        variable.add(0, new TextHasValue(SELECCIONE, ""));
        unit = dao.getStrutsComboList(Unit.class, false, null, 0);
        unit.add(0, new TextHasValue(SELECCIONE, ""));
        
        IPatternDAO patternDAO = getBean(IPatternDAO.class);
        pattern = patternDAO.getStrutsComboList("c.businessUnitId IN (" 
                + usrDAO.getBusinessUnitsIds(getLoggedUserId().toString(), false, isCorporative())
                +") AND c.deleted = " +Pattern.IS_NOT_DELETED);
        pattern.add(0, new TextHasValue(SELECCIONE, ""));
        
        if (this.getId() != null && !this.getId().isEmpty() && !"-1".equals(this.getId())) {
            IServiceScheduleDAO shuDao = getBean(IServiceScheduleDAO.class);
            ServiceSchedule s = shuDao.HQLT_findById(Long.parseLong(this.getId()));
            if(s != null){
                if(ServiceSchedule.UNSCHEDULED.equals(s.getStatus())){
                    unsheduledService = true;
                }
            }
        }
        return super.execute();
    }

    
    public List<ITextHasValue> getDevices() {
        return devices;
    }

    @StrutsParameter
    public void setDevices(List<ITextHasValue> devices) {
        this.devices = devices;
    }

    public List<ITextHasValue> getServiceTypes() {
        return serviceTypes;
    }

    @StrutsParameter
    public void setServiceTypes(List<ITextHasValue> serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public List<UserHasValue> getAttendant() {
        return attendant;
    }

    @StrutsParameter
    public void setAttendant(List<UserHasValue> attendant) {
        this.attendant = attendant;
    }

    public List<ITextHasValue> getVariable() {
        return variable;
    }

    @StrutsParameter
    public void setVariable(List<ITextHasValue> variable) {
        this.variable = variable;
    }

    public List<ITextHasValue> getUnit() {
        return unit;
    }

    @StrutsParameter
    public void setUnit(List<ITextHasValue> unit) {
        this.unit = unit;
    }

    public List<ITextHasValue> getPattern() {
        return pattern;
    }

    @StrutsParameter
    public void setPattern(List<ITextHasValue> pattern) {
        this.pattern = pattern;
    }

    /**
     * @return the unplanedService
     */
    public boolean isUnsheduledService() {
        return unsheduledService;
    }

    /**
     * @param unplanedService the unplanedService to set
     */
    public void setUnsheduledService(boolean unplanedService) {
        this.unsheduledService = unplanedService;
    }

    public String getTask() {
        return task;
    }

    @StrutsParameter
    public void setTask(String task) {
        this.task = task;
    }

 }
