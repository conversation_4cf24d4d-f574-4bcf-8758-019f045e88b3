package DPMS.Action;

import DPMS.ActiveDirectoryInterface.ActiveDirectoryUtil;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Settings;
import DPMS.Mapping.SupportedLanguages;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.UserHasValue;
import Framework.Config.Utilities;
import bnext.licensing.LicenseUtil;
import com.google.gson.Gson;
import mx.bnext.access.ProfileServices;
import mx.bnext.licensing.Schema;
import qms.access.dto.UserEditionInfoDTO;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR> Lima<PERSON>
 */
public class Action_User extends DefaultAction {

    private static final long serialVersionUID = 1L;

    private List<UserHasValue> boss;
    private UserEditionInfoDTO user;
    private List<ITextHasValue> usersAll;
    private List<ITextHasValue> lang;
    private Boolean availableAuthTypeIntegrated;
    private Boolean availableAuthTypeLdap;
    private Boolean availableAuthTypeLandingPage;
    private Boolean availableAuthTypeOidc;
    private Boolean userDirectBossOptional;

    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        final Long userId = parseUserId();
        user = loadUser(userId);
        if (userId == null || userId == -1L) {
            if (!isAdmin() && !getLoggedUserServices().contains(ProfileServices.ADMON_ACCESOS)) {
                return NO_ACCESS;
            }
        } else if (!isAdmin()) {
            if (user == null
                    || (user.isHasJobs()
                    && !user.isSameBusinessUnitAsCurrentUser() && (!user.isSameOrganizationalUnitAsCurrentUser() || !user.isCurrentUserCorporative()))
                    || (!user.isHasJobs() && !user.isCurrentUserEscalationManager())) {
                return NO_ACCESS;
            }
        }
        userDirectBossOptional = Utilities.getSettings().getUserDirectBossOptional();
        availableAuthTypeIntegrated = Objects.equals(Utilities.getSettings().getEnableLoginForm(), 1);
        final Settings.SSO_STATUS ssoStatus = ActiveDirectoryUtil.status();
        availableAuthTypeLdap = Objects.equals(ssoStatus, Settings.SSO_STATUS.SSO_ONLY) || Objects.equals(ssoStatus, Settings.SSO_STATUS.SSO_MIXED_LOGIN);
        availableAuthTypeLandingPage = Objects.equals(Utilities.getSettings().getEnabledLandingPage(), 1);
        availableAuthTypeOidc = Objects.equals(Utilities.getSettings().getOidcEnabled(), 1);
        ProfileServices[] serv = {ProfileServices.ALL};
        final IUserDAO dao = getBean(IUserDAO.class);
        List<Long> bunId = dao.getBusinessUnits(getLoggedUserId(), isAdmin(), isCorporative());
        boss = dao.getUsersInBusinessUnits(user.getBossId(), bunId, serv);
        if (getId() != null && !"-1".equals(getId())) {
            boss.remove(new UserHasValue("", Long.valueOf(getId())));
        }
        boss.add(0, new UserHasValue("", ""));
        usersAll = dao.getUsersAll();
        lang = dao.getStrutsComboList(SupportedLanguages.class, "description", "c.id IN (" + LicenseUtil.filterSupportedLanguages() + ")");
        lang.add(0, new TextHasValue(getTag("selectAnOption"), ""));
        return super.execute();
    }

    public List<UserHasValue> getBoss() {
        return boss;
    }

    public void setBoss(List<UserHasValue> boss) {
        this.boss = boss;
    }

    public List<ITextHasValue> getLang() {
        return lang;
    }

    public void setLang(List<ITextHasValue> lang) {
        this.lang = lang;
    }

    public List<ITextHasValue> getUsersAll() {
        return usersAll;
    }

    public void setUsersAll(List<ITextHasValue> usersAll) {
        this.usersAll = usersAll;
    }

    public UserEditionInfoDTO getEntity() {
        return user;
    }

    private Long parseUserId() {
        String id = getId();
        if (id == null
                || "".equals(id)
                || "-1L".equals(id)) {
            return -1L;
        }
        return Long.valueOf(id);
    }

    private UserEditionInfoDTO loadUser(final Long id) {
        if (id == null || id == -1L) {
            return new UserEditionInfoDTO();
        }
        final IUserDAO dao = getBean(IUserDAO.class);
        final UserEditionInfoDTO entity = dao.getUserEditionInfoById(id, getLoggedUserDto());
        return entity;
    }

    public Schema getSchema() {
        String licenseCode = user.getLicenseCode();
        return LicenseUtil.getSchema(licenseCode);
    }

    public String getSerializedSchemas() {
        return LicenseUtil.getSerializedSchemas();
    }

    public String getSerializedLicenseCodes() {
        return new Gson().toJson(LicenseUtil.getLicenseCodes());
    }

    public Boolean getAvailableAuthTypeIntegrated() {
        return availableAuthTypeIntegrated;
    }

    public Boolean getAvailableAuthTypeLdap() {
        return availableAuthTypeLdap;
    }

    public Boolean getAvailableAuthTypeLandingPage() {
        return availableAuthTypeLandingPage;
    }

    public Boolean getAvailableAuthTypeOidc() {
        return availableAuthTypeOidc;
    }

    public Boolean getUserDirectBossOptional() {
        return userDirectBossOptional;
    }
}
