package DPMS.Action;

import DPMS.Mapping.AuditScore;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_AuditScore extends DefaultAction {

    @Override
    public String execute() throws Exception {
        return super.execute(); //To change body of generated methods, choose Tools | Templates.
    }

    public AuditScore getEntity() {
        return loadAuditScore(Long.parseLong(getId()));
    }

    protected AuditScore loadAuditScore(Long id) {
        if(id == null || id.equals(-1L)) {
            return new AuditScore();
        }
        return getUntypedDAO().HQLT_findById(AuditScore.class, id);
    }

    public List<ITextHasValue> getYesNoList(){
        return Utilities.YES_NO_LIST;
    }
}
