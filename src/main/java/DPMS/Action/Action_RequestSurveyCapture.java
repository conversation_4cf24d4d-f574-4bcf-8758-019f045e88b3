package DPMS.Action;

import DPMS.DAOInterface.IActionDAO;
import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Action;
import DPMS.Mapping.ActionSources;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Document;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.Request;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.struts2.action.SurveyCaptureAction;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.regex.Pattern;
import mx.bnext.access.ProfileServices;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.DAOInterface.IActivityDAO;
import qms.activity.dto.ActivitySurveyCaptureDto;
import qms.activity.entity.Activity;
import qms.activity.entity.ActivityType;
import qms.activity.util.CommitmentTask;
import qms.document.core.ISurveyCaptureAction;
import qms.document.dto.RequestSaveHandle;
import qms.form.dao.IFormCaptureDAO;
import qms.form.entity.FormRequest;
import qms.form.pending.imp.ToAuthorizeAdjustmentRequest;
import qms.form.pending.imp.ToVerifyFormAdjustmentRequest;
import qms.form.util.FormRequestType;
import qms.form.util.IOutstandingSurveysLoadAnswers;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.framework.util.CacheRegion;
import qms.framework.util.MeasureTime;
import qms.meeting.entity.Meeting;
import qms.survey.dto.SurveyCaptureConfig;
import qms.survey.util.SurveyCaptureUtil;
import qms.timesheet.dto.TimesheetDto;
import qms.util.FormUtil;
import qms.util.QMSException;
import qms.util.annotations.ExcludeFromAOP;
import qms.util.dto.FieldObjectAttributes;
import qms.util.dto.FormCurrentApprover;
import qms.workflow.util.WorkflowAuthRole;
import qms.workflow.util.WorkflowRequestStatus;
import static Framework.Config.StandardEntity.IS_NOT_DELETED;

/**
 * Created on : Feb 24, 2015, 1:40:53 PM
 *
 * <AUTHOR> Carlos Limas @ Block Networks S.A. de C.V.
 *
 * Es para solicitudes de llenado, crea automaticamente el registro de OutstandingSurvey para iniciar el llenado
 */
@ExcludeFromAOP
public class Action_RequestSurveyCapture extends SurveyCaptureAction implements ISurveyCaptureAction {

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Boolean getViewFinder() {
        return viewFinder;
    }

    @Override
    public void setViewFinder(Boolean viewFinder) {
        this.viewFinder = viewFinder;
    }

    private static final String NO_VIEW_ALLOWED = Action_DocumentHandle.NO_VIEW_ALLOWED;
    private static final String PENDING_ATTENDED_RESULT = "attended";
    private static final List<Pattern> PENDING_EXCLUDE_PROPERTIES = Collections.singletonList(
            Pattern.compile("cuestionario|request|formReopenRequest|formCancelRequest|formAdjustRequest")
    );
    private Long findingId = null;
    private Long meetingId = null;
    private Long outstandingSurveyId = null;
    private Long currentAutorizationPoolIndex;
    private Long currentAutorizationPoolDetailId;
    private Boolean unrestrictedAccess = false;
    private Boolean validIndex = true;
    private Long nextFillUser = null;
    private String requestAuthor = null;
    private String impersonateUserId = null;
    private int documentStatus;
    private int surveyStatus;
    /**
     * El indice recibido aqui será pintado en la vista previa
     */
    private Integer invalidAutorizationPoolIndex = -1;
    private Integer currentRecurrence;

    private String serializedCancelableFillIndexes;
    private Boolean showCancelFillForm = false;
    private Boolean cancelAny = false;
    private Long activityId = null;
    private Boolean viewFinder = false;
    private IOutstandingSurveysLoadAnswers pending;
    private Boolean allowCaptureTime;
    private String activityPlannedCode; // propiedades para el registro de timesheet desde formularios
    private String activityPlannedDescription; // propiedades para el registro de timesheet desde formularios
    private Boolean continueFill = false;
    private boolean enableOpenPdfFiles = false;
    private Long documentTypeId = null;

    @Override
    public String execute() throws Exception {
        String result;
        ElapsedDataDTO tFullStart = MeasureTime.start(getClass());
        try {
            result = super.defaultActionExecute();
            surveyAllowCaptureTime();
            if (!SUCCESS.equals(result)) {
                return result;
            }
            if (getDocumentMasterId() == null && getDocumentId() != null && getId() != null && "REQUESTOR".equals(getRequestMode())) {
                // Ligas rezagadas que los usuarios guardaron en favoritos
                fillMasterIdFromDocumentId();
            }
            if (getDocumentMasterId() != null) {
                fillFromMasterId();
            }
            if (getDocumentMasterId() != null && getDocumentId() == null) {
                return NO_ACCESS;
            }
            ElapsedDataDTO tStart = MeasureTime.start(getClass());
            final SurveyCaptureConfig config = SurveyCaptureUtil.getDefaultConfig(getId(), getLoggedUserDto());
            result = doExecute(config);
            MeasureTime.stop(tStart, "Elapsed time LOADING & GENERATING survey HTML for ID " + getId());
            IDocumentDAO daoDocument = getBean(IDocumentDAO.class);
            if (SUCCESS.equals(result) && getDocumentId() != null && getDocumentId() > 0) {
                daoDocument.readDocument(getDocumentId(), getLoggedUserDto());
            }
            if (SUCCESS.equals(result) && getPending() != null && getPending().getDocumentId() > 0 && getPending().getCreatorUserId() > 0) {
                IUserDAO userDao = getBean(IUserDAO.class);
                setRequestAuthor(userDao.getUserRef(getPending().getCreatorUserId()).getDescription());
            }
        } finally {
            if (getImpersonatingUserId() != null) {
                resetUserValues();
                this.impersonateUserId = null;
            }
            MeasureTime.stop(tFullStart, "Full elapsed time LOADING FILL FORM with ID " + getId());
        }
        return result;
    }

    @Override
    public String doExecute(final SurveyCaptureConfig config) throws IOException, QMSException, ExecutionException {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Action.Action_RequestSurveyCapture.execute() - requestMode: {}", getRequestMode());
        }
        if (getId() != null && getId().startsWith("O")) {
            setOutstandingSurveyId(Long.valueOf(getId().replace("O", "")));
        }
        this.progressButtonAvailable();
        if (impersonateUserId != null
                && !getLoggedUserId().toString().equals(impersonateUserId)
                && getLoggedUserServices().contains(ProfileServices.getServicio("formFillImpersonation"))){
            setImpersonatingUserId(Long.valueOf(impersonateUserId));
        }
        IRequestDAO dao = getBean(IRequestDAO.class);
        if (getDocumentId() != null && (this.getId() == null || this.getId().equals("fill"))) {
            setSurveyIdFromDocumentId(dao);
        }
        if (getRequestId()!= null && (this.getId() == null || this.getId().equals("-1"))) {
            setIdFromRequestId(config, dao);
        }
        if (getOutstandingSurveyId() != null) {
            loadAuthPoolIndexData();
            setOutstandingArchived(loadOutstandingArchived(getOutstandingSurveyId()));
        }
        if (getActivityId() != null) {
            final ElapsedDataDTO tStart = MeasureTime.start(getClass());
            try {
                // La funcionalidad de cargar información está pensada solo para actividades sin periodicidad
                IActivityDAO activityDao = Utilities.getBean(IActivityDAO.class);
                ActivitySurveyCaptureDto data = activityDao.getActivitySurveyCaptureDto(activityId);
                if (this.getRequestMode().equals("PREVIEW")) {
                    switch (this.getId()) {
                        case "VERIFY":
                            if (CommitmentTask.IMPLEMENTATION.isEqual(data)) {
                                activityId = data.getPlannedVerificationActivityId();
                            }
                            data = activityDao.getActivitySurveyCaptureDto(activityId);
                            if (
                                CommitmentTask.IMPLEMENTATION.isEqual(data)
                                || data.getRecurrent() == 1
                            ) {
                                return NO_VIEW_ALLOWED;
                            }
                            List<ActivitySurveyCaptureDto> implementations = activityDao.getImplementationsActivitySurveyCaptureDto(
                                    activityId
                            );
                            if (implementations.size() != 1) {
                                return NO_VIEW_ALLOWED;
                            }
                            setOutstandingSurveyId(implementations.get(0).getOutstandingSurveyId());
                            setDocumentId(implementations.get(0).getDocumentId());
                            setDocumentDescription(implementations.get(0).getDocumentDescription());
                            unrestrictedAccess = true;
                            break;
                        case "IMPLEMENT":
                            if (
                                CommitmentTask.VERIFICATION.isEqual(data)
                                || data.getRecurrent() == 1
                            ) {
                                return NO_VIEW_ALLOWED;
                            }
                            setOutstandingSurveyId(data.getOutstandingSurveyId());
                            setDocumentId(data.getDocumentId());
                            setDocumentDescription(data.getDocumentDescription());
                            break;
                        default:
                            setOutstandingSurveyId(data.getOutstandingSurveyId());
                            setDocumentId(data.getDocumentId());
                            setDocumentDescription(data.getDocumentDescription());
                            break;
                    }
                    if (getOutstandingSurveyId() != null) {
                        setId("O" + getOutstandingSurveyId());
                        SurveyCaptureUtil.updatePendingData(config, getOutstandingSurveyId(), getLoggedUserDto());
                    } else {
                        setSurveyIdFromDocumentId(dao);
                    }
                } else if (getActivityId() != null && this.getRequestMode().equals("REQUESTOR")) {
                    // Se verifica si hubo cambio de formulario
                    if (data.getOutstandingSurveyId() != null) {
                        // Se revisa si el registro ya existente aun es valido o por cambio de formulario se debe de ignorar
                        String queryOut = " SELECT new map(c.documentId as documentId, c.status as status) "
                                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
                                + " WHERE c.id = :id ";
                        Map<String, Object> outDate = dao.HQL_findSimpleMap(
                                queryOut,
                                ImmutableMap.of("id", data.getOutstandingSurveyId()),
                                true,
                                CacheRegion.ACTIVITY,
                                0
                        );
                        Short outStatus = Short.parseShort(outDate.get("status").toString());
                        Long outDocumentId = Long.parseLong(outDate.get("documentId").toString());
                        Long latestDocumentId = getDocumentIdByMasterId();
                        boolean need_update = Objects.equals(outStatus, OutstandingSurveys.ESTATUS_NUEVO) && latestDocumentId > outDocumentId;
                        if (need_update) {
                            setOutstandingSurveyId(null);
                        } else {
                            setOutstandingSurveyId(data.getOutstandingSurveyId());
                        }
                    } else {
                        // Nuevo registro
                        setOutstandingSurveyId(null);
                    }
                }
            } finally {
                MeasureTime.stop(tStart, "Loading activitiy info for ID " + getId());
            }
        }
        if (!config.isNoSessionMode()) {
            final ElapsedDataDTO tStart = MeasureTime.start(getClass());
            try {
                if (findingId != null && findingId > 0) {
                    final IActionDAO findingDao = getBean(IActionDAO.class);
                    final boolean access = findingDao.hasSpecialAccessToForm(findingId, getLoggedUserId(), isAdmin());
                    if (!access) {
                        return NO_VIEW_ALLOWED;
                    }
                }
                fillDocumentData();
                final boolean isNewDocumentOrAuthorAccess = isNewDocumentOrAuthorAccessOrAttendingPending();
                final IDocumentDAO documentDao = getBean(IDocumentDAO.class);
                if (isNewDocumentOrAuthorAccess) {
                    getLogger().debug(" Se autoriza el acceso al REQUEST {} por que se tiene acceso a la carpeta, aún no existe un documento creado para esta solicitud o es el solicitante", getRequestId());
                } else {
                    if (getDocumentId() == null) {
                        return NO_VIEW_ALLOWED;
                    }
                    if (
                        !unrestrictedAccess
                        && !documentDao.hasFormPending(outstandingSurveyId, getLoggedUserId())
                        && !documentDao.hasDocumentAccess(
                            getDocumentId(),
                            isAdmin(),
                            getLoggedUserId(),
                            getLoggedUserServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS),
                            getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)
                        )
                    ) {
                        return NO_VIEW_ALLOWED;
                    }
                }
            } finally {
                MeasureTime.stop(tStart, "Valdating document access for ID " + getId());
            }
        }
        final ElapsedDataDTO pStart = MeasureTime.start(getClass());
        if (getRequestMode() == null) {
            return INVALID;
        }
        SurveyRequestMode mode = SurveyRequestMode.valueOf(getRequestMode().toUpperCase());
        if (getOutstandingArchived()) {
            mode = SurveyRequestMode.PREVIEW;
            setTask(TASK_GOING_TO_PREVIEW);
        }
        switch (mode) {
            case REQUESTOR:
                pending = getPendingByRequestorMode(config, dao);
                setSurveyStatus(pending.getEstatus());
                isValidIndex(dao, pending);
                loadCancelablesIndexes(dao);
                break;
            case PREVIEW:
                pending = getPendingByPreviewMode(dao);
                if (pending != null) {
                    setSurveyStatus(pending.getEstatus());
                }
                final ISurveyCaptureDAO captureDao = getBean(ISurveyCaptureDAO.class);
                final Long authorizationPoolIndex = captureDao.getOutstandingAutorizationPoolIndex(outstandingSurveyId);
                setCurrentAutorizationPoolIndex(authorizationPoolIndex);
                break;
            case FILL:
                pending = getPendingByFillMode(dao);
                if (pending != null) {
                    setSurveyStatus(pending.getEstatus());
                }
                if (shouldForcePreviewMode(pending)) {
                    setPreviewMode(true);
                    setRequestMode(SurveyRequestMode.PREVIEW.name());
                } else {
                    if (currentAutorizationPoolIndex == null) {
                        return INVALID;
                    }
                    if (this.getRequestId() != null) {
                        setUnrestrictedAccess(!loadRestrictAccess(this.getRequestId()));
                    }
                    setHasAccessToDefitiveCancelWithAuth(
                        getLoggedUserServices().contains(ProfileServices.SPECIAL_FORM_CANCELATION_APPROVER) ||
                        getLoggedUserServices().contains(ProfileServices.FORMULARIO_CONTROL) ||
                        getLoggedUserDto().isAdmin()
                    );
                    final FormCurrentApprover data = getFormAdjustmentRequestData();
                    setOutstandingStatusData(config, data);
                    setFormAdjustmentRequestData(data);
                    setSignatureFieldObjectAttributes(outstandingSurveyId, currentAutorizationPoolIndex);
                    defineAdminAuth(config);
                }
                isValidIndex(dao, pending);
                loadCancelablesIndexes(dao);
                break;
            default:
                pending = getPendingByFillMode(dao);
                if (shouldForcePreviewMode(pending)) {
                    setPreviewMode(true);
                }
                isValidIndex(dao, pending);
                loadCancelablesIndexes(dao);
                break;
        }
        if (isPreviewMode() && !getOutstandingArchived()) {
            FormCurrentApprover data = getOutstandingStatusData();
            setOutstandingStatusData(config, data);
            setFormReopenRequestData();
            setFormCancelRequestData();
            setFormAdjustmentRequestData();
            checkForAdjustmentRequest(dao);
            defineAdminAuth(config);
            loadPreviewCancelAnyState(pending);
        }
        MeasureTime.stop(pStart, "Loading pending config for ID " + getId());
        if (!Boolean.TRUE.equals(validIndex)) {
            return PENDING_ATTENDED_RESULT;
        }
        if (pending != null) {
            final ElapsedDataDTO sStart = MeasureTime.start(getClass());
            hasNextFillUser(dao);
            setFindingDepartments();
            setFindingCatalogsJSON(ActionSources.MODULE_FORMS);
            if (getRequestJustStarting() || currentAutorizationPoolIndex != null) {
                setFormsCatalogsJSON(pending, currentAutorizationPoolIndex);
            }
            setSerializedOutstandingSurvey(Utilities.getSerializedObj(pending, true, PENDING_EXCLUDE_PROPERTIES));
            MeasureTime.stop(sStart, "Set pending serialized data for ID " + getId());
        }
        setLoadAction("Request.Survey.Capture");
        return super.doExecute(config, pending);
    }

    private Boolean loadRestrictAccess(Long requestId) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleBoolean(" "
                + " SELECT "
                    + "r.restrictRecordsByDepartment"
                + " FROM " + Request.class.getCanonicalName() + " r "
                    + " WHERE r.id = :requestId " ,
                "requestId", requestId);
    }

    private void checkForAdjustmentRequest(IUntypedDAO dao) {
        if (pending == null || Objects.equals(pending.getDeleted(), 1)) {
            return;
        }
        if (OutstandingSurveys.STATUS.RETURN_APPROVAL.equals(pending.getEstatus().toString())) {
            /*
              En caso de estar pendiente de aprobar se agregan varibles para validar
              si el usuario loggeado es el responsable de aprobar.
             */
            final FormCurrentApprover data = getFormAdjustmentRequestData();
            setFormAdjustmentRequestData(data);
            Long verificationPendingRecordId = new ToVerifyFormAdjustmentRequest(dao).getPendingRecordIdFromRecordId(
                getLoggedUserDto(), data.getOutstandingSurveyId()
            );
            Long authorizationPendingRecordId = new ToAuthorizeAdjustmentRequest(dao).getPendingRecordIdFromRecordId(
                getLoggedUserDto(), data.getFormRequestId()
            );
            setAdjustmentAuthorizationAvailable(authorizationPendingRecordId != null);
            setAdjustmentVerifyAvailable(verificationPendingRecordId != null);
        }
    }

    private boolean shouldForcePreviewMode(IOutstandingSurveysLoadAnswers pending) {
        if (pending == null) {
            return false;
        }
        final Short status = pending.getEstatus();
        String statusStr = status.toString();
        return (
            /*
              La comparación se hace de `String` vs `String` por facilidad, dentro
              del enúm, el `equals` tiene override.
             */
            OutstandingSurveys.STATUS.EXPIRED.equals(statusStr)
            || OutstandingSurveys.STATUS.RETURN_APPROVAL.equals(statusStr)
        );
    }

    private void setFindingDepartments() {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        IUntypedDAO dao = getUntypedDAO();
        ILoggedUser loggedUser = getLoggedUserDto();
        List<Map<String, Object>> d = dao.HQL_selectMapQuery(" SELECT new map("
                + " c.id AS value "
                + ",c.businessUnitId AS businessUnitId "
                + ",c.description AS text "
            + " ) "
            + " FROM " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " c "
            + " WHERE "
                + " c.businessUnitId IN ("
                    + IBusinessUnitDAO.getLoggedUserBusinessUnitIdsQuery(loggedUser.getId(), loggedUser.isAdmin())
                + " )",
            false,
            null,
            0
        );
        setAuditIndividualDepartment(Utilities.getSerializedObj(d));
        MeasureTime.stop(tStart, "Loading departemtns for ID " + getId());
    }

    private boolean isNewDocumentOrAuthorAccessOrAttendingPending() {
        final IDocumentDAO documentDao = getBean(IDocumentDAO.class);
        if (getDocumentId() == null && getRequestId() != null) {
            // Aquí entrán ÚNICAMENTE solicitudes de documentos y formularios de nueva creación.
            final Map<String, Object> requestData = documentDao.HQL_findSimpleMap(" SELECT new map("
                    + " r.type AS requestType"
                    + ",r.nodoId AS nodeId"
                    + ",a.id AS authorId"
                + " ) "
                + " FROM " + Request.class.getCanonicalName() + " r "
                + " LEFT JOIN r.author a "
                + " WHERE r.id = :requestId", "requestId", getRequestId()
            );
            return getLoggedUserId().equals(requestData.get("authorId")) || (
                Request.TYPE.NEW.getValue().equals(requestData.get("requestType"))
                && documentDao.hasPermission(
                    getLoggedUserId(),
                    (Long) requestData.get("nodeId"),
                    isAdmin(),
                    getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)
                )
            );
        } else if (getRequestId() != null) {
            boolean isAuthor = documentDao.HQL_findSimpleLong(" SELECT count(*) "
                + " FROM " + Request.class.getCanonicalName() + " r "
                + " JOIN r.author a "
                + " WHERE "
                    + " r.id = :requestId"
                    + " AND a.id = :userId",
            ImmutableMap.of(
                    "requestId", getRequestId(),
                    "userId", getLoggedUserId()
                )
            ) > 0;
            if (isAuthor) {
                return true;
            } else {
                if(getRequestId() != null && getRequestId() != 0 && getRequestId() != -1){
                    final IRequestDAO dao = getBean(IRequestDAO.class);
                    boolean hasAuthorizationPending = dao.hasAuthorizationPending(getRequestId(), getLoggedUserId());
                    if (hasAuthorizationPending) {
                        return hasAuthorizationPending;
                    }
                    return dao.hasVerificationPending(getRequestId(), getLoggedUserId());
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    private void setIdFromRequestId(final SurveyCaptureConfig config, final IUntypedDAO dao) {
        String outstandingSurveysId = dao.HQL_findSimpleString(" SELECT r.outstandingSurveysId"
            + " FROM " + Request.class.getCanonicalName() + " r "
            + " WHERE r.id = :requestId ", "requestId", getRequestId()
        );
        setId("O" + outstandingSurveysId);
        setOutstandingSurveyId(Long.parseLong(outstandingSurveysId));
        SurveyCaptureUtil.updatePendingData(config, getOutstandingSurveyId(), getLoggedUserDto());
    }

    private void setSurveyIdFromDocumentId(IUntypedDAO dao) {
        setId(
            dao.HQL_findSimpleString(" SELECT d.surveyId"
                + " FROM " + Document.class.getCanonicalName() + " d "
                + " WHERE d.id = :id",
                ImmutableMap.of("id", getDocumentId())
            )
        );
    }

    private void loadCancelablesIndexes(IRequestDAO dao) {
        final List<Long> values = dao.cancelableFillIndexes(outstandingSurveyId);
        if (values != null && !values.isEmpty()
            && (findingId == null || findingId <= 0) && !getOutstandingArchived()) {
            if (FormUtil.canCancelCurrentSection(
                    this.outstandingSurveyId,
                    this.currentAutorizationPoolIndex,
                    getLoggedUserServices(),
                    documentStatus,
                    surveyStatus)
            ) {
                this.showCancelFillForm = true;
            }
            serializedCancelableFillIndexes = Utilities.getSerializedObj(values);
            // Shows cancel button besides configuration.
            this.setCancelAny(getLoggedUserServices().contains(ProfileServices.getServicio("formCancelAny")));
        } else {
            this.showCancelFillForm = false;
        }
    }

    private void isValidIndex(IRequestDAO dao, IOutstandingSurveysLoadAnswers o) {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            if (currentAutorizationPoolIndex != null) {
                final Map<String, Object> params = new HashMap<>(2);
                params.put("id", o.getRequestId());
                params.put("index", currentAutorizationPoolIndex.intValue());
                validIndex = dao.HQL_findLong(" SELECT count(c.id)"
                        + " FROM " + Request.class.getCanonicalName() + " c"
                        + " JOIN c.autorizationPool ap"
                        + " JOIN ap.autorizationPoolDetailsList apd"
                        + " WHERE c.id = :id"
                        + " AND apd.indice = :index "
                        + " AND apd.accepted IS NULL", params
                ) > 0;
            }
        } finally {
            MeasureTime.stop(tStart, "Validating current authorization pool index for ID " + getId());
        }
    }

    private void hasNextFillUser(IRequestDAO dao) {
        if (currentAutorizationPoolIndex == null){
            return;
        }
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        Map params = new HashMap<>(2);
        params.put("pending", pending.getId());
        params.put("currentAutorizationPoolIndex", currentAutorizationPoolIndex + 1);
        nextFillUser = dao.HQL_findLong(" SELECT oa.userDefinedToFillId "
            + " FROM " + OutstandingSurveysAttendant.class.getCanonicalName() + " oa "
            + " WHERE oa.outstandingSurveyId = :pending"
            + " AND oa.fillAutorizationPoolIndex = :currentAutorizationPoolIndex"
            + " AND oa.fieldType = '" + SurveyField.TYPE_SECCION + "' ",
            params
        );
        MeasureTime.stop(tStart, "Loading next fill user for ID " + getId());
    }

    private IOutstandingSurveysLoadAnswers getPendingByFillMode(IRequestDAO dao) {
        if (getId().startsWith("O")) {
            outstandingSurveyId = Long.valueOf(getId().substring(1));
        }
        final ElapsedDataDTO oStart = MeasureTime.start(getClass());
        OutstandingSurveys pending = dao.HQLT_findById(OutstandingSurveys.class, outstandingSurveyId);
        MeasureTime.stop(oStart, "Elapsed time loading entity by fill mode for id " + getId());
        return pending;
    }

    private IOutstandingSurveysLoadAnswers getPendingByPreviewMode(IRequestDAO dao) {
        final boolean isArchived = getOutstandingArchived();
        setPreviewMode(true);
        if (getId().startsWith("O")) {
            outstandingSurveyId = Long.valueOf(getId().substring(1));
        }
        if (outstandingSurveyId == null) {
            return null;
        }
        if (isArchived) {
            final ISurveyCaptureDAO formDao = getBean(ISurveyCaptureDAO.class);
            final Long surveyId = dao.HQL_findSimpleLong(" " +
                            " SELECT " +
                            " o.surveyId " +
                            " FROM " + OutstandingSurveys.class.getCanonicalName() + " o " +
                            " WHERE o.id = :id ",
                    ImmutableMap.of("id", outstandingSurveyId)
            );
            if (getDocumentMasterId() == null) {
                setDocumentMasterId(dao.HQL_findSimpleString(" "
                        + " SELECT d.masterId "
                        + " FROM " + Document.class.getCanonicalName() + " d "
                        + " WHERE d.surveyId = :surveyId", ImmutableMap.of("surveyId", surveyId)
                ));
            }
            return formDao.loadDataFromSurveyAnswers(getOutstandingSurveyId(), getDocumentMasterId(), getLoggedUserDto());
        } else {
            OutstandingSurveys pending;
            setPreviewMode(true);
            pending = dao.HQLT_findById(OutstandingSurveys.class, outstandingSurveyId);
            return pending;
        }
    }

    private FormCurrentApprover getFormCurrentApprover() {
        return getFormCurrentApprover(null);
    }

    private FormCurrentApprover getFormCurrentApprover(FormRequestType formRequestType) {
        if (outstandingSurveyId != null) {
            return FormUtil.getApproverData(outstandingSurveyId, formRequestType);
        } else if (getId() != null && getId().charAt(0) == 'O') {
            return FormUtil.getApproverData(Long.valueOf(getId().substring(1)), formRequestType);

        } else if (meetingId == null && findingId == null) {
            String archivedWhere = "";
            if (getArchived() && getOutstandingArchived()) {
                archivedWhere = " AND c.archived = 0 ";
            }
            return FormUtil.getApproverData(formRequestType, " WHERE "
                    + " c.creatorUserId = :creatorUserId "
                    + " AND c.cuestionario.id = :surveyId"
                    + " AND c.estatus IN ("
                        + OutstandingSurveys.ESTATUS_BORRADOR
                    + " )"
                    + archivedWhere, ImmutableMap.of(
                        "creatorUserId", getLoggedUserId(),
                        "surveyId", Long.valueOf(getId())
                    )
            );
        }
        return null;
    }

    private void setFormReopenRequestData() {
        FormCurrentApprover data = getFormCurrentApprover(FormRequestType.REOPEN);
        if (data != null) {
            this.setActiveReopenFormRequestId(data.getFormRequestId());
            this.setActiveReopenReason(data.getFormRequestReason());
            this.setActiveReopenStatus(data.getFormRequestStatus());
            this.setReopenRequestorUserName(data.getCreatedByName());
        }
    }

    private void setFormCancelRequestData() {
        FormCurrentApprover data = getFormCurrentApprover(FormRequestType.CANCEL);
        if (data != null) {
            this.setActiveCancelationFormRequestId(data.getFormRequestId());
            this.setActiveCancelationReason(data.getFormRequestReason());
            this.setActiveCancelationStatus(data.getFormRequestStatus());
            this.setCancelationRequestorUserName(data.getCreatedByName());
        }
    }

    private FormCurrentApprover getFormAdjustmentRequestData() {
        return getFormCurrentApprover(FormRequestType.ADJUSTMENT);
    }

    private void setFormAdjustmentRequestData() {
        FormCurrentApprover data = getFormAdjustmentRequestData();
        setFormAdjustmentRequestData(data);
    }

    private void setFormAdjustmentRequestData(FormCurrentApprover data) {
        if (data != null) {
            this.setActiveAdjustmentFormRequestId(data.getFormRequestId());
            this.setActiveAdjustmentRejectedAutorizationPoolIndex(data.getFormRejectedAutorizationPoolIndex());
            this.setActiveAdjustmentReason(data.getFormRequestReason());
            this.setActiveAdjustmentStatus(data.getFormRequestStatus());
            this.setAdjustmentRequestorUserName(data.getCreatedByName());
        }
    }

    private void setSignatureFieldObjectAttributes(Long outstandingSurveyId, Long currentIndex) {
        FieldObjectAttributes data = FormUtil.getSignatureProperties(outstandingSurveyId, currentIndex);
        if (data != null) {
            this.setSignatureRejectApprovalRequired(
                Boolean.TRUE.equals(data.getSignRejectApproval())
            );
        }
    }

    private FormCurrentApprover getOutstandingStatusData() {
        return getFormCurrentApprover();
    }

    private void setOutstandingStatusData(final SurveyCaptureConfig config, final FormCurrentApprover data) {
        // Se valida que tenga acceso
        if (data == null || data.getStatus() == null) {
            return;
        }
        this.setEstatus(String.valueOf(data.getStatus()));
        this.setFlowBusinessUnitId(data.getBusinessUnitId());
        this.setFlowBusinessUnitDepartmentId(data.getBusinessUnitDepartmentId());
        this.setFlowAreaId(data.getAreaId());
        this.setCancelationCurrentUserId(data.getCancelationCurrentUserId());
        this.setAdjustmentCurrentUserId(data.getAdjustmentCurrentUserId());
        this.setReopenCurrentUserId(data.getReopenCurrentUserId());
        this.setCancelationCurrentUserName(data.getCancelationCurrentUserName());
        this.setAdjustmentCurrentUserName(data.getAdjustmentCurrentUserName());
        this.setReopenCurrentUserName(data.getReopenCurrentUserName());
        this.setCancelationCurrentUserStatus(data.getCancelationCurrentUserStatus());
        this.setAdjustmentCurrentUserStatus(data.getAdjustmentCurrentUserStatus());
        this.setReopenCurrentUserStatus(data.getReopenCurrentUserStatus());
        if (data.getStageLatestFillOutUserId() != null) {
            this.setStageLatestFillOutUserId(
                ImmutableList.of(data.getStageLatestFillOutUserId())
            );
        }
    }

    private void defineAdminAuth(final SurveyCaptureConfig config) {
        if (!WorkflowAuthRole.ADMIN.equals(config.getAuthRole()) || Objects.equals(pending.getDeleted(), 1)) {
            return;
        }
        if (OutstandingSurveys.STATUS.RETURN_APPROVAL.equals(pending.getEstatus().toString())) {
            if (Objects.equals(WorkflowRequestStatus.VERIFING.getValue(), getActiveAdjustmentStatus())) {
                setAdjustmentAuthorizationAvailable(false);
                setAdjustmentVerifyAvailable(true);
                setIsAdjustmentVerifyAvailableByAdmin(true);
            } else {
                setAdjustmentAuthorizationAvailable(true);
                setAdjustmentVerifyAvailable(false);
            }
        }
        if (isReopenAuthorizerAvailableByAny() && !isReopenAuthorizerAvailable()) {
            setReopenCurrentUserId(getLoggedUserId());
            setIsReopenAuthorizerAvailableByAdmin(true);
            setIsAdjustmentAuthorizationAvailableByAdmin(true);
        } else if (isReopenAvailableByAny() && !isReopenAvailable()) {
            setStageLatestFillOutUserId(ImmutableList.of(getLoggedUserId()));
            setIsReopenAvailableByAdmin(true);
        }

        if (!getUnrestrictedAccess() && isCancelAuthorizerAvailableByAny() && getLoggedUserDto().isAdmin()) {
            setIsCancelAuthorizerAvailableByAdmin(true);
        }
    }

    private IOutstandingSurveysLoadAnswers getPendingByRequestorMode(final SurveyCaptureConfig config, final IRequestDAO dao) throws IOException, QMSException {
        ElapsedDataDTO tStart = MeasureTime.start(getClass());
            IOutstandingSurveysLoadAnswers pending = null;
            //se revisa si ya existe un en proceso de creación para este formulario por el usuario

            if (outstandingSurveyId != null) {
                pending = dao.HQLT_findById(OutstandingSurveys.class, outstandingSurveyId);
                MeasureTime.stop(tStart, "Elapsed time loading OutstandingSurveys entitiy with id " + outstandingSurveyId);
            }
            final Long documentId = getDocumentId();
            //si no existe se crea uno nuevo
            if (getLogger().isDebugEnabled()) {
                getLogger().debug("El usuario {} realiza una solicitud al formulario {surveyId: {}, documentId: {}}", getLoggedUserId(), getId(), documentId);
            }
            if (pending == null) {
                pending = new OutstandingSurveys();
                Survey s = dao.HQLT_findById(
                        Survey.class,
                        Long.valueOf(getId()),
                        true,
                        CacheRegion.SURVEY,
                        Utilities.getSettings().getConnQueryTimeout()
                );
                pending.setId(-1L);
                pending.setCreatedBy(getLoggedUserId());
                pending.setCreatedDate(new Date());
                pending.setLastModifiedBy(getLoggedUserId());
                pending.setLastModifiedDate(new Date());
                pending.setCuestionario(s);
                pending.setDocumentId(documentId);
                pending.setCreatorUserId(getLoggedUserId());
                //generacion de folio de llenado
                ISurveysDAO surveyDao = getBean(ISurveysDAO.class);
                pending.setCode(
                        surveyDao.getOutstandingSurveyRequestPrefix(documentId)
                        + new CodeSequence().next(CodeSequence.type.CODE_REQUEST_OUTSTANDING_SURVEY)
                );
                final Map<String, Object> documentData = dao.HQL_findSimpleMap(" SELECT new map("
                            + " c.validateAccessFormDepartment AS validateAccessFormDepartment"
                            + ", c.businessUnitDepartmentId AS businessUnitDepartmentId"
                            + ", c.businessUnitId AS businessUnitId"
                        + " )"
                        + " FROM " + Document.class.getCanonicalName() + " c"
                        + " WHERE c.id = :id", "id", documentId
                );
                final Boolean validateAccessFormDepartment = (Boolean) documentData.get("validateAccessFormDepartment");
                if (Boolean.TRUE.equals(validateAccessFormDepartment)) {
                    pending.setBusinessUnitDepartmentId((Long) documentData.get("businessUnitDepartmentId"));
                    pending.setBusinessUnitId((Long) documentData.get("businessUnitId"));
                }
                pending.setEstatus(OutstandingSurveys.ESTATUS_NUEVO);
                ElapsedDataDTO rStart = MeasureTime.start(getClass());
                //se liga su request en borrador
                List<Request> sols = dao.HQL_findByQueryLimit(" SELECT new " +  Request.class.getCanonicalName() + "("
                        + " c.documentType, "
                        + " c.retentionText, "
                        + " c.retentionTime, "
                        + " c.scope, "
                        + " c.storagePlaceId, "
                        + " c.businessUnit.id, "
                        + " c.department.id, "
                        + " c.nodo.id, "
                        + " c.organizationalUnit.id,  "
                        + " c.outstandingSurveysId, "
                        + " c.surveyId, "
                        + " c.templateSurveyId, "
                        + " c.restrictRecordsByDepartment, "
                        + " c.validateAccessFormDepartment, "
                        + " c.restrictRecordsField, "
                        + " c.restrictRecordsObjId, "
                        + " c.documentMasterId, "
                        + " c.document.description, "
                        + " c.documentCode, "
                        + " c.version "
                    + " )"
                    + " FROM " + Request.class.getCanonicalName() + " c"
                    + " JOIN c.document d"
                    + " WHERE d.id = :documentId"
                    + " AND c.status = " + WorkflowRequestStatus.CLOSED.getValue()
                    + " ORDER BY c.creationDate DESC",
                    ImmutableMap.of("documentId", documentId),
                    1,
                    false,
                    null,
                    0
                );
                final Request sol = sols.get(0);
                sol.setId(-1L);
                sol.setType(Request.FILL);
                sol.setReazon("Solicitud de llenado del formulario " + sol.getDocumentCode() + " por " + getLoggedUserDto().getDescription());
                sol.setDocumentCode(pending.getCode());
                sol.setDescription(
                    sol.getDocument().getDescription()
                    + ", " + Utilities.getTag("label.by")
                    +  getLoggedUserDto().getDescription()
                );
                sol.setCreationDate(new Date());
                sol.setAuthor(getLoggedUserDto().getUserRefInstance());
                sol.setAutorizationPool(null);
                sol.setFlujoId(null);
                sol.setFillOutDocument(new DocumentRef(documentId));
                sol.setDocument(null);
                sol.setEnablePdfViewer(1);
                sol.setIsBusy(0);
                if(getImpersonatingUserId() != null){
                    sol.setImpersonatedBy(new UserRef(getSavedLoggedUserId()));
                }
                final RequestSaveHandle saveResult = dao.save(sol, null, sol.getType(), getLoggedUserDto(), sol.getSurveyId() != null, true);
                MeasureTime.stop(rStart, "Elapsed time saving request by requestor mode for id " + getId());
                ElapsedDataDTO oStart = MeasureTime.start(getClass());
                Request savedRequest = saveResult.getRequest();
                pending = dao.startFillForm(savedRequest, pending, activityId, getLoggedUserDto());
                MeasureTime.stop(oStart, "Elapsed time starting fill form by requestor mode for id " + getId());
                if (getLogger().isDebugEnabled()) {
                    getLogger().debug("La solicitud es totalmente nueva {outstandingSurveyId: {}, requestId: {}}", pending.getId(), pending.getRequestId());
            }
        } else {
            if (getLogger().isDebugEnabled()) {
                getLogger().debug("La solicitud ya existia {outstandingSurveyId: {}, requestId: {}}", pending.getId(), pending.getRequestId());
            }
        }
        if (findingId != null) {
            dao.HQL_updateByQuery(" UPDATE " + Action.class.getCanonicalName() + " c "
                    + " SET c.outstandingSurveyId = " + pending.getId()
                    + " WHERE c.id = " + findingId);
        }
        if (meetingId != null) {
            String query = " UPDATE " + Meeting.class.getCanonicalName() + " c "
                    + " SET c.outstandingSurveyId = " + pending.getId()
                    + " WHERE c.id = " + meetingId
                    + " AND c.deleted = 0"
                    + " AND c.recurrent = " + IPeriodicEntity.RECURRENT.NO;
            getLogger().error("query: {}", query);
            dao.HQL_updateByQuery(query);
        }
        setPendiente(pending);
        setId("O" + pending.getId());
        SurveyCaptureUtil.updatePendingData(config, pending.getId(), getLoggedUserDto());
        MeasureTime.stop(tStart, "Elapsed time getting pending by requestor mode for id " + getId());
        return pending;
    }

    public Long getOutstandingSurveyId() {
        return outstandingSurveyId;
    }

    public void setOutstandingSurveyId(Long outstandingSurveyId) {
        this.outstandingSurveyId = outstandingSurveyId;
    }

    public Long getCurrentAutorizationPoolIndex() {
        return currentAutorizationPoolIndex;
    }

    public void setCurrentAutorizationPoolIndex(Long currentAutorizationPoolIndex) {
        this.currentAutorizationPoolIndex = currentAutorizationPoolIndex;
    }

    public Long getCurrentAutorizationPoolDetailId() {
        return currentAutorizationPoolDetailId;
    }

    public void setCurrentAutorizationPoolDetailId(Long currentAutorizationPoolDetailId) {
        this.currentAutorizationPoolDetailId = currentAutorizationPoolDetailId;
    }

    public Integer getCurrentRecurrence() {
        return currentRecurrence;
    }

    public void setCurrentRecurrence(Integer currentRecurrence) {
        this.currentRecurrence = currentRecurrence;
    }

    @Override
    public Integer getInvalidAutorizationPoolIndex() {
        return invalidAutorizationPoolIndex;
    }

    public void setInvalidAutorizationPoolIndex(Integer invalidAutorizationPoolIndex) {
        this.invalidAutorizationPoolIndex = invalidAutorizationPoolIndex;
    }

    /**
     * @return the showCancelFillForm
     */
    public Boolean getShowCancelFillForm() {
        if (this.getOutstandingArchived()) {
            return false;
        }
        return showCancelFillForm;
    }

    public Boolean getCancelAny() {
        return cancelAny;
    }

    public void setCancelAny(Boolean cancelAny) {
        this.cancelAny = cancelAny;
    }

    public String getSerializedCancelableFillIndexes() {
        if (serializedCancelableFillIndexes == null || serializedCancelableFillIndexes.isEmpty()) {
            return "[]";
        }
        return serializedCancelableFillIndexes;
    }

    public Long getFindingId() {
        return findingId;
    }

    public void setFindingId(Long findingId) {
        this.findingId = findingId;
    }

    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    public Boolean getUnrestrictedAccess() {
        return unrestrictedAccess;
    }

    public void setUnrestrictedAccess(Boolean unrestrictedAccess) {
        this.unrestrictedAccess = unrestrictedAccess;
    }

    @Override
    public boolean getRequestJustStarting() {
        return getRequestMode().equalsIgnoreCase("REQUESTOR") && (
            currentAutorizationPoolIndex == null || currentAutorizationPoolIndex == 1
        );
    }


    public IOutstandingSurveysLoadAnswers getPending() {
        return pending;
    }

    public void setNextFillUser(Long nextFillUser) {
        this.nextFillUser = nextFillUser;
    }

    public Long getNextFillUser() {
        return nextFillUser;
    }

    public void setRequestAuthor(String requestAuthor) {
        this.requestAuthor = requestAuthor;
    }

    public String getRequestAuthor() {
        return requestAuthor;
    }

    public void setImpersonateUserId(String impersonateUserId) {
        this.impersonateUserId = impersonateUserId;
    }

    public String getImpersonateUserId() {
        return impersonateUserId;
    }

    public void setDocumentStatus(int documentStatus) {
        this.documentStatus = documentStatus;
    }

    public int getDocumentStatus() {
        return documentStatus;
    }

    public void setSurveyStatus(int surveyStatus) {
        this.surveyStatus = surveyStatus;
    }

    public int getSurveyStatus() {
        return surveyStatus;
    }

    public Boolean getAllowCaptureTime() {
        return allowCaptureTime;
    }

    public void setAllowCaptureTime(Boolean allowCaptureTime) {
        this.allowCaptureTime = allowCaptureTime;
    }

    public String getActivityPlannedCode() {
        return activityPlannedCode;
    }

    public void setActivityPlannedCode(String activityPlannedCode) {
        this.activityPlannedCode = activityPlannedCode;
    }

    public String getActivityPlannedDescription() {
        return activityPlannedDescription;
    }

    public void setActivityPlannedDescription(String activityPlannedDescription) {
        this.activityPlannedDescription = activityPlannedDescription;
    }

    private void surveyAllowCaptureTime() {
        if (this.activityId == null || this.activityId <= 0 || !getModuleTimesheetAccess()) {
            this.allowCaptureTime = false;
        } else {
            IUntypedDAO dao = getUntypedDAO();
            String fillTypes = dao.HQL_findSimpleString(" SELECT at.fillTypes "
                    + " FROM " + Activity.class.getCanonicalName() + " a"
                    + " JOIN a.type at"
                    + " WHERE a.id = :activityId",
                    ImmutableMap.of("activityId", this.activityId),
                    true,
                    CacheRegion.ACTIVITY,
                    0
            );
            if (fillTypes.contains(ActivityType.FILL_TYPE.FILL_FORM.getValue().toString()) && getModuleTimesheetAccess()) {
                this.allowCaptureTime = true;
                if (this.activityId != null) {
                    getTimesheetActivityPlannedData();
                }
            } else {
                this.allowCaptureTime = false;
            }
        }
    }

    private void getTimesheetActivityPlannedData() {
        IUntypedDAO dao = getUntypedDAO();
        TimesheetDto timesheet = dao.HQLT_findSimple(TimesheetDto.class, " SELECT new " + TimesheetDto.class.getCanonicalName()+ "("
                    + " a.code, "
                    + " a.description"
                + " )"
                + " FROM " + Activity.class.getCanonicalName() + " a "
                + " WHERE a.id =  :activityId",
                ImmutableMap.of("activityId", activityId),
                true,
                CacheRegion.ACTIVITY,
                0
        );

        this.activityPlannedDescription = timesheet.getActivityPlannedDescription();
        this.activityPlannedCode = timesheet.getActivityPlannedCode();
    }

    private Boolean getModuleTimesheetAccess() {
        return hasAnyModuleAccess(new String[]{mx.bnext.access.Module.TIMESHEET.getKey()});
    }

    private void fillDocumentData() {
        final IUntypedDAO dao = getUntypedDAO();
        if (getId() != null && getId().startsWith("O")) {
            /*
              "O" representa los "outstandingSurveyIds", la información
              resguardada por esos ID es confidencial.

              Por lo anterior se le da prioridad al formulario que se llenó
              en lugar del parametrizado para validar permisos.
              */
            Long outstandingSurveyId = Long.valueOf(getId().substring(1));
            final Long documentId = dao.HQL_findLong(" "
                            + " SELECT d.id"
                            + " FROM " + OutstandingSurveys.class.getCanonicalName() + " o "
                            + " JOIN o.cuestionario q "
                            + " JOIN " + Request.class.getCanonicalName() + " r ON r.id = q.request.id "
                            + " JOIN r.document d "
                            + " WHERE o.id = :outstandingSurveyId",
                    "outstandingSurveyId", outstandingSurveyId
            );
            if (documentId != null && documentId > 0) {
                setDocumentId(documentId);
            }
        }
        if (getDocumentId() == null && getRequestId() != null) {
            // Request tipo "fill"
            final Long documentId = dao.HQL_findLong(" "
                    + " SELECT d.id"
                    + " FROM " + Request.class.getCanonicalName() + " r"
                    + " JOIN r.fillOutDocument d "
                    + " WHERE r.id = :requestId", "requestId", getRequestId()
            );
            if (documentId != null && documentId > 0) {
                setDocumentId(documentId);
            }
        }
        if (getDocumentId() == null && getRequestId() != null) {
            // Request tipo "new" o "modify"
            final Long documentId = dao.HQL_findLong(" "
                    + " SELECT d.id"
                    + " FROM " + Request.class.getCanonicalName() + " r "
                    + " JOIN r.document d "
                    + " WHERE r.id = :requestId", "requestId", getRequestId()
            );
            if (documentId != null && documentId > 0) {
                setDocumentId(documentId);
            }
        }

        if (getDocumentId() == null && getTask().equals(TASK_GOING_TO_PREVIEW) && getId() != null && !getId().startsWith("O")) {
            final Long documentId = dao.HQL_findLong(" "
                            + " SELECT d.id"
                            + " FROM " + Document.class.getCanonicalName() + " d"
                            + " WHERE d.surveyId = :id",
                    ImmutableMap.of("id", Long.valueOf(getId()))
            );
            if (documentId != null && documentId > 0) {
                setDocumentId(documentId);
            }
        }
        setDocumentData();
    }

    private void fillMasterIdFromDocumentId() {
        if (getDocumentId() == null || getDocumentMasterId() != null || !"REQUESTOR".equals(getRequestMode())) {
            return;
        }
        final IUntypedDAO dao = getUntypedDAO();
        final String masterId = dao.HQL_findSimpleString(" "
            + " SELECT d.masterId "
            + " FROM " + Document.class.getCanonicalName() + " d"
            + " WHERE d.id = :id",
                ImmutableMap.of("id", getDocumentId())
        );
        this.setDocumentMasterId(masterId);
    }

    private void fillFromMasterId() {
        if (getDocumentMasterId() == null || getRequestMode() == null) {
            // No llenar si no hay "masterId"
            return;
        }
        final SurveyRequestMode mode = SurveyRequestMode.valueOf(getRequestMode().toUpperCase());
        if (!mode.equals(SurveyRequestMode.REQUESTOR)) {
            // No llenar si no es una nueva solicitud
            return;
        }
        setDocumentDataFromMasterId();
    }

    private void setDocumentData() {
        final Long documentId = getDocumentId();
        if (documentId == null || documentId <= 0) {
            return;
        }
        final Map<String, Object> documentData = getDocumentData(documentId);
        if (!documentData.isEmpty()) {
            setDocumentDescription((String) documentData.get("documentDescription"));
            setDocumentStatus((Integer) documentData.get("status"));
            setDocumentMasterId((String) documentData.get("masterId"));
            this.documentTypeId = (Long) documentData.get("documentTypeId");
            this.enableOpenPdfFiles = Boolean.TRUE.equals(documentData.get("filePdfPagesEnabled"));
        }
    }

    private Map<String, Object> getDocumentData(Long documentId) {
        final IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleMap(" "
                        + " SELECT new map("
                            + " d.id as id"
                            + ", d.status as status"
                            + ", d.surveyId AS surveyId "
                            + ", d.description as documentDescription"
                            + ", d.masterId as masterId"
                            + ", t.id as documentTypeId "
                            + ", t.filePdfPagesEnabled as filePdfPagesEnabled "
                        + ") "
                        + " FROM " + Document.class.getCanonicalName() + " d"
                        + " JOIN d.documentType t "
                        + " WHERE d.id = :id",
                ImmutableMap.of("id", documentId)
        );
    }

    private Map<String, Object> getDocumentDataByMasterId() {
        final Long documentId = getDocumentIdByMasterId();
        if (documentId == null || documentId <= 0) {
            getLogger().error("Invalid documentMasterId: {}", getDocumentMasterId());
            return Collections.emptyMap();
        }
        return getDocumentData(documentId);
    }

    private Long getDocumentIdByMasterId() {
        final IUntypedDAO dao = getUntypedDAO();
        final String documentMasterId = getDocumentMasterId();
        return dao.HQL_findLong(" "
                        + " SELECT d.id"
                        + " FROM " + Document.class.getCanonicalName() + " d "
                        + " WHERE d.status IN ( "
                            + Document.STATUS.ACTIVE.getValue()
                            + "," + Document.STATUS.IN_EDITION.getValue()
                        + " ) "
                        + " AND d.masterId = :masterId",
                "masterId", documentMasterId
        );
    }

    private void setDocumentDataFromMasterId() {
        final String documentMasterId = getDocumentMasterId();
        if (documentMasterId == null || documentMasterId.isEmpty()) {
            return;
        }
        final Map<String, Object> documentData = getDocumentDataByMasterId();
        if (!documentData.isEmpty() && documentData.get("id") != null) {
            setDocumentId((Long) documentData.get("id"));
            setDocumentStatus((Integer) documentData.get("status"));
            setDocumentDescription((String) documentData.get("documentDescription"));
            if (documentData.get("surveyId") != null) {
                setId(documentData.get("surveyId").toString());
            }
        }
    }

    private void loadPreviewCancelAnyState(IOutstandingSurveysLoadAnswers pending) {
        if (pending == null || this.getArchived() && this.getOutstandingArchived()) {
            return;
        }
        long status = pending.getStatus();
        if (status == OutstandingSurveys.ESTATUS_CERRADA
                || status == OutstandingSurveys.ESTATUS_CANCELADA
                || !Objects.equals(pending.getDeleted(), IS_NOT_DELETED)
        ) {
            return;
        }
        LoggedUser loggedUser = getLoggedUserDto();
        List<ProfileServices> services = loggedUser.getServices();
        this.setCancelAny(services.contains(ProfileServices.getServicio("formCancelAny")));
        this.showCancelFillForm = true;
        boolean hasAccess = services.contains(ProfileServices.SPECIAL_FORM_CANCELATION_APPROVER)
                || services.contains(ProfileServices.FORMULARIO_CONTROL)
                || loggedUser.isAdmin();
        setHasAccessToDefitiveCancelWithAuth(
                hasAccess
        );
    }

    public Boolean getContinueFill() {
        return continueFill;
    }

    public void setContinueFill(Boolean continueFill) {
        this.continueFill = continueFill;
    }

    private void progressButtonAvailable() {
        if (getArchived() && getOutstandingArchived()) {
            return;
        }
        Long outstandingSurveysId = parseOutstandingSurveyId();

        if (outstandingSurveysId != null) {
            boolean hasRequestDefinitiveCancelationInProgress = getUntypedDAO().HQL_findSimpleInteger(" " +
                            "SELECT " +
                                "count(fr.id) " +
                            "FROM " + FormRequest.class.getCanonicalName() + " fr " +
                            "WHERE " +
                                "fr.outstandingSurveysId = :outstandingSurveysId AND " +
                                "fr.status IN (" 
                                    +  WorkflowRequestStatus.VERIFING.getValue()
                                    + ", " + WorkflowRequestStatus.RETURNED.getValue()
                                    + ", " + WorkflowRequestStatus.APROVING.getValue()
                                + ")",
                    ImmutableMap.of(
                            "outstandingSurveysId", outstandingSurveysId
                    )
            ) > 0;
            setProgressButtonAvailable(!hasRequestDefinitiveCancelationInProgress);
        }
    }

    private void loadAuthPoolIndexData() {
        IFormCaptureDAO dao = getBean(IFormCaptureDAO.class);
        if (getRequestId() == null) {
            setRequestId(dao.HQL_findSimpleLong(" " +
                    "SELECT " +
                        "o.requestId " +
                    "FROM " + OutstandingSurveys.class.getCanonicalName() + " o " +
                    "WHERE o.id = :id",
                    ImmutableMap.of("id", getOutstandingSurveyId())
            ));
        }
        Map<String, Object> currentAuthPoolIndexes = dao.getCurrentAuthPoolSimpleData(getRequestId());
        if (!currentAuthPoolIndexes.isEmpty()) {
            this.currentAutorizationPoolIndex = Long.valueOf(currentAuthPoolIndexes.get("currentAutorizationPoolIndex").toString());
            this.currentAutorizationPoolDetailId = Long.valueOf(currentAuthPoolIndexes.get("currentAutorizationPoolDetailId").toString());
            this.currentRecurrence  = Integer.valueOf(currentAuthPoolIndexes.get("currentRecurrence").toString());
        }
    }

    private Boolean loadOutstandingArchived(Long outstandingSurveyId) {
        ISurveyCaptureDAO captureDao = getBean(ISurveyCaptureDAO.class);
        return captureDao.isArchived(outstandingSurveyId);
    }

    public boolean getEnableOpenPdfFiles() {
        return enableOpenPdfFiles;
    }

    @Override
    public Long getDocumentTypeId() {
        return documentTypeId;
    }
}
