/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package DPMS.Action;

import DPMS.DAOInterface.IExternalDocumentCatalogDAO;
import DPMS.Mapping.IAuditableEntity;
import Framework.Action.AuditableEntityAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import qms.form.util.CatalogFieldType;
import qms.framework.dto.ExternalDocumentCatalogDTO;
import qms.framework.util.IDatabaseQuery;
import qms.framework.util.IQueryColumn;

/**
 *
 * <AUTHOR>
 */
public class Action_ExternalDocumentCatalog extends AuditableEntityAction {

    private Integer deleted;
    private String fieldType = "select";
    private IDatabaseQuery query;
    private IQueryColumn valueColumn;
    private IQueryColumn labelColumn;
    private IQueryColumn sortColumn;
    private Integer sortDirection;
    private Boolean editableConfiguration = true;
    private Boolean labelValuesExceedsMaxLength = false;
    private Boolean duplicate = false;

    @Override
    protected IAuditableEntity getAuditableEntity(Long id, String auditableEntityImpl) {
        if (id == null || id.equals(-1L)) {
            return new ExternalDocumentCatalogDTO(-1L);
        }
        final IExternalDocumentCatalogDAO dao = Utilities.getBean(IExternalDocumentCatalogDAO.class);
        return dao.loadExternalCatalog(id);
    }
        
    @Override
    public String execute() throws Exception {
        String rValue = super.execute();
        long id = Long.parseLong(this.getId());
        if(-1L == id){
            return rValue;
        }
        final ExternalDocumentCatalogDTO catalog = (ExternalDocumentCatalogDTO) getEntity();
        this.setCode(catalog.getCode());
        this.setDescription(catalog.getDescription());
        this.setStatus(catalog.getStatus().toString());
        deleted = catalog.getDeleted();
        fieldType = catalog.getFieldType();
        query = catalog.getQuery();
        valueColumn = catalog.getValueColumn();
        sortColumn = catalog.getSortColumn();
        sortDirection = catalog.getSortDirection();
        labelColumn = catalog.getLabelColumn();
        editableConfiguration = catalog.getEditableConfiguration();
        labelValuesExceedsMaxLength = catalog.getLabelValuesExceedsMaxLength();
        if (Objects.equals(duplicate, true)) {
            setDeleted(0);
            setCode(null);
            editableConfiguration = true;
            labelValuesExceedsMaxLength = false;
            clearEntity();
        }
        return rValue;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
    
    public List<ITextHasValue> getFieldTypes(){
        List<ITextHasValue> l = new ArrayList<>();
        l.add(new TextHasValue(getTag("selectAnOption"), "select"));
        l.add(new TextHasValue(CatalogFieldType.CATALOG.toString(), CatalogFieldType.CATALOG.toString()));
        l.add(new TextHasValue(CatalogFieldType.CATALOG_MULTIPLE.toString(), CatalogFieldType.CATALOG_MULTIPLE.toString()));
        l.add(new TextHasValue(CatalogFieldType.CATALOG_HIERARCHY.toString(), CatalogFieldType.CATALOG_HIERARCHY.toString()));
        return l;
    }

    public String getFieldType() {
        return fieldType;
    }
    
    public Long getQueryId() {
        return query.getId();
    }
    
    public Long getValueColumnId() {
        return valueColumn.getId();
    }

    public Long getSortColumnId() {
        if (sortColumn == null) {
            return null;
        }
        return sortColumn.getId();
    }

    public Integer getSortDirection() {
        return sortDirection;
    }

    public Long getLabelColumnId() {
        return labelColumn.getId();
    }

    public Boolean getEditableConfiguration() {
        return editableConfiguration;
    }

    public Boolean getLabelValuesExceedsMaxLength() {
        return labelValuesExceedsMaxLength;
    }

    public Boolean getDuplicate() {
        return duplicate;
    }

    public void setDuplicate(Boolean duplicate) {
        if (duplicate == null) {
            duplicate = false;
        }
        this.duplicate = duplicate;
    }
    
}
