package DPMS.Action;

import DPMS.DAOInterface.IBuildingDAO;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;

/**
 *
 * <AUTHOR> Lares
 */
public class Action_BuildingMap extends DefaultAction {

    private static final String SELECCIONE = "-- Se<PERSON>ccione --";
    private List<ITextHasValue> buildings;
    @Override
    public String execute() throws Exception {
        IBuildingDAO dao = Utilities.getBean(IBuildingDAO.class);
        buildings = dao.getActivesInMyBusinessUnit(getLoggedUserId(), isAdmin());
        buildings.add(0, new TextHasValue(SELECCIONE, ""));
        
        return super.execute();
    }

    public List<ITextHasValue> getBuildings() {
        return buildings;
    }

    public void setBuildings(List<ITextHasValue> buildings) {
        this.buildings = buildings;
    }
    

}
