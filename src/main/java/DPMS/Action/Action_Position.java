package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IOrganizationalUnitDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.Position;
import DPMS.Mapping.PositionLite;
import DPMS.Mapping.User;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.DAO.IUntypedDAO;
import bnext.licensing.LicenseUtil;
import java.util.ArrayList;
import java.util.List;
import mx.bnext.licensing.Schema;

/**
 *
 * <AUTHOR>
 */
public class Action_Position extends Action_ActiveTRESS {

    private List<ITextHasValue> perfil;
    private List<ITextHasValue> corp;
    private List<ITextHasValue> une;
    private Long prealoadedSuperior = -1L;
    private Long prealoadedPerfil = -1L;

    private final IUntypedDAO dao;
    
    public Action_Position() {
        dao = getUntypedDAO();
    }
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        super.execute();
        getLogger().debug("prealoadedSuperior" + prealoadedSuperior);
        Long organizationalId = null;
        Long businessUnitId = null;
        if (!"-1".equals(getId())) {
            PositionLite position = dao.HQLT_findById(PositionLite.class, Long.parseLong(getId()));
            if (position != null) {
                organizationalId = position.getCorp() == null ? null : position.getCorp().getId();
                businessUnitId = position.getUne() == null ? null : position.getUne().getId();
            }
        }
        perfil = new ArrayList<>();
        corp = getBean(IOrganizationalUnitDAO.class).getStrutsComboList(""
                + " EXISTS ("
                    + " SELECT 1"
                    + " FROM " + BusinessUnit.class.getCanonicalName() + " bu"
                + " WHERE c.id = bu.organizationalUnitId"
                + " )" + (isAdmin() ? "":""
                + " AND EXISTS ( "
                    + " SELECT 1"
                    + " FROM " + User.class.getCanonicalName() + " as u "
                    + " JOIN u.puestos as p "
                    + " WHERE c.id = p.corp.id"
                    + " AND u.id = " + getLoggedUserId()
              + ")"), organizationalId);
        une = getBean(IBusinessUnitDAO.class).getStrutsComboList(""
                + " c.status = " + BusinessUnit.ACTIVE_STATUS, getLoggedUserId(), getServiciosActivos(), isAdmin(), businessUnitId);
        une.add(0, new TextHasValue("puesto.sin.jefe", ""));
        corp.add(0, new TextHasValue("puesto.sin.jefe", ""));
        perfil.add(0, new TextHasValue("combo.seleccione", ""));
        return super.execute();
    }

    public Long getPrealoadedSuperior() {
        return prealoadedSuperior;
    }

    public void setPrealoadedSuperior(Long prealoadedSuperior) {
        this.prealoadedSuperior = prealoadedSuperior;
    }

    public List<ITextHasValue> getPerfil() {
        return perfil;
    }

    public void setPerfil(List<ITextHasValue> perfil) {
        this.perfil = perfil;
    }

    public Long getPrealoadedPerfil() {
        return prealoadedPerfil;
    }

    public void setPrealoadedPerfil(Long prealoadedPerfil) {
        this.prealoadedPerfil = prealoadedPerfil;
    }

    public List<ITextHasValue> getCorp() {
        return corp;
    }

    public void setCorp(List<ITextHasValue> corp) {
        this.corp = corp;
    }

    public List<ITextHasValue> getUne() {
        return une;
    }

    public void setUne(List<ITextHasValue> une) {
        this.une = une;
    }

    public Schema getSchema() {
        String licenseCode = dao.HQL_findSimpleString(""
                + " SELECT c.licenseCode"
                + " FROM " + Position.class.getCanonicalName() + " c"
                + " WHERE c.id = " + getId());
        return LicenseUtil.getSchema(licenseCode);
    }
    
    public String getSerializedSchemas() {
        return LicenseUtil.getSerializedSchemas();
    }
}
