package DPMS.Action;

import Framework.Action.DefaultAction;

/**
 *
 * <AUTHOR>
 */
public class Action_SurveyList extends DefaultAction {

    private String js_controller = "survey";

    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        getLogger().trace("Entrando a execute..." + getJs_controller());

        return super.execute();
    }

    public String getJs_controller() {
        return js_controller;
    }

    public void setJs_controller(String js_controller) {
        this.js_controller = js_controller;
    }

}
