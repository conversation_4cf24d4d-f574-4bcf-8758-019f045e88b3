package DPMS.Action;

import DPMS.DAOInterface.IActivityDAO;
import DPMS.DAOInterface.IProjectDAO;
import DPMS.Mapping.ActivityLegacy;
import DPMS.Mapping.Goals;
import DPMS.Mapping.Project;
import DPMS.Mapping.ProjectGoal;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.DateAxis;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PlotOrientation;
import org.jfree.chart.renderer.category.CategoryItemRenderer;
import org.jfree.chart.renderer.category.GanttRenderer;
import org.jfree.data.gantt.Task;
import org.jfree.data.gantt.TaskSeries;
import org.jfree.data.gantt.TaskSeriesCollection;
import org.jfree.data.time.SimpleTimePeriod;
import qms.util.ReportUtil;
/**
 *
 * <AUTHOR>
 */
public class Action_ProjectGantt extends DefaultAction {
    private final TaskSeriesCollection collection;
    private Long proyectoId;
    private Long metaId;
    private Long activityId;
    private final Date today;
    private Integer actNum = 0;
    private Integer estado;
    private String reportB64;
    private Short porcentaje;
    private Date inicio, fin, dteEstimado;
    final TaskSeries serieProgramado;
    final TaskSeries serieEstimado;
    
    public Action_ProjectGantt() {
        collection = new TaskSeriesCollection();
        today = Utilities.truncDate(new Date());
        serieProgramado = new TaskSeries(getTag("programmed"));
        serieEstimado = new TaskSeries(getTag("estimated"));
    }

    @Override
    public String execute() throws IOException, Exception {
        if (!SUCCESS.equals(super.execute())) {
            return LOGIN;
        }

        IProjectDAO DAO = Utilities.getBean(IProjectDAO.class);
        Project project = DAO.HQLT_findById(proyectoId);

        if (activityId != null) {
            IActivityDAO daoActivity = Utilities.getBean(IActivityDAO.class);
            ActivityLegacy act = daoActivity.HQLT_findById(activityId);
            diagramGanttActivity(act);
            reportB64 = this.makeGantt(act.getCode());
        } else if (metaId != null) {
            IUntypedDAO daoGoal = getUntypedDAO();
            Goals goal = daoGoal.HQLT_findById(Goals.class, metaId);
            diagramGanttGoal(goal);
            reportB64 = this.makeGantt(goal.getCode());
        } else if (proyectoId != null) {
            this.diagramGanttProyect(project);
            reportB64 = this.makeGantt(project.getTitle());
        } else {
            getLogger().error("DPMS.Action.Action_ProjectGantt.execute()");
        }
        return SUCCESS;
    }
     
    private void diagramGanttProyect(Project p) {
        Set<ProjectGoal> pGoalList = p.getProyectGoalList();
        //Iteramos todas las metas que se encuentren de un proyecto para poder obtener las actividades y mostrarlas en el diagrama de gantt
        for (ProjectGoal pGoal : pGoalList) {
            Set<ActivityLegacy> activity = pGoal.getGoal().getActivityList();
            //Iteramos todas las activades a mostrar
            for (ActivityLegacy act : activity) {
                inicio = act.getDtefechainicio();
                fin = act.getDtefechafin();
                dteEstimado = act.getEstimationDate();
                estado = act.getStatus();
                actNum++;
                porcentaje = act.getPorcentaje();
                if (porcentaje == null) {
                    porcentaje = 0;
                }
                serieProgramado.add(new Task(actNum + ".- " + act.getCode(), new SimpleTimePeriod(act.getDtefechainicio(), act.getDtefechafin())));
                final Date estimationDate = this.getEstimado(); 
                if (estimationDate != null) {
                    if(this.getEstimado().before(act.getDtefechainicio())){
                        serieEstimado.add(new Task(actNum + ".- " + act.getCode(), new SimpleTimePeriod(this.getEstimado(), this.getEstimado())));
                    }else{
                        serieEstimado.add(new Task(actNum + ".- " + act.getCode(), new SimpleTimePeriod(act.getDtefechainicio(), this.getEstimado())));
                    }
                } else {
                    getLogger().error("Activity with id: {} and code: '{}' has no verification date(estimation_date).", new Object[]{act.getId(), act.getCode()});
                }
            }
        }
    }
    
    private void diagramGanttGoal(Goals goal) {
        Set<ActivityLegacy> activity = goal.getActivityList();
        for (ActivityLegacy act : activity) {
            inicio = act.getDtefechainicio();
            fin = act.getDtefechafin();
            dteEstimado = act.getEstimationDate();
            estado = act.getStatus();
            actNum++;
            porcentaje = act.getPorcentaje();
            if (porcentaje == null) {
                porcentaje = 0;
            }
            serieProgramado.add(new Task(actNum + ".- " + act.getCode(), new SimpleTimePeriod(act.getDtefechainicio(), act.getDtefechainicio())));
            serieEstimado.add(new Task(actNum + ".- " + act.getCode(), new SimpleTimePeriod(act.getDtefechafin(), this.getEstimado())));
        }
    }
    
    private void diagramGanttActivity(ActivityLegacy activity) {
        inicio = activity.getDtefechainicio();
        fin = activity.getDtefechafin();
        dteEstimado = activity.getEstimationDate();
        estado = activity.getStatus();
        porcentaje = activity.getPorcentaje();
        actNum++;
        if (porcentaje == null) {
            porcentaje = 0;
        }
        serieProgramado.add(new Task(actNum + ".- " + activity.getCode(), new SimpleTimePeriod(activity.getDtefechainicio(), activity.getDtefechafin())));
        serieEstimado.add(new Task(actNum + ".- " + activity.getCode(), new SimpleTimePeriod(activity.getDtefechainicio(), this.getEstimado())));
    }
    
    private Date getEstimado(){
        if(estado == 4){
            // Si ya esta terminado retorna la fecha final estimada.
//        Estado(4) = Verificada.     dteEstimado= La fecha en que se Verificada la actividad. 
            return dteEstimado;
        }else{
            long base = 0;
            // Se calcula la duracion 
            long duracion = fin.getTime() - inicio.getTime();
            // Se calcula cuantos dias faltan para terminar segun el avance que se lleve hecho.
            long cuantofalta = duracion * (100 - porcentaje) / 100;
            if(fin.getTime() > today.getTime()){
                base = fin.getTime();
            }else{
                base = today.getTime();
            }
            // Se le suman los dias que restan a la fecha base.
            base = base + cuantofalta;
            return new Date(base);
        }
    } 
    
    private String makeGantt(String title) throws IOException {
        int height = 104 + 44 * actNum;
        if (height > 720) {
            height = 720;
        } else if (actNum == 0) {
            height = 350;
        }
        collection.add(serieProgramado);
        collection.add(serieEstimado);
        JFreeChart chart = null;
        if (!title.isEmpty()) {
            //chart = ChartFactory.createGanttChart(title, , "", collection, true, true, false);
            CategoryAxis categoryAxis = new CategoryAxis(getTag("activities"));
            DateAxis dateAxis = new DateAxis("");
            dateAxis.setDateFormatOverride(new SimpleDateFormat("dd/MM/yyyy", getLocale()));
            CategoryItemRenderer renderer = new GanttRenderer();
            CategoryPlot plot = new CategoryPlot(collection, categoryAxis, dateAxis, renderer);
            plot.setOrientation(PlotOrientation.HORIZONTAL);
            chart = new JFreeChart(title, plot);
        }
        ReportUtil reportUtil = new ReportUtil();
        return reportUtil.chartAsBase64("graficagantt", chart, 600, height);
    }
    
    
    /**
     * @return the reportName
     */
    public String getReportB64() {
        return reportB64;
    }

    /**
     * @return the proyectoId
     */
    public Long getProyectoId() {
        return proyectoId;
    }

    /**
     * @param proyectoId the proyectoId to set
     */
    public void setProyectoId(Long proyectoId) {
        this.proyectoId = proyectoId;
    }

    /**
     * @return the metaId
     */
    public Long getMetaId() {
        return metaId;
    }

    /**
     * @param metaId the metaId to set
     */
    public void setMetaId(Long metaId) {
        this.metaId = metaId;
    }

    /**
     * @return the activityId
     */
    public Long getActivityId() {
        return activityId;
    }

    /**
     * @param activityId the activityId to set
     */
    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }
    
}
