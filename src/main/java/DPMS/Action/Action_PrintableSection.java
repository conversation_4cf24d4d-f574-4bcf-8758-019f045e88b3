package DPMS.Action;

import DPMS.DAOInterface.ISectionDAO;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import com.opensymphony.xwork2.ActionSupport;
import java.util.List;
import mx.bnext.access.ProfileServices;

/**
 *
 * <AUTHOR>
 */
public class Action_PrintableSection extends DefaultAction {
    
    private Long sectionId = null;
    private List<ITextHasValue> sections = null;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        ISectionDAO dao = getBean(ISectionDAO.class);
        String statusAccess = super.execute();
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        //valido permiso de crear y editar
        if(!isAdmin() && !getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_LINK)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            sectionId = Long.parseLong(getId());
        }
        sections = dao.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, sectionId, getServiciosActivos());
        sections.add(0, new TextHasValue("-- SELECCIONE --", ""));
        return statusAccess;
    }

    /**
     * @return the sectionId
     */
    public Long getSectionId() {
        return sectionId;
    }

    /**
     * @param sectionId the sectionId to set
     */
    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    /**
     * @return the sections
     */
    public List<ITextHasValue> getSections() {
        return sections;
    }

    /**
     * @param sections the sections to set
     */
    public void setSections(List<ITextHasValue> sections) {
        this.sections = sections;
    }


}