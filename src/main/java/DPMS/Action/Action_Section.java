package DPMS.Action;

import DPMS.DAOInterface.IAreaDAO;
import DPMS.DAOInterface.ISectionDAO;
import mx.bnext.access.ProfileServices;
import DPMS.Mapping.Section;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_Section extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> areas;
    private List<ITextHasValue> parents;
    
    @Override
    public String execute() throws Exception {
        IAreaDAO dao = getBean(IAreaDAO.class);  
        ISectionDAO sectionDAO = getBean(ISectionDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        Long areaId = null;
        Long parentId = null;
        if (getId() != null && !getId().equals("-1")) {
            Section section = sectionDAO.HQLT_findById(Long.parseLong(getId()));
            areaId = section.getAreaId();
            parentId = section.getParentId();
        }   
        areas = dao.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, areaId, getServiciosActivos());
        parents = sectionDAO.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, parentId, getServiciosActivos());
        areas.add(0, new TextHasValue(SELECCIONE, ""));
        parents.add(0, new TextHasValue(SELECCIONE, ""));
        return super.execute();
    }

    public List<ITextHasValue> getAreas() {
        return areas;
    }

    public void setAreas(List<ITextHasValue> areas) {
        this.areas = areas;
    }

    public List<ITextHasValue> getParents() {
        return parents;
    }

    public void setParents(List<ITextHasValue> parents) {
        this.parents = parents;
    }
    

}
