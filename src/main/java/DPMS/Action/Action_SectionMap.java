package DPMS.Action;

import DPMS.DAOInterface.ISectionDAO;
import mx.bnext.access.ProfileServices;
import DPMS.Mapping.SectionMap;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> Lares
 */
public class Action_SectionMap extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> sections;
    private List<ITextHasValue> selector;
    @Override
    public String execute() throws Exception {
        ISectionDAO dao = getBean(ISectionDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        Long sectionId = null;
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            SectionMap sectionMap = dao.HQLT_findById(SectionMap.class,Long.decode(getId()));
            sectionId = sectionMap.getSectionId();            
        }
        sections = dao.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, sectionId, getServiciosActivos());
        sections.add(0, new TextHasValue(SELECCIONE, ""));
        selector =  new ArrayList<>();
        selector.add(new TextHasValue(Utilities.getTag("sectionMap.fiveSItem"), 1L));
        selector.add(new TextHasValue(Utilities.getTag("sectionMap.fiveSSection"), 2L));
        
        return super.execute();
    }

    public List<ITextHasValue> getSections() {
        return sections;
    }

    public void setSections(List<ITextHasValue> sections) {
        this.sections = sections;
    }

    public List<ITextHasValue> getSelector() {
        return selector;
    }

    public void setSelector(List<ITextHasValue> selector) {
        this.selector = selector;
    }
    
}
