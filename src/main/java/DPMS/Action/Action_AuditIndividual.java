package DPMS.Action;

import DPMS.DAOInterface.IAuditIndividualDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditIndividualPending;
import DPMS.Mapping.AuditType;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import bnext.reference.UserRef;
import com.opensymphony.xwork2.ActionSupport;
import isoblock.surveys.dao.hibernate.SurveySearch;
import java.util.Date;
import mx.bnext.access.ProfileServices;

/**
 *
 * <AUTHOR>
 */
public class Action_AuditIndividual extends DefaultAction {
    
    public static String TASK_GOING_TO_CONFIRM = "confirm" ;
    public static String TASK_GOING_TO_EDIT = "edit" ;
    
    private AuditIndividualPending audit = null; 
    private boolean auditLiderGeneralAccess = false;
    private boolean auditLiderAccess = false;
    private boolean auditHelperAccess = false;
    private boolean auditedAccess = false;
    private boolean ponderated = false;
    private boolean externalStaff = false;
    private boolean requireMinimumScore = false;
    private Double minimumScore = null;
    private String surveyText = "Survey";
    private String task = TASK_GOING_TO_EDIT;
    private boolean auditCanBeEdited;
    private String departmentProcessAttendant;
    private String areaAttendant;
    private Long auditId = 0L;
            
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        String r = super.execute();//<-- hago esto para obtener los permisos base antes de todo
        //valido permiso basico
        if(getId().equals("-1") || !isAuditAccess()) {
            return "my-audit"; //<-- no hace nada
        }
        if (!parameter("pendingCamelName").equals("") && !isPendingValid()) {
            return "attended";
        }
        IAuditIndividualDAO dao = Utilities.getBean(IAuditIndividualDAO.class);
        audit = dao.HQLT_findById(AuditIndividualPending.class, Long.parseLong(getId()));
        //validacion de permiso detallado
        if(getSession().get("superAudit"+audit.getAudit().getBusinessUnit().getId()) == null) {
            getSession().put("superAudit"+audit.getAudit().getBusinessUnit().getId(),
                    Utilities.getBean(IUserDAO.class).getUserServicesByUne(getLoggedUserId(),audit.getAudit().getBusinessUnit().getId())
                    .contains(ProfileServices.AUDIT_QUALITY_MANAGER));
        }
        boolean superUser = (Boolean) getSession().get("superAudit"+audit.getAudit().getBusinessUnit().getId());
        auditLiderGeneralAccess = getLoggedUserId().equals(audit.getAudit().getAttendantId()) || superUser;
        auditLiderAccess =  getLoggedUserId().equals(audit.getAttendant().getId());
        auditHelperAccess = dao.isAuditHelper(audit.getId(), getLoggedUserId());
        externalStaff = audit.getAudit().getType().getSupportStaff() == 1
                || audit.getAudit().getType().getAuditorsInTraining() == 1
                || audit.getAudit().getType().getTechnicalExperts() == 1;
        requireMinimumScore = audit.getAudit().getType().getMinimumScore() == 1;
        if (requireMinimumScore) {
            minimumScore = audit.getMinimumScore();
        }
        if(validProcessScope(audit)) {
            auditedAccess = audit.getDepartmentProcess().getAttendantId().equals(getLoggedUserId());
        }
        if(validAreaScope(audit)) {
            auditedAccess = getLoggedUserId().equals(audit.getArea().getAttendantId());
        }
        auditCanBeEdited = audit.getStatus() < AuditIndividual.STATUS_PLANED_MAILS_SENT
                || audit.getStatus() == AuditIndividual.STATUS_WAITNG_FOR_DATE_CHANGE_BY_LEADER
                || audit.getStatus() == AuditIndividual.STATUS_ACTIVE_CONFIRMED
                || audit.getStatus() == AuditIndividual.STATUS_DONE
                || audit.getStatus() == AuditIndividual.STATUS_ACCEPTED
                || audit.getStatus() == AuditIndividual.STATUS_CANCELED;
        if(!auditLiderGeneralAccess && !auditLiderAccess && !auditHelperAccess && !auditedAccess && !isAdmin()) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        surveyText = dao.HQL_findSimpleString(""
                + " SELECT plainText "
                + " FROM " + SurveySearch.class.getCanonicalName()
                + " WHERE id = " + audit.getSurveyId()
                + " ");
        ponderated = dao.HQL_findSimpleInteger(""
                + "SELECT s.globals.obj.hasWeight "
                + "FROM isoblock.surveys.dao.hibernate.Survey s "
                + " WHERE s.id = " + audit.getAudit().getSurveyId()
        ).equals(1);
        if (audit.getDepartmentProcess() != null) {
            departmentProcessAttendant = dao.HQL_findSimpleString(""
                    + " SELECT c.description "
                    + " FROM " + UserRef.class.getCanonicalName() + " c"
                    + " WHERE c.id = " + audit.getDepartmentProcess().getAttendantId()
                    + " ");
        }
        if (audit.getAudit().getArea() != null) {
            areaAttendant =  dao.HQL_findSimpleString(""
                    + " SELECT c.description "
                    + " FROM " + UserRef.class.getCanonicalName() + " c"
                    + " WHERE c.id = " + audit.getAudit().getArea().getAttendantId()
                    + " ");
        }
        if(task.equals(TASK_GOING_TO_CONFIRM) 
                && AuditIndividual.STATUS.PLANNED_TO_CONFIRM.getValue().equals(audit.getStatus())
                && (auditLiderGeneralAccess || auditLiderAccess) && auditedAccess
                ) {
            /**
             * Si se pretende CONFIRMAR (se dio click en el boton "por confirmar" de mis auditorias)
             * Y el estatus de la auditoria es "por confirmar" ... 
             * */
            auditLiderAccess = false;
            auditLiderGeneralAccess = false;
        } else if(task.equals(TASK_GOING_TO_EDIT) 
                && audit.getStatus() <= AuditIndividual.STATUS.PLANNED_TO_CONFIRM.getValue()
                && (auditLiderGeneralAccess || auditLiderAccess) && auditedAccess
                ) {
            /**
             * Si se pretende EDITAR (se dio click en el boton "editar" de mis auditorias)
             * Y el estatus de la auditoria es "por confirmar" ... 
             * */
            auditedAccess = false;
        }
        getLogger().info("---> ID de auditoria individual: "+getId());
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("---> accesses: {}", Utilities.getSerializedObj(this));
        }
        auditId = audit.getAudit().getId();
        return r;
    }
    
    public static boolean validProcessScope(AuditIndividual audit) {
        return 
            (audit.getAudit().getType() == null && audit.getDepartmentProcess() != null)
            || 
            (audit.getAudit().getType() != null && AuditType.PROCESS_SCOPE.equals(audit.getAudit().getType().getScope()))
        ;
    }
    public static boolean validAreaScope(AuditIndividual audit) {
        return 
            (audit.getAudit().getType() == null && audit.getArea() != null) 
            || 
            (audit.getAudit().getType() != null && AuditType.AREA_SCOPE.equals(audit.getAudit().getType().getScope()))
        ;
    }
    
    
    public static boolean validProcessScope(AuditIndividualPending audit) {
        return 
            (audit.getAudit().getType() == null && audit.getDepartmentProcess() != null)
            || 
            (audit.getAudit().getType() != null && AuditType.PROCESS_SCOPE.equals(audit.getAudit().getType().getScope()))
        ;
    }
    public static boolean validAreaScope(AuditIndividualPending audit) {
        return 
            (audit.getAudit().getType() == null && audit.getArea() != null) 
            || 
            (audit.getAudit().getType() != null && AuditType.AREA_SCOPE.equals(audit.getAudit().getType().getScope()))
        ;
    }
    
    public String dateTmpParser(Date fecha) {
        return Utilities.formatDateBy(fecha, "yyyy-MM-dd'T'HH:mm:ss");
    }
    public String dateDteParser(Date fecha) {
        return Utilities.formatDateBy(fecha, "dd/MM/yyyy");
    }
    public String dateTmeParser(Date fecha) {
        return Utilities.formatDateBy(fecha, "HH:mm:ss");
    }

    public AuditIndividualPending getAudit() {
        return audit;
    }

    /**
     * @return the auditLiderGeneralAccess
     */
    public boolean isAuditLiderGeneralAccess() {
        return auditLiderGeneralAccess;
    }

    /**
     * @param auditLiderGeneralAccess the auditLiderGeneralAccess to set
     */
    public void setAuditLiderGeneralAccess(boolean auditLiderGeneralAccess) {
        this.auditLiderGeneralAccess = auditLiderGeneralAccess;
    }

    public boolean isAuditLiderAccess() {
        return auditLiderAccess;
    }

    public boolean isAuditHelperAccess() {
        return auditHelperAccess;
    }
    

    public boolean isAuditedAccess() {
        return auditedAccess;
    }

    public String getSurveyText() {
        return surveyText;
    }

    public void setSurveyText(String surveyText) {
        this.surveyText = surveyText;
    }

    public String getTask() {
        return task;
    }

    public String getAuditTypeLabel() { 
      IUntypedDAO dao = Utilities.getUntypedDAO();
      if (audit.getAudit().getTypeId()==null){
        return "No tiene valor";
      }
      String nn = dao.HQL_findSimpleString(""
              + "select t.description "
              + "from DPMS.Mapping.AuditType t "
              + "where t.id = :idttt ","idttt",audit.getAudit().getTypeId());
        return nn;
    }
    
    public void setTask(String task) {
        this.task = task;
    }

    public boolean isPonderated() {
        return ponderated;
    }

    public void setPonderated(boolean ponderated) {
        this.ponderated = ponderated;
    }

    /**
     * @return the externalStaff
     */
    public boolean isExternalStaff() {
        return externalStaff;
    }

    /**
     * @param externalStaff the externalStaff to set
     */
    public void setExternalStaff(boolean externalStaff) {
        this.externalStaff = externalStaff;
    }

    /**
     * @return the requireMinimumScore
     */
    public boolean isRequireMinimumScore() {
        return requireMinimumScore;
    }

    /**
     * @param requireMinimumScore the requireMinimumScore to set
     */
    public void setRequireMinimumScore(boolean requireMinimumScore) {
        this.requireMinimumScore = requireMinimumScore;
    }

    /**
     * @return the minimumScore
     */
    public Double getMinimumScore() {
        return minimumScore;
    }

    /**
     * @param minimumScore the minimumScore to set
     */
    public void setMinimumScore(Double minimumScore) {
        this.minimumScore = minimumScore;
    }

    /**
     * @return the auditCanBeEdited
     */
    public boolean isAuditCanBeEdited() {
        return auditCanBeEdited;
    }

    /**
     * @param auditCanBeEdited the auditCanBeEdited to set
     */
    public void setAuditCanBeEdited(boolean auditCanBeEdited) {
        this.auditCanBeEdited = auditCanBeEdited;
    }

    public String getDepartmentProcessAttendant() {
        return departmentProcessAttendant;
    }

    public String getAreaAttendant() {
        return areaAttendant;
    }

    public Long getAuditId() {
        return auditId;
    }

    public void setAuditId(Long auditId) {
        this.auditId = auditId;
    }
}