package DPMS.Action;

import DPMS.DAOInterface.IActionDAO;
import DPMS.DAOInterface.IAreaDAO;
import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IDepartmentDAO;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import java.util.ArrayList;
import java.util.List;
import mx.bnext.access.ProfileServices;


/**
 *
 * <AUTHOR>
 */
public class Action_General extends DefaultAction{
    List<ITextHasValue> businessUnits = new ArrayList<>();
    List<ITextHasValue> departments = new ArrayList<>();
    List<ITextHasValue> sources = new ArrayList<>();
    List<ITextHasValue> areas = new ArrayList<>();
    
    private boolean businessUnitSet = false;
    private boolean departmentSet = false;
    private boolean sourceSet = false;
    private boolean areasSet = false;
    @Override
    public String execute() throws Exception {
        if(businessUnitSet) {
            businessUnits = Utilities.getBean(IBusinessUnitDAO.class).getBusinessUnitByUser(getLoggedUserId(), "", isAdmin());
            if(!businessUnits.isEmpty()) {
                businessUnits.add(0, (ITextHasValue) new TextHasValue("-- SELECCIONE --", -1L));
            }
        }
        if(departmentSet) {
            departments = Utilities.getBean(IDepartmentDAO.class).getDepartmentByBusinessUnitId(0L);
        } else {
            departments = Utilities.getBean(IDepartmentDAO.class).getDeparmentsByUserOrBusinessUnitPosition(getLoggedUserId(), getLoggedUserServices().contains(ProfileServices.AUDIT_QUALITY_MANAGER));
        }
        
        if(!departments.isEmpty()) {
            departments.add(0, (ITextHasValue) new TextHasValue("-- SELECCIONE --", -1L));
        }
        if(sourceSet) {
            sources = Utilities.getBean(IActionDAO.class).sourceAction(0L, 0L);
            if(!sources.isEmpty()) {
                sources.add(0, (ITextHasValue) new TextHasValue("-- SELECCIONE --", -1L));
            }
        }
        return super.execute(); //To change body of generated methods, choose Tools | Templates.
    }

    public void setBusinessUnitSet(boolean businessUnitSet) {
        this.businessUnitSet = businessUnitSet;
    }
    
    public void setSourceSet(boolean sourceSet) {
        this.sourceSet = sourceSet;
    }

    public boolean isAreasSet() {
        return areasSet;
    }

    public void setAreasSet(boolean areasSet) {
        this.areasSet = areasSet;
    }
    
    
    
    public List<ITextHasValue> getBusinessUnits() {
        return businessUnits;
    }
    
    public List<ITextHasValue> getSources() {
        return sources;
    }
    
    public void setDepartmentSet(boolean departmentSet) {
        this.departmentSet = departmentSet;
    }
    
    public void setDepartments(List<ITextHasValue> departments) {
       this.departments = departments;
    }
    
    public List<ITextHasValue> getDepartments() {
        return departments;
    }

    public List<ITextHasValue> getAreas() {
        return areas;
    }

    public void setAreas(List<ITextHasValue> areas) {
        this.areas = areas;
    }
  }
