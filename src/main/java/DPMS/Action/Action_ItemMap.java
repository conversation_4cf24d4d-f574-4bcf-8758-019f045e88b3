package DPMS.Action;

import DPMS.DAOInterface.IItemDAO;
import DPMS.Mapping.ItemMap;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_ItemMap extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> items;
    @Override
    public String execute() throws Exception {
        IItemDAO dao = getBean(IItemDAO.class);
        
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        Long itemId = null;
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            ItemMap itemMap = dao.HQLT_findById(ItemMap.class,Long.decode(getId()));
            itemId = itemMap.getItemId();
        }
        items = dao.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, itemId, getServiciosActivos());
        if (!items.isEmpty()) {
        items.add(0, new TextHasValue(SELECCIONE, ""));
        }
        return super.execute();
    }

    public List<ITextHasValue> getItems() {
        return items;
    }

    public void setItems(List<ITextHasValue> items) {
        this.items = items;
    }
    

}
