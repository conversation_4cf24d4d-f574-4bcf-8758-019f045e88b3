package DPMS.Action;

import DPMS.DAOInterface.IAuditTypeDAO;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;
import java.util.Objects;
import mx.bnext.access.ProfileServices;

/**
 *
 * <AUTHOR>
 */
public class Action_Audit extends DefaultAction {

    private static final long serialVersionUID = 1L;
    
    private static final String SELECCIONE = "-- Seleccione --";
    public static String TASK_GOING_TO_VIEW = "view" ;
    public static String TASK_GOING_TO_EDIT = "edit" ;
    
    private final boolean auditedViewSurvey = Objects.equals(Utilities.getSettings().getAuditedViewSurvey(), 1);
    private Boolean auditQualityManager = false;
    private String paperbin = "false";
    private List<ITextHasValue> types;
    private String task = TASK_GOING_TO_EDIT;
    private String auditorLiderName = "";
    private String auditadoName = "";
    private String auditorApoyoName = "";
    
    private String score = "";
    //Método que se invoca de forma automatica

    @Override
    public String execute() throws Exception {
        getLogger().trace("---> ID de auditoria : " + getId());
        if (!isAuditAccess()) {
            return NONE;
        }
        auditQualityManager = getLoggedUserServices().contains(ProfileServices.AUDIT_QUALITY_MANAGER);
        
        IAuditTypeDAO dao = Utilities.getBean(IAuditTypeDAO.class);
        types = dao.getActives();

        if (getId() != null) {
            score = dao.HQL_findSimpleString("SELECT c.score FROM DPMS.Mapping.Audit c WHERE c.id = :id", "id", Long.parseLong(getId()));
            score = score == null || "null".equals(score)  ? "" : score;
        }
        return super.execute();
    }

    public Boolean getAuditQualityManager() {
        return auditQualityManager;
    }
    public boolean isAuditedViewSurvey() {
        return auditedViewSurvey;
    }

    public void setAuditQualityManager(Boolean auditQualityManager) {
        this.auditQualityManager = auditQualityManager;
    }

    public List<ITextHasValue> getTypes() {
        return types;
    }

    public void setTypes(List<ITextHasValue> types) {
        this.types = types;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public String getAuditorLiderName() {
        return auditorLiderName;
    }

    public void setAuditorLiderName(String auditorLiderName) {
        this.auditorLiderName = auditorLiderName;
    }

    public String getAuditadoName() {
        return auditadoName;
    }

    public void setAuditadoName(String auditadoName) {
        this.auditadoName = auditadoName;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getAuditorApoyoName() {
        return auditorApoyoName;
    }

    public void setAuditorApoyoName(String auditorApoyoName) {
        this.auditorApoyoName = auditorApoyoName;
    }

    public String getPaperbin() {
        return paperbin;
    }

    public void setPaperbin(String paperbin) {
        this.paperbin = paperbin;
    }
}
