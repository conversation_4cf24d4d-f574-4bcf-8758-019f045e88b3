package DPMS.Action;

import DPMS.Mapping.InternalDocumentCatalog;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import qms.framework.util.CacheRegion;

public class Action_InternalDocumentCatalog extends DefaultAction {

    private String status;
    private Integer deleted;
    
    @Override
    public String execute() throws Exception {
        String rValue = super.execute();
        Long id = Long.valueOf(this.getId());
        if(-1L == id){
            return rValue;
        }
        InternalDocumentCatalog catalog = Utilities.getUntypedDAO().HQLT_findById(InternalDocumentCatalog.class, id, true, CacheRegion.SURVEY, 0);
        this.setCode(catalog.getCode());
        this.setDescription(catalog.getDescription());
        this.setStatus(catalog.getStatus().toString());
        deleted = catalog.getDeleted();
        return rValue;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Override
    public String getStatus() {
        return status;
    }

    @Override
    public void setStatus(String status) {
        this.status = status;
    }
}