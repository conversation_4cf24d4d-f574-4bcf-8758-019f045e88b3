package DPMS.Action;

import Framework.Action.DefaultAction;

public class Action_Logging extends DefaultAction {
    private String record_id;
    private String logElement;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        return super.execute();
    }

    public String getRecord_id() {
        return record_id;
    }

    public void setRecord_id(String record_id) {
        this.record_id = record_id;
    }

    public String getLogElement() {
        return logElement;
    }

    public void setLogElement(String logElement) {
        this.logElement = logElement;
    } 
 }
