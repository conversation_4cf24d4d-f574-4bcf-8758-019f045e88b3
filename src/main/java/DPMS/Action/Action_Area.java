package DPMS.Action;

import DPMS.DAOInterface.IBuildingDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IUserDAO;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.UserHasValue;
import java.util.List;

/**
 *
 * <AUTHOR> Limas
 */
public class Action_Area extends Action_AreaList {
    private static final String SELECCIONE = "-- Seleccione --";
    private List<UserHasValue> attendant;
    private List<ITextHasValue> department;
    private List<ITextHasValue> building;

    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        attendant = getBean(IUserDAO.class).getUsersInBusinessUnitOf(isAdmin(), getLoggedUserId(),getServiciosActivos());
        attendant.add(0, new UserHasValue(SELECCIONE, ""));
        department = getBean(IBusinessUnitDepartmentLoadDAO.class)
                .getStrutsComboListWithBusinessUnit(isAdmin(), getLoggedUserId(), getServiciosActivos());
        department.add(0, new TextHasValue(SELECCIONE, ""));
        
        building = getBean(IBuildingDAO.class).getActivesInMyBusinessUnit(getLoggedUserId(), isAdmin());
        building.add(0, new TextHasValue(SELECCIONE, ""));
        
        return super.execute();
    }

    public List<UserHasValue> getAttendant() {
        return attendant;
    }

    public void setAttendant(List<UserHasValue> attendant) {
        this.attendant = attendant;
    }

    public List<ITextHasValue> getDepartment() {
        return department;
    }

    public void setDepartment(List<ITextHasValue> department) {
        this.department = department;
    }

    public List<ITextHasValue> getBuilding() {
        return building;
    }

    public void setBuilding(List<ITextHasValue> building) {
        this.building = building;
    }

    
 }
