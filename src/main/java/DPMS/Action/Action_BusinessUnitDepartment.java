package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import java.util.List;
import java.util.Objects;
import qms.configuration.entity.Region;
import qms.configuration.entity.Zone;

/**
 *
 * <AUTHOR>
 */
public class Action_BusinessUnitDepartment extends DefaultAction {
    
    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> businessUnits;
    private List<ITextHasValue> regions;
    private List<ITextHasValue> zones;
    private Integer regionCatalogEnabled;
    private Integer zoneCatalogEnabled;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        final String businessUnitFilter;
        final IBusinessUnitDAO DAO = Utilities.getBean(IBusinessUnitDAO.class);
        if (!isAdmin()) {
            businessUnitFilter = " "
                    + " EXISTS ( "
                        + " SELECT 1  "
                        + " FROM " + User.class.getCanonicalName() + " AS u "
                        + " JOIN u.puestos as p "
                        + " JOIN p.perfil profile"
                        + " LEFT JOIN p.une as bu "
                        + " LEFT JOIN p.corp as org "
                        + " WHERE u.id = " + getLoggedUserId()
                        + " AND ("
                            + " c.id = bu.id"
                            + " OR ("
                                + " profile.intBUsuarioCorporativo = 1"
                                + " AND c.organizationalUnitId = org.id"
                            + " )"
                        + " )"
                    + ")"
                    + " AND c.status = " + BusinessUnit.ACTIVE_STATUS 
                    + " AND c.deleted = "+ BusinessUnit.IS_NOT_DELETED ;
        } else {
          businessUnitFilter = "c.status = " + BusinessUnit.ACTIVE_STATUS 
                    + " AND c.deleted = "+ BusinessUnit.IS_NOT_DELETED ;
        }
        final Settings settings = Utilities.getSettings();
        regionCatalogEnabled = settings.getRegionCatalogEnabled();
        zoneCatalogEnabled = settings.getZoneCatalogEnabled();
        businessUnits = DAO.getStrutsComboList(businessUnitFilter);
        businessUnits.add(0, new TextHasValue(SELECCIONE, ""));
        if (Objects.equals(regionCatalogEnabled, 1)) {
            regions = DAO.getStrutsComboList(Region.class, "description", "c.status = " + Region.ACTIVE_STATUS 
                        + " AND c.deleted = "+ Region.IS_NOT_DELETED);
            regions.add(0, new TextHasValue(SELECCIONE, ""));
        }
        if (Objects.equals(zoneCatalogEnabled, 1)) {
            zones = DAO.getStrutsComboList(Zone.class, "description", "c.status = " + Zone.ACTIVE_STATUS 
                        + " AND c.deleted = "+ Zone.IS_NOT_DELETED);
            zones.add(0, new TextHasValue(SELECCIONE, ""));
        }
        
        return super.execute();
    }

    public List<ITextHasValue> getBusinessUnits() {
        return businessUnits;
    }

    public List<ITextHasValue> getRegions() {
        return regions;
    }

    public List<ITextHasValue> getZones() {
        return zones;
    }

    public Integer getRegionCatalogEnabled() {
        return regionCatalogEnabled;
    }

    public Integer getZoneCatalogEnabled() {
        return zoneCatalogEnabled;
    }
    

 }
