package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.Mapping.Profile;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import com.opensymphony.xwork2.ActionSupport;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_PrintableBusinessUnitDepartment extends DefaultAction {
    
    private Long businessUnitDepartmentId = null;
    private List<ITextHasValue> businessUnitDepartments = null;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        IBusinessUnitDepartmentLoadDAO dao = getBean(IBusinessUnitDepartmentLoadDAO.class);
        String statusAccess = super.execute();
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        //valido permiso de crear y editar
        if(!isAdmin() && !isFiveSManager
                && !getLoggedUserServices().contains(ProfileServices.FIVES_LINK)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            businessUnitDepartmentId = Long.parseLong(getId());
        }
        businessUnitDepartments = dao.getActivesByBusinessUnitDepartment(businessUnitDepartmentId,
                isAdmin(), isFiveSManager, getLoggedUserId(), getServiciosActivos());
        businessUnitDepartments.add(0, new TextHasValue("-- SELECCIONE --", ""));
        return statusAccess;
    }

    /**
     * @return the businessUnitDepartmentId
     */
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    /**
     * @param businessUnitDepartmentId the businessUnitDepartmentId to set
     */
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    /**
     * @return the businessUnitDepartments
     */
    public List<ITextHasValue> getBusinessUnitDepartments() {
        return businessUnitDepartments;
    }

    /**
     * @param businessUnitDepartments the businessUnitDepartments to set
     */
    public void setBusinessUnitDepartments(List<ITextHasValue> businessUnitDepartments) {
        this.businessUnitDepartments = businessUnitDepartments;
    }


}