package DPMS.Action;

import DPMS.Mapping.User;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import com.google.common.collect.ImmutableMap;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import qms.util.UserUtil;
import qms.util.dto.FormApproverAnalyst;
import qms.util.dto.FormApproverAnalystByDepartment;

/**
 *
 * <AUTHOR>
 */
public class Action_UserEdit extends Action_Preferences {
    
    private boolean isFormApprover;
    private List<FormApproverAnalystByDepartment> approverAnalysts;
    private String timezoneUser;
    
    @Override
    public String execute() throws Exception {
        if (Utilities.isAnonymous(getLoggedUserAccount())) {
            return NO_ACCESS;
        }
        String result = super.execute();
        if (!SUCCESS.equals(result)) {
            return result;
        }
        String timezone = getUntypedDAO().HQL_findSimpleString(""
                + " SELECT "
                    + "u.timezone "
                + " FROM " + User.class.getCanonicalName() + " u"
                + " WHERE u.id = :userId ", 
                ImmutableMap.of("userId", getLoggedUserId())
        );
        if (timezone == null || timezone.isEmpty()) {
            timezone = Utilities.getSettings().getTimeZone();
        }
        setTimezoneUser(timezone);
        List<FormApproverAnalyst> approvers = UserUtil.getFormApproverAnalyst(getLoggedUserId());
        // Se llena bandera
        this.isFormApprover = !approvers.isEmpty();
        // Se llena listado para iterar
        this.approverAnalysts = new ArrayList<>(approvers.size());
        approvers.forEach((approver) -> {
            FormApproverAnalystByDepartment departmentRegistry = new FormApproverAnalystByDepartment(approver);
            int index = this.approverAnalysts.indexOf(departmentRegistry);
            if (index == -1) {
                departmentRegistry.setAnalysts(new LinkedHashSet<>(1));
                this.approverAnalysts.add(departmentRegistry);
                // Se agrega opción "-- SELECCIONE --"
                departmentRegistry.getAnalysts().add(new TextHasValue(
                    "-- Seleccione --", ""
                ));
            } else {
                departmentRegistry = this.approverAnalysts.get(index);
            }
            if (
                // Se excluye el usuario logeado
                !getLoggedUserId().equals(approver.getAnalystUserId())
                // Se excluyen los sustitos actuales
                && !approver.getAnalystUserId().equals(departmentRegistry.getCancelationWhileAwayUserId())
                && !approver.getAnalystUserId().equals(departmentRegistry.getAdjustmentWhileAwayUserId())
                && !approver.getAnalystUserId().equals(departmentRegistry.getReopenWhileAwayUserId())
            ) {
                departmentRegistry.getAnalysts().add(new TextHasValue(
                    approver.getAnalystUserName(),
                    approver.getAnalystUserId()
                ));
            }
        });
        return result;
    }

    public List<TextHasValue> getTimezonesList() {
        return Utilities.allTimezones();
    }
    public boolean isIsFormApprover() {
        return isFormApprover;
    }

    public List<FormApproverAnalystByDepartment> getApproverAnalysts() {
        return approverAnalysts;
    }

    public String getTimezoneUser() {
        return timezoneUser;
    }

    public void setTimezoneUser(String timezoneUser) {
        this.timezoneUser = timezoneUser;
    }
}
