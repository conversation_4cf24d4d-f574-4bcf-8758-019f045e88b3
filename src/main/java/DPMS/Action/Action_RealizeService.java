package DPMS.Action;

import DPMS.DAOInterface.IDeviceDAO;
import DPMS.DAOInterface.IServiceScheduleDAO;
import DPMS.Mapping.ServiceResult;
import DPMS.Mapping.ServiceType;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import java.util.List;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

public class Action_RealizeService extends DefaultAction {
   private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> results;
    private List<ITextHasValue> devices;
    private List<ITextHasValue> serviceTypes;
    private Long serviceScheduleId;
    private String task;
    private boolean readOnly;
    private String titleType;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        IServiceScheduleDAO shDao = getBean(IServiceScheduleDAO.class);
        IDeviceDAO daoDevice = getBean(IDeviceDAO.class);

        results = shDao.getStrutsComboList(ServiceResult.class, false, null, 0);
        results.add(0, new TextHasValue(SELECCIONE, ""));
        Long deviceId = shDao.getDeviceIdByServiceScheduleId(serviceScheduleId);
        devices = daoDevice.getDevicesInMyBusinessUnit(deviceId, isAdmin(), getLoggedUserId(), getServiciosActivos()); 
        
        devices.add(0, new TextHasValue(SELECCIONE, ""));
        serviceTypes = shDao.getStrutsComboList(ServiceType.class, false, null, 0);
        serviceTypes.add(0, new TextHasValue(SELECCIONE, "")); 
        return super.execute();
    }

    public List<ITextHasValue> getResults() {
        return results;
    }

    @StrutsParameter
    public void setResults(List<ITextHasValue> results) {
        this.results = results;
    }

    public List<ITextHasValue> getDevices() {
        return devices;
    }

    @StrutsParameter
    public void setDevices(List<ITextHasValue> devices) {
        this.devices = devices;
    }

    public List<ITextHasValue> getServiceTypes() {
        return serviceTypes;
    }

    @StrutsParameter
    public void setServiceTypes(List<ITextHasValue> serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public Long getServiceScheduleId() {
        return serviceScheduleId;
    }

    @StrutsParameter
    public void setServiceScheduleId(Long serviceScheduleId) {
        this.serviceScheduleId = serviceScheduleId;
    }

    /**
     * @return the task
     */
    public String getTask() {
        return task;
    }

    /**
     * @param task the task to set
     */
    @StrutsParameter
    public void setTask(String task) {
        this.task = task;
    }
    
    public boolean isDeviceEditImplementation() {
        return (this.getId() == null || Long.parseLong(this.getId()) <= 0)
               && Utilities.getSettings().getDeviceEditImplementation() == 1;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }

    public String getTitleType() {
        return titleType;
    }

    @StrutsParameter
    public void setTitleType(String titleType) {
        this.titleType = titleType;
    }

 }
