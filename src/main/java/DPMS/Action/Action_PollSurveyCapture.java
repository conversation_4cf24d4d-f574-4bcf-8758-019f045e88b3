package DPMS.Action;

import DPMS.DAOInterface.IOutstandingSurveysDAO;
import DPMS.DAOInterface.IPollDAO;
import DPMS.Mapping.Action;
import DPMS.Mapping.ActionSources; 
import DPMS.Mapping.Poll;
import DPMS.Mapping.PollRespondent;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import static com.opensymphony.xwork2.Action.NONE;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.struts2.action.SurveyCaptureAction;
import java.util.List;
import mx.bnext.access.ProfileServices;

/**
 *
 * <AUTHOR>
 */
public class Action_PollSurveyCapture extends SurveyCaptureAction {

    private static final long serialVersionUID = 1L;

    @Override
    public String execute() throws Exception {
        String result = super.defaultActionExecute();
        if (!SUCCESS.equals(result)) {
            return result;
        }
        setTask(TASK_GOING_TO_FILL);
        if (Utilities.isInteger(getId()) && isPoll()) {
            //si es una encuesta
            getLogger().debug(" --- es encuesta! {}", getId());
            //si tengo permiso de responder
            IPollDAO dao = getBean(IPollDAO.class);
            Poll poll = dao.HQLT_findById(Long.valueOf(getId()));
            if (poll == null) {
                getLogger().info("ID de encuesta invalido!");
                return NONE;
            }
            String hql = ""
                    + " SELECT c.outstandingSurveysId"
                    + " FROM " + PollRespondent.class.getCanonicalName() + " c "
                    + " WHERE "
                    + " c.id.pollId = " + getId()
                    + " AND c.id.respondentId = "
                    + ((isAdmin()
                    || getLoggedUserServices().contains(ProfileServices.SURVEY_MANAGER)
                    || getLoggedUserServices().contains(ProfileServices.SURVEY_SUPERVISOR))
                            ? getUserId()
                            : getLoggedUserId())
                    + "";
            getLogger().debug("-- filter : {}", hql);
            getLogger().debug("-- getLoggedUserId : {}", getLoggedUserId());
            getLogger().debug("-- getUserId : {}", getUserId());
            List<Long> temp = dao.HQL_findByQuery(hql);
            if (temp == null || temp.isEmpty()) {
                getLogger().info("-- NO SE ENCONTRÓ EL REGISTRO Ó EL USUARIO NO TIENE PERMISO PARA VER EL DETALLE -- ");
                return NONE;
            }
            Long outstandingSurvey = temp.get(0);
            getLogger().debug("-- pollRespondent : {}", outstandingSurvey);
            setActionSources(dao.getStrutsComboList(ActionSources.class, "code", "c.moduleId = " + ActionSources.MODULE_POLLS));
            if (getActionSources().isEmpty()) {
                getActionSources().add(new TextHasValue("-Sin fuentes de acciones configuradas-", ""));
            } else {
                getActionSources().add(0, new TextHasValue("-- Seleccione --", ""));
            }
            getActionTypes().add(new TextHasValue("-- Seleccione --", ""));
            getActionTypes().add(new TextHasValue("Correctiva", Action.ACTION_TYPE_CORRECTIVE));
            getActionTypes().add(new TextHasValue("Preventiva", Action.ACTION_TYPE_PREVENTIVE));
            getActionTypes().add(new TextHasValue("Mejora continua", Action.ACTION_TYPE_IMPROVEMENT));
            getActionTypes().add(new TextHasValue("Producto no conforme", Action.ACTION_TYPE_PRODUCT));
            getActionTypes().add(new TextHasValue("Sobre proyecto(s)", Action.ACTION_TYPE_PROJECT));
            IOutstandingSurveysDAO bean = Utilities.getBean(IOutstandingSurveysDAO.class);
            OutstandingSurveys survey = bean.HQLT_findById(outstandingSurvey);
            setPendiente(survey);
            setLoadAction("Poll.Survey.Capture");
            setId("O" + getPendiente().getId());
            setSerializedOutstandingSurvey(Utilities.getSerializedObj(getPendiente()));
        }
        return super.execute();
    }

}
