package DPMS.Action;

import DPMS.DAOInterface.IClauseTypeDAO;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_Clause extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> types;
    
    @Override
    public String execute() throws Exception {
        IClauseTypeDAO dao = Utilities.getBean(IClauseTypeDAO.class);
        if (getId() == null) {
            types = dao.getActives();
        } else {
            types = dao.getActivesAndSelected(Long.valueOf(getId()));
        }
        types.add(0, new TextHasValue(SELECCIONE, ""));
        return super.execute();
    }

    public List<ITextHasValue> getTypes() {
        return types;
    }

    public void setTypes(List<ITextHasValue> types) {
        this.types = types;
    }

}
