package DPMS.Action;

import isoblock.surveys.dao.hibernate.Survey;

/**
 *
 * <AUTHOR>
 */
public class Action_PollSurveyList  extends Action_SurveyList {

    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        getLogger().trace("Entrando a execute..."+getJs_controller());
        return super.execute();
    }
    
    @Override
    public String getJs_controller() {
        return Survey.TYPE_POLL + ".survey";
    }
}
