package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDAO;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_Building extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> businessUnits;
    
    @Override
    public String execute() throws Exception {
        IBusinessUnitDAO dao = Utilities.getBean(IBusinessUnitDAO.class);
        businessUnits = dao.getStrutsComboList();
        businessUnits.add(0, new TextHasValue(SELECCIONE, ""));
        return super.execute();
    }

    public List<ITextHasValue> getBusinessUnits() {
        return businessUnits;
    }

    public void setBusinessUnits(List<ITextHasValue> types) {
        this.businessUnits = types;
    }

}
