package DPMS.Action;

import Framework.Action.DefaultAction;
import Framework.Config.Utilities;

/**
 *
 * <AUTHOR>
 */
public class Action_AreaList extends DefaultAction {

    public boolean isIsAreaCustomFieldsEnabled() {
        return Utilities.getSettings().getIsAreaCustomFieldsEnabled();
    }

    public Integer getAdditionalExtraAreaFields() {
        return Utilities.getSettings().getAdditionalExtraAreaFields();
    }

    public String getLabelAreaField1() {
        return Utilities.getSettings().getLabelAreaField1();
    }

    public String getLabelAreaField2() {
        return Utilities.getSettings().getLabelAreaField2();
    }

    public String getLabelAreaField3() {
        return Utilities.getSettings().getLabelAreaField3();
    }

    public String getLabelAreaField4() {
        return Utilities.getSettings().getLabelAreaField4();
    }

    public String getLabelAreaField5() {
        return Utilities.getSettings().getLabelAreaField5();
    }

    public String getLabelAreaField6() {
        return Utilities.getSettings().getLabelAreaField6();
    }

    public String getLabelAreaField7() {
        return Utilities.getSettings().getLabelAreaField7();
    }

    public String getLabelAreaField8() {
        return Utilities.getSettings().getLabelAreaField8();
    }

    public String getLabelAreaField9() {
        return Utilities.getSettings().getLabelAreaField9();
    }

    public String getLabelAreaField10() {
        return Utilities.getSettings().getLabelAreaField10();
    }

    public String getLabelAreaField11() {
        return Utilities.getSettings().getLabelAreaField11();
    }

    public String getLabelAreaField12() {
        return Utilities.getSettings().getLabelAreaField12();
    }

    public String getLabelAreaField13() {
        return Utilities.getSettings().getLabelAreaField13();
    }

    public String getLabelAreaField14() {
        return Utilities.getSettings().getLabelAreaField14();
    }

    public String getLabelAreaField15() {
        return Utilities.getSettings().getLabelAreaField15();
    }

    public String getLabelAreaField16() {
        return Utilities.getSettings().getLabelAreaField16();
    }

    public String getLabelAreaField17() {
        return Utilities.getSettings().getLabelAreaField17();
    }

    public String getLabelAreaField18() {
        return Utilities.getSettings().getLabelAreaField18();
    }

    public String getLabelAreaField19() {
        return Utilities.getSettings().getLabelAreaField19();
    }

    public String getLabelAreaField20() {
        return Utilities.getSettings().getLabelAreaField20();
    }

    public String getLabelAreaField21() {
        return Utilities.getSettings().getLabelAreaField21();
    }

    public String getLabelAreaField22() {
        return Utilities.getSettings().getLabelAreaField22();
    }

    public String getLabelAreaField23() {
        return Utilities.getSettings().getLabelAreaField23();
    }

    public String getLabelAreaField24() {
        return Utilities.getSettings().getLabelAreaField24();
    }

    public String getLabelAreaField25() {
        return Utilities.getSettings().getLabelAreaField25();
    }

    public String getLabelAreaField26() {
        return Utilities.getSettings().getLabelAreaField26();
    }
 }
