package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.Mapping.DepartmentMap;
import DPMS.Mapping.Profile;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;

/**
 *
 * <AUTHOR> Lares
 */
public class Action_DepartmentMap extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> departments;
    @Override
    public String execute() throws Exception {
        IBusinessUnitDepartmentLoadDAO dao = Utilities.getBean(IBusinessUnitDepartmentLoadDAO.class);
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        Long departmentId = null;
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            DepartmentMap departmentMap = dao.HQLT_findById(DepartmentMap.class,Long.decode(getId()));
            departmentId = departmentMap.getDepartmentId();
        }
        ProfileServices[] service = {ProfileServices.ALL};
        departments = dao.getActivesByBusinessUnitDepartment(departmentId, isAdmin(), isFiveSManager, getLoggedUserId(), service);
        departments.add(0, new TextHasValue(SELECCIONE, ""));
        
        return super.execute();
    }

    public List<ITextHasValue> getDepartments() {
        return departments;
    }

    public void setDepartments(List<ITextHasValue> departments) {
        this.departments = departments;
    }
    

}
