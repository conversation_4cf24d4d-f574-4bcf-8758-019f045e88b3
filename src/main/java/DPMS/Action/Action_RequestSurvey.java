package DPMS.Action;

import Framework.Config.Utilities;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.struts2.action.SurveyAction;

/**
 * Created on : Dec 31, 2014, 3:01:48 PM
 *
 * <AUTHOR> @ Block Networks S.A. de C.V.
 */
public class Action_RequestSurvey extends SurveyAction {

    private static enum viewModes {
        NEW,
        MODIFY,
        APROVE,
        CANCEL,
        EDIT;
    }

    private Long templateSurveyId;
    private Long requestId;
    private Long documentId;
    private Boolean userDirectBossOptional;

    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        getLogger().trace("DPMS.Action.Action_RequestSurvey.execute()");
        setSetEvaluationType(false);
        setSurveyType(Survey.TYPE_REQUEST);
        userDirectBossOptional = Utilities.getSettings().getUserDirectBossOptional();
        if (getTemplateSurveyId() != -1L) {
            setId(getTemplateSurveyId().toString());
        }
        return super.execute();
    }

    public Long getTemplateSurveyId() {
        if (templateSurveyId == null) {
            templateSurveyId = -1L;
        }
        return templateSurveyId;
    }

    public void setTemplateSurveyId(Long templateSurveyId) {
        this.templateSurveyId = templateSurveyId;
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    public Boolean getUserDirectBossOptional() {
        return userDirectBossOptional;
    }
}
