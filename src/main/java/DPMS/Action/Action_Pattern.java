package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.Mapping.Pattern;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.StandardEntity.STATUS;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import mx.bnext.access.ProfileServices;

public class Action_Pattern extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> businessUnit;

    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        IBusinessUnitDAO DAO = Utilities.getBean(IBusinessUnitDAO.class);
        ProfileServices[] service = {ProfileServices.ALL};
        Long businessUnitO = 0L;
        if (this.getId() != null) {
            businessUnitO = DAO.HQL_findLong(""
            +   " SELECT p.businessUnitId FROM "
                    + Pattern.class.getCanonicalName() + " as p "
                    + " WHERE "
                    + " p.id = :id", ImmutableMap.of("id", Long.parseLong(this.getId())));
        }
        
        businessUnit = DAO.getStrutsComboList(""
                + " c.status = "+STATUS.ACTIVE.getValue(), getLoggedUserId(), service, isAdmin(), businessUnitO);
        businessUnit.add(0, new TextHasValue(SELECCIONE, ""));
        return super.execute();
    }

    public List<ITextHasValue> getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(List<ITextHasValue> deviceGroup) {
        this.businessUnit = deviceGroup;
    }
}
