package DPMS.Action;

import DPMS.DAOInterface.IItemDAO;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.apache.struts2.ActionSupport;
import org.apache.struts2.interceptor.parameter.StrutsParameter;

/**
 *
 * <AUTHOR>
 */
public class Action_PrintableItem extends DefaultAction {
    
    private Long itemId = null;
    private List<ITextHasValue> items = null;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        IItemDAO dao = getBean(IItemDAO.class);
        String statusAccess = super.execute();
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        //valido permiso de crear y editar
        if(!isAdmin() && !getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_LINK)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            itemId = Long.parseLong(getId());
        }
        items = dao.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, itemId, getServiciosActivos());
        items.add(0, new TextHasValue("-- SELECCIONE --", ""));
        return statusAccess;
    }

    /**
     * @return the itemId
     */
    public Long getItemId() {
        return itemId;
    }

    /**
     * @param itemId the itemId to set
     */
    @StrutsParameter
    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    /**
     * @return the items
     */
    public List<ITextHasValue> getItems() {
        return items;
    }

    /**
     * @param items the items to set
     */
    @StrutsParameter
    public void setItems(List<ITextHasValue> items) {
        this.items = items;
    }


}