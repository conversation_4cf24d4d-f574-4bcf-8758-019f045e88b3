package DPMS.Action;

import DPMS.DAOInterface.IBuildingDAO;
import DPMS.Mapping.Profile;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import com.opensymphony.xwork2.ActionSupport;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_PrintableBuilding extends DefaultAction {
    
    private Long buildingId = null;
    private List<ITextHasValue> buildings = null;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        IBuildingDAO dao = Utilities.getBean(IBuildingDAO.class);
        String statusAccess = super.execute();
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        //valido permiso de crear y editar
        if(!isAdmin() && !isFiveSManager
                && !getLoggedUserServices().contains(ProfileServices.FIVES_LINK)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            buildingId = Long.parseLong(getId());
        }
        buildings = dao.getActivesInMyBusinessUnit(getLoggedUserId(),isAdmin());
        buildings.add(0, new TextHasValue("-- SELECCIONE --", ""));
        return statusAccess;
    }

    /**
     * @return the buildingId
     */
    public Long getBuildingId() {
        return buildingId;
    }

    /**
     * @param buildingId the buildingId to set
     */
    public void setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
    }

    /**
     * @return the buildings
     */
    public List<ITextHasValue> getBuildings() {
        return buildings;
    }

    /**
     * @param buildings the buildings to set
     */
    public void setBuildings(List<ITextHasValue> buildings) {
        this.buildings = buildings;
    }


}