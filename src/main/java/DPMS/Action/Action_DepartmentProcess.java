package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.Mapping.DepartmentProcess;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_DepartmentProcess extends DefaultAction {
    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> department;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        IBusinessUnitDepartmentLoadDAO dao =
                Utilities.getBean(IBusinessUnitDepartmentLoadDAO.class);
        department = dao.getStrutsComboListWithBusinessUnit(isAdmin(), getLoggedUserId(), getServiciosActivos());
        department.add(0, new TextHasValue(SELECCIONE, ""));
        if (Utilities.isInteger(this.getId())) {
            TextHasValue d = dao.HQLT_findSimple(TextHasValue.class, ""
                + " SELECT new " + TextHasValue.class.getCanonicalName() + " ("
                    + " d.description,"
                    + " d.id"
                + " )"
                + " FROM " + DepartmentProcess.class.getCanonicalName() + " c "
                + " JOIN c.department d"
                + " WHERE c.id = " + this.getId()
            );

            if (!department.contains(d)) {
                department.add(1, d);
            }
        }
        
        return super.execute();
    }

    public List<ITextHasValue> getDepartment() {
        return department;
    }

    public void setDepartment(List<ITextHasValue> department) {
        this.department = department;
    }
 }
