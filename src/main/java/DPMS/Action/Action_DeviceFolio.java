package DPMS.Action;

import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.DeviceType;
import Framework.Action.DefaultAction;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class Action_DeviceFolio extends DefaultAction {
    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> deviceType;
    private List<ITextHasValue> department = new ArrayList<ITextHasValue>();
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        IUntypedDAO deviceTypeDAO = getUntypedDAO();
        deviceType = deviceTypeDAO.getStrutsComboList(DeviceType.class, false, null, 0);
        deviceType.add(0, new TextHasValue(SELECCIONE, ""));        
        List<BusinessUnitDepartmentLite> dptos  = Utilities.getBean(IUserDAO.class)
                .getDepartments(this.getLoggedUserId().toString(), this.isAdmin(), isCorporative());
        Iterator<BusinessUnitDepartmentLite> it = dptos.iterator();
        BusinessUnitDepartmentLite d;
        while(it.hasNext()){
          d = it.next();
          department.add(new TextHasValue(d.getDescription(), d.getId()));
        }
        department.add(0, new TextHasValue(SELECCIONE, "")); 
        return super.execute();
    }

    public List<ITextHasValue> getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(List<ITextHasValue> deviceType) {
        this.deviceType = deviceType;
    }

    public List<ITextHasValue> getDepartment() {
        return department;
    }

    public void setDepartment(List<ITextHasValue> department) {
        this.department = department;
    }

 }
