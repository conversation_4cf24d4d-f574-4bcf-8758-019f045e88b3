package DPMS.Action;

import Framework.Action.DefaultAction;
import Framework.Config.Utilities;

public class Action_RealizedServices extends DefaultAction {
    private Long deviceId;
    private String task;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        
        return super.execute();
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getTask() {
        return task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    public boolean isServiceToApprove() {
        return Utilities.getSettings().getServiceToApprove().equals(1);
    }
 }
