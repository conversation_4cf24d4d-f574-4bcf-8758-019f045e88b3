package DPMS.Action;

import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.Mapping.Profile;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import Framework.Config.Utilities;
import com.opensymphony.xwork2.ActionSupport;

/**
 *
 * <AUTHOR>
 */
public class Action_MyPrintable extends DefaultAction {
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        IBusinessUnitDepartmentLoadDAO dao = Utilities.getBean(IBusinessUnitDepartmentLoadDAO.class);
        String statusAccess = super.execute();
        //valido permiso de crear y editar
        if(!isAdmin() && !getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_CREATE)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_LINK)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_PRINT)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        return statusAccess;
    }
    
}