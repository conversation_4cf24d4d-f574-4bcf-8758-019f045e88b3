package DPMS.Action;

import DPMS.DAOInterface.IAuditTypeDAO;
import DPMS.Mapping.AuditType;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_AuditType extends DefaultAction {

    private static final String SELECCIONE = "-- Seleccione --";
    private List<ITextHasValue> scope;
    private List<ITextHasValue> services;
    private List<ITextHasValue> externalStaffServices; 
    
    @Override
    public String execute() throws Exception {
        IAuditTypeDAO dao = Utilities.getBean(IAuditTypeDAO.class);
        scope = dao.getScope();
        scope.add(0, new TextHasValue(SELECCIONE, ""));        
        services = new ArrayList<>();
        services.add(new TextHasValue(AuditType.Servicios.QUALIFIED_QUESTION));
        services.add(new TextHasValue(AuditType.Servicios.FINDING_CAPABLE));
        services.add(new TextHasValue(AuditType.Servicios.CONFIRMED_BY_AUDITED));
        services.add(new TextHasValue(AuditType.Servicios.ACCEPTED_BY_AUDITED));
        services.add(new TextHasValue(AuditType.Servicios.ADD_ACTIVITY));
        services.add(new TextHasValue(AuditType.Servicios.MINIMUM_SCORE));
        externalStaffServices = new ArrayList<>();
        externalStaffServices.add(new TextHasValue(AuditType.Servicios.SUPPORT_STAFF));
        externalStaffServices.add(new TextHasValue(AuditType.Servicios.TECHNICAL_EXPERTS));
        externalStaffServices.add(new TextHasValue(AuditType.Servicios.AUDITORS_IN_TRAINING));
        return super.execute();
    }

    public List<ITextHasValue> getScope() {
        return scope;
    }

    public void setScope(List<ITextHasValue> scope) {
        this.scope = scope;
    }

    public List<ITextHasValue> getServices() {
        return services;
    }

    public void setServices(List<ITextHasValue> services) {
        this.services = services;
    }

    /**
     * @return the externalStaffServices
     */
    public List<ITextHasValue> getExternalStaffServices() {
        return externalStaffServices;
    }

    /**
     * @param externalStaffServices the externalStaffServices to set
     */
    public void setExternalStaffServices(List<ITextHasValue> externalStaffServices) {
        this.externalStaffServices = externalStaffServices;
    }

}
