package DPMS.Action;

import DPMS.DAOInterface.IUserDAO;
import Framework.Action.DefaultAction;
import Framework.Config.UserHasValue;
import java.util.List;

public class Action_Process extends DefaultAction {
    private static final String SELECCIONE = "-- Seleccione --";
    private List<UserHasValue> attendant;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {
        attendant = getBean(IUserDAO.class).getStrutsComboListByCorp(isAdmin(), getLoggedUserId(), getServiciosActivos());
        attendant.add(0, new UserHasValue(SELECCIONE, ""));
        
        return super.execute();
    }

    public List<UserHasValue> getAttendant() {
        return attendant;
    }

    public void setAttendant(List<UserHasValue> attendant) {
        this.attendant = attendant;
    }
 }
