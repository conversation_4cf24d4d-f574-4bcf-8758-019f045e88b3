package DPMS.Action;

import DPMS.Mapping.Profile;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import com.opensymphony.xwork2.ActionSupport;

/**
 *
 * <AUTHOR>
 */
public class Action_PrintableViewer extends DefaultAction {
           
    private String template = parameter("template");
    private String printables = parameter("printables");

    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        String statusAccess = super.execute();
        //valido permiso de crear
        if(!isAdmin() && !getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_CREATE)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_LINK)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_PRINT)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        return statusAccess;
    }

    /**
     * @return the template
     */
    public String getTemplate() {
        return template;
    }

    /**
     * @param template the template to set
     */
    public void setTemplate(String template) {
        this.template = template;
    }

    /**
     * @return the printables
     */
    public String getPrintables() {
        return printables;
    }

    /**
     * @param printables the printables to set
     */
    public void setPrintables(String printables) {
        this.printables = printables;
    }
}