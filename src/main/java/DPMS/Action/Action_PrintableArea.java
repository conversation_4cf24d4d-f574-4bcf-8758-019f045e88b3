package DPMS.Action;

import DPMS.DAOInterface.IAreaDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.Mapping.Area;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import com.opensymphony.xwork2.ActionSupport;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_PrintableArea extends DefaultAction {
    
    private Long businessUnitDepartmentId = null;
    private Long areaId = null;
    private List<ITextHasValue> businessUnitDepartments = null;
    private List<ITextHasValue> areas = null;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        IBusinessUnitDepartmentLoadDAO dao = getBean(IBusinessUnitDepartmentLoadDAO.class);
        IAreaDAO daoArea = getBean(IAreaDAO.class);
        String statusAccess = super.execute();
        String extraFilterArea = "";
        Boolean isFiveSManager = getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER);
        //valido permiso de crear y editar
        if(!isAdmin() && !isFiveSManager
                && !getLoggedUserServices().contains(ProfileServices.FIVES_LINK)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            areaId = (Long) Long.parseLong(getId()); 
            Area area = dao.HQLT_findById(Area.class, areaId);
            businessUnitDepartmentId = (Long) Long.parseLong(area.getDepartmentId().toString());
            extraFilterArea = "c.department.id="+businessUnitDepartmentId;
        }
        businessUnitDepartments = dao.getActivesByBusinessUnitDepartment(businessUnitDepartmentId,
                isAdmin(), isFiveSManager, getLoggedUserId(), getServiciosActivos());
        areas = daoArea.getActives(getLoggedUserId(), isAdmin(), isFiveSManager, areaId, extraFilterArea, getServiciosActivos());

        areas.add(0, new TextHasValue("-- SELECCIONE --", ""));
        businessUnitDepartments.add(0, new TextHasValue("-- SELECCIONE --", ""));
        return statusAccess;
    }

    /**
     * @return the businessUnitDepartmentId
     */
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    /**
     * @param businessUnitDepartmentId the businessUnitDepartmentId to set
     */
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    /**
     * @return the areaId
     */
    public Long getAreaId() {
        return areaId;
    }

    /**
     * @param areaId the areaId to set
     */
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    /**
     * @return the businessUnitDepartments
     */
    public List<ITextHasValue> getBusinessUnitDepartments() {
        return businessUnitDepartments;
    }

    /**
     * @param businessUnitDepartments the businessUnitDepartments to set
     */
    public void setBusinessUnitDepartments(List<ITextHasValue> businessUnitDepartments) {
        this.businessUnitDepartments = businessUnitDepartments;
    }

    /**
     * @return the areas
     */
    public List<ITextHasValue> getAreas() {
        return areas;
    }

    /**
     * @param areas the areas to set
     */
    public void setAreas(List<ITextHasValue> areas) {
        this.areas = areas;
    }

}