package DPMS.Action;

import DPMS.Mapping.Printable;
import DPMS.Mapping.PrintableType;
import DPMS.Mapping.Profile;
import mx.bnext.access.ProfileServices;
import Framework.Action.DefaultAction;
import Framework.Config.TextHasValue;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.IUntypedDAO;
import com.opensymphony.xwork2.ActionSupport;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Action_Printable extends DefaultAction {

    private Printable printable = null;
    private List<ITextHasValue> printableTypes = null;
    
    //Método que se invoca de forma automatica
    @Override
    public String execute() throws Exception {   
        IUntypedDAO dao = Utilities.getUntypedDAO();
        String statusAccess = super.execute();
        //valido permiso de crear
        if(!isAdmin() && !getLoggedUserServices().contains(ProfileServices.FIVES_MANAGER)
                && !getLoggedUserServices().contains(ProfileServices.FIVES_CREATE)) {
            return ActionSupport.NONE; //<-- no hace nada
        }
        if (getId() != null && !getId().isEmpty() && !getId().equals("-1")) {
            printable = dao.HQLT_findById(Printable.class, Long.parseLong(getId()));
            printableTypes = dao.getStrutsComboList(PrintableType.class, "description", "",printable.getPrintableType().getId(),true);
        } else {
            printableTypes = dao.getStrutsComboList(PrintableType.class, "description");
        }
        printableTypes.add(0, new TextHasValue("-- SELECCIONE --", ""));
        return statusAccess;
    }

    /**
     * @return the printable
     */
    public Printable getPrintable() {
        return printable;
    }

    /**
     * @param printable the printable to set
     */
    public void setPrintable(Printable printable) {
        this.printable = printable;
    }

    /**
     * @return the printableTypes
     */
    public List<ITextHasValue> getPrintableTypes() {
        return printableTypes;
    }

    /**
     * @param printableTypes the printableTypes to set
     */
    public void setPrintableTypes(List<ITextHasValue> printableTypes) {
        this.printableTypes = printableTypes;
    }

    
}