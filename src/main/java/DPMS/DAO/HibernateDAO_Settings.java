package DPMS.DAO;

import DPMS.DAOInterface.ISettingsDAO;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import Framework.Action.SessionViewer;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import ape.pending.listeners.OnSettingsEdited;
import ape.pending.listeners.OnSettingsEditedComplaints;
import ape.pending.listeners.OnSettingsEditedSchedule;
import ape.pending.listeners.OnSettingsEditedService;
import ape.pending.listeners.OnSettingsReadersEdited;
import bnext.dto.LocaleDTO;
import bnext.exception.InvalidCipherDecryption;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import mx.bnext.cipher.Encryptor;
import mx.bnext.licensing.util.LicenseType;
import mx.bnext.qms.configuration.util.BnextStrutsConfigurationProvider;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Cache;
import org.hibernate.SessionFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.activity.pending.ActivityPending;
import qms.audit.pending.AuditPending;
import qms.configuration.pending.UserPending;
import qms.device.pending.DevicePending;
import qms.document.pending.DocumentPending;
import qms.document.pending.RequestPending;
import qms.finding.pending.FindingPending;
import qms.form.pending.FormPending;
import qms.framework.file.BnextSpringConfiguration;
import qms.framework.util.PreferencesUtil;
import qms.planner.pending.PlannerPending;

/**
 *
 * <AUTHOR> Limas
 */
@Lazy
@Repository(value = "HibernateDAO_Settings")
@Scope(value = "singleton")
public class HibernateDAO_Settings extends GenericDAOImpl<Settings, Long> implements ISettingsDAO {
    
    private static final String SYSTEM_ID_FIELD_NAME = "systemId";
    private static final String UNATTENDED_DAYS_UPDATE = ""
        + " WITH  "
        + " break_days AS ( "
                + " SELECT u.break_date, u.user_id "
                + " FROM user_break_date u "
                + " WHERE u.status = 1 "
        + " ), "
        + " unattended_ AS ( "
                + " SELECT DATEDIFF(DAY, commitment_date, CURRENT_TIMESTAMP) - CASE WHEN max(ub.break_date) IS NULL THEN 0 ELSE count(1) END unattended_days "
                        + " ,pending_record_id "
                + " FROM ape_pending_record pending "
                + " JOIN ape_pending_type apt ON apt.pending_type_id = pending.type "
                + " LEFT JOIN break_days ub ON "
                        + " ub.break_date > pending.commitment_date "
                        + " AND ub.break_date < CURRENT_TIMESTAMP "
                        + " AND ( "
                                + " ( "
                                        + " ub.user_id = pending.OWNER "
                                        + " AND pending.STATUS = 1 "
                                + " ) OR ( "
                                        + " ub.user_id = pending.super_owner_id "
                                        + " AND pending.STATUS = 4 "
                                + " ) OR ( "
                                        + " ub.user_id = pending.super_owner_id "
                                        + " AND pending.STATUS = 3 "
                                        + " AND apt.escalation_mode = 2 "
                                + " ) OR ( "
                                        + " ub.user_id = pending.OWNER "
                                        + " AND pending.STATUS = 3 "
                                        + " AND apt.escalation_mode = 1 "
                                + " ) "
                        + " ) "
                + " GROUP BY pending_record_id, commitment_date "
        + " ), "
        + " unattended AS ( "
                + " SELECT unattended_days, pending_record_id "
                + " FROM unattended_"
                + " WHERE unattended_days > 0"
        + " ) "
        + " UPDATE ape "
        + " SET unattended_days = u.unattended_days "
        + " FROM ape_pending_record ape "
        + " JOIN unattended u ON u.pending_record_id = ape.pending_record_id "
        + " WHERE  "
                + " u.unattended_days != ape.unattended_days "
                + " AND ape.STATUS IN ( "
                        + " 1 "
                        + " ,3 "
                        + " ,4 "
                + " ) ";
    public static final Set<String> ENCRYPTED_COLUMNS = new HashSet<String>(Arrays.asList(new String[]{
        //Agregar aquí las columnas dentro de settings que serán encriptadas
    }));
    
    
    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ISettingsDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(ISettingsDAO.class);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnSettingsEdited
    public GenericSaveHandle save(Settings ent, LoggedUser loggedUser) {
        GenericSaveHandle gsh = new GenericSaveHandle();

        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Catalog.CRUD_Settings @ Save:" + Utilities.getSerializedObj(ent));
        }
        ent.setLocalizeEntities(HQL_findSimpleInteger(""
                + " SELECT c.localizeEntities"
                + " FROM " + Settings.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", ent.getId()));
        
        Integer currentReaders = ent.getReaders();
        Integer oldReaders = getReaders(ent.getId());
        Integer currentComplaintManagerWhoAccept =  ent.getComplaintManagerWhoAccept();
        Integer oldComplaintManagerWhoAccept = getComplaintManagerWhoAccept(ent.getId());
        Long currentUploadFileMaxSizeBytes =  ent.getUploadFileMaxSizeBytes();
        Long oldUploadFileMaxSizeBytes = getUploadFileMaxSizeBytes(ent.getId());
        String currentWelcomeMessage =  ent.getWelcomeMessage();
        String oldWelcomeMesString = getWelcomeMEssage(ent.getId());
        ent = makePersistent(ent, loggedUser.getId());
        if (!Objects.equals(currentReaders, oldReaders)) {
            getAspectJAutoProxy().changeSettingsReaders(ent, currentReaders, oldReaders, loggedUser);
        }
        if (!Objects.equals(currentComplaintManagerWhoAccept, oldComplaintManagerWhoAccept)) {
            getAspectJAutoProxy().changeComplaintsResponsible(ent, currentComplaintManagerWhoAccept, oldComplaintManagerWhoAccept, loggedUser);
        }
        if (!Objects.equals(currentWelcomeMessage, oldWelcomeMesString)) {
            getAspectJAutoProxy().changeWelcomeMessage();
        }
        if (ent != null) {   
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage(SessionViewer.EDIT_SUCCESS);
        } else {
            gsh.setOperationEstatus(0);
        }
        Utilities.resetSettings(getServletContext());
        isoblock.common.Properties.reloadXML(); 
        if (currentUploadFileMaxSizeBytes != null && Objects.equals(currentUploadFileMaxSizeBytes, oldUploadFileMaxSizeBytes)) {
            BnextStrutsConfigurationProvider.reloadConfig();
            BnextSpringConfiguration.reloadSpringMultipartConfig();
        }
        return gsh;
    }
    
    private Integer getReaders(Long id) {
        return HQL_findSimpleInteger(""
            + " SELECT c.readers"
            + " FROM " + Settings.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id", id);
    }

    private Integer getComplaintManagerWhoAccept(Long id) {
        return HQL_findSimpleInteger(""
            + " SELECT c.complaintManagerWhoAccept"
            + " FROM " + Settings.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id", id);
    }
    
    private Long getUploadFileMaxSizeBytes(Long id) {
        return HQL_findLong(""
            + " SELECT c.uploadFileMaxSizeBytes"
            + " FROM " + Settings.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id", id);
    }

    private String getWelcomeMEssage(Long id) {
        return HQL_findSimpleString(""
            + " SELECT c.welcomeMEssage"
            + " FROM " + Settings.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id", id);
    }


    @Override
    @OnSettingsReadersEdited
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Settings changeSettingsReaders(Settings ent, Integer currentValue, Integer oldValue, LoggedUser loggedUser) {
        return ent;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Object> getValueSetting(Map<String, Object> ent){
        Map<String, Object> action = new HashMap<>(ent.size());
        for (Map.Entry<String, Object> entrySet : ent.entrySet()) {
            String key = entrySet.getKey();
            if (!key.isEmpty()) {
                if (key.endsWith("@P")) {
                    key = key.substring(0, key.length() - 2);
                }
                Object obj = HQL_findSimpleObject(""
                        + " SELECT c." + key + " "
                        + " FROM " + Settings.class.getCanonicalName() + " c "
                );

                Object value = null;
                if (ENCRYPTED_COLUMNS.contains(key)) {
                    if (obj == null || obj.toString().isEmpty()) {
                        value = null;
                    } else {
                        try {
                            value = PreferencesUtil.getDencryptedValue(key, obj.toString());
                        } catch (InvalidCipherDecryption ex) {
                            getLogger().error("Invalid decripted data for '{}', please reset its value", key);
                        }
                    }
                } else if (key.contains(SYSTEM_ID_FIELD_NAME)) {
                    for (final LicenseType p : LicenseType.values()) {
                        if (obj.toString().contains(p.toString())) {
                            value = obj.toString().replace(p.toString(), "");
                            break;
                        }
                    }
                    if (value == null) {
                        value = obj;
                    }
                } else {
                    value = obj;
                }
                action.put(key, value);
            }
        }
        return action;
    }
    
    private Integer saveEncryptedValue(final String key, final Object value) throws InvalidCipherDecryption {
        if (SYSTEM_ID_FIELD_NAME.equals(key)) {
            throw new RuntimeException("Not allowed to edit field systemId in Settings entity");
        }
        if (value == null || value.toString().isEmpty()) {
            return HQL_updateByQuery(""
                    + " UPDATE " + Settings.class.getCanonicalName()
                    + " SET " + key + " = null"
            );
        }
        final String encryptedValue = PreferencesUtil.getEncryptedValue(key, value.toString());
        return HQL_updateByQuery(""
                + " UPDATE " + Settings.class.getCanonicalName()
                + " SET " + key + " = :encrypted",
                "encrypted",
                encryptedValue
        );
    }
    
    private void validHashValueSetting(Map<String, Object> ent) {
        String oidcClientSecret = "hashedSmtpPsw";
        String hashedSmtpPsw = "oidcClientSecret";
        if (ent.containsKey(hashedSmtpPsw)) {
            String currentSmtpPsw = ent.get(hashedSmtpPsw).toString();
            String token = Utilities.getSettings().getSystemId() + "_" + Utilities.getSettings().getRcKey();
            String hashedPsw = Encryptor.encrypt(currentSmtpPsw, token);
            ent.put(hashedSmtpPsw, hashedPsw);
            HQL_updateByQuery(""
                + " UPDATE " + Settings.class.getCanonicalName()
                + " SET smtpPsw = '' "
            );
        } else if (ent.containsKey(oidcClientSecret)) {
            String plainSecret = ent.get(oidcClientSecret).toString();
            String token = Utilities.getSettings().getSystemId() + "_" + Utilities.getSettings().getRcKey();
            String hashedSecret = Encryptor.encrypt(plainSecret, token);
            ent.put(oidcClientSecret, hashedSecret);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateSettingsLocale(LocaleDTO systemLocale) {
        final String lang = StringUtils.truncate(systemLocale.getLang(), 5);
        final String locale = StringUtils.truncate(systemLocale.getLocale(), 5);
        update("lang", lang);
        update("locale", locale);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer setValueSetting(Map<String, Object> ent, LoggedUser loggedUser){
        Integer result = 0;
        validHashValueSetting(ent);
        for (Map.Entry<String, Object> entrySet : ent.entrySet()) {
            String key = entrySet.getKey();
            Object value = entrySet.getValue();
            if (!key.isEmpty()) {
                result += update(key, value);
            }
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer update(String key, Object value) {
        try {
            if (key.endsWith("@P")) {
                final String actualKey = key.substring(0, key.length() - 2);
                if (ENCRYPTED_COLUMNS.contains(actualKey)) {
                    return saveEncryptedValue(actualKey, value);
                } else {
                    getLogger().error("Trying to save {} not encrypted column as encrypted", key);
                }
            } else {
                if (SYSTEM_ID_FIELD_NAME.equals(key)) {
                    throw new RuntimeException("Not allowed to edit field systemId in Settings entity");
                }
                if (value == null) {
                    return HQL_updateByQuery(""
                            + " UPDATE " + Settings.class.getCanonicalName()
                            + " SET " + key + " = null ", "p",
                            value
                    );
                } else {
                    return HQL_updateByQuery(""
                            + " UPDATE " + Settings.class.getCanonicalName()
                            + " SET " + key + " = :p ", "p",
                            value
                    );
                }
            }
        } catch (Exception e) {
            getLogger().error("Failed to save key '" + key + "' with value '" + value + "'.", e);
        }
        return 0;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void refreshAllPendings(LoggedUser loggedUser) {
        new FindingPending(this).normalize(loggedUser);
        new ActivityPending(this).normalize(loggedUser);
        new PlannerPending(this).normalize(loggedUser);
        new RequestPending(this).normalize(loggedUser);
        new DocumentPending(this).normalize(loggedUser);
        new UserPending(this).normalize(loggedUser);
        new AuditPending(this).normalize(loggedUser);
        new DevicePending(this).normalize(loggedUser);
        new FormPending(this).normalize(loggedUser);
        SQL_updateByQuery(
                UNATTENDED_DAYS_UPDATE, 
                Utilities.EMPTY_MAP, 0,
                Arrays.asList("user_break_date", "ape_pending_record", "ape_pending_type")
        );
    }
    
    @Override
    @OnSettingsEditedComplaints
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Settings changeComplaintsResponsible(Settings ent, Integer currentValue, Integer oldValue, LoggedUser loggedUser) {
        return ent;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void changeWelcomeMessage() {
        HQL_updateByQuery("UPDATE " + User.class.getCanonicalName() + " SET showWelcomeDialog = 1 ");
    }

    @Override
    @OnSettingsEditedService
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Settings changeServiceToApprove(final Settings ent, final Integer currentValue, final Integer oldValue, final LoggedUser loggedUser) {
        return ent;
    }

    @Override
    @OnSettingsEditedSchedule
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Settings changeSchedulingToApprove(final Settings ent, final Integer currentValue, final Integer oldValue, final LoggedUser loggedUser) {
        return ent;
    }
    
    
    @Override
    @OnSettingsEditedComplaints
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void clearHibernateCache() {
        try {
            SessionFactory sessionFactory = getSession().getSessionFactory();
            getSession().clear();
            Cache cache = sessionFactory.getCache();
            if (cache != null) {
                cache.evictEntityData();
                cache.evictCollectionData();
                cache.evictCollectionData();
                cache.evictDefaultQueryRegion();
                cache.evictNaturalIdData();
                cache.evictAllRegions();
                cache.evictQueryRegions();
                cache.evictAll();
            }
        } catch(final Exception e) {
            getLogger().error("Failed to clear hibernate cache", e);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateConfigHibernateCache() {
        final Settings settings = Utilities.getSettings();
        final Boolean useSecondLevelCache = settings != null 
                && settings.getUseSecondLevelCache() != null && settings.getUseSecondLevelCache() == 1;
        final SessionFactory sessionFactory = getSession().getSessionFactory();
        sessionFactory.getProperties().put("hibernate.cache.use_query_cache", useSecondLevelCache);
        sessionFactory.getProperties().put("hibernate.cache.use_second_level_cache", useSecondLevelCache);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void changeLangLoggedUser(LoggedUser loggedUser){
        HQL_updateByQuery("UPDATE " + User.class.getCanonicalName() + " SET lang = '', locale = '' WHERE id = " + loggedUser.getId());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateCalendarTable(Integer years, Date lastDateCalendar){
        Calendar endDateTemp = Calendar.getInstance();
        endDateTemp.setTime(lastDateCalendar);
        endDateTemp.add(Calendar.YEAR, years);
        Date endDate = new Date(endDateTemp.getTimeInMillis());
        
        String startDate = Utilities.formatDate(lastDateCalendar);
        String endDateS = Utilities.formatDate(endDate);
        
        String query = ""
                + " WITH Dates(IDDia) AS "
                + " ( "
                    + " SELECT CONVERT(DATE, '" + startDate + "', 23) as IDDia" 
                    + " UNION ALL "
                    + " SELECT CAST(DATEADD(day, 1, IDDia) as DATE) as IDDia "    
                    + " FROM Dates "    
                    + " WHERE CAST(DATEADD(day, 1, IDDia) as DATE) <= CONVERT(DATE, '" + endDateS + "', 23)"    
                + " )"    
                + " INSERT dbo.Calendar_N WITH (TABLOCKX) "    
                + " SELECT"
                    + " ID	      = CONVERT(INT, CONVERT(VARCHAR(8), IDDia, 112)),"
                    + " [Date]        = IDDia,"
                    + " [Day]         = CONVERT(TINYINT, DAY(IDDia)),"  
                    + " [Weekday]     = CONVERT(TINYINT, DATEPART(weekday, IDDia)),"  
                    + " [WeekDayName] = CONVERT(VARCHAR(10), DATENAME(WEEKDAY, IDDia))," 
                    + " [IsWeekend]   = CONVERT(BIT, CASE WHEN DATEPART(weekday, IDDia) = 1 THEN 1 ELSE 0 END),"  
                    + " [IsHoliday]   = CONVERT(BIT, CASE WHEN MONTH(IDDia) = 1 AND DAY(IDDia) = 1 THEN 1 WHEN MONTH(IDDia) = 12 AND DAY(IDDia) = 25 THEN 1 ELSE 0 END),"  
                    + " HolidayText   = CONVERT(VARCHAR(64), "  
                    + " CASE WHEN MONTH(IDDia) = 1 AND DAY(IDDia) = 1 THEN 'Año Nuevo' WHEN MONTH(IDDia) = 12 AND DAY(IDDia) = 25 THEN 'Navidad' ELSE NULL END),"  
                    + " [DayOfYear]   = CONVERT(SMALLINT, DATEPART(DAYOFYEAR, IDDia)),"  
                    + " WeekOfMonth   = CONVERT(TINYINT, DATEPART(day, DATEDIFF(day, 0, IDDia)/7 * 7)/7 + 1),"  
                    + " WeekOfYear    = CONVERT(TINYINT, DATEPART(week, IDDia))," 
                    + " ISOWeekOfYear = CONVERT(TINYINT, DATEPART(ISO_WEEK, IDDia)),"  
                    + " [Month]       = CONVERT(TINYINT, DATEPART(month, IDDia)),"  
                    + " [MonthName]   = CONVERT(VARCHAR(10), DATENAME(MONTH, IDDia)),"  
                    + " [Quarter]     = CONVERT(TINYINT, DATEPART(QUARTER, IDDia)),"  
                    + " QuarterName   = CONVERT(VARCHAR(6), CASE DATEPART(QUARTER, IDDia) WHEN 1 THEN 'First' WHEN 2 THEN 'Second' WHEN 3 THEN 'Third' WHEN 4 THEN 'Fourth' END),"  
                    + " [Year]        = DATEPART(YEAR, IDDia),"
                    + " MMYYYY        = CONVERT(CHAR(6), LEFT(CONVERT(VARCHAR(10), IDDia, 101), 2)    + LEFT(CONVERT(VARCHAR(8), IDDia, 112), 4)),"  
                    + " MonthYear     = CONVERT(CHAR(7), LEFT(DATENAME(MONTH, IDDia), 3) + LEFT(CONVERT(VARCHAR(8), IDDia, 112), 4))"  
                + " FROM Dates"  
                + " OPTION (MAXRECURSION  0)";
        
        SQL_updateByQuery(query, Utilities.EMPTY_MAP, 0, Arrays.asList("Calendar_N"));
    }
}
