package DPMS.DAO;

import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IDocumentReaderDAO;
import DPMS.DAOInterface.IDocumentTypeDAO;
import DPMS.DAOInterface.IFilesDAO;
import DPMS.DAOInterface.INodeAccessDAO;
import DPMS.DAOInterface.IOrganizationalUnitDAO;
import DPMS.DAOInterface.IReceiptAcknowledgmentDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.DAOInterface.IUserRefDAO;
import DPMS.Document.Util.LockRequestTask;
import DPMS.Mapping.AutorizationPool;
import DPMS.Mapping.AutorizationPoolDetailComment;
import DPMS.Mapping.AutorizationPoolDetailRead;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.BusinessUnitLite;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentSimple;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.FilesLite;
import DPMS.Mapping.Node;
import DPMS.Mapping.NodeSimple;
import DPMS.Mapping.OrganizationalUnitLite;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.OutstandingSurveysAttendantLoad;
import DPMS.Mapping.PhysicalReader;
import DPMS.Mapping.RelatedDocument;
import DPMS.Mapping.RelatedDocumentPK;
import DPMS.Mapping.Request;
import DPMS.Mapping.Settings;
import DPMS.Mapping.SimplePuesto;
import DPMS.Mapping.User;
import DPMS.Mapping.Verification;
import DPMS.Mapping.VerificationLog;
import DPMS.Mapping.VerificationLogPK;
import DPMS.Mapping.WorkflowPool;
import Framework.Action.SessionViewer;
import Framework.Config.Language;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.SaveHandle;
import ape.pending.core.IPendingQueries;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Streams;
import isoblock.surveys.dao.hibernate.OutstandingQuestion;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingSurveysRef;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import isoblock.surveys.dao.hibernate.SurveySimple;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Timer;
import java.util.UUID;
import javax.annotation.Nonnull;
import javax.persistence.Table;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.security.ISecurityUser;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.interfaces.IPlainUser;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.core.DynamicTableHelper;
import qms.custom.dao.IPrintingFormatDAO;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.dto.DynamicFieldsDTO;
import qms.document.dao.IDocumentHistoryModifyedDAO;
import qms.document.dao.IRequestHistoryDAO;
import qms.document.dto.CodeValidatorDTO;
import qms.document.dto.CommentHistoryDTO;
import qms.document.dto.FillFormDTO;
import qms.document.dto.JoinableRequestDTO;
import qms.document.dto.PreValidateRequestDTO;
import qms.document.dto.RequestPlainDTO;
import qms.document.dto.RequestSaveHandle;
import qms.document.entity.DocumentTraceability;
import qms.document.entity.DocumentTraceabilityUser;
import qms.document.entity.FillRequest;
import qms.document.entity.RequestRef;
import qms.document.interfaces.IJoinableRequest;
import qms.document.interfaces.IPlainDocument;
import qms.document.interfaces.IPlainDocumentType;
import qms.document.interfaces.IPlainNode;
import qms.document.interfaces.IPlainRequest;
import qms.document.listeners.OnCreatedNextFill;
import qms.document.listeners.OnDocumentCancelled;
import qms.document.listeners.OnDocumentModified;
import qms.document.listeners.OnDocumentReapproved;
import qms.document.listeners.OnEditDetailsDocument;
import qms.document.listeners.OnFillFormRejected;
import qms.document.listeners.OnFillFormRequested;
import qms.document.listeners.OnFormRequested;
import qms.document.listeners.OnNewDocumentPublished;
import qms.document.listeners.OnNextAuthorizeCancellation;
import qms.document.listeners.OnNextAuthorizeModification;
import qms.document.listeners.OnNextAuthorizeNewDocument;
import qms.document.listeners.OnNextAuthorizeReapproval;
import qms.document.listeners.OnRejectAuthorization;
import qms.document.listeners.OnRejectFilledSection;
import qms.document.listeners.OnRejectedVerification;
import qms.document.listeners.OnRequestDocumentApproval;
import qms.document.listeners.OnRequestDocumentCancellation;
import qms.document.listeners.OnRequestDocumentModification;
import qms.document.listeners.OnRequestNewDocument;
import qms.document.listeners.OnRequestNewManager;
import qms.document.listeners.OnRequestVerified;
import qms.document.listeners.OnRequestVerifiedPublished;
import qms.document.listeners.OnStartedFillForm;
import qms.document.logic.DocumentCodeValidator;
import qms.document.logic.FormHelper;
import qms.document.pending.imp.ToAuthorizeRequest;
import qms.document.pending.imp.ToVerifyRequest;
import qms.document.util.DocumentPublicationUtils;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.AuthorizeSectionDTO;
import qms.form.dto.AutorizationDetailReadDTO;
import qms.form.dto.FieldCommentDTO;
import qms.form.dto.FillInfoDTO;
import qms.form.dto.FormRequestorDTO;
import qms.form.dto.ReadableFieldDTO;
import qms.form.entity.OutstandingSurveysAnybodyRead;
import qms.form.util.IOutstandingSurveysLoadAnswers;
import qms.form.util.SurveyUtil;
import qms.form.util.SurveyUtilCache;
import qms.framework.daemon.BnextDaemonUtil;
import qms.framework.dao.ISlimReportsDAO;
import qms.framework.dto.DocumentDataIndex;
import qms.framework.entity.Owner;
import qms.framework.interfaces.IPlainBusinessUnit;
import qms.framework.interfaces.IPlainFile;
import qms.framework.interfaces.IPlainOrganizationalUnit;
import qms.framework.interfaces.IPlainSearchBusinessUnitDepartment;
import qms.framework.util.CacheRegion;
import qms.framework.util.PdfCleanerExecutor;
import qms.framework.util.SurveyFillEntity;
import qms.survey.dao.ISurveyFieldHistoryDAO;
import qms.survey.dto.SurveyFieldObjectDTO;
import qms.survey.interfaces.IPlainSurvey;
import qms.util.BindUtil;
import qms.util.DAOException;
import qms.util.FormUtil;
import qms.util.QMSException;
import qms.util.UserUtil;
import qms.util.interfaces.IGridFilter;
import qms.workflow.bean.WorkflowBaseDAO;
import qms.workflow.util.IAutorizationPoolDetails;
import qms.workflow.util.IWorkflowBaseDAO;
import qms.workflow.util.IWorkflowDAO;
import qms.workflow.util.IWorkflowRequest;
import qms.workflow.util.WorkflowAuthRole;
import qms.workflow.util.WorkflowRequestStatus;
import qms.workflow.util.WorkflowSupported;

@Lazy
@Repository(value = "HibernateDAO_Request")
@Scope(value = "singleton")
@Language(module = "DPMS.DAO.HibernateDAO_Request")
public class HibernateDAO_Request extends WorkflowBaseDAO<Request, Long> implements IRequestDAO {

    public static final String
        HQL_FILL_FORMS_DTO = " "
            + " SELECT "
                    + " new " + FillFormDTO.class.getCanonicalName() + "("
                        + " request.code AS code,"
                        + " request.creationDate AS creationDate,"
                        + " request.documentCode AS documentCode,"
                        + " author AS author,"
                        + " fill AS fillOutDocument,"
                        + " dep AS department,"
                        + " documentType AS documentType,"
                        + " request.reazon AS reazon,"
                        + " request.outstandingSurveysId AS outstandingSurveysId,"
                        + " request.id AS id,"
                        + " request.status AS status,"
                        + " request.surveyId AS surveyId"
                    + ") " 
                + " FROM " 
                    + Request.class.getCanonicalName() + " request "
                + " LEFT JOIN request.autorizationPool pool"
                + " LEFT JOIN request.documentType documentType"
                + " LEFT JOIN request.author author"
                + " LEFT JOIN request.fillOutDocument fill "
                + " LEFT JOIN request.department dep "
            ;
    public static final String 
        HQL_FILL_FORMS_DETAIL_DTO = ""
            + " SELECT "
                    + " new " + FillFormDTO.class.getCanonicalName() + "("
                        + " request.code AS code,"
                        + " request.creationDate AS creationDate,"
                        + " request.documentCode AS documentCode,"
                        + " author AS author,"
                        + " fill AS fillOutDocument,"
                        + " dep AS department,"
                        + " documentType AS documentType,"
                        + " request.reazon AS reazon,"
                        + " request.outstandingSurveysId AS outstandingSurveysId,"
                        + " request.id AS id,"
                        + " detail.indice AS indice, "
                        + " detail.id AS id, "
                        + " detail.currentRecurrence AS currentRecurrence, "
                        + " request.status AS status,"
                        + " request.surveyId AS surveyId"
                    + ")" 
                + " FROM " 
                    + Request.class.getCanonicalName() + " request "
                + " LEFT JOIN request.autorizationPool pool"
                + " LEFT JOIN request.documentType documentType"
                + " LEFT JOIN request.author author"
                + " LEFT JOIN pool.autorizationPoolDetailsList detail"
                + " LEFT JOIN request.fillOutDocument fill "
                + " LEFT JOIN request.department dep "
            ;
    private static final String VALID_ENTITIES_FILTER = ""
            //documentos que están en mi planta por planta del documento
            + " exists ("
                + " SELECT p2.une.id "
                + " FROM DPMS.Mapping.User u1 "
                + " JOIN u1.puestos p2 "
                + " JOIN p2.perfil perf "
                + " WHERE c.scope = " + Request.SCOPE.BUSINESS_UNIT.getValue() + " "
                + " AND p2.une.id = c.businessUnit.id"
                + " AND  u1.id = :userId "
                + " AND 1 IN (:services)"
            + ") "
            + " OR exists ("
            //documentos que están en mi planta por el departamento del documento
            + " SELECT une.id "
            + " FROM DPMS.Mapping.User u1 "
            + " JOIN u1.puestos p2 "
            + " JOIN p2.une une "
            + " WHERE c.scope = " + Request.SCOPE.DEPARTMENT.getValue() + " "
            + " AND p2.une.id = c.department.businessUnitId"
            + " AND  u1.id = :userId"
            + " ) "
            //donde soy autor
            + " OR c.author.id = :userId "
            //donde soy encargado de documentos por planta (business_unit)
            + " OR exists ("
            + "     SELECT 1 "
            + "     FROM DPMS.Mapping.BusinessUnit bu "
            + "     WHERE bu.documentManagerId = :userId AND (("
            + "         c.scope = " + Request.SCOPE.BUSINESS_UNIT.getValue() + " "
            + "             AND bu.id = c.businessUnit.id"
            + "         ) OR ("
            + "         c.scope = " + Request.SCOPE.DEPARTMENT.getValue() + " "
            + "             AND bu.id = c.department.businessUnitId"
            + "         )"
            + "     )"
            + " ) "
            //donde soy encargado de documentos por departamento (business_unit_department)
            + " OR exists ("
            + "     SELECT 1 "
            + "     FROM DPMS.Mapping.BusinessUnitDepartmentLite bu "
            + "     WHERE bu.documentManagerId = :userId AND ("
            + "         (c.scope = " + Request.SCOPE.BUSINESS_UNIT.getValue() + " "
            + "             AND bu.businessUnitId = c.businessUnit.id)"
            + "         OR"
            + "         (c.scope = " + Request.SCOPE.DEPARTMENT.getValue() + " "
            + "             AND bu.businessUnitId = c.department.businessUnitId)"
            + "     )"
            + " )";

    public static final String REQUEST_DRAFTS_QUERY = ""
            + " SELECT new map("
                + " c.surveyId AS surveyId "
                + ",max(c.id) AS id "
                + ",max(r.creationDate) AS creationDate "
                + ",max(r.id) AS requestId "
            + " )"
            + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
            + " JOIN c.request r "
            + " WHERE"
                + " c.deleted = 0"
                + " AND c.creatorUserId = :creatorUserId" 
                + " AND c.surveyId = :surveysId"
                + " AND r.status = " + Request.STATUS.STAND_BY.getValue()
                + " AND c.estatus IN (" 
                    + OutstandingSurveys.STATUS.STAND_BY.getValue()
                    + "," + OutstandingSurveys.STATUS.IN_PROGRESS.getValue()
                    + "," + OutstandingSurveys.STATUS.IN_PROGRESS_FILLED_PARCIALLY.getValue()
                    + "," + OutstandingSurveys.STATUS.IN_PROGRESS_FILL_LATER.getValue()
                + " )"
            + " GROUP BY "
                + " c.surveyId"
        ;
    
    private static final List<Integer> TO_ACCEPT_STATUSES = Arrays.asList(new Integer[] {
        AutorizationPoolDetails.ACCEPTED, AutorizationPoolDetails.NOT_ALTERED
    });
    
    public static final String LOCK_REQUEST = ""
            + " UPDATE "
            + Request.class.getCanonicalName() + "  c "
            + " SET  isBusy = :isBusy, "
            + " timeToFreeMinutes = :timeToFreeMinutes, "
            + " blockedBy = :blockedBy, "
            + " blockedByName = :blockedByName "
            + " WHERE c.id = :id";
    
    public HibernateDAO_Request() {
        getLogger(LOGGER.STARTUP).debug("HibernateDAO_Request bean has been successfully created.");
    }   
    
    
    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IRequestDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IRequestDAO.class);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        SessionViewer session = new SessionViewer();
        Boolean hasUnrestrictedDocumentAccess = session.getLoggedUserServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
        if (isAdmin || hasUnrestrictedDocumentAccess) {
            return;
        }
        filter.getCriteria().put("<filtered-entity>",
                HibernateDAO_Request.VALID_ENTITIES_FILTER
                .replaceAll(":userId", userId)
                .replaceAll(":services", ProfileServices.getCodedServices("perf", servicio)));
    }

    /**
     * Función que bloquea una solicitud para que otro usuario no pueda autorizar.
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean setToBusy(Request p, boolean fill, String nombre, Long userId) {
        boolean alreadyBlocked;
        Integer isBusy = p.getIsBusy();
        if (fill) {
            isBusy = this.HQL_findSimpleInteger(""
                    + " SELECT "
                    + " c.isBusy"
                    + " FROM " + Request.class.getCanonicalName() + " c"
                    + " WHERE c.id = :id ", "id", p.getId());
        }
        alreadyBlocked = isBusy.equals(StandardEntity.ACTIVE_STATUS);
        if (alreadyBlocked) {
            return alreadyBlocked;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("id", p.getId());
        params.put("isBusy", StandardEntity.STATUS.ACTIVE.getValue());
        params.put("blockedByName", nombre);
        params.put("blockedBy", userId);
        params.put("timeToFreeMinutes", 5); //<--- se liberará el pendiente en 5 minutos ToDo parametrizar los minutos
        
        getLogger().trace("<<>> -- Se colocan 5 minutos! - " + HQL_updateByQuery(LOCK_REQUEST, params));
        return alreadyBlocked;
    }

    /**
     * Función que libera la solicitud bloqueada
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean setToNotBusy(Long id) {
        Request p = new Request(id);
        Map<String, Object> u = new HashMap<>();
        u.put("isBusy", StandardEntity.INACTIVE_STATUS);
        u.put("blockedBy", 0L);
        u.put("timeToFreeMinutes", 0); //<--- se liberará el pendiente en 5 minutos
        int t = p.update(u, this);
        getLogger().trace("<<>> -- Se libera el request " + t);
        return t > 0;
    }

    private boolean isRepeatedScopeApplication(PreValidateRequestDTO result, StringBuilder msg) {
        final boolean repeatedCodeDocument = result.isRepeatedCodeDocument();
        final boolean repeatedCodeRequest = result.isRepeatedCodeRequest();
        final boolean repeatedNameDocument = result.isRepeatedNameDocument();
        final boolean repeatedNameRequest = result.isRepeatedNameRequest();
        if (repeatedCodeDocument || repeatedCodeRequest || repeatedNameDocument || repeatedNameRequest) {
            if (repeatedCodeDocument) {
                msg.append(", <br/>").append(getTag("repeatedDataCode1"));
            } else if (repeatedCodeRequest) {
                msg.append(", <br/>").append(getTag("repeatedDataCode2"));
            }
            if (repeatedNameDocument) {
                msg.append((repeatedCodeDocument || repeatedCodeRequest ? ", " : ",<br/>")).append(
                    getTag("repeatedDataTitle1")
                );
            } else if (repeatedNameRequest) {
                msg.append(repeatedCodeDocument || repeatedCodeRequest ? "," : ",<br/>").append(
                    getTag("repeatedDataTitle2")
                );
            }
            return true;
        }
        return false;
    }
    
    private boolean isRepeatedScopeBusinessUnit(PreValidateRequestDTO result, StringBuilder msg) {
        final boolean repeatedCodeDocument = result.isRepeatedCodeDocument();
        final boolean repeatedCodeRequest = result.isRepeatedCodeRequest();
        if (repeatedCodeDocument || repeatedCodeRequest) {
            if (repeatedCodeDocument) {
                msg.append(", <br/>").append(getTag("repeatedDataCode1"));
            } else if (repeatedCodeRequest) {
                msg.append(", <br/>").append(getTag("repeatedDataCode2"));
            }
            return true;
        }
        return false;
    }
    
    /**
     * Función que determina si la clave o el código están repetidos
     *
     */
    private Boolean isRepeatedCodeOrName(Request sol, ILoggedUser loggedUser, GenericSaveHandle gsh) {
        StringBuilder errorMessage = new StringBuilder(200).append(getTag("repeatedData"));
        PreValidateRequestDTO pre = preValidateRequest(sol, loggedUser);
        boolean isRepeated = false;
        if (pre.isRepeated()) {
            
            switch (Settings.CODE_SCOPE.fromValue(Utilities.getSettings().getDocumentCodeScope())) {
                case LEVEL_APPLICATION:
                    isRepeated = isRepeatedScopeApplication(pre, errorMessage);
                    break;
                case LEVEL_BUSINESS_UNIT:
                    isRepeated = isRepeatedScopeBusinessUnit(pre, errorMessage);
                    break;
            }
            if (isRepeated) {
                gsh.setErrorMessage(errorMessage.toString());
                gsh.setOperationEstatus(0);
            }
        }
        return isRepeated;
    }
    
    /**
     * Función que guarda la solicitud
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public RequestSaveHandle save(
            final Request request,
            final FilesLite file,
            final Integer tipo,
            final ILoggedUser loggedUser,
            final boolean isForm,
            final boolean ajax
    ) throws IOException, QMSException {
        RequestSaveHandle gsh = new RequestSaveHandle();
        boolean nuevo = (request.getId() == null || request.getId() == -1L);
        updateRequestStatus(request, isForm);
        if (nuevo) {
            if(request.getCreationDate() == null ){
                request.setCreationDate(new Date());
            }
            if(request.getAuthor() == null ){
                request.setAuthor(loggedUser.getUserRefInstance());
            }
            initializeVersion(request); 
            Request.TYPE type = Request.TYPE.getType(tipo);
            if (type == null) {
                gsh.setErrorMessage("Invalido tipo de solicitud");
                gsh.setOperationEstatus(0);
                return gsh;
            }
            request.setType(tipo);
            if (WorkflowRequestStatus.VERIFING.getValue().equals(request.getStatus())) {
                gsh = toVerifyDocument(type, request, file, loggedUser, ajax);
            } else if (WorkflowRequestStatus.CLOSED.getValue().equals(request.getStatus())) {
                gsh = saveNewDocument(request, file, loggedUser, true);
            } else {
                gsh = saveRequest(request, file, loggedUser, ajax, true);
            }
        } else {
            if (request.getDocumentCode() == null || request.getDocumentCode().equals("")) {
                String currentDocumentCode = HQL_findSimpleString(""
                        + "SELECT "
                            + " r.documentCode "
                        + "FROM " + Request.class.getCanonicalName() + " r "
                        + "WHERE r.id = :id ", ImmutableMap.of("id", request.getId()));
                request.setDocumentCode(currentDocumentCode);
            }
            gsh = saveRequest(request, file, loggedUser, ajax, false);
        }
        gsh.setSavedId(request.getId().toString());
        if (gsh.getOperationEstatus() == 1) {
            if (nuevo) {
                gsh.setSuccessMessage(SessionViewer.ADD_SUCCESS);
            } else {
                gsh.setSuccessMessage(SessionViewer.EDIT_SUCCESS);
            }
            if (Request.STATUS_CLOSED == request.getStatus()) {
                final IWorkflowRequest savedRequest = onWorkflowEnd(request.getId(), loggedUser, nuevo);
                gsh.setRequest((Request) savedRequest);
            }
        }
        return gsh;
    }

    private RequestSaveHandle toVerifyDocument(
            Request.TYPE type,
            Request sol, 
            FilesLite file, 
            ILoggedUser loggedUser, 
            boolean ajax
    ) throws QMSException {
        switch (type) {
            case NEW:
                return getAspectJAutoProxy().requestNewDocument(sol, file, loggedUser, true);
            case MODIFY:
            case EDIT_DETAILS:
                return getAspectJAutoProxy().requestDocumentModification(sol, file, loggedUser, true);
            case APROVE:
                return getAspectJAutoProxy().requestDocumentApproval(sol, file, loggedUser, true);
            case CANCEL:
                return getAspectJAutoProxy().requestDocumentCancellation(sol, file, loggedUser, true);
            case FILL:
                return saveRequest(sol, file, loggedUser, ajax, true);
        }
        return failedSaveRequest();
    }

    protected RequestSaveHandle failedSaveRequest() {
        RequestSaveHandle gsh = new RequestSaveHandle();
        gsh.setOperationEstatus(0);
        throw new DAOException(gsh);
    }

    private void updateRequestStatus(final Request sol, final boolean isForm) {
        boolean isUncontrolled = isUncontrolledDocument(sol);
        if (isUncontrolled && !Request.CANCEL.equals(sol.getType())) {
            sol.setStatus(WorkflowRequestStatus.CLOSED.getValue());
        } else if (isForm && !(Request.APROVE.equals(sol.getType()) || Request.CANCEL.equals(sol.getType()) || Request.EDIT_DETAILS.equals(sol.getType())))  {
            sol.setStatus(Request.STATUS.STAND_BY.getValue());
        } else {
            sol.setStatus(WorkflowRequestStatus.VERIFING.getValue());
        }
    }

    private boolean isUncontrolledDocument(Request sol) {
        final DocumentType docType = sol.getDocumentType();
        if (docType == null || docType.getId() == null || docType.getId() == -1L) {
            throw new RuntimeException("Document type of requestId: '" + sol.getId() + "' cannot be validated");
        }
        final String documentControlledType;
        if (docType.getDocumentControlledType() != null && !docType.getDocumentControlledType().isEmpty()) {
            documentControlledType = docType.getDocumentControlledType();
        } else {
            documentControlledType = getDocumentControlledType(docType.getId());
            sol.getDocumentType().setDocumentControlledType(documentControlledType);
        }
        return documentControlledType.equals(DocumentType.documentControlledType.UNCONTROLLED.toString());
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getDocumentControlledType(Long docTypeId) {
        return HQL_findSimpleString(" "
            + " SELECT c.documentControlledType "
            + " FROM " + DocumentType.class.getCanonicalName() + " c "
            + " WHERE c.id = :id",
            ImmutableMap.of("id", docTypeId)
        );
    }

    /**
     * Función que guarda la solicitud
     */
    private RequestSaveHandle saveRequest(Request sol, FilesLite file, ILoggedUser loggedUser, boolean ajax, boolean nuevo) {
        RequestSaveHandle gsh = new RequestSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.DAO.HibernateDAO_Request @ Save:" + Utilities.getSerializedObj(sol));
        }
        if (nuevo && preconfigSave(sol, file, ajax, loggedUser) == false) {
            return gsh;
        }
        ICodeSequenceDAO sq = getBean(ICodeSequenceDAO.class); 
        try {
            sol.setCode("SOL-" + sq.next(CodeSequence.type.CODE_REQUEST));
        } catch (final QMSException ex) {
            throw new RuntimeException(ex);
        }
        Request savedReq = makePersistent(sol, loggedUser.getId());
        gsh.setRequest(savedReq);
        if (savedReq == null) {
            gsh.setOperationEstatus(0);
        } else {
            refreshWorkflowPreview(savedReq);
            getLogger().info("saving request {}", savedReq.getId());  
            gsh.setOperationEstatus(1);
            getLogger().trace("id: " + gsh.getSavedId() + " - id - " + savedReq.getId());
        } 
        return gsh;
    }

    /**
     * Función que verifica la solicitud, llega desde el crud
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public SaveHandle<Request> verifyRequest(
            Request request, 
            Long verifierUserId, 
            Boolean skipAutomaticApproval,
            WorkflowAuthRole authRole,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        return workflowDao.workflowRequestStart(
                this.getAspectJAutoProxy(),
                request,
                verifierUserId,
                skipAutomaticApproval,
                authRole,
                loggedUser
        );
    }

    private boolean objectEquals(Object original, Object copy) {
        return original != null && original.equals(copy);
    }

    private void verificationToLog(Long requestId, Long loggedUserId) {
        List<Verification> lst = HQL_findByQuery(""
            + " SELECT c "
            + " FROM " + Verification.class.getCanonicalName() + " c "
            + " WHERE c.id.requestId = :requestId ", "requestId", requestId
        );
        for (Verification ver : lst) {
            VerificationLog log = new VerificationLog(
                new VerificationLogPK(
                    requestId, 
                    ver.getId().getUserId(),
                    ver.getLastModification()
                ), ver.getLastAction()
            );
            this.deleteEntity(ver);
            HQL_updateByQuery(""
                + " DELETE FROM " + VerificationLog.class.getCanonicalName() + " v "
                + " WHERE "
                    + " v.id.requestId = :requestId "
                    + " AND v.id.userId = :userId ", 
                ImmutableMap.of(
                    "requestId", requestId,
                    "userId", ver.getId().getUserId()
                )
            );
            this.makePersistent(log, loggedUserId);
        }
        //Aqui se deben de mandar los mails pertinentes basados en los usuarios que se borraron
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isVerifyRequestValid(IWorkflowRequest entity, SaveHandle result, ILoggedUser loggedUser) {
        Request request = (Request) entity;
        if(request.getType().equals(Request.NEW)){
            return !isRepeatedCodeOrName(request, loggedUser, result.getSaveHandle());
        }
        return true;
    }
        
    @Override
    @OnCreatedNextFill
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest createNextFill(IWorkflowBaseDAO holder, IWorkflowRequest request, ILoggedUser loggedUser, WorkflowPool poolDetails) {
        return getBean(IWorkflowDAO.class).createNextPoolDetails(holder, request, poolDetails, loggedUser);
    }
    
    @Override
    @OnFillFormRequested
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest requestFillForm(IWorkflowBaseDAO holder, IWorkflowRequest request, ILoggedUser loggedUser, WorkflowPool poolDetails) {
        return getBean(IWorkflowDAO.class).createNextPoolDetails(holder, request, poolDetails, loggedUser);
    }
    
    /**
     * Función que obtiene la siguiente versión del documento
     */
    private String getNextVersion(String currentVersion, Long dictionaryIndexId) {
    //TODO:Comment  Aqui va la nueva implementacion de los diccionarios de versionamiento
        Map map = new HashMap();
        Integer len = currentVersion.length();
        Integer deep = 1;
        // Entrar en un case gigante para los tipos de versionamiento
        Long maxId = HQL_findSimpleLong("SELECT MAX (c.id) FROM DPMS.Mapping.DocumentVersion c where c.dictionaryIndexId = :dictionaryIndexId ", "dictionaryIndexId", dictionaryIndexId); // Max Id 10 = 9
        Long minId = HQL_findSimpleLong("SELECT MIN (c.id) FROM DPMS.Mapping.DocumentVersion c where c.dictionaryIndexId = :dictionaryIndexId ", "dictionaryIndexId", dictionaryIndexId); // Max Id 1 = 0
        String previous;
        String unit;
        if (currentVersion.isEmpty()) {
            previous = currentVersion;
            unit = currentVersion;
        } else {
            previous = currentVersion.substring(0, len - deep);
            unit = currentVersion.substring(len - deep, len - deep + 1);
        }
        map.put("unit", unit);
        map.put("dictionaryIndexId", dictionaryIndexId);
        Long nextId;
        String tryout = this.HQL_findSimpleString(""
                + "SELECT c.id FROM DPMS.Mapping.DocumentVersion c "
                + "WHERE c.version = :unit "
                + "AND c.dictionaryIndexId = :dictionaryIndexId ", map);
        if ("null".equals(tryout) || tryout.isEmpty()) {
            nextId = minId;
            previous = "";
        } else {
            nextId = Long.parseLong(tryout) + 1;
        }
        String nextVersion;
        if (nextId > maxId) {
            nextId = minId;
            deep++;
            if ((len - deep) >= 0) {
                previous = getNextVersion(previous, dictionaryIndexId);
            } else {
                String firstNotNull;
                firstNotNull = this.HQL_findSimpleString(""
                + "    SELECT v.version from DPMS.Mapping.DocumentVersion v "
                    + " where v.dictionaryIndexId = :dictionaryIndexId "
                    + " and v.isFirstDigit = 1 ", "dictionaryIndexId", dictionaryIndexId);
                if (firstNotNull == null || firstNotNull.isEmpty()){
                    firstNotNull = this.HQL_findSimpleString(""
                        + "    SELECT s.newDigit from DPMS.Mapping.Settings s"
                        );
                }
                previous = firstNotNull;
            }

        }
        nextVersion = (this.HQL_findSimpleString(""
                + "SELECT c.version FROM DPMS.Mapping.DocumentVersion c "
                + "WHERE c.id = :nextId ", "nextId", nextId));
        nextVersion = previous + nextVersion;
        getLogger().trace("getNextVersion: {}",nextVersion);
        return nextVersion;
    }

    /**
     * Función que finaliza el flujo de una solicitud, las solicitudes pueden ser de documentos controlados, 
     * no controlados y formularios. Cierra los registros asociados al flujo que está 
     * cambiando según el tipo de solicitud.
     * 
     * 1. NEW: Publica el nuevo documento/formulario y envía los avisos configurados desde preferencias
     * 2. MODIFY : En ambos casos envía los avisos configurados desde preferencias
     *     2.1. Documentos: Publica el nuevo documento
     *     2.2. Formularios: 
     *          - Publica el nuevo formularios
     *          - Crea la nueva tabla de respuesatas
     *          - Migra las respuestas de una versión a otra
     * 3. APROVE: 
     * 4. CANCEL:
     * 5. FILL:
     *
     */ 
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IWorkflowRequest onWorkflowEnd(Long requestId, ILoggedUser loggedUser, Boolean readers) throws IOException, QMSException {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("finalizeRequest [requestId={}, loggedUser={}, readers={}]", new Object[] {
                requestId, loggedUser, readers
            });
        }
        Request request = HQLT_findById(requestId);
        if (request.getDocument() != null) {
            request.getDocument().setLastModificationDate(new Date());
        }
        // Cierre de solicitud de nuevo/modificación/reaprobación/cancelación de documentos
        request.setStatus(Request.STATUS_CLOSED);
        if (request.getAutorizationPool() != null) {
            request.getAutorizationPool().setStatus(AutorizationPool.STATUS_CLOSED);
        }
        Request.TYPE type = Request.TYPE.getType(request.getType());
        Request result = null;
        switch (type) {
            case NEW:
                result = getAspectJAutoProxy().publishNewDocument(request, readers, loggedUser);
                break;
            case MODIFY:
                final DocumentRef previousDocument = HQLT_findById(DocumentRef.class, request.getDocument().getId());
                result = getAspectJAutoProxy().publishDocumentModification(request, previousDocument, readers, loggedUser);
                break;
            case EDIT_DETAILS:
                final DocumentRef currentDocument = HQLT_findById(DocumentRef.class, request.getDocument().getId());
                result = getAspectJAutoProxy().publishEditDetailsDocument(request, currentDocument, loggedUser);
                break;
            case APROVE:
                result = getAspectJAutoProxy().finalizeDocumentReapproval(request, loggedUser);
                break;
            case CANCEL:
                result = finalizeCancellation(request, loggedUser);
                break;
            case FILL:
                result = finalizeFormFill(request, loggedUser);
                break;
            default:
        }
        return result;
    }    
    
    private Request finalizeCancellation(final Request request, final ILoggedUser loggedUser) {
        Request result = getAspectJAutoProxy().finalizeDocumentCancellation(request, request.getDocumentCode(), loggedUser);
        FormHelper helper = new FormHelper(this);
        if (helper.isForm(request.getId())) {
            /**
             * El siguiente codigo se coloca aquí debido a que
             * el metodo "finalizeDocumentCancellation" envia correos
             * que dependen de que lo siguiente no se haya ejecutado todavia
             * */
            ISurveyCaptureDAO dao = getBean(ISurveyCaptureDAO.class);
            Set<FormRequestorDTO> fillers = helper.getCurrentFormRequestors(request.getDocument().getId());
            for (FormRequestorDTO filler : fillers) {
                dao.cancelledForm(filler, request.getAuthor().getDescription(), request.getReazon(), filler.getOutstandingSurveyId(), loggedUser);
            }
            if (request.getDocument() != null && request.getDocument().getAnswersNodeId() != null) {
                HQL_updateByQuery(""
                        + " UPDATE " + NodeSimple.class.getCanonicalName()
                        + " SET deleted = 1 "
                        + " WHERE id = :id",
                        ImmutableMap.of("id", request.getDocument().getAnswersNodeId())
                );    
            }
        }
        return result;
    }

    private Request finalizeFormFill(final Request request, final ILoggedUser loggedUser) {
        final Request savedRequest = makePersistent(request, loggedUser.getId());
        workflowDao.updateRequestAuthorizatorNames(this.getAspectJAutoProxy(), savedRequest.getId());
        return savedRequest;
    }

    
    private void addDocumentTraceability(final Request request, final ILoggedUser loggedUser) {
        final DocumentTraceability trace = new DocumentTraceability();
        trace.setId(-1L);
        trace.setCreatedOn(new Date());
        trace.setDocumentCode(request.getDocumentCode());
        trace.setReason(request.getReazon());
        trace.setRequestType(request.getType());
        trace.setMasterId(request.getDocumentMasterId());
        Map params = new HashMap();
        params.put("requestId", request.getId());
        final String hql = ""
                + " SELECT c.verificator.id"
                + " FROM " + Verification.class.getCanonicalName() + " c"
                + " WHERE c.requestId = :requestId";
        final Long verificator = HQL_findSimpleLong(hql, params);
        if (verificator > 0) {
            trace.setVerifiedBy(new UserRef(verificator));
        }
        if (request.getDocument() != null) {
            trace.setVersion(request.getDocument().getVersion());
        } else if (request.getFillOutDocument() != null) {
            trace.setVersion(request.getFillOutDocument().getVersion());
        }
        makePersistent(trace, loggedUser.getId());
        // No se agrega traceId + L al map por no ser un parámetro
        final String authorizersHql = " "
                + " SELECT new " + DocumentTraceabilityUser.class.getCanonicalName() + "("
                + " " + trace.getId() + "L, c.userId, c.indice"
                + " )"
                + " FROM " + AutorizationPoolDetails.class.getCanonicalName() + " c"
                + " WHERE c.requestId = :requestId"
                + " AND c.status = " + AutorizationPoolDetails.STATUS.ACTIVE.getValue()
                + " AND c.userId IS NOT NULL";
        final List<DocumentTraceabilityUser> authorizers = HQL_findByQuery(authorizersHql, params);
        for (final DocumentTraceabilityUser authorizer : authorizers) {
            makePersistent(authorizer, loggedUser.getId());
        }
    }

    @Override
    @OnNewDocumentPublished
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request publishNewDocument(Request request, Boolean readers, ILoggedUser loggedUser) {
        String thisVersion = request.getVersion();
        if (thisVersion == null || thisVersion.isEmpty()) {
            thisVersion = Utilities.getSettings().getDocumentInitialVersion();
        }
        request.setVersion(thisVersion);
        Document document = requestToDocument(request, readers);
        document.setVersion(thisVersion);
        if (document.getSurveyId() != null) {
            NodeSimple node = new NodeSimple(-1L);
            node.setCode(document.getCode());
            node.setDescription(document.getDescription());
            node.setModule(Module.FORMULARIE.getKey());
            node.setParent(0L);
            node.setStatus(Node.STATUS.ACTIVE.getValue());
            node.setPath(document.getCode());
            node = makePersistent(node, loggedUser.getId());
            if (node == null) {
                getLogger().error("No fue posible establecer el nodoId en HibernateDAO_Request.publishNewDocument");
            } else {
                document.setAnswersNodeId(node.getId());
            }
        }
        document = makePersistent(document, loggedUser.getId());
        request.setDocument(document);
        Request r = makePersistent(request, loggedUser.getId());
        final IFormCaptureDAO formDao = getBean(IFormCaptureDAO.class);
        setRequestTypeIfMissing(request);
        final FormHelper helper = new FormHelper(this);
        if (helper.isForm(request.getId())) {
            if (r.getStatus() == Request.STATUS_CLOSED) {
                freeze(r);
            }
            formDao.createAnswerTable(
                    request.getDocumentMasterId(), 
                    request.getVersion(),
                    request.getSurveyId(), 
                    loggedUser
            );
            formDao.freezeExternalCatalog(request.getSurveyId(), loggedUser);
            
            final ISlimReportsDAO slimDao = getBean(ISlimReportsDAO.class);
            slimDao.newSlimReportFromDocument(request, document, loggedUser);
            
            IPrintingFormatDAO printDao = getBean(IPrintingFormatDAO.class);
            printDao.updateCountEntity(document.getMasterId(), loggedUser);
            
            final SurveyUtilCache cache = new SurveyUtilCache();
            formDao.recalculateSurveyDataAnswerColumns(r.getSurveyId(), cache, loggedUser);
            formDao.refreshSurveyAnswersMigration(document.getMasterId(), loggedUser);
            
        }
        addDocumentTraceability(r, loggedUser);
        workflowDao.updateRequestAuthorizatorNames(this.getAspectJAutoProxy(), r.getId());
        return r;
    }

    private void discontinuePreviousDocument(String masterId, ILoggedUser loggedUser) {
        Map map = new HashMap();
        map.put("masterId", masterId);
        map.put("status", Document.STATUS.DISCONTINUED.getValue());
        List<Long> previousDocuments = HQL_findByQuery(""
                + " SELECT c.id"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.masterId = :masterId"
                + " AND c.status IN ("
                    + Document.STATUS.ACTIVE.getValue()
                    + "," + Document.STATUS.IN_EDITION.getValue()
                + ")",
                map
        );
        HQL_updateByQuery(""
                + " UPDATE " + Document.class.getCanonicalName()
                + " SET status = :status "
                + " WHERE masterId = :masterId"
                + " AND status IN ("
                    + Document.STATUS.ACTIVE.getValue()
                    + "," + Document.STATUS.IN_EDITION.getValue()
                + ")",
                map
        );         
        IDocumentReaderDAO dao = getBean(IDocumentReaderDAO.class);
        for (final Long documentId : previousDocuments) {
            dao.cancelDocumentReaders(documentId, loggedUser);
        }
    }
    
    @Override
    @OnDocumentModified
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request publishDocumentModification(
            Request request,
            DocumentRef previousDocument,
            Boolean readers,
            ILoggedUser loggedUser
    ) throws QMSException {
        try {
            DocumentPublicationUtils.lockDocument(request.getDocumentMasterId());
            Long prevDocumentId = previousDocument.getId();
            String masterId = previousDocument.getMasterId();
            Long prevDictionaryIndex = getDictionaryIndexId(previousDocument.getTypeId());
            String prevVersion = previousDocument.getVersion();
            if (prevVersion == null) {
                prevVersion = Utilities.EMPTY_STRING;
            }

            discontinuePreviousDocument(masterId, loggedUser);
            Document newDocument = requestToDocument(request, readers);
            String nextVersion = getNextVersion(prevVersion, prevDictionaryIndex);
            newDocument.setVersion(nextVersion);
            newDocument.setAnswersNodeId(previousDocument.getAnswersNodeId());
            newDocument = makePersistent(newDocument, loggedUser.getId());
            Long newId = newDocument.getId();
            IDocumentHistoryModifyedDAO dhmDAO = getBean(IDocumentHistoryModifyedDAO.class);
            dhmDAO.save(newId, prevDocumentId);
            copyPhysicalReaders(prevDocumentId, newId, loggedUser.getId());
            IReceiptAcknowledgmentDAO dao = getBean(IReceiptAcknowledgmentDAO.class);
            dao.omitPreviousPrintHistory(prevDocumentId, loggedUser);
            dao.copyPrintHistory(prevDocumentId, newDocument, loggedUser);
            copyRelatedDocuments(prevDocumentId, newId, loggedUser.getId());
            deletePreviousRelatedDocuments(prevDocumentId);
            request.setVersion(newDocument.getVersion());
            request.setOldDocument(new Document(prevDocumentId));
            request.setDocument(newDocument);
            Request r = makePersistent(request, loggedUser.getId());
            setRequestTypeIfMissing(request);
            if (previousDocument.getFileId() != null && !Objects.equals(request.getFileId(), previousDocument.getFileId())) {
                final PdfCleanerExecutor lazyExecutor = BnextDaemonUtil.getRunningInstance(PdfCleanerExecutor.class);
                lazyExecutor.execute(previousDocument.getFileId(), true, true);
            }
            final FormHelper helper = new FormHelper(this);
            if (helper.isForm(request.getId())) {
                if (r.getStatus() == Request.STATUS_CLOSED) {
                    freeze(r);
                }
                final IFormCaptureDAO formDao = getBean(IFormCaptureDAO.class);
                final Integer insertedRecords = formDao.migrateAnswerTable(
                        request.getDocumentMasterId(),
                        request.getVersion(),
                        request.getSurveyId(),
                        previousDocument.getSurveyId(),
                        loggedUser);
                final ISurveysDAO surveysDAO = getBean(ISurveysDAO.class);
                surveysDAO.migrateFormConfig(request.getSurveyId(), request.getVersion(), prevDocumentId, loggedUser);
                getLogger().info(
                        "{} were migrated of form with code {} form version {} to {}",
                        insertedRecords,
                        newDocument.getCode(),
                        previousDocument.getVersion(),
                        newDocument.getVersion()
                );
                formDao.freezeExternalCatalog(request.getSurveyId(), loggedUser);
                IPrintingFormatDAO printDao = getBean(IPrintingFormatDAO.class);
                printDao.updateCountEntity(newDocument.getMasterId(), loggedUser);

                final SurveyUtilCache cache = new SurveyUtilCache();
                formDao.recalculateSurveyDataAnswerColumns(r.getSurveyId(), cache, loggedUser);
                formDao.refreshSurveyAnswersMigration(newDocument.getMasterId(), loggedUser);
            }
            addDocumentTraceability(r, loggedUser);
            workflowDao.updateRequestAuthorizatorNames(this.getAspectJAutoProxy(), r.getId());
            DocumentPublicationUtils.releaseDocument(request.getDocumentMasterId());
            return r;
        } catch (Exception ex) {
            DocumentPublicationUtils.releaseDocument(request.getDocumentMasterId());
            throw ex;
        }
    }

    @Override
    @OnEditDetailsDocument
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request publishEditDetailsDocument(
            Request request,
            DocumentRef document,
            ILoggedUser loggedUser
    ) {
        try {
            DocumentPublicationUtils.lockDocument(request.getDocumentMasterId());
            Document currentDocument = HQLT_findById(Document.class, document.getId());
            updateDocumentFromRequest(request, currentDocument, false);
            makePersistent(currentDocument, loggedUser.getId());
            Request r = makePersistent(request, loggedUser.getId());
            addDocumentTraceability(r, loggedUser);
            workflowDao.updateRequestAuthorizatorNames(this.getAspectJAutoProxy(), r.getId());
            DocumentPublicationUtils.releaseDocument(request.getDocumentMasterId());
            return r;
        } catch (Exception ex) {
            DocumentPublicationUtils.releaseDocument(request.getDocumentMasterId());
            throw ex;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void cancelFillsByUserId(final Long userId, final String comment, final ILoggedUser loggedUser) {
        final List<FillInfoDTO> fills = HQL_findByQuery(""
                + " SELECT new " + FillInfoDTO.class.getCanonicalName() + "("
                        + " c.id, "
                        + " c.outstandingSurveysId"
                + " )"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.type = " + Request.TYPE.FILL.getValue()
                + " AND c.author.id = :userId"
                + " AND c.status IN (" 
                    + Request.STATUS.STAND_BY.getValue() + "," 
                    + WorkflowRequestStatus.APROVING.getValue() + "," 
                    + WorkflowRequestStatus.RETURNED.getValue() 
                + ")", "userId", userId);
        if (fills == null || fills.isEmpty()) {
            return;
        }
        final ISurveyCaptureDAO dao = getBean(ISurveyCaptureDAO.class);
        dao.cancellFillForms(fills, comment, loggedUser);
    }

    private void copyPhysicalReaders(Long previousDocumentId, Long newDocumentId, Long loggedUserId) {
        List<PhysicalReader> lstPhReader = HQL_findByQuery(""
                + " SELECT ph"
                + " FROM " + PhysicalReader.class.getCanonicalName() + " ph "
                + " WHERE ph.id.documentId = :previousDocumentId ", "previousDocumentId", previousDocumentId);
        for (PhysicalReader ph : lstPhReader) {
            PhysicalReader ent = new PhysicalReader(newDocumentId, ph.getId().getPositionId());
            ent.setStatus(0);
            ent.setAssignedDate(new Date());
            makePersistent(ent, loggedUserId);
        }
    }
    
    private void copyRelatedDocuments(Long previousDocumentId, Long newDocumentId, Long loggedUserId) {
        //Verificamos si existen documentos relacionados
        List<RelatedDocument> lst;
        lst = (List<RelatedDocument>) this.HQL_findByQuery(""
                + "FROM " + RelatedDocument.class.getCanonicalName() + " c "
                + "WHERE c.id.documentIdB = :did", "did", previousDocumentId);
        for (RelatedDocument rd : lst) {
            RelatedDocumentPK rdocPk = new RelatedDocumentPK(rd.getId().getDocumentIdA(), newDocumentId);
            RelatedDocument rdoc = new RelatedDocument(rdocPk);
            this.makePersistent(rdoc, loggedUserId);
        }
        lst = (List<RelatedDocument>) this.HQL_findByQuery(""
                + "FROM " + RelatedDocument.class.getCanonicalName() + " c "
                + "WHERE c.id.documentIdA = :did", "did", previousDocumentId);
        for (RelatedDocument rd : lst) {
            RelatedDocumentPK rdocPk = new RelatedDocumentPK(newDocumentId, rd.getId().getDocumentIdB());
            RelatedDocument rdoc = new RelatedDocument(rdocPk);
            this.makePersistent(rdoc, loggedUserId);
        }
    }
    
    private void deletePreviousRelatedDocuments(Long previousDocumentId) {
        this.HQL_updateByQuery(""
                + "DELETE FROM DPMS.Mapping.RelatedDocument "
                + "WHERE id.documentIdA = :did OR id.documentIdB = :did", "did", previousDocumentId);
    }
    
    @Override
    @OnDocumentReapproved
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request finalizeDocumentReapproval(Request request, ILoggedUser loggedUser) {
        Document document = request.getDocument();
        document.setStatus(Document.ACTIVE_STATUS);
        final Date lastModificationDate = new Date();
        document.setLastModificationDate(lastModificationDate);
        final Long typeId = request.getDocument().getDocumentType().getId();
        final Long departmentId = request.getDocument().getBusinessUnitDepartmentId();
        final Date calculatedExpirationDate = calculateExpirationDate(lastModificationDate, typeId, departmentId);
        document.setExpirationDate(calculatedExpirationDate);
        final Integer enablePdfViewer = getBean(IDocumentTypeDAO.class).getEnablePdfViewer(typeId);
        if (!Objects.equals(enablePdfViewer, request.getEnablePdfViewer())) {
            getLogger().warn("PDF Viewer for document {} changed after saving. Old value {}. New value {}", new Object[]{
                request, enablePdfViewer, request.getEnablePdfViewer()
            });
        }
        document.setEnablePdfViewer(enablePdfViewer);
        document.setStoragePlaceId(request.getStoragePlaceId());
        document.setRetentionText(request.getRetentionText());
        document.setRetentionTime(request.getRetentionTime());
        if(document.getAuthor() == null) {
            document.setAuthor(loggedUser.getUserRefInstance());
        }
        makePersistent(request, loggedUser.getId());
        makePersistent(document, loggedUser.getId());
        addDocumentTraceability(request, loggedUser);
        workflowDao.updateRequestAuthorizatorNames(this.getAspectJAutoProxy(), request.getId());
        return request;
    }
    
    @Override
    @OnDocumentCancelled
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request finalizeDocumentCancellation(Request request, String bkUpCode, ILoggedUser loggedUser) {
        final IReceiptAcknowledgmentDAO ackDao = getBean(IReceiptAcknowledgmentDAO.class);
        final IDocumentReaderDAO docDao = getBean(IDocumentReaderDAO.class);
        final Request result = makePersistent(request, loggedUser.getId());
        final String masterId = request.getDocument().getMasterId();
        Document document = request.getDocument();
        document.setCode(Document.CANCEL_PREFIX + document.getCode() + "-" + request.getId());
        document.setDescription(Document.CANCEL_PREFIX + document.getDescription() + "-" + request.getId());
        document.setStatus(Document.CANCELED_STATUS);
        document.setLastModificationDate(new Date());
        document.setStoragePlaceId(request.getStoragePlaceId());
        document.setRetentionText(request.getRetentionText());
        document.setRetentionTime(request.getRetentionTime());
        if(document.getAuthor() == null) {
            document.setAuthor(loggedUser.getUserRefInstance());
        }
        makePersistent(document, loggedUser.getId());
        updateRequestsFromHistory(masterId, bkUpCode);
        cancelOtherRequests(masterId, document.getId());
        IUserRefDAO links = getBean(IUserRefDAO.class);
        links.cancelFavoritesByMasterId(masterId, loggedUser);
        docDao.cancelDocumentReaders(document.getId(), loggedUser);
        ackDao.cancelPrintCopies(document.getId(), loggedUser);
        ackDao.requestPickUpCopies(document.getId(), loggedUser);
        if (request.getFileId() != null) {
            final PdfCleanerExecutor lazyExecutor = BnextDaemonUtil.getRunningInstance(PdfCleanerExecutor.class);
            lazyExecutor.execute(request.getFileId(), true, true);
        }
        this.updateDocumentCodeInRequest(masterId);
        this.addDocumentTraceability(result, loggedUser);
        workflowDao.updateRequestAuthorizatorNames(this.getAspectJAutoProxy(), request.getId());
        return result;
    }

    private void updateRequestsFromHistory(final String masterId, final String bkUpCode) {
        Map<String, Object> map = new HashMap<>(3);
        map.put("masterId", masterId);
        map.put("new_code", Document.CANCEL_PREFIX+Document.DISCONTINUED_PREFIX+bkUpCode);
        map.put("status",Document.CANCELED_DISCONTINUED_STATUS);
        getLogger().trace("MAP> " + map.toString());
        /*Actualiza la clave del documento a las solicitudes
        de los documentos descontinuados que serán cancelados descontinuados*/
        this.HQL_updateByQuery(""
            + " UPDATE " + Request.class.getCanonicalName() + " c "
            + " SET c.documentCode = \'" + Document.CANCEL_PREFIX + Document.DISCONTINUED_PREFIX + "\' + c.documentCode "
            + " WHERE"
                + " exists ("
                    + " SELECT d.id "
                    + " FROM " + Document.class.getCanonicalName() + " d"
                    + " WHERE"
                        + " c.id = d.request.id"
                        + " AND d.masterId = :masterId "
                        + " AND d.status IN ("
                            + Document.STATUS.DISCONTINUED.getValue()
                        + " )"
                + " )", map
        );
        /*Actualiza la clave y status a los documentos descontinuados
        para indicar que ahora son cancelados descontinudados*/
        this.HQL_updateByQuery(""
            + " UPDATE "+ Document.class.getCanonicalName() + " c "
            + " SET"
                + " c.status = :status, "
                + " c.code = :new_code "
            + " WHERE c.masterId = :masterId "
            + " AND c.status IN (" + Document.DISCONTINUED_STATUS + ")", map
        );
    }
    
   
    private void cancelOtherRequests(String documentMasterId, Long documentId) {
        HQL_updateByQuery(""
            + " UPDATE " + Request.class.getCanonicalName() + " c "
            + " SET "
                + " c.status = " + WorkflowRequestStatus.CANCELED.getValue()
                + " ,rejectionDate = CURRENT_DATE() "
            + " WHERE "
                + " c.status IN ("
                    + WorkflowRequestStatus.APROVING.getValue()
                    + "," + WorkflowRequestStatus.RETURNED.getValue()
                    + "," + WorkflowRequestStatus.VERIFING.getValue()
                + ")"
                + " AND ("
                    + " c.documentMasterId = :documentMasterId"
                    + " OR c.document.id = " + documentId
                + " ) "
                + " AND c.type != " + Request.TYPE.FILL.getValue()
            , "documentMasterId", documentMasterId
        );
    
    }

    private void updateDocumentCodeInRequest(String masterId) {
        Map<String, String> map = new HashMap<>(1);
        map.put("masterId", masterId);
        this.HQL_updateByQuery(""
                + " UPDATE " + Request.class.getCanonicalName() + " c "
                + " SET c.documentCode = \'" + Document.CANCEL_PREFIX + "\' + c.documentCode + " + "\'-\' + toString19(c.id)"
                + " WHERE c.documentMasterId = :masterId", map);
    }
    
    private Date calculateExpirationDate(final Date lastModificationDate, final Long documentTypeId, final Long buinessUnitDepartmentId) {
        final Calendar cal = Calendar.getInstance();
        cal.setTime(lastModificationDate);
        Integer documentTypeExpiration = getBean(IBusinessUnitDepartmentLoadDAO.class).getDocumentExpirationById(buinessUnitDepartmentId);
        if (documentTypeExpiration == null || documentTypeExpiration <= 0) {
            documentTypeExpiration = getBean(IDocumentTypeDAO.class).getDocumentExpirationById(documentTypeId);
        }
        cal.add(Calendar.MONTH, documentTypeExpiration);
        return cal.getTime();
    }

    private Document requestToDocument(Request req, boolean readers) {
        Document doc = new Document(-1L);
        updateDocumentFromRequest(req, doc, readers);
        return doc;
    }

    private void updateDocumentFromRequest(Request req, Document doc, boolean readers) {
        doc.setCode(req.getDocumentCode());
        doc.setMasterId(req.getDocumentMasterId());
        doc.setCreationDate((req.getDocument() == null || req.getDocument().getCreationDate() == null) ? new Date() : req.getDocument().getCreationDate());
        doc.setDeleted(Document.IS_NOT_DELETED);
        doc.setDescription(req.getDescription());
        doc.setDocumentType(req.getDocumentType());
        doc.setIsBackup(0);
        doc.setFileId(req.getFileId());
        doc.setFlujoId(req.getFlujoId());
        final Long surveyId = req.getSurveyId();
        Map<String, Object> businessUnitMain = null;
        if (surveyId != null) {
            businessUnitMain = HQL_findSimpleMap(""
                + " SELECT new map("
                    + " fo.id AS fieldObjectId"
                    + ",fo.field_id AS fieldId"
                + " )"
                + " FROM " + SurveyField.class.getCanonicalName() + " f "
                + " JOIN f.obj fo "
                + " WHERE fo.surveyId = :surveyId"
                + " AND fo.businessUnitDepartmentMainField = 1", 
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
            );
        }
        if (businessUnitMain != null && !businessUnitMain.isEmpty()) {
            doc.setRestrictRecordsByDepartment(true);
            doc.setValidateAccessFormDepartment(false);
            doc.setRestrictRecordsField((String)businessUnitMain.get("fieldId")); 
            doc.setRestrictRecordsObjId((Long)businessUnitMain.get("fieldObjectId"));  
        } else {
            doc.setRestrictRecordsByDepartment(req.getRestrictRecordsByDepartment());
            doc.setValidateAccessFormDepartment(req.getValidateAccessFormDepartment());
            doc.setRestrictRecordsField(req.getRestrictRecordsField());
            doc.setRestrictRecordsObjId(req.getRestrictRecordsObjId());
        }
        doc.setRequest(req);
        final Date lastModificationDate = new Date();
        doc.setLastModificationDate(lastModificationDate);
        final Long typeId = req.getDocumentType().getId();
        final Long departmentId = req.getBusinessUnitDepartmentId();
        final Date calculatedExpirationDate = calculateExpirationDate(lastModificationDate, typeId, departmentId);
        doc.setExpirationDate(calculatedExpirationDate);
        final Integer enablePdfViewer = getBean(IDocumentTypeDAO.class).getEnablePdfViewer(req.getDocumentType().getId());
        if (!Objects.equals(enablePdfViewer, req.getEnablePdfViewer())) {
            getLogger().warn("PDF Viewer for document {} changed after saving. Old value {}. New value {}", new Object[]{
                req, enablePdfViewer, req.getEnablePdfViewer()
            });
        }
        doc.setEnablePdfViewer(enablePdfViewer);
        if (readers) {
            doc.setReaders(0);
        } else {
            doc.setReaders(1);
        }
        doc.setRetentionText(req.getRetentionText());
        doc.setRetentionTime(req.getRetentionTime());
        doc.setStatus(1);
        doc.setAuthor(req.getAuthor());
        doc.setStatus(Document.ACTIVE_STATUS);
        doc.setNodo(req.getNodo());
        if (req.getDocument() == null || req.getDocument().getOriginador() == null) {
            doc.setOriginador(req.getAuthor());
        } else {
            doc.setOriginador(req.getDocument().getOriginador());
        }
        doc.setOrganizationalUnit(req.getOrganizationalUnit());
        doc.setBusinessUnit(req.getBusinessUnit());
        if (req.getDepartment() != null) {
        doc.setDepartment(new BusinessUnitDepartmentLite(req.getDepartment().getId(), req.getDepartment().getDescription()));
        }
        doc.setStoragePlaceId(req.getStoragePlaceId());
        doc.setSurveyId(req.getSurveyId());
        doc.setDynamicTableName(req.getDynamicTableName());
        if (req.getCollectingAndStoreResponsible() != null ) {
            doc.setCollectingAndStoreResponsible(req.getCollectingAndStoreResponsible());
        }
        doc.setCollectingAndStoreResponsibleDescription(req.getCollectingAndStoreResponsibleDescription());
        doc.setInformationClassification(req.getInformationClassification());
        doc.setDisposition(req.getDisposition());
    }
        
    @Override
    @OnRejectAuthorization
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean rejectAutorization(
            final Request original,
            final String originalCode,
            final String comment, 
            final WorkflowAuthRole authRole,
            final ILoggedUser loggedUser
    ) throws IOException, QMSException { 
        return rejectRequest(original, comment, authRole, loggedUser, false);
    }
    
    @Override
    @OnFillFormRejected
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean rejectFillForm(
            final Request original,
            final String comment,
            final WorkflowAuthRole authRole, 
            final ILoggedUser loggedUser
    ) throws IOException, QMSException { 
        return rejectRequest(original, comment, authRole, loggedUser, true);
    }
    
    private boolean rejectRequest(
            final Request original,
            final String comment,
            final WorkflowAuthRole authRole,
            final ILoggedUser loggedUser,
            boolean isFillForm
    ) throws IOException, QMSException { 
        Boolean result = true;
        Request newReq = new Request();
        if (!isFillForm && Utilities.getSettings().getRequestReturnsToVerification() == 1) {
            getLogger().info("creating new request to verify !!! ");
            newReq.setAuthor(original.getAuthor());
            newReq.setBusinessUnit(original.getBusinessUnit());
            newReq.setDepartment(original.getDepartment());
            newReq.setDescription(original.getDescription());
            newReq.setDocument(original.getDocument());
            newReq.setDocumentCode(original.getDocumentCode());
            newReq.setDocumentType(original.getDocumentType());
            newReq.setRestrictRecordsByDepartment(original.getRestrictRecordsByDepartment());
            newReq.setValidateAccessFormDepartment(original.getValidateAccessFormDepartment());
            newReq.setRestrictRecordsField(original.getRestrictRecordsField());
            newReq.setRestrictRecordsObjId(original.getRestrictRecordsObjId());
            newReq.setFileId(original.getFileId());
            newReq.setNodo(original.getNodo());
            newReq.setOrganizationalUnit(original.getOrganizationalUnit());
            newReq.setReazon(original.getReazon());
            newReq.setEnablePdfViewer(original.getEnablePdfViewer());
            newReq.setRetentionText(original.getRetentionText());
            newReq.setRetentionTime(original.getRetentionTime());
            newReq.setStoragePlaceId(original.getStoragePlaceId());
            newReq.setType(original.getType());
            newReq.setDocumentMasterId(original.getDocumentMasterId());
            newReq.setVersion(original.getVersion());
            newReq.setScope(original.getScope()); 
            newReq.setDynamicTableName(original.getDynamicTableName());
            newReq.setSurveyId(original.getSurveyId());
            newReq.setCollectingAndStoreResponsible(original.getCollectingAndStoreResponsible());
            newReq.setCollectingAndStoreResponsibleDescription(original.getCollectingAndStoreResponsibleDescription());
            newReq.setDisposition(original.getDisposition());
            newReq.setInformationClassification(original.getInformationClassification());
            getLogger().info("request rejected {}", newReq);
        }
        if (isFillForm) {
            workflowDao.workflowRequestRejectAndRestart(this.getAspectJAutoProxy(), original, authRole, comment, loggedUser);
        } else {
            workflowDao.workflowRequestReject(this.getAspectJAutoProxy(), original, authRole, comment, loggedUser);
        }
        if (!isFillForm && Utilities.getSettings().getRequestReturnsToVerification() == 1) {
            FilesLite file = HQLT_findById(FilesLite.class, original.getFileId());
            RequestSaveHandle gsh = getAspectJAutoProxy().save(newReq, file, original.getType(), loggedUser, false, true);
            result = gsh.getOperationEstatus().equals(1);
            if(original.getDynamicTableName() != null && !original.getDynamicTableName().isEmpty()){
                updateReferenceDyntable(
                    original.getDynamicTableName(), 
                    Request.class.getAnnotation(Table.class).name(), 
                    original.getId(),
                    newReq.getId()
                );
            }
        }
        if (original.getSurveyId() != null) {
            Long sId = original.getSurveyId();
            if (Utilities.getSettings().getRequestReturnsToVerification() == 1) {
                HQL_updateByQuery(""
                    + " UPDATE " + Survey.class.getCanonicalName() + " s "
                    + " SET s.request.id = :requestId"
                    + " WHERE s.id = :surveyId",
                    ImmutableMap.of(
                        "requestId", newReq.getId(),
                        "surveyId", sId
                    )
                );
            } else if(original.getType().equals(Request.NEW)) {
                HQL_updateByQuery(""
                    + " UPDATE " + Survey.class.getCanonicalName() + " s "
                    + " SET s.estatus = " + Survey.ESTATUS_INACTIVO
                    + " WHERE s.id = :surveyId",
                    ImmutableMap.of(
                        "surveyId", sId
                    )
                );
            }
        }
        return result;
    }

    private void updateReferenceDyntable(String dynamicFieldTableName, String referenceTableName, Long originalId, Long newId) {
        String idReferenced = DynamicTableHelper.getReferenceIdName(this, dynamicFieldTableName, referenceTableName);
        // Solo se parametrizan valores.
        SQL_updateByQuery(""
            + " UPDATE " + dynamicFieldTableName
            + " SET " + idReferenced + " = :newId "
            + " WHERE " + idReferenced + " = :originalId",
            ImmutableMap.of(
                "newId", newId,
                "originalId", originalId
            ),
            0,
            Arrays.asList(dynamicFieldTableName)
        );
    }

    private boolean preconfigSave(Request newReq, FilesLite file, boolean ajax, ILoggedUser loggedUser) {
        if (ajax) {
            if (newReq.getDepartment() == null) {
                SimplePuesto defaulPosition = (SimplePuesto) HQL_findSimpleObject(""
                        + " SELECT c "
                        + " FROM " + SimplePuesto.class.getCanonicalName() + " c"
                        + " WHERE EXISTS ("
                            + " SELECT 1"
                            + " FROM " + User.class.getCanonicalName() + " u"
                            + " WHERE u.id = :loggedUser AND u.defaultWorkflowPosition.id = c.id"
                        + " )", "loggedUser", loggedUser.getId());
                if (defaulPosition == null) {
                    GenericSaveHandle gsh = new GenericSaveHandle();
                    gsh.setOperationEstatus(0);
                    String msg = "La solicitud no pud&oacute; ser guardada.<br/>Revise los puestos"
                            + "que est&aacute;n asignados a su cuenta de usuario.";
                    gsh.setErrorMessage(msg);
                    throw new DAOException(gsh);
                }
                if (defaulPosition.getUneId() == null) {
                    newReq.setScope(Request.SCOPE.ORGANIZATIONAL_UNIT.getValue()); 
                    newReq.setOrganizationalUnit(new OrganizationalUnitLite(defaulPosition.getCorpId()));
                } else {    
                    newReq.setScope(Request.SCOPE.BUSINESS_UNIT.getValue());  
                    Long businessUnitId = defaulPosition.getUneId();
                    Long organizationalId = HQL_findSimpleLong(""
                            + " SELECT c.organizationalUnitId"
                            + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                            + " WHERE c.id = :businessUnitId", "businessUnitId", businessUnitId); 
                    newReq.setOrganizationalUnit(organizationalId == 0L ? null : new OrganizationalUnitLite(organizationalId));
                    newReq.setBusinessUnit(new BusinessUnitLite(businessUnitId));
                }
            } else {
                Long departmentId = newReq.getDepartment().getId();
                Long businessUnitId = HQL_findSimpleLong(""
                        + " SELECT c.businessUnitId"
                        + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " c"
                        + " WHERE c.id = :departmentId", "departmentId", departmentId);
                Long organizationalId = HQL_findSimpleLong(""
                        + " SELECT c.organizationalUnitId"
                        + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                        + " WHERE c.id = :businessUnitId", "businessUnitId", businessUnitId);
                newReq.setOrganizationalUnit(organizationalId == 0L ? null : new OrganizationalUnitLite(organizationalId));
                newReq.setBusinessUnit(new BusinessUnitLite(businessUnitId));
                newReq.setScope(Request.SCOPE.DEPARTMENT.getValue());
            }
        }
        if (file != null) {
            newReq.setFileId(file.getId());
        }
        if (newReq.getDocument() != null && (newReq.getDocument().getId() == null 
                || newReq.getDocument().getId().equals(-1L))) {
            newReq.setDocument(null);
        }
        if (newReq.getDocument() != null && newReq.getDocument().getId() != null) {
            newReq.setDocumentMasterId(getDocumentMasterId(newReq.getDocument().getId()));
        }
        if (newReq.getDocumentMasterId() == null && Objects.equals(newReq.getType(), Request.TYPE.NEW.getValue())) {
            newReq.setDocumentMasterId(generateDocumentMasterId());
        }
        return true;
    }
  
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PreValidateRequestDTO preValidateRequest(final Request request, final ILoggedUser loggedUser) {
        final IDocumentDAO docCrud = getBean(IDocumentDAO.class);
        final FilesLite file;
        if (request.getFileContent() != null) {
            file = new FilesLite(request.getFileContent().getId());
        } else {
            file = null;
        }
        preconfigSave(request, file, true, loggedUser);
        if (request.getDepartment() != null) {
            request.setBusinessUnitDepartmentId(request.getDepartment().getId());
        }
        if (request.getBusinessUnit() != null) {
            request.setBusinessUnitId(request.getBusinessUnit().getId());
        }
        final CodeValidatorDTO validator = new DocumentCodeValidator().isValid(request, request.getDocumentCode(), this);
        boolean repeatedCodeDocument;
        String modifiedDocumentCode = null;
        if (Request.MODIFY.equals(request.getType()) || Request.EDIT_DETAILS.equals(request.getType())) {
            repeatedCodeDocument = false;
            modifiedDocumentCode = request.getDocumentCode();
        } else {
            repeatedCodeDocument = validator.isRepeatedByDocument();
        }
        final boolean repeatedCodeRequest = validator.isRepeatedByRequest();
        Settings.CODE_SCOPE scope = Settings.CODE_SCOPE.fromValue(Utilities.getSettings().getDocumentCodeScope());
        boolean repeatedNameDocument = false;
        switch(scope) {
            case LEVEL_APPLICATION:
                repeatedNameDocument = docCrud.repeatedNameDocument(request.getDescription(), modifiedDocumentCode);
                break;
            case LEVEL_BUSINESS_UNIT:
                repeatedNameDocument = docCrud.repeatedNameDocument(request.getDescription(), request.getBusinessUnitId(), modifiedDocumentCode);
            break;
        }
        final ISlimReportsDAO slimDao = Utilities.getBean(ISlimReportsDAO.class);
        final Boolean repeatedSlimReportName = slimDao.repeatedSlimReportName(request.getSlimReportName());
        final boolean repeatedNameRequest = docCrud.repeatedNameRequest(request.getDescription(), request.getId());
        final PreValidateRequestDTO result = new PreValidateRequestDTO();
        if (repeatedCodeDocument || repeatedCodeRequest || repeatedNameDocument || repeatedNameRequest || repeatedSlimReportName) {
            result.setRepeated(true);
            result.setRepeatedCodeRequest(repeatedCodeRequest);
            result.setRepeatedCodeDocument(repeatedCodeDocument);
            result.setRepeatedNameRequest(repeatedNameRequest);
            result.setRepeatedNameDocument(repeatedNameDocument);
            result.setRepeatedSlimReportName(repeatedSlimReportName);
            if (repeatedCodeDocument || repeatedCodeRequest) {
                result.setRepeatedCode(true);
            }
            if (repeatedNameDocument || repeatedNameRequest) {
                result.setRepeatedName(true);
            }
        } else {
            result.setRepeated(false);
        }
        result.setDocumentCodeScope(Utilities.getSettings().getDocumentCodeScope());
        return result;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setRequestOnWorkflowReject(IWorkflowRequest entity, ILoggedUser loggedUser) {
        Request request = (Request) entity;
        request.setDocumentCode(Request.CANCELED_REQUEST_PREFIX + request.getDocumentCode());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setRequestReleasedOnWorkflowReject(Long requestId, WorkflowAuthRole authRole, ILoggedUser loggedUser) {
        HQL_updateByQuery(""
            + " UPDATE " + Document.class.getCanonicalName() + " c"
            + " SET c.status = " + Document.ACTIVE_STATUS
            + " WHERE EXISTS ( "
                + " SELECT 1 "
                + " FROM " + Request.class.getCanonicalName() + " r "
                + " WHERE r.id = :requestId "
                + " AND c.id = r.document.id"
            + " )", "requestId", requestId
        );
    }
    
    /**
     * Función que devuelve la unidad de la solicitud, que puede estar especificada directamente en 
     * la solicitud, o tomada desde el departamento
     *
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public String getBusinessUnitFromRequest(Long requestId) {
        /*Permiso de encargado de documentos*/
        String businessUnitId = HQL_findSimpleString(""
            + " SELECT c.businessUnit.id"
            + " FROM " + Request.class.getCanonicalName() + " c "
            + " WHERE c.id = :requestId ", "requestId", requestId
        );
        if ("null".equals(businessUnitId)){
            getLogger().trace("the request does not have a business unit assigned, trying on departments business unit");
            businessUnitId = HQL_findSimpleString(""
                + " SELECT c.department.businessUnitId"
                + " FROM " + Request.class.getCanonicalName() + " c "
                + " WHERE c.id = :requestId ", "requestId", requestId
            );
        }
        getLogger().trace("request {} belongs to business unit {}", requestId, businessUnitId);
        return businessUnitId;
    }

    /**
     * Obtiene la información de bloqueo de una solicitud
     *
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Map<String, Object> loadRequestLockedInfo(Long requestId) {
        return this.HQL_findSimpleMap(" "
            + " SELECT"
            + " new Map(" +
                "c.id as id, " +
                "c.isBusy as isBusy," +
                " c.timeToFreeMinutes as timeToFreeMinutes," +
                " c.blockedBy as blockedBy," +
                " c.blockedByName as blockedByName" +
                ")"
            + " FROM " + Request.class.getCanonicalName() + " c"
            + " WHERE c.id = :requestId ", "requestId", requestId);
    }
    
    /**
     * Coloca el pendiente como "ocupado" durante 5 minutos, al llegar el momento el pendiente se libera
     *
     * @param userId Id del usuario que bloquea la solicitud
     * @param userDescription Descripción del usuario que bloquea la solicitud
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public String lockRequest(Long requestId, Long userId, String userDescription, @Nonnull ISecurityUser admin) {
        //Bloqueo el pendiente
        Request request = new Request(requestId);
        boolean alreadyBlocked = setToBusy(request, true, userDescription, userId);
        if (alreadyBlocked) {
            Long lockedBy = this.HQL_findSimpleLong(""
                    + " SELECT c.blockedBy"
                    + " FROM " + Request.class.getCanonicalName() + " c"
                    + " WHERE id = :requestId ", "requestId", requestId); 
            request.update("timeToFreeMinutes", 5, this); //<--- se liberará el pendiente en 5 minutos
            if (lockedBy.compareTo(userId) == 0) {
                getLogger().debug("El pendiente esta bloqueado correctamente {}", requestId);  
                return StandardEntity.ACTIVE_STATUS.toString(); //"Se bloquea el pendiente correctamente.";            
            }
            getLogger().info("El timer ya esta corriendo...");   
            return StandardEntity.INACTIVE_STATUS.toString();//"El pendiente ya estaba bloqueado, se reinicia a 5 minutos";
        }
        final Timer timer = new Timer();
        timer.scheduleAtFixedRate(
            /**
             * @IMPORTANTE: Este tiempo debe coincidir el que se resta
             * dentro del "run")
             */
            new LockRequestTask(requestId, timer), 0, 300000
        );//<--- cada 5 minutos (300,000 milisegundos)
        getLogger().debug("Se bloquea correctamente el pendiente {}", requestId);
        return StandardEntity.ACTIVE_STATUS.toString(); //"Se bloquea el pendiente correctamente.";
    }
    
    /**
     * Firma un campo de un formulario
     *
     * @param loggedUser Usuario que bloquea la solicitud
     * <AUTHOR> Guadalupe Quintanilla Flores
     * @since 2.6.0.0
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public boolean signTheForm(
            final Long outstandingSurveyId, 
            final Boolean sign, 
            final WorkflowAuthRole authRole,
            final List<FieldCommentDTO> comments,
            final Long surveyFieldObjId, 
            final ILoggedUser loggedUser, 
            final Long currentAutorizationPoolIndex,
            final Integer currentRecurrence,
            final Request sol
    ) {
        try {
            OutstandingQuestion question = (OutstandingQuestion) this.HQL_findSimpleObject(""
                + " SELECT p"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " JOIN c.preguntasRespondidas p"
                + " WHERE c.id = :outstandingSurveyId "
                + " AND p.fieldId = :surveyFieldObjId ",
                ImmutableMap.of(
                    "outstandingSurveyId", outstandingSurveyId,
                    "surveyFieldObjId", surveyFieldObjId
                )
            );
            question.setFilledByUserId(loggedUser.getId());
            question.setFillOutDate(new Date());
            question.setFilledAutorizationPoolIndex(currentAutorizationPoolIndex);
            this.makePersistent(question, loggedUser.getId());
            getEntityManager().flush();
            getBean(IFormCaptureDAO.class).saveSignature(outstandingSurveyId, authRole, loggedUser);
            //se llenan leidos
            boolean readed = this.readSigned(
                    outstandingSurveyId,
                    currentAutorizationPoolIndex, 
                    loggedUser, 
                    comments, 
                    currentRecurrence
            );    
            if (!readed) {
                getLogger().error("could set the form as readed ... outstandingSurveyId : " + outstandingSurveyId);
                return false;
            }
            String comment = Utilities.getListAsText(comments);
            ISurveyCaptureDAO dao = getBean(ISurveyCaptureDAO.class);
            if (sign) {
                //firmar
                getLogger().debug(
                        "El usuario {} llena el pool {} para el request {}",
                        new Object[] {
                            loggedUser.getId(),
                            sol.getAutorizationPool(),
                            sol
                        }
                );
                final SurveyFieldObjectDTO surveyField = HQLT_findSimple(SurveyFieldObjectDTO.class, ""
                        + " SELECT new " + SurveyFieldObjectDTO.class.getCanonicalName() + "("
                            + " c.id "
                            + ",c.field_id "
                            + ",c.stage "
                            + ",c.attendProgressStateId "
                            + ",c.order"
                            + ",c.businessUnitMainField"
                            + ",c.businessUnitDepartmentMainField"
                            + ",c.areaMainField"
                            + ",c.allowDeleteStopwatchRecord"
                            + ",c.daysToExpire"
                            + ",c.daysToNotifyBeforeExpiration"
                            + ",c.defaultDateValue"
                            + ",c.defaultValue"
                            + ",c.includeTime"
                            + ",c.restrictPastDates"
                            + ",c.maxPastHours"
                            + ",c.answerType"
                            + ",c.weightingType"
                            + ",c.summationQuestion"
                            + ",c.selectedWeightedField"
                        + ")"
                        + " FROM " + SurveyFieldObject.class.getCanonicalName() + " c"
                        + " WHERE c.id = :surveyFieldObjId",
                        ImmutableMap.of(
                            "surveyFieldObjId", surveyFieldObjId
                        ),
                        true,
                        CacheRegion.SURVEY,
                        0
                );
                if (surveyField.getAttendProgressStateId() != null && surveyField.getAttendProgressStateId() > 0) {
                    HQL_updateByQuery(""
                        + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                        + " SET c.progressStateId = :progressStateId"
                        + " WHERE c.id = :outstandingSurveyId",
                        ImmutableMap.of(
                            "outstandingSurveyId", outstandingSurveyId,
                            "progressStateId", surveyField.getAttendProgressStateId()
                        )
                    );
                } else {
                    HQL_updateByQuery(""
                        + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                        + " SET c.progressStateId = null"
                        + " WHERE c.id = :outstandingSurveyId",
                        ImmutableMap.of(
                            "outstandingSurveyId", outstandingSurveyId
                        )
                    );                    
                }
                final String fieldStage = Utilities.getBean(IWorkflowDAO.class).getNextFillFormStage(
                        sol.getSurveyId(),
                        outstandingSurveyId,
                        sol.getId(),
                        SurveyRequestMode.parse(sol.getSurveyRequestMode()),
                        currentAutorizationPoolIndex,
                        sol.getConditionalValidatorCacheId()
                );
                if (fieldStage != null) {
                    HQL_updateByQuery(" "
                         + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                         + " SET c.stage = :stage"
                         + " WHERE c.id = :outstandingSurveyId",
                         ImmutableMap.of(
                             "outstandingSurveyId", outstandingSurveyId,
                             "stage", fieldStage
                         )
                     );
                }
                GenericSaveHandle auth = workflowDao.workflowRequestApprove(
                        getAspectJAutoProxy(),
                        sol, 
                        authRole,
                        comment, 
                        loggedUser.getId(), 
                        loggedUser, 
                        true, 
                        outstandingSurveyId, 
                        surveyFieldObjId, 
                        currentAutorizationPoolIndex
                );
                boolean r = (boolean)auth.getJsonEntityData().get("authorized");
                boolean isFinished = (boolean) auth.getJsonEntityData().get("requestClosed");                
                if (isFinished) {
                    dao.finishFillForm(sol, outstandingSurveyId, loggedUser);
                }
                return r;
            } else { // ToDo: validar si aun se utiliza esta parte de código 
                //rechazar  
                dao.cancellFillForm(sol.getId(), comment, outstandingSurveyId, true, loggedUser);
                return getAspectJAutoProxy().rejectFillForm(sol, comment, authRole, loggedUser);
            }
        } catch (Exception e) {
            getLogger().error("signTheForm ... outstandingSurveyId : " + outstandingSurveyId, e);
            return false;
        }
    }
    
    private List<AuthorizeSectionDTO> loadSectionsToAuthorize(
            final Long outstandingSurveyId,
            final Long currentAutorizationPoolIndex
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("outstandingSurveyId", outstandingSurveyId);
        params.put("currentAutorizationPoolIndex", currentAutorizationPoolIndex.intValue());
        final String HQL = ""
            + " SELECT new " + AuthorizeSectionDTO.class.getCanonicalName() + "("
                    + " c.fieldObjectId as fieldObjectId "
                    + ", d.id as autorizationPoolDetailId "
                    + ", d.indice as fieldOrder "
            + " )"
            + " FROM " + OutstandingSurveysAttendantLoad.class.getCanonicalName() + " c "
            + " JOIN c.outstandingSurvey o  "
            + " JOIN " + SurveyField.class.getCanonicalName() + " field "
                + " ON field.fieldObjectId = c.fieldObjectId"
            + " JOIN field.obj fieldObj"
            + " JOIN " + AutorizationPoolDetails.class.getCanonicalName() + " d "
                + " ON d.finishDate is not null "
                + " AND o.requestId = d.requestId "
                + " AND c.workflowIndex = d.indice "
            + " WHERE "
                + " o.id = :outstandingSurveyId"
                + " AND d.accepted is not null " 
                + " AND d.indice <= :currentAutorizationPoolIndex"
                + " AND c.fieldType = 'seccion' " // <-- signature??
            + " GROUP BY "
                    + " c.fieldObjectId "
                    + ", d.id "
                    + ", d.indice "
            + " ORDER BY d.indice DESC "
        ;
        final List<AuthorizeSectionDTO> result = HQL_findByQuery(HQL, params);
        return result;
    }
    
    @Override 
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean readSigned(
            final Long outstandingSurveyId,
            final Long currentAutorizationPoolIndex,
            final ILoggedUser loggedUser,
            final List<FieldCommentDTO> comments,
            final Integer currentRecurrence
    ) throws QMSException {
        // ToDo filtrar personas que no respondieron el formulario y que salen a partir de este query
        final String loggedUserName = loggedUser.getDescription();
        final List<AuthorizeSectionDTO> sectionsToAuthorize = loadSectionsToAuthorize(
                outstandingSurveyId, 
                currentAutorizationPoolIndex
        );
        boolean success = !sectionsToAuthorize.isEmpty();
        if (!success) {
            return false;
        }
        final Map<Long, String> dataComment = new HashMap<>();
        for (final FieldCommentDTO comment : comments) {
            dataComment.put(
                comment.getFieldObjId(), 
                comment.getComment()
            );
        }
        for (final AuthorizeSectionDTO section : sectionsToAuthorize) {
            final Long readedFieldSectionId = HQL_findSimpleLong(""
                    + " SELECT c.id"
                    + " FROM " + SurveyField.class.getCanonicalName() + " c"
                    + " WHERE c.obj.id = :id", "id", section.getFieldObjectId());
            success = success && generateAuthHistory(
                    section.getAutorizationPoolDetailId(),
                    readedFieldSectionId,
                    loggedUser,
                    loggedUserName,
                    null, 
                    dataComment.get(readedFieldSectionId), 
                    AutorizationPoolDetailRead.Status.IS_READED.parseInt(),
                    currentRecurrence
            );
        }
        final List<Long> anybodySections = loadAnybodySections(outstandingSurveyId);
        for (final Long sectionId : anybodySections) {
            success = success && generateNobodyHistory(
                    outstandingSurveyId,
                    currentRecurrence,
                    sectionId, 
                    OutstandingSurveysAnybodyRead.STATUS.IS_READED,
                    dataComment,
                    loggedUser
            );
        }
        return success;
    }
    
    private List<Long> loadAnybodySections(final Long outstandingSurveyId) { 
        final List<Long> sectionsToAuthorize = HQL_findByQuery(""
            + " SELECT obj.id as fieldObjectId "
            + " FROM " + OutstandingSurveysRef.class.getCanonicalName() + " outs "
            + " , " + Survey.class.getCanonicalName() + " sur"
            + " JOIN sur.fields field"
            + " JOIN field.obj obj"
            + " WHERE sur.id = outs.surveyId"
            + " AND outs.id = :outstandingSurveyId "
            + " AND field.type = '" + SurveyField.TYPE_SECCION + "' "
            + " AND obj.fillEntity = '" + SurveyFillEntity.NOBODY + "'",
                "outstandingSurveyId", outstandingSurveyId
        );
        return sectionsToAuthorize;
    }

    private boolean generateAuthHistory(
            final Long autorizationPoolDetailId, 
            final Long fieldSectionId, 
            final ILoggedUser loggedUser, 
            final String loggedUserName, 
            final Long autorizationPoolId,
            String description, 
            final Integer status,
            final Integer currentRecurrence
    ) {
        if (description == null) {
            description = "";
        }
        // Rescatar el id correspondiente al comentario realizado
        final Long autorizationPoolDetailReadId = HQL_findSimpleLong(""
                + " SELECT max(c.id)"
                + " FROM " + AutorizationPoolDetailRead.class.getCanonicalName() + " c "
                + " WHERE c.autorizationPoolDetail.id = :autorizationPoolDetailId "
                + " AND c.recurrence = :currentRecurrence "
                + " AND c.readedFieldSectionId = :fieldSectionId "
                + " AND c.userId = :loggedUserId",
                ImmutableMap.of(
                    "autorizationPoolDetailId", autorizationPoolDetailId,
                    "currentRecurrence", currentRecurrence,
                    "fieldSectionId", fieldSectionId,
                    "loggedUserId", loggedUser.getId()
                )
        );
        // Se busca el objeto existente o se genera uno nuevo
        if (autorizationPoolDetailReadId > 0L) {
            StringBuilder query = new StringBuilder();
            Map params = ImmutableMap.of(
                "autorizationPoolDetailReadId", autorizationPoolDetailReadId,
                "status", status,
                "description", description
            );
            query.append(""
                + " UPDATE ").append(AutorizationPoolDetailRead.class.getCanonicalName()).append(" c "
                + " SET "
                    + " description = :description"
                    + ",status = :status"
                    + ",lastStatusChange = current_date() "
            );
            if (autorizationPoolId != null) {
                query.append(", autorizationPoolId = :autorizationPoolId");
                params = ImmutableMap.of(
                    "autorizationPoolId", autorizationPoolId,
                    "status", status,
                    "autorizationPoolDetailReadId", autorizationPoolDetailReadId,
                    "description", description
                );
            }
            query.append(" WHERE id = :autorizationPoolDetailReadId");
            return HQL_updateByQuery(query.toString(), params) > 0;
        } else {
            // Nuevo
            AutorizationPoolDetailRead authHistory = new AutorizationPoolDetailRead(-1L);
            authHistory.setAutorizationPoolDetail(new AutorizationPoolDetails(autorizationPoolDetailId));
            authHistory.setReadedFieldSectionId(fieldSectionId);
            if (autorizationPoolId != null) {
                authHistory.setAutorizationPoolId(autorizationPoolId);
            }
            try {
                authHistory.setCode(getCodeSequence().next(CodeSequence.type.CODE_AUTORIZATION_POOL_DETAIL_READ_TYPE));
            } catch (final QMSException ex) {
                throw new RuntimeException(ex);
            }
            authHistory.setDeleted(AutorizationPoolDetailRead.IS_NOT_DELETED);
            authHistory.setDescription(description);
            authHistory.setStatus(status);
            authHistory.setLastStatusChange(new Date());
            authHistory.setRecurrence(currentRecurrence);
            authHistory.setUserId(loggedUser.getId());
            authHistory.setUserName(loggedUserName);
            return makePersistent(authHistory, loggedUser.getId()) != null;
        }
    }
    
    private boolean generateNobodyHistory(
            final Long outstandingSurveysId,
            final Integer currentRecurrence,
            final Long fieldObjectId, 
            final OutstandingSurveysAnybodyRead.STATUS status,
            final Map<Long, String> dataComment,
            final ILoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("outstandingSurveysId", outstandingSurveysId);
        params.put("currentRecurrence", currentRecurrence);
        params.put("fieldObjectId", fieldObjectId);
        final OutstandingSurveysAnybodyRead history = (OutstandingSurveysAnybodyRead) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + OutstandingSurveysAnybodyRead.class.getCanonicalName() + " c "
                + " WHERE c.outstandingSurveysId  = :outstandingSurveysId"
                + " AND c.currentRecurrence = :currentRecurrence"
                + " AND c.fieldObjectId = :fieldObjectId",
                params
        );
        final String description = dataComment.get(fieldObjectId);
        if(history != null) {
            history.setDescription(description);
            history.setStatus(status.parseInt());
            return makePersistent(history, loggedUser.getId()) != null;
        } else {
            final OutstandingSurveysAnybodyRead newHistory = new OutstandingSurveysAnybodyRead(-1L);
            newHistory.setOutstandingSurveysId(outstandingSurveysId);
            newHistory.setFieldObjectId(fieldObjectId);
            newHistory.setCurrentRecurrence(currentRecurrence);
            try {
                newHistory.setCode(getCodeSequence().next(CodeSequence.type.CODE_OUTSTANDING_SURVEYS_ANYBODY_READ));
            } catch (final QMSException ex) {
                throw new RuntimeException(ex);
            }
            newHistory.setDeleted(AutorizationPoolDetailRead.IS_NOT_DELETED);
            newHistory.setDescription(description);
            newHistory.setStatus(status.parseInt());
            return makePersistent(newHistory, loggedUser.getId()) != null;
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public List<ReadableFieldDTO> readableFields(
            final Long outstandingSurveyId, 
            final Long currentAutorizationPoolIndex,
            final Integer currentRecurrence
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("outstandingSurveyId", outstandingSurveyId);        
        String HQL = ""
            + " SELECT "
                + " new " + ReadableFieldDTO.class.getCanonicalName() + " ("
                    + " c.fieldObjectId as fieldObjectId "
                    + ", field.id as fieldId"
                    + ", MAX(d.finishDate) as finishDate "
                    + ", MAX(d.currentRecurrence) as currentRecurrence "
                    + ", MAX(d.id) as autorizationPoolDetailId "
                    + ", fieldObj.order as fieldOrder "
                + " ) "
            + " FROM "  + OutstandingSurveysAttendantLoad.class.getCanonicalName() + " c "
            + " JOIN c.outstandingSurvey o"
            + " JOIN " + SurveyField.class.getCanonicalName() + " field "
            + " ON field.fieldObjectId = c.fieldObjectId"
            + " JOIN field.obj fieldObj"
            + " JOIN " + AutorizationPoolDetails.class.getCanonicalName() + " d "
                + " ON o.requestId = d.requestId "
                + " AND c.workflowIndex = d.indice "
            + " WHERE "
                + " o.id = :outstandingSurveyId"
                + " AND d.finishDate is not null "
                + " AND d.accepted is not null "
                + " AND d.accepted != " + AutorizationPoolDetails.SKIPPED
                + " AND c.fieldType = 'seccion' "
        ;
        if(currentAutorizationPoolIndex != null) {
            params.put("currentAutorizationPoolIndex", currentAutorizationPoolIndex);
            HQL += " AND c.workflowIndex <= :currentAutorizationPoolIndex";
        }
        HQL += " GROUP BY"
                + " c.fieldObjectId"
                    + ", field.id "
                    + ", fieldObj.order"
               + " ORDER BY fieldObj.order";
        final List<ReadableFieldDTO> fields = HQL_findByQuery(HQL, params);
        for (final ReadableFieldDTO field : fields) {
            final Map<String, Object> fieldParams = new HashMap<>();
            fieldParams.put("autorizationPoolDetailId", field.getAutorizationPoolDetailId());
            fieldParams.put("fieldObId", field.getFieldObjectId());
            final List<AutorizationDetailReadDTO> readList = HQL_findByQuery(""
                + " SELECT"
                    + " new " + AutorizationDetailReadDTO.class.getCanonicalName() + " ("
                        + "c.description as description"
                        + ", c.status as status"
                        + ", c.userId as userId"
                    + " ) "
                + " FROM " + AutorizationPoolDetailRead.class.getCanonicalName() + " c"
                + " , " + SurveyField.class.getCanonicalName() + " field"
                + " JOIN field.obj fieldObj"  
                + " WHERE c.readedFieldSectionId = field.id"
                + " AND c.autorizationPoolDetail.id = :autorizationPoolDetailId"
                + " AND fieldObj.id = :fieldObId",
                 fieldParams
            );
            field.setReadDetails(readList);
            field.setCanReject(1);
        }
        loadAnybodyReadableFields(outstandingSurveyId, currentRecurrence, fields);
        return fields;
    }

    private void loadAnybodyReadableFields(
            final Long outstandingSurveyId, 
            final Integer currentRecurrence,
            final List<ReadableFieldDTO> fields) {
        final List<ReadableFieldDTO> anybodyFields = HQL_findByQuery(""
            + " SELECT "
                + " new " + ReadableFieldDTO.class.getCanonicalName() + " ("
                    + " obj.id as fieldObjectId "
                + " ) "
            + " FROM " + OutstandingSurveysRef.class.getCanonicalName() + " outs "
            + " , " + Survey.class.getCanonicalName() + " sur"
            + " JOIN sur.fields field"
            + " JOIN field.obj obj"
            + " WHERE sur.id = outs.surveyId"
            + " AND outs.id = :outstandingSurveyId"
            + " AND field.type = '" + SurveyField.TYPE_SECCION + "' "
            + " AND obj.fillEntity = '" + SurveyFillEntity.NOBODY + "'",
            "outstandingSurveyId", outstandingSurveyId
        );
        for (final ReadableFieldDTO field : anybodyFields) {
            final Map<String, Object> params = new HashMap<>();
            params.put("fieldObjectId", field.getFieldObjectId());
            params.put("outstandingSurveysId", outstandingSurveyId);
            params.put("currentRecurrence", currentRecurrence);
            final List<AutorizationDetailReadDTO> readList = HQL_findByQuery(""
                + " SELECT"
                    + " new " + AutorizationDetailReadDTO.class.getCanonicalName() + " ("
                        + " c.description as description"
                        + ", c.status as status"
                        + ", c.createdBy as userId"
                    + " ) "
                + " FROM " + OutstandingSurveysAnybodyRead.class.getCanonicalName() + " c "
                + " WHERE c.fieldObjectId  = :fieldObjectId"
                + " AND c.outstandingSurveysId = :outstandingSurveysId"
                + " AND c.currentRecurrence = :currentRecurrence"
                + " ORDER BY c.createdDate DESC",
                params
            );
            field.setReadDetails(readList);
            field.setCanReject(0);
        }
        fields.addAll(anybodyFields);
    }
    
    @Override 
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public List<Long> cancelableFillIndexes(Long outstandingSurveyId) {
        if (outstandingSurveyId == null) {
            return ImmutableList.of(1l);
        }
        List<Long> items = HQL_findByQuery(""
                + " SELECT DISTINCT c.fillAutorizationPoolIndex"
                + " FROM " 
                    + OutstandingSurveysAttendant.class.getCanonicalName() + " c "
                    + "," + AutorizationPoolDetails.class.getCanonicalName() + " d "
                    + "," + SurveyFieldObject.class.getCanonicalName() + " obj "
                + " WHERE "
                    + " c.outstandingSurvey.requestId = d.requestId"
                    + " AND c.fieldObjectId = obj.id"
                    + " AND obj.canCancel = 't'"
                    + " AND c.fillAutorizationPoolIndex = d.indice "
                    + " AND c.outstandingSurvey.id = :outstandingSurveyId"
                    + " AND c.fieldType in ('" + SurveyField.TYPE.SECTION.getValue() + "', '" + SurveyField.TYPE.SIGNATURE.getValue() + "') ",
                ImmutableMap.of("outstandingSurveyId", outstandingSurveyId)
        );
        if (items.isEmpty() || !items.contains(1L)) {
            items.add(1L);
        }
        return items;
    }
    

    private Map<Long, String> getDataCommentsByLists(List<FieldCommentDTO> acceptedCommentlist, List<FieldCommentDTO> rejectionCommentList) throws NumberFormatException {
        Map <Long, String> dataComments = new HashMap<> ();
        for (FieldCommentDTO map : acceptedCommentlist) {
            dataComments.put(map.getFieldObjId(), map.getComment());
        }
        for (FieldCommentDTO map : rejectionCommentList) {
            dataComments.put(map.getFieldObjId(), map.getComment());
            break; // Solo se toma el primer Commentario de rechazo
        }
        return dataComments;
    }
    
    /**
     *
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    @Override
    public GenericSaveHandle rejectFilledSectionAndCreateCopy(
            Long outstandingSurveyId, 
            Long invalidAutorizationPoolIndex, 
            Long currentAutorizationPoolIndex,
            Long currentAutorizationPoolDetailId,
            Integer currentRecurrence,
            List<Long> readedFieldSectionsId, 
            Long rejectedFieldSeccionId, 
            Long rejectedFieldObjSeccionId, 
            ArrayList<FieldCommentDTO> rejectionCommentList,
            ArrayList<FieldCommentDTO> acceptedCommentlist,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        // ToDo: Validar que la sección NO requiera autorización o que ya se haya autorizado `FormUtil.isSignatureRejectionApprovalRequired`
        final IRequestDAO dao = getBean(IRequestDAO.class);
        final Map <Long, String> dataComments = getDataCommentsByLists(acceptedCommentlist, rejectionCommentList);
        final String comment;
        
        comment = Streams.mapWithIndex(rejectionCommentList.stream(), (c, index) -> {
            return "\r\n*" + (index + 1) + ". " + c.getFieldObjText() + "*: " + c.getComment();
        }).reduce("", (a, b) -> a + b);
        
        final GenericSaveHandle gsh = rejectFilledSection(
                outstandingSurveyId, 
                invalidAutorizationPoolIndex,
                currentAutorizationPoolIndex,
                currentAutorizationPoolDetailId,
                currentRecurrence,
                readedFieldSectionsId, 
                rejectedFieldSeccionId,
                rejectedFieldObjSeccionId,
                comment, 
                dataComments, 
                loggedUser
        );

        if (gsh.getOperationEstatus().equals(1)) {
            // Se guarda el comentario de rechazo y se libera la solicitud
            acceptedCommentlist.addAll(rejectionCommentList);
            ISurveyFieldHistoryDAO daoHistoryComment = getBean(ISurveyFieldHistoryDAO.class);
            GenericSaveHandle commentGsh = daoHistoryComment.save(acceptedCommentlist, loggedUser, outstandingSurveyId);
            dao.setToNotBusy((Long) commentGsh.getJsonEntityData().get("requestId"));
            // Se genera copia del formulario
            OutstandingSurveys original = daoHistoryComment.HQLT_findById(OutstandingSurveys.class, outstandingSurveyId);
            original.setEstatus(OutstandingSurveys.ESTATUS_RECHAZADO);
            GenericSaveHandle copyGsh = getBean(ISurveyCaptureDAO.class).saveRequestCopy(original, loggedUser);
            final Map<String, Object> params = new HashMap<>();
            params.put("rejectedOutstandingSurveysId", copyGsh.getPersistentId());
            params.put("rejectedCommentId", gsh.getJsonEntityData().get("rejectedCommentId"));
            // Se guarda el ID del nuevo llenado en el comentario de rechazo
            daoHistoryComment.HQL_updateByQuery(""
                + " UPDATE " + AutorizationPoolDetailComment.class.getCanonicalName() + " c "
                + " SET c.rejectedOutstandingSurveysId = :rejectedOutstandingSurveysId"
                + " WHERE c.id = :rejectedCommentId",
                params
            );
        }
        return gsh;
    }
    /**
     * 
     * @param invalidAutorizationPoolIndex      : Es el "indice" que se esta rechazando
     * @param readedFieldSectionsId             : field_id[]
     * @param rejectedFieldSeccionId            : field_id
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    @Override 
    public GenericSaveHandle rejectFilledSection(
            Long outstandingSurveyId, 
            Long invalidAutorizationPoolIndex, 
            Long currentAutorizationPoolIndex,
            Long currentAutorizationPoolDetailId,
            Integer currentRecurrence,
            List<Long> readedFieldSectionsId, 
            Long rejectedFieldSeccionId, 
            Long rejectedFieldObjSeccionId, 
            String rejectionComment,
            Map <Long, String> dataComments,
            ILoggedUser loggedUser
    ) {
        GenericSaveHandle result = new GenericSaveHandle(1);
        saveRejectAuthorizationHistory(
            readedFieldSectionsId, 
            outstandingSurveyId, 
            loggedUser, 
            dataComments, 
            currentAutorizationPoolDetailId, 
            invalidAutorizationPoolIndex, 
            rejectedFieldSeccionId,
            currentRecurrence
        );
        HQL_updateByQuery(""
            + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c"
            + " SET "
                + " c.description = :description"
                + ",c.accepted = null "
                + ",c.userId = :userId "
                + ",c.status = " + AutorizationPoolDetails.STATUS.INACTIVE.getValue()
                + ",c.finishDate = current_date() "
                + ",c.modificationDate = current_date()"
            + " WHERE"
                + " c.id = :currentAutorizationPoolDetailId "
            ,
            ImmutableMap.of(
                "description", rejectionComment,
                "userId", loggedUser.getId(),
                "currentAutorizationPoolDetailId", currentAutorizationPoolDetailId
            )
        );
        Long requestId = HQL_findSimpleLong(""
                + " SELECT c.requestId"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c"
                + " WHERE c.id = :outstandingSurveyId", "outstandingSurveyId", outstandingSurveyId);
        Map<String, Object> params = new HashMap<>(2);
        params.put("id", requestId);
        params.put("status", WorkflowRequestStatus.RETURNED.getValue());
        HQL_updateByQuery(""
                + " UPDATE " + Request.class.getCanonicalName() + " c"
                + " SET c.status = :status "
                + " WHERE c.id = :id", params);
        final String fieldStage = HQL_findSimpleString(""
                + " SELECT c.stage"
                + " FROM " + SurveyFieldObject.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", rejectedFieldObjSeccionId),
                true,
                CacheRegion.SURVEY,
                0
        );
        if (fieldStage != null) {
            HQL_updateByQuery(""
                    + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                    + " SET c.stage = :stage"
                    + " WHERE c.id = :outstandingSurveyId",
                    ImmutableMap.of(
                        "outstandingSurveyId", outstandingSurveyId,
                        "stage", fieldStage
                    )
                );
        }
        //se guarda historico de recurrencia y se reenvia el pendiente
        List<CommentHistoryDTO> details = HQL_findByQuery(""
            + " SELECT"
                + " new " + CommentHistoryDTO.class.getCanonicalName() + "("
                    + " c "
                    + ", d.id"
                    + ", d.userId"
                    + ", d.indice"
                    + ", d.currentRecurrence"
                    + ", d.accepted" 
                    + ", d.description"
                + " )"
            + " FROM " 
                + OutstandingSurveysAttendant.class.getCanonicalName() + " c "
                + "," + AutorizationPoolDetails.class.getCanonicalName() + " d "
            + " WHERE "
                + " c.outstandingSurvey.requestId = d.requestId"
                + " AND c.fillAutorizationPoolIndex = d.indice "
                + " AND d.userId is not null "
                + " AND c.fieldType in ('seccion', 'signature') "
                + " AND c.outstandingSurvey.id = :outstandingSurveyId"
                + " AND d.indice >= :invalidAutorizationPoolIndex "
            + " ORDER BY d.indice DESC ",
            ImmutableMap.of(
                "outstandingSurveyId", outstandingSurveyId,
                "invalidAutorizationPoolIndex", invalidAutorizationPoolIndex.intValue()
            )
        );
        for (CommentHistoryDTO detail : details) {
            Long autorizationPoolDetailsId = detail.getAutorizationPoolDetailsId();
            Integer 
                autorizationPoolDetailIndex = detail.getAutorizationPoolDetailIndex(),
                accepted = detail.getAccepted(),
                nextRecurrence = detail.getCurrentRecurrence() + 1
            ;
            if (autorizationPoolDetailIndex <= currentAutorizationPoolIndex.intValue()) {
                HQL_updateByQuery(""
                    + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c "
                    + " SET c.currentRecurrence = :nextRecurrence "
                    + " , c.userId = null"
                    + " WHERE c.id = :autorizationPoolDetailsId ",
                    ImmutableMap.of(
                        "nextRecurrence", nextRecurrence,
                        "autorizationPoolDetailsId", autorizationPoolDetailsId
                    )
                );
            }
            /**
             * Permite a los usuarios que marcados en el pool
             * como SKIPPED que sean contemplados nuevamente debido 
             * a que hubo una cancelación del llenado
             * */
            if (TO_ACCEPT_STATUSES.contains(accepted)) {
                accepted = AutorizationPoolDetails.TO_ACCEPT; // <-- La constante `TO_ACCEPT` vale NULL
            }
            if(autorizationPoolDetailIndex == invalidAutorizationPoolIndex.intValue()) {
                HQL_updateByQuery(""
                    + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c"
                    + " SET "
                            + " c.status = " + AutorizationPoolDetails.STATUS.ACTIVE.getValue() 
                            + ",c.finishDate = current_date()"
                            + ",c.accepted = " + accepted // <-- Vale NULL!, no parametrizar en el MAP
                    + " WHERE c.id = :autorizationPoolDetailsId ",
                    ImmutableMap.of(
                        /* No parametrizar `accepted` */
                        "autorizationPoolDetailsId", autorizationPoolDetailsId
                    )
                );
            } else {
                HQL_updateByQuery(""
                    + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c "
                    + " SET "
                        + " c.status = " + AutorizationPoolDetails.STATUS.INACTIVE.getValue() 
                        + ",c.accepted = " + accepted // <-- Vale NULL!, no parametrizar en el MAP
                    + " WHERE c.id = :autorizationPoolDetailsId ",
                    ImmutableMap.of(
                        /* No parametrizar `accepted` */
                        "autorizationPoolDetailsId", autorizationPoolDetailsId
                    )
                );
            }
            //Se llena el histórial de comentarios
            fillHistory(
                outstandingSurveyId, 
                rejectionComment, 
                rejectedFieldObjSeccionId, 
                invalidAutorizationPoolIndex, 
                currentAutorizationPoolIndex, 
                detail, 
                result,
                loggedUser
            );
        }
        Long autorizationPoolId = HQL_findSimpleLong(""
            + " SELECT pool.id "
            + " FROM " + AutorizationPoolDetails.class.getCanonicalName() + " c "
            + " JOIN c.autorizationPool pool "
            + " WHERE c.id = :currentAutorizationPoolDetailId ", "currentAutorizationPoolDetailId", currentAutorizationPoolDetailId
        );
        //Resetea los pendientes posteriores (index) al que se rechaza
        HQL_updateByQuery(""
            + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c "
            + " SET c.status = " + AutorizationPoolDetails.STATUS.INACTIVE.getValue() + ", c.accepted = null"
            + " WHERE "
                + " c.autorizationPool.id = :autorizationPoolId "
                + " AND c.indice > :invalidAutorizationPoolIndex",
                ImmutableMap.of(
                    "autorizationPoolId", autorizationPoolId,
                    "invalidAutorizationPoolIndex", invalidAutorizationPoolIndex.intValue()
                )
        );
        /*
         Los siniestros retornados NUNCA deben expirar.
         */
        return result;
    }
    
    private void fillHistory(
            Long outstandingSurveyId,
            String rejectionComment,
            Long rejectedFieldObjSeccionId,
            Long invalidAutorizationPoolIndex,
            Long currentAutorizationPoolIndex,
            CommentHistoryDTO detail,
            GenericSaveHandle result, 
            ILoggedUser loggedUser
    ) {
        OutstandingSurveysAttendant attendant = detail.getAttendant();
        AutorizationPoolDetailComment comment = new AutorizationPoolDetailComment();
        comment.setId(-1L);
        comment.setRecurrence(detail.getCurrentRecurrence());
        comment.setAutorizationPoolDetailsId(detail.getAutorizationPoolDetailsId());
        comment.setDeleted(AutorizationPoolDetailComment.IS_NOT_DELETED);
        comment.setStatus(AutorizationPoolDetailComment.ACTIVE_STATUS);
        comment.setAuthorizationUserName(
            UserUtil.getUserName(detail.getUserId(), this)
        );
        try {
            comment.setCode(
                    getCodeSequence().next(CodeSequence.type.CODE_AUTORIZATION_POOL_COMMENT_TYPE)
            );
        } catch (final QMSException ex) {
            throw new RuntimeException(ex);
        }
        // comentario de autorización
        if(detail.getAutorizationPoolDetailIndex().equals(currentAutorizationPoolIndex.intValue())) {
            comment.setDescription("Rechazo");
        } else if (SurveyField.TYPE_SIGNATURE.equals(attendant.getFieldType())) {
            comment.setDescription("Firmado");
        } else {
            comment.setDescription("Llenado");
        }
        //comentario de rechazo
        comment.setRejectionComment(rejectionComment);
        comment.setRejectionUserId(loggedUser.getId());
        comment.setRejectionUserName(loggedUser.getDescription());
        if (Objects.equals(attendant.getFieldObjectId(), rejectedFieldObjSeccionId)) {
            getEntityManager().flush();
            final AutorizationPoolDetailComment resultComment = getAspectJAutoProxy().rejectFilledSection(
                outstandingSurveyId,
                detail.getAutorizationPoolDetailsId(), 
                comment, 
                invalidAutorizationPoolIndex,
                currentAutorizationPoolIndex,
                loggedUser
            );
            if (getLogger().isDebugEnabled()) {
                getLogger().debug("Rejected comment: {}", resultComment);
            }
            result.getJsonEntityData().put("rejectedCommentId", comment.getId());
        } else {
            makePersistent(comment, loggedUser.getId());
        }
    }
    
    /**
     *
     */
    private boolean saveRejectAuthorizationHistory(
            final List<Long> readedFieldSectionsId, 
            final Long outstandingSurveyId, 
            final ILoggedUser loggedUser, 
            final Map<Long, String> dataComments,
            final Long currentAutorizationPoolDetailId, 
            final Long invalidAutorizationPoolIndex,
            final Long rejectedFieldSeccionId,
            final Integer currentRecurrence
    ) {
        //se guardan historico de leidos y rechazados actuales
        final Long autorizationPoolId = HQL_findSimpleLong(""
                + " SELECT c.autorizationPool.id "
                + " FROM " + AutorizationPoolDetails.class.getCanonicalName() + " c "
                + " WHERE c.id = :currentAutorizationPoolDetailId ", "currentAutorizationPoolDetailId", currentAutorizationPoolDetailId
        );
        for (final Long readedFieldSectionId : readedFieldSectionsId) {
            final boolean success = readSection(
                    readedFieldSectionId,
                    outstandingSurveyId,
                    loggedUser,
                    autorizationPoolId,
                    dataComments,
                    currentRecurrence
            );
            if (!success) {
                return false;
            }
        }
        return rejectSection(    
                invalidAutorizationPoolIndex,
                rejectedFieldSeccionId,
                currentRecurrence,
                autorizationPoolId, 
                dataComments, 
                loggedUser
        );
    }
    
    private boolean readSection(
            final Long readedFieldSectionId,
            final Long outstandingSurveyId,
            final ILoggedUser loggedUser,
            final Long autorizationPoolId,
            final Map<Long, String> dataComments,
            final Integer currentRecurrence
    ) {
        final Long autorizationPoolDetailId = getCurrentAutorizationPoolRecurrence(
                readedFieldSectionId, 
                outstandingSurveyId
        );  
        if (autorizationPoolDetailId == 0) {
            final boolean success = generateNobodyHistory(
                    outstandingSurveyId,
                    currentRecurrence,
                    readedFieldSectionId, 
                    OutstandingSurveysAnybodyRead.STATUS.IS_READED,
                    dataComments,
                    loggedUser
            );
            return success;
        } else {
            final boolean success = generateAuthHistory(
                    autorizationPoolDetailId, 
                    readedFieldSectionId, 
                    loggedUser, 
                    loggedUser.getDescription(), 
                    autorizationPoolId,
                    dataComments.get(readedFieldSectionId), 
                    AutorizationPoolDetailRead.Status.IS_READED.parseInt(),
                    currentRecurrence
            );  
            return success;
        }
    }
    
    private boolean rejectSection(
            final Long invalidAutorizationPoolIndex,
            final Long rejectedFieldSeccionId,
            final Integer currentRecurrence, 
            final Long autorizationPoolId,
            final Map<Long, String> dataComments, 
            final ILoggedUser loggedUser
    ) {
        final Long autorizationPoolDetailId = getCurrentAutorizationPoolRecurrence(
                invalidAutorizationPoolIndex.intValue(), autorizationPoolId
        );
        if (autorizationPoolDetailId == 0) {
            getLogger().error("You can not reject a section without a autorizationPoolDetailId of the field {}", rejectedFieldSeccionId);
            return false;
        } else {
            final boolean success = generateAuthHistory(
                    autorizationPoolDetailId, 
                    rejectedFieldSeccionId, 
                    loggedUser, 
                    loggedUser.getDescription(), 
                    autorizationPoolId,
                    dataComments.get(rejectedFieldSeccionId), 
                    AutorizationPoolDetailRead.Status.IS_REJECTED.parseInt(),
                    currentRecurrence
            );
            return success;
        }
    }

    @Override
    @OnRejectFilledSection
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public AutorizationPoolDetailComment rejectFilledSection(
        Long outstandingSurveyId, 
        Long autorizationPoolDetailsId, 
        AutorizationPoolDetailComment comment, 
        Long invalidAutorizationPoolIndex, 
        Long currentAutorizationPoolIndex, 
        ILoggedUser loggedUser
    ) {
        comment.setDescription(comment.getDescription() + getTag("rejectedReason"));
        comment.setInvalidAutorizationPoolIndex(invalidAutorizationPoolIndex.intValue());
        comment.setAutorizationPoolIndex(currentAutorizationPoolIndex.intValue());
        return makePersistent(comment, loggedUser.getId());
    }
    
    private Long getCurrentAutorizationPoolRecurrence(Integer index, Long autorizationPoolId) {
        return HQL_findSimpleLong(""
            + " SELECT max(c.id) as autorizationPoolDetailId "
            + " FROM " + AutorizationPoolDetails.class.getCanonicalName() + " c "
            + " WHERE"
            + " c.autorizationPool.id = :autorizationPoolId "
            + " AND c.indice = :index ",
            ImmutableMap.of(
                "autorizationPoolId", autorizationPoolId,
                "index", index
            )
        );
    }
    
    private Long getCurrentAutorizationPoolRecurrence(
            final Long readedFieldSectionId,
            final Long outstandingSurveyId
    ) {
        return HQL_findSimpleLong(""
            + " SELECT max(d.id) as autorizationPoolDetailId"
            + " FROM " 
                + OutstandingSurveysAttendant.class.getCanonicalName() + " c "
                + "," + AutorizationPoolDetails.class.getCanonicalName() + " d "
                + "," + SurveyField.class.getCanonicalName() + " field "
            + " WHERE "
                + " c.outstandingSurvey.requestId = d.requestId"
                + " AND c.fillAutorizationPoolIndex = d.indice "
                + " AND c.fieldObjectId = field.obj.id"
                + " AND field.id = :readedFieldSectionId "
                + " AND c.outstandingSurvey.id = :outstandingSurveyId ",
                ImmutableMap.of(
                    "readedFieldSectionId", readedFieldSectionId,
                    "outstandingSurveyId", outstandingSurveyId
                )
        );
    }

    @Override
    @OnRejectedVerification
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean rejectVerification(Long requestId, Long docId, String reason, ILoggedUser loggedUser) {
        Request r = HQLT_findById(requestId);
        if (r.getType().equals(Request.NEW) && r.getSurveyId() != null) {
            HQL_updateByQuery(""
                + " UPDATE " + Survey.class.getCanonicalName() + " c"
                + " SET c.estatus = " + Survey.ESTATUS_INACTIVO
                + " WHERE c.request.id = :requestId" , "requestId", requestId
            );
        }
        return cancelRequest(requestId, reason, loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean cancelRequest(Long requestId, String reason, ILoggedUser loggedUser) {
          HQL_updateByQuery(""
                 + " UPDATE " + AutorizationPool.class.getCanonicalName() + " c"
                 + " SET c.status = " + AutorizationPool.STATUS_CANCELLED
                 + " WHERE c.request.id = :requestId" , "requestId", requestId
          );
          return HQL_updateByQuery(""
                + " UPDATE " + Request.class.getCanonicalName()
                + " SET "
                    + " status = " + WorkflowRequestStatus.CANCELED.getValue()
                    + " ,reazon = reazon || '\n' || :reason "
                    + " ,rejectionDate = CURRENT_DATE() "
                    + " ,rejectedByUserId =  :loggedUserId "
                + " WHERE id = :requestId ",
                ImmutableMap.of(
                    "reason", reason,
                    "loggedUserId", loggedUser.getId(),
                    "requestId", requestId
                )
        ) == 1;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean cancelSurveyRequest(Long requestId, String reason, ILoggedUser loggedUser) {
        final FormHelper formHelper = new FormHelper(Utilities.getUntypedDAO());
        final FillFormDTO fillForm = formHelper.loadFillFormDTO(requestId);
        final Map<String, Object> params = new HashMap<>(3);
        params.put("reason", reason.replaceAll("\\<.*?\\>", ""));
        params.put("requestId", requestId);
        params.put("currentAutorizationPoolIndex", fillForm.getCurrentAutorizationPoolIndex());
        HQL_updateByQuery(""
            + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c"
            + " SET c.accepted = " + AutorizationPoolDetails.REJECTED + ","
                + " c.description = :reason,"
                + " c.modificationDate = current_date(),"
                + " c.finishDate = current_date()"
            + " WHERE c.request.id = :requestId"
            + " AND c.indice = :currentAutorizationPoolIndex ",
            params
        );
        return cancelRequest(requestId, reason, loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean cancelRequests(List<Long> requestIds, String reason, ILoggedUser loggedUser) {
        HQL_updateByQuery(""
            + " UPDATE " + AutorizationPool.class.getCanonicalName() + " c"
            + " SET c.status = " + AutorizationPool.STATUS_CANCELLED
            + " WHERE " + BindUtil.parseFilterList("c.request.id ", requestIds)
        );
        HQL_updateByQuery(""
            + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c"
            + " SET c.accepted = " + AutorizationPoolDetails.REJECTED + ","
            + " c.description = :reason,"
            + " c.modificationDate = current_date(),"
            + " c.finishDate = current_date()"
            + " WHERE c.accepted IS NULL"
            + " AND " + BindUtil.parseFilterList("c.request.id ", requestIds), "reason", reason
        );
        return HQL_updateByQuery(""
            + " UPDATE " + Request.class.getCanonicalName()
            + " SET "
                + " status = " + WorkflowRequestStatus.CANCELED.getValue()
                + " ,reazon = reazon || '\n' || :reason "
                + " ,rejectionDate = CURRENT_DATE() "
                + " ,rejectedByUserId = :loggedUserId "
            + " WHERE " + BindUtil.parseFilterList("id ", requestIds),
            ImmutableMap.of(
                "reason", reason,
                "loggedUserId", loggedUser.getId()
            )
        ) > 0;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean destroyRequest(Long requestId, String reason) {
        HashMap<String,Object> params = new HashMap<>();
        params.put("id", requestId);
        String prevReason = HQL_findSimpleString(""
                + " SELECT c.reazon"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id" , requestId);
        if (prevReason != null && !prevReason.isEmpty()) {
            reason = prevReason + "\n" + reason;
        }
        params.put("reason", reason);
        return HQL_updateByQuery(""
                + " UPDATE " + Request.class.getCanonicalName()
                + " SET "
                + " deleted = 1,"
                + " reazon = :reason "
                + " WHERE id = :id", params
        ) == 1;
    }
    
    @Override
    @OnStartedFillForm
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IOutstandingSurveysLoadAnswers startFillForm(Request sol, IOutstandingSurveysLoadAnswers outstandingSurvey, Long activityId, ILoggedUser loggedUser) {
        final RequestRef request = new RequestRef(sol.getId());
        request.setAutorId(sol.getAuthor().getId());
        outstandingSurvey.setRequest(request);
        outstandingSurvey.setRequestId(sol.getId());
        outstandingSurvey.setBusinessUnitDepartmentId(sol.getBusinessUnitDepartmentId());
        final String stage = FormUtil.getStartFillFormStage(outstandingSurvey.getCuestionario().getId());
        outstandingSurvey.setStage(stage);
        outstandingSurvey.setStatus(OutstandingSurveys.STATUS.STAND_BY.getValue());
        outstandingSurvey = makePersistent(outstandingSurvey, loggedUser.getId());
        sol.setStatus(Request.STATUS.STAND_BY.getValue()); 
        sol.setOutstandingSurveysId(outstandingSurvey.getId());
        makePersistent(sol, loggedUser.getId());
        workflowDao.updateRequestAuthorizatorNames(this.getAspectJAutoProxy(), sol.getId());
        return outstandingSurvey;
    }
   
    private void initializeVersion(Request sol) {
        if (sol.getVersion() == null) {
            String initialVersion = HQL_findSimpleString(""
                    + " SELECT c.documentInitialVersion "
                    + " FROM DPMS.Mapping.Settings c "
                    + " WHERE c.id = 1");
            sol.setVersion(initialVersion);
        }
    }    
    
    @Override
    @OnRequestNewDocument
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public RequestSaveHandle requestNewDocument(Request request, FilesLite file, ILoggedUser loggedUser, boolean ajax) {
        return saveNewDocument(request, file, loggedUser, ajax);
    }
    
    private RequestSaveHandle saveNewDocument(Request request, FilesLite file, ILoggedUser loggedUser, boolean ajax) {
        if (request.getGenerateCode() == 0) {
            RequestSaveHandle gsh = new RequestSaveHandle();
            final Boolean invalid = isRepeatedCodeOrName(request, loggedUser, gsh);
            if (invalid) {
                gsh.setOperationEstatus(0);
                return gsh;
            }
        }
        return saveRequest(request, file, loggedUser, ajax, true);
    }

    @Override
    @OnRequestDocumentModification
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public RequestSaveHandle requestDocumentModification(Request sol, FilesLite file, ILoggedUser loggedUser, boolean ajax) {
        return saveRequest(sol, file, loggedUser, ajax, true);
    }

    @Override
    @OnRequestDocumentApproval
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public RequestSaveHandle requestDocumentApproval(Request sol, FilesLite file, ILoggedUser loggedUser, boolean ajax) {
        return saveRequest(sol, file, loggedUser, ajax, true);
    }

    @Override
    @OnRequestDocumentCancellation
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public RequestSaveHandle requestDocumentCancellation(Request sol, FilesLite file, ILoggedUser loggedUser, boolean ajax) {
        return saveRequest(sol, file, loggedUser, ajax, true);
    }

    @Override
    @OnRequestNewManager
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request verifyNewManager(Request request, ILoggedUser loggedUser) {
        request.setStatus(WorkflowRequestStatus.VERIFING.getValue());
        return makePersistent(request, loggedUser.getId());
    }

    @Override
    @OnRequestVerified
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request verifyApproved(Request request, WorkflowAuthRole authRole, ILoggedUser loggedUser) throws IOException, QMSException {
        request.setStatus(WorkflowRequestStatus.APROVING.getValue());
        makePersistent(request, loggedUser.getId());
        if (request.getDocument() != null) {
            request.getDocument().setStatus(Document.IN_EDITION_STATUS);
            makePersistent(request.getDocument(), loggedUser.getId());
            //Cancela todas las demas solicitudes al documento
            List<Request> lstRequest = HQL_findByQuery(""
                    + " SELECT c "
                    + " FROM " + Request.class.getCanonicalName() + " c"
                    + " WHERE  c.deleted = 0 AND c.id != :requestId "
                    + " AND c.documentMasterId = :masterId"
                    + " AND c.status = " + Request.STATUS_VERIFING,
                ImmutableMap.of(
                    "requestId", request.getId(),
                    "masterId", request.getDocumentMasterId()
                )
            );
            for (Request anotherRequest : lstRequest) {
                getAspectJAutoProxy().rejectVerification(anotherRequest.getId(), anotherRequest.getDocument().getId(),
                        getTag("rejectReason").replace(":reason", request.getReazon()), loggedUser);
            }
        }
        workflowDao.workflowNextOrEnd(true, this.getAspectJAutoProxy(), request, authRole, loggedUser);
        return request;
    }

    @Override
    @OnRequestVerifiedPublished
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request verifyApprovedPublished(Request request, ILoggedUser loggedUser) throws IOException, QMSException {
        return (Request) onWorkflowEnd(request.getId(), loggedUser, false);
    }
    
    private void setRequestTypeIfMissing(Request request) {
        if (request == null 
                || request.getId() == null
                || request.getId() == -1L
        ) {
            return;
        }
        if (request.getType() == null) {
            request.setType(HQL_findSimpleInteger(""
                + " SELECT c.type"
                + " FROM " + Request.class.getCanonicalName() + " c "
                + " WHERE c.id = :requestId ", "requestId", request.getId()
            ));
        }
    }
    
    private void freeze(Request request) {
        if (request == null 
                || request.getId() == null
                || request.getId() == -1L
        ) {
            return;
        }
        if (request.getSurveyId() == null) {
            request.setSurveyId(HQL_findSimpleLong(""
                + " SELECT c.surveyId"
                + " FROM " + Request.class.getCanonicalName() + " c "
                + " WHERE c.id = :requestId ", "requestId", request.getId()
            ));
            if (request.getSurveyId().equals(0L)){
                request.setSurveyId(null);
            }
        }
        if (request.getSurveyId() == null) {
            return;
        }
        SurveyUtil.freeze(request.getSurveyId(), this);
    }


    @Override
    @OnFormRequested
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean requestForm(Long requestId, ILoggedUser loggedUser) {
        Map params = new HashMap();
        params.put("id", requestId);
        params.put("status", WorkflowRequestStatus.VERIFING.getValue());
        return HQL_updateByQuery(""
                + " UPDATE  " + Request.class.getCanonicalName() + " c"
                + " SET c.status = :status "
                + " WHERE c.id = :id", params) == 1L;
    }

    @Override
    @OnNextAuthorizeNewDocument
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request nextAuthorizeNewDocument(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails) {
        return (Request) workflowDao.createNextPoolDetails(this.getAspectJAutoProxy(), request, poolDetails, loggedUser);
    }

    @Override
    @OnNextAuthorizeModification
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request nextAuthorizeModification(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails) {
        return (Request) workflowDao.createNextPoolDetails(this.getAspectJAutoProxy(), request, poolDetails, loggedUser);
    }

    @Override
    @OnNextAuthorizeReapproval
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request nextAuthorizeReapproval(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails) {
        return (Request) workflowDao.createNextPoolDetails(this.getAspectJAutoProxy(), request, poolDetails, loggedUser);
    }

    @Override
    @OnNextAuthorizeCancellation
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Request nextAuthorizeCancellation(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails) {
        return (Request) workflowDao.createNextPoolDetails(this.getAspectJAutoProxy(), request, poolDetails, loggedUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isUserTheAuthor(Long requestId, Long loggedUserId) {
        return HQL_findSimpleLong(""
                + " SELECT COUNT(c.id)"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.id = :requestId "
                + " AND c.author.id = :loggedUserId ",
                ImmutableMap.of(
                    "requestId", requestId,
                    "loggedUserId", loggedUserId
                )
            ) == 1L;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isDocumentManager(Long requestId, Long loggedUserId, boolean admin) {
        if (admin) {
            return true;
        }
        ProfileServices[] servicesTheirs = {ProfileServices.DOCUMENTO_ENCARGADO};
        Map params = new HashMap();
        params.put("requestId", requestId);
        params.put("loggedUserId", loggedUserId);
        String hql = ""
                + " SELECT count(req.id)"
                + " FROM " + Request.class.getCanonicalName() + " req"
                + " JOIN req.department dep"
                + " JOIN req.businessUnit bu"
                + " JOIN req.organizationalUnit org"
                + " WHERE req.id = :requestId AND ("
                    + " dep.documentManagerId = :loggedUserId OR"
                    + " bu.documentManagerId = :loggedUserId OR"
                    + " org.documentManagerId = :loggedUserId OR"
                    + " EXISTS ("
                        + " SELECT 1"
                            + " FROM " + User.class.getCanonicalName() + " u"
                        + " JOIN u.puestos pos"
                        + " JOIN pos.une une"
                        + " JOIN pos.perfil pr "
                        + " WHERE une.id = dep.businessUnitId"
                        + " AND 1 IN (" + ProfileServices.getCodedServices("pr", servicesTheirs) + ")"
                    + " )"
                + " )";
        return HQL_findSimpleLong(hql, params) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasAuthorizationPending(final Long requestId, final Long loggedUserId) {
        final List<Long> requestIds = new ArrayList<>(1);
        requestIds.add(requestId);
        return hasAuthorizationPending(requestIds, loggedUserId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasAuthorizationPending(final List<Long> requestIds, final Long loggedUserId) {
        final IPendingQueries ops = new ToAuthorizeRequest(this);
        final Long count = HQL_findSimpleLong(""
                + " SELECT COUNT(c.id) "
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND " + BindUtil.parseFilterList("c.id", requestIds)
                + " AND c.deleted = 0 AND " + ops.filterRecordsByUser(loggedUserId, "c"));
        return count > 0;   
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasVerificationPending(final Long requestId, final Long loggedUserId) {
        final List<Long> requestIds = new ArrayList<>(1);
        requestIds.add(requestId);
        return hasVerificationPending(requestIds, loggedUserId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasVerificationPending(final List<Long> requestIds, final Long loggedUserId) {
        final IPendingQueries ops = new ToVerifyRequest(this);
        final Long count = HQL_findSimpleLong(""
                + " SELECT COUNT(c.id) "
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND " + BindUtil.parseFilterList("c.id", requestIds)
                + " AND c.deleted = 0 AND " + ops.filterRecordsByUser(loggedUserId, "c"));
        return count > 0;   
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String generateDocumentMasterId() {
        final String masterId = UUID.randomUUID().toString();
        return validateDocumentMasterId(masterId);
    }
    
    private String validateDocumentMasterId(String masterId) {
        final boolean unique = isUniqueDocumentMasterId(masterId);
        if (unique) {
            return masterId;
        }
        return generateDocumentMasterId();
    }

    private boolean isUniqueDocumentMasterId(String masterId) {
        return HQL_findSimpleLong(""
                + " SELECT count(c)"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.documentMasterId = :masterId", "masterId", masterId) == 0L;
    }
    
    private String getDocumentMasterId(Long documentId) {
        return HQL_findSimpleString(""
                + " SELECT c.masterId"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :documentId", "documentId", documentId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getDocumentMasterIdByRequestId(Long requestId) {
        return HQL_findSimpleString(""
                + " SELECT c.documentMasterId"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", requestId);
    }
    private Long getDictionaryIndexId(Long documentTypeId) {
        return HQL_findSimpleLong(""
                + " SELECT c.dictionaryIndexId"
                + " FROM " + DocumentType.class.getCanonicalName() + " c"
                + " WHERE c.id = :documentTypeId", "documentTypeId", documentTypeId);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer getActiveFillFormsByAuthorCount(Long userId) {
        if (userId == null || userId == -1L) {
            return 0;
        }
        return HQL_findSimpleInteger(""
            + " SELECT COUNT(c.id)"
            + " FROM " + FillRequest.class.getCanonicalName() + " c"
            + " WHERE  c.deleted = 0"
            + " AND c.author.id = :userId"
            + " AND c.status IN ("
                + Request.STATUS.STAND_BY.getValue() + "," 
                + WorkflowRequestStatus.APROVING.getValue() + "," 
                + WorkflowRequestStatus.RETURNED.getValue() 
            + " )"
            + " AND c.type = " + Request.FILL, "userId", userId);
    }

    @Override 
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isRequestInStandBy(Long requestId) {
        if (requestId == null) {
            return true;
        }
        final Long result = HQL_findSimpleLong(""
                + " SELECT COUNT(req.id)"
                + " FROM " + Request.class.getCanonicalName() + " req "
                + " WHERE req.id = :id"
                + " AND req.status = " + Request.STATUS_STANDBY,
                "id", requestId
        );
        return result != null && result > 0;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessById(final Long requestId, final ILoggedUser loggedUser) {
        boolean hasUnrestrictedDocumentAccess = loggedUser.getServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
        if (loggedUser.isAdmin() || hasUnrestrictedDocumentAccess) {
            return true;
        }
        final Integer type = HQL_findSimpleInteger(" "
                + " SELECT c.type"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", requestId)
        );
        final int countAccess;
        if (Request.TYPE.FILL.getValue().equals(type)) {
            countAccess = HQL_findSimpleInteger(
                    ISurveyCaptureDAO.MY_FILLS_COUNT_ACCESS,
                    ImmutableMap.of(
                            "id", requestId,
                            "userId", loggedUser.getId()
                    )
            );
        } else {
            final ProfileServices[] services  = new ProfileServices[loggedUser.getServices().size()];
            final Map<String, Object> params = new HashMap<>(2);
            params.put("userId", loggedUser.getId());
            params.put("id", requestId);
            final String perf = VALID_ENTITIES_FILTER.replaceAll(":services", ProfileServices.getCodedServices("perf", services));
            countAccess = HQL_findSimpleInteger(" "
                            + " SELECT count(c.id)"
                            + " FROM " + Request.class.getCanonicalName() + " c"
                            + " WHERE c.id = :id"
                            + " AND (" + perf + " )",
                    params
            );
        }
        return countAccess > 0;
    }
    

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle multipleReapprove(final List<Long> documentIds, final String reason, final ILoggedUser loggedUser) throws IOException, QMSException {
        final List<DocumentSimple> docs = HQL_findByQuery(" "
                + " SELECT c"
                + " FROM " + DocumentSimple.class.getCanonicalName() + " c"
                + " WHERE " + BindUtil.parseFilterList("c.id", documentIds));
        final GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.setJsonEntityData(new HashMap<>(documentIds.size()));
        docs.forEach(doc -> {
            try {
                final GenericSaveHandle reqResult = createReapproveRequest(doc, reason, loggedUser);
                gsh.getJsonEntityData().put(doc.getId().toString(), reqResult);
            } catch (final QMSException | IOException ex) {
                throw new RuntimeException(ex);
            }
        });
        gsh.setOperationEstatus(1);
        return gsh;
    }
    
    private Request createRequestFromDocument(final DocumentSimple document, final String reason, final ILoggedUser loggedUser) throws QMSException {
        final Request request = new Request(-1l);
        request.setReazon(reason);
        request.setStatus(Request.STATUS.STAND_BY.getValue());
        if (document.getDepartment() != null) {
            request.setDepartment(new BusinessUnitDepartmentRef(document.getDepartment().getId()));
        }
        request.setDescription(document.getDescription());
        request.setStoragePlaceId(document.getStoragePlaceId());
        request.setAuthor(loggedUser.getUserRefInstance());
        request.setType(Request.TYPE.APROVE.getValue());
        request.setDocumentCode(document.getCode());
        request.setFileId(document.getFileId());
        request.setBusinessUnit(document.getBusinessUnit());
        request.setBusinessUnitDepartmentId(document.getBusinessUnitDepartmentId());
        if (document.getBusinessUnitDepartmentId() != null) {
            request.setDepartment(new BusinessUnitDepartmentRef(document.getBusinessUnitDepartmentId()));
        }
        request.setBusinessUnitId(document.getBusinessUnitId());
        request.setDocumentMasterId(document.getMasterId());
        if (document.getNodoId() != null) {
            request.setNodo(new NodeSimple(document.getNodoId()));
        }
        if (document.getFileId() != null) {
            request.setFileId(document.getFileId());
        }        
        request.setFileContent(document.getFileContent());
        request.setDocumentType(document.getDocumentType());
        request.setDocument(new Document(document.getId()));
        request.setVersion(document.getVersion());
        request.setEnablePdfViewer(document.getEnablePdfViewer());
        request.setRetentionText(document.getRetentionText());
        request.setRetentionTime(document.getRetentionTime());
        request.setSurveyId(document.getSurveyId());
        request.setRestrictRecordsByDepartment(document.getRestrictRecordsByDepartment());
        request.setValidateAccessFormDepartment(document.getValidateAccessFormDepartment());
        request.setRestrictRecordsField(document.getRestrictRecordsField());
        request.setRestrictRecordsObjId(document.getRestrictRecordsObjId());
        if (document.getCollectingAndStoreResponsible() != null) {
            request.setCollectingAndStoreResponsible(document.getCollectingAndStoreResponsible().getId());
        }
        request.setCollectingAndStoreResponsibleDescription(document.getCollectingAndStoreResponsibleDescription());
        request.setDisposition(document.getDisposition());
        request.setInformationClassification(document.getInformationClassification());
        if (document.getOrganizationalUnitId() != null) {
            request.setOrganizationalUnit(new OrganizationalUnitLite(document.getOrganizationalUnitId()));
        }
        if (document.getBusinessUnitDepartmentId() != null) {
            request.setScope(Request.SCOPE.DEPARTMENT.getValue());
        } else if (document.getBusinessUnit()!= null) {
            request.setScope(Request.SCOPE.BUSINESS_UNIT.getValue());
        } else {
            request.setScope(Request.SCOPE.ORGANIZATIONAL_UNIT.getValue());
        }
        if (document.getSurveyId() != null) {
            request.setSurvey(new SurveySimple(document.getSurveyId()));
        }
        final String dynamicTableName = document.getDynamicTableName();
        if (dynamicTableName != null) {
            final IDynamicFieldDAO dynamicDao = getBean(IDynamicFieldDAO.class);
            final DynamicFieldsDTO dynamicConfig = dynamicDao.getDynamicFieldsByTableName(
                    dynamicTableName, 
                    Document.class.getCanonicalName(),
                    false,
                    false,
                    null,
                    new HashMap<>()
            );   
            if (dynamicConfig.getDynamicTableName() != null ) {
                final Map<String, String> dynamicData = dynamicDao.selectDynReference(
                        document.getId(),
                        Document.class, 
                        dynamicConfig.getDynamicFields(), 
                        dynamicConfig.getDynamicTableName()
                );
                dynamicData.remove("dynamicTableName");
                dynamicData.remove("id");
                final DynamicTableHelper dynamicHelper = new DynamicTableHelper(dynamicDao);
                final DynamicFieldInsertDTO savedData = dynamicHelper.persist(-1L, new LinkedHashMap<>(dynamicData), dynamicTableName);
                request.setDynamicTableName(dynamicTableName);
                request.setDynamicFieldInsertDTO(savedData);
            }
        }
        return request;
        
    }
    
    private GenericSaveHandle createReapproveRequest(
            final DocumentSimple document, 
            final String reason,
            final ILoggedUser loggedUser
    ) throws IOException, QMSException {
        final Request request = createRequestFromDocument(document, reason, loggedUser);
        final FilesLite file;
        if (document.getFileId() != null) {
            file = new FilesLite(document.getFileId());
        } else {
            file = null;
        }
        final RequestSaveHandle gsh = getAspectJAutoProxy().save(
                request,
                file, 
                Request.TYPE.APROVE.getValue(),
                loggedUser,
                document.getSurveyId() != null,
                true
        );
        if (gsh.getOperationEstatus() == 1) {
            getBean(IRequestHistoryDAO.class).save(request, loggedUser);
        }
        gsh.setRequest(null);
        return gsh;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle authorizeMultipleRequest(final List<Long> requestIds, final String comments, final ILoggedUser loggedUser) throws IOException, QMSException {
        final List<Request> reqs = HQL_findByQuery(" "
                + " SELECT c"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE " + BindUtil.parseFilterList("c.id", requestIds));
        final GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.setJsonEntityData(new HashMap<>(requestIds.size()));
        for (Request req : reqs) {
            final GenericSaveHandle auth = workflowDao.workflowRequestApprove(
                getAspectJAutoProxy(),
                req, 
                WorkflowAuthRole.ASSIGNED,
                comments,
                loggedUser
            );
            if ((boolean) auth.getJsonEntityData().get("authorized")) {
                auth.getJsonEntityData().put("autorized", true);
            } else {
                auth.getJsonEntityData().put("autorized", false);
            }
            gsh.getJsonEntityData().put(req.getId().toString(), auth);
        }
        gsh.setOperationEstatus(1);
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle rejectMultipleAutorization(final List<Long> requestIds, final String comments, final ILoggedUser loggedUser) throws IOException, QMSException {
        final List<Request> reqs = HQL_findByQuery(" "
                + " SELECT c"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE " + BindUtil.parseFilterList("c.id", requestIds));
        final GenericSaveHandle gsh = new GenericSaveHandle();
        gsh.setJsonEntityData(new HashMap<>(requestIds.size()));
        reqs.forEach(req -> {
            try {
                final Boolean auth = getAspectJAutoProxy().rejectAutorization(req, req.getDocumentCode(), comments, WorkflowAuthRole.ASSIGNED, loggedUser);
                gsh.getJsonEntityData().put(req.getId().toString(), auth);
            } catch (final QMSException | IOException ex) {
                throw new RuntimeException(ex);
            }
        });
        gsh.setOperationEstatus(1);
        return gsh;
    }

    /**
     * Función que obtiene el pool de autorización de una solicitud.
     * @param request la solicitud de la que se obtendrá el pool, si no tiene, se crea uno nuevo
     * @return  el pool lleno
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AutorizationPool getPool(IWorkflowRequest request, ILoggedUser loggedUser) {
        Long requestId = request.getId();
        AutorizationPool authorizationPool = request.getAutorizationPool(); 
        if (authorizationPool == null) {
            authorizationPool = new AutorizationPool(null,requestId);
            authorizationPool.setStatus(AutorizationPool.STATUS_ACTIVE);
            authorizationPool.setCreationDate(new Date());
        }
        return this.makePersistent(authorizationPool, loggedUser.getId());
    }

    @Override 
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public boolean isAutomaticallyAuthorizeEnabled(IWorkflowRequest entity) {
        Request request = (Request) entity;
        return !Objects.equals(request.getType(), Request.FILL);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isRecurrenceEnabled(IWorkflowRequest entity) {
        Request request = (Request) entity;
        return request.getOutstandingSurveysId() != null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public WorkflowSupported getWorkflowSettings() {
        return WorkflowSupported.REQUEST;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getEntityIdFromAutorizationPoolDetails(IAutorizationPoolDetails apd) {
        return apd.getRequestId();
    }
    
    private void refreshWorkflowPreview(final IWorkflowRequest request) {
        workflowDao.refreshWorkflowPreview(this.getAspectJAutoProxy(), request.getId());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AutorizationPoolDetails getAutorizationPoolDetailsInstance(IWorkflowRequest entity, WorkflowPool workPool, Owner owner, AutorizationPool pool) {
        Request request = (Request) entity;
        AutorizationPoolDetails detail = new AutorizationPoolDetails(-1L);
        detail.setIndice(workPool.getIndex());
        detail.setOwnerId(owner.getId());
        detail.setAutorizationPool(pool);
        detail.setRequestId(request.getId());
        detail.setOutstandingSurveyId(request.getOutstandingSurveysId());
        detail.setFinishDate(null);
        detail.setUserId(null);
        detail.setDocumentId(null);
        return detail;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean onCreateNextPoolDetails(IWorkflowRequest entity, WorkflowPool currentPool, ILoggedUser loggedUser) {
        Request request = (Request) entity;
        Request.TYPE type = Request.TYPE.getType(request.getType());
        switch (type) {
            case NEW:
                return getAspectJAutoProxy().nextAuthorizeNewDocument(request, loggedUser, currentPool) !=  null;
            case EDIT_DETAILS:
            case MODIFY:
                return getAspectJAutoProxy().nextAuthorizeModification(request, loggedUser, currentPool) !=  null;
            case APROVE:
                return getAspectJAutoProxy().nextAuthorizeReapproval(request, loggedUser, currentPool) !=  null;
            case CANCEL:
                return getAspectJAutoProxy().nextAuthorizeCancellation(request, loggedUser, currentPool) !=  null;
        }
        return false;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean onBusinessUnitDepartmentChange(IWorkflowRequest requested, Long userId, ILoggedUser loggedUser) {
        Request request = (Request) requested;
        getAspectJAutoProxy().verifyNewManager(request, loggedUser); 
        return true;
}
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isStartWorkflowAvailable(IWorkflowRequest entity, WorkflowAuthRole authRole, ILoggedUser loggedUser) throws IOException, QMSException {
        Request request = (Request) entity;
        boolean isUncontrolled = isUncontrolledDocument(request);
        if (request.getFlujoId() != null && !isUncontrolled) {
            getAspectJAutoProxy().verifyApproved(request, authRole, loggedUser);
            getLogger().trace("Pasa a autorizacion");
            return true;
        }
        return false;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isRequestEndedImmediately(IWorkflowRequest entity, ILoggedUser loggedUser) throws IOException, QMSException {
        Request request = (Request) entity;
        boolean isUncontrolled = isUncontrolledDocument(request);
        if (Request.STATUS_CLOSED == request.getStatus() && isUncontrolled) {
            getAspectJAutoProxy().verifyApprovedPublished(request, loggedUser);
            return true;
        }
        return false;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void onStartWorkflowValidation(IWorkflowRequest before, IWorkflowRequest after, Long userId, ILoggedUser loggedUser) {
        Request oRequest = (Request) before;
        Request request = (Request) after;
        Long requestId = after.getId();
        //iniciar aqui evita problemas de transacciones con hibernate al guardar
        Verification ver = new Verification(requestId, userId);
        this.verificationToLog(requestId, loggedUser.getId());
        if (isRequestChanged(oRequest, request)) {
            ver.setLastAction(Verification.VERIFICATION_MODIFIED);
        } else {
            ver.setLastAction(Verification.VERIFICATION_ACCEPT);
        }
        ver.setLastModification(new Date());
        ver.setVerificator(new UserRef(userId));
        this.makePersistent(ver, loggedUser.getId());
        boolean isUncontrolled = isUncontrolledDocument(request);
        if(isUncontrolled) {
            request.setStatus(Request.STATUS_CLOSED);
        }
    }

    private boolean isRequestChanged(IWorkflowRequest base, IWorkflowRequest copia) {
        Request original = (Request) base;
        Request copy = (Request) copia;
        //TODO: this is a complex method 15/10
        //there is a lot of conditionals operator max allowed is 3
        return !objectEquals(original, copy)
                || !objectEquals(original.getDescription(), copy.getDescription())
                || !objectEquals(original.getDocumentCode(), copy.getDocumentCode())
                || !objectEquals(original.getVersion(), copy.getVersion())
                || !objectEquals(original.getDepartment(), copy.getDepartment())
                || !objectEquals(original.getDocumentType(), copy.getDocumentType())
                || !objectEquals(original.getStoragePlaceId(), copy.getStoragePlaceId())
                || !objectEquals(original.getRetentionText(), copy.getRetentionText())
                || !objectEquals(original.getRetentionTime(), copy.getRetentionTime())
                || !objectEquals(original.getFileId(), copy.getFileId())
                || !objectEquals(original.getNodo(), copy.getNodo())
                || !objectEquals(original.getDynamicTableName(), copy.getDynamicTableName())
                || (original.getFlujoId() != null && !objectEquals(original.getFlujoId(), copy.getFlujoId()))
                || !objectEquals(original.getAutorizationPool(), copy.getAutorizationPool());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IJoinableRequest loadRequestJoinable(Long requestId, DocumentDataIndex index) {
        if (requestId == null || requestId <= 0) {
            return null;
        }
        if (index.containsJoinableRequest(requestId)) {
            return index.getJoinableRequest(requestId);
        }

        IPlainRequest plainRequest;
        if (index.containsPlainRequest(requestId)) {
            plainRequest = index.getPlainRequest(requestId);
        } else {
            plainRequest = loadPlainRequest(requestId);
            index.setPlainRequest(requestId, plainRequest);
        }

        if (plainRequest == null) {
            return null;
        }

        final JoinableRequestDTO request = new JoinableRequestDTO(plainRequest);
        index.setJoinableRequest(requestId, request);

        IDocumentDAO documentDao = getBean(IDocumentDAO.class);

        Long fillOutDocumentId = request.getFillOutDocumentId();
        if (index.containsPlainDocument(fillOutDocumentId)) {
            request.setDocument(index.getPlainDocument(fillOutDocumentId));
        } else {
            IPlainDocument fillOutDocument = documentDao.loadPlainDocument(fillOutDocumentId);
            request.setDocument(fillOutDocument);
            index.setPlainDocument(fillOutDocumentId, fillOutDocument);
        }

        Long businessUnitId = request.getBusinessUnitId();
        if (index.containsBusinessUnit(businessUnitId)) {
            request.setBusinessUnit(index.getBusinessUnit(businessUnitId));
        } else {
            IBusinessUnitDAO businessUnitDAO = getBean(IBusinessUnitDAO.class);
            IPlainBusinessUnit businessUnit = businessUnitDAO.loadPlainBusinessUnit(businessUnitId);
            request.setBusinessUnit(businessUnit);
            index.setBusinessUnit(businessUnitId, businessUnit);
        }

        Long documentId = request.getDocumentId();
        if (index.containsPlainDocument(documentId)) {
            request.setDocument(index.getPlainDocument(documentId));
        } else {
            IPlainDocument document = documentDao.loadPlainDocument(documentId);
            request.setDocument(document);
            index.setPlainDocument(documentId, document);
        }

        Long oldDocumentId = request.getOldDocumentId();
        if (index.containsPlainDocument(oldDocumentId)) {
            request.setOldDocument(index.getPlainDocument(oldDocumentId));
        } else {
            IPlainDocument oldDocument = documentDao.loadPlainDocument(oldDocumentId);
            request.setOldDocument(oldDocument);
            index.setPlainDocument(oldDocumentId, oldDocument);
        }

        Long typeId = request.getTypeId();
        if (index.containsDocumentType(typeId)) {
            request.setDocumentType(index.getDocumentType(typeId));
        } else {
            IPlainDocumentType documentType = documentDao.loadPlainDocumentType(typeId);
            request.setDocumentType(documentType);
            index.setDocumentType(typeId, documentType);
        }

        Long fileId = request.getFileId();
        if (index.containsFile(fileId)) {
            request.setFileContent(index.getFile(fileId));
        } else {
            IFilesDAO filesDAO = getBean(IFilesDAO.class);
            IPlainFile fileContent = filesDAO.loadPlainFile(fileId);
            request.setFileContent(fileContent);
            index.setFile(fileId, fileContent);
        }

        Long nodoId = request.getNodoId();
        if (index.containsNode(nodoId)) {
            request.setNodo(index.getNode(nodoId));
        } else {
            INodeAccessDAO nodeDAO = getBean(INodeAccessDAO.class);
            IPlainNode nodo = nodeDAO.loadPlainNode(nodoId);
            request.setNodo(nodo);
            index.setNode(nodoId, nodo);
        }

        Long organizationalUnitId = request.getOrganizationalUnitId();
        if (index.containsOrganizationalUnit(organizationalUnitId)) {
            request.setOrganizationalUnit(index.getOrganizationalUnit(organizationalUnitId));
        } else {
            IOrganizationalUnitDAO orgDao = getBean(IOrganizationalUnitDAO.class);
            IPlainOrganizationalUnit organizationalUnit = orgDao.loadPlainOrganizationalUnit(organizationalUnitId);
            request.setOrganizationalUnit(organizationalUnit);
            index.setOrganizationalUnit(organizationalUnitId, organizationalUnit);
        }

        Long businessUnitDepartmentId = request.getBusinessUnitDepartmentId();
        if (index.containsBusinessUnitDepartment(businessUnitDepartmentId)) {
            IPlainSearchBusinessUnitDepartment department = index.getBusinessUnitDepartment(businessUnitDepartmentId);
            request.setBusinessUnitDepartment(department);
            request.setDepartment(department);
        } else {
            IBusinessUnitDepartmentDAO departmentDao = getBean(IBusinessUnitDepartmentDAO.class);
            IPlainSearchBusinessUnitDepartment department = departmentDao.loadPlainBusinessUnitDepartment(businessUnitDepartmentId);
            request.setBusinessUnitDepartment(department);
            request.setDepartment(department);
            index.setBusinessUnitDepartment(businessUnitDepartmentId, department);
        }

        Long surveyId = request.getSurveyId();
        if (index.containsSurvey(surveyId)) {
            request.setSurvey(index.getSurvey(surveyId));
        } else {
            ISurveysDAO surveyDAO = getBean(ISurveysDAO.class);
            IPlainSurvey survey = surveyDAO.loadPlainSurvey(surveyId);
            request.setSurvey(survey);
            index.setSurvey(surveyId, survey);
        }

        IUserDAO userDAO = getBean(IUserDAO.class);

        Long autorId = request.getAutorId();
        if (index.containsUser(autorId)) {
            request.setAuthor(index.getUser(autorId));
        } else {
            IPlainUser author = userDAO.loadPlainUser(autorId);
            request.setAuthor(author);
            index.setUser(autorId, author);
        }

        Long collectingAndStoreResponsible = request.getCollectingAndStoreResponsible();
        if (index.containsUser(collectingAndStoreResponsible)) {
            request.setCollectingAndStoreResponsibleUser(index.getUser(collectingAndStoreResponsible));
        } else {
            IPlainUser user = userDAO.loadPlainUser(collectingAndStoreResponsible);
            request.setCollectingAndStoreResponsibleUser(user);
            index.setUser(collectingAndStoreResponsible, user);
        }

        Long impersonatedById = request.getImpersonatedById();
        if (index.containsUser(impersonatedById)) {
            request.setImpersonatedBy(index.getUser(impersonatedById));
        } else {
            IPlainUser user = userDAO.loadPlainUser(impersonatedById);
            request.setImpersonatedBy(user);
            index.setUser(impersonatedById, user);
        }

        Long rejectedByUserId = request.getRejectedByUserId();
        if (index.containsUser(rejectedByUserId)) {
            request.setRejectedByUser(index.getUser(rejectedByUserId));
        } else {
            IPlainUser user = userDAO.loadPlainUser(rejectedByUserId);
            request.setRejectedByUser(user);
            index.setUser(rejectedByUserId, user);
        }

        return request;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IPlainRequest loadPlainRequest(Long requestId) {
        if (requestId == null || requestId <= 0) {
            return null;
        }
        return (IPlainRequest) HQL_findSimpleObject(" "
                + " SELECT new " + RequestPlainDTO.class.getCanonicalName() + "("
                    + " c.id"
                    + ", c.organizationalUnitId"
                    + ", c.restrictRecordsByDepartment"
                    + ", c.validateAccessFormDepartment"
                    + ", c.creationDate"
                    + ", c.rejectionDate"
                    + ", c.creationType"
                    + ", c.deleted"
                    + ", c.enablePdfViewer"
                    + ", c.generateCode"
                    + ", c.isBusy"
                    + ", c.retentionText"
                    + ", c.retentionTime"
                    + ", c.scope"
                    + ", c.status"
                    + ", c.storagePlaceId"
                    + ", c.timeToFreeMinutes"
                    + ", c.type"
                    + ", c.autorId"
                    + ", c.blockedBy"
                    + ", c.businessUnitDepartmentId"
                    + ", c.businessUnitId"
                    + ", c.collectingAndStoreResponsible"
                    + ", c.createdBy"
                    + ", c.disposition"
                    + ", c.documentId"
                    + ", c.oldDocumentId"
                    + ", c.fileId"
                    + ", c.fillOutDocumentId"
                    + ", c.flujoId"
                    + ", c.impersonatedById"
                    + ", c.informationClassification"
                    + ", c.nodoId"
                    + ", c.outstandingSurveysId"
                    + ", c.rejectedByUserId"
                    + ", c.restrictRecordsObjId"
                    + ", c.surveyId"
                    + ", c.surveyThemeId"
                    + ", c.templateSurveyId"
                    + ", c.typeId"
                    + ", c.workflowId"
                    + ", c.authorizersNames"
                    + ", c.blockedByName"
                    + ", c.code"
                    + ", c.collectingAndStoreResponsibleDescription"
                    + ", c.description"
                    + ", c.documentCode"
                    + ", c.documentMasterId"
                    + ", c.dynamicTableName"
                    + ", c.reazon"
                    + ", c.restrictRecordsField"
                    + ", c.slimReportName"
                    + ", c.version"
                + ")"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", requestId);
    }

}
