package DPMS.DAO;

import DPMS.DAOInterface.IPrintableSectionDAO;
import DPMS.Mapping.PrintableSection;
import DPMS.Mapping.PrintableSectionPK;
import Framework.Action.SessionViewer;
import Framework.Config.SortedPagedFilter;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "HibernateDAO_PrintableSection")
@Scope(value = "singleton")
public class HibernateDAO_PrintableSection extends GenericDAOImpl<PrintableSection, PrintableSectionPK> implements IPrintableSectionDAO {
 
    private static final String validEntitiesFilterByBusinessUnitDepartment = ""
            + " exists ( "
            + " SELECT dept.id FROM "
            + " DPMS.Mapping.User as u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.id = c.section.area.department.id "
            + " AND u.id = :userId "
            + " AND 1 IN (:servicios)"
            + ")";

    private static final String validEntitiesFilterByBusinessUnit = ""
            + " exists ( "
            + " SELECT dept.id FROM "
            + " DPMS.Mapping.User as u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.businessUnitId = c.section.area.department.businessUnitId "
            + " AND u.id = :userId "
            + " AND 1 IN (:servicios)"
            + ")";
    
   @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(SortedPagedFilter filter, Long userId, ProfileServices[] servicio, Boolean isAdmin, Boolean isManager) {
        filter.getCriteria().put("<filtered-entity>", (isAdmin ? "" : isManager ? validEntitiesFilterByBusinessUnit 
                : validEntitiesFilterByBusinessUnitDepartment).replace(":userId", userId.toString()).replace(":servicios", ProfileServices.getCodedServices("perfil", servicio))
        );
    }
    
    /**
     * Implementacion del metodo para guardar relación imprimible-sección
     *
     * @param sectionId
     * @param printableIds Lista de relación imprimible-sección a guardar/actualizar
     * @param loggedUserId
     * @return GenericSaveHandle con los resultados de la operacion
     * <AUTHOR> Guadalupe Quintanilla Flores
     * @since 2.4.0.7
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GenericSaveHandle save(Long sectionId, List<Long> printableIds, Long loggedUserId) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (sectionId != null && sectionId > 0) {
            final Map<String,Long> params = new HashMap<>(1);
            params.put("sectionId", sectionId);
            HQL_updateByQuery(""
                    + " DELETE FROM " + PrintableSection.class.getCanonicalName() + " "
                    + " WHERE id.sectionId = :sectionId", params);      
                for (Long printableId : printableIds) {
                final PrintableSection ent = new PrintableSection(printableId, sectionId);
                makePersistent(ent, loggedUserId);
            }
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage(SessionViewer.EDIT_SUCCESS);

        } else {
            gsh.setErrorMessage("sección no válida");
            gsh.setOperationEstatus(0);
            gsh.setSuccessMessage(SessionViewer.ERROR);
        }
        return gsh;
    }
    
}
