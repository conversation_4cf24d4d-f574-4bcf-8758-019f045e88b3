package DPMS.DAO;

import DPMS.DAOInterface.IDeviceDAO;
import DPMS.Mapping.Area;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.Device;
import DPMS.Mapping.DeviceFolio;
import DPMS.Mapping.DeviceType;
import DPMS.Mapping.DisposedDevice;
import DPMS.Mapping.User;
import Framework.Config.ITextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import com.google.common.collect.ImmutableMap;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.device.listeners.OnCreatedDevice;
import qms.device.listeners.OnDisposedDevice;
import qms.device.listeners.OnEditedDevice;
import qms.device.listeners.OnEditedStatusDevice;
import qms.util.interfaces.IGridFilter;

@Lazy
@Repository(value = "HibernateDAO_Device")
@Scope(value = "singleton")
public class HibernateDAO_Device extends GenericDAOImpl<Device, Long> implements IDeviceDAO {

    private static final Integer MAX_ATTEMPTS_CODE_GENERATION = 101;
    
    private static final String validEntitiesFilter = " "
        + " exists ( "
            + " SELECT dept.id "
            + " FROM " + User.class.getCanonicalName() + " u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.businessUnitId = c.department.businessUnitId "
                + " AND u.id = :userId "
                + " AND 1 IN (:servicesMine)"
        + " ) ";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        filter.getCriteria().put("<filtered-entity>", isAdmin ? ""
                : validEntitiesFilter.replace(":userId", userId).replace(":servicesMine", ProfileServices.getCodedServices("perfil", servicio)));
    }

    /**
     * Implementacion del metodo para guardar equipos
     *
     * @param ent equipo que se va a guardar
     * @param user
     * @return GenericSaveHandle con los resultados de la operación
     */
    @Override
    @OnCreatedDevice
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle create(final Device ent, final LoggedUser user) {
        return update(ent, user, true);
    }
    
    /**
     * Implementacion del metodo para actualizar equipos
     *
     * @param ent equipo que se va a actualizar
     * @param user
     * @return GenericSaveHandle con los resultados de la operación
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @Override
    @OnEditedDevice
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle update(final Device ent, final LoggedUser user) {
        return update(ent, user, false);
    }
    
    private GenericSaveHandle update(Device ent, final LoggedUser user, final boolean nuevo) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.DAO.HibernateDAO_Device @ update: " + Utilities.getSerializedObj(ent));
        }
        boolean generate = false;
        if (ent.getCode().isEmpty()) {
            generate = true;
            ent.setCode("#");
        }
        ent = makePersistent(ent, user.getId());
        if (ent != null) {
            if (generate) {
                String deptPrefix = HQL_findSimpleString(""
                        + " SELECT c.code"
                        + " FROM " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " c"
                        + " WHERE c.id = " + ent.getDepartmentId());
                deptPrefix = deptPrefix.substring(0, 1);

                String areaPrefix = HQL_findSimpleString(""
                        + " SELECT c.code"
                        + " FROM " + Area.class.getCanonicalName() + " c"
                        + " WHERE c.id = " + ent.getAreaId());
                areaPrefix = areaPrefix.substring(0, 1);

                String dtyPrefix = HQL_findSimpleString(""
                        + " SELECT c.code"
                        + " FROM " + DeviceType.class.getCanonicalName() + " c"
                        + " WHERE c.id = " + ent.getDeviceTypeId());
                if (dtyPrefix.length() > 2) {
                    dtyPrefix = dtyPrefix.substring(0, 2);
                }

                String filter = "c.departmentId = " + ent.getDepartmentId()
                        + " AND c.areaId = " + ent.getAreaId()
                        + " AND c.deviceTypeId =" + ent.getDeviceTypeId();
                List<DeviceFolio> folios = HQLT_findByQueryFilter(DeviceFolio.class, filter);
                DeviceFolio dFolio = new DeviceFolio();
                if (!folios.isEmpty()) {
                    dFolio = folios.get(0);
                } else {
                    dFolio.setDepartmentId(ent.getDepartmentId());
                    dFolio.setAreaId(ent.getAreaId());
                    dFolio.setDeviceTypeId(ent.getDeviceTypeId());
                    dFolio.setCurrent(0L);
                    dFolio.setId(-1L);
                }
                final String code = generateCode(dFolio, deptPrefix, areaPrefix, dtyPrefix, 0);
                ent.setCode(code);

                ent = makePersistent(ent, user.getId(), false);
            }
            BusinessUnitDepartment dpt =
                    HQLT_findById(BusinessUnitDepartment.class, ent.getDepartmentId());
            if (Objects.equals(ent.getDeleted(), Device.IS_NOT_DELETED)
                    && this.getDuplicatedDevices(dpt.getBusinessUnitId(), ent.getCode())
                    > 1) {
                SQL_updateByQuery(
                        "DELETE FROM device_files WHERE device_id = :deviceId",
                        ImmutableMap.of("deviceId", ent.getId()), 0, Arrays.asList("device_files")
                );
                SQL_updateByQuery(
                        "DELETE FROM device_document WHERE device_id = :deviceId",
                        ImmutableMap.of("deviceId", ent.getId()), 0, Arrays.asList("device_document")
                );
                SQL_updateByQuery(
                        "DELETE FROM device WHERE device_id = :deviceId",
                        ImmutableMap.of("deviceId", ent.getId()), 0, Arrays.asList("device")
                );
                gsh.setOperationEstatus(0);
                gsh.setErrorMessage("exist_record");
                return gsh;
            }
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    private String generateCode(
            final DeviceFolio dFolio,
            final String deptPrefix, 
            final String areaPrefix, 
            final String typePrefix,
            final Integer attempt
    ) {
        dFolio.setCurrent(dFolio.getCurrent() + 1);
        final DeviceFolio savedFolio = makePersistent(dFolio);
        String folio = Framework.Config.Utilities.formatConsecutivo(savedFolio.getCurrent());
        if (savedFolio.getCurrent() > 99) {
            folio = savedFolio.getCurrent().toString();
        } else {
            folio = folio.substring(folio.length() - 2);  
        }
        String code = deptPrefix + areaPrefix + typePrefix + "-" + folio;
        final Long countCode = HQL_findSimpleLong(""
                + " SELECT count(c.id)"
                + " FROM " + Device.class.getCanonicalName() + " c"
                + " WHERE c.code = :code", "code", code
        );
        if (attempt > MAX_ATTEMPTS_CODE_GENERATION) {
            getLogger().error("Could not generate unique device folio: {}, department: {}, area: {}, type: {}",
                deptPrefix,
                areaPrefix,
                typePrefix,
                Utilities.getSerializedObj(dFolio)
            );
            return code;
        }
        if (countCode > 0) {
            return generateCode(dFolio, deptPrefix, areaPrefix, typePrefix, attempt + 1);
        }
        return code;
    }
    
    /**
     * Regresa una lista de los equipos a los que tiene acceso el usuario
     *
     * @param deviceId valor especifico para mostrar
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId el usuario en base al cual se va a filtrar
     * @param servicesMine Los servicios que debe tener el usuario en base al
     * cual se va a filtrar
     * @return Lista de usuarios en el alcance del usuario
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getDevicesInMyBusinessUnit(Long deviceId, boolean isAdmin, Long userId, ProfileServices[] servicesMine) {
        String filter = "1=1";
        if (!isAdmin) {
            filter = validEntitiesFilter
                    .replace(":userId", userId + "")
                    .replace(":servicesMine", ProfileServices.getCodedServices("perfil", servicesMine));
        }
        return this.getStrutsComboList(null, "concat(description,' - ', code)", filter, deviceId, false);
    }

    /**
     * Regresa el número de veces que se encuentra el código de equipo en la
     * planta
     *
     * @param businessUnitId La planta en la que se verificará
     * @param code la clave que se verificará
     * @return un entero con el el número de veces que se encuentra el código de
     * equipo en la planta
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer getDuplicatedDevices(Long businessUnitId, String code) {
        String filter = "c.department.businessUnitId = :businessUnits "
                + " AND c.code =':code'"
                + " AND c.deleted = " + Device.IS_NOT_DELETED;
        filter = filter.replace(":businessUnits", businessUnitId.toString())
                .replace(":code", code);
        return this.HQLT_findByQueryFilter(filter).size();

    }

    /**
     * Regresa el businessUnitId del equipo
     *
     * @param deviceId id del equipo
     * @return businessUnitId del equipo
     * @since *******
     * <AUTHOR> Guadalupe Alemán Reyes
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getBusinessUnitId(Long deviceId) {
        Map<String, Object> params = new HashMap<>();
        params.put("deviceId", deviceId);
        String query = " SELECT c.department.businessUnitId "
                + " FROM DPMS.Mapping.Device c "
                + " WHERE c.id = :deviceId ";

        return this.HQL_findSimpleLong(query, params);
    }

    @Override
    @OnEditedStatusDevice
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Device toggleStatus(Device device, Integer status, LoggedUser user) {
        device = HQLT_findById(Device.class, device.getId());
        if (device != null) {
            device.setStatus(status);
            makePersistent(device, user.getId());
            return device;
        } else {
            return null;
        }
    }

    @Override
    @OnDisposedDevice
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Device dispose(Device device, final DisposedDevice disposedDevice, final LoggedUser user) {
        device = HQLT_findById(Device.class, disposedDevice.getId());
        if (device != null) {
            device.setDeleted(1);
            device.setDisposeDate(disposedDevice.getDisposeDate());
            device.setDisposeComment(disposedDevice.getDisposeComment());
            return makePersistent(device, user.getId());
        }
        return device;
    }
}
