package DPMS.DAO;

import DPMS.DAOInterface.IActionDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IDepartmentDAO;
import DPMS.Mapping.Action;
import DPMS.Mapping.ActionLite;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.Catalog;
import DPMS.Mapping.ClauseType;
import DPMS.Mapping.FindingDocument;
import DPMS.Mapping.Priority;
import DPMS.Mapping.User;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import ape.pending.core.APE;
import ape.pending.entities.PendingRecord;
import com.google.common.collect.ImmutableMap;
import isoblock.accion.accionGenerica;
import isoblock.common.Properties;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.activity.entity.Activity;
import qms.finding.dto.ActionFindingInfoDTO;
import qms.finding.entity.FindingType;
import qms.finding.listeners.OnAnalyzedFinding;
import qms.finding.listeners.OnAssignedFinding;
import qms.finding.listeners.OnCompletedFinding;
import qms.finding.listeners.OnEffectiveFinding;
import qms.finding.listeners.OnEvaluatedIfRequireAnalysis;
import qms.finding.listeners.OnNotEffectiveFinding;
import qms.finding.listeners.OnRejectedFinding;
import qms.finding.listeners.OnReportedFinding;
import qms.finding.listeners.OnRequireAnalyzeFinding;
import qms.finding.listeners.OnUpdatedFinding;
import qms.finding.pending.FindingPending;
import qms.framework.rest.SecurityUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Lazy
@Repository(value = "HibernateDAO_Action")
@Scope(value = "singleton")
@Language(module = "Framework.Config.Lang.DAO.HibernateDAO_Action")
public class HibernateDAO_Action extends GenericDAOImpl<Action, Long> implements IActionDAO {

    /**
     *
     * @param ent
     * @param childFinding: indica cuando esta accion proviene de una accion marcada como NO EFECTIVA, es decir que es
     * hija de otra acción
     * @return
     */
    @OnReportedFinding
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action reportFinding(Action ent, boolean childFinding, LoggedUser loggedUser) {
        if (childFinding) {
            ent.setId(-1L);

        }
        ent = makePersistent(ent, loggedUser.getId());
        this.updateFindingStatus(ent.getId());
        return ent;
    }
    
    /**
     *
     * @param ent
     * @param loggedUser
     * @return
     */
    @OnAssignedFinding
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action assignFinding(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.removeOldFindingDocument(ent.getId());
        this.saveFindingDocument(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }

    @Override
    @OnRejectedFinding
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action rejectFinding(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }

    @OnAnalyzedFinding
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action analyzeFinding(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }
    
    @OnRequireAnalyzeFinding
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action requireAnalyzeFinding(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }    
    
    @OnEvaluatedIfRequireAnalysis
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action evaluatedIfRequireAnalysis(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }    

    @Override
    @OnCompletedFinding
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action completeFinding(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }

    @Override
    @OnUpdatedFinding
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action updateFinding(Action ent, Action previous, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }

    @OnNotEffectiveFinding
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action notEffectiveFinding(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }

    @OnEffectiveFinding
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Action effectiveFinding(Action ent, LoggedUser loggedUser) {
        ent = makePersistent(ent);
        this.updateFindingStatus(ent.getId());
        return ent;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public String getComboAllDepartmentsInMyBusinessUnit(String userId, String selected, boolean isAdmin, String actionRol, boolean isCorporative) {
        IBusinessUnitDepartmentLoadDAO dao = getBean(IBusinessUnitDepartmentLoadDAO.class);
        IDepartmentDAO deptDao = getBean(IDepartmentDAO.class);
        List<ITextHasValue> depts;
        if (actionRol.equals(accionGenerica.ACCIONES_MANAGER)){
            depts = deptDao.getAllDepartmentsInMyBusinessUnit(userId, true, isCorporative);
        } else {
            depts = deptDao.getAllDepartmentsInMyBusinessUnit(userId, isAdmin, isCorporative);
        }
        // This is backward compatibility, please, someday destroy A1 modules
        if(!selected.equals("0") && !selected.isEmpty()){
            BusinessUnitDepartmentLoad dept = dao.HQLT_findById(Long.parseLong(selected));
            if(dept != null) {
                TextHasValue selDept = new TextHasValue(dept.getDescription(), dept.getId());
                if(!depts.contains(selDept)){
                    depts.add(selDept);
                }
            }
        }
        return Utilities.comboParser(depts,selected);
    }

    /**
     * Metodo para mostrar el combo de fuentes en el alta/edicion de acciones
     * @param valueDefaultId
     * @param tipoFuenteId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String comboSourceFinding(Long valueDefaultId, Long tipoFuenteId) {
        List<ITextHasValue> sourceAction = this.sourceAction(valueDefaultId, tipoFuenteId);
        if(sourceAction.isEmpty()){
            return "<option title='" + getTag("addSource")+ "' value='' >" +getTag("combo.noSource")+"</option>";
        }
        return Utilities.comboParser(sourceAction, String.valueOf(valueDefaultId.equals(0L) ? "" : valueDefaultId));
    }

    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> sourceAction(Long valueDefaultId, Long tipoFuenteId) {
        List<ITextHasValue> sourceAction = HQL_findByQuery(""
            + " SELECT new Framework.Config.TextHasValue(c.code, c.value)"
            + " FROM " + Catalog.class.getCanonicalName() + " c "
            + " WHERE c.id.catalogoId = 4 "
            + " AND ("
                + " ("
                    + " c.status = 4 "
                    + (tipoFuenteId.equals(0L)? "" : " AND c.moduleId = " + tipoFuenteId)
                + " )"
            + " AND c.moduleId NOT IN (" + Properties.MODULO_QUEJAS + "," + Properties.MODULO_INDICADORES + ")"
            + " OR c.id.tipoId = " + valueDefaultId
            + " )"
            + " ORDER BY LOWER(c.code) ASC ");
        return sourceAction;
    }
    
    /**
     * Metodo para mostrar las fuentes de acuerdo al modulo.
     * @param module
     * @param catalogoId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getSourceJSON(Integer module, Integer catalogoId) {
        return getSourceJSON(module, catalogoId, null);
    }

    /**
     * Metodo para mostrar las fuentes de acuerdo al modulo.
     * @param module
     * @param catalogoId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getSourceJSON(Integer module, Integer catalogoId, Integer valueDefaultId) {
        StringBuilder hqlBuilder = new StringBuilder(200).append(""
            + " SELECT new map(c.code as text, c.value as value) "
            + " FROM ").append(Catalog.class.getCanonicalName()).append(" c "
            + " WHERE "
                + " c.moduleId = ").append(module).append(""
                + " AND ("
                    + " ("
                        + " c.status = ").append(Catalog.ACTIVE_STATUS).append(""
                        + " AND c.id.catalogoId = ").append(catalogoId).append(""
                        + " AND c.deleted = 0 "
                    + " ) ");
        if(valueDefaultId != null) {
            hqlBuilder.append(" OR c.id.tipoId = ").append(valueDefaultId);
        }
        hqlBuilder.append(""
                + " ) "
            + " ORDER BY c.code ASC"
        );
        List sources = HQL_selectMapQuery(hqlBuilder.toString(), false, null, 0);
        return Utilities.getSerializedObj(
            sources
        );
    }

    /**
     * Metodo para mostrar el combo de prioridades en el alta/edicion de acciones
     * @param valueDefaultId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String comboPriorityFinding(Long valueDefaultId) {
        Long valueDeafault = valueDefaultId.equals(0L) ? -1L : valueDefaultId;
        List<ITextHasValue> list =  getUntypedDAO().getStrutsComboList(Priority.class, "c.description", " c.deleted = 0", valueDeafault, true);
        if(list.isEmpty()){
            return "<option title='" + getTag("addPriorities")+ "' value=''>" +getTag("combo.noPriorities")+"</option>";
        }
        return Utilities.comboParser(list, String.valueOf(valueDefaultId.equals(0L) ? "" : valueDefaultId));
    }
    
    /**
     * Metodo para mostrar el combo de tipo deacciones en el alta/edicion de acciones
     * @param valueDefaultId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String comboTypeFinding(Long valueDefaultId) {
        Long valueDeafault = valueDefaultId.equals(0L) ? -1L : valueDefaultId;
        List<ITextHasValue> list =  getUntypedDAO().getStrutsComboList(FindingType.class, "c.description", " c.deleted = 0", valueDeafault, true);
        if(list.isEmpty()){
            return "<option title='" + getTag("addTypes")+ "' value=''>" +getTag("combo.noTypes") + "</option>";
        }
        return Utilities.comboParser(list, String.valueOf(valueDefaultId.equals(0L) ? "" : valueDefaultId));
    }
    
    /**
     * Metodo para mostrar el combo de normas en el alta/edicion de acciones
     * @param valueDefaultId
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String comboSystemFinding(Long valueDefaultId) {
        Long valueDeafault = valueDefaultId.equals(0L) ? -1L : valueDefaultId;
        List<ITextHasValue> list =  getUntypedDAO().getStrutsComboList(
           ClauseType.class, "c.description", " c.deleted = 0", valueDeafault, true
        );
        if(list.isEmpty()){
            return "<option title='" + getTag("addNorma")+ "' value=''>" +getTag("combo.noSystem") + "</option>";
        }
        return Utilities.comboParser(list, String.valueOf(valueDefaultId.equals(0L) ? "" : valueDefaultId));
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasSpecialAccessToForm(final Long findindId, final Long loggedUserId, final boolean admin) {
        if (admin) {
            return true;
}
        return  HQL_findSimpleLong(" "
            + " SELECT count(c.id)"
            + " FROM " + Action.class.getCanonicalName() + " c"
            + " LEFT JOIN c.department dep"   
            + " LEFT JOIN c.attendant att"       
            + " WHERE c.status IN (" + Action.STATUS.REPORTED.getValue() + ","  + Action.STATUS.ASSIGNED.getValue() + ")"
            + " AND c.id = " + findindId
            + " AND ("
                + " dep.attendantId = " + loggedUserId
                + " OR att.id = " + loggedUserId
                + " OR EXISTS ( "
                    + " SELECT us.id "
                    + " FROM " + User.class.getCanonicalName() +  " us "
                    + " LEFT JOIN us.puestos pu "
                    + " LEFT JOIN pu.perfil prf "
                    + " WHERE us.id = " + loggedUserId
                    + " AND 1 IN (prf." +  ProfileServices.ACCION_ENCARGADO.getCode() + ")"
                + " )"
            + " )") > 0;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateFindingStatus(Long actionId) {
        final Map<String, Object> params = new HashMap<>();
        if (actionId != null) {
            params.put("actionId", actionId);
        }
        Long pendingTypeId = Utilities.getPendingTypeId(APE.FINDING_ACTIVITY_TO_COMPLETE);
        params.put("pendingTypeId", pendingTypeId);
        List<ActionFindingInfoDTO> findings = HQLT_findByQuery(ActionFindingInfoDTO.class, ""
            + " SELECT new " + ActionFindingInfoDTO.class.getCanonicalName() + "("
                + " c.id "
                + ",a.status "
                + ",c.findingsStatus "
                + ",CASE WHEN pr.noticeDate IS NOT NULL THEN pr.noticeDate ELSE singleImplementation.notice END as noticeImplementation "
                + ",pr.commitmentDate "
                + ",a.implementationOn "
            + " ) "
            + " FROM "
                + ActionLite.class.getCanonicalName() + " c"
                + "," + Activity.class.getCanonicalName() + " a "
                + "," + PendingRecord.class.getCanonicalName() + " pr "
            + " JOIN c.activities fa "
            + " LEFT JOIN a.singleImplementation singleImplementation "
            + " WHERE"
                + " a.id = fa.id"
                + " AND a.id = pr.recordId"
                + " AND c.deleted = 0"
                + " AND pr.type = :pendingTypeId"
                + " AND c.status NOT IN ( "
                    + Action.STATUS.CLOSED_EFFECTIVE.getValue() + ","
                    + Action.STATUS.CLOSED_UNEFFECTIVE.getValue() + ","
                    + Action.STATUS.CANCELED.getValue()
                + " )"
                + " AND a.deleted = 0"
                + " AND a.status NOT IN ( "
                    + Activity.STATUS.NOT_APPLY.getValue() + ","
                    + Activity.STATUS.NOT_APPLY_VERIFIED.getValue()
                + " )"
                + (actionId != null ? " AND c.id = :actionId" : "")
            + " ORDER BY c.id, pr.commitmentDate DESC",
            params,
            false,
            null,
            0
        );
        Date today = Utilities.truncDate(new Date());
        List<ActionFindingInfoDTO> findingsAttended =  new ArrayList<>();
        findings.forEach(finding -> {
            if (finding.getStatus() != null && finding.getStatus().equals(Activity.STATUS.VERIFIED.getValue())) {
                finding.setFindingsStatus(Action.findingsStatus.ATTENDED.getValue());
                findingsAttended.add(finding);
            } else if (
                finding.getNoticeImplementation() == null || finding.getPlannedImplementation() == null
            ) {
                finding.setFindingsStatus(Action.findingsStatus.ONTIME.getValue());
                getLogger().error("Error en el metodo updateFindingStatus, NoticeImplementation con valor null");
            } else {
                if (finding.getNoticeImplementation().after(today)){
                    finding.setFindingsStatus(Action.findingsStatus.ONTIME.getValue());
                } else if (finding.getNoticeImplementation().before(today)) {
                    Date plannedImplementation = Utilities.truncDate(finding.getPlannedImplementation());
                    if (plannedImplementation.compareTo(today) >= 0) {
                        finding.setFindingsStatus(Action.findingsStatus.EXPIRESOON.getValue());
                    } else {
                        finding.setFindingsStatus(Action.findingsStatus.OVERDUE.getValue());
                    }
                }
            }
        });
        if (findings.size() == findingsAttended.size()) {
            findings = this.findingsFilterToUpdate(findingsAttended);
        } else {
            findings = this.findingsFilterToUpdate(findings);
        }
        findings.forEach(finding -> {
            HQL_updateByQuery(""
                + " UPDATE " + Action.class.getCanonicalName() + " c "
                + " SET c.findingsStatus = :findingsStatus"
                + " WHERE c.id = :id",
            ImmutableMap.of(
                    "findingsStatus", finding.getFindingsStatus(),
                    "id", finding.getFindingId()
            ));
        });
    }
    
    private List<ActionFindingInfoDTO> findingsFilterToUpdate(List<ActionFindingInfoDTO> findings) {
        List<ActionFindingInfoDTO> findingsToUpdated = new ArrayList<>();
        List<Integer> status = new ArrayList<>();
        status.add(Action.findingsStatus.OVERDUE.getValue());
        status.add(Action.findingsStatus.EXPIRESOON.getValue());
        status.add(Action.findingsStatus.ONTIME.getValue());
        status.add(Action.findingsStatus.ATTENDED.getValue());
        // El orden en que son agregados los estados es la prioridad.
        status.forEach(s -> {
            findings.stream().filter(f -> f.getFindingsStatus().equals(s))
                .forEach(finding -> {
                    if(!findingsToUpdated.stream().anyMatch(f -> f.getFindingId().equals(finding.getFindingId()))){
                        findingsToUpdated.add(finding);
                    }
            });
        });
        return findingsToUpdated;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveFindingDocument(Action action) {
        if (action.getFindingsDocumentIds() != null && action.getFindingsDocumentIds().length() > 0) {
            String[] findingsDocuments = action.getFindingsDocumentIds().split(",");
            for (String documentId : findingsDocuments) {
                documentId = documentId.trim();
                if (documentId != null && documentId.length() > 0) {
                    makePersistent(new FindingDocument(action.getId(), Long.valueOf(documentId)), SecurityUtils.getLoggedUserId());
                }
            }
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer removeOldFindingDocument(Long idAction) {
        return HQL_updateByQuery(""
                + " DELETE FROM " + FindingDocument.class.getCanonicalName() + " fd "
                + " WHERE fd.id.findingId = :actionId",
                ImmutableMap.of("actionId", idAction));
    }

    @Override
    @Transactional
    public void onCompletedFillForm(Long outstandingSurveyId, ILoggedUser loggedUser) {
        Integer result = HQL_updateByQuery("" +
                "UPDATE " + Action.class.getCanonicalName() + " a " +
                    "SET a.status = " + Action.STATUS.ANALIZED.getValue() +
                " WHERE a.outstandingSurveyId = :outstandingSurveyId",
                 ImmutableMap.of("outstandingSurveyId", outstandingSurveyId)
        );
        if (result > 0) {
            Long findingId = HQL_findSimpleLong("" +
                    "SELECT " +
                        "a.id " +
                    "FROM " + Action.class.getCanonicalName() + " a " +
                    "WHERE " +
                        "a.outstandingSurveyId = :outstandingSurveyId"
            , ImmutableMap.of("outstandingSurveyId", outstandingSurveyId));
            final FindingPending findingPending = new FindingPending(this);
            findingPending.toAddPlan(findingId, this.getClass(), loggedUser);
        }
    }
}
