package DPMS.DAO;

import DPMS.DAOInterface.IGalleryDAO;
import DPMS.Mapping.Gallery;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import com.google.common.collect.ImmutableMap;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "HibernateDAO_Gallery")
@Scope(value = "singleton")
public class HibernateDAO_Gallery extends GenericDAOImpl<Gallery, Long> implements IGalleryDAO {

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(Gallery gItem) {
        GenericSaveHandle gsh;
        boolean nuevo = gItem.getId() == -1;
        gItem = this.makePersistent(gItem);
        gsh = this.successGSH(nuevo, gItem.getId());
        return gsh;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer delete(Long id) {
        getLogger().trace("DPMS.DAO.HibernateDAO_Gallery @ delete: [id={}]",id);
        return HQL_updateByQuery(""
            + " UPDATE " + Gallery.class.getCanonicalName() + " g "
            + " SET g.deleted = 1 "
            + " WHERE g.id = :id",
            ImmutableMap.of("id", id)
        );
    }
}
