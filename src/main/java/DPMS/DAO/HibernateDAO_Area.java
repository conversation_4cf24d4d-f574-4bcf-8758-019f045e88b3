package DPMS.DAO;

import DPMS.DAOInterface.IAreaDAO;
import DPMS.Mapping.Area;
import Framework.Config.ITextHasValue;
import Framework.DAO.GenericDAOImpl;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.configuration.listeners.OnChangeAreaManager;
import qms.util.interfaces.IGridFilter;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "HibernateDAO_Area")
@Scope(value = "singleton")
public class HibernateDAO_Area extends GenericDAOImpl<Area, Long> implements IAreaDAO {

    private static final String validEntitiesFilter = ""
            + " exists ( "
                + " SELECT dept.id"
                + " FROM DPMS.Mapping.User as u "
                + " JOIN u.puestos as p "
                + " JOIN p.departamentos as dept "
                + " JOIN p.perfil as perfil "
                + " WHERE ("
                    + " dept.businessUnitId = c.department.businessUnitId "
                    + " OR u.businessUnitId = c.department.businessUnitId "
                    + " OR p.businessUnitId = c.department.businessUnitId "
                + " )"
                + " AND u.id = :userId "
                + " AND 1 IN (:servicios)"
            + ")";
    
    private static final String validEntitiesFilterByBusinessUnitDepartment = ""
            + " exists ( "
            + " SELECT dept.id FROM "
            + " DPMS.Mapping.User as u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.id = c.department.id "
            + " AND u.id = :userId "
            + " AND 1 IN (:servicios)"
            + ")";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        filter.getCriteria().put("<filtered-entity>", isAdmin ? ""
                : validEntitiesFilter.replace(":userId", userId).replace(":servicios", ProfileServices.getCodedServices("perfil", servicio))
        );
    }

    @Override  
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives(Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        return getActives(userId, isAdmin, false, null, null, servicio);
    }
    
    @Override  
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives(Long userId, Boolean isAdmin, Boolean isManager, Long areaId, ProfileServices[] servicio) {
        return getActives(userId, isAdmin, isManager, areaId, null, servicio);
    }
    
    @Override  
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives(Long userId, Boolean isAdmin, Boolean isManager, Long areaId, String extraFilter, ProfileServices[] servicio) {
        String entityFilter = (isAdmin ? "1=1" : isManager ? validEntitiesFilter : validEntitiesFilterByBusinessUnitDepartment)
                .replace(":userId", userId.toString())
                .replace(":servicios", ProfileServices.getCodedServices("perfil", servicio));
        String query = ""
                + " SELECT"
                + " new " + Framework.Config.TextHasValue.class.getCanonicalName()+"(concat(c.description, ' - ',c.department.businessUnit.description), c.id) FROM"
                + " " + Area.class.getCanonicalName() + " c "
                + " WHERE " + entityFilter
                +  (extraFilter == null || extraFilter.isEmpty() ? " AND 1 = 1" :  " AND (" + extraFilter + ")")
                + " AND (c.status = " + Area.ACTIVE_STATUS
                + " OR " + (areaId != null ? "c.id = " + areaId : "1=0") + ")";
        return this.HQL_findByQuery(query);
    }

    @Override  
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> getManagerByAreaId(String extraFilter) {
        String query = ""
                + " SELECT"
                + " a.attendantId FROM "
                + Area.class.getCanonicalName() + " a " 
                + " WHERE a.id IN(" + extraFilter + "))";
        return this.HQL_findByQuery(query);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getManagerId(Long id) {
        return HQL_findSimpleLong(""
            + " SELECT c.attendantId"
            + " FROM " + Area.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id", id);
    }

    @Override
    @OnChangeAreaManager
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Area changeManager(Area ent, Long newMangerId, Long oldManagerId, LoggedUser loggedUser) {
        return makePersistent(ent);
    }
}
