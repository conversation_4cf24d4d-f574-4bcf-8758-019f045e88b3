package DPMS.DAO;

import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.DAOInterface.IServiceApprovalDAO;
import DPMS.DAOInterface.IServiceDAO;
import DPMS.Mapping.Measurement;
import DPMS.Mapping.Periodicity;
import DPMS.Mapping.Service;
import DPMS.Mapping.ServiceSchedule;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import java.util.Calendar;
import java.util.Date;
import java.util.Set;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.device.listeners.OnRealizedService;
import qms.device.listeners.OnRequestedServiceApproval;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "HibernateDAO_Service")
@Scope(value = "singleton")
public class HibernateDAO_Service extends GenericDAOImpl<Service, Long> implements IServiceDAO {

    /**
     * Implementacion del metodo para guardar/actualizar servicios
     *
     * @param service servicio a guardar/actualizar
     * @param user
     * @return GenericSaveHandle con los datos de la operación
     * <AUTHOR> Cavazos Galindo
     * @since 2.3.2.133
     */
    private GenericSaveHandle update(Service service, LoggedUser user) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (user.getId() == 0L) {
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("session_expired");
            return gsh;
        }
        boolean nuevo = (service.getId() == null || service.getId() == -1);
        
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.DAO.HibernateDAO_Service @ update: {}", Utilities.getSerializedObj(service));
        }
        if (user.getId() == 0L) {
            return null;
        }
        /*Actualiza la fecha del siguiente servicio*/
        ServiceSchedule sSchedule = new ServiceSchedule();
        if (service.getScheduleId() == null) {
            sSchedule.setId(-1L);
            Periodicity per = new Periodicity();
            per.setVchtipoperiodicidad("never");
            per.setId(-1L);
            getBean(IPeriodicityDAO.class).makePersistent(per, user.getId());
            sSchedule.setPeriodicity(per);
            sSchedule.setAdvanceDays(0);
            sSchedule.setDeleted(0);
            sSchedule.setCode(service.getCode());
        } else {
            sSchedule = HQLT_findById(ServiceSchedule.class, service.getScheduleId());
        }
        Calendar implementedAt = getServiceImplementationDate(service);
        if (sSchedule.getId() == -1L) {
            sSchedule.setStatus(99);
            service.setStatus(1);
        } else {
            if (implementedAt.getTime().compareTo(sSchedule.getNextService()) == 0) {
                service.setStatus(1); // en fecha
            }
            if (implementedAt.getTime().compareTo(sSchedule.getNextService()) > 0) {
                service.setStatus(2); // en tiempo permitido
            }
            if (implementedAt.getTime().compareTo(sSchedule.getNextService()) < 0) {
                service.setStatus(3); // atrasado
            }
        }
        service.setPlanned(sSchedule.getNextService());
        service.setRegisteredOn(Utilities.getNow());
        service.setValidFrom(implementedAt.getTime());
        Date nextService = getBean(IPeriodicityDAO.class).getNextOccurence(implementedAt.getTime(), sSchedule.getPeriodicity().getId());
        service.setValidTo(nextService);
        sSchedule.setNextService(nextService);

        //Si es periodicidad de "nunca", elimina la programación
        if (sSchedule.getPeriodicity().getVchtipoperiodicidad().equals("never") && sSchedule.getStatus() != 99) {
            sSchedule.setStatus(0);
        }
        sSchedule = makePersistent(sSchedule, user.getId());
        if (sSchedule == null) {
            return gsh;
        }

        service.setAttendantId(user.getId());
        insertMeasurements(service.getMeasurements(), user.getId());        

        //Se intenta guardar en la base de datos
        service = makePersistent(service, user.getId());
        if (service != null && Utilities.getSettings().getServiceToApprove().equals(1)) {
            IServiceApprovalDAO aproovalDao = getBean(IServiceApprovalDAO.class);
            aproovalDao.createApprovalRequest(service.getId(), user.getId());
        }

        if (service != null) {
            gsh.setOperationEstatus(1);
            gsh.setSavedId(service.getId().toString());
            gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
        } else {
            gsh.setOperationEstatus(0);
            gsh.setSavedId(null);
        }
        return gsh;
    }

    private void insertMeasurements(Set<Measurement> measurements, Long loggedUserId) {
        for (Measurement me : measurements) {
            me.setId(-1L);
            makePersistent(me, loggedUserId);
        }
    }

    private Calendar getServiceImplementationDate(Service ent) {
        Calendar implementedAt = Calendar.getInstance();
        if (Utilities.getSettings().getDeviceEditImplementation() == 0 && ent.getId() <= 0) {
            implementedAt.setTime(Utilities.getNow());
        } else {
            implementedAt.setTime(ent.getValidFrom());
        }
        implementedAt.set(Calendar.HOUR, 0);
        implementedAt.set(Calendar.MINUTE, 0);
        implementedAt.set(Calendar.SECOND, 0);
        implementedAt.set(Calendar.MILLISECOND, 0);
        return implementedAt;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Service load(Long id) {
        getLogger().info(">> load");
        return HQLT_findById(id, false);
    }
    
    @Override
    @OnRequestedServiceApproval
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle requestServiceApproval(Service service, LoggedUser loggedUser) {
        return update(service, loggedUser);
    }

    @Override
    @OnRealizedService
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle realizeService(Service service, LoggedUser loggedUser) {  
        return update(service, loggedUser);
    }    
    
}
