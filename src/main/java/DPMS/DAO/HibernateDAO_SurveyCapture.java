package DPMS.DAO;

import DPMS.DAOInterface.IAuditDAO;
import DPMS.DAOInterface.IAuditIndividualDAO;
import DPMS.DAOInterface.IFlowDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveyCaptureDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.Mapping.Audit;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditScoreSource;
import DPMS.Mapping.AutorizationPool;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.BusinessUnitLite;
import DPMS.Mapping.OrganizationalUnitSimple;
import DPMS.Mapping.OutstandingSurveysAttendant;
import DPMS.Mapping.OutstandingSurveysAttendantLoad;
import DPMS.Mapping.OutstandingSurveysSimple;
import DPMS.Mapping.Poll;
import DPMS.Mapping.PollRespondent;
import DPMS.Mapping.Request;
import DPMS.Mapping.Workflow;
import Framework.Action.SessionViewer;
import Framework.Config.DomainObject;
import Framework.Config.Language;
import Framework.Config.TextLongValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.APE;
import ape.pending.util.ApeUtil;
import bnext.licensing.LicenseUtil;
import com.google.common.collect.ImmutableMap;
import isoblock.common.beanGeneric;
import isoblock.surveys.dao.hibernate.IOutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingAnswer;
import isoblock.surveys.dao.hibernate.OutstandingQuestion;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.OutstandingSurveysLogging;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveyField;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import isoblock.surveys.dao.interfaces.ISurveyField;
import isoblock.surveys.dao.interfaces.ISurveyFieldObject;
import isoblock.surveys.dao.type.OutstandingSurveysLoggingType;
import isoblock.surveys.struts2.action.SurveyRequestMode;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.security.ISecurityUser;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.configuration.dto.ExchangeRateDTO;
import qms.custom.dao.IWebhookDAO;
import qms.document.entity.RequestRef;
import qms.document.listeners.OnCancelledFillForm;
import qms.document.listeners.OnCancelledFillForms;
import qms.document.listeners.OnCompletedFillForm;
import qms.document.pending.imp.ToFillForm;
import qms.document.util.DocumentPublicationUtils;
import qms.form.conditional.ConditionalValidator;
import qms.form.core.FillHelper;
import qms.form.dao.IFormCaptureDAO;
import qms.form.dto.FieldConfigDTO;
import qms.form.dto.FillInfoDTO;
import qms.form.dto.FormCaptureDTO;
import qms.form.dto.FormRequestorDTO;
import qms.form.dto.OutstandingQuestionAnsweredDTO;
import qms.form.dto.OutstandingSurveyDTO;
import qms.form.dto.OutstandingSurveyLoadAnswersDTO;
import qms.form.dto.OutstandingSurveysAttendantInfo;
import qms.form.dto.OutstandingSurveysInfoDTO;
import qms.form.dto.OutstandingSurveysSignDTO;
import qms.form.dto.PoolAttendantDTO;
import qms.form.entity.SurveyData;
import qms.form.errors.FormConfigurationException;
import qms.form.listeners.OnCancelledForm;
import qms.form.pending.FormPending;
import qms.form.util.CatalogFieldType;
import qms.form.util.RetrieveArchivedSurveyAnswer;
import qms.form.util.SurveyAnswersFn;
import qms.form.util.SurveyFieldAnswerType;
import qms.form.util.SurveyUtil;
import qms.form.util.SurveyUtilCache;
import qms.form.util.SurveyWeightingType;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.dto.WebhookDataDTO;
import qms.framework.entity.Owner;
import qms.framework.util.CacheRegion;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.MeasureTime;
import qms.poll.listeners.OnPollAnswer;
import qms.survey.dao.ISurveyFieldHistoryDAO;
import qms.survey.dto.PoolFillerDTO;
import qms.survey.dto.SurveyFieldObjectDTO;
import qms.survey.logic.ConditionalValidatorCache;
import qms.timework.dto.TimeworkDoCheckData;
import qms.util.BindUtil;
import qms.util.OwnerUtil;
import qms.util.QMSException;
import qms.workflow.entity.WorkflowRequestData;
import qms.workflow.util.IWorkflowDAO;
import qms.workflow.util.WorkflowAuthRole;
import qms.workflow.util.WorkflowPreviewGenerator;
import qms.workflow.util.WorkflowRequestStatus;
import qms.workflow.util.WorkflowSupported;

/**
 * <AUTHOR> Cavazos Galindo
 */
@Lazy
@Repository(value = "HibernateDAO_SurveyCapture")
@Scope(value = "singleton")
@Language(module = "Framework.Config.Lang.DAO.HibernateDAO_SurveyCapture")
public class HibernateDAO_SurveyCapture extends GenericDAOImpl<OutstandingSurveys, Long> implements ISurveyCaptureDAO {

    private static final List<Integer> VALID_EDIT_ADMIN_STATUS = Arrays.asList(
            Request.STATUS.APROVING.getValue(),
            Request.STATUS.STAND_BY.getValue(),
            Request.STATUS.EXPIRED.getValue(),
            Request.STATUS.RETURNED.getValue()
    );

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE makePersistent(TYPE entity, Boolean doCopy) {
        if (doCopy) {
            if (entity instanceof DomainObject) {
                ((DomainObject) entity).setId(-1L);
            } else {
                beanGeneric.callMethodSetLong(entity, "setId", -1L);
            }
        }
        return super.makePersistent(entity);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(
            final OutstandingSurveys ent,
            final Module module,
            final ILoggedUser loggedUser
    ) throws IOException, QMSException {
        final IFormCaptureDAO formDao = getBean(IFormCaptureDAO.class);
        final Long autorizationIndex = 0L;
        final WorkflowAuthRole authRole = getSurveyAuthRole(ent.getId(), autorizationIndex, loggedUser);
        final FillHelper validator = new FillHelper(
                ent,
                module,
                authRole,
                formDao,
                loggedUser
        );
        return save(ent, loggedUser, autorizationIndex, false, validator);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle saveRequestCopy(
            OutstandingSurveys ent,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        final Long autorizationIndex = 0L;
        final WorkflowAuthRole authRole = getSurveyAuthRole(ent.getId(), autorizationIndex, loggedUser);
        final FillHelper validator = new FillHelper(
                ent,
                Module.FORMULARIE,
                authRole,
                getBean(IFormCaptureDAO.class),
                loggedUser
        );
        return save(ent, loggedUser, autorizationIndex, true, validator);
    }

    private GenericSaveHandle save(
            OutstandingSurveys outstandingSurvey,
            ILoggedUser loggedUser,
            Long autorizationIndex,
            Boolean doCopy,
            FillHelper validator
    ) throws IOException, QMSException {
        doCopy = Boolean.TRUE.equals(doCopy);
        final ElapsedDataDTO oStart = MeasureTime.start(getClass());
        final GenericSaveHandle result = new GenericSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("@ save: {}", Utilities.getSerializedObj(outstandingSurvey));
        }
        result.setSavedObjName("misDatos");

        //Verificando si se recibieron parámetros válidos
        if (outstandingSurvey == null) {
            result.setOperationEstatus(0);
            result.setExtraJson("{\"estatus\":-1}");
            return result;
        }
        //Verificando si el pendiente ya está cerrado, cancelado o interrumpido
        Short status = getSavedOutstandingSurveyStatus(outstandingSurvey.getId());
        getLogger().trace("El estatus del pendiente es:" + status);
        if (status >= OutstandingSurveys.ESTATUS_CERRADA
                && status != OutstandingSurveys.ESTATUS_NUEVO
                && status != OutstandingSurveys.STATUS_RETURN_APPROVAL
                && status != OutstandingSurveys.STATUS_CANCEL_APPROVAL
        ) {
            getLogger().trace("La encuesta ya no es editable.");
            result.setOperationEstatus(0);
            result.setExtraJson("{\"estatus\":" + status + "}");
            MeasureTime.stop(oStart, "Elapsed time in failed save of closed outstanding answers for id " + outstandingSurvey.getId());
            return result;
        }
        final boolean isFinishing = outstandingSurvey.getEstatus() == OutstandingSurveys.ESTATUS_CERRADA;
        final Long surveyId = outstandingSurvey.getCuestionario().getId();
        final Long outstandingSurveyId = outstandingSurvey.getId();
        Long progressStateId = null;
        String stage = null;
        //Actualizando los datos del pendiente
        if (getLogger().isDebugEnabled()) {
            getLogger().debug(" "
                            + "[surveyId: {}, outstandingSurveysId: {}, status: {}] Saving answers of {} questions.", new Object[]{
                            surveyId, outstandingSurveyId, outstandingSurvey.getEstatus(), outstandingSurvey.getPreguntasRespondidas().size()
                    }
            );
        }
        Integer numberAnswers;
        if (!doCopy) {
            // Eliminando físicamente respuestas previas
            numberAnswers = HQL_updateByQuery(" "
                            + " UPDATE " + OutstandingAnswer.class.getCanonicalName() + " ans"
                            + " SET ans.deleted = 1"
                            + " WHERE ans.deleted = 0"
                            + " AND ans.outstandingSurveyId = :outstandingSurveyId",
                    ImmutableMap.of("outstandingSurveyId", outstandingSurveyId)
            );
            if (getLogger().isDebugEnabled()) {
                getLogger().debug(" "
                                + "[surveyId: {}, outstandingSurveysId: {}, status: {}] {} previews answers were deleted,", new Object[]{
                                surveyId, outstandingSurveyId, outstandingSurvey.getEstatus(), numberAnswers
                        }
                );
            }
        }
        // Se obtiene información que se utilizará para guardar información de sociedad/departamento/area en `OutstandingSurveys`
        List<Map<String, Object>> surveyFieldObjects = HQL_findByQuery(" "
                        + " SELECT new map("
                            + " fo.id AS fieldObjectId"
                            + ",f.id AS fieldId"
                            + ",fo.businessUnitMainField AS businessUnitMainField"
                            + ",fo.businessUnitDepartmentMainField AS businessUnitDepartmentMainField"
                            + ",fo.areaMainField AS areaMainField"
                        + " )"
                        + " FROM " + SurveyField.class.getCanonicalName() + " f "
                        + " JOIN f.obj fo "
                        + " WHERE fo.surveyId = :surveyId",
                ImmutableMap.of("surveyId", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        Set<Long> businessUnitMainFieldIds = surveyFieldObjects.stream()
                .filter(f -> Boolean.TRUE.equals(f.get("businessUnitMainField")))
                .map((f) -> Long.valueOf(f.get("fieldId").toString()))
                .collect(Collectors.toSet());
        Set<Long> businessUnitDepartmentMainFieldIds = surveyFieldObjects.stream()
                .filter(f -> Boolean.TRUE.equals(f.get("businessUnitDepartmentMainField")))
                .map((f) -> Long.valueOf(f.get("fieldId").toString()))
                .collect(Collectors.toSet());
        Set<Long> areaMainFieldIds = surveyFieldObjects.stream()
                .filter(f -> Boolean.TRUE.equals(f.get("areaMainField")))
                .map((f) -> Long.valueOf(f.get("fieldId").toString()))
                .collect(Collectors.toSet());
        // Se guarda únicamente las preguntas
        final List<OutstandingQuestion> questions = new ArrayList<>(outstandingSurvey.getPreguntasRespondidas().size());
        final Date now = new Date();
        final Map<Long, List<OutstandingAnswer>> answersByFieldObjIdIndex = outstandingSurvey.getPreguntasRespondidas().stream()
                .collect(Collectors.toMap(
                        validator::getObjIdByFielId,
                        OutstandingQuestion::getRespuesta
                ));
        final ConditionalValidatorCache instance = ConditionalValidatorCache.getInstance();
        final ConditionalValidator conditionalValidator = instance.get(
                outstandingSurvey.getId(),
                SurveyRequestMode.parse(outstandingSurvey.getSurveyRequestMode()),
                outstandingSurvey.getConditionalValidatorCacheId(),
                validator.getModule()
        );
        for (final OutstandingQuestion question : outstandingSurvey.getPreguntasRespondidas()) {
            //Obtiene una lista de las respuestas de esa pregunta
            List<OutstandingAnswer> copyAnswers;
            final ISurveyField<SurveyFieldObjectDTO> field = validator.getFieldByFieldId(question.getFieldId());
            boolean isFiller = doCopy || validator.isFillAvailable(field, loggedUser);
            if (isFiller) {
                copyAnswers = new ArrayList<>(question.getRespuesta());
            } else {
                copyAnswers = validator.getSavedOutstandingAnswersListByFieldId(field.getObj().getField_id());
            }
            boolean skippedConditionedField = !copyAnswers.isEmpty()
                    && conditionalValidator.isConditioned(field.getId())
                    && !SurveyField.TYPE_FIELD_ARRAY.equals(field.getType())
                    && !conditionalValidator.isFieldShownByAnyAnswer(field.getId());
            if (skippedConditionedField) {
                getLogger().debug(
                        "Clear field {} value because it is conditioned and not shown by any answer for outstanding survey id {}",
                        field.getId(),
                        outstandingSurvey.getId()
                );
                copyAnswers = new ArrayList<>();
            }
            if (isValidationEnabled(outstandingSurvey, doCopy, isFiller, isFinishing)) {
                try {
                    validateQuestionAnswers(outstandingSurvey, question, field, validator, answersByFieldObjIdIndex);
                } catch (final Exception e) {
                    getLogger().error(
                            "Failed to validate question answers for field {} of survey {} and outstanding survey {}",
                            question.getId(),
                            outstandingSurvey.getCuestionario().getId(),
                            outstandingSurvey.getId(),
                            e
                    );
                    final IFormCaptureDAO formDao = getBean(IFormCaptureDAO.class);
                    formDao.logError(outstandingSurvey.getId(), e, loggedUser);
                    if (LicenseUtil.isDevelopment()) {
                        throw e;
                    }
                }
            }
            final int numberQuestionAnswers = copyAnswers.size();
            if (!SurveyField.TYPE_SIGNATURE.equals(field.getType())
                    && ((isFiller && (numberQuestionAnswers > 0 || SurveyField.TYPE_SECCION.equals(field.getType())))
                    && (
                    (autorizationIndex.equals(0L) && Objects.equals(question.getFilledAutorizationPoolIndex(), 1l))
                            || autorizationIndex.equals(question.getFilledAutorizationPoolIndex()))
            )) {
                question.setFillOutDate(now);
                question.setFilledByUserId(loggedUser.getId());
            } else {
                question.setFilledAutorizationPoolIndex(validator.getFilledAutorizationPoolIndex(question.getId()));
                question.setFillOutDate(validator.getFillOutDate(question.getId()));
                question.setFilledByUserId(validator.getFilledByUserId(question.getId()));
            }
            if (
                    !doCopy
                            && isFinishing
                            && isFiller
                            && (
                            SurveyField.TYPE_SIGNATURE.equals(field.getType())
                                    || SurveyField.TYPE_SECCION.equals(field.getType())
                    )
                            && (
                            (Objects.equals(autorizationIndex, 0L) && Objects.equals(question.getFilledAutorizationPoolIndex(), 1L))
                                    || Objects.equals(autorizationIndex, question.getFilledAutorizationPoolIndex())
                    )
            ) {
                progressStateId = validator.getAttendProgressState(question);
                stage = validator.getNextAuthorizationStage(
                        question,
                        autorizationIndex,
                        outstandingSurvey.getId(),
                        outstandingSurvey.getRequestId(),
                        SurveyRequestMode.parse(outstandingSurvey.getSurveyRequestMode()),
                        outstandingSurvey.getConditionalValidatorCacheId()
                );
            }
            /**
             * Se eliminan las relaciones a todas las respuestas de las preguntas
             * para copiarlas por separado al iterar `copyAnswers`
             */
            question.setRespuesta(null);
            if (Objects.equals(question.getScoreId(), 0l)) {
                question.setScoreId(null);
            }
            final OutstandingQuestion savedQuestion = makePersistent(question, doCopy);
            if (savedQuestion == null) {
                MeasureTime.stop(oStart, "Elapsed time in failed save of null question for id " + outstandingSurvey.getId());
                return result;
            } else if (!doCopy) {
                // Se agrega información para validar sociedad/departamento/area
                field.getObj().setBusinessUnitMainField(
                        businessUnitMainFieldIds.contains(field.getId())
                );
                field.getObj().setBusinessUnitDepartmentMainField(
                        businessUnitDepartmentMainFieldIds.contains(field.getId())
                );
                field.getObj().setAreaMainField(
                        areaMainFieldIds.contains(field.getId())
                );
            }
            validator.addOutstandingQuestion(savedQuestion);
            final List<OutstandingAnswer> savedAnswers = new ArrayList<>(copyAnswers.size());
            for (final OutstandingAnswer answer : copyAnswers) {
                OutstandingAnswer savedAnswer = SerializationUtils.clone(answer);
                // Se limpian valores
                savedAnswer.setId(-1L);
                savedAnswer.setDeleted(0);
                savedAnswer.setPregunta(savedQuestion);
                // Si es copia en este momento se asigna el id del pendiente
                if (!doCopy) {
                    savedAnswer.setOutstandingSurveyId(outstandingSurvey.getId());
                }
                if (savedQuestion.getFillOutDate() == null) {
                    savedAnswer.setFechaCaptura(now);
                } else {
                    savedAnswer.setFechaCaptura(question.getFillOutDate());
                }

                savedAnswer = makePersistent(savedAnswer, doCopy);
                if (savedAnswer == null) {
                    getLogger().error(" "
                                    + "[surveyId: {}, outstandingSurveysId: {}, status: {}] Answer of question {} not saved correctly, returning savehandle.", new Object[]{
                                    surveyId, outstandingSurvey.getId(), outstandingSurvey.getEstatus(), savedQuestion.getId()
                            }
                    );
                    MeasureTime.stop(oStart, "Elapsed time in misconfigured save of outstanding answers for id " + outstandingSurvey.getId());
                    return result;
                }
                savedAnswers.add(savedAnswer);
            }
            getLogger().info("Son {} respuestas para el campo {}", savedAnswers.size(), field.getId());
            savedQuestion.setRespuesta(savedAnswers);
            questions.add(savedQuestion);
        }
        outstandingSurvey.setPreguntasRespondidas(questions.isEmpty() ? null : questions);
        // Se itera por segunda vez para verificar las respuestas que dependen de todas las respuestas
        for (final OutstandingQuestion question : outstandingSurvey.getPreguntasRespondidas()) {
            final ISurveyField<SurveyFieldObjectDTO> field = validator.getFieldByFieldId(question.getFieldId());
            boolean isFiller = doCopy || validator.isFillAvailable(field, loggedUser);
            if (isValidationEnabled(outstandingSurvey, doCopy, isFiller, isFinishing)) {
                try {
                    validateFormAnswers(outstandingSurvey, question, field, validator);
                } catch (final Exception e) {
                    getLogger().error(
                            "Failed to validate question answers for field {} of survey {} and outstanding survey {}",
                            question.getId(),
                            outstandingSurvey.getCuestionario().getId(),
                            outstandingSurvey.getId(),
                            e
                    );
                    final IFormCaptureDAO formDao = getBean(IFormCaptureDAO.class);
                    formDao.logError(outstandingSurvey.getId(), e, loggedUser);
                    if (LicenseUtil.isDevelopment()) {
                        throw e;
                    }
                }
            }
        }
        if (getLogger().isDebugEnabled()) {
            getLogger().debug(" "
                            + "[surveyId: {}, outstandingSurveysId: {}, status: {}] Saved {} questions, saving 'OutstandingSurveys' instance with id {}.", new Object[]{
                            surveyId, outstandingSurvey.getId(), outstandingSurvey.getEstatus(), questions.size(), outstandingSurvey.getId()
                    }
            );
        }
        if (shouldUpdateFechaInicio(outstandingSurvey, isFinishing)) {
            outstandingSurvey.setDteFechaInicio(now);
        }
        if (isFinishing) {
            outstandingSurvey.setDteFechaCierre(now);
            outstandingSurvey.setProgressStateId(progressStateId);
            outstandingSurvey.setStage(stage);
        }
        outstandingSurvey.setLastModifiedBy(loggedUser.getId());
        outstandingSurvey.setLastModifiedDate(new Date());
        outstandingSurvey = makePersistent(outstandingSurvey, doCopy);
        // Ya se tiene el outstanding id, se actualiza el FK  en ansswers
        if (doCopy) {
            outstandingSurvey = updateOutstandingAnswerFK(outstandingSurvey, questions);
        }
        if (outstandingSurvey == null) {
            MeasureTime.stop(oStart, "Elapsed time in failed save of outstanding answers for id " + (outstandingSurvey != null ? outstandingSurvey.getId() : null));
            return result;
        }

        final FormCaptureDTO data = new FormCaptureDTO();
        data.setDocumentId(outstandingSurvey.getDocumentId());
        data.setOutstandingQuestions(new LinkedHashSet<>(outstandingSurvey.getPreguntasRespondidas()));
        data.setLoggedUser(loggedUser);
        final OutstandingSurveyDTO outstandingData = new OutstandingSurveyDTO(
                outstandingSurvey.getId(),
                surveyId,
                outstandingSurvey.getDocumentId(),
                outstandingSurvey.getRequestId(),
                outstandingSurvey.getBusinessUnitDepartmentId(),
                outstandingSurvey.getArchived(),
                outstandingSurvey.getDeleted(),
                outstandingSurvey.getDteFechaInicio()
        );
        data.setOutstandingData(outstandingData);
        data.setRequestId(outstandingSurvey.getRequestId());
        data.setBusinessUnitDepartmentId(outstandingSurvey.getBusinessUnitDepartmentId());
        getEntityManager().flush();
        if (!doCopy) {
            final IFormCaptureDAO formDao = getBean(IFormCaptureDAO.class);
            formDao.save(data, validator, loggedUser);
            if (outstandingSurvey.getRequestId() != null && outstandingSurvey.getRequestId() > 0) {
                formDao.migrateToLatestAnswersTable(
                        outstandingSurvey.getId(),
                        outstandingSurvey.getRequestId(),
                        surveyId
                );
            }
        }
        result.setSuccessMessage("Se han guardado sus respuestas");
        result.setSavedId(outstandingSurvey.getId().toString());
        result.setOperationEstatus(1);
        result.setExtraJson("{\"estatus\":" + outstandingSurvey.getEstatus() + "}");

        if (isFinishing && !doCopy) {
            Long auditIndividualId = HQL_findSimpleLong(""
                            + " SELECT c.id "
                            + " FROM " + AuditIndividual.class.getCanonicalName() + " c "
                            + " WHERE c.fill.id = :outstandingSurveyId",
                    "outstandingSurveyId", outstandingSurvey.getId()
            );
            if (auditIndividualId != null && auditIndividualId != 0) {
                final IAuditIndividualDAO auditInd = getBean(IAuditIndividualDAO.class);
                final String fillMessage = getTag("theQuestionaireWasFilled");
                final Integer accepted = HQL_findSimpleInteger(""
                        + " SELECT ind.audit.type.acceptedByAudited"
                        + " FROM " + AuditIndividual.class.getCanonicalName() + " ind "
                        + " WHERE ind.id = :auditIndividualId", "auditIndividualId", auditIndividualId);
                try {
                    auditInd.saveComment(auditIndividualId, fillMessage, loggedUser);
                } catch (final QMSException ex) {
                    throw new RuntimeException(ex);
                }
                Long auditId = HQL_findSimpleLong(""
                        + " SELECT c.audit.id"
                        + " FROM " + AuditIndividual.class.getCanonicalName() + " c"
                        + " WHERE c.id = :auditIndividualId", "auditIndividualId", auditIndividualId
                );
                if (accepted.equals(1)) {
                    numberAnswers = auditInd.finishFill(auditIndividualId, fillMessage, loggedUser);
                    Long audits = HQL_findSimpleLong(""
                                    + " SELECT COUNT(a.id)"
                                    + " FROM " + Audit.class.getCanonicalName() + " a"
                                    + " JOIN a.audits au"
                                    + " WHERE"
                                    + " a.id = :auditId"
                                    + " AND au.deleted = 0"
                                    + " AND au.actualEnd IS NULL"
                                    + " GROUP BY a.id"
                                    + " HAVING COUNT(a.id) > 0",
                            "auditId", auditId
                    );
                    if (audits == 0) {
                        final Map<String, Object> params = new HashMap<>();
                        params.put("auditId", auditId);
                        params.put("actualEnd", now);
                        Integer plan = HQL_updateByQuery(""
                                + " UPDATE " + Audit.class.getCanonicalName() + " "
                                + " SET actualEnd = :actualEnd"
                                + " WHERE id = :auditId", params
                        );
                        getLogger().info("Se actualiza " + plan + " el plan de auditoría");
                    }
                } else {
                    numberAnswers = auditInd.acceptResultsAutomatically(auditIndividualId, fillMessage, loggedUser);
                    numberAnswers += HQL_updateByQuery(""
                            + " UPDATE " + Audit.class.getName() + " "
                            + " SET status = " + Audit.STATUS_DONE + " "
                            + " WHERE id = :auditId", "auditId", auditId
                    );
                }
                getLogger().debug("{} audits has been updated", numberAnswers);
            } else {
                Map<String, Object> validateParent = HQL_findSimpleMap(""
                                + " SELECT new map("
                                + " count(resp) AS quantity, "
                                + " po.id AS pollId"
                                + " )"
                                + " FROM "
                                + PollRespondent.class.getCanonicalName() + " resp "
                                + "," + PollRespondent.class.getCanonicalName() + " r "
                                + " JOIN resp.outstandingSurveys os "
                                + " JOIN resp.poll po "
                                + " JOIN r.outstandingSurveys o "
                                + " JOIN r.poll p "
                                + " WHERE"
                                + " os.status IN ("
                                + OutstandingSurveys.ESTATUS_EN_PROCESO + ","
                                + OutstandingSurveys.ESTATUS_EN_PROCESO_CONTINUAR_MAS_TARDE + ","
                                + OutstandingSurveys.ESTATUS_EN_PROCESO_PARCIALMENTE_LLENA
                                + " )"
                                + " AND p.id = po.id "
                                + " AND o.id = :outstandingSurveyId"
                                + " GROUP BY po.id",
                        "outstandingSurveyId", outstandingSurvey.getId()
                );
                if (validateParent.get("quantity") != null && Long.parseLong(validateParent.get("quantity").toString()) == 0) {
                    HQL_updateByQuery(""
                                    + " UPDATE " + Poll.class.getCanonicalName() + " po "
                                    + " SET po.status = " + Poll.STATUS_DONE
                                    + " WHERE po.id = :pollId",
                            "pollId", validateParent.get("pollId")
                    );
                }
            }
        }
        MeasureTime.stop(oStart, "Elapsed time saving outstanding answers for id " + outstandingSurvey.getId());
        return result;
    }

    private OutstandingSurveys updateOutstandingAnswerFK(
            final OutstandingSurveys outstandingSurvey,
            final List<OutstandingQuestion> questions
    ) {
        if (questions == null || questions.isEmpty()) {
            return outstandingSurvey;
        }
        for (final OutstandingQuestion question : questions) {
            if (question.getRespuesta() == null || question.getRespuesta().isEmpty()) {
                continue;
            }
            for (final OutstandingAnswer answer : question.getRespuesta()) {
                answer.setOutstandingSurveyId(outstandingSurvey.getId());
            }
        }
        return makePersistent(outstandingSurvey, false);
    }


    private boolean shouldUpdateFechaInicio(final OutstandingSurveys outstandingSurvey, final boolean isFinishing) {
        final boolean fechaInicioMissing = outstandingSurvey.getDteFechaInicio() == null;
        if (!fechaInicioMissing) {
            return  false;
        }
        return isFinishingSeccionOrSignature(outstandingSurvey, isFinishing);
    }

    private boolean isValidationEnabled(
            final OutstandingSurveys outstandingSurvey,
            final Boolean doCopy,
            final boolean isFiller,
            final boolean isFinishing
    ) {
        if (!isFiller) {
            return false;
        }
        if (doCopy) {
            return false;
        }
        return isFinishingSeccionOrSignature(outstandingSurvey, isFinishing);
    }

    private boolean isFinishingSeccionOrSignature(
            final OutstandingSurveys outstandingSurvey,
            final boolean isFinishing
    ) {
        if (Boolean.TRUE.equals(isFinishing)) {
            return true;
        }
        return !Objects.equals(OutstandingSurveys.ESTATUS_EN_PROCESO_CONTINUAR_MAS_TARDE, outstandingSurvey.getEstatus());
    }

    private void validateQuestionAnswers(
            final OutstandingSurveys outstandingSurvey,
            final OutstandingQuestion question,
            final ISurveyField<SurveyFieldObjectDTO> field,
            final FillHelper validator,
            final Map<Long, List<OutstandingAnswer>> answersByFieldObjIdIndex
    ) throws QMSException {
        if (outstandingSurvey == null || outstandingSurvey.getId() == null || Objects.equals(outstandingSurvey.getId(), -1L)) {
            return;
        }

        if (Module.FORMULARIE.equals(validator.getModule())) {
            if (outstandingSurvey.getRequestId() == null || Objects.equals(outstandingSurvey.getRequestId(), -1L)) {
                getLogger().error("Missing requestId for outstandingSurvey fields validator for record {}", outstandingSurvey.getId());
                return;
            }
            if (outstandingSurvey.getDocumentId() == null || Objects.equals(outstandingSurvey.getDocumentId(), -1L)) {
                getLogger().error("Missing documentId for outstandingSurvey fields validator for record {}", outstandingSurvey.getId());
                return;
            }
        }
        final SurveyField.TYPE fieldType = SurveyField.TYPE.fromValue(field.getType());
        if (SurveyField.TYPE.TEXT_DATE.equals(fieldType)) {
            validateDateSelectorField(field, question, validator);
        } else if (SurveyField.TYPE.TEXT_FIELD.equals(fieldType) || SurveyField.TYPE.TEXT_FIELD_ARRAY.equals(fieldType)) {
            validateTextField(
                    outstandingSurvey,
                    field,
                    question,
                    validator,
                    answersByFieldObjIdIndex
            );
        }
    }

    private void validateFormAnswers(
            final OutstandingSurveys outstandingSurvey,
            final OutstandingQuestion question,
            final ISurveyField<SurveyFieldObjectDTO> field,
            final FillHelper validator
    ) throws QMSException {
        if (outstandingSurvey == null || outstandingSurvey.getId() == null || Objects.equals(outstandingSurvey.getId(), -1L)) {
            return;
        }
        if (Module.FORMULARIE.equals(validator.getModule())) {
            if (outstandingSurvey.getRequestId() == null || Objects.equals(outstandingSurvey.getRequestId(), -1L)) {
                getLogger().error("Missing requestId for outstandingSurvey fields form validator for record {}", outstandingSurvey.getId());
                return;
            }
            if (outstandingSurvey.getDocumentId() == null || Objects.equals(outstandingSurvey.getDocumentId(), -1L)) {
                getLogger().error("Missing documentId for outstandingSurvey fields form validator for record {}", outstandingSurvey.getId());
                return;
            }
        }
        final SurveyField.TYPE fieldType = SurveyField.TYPE.fromValue(field.getType());
        final SurveyWeightingType weightingType = SurveyWeightingType.fromValue(field.getObj().getWeightingType());
        final boolean isSelectedWeightedField = "t".equals(field.getObj().getSelectedWeightedField());
        if (SurveyField.TYPE.FORM_WEIGHTING_RESULT.equals(fieldType)) {
            if (isSelectedWeightedField) {
                getLogger().debug("No validation required for selected weighting values {}", field);
                return;
            }
            if (weightingType != null) {
                switch (weightingType) {
                    default:
                        validateFormWeightingResultField(
                                outstandingSurvey,
                                field,
                                question,
                                validator
                        );
                        break;
                    case INIT:
                        getLogger().debug("No validation required for weighting static values {}", field);
                        break;
                }
            } else {
                validateFormWeightingResultField(
                        outstandingSurvey,
                        field,
                        question,
                        validator
                );
            }
        }
    }

    private void validateDateSelectorField(
            final ISurveyField<SurveyFieldObjectDTO> field,
            final OutstandingQuestion question,
            final FillHelper validator
    ) throws QMSException {
        final ISurveyFieldObject obj = field.getObj();
        final boolean includeTime = Objects.equals(obj.getIncludeTime(), "t");
        if (!includeTime) {
            return;
        }
        final boolean restrictPastDates = Objects.equals(obj.getRestrictPastDates(), "t");
        if (!restrictPastDates) {
            return;
        }
        final boolean hasMaxPastHours = obj.getMaxPastHours() != null && obj.getMaxPastHours() > 0;
        if (!hasMaxPastHours) {
            return;
        }
        final List<OutstandingAnswer> respuesta = question.getRespuesta();
        if (respuesta == null || respuesta.isEmpty()) {
            return;
        }
        final String dateAnswer = respuesta.get(0).getDescripcion();
        if (dateAnswer == null || dateAnswer.isEmpty()) {
            return;
        }
        final Date textDate = Utilities.parseDateBy(dateAnswer, "dd/MM/yyyy HH:mm");
        if (textDate == null) {
            return;
        }
        final Integer defaultDateValue = obj.getDefaultDateValue();
        final boolean customAnswer = Objects.equals(defaultDateValue, 0);
        if (customAnswer && obj.getDefaultValue() != null && !obj.getDefaultValue().isEmpty()) {
            final Date defaultValue = Utilities.parseDateBy(obj.getDefaultValue(), "yyyy-MM-dd'T'HH:mm");
            if (textDate.equals(defaultValue)) {
                return;
            }
        }
        final boolean hasFillRequestDate = Objects.equals(defaultDateValue, 1);
        if (hasFillRequestDate) {
            final Date fillRequestDate = Utilities.parseDateBy(validator.getFillingRequestDate(), "yyyy-MM-dd'T'HH:mm");
            if (textDate.equals(fillRequestDate)) {
                return;
            }
        }
        final boolean hasApprovalFormDate = Objects.equals(defaultDateValue, 2);
        if (hasApprovalFormDate) {
            final Date approvalFormatDate = Utilities.parseDateBy(validator.getApprovalFormDate(), "yyyy-MM-dd'T'HH:mm");
            if (textDate.equals(approvalFormatDate)) {
                return;
            }
        }
        final List<OutstandingAnswer> originalAnswers = validator.getSavedOutstandingAnswersListByFieldId(field.getObj().getField_id());
        if (originalAnswers != null && !originalAnswers.isEmpty() && Objects.equals(dateAnswer, originalAnswers.get(0).getDescripcion())) {
            return;
        }
        if (validator.getHasFormApproverRole()) {
            return;
        }
        Date minDate = getFieldMinDate(validator, obj);
        if (textDate.before(minDate)) {
            final String error = getTag("dateSelector_invalid_past_date")
                    .replace(":minDate", Utilities.formatDateBy(minDate, "dd/MM/yyyy HH:mm"))
                    .replace(":currentDate", Utilities.formatDateBy(textDate, "dd/MM/yyyy HH:mm"));
            getLogger().error("Failed to save text answer {} with error {}", question.getId(), error);
            throw new QMSException(error);
        }
    }

    private Date getFieldMinDate(FillHelper validator, ISurveyFieldObject obj) {
        Date now = new Date();
        Integer currentRecurrence = validator.getCurrentRecurrence();
        Date dteFechaInicio = validator.getDteFechaInicio();
        if (currentRecurrence != null && currentRecurrence > 1 && dteFechaInicio != null) {
            // Para formularios retornados, se utiliza la fecha de creación de la solicitud
            now = new Date(dteFechaInicio.getTime());
        }
        Date minDate = new Date(now.getTime() - TimeUnit.HOURS.toMillis(obj.getMaxPastHours()));
        // Se restan dos minutos a fecha mínima para evitar error por lentitud al guardar
        minDate = new Date(minDate.getTime() - TimeUnit.MINUTES.toMillis(2));
        return minDate;
    }

    private void validateTextField(
            final OutstandingSurveys outstandingSurvey,
            final ISurveyField<SurveyFieldObjectDTO> field,
            final OutstandingQuestion question,
            final FillHelper validator,
            final Map<Long, List<OutstandingAnswer>> answersByFieldObjIdIndex
    ) throws QMSException {
        if (Objects.equals(field.getObj().getSummationQuestion(), "t")) {
            validateTextWithSummationField(field, question, validator, answersByFieldObjIdIndex);
            return;
        }
        final List<OutstandingAnswer> respuesta = question.getRespuesta();
        if (respuesta == null || respuesta.isEmpty()) {
            return;
        }
        final OutstandingAnswer singleAnswer = respuesta.get(0);
        final String textAnswer = singleAnswer.getDescripcion();
        if (textAnswer == null || textAnswer.isEmpty()) {
            return;
        }
        final SurveyFieldAnswerType answerType = getSurveyFieldAnswerType(field, singleAnswer, validator);
        if (!SurveyFieldAnswerType.CURRENCY_USD_TO_MXN.equals(answerType)) {
            return;
        }
        final Double targetConversion;
        try {
            targetConversion = Utilities.parseDouble(textAnswer);
            if (targetConversion == null) {
                return;
            }
        } catch (final Exception e) {
            final String error = getTag("textField_invalid_currency_usd_to_mxn");
            getLogger().error("Failed to save answer {} with error {}", new Object[]{
                    question.getId(),
                    error
            });
            throw new QMSException(error, e);
        }
        if (targetConversion < 0) {
            final String error = getTag("textField_invalid_exchange_negative_value");
            throw new QMSException(error);

        }
        final List<OutstandingAnswer> originalAnswers = validator.getSavedOutstandingAnswersListByFieldId(field.getObj().getField_id());
        if (originalAnswers != null && !originalAnswers.isEmpty() && Objects.equals(textAnswer, originalAnswers.get(0).getDescripcion())) {
            return;
        }
        final Long exchangeRateId = singleAnswer.getExchangeRateId();
        if (exchangeRateId == null) {
            final String error = getTag("textField_invalid_exchange_rate_id");
            throw new QMSException(error);
        }
        final ExchangeRateDTO exchangeRate = validator.getCurrentExchangeRate();
        if (exchangeRate == null) {
            final String error = getTag("textField_missing_exchange_rate_config");
            getLogger().error("Failed to save answer {} with error {}", new Object[]{
                    question.getId(),
                    error
            });
            throw new QMSException(error);
        }
        if (!Objects.equals(exchangeRate.getId(), exchangeRateId)) {
            final String error = getTag("textField_mismatch_exchange_rate_id");
            getLogger().error("Failed to save answer {} with error {}", new Object[]{
                    question.getId(),
                    error
            });
            throw new QMSException(error);

        }
        final Date exchangeRateDate = singleAnswer.getExchangeRateDate();
        final String exchangeRateDateString = Utilities.formatDate(exchangeRateDate);
        final String syncDateString = Utilities.formatDate(exchangeRate.getSyncDate());
        if (!Objects.equals(syncDateString, exchangeRateDateString)) {
            final String error = getTag("textField_mismatch_exchange_rate_date");
            getLogger().error("Failed to save answer {} with error {}", new Object[]{
                    question.getId(),
                    error
            });
            throw new QMSException(error);
        }

        final Double exchangeRateSource = singleAnswer.getExchangeRateSource();
        if (exchangeRateSource == null || exchangeRate.getConversionRate() == null || Objects.equals(exchangeRate.getConversionRate(), 0.0)) {
            final String error = getTag("textField_invalid_exchange_rate_source");
            getLogger().error("Failed to save answer {} with error {}", new Object[]{
                    question.getId(),
                    error
            });
            throw new QMSException(error);
        }
        final Double convertedValue = Utilities.formatDouble(exchangeRate.getConversionRate() * exchangeRateSource, 2);
        if (!Objects.equals(convertedValue, targetConversion)) {
            final String error = getTag("textField_invalid_exchange_rate_conversion");
            getLogger().error("Failed to save answer {} with error {}", new Object[]{
                    question.getId(),
                    error
            });
            throw new QMSException(error);
        }
    }

    private void validateTextWithSummationField(
            final ISurveyField<SurveyFieldObjectDTO> field,
            final OutstandingQuestion question,
            final FillHelper validator,
            final Map<Long, List<OutstandingAnswer>> answersByFieldObjIdIndex
    ) throws QMSException {
        final List<OutstandingAnswer> respuesta = question.getRespuesta();
        if (respuesta == null || respuesta.isEmpty()) {
            final String error = getTag("textField_missing_summation_answer");
            getLogger().trace(
                    "Skipped summation answer {} for outstanding survey {} with error {}",
                    question.getId(),
                    validator.getOutstandingSurveyId(),
                    error
            );
            return;
        }
        final OutstandingAnswer singleAnswer = respuesta.get(0);
        final String textAnswer = singleAnswer.getDescripcion();
        if (textAnswer == null || textAnswer.isEmpty()) {
            final String error = getTag("textField_missing_summation_answer");
            throw new QMSException(error);
        }
        final Double targetValue;
        try {
            targetValue = Utilities.parseDouble(textAnswer);
            if (targetValue == null) {
                return;
            }
        } catch (final Exception e) {
            final String error = getTag("textField_invalid_summation_value");
            getLogger().error("Failed to compute summation answer {} with error {}", question.getId(), error);
            throw new QMSException(error, e);
        }
        if (targetValue < 0) {
            final String error = getTag("textField_invalid_summation_value");
            throw new QMSException(error);
        }
        final Double computedValue = validator.getComputedSummationForField(field, answersByFieldObjIdIndex);
        if (!Objects.equals(computedValue, targetValue)) {
            final String error = getTag("textField_mismatch_summation_value")
                    .replace(":targetSummation", targetValue.toString())
                    .replace(":computedSummation", computedValue.toString());
            getLogger().error("Mismatched summation valor for answer {} with error {}", question.getId(), error);
            throw new QMSException(error);
        }
    }

    private void validateFormWeightingResultField(
            final OutstandingSurveys outstandingSurvey,
            final ISurveyField<SurveyFieldObjectDTO> field,
            final OutstandingQuestion question,
            final FillHelper validator
    ) throws QMSException {
        final List<OutstandingAnswer> respuesta = question.getRespuesta();
        if (respuesta == null || respuesta.isEmpty()) {
            final String error = getTag("textField_missing_form_weighting_result_answer");
            getLogger().trace(
                    "Skipped form weighted grade result answer {} for outstanding survey {} with error {}",
                    question.getId(),
                    validator.getOutstandingSurveyId(),
                    error
            );
            return;
        }
        final OutstandingAnswer singleAnswer = respuesta.get(0);
        final String textAnswer = singleAnswer.getDescripcion();
        if (textAnswer == null || textAnswer.isEmpty()) {
            final String error = getTag("textField_missing_form_weighting_result_answer");
            throw new QMSException(error);
        }
        final Double targetValue;
        try {
            targetValue = Utilities.parseDouble(textAnswer);
            if (targetValue == null) {
                return;
            }
        } catch (final Exception e) {
            final String error = getTag("textField_invalid_form_weighting_result_value");
            getLogger().error("Failed to compute weighted grade result answer {} with error {}", question.getId(), error);
            throw new QMSException(error, e);
        }
        final Double computedValue = validator.getComputedWeightedResultForField(
                outstandingSurvey,
                field
        );
        if (!Objects.equals(computedValue, targetValue)) {
            final String computedString = computedValue == null ? "null" : computedValue.toString();
            final String error = getTag("textField_mismatch_summation_value")
                    .replace(":targetSummation", targetValue.toString())
                    .replace(":computedSummation", computedString);
            getLogger().error("Mismatched weighted grade result valor for answer {} with error {}", question.getId(), error);
            throw new QMSException(error);
        }
    }

    @Nullable
    private SurveyFieldAnswerType getSurveyFieldAnswerType(
            final ISurveyField<SurveyFieldObjectDTO> field,
            final OutstandingAnswer answer,
            final FillHelper validator
    ) {
        if (SurveyField.TYPE.TEXT_FIELD_ARRAY.getValue().equals(field.getType())) {
            return validator.getOptionAnswerType(answer.getOpcionId());
        } else if (SurveyField.TYPE.TEXT_FIELD.getValue().equals(field.getType())) {
            return SurveyFieldAnswerType.fromValue(field.getObj().getAnswerType());
        } else {
            return null;
        }
    }

    @OnPollAnswer
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle makePollAttended(OutstandingSurveys outstandingSurveys, ILoggedUser loggedUser) throws IOException, QMSException {
        return save(outstandingSurveys, Module.POLL, loggedUser);
    }

    /**
     * Función que devuelve el estatus de un pendiente
     *
     * @param id id del pendiente
     * @return
     * <AUTHOR> Cavazos Galindo
     * @since 2.3.2.133
     */
    private Short getSavedOutstandingSurveyStatus(Long id) {
        Short status = 0;
        //Si id<=1 regresa 0
        if (id == null || id < 1) {
            return status;
        }
        //Busca en la base de datos el pendiente usando su identificador.
        return Short.valueOf(HQL_findSimpleString(""
                + " SELECT c.estatus"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", id));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public OutstandingSurveys calculateAverageIndividual(OutstandingSurveys ent) {
        Double aiScore;
        Long id = HQL_findSimpleLong(""
                + "SELECT c.id "
                + "FROM DPMS.Mapping.AuditIndividual c "
                + "WHERE c.fill.id = :outstandingSurvey", "outstandingSurvey", ent.getId());
        getLogger().trace("calculating average of auditIndividual {}", id);
        String query = ""
                + "SELECT c FROM "
                + "DPMS.Mapping.AuditScoreSource c "
                + "WHERE c.auditIndividualId = :id "
                + "AND c.score != -1 ";
        getLogger().trace(query);
        List<AuditScoreSource> l = HQL_findByQuery(query, "id", id);
        getLogger().trace("total answers {}", l.size());
        if (l.isEmpty()) {
            aiScore = 0D;
        } else {
            Double sum = 0D;
            for (AuditScoreSource source : l) {
                sum += source.getScore();
            }
            aiScore = sum / l.size();
        }
        ent.setScore(aiScore);
        getLogger().trace("total promedio {}", aiScore);
        return makePersistent(ent, false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Audit calculateAveragePlan(OutstandingSurveys ent) {
        Double aScore;
        Long id = HQL_findSimpleLong(""
                + "SELECT c.audit.id "
                + "FROM DPMS.Mapping.AuditIndividual c "
                + "WHERE c.fill.id = :outstandingSurvey", "outstandingSurvey", ent.getId());
        getLogger().trace("calculating average of auditIndividual {}", id);
        String query = ""
                + "SELECT c FROM "
                + "DPMS.Mapping.AuditScoreSource c "
                + "WHERE c.auditId = :id "
                + "AND c.score != -1 ";
        getLogger().trace(query);
        List<AuditScoreSource> l = HQL_findByQuery(query, "id", id);
        getLogger().trace("total answers {}", l.size());
        if (l.isEmpty()) {
            aScore = 0D;
        } else {
            Double sum = 0D;
            for (AuditScoreSource source : l) {
                sum += source.getScore();
            }
            aScore = sum / l.size();
        }

        Audit audit;
        IAuditDAO dao = Utilities.getBean(IAuditDAO.class);
        audit = dao.HQLT_findById(id);
        audit.setScore(aScore);
        audit = dao.makePersistent(audit);
        getLogger().trace("total promedio {}", aScore);
        return audit;
    }

    /**
     * @param request
     * @param attendants: 1. Contiene un mapa de todas las preguntas en el orden que se van a responder 2. El mapa esta construido asi: 2.1.
     *                    La llave es el FIELD_OBJECT_ID del field 2.2. El valor es una lista de todas las personas que les corresponde llenar esa pregunta
     * @param loggedUser
     */
    private void setRequestWorkflow(Request request, WorkflowAuthRole authRole, List<OutstandingSurveysAttendant> attendants, ILoggedUser loggedUser) throws IOException, QMSException {
        Long organizationalUnitId = null;
        if (request.getOrganizationalUnit() != null) {
            organizationalUnitId = request.getOrganizationalUnit().getId();
        }
        if (organizationalUnitId == null && request.getBusinessUnit() != null) {
            organizationalUnitId = request.getBusinessUnit().getOrganizationalUnitId();
        }
        if (organizationalUnitId == null && request.getDepartment() != null) {
            BusinessUnitLite b = HQLT_findById(BusinessUnitLite.class, request.getDepartment().getBusinessUnitId());
            organizationalUnitId = b.getOrganizationalUnitId();
        }
        List<OutstandingSurveysAttendant> flow = getFilteredAttendants(attendants);
        // Los flujos de autorizacion para llenado son de tipo global
        final Workflow savedFlow = getBean(IFlowDAO.class).save(
                flow,
                HQLT_findById(OrganizationalUnitSimple.class,
                        organizationalUnitId),
                Module.FORMULARIE,
                loggedUser.getId()
        );
        request.setFlujoId(savedFlow.getId());
        request = makePersistent(request, loggedUser.getId());
        updateWorkflowPreview(request.getId());
        getBean(IWorkflowDAO.class).workflowNextOrEnd(true, getBean(IRequestDAO.class), request, authRole, loggedUser);
    }

    /**
     * Regresa el flujo de llenado por sección o firma (responsable de llenado), este metodo es necesario debido a que 'attendants' contiene
     * el flujo de llenado pregunta por pregunta.
     *
     * @param attendants
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<OutstandingSurveysAttendant> getFilteredAttendants(List<OutstandingSurveysAttendant> attendants) {
        List<OutstandingSurveysAttendant> result = new ArrayList<>(attendants.size());
        Long temp;
        for (OutstandingSurveysAttendant attendant : attendants) {
            if (attendant.getFieldType() == null || !SurveyField.ATTENDANT_FIELDS.contains(attendant.getFieldType())) {
                continue;
            }
            temp = Long.valueOf(result.size() + 1);
            if (!Objects.equals(temp, attendant.getFillAutorizationPoolIndex())) {
                getLogger().error("OutstandingSurveysAttendant index should be '{}' but instead is using '{}'.", new Object[]{
                        temp, attendant.getFillAutorizationPoolIndex()
                });
            }
            result.add(attendant);
        }
        return result;
    }

    @Override
    @OnCompletedFillForm
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer finishFillForm(Request sol, Long outstandingSurveyId, ILoggedUser loggedUser) {
        return HQL_updateByQuery(""
                + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " SET c.status = " + OutstandingSurveys.STATUS.CLOSED.getValue()
                + " WHERE c.id = :outstandingSurveyId", "outstandingSurveyId", outstandingSurveyId
        );
    }

    @Override
    @OnCancelledFillForm
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer cancellFillForm(
            final Long requestId,
            final String reason,
            final Long outstandingSurveyId,
            final Boolean sendMail,
            final ILoggedUser loggedUser
    ) {
        Integer surveyCancelled = 0;
        if (outstandingSurveyId != null) {
            surveyCancelled = HQL_updateByQuery(""
                    + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                    + " SET c.status = " + OutstandingSurveys.STATUS.CANCELLED.getValue()
                    + " WHERE c.id = :id", "id", outstandingSurveyId
            );
        }
        if (requestId != null) {
            getBean(IRequestDAO.class).cancelSurveyRequest(requestId, reason, loggedUser);
        }
        return surveyCancelled;
    }

    @Override
    @OnCancelledFillForms
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean cancellFillForms(final List<FillInfoDTO> fills, final String comment, final ILoggedUser loggedUser) {
        final List<Long> outstandingSurveysIds = fills.stream()
                .filter(fill -> fill.getOutstandingSurveysId() != null)
                .map(fill -> fill.getOutstandingSurveysId())
                .collect(Collectors.toList());
        if (!outstandingSurveysIds.isEmpty()) {
            HQL_updateByQuery(""
                    + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                    + " SET c.status = " + OutstandingSurveys.STATUS.CANCELLED.getValue()
                    + " WHERE " + BindUtil.parseFilterList("c.id", outstandingSurveysIds)
            );
        }
        final List<Long> requestIds = fills.stream()
                .filter(fill -> fill.getRequestId() != null)
                .map(fill -> fill.getRequestId())
                .collect(Collectors.toList());
        if (!requestIds.isEmpty()) {
            return getBean(IRequestDAO.class).cancelRequests(requestIds, comment, loggedUser);
        }
        return true;
    }

    @Override
    @OnCancelledForm
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer cancelledForm(FormRequestorDTO cancelledFiller, String cancelByUser, String cancelByReason, Long outstandingSurveyId, ILoggedUser loggedUser) {
        Integer surveyCancelled = HQL_updateByQuery(""
                + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " SET c.status = " + OutstandingSurveys.STATUS.CANCELLED.getValue()
                + " WHERE c.id = :outstandingSurveyId", "outstandingSurveyId", outstandingSurveyId
        );
        getBean(IRequestDAO.class).cancelRequest(cancelledFiller.getRequestId(), cancelByReason, loggedUser);
        return surveyCancelled;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getOutstandingAutorizationPoolIndex(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return null;
        }
        final Long authorizationPoolIndex = HQL_findLong(""
                        + " SELECT MAX(apd.indice) "
                        + " FROM " + Request.class.getCanonicalName() + " c "
                        + " LEFT JOIN " + AutorizationPoolDetails.class.getCanonicalName() + " apd"
                        + " ON apd.requestId = c.id "
                        + " WHERE "
                        + " c.outstandingSurveysId = :outstandingSurveyId "
                        + " AND apd.accepted IS NOT NULL ",
                ImmutableMap.of("outstandingSurveyId", outstandingSurveyId)
        );
        return authorizationPoolIndex;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public WorkflowAuthRole getSurveyAuthRole(final Long outstandingSurveyId, final ILoggedUser loggedUser) {
        Long autorizationIndex = getOutstandingAutorizationPoolIndex(outstandingSurveyId);
        if (autorizationIndex == null) {
            autorizationIndex = 0l;
        }
        final WorkflowAuthRole role = getSurveyAuthRole(outstandingSurveyId, autorizationIndex, loggedUser);
        return role;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public WorkflowAuthRole getSurveyAuthRole(final Long outstandingSurveyId, final Long autorizationIndex, final ILoggedUser loggedUser) {
        if (outstandingSurveyId == null) {
            return WorkflowAuthRole.NONE;
        }
        final Long requestId = HQL_findLong(""
                        + " SELECT o.requestId "
                        + " FROM " + OutstandingSurveys.class.getCanonicalName() + " o "
                        + " WHERE o.id = :outstandingSurveyId",
                ImmutableMap.of("outstandingSurveyId", outstandingSurveyId)
        );
        if (requestId == null || Objects.equals(requestId, 0l) || Objects.equals(requestId, -1l)) {
            return WorkflowAuthRole.NONE;
        }
        return getRequestAuthRole(requestId, autorizationIndex, loggedUser);
    }

    private WorkflowAuthRole getRequestAuthRole(final Long requestId, final Long autorizationIndex, final ILoggedUser loggedUser) {
        if (Objects.equals(autorizationIndex, 0L)) {
            final Long countDraft = HQL_findLong(" "
                + " SELECT count(c.id) "
                + " FROM " + Request.class.getCanonicalName() + " c "
                + " JOIN c.author a"
                + " WHERE"
                    + " c.id = :requestId"
                    + " AND c.status IN ( "
                            + Request.STATUS.STAND_BY.getValue()
                            + "," + Request.STATUS.RETURNED.getValue()
                        + " )"
                    + " AND ("
                        + " c.impersonatedById = :userId"
                        + " OR a.id = :userId"
                    + " )",
                    ImmutableMap.of(
                    "requestId", requestId,
                    "userId", loggedUser.getId()
                    )
            );
            if (countDraft != null && countDraft > 0) {
                return WorkflowAuthRole.REQUESTOR;
            } else {
                return getAdminAuthRole(requestId, loggedUser);
            }
        }
        final ToFillForm pending = new ToFillForm(this);
        final Long pendingRecordId = pending.getPendingRecordIdFromRecordId(loggedUser, requestId, APE.DOCUMENT_TO_FILL_FORM.getCode());
        if (pendingRecordId != null) {
            return WorkflowAuthRole.ASSIGNED;
        }
        return getAdminAuthRole(requestId, loggedUser);
    }

    private WorkflowAuthRole getAdminAuthRole(final Long requestId, final ILoggedUser loggedUser) {
        if (!loggedUser.isAdmin()) {
            return WorkflowAuthRole.NONE;
        }
        final Integer status = HQL_findSimpleInteger(""
                        + " SELECT status "
                        + " FROM " + Request.class.getCanonicalName() + " c "
                        + " WHERE c.id = :requestId",
                ImmutableMap.of("requestId", requestId)
        );
        if (VALID_EDIT_ADMIN_STATUS.contains(status)) {
            return WorkflowAuthRole.ADMIN;
        } else {
            return WorkflowAuthRole.NONE;
        }
    }

    private void setupWebhookExecution(
            GenericSaveHandle gsh,
            PoolFillerDTO fillers,
            IOutstandingSurveys o,
            Long authIndex,
            ILoggedUser loggedUser
    ) {
        final WebhookDataDTO data = new WebhookDataDTO(fillers, o, authIndex, loggedUser);
        gsh.getJsonEntityData().put(IWebhookDAO.WEBHOOK_DATA, data);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle saveRequest(
            IOutstandingSurveys o,
            final Long autorizationIndex,
            final WorkflowAuthRole authRole,
            final Boolean refreshExpiration,
            FillHelper validator,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        SessionViewer sv = new SessionViewer(false);

        final OutstandingSurveysInfoDTO bk = loadOustandingSurveysInfo(o.getId());
        final Request request = HQLT_findById(Request.class, bk.getRequestId());
        final boolean canRetriveData = DocumentPublicationUtils.canRetrieveData(request.getDocumentMasterId());
        if (!canRetriveData) {
            getLogger().debug("Document is being published, masterId: {}", request.getDocumentMasterId());
            GenericSaveHandle gsh = new GenericSaveHandle(0);
            gsh.setOperationEstatus(0);
            gsh.getJsonEntityData().put("error", "canNotRetriveData");
            return gsh;
        }
        request.setSurveyRequestMode(o.getSurveyRequestMode());
        request.setConditionalValidatorCacheId(o.getConditionalValidatorCacheId());
        if (o.getEditedOutstandingidTitle() != null) {
            request.setDescription(o.getEditedOutstandingidTitle());
        }
        final int currentIndex = autorizationIndex.intValue();
        if (currentIndex == 0
                && Objects.equals(request.getImpersonatedById(), loggedUser.getId())
                && loggedUser.getServices().contains(ProfileServices.getServicio("formFillImpersonation"))) {
            /*
                ToDo: Reinstanciar loggedUserId con nueva intefaz de SessionViewer.
                Por ejemplo new SessionViewerSubstitute()
            */
            sv.setImpersonatingUserId(bk.getCreatorUserId());
            loggedUser = sv.getLoggedUserDto();
        } else if (WorkflowAuthRole.NONE.equals(authRole)) {
            GenericSaveHandle gsh = new GenericSaveHandle(0);
            gsh.getJsonEntityData().put("error", "pendingNoAccess");
            return gsh;
        }
        IFormCaptureDAO formCaptureDAO = getBean(IFormCaptureDAO.class);
        if (validator == null) {
            // ISSUE-5081 - Se mueve aqui para poder cargar el impersonatingUserId
            validator = new FillHelper(
                    o,
                    Module.FORMULARIE,
                    authRole,
                    formCaptureDAO,
                    loggedUser
            );
        }
        final boolean signatureAsSaveBehavior = o.isSignatureAsSaveBehavior();
        GenericSaveHandle gsh;
        if (!(o instanceof OutstandingSurveys)) {
            o = new OutstandingSurveys(o);
        }
        gsh = saveRequestAnswers(bk, (OutstandingSurveys) o, autorizationIndex, validator, loggedUser);
        if ("failedToUpdate".equals(gsh.getErrorMessage())) {
            return gsh;
        }
        final ElapsedDataDTO oStart = MeasureTime.start(getClass());
        MeasureTime.stop(oStart, "Elapsed time loading request for id " + o.getRequestId());
        final Long id = o.getId();
        if (currentIndex != 0 && request.getAutorizationPool() != null) {
            boolean validAutorizationIndex = request.getAutorizationPool().getAutorizationPoolDetailsList().stream().anyMatch((pool) -> {
                return pool.getIndice() == autorizationIndex.intValue() && pool.getAccepted() == null;
            });
            if (!validAutorizationIndex) {
                gsh.setOperationEstatus(0);
                gsh.getJsonEntityData().put("error", "pendingAttended");
                return gsh;
            }
        }
        if (o.getEstatus().equals(OutstandingSurveys.ESTATUS_CERRADA)) {
            PoolFillerDTO fillers = updateFillers(o, autorizationIndex, loggedUser.getId());
            if (!fillers.isValid()) {
                gsh.setOperationEstatus(0);
                gsh.getJsonEntityData().put("error", fillers.getMessage());
                this.updateEstatus(o, OutstandingSurveys.ESTATUS_BORRADOR);
            } else {
                setupWebhookExecution(gsh, fillers, o, autorizationIndex, loggedUser);
                gsh.setOperationEstatus(1);
                IRequestDAO daoRequest = getBean(IRequestDAO.class);
                IWorkflowDAO daoWp = getBean(IWorkflowDAO.class);
                if (request.getAutorizationPool() == null) {
                    if (fillers.getFillers().isEmpty()) {
                        final ElapsedDataDTO rStart = MeasureTime.start(getClass());
                        request.update("status", Request.STATUS_CLOSED, this);
                        getAspectJAutoProxy().finishFillForm(request, o.getId(), loggedUser);
                        MeasureTime.stop(rStart, "Elapsed time finishing request for id " + o.getId());
                    } else {
                        final ElapsedDataDTO aStart = MeasureTime.start(getClass());
                        /**
                         * Aqui solo entra la PRIMERA vez que se presiona el boton "Terminar" por el solicitante, ningun otro usuario entra
                         * aqui el flujo de autorizacion es creado "al vuelo" no se da de alta el flujo
                         *
                         */
                        request.setStatus(WorkflowRequestStatus.APROVING.getValue());
                        setRequestWorkflow(request, authRole, fillers.getFillers(), loggedUser);
                        //Autorizacion del primer evento del pool
                        daoWp.workflowRequestApprove(
                                daoRequest,
                                request,
                                authRole,
                                "Comentario automático por creación de solicitud",
                                loggedUser.getId(),
                                loggedUser,
                                true,
                                null,
                                null,
                                null
                        );
                        //cambiando el estatus nos brincamos al verificacion
                        MeasureTime.stop(aStart, "Elapsed time for new request for id " + o.getId());
                    }
                } else {
                    final ElapsedDataDTO nStart = MeasureTime.start(getClass());
                    //aqui entra cuando ya se creo el flujo y el mismo esta en proceso de llenado
                    getLogger().debug("El usuario " + loggedUser.getId() + " llena el pool {" + request.getAutorizationPool() + "} para el request {" + request + "}");
                    daoWp.workflowRequestApprove(
                            daoRequest,
                            request,
                            authRole,
                            Utilities.todayDateBy("HH:mm") + " - Llenado ",
                            loggedUser.getId(),
                            loggedUser,
                            true,
                            null,
                            null,
                            null
                    );
                    MeasureTime.stop(nStart, "Elapsed time for authorization of request for id " + o.getId());
                }
                Integer status = daoRequest.HQL_findSimpleInteger(" "
                        + " SELECT c.status"
                        + " FROM " + Request.class.getCanonicalName() + " c"
                        + " WHERE c.id = :id", "id", request.getId());
                if (WorkflowRequestStatus.APROVING.getValue().equals(status) || WorkflowRequestStatus.RETURNED.getValue().equals(status)) {
                    //se regresa a en proceso para que el siguiente autorizante pueda llenar
                    this.updateEstatus(o, OutstandingSurveys.ESTATUS_EN_PROCESO);
                    daoRequest.setToNotBusy(o.getRequestId());
                } else if (WorkflowRequestStatus.CLOSED.getValue().equals(status)) {
                    final ElapsedDataDTO nStart = MeasureTime.start(getClass());
                    request.setStatus(status);
                    getAspectJAutoProxy().finishFillForm(request, o.getId(), loggedUser);
                    MeasureTime.stop(nStart, "Elapsed time for finishing  request for id " + o.getId());
                } else {
                    getLogger().warn("You should check the process, Request must be in STATUS APROVING OR CLOSED \n OutstandingSurveys: {}", o);
                }
            }
        } else if (signatureAsSaveBehavior) {
            PoolFillerDTO fillers = updateFillers(o, autorizationIndex, loggedUser.getId());
            if (!fillers.isValid()) {
                gsh.setOperationEstatus(0);
                gsh.getJsonEntityData().put("error", fillers.getMessage());
            } else {
                setupWebhookExecution(gsh, fillers, o, autorizationIndex, loggedUser);
            }
        } else if (o.getEstatus().equals(OutstandingSurveys.ESTATUS_EN_PROCESO)
                || o.getEstatus().equals(OutstandingSurveys.ESTATUS_EN_PROCESO_PARCIALMENTE_LLENA)
                || o.getEstatus().equals(OutstandingSurveys.ESTATUS_EN_PROCESO_CONTINUAR_MAS_TARDE)) {
            AutorizationPool pool = request.getAutorizationPool();
            if (pool != null) {
                final Map<String, Object> params = new HashMap<>();
                params.put("autorizationPoolId", pool.getId());
                params.put("autorizationIndex", autorizationIndex.intValue());
                params.put("loggedUserId", loggedUser.getId());
                boolean isRequiredRecalculatePendings = HQL_updateByQuery(""
                                + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c "
                                + " SET c.accepted = " + AutorizationPoolDetails.NOT_ALTERED
                                + " WHERE"
                                + " c.autorizationPool.id = :autorizationPoolId"
                                + " AND c.indice = :autorizationIndex"
                                + " AND c.userId <> :loggedUserId",
                        params
                ) > 0;
                if (isRequiredRecalculatePendings) {
                    final ElapsedDataDTO rcStart = MeasureTime.start(getClass());
                    FormPending pending = new FormPending(this);
                    pending.fillForm(request.getId(), HibernateDAO_SurveyCapture.class, loggedUser);
                    MeasureTime.stop(rcStart, "Elapsed time for recalculate request pending for id " + request.getId());
                }
            }
            PoolFillerDTO fillers = updateFillers(o, autorizationIndex, loggedUser.getId());
            if (!fillers.isValid()) {
                gsh.setOperationEstatus(0);
                gsh.getJsonEntityData().put("error", fillers.getMessage());
            } else {
                setupWebhookExecution(gsh, fillers, o, autorizationIndex, loggedUser);
            }
        } else {
            //el estatus de "borrador" solo le pertenece al solicitante
            List<OutstandingSurveysAttendant> fillers = getPoolAttendantsInfo(o.getCuestionario().getId(), id, o.getRequestId()).getAttendants();
            if (fillers.isEmpty()
                    && !hasRequestorActivities(o.getCuestionario().getId())) {
                //aqui entran cuando se usa el boton "Guardar avances" (todos los usuarios excepto el solicitante)
                this.updateEstatus(o, OutstandingSurveys.ESTATUS_EN_PROCESO);
            } else {
                //aqui entra siempre que el solicitante no haya usado el boton "Terminar"
                this.updateEstatus(o, OutstandingSurveys.ESTATUS_BORRADOR);
            }
        }
        if (sv.getImpersonatingUserId() != null) {
            sv.resetUserValues();
        }
        gsh.getJsonEntityData().put("estatus", o.getEstatus());
        gsh.getJsonEntityData().put("signatureAsSaveBehavior", signatureAsSaveBehavior);
        final ElapsedDataDTO sqStart = MeasureTime.start(getClass());
        final Long sequenceCount = getSequenceCount(
                sv.getLoggedUserId(),
                o.getSurveyId(),
                o.getId(),
                o.getRequestId(),
                SurveyRequestMode.parse(o.getSurveyRequestMode()),
                o.getConditionalValidatorCacheId(),
                autorizationIndex
        );
        MeasureTime.stop(sqStart, "Elapsed time for loading sequence detail for id " + o.getRequestId());
        if (sequenceCount > 0
                && !OutstandingSurveys.STATUS.IN_PROGRESS_FILL_LATER.getValue().equals(o.getEstatus().intValue()) // guardar avances
                && !OutstandingSurveys.STATUS.IN_PROGRESS_FILLED_PARCIALLY.getValue().equals(o.getEstatus().intValue())) { // autoguardado
            final ElapsedDataDTO mStart = MeasureTime.start(getClass());
            final String link = ApeUtil.getPendingLink(APE.DOCUMENT_TO_FILL_FORM, o.getRequestId());
            gsh.setExtraJson("{\"link\":\"" + link + "\"}");
            MeasureTime.stop(mStart, "Elapsed time generating mail link with fill form pending for " + o.getRequestId());
        }
        if (refreshExpiration) {
            final boolean updated = formCaptureDAO.refreshExpirationData(
                    o.getCuestionario().getId(),
                    o.getId(),
                    loggedUser,
                    o.getPreguntasRespondidas()
            );
            getLogger().trace("Refresh expiration data for survey {} and outstanding survey {} was {}", o.getSurveyId(), o.getId(), updated);
        }
        return gsh;
    }

    private Integer updateEstatus(IOutstandingSurveys o, Short estatus) {
        return HQL_updateByQuery(" "
                + " UPDATE " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " SET c.estatus = : estatus"
                + " WHERE c.id = :id "
                , ImmutableMap.of("estatus", estatus, "id", o.getId())
        );
    }

    // Revisar
    private Long calculateActivationIndex(
            Long outstandingSurveyId,
            SurveyRequestMode mode,
            String conditionalValidatorCacheId,
            Long autorizationIndex,
            List<OutstandingSurveysAttendant> fillers
    ) {
        long activationIndex;
        // Valor inicial
        if (autorizationIndex.intValue() == 0) {
            activationIndex = 2L;
        } else {
            activationIndex = autorizationIndex + 1L;
        }
        // Se actualiza si hay secciones a omitir
        if (conditionalValidatorCacheId != null) {
            final ConditionalValidatorCache instance = ConditionalValidatorCache.getInstance();
            final ConditionalValidator conditionalValidator = instance.get(outstandingSurveyId, mode, conditionalValidatorCacheId, Module.FORMULARIE);
            OutstandingSurveysAttendant outstandingSurveysAttendant = fillers.stream().filter(fill -> fill.getFillAutorizationPoolIndex().equals(activationIndex) && SurveyField.ATTENDANT_FIELDS.contains(fill.getFieldType())).findFirst().orElse(null);
            if (outstandingSurveysAttendant != null) {
                Long fieldId = outstandingSurveysAttendant.getFieldObjectId();
                boolean omittedAttendantField = conditionalValidator.isConditioned(fieldId) && !conditionalValidator.isFieldShownByAnyAnswer(fieldId);
                if (omittedAttendantField) {
                    return calculateActivationIndex(outstandingSurveyId, mode, conditionalValidatorCacheId, activationIndex, fillers);
                }
            }
        }
        return activationIndex;
    }

    /**
     * Calcula el siguiente índice del pool
     */
    private Long nextAuthorizationIndex(
            Long surveyId,
            Long outstandingSurveyId,
            Long requestId,
            SurveyRequestMode mode,
            String conditionalValidatorCacheId,
            Long autorizationIndex
    ) {
        try {
            final OutstandingSurveysAttendantInfo attendantsInfo = getPoolAttendantsInfo(surveyId, outstandingSurveyId, requestId);
            final List<OutstandingSurveysAttendant> fillers = attendantsInfo.getAttendants();
            return calculateActivationIndex(outstandingSurveyId, mode, conditionalValidatorCacheId, autorizationIndex, fillers);
        } catch (Exception e) {
            getLogger().error("Error al calcular el siguiente authorization index.", e);
            return autorizationIndex;
        }
    }

    private PoolFillerDTO updateFillers(
            IOutstandingSurveys outstandingSurvey,
            Long autorizationIndex,
            Long loggedUserId
    ) {
        final ElapsedDataDTO oStart = MeasureTime.start(getClass());
        final Long surveyId = outstandingSurvey.getCuestionario().getId();
        final Long outstandingSurveyId = outstandingSurvey.getId();
        final Long nextUserAssigned = outstandingSurvey.getNextUserToFill();
        final PoolFillerDTO result = new PoolFillerDTO(false);
        try {
            final OutstandingSurveysAttendantInfo attendantsInfo = getPoolAttendantsInfo(surveyId, outstandingSurveyId, outstandingSurvey.getRequestId());
            final List<OutstandingSurveysAttendant> fillers = attendantsInfo.getAttendants();
            Long activationIndex = calculateActivationIndex(
                    outstandingSurveyId,
                    SurveyRequestMode.parse(outstandingSurvey.getSurveyRequestMode()),
                    outstandingSurvey.getConditionalValidatorCacheId(),
                    autorizationIndex,
                    fillers
            );
            boolean needUpdateWorkflowData = false;
            if (outstandingSurveyId != null) {
                for (OutstandingSurveysAttendant filler : fillers) {
                    if (filler.getId() == null) {
                        filler.setId(-1L);
                    }
                    if (filler.getOwner().getId() == null) {
                        filler.getOwner().setId(-1L);
                    }
                    final Long currentIndex = filler.getFillAutorizationPoolIndex();
                    final Boolean newUserToBeDefined;
                    if (Owner.TYPE.USER_TO_BE_DEFINED.equals(filler.getOwner().getType())
                            && nextUserAssigned != null
                            && activationIndex.equals(currentIndex)) {
                        OwnerUtil.setUserToBeDefined(filler.getOwner(), nextUserAssigned);
                        filler.setUserDefinedToFillId(nextUserAssigned);
                        newUserToBeDefined = true;
                    } else {
                        newUserToBeDefined = false;
                    }
                    if (attendantsInfo.getIsNew() || newUserToBeDefined) {
                        needUpdateWorkflowData = true;
                        Owner owner = OwnerUtil.save(this, loggedUserId, filler.getOwner());
                        if (owner != null) {
                            filler.setOwnerId(owner.getId());
                            makePersistent(filler, loggedUserId);
                        }
                    }
                }
                if (needUpdateWorkflowData) {
                    final Long requestId = outstandingSurvey.getRequestId();
                    updateWorkflowPreview(requestId);
                }
            }
            result.setFillers(fillers);
            result.setValid(true);
        } catch (Exception e) {
            getLogger().error("Error al calcular los responsables de llenado.", e);
            result.setFillers(Utilities.EMPTY_LIST);
            result.setMessage(e.getMessage());
        }

        MeasureTime.stop(oStart, "Elapsed time updating filler for id " + outstandingSurvey.getId());
        return result;
    }

    private void updateWorkflowPreview(final Long requestId) {
        WorkflowPreviewGenerator.updateByRequestRecords(WorkflowSupported.REQUEST, requestId, this);
    }

    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ISurveyCaptureDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(ISurveyCaptureDAO.class);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public OutstandingSurveysAttendantInfo getPoolAttendantsInfo(Long surveyId, Long outstandingSurveyId, Long requestId) {
        final OutstandingSurveysAttendantInfo info = new OutstandingSurveysAttendantInfo();
        final List<OutstandingSurveysAttendant> newAttendants = getNewPoolAttendants(surveyId, outstandingSurveyId, requestId);
        if (newAttendants.isEmpty() && outstandingSurveyId != null) {
            info.setIsNew(false);
            info.setAttendants(loadPoolAttendants(outstandingSurveyId));
        } else {
            info.setIsNew(true);
            info.setAttendants(newAttendants);
        }
        return info;
    }

    private List<OutstandingSurveysAttendant> getNewPoolAttendants(Long surveyId, Long outstandingSurveyId, Long requestId) {
        List<PoolAttendantDTO> fieldsWithAttendant = getPoolAttendants(surveyId, outstandingSurveyId);
        List<OutstandingSurveysAttendant> fillers = new ArrayList<>(fieldsWithAttendant.size());
        Integer fillAutorizationPoolIndex = 0;
        for (PoolAttendantDTO dto : fieldsWithAttendant) {
            if (dto.getFillEntity() == null) {
                getLogger().warn("fieldObjectId {} doesn't have fillEntity! for surveyId {} and outstandingSurveyId {}", dto.getFieldObjectId(), surveyId, outstandingSurveyId);
                continue;
            }
            OutstandingSurveysAttendant attendant = new OutstandingSurveysAttendant(outstandingSurveyId, dto.getFieldObjectId(), dto.getType());
            attendant.setRequestId(requestId);
            attendant.setOwner(new Owner());
            attendant.getOwner().setId(null);
            if (attendant.getFieldType() != null && SurveyField.ATTENDANT_FIELDS.contains(attendant.getFieldType())) {
                fillAutorizationPoolIndex++;
            }
            attendant.setFillAutorizationPoolIndex(fillAutorizationPoolIndex.longValue());
            switch (dto.getFillEntity()) {
                case "business-unit-position":
                    final Long businessUnitPositionId = dto.getBusinessUnitPositionId();
                    if (businessUnitPositionId == null) {
                        throw new FormConfigurationException(getTag("attendant_assignment_invalid_configuration"));
                    }
                    OwnerUtil.setPosition(attendant.getOwner(), businessUnitPositionId);
                    break;
                case "organizational-unit-position":
                    final Long organizationalUnitPositionId = dto.getOrganizationalUnitPositionId();
                    if (organizationalUnitPositionId == null) {
                        throw new FormConfigurationException(getTag("attendant_assignment_invalid_configuration"));
                    }
                    OwnerUtil.setPosition(attendant.getOwner(), organizationalUnitPositionId);
                    break;
                case "user":
                    final Long userId = dto.getUserId();
                    if (userId == null) {
                        throw new FormConfigurationException(getTag("attendant_assignment_invalid_configuration"));
                    }
                    OwnerUtil.setUser(attendant.getOwner(), userId);
                    break;
                case "user-to-be-defined":
                    OwnerUtil.setUserToBeDefined(attendant.getOwner());
                    break;
                case "requestor":
                    OwnerUtil.setRequestor(attendant.getOwner());
                    break;
                case "boss":
                    OwnerUtil.setBoss(attendant.getOwner());
                    break;
                default:
                    throw new FormConfigurationException(getTag("attendant_assignment_fail"));
            }
            fillers.add(attendant);
        }
        return fillers;
    }

    private List<PoolAttendantDTO> getPoolAttendants(Long surveyId, Long outstandingSurveyId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("surveyId", surveyId);
        StringBuilder hqlBuilder = new StringBuilder(255).append(" "
                + " SELECT new ").append(PoolAttendantDTO.class.getCanonicalName()).append("("
                + " c.id, "
                + " c.businessUnitPositionId, "
                + " c.organizationalUnitPositionId, "
                + " c.userId, "
                + " c.fillEntity, "
                + " d.type "
                + " ) "
                + " FROM ").append(SurveyField.class.getCanonicalName()).append(" d "
                + " JOIN d.obj c "
                + " WHERE"
                + " c.surveyId = :surveyId"
                + " AND ("
                + " d.type != '").append(SurveyField.TYPE_TABLE_FIELD_ITEM).append("'"
                + " OR ( "
                + " d.type = '").append(SurveyField.TYPE_TABLE_FIELD_ITEM).append("'"
                + " AND c.parentFieldId IS NOT NULL "
                + " )"
                + " )"
                + " AND c.fillEntity IN ("
                + "'business-unit-position', 'organizational-unit-position', 'user', 'user-to-be-defined', 'boss', 'requestor'"
                + " )"
        );

        if (outstandingSurveyId != null) {
            params.put("outstandingSurveyId", outstandingSurveyId);
            hqlBuilder.append(" "
                    + " AND NOT EXISTS ("
                    + " SELECT d"
                    + " FROM ").append(OutstandingSurveysAttendant.class.getCanonicalName()).append(" d"
                    + " WHERE "
                    + " d.outstandingSurvey.id = :outstandingSurveyId"
                    + " AND d.fieldObjectId = c.id "
                    + " )"
            );
        }
        hqlBuilder.append(" "
                + " ORDER BY c.order"
        );
        return HQLT_findByQuery(
                PoolAttendantDTO.class, hqlBuilder.toString(), params, true, CacheRegion.SURVEY, 0
        );
    }

    private List<OutstandingSurveysAttendant> loadPoolAttendants(Long outstandingSurveyId) {
        final Map<String, Object> params = new HashMap<>();
        params.put("outstandingSurveyId", outstandingSurveyId);
        return HQL_findByQuery(""
                        + " SELECT c "
                        + " FROM " + OutstandingSurveysAttendant.class.getCanonicalName() + " c "
                        + " WHERE c.outstandingSurveyId = :outstandingSurveyId"
                        + " ORDER BY c.fillAutorizationPoolIndex",
                params, true, CacheRegion.SURVEY, 0);
    }

    private Boolean hasRequestorActivities(Long surveyId) {
        StringBuilder hql = new StringBuilder(100);
        hql.append(""
                + " SELECT count(c.id) "
                + " FROM ").append(SurveyFieldObject.class.getCanonicalName()).append(" c "
                + " WHERE"
                + " c.surveyId = :surveyId"
                + " AND c.fillEntity = :fillEntity"
        );
        return HQL_findSimpleInteger(
                hql.toString(),
                ImmutableMap.of(
                        "surveyId", surveyId,
                        "fillEntity", "requestor"
                ),
                true,
                CacheRegion.SURVEY,
                0
        ) > 0;
    }

    private OutstandingSurveysInfoDTO loadOustandingSurveysInfo(final Long outstandingSurveyId) {
        final ElapsedDataDTO oStart = MeasureTime.start(getClass());
        final OutstandingSurveysInfoDTO data = (OutstandingSurveysInfoDTO) HQL_findSimpleObject(""
                + " SELECT new " + OutstandingSurveysInfoDTO.class.getCanonicalName() + " ("
                + " c.id as outstandingSurveysId"
                + ", c.code as code"
                + ", c.creatorUserId as creatorUserId"
                + ", c.progressStateId as progressStateId"
                + ", c.businessUnitId as businessUnitId"
                + ", c.businessUnitDepartmentId as businessUnitDepartmentId"
                + ", c.stage as stage"
                + ", c.documentId as documentId"
                + ", c.requestId as requestId"
                + ", c.surveyId as surveyId"
                + ", c.dteFechaInicio as dteFechaInicio"
                + ", c.createdDate as createdDate"
                + ", c.lastModifiedDate as lastModifiedDate"
                + ", c.createdBy as createdBy"
                + ", c.lastModifiedBy as lastModifiedBy"
                + ", c.deleted as deleted"
                + ")"
                + " FROM " + OutstandingSurveysSimple.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", outstandingSurveyId);
        MeasureTime.stop(oStart, "Elapsed time loading outstanding info for " + outstandingSurveyId);
        return data;
    }

    private GenericSaveHandle saveRequestAnswers(
            OutstandingSurveysInfoDTO bk,
            OutstandingSurveys ent,
            Long autorizationIndex,
            FillHelper validator,
            ILoggedUser loggedUser
    ) throws IOException, QMSException {
        final ElapsedDataDTO oStart = MeasureTime.start(getClass());
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (bk.getRequestId() != null) {
            RequestRef r = HQLT_findById(RequestRef.class, bk.getRequestId());
            if (r.getDeleted() == 1) {
                gsh.setExtraJson("{\"estatus\":" + OutstandingSurveys.ESTATUS_CANCELADA + "}");
                gsh.setErrorMessage("failedToUpdate");
                gsh.setOperationEstatus(0);
                return gsh;
            }
            ent.setRequest(r);
        }
        boolean signatureAsSaveBehavior = ent.isSignatureAsSaveBehavior();
        ent.setDeleted(bk.getDeleted());
        ent.setCode(bk.getCode());
        ent.setCreatorUserId(bk.getCreatorUserId());
        ent.setBusinessUnitDepartmentId(bk.getBusinessUnitDepartmentId());
        ent.setBusinessUnitId(bk.getBusinessUnitId());
        ent.setDocumentId(bk.getDocumentId());
        ent.setRequestId(bk.getRequestId());
        ent.setDteFechaInicio(bk.getDteFechaInicio());
        ent.setStage(bk.getStage());
        ent.setCuestionario(new Survey(bk.getSurveyId()));
        ent.setCreatedBy(bk.getCreatedBy());
        ent.setCreatedDate(bk.getCreatedDate());
        ent.setLastModifiedBy(bk.getLastModifiedBy());
        ent.setLastModifiedDate(bk.getLastModifiedDate());
        if (signatureAsSaveBehavior) {
            //se regresa a en proceso debido a que la firma es la que marca el cerrada
            ent.setEstatus(OutstandingSurveys.ESTATUS_EN_PROCESO);
        }
        if (ent.getId() != -1) {
            ent.setArchived(isArchived(ent.getId()));
        }
        gsh = save(ent, loggedUser, autorizationIndex, false, validator);

        MeasureTime.stop(oStart, "Elapsed time saving request answers for id " + ent.getId());
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle signTheForm(OutstandingSurveysSignDTO data, ILoggedUser loggedUser) {
        try {
            final WorkflowAuthRole authRole = getSurveyAuthRole(data.getOutstandingSurveyId(), data.getCurrentAutorizationPoolIndex(), loggedUser);
            IFormCaptureDAO fromCaptureDao = getBean(IFormCaptureDAO.class);
            final FillHelper validator = new FillHelper(
                    data.getOutstandingSurveysData(),
                    Module.FORMULARIE,
                    authRole,
                    fromCaptureDao,
                    loggedUser
            );
            final GenericSaveHandle partialSave = saveRequest(
                    data.getOutstandingSurveysData(),
                    data.getCurrentAutorizationPoolIndex(),
                    authRole,
                    false,
                    validator,
                    loggedUser
            );
            if (partialSave.getOperationEstatus().equals(0)) {
                return partialSave;
            }
            final IRequestDAO dao = getBean(IRequestDAO.class);
            Request sol = (Request) (Utilities.getBean(IWorkflowDAO.class).getRequestEntityFromId(
                    Request.class,
                    data.getOutstandingSurveysData().getRequestId(),
                    data.getOutstandingSurveysData().getSurveyRequestMode(),
                    data.getOutstandingSurveysData().getConditionalValidatorCacheId())
            );
            final boolean success = dao.signTheForm(
                    data.getOutstandingSurveyId(),
                    data.getSign(),
                    authRole,
                    data.getComments(),
                    data.getSurveyFieldObjId(),
                    loggedUser,
                    data.getCurrentAutorizationPoolIndex(),
                    data.getCurrentRecurrence(),
                    sol
            );
            if (success) {
                final ISurveyFieldHistoryDAO daoHistoryComment = getBean(ISurveyFieldHistoryDAO.class);
                daoHistoryComment.save(data.getComments(), loggedUser, data.getOutstandingSurveyId());
                final Long sequenceCount = getSequenceCount(
                        loggedUser.getId(),
                        sol.getSurveyId(),
                        data.getOutstandingSurveyId(),
                        sol.getId(),
                        SurveyRequestMode.parse(data.getOutstandingSurveysData().getSurveyRequestMode()),
                        data.getOutstandingSurveysData().getConditionalValidatorCacheId(),
                        data.getCurrentAutorizationPoolIndex()
                );
                if (sequenceCount > 0) {
                    final String link = ApeUtil.getPendingLink(APE.DOCUMENT_TO_FILL_FORM, sol.getId());
                    partialSave.setExtraJson("{\"link\":\"" + link + "\"}");
                }
            } else {
                partialSave.setOperationEstatus(0);
            }
            final boolean updated = fromCaptureDao.refreshExpirationData(
                    data.getOutstandingSurveysData().getCuestionario().getId(),
                    data.getOutstandingSurveyId(),
                    loggedUser,
                    null
            );
            getLogger().trace("Refresh expiration data for survey {} and outstanding survey {} was {}", data.getOutstandingSurveysData().getSurveyId(), data.getOutstandingSurveyId(), updated);
            return partialSave;
        } catch (Exception ex) {
            getLogger().error(
                    "Error signing the outstanding form {} and index {}",
                    data.getOutstandingSurveysData().getId(),
                    data.getCurrentAutorizationPoolIndex(),
                    ex
            );
            GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setOperationEstatus(0);
            gsh.setSuccessMessage(SessionViewer.ERROR);
            gsh.setErrorMessage(ExceptionUtils.getRootCauseMessage(ex));
            return gsh;
        }
    }

    /**
     * Revisa el siguiente índice de la secuencia
     *
     * @param currentAuthorizationPoolIndex El índice actual SIN omisión de secciones.
     */
    private Long getSequenceCount(
            Long loggedUserId,
            Long surveyId,
            Long outstandingSurveysId,
            Long requestId,
            SurveyRequestMode mode,
            String conditionalValidatorCacheId,
            Long currentAuthorizationPoolIndex
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("loggedUserId", loggedUserId);
        params.put("requestId", requestId);
        Long nextAuthorizationIndex = nextAuthorizationIndex(
                surveyId,
                outstandingSurveysId,
                requestId,
                mode,
                conditionalValidatorCacheId,
                currentAuthorizationPoolIndex
        );
        params.put("sequenceIndex", (nextAuthorizationIndex != 0 ? nextAuthorizationIndex : 2L));
        return HQL_findSimpleLong(" "
                        + " SELECT COUNT(c.id) "
                        + " FROM " + WorkflowRequestData.class.getCanonicalName() + " c "
                        + " WHERE c.userId = :loggedUserId"
                        + " AND c.requestId = :requestId"
                        + " AND c.workflowIndex = :sequenceIndex ",
                params
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextLongValue> getRequestPartialProgressStatuses(final Long requestId, final Long currentAutorizationPoolIndex) {
        final Long surveyFieldObjId = HQL_findLong(""
                        + " SELECT att.fieldObjectId"
                        + " FROM " + OutstandingSurveysAttendant.class.getCanonicalName() + " att"
                        + " WHERE att.requestId = :id"
                        + " AND att.fieldType IN ("
                        + "'" + SurveyField.TYPE_SECCION + "',"
                        + "'" + SurveyField.TYPE_SIGNATURE + "'"
                        + " )"
                        + " AND att.fillAutorizationPoolIndex = :index ",
                ImmutableMap.of(
                        "id", requestId,
                        "index", currentAutorizationPoolIndex
                )
        );
        if (surveyFieldObjId == null || Objects.equals(surveyFieldObjId, 0l)) {
            return new ArrayList<>(0);
        }
        final ISurveysDAO dao = getBean(ISurveysDAO.class);
        final List<TextLongValue> statuses = dao.getSurveyFieldPartialProgressStatuses(surveyFieldObjId, currentAutorizationPoolIndex);
        return statuses;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public OutstandingSurveysLogging createOutstandingSurveysLog(
            final Long outstandingSurveysId,
            final OutstandingSurveysLoggingType type,
            final Boolean skipLog,
            final ISecurityUser user
    ) {
        if (skipLog) {
            return null;
        }
        if (user == null || user.getId() == null || user.getId() <= 0) {
            getLogger().warn("Missing userId for logging of outstandingSurveyId {}.", outstandingSurveysId);
            return null;
        }
        if (outstandingSurveysId == null || outstandingSurveysId <= 0) {
            getLogger().warn("Missing userId for logging of userId {}.", user.getId());
            return null;
        }
        final OutstandingSurveysLogging logging = new OutstandingSurveysLogging();
        final Date now = new Date();
        logging.setId(-1l);
        logging.setOutstandingSurveysId(outstandingSurveysId);
        logging.setCreatedBy(user.getId());
        logging.setCreatedDate(now);
        logging.setLastModifiedBy(user.getId());
        logging.setLastModifiedDate(now);
        logging.setUserId(user.getId());
        logging.setType(type.getType());
        return makePersistent(logging, user.getId());
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Boolean isArchived(Long outstandingSurveyId) {
        return getUntypedDAO().HQL_findSimpleBoolean(" " +
                        "SELECT o.archived " +
                        "FROM " + OutstandingSurveys.class.getCanonicalName() + " o " +
                        " WHERE o.id = :outstandingSurveyId ",
                "outstandingSurveyId",
                outstandingSurveyId
        );
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Boolean isDeleted(Long outstandingSurveyId) {
        final Integer deleted = getUntypedDAO().HQL_findSimpleInteger(" " +
                        "SELECT o.deleted " +
                        "FROM " + OutstandingSurveys.class.getCanonicalName() + " o " +
                        " WHERE o.id = :outstandingSurveyId ",
                "outstandingSurveyId",
                outstandingSurveyId
        );
        return Objects.equals(deleted, 1);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public Survey surveyLazyData(Long surveyId) {
        return (Survey) HQL_findSimpleObject("" +
                        "SELECT new " + Survey.class.getCanonicalName() + " (" +
                        " s.id," +
                        " s.signRejectApproval," +
                        " s.addAttachmentTotalLimit," +
                        " s.authorId," +
                        " s.type," +
                        " s.code," +
                        " s.answersTable," +
                        " s.mailsToOnCompletedFillForm," +
                        " s.mailsSubjectOnCompletedFill," +
                        " s.isAddAttachmentEnabled," +
                        " s.isMailIncludeAnswers," +
                        " s.addFindingsEnabled," +
                        " s.addActivitiesEnabled," +
                        " s.estatus," +
                        " s.hasAnswers," +
                        " s.isFillRequestAvailable," +
                        " s.isTemplateUseAvailable," +
                        " s.isFreezed" +
                        ") FROM " + Survey.class.getCanonicalName() + " s " +
                        " WHERE s.id = :surveyId",
                ImmutableMap.of("surveyId", surveyId));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<OutstandingSurveysAttendantLoad> outstandingSurveysAttendantLoadLazy(Long outstandingSurveyId) {
        return HQLT_findByQuery(OutstandingSurveysAttendantLoad.class, "" +
                        "SELECT new " + OutstandingSurveysAttendantLoad.class.getCanonicalName() + " (  " +
                        " qa.outstandingSurveyId," +
                        " qa.fieldObjectId," +
                        " qa.fillerId, " +
                        " qa.userId," +
                        " qa.surveyId," +
                        " qa.requestId," +
                        " qa.documentId," +
                        " qa.workflowId," +
                        " qa.workflowPoolId," +
                        " qa.ownerId," +
                        " qa.positionId," +
                        " qa.workflowIndex," +
                        " qa.outstandingSurveyStatus," +
                        " qa.ownerType," +
                        " qa.fieldType," +
                        " qa.userName" +
                        ") FROM " + OutstandingSurveysAttendantLoad.class.getCanonicalName() + " qa " +
                        "WHERE qa.outstandingSurveyId = :outstandingSurveyId",
                ImmutableMap.of("outstandingSurveyId", outstandingSurveyId));
    }

    private RetrieveArchivedSurveyAnswer getArchivedSurveyAnswer(
            Long outstandingSurveyId,
            String fieldId,
            Map<String, Object> answersMap,
            FieldConfigDTO config
    ) {
        return new RetrieveArchivedSurveyAnswer.RetrieveArchivedSurveyAnswerBuilder()
                .setFieldId(Long.valueOf(fieldId))
                .setAnswersMap(answersMap)
                .setOutstandingSurveyId(outstandingSurveyId)
                .setFieldConfig(config)
                .build();
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public OutstandingSurveyLoadAnswersDTO loadDataFromSurveyAnswers(
            final Long outstandingSurveyId,
            final String documentMasterId,
            final ILoggedUser loggedUser
    ) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return null;
        }
        final IUntypedDAO dao = getUntypedDAO();
        final ISurveyCaptureDAO surveyCaptureDAO = getBean(ISurveyCaptureDAO.class);
        final OutstandingSurveyLoadAnswersDTO result = (OutstandingSurveyLoadAnswersDTO) dao.HQL_findSimpleObject(" " +
                        "SELECT new " + OutstandingSurveyLoadAnswersDTO.class.getCanonicalName() + " (" +
                        " o.answersTableId," +
                        " o.businessUnitDepartmentId," +
                        " o.code," +
                        " o.creatorUserId," +
                        " o.deleted," +
                        " o.code," +
                        " o.documentId," +
                        " o.dteFechaInicio," +
                        " o.estatus," +
                        " o.id," +
                        " o.lastModifiedBy," +
                        " o.lastModifiedDate," +
                        " o.progressStateId," +
                        " o.requestId," +
                        " o.stage," +
                        " o.status," +
                        " o.surveyId" +
                        ") " +
                        "FROM " + OutstandingSurveys.class.getCanonicalName() + " o " +
                        "WHERE o.id = :id",
                ImmutableMap.of("id", outstandingSurveyId)
        );
        if (result == null) {
            return null;
        }
        Long surveyId = this.getSurveyId(outstandingSurveyId);
        result.setCuestionario(surveyCaptureDAO.surveyLazyData(surveyId));
        List<OutstandingSurveysAttendantLoad> attendants = surveyCaptureDAO.outstandingSurveysAttendantLoadLazy(outstandingSurveyId);
        result.setQuestionAttendants(attendants);
        final String dynamicTableFieldName = SurveyUtil.generateTableSynonymName(documentMasterId);
        final FieldConfigDTO fieldConfigDTO = SurveyUtil.getFields(
                surveyId,
                true,
                false,
                false,
                null, 0,
                loggedUser,
                false,
                true,
                true
        );
        final List<OutstandingQuestionAnsweredDTO> results = new ArrayList<>();
        final List<Map<String, Object>> surveyColumnsAnswerNames = getSurveyColumnsAnswerNames(surveyId, dao, loggedUser);

        Map<String, String> proccessedIds = new HashMap<>();
        final Map<String, Object> answersColumns = getUntypedDAO().SQL_findSimpleMap(
                // Por lo general es mala practica usar SELECT *, en este caso se trae toda la table de respuestas para
                // renderizar siempre por lo que no es necesario seleccionar columnas especificas
                // y debe seguir siendo el mismo caso a futuro
                "SELECT * FROM " + dynamicTableFieldName
                        + " WHERE outstanding_surveys_id1 = :outstandingSurveyId",
                Collections.singletonMap("outstandingSurveyId", outstandingSurveyId));

        surveyColumnsAnswerNames.forEach((surveyData) -> {
            String fieldType = surveyData.get("fieldType").toString();
            String fieldId = surveyData.get("fieldId").toString();
            if (proccessedIds.containsKey(fieldId)) {
                return;
            }
            proccessedIds.put(fieldId, fieldId);
            String surveyAnswersColumns = (String) surveyData.get("surveyAnswersColumns");
            Map<String, Object> answersMap = copyMapOnlyWithKeys(answersColumns, surveyAnswersColumns.split(","));
            switch (fieldType) {
                case SurveyField.TYPE_ABIERTA:
                    final RetrieveArchivedSurveyAnswer builderText = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objText = SurveyAnswersFn.retrieveTextFieldAnswer(builderText);
                    results.add(objText);
                    break;
                case SurveyField.TYPE_TEXT_DATE:
                    final RetrieveArchivedSurveyAnswer builderDate = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objDate = SurveyAnswersFn.retrieveDateFieldAnswer(builderDate);
                    results.add(objDate);
                    break;
                case SurveyField.TYPE_TEXT_TIME:
                    final RetrieveArchivedSurveyAnswer buildTime = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objTime = SurveyAnswersFn.retrieveTimeFieldAnswer(buildTime);
                    results.add(objTime);
                    break;
                case SurveyField.TYPE_FILE_UPLOAD:
                    final RetrieveArchivedSurveyAnswer builderFile = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objFile = SurveyAnswersFn.retrieveFileFieldAnswer(builderFile);
                    results.add(objFile);
                    break;
                case SurveyField.TYPE_ABIERTA_RENGLONES:
                    final RetrieveArchivedSurveyAnswer builderObjTextArray = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objTextArray = SurveyAnswersFn.retrieveTextFieldArrayAnswer(
                            builderObjTextArray
                    );
                    results.add(objTextArray);
                    break;
                case SurveyField.TYPE_STOPWATCH:
                    final RetrieveArchivedSurveyAnswer builderStw = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objStw = SurveyAnswersFn.retrieveStopwatchSelector(
                            builderStw
                    );
                    results.add(objStw);
                    break;
                case SurveyField.TYPE_FORM_WEIGHTING_RESULT:
                    final RetrieveArchivedSurveyAnswer builderWg = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objWg = SurveyAnswersFn.retrieveWeightingResult(
                            builderWg
                    );
                    results.add(objWg);
                    break;
                case SurveyField.TYPE_OPCION_MULTIPLE:
                case SurveyField.TYPE_MATRIZ_MULTIPLE:
                case SurveyField.TYPE_MATRIZ:
                case SurveyField.TYPE_TABLE_FIELD:
                case SurveyField.TYPE_UNA_RESPUESTA:
                case SurveyField.TYPE_MENU:
                    final RetrieveArchivedSurveyAnswer builderES = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objResultES = SurveyAnswersFn.retrieveFieldAnswers(builderES);
                    results.add(objResultES);
                    break;
                case SurveyField.TYPE_SI_NO_PORQUE:
                    final RetrieveArchivedSurveyAnswer builderYN = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objResultYN = SurveyAnswersFn.retrieveExclusiveSelectYesNoFieldAnswer(builderYN);
                    results.add(objResultYN);
                    break;
                case SurveyField.TYPE_EXTERNAL_CATALOG:
                    final RetrieveArchivedSurveyAnswer builderCI = getArchivedSurveyAnswer(
                            outstandingSurveyId,
                            fieldId,
                            answersMap,
                            fieldConfigDTO
                    );
                    final OutstandingQuestionAnsweredDTO objResultCI;
                    final CatalogFieldType catalogSubType = CatalogFieldType.fromValue(surveyData.get("catalogSubType").toString());
                    switch (catalogSubType) {
                        case CATALOG_HIERARCHY:
                            objResultCI = SurveyAnswersFn.retrieveCatalogHierarchyFieldAnswers(builderCI);
                            break;
                        case CATALOG_MULTIPLE:
                            objResultCI = SurveyAnswersFn.retrieveCatalogMultipleFieldAnswers(builderCI);
                            break;
                        default:
                            objResultCI = SurveyAnswersFn.retrieveCatalogFieldAnswers(builderCI);
                            break;
                    }
                    results.add(objResultCI);
                    break;

                default:
                    //Poner los otros tipos de campo y agregar soporte
                    break;
            }
        });
        result.setOutstandingAnswers(results);
        return result;
    }

    private List<Map<String, Object>> getSurveyColumnsAnswerNames(
            Long surveyId,
            IUntypedDAO dao,
            ILoggedUser loggedUser
    ) {
        List<Map<String, Object>> surveyColumnsAnswerNames = this.surveyColumnsAnswerNames(surveyId, dao);
        final IFormCaptureDAO formDao = getBean(IFormCaptureDAO.class);

        if (surveyColumnsAnswerNames == null) {
            SurveyUtil.freeze(surveyId, dao);
        }
        if (surveyColumnsAnswerNames == null) {
            throw new RuntimeException("Missing SurveyData for surveyId " + surveyId);
        }
        final SurveyUtilCache cache = new SurveyUtilCache();
        if (surveyColumnsAnswerNames.isEmpty()) {
            // Si hay al menos un campo sin `surveyAnswersColumns`, se recalculan.
            formDao.recalculateSurveyDataAnswerColumns(surveyId, cache, loggedUser);
        }
        if (surveyColumnsAnswerNames.stream().anyMatch(c -> c.get("surveyAnswersColumns") == null)) {
            // Si hay al menos un campo sin `surveyAnswersColumns`, se recalculan.
            formDao.recalculateSurveyDataAnswerColumns(surveyId, cache, loggedUser);
            // Se intenta de nuevo
            surveyColumnsAnswerNames = this.surveyColumnsAnswerNames(surveyId, dao);
            if (surveyColumnsAnswerNames.stream().anyMatch(c -> c.get("surveyAnswersColumns") == null)) {
                throw new RuntimeException("Missing surveyAnswersColumns for surveyId " + surveyId);
            }
        }
        return surveyColumnsAnswerNames;
    }

    private List<Map<String, Object>> surveyColumnsAnswerNames(Long surveyId, IUntypedDAO dao) {
        List<Map<String, Object>> surveyColumnsAnswerNames = dao.HQL_findByQuery("" +
                        "SELECT new map(" +
                        "sd.surveyAnswersColumns as surveyAnswersColumns," +
                        "sd.fieldType as fieldType," +
                        "sd.catalogSubType as catalogSubType," +
                        "sd.fieldId as fieldId " +
                        ") FROM " + SurveyData.class.getCanonicalName() + " sd" +
                        " WHERE "
                        + " sd.surveyId = :surveyId"
                        + " AND surveyAnswersColumns IS NOT NULL",
                ImmutableMap.of("surveyId", surveyId)
        );
        return surveyColumnsAnswerNames;
    }

    private <T, X> Map<T, X> copyMapOnlyWithKeys(Map<T, X> sourceMap, T[] keys) {
        Map<T, X> targetMap = new HashMap<>(keys.length);
        for (T key : keys) {
            targetMap.put(key, sourceMap.get(key));
        }
        return targetMap;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public OutstandingSurveyDTO getOutstandingSurveyDTO(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return null;
        }
        return (OutstandingSurveyDTO) HQL_findSimpleObject(" "
                        + " SELECT new " + OutstandingSurveyDTO.class.getCanonicalName() + "("
                            + " c.requestId"
                            + ", c.id"
                            + ", c.surveyId"
                            + ", c.score"
                            + ", c.estatus"
                            + ", c.progressStateId"
                            + ", c.code"
                            + ", r.autorId"
                            + ", c.businessUnitDepartmentId"
                            + ", c.documentId"
                            + ", r.documentMasterId"
                            + ", c.archived"
                            + ", c.deleted"
                            + ", c.dteFechaInicio"
                        + " ) "
                        + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c "
                        + " LEFT JOIN c.request r"
                        + " WHERE c.id = :outstandingid",
                "outstandingid", outstandingSurveyId
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean canDeleteTimework(TimeworkDoCheckData data, ILoggedUser loggedUser) {
        final WorkflowAuthRole authRole = getSurveyAuthRole(data.getOutsandingSurveyId(), loggedUser);
        if (WorkflowAuthRole.NONE.equals(authRole)) {
            return false;
        }
        final Long surveyFieldId = data.getSurveyFieldId();
        if (surveyFieldId == null) {
            return false;
        }
        final Long countField = HQL_findLong(" "
                        + " SELECT COUNT(f.id)"
                        + " FROM " + SurveyField.class.getCanonicalName() + " f"
                        + " JOIN f.obj fo"
                        + " WHERE f.id = :surveyFieldId"
                        + " AND fo.allowDeleteStopwatchRecord = 1",
                ImmutableMap.of(
                        "surveyFieldId", surveyFieldId
                )
        );
        return countField > 0;
    }

    private Long getSurveyId(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return null;
        }
        return HQL_findSimpleLong(" " +
                        "SELECT " +
                        "o.surveyId " +
                        "FROM " + OutstandingSurveys.class.getCanonicalName() + " o " +
                        "WHERE o.id = :id ",
                ImmutableMap.of("id", outstandingSurveyId)
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date getDteFechaInicioById(Long outstandingSurveyId) {
        if (outstandingSurveyId == null || outstandingSurveyId <= 0) {
            return null;
        }
        return HQL_findDate(" " +
                        "SELECT o.dteFechaInicio " +
                        "FROM " + OutstandingSurveys.class.getCanonicalName() + " o " +
                        "WHERE o.id = :id ",
                ImmutableMap.of("id", outstandingSurveyId)
        );
    }
}
