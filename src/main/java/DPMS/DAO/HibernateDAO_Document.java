package DPMS.DAO;

import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentDAO;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IDocumentReaderDAO;
import DPMS.DAOInterface.IFilesDAO;
import DPMS.DAOInterface.INodeAccessDAO;
import DPMS.DAOInterface.IOrganizationalUnitDAO;
import DPMS.DAOInterface.IReceiptAcknowledgmentDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.ISurveysDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.Catalog;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentSimple;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.DocumentType.documentControlledType;
import DPMS.Mapping.ExternalDocumentCatalog;
import DPMS.Mapping.Files;
import DPMS.Mapping.Node;
import DPMS.Mapping.NodeArea;
import DPMS.Mapping.NodeBusinessUnit;
import DPMS.Mapping.NodeBusinessUnitDepartment;
import DPMS.Mapping.NodeSimple;
import DPMS.Mapping.NodeUser;
import DPMS.Mapping.RelatedDocument;
import DPMS.Mapping.Request;
import DPMS.Mapping.User;
import Framework.Action.SessionViewer;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import ape.pending.listeners.OnDocumentBusinessUnitDepartmentExpirationEdited;
import ape.pending.listeners.OnDocumentTypeExpirationEdited;
import com.google.common.collect.ImmutableMap;
import isoblock.common.Properties;
import isoblock.surveys.dao.hibernate.SurveyFieldObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.access.interfaces.IPlainUser;
import qms.access.logic.SessionHelper;
import qms.document.dto.JoinableDocumentDTO;
import qms.document.dto.DocumentPlainDTO;
import qms.document.dto.DocumentLinkedSelectorDTO;
import qms.document.dto.DocumentTypePlainDTO;
import qms.document.dto.NodePathDTO;
import qms.document.dto.ShareDocumentDTO;
import qms.document.entity.DocumentReader;
import qms.document.entity.DocumentReference;
import qms.document.entity.ShareDocument;
import qms.document.interfaces.IJoinableDocument;
import qms.document.interfaces.IPlainDocument;
import qms.document.interfaces.IPlainDocumentType;
import qms.document.interfaces.IPlainNode;
import qms.document.interfaces.IPlainRequest;
import qms.document.listeners.OnChangedTypeCanPrintControlledCopies;
import qms.document.listeners.OnChangedTypeMustAssignUsers;
import qms.document.listeners.OnChangedTypeMustRead;
import qms.document.listeners.OnNoReadersAssigned;
import qms.document.listeners.OnReadersAssigned;
import qms.document.logic.DocumentHelper;
import qms.document.pending.imp.ToFillForm;
import qms.document.pending.imp.ToRenew;
import qms.form.dto.FormSaveDTO;
import qms.form.entity.FavoriteTask;
import qms.form.util.CatalogFieldType;
import qms.form.util.SurveyParserHelper;
import qms.framework.dto.DocumentDataIndex;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.interfaces.IPlainBusinessUnit;
import qms.framework.interfaces.IPlainFile;
import qms.framework.interfaces.IPlainOrganizationalUnit;
import qms.framework.interfaces.IPlainSearchBusinessUnitDepartment;
import qms.framework.util.CacheRegion;
import qms.framework.util.DocumentLinkUtil;
import qms.framework.util.FileLoggingType;
import qms.framework.util.MeasureTime;
import qms.survey.interfaces.IPlainSurvey;
import qms.util.BindUtil;
import qms.util.GridFilter;
import qms.util.QMSException;
import qms.util.interfaces.IGridFilter;
import qms.workflow.util.WorkflowRequestStatus;

/**
 * <AUTHOR> Limas
 */
@Lazy
@Repository(value = "HibernateDAO_Document")
@Scope(value = "singleton")
@Language(module = "Framework.Config.Lang.DAO.HibernateDAO_Document")
public class HibernateDAO_Document extends GenericDAOImpl<DocumentSimple, Long> implements IDocumentDAO {
    // LL: subquery
    public static final String QUERY_GET_NODE_CHILDRENS = ""
            + " WITH nodosAgrupados (result,intnodoid,intnodopadre) AS ( "
            //-- Anchor member definition
                + " SELECT CAST(nodo.intnodoid as nvarchar(max)) AS result, nodo.intnodoid,nodo.intnodopadre "
            + " FROM tblnodo nodo "
            + " WHERE nodo.intnodoid = :nodoPadre "
            + " UNION ALL "
            //-- Recursive member definition
                + " SELECT CAST(subnodo.intnodoid as nvarchar(max)) as result,subnodo.intnodoid,subnodo.intnodopadre "
            + " FROM tblnodo subnodo "
            + " INNER JOIN nodosAgrupados nodosuperior "
            + "    ON nodosuperior.intnodoid = subnodo.intnodopadre "
            + " ) "
            //-- Statement that executes the CTE
            + " SELECT result"
            + " FROM nodosAgrupados "
        ;

    private static final String ACCESS_BY_BUSINESS_UNIT_LIST = ""
            + " EXISTS ("
            + "  SELECT 1"
            + "  FROM " + User.class.getCanonicalName() + " u1"
            + "  JOIN u1.puestos p2"
            + "  WHERE "
            + "     u1.id = :userId"
            + "     AND ( "
            + "         p2.une.id = c.businessUnitId"
            + "         OR u1.businessUnitId = c.businessUnitId"
            + "     )"
            + " )";
    private static final String ACCESS_BY_BUSINESS_UNIT_OPEN = ""
            + " EXISTS ("
            + "  SELECT 1"
            + "  FROM " + User.class.getCanonicalName() + " u1"
            + "  JOIN u1.puestos p2"
            + "  JOIN p2.perfil per "
            + "  WHERE "
            + "     u1.id = :userId"
            + "     AND per.unrestrictedDocumentAccess = 1 "
            + "     AND ( "
            + "         p2.une.id = c.businessUnitId"
            + "         OR u1.businessUnitId = c.businessUnitId"
            + "     )"
            + " )";
    private static final String ACCESS_BY_BUSINESS_UNIT_DEPARTMENT = ""
            + "  EXISTS ("
            + "    SELECT 1 "
            + "    FROM " + User.class.getCanonicalName() + " us"
            + "    LEFT JOIN us.puestos p2"
            + "    LEFT JOIN p2.departamentos bud "
            + "    WHERE"
                    + " us.id = :userId"
                    + " AND ("
                        + " bud.id = c.businessUnitDepartmentId"
                        + " OR us.departmentId = c.department.departmentId"
                    + " )"
            + " )";
    private static final String ACCESS_BY_AUTHOR = ""
            + " c.authorId = :userId";
    private static final String ACCESS_BY_ORIGINATOR = ""
            + " c.originadorId = :userId";
    private static final String ACCESS_BY_BUSINESS_UNIT_DOCUMENT_MANAGER = ""
            + " EXISTS ( "
            + "    SELECT 1"
            + "    FROM " + BusinessUnit.class.getCanonicalName() + "  bu"
            + "    WHERE bu.documentManagerId = :userId"
            + "    AND bu.id IN (c.businessUnitId,c.department.businessUnitId) "
            + " )";
    private static final String ACCESS_BY_BUSINESS_UNIT_DEPARTMENT_DOCUMENT_MANAGER = ""
            + " EXISTS ( "
            + "    SELECT 1"
            + "    FROM  " + BusinessUnitDepartmentLite.class.getCanonicalName() + " bud "
            + "    WHERE bud.documentManagerId = :userId"
            + "    AND bud.departmentId = c.department.departmentId "
            + " )";
    

    private static final String BUSINESS_UNIT_PERMISSION = ""
                + " SELECT nb.id.nodeId "
                + " FROM DPMS.Mapping.NodeBusinessUnit nb "
                + " WHERE exists ("
                    + " SELECT u.id "
                    + " FROM DPMS.Mapping.User u "
                    + " LEFT JOIN u.puestos p "
                    + " WHERE"
                        + " u.id= :userId"
                        + " AND ( "
                            + " nb.id.businessUnitId = p.businessUnitId "
                            + " OR nb.id.businessUnitId = u.businessUnitId "
                        + " )"
                + " ) AND nb.id.nodeId =:nodeId ";
    private static final String PROCESS_PERMISSION = ""
                + " SELECT na.id.nodeId "
                + " FROM DPMS.Mapping.NodeArea na "
                + " WHERE exists ("
                    + " SELECT dp.id "
                    + " FROM DPMS.Mapping.User u "
                    + " JOIN u.puestos p "
                    + " JOIN p.departmentProcess dp "
                    + " WHERE u.id= :userId"
                    + " AND na.id.areaId = dp.processId "
                + " ) AND na.id.nodeId = :nodeId";
    private static final String DEPARTMENT_PERMISSION = ""
                + " SELECT nbud.id.nodeId "
                + " FROM DPMS.Mapping.NodeBusinessUnitDepartment nbud"
                + " WHERE exists ("
                    + " SELECT u.id "
                    + " FROM DPMS.Mapping.User u "
                    + " LEFT JOIN u.puestos p "
                    + " LEFT JOIN p.departamentos d "
                    + " WHERE"
                        + " u.id = :userId" 
                        + " AND ("
                            + " d.id = nbud.id.businessUnitDepartmentId "
                            + " OR u.businessUnitDepartmentId = nbud.id.businessUnitDepartmentId "
                        + " ) "
                + " ) AND nbud.id.nodeId = :nodeId";
    private static final String USER_PERMISSION = ""
                + " SELECT nu.id.nodeId"
                + " FROM DPMS.Mapping.NodeUser nu"
                + " WHERE nu.id.userId=:userId"
                + " AND nu.id.nodeId = :nodeId";
    private static final String BUSINESS_UNIT_SPECIAL_PERMISSION = ""
                + " SELECT doc.nodo.id"
                + " FROM DPMS.Mapping.Document doc "
                + " WHERE doc.businessUnit.id IN (:businessUnits) "
                + " AND doc.businessUnit.documentManagerId = :userId "
                + " AND doc.nodo.id = :nodeId";
    private static final String DEPARTMENT_SPECIAL_PERMISSION = ""
                + " SELECT ds.nodoId"
                + " FROM DPMS.Mapping.DocumentSimple ds "
                + " WHERE ds.department.businessUnitId IN (:businessUnits) "
                + " AND ds.department.documentManagerId = :userId "
                + " AND ds.nodoId = :nodeId";
    
    public static final String FOLDER_PERMISSION = " AND ("
                /*Tiene permiso por proceso*/
                + " exists (" + PROCESS_PERMISSION + " ) "
                /*Tiene permiso por planta*/
                + " or exists (" + BUSINESS_UNIT_PERMISSION + " ) "               
                /*Tiene permiso por usuario*/
                + " OR exists (" + USER_PERMISSION + " )"
                /*Tiene permiso por departamento*/
                + " OR exists (" + DEPARTMENT_PERMISSION + " )" 
                + " :isDocMgr"
                + " )" ;
    
    public static final String DOCUMENT_MANAGER_PERMISSION = 
                /*Tiene un documento de su planta*/
                 " OR exists (" + BUSINESS_UNIT_SPECIAL_PERMISSION + " )"         
                /*Tiene un documento de algún departamento de su planta*/
                + " OR exists (" + DEPARTMENT_SPECIAL_PERMISSION + " )";
    /*
    Este filtro es utilizado en al menos 2 lugares 
     - Formularios disponbiles para llenar
     - Documentos en la papelera que puedo consultar

    De entrada se tienen las validaciones que no sean un backup y que no sean de otros módulos del sistema: Acciones, Proyectos, Quejas
    y que no pertenescan al nodo Raiz

       Por lo que los requisitos para visualiar los documentos quedan determinados por los permisos dados por:
        *) Ser autor
        *) Ser originador
        *) Ser encargado de los documentos de la planta
        *) Ser encargado de los documentos del departamento
        *) Acceso a las carpetas que contienen los documentos

       Luego se determina el acceso mediante los permisos individuales Ser autor o Ser originador o encargado de documentos en la Planta o encargado de documentos por departamento
       Se deja como prioridad el que los usuarios tengan permisos por carpeta
       Por último se podrá dar acceso a los

    */
    private static final String validEntitiesFilterStub = ""
            + " c.nodoId NOT IN ("
            + Properties.NODO_ACCIONES
            + ", " + Properties.NODO_PROYECTOS
            + ", " + Properties.NODO_QUEJAS
            + ", " + Properties.NODO_RAIZ
            + " ) "
            + " AND "
            + " c.isBackup = 0"
            + " AND " 
            + "("
                //donde soy autor
                + ACCESS_BY_AUTHOR
                //donde soy originador
                + " OR " + ACCESS_BY_ORIGINATOR
                //donde soy encargado de documentos por planta (business_unit)
                + " OR " + ACCESS_BY_BUSINESS_UNIT_DOCUMENT_MANAGER
                //donde soy encargado de documentos por departamento (business_unit_department)
                + " OR " + ACCESS_BY_BUSINESS_UNIT_DEPARTMENT_DOCUMENT_MANAGER
                + " OR :folderAccess "
                + " :optionalFilter "
            + ")";
    
    private static final String validEntitiesFilterByUserAndDocumentAuthor = ""
            + " exists ( "
                + " SELECT u.id"
                + " FROM DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " WHERE ("
                    + " exists ( "
                        + " SELECT us.id "
                        + " FROM DPMS.Mapping.User us "
                        + " JOIN us.puestos pu "
                        + " JOIN pu.perfil perfil "
                        + " WHERE"
                            + " us.id = :userId "
                            + " AND ( "
                                + " p.une.id = pu.une.id"
                                + " OR u.businessUnitId = pu.une.id"
                                + " OR p.une.id = us.businessUnitId "
                                + " OR u.businessUnitId = us.businessUnitId "
                            + " )"
                            + " AND 1 IN (:servicesMine)"
                    + " ) AND c.id = u.id "
                    + " AND 1 IN (:servicesTheirs)"
                + " )"
                + " OR exists ("
                    + " SELECT author.id "
                    + " FROM DPMS.Mapping.DocumentSimple d "
                    + " WHERE author.id = :selected AND c.id = author.id"
                + " )"
            + " )";

    private static final String DOCUMENT_COUNT = ""
            + " SELECT count(c)"
            + " FROM " + Document.class.getCanonicalName() + " c";


    private static final String DOCUMENT_REQUEST_TYPED_COUNT = ""
            + " SELECT count(*)"
            + " FROM " + Request.class.getCanonicalName() + " r "
            + " JOIN r.document d "
            + " WHERE "
                + " d.id = :documentId"
                + " AND r.status IN ("
                    + WorkflowRequestStatus.APROVING.getValue()
                    + ',' + Request.STATUS.STAND_BY.getValue()
                    + ',' + WorkflowRequestStatus.RETURNED.getValue()
                    + ',' + WorkflowRequestStatus.VERIFING.getValue()
                + " )"
                + " AND r.type = :requestType"
    ;
    private static final String DOCUMENT_REQUEST_FILL_COUNT = ""
            + " SELECT count(*)"
            + " FROM " + Request.class.getCanonicalName() + " r "
            + " JOIN r.fillOutDocument d "
            + " WHERE "
                + " d.id = :documentId"
                + " AND r.status IN ("
                    + WorkflowRequestStatus.APROVING.getValue()
                    + ',' + Request.STATUS.STAND_BY.getValue()
                    + ',' + WorkflowRequestStatus.RETURNED.getValue()
                    + ',' + WorkflowRequestStatus.VERIFING.getValue()
                + " )"
                + " AND r.type = :requestType"
    ;

    /* Filtra los documentos activos o en edición que no hayan sido borrados */
    public final static String ACTIVE_DOCUMENT = ""
            + " c.status IN ("
            + Document.ACTIVE_STATUS
            + "," + Document.IN_EDITION_STATUS
            + " )"
            + " AND c.deleted = 0 ";

    /* Filtra los documentos activos o en edición que no hayan sido borrados */
    public final static String ONLY_DOCUMENT_MODULE = ""
        +   " c.nodoId NOT IN ("
        +   Properties.NODO_ACCIONES + ","
        +   Properties.NODO_PROYECTOS + ","
        +   Properties.NODO_RAIZ + ","
        +   Properties.NODO_QUEJAS + ")";

    @Override
    @OnChangedTypeMustAssignUsers
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DocumentType changeTypeMustAssignReaders(DocumentType type, LoggedUser loggedUser) {
        return type;
    }
   
    @Override
    @OnChangedTypeCanPrintControlledCopies
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long changeTypeCanPrintControlledCopies(DocumentType type, LoggedUser loggedUser) {
        Long countCopies = 0l;
        if (Objects.equals(type.getCanPrintControlledCopies(), 0)) {
            final IReceiptAcknowledgmentDAO ackDao = getBean(IReceiptAcknowledgmentDAO.class);
            final Long countDeliverCopies = ackDao.getCopiesToDeliverCountByType(type.getId());
            if (countDeliverCopies != null && countDeliverCopies > 0) {
                // Cancelar copias por entregar
                countCopies += ackDao.cancelDeliverCopiesByType(type.getId(), loggedUser);
            }
            final Long countPickUpCopies = ackDao.getCopiesToPickUpCountByType(type.getId());
            if (countPickUpCopies != null && countPickUpCopies > 0) {
                // Cancelar copias por recoger
                countCopies += ackDao.cancelPickUpCopiesByType(type.getId(), loggedUser);
            }
        }
        return countCopies;
    }

    @Override
    @OnChangedTypeMustRead
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public DocumentType changeTypeMustRead(DocumentType type, LoggedUser loggedUser) {
        return type;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer saveRelatedDocument(final Long masterDocumentId, final Long relatedDocumentId, final ILoggedUser loggedUser) {
        final List<Long> relatedDocumentIds = new ArrayList<>(1);
        relatedDocumentIds.add(relatedDocumentId);
        return saveRelatedDocuments(masterDocumentId, relatedDocumentIds, loggedUser);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer saveRelatedDocuments(final Long masterDocumentId, final List<Long> relatedDocumentIds, final ILoggedUser loggedUser) {
        if (!DocumentHelper.canEditDocument(loggedUser)) {
            getLogger().error(
                    "Acccess denied to saveRelatedDocuments with masterDocumentId {} and  relatedDocumentIds {} to {}",
                    masterDocumentId,
                    relatedDocumentIds,
                    loggedUser.getAccount()
            );
            return 0;
        }
        final Integer status = HQL_findSimpleInteger(""
                + " SELECT d.status"         
                + " FROM " + Document.class.getCanonicalName() + " d"
                + " WHERE d.id = :id", "id", masterDocumentId);
        if (!Document.STATUS.ACTIVE.getValue().equals(status)
                && !Document.STATUS.IN_EDITION.getValue().equals(status)) {
            getLogger().error(
                    "Invalid status {} to saveRelatedDocuments with masterDocumentId {} and  relatedDocumentIds {} to {}",
                    status,
                    masterDocumentId,
                    relatedDocumentIds,
                    loggedUser.getAccount()
            );
            return 0;
        }
        final List<Long> newRelatedDocuments = HQL_findByQuery(""
                + " SELECT d.id"
                + " FROM " + Document.class.getCanonicalName() + " d"
                + " WHERE " + BindUtil.parseFilterList("d.id", relatedDocumentIds)
                + " AND NOT EXISTS ("
                    + " SELECT 1 "
                    + " FROM " + RelatedDocument.class.getCanonicalName() + " rd"
                    + " WHERE (rd.id.documentIdB = " + masterDocumentId + " AND rd.id.documentIdA = d.id)"
                    + " OR (rd.id.documentIdA = " + masterDocumentId + " AND rd.id.documentIdB = d.id)"
                + ")"
                + " GROUP BY d.id");
        saveLinkedItems(RelatedDocument.class, masterDocumentId, relatedDocumentIds, false, loggedUser.getId());
        return newRelatedDocuments.size();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer deleteRelatedDocument(final Long masterDocumentId, final Long relatedDocumentId, final ILoggedUser loggedUser) {
        if (!DocumentHelper.canEditDocument(loggedUser)) {
            getLogger().error(
                    "Acccess denied to deleteRelatedDocument with masterDocumentId {} and  relatedDocumentId {} to {}",
                    masterDocumentId,
                    relatedDocumentId,
                    loggedUser.getAccount()
            );
            return 0;
        }
        final Integer status = HQL_findSimpleInteger(""
                + " SELECT d.status"         
                + " FROM " + Document.class.getCanonicalName() + " d"
                + " WHERE d.id = :id", "id", masterDocumentId);
        if (!Document.STATUS.ACTIVE.getValue().equals(status)
                && !Document.STATUS.IN_EDITION.getValue().equals(status)) {
            getLogger().error(
                    "Invalid status {} to deleteRelatedDocument with masterDocumentId {} and  relatedDocumentIds {} to {}",
                    status,
                    masterDocumentId,
                    relatedDocumentId,
                    loggedUser.getAccount()
            );
            return 0;
        }
        return HQL_updateByQuery(""
            + " DELETE FROM " + RelatedDocument.class.getCanonicalName() + " a "
            + " WHERE (id.documentIdA = " + masterDocumentId + " AND id.documentIdB = " + relatedDocumentId + ")"
            + " OR (id.documentIdA = " + relatedDocumentId + " AND id.documentIdB = " + masterDocumentId + ")"

        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        //documentos que estan en mi departamento o planta
        String optionalFilter = " OR " + ACCESS_BY_BUSINESS_UNIT_LIST
                + " OR " + ACCESS_BY_BUSINESS_UNIT_DEPARTMENT;
        setValidEntitiesFilterSpecific(isAdmin, filter, userId, optionalFilter);
    }

    private void setValidEntitiesFilterSpecific(boolean isAdmin, IGridFilter filter, String userId, String optionalFilter) {
        final SessionViewer session = new SessionViewer();
        IUserDAO userDao = Utilities.getBean(IUserDAO.class);
        boolean isCorporateUser = session.getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO);
        boolean hasUnrestrictedDocumentAccess = session.getLoggedUserServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
        String businessUnits = Utilities.arrayToSelectInFullCondition(session.getFolderAccess(this).toArray(), "c.nodoId");
        
        boolean adminAccess = isAdmin || hasUnrestrictedDocumentAccess;
        filter.getCriteria().put("<filtered-entity>",""
                + " c.status IN ("
                + Document.ACTIVE_STATUS
                + "," + Document.IN_EDITION_STATUS
                + " )"
                        + " AND c.deleted = 0 "
                //documentos que pertenecen a mi planta
                + (adminAccess ? "" : (" AND (" + validEntitiesFilterStub
                        .replace(":folderAccess", businessUnits)
                        .replace(":optionalFilter", optionalFilter)
                        .replace(":userId", userId)
                        + ")"))
        );
    }
    
    private void setValidTrashEntitiesFilterSpecific(
            boolean isAdmin, 
            final SortedPagedFilter filter,
            final String userId,
            final String optionalFilter
    ) {
        final SessionViewer session = new SessionViewer();
        final Boolean isCorporateUser = session.getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO);
        final Boolean hasUnrestrictedDocumentAccess = session.getLoggedUserServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
        final String bussinessUnits = Utilities.arrayToSelectInFullCondition(session.getFolderAccess(this).toArray(), "c.nodoId");
        final Boolean adminAccess = isAdmin || isCorporateUser || hasUnrestrictedDocumentAccess;
        filter.getCriteria().put("<filtered-entity>", ""
                + " c.deleted = 0 "
                //documentos que pertenecen a mi planta
                + (adminAccess ? "" : (" AND (" + validEntitiesFilterStub
                                .replace(":folderAccess", bussinessUnits)
                                .replace(":optionalFilter", optionalFilter)
                                .replace(":userId", userId)
                        + ")"))
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setAvailableDocumentsFilter(SortedPagedFilter filter, String userId, boolean isAdmin) {
        setValidEntitiesFilterSpecific(isAdmin, filter, userId, "");
    }

    /**
   * funcion strippedDown de setValidEntitiesFilter, para evitar conflictos en papelera
     * @param filter
     * @param loggedUserId
     * @param servicio
     * @param isAdmin
     * @since ********
     * <AUTHOR> Garza Verastegui
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilterThrash(
            final SortedPagedFilter filter,
            final String loggedUserId,
            final ProfileServices[] servicio, 
            final boolean isAdmin
    ) {
        //documentos que estan en mi departamento o planta
        final String optionalFilter = " OR " + ACCESS_BY_BUSINESS_UNIT_LIST
                + " OR " + ACCESS_BY_BUSINESS_UNIT_DEPARTMENT;
        setValidTrashEntitiesFilterSpecific(isAdmin, filter, loggedUserId, optionalFilter);
        }

    /**
   * Método que obtiene todos los usuarios dentro de la planta y el autor del documento
     *
     * @param isAdmin Bandera que determina si el usuario es root.
     * @param userId Usuario en base al cual se va a filtrar.
     * @param selected Usuario que aparecerá seleccionado.
     * @return Lista de TextHasValue
     *
     * @since 2.3.2.114
     * <AUTHOR> Cavazos Galindo
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getUsersInBusinessUnitAndDocumentAuthor(Boolean isAdmin, Long userId, Long selected) {
        ProfileServices[] servicesMine = {ProfileServices.ALL};
        ProfileServices[] servicesTheirs = {ProfileServices.ALL};
        String filter = "1=1";
        if (!isAdmin) {
            filter = validEntitiesFilterByUserAndDocumentAuthor
                    .replace(":userId", userId.toString())
                    .replace(":selected", selected.toString())
                    .replace(":servicesMine", ProfileServices.getCodedServices("perfil", servicesMine))
                    .replace(":servicesTheirs", ProfileServices.getCodedServices("pr", servicesTheirs));
        }
        //return new GenericHibernateDAO<UserRef, Long>() {}.getStrutsComboList(filter);
        return this.getStrutsComboList(filter);
    }

    /**
     * Implementación del metodo para guardar/actualizar documentos
     *
     * @param entity documento a guardar/actualizar
     * @return GenericSaveHandle con los datos de la operación
     * <AUTHOR> Cavazos Galindo
     * @since 2.3.2.133
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(final DocumentSimple entity) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        DocumentSimple ent = entity;
        getLogger().info("DPMS.HibernateDAO_Document.CRUD_Document @ save:" + Utilities.getSerializedObj(ent));
        boolean nuevo = (ent.getId() == -1);
        //Se intenta guardar en la base de datos
        ent.setAuthor(ent.getOriginador());
        if (ent.getCode() == null || ent.getCode().isEmpty()) {
            try {
                ent.setCode(DPMS.Document.CRUD_Document.getGeneratedCode());
            } catch (final QMSException ex) {
                throw new RuntimeException(ex);
            }
        }
        ent = this.makePersistent(ent);
        if (ent == null) {
            gsh.setOperationEstatus(0);
        } else {
            getLogger().info("VALOR: " + ent.getCode());
            gsh.setOperationEstatus(1);
            gsh.setSavedId(ent.getId() + "");
            getLogger().info("id: " + gsh.getSavedId() + " - id - " + ent.getId());
            gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean repeatedNameDocument(String name, String modifiedDocumentCode) {
        final HashMap<String, String> params = new HashMap<>();
        params.put("description", name);
        if (modifiedDocumentCode != null) {
            params.put("modifiedDocumentCode", modifiedDocumentCode);
            modifiedDocumentCode = " AND d.code != :modifiedDocumentCode ";
        } else {
            modifiedDocumentCode = "";
        }
        final String HQL_FROM = ""
                + " FROM " + Document.class.getCanonicalName() + " d"
                + " WHERE "
                + " d.description = :description "
                + modifiedDocumentCode
                + " AND d.deleted = 0"
                + " AND d.status not in ("
                    + Document.DISCONTINUED_STATUS
                    + "," + Document.CANCELED_DISCONTINUED_STATUS
                + ")";
        Long i = HQL_findSimpleLong("SELECT COUNT(*) "+ HQL_FROM, params);
        if(i > 0) {
            getLogger().warn("El nombre del documento '{}' se encuentra repetido {} veces.", new Object[] {
                name, i
            });
            if(getLogger().isInfoEnabled()) {
                List<String> documentCodes = HQL_findByQuery("SELECT d.code " + HQL_FROM, params);
                for (String documentCode : documentCodes) {
                    getLogger().info("El nombre del documento '{}' se para la clave del documento '{}'.", new Object[] {
                        name, documentCode
                    });
                }
            }
            return true;
        }
        return false;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean repeatedNameDocument(String name, Long businessUnitId, String modifiedDocumentCode) {
        final HashMap<String, String> params = new HashMap<>();
        params.put("description", name);
        if (modifiedDocumentCode != null) {
            params.put("modifiedDocumentCode", modifiedDocumentCode);
            modifiedDocumentCode = " AND d.code != :modifiedDocumentCode ";
        } else {
            modifiedDocumentCode = "";
        }
        final String HQL_FROM = ""
                + " FROM " + Document.class.getCanonicalName() + " d"
                + " WHERE "
                + " d.description = :description "
                + modifiedDocumentCode
                + " AND ("
                    + " d.businessUnitId = " + businessUnitId
                    + " OR exists ("
                        + " SELECT c.businessUnitId"
                        + " FROM d.department c "
                        + " WHERE c.businessUnitId = " + businessUnitId
                    + " )"
                + " )"
                + " AND d.deleted = 0"
                + " AND d.status not in ("
                    + Document.DISCONTINUED_STATUS
                    + "," + Document.CANCELED_DISCONTINUED_STATUS
                + ")";
        Long i = HQL_findSimpleLong("SELECT COUNT(*) "+ HQL_FROM, params);
        if(i > 0) {
            getLogger().warn("El nombre del documento '{}' se encuentra repetido {} veces.", new Object[] {
                name, i
            });
            if(getLogger().isInfoEnabled()) {
                List<String> documentCodes = HQL_findByQuery("SELECT d.code " + HQL_FROM, params);
                for (String documentCode : documentCodes) {
                    getLogger().info("El nombre del documento '{}' se para la clave del documento '{}'.", new Object[] {
                        name, documentCode
                    });
                }
            }
            return true;
        }
        return false;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean repeatedNameRequest(String name){
      return this.repeatedNameRequest(name,null);
    }
    

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean repeatedNameRequest(String name, Long excludeFromQueryById) {
        HashMap<String,Object> params = new HashMap<>();
        params.put("param", name);
        params.put("excludeId", excludeFromQueryById != null ? excludeFromQueryById : -1L);
        final String HQL_FROM = ""
            + " FROM DPMS.Mapping.Request r "
            + " WHERE "
                + " r.description = :param "
                + " AND r.status NOT IN ("
                    + Request.STATUS_CANCELED + ","
                    + Request.STATUS_CLOSED
                + " ) AND r.type = " + Request.NEW
                + " AND r.deleted = 0 "
                + " AND r.id != :excludeId"
            ;
        Long i = HQL_findSimpleLong("SELECT COUNT(*) "+ HQL_FROM, params);
        if(i > 0) {
            getLogger().warn("El nombre del documento '{}' se encuentra repetido {} veces.", new Object[] {
                name, i
            });
            if(getLogger().isInfoEnabled()) {
                List<String> requestCodes = HQL_findByQuery("SELECT r.code " + HQL_FROM, params);
                for (String requestCode : requestCodes) {
                    getLogger().info("El nombre del documento '{}' se repite para la clave de solicitud '{}'.", new Object[] {
                        name, requestCode
                    });
                }
            }
            return true;
        }
        return false;
    } 

    /**
     * Obtiene el código de la solicitud que se se esta autorizando para un documento
     * @param documentCode Codigo del documento
     * @return Código de la  solicitud
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getApprovingRequestCode(String documentCode) {
        return HQL_findSimpleString(""
                + " SELECT "
                + "     c.code"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0 AND c.documentCode = :documentCode "
                + " AND c.status IN (" + Request.STATUS_APROVING + ")", "documentCode", documentCode);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getStorageLocationCode(Long documentId) {
        Long storageLocationId = HQL_findSimpleLong(""
                + " SELECT"
                + "  c.storagePlaceId "
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :documentId", "documentId", documentId);
        Map params = new HashMap();
        params.put("catalogoId", Long.decode(Properties.CATALOGO_LUGAR_ALMACENAMIENTO));
        params.put("storageLocationId", storageLocationId.toString());
        String code = HQL_findSimpleString(""
                + " SELECT"
                + "  c.code "
                + " FROM " + Catalog.class.getCanonicalName() + " c"
                + " WHERE c.id.catalogoId = :catalogoId AND c.value = :storageLocationId", params);
        return "null".equals(code) ? "" : code;
    }

    /**
     * Indicates if the logged user is business unit department's document manager for given document
     * @param documentId Document Id
     * @param loggedUserId Id of logged user
     * @return true if the logged user is business unit department's document manager for given document, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByBusinessUnitDepartmentDocumentManager(Long documentId, Long loggedUserId) {
        Map<String,Long> params = new HashMap<>();
        params.put("id", documentId);
        params.put("userId", loggedUserId);
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND " + ACCESS_BY_BUSINESS_UNIT_DEPARTMENT_DOCUMENT_MANAGER, params) > 0;
    }

    /**
     * Indicates if the logged user is business unit's document manager for given document
     * @param documentId Document Id
     * @param loggedUserId Id of logged user
     * @return true if the logged user is business unit's document manager for given document, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByBusinessUnitDocumentManager(Long documentId, Long loggedUserId) {
        Map<String,Long> params = new HashMap<>();
        params.put("id", documentId);
        params.put("userId", loggedUserId);
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND " + ACCESS_BY_BUSINESS_UNIT_DOCUMENT_MANAGER, params) > 0;
    }

    /**
     * Indicates if the logged user is originator for given document
     * @param documentId Document Id
     * @param loggedUserId Id of logged user
     * @return true if the logged user is originator for given document, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByOriginator(Long documentId, Long loggedUserId) {
        Map<String,Long> params = new HashMap<>();
        params.put("id", documentId);
        params.put("userId", loggedUserId);
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND " + ACCESS_BY_ORIGINATOR, params) > 0;
    }

    /**
     * Indicates if the logged user is author of current version for given document
     * @param documentId Document Id
     * @param loggedUserId Id of logged user
     * @return true if the logged user is author of current version  for given document, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByLastAuthor(Long documentId, Long loggedUserId) {
        Map<String,Long> params = new HashMap<>();
        params.put("id", documentId);
        params.put("userId", loggedUserId);
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND " + ACCESS_BY_AUTHOR, params) > 0;
    }

    /**
     * Indicates if given document belongs to his business unit department
     * @param documentId Document Id
     * @param loggedUserId Id of logged user
     * @return true  if given document belongs to his business unit department, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByBusinessUnitDepartment(Long documentId, Long loggedUserId) {
        Map<String,Long> params = new HashMap<>();
        params.put("id", documentId);
        params.put("userId", loggedUserId);
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND " + ACCESS_BY_BUSINESS_UNIT_DEPARTMENT, params) > 0;
    }

    /**
     * Indicates if given document belongs to his business unit
     * @param documentId Document Id
     * @param loggedUserId Id of logged user
     * @return true  if given document belongs to his business unit, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByBusinessUnit(Long documentId, Long loggedUserId) {
        Map<String,Long> params = new HashMap<>();
        params.put("id", documentId);
        params.put("userId", loggedUserId);
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND " + ACCESS_BY_BUSINESS_UNIT_OPEN, params) > 0;
    }

    /**
     * Indicates if logged user has folder access to the given document
     * @param documentId Document Id
     * @param loggedUserId Id of logged user
     * @return true  if logged user has folder access to the given document, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByFolderAccess(Long documentId, Long loggedUserId) {
        Map<String,Long> params = new HashMap<>();
        params.put("id", documentId);
        String bussinessUnitsFilter = Utilities.arrayToSelectInFullCondition(SessionHelper.getFolderAccess(loggedUserId, this).toArray(), "c.nodoId");
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND " + bussinessUnitsFilter, params) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasAccessByReference(Long documentId, Long loggedUserId) {
        List<Long> folders = SessionHelper.getFolderAccess(loggedUserId, this);
        List<Long> nodes = HQL_findByQuery(""
                + " SELECT r.id.nodeId"
                + " FROM "
                    + Document.class.getCanonicalName() + " c "
                    + ", " + DocumentReference.class.getCanonicalName() + " r "
                + " WHERE "
                    + " c.id = " + documentId
                    + " AND r.id.documentMasterId = c.masterId"
                );
        return nodes.stream().anyMatch((node) -> (folders.contains(node)));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ShareDocumentDTO getShareLink(Long documentId, LoggedUser loggedUser) {
        if (!loggedUser.isAdmin() && !loggedUser.getServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            getLogger().error("User is not authorized with DOCUMENT_SHARE");
            return new ShareDocumentDTO(false);
        }
        ShareDocumentDTO link = newShareLink(documentId, loggedUser);
        if (!link.isValid()) {
            return link;
        }
        DocumentLinkUtil.prepareShareLink(documentId, this, link, loggedUser);
        if (!hasAccessToDocument(documentId, loggedUser)) {
            link.setValid(false);
            return link;
        }
        return link;
    }
    
    private ShareDocumentDTO newShareLink(Long documentId, LoggedUser loggedUser) {
        ShareDocumentDTO shareDocument = getInfoDocument(documentId);
        if (shareDocument == null) {
            return new ShareDocumentDTO(false);
        }
        ShareDocument entity = new ShareDocument();
        entity.setDocumentsId(shareDocument.getDocumentId());
        entity.setCode(shareDocument.getDocumentCode());
        entity.setEmailListTo(shareDocument.getEmailToText());
        entity.setEmailListCc(shareDocument.getEmailCCText());
        entity.setDescription(shareDocument.getEmailMessage());
        entity.setShareMaxDownloads(shareDocument.getShareMaxDownloads());
        entity.setShareMaxViews(shareDocument.getShareMaxViews());
        entity.setShareLife(shareDocument.getShareLife());
        entity.setStatus(0);
        makePersistent(entity, loggedUser.getId());
        shareDocument.setId(entity.getId());
        return shareDocument;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ShareDocumentDTO getExternalLink(Long documentId, LoggedUser loggedUser) {
        if (!loggedUser.isAdmin() && !loggedUser.getServices().contains(ProfileServices.DOCUMENT_SHARE)) {
            getLogger().error("User is not authorized with DOCUMENT_SHARE");
            return new ShareDocumentDTO(false);
        }
        ShareDocumentDTO shareDocument = newShareLink(documentId, loggedUser);
        if (!shareDocument.isValid()) {
            return shareDocument;
        }
        DocumentLinkUtil.prepareExternalLink(documentId, this, shareDocument, loggedUser);
        if (!hasAccessToDocument(documentId, loggedUser)) {
            shareDocument.setValid(false);
            return shareDocument;
        }
        return shareDocument;
    }

    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public String filterRowsForCurrentUser(final ILoggedUser loggedUser) {
        return filterRowsForCurrentUser(loggedUser, true, loggedUser.getServices().contains(ProfileServices.USUARIO_CORPORATIVO));
    }

    private String filterRowsForCurrentUser(final ILoggedUser loggedUser, final boolean activeDocuments, final boolean isCorporative) {
        //Cambio para saltarse control de acessos para alta de soliciutdes
        StringBuilder condition = new StringBuilder(50);
        if (activeDocuments) {
            condition
                .append(ACTIVE_DOCUMENT)
                .append(" AND ")
                .append(ONLY_DOCUMENT_MODULE)
            ;
        } else {
            condition.append(ONLY_DOCUMENT_MODULE);
        }
        //el administrador del sistema o si el usuario tiene servicio de lectura de todos los documentos
        boolean adminAccess = loggedUser.isAdmin()
                || loggedUser.getServices().contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
        boolean bigAND = (!adminAccess
                //el servicio de encargado de documentos corporativo tambien
                && !loggedUser.getServices().contains(ProfileServices.USUARIO_CORPORATIVO))
                || loggedUser.getServices().contains(ProfileServices.DOCUMENTO_ENCARGADO);
        if (adminAccess) {
            return condition.toString();
        }
        if (bigAND) {
            condition.append(" AND (1=0");
        }
        //el servicio de encargado de documentos corporativo tambien
        if (!loggedUser.getServices().contains(ProfileServices.USUARIO_CORPORATIVO)) {
            condition
                    //autor
                    .append(" OR c.author.id = ").append(loggedUser.getId())
                    //originador
                    .append(" OR c.originador.id = ").append(loggedUser.getId())
                    //encargado de documentos por planta (business_unit)
                    .append(" OR exists ( ")
                    .append(" SELECT 1")
                    .append(" FROM DPMS.Mapping.BusinessUnit bu")
                    .append(" WHERE bu.documentManagerId = ").append(loggedUser.getId())
                    .append(" AND bu.id IN (c.businessUnitId,c.department.businessUnitId) ")
                    .append(" )")
                    //encargado de documentos por departamento (business_unit_department)
                    .append(" OR exists ( ")
                    .append(" SELECT 1")
                    .append(" FROM DPMS.Mapping.BusinessUnitDepartmentLite bu")
                    .append(" WHERE bu.documentManagerId = ").append(loggedUser.getId())
                    .append(" AND bu.id IN (c.department.id) ")
                    .append(" ) ");
        }
        if (loggedUser.getServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)) {
            //acceso por ser encargado del módulo
            IUserDAO usrdao = getBean(IUserDAO.class);
            String businessUnits = usrdao.getBusinessUnitsIds(loggedUser.getId().toString(), false, isCorporative);
            condition
                    .append(" OR exists ( ")
                    .append(" SELECT 1")
                    .append(" FROM DPMS.Mapping.DocumentSimple doc")
                    .append(" WHERE doc.businessUnitId IN (").append(businessUnits).append(")")
                    .append(" AND doc.id=c.id ")
                    .append(" ) ")
                    .append(" OR exists ( ")
                    .append(" SELECT 1")
                    .append(" FROM DPMS.Mapping.DocumentSimple doc")
                    .append(" JOIN doc.department dep")
                    .append(" WHERE dep.businessUnitId IN (").append(businessUnits).append(")")
                    .append(" AND doc.id=c.id ")
                    .append(" ) ");
        }
        //Accesos por permisos de carpeta
        condition.append(" OR ").append(Utilities.arrayToSelectInFullCondition(SessionHelper.getFolderAccess(loggedUser.getId(), this).toArray(), "c.nodoId"));
        if (bigAND) {
            condition.append(" )");
        }
        return condition.toString();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String filterTrashRowsForCurrentUser(LoggedUser loggedUser) {
        return filterRowsForCurrentUser(loggedUser, false, loggedUser.getServices().contains(ProfileServices.USUARIO_CORPORATIVO));
    }

    private Boolean hasAccessToDocument(Long documentId, LoggedUser loggedUser) {
        if (loggedUser.isAdmin()) {
            return true;
        }
        if (hasAccessByFolderAccess(documentId, loggedUser.getId())) {
            return true;
        }
        if (hasAccessByReference(documentId, loggedUser.getId())) {
            return true;
        }
        if (hasAccessByBusinessUnit(documentId, loggedUser.getId())) {
            return true;
        }
        if (hasAccessByBusinessUnitDepartment(documentId, loggedUser.getId())) {
            return true;
        }
        if (hasAccessByLastAuthor(documentId, loggedUser.getId())) {
            return true;
        }
        if (hasAccessByOriginator(documentId, loggedUser.getId())) {
            return true;
        }
        if (hasAccessByBusinessUnitDocumentManager(documentId, loggedUser.getId())) {
            return true;
        }
        if (hasAccessByBusinessUnitDepartmentDocumentManager(documentId, loggedUser.getId())) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> mailDataDocument(DocumentSimple documentSimple) {
        return new ArrayList<>(Arrays.asList(
            new TextHasValue(getTag("mail.id"), documentSimple.getCode()),
            new TextHasValue(getTag("mail.description"), documentSimple.getDescription())
        ));
    }

    @Override
    @OnNoReadersAssigned
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer saveNoDocumentReaders(Long documentId, LoggedUser loggedUser) {
        return HQL_updateByQuery(""
                + " UPDATE " + Document.class.getCanonicalName() + " c"
                + " SET c.readers = 0 WHERE c.id = :id", "id", documentId);
    }

    @Override
    @OnReadersAssigned
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer saveWithDocumentReaders(Long documentId, List<Long> readers, LoggedUser loggedUser) {
        return HQL_updateByQuery(""
                + " UPDATE " + Document.class.getCanonicalName() + " c"
                + " SET c.readers = 1 WHERE c.id = :id", "id", documentId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean hasDocumentReaders(Long documentId) {
        return HQL_findSimpleInteger(""
                + " SELECT c.readers"
                + " FROM " + Document.class.getCanonicalName() + " c"
            + " WHERE c.id = :id", "id" , documentId) == 1;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean readDocument(Long documentId, LoggedUser loggedUser) {
        IFilesDAO filesDao = getBean(IFilesDAO.class);
        final Long fileId = getFileId(documentId);
        if (fileId != null && fileId > 0) {
            filesDao.insertFileLogging(fileId, FileLoggingType.DOCUMENT_VIEWER, false, loggedUser);
        }
        final Map<String, Object> params = new HashMap();
        params.put("readerId", loggedUser.getId());
        params.put("documentId", documentId);
        DocumentReader dReader = (DocumentReader) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + DocumentReader.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0"
                + " AND c.readerId = :readerId"
                + " AND c.documentId = :documentId", params);
        IDocumentReaderDAO dao = getBean(IDocumentReaderDAO.class);
        if (dReader != null && Objects.equals(dReader.getReaded(), DocumentReader.NOT_READED)) {
            // Si el usuario estaba asignado como lector del documento     
            return dao.readDocument(dReader, loggedUser) != null;
        } else if (dReader == null) {
            /**
             * TO-DO: Aqui a veces truena el indice `idx_document_reader`, el motivo es que el usuario 
             *        deja abierta la vista previa del formulario 2 veces despues de que se actualzó el QMS.
             *
             *        Ambas pestañas se cargan al mismo tiempo insertando doble la lectura.
             * 
             * */
            return dao.saveReaded(documentId, loggedUser.getId(), loggedUser) != null;
        }
        return false;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean getAllowDownload(Long documentId, LoggedUser loggedUserDto) {
        Boolean allowDownload = HQL_findSimpleInteger(""
                + " SELECT c.documentType.downloadFiles"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :id and c.documentType.documentControlledType = '" + documentControlledType.UNCONTROLLED.toString() + "'", "id", documentId) == 1;
        if (allowDownload) {
            return true;
        }

        Long authorId = HQL_findSimpleLong(""
                + " SELECT c.author.id"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", documentId);
        if (loggedUserDto.getId().equals(authorId)) {
            return true;
        }
        Long departmentAttendantId = HQL_findSimpleLong(""
                + " SELECT c.department.attendantId"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", documentId);
        if (loggedUserDto.getId().equals(departmentAttendantId)) {
            return true;
        }
        return loggedUserDto.getServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)
                || loggedUserDto.getServices().contains(ProfileServices.DOCUMENTO_EDITOR)
                || loggedUserDto.isAdmin();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getFileId(Long documentId) {
        return HQL_findSimpleLong(""
                + " SELECT c.fileId"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", documentId);
    }

    private ShareDocumentDTO getInfoDocument(Long documentId) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", documentId);
        boolean hasFile = hasDocumentFile(documentId);
        if (hasFile) {
            return (ShareDocumentDTO) HQL_findSimpleObject(""
                + " SELECT new " + ShareDocumentDTO.class.getCanonicalName() +  "("
                        + " c.id,"
                        + " c.code,"
                        + " c.description,"
                        + " f.description,"
                        + " dt.shareMaxDownloads,"
                        + " dt.shareMaxViews,"
                        + " dt.shareLife"
                    + ") "
                + " FROM " + DocumentSimple.class.getCanonicalName() + " c "
                + " LEFT JOIN " + Files.class.getCanonicalName() + " f"
                + " ON c.fileId = f.id"
                + " JOIN c.documentType dt"
                + " WHERE c.id = :id", params);
        }
        return (ShareDocumentDTO) HQL_findSimpleObject(""
                + " SELECT new " + ShareDocumentDTO.class.getCanonicalName() +  "("
                        + " c.id,"
                        + " c.code,"
                        + " c.description,"
                        + " s.description,"
                        + " dt.shareMaxDownloads,"
                        + " dt.shareMaxViews,"
                        + " dt.shareLife"
                    + ") "
                + " FROM " + DocumentSimple.class.getCanonicalName() + " c "
                + " JOIN c.survey s"
                + " JOIN c.documentType dt"
                + " WHERE c.id = :id", params);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasDocumentFile(Long documentId) {
        boolean hasFile = HQL_findSimpleLong(""
                + " SELECT count(c.id)"
                + " FROM " + DocumentSimple.class.getCanonicalName() + " c "
                        + " WHERE c.id = " + documentId
                + " AND c.fileId IS NOT NULL") == 1L;
        return hasFile;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getSurveyId(Long documentId) {
        return HQL_findSimpleLong(""
                + " SELECT c.surveyId"
                + " FROM " + DocumentSimple.class.getCanonicalName() + " c "
                + " WHERE c.id = " + documentId);
    }
    
    
    /**
     *
     * @param docId
     * @param hasAdminRol
     * @param userId
     * @param hasUnrestrictedDocumentAccess
     * @param hasDocumentManagerRole
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasDocumentAccess(Long docId, boolean hasAdminRol, Long userId, Boolean hasUnrestrictedDocumentAccess, Boolean hasDocumentManagerRole) {
        ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            String nodeQuery = "SELECT c.nodoId FROM DPMS.Mapping.DocumentSimple c where c.id = " + docId;
            Integer nodo = this.HQL_findSimpleInteger(nodeQuery);
            Long documentId = docId;   
            boolean adminAccess = hasAdminRol || hasUnrestrictedDocumentAccess;
            boolean hasPermission = hasPermission(userId, nodo.longValue(), adminAccess, hasDocumentManagerRole);
            return adminAccess
                    || hasDocumentManagerRole
                    || hasPermission 
                    || hasDocumentPermission(documentId, userId);
        } finally {
            MeasureTime.stop(tStart, "Elapsed time getting document access " + docId + " for user " + userId);    
        }
    } 
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasDocumentAccessByRequest(
            Long requestId,
            boolean hasAdminRol, 
            Long userId, 
            Boolean hasUnrestrictedDocumentAccess, 
            Boolean hasDocumentManagerRole
    ) {
        ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            boolean adminAccess = hasAdminRol || hasUnrestrictedDocumentAccess;
            if (adminAccess) {
                return true;
            }
            if (hasDocumentManagerRole) {
                return true;
            }
            final IRequestDAO dao = getBean(IRequestDAO.class);
            final Boolean hasPending = dao.hasAuthorizationPending(requestId, userId) || dao.hasVerificationPending(requestId, userId);
            if (hasPending) {
                return true;
            }
            final Long nodeId = HQL_findSimpleLong(""
                    + " SELECT c.nodoId"
                    + " FROM " + Request.class.getCanonicalName() + " c "
                    + " WHERE c.id = :requestId",
                    ImmutableMap.of(
                            "requestId", requestId
                    ));
            boolean hasPermission = hasPermission(userId, nodeId, adminAccess, hasDocumentManagerRole);
            return hasPermission;
        } finally {
            MeasureTime.stop(tStart, "Elapsed time getting request access " + requestId + " for user " + userId);    
        }
    }
    
    
    /**
     *
     * @param documentId Id del documento
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasAccess(Long documentId, Long userId, Boolean isAdmin, List<ProfileServices> profileService) { 
        Boolean hasUnrestrictedDocumentAccess = profileService.contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
        final Boolean hasDocumentManagerRole = profileService.contains(ProfileServices.DOCUMENTO_ENCARGADO);
        return hasDocumentAccess(documentId, isAdmin, userId, hasUnrestrictedDocumentAccess, hasDocumentManagerRole);
    }
    
    /**
     * checa si el usuario tiene permiso directo en alguna carpeta con referncia
     * al documento
     *
     * @param iddoc: id del documento
     * @param id: id del usuario
     * @return
     */
    private boolean hasDocumentPermission(Long iddoc, Long id) {
        String strsql = " select sum(access) from ("
                + " SELECT count(1) as access "
                + " FROM document_reference r "
                + " JOIN tblcarpetausuario cu ON r.node_id = cu.intnodoid "
                + " WHERE exists ("
                    + " SELECT 1"
                    + " FROM document d "
                    + " WHERE d.id = " + iddoc
                    + " AND d.master_id = r.document_master_id "
                + " ) "
                + " AND cu.intusuarioid = " + id
                // si eres autor u originador
                + " UNION SELECT distinct 1 as access FROM tbldocumento d WHERE " + id + " IN (d.intautorid,d.intoriginadorid) AND  d.intdocumentoid = " + iddoc
                // si eres lector
                + " UNION SELECT distinct 1 as access FROM document_reader WHERE deleted = 0 AND reader_id = " + id + " AND document_id = " + iddoc
                // si eres encargado de documentos (departamento)
                + " UNION SELECT distinct 1 as access  FROM business_unit_department_view dp WHERE dp.document_manager_id = " + id
                + " AND exists ("
                + " SELECT 1 FROM document d WHERE d.business_unit_id = dp.business_unit_id AND d.id = " + iddoc
                + " )"
                // si eres encargado de documentos (planta)
                + " UNION SELECT distinct 1 as access  FROM business_unit bu WHERE bu.document_manager_id = " + id
                + " AND exists ("
                + " SELECT 1 FROM document d WHERE bu.business_unit_id = d.business_unit_id AND d.id = " + iddoc
                + " )) as tempTable";
        getLogger().trace("--------->>> {}",strsql);
        Integer count = Integer.valueOf(
                SQL_findSimpleString(strsql)
        );
        return count > 0;
    }

    /**
     * retorna si se tiene acceso al nodo con el id nodeId
     *
     * @param loggedUserId
     * @param nodeId
     * @param adminAccess
     * @param hasDocumentManagerRole
     * @return
     * @since 2.3.2.76
     * <AUTHOR>
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasPermission(
            final Long loggedUserId,
            final Long nodeId,
            final boolean adminAccess,
            final boolean hasDocumentManagerRole
    ) {
        if (nodeId == null) {
            return false;
        }
        boolean access = adminAccess;
        Object[] params = {loggedUserId, nodeId, access};
        if (adminAccess) {
            getLogger().info("User {} wants to access a document on node {}: adminAccess:{}",params);
        }
        if(!access){
            access = hasBusinessUnitPermission(loggedUserId, nodeId);
            params[2] =access;
            getLogger().info("User {} wants to access a document on node {}: businessUnit:{}",params);
        }
        
        if(!access){
            access = hasProcessPermission(loggedUserId, nodeId);
            params[2] =access;
            getLogger().info("User {} wants to access a document on node {}: process:{}",params);
        }
        
        if(!access){
            access = hasDepartmentPermission(loggedUserId, nodeId);
            params[2] = access;
            getLogger().info("User {} wants to access a document on node {}: department:{}",params);
        }
        
        if(!access){
            access = hasUserPermission(loggedUserId, nodeId);
            params[2] = access;
            getLogger().info("User {} wants to access a document on node {}: user:{}",params);
        }
        if(!access){
            access = hasBusinessUnitSpecialPermission(nodeId, loggedUserId);
            params[2] = access;
            getLogger().info("User {} wants to access a document on node {}: hasBusinessUnitSpecialPermission:{}",params);
        }
        if(!access){
            access = hasDepartmentSpecialPermission(nodeId, loggedUserId);
            params[2] =access;
            getLogger().info("User {} wants to access a document on node {}: hasDepartmentSpecialPermission:{}",params);
        }
        if(!access) {
            access = hasNodeDocumentManagerPermission(nodeId, loggedUserId, hasDocumentManagerRole);
            params[2] =access;
            getLogger().info("User {} wants to access a document on node {}: hasDepartmentSpecialPermission:{}",params);
        }
        return access;
    }
    
    private boolean hasNodeDocumentManagerPermission(final Long nodeId, final Long userId, final Boolean hasDocumentManagerRole) {
        if (!hasDocumentManagerRole) {
            return false;
        }
        final String businessUnits = ""
                + " SELECT DISTINCT bun.id "
                + " FROM " + User.class.getCanonicalName() + " usr "
                + " JOIN usr.puestos as pst "
                + " JOIN pst.une as bun"
                + " WHERE usr.id = " + userId
                + " AND bun.deleted = 0 ";
        final Integer count = HQL_findSimpleInteger(""
            + " SELECT count(1) "
            + " FROM " + Document.class.getCanonicalName() + " ds "
            + " WHERE ds.nodo.id = " + nodeId
            + " AND ds.deleted = " + DocumentSimple.IS_NOT_DELETED
            + " AND ds.status NOT IN (" + Document.CANCELED_STATUS + ", " + Document.CANCELED_DISCONTINUED_STATUS + ")"
            + " AND ("
                + " exists ( "
                    + " SELECT 1"
                    + " FROM " + DocumentSimple.class.getCanonicalName() + " doc"
                    + " WHERE doc.businessUnitId IN (" + businessUnits + ")"
                    + " AND doc.id = ds.id "
                + " ) "
                + " OR exists ( "
                    + " SELECT 1"
                    + " FROM " + DocumentSimple.class.getCanonicalName() + " doc"
                    + " JOIN doc.department dep"
                    + " WHERE dep.businessUnitId IN (" + businessUnits + ")"
                    + " AND doc.id = ds.id "
                + " ) "
            + ")"
        );
        boolean access = count > 0;
        getLogger().trace("hasDocumentManagerPermission: {}",access);
        return (access);
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasDocumentManagerPermission(
        final Long documentId, 
        final Long loggedUserId,
        boolean hasDocumentManagerRole
    ) {
        if (!hasDocumentManagerRole) {
            return false;
        }
        Map<String, Object> params = new HashMap<>(2);
        params.put("id", documentId);
        params.put("userId", loggedUserId);
        final String businessUnits = ""
                + " SELECT DISTINCT bun.id "
                + " FROM " + User.class.getCanonicalName() + " usr "
                + " JOIN usr.puestos as pst "
                + " JOIN pst.une as bun"
                + " WHERE usr.id = :userId"
                + " AND bun.deleted = 0 ";
        return HQL_findSimpleLong(""
                + DOCUMENT_COUNT
                + " WHERE c.id = :id"
                + " AND c.deleted = " + DocumentSimple.IS_NOT_DELETED
                + " AND c.status NOT IN (" + Document.CANCELED_STATUS + ", " + Document.CANCELED_DISCONTINUED_STATUS + ")"
                + " AND ("
                    + " exists ( "
                        + " SELECT 1"
                        + " FROM " + DocumentSimple.class.getCanonicalName() + " doc"
                        + " WHERE doc.businessUnitId IN (" + businessUnits + ")"
                        + " AND doc.id = c.id "
                    + " ) "
                    + " OR exists ( "
                        + " SELECT 1"
                        + " FROM " + DocumentSimple.class.getCanonicalName() + " doc"
                        + " JOIN doc.department dep"
                        + " WHERE dep.businessUnitId IN (" + businessUnits + ")"
                        + " AND doc.id = c.id "
                    + " ) "
                + ")", params) > 0;
    }
    
  /**
   * Determina si un usuario tiene acceso o no a un nodo por medio de la planta
   * 
   * @param userId el usuario que desea acceder
   * @param nodeId el nodo al que quiere accede
   * @return boolean con el acceso al nodo
   * <AUTHOR> Germán Lares Lares
   * @version 2.3.2.155 
   */
    private boolean hasBusinessUnitPermission(Long userId, Long nodeId){
        final Map<String, Object> map = new HashMap<>(2);
        map.put("nodeId",nodeId);
        map.put("userId",userId);
        String query = BUSINESS_UNIT_PERMISSION;
        List result = HQL_findByQuery(query,map);
        Boolean access = !result.isEmpty();
        getLogger().trace("hasBusinessUnitPermission: {}",access);
        return access;
    }
    
  /**
   * Determina si un usuario tiene acceso o no a un nodo por medio del proceso
   * 
   * @param userId el usuario que desea acceder
   * @param nodeId el nodo al que quiere accede
   * @return boolean con el acceso al nodo
   * <AUTHOR> Germán Lares Lares
   * @version 2.3.2.155 
   */
    private boolean hasProcessPermission(Long userId, Long nodeId){
        Map<String, Object> map = new HashMap<>(2);
        map.put("nodeId",nodeId);
        map.put("userId",userId);
        String query = PROCESS_PERMISSION;
        List result = HQL_findByQuery(query,map);
        Boolean access = !result.isEmpty();
        getLogger().trace("hasProcessPermission: {}",access);
        return access;
    }
    
  /**
   * Determina si un usuario tiene acceso o no a un nodo por medio del departamento
   * 
   * @param userId el usuario que desea acceder
   * @param nodeId el nodo al que quiere accede
   * @return boolean con el acceso al nodo
   * <AUTHOR> Germán Lares Lares
   * @version 2.3.2.155 
   */
    private boolean hasDepartmentPermission(Long userId, Long nodeId){
        Map<String, Object> map = new HashMap<>(2);
        map.put("nodeId",nodeId);
        map.put("userId",userId);
        String query = DEPARTMENT_PERMISSION;
        List result = HQL_findByQuery(query,map);
        Boolean access = !result.isEmpty();
        getLogger().trace("hasDepartmentPermission: {}",access);
        return access;
    }
    
  /**
   * Determina si un usuario tiene acceso o no a un nodo por medio de la lista de usuarios
   * 
   * @param userId el usuario que desea acceder
   * @param nodeId el nodo al que quiere accede
   * @return boolean con el acceso al nodo
   * <AUTHOR> Germán Lares Lares
   * @version 2.3.2.155 
   */
    private boolean hasUserPermission(Long userId, Long nodeId){
        Map<String, Object> map = new HashMap<>(2);
        map.put("nodeId",nodeId);
        map.put("userId",userId);
        String query = USER_PERMISSION;
        List result = HQL_findByQuery(query,map);
        Boolean access = !result.isEmpty();
        getLogger().trace("hasUserPermission: {}",access);
        return (access);
    }
    
  /**
   * Determina si un usuario tiene acceso o no a un nodo por ser el encargado de la planta 
   * donde se encuentra el documento
   *
   * @param nodeId el nodo al que quiere accede
     * @param userId
   * @return boolean con el acceso al nodo
   * <AUTHOR> Germán Lares Lares
   * @version 2.3.2.155 
   */
    private boolean hasBusinessUnitSpecialPermission(Long nodeId, Long userId){
        int count = HQL_findSimpleInteger(""
            + " SELECT count(1) "
            + " FROM " + Document.class.getCanonicalName() + " doc "
            + " WHERE doc.businessUnit.id IN ("
                + " SELECT bun.id "
                + " FROM " + User.class.getCanonicalName() +  " usr "
                + " JOIN usr.puestos as pst "
                + " JOIN pst.une as bun"
                + " WHERE "
                    + " usr.id = " + userId
                    + " AND bun.deleted = 0 "
            + ") "
            + " AND doc.businessUnit.documentManagerId = :userId"
            + " AND doc.nodo.id = " + nodeId
            + " AND doc.status NOT IN (" + Document.CANCELED_STATUS + ", " + Document.CANCELED_DISCONTINUED_STATUS + ")"
            + " AND doc.deleted = " + Document.IS_NOT_DELETED,
            ImmutableMap.of("userId", userId)
        );
        boolean access = count > 0;
        getLogger().trace("hasBusinessUnitSpecialPermission: {}",access);
        return access;
    }
    
  /**
   * Determina si un usuario tiene acceso o no a un nodo por ser encargado del departamento
   * en que está el documento.
   * 
   * @param nodeId el nodo al que quiere accede
   * @return boolean con el acceso al nodo
   * <AUTHOR> Germán Lares Lares
   * @version 2.3.2.155 
   */
    private boolean hasDepartmentSpecialPermission(Long nodeId, Long userId){
        int count = HQL_findSimpleInteger(""
            + " SELECT count(1) "
            + " FROM " + DocumentSimple.class.getCanonicalName() + " ds "
            + " WHERE ds.department.businessUnitId IN ("
                + " SELECT bun.id "
                + " FROM " + User.class.getCanonicalName() +  " usr "
                + " JOIN usr.puestos as pst "
                + " JOIN pst.une as bun"
                + " WHERE "
                    + " usr.id = " + userId
                    + " AND bun.deleted = 0 "
            + ") "
            + " AND ds.department.documentManagerId = :userId"
            + " AND ds.nodoId = " + nodeId
            + " AND ds.deleted = " + DocumentSimple.IS_NOT_DELETED
            + " AND ds.status NOT IN (" + Document.CANCELED_STATUS + ", " + Document.CANCELED_DISCONTINUED_STATUS + ")",
            ImmutableMap.of("userId", userId)
        );
        boolean access = count > 0;
        getLogger().trace("hasDepartmentSpecialPermission: {}",access);
        return (access);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isRequestInProcess(Long documentId, Request.TYPE type) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("documentId", documentId);
        params.put("requestType", type.getValue());
        switch (type) {
            case FILL:
                return HQL_findSimpleLong(DOCUMENT_REQUEST_FILL_COUNT, params) > 0;
            default:
                return HQL_findSimpleLong(DOCUMENT_REQUEST_TYPED_COUNT, params) > 0;
        }
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Map<String, Object> getDocumentReaderDetails(Long documentReaderId){
        return HQL_findSimpleMap(""
                + " SELECT"
                + " new Map(c.documentId as documentId, d.fileId as fileId, d.code as code)"
                + " FROM " + DocumentReader.class.getCanonicalName() + " c"
                + " ," + Document.class.getCanonicalName() + " d"
                + " WHERE c.documentId = d.id"
                + " AND c.id = :id", "id", documentReaderId);
    }
    
    private String buildSubFolderAccessFilter(
            final String nodeIdProperty,
            final boolean isRootNode,
            final Long loggedUserId,
            final boolean isAdmin,
            final boolean isDocumentManager
    ) {
        final String baseFilterStr = " " + nodeIdProperty + " NOT IN " + excludedNodes + " AND c.deleted = 0";
        if (isAdmin || isRootNode) {
            return baseFilterStr;
        }
        final String folderPermissionFilter = HibernateDAO_Document.FOLDER_PERMISSION
                .replaceAll(":userId", loggedUserId.toString())
                .replaceAll(":nodeId", nodeIdProperty);
        if (isDocumentManager) {
            final String businessUnitFilter = ""
                + " SELECT DISTINCT bun.id "
                + " FROM " + User.class.getCanonicalName() + " usr "
                + " JOIN usr.puestos as pst "
                + " JOIN pst.une as bun"
                + " WHERE usr.id = " + loggedUserId
                + " AND bun.deleted = 0 ";
            return baseFilterStr + folderPermissionFilter
                .replaceAll(":isDocMgr", HibernateDAO_Document.DOCUMENT_MANAGER_PERMISSION
                .replaceAll(":businessUnits", businessUnitFilter)
                .replaceAll(":nodeId", nodeIdProperty)
                .replaceAll(":userId", loggedUserId.toString()));
        } else {
            return baseFilterStr + folderPermissionFilter.replaceAll(":isDocMgr", "");
        }
    }
    
    /**
     * Devuelve el filtro de HQL para ver los accesos a las carpetas hijas
     *
     * @param filter
     * @param loggedUserId
     * @param isAdmin
     * @param services
     * @return
     * @since 2.3.2.76
     * <AUTHOR>
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getSubFolderAccessFilter(
            final SortedPagedFilter filter,
            final Long loggedUserId,
            boolean isAdmin, 
            final List<ProfileServices> services
    ) {
        final boolean isRootNode = filter.getCriteria().containsKey("id")
                && filter.getCriteria().get("id") != null
                && filter.getCriteria().get("id").equals("0L");
        final Boolean isDocumentManager =  services.contains(ProfileServices.DOCUMENTO_ENCARGADO);
        return buildSubFolderAccessFilter("c.id", isRootNode, loggedUserId, isAdmin, isDocumentManager);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getSubFoldersAccessFilter(
            final String parentNodeId,
            final Long loggedUserId,
            boolean isAdmin,
            final List<ProfileServices> services
    ) {
        final String nodeSql = QUERY_GET_NODE_CHILDRENS;
        final List<Long> subFolderIds = SQL_findByQuery(nodeSql.replace(":nodoPadre", parentNodeId));
        if (!subFolderIds.isEmpty()) {
            final Boolean isDocumentManager = services.contains(ProfileServices.DOCUMENTO_ENCARGADO);
            final Boolean hasFullDocumentAccess = services.contains(ProfileServices.UNRESTRICTED_DOCUMENT_ACCESS);
            final Boolean adminAccess = isAdmin || hasFullDocumentAccess;
            final List<Long> folderIds = HQL_findByQuery(""
                    + " SELECT c.id "
                    + " FROM " + Node.class.getCanonicalName() + " c"
                    + " WHERE c.id = " + parentNodeId 
                    + " OR (" + BindUtil.parseFilterList("c.id", subFolderIds)
                       + " AND " + buildSubFolderAccessFilter("c.id", false, loggedUserId, adminAccess, isDocumentManager)
                    + " )");
            return BindUtil.parseFilterList("c.nodoId", folderIds);
        }
        return null;
    }    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasFormPending(final Long outstandingSurveyId, final Long loggedUserId) {
        final Integer countToFill = new ToFillForm(this).getCountByUser(loggedUserId);
        return countToFill > 0;
    }
    private String buildNodesFilter(boolean adminAccess, boolean isDocumentManager) {
        String baseNodeFilter = ""
            + " c.module = '" + Module.DOCUMENT.getKey() + "'"
            + " AND c.id NOT IN ("
                + Properties.NODO_ACCIONES + ","
                + Properties.NODO_PROYECTOS + ","
                + Properties.NODO_QUEJAS
            + " )"
            + " AND c.parent IS NOT NULL"
            + " AND c.path IS NOT NULL"
            + " AND NOT ("
                + " c.parent = 0 AND c.topLevel = 0"
            + " )"
            + " AND c.deleted = 0";
        if (!adminAccess) {
            final String locationFilter;
            if (isDocumentManager) {
                final String businessUnitFilter = ""
                        + " SELECT DISTINCT bun.id "
                        + " FROM " + User.class.getCanonicalName() + " usr "
                        + " JOIN usr.puestos as pst "
                        + " JOIN pst.une as bun"
                        + " WHERE usr.id = :loggedUserId"
                        + " AND bun.deleted = 0 ";
                locationFilter = "" 
                            /* El usuario es encargado de documentos por planta y tiene asociada la planta del documento */
                            + " OR EXISTS ("
                                + " SELECT 1"
                                + " FROM " + Document.class.getCanonicalName() + " doc "
                                + " WHERE doc.businessUnit.id IN (" + businessUnitFilter + ") "
                                + " AND doc.businessUnit.documentManagerId = :loggedUserId"
                                + " AND doc.nodo.id = c.id"
                            + " )"
                            /* El usuario es encargado de documentos por departamento y tiene asociada la planta del documento */
                            + " OR EXISTS ("
                                + " SELECT 1"
                                + " FROM " + DocumentSimple.class.getCanonicalName() + " ds "
                                + " WHERE ds.department.businessUnitId IN (" + businessUnitFilter + ")"
                                + " AND ds.department.documentManagerId =  :loggedUserId"
                                + " AND ds.nodoId = c.id"
                            + " )";
            } else {
                locationFilter = "";
            }
            baseNodeFilter += ""
                + " AND ("
                        /* El usuario tiene asociada un área que tiene permiso de ver la carpeta */
                        + " EXISTS ("
                            + " SELECT 1 "
                            + " FROM " + NodeArea.class.getCanonicalName() + " na "
                            + " WHERE EXISTS ("
                                + " SELECT 1 "
                                + " FROM " + User.class.getCanonicalName() + " u "
                                + " JOIN u.puestos p "
                                + " JOIN p.departmentProcess dp "
                                + " WHERE u.id = :loggedUserId"
                                + " AND na.id.areaId = dp.processId "
                            + " ) AND na.id.nodeId = c.id "
                        + ") "
                        /* El usuario tiene asociada una planta que tiene permiso de ver la carpeta */
                        + " OR EXISTS ("
                            + " SELECT 1 "
                            + " FROM " + NodeBusinessUnit.class.getCanonicalName() + " nb "
                            + " WHERE EXISTS ("
                                + " SELECT 1 "
                                + " FROM " + User.class.getCanonicalName() + " u "
                                + " JOIN u.puestos p "
                                + " WHERE u.id = :loggedUserId"
                                + " AND nb.id.businessUnitId = p.businessUnitId "
                            + " ) AND nb.id.nodeId = c.id  "
                        + ") "
                        /* El usuario esta configurado para ver la carpeta */
                        + " OR EXISTS ("
                            + " SELECT 1"
                            + " FROM " + NodeUser.class.getCanonicalName() + " nu"
                            + " WHERE nu.id.userId =:loggedUserId"
                            + " AND nu.id.nodeId = c.id"
                        + " )"
                        /* El usuario tiene asociada un departamento que tiene permiso de ver la carpeta */
                        + " OR EXISTS ("
                            + " SELECT 1 "
                            + " FROM " + NodeBusinessUnitDepartment.class.getCanonicalName() + " nbud"
                            + " WHERE EXISTS ("
                                + " SELECT 1 "
                                + " FROM " + User.class.getCanonicalName() + " u "
                                + " JOIN u.puestos p "
                                + " JOIN p.departamentos d "
                                + " WHERE d.id = nbud.id.businessUnitDepartmentId "
                                + " AND u.id = :loggedUserId"
                            + " ) AND nbud.id.nodeId = c.id"
                        + " )"
                        + locationFilter
                + " )";
        }
        return baseNodeFilter;
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public List<NodePathDTO> getMyNodes(final Long loggedUserId, final boolean adminAccess, final boolean isDocumentManager) {
        final String baseNodeFilter = buildNodesFilter(adminAccess, isDocumentManager);
        final String hqlNodes = ""
                + " SELECT new " + NodePathDTO.class.getCanonicalName() + "("
                    + " c.id, c.description, c.path, c.parent, c.code"
                + " )"
                + " FROM " + NodeSimple.class.getCanonicalName() + " c"
                + " WHERE " + baseNodeFilter
                + " AND c.module = '" + Module.DOCUMENT.getKey() + "'"
                + " ORDER BY c.description ASC";
        Map<String, Object> params = new HashMap<>();
        params.put("loggedUserId", loggedUserId);
        final List<NodePathDTO> nodes = HQL_findByQuery(hqlNodes, params);
        return nodes; 
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public GridInfo<Map<String, Object>> getFolderNodes(final Long loggedUserId, final boolean adminAccess, final boolean isDocumentManager, SortedPagedFilter filter) {
        final String baseNodeFilter = buildNodesFilter(adminAccess, isDocumentManager).replaceAll(":loggedUserId", loggedUserId.toString());
        final String hqlNodes = ""
                + " SELECT new " + NodePathDTO.class.getCanonicalName() + "("
                    + " c.id, c.description, c.path, c.parent, c.code"
                + " )"
                + " FROM " + NodeSimple.class.getCanonicalName() + " c"
                + " WHERE " + baseNodeFilter
                + " AND c.module = '" + Module.DOCUMENT.getKey() + "'";
        return HQL_getRows(hqlNodes, filter);
    }

    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public GridInfo<NodePathDTO> getNodes(
            final Long parentFolderId,
            final SortedPagedFilter filter,
            final Long loggedUserId,
            final Boolean adminAccess,
            final Boolean documentManager
    ) {
        final String parentPath = (String) HQL_findSimpleObject(""
                + " SELECT c.path"
                + " FROM " + NodeSimple.class.getCanonicalName() + " c"
                + " WHERE c.id = " + parentFolderId);
        String baseNodeFilter = ""
                + " c.id NOT IN (" + parentFolderId + ")" 
                + " AND c.path LIKE '" + parentPath + "%'"
                + " AND ("
                    + buildNodesFilter(adminAccess, documentManager)
                + " )";
        baseNodeFilter = baseNodeFilter.replaceAll(":loggedUserId", loggedUserId.toString());
        filter.getCriteria().put("<condition>", baseNodeFilter);
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("path");
        }
        final String hqlNodes = ""
                + " SELECT new " + NodePathDTO.class.getCanonicalName() + "("
                    + " c.id, c.description, c.path, c.parent, c.code "
                + " )"
                + " FROM " + NodeSimple.class.getCanonicalName() + " c";
        return HQL_getRows(hqlNodes, filter);
    }   
    
    @Override
    @OnDocumentTypeExpirationEdited
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer changeDocumentTypeExpiration(
            final Long documentTypeId,
            final Integer currentExpiration, 
            final Integer oldExpiration,
            final LoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("documentTypeId", documentTypeId);
        params.put("currentExpiration", currentExpiration);
        return HQL_updateByQuery(""
                + " UPDATE " + Document.class.getCanonicalName() + " c"
                + " SET c.expirationDate = DATEADD(MONTH, :currentExpiration, c.lastModificationDate)"
                + " WHERE c.id IN ("
                    + " SELECT d.id "
                    + " FROM " + Document.class.getCanonicalName() + " d"
                    + " JOIN d.documentType type"
                    + " LEFT JOIN d.department dep"
                    + " WHERE type.id = :documentTypeId"
                    + " AND ("
                            + " dep.id IS NULL OR dep.documentExpiration IS NULL"
                    + ")"
                    + " AND d.status IN ("
                        + Document.STATUS.ACTIVE.getValue()
                        + "," + Document.STATUS.IN_EDITION.getValue()
                + ")"
                    + " AND d.deleted = 0"
                + " )", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getExpiredSoonDocumentsForCurrentUser(SortedPagedFilter filter, ILoggedUser loggedUser) {
        final String condition = ToRenew.TO_RENEW_SOON
                .replaceAll(":userId", loggedUser.getId().toString())
                .replaceAll(":documentId", "c.id");
        filter.getCriteria().put("<condition>", condition);
        return getRows(filter);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateFavoritesStatus(String masterDocumentId, Integer status, ILoggedUser loggedUser) {
        HQL_updateByQuery(""
            + " UPDATE " + FavoriteTask.class.getCanonicalName() + " c "
            + " SET "
                + " c.status = :status"
                + " ,lastModifiedDate = CURRENT_DATE() "
                + " ,lastModifiedBy = :userId" 
            + " WHERE "
                + " c.documentMasterId = :documentMasterId "
            , ImmutableMap.of(
                    "documentMasterId", masterDocumentId,
                    "status", status,
                    "userId", loggedUser.getId()
            ),
            true,
            CacheRegion.FAVORITE,
            0
        );
    }
    
    @Override
    @OnDocumentBusinessUnitDepartmentExpirationEdited
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer changeDocumentBusinessUnitDepartmentExpiration(
            final Long businessUnitDepartmentId,
            final Integer currentExpiration, 
            final Integer oldExpiration,
            final LoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("businessUnitDepartmentId", businessUnitDepartmentId);
        if (currentExpiration != null) {
            params.put("currentExpiration", currentExpiration);
            return HQL_updateByQuery(""
                    + " UPDATE " + Document.class.getCanonicalName() + " c"
                    + " SET c.expirationDate = DATEADD(MONTH, :currentExpiration, c.lastModificationDate)"
                    + " WHERE c.id IN ("
                        + " SELECT d.id "
                        + " FROM " + Document.class.getCanonicalName() + " d"
                        + " JOIN d.department dep"
                        + " WHERE dep.id = :businessUnitDepartmentId"
                        + " AND d.status IN ("
                            + Document.STATUS.ACTIVE.getValue()
                            + "," + Document.STATUS.IN_EDITION.getValue()
                    + ")"
                        + " AND d.deleted = 0"
                    + " )", params);
        } else {
            return HQL_updateByQuery(""
                    + " UPDATE " + Document.class.getCanonicalName() + " c"
                    + " SET c.expirationDate = DATEADD(MONTH, ("
                        + " SELECT type.documentExpiration "
                        + " FROM " + Document.class.getCanonicalName() + " d"
                        + " JOIN d.documentType type"
                        + " WHERE d.id = c.id"
                    + "), c.lastModificationDate)"
                    + " WHERE c.id IN ("
                        + " SELECT d.id "
                        + " FROM " + Document.class.getCanonicalName() + " d"
                        + " JOIN d.department dep"
                        + " WHERE dep.id = :businessUnitDepartmentId"
                        + " AND d.status IN ("
                            + Document.STATUS.ACTIVE.getValue()
                            + "," + Document.STATUS.IN_EDITION.getValue() + ")"
                        + " AND d.deleted = 0"
                    + " )", params);            
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<DocumentLinkedSelectorDTO> getForms(GridFilter filter, ILoggedUser loggedUser) {
        if (filter == null) {
            filter = new GridFilter();
        }
        final String condition = filterRowsForCurrentUser(loggedUser);
        final Object previousCondition = filter.getCriteria().get("<condition>");
        if (previousCondition == null || previousCondition.toString().trim().isEmpty()) {
            filter.getCriteria().put("<condition>", condition + " AND deleted = 0");
        } else {
            filter.getCriteria().put("<condition>", previousCondition + " AND deleted = 0 AND " + condition);
        }
        filter.getField().setOrderBy("id");
        filter.setDirection((byte) 2);
        return HQL_getRows(" "
            + " SELECT new " + DocumentLinkedSelectorDTO.class.getCanonicalName() + " ("
                    + DocumentLinkedSelectorDTO.HQL_DIALOG_DOCUMENT_LINKED_SELECTOR
                + " )"
            + " FROM " + Document.class.getCanonicalName() + " c "
            + " WHERE c.status IN ("
                + " " + Document.STATUS.ACTIVE.getValue()
                + "," + Document.STATUS.IN_EDITION.getValue()
            + " )"
            + " AND c.surveyId is not null"
            + " AND c.documentType.documentControlledType = '" + DocumentType.documentControlledType.CONTROLLED.toString() + "'", filter);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean moveDocument(Long documentid, Long nodeid, ILoggedUser loggedUser) {
        IDocumentDAO DAO = getBean(IDocumentDAO.class);
        boolean hasAccess = loggedUser.isAdmin() || loggedUser.getServices().contains(ProfileServices.DOCUMENTO_ENCARGADO);
        if (!hasAccess) {
            List<Long> nodos = SessionHelper.getFolderAccess(loggedUser.getId(), this);
            hasAccess = nodos.contains(nodeid);
        }
        Integer count = 0;
        if (hasAccess) {
            Map<String,Long> map = new HashMap<>();
            map.put("id", documentid);
            Long surveyId = DAO.HQL_findLong("SELECT surveyId FROM " + Document.class.getCanonicalName() + " WHERE id = :id", map);
            map.put("nid", nodeid);
            count = DAO.HQL_updateByQuery(""
                    + " UPDATE " + Document.class.getCanonicalName() + " document "
                    + " SET document.nodo.id=:nid "
                    + " WHERE document.id=:id", map);
            if (surveyId != null && count > 0) {
                map.put("surveyId", surveyId);
                DAO.HQL_updateByQuery(""
                    + " UPDATE " + Request.class.getCanonicalName() + " c"
                    + " SET c.nodoId = :nid "
                    + " WHERE c.surveyId = :surveyId "
                    + " AND c.fillOutDocument.id = :id "
                    + " AND c.status IN ("
                        + WorkflowRequestStatus.APROVING.getValue() + ","
                        + WorkflowRequestStatus.RETURNED.getValue() + ","
                        + Request.STATUS.STAND_BY.getValue() + ","
                        + WorkflowRequestStatus.VERIFING.getValue() 
                    + " )"
                + " AND c.deleted = 0", map);
            }
        }
        return count > 0;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<TextHasValue> getRestrictRecordsFieldValues(Long surveyId) {
        final List<TextHasValue> results = HQL_findByQuery(""
                + " SELECT new " + TextHasValue.class.getCanonicalName() + " ("
                    + " MAX(pt.title) as text,"
                    + " p.field_id as value"
                + " )"
                + " FROM " + SurveyFieldObject.class.getCanonicalName() + " p "
                + " JOIN " + ExternalDocumentCatalog.class.getCanonicalName() + " d "
                + " ON d.id = p.externalCatalogId"
                + " JOIN d.query q "
                + " JOIN p.title pt"
                + " WHERE p.surveyId = :id"
                + " AND p.catalogType = '" + SurveyParserHelper.EXTERNAL_CATALOG_ID + "'"
                + " AND p.catalogSubType = '" + CatalogFieldType.CATALOG_HIERARCHY.getName() + "'"
                + " AND q.hasBusinessUnitDepartmentKey = 1"
                + " GROUP BY"
                + " p.field_id ",
                ImmutableMap.of("id", surveyId),
                true,
                CacheRegion.SURVEY,
                0
        );
        return results;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public FormSaveDTO loadForm(Long id) {
        final FormSaveDTO result = HQLT_findSimple(FormSaveDTO.class, ""
            + " SELECT new " + FormSaveDTO.class.getCanonicalName() + "("
                + "d.id as id,"
                + "d.code as code,"
                + "d.description as description,"
                + "d.surveyId as surveyId,"
                + "d.restrictRecordsByDepartment as restrictRecordsByDepartment,"
                + "d.validateAccessFormDepartment as validateAccessFormDepartment,"
                + "d.restrictRecordsField as restrictRecordsField"
            + " )"
            + " FROM " + Document.class.getCanonicalName() + " d "
            + " WHERE d.id = :id",
            "id", id
        );
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer saveForm(FormSaveDTO dto, ILoggedUser loggedUser) {
        Long restrictRecordsObjId = null;
        if (dto.getRestrictRecordsField() != null && !dto.getRestrictRecordsField().isEmpty()) {
            restrictRecordsObjId = HQL_findLong(""
                    + " SELECT p.id"
                    + " FROM " + SurveyFieldObject.class.getCanonicalName() + " p "
                    + " WHERE p.surveyId = :surveyId"
                    + " AND p.catalogType = '" + SurveyParserHelper.EXTERNAL_CATALOG_ID + "'"
                    + " AND p.catalogSubType = '" + CatalogFieldType.CATALOG_HIERARCHY.getName() + "'"
                    + " AND p.field_id = :field",
                    ImmutableMap.of(
                        "surveyId", dto.getSurveyId(),
                        "field", dto.getRestrictRecordsField()
                    ),
                    true,
                    CacheRegion.SURVEY,
                    0
            );
            if (Objects.equals(restrictRecordsObjId, 0l) || Objects.equals(restrictRecordsObjId, -1l)) {
                restrictRecordsObjId = null;
            }
        }
        final FormSaveDTO previous = loadForm(dto.getId());
        if (Objects.equals(previous.getRestrictRecordsByDepartment(), true) 
                && Objects.equals(previous.getValidateAccessFormDepartment(), false)
                && !Objects.equals(previous.getRestrictRecordsField(), dto.getRestrictRecordsField())
        ) {
            HQL_updateByQuery(""
                    + " UPDATE " + SurveyFieldObject.class.getCanonicalName() + " c"
                    + " SET"
                            + " c.businessUnitDepartmentMainField = 0"
                    + " WHERE c.surveyId = :surveyId"
                    + " AND c.field_id = :field_id",
                    ImmutableMap.of(
                            "surveyId", previous.getSurveyId(),
                            "field_id", previous.getRestrictRecordsField()
                    )
            );
        }
        if (restrictRecordsObjId == null) {
            return HQL_updateByQuery(""
                    + " UPDATE " + Document.class.getCanonicalName() + " c"
                    + " SET"
                            + " c.restrictRecordsByDepartment = :restrictRecordsByDepartment,"
                            + " c.validateAccessFormDepartment = :validateAccessFormDepartment,"
                            + " c.restrictRecordsField = null,"
                            + " c.restrictRecordsObjId = null"
                    + " WHERE c.id = :id",
                    ImmutableMap.of(
                            "id", dto.getId(),
                            "restrictRecordsByDepartment", dto.getRestrictRecordsByDepartment(),
                            "validateAccessFormDepartment", dto.getValidateAccessFormDepartment()
                    )
            );
        } else {
            HQL_updateByQuery(""
                    + " UPDATE " + SurveyFieldObject.class.getCanonicalName() + " c"
                    + " SET"
                            + " c.businessUnitDepartmentMainField = 1"
                    + " WHERE c.surveyId = :surveyId"
                    + " AND c.field_id = :field_id",
                    ImmutableMap.of(
                            "surveyId", dto.getSurveyId(),
                            "field_id", dto.getRestrictRecordsField()
                    )
            );
            return HQL_updateByQuery(""
                    + " UPDATE " + Document.class.getCanonicalName() + " c"
                    + " SET"
                            + " c.restrictRecordsByDepartment = :restrictRecordsByDepartment,"
                            + " c.validateAccessFormDepartment = :validateAccessFormDepartment,"
                            + " c.restrictRecordsField = :restrictRecordsField,"
                            + " c.restrictRecordsObjId = :restrictRecordsObjId"
                    + " WHERE c.id = :id",
                    ImmutableMap.of(
                            "id", dto.getId(),
                            "restrictRecordsByDepartment", dto.getRestrictRecordsByDepartment(),
                            "validateAccessFormDepartment", dto.getValidateAccessFormDepartment(),
                            "restrictRecordsField", dto.getRestrictRecordsField(),
                            "restrictRecordsObjId", restrictRecordsObjId
                    )
            );
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateExpiredRequests(List<Long> documentIds){
        Integer result = 0;
        if (!documentIds.isEmpty()) {
            result = HQL_updateByQuery(""
                + " UPDATE " + Document.class.getCanonicalName() + " c"
                + " SET"
                        + " c.expiredRetention = 1"
                + " WHERE c.id IN (:id)",
                ImmutableMap.of(
                        "id", documentIds
                )
            );
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IJoinableDocument loadDocumentJoinable(Long documentId, DocumentDataIndex index) {
        if (documentId == null || documentId <= 0) {
            return null;
        }
        if (index.containsJoinableDocument(documentId)) {
            return index.getJoinableDocument(documentId);
        }

        final IPlainDocument plainDocument;

        if (index.containsPlainDocument(documentId)) {
            plainDocument = index.getPlainDocument(documentId);
        } else {
            plainDocument = loadPlainDocument(documentId);
            index.setPlainDocument(documentId, plainDocument);
        }

        if (plainDocument == null) {
            return null;
        }
        final JoinableDocumentDTO document = new JoinableDocumentDTO(plainDocument);
        index.setJoinableDocument(documentId, document);

        Long businessUnitId = document.getBusinessUnitId();
        if (index.containsBusinessUnit(businessUnitId)) {
            document.setBusinessUnit(index.getBusinessUnit(businessUnitId));
        } else {
            IBusinessUnitDAO businessUnitDAO = getBean(IBusinessUnitDAO.class);
            IPlainBusinessUnit businessUnit = businessUnitDAO.loadPlainBusinessUnit(businessUnitId);
            index.setBusinessUnit(businessUnitId, businessUnit);
            document.setBusinessUnit(businessUnit);
        }

        Long typeId = document.getTypeId();
        if (index.containsDocumentType(typeId)) {
            document.setDocumentType(index.getDocumentType(typeId));
        } else {
            IPlainDocumentType documentType = loadPlainDocumentType(typeId);
            document.setDocumentType(documentType);
            index.setDocumentType(typeId, documentType);
        }

        Long fileId = document.getFileId();
        if (index.containsFile(fileId)) {
            document.setFileContent(index.getFile(fileId));
        } else {
            IFilesDAO filesDAO = getBean(IFilesDAO.class);
            IPlainFile fileContent = filesDAO.loadPlainFile(fileId);
            document.setFileContent(fileContent);
            index.setFile(fileId, fileContent);
        }

        Long nodoId = document.getNodoId();
        if (index.containsNode(nodoId)) {
            document.setNodo(index.getNode(nodoId));
        } else {
            INodeAccessDAO nodeDAO = getBean(INodeAccessDAO.class);
            IPlainNode nodo = nodeDAO.loadPlainNode(nodoId);
            document.setNodo(nodo);
            index.setNode(nodoId, nodo);
        }

        Long organizationalUnitId = document.getOrganizationalUnitId();
        if (index.containsOrganizationalUnit(organizationalUnitId)) {
            document.setOrganizationalUnit(index.getOrganizationalUnit(organizationalUnitId));
        } else {
            IOrganizationalUnitDAO orgDao = getBean(IOrganizationalUnitDAO.class);
            IPlainOrganizationalUnit organizationalUnit = orgDao.loadPlainOrganizationalUnit(organizationalUnitId);
            document.setOrganizationalUnit(organizationalUnit);
            index.setOrganizationalUnit(organizationalUnitId, organizationalUnit);
        }

        Long requestId = document.getRequestId();
        if (index.containsPlainRequest(requestId)) {
            document.setRequest(index.getPlainRequest(requestId));
        } else {
            IRequestDAO requestDao = getBean(IRequestDAO.class);
            IPlainRequest request = requestDao.loadPlainRequest(requestId);
            document.setRequest(request);
            index.setPlainRequest(requestId, request);
        }

        Long businessUnitDepartmentId = document.getBusinessUnitDepartmentId();
        if (index.containsBusinessUnitDepartment(businessUnitDepartmentId)) {
            document.setDepartment(index.getBusinessUnitDepartment(businessUnitDepartmentId));
        } else {
            IBusinessUnitDepartmentDAO depDao = getBean(IBusinessUnitDepartmentDAO.class);
            IPlainSearchBusinessUnitDepartment department = depDao.loadPlainBusinessUnitDepartment(businessUnitDepartmentId);
            document.setDepartment(department);
            index.setBusinessUnitDepartment(businessUnitDepartmentId, department);
        }

        Long surveyId = document.getSurveyId();
        if (index.containsSurvey(surveyId)) {
            document.setSurvey(index.getSurvey(surveyId));
        } else {
            ISurveysDAO surveyDAO = getBean(ISurveysDAO.class);
            IPlainSurvey survey = surveyDAO.loadPlainSurvey(surveyId);
            document.setSurvey(survey);
            index.setSurvey(surveyId, survey);
        }

        Long authorId = document.getAuthorId();
        if (index.containsUser(authorId)) {
            document.setAuthor(index.getUser(authorId));
        } else {
            IUserDAO userDAO = getBean(IUserDAO.class);
            IPlainUser author = userDAO.loadPlainUser(authorId);
            document.setAuthor(author);
            index.setUser(authorId, author);
        }

        Long originadorId = document.getOriginadorId();
        if (index.containsUser(originadorId)) {
            document.setAuthor(index.getUser(originadorId));
        } else {
            IUserDAO userDAO = getBean(IUserDAO.class);
            IPlainUser originador = userDAO.loadPlainUser(originadorId);
            document.setOriginador(originador);
            index.setUser(originadorId, originador);
        }

        return document;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IPlainDocument loadPlainDocument(Long documentId) {
        if (documentId == null || documentId <= 0) {
            return null;
        }
        return (IPlainDocument) HQL_findSimpleObject(" "
                + " SELECT new " + DocumentPlainDTO.class.getCanonicalName() + "("
                    + " c.id"
                    + ", c.organizationalUnitId"
                    + ", c.restrictRecordsByDepartment"
                    + ", c.validateAccessFormDepartment"
                    + ", c.cancelDate"
                    + ", c.creationDate"
                    + ", c.expirationDate"
                    + ", c.lastModificationDate"
                    + ", c.countPrintFormats"
                    + ", c.countWebhooks"
                    + ", c.creationType"
                    + ", c.deleted"
                    + ", c.enablePdfViewer"
                    + ", c.expiredRetention"
                    + ", c.isBackup"
                    + ", c.readers"
                    + ", c.retentionText"
                    + ", c.retentionTime"
                    + ", c.status"
                    + ", c.storagePlaceId"
                    + ", c.answersNodeId"
                    + ", c.authorId"
                    + ", c.businessUnitDepartmentId"
                    + ", c.businessUnitId"
                    + ", c.collectingAndStoreResponsible"
                    + ", c.disposition"
                    + ", c.fileId"
                    + ", c.flujoId"
                    + ", c.informationClassification"
                    + ", c.nodoId"
                    + ", c.originadorId"
                    + ", c.requestId"
                    + ", c.restrictRecordsObjId"
                    + ", c.surveyId"
                    + ", c.templateSurveyId"
                    + ", c.typeId"
                    + ", c.code"
                    + ", c.collectingAndStoreResponsibleDescription"
                    + ", c.description"
                    + ", c.dynamicTableName"
                    + ", c.masterId"
                    + ", c.rejectCode"
                    + ", c.restrictRecordsField"
                    + ", c.version"
                + " )"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", documentId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IPlainDocumentType loadPlainDocumentType(Long typeId) {
        if (typeId == null || typeId <= 0) {
            return null;
        }
        return (IPlainDocumentType) HQL_findSimpleObject(" "
                + " SELECT new " + DocumentTypePlainDTO.class.getCanonicalName() + "("
                    + " c.id"
                    + ", c.code"
                    + ", c.status"
                    + ", c.deleted"
                    + ", c.description"
                    + ", c.createdDate"
                    + ", c.lastModifiedDate"
                    + ", c.createdBy"
                    + ", c.lastModifiedBy"
                    + ", c.dictionaryIndexId"
                    + ", c.isRetainable"
                    + ", c.documentControlledType"
                    + ", c.registryCode"
                    + ", c.registryCodeSequenceLength"
                    + ", c.shareDocuments"
                    + ", c.shareMaxDownloads"
                    + ", c.shareMaxViews"
                    + ", c.shareLife"
                    + ", c.shareRestrictUserAgent"
                    + ", c.shareRestrictFileType"
                    + ", c.externalLink"
                    + ", c.externalMaxDownloads"
                    + ", c.externalLife"
                    + ", c.externalRestrictUserAgent"
                    + ", c.externalRestrictFileType"
                    + ", c.downloadFiles"
                    + ", c.creationType"
                    + ", c.mustRead"
                    + ", c.mustAssignReaders"
                    + ", c.addFindingsEnabled"
                    + ", c.addActivitiesEnabled"
                    + ", c.quickPrintEnabled"
                    + ", c.collectingAndStoreResponsible"
                    + ", c.informationClassification"
                    + ", c.disposition"
                    + ", c.documentExpiration"
                    + ", c.enablePdfViewer"
                    + ", c.pdfViewerFitDocumentScreen"
                    + ", c.readOnlyEnabled"
                    + ", c.canPrintControlledCopies"
                + " )"
                + " FROM " + DocumentType.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", typeId);
    }
}
