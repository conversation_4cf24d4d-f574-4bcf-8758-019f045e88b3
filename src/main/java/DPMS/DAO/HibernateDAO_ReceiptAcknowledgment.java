/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package DPMS.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IFilesDAO;
import DPMS.DAOInterface.IReceiptAcknowledgmentDAO;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentPrinting;
import DPMS.Mapping.Position;
import DPMS.Mapping.ReceiptAcknowledgment;
import DPMS.Mapping.User;
import Framework.Config.Language;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.exception.GenericSaveException;
import bnext.reference.document.DocumentPrintingRef;
import com.google.common.collect.ImmutableMap;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.document.dto.ReceiptAcknowledgmentDTO;
import qms.document.entity.ReceiptAcknowledgmentLite;
import qms.document.listeners.OnAddedPhysicalCopy;
import qms.document.listeners.OnCancelledPhysicalCopy;
import qms.document.listeners.OnCollectedCopies;
import qms.document.listeners.OnCollectedCopy;
import qms.document.listeners.OnDeliveredPhysicalCopy;
import qms.document.listeners.OnOmittedByInactiveJob;
import qms.document.listeners.OnOmittedByInactiveUser;
import qms.document.listeners.OnOmittedPhysicalCopy;
import qms.document.listeners.OnRequestedPickUpCopies;
import qms.util.BindUtil;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Garza Verastegui
 */
@Lazy
@Repository(value = "HibernateDAO_ReceiptAcknowledgment")
@Language(module = "DPMS.DAO.HibernateDAO_ReceiptAcknowledgment")
@Scope(value = "singleton")
public class HibernateDAO_ReceiptAcknowledgment extends GenericDAOImpl<ReceiptAcknowledgment, Long> implements IReceiptAcknowledgmentDAO {

    @Override
    @OnOmittedPhysicalCopy
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> omitPreviousPrintHistory(Long previousDocumentId, ILoggedUser loggedUser) {
        List<Long> reciepts = HQL_findByQuery(""
                + " SELECT rack.id"
                + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                + " WHERE rack.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue() + " "
                + " AND rack.documentId = :id", "id", previousDocumentId
        ); 
        HQL_updateByQuery(""
                + "UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                + "SET rack.status = " + ReceiptAcknowledgment.STATUS.OMITTED_BY_MODIFICATION.getValue() + " "
                + "WHERE rack.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue() + " "
                + "AND rack.documentId = :id", "id", previousDocumentId
        );
        return reciepts;
    }
    
    @Override
    @OnAddedPhysicalCopy
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> copyPrintHistory(Long previousDocumentId, Document newDocument, ILoggedUser loggedUser) throws QMSException {
        final  List<ReceiptAcknowledgmentDTO> receipts = HQL_findByQuery(""
            + " SELECT DISTINCT new " + ReceiptAcknowledgmentDTO.class.getCanonicalName() + "("
                    + " c.readerId,"
                    + " us.status,"
                    + " c.positionId,"
                    + " pos.status"
                + ") "
            + " FROM " 
                + ReceiptAcknowledgment.class.getCanonicalName() + " c "
                + "," + DocumentPrinting.class.getCanonicalName() + " dp "
            + " LEFT JOIN c.position pos"
            + " LEFT JOIN c.readerUser us"
            + " WHERE "
                + " c.documentId = :id"
                + " AND c.documentPrintingId = dp.id"
                + " AND dp.printingType = " + DocumentPrinting.printingType.CONTROLLED.getValue(),
            "id", previousDocumentId
        );
        if (receipts.isEmpty()) {
            return Utilities.EMPTY_LIST;
        }
        final ICodeSequenceDAO codeSeqDAO = getBean(ICodeSequenceDAO.class);
        final String codeHistoryPrefix = DocumentPrinting.PREFIX + "-" + Utilities.todayDateBy("yy");
        final String codeRecieptPrefix = ReceiptAcknowledgment.PREFIX + "-" + Utilities.todayDateBy("yy");
        final Date now = new Date();
        final DocumentPrinting dpHistory = new DocumentPrinting();
        dpHistory.setId(-1L);//Se pone aqui para que pueda ser nulo en recipt acknowledgement nulo
        dpHistory.setCode(codeHistoryPrefix + codeSeqDAO.next(CodeSequence.type.CODE_DOCUMENT_PRINTING));
        dpHistory.setStatus(DocumentPrinting.QUEUE);
        dpHistory.setCreatedBy(loggedUser.getId());
        dpHistory.setCreatedDate(now);
        dpHistory.setPrintBy(loggedUser.getId());
        dpHistory.setPrintDate(now);
        dpHistory.setPrintingType(DocumentPrinting.printingType.CONTROLLED.getValue());
        dpHistory.setPrintTimes(0);
        final DocumentPrintingRef lastPrint = HQLT_findSimple(DocumentPrintingRef.class, ""
            + " SELECT c.documentPrinting"
            + " FROM "  + ReceiptAcknowledgment.class.getCanonicalName() + " c "
            + " WHERE "
                + " c.documentId = :id"
                + " AND c.documentPrinting.printingType = " + DocumentPrinting.printingType.CONTROLLED.getValue(),
            "id", previousDocumentId
        );
        dpHistory.setPrintLayout(lastPrint.getPrintLayout());
        dpHistory.setPrintRotation(lastPrint.getPrintRotation());
        final DocumentPrinting savedDpHistory = makePersistent(dpHistory, loggedUser.getId());
        final Integer newDocumentFilePages = getBean(IFilesDAO.class).findNumberPages(newDocument.getFileId());
        final List<Long> results = receipts.stream()
            .map(receipt -> {
                try {
                final ReceiptAcknowledgment ent = new ReceiptAcknowledgment(-1L);
                if (isActiveReader(receipt)) {
                    ent.setStatus(ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue());
                } else { 
                    ent.setStatus(ReceiptAcknowledgment.STATUS.OMITTED_BY_INACTIVE.getValue());
                }
                ent.setDeleted(0);
                ent.setCreatedBy(loggedUser.getId());
                ent.setCreatedDate(now);
                ent.setLastModifiedBy(loggedUser.getId());
                ent.setLastModifiedDate(now);
                ent.setCode(codeRecieptPrefix + codeSeqDAO.next(CodeSequence.type.CODE_RECEIPT_ACK));
                ent.setDocumentId(newDocument.getId());
                ent.setDocumentCode(newDocument.getCode());
                ent.setDocumentVersion(newDocument.getVersion());
                ent.setFileId(newDocument.getFileId());
                ent.setFilePages(newDocumentFilePages);
                ent.setReaderId(receipt.getReaderId());
                ent.setPositionId(receipt.getPositionId());
                ent.setPositionAssignedDate(now);
                ent.setCreatedTimes(0);
                ent.setDocumentPrintingId(savedDpHistory.getId());
                final ReceiptAcknowledgment savedReciept = makePersistent(ent, loggedUser.getId());
                return savedReciept.getId();
                } catch(final QMSException ex) {
                    throw new RuntimeException(ex);
                }
            })
            .collect(Collectors.toList());
        return results;
    }

    private boolean isActiveReader(ReceiptAcknowledgmentDTO receipt) {
        final boolean isActiveUser = receipt.getPositionStatus() == null 
                || Position.STATUS.ACTIVE.getValue().equals(receipt.getPositionStatus());
        final boolean isActivePosition = receipt.getReaderStatus() == null 
                || User.STATUS.ACTIVE.getValue().equals(receipt.getReaderStatus());
        return isActiveUser && isActivePosition;
    }
    

    @Override
    @OnDeliveredPhysicalCopy
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long deliverPhysicalCopy(Long rackId, ILoggedUser loggedUser) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("printBy", loggedUser.getId());
        params.put("id", rackId);
        params.put("printDate", new Date());
        return HQL_updateByQuery(""
                + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                + " SET "
                    + " rack.status = " + ReceiptAcknowledgment.STATUS.DELIVERED.getValue() + ","
                    + " rack.createdTimes = 1, "
                    + " rack.printBy = :printBy,"
                    + " rack.printDate = :printDate "
                + " WHERE rack.id = :id"
                + " AND rack.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue(), 
                params
        ).longValue();
    }
    
    @Override
    @OnCancelledPhysicalCopy
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> cancelPrintCopies(Long documentId, ILoggedUser loggedUser) {
        List<Long> reciepts = HQL_findByQuery(""
                + " SELECT rack.id"
                + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                + " WHERE rack.documentId = :id AND rack.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue(), 
                "id", documentId
        ); 
        HQL_updateByQuery(""
                + "UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                + "SET rack.status = " + ReceiptAcknowledgment.STATUS.OMITTED_BY_CANCELLATION.getValue()
                + "WHERE rack.documentId = :id AND rack.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue(),
                "id", documentId
        );
        return reciepts;
    }
    
    @Override
    @OnRequestedPickUpCopies
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> requestPickUpCopies(final Long documentId, final ILoggedUser loggedUser) {
        final List<Long> copies = HQL_findByQuery(""
                + " SELECT rack.id"
                + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                + " WHERE rack.documentId = :id"
                + " AND rack.status = " + ReceiptAcknowledgment.STATUS.DELIVERED.getValue(),
                "id", documentId
        ); 
        HQL_updateByQuery(""
                + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                + " SET rack.status = " + ReceiptAcknowledgment.STATUS.TO_PICK_UP.getValue()
                + " WHERE rack.documentId = :id"
                + " AND rack.status = " + ReceiptAcknowledgment.STATUS.DELIVERED.getValue(), 
                "id", documentId
        );
        return copies;
    }

    @Override
    @OnCollectedCopies
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle pickUpPhysicalCopies(final List<ReceiptAcknowledgmentLite> copies, final ILoggedUser loggedUser) {
        for (final ReceiptAcknowledgmentLite copy : copies) {
            if (ReceiptAcknowledgment.STATUS.COLLECTED.getValue().equals(copy.getStatus())) {
                HQL_updateByQuery(""
                        + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                        + " SET "
                            + " rack.status = :status,"
                            + " rack.collectedBy = :userId,"
                            + " rack.collectedAt = current_date()" 
                        + " WHERE rack.id = :id",
                        ImmutableMap.of(
                                "status", copy.getStatus(),
                                "userId", loggedUser.getId(),
                                "id", copy.getId()
                        )
                );
            } else if (ReceiptAcknowledgment.STATUS.LOST.getValue().equals(copy.getStatus())) {
                HQL_updateByQuery(""
                        + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                        + " SET "
                            + " rack.status = :status,"
                            + " rack.reportedLostBy = :userId,"
                            + " rack.reportedLostAt = current_date()" 
                        + " WHERE rack.id = :id",
                        ImmutableMap.of(
                                "status", copy.getStatus(),
                                "userId", loggedUser.getId(),
                                "id", copy.getId()
                        )
                );
            } else {
                final GenericSaveHandle gsh = new GenericSaveHandle(
                        "Invalid status " + copy.getStatus() + "for ReceiptAcknowledgment with id " + copy.getId()
                );
                throw new GenericSaveException(gsh);
            }
        }
        return successGSH();
    }

    @Override
    @OnCollectedCopy
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle pickUpPhsyicalCopy(final ReceiptAcknowledgmentLite copy, final ILoggedUser loggedUser) {
        if (ReceiptAcknowledgment.STATUS.COLLECTED.getValue().equals(copy.getStatus())) {
            HQL_updateByQuery(""
                    + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                    + " SET "
                        + " rack.status = :status,"
                        + " rack.collectedBy = :userId,"
                        + " rack.collectedAt = current_date()" 
                    + " WHERE rack.id = :id",
                        ImmutableMap.of(
                                "status", copy.getStatus(),
                                "userId", loggedUser.getId(),
                                "id", copy.getId()
                        )
            );
        } else if (ReceiptAcknowledgment.STATUS.LOST.getValue().equals(copy.getStatus())) {
            HQL_updateByQuery(""
                    + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " rack "
                    + " SET "
                        + " rack.status = :status,"
                        + " rack.reportedLostBy = :userId,"
                        + " rack.reportedLostAt = current_date()" 
                    + " WHERE rack.id = :id",
                        ImmutableMap.of(
                                "status", copy.getStatus(),
                                "userId", loggedUser.getId(),
                                "id", copy.getId()
                        )
            );
        } else {
            final GenericSaveHandle gsh = new GenericSaveHandle(
                    "Invalid status " + copy.getStatus() + "for ReceiptAcknowledgment with id " + copy.getId()
            );
            throw new GenericSaveException(gsh);
        }
        return successGSH();
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getCopiesToDeliverCountByUser(Long userId) {
        if (userId == null || userId == -1L) {
            return 0l;
        }        
        final Integer status = ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue();
        return HQL_findLong(""
            + " SELECT COUNT(c.id)"
            + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c"
            + " JOIN c.readerUser us"
            + " JOIN c.document d"
            + " JOIN d.documentType dt"
            + " WHERE c.status = :status"
            + " AND dt.canPrintControlledCopies = 1 "
            + " AND us.id = :userId",
            ImmutableMap.of(
                "userId", userId,
                "status", status
            )
        );
    }
        
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getCopiesToDeliverCountByJob(Long jobId) {
        if (jobId == null || jobId == -1L) {
            return 0l;
        }        
        final Integer status = ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue();
        return HQL_findLong(""
            + " SELECT COUNT(c.id)"
            + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c"
            + " JOIN c.position pos"
            + " JOIN c.document d"
            + " JOIN d.documentType dt"
            + " WHERE c.status = :status"
            + " AND dt.canPrintControlledCopies = 1 "
            + " AND pos.id = :jobId", 
            ImmutableMap.of(
                    "jobId", jobId,
                    "status", status
            )
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getCopiesToDeliverByUser(final Long userId, final SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
                + " c.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue()
                + " AND dt.canPrintControlledCopies = 1 "
                + " AND us.id = " + userId);
        return HQL_getRows(""
            + " SELECT new map("
                + " c.id as id,"
                + " c.code as code,"
                + " doc.code as documentCode,"
                + " doc.description as documentDescription,"
                + " doc.version as documentVersion,"
                + " c.positionAssignedDate as positionAssignedDate"
            + " )"
            + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c"
            + " LEFT JOIN c.readerUser us"
            + " LEFT JOIN c.document doc"
            + " JOIN doc.documentType dt",
                filter
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getCopiesToDeliverByJob(final Long jobId, final SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
                + " c.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue()
                + " AND dt.canPrintControlledCopies = 1 "
                + " AND pos.id = " + jobId);
        return HQL_getRows(""
            + " SELECT new map("
                + " c.id as id,"
                + " c.code as code,"
                + " doc.code as documentCode,"
                + " doc.description as documentDescription,"
                + " doc.version as documentVersion,"
                + " c.positionAssignedDate as positionAssignedDate"
            + " )"
            + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c"
            + " LEFT JOIN c.position pos"
            + " LEFT JOIN c.document doc"
            + " JOIN doc.documentType dt",
            filter
        );
    }
    
    @Override
    @OnOmittedByInactiveUser
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> omitCopiesByInactiveUser(final Long userId, final ILoggedUser loggedUser) {
        final List<Long> copies = HQL_findByQuery(""
            + " SELECT c.id"
            + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c "
            + " WHERE c.readerId = :readerId"
            + " AND c.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue(),
            "readerId", userId
        );
        omitCopies(copies, loggedUser);
        return copies;
    }

    private void omitCopies(final List<Long> copies, final ILoggedUser loggedUser) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("lastModifiedDate", new Date());
        params.put("lastModifiedBy", loggedUser.getId());
        HQL_updateByQuery(""
                + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " c "
                + " SET"
                    + " c.status = " + ReceiptAcknowledgment.STATUS.OMITTED_BY_INACTIVE.getValue() + ","
                    + " c.lastModifiedDate = :lastModifiedDate,"
                    + " c.lastModifiedBy = :lastModifiedBy"
                + " WHERE " + BindUtil.parseFilterList("c.id", copies), params);
    }
    
    @Override
    @OnOmittedByInactiveJob
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> omitCopiesByInactiveJob(final Long jobId, final ILoggedUser loggedUser) {
        final List<Long> copies = HQL_findByQuery(""
            + " SELECT c.id"
            + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c "
            + " WHERE c.positionId = :jobId"
            + " AND c.status = " + ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue(),
            "jobId", jobId
        );
        omitCopies(copies, loggedUser);
        return copies;
    }
    

    private Long getCountByType(final Integer status, final Long documentTypeId) {
        final Long count = HQL_findLong(""
                + " SELECT COUNT(c.id)"
                + " FROM " + ReceiptAcknowledgment.class.getCanonicalName() + " c"
                + " JOIN c.document d"
                + " JOIN d.documentType dt"
                + " WHERE c.status = :status"
                + " AND dt.id = :id",
                ImmutableMap.of(
                        "id", documentTypeId,
                        "status", status
                )
        );
        if (count == null) {
            return 0l;
        }
        return count;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getCopiesToDeliverCountByType(Long documentTypeId) {
        if (documentTypeId == null || documentTypeId == -1L) {
            return 0l;
        }        
        final Integer status = ReceiptAcknowledgment.STATUS.NOT_DELIVERED.getValue();
        return getCountByType(status, documentTypeId);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getCopiesToPickUpCountByType(Long documentTypeId) {
        if (documentTypeId == null || documentTypeId == -1L) {
            return 0l;
        }        
        final Integer status = ReceiptAcknowledgment.STATUS.TO_PICK_UP.getValue();
        return getCountByType(status, documentTypeId);
    }    
    
    private Long cancelCopiesByType(
            final Long documentTypeId,
            final ReceiptAcknowledgment.STATUS currentStatus,
            final ReceiptAcknowledgment.STATUS newStatus,
            final ILoggedUser loggedUser
    ) {
        final String cancellationReason = getTag("cancellationReason").replace(":user", loggedUser.getDescription());
        return HQL_updateByQuery(""
                + " UPDATE " + ReceiptAcknowledgment.class.getCanonicalName() + " c "
                + " SET"
                    + " c.status = :newStatus,"
                    + " c.lastModifiedDate = :lastModifiedDate,"
                    + " c.cancellationReason = :cancellationReason,"
                    + " c.lastModifiedBy = :lastModifiedBy"
                + " WHERE c.status = :currentStatus"
                + " AND EXISTS ("
                        + " SELECT 1"
                        + " FROM " + Document.class.getCanonicalName() + " d"
                        + " JOIN d.documentType dt"
                        + " WHERE dt.id = :id"
                        + " AND d.id = c.documentId"
                + " )",
                ImmutableMap.of(
                        "id", documentTypeId,
                        "newStatus", newStatus.getValue(),
                        "currentStatus", currentStatus.getValue(),
                        "cancellationReason", cancellationReason,
                        "lastModifiedDate", new Date(),
                        "lastModifiedBy", loggedUser.getId()
                )
        ).longValue();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long cancelPickUpCopiesByType(final Long documentTypeId, final ILoggedUser loggedUser) {
        final Long count = cancelCopiesByType(
                documentTypeId, 
                ReceiptAcknowledgment.STATUS.TO_PICK_UP,
                ReceiptAcknowledgment.STATUS.PICK_UP_CANCELLED,
                loggedUser
        );
        return count;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})    
    public Long cancelDeliverCopiesByType(final Long documentTypeId, final ILoggedUser loggedUser) {
        final Long count = cancelCopiesByType(
                documentTypeId,
                ReceiptAcknowledgment.STATUS.NOT_DELIVERED,
                ReceiptAcknowledgment.STATUS.DELIVER_CANCELLED,
                loggedUser
        );
        return count;        
    }
}
