package DPMS.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.Mapping.Periodicity;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.util.QMSException;

/**
 * Creation date: 16/04/2014
 *
 * <AUTHOR> Cavazos Galindo
 */
@Lazy
@Repository(value = "HibernateDAO_Periodicity")
@Scope(value = "singleton")
public class HibernateDAO_Periodicity extends GenericDAOImpl<Periodicity, Long> implements IPeriodicityDAO {

    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE makePersistent(TYPE entity) {
        return makePersistent(entity, null);
    }
        
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public <TYPE> TYPE makePersistent(TYPE entity, Long loggedUserId) {
        if (entity instanceof Periodicity) {
            Periodicity ent = (Periodicity) entity;
            boolean nuevo = (ent.getId() == null || ent.getId() == -1);
            if (nuevo && ent.getCode() == null) {
                try {
                    ICodeSequenceDAO sq = (ICodeSequenceDAO) getBean("CodeSequence");
                    ent.setCode("FREQ-" + Utilities.todayDateBy("yy") + sq.next(Periodicity.class.getCanonicalName(), null, false));
                } catch (final QMSException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }
        return super.makePersistent(entity, loggedUserId);
    }    

    /**
     * Función que obtiene la siguiente coincidencia mensual basado en los
     * ordinales semanales (tercer lunes del mes) Este método puede ser invocado
     * por ajax
     *
     * @param d la recha de incio
     * @param months el numero de meses de separación
     * @return la fecha de la siguiente coincidencia mensual
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date getNextMonthlyOccurrence(Date d, Integer months) {
        Calendar c = Calendar.getInstance();
        c.setTime(d);
        c.setFirstDayOfWeek(Calendar.SUNDAY);
        /*arma la nueva fecha*/
        Calendar newC = Calendar.getInstance();
        newC.setTime(d);
        newC.add(Calendar.MONTH, months);
        newC.set(Calendar.WEEK_OF_MONTH, c.get(Calendar.WEEK_OF_MONTH));
        newC.set(Calendar.DAY_OF_WEEK, c.get(Calendar.DAY_OF_WEEK));
        return newC.getTime();
    }

    /**
     * Función que obtiene la primiera coincidencia de periodicidad
     *
     * @param startDate la fecha de inicio del cálculo
     * @param periodicityId el id de la peridicidad que utilizará para el cálculo
     * @return la fecha de la primera coincidencia
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date getFirstOccurence(Date startDate, Long periodicityId) {
        Periodicity p = HQLT_findById(periodicityId);
        Calendar c = Calendar.getInstance();
        c.setTime(startDate);
        if (p != null) {
            switch (p.getVchtipoperiodicidad()) {
                case Periodicity.DAILY:
                case Periodicity.MONTHLY:
                case Periodicity.YEARLY:
                case Periodicity.HYBRID:
                    return startDate;
                case Periodicity.WEEKLY:
                    return getFirstWeeklyOccurrence(c, p);
            }
        }
        return startDate;
    }

    /**
     * Función que obtiene la siguiente coincidencia de periodicidad
     *
     * @param startDate la fecha de inicio del cálculo
     * @param periodicityId el id de la peridicidad que utilizará para el
     * cálculo
     * @return la fecha de la siguiente coincidencia
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Date getNextOccurence(Date startDate, Long periodicityId) {
        Periodicity p = HQLT_findById(periodicityId);
        Calendar c = Calendar.getInstance();
        c.setTime(startDate);
        if (p != null) {
            if (Periodicity.DAILY.equals(p.getVchtipoperiodicidad())) {
                return getNextDailyOccurrence(c, p);
            }
            if (Periodicity.WEEKLY.equals(p.getVchtipoperiodicidad())) {
                return getNextWeeklyOccurrence(c, p);
            }
            if (Periodicity.MONTHLY.equals(p.getVchtipoperiodicidad())) {
                return getNextMonthlyOccurrence(c, p);
            }
            if (Periodicity.YEARLY.equals(p.getVchtipoperiodicidad())) {
                return getNextYearlyOccurrence(c, p);
            }
            if (Periodicity.HYBRID.equals(p.getVchtipoperiodicidad())) {
                return getNextHybridOccurrence(c, p);
            }
        }
        return startDate;
    }

    /**
     * Devuelve el numero de días desde el día "c", hasta el día siguiente dependiendo la configuración.
     * @param c Calendar con el dia del cual partir.
     * @param p Configuración de la periodicidad
     * @param includeToday Si true, revisa si la periodicidad debe considerar iniciar el mismo día dado por "c".
     * @return Número de días entre "c" y el siguiente día en la periodicidad.
     */
    public static Integer getWeeklyDaysDiff(Calendar c, Periodicity p, boolean includeToday) {
        Integer today = c.get(Calendar.DAY_OF_WEEK) - 1;
        Integer daysDiff = 7; // Diferencia maxima posible entre dos dias
        Integer daysToFinishWeek = 7 - today; // Dias para que acabe la semana
        // Dias a revisar, empezando por domingo. Si el dia se revisa se le asigna su numero de día.
        int[] daysToCheck = {
            p.getBooldomingo() == 1 ? 7 : -1, // Dado que Calendar.DAY_OF_WEEK regresa 7 para DOMINGO, se fija dicho valor.
            p.getBoollunes() == 1 ? 1 : -1,
            p.getBoolmartes() == 1 ? 2 : -1,
            p.getBoolmiercoles() == 1 ? 3 : -1,
            p.getBooljueves() == 1 ? 4 : -1,
            p.getBoolviernes() == 1 ? 5 : -1,
            p.getBoolsabado() == 1 ? 6 : -1
        };
        // i.- Aparte de iterar, representa el día que se revisa, se empiza DOMINGO = 0, por ser el último día al que llega la semana (NO confundir con su valor de Calendar).
        for (int i= 0; i < daysToCheck.length; i++) {
            if (daysToCheck[i] != -1) {
                if (today == daysToCheck[i] && includeToday) { // La siguiente recurrencia es el mismo dia.
                    return 0;
                }
                /*
                  daysTillDayToCheck: Dias de diferencia entre TODAY y el día "i" de la siguiente semana.
                           E.g: Si TODAY = JUEVES(4), faltan 3 (daysToFinishWeek) dias para que acabe la semana, es decir, hasta que llegue domingo.
                                Si el dia que se esta revisando es MARTES dado por i = 2, entonces, de JUEVES para MARTES faltan daysToFinishWeek + i = 3 + 2 = 5 días.
                                Si se revisa un dia que siga después del jueves, como VIERNES con i = 5, entonces, los días entre JUEVES y VIERNES de la sig. semana
                                sería daysToFinishWeek + i = 3 + 5 = 8 días.
                  daysTillDayToCheckAfterToday: Dias de diferencia entre TODAY y el día "i".
                           E.g: Si TODAY = JUEVES(4), y el dia que se esta revisando es MARTES dado por i = 2, como no se considera acabar la semana, entonces
                               los días entre JUEVES y MARTES serían i - TODAY = 2 - 4 = -2.
                               Si se revisa un dia que siga después del jueves, como VIERNES con i = 5, entonces, los días entre JUEVES y VIERNES serían i - TODAY = 5 - 4 = 1
                 */
                int daysTillDayToCheck = daysToFinishWeek + i;
                int daysTillDayToCheckAfterToday = (i - today);
                /*
                   Si TODAY es mayor o igual que el día "i" que se revisa:
                   Quiere decir que el siguiente día debe de estar contenido en la siguiente semana y por ende,
                   se usa el cálculo que considera los dias para terminar la semana.
                            E.g: Si TODAY = JUEVES(4), los días DOMINGO(0), LUNES(1), MARTES(2), MIERCOLES(3) y JUEVES(4), ya pasaron, por lo que, se deben de buscar en la sig. semana.
                   Si TODAY es menor que el día "i" que se revisa:
                   Quiere decir que el siguiente día está contenido en la semana actual y por ende,
                   se usa el cálculo daysTillDayToCheckAfterToday.
                            E.g: Si TODAY = JUEVES(4), los días VIERNES(5) y SABADO(6) aún no llegan, por lo que, se deben de buscar en la semana actual.
                   NOTA: daysTillDayToCheckAfterToday(i - today) es negativo  cuando TODAY > i, y como el siguiente condicional al evaluar today >= i regresa daysTillDayToCheck,
                   nunca se tomarán valores negativos.
                 */
                int diff = today >= i ? daysTillDayToCheck : daysTillDayToCheckAfterToday;
                // Actualiza la diferencia comparando cual es el minimo entre la diferencia en la iteración y la del método.
                daysDiff = Math.min(daysDiff, diff);
            }
        }
        return daysDiff;
    }
    
    
    /**
     * Función que obtiene la primera coincidencia semanal
     *
     * @param c la instancia de calendario que se utiliza
     * @param p la instancia de periodicidad que se utiliza
     * @return la fecha de la siguiente coincidencia semanal
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    private Date getFirstWeeklyOccurrence(Calendar c, Periodicity p) {
        getLogger().trace("getFirstWeeklyOccurrence");
        if (p.getVchtipoperiodicidad().equals(Periodicity.WEEKLY)) {
            Integer daysDiff = getWeeklyDaysDiff(c, p, true);
            if (daysDiff == 0 || daysDiff == 7) {
                return c.getTime();
            }
            c.add(Calendar.DATE, daysDiff);
            return c.getTime();
        } else {
            return c.getTime();
        }
    }
    
    /**
     * Función que obtiene la siguiente coincidencia semanal
     *
     * @param c la instancia de calendario que se utiliza
     * @param p la instancia de periodicidad que se utiliza
     * @return la fecha de la siguiente coincidencia semanal
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    private Date getNextWeeklyOccurrence(Calendar c, Periodicity p) {
        getLogger().trace("getNextWeeklyOccurrence");
        if (p.getVchtipoperiodicidad().equals(Periodicity.WEEKLY)) {
            Integer daysDiff = getWeeklyDaysDiff(c, p, false);
            if (daysDiff == 0) {
                return c.getTime();
            }
            Integer cweek = c.get(Calendar.WEEK_OF_YEAR);
            c.add(Calendar.DATE, daysDiff);
            Integer ndweek = c.get(Calendar.WEEK_OF_YEAR);
            if(!Objects.equals(cweek, ndweek)) {
                c.add(Calendar.DATE, (p.getWeeks() - 1) * 7);
            }
        } else {
            c.add(Calendar.DATE, (p.getWeeks()) * 7);
        }
        return c.getTime();
    }

    /**
     * Función que obtiene la siguiente coincidencia diaria
     *
     * @param c la instancia de calendario que se utiliza
     * @param p la instancia de periodicidad que se utiliza
     * @return la fecha de la siguiente coincidencia diaria
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    private Date getNextDailyOccurrence(Calendar c, Periodicity p) {
        getLogger().trace("getNextDailyOccurrence");
        c.add(Calendar.DATE, p.getDays());
        return c.getTime();
    }

    /**
     * Función que obtiene la siguiente coincidencia anual
     *
     * @param c la instancia de calendario que se utiliza
     * @param p la instancia de periodicidad que se utiliza
     * @return la fecha de la siguiente coincidencia anual
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    private Date getNextYearlyOccurrence(Calendar c, Periodicity p) {
        getLogger().trace("getNextYearlyOccurrence");
        c.add(Calendar.YEAR, p.getYears());
        return c.getTime();
    }

    /**
     * Función que obtiene la siguiente coincidencia híbrida
     *
     * @param c la instancia de calendario que se utiliza
     * @param p la instancia de periodicidad que se utiliza
     * @return la fecha de la siguiente coincidencia híbrida
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    private Date getNextHybridOccurrence(Calendar c, Periodicity p) {
        getLogger().trace("getNextHybridOccurrence");
        c.add(Calendar.YEAR, p.getYears());
        c.add(Calendar.MONTH, p.getMonths());
        c.add(Calendar.DATE, p.getWeeks() * 7);
        c.add(Calendar.DATE, p.getDays());
        return c.getTime();
    }
    
    /**
     * Función que obtiene la siguiente coincidencia mensual
     *
     * @param c la instancia de calendario que se utiliza
     * @param p la instancia de periodicidad que se utiliza
     * @return la fecha de la siguiente coincidencia mensual
     * <AUTHOR> Germán Lares Lares
     * @since ********
     */
    private Date getNextMonthlyOccurrence(Calendar c, Periodicity p) {
        getLogger().trace("getNextMonthlyOccurrence");
        if (p.getVchopcion().equals(Periodicity.BY_DAY_OF_MONTH)) {
            c.add(Calendar.MONTH, p.getMonths());
            return c.getTime();
        } else {
            return getNextMonthlyOccurrence(c.getTime(), p.getMonths());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isPeriodic(final Class<? extends IPeriodicEntity> entityClass, final Long entityId) {
        final Boolean isPeriodic = HQL_findLong(""
            + " SELECT count(*)"
            + " FROM " 
                + entityClass.getCanonicalName() + " c"
                + " CROSS JOIN " + entityClass.getCanonicalName() + " p"
            + " WHERE "
                + " c.id = " + entityId
                + " AND c.recurrenceId = p.id"
                + " AND p.periodicity.vchtipoperiodicidad <> '" + Periodicity.NEVER + "'"
        ) > 0;
        return isPeriodic;
    }
}
