package DPMS.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IUserRefDAO;
import DPMS.Mapping.Document;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.form.dto.FavoriteTaskDto;
import qms.form.dto.FavoriteTaskSaveDTO;
import qms.form.entity.FavoriteTask;
import qms.form.entity.IFavoriteTask;
import qms.form.entity.UserFavoriteTask;
import qms.framework.util.CacheRegion;
import qms.task.dto.FavoriteMenuItem;
import qms.task.dto.IQualifiedMenuItem;
import qms.task.enums.FavoriteTaskType;
import qms.task.util.QualifiedMenuItemUtil;
import qms.util.GridFilter;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Germán Lares Lares
 */
@Lazy
@Repository(value = "HibernateDAO_UserRef")
@Scope(value = "singleton")
public class HibernateDAO_UserRef  extends GenericDAOImpl<UserRef, Long> implements IUserRefDAO {

    private boolean isParemeterIncomplete(Object value, String urlParams, String parameterName) {
        if (
            value != null
            && (
                urlParams == null
                || !urlParams.contains(parameterName)
            )
        ) {
            getLogger().error("Missing " + parameterName + " urlParam, the parameter is needed as KEY for future searchs");
            return true;
        }
        return false;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public FavoriteTaskSaveDTO addMenuPath(final FavoriteMenuItem favorite, final Long loggedUserId) throws QMSException {
        final String menuPath = favorite.getMenuPath();
        final String description = favorite.getName();
        final String icon = favorite.getIcon();
        final String urlParams = favorite.getUrlParams();
        final Boolean isSystemGenerated = favorite.getIsSystemGenerated();
        final String docMasterId = favorite.getDocumentMasterId();
        final FavoriteTaskSaveDTO saveDto = new FavoriteTaskSaveDTO();
        final String taskName = favorite.getTaskName();
        if (
            this.isParemeterIncomplete(favorite.getTemplateActivityTemplateId(), urlParams, "templateActivityTemplateId")
            || (!"FORMULARIE".equals(favorite.getModuleName()) && this.isParemeterIncomplete(docMasterId, urlParams, "documentMasterId"))
        ) {
            saveDto.setSuccess(false);
            return saveDto;
        }
        final Map<String, Object> params = new HashMap<>();
        params.put("menuPath", menuPath);
        params.put("urlParams", urlParams);
        params.put("description", description);
        params.put("isSystemGenerated", isSystemGenerated);
        params.put("taskName", taskName);
        String taskQuery = ""
            + " SELECT t "
            + " FROM " + FavoriteTask.class.getCanonicalName() + " t "
            + " WHERE "
                + " t.menuPath = :menuPath"
                + " AND t.urlParams = :urlParams"
                + " AND t.description = :description"
                + " AND t.isSystemGenerated = :isSystemGenerated"
                + " AND t.taskName = :taskName";
        if (docMasterId != null) {
            taskQuery += " AND t.documentMasterId = :docMasterId";
            params.put("docMasterId", docMasterId);
        }
        FavoriteTask task = HQLT_findSimple(FavoriteTask.class, taskQuery, params, true, CacheRegion.FAVORITE, 0);
        if (task == null) {
            ICodeSequenceDAO sq = Utilities.getBean(ICodeSequenceDAO.class);
            task = new FavoriteTask(-1L);
            task.setCode(
                "FT-" + Utilities.todayDateBy("yy") + sq.next(task)
            );
            task.setDescription(description);
            task.setStatus(FavoriteTask.ACTIVE_STATUS);
            task.setDeleted(FavoriteTask.IS_NOT_DELETED);
            task.setTemplateActivityTemplateId(favorite.getTemplateActivityTemplateId());
            task.setDocumentMasterId(docMasterId);
            task.setLastModifiedDate(new Date());
            task.setCreatedDate(new Date());
            task.setCreatedBy(loggedUserId);
            task.setLastModifiedBy(loggedUserId);
            task.setType(favorite.getType());
            task.setIsSystemGenerated(favorite.getIsSystemGenerated());
            task.setMenuPath(menuPath);
            task.setUrlParams(urlParams);
            task.setParams(favorite.getParams());
            task.setIcon(icon);
            task.setTaskName(favorite.getTaskName());
            task.setReportId(favorite.getReportId());
            if (favorite.getModuleName() != null || !favorite.getModuleName().equals("NONE")) {
                task.setModuleName(favorite.getModuleName());
            }
            if (
                makePersistent(task, loggedUserId) == null
            ) {
                saveDto.setSuccess(false);
                return saveDto;
            }
        } else if (
            HQL_findSimpleInteger(""
                + " SELECT count(*) "
                + " FROM " + UserFavoriteTask.class.getCanonicalName() + " ut"
                + " WHERE"
                    + " ut.id.favoriteTaskId = :favoriteTaskId"
                    + " AND ut.id.userId = :userId",
                ImmutableMap.of(
                        "favoriteTaskId", task.getId(),
                        "userId", loggedUserId
                ),
                true,
                CacheRegion.FAVORITE,
                0
            ) > 0
        ) { 
            saveDto.setId(task.getId());
            saveDto.setSuccess(true);
            return saveDto;
        }
        saveLinkedItems(
                UserFavoriteTask.class,
                loggedUserId, 
                Arrays.asList(task.getId()), 
                false,
                null, 
                true,
                CacheRegion.FAVORITE, 
                0, 
                loggedUserId
        );
        updateLinkAuditable(task.getId(), loggedUserId);
        saveDto.setId(task.getId());
        saveDto.setSuccess(true);
        return saveDto;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> myLinks(GridFilter filter, Long loggedUserId) {
        if (filter == null) {
            filter = new GridFilter();
        }
        filter.getField().setOrderBy("id");
        filter.setDirection((byte) 2);
        filter.getCriteria().put("<condition>", ""
                + " u.id = " + loggedUserId
                + " AND c.isSystemGenerated = false "
                + " AND c.status IN ("
                                + Document.ACTIVE_STATUS
                                + "," + Document.IN_EDITION_STATUS
                + " ) "
                + " AND c.type IN ("
                    + FavoriteTaskType.FORM.getValue()
                    +  "," + FavoriteTaskType.MENU_PATH.getValue()
                    +  "," + FavoriteTaskType.ACTIVITY.getValue()
                    +  "," + FavoriteTaskType.EXTERNAL_LINK.getValue()
                    +  "," + FavoriteTaskType.ACTIVITY_TEMPLATE.getValue()
                    +  "," + FavoriteTaskType.REPORT.getValue()
                    +  "," + FavoriteTaskType.USER_GROUP.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_REPORT_BD.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_QUERY_BD.getValue()
                    +  "," + FavoriteTaskType.ACTIVITY_PLANNED.getValue()
                    +  "," + FavoriteTaskType.USER_GROUP_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_REPORT_BD_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_QUERY_BD_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_RESOLUTION_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_CONNECTIONS_REPORTS.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_PRIORITIES_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_SOURCES_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_OBJETIVE_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_CACHE_BD_CONTROL.getValue()
                    +  "," + FavoriteTaskType.ACTIVITIES_TYPES_CONTROL.getValue()
                    +  "," + FavoriteTaskType.DYNAMIC_FIELD_CONTROL.getValue()
                + " ) "
                + " AND c.deleted = 0"
        );
        return HQL_getRows(new StringBuilder(""
            + " SELECT "
                + " new Map("
                    + "c.id AS id"
                    + ",c.status AS status"
                    + ",c.deleted AS deleted"
                    + ",c.type AS type"
                    + ",c.menuPath AS menuPath"
                    + ",c.code AS code"
                    + ",c.description AS description"
                    + ",c.urlParams AS urlPArams"
                    + ",c.href AS href"
                    + ",c.icon AS icon"
                    + ",c.documentMasterId AS documentMasterId"
                    + ",c.isSystemGenerated AS isSystemGenerated"
                    + ",c.createdDate AS createdDate"
                    + ",c.lastModifiedDate AS lastModifiedDate"
                    + ",c.taskName AS taskName"
                + " )"
            + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
            + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                + " ON uf.id.favoriteTaskId = c.id"
            + " JOIN " + UserRef.class.getCanonicalName() + " u "
                + " ON uf.id.userId = u.id"
        ), filter, true, CacheRegion.FAVORITE, 0);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> deleteMyLinks(List<Long> favoriteTaskIds, Long loggedUserId) {
        final List<Long> deletedIds = new ArrayList<>(favoriteTaskIds.size());
        favoriteTaskIds.forEach(id -> {
            final Integer result = deleteLink(id, loggedUserId);
            if (result > 0) {
                deletedIds.add(id);
            }
        });
        if (!deletedIds.isEmpty()) {
            return deletedIds;
        }
        return null;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> updateMyLinks(List<FavoriteTaskDto> links, Long loggedUserId) {
        final List<Long> results = new ArrayList<>(links.size());
        links.forEach(link -> {
            try {
                final Integer result = deleteLink(link.getId(), loggedUserId);
                if (result > 0) {
                    final FavoriteMenuItem item = getLink(link.getId());
                    item.setName(link.getDescription());
                    addMenuPath(item, loggedUserId);
                    results.add(link.getId());
                }
            } catch (final QMSException ex) {
                throw new RuntimeException(ex);
            }
        });
        if (!results.isEmpty()) {
            return results;
        }
        return null;

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<IQualifiedMenuItem> getExternalLinks(final ILoggedUser loggedUser) throws AuthenticationException {
        List<IQualifiedMenuItem> result = new ArrayList<>();
        List<IFavoriteTask> favorites = HQL_findByQuery(""
            + " SELECT "
                + " new " + FavoriteTaskDto.class.getCanonicalName() + "("
                    + "c.id"
                    + ",c.status"
                    + ",c.deleted"
                    + ",c.type"
                    + ",c.menuPath"
                    + ",c.code"
                    + ",c.description"
                    + ",c.urlParams"
                    + ",c.href"
                    + ",c.icon"
                    + ",c.documentMasterId"
                    + ",c.isSystemGenerated"
                    + ",c.moduleName"
                    + ",c.createdBy"
                    + ",c.lastModifiedBy"
                    + ",c.createdDate"
                    + ",c.lastModifiedDate"
                + " )"
            + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
            + " JOIN " + UserFavoriteTask.class.getCanonicalName() + " uf "
                + " ON uf.id.favoriteTaskId = c.id"
            + " JOIN " + UserRef.class.getCanonicalName() + " u "
                + " ON uf.id.userId = u.id"
            + " WHERE"
                + " u.id = :loggedUserId"
                + " AND c.status = 1"
                + " AND c.isSystemGenerated = false"
                + " AND c.type IN ("
                    + FavoriteTaskType.MENU_PATH.getValue()
                    +  "," + FavoriteTaskType.EXTERNAL_LINK.getValue()
                + " ) "
                + " AND c.deleted = 0",
            ImmutableMap.of("loggedUserId", loggedUser.getId()),
            true,
            CacheRegion.FAVORITE,
            0
        );
        String account = loggedUser.getAccount();
        favorites.forEach((fav) -> {
            IQualifiedMenuItem task = null;
            try {
                task = QualifiedMenuItemUtil.fromUserFavoriteTask(account, fav, this);
            } catch (UnsupportedEncodingException ex) {
                getLogger().error("Invalid userFavoriteTask, account ''", account, ex);
            }
            if (task != null) {
                result.add(task);
            }
        });
        return result;
    }

    private Integer updateLinkAuditable(final Long id, final Long userId) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("favoriteTaskId", id);
        params.put("userId", userId);
        return HQL_updateByQuery(""
           + " UPDATE " + FavoriteTask.class.getCanonicalName() + " c"
           + " SET "
                + " c.lastModifiedBy = :userId,"
                + " c.lastModifiedDate = getdate()"
           + " WHERE c.id = :favoriteTaskId",
           params,
           true,
           CacheRegion.FAVORITE,
           0
       );
    }

    private Integer deleteLink(final Long favoriteTaskId, final Long userId) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("favoriteTaskId", favoriteTaskId);
        params.put("userId", userId);
        final Integer result = HQL_updateByQuery(""
                + " DELETE FROM " + UserFavoriteTask.class.getCanonicalName() + " c"
                + " WHERE c.id.favoriteTaskId = :favoriteTaskId"
                + " AND c.id.userId = :userId",
                params,
                true,
                CacheRegion.FAVORITE,
                0
        );
        if (result  > 0) {
            updateLinkAuditable(favoriteTaskId, userId);
        }
        return result;
    }

    private FavoriteMenuItem getLink(final Long linkId) {
        return (FavoriteMenuItem) HQL_findSimpleObject(""
                + " SELECT new " + FavoriteMenuItem.class.getCanonicalName() + " ("
                   + "c.type,"
                   + "c.icon,"
                   + "c.description,"
                   + "c.urlParams,"
                   + "c.menuPath,"
                   + "c.isSystemGenerated,"
                   + "c.moduleName,"
                   + "c.documentMasterId,"
                   + "c.reportId"
                + ")"
            + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
            + " WHERE c.id = :id",
            ImmutableMap.of("id", linkId),
            true,
            CacheRegion.FAVORITE,
            0
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void cancelFavoritesByMasterId(String documentMasterId, ILoggedUser loggedUser) {
        final List<Long> favoriteIds  = HQL_findByQuery(" "
                        + " SELECT c.id "
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.deleted = 0 "
                        + " AND c.documentMasterId = :documentMasterId "
                        + " AND c.isSystemGenerated = false "
                ,
                ImmutableMap.of(
                        "documentMasterId", documentMasterId,
                        "loggedUserId", loggedUser.getId()
                ),
                true,
                CacheRegion.FAVORITE,
                0
        );
        if (favoriteIds == null || favoriteIds.isEmpty()) {
            return;
        }
        cancelFavoritesById(favoriteIds, loggedUser);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void cancelFavoritesByReportId(Long reportId, ILoggedUser loggedUser) {
        final List<Long> favoriteIds  = HQL_findByQuery(" "
                        + " SELECT c.id "
                        + " FROM " + FavoriteTask.class.getCanonicalName() + " c "
                        + " WHERE "
                        + " c.deleted = 0 "
                        + " AND c.reportId = :reportId "
                        + " AND c.isSystemGenerated = false "
                ,
                ImmutableMap.of(
                        "reportId", reportId,
                        "loggedUserId", loggedUser.getId()
                ),
                true,
                CacheRegion.FAVORITE,
                0
        );
        if (favoriteIds == null || favoriteIds.isEmpty()) {
            return;
        }
        cancelFavoritesById(favoriteIds, loggedUser);
    }

    private void cancelFavoritesById(List<Long> favoriteIds, ILoggedUser loggedUser) {
        if (favoriteIds == null || favoriteIds.isEmpty()) {
            return;
        }
        favoriteIds.forEach(favoriteId -> cancelFavoriteById(favoriteId, loggedUser));
    }

    private void cancelFavoriteById(Long favoriteId, ILoggedUser loggedUser) {
        final Integer result = HQL_updateByQuery(" "
                        + " UPDATE " + FavoriteTask.class.getCanonicalName() + " c "
                        + " SET "
                        + " c.deleted = 1 "
                        + " ,lastModifiedDate = CURRENT_DATE() "
                        + " ,lastModifiedBy = :loggedUserId "
                        + " WHERE c.id = :id",
                ImmutableMap.of(
                        "id", favoriteId,
                        "loggedUserId", loggedUser.getId()
                ),
                true,
                CacheRegion.FAVORITE,
                0
        );
        getLogger().trace("FavoriteTask {} canceled {}", favoriteId, result);
    }


}

