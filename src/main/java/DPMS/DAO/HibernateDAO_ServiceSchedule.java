package DPMS.DAO;

import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.DAOInterface.IServiceScheduleDAO;
import DPMS.Mapping.Measurement;
import DPMS.Mapping.Periodicity;
import DPMS.Mapping.SchedulingApproval;
import DPMS.Mapping.ServiceSchedule;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import java.util.HashMap;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.device.listeners.OnEditedSchedule;
import qms.device.listeners.OnRequestedScheduleApproval;
import qms.device.listeners.OnScheduledService;
import qms.util.interfaces.IGridFilter;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Lazy
@Repository(value = "HibernateDAO_ServiceSchedule")
@Scope(value = "singleton")
public class HibernateDAO_ServiceSchedule extends GenericDAOImpl<ServiceSchedule, Long> implements IServiceScheduleDAO {

    private static final String validEntitiesFilter = ""
            + "exists ("
            + "SELECT dept.id FROM "
            + "DPMS.Mapping.User as u "
            + "JOIN u.puestos as p "
            + "JOIN p.departamentos as dept "
            + "JOIN p.perfil as perfil "
            + "WHERE dept.businessUnitId = c.device.department.businessUnitId "
            + "AND u.id = :userId AND 1 IN (:servicios)"
            + ")";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        filter.getCriteria().put("<filtered-entity>", isAdmin ? ""
                : validEntitiesFilter.replace(":userId", userId).replace(":servicios", ProfileServices.getCodedServices("perfil", servicio))
        );
    }

    @Override
    @OnRequestedScheduleApproval
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle requestScheduleApproval(final ServiceSchedule ent, final LoggedUser user) {
        if (ent.getStatus().equals(ServiceSchedule.UNSCHEDULED)) {
            ent.setResponsibleId(user.getId());
        }
        return update(ent, user);
    }

    @Override
    @OnScheduledService
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle scheduleService(final ServiceSchedule ent, final LoggedUser user) {
        if (ent.getStatus().equals(ServiceSchedule.UNSCHEDULED)) {
            ent.setResponsibleId(user.getId());
        }
        return update(ent, user);
    }

    @Override
    @OnEditedSchedule
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle editSchedule(final ServiceSchedule ent, final LoggedUser user) {
        if (ent.getStatus().equals(ServiceSchedule.UNSCHEDULED)) {
            ent.setResponsibleId(user.getId());
        }
        return update(ent, user);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle update(ServiceSchedule ent, final LoggedUser user) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        boolean generate = false;
        if ("".equals(ent.getCode())) {
            generate = true;
            ent.setCode("#");
        }
        if (getLogger().isDebugEnabled()) {
            getLogger().debug("update: {}", Utilities.getSerializedObj(ent));
        }
        for (Measurement m : ent.getMeasurements()) {
            makePersistent(m, user.getId());
        }
        boolean nuevo = (ent.getId() == null || ent.getId() == -1);
        gsh.setOperationEstatus(0);
        Periodicity p = ent.getPeriodicity();
        
        if (getBean(IPeriodicityDAO.class).makePersistent(p, user.getId()) != null) {
            ent = makePersistent(ent, user.getId());
            if (generate) {
                ent.setCode(generateCode(ServiceSchedule.PREFIX, ent.getId()));
                gsh.setExtraJson("{\"code\":\"" + ent.getCode() + "\"}");
                ent = makePersistent(ent, user.getId(), false);
            }
            if (ent != null) {
                gsh.setOperationEstatus(1);
                gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
                gsh.setSavedId(ent.getId().toString());
                if (Utilities.getSettings().getSchedulingToApprove().equals(1)) {
                    /*Create Schedule Approval Request*/
                    SchedulingApproval approval = new SchedulingApproval();
                    approval.setId(-1L);
                    approval.setScheduleId(ent.getId());
                    approval.setStatus(SchedulingApproval.NEWLY_CREATED);
                    makePersistent(approval, user.getId());
                }
            }
        }
        return gsh;
    }

    /**
     * Implementacion del metodo para obtener el deviceId dado un scheduleId
     *
     * @param serviceScheduleId Id de la programacion del servicio relacionado
     * con el equipo
     * @return deviceId Id del equipo
     * <AUTHOR> Guadalupe Alemán Reyes
     * @since *******
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getDeviceIdByServiceScheduleId(Long serviceScheduleId) {
        Map params = new HashMap();
        params.put("serviceScheduleId", serviceScheduleId);
        Long deviceId = HQL_findSimpleLong(""
                + " SELECT c.device.id "
                + " FROM " + ServiceSchedule.class.getCanonicalName() + " c "
                + " WHERE c.id = :serviceScheduleId ", params);
        return deviceId;
    }
}
