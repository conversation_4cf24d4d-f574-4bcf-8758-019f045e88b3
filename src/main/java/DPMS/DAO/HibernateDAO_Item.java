package DPMS.DAO;

import DPMS.DAOInterface.IItemDAO;
import DPMS.Mapping.Item;
import DPMS.Mapping.ItemMapZone;
import DPMS.Mapping.SectionMapZone;
import Framework.Config.ITextHasValue;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "HibernateDAO_Item")
@Scope(value = "singleton")
public class HibernateDAO_Item extends GenericDAOImpl<Item, Long> implements IItemDAO {

    private static final String validEntitiesFilterByBusinessUnitDepartment = ""
            + " exists ( "
            + " SELECT dept.id FROM "
            + " DPMS.Mapping.User as u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.id = c.section.area.department.id "
            + " AND u.id = :userId "
            + " AND 1 IN (:servicios)"
            + ")";

    private static final String validEntitiesFilterByBusinessUnit = ""
            + " exists ( "
            + " SELECT dept.id FROM "
            + " DPMS.Mapping.User as u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.businessUnitId = c.section.area.department.businessUnitId "
            + " AND u.id = :userId "
            + " AND 1 IN (:servicios)"
            + ")";
  
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(SortedPagedFilter filter, Long userId, ProfileServices[] servicio, Boolean isAdmin, Boolean isManager) {
        filter.getCriteria().put("<filtered-entity>", (isAdmin ? "" : isManager ? validEntitiesFilterByBusinessUnit 
                : validEntitiesFilterByBusinessUnitDepartment).replace(":userId", userId.toString())
                .replace(":servicios", ProfileServices.getCodedServices("perfil", servicio))
        );
    }
    
    /**
     * 
     * @param entity
     * @return 
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public GenericSaveHandle save(final Item entity) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        Item ent = entity; 
        boolean nuevo = (ent.getId() == -1);
        boolean generateCode = false;
        if (ent.getCode().isEmpty()) {
            generateCode = true;
            ent.setCode("#");
        }
        if (ent.getParentId().equals(0L)) {
            ent.setParentId(null);
        }

        ent = makePersistent(ent);
        if (ent != null) {
            if (generateCode) {
                ent.setCode(ent.PREFIX + Framework.Config.Utilities.formatConsecutivo(ent.getId()));
                ent = makePersistent(ent);
            }
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
            if(!nuevo){
                IUntypedDAO dao = Utilities.getUntypedDAO();
                Map param =  new HashMap();
                param.put("name", ent.getDescription());
                param.put("id", ent.getId());
                dao.HQL_updateByQuery(""
                        + " UPDATE " + SectionMapZone.class.getCanonicalName() + ""
                        + " SET name = :name "
                        + " WHERE sectionId = :id",param);
                dao.HQL_updateByQuery(""
                        + " UPDATE " + ItemMapZone.class.getCanonicalName() + ""
                        + " SET name = :name "
                        + " WHERE sectionId = :id",param);
            }
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives(Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        return getActives(userId, isAdmin, false, null, servicio);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives(Long userId, Boolean isAdmin, Boolean isManager, Long itemId, ProfileServices[] servicio) {
        String entityFilter = (isAdmin ? "1=1" : isManager ? validEntitiesFilterByBusinessUnit : validEntitiesFilterByBusinessUnitDepartment)
                .replace(":userId", userId.toString())
                .replace(":servicios", ProfileServices.getCodedServices("perfil", servicio));
        String query = ""
                + " SELECT"
                + " new " + Framework.Config.TextHasValue.class.getCanonicalName() + "(concat(c.description, ' - ',c.section.description), c.id) FROM"
                + " " + Item.class.getCanonicalName() + " c "
                + " WHERE " + entityFilter
                + " AND (c.status = " + Item.ACTIVE_STATUS
                + " OR " + (itemId != null ? "c.id = " + itemId : "1=0") + ")";
        return this.HQL_findByQuery(query);
    }
}
