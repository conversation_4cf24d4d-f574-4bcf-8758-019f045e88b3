package DPMS.DAO;

import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IReceiptAcknowledgmentDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Area;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.DepartmentProcessLoad;
import DPMS.Mapping.Document;
import DPMS.Mapping.IUser;
import DPMS.Mapping.IUserLdap;
import DPMS.Mapping.IndicadorCalificacion;
import DPMS.Mapping.NodeAccess;
import DPMS.Mapping.PollRespondent;
import DPMS.Mapping.Position;
import DPMS.Mapping.Profile;
import DPMS.Mapping.Request;
import DPMS.Mapping.Settings;
import DPMS.Mapping.User;
import DPMS.Mapping.UserAttempts;
import DPMS.Mapping.UserPosition;
import Framework.Config.ITextHasValue;
import Framework.Config.Language;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextHasValue;
import Framework.Config.UserHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import ape.pending.core.IPendingQueries;
import ape.pending.entities.PendingCount;
import ape.pending.util.ApeConstants;
import ape.pending.util.ApeUtil;
import bnext.dto.LocaleDTO;
import bnext.licensing.LicenseUtil;
import bnext.login.dto.ServicesDTO;
import bnext.login.logic.RenewDataStatus;
import bnext.login.util.LoginError;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import isoblock.common.Properties;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import mx.bnext.access.Module;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.access.dto.TeamUserSaveDTO;
import qms.access.dto.UserCountPendingDTO;
import qms.access.dto.UserEditionInfoDTO;
import qms.access.dto.UserPlainDTO;
import qms.access.interfaces.IPlainUser;
import qms.access.util.ProfileServicesUtil;
import qms.activity.pending.ActivityPending;
import qms.configuration.dto.GridStateDTO;
import qms.configuration.entity.GridStateByUser;
import qms.configuration.listeners.OnRegisterLdapUser;
import qms.configuration.listeners.OnRegisterUser;
import qms.configuration.listeners.OnSaveUser;
import qms.configuration.util.GridStateUserHelper;
import qms.document.entity.DocumentReader;
import qms.document.pending.imp.ToAuthorizeRequest;
import qms.document.pending.imp.ToFillForm;
import qms.document.pending.imp.ToRenew;
import qms.escalation.dto.UserEscalableDTO;
import qms.form.entity.FavoriteTask;
import qms.form.entity.UserFavoriteTask;
import qms.form.pending.FormPending;
import qms.framework.core.PasswordEncoder;
import qms.framework.dao.ILicenseUserDAO;
import qms.framework.dto.ElapsedDataDTO;
import qms.framework.dto.UserCertificateDTO;
import qms.framework.entity.Owner;
import qms.framework.entity.OwnerTeam;
import qms.framework.entity.OwnerTeamUser;
import qms.framework.enums.UpdateBossStatus;
import qms.framework.listeners.OnBossChanged;
import qms.framework.listeners.OnJobAdded;
import qms.framework.listeners.OnJobRemoved;
import qms.framework.rest.SecurityRootUtils;
import qms.framework.rest.UserUtils;
import qms.framework.security.UserLogin;
import qms.framework.util.CacheRegion;
import qms.framework.util.MeasureTime;
import qms.framework.util.UserCreationSource;
import qms.survey.dto.BusinessUnitDepartmentDTO;
import qms.util.BindUtil;
import qms.util.FormUtil;
import qms.util.GridFilter;
import qms.util.UserUtil;
import qms.util.dto.FormApproverAnalystByDepartment;
import qms.util.dto.FormApproverUserInfo;
import qms.util.interfaces.IGridFilter;
import qms.workflow.util.WorkflowPreviewGenerator;
import qms.workflow.util.WorkflowSupported;

@Lazy
@Repository(value = "HibernateDAO_User") 
@Scope(value = "singleton")
@Language(module = "DPMS.DAO.HibernateDAO_User")
public class HibernateDAO_User extends GenericDAOImpl<User, Long> implements IUserDAO {
    
     static Pattern PASSWORD_VALIDATE_PATTERN = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=(.*[0-9]){2})(?=.*[<>(){}\\[\\]\\\\.,;:=%/_\\-\\'@\"*#\\$!&\\*?^]).{8,}$");

    private static final String USER_VERSION_BY_LOGIN = ""
            + " SELECT usr.version"
            + " FROM " + User.class.getCanonicalName() + " usr "
            + " WHERE usr.status IN (" 
                + User.STATUS.ACTIVE.getValue() + ","
                + User.STATUS.LOCKED.getValue()
            + " )"
            + " AND usr.login = :login";
        
    private static final String USER_EDITION_INFO_BY_FILTER = ""
                + " SELECT new " + UserEditionInfoDTO.class.getCanonicalName() + "("
                    + " c.id AS id"
                    + ", c.status AS status"
                    + ", c.code AS code"
                    + ", c.description AS name"
                    + ", c.correo AS email"
                    + ", c.cuenta AS account"
                    + ", b.description AS boss"
                    + ", d.description AS department"
                    + ", string_agg(p.description) AS jobs"
                    + ", string_agg(bu.id) AS businessUnitIds"
                    + ", string_agg(bu.description) AS businessUnits"
                    + ", string_agg(org.id) AS organizationUnitIds"
                    + ", c.version AS version"
                    + ", c.licenseCode AS licenseCode"
                    + ", c.gridSize AS gridSize"
                    + ", c.detailGridSize AS detailGridSize"
                    + ", c.floatingGridSize AS floatingGridSize"
                    + ", c.searchInSubfolders AS searchInSubfolders"
                    + ", c.showExternalDialog AS showExternalDialog"
                    + ", c.showWelcomeDialog AS showWelcomeDialog"
                    + ", c.avatarId AS avatarId"
                    + ", c.lang AS lang"
                    + ", c.locale AS locale"
                    + ", c.authTypeIntegrated AS authTypeIntegrated"
                    + ", c.authTypeLdap AS authTypeLdap"
                    + ", c.authTypeLandingPage AS authTypeLandingPage"
                    + ", c.authTypeOidc AS authTypeOidc"
                    + ", c.bossId AS bossId"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " LEFT JOIN c.puestos p"
                + " LEFT JOIN c.boss b"
                + " LEFT JOIN c.businessUnitDepartment d"
                + " LEFT JOIN p.une bu"
                + " LEFT JOIN p.corp org "
                + " WHERE <filter>"
                + " GROUP BY"
                    + " c.id"
                    + ", c.status"
                    + ", c.code"
                    + ", c.description"
                    + ", c.correo"
                    + ", c.cuenta"
                    + ", b.id"
                    + ", b.description"
                    + ", d.id"
                    + ", d.description"
                    + ", c.version"
                    + ", c.licenseCode"
                    + ", c.gridSize"
                    + ", c.detailGridSize"
                    + ", c.floatingGridSize"
                    + ", c.searchInSubfolders"
                    + ", c.showExternalDialog"
                    + ", c.showWelcomeDialog"
                    + ", c.avatarId"
                    + ", c.lang"
                    + ", c.locale"
                    + ", c.authTypeIntegrated"
                    + ", c.authTypeLdap"
                    + ", c.authTypeLandingPage"
                    + ", c.authTypeOidc"
                    + ", c.bossId";

    private final String LOAD_USER_ESCALABLE_HQL = ""
    + " SELECT new " + UserEscalableDTO.class.getCanonicalName() + "("
        + " u.id,"
        + " u.status"
    + " )"
    + " FROM " + User.class.getCanonicalName() + " u"
    + " WHERE u.code = :code";
    private final String BOSS_SELECT_HQL = ""
        + " SELECT u.bossId"
        + " FROM " + User.class.getCanonicalName() + " u"
        + " WHERE u.code = :code";
    
    public static final String DUPLICATE_KEY_ACCOUNT = "DUPLICATE_KEY_ACCOUNT";    
    public static final String TEMPORALY_UNAVAILABLE_RETRY_SAVE = "TEMPORALY_UNAVAILABLE_RETRY_SAVE";    
    public static final String DUPLICATE_KEY_CODE = "DUPLICATE_KEY_CODE";
    public static final String ALREADY_IN_EDTION = "ALREADY_IN_EDITION";   
    public static final String UQ_CODE_KEY_NAME = "uk_users_code";      
    public static final String UQ_ACCOUNT_KEY_NAME = "uk_users_account"; 
    
    private boolean checkNotEnoughLicences(User ent) {
        if (Objects.equals(ent.getStatus(), User.STATUS.INACTIVE.getValue())) {
            return false;
        }
        final Integer exceededLicences = Utilities.exceededLicences(ent.getLicenseCode(), this);
        return exceededLicences > 0 || (exceededLicences == 0 && !isActiveUser(ent.getId()));
    }

    private boolean isActiveUser(Long userId) {
        return Objects.equals(HQL_findSimpleInteger(""
                + " SELECT c.status"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", userId), User.STATUS.ACTIVE.getValue());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer getUserVersion(final String login) {
        return HQL_findSimpleInteger(USER_VERSION_BY_LOGIN, "login", login);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isAdmin(final Long userId) {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("id", userId);
        final Long root = HQL_findSimpleLong(""
                + " SELECT root"
                + " FROM " + User.class.getCanonicalName() +  " u"
                + " WHERE u.id = :id", params);
        return root != null && root.equals(1L);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public int updateBreakDates() {
        return updateBreakDates(null);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public int updateBreakDates(Long userId) {
        getEntityManager().flush();
        final Map<String, Object> params = new HashMap<>();
        StringBuilder sqlBuilder = new StringBuilder(300).append(""
            + " WITH id AS ( "
                + " SELECT CASE  "
                    + " WHEN max(user_break_date_id) IS NULL "
                    + " THEN 0 "
                    + " ELSE max(user_break_date_id) "
                    + " END next_id "
                + " FROM user_break_date "
            + " ) "
            + " INSERT INTO user_break_date ( "
                + " user_break_date_id "
                + " ,break_date "
                + " ,user_id "
                + " ,STATUS "
            + " ) "
            + " SELECT row_number() OVER ( "
                    + " ORDER BY u.user_id "
                    + " ,ub.break_date ASC "
                + " ) + id.next_id AS user_break_date_id "
                + " ,ub.break_date "
                + " ,u.user_id "
                + " ,1 STATUS "
            + " FROM id "
            + " CROSS JOIN users u "
            + " CROSS JOIN user_break ub "
            + " LEFT JOIN user_break_date ubd ON ubd.user_id = u.user_id AND ubd.break_date = ub.break_date AND ubd.STATUS = 1 "
            + " WHERE ub.STATUS = 1 "
                + " AND ubd.user_break_date_id IS NULL "
                + " AND ub.deleted = 0 "
                + " AND ( "
                    + " ub.type = 2 "
                    + " OR ( "
                        + " ub.type = 1 "
                        + " AND ub.user_id = u.user_id "
                    + " ) "
                + " ) ");
        if (userId != null) {
            sqlBuilder.append(" AND u.user_id = :userId");            
            params.put("userId", userId);
        }
        sqlBuilder.append(""
            + " GROUP BY ub.break_date "
                + " ,u.user_id "
                + " ,id.next_id ")
                ;
        Integer result = SQL_updateByQuery(
                sqlBuilder.toString(), 
                params,
                0,
                Arrays.asList("user_break_date")
        );
        if (result == 0) {
            getLogger(LOGGER.PING).warn("New user {} doesn't have break days!", userId);
        }
        return result;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    @OnSaveUser
    public GenericSaveHandle save(final User user, final ILoggedUser loggedUser) {
        final Long loggedUserId;
        if (loggedUser != null) {
            loggedUserId = loggedUser.getId();
        } else {
            loggedUserId = null;
        }
        User ent = user;
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("User Save : {}", Utilities.getSerializedObj(ent));
        }
        final UserCountPendingDTO pending = new UserCountPendingDTO();
        final List<Long> oldJobs;
        final List<Long> oldTeams;
        final Boolean isEdition = ent.getId() != -1;
        final List<TeamUserSaveDTO> teams = user.getTeams();
        if (isEdition) {
            oldJobs = getJobIds(ent.getId());
            oldTeams = getTeamsIds(ent.getId());
            pending.setOldExpiredDocumentIds(loadExpiredDocumentPendingsCount(ent.getId()));
            if (ent.getBossId() != null && ent.getBossId() > 0) {
                pending.setOldFillRequestIds(loadFillRequestPendingsCount(ent.getBossId()));
                pending.setOldRequestIds(loadRequestPendingsCount(ent.getBossId()));
            }
        } else {
            oldJobs = Utilities.EMPTY_LIST;
            oldTeams = Utilities.EMPTY_LIST;
        }
        Long oldBossId;
        if (isEdition) {
            oldBossId = UserUtil.getUserBossId(ent.getId(), this);
        } else {
            oldBossId = null;
        }
        if (ent.getCode() == null || ent.getCode().isEmpty()) {
            ent.setCode("*generar*");
        } else {
            // Non-Repeated Code validation
            final Map<String, Object> params = new HashMap<>(2);
            params.put("id", user.getId());
            params.put("code", ent.getCode());
            final Integer codeCount = HQL_findSimpleInteger(""
                    + " SELECT count(1)"
                    + " FROM " + User.class.getCanonicalName() + " c "
                    + " WHERE c.id <> :id"
                    + " AND c.code = :code", params); 
            if (codeCount != 0) {
                gsh.setOperationEstatus(0);
                gsh.setErrorMessage(DUPLICATE_KEY_CODE);
                return gsh;
            }     
        }
        // Non-Repeated account name validation
        final Map<String, Object> params = new HashMap<>(2);
        params.put("id", user.getId());
        params.put("account", ent.getCuenta());
        final Integer accountNameCount = HQL_findSimpleInteger(""
                + " SELECT count(1)"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " WHERE c.id <> :id"
                + " AND c.cuenta = :account", params); 
        
        if (accountNameCount != 0){
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage(DUPLICATE_KEY_ACCOUNT);
            return gsh;
        }
        
        final boolean nuevo = (ent.getId() == -1);
        String type = "add_success";
        if (nuevo) {
            ent.setAskToRenewTimezone(true);
        } else {
            final Boolean askToRenewTimezone = HQL_findSimpleBoolean(""
                + " SELECT askToRenewTimezone "
                + " FROM " + User.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", ent.getId()
            ); 
            ent.setAskToRenewTimezone(
                Boolean.TRUE.equals(askToRenewTimezone)
            );
            ent.setRoot(
                HQL_findSimpleInteger(""
                + " SELECT c.root"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", ent.getId())
            );
        }
        if (nuevo) {
            ent.setScalable(1);
        } else {
            ent.setScalable(
                    HQL_findSimpleInteger(""
                    + " SELECT c.scalable"
                    + " FROM " + User.class.getCanonicalName() + " c "
                    + " WHERE c.id = :id", "id", ent.getId())
            );
            ent.setCreationSource(
                    HQL_findSimpleInteger(""
                    + " SELECT c.creationSource"
                    + " FROM " + User.class.getCanonicalName() + " c "
                    + " WHERE c.id = :id", "id", ent.getId())
            );
        }
        if (ent.getLicenseCode() == null || ent.getLicenseCode().isEmpty()) {
            ent.setLicenseCode(LicenseUtil.DEFAULT_LICENSE_SCHEMA);
        }
        final Settings settings = Utilities.getSettings();
        if (checkNotEnoughLicences(ent)) {
            ent.setStatus(User.STATUS.INACTIVE.getValue());
            ent.setCertificate(null);
            ent.setInactiveBySystem(1); 
            if (nuevo) {
                type = "add_inactive_success";
            }
        } else if (!nuevo) {
            final ILicenseUserDAO licenseDao = getBean(ILicenseUserDAO.class);
            final String encryptedCert = licenseDao.generateCertificateToken(ent.getCuenta());
            ent.setCertificate(encryptedCert);
            final Integer inactiveBySystem = HQL_findSimpleInteger(""
                    + " SELECT c.inactiveBySystem"
                    + " FROM " + User.class.getCanonicalName() + " c "
                    + " WHERE c.id = :id", "id", ent.getId());
            if (inactiveBySystem != null && inactiveBySystem.equals(1)) {
                ent.setInactiveBySystem(0);
            }
        } else {
            final ILicenseUserDAO licenseDao = getBean(ILicenseUserDAO.class);
            final String encryptedCert = licenseDao.generateCertificateToken(ent.getCuenta());
            ent.setCertificate(encryptedCert);            
        }
        final boolean generaClave = (ent.getCode().equals("*generar*"));
        if (nuevo) {
            ent.setGridSize(15);
            ent.setDetailGridSize(15);
            ent.setFloatingGridSize(5);
            ent.setSearchInSubfolders(settings.getSearchSubFolder());
            ent.setShowExternalDialog(1);
        } else {
            type = "edit_success";
        }

        final String password = ent.getContrasena();
        if (password != null && password.startsWith("#Bnext") && password.endsWith("||")) {
            String realPassword = password.substring(6,password.length()-2);
            //Validación de la contraseña debe contener al menos una letra minúscula, una letra mayúscula, un carácter especial y dos números
            Matcher m = PASSWORD_VALIDATE_PATTERN.matcher(realPassword);
            if (!m.matches()) {
                ent.setAskToRenewPassword(true);
            } else {
                // Ponemos en 0 para evitar renovar la contraseña en la creación de usuarios nuevos,
                // ya que, ya se registraron con las reglas de validación de la contraseña
                ent.setAskToRenewPassword(false);
            }
            ent.setHashedPassword(PasswordEncoder.encode(realPassword));
        } else if (Objects.equals("hashedPassword", password)) {
            final String hashedPassword = HQL_findSimpleString(""
                    + " SELECT c.hashedPassword"
                    + " FROM " + User.class.getCanonicalName() + " c "
                    + " WHERE c.id = :id", "id", ent.getId());
            ent.setHashedPassword(hashedPassword);
        } else if (password != null) {
            ent.setHashedPassword(ent.getContrasena());
        }
        ent.setContrasena(null);
        if (nuevo) {
            ent.setVersion(0);
        } else {
            ent.setVersion(getUserVersion(ent.getCuenta()) + 1);
        }
        if (ent.getBusinessUnitDepartmentId() == null) {
            ent.setAskToRenewLocation(RenewDataStatus.RENEW_DATA.value());
        } else {
            final Map<String, Long> businessUnitDepartment = HQL_findSimpleMap(""
               + " SELECT new map("
                   + " bud.businessUnitId AS businessUnitId"
                   + ",bud.departmentId AS departmentId"
               + " )"
               + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " bud "
               + " WHERE bud.id = " + ent.getBusinessUnitDepartmentId()
           );
           ent.setDepartmentId(businessUnitDepartment.get("departmentId"));
           ent.setBusinessUnitId(businessUnitDepartment.get("businessUnitId"));
           ent.setAskToRenewLocation(RenewDataStatus.DATA_RENEWED.value());
        }
        ent = makePersistent(ent, loggedUserId);
        if (user.getCloseSession() != null && user.getCloseSession()) {
            final UserLogin loginInfo = new UserLogin(user.getId());
            loginInfo.removeSession(true);
        }
        if (ent == null) {
            gsh.setOperationEstatus(0);
        } else {
            if (generaClave) {
                String clave = "USR-" + ent.getId();
                ent.setCode(clave);
                ent = makePersistent(ent, loggedUserId);
            }
            if (ent == null) {
                gsh.setErrorMessage(UQ_CODE_KEY_NAME);
                gsh.setOperationEstatus(0);
                return gsh;
            }
            saveUserTeams(ent, teams, nuevo, oldTeams, loggedUserId);
            final Long bossId = ent.getBossId();
            final Boolean isEditionWithChangedJobs = hasChangedJobs(isEdition, oldJobs, ent);
            final Boolean isEditionWithChangedBoss = hasChangedBoss(isEdition, oldBossId, bossId);
            final Boolean isNewWithJobs = nuevo && user.getPuestos() != null && !user.getPuestos().isEmpty();
            final Map<String, String> values = new HashMap<>(1);
            values.put("clave", ent.getCode());
            gsh.setSavedId(ent.getId().toString());
            gsh.setOperationEstatus(1);
            gsh.setExtraJson(Utilities.getSerializedObj(values));
            gsh.setSuccessMessage("{\"tipo\":\"" + type + "\"}");
            if (nuevo) {
                updateBreakDates(ent.getId());
            }
            if (isEditionWithChangedJobs || isNewWithJobs) {
                getEntityManager().flush();
                pending.setNewExpiredDocumentIds(loadExpiredDocumentPendingsCount(ent.getId()));
            }
            if (isEditionWithChangedBoss || isEditionWithChangedJobs || isNewWithJobs) {
                final Set<Long> userIds = new HashSet<>(3);
                userIds.add(ent.getId());
                if (isEditionWithChangedBoss) {
                    if (oldBossId != null && oldBossId != 0L) {
                        userIds.add(oldBossId);
                    }
                    if (bossId != null && bossId != 0L) {
                        userIds.add(bossId);
                    }
                }
                getEntityManager().flush();
                updateWorkflowPreviewByUserIds(userIds);
                final Set<Long> removedJobsIds = getRemovedJobIds(isEdition, oldJobs, ent);
                if (removedJobsIds != null && !removedJobsIds.isEmpty()) {
                    updateWorkflowPreviewByPositionIds(removedJobsIds);
                }
            }
            if (isNewWithJobs) {
                final List<Long> jobs = user.getPuestos().stream().map(job -> job.getId()).collect(Collectors.toList());
                getEntityManager().flush();
                getAspectJAutoProxy().addJobs(ent.getId(), pending, jobs, loggedUser);
                gsh.getJsonEntityData().put(ASSIGN_JOB_UPDATE_REQUIRED, false);
            } else if (nuevo) {
                gsh.getJsonEntityData().put(ASSIGN_JOB_UPDATE_REQUIRED, true);        
            }
            if (isEditionWithChangedJobs) {
                changeJobs(ent, pending, oldJobs, loggedUser);
                gsh.getJsonEntityData().put(ASSIGN_JOB_UPDATE_REQUIRED, true);
            } else if (ent.getPuestos() == null || ent.getPuestos().isEmpty()) {
                gsh.getJsonEntityData().put(ASSIGN_JOB_UPDATE_REQUIRED, true);
            }
            if (isEditionWithChangedBoss) {
                getEntityManager().flush();
                if (ent.getBossId() != null) {
                    pending.setNewFillRequestIds(loadFillRequestPendingsCount(ent.getBossId()));
                    pending.setNewRequestIds(loadRequestPendingsCount(ent.getBossId()));
                }
                getAspectJAutoProxy().changeBoss(ent, oldBossId, bossId, pending, loggedUser);
            }
        }
        return gsh;
    }

    private boolean hasChangedBoss(final Boolean isEdition, final Long oldBossId, final Long newBossId) {
        if (!isEdition) {
            return false;
        }
        final Boolean changed = !Objects.equals(oldBossId, newBossId);
        return changed;
    }

    private boolean hasChangedJobs(final Boolean isEdition, final List<Long> oldJobs, final User ent) {
        if (!isEdition) {
            return false;
        }
        final List<Long> currentJobIds = getJobsIds(ent);
        final Boolean sameJobs = currentJobIds.containsAll(oldJobs) && oldJobs.containsAll(currentJobIds);
        return !sameJobs;
    }
    
    private Set<Long> getRemovedJobIds(final Boolean isEdition, final List<Long> oldJobs, final User ent) {
        if (!isEdition || oldJobs == null || oldJobs.isEmpty()) {
            return null;
        }
        final List<Long> currentJobIds = getJobsIds(ent);
        final List<Long> result = new ArrayList<>(oldJobs);
        result.removeAll(currentJobIds);
        return new HashSet<>(result);        
    }
    
    private List<Long> getJobsIds(final User ent) {
        if (ent.getPuestos() == null || ent.getPuestos().isEmpty()) {
            return Utilities.EMPTY_LIST;
        }
        final List<Long> ids = ent.getPuestos().stream()
                .map((team) -> team.getId())
                .collect(Collectors.toList());
        return ids;
        
    }
    
    private List<Long> getTeamsIds(final List<TeamUserSaveDTO> teams) {
        if (teams == null || teams.isEmpty()) {
            return Utilities.EMPTY_LIST;
        }
        final List<Long> currentTeams = teams.stream()
                .map((team) -> team.getOwnerId())
                .collect(Collectors.toList());
        return currentTeams;
    }
    
    private List<Long> getTeamsIds(Long userId) {
        String query = ""
                + " SELECT ou.ownerId"
                + " FROM " + OwnerTeamUser.class.getCanonicalName() + " ou"
                + " WHERE ou.type IS NOT NULL "
                + " AND ou.userId = :id"
                + " AND ou.type IN ( "
                        + OwnerTeamUser.TYPE.GLOBAL.getValue() + "," +
                        + OwnerTeamUser.TYPE.BY_GROUP.getValue()
                + " )";
        
        
        return HQL_findByQuery(query, "id", userId);
    }
    
    private Set<Long> loadExpiredDocumentPendingsCount(Long userId) {
        final IPendingQueries ops = new ToRenew(this);
        final Set<Long> documentIds = new HashSet<>(HQL_findByQuery(""
                + " SELECT c.id"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " WHERE " + ops.filterSourceRecordsByUser(userId, "c")
        ));
        return documentIds;
    }
    
    private Set<Long> loadFillRequestPendingsCount(Long userId) {
        final IPendingQueries ops = new ToFillForm(this);
        final Set<Long> fillRequestIds = new HashSet<>(HQL_findByQuery(""
                + " SELECT c.id"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE " + ops.filterSourceRecordsByUser(userId, "c")
        ));
        return fillRequestIds;
    }
    
    private Set<Long> loadRequestPendingsCount(Long userId) {
        final IPendingQueries ops = new ToAuthorizeRequest(this);
        final Set<Long> requestIds = new HashSet<>(HQL_findByQuery(""
                + " SELECT c.id"
                + " FROM " + Request.class.getCanonicalName() + " c"
                + " WHERE " + ops.filterSourceRecordsByUser(userId, "c")
        ));
        return requestIds;
    }
    
    private Integer saveUserTeams(
            final User ent,
            final List<TeamUserSaveDTO> teams,
            final Boolean nuevo,
            final List<Long> oldTeams,
            final Long loggedUserId
    ) {
        if (nuevo) {
            if (teams == null || teams.isEmpty()) {
                return 0;
            }
            final Long userId = ent.getId();
            teams.forEach(team -> saveTeam(userId, team, loggedUserId));
            return teams.size();
        } else {
            final List<Long> currentTeams = getTeamsIds(teams);
            if (currentTeams.containsAll(oldTeams) && oldTeams.containsAll(currentTeams)) {
                return 0;
            }
            deleteTeams(ent, teams);
            if (teams == null || teams.isEmpty()) {
                return 0;
            }
            final Long userId = ent.getId();
            teams.forEach(team -> saveTeam(userId, team, loggedUserId));
            return teams.size();
        }
    }
   
    private void deleteTeams(final User ent, final List<TeamUserSaveDTO> teams) {
        final Map<String, Object> params = new HashMap<>();
        final String teamsFilter;
        if (teams != null && !teams.isEmpty()) {
            final List<Long> ownerIds = teams.stream()
                    .filter(team -> team.getOwnerId() != null)
                    .map(team -> team.getOwnerId())
                    .collect(Collectors.toList());
            if (!ownerIds.isEmpty()) {
                teamsFilter = " AND " + BindUtil.bindFilterListNotIn("ou.owner.id", ownerIds, true, null, params);
            } else {
                teamsFilter = "";

            }
        } else {
            teamsFilter = "";
        }
        params.put("userId", ent.getId());
        HQL_updateByQuery(""
            + " DELETE FROM " + OwnerTeamUser.class.getCanonicalName() + " ou "
            + " WHERE ou.type IS NOT NULL "
            + " AND ou.userId = :userId"
            + teamsFilter
            + " AND ou.type IN ( "
                    + OwnerTeamUser.TYPE.GLOBAL.getValue() + "," +
                    + OwnerTeamUser.TYPE.BY_GROUP.getValue()
            + " )",
           params
        );
    }
    
    private void saveTeam(final Long userId, final TeamUserSaveDTO team, final Long loggedUserId) {
        final OwnerTeam owner = HQLT_findById(OwnerTeam.class, team.getOwnerId());
        Owner.TYPE type = Owner.TYPE.getValue(team.getType());
        switch (type) {
            case GLOBAL_TEAM:
                if (owner.getUsers() == null) {
                    owner.setUsers(new HashSet<>(1));
                }
                owner.getUsers().add(new OwnerTeamUser(-1L, userId, null, owner, OwnerTeamUser.TYPE.GLOBAL));
                break;
            case TEAM_BY_GROUP:  
                if (owner.getUsers() == null) {
                    owner.setUsers(new HashSet<>(1));
                }
                owner.getUsers().add(new OwnerTeamUser(-1L, userId, team.getUserGroupId(), owner, OwnerTeamUser.TYPE.BY_GROUP));
                break;
        }
        makePersistent(owner, loggedUserId);
    }
    
    private void changeJobs(
            final User ent, 
            final UserCountPendingDTO countPending,
            final List<Long> oldJobs,
            final ILoggedUser loggedUser
    ) {
        List<Long> newJobs = new ArrayList<>(ent.getPuestos().size());
        List<Long> deletedJobs = new ArrayList<>(ent.getPuestos().size());
        deletedJobs.addAll(oldJobs);
        for (Position puesto : ent.getPuestos()) {
            if (oldJobs.contains(puesto.getId())) {
                deletedJobs.remove(puesto.getId());
            } else {
                newJobs.add(puesto.getId());
            }

        }
        if (!newJobs.isEmpty()) {
            getEntityManager().flush();
            getAspectJAutoProxy().addJobs(ent.getId(), countPending, newJobs, loggedUser);
        }
        if (!deletedJobs.isEmpty()) {
            getEntityManager().flush();
            getAspectJAutoProxy().removeJobs(ent.getId(), countPending, deletedJobs, loggedUser);
        }
    }

    
    public static final String validEntitiesFilterByAdminWithProcessExtra = ""
            + " exists ( "
                + " SELECT u.id FROM "
                + " DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " left JOIN p.procesos pr "
                + " WHERE"
                + " ("
                    + " c.id = u.id "
                    + " AND pr.id IN (:process)"
                + ":extra )  "
            + " )";
    public static final String validEntitiesFilterByUserAtNodeAccess = ""
        + " ("
            + " :id IN ("
                + " SELECT u.id"
                + " FROM DPMS.Mapping.NodeBusinessUnit nbu, DPMS.Mapping.User u"
                + " JOIN u.puestos p"
                + " WHERE"
                    + " nbu.id.nodeId = :nodeId"
                    + " AND nbu.id.businessUnitId = p.une.id"
            + " )"
            + " OR :id IN ("
                + " SELECT u.id"
                + " FROM DPMS.Mapping.NodeBusinessUnit nbu, DPMS.Mapping.User u"
                + " WHERE"
                    + " nbu.id.nodeId = :nodeId"
                    + " AND nbu.id.businessUnitId = u.businessUnitId "
            + " )"
            + " OR :id IN ("
                + " SELECT u.id"
                + " FROM DPMS.Mapping.NodeBusinessUnitDepartment nbud, DPMS.Mapping.User u"
                + " JOIN u.puestos p"
                + " JOIN p.departamentos d"
                + " WHERE"
                    + " nbud.id.nodeId = :nodeId"
                    + " AND nbud.id.businessUnitDepartmentId = d.id"
            + " )"
            + " OR :id IN ("
                + " SELECT u.id"
                + " FROM DPMS.Mapping.NodeBusinessUnitDepartment nbud, DPMS.Mapping.User u"
                + " WHERE"
                    + " nbud.id.nodeId = :nodeId"
                    + " AND nbud.id.businessUnitDepartmentId = u.businessUnitDepartmentId"
            + " )"
            + " OR :id IN ("
                + " SELECT nu.id.userId"
                + " FROM DPMS.Mapping.NodeUser nu"
                + " WHERE"
                    + " nu.id.nodeId =  :nodeId"
            + " ) OR :id IN ("
                + " SELECT u.id"
                + " FROM DPMS.Mapping.NodeArea na, DPMS.Mapping.User u"
                + " JOIN u.puestos p JOIN p.procesos pro"
                + " WHERE"
                    + " na.id.nodeId = :nodeId"
                    + " AND na.id.areaId = pro.id"
            + " )"
            + " ) ";
    public static final String validEntitiesFilterByUserWithProcessExtra = ""
            + " exists ( "
                + " SELECT u.id FROM "
                + " DPMS.Mapping.User u "
                + " LEFT JOIN u.puestos p "
                + " LEFT JOIN p.procesos pr "
                + " WHERE"
                + " ("
                    + " exists ( "
                        + " SELECT us.id "
                        + " FROM DPMS.Mapping.User us "
                        + " LEFT JOIN us.puestos pu "
                        + " LEFT JOIN pu.perfil perfil "
                        + " WHERE us.id = :userId"
                        + " AND ( "
                            + " p.une.id = pu.une.id"
                            + " OR u.businessUnitId = pu.une.id"
                            + " OR p.une.id = us.businessUnitId"
                            + " OR u.businessUnitId = us.businessUnitId"
                        + " )"
                        + " AND 1 IN (:servicios)"
                    + " ) AND c.id = u.id "
                    + " AND pr.id IN (:process)"
                + ":extra )  "
            + " )";
    /*Filtro para usuarios de planta*/
    private static final String validEntitiesFilterByUser = ""
        + " c.id = :userId "
        + " OR ("
            + " c.status = :status"
            + " AND c.id IN ( "
                + " SELECT u.id "
                + " FROM " + User.class.getCanonicalName() + " u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " LEFT JOIN p.une bu"
                + " LEFT JOIN p.corp org"
                + " WHERE ("
                    + " EXISTS ( "
                        + " SELECT 1 "
                        + " FROM " + User.class.getCanonicalName() + " us "
                        + " JOIN us.puestos pu "
                        + " JOIN pu.perfil perfil "
                        + " LEFT JOIN pu.une bus "
                        + " LEFT JOIN pu.corp orgu "
                        + " WHERE us.id = :userId "
                        + " AND ("
                            + " bu.id = bus.id"
                            + " OR ("
                                + "perfil.intBUsuarioCorporativo = 1"
                                + " AND org.id = orgu.id"
                            + ")"
                        + ")"
                        + " AND 1 IN (:servicesMine)"
                    + " )"
                    + " AND 1 IN (:servicesTheirs)"
                + " )"
            + " )"
        + " )";

    /*Filtro para usuarios de organización*/
    public static final String ORGANIZATIONAL_UNIT_BY_USER_FILTER = ""
            + " c.id = :userId "
            + " OR EXISTS ( "
                + " SELECT 1"
                + " FROM " + User.class.getCanonicalName() + " u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " WHERE EXISTS ( "
                    + " SELECT 1 "
                    + " FROM " + User.class.getCanonicalName() + " us "
                    + " JOIN us.puestos pu "
                    + " JOIN pu.perfil perfil "
                    + " WHERE us.id = :userId"
                    + " AND p.corp.id = pu.corp.id"
                    + " AND 1 IN (:servicesMine)"
                + " ) "
                + " AND c.id = u.id "
                + " AND 1 IN (:servicesTheirs)"
            + " )";

    public static final String validEntitiesFilterByPosition = ""
            + " exists ("
            + " SELECT 1 "
            + " FROM " + UserPosition.class.getCanonicalName() + " u "  
            + " WHERE u.userId = c.id"
            + " )";
    
    /*Filtro para usuarios por departamento*/
    private static final String validEntitiesFilterByBusinessUnitDepartmentUser = ""
        + " c.id = :userId "
        + " OR ( "
            + " c.status = " + User.STATUS.ACTIVE.getValue()
            + " AND c.id IN ( "
                + " SELECT u.id"
                + " FROM DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " JOIN p.departamentos dept "
                + " WHERE (exists ( "
                    + " SELECT pu.une.id "
                    + " FROM DPMS.Mapping.User us "
                    + " JOIN us.puestos pu "
                    + " JOIN pu.perfil perfil "
                    + " JOIN pu.departamentos dptu "
                    + " WHERE us.id = :userId"
                    + " AND ("
                        + " dept.id = dptu.id"
                        + " OR u.businessUnitDepartmentId = dptu.id"
                        + " OR dept.id = us.businessUnitDepartmentId"
                        + " OR u.businessUnitDepartmentId = us.businessUnitDepartmentId"
                    + " )"
                    + " AND 1 IN (:servicesMine)"
                + " AND 1 IN (:servicesTheirs)))"
            + ")"
        + " )"
            ;
    
    /*Filtro para usuarios por departamento*/
    private static final String validEntitiesFilterByBusinessUnitDepartmentId = ""
        + " ( "
            + " c.status = " + User.STATUS.ACTIVE.getValue()
            + " AND c.id IN ( "
                + " SELECT u.id"
                + " FROM "+ User.class.getCanonicalName()+ " u "
                + " LEFT JOIN u.puestos p "
                + " LEFT JOIN p.departamentos dept "
                + " WHERE dept.id IN (:businessUnitDepartmentsIds) "
                + " OR u.businessUnitDepartmentId IN (:businessUnitDepartmentsIds) "
            + " )"
        + " )";
    
    /*Filtro para usuarios por organización*/
    private static final String validEntitiesFilterByOrganizationUnitUser = ""
        + " c.id = :userId "
        + " OR ( "
            + " c.status = " + User.STATUS.ACTIVE.getValue()
            + " AND c.id IN ( "
                + " SELECT u.id"
                + " FROM DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " WHERE exists ( "
                    + " SELECT pu.une.id "
                    + " FROM DPMS.Mapping.User us "
                    + " JOIN us.puestos pu "
                    + " JOIN pu.perfil perfil "
                    + " WHERE us.id = :userId"
                    + " AND p.corp.id = pu.corp.id"
                    + " AND 1 IN (:servicesMine)"
                + " )"
                + " AND 1 IN (:servicesTheirs)"
            + " )"
        + " )";
    /*Filtro por servicios de usuarios*/
    public static final String validEntitiesFilterByServices = ""
            + " c.id IN ( "
                + " SELECT u.id"
                + " FROM DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " WHERE 1 IN (:servicesTheirs)"
            + " )";
    public static String validEntitiesFilterByUne = ""
            + "  c.id IN ( "
                + " SELECT u.id"
                + " FROM DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " WHERE ("
                + " p.une.id IN (:unes)"
                    + " OR u.businessUnitId IN (:unes)"
                + " )  "
                + " AND 1 IN (:servicesTheirs)"
                + " AND u.status = 1"
            + " )";
    /*Filtro para usuarios corporativos*/
    private static String validEntitiesFilterByCorporativeUser = ""
            + "  c.id IN ( "
                + " SELECT u.id"
                + " FROM " + User.class.getCanonicalName() + " u "
                + " JOIN u.puestos p "
                + " WHERE EXISTS ( "
                    + " SELECT 1"
                    + " FROM " + User.class.getCanonicalName() + " us "
                    + " JOIN us.puestos pu "
                    + " JOIN pu.perfil perfil "
                    + " WHERE us.id = :userId"
                    + " AND p.corp.id = pu.corp.id"
                    + " AND 1 IN (:servicesMine)"
                + " ) "
                + " AND p.perfil.intBUsuarioCorporativo = 1"
            + " )";
    /*Filtro para usuarios por departamento*/
    private static final String validEntitiesFilterByDepartmentUser = ""
            + "  c.id IN ( "
                + " SELECT u.id"
                + " FROM DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " JOIN p.departamentos dept "
                + " WHERE (exists ( "
                    + " SELECT pu.une.id "
                    + " FROM DPMS.Mapping.User us "
                    + " JOIN us.puestos pu "
                    + " JOIN pu.perfil perfil "
                    + " WHERE us.id = :userId"
                    + " AND ("
                        + " p.une.id = pu.une.id "
                        + " OR u.businessUnitId = pu.une.id "
                        + " OR p.une.id = us.businessUnitId "
                        + " OR u.businessUnitId = us.businessUnitId "
                    + " )"
                    + " AND 1 IN (:servicesMine)"
                + " )"
                + " AND 1 IN (:servicesTheirs)"
                + " AND ("
                    + " dept.id = :deptId"
                    + " OR u.businessUnitDepartmentId = :deptId"
                + " )"
                + " AND u.status = " + User.ACTIVE_STATUS + ")"
            + " )";
    /*Filtro para usuarios por BusinessUnit*/
    private static final String validEntitiesFilterByBusinessUnit = ""
            + " c.id IN ( "
                + " SELECT u.id"
                + " FROM DPMS.Mapping.User u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " JOIN p.departamentos dept "
                + " WHERE "
                    + " 1 IN (:servicesTheirs)"
                    + " AND ("
                        + " dept.businessUnit = :businessUnit "
                        + " OR u.businessUnitId = :businessUnit "
                    + " )"
                + " AND u.status = " + User.ACTIVE_STATUS 
            + " )";
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio,
            boolean isAdmin) {
        setCriteria(filter, userId, servicio, isAdmin, User.STATUS.ACTIVE.getValue());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio,
            boolean isAdmin, int status) {
        setCriteria(filter, userId, servicio, isAdmin, status);
    }
    
    private void setCriteria(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin, int status){
        filter.getCriteria().put("<filtered-entity>", isAdmin ? ""
                : getValidEntitiesFilterByUser(userId, ProfileServices.getCodedServices("perfil", servicio), "1", "" + status));
    }
    
    /**
     * Función que devuelve los encargados de acciones de una planta
     *
     * @param isAdmin
     * @param businessUnit
     * @return 
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<UserHasValue> getFindingManagerInBusinessUnitCbo(Long businessUnit) {  
        
        ProfileServices[] services = {ProfileServices.ACCION_ENCARGADO};
        String filter =  validEntitiesFilterByUne
                .replaceAll(":unes", businessUnit.toString())
                .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", services));
        
        return getUsersComboList(filter);
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<UserRef> getTimeworkSyncs() {        
        ProfileServices[] services = {ProfileServices.TIMEWORK_SYNC};
        String filter =  validEntitiesFilterByServices
                .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", services));
        
        return HQLT_findByQueryFilter(UserRef.class, filter);
    }
    
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<UserRef> getFindingManagerInBusinessUnit(Long businessUnit) {        
        ProfileServices[] services = {ProfileServices.ACCION_ENCARGADO};
        String filter =  validEntitiesFilterByUne
                .replaceAll(":unes", businessUnit.toString())
                .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", services));
        
        return HQL_findByQuery(""
                + "SELECT new " + UserRef.class.getCanonicalName() + " "
                    + "( "
                        + "c.id,"
                        + " c.account,"
                        + " c.description,"
                        + " c.correo,"
                        + " c.version"
                    + " ) "
                + "FROM " + UserRef.class.getCanonicalName() + " c "
                + " WHERE " + filter);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public List<UserRef> getFindingManagers() {        
        ProfileServices[] services = {ProfileServices.ACCION_ENCARGADO};
        String filter =  validEntitiesFilterByServices
                .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", services));
        
        return HQLT_findByQueryFilter(UserRef.class, filter);
    }

    /**
     * Función que devuelve los usuarios que estén en la misma unidad de negocio que el usuario de userId si es Root,
     * devuelve la lista completa de usuarios, tambien filtra en base a los servicios propios del usuario y los
     * servicios de los usuarios retornados
     *
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId el usuario en base al cual se va a filtrar
     * @param servicesMine Los servicios que debe tener el usuario en base al cual se va a filtrar
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
     * @return Lista de usuarios que cumplen con todas las caraterísticas en formato TextHasValue
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInBusinessUnitOf(boolean isAdmin, Long userId, ProfileServices[] servicesMine,
            ProfileServices[] servicesTheirs) {
        return getUsersInBusinessUnitOf(isAdmin, userId, servicesMine, servicesTheirs, null);
    }

    private List<UserHasValue> getUsersInBusinessUnitOf(boolean isAdmin, Long userId, ProfileServices[] servicesMine,
            ProfileServices[] servicesTheirs, String def) {
        String filter = " c.status = " + User.STATUS.ACTIVE.getValue();
        if (!isAdmin) {
            if (def != null && !def.isEmpty()) {
                filter = " c.id = " + def + " OR ";
            } else {
                filter = "";
            }
            filter += " ( "
                    + getValidEntitiesFilterByUser("" + userId, ProfileServices.getCodedServices("perfil", servicesMine), ProfileServices.getCodedServices("pr", servicesTheirs))
                    + ")";
        }
        return getUsersComboList(filter, userId, false);
    }
    
    /**
     * Sobrecarga del metodo getUsersInBusinessUnitOf que omite los servicios del los usuarios retornados
     *
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId el usuario en base al cual se va a filtrar
     * @param servicesMine Los servicios que debe tener el usuario en base al cual se va a filtrar
     * @return Lista de usuarios que cumplen con todas las caraterísticas en formato TextHasValue
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInBusinessUnitOf(boolean isAdmin, Long userId, ProfileServices[] servicesMine) {
        ProfileServices[] servicesTheirs = {ProfileServices.ALL};
        return getUsersInBusinessUnitOf(isAdmin, userId, servicesMine, servicesTheirs);
    }
    
    /**
     * Sobrecarga del metodo getUsersInBusinessUnitOf que omite los servicios
     * 
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId el usuario en base al cual se va a realizar el filtro
     * @return Lista de usuarios que cumplen con todas las caraterísticas en formato TextHasValue
     * 
     * @since 2.3.2.81
     * <AUTHOR> Cavazos Galindo
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInBusinessUnitOf(boolean isAdmin, Long userId) {
        ProfileServices[] servicesMine = {ProfileServices.ALL};
        ProfileServices[] servicesTheirs = {ProfileServices.ALL};
        return getUsersInBusinessUnitOf(isAdmin, userId, servicesMine, servicesTheirs);
    }

    /**
     * Sobrecarga del metodo getUsersInOrganizationalUnitOf que omite los servicios del los usuarios retornados
     *
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId el usuario en base al cual se va a filtrar
     * @param servicesMine Los servicios que debe tener el usuario en base al cual se va a filtrar
     * @return Lista de usuarios que cumplen con todas las caraterísticas en formato TextHasValue
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInOrganizationalUnitOf(boolean isAdmin, Long userId, ProfileServices[] servicesMine) {
        ProfileServices[] servicesTheirs = {ProfileServices.ALL};
        return getUsersInOrganizationalUnitOf(isAdmin, userId, servicesMine, servicesTheirs);
    }
    
    
    /**
     * Función que devuelve los usuarios que estén en la misma organización que el usuario de userId si es Root,
     * devuelve la lista completa de usuarios, tambien filtra en base a los servicios propios del usuario y los
     * servicios de los usuarios retornados
     *
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId el usuario en base al cual se va a filtrar
     * @param servicesMine Los servicios que debe tener el usuario en base al cual se va a filtrar
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
     * @return Lista de usuarios que cumplen con todas las caraterísticas en formato TextHasValue
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInOrganizationalUnitOf(boolean isAdmin, Long userId, ProfileServices[] servicesMine,
            ProfileServices[] servicesTheirs) {
        return getUsersInOrganizationalUnitOf(isAdmin, userId, servicesMine, servicesTheirs, null);
    }
    
    /**
     * Sobrecarga del metodo getUsersInBusinessUnitOf que omite los servicios
     * 
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId el usuario en base al cual se va a realizar el filtro
     * @return Lista de usuarios que cumplen con todas las caraterísticas en formato TextHasValue
     * 
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInOrganizationalUnitOf(boolean isAdmin, Long userId) {
        ProfileServices[] servicesMine = {ProfileServices.ALL};
        ProfileServices[] servicesTheirs = {ProfileServices.ALL};
        return getUsersInBusinessUnitOf(isAdmin, userId, servicesMine, servicesTheirs);
    }

    
    private List<UserHasValue> getUsersInOrganizationalUnitOf(boolean isAdmin, Long userId, ProfileServices[] servicesMine,
            ProfileServices[] servicesTheirs, String def) {
        String filter = "1=1";
        if (!isAdmin) {
            if(def != null && !def.isEmpty()){
                filter = " c.id = " + def + " OR ";
            }else{
                filter = "";
            }
            filter += " ( "
                    + ORGANIZATIONAL_UNIT_BY_USER_FILTER
                    .replaceAll(":userId", userId + "")
                    .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicesMine))
                    .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", servicesTheirs))
                    + ")";
        }
        filter +=  " AND c.status = " + User.STATUS.ACTIVE.getValue();
        return getUsersComboList(filter, userId, false);
    }
    
    /**
     * Pendiente de documentar
     *
     * @param isAdmin
     * @param userId
     * @param servicio
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsComboListByCorp(boolean isAdmin, Long userId, ProfileServices[] servicio) {
        String filter = "1=1";
        if (!isAdmin) {
            filter = validEntitiesFilterByCorporativeUser
                    .replaceAll(":userId", userId + "")
                    .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicio));
        }
        return getUsersComboList(filter);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersWithPosition() {    
       return getUsersComboList(validEntitiesFilterByPosition);
    }

    /**
     * Pendiente de documentar
     *
     * @param filter
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getStrutsComboListWithBusinessUnit(String filter) {
        IBusinessUnitDepartmentLoadDAO dao = getBean(IBusinessUnitDepartmentLoadDAO.class);
        return dao.getStrutsComboList("businessUnit.description + ' - ' + description", filter);
    }

    
    /**
     *  Regresa los usuarios de las unidades de negocio a las que pertence el usuario por el que se filtra
     *
     * @param selectedUserId Valor que debe estar por default en los resultados
     * @param isAdmin Boolean que indica si el usuario por el que se filtra es admin
     * @param userId Id del usuario por el que se filtra
     * @param servicio  Servicios del usuario por el que se filtra
     * @return Lista de UserRef
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsUsersByUserBusinessUnits(Long selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        String filter = "";
        if (isAdmin) {
            filter = "c.status = 1";
        } else {
            filter = getValidEntitiesFilterByUser("" + userId, ProfileServices.getCodedServices("perfil", servicio), "1");
        }
        return getUsersComboList(filter, selectedUserId, true);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsUsersByUserBusinessUnits(List<Long> selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        String filter = "";
        if (isAdmin) {
            filter = "c.status = 1";
        } else {
            filter = getValidEntitiesFilterByUser("" + userId, ProfileServices.getCodedServices("perfil", servicio), "1");
        }
        return getUsersComboList(filter, selectedUserId, true);
    }

    /**
     *  Regresa los usuarios de las unidades de negocio a las que pertence el usuario por el que se filtra
     *
     * @param selectedUserId Valor que debe estar por default en los resultados
     * @param onlyActiveUsers Boolean que determina si se filtran solo los usuarios activos
     * @param isAdmin Boolean que indica si el usuario por el que se filtra es admin
     * @param userId Id del usuario por el que se filtra
     * @param servicio  Servicios del usuario por el que se filtra
     * @return Lista de UserRef
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserRef> getUsersByUserBusinessUnits(Long selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio, Boolean onlyActiveUsers) {
        return getUsersByUserBusinessUnits(new Long[] {selectedUserId}, userId, isAdmin, servicio, onlyActiveUsers);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserRef> getUsersByUserBusinessUnits(Long[] selectedUserIds, Long userId, Boolean isAdmin, ProfileServices[] servicio, Boolean onlyActiveUsers) {
        String filter = onlyActiveUsers ? "c.status = " + User.ACTIVE_STATUS : "1=1";
        if (!isAdmin) {
            filter = getValidEntitiesFilterByUser("" + userId, ProfileServices.getCodedServices("perfil", servicio), "1")
                    + " AND " + filter;        
        }
        if (selectedUserIds != null && selectedUserIds.length > 0) {
            List<Long> ids = new ArrayList<>(selectedUserIds.length);
            for (Long selectedUserId : selectedUserIds) {
                if (selectedUserId != null) {
                    ids.add(selectedUserId);
                }
            }
            if (!ids.isEmpty()) {
                filter = "c.id IN ( " + StringUtils.join(ids, ",") + " ) OR (" + filter + ")";
            }
        }
        return HQLT_findByQueryFilter(UserRef.class, filter);
    }
    
    /**
     *  Regresa los usuarios de las unidades de negocio a las que pertence el usuario por el que se filtra
     *
     * @param selectedUserId Valor que debe estar por default en los resultados
     * @param isAdmin Boolean que indica si el usuario por el que se filtra es admin
     * @param userId Id del usuario por el que se filtra
     * @param servicio  Servicios del usuario por el que se filtra
     * @return Lista de UserRef
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsUsersByUserDepartments(Long selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        String filter = "";
        if (isAdmin) {
            filter = "status = 1";
        } else {
                filter = validEntitiesFilterByBusinessUnitDepartmentUser
                        .replaceAll(":userId", userId + "")
                        .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicio))
                        .replaceAll(":servicesTheirs", "1");        
            }
        return getUsersComboList(filter, selectedUserId, true);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsUsersByUserDepartments(List<Long> selectedUserIds, Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        String filter = "";
        if (isAdmin) {
            filter = "status = 1";
        } else {
                filter = validEntitiesFilterByBusinessUnitDepartmentUser
                        .replaceAll(":userId", userId + "")
                        .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicio))
                        .replaceAll(":servicesTheirs", "1");
            }
        return getUsersComboList(filter, selectedUserIds, true);
    }
    
     /**
     *  Regresa los usuarios del departamento sleccionado
     *
     * @param businessUnitDepartmentId Id del departamento por el que se filtra
     * @param userId Id del usuario por el que se filtra
     * @param isAdmin Boolean que indica si el usuario por el que se filtra es admin
     * @return Lista de UserRef
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsUsersByDepartment(List<Long> deparmentsListIds, Long userId, Boolean isAdmin, ILoggedUser loggedUser) {
        String filter;
        if (isAdmin) {
            filter = "status = " + User.STATUS.ACTIVE.getValue();
        } else {
            filter = getValidEntitiesFilterByBusinessUnitDepartmentId(deparmentsListIds, loggedUser);
        }
        return getUsersComboList(filter, userId, true);
    }
    
    public static String getValidEntitiesFilterByUser(ILoggedUser loggedUser) {
        return getValidEntitiesFilterByUser(loggedUser.getId());
    }
    
    public static String getValidEntitiesFilterByUser(Long loggedUserId) {
        return getValidEntitiesFilterByUser("" + loggedUserId, ProfileServices.getCodedServices("perfil", new ProfileServices[]{ProfileServices.ALL}), "1");
    }
    
    /**
     * Obtiene la cadena validEntitiesFilterByUser reemplazando los valores, por defecto STATUS = User.STATUS.ACTIVE.
     * @param userId
     * @param servicesMine
     * @param servicesTheirs
     * @return La cadena con valores reemplazados.
     */
    private static String getValidEntitiesFilterByUser(String userId, String servicesMine, String servicesTheirs){
        return HibernateDAO_User.validEntitiesFilterByUser.replaceAll(":userId", userId)
                .replaceAll(":servicesMine", servicesMine)
                .replaceAll(":servicesTheirs", servicesTheirs)
                .replaceAll(":status", "" + User.STATUS.ACTIVE.getValue());
    }
    
    /**
     * Obtiene la cadena validEntitiesFilterByUser reemplazando los valores
     * @param userId
     * @param servicesMine
     * @param servicesTheirs
     * @param status
     * @return 
     */
    private static String getValidEntitiesFilterByUser(String userId, String servicesMine, String servicesTheirs, String status){
        return HibernateDAO_User.validEntitiesFilterByUser.replaceAll(":userId", userId)
                .replaceAll(":servicesMine", servicesMine)
                .replaceAll(":servicesTheirs", servicesTheirs)
                .replaceAll(":status", status);
    }
    
    public static String getValidEntitiesFilterByBusinessUnitDepartmentId(List<Long> deparmentsListIds, ILoggedUser loggedUser) {
        return validEntitiesFilterByBusinessUnitDepartmentId
                .replaceAll(":businessUnitDepartmentsIds", StringUtils.join(deparmentsListIds, ","));
    }
    
    public static String getValidEntitiesFilterByOrganizationUnitUser(ILoggedUser loggedUser) {
        return validEntitiesFilterByOrganizationUnitUser
                            .replaceAll(":userId", loggedUser.getId() + "")
                            .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", new ProfileServices[]{ProfileServices.ALL}))
                            .replaceAll(":servicesTheirs", "1");       
    }
    
    /**
     *  Regresa los usuarios de las unidades de negocio a las que pertence el usuario por el que se filtra
     *
     * @param selectedUserId Valor que debe estar por default en los resultados
     * @param onlyActiveUsers Boolean que determina si se filtran solo los usuarios activos
     * @param isAdmin Boolean que indica si el usuario por el que se filtra es admin
     * @param userId Id del usuario por el que se filtra
     * @param servicio  Servicios del usuario por el que se filtra
     * @return Lista de UserRef
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserRef> getUsersByUserDepartments(Long selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio, Boolean onlyActiveUsers) {
        String filter = onlyActiveUsers ? "c.status = " + User.ACTIVE_STATUS : "1=1";
        if (!isAdmin) {
            filter = validEntitiesFilterByBusinessUnitDepartmentUser
                    .replaceAll(":userId", userId + "")
                    .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicio))
                    .replaceAll(":servicesTheirs", "1") + " AND " + filter;        
        }
        if (selectedUserId != null) {
            filter = "c.id = " + selectedUserId + " OR (" + filter + ")";
        }
        return HQLT_findByQueryFilter(UserRef.class, filter);
    }

    
    /**
     *  Regresa los usuarios de las unidades de negocio a las que pertence el usuario por el que se filtra
     *
     * @param selectedUserId Valor que debe estar por default en los resultados
     * @param isAdmin Boolean que indica si el usuario por el que se filtra es admin
     * @param userId Id del usuario por el que se filtra
     * @param servicio  Servicios del usuario por el que se filtra
     * @return Lista de UserRef
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsUsersByUserOrganizationUnits(Long selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        String filter = "";
        if (!isAdmin) {
            filter = validEntitiesFilterByOrganizationUnitUser
                    .replaceAll(":userId", userId + "")
                    .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicio))
                    .replaceAll(":servicesTheirs", "1");        
        }
        return getUsersComboList(filter, selectedUserId, true);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getStrutsUsersByUserOrganizationUnits(List<Long> selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio) {
        String filter = "";
        if (!isAdmin) {
            filter = validEntitiesFilterByOrganizationUnitUser
                    .replaceAll(":userId", userId + "")
                    .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicio))
                    .replaceAll(":servicesTheirs", "1");        
        }
        return getUsersComboList(filter, selectedUserId, true);
    }
    
    /**
     *  Regresa los usuarios de las unidades de negocio a las que pertence el usuario por el que se filtra
     *
     * @param selectedUserId Valor que debe estar por default en los resultados
     * @param onlyActiveUsers Boolean que determina si se filtran solo los usuarios activos
     * @param isAdmin Boolean que indica si el usuario por el que se filtra es admin
     * @param userId Id del usuario por el que se filtra
     * @param servicio  Servicios del usuario por el que se filtra
     * @return Lista de UserRef
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserRef> getUsersByUserOrganizationUnits(Long selectedUserId, Long userId, Boolean isAdmin, ProfileServices[] servicio, Boolean onlyActiveUsers) {
        String filter = onlyActiveUsers ? "c.status = " + User.ACTIVE_STATUS : "1=1";
        if (!isAdmin) {
            filter = validEntitiesFilterByOrganizationUnitUser
                    .replaceAll(":userId", userId + "")
                    .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicio))
                    .replaceAll(":servicesTheirs", "1") + " AND " + filter;        
        }
        if (selectedUserId != null) {
            filter = "c.id = " + selectedUserId + " OR (" + filter + ")";
        }
        return HQLT_findByQueryFilter(UserRef.class, filter);
    }

    /**
     * Función que devuelve una lista de id´s separados por coma de los usuarios que pertenecen a una unidad de negocio
     *
     * @param buId lista de unidades de negocio separadas por coma
     * @return lista de usuarios que existen en la unidad de negocio
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByBusinessUnitIds(String buId) {
        List<UserRef> lstusr = getUserByBusinessUnit(buId);

        StringBuilder strUsr = new StringBuilder("");
        for (UserRef UIN : lstusr) {
            strUsr.append(UIN.getId());
            strUsr.append(",");
        }
        if (strUsr.length() > 0) {
            return strUsr.substring(0, strUsr.length() - 1);
        }
        return strUsr.toString();

    }

    /**
     * Función que devuelve una lista de usuarios que estan asignados a la lista de unidades de negocio (separadas por
     * coma)
     *
     * @param buId - lista de unidades de negocio separadas por coma
     * @return - Lista de usuarios que pertencen a las unidades de negocio en cuestión
     *
     * @since 2.3.2.38
     * <AUTHOR> Lares Lares
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserRef> getUserByBusinessUnit(String buId) {
        return getUserByBusinessUnit(buId, false);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserRef> getUserByBusinessUnit(String buId, boolean isAdmin) {
        getLogger().trace("getUserByBusinessUnit ...");
        String query;
        query = ""
                + " SELECT distinct "
                + " new bnext.reference.UserRef(c.id,c.description,c.status)"
                + " FROM DPMS.Mapping.User c "
                + " JOIN c.puestos as p "
                + (isAdmin
                    ?""
                    :" WHERE p.une.id IN(" + Utilities.safeIN(buId) + ") ")
                + " ORDER BY c.description";
        return HQL_findByQuery(query);
    }
    
    /**
     * Devuelve una lista de los usuarios activos que estan asignados a la lista de unidades de negocio
     *
     * @param buId - lista de unidades de negocio separadas por coma
     * @param isAdmin - marca si el usuario es administrador
     * @return una lista de los usuarios activos
     *
     * @since *******
     * <AUTHOR> Contreras Chaparro
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class}) 
    public List<UserRef> getActiveUserByBusinessUnit(String buId, boolean isAdmin) {
        getLogger().trace("getActiveUserByBusinessUnit ...");
        String query;
        query = ""
                + " SELECT DISTINCT new " + UserRef.class.getCanonicalName() + "("
                    + " c.id,"
                    + " c.description,"
                    + " c.status"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " JOIN c.puestos as p "
                + (isAdmin
                    ?" WHERE c.status = 1"
                    :" WHERE p.une.id IN(" + Utilities.safeIN(buId) + ") AND c.status = 1")
                + " ORDER BY c.description";
        return HQL_findByQuery(query);
    }
    
    /**
     * Devuelve una lista de id's de las unidades de negocio asociadas al usuario.
     *
     * @param userId - El usuario con el que va a filtrar
     * @param isAdmin - marca si el usuario es administrador
     * @param isCorporative - Indica si es usuario corporativo, para que muestre todas las unidades de negocio en su organización
     * @return un lista de id's de unidades de negocio separadas por coma
     * @deprecated  Usar getBusinessUnitsForUser dentro de un exists
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getBusinessUnitsIds(String userId, Boolean isAdmin, Boolean isCorporative) {
        getLogger().warn("This function has been deprecated, please change it for getBusinessUnitsForUser");
        if(!Utilities.isInteger(userId)) {
            return "0";
        }
        List<Long> lstBUn = getBusinessUnits(new Long(userId), isAdmin, isCorporative);
        StringBuilder strBUn = new StringBuilder("");
        for (Long BUn : lstBUn) {
            strBUn.append(BUn);
            strBUn.append(",");
        }
        if (strBUn.length() > 0) {
            return strBUn.substring(0, strBUn.length() - 1);
        }
        return strBUn.toString().length() > 0 ? strBUn.toString() : "0";
    }
    
    
    /**
     * Devuelve una lista de id's de los departamentos asociados al usuario.
     *
     * @param userId - El usuario con el que va a filtrar
     * @param isAdmin - marca si el usuario es administrador
     * @param isDepartmentManager Bandera que indica si se requieren los departamentos en que el usuario es encargado
     * @param isCorporative - Indica si es usuario corporativo, para que muestre todas las unidades de negocio en su organización
     * @return un lista de id's de departamentos separados por coma
     * @since 2.3.2.173
     * <AUTHOR> Guadalupe Quintanilla Flores
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getDepartmentsIds(String userId, Boolean isAdmin, Boolean isDepartmentManager, Boolean isCorporative) {
        List<BusinessUnitDepartmentLite> lstDep = getDepartments(userId, isAdmin, false, isDepartmentManager, isCorporative);
        StringBuilder strDep = new StringBuilder("");
        for (BusinessUnitDepartmentLite dep : lstDep) {
            strDep.append(dep.getId());
            strDep.append(",");
        }
        if (strDep.length() > 0) {
            return strDep.substring(0, strDep.length() - 1);
        }
        return strDep.toString();
    }

    /**
     * Función que devuelve una lista de unidades de negocio que el usuario tiene asignado apartir de su puesto
     *
     * @param userId - El usuario del que queremos obtener las unidades de negocio
     * @param isAdmin - Indica si es administrador general, para que muestre todas las unidades de negocio
     * @param isCorporative - Indica si es usuario corporativo, para que muestre todas las unidades de negocio en su organización
     * @return - Lista de unidades de negocio que el usuario tiene asignado
     *
     * @since 2.3.2.38
     * <AUTHOR> Lares Lares
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> getBusinessUnits(Long userId, Boolean isAdmin, Boolean isCorporative) {
        String query;
        if (isAdmin) {
            query = ""
                    + " SELECT c.id "
                    + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                    + " WHERE c.deleted = 0";
            return HQL_findByQuery(query);
        } else if (isCorporative) {
            query = ""
                    + " SELECT DISTINCT bun.id "
                    + " FROM " + User.class.getCanonicalName() + " usr "
                    + " , " + BusinessUnit.class.getCanonicalName() + " bun "
                    + " JOIN usr.puestos as pst "
                    + " LEFT JOIN pst.corp as org"
                    + " LEFT JOIN pst.une as bu"
                    + " WHERE usr.id = :userId"
                    + " AND bun.deleted = 0"
                    + " AND ("
                        + " ("
                            + "pst.perfil.intBUsuarioPlanta = 1"
                            + " AND bu.id = bun.id"
                        + " )"
                        + " OR ("
                            + "pst.perfil.intBUsuarioCorporativo = 1"
                            + " AND org.id = bun.organizationalUnitId"
                        + " )"
                    + ")";
            return HQL_findByQuery(query, ImmutableMap.of("userId", userId));
        } else {
            query = ""
                    + " SELECT DISTINCT bun.id "
                    + " FROM " + User.class.getCanonicalName() + " usr "
                    + " JOIN usr.puestos as pst "
                    + " JOIN pst.une as bun"
                    + " WHERE usr.id = :userId"
                    + " AND bun.deleted = 0 ";
            return HQL_findByQuery(query, ImmutableMap.of("userId", userId));
        }
    }
    
    /**
     * Función que devuelve el query para obtener las unidades de negocio asociadas a un usario
     *
     * @param userId El usuario del que queremos obtener las unidades de negocio
     * @param isAdmin bandera que determina si el usuario es root
     * @param businessUnitId condición contra la que vamos a comparar el where
     * @return unu string con el query 
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getBusinessUnitsForUser(String userId, Boolean isAdmin, String businessUnitId) {
        String query;
        if (isAdmin) {
            query = " 1 = 1";
        } else {
            query = ""
                //usuarios con perfil planta
                + " exists ("
                    + " SELECT bun.id "
                    + " FROM " + User.class.getCanonicalName() + " usr "
                    + " JOIN usr.puestos as pst "
                    + " JOIN pst.une as bun"
                    + " WHERE usr.id IN ( " + userId + ")"
                    + " AND bun.id IN ( " + businessUnitId + ")"
                    + " AND bun.deleted = 0 "
                + " )"
                //usuarios con perfil corporativo
                + " OR exists ("
                    + " SELECT bun.id "
                    + " FROM " + User.class.getCanonicalName() + " usr "
                    + " JOIN usr.puestos pst "
                    + " JOIN pst.perfil perfil "
                    + " JOIN pst.corp corp, " + BusinessUnit.class.getCanonicalName() + " bun"
                    + " WHERE usr.id IN (" + userId + ")"
                    + " AND bun.organizationalUnitId = corp.id"
                    + " AND perfil.intBUsuarioCorporativo = " + Profile.ACTIVE_STATUS
                    + " AND bun.id IN ( " + businessUnitId + ")"
                    + " AND bun.deleted = 0 "
                + " )";
        }
        return query;
    }
    
    /**
     * Función que devuelve el query para validar si un usuario tiene asociado un departamento/planta
     *
     * @param userId El usuario del que queremos obtener los departamentos
     * @param isAdmin bandera que determina si el usuario es root
     * @param businessUnitDepartmentId condición contra la que vamos a comparar el where
     * @return unu string con el query 
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getBusinessUnitDepartmentsForUser(String userId, Boolean isAdmin, String businessUnitDepartmentId) {
        String query;
        if (isAdmin) {
            query = " 1 = 1";
        } else {
            query = ""
                + " exists ("
                    + " SELECT 1 "
                    + " FROM " + User.class.getCanonicalName() + " usr "
                    + " LEFT JOIN usr.puestos as pst "
                    + " LEFT JOIN pst.departamentos as dep"
                    + " WHERE usr.id IN ( " + userId + ")"
                    + " AND ("
                        + " usr.businessUnitDepartmentId = " + businessUnitDepartmentId
                        + " OR dep.id = " + businessUnitDepartmentId
                    + " )"
                + " )";
        }
        return query;
    }
    
    private String getDepartmentsQuery(
            Long loggedUserId,
            Boolean isAdmin, 
            Boolean isDocumentManager, 
            Boolean isDepartmentManager,
            Boolean isCorporative
    ) {
        String query;
        if (isAdmin) {
            query = ""
                + " FROM " + BusinessUnitDepartmentLite.class.getCanonicalName() + " c "
                + " WHERE c.status = " + BusinessUnitDepartmentLite.STATUS.ACTIVE.getValue() 
                + " OR c.id = :businessUnitDepartmentId";
        } else if (isDocumentManager) {
            query = " FROM " + BusinessUnitDepartmentLite.class.getCanonicalName() + " c "
                    + " WHERE c.id = :businessUnitDepartmentId"
                    + " OR ("
                        + " c.status = " + BusinessUnitDepartmentLite.STATUS.ACTIVE.getValue()
                        + " AND c.businessUnitId IN (" + getBusinessUnitsIds(loggedUserId.toString(), isAdmin, isCorporative) + ")"
                    + ")";
        } else if (isDepartmentManager) {
            query = " FROM " + BusinessUnitDepartmentLite.class.getCanonicalName() + " c "
                    + " WHERE c.attendantId = :loggedUserId"
                    + " AND c.status = " + BusinessUnitDepartmentLite.STATUS.ACTIVE.getValue();
        } else {
            query = " FROM " + User.class.getCanonicalName() + " usr "
                    + " JOIN usr.puestos as pst "
                    + " JOIN pst.departamentos as c "
                    + " WHERE c.id = :businessUnitDepartmentId"
                    + " OR ("
                        + " usr.id = :loggedUserId"
                        + " AND c.status = " + BusinessUnitDepartmentLite.STATUS.ACTIVE.getValue()
                    + " )";
        }
        return query;
    }
    
    /**
     * Función que devuelve una lista de departamentos que el usuario tiene asigando apartir de su puesto
     *
     * @param userId El usuario del que queremos obtener los departamentos
     * @param isAdmin Bandera que indica si se trata de un usuario root
     * @param isDocumentManager Bandera que indica si se trata de un usuario encargado de documentos
     * @param isDepartmentManager Bandera que indica si se requieren los departamentos en que el usuario es encargado
     * @param isCorporative - Indica si es usuario corporativo, para que muestre todas las unidades de negocio en su organización
     * @return Lista de departamentos que el usuario tiene asignado
     *
     * @since 2.3.2.38
     * <AUTHOR> Lares Lares
     * 
     * @modified Se agrega parámetro isDocumentManager para obtener la lista completa de departamentos del usuario
     * encargado de documentos (@since *********)
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<BusinessUnitDepartmentLite> getDepartments(
            String userId,
            Boolean isAdmin,
            Boolean isDocumentManager,
            Boolean isDepartmentManager,
            Boolean isCorporative
    ) {
        final String departmentsQuery = getDepartmentsQuery(
                Long.parseLong(userId),
                isAdmin,
                isDocumentManager, 
                isDepartmentManager, 
                isCorporative
        );
        final Map<String, Object> params = new HashMap<>();
        if(!isDepartmentManager){
            params.put("businessUnitDepartmentId", -1L);
        }
        if(departmentsQuery.contains(":loggedUserId")){
            params.put("loggedUserId", Long.parseLong(userId));
        }
        return HQL_findByQuery(""
                + " SELECT distinct c " + departmentsQuery, params);
    }
    
    /**
     * Sobrecarga de función getDepartments(String, Boolean, Boolean) para conservar la compatibilidad con los usos que
     * se hacían de la misma.
     * 
     * @param userId Usuario en base al cuál se obtendran los departamentos
     * @param isAdmin Bandera que indica si se trata de un usuario root
     * @param isCorporative - Indica si es usuario corporativo, para que muestre todas las unidades de negocio en su organización
     * @return Lista de departamentos
     * 
     * @since *********
     * <AUTHOR> Cavazos Galindo
     * @see HibernateDAO_User#getDepartments(String, Boolean, Boolean)
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<BusinessUnitDepartmentLite> getDepartments(String userId, boolean isAdmin, boolean isCorporative) {
        return getDepartments(userId, isAdmin, false, false, isCorporative);
    }

    /**
     * Función para obtener todos los departamentos de un usuario que sea "encargado de documentos por unidad de
     * negocio" ó "encargado de documentos por departamento"
     * 
     * @param businessUnitDepartmentId
     * @param loggedUserId Usuario en base al cuál se obtendran los departamentos
     * @param isAdmin Bandera que indica si se trata de un usuario root
     * @param services
     * @param isCorporative - Indica si es usuario corporativo, para que muestre todas las unidades de negocio en su organización
     * @return Lista de departamentos
     * 
     * @since *********
     * <AUTHOR> Cavazos Galindo
     * @see HibernateDAO_User#getDepartments(String, Boolean, Boolean)
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<BusinessUnitDepartmentDTO> getDepartmentsBySpecialService(Long businessUnitDepartmentId, Long loggedUserId, boolean isAdmin, List<ProfileServices> services, boolean isCorporative) {
        Boolean isDocumentManager = false;
        if (services.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT) 
            || services.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT)) {
            isDocumentManager = true;
        }
        String query = getDepartmentsQuery(loggedUserId, isAdmin, isDocumentManager, false, isCorporative);
        final Map<String, Object> params = new HashMap<>();
        params.put("businessUnitDepartmentId", businessUnitDepartmentId);
        if(query.contains(":loggedUserId")){
            params.put("loggedUserId", loggedUserId);
        }
        
        List<BusinessUnitDepartmentDTO> list = HQL_findByQuery(""
                + " SELECT distinct new qms.survey.dto.BusinessUnitDepartmentDTO( "
                + " c.id as id, "
                + " c.description as description, "
                + " c.formTemplateId as formTemplateId, "
                + " c.departmentFormTemplateId as departmentFormTemplateId "
                + " ) " + query, params);
        for (BusinessUnitDepartmentDTO businessUnitDepartmentDTO : list) {
            if (businessUnitDepartmentDTO.getFormTemplateId() == null) {
                businessUnitDepartmentDTO.setFormTemplateId(businessUnitDepartmentDTO.getDepartmentFormTemplateId());
            }
        }
        return list;
    }
    
    /**
     * Función para obtener la información del departamento que el usuario tiene asignado
     * 
     * @param loggedUserId
     * @return Departamento asociado al usuario
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public BusinessUnitDepartmentDTO getDepartmentsLoggedUser(Long loggedUserId) {
        BusinessUnitDepartmentDTO department = (BusinessUnitDepartmentDTO) HQL_findSimpleObject(""
                + " SELECT new qms.survey.dto.BusinessUnitDepartmentDTO( "
                + " c.id as id, "
                + " c.description as description, "
                + " c.formTemplateId as formTemplateId, "
                + " c.departmentFormTemplateId as departmentFormTemplateId "
                + " ) " 
                + " FROM " + User.class.getCanonicalName() + " usr "
                    + " JOIN usr.businessUnitDepartment as c "
                    + " WHERE usr.id = " + loggedUserId + " "
                        + " AND c.status = " + BusinessUnitDepartmentLite.STATUS.ACTIVE.getValue()
                );
            if (department != null && department.getFormTemplateId() == null) {
                department.setFormTemplateId(department.getDepartmentFormTemplateId());
            }
        return department;
    }

    /**
     * Pendiente de documentar
     *
     * @param deptId
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserRef> getUserByDepartment(String deptId) {
        String query;
        query = " SELECT DISTINCT c FROM DPMS.Mapping.User c "
                + " JOIN c.puestos as p "
                + " JOIN p.departamentos as dept "
                + " WHERE dept.id IN(" + Utilities.safeIN(deptId) + ") ";
        return HQL_findByQuery(query);
    }

    /**
     * Pendiente de documentar
     *
     * <code>order</code> defaults to null.
     *
     * @return 
     * @see HibernateDAO_User#getFilteredEntity(boolean, SortedPagedFilter, String, ProfileServices[], String)
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<User> getFilteredEntity(boolean isAdmin, SortedPagedFilter filter, String intusuarioid, ProfileServices[] servicio) {
        return getFilteredEntity(isAdmin, filter, intusuarioid, servicio, null);
    }

    /**
     * Pendiente de documentar
     *
     * @param isAdmin
     * @param filter
     * @param intusuarioid
     * @param servicio
     * @param order
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<User> getFilteredEntity(boolean isAdmin, IGridFilter filter, String intusuarioid, ProfileServices[] servicio, String order) {
        List<User> r = new ArrayList<User>();
        try {
            filter.setPageSize(-1);
            filter.getCriteria().put("deleted", "0");
            filter.getCriteria().put("status", "1");
            r = HQLT_findByPagedFilter(filter);
        } catch (Exception e) {
            getLogger().error("DPMS.DAO.HibernateDAO_User.getFilteredEntity() ... {}",new Object[]{
                isAdmin, filter, intusuarioid, servicio, order
            }, e);
        }
        getLogger().trace("Sale filtering... se obtuvieron " + r.size() + " valores filtrados.");
        return r;
    }

    /**
     * Pendiente de documentar
     *
     * @param prcId
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByProcess(String prcId) {
        String filter;
        if (!(prcId.equals("") || prcId.equals("0"))) {
            filter = ""
                    + " exists ( "
                    + " SELECT u.id "
                    + " FROM DPMS.Mapping.User u "
                    + " JOIN u.puestos as p "
                    + " JOIN p.procesos as pro "
                    + " WHERE pro.id IN(" + Utilities.safeIN(prcId) + ") "
                    + " AND c.id = u.id "
                    + " ) ";
        } else {
            filter = "1=0";
        }
        return Utilities.comboParser(getStrutsComboList(filter));
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByDepartmentProcess(String prcId, String businessUnit,ProfileServices[] services) {
        String filter;
        if (!prcId.isEmpty()) {
            boolean userServices = services != null && services.length > 0;
                filter = ""
                        + " exists ( "
                        + " SELECT u.id "
                        + " FROM DPMS.Mapping.User u "
                        + " JOIN u.puestos as p "
                        + " JOIN p.departmentProcess as pro "
                        + (userServices ? " JOIN p.perfil AS per " : "")
                        + (prcId.equals("0")?" WHERE 1=1 ":" WHERE pro.processId IN(" + Utilities.safeIN(prcId) + ") ")
                        + " AND pro.department.businessUnitId IN(" + Utilities.safeIN(businessUnit) + ") "
                        + ( userServices? " AND 1 IN ("+ProfileServices.getCodedServices("per", services)+")" : "")
                        + " AND c.id = u.id "
                        + " ) ";
        } else {
            filter = "1=0";
        }
        return Utilities.comboParser(getStrutsComboList(filter), prcId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByDepartmentProcess(String prcId, String businessUnit) {
        return getUserByDepartmentProcess(prcId,businessUnit,null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserInProcessByBusinessUnits(Long processId,String businessUnits){
        return getUserByDepartmentProcess(processId.toString(),businessUnits,null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserInProcessByBusinessUnits(Long processId,String businessUnits,ProfileServices[] services){
        return getUserByDepartmentProcess(processId.toString(),businessUnits,services);
    }

    /**
     * Pendiente de documentar
     *
     * @param prcId
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByProcess2(String prcId) {
        String filter;
        if (!(prcId.equals("") || prcId.equals("0"))) {
            filter = ""
                    + " exists ( "
                    + " SELECT u.id "
                    + " FROM DPMS.Mapping.User u "
                    + " JOIN u.puestos as p "
                    + " JOIN p.procesos as pro "
                    + " WHERE pro.id IN(" + Utilities.safeIN(prcId) + ") "
                    + " AND c.id = u.id "
                    + ") ";
        } else {
            filter = "0=0";
        }
        return Utilities.comboParser(getStrutsComboList(filter));
    }

    /**
     * Pendiente de documentar
     *
     * @param intubicacionid
     * @param selected
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByDepartment(String intubicacionid, String selected) {
        String filter;
        if (!(intubicacionid.equals("") || intubicacionid.equals("0"))) {
            filter = USER_BY_BUSINESS_UNIT_DEPARTMENT.replace(":businessUnitDepartmentIds", Utilities.safeIN(intubicacionid));
        } else {
            filter = "1=0";
        }
        List<UserHasValue> users = getUsersComboList(filter, -1L, true);
        // This is backward compatibility, please, someday destroy A1 modules
        if(!selected.equals("0") && !selected.isEmpty()){
            UserRef selUser = HQLT_findById(UserRef.class, Long.parseLong(selected));
            UserHasValue selectedUser = new UserHasValue(selUser.getDescription(),selUser.getId());
            if  (!users.contains(selectedUser)) {
                users.add(selectedUser);
            }
        }
        return Utilities.comboParser(users, selected);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByBusinessUnitByNOTProcess(String prcId, String intfacultadid) {
        return getUserByBusinessUnitByNOTProcess(prcId, intfacultadid, "");
    }

    /**
     * Pendiente de documentar
     *
     * @param prcId
     * @param intfacultadid
     * @param selected
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByBusinessUnitByNOTProcess(String prcId, String intfacultadid, String selected) {
        String filter;
        if (!(prcId.equals("") || prcId.equals("0"))) {
            filter = "c.id NOT IN( "
                    + "SELECT DISTINCT c.id "
                    + "FROM c.puestos as p "
                    + "JOIN p.procesos as pro "
                    + "WHERE pro.id IN(" + Utilities.safeIN(prcId) + ") "
                    + ") "
                    + " AND exists ( "
                    + " SELECT c.id "
                    + " FROM c.puestos as p "
                    + " WHERE p.une.id = " + intfacultadid
                    + ") ";
        } else {
            filter = "0=0";
        }
        return Utilities.comboParser(getStrutsComboList(filter), selected);
    }

    /**
     * Regresa los usuarios que estan en la(s) unidades de negocio de "intusuarioid" del parametro
     *
     * @param intusuarioid
     * @param selected
     * @return
     * @deprecated Utilizar getUsersInBusinessUnitOf
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUserByBusinessUnit(String intusuarioid, String selected) {
        String filter = ""
                + " exists ( "
                + " SELECT u.id FROM "
                + " DPMS.Mapping.User as u "
                + " JOIN u.puestos as p "
                + " JOIN p.perfil as perfil "
                + " WHERE exists ( "
                + " SELECT p.une.id "
                + " FROM DPMS.Mapping.User as u "
                + " JOIN u.puestos as p "
                + " JOIN p.perfil as perfil "
                + " WHERE u.id = " + intusuarioid
                + " ) AND c.id = u.id "
                + " )";
        return Utilities.comboParser(getStrutsComboList(filter), selected);
    }

    private Set<ProfileServices> getAllUserServices(final Long userId) {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Set<ProfileServices> services = new HashSet<>();
            if (userId.equals(0L)) {
                getLogger().trace("DPMS.DAO.HibernateDAO_User.getAllUserServices() --> > > Sesión caducada < < <-- ");
                return services;
            }
            final String query = ""
                    + " SELECT profile "
                    + " FROM " + User.class.getCanonicalName() + " as user  "
                    + " JOIN user.puestos as position "
                    + " JOIN position.perfil as profile "
                    + " WHERE user.id = :userId";
            if (getLogger().isDebugEnabled()) {
                getLogger().debug("QP: {}", query);
            }
            final List<Profile> results = HQL_findByQuery(query, "userId", userId);
            if (getLogger().isTraceEnabled()) {
                getLogger().trace("Son {} perfiles.", results.size());
            }
            results.forEach(result -> {
                final Set<ProfileServices> servicios = ProfileServicesUtil.getServicios(result);
                services.addAll(servicios);
            });
            if (getLogger().isTraceEnabled()) {
                getLogger().trace(
                    ">>>> El usuario {} cuenta con {}",
                    new Object[] {
                        userId, services.size()
                    }
                );
            }
            return services;
        } finally {
            MeasureTime.stop(tStart, "Elapsed time loading all services for user " + userId);
        }
    }

    private Set<ProfileServices> getUserServicesByBusinessUnit(final Long userId, final Long businessUnitId) {
        final Set<ProfileServices> services = new HashSet<>();
        if (userId.equals(0L)) {
            getLogger().trace("DPMS.DAO.HibernateDAO_User.getUserServicesByBusinessUnit() --> > > Sesión caducada < < <-- ");
            return services;
        }
        final String query = ""
                + " SELECT profile "
                + " FROM " + User.class.getCanonicalName() + " as user  "
                + " JOIN user.puestos as position "
                + " JOIN position.perfil as profile "
                + " LEFT JOIN position.une businessUnit "
                + " LEFT JOIN position.corp organization "
                + " WHERE user.id = :userId  "
                + " AND ("
                    + " (businessUnit.id = :businessUnitId)"
                    + " OR ("
                        + " profile.intBUsuarioCorporativo = 1"
                        + " AND organization.id IN ("
                            + " SELECT otherOrg.id"
                            + " FROM " + BusinessUnit.class.getCanonicalName() + " otherBu"
                            + " JOIN otherBu.organizationalUnit otherOrg"
                            + " WHERE otherBu.id = :businessUnitId"
                        + ")"
                    + ") "
                + " )";
        if (getLogger().isDebugEnabled()) {
            getLogger().debug("QP: {}", query);
        }
        final Map<String, Object> params = new HashMap<>(2);
        params.put("userId", userId);
        params.put("businessUnitId", businessUnitId);
        final List<Profile> results = HQL_findByQuery(query, params);
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("Son {} perfiles.", results.size());
        }
        results.forEach(result -> {
            final Set<ProfileServices> servicios = ProfileServicesUtil.getServicios(result);
            services.addAll(servicios);
        });
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(
                ">>>> El usuario {} cuenta con {} servicios normales (une: {})",
                new Object[] {
                    userId, services.size(), businessUnitId
                }
            );
        }
        return services;
    }
    
    /**
     * Pendiente de documentar
     *
     * @param userId
     * @param id
     * @param uneId
     * @return
     *
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Set<ProfileServices> getUserServicesByUne(Long userId, Long uneId) {
        if (userId == 0) {
            return Utilities.EMPTY_SET;
        }
        Set<ProfileServices> s = getUserServicesByBusinessUnit(userId, uneId);
        s.addAll(getUserSpecialServices(userId));
        /**
         * ToDo!! Los servicios especiales de actividades deben ir siempre al final debido a
         * que considera los anteriores para obtener un servicio especial
         * */
        final Set<ProfileServices> servicesAddedBySystem = new HashSet<>();
        getUserSpecialServices_Clones(userId, s, servicesAddedBySystem);
        s.addAll(servicesAddedBySystem);
        return s;
    }

    /**
     * Pendiente de documentar
     *
     * @param userId
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ServicesDTO getUserServices(final Long userId, final Boolean isAdmin) {
        final ServicesDTO services = new ServicesDTO();
        if (userId == 0) {
	    services.setUserServices(Utilities.EMPTY_SET);
            services.setUserServicesAddedBySystem(Utilities.EMPTY_SET);
            return services;
        }
        if (Boolean.TRUE.equals(isAdmin)) {
            Set<ProfileServices> s = new HashSet<>(ProfileServicesUtil.getAllServices());
            s.remove(ProfileServices.DOCUMENT_STARTED_AT_DOCUMENT_VIEWER);
            services.setUserServices(s);
            services.setUserServicesAddedBySystem(Utilities.EMPTY_SET);
            return services;
        }
        Set<ProfileServices> s = getAllUserServices(userId);
        if (!s.contains(ProfileServices.SPECIAL_ACTIVITY_SUBTASK_CREATOR) 
                && ProfileServicesUtil.hasAnyProfileService(s, ProfileServices.getServicesByModule(Module.ACTIVITY))) {
            s.add(ProfileServices.SPECIAL_ACTIVITY_SUBTASK_CREATOR); 
        }
        if (!s.contains(ProfileServices.SPECIAL_ACTIVITY_SUBTASK_CREATOR_PLANNER)
                && ProfileServicesUtil.hasAnyProfileService(s, ProfileServices.getServicesByModule(Module.PLANNER))) {
            s.add(ProfileServices.SPECIAL_ACTIVITY_SUBTASK_CREATOR_PLANNER); 
        }
        s.addAll(getUserSpecialServices(userId));
        /**
         * ToDo!! Los servicios especiales de actividades deben ir siempre al final debido a
         * que considera los anteriores para obtener un servicio especial
         * */
        final Set<ProfileServices> servicesAddedBySystem = new HashSet<>();
        getUserSpecialServices_Clones(userId, s, servicesAddedBySystem);
        services.setUserServices(s);
        services.setUserServicesAddedBySystem(servicesAddedBySystem);
        return services;
    }

    /**
     * Pendiente de documentar
     *
     * @param id
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    private Set<ProfileServices> getUserSpecialServices(Long id) { 
        return getUserSpecialServicesByUne(id, null);
    }

    /**
     * Aqui agregar cualquier servicio especial extra
     *
     * @param userId
     * @param uneId
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    private Set<ProfileServices> getUserSpecialServicesByUne(Long userId, Long uneId) {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final Set<ProfileServices> s = new HashSet();
            
            final ElapsedDataDTO auditStart = MeasureTime.start(getClass());
            getUserSpecialServices_AuditByUne(userId, s, uneId);
            MeasureTime.stop(
                    auditStart, 
                    "Elapsed time loading user audit special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO depStart = MeasureTime.start(getClass());
            getUserSpecialServices_DepartmentAttendantByUne(userId, s, uneId);
            MeasureTime.stop(
                    depStart, 
                    "Elapsed time loading user department attentant special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO complaintStart = MeasureTime.start(getClass());
            getUserSpecialServices_ComplaintsDepartmentAttendantByUne(userId, s, uneId);
            MeasureTime.stop(
                    complaintStart, 
                    "Elapsed time loading user complaints department attendants special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO docStart = MeasureTime.start(getClass());
            getUserSpecialServices_DepartmentDocumentManagerByUne(userId, s, uneId);
            MeasureTime.stop(
                    docStart, 
                    "Elapsed time loading user department document manager special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO docDepStart = MeasureTime.start(getClass());
            getUserSpecialServices_BusinessUnitDocumentManagerByUne(userId, s, uneId);
            MeasureTime.stop(
                    docDepStart, 
                    "Elapsed time loading user business unit document manager special services by une"
                            + " for user " + userId + " and business unit " + uneId);
            
            final ElapsedDataDTO formStart = MeasureTime.start(getClass());
            getUserSpecialServices_BusinessUnitDepartmentFormApproverByUne(userId, s, uneId);
            MeasureTime.stop(
                    formStart, 
                    "Elapsed time loading user business unit department form approver special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO orgStart = MeasureTime.start(getClass());
            getUserSpecialServices_OrganizationalUnitDocumentManagerByUne(userId, s, uneId);
            MeasureTime.stop(
                    orgStart, 
                    "Elapsed time loading user  organization unit document manager  special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO docFillStart = MeasureTime.start(getClass());
            getUserSpecialServices_DocumentFillFormByUne(userId, s, uneId);
            MeasureTime.stop(
                    docFillStart, 
                    "Elapsed time loading user document fill form special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO readerStart = MeasureTime.start(getClass());
            getUserSpecialServices_DocumentReaderByUne(userId, s, uneId);
            MeasureTime.stop(
                    readerStart, 
                    "Elapsed time loading user document reader special services by une"
                            + " for user " + userId + " and business unit " + uneId);
            
            final ElapsedDataDTO pollStart = MeasureTime.start(getClass());
            getUserSpecialServices_PollRespondentByUne(userId, s, uneId);
            MeasureTime.stop(
                    pollStart, 
                    "Elapsed time loading user poll respondent special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO docAuthStart = MeasureTime.start(getClass());
            getUserSpecialServices_DocumentAuthorizer(userId, s, uneId);
            MeasureTime.stop(
                    docAuthStart, 
                    "Elapsed time loading user document authorizer special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO meterStart = MeasureTime.start(getClass());
            getUserSpecialServices_MeterReviewRespondent(userId, s);
            MeasureTime.stop(
                    meterStart, 
                    "Elapsed time loading user meter reviewer special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            final ElapsedDataDTO editProfileStart = MeasureTime.start(getClass());
            getUserSpecialServices_AccessEditProfile(userId, s, uneId);
            MeasureTime.stop(
                    editProfileStart, 
                    "Elapsed time loading user acess edit profile special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );

            final ElapsedDataDTO anonymousStart = MeasureTime.start(getClass());
            getUserSpecialServices_AnonymousUser(userId, s, uneId);
            MeasureTime.stop(
                    anonymousStart,
                    "Elapsed time loading user acess anonymous special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );

            /**
             * ToDo!! Los servicios especiales de actividades deben ir siempre al final debido a 
             * que considera los anteriores para obtener un servicio especial
             * */
            getUserSpecialServices_Activity(userId, s, uneId);
            MeasureTime.stop(
                    editProfileStart, 
                    "Elapsed time loading user activity special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
            
            // NO AGREGAR SERVICIOS EN ESTA LINEA //
            getLogger().trace(">>>> El usuario " + userId + " cuenta con " + s.size() + " servicios especiales.");
            return ProfileServicesUtil.getRemovedUnavailableModulesServices(s);
        } finally {
            MeasureTime.stop(
                    tStart, 
                    "Elapsed time loading user special services by une"
                            + " for user " + userId + " and business unit " + uneId
            );
        }
    }

    /**
     * Pendiente de documentar
     *
     * @param userId
     * @param s
     * @param uneId
     * @return
     *
     * @since ********
     * <AUTHOR> Staff
     */
    private boolean getUserSpecialServices_AuditByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_AUDIT_AUDITED.getModule()) 
                || s.contains(ProfileServices.SPECIAL_AUDIT_AUDITED)) {
            return true;
        }
        
        if (isProcessAudited(userId, s, uneId) || isAreaAudited(userId, s, uneId)) {
            s.add(ProfileServices.SPECIAL_AUDIT_AUDITED);
        }
        
        return true;
    }
    
    private boolean isProcessAudited(Long userId, Set<ProfileServices> s, Long uneId) {
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        String query = ""
                + " SELECT 1"
                + " FROM " + DepartmentProcessLoad.class.getCanonicalName() + " dp"
                + " WHERE dp.attendantId = :userId"
                + (uneId == null ? "" : " AND dp.department.businessUnitId = :businessUnitId");
        return HQL_findSimpleLong(query, params) > 0;
    }
    
    private boolean isAreaAudited(Long userId, Set<ProfileServices> s, Long uneId) {
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        String query = ""
                + " SELECT 1"
                + " FROM " + Area.class.getCanonicalName() + " area"
                + " WHERE area.attendantId = :userId"
                + (uneId == null ? "" : " AND area.department.buisnessUnitId = :businessUnitId");
        return HQL_findSimpleLong(query, params) > 0;
    }
    
    private boolean getUserSpecialServices_DepartmentAttendantByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_DEPARTMENT_ATTENDANT.getModule()) 
                || s.contains(ProfileServices.SPECIAL_DEPARTMENT_ATTENDANT)) {
            return true;
        }
        String query;
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        //encargado de un proceso auditado en un departamento
        query = ""
                + " SELECT 1 "
                + " FROM " + BusinessUnitDepartmentLite.class.getCanonicalName() + " dp"
                + " WHERE dp.attendantId = :userId"
                + (uneId == null ? "" : " AND dp.businessUnitId = :businessUnitId");
        if (HQL_findSimpleLong(query, params) > 0) {
            s.add(ProfileServices.SPECIAL_DEPARTMENT_ATTENDANT); 
        }
        return true;
    }
    
    private boolean getUserSpecialServices_ComplaintsDepartmentAttendantByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_COMPLAINT_DEPARTMENT_ATTENDANT.getModule()) 
                || s.contains(ProfileServices.SPECIAL_COMPLAINT_DEPARTMENT_ATTENDANT)) {
            return true;
        }
        String query;
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        //encargado de un departamento que permite quejas
        query = ""
                + " SELECT 1 "
                + " FROM " + BusinessUnitDepartmentLite.class.getCanonicalName() + " dp"
                + " WHERE dp.attendantId = :userId"
                + (uneId == null ? "" : " AND dp.businessUnitId = :businessUnitId")
                + " AND dp.complaints = 1"
                + "";
        if (HQL_findSimpleLong(query, params) > 0) {
            s.add(ProfileServices.SPECIAL_COMPLAINT_DEPARTMENT_ATTENDANT); 
        }
        return true;
    }
    
    private boolean getUserSpecialServices_MeterReviewRespondent(Long userId, Set<ProfileServices> s) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_METER_REVIEW_RESPONDENT.getModule()) 
                || s.contains(ProfileServices.SPECIAL_METER_REVIEW_RESPONDENT)) {
            return true;
        }
        String query;
        //encargado de calificar un indicador
        query = ""
                + " SELECT 1 "
                + " FROM " + IndicadorCalificacion.class.getCanonicalName() +  " c"
                + " WHERE c.deleted = 0"
                + " AND c.auditadoId = :userId"
                + " AND c.status = " + IndicadorCalificacion.STATUS_POR_CALIFICAR 
                + " AND c.indicador.status = " + Properties.INDICADOR_ACTIVO
                + "";
        boolean hasMeterRole = HQL_findSimpleLong(query, ImmutableMap.of("userId", userId)) > 0;
        query = "SELECT 1"
                + " FROM " + User.class.getCanonicalName() + " u "
                + " JOIN u.puestos p "
                + " JOIN p.perfil pr "
                + " WHERE u.id = :userId"
                + " AND 1 IN ("
                    + " pr.intBEncargadoIndicador,"
                    + " pr.intBEditorIndicador,"
                    + " pr.intBLectorIndicador"
                + ")";
        boolean hasMeterPermision  = HQL_findSimpleLong(query, ImmutableMap.of("userId", userId)) > 0;
        if (hasMeterRole && !hasMeterPermision) {
            s.add(ProfileServices.SPECIAL_METER_REVIEW_RESPONDENT); 
        }
        return true;
    }
    
    private boolean getUserSpecialServices_DepartmentDocumentManagerByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT.getModule()) 
                || s.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT)) {
            return true;
        }
        String query;
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        query = ""
                + " SELECT 1 "
                + " FROM " + BusinessUnitDepartmentLite.class.getCanonicalName() + " dp"
                + " WHERE dp.documentManagerId = :userId"
                + (uneId == null ? "" : " AND dp.businessUnitId = :businessUnitId");
        if (HQL_findSimpleLong(query, params) > 0) {
            s.add(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_DEPARTMENT); 
            return true;
        }
        return true;
    }

    private boolean getUserSpecialServices_OrganizationalUnitDocumentManagerByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_ORGANIZATIONAL_UNIT.getModule()) 
                || s.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_ORGANIZATIONAL_UNIT)) {
            return true;
        }
        String query;
        query = ""
                + " SELECT 1 "
                + " FROM " + BusinessUnit.class.getCanonicalName() + " bu "
                + " JOIN bu.organizationalUnit ou "
                + " WHERE ou.documentManagerId = :userId"
                + (uneId == null ? "" : " AND bu.id = :businessUnitId");
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        if (HQL_findSimpleLong(query, params) > 0) {
            s.add(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_ORGANIZATIONAL_UNIT);
            return true;
        }
        return true;
    }

    private boolean getUserSpecialServices_BusinessUnitDocumentManagerByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT.getModule()) 
                || s.contains(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT)) {
            return true;
        }
        String query;
        query = ""
            + " SELECT 1"
            + " FROM " + BusinessUnit.class.getCanonicalName() + " bu"
            + " WHERE bu.documentManagerId = :userId"
                + (uneId == null ? "" : " AND bu.id = :businessUnitId");
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        if (HQL_findSimpleLong(query, params) > 0) {
            s.add(ProfileServices.SPECIAL_DOCUMENT_MANAGER_BY_BUSINESS_UNIT); 
        }
        return true;
    }

    private boolean getUserSpecialServices_BusinessUnitDepartmentFormApproverByUne(Long userId, Set<ProfileServices> s, Long businessUnitId) {
        if (!Utilities.isModuleAvailable(Module.FORMULARIE)) {
            return true;
        }
        FormApproverUserInfo userInfo = FormUtil.getFormApproverUser(userId, businessUnitId);
        if (!s.contains(ProfileServices.SPECIAL_FORM_ADJUSTMENT_APPROVER) && userInfo.isAdjustmentActionAvailableSomewhere()) {
            s.add(ProfileServices.SPECIAL_FORM_ADJUSTMENT_APPROVER); 
        }
        if (!s.contains(ProfileServices.SPECIAL_FORM_CANCELATION_APPROVER) && userInfo.isCancelationActionAvailableSomewhere()) {
            s.add(ProfileServices.SPECIAL_FORM_CANCELATION_APPROVER);
        }
        if (!s.contains(ProfileServices.SPECIAL_FORM_REOPEN_APPROVER) && userInfo.isReopenActionAvailableSomewhere()) {
            s.add(ProfileServices.SPECIAL_FORM_REOPEN_APPROVER);
        }
        if (!s.contains(ProfileServices.SPECIAL_FORM_ANALYST_APPROVER) && userInfo.isAnalyst()) {
            s.add(ProfileServices.SPECIAL_FORM_ANALYST_APPROVER);
        }
        return true;
    }

    private boolean getUserSpecialServices_DocumentReaderByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_DOCUMENT_READER.getModule()) 
                || s.contains(ProfileServices.SPECIAL_DOCUMENT_READER)) {
            return true;
        }
        String query;
        query = ""
                + " SELECT 1 "
                + " FROM " + UserRef.class.getCanonicalName() + " c "
                + " WHERE c.id = :userId"
                + " AND exists ("
                    + " SELECT 1"
                    + " FROM " + DocumentReader.class.getCanonicalName() + "  dr"
                    + " WHERE c.id = dr.readerId"
                    + " AND dr.readed = " + DocumentReader.READ.NOT_READED.getValue()
                + " )";
        if (HQL_findSimpleLong(query, ImmutableMap.of("userId", userId)) > 0) {
            s.add(ProfileServices.SPECIAL_DOCUMENT_READER); 
        }
        return true;
    }
    
    private boolean getUserSpecialServices_DocumentAuthorizer(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_DOCUMENT_AUTHORIZER.getModule())
                || s.contains(ProfileServices.SPECIAL_DOCUMENT_AUTHORIZER)) {
            return true;
        }
        String query;
        query = ""
                + " SELECT 1 "
                + " FROM " + UserRef.class.getCanonicalName() + "   c "
                + " WHERE c.id = :userId"
                + " AND exists ("
                    + " SELECT d.id "
                    + " FROM " + AutorizationPoolDetails.class.getCanonicalName() + " d "
                    + " JOIN d.request r"
                    + " WHERE"
                        + " c.id = d.userId"
                        + " AND r.type != " + Request.FILL
                + " )";
        if (HQL_findSimpleLong(query, ImmutableMap.of("userId", userId)) > 0) {
            s.add(ProfileServices.SPECIAL_DOCUMENT_AUTHORIZER); 
        }
        return true;
    }
    
    private boolean getUserSpecialServices_AccessEditProfile(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_ACCESS_EDIT_PROFILE.getModule()) 
                || s.contains(ProfileServices.SPECIAL_ACCESS_EDIT_PROFILE)) {
            return true;
        }
        String query;
        query = ""
                + " SELECT count(*) "
                + " FROM " + User.class.getCanonicalName() + " u "
                + " WHERE"
                + " u.id = :userId"
                + " AND u.accessEditProfile = true";
        if (HQL_findSimpleInteger(query, "userId", userId) > 0) {
            s.add(ProfileServices.SPECIAL_ACCESS_EDIT_PROFILE); 
        }
        return true;
    }

    private boolean getUserSpecialServices_AnonymousUser(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_IS_ANONYMOUS_USER.getModule())
                || s.contains(ProfileServices.SPECIAL_IS_ANONYMOUS_USER)) {
            return true;
        }
        String query = " "
                + " SELECT count(*) "
                + " FROM " + User.class.getCanonicalName() + " u "
                + " WHERE"
                + " u.id = :userId"
                + " AND u.isAnonymous = true";
        if (HQL_findSimpleInteger(query, "userId", userId) > 0) {
            s.add(ProfileServices.SPECIAL_IS_ANONYMOUS_USER);
        }
        return true;
    }
    
    private boolean getUserSpecialServices_Activity(Long userId, Set<ProfileServices> s, Long uneId) {
        ActivityPending pending = new ActivityPending(this);
        if (Utilities.isModuleAvailable(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT.getModule())  
                && !s.contains(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT) 
                && pending.getPendingsCount(userId, Module.ACTIVITY) > 0) {
            s.add(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT); 
        }
        if (Utilities.isModuleAvailable(ProfileServices.ACCION_LECTOR.getModule())  
                && !s.contains(ProfileServices.ACCION_LECTOR)
                && pending.getPendingsCount(userId, Module.ACTION) > 0) {
            s.add(ProfileServices.ACCION_LECTOR);
        }
        if (Utilities.isModuleAvailable(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_AUDIT.getModule())  
                && !s.contains(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_AUDIT) 
                && pending.getPendingsCount(userId, Module.AUDIT) > 0) {
            s.add(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_AUDIT); 
        }
        if (Utilities.isModuleAvailable(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_MEETING.getModule())  
                && !s.contains(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_MEETING) 
                && pending.getPendingsCount(userId, Module.MEETING) > 0) {
            s.add(ProfileServices.SPECIAL_ACTIVITY_ATTENDANT_MEETING); 
        }
        if (Utilities.isModuleAvailable(ProfileServices.SPECIAL_FORM_ACTIVITY_ATTENDANT.getModule())
                && !s.contains(ProfileServices.SPECIAL_FORM_ACTIVITY_ATTENDANT)
                && pending.getPendingsCount(userId, Module.FORMULARIE) > 0) {
            s.add(ProfileServices.SPECIAL_FORM_ACTIVITY_ATTENDANT);
        }
        return true;
    }
    
    
    private boolean getUserSpecialServices_Clones(final Long userId, final Set<ProfileServices> s, final Set<ProfileServices> servicesAddedBySystem) {
        final ElapsedDataDTO tStart = MeasureTime.start(getClass());
        try {
            final List<Long> typeIds = HQL_findByQuery(
                    ApeConstants.SAVED_RECORDS_TYPES,
                    ImmutableMap.of(
                        "status", PendingCount.STATUS.ACTIVE.value(),
                        "owner", userId  
                    ),
                    true, 
                    CacheRegion.APE_COUNT,
                    0
            );
            if (typeIds == null || typeIds.isEmpty()) {
                return false;
            }
            final Map<Long, String> apeTypeIds = Utilities.getApeIndexDataId();
            final List<String> typeCodes = typeIds.stream()
                    .map((typeId) -> apeTypeIds.get(typeId))
                    .filter((typeId) -> typeId != null)
                    .collect(Collectors.toList());
            if (typeCodes.isEmpty()) {
                return false;
            }
            ApeUtil.configurePendingAccess(typeCodes, s, servicesAddedBySystem);
            return true;
        } finally {
            MeasureTime.stop(tStart, "Elapsed time loading user special services by clones for user " + userId);
        }
    }     

    
    
    private boolean getUserSpecialServices_PollRespondentByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_SURVEY_RESPONDENT.getModule()) 
                || s.contains(ProfileServices.SPECIAL_SURVEY_RESPONDENT)) {
            return true;
        }
        String query;
        //encargado de un proceso auditado en un departamento
        final Map<String, Long> params = new HashMap<>();
        params.put("userId", userId);
        if (uneId != null) {
            params.put("businessUnitId", uneId);
        }
        query = ""
                + " SELECT 1 "
                + " FROM " + PollRespondent.class.getCanonicalName() + " c"
                + " WHERE c.outstandingSurveys.status IN ("
                    + OutstandingSurveys.ESTATUS_EN_PROCESO
                    + "," + OutstandingSurveys.ESTATUS_EN_PROCESO_PARCIALMENTE_LLENA
                    + "," + OutstandingSurveys.ESTATUS_EN_PROCESO_CONTINUAR_MAS_TARDE
                + ")"
                + " AND c.id.respondentId = :userId"
                + (uneId == null ? "" : " AND c.poll.businessUnitId = :businessUnitId")
                + "";
        if (HQL_findSimpleLong(query, params) > 0) {
            s.add(ProfileServices.SPECIAL_SURVEY_RESPONDENT); 
        }
        return true;
    }

    /**
     * Vuelve option la lista de TextHasValue de los usuarios que pertenenecen a las unidades de negocio de userId
     *
     * @param userId El usuario del que se desea obtener a los compañeros
     * @param selected El usuario que aparecera seleccionado en el select
     * @param isAdmin Bandera que determina si es root y muestra todos
     * @param servicesMine Los servicios que debe tener el usuario en base al cual se va a filtrar
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
     * @return option para llenar el select en base a la lista de usuarios que pertenecen a las unidades de negocio de
     * userId
     *
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getOptionUsersInBusinessUnitOf(Long userId, String selected, Boolean isAdmin,
            ProfileServices[] servicesMine, ProfileServices[] servicesTheirs) {
        return Utilities.comboParser(getUsersInBusinessUnitOf(isAdmin, userId, servicesMine, servicesTheirs, selected), selected);
    }

    /**
     * Vuelve option la lista de TextHasValue de los usuarios que pertenenecen a las unidades de negocio de userId
     *
     * @param userId El usuario del que se desea obtener a los compañeros <code>selected</code> Defaults to "".<br/>
     * <code>isAdmin</code> Defaults to false.<br/> <code>servicesMine</code> Defaults to
     * {@link Profile#Servicios.ALL}.<br/> <code>servicesTheirs</code> Defaults to {@link Profile#Servicios.ALL}.<br/>
     *
     * @return option para llenar el select en base a la lista de usuarios que pertenecen a las unidades de negocio de
     * userId
     *
     * @since ********
     * <AUTHOR> Germán Lares Lares
     *
     * @see HibernateDAO_User#getOptionUsersInBusinessUnitOf(Long, String, Boolean, ProfileServices[],
     * ProfileServices[])
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getOptionUsersInBusinessUnitOf(Long userId) {
        ProfileServices[] services = {ProfileServices.ALL};
        return getOptionUsersInBusinessUnitOf(userId, "", false, services, services);
    }

    /**
     * Vuelve option la lista de TextHasValue de los usuarios que pertenenecen a las unidades de negocio de userId
     *
     * @param userId El usuario del que se desea obtener a los compañeros
     * @param isAdmin Bandera que determina si es root y muestra todos <code>selected</code> Defaults to "".<br/>
     * <code>servicesMine</code> Defaults to {@link Profile#Servicios.ALL}.<br/> <code>servicesTheirs</code> Defaults to
     * {@link Profile#Servicios.ALL}.<br/>
     *
     * @return option para llenar el select en base a la lista de usuarios que pertenecen a las unidades de negocio de
     * userId
     *
     * @since ********
     * <AUTHOR> Germán Lares Lares
     *
     * @see HibernateDAO_User#getOptionUsersInBusinessUnitOf(Long, String, Boolean, ProfileServices[],
     * ProfileServices[])
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getOptionUsersInBusinessUnitOf(Long userId, Boolean isAdmin) {
        ProfileServices[] services = {ProfileServices.ALL};
        return getOptionUsersInBusinessUnitOf(userId, "", isAdmin, services, services);
    }

    /**
     * Vuelve option la lista de TextHasValue de los usuarios que pertenenecen a las unidades de negocio de userId
     *
     * @param userId El usuario del que se desea obtener a los compañeros
     * @param isAdmin Bandera que determina si es root y muestra todos
     * @param selected El usuario que aparecera seleccionado en el select
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
      * <code>servicesMine</code> Defaults to {@link Profile#Servicios.ALL}.<br/>
     *
     * @return option para llenar el select en base a la lista de usuarios que pertenecen a las unidades de negocio de
     * userId
     *
     * @since ********
     * <AUTHOR> Germán Lares Lares
     *
     * @see HibernateDAO_User#getOptionUsersInBusinessUnitOf(Long, String, Boolean, ProfileServices[],
     * ProfileServices[])
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getOptionUsersInBusinessUnitOf(Long userId, String selected, Boolean isAdmin, ProfileServices[] servicesTheirs) {
        ProfileServices[] services = {ProfileServices.ALL};
        return getOptionUsersInBusinessUnitOf(userId, selected, isAdmin, services, servicesTheirs);
    }
    
    /**
     * Función que devuelve los usuarios en base al departamento en el que se encuentra userId, si es root devuelve la
     * lista completa de usuarios, tambien filtra en base a los servicios propios del usuario y los servicios de los
     * usuarios retornados
     *
     * @param isAdmin Bandera que dice si el usuario es Root
     * @param userId Usuario en base al cual se va a filtrar
     * @param deptId Departamento en base al cual se va a filtrar
     * @param servicesMine Los servicios que debe tener el usuario en base al cual se va a filtrar
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
     * @return Lista de usuarios que cumplen con todas las caraterísticas en formato TextHasValue
     *
     * @since *********
     * <AUTHOR> Cavazos Galindo
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInDepartmentOf(boolean isAdmin, Long userId, Long deptId, ProfileServices[] servicesMine,
            ProfileServices[] servicesTheirs) {
        String filter = "1=1";
        if (!isAdmin) {
            filter = validEntitiesFilterByDepartmentUser
                    .replaceAll(":userId", userId + "")
                    .replaceAll(":deptId", deptId.toString())
                    .replaceAll(":servicesMine", ProfileServices.getCodedServices("perfil", servicesMine))
                    .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", servicesTheirs));
        }
        return getUsersComboList(filter);
    }
    
    /*
     * Regresa una lista de Ids de los usuarios en la unidad de negocio y con los servicios dados
     * @param bussinesUnitId El id de la unidad de negocios para la búsqueda
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
     * @return option para llenar el select en base a la lista de usuarios que pertenecen a las unidades de negocio de
     * userId
     *
     * @since 2.7.5.3
     * <AUTHOR> Guadalupe Alemán Reyes
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> getUsersIdsByBusinessUnit(Long businessUnit, ProfileServices[] servicesTheirs) {
        
        String filter  = validEntitiesFilterByBusinessUnit
            .replaceAll(":businessUnit", businessUnit + "")
            .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", servicesTheirs));
        String query=" SELECT c.id"
                + " FROM DPMS.Mapping.User c "
                + " WHERE " + filter;
        
        return HQL_findByQuery(query);
    }

    /**
     * Vuelve option la lista de TextHasValue de los usuarios que pertenenecen a el departamento de userId
     *
     * @param userId El usuario del que se desea obtener a los compañeros
     * @param deptId El departamento del cual se desea obtener a los compañeros
     * @param selected El usuario que aparecera seleccionado en el select
     * @param isAdmin Bandera que determina si es root y muestra todos
     * @param servicesMine Los servicios que debe tener el usuario en base al cual se va a filtrar
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
     * @return option para llenar el select en base a la lista de usuarios que pertenecen a las unidades de negocio de
     * userId
     *
     * @since *********
     * <AUTHOR> Cavazos Galindo
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUsersInDepartmentOf(Long userId, Long deptId, String selected, Boolean isAdmin,
            ProfileServices[] servicesMine, ProfileServices[] servicesTheirs) {
        List<UserHasValue> users = getUsersInDepartmentOf(isAdmin, userId, deptId, servicesMine, servicesTheirs);
        // This is backward compatibility, please, someday destroy A1 modules
        if(!selected.equals("0") && !selected.isEmpty()){
            UserRef selUser = HQLT_findById(UserRef.class, Long.parseLong(selected));
            UserHasValue selectedUser = new UserHasValue(selUser.getDescription(),selUser.getId());
            if (!users.contains(selectedUser)) {
                users.add(selectedUser);
            }
        }
        return Utilities.comboParser(users, selected);
    }
    
    /**
     * Vuelve option la lista de TextHasValue de los usuarios que pertenenecen a el departamento de userId
     *
     * @param userId El usuario del que se desea obtener a los compañeros
     * @param deptId El departamento del cual se desea obtener a los compañeros
     * @param selected El usuario que aparecera seleccionado en el select
     * @param isAdmin Bandera que determina si es root y muestra todos
     * @param servicesTheirs Los servicios que deben tener los usuarios que va a devolver la función
      * <code>servicesMine</code> Defaults to {@link Profile#Servicios.ALL}.<br/>
     *
     * @return option para llenar el select en base a la lista de usuarios que pertenecen a las unidades de negocio de
     * userId
     *
     * @since *********
     * <AUTHOR> Cavazos Galindo
     *
     * @see HibernateDAO_User#getUsersInDepartmentOf(Long, Long, String, Boolean, ProfileServices[],
     * ProfileServices[])
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getUsersInDepartmentOf(Long userId, Long deptId, String selected, Boolean isAdmin, ProfileServices[] servicesTheirs) {
        ProfileServices[] services = {ProfileServices.ALL};
        return getUsersInDepartmentOf(userId, deptId, selected, isAdmin, services, servicesTheirs);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> getUserIdByPosition(Long positionId) {
        String query = ""
                + " SELECT usr.id"
                + " FROM DPMS.Mapping.User usr "
                + " JOIN usr.puestos pst "
                + " WHERE pst.id = :id";
        return HQL_findByQuery(query, "id", positionId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<UserHasValue> getUsersInBusinessUnits(Long userId, List<Long> businessUnits, ProfileServices[] servicios) {
        String filter, in;
        StringBuilder sb = new StringBuilder(10);
        for (Long une : businessUnits) {
            if (sb.length() != 0) {
                sb.append(',');
            }
            sb.append(une);
        }
        if (sb.length() == 0) {
            in = "0";
        } else {
            in = sb.toString();
        }
        filter = validEntitiesFilterByUne
            .replaceAll(":unes", in)
                .replaceAll(":servicesTheirs", ProfileServices.getCodedServices("pr", servicios));
        return getUsersComboList(filter, userId, true);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getUsersAll() {
        //Class clazz, String mappedDescription, String filtros
        String filtro = "c.id IN (SELECT u.boss.id FROM " + User.class.getCanonicalName() + " AS u)";
        return getStrutsComboList(User.class, "description", filtro);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getOptionUsersInBusinessUnits(List<Long> businessUnits, String selected, ProfileServices[] servicios) {
        return Utilities.comboParser(getUsersInBusinessUnits(null, businessUnits, servicios), selected);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer getActiveUsers(){
        String query = ""
                + " SELECT count(*)"
                + " FROM " + User.class.getCanonicalName() + " usr "
            + " WHERE usr.status = " + User.ACTIVE_STATUS;
        return HQL_findSimpleInteger(query);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean updateShowExternalDialog(Long userId) {
        int result = HQL_updateByQuery("UPDATE " + User.class.getCanonicalName() + " SET showExternalDialog = 0 WHERE id = " + userId);
        return result == 1;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean readShowWelcomeDialog(Long userId) {
        int result = HQL_updateByQuery("UPDATE " + User.class.getCanonicalName() + " SET showWelcomeDialog = 0 WHERE id = " + userId);
        return result == 1;
    }

    private List<Long> getJobIds(Long userId) {
        String query = ""
                + " SELECT p.id"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " JOIN c.puestos p "
                + " WHERE c.id = :id";
        return HQL_findByQuery(query, "id", userId);
    }

    @Override
    @OnBossChanged
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean changeBoss(User modifiedUser, Long oldBossId, Long currentBossId, UserCountPendingDTO countPending, ILoggedUser loggedUser)  {
        return true;
    }
    
    @Override
    @OnJobAdded
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean addJobs(Long userId, UserCountPendingDTO countPending, List<Long> jobs, ILoggedUser loggedUser) {
            return true;
    }
    
    @Override
    @OnJobRemoved
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean removeJobs(Long userId, UserCountPendingDTO countPending, List<Long> jobs, ILoggedUser loggedUser) {
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer getActiveUsers(final String licenseCode) {
        final String query = ""
                + " SELECT count(*)"
                + " FROM " + User.class.getCanonicalName() + " usr "
                + " WHERE usr.status = " + User.STATUS.ACTIVE.getValue()
                + " AND usr.licenseCode = :licenseCode";
        return HQL_findSimpleInteger(query, "licenseCode", licenseCode);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer getInactiveBySystemUsers(final String licenseCode) {
        final String query = ""
                + " SELECT count(*)"
                + " FROM " + User.class.getCanonicalName() + " usr "
                + " WHERE usr.status = " + User.STATUS.INACTIVE.getValue()
                + " AND usr.inactiveBySystem = 1"
                + " AND usr.licenseCode = :licenseCode";
        return HQL_findSimpleInteger(query, "licenseCode", licenseCode);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public UserRef getUserRef(Long id) {
        return HQLT_findById(UserRef.class, id);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle changePassword(String user, String hashedPassword) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        Map params = new HashMap();
        params.put("account", user);
        params.put("hashedPassword", hashedPassword);
        Integer records = HQL_updateByQuery(""
                + " UPDATE " + User.class.getCanonicalName() + " c"
                + " SET c.hashedPassword = :hashedPassword, c.version = c.version + 1"
                + " WHERE c.cuenta = :account", params);
        if (records == 1) {
            gsh.setOperationEstatus(1);
        }
        return gsh;
    }
    
    @Override
    @OnRegisterUser
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle registerUser(User user) {
        user.setLicenseCode(LicenseUtil.DEFAULT_LICENSE_SCHEMA);
        user.setIsAway(false);
        user.setChangeAwayReplacement(0);
        user.setIsAnonymous(false);
         user.setAskToRenewTimezone(true);
        if (Utilities.exceededLicences(user.getLicenseCode(), this) >= 0) {
            GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setErrorMessage(LoginError.EXCEEDED_LICENCES.getError());
            return gsh;
        }
        user.setCreationSource(UserCreationSource.REGISTER.getValue());
        final ILoggedUser userAdmin = SecurityRootUtils.getFirstAdminDto();
        getEntityManager().flush();
        return getAspectJAutoProxy().save(user, userAdmin);
    }

    @Override
    @OnRegisterLdapUser
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle registerExternalUser(User user, UserCreationSource source) {
        // Se coloca el lenguaje predeterminado (si existe)
        setDefaultLang(user);
        // Se coloca el esquema de licencias correspondiente
        setDefaultSchema(user);
        user.setAskToRenewPassword(false);
        user.setChangeAwayReplacement(0);
        user.setIsAway(false);
        user.setIsAnonymous(false);
         user.setAskToRenewTimezone(true);
        // Se verifican si el usuario puede marcarse como ACTIVO de acuerdo a las licencias
        setDefaultStatus(user);
        // Se coloca el puesto predeterminado configurado
        setDefaultPosition(user);
        // Se guarda la información propia del usuario
        user.setCreationSource(source.getValue());
        final ILoggedUser userAdmin = SecurityRootUtils.getFirstAdminDto();
        getEntityManager().flush();
        GenericSaveHandle saved = getAspectJAutoProxy().save(user, userAdmin);
        if (saved.getOperationEstatus() == 1) {
            // Se asignan permisos de carpeta configurados
            saveDefaultFolder(user);
            // Se asignan permisos de "favoritos" configurados
            saveDefaultFavorites(user);
        }
        return saved;
    }
    
    private void setDefaultLang(User user) {
        if (
            Utilities.getSettings().getAdDefaultLang() == null
            || Utilities.getSettings().getAdDefaultLocale()== null
            || Utilities.getSettings().getAdDefaultLang().trim().isEmpty()
            || Utilities.getSettings().getAdDefaultLocale().trim().isEmpty()
        ) {
            user.setLang("");
            user.setLocale("");
        } else {
            user.setLang(Utilities.getSettings().getAdDefaultLang());
            user.setLocale(Utilities.getSettings().getAdDefaultLocale());
        }
    }
    
    private void setDefaultSchema(User user) {
        if (
            Utilities.getSettings().getAdDefaultSchema() == null
            || Utilities.getSettings().getAdDefaultSchema().isEmpty()
        ) {
            user.setLicenseCode(LicenseUtil.DEFAULT_LICENSE_SCHEMA);
        } else {
            user.setLicenseCode(Utilities.getSettings().getAdDefaultSchema());
        }
    }
    private void setDefaultStatus(User user) {
        if (Utilities.exceededLicences(user.getLicenseCode(), this) >= 0) {
            user.setStatus(User.STATUS.INACTIVE.getValue());
        } else {
            user.setStatus(User.STATUS.ACTIVE.getValue());
        }
    }

    private void setDefaultPosition(User user) {
        final String adDefaultPositionCode = Utilities.getSettings().getAdDefaultPositionCode();
        if (adDefaultPositionCode == null || adDefaultPositionCode.isEmpty()) {
            return;
        }
        Position p = HQLT_findSimple(Position.class, ""
                + " SELECT p"
                + " FROM " + Position.class.getCanonicalName() + " p"
                + " WHERE p.code = :code", "code", adDefaultPositionCode);
        if (p == null) {
            getLogger().warn(""
                    + "LDAP user '{}' has been created without position, default position code '{}' not found.",
                     user.getCuenta(),
                     adDefaultPositionCode);
        } else {
            user.setDefaultWorkflowPosition(p);
            user.setPuestos(new HashSet<>(1));
            user.getPuestos().add(p);
        }
    }

    private void saveDefaultFolder(User user) {
        if (
            Utilities.getSettings().getAdDefaultfolder() == null
            || Utilities.getSettings().getAdDefaultfolder().trim().isEmpty()
        ) {
            return;
        }
        final Map<String, Object> params = new HashMap<>();
        final List<String> folders = Arrays.asList(getSeparatedByCommaIn(Utilities.getSettings().getAdDefaultfolder()).split(","));
        SQL_updateByQuery(""
            + " INSERT INTO tblcarpetausuario "
            + " (intnodoid, intusuarioid, tspfechahoracreacion) "
            + " SELECT "
                    + " n.intnodoid "
                    + "," + user.getId()
                    + ",current_timestamp "
            + " FROM tblnodo n "
            + " WHERE " + BindUtil.bindFilterList("n.path", folders, false, params), 
                params,
                0,
                Arrays.asList("tblcarpetausuario")
        );
    }
    
    private void saveDefaultFavorites(User user) {
        if (
            Utilities.getSettings().getAdFavoriteTaskCodes() == null
            || Utilities.getSettings().getAdFavoriteTaskCodes().trim().isEmpty()
        ) {
            return;
        }
        final List<Long> taskIds = HQL_findByQuery(""
            + " SELECT "
                    + "t.id "
            + " FROM " + FavoriteTask.class.getCanonicalName() +  " t "
            + " WHERE t.code IN ("
                + getSeparatedByCommaIn(Utilities.getSettings().getAdFavoriteTaskCodes())
            + " )",
            Utilities.EMPTY_MAP,
            true,
            CacheRegion.FAVORITE,
            0
        );
        final Long userId = SecurityRootUtils.getFirstAdminUserId();
        saveLinkedItems(
                UserFavoriteTask.class, 
                user.getId(),
                taskIds,
                true, 
                null,
                true, 
                CacheRegion.FAVORITE,
                0, 
                userId
        );
    }
    
    private String getSeparatedByCommaIn(String value) {
        return ""
            + "'" 
                + value.trim()
                    .replaceAll(",", "', '")
                    .replaceAll("\\s+'", "'") 
                    .replaceAll("'\\s+", "'") 
            + "'";
    }
    
     @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle toggleStatus(Long userId, LoggedUser loggedUser) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final ILicenseUserDAO licDao = getBean(ILicenseUserDAO.class);
        final UserCertificateDTO data = licDao.getUser(userId);
        getLogger().trace("toggleStatus ... " + userId);
        if (Objects.equals(data.getStatus(), User.STATUS.INACTIVE.getValue())) {
            final Integer exceededLicences = Utilities.exceededLicences(data.getLicenseCode(), this);
            if (exceededLicences >= 0) {
                return null;
            }
        }
        final StringBuilder sb = new StringBuilder();
        sb.append("")
                .append(" UPDATE ").append(User.class.getCanonicalName()).append(" c")
                .append(" SET ")
                .append(" c.lastModifiedDate = :lastModifiedDate")
                .append(" , c.lastModifiedBy = :lastModifiedBy ")
                .append(" , c.version = c.version + 1 ");
        final Map<String, Object> params = new HashMap<>();
        params.put("lastModifiedDate", new Date());
        params.put("lastModifiedBy", loggedUser.getId());
        if (Objects.equals(data.getStatus(), User.STATUS.ACTIVE.getValue())) {
            data.setStatus(User.STATUS.INACTIVE.getValue());
            params.put("status", User.STATUS.INACTIVE.getValue());
            sb.append("")
                    .append(", c.status = :status")
                    .append(", c.certificate = null");
            try {
                final UserLogin loginInfo = new UserLogin(userId);
                loginInfo.removeSession(true);
            } catch(final Exception e) {
                getLogger().error("Failed to close session for user {} in toggleStatus", new Object[]{userId});
            }
        } else if (Objects.equals(data.getStatus(), User.STATUS.LOCKED.getValue())) {
            data.setStatus(User.STATUS.ACTIVE.getValue());
            params.put("status", User.STATUS.ACTIVE.getValue());
            sb.append("")
                    .append(", c.status = :status")
                    .append(", c.certificate = null");
            //Cuando es el caso de un usuario bloqueado se tiene que insertar un nuevo registro, ya que si no se ha 
            //cumplido con el tiempo de desbloqueo podría volver a bloquear al usuario 
            //Ignorar este registro en el historial de accesos
            UserAttempts newUserAttempt = new UserAttempts(-1L, data.getCuenta(), userId, 0, new Date(), "-", "-");
            UserAttempts savedAttempts = makePersistent(newUserAttempt, loggedUser.getId());
            if (savedAttempts == null) {
                gsh.setOperationEstatus(0);
            }
        } else {
            data.setStatus(User.STATUS.ACTIVE.getValue());
            params.put("status", User.STATUS.ACTIVE.getValue());
            final String encryptedCert = getBean(ILicenseUserDAO.class).generateCertificateToken(data.getCuenta());
            params.put("certificate", encryptedCert);
            sb.append("")
                    .append(", c.status = :status")
                    .append(", c.inactiveBySystem = 0")
                    .append(", c.certificate = :certificate");
        }
        sb.append(" WHERE c.id = :id ");
        params.put("id", userId);
        final Integer result = HQL_updateByQuery(
                    User.class, 
                    params, 
                    loggedUser.getId(), 
                    userId,
                    false,
                    null,
                    0,
                    null
        );
        if (result > 0) {
            if (User.STATUS.INACTIVE.getValue().equals(data.getStatus())) {
                cancelFills(userId, data.getDescription(), loggedUser);
                omitCopies(userId, loggedUser);
            }
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage(""
                    + "{"
                    + "tipo:'toggle_status_success'"
                    + ",fijo:[" + Utilities.getSerializedObj(new TextHasValue("clave", data.getCode())) + "]"
                    + "}");
        }
        return gsh;
    }
    
    private void omitCopies(Long userId, LoggedUser loggedUser) {
        final IReceiptAcknowledgmentDAO ackDao = getBean(IReceiptAcknowledgmentDAO.class);
        final Long countDeliverCopies = ackDao.getCopiesToDeliverCountByUser(userId);
        if (countDeliverCopies > 0) {
            ackDao.omitCopiesByInactiveUser(userId, loggedUser);
        }
    }

    private void cancelFills(Long userId, String userDescription, LoggedUser loggedUser) {
        final IRequestDAO reqDao = getBean(IRequestDAO.class);
        final Integer countFillForms = reqDao.getActiveFillFormsByAuthorCount(userId);
        if (countFillForms > 0) {
            final String comment = getTag("deactiveUserCancelFillComment")
                    .replace(":requestor", userDescription)
                    .replace(":user", loggedUser.getDescription());
            reqDao.cancelFillsByUserId(userId, comment, loggedUser);
        }
    }

    private void getUserSpecialServices_DocumentFillFormByUne(Long userId, Set<ProfileServices> s, Long uneId) {
        if (!Utilities.isModuleAvailable(ProfileServices.SPECIAL_REQUEST_FILL_FORM_TASK.getModule()) 
                || s.contains(ProfileServices.SPECIAL_REQUEST_FILL_FORM_TASK)) {
            return;
        }
        ToFillForm p = new ToFillForm(this);
        if (p.getCountByUser(userId) > 0) {
            s.add(ProfileServices.SPECIAL_REQUEST_FILL_FORM_TASK);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean updateUserLdap(IUserLdap user) {
        StringBuilder update = new StringBuilder(200);
        Map<String, Object> params = new HashMap<>();
        if (user.getCorreo() != null) {
            update.append(", u.correo = :correo ");
            params.put("correo", user.getCorreo());
        }
        if (user.getContacto() != null) {
            update.append(", u.contacto = :contacto ");
            params.put("contacto", user.getContacto());
        }
        if (user.getDescription() != null) {
            update.append(", u.description = :description ");
            params.put("description", user.getDescription());
        }
        if (update.length() <= 0) {
            return false;
        }
        params.put("cuenta", user.getCuenta());
        update.delete(0, 2); // <-- se elimina la 1era coma
        update.insert(0, "UPDATE " + User.class.getCanonicalName() + " u SET ").append(" WHERE u.cuenta = :cuenta ");
        return HQL_updateByQuery(
            update.toString(), params
        ) > 0;
    }

    private void updateWorkflowPreviewByUserIds(final Set<Long> userIds) {
        // TODO: Pruebas de crear flujos de cancelación de formularios y probar alta de usuarios con puestos
        WorkflowPreviewGenerator.updateByUsers(WorkflowSupported.FORM_REQUEST, userIds, this);
        WorkflowPreviewGenerator.updateByUsers(WorkflowSupported.REQUEST, userIds, this);
    }
    
    private void updateWorkflowPreviewByPositionIds(final Set<Long> positionIds) {
        // TODO: Pruebas de crear flujos de cancelación de formularios y probar alta de usuarios con puestos
        WorkflowPreviewGenerator.updateByPositions(WorkflowSupported.FORM_REQUEST, positionIds, this);
        WorkflowPreviewGenerator.updateByPositions(WorkflowSupported.REQUEST, positionIds, this);
    }

    private void updateUserAccessToEdition(final UserEditionInfoDTO user, final LoggedUser loggedUser) {
        if (loggedUser.isAdmin()) {
            user.setHasJobs(user.getJobs() != null && !user.getJobs().trim().isEmpty());
            user.setSameBusinessUnitAsCurrentUser(true);
            user.setSameOrganizationalUnitAsCurrentUser(true);
            user.setCurrentUserEscalationManager(true);
            user.setCurrentUserCorporative(true);            
        } else if (user.getJobs() != null && !user.getJobs().trim().isEmpty()) {
            user.setHasJobs(true);
            user.setSameBusinessUnitAsCurrentUser(false);
            user.setSameOrganizationalUnitAsCurrentUser(false);
            if (user.getBusinessUnitIds() != null && !user.getBusinessUnitIds().trim().isEmpty()) {
                final Long countBusinessUnits = HQL_findSimpleLong(""
                        + " SELECT count(c.id)"
                        + " FROM " + User.class.getCanonicalName() + " c "
                        + " LEFT JOIN c.puestos p"
                        + " LEFT JOIN p.une bu"
                        + " WHERE c.id = :id"
                        + " AND bu.id IN (" + user.getBusinessUnitIds() + ")",
                        "id", loggedUser.getId());
                user.setSameBusinessUnitAsCurrentUser(countBusinessUnits > 0);
            }
            if (user.getOrganizationUnitIds() != null && !user.getOrganizationUnitIds().trim().isEmpty()) {
                final Long countOrganizationUnitIds = HQL_findSimpleLong(""
                        + " SELECT count(c.id)"
                        + " FROM " + User.class.getCanonicalName() + " c "
                        + " LEFT JOIN c.puestos p"
                        + " LEFT JOIN p.corp org"
                        + " WHERE c.id = :id"
                        + " AND org.id IN (" + user.getOrganizationUnitIds() + ")",
                        "id", loggedUser.getId());
                user.setSameOrganizationalUnitAsCurrentUser(countOrganizationUnitIds > 0);
            }
            user.setCurrentUserEscalationManager(loggedUser.getServices().contains(ProfileServices.ESCALATION_MANAGER));
            user.setCurrentUserCorporative(loggedUser.getServices().contains(ProfileServices.ESCALATION_MANAGER));
        } else {
            user.setHasJobs(false);
            user.setSameBusinessUnitAsCurrentUser(false);
            user.setSameOrganizationalUnitAsCurrentUser(false);
            user.setCurrentUserEscalationManager(loggedUser.getServices().contains(ProfileServices.ESCALATION_MANAGER));
            user.setCurrentUserCorporative(loggedUser.getServices().contains(ProfileServices.ESCALATION_MANAGER));
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public UserEditionInfoDTO getUserEditionInfoByAccount(final String account, final LoggedUser loggedUser) {
        if (!loggedUser.isAdmin()
                && !loggedUser.getServices().contains(ProfileServices.ADMON_ACCESOS)) {
            getLogger().error("Invalid access to getUserInfoByAccount by {} with account {}", loggedUser.getLogin(), account);
            return null;
        }
        if (account == null || account.trim().isEmpty()) {
            getLogger().error("Invalid parameter provided to getUserInfoByAccount by {} with account {}", loggedUser.getLogin(), account);
            return null;
        }
        final UserEditionInfoDTO user = (UserEditionInfoDTO) HQL_findSimpleObject(
                USER_EDITION_INFO_BY_FILTER.replace("<filter>", "c.cuenta = :cuenta"),
                "cuenta", account
        );
        updateUserAccessToEdition(user, loggedUser);
        return user;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public UserEditionInfoDTO getUserEditionInfoByCode(final String code, final LoggedUser loggedUser) {
        if (!loggedUser.isAdmin()
                && !loggedUser.getServices().contains(ProfileServices.ADMON_ACCESOS)) {
            getLogger().error("Invalid access to getUserInfoByCode by {} with code {}", loggedUser.getLogin(), code);
            return null;
        } 
        if (code == null || code.trim().isEmpty()) {
            getLogger().error("Invalid parameter provided to getUserInfoByCode by {} with code {}", loggedUser.getLogin(), code);
            return null;
        }
        final UserEditionInfoDTO user = (UserEditionInfoDTO) HQL_findSimpleObject( 
                USER_EDITION_INFO_BY_FILTER.replace("<filter>", "c.code = :code"),
                "code", code
        );
        updateUserAccessToEdition(user, loggedUser);
        return user;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public UserEditionInfoDTO getUserEditionInfoById(final Long userId, final LoggedUser loggedUser) {
        if (!loggedUser.isAdmin()
                && !loggedUser.getServices().contains(ProfileServices.ADMON_ACCESOS) 
                && !loggedUser.getServices().contains(ProfileServices.SPECIAL_ACCESS_EDIT_PROFILE)) {
            getLogger().error("Invalid access to getUserInfoByCode by {} with id {}", loggedUser.getLogin(), userId);
            return null;
        } 
        if (userId == null || userId == -1L) {
            getLogger().error("Invalid parameter provided to getUserInfoByCode by {} with id {}", loggedUser.getLogin(), userId);
            return null;
        }
        final UserEditionInfoDTO user = (UserEditionInfoDTO) HQL_findSimpleObject(
                USER_EDITION_INFO_BY_FILTER.replace("<filter>", "c.id = :id"),
                "id", userId
        );
        updateUserAccessToEdition(user, loggedUser);
        return user;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public UserRef getUserRef(final Long userId, final Boolean onlyActive) {
        String query = ""
                + " SELECT c "
                + " FROM " + UserRef.class.getCanonicalName() + " c"
                + " WHERE c.id = :id"
                + (onlyActive ? " AND c.status = " + User.STATUS.ACTIVE.getValue() : "");
        return (UserRef) HQL_findSimpleObject(query, "id", userId);
    }
        
    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IUserDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(IUserDAO.class);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> getSurveyDocumentAccess(Long loggedUserId) {
        if (isAdmin(loggedUserId)) {
            return null;
        }
        String query = ""
                + " SELECT d.surveyId "
                + " FROM " + Document.class.getCanonicalName() + " d "
                + " LEFT JOIN " + NodeAccess.class.getCanonicalName() + " s "
                + " ON s.id = d.answersNodeId"
                + " WHERE d.status = " + Document.ACTIVE_STATUS
                + " AND ( "
                    + validEntitiesFilterByUserAtNodeAccess.replaceAll(":nodeId", "s.id")
                + " )";
        return HQL_findByQuery(
                query,
                ImmutableMap.of(
                        "id", loggedUserId
                )
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasSurveyDocumentAccess(String masterId, LoggedUser loggedUser) {
        if (loggedUser.getServices().contains(ProfileServices.FORMULARIO_CONTROL) || loggedUser.isAdmin()) {
            return true;
        }
        final Long answersNodeId = HQL_findSimpleLong(""
            + " SELECT d.answersNodeId "
            + " FROM " + Document.class.getCanonicalName() + " d "
            + " WHERE d.masterId = :masterId",
            ImmutableMap.of("masterId", masterId)
        );
        String query = ""
                + " SELECT count(s.id) "
                + " FROM " + NodeAccess.class.getCanonicalName() + " s "
                + " WHERE s.id = :answersNodeId"
                + " AND ( "
                    + validEntitiesFilterByUserAtNodeAccess
		+ " ) "
            ;
        final Integer result = HQL_findSimpleInteger(
                query,
                ImmutableMap.of(
                        "answersNodeId", answersNodeId,
                        "nodeId", answersNodeId,
                        "id", loggedUser.getId()
                )
        );
        return result > 0;
    }
    
    private List<UserHasValue> getUsersComboList(final String filtros) {
        return getUsersComboList(filtros, -1L, true);
    }
 
    private List<UserHasValue> getUsersComboList(final String filtros, final Long id, final Boolean withStatus) {
        final List<Long> ids = new ArrayList<>(1);
        if (id != null && id > 0) {
            ids.add(id);
        }
        return getStrutsComboList(
                UserHasValue.class, 
                User.class,
                "c.description, c.code, c.cuenta, c.correo", 
                filtros, 
                ids,
                withStatus,
                false,
                null,
                0
        );
    }
    
    private List<UserHasValue> getUsersComboList(final String filtros, final List<Long> id, final Boolean withStatus) {
        final List<Long> ids = new ArrayList<>(1);
        if (id != null && !id.isEmpty()) {
            ids.addAll(id);
        }
        return getStrutsComboList(
                UserHasValue.class, 
                User.class,
                "c.description, c.code, c.cuenta, c.correo", 
                filtros, 
                ids,
                withStatus,
                false,
                null,
                0
        );
    }   

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateUserLocale(Long userId, LocaleDTO systemLocale) {
        final String lang = StringUtils.truncate(systemLocale.getLang(), 5);
        final String locale = StringUtils.truncate(systemLocale.getLocale(), 5);
        final Map<String, Object> params = new HashMap<>(3);
        params.put("id", userId);
        params.put("lang", lang);
        params.put("locale", locale);
        HQL_updateByQuery(""
                + " UPDATE " + User.class.getCanonicalName()
                + " SET"
                        + " lang = :lang,"
                        + " locale = :locale "
                + " WHERE id = :id", params
        );
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridStateByUser syncGridState(final String gridId, final GridStateDTO state, final ILoggedUser loggedUser) {
        final GridStateByUser entity = getGridStateUserData(gridId, loggedUser.getId());
        final GridStateUserHelper helper = new GridStateUserHelper(this);
        final Date now = new Date();
        if (entity == null) {
            final GridStateByUser newEntity = new GridStateByUser(loggedUser.getId(), gridId);
            final Long stateId = helper.persist(-1L, loggedUser.getId(), state);
            if (stateId == null) {
                getLogger().error("Failed to syncTabState for {}", state);
            }
            newEntity.setStateId(stateId);
            newEntity.setCreatedBy(loggedUser.getId());
            newEntity.setLastModifiedBy(loggedUser.getId());
            newEntity.setLastModifiedDate(now);
            newEntity.setCreatedDate(now);
            newEntity.setInsert(true);
            final GridStateByUser saved = makePersistent(newEntity, loggedUser.getId());
            return saved;
        } else {
            if (entity.getStateId() == null) {
                final Long stateId = helper.persist(-1L, loggedUser.getId(), state);
                if (stateId == null) {
                    getLogger().error("Failed to syncTabState for {}", state);
                    return entity;
                }
                if (stateId.equals(0L)) {
                    return entity;
                }
                entity.setStateId(stateId);
                entity.setLastModifiedBy(loggedUser.getId());
                entity.setLastModifiedDate(now);
                final GridStateByUser saved = makePersistent(entity, loggedUser.getId());
                return saved;
            } else {
                final Long stateId = helper.persist(entity.getStateId(), loggedUser.getId(), state);
                if (stateId == null) {
                    getLogger().error("Failed to syncGridState for {}", state);
                }
                return entity;
            }
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridStateDTO getMyGridState(final String gridId, final ILoggedUser loggedUser){
        final Map<String, Object> params = new HashMap<>(3);
        params.put("userId", loggedUser.getId());
        params.put("gridId", gridId);
        Long stateId = HQL_findSimpleLong(""
            + " SELECT c.stateId"
            + " FROM " + GridStateByUser.class.getCanonicalName() + " c"
            + " WHERE c.id.userId = :userId"
            + " AND c.id.gridId = :gridId",
            params,
            true,
            CacheRegion.FAVORITE,
            0
        );
        if (stateId == null) {
            return null;
        }
        final GridStateUserHelper helper = new GridStateUserHelper(this);
        final GridStateDTO state = helper.load(stateId, loggedUser.getId());
        return state;
    }
    
    
    /**
     * Recibe `departmentRegistry` con los usuarios que se les QUITARÁN o AGREGARÁN pendientes, en
     * caso de QUITAR, Es importante respaldar su valor antes de haber quitado sus pendeintes.
     * 
     * @param userId
     * @param departmentRegistries
     * @return 
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean refreshFormApproversPendings(final Long userId, List<FormApproverAnalystByDepartment> departmentRegistries) {
        final Map<Long, ILoggedUser> users = new HashMap<>();
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(5);
        taskExecutor.setMaxPoolSize(10);
        taskExecutor.setKeepAliveSeconds(40);
        // Hace que metodo taskExecutor.shutdown() espere a que terminen todos les threads antes de terminar
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(Integer.MAX_VALUE);
        taskExecutor.initialize();
        // Mueve pendientes de verificar ajuste
        formPendingNormalizeUserTaskExecutorSchedule(taskExecutor,departmentRegistries,users,
                userId,FormApproverAnalystByDepartment::getAdjustmentApproverUserId,
                FormApproverAnalystByDepartment::getAdjustmentWhileAwayUserId,
                FormPending::normalizeVerifyAdjustment
        );
        // Mueve pendientes de verificar cancelacion
        formPendingNormalizeUserTaskExecutorSchedule(taskExecutor,departmentRegistries,users,
                userId,FormApproverAnalystByDepartment::getCancelationApproverUserId,
                FormApproverAnalystByDepartment::getCancelationWhileAwayUserId,
                FormPending::normalizeVerifyCancel
        );
        // Mueve pendiente de verificar reapertura
        formPendingNormalizeUserTaskExecutorSchedule(taskExecutor,departmentRegistries,users,
                userId,FormApproverAnalystByDepartment::getReopenApproverUserId,
                FormApproverAnalystByDepartment::getReopenWhileAwayUserId,
                FormPending::normalizeVerifyReopen
        );
        FormPending pending = new FormPending(this);
        pending.normalize(getLoggedUserById(userId, users));
        taskExecutor.shutdown();
        return true;
    }

    /**
     * Paraleliza acciones normalize de form pending, cuando hay muchos usuarios diferentes agiliza enormemente la normalización
     * cuando son pocos usuarios diferentes puede hacer incluso un poco mas lento el proceso
     * @param executor thread pool sobre el cual paralelizar
     * @param data listado para extraer el user id
     * @param userCache mapa para tener un "cache" basado en userId y no hacer mas queries de los necesarios
     * @param loggedUserId usuario con la session activa
     * @param getMatchCurrentUserId usuario contra el que comparar la session activa para ver si se ejecuta proceso
     * @param getter function para extraer el userId de los objetos de la lista
     * @param executeFunction refrencia a función en FormPending para ejecutar en paralelo
     * @param <T> Tipo de objeto en la lista
     */
    private <T> void formPendingNormalizeUserTaskExecutorSchedule(
            TaskExecutor executor, List<T> data, Map<Long, ILoggedUser> userCache,
            Long loggedUserId,Function<T,Long> getMatchCurrentUserId,
            Function<T, Long> getter, BiConsumer<FormPending, ILoggedUser> executeFunction) {
        //TODO: Falta manejar múltiples peticiones simultaneas por cada usuario y método y el manejo de hilos
        // al apagar/reiniciar la aplicación (Evento muy común en el caso de desarrollo) Un ejemplo sería
        // con algo similar a ApeLazyExecutor donde se tenga un ID único para cada petición:
        //      final ApeLazyExecutor lazyExecutor = BnextDaemonUtil.getRunningInstance(ApeLazyExecutor.class);
        //      lazyExecutor.execute(requestId, pending.getApe(), trigger, loggedUser);
        data.stream()
                // Se filtra para que solo modifique el pendiente que le pertence al usuario logueado
                .filter(d -> Objects.equals(loggedUserId, getMatchCurrentUserId.apply(d)))
                .map(getter)
                .filter(Objects::nonNull)
                .distinct()
                .map(id -> getLoggedUserById(id, userCache))
                .map(user -> (Runnable) () -> {
                    FormPending pending = new FormPending(Utilities.getUntypedDAO());
                    executeFunction.accept(pending, user);
                })
                .forEach(executor::execute);
    }

    
    private ILoggedUser getLoggedUserById(final Long userId, final Map<Long, ILoggedUser> users) {
        if (users.containsKey(userId)) {
            return users.get(userId);
        }
        final ILoggedUser user = UserUtils.getLoggedUserById(userId);
        users.put(userId, user);
        return user;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean notAwayAnymore(final Long userId) {
        if(
            // Reactivate User
            HQL_updateByQuery(""
                + " UPDATE " + User.class.getCanonicalName() + " b "
                + " SET "
                        + " b.isAway = false"
                        + ",b.isAwayReason = null"
                        + ",b.isAwaySince = null"
                + " WHERE"
                        + " b.id = :userId", "userId", userId
            ) > 0
        ) {
            // Cancelación
            HQL_updateByQuery(""
                + " UPDATE " + BusinessUnitDepartment.class.getCanonicalName() + " b "
                + " SET "
                        + " b.cancelationWhileAwayUserId = null"
                + " WHERE"
                        + " b.cancelationApproverUserId = :userId"
                        + " AND b.cancelationWhileAwayUserId IS NOT NULL", "userId", userId
            );
            // Re-apertura
            HQL_updateByQuery(""
                + " UPDATE " + BusinessUnitDepartment.class.getCanonicalName() + " b "
                + " SET "
                        + " b.reopenWhileAwayUserId = null "
                + " WHERE"
                        + " b.reopenApproverUserId = :userId"
                        + " AND b.reopenWhileAwayUserId IS NOT NULL ", "userId", userId
            );
            // Ajuste
            HQL_updateByQuery(""
                + " UPDATE " + BusinessUnitDepartment.class.getCanonicalName() + " b "
                + " SET "
                        + " b.adjustmentWhileAwayUserId = null "
                + " WHERE"
                        + " b.adjustmentApproverUserId = :userId"
                        + " AND b.adjustmentWhileAwayUserId IS NOT NULL ", "userId", userId
            );
            return true;
        }
        return false;
    }
    
    private GridStateByUser getGridStateUserData(final String gridId, final Long userId) {
        final Map<String, Object> params = new HashMap<>(3);
        params.put("userId", userId);
        params.put("gridId", gridId);
        return (GridStateByUser)HQL_findSimpleObject(""
            + " SELECT c"
            + " FROM " + GridStateByUser.class.getCanonicalName() + " c"
            + " WHERE c.id.userId = :userId"
            + " AND c.id.gridId = :gridId",
            params,
            true,
            CacheRegion.FAVORITE,
            0
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Map> getAllUsersToDefineForm(ILoggedUser loggeUser) {
        StringBuilder query = getAllUsersQuery(loggeUser);
        return HQL_findByQuery(query.toString());
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getAllUsersToDefineForm(SortedPagedFilter filter, ILoggedUser loggedUser) {
        StringBuilder query = getAllUsersQuery(loggedUser);
        return HQL_getRows(query.toString().replace("LEFT JOIN c.defaultWorkflowPosition puesto", " INNER JOIN c.defaultWorkflowPosition puesto "), filter);
    }
    
    private StringBuilder getAllUsersQuery(ILoggedUser loggedUser) {
        StringBuilder sb = new StringBuilder(300);
            sb.append(" "
                + " SELECT new map("
                    + " c.id as userId, "
                    + " c.description as userDescription, "
                    + " c.correo as correo, "
                    + " puesto.description as positionDescription, "
                    + " c.cuenta as account, "
                    + " c.businessUnitDepartment.departmentId as departmentId,"
                    + " c.businessUnitDepartment.departmentDescription as departmentDescription,"
                    + " string_agg(positions.description) as positions, "
                    + " string_agg(participationDepartments.description) as departmentsWhereParticipate, "
                    + " string_agg(profiles.description) as profiles "
                + " )  "
                + " FROM ")
                .append(User.class.getCanonicalName()).append(" c ")
                .append(" LEFT JOIN c.defaultWorkflowPosition puesto ")
                .append(" LEFT JOIN c.puestos positions ")
                .append(" LEFT JOIN positions.perfil profiles  ")
                .append( "LEFT JOIN positions.departamentos participationDepartments" )
                .append(" "
                + " WHERE ("
                + " c.id in ( "
                    + " SELECT up.user.id "
                    + " FROM ").append(UserPosition.class.getCanonicalName()).append(" up "
                    + " WHERE up.businessUnitId IN (").append( ""
                        + " SELECT upl.businessUnitId "
                        + " FROM ").append(UserPosition.class.getCanonicalName()).append(" upl "
                        + " WHERE upl.user.id = ").append(loggedUser.getId()).append(" ) "
                ).append(" ) "
                    + " OR c.businessUnitId in ( "
                        + " SELECT loggeduser.businessUnitId "
                        + " FROM ").append(User.class.getCanonicalName()).append(" loggeduser "
                        + " WHERE loggeduser.id = ").append(loggedUser.getId()).append(" "
                        + " ) "
                    + " ) "
                + " AND c.status = ").append(User.STATUS.ACTIVE.getValue()).append(
                " AND c.deleted = 0 "
                + " GROUP BY "
                    + " c.id, "
                    + " c.description, "
                    + " c.correo, "
                    + " puesto.description, "
                    + " c.cuenta, "
                    + " c.businessUnitDepartment.departmentId,"
                    + " c.businessUnitDepartment.departmentDescription ");
            return sb;
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public UpdateBossStatus updateUserBoss(final String userCode, final String bossCode, final ILoggedUser loggedUser) {
        if (userCode == null || userCode.isEmpty()) {
            return UpdateBossStatus.USER_CODE_NULL;
        }
        if (bossCode == null || bossCode.isEmpty()) {
            return UpdateBossStatus.BOSS_CODE_NULL;
        }
        final UserEscalableDTO user = (UserEscalableDTO) HQL_findSimpleObject(
                LOAD_USER_ESCALABLE_HQL, "code", userCode
        );
        if (user == null) {
            return UpdateBossStatus.USER_NOT_FOUND;
        } else if (user.getUserStatus() == null
                || User.STATUS.INACTIVE.getValue().equals(user.getUserStatus())) {
            return UpdateBossStatus.USER_INACTIVE;
        }
        final UserEscalableDTO boss = (UserEscalableDTO) HQL_findSimpleObject(
                LOAD_USER_ESCALABLE_HQL, "code", bossCode
        );
        if (boss == null) {
            return UpdateBossStatus.BOSS_NOT_FOUND;
        } else if (boss.getUserStatus() == null
                || User.STATUS.INACTIVE.getValue().equals(boss.getUserStatus())) {
            return UpdateBossStatus.BOSS_INACTIVE;
        }
        final Long oldBossId = HQL_findSimpleLong(BOSS_SELECT_HQL, "code", userCode);
        if (Objects.equals(oldBossId, boss.getUserId())) {
            return UpdateBossStatus.ALREADY_UPDATED;
        }
        final UserCountPendingDTO pending = new UserCountPendingDTO();
        pending.setOldFillRequestIds(loadFillRequestPendingsCount(boss.getUserId()));
        pending.setOldRequestIds(loadRequestPendingsCount(boss.getUserId()));
        final Map<String, Object> params = new HashMap<>();
        params.put("bossId", boss.getUserId());
        params.put("userId", user.getUserId());
        params.put("lastModifiedBy", loggedUser.getId());
        params.put("lastModifiedDate", new Date());
        final Integer recordsUpdated = HQL_updateByQuery(""
                + " UPDATE  " + User.class.getCanonicalName() + " u"
                + " SET u.bossId = :bossId"
                + " , u.lastModifiedBy = :lastModifiedBy "
                + " , u.lastModifiedDate = :lastModifiedDate "
                + " WHERE u.id = :userId",
                params
        );
        if (Objects.equals(recordsUpdated, 1)) {
            getEntityManager().flush();
            pending.setNewFillRequestIds(loadFillRequestPendingsCount(boss.getUserId()));
            pending.setNewRequestIds(loadRequestPendingsCount(boss.getUserId()));
            getAspectJAutoProxy().changeBoss(new User(user.getUserId()), oldBossId, oldBossId, pending, loggedUser);
            return UpdateBossStatus.SUCCESS;
        } else {
            return UpdateBossStatus.UNKNOWN_ERROR;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean deleteUser(Long userId, Long loggedUserId) {
        return HQL_updateByQuery("" +
                "UPDATE " + User.class.getCanonicalName() + " u " +
                " SET u.deleted = 1, " +
                " u.lastModifiedBy = :lastModifiedBy, " +
                " u.lastModifiedDate = :lastModifiedDate, " +
                " u.version = (version + 1), " +
                " u.status = :status" +
                " WHERE u.id = :userId",
                ImmutableMap.of(
                        "userId", userId,
                        "lastModifiedBy", loggedUserId,
                        "lastModifiedDate", new Date(),
                        "status", User.STATUS.INACTIVE.getValue()
                )) >= 1;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean restoreUser(Long userId, Long loggedUserId) {
        return HQL_updateByQuery("" +
                "UPDATE " + User.class.getCanonicalName() + " u " +
                "SET u.deleted = 0," +
                " u.lastModifiedBy = :lastModifiedBy," +
                " u.lastModifiedDate = :lastModifiedDate, " +
                " u.version = (version + 1),  " +
                " u.status = :status" +
                " WHERE u.id = :userId",
                ImmutableMap.of(
                        "userId", userId,
                        "lastModifiedBy", loggedUserId,
                        "lastModifiedDate", new Date(),
                        "status", User.STATUS.ACTIVE.getValue()
                )) >= 1;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<IUser> deletedUsers(GridFilter filter) {
        return HQL_getRows("" +
                "SELECT NEW " + User.class.getCanonicalName() + " ( " +
                    " c.description," +
                    " c.correo," +
                    " c.id " +
                ") FROM " + User.class.getCanonicalName() + " c " +
                " WHERE c.deleted = 1 ", filter);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> businessUnitsByCorporativeUser(Long userId) {
        return HQL_findByQuery(" " +
            "SELECT " +
                "bu.id " +
            "FROM " + User.class.getCanonicalName() + " u " +
            "JOIN u.puestos up " +
            "JOIN up.perfil p " +
            "JOIN up.corp o " +
            "JOIN " + BusinessUnit.class.getCanonicalName() + " bu ON bu.organizationalUnitId = o.id " +
            "WHERE p.intBUsuarioCorporativo = 1 AND u.id = :userId ",
            ImmutableMap.of("userId", userId)
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> businessUnitDeparmentsByCorporativeUser(Long userId) {
        return HQL_findByQuery(" " +
            "SELECT " +
                "bud.departmentId " +
            "FROM " + User.class.getCanonicalName() + " u " +
            "JOIN u.puestos up " +
            "JOIN up.perfil p " +
            "JOIN up.corp o " +
            "JOIN " + BusinessUnit.class.getCanonicalName() + " bu ON bu.organizationalUnitId = o.id " +
            "JOIN " + BusinessUnitDepartment.class.getCanonicalName() + " bud ON bud.businessUnitId = bu.id " +
            "WHERE p.intBUsuarioCorporativo = 1 AND u.id = :userId ",
            ImmutableMap.of("userId", userId)
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IPlainUser loadPlainUser(Long userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        return (IPlainUser) HQL_findSimpleObject(" "
            + " SELECT NEW " + UserPlainDTO.class.getCanonicalName() + " ("
                + " c.id "
                + ", c.businessUnitId "
                + ", c.departmentId "
                + ", c.businessUnitDepartmentId "
                + ", c.description "
                + ", c.login "
                + ", c.code "
                + ", c.correo "
                + ", c.status "
                + ", c.deleted "
                + ", c.cuenta "
            + " ) "
            + " FROM " + User.class.getCanonicalName() + " c "
            + " WHERE c.id = :autorId",
            ImmutableMap.of("autorId", userId)
        );
    }
}
