/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package DPMS.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.CodeSequence.type;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import bnext.reference.IAuditable;
import java.text.DecimalFormat;
import java.util.Objects;
import javax.persistence.Table;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.framework.periodicity.util.ICommitmentTask;
import qms.framework.periodicity.util.ICommitmentTaskName;
import qms.framework.periodicity.util.IPeriodicEntity;
import qms.framework.util.SqlSequenceUtil;
import qms.util.EntityCommon;
import qms.util.QMSException;
import qms.util.interfaces.IPersistableCode;

/**
 *
 * <AUTHOR> Carlos Limas @ Block Networks S.A. de C.V. © 2014
 */
@Lazy
@Repository(value = "CodeSequence") 
@Scope(value = "singleton")
public class CodeSequenceDAO extends GenericDAOImpl<CodeSequence, Long> implements ICodeSequenceDAO {

    private static final int MAXIMUM_CODE_DIGITS = 9;
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    /**
     * Utilizar ICodeSequenceDAO.next(Class<? extends IAuditableEntity> entity)
     **/
    public String next(CodeSequence.type t) throws QMSException {
        return next(t, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String next(Class<? extends IAuditable> entity) throws QMSException {
        return next(entity.getAnnotation(Table.class).name(), null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public String nextRequiresNew(final IAuditable entity) throws QMSException {
        return next(entity);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String next(final IAuditable entity) throws QMSException {
        final Class<? extends IAuditable> entityClazz = entity.getClass();
        if (
            IPeriodicEntity.class.isInstance(entity)
        ) {
            IPeriodicEntity e = (IPeriodicEntity) entity;
            if (Objects.equals(e.getRecurrent(), IPeriodicEntity.RECURRENT.YES.getValue())) {
                return next(entityClazz.getAnnotation(Table.class).name(), null);
            }
            StringBuilder prefix = new StringBuilder(30);
            if (IPersistableCode.class.isInstance(entity)) {
                IPersistableCode c = (IPersistableCode) entity;
                if (c.getCode() == null || c.getCode().isEmpty()) {
                    String code = HQL_findSimpleString(""
                        + " SELECT e.code "
                        + " FROM " + entityClazz.getCanonicalName() + " e "
                        + " WHERE e.id = :recurrenceId", "recurrenceId", e.getRecurrenceId()
                    );
                    if (!code.isEmpty()) {
                        prefix.append(code);
                    }
                } else {
                    prefix.append(c.getCode());
                }
            }
            String commitmentTask = "EV";
            if (ICommitmentTaskName.class.isInstance(entity)) {
                commitmentTask = ((ICommitmentTaskName) e).getCommitmentTaskName();
            } else if (ICommitmentTask.class.isInstance(e)) {
                commitmentTask = ((ICommitmentTask)e).getCommitmentTask().toString();
            }
            prefix.append('-').append(commitmentTask).append('-');
            return prefix.toString() + next(prefix.toString()
                + entityClazz.getAnnotation(Table.class).name(), null
            );
        }
        return next(entityClazz.getAnnotation(Table.class).name(), null);
    }

    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String next(type codeSequenceType, CodeSequence ejemplo) throws QMSException {
        return next(codeSequenceType.getCode(), ejemplo); 
    }
    
    private String next(String codeSequenceType, CodeSequence ejemplo)  throws QMSException {
        return next(codeSequenceType, ejemplo, true);
    }
    
    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String next(String codeSequenceType, CodeSequence ejemplo, boolean format) throws QMSException {
        return next(codeSequenceType, ejemplo, format, false);
    }
    
    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public String nextRequireNew(String codeSequenceType, CodeSequence ejemplo, boolean format) throws QMSException {
        return next(codeSequenceType, ejemplo, format, false);
    }

    private String next(String codeSequenceType, CodeSequence ejemplo, boolean format, boolean skipExist) throws QMSException {
        if (getLogger().isDebugEnabled()) {
            getLogger().debug("--> this S@CSD: " + this.getClass().getSimpleName() + " - " +this.getSession().hashCode());
            getLogger().debug("--> this T@CSD: " + this.getSession().getTransaction().toString());
        }
        Integer contador;
        if (isSql2012OrGreater()) {
            final SqlSequenceUtil sequenceUtil = new SqlSequenceUtil(this);
            contador = sequenceUtil.next(SqlSequenceUtil.PREFIX + codeSequenceType.toUpperCase(), null, skipExist);
            return formatSequence(contador, format);
        } else {
            ejemplo = getNextSequence(ejemplo, codeSequenceType, null);
            //obtiene el valor del consecutivo actual
                contador = ejemplo.getSequenceValue();

            //Se incrementa en 1 y se actualiza en la base de datos
            contador++;
            ejemplo.setSequenceValue(contador);
            ejemplo = this.makePersistent(ejemplo);
            detachEntity();
            //Si todo esta ok, se asigna la referencia a la investigacion
            if (ejemplo!=null){
                    return formatSequence(contador, format);
            }
        }
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public String next(String codeSequenceType) throws QMSException {
        String name = null;
        String result = null;
        try {
            name = codeSequenceType.toUpperCase();
            result = getAspectJAutoProxy().next(name, false);
        } catch (Exception e) {
            if (
                e.getMessage() != null
                && e.getMessage().contains(name)
            ) {
                result = getAspectJAutoProxy().next(name, true);
            } else {
                throw e;
            }
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public String next(String codeSequenceType, boolean skipExist) throws QMSException {
        String name = codeSequenceType.toUpperCase();
        try {
            return this.next(name, null, true, skipExist);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String next(String codeSequenceType, StandardEntity entityInstance, CodeSequence ejemplo, boolean format) throws QMSException {
        if (getLogger().isDebugEnabled()) {
            getLogger().debug("--> this S@CSD: " + this.getClass().getSimpleName() + " - " + this.getSession().hashCode());
            getLogger().debug("--> this T@CSD: " + this.getSession().getTransaction().toString());
        }
        Integer contador;
        if (isSql2012OrGreater()) {
            final SqlSequenceUtil sequenceUtil = new SqlSequenceUtil(this);
            contador = sequenceUtil.next(SqlSequenceUtil.PREFIX + codeSequenceType.toUpperCase(), entityInstance);
            return formatSequence(contador, format);
        } else {
            ejemplo = getNextSequence(ejemplo, codeSequenceType, entityInstance);
            //obtiene el valor del consecutivo actual
            contador = ejemplo.getSequenceValue();

            //Se incrementa en 1 y se actualiza en la base de datos
            contador++;
            ejemplo.setSequenceValue(contador);
            ejemplo = this.makePersistent(ejemplo);
            detachEntity();
            //Si todo esta ok, se asigna la referencia a la investigacion
            if (ejemplo != null) {
                return formatSequence(contador, format);
            }
        }
        return null;
    }
    
    private String formatSequence(final Integer value, final boolean format) {
        if (format) {
            return new DecimalFormat(getCodeDigitsPattern()).format(value);
        } else {
            return value.toString();
        }
    }

    private CodeSequence getNextSequence(CodeSequence ejemplo, String codeSequenceType, StandardEntity entityInstance) {
        if (ejemplo == null) {
            ejemplo = new CodeSequence();
        }
        ejemplo.setType(codeSequenceType);
        //Obteniendo consecutivo de investigacion en base a pais y tipo de inv.
        //ConsecutivoDAOHibernate conDAO = new ConsecutivoDAOHibernate();
        ejemplo.setLastModificationDate(null);
        //Se busca el registro del consecutivo
        CodeSequence temp = (CodeSequence) this.HQL_findSimpleObject(""
                + " SELECT c FROM"
                + " DPMS.Mapping.CodeSequence c "
                + " WHERE c.type = :type", "type", codeSequenceType);
        //Si busca el registro del contador
        if (temp == null) {
            //Si no encuentra el registro, se crea uno nuevo
            ejemplo.setId(-1L);
            final SqlSequenceUtil sequenceUtil = new SqlSequenceUtil(this);
            Integer defaultValue = sequenceUtil.generateDefaultValue(entityInstance);
            if (defaultValue == null) {
                defaultValue = 0;
            }
            ejemplo.setSequenceValue(defaultValue);
        } else {
            ejemplo = temp;
        }
        return ejemplo;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void set(final String codeSequenceType, final Integer sequence) throws QMSException {
        if (isSql2012OrGreater()) {
            final SqlSequenceUtil sequenceUtil = new SqlSequenceUtil(this); 
            sequenceUtil.rebuild(SqlSequenceUtil.PREFIX + codeSequenceType.toUpperCase(), sequence);
        } else {
        final CodeSequence temp = getNextSequence(null, codeSequenceType, null);
        temp.setSequenceValue(sequence);
        makePersistent(temp);
        detachEntity();
    }
    }
    
    private String getCodeDigitsPattern() {
        final Integer minDigits = Utilities.getSettings().getMinimumCodeDigits();
        Integer optDigits = MAXIMUM_CODE_DIGITS - minDigits;
        if (optDigits <= 0) {
            optDigits = MAXIMUM_CODE_DIGITS;
        }
        return EntityCommon.getCodePreviewPattern(optDigits)
                + StringUtils.repeat('0', minDigits);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean hasSQLSequences() {
        return isSql2012OrGreater();
    }


    
    /**
     * Try to return the current AOP proxy. This method must be called in internal
     * calls inside the DAO so Development environments
     * are the sames as the aspectj-maven-plugin compilation.
     * Check Maven property spring-aop-file for more details.
     *
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public ICodeSequenceDAO getAspectJAutoProxy() {
        return super.getAspectJAutoProxy(ICodeSequenceDAO.class);
    }
}
