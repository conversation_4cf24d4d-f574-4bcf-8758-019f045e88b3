package DPMS.DAO;

import DPMS.DAOInterface.IScheduledServicesDAO;
import DPMS.Mapping.ScheduledServices;
import Framework.DAO.GenericDAOImpl;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.util.interfaces.IGridFilter;

@Lazy
@Repository(value = "HibernateDAO_ScheduledServices")
@Scope(value = "singleton")
public class HibernateDAO_ScheduledServices extends GenericDAOImpl<ScheduledServices, Long> implements IScheduledServicesDAO {

    private static final String validEntitiesFilter = ""
            + "exists ("
            + "SELECT dept.id FROM "
            + "DPMS.Mapping.User as u "
            + "JOIN u.puestos as p "
            + "JOIN p.departamentos as dept "
            + "JOIN p.perfil as perfil "
            + "WHERE dept.businessUnitId = c.business_unit_id "
            + "AND u.id = :userId AND 1 IN (:servicios)"
            + ")";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(IGridFilter filter, String userId, ProfileServices[] servicio, boolean isAdmin) {
        filter.getCriteria().put("<filtered-entity>", isAdmin ? ""
                : validEntitiesFilter.replace(":userId", userId).replace(":servicios", ProfileServices.getCodedServices("perfil", servicio))
        );
    }
}
