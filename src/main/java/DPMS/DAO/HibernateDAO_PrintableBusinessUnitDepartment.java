package DPMS.DAO;

import DPMS.DAOInterface.IPrintableBusinessUnitDepartmentDAO;
import DPMS.Mapping.PrintableBusinessUnitDepartment;
import DPMS.Mapping.PrintableBusinessUnitDepartmentPK;
import Framework.Action.SessionViewer;
import Framework.Config.SortedPagedFilter;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import java.util.List;
import mx.bnext.access.ProfileServices;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "HibernateDAO_PrintableBusinessUnitDepartment")
@Scope(value = "singleton")
public class HibernateDAO_PrintableBusinessUnitDepartment extends GenericDAOImpl<PrintableBusinessUnitDepartment, PrintableBusinessUnitDepartmentPK> implements IPrintableBusinessUnitDepartmentDAO {

    private static final String validEntitiesFilterByBusinessUnitDepartment = ""
            + " exists ( "
            + " SELECT dept.id FROM "
            + " DPMS.Mapping.User as u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.id = c.businessUnitDepartment.id "
            + " AND u.id = :userId "
            + " AND 1 IN (:servicios)"
            + ")";

    private static final String validEntitiesFilterByBusinessUnit = ""
            + " exists ( "
            + " SELECT dept.id FROM "
            + " DPMS.Mapping.User as u "
            + " JOIN u.puestos as p "
            + " JOIN p.departamentos as dept "
            + " JOIN p.perfil as perfil "
            + " WHERE dept.businessUnitId = c.businessUnitDepartment.businessUnitId "
            + " AND u.id = :userId "
            + " AND 1 IN (:servicios)"
            + ")";

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void setValidEntitiesFilter(SortedPagedFilter filter, Long userId, ProfileServices[] servicio, Boolean isAdmin, Boolean isManager) {
        filter.getCriteria().put("<filtered-entity>", (isAdmin ? "" : isManager ? validEntitiesFilterByBusinessUnit 
                : validEntitiesFilterByBusinessUnitDepartment).replace(":userId", userId.toString()).replace(":servicios", ProfileServices.getCodedServices("perfil", servicio))
        );
    }

    /**
     * Implementacion del metodo para guardar relación imprimible planta-departamento
     *
     * @param entity Lista de relación imprimible planta-departamento a guardar/actualizar
     * @return GenericSaveHandle con los resultados de la operacion
     * <AUTHOR> Guadalupe Quintanilla Flores
     * @since 2.4.0.7
     */
    @Override  
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(Long businessUnitDepartmentId, List<Long> printableIds, Long userId) {
        GenericSaveHandle gs = new GenericSaveHandle();
        PrintableBusinessUnitDepartment ent = null;
        int delete = this.HQL_updateByQuery(""
                + " DELETE"
                + " FROM " + PrintableBusinessUnitDepartment.class.getCanonicalName()
                + " WHERE businessUnitDepartment.id = " + businessUnitDepartmentId
        );
        for (Long printableId : printableIds) {
            ent = new PrintableBusinessUnitDepartment(printableId, businessUnitDepartmentId);
            ent = this.makePersistent(ent, userId);
        }
        if (ent != null) {
            gs.setOperationEstatus(1);
            if(delete == 1){
                gs.setSuccessMessage(SessionViewer.EDIT_SUCCESS);
            }
        } else {
            gs.setSuccessMessage(SessionViewer.ERROR);
        }
        return gs;
    }

}
