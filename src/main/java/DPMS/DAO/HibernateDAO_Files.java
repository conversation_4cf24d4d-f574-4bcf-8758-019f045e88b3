package DPMS.DAO;

import DPMS.DAOInterface.IFilesDAO;
import DPMS.Mapping.Files;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import bnext.reference.document.FileRef;
import com.google.common.io.ByteStreams;
import jakarta.activation.DataHandler;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.util.ByteArrayDataSource;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Path;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import mx.bnext.core.errors.ClientAbortException;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.file.IFileData;
import mx.bnext.core.security.CryptoUtils;
import mx.bnext.core.security.ISecurityUser;
import mx.bnext.core.util.KnownExceptions;
import org.hibernate.HibernateException;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.framework.dto.FilePlainDTO;
import qms.framework.dto.UploadFileResult;
import qms.framework.entity.FileLogging;
import qms.framework.entity.PdfPage;
import qms.framework.file.FileManager;
import qms.framework.interfaces.IPlainFile;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.FileLoggingType;
import qms.util.BindUtil;

@Lazy
@Repository(value = "HibernateDAO_Files")
@Scope(value = "singleton")
public class HibernateDAO_Files extends GenericDAOImpl<Files, Long> implements IFilesDAO {

    public final static String FILE_AUTODESK_EXTENSION = "dwg";
    
    private final static String DATABASE_FULL = "DATABASE_FULL";

    private Integer SQL_writeBlobToOutput(
            final Long id,
            final String table,
            final String columnId,
            final String columnBlob,
            final OutputStream output
    ) throws SQLException {
        final String sql = ""
                + " SELECT c." + columnBlob
                + " FROM " + table + " c"
                + " WHERE c." + columnId + " = ?";
        final String sqlParsed = sql.replace("?", id.toString());
        return getSession().doReturningWork(work -> {
        try {
                    try (final PreparedStatement statement = work.prepareStatement(sql)) {
                statement.setLong(1, id);
                try (final ResultSet result = statement.executeQuery()) {
                    if (result.next()) {
                        try (final InputStream columnInput = result.getBinaryStream(columnBlob)) {
                            if (columnInput == null) {
                                getLogger().warn("Null binary steam for query [" + sqlParsed + "].", sqlParsed);
                                return 0;
                            } else {
                                ByteStreams.copy(columnInput, output);
                                return 1;
                            }
                        }
                    }
                }
            }
        } catch (final IOException e) {
            if (KnownExceptions.hasClientAborted(e)) {
                throw new ClientAbortException(e);
            }
            final String message = ExceptionUtils.getRootCauseMessage(e);
            getLogger().debug("Failed while wrting Blob for column: '" + columnBlob + "' for table: '" + table + "' and id [" + id + "] due to: " + message);
            throw new SQLException(
                    "Failed while uploading Blob for column: '" + columnBlob + "' for table: '" + table + "' and id [" + id + "]",
                    e
            );
        } catch (final HibernateException | SQLException e) {
            if (KnownExceptions.hasClientAborted(e)) {
                throw new ClientAbortException(e);
            }
            throw new SQLException(
                    "Failed while writing Blob for column: '" + columnBlob + "' for table: '" + table + "' and id [" + id + "]",
                    e
            );
        }
        return 0;
        });
    }

    private Integer SQL_uploadBlob(
            final Long id,
            final String table,
            final String columnId,
            final String columnBlob,
            final Path blobPath
    ) throws SQLException {
        final String sql = ""
                + " UPDATE " + table
                + " SET " + columnBlob + " = ?"
                + " WHERE " + columnId + " = ?";
        return getSession().doReturningWork(work -> {
        try {
            try (
                    final PreparedStatement statement = work.prepareStatement(sql);
                final InputStream blobInput = java.nio.file.Files.newInputStream(blobPath)
            ) {
                statement.setBinaryStream(1, blobInput);
                statement.setLong(2, id);
                return statement.executeUpdate();
            }
        } catch (final IOException e) {
            if (KnownExceptions.hasClientAborted(e)) {
                throw new ClientAbortException(e);
            }
            final String message = ExceptionUtils.getRootCauseMessage(e);
            getLogger().debug("Failed while uploading Blob for column: '" + columnBlob + "' for table: '" + table + "' and id [" + id + "] due to: " + message);
            throw new SQLException(
                    "Failed while uploading Blob for column: '" + columnBlob + "' for table: '" + table + "' and id [" + id + "]",
                    e
            );
        } catch (final HibernateException | SQLException e) {
            if (KnownExceptions.hasClientAborted(e)) {
                throw new ClientAbortException(e);
            }
            throw new SQLException(
                    "Failed while uploading Blob for column: '" + columnBlob + "' for table: '" + table + "' and id [" + id + "]",
                    e
            );
        }
        });
    }

    /**
     * Gets number of pages of file
     *
     * @param fileId Id of the file
     * @return Number of pages of file
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer findNumberPages(Long fileId) {
        return HQL_findSimpleInteger(""
                + " SELECT c.numberPages "
                + " FROM " + Files.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", fileId);
    }
    
    /**
     * Finds if exists PDF file in file
     * @param fileId Id of the file
     * @return True if exists PDF file, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean existsFilePdf(final Long fileId) {
        return HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id"
                + " AND c.hasPdf = 1", "id", fileId) > 0;
    }

    /**
     * Finds if exists content in file
     * @param fileId Id of the file
     * @return True if exists content in file, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean existsContent(final Long fileId) {
        return HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id"
                + " AND c.contentSize IS NOT NULL", "id", fileId) > 0;
    }
    
    /**
     * Finds if file is busy
     * @param fileId Id of the file
     * @return True if the file is busy, false otherwise
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean busyFile(final Long fileId) {
        return HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id"
                + " AND c.busy = 1", "id", fileId) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> getPdfPageIds(final Long fileId) {
        return HQL_findByQuery(""
                + " SELECT c.id"
                + " FROM " + PdfPage.class.getCanonicalName() + " c "
                + " WHERE c.fileId = :id", "id", fileId);
    }
    
    /**
     *  Set number of pages of a PDF file
     * @param fileId Id of the file
     * @param numberPages Number of total pages
     * @param numberSavedPages Number saved pages
     * @return 1 if the file was updated
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateNumberPages(final Long fileId, final Integer numberPages, final Integer numberSavedPages) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("numberPages", numberPages);
        params.put("numberSavedPages", numberSavedPages);
        params.put("id", fileId);
        return HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName()
                + " SET "
                    + " numberPages = :numberPages,"
                    + " numberSavedPages = :numberSavedPages"
                + " WHERE id = :id", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateHasPassword(final Long fileId, final Boolean hasPassword) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("id", fileId);
        params.put("hasPassword", hasPassword);
        return HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName()
                + " SET "
                    + " hasPassword = :hasPassword"
                + " WHERE id = :id", params);
    }

    /**
     * Permite subir el contenido de un archivo de base de datos al output
     * @param fileId Id del archivo - files
     * @param file Archivo a subir
     * @param sha512 sha512 del archivo
     * @return Número de registros actualizados
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer persistFileContent(final Long fileId, final Path file, final String sha512) {
        try {
            final Integer upload = SQL_uploadBlob(fileId, "files", "id", FileUtils.COLUMN_FILES_CONTENT, file);
            if (upload == null || upload == 0) {
                return upload;
            }
            final Long length = java.nio.file.Files.size(file);
            final Map<String, Object> params = new HashMap<>(4);
            params.put("contentSize", length);
            params.put("contentSha512", sha512);
            params.put("id", fileId);
            final Integer result = HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName() + " c "
                    + " SET "
                    + " c.contentSize = :contentSize,"
                    + " c.contentSha512 = :contentSha512"
                + " WHERE c.id = :id", params);
            return result;
        } catch (final FileNotFoundException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to save content of file: '{}', file not found", new Object[]{file.getFileName(), ex});
            return null;
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to save content of file: '{}'", new Object[]{file.getFileName(), ex});
            return null;
        }
    }

    /**
     * Permite subir la vista previa de un archivo de base de datos al output
     * @param fileId Id del archivo - files
     * @param file Archivo a subir
     * @return
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer persistFileThumbnail(final Long fileId, final Path file) {
        try {
            final Integer upload = SQL_uploadBlob(fileId, "files", "id", FileUtils.THUMBNAIL_COLUMN, file);
            if (upload == null || upload == 0) {
                return upload;
            }
            final Long length = java.nio.file.Files.size(file);
            final Map<String, Object> params = new HashMap<>(4);
            params.put("thumbnailSize", length);
            params.put("thumbnailSha512", CryptoUtils.sha512(file));
            params.put("id", fileId);
            return HQL_updateByQuery(""
                    + " UPDATE " + Files.class.getCanonicalName() + " c "
                    + " SET"
                    + " c.thumbnailSize = :thumbnailSize,"
                    + " c.thumbnailSha512 = :thumbnailSha512"
                    + " WHERE c.id = :id", params);
        } catch (final FileNotFoundException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist thumbnail of file: '{}', file not found", new Object[]{file.getFileName(), ex});
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist thumbnail of file: '{}'", new Object[]{file.getFileName(), ex});
        }
        return 0;
    }

    /**
     * Permite subir la vista previa de la vista previa de la pagina de un pdf de un archivo de base de datos al output
     *
     * @param pdfPageId Id del archivo - files
     * @param file Archivo a subir
     * @return
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer persistPdfPageThumbnail(final Long pdfPageId, final Path file) {
        try {
            final Integer upload = SQL_uploadBlob(pdfPageId, "file_pdf_pages", "id", FileUtils.THUMBNAIL_COLUMN, file);
            if (upload == null || upload == 0) {
                return upload;
            }
            final Long length = java.nio.file.Files.size(file);
            final String sha512 = CryptoUtils.sha512(file);
            final Map<String, Object> params = new HashMap<>(4);
            params.put("thumbnailSize", length);
            params.put("thumbnailSha512", sha512);
            params.put("id", pdfPageId);
            return HQL_updateByQuery(""
                    + " UPDATE " + PdfPage.class.getCanonicalName() + " c "
                    + " SET"
                    + " c.thumbnailSize = :thumbnailSize,"
                    + " c.thumbnailSha512 = :thumbnailSha512,"
                    + " c.hasThumbnail = 1"
                    + " WHERE c.id = :id", params);
        } catch (final FileNotFoundException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist pdf page thumbnail of file: '{}', file not found", new Object[]{file.getFileName(), ex});
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist pdf page thumbnail of file: '{}'", new Object[]{file.getFileName(), ex});
        }
        return 0;
    }
    
    /**
     * Permite subir el pdf de un archivo de base de datos al output
     * @param fileId Id del archivo - files
     * @param file Archivo a subir
     * @return 
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer persistFilePdf(final Long fileId, final Path file) {
        try {
            final Integer upload = SQL_uploadBlob(fileId, "files", "id", FileUtils.COLUMN_FILES_PDF, file);
            if (upload == null || upload == 0) {
                return upload;
            }
            //TODO: Subir con Stream, ver SQL_writeBlobToOutput
            final Map<String, Object> params = new HashMap<>(4);
            params.put("pdfSize", java.nio.file.Files.size(file));
            params.put("pdfSha512", CryptoUtils.sha512(file));
            params.put("id", fileId);
            return HQL_updateByQuery(""
                    + " UPDATE " + Files.class.getCanonicalName() + " c "
                    + " SET"
                    + " c.pdfSize = :pdfSize,"
                    + " c.pdfSha512 = :pdfSha512,"
                    + " c.hasPdf = 1"
                    + " WHERE c.id = :id", params);
        } catch (final FileNotFoundException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist pdf of file: '{}', file not found", new Object[]{file.getFileName(), ex});
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist pdf of file: '{}'", new Object[]{file.getFileName(), ex});
        }
        return 0;
    }
    
    /**
     * Permite subir la imagen de un archivo de base de datos al output
     * @param pdfPageId Id del archivo - file_pdf_pages
     * @param page Archivo a subir
     * @return 
     * @throws java.io.IOException
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer persistFilePdfPage(final Long pdfPageId, final Path page) throws IOException {
        try {
            final Integer upload = SQL_uploadBlob(pdfPageId, "file_pdf_pages", "id", "image", page);
            if (upload == null || upload == 0) {
                return upload;
            }
            final Long length = java.nio.file.Files.size(page);
            final String sha512 = CryptoUtils.sha512(page);
            final Map<String, Object> params = new HashMap<>(4);
            params.put("imageSize", length);
            params.put("imageSha512", sha512);
            params.put("id", pdfPageId);
            return HQL_updateByQuery(""
                    + " UPDATE " + PdfPage.class.getCanonicalName() + " c "
                    + " SET"
                    + " c.imageSize = :imageSize,"
                    + " c.imageSha512 = :imageSha512,"
                    + " c.hasImage = 1"
                    + " WHERE c.id = :id", params
            );
        } catch (final FileNotFoundException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist pdf page of file: '{}', file not found", new Object[]{page.getFileName(), ex});
            throw ex;
        } catch (final IOException | SQLException ex) {
            if (KnownExceptions.hasClientAborted(ex)) {
                throw new ClientAbortException(ex);
            }
            getLogger().error("Failed to persist pdf page of file: '{}'", new Object[]{page.getFileName(), ex});
        }
        return 0;
    } 
    


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getPdfPageId(final Long fileId, final Integer pageNumber) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("fileId", fileId);
        params.put("page", pageNumber);
        final Long id = HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + PdfPage.class.getCanonicalName() + " c"
                + " WHERE c.fileId = :fileId"
                + " AND c.page = :page", params);
        return id;
    }
    
    /**
     * Permite descargar  el content de un archivo de base de datos al output
     * @param fileId Id del archivo - files
     * @param output An output stream of bytes
     * @return 
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer writeContentToOutput(final Long fileId, final OutputStream output)
            throws IOException, SQLException {
        return SQL_writeBlobToOutput(fileId, "files", "id", FileUtils.COLUMN_FILES_CONTENT, output);
    }
    
    /**
     * Permite descargar  el pdf de un archivo de base de datos al output
     * @param fileId Id del archivo - files
     * @param output An output stream of bytes
     * @return 
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer writePdfToOutput(final Long fileId, final OutputStream output)
     throws IOException, SQLException {
        return SQL_writeBlobToOutput(fileId, "files", "id", FileUtils.COLUMN_FILES_PDF, output);
        } 
    
    /**
     * Permite descargar la vista previa de un archivo de base de datos al output
     * @param fileId Id del archivo - files
     * @param output An output stream of bytes
     * @return 
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer writeThumbnailToOutput(final Long fileId, final OutputStream output)
        throws IOException, SQLException{
        return SQL_writeBlobToOutput(fileId, "files", "id", "thumbnail", output);
        }
    
    /**
     * Permite descargar la vista previa de un pagina del pdf de un archivo de base de datos al output
     * @param pdfPageId Id del la pagina de PDF del archivo - files
     * @param output An output stream of bytes
     * @return 
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Override
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer writePdfPageThumbnailToOutput(final Long pdfPageId, final OutputStream output)
        throws IOException, SQLException{
        return SQL_writeBlobToOutput(pdfPageId, "file_pdf_pages", "id", "thumbnail", output);
    }
    
    /**
     * Permite descargar la imagen de un archivo de base de datos al output
     * @param pdfPageId Id del archivo - file_pdf_pages
     * @param output An output stream of bytes
     * @return 
     * @deprecated Utilizar FileCacheManager con el cache de archivos.
     */
    @Deprecated
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer writePdfPageToOutput(final Long pdfPageId, final OutputStream output)
        throws IOException, SQLException{
        return SQL_writeBlobToOutput(pdfPageId, "file_pdf_pages", "id", "image", output);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class}, readOnly = true)
    public Integer markFileBusy(Long fileId) {
        return HQL_updateByQuery(""
                + " UPDATE  " + FileRef.class.getCanonicalName() + " c"
                + " SET c.busy = 1"
                + " WHERE c.id = :fileId",
                "fileId",
                fileId
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public Integer releaseFileBusy(Long fileId) {
        return HQL_updateByQuery(""
                + " UPDATE " + FileRef.class.getCanonicalName() + " c"
                + " SET c.busy = 0"
                + " WHERE c.id = :fileId",
                "fileId",
                fileId
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public Integer markFileBusy(String fileCode) {
        return HQL_updateByQuery(""
                + " UPDATE  " + FileRef.class.getCanonicalName() + " c"
                + " SET c.busy = 1"
                + " WHERE c.code = :fileCode ",
                "fileCode",
                fileCode
        );
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public Integer releaseFileBusy(String fileCode) {
        return HQL_updateByQuery(""
                + " UPDATE " + FileRef.class.getCanonicalName() + " c"
                + " SET c.busy = 0"
                + " WHERE c.code = :fileCode ",
                "fileCode", 
                fileCode
        );
    }
    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = {Exception.class})
    public Integer lockFile(Long fileId) {
        return HQL_updateByQuery(""
                + " UPDATE " + FileRef.class.getCanonicalName() + " c"
                + " SET c.busy = 1"
                + " WHERE c.id = :fileId",
                "fileId",
                fileId
        );
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public UploadFileResult parseUploadException(String fileName, Path file, Exception e) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        if(IS_SQL_SERVER
                && e instanceof org.springframework.orm.jpa.JpaSystemException
                && e.getCause() instanceof org.hibernate.HibernateException
                && e.getCause().getCause() instanceof org.hibernate.exception.SQLGrammarException
                && e.getCause().getCause().getCause() instanceof com.microsoft.sqlserver.jdbc.SQLServerException
                && ((java.sql.SQLException)e.getCause().getCause().getCause()).getErrorCode() == 1105) {
            gsh.setErrorMessage(DATABASE_FULL);
            return  new UploadFileResult(gsh, null);
        }
        errorMsgHandle("Fail while uploading file: '" + fileName + "'\n in path: '" + file.toString()  + "'", e);
        return new UploadFileResult(gsh, null);
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})    
    public Integer attachFileContent(Multipart bodyParts, Set<Long> attachmentFileIds) {
        if (attachmentFileIds == null || attachmentFileIds.isEmpty()) {
            return 0;
        }
        final Map<String, Object> params = new HashMap<>();
        final List<FileRef> results = HQL_findByQuery(""
            + " SELECT c"
            + " FROM " + FileRef.class.getCanonicalName() + " c"
            + " WHERE " +  BindUtil.bindFilterList("c.id", attachmentFileIds, true, params),
            params
        );
        try {
            final FileManager fileManager = new FileManager();
            results.stream().forEach((file) -> {
                try {
                    MimeBodyPart temp = new MimeBodyPart();
                    final ByteArrayDataSource byteArrayDataSource = new ByteArrayDataSource(
                            fileManager.getFileContent(file, true, null),
                            file.getContentType()
                    );
                    DataHandler dh = new DataHandler(byteArrayDataSource);
                    temp.setDataHandler(dh);
                    temp.setFileName((String) file.getDescription());
                    bodyParts.addBodyPart(temp);
                } catch (MessagingException ex) {
                    if (KnownExceptions.hasClientAborted(ex)) {
                        throw new ClientAbortException(ex);
                    }
                    getLogger().error("Failure while attaching files to mail '{}', files: '{}'", new Object[]{bodyParts, attachmentFileIds, ex});
                }
            });
            return 1;
        } catch (final HibernateException e) {
            if (KnownExceptions.hasClientAborted(e)) {
                throw new ClientAbortException(e);
            }
            getLogger().error("Failure while attaching files to mail '{}', files: '{}'", new Object[]{bodyParts, attachmentFileIds, e});
        }
        return 0;
    }
   
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public boolean isImage(Long fileId) {
        final Integer imageId = HQL_findSimpleInteger(""
            + " SELECT c.id "
            + " FROM " + Files.class.getCanonicalName() + " c "
            + " WHERE"
                + " c.contentType like '%image%'"
                + " AND c.id = :id",
                "id", 
                fileId
        );
        return imageId > 0;
        }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getFileContentSha512(final Long fileId) {
        return HQL_findSimpleString(""
                + " SELECT contentSha512 "
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", fileId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getFileContentSha512ByCode(final String fileCode) {
        return HQL_findSimpleString(""
                + " SELECT contentSha512 "
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.code = :code", "code", fileCode);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getFilePdfSha512(final Long fileId) {
        return HQL_findSimpleString(""
                + " SELECT pdfSha512 "
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", fileId);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getPdfPageSha512(final Long pdfPageId) {
        return HQL_findSimpleString(""
                + " SELECT imageSha512 "
                + " FROM " + PdfPage.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", pdfPageId);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getPdfPageThumbnailSha512(final Long pdfPageId) {
        return HQL_findSimpleString(""
                + " SELECT thumbnailSha512 "
                + " FROM " + PdfPage.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", pdfPageId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getFileThumbnailSha512(final Long fileId) {
        return HQL_findSimpleString(""
                + " SELECT thumbnailSha512 "
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", fileId);
        }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean existsThumbnail(final Long fileId) {
        return HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id"
                + " AND c.thumbnailSize IS NOT NULL", "id", fileId) > 0;
    }
        
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean existsPdfPageThumbnail(final Long pdfPageId) {
        return HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + PdfPage.class.getCanonicalName() + " c"
                + " WHERE c.id = :id" 
                + " AND c.thumbnailSize IS NOT NULL", "id", pdfPageId) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean existsPdfPage(final Long pdfPageId) {
        return HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + PdfPage.class.getCanonicalName() + " c"
                + " WHERE c.id = :id" 
                + " AND c.imageSize IS NOT NULL", "id", pdfPageId) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getFirstPdfPage(final Long fileId) {
        return HQL_findSimpleLong(""
                    + " SELECT c.id"
                    + " FROM " + PdfPage.class.getCanonicalName() + " c"
                    + " WHERE c.fileId = :fileId"
                    + " AND c.page = 0", "fileId", fileId);
}

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getPdfPageSize(final Long pdfPageId) {
        return HQL_findSimpleLong(""
                + " SELECT imageSize"
                + " FROM " + PdfPage.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", pdfPageId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getPdfPageThumbnailSize(final Long pdfPageId) {
        return HQL_findSimpleLong(""
                + " SELECT thumbnailSize"
                + " FROM " + PdfPage.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", pdfPageId);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String getFileCode(final Long fileId) {
        return HQL_findSimpleString(""
                + " SELECT code"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", fileId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getFileContentSize(final Long fileId) {
        return HQL_findSimpleLong(""
                + " SELECT contentSize"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", fileId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getFilePdfSize(final Long fileId) {
        return HQL_findSimpleLong(""
                + " SELECT pdfSize"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", fileId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getFileThumbnailSize(final Long fileId) {
        return HQL_findSimpleLong(""
                + " SELECT thumbnailSize"
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.id = :id", "id", fileId);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateFilePdfSha512(final Long fileId, final String sha512) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("sha512", sha512);
        params.put("id", fileId);
        return HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName()
                + " SET pdfSha512 = :sha512"
                + " WHERE id = :id", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateFileContentSha512(final Long fileId, final String sha512) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("sha512", sha512);
        params.put("id", fileId);
        return HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName()
                + " SET contentSha512 = :sha512"
                + " WHERE id = :id", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updatePdgPageSha512(final Long pdfPageId, final String sha512) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("sha512", sha512);
        params.put("id", pdfPageId);
        return HQL_updateByQuery(""
                + " UPDATE " + PdfPage.class.getCanonicalName()
                + " SET imageSha512 = :sha512"
                + " WHERE id = :id", params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateThumbnailSha512(final Long fileId, final String sha512) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("sha512", sha512);
        params.put("id", fileId);
        return HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName()
                + " SET thumbnailSha512 = :sha512"
                + " WHERE id = :id", params);
    }
        
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updatePdfPageThumbnailSha512(final Long pdfPageId, final String sha512) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("sha512", sha512);
        params.put("id", pdfPageId);
        return HQL_updateByQuery(""
                + " UPDATE " + PdfPage.class.getCanonicalName()
                + " SET thumbnailSha512 = :sha512"
                + " WHERE id = :id", params);
}

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateHasPdfPages(final Long fileId, final Integer hasPdfPages) {
        if (hasPdfPages != null && hasPdfPages.equals(1)) {
            return HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName() + " c "
                + " SET"
                + " c.hasPdfPages = 1"
                + " WHERE c.id = :id", "id", fileId);
        } else {
            return HQL_updateByQuery(""
                + " UPDATE " + Files.class.getCanonicalName() + " c "
                + " SET"
                + " c.hasPdfPages = 0"
                + " WHERE c.id = :id", "id", fileId);
        }
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateHasPdf(final Long fileId) {
        return HQL_updateByQuery(""
            + " UPDATE " + Files.class.getCanonicalName() + " c "
            + " SET"
            + " c.hasPdf = 1"
            + " WHERE c.id = :id", "id", fileId);
    }
    
    @Override 
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer updateFileThumbnailFromPdfPages(final Long fileId) {
        return HQL_updateByQuery(""
            + " UPDATE " + Files.class.getCanonicalName() + " c "
            + " SET"
            + " c.thumbnail = ("
                    + " SELECT p.thumbnail"
                    + " FROM  " + PdfPage.class.getCanonicalName() + " p"
                    + " WHERE p.fileId = c.id"
                    + " AND p.page = 0"
             + "),"
            + " c.thumbnailSize = ("
                    + " SELECT p.thumbnailSize"
                    + " FROM  " + PdfPage.class.getCanonicalName() + " p"
                    + " WHERE p.fileId = c.id"
                    + " AND p.page = 0"
             + "),"
            + " c.thumbnailSha512 = ("
                    + " SELECT p.thumbnailSha512"
                    + " FROM  " + PdfPage.class.getCanonicalName() + " p"
                    + " WHERE p.fileId = c.id"
                    + " AND p.page = 0"
             + ")"
            + " WHERE c.id = :id", "id", fileId);
}

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Long getFileIdByCode(final String fileCode) {
        return HQL_findSimpleLong(""
                + " SELECT c.id "
                + " FROM " + Files.class.getCanonicalName() + " c "
                + " WHERE c.code = :code", "code", fileCode);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IFileData findOneByCode(final String fileCode) {
        return (IFileData) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + FileRef.class.getCanonicalName() + " c "
                + " WHERE c.code = :code", "code", fileCode);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public FileLogging insertFileLogging(
            final Long fileId, 
            final FileLoggingType type,
            final Boolean skipLog,
            final ISecurityUser user
    ) {
        if (skipLog) {
            return null;
        }
        if (user == null || user.getId() == null || user.getId() <= 0 ) {
            getLogger().warn("Missing userId for file logging for fileId {}.", fileId);
            return null;
        }
        if (fileId == null || fileId <= 0) {
            getLogger().warn("Missing userId for file logging for userId {}.", user.getId());
            return null;
        }
        final FileLogging logging = new FileLogging();
        final Date now = new Date();
        logging.setId(-1l);
        logging.setFileId(fileId);
        logging.setCreatedBy(user.getId());
        logging.setCreatedDate(now);
        logging.setLastModifiedBy(user.getId());
        logging.setLastModifiedDate(now);
        logging.setUserId(user.getId());
        logging.setType(type.getType());
        return makePersistent(logging, user.getId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public IPlainFile loadPlainFile(Long fileId) {
        if (fileId == null || fileId <= 0) {
            return null;
        }
        return (FilePlainDTO) HQL_findSimpleObject(" "
                + " SELECT new " + FilePlainDTO.class.getCanonicalName() + "("
                    + " c.id"
                    + ", c.description"
                    + ", c.extension"
                    + ", c.contentType"
                    + ", c.busy"
                    + ", c.numberPages"
                    + ", c.contentSize"
                    + ", c.docGuid"
                    + ", c.code"
                    + ", c.thumbnailSize"
                    + ", c.pdfSize"
                    + ", c.contentSha512"
                    + ", c.thumbnailSha512"
                    + ", c.pdfSha512"
                    + ", c.hasPdf"
                    + ", c.hasPdfPages"
                    + ", c.creationType"
                    + ", c.numberSavedPages"
                    + ", c.hasPassword"
                    + ", c.createdDate"
                    + ", c.lastModifiedDate"
                    + ", c.createdBy"
                    + ", c.lastModifiedBy"
                + ")"
                + " FROM " + FileRef.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", fileId);
    }
}
