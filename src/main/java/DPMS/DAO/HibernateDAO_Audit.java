/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package DPMS.DAO;

import DPMS.DAOInterface.IAuditDAO;
import DPMS.DAOInterface.IAuditIndividualDAO;
import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.Mapping.Audit;
import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditSave;
import DPMS.Mapping.AuditsComment;
import DPMS.Mapping.AuditsDocument;
import DPMS.Mapping.AuditsFiles;
import DPMS.Mapping.CodeSequence;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import isoblock.surveys.dao.hibernate.Survey;
import isoblock.surveys.dao.hibernate.SurveySimple;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.document.entity.DocumentLite;
import qms.form.dao.IFormCaptureDAO;
import qms.form.util.SurveyUtilCache;
import qms.util.EntityCommon;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Lazy
@Repository(value = "HibernateDAO_Audit")
@Scope(value = "singleton")
public class HibernateDAO_Audit extends GenericDAOImpl<Audit, Long> implements IAuditDAO {

    /**
     * Implementacion del metodo para guardar el registro padre de la auditorias
     * (Plan de auditorias).
     *
     * @param audit plan de auditoria a guardar/actualizar
     * @param audits Auditorías individuales a crear
     * @param loggedUser usuario autor del plan de auditorias
     * @return GenericSaveHandle con los resultados de la operacion
     * <AUTHOR> Cavazos Galindo
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle save(AuditSave audit, Set<AuditIndividual> audits, LoggedUser loggedUser)throws QMSException {
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.DAO.HibernateDAO_Audit @ save: {}", Utilities.getSerializedObj(audit));
        }
        if (audit.getProcess().getId() == null || audit.getProcess().getId().equals(0L)) {
            audit.setProcess(null);
        }
        if (audit.getArea().getId() == null || audit.getArea().getId().equals(0L)) {
            audit.setArea(null);
            audit.setBuildingId(null);
            audit.setBuilding(null);
        }
        boolean nuevo = (audit.getId() == -1);
        if (nuevo) {
            audit.setAuthor(loggedUser.getUser());
            audit.setCreatedDate(new Date());
            String code = audit.getCode();
            if (code != null) {
                code = code.trim();
            } else {
                code =  "";
            }
            if (code.isEmpty()) {
                String prefix = "PLAN-AUD-" + Utilities.todayDateBy("yy");
                String tmp;
                boolean invalid = true;
                do {
                    tmp = EntityCommon.getNextCode(audit, prefix);
                    invalid = HQL_findSimpleInteger(""
                            + " SELECT count(*)"
                            + " FROM " + Audit.class.getCanonicalName() + " c "
                            + " WHERE c.code = :code"
                            + "", "code", tmp) > 0;
                } while (invalid);
                audit.setCode(tmp);
            }
            audit.setStatus(Audit.STATUS_PLANNED);
        } else {
            Audit auditBk = this.HQLT_findById(audit.getId());
            audit.setAuthor(auditBk.getAuthor());
        }

        if (audit.getDteAnticipation().before(Utilities.sumarFechasDias(new Date(), 1))) {
            audit.setStatus(Audit.STATUS_IN_PROCESS);
            audit.getSurvey().update("estatus", Survey.ESTATUS_BLOQUEADO, this);

            // Cuando se hace una auditoría de área no se incluye el set de audits, ya que dicho set se llena con los datos del departamento (auditoría de proceso - tabla agregar departamento)
            // Por lo que se agregan las siguientes líneas para solucionar el guardado de avance para auditorías de área
            final Set<Long> surveyIds;
            if (audits.isEmpty()) {
                surveyIds = Stream.of(audit.getSurvey().getId())
                        .collect(Collectors.toCollection(LinkedHashSet::new));
            } else {
                surveyIds = audits.stream()
                        .map(auditIndividual -> auditIndividual.getSurvey().getId())
                        .collect(Collectors.toCollection(LinkedHashSet::new));
            }
            final IFormCaptureDAO captureDao = Utilities.getBean(IFormCaptureDAO.class);
            final SurveyUtilCache cache = new SurveyUtilCache();
            surveyIds.stream().forEach((surveyId) -> {
                captureDao.getFactoryBuildedTable(surveyId, cache, loggedUser);
            });
        }
        getLogger().info("Son {} departamentos.", audit.getDepartments().size());
        Set<DocumentLite> documents = audit.getDocuments();
        String[] filesIds = audit.getFilesIds().length() > 0 ? audit.getFilesIds().split(",") : null;
        List<AuditsComment> comments = audit.getComments();
        //Guardando registro padre
        audit = makePersistent(audit);
        //Se guardan los documentos
        if (documents != null && documents.size() > 0) {
            for (DocumentLite document : documents) {
                makePersistent(new AuditsDocument(audit.getId(), document.getId()), loggedUser.getId());
            }
        }
        if (filesIds != null && filesIds.length > 0) {
            for (String filesId : filesIds) {
                makePersistent(new AuditsFiles( Long.parseLong(filesId), audit.getId()), loggedUser.getId());
            }
        }
        if (comments != null && comments.size() > 0) {
            for (AuditsComment comment : comments) {
                comment.setCode(((ICodeSequenceDAO) Utilities.getBean("CodeSequence")).next(CodeSequence.type.CODE_AUDIT_COMMENT));
                comment.setCreationTime(new Date());
                comment.setAuthorId(loggedUser.getId());
                comment.setAuthorName(loggedUser.getDescription());
                comment.setDeleted(0);
                comment.setStatus(1);
                comment.setAuditId(audit.getId());
                comment.setCommentId(-1L);
                makePersistent(comment, loggedUser.getId());
            }
        }
        if (audit != null) {
            IAuditIndividualDAO dao = getBean(IAuditIndividualDAO.class);
            dao.save(loggedUser, audit, audits);
            gsh.setOperationEstatus(1);
            Map<String, Object> m = new HashMap<>();
            gsh.setJsonEntityData(m);
            if (nuevo) {
                //gsh.getJsonData().put("tipo", "add_success");
                gsh.setSuccessMessage("{\"tipo\":\"add_success\"}");
            } else {
                //gsh.getJsonData().put("tipo", "edit_success");
                gsh.setSuccessMessage("{\"tipo\":\"edit_success\"}");
            }
            gsh.getJsonEntityData().put("code", audit.getCode());
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    /**
     * Implementacion del metodo para cancelar plan
     *
     * @param auditId Id del plan de auditorias que será cancelado.
     * @param loggedUser
     * @return Integer 0 ó 1 para saber si la cancelación se realizó
     * correctamente.
     * <AUTHOR> Cavazos Galindo
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer cancel(Long auditId, LoggedUser loggedUser) {
        getLogger().trace("DPMS.DAO.HibernateDAO_Audit @ cancel: [auditId={}", auditId);
        Audit a = this.HQLT_findById(auditId);
        if (a == null) {
            return 0;
        }
        a.setStatus(Audit.STATUS_CANCELED);
        this.makePersistent(a);
        SurveySimple survey = a.getSurvey();
        Long surveyId = survey.getId();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("sId", surveyId);
        params.put("aId", auditId);
        params.put("status", Audit.ACTIVE_STATUS);
        Long surveyHasAuditsNum
                = this.HQL_findSimpleLong("SELECT COUNT(c.id) FROM DPMS.Mapping.Audit c "
                        + "WHERE c.survey.id = :sId "
                        + "AND c.id != :aId "
                        + "AND c.status = :status", params);
        if (surveyHasAuditsNum == 0L) {
            survey.setEstatus(Survey.ESTATUS_ACTIVO);
            this.makePersistent(survey);
        }
        IAuditIndividualDAO dao = getBean(IAuditIndividualDAO.class);
        dao.cancelAudit(auditId, "", loggedUser);
        return 1;
    }

    /**
     * Implementacion del metodo para cancelar plan
     *
     * @param auditId Id del plan de auditorias que será cancelado.
     * @param loggedUser
     * @return Integer 0 ó 1 para saber si la cancelación se realizó
     * correctamente.
     * <AUTHOR> Cavazos Galindo
     * @since *********
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer delete(Long auditId, LoggedUser loggedUser) {
        getLogger().trace("DPMS.DAO.HibernateDAO_Audit @ delete: [auditId={}]", auditId);
        Integer update = this.HQL_updateByQuery(""
                + "UPDATE DPMS.Mapping.Audit au "
                + "SET au.deleted = 1 "
                + "WHERE au.id = " + auditId);
        if (update != 0) {
            IAuditIndividualDAO dao = getBean(IAuditIndividualDAO.class);
            return dao.deleteAudit(auditId, "", loggedUser).size();
        }
        return 0;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo getRowsTrash(SortedPagedFilter filter) {
        filter.getCriteria().put("deleted", Audit.IS_DELETED.toString());
        return this.getRows(filter);
    }
}
