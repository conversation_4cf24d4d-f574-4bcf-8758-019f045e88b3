package DPMS.DAO;

import DPMS.Catalog.CRUD_BusinessUnit;
import DPMS.Catalog.CRUD_Department;
import DPMS.Catalog.CRUD_DepartmentProcess;
import DPMS.Catalog.CRUD_OrganizationalUnit;
import DPMS.Catalog.CRUD_Process;
import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IDocumentReaderDAO;
import DPMS.DAOInterface.IFilesDAO;
import DPMS.DAOInterface.IFlowDAO;
import DPMS.DAOInterface.IInitialLoadDAO;
import DPMS.DAOInterface.IPeriodicityDAO;
import DPMS.DAOInterface.IPositionLoadDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Document.CRUD_Request;
import DPMS.Mapping.Area;
import DPMS.Mapping.AutorizationPool;
import DPMS.Mapping.AutorizationPoolDetails;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.BusinessUnitLite;
import DPMS.Mapping.Catalog;
import DPMS.Mapping.CatalogPK;
import DPMS.Mapping.Department;
import DPMS.Mapping.DepartmentProcess;
import DPMS.Mapping.Device;
import DPMS.Mapping.DeviceClassification;
import DPMS.Mapping.DeviceGroup;
import DPMS.Mapping.DeviceStatus;
import DPMS.Mapping.DeviceType;
import DPMS.Mapping.Document;
import DPMS.Mapping.DocumentType;
import DPMS.Mapping.FilesLite;
import DPMS.Mapping.Node;
import DPMS.Mapping.NodeAccess;
import DPMS.Mapping.NodeSimple;
import DPMS.Mapping.NodeUser;
import DPMS.Mapping.OrganizationalUnit;
import DPMS.Mapping.OrganizationalUnitSimple;
import DPMS.Mapping.Periodicity;
import DPMS.Mapping.Position;
import DPMS.Mapping.PositionDepartmentProcess;
import DPMS.Mapping.PositionSave;
import DPMS.Mapping.Priority;
import DPMS.Mapping.Process;
import DPMS.Mapping.Profile;
import DPMS.Mapping.RelatedDocument;
import DPMS.Mapping.Request;
import DPMS.Mapping.Service;
import DPMS.Mapping.ServiceResult;
import DPMS.Mapping.ServiceSchedule;
import DPMS.Mapping.ServiceType;
import DPMS.Mapping.User;
import DPMS.Mapping.Verification;
import DPMS.Mapping.Workflow;
import Framework.Config.Language;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import ape.pending.entities.PendingRecord;
import bnext.login.logic.RenewDataStatus;
import bnext.reference.BusinessUnitDepartmentRef;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import com.google.common.collect.ImmutableMap;
import isoblock.common.Properties;
import isoblock.surveys.dao.hibernate.DepartmentProcessView;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.file.InvalidPathException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import mx.bnext.access.Module;
import mx.bnext.core.file.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.access.util.ProfileServicesUtil;
import qms.configuration.dto.BusinessUnitDepartmentDTO;
import qms.configuration.entity.Region;
import qms.configuration.entity.Zone;
import qms.configuration.util.IBulkUser;
import qms.custom.DAOInterface.IDynamicFieldDAO;
import qms.custom.core.DynamicTableHelper;
import qms.custom.dto.DynamicFieldInsertDTO;
import qms.custom.entity.DynamicField;
import qms.custom.entity.DynamicFieldValue;
import qms.document.entity.DocumentReference;
import qms.document.entity.DocumentTraceability;
import qms.document.entity.DocumentTypeDynamicField;
import qms.framework.dao.ILicenseUserDAO;
import qms.framework.dto.BackendUploadResult;
import qms.framework.dto.BulkLoadDTO;
import qms.framework.dto.LoadCacheDTO;
import qms.framework.dto.RowDTO;
import qms.framework.entity.BulkLoadRow;
import qms.framework.entity.UserPositionSave;
import qms.framework.enums.BulkLoadError;
import qms.framework.enums.BulkLoadRowType;
import qms.framework.enums.UpdateBossStatus;
import qms.framework.file.FileManager;
import qms.framework.initialload.BackendUploaderHelper;
import qms.framework.initialload.CSVOrganizationColumns;
import qms.framework.initialload.CSVReference;
import qms.framework.initialload.CSVUserColumns;
import qms.framework.initialload.CsvArea;
import qms.framework.initialload.CsvBusinessUnit;
import qms.framework.initialload.CsvBusinessUnitDepartment;
import qms.framework.initialload.CsvDepartmentProcess;
import qms.framework.initialload.CsvDocumentColumns;
import qms.framework.initialload.CsvException;
import qms.framework.initialload.CsvFileColumns;
import qms.framework.initialload.CsvFoldersPermissions;
import qms.framework.initialload.CsvOrganizationalUnit;
import qms.framework.initialload.CsvRelatedDocument;
import qms.framework.initialload.CsvUpdateBossColumns;
import qms.framework.initialload.ICsvIndex;
import qms.framework.initialload.ProfileColumns;
import qms.framework.listeners.OnSuccessProcessBulkUserFile;
import qms.framework.rest.UserUtils;
import qms.framework.util.BusinessUnitUtil;
import qms.framework.util.CacheRegion;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.UserCreationSource;
import qms.util.EncodeUtil;
import qms.util.QMSException;
import qms.util.UserUtil;
import qms.workflow.util.IWorkflowDAO;
import qms.workflow.util.WorkflowAuthRole;

@Lazy
@Repository(value = "HibernateDAO_InitialLoad")
@Scope(value = "singleton")
@Language(module = "DPMS.DAO.HibernateDAO_InitialLoad")
public class HibernateDAO_InitialLoad extends GenericDAOImpl implements IInitialLoadDAO {

    private final BackendUploaderHelper helper = new BackendUploaderHelper();

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertFile(RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, LoggedUser loggedUser) throws IOException {
        final String path = helper.cellValue(rowData, CsvFileColumns.FULL_FOLDER_PATH);
        final String filename = helper.cellValue(rowData, CsvFileColumns.FILE_NAME);
        final String fullPath = path + "\\" + filename;
        final Path f = Paths.get(path, filename);
        if (!FileUtils.exists(f)) {
            result.getSb().append("<p class='error file'>Error: File '").append(fullPath).append("' not found!</p>");
            return;
        }
        try {
            final String contentType = FileUtils.validContentType(f, null);
            final IFilesDAO dao = getBean(IFilesDAO.class);
            final FileManager fileManager = new FileManager();
            final GenericSaveHandle gsh = fileManager
                    .persistFileContent(-1L, f, filename, contentType, false, loggedUser.getUser())
                    .getGsh();
            final Long fileId = gsh.getPersistentId();
            final String documentCode = helper.cellValue(rowData, CsvFileColumns.DOCUMENT_CODE);
            final Integer reqs;
            final Integer docs;
            if (gsh.getOperationEstatus().equals(1)) {
                if (documentCode.isEmpty()) {
                    result.getSb().append("<p class='added file'>Archivo '").append(fullPath).append(" agregado correctamente. </p>");
                    return;
                }
                //ToDo, Considerar revisiones (Ej. Versiones 1 y 2 son documentos diferentes)
                reqs = dao.HQL_updateByQuery(""
                        + " UPDATE " + Request.class.getCanonicalName()
                        + " SET fileId = :fileId"
                        + " WHERE document_code = :documentCode",
                        ImmutableMap.of(
                                "documentCode", documentCode,
                                "fileId", fileId
                        )
                );
                docs = dao.HQL_updateByQuery(""
                        + " UPDATE " + Document.class.getCanonicalName()
                        + " SET fileId = :fileId"
                        + " WHERE vch_clave = :documentCode",
                        ImmutableMap.of(
                                "documentCode", documentCode,
                                "fileId", fileId
                        )
                );
                if ((docs + reqs) == 2) {
                    result.getSb().append("<p class='added file'>Archivo '").append(fullPath).append(" agregado correctamente. </p>");
                    return;
                } else if ((docs + reqs) > 2) {
                    result.getSb().append("<p class='added file'>Archivo '").append(fullPath).append("' agregado correctamente. </p>")
                            .append("* Revisar: Hay ").append(docs).append(" documentos y ")
                            .append(reqs).append("  solicitudes relacionadas.");
                    return;
                }
            } else {
                reqs = 0;
                docs = 0;
            }
            result.getSb().append("<p class='error file'>Error al insertar el Archivo '").append(fullPath).append("' </p>")
                    .append(", upload: '").append(gsh.getErrorMessage()).append("'")
                    .append(", doc: '").append(documentCode).append("'")
                    .append(", file: '").append(fileId).append("'")
                    .append(", reqs: '").append(reqs).append("'")
                    .append(", docs: '").append(docs).append("'");
        } catch (IOException e) {
            getLogger().error("IOException: ", e);
            result.getSb().append("<p class='error file'>Error al insertar el Archivo '").append(fullPath).append("' </p>");
        } catch (InvalidPathException e) {
            getLogger().error("InvalidPathException: ", e);
            result.getSb().append("<p class='error file'>Error al insertar el Archivo '").append(fullPath).append("' </p>");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateProfile(
            final RowDTO rowData,
            final LoadCacheDTO cache,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            final String profileCode = helper.profileIdCSV_Profile(rowData);
            final Long profileId = cache.getProfileCache().get(profileCode);
            Profile prf;
            Boolean add = true;
            if (profileId == null || Objects.equals(profileId, 0)) {//No existe, hay que agregarlo
                prf = ProfileServicesUtil.getInstance();
                prf.setId(-1L);
            } else {
                prf = (Profile) HQLT_findById(Profile.class, profileId);
                add = false;
            }
            prf.setCode(profileCode);
            prf.setDeleted(Profile.IS_NOT_DELETED);
            prf.setDescription(profileCode.replace("_", " "));
            switch (helper.parseProfilePerm(rowData, ProfileColumns.ADMINISTRATION.getIndex())) {
                case 0:
                    prf.setIntBAdmonSistema(0);
                    prf.setBulkAccessCreator(0);
                    prf.setIntBAdmonAccesos(0);
                    prf.setCatalogsMeetings(0);
                    prf.setCatalogsDocuments(0);
                    prf.setCatalogsFormularies(0);
                    prf.setCatalogsAudits(0);
                    prf.setCatalogsActions(0);
                    prf.setCatalogsSurveys(0);
                    prf.setCatalogsComplaints(0);
                    prf.setCatalogsMeters(0);
                    prf.setCatalogsProjects(0);
                    prf.setCatalogsDevices(0);
                    prf.setCatalogsFiveS(0);
                    prf.setCatalogsGenerals(0);
                    prf.setIntBReporteConfiguracion(0);
                    prf.setIntBReporteGerencial(0);
                    break;
                case 1:
                    prf.setIntBAdmonSistema(0);
                    prf.setBulkAccessCreator(0);
                    prf.setIntBAdmonAccesos(0);
                    prf.setCatalogsMeetings(0);
                    prf.setCatalogsDocuments(0);
                    prf.setCatalogsFormularies(0);
                    prf.setCatalogsAudits(0);
                    prf.setCatalogsActions(0);
                    prf.setCatalogsSurveys(0);
                    prf.setCatalogsComplaints(0);
                    prf.setCatalogsMeters(0);
                    prf.setCatalogsProjects(0);
                    prf.setCatalogsDevices(0);
                    prf.setCatalogsFiveS(0);
                    prf.setCatalogsGenerals(0);
                    prf.setIntBReporteConfiguracion(0);
                    prf.setIntBReporteGerencial(0);
                    break;
                case 2:
                    prf.setIntBAdmonSistema(0);
                    prf.setBulkAccessCreator(0);
                    prf.setIntBAdmonAccesos(1);
                    prf.setCatalogsMeetings(0);
                    prf.setCatalogsDocuments(0);
                    prf.setCatalogsFormularies(0);
                    prf.setCatalogsAudits(0);
                    prf.setCatalogsActions(0);
                    prf.setCatalogsSurveys(0);
                    prf.setCatalogsComplaints(0);
                    prf.setCatalogsMeters(0);
                    prf.setCatalogsProjects(0);
                    prf.setCatalogsDevices(0);
                    prf.setCatalogsFiveS(0);
                    prf.setCatalogsGenerals(0);
                    prf.setIntBReporteConfiguracion(1);
                    prf.setIntBReporteGerencial(1);
                    break;
                case 3:
                    prf.setIntBAdmonSistema(1);
                    prf.setBulkAccessCreator(1);
                    prf.setIntBAdmonAccesos(1);
                    prf.setCatalogsMeetings(0);
                    prf.setCatalogsDocuments(0);
                    prf.setCatalogsFormularies(0);
                    prf.setCatalogsAudits(0);
                    prf.setCatalogsActions(0);
                    prf.setCatalogsSurveys(0);
                    prf.setCatalogsComplaints(0);
                    prf.setCatalogsMeters(0);
                    prf.setCatalogsProjects(0);
                    prf.setCatalogsDevices(0);
                    prf.setCatalogsFiveS(0);
                    prf.setCatalogsGenerals(0);
                    prf.setIntBReporteConfiguracion(1);
                    prf.setIntBReporteGerencial(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.DOCUMENTS.getIndex())) {
                case 0:
                    prf.setIntBLectorDocumento(0);
                    prf.setIntBEditorDocumento(0);
                    prf.setIntBEncargadoDocumento(0);
                    prf.setIntBReporteDocumento(0);
                    prf.setShareDocument(0);
                    prf.setIntBPrintUncontrolledCopies(0);
                    break;
                case 1:
                    prf.setIntBLectorDocumento(1);
                    prf.setIntBEditorDocumento(0);
                    prf.setIntBEncargadoDocumento(0);
                    prf.setIntBReporteDocumento(0);
                    prf.setShareDocument(0);
                    prf.setIntBPrintUncontrolledCopies(1);
                    break;
                case 2:
                    prf.setIntBLectorDocumento(0);
                    prf.setIntBEditorDocumento(1);
                    prf.setIntBEncargadoDocumento(0);
                    prf.setIntBReporteDocumento(1);
                    prf.setShareDocument(0);
                    prf.setIntBPrintUncontrolledCopies(1);
                    break;
                case 3:
                    prf.setIntBLectorDocumento(0);
                    prf.setIntBEditorDocumento(0);
                    prf.setIntBEncargadoDocumento(1);
                    prf.setIntBReporteDocumento(1);
                    prf.setShareDocument(1);
                    prf.setIntBPrintUncontrolledCopies(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.FORMS.getIndex())) {
                case 0:
                    prf.setIntBCrearFormulario(0);
                    prf.setIntBControlFormulario(0);
                    prf.setIntBFillOutHistory(0);
                    break;
                case 1:
                    prf.setIntBCrearFormulario(1);
                    prf.setIntBControlFormulario(0);
                    prf.setIntBFillOutHistory(0);
                    break;
                case 2:
                    prf.setIntBCrearFormulario(0);
                    prf.setIntBControlFormulario(1);
                    prf.setIntBFillOutHistory(1);
                    break;
                case 3:
                    prf.setIntBCrearFormulario(1);
                    prf.setIntBControlFormulario(1);
                    prf.setIntBFillOutHistory(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.AUDITS.getIndex())) {
                case 0:
                    prf.setIntBReporteAuditoria(0);
                    prf.setIntBAuditAuditorHelper(0);
                    prf.setIntBAuditAuditorLider(0);
                    prf.setIntBAuditSurvey(0);
                    prf.setIntBAuditQuality(0);
                    prf.setIntBReporteAuditoria(0);
                    prf.setIntBReporteCuestionario(0);
                    break;
                case 1:
                    prf.setIntBReporteAuditoria(0);
                    prf.setIntBAuditAuditorHelper(1);
                    prf.setIntBAuditAuditorLider(0);
                    prf.setIntBAuditSurvey(0);
                    prf.setIntBAuditQuality(0);
                    prf.setIntBReporteAuditoria(0);
                    prf.setIntBReporteCuestionario(0);
                    break;
                case 2:
                    prf.setIntBReporteAuditoria(1);
                    prf.setIntBAuditAuditorHelper(1);
                    prf.setIntBAuditAuditorLider(1);
                    prf.setIntBAuditSurvey(1);
                    prf.setIntBAuditQuality(0);
                    prf.setIntBReporteAuditoria(1);
                    prf.setIntBReporteCuestionario(1);
                    break;
                case 3:
                    prf.setIntBReporteAuditoria(1);
                    prf.setIntBAuditAuditorHelper(1);
                    prf.setIntBAuditAuditorLider(1);
                    prf.setIntBAuditSurvey(1);
                    prf.setIntBAuditQuality(1);
                    prf.setIntBReporteAuditoria(1);
                    prf.setIntBReporteCuestionario(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.FINDINGS.getIndex())) {
                case 0:
                    prf.setIntBLectorAccion(0);
                    prf.setIntBEditorAccion(0);
                    prf.setIntBEncargadoAccion(0);
                    prf.setIntBReporteAccion(0);
                    break;
                case 1:
                    prf.setIntBLectorAccion(1);
                    prf.setIntBEditorAccion(0);
                    prf.setIntBEncargadoAccion(0);
                    prf.setIntBReporteAccion(0);
                    break;
                case 2:
                    prf.setIntBLectorAccion(0);
                    prf.setIntBEditorAccion(1);
                    prf.setIntBEncargadoAccion(0);
                    prf.setIntBReporteAccion(1);
                    break;
                case 3:
                    prf.setIntBLectorAccion(0);
                    prf.setIntBEditorAccion(0);
                    prf.setIntBEncargadoAccion(1);
                    prf.setIntBReporteAccion(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.DEVICES.getIndex())) {
                case 0:
                    prf.setIntBDeviceCreator(0);
                    prf.setIntBDeviceDisposeViewer(0);
                    prf.setIntBDeviceManager(0);
                    prf.setIntBDeviceRealizeService(0);
                    prf.setIntBDeviceScheduledServicesViewer(0);
                    prf.setIntBDeviceServiceHistoryViewer(0);
                    prf.setIntBDeviceServiceMetricViewer(0);
                    prf.setIntBDeviceServiceScheduler(0);
                    prf.setIntBDeviceViewer(0);
                    break;
                case 1:
                    prf.setIntBDeviceCreator(0);
                    prf.setIntBDeviceDisposeViewer(0);
                    prf.setIntBDeviceManager(0);
                    prf.setIntBDeviceRealizeService(1);
                    prf.setIntBDeviceScheduledServicesViewer(1);
                    prf.setIntBDeviceServiceHistoryViewer(1);
                    prf.setIntBDeviceServiceMetricViewer(0);
                    prf.setIntBDeviceServiceScheduler(0);
                    prf.setIntBDeviceViewer(1);
                    break;
                case 2:
                    prf.setIntBDeviceCreator(1);
                    prf.setIntBDeviceDisposeViewer(1);
                    prf.setIntBDeviceManager(0);
                    prf.setIntBDeviceRealizeService(1);
                    prf.setIntBDeviceScheduledServicesViewer(1);
                    prf.setIntBDeviceServiceHistoryViewer(1);
                    prf.setIntBDeviceServiceMetricViewer(0);
                    prf.setIntBDeviceServiceScheduler(1);
                    prf.setIntBDeviceViewer(1);
                    break;
                case 3:
                    prf.setIntBDeviceCreator(1);
                    prf.setIntBDeviceDisposeViewer(1);
                    prf.setIntBDeviceManager(1);
                    prf.setIntBDeviceRealizeService(1);
                    prf.setIntBDeviceScheduledServicesViewer(1);
                    prf.setIntBDeviceServiceHistoryViewer(1);
                    prf.setIntBDeviceServiceMetricViewer(1);
                    prf.setIntBDeviceServiceScheduler(1);
                    prf.setIntBDeviceViewer(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.MEETINGS.getIndex())) {
                case 0:
                    prf.setIntBLectorReunion(0);
                    prf.setIntBEditorReunion(0);
                    prf.setIntBEncargadoReunion(0);
                    prf.setIntBReporteReunion(0);
                    break;
                case 1:
                    prf.setIntBLectorReunion(1);
                    prf.setIntBEditorReunion(0);
                    prf.setIntBEncargadoReunion(0);
                    prf.setIntBReporteReunion(0);
                    break;
                case 2:
                    prf.setIntBLectorReunion(0);
                    prf.setIntBEditorReunion(1);
                    prf.setIntBEncargadoReunion(0);
                    prf.setIntBReporteReunion(1);
                    break;
                case 3:
                    prf.setIntBLectorReunion(0);
                    prf.setIntBEditorReunion(0);
                    prf.setIntBEncargadoReunion(1);
                    prf.setIntBReporteReunion(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.POLLS.getIndex())) {
                case 0:
                    prf.setIntBSurveyManager(0);
                    prf.setIntBSurveyRespondent(0);
                    prf.setIntBSurveySupervisor(0);
                    prf.setIntBReporteEncuesta(0);
                    break;
                case 1:
                    prf.setIntBSurveyManager(0);
                    prf.setIntBSurveyRespondent(1);
                    prf.setIntBSurveySupervisor(1);
                    prf.setIntBReporteEncuesta(0);
                    break;
                case 2:
                    prf.setIntBSurveyManager(0);
                    prf.setIntBSurveyRespondent(1);
                    prf.setIntBSurveySupervisor(1);
                    prf.setIntBReporteEncuesta(1);
                    break;
                case 3:
                    prf.setIntBSurveyManager(1);
                    prf.setIntBSurveyRespondent(1);
                    prf.setIntBSurveySupervisor(1);
                    prf.setIntBReporteEncuesta(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.COMPLAINTS.getIndex())) {
                case 0:
                    prf.setIntBLectorQueja(0);
                    prf.setIntBEditorQueja(0);
                    prf.setIntBEncargadoQueja(0);
                    prf.setIntBReporteQueja(0);
                    break;
                case 1:
                    prf.setIntBLectorQueja(1);
                    prf.setIntBEditorQueja(0);
                    prf.setIntBEncargadoQueja(0);
                    prf.setIntBReporteQueja(0);
                    break;
                case 2:
                    prf.setIntBLectorQueja(0);
                    prf.setIntBEditorQueja(1);
                    prf.setIntBEncargadoQueja(0);
                    prf.setIntBReporteQueja(1);
                    break;
                case 3:
                    prf.setIntBLectorQueja(0);
                    prf.setIntBEditorQueja(0);
                    prf.setIntBEncargadoQueja(1);
                    prf.setIntBReporteQueja(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.DEVICES.getIndex())) {
                case 0:
                    prf.setIntBLectorIndicador(0);
                    prf.setIntBEditorIndicador(0);
                    prf.setIntBEncargadoIndicador(0);
                    prf.setIntBReporteIndicador(0);
                    break;
                case 1:
                    prf.setIntBLectorIndicador(1);
                    prf.setIntBEditorIndicador(0);
                    prf.setIntBEncargadoIndicador(0);
                    prf.setIntBReporteIndicador(0);
                    break;
                case 2:
                    prf.setIntBLectorIndicador(0);
                    prf.setIntBEditorIndicador(1);
                    prf.setIntBEncargadoIndicador(0);
                    prf.setIntBReporteIndicador(1);
                    break;
                case 3:
                    prf.setIntBLectorIndicador(0);
                    prf.setIntBEditorIndicador(0);
                    prf.setIntBEncargadoIndicador(1);
                    prf.setIntBReporteIndicador(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.PROJECTS.getIndex())) {
                case 0:
                    prf.setIntBEncargadoProyecto(0);
                    prf.setIntBEditorProyecto(0);
                    prf.setIntBLectorProyecto(0);
                    prf.setIntBContabilidadProyecto(0);
                    prf.setIntBReporteProyecto(0);
                    break;
                case 1:
                    prf.setIntBEncargadoProyecto(0);
                    prf.setIntBEditorProyecto(0);
                    prf.setIntBLectorProyecto(1);
                    prf.setIntBContabilidadProyecto(0);
                    prf.setIntBReporteProyecto(0);
                    break;
                case 2:
                    prf.setIntBEncargadoProyecto(0);
                    prf.setIntBEditorProyecto(1);
                    prf.setIntBLectorProyecto(0);
                    prf.setIntBContabilidadProyecto(1);
                    prf.setIntBReporteProyecto(1);
                    break;
                case 3:
                    prf.setIntBEncargadoProyecto(1);
                    prf.setIntBEditorProyecto(0);
                    prf.setIntBLectorProyecto(0);
                    prf.setIntBContabilidadProyecto(0);
                    prf.setIntBReporteProyecto(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.DEVICES.getIndex())) {
                case 0:
                    prf.setIntBCreateFiveS(0);
                    prf.setIntBLinkFiveS(0);
                    prf.setIntBPrintFiveS(0);
                    prf.setIntBFiveSManager(0);
                    prf.setIntBFiveSMap(0);
                    prf.setIntBFiveSSection(0);
                    prf.setIntBFiveSItem(0);
                    break;
                case 1:
                    prf.setIntBCreateFiveS(0);
                    prf.setIntBLinkFiveS(0);
                    prf.setIntBPrintFiveS(1);
                    prf.setIntBFiveSManager(0);
                    prf.setIntBFiveSMap(0);
                    prf.setIntBFiveSSection(0);
                    prf.setIntBFiveSItem(0);
                    break;
                case 2:
                    prf.setIntBCreateFiveS(0);
                    prf.setIntBLinkFiveS(1);
                    prf.setIntBPrintFiveS(1);
                    prf.setIntBFiveSManager(0);
                    prf.setIntBFiveSMap(0);
                    prf.setIntBFiveSSection(0);
                    prf.setIntBFiveSItem(0);
                    break;
                case 3:
                    prf.setIntBCreateFiveS(1);
                    prf.setIntBLinkFiveS(1);
                    prf.setIntBPrintFiveS(1);
                    prf.setIntBFiveSManager(1);
                    prf.setIntBFiveSMap(1);
                    prf.setIntBFiveSSection(1);
                    prf.setIntBFiveSItem(1);
                    break;
            }
            switch (helper.parseProfilePerm(rowData, ProfileColumns.ACTIVITIES.getIndex())) {
                case 0:
                    prf.setActivityViewer(0);
                    prf.setActivityCreator(0);
                    prf.setActivityManager(0);
                    break;
                case 1:
                    prf.setActivityViewer(1);
                    prf.setActivityCreator(0);
                    prf.setActivityManager(0);
                    break;
                case 2:
                    prf.setActivityViewer(1);
                    prf.setActivityCreator(1);
                    prf.setActivityManager(0);
                    break;
                case 3:
                    prf.setActivityViewer(1);
                    prf.setActivityCreator(1);
                    prf.setActivityManager(1);
                    break;
            }

            prf.setIntBUsuarioCorporativo(0);
            prf.setIntBUsuarioPlanta(1);

            prf.setStatus(Profile.STATUS.ACTIVE.getValue());
            prf = (Profile) makePersistent(prf, loggedUser.getId());
            if (prf == null) {
                result.getSb().append("<p class='error prf'>Error al modificar el Perfil '").append(profileCode).append("' </p>");
            } else {
                if (add) {
                    result.getSb().append("<p class='added prf'>Perfil ").append(profileCode
                    ).append(" agregado correctamente. </p>");
                } else {
                    result.getSb().append("<p class='added prf'>Perfil ").append(profileCode
                    ).append(" modificado correctamente. </p>");
                }
            }
        } catch (final Exception e) {
            result.getSb().append("<p class='error prf'>Error al modificar el Perfil '").append(row).append("' </p>");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateBoss(RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, LoggedUser loggedUser) throws IOException {
        final String userCode = helper.cellValue(rowData, CsvUpdateBossColumns.USER_CODE.getIndex());
        if (userCode.isEmpty()) {
            result.getSb().append("<p class='error modify-boss' >El campo 'Clave de usuario' es requerido ");
            return;
        }
        final String bossCode = helper.cellValue(rowData, CsvUpdateBossColumns.BOSS_CODE.getIndex());
        if (bossCode.isEmpty()) {
            result.getSb().append("<p class='error modify-boss' >El campo 'Clave de jefe' es requerido ");
            return;
        }
        try {
            IUserDAO userDao = getBean(IUserDAO.class);
            final UpdateBossStatus udpated = userDao.updateUserBoss(userCode, bossCode, loggedUser);
            if (udpated == null) {
                result.getSb().append("<p class='error modify-boss'>Error al grabar el jefe ")
                        .append(userCode).append(" para el usuario ").append(bossCode)
                        .append(" Detalles: ").append(getTag("modify-boss." + udpated))
                        .append("</p>");
            } else {
                switch (udpated) {
                    case SUCCESS:
                        result.getSb().append("<p class='done modify-boss' >El jefe ").append(userCode)
                                .append(" para el usuario ").append(bossCode).append(" se ha actualizado. </p>");
                        break;
                    case ALREADY_UPDATED:
                        result.getSb().append("<p class='done modify-boss' >El jefe ").append(userCode)
                                .append(" para el usuario ").append(bossCode).append(" fue previamente configurado. </p>");
                        break;
                    default:
                        result.getSb().append("<p class='error modify-boss'>Error al grabar el jefe ")
                                .append(userCode).append(" para el usuario ").append(bossCode)
                                .append(" Detalles: ").append(getTag("modify-boss." + udpated))
                                .append("</p>");
                        break;
                }
            }
        } catch (Exception e) {
            getLogger().error("Stack trace: ", e);
            result.getSb().append("<p class='error modify-boss'>Error al grabar el jefe ")
                    .append(userCode).append(" para el usuario ").append(bossCode).append("</p>");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @Override
    public void saveUser(
            final RowDTO rowData,
            final LoadCacheDTO cache,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            String account = helper.cellValue(rowData, CSVUserColumns.ACCOUNT.getIndex());
            boolean hasError = false;
            if (account.isEmpty()) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.ACCOUNT, BulkLoadError.EMPTY_FIELD, loggedUser);
            }
            String positionIdUser = helper.positionIdCSV_User(rowData);
            if (positionIdUser.endsWith("_") || positionIdUser.startsWith("_")) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.POSITION, BulkLoadError.INCORRECT_VALUE, loggedUser);
            }
            String userStatus = helper.userStatusCSV_User(rowData);
            if (!Utilities.isInteger(userStatus)) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.USER_STATUS, BulkLoadError.INCORRECT_TYPE, loggedUser);
            }
            if (Utilities.ParseInteger(userStatus) != User.STATUS.ACTIVE.getValue() 
                    && Utilities.ParseInteger(userStatus) != User.STATUS.INACTIVE.getValue()) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.USER_STATUS, BulkLoadError.INCORRECT_VALUE, loggedUser);
            }
            final String password = helper.cellValue(rowData, CSVUserColumns.PASSWORD.getIndex());
            if (password.isEmpty()) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.PASSWORD, BulkLoadError.EMPTY_FIELD, loggedUser);
            }
            String fullName = helper.cellValue(rowData, CSVUserColumns.NAME.getIndex());
            if (fullName.isEmpty()) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.ACCOUNT, CSVUserColumns.NAME, BulkLoadError.EMPTY_FIELD, loggedUser);
            }
            String bossAccount = helper.cellValue(rowData, CSVUserColumns.BOSS_ACCOUNT.getIndex());
            Long bossId = null;
            if (bossAccount.isEmpty()) {
                if  (!Utilities.getSettings().getUserDirectBossOptional()) {
                   hasError = true;
                   handleError(result, rowData, CSVUserColumns.ACCOUNT, CSVUserColumns.NAME, BulkLoadError.EMPTY_FIELD, loggedUser);
                }
            } else {
                bossId = cache.getUserCache().getUserIdByAccount(bossAccount.replaceAll("'", "''"));
                if (Objects.equals(bossId, 0L)) {
                    hasError = true;
                    handleError(result, rowData, CSVUserColumns.ACCOUNT, CSVUserColumns.NAME, BulkLoadError.INCORRECT_VALUE, loggedUser);
                }
            }
            if (hasError) {
                result.getSb().append("<p  class='error prf'>")
                        .append("Usuario invalido: {'").append(row).append("'}")
                        .append("</p>");
                return;
            }
            /*Busca el usuario*/
            Long userId = cache.getUserCache().getUserIdByAccount(account.replaceAll("'", "''"));
            if (userId == null || userId <= 0) {//No existe, hay que agregarlo
                User usr = new User();
                usr.setCode(account);
                usr.setIsAnonymous(false);
                usr.setCreationSource(UserCreationSource.BULK.getValue());
                usr.setIsAway(false);
                if (row.size() > CSVUserColumns.PASSWORD.getIndex()) {
                    usr.setHashedPassword(cache.getPasswordCache().get(password));
                } else {
                    usr.setHashedPassword(cache.getPasswordCache().get(account));
                }
                String mail = helper.cellValue(rowData, CSVUserColumns.MAIL.getIndex());
                usr.setCorreo(mail);
                usr.setCuenta(account);
                Long jobId = cache.getPositionCache().get(positionIdUser);
                if (jobId == null || Objects.equals(jobId, 0l) || Objects.equals(jobId, -1l)) {
                    handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.POSITION, BulkLoadError.NOT_EXIST, loggedUser);
                    result.getSb().append("<p class='error usr'>")
                            .append("Error al grabar el Usuario '").append(fullName).append("' El puesto '").append(positionIdUser).append("' no existe")
                            .append("</p>");
                    return;
                }
                usr.setDefaultWorkflowPosition(new Position(jobId));
                BusinessUnitDepartmentDTO department = cache.getBusinessUnitDepartmentCache().get(positionIdUser);
                if (department != null) {
                    usr.setBusinessUnitId(department.getBusinessUnitId());
                    usr.setDepartmentId(department.getDepartmentId());
                    usr.setBusinessUnitDepartmentId(department.getValue());
                    usr.setAskToRenewLocation(RenewDataStatus.DATA_RENEWED.value());
                } else {
                    usr.setAskToRenewLocation(RenewDataStatus.RENEW_DATA.value());
                }
                usr.setAskToRenewPassword(true);
                usr.setAskToRenewTimezone(true);
                usr.setScalable(1);
                usr.setSearchInSubfolders(Utilities.getSettings().getSearchSubFolder());
                usr.setDeleted(User.IS_NOT_DELETED);
                usr.setDescription(fullName);
                usr.setId(-1L);
                usr.setLang("");
                usr.setLocale("");
                usr.setAuthTypeIntegrated(1);
                usr.setAuthTypeLdap(1);
                usr.setAuthTypeLandingPage(1);
                usr.setAuthTypeOidc(0);
                usr.setDetailGridSize(0);
                usr.setFloatingGridSize(5);
                usr.setGridSize(15);
                Set<Position> setPositions = new HashSet<>();
                setPositions.add(new Position(jobId));
                String licenseCode = cache.getLicenseCodeCache().get(jobId);
                usr.setLicenseCode(licenseCode);
                usr.setPuestos(setPositions);
                usr.setBossId(bossId);
                Integer status = Utilities.ParseInteger(userStatus);
                final BulkLoadDTO log = result.getLog();
                final boolean notEnoughLicences = checkNotEnoughLicences(status, usr.getLicenseCode());
                if (notEnoughLicences) {
                    status = User.STATUS.INACTIVE.getValue();
                    usr.setCertificate(null);
                    usr.setInactiveBySystem(1);
                } else {
                    final ILicenseUserDAO licenseDao = getBean(ILicenseUserDAO.class);
                    final String encryptedCert = licenseDao.generateCertificateToken(usr.getCuenta());
                    usr.setCertificate(encryptedCert);   
                    usr.setInactiveBySystem(0);
                }
                usr.setStatus(status);
                usr = (User) makePersistent(usr, loggedUser.getId());
                if (usr == null) {
                    handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.NAME, BulkLoadError.PERSISTENT, loggedUser);
                    result.getSb().append("<p class='error usr'>").append("Error al grabar el Usuario '").append(fullName).append("'").append("</p>");
                } else {
                    handleCreated(result, rowData, CSVUserColumns.NAME, loggedUser);
                    if (notEnoughLicences) {
                        increaseInactiveUserCount(log);
                        increaseRequireLicenseCount(log);
                    } else if (Objects.equals(User.STATUS.ACTIVE.getValue(), status)) {
                        increaseActiveUserCount(log);
                    } else {
                        increaseInactiveUserCount(log);
                    }
                    result.getSb().append("<p class='added usr'>")
                            .append("- Registro "+((usr.getStatus() != null && usr.getStatus().equals(User.STATUS.ACTIVE.getValue()))  ? "activo" : "inactivo")+" - ")
                            .append(" Usuario ").append(fullName).append(" guardado correctamente. </p>");
                    cache.getUserCache().storeUserIdAccount(usr.getId(), account);
                }
                return;
            } else {
                Long jobId = cache.getPositionCache().get(positionIdUser);
                if (jobId <= 0) {
                    handleError(result, rowData, CSVUserColumns.NAME, CSVUserColumns.POSITION, BulkLoadError.NOT_EXIST, loggedUser);
                    result.getSb().append("<p class='error usr'>").append("Error al grabar el Usuario '").append(fullName).append("'").append("</p>");
                    return;
                }
                Boolean existsPosition = HQL_findSimpleLong(""
                        + " SELECT count(c.id.userId)"
                        + " FROM " + UserPositionSave.class.getCanonicalName() + " c "
                        + " WHERE c.id.userId = :userId"
                        + " AND c.id.positionId = :posId",
                        ImmutableMap.of(
                                "userId", userId,
                                "posId", jobId
                        )
                ) > 0;
                handleModified(result, rowData, CSVUserColumns.NAME, loggedUser);
                updateBulkDataUserExist(result, helper, userId, rowData, cache, loggedUser);
                if (existsPosition) {
                    result.getSb().append("<p class='done usr' >")
                            .append("- Registro "+((userStatus != null && Utilities.ParseInteger(userStatus) == User.STATUS.ACTIVE.getValue())  ? "activo" : "inactivo")+" - ")
                            .append("El Usuario ").append(fullName).append(" ya tiene asignado el puesto '").append(positionIdUser)
                            .append("'").append("</p>");
                    return;
                }
                saveLinkedItem(UserPositionSave.class, userId, jobId, loggedUser.getId());
                result.getSb().append("<p class='done usr' >")
                        .append("- Registro "+((userStatus != null && Utilities.ParseInteger(userStatus) == User.STATUS.ACTIVE.getValue())  ? "activo" : "inactivo")+" - ")                            
                        .append("El Usuario ").append(fullName).append(" se ha actualizado. </p>");
                return;
            }
        } catch (final Exception e) {
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            final String error;
            if (rootCause != null) {
                error = ExceptionUtils.getRootCauseMessage(rootCause);
            } else {
                rootCause = e;
                error = ExceptionUtils.getRootCauseMessage(e);
            }
            getLogger().error("Error al cargar usuario {} con error {}.", error, row, rootCause);
            result.getSb().append("<p  class='error prf'>Usuario con error: {'").append(row).append("'}</p>");
            return;
        }
    }

    private Integer updateBulkDataUserExist(
            final BackendUploadResult result,
            final BackendUploaderHelper helper,
            final Long userId,
            final RowDTO rowData,
            final LoadCacheDTO cache,
            final LoggedUser loggedUser)
    throws ExecutionException {
        final Map<String, Object> params = new HashMap<>(); // Params to update
        String fullName = helper.cellValue(rowData, CSVUserColumns.NAME.getIndex());
        if (fullName != null && !fullName.isEmpty()) {
            params.put("description", fullName);
        }
        String account = helper.cellValue(rowData, CSVUserColumns.ACCOUNT.getIndex());
        if (account != null && !account.isEmpty()) {
            params.put("cuenta", account);
        }
        String hashedPassword;
        if (rowData.getValue().size() > CSVUserColumns.PASSWORD.getIndex()) {
            String password = helper.cellValue(rowData, CSVUserColumns.PASSWORD.getIndex());
            hashedPassword = cache.getPasswordCache().get(password);
        } else {
            hashedPassword = cache.getPasswordCache().get(account);
        }
        params.put("hashedPassword", hashedPassword);
        params.put("contrasena", null);
        String email = helper.cellValue(rowData, CSVUserColumns.MAIL.getIndex());
        if (email != null && !email.isEmpty()) {
            params.put("correo", email);
        }
        String bossAccount = helper.cellValue(rowData, CSVUserColumns.BOSS_ACCOUNT.getIndex());
        Long bossId = cache.getUserCache().getUserIdByAccount(bossAccount.replaceAll("'", "''"));
        if (bossId != null && !Objects.equals(bossId, 0L)) {
            params.put("bossId", bossId);
        } else {
            params.put("bossId", null);
        }
        Integer status = Integer.valueOf(helper.userStatusCSV_User(rowData));
        String positionIdUser = helper.positionIdCSV_User(rowData);
        Long jobId = cache.getPositionCache().get(positionIdUser);
        Integer inactiveBySystem;
        final BulkLoadDTO log = result.getLog();
        String licenseCode = cache.getLicenseCodeCache().get(jobId);
        if (checkNotEnoughLicences(status, licenseCode)) {
            status = User.STATUS.INACTIVE.getValue();
            inactiveBySystem = 1;
            increaseInactiveUserCount(log);
            increaseRequireLicenseCount(log);
        } else if (Objects.equals(User.STATUS.ACTIVE.getValue(), status)) {
            inactiveBySystem = 0;
            increaseActiveUserCount(log);
        } else {
            inactiveBySystem = 0;
            increaseInactiveUserCount(log);
        }
        params.put("status", status);
        params.put("inactiveBySystem", inactiveBySystem);
        UserUtil.updateUser(params, loggedUser.getId(), userId, null);
        return status;
    }

    private boolean checkNotEnoughLicences(Integer status, String licenseCode) {
        if (Objects.equals(status, User.STATUS.INACTIVE.getValue())) {
            return false;
        }
        final Integer exceededLicences = Utilities.exceededLicences(licenseCode, getBean(IUserDAO.class));
        return exceededLicences > 0 || exceededLicences == 0;
    }
    
    private void increaseRequireLicenseCount(final BulkLoadDTO log) {
        if (log.getRequireLicenseCount() == null) {
            log.setRequireLicenseCount(0L);
        }
        log.setRequireLicenseCount(log.getRequireLicenseCount() + 1);
    }

    private void increaseActiveUserCount(final BulkLoadDTO log) {
        if (log.getActiveUsersCount() == null) {
            log.setActiveUsersCount(0L);
        }
        log.setActiveUsersCount(log.getActiveUsersCount() + 1);
    }

    private void increaseInactiveUserCount(final BulkLoadDTO log) {
        if (log.getInactiveUsersCount() == null) {
            log.setInactiveUsersCount(0L);
        }
        log.setInactiveUsersCount(log.getInactiveUsersCount() + 1);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertOrganizationalUnit(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            /*Busca la unidad organizacional*/
            CRUD_OrganizationalUnit oruCRUD = new CRUD_OrganizationalUnit();
            oruCRUD.setActiveServices("*");
            List<OrganizationalUnit> oruList = HQL_findByQuery(""
                    + " SELECT c "
                    + " FROM " + OrganizationalUnit.class.getCanonicalName() + " c"
                    + " WHERE c.code = :organizationUnitCode",
                    "organizationUnitCode", row.get(0)
            );
            if (oruList.isEmpty()) {//No existe, hay que agregarlo
                OrganizationalUnit oru = new OrganizationalUnit();
                oru.setCode(row.get(0));
                oru.setDeleted(OrganizationalUnit.IS_NOT_DELETED);
                oru.setDescription(row.get(1));
                oru.setDocumentManagerId(loggedUser.getId());
                oru.setId(-1L);
                oru.setStatus(OrganizationalUnit.ACTIVE_STATUS);
                GenericSaveHandle oruSH = oruCRUD.save(oru);
                if (oruSH.getOperationEstatus() == 0) {
                    result.getSb().append("<p class='error une'>Error al grabar la Unidad de Negocio '").append(row.get(1)).append("' </p>");
                } else {
                    result.getSb().append("<p class='added une'>Unidad de Negocio ").append(row.get(1)
                    ).append(" guardada correctamente. </p>");
                }
            } else {
                result.getSb().append("<p class='done une' >La Unidad de negocio ").append(row.get(1)).append(" ya se ha agregado. </p>");
            }
        } catch (final Exception e) {
            getLogger().error("Error al grabar la Unidad de Negocio ':", e);
            result.getSb().append("<p class='error une'>Error al grabar la Unidad de Negocio '").append(row.get(1)).append("' </p>");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertBusinessUnit(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            String businessUnitCode = helper.cellValue(rowData, CSVOrganizationColumns.BUSINESS_UNIT_CODE.getIndex());
            String businessUnitName = helper.cellValue(rowData, CSVOrganizationColumns.BUSINESS_UNIT_NAME.getIndex());
            String organizationalUnitName = helper.cellValue(rowData, CSVOrganizationColumns.ORGANIZATION_UNIT_NAME.getIndex());
            Map<String, Long> resultStatus = insertBusinessUnitStatus(businessUnitCode, businessUnitName, organizationalUnitName, loggedUser);
            if (resultStatus.get("status").equals(2L)) {
                result.getSb().append(BusinessUnitUtil.interpolate(
                        "<p class='done bun' >${TheFacility} '" + businessUnitCode + "' ya se ha agregado. </p>"
                ));
            } else if (resultStatus.get("status").equals(0L)) {
                result.getSb().append(BusinessUnitUtil.interpolate(
                        "<p class='error bun'>Error al grabar ${theFacility} " + businessUnitCode + "</p>"
                ));
            } else if (resultStatus.get("status").equals(3L)) {
                result.getSb().append(BusinessUnitUtil.interpolate(""
                        + "<p class='error bun'>Error al grabar ${theFacility} " + businessUnitCode + "."
                        + " La organización '" + organizationalUnitName + "' no existe</p>"
                ));
            } else {
                result.getSb().append(BusinessUnitUtil.interpolate(
                        "<p class='added bun'>${TheFacility} " + businessUnitCode + " guardada correctamente. </p>"
                ));
            }
        } catch (Exception e) {
            getLogger().error("Stack trace: ", e);
            result.getSb().append(BusinessUnitUtil.interpolate(
                    "<p class='error bun'>Error al grabar ${theFacility} {'" + row + "'}</p>"
            ));
        }
    }

    private Map<String, Long> insertBusinessUnitStatus(
            String businessUnitCode,
            String businessUnitName,
            String organizationUnitName,
            LoggedUser loggedUser
    ) {
        Map<String, Long> m = new HashMap<>();
        Long bunId = HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                + " WHERE c.code = :businessUnitCode", "businessUnitCode", businessUnitCode);
        BusinessUnit bun;
        if (bunId == null || bunId == 0L) {//No existe, hay que agregarlo
            bun = new BusinessUnit();
            bun.setCode(businessUnitCode);
            bun.setDeleted(BusinessUnit.IS_NOT_DELETED);
            bun.setDescription(businessUnitName);
            bun.setDocumentManagerId(loggedUser.getId());
            bun.setId(-1L);
            bun.setStatus(BusinessUnit.STATUS.ACTIVE.getValue());
            bun.setAttendantId(loggedUser.getId());
            Long organizationalUnitId;
            /*Busca la unidad organizacional*/
            if (organizationUnitName == null || organizationUnitName.isEmpty()) {
                organizationalUnitId = HQL_findSimpleLong(""
                        + " SELECT min(c.id)"
                        + " FROM " + OrganizationalUnit.class.getCanonicalName() + " c");
            } else {
                organizationalUnitId = HQL_findSimpleLong(""
                        + " SELECT c.id"
                        + " FROM " + OrganizationalUnit.class.getCanonicalName() + " c"
                        + " WHERE c.description = :organizationUnitName", "organizationUnitName", organizationUnitName);
            }
            if (organizationalUnitId != null && organizationalUnitId > 0) {
                bun.setOrganizationalUnitId(organizationalUnitId);
            } else {
                m.put("status", 3L);
                m.put("id", null);
                return m;
            }
            CRUD_BusinessUnit bunCRUD = new CRUD_BusinessUnit();

            bunCRUD.setActiveServices("*");
            GenericSaveHandle oruSH = bunCRUD.save(bun, getBean(IBusinessUnitDAO.class));
            m.put("status", Long.valueOf(oruSH.getOperationEstatus().toString()));
            m.put("id", Long.valueOf(oruSH.getSavedId()));
        } else {
            m.put("status", 2L);
            m.put("id", bunId);
        }
        return m;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertRegion(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            String regionCode = helper.cellValue(rowData, CSVOrganizationColumns.REGION_CODE.getIndex());
            String regionName = helper.cellValue(rowData, CSVOrganizationColumns.REGION_NAME.getIndex());
            Map<String, Long> resultStatus = insertRegionStatus(regionCode, regionName, loggedUser);
            if (resultStatus.get("status").equals(2L)) {
                result.getSb().append("<p class='done bun' >Region '").append(regionCode).append("' ya se ha agregado. </p>");
            }
            if (resultStatus.get("status").equals(0L)) {
                result.getSb().append("<p class='error bun'>Error al grabar Region ").append(regionCode).append("</p>");
            } else {
                result.getSb().append("<p class='added bun'>Region ").append(regionCode).append(" guardada correctamente. </p>");
            }
        } catch (Exception e) {
            getLogger().error("Stack trace: ", e);
            result.getSb().append("<p class='error bun'>Error al grabar Region{'").append(row).append("'}</p>");
        }
    }

    private Map<String, Long> insertRegionStatus(
            String regionCode,
            String regionName,
            LoggedUser loggedUser
    ) {
        Map<String, Long> result = new HashMap<>();
        Long regionId = HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Region.class.getCanonicalName() + " c"
                + " WHERE c.code = :regionCode", "regionCode", regionCode);
        if (regionId == null || regionId == 0L) {//No existe, hay que agregarlo
            final Region entity = new Region();
            entity.setCode(regionCode);
            entity.setDeleted(Region.IS_NOT_DELETED);
            entity.setDescription(regionName);
            entity.setId(-1L);
            entity.setStatus(Region.STATUS.ACTIVE.getValue());
            final Region saved = (Region) makePersistent(entity, loggedUser.getId());
            result.put("status", 1l);
            result.put("id", saved.getId());
        } else {
            result.put("status", 2L);
            result.put("id", regionId);
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertZone(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            String zoneCode = helper.cellValue(rowData, CSVOrganizationColumns.ZONE_CODE.getIndex());
            String zoneName = helper.cellValue(rowData, CSVOrganizationColumns.ZONE_NAME.getIndex());
            Map<String, Long> resultStatus = insertZoneStatus(zoneCode, zoneName, loggedUser);
            if (resultStatus.get("status").equals(2L)) {
                result.getSb().append("<p class='done bun' >Zone '").append(zoneCode).append("' ya se ha agregado. </p>");
            }
            if (resultStatus.get("status").equals(0L)) {
                result.getSb().append("<p class='error bun'>Error al grabar Zone ").append(zoneCode).append("</p>");
            } else {
                result.getSb().append("<p class='added bun'>Zone ").append(zoneCode).append(" guardada correctamente. </p>");
            }
        } catch (Exception e) {
            getLogger().error("Stack trace: ", e);
            result.getSb().append("<p class='error bun'>Error al grabar Zone{'").append(row).append("'}</p>");
        }
    }

    private Map<String, Long> insertZoneStatus(
            String zoneCode,
            String zoneName,
            LoggedUser loggedUser
    ) {
        Map<String, Long> result = new HashMap<>();
        Long zoneId = HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Zone.class.getCanonicalName() + " c"
                + " WHERE c.code = :zoneCode", "zoneCode", zoneCode);
        if (zoneId == null || zoneId == 0L) {//No existe, hay que agregarlo
            final Zone entity = new Zone();
            entity.setCode(zoneCode);
            entity.setDeleted(Zone.IS_NOT_DELETED);
            entity.setDescription(zoneName);
            entity.setId(-1L);
            entity.setStatus(Zone.STATUS.ACTIVE.getValue());
            final Zone saved = (Zone) makePersistent(entity, loggedUser.getId());
            result.put("status", 1l);
            result.put("id", saved.getId());
        } else {
            result.put("status", 2L);
            result.put("id", zoneId);
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void savePosition(
            final RowDTO rowData, 
            final LoadCacheDTO cache,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            String positionId = helper.positionIdCSV_User(rowData);
            String positionStatus = helper.positionStatusCSV_User(rowData);
            boolean hasError = false;
            String reason;
            if (positionId.isEmpty()) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.POSITION, BulkLoadError.EMPTY_FIELD, loggedUser);
            }
            if (positionId.endsWith("_") || positionId.startsWith("_")) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.POSITION, BulkLoadError.INCORRECT_VALUE, loggedUser);
            }
            if (!Utilities.isInteger(positionStatus)) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.POSITION_STATUS, BulkLoadError.INCORRECT_TYPE, loggedUser);
            }
            if (Utilities.ParseInteger(positionStatus) != Position.STATUS.ACTIVE.getValue()
                    && Utilities.ParseInteger(positionStatus) != Position.STATUS.INACTIVE.getValue()) {
                hasError = true;
                handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.POSITION_STATUS, BulkLoadError.INCORRECT_VALUE, loggedUser);
            }
            if (hasError) {
                reason = "Puesto invalido: {'" + row + "'}";
                result.getSb().append("<p  class='error prf'>").append(reason).append("</p>");
                return;
            }
            /*Busca el departamento*/
            String businessUnit = helper.cellValue(rowData, CSVUserColumns.BUSINESS_UNIT.getIndex());
            String department = helper.cellValue(rowData, CSVUserColumns.DEPARTMENT.getIndex());
            BusinessUnitDepartmentLite bud = (BusinessUnitDepartmentLite) HQL_findSimpleObject(""
                    + " SELECT bu "
                    + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " c"
                    + " JOIN " + BusinessUnitDepartmentLite.class.getCanonicalName() + " bu"
                    + " ON c.id = bu.id"
                    + " WHERE c.department.description = :departmentDescription"
                    + " AND c.businessUnit.code = :businessUnitCode",
                    ImmutableMap.of(
                            "departmentDescription", department.replaceAll("'", "''"),
                            "businessUnitCode", businessUnit.replaceAll("'", "''")
                    )
            );
            if (bud == null) {
                handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.DEPARTMENT, BulkLoadError.NOT_EXIST, loggedUser);
                result.getSb().append("<p class='error usr'>Error al grabar el Puesto '").append(positionId)
                        .append("'. El departamento '").append(department).append("' no existe para la planta '").append(businessUnit).append("'</p>");
                return;
            }
            Map params = new HashMap();
            String process = helper.cellValue(rowData, CSVUserColumns.PROCESS.getIndex());
            params.put("processDescription", process.replaceAll("'", "''"));
            params.put("departmentCode", department.replaceAll("'", "''"));
            Long departmentProcessId = HQL_findSimpleLong(""
                    + " SELECT c.id"
                    + " FROM " + DepartmentProcess.class.getCanonicalName() + " c "
                    + " WHERE c.process.description = :processDescription"
                    + " AND c.department.code = :departmentCode", params);
            Long posId = HQL_findSimpleLong(""
                    + " SELECT c.id"
                    + " FROM " + Position.class.getCanonicalName() + " c"
                    + " WHERE c.code = :positionCode",
                    ImmutableMap.of("positionCode", positionId.replaceAll("'", "''"))
            );
            String profileCode = helper.profileIdCSV_User(rowData);
            Long profileId = cache.getProfileCache().get(profileCode);
            if (profileId == null || profileId == 0) {
                handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.PROFILE, BulkLoadError.NOT_EXIST, loggedUser);
                result.getSb().append("<p class='error usr'>Error al grabar el Puesto '").append(positionId)
                        .append("'. El perfil '").append(profileCode).append("' no existe'</p>");
                return;
            }
            Long uneId = HQL_findSimpleLong(""
                        + " SELECT c.organizationalUnit.id"
                        + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                        + " WHERE c.code = :businessUnitCode",
                        ImmutableMap.of("businessUnitCode", businessUnit.replaceAll("'", "''"))
                );
            Long bunId = HQL_findSimpleLong(""
                        + " SELECT c.id"
                        + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                        + " WHERE c.code = :businessUnitCode",
                        ImmutableMap.of("businessUnitCode", businessUnit.replaceAll("'", "''"))
                );
            if (posId == null || posId == 0L) {//No existe, hay que agregarlo
                Position pos = new Position();
                pos.setCode(positionId);
                if (!businessUnit.isEmpty()
                        && (bunId == null || Objects.equals(bunId, 0l) || Objects.equals(bunId, -1l))) {
                    handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.BUSINESS_UNIT, BulkLoadError.NOT_EXIST, loggedUser);
                    result.getSb().append("<p class='error usr'>Error al grabar el Puesto '").append(positionId)
                            .append("'. La planta '").append(businessUnit).append("' no existe</p>");
                    return;
                }
                pos.setCorp(new OrganizationalUnit(uneId));
                pos.setDeleted(0);
                Set<BusinessUnitDepartmentLite> departments = new HashSet<>();
                departments.add(bud);
                pos.setDepartamentos(departments);

                pos.setDescription(positionId.replace("_", " "));
                pos.setId(-1L);
                pos.setPerfil(new Profile(profileId));
                pos.setStatus(Utilities.ParseInteger(positionStatus));
                pos.setUne(new BusinessUnit(bunId));
                List<Long> departmentProcessIds = new ArrayList<>();
                if (departmentProcessId != null && departmentProcessId != 0L) {
                    departmentProcessIds.add(departmentProcessId);
                }
                IPositionLoadDAO dao = getBean(IPositionLoadDAO.class);
                GenericSaveHandle posSH = dao.save(pos, loggedUser);
                if (posSH.getOperationEstatus() == 0) {
                    handleError(result, rowData, CSVUserColumns.POSITION, CSVUserColumns.POSITION, BulkLoadError.PERSISTENT, loggedUser);
                    result.getSb().append("<p class='error pst'>Error al grabar el Puesto '")
                            .append(positionId.replaceAll("'", "''")).append("' </p>");
                } else {
                    handleCreated(result, rowData, CSVUserColumns.POSITION, loggedUser);
                    dao.savePositionDepartmentProcesses(pos.getId(), departmentProcessIds);
                    result.getSb().append("<p class='added pst'>")
                            .append("- Registro "+(pos.getStatus() != null && Utilities.ParseInteger(positionStatus)==User.STATUS.ACTIVE.getValue()  ? "activo" : "inactivo")+" - ")
                            .append("Puesto '").append(positionId.replaceAll("'", "''"))
                            .append("' guardado correctamente. </p>");
                }
            } else {
                params.clear();
                params.put("positionId", posId);
                List<Long> departmentProcessIds = HQL_findByQuery(""
                        + " SELECT c.id.department_process_id"
                        + " FROM " + PositionDepartmentProcess.class.getCanonicalName() + " c "
                        + " WHERE c.id.puesto_id = :positionId"
                        + " GROUP BY c.id.department_process_id", params);
                if (departmentProcessId != null && departmentProcessId != 0L
                        && !departmentProcessIds.contains(departmentProcessId)) {
                    departmentProcessIds.add(departmentProcessId);
                }
                IPositionLoadDAO dao = getBean(IPositionLoadDAO.class);
                dao.savePositionDepartmentProcesses(posId, departmentProcessIds);

                PositionSave posSave = (PositionSave) HQLT_findById(PositionSave.class, posId);
                posSave.setPerfil(new Profile(profileId));
                if (uneId != null) {
                    posSave.setCorp(new OrganizationalUnit(uneId));
                }
                if (bunId != null){
                    posSave.setUne(new BusinessUnit(bunId));
                }
                if (bud != null) {
                    Set<BusinessUnitDepartmentLite> departments = new HashSet<>();
                    departments.add(bud);
                    posSave.setDepartamentos(departments);
                }
                if (positionId != null) {
                    posSave.setDescription(positionId.replace("_", " "));
                }
                if (positionStatus != null) {
                    posSave.setStatus(Utilities.ParseInteger(positionStatus));
                }
                handleModified(result, rowData, CSVUserColumns.POSITION, loggedUser);
                if (posSave.getDepartamentos().add(bud)) {
                    makePersistent(posSave, loggedUser.getId());
                    result.getSb().append("<p class='done pst'>Puesto '").append(positionId.replaceAll("'", "''"))
                            .append("' ya se ha agregado y se sumo el Departamento ").append(department.replaceAll("'", "''")).append(".. <br />");
                    return;
                }
                makePersistent(posSave, loggedUser.getId());
                result.getSb().append("<p class='done pst'>")
                        .append("- Registro " + ((positionStatus != null && positionStatus.equals(User.STATUS.ACTIVE.getValue()))  ? "activo" : "inactivo")+ " - ")
                        .append( "Puesto '").append(positionId.replaceAll("'", "''"))
                        .append("' ya se ha agregado. <br />");
            }

        } catch (Exception e) {
            getLogger().error("Stack trace: ", e);
            result.getSb().append("<p class='error bun'>Error al grabar el puesto {'").append(row).append("'}</p>");
        }
    }

    private Long saveNode(String name, Long parent, String path, LoggedUser loggedUser) {
        Long nodeId;
        Long existingNodeId = HQL_findSimpleLong(""
                + " SELECT c.id "
                + " FROM " + Node.class.getCanonicalName() + " c"
                + " WHERE c.path = :path", "path", path);
        if (existingNodeId > 0) {
            nodeId = existingNodeId;
        } else {
            getLogger().trace("Inserting Node: [{}]", path);
            NodeSimple n = new NodeSimple(-1l);
            n.setCode(name);
            n.setDescription(name);
            n.setPath(path);
            n.setModule(Module.DOCUMENT.getKey());
            n.setStatus(Node.STATUS.ACTIVE.getValue());
            if (parent == 0L) {//Es de nivel superior
                n.setTopLevel(1);
            } else {// no es de nivel superior, hay que buscar asignarle a su padre suyo de el
                n.setTopLevel(0);
            }
            n.setParent(parent);
            n = (NodeSimple) makePersistent(n, loggedUser.getId());
            nodeId = n.getId();
            final NodeAccess loadedNode = (NodeAccess) HQLT_findById(NodeAccess.class, nodeId);
            makePersistent(loadedNode, loggedUser.getId());
        }
        return nodeId;
    }

    private NodeSimple insertNode(final RowDTO rowData, final LoggedUser loggedUser) {
        final List<String> row = rowData.getValue();
        Long parent = 0L;
        String path = "";
        for (String nodeName : row) {
            path = path + nodeName;
            if (!path.endsWith("\\")) {
                path += "\\";
            }
            parent = saveNode(nodeName, parent, path, loggedUser);
        }
        return (NodeSimple) HQLT_findById(NodeSimple.class, parent);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertDynamicField(RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            DynamicField dynamicField = null;
            List<Map<String, Object>> queryExists = HQL_selectMapQuery(""
                    + " SELECT new map("
                    + "     c.code as clave, "
                    + "     c.id as idDynamicField"
                    + ") "
                    + " FROM " + DynamicField.class.getCanonicalName() + " c "
                    + " WHERE c.code = '" + helper.nullAsEmpty(row.get(0)) + "'",
                    true,
                    CacheRegion.DYNAMIC_FIELD,
                    0
            );
            if (queryExists.isEmpty()) {
                DynamicField.DYNAMIC_FIELD_TYPE typeCatalog = DynamicField.DYNAMIC_FIELD_TYPE.fromType(row.get(3));
                switch (typeCatalog) {
                    case BIG_TEXT:
                    case TEXT:
                        dynamicField = new DynamicField(-1L, typeCatalog);
                        break;
                    case CATALOG:
                        dynamicField = new DynamicField(-1L, DynamicField.DYNAMIC_FIELD_TYPE.CATALOG);
                        break;
                    case CATALOG_MULTIPLE:
                        dynamicField = new DynamicField(-1L, DynamicField.DYNAMIC_FIELD_TYPE.CATALOG_MULTIPLE);
                        break;
                    case LINK:
                        dynamicField = new DynamicField(-1L, DynamicField.DYNAMIC_FIELD_TYPE.LINK);
                        break;
                    default:
                        break;
                }
                if (dynamicField == null) {
                    result.getSb()
                            .append("<p class='error one_tab '>Error al insertar, tipo de campo dinámico invalido:{'")
                            .append(row).append("'}</p>");
                    return;
                }
                dynamicField.setId(-1L);
                dynamicField.setCode(helper.nullAsEmpty(row.get(0)));
                dynamicField.setDescription(helper.nullAsEmpty(row.get(1)));
                dynamicField.setStatus(DynamicField.ACTIVE_STATUS);
                dynamicField.setDeleted(0);
                dynamicField.setLastModifiedDate(new Date());
                dynamicField.setCreatedDate(new Date());
                dynamicField.setCreatedBy(loggedUser.getId());
                dynamicField.setLastModifiedBy(loggedUser.getId());
                dynamicField.setMandatory(1);
                dynamicField.setSearchAlwaysVisible(1);
                makePersistent(dynamicField, loggedUser.getId());
                insertValueDynamicField(helper.nullAsEmpty(row.get(2)), dynamicField.getId(), loggedUser);
                result.getSb().append("<p class='added pro'> ").append(dynamicField.getCode())
                        .append(" - guardado correctamente en campos personalizados ");
            } else {
                insertValueDynamicField(helper.nullAsEmpty(row.get(2)), (Long) queryExists.get(0).get("idDynamicField"), loggedUser);
                result.getSb().append("<p class='done pro'> El catalogo ")
                        .append(helper.nullAsEmpty(row.get(1))).append(" - ya existe, pero se registro el campo ")
                        .append(helper.nullAsEmpty(row.get(2))).append(" en el tipo de catalago ");

            }
        } catch (Exception e) {
            getLogger().error("There was an error ", e);
            result.getSb().append("<p class='error one_tab '>Error al insertar</p>");
        }
    }

    private DynamicFieldValue insertValueDynamicField(String row, Long dynamicId, LoggedUser loggedUser) {
        DynamicFieldValue dynamicFieldValue = new DynamicFieldValue();
        dynamicFieldValue.setId(-1L);
        dynamicFieldValue.setCode(row);
        dynamicFieldValue.setDescription(row);
        dynamicFieldValue.setStatus(DynamicFieldValue.ACTIVE_STATUS);
        dynamicFieldValue.setDeleted(DynamicFieldValue.IS_NOT_DELETED);
        dynamicFieldValue.setLastModifiedDate(new Date());
        dynamicFieldValue.setCreatedDate(new Date());
        dynamicFieldValue.setCreatedBy(loggedUser.getId());
        dynamicFieldValue.setLastModifiedBy(loggedUser.getId());
        dynamicFieldValue.setDynamicFieldId(dynamicId);
        makePersistent(dynamicFieldValue, loggedUser.getId());
        return dynamicFieldValue;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void relationTypeDocuments(RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            DocumentTypeDynamicField documentTypeDynamicField = null;
            Long dynamicId = HQL_findSimpleLong(" "
                    + " SELECT c.id "
                    + " FROM " + DynamicField.class.getCanonicalName() + " c "
                    + " WHERE c.code = '" + helper.nullAsEmpty(row.get(1)) + "'");
            Long documentTypeId = HQL_findSimpleLong(""
                    + " SELECT c.id "
                    + " FROM " + DocumentType.class.getCanonicalName() + " c "
                    + " WHERE c.code = '" + helper.nullAsEmpty(row.get(0)) + "'");
            Long relationExist = HQL_findSimpleLong(""
                    + " SELECT c "
                    + " FROM " + DocumentTypeDynamicField.class.getCanonicalName() + " c "
                    + " JOIN c.id dtd "
                    + " WHERE dtd.dynamicFieldId = " + dynamicId + " "
                    + " AND dtd.documentTypeId = " + documentTypeId
            );
            if (relationExist.equals(0L)) {
                documentTypeDynamicField = new DocumentTypeDynamicField(dynamicId, documentTypeId);
                makePersistent(documentTypeDynamicField, loggedUser.getId());
                result.getSb().append("<p class='added '> La relacion del tipo de documento '")
                        .append(helper.nullAsEmpty(row.get(0))).append("' con el campo dinamico ")
                        .append(helper.nullAsEmpty(row.get(1))).append(" se registro correctamente ");
            } else {
                result.getSb().append("<p class='added '> La relacion del tipo de documento '")
                        .append(helper.nullAsEmpty(row.get(0))).append("' con el campo dinamico ")
                        .append(helper.nullAsEmpty(row.get(1))).append(" ya existe ");
            }
        } catch (Exception e) {
            getLogger().error("There was an error ", e);
            result.getSb().append("<p class='error'>Error al realizar la relacion </p>");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<String> getHeader(final RowDTO rowData, LoadCacheDTO cache, final LoggedUser loggedUser) {
        final List<String> row = rowData.getValue();
        List<String> head = new ArrayList<>();
        for (int i = 3; i < row.size(); i++) {
            head.add(row.get(i));
        }
        return head;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void modifyDocuments(
            LoadCacheDTO cache,
            IDynamicFieldDAO dao,
            ICodeSequenceDAO codeSequenceDAO,
            RowDTO rowData,
            final BackendUploadResult result,
            List<String> head,
            LoggedUser loggedUser
    ) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            Long typeDocumentId = HQL_findSimpleLong(""
                    + " SELECT doc.id "
                    + " FROM " + DocumentType.class.getCanonicalName() + " doc "
                    + " WHERE doc.description = '" + helper.nullAsEmpty(row.get(2)) + "'"
            );

            if (typeDocumentId.equals(0L)) {
                result.getSb().append("<p class='error prf '>El tipo de documento '")
                        .append(helper.nullAsEmpty(row.get(2))).append("' que intenta relacionar no existe </p>");
                return;
            }
            LinkedHashMap<String, Object> params = new LinkedHashMap<>();
            for (int j = 0; j < head.size(); j++) {
                String query = HQL_findSimpleString(""
                        + " SELECT dyn.code "
                        + " FROM " + DynamicField.class.getCanonicalName() + " dyn "
                        + " WHERE dyn.description = '" + head.get(j) + "'"
                );
                if (!query.isEmpty()) {
                    if (!helper.nullAsEmpty(row.get(j + 3)).equals("N/A")) {
                        params.put(query, row.get(j + 3));
                    }
                }
            }

            DynamicTableHelper dynHelper = new DynamicTableHelper(codeSequenceDAO);
            /**
             * Para mejorar la velocidad de carga en versiones menor o igual 2.12 reemplazar tableName ? getDyn... por el valor directo Ej. DYNTABLE_1 , Lo
             * anterior con la intención de eliminar el uso de "getBean"
             *
             */
            String tableName = dao.getDynamicFields(
                    DocumentType.class.getCanonicalName(),
                    null,
                    null,
                    new HashMap<>(),
                    loggedUser.getId(),
                    typeDocumentId
            ).getDynamicTableName();
            DynamicFieldInsertDTO insertDynamic = dynHelper.persist(-1L, params, tableName);

            Request request = (Request) HQL_findSimpleObject(""
                    + " SELECT req "
                    + " FROM " + Request.class.getCanonicalName() + " req "
                    + " WHERE req.documentCode = '" + helper.nullAsEmpty(row.get(1)) + "'"
            );
            if (request == null || request.getId().equals(0L)) {
                result.getSb().append("<p class='error prf'>El nombre de la clave '")
                        .append(helper.nullAsEmpty(row.get(1))).append(" del documento que intenta buscar no existe en la base de datos.</p>");
                return;
            }
            request.setDynamicFieldInsertDTO(insertDynamic);
            makePersistent(request, loggedUser.getId());

            Document document = (Document) HQL_findSimpleObject(""
                    + " SELECT doc "
                    + " FROM " + Document.class.getCanonicalName() + " doc "
                    + " WHERE doc.code =  :code",
                    ImmutableMap.of("code", helper.nullAsEmpty(row.get(1)))
            );
            document.setDynamicFieldInsertDTO(insertDynamic);
            makePersistent(document, loggedUser.getId());

            result.getSb().append("<p class='done prf'> El documento ").append(document.getDescription()).append(" se actualizo correctamente ");
        } catch (Exception e) {
            getLogger().error("There was an error ", e);
            result.getSb().append("<p class='error prf'>Error al actualizar el documento con la clave '")
                    .append(helper.nullAsEmpty(row.get(1))).append("' (Repetido) </p>");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void documentReader(RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            DocumentRef document = (DocumentRef) HQL_findSimpleObject(""
                    + " SELECT doc "
                    + " FROM " + DocumentRef.class.getCanonicalName() + " doc "
                    + " WHERE doc.code =  '" + helper.nullAsEmpty(row.get(0)) + "'"
            );
            if (document == null || document.getId().equals(0L)) {
                result.getSb().append("<p class='error prf'>El documento con la clave ")
                        .append(helper.nullAsEmpty(row.get(0))).append(" que intenta buscar no existe en la base de datos.</p>");
                return;
            }
            List<Long> users = new ArrayList<>();
            List<String> nameUser = new ArrayList<>();
            for (int i = 1; i < row.size(); i++) {
                if (!helper.nullAsEmpty(row.get(i)).isEmpty()) {
                    Long userId = cache.getUserCache().getUserIdByAccount(helper.nullAsEmpty(row.get(i)));
                    if (userId != null && !Objects.equals(userId, 0L)) {
                        users.add(userId);
                        nameUser.add(row.get(i));
                    } else {
                        result.getSb().append("<p class='error prf'>El usuario con la cuenta ")
                                .append(helper.nullAsEmpty(row.get(i))).append(" no existe.</p>");
                    }
                }
            }
            if (users.size() != 0L) {
                IDocumentReaderDAO readerDAO = getBean(IDocumentReaderDAO.class);
                if (!readerDAO.saveReaders(document.getId(), users, loggedUser).isEmpty()) {
                    result.getSb().append("<p class='added prf'>Se agregaron exitosamente los lectores con las cuentas <b>")
                            .append(nameUser).append("</b> al documento <b>").append(document.getDescription())
                            .append("</b> con la clave <b>").append(helper.nullAsEmpty(row.get(0))).append("</b>.</p>");
                } else {
                    result.getSb().append("<p class='error prf'>El documento con la clave ")
                            .append(helper.nullAsEmpty(row.get(0))).append(" no se pudo relacionar con los lectores.</p>");
                }
            } else {
                result.getSb().append("<p class='done one_tab'>No existen usuarios lectores para el documento <b>")
                        .append(document.getDescription()).append("</b>.</p>");
            }
            result.getSb().append(result.toString());
        } catch (Exception e) {
            getLogger().error("There was an error ", e);
            result.getSb().append("Error en el metodo");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void updateDocumentDate(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        String documentCode = "";
        String documentVersion = "";
        String requestedOn = "";
        try {
            documentCode = helper.nullAsEmpty(row.get(2));
            documentVersion = helper.nullAsEmpty(row.get(4));
            requestedOn = helper.nullAsEmpty(row.get(5));
            if (requestedOn == null || requestedOn.trim().isEmpty()) {
                result.getSb().append("<p class='error'>Error en request, ")
                        .append(" la fecha  '").append(requestedOn).append("' no es un valor válido para el documento")
                        .append(" con clave '").append(documentCode).append("' y version '").append(documentVersion).append("'</p>");
                return;
            }
            Date createdOn = Utilities.parseDateBy(requestedOn, "dd/MM/yyyy");
            if (createdOn == null) {
                result.getSb().append("<p class='error'>Error en request, ")
                        .append(" la fecha  '").append(requestedOn).append("' no es un valor válido para el documento")
                        .append(" con clave '").append(documentCode).append("' y version '").append(documentVersion).append("'</p>");
                return;
            }
            Date now = Utilities.getNow();
            if (createdOn.after(now)) {
                createdOn = now;
            }
            updateRequestDate(documentCode, documentVersion, createdOn);
            updateVerificationDate(documentCode, documentVersion, createdOn);
            updateAuthorizationDate(documentCode, documentVersion, createdOn);
            updateDocumentDate(documentCode, documentVersion, createdOn);
            result.getSb().append("<p class='done'>Fecha de documento con clave '")
                    .append(documentCode).append("' y version '").append(documentVersion)
                    .append("' actualizada a '").append(createdOn).append("'. </p>");
        } catch (Exception e) {
            final String error = "<p class='error'>Failed to update document publish date."
                    + " code: '" + documentCode + "'"
                    + " version: '" + documentVersion + "'"
                    + " date: '" + requestedOn + "' </p>";
            getLogger().error(error, e);
            result.getSb().append(error);
        }
    }
    
    private Node getNode(final Long nodeId, final Map<Long, Node> nodeCache) {
        if (nodeCache.containsKey(nodeId)) {
            return nodeCache.get(nodeId);
        }
        final Node node = getNodeById(nodeId);
        nodeCache.put(nodeId, node);
        return node;
    }

    private Node getNodeById(Long nodeId) {
        final Node node = (Node) HQLT_findById(Node.class, nodeId);
        return node;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertRequest(RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            final Map<Long, Node> nodeCache = new HashMap<>();
            String une = helper.cellValue(rowData, CsvDocumentColumns.BUSINESS_UNIT);

            BusinessUnitLite bun;
            if (une.isEmpty()) {
                BusinessUnitLite bl = (BusinessUnitLite) HQL_findSimpleObject(""
                        + " SELECT c"
                        + " FROM " + BusinessUnitLite.class.getCanonicalName() + " c "
                        + " WHERE c.code = 'Corporativo'", Utilities.EMPTY_MAP);
                if (bl != null) {
                    bl = (BusinessUnitLite) HQL_findSimpleObject(""
                            + " SELECT c"
                            + " FROM " + BusinessUnitLite.class.getCanonicalName() + " c "
                            + " order by c.id desc", Utilities.EMPTY_MAP);
                }
                if (bl == null) {
                    result.getSb().append("")
                            .append("<p class='error'>")
                            .append("Error en request al obtener 'BusinessUnit' de donde pertenecen los documentos, {'")
                            .append(row).append("'} ")
                            .append("</p>");
                    return;
                }
                bun = bl;
            } else {
                Map<String, Long> m = insertBusinessUnitStatus(une, une, null, loggedUser);
                bun = (BusinessUnitLite) HQLT_findById(BusinessUnitLite.class, m.get("id"));
            }
            final String[] array;
            OrganizationalUnitSimple org = (OrganizationalUnitSimple) HQLT_findById(OrganizationalUnitSimple.class, bun.getOrganizationalUnitId());
            String path = helper.cellValue(rowData, CsvDocumentColumns.PATH);
            final boolean hasFullPath;
            if (path.contains("/")) {
                hasFullPath = path.startsWith("/");
                if (hasFullPath) {
                    path = org.getDescription() + path;
                }
                array = path.split("/", -1);
            } else if (path.contains("\\")) {
                hasFullPath = path.startsWith("\\");
                if (hasFullPath) {
                    path = org.getDescription() + path;
                }
                array = path.split("\\\\", -1);
            } else {
                array = new String[]{path};
                hasFullPath = false;
            }
            List<String> both;
            if (hasFullPath) {
                both = new ArrayList<>(array.length);
            } else {
                String[] base = new String[]{org.getDescription(), bun.getDescription()};
                both = new ArrayList<>(base.length + array.length);
                Collections.addAll(both, base);
            }
            Collections.addAll(both, array);
            String verifier = helper.cellValue(rowData, CsvDocumentColumns.VERIFIER);
            Long verifierId = cache.getUserCache().getUserIdByAccount(verifier);
            if (verifierId == null || verifierId <= 0) {
                result.getSb().append("<p class='error'>Error en request, el ORIGINADOR '")
                        .append(verifier).append("' no es un usuario válido.<br/>{'").append(row).append("'} </p>");
                return;
            }
            Workflow flujo = getNewWorkflowFrom(rowData, bun, loggedUser);

            if (flujo == null) {
                result.getSb().append("<p class='error'>Error en request al grabar el flujo, {'").append(row).append("'} </p>");
                return;
            }
            Request req = new Request();

            UserRef usrIN = new UserRef(verifierId);
            req.setAuthor(usrIN);
            req.setBusinessUnit(bun);
            /*
             @ToDo: FAIL!! La aplicacion no contempla documentos que pertenecen a un departamento
             */
            String departamento = helper.cellValue(rowData, CsvDocumentColumns.DEPARTMENT);
            List<String> bus = Arrays.asList(bun.getCode(), departamento);
            req.setDepartment(new BusinessUnitDepartmentRef(
                    insertBusinessUnitDepartmentStatus(new RowDTO(bus), loggedUser).get("id")
            ));
            final Catalog storage = insertStorage(rowData, loggedUser);
            if (storage != null) {
                req.setStoragePlaceId(storage.getId().getTipoId().intValue());
                final String retentionText = helper.cellValue(rowData, CsvDocumentColumns.STORAGE_TIME);
                if (retentionText == null || retentionText.trim().isEmpty()) {
                    result.getSb().append("<p class='error'>Campo tiempo de almacenamiento requerido, {'")
                            .append(row).append("'} </p>");
                    return;
                }
                req.setRetentionText(Integer.valueOf(retentionText.trim()));
                req.setRetentionTime(1);
            }
            req.setScope(Request.SCOPE.DEPARTMENT.getValue());
            /**/
            req.setCreationDate(new Date());
            req.setDeleted(Request.IS_NOT_DELETED);
            String code = helper.cellValue(rowData, CsvDocumentColumns.CODE);
            Long docId = HQL_findSimpleLong(""
                    + " SELECT c.id "
                    + " FROM " + Document.class.getCanonicalName() + " c"
                    + " LEFT JOIN c.businessUnit bu"
                    + " WHERE c.deleted = 0"
                    + " AND c.status = " + Document.STATUS.ACTIVE.getValue()
                    + " AND bu.id = :businessUnitId"
                    + " AND c.code = :code",
                    ImmutableMap.of(
                            "businessUnitId", bun.getId(),
                            "code", code.replaceAll("'", "''")
                    )
            );
            if (docId > 0) {
                req.setDocument((Document) HQLT_findById(Document.class, docId));
                req.setType(Request.MODIFY);
            } else {
                req.setType(Request.NEW);
            }
            String description = helper.cellValue(rowData, CsvDocumentColumns.DESCRIPTION);
            if (Request.NEW.equals(req.getType())) {
                List<Long> reqList = HQL_findByQuery(""
                        + " SELECT c.id "
                        + " FROM " + Request.class.getCanonicalName() + " c"
                        + " WHERE c.deleted = 0"
                        + " AND c.description = :description",
                        ImmutableMap.of("description", description.replaceAll("'", "''"))
                );
                req.setDescription(description);
                if (!reqList.isEmpty()) {
                    result.getSb().append("<p class='error one_tab'>Cambiando Nombre '")
                            .append(description).append("', por  '").append(description)
                            .append("_").append(bun.getCode()).append("'</p>");
                    req.setDescription(description + "_" + bun.getCode());
                }
                reqList = HQL_findByQuery(""
                        + " SELECT c.id"
                        + " FROM " + Request.class.getCanonicalName() + " c"
                        + " WHERE c.deleted = 0"
                        + " AND c.documentCode = :documentCode",
                        ImmutableMap.of("documentCode", code.replaceAll("'", "''"))
                );
                if (!reqList.isEmpty()) {
                    result.getSb().append("<p class='error one_tab'>Cambiando Clave '")
                            .append(code).append("', por  '").append(code)
                            .append("_").append(bun.getCode()).append("'</p>");
                    req.setDocumentCode(code + "_" + bun.getCode());
                } else {
                    req.setDocumentCode(code);
                }
            } else {
                req.setDescription(description);
                req.setDocumentCode(code);
            }
            String type = helper.cellValue(rowData, CsvDocumentColumns.TYPE);
            DocumentType dT = insertDocumentType(type, loggedUser);
            if (dT == null) {
                result.getSb().append("<p class='error'>Error al grabar el tipo de documento '").append(type).append("' </p>");
                return;
            }
            req.setDocumentType(dT);
            FilesLite file;

            String filename = helper.cellValue(rowData, CsvDocumentColumns.FILE_NAME);
            file = (FilesLite) HQL_findSimpleObject(""
                    + " SELECT c"
                    + " FROM " + FilesLite.class.getCanonicalName() + " c"
                    + " WHERE c.description = :fname", "fname",
                    filename
            );
            if (file == null) {
                file = (FilesLite) HQLT_findById(FilesLite.class, 1L);
            }
            if (file == null) {
                result.getSb().append("<p class='error'>Error al grabar el archivo '").append(description).append("' </p>");
                return;
            }
            req.setFileId(1L);
            req.setFlujoId(flujo.getId());
            req.setId(-1L);
            req.setEnablePdfViewer(1);
            req.setNodo(insertNode(new RowDTO(both), loggedUser));
            req.setReazon("Carga inicial de documentos al sistema Bnext QMS");
            req.setStatus(Request.ACTIVE_STATUS);
            String doc_version = helper.cellValue(rowData, CsvDocumentColumns.VERSION);
            String version = doc_version;
            if (version == null) {
                result.getSb().append("<p class='error'>Error de versión '").append(doc_version).append("' </p>");
                return;
            }
            req.setVersion(version);
            IRequestDAO dao = getBean(IRequestDAO.class);
            req.setDocumentMasterId(dao.generateDocumentMasterId());
            GenericSaveHandle sH = dao.save(req, file, req.getType(), loggedUser, false, false);
            if (sH.getOperationEstatus() == 0) {
                result.getSb().append(sH.getErrorMessage()).append("</br>").append(sH.getSuccessMessage()
                        + "<p class='error'>Error al grabar la solicitud '").append(description).append("' en la planta ")
                        .append(bun.getDescription()).append(", {'" + row).append("'} </p>");
            } else {
                final Long authorId = req.getAuthor().getId();
                final Node node = getNode(req.getNodo().getId(), nodeCache);
                if (DocumentType.documentControlledType.UNCONTROLLED.equals(req.getDocumentType())) {
                    result.getSb()
                            .append("<p class='added '>Solicitud del documento no controlado '").append(description)
                            .append("' fue guardada correctamente en la planta ").append(bun.getDescription()).append(". </p>");
                    assignPermission(authorId, node, result, nodeCache, loggedUser);
                } else {
                    result.getSb().append("<p class='added '>Solicitud '")
                            .append(description).append("' guardada correctamente en la planta ").append(bun.getDescription())
                            .append(". </p>");
                    verifyRequest(req, rowData, cache, result, dao, nodeCache, loggedUser);
                    assignPermission(authorId, node, result, nodeCache, loggedUser);
                }
            }
        } catch (ConstraintViolationException e) {
            getLogger().error("Fail request ... {''}", row, e);
            result.getSb().append("<p class='error'>Fail request:").append(e.getMessage()).append(" <br/>{'")
                    .append(row).append("'}</p>");
        } catch (Exception e) {
            getLogger().error("Fail request ... {''}", row, e);
            result.getSb().append("<p class='error'>Fail request:").append(e.getMessage()).append(" <br/>{'")
                    .append(row).append("'}</p>");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer resetDocumentSequence(LoggedUser loggedUser) {
        Integer count = SQL_updateByQuery(""
                + " UPDATE HIBERNATE_SEQUENCES "
                + " SET SEQUENCE_NEXT_HI_VALUE = (SELECT (((MAX(id)- 100) / 100) + 101) FROM document ) "
                + " WHERE SEQUENCE_NAME = :sequenceName ",
                ImmutableMap.of("sequenceName", "document"), 0, 
                Arrays.asList("HIBERNATE_SEQUENCES")
        );
        SQL_updateByQuery(""
                + " UPDATE HIBERNATE_SEQUENCES "
                + " SET SEQUENCE_NEXT_HI_VALUE = 1 "
                + " WHERE SEQUENCE_NEXT_HI_VALUE IS NULL ", 
                Utilities.EMPTY_MAP, 0, Arrays.asList("HIBERNATE_SEQUENCES")
        );
        return count;
    }

    private void assignPermission(
            final Long userId,
            final Node node,
            final BackendUploadResult result, 
            final Map<Long, Node> nodeCache, 
            final LoggedUser loggedUser
    ) throws IOException {
        final List<Long> users = HQL_findByQuery(" "
                + " SELECT c.id.userId"
                + " FROM " + NodeUser.class.getCanonicalName() + " c"
                + " WHERE c.id.nodeId = :nodeId",
                ImmutableMap.of("nodeId", node.getId())
        );
        final Set<Long> usrs = users != null ? new HashSet<>(users) : new HashSet<>();
        if (!usrs.contains(userId)) {
            final NodeUser nu = new NodeUser(node.getId(), userId);
            nu.setInsert(true);
            usrs.add(userId);
            makePersistent(nu, loggedUser.getId());
            if (node.getParent() != 0) {
                assignPermission(userId, getNode(node.getParent(), nodeCache), result, nodeCache, loggedUser);
                return;
            }
            result.getSb().append("<p class='added one_tab'>Usuario ")
                    .append(userId).append(" asignado a la carpeta ").append(node.getPath())
                    .append("</p>");
        } else {
            result.getSb().append("<p class='done one_tab'>Usuario ")
                    .append(userId).append(" asignado a la carpeta ").append(node.getPath()).append("</p>");
        }
    }

    private void verifyRequest(
            final Request req, 
            final RowDTO rowData, 
            final LoadCacheDTO cache,
            final BackendUploadResult result, 
            final IRequestDAO dao, 
            final Map<Long, Node> nodeCache,
            final LoggedUser loggedUser
    ) throws IOException, QMSException, ExecutionException {
        final List<String> row = rowData.getValue();
        String verifierAcc = helper.cellValue(rowData, CsvDocumentColumns.VERIFIER);
        Long userId = cache.getUserCache().getUserIdByAccount(verifierAcc.toUpperCase().replaceAll("'", "''"));
        if (userId == null || userId <= 0) {
            userId = cache.getUserCache().getUserIdByAccount(verifierAcc
                            .toUpperCase()
                            .replaceAll("'", "''")
                            .replaceAll("ñ", "n")
                            .replaceAll("á", "a")
                            .replaceAll("é", "e")
                            .replaceAll("í", "i")
                            .replaceAll("ó", "o")
                            .replaceAll("ú", "u")
                            + "'");
            if (userId == null || userId <= 0) {
                result.getSb().append("<p class='error one_tab '>Error al verificar por '").append(verifierAcc).append("'</p>");
                return;
            }
        }

        CRUD_Request reqCRUD = new CRUD_Request("*");
        final ILoggedUser userAdmin = UserUtils.getLoggedUserById(userId);

        String token = req.getDocumentCode() + req.getVersion();
        GenericSaveHandle sH = reqCRUD.verifyRequest(req, true, WorkflowAuthRole.ADMIN, token, userAdmin, dao);

        if (sH.getOperationEstatus() != 0) {

            Node n = getNode(req.getNodo().getId(), nodeCache);
            assignPermission(userId, n, result, nodeCache, loggedUser);
            if (n != null) {
                if (Objects.equals(userId, loggedUser.getId())) {
                    result.getSb().append("<p class='added one_tab'>Solicitud verificada correctamente por ADMINISTRADOR | ID: 1</p>");
                    approveRequest(req, rowData, cache, result, dao, nodeCache, loggedUser);
                } else {

                    List<String> autorizators = new ArrayList<>();
                    for (int i = CsvDocumentColumns.AUTHORIZER_FOR_LIMIT.getIndex(); i < row.size(); i++) {
                        if (!row.get(i).isEmpty()) {
                            autorizators.add(row.get(i).trim());
                        }
                    }
                    if (autorizators.size() == 1 && autorizators.contains(verifierAcc)) {
                        result.getSb().append("<p class='added one_tab '>Solicitud verificada y aprobada correctamente por ")
                                .append(verifierAcc).append(" | ").append(userId).append("</p>");
                    } else {
                        result.getSb().append("<p class='added one_tab '>Solicitud verificada correctamente por ")
                                .append(verifierAcc).append(" | ").append(userId).append("</p>");
                        approveRequest(req, rowData, cache, result, dao, nodeCache, loggedUser);
                    }
                }
            } else {
                result.getSb().append("<p class='error one_tab '>Error al asignar usuario userId: '").append(userId).append("' a la carpeta</p>");
            }
        } else {
            result.getSb().append("<p class='error one_tab '>Error al verificar por verifierAcc: '").append(verifierAcc)
                    .append("' | userId: '").append(userId).append("'</p>");
        }
    }

    private void approveRequest(
            final Request req, 
            final RowDTO rowData,
            final LoadCacheDTO cache,
            final BackendUploadResult result,
            final IRequestDAO dao,
            final Map<Long, Node> nodeCache,
            final LoggedUser loggedUser
    ) throws IOException, QMSException, ExecutionException {
        final List<String> row = rowData.getValue();
        if (DocumentType.documentControlledType.UNCONTROLLED.toString().equals(req.getDocumentType().getDocumentControlledType())) {
            result.getSb().append("[no se requiere workflow, documento publicado]");
            return;
        }
        List<String> autorizators = new ArrayList<>();
        for (int i = CsvDocumentColumns.AUTHORIZER_FOR_LIMIT.getIndex(); i < row.size(); i++) {
            if (!row.get(i).isEmpty()) {
                autorizators.add(row.get(i).trim());
            }
        }
        Boolean published = false, authorized = false;
        for (String autorizator : autorizators) {
            Long nextUserId = cache.getUserCache().getUserIdByAccount(autorizator.toUpperCase().replaceAll("'", "''"));
            if (nextUserId <= 0) {
                nextUserId = loggedUser.getId();
            }
            if (!published) {
                final IWorkflowDAO workflowDao = dao.getBean(IWorkflowDAO.class);
                final GenericSaveHandle gsh = workflowDao.workflowRequestApproveForceCreateNextPool(
                        dao,
                        req,
                        WorkflowAuthRole.ASSIGNED,
                        "Autorizado automaticamente en carga inicial",
                        nextUserId,
                        loggedUser
                );
                final Map jsonData = gsh.getJsonEntityData();
                if (jsonData == null) {
                    authorized = false;
                    published = false;
                } else {
                    if (jsonData.containsKey("autorized")) {
                        authorized = (Boolean) jsonData.get("autorized");
                    } else {
                        authorized = (Boolean) jsonData.get("authorized");
                    }
                    published = jsonData.get("published") != null
                            && jsonData.get("published").equals(true);
                }
            }
            if (authorized != null && authorized) {
                Node n = getNode(req.getNodo().getId(), nodeCache);
                assignPermission(nextUserId, n, result, nodeCache, loggedUser);
                if (n != null) {
                    if (Objects.equals(nextUserId, loggedUser.getId())) {
                        result.getSb().append("<p class='added one_tab'>Solicitud aprobada correctamente por ADMINISTRADOR | ID: 1</p>");
                    } else {
                        result.getSb().append("<p class='added one_tab '>Solicitud aprobada correctamente por ")
                                .append(autorizator).append(" | ").append(nextUserId).append("</p>");
                    }
                } else {
                    result.getSb().append("<p class='added one_tab '>Error al asignar usuario a la carpeta</p>");
                    return;
                }
            } else {
                result.getSb().append("<p class='error one_tab '>Error al aprobar  por ")
                        .append(autorizator).append(" | ").append(nextUserId).append("</p>");
            }
        }
    }

    private DocumentType insertDocumentType(String docTypeDescription, LoggedUser loggedUser) {
        DocumentType dot;
        List<DocumentType> dotList = HQL_findByQueryLimit(""
                + " SELECT c"
                + " FROM " + DocumentType.class.getCanonicalName() + " c"
                + " WHERE c.description = :description",
                ImmutableMap.of("description", docTypeDescription.replaceAll("'", "''")),
                1
        );

        if (!dotList.isEmpty()) {
            dot = dotList.get(0);
        } else {
            dot = new DocumentType(-1L, DocumentType.documentControlledType.CONTROLLED);
            dot.setCode(docTypeDescription);
            dot.setDescription(docTypeDescription);
            dot.setDeleted(0);
            dot.setIsRetainable(0);
            dot.setRegistryCode("{'0':'${sequence}'}");
            dot.setQuickPrintEnabled(1);
            dot.setDocumentExpiration(12);
            dot.setCollectingAndStoreResponsible(0);
            dot.setReadOnlyEnabled(0);
            dot.setMustAssignReaders(1);
            dot.setMustRead(1);
            dot.setEnablePdfViewer(1);
            dot.setPdfViewerFitDocumentScreen(1);
            dot.setCanPrintControlledCopies(0);
            dot.setAddActivitiesEnabled(0);
            dot.setAddFindingsEnabled(0);
            dot.setRegistryCodeSequenceLength(4);
            dot.setDictionaryIndexId(1L);
            dot.setStatus(DocumentType.STATUS.ACTIVE.getValue());
            makePersistent(dot, loggedUser.getId());
        }
        return dot;
    }

    private Workflow getNewWorkflowFrom(RowDTO rowData, BusinessUnitLite businessUnit, LoggedUser loggedUser) {
        final List<String> row = rowData.getValue();
        List<String> autorizator = new ArrayList<>();
        for (int i = CsvDocumentColumns.AUTHORIZER_FOR_LIMIT.getIndex(); i < row.size(); i++) {
            if (!helper.nullAsEmpty(row.get(i)).isEmpty()) {
                autorizator.add(helper.nullAsEmpty(row.get(i)));
            }
        }

        IFlowDAO flowDAO = getBean(IFlowDAO.class);
        String flujoCode = Integer.toString(autorizator.hashCode()),
                flujoName = autorizator.toString();
        List<Workflow> flujos = HQLT_findByQueryFilter(Workflow.class, "c.code = '" + flujoCode + "'");
        if (!flujos.isEmpty()) {
            return flujos.get(0);
        } else {
            List<Long> autorizators = new ArrayList<>();
            for (String user : autorizator) {
                final String userCode = user.toUpperCase().replaceAll("'", "''");
                if (userCode == null || "N/A".equals(userCode)) {
                    continue;
                }
                List<BigInteger> usrList = SQL_findByQuery("select user_id from users where upper(account) = '" + userCode + "'");
                if (usrList.isEmpty()) {
                    throw new NoSuchElementException("Autorizador '" + user.toUpperCase() + "' no econtrado.");
                }
                autorizators.add(usrList.get(0).longValue());
            }
            return flowDAO.getNewWorkflowFrom(
                    autorizators,
                    flujoName,
                    flujoCode,
                    null,
                    businessUnit.getOrganizationalUnitId(),
                    Module.DOCUMENT,
                    loggedUser
            );
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void saveProfile(
            final RowDTO rowData, 
            final LoadCacheDTO cache, 
            final BackendUploadResult result, 
            final LoggedUser loggedUser
    ) throws IOException {
        final List<String> row = rowData.getValue();
        try {
            final String profileCode = helper.profileIdCSV_User(rowData);
            if (profileCode == null || profileCode.isEmpty()) {
                handleError(result, rowData, CSVUserColumns.PROFILE, CSVUserColumns.PROFILE, BulkLoadError.EMPTY_FIELD, loggedUser);
                result.getSb().append("<p  class='error prf'>")
                        .append("Nombre de Perfil vacio")
                        .append(row)
                        .append("</p>");
                return;
            }
            final String profileStatus = helper.profileStatusCSV_User(rowData);
            if (!Utilities.isInteger(profileStatus)) {
                handleError(result, rowData, CSVUserColumns.PROFILE, CSVUserColumns.PROFILE_STATUS, BulkLoadError.INCORRECT_TYPE, loggedUser);
                result.getSb().append("<p  class='error prf'>")
                        .append("Estado de perfil vacio ")
                        .append(row)
                        .append("</p>");
                return;
            }
            if ((Utilities.ParseInteger(profileStatus) != Profile.STATUS.ACTIVE.getValue()
                    && Utilities.ParseInteger(profileStatus) != Profile.STATUS.INACTIVE.getValue())) {
                handleError(result, rowData, CSVUserColumns.PROFILE, CSVUserColumns.PROFILE_STATUS, BulkLoadError.INCORRECT_VALUE, loggedUser);
                result.getSb().append("<p  class='error prf'>")
                        .append("Estado de perfil invalido ").append(profileStatus)
                        .append(row)
                        .append("</p>");
                return;
            }
            final Long profileId = cache.getProfileCache().get(profileCode);
            if (profileId == null || profileId <= 0) {//No existe, hay que agregarlo
                Profile prf = ProfileServicesUtil.getInstance();
                prf.setCode(profileCode);
                prf.setDeleted(Profile.IS_NOT_DELETED);
                prf.setDescription(profileCode.replace("_", " "));
                prf.setIntBUsuarioPlanta(Integer.valueOf("1"));
                prf.setId(-1L);
                prf.setStatus(Utilities.ParseInteger(profileStatus));
                /*Sistema*/

                prf.setIntBAdmonSistema(0);
                prf.setBulkAccessCreator(0);
                prf.setIntBAdmonAccesos(0);

                prf.setIntBLectorReunion(0);
                prf.setIntBEditorReunion(0);
                prf.setIntBEncargadoReunion(0);

                prf.setIntBLectorDocumento(0);
                prf.setShareDocument(0);
                prf.setIntBEditorDocumento(0);
                prf.setIntBEncargadoDocumento(0);
                prf.setIntBPrintUncontrolledCopies(0);

                prf.setIntBDeviceCreator(0);
                prf.setIntBDeviceDisposeViewer(0);
                prf.setIntBDeviceManager(0);
                prf.setIntBDeviceRealizeService(0);
                prf.setIntBDeviceScheduledServicesViewer(0);
                prf.setIntBDeviceServiceHistoryViewer(0);
                prf.setIntBDeviceServiceMetricViewer(0);
                prf.setIntBDeviceServiceScheduler(0);
                prf.setIntBDeviceViewer(0);

                prf.setIntBReporteAuditoria(0);
                prf.setIntBAuditAuditorHelper(0);
                prf.setIntBAuditAuditorLider(0);
                prf.setIntBAuditSurvey(0);
                prf.setIntBAuditQuality(0);

                prf.setIntBLectorAccion(0);
                prf.setIntBEditorAccion(0);
                prf.setIntBEncargadoAccion(0);
                prf.setIntBAccionPorOrganizacion(0);

                prf.setIntBEncargadoProyecto(0);
                prf.setIntBEditorProyecto(0);
                prf.setIntBLectorProyecto(0);
                prf.setIntBContabilidadProyecto(0);

                prf.setIntBLectorIndicador(0);
                prf.setIntBEditorIndicador(0);
                prf.setIntBEncargadoIndicador(0);

                prf.setIntBLectorQueja(0);
                prf.setIntBEditorQueja(0);
                prf.setIntBEncargadoQueja(0);

                prf.setIntBReporteAccion(0);
                prf.setIntBReporteAuditoria(0);
                prf.setIntBReporteConfiguracion(0);
                prf.setIntBReporteCuestionario(0);
                prf.setIntBReporteDocumento(0);
                prf.setIntBReporteEncuesta(0);
                prf.setIntBReporteGerencial(0);
                prf.setIntBReporteIndicador(0);
                prf.setIntBReporteProyecto(0);
                prf.setIntBReporteQueja(0);
                prf.setIntBReporteReunion(0);

                prf.setIntBSurveyManager(0);
                prf.setIntBSurveyRespondent(0);
                prf.setIntBSurveySupervisor(0);

                prf.setIntBUsuarioPlanta(1);
                prf.setIntBUsuarioCorporativo(0);

                /*Formularios*/
                prf.setIntBCrearFormulario(0);
                prf.setIntBControlFormulario(0);
                prf.setIntBFillOutHistory(0);

                /*Five S*/
                prf.setIntBCreateFiveS(0);
                prf.setIntBLinkFiveS(0);
                prf.setIntBPrintFiveS(0);
                prf.setIntBFiveSManager(0);
                prf.setIntBFiveSMap(0);
                prf.setIntBFiveSSection(0);
                prf.setIntBFiveSItem(0);

                prf.setCatalogsMeetings(0);
                prf.setCatalogsDocuments(0);
                prf.setCatalogsFormularies(0);
                prf.setCatalogsAudits(0);
                prf.setCatalogsActions(0);
                prf.setCatalogsSurveys(0);
                prf.setCatalogsComplaints(0);
                prf.setCatalogsMeters(0);
                prf.setCatalogsProjects(0);
                prf.setCatalogsDevices(0);
                prf.setCatalogsFiveS(0);
                prf.setCatalogsGenerals(0);

                prf.setInitializedAtDocumentViewer(0);
                prf.setUnrestrictedDocumentAccess(0);

                prf = (Profile) makePersistent(prf, loggedUser.getId());
                final String businessUnit = helper.cellValue(rowData, CSVUserColumns.BUSINESS_UNIT.getIndex());
                if (prf == null) {
                    handleError(result, rowData, CSVUserColumns.PROFILE, CSVUserColumns.PROFILE, BulkLoadError.PERSISTENT, loggedUser);
                    result.getSb().append("<p class='error prf'>Error al grabar el Perfil '").append(businessUnit).append("_").append(profileCode).append("' </p>");
                } else {
                    handleCreated(result, rowData, CSVUserColumns.PROFILE, loggedUser);
                    result.getSb().append("<p class='added prf'>")
                        .append("- Registro "+((profileStatus != null && Utilities.ParseInteger(profileStatus) == User.STATUS.ACTIVE.getValue())  ? "activo" : "inactivo")+" - ")
                        .append("Perfil '").append(profileCode).append("' guardado correctamente. </p>");
                }
            } else {
                final String businessUnit = helper.cellValue(rowData, CSVUserColumns.BUSINESS_UNIT.getIndex());
                handleModified(result, rowData, CSVUserColumns.PROFILE, loggedUser);
                updateBulkDataProfileExist(helper, cache, profileCode, rowData);
                result.getSb().append("<p  class='done prf'>")
                        .append("- Registro "+((profileStatus != null && Utilities.ParseInteger(profileStatus) == User.STATUS.ACTIVE.getValue())  ? "activo" : "inactivo")+" - ")
                        .append("El Perfil: '")
                        .append(profileCode).append("' ya se ha agregado en la planta ")
                        .append(businessUnit).append(" </p>");
            }
        } catch (final Exception e) {
            handleError(result, rowData, CSVUserColumns.PROFILE, CSVUserColumns.PROFILE, BulkLoadError.INCORRECT_VALUE, loggedUser);
            result.getSb().append("<p class='error prf'>Error al grabar el Perfil '").append(row).append("</p>");
        }
    }

    private void updateBulkDataProfileExist(
            final BackendUploaderHelper helper,
            final LoadCacheDTO cache,
            final String profileCode,
            final RowDTO rowData
    ) throws ExecutionException {
        final Long profileId = cache.getProfileCache().get(profileCode);
        if (profileCode != null && !profileCode.isEmpty()) {
            HQL_updateByQuery("" +
                " UPDATE " + Profile.class.getCanonicalName() + " p " +
                " SET p.description = :name" +
                " WHERE p.id = :id",
                ImmutableMap.of("name", profileCode.replace("_", " "), "id", profileId)
            );
        }
        final Integer profileStatus = Integer.valueOf(helper.profileStatusCSV_User(rowData));
        HQL_updateByQuery("" +
            " UPDATE " + Profile.class.getCanonicalName() + " p " +
            " SET p.status = :status " +
            " WHERE p.id = :id",
            ImmutableMap.of("status", profileStatus, "id", profileId)
        );
    }


    private void insertDepartmentProcess(
            String departmentName,
            String businessUnitCode,
            String processName,
            final BackendUploadResult result,
            LoggedUser loggedUser
    ) throws IOException {
        CRUD_DepartmentProcess dprCRUD = new CRUD_DepartmentProcess();
        dprCRUD.setActiveServices("*");
        Long budId = HQL_findSimpleLong(""
                + " SELECT c.id "
                + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " c"
                + " WHERE c.department.description = :departmentDescription "
                + " AND c.businessUnit.code = :businessUnitCode",
                ImmutableMap.of(
                        "departmentDescription", departmentName,
                        "businessUnitCode", businessUnitCode
                )
        );
        Integer dprListCount = HQL_findSimpleInteger(""
                + " SELECT COUNT(c.id)"
                + " FROM " + DepartmentProcessView.class.getCanonicalName() + " c"
                + " WHERE c.businessUnitDepartment.id = :budId "
                + " AND c.process.description = :processName ",
                ImmutableMap.of(
                        "budId", budId,
                        "processName", processName
                )
        );
        if (dprListCount == null || Objects.equals(dprListCount, 0)) {//No existe, hay que agregarlo
            DepartmentProcess dpr = new DepartmentProcess();
            dpr.setAttendantId(loggedUser.getId());
            budId = HQL_findSimpleLong(""
                    + " SELECT c.id "
                    + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " c"
                    + " WHERE c.department.description = :departmentDescription "
                    + " AND c.businessUnit.code = :businessUnitCode",
                    ImmutableMap.of(
                            "departmentDescription", departmentName,
                            "businessUnitCode", businessUnitCode
                    )
            );
            dpr.setDepartmentId(budId);
            dpr.setId(-1L);
            List<Process> pross = HQL_findByQuery(""
                    + " SELECT c "
                    + " FROM " + Process.class.getCanonicalName() + " c"
                    + " WHERE c.description = :processName",
                    "processName", processName
            );
            dpr.setProcessId(pross.get(0).getId());
            dpr.setStatus(DepartmentProcess.ACTIVE_STATUS);
            GenericSaveHandle dprSH = dprCRUD.save(dpr);
            if (dprSH.getOperationEstatus() == 0) {
                result.getSb().append("<p  class='error dpr'>Error al grabar la relación DEPT/PRO'")
                        .append(departmentName).append("/").append(processName).append("' </p>");
            } else {
                result.getSb().append("<p  class='added dpr'>La relación DEPT/PRO ")
                        .append(departmentName).append("/").append(processName).append(" guardado correctamente. </p>");
            }
        } else {
            result.getSb().append("<p  class='done dpr'>La relación DEPT/PRO: ")
                    .append(departmentName).append("/").append(processName).append("' ya se ha agregado. </p>");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertProcess(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        String processCode = helper.cellValue(rowData, CSVOrganizationColumns.PROCESS_CODE.getIndex());
        String processName = helper.cellValue(rowData, CSVOrganizationColumns.PROCESS_NAME.getIndex());
        if (processCode.isEmpty() || processName.isEmpty()) {
            result.getSb().append("<p class='error pro'>Error Proceso invalido {'").append(row).append("'} </p>");
            return;
        }
        /*Busca el departamento*/
        CRUD_Process proCRUD = new CRUD_Process();
        proCRUD.setActiveServices("*");
        List<Process> proList = HQL_findByQuery(""
                + " SELECT c"
                + " FROM " + Process.class.getCanonicalName() + " c"
                + " WHERE c.description = :processName", "processName", processName);
        String departmentName = helper.cellValue(rowData, CSVOrganizationColumns.DEPARTMENT_NAME.getIndex());
        String businessUnitCode = helper.cellValue(rowData, CSVOrganizationColumns.BUSINESS_UNIT_CODE.getIndex());
        if (proList.isEmpty()) {//No existe, hay que agregarlo
            Process pro = new Process();
            pro.setId(-1L);
            pro.setCode(processCode);
            pro.setDeleted(0);
            pro.setDescription(processName);
            pro.setStatus(Process.ACTIVE_STATUS);
            pro.setAttendantId(loggedUser.getId());
            GenericSaveHandle proSH = proCRUD.save(pro);
            if (proSH.getOperationEstatus() == 0) {
                result.getSb().append("<p class='error pro'>Error al grabar el Proceso Maestro '").append(processCode).append("' </p>");
            } else {
                result.getSb().append("<p class='added pro'>Proceso Maestro '").append(processCode).append("' guardado correctamente. </p>");
                insertDepartmentProcess(departmentName, businessUnitCode, processName, result, loggedUser);
            }
        } else {
            result.getSb().append("<p class='done pro'>El Proceso Maestro'").append(processCode).append("' ya se ha agregado. </p>");
            insertDepartmentProcess(departmentName, businessUnitCode, processName, result, loggedUser);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertArea(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        String businessUnitCode = helper.cellValue(rowData, CSVOrganizationColumns.BUSINESS_UNIT_CODE.getIndex());
        String departmentName = helper.cellValue(rowData, CSVOrganizationColumns.DEPARTMENT_NAME.getIndex());
        String areaCode = helper.cellValue(rowData, CSVOrganizationColumns.AREA_CODE.getIndex());
        String areaName = helper.cellValue(rowData, CSVOrganizationColumns.AREA_NAME.getIndex());
        Integer areaStatus = helper.cellValue(rowData, CSVOrganizationColumns.AREA_STATUS.getIndex(), Area.STATUS.ACTIVE.getValue());
        if (businessUnitCode.isEmpty() || departmentName.isEmpty()) {
            result.getSb().append("<p class='error pro'>Error Area invalida {'").append(rowData.getRow()).append("'} </p>");
            return;
        }
        Long departmentId = getBusinessUnitDepartmentId(businessUnitCode, departmentName);
        String customField1 = helper.cellValue(rowData, CSVOrganizationColumns.AREA_FIELD_1.getIndex());
        String customField2 = helper.cellValue(rowData, CSVOrganizationColumns.AREA_FIELD_2.getIndex());
        String customField3 = helper.cellValue(rowData, CSVOrganizationColumns.AREA_FIELD_3.getIndex());
        String customField4 = helper.cellValue(rowData, CSVOrganizationColumns.AREA_FIELD_4.getIndex());
        String customField5 = helper.cellValue(rowData, CSVOrganizationColumns.AREA_FIELD_5.getIndex());
        String customField6 = helper.cellValue(rowData, CSVOrganizationColumns.AREA_FIELD_6.getIndex());
        Map areaMap = insertArea(
                areaCode,
                areaName,
                areaStatus,
                businessUnitCode,
                departmentId,
                departmentName,
                customField1,
                customField2,
                customField3,
                customField4,
                customField5,
                customField6,
                loggedUser
        );
        getEntityManager().flush();
        if (areaMap.get("modified") != null) {
            result.getSb().append("<p class='done area'>").append(areaMap.get("description")).append(" </p><br />");
        } else {
            result.getSb().append("<p class='added area'>").append(areaMap.get("description")).append(" </p><br />");
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertDepartment(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        // Check if encoding its UTF-8 for all the values in the row
        StringBuilder utfCheck = new StringBuilder();
        rowData.getValue().forEach( e-> {
            if (!EncodeUtil.validEncoding(e)) {
                utfCheck.append(" - ").append(e);
            }
        });
        if (!utfCheck.toString().isEmpty()) {
            result.getSb().append("<p class='error dept'>La fila contiene carácteres inválidos: '").append(utfCheck).append(". Revise que la codificación sea UTF-8.' </p>");
            return;
        }
        String businessUnitCode = helper.cellValue(rowData, CSVOrganizationColumns.BUSINESS_UNIT_CODE.getIndex());
        String departmentCode = helper.cellValue(rowData, CSVOrganizationColumns.DEPARTMENT_CODE.getIndex());
        if (departmentCode.length() > 20) {
            result.getSb().append("<p class='error dept'>La clave del departamento no puede exceder los 20 caracteres: '").append(departmentCode).append("' </p>");
            return;   
        }
        String departmentName = helper.cellValue(rowData, CSVOrganizationColumns.DEPARTMENT_NAME.getIndex());
        Map resultStatus = insertDepartmentStatus(departmentCode, departmentName);
        String regionCode = helper.cellValue(rowData, CSVOrganizationColumns.REGION_CODE.getIndex());
        String zoneCode = helper.cellValue(rowData, CSVOrganizationColumns.ZONE_CODE.getIndex());
        if (resultStatus.get("status").equals(0L)) {
            result.getSb().append("<p class='error dept'>Error al grabar el Departamento'").append(departmentCode).append("' </p>");
            return;
        } else if (resultStatus.get("status").equals(1L)) {
            result.getSb()
                    .append("<p class='added dept'>Departamento'").append(departmentCode).append("' guardado correctamente.</p>");
            insertBusinessUnitDepartment(businessUnitCode, departmentCode, departmentName, regionCode, zoneCode, result, loggedUser);
            return;
        }
        result.getSb()
                .append("<p class='done dept'>El Departamento'").append(departmentCode).append("' ya se ha agregado. </p>");
        insertBusinessUnitDepartment(businessUnitCode, departmentCode, departmentName, regionCode, zoneCode, result, loggedUser);
    }

    private void insertDepartmentFromDevice(final RowDTO rowData, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        String department = helper.cellValue(rowData, CSVUserColumns.DEPARTMENT.getIndex());
        Map m = insertDepartmentStatus(department, department);
        if (m.get("status").equals(0L)) {
            result.getSb().append("<p class='error dept'>Error al grabar el Departamento Maestro'").append(department).append("' </p>");
            return;
        } else if (m.get("status").equals(1L)) {
            result.getSb()
                    .append("<p class='added dept'>Departamento Maestro'").append(department).append("' guardado correctamente.</p>");
            insertBusinessUnitDepartment(rowData, result, loggedUser);
            return;
        }
        result.getSb()
                .append("<p class='done dept'>El Departamento Maestro'").append(department).append("' ya se ha agregado. </p>");
        insertBusinessUnitDepartment(rowData, result, loggedUser);
    }

    private Map<String, Long> insertDepartmentStatus(String departmentCode, String departmentDescription) {
        Map<String, Long> m = new HashMap<>();
        /*Busca el departamento*/
        CRUD_Department deptCRUD = new CRUD_Department();

        deptCRUD.setActiveServices("*");

        List<Department> deptList = HQL_findByQuery(""
                + " SELECT c"
                + " FROM " + Department.class.getCanonicalName() + " c"
                + " WHERE c.description = :departmentDescription", "departmentDescription", departmentDescription
        );
        Department dept;
        if (deptList.isEmpty()) {//No existe, hay que agregarlo
            dept = new Department();
            dept.setId(-1L);
            dept.setCode(departmentCode);
            dept.setSmallCode(departmentCode);
            dept.setDeleted(0);
            dept.setDescription(departmentDescription);
            dept.setStatus(Department.ACTIVE_STATUS);
            GenericSaveHandle deptSH = deptCRUD.save(dept);
            if (deptSH.getOperationEstatus() == 0) {
                m.put("status", 0L);//"<p  class='error bud'>Error al grabar la relación UNE/DEPT'" + depto + "' </p>";
                return m;
            } else {
                m.put("status", 1L);//"<p  class='error bud'>Error al grabar la relación UNE/DEPT'" + depto + "' </p>";
            }
        } else {
            dept = deptList.get(0);
            m.put("status", 2L);//"<p  class='error bud'>Error al grabar la relación UNE/DEPT'" + depto + "' </p>";
        }
        m.put("id", dept.getId());
        return m;
    }

    private void insertBusinessUnitDepartment(
            RowDTO rowData,
            final BackendUploadResult result,
            LoggedUser loggedUser
    ) throws IOException {
        String businessUnit = helper.cellValue(rowData, CSVUserColumns.BUSINESS_UNIT.getIndex());
        String department = helper.cellValue(rowData, CSVUserColumns.DEPARTMENT.getIndex());
        insertBusinessUnitDepartment(businessUnit, department, department, null, null, result, loggedUser);
    }

    private void insertBusinessUnitDepartment(
            String businessUnitCode,
            String departmentCode,
            String departmentName,
            String regionCode,
            String zoneCode,
            final BackendUploadResult result,
            LoggedUser loggedUser
    ) throws IOException {
        Map resultStatus = insertBusinessUnitDepartmentStatus(
                businessUnitCode,
                departmentCode,
                departmentName,
                regionCode,
                zoneCode,
                loggedUser
        );
        if (resultStatus.get("status").equals(0L)) {
            result.getSb().append("<p  class='error bud'>Error al grabar la relación UNE/DEPT'").append(departmentCode).append("' </p>");
            return;
        } else if (resultStatus.get("status").equals(1L)) {
            result.getSb().append("<p  class='added bud'>La relación UNE/DEPT ")
                    .append(businessUnitCode).append("/").append(departmentCode).append(" guardado correctamente.</p>");
            return;
        }
        result.getSb().append("<p  class='done bud'>La relación UNE/DEPT: ")
                .append(businessUnitCode).append("/").append(departmentCode).append("' ya se ha agregado. </p>");
    }

    /**
     *
     * @param row.get(0) la descripcion de la planta
     * @param row.get(1) la descripcion del departamento
     * @return
     */
    private Map<String, Long> insertBusinessUnitDepartmentStatus(final RowDTO rowData, final LoggedUser loggedUser) {
        String businessUnit = helper.cellValue(rowData, CSVUserColumns.BUSINESS_UNIT.getIndex());
        String department = helper.cellValue(rowData, CSVUserColumns.DEPARTMENT.getIndex());
        return insertBusinessUnitDepartmentStatus(businessUnit, department, department, null, null, loggedUser);
    }

    private Map<String, Long> insertBusinessUnitDepartmentStatus(
            String businessUnitCode,
            String departmentCode,
            String departmentName,
            String regionCode,
            String zoneCode,
            LoggedUser loggedUser
    ) {
        Map<String, Long> m = new HashMap<>();
        Long budId = HQL_findSimpleLong(""
                + " SELECT c.id "
                + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " c"
                + " WHERE c.department.code = :departmentCode "
                + " AND c.businessUnit.code = :businessUnitCode",
                ImmutableMap.of(
                        "businessUnitCode", businessUnitCode,
                        "departmentCode", departmentCode
                )
        );
        if (budId == null || budId == 0L) {//No existe, hay que agregarlo
            BusinessUnitDepartment bud = new BusinessUnitDepartment();
            bud.setAttendantId(loggedUser.getId());

            List<BusinessUnit> bunList = HQL_findByQuery(""
                    + " SELECT c"
                    + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                    + " WHERE c.code = :businessUnitCode", "businessUnitCode", businessUnitCode
            );
            if (bunList == null || bunList.isEmpty()) {
                m.put("id", budId);
                m.put("status", 1L);//"<p  class='error bud'>Error al grabar la relación UNE/DEPT'" + depto + "' </p>";
                return m;
            }
            final BusinessUnit businessUnitEntity = bunList.get(0);
            bud.setBusinessUnitId(businessUnitEntity.getId());
            bud.setComplaints(0);

            bud.setDepartmentId(insertDepartmentStatus(departmentCode, departmentName).get("id"));
            bud.setId(-1L);
            bud.setStatus(1);
            bud.setDocumentManagerId(loggedUser.getId());
            if (regionCode != null && !regionCode.isEmpty()) {
                final Long regionId = HQL_findSimpleLong(""
                        + " SELECT c.id "
                        + " FROM " + Region.class.getCanonicalName() + " c"
                        + " WHERE c.code = :regionCode ",
                        ImmutableMap.of(
                                "regionCode", regionCode
                        )
                );
                if (regionId != null && regionId > 0) {
                    bud.setRegionId(regionId);
                }
            }
            if (zoneCode != null && !zoneCode.isEmpty()) {
                final Long zoneId = HQL_findSimpleLong(""
                        + " SELECT c.id "
                        + " FROM " + Zone.class.getCanonicalName() + " c"
                        + " WHERE c.code = :zoneCode ",
                        ImmutableMap.of(
                                "zoneCode", zoneCode
                        )
                );
                if (zoneId != null && zoneId > 0) {
                    bud.setZoneId(zoneId);
                }
            }
            bud = (BusinessUnitDepartment) makePersistent(bud, loggedUser.getId());
            if (bud == null) {
                m.put("status", 0L);//"<p  class='error bud'>Error al grabar la relación UNE/DEPT'" + depto + "' </p>";
                return m;
            } else {
                budId = bud.getId();
                m.put("status", 1L);//"<p  class='error bud'>Error al grabar la relación UNE/DEPT'" + depto + "' </p>";
            }
        } else {
            m.put("status", 2L);//"<p  class='error bud'>Error al grabar la relación UNE/DEPT'" + depto + "' </p>";
        }
        m.put("id", budId);
        return m;
    }

    private Device getDevice(String code) {
        Map params = new HashMap();
        params.put("code", code);
        String query = "SELECT dev FROM " + Device.class.getCanonicalName() + " dev "
                + "WHERE dev.code = :code";
        List<Device> devices = this.HQL_findByQuery(query, params);
        try {
            return devices.get(0);
        } catch (Exception ex) {
            getLogger().error("There was an error ", ex);
            return null;
        }
    }

    private Map<String, Object> insertServiceType(String description, LoggedUser loggedUser) {
        getLogger().trace("Inserting Service Type {}", description);
        Map<String, Object> result = new HashMap<>();
        if (description.isEmpty()) {
            getLogger().trace("Service Type is empty");
            result.put("description", "Service Type is empty");
            result.put("id", -1L);
            return result;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("description", description);
        List<ServiceType> types = HQL_findByQuery(""
                + " SELECT type"
                + " FROM " + ServiceType.class.getCanonicalName() + " type "
                + " WHERE type.description = :description", params);
        if (types == null || types.isEmpty()) {
            getLogger().trace("Adding types");
            ServiceType type = new ServiceType();
            type.setId(-1L);
            type.setCode(description);
            type.setDescription(description);
            type.setDeleted(0);
            type.setStatus(1);
            makePersistent(type, loggedUser.getId());
            result.put("description", "Adding ServiceType " + description);
            result.put("id", type.getId().toString());
            return result;
        }
        getLogger().trace("ServiceType already exists");

        result.put("description", "ServiceType already exists " + description);
        result.put("id", types.get(0).getId().toString());
        return result;
    }

    private Map insertServiceResult(String description, LoggedUser loggedUser) {
        getLogger().trace("insertServiceResult {}", description);
        Map result = new HashMap();
        Map params = new HashMap();
        params.put("description", description);
        List<ServiceResult> serviceResult = this.HQL_findByQuery("SELECT serviceResult from "
                + ServiceResult.class.getCanonicalName() + " serviceResult "
                + "WHERE serviceResult.description = :description", params);

        if (serviceResult.isEmpty()) {
            getLogger().trace("Adding device serviceResult");
            ServiceResult status = new ServiceResult();
            status.setId(-1L);
            status.setCode(description);
            status.setDescription(description);
            status.setDeleted(0);
            status.setStatus(1);
            makePersistent(status, loggedUser.getId());
            result.put("description", "Adding ServiceResult status " + description);
            result.put("id", status.getId().intValue());
            return result;
        }
        result.put("description", "Description already exists " + description);
        result.put("id", serviceResult.get(0).getId().intValue());
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertSchedule(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        String code = row.get(0);
        String deviceCode2 = row.get(1);
        String deviceCode = row.get(2);
        String serviceType = row.get(3);
        String planned = row.get(4);
        String realized = row.get(5);
        String certificateNumber = row.get(6);
        String certificate = row.get(7);
        String resultService = row.get(8);

        Device dev = getDevice(deviceCode);
        if (dev == null) {
            result.getSb().append("NO SE ENCONTRO EL DEVICE </br>");
            return;
        }
        ServiceSchedule schedule = new ServiceSchedule();
        schedule.setId(-1L);
        try {
            schedule.setDeviceId(dev.getId());
        } catch (Exception ex) {
            result.getSb().append("NO SE ENCONTRO EL DEVICE </br>");
            getLogger().error("There was an error ", ex);
            return;
        }
        schedule.setCode(code);
        schedule.setResponsibleId(dev.getResponsibleId());
        schedule.setAdvanceDays(1);
        schedule.setStatus(ServiceSchedule.ACTIVE_STATUS);
        schedule.setDeleted(0);
        Periodicity p = getBean(IPeriodicityDAO.class).makePersistent(new Periodicity(-1l), loggedUser.getId());
        schedule.setPeriodicity(p);
        schedule.setNextService(new Date(planned));
        Map type = insertServiceType(serviceType, loggedUser);
        if (type == null || type.get("id") == null || type.get("id").equals(-1L)) {
            result.getSb().append("NO SE ENCONTRO EL DEVICE TYPE </br>");
            return;
        }
        result.getSb().append(type.get("description")).append("<br />");
        schedule.setServiceTypeId(Long.valueOf(type.get("id").toString()));
        schedule = (ServiceSchedule) makePersistent(schedule, loggedUser.getId());
        if (!realized.isEmpty()) {
            Service service = new Service();
            service.setId(-1L);
            service.setScheduleId(schedule.getId());
            service.setAttendantId(dev.getResponsibleId());
            service.setCode(code);
            service.setRegisteredOn(new Date());
            if (Utilities.getSettings().getDeviceEditImplementation() == 0) {
                service.setValidFrom(new Date(realized));
            } else {
                service.setValidFrom(new Date());
            }
            service.setValidTo(new Date(realized));
            service.setPlanned(new Date(planned));
            service.setCompany(deviceCode2);
            service.setCertificateNumber(certificateNumber);
            service.setCertificate(certificate);
            Map serviceResul = insertServiceResult(resultService, loggedUser);
            result.getSb().append(serviceResul.get("description")).append("<br />");
            service.setResultId(Long.valueOf(serviceResul.get("id").toString()));
            service.setDeleted(0);
            service.setScheduled(1);
            service.setStatus(Service.DELAYED);
            if (service.getPlanned().compareTo(service.getValidFrom()) <= 2) {
                service.setStatus(Service.PERMITTED);
            }
            if (service.getPlanned() == service.getValidFrom()) {
                service.setStatus(Service.ON_TIME);
            }
            makePersistent(service, loggedUser.getId());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertDevice(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        final List<String> row = rowData.getValue();
        try {

            String status = row.get(0);
            String businessUnit = row.get(1);
            String department = row.get(2);
            String area = row.get(3);
            String code = row.get(4);
            String description = row.get(5);
            String deviceGroup = row.get(6);
            String deviceType = row.get(7);
            //En jafra, este es el código de identificación
            String model = row.get(8);
            //En jafra, este es el código bulk
            String serial = row.get(9);
            //En jafra, este es el código bulk
            String localization = row.get(10);
            String attendant = row.get(11);
            String priority = row.get(12);
            String classification = row.get(13);
            //En jafra, este es el lote máximo en kg.
            String range = row.get(14);
            //En jafra, este es el lote mínimo en kg.
            String precision = row.get(15);
            //En jafra, este es Fecha de elaboración
            SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            Date purchase = null;
            if (row.size() > 16) {
                try {
                    purchase = formatter.parse(row.get(16));
                } catch (ParseException ex) {
                    getLogger().error("Excepcion e", ex);
                }
            }
            Date use = null;
            if (row.size() > 17) {
                try {
                    use = formatter.parse(row.get(17));
                } catch (ParseException ex) {
                    getLogger().error("Excepcion e", ex);
                }
            }
            //En jafra, este es Comentarios
            String treshold = row.size() > 18 ? row.get(18) : "";
            //En Jafra este es Control de cambios
            String uncertainty = row.size() > 19 ? row.get(19) : "";

            Device device = new Device();
            Map deviceStatus = insertDeviceStatus(status, loggedUser);
            result.getSb().append(deviceStatus.get("description")).append("<br />");
            device.setStatus((Integer) deviceStatus.get("id"));
            device.setCode(code);
            device.setDescription(description);
            Map deviceGroupMap = insertDeviceGroup(deviceGroup, loggedUser);
            result.getSb().append(deviceGroupMap.get("description")).append("<br />");
            Long deviceGroupId = Long.valueOf(deviceGroupMap.get("id").toString());
            device.setDeviceGroupId(deviceGroupId);
            Map deviceTypeMap = insertDeviceType(deviceType, deviceGroupId, loggedUser);
            result.getSb().append(deviceTypeMap.get("description")).append("<br />");
            device.setDeviceTypeId(Long.valueOf(deviceTypeMap.get("id").toString()));
            device.setModelBrand(model);
            device.setSerial(serial);
            List<String> depto = Arrays.asList(new String[]{businessUnit, department});
            insertDepartmentFromDevice(new RowDTO(depto), result, loggedUser);
            Long departmentId = getBusinessUnitDepartmentId(businessUnit, department);
            Map areaMap = insertArea(
                    area,
                    area,
                    1,
                    businessUnit,
                    departmentId,
                    department,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    loggedUser
            );
            if (areaMap.get("modified") != null) {
                result.getSb().append("<p class='done area'>").append(areaMap.get("description")).append(" </p><br />");
            } else {
                result.getSb().append("<p class='added area'>").append(areaMap.get("description")).append(" </p><br />");
            }
            device.setDepartmentId(departmentId);
            device.setAreaId(Long.valueOf(areaMap.get("id").toString()));
            Map params = new HashMap();
            params.put("code", attendant);
            Long user = this.HQL_findSimpleLong(""
                    + "SELECT usr.id from "
                    + UserRef.class.getCanonicalName() + " usr "
                    + "WHERE usr.code = :code ", params);
            if (user == 0) {
                user = loggedUser.getId();
            }
            device.setResponsibleId(user);

            Map priorityMap = insertPriority(priority, loggedUser);
            result.getSb().append(priorityMap.get("description")).append("<br />");
            device.setPriorityId(Long.valueOf(priorityMap.get("id").toString()));

            Map classificationMap = insertClassification(classification, loggedUser);
            result.getSb().append(classificationMap.get("description")).append("<br />");
            device.setClassificationId(Long.valueOf(classificationMap.get("id").toString()));

            device.setLocalization(localization);
            device.setPrecision(precision);
            device.setOperationRange(range);
            device.setUseDate(use);
            device.setPurchaseDate(purchase);
            device.setChangeDate(null);
            device.setUncertainty(uncertainty);
            device.setTreshold(treshold);

            makePersistent(device, loggedUser.getId());
            getLogger().error("Device {} saved", code);
        } catch (final Exception e) {
            getLogger().error("Error al grabar el Equipo '" + row.get(5) + "': ", e);
            result.getSb().append("<p class='error une'>Error al grabar el Equipo '").append(row.get(5)).append("' </p>");
        }
    }

    private Map insertDeviceStatus(String description, LoggedUser loggedUser) {
        getLogger().trace("Inserting device status {}", description);
        Map result = new HashMap();
        Map params = new HashMap();
        params.put("description", description);
        List<DeviceStatus> devStatus = this.HQL_findByQuery("SELECT devStatus from "
                + DeviceStatus.class.getCanonicalName() + " devStatus "
                + "WHERE devStatus.description = :description", params);

        if (devStatus.isEmpty()) {
            getLogger().trace("Adding device status");
            DeviceStatus status = new DeviceStatus();
            status.setId(-1L);
            status.setCode(description);
            status.setCubeId(4);
            status.setDescription(description);
            status.setDeleted(0);
            status.setStatus(1);
            makePersistent(status, loggedUser.getId());
            result.put("description", "Adding device status " + description);
            result.put("id", status.getId().intValue());
            return result;
        }
        result.put("description", "Description already exists " + description);
        result.put("id", devStatus.get(0).getId().intValue());
        return result;
    }

    private Map insertDeviceGroup(String description, LoggedUser loggedUser) {
        getLogger().trace("Inserting device group {}", description);
        Map result = new HashMap();
        if (description.isEmpty()) {
            getLogger().trace("Device group description is empty");
            result.put("description", "Description is empty");
            result.put("id", 1);
            return result;
        }
        Map params = new HashMap();
        params.put("description", description);
        List<DeviceGroup> devGroup = HQL_findByQuery("SELECT devGroup from "
                + DeviceGroup.class.getCanonicalName() + " devGroup "
                + "WHERE devGroup.description = :description", params);
        if (devGroup.isEmpty()) {
            getLogger().trace("Adding device group");
            DeviceGroup group = new DeviceGroup();
            group.setId(-1L);
            group.setCode(description);
            group.setDescription(description);
            group.setDeleted(0);
            group.setStatus(1);
            makePersistent(group, loggedUser.getId());
            result.put("description", "Adding device group " + description);
            result.put("id", group.getId().toString());
            return result;
        }
        getLogger().trace("Description already exists");

        result.put("description", "Device group already exists " + description);
        result.put("id", devGroup.get(0).getId().toString());
        return result;
    }

    private Map insertDeviceType(String description, Long deviceGroup, LoggedUser loggedUser) {
        getLogger().trace("Inserting device type {}", description);
        Map result = new HashMap();
        if (description.isEmpty()) {
            getLogger().trace("Device type description is empty");
            result.put("description", "Device type is empty");
            result.put("id", 1);
            return result;
        }
        Map params = new HashMap();
        params.put("description", description);
        params.put("deviceGroupId", deviceGroup);
        List<DeviceType> devType = HQL_findByQuery("SELECT devType from "
                + DeviceType.class.getCanonicalName() + " devType "
                + "WHERE devType.description = :description "
                + "AND devType.deviceGroupId = :deviceGroupId", params);
        if (devType.isEmpty()) {
            getLogger().trace("Adding device tyoe");
            DeviceType type = new DeviceType();
            type.setId(-1L);
            type.setCode(description);
            type.setDescription(description);
            type.setDeviceGroupId(deviceGroup);
            type.setDeleted(0);
            type.setStatus(1);
            makePersistent(type, loggedUser.getId());
            result.put("description", "Adding device type " + description);
            result.put("id", type.getId().intValue());
            return result;
        }
        getLogger().trace("Description already exists");
        result.put("description", "Device type already exists " + description);
        result.put("id", devType.get(0).getId().intValue());
        return result;
    }

    private Map insertArea(
            String areaCode,
            String areaName,
            Integer areaStatus,
            String businessUnitCode,
            Long departmentId,
            String departmentName,
            String customField1,
            String customField2,
            String customField3,
            String customField4,
            String customField5,
            String customField6,
            LoggedUser loggedUser
    ) {
        getLogger().trace("Inserting area {}", areaName);
        final Map<String, Object> result = new HashMap<>(2);
        if (areaName.isEmpty()) {
            getLogger().trace("area description is empty");
            result.put("description", "area is empty");
            result.put("id", 1);
            return result;
        }
        Long areaId = HQL_findLong(" "
            + " SELECT area.id"
            + " FROM " + Area.class.getCanonicalName() + " area "
            + " WHERE "
                + " area.code = :areaCode ",
            ImmutableMap.of("areaCode", areaCode)
        );

        String existingAreaId = "";
        if (areaId == null || areaId == 0L) {
            result.put("description", "Adding new area " + areaCode);
            areaId = -1L;
        } else {
            result.put("modified", true);
            result.put("description", "Area " + areaCode + " modified");
            existingAreaId = " AND area.id != " + areaId;
        }

        final String sanitizedName = areaName.replaceAll(" \\(.+?\\)$", "");
        final Map<String, Object> repeatedName = HQL_findSimpleMap(" "
            + " SELECT "
                + " new map("
                    + " count(*) AS count"
                    + ",max(area.description) AS descriptionWithDepartment"
                + " ) "
            + " FROM " + Area.class.getCanonicalName() + " area "
            + " WHERE "
                + " ("
                    + " area.description like :descriptionLike "
                    + " OR area.description = :description "
                + " ) " + existingAreaId, ImmutableMap.of(
                    "descriptionLike", sanitizedName + " (%)"
                    ,"description", areaName
                )
        );
        if (!repeatedName.isEmpty()) {
            final String descriptionWithDepartment = sanitizedName + " (" + departmentName + ")";
            final Long count = (Long)repeatedName.get("count");
            if (descriptionWithDepartment.equals(repeatedName.get("descriptionWithDepartment"))) {
                areaName = sanitizedName + " (" + (count + 1) + ")";
            } else if (count > 0){
                areaName = descriptionWithDepartment;
            }
        }
        Area area;
        if (areaId == -1L) {
            getLogger().trace("Creating new area: " + areaCode);
            area = new Area();
            area.setId(-1L);
            area.setDeleted(0);
            area.setAttendantId(loggedUser.getId());
            area.setCode(areaCode);
        } else {
            area = (Area) HQLT_findById(Area.class, areaId);
            if(getLogger().isTraceEnabled()) {
                getLogger().trace("Updating area: " + area);
            }
        }
        area.setDepartmentId(departmentId);
        area.setDescription(areaName);
        area.setCustomField1(customField1);
        area.setCustomField2(customField2);
        area.setCustomField3(customField3);
        area.setCustomField4(customField4);
        area.setCustomField5(customField5);
        area.setCustomField6(customField6);
        area.setStatus(areaStatus);
        if (makePersistent(area, loggedUser.getId()) == null) {
            result.put("description", "Error at areaCode " + areaCode);
            result.put("id", 1);
            return result;
        }
        result.put("id", area.getId().toString());
        return result;

    }

    private Map insertPriority(String description, LoggedUser loggedUser) {
        getLogger().trace("Inserting priority {}", description);
        Map result = new HashMap();
        if (description.isEmpty()) {
            getLogger().trace("priority is empty");
            result.put("description", "Priority is empty");
            result.put("id", 1);
            return result;
        }
        Map params = new HashMap();
        params.put("description", description);
        List<Priority> priorities = HQL_findByQuery(""
                + " SELECT priority "
                + " FROM " + Priority.class.getCanonicalName() + " priority "
                + "WHERE priority.description = :description", params);
        if (priorities.isEmpty()) {
            getLogger().trace("Adding priority");
            Priority priority = new Priority();
            priority.setId(-1L);
            priority.setCode(description);
            priority.setDescription(description);
            priority.setDeleted(0);
            priority.setStatus(1);
            makePersistent(priority, loggedUser.getId());
            result.put("description", "Adding priority " + description);
            result.put("id", priority.getId().toString());
            return result;
        }
        getLogger().trace("priority already exists");

        result.put("description", "priority already exists " + description);
        result.put("id", priorities.get(0).getId().toString());
        return result;
    }

    private Map insertClassification(String description, LoggedUser loggedUser) {
        getLogger().trace("Inserting classification {}", description);
        Map result = new HashMap();
        if (description.isEmpty()) {
            getLogger().trace("classification is empty");
            result.put("description", "Classification is empty");
            result.put("id", 1);
            return result;
        }
        Map params = new HashMap();
        params.put("description", description);
        List<DeviceClassification> classifications = HQL_findByQuery("SELECT classification from "
                + DeviceClassification.class.getCanonicalName() + " classification "
                + "WHERE classification.description = :description", params);
        if (classifications.isEmpty()) {
            getLogger().trace("Adding classification");
            DeviceClassification classification = new DeviceClassification();
            classification.setId(-1L);
            classification.setCode(description);
            classification.setDescription(description);
            classification.setDeleted(0);
            classification.setStatus(1);
            makePersistent(classification, loggedUser.getId());
            result.put("description", "Adding classification " + description);
            result.put("id", classification.getId().toString());
            return result;
        }
        getLogger().trace("classification already exists");

        result.put("description", "priority already exists " + description);
        result.put("id", classifications.get(0).getId().toString());
        return result;
    }

    /**
     * Get business unit department id by department and business unit
     *
     * @param businessUnitCode Business Unit code
     * @param departmentName Department Description
     * @return Department Id value or 1L if not found
     */
    private Long getBusinessUnitDepartmentId(String businessUnitCode, String departmentName) {
        Map params = new HashMap();
        params.put("description", departmentName);
        params.put("code", businessUnitCode);
        Long departmentId = this.HQL_findSimpleLong(""
                + "SELECT dept.id from "
                + BusinessUnitDepartment.class.getCanonicalName() + " dept "
                + "WHERE dept.department.description = :description "
                + "AND dept.businessUnit.code = :code", params);
        if (departmentId == 0) {
            departmentId = 1L;
        }
        return departmentId;
    }

    private void updateRequestDate(String documentCode, String documentVersion, Date createdOn) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("documentCode", documentCode);
        params.put("documentVersion", documentVersion);
        params.put("creationDate", createdOn);
        Integer items = HQL_updateByQuery(""
                + " UPDATE " + Request.class.getCanonicalName() + " c"
                + " SET c.creationDate = :creationDate"
                + " WHERE c.documentCode = :documentCode AND c.version = :documentVersion", params);
        if (items > 0) {
            getLogger().trace("Updated creation date for request of document with code '{}' and version '{}'", documentCode, documentVersion);
        }
        items = HQL_updateByQuery(""
                + " UPDATE " + PendingRecord.class.getCanonicalName() + " c"
                + " SET c.creationDate = :creationDate, c.lastUpdated = :creationDate, "
                + " c.attendedOn = :creationDate, c.commitmentDate = :creationDate"
                + " WHERE c.module = 'document' AND c.base = 'DPMS.Mapping.Request'"
                + " AND c.recordId IN ("
                + " SELECT r.id"
                + " FROM " + Request.class.getCanonicalName() + " r"
                + " WHERE r.documentCode = :documentCode AND r.version = :documentVersion"
                + " )", params);
        if (items > 0) {
            getLogger().trace("Updated creation date for pending request of document with code '{}' and version '{}'", documentCode, documentVersion);
        }
    }

    private void updateVerificationDate(String documentCode, String documentVersion, Date createdOn) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("documentCode", documentCode);
        params.put("documentVersion", documentVersion);
        params.put("creationDate", createdOn);
        Integer items = HQL_updateByQuery(""
                + " UPDATE " + Verification.class.getCanonicalName() + " c"
                + " SET c.lastModification = :creationDate"
                + " WHERE c.requestId IN ("
                + " SELECT r.id"
                + " FROM c.request r"
                + " WHERE r.documentCode = :documentCode AND r.version = :documentVersion"
                + " )", params);
    }

    private void updateAuthorizationDate(String documentCode, String documentVersion, Date createdOn) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("documentCode", documentCode);
        params.put("documentVersion", documentVersion);
        params.put("creationDate", createdOn);
        Integer items = HQL_updateByQuery(""
                + " UPDATE " + AutorizationPoolDetails.class.getCanonicalName() + " c"
                + " SET c.modificationDate = :creationDate, c.finishDate = :creationDate"
                + " WHERE c.requestId IN ("
                + " SELECT r.id"
                + " FROM c.request r"
                + " WHERE r.documentCode = :documentCode AND r.version = :documentVersion"
                + " )", params);
        if (items > 0) {
            getLogger().trace("Updated creation date for autorization pool details of document with code '{}' and version '{}'", documentCode, documentVersion);
        }
        items = HQL_updateByQuery(""
                + " UPDATE " + AutorizationPool.class.getCanonicalName() + " c"
                + " SET c.creationDate = :creationDate"
                + " WHERE c.requestId IN ("
                + " SELECT r.id"
                + " FROM c.request r"
                + " WHERE r.documentCode = :documentCode AND r.version = :documentVersion"
                + " )", params);
        if (items > 0) {
            getLogger().trace("Updated creation date for autorization pool of document with code '{}' and version '{}'", documentCode, documentVersion);
        }
    }

    private void updateDocumentDate(String documentCode, String documentVersion, Date createdOn) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("documentCode", documentCode);
        params.put("documentVersion", documentVersion);
        params.put("creationDate", createdOn);
        Integer items = HQL_updateByQuery(""
                + " UPDATE " + Document.class.getCanonicalName() + " c"
                + " SET c.creationDate = :creationDate, c.lastModificationDate = :creationDate"
                + " WHERE c.code = :documentCode AND c.version = :documentVersion", params);
        if (items > 0) {
            getLogger().trace("Updated creation date for document with code '{}' and version '{}'", documentCode, documentVersion);
        }
        items = HQL_updateByQuery(""
                + " UPDATE " + DocumentTraceability.class.getCanonicalName() + " c"
                + " SET c.createdOn = :creationDate"
                + " WHERE c.documentCode = :documentCode AND c.version = :documentVersion", params);
        if (items > 0) {
            getLogger().trace("Updated creation date for request of document traceability with code '{}' and version '{}'", documentCode, documentVersion);
        }
        items = HQL_updateByQuery(""
                + " UPDATE " + PendingRecord.class.getCanonicalName() + " c"
                + " SET c.creationDate = :creationDate, c.lastUpdated = :creationDate, "
                + " c.attendedOn = :creationDate, c.commitmentDate = :creationDate"
                + " WHERE c.module = 'document' AND c.base = 'DPMS.Mapping.Document'"
                + " AND c.recordId IN ("
                + " SELECT d.id"
                + " FROM " + Document.class.getCanonicalName() + " d"
                + " WHERE d.code = :documentCode AND d.version = :documentVersion"
                + " )", params);
        if (items > 0) {
            getLogger().trace("Updated creation date for pending of document with code '{}' and version '{}'", documentCode, documentVersion);
        }
    }

    private Long getNextStorageId() {
        final Long lastId = HQL_findSimpleLong(""
                + " SELECT MAX(c.id.tipoId)"
                + " FROM " + Catalog.class.getCanonicalName() + " c"
                + " WHERE c.id.catalogoId = :catalogoId",
                ImmutableMap.of("catalogoId", Long.valueOf(Properties.CATALOGO_LUGAR_ALMACENAMIENTO))
        );
        if (lastId == null) {
            return 1L;
        }
        return lastId + 1;
    }

    private Catalog getStoragePlaceByCode(final String storageCode) {
        if (storageCode == null || storageCode.trim().isEmpty()) {
            return null;
        }
        return (Catalog) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + Catalog.class.getCanonicalName() + " c "
                + " WHERE c.id.catalogoId = :catalogoId"
                + " AND c.code = :storageCode", 
                ImmutableMap.of("storageCode", storageCode.trim(),
                        "catalogoId", Long.valueOf(Properties.CATALOGO_LUGAR_ALMACENAMIENTO)
                )
        );
    }

    private Catalog insertStorage(final RowDTO rowData, final LoggedUser loggedUser) {
        final String storageLocation = helper.cellValue(rowData, CsvDocumentColumns.STORAGE_LOCATION);
        if (storageLocation == null || storageLocation.trim().isEmpty()
                || "N/A".equals(storageLocation.trim())) {
            return null;
        }
        final Catalog storage = getStoragePlaceByCode(storageLocation);
        if (storage != null) {
            return storage;
        }
        final Catalog newStorage = new Catalog();
        final Long catalogId = Long.valueOf(Properties.CATALOGO_LUGAR_ALMACENAMIENTO);
        final Long nextStorageId = getNextStorageId();
        final CatalogPK storageId = new CatalogPK(catalogId, nextStorageId);
        newStorage.setId(storageId);
        newStorage.setModuleId(0);
        newStorage.setIsRetainable(0);
        newStorage.setValue(nextStorageId.toString());
        newStorage.setCode(storageLocation.trim());
        newStorage.setDescription(storageLocation.trim());
        return (Catalog) makePersistent(newStorage, loggedUser.getId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void insertDocumentReference(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        try {
            notEmpty(CSVReference.FOLDER_PATH, rowData);
            notEmpty(CSVReference.BUSINESS_UNIT_CODE, rowData);
            notEmpty(CSVReference.DOCUMENT_CODE, rowData);
            notEmpty(CSVReference.DOCUMENT_VERSION, rowData);

            final String targetPath = helper.cellValue(CSVReference.FOLDER_PATH, rowData);
            final String documentBusinessUnitCode = helper.cellValue(CSVReference.BUSINESS_UNIT_CODE, rowData);
            final String documentCode = helper.cellValue(CSVReference.DOCUMENT_CODE, rowData);
            final String documentVersion = helper.cellValue(CSVReference.DOCUMENT_VERSION, rowData);

            final Long existsDocument = countDocument(documentCode, documentVersion, documentBusinessUnitCode);
            exists(existsDocument, CSVReference.DOCUMENT_CODE, rowData);

            final Map<String, Long> businessUnitOperation = insertBusinessUnitStatus(documentBusinessUnitCode, documentBusinessUnitCode, null, loggedUser);
            final BusinessUnitLite businessUnit = (BusinessUnitLite) HQLT_findById(
                    BusinessUnitLite.class,
                    businessUnitOperation.get("id")
            );
            final String[] pathParts = targetPath.split("/", -1);
            final OrganizationalUnitSimple organizationalUnit = (OrganizationalUnitSimple) HQLT_findById(
                    OrganizationalUnitSimple.class,
                    businessUnit.getOrganizationalUnitId()
            );
            final String[] basePath = new String[]{organizationalUnit.getDescription(), businessUnit.getDescription()};
            final List<String> fullPathParts = new ArrayList<>(basePath.length + pathParts.length);
            Collections.addAll(fullPathParts, basePath);
            Collections.addAll(fullPathParts, pathParts);

            final NodeSimple node = insertNode(new RowDTO(fullPathParts), loggedUser);
            final String documentMasterId = getDocumentMasterId(documentCode, documentVersion, documentBusinessUnitCode);
            saveReference(node, documentMasterId, result, loggedUser);
        } catch (final CsvException e) {
            handleCsvException(e.getMessage(), result);
        } catch (final Exception e) {
            handleException(CSVReference.getGenericErrorKey(), rowData, result, e);
        }
    }

    private String getDocumentMasterId(final String documentCode, final String documentVersion, final String documentBusinessUnitCode) {
        final Map<String, String> params = new HashMap<>(3);
        params.put("documentCode", documentCode);
        params.put("documentVersion", documentVersion);
        params.put("businessUnit", documentBusinessUnitCode);
        final String documentMasterId = HQL_findSimpleString(""
                + " SELECT c.masterId"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " LEFT JOIN c.businessUnit bu"
                + " WHERE c.deleted = 0"
                + " AND c.code = :documentCode"
                + " AND c.version = :documentVersion"
                + " AND bu.code = :businessUnit", params);
        return documentMasterId;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void modifyOrganizationalUnit(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        try {
            notEmpty(CsvOrganizationalUnit.CODE, rowData);
            notEmpty(CsvOrganizationalUnit.DOCUMENT_MANAGER_ACCOUNT, rowData);

            final String organizationalUnitCode = helper.cellValue(CsvOrganizationalUnit.CODE, rowData);
            final String documentManagerAccount = helper.cellValue(CsvOrganizationalUnit.DOCUMENT_MANAGER_ACCOUNT, rowData);

            final Long countOrg = HQL_findSimpleLong(""
                    + " SELECT count(c.id)"
                    + " FROM " + OrganizationalUnit.class.getCanonicalName() + " c"
                    + " WHERE c.deleted = 0 AND c.code = :code",
                    "code", organizationalUnitCode);

            exists(countOrg, CsvOrganizationalUnit.CODE, rowData);
            exists(countUser(cache, documentManagerAccount), CsvOrganizationalUnit.DOCUMENT_MANAGER_ACCOUNT, rowData);

            updateOrganizationUnit(cache, organizationalUnitCode, documentManagerAccount, result, loggedUser);
        } catch (final CsvException e) {
            handleCsvException(e.getMessage(), result);
        } catch (final Exception e) {
            handleException(CsvOrganizationalUnit.getGenericErrorKey(), rowData, result, e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void modifyBusinessUnit(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        try {
            notEmpty(CsvBusinessUnit.CODE, rowData);

            final String businessUnitCode = helper.cellValue(CsvBusinessUnit.CODE, rowData);
            final String managerAccount = helper.cellValue(CsvBusinessUnit.MANAGER_ACCOUNT, rowData);
            final String documentManagerAccount = helper.cellValue(CsvBusinessUnit.DOCUMENT_MANAGER_ACCOUNT, rowData);
            final String description = helper.cellValue(CsvBusinessUnit.DESCRIPTION, rowData);

            exists(countBusinessUnit(businessUnitCode), CsvBusinessUnit.CODE, rowData);
            if (!managerAccount.isEmpty()) {
                exists(countUser(cache, managerAccount), CsvBusinessUnit.MANAGER_ACCOUNT, rowData);
            }
            if (!documentManagerAccount.isEmpty()) {
                exists(countUser(cache, documentManagerAccount), CsvBusinessUnit.DOCUMENT_MANAGER_ACCOUNT, rowData);
            }
            updateBusinessUnit(cache, businessUnitCode, managerAccount, documentManagerAccount, description, result, loggedUser);
        } catch (final CsvException e) {
            handleCsvException(e.getMessage(), result);
        } catch (final Exception e) {
            handleException(CsvBusinessUnit.getGenericErrorKey(), rowData, result, e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void modifyBusinessUnitDepartment(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        try {
            notEmpty(CsvBusinessUnitDepartment.BUSINESS_UNIT_CODE, rowData);
            notEmpty(CsvBusinessUnitDepartment.DEPARTMENT_CODE, rowData);
            notEmpty(CsvBusinessUnitDepartment.MANAGER_ACCOUNT, rowData);
            notEmpty(CsvBusinessUnitDepartment.DOCUMENT_MANAGER_ACCOUNT, rowData);

            final String businessUnitCode = helper.cellValue(CsvBusinessUnitDepartment.BUSINESS_UNIT_CODE, rowData);
            final String departmentCode = helper.cellValue(CsvBusinessUnitDepartment.DEPARTMENT_CODE, rowData);
            final String managerAccount = helper.cellValue(CsvBusinessUnitDepartment.MANAGER_ACCOUNT, rowData);
            final String documentManagerAccount = helper.cellValue(CsvBusinessUnitDepartment.DOCUMENT_MANAGER_ACCOUNT, rowData);

            exists(countBusinessUnit(businessUnitCode), CsvBusinessUnitDepartment.BUSINESS_UNIT_CODE, rowData);
            exists(countBusinessUnitDepartment(departmentCode), CsvBusinessUnitDepartment.DEPARTMENT_CODE, rowData);
            exists(countUser(cache, managerAccount), CsvBusinessUnitDepartment.MANAGER_ACCOUNT, rowData);
            exists(countUser(cache, documentManagerAccount), CsvBusinessUnitDepartment.DOCUMENT_MANAGER_ACCOUNT, rowData);

            modifyBusinessUnitDepartment(
                    cache,
                    businessUnitCode,
                    departmentCode,
                    managerAccount,
                    documentManagerAccount,
                    result,
                    loggedUser
            );
        } catch (final CsvException e) {
            handleCsvException(e.getMessage(), result);
        } catch (final Exception e) {
            handleException(CsvBusinessUnitDepartment.getGenericErrorKey(), rowData, result, e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void modifyDepartmentProcess(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        try {
            notEmpty(CsvDepartmentProcess.BUSINESS_UNIT_CODE, rowData);
            notEmpty(CsvDepartmentProcess.DEPARTMENT_CODE, rowData);
            notEmpty(CsvDepartmentProcess.PROCESS_CODE, rowData);
            notEmpty(CsvDepartmentProcess.MANAGER_ACCOUNT, rowData);

            final String businessUnitCode = helper.cellValue(CsvDepartmentProcess.BUSINESS_UNIT_CODE, rowData);
            final String departmentCode = helper.cellValue(CsvDepartmentProcess.DEPARTMENT_CODE, rowData);
            final String processCode = helper.cellValue(CsvDepartmentProcess.PROCESS_CODE, rowData);
            final String managerAccount = helper.cellValue(CsvDepartmentProcess.MANAGER_ACCOUNT, rowData);

            exists(countBusinessUnit(businessUnitCode), CsvDepartmentProcess.BUSINESS_UNIT_CODE, rowData);
            exists(countBusinessUnitDepartment(departmentCode), CsvDepartmentProcess.DEPARTMENT_CODE, rowData);
            final Long countPro = HQL_findSimpleLong(""
                    + " SELECT count(c.id)"
                    + " FROM " + Process.class.getCanonicalName() + " c"
                    + " WHERE c.deleted = 0 AND c.code = :code", "code", processCode);
            exists(countPro, CsvDepartmentProcess.PROCESS_CODE, rowData);
            exists(countUser(cache, managerAccount), CsvDepartmentProcess.MANAGER_ACCOUNT, rowData);

            modifyDepartmentProcess(cache, businessUnitCode, departmentCode, processCode, managerAccount, result, loggedUser);
        } catch (final CsvException e) {
            handleCsvException(e.getMessage(), result);
        } catch (final Exception e) {
            handleException(CsvDepartmentProcess.getGenericErrorKey(), rowData, result, e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void modifyArea(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        try {
            notEmpty(CsvArea.CODE, rowData);
            notEmpty(CsvArea.MANAGER_ACCOUNT, rowData);

            final String areaCode = helper.cellValue(CsvArea.CODE, rowData);
            final String managerAccount = helper.cellValue(CsvArea.MANAGER_ACCOUNT, rowData);
            final Long countArea = HQL_findSimpleLong(""
                    + " SELECT count(c.id)"
                    + " FROM " + Area.class.getCanonicalName() + " c"
                    + " WHERE c.deleted = 0 AND c.code = :code", "code", areaCode);
            exists(countArea, CsvArea.CODE, rowData);
            exists(countUser(cache, managerAccount), CsvArea.MANAGER_ACCOUNT, rowData);

            updateArea(cache, areaCode, managerAccount, result, loggedUser);
        } catch (final CsvException e) {
            handleCsvException(e.getMessage(), result);
        } catch (final Exception e) {
            handleException(CsvArea.getGenericErrorKey(), rowData, result, e);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void addRelatedDocument(final RowDTO rowData, LoadCacheDTO cache, final BackendUploadResult result, final LoggedUser loggedUser) throws IOException {
        try {
            notEmpty(CsvRelatedDocument.BASE_BUSINESS_UNIT_CODE, rowData);
            notEmpty(CsvRelatedDocument.BASE_DOCUMENT_CODE, rowData);
            notEmpty(CsvRelatedDocument.BASE_DOCUMENT_VERSION, rowData);
            notEmpty(CsvRelatedDocument.TARGET_BUSINESS_UNIT_CODE, rowData);
            notEmpty(CsvRelatedDocument.TARGET_DOCUMENT_CODE, rowData);
            notEmpty(CsvRelatedDocument.TARGET_DOCUMENT_VERSION, rowData);

            final String baseBusinessUnitCode = helper.cellValue(CsvRelatedDocument.BASE_BUSINESS_UNIT_CODE, rowData);
            final String baseDocumentCode = helper.cellValue(CsvRelatedDocument.BASE_DOCUMENT_CODE, rowData);
            final String baseDocumentVersion = helper.cellValue(CsvRelatedDocument.BASE_DOCUMENT_VERSION, rowData);
            final String targetBusinessUnitCode = helper.cellValue(CsvRelatedDocument.TARGET_BUSINESS_UNIT_CODE, rowData);
            final String targetDocumentCode = helper.cellValue(CsvRelatedDocument.TARGET_DOCUMENT_CODE, rowData);
            final String targetDocumentVersion = helper.cellValue(CsvRelatedDocument.TARGET_DOCUMENT_VERSION, rowData);

            exists(countBusinessUnit(baseBusinessUnitCode), CsvRelatedDocument.BASE_BUSINESS_UNIT_CODE, rowData);
            exists(countBusinessUnit(targetBusinessUnitCode), CsvRelatedDocument.TARGET_BUSINESS_UNIT_CODE, rowData);

            final Long existsBaseDocument = countDocument(baseDocumentCode, baseDocumentVersion, baseBusinessUnitCode);
            exists(existsBaseDocument, CsvRelatedDocument.BASE_DOCUMENT_CODE, rowData);

            final Long existsTargetDocument = countDocument(targetDocumentCode, targetDocumentVersion, targetBusinessUnitCode);
            exists(existsTargetDocument, CsvRelatedDocument.TARGET_DOCUMENT_CODE, rowData);

            final Long baseDocumentId = getDocumentId(baseDocumentCode, baseDocumentVersion, baseBusinessUnitCode);
            final Long targetDocumentId = getDocumentId(targetDocumentCode, targetDocumentVersion, targetBusinessUnitCode);

            addRelatedDocument(baseDocumentId, targetDocumentId, rowData, result, loggedUser);
        } catch (final CsvException e) {
            handleCsvException(e.getMessage(), result);
        } catch (final Exception e) {
            handleException(CsvDepartmentProcess.getGenericErrorKey(), rowData, result, e);
        }
    }

    @Override
    @OnSuccessProcessBulkUserFile
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Boolean onSuccessProcessBulkUser(
            final IBulkUser bulkUser,
            final GenericSaveHandle gsh,
            final ILoggedUser loggedUser
    ) {
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void foldersPermissions(
            RowDTO rowData,
            LoadCacheDTO cache,
            BackendUploadResult result,
            LoggedUser loggedUser
    ) throws IOException {
        final String targetPath = helper.cellValue(CsvFoldersPermissions.FOLDER_PATH, rowData);
        if (targetPath == null || targetPath.trim().isEmpty()) {
            result.getSb().append("<p class='error one_tab'>Ruta inválida, agregue un valor </p>");
            return;
        }
        String[] pathParts;
        if (targetPath.contains("/")) {
            pathParts = targetPath.split("/", -1);
        } else {
            pathParts = targetPath.split("\\\\", -1);
        }
        if (pathParts.length == 0) {
            result.getSb().append("<p class='error one_tab'>Ruta inválida \"")
                    .append(targetPath)
                    .append("\", agregue un valor </p>");
            return;
        }
        final String userAccount = helper.cellValue(CsvFoldersPermissions.USER_ACCOUNT, rowData);
        if (userAccount == null || userAccount.trim().isEmpty()) {
            result.getSb().append("<p class='error one_tab'>Usuario inválido, agregue un valor </p>");
            return;
        }
        final Long userId = getUser(cache, userAccount);
        if (userId == null || userId <= 0) {
            result.getSb().append("<p class='error one_tab'>Usuario no encontrado \"")
                    .append(userAccount)
                    .append("\", verifique el valor </p>");
            return;
        }
        final RowDTO nodeRow = new RowDTO(Arrays.asList(pathParts));
        final NodeSimple node = insertNode(nodeRow, loggedUser);
        if (node == null) {
            result.getSb().append("<p class='error one_tab'>No se pudo crear la ruta \"")
                    .append(targetPath)
                    .append("\", verifique el valor </p>");
            return;
        }
        final Map<Long, Node> nodeCache = new HashMap<>();
        final Node nodeFull = (Node) HQLT_findById(Node.class, node.getId());
        assignPermission(userId, nodeFull, result, nodeCache, loggedUser);
    }

    private void saveReference(
            final NodeSimple node,
            final String documentMasterId,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final Map<String, Object> params = new HashMap<>(3);
        params.put("nodeId", node.getId());
        params.put("documentMasterId", documentMasterId);
        final Long exsistsReference = HQL_findSimpleLong(""
                + " SELECT COUNT(c.id.code) "
                + " FROM " + DocumentReference.class.getCanonicalName() + " c"
                + " WHERE c.id.nodeId = :nodeId"
                + " AND c.id.documentMasterId = :documentMasterId", params);
        if (exsistsReference <= 0) {
            final DocumentReference reference = new DocumentReference(node.getId(), documentMasterId);
            reference.setInsert(true);
            makePersistent(reference, loggedUser.getId());
            final String message = getTag(CSVReference.getSuccessKey())
                    .replace(":documentMasterId", documentMasterId)
                    .replace(":path", node.getPath());
            result.getSb().append("<p class='added documentReference'>").append(message).append("</p>");
        } else {
            final String message = getTag(CSVReference.getSkippedKey())
                    .replace(":documentMasterId", documentMasterId)
                    .replace(":path", node.getPath());
            result.getSb().append("<p class='done usr' >").append(message).append("</p>");
        }
    }

    private Long countBusinessUnitDepartment(final String code) {
        return HQL_findSimpleLong(""
                + " SELECT count(c.id)"
                + " FROM " + Department.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0 AND c.code = :code", "code", code);
    }

    private Long countBusinessUnit(final String code) {
        return HQL_findSimpleLong(""
                + " SELECT count(c.id)"
                + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                + " WHERE c.deleted = 0 AND c.code = :code", "code", code);
    }

    private Long countUser(LoadCacheDTO cache, final String account) {
        final Long userId = getUser(cache, account);
        return userId != null && userId > 0 ? 1L : 0L;
    }

    private Long getUser(LoadCacheDTO cache, final String account) {
        final Long userId = cache.getUserCache().getUserIdByAccount(account);
        return userId;
    }

    private void updateOrganizationUnit(
            LoadCacheDTO cache,
            final String organizationalUnitCode,
            final String documentManagerAccount,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final OrganizationalUnit org = (OrganizationalUnit) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + OrganizationalUnit.class.getCanonicalName() + " c"
                + " WHERE c.code = :code", "code", organizationalUnitCode);
        org.setDocumentManagerId(getUser(cache, documentManagerAccount));
        makePersistent(org, loggedUser.getId());
        final String message = getTag(CsvOrganizationalUnit.getSuccessKey())
                .replace(":organizationalUnitCode", organizationalUnitCode);
        result.getSb().append("<p class='done org'>").append(message).append("</p>");
    }

    private void updateBusinessUnit(
            LoadCacheDTO cache,
            final String businessUnitCode,
            final String managerAccount,
            final String documentManagerAccount,
            final String description,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final BusinessUnit bu = (BusinessUnit) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + BusinessUnit.class.getCanonicalName() + " c"
                + " WHERE c.code = :code", "code", businessUnitCode);
        if (!managerAccount.isEmpty()) {
            bu.setAttendantId(getUser(cache, managerAccount));
        }
        if (!documentManagerAccount.isEmpty()) {
            bu.setDocumentManagerId(getUser(cache, documentManagerAccount));
        }
        if (!description.isEmpty()) {
            bu.setDescription(description);
        }
        makePersistent(bu, loggedUser.getId());
        final String message = getTag(CsvBusinessUnit.getSuccessKey())
                .replace(":businessUnitCode", businessUnitCode);
        result.getSb().append("<p class='done businessUnit'>").append(message).append("</p>");
    }

    private void modifyBusinessUnitDepartment(
            LoadCacheDTO cache,
            final String businessUnitCode,
            final String departmentCode,
            final String managerAccount,
            final String documentManagerAccount,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("departmentCode", departmentCode);
        params.put("businessUnitCode", businessUnitCode);
        final BusinessUnitDepartment businessUnitDep = (BusinessUnitDepartment) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + BusinessUnitDepartment.class.getCanonicalName() + " c"
                + " WHERE c.department.code = :departmentCode"
                + " AND c.businessUnit.code = :businessUnitCode", params);
        businessUnitDep.setAttendantId(getUser(cache, managerAccount));
        businessUnitDep.setDocumentManagerId(getUser(cache, documentManagerAccount));
        makePersistent(businessUnitDep, loggedUser.getId());
        final String message = getTag(CsvBusinessUnitDepartment.getSuccessKey())
                .replace(":departmentCode", departmentCode);
        result.getSb().append("<p class='done department'>").append(message).append("</p>");
    }

    private void modifyDepartmentProcess(
            LoadCacheDTO cache,
            final String businessUnitCode,
            final String departmentCode,
            final String processCode,
            final String managerAccount,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final Map<String, Object> params = new HashMap<>(3);
        params.put("departmentCode", departmentCode);
        params.put("businessUnitCode", businessUnitCode);
        params.put("processCode", processCode);
        final DepartmentProcess processDep = (DepartmentProcess) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + DepartmentProcess.class.getCanonicalName() + " c"
                + " WHERE c.department.code = :departmentCode"
                + " AND c.department.businessUnit.code = :businessUnitCode"
                + " AND c.process.code = :processCode", params);
        processDep.setAttendantId(getUser(cache, managerAccount));
        makePersistent(processDep, loggedUser.getId());
        final String message = getTag(CsvDepartmentProcess.getSuccessKey())
                .replace(":processCode", departmentCode);
        result.getSb().append("<p class='done process'>").append(message).append("</p>");
    }

    private void updateArea(
            LoadCacheDTO cache,
            final String areaCode,
            final String managerAccount,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final Map<String, Object> params = new HashMap<>(1);
        params.put("areaCode", areaCode);
        final Area area = (Area) HQL_findSimpleObject(""
                + " SELECT c"
                + " FROM " + Area.class.getCanonicalName() + " c"
                + " WHERE c.code = :areaCode", params);
        area.setAttendantId(getUser(cache, managerAccount));
        makePersistent(area, loggedUser.getId());
        final String message = getTag(CsvArea.getSuccessKey())
                .replace(":areaCode", areaCode);
        result.getSb().append("<p class='done area'>").append(message).append("</p>");
    }

    private void notEmpty(final ICsvIndex index, final RowDTO rowData) {
        final String value = helper.cellValue(index, rowData);
        if (value == null || value.isEmpty()) {
            final String message = getTag("EMPTY_" + index.getName());
            throw new CsvException(helper.getErrorDetail(message, rowData));
        }
    }

    private void exists(final Long value, final ICsvIndex index, final RowDTO rowData) {
        if (value == null || value <= 0) {
            final String message = getTag("NOT_EXISTS_" + index.getName());
            throw new CsvException(helper.getErrorDetail(message, rowData));
        }
    }

    private Long countDocument(
            final String code,
            final String version,
            final String businessUnitCode
    ) {
        final Map<String, String> params = new HashMap<>(3);
        params.put("documentCode", code);
        params.put("documentVersion", version);
        params.put("businessUnit", businessUnitCode);
        final Long count = HQL_findSimpleLong(""
                + " SELECT count(c.id)"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " LEFT JOIN c.businessUnit bu"
                + " WHERE c.deleted = 0"
                + " AND c.code = :documentCode"
                + " AND c.version = :documentVersion"
                + " AND bu.code = :businessUnit", params);
        return count;
    }

    private Long getDocumentId(final String code, final String version, final String businessUnitCode) {
        final Map<String, String> params = new HashMap<>(3);
        params.put("documentCode", code);
        params.put("documentVersion", version);
        params.put("businessUnit", businessUnitCode);
        final Long documentId = HQL_findSimpleLong(""
                + " SELECT c.id"
                + " FROM " + Document.class.getCanonicalName() + " c"
                + " LEFT JOIN c.businessUnit bu"
                + " WHERE c.deleted = 0"
                + " AND c.code = :documentCode"
                + " AND c.version = :documentVersion"
                + " AND bu.code = :businessUnit", params);
        return documentId;
    }

    private void addRelatedDocument(
            final Long baseDocumentId,
            final Long targetDocumentId,
            final RowDTO rowData,
            final BackendUploadResult result,
            final LoggedUser loggedUser
    ) throws IOException {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("baseDocumentId", baseDocumentId);
        params.put("targetDocumentId", targetDocumentId);
        final Long exsistsRelation = HQL_findSimpleLong(""
                + " SELECT COUNT(c.id.documentIdA) "
                + " FROM " + RelatedDocument.class.getCanonicalName() + " c"
                + " WHERE c.id.documentIdA = :targetDocumentId"
                + " AND c.id.documentIdB = :baseDocumentId", params);
        final String baseDocumentCode = helper.cellValue(CsvRelatedDocument.BASE_DOCUMENT_CODE, rowData);
        final String targetDocumentCode = helper.cellValue(CsvRelatedDocument.TARGET_DOCUMENT_CODE, rowData);
        if (exsistsRelation <= 0) {
            final RelatedDocument related = new RelatedDocument(targetDocumentId, baseDocumentId);
            related.setInsert(true);
            makePersistent(related, loggedUser.getId());
            final String message = getTag(CsvRelatedDocument.getSuccessKey());
            result.getSb().append("<p class='added relatedDocument'>")
                    .append(helper.getRowDetail(message, rowData)
                            .replace(":baseDocumentCode", baseDocumentCode)
                            .replace(":targetDocumentCode", targetDocumentCode)
                    )
                    .append("</p>");
        } else {
            final String message = getTag(CsvRelatedDocument.getSkippedKey());
            result.getSb().append("<p class='done relatedDocument' >")
                    .append(helper.getRowDetail(message, rowData)
                            .replace(":baseDocumentCode", baseDocumentCode)
                            .replace(":targetDocumentCode", targetDocumentCode)
                    )
                    .append("</p>");
        }
    }

    private void handleCsvException(final String message, final BackendUploadResult result) throws IOException {
        handleCsvException(message, result, null);
    }

    private void handleCsvException(final String message, final BackendUploadResult result, final Exception e) throws IOException {
        getLogger().error(message, e);
        result.getSb().append("<p class='error'>").append(message).append("</p>");
    }

    private void handleException(final String errorKey, final RowDTO rowData, final BackendUploadResult result, final Exception e) throws IOException {
        final String errorTag = getTag(errorKey);
        final String message = helper.getErrorDetail(errorTag, rowData, e);
        handleCsvException(message, result);
    }

    private void handleError(
            final BackendUploadResult result,
            final RowDTO row, 
            final ICsvIndex descriptionIndex, 
            final ICsvIndex errorIndex,
            final BulkLoadError errorType, 
            final LoggedUser loggedUser
    ) {
        final BulkLoadDTO log = result.getLog();
        if (log.getNoFailures() == null) {
            log.setNoFailures(0l);
        }
        log.setNoFailures(log.getNoFailures() + 1);
        final String description = helper.cellValue(row, descriptionIndex.getIndex());
        final String columnErrorValue = helper.cellValue(row, errorIndex);
        final BulkLoadRow failureEntity = new BulkLoadRow(
                StringUtils.substring(description, 0, 255),
                BulkLoadRowType.FAILED,
                row.getRow(),
                log.getId(),
                errorType.getValue(),
                columnErrorValue
        );
        makePersistent(failureEntity, loggedUser.getId());
    }

    private void handleCreated(
            final BackendUploadResult result,
            final RowDTO row,
            final ICsvIndex index,
            final LoggedUser loggedUser
    ) {
        final BulkLoadDTO log = result.getLog();
        if (log.getNoCreations() == null) {
            log.setNoCreations(0l);
        }
        log.setNoCreations(log.getNoCreations() + 1);
        final String description = helper.cellValue(row, index.getIndex());
        final BulkLoadRow updateEntity = new BulkLoadRow(
                StringUtils.substring(description, 0, 255),
                BulkLoadRowType.CREATED,
                row.getRow(),
                log.getId()
        );
        makePersistent(updateEntity, loggedUser.getId());
    }

    private void handleModified(
            final BackendUploadResult result,
            final RowDTO row,
            final ICsvIndex index,
            final LoggedUser loggedUser
    ) {
        final BulkLoadDTO log = result.getLog();
        if (log.getNoUpdates() == null) {
            log.setNoUpdates(0l);
        }
        log.setNoUpdates(log.getNoUpdates() + 1);
        final String description = helper.cellValue(row, index.getIndex());
        final BulkLoadRow updateEntity = new BulkLoadRow(
                StringUtils.substring(description, 0, 255),
                BulkLoadRowType.MODIFIED,
                row.getRow(),
                log.getId()
        );
        makePersistent(updateEntity, loggedUser.getId());
    }

}
