package DPMS.DAO;

import DPMS.DAOInterface.IBuildingDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Building;
import DPMS.Mapping.BusinessUnitDepartment;
import DPMS.Mapping.ClauseType;
import Framework.Config.ITextHasValue;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import java.util.List;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Lazy
@Repository(value = "HibernateDAO_Building")
@Scope(value = "singleton")
public class HibernateDAO_Building extends GenericDAOImpl<Building, Long> implements IBuildingDAO {

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActives() {
        String query
                = " SELECT new " + TextHasValue.class.getCanonicalName() + "("
                    + " bld.description || ' - ' || bu.description,"
                    + " bld.id"
                + " ) "
                + " FROM " + Building.class.getCanonicalName() + " bld "
                + " LEFT JOIN bld.businessUnit bu"
                + "WHERE bld.deleted = :deleted "
                + "AND bld.status = :status";
        query = query.replace(":deleted",ClauseType.IS_NOT_DELETED.toString())
                .replace(":status",ClauseType.ACTIVE_STATUS.toString());
        return this.HQL_findByQuery(query);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActivesInMyBusinessUnit(Long userId, Boolean isAdmin) {
        IUserDAO dao = getBean(IUserDAO.class);
        String query
                = " SELECT new " + TextHasValue.class.getCanonicalName() + "("
                    + " bld.description || ' - ' || bu.description,"
                    + " bld.id"
                + " )"
                + " FROM " + Building.class.getCanonicalName() + " bld "
                + " LEFT JOIN bld.businessUnit bu "
                + " WHERE bld.deleted = :deleted "
                + " AND bld.status = :status "
                + " AND " + dao.getBusinessUnitsForUser(userId.toString(), isAdmin, " bld.businessUnitId ")
                + " GROUP BY bld.id, bld.description, bu.description";
        query = query.replace(":deleted", ClauseType.IS_NOT_DELETED.toString())
                .replace(":status", ClauseType.ACTIVE_STATUS.toString());
        return this.HQL_findByQuery(query);
    }
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActivesInMyBusinessUnitByDepartmentId(Long userId, Boolean isAdmin, Long businessUnitDepartmentId) {
        String query
                = " SELECT new " + TextHasValue.class.getCanonicalName() + "("
                    + " bld.description || ' - ' || bu.description,"
                    + " bld.id)"
                + " FROM " + Building.class.getCanonicalName() + " bld, "
                + BusinessUnitDepartment.class.getCanonicalName() + " bud "
                + " LEFT JOIN bld.businessUnit bu"
                + "WHERE bld.businessUnit.id = bud.businessUnitId  "
                + "AND bld.deleted = :deleted "
                + "AND bld.status = :status "
                + "AND bud.id = :businessUnitDepartmentId "                
                + " " + getBusinessUnitsForUserFilter(userId, isAdmin, " bld.businessUnitId ")
                + " GROUP BY bld.id, bld.description, bu.description";
        query = query.replace(":deleted",ClauseType.IS_NOT_DELETED.toString())
                .replace(":status",ClauseType.ACTIVE_STATUS.toString())
                .replace(":businessUnitDepartmentId", businessUnitDepartmentId.toString());
        return this.HQL_findByQuery(query);
    }    
    
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActivesInMyBusinessUnitByBUDId(Long userId, Boolean isAdmin, Long budId) {
        StringBuilder qryBuilder = new StringBuilder(300);
        qryBuilder.append(""
            + " SELECT new ").append(TextHasValue.class.getCanonicalName()).append("("
                    + " bld.description || ' - ' || bu.description,"
                    + " bld.id"
                + ")"
            + " FROM "
                + "").append(Building.class.getCanonicalName()).append(" bld"
                + ",").append(BusinessUnitDepartment.class.getCanonicalName()).append(" bud "
                + " LEFT JOIN bld.businessUnit bu "
            + " WHERE"
                + " bld.businessUnit.id = bud.businessUnitId  "
                + " AND bld.deleted = ").append(ClauseType.IS_NOT_DELETED.toString()).append(""
                + " AND bld.status = ").append(ClauseType.ACTIVE_STATUS.toString()).append(""
                + " ").append(getBusinessUnitsForUserFilter(userId, isAdmin, " bld.businessUnitId "));
        if (budId != null) {
            qryBuilder.append(" AND bud.id = ").append(budId);
        }
        qryBuilder.append(" GROUP BY bld.id, bld.description, bu.description");
        return this.HQL_findByQuery(qryBuilder.toString());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<ITextHasValue> getActivesInMyBusinessUnitByBusinessUnitId(Long userId, Boolean isAdmin, Long businessUnitId) {
        StringBuilder qryBuilder = new StringBuilder(300);
        qryBuilder.append(""
            + " SELECT new ").append(TextHasValue.class.getCanonicalName()).append("("
                + " bld.description || ' - ' || bu.description,"
                + " bld.id"
            + " )"
            + " FROM ").append(Building.class.getCanonicalName()).append(" bld"
            + " JOIN bld.businessUnit bu"
            + " WHERE"
                + " bld.deleted = ").append(ClauseType.IS_NOT_DELETED.toString()).append(""
                + " AND bld.status = ").append(ClauseType.ACTIVE_STATUS.toString()).append(""
                + " ").append(getBusinessUnitsForUserFilter(userId, isAdmin, " bld.businessUnitId ")
        );
        if (businessUnitId != null) {
            qryBuilder.append(" AND bld.businessUnitId = ").append(businessUnitId);
        } else {
            // solo regresamos información si se utilizó el filtro por planta
            return Utilities.EMPTY_LIST;
        }
        return this.HQL_findByQuery(qryBuilder.toString());
    }

    private String getBusinessUnitsForUserFilter(Long userId, boolean isAdmin, String columnName) {
        IUserDAO dao = getBean(IUserDAO.class);
        final String filter = dao.getBusinessUnitsForUser(userId.toString(), isAdmin, columnName);
        if (filter.trim().replaceAll("\\s", Utilities.EMPTY_STRING).equals("1=1")) {
            return Utilities.EMPTY_STRING;
        }
        return " AND " + filter;
    }
}
