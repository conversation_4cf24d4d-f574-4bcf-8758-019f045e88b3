package DPMS.DAO;

import DPMS.DAOInterface.IOutstandingSurveysDAO;
import Framework.DAO.GenericDAOImpl;
import com.google.common.collect.ImmutableMap;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.document.listeners.OnCancelledFillForm;
import qms.document.listeners.OnDestroyedFillForm;
import qms.form.dao.IFormRequestDAO;

/**
 *
 * <AUTHOR>
 */
@Lazy
@Repository(value = "HibernateDAO_OutstandingSurveys")
@Scope(value = "singleton")
public class HibernateDAO_OutstandingSurveys extends GenericDAOImpl<OutstandingSurveys, Long> implements IOutstandingSurveysDAO {

    @Override
    @OnCancelledFillForm
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer cancelOutstandingSurvey(
            final Long requestId,
            final String reason,
            final Long outstandingSurveyId,
            final Boolean sendMail,
            final ILoggedUser loggedUser
    ) {
        final Long surveyProgressStateCancelId = HQL_findLong(" "
                + " SELECT c.surveyProgressStateCancelId"
                + " FROM " + OutstandingSurveys.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                ImmutableMap.of("id", outstandingSurveyId)
        );
        if (surveyProgressStateCancelId != null && surveyProgressStateCancelId > 0)  {
            HQL_updateByQuery(" "
                    + " UPDATE " + OutstandingSurveys.class.getCanonicalName()
                    + " SET progressStateId = :progressStateId "
                    + " WHERE id = :id",
                    ImmutableMap.of(
                            "progressStateId", surveyProgressStateCancelId,
                            "id", outstandingSurveyId
                    )
            );
        }
        final Integer result = HQL_updateByQuery(" "
                + " UPDATE " + OutstandingSurveys.class.getCanonicalName()
                + " SET status = :status, formReopenRequestId = null, formAdjustRequestId = null "
                + " WHERE id = :id",
                ImmutableMap.of(
                        "status", OutstandingSurveys.STATUS.CANCELLED.getValue(),
                        "id", outstandingSurveyId
                )
        );
        if (result > 0) {
            cancelFormRequestByOutstandingSurveysId(outstandingSurveyId, reason, sendMail, loggedUser);
        }
        return result;
    }
    
    @Override
    @OnDestroyedFillForm
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer destroyOutstandingSurvey(
            Long requestId,
            Long outstandingSurveyId,
            String reason,
            Boolean sendMail,
            LoggedUser loggedUser
    ) {
        final Map<String, Object> params = new HashMap<>();
        params.put("id", outstandingSurveyId);
        final Integer result = HQL_updateByQuery(" "
                + " UPDATE " + OutstandingSurveys.class.getCanonicalName()
                + " SET deleted = 1 "
                + " WHERE id = :id", params
        );
        if (result != null && result > 0) {
            cancelFormRequestByOutstandingSurveysId(outstandingSurveyId, reason, sendMail, loggedUser);
        }
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void cancelFormRequestByOutstandingSurveysId(
            final Long outstandingSurveyId,
            final String reason,
            final Boolean sendMail,
            final ILoggedUser loggedUser
    ) {
        final IFormRequestDAO formRequestDAO = getBean(IFormRequestDAO.class);
        final List<Long> formRequestCancelResult = formRequestDAO.cancelFormRequestByOutstandingSurveysId(
                outstandingSurveyId,
                reason,
                sendMail,
                loggedUser
        );
        if (getLogger().isTraceEnabled()) {
            getLogger().trace(
                    "Cancelled form requests by outstanding survey id: {}",
                    formRequestCancelResult.stream().map(Object::toString).collect(Collectors.joining(", "))
            );
        }
    }

}
