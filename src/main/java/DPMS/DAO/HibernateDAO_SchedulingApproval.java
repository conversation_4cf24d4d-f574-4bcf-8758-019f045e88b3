package DPMS.DAO;

import DPMS.DAOInterface.ISchedulingApprovalDAO;
import DPMS.Mapping.SchedulingApproval;
import Framework.DAO.GenericDAOImpl;
import Framework.DAO.GenericSaveHandle;
import java.util.Date;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.LoggedUser;
import qms.device.entity.ServiceScheduleRef;
import qms.device.listeners.OnApprovedSchedule;
import qms.device.listeners.OnRejectedSchedule;

@Lazy
@Repository(value = "HibernateDAO_SchedulingApproval")
@Scope(value = "singleton")
public class HibernateDAO_SchedulingApproval extends GenericDAOImpl<SchedulingApproval, Long> implements ISchedulingApprovalDAO {


    private SchedulingApproval getAuthorizationDetails(final ServiceScheduleRef schedule, final LoggedUser loggedUser, final String comment) {
        SchedulingApproval approval = getSchedulingApproval(schedule);
        approval.setApprovedBy(loggedUser.getId());
        approval.setApprovedOn(new Date());
        approval.setComment(comment);
        return approval;
    }
    
    private SchedulingApproval getSchedulingApproval(final ServiceScheduleRef schedule) {
        SchedulingApproval approval = (SchedulingApproval) HQL_findSimpleObject(""
                + " SELECT ssa"
                + " FROM " + SchedulingApproval.class.getCanonicalName() + " ssa "
                + " WHERE ssa.status = " + SchedulingApproval.NEWLY_CREATED + " "
                + " AND ssa.scheduleId = " + schedule.getId()
        );
        return approval;
    }

    @Override
    @OnApprovedSchedule
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle approveSchedule(final ServiceScheduleRef schedule, final String comment, final LoggedUser loggedUser) {
        SchedulingApproval approval = getAuthorizationDetails(schedule, loggedUser, comment);
        approval.setStatus(SchedulingApproval.APPROVED);
        approval = makePersistent(approval, loggedUser.getId());
        if (approval != null) {
            return successGSH();
        }
        return failGSH();
    }

    @Override
    @OnRejectedSchedule
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GenericSaveHandle rejectSchedule(final ServiceScheduleRef schedule, final String comment, final LoggedUser loggedUser) {
        SchedulingApproval approval = getAuthorizationDetails(schedule, loggedUser, comment);
        approval.setStatus(SchedulingApproval.REJECTED);
        approval = makePersistent(approval, loggedUser.getId());
        if (approval != null) {
            return successGSH();
        }
        return failGSH();
    }
    
    
}
