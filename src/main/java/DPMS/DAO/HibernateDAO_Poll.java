package DPMS.DAO;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IPollDAO;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Poll;
import DPMS.Mapping.PollLite;
import DPMS.Mapping.PollRespondent;
import Framework.Config.GridColumn;
import Framework.Config.Language;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericDAOImpl;
import bnext.exception.GenericSaveException;
import bnext.reference.UserRef;
import isoblock.surveys.dao.hibernate.OutstandingSurveys;
import isoblock.surveys.dao.hibernate.Survey;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import mx.bnext.core.util.GridInfo;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.custom.DAOInterface.IDynamicTableDAO;
import qms.custom.core.DynamicTableHelper;
import qms.form.entity.SurveyData;
import qms.form.util.SurveyUtil;
import qms.form.util.SurveyUtilCache;
import qms.framework.util.CacheRegion;
import qms.poll.core.IPoll;
import qms.poll.dto.PollRespondantDTO;
import qms.poll.listeners.OnClosePoll;
import qms.poll.listeners.OnSavePlannedPoll;
import qms.poll.listeners.OnSavePollInProcess;
import qms.poll.listeners.OnStartPoll;
import qms.util.BindUtil;
import qms.util.MetadataSqlException;
import qms.util.QMSException;

/**
 *
 * <AUTHOR> Garza Verastegui
 */
@Lazy
@Repository(value = "HibernateDAO_Poll")
@Scope(value = "singleton")
@Language(module = "Framework.Config.Lang.DAO.HibernateDAO_Poll")
public class HibernateDAO_Poll extends GenericDAOImpl<Poll, Long> implements IPollDAO {

    private static final String CHANGE_STATUS_PLANNED_TO_PROCESS = ""
            + " FROM " + PollLite.class.getCanonicalName() + " poll "
            + " WHERE poll.status = " + Poll.STATUS_PLANNED
            + " AND :today between poll.dteStart AND poll.dteEnd ";

    private static final String CHANGE_STATUS_PROCESS_TO_DONE = ""
            + " FROM " + PollLite.class.getCanonicalName() + " poll "
            + " WHERE poll.status = " + Poll.STATUS_IN_PROCESS
            + "   AND :today > poll.dteEnd ";

    @Override
    @OnClosePoll
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PollLite closePoll(final PollLite poll, final ILoggedUser admin) {
        getLogger().trace("Encuesta se CIERRA, id: '{}'", poll.getId());
        poll.setStatus(Poll.STATUS_DONE);
        HQL_updateByQuery(""
                + " UPDATE  " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " SET c.status = " + OutstandingSurveys.STATUS.UNFINISHED.getValue()
                + " WHERE EXISTS ("
                        + " SELECT 1"
                        + " FROM " + PollRespondent.class.getCanonicalName() + " pr"
                        + " WHERE pr.outstandingSurveysId = c.id"
                        + " AND pr.id.pollId = :pollId "
                + " )"
                + " AND c.status IN ("
                    + OutstandingSurveys.STATUS.PROGRAMED.getValue() + ","
                    + OutstandingSurveys.STATUS.IN_PROGRESS.getValue() + ","
                    + OutstandingSurveys.STATUS.IN_PROGRESS_FILLED_PARCIALLY.getValue() + ","
                    + OutstandingSurveys.STATUS.IN_PROGRESS_FILL_LATER.getValue() 
                + ")",
                "pollId",
                poll.getId()
        );
        return makePersistent(poll, admin.getId());
            }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<Long> getOutstandingSurveyIds(final Long pollId) {
        final String hql = ""
                + " SELECT osl.outstandingSurveysId "
                + " FROM " + PollRespondent.class.getCanonicalName() + " osl "
                + " WHERE osl.id.pollId =:pollId";
        return HQL_findByQuery(hql, "pollId", pollId);
        }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<PollLite> getPollsInProcess() {
        final Date today = Framework.Config.Utilities.truncDate(new Date());
        return HQL_findByQuery(CHANGE_STATUS_PROCESS_TO_DONE, "today", today);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<PollLite> getPollsPlanned() {
        final Date today = Framework.Config.Utilities.truncDate(new Date());
        return HQL_findByQuery(CHANGE_STATUS_PLANNED_TO_PROCESS, "today", today);
        }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    @OnSavePlannedPoll
    public PollLite savePlannedPoll(final Poll poll, final boolean isNewObject, final LoggedUser loggedUser) throws QMSException {
        poll.setStatus(Poll.STATUS_PLANNED);
        return savePoll(poll, isNewObject, loggedUser);
    }

    @Override
    @OnSavePollInProcess
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PollLite savePollInProcess(final Poll poll, final boolean isNewObject, final LoggedUser loggedUser) throws QMSException {
        lockSurvey(poll.getSurveyId(), loggedUser.getId());
        poll.setStatus(Poll.STATUS_IN_PROCESS);
        final PollLite savedPoll = savePoll(poll, isNewObject, loggedUser);
        generateAnswerTable(poll.getSurveyId(), loggedUser);
        return savedPoll;
    }

    @Override
    @OnStartPoll
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public PollLite startPoll(final PollLite poll, final ILoggedUser loggedUser) throws QMSException {
        getLogger().trace("Encuesta en PROCESO, id: '{}'", poll.getId());
        lockSurvey(poll.getSurveyId(), loggedUser.getId());
        poll.setStatus(Poll.STATUS_IN_PROCESS);
        final PollLite savedPoll = makePersistent(poll, loggedUser.getId());
        HQL_updateByQuery(""
                + " UPDATE  " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " SET c.status = " + OutstandingSurveys.ESTATUS_EN_PROCESO
                + " WHERE EXISTS ("
                    + " SELECT 1"
                    + " FROM " + PollRespondent.class.getCanonicalName() + " pr"
                    + " WHERE pr.outstandingSurveysId = c.id"
                    + " AND pr.id.pollId = :pollId "
                + " )",
                "pollId",
                poll.getId()
        );
        generateAnswerTable(poll.getSurveyId(), loggedUser);
        return savedPoll;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<Map<String, Object>> getAnswerRows(final SortedPagedFilter filter, final Long pollId) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            final String defaultOrderColumn = "o#outstanding_surveys_id";
            filter.getColumns().put(defaultOrderColumn, new GridColumn(defaultOrderColumn));
            filter.getField().setOrderBy(defaultOrderColumn);
            filter.setDirection(Byte.parseByte("2"));
        }
        final String answersTable = getAnswerstable(pollId);
        if (answersTable == null || answersTable.isEmpty()) {
            getLogger().error(" Table answers does not exists for poll with id {}.", pollId);
            return Utilities.EMPTY_GRID_INFO;
        }
        final String uptatableColumnName = DynamicTableHelper.getReferenceIdName(this, answersTable, "outstanding_surveys");
        return SQL_getRowsByQuery(""
                + " FROM " + answersTable + " c"
                + " JOIN poll_respondent r ON r.outstanding_surveys_id = c." + uptatableColumnName
                + " JOIN outstanding_surveys o ON o.outstanding_surveys_id = r.outstanding_surveys_id"
                + " JOIN users u ON u.user_id = r.respondent_id", filter);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public GridInfo<SurveyData> getMetadataRows(final SortedPagedFilter filter, final Long pollId) {
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("c.fieldOrder, c.fieldCode");
        }
        filter.getCriteria().put("<condition>", "EXISTS("
                + " SELECT 1"
                + " FROM " + Poll.class.getCanonicalName() + " p"
                + " WHERE p.id = " + pollId
                + " AND p.surveyId = c.surveyId"
        + ")");
        return HQL_getRowsByQuery(""
                + " SELECT c"
                + " FROM " + SurveyData.class.getCanonicalName() + " c ", 
                filter,
                true,
                CacheRegion.SURVEY,
                0
        );
    }
    
    
    private String getAnswerstable(final Long pollId) {
        return HQL_findSimpleString(""
            + " SELECT c.survey.answersTable"
            + " FROM " + Poll.class.getCanonicalName() + " c "
            + " WHERE c.id = :id",
            "id", pollId
        );
    }

    private PollLite savePoll(final Poll poll, final boolean isNewObject, final LoggedUser loggedUser) throws QMSException {
        final PollLite savedPoll;
        if (isNewObject) {
            savedPoll = persistNewPoll(poll, loggedUser.getId());
        } else {
            savedPoll = updatePoll(poll, loggedUser.getId());
    }
        return savedPoll;
    }
    
    private PollLite persistNewPoll(final Poll poll, final Long loggedUserId) throws QMSException {
        final String code = getNewPollCode();
        poll.setCode(code);
        makePersistent(poll, loggedUserId);
        getEntityManager().flush();
        final Set<Long> newRespondants = getRespondantsIds(poll.getRespondents());
        final Survey survey = new Survey(poll.getSurveyId());
        newRespondants.forEach(respondant -> updateNewRespondant(survey, poll, respondant, loggedUserId));
        return HQLT_findById(PollLite.class, poll.getId());
    }
    
    private void updateOutstandingSurveys(final IPoll poll, final PollRespondantDTO respondant) {
        final Short outstandingSurveyStatus;
        if (Poll.STATUS_IN_PROCESS.equals(poll.getStatus())) {
            outstandingSurveyStatus = OutstandingSurveys.ESTATUS_EN_PROCESO;
        } else {
            outstandingSurveyStatus = OutstandingSurveys.ESTATUS_PROGRAMADA;
        }
        final Map<String, Object> params = new HashMap<>(2);
        params.put("id", respondant.getOutstandingSurveyId());
        HQL_updateByQuery(""
                + " UPDATE  " + OutstandingSurveys.class.getCanonicalName() + " c "
                + " SET c.status = " + outstandingSurveyStatus
                + " WHERE c.id = :id ",
                params
                );
                    }

    private PollLite updatePoll(final Poll poll, final Long loggedUserId) {
        final Set<Long> newRespondants = getNewRespondants(poll, poll.getRespondents());
        final Set<PollRespondantDTO> sameRespondants = loadRespondants(poll, poll.getRespondents());
        final Poll savedPoll = makePersistent(poll, loggedUserId);
        getEntityManager().flush();
        final Survey survey = new Survey(savedPoll.getSurveyId());
        newRespondants.forEach(respondant -> updateNewRespondant(survey, savedPoll, respondant, loggedUserId));
        sameRespondants.forEach(respondant -> updateRespondant(savedPoll, respondant));
        return HQLT_findById(PollLite.class, savedPoll.getId());
    }

    private void generateAnswerTable(final Long surveyId, final ILoggedUser loggedUser) throws QMSException {
        try {
            getEntityManager().flush();
            SurveyUtil.freeze(surveyId, this);
            final SurveyUtilCache cache = new SurveyUtilCache();
            final IDynamicTableDAO tableDAO = getBean(IDynamicTableDAO.class);
            SurveyUtil.getFactoryBuilded(surveyId, cache, tableDAO, loggedUser);
        } catch (final MetadataSqlException ex) {
            getLogger().error("Table answers not created for survey with id {}", surveyId, ex);
        }
    }

    private void updateNewRespondant(
            final Survey survey,
            final IPoll poll,
            final Long respondant,
            final Long loggedUserId
    ) {
        final OutstandingSurveys outstandingSurveys = new OutstandingSurveys(-1L);
        outstandingSurveys.setCuestionario(survey);
        outstandingSurveys.setCreatedBy(loggedUserId);
        outstandingSurveys.setCreatedDate(new Date());
        outstandingSurveys.setLastModifiedBy(loggedUserId);
        outstandingSurveys.setLastModifiedDate(new Date());
        if (Poll.STATUS_IN_PROCESS.equals(poll.getStatus())) {
            outstandingSurveys.setEstatus(OutstandingSurveys.ESTATUS_EN_PROCESO);
        } else {
            outstandingSurveys.setEstatus(OutstandingSurveys.ESTATUS_PROGRAMADA);
        }
        final OutstandingSurveys savedOutstandingSurvey = makePersistent(outstandingSurveys, loggedUserId);
        getEntityManager().flush();
        final Map<String, Object> params = new HashMap<>(2);
        params.put("pollId", poll.getId());
        params.put("outstandingSurveysId", savedOutstandingSurvey.getId());
        params.put("respondentId", respondant);
        final Integer count = HQL_updateByQuery(""
                + " UPDATE  " + PollRespondent.class.getCanonicalName() + " pr "
                + " SET "
                + " pr.outstandingSurveysId = :outstandingSurveysId,"
                + " pr.deleted = 0"
                + " WHERE pr.id.pollId = :pollId "
                + " AND pr.id.respondentId = :respondentId",
                params
        );
        if (!count.equals(1)) {
            final String message = getTag("saveRepondantsFailed");
            throw new GenericSaveException(message);
        }
    }
    
    private void updateRespondant(final IPoll poll, final PollRespondantDTO respondant) {
        final Map<String, Object> params = new HashMap<>(2);
        params.put("pollId", poll.getId());
        params.put("outstandingSurveysId", respondant.getOutstandingSurveyId());
        params.put("respondentId", respondant.getRespondantId());
        final Integer count = HQL_updateByQuery(""
                + " UPDATE  " + PollRespondent.class.getCanonicalName() + " pr "
                + " SET "
                + " pr.outstandingSurveysId = :outstandingSurveysId"
                + " WHERE pr.id.pollId = :pollId "
                + " AND pr.id.respondentId = :respondentId",
                params
        );
        if (!count.equals(1)) {
            final String message = getTag("saveRepondantsFailed");
            throw new GenericSaveException(message);
        }
        updateOutstandingSurveys(poll, respondant);
    }
    
    private String getNewPollCode() throws QMSException {
        final ICodeSequenceDAO codeDao = (ICodeSequenceDAO) Utilities.getBean("CodeSequence");
        return "POLL-" + Utilities.formatDateBy(new Date(), "yy") + codeDao.next(CodeSequence.type.CODE_POLL);
}

    private Set<Long> getRespondantsIds(final Set<UserRef> respondents) {
        return respondents.stream().map(respondent -> respondent.getId()).collect(Collectors.toCollection(LinkedHashSet::new));
    }

    private Set<Long> getNewRespondants(final IPoll poll, final Set<UserRef> toSaveRespondants) {
        final List<Long> toSaveIds = toSaveRespondants.stream()
                .map(respondant -> respondant.getId())
                .collect(Collectors.toList());
        final List<Long> results = HQL_findByQuery(""
                + " SELECT u.id"
                + " FROM " + UserRef.class.getCanonicalName() + " u"
                + " WHERE " + BindUtil.parseFilterList("u.id", toSaveIds)
                + " AND NOT EXISTS ("
                        + " SELECT 1"
                        + " FROM " + PollRespondent.class.getCanonicalName() + " c"
                        + " WHERE c.id.pollId = :pollId"
                        + " AND c.id.respondentId = u.id"
                + ")",
                "pollId",
                poll.getId()
        );
        if (results == null || results.isEmpty()) {
            return new HashSet<>(0);
        }
        return new HashSet<>(results);
    }

    private Set<PollRespondantDTO> loadRespondants(final IPoll poll, final Set<UserRef> toSaveRespondants) {
        final List<Long> toSaveIds = toSaveRespondants.stream()
                .map(respondant -> respondant.getId())
                .collect(Collectors.toList());
        final List<PollRespondantDTO> results = HQL_findByQuery(""
                + " SELECT new " + PollRespondantDTO.class.getCanonicalName() + " ("
                    + " c.outstandingSurveysId,"
                    + " c.id.respondentId"
                + " )"
                + " FROM " + PollRespondent.class.getCanonicalName() + " c"
                + " WHERE c.id.pollId = :pollId"
                + " AND " + BindUtil.parseFilterList("c.id.respondentId", toSaveIds),
                "pollId",
                poll.getId()
        );
        if (results == null || results.isEmpty()) {
            return new HashSet<>(0);
        }
        return new HashSet<>(results);
    }

    private void lockSurvey(final Long surveyId, final Long adminId) {
        final Survey Survey = HQLT_findById(Survey.class, surveyId, true, CacheRegion.SURVEY, Utilities.getSettings().getConnQueryTimeout());
        Survey.setEstatus(Survey.ESTATUS_BLOQUEADO);
        makePersistent(Survey, adminId);
    }
}
