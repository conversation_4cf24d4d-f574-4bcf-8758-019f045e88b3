package DPMS.Audit;

import DPMS.Mapping.AuditIndividual;
import DPMS.Mapping.AuditIndividualList;
import Framework.Config.SortedPagedFilter;
import Framework.DAO.CRUD_Generic;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;

/**
 *
 * <AUTHOR>
 */
public class CRUD_AuditIndividualList extends CRUD_Generic<AuditIndividualList> {

    @Override
    public String smd() {
      if (!isAuditAccess()) {
        return NONE;//<---- Si no ningun acceso al modulo no hace nada
      }
      return SUCCESS;
    }

    private static final String MY_AUDITS_FILTER = ""
            + " ("
                + " c.liderId = :userId"        //auditor lider
                + " OR exists ("              //auditor de apoyo
                    + " SELECT h.id "
                    + " FROM " + AuditIndividual.class.getCanonicalName() + " xh"
                    + " JOIN xh.helpers h"
                    + " WHERE xh.id = c.id"
                    + " AND h.id = :userId"
                + " ) "
                + " OR ("                       //auditado por AREA o PROCESO (Esta encapsulado en la vista)
                    + " c.status != " + AuditIndividual.STATUS.PLANNED + " "
                    + " AND c.auditedId = :userId"
                + " )"
            + " ) "
            + " AND c.deleted = " + AuditIndividual.IS_NOT_DELETED;
    
    private static final String MY_AUDITS_CONDITION_EXTRA = " AND days(-c.anticipation, c.dteStart) <= CURRENT_DATE()";
    /**
     * El tipo de dato gridInfo<?> es "DPMS.Mapping.AuditIndividual" y viene configurado en struts
     *
     * @param filter
     * @return
     */
    @SMDMethod
    public GridInfo<Map<String, Object>> getMyAudits(SortedPagedFilter filter) {
        String cEntity = firstParamCurrentEntityId();
        String extraCondition = "";
        if("ToDo".equals(cEntity)){
            extraCondition = MY_AUDITS_CONDITION_EXTRA;
        }
        filter.getCriteria().put("<condition>", MY_AUDITS_FILTER.replaceAll(":userId", getLoggedUserId().toString()) + extraCondition);
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("id");
            filter.setDirection(Byte.parseByte("2"));
        }
        return getUntypedDAO().HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id"
                    + ", c.status AS status"
                    + ", c.fillStatus AS fillStatus"
                    + ", c.code AS code"
                    + ", c.auditTypeDescription AS auditTypeDescription"
                    + ", c.departmentProcessDescription AS departmentProcessDescription"
                    + ", c.businessUnitDepartmentDescription AS businessUnitDepartmentDescription"
                    + ", c.areaDescription AS areaDescription"
                    + ", c.auditTeamLeader AS auditTeamLeader"
                    + ", c.lider AS lider"
                    + ", c.audited AS audited"
                    + ", c.dteStart AS dteStart"
                    + ", c.actualStart AS actualStart"
                    + ", c.dteEnd AS dteEnd"
                    + ", c.actualEnd AS actualEnd"
                    + ", c.liderId AS liderId"
                    + ", c.helpersNames AS helpersNames"
                    + ", c.helpersIds AS helpersIds"
                    + ", c.auditedId AS auditedId"
                    + ", c.dteStartRequest AS dteStartRequest"
                + " )"
                + " FROM " + AuditIndividualList.class.getCanonicalName() + " c", 
                filter
        );
    }
    

    /**
     * El tipo de dato gridInfo<?> es "DPMS.Mapping.AuditIndividual" y viene configurado en struts
     *
     * @param filter
     * @return
     */
    @SMDMethod
    @Override
    public GridInfo getRows(SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
                + (getCurrentEntityId().length > 0
                ? "c.audit.id = " + getCurrentEntityId()[0] : "1=0"));
        if(!filter.getCriteria().containsKey("deleted")){
            filter.getCriteria().put("deleted", "0");
        }
        return super.getRows(filter);
    }
}
