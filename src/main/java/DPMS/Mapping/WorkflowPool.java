package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Objects;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import qms.framework.entity.Owner;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "workflow_pool")
public class WorkflowPool extends DomainObject implements Serializable {

    public final static Integer AND_TYPE = 0;
    public final static Integer OR_TYPE = 1;
    private final static long serialVersionUID = 5060153741975618684L;
    
    private Integer deleted = 0;
    private Integer type;
    private Set<Owner> owners;
    private Integer delay;
    private Integer index;              //Mala praxis, "index" es palabra reservada.
    private Integer workflowPoolIndex;  //Para consultas utilizar este en lugar de "index"
    
    private Long workflowId;
    private Workflow flujo; //updatable = false, insertable = false

    public WorkflowPool() {
    }

    public WorkflowPool(Long id) {
        this.id = id;
    }

    public WorkflowPool(Long id, Integer type) {
        this.id = id;
        this.type = type;
    }


    @Id
    @Basic(optional = false)
    @Column(name = "workflow_pool_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
    
    @Basic(optional = false)
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
        name = "workflow_pool_owner", 
        joinColumns = @JoinColumn(name = "workflow_pool_id"), 
        inverseJoinColumns = @JoinColumn(name = "owner_id")
    )
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<Owner> getOwners() {
        return this.owners;
    }

    public void setOwners(Set<Owner> owners) {
        this.owners = owners;
    }
    
    //@JsonIgnore
    //@JoinColumn(name = "workflow_id", insertable = false, updatable = false)

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "workflow_id", referencedColumnName = "workflow_id", insertable = false, updatable = false)
    public Workflow getFlujo() {
        return flujo;
    }

    public void setFlujo(Workflow flujo) {
        this.flujo = flujo;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 41 * hash + (this.id != null ? this.id.hashCode() : 0);
        hash = 41 * hash + (this.type != null ? this.type.hashCode() : 0);
        hash = 41 * hash + (this.delay != null ? this.delay.hashCode() : 0);
        hash = 41 * hash + (this.flujo != null ? this.flujo.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final WorkflowPool other = (WorkflowPool) obj;
        if (this.id == null && other.id == null) {
            return true;
        }
        if (!Objects.equals(this.id, other.id) && (this.id == null || !this.id.equals(other.id))) {
            return false;
        }
        if (!Objects.equals(this.type, other.type) && (this.type == null || !this.type.equals(other.type))) {
            return false;
        }
        if (!Objects.equals(this.delay, other.delay) && (this.delay == null || !this.delay.equals(other.delay))) {
            return false;
        }
        if (this.flujo != other.flujo && (this.flujo == null || !this.flujo.equals(other.flujo))) {
            return false;
        }
        if (this.id == null || other.id == null) {
            return false;
        }
        return this.id != -1 && other.id != -1;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.WorkflowPool[ id=" + id + " ]";
    }

    @Basic(optional = false)
    @Column(name = "delay")
    public Integer getDelay() {
        return delay;
    }

    public void setDelay(Integer delay) {
        this.delay = delay;
    }

    @Column(name = "indice")
    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    @Column(name = "indice", insertable = false, updatable = false)
    public Integer getWorkflowPoolIndex() {
        return workflowPoolIndex;
    }

    public void setWorkflowPoolIndex(Integer workflowPoolIndex) {
        this.workflowPoolIndex = workflowPoolIndex;
    }

    @Column(name = "workflow_id")
    public Long getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(Long workflowId) {
        this.workflowId = workflowId;
    }
    
    
        
}
