package DPMS.Mapping;

import Framework.Config.DomainObject;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "department_process_view")
@Immutable
public class DepartmentProcessLoad extends DomainObject implements Serializable, IDepartmentProcess {   

    public static final Integer INACTIVE_STATUS = 0;
    public static final Integer ACTIVE_STATUS = 1;
    
    private static final long serialVersionUID = 1L;
    
    private Long departmentProcessId;
    private Long departmentId;
    private Long businessUnitId;
    private Long processId;
    private Long attendantId;
    private String title;
    private String description;
    private String code;
    private Integer status;
    private BusinessUnitDepartmentLite department;
    private UserRef attendant;
    private Integer budStatus;
    
    public DepartmentProcessLoad() {
    }

    public DepartmentProcessLoad(Long id) {
    }

    @JsonIgnore
    @Override
    @Column(name = "department_process_id", updatable = false, insertable = false)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @JsonIgnore
    @Id
    @Column(name = "department_process_id", updatable = false, insertable = false)
    public Long getDepartmentProcessId() {
        return departmentProcessId;
    }

    /**
     * Solo se utiliza para obtener el ID de businessUnit en un HQL
     *
     * @return
     */
    @JsonIgnore
    @OneToOne
    //@Column(updatable=false,insertable=false)
    @JoinColumn(referencedColumnName = "business_unit_department_id", name = "business_unit_department_id", insertable = false, updatable = false)
    public BusinessUnitDepartmentLite getDepartment() {
        /*BusinessUnitDepartmentLite t = new BusinessUnitDepartmentLite();
         t.setId(departmentId);
         t.setBusinessUnitId(businessUnitId);/**/
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentLite department) {
        this.department = department;
    }

    public void setDepartmentProcessId(Long departmentProcessId) {
        this.departmentProcessId = departmentProcessId;
    }

    @Column(name = "business_unit_department_id")
    public Long getDepartmentId() {
        return departmentId;
    }

    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    @Column(name = "process_id")
    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    @Column(name = "attendant_id")
    public Long getAttendantId() {
        return attendantId;
    }

    public void setAttendantId(Long attendantId) {
        this.attendantId = attendantId;
    }

    @Column(name = "title")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "title", insertable = false, updatable = false)
    @Override
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "attendant_id", insertable = false, updatable = false)
    public UserRef getAttendant() {
        return attendant;
    }

    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }
    
    @Column(name = "bud_status")
    public Integer getBudStatus() {
        return budStatus;
    }

    public void setBudStatus(Integer budStatus) {
        this.budStatus = budStatus;
    }
}
