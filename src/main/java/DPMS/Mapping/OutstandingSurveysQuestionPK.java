package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class OutstandingSurveysQuestionPK implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long questionId;
    private Long outstandingSurveysId;

    public OutstandingSurveysQuestionPK() {
    }

    public OutstandingSurveysQuestionPK(Long questionId, Long outstandingSurveysId) {
        this.questionId = questionId;
        this.outstandingSurveysId = outstandingSurveysId;
    }

    @Basic(optional = false)
    @Column(name = "question_id")
    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    @Basic(optional = false)
    @Column(name = "outstanding_surveys_id")
    public Long getOutstandingSurveysId() {
        return outstandingSurveysId;
    }

    public void setOutstandingSurveysId(Long outstandingSurveysId) {
        this.outstandingSurveysId = outstandingSurveysId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 29 * hash + Objects.hashCode(this.questionId);
        hash = 29 * hash + Objects.hashCode(this.outstandingSurveysId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final OutstandingSurveysQuestionPK other = (OutstandingSurveysQuestionPK) obj;
        if (!Objects.equals(this.questionId, other.questionId)) {
            return false;
        }
        return Objects.equals(this.outstandingSurveysId, other.outstandingSurveysId);
    }

    @Override
    public String toString() {
        return "OutstandingSurveysQuestionPK{" + "questionId=" + questionId + ", outstandingSurveysId=" + outstandingSurveysId + '}';
    }


}
