/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package DPMS.Mapping;

import Framework.Config.StandardEntity;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import qms.framework.util.CacheConstants;


/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Cacheable
@Cache(region = CacheConstants.SURVEY, usage = CacheConcurrencyStrategy.READ_WRITE)
@Table(name = "internal_doc_catalog_items")

public class InternalDocumentCatalogItem extends StandardEntity<InternalDocumentCatalogItem> implements Serializable {

    private static final long serialVersionUID = 1L;

    private String description;
    private Integer status;
    private Integer deleted;
    private String code;
    private InternalDocumentCatalog internalDocumentCatalog;

    public InternalDocumentCatalogItem() {
    }

    public InternalDocumentCatalogItem(Long internalDocumentCatalogItemsId) {
        this.id = internalDocumentCatalogItemsId;
    }

    public InternalDocumentCatalogItem(Long internalDocumentCatalogItemsId, String vchDescription, Integer intStatus, Integer intDeleted) {
        this.id = internalDocumentCatalogItemsId;
        this.description = vchDescription;
        this.status = intStatus;
        this.deleted = intDeleted;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "internal_doc_catalog_items_id")
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long internalDocumentCatalogItemsId) {
        this.id = internalDocumentCatalogItemsId;
    }

    @Basic(optional = false)
    @Column(name = "vch_description", length = 200)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String vchDescription) {
        this.description = vchDescription;
    }

    @Basic(optional = false)
    @Column(name = "int_status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer intStatus) {
        this.status = intStatus;
    }

    @Basic(optional = false)
    @Column(name = "int_deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer intDeleted) {
        this.deleted = intDeleted;
    }

    @Column(name = "vch_code")
    @Override
    public String getCode() {
        if (this.code == null || this.code.isEmpty()) {
            this.code = this.description;
        }
        return code;
    }

    @Override
    public void setCode(String code) {
        if(code == null || code.isEmpty()) {
            code = this.description;
        }
        this.code = code;
    }

    @JoinColumn(name = "internal_doc_catalog_id", referencedColumnName = "internal_document_catalog_id")
    @ManyToOne(fetch = FetchType.EAGER)
    public InternalDocumentCatalog getInternalDocumentCatalog() {
        return internalDocumentCatalog;
    }

    public void setInternalDocumentCatalog(InternalDocumentCatalog internalDocumentCatalog) {
        this.internalDocumentCatalog = internalDocumentCatalog;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.InternalDocumentCatalogItem[ internalDocumentCatalogItemsId=" + id + " ]";
    }
    
}
