package DPMS.Mapping;

import Framework.Config.CatalogSuperClass;
import java.io.Serializable;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "("
        + " SELECT "
            + " ca.inttipoid,"
            + " ca.vchtexto,"
            + " ca.txtdescripcion,"
            + " ca.intestadoc,"
            + " ca.int_borrado,"
            + " ca.intvalor,"
            + " ca.intmoduloid,"
            + " ca.intretenible" 
        + " FROM tblcatalogo ca WHERE ca.intcatalogoid = 13"
        + ")")
@Immutable
public class StoragePlace extends CatalogSuperClass<StoragePlace> implements Serializable {
}
