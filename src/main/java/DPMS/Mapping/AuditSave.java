package DPMS.Mapping;

import DPMS.Mapping.Process;
import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import isoblock.surveys.dao.hibernate.SurveySimple;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.document.entity.DocumentLite;

/**
 *
 * <AUTHOR> Guadalupe Quintanilla Flores
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table (name="audits")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
public class AuditSave extends StandardEntity<AuditSave> implements DomainObjectInterface, IAuditableEntity {
    private static final long serialVersionUID = 1L;
    
    public static final Integer STATUS_PLANNED = 1;
    public static final Integer STATUS_IN_PROCESS = 2;
    public static final Integer STATUS_DONE = 3;
    public static final Integer STATUS_CANCELED = 4;
    
    private Short anticipation;
    private Date dteStart;
    private Date dteEnd;
    private Date dteAnticipation;
    private Date tmpStart;
    private Date tmpEnd;
    private UserRef author;
    private UserRef attendant;
    private SurveySimple survey;
    private BusinessUnitLite businessUnit;
    private Process process;
    private Double score;
    private AuditType type;
    private Long typeId;
    private Long buildingId;
    
    private Set<BusinessUnitDepartmentLite> departments;
    private Area area;
    private Building building;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Set<UserRef> helpers;
    private Date actualStart;
    private Date actualEnd;
    private String criteria;

    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    
    private Set<DocumentLite> documents;
    private Set<FilesLite> files;
    private String filesIds;
    private List<AuditsComment> comments;

    public AuditSave() {
    }

    public AuditSave(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "audit_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    

    @Basic(optional = false)
    @Column(nullable = false)
    public Short getAnticipation() {
        return anticipation;
    }

    public void setAnticipation(Short anticipation) {
        this.anticipation = anticipation;
    }

    @Basic(optional = false)
    @Column(name = "dte_start", nullable = false)
    @Temporal(TemporalType.DATE)
    public Date getDteStart() {
        return dteStart;
    }

    public void setDteStart(Date dteStart) {
        this.dteStart = dteStart;
    }

    @Basic(optional = false)
    @Column(name = "dte_end", nullable = false)
    @Temporal(TemporalType.DATE)
    public Date getDteEnd() {
        return dteEnd;
    }

    public void setDteEnd(Date dteEnd) {
        this.dteEnd = dteEnd;
    }

    @Basic(optional = false)
    @Column(name = "dte_anticipation", nullable = false)
    @Temporal(TemporalType.DATE)
    public Date getDteAnticipation() {
        return dteAnticipation;
    }

    public void setDteAnticipation(Date dteAnticipation) {
        this.dteAnticipation = dteAnticipation;
    }

    @Basic(optional = false)
    @Column(name = "tmp_start", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpStart() {
        return tmpStart;
    }

    public void setTmpStart(Date tmpStart) {
        this.tmpStart = tmpStart;
    }

    @Column(name = "building_id")
    public Long getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
    }

    @Basic(optional = false)
    @Column(name = "tmp_end", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getTmpEnd() {
        return tmpEnd;
    }

    public void setTmpEnd(Date tmpEnd) {
        this.tmpEnd = tmpEnd;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "author_id", referencedColumnName = "user_id")
    public UserRef getAuthor() {
        return author;
    }

    public void setAuthor(UserRef author) {
        this.author = author;
    }
    
    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "audit_type_id", referencedColumnName = "audit_type_id", insertable = false, updatable = false)
    public AuditType getType() {
        return type;
    }

    public void setType(AuditType type) {
        this.type = type;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "attendant_id", referencedColumnName = "user_id")
    public UserRef getAttendant() {
        return attendant;
    }

    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }

    @JsonIgnore
    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "survey_id", referencedColumnName = "survey_id")
    public SurveySimple getSurvey() {
        return survey;
    }

    public void setSurvey(SurveySimple survey) {
        this.survey = survey;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id")
    public BusinessUnitLite getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitLite businessUnit) {
        this.businessUnit = businessUnit;
    }

    @ManyToMany(fetch= FetchType.EAGER)
    @JoinTable(name = "audit_business_unit_department", 
            joinColumns = @JoinColumn(name = "audit_id", insertable = false, updatable = false),
            inverseJoinColumns = @JoinColumn(name = "business_unit_department_id", insertable = false, updatable = false))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<BusinessUnitDepartmentLite> getDepartments() {
        return departments;
    }

    public void setDepartments(Set<BusinessUnitDepartmentLite> departments) {
        this.departments = departments;
    }

    public void strDepartments(HashSet<BusinessUnitDepartmentLite> departments) {
        this.departments = departments;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "process_id", referencedColumnName = "process_id")
    public Process getProcess() {
        return process;
    }

    public void setProcess(Process process) {
        this.process = process;
    }

    @Column(name = "score")
    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    @Column(name = "audit_type_id")
    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof AuditSave)) {
            return false;
        }
        AuditSave other = (AuditSave) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.AuditSave[ id=" + id + " ]";
    }
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "area_id", referencedColumnName = "area_id")
    public Area getArea() {
        return area;
    }

    public void setArea(Area area) {
        this.area = area;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "building_id", referencedColumnName = "building_id", insertable = false, updatable = false)
    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }
    
    @Basic(optional = true)
    @ManyToMany(fetch= FetchType.EAGER)
    @JoinTable(name = "audit_helpers", joinColumns =
        @JoinColumn(name = "audit_id"), inverseJoinColumns =
        @JoinColumn(name = "helper_id"))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<UserRef> getHelpers() {
        return helpers;
    }

    public void setHelpers(Set<UserRef> helpers) {
        this.helpers = helpers;
    }

    /**
     * @return the actualStart
     */
    @Column(name = "actual_start")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getActualStart() {
        return actualStart;
    }

    /**
     * @param actualStart the actualStart to set
     */
    public void setActualStart(Date actualStart) {
        this.actualStart = actualStart;
    }

    /**
     * @return the actualEnd
     */
    @Column(name = "actual_end")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getActualEnd() {
        return actualEnd;
    }

    /**
     * @param actualEnd the actualEnd to set
     */
    public void setActualEnd(Date actualEnd) {
        this.actualEnd = actualEnd;
    }

    /**
     * @return the criteria
     */
    @Column(name = "criteria")
    public String getCriteria() {
        return criteria;
    }

    /**
     * @param criteria the criteria to set
     */
    public void setCriteria(String criteria) {
        this.criteria = criteria;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    
    //Esta propieda solo es usada para el tranporte de los datos para ser
    // guardada en AuditsDocument
    @Transient
    public Set<DocumentLite> getDocuments() {
        return documents;
    }

    public void setDocuments(Set<DocumentLite> documents) {
        this.documents = documents;
    }

    @Transient
    public String getFilesIds() {
        return filesIds;
    }

    public void setFilesIds(String filesIds) {
        this.filesIds = filesIds;
    }
    

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "audits_files", joinColumns = {
        @JoinColumn(name = "audit_id", referencedColumnName = "audit_id")},
        inverseJoinColumns = @JoinColumn(name = "file_id", referencedColumnName = "id"))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<FilesLite> getFiles() {
        return files;
    }

    public void setFiles(Set<FilesLite> files) {
        this.files = files;
    }
    
    @Transient
    public List<AuditsComment> getComments() {
        return comments;
}

    public void setComments(List<AuditsComment> comments) {
        this.comments = comments;
    }  
}
