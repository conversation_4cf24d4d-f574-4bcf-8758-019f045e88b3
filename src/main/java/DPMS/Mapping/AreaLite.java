package DPMS.Mapping;

import Framework.Config.DomainObject;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import org.hibernate.annotations.GenericGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "area")
public class AreaLite extends DomainObject implements Serializable{
    private static final long serialVersionUID = 1L;
    
    private String description;
    private Long attendantId;
    
    public AreaLite() {
    }
    public AreaLite(Long id, String description){
        this.id = id;
        this.description = description;
    }
    
    public AreaLite(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "area_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable=false)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Basic(optional = false)
    @Column(name = "attendant_id", nullable=false)
    public Long getAttendantId() {
        return attendantId;
    }

    public void setAttendantId(Long attendantId) {
        this.attendantId = attendantId;
    }    

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof AreaLite)) {
            return false;
        }
        AreaLite other = (AreaLite) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Area[ Id=" + id + " ]";
    }
    
}



