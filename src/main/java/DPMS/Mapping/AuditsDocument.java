/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "audits_document")
public class AuditsDocument extends CompositeStandardEntity<AuditsDocumentPK> implements ILinkedCompositeEntity<AuditsDocumentPK>, ILinkedComposityGrid<AuditsDocumentPK>, Serializable {

    private AuditsDocumentPK id;

    public AuditsDocument() {
    }

    public AuditsDocument(AuditsDocumentPK id) {
        this.id = id;
    }
    
    
    public AuditsDocument(Long auditId, Long documentId) {
        this.id = new AuditsDocumentPK(auditId, documentId);
    }
    
    @Override
    public AuditsDocumentPK identifuerValue() {
        return id;
    }

    @Override
    @EmbeddedId
    public AuditsDocumentPK getId() {
        return id;
    }

    @Override
    public void setId(AuditsDocumentPK id) {
        this.id = id;
    }

}
