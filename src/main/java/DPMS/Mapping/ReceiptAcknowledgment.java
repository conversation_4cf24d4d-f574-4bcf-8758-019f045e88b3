package DPMS.Mapping;

import Framework.Config.DomainObject;
import ape.pending.core.StrongBaseAPE;
import ape.pending.core.StrongEntityId;
import ape.pending.core.WeakEntityId;
import bnext.reference.UserRef;
import bnext.reference.document.DocumentPrintingRef;
import bnext.reference.document.FileRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "receipt_acknowledgment")
public class ReceiptAcknowledgment extends DomainObject implements IAuditableEntity, Serializable, StrongBaseAPE {

    private static final long serialVersionUID = 1L;
    public static final String PREFIX = "RECEIPT-ACK";

    public static enum STATUS implements IStatusEnum {
        //Determinado después de cambiar de version de documento
        NOT_DELIVERED(0, IStatusEnum.COLOR_CYAN),
        //Determinado después de realizar la impresión de la copia controlada
        DELIVERED(1, IStatusEnum.COLOR_GREEN),
        //Determinado después de realizar cambio de version de documento y no haber leido la version anterior
        OMITTED_BY_MODIFICATION(2, IStatusEnum.COLOR_GRAY), 
        //Determinado después de haber realizado una cancelación de documento
        OMITTED_BY_CANCELLATION(3, IStatusEnum.COLOR_BLACK),
        TO_PICK_UP(4, IStatusEnum.COLOR_YELLOW),
        COLLECTED(5, IStatusEnum.COLOR_BLUE),
        LOST(6, IStatusEnum.COLOR_PURPLE),
        //Determinado después de haber realizado al inactivar un usuario
        OMITTED_BY_INACTIVE(7, IStatusEnum.COLOR_ORANGE),
        //Recolección cancelada
        PICK_UP_CANCELLED(8, IStatusEnum.COLOR_LOCK),
        //Entrega cancelada
        DELIVER_CANCELLED(9, IStatusEnum.COLOR_LOCK);

        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        
        @Override
        public Integer getValue() {
            return this.value;
        }
        
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        
        @Override
        public IStatusEnum getActiveStatus() {
            return NOT_DELIVERED;
        } 
        
        public static STATUS getStatus(Integer status) {
            for(STATUS s : values()) {
                if (Objects.equals(s.getValue(), status)) {
                    return s;
                }
            }
            return null;
        }
    }

    private Integer deleted;
    private Integer status;
    private String code;
    private String description;
    private Long documentId;
    private String documentCode;
    private String documentVersion;
    private Long fileId;
    private Integer filePages;
    private Long readerId;
    private Long positionId;
    private Long printBy;
    private Date printDate;
    private Date positionAssignedDate;
    private Integer createdTimes = 1;
    private Long documentPrintingId;
    private DocumentPrintingRef documentPrinting;
    private UserPosition reader;
    private UserRef readerUser;
    private PositionSimple position;
    private DocumentSimple document;
    private UserSimple createdByUser;
    private UserSimple userPrint;
    private Long collectedBy;
    private Date collectedAt;
    private Long reportedLostBy;
    private Date reportedLostAt;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private FileRef fileContent;
    private String cancellationReason;

    public ReceiptAcknowledgment() {
    }

    public ReceiptAcknowledgment(Long id) {
        this.id = id;
    }

    public ReceiptAcknowledgment(Long readerId, Long positionId) {
        this.readerId = readerId;
        this.positionId = positionId;
    }

    @Id
    @Override
    @Basic(optional = false)
    @Column(name = "id", precision = 19, scale = 0)
    @GenericGenerator(name = "increment", strategy = "increment")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "increment")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @StrongEntityId
    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Column(name = "document_code")
    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Column(name = "document_version")
    public String getDocumentVersion() {
        return documentVersion;
    }

    public void setDocumentVersion(String documentVersion) {
        this.documentVersion = documentVersion;
    }

    @Column(name = "file_id", nullable = false)
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @Column(name = "file_pages", nullable = false)
    public Integer getFilePages() {
        return filePages;
    }

    public void setFilePages(Integer filePages) {
        this.filePages = filePages;
    }

    @WeakEntityId
    @Column(name = "reader_id")
    public Long getReaderId() {
        return readerId;
    }

    public void setReaderId(Long readerId) {
        this.readerId = readerId;
    }

    @Column(name = "position_id")
    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    @Column(name = "print_by")
    public Long getPrintBy() {
        return printBy;
    }

    public void setPrintBy(Long printBy) {
        this.printBy = printBy;
    }

    @ManyToOne(optional = true, fetch = FetchType.EAGER)
    @JoinColumns({
        @JoinColumn(name = "print_by", referencedColumnName = "user_id", insertable = false, updatable = false)})
    public UserSimple getUserPrint() {
        return userPrint;
    }

    public void setUserPrint(UserSimple userPrint) {
        this.userPrint = userPrint;
    }

    @Column(name = "print_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getPrintDate() {
        return printDate;
    }

    public void setPrintDate(Date printDate) {
        this.printDate = printDate;
    }

    @Column(name = "ph_assigned_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getPositionAssignedDate() {
        return positionAssignedDate;
    }

    public void setPositionAssignedDate(Date positionAssignedDate) {
        this.positionAssignedDate = positionAssignedDate;
    }

    @Column(name = "document_printing_id")
    public Long getDocumentPrintingId() {
        return documentPrintingId;
    }

    public void setDocumentPrintingId(Long documentPrintingId) {
        this.documentPrintingId = documentPrintingId;
    }

    @Column(name = "created_times")
    public Integer getCreatedTimes() {
        return createdTimes;
    }

    public void setCreatedTimes(Integer createdTimes) {
        this.createdTimes = createdTimes;
    }

    @JsonIgnore(true)
    @OneToOne(optional = true, fetch = FetchType.EAGER)
    @JoinColumns({
        @JoinColumn(name = "reader_id", referencedColumnName = "user_id", insertable = false, updatable = false)
        ,
    @JoinColumn(name = "position_id", referencedColumnName = "position_id", insertable = false, updatable = false)
    })
    public UserPosition getReader() {
        return reader;
    }

    public void setReader(UserPosition reader) {
        this.reader = reader;
    }
    
    @JsonIgnore(true)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "reader_id", insertable = false, updatable = false)
    public UserRef getReaderUser() {
        return readerUser;
    }

    public void setReaderUser(UserRef readerUser) {
        this.readerUser = readerUser;
    }

    @JsonIgnore(true)
    @OneToOne(optional = true, fetch = FetchType.EAGER)
    @JoinColumns({
        @JoinColumn(name = "document_id", referencedColumnName = "id", insertable = false, updatable = false)
    })
    public DocumentSimple getDocument() {
        return document;
    }

    public void setDocument(DocumentSimple document) {
        this.document = document;
    }
    
    @ManyToOne
    @JoinColumns({
        @JoinColumn(name = "created_by", referencedColumnName = "user_id", insertable = false, updatable = false)})
    public UserSimple getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(UserSimple createdByUser) {
        this.createdByUser = createdByUser;
    }

    @OneToOne(optional = true, fetch = FetchType.EAGER)
    @JoinColumn(name = "position_id", referencedColumnName = "puesto_id", insertable = false, updatable = false)
    public PositionSimple getPosition() {
        return position;
    }

    public void setPosition(PositionSimple position) {
        this.position = position;
    }

    @Column(name = "collected_by")
    public Long getCollectedBy() {
        return collectedBy;
    }

    public void setCollectedBy(Long collectedBy) {
        this.collectedBy = collectedBy;
    }

    @Column(name = "collected_at")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCollectedAt() {
        return collectedAt;
    }

    public void setCollectedAt(Date collectedAt) {
        this.collectedAt = collectedAt;
    }

    @Column(name = "reported_lost_by")
    public Long getReportedLostBy() {
        return reportedLostBy;
    }

    public void setReportedLostBy(Long reportedLostBy) {
        this.reportedLostBy = reportedLostBy;
    }

    @Column(name = "reported_lost_at")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getReportedLostAt() {
        return reportedLostAt;
    }

    public void setReportedLostAt(Date reportedLostAt) {
        this.reportedLostAt = reportedLostAt;
    }

    @Override
    @Column(name = "document_code", updatable = false, insertable = false)
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @JoinColumn(name = "document_printing_id", referencedColumnName = "id", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    public DocumentPrintingRef getDocumentPrinting() {
        return documentPrinting;
    }


    public void setDocumentPrinting(DocumentPrintingRef documentPrinting) {
        this.documentPrinting = documentPrinting;
    }

    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date dt) {
        this.createdDate = dt;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date dt) {
        this.lastModifiedDate = dt;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }   
   
    @JoinColumn(name = "file_id", referencedColumnName = "id", updatable = false, insertable = false)
    @ManyToOne
    public FileRef getFileContent() {
        return fileContent;
}

    public void setFileContent(FileRef fileContent) {
        this.fileContent = fileContent;
    }

    @Column(name = "cancellation_reason")
    public String getCancellationReason() {
        return cancellationReason;
    }

    public void setCancellationReason(String cancellationReason) {
        this.cancellationReason = cancellationReason;
    }

}
