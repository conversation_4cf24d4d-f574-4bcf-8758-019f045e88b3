package DPMS.Mapping;

import Framework.Config.DomainObject;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblusuario")
@Immutable
public class User_old extends DomainObject  implements Serializable, IUser {
    private static final long serialVersionUID = 1L;
    
    
    private String key;
    private String name;
    private String account;
    private String password;
    private String mail;
    private Integer auditRole;
    private Integer actionRole;
    private Integer repositoryRole;
    private Integer meetingRole;
    private Integer administrationRole;
    private Integer documentRole;
    private Integer projectRole;
    private Integer status;
    private Integer indicatorRole;
    private String description;
    private String language;
    private Integer complaintRole;
    private Integer gridSize;
    private Integer detailGridSize;
    private Integer floatingGridSize;
    private String documentPermits;
    private String complaintPermits;
    private Integer root;

    public User_old() {
    }

    public User_old(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intusuarioid", nullable = false)
    @GeneratedValue(generator="increment",strategy=GenerationType.SEQUENCE)
    @GenericGenerator(name="increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vchclave", nullable = false, length = 255)
    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @Basic(optional = false)
    @Column(name = "vchnombrecompleto", nullable = false, length = 255)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic(optional = false)
    @Column(name = "vchnombre", nullable = false, length = 255)
    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    @JsonIgnore
    @Basic(optional = false)
    @Column(name = "txtcontrasena", nullable = false, length = **********)
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Basic(optional = false)
    @Column(name = "vchcorreo", nullable = false, length = 255)
    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    @Basic(optional = false)
    @Column(name = "introlauditorias", nullable = false)
    public Integer getAuditRole() {
        return auditRole;
    }

    public void setAuditRole(Integer auditRole) {
        this.auditRole = auditRole;
    }

    @Basic(optional = false)
    @Column(name = "introlacciones", nullable = false)
    public Integer getActionRole() {
        return actionRole;
    }

    public void setactionRole(Integer actionRole) {
        this.actionRole = actionRole;
    }

    @Basic(optional = false)
    @Column(name = "introlrepositorios", nullable = false)
    public Integer getRepositoryRole() {
        return repositoryRole;
    }

    public void setRepositoryRole(Integer repositoryRole) {
        this.repositoryRole = repositoryRole;
    }

    @Basic(optional = false)
    @Column(name = "introlreuniones", nullable = false)
    public Integer getMeetingRole() {
        return meetingRole;
    }

    public void setMeetingRole(Integer meetingRole) {
        this.meetingRole = meetingRole;
    }

    @Basic(optional = false)
    @Column(name = "introladministracion", nullable = false)
    public Integer getAdministrationRole() {
        return administrationRole;
    }

    public void setAdministrationRole(Integer administrationRole) {
        this.administrationRole = administrationRole;
    }

    @Basic(optional = false)
    @Column(name = "introldocumentos", nullable = false)
    public Integer getDocumentRole() {
        return documentRole;
    }

    public void setDocumentRole(Integer documentRole) {
        this.documentRole = documentRole;
    }

    @Column(name = "introlproyectos")
    public Integer getProjectRole() {
        return projectRole;
    }

    public void setProjectRole(Integer projectRole) {
        this.projectRole = projectRole;
    }
    @Column(name = "intstatususuario")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "introlindicadores")
    public Integer getIndicatorRole() {
        return indicatorRole;
    }

    public void setIndicatorRole(Integer indicatorRole) {
        this.indicatorRole = indicatorRole;
    }

    @Basic(optional = false)
    @Column(name = "txtdescripcion")
    public String getDescripcion() {
        return description;
    }

    public void setDescripcion(String descripcion) {
        this.description = descripcion;
    }
    
    @Basic(optional = false)
    @Column(name = "language")
    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    @Column(name = "introlquejas")
    public Integer getComplaintRole() {
        return complaintRole;
    }

    public void setComplaintRole(Integer complaintRole) {
        this.complaintRole = complaintRole;
    }
    
    @Column(name = "gridSize")
    public Integer getGridSize() {
        return gridSize;
    }

    public void setGridSize(Integer gridSize) {
        this.gridSize = gridSize;
    }

    @Column(name = "detailGridSize")
    public Integer getDetailGridSize() {
        return detailGridSize;
    }

    public void setDetailGridSize(Integer detailGridSize) {
        this.detailGridSize = detailGridSize;
    }

    @Column(name = "floatingGridSize")
    public Integer getFloatingGridSize() {
        return floatingGridSize;
    }

    public void setFloatingGridSize(Integer floatingGridSize) {
        this.floatingGridSize = floatingGridSize;
    }

    @Column(name = "vchpermisosdocumentos", length = 255)
    public String getDocumentPermits() {
        return documentPermits;
    }

    public void setDocumentPermits(String documentPermits) {
        this.documentPermits = documentPermits;
    }

    @Column(name = "vchpermisosquejas", length = **********)
    public String getComplaintPermits() {
        return complaintPermits;
    }

    public void setComplaintPermits(String complaintPermits) {
        this.complaintPermits = complaintPermits;
    }
    
    @Override
    @Basic(optional = false)
    @Column(name = "vchnombrecompleto", updatable = false, insertable = false)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "admin_general", updatable = false, insertable = false)
    public Integer getRoot() {
        return root;
    }

    public void setRoot(Integer root) {
        this.root = root;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }
    
    @Override
    public boolean equals(Object object) {
        //Req 3242
        if (!(object instanceof UserRef)
                && !(object instanceof User)
                && !(object instanceof UserSimple)
                && !(object instanceof User_old)) {
            return false;
        }
        IUser other = (IUser) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "DPMS.User[ Id=" + id + " ]";
    }

    
}
