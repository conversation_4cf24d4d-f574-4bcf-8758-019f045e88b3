package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR> G<PERSON>ndo
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "business_unit_settings")
public class BusinessUnitSettings extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long businessUnitId;
    private Integer daysToEscalate;
    //copias controladas
    private String headerTitle;
    private String subHeaderTitle;
    private String watermark;
    private Integer watermarkOpacity;
    private String subFooterTitle;
    private String footerTitle;
    private String headerSize;
    private String footerSize;
    private Integer news;
    //copias NO controladas
    private String headerTitleUncontrolled;
    private String subHeaderTitleUncontrolled;
    private String watermarkUncontrolled;
    private Integer watermarkOpacityUncontrolled;
    private String subFooterTitleUncontrolled;
    private String footerTitleUncontrolled;
    private String headerSizeUncontrolled;
    private String footerSizeUncontrolled;
    private BusinessUnit businessUnit;

    public BusinessUnitSettings() {
    }

    @Id
    @Basic
    @Override
    @Column(name = "id")
    @GenericGenerator(name = "increment", strategy = "increment")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "increment")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "business_unit_id", insertable = false, updatable = false, unique = true)
    public BusinessUnit getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnit businessUnit) {
        this.businessUnit = businessUnit;
    }

    @Column(name = "header_title", length = 255, nullable = false)
    public String getHeaderTitle() {
        return headerTitle;
    }

    public void setHeaderTitle(String headerTitle) {
        this.headerTitle = headerTitle;
    }

    @Column(name = "subheader_title", length = 255, nullable = false)
    public String getSubHeaderTitle() {
        return subHeaderTitle;
    }

    public void setSubHeaderTitle(String subHeaderTitle) {
        this.subHeaderTitle = subHeaderTitle;
    }

    @Column(name = "watermark_opacity")
    public Integer getWatermarkOpacity() {
        return watermarkOpacity;
    }

    public void setWatermarkOpacity(Integer watermarkOpacity) {
        this.watermarkOpacity = watermarkOpacity;
    }
    
    @Column(name = "news")
    public Integer getNews() {
        return news;
    }

    public void setNews(Integer news) {
        this.news = news;
    }

    @Column(name = "watermark_opacity_uncontrolled")
    public Integer getWatermarkOpacityUncontrolled() {
        return watermarkOpacityUncontrolled;
    }

    public void setWatermarkOpacityUncontrolled(Integer watermarkOpacityUncontrolled) {
        this.watermarkOpacityUncontrolled = watermarkOpacityUncontrolled;
    }

    @Column(name = "watermark", length = 255)
    public String getWatermark() {
        return watermark;
    }

    public void setWatermark(String watermark) {
        this.watermark = watermark;
    }

    @Column(name = "subfooter_title", length = 255)
    public String getSubFooterTitle() {
        return subFooterTitle;
    }

    public void setSubFooterTitle(String subFooterTitle) {
        this.subFooterTitle = subFooterTitle;
    }

    @Column(name = "footer_title", length = 255, nullable = false)
    public String getFooterTitle() {
        return footerTitle;
    }

    public void setFooterTitle(String footerTitle) {
        this.footerTitle = footerTitle;
    }

    @Column(name = "days_to_escalate", nullable = false)
    public Integer getDaysToEscalate() {
        return daysToEscalate;
    }

    public void setDaysToEscalate(Integer daysToEscalate) {
        this.daysToEscalate = daysToEscalate;
    }

    /**
     * @return the headerSize
     */
    @Column(name = "header_size")
    public String getHeaderSize() {
        return headerSize;
    }

    /**
     * @param headerSize the headerSize to set
     */
    public void setHeaderSize(String headerSize) {
        this.headerSize = headerSize;
    }

    /**
     * @return the footerSize
     */
    @Column(name = "footer_size")
    public String getFooterSize() {
        return footerSize;
    }

    /**
     * @param footerSize the footerSize to set
     */
    public void setFooterSize(String footerSize) {
        this.footerSize = footerSize;
    }

    @Column(name = "header_title_uncontrolled")
    public String getHeaderTitleUncontrolled() {
        return headerTitleUncontrolled;
    }

    public void setHeaderTitleUncontrolled(String headerTitleUncontrolled) {
        this.headerTitleUncontrolled = headerTitleUncontrolled;
    }

    @Column(name = "subheader_title_uncontrolled")
    public String getSubHeaderTitleUncontrolled() {
        return subHeaderTitleUncontrolled;
    }

    public void setSubHeaderTitleUncontrolled(String subHeaderTitleUncontrolled) {
        this.subHeaderTitleUncontrolled = subHeaderTitleUncontrolled;
    }

    @Column(name = "watermark_uncontrolled")
    public String getWatermarkUncontrolled() {
        return watermarkUncontrolled;
    }

    public void setWatermarkUncontrolled(String watermarkUncontrolled) {
        this.watermarkUncontrolled = watermarkUncontrolled;
    }

    @Column(name = "subfooter_title_uncontrolled")
    public String getSubFooterTitleUncontrolled() {
        return subFooterTitleUncontrolled;
    }

    public void setSubFooterTitleUncontrolled(String subFooterTitleUncontrolled) {
        this.subFooterTitleUncontrolled = subFooterTitleUncontrolled;
    }

    @Column(name = "footer_title_uncontrolled")
    public String getFooterTitleUncontrolled() {
        return footerTitleUncontrolled;
    }

    public void setFooterTitleUncontrolled(String footerTitleUncontrolled) {
        this.footerTitleUncontrolled = footerTitleUncontrolled;
    }

    @Column(name = "header_size_uncontrolled")
    public String getHeaderSizeUncontrolled() {
        return headerSizeUncontrolled;
    }

    public void setHeaderSizeUncontrolled(String headerSizeUncontrolled) {
        this.headerSizeUncontrolled = headerSizeUncontrolled;
    }

    @Column(name = "footer_size_uncontrolled")
    public String getFooterSizeUncontrolled() {
        return footerSizeUncontrolled;
    }

    public void setFooterSizeUncontrolled(String footerSizeUncontrolled) {
        this.footerSizeUncontrolled = footerSizeUncontrolled;
    }

}
