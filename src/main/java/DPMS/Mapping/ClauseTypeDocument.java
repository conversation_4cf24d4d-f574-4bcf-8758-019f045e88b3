package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "clause_type_documents")
public class ClauseTypeDocument extends CompositeStandardEntity<ClauseTypeDocumentPK> 
        implements ILinkedComposityGrid<ClauseTypeDocumentPK>, Serializable {

    private ClauseTypeDocumentPK id;

    public ClauseTypeDocument() {
    }

    public ClauseTypeDocument(ClauseTypeDocumentPK id) {
        this.id = id;
    }

    public ClauseTypeDocument(Long clauseTypeId, Long documentId) {
        this.id = new ClauseTypeDocumentPK(clauseTypeId, documentId);
    }

    @EmbeddedId
    @Override
    public ClauseTypeDocumentPK getId() {
        return id;
    }

    @Override
    public ClauseTypeDocumentPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(ClauseTypeDocumentPK id) {
        this.id = id;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 41 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!Objects.equals(getClass(), obj.getClass())) {
            return false;
        }
        final ClauseTypeDocument other = (ClauseTypeDocument) obj;
        return !(this.id != other.id && (this.id == null || !this.id.equals(other.id)));
    }

}
