package DPMS.Mapping;

import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;
import qms.document.interfaces.IPlainNode;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "tblnodo")
public class NodeSimple extends StandardEntity<NodeSimple> implements Serializable, IPlainNode {

    private static final long serialVersionUID = 1L;
    
    private Long parent;
    private Integer topLevel = 0;
    private String path;
    private String module;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public NodeSimple() {
    }

    public NodeSimple(NodeSimple node) {
        this.id = node.id;
        this.parent = node.parent;
        this.topLevel = node.topLevel;
        this.path = node.path;
        this.module = node.module;
        this.code = node.code;
        this.description = node.description;
        this.status = node.status;
        this.deleted = node.deleted;
    }

    public NodeSimple(Long id) {
        this.id = id;
    }

    public NodeSimple(Long id, String code, Long parent) {
        this.id = id;
        this.code = code;
        this.parent = parent;
    }

    /**
     *
     * @return
     */
    @Id
    @Basic(optional = false)
    @Column(name = "intnodoid", nullable = false)
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Basic(optional = false)
    @Column(name = "vchtitulo", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "txtdescripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Basic(optional = false)
    @Column(name = "intnodopadre", nullable = false)
    @Override
    public Long getParent() {
        return parent;
    }

    @Override
    public void setParent(Long parent) {
        this.parent = parent;
    }

    @Column(name = "inttoplevel")
    @Override
    public Integer getTopLevel() {
        return topLevel;
    }

    @Override
    public void setTopLevel(Integer topLevel) {
        this.topLevel = topLevel;
    }

    @Column(name = "path")
    @Override
    public String getPath() {
        return path;
    }

    @Override
    public void setPath(String path) {
        this.path = path;
    }
    
    @Column(name = "module")
    @Override
    public String getModule() {
        return module;
    }

    @Override
    public void setModule(String module) {
        this.module = module;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 19 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final NodeSimple other = (NodeSimple) obj;
        return !(this.id != other.id 
                && (this.id == null || !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.NodeSimple{" + "id=" + id + ", path=" + path + ", code=" + code + ", description=" + description + '}';
    }
    
    
}
