package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Immutable;

/**
 * <AUTHOR>
 * 
 * @deprecated en la medida de lo posible utilizar `SequenceDetailGenerator`
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "sequence_detail")
@Immutable
@Deprecated
public class SequenceDetail extends CompositeStandardEntity<SequenceDetailPK> implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private SequenceDetailPK id;
    private Long flujoId;
    private String userDescription;
    private String positionDescription;
    private Integer status;
    private String comments;
    private Date modificationDate;
    private Date finishDate;
    private String currentRecurrence;
    private String types;
    private String autorizationPoolDetailStatus;

    public SequenceDetail() {
    }

    public SequenceDetail(SequenceDetailPK id) {
        this.id = id;
    }

    @Column(name = "current_recurrence")
    public String getCurrentRecurrence() {
        return currentRecurrence;
    }

    public void setCurrentRecurrence(String currentRecurrence) {
        this.currentRecurrence = currentRecurrence;
    }

    @Column(name = "auth_pool_detail_status")
    public String getAutorizationPoolDetailStatus() {
        return autorizationPoolDetailStatus;
    }

    public void setAutorizationPoolDetailStatus(String autorizationPoolDetailStatus) {
        this.autorizationPoolDetailStatus = autorizationPoolDetailStatus;
    }

    @Override
    @EmbeddedId
    public SequenceDetailPK getId() {
        return id;
    }

    @Override
    public SequenceDetailPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(SequenceDetailPK id) {
        this.id = id;
    }

    @Column(name = "flujo_id")
    public Long getFlujoId() {
        return flujoId;
    }

    public void setFlujoId(Long flujoId) {
        this.flujoId = flujoId;
    }

    @Column(name = "user_description")
    public String getUserDescription() {
        return userDescription;
    }

    public void setUserDescription(String userDescription) {
        this.userDescription = userDescription;
    }

    /**
     * Regresa el status del entity "SequenceDetailRequest"
     * 
     * @return SequenceDetailRequest.getStatus()
     */
    @Column(name = "intestado")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "comments")
    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    @Column(name = "modification_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getModificationDate() {
        return modificationDate;
    }

    public void setModificationDate(Date modificationDate) {
        this.modificationDate = modificationDate;
    }

    @Column(name = "finish_date")
    @Temporal(TemporalType.DATE)
    public Date getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }

    @Column(name = "position_description")
    public String getPositionDescription() {
        return positionDescription;
    }

    public void setPositionDescription(String positionDescription) {
        this.positionDescription = positionDescription;
    }

    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 47 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final SequenceDetail other = (SequenceDetail) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "SequenceDetail{" + "id=" + id + '}';
    }

}
