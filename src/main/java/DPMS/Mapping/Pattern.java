package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "pattern")
public class Pattern extends StandardEntity<Pattern> implements DomainObjectInterface {

    private static final long serialVersionUID = 1L;
    
    private Long businessUnitId;
    private BusinessUnitLite businessUnit;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public Pattern() {
        this.setDeleted(IS_NOT_DELETED);
        this.setStatus(ACTIVE_STATUS);
    }

    public Pattern(Long device_group_id) {
        this.id = device_group_id;
    }

    @Id
    @Basic
    @Column(name = "pattern_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long device_group_id) {
        this.id = device_group_id;
    }


    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "business_unit_id", name = "business_unit_id",
            insertable = false, updatable = false)
    public BusinessUnitLite getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitLite businessUnit) {
        this.businessUnit = businessUnit;
    }
}
