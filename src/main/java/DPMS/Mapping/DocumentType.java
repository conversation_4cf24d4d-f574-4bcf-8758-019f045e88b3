package DPMS.Mapping;

import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Convert;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;
import jakarta.persistence.Transient;
import mx.bnext.core.file.IPdfConversionEnabled;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.custom.core.DynamicFields;
import qms.custom.core.DynamicSearchColumn;
import qms.custom.entity.DynamicField;
import qms.document.dto.DocumentTypeLinkDTO;
import qms.document.entity.DictionaryIndex;
import qms.document.entity.DocumentTypeDynamicField;
import qms.document.interfaces.IPlainDocumentType;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.initialload.ICreationTypeAware;
import qms.util.interfaces.EntityType;
import qms.util.interfaces.IUserDefinedCodeConfig;


/**
 *
 * <AUTHOR> Carlos Limas Álvarez
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "document_type")
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@DynamicFields(
    compositeEntityClass = DocumentTypeDynamicField.class,
    createViewPreffix = "",
    entityMappedBy = "documentTypeId",
    dynamicFieldMappedBy = "dynamicFieldId"
)
public class DocumentType extends StandardEntity<DocumentType>
        implements EntityType, IAuditableEntity, ICreationTypeAware, IUserDefinedCodeConfig, Serializable, IPlainDocumentType, IPdfConversionEnabled {

    private static final long serialVersionUID = 1L;
    
    public static enum documentControlledType {
        CONTROLLED, UNCONTROLLED;
        @Override
        public String toString() {
            return name().toLowerCase();
        }
        public boolean equals(DocumentType t) {
            return name().toLowerCase().equals(t.getDocumentControlledType());
        }
    }
    
    public static enum STATUS implements IStatusEnum {
        ACTIVE(1, IStatusEnum.COLOR_GREEN),
        INACTIVE(0, IStatusEnum.COLOR_GRAY)
        ;
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public String toString() { 
            return this.value.toString();
        }

        @Override
        public IStatusEnum getActiveStatus() {
            return ACTIVE;
        }
    }
    
    private String code;
    private Integer status;
    private Integer deleted = 0;
    private String description;
    private Date createdDate;
    private Date lastModifiedDate;
    private Long createdBy;
    private Long lastModifiedBy;
    private Long dictionaryIndexId;
    
    private Integer isRetainable;
    private String documentControlledType;
    private String registryCode;
    private Integer registryCodeSequenceLength;
    
    private DocumentTypeLinkDTO link;
    private Integer shareDocuments = 0; 
    private Long shareMaxDownloads = 1L;
    private Long shareMaxViews = 1L;
    private Long shareLife = 1L;
    private Integer shareRestrictUserAgent = 0;
    private Integer shareRestrictFileType = 0; 
    private Integer externalLink = 0; 
    private Long externalMaxDownloads = 1L;
    private Long externalLife = 1L;
    private Integer externalRestrictUserAgent = 0;
    private Integer externalRestrictFileType = 0;
    
    private Integer downloadFiles = 0;
    private Integer creationType;
    private DictionaryIndex dictionaryIndex;
    private Integer mustRead;
    private Integer mustAssignReaders;
    private Integer addFindingsEnabled = 0;
    private Integer addActivitiesEnabled = 0;
    
    private Integer quickPrintEnabled;
    private Boolean filePdfPagesEnabled = false; // <-- Al estar encendido, se habilita el botón de "Ver" en los campos de tipo archivo de FORMULARIOS
    
    //Retencion
    private Integer collectingAndStoreResponsible;
    private Integer informationClassification = 0;
    private Integer disposition = 0;
    
    //Transient
    private List<DynamicField> dynamicFields;
    private Integer documentExpiration;
    private Integer enablePdfViewer = 1;
    private Integer pdfViewerFitDocumentScreen;
    
    private Integer readOnlyEnabled;
    private Integer canPrintControlledCopies;
    
    public DocumentType() {
    }

    public DocumentType(Long id, documentControlledType s) {
        this.id = id;
        this.documentControlledType = s.name().toLowerCase();
    }
    
    public DocumentType(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "document_type_id")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date dt) {
        this.createdDate = dt;
    }

    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(jakarta.persistence.TemporalType.TIMESTAMP)
    @Override
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date dt) {
        this.lastModifiedDate = dt;
    }

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    @Override
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "last_modified_by")
    @LastModifiedBy
    @Override
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }   

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @LocalizedField
    @Column(name = "description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }


    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof DocumentType)) {
            return false;
        }
        DocumentType other = (DocumentType) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.DocumentType[ id=" + id + " ]";
    }

    @Column(name = "retainable")
    @Override
    public Integer getIsRetainable() {
        return isRetainable;
    }

    @Override
    public void setIsRetainable(Integer isRetainable) {
        this.isRetainable = isRetainable;
    }

    @Column(name = "document_controlled_type")
    @DynamicSearchColumn
    @Override
    public String getDocumentControlledType() {
        return documentControlledType;
    }

    @Override
    public void setDocumentControlledType(String documentControlledType) {
        this.documentControlledType = documentControlledType;
    }

    @Column(name = "registry_code")
    @Override
    public String getRegistryCode() {
        return registryCode;
    }

    @Override
    public void setRegistryCode(String registryCode) {
        this.registryCode = registryCode;
    }

    @DynamicSearchColumn
    @Column(name = "dictionary_index_id")
    @Override
    public Long getDictionaryIndexId() {
        return dictionaryIndexId;
    }

    @Override
    public void setDictionaryIndexId(Long dictionaryIndexId) {
        this.dictionaryIndexId = dictionaryIndexId;
    }
    
    @ManyToOne
    @JoinColumn(name = "dictionary_index_id", insertable = false, updatable = false)
    public DictionaryIndex getDictionaryIndex() {
        return dictionaryIndex;
    }

    public void setDictionaryIndex(DictionaryIndex dictionaryIndex) {
        this.dictionaryIndex = dictionaryIndex;
    }

    
    @Column(name = "registry_code_sequence_length")
    @Override
    public Integer getRegistryCodeSequenceLength() {
        return registryCodeSequenceLength;
    }

    @Override
    public void setRegistryCodeSequenceLength(Integer registryCodeSequenceLength) {
        this.registryCodeSequenceLength = registryCodeSequenceLength;
    }

    /**
     * Se utilizan solo para guardar datos en el Service (CRUD), para traer
     * datos utilizar el entity individual "DocumentTypeDynamicField"
     * 
     * @return 
     */
    @Transient
    public List<DynamicField> getDynamicFields() {
        return dynamicFields;
    }

    public void setDynamicFields(List<DynamicField> dynamicFields) {
        this.dynamicFields = dynamicFields;
    }
    
    /**
     * Se utilizan solo para guardar datos en el Service (CRUD)
     * 
     * @return 
     */
    @Transient
    public DocumentTypeLinkDTO getLink() {
        return link;
    }

    public void setLink(DocumentTypeLinkDTO link) {
        this.link = link;
    }
    
    @Override
    public String configCode() {
        return registryCode;
    }
    
    @Column(name = "share_documents")
    @Override
    public Integer getShareDocuments() {
        return shareDocuments;
}

    @Override
    public void setShareDocuments(Integer shareDocuments) {
        this.shareDocuments = shareDocuments;
    }

    @Column(name = "share_max_downloads")
    @Override
    public Long getShareMaxDownloads() {
        return shareMaxDownloads;
    }

    @Override
    public void setShareMaxDownloads(Long shareMaxDownloads) {
        this.shareMaxDownloads = shareMaxDownloads;
    }
    
    @Column(name = "share_max_views")
    @Override
    public Long getShareMaxViews() {
        return shareMaxViews;
    }

    @Override
    public void setShareMaxViews(Long shareMaxViews) {
        this.shareMaxViews = shareMaxViews;
    }


    @Column(name = "share_life")
    @Override
    public Long getShareLife() {
        return shareLife;
    }

    @Override
    public void setShareLife(Long shareLife) {
        this.shareLife = shareLife;
    }

    @Column(name = "share_restrict_user_agent")
    @Override
    public Integer getShareRestrictUserAgent() {
        return shareRestrictUserAgent;
    }

    @Override
    public void setShareRestrictUserAgent(Integer shareRestrictUserAgent) {
        this.shareRestrictUserAgent = shareRestrictUserAgent;
    }

    @Column(name = "share_restrict_file_type")
    @Override
    public Integer getShareRestrictFileType() {
        return shareRestrictFileType;
    }

    @Override
    public void setShareRestrictFileType(Integer shareRestrictFileType) {
        this.shareRestrictFileType = shareRestrictFileType;
    }
    
    @Column(name = "external_link")
    @Override
    public Integer getExternalLink() {
        return externalLink;
    }

    @Override
    public void setExternalLink(Integer externalLink) {
        this.externalLink = externalLink;
    }

    @Column(name = "external_max_downloads")
    @Override
    public Long getExternalMaxDownloads() {
        return externalMaxDownloads;
    }

    @Override
    public void setExternalMaxDownloads(Long externalMaxDownloads) {
        this.externalMaxDownloads = externalMaxDownloads;
    }

    @Column(name = "external_life")
    @Override
    public Long getExternalLife() {
        return externalLife;
    }

    @Override
    public void setExternalLife(Long externalLife) {
        this.externalLife = externalLife;
    }

    @Column(name = "external_restrict_user_agent")
    @Override
    public Integer getExternalRestrictUserAgent() {
        return externalRestrictUserAgent;
    }

    @Override
    public void setExternalRestrictUserAgent(Integer externalRestrictUserAgent) {
        this.externalRestrictUserAgent = externalRestrictUserAgent;
    }

    @Column(name = "external_restrict_file_type")
    @Override
    public Integer getExternalRestrictFileType() {
        return externalRestrictFileType;
    }

    @Override
    public void setExternalRestrictFileType(Integer externalRestrictFileType) {
        this.externalRestrictFileType = externalRestrictFileType;
    }
    
    @DynamicSearchColumn
    @Column(name = "download_files")
    @Override
    public Integer getDownloadFiles() {
        return downloadFiles;
    }

    @Override
    public void setDownloadFiles(Integer downloadFiles) {
        this.downloadFiles = downloadFiles;
    }

    @Basic(optional = false)
    @Column(name = "document_expiration")
    @Override
    public Integer getDocumentExpiration() {
        return documentExpiration;
    }


    @Override
    public void setDocumentExpiration(Integer documentExpiration) {
        this.documentExpiration = documentExpiration;
    }


    @Column(name = "must_read")
    @Override
    public Integer getMustRead() {
        return mustRead;
    }

    @Override
    public void setMustRead(Integer mustRead) {
        this.mustRead = mustRead;
    }
    
    @Column(name = "must_assign_readers")
    @Override
    public Integer getMustAssignReaders() {
        return mustAssignReaders;
    }

    @Override
    public void setMustAssignReaders(Integer mustAssignReaders) {
        this.mustAssignReaders = mustAssignReaders;
    }

    @Column(name = "add_findings_enabled")
    @Override
    public Integer getAddFindingsEnabled() {
        return addFindingsEnabled;
    }

    @Override
    public void setAddFindingsEnabled(Integer addFindingsEnabled) {
        this.addFindingsEnabled = addFindingsEnabled;
    }

    @Column(name = "add_activities_enabled")
    @Override
    public Integer getAddActivitiesEnabled() {
        return addActivitiesEnabled;
    }

    @Override
    public void setAddActivitiesEnabled(Integer addActivitiesEnabled) {
        this.addActivitiesEnabled = addActivitiesEnabled;
    }
    
    @Column(name = "collecting_store_responsible")
    @Override
    public Integer getCollectingAndStoreResponsible() {
        return collectingAndStoreResponsible;
    }

    @Override
    public void setCollectingAndStoreResponsible(Integer collectingAndStoreResponsible) {
        this.collectingAndStoreResponsible = collectingAndStoreResponsible;
    }
    
    @Column(name = "information_classification")
    @Override
    public Integer getInformationClassification() {
        return informationClassification;
    }

    @Override
    public void setInformationClassification(Integer informationClassification) {
        this.informationClassification = informationClassification;
    }
    
    @Column(name = "disposition")
    @Override
    public Integer getDisposition() {
        return disposition;
    }

    @Override
    public void setDisposition(Integer disposition) {
        this.disposition = disposition;
    }

    @Column(name = "enable_pdf_viewer")
    @Override
    public Integer getEnablePdfViewer() {
        return enablePdfViewer;
    }

    @Override
    public void setEnablePdfViewer(Integer enablePdfViewer) {
        this.enablePdfViewer = enablePdfViewer;
    }
    
    @Column(name = "read_only_enabled")
    @Override
    public Integer getReadOnlyEnabled() {
        return readOnlyEnabled;
    }

    @Override
    public void setReadOnlyEnabled(Integer readOnlyEnabled) {
        this.readOnlyEnabled = readOnlyEnabled;
    }
    
    @Column(name = "quick_print_enabled")
    @Override
    public Integer getQuickPrintEnabled() {
        return quickPrintEnabled;
    }

    @Override
    public void setQuickPrintEnabled(Integer quickPrintEnabled) {
        this.quickPrintEnabled = quickPrintEnabled;
    }
    
    @Column(name = "pdf_viewer_fit_document_screen")
    @Override
    public Integer getPdfViewerFitDocumentScreen() {
        return pdfViewerFitDocumentScreen;
    }

    @Override
    public void setPdfViewerFitDocumentScreen(Integer pdfViewerFitDocumentScreen) {
        this.pdfViewerFitDocumentScreen = pdfViewerFitDocumentScreen;
    }

    @Column(name = "can_print_controlled_copies")
    @Override
    public Integer getCanPrintControlledCopies() {
        return canPrintControlledCopies;
    }

    @Override
    public void setCanPrintControlledCopies(Integer canPrintControlledCopies) {
        this.canPrintControlledCopies = canPrintControlledCopies;
    }

    /**
     * Configuración para FORMULARIOS:
     *  - Al estar encendido, se habilita el botón de "Ver" en los campos de tipo archivo de FORMULARIOS,
     *  - Al estar apagado ni siquiera genera información den `file_pdf_pages`
     */
    @Column(name = "is_file_pdf_pages_enabled")
    @Convert(converter = org.hibernate.type.NumericBooleanConverter.class)
    public Boolean getFilePdfPagesEnabled() {
        return filePdfPagesEnabled;
}

    public void setFilePdfPagesEnabled(Boolean filePdfPagesEnabled) {
        this.filePdfPagesEnabled = filePdfPagesEnabled;
    }


    @Transient
    public Boolean getPdfImagesEnabled() {
        return Boolean.TRUE.equals(filePdfPagesEnabled);
    }

    public void setPdfImagesEnabled(Boolean pdfImagesEnabled) {
        // empty
    }

    @Transient
    public Boolean getPdfConversionEnabled() {
        return Boolean.TRUE.equals(filePdfPagesEnabled);
    }

    public void setPdfConversionEnabled(Boolean pdfConversionEnabled) {
        // empty
    }

}
