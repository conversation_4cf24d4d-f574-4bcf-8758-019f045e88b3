package DPMS.Mapping;

import Framework.Config.BaseDomainObject;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import jakarta.persistence.Temporal;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "request_to_authorize")
public class RequestToAuthorize extends BaseDomainObject<Long> implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private Long id;
    private Long requestId;
    private Long attendantId;
    private Long positionId;
    private Integer delay;
    private Date assignament;
    private Date deadline;

    public RequestToAuthorize() {
    }

    public RequestToAuthorize(Long device_group_id) {
        this.id = device_group_id;
    }

    @Id
    @Basic
    @Column(name = "request_to_authorize_id", precision = 19)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    public void setId(Long device_group_id) {
        this.id = device_group_id;
    }

    @Override
    public Long identifuerValue() {
        return id;
    }


    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "attendant_id")
    public Long getAttendantId() {
        return attendantId;
    }

    public void setAttendantId(Long AttendantId) {
        this.attendantId = AttendantId;
    }

    @Column(name = "position_id")
    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    @Column(name = "delay")
    public Integer getDelay() {
        return delay;
    }

    public void setDelay(Integer delay) {
        this.delay = delay;
    }

    @Column(name = "assignament")
    @Temporal(jakarta.persistence.TemporalType.DATE)
    public Date getAssignament() {
        return assignament;
    }

    public void setAssignament(Date assignament) {
        this.assignament = assignament;
    }

    @Column(name = "deadline")
    @Temporal(jakarta.persistence.TemporalType.DATE)
    public Date getDeadline() {
        return deadline;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    @Override
    public int hashCode() {
      int hash = 7;
      return hash;
    }

    @Override
    public boolean equals(Object obj) {
      if (obj == null) {
        return false;
      }
      if (getClass() != obj.getClass()) {
        return false;
      }
      final RequestToAuthorize other = (RequestToAuthorize) obj;
      return Objects.equals(this.id, other.id) || (this.id != null && this.id.equals(other.id));
    }

    @Override
    public String toString() {
      return "DPMS.Mapping.RequestToAuthorize [id=" + id + "]";
    }
    
}
