package DPMS.Mapping;

import Framework.Config.StandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.FilterDefs;
import org.hibernate.annotations.GenericGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.initialload.ICreationTypeAware;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "tblnodo")
@FilterDefs({
    @FilterDef(name = "not_deleted_children"),
    @FilterDef(name = "not_children")
})
public class Node extends StandardEntity<Node> implements Serializable, ICreationTypeAware {

    private static final long serialVersionUID = 1L;

    private Long parent;
    private Integer topLevel = 0;
    private String module;
    private String path;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer creationType;

    public Node() {
    }

    public Node(Long id) {
        this.id = id;
    }

    public Node(Long id, String code, Long parent) {
        this.id = id;
        this.code = code;
        this.parent = parent;
    }

    /**
     *
     * @return
     */
    @Id
    @Basic(optional = false)
    @Column(name = "intnodoid", nullable = false)
    @Override
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Basic(optional = false)
    @Column(name = "vchtitulo", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }
    
    @LocalizedField
    @Basic(optional = false)
    @Column(name = "txtdescripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Basic(optional = false)
    @Column(name = "intnodopadre", nullable = false)
    public Long getParent() {
        return parent;
    }

    public void setParent(Long parent) {
        this.parent = parent;
    }

    @Column(name = "inttoplevel")
    public Integer getTopLevel() {
        return topLevel;
    }

    public void setTopLevel(Integer topLevel) {
        this.topLevel = topLevel;
    }

    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    /**
     *
     * @return
     */
    @Column(name = "path")
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //  Req 3328
        if (!(object instanceof Node)) {
            return false;
        }
        Node other = (Node) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "Node[ intnodoid=" + id + " ]";
    }
}
