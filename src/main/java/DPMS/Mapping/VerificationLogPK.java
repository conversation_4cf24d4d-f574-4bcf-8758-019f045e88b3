package DPMS.Mapping;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class VerificationLogPK implements Serializable {

    private Long requestId;
    private Long userId;
    private Date lastModification;

    public VerificationLogPK() {
    }

    public VerificationLogPK(Long requestId, Long userId, Date lastModification) {
        this.requestId = requestId;
        this.userId = userId;
        this.lastModification = lastModification;
    }

    @Basic(optional = false)
    @Column(name = "request_id")
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Basic(optional = false)
    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Basic(optional = false)
    @Column(name = "last_modification")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getLastModification() {
        return lastModification;
    }

    public void setLastModification(Date lastModification) {
        this.lastModification = lastModification;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 17 * hash + Objects.hashCode(this.requestId);
        hash = 17 * hash + Objects.hashCode(this.userId);
        hash = 17 * hash + Objects.hashCode(this.lastModification);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final VerificationLogPK other = (VerificationLogPK) obj;
        if (!Objects.equals(this.requestId, other.requestId)) {
            return false;
        }
        if (!Objects.equals(this.userId, other.userId)) {
            return false;
        }
        if (!Objects.equals(this.lastModification, other.lastModification)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "VerificationLogPK{" + "requestId=" + requestId + ", userId=" + userId + ", lastModification=" + lastModification + '}';
    }

}
