package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblproyectoactividad")
public class ProjectGoal extends CompositeStandardEntity<ProjectGoalPK> implements Serializable {

    private static final long serialVersionUID = 1L;
    private ProjectGoalPK id;
    private Date tspfechahoracreacion;
    private Project proyect;
    private Goals goal;

    public ProjectGoal() {
    }

    public ProjectGoal(ProjectGoalPK id) {
        this.id = id;
    }

    public ProjectGoal(Long intactividadid, Long intproyectoid) {
        this.id = new ProjectGoalPK(intactividadid, intproyectoid);
    }

    @EmbeddedId
    public ProjectGoalPK getId() {
        return id;
    }

    @Override
    public ProjectGoalPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(ProjectGoalPK id) {
        this.id = id;
    }

    @Column(name = "tspfechahoracreacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaCreacion() {
        return tspfechahoracreacion;
    }

    public void setFechaCreacion(Date tspfechahoracreacion) {
        this.tspfechahoracreacion = tspfechahoracreacion;
    }

    @JsonIgnore
    @JoinColumn(name = "intproyectoid", referencedColumnName = "intproyectoid", insertable = false, updatable = false)
    @ManyToOne(optional = false, fetch = FetchType.EAGER)
    public Project getProyect() {
        return proyect;
    }

    public void setProyect(Project proyect) {
        this.proyect = proyect;
    }

    @JsonIgnore
    @JoinColumn(name = "intactividadid", referencedColumnName = "intactividadid", insertable = false, updatable = false)
    @OneToOne(optional = false, fetch = FetchType.EAGER)
    public Goals getGoal() {
        return goal;
    }

    public void setGoal(Goals activity) {
        this.goal = activity;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3264
        if (!(object instanceof ProjectGoal)) {
            return false;
        }
        ProjectGoal other = (ProjectGoal) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.ProyectActivity[ id=" + id + " ]";
    }
}
