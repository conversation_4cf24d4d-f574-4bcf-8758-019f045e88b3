package DPMS.Mapping;

import DPMS.Mapping.Process;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.GenericGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "tblnodo")
public class NodeAccess extends StandardEntity<NodeAccess> implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String path;
    private String module;
    private Integer topLevel = 0;
    private Long parent;
    private Set<UserRef> users;
    private Set<BusinessUnitDepartmentLite> departments;
    private Set<Process> process;
    private Set<BusinessUnitLite> businessUnits;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public NodeAccess() {
    }

    public NodeAccess(Long id) {
        this.id = id;
    }

    public NodeAccess(Long id, String vchtitulo) {
        this.id = id;
        this.setCode(vchtitulo);
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intnodoid", nullable = false)
    @Override
    @GeneratedValue(generator = "increment", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "increment", strategy = "increment")
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Column(name = "intnodopadre")
    public Long getParent() {
        return parent;
    }

    public void setParent(Long parent) {
        this.parent = parent;
    }
    
    @Column(name = "inttoplevel")
    public Integer getTopLevel() {
        return topLevel;
    }

    public void setTopLevel(Integer topLevel) {
        this.topLevel = topLevel;
    }

    @Basic(optional = false)
    @Column(name = "vchtitulo", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "txtdescripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }

    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }
    
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 19 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final NodeAccess other = (NodeAccess) obj;
        if (this.id != other.id && (this.id == null || !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Column(name = "path")
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "tblcarpetausuario", joinColumns = {
        @JoinColumn(name = "intnodoid")}, inverseJoinColumns = {
        @JoinColumn(name = "intusuarioid")})
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<UserRef> getUsers() {
        return users;
    }

    public void setUsers(Set<UserRef> users) {
        this.users = users;
    }

    public void strUsers(HashSet<UserRef> users) {
        this.users = users;
    }

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "node_business_unit_department", joinColumns = {
        @JoinColumn(name = "node_id")}, inverseJoinColumns = {
        @JoinColumn(name = "business_unit_department_id")})
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<BusinessUnitDepartmentLite> getDepartments() {
        return departments;
    }

    public void setDepartments(Set<BusinessUnitDepartmentLite> departments) {
        this.departments = departments;
    }

    public void strDepartments(HashSet<BusinessUnitDepartmentLite> departments) {
        this.departments = departments;
    }

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "tblcarpetaarea", joinColumns = {
        @JoinColumn(name = "intnodoid")}, inverseJoinColumns = {
        @JoinColumn(name = "intareaid")})
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<Process> getProcess() {
        return process;
    }

    public void setProcess(Set<Process> process) {
        this.process = process;
    }

    public void strProcess(HashSet<Process> process) {
        this.process = process;
    }

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "node_business_unit", joinColumns = {
        @JoinColumn(name = "node_id")}, inverseJoinColumns = {
        @JoinColumn(name = "business_unit_id")})
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<BusinessUnitLite> getBusinessUnits() {
        return businessUnits;
    }

    public void setBusinessUnits(Set<BusinessUnitLite> businessUnits) {
        this.businessUnits = businessUnits;
    }

    public void strBusinessUnits(HashSet<BusinessUnitLite> businessUnits) {
        this.businessUnits = businessUnits;
    }

}
