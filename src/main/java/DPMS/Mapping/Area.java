package DPMS.Mapping;
// Generated 26/07/2012 01:31:24 PM by Hibernate Tools 3.2.1.GA

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.initialload.ICreationTypeAware;
import qms.util.interfaces.IPersistableDescription;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "area")
public class Area extends StandardEntity<Area> implements DomainObjectInterface, IPersistableDescription, ICreationTypeAware {
    private static final long serialVersionUID = 1L;
    
    public static final String PREFIX = "ARE-";
	
    private UserRef attendant;
    private String fontColor;
    private String description = "";
    private String code = "";
    private String backgroundColor;
    private Long departmentId;
    private Long buildingId;
    private Long attendantId;
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer creationType;
    private BusinessUnitDepartmentLoad department;
    private Building building;
    
    private String customField1;
    private String customField2;
    private String customField3;
    private String customField4;
    private String customField5;
    private String customField6;
    private String customField7;
    private String customField8;
    private String customField9;
    private String customField10;
    private String customField11;
    private String customField12;
    private String customField13;
    private String customField14;
    private String customField15;
    private String customField16;
    private String customField17;
    private String customField18;
    private String customField19;
    private String customField20;
    private String customField21;
    private String customField22;
    private String customField23;
    private String customField24;
    private String customField25;
    private String customField26;


    public Area() {
    }
    public Area(Long areaId, String title){
        this.id = areaId;
        this.description = title;
    }
    
    public Area(Long areaId) {
        this.id = areaId;
    }

    public Area(
            Long areaId, 
            Long departmentId,
            Long attendantId,
            String areaKey,
            String title, 
            Integer status) {
        this.id = areaId;
        this.departmentId = departmentId;
        this.attendantId = attendantId;
        this.description = title;
        this.status = status;
        
    }
    @Id
    @Basic
    @Column(name = "area_id", precision = 19)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long validationTypeId) {
        this.id = validationTypeId;
    }

    @Column(name = "custom_field1")
    public String getCustomField1() {
        return customField1;
    }

    public void setCustomField1(String customField1) {
        this.customField1 = customField1;
    }

    @Column(name = "custom_field2")
    public String getCustomField2() {
        return customField2;
    }

    public void setCustomField2(String customField2) {
        this.customField2 = customField2;
    }

    @Column(name = "custom_field3")
    public String getCustomField3() {
        return customField3;
    }

    public void setCustomField3(String customField3) {
        this.customField3 = customField3;
    }

    @Column(name = "custom_field4")
    public String getCustomField4() {
        return customField4;
    }

    public void setCustomField4(String customField4) {
        this.customField4 = customField4;
    }

    @Column(name = "custom_field5")
    public String getCustomField5() {
        return customField5;
    }

    public void setCustomField5(String customField5) {
        this.customField5 = customField5;
    }

    @Column(name = "custom_field6")
    public String getCustomField6() {
        return customField6;
    }

    public void setCustomField6(String customField6) {
        this.customField6 = customField6;
    }

    @Column(name = "custom_field7")
    public String getCustomField7() {
        return customField7;
    }

    public void setCustomField7(String customField7) {
        this.customField7 = customField7;
    }

    @Column(name = "custom_field8")
    public String getCustomField8() {
        return customField8;
    }

    public void setCustomField8(String customField8) {
        this.customField8 = customField8;
    }

    @Column(name = "custom_field9")
    public String getCustomField9() {
        return customField9;
    }

    public void setCustomField9(String customField9) {
        this.customField9 = customField9;
    }

    @Column(name = "custom_field10")
    public String getCustomField10() {
        return customField10;
    }

    public void setCustomField10(String customField10) {
        this.customField10 = customField10;
    }

    @Column(name = "custom_field11")
    public String getCustomField11() {
        return customField11;
    }

    public void setCustomField11(String customField11) {
        this.customField11 = customField11;
    }

    @Column(name = "custom_field12")
    public String getCustomField12() {
        return customField12;
    }

    public void setCustomField12(String customField12) {
        this.customField12 = customField12;
    }

    @Column(name = "custom_field13")
    public String getCustomField13() {
        return customField13;
    }

    public void setCustomField13(String customField13) {
        this.customField13 = customField13;
    }

    @Column(name = "custom_field14")
    public String getCustomField14() {
        return customField14;
    }

    public void setCustomField14(String customField14) {
        this.customField14 = customField14;
    }

    @Column(name = "custom_field15")
    public String getCustomField15() {
        return customField15;
    }

    public void setCustomField15(String customField15) {
        this.customField15 = customField15;
    }

    @Column(name = "custom_field16")
    public String getCustomField16() {
        return customField16;
    }

    public void setCustomField16(String customField16) {
        this.customField16 = customField16;
    }

    @Column(name = "custom_field17")
    public String getCustomField17() {
        return customField17;
    }

    public void setCustomField17(String customField17) {
        this.customField17 = customField17;
    }

    @Column(name = "custom_field18")
    public String getCustomField18() {
        return customField18;
    }

    public void setCustomField18(String customField18) {
        this.customField18 = customField18;
    }

    @Column(name = "custom_field19")
    public String getCustomField19() {
        return customField19;
    }

    public void setCustomField19(String customField19) {
        this.customField19 = customField19;
    }

    @Column(name = "custom_field20")
    public String getCustomField20() {
        return customField20;
    }

    public void setCustomField20(String customField20) {
        this.customField20 = customField20;
    }

    @Column(name = "custom_field21")
    public String getCustomField21() {
        return customField21;
    }

    public void setCustomField21(String customField21) {
        this.customField21 = customField21;
    }

    @Column(name = "custom_field22")
    public String getCustomField22() {
        return customField22;
    }

    public void setCustomField22(String customField22) {
        this.customField22 = customField22;
    }

    @Column(name = "custom_field23")
    public String getCustomField23() {
        return customField23;
    }

    public void setCustomField23(String customField23) {
        this.customField23 = customField23;
    }

    @Column(name = "custom_field24")
    public String getCustomField24() {
        return customField24;
    }

    public void setCustomField24(String customField24) {
        this.customField24 = customField24;
    }

    @Column(name = "custom_field25")
    public String getCustomField25() {
        return customField25;
    }

    public void setCustomField25(String customField25) {
        this.customField25 = customField25;
    }

    @Column(name = "custom_field26")
    public String getCustomField26() {
        return customField26;
    }

    public void setCustomField26(String customField26) {
        this.customField26 = customField26;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    

    @Basic(optional = false)
    @Column(name = "attendant_id", nullable=false)
    public Long getAttendantId() {
        return attendantId;
    }

    public void setAttendantId(Long attendantId) {
        this.attendantId = attendantId;
    }

    @Basic(optional = false)
    @Column(name = "department_id", nullable=false)
    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }
    
    @OneToOne
    @JoinColumn(referencedColumnName="user_id", name="attendant_id",insertable=false,updatable=false)
    public UserRef getAttendant() {
        return attendant;
    }

    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }

    @OneToOne
    @JoinColumn(referencedColumnName="business_unit_department_id", name="department_id", insertable=false,updatable=false)
    public BusinessUnitDepartmentLoad getDepartment() {
        return department;
    }

    public void setDepartment(BusinessUnitDepartmentLoad department) {
        this.department = department;
    }

    @Column(name = "building_id")
    public Long getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(Long buildingId) {
        this.buildingId = buildingId;
    }
    @OneToOne
    @JoinColumn(referencedColumnName="building_id", name="building_id", insertable=false,updatable=false)
    public Building getBuilding() {
        return building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    @Column(name = "font_color")
    public String getFontColor() {
        return fontColor;
    }
    

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    @Column(name = "background_color")
    public String getBackgroundColor() {
        return backgroundColor;
    }
    

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3229
        if (!(object instanceof Area)) {
            return false;
        }
        Area other = (Area) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Area[ Id=" + id + " ]";
    }
    
}



