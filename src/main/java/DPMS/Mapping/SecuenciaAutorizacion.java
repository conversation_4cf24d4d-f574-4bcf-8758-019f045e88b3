package DPMS.Mapping;

import Framework.Config.AutorizacionComparator;
import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Immutable;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblsecuenciaautorizacion")
@Immutable
public class SecuenciaAutorizacion extends StandardEntity<SecuenciaAutorizacion> implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date fechaHoraCreacion;
    private Set<Autorizacion> autorizaciones;
    private Long modificacionId;
    private Set<SequenceDetail> sequenceDetails;
    private Workflow flujo;
    private Long autorId;
    private Long businessUnitId;
    private Long businessUnitDepartmentId;
    private Long documentId;
    private String documentCode;
    private String documentStatus;
    private Boolean documentIsBackup;
    private Boolean documentDeleted;
    private String documentDescription;
    private Integer requestType;
    private Long requestId;
    private Long flujoId;
    private Long autorizationPoolId;
    

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer deletedRequest;

    public SecuenciaAutorizacion() {
    }

    public SecuenciaAutorizacion(Long id) {
        this.id = id;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intsecuenciaautorizacionid", insertable = false, updatable = false)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vchclave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Basic(optional = false)
    @Column(name = "vchclavesolicitud", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "intestado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    //@Basic(optional = false)
    @Column(name = "tspfechahoracreacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaHoraCreacion() {
        return fechaHoraCreacion;
    }

    public void setFechaHoraCreacion(Date fechaHoraCreacion) {
        this.fechaHoraCreacion = fechaHoraCreacion;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @JoinColumn(name = "intsecuenciaautorizacionid", referencedColumnName = "intsecuenciaautorizacionid", insertable = false, updatable = false)
    @OneToMany(fetch = FetchType.EAGER)
    public Set<Autorizacion> getAutorizaciones() {
        if (autorizaciones == null) {
            return null;
        }
        List<Autorizacion> d = new ArrayList<>(autorizaciones.size());
        d.addAll(autorizaciones);
        Collections.sort(d, new AutorizacionComparator());
        LinkedHashSet<Autorizacion> s = new LinkedHashSet<>();
        s.addAll(d);
        return s;
    }

    public void setAutorizaciones(Set<Autorizacion> autorizaciones) {
        this.autorizaciones = autorizaciones;
    }

    public void strAutorizaciones(HashSet<Autorizacion> autorizaciones) {
        this.autorizaciones = autorizaciones;
    }

    @Column(name = "intmodificacionid")
    public Long getModificacionId() {
        return modificacionId;
    }

    public void setModificacionId(Long modificacionId) {
        this.modificacionId = modificacionId;
    }

    @Column(name = "intautorid")
    public Long getAutorId() {
        return autorId;
    }

    public void setAutorId(Long autorId) {
        this.autorId = autorId;
    }

    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "document_id")
    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    @Column(name = "document_code")
    public String getDocumentCode() {
        return documentCode;
    }

    public void setDocumentCode(String documentCode) {
        this.documentCode = documentCode;
    }

    @Column(name = "document_status")
    public String getDocumentStatus() {
        return documentStatus;
    }

    public void setDocumentStatus(String documentStatus) {
        this.documentStatus = documentStatus;
    }

    @Column(name = "document_is_backup")
    public Boolean getDocumentIsBackup() {
        return documentIsBackup;
    }

    public void setDocumentIsBackup(Boolean documentIsBackup) {
        this.documentIsBackup = documentIsBackup;
    }

    @Column(name = "deleted_document")
    public Boolean getDocumentDeleted() {
        return documentDeleted;
    }

    public void setDocumentDeleted(Boolean documentDeleted) {
        this.documentDeleted = documentDeleted;
    }

    @Column(name = "document_description")
    public String getDocumentDescription() {
        return documentDescription;
    }

    public void setDocumentDescription(String documentDescription) {
        this.documentDescription = documentDescription;
    }

    @Column(name = "request_type")
    public Integer getRequestType() {
        return requestType;
    }

    public void setRequestType(Integer requestType) {
        this.requestType = requestType;
    }

    @Column(name = "flujo_id")
    public Long getFlujoId() {
        return flujoId;
    }

    public void setFlujoId(Long flujoId) {
        this.flujoId = flujoId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3324
        if (!(object instanceof SecuenciaAutorizacion)) {
            return false;
        }
        SecuenciaAutorizacion other = (SecuenciaAutorizacion) object;
        return !((this.id == null && other.id != null)
                || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.SecuenciaAutorizacion[ intsecuenciaautorizacionid=" + id + " ]";
    }

    @Column(name = "autorization_pool_id")
    public Long getAutorizationPoolId() {
        return autorizationPoolId;
    }

    public void setAutorizationPoolId(Long autorizationPoolId) {
        this.autorizationPoolId = autorizationPoolId;
    }
    
    @Column(name = "request_id", insertable = false, updatable = false)
    public Long getRequestId() {
        return requestId != null ? requestId : 0;
}

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @Column(name = "deleted_request", insertable = false, updatable = false)
    public Integer getDeletedRequest() {
        return deletedRequest;
    }

    public void setDeletedRequest(Integer deletedRequest) {
        this.deletedRequest = deletedRequest;
    }
    
    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "request_id", referencedColumnName = "request_id")
    public Set<SequenceDetail> getSequenceDetails() {
        return sequenceDetails;
    }

    public void setSequenceDetails(Set<SequenceDetail> SequenceDetails) {
        this.sequenceDetails = SequenceDetails;
    }
    
    @JoinColumn(name = "flujo_id", referencedColumnName = "workflow_id", insertable = false, updatable = false)
    @OneToOne
    public Workflow getFlujo() {
        return flujo;
    }

    /**
     * @param flujo the autorizante to set
     */
    public void setFlujo(Workflow flujo) {
        this.flujo = flujo;
    }
    
}
