package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
/**
 *
 * <AUTHOR> -- TODO: Para poder guardar se requiere agregar el
 * EntityInterceptor que excluya el la clase de esta PK
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblindicadorprogramacion")
public class IndicadorProgramacion extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    protected IndicadorProgramacionPK indicadorProgramacionPK;
    private Indicador indicador;

    public IndicadorProgramacion() {
    }

    public IndicadorProgramacion(IndicadorProgramacionPK indicadorProgramacionPK) {
        this.indicadorProgramacionPK = indicadorProgramacionPK;
    }

    public IndicadorProgramacion(long intindicadorid, Date dtefechaprogramada) {
        this.indicadorProgramacionPK = new IndicadorProgramacionPK(intindicadorid, dtefechaprogramada);
    }

    @EmbeddedId
    public IndicadorProgramacionPK getIndicadorProgramacionPK() {
        return indicadorProgramacionPK;
    }

    public void setIndicadorProgramacionPK(IndicadorProgramacionPK indicadorProgramacionPK) {
        this.indicadorProgramacionPK = indicadorProgramacionPK;
    }

    @JsonIgnore
    @JoinColumn(name = "intindicadorid", referencedColumnName = "intindicadorid", insertable = false, updatable = false)
    @ManyToOne(optional = false)
    public Indicador getIndicador() {
        return indicador;
    }

    public void setIndicador(Indicador indicador) {
        this.indicador = indicador;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (indicadorProgramacionPK != null ? indicadorProgramacionPK.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof IndicadorProgramacion)) {
            return false;
        }
        IndicadorProgramacion other = (IndicadorProgramacion) object;
        if ((this.indicadorProgramacionPK == null && other.indicadorProgramacionPK != null) || (this.indicadorProgramacionPK != null && !this.indicadorProgramacionPK.equals(other.indicadorProgramacionPK))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.IndicadorProgramacion[ indicadorProgramacionPK=" + indicadorProgramacionPK + " ]";
    }
}
