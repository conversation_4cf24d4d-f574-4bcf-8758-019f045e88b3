package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "section")
public class Section extends StandardEntity<Section> implements DomainObjectInterface {
    private static final long serialVersionUID = 1L;
    
    public final String PREFIX = "SEC-";
    
    private String code = "";
    private String description = "";
    private Long areaId;
    private Long parentId;
    private Long masterId;
    private Integer status = 1;
    private Integer deleted = 0;
    
    private Area area;
    private Section parent;
    private Section master;

    public Section() {
    }
    
    
    @Id
    @Basic(optional = false)
    @Column(name = "section_id", nullable = false)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "area_id")
    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    @Column(name = "parent_id")
    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Column(name = "master")
    public Long getMasterId() {
        return masterId;
    }

    public void setMasterId(Long masterId) {
        this.masterId = masterId;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
 @OneToOne
    @JoinColumn(referencedColumnName = "area_id", name = "area_id",
            insertable = false, updatable = false)
    public Area getArea() {
        return area;
    }

    public void setArea(Area area) {
        this.area = area;
    }
 @OneToOne
    @JoinColumn(referencedColumnName = "section_id", name = "parent_id",
            insertable = false, updatable = false)
    public Section getParent() {
        return parent;
    }

    public void setParent(Section parent) {
        this.parent = parent;
    }
 @OneToOne
    @JoinColumn(referencedColumnName = "section_id", name = "master",
            insertable = false, updatable = false)
    public Section getMaster() {
        return master;
    }

    public void setMaster(Section master) {
        this.master = master;
    }
    
    
}