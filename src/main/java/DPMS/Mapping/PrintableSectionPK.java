package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class PrintableSectionPK implements Serializable {

    private Long printableId;
    private Long sectionId;

    public PrintableSectionPK() {
    }

    public PrintableSectionPK(Long printableId, Long sectionId) {
        this.printableId = printableId;
        this.sectionId = sectionId;
    }

    @Basic(optional = false)
    @Column(name = "printable_id")
    public Long getPrintableId() {
        return printableId;
    }

    public void setPrintableId(Long printableId) {
        this.printableId = printableId;
    }

    @Basic(optional = false)
    @Column(name = "section_id")
    public Long getSectionId() {
        return sectionId;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 59 * hash + Objects.hashCode(this.printableId);
        hash = 59 * hash + Objects.hashCode(this.sectionId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PrintableSectionPK other = (PrintableSectionPK) obj;
        if (!Objects.equals(this.printableId, other.printableId)) {
            return false;
        }
        if (!Objects.equals(this.sectionId, other.sectionId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PrintableSectionPK{" + "printableId=" + printableId + ", sectionId=" + sectionId + '}';
    }

}
