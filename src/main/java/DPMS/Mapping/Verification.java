package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import qms.document.entity.RequestSimple;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "verification")
public class Verification extends CompositeStandardEntity<VerificationPK> implements Serializable {

    private static final long serialVersionUID = -2839028090237970785L;

    public static final Integer VERIFICATION_ACCEPT = 1;
    public static final Integer VERIFICATION_MODIFIED = 2;
    public static final Integer VERIFICATION_RETURNED = 3;
    
    private VerificationPK id;
    private Integer lastAction;
    private Date lastModification;
    private Long requestId;
    private RequestSimple request;
    private List<VerificationLog> verificationLogList;
    private UserRef verificator;

    public Verification() {
    }

    public Verification(VerificationPK id) {
        this.id = id;
    }

    public Verification(VerificationPK id, Integer lastAction) {
        this.id = id;
        this.lastAction = lastAction;
    }

    public Verification(Long requestId, Long userId) {
        this.id = new VerificationPK(requestId, userId);
    }

    @Override
    @EmbeddedId
    public VerificationPK getId() {
        return id;
    }

    @Override
    public VerificationPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(VerificationPK id) {
        this.id = id;
    }
    
    @Basic(optional = false)
    @Column(name = "last_action")
    public Integer getLastAction() {
        return lastAction;
    }

    public void setLastAction(Integer lastAction) {
        this.lastAction = lastAction;
    }

    @Column(name = "last_modification")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getLastModification() {
        return lastModification;
    }

    public void setLastModification(Date lastModification) {
        this.lastModification = lastModification;
    }

    @Column(name = "request_id", insertable = false, updatable = false)
    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    @JsonIgnore
    @JoinColumn(name = "request_id", referencedColumnName = "id", insertable = false, updatable = false)
    @ManyToOne(optional = false, fetch = FetchType.EAGER)
    public RequestSimple getRequest() {
        return request;
    }

    public void setRequest(RequestSimple request) {
        this.request = request;
    }

    @Fetch(value = FetchMode.SUBSELECT)
    @OneToMany(mappedBy = "verification", fetch = FetchType.EAGER)
    public List<VerificationLog> getVerificationLogList() {
        return verificationLogList;
    }

    public void setVerificationLogList(List<VerificationLog> verificationLogList) {
        this.verificationLogList = verificationLogList;
    }

    /**
     * @return the verificator
     */
    @JoinColumn(name = "verificator_id", referencedColumnName = "user_id")
    @ManyToOne(fetch = FetchType.EAGER)
    public UserRef getVerificator() {
        return verificator;
    }

    /**
     * @param verificator the verificator to set
     */
    public void setVerificator(UserRef verificator) {
        this.verificator = verificator;
    }
        
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3246
        if (!(object instanceof Verification)) {
            return false;
        }
        Verification other = (Verification) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Verification[ id=" + id + " ]";
    }
}
