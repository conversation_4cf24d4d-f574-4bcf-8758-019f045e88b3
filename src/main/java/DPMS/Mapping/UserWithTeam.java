package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import Framework.Config.Utilities;
import bnext.reference.BusinessUnitRef;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.Transient;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import qms.framework.entity.OwnerTeam;
import qms.util.interfaces.IBusinessUnitDepartmentId;
import qms.util.interfaces.IBusinessUnitId;

/**
 * Este entity se utiliza para guardar la relación bidireccional con "OwnerTeam"
 * El resto de columnas se marcan con "insertable = false" y "updatable = false"
 *
 * <AUTHOR> Carlos Limas
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "users")
public class UserWithTeam extends StandardEntity<UserWithTeam>
        implements Serializable, DomainObjectInterface, IUser, IAuditableEntity, IBusinessUnitDepartmentId, IBusinessUnitId {

    private Date createdDate;
    private Date lastModifiedDate;
    private Integer deleted = 0;
    private Integer status = 1;
    private Long businessUnitDepartmentId;
    private Long businessUnitId;
    private Long createdBy;
    private Long lastModifiedBy;
    private String correo;
    private String cuenta;
    private String code = "";
    private String description = "";

    private OwnerTeam mainTeamOwner;
    private BusinessUnitDepartmentLite businessUnitDepartment;
    private BusinessUnitRef businessUnit;

    // Transient
    private Long groupId; // <-- Se utiliza como DTP para generar la relación de "groupId" y "owner"

    public UserWithTeam() {
    }

    public UserWithTeam(Long id) {
        this.id = id;
    }

    public UserWithTeam(UserWithTeam user, Long groupId) {
        this.id = user.getId();
        this.createdDate = user.getCreatedDate();
        this.lastModifiedDate = user.getLastModifiedDate();
        this.deleted = user.getDeleted();
        this.status = user.getStatus();
        this.businessUnitDepartmentId = user.getBusinessUnitDepartmentId();
        this.businessUnitId = user.getBusinessUnitId();
        this.createdBy = user.getCreatedBy();
        this.lastModifiedBy = user.getLastModifiedBy();
        this.correo = user.getCorreo();
        this.cuenta = user.getCuenta();
        this.code = user.getCode();
        this.description = user.getDescription();
        this.groupId = groupId;
        if (user.getMainTeamOwner() != null) {
            this.mainTeamOwner = new OwnerTeam(user.getMainTeamOwner().getId());
        }
        if (user.getBusinessUnitDepartment() != null) {
            this.businessUnitDepartment = new BusinessUnitDepartmentLite(user.getBusinessUnitDepartment().getId());
        }
    }

    public UserWithTeam(
        Long id,
        String cuenta,
        String code,
        String description,
        Long businessUnitId,
        Long businessUnitDepartmentId,
        String businessUnitName,
        String businessUnitDepartmentName
    ) {
        this.id = id;
        this.cuenta = cuenta;
        this.code = code;
        this.description = description;
        if (businessUnitDepartmentId != null) {
            this.businessUnitDepartment = new BusinessUnitDepartmentLite(
                businessUnitDepartmentId, businessUnitDepartmentName,
                businessUnitId, businessUnitName
            );
        }
    }

    public UserWithTeam(Long id, OwnerTeam mainTeamOwner) {
        this.id = id;
        this.mainTeamOwner = mainTeamOwner;
    }


    @Id
    @Basic(optional = false)
    @Column(name = "user_id", nullable = false, precision = 19, scale = 0, insertable = false, updatable = false)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255, insertable = false, updatable = false)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "first_name", nullable = false, length = 255, insertable = false, updatable = false)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status", insertable = false, updatable = false)
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted", insertable = false, updatable = false)
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Override
    @Column(name = "created_date", updatable = false)
    @CreatedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getCreatedDate() {
        return createdDate;
    }

    @Override
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    @Column(name = "last_modified_date")
    @LastModifiedDate
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    @Override
    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    @Column(name = "created_by", updatable = false)
    @CreatedBy
    public Long getCreatedBy() {
        return createdBy;
    }

    @Override
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    @Column(name = "last_modified_by")
    @LastModifiedBy
    public Long getLastModifiedBy() {
        return lastModifiedBy;
    }

    @Override
    public void setLastModifiedBy(Long lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }
    
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }
    
    @Override
    public boolean equals(Object object) {
        //Req 3242
        if (!(object instanceof UserRef)
                && !(object instanceof User)
                && !(object instanceof UserLite)
                && !(object instanceof UserWithTeam)
                && !(object instanceof User_old)) {
            return false;
        }
        IUser other = (IUser) object;
        return !((this.id == null && other.getId() != null) || (this.id != null && !this.id.equals(other.getId())));
    }

    @Override
    public String toString() {
        return "UserWithGroup{" + "code=" + code + '}';
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "main_team_owner_id", referencedColumnName = "owner_id")
    public OwnerTeam getMainTeamOwner() {
        return mainTeamOwner;
    }

    public void setMainTeamOwner(OwnerTeam mainTeamOwner) {
        this.mainTeamOwner = mainTeamOwner;
    }

    @Override
    @Column(name = "business_unit_department_id", insertable = false, updatable = false)
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    @Override
    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Column(name = "account", insertable = false, updatable = false)
    public String getCuenta() {
        return cuenta;
    }

    public void setCuenta(String cuenta) {
        this.cuenta = cuenta;
    }

    @Column(name = "mail", insertable = false, updatable = false)
    public String getCorreo() {
        return correo;
    }

    public void setCorreo(String correo) {
        this.correo = correo;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_department_id", referencedColumnName = "business_unit_department_id", insertable = false, updatable = false)
    public BusinessUnitDepartmentLite getBusinessUnitDepartment() {
        return businessUnitDepartment;
    }

    public void setBusinessUnitDepartment(BusinessUnitDepartmentLite businessUnitDepartment) {
        this.businessUnitDepartment = businessUnitDepartment;
    }

    @Override
    @Column(name = "business_unit_id", insertable = false, updatable = false)
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    @Override
    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }

    @JsonIgnore
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "business_unit_id", referencedColumnName = "business_unit_id", insertable = false, updatable = false)
    public BusinessUnitRef getBusinessUnit() {
        return businessUnit;
    }

    public void setBusinessUnit(BusinessUnitRef businessUnit) {
        this.businessUnit = businessUnit;
    }

    @Transient
    public String getBusinessUnitName() {
        if (this.businessUnitDepartment == null) {
            return Utilities.EMPTY_STRING;
        }
        if (businessUnitDepartment.getBusinessUnit() == null) {
            return Utilities.EMPTY_STRING;
        }
        return businessUnitDepartment.getBusinessUnit().getDescription();
    }

    @Transient
    public String getBusinessUnitDepartmentName() {
        if (this.businessUnitDepartment == null) {
            return Utilities.EMPTY_STRING;
        }
        return businessUnitDepartment.getDescription();
    }

    @Transient
    public Long getMainTeamOwnerId() {
        if (this.mainTeamOwner == null) {
            return null;
        }
        return mainTeamOwner.getId();
    }

    @Transient
    public String getMainTeamOwnerName() {
        if (this.mainTeamOwner == null) {
            return Utilities.EMPTY_STRING;
        }
        return mainTeamOwner.getDescription();
    }

    @Transient
    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

}
    
