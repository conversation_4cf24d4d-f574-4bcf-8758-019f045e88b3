package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class ProjectGoalPK implements Serializable {

    private Long intactividadid;
    private Long intproyectoid;

    public ProjectGoalPK() {
    }

    public ProjectGoalPK(Long intactividadid, Long intproyectoid) {
        this.intactividadid = intactividadid;
        this.intproyectoid = intproyectoid;
    }

    @Basic(optional = false)
    @Column(name = "intactividadid")
    public Long getActividadId() {
        return intactividadid;
    }

    public void setActividadId(Long intactividadid) {
        this.intactividadid = intactividadid;
    }

    @Basic(optional = false)
    @Column(name = "intproyectoid")
    public Long getProyectoId() {
        return intproyectoid;
    }

    public void setProyectoId(Long intproyectoid) {
        this.intproyectoid = intproyectoid;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 89 * hash + Objects.hashCode(this.intactividadid);
        hash = 89 * hash + Objects.hashCode(this.intproyectoid);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ProjectGoalPK other = (ProjectGoalPK) obj;
        if (!Objects.equals(this.intactividadid, other.intactividadid)) {
            return false;
        }
        if (!Objects.equals(this.intproyectoid, other.intproyectoid)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "ProjectGoalPK{" + "intactividadid=" + intactividadid + ", intproyectoid=" + intproyectoid + '}';
    }
}
