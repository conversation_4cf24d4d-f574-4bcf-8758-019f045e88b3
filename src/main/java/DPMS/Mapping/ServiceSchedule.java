package DPMS.Mapping;

import Framework.Config.DomainObject;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Cascade;
import org.hibernate.annotations.CascadeType;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import qms.device.interfaces.IServiceSchedule;
import qms.framework.initialload.ICreationTypeAware;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "service_schedule")
public class ServiceSchedule extends DomainObject implements Serializable, IServiceSchedule, ICreationTypeAware {
    public static final String PREFIX = "SSC";
    public static final Integer ACTIVE_STATUS = 1;
    public static final Integer INACTIVE_STATUS = 0;
    public static final Integer UNSCHEDULED = 99;

    private static final long serialVersionUID = 1L;
    
    private Long deviceId;
    private String code;
    private Long serviceTypeId;
    private Long responsibleId;
    private Date nextService;
    //private Long periodicityId;
    private Integer advanceDays;
    private Integer status;
    private Integer deleted;
    private Device device;
    private ServiceType serviceType;
    private UserRef responsible;
    private Periodicity periodicity;
    private Set<Measurement> measurements;
    private Integer creationType;

    public ServiceSchedule() {
    }
    
    public ServiceSchedule(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "service_schedule_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "creation_type")
    @Override
    public Integer getCreationType() {
        return creationType;
    }

    @Override
    public void setCreationType(Integer creationType) {
        this.creationType = creationType;
    }

    @Column(name = "device_id")
    @Override
    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    @Column(name = "service_type_id")
    public Long getServiceTypeId() {
        return serviceTypeId;
    }

    public void setServiceTypeId(Long serviceTypeId) {
        this.serviceTypeId = serviceTypeId;
    }

    @Column(name = "responsible_id")
    @Override
    public Long getResponsibleId() {
        return responsibleId;
    }

    public void setResponsibleId(Long responsibleId) {
        this.responsibleId = responsibleId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "next_service")
    public Date getNextService() {
        return nextService;
    }

    public void setNextService(Date nextService) {
        this.nextService = nextService;
    }

    @Column(name = "advance_days")
    public Integer getAdvanceDays() {
        return advanceDays;
    }

    public void setAdvanceDays(Integer advanceDays) {
        this.advanceDays = advanceDays;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @OneToOne
    @JsonIgnore
    @JoinColumn(referencedColumnName = "device_id", name = "device_id", insertable = false, updatable = false)
    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    @OneToOne    
    @JsonIgnore
    @JoinColumn(referencedColumnName = "service_type_id", name = "service_type_id", insertable = false, updatable = false)
    public ServiceType getServiceType() {
        return serviceType;
    }

    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }

    @JsonIgnore
    @OneToOne
    @JoinColumn(referencedColumnName = "user_id", name = "responsible_id", insertable = false, updatable = false)
    public UserRef getResponsible() {
        return responsible;
    }

    public void setResponsible(UserRef responsible) {
        this.responsible = responsible;
    }

    @OneToOne
    @Cascade(CascadeType.ALL)
    @JoinColumn(referencedColumnName = "intperiodicidadid", name = "periodicity_id", insertable = true, updatable = true)
    public Periodicity getPeriodicity() {
        return periodicity;
    }

    public void setPeriodicity(Periodicity periodicity) {
        this.periodicity = periodicity;
    }

    @OneToMany(fetch = FetchType.EAGER)
    @JoinTable(
              name = "service_schedule_measurement",
    joinColumns = {
        @JoinColumn( name = "service_schedule_id", referencedColumnName = "service_schedule_id")},
    inverseJoinColumns =
    @JoinColumn( name = "measurement_id", referencedColumnName = "measurement_id"))
    @Fetch(value = FetchMode.SUBSELECT)
    public Set<Measurement> getMeasurements() {
        return measurements;
    }

    public void setMeasurements(Set<Measurement> measurements) {
        this.measurements = measurements;
    }


    public void strMeasurements(HashSet<Measurement> measurements) {
        this.measurements = measurements;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
