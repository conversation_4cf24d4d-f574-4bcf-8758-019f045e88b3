package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import mx.bnext.core.daemon.util.IDaemonSettings;
import mx.bnext.core.pdf.util.IPdfViewerSettings;
import org.hibernate.annotations.Type;
import qms.framework.security.AuthenticationProtocol;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "settings")
/**
 * @Req 3206
 */
public class Settings extends DomainObject implements Serializable, IPdfViewerSettings, IDaemonSettings {

    private static final long serialVersionUID = 1L;

    public static final Integer DAYS_TO_ADVICE = 3;
    public static final String THEME_MODERN = "modern";
    public static final String THEME_LEGACY = "legacy";

    public enum CODE_SCOPE {
        LEVEL_APPLICATION(1), LEVEL_BUSINESS_UNIT(2);
        private final int value;
        private CODE_SCOPE(int value) {
            this.value = value;
        }
        public int getValue() {
            return value;
        }
        public static CODE_SCOPE fromValue(int value) {
            for (CODE_SCOPE v : CODE_SCOPE.values()) {
                if(value == v.value) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum LICENCE_TYPE {
        DEVELOPMENT(0), DEMO(1), QA(2), PRODUCTION(3);
        private final int value;
        private LICENCE_TYPE(int value) {
            this.value = value;
        }
        public int getValue() {
            return value;
        }
        public static LICENCE_TYPE fromValue(int value) {
            for (LICENCE_TYPE v : LICENCE_TYPE.values()) {
                if(value == v.value) {
                    return v;
                }
            }
            return null;
        }
    }


    public enum SSO_STATUS {
        SSO_ONLY, SSO_MIXED_LOGIN, SSO_OFF
    }

    private String traceRequestUrlRegExp;
    private String traceRequestUserIds;
    private String databasePath;
    private String zipPath;
    private String officePath;
    private String msWordPath;
    private String autodeskTvPath;
    private String autodeskTvPlotScriptPath;
    private String autodeskTvPlotFolderPath;
    private String autodeskTvFileParams;
    private String autodeskTvPlotParams;
    private String businessUnitLabelES;
    private String businessUnitLabelManyES;
    private String businessUnitLabelGenreES;
    private String businessUnitLabelEN;
    private String businessUnitLabelManyEN;
    private String businessUnitLabelGenreEN;
    private String departmentUnitLabelES;
    private String departmentUnitLabelManyES;
    private String departmentUnitLabelGenreES;
    private String departmentUnitLabelEN;
    private String departmentUnitLabelManyEN;
    private String departmentUnitLabelGenreEN;
    private String areaUnitLabelES;
    private String areaUnitLabelManyES;
    private String areaUnitLabelGenreES;
    private String areaUnitLabelEN;
    private String areaUnitLabelManyEN;
    private String areaUnitLabelGenreEN;
    private Integer allowSwitchSearchInSubfolders;
    private Integer renewByPosition;

    private Integer filePdfPagesEnabledCount = 1000;
    private Integer interfaceTRESS;
    private Integer msConversion;
    private Integer sendMail;
    private Integer sendFormInfoMail;
    private Integer useAuthenticationTokenMail;
    private String adminMail;
    private String interfaceTRESSProfileCode;
    private String pathToBossEmployeesCsv;
    private String mailSubject;
    private String smtpHost;
    private String smtpUid;
    private String smtpPsw;
    private String hashedSmtpPsw;
    private Integer smtpPort;
    private Integer smtpAuthentication = 0;
    private Integer smtpUserset = 0;
    private Integer smtpPrintDebugInfo = 0;
    private Integer enableBrowserApmTracking = 0;
    private String smtpProtocol = AuthenticationProtocol.NONE.getCode();
    private String lang;
    private String locale;
    private String systemId;
    private Integer showManuals;
    private String folderName;
    private String url;
    private String host;
    private Integer officeConversion;
    private Integer savePostPdf;
    private Integer dirPdfs;
    private Integer mailToAuthor;
    private Integer mailToDocumentManager;
    private Integer mailToBusinessUnitManager;
    private Integer mailToDepartmentManager;
    private Integer mailToAssignedReaders;
    private Integer mailToAuthorizers;
    private Integer mailToBusinessUnitPermission;
    private Integer mailToProcessPermission;
    private Integer mailToDepartmentPermission;
    private Integer mailToUserPermission;
    private Integer mailIssueDefault;
    private String licencetypeLeyendDevelopment;
    private String licencetypeLeyendDemo;
    private String licencetypeLeyendQA;
    private String licencetypeLeyendProduction;
    private Integer licenceType;
    private String dummyMail;
    private String documentInitialVersion;
    private Integer positionForControlledCopy;
    private String systemColor;
    private String systemSecondaryColor;
    private String systemTheme;
    private Long defaultPosition;
    private Long defaultFileId;
    private Long dbReplicationDatabaseConnectionId;
    private Long welcomeBgId;
    private Integer pageCountDefault;
    private String rcKey;
    private String toPdfWord;
    private String toPdfPp;
    private String toPdfXl;
    private Integer mailLifeTime;
    private Date backupDate;
    private Date fullPendingCountLastRefreshDate;
    private Integer backupPeriod;
    private String loginMessage;
    private String welcomeMessage;
    private String documentsFolder;
    private String auditFolder;
    private String requestFolder;
    private String backupFolder;
    private String listFolder;
    private String jspUploadPath;
    private String filesContext;
    private String documentsFolderExt;
    private String requestFolderExt;
    private String folderImgReport;
    private String folderImgReportExt;
    private String folderListExt;
    private String backupFolderExt;
    private String backupExternalPath;
    private String fileFolder;
    //Devices
    private Integer serviceToApprove;
    private Integer schedulingToApprove;
    private Integer deviceEditImplementation = 0;
    //Documents
    private Integer showReadersForPrint;
    // ====  Survey settings
    private Integer autosaveTime;  //time in minutes
    private String exitURL;
    private String adAddress;
    private String adUsername;
    private String adPassword;
    private String adAuthentication;
    private String adReferral;
    private String adDefaultPositionCode;
    private String adDefaultLang;
    private String adDefaultLocale;
    private String adDefaultSchema;
    private String adDefaultfolder;
    private String adDefaultDocumentCodeTask;
    private String newDigit;
    private Date nightlyProcessTime;
    private Integer complaintManagerWhoAccept;
    private Integer complaintManagerNotification;
    private Integer filterComplantResponsibleByDepartment;
    private String complaintsCodePrefix;
    private Integer reapproveByDocumentManager;
    private Integer requestReturnsToVerification;
    private Integer actionManagerWhoAccept;
    private Integer actionManagerWhoEvaluate;
    private Integer findingShowAllUsers;
    private Integer requireAnalysisCause;
    private Integer adminCanAttendAnyFinding;
    private Integer auditedViewSurvey;
    private Integer maxChangeDateAllowed;
    private String programConvertToPDF;
    private String officeToPDFPath;
    private String pathPublisher;
    private String pathVisio;
    private String pathProject;
    private Long officeToPDFAliveTime;
    private Integer officeToPDFMaxWaitingTasks;
    private Integer officeFileAliveTime = 60 * 60;
    private Integer enabledDailyTaskDocuments;
    private Integer dailyTaskDocuments = 100;
    private Integer enabledCleanupPdfRecords;
    private Integer maxCleanupPdfRecords;
    private Integer daysAnticipation;

    private Integer trackGeolocation = 1;

    private Integer mailSenderPoolSize;
    private Long mailSenderAliveTime;
    private Integer mailSenderMaxWaitingTasks;

    private Integer pdfImagePoolSize;
    private Long pdfImageAliveTime;
    private Integer pdfImageMaxWaitingTasks;
    private Integer pdfImageDpi = 300;
    private Integer dwgImageDpi = 72;
    private String pdfImageFormat = "webp";
    private Integer pdfMaximumPersistedPages = 20;

    private Integer expiredDocumentPendingModuleManager;
    private Integer expiredDocumentPendingDocumentManager;
    private Integer expiredDocumentMailModuleManager;
    private Integer expiredDocumentMailDocumentManager;
    private Integer expiredDocumentMailLastAuthor;
    private Integer expiredRequestMailDocumentManager = 1;
    private Integer downloadAttemptsLimit;

    private Integer apePoolSize;
    private Long apeAliveTime;
    private Integer apeMaxWaitingTasks;


    private Integer shareDocuments;
    private Integer externalLink;

    private Integer searchSubFolder;
    private String printingDateFormat;
    private String printingDateTimeFormat;
    private Long reportLogoId;
    private Long googleDriveKeyId;

    private Integer minimumCodeDigits;
    private String officeToPdfParams = "";

    private Integer localizeEntities = 1;
    private Integer documentCodeScope = 1;
    private Integer pendingMaxCount = 5;

    private Integer enableFileCache;
    private String fileCachePath;
    private Integer googleDriveIntegration = 0;
    private String googleApiKey;
    private String googleClientId;
    private String googleApplicationId;
    private Integer freezedFormAnswersRun = 0;
    private Integer refreshPendingsNextRun = 0;
    private Integer ssoActive = 0;
    private Integer enableLoginForm = 1;
    private Integer ssoDomainValidation = 1;
    // Apagar pendientes
    private Integer readers;
    private String serviceDeskContact;
    private String serviceDeskMail;
    private String adFavoriteTaskCodes;
    private String maximoFavoriteTaskCode;

    private Long fileCacheStoreDays;
    private Integer sessionMaxInactiveTime;
    private Integer closeOfficePrograms;
    private String pdfGeneratorUrl;
    private String pdfGeneratorAccount;
    private String pdfGeneratorPassword;
    private Long pdfGeneratorTimeout;
    private Integer enableTimework = 0;
    private String timeworkUrl;
    private String timeworkMails;
    private String timeworkSyncType;
    private Integer exchangeRateConversorEnabled;
    private String exchangeRateSyncType;
    private Integer allowManyTabsPerPending = 0;
    private Integer initializedApe = 0;
    private Integer showPositionsForCopies;
    private Integer printReceiptEnabled;
    private Integer numAttemptsSession;
    private Integer timeoutUnlocking;
    private Long isotypeLogoId;
    private String timeZone;
    private String javaUserAgent;
    private Long cacheQuerySyncAliveTime;
    private Integer cacheQuerySyncImagePoolSize;
    private Integer cacheQuerySyncImageMaxWaitingTasks;
    private Integer timeLimitToModifyTimesheet;
    private Integer pendingHistory;
    private Integer indexRebuild;
    private Integer connQueryTimeout;
    private Integer useSecondLevelCache;
    private Integer yearsToAddCalendar;
    private Long uploadFileMaxSizeBytes;
    private Long totalUploadedBytes;
    private Integer enableRegistration;
    private Integer enableTimeworkSyncTest;
    private Long maxConnectionTimeout;
    private Long maxRequestTimeout;
    private Integer preloadSecondLevelCache;
    private Integer enableDbReplication;
    private Integer newsBySociety;
    private Long limitApeCachePreload;
    private Integer oidcEnabled;
    private Integer oidcProvider;
    private String oidcAccountAttribute;
    private Integer oidcBnextAccountAttribute;
    private Integer oidcAuthenticationType;
    private String oidcDomain;
    private String oidcAuthorizationServerId;
    private String oidcClientId;
    private String oidcClientSecret;
    private Integer oidcAllowRegisterUser;
    private Integer enabledLandingPage;
    private String landingPageUrl;
    private Integer regionCatalogEnabled;
    private Integer allowExportAllRecords;
    private Integer zoneCatalogEnabled;
    private Boolean updateAllTimezoneActivityColumnsToSystemTimezone = false;
    //Esta propiedad es para validar que se generen por primza vez los slim reportes con todos los campos de
    //formularios y se migren los permisos, lo equivalente a la pantalla legacy de answers
    private Boolean pendingBuildAllSlimReports = false;
    //Esta propiedad es para validar que se regeneren los slim-reportes
    private Boolean pendingRegenerateAllSlimReports = false;
    private Boolean regenerateAllSlimReportsAfterFreeze = false;
    private Integer formReportMaxColumns;
    //Poner en true para apagar el recalculo de reportes cuando se modifica un formulario.
    private Boolean shutdownRecalculateSlimReport = false;
    /**
     * ---- La bandera de 'freezed_survey_answers_run' TAMBIEN enciende esta funcionalidad ---
     * Poner en TRUE para recalcular las tablas de:
     *
     * - survey_answer_migration
     * - survey_answer_migration_column
     *
     * */
    private Boolean recalculateSurveyMigration = false;
    // Area
    private Boolean isAreaCustomFieldsEnabled = false;
    private String labelAreaField1;
    private String labelAreaField2;
    private String labelAreaField3;
    private String labelAreaField4;
    private String labelAreaField5;
    private String labelAreaField6;
    private String labelAreaField7;
    private String labelAreaField8;
    private String labelAreaField9;
    private String labelAreaField10;
    private String labelAreaField11;
    private String labelAreaField12;
    private String labelAreaField13;
    private String labelAreaField14;
    private String labelAreaField15;
    private String labelAreaField16;
    private String labelAreaField17;
    private String labelAreaField18;
    private String labelAreaField19;
    private String labelAreaField20;
    private String labelAreaField21;
    private String labelAreaField22;
    private String labelAreaField23;
    private String labelAreaField24;
    private String labelAreaField25;
    private String labelAreaField26;
    private Integer additionalExtraAreaFields;
    private Boolean recalculateWorkflowPreviewDataRun = false;
    private Boolean recalculateWorkflowFormRequestDataRun = false;
    private Boolean userDirectBossOptional = false;

    private Long apeLazyAliveTime;
    private Integer apeLazyMaxWaitingTasks;
    private Integer queryPdfDeletionTimeOut;
    private Long pdfCleanerAliveTime;
    private Integer pdfCleanerMaxWaitingTasks;
    private Boolean archived = false;
    private Boolean mapAllSurveyAnswersColumns = false;

    public Settings() {
        this.autosaveTime = 10;
        this.exitURL = "";
        this.timeZone = "America/Monterrey";
    }

    public Settings(Long id) {
        this.id = id;
    }

    @Column(name = "is_area_custom_fields_enabled")
    @Type(type = "numeric_boolean")
    public Boolean getIsAreaCustomFieldsEnabled() {
        return Boolean.TRUE.equals(isAreaCustomFieldsEnabled);
    }

    public void setIsAreaCustomFieldsEnabled(Boolean isAreaCustomFieldsEnabled) {
        this.isAreaCustomFieldsEnabled = isAreaCustomFieldsEnabled;
    }

    @Column(name = "label_area_field1")
    public String getLabelAreaField1() {
        return labelAreaField1;
    }

    public void setLabelAreaField1(String labelAreaField1) {
        this.labelAreaField1 = labelAreaField1;
    }

    @Column(name = "label_area_field2")
    public String getLabelAreaField2() {
        return labelAreaField2;
    }

    public void setLabelAreaField2(String labelAreaField2) {
        this.labelAreaField2 = labelAreaField2;
    }

    @Column(name = "label_area_field3")
    public String getLabelAreaField3() {
        return labelAreaField3;
    }

    public void setLabelAreaField3(String labelAreaField3) {
        this.labelAreaField3 = labelAreaField3;
    }

    @Column(name = "label_area_field4")
    public String getLabelAreaField4() {
        return labelAreaField4;
    }

    public void setLabelAreaField4(String labelAreaField4) {
        this.labelAreaField4 = labelAreaField4;
    }

    @Column(name = "label_area_field5")
    public String getLabelAreaField5() {
        return labelAreaField5;
    }

    public void setLabelAreaField5(String labelAreaField5) {
        this.labelAreaField5 = labelAreaField5;
    }

    @Column(name = "label_area_field6")
    public String getLabelAreaField6() {
        return labelAreaField6;
    }

    public void setLabelAreaField6(String labelAreaField6) {
        this.labelAreaField6 = labelAreaField6;
    }

    @Column(name = "label_area_field7")
    public String getLabelAreaField7() {
        return labelAreaField7;
    }

    public void setLabelAreaField7(String labelAreaField7) {
        this.labelAreaField7 = labelAreaField7;
    }

    @Column(name = "label_area_field8")
    public String getLabelAreaField8() {
        return labelAreaField8;
    }

    public void setLabelAreaField8(String labelAreaField8) {
        this.labelAreaField8 = labelAreaField8;
    }

    @Column(name = "label_area_field9")
    public String getLabelAreaField9() {
        return labelAreaField9;
    }

    public void setLabelAreaField9(String labelAreaField9) {
        this.labelAreaField9 = labelAreaField9;
    }

    @Column(name = "label_area_field10")
    public String getLabelAreaField10() {
        return labelAreaField10;
    }

    public void setLabelAreaField10(String labelAreaField10) {
        this.labelAreaField10 = labelAreaField10;
    }

    @Column(name = "label_area_field11")
    public String getLabelAreaField11() {
        return labelAreaField11;
    }

    public void setLabelAreaField11(String labelAreaField11) {
        this.labelAreaField11 = labelAreaField11;
    }

    @Column(name = "label_area_field12")
    public String getLabelAreaField12() {
        return labelAreaField12;
    }

    public void setLabelAreaField12(String labelAreaField12) {
        this.labelAreaField12 = labelAreaField12;
    }

    @Column(name = "label_area_field13")
    public String getLabelAreaField13() {
        return labelAreaField13;
    }

    public void setLabelAreaField13(String labelAreaField13) {
        this.labelAreaField13 = labelAreaField13;
    }

    @Column(name = "label_area_field14")
    public String getLabelAreaField14() {
        return labelAreaField14;
    }

    public void setLabelAreaField14(String labelAreaField14) {
        this.labelAreaField14 = labelAreaField14;
    }

    @Column(name = "label_area_field15")
    public String getLabelAreaField15() {
        return labelAreaField15;
    }

    public void setLabelAreaField15(String labelAreaField15) {
        this.labelAreaField15 = labelAreaField15;
    }

    @Column(name = "label_area_field16")
    public String getLabelAreaField16() {
        return labelAreaField16;
    }

    public void setLabelAreaField16(String labelAreaField16) {
        this.labelAreaField16 = labelAreaField16;
    }

    @Column(name = "label_area_field17")
    public String getLabelAreaField17() {
        return labelAreaField17;
    }

    public void setLabelAreaField17(String labelAreaField17) {
        this.labelAreaField17 = labelAreaField17;
    }

    @Column(name = "label_area_field18")
    public String getLabelAreaField18() {
        return labelAreaField18;
    }

    public void setLabelAreaField18(String labelAreaField18) {
        this.labelAreaField18 = labelAreaField18;
    }

    @Column(name = "label_area_field19")
    public String getLabelAreaField19() {
        return labelAreaField19;
    }

    public void setLabelAreaField19(String labelAreaField19) {
        this.labelAreaField19 = labelAreaField19;
    }

    @Column(name = "label_area_field20")
    public String getLabelAreaField20() {
        return labelAreaField20;
    }

    public void setLabelAreaField20(String labelAreaField20) {
        this.labelAreaField20 = labelAreaField20;
    }

    @Column(name = "label_area_field21")
    public String getLabelAreaField21() {
        return labelAreaField21;
    }

    public void setLabelAreaField21(String labelAreaField21) {
        this.labelAreaField21 = labelAreaField21;
    }

    @Column(name = "label_area_field22")
    public String getLabelAreaField22() {
        return labelAreaField22;
    }

    public void setLabelAreaField22(String labelAreaField22) {
        this.labelAreaField22 = labelAreaField22;
    }

    @Column(name = "label_area_field23")
    public String getLabelAreaField23() {
        return labelAreaField23;
    }

    public void setLabelAreaField23(String labelAreaField23) {
        this.labelAreaField23 = labelAreaField23;
    }

    @Column(name = "label_area_field24")
    public String getLabelAreaField24() {
        return labelAreaField24;
    }

    public void setLabelAreaField24(String labelAreaField24) {
        this.labelAreaField24 = labelAreaField24;
    }

    @Column(name = "label_area_field25")
    public String getLabelAreaField25() {
        return labelAreaField25;
    }

    public void setLabelAreaField25(String labelAreaField25) {
        this.labelAreaField25 = labelAreaField25;
    }

    @Column(name = "label_area_field26")
    public String getLabelAreaField26() {
        return labelAreaField26;
    }

    public void setLabelAreaField26(String labelAreaField26) {
        this.labelAreaField26 = labelAreaField26;
    }

    @Column(name = "additional_extra_area_fields")
    public Integer getAdditionalExtraAreaFields() {
        return additionalExtraAreaFields;
    }

    public void setAdditionalExtraAreaFields(Integer additionalExtraAreaFields) {
        this.additionalExtraAreaFields = additionalExtraAreaFields;
    }

    @Basic(optional = false)
    @Column(name = "database_path")
    public String getDatabasePath() {
        return databasePath;
    }

    public void setDatabasePath(String databasePath) {
        this.databasePath = databasePath;
    }

    @Basic(optional = false)
    @Column(name = "zip_path")
    public String getZipPath() {
        return zipPath;
    }

    public void setZipPath(String zipPath) {
        this.zipPath = zipPath;
    }

    @Column(name = "renew_by_position")
    public Integer getRenewByPosition() {
        return renewByPosition;
    }

    public void setRenewByPosition(Integer renewByPosition) {
        this.renewByPosition = renewByPosition;
    }
    
    @Basic(optional = false)
    @Column(name = "office_path")
    @Override
    public String getOfficePath() {
        return officePath;
    }

    public void setOfficePath(String officePath) {
        this.officePath = officePath;
    }

    @Basic(optional = false)
    @Column(name = "ms_word_path")
    public String getMsWordPath() {
        return msWordPath;
    }

    public void setMsWordPath(String msWordPath) {
        this.msWordPath = msWordPath;
    }

    @Basic(optional = false)
    @Column(name = "ms_conversion")
    public Integer getMsConversion() {
        return msConversion;
    }

    public void setMsConversion(Integer msConversion) {
        this.msConversion = msConversion;
    }

    @Basic(optional = false)
    @Column(name = "send_mail")
    public Integer getSendMail() {
        return sendMail;
    }

    public void setSendMail(Integer sendMail) {
        this.sendMail = sendMail;
    }
    
    @Basic(optional = false)
    @Column(name = "send_form_info_mail")
    public Integer getSendFormInfoMail() {
        return sendFormInfoMail;
    }

    public void setSendFormInfoMail(Integer sendFormInfoMail) {
        this.sendFormInfoMail = sendFormInfoMail;
    }


    @Basic(optional = false)
    @Column(name = "use_auth_token_mail")
    public Integer getUseAuthenticationTokenMail() {
        return useAuthenticationTokenMail;
    }

    public void setUseAuthenticationTokenMail(Integer useAuthenticationTokenMail) {
        this.useAuthenticationTokenMail = useAuthenticationTokenMail;
    }
    
    @Column(name = "admin_mail")
    public String getAdminMail() {
        return adminMail;
    }

    public void setAdminMail(String adminMail) {
        this.adminMail = adminMail;
    }

    @Id
    @Basic
    @Column(name = "id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "mail_subject")
    public String getMailSubject() {
        return mailSubject;
    }

    public void setMailSubject(String mailSubject) {
        this.mailSubject = mailSubject;
    }

    @Basic(optional = false)
    @Column(name = "smtp_host")
    public String getSmtpHost() {
        return smtpHost;
    }

    public void setSmtpHost(String smtpHost) {
        this.smtpHost = smtpHost;
    }

    @Basic(optional = false)
    @Column(name = "smtp_uid")
    public String getSmtpUid() {
        return smtpUid;
    }

    public void setSmtpUid(String smtpUid) {
        this.smtpUid = smtpUid;
    }

    @Basic(optional = false)
    @Column(name = "smtp_psw")
    public String getSmtpPsw() {
        return smtpPsw;
    }

    public void setSmtpPsw(String smtpPsw) {
        this.smtpPsw = smtpPsw;
    }

    @Basic(optional = false)
    @Column(name = "hashed_smtp_psw")
    public String getHashedSmtpPsw() {
        return hashedSmtpPsw;
    }

    public void setHashedSmtpPsw(String hashedSmtpPsw) {
        this.hashedSmtpPsw = hashedSmtpPsw;
    }

    @Basic(optional = false)
    @Column(name = "smtp_port")
    public Integer getSmtpPort() {
        return smtpPort;
    }

    public void setSmtpPort(Integer smtpPort) {
        this.smtpPort = smtpPort;
    }

    @Basic(optional = false)
    @Column(name = "smtp_authentication")
    public Integer getSmtpAuthentication() {
        return smtpAuthentication;
    }

    public void setSmtpAuthentication(Integer smtpAuthentication) {
        this.smtpAuthentication = smtpAuthentication;
    }

    @Basic(optional = false)
    @Column(name = "smtp_userset")
    public Integer getSmtpUserset() {
        return smtpUserset;
    }

    public void setSmtpUserset(Integer smtpUserset) {
        this.smtpUserset = smtpUserset;
    }

    @Basic(optional = false)
    @Column(name = "smtp_print_debug_info")
    public Integer getSmtpPrintDebugInfo() {
        return smtpPrintDebugInfo;
    }

    public void setSmtpPrintDebugInfo(Integer smtpPrintDebugInfo) {
        this.smtpPrintDebugInfo = smtpPrintDebugInfo;
    }

    @Basic(optional = false)
    @Column(name = "enable_browser_apm_tracking")
    public Integer getEnableBrowserApmTracking() {
        return enableBrowserApmTracking;
    }

    public void setEnableBrowserApmTracking(Integer enableBrowserApmTracking) {
        this.enableBrowserApmTracking = enableBrowserApmTracking;
    }

    @Basic(optional = false)
    @Column(name = "smtp_protocol")
    public String getSmtpProtocol() {
        return smtpProtocol;
    }

    public void setSmtpProtocol(String smtpProtocol) {
        this.smtpProtocol = smtpProtocol;
    }

    @Column(name = "business_unit_label_es")
    public String getBusinessUnitLabelES() {
        return businessUnitLabelES;
    }

    @Column(name = "business_unit_label_es_many")
    public String getBusinessUnitLabelManyES() {
        return businessUnitLabelManyES;
    }

    @Column(name = "business_unit_label_es_genre")
    public String getBusinessUnitLabelGenreES() {
        return businessUnitLabelGenreES;
    }

    @Column(name = "business_unit_label_en")
    public String getBusinessUnitLabelEN() {
        return businessUnitLabelEN;
    }

    @Column(name = "business_unit_label_en_many")
    public String getBusinessUnitLabelManyEN() {
        return businessUnitLabelManyEN;
    }

    @Column(name = "business_unit_label_en_genre")
    public String getBusinessUnitLabelGenreEN() {
        return businessUnitLabelGenreEN;
    }

    public void setBusinessUnitLabelES(String businessUnitLabelES) {
        this.businessUnitLabelES = businessUnitLabelES;
    }

    public void setBusinessUnitLabelManyES(String businessUnitLabelManyES) {
        this.businessUnitLabelManyES = businessUnitLabelManyES;
    }

    public void setBusinessUnitLabelGenreES(String businessUnitLabelGenreES) {
        this.businessUnitLabelGenreES = businessUnitLabelGenreES;
    }

    public void setBusinessUnitLabelEN(String businessUnitLabelEN) {
        this.businessUnitLabelEN = businessUnitLabelEN;
    }

    public void setBusinessUnitLabelManyEN(String businessUnitLabelManyEN) {
        this.businessUnitLabelManyEN = businessUnitLabelManyEN;
    }

    public void setBusinessUnitLabelGenreEN(String businessUnitLabelGenreEN) {
        this.businessUnitLabelGenreEN = businessUnitLabelGenreEN;
    }

    @Column(name = "department_unit_label_es")
    public String getDepartmentUnitLabelES() {
        return departmentUnitLabelES;
    }


    @Column(name = "department_unit_label_es_many")
    public String getDepartmentUnitLabelManyES() {
        return departmentUnitLabelManyES;
    }

    @Column(name = "department_unit_label_es_genre")
    public String getDepartmentUnitLabelGenreES() {
        return departmentUnitLabelGenreES;
    }

    @Column(name = "department_unit_label_en")
    public String getDepartmentUnitLabelEN() {
        return departmentUnitLabelEN;
    }

    @Column(name = "department_unit_label_en_many")
    public String getDepartmentUnitLabelManyEN() {
        return departmentUnitLabelManyEN;
    }

    @Column(name = "department_unit_label_en_genre")
    public String getDepartmentUnitLabelGenreEN() {
        return departmentUnitLabelGenreEN;
    }

    public void setDepartmentUnitLabelES(String departmentUnitLabelES) {
        this.departmentUnitLabelES = departmentUnitLabelES;
    }

    public void setDepartmentUnitLabelManyES(String departmentUnitLabelManyES) {
        this.departmentUnitLabelManyES = departmentUnitLabelManyES;
    }

    public void setDepartmentUnitLabelGenreES(String departmentUnitLabelGenreES) {
        this.departmentUnitLabelGenreES = departmentUnitLabelGenreES;
    }

    public void setDepartmentUnitLabelEN(String departmentUnitLabelEN) {
        this.departmentUnitLabelEN = departmentUnitLabelEN;
    }

    public void setDepartmentUnitLabelManyEN(String departmentUnitLabelManyEN) {
        this.departmentUnitLabelManyEN = departmentUnitLabelManyEN;
    }

    public void setDepartmentUnitLabelGenreEN(String departmentUnitLabelGenreEN) {
        this.departmentUnitLabelGenreEN = departmentUnitLabelGenreEN;
    }
    /**
     * Devuelve el lenguaje: "es", "en", etc.
     * @return
     */
    @Override
    @Basic(optional = false)
    @Column(name = "lang")
    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    /**
     * Devuelve la localización: "MX", "US", etc.
     * @return
     */
    @Override
    @Basic(optional = false)
    @Column(name = "locale")
    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    @Basic(optional = false)
    @Column(name = "system_id", insertable = false, updatable = false)
    @Override
    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    @Basic(optional = false)
    @Column(name = "show_manuals")
    public Integer getShowManuals() {
        return showManuals;
    }

    public void setShowManuals(Integer showManuals) {
        this.showManuals = showManuals;
    }

    @Column(name = "folder_name")
    public String getFolderName() {
        return folderName;
    }

    public void setFolderName(String folderName) {
        this.folderName = folderName;
    }

    @Basic(optional = false)
    @Column(name = "url")
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Basic(optional = false)
    @Column(name = "host")
    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    @Basic(optional = false)
    @Column(name = "office_conversion")
    public Integer getOfficeConversion() {
        return officeConversion;
    }

    public void setOfficeConversion(Integer officeConversion) {
        this.officeConversion = officeConversion;
    }

    @Basic(optional = false)
    @Column(name = "save_post_pdf")
    public Integer getSavePostPdf() {
        return savePostPdf;
    }

    public void setSavePostPdf(Integer savePostPdf) {
        this.savePostPdf = savePostPdf;
    }

    @Basic(optional = false)
    @Column(name = "dir_pdfs")
    public Integer getDirPdfs() {
        return dirPdfs;
    }

    public void setDirPdfs(Integer dirPdfs) {
        this.dirPdfs = dirPdfs;
    }


    @Basic(optional = false)
    @Column(name = "readers")
    public Integer getReaders() {
        return readers;
    }

    public void setReaders(Integer readers) {
        this.readers = readers;
    }

    @Basic(optional = false)
    @Column(name = "mail_issue_default")
    public Integer getMailIssueDefault() {
        return mailIssueDefault;
    }

    public void setMailIssueDefault(Integer mailIssueDefault) {
        this.mailIssueDefault = mailIssueDefault;
    }

    @Basic(optional = false)
    @Column(name = "document_initial_version")
    public String getDocumentInitialVersion() {
        return documentInitialVersion;
    }

    public void setDocumentInitialVersion(String documentInitialVersion) {
        this.documentInitialVersion = documentInitialVersion;
    }


    /**
     * @return the positionForControlledCopy
     */
    @Basic(optional = false)
    @Column(name = "position_for_controlled_copy")
    public Integer getPositionForControlledCopy() {
        return positionForControlledCopy;
    }

    /**
     * @param positionForControlledCopy the positionForControlledCopy to set
     */
    public void setPositionForControlledCopy(Integer positionForControlledCopy) {
        this.positionForControlledCopy = positionForControlledCopy;
    }

    @Basic(optional = false)
    @Column(name = "system_color")
    public String getSystemColor() {
        return systemColor;
    }

    public void setSystemColor(String systemColor) {
        this.systemColor = systemColor;
    }

    //systemSecondaryColor
    @Basic(optional = false)
    @Column(name = "system_secondary_color")
    public String getSystemSecondaryColor() {
        return systemSecondaryColor;
    }

    public void setSystemSecondaryColor(String systemSecondaryColor) {
        this.systemSecondaryColor = systemSecondaryColor;
    }

    @Column(name = "system_theme")
    public String getSystemTheme() {
        return systemTheme;
    }

    public void setSystemTheme(String systemTheme) {
        this.systemTheme = systemTheme;
    }

    @Basic(optional = false)
    @Column(name = "default_position")
    public Long getDefaultPosition() {
        return defaultPosition;
    }

    public void setDefaultPosition(Long defaultPosition) {
        this.defaultPosition = defaultPosition;
    }

    @Basic(optional = false)
    @Column(name = "page_count_default")
    public Integer getPageCountDefault() {
        return pageCountDefault;
    }

    public void setPageCountDefault(Integer pageCountDefault) {
        this.pageCountDefault = pageCountDefault;
    }

    @Basic(optional = false)
    @Column(name = "rc_key")
    public String getRcKey() {
        return rcKey;
    }

    public void setRcKey(String rcKey) {
        this.rcKey = rcKey;
    }

    @Basic(optional = false)
    @Column(name = "to_pdf_word")
    public String getToPdfWord() {
        return toPdfWord;
    }

    public void setToPdfWord(String toPdfWord) {
        this.toPdfWord = toPdfWord;
    }

    @Basic(optional = false)
    @Column(name = "to_pdf_pp")
    public String getToPdfPp() {
        return toPdfPp;
    }

    public void setToPdfPp(String toPdfPp) {
        this.toPdfPp = toPdfPp;
    }

    @Column(name = "to_pdf_xl")
    public String getToPdfXl() {
        return toPdfXl;
    }

    public void setToPdfXl(String toPdfXl) {
        this.toPdfXl = toPdfXl;
    }

    @Basic(optional = false)
    @Column(name = "mail_life_time")
    public Integer getMailLifeTime() {
        return mailLifeTime;
    }

    public void setMailLifeTime(Integer mailLifeTime) {
        this.mailLifeTime = mailLifeTime;
    }

    @Column(name = "backup_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getBackupDate() {
        return backupDate;
    }

    public void setBackupDate(Date backupDate) {
        this.backupDate = backupDate;
    }

    @Basic(optional = false)
    @Column(name = "backup_period")
    public Integer getBackupPeriod() {
        return backupPeriod;
    }

    public void setBackupPeriod(Integer backupPeriod) {
        this.backupPeriod = backupPeriod;
    }

    @Basic(optional = false)
    @Column(name = "welcome_message")
    public String getWelcomeMessage() {
        return welcomeMessage;
    }

    public void setWelcomeMessage(String welcomeMessage) {
        this.welcomeMessage = welcomeMessage;
    }

    @Column(name = "login_message")
    public String getLoginMessage() {
        return loginMessage;
    }

    public void setLoginMessage(String loginMessage) {
        this.loginMessage = loginMessage;
    }



    @Basic(optional = false)
    @Column(name = "documents_folder")
    public String getDocumentsFolder() {
        return documentsFolder;
    }

    public void setDocumentsFolder(String documentsFolder) {
        this.documentsFolder = documentsFolder;
    }

    @Basic(optional = false)
    @Column(name = "audit_folder")
    public String getAuditFolder() {
        return auditFolder;
    }

    public void setAuditFolder(String auditFolder) {
        this.auditFolder = auditFolder;
    }

    @Basic(optional = false)
    @Column(name = "request_folder")
    public String getRequestFolder() {
        return requestFolder;
    }

    public void setRequestFolder(String requestFolder) {
        this.requestFolder = requestFolder;
    }

    @Basic(optional = false)
    @Column(name = "backup_folder")
    public String getBackupFolder() {
        return backupFolder;
    }

    public void setBackupFolder(String backupFolder) {
        this.backupFolder = backupFolder;
    }

    @Column(name = "list_folder")
    public String getListFolder() {
        return listFolder;
    }

    public void setListFolder(String listFolder) {
        this.listFolder = listFolder;
    }

    @Basic(optional = false)
    @Column(name = "jsp_upload_path")
    public String getJspUploadPath() {
        return jspUploadPath;
    }

    public void setJspUploadPath(String jspUploadPath) {
        this.jspUploadPath = jspUploadPath;
    }

    @Column(name = "files_context")
    public String getFilesContext() {
        return filesContext;
    }

    public void setFilesContext(String filesContext) {
        this.filesContext = filesContext;
    }

    @Column(name = "documents_folder_ext")
    public String getDocumentsFolderExt() {
        return documentsFolderExt;
    }

    public void setDocumentsFolderExt(String documentsFolderExt) {
        this.documentsFolderExt = documentsFolderExt;
    }

    @Column(name = "request_folder_ext")
    public String getRequestFolderExt() {
        return requestFolderExt;
    }

    public void setRequestFolderExt(String requestFolderExt) {
        this.requestFolderExt = requestFolderExt;
    }

    @Column(name = "folder_img_report")
    public String getFolderImgReport() {
        return folderImgReport;
    }

    public void setFolderImgReport(String folderImgReport) {
        this.folderImgReport = folderImgReport;
    }

    @Column(name = "folder_img_report_ext")
    public String getFolderImgReportExt() {
        return folderImgReportExt;
    }

    public void setFolderImgReportExt(String folderImgReportExt) {
        this.folderImgReportExt = folderImgReportExt;
    }

    @Column(name = "folder_list_ext")
    public String getFolderListExt() {
        return folderListExt;
    }

    public void setFolderListExt(String folderListExt) {
        this.folderListExt = folderListExt;
    }

    @Column(name = "backup_folder_ext")
    public String getBackupFolderExt() {
        return backupFolderExt;
    }

    public void setBackupFolderExt(String backupFolderExt) {
        this.backupFolderExt = backupFolderExt;
    }

    @Column(name = "backup_external_path")
    public String getBackupExternalPath() {
        return backupExternalPath;
    }

    public void setBackupExternalPath(String backupExternalPath) {
        this.backupExternalPath = backupExternalPath;
    }

    @Column(name = "nightly_process_time")
    @Override
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getNightlyProcessTime() {
        return nightlyProcessTime;
    }

    public void setNightlyProcessTime(Date nightlyProcessTime) {
        this.nightlyProcessTime = nightlyProcessTime;
    }

    /**
     * @return the fileFolder
     */
    @Column(name = "file_folder")
    public String getFileFolder() {
        return fileFolder;
    }

    /**
     * @param fileFolder the fileFolder to set
     */
    public void setFileFolder(String fileFolder) {
        this.fileFolder = fileFolder;
    }

    @Column(name = "SURVEYS_AUTOSAVE_TIME")
    public Integer getAutosaveTime() {
        return autosaveTime;
    }

    public void setAutosaveTime(Integer autosaveTime) {
        this.autosaveTime = autosaveTime;
    }

    @Column(name = "SURVEYS_EXIT_URL")
    public String getExitURL() {
        return exitURL;
    }

    public void setExitURL(String exitURL) {
        this.exitURL = exitURL;
    }

    @Column(name = "ad_address")
    public String getAdAddress() {
        return adAddress;
    }

    public void setAdAddress(String adAddress) {
        this.adAddress = adAddress;
    }

    @Column(name = "ad_username")
    public String getAdUsername() {
        return adUsername;
    }

    public void setAdUsername(String adUsername) {
        this.adUsername = adUsername;
    }

    @Column(name = "ad_password")
    public String getAdPassword() {
        return adPassword;
    }

    public void setAdPassword(String adPassword) {
        this.adPassword = adPassword;
    }

    @Column(name = "new_digit")
    public String getNewDigit() {
        return newDigit;
    }

    public void setNewDigit(String newDigit) {
        this.newDigit = newDigit;
    }

    @Column(name = "mail_to_author")
    public Integer getMailToAuthor() {
        return mailToAuthor;
    }

    public void setMailToAuthor(Integer mailToAuthor) {
        this.mailToAuthor = mailToAuthor;
    }

    @Column(name = "mail_to_document_manager")
    public Integer getMailToDocumentManager() {
        return mailToDocumentManager;
    }

    public void setMailToDocumentManager(Integer mailToDocumentManager) {
        this.mailToDocumentManager = mailToDocumentManager;
    }

    @Column(name = "mail_to_business_unit_manager")
    public Integer getMailToBusinessUnitManager() {
        return mailToBusinessUnitManager;
    }

    public void setMailToBusinessUnitManager(Integer mailToBusinessUnitManager) {
        this.mailToBusinessUnitManager = mailToBusinessUnitManager;
    }

    @Column(name = "mail_to_department_manager")
    public Integer getMailToDepartmentManager() {
        return mailToDepartmentManager;
    }

    public void setMailToDepartmentManager(Integer mailToDepartmentManager) {
        this.mailToDepartmentManager = mailToDepartmentManager;
    }

    @Column(name = "mail_to_assigned_readers")
    public Integer getMailToAssignedReaders() {
        return mailToAssignedReaders;
    }

    public void setMailToAssignedReaders(Integer mailToAssignedReaders) {
        this.mailToAssignedReaders = mailToAssignedReaders;
    }

    @Column(name = "mail_to_authorizers")
    public Integer getMailToAuthorizers() {
        return mailToAuthorizers;
    }

    public void setMailToAuthorizers(Integer mailToAuthorizers) {
        this.mailToAuthorizers = mailToAuthorizers;
    }

    @Column(name = "mail_to_bu_permission")
    public Integer getMailToBusinessUnitPermission() {
        return mailToBusinessUnitPermission;
    }

    public void setMailToBusinessUnitPermission(Integer mailToBusinessUnitPermission) {
        this.mailToBusinessUnitPermission = mailToBusinessUnitPermission;
    }

    @Column(name = "mail_to_process_permission")
    public Integer getMailToProcessPermission() {
        return mailToProcessPermission;
    }

    public void setMailToProcessPermission(Integer mailToProcessPermission) {
        this.mailToProcessPermission = mailToProcessPermission;
    }

    @Column(name = "mail_to_dep_permission")
    public Integer getMailToDepartmentPermission() {
        return mailToDepartmentPermission;
    }

    public void setMailToDepartmentPermission(Integer mailToDepartmentPermission) {
        this.mailToDepartmentPermission = mailToDepartmentPermission;
    }

    @Column(name = "mail_to_user_permission")
    public Integer getMailToUserPermission() {
        return mailToUserPermission;
    }

    public void setMailToUserPermission(Integer mailToUserPermission) {
        this.mailToUserPermission = mailToUserPermission;
    }

    @Column(name = "complaint_manager_accept")
    public Integer getComplaintManagerWhoAccept() {
        return complaintManagerWhoAccept;
    }

    public void setComplaintManagerWhoAccept(Integer complaintManagerWhoAccept) {
        this.complaintManagerWhoAccept = complaintManagerWhoAccept;
    }

    @Column(name = "reapprove_by_manager")
    public Integer getReapproveByDocumentManager() {
        return reapproveByDocumentManager;
    }

    public void setReapproveByDocumentManager(Integer reapproveByDocumentManager) {
        this.reapproveByDocumentManager = reapproveByDocumentManager;
    }

    @Column(name = "request_return_to_verification")
    public Integer getRequestReturnsToVerification() {
        return requestReturnsToVerification;
    }

    public void setRequestReturnsToVerification(Integer requestReturnsToVerification) {
        this.requestReturnsToVerification = requestReturnsToVerification;
    }

    @Column(name = "action_manager_who_accept")
    public Integer getActionManagerWhoAccept() {
        return actionManagerWhoAccept;
    }

    public void setActionManagerWhoAccept(Integer actionManagerWhoAccept) {
        this.actionManagerWhoAccept = actionManagerWhoAccept;
    }
    @Column(name = "action_manager_who_evaluate")
    public Integer getActionManagerWhoEvaluate() {
        return actionManagerWhoEvaluate;
    }

    public void setActionManagerWhoEvaluate(Integer actionManagerWhoEvaluate) {
        this.actionManagerWhoEvaluate = actionManagerWhoEvaluate;
    }

    @Column(name = "finding_show_all_users")
    public Integer getFindingShowAllUsers() {
        return findingShowAllUsers;
    }

    public void setFindingShowAllUsers(Integer findingShowAllUsers) {
        this.findingShowAllUsers = findingShowAllUsers;
    }

    @Column(name = "require_analysis_cause")
    public Integer getRequireAnalysisCause() {
        return requireAnalysisCause;
    }

    public void setRequireAnalysisCause(Integer requireAnalysisCause) {
        this.requireAnalysisCause = requireAnalysisCause;
    }

    @Column(name = "admin_can_attend_any_finding")
    public Integer getAdminCanAttendAnyFinding() {
        return adminCanAttendAnyFinding;
    }

    public void setAdminCanAttendAnyFinding(Integer adminCanAttendAnyFinding) {
        this.adminCanAttendAnyFinding = adminCanAttendAnyFinding;
    }

    @Column(name = "audited_view_survey")
    public Integer getAuditedViewSurvey() {
        return auditedViewSurvey;
    }

    public void setAuditedViewSurvey(Integer auditedViewSurvey) {
        this.auditedViewSurvey = auditedViewSurvey;
    }

    @Column(name = "max_change_date_allowed")
    public Integer getMaxChangeDateAllowed() {
        return maxChangeDateAllowed;
    }

    public void setMaxChangeDateAllowed(Integer maxChangeDateAllowed) {
        this.maxChangeDateAllowed = maxChangeDateAllowed;
    }

    @Column(name = "service_to_approve")
    public Integer getServiceToApprove() {
        return serviceToApprove;
    }

    public void setServiceToApprove(Integer serviceToApprove) {
        this.serviceToApprove = serviceToApprove;
    }

    @Column(name = "scheduling_to_approve")
    public Integer getSchedulingToApprove() {
        return schedulingToApprove == null ? 0 : schedulingToApprove;
    }

    public void setSchedulingToApprove(Integer schedulingToApprove) {
        this.schedulingToApprove = schedulingToApprove;
    }

    @Column(name = "show_readers_for_print")
    public Integer getShowReadersForPrint() {
        return showReadersForPrint == null ? 0 : showReadersForPrint;
    }

    public void setShowReadersForPrint(Integer showReadersForPrint) {
        this.showReadersForPrint = showReadersForPrint;
    }
    /**
     * @return the programConvertToPDF
     */
    @Basic(optional = false)
    @Column(name = "program_convert_to_pdf")
    @Override
    public String getProgramConvertToPDF() {
        return programConvertToPDF;
    }


    /**
     * @param programConvertToPDF the programConvertToPDF to set
     */
    public void setProgramConvertToPDF(String programConvertToPDF) {
        this.programConvertToPDF = programConvertToPDF;
    }

    /**
     * @return the officeToPDFPath
     */
    @Column(name = "office_to_pdf_path")
    @Override
    public String getOfficeToPDFPath() {
        return officeToPDFPath;
    }

    /**
     * @param officeToPDFPath the officeToPDFPath to set
     */
    public void setOfficeToPDFPath(String officeToPDFPath) {
        this.officeToPDFPath = officeToPDFPath;
    }

    @Column(name = "path_publisher")
    @Override
    public String getPathPublisher() {
        return pathPublisher;
    }

    public void setPathPublisher(String pathPublisher) {
        this.pathPublisher = pathPublisher;
    }

    @Column(name = "path_visio")
    @Override
    public String getPathVisio() {
        return pathVisio;
    }

    public void setPathVisio(String pathVisio) {
        this.pathVisio = pathVisio;
    }

    @Column(name = "path_project")
    @Override
    public String getPathProject() {
        return pathProject;
    }

    public void setPathProject(String pathProject) {
        this.pathProject = pathProject;
    }

    /**
     * @return the officeToPDFAliveTime
     */
    @Column(name = "office_to_pdf_alive_time")
    @Override
    public Long getOfficeToPDFAliveTime() {
        return officeToPDFAliveTime;
    }

    /**
     * @param officeToPDFAliveTime the officeToPDFAliveTime to set
     */
    public void setOfficeToPDFAliveTime(Long officeToPDFAliveTime) {
        this.officeToPDFAliveTime = officeToPDFAliveTime;
    }

    /**
     * @return the officeToPDFMaxWaitingTasks
     */
    @Column(name = "office_max_waiting_tasks")
    @Override
    public Integer getOfficeToPDFMaxWaitingTasks() {
        return officeToPDFMaxWaitingTasks;
    }

    /**
     * @param officeToPDFMaxWaitingTasks the officeToPDFMaxWaitingTasks to set
     */
    public void setOfficeToPDFMaxWaitingTasks(Integer officeToPDFMaxWaitingTasks) {
        this.officeToPDFMaxWaitingTasks = officeToPDFMaxWaitingTasks;
    }

    @Column(name = "office_file_alive_time")
    @Override
    public Integer getOfficeFileAliveTime() {
        return officeFileAliveTime;
    }

    public void setOfficeFileAliveTime(Integer officeFileAliveTime) {
        this.officeFileAliveTime = officeFileAliveTime;
    }

    @Column(name = "enabled_daily_task_doc")
    public Integer getEnabledDailyTaskDocuments() {
        return enabledDailyTaskDocuments;
    }

    public void setEnabledDailyTaskDocuments(Integer enabledDailyTaskDocuments) {
        this.enabledDailyTaskDocuments = enabledDailyTaskDocuments;
    }

    @Column(name = "daily_task_documents")
    @Override
    public Integer getDailyTaskDocuments() {
        return dailyTaskDocuments;
    }

    public void setDailyTaskDocuments(Integer dailyTaskDocuments) {
        this.dailyTaskDocuments = dailyTaskDocuments;
    }

    @Column(name = "enabled_clean_pdf_record")
    public Integer getEnabledCleanupPdfRecords() {
        return enabledCleanupPdfRecords;
    }

    public void setEnabledCleanupPdfRecords(Integer enabledCleanupPdfRecords) {
        this.enabledCleanupPdfRecords = enabledCleanupPdfRecords;
    }

    @Column(name = "max_cleanup_pdf_records")
    public Integer getMaxCleanupPdfRecords() {
        return maxCleanupPdfRecords;
    }

    public void setMaxCleanupPdfRecords(Integer maxCleanupPdfRecords) {
        this.maxCleanupPdfRecords = maxCleanupPdfRecords;
    }

    @Column(name = "track_geolocation")
    public Integer getTrackGeolocation() {
        return trackGeolocation;
    }

    public void setTrackGeolocation(Integer trackGeolocation) {
        this.trackGeolocation = trackGeolocation;
    }

    /**
     * @return the mailSenderPoolSize
     */
    @Basic(optional = false)
    @Column(name = "mail_sender_pool_size")
    @Override
    public Integer getMailSenderPoolSize() {
        return mailSenderPoolSize;
    }

    /**
     * @param mailSenderPoolSize the mailSenderPoolSize to set
     */
    public void setMailSenderPoolSize(Integer mailSenderPoolSize) {
        this.mailSenderPoolSize = mailSenderPoolSize;
    }

    /**
     * @return the mailSenderAliveTime
     */
    @Basic(optional = false)
    @Column(name = "mail_sender_alive_time")
    @Override
    public Long getMailSenderAliveTime() {
        return mailSenderAliveTime;
    }

    /**
     * @param mailSenderAliveTime the mailSenderAliveTime to set
     */
    public void setMailSenderAliveTime(Long mailSenderAliveTime) {
        this.mailSenderAliveTime = mailSenderAliveTime;
    }

    /**
     * @return the mailSenderMaxWaitingTasks
     */
    @Basic(optional = false)
    @Column(name = "mail_sender_max_waiting_tasks")
    @Override
    public Integer getMailSenderMaxWaitingTasks() {
        return mailSenderMaxWaitingTasks;
    }

    /**
     * @param mailSenderMaxWaitingTasks the mailSenderMaxWaitingTasks to set
     */
    public void setMailSenderMaxWaitingTasks(Integer mailSenderMaxWaitingTasks) {
        this.mailSenderMaxWaitingTasks = mailSenderMaxWaitingTasks;
    }

    /**
     * @return the pdfImagePoolSize
     */
    @Basic(optional = false)
    @Column(name = "pdf_image_pool_size")
    @Override
    public Integer getPdfImagePoolSize() {
        return pdfImagePoolSize;
    }

    /**
     * @param pdfImagePoolSize the pdfImagePoolSize to set
     */
    public void setPdfImagePoolSize(Integer pdfImagePoolSize) {
        this.pdfImagePoolSize = pdfImagePoolSize;
    }

    /**
     * @return the pdfImageAliveTime
     */
    @Basic(optional = false)
    @Column(name = "pdf_image_alive_time")
    @Override
    public Long getPdfImageAliveTime() {
        return pdfImageAliveTime;
    }

    /**
     * @param pdfImageAliveTime the pdfImageAliveTime to set
     */
    public void setPdfImageAliveTime(Long pdfImageAliveTime) {
        this.pdfImageAliveTime = pdfImageAliveTime;
    }

    /**
     * @return the pdfImageMaxWaitingTasks
     */
    @Basic(optional = false)
    @Column(name = "pdf_image_max_waiting_tasks")
    @Override
    public Integer getPdfImageMaxWaitingTasks() {
        return pdfImageMaxWaitingTasks;
    }

    /**
     * @param pdfImageMaxWaitingTasks the pdfImageMaxWaitingTasks to set
     */
    public void setPdfImageMaxWaitingTasks(Integer pdfImageMaxWaitingTasks) {
        this.pdfImageMaxWaitingTasks = pdfImageMaxWaitingTasks;
    }

    @Column(name = "pdf_image_dpi")
    @Override
    public Integer getPdfImageDpi() {
        return pdfImageDpi;
    }

    public void setPdfImageDpi(Integer pdfImageDpi) {
        this.pdfImageDpi = pdfImageDpi;
    }

    @Column(name = "dwg_image_dpi")
    @Override
    public Integer getDwgImageDpi() {
        return dwgImageDpi;
    }

    public void setDwgImageDpi(Integer dwgImageDpi) {
        this.dwgImageDpi = dwgImageDpi;
    }

    @Column(name = "pdf_image_format")
    public String getPdfImageFormat() {
        return pdfImageFormat;
    }

    public void setPdfImageFormat(String pdfImageFormat) {
        this.pdfImageFormat = pdfImageFormat;
    }

    @Override
    @Column(name = "pdf_maximum_persisted_pages")
    public Integer getPdfMaximumPersistedPages() {
        return pdfMaximumPersistedPages;
    }

    public void setPdfMaximumPersistedPages(Integer pdfMaximumPersistedPages) {
        this.pdfMaximumPersistedPages = pdfMaximumPersistedPages;
    }

    /**
     * @return the expiredDocumentPendingModuleManager
     */
    @Basic(optional = false)
    @Column(name = "expired_doc_pen_module_mgr")
    public Integer getExpiredDocumentPendingModuleManager() {
        return expiredDocumentPendingModuleManager;
    }

    /**
     * @param expiredDocumentPendingModuleManager the expiredDocumentPendingModuleManager to set
     */
    public void setExpiredDocumentPendingModuleManager(Integer expiredDocumentPendingModuleManager) {
        this.expiredDocumentPendingModuleManager = expiredDocumentPendingModuleManager;
    }

    /**
     * @return the expiredDocumentPendingDocumentManager
     */
    @Basic(optional = false)
    @Column(name = "expired_doc_pen_doc_mgr")
    public Integer getExpiredDocumentPendingDocumentManager() {
        return expiredDocumentPendingDocumentManager;
    }

    /**
     * @param expiredDocumentPendingDocumentManager the expiredDocumentPendingDocumentManager to set
     */
    public void setExpiredDocumentPendingDocumentManager(Integer expiredDocumentPendingDocumentManager) {
        this.expiredDocumentPendingDocumentManager = expiredDocumentPendingDocumentManager;
    }

    /**
     * @return the expiredDocumentMailModuleManager
     */
    @Basic(optional = false)
    @Column(name = "expired_doc_mail_module_mgr")
    public Integer getExpiredDocumentMailModuleManager() {
        return expiredDocumentMailModuleManager;
    }

    /**
     * @param expiredDocumentMailModuleManager the expiredDocumentMailModuleManager to set
     */
    public void setExpiredDocumentMailModuleManager(Integer expiredDocumentMailModuleManager) {
        this.expiredDocumentMailModuleManager = expiredDocumentMailModuleManager;
    }

    /**
     * @return the expiredDocumentMailDocumentManager
     */
    @Basic(optional = false)
    @Column(name = "expired_doc_mail_doc_manager")
    public Integer getExpiredDocumentMailDocumentManager() {
        return expiredDocumentMailDocumentManager;
    }

    /**
     * @param expiredDocumentMailDocumentManager the expiredDocumentMailDocumentManager to set
     */
    public void setExpiredDocumentMailDocumentManager(Integer expiredDocumentMailDocumentManager) {
        this.expiredDocumentMailDocumentManager = expiredDocumentMailDocumentManager;
    }

    /**
     * @return the expiredDocumentMailLastAuthor
     */
    @Basic(optional = false)
    @Column(name = "expired_doc_mail_last_author")
    public Integer getExpiredDocumentMailLastAuthor() {
        return expiredDocumentMailLastAuthor;
    }

    /**
     * @param expiredDocumentMailLastAuthor the expiredDocumentMailLastAuthor to set
     */
    public void setExpiredDocumentMailLastAuthor(Integer expiredDocumentMailLastAuthor) {
        this.expiredDocumentMailLastAuthor = expiredDocumentMailLastAuthor;
    }

    /**
     * @return the expiredRequestMailDocumentManager
     */

    /**
     * @return the expiredDocumentMailLastAuthor
     */
    @Basic(optional = false)
    @Column(name = "expired_request_mail_doc_mgr")
    public Integer getExpiredRequestMailDocumentManager() {
        return expiredRequestMailDocumentManager;
    }

    /**
     * @param expiredRequestMailDocumentManager the expiredRequestMailDocumentManager to set
     */
    public void setExpiredRequestMailDocumentManager(Integer expiredRequestMailDocumentManager) {
        this.expiredRequestMailDocumentManager = expiredRequestMailDocumentManager;
    }

    @Column(name = "pending_count_last_refresh_dte")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    public Date getFullPendingCountLastRefreshDate() {
        return fullPendingCountLastRefreshDate;
    }

    public void setFullPendingCountLastRefreshDate(Date fullPendingCountLastRefreshDate) {
        this.fullPendingCountLastRefreshDate = fullPendingCountLastRefreshDate;
    }

    /**
     * @return the downloadAttemptsLimit
     */
    @Override
    @Basic(optional = false)
    @Column(name = "download_attempts_limit")
    public Integer getDownloadAttemptsLimit() {
        return downloadAttemptsLimit;
    }

    /**
     * @param downloadAttemptsLimit the downloadAttemptsLimit to set
     */
    public void setDownloadAttemptsLimit(Integer downloadAttemptsLimit) {
        this.downloadAttemptsLimit = downloadAttemptsLimit;
    }

    /**
     * @return the daysAnticipation
     */
    @Basic(optional = false)
    @Column(name = "days_anticipation")
    public Integer getDaysAnticipation(){
        return daysAnticipation;
    }

    /**
     *
     * @param daysAnticipation the daysAnticipation to set
     */
    public void setDaysAnticipation(Integer daysAnticipation){
        this.daysAnticipation = daysAnticipation;
    }

    @Column(name = "default_file_id")
    public Long getDefaultFileId() {
        return defaultFileId;
    }

    public void setDefaultFileId(Long defaultFileId) {
        this.defaultFileId = defaultFileId;
    }

    @Column(name = "welcome_bg_id")
    public Long getWelcomeBgId() {
        return welcomeBgId;
    }

    public void setWelcomeBgId(Long welcomeBgId) {
        this.welcomeBgId = welcomeBgId;
    }

    @Column(name = "autodesk_tv_path")
    @Override
    public String getAutodeskTvPath() {
        return autodeskTvPath;
    }

    public void setAutodeskTvPath(String autodeskTvPath) {
        this.autodeskTvPath = autodeskTvPath;
    }

    @Column(name = "autodesk_tv_plot_script_path")
    @Override
    public String getAutodeskTvPlotScriptPath() {
        return autodeskTvPlotScriptPath;
    }

    public void setAutodeskTvPlotScriptPath(String autodeskTvPlotScriptPath) {
        this.autodeskTvPlotScriptPath = autodeskTvPlotScriptPath;
    }

    @Column(name = "autodesk_tv_plot_folder_path")
    @Override
    public String getAutodeskTvPlotFolderPath() {
        return autodeskTvPlotFolderPath;
    }

    public void setAutodeskTvPlotFolderPath(String autodeskTvPlotFolderPath) {
        this.autodeskTvPlotFolderPath = autodeskTvPlotFolderPath;
    }

    @Column(name = "autodesk_tv_file_params")
    @Override
    public String getAutodeskTvFileParams() {
        return autodeskTvFileParams;
    }

    public void setAutodeskTvFileParams(String autodeskTvFileParams) {
        this.autodeskTvFileParams = autodeskTvFileParams;
    }

    @Column(name = "autodesk_tv_plot_params")
    @Override
    public String getAutodeskTvPlotParams() {
        return autodeskTvPlotParams;
    }

    public void setAutodeskTvPlotParams(String autodeskTvPlotParams) {
        this.autodeskTvPlotParams = autodeskTvPlotParams;
    }

    @Column(name = "interface_tress")
    public Integer getInterfaceTRESS() {
        return interfaceTRESS;
    }

    public void setInterfaceTRESS(Integer interfaceTRESS) {
        this.interfaceTRESS = interfaceTRESS;
    }

    /**
     * Guarda el perfil que se utilizara por defecto en todos los
     * usuarios cargados desde TRESS.
     *
     * @return
     */
    @Column(name = "interface_tress_profile_code")
    public String getInterfaceTRESSProfileCode() {
        return interfaceTRESSProfileCode;
    }

    public void setInterfaceTRESSProfileCode(String interfaceTRESSProfileCode) {
        this.interfaceTRESSProfileCode = interfaceTRESSProfileCode;
    }

    @Column(name = "allow_switch_search_subfolders")
    public Integer getAllowSwitchSearchInSubfolders() {
        return allowSwitchSearchInSubfolders;
    }
    public void setAllowSwitchSearchInSubfolders(Integer allowSwitchSearchInSubfolders) {
        this.allowSwitchSearchInSubfolders = allowSwitchSearchInSubfolders;
    }

    @Column(name = "share_documents")
    public Integer getShareDocuments() {
        return shareDocuments;
    }

    public void setShareDocuments(Integer shareDocuments) {
        this.shareDocuments = shareDocuments;
    }

    @Column(name = "external_link")
    public Integer getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(Integer externalLink) {
        this.externalLink = externalLink;
    }

    /**
     * @return the searchSubFolder
     */
    @Column(name = "search_in_subfolders")
    public Integer getSearchSubFolder() {
        return searchSubFolder;
    }

    public void setSearchSubFolder(Integer searchSubFolder) {
        this.searchSubFolder = searchSubFolder;
    }

    @Column(name = "printing_date_format")
    public String getPrintingDateFormat() {
        return printingDateFormat;
    }

    public void setPrintingDateFormat(String printingDateFormat) {
        this.printingDateFormat = printingDateFormat;
    }

    @Column(name = "printing_date_time_format")
    public String getPrintingDateTimeFormat() {
        return printingDateTimeFormat;
    }

    public void setPrintingDateTimeFormat(String printingDateTimeFormat) {
        this.printingDateTimeFormat = printingDateTimeFormat;
    }

    /**
     * @return the apePoolSize
     */
    @Basic(optional = false)
    @Column(name = "ape_pool_size")
    @Override
    public Integer getApePoolSize() {
        return apePoolSize;
    }


    /**
     * @param apePoolSize the apePoolSize to set
     */
    public void setApePoolSize(Integer apePoolSize) {
        this.apePoolSize = apePoolSize;
    }

    /**
     * @return the apeAliveTime
     */
    @Basic(optional = false)
    @Column(name = "ape_alive_time")
    @Override
    public Long getApeAliveTime() {
        return apeAliveTime;
    }

    /**
     * @param apeAliveTime the apeAliveTime to set
     */
    public void setApeAliveTime(Long apeAliveTime) {
        this.apeAliveTime = apeAliveTime;
    }

    /**
     * @return the apeMaxWaitingTasks
     */
    @Basic(optional = false)
    @Column(name = "ape_max_waiting_tasks")
    @Override
    public Integer getApeMaxWaitingTasks() {
        return apeMaxWaitingTasks;
    }

    /**
     * @param apeMaxWaitingTasks the apeMaxWaitingTasks to set
     */
    public void setApeMaxWaitingTasks(Integer apeMaxWaitingTasks) {
        this.apeMaxWaitingTasks = apeMaxWaitingTasks;
    }


    @Basic(optional = true)
    @Column(name = "report_logo_id")
    public Long getReportLogoId() {
        return reportLogoId;
    }

    public void setReportLogoId(Long reportLogoId) {
        this.reportLogoId = reportLogoId;
    }

    @Override
    @Basic(optional = true)
    @Column(name = "google_drive_key_id")
    public Long getGoogleDriveKeyId() {
        return googleDriveKeyId;
    }

    public void setGoogleDriveKeyId(Long googleDriveKeyId) {
        this.googleDriveKeyId = googleDriveKeyId;
    }

    /**
     * @return the minimumCodeDigits
     */
    @Basic(optional = false)
    @Column(name = "minimum_code_digits")
    public Integer getMinimumCodeDigits() {
        return minimumCodeDigits;
    }

    /**
     * @param minimumCodeDigits the minimumCodeDigits to set
     */
    public void setMinimumCodeDigits(Integer minimumCodeDigits) {
        this.minimumCodeDigits = minimumCodeDigits;
    }

    @Column(name = "office_to_pdf_params")
    @Override
    public String getOfficeToPdfParams() {
        return officeToPdfParams;
    }

    public void setOfficeToPdfParams(String officeToPdfParams) {
        this.officeToPdfParams = officeToPdfParams;
    }

    @Column(name = "localize_entities")
    public Integer getLocalizeEntities() {
        return localizeEntities;
    }

    public void setLocalizeEntities(Integer localizeEntities) {
        this.localizeEntities = localizeEntities;
    }

    @Column(name = "complaint_manager_notification")
    public Integer getComplaintManagerNotification() {
        return complaintManagerNotification;
    }

    public void setComplaintManagerNotification(Integer complaintManagerNotification) {
        this.complaintManagerNotification = complaintManagerNotification;
    }

    @Column(name = "path_to_boss_employees_csv")
    public String getPathToBossEmployeesCsv() {
        return pathToBossEmployeesCsv;
    }

    public void setPathToBossEmployeesCsv(String pathToBossEmployeesCsv) {
        this.pathToBossEmployeesCsv = pathToBossEmployeesCsv;
    }

    @Column(name = "filter_complaint_responsible_by_dep")
    public Integer getFilterComplantResponsibleByDepartment() {
        return filterComplantResponsibleByDepartment;
    }

    public void setFilterComplantResponsibleByDepartment(Integer filterComplantResponsibleByDepartment) {
        this.filterComplantResponsibleByDepartment = filterComplantResponsibleByDepartment;
    }

    @Column(name = "complaints_code_prefix")
    public String getComplaintsCodePrefix() {
        return complaintsCodePrefix;
    }

    public void setComplaintsCodePrefix(String complaintsCodePrefix) {
        this.complaintsCodePrefix = complaintsCodePrefix;
    }

    @Column(name = "document_code_scope")
    public Integer getDocumentCodeScope() {
        return documentCodeScope;
    }

    public void setDocumentCodeScope(Integer documentCodeScope) {
        this.documentCodeScope = documentCodeScope == null ? CODE_SCOPE.LEVEL_BUSINESS_UNIT.value : documentCodeScope;
    }


    @Column(name = "device_edit_implementation")
    public Integer getDeviceEditImplementation() {
        return deviceEditImplementation;
    }


    public void setDeviceEditImplementation(Integer deviceEditImplementation) {
        this.deviceEditImplementation = deviceEditImplementation;
    }
    @Column(name = "enable_file_cache")
    @Override
    public Integer getEnableFileCache() {
        return enableFileCache;
    }

    public void setEnableFileCache(Integer enableFileCache) {
        this.enableFileCache = enableFileCache;
    }

    @Column(name = "file_cache_path")
    @Override
    public String getFileCachePath() {
        return fileCachePath;
    }

    public void setFileCachePath(String fileCachePath) {
        this.fileCachePath = fileCachePath;
    }

    @Column(name = "file_cache_store_days")
    @Override
    public Long getFileCacheStoreDays() {
        return fileCacheStoreDays;
    }

    public void setFileCacheStoreDays(Long fileCacheStoreDays) {
        this.fileCacheStoreDays = fileCacheStoreDays;
    }

    @Column(name = "session_max_inactive_time")
    public Integer getSessionMaxInactiveTime() {
        return sessionMaxInactiveTime;
    }

    public void setSessionMaxInactiveTime(Integer sessionMaxInactiveTime) {
        this.sessionMaxInactiveTime = sessionMaxInactiveTime;
    }

    @Column(name = "close_office_programs")
    @Override
    public Integer getCloseOfficePrograms() {
        return closeOfficePrograms;
    }

    public void setCloseOfficePrograms(Integer closeOfficePrograms) {
        this.closeOfficePrograms = closeOfficePrograms;
    }
    @Column(name = "pending_max_count")
    public Integer getPendingMaxCount() {
        return pendingMaxCount;
    }

    public void setPendingMaxCount(Integer pendingMaxCount) {
        this.pendingMaxCount = pendingMaxCount;
    }

    @Column(name = "google_drive_integration")
    @Override
    public Integer getGoogleDriveIntegration() {
        return googleDriveIntegration;
    }

    public void setGoogleDriveIntegration(Integer googleDriveIntegration) {
        this.googleDriveIntegration = googleDriveIntegration;
    }

    @Column(name = "google_api_key")
    public String getGoogleApiKey() {
        return googleApiKey;
    }

    public void setGoogleApiKey(String googleApiKey) {
        this.googleApiKey = googleApiKey;
    }

    @Column(name = "google_client_id")
    public String getGoogleClientId() {
        return googleClientId;
    }

    public void setGoogleClientId(String googleClientId) {
        this.googleClientId = googleClientId;
    }

    @Column(name = "google_application_id")
    public String getGoogleApplicationId() {
        return googleApplicationId;
    }

    public void setGoogleApplicationId(String googleApplicationId) {
        this.googleApplicationId = googleApplicationId;
    }

    /**
     * Sirve para recalcular las tablas de respeustas de formularios,
     *
     * Para activar, colocar su valor en "1". Al finalizar de recalcular, su valor regresara a "0".
     * @return
     */
    @Column(name = "freezed_survey_answers_run")
    public Integer getFreezedFormAnswersRun() {
        return freezedFormAnswersRun;
    }

    public void setFreezedFormAnswersRun(Integer freezedSurveyAnswersRun) {
        this.freezedFormAnswersRun = freezedSurveyAnswersRun;
    }

    @Column(name = "refresh_pendings_next_run")
    public Integer getRefreshPendingsNextRun() {
        return refreshPendingsNextRun;
    }

    public void setRefreshPendingsNextRun(Integer refreshPendingsNextRun) {
        this.refreshPendingsNextRun = refreshPendingsNextRun;
    }

    @Column(name = "sso_active")
    public Integer getSsoActive() {
        return ssoActive;
    }

    public void setSsoActive(Integer ssoActive) {
        this.ssoActive = ssoActive;
    }

    @Column(name = "sso_domain_validation")
    public Integer getSsoDomainValidation() {
        return ssoDomainValidation;
    }

    public void setSsoDomainValidation(Integer ssoDomainValidation) {
        this.ssoDomainValidation = ssoDomainValidation;
    }

    @Column(name = "ad_authentication")
    public String getAdAuthentication() {
        return adAuthentication;
    }

    public void setAdAuthentication(String adAuthentication) {
        this.adAuthentication = adAuthentication;
    }

    @Column(name = "ad_referral")
    public String getAdReferral() {
        return adReferral;
    }

    public void setAdReferral(String adReferral) {
        this.adReferral = adReferral;
    }

    @Column(name = "ad_default_position_code")
    public String getAdDefaultPositionCode() {
        return adDefaultPositionCode;
    }

    public void setAdDefaultPositionCode(String adDefaultPositionCode) {
        this.adDefaultPositionCode = adDefaultPositionCode;
    }

    @Column(name = "ad_default_lang")
    public String getAdDefaultLang() {
        return adDefaultLang;
    }

    public void setAdDefaultLang(String adDefaultLang) {
        this.adDefaultLang = adDefaultLang;
    }

    @Column(name = "ad_default_locale")
    public String getAdDefaultLocale() {
        return adDefaultLocale;
    }

    public void setAdDefaultLocale(String adDefaultLocale) {
        this.adDefaultLocale = adDefaultLocale;
    }



    @Column(name = "ad_default_schema")
    public String getAdDefaultSchema() {
        return adDefaultSchema;
    }

    public void setAdDefaultSchema(String adDefaultSchema) {
        this.adDefaultSchema = adDefaultSchema;
    }

    @Column(name = "ad_default_folder")
    public String getAdDefaultfolder() {
        return adDefaultfolder;
    }

    public void setAdDefaultfolder(String adDefaultfolder) {
        this.adDefaultfolder = adDefaultfolder;
    }

    @Column(name = "ad_default_document_code_task")
    public String getAdDefaultDocumentCodeTask() {
        return adDefaultDocumentCodeTask;
    }

    public void setAdDefaultDocumentCodeTask(String adDefaultDocumentCodeTask) {
        this.adDefaultDocumentCodeTask = adDefaultDocumentCodeTask;
    }

    @Column(name = "service_desk_telephones")
    public String getServiceDeskContact() {
        return serviceDeskContact;
    }

    public void setServiceDeskContact(String serviceDeskContact) {
        this.serviceDeskContact = serviceDeskContact;
    }

    @Column(name = "ad_favorite_task_codes")
    public String getAdFavoriteTaskCodes() {
        return adFavoriteTaskCodes;
    }

    public void setAdFavoriteTaskCodes(String adFavoriteTaskCodes) {
        this.adFavoriteTaskCodes = adFavoriteTaskCodes;
    }

    @Column(name = "maximo_favorite_task_code")
    public String getMaximoFavoriteTaskCode() {
        return maximoFavoriteTaskCode;
    }

    public void setMaximoFavoriteTaskCode(String maximoFavoriteTaskCode) {
        this.maximoFavoriteTaskCode = maximoFavoriteTaskCode;
    }

    @Column(name = "mail_licencetype_leyend_1")
    public String getLicencetypeLeyendDevelopment() {
        return licencetypeLeyendDevelopment;
    }

    public void setLicencetypeLeyendDevelopment(String licencetypeLeyendDevelopment) {
        this.licencetypeLeyendDevelopment = licencetypeLeyendDevelopment;
    }

    @Column(name = "mail_licencetype_leyend_2")
    public String getLicencetypeLeyendDemo() {
        return licencetypeLeyendDemo;
    }

    public void setLicencetypeLeyendDemo(String licencetypeLeyendDemo) {
        this.licencetypeLeyendDemo = licencetypeLeyendDemo;
    }

    @Column(name = "mail_licencetype_leyend_3")
    public String getLicencetypeLeyendQA() {
        return licencetypeLeyendQA;
    }

    public void setLicencetypeLeyendQA(String licencetypeLeyendQA) {
        this.licencetypeLeyendQA = licencetypeLeyendQA;
    }

    @Column(name = "mail_licencetype_leyend_4")
    public String getLicencetypeLeyendProduction() {
        return licencetypeLeyendProduction;
    }

    public void setLicencetypeLeyendProduction(String licencetypeLeyendProduction) {
        this.licencetypeLeyendProduction = licencetypeLeyendProduction;
    }

    @Column(name = "service_desk_mail")
    public String getServiceDeskMail() {
        return serviceDeskMail;
    }

    public void setServiceDeskMail(String serviceDeskMail) {
        this.serviceDeskMail = serviceDeskMail;
    }

    @Column(name = "licence_type")
    public Integer getLicenceType() {
        return licenceType;
    }

    public void setLicenceType(Integer licenceType) {
        this.licenceType = licenceType;
    }

    @Column(name = "dummy_mail")
    public String getDummyMail() {
        return dummyMail;
    }

    public void setDummyMail(String dummyMail) {
        this.dummyMail = dummyMail;
    }

    @Override
    @Column(name = "pdf_generator_url")
    public String getPdfGeneratorUrl() {
        return this.pdfGeneratorUrl;
    }

    public void setPdfGeneratorUrl(String pdfGeneratorUrl) {
        this.pdfGeneratorUrl = pdfGeneratorUrl;
    }

    @Override
    @Column(name = "pdf_generator_account")
    public String getPdfGeneratorAccount() {
        return this.pdfGeneratorAccount;
    }

    public void setPdfGeneratorAccount(String pdfGeneratorAccount) {
        this.pdfGeneratorAccount = pdfGeneratorAccount;
    }

    @Override
    @Column(name = "pdf_generator_password")
    public String getPdfGeneratorPassword() {
        return this.pdfGeneratorPassword;
    }

    public void setPdfGeneratorPassword(String pdfGeneratorPassword) {
        this.pdfGeneratorPassword = pdfGeneratorPassword;
    }


    @Override
    @Column(name = "pdf_generator_timeout")
    public Long getPdfGeneratorTimeout() {
        return pdfGeneratorTimeout;
    }

    public void setPdfGeneratorTimeout(Long pdfGeneratorTimeout) {
        this.pdfGeneratorTimeout = pdfGeneratorTimeout;
    }

    @Column(name =  "enable_timework")
    public Integer getEnableTimework() {
        return enableTimework;
    }

    public void setEnableTimework(Integer enableTimework) {
        this.enableTimework = enableTimework;
    }

    @Column(name = "timework_url")
    public String getTimeworkUrl() {
        return timeworkUrl;
    }

    public void setTimeworkUrl(String timeworkUrl) {
        this.timeworkUrl = timeworkUrl;
    }

    @Column(name = "timework_mails")
    public String getTimeworkMails() {
        return timeworkMails;
    }

    public void setTimeworkMails(String timeworkMails) {
        this.timeworkMails = timeworkMails;
    }

    @Column(name = "timework_sync_type")
    public String getTimeworkSyncType() {
        return timeworkSyncType;
    }

    public void setTimeworkSyncType(String timeworkSyncType) {
        this.timeworkSyncType = timeworkSyncType;
    }

    @Column(name = "exchange_rate_conv_enabled")
    public Integer getExchangeRateConversorEnabled() {
        return exchangeRateConversorEnabled;
    }

    public void setExchangeRateConversorEnabled(Integer exchangeRateConversorEnabled) {
        this.exchangeRateConversorEnabled = exchangeRateConversorEnabled;
    }

    @Column(name = "exchange_rate_sync_type")
    public String getExchangeRateSyncType() {
        return exchangeRateSyncType;
    }

    public void setExchangeRateSyncType(String exchangeRateSyncType) {
        this.exchangeRateSyncType = exchangeRateSyncType;
    }

    @Column(name =  "allow_many_tabs_per_pending")
    public Integer getAllowManyTabsPerPending() {
        return allowManyTabsPerPending;
    }

    public void setAllowManyTabsPerPending(Integer allowManyTabsPerPending) {
        this.allowManyTabsPerPending = allowManyTabsPerPending;
    }

    @Column(name =  "initialized_ape")
    public Integer getInitializedApe() {
        return initializedApe;
    }

    public void setInitializedApe(Integer initializedApe) {
        this.initializedApe = initializedApe;
    }

    @Column(name = "show_positions_for_copies")
    public Integer getShowPositionsForCopies() {
        return showPositionsForCopies;
    }

    public void setShowPositionsForCopies(Integer showPositionsForCopies) {
        this.showPositionsForCopies = showPositionsForCopies;
    }

    @Column(name = "print_receipt_enabled")
    public Integer getPrintReceiptEnabled() {
        return printReceiptEnabled;
    }

    public void setPrintReceiptEnabled(Integer printReceiptEnabled) {
        this.printReceiptEnabled = printReceiptEnabled;
    }

    @Column(name = "num_attempt_session")
    public Integer getNumAttemptsSession() {
        return numAttemptsSession;
    }

    public void setNumAttemptsSession(Integer numAttemptsSession) {
        this.numAttemptsSession = numAttemptsSession;
    }

    @Column(name = "timeout_unlocking")
    public Integer getTimeoutUnlocking() {
        return timeoutUnlocking;
    }

    public void setTimeoutUnlocking(Integer timeoutUnlocking) {
        this.timeoutUnlocking = timeoutUnlocking;
    }

    @Basic(optional = true)
    @Column(name = "isotype_logo_id")
    public Long getIsotypeLogoId() {
        return isotypeLogoId;
    }

    public void setIsotypeLogoId(Long isotypeLogoId) {
        this.isotypeLogoId = isotypeLogoId;
    }

    @Column(name = "time_zone")
    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    @Column(name = "java_user_agent")
    public String getJavaUserAgent() {
        return javaUserAgent;
    }

    public void setJavaUserAgent(String javaUserAgent) {
        this.javaUserAgent = javaUserAgent;
    }

    @Column(name = "cache_sync_alive_t")
    public Long getCacheQuerySyncAliveTime() {
        return cacheQuerySyncAliveTime;
    }

    public void setCacheQuerySyncAliveTime(Long cacheQuerySyncAliveTime) {
        this.cacheQuerySyncAliveTime = cacheQuerySyncAliveTime;
    }

    @Column(name = "cache_sync_img_pool_s")
    public Integer getCacheQuerySyncImagePoolSize() {
        return cacheQuerySyncImagePoolSize;
    }

    public void setCacheQuerySyncImagePoolSize(Integer cacheQuerySyncImagePoolSize) {
        this.cacheQuerySyncImagePoolSize = cacheQuerySyncImagePoolSize;
    }

    @Column(name = "cache_sync_img_max_wait_task")
    public Integer getCacheQuerySyncImageMaxWaitingTasks() {
        return cacheQuerySyncImageMaxWaitingTasks;
    }

    public void setCacheQuerySyncImageMaxWaitingTasks(Integer cacheQuerySyncImageMaxWaitingTasks) {
        this.cacheQuerySyncImageMaxWaitingTasks = cacheQuerySyncImageMaxWaitingTasks;
    }

    @Column(name = "time_limit_to_modify_timesheet")
    public Integer getTimeLimitToModifyTimesheet() {
        return timeLimitToModifyTimesheet;
    }

    public void setTimeLimitToModifyTimesheet(Integer timeLimitToModifyTimesheet) {
        this.timeLimitToModifyTimesheet = timeLimitToModifyTimesheet;
    }

    @Column(name = "pending_history")
    public Integer getPendingHistory() {
        return pendingHistory;
    }

    public void setPendingHistory(Integer pendingHistory) {
        this.pendingHistory = pendingHistory;
    }

    @Column(name = "index_rebuild")
    public Integer getIndexRebuild() {
        return indexRebuild;
    }

    public void setIndexRebuild(Integer indexRebuild) {
        this.indexRebuild = indexRebuild;
    }

    @Column(name = "conn_query_timeout")
    public Integer getConnQueryTimeout() {
        return connQueryTimeout;
    }

    public void setConnQueryTimeout(Integer connQueryTimeout) {
        this.connQueryTimeout = connQueryTimeout;
    }

    @Column(name = "use_2nd_lvl_cache")
    public Integer getUseSecondLevelCache() {
        return useSecondLevelCache;
    }

    public void setUseSecondLevelCache(Integer useSecondLevelCache) {
        this.useSecondLevelCache = useSecondLevelCache;
    }


    @Column(name = "years_to_add_calendar")
    public Integer getYearsToAddCalendar() {
        return yearsToAddCalendar;
    }

    public void setYearsToAddCalendar(Integer yearsToAddCalendar) {
        this.yearsToAddCalendar = yearsToAddCalendar;
    }

    @Column(name = "upload_file_max_size_bytes")
    public Long getUploadFileMaxSizeBytes() {
        return uploadFileMaxSizeBytes;
    }

    public void setUploadFileMaxSizeBytes(Long uploadFileMaxSizeBytes) {
        this.uploadFileMaxSizeBytes = uploadFileMaxSizeBytes;
    }

    @Column(name = "total_uploaded_bytes")
    public Long getTotalUploadedBytes() {
        return totalUploadedBytes;
    }

    public void setTotalUploadedBytes(Long totalUploadedBytes) {
        this.totalUploadedBytes = totalUploadedBytes;
    }

    @Column(name = "enable_registration")
    public Integer getEnableRegistration() {
        return enableRegistration;
    }

    public void setEnableRegistration(Integer enableRegistration) {
        this.enableRegistration = enableRegistration;
    }

    @Column(name = "enable_timework_sync_test")
    public Integer getEnableTimeworkSyncTest() {
        return enableTimeworkSyncTest;
    }

    public void setEnableTimeworkSyncTest(Integer enableTimeworkSyncTest) {
        this.enableTimeworkSyncTest = enableTimeworkSyncTest;
    }

    @Column(name = "tw_max_connection_timeout")
    public Long getMaxConnectionTimeout() {
        return maxConnectionTimeout;
    }

    public void setMaxConnectionTimeout(Long maxConnectionTimeout) {
        this.maxConnectionTimeout = maxConnectionTimeout;
    }

    @Column(name = "tw_max_request_timeout")
    public Long getMaxRequestTimeout() {
        return maxRequestTimeout;
    }

    public void setMaxRequestTimeout(Long maxRequestTimeout) {
        this.maxRequestTimeout = maxRequestTimeout;
    }

    @Column(name = "preload_second_level_cache")
    public Integer getPreloadSecondLevelCache() {
        return preloadSecondLevelCache;
    }

    public void setPreloadSecondLevelCache(Integer preloadSecondLevelCache) {
        this.preloadSecondLevelCache = preloadSecondLevelCache;
    }

    @Column(name = "enable_db_replication")
    public Integer getEnableDbReplication() {
        return enableDbReplication;
    }

    public void setEnableDbReplication(Integer enableDbReplication) {
        this.enableDbReplication = enableDbReplication;
    }

    @Column(name = "news_by_society")
    public Integer getNewsBySociety() {
        return newsBySociety;
    }

    public void setNewsBySociety(Integer newsBySociety) {
        this.newsBySociety = newsBySociety;
    }

    @Column(name = "limit_ape_cache_preload")
    public Long getLimitApeCachePreload() {
        return limitApeCachePreload;
    }

    public void setLimitApeCachePreload(Long limitApeCachePreload) {
        this.limitApeCachePreload = limitApeCachePreload;
    }

    @Column(name = "oidc_enabled")
    public Integer getOidcEnabled() {
        return oidcEnabled;
    }

    public void setOidcEnabled(Integer oidcEnabled) {
        this.oidcEnabled = oidcEnabled;
    }

    @Column(name = "oidc_provider")
    public Integer getOidcProvider() {
        return oidcProvider;
    }

    public void setOidcProvider(Integer oidcProvider) {
        this.oidcProvider = oidcProvider;
    }

    @Column(name = "oidc_account_attribute")
    public String getOidcAccountAttribute() {
        return oidcAccountAttribute;
    }

    public void setOidcAccountAttribute(String oidcAccountAttribute) {
        this.oidcAccountAttribute = oidcAccountAttribute;
    }

    @Column(name = "oidc_bnext_account_attribute")
    public Integer getOidcBnextAccountAttribute() {
        return oidcBnextAccountAttribute;
    }

    public void setOidcBnextAccountAttribute(Integer oidcBnextAccountAttribute) {
        this.oidcBnextAccountAttribute = oidcBnextAccountAttribute;
    }

    @Column(name = "area_unit_label_es")
    public String getAreaUnitLabelES() {
        return areaUnitLabelES;
    }

    public void setAreaUnitLabelES(String areaUnitLabelES) {
        this.areaUnitLabelES = areaUnitLabelES;
    }

    @Column(name = "area_unit_label_es_many")
    public String getAreaUnitLabelManyES() {
        return areaUnitLabelManyES;
    }

    public void setAreaUnitLabelManyES(String areaUnitLabelManyES) {
        this.areaUnitLabelManyES = areaUnitLabelManyES;
    }

    @Column(name = "area_unit_label_es_genre")
    public String getAreaUnitLabelGenreES() {
        return areaUnitLabelGenreES;
    }

    public void setAreaUnitLabelGenreES(String areaUnitLabelGenreES) {
        this.areaUnitLabelGenreES = areaUnitLabelGenreES;
    }

    @Column(name = "area_unit_label_en")
    public String getAreaUnitLabelEN() {
        return areaUnitLabelEN;
    }

    public void setAreaUnitLabelEN(String areaUnitLabelEN) {
        this.areaUnitLabelEN = areaUnitLabelEN;
    }

    @Column(name = "area_unit_label_en_many")
    public String getAreaUnitLabelManyEN() {
        return areaUnitLabelManyEN;
    }

    public void setAreaUnitLabelManyEN(String areaUnitLabelManyEN) {
        this.areaUnitLabelManyEN = areaUnitLabelManyEN;
    }

    @Column(name = "area_unit_label_en_genre")
    public String getAreaUnitLabelGenreEN() {
        return areaUnitLabelGenreEN;
    }

    public void setAreaUnitLabelGenreEN(String areaUnitLabelGenreEN) {
        this.areaUnitLabelGenreEN = areaUnitLabelGenreEN;
    }

    @Column(name = "oidc_authentication_type")
    public Integer getOidcAuthenticationType() {
        return oidcAuthenticationType;
    }

    public void setOidcAuthenticationType(Integer oidcAuthenticationType) {
        this.oidcAuthenticationType = oidcAuthenticationType;
    }

    @Column(name = "oidc_domain")
    public String getOidcDomain() {
        return oidcDomain;
    }

    public void setOidcDomain(String oidcDomain) {
        this.oidcDomain = oidcDomain;
    }

    @Column(name = "oidc_authorization_server_id")
    public String getOidcAuthorizationServerId() {
        return oidcAuthorizationServerId;
    }

    public void setOidcAuthorizationServerId(String oidcAuthorizationServerId) {
        this.oidcAuthorizationServerId = oidcAuthorizationServerId;
    }

    @Column(name = "oidc_client_id")
    public String getOidcClientId() {
        return oidcClientId;
    }

    public void setOidcClientId(String oidcClientId) {
        this.oidcClientId = oidcClientId;
    }

    @Column(name = "oidc_client_secret")
    public String getOidcClientSecret() {
        return oidcClientSecret;
    }

    public void setOidcClientSecret(String oidcClientSecret) {
        this.oidcClientSecret = oidcClientSecret;
    }

    @Column(name = "oidc_allow_register_user")
    public Integer getOidcAllowRegisterUser() {
        return oidcAllowRegisterUser;
    }

    public void setOidcAllowRegisterUser(Integer oidcAllowRegisterUser) {
        this.oidcAllowRegisterUser = oidcAllowRegisterUser;
    }


    @Column(name = "enabled_landing_page")
    public Integer getEnabledLandingPage() {
        return enabledLandingPage;
    }

    public void setEnabledLandingPage(Integer enabledLandingPage) {
        this.enabledLandingPage = enabledLandingPage;
    }

    @Column(name = "landing_page_url")
    public String getLandingPageUrl() {
        return landingPageUrl;
    }

    public void setLandingPageUrl(String landingPageUrl) {
        this.landingPageUrl = landingPageUrl;
    }

    @Column(name = "enable_login_form")
    public Integer getEnableLoginForm() {
        return enableLoginForm;
    }

    public void setEnableLoginForm(Integer enableLoginForm) {
        this.enableLoginForm = enableLoginForm;
    }

    @Column(name = "region_catalog_enabled")
    public Integer getRegionCatalogEnabled() {
        return regionCatalogEnabled;
    }

    public void setRegionCatalogEnabled(Integer regionCatalogEnabled) {
        this.regionCatalogEnabled = regionCatalogEnabled;
    }

    @Column(name = "allow_export_all_records")
    public Integer getAllowExportAllRecords() {
        return allowExportAllRecords;
    }

    public void setAllowExportAllRecords(Integer allowExportAllRecords) {
        this.allowExportAllRecords = allowExportAllRecords;
    }

    @Column(name = "zone_catalog_enabled")
    public Integer getZoneCatalogEnabled() {
        return zoneCatalogEnabled;
    }

    public void setZoneCatalogEnabled(Integer zoneCatalogEnabled) {
        this.zoneCatalogEnabled = zoneCatalogEnabled;
    }

    @Column(name = "update_all_timezone_activity_columns_to_system_timezone")
    public Boolean getUpdateAllTimezoneActivityColumnsToSystemTimezone() {
        return updateAllTimezoneActivityColumnsToSystemTimezone;
    }

    public void setUpdateAllTimezoneActivityColumnsToSystemTimezone(Boolean updateAllTimezoneActivityColumnsToSystemTimezone) {
        this.updateAllTimezoneActivityColumnsToSystemTimezone = updateAllTimezoneActivityColumnsToSystemTimezone;
    }

    @Column(name = "pending_build_slim_rep")
    public Boolean getPendingBuildAllSlimReports() {
        return pendingBuildAllSlimReports;
    }

    public void setPendingBuildAllSlimReports(Boolean pendingBuildAllSlimReports) {
        this.pendingBuildAllSlimReports = pendingBuildAllSlimReports;
    }

    @Column(name = "pending_regen_slim_rep")
    public Boolean getPendingRegenerateAllSlimReports() {
        return pendingRegenerateAllSlimReports;
    }

    public void setPendingRegenerateAllSlimReports(Boolean pendingRegenerateAllSlimReports) {
        this.pendingRegenerateAllSlimReports = pendingRegenerateAllSlimReports;
    }

    @Column(name = "regen_slim_rep_after_freeze")
    @Type(type = "numeric_boolean")
    public Boolean getRegenerateAllSlimReportsAfterFreeze() {
        return regenerateAllSlimReportsAfterFreeze;
    }

    public void setRegenerateAllSlimReportsAfterFreeze(Boolean regenerateAllSlimReportsAfterFreeze) {
        this.regenerateAllSlimReportsAfterFreeze = regenerateAllSlimReportsAfterFreeze;
    }

    @Column(name = "form_report_max_columns")
    public Integer getFormReportMaxColumns() {
        return formReportMaxColumns;
    }

    public void setFormReportMaxColumns(Integer formReportMaxColumns) {
        this.formReportMaxColumns = formReportMaxColumns;
    }

    /**
     * ---- La bandera de 'freezed_survey_answers_run' TAMBIEN enciende esta funcionalidad ---
     *
     * Poner en TRUE para recalcular las tablas de:
     *
     * - survey_answer_migration
     * - survey_answer_migration_column
     *
     *
     * @return
     **/
    @Column(name = "recalculate_survey_migration")
    public Boolean getRecalculateSurveyMigration() {
        return recalculateSurveyMigration;
    }

    public void setRecalculateSurveyMigration(Boolean recalculateSurveyMigration) {
        this.recalculateSurveyMigration = recalculateSurveyMigration;
    }

    @Column(name = "shutdown_recalculate_slim_report")
    public Boolean getShutdownRecalculateSlimReport() {
        return shutdownRecalculateSlimReport;
    }

    public void setShutdownRecalculateSlimReport(Boolean shutdownRecalculateSlimReport) {
        this.shutdownRecalculateSlimReport = shutdownRecalculateSlimReport;
    }

    @Column(name = "recalculate_wp_run")
    public Boolean getRecalculateWorkflowPreviewDataRun() {
        return recalculateWorkflowPreviewDataRun;
    }

    public void setRecalculateWorkflowPreviewDataRun(Boolean recalculateWorkflowPreviewDataRun) {
        this.recalculateWorkflowPreviewDataRun = recalculateWorkflowPreviewDataRun;
    }

    @Column(name = "recalculate_wp_fr_run")
    public Boolean getRecalculateWorkflowFormRequestDataRun() {
        return recalculateWorkflowFormRequestDataRun;
    }

    public void setRecalculateWorkflowFormRequestDataRun(Boolean recalculateWorkflowFormRequestDataRun) {
        this.recalculateWorkflowFormRequestDataRun = recalculateWorkflowFormRequestDataRun;
    }

    @Column(name = "user_direct_boss_optional")
    @Type(type = "numeric_boolean")
    public Boolean getUserDirectBossOptional() {
        return userDirectBossOptional;
    }

    public void setUserDirectBossOptional(Boolean userDirectBossOptional) {
        this.userDirectBossOptional = userDirectBossOptional;
    }

    /**
     * Expresión regular que conicidna con algún ServletPath.
     *
     * @return
     * Ej. "pendings.data-source"
     */
    @Column(name = "trace_request_url_reg_exp")
    public String getTraceRequestUrlRegExp() {
        return traceRequestUrlRegExp;
    }

    public void setTraceRequestUrlRegExp(String traceRequestUrlRegExp) {
        this.traceRequestUrlRegExp = traceRequestUrlRegExp;
    }

    /**
     * Listado de userdIds, separados por comas.
     *
     * @return
     * Ej. "1, 2, 5030"
     */
    @Column(name = "trace_request_user_ids")
    public String getTraceRequestUserIds() {
        return traceRequestUserIds;
    }

    public void setTraceRequestUserIds(String traceRequestUserIds) {
        this.traceRequestUserIds = traceRequestUserIds;
    }


    @Column(name = "ape_lazy_alive_t")
    public Long getApeLazyAliveTime() {
        return apeLazyAliveTime;
    }

    public void setApeLazyAliveTime(Long apeLazyAliveTime) {
        this.apeLazyAliveTime = apeLazyAliveTime;
    }

    @Column(name = "ape_lazy_max_wait_tks")
    public Integer getApeLazyMaxWaitingTasks() {
        return apeLazyMaxWaitingTasks;
    }

    public void setApeLazyMaxWaitingTasks(Integer apeLazyMaxWaitingTasks) {
        this.apeLazyMaxWaitingTasks = apeLazyMaxWaitingTasks;
    }

    @Column(name = "query_pdf_deletion_timeout")
    public Integer getQueryPdfDeletionTimeOut() {
        return queryPdfDeletionTimeOut;
    }

    public void setQueryPdfDeletionTimeOut(Integer queryPdfDeletionTimeOut) {
        this.queryPdfDeletionTimeOut = queryPdfDeletionTimeOut;
    }

    @Column(name = "pdf_clear_alive_t")
    public Long getPdfCleanerAliveTime() {
        return pdfCleanerAliveTime;
    }

    public void setPdfCleanerAliveTime(Long pdfCleanerAliveTime) {
        this.pdfCleanerAliveTime = pdfCleanerAliveTime;
    }

    @Column(name = "pdf_clear_max_wait_tks")
    public Integer getPdfCleanerMaxWaitingTasks() {
        return pdfCleanerMaxWaitingTasks;
    }

    public void setPdfCleanerMaxWaitingTasks(Integer pdfCleanerMaxWaitingTasks) {
        this.pdfCleanerMaxWaitingTasks = pdfCleanerMaxWaitingTasks;
    }

    @Column(name = "db_replication_database_connection_id")
    public Long getDbReplicationDatabaseConnectionId() {
        return dbReplicationDatabaseConnectionId;
    }

    public void setDbReplicationDatabaseConnectionId(Long dbReplicationDatabaseConnectionId) {
        this.dbReplicationDatabaseConnectionId = dbReplicationDatabaseConnectionId;
    }

    @Column(name = "archived")
    @Type(type = "numeric_boolean")
    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    @Column(name = "map_all_survey_answers_columns")
    @Type(type = "numeric_boolean")
    public Boolean getMapAllSurveyAnswersColumns() {
        return mapAllSurveyAnswersColumns;
    }

    public void setMapAllSurveyAnswersColumns(Boolean mapAllSurveyAnswersColumns) {
        this.mapAllSurveyAnswersColumns = mapAllSurveyAnswersColumns;
    }

    @Column(name = "file_to_pdf_pages_size")
    public Integer getFilePdfPagesEnabledCount() {
        return filePdfPagesEnabledCount;
    }

    public void setFilePdfPagesEnabledCount(Integer filePdfPagesEnabledCount) {
        this.filePdfPagesEnabledCount = filePdfPagesEnabledCount;
    }

    @Override
    public int hashCode() {
        Integer hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        //Req 3207
        if (!(object instanceof Settings)) {
            return false;
        }
        Settings other = (Settings) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Settings[ id=" + id + " ]";
    }

}
