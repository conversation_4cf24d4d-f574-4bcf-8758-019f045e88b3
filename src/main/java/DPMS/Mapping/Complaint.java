package DPMS.Mapping;

import Framework.Config.StandardEntity;
import ape.pending.core.StrongBaseAPE;
import ape.pending.core.StrongBaseSourceAPE;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import mx.bnext.core.util.IStatusEnum;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import qms.finding.entity.Finding;
import qms.util.interfaces.IBusinessUnitDepartmentId;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblqueja")
public class Complaint extends StandardEntity<Complaint> implements StrongBaseAPE, StrongBaseSourceAPE<ComplaintSources>, IBusinessUnitDepartmentId,  Serializable {

    private static final long serialVersionUID = 1L;

    private Integer estadoAlumno;
    private Date fechaCreacion;
    private String resultadosEfectividad;
    private Date fechaAceptacion;
    private Integer resultadoEvaluacion = 0;
    private String mailTo;
    private Integer procede;
    private String razonNoProcede;
    private Date fechaModificacion;
    private String respuesta;
    private Date fechaNoProcede;
    private Date fechaAtendida;
    private Date fechaAsignada;
    private Date fechaEvaluada;
    private Date commitmentDate;
    private Integer clasificacion;
    private String analisis;
    private UserRef author;
    private Long businessUnitDepartmentId;
    private BusinessUnitDepartment department;
    private UserRef attendant;
    private ComplaintSources fuente;
    private List<Finding> actionList;
    private Long sourceId;
    private Integer answered = 0;

    private Long priorityId;
    private Long parentComplaintId;
    private String unsatisfactoryReason;
    
    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    
    public static enum STATUS implements IStatusEnum {
        REPORTED(1, IStatusEnum.COLOR_RED),
        ASSIGNED(2, IStatusEnum.COLOR_ORANGE),
        ATTENDED_ON_IMPLEMENTATION(3, IStatusEnum.COLOR_GREEN),
        ATTEND_IMPLEMENTED(4, IStatusEnum.COLOR_DEEP_GREEN),
        IMPLEMENTED_EVALUATING(5, IStatusEnum.COLOR_PURPLE),
        NOT_APPLY(6, IStatusEnum.COLOR_BLACK),
        EVALUATED(10, IStatusEnum.COLOR_BEIGE);
        private final Integer value;
        private final String gridCube; 
        private STATUS(Integer value, String gridCube) {
            this.value = value;
            this.gridCube = gridCube;
        }
        @Override
        public Integer getValue() {
            return this.value;
        }
        @Override
        public String getGridCube() { 
            return this.gridCube;
        }
        @Override
        public IStatusEnum getActiveStatus() {
            return REPORTED;
        }
        public static STATUS getStatus(Integer value) {
            for (STATUS ar : STATUS.class.getEnumConstants()) {
                if(ar.getValue().equals(value)) {
                    return ar;
                }
            }
            return null;
        }
    }
    
   

    public Complaint() {
    }

    public Complaint(Long intquejaid) {
        this.id = intquejaid;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "intquejaid")
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Basic(optional = false)
    @Column(name = "vchclave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Basic(optional = false)
    @Column(name = "txtredaccionqueja", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "intestado_control")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Basic(optional = false)
    @Column(name = "intestado_alumno")
    public Integer getEstadoAlumno() {
        return estadoAlumno;
    }

    public void setEstadoAlumno(Integer estadoAlumno) {
        this.estadoAlumno = estadoAlumno;
    }

    @Basic(optional = false)
    @Column(name = "tspfechahoracreacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    @Column(name = "txtresultadosefectividad")
    public String getResultadosEfectividad() {
        return resultadosEfectividad;
    }

    public void setResultadosEfectividad(String resultadosEfectividad) {
        this.resultadosEfectividad = resultadosEfectividad;
    }

    @Column(name = "tspfechahoraaceptacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaAceptacion() {
        return fechaAceptacion;
    }

    public void setFechaAceptacion(Date fechaAceptacion) {
        this.fechaAceptacion = fechaAceptacion;
    }

    @Basic(optional = false)
    @Column(name = "intresultadoevaluacion")
    public Integer getResultadoEvaluacion() {
        return resultadoEvaluacion;
    }

    public void setResultadoEvaluacion(Integer resultadoEvaluacion) {
        this.resultadoEvaluacion = resultadoEvaluacion;
    }

    @Column(name = "vchmailto")
    public String getMailTo() {
        return mailTo;
    }

    public void setMailTo(String mailTo) {
        this.mailTo = mailTo;
    }

    @Column(name = "intprocede")
    public Integer getProcede() {
        return procede;
    }

    public void setProcede(Integer procede) {
        this.procede = procede;
    }

    @Column(name = "txtrazonnoprocede")
    public String getRazonNoProcede() {
        return razonNoProcede;
    }

    public void setRazonNoProcede(String razonNoProcede) {
        this.razonNoProcede = razonNoProcede;
    }

    @Column(name = "tspfechamodificacion")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    @Column(name = "txtrespuesta")
    public String getRespuesta() {
        return respuesta;
    }

    public void setRespuesta(String respuesta) {
        this.respuesta = respuesta;
    }

    @Column(name = "tspfechahoranoprocede")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaNoProcede() {
        return fechaNoProcede;
    }

    public void setFechaNoProcede(Date fechaNoProcede) {
        this.fechaNoProcede = fechaNoProcede;
    }

    @Column(name = "tspfechahoraatendida")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaAtendida() {
        return fechaAtendida;
    }

    public void setFechaAtendida(Date fechaAtendida) {
        this.fechaAtendida = fechaAtendida;
    }

    @Column(name = "tspfechahoraasignada")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaAsignada() {
        return fechaAsignada;
    }

    public void setFechaAsignada(Date fechaAsignada) {
        this.fechaAsignada = fechaAsignada;
    }

    @Column(name = "tspfechahoraevaluada")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaEvaluada() {
        return fechaEvaluada;
    }

    public void setFechaEvaluada(Date fechaEvaluada) {
        this.fechaEvaluada = fechaEvaluada;
    }

    @Column(name = "commitment_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCommitmentDate() {
        return commitmentDate;
    }

    public void setCommitmentDate(Date commitmentDate) {
        this.commitmentDate = commitmentDate;
    }

    @Column(name = "intclasificacion")
    public Integer getClasificacion() {
        return clasificacion;
    }

    public void setClasificacion(Integer clasificacion) {
        this.clasificacion = clasificacion;
    }

    @Column(name = "txtanalisis")
    public String getAnalisis() {
        return analisis;
    }

    public void setAnalisis(String analisis) {
        this.analisis = analisis;
    }

    @JoinColumn(name = "intfuenteid", referencedColumnName = "inttipoid")
    @ManyToOne(fetch = FetchType.EAGER)
    public ComplaintSources getFuente() {
        return this.fuente;
    }

    public void setFuente(ComplaintSources fuente) {
        this.fuente = fuente;
    }//*/

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Complaint)) {
            return false;
        }
        Complaint other = (Complaint) object;
        return !((this.id == null && other.id != null) 
                || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Complaint[ id=" + id + " ]";
    }

    /**
     * @return the author
     */
    @JoinColumn(name = "intautorid", referencedColumnName = "user_id")
    @ManyToOne
    public UserRef getAuthor() {
        return author;
    }

    /**
     * @param author the author to set
     */
    public void setAuthor(UserRef author) {
        this.author = author;
    }

    /**
     * @return the department
     */
    @JoinColumn(name = "intubicacionid", referencedColumnName = "business_unit_department_id")
    @ManyToOne
    public BusinessUnitDepartment getDepartment() {
        return department;
    }

    /**
     * @param department the department to set
     */
    public void setDepartment(BusinessUnitDepartment department) {
        this.department = department;
    }

    @Column(name = "intubicacionid", updatable = false, insertable = false)
    @Override
    public Long getBusinessUnitDepartmentId() {
        if(businessUnitDepartmentId == null && department != null) {
            businessUnitDepartmentId = department.getId();
        }
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    /**
     * @return the attendant
     */
    @JoinColumn(name = "intworkerid", referencedColumnName = "user_id")
    @ManyToOne
    public UserRef getAttendant() {
        return attendant;
    }

    /**
     * @param attendant the attendant to set
     */
    public void setAttendant(UserRef attendant) {
        this.attendant = attendant;
    }

    /**
     * @return the actionList
     */
    @Fetch(value = FetchMode.SUBSELECT)
    @JoinColumn(name = "vchdetallefuente", referencedColumnName = "vchclave")
    @OneToMany(fetch = FetchType.EAGER)
    public List<Finding> getActionList() {
        return actionList;
    }

    /**
     * @param actionList the actionList to set
     */
    public void setActionList(List<Finding> actionList) {
        this.actionList = actionList;
    }

    
    @Column(name = "answered")
    public Integer getAnswered() {
        return answered;
    }

    public void setAnswered(Integer answered) {
        this.answered = answered;
    }

    @Column(name = "priority_id")
    public Long getPriorityId() {
        return priorityId;
    }

    public void setPriorityId(Long priorityId) {
        this.priorityId = priorityId;
    }

    @Column(name = "parent_complaint_id")
    public Long getParentComplaintId() {
        return parentComplaintId;
    }

    public void setParentComplaintId(Long parentComplaintId) {
        this.parentComplaintId = parentComplaintId;
    }

    @Column(name = "unsatisfacted_reason")
    public String getUnsatisfactoryReason() {
        return unsatisfactoryReason;
    }

    public void setUnsatisfactoryReason(String unsatisfactoryReason) {
        this.unsatisfactoryReason = unsatisfactoryReason;
    }


    @Override
    @Column(name = "intfuenteid", updatable = false, insertable = false)
    public Long getSourceId() {
        return this.sourceId;
    }    

    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }
    
    
}
