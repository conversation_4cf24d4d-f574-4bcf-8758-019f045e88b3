/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Mapping;

import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.util.Objects;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 * <AUTHOR>
 */
@Embeddable
public class AuditsFilesPK implements Serializable {

    private static final Long serialVersionUID = 1L;

    private Long fileId;
    private Long auditId;

    public AuditsFilesPK() {
    }

    public AuditsFilesPK(Long fileId, Long auditId) {
        this.fileId = fileId;
        this.auditId = auditId;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "file_id")
    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "audit_id")
    public Long getAuditId() {
        return auditId;
    }

    public void setAuditId(Long auditId) {
        this.auditId = auditId;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AuditsFilesPK that = (AuditsFilesPK) o;
        return Objects.equals(fileId, that.fileId) && Objects.equals(auditId, that.auditId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fileId, auditId);
    }
}
