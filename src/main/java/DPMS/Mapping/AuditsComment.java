/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Mapping;

import DPMS.DAOInterface.ICodeSequenceDAO;
import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import Framework.Config.Utilities;
import java.util.Date;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.TableGenerator;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "audits_comment")
public class AuditsComment extends StandardEntity<AuditsComment> implements DomainObjectInterface{
    
    private static final long serialVersionUID = 1L;
    
    private Long auditId;
    private String code;
    private String description;
    private Long authorId;
    private String authorName;
    private Date creationTime;
    private Integer deleted;
    private Integer status;

    public AuditsComment() {

    }

    public AuditsComment(Long auditId, String code, String description, Long authorId, String authorName, Date creationTime, Integer deleted, Integer status) {
        this.auditId = auditId;
        this.code = code;
        this.description = description;
        this.authorId = authorId;
        this.authorName = authorName;
        this.creationTime = creationTime;
        this.deleted = deleted;
        this.status = status;
    }
    
    public AuditsComment(Long auditId, String description, Long authorId, String authorName) throws QMSException {
        this.auditId = auditId;
        this.code = ((ICodeSequenceDAO) Utilities.getBean("CodeSequence")).next(CodeSequence.type.CODE_AUDIT_COMMENT);
        this.description = description;
        this.authorId = authorId;
        this.authorName = authorName;
        this.creationTime = new Date();
        this.deleted = IS_NOT_DELETED;
        this.status = ACTIVE_STATUS;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "comment_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getCommentId() {
        return id;
    }

    public void setCommentId(Long id) {
        this.id = id;
    }

    @Column(name = "id_audit")
    public Long getAuditId() {
        return auditId;
    }

    public void setAuditId(Long auditId) {
        this.auditId = auditId;
    }

    @Column(name = "code")
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "description")
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Column(name = "author_id")
    public Long getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    @Column(name = "author_name")
    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    @Column(name = "creation_time")
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }
    
}
