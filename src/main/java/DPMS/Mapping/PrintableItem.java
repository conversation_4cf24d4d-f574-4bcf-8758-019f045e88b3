package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import Framework.Config.StandardEntity;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "printable_item")
public class PrintableItem extends CompositeStandardEntity<PrintableItemPK> implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final Integer INACTIVE_STATUS = 0;
    public static final Integer ACTIVE_STATUS = 1;

    private Printable printable;
    private Item item;

    private Integer status = 1;
    private Integer deleted = 0;

    private PrintableItemPK id;

    public PrintableItem() {
    }

    public PrintableItem(PrintableItemPK id) {
        this();
        this.id = id;
    }

    public PrintableItem(Long printableId, Long itemId) {
        this();
        this.id = new PrintableItemPK(printableId, itemId);
    }

    @Override
    @EmbeddedId
    public PrintableItemPK getId() {
        return id;
    }

    @Override
    public PrintableItemPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(PrintableItemPK id) {
        this.id = id;
    }

    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "deleted")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = StandardEntity.IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "printable_id", insertable = false, updatable = false)
    public Printable getPrintable() {
        return printable;
    }

    public void setPrintable(Printable printable) {
        this.printable = printable;
    }

    @Basic(optional = false)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "item_id", insertable = false, updatable = false)
    public Item getItem() {
        return item;
    }

    public void setItem(Item item) {
        this.item = item;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof PrintableItem)) {
            return false;
        }
        PrintableItem other = (PrintableItem) object;
        return !((this.id == null && other.id != null)
                || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.PrintableItem[ id=" + id + " ]";
    }
}
