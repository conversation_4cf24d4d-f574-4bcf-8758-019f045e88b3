package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "clause_type_files")
    public class ClauseTypeFile extends CompositeStandardEntity<ClauseTypeFilePK> implements ILinkedComposityGrid<ClauseTypeFilePK>, Serializable {
    

    private ClauseTypeFilePK id;

    public ClauseTypeFile() {
    }

    public ClauseTypeFile(ClauseTypeFilePK id) {
        this.id = id;
    }

    public ClauseTypeFile(Long clauseTypeId, Long fileId) {
        this.id = new ClauseTypeFilePK(clauseTypeId, fileId);
    }

    @EmbeddedId
    @Override
    public ClauseTypeFilePK getId() {
        return id;
    }

    @Override
    public ClauseTypeFilePK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(ClauseTypeFilePK id) {
        this.id = id;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 41 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (!Objects.equals(getClass(), obj.getClass())) {
            return false;
        }
        final ClauseTypeFile other = (ClauseTypeFile) obj;
        return !(this.id != other.id && (this.id == null || !this.id.equals(other.id)));
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.ClauseTypeFile[ id=" + id + " ]";
    }

}
