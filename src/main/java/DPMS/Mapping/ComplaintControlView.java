package DPMS.Mapping;

import Framework.Config.StandardEntity;
import static Framework.Config.StandardEntity.IS_DELETED;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Subselect(value = ComplaintControlView.COMPLAINT_CONTROL_VIEW_SQL)
@Immutable
public class ComplaintControlView extends StandardEntity<ComplaintControlView> implements Serializable {

    private static final long serialVersionUID = 1L;
    
    public static final String COMPLAINT_CONTROL_VIEW_SQL = ""
            + " SELECT "
                + " q.intquejaid AS id "
                + " ,q.intestado_control AS int_estado "
                + " ,q.intautorid AS author_id "
                + " ,u1.first_name + ' ' + u1.primary_last_name AS author "
                + " ,q.intworkerid AS attendant_id "
                + " ,u2.first_name + ' ' + u2.primary_last_name AS attendant "
                + " ,q.vchclave AS vch_clave "
                + " ,q.tspfechahoracreacion AS fecha_reportada "
                + " ,q.txtredaccionqueja AS vch_descripcion "
                + " ,q.tspfechahoraaceptacion AS fecha_verificada "
                + " ,q.tspfechahoranoprocede AS fecha_no_procede "
                + " ,q.tspfechahoraatendida AS fecha_respondida "
                + " ,q.tspfechahoraasignada AS fecha_asignada "
                + " ,q.commitment_date "
                + " ,q.tspfechahoraevaluada AS fecha_finalizada "
                + " ,ubi.description + ' - ' + b.description AS department "
                + " ,ubi.department_id AS department_id "
                + " ,b.business_unit_id as business_unit_id "
                + " ,cat.vchtexto AS fuente "
                + " ,tipo.vchtexto "
                + " ,q.intresultadoevaluacion "
                + " ,p.description AS priority "
                + " ,q.int_borrado "
            + " FROM tblqueja q "
            + " INNER JOIN users u1 ON q.intautorid = u1.user_id "
            + " LEFT  JOIN users u2 ON q.intworkerid = u2.user_id "
            + " INNER JOIN business_unit_department AS bdep ON bdep.business_unit_department_id = q.intubicacionid  "
            + " INNER JOIN business_unit AS b ON b.business_unit_id = bdep.business_unit_id "
            + " INNER JOIN department ubi ON ubi.department_id = bdep.department_id "
            + " INNER JOIN tblcatalogo cat ON cat.inttipoid = q.intfuenteid "
                + " AND cat.intcatalogoid = 5 "
            + " LEFT JOIN tblcatalogo tipo ON q.intclasificacion = tipo.inttipoid "
                + " AND tipo.intcatalogoid = 12 "
            + " LEFT JOIN priority p ON q.priority_id = p.priority_id ";
    
    private BigInteger authorId;
    private String author;
    private BigInteger attendantId;
    private String attendant;
    private Date fechaReportada;
    private Date fechaVerificada;
    private Date fechaNoProcede;
    private Date fechaRespondida;
    private Date fechaAsignada;
    private Date commitmentDate;
    private Date fechaFinalizada;
    private String department;
    private BigInteger departmentId;
    private String fuente;
    private String vchtexto;
    private Long businessUnitId;
    private Integer resultadoEvaluacion;
    private String priority;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public ComplaintControlView() {
    }

    @Id
    @Column(name = "id")
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Basic(optional = false)
    @Column(name = "vch_clave", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @Column(name = "commitment_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCommitmentDate() {
        return commitmentDate;
    }

    public void setCommitmentDate(Date commitmentDate) {
        this.commitmentDate = commitmentDate;
    }

    @Basic(optional = false)
    @Column(name = "vch_descripcion", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "int_estado")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "int_borrado")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Column(name = "author_id")
    public BigInteger getAuthorId() {
        return authorId;
    }

    public void setAuthorId(BigInteger authorId) {
        this.authorId = authorId;
    }

    @Column(name = "author")
    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    @Column(name = "attendant_id")
    public BigInteger getAttendantId() {
        return attendantId;
    }

    public void setAttendantId(BigInteger attendantId) {
        this.attendantId = attendantId;
    }

    @Column(name = "attendant")
    public String getAttendant() {
        return attendant;
    }

    public void setAttendant(String attendant) {
        this.attendant = attendant;
    }

    @Column(name = "fecha_reportada")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaReportada() {
        return fechaReportada;
    }

    public void setFechaReportada(Date fechaReportada) {
        this.fechaReportada = fechaReportada;
    }

    @Column(name = "fecha_verificada")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaVerificada() {
        return fechaVerificada;
    }

    public void setFechaVerificada(Date fechaVerificada) {
        this.fechaVerificada = fechaVerificada;
    }

    @Column(name = "fecha_no_procede")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaNoProcede() {
        return fechaNoProcede;
    }

    public void setFechaNoProcede(Date fechaNoProcede) {
        this.fechaNoProcede = fechaNoProcede;
    }

    @Column(name = "fecha_respondida")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaRespondida() {
        return fechaRespondida;
    }

    public void setFechaRespondida(Date fechaRespondida) {
        this.fechaRespondida = fechaRespondida;
    }

    @Column(name = "fecha_asignada")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaAsignada() {
        return fechaAsignada;
    }

    public void setFechaAsignada(Date fechaAsignada) {
        this.fechaAsignada = fechaAsignada;
    }

    @Column(name = "fecha_finalizada")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaFinalizada() {
        return fechaFinalizada;
    }

    public void setFechaFinalizada(Date fechaFinalizada) {
        this.fechaFinalizada = fechaFinalizada;
    }

    @Column(name = "department")
    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    @Column(name = "department_id")
    public BigInteger getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(BigInteger departmentId) {
        this.departmentId = departmentId;
    }

    @Column(name = "fuente")
    public String getFuente() {
        return fuente;
    }

    public void setFuente(String fuente) {
        this.fuente = fuente;
    }

    @Column(name = "vchtexto")
    public String getTipo() {
        return vchtexto;
    }

    public void setTipo(String vchtexto) {
        this.vchtexto = vchtexto;
    }

    @Column(name = "business_unit_id")
    public Long getBusinessUnitId() {
        return businessUnitId;
    }

    public void setBusinessUnitId(Long businessUnitId) {
        this.businessUnitId = businessUnitId;
    }
    
    @Column(name = "priority")
    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    @Column(name = "intresultadoevaluacion")
    public Integer getResultadoEvaluacion() {
        return resultadoEvaluacion;
    }

    public void setResultadoEvaluacion(Integer resultadoEvaluacion) {
        this.resultadoEvaluacion = resultadoEvaluacion;
    }
    
}
