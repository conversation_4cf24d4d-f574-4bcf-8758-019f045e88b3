package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import mx.bnext.access.IBnextModule;
import mx.bnext.access.IService;
import mx.bnext.access.Module;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.util.interfaces.EntityType;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "audit_type")
public class AuditType extends StandardEntity<AuditType> implements DomainObjectInterface, EntityType {

    private static final long serialVersionUID = 1L;
    public static final String PREFIX = "AUT-";
    public static final Integer PROCESS_SCOPE = 1;
    public static final Integer AREA_SCOPE = 2;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    private Integer qualifiedQuestion = 0;
    private Integer findingCapable = 0;
    private Integer confirmedByAudited = 0;
    private Integer acceptedByAudited = 0;
    private Integer scope = 0;
    private Integer addActivity = 0;
    private Integer minimumScore = 0;
    private Integer supportStaff = 0;
    private Integer technicalExperts = 0;
    private Integer auditorsInTraining = 0;
        
    public AuditType() {
        this.deleted = IS_NOT_DELETED;
        this.status = ACTIVE_STATUS;
        this.acceptedByAudited = 0;
        this.confirmedByAudited = 0;
        this.findingCapable = 0;
        this.qualifiedQuestion = 0;
    }

    public AuditType(Long audit_type_id) {
        this.id = audit_type_id;
    }

    @Id
    @Basic
    @Column(name = "audit_type_id", precision = 19, scale = 0)
    @Override
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long audit_type_id) {
        this.id = audit_type_id;
    }

    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer status) {
        if (status == null) {
            status = 1;
        }
        this.status = status;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer deleted) {
        if (deleted == null) {
            deleted = IS_DELETED;
        }
        this.deleted = deleted;
    }

    @Column(name = "qualified_question")
    public Integer getQualifiedQuestion() {
        return qualifiedQuestion;
    }

    public void setQualifiedQuestion(Integer qualifiedQuestion) {
        this.qualifiedQuestion = qualifiedQuestion;
    }

    @Column(name = "finding_capable")
    public Integer getFindingCapable() {
        return findingCapable;
    }

    public void setFindingCapable(Integer findingCapable) {
        this.findingCapable = findingCapable;
    }

    /**
     * Si vale 1 las auditorías relacionadas deberán ser confirmadas por el auditado.
     * 
     * @return 
     */
    @Column(name = "confirmed_by_audited")
    public Integer getConfirmedByAudited() {
        return confirmedByAudited;
    }

    public void setConfirmedByAudited(Integer ConfirmedByAudited) {
        this.confirmedByAudited = ConfirmedByAudited;
    }

    @Column(name = "accepted_by_audited")
    public Integer getAcceptedByAudited() {
        return acceptedByAudited;
    }

    public void setAcceptedByAudited(Integer AcceptedByAudited) {
        this.acceptedByAudited = AcceptedByAudited;
    }

    @Column(name = "scope")
    public Integer getScope() {
        return scope;
    }

    public void setScope(Integer scope) {
        this.scope = scope;
    }

    /**
     * @return the addActivity
     */
    @Column(name = "add_activity")
    public Integer getAddActivity() {
        return addActivity;
    }

    /**
     * @param addActivity the addActivity to set
     */
    public void setAddActivity(Integer addActivity) {
        this.addActivity = addActivity;
    }

    /**
     * @return the minimumScore
     */
    @Column(name = "minimum_score")
    public Integer getMinimumScore() {
        return minimumScore;
    }

    /**
     * @param minimumScore the minimumScore to set
     */
    public void setMinimumScore(Integer minimumScore) {
        this.minimumScore = minimumScore;
    }

    /**
     * @return the supportStaff
     */
    @Column(name = "support_staff")
    public Integer getSupportStaff() {
        return supportStaff;
    }

    /**
     * @param supportStaff the supportStaff to set
     */
    public void setSupportStaff(Integer supportStaff) {
        this.supportStaff = supportStaff;
    }

    /**
     * @return the technicalExperts
     */
    @Column(name = "technical_experts")
    public Integer getTechnicalExperts() {
        return technicalExperts;
    }

    /**
     * @param technicalExperts the technicalExperts to set
     */
    public void setTechnicalExperts(Integer technicalExperts) {
        this.technicalExperts = technicalExperts;
    }

    /**
     * @return the auditorsInTraining
     */
    @Column(name = "auditors_in_training")
    public Integer getAuditorsInTraining() {
        return auditorsInTraining;
    }

    /**
     * @param auditorsInTraining the auditorsInTraining to set
     */
    public void setAuditorsInTraining(Integer auditorsInTraining) {
        this.auditorsInTraining = auditorsInTraining;
    }

    public static enum Servicios implements IService {
        /* SERVICIOS *//* SERVICIOS */
        QUALIFIED_QUESTION("qualifiedQuestion"), 
        FINDING_CAPABLE("findingCapable"),
        CONFIRMED_BY_AUDITED("confirmedByAudited"), 
        ACCEPTED_BY_AUDITED("acceptedByAudited"), 
        ADD_ACTIVITY("addActivity"), 
        MINIMUM_SCORE("minimumScore"),
        SUPPORT_STAFF("supportStaff"),
        TECHNICAL_EXPERTS("technicalExperts"),
        AUDITORS_IN_TRAINING("auditorsInTraining"),
        NONE(""),
        ALL("1");
        String code;

        private Servicios(String code) {
            this.code = code;
        }

        @Override
        public String getCode() {
            return this.code;
        }

        @Override
        public IBnextModule getModule() {
            return Module.AUDIT;
        }

        @Override
        public IBnextModule getSubModule() {
            return null;
        }
        

        public static List<Servicios> getAll() {
            List<Servicios> s = new ArrayList<>();
            s.addAll(Arrays.asList(Servicios.values()));
            s.remove(NONE);
            return s;
        }

        public static Servicios getServicio(String code) {
            try {
                for (Servicios service : Servicios.class.getEnumConstants()) {
                    if (service == null || service.getCode() == null) {
                        continue;
                    }
                    if (code.equals(service.getCode())) {
                        return service;
                    }
                }
                return NONE;
            } catch (Exception e) {
                return NONE;
            }
        }
    }

}
