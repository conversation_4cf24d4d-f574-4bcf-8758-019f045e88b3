/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.util.Objects;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Table;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "business_units_timework_sync")
public class BusinessUnitTimeworkSync extends CompositeStandardEntity<BusinessUnitTimeworkSyncPK>  implements ILinkedComposityGrid<BusinessUnitTimeworkSyncPK> {
    
    private static final long serialVersionUID = 1L;
    
    BusinessUnitTimeworkSyncPK id;

    public BusinessUnitTimeworkSync() {
    }

    public BusinessUnitTimeworkSync(BusinessUnitTimeworkSyncPK id) {
        this.id = id;
    }
    
    public BusinessUnitTimeworkSync(Integer settingsId, Long businessUnitId) {
        this.id = new BusinessUnitTimeworkSyncPK(settingsId, businessUnitId);
    }

    @Override
    
    public BusinessUnitTimeworkSyncPK identifuerValue() {
        return this.getId();
    }

    @Override
    @EmbeddedId
    public BusinessUnitTimeworkSyncPK getId() {
        return this.id;
    }

    @Override
    public void setId(BusinessUnitTimeworkSyncPK id) {
        this.id = id;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 53 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final BusinessUnitTimeworkSync other = (BusinessUnitTimeworkSync) obj;
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
         return "DPMS.Mapping.BusinessUnitTimeworkSync[ id=" + id + " ]";
    }
    
    
}
