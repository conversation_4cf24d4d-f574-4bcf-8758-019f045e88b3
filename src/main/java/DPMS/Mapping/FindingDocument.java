/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import java.io.Serializable;
import java.util.Objects;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Table;
import qms.util.interfaces.ILinkedCompositeEntity;
import qms.util.interfaces.ILinkedComposityGrid;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "finding_document")
public class FindingDocument extends CompositeStandardEntity<FindingDocumentPK> implements ILinkedCompositeEntity<FindingDocumentPK>, ILinkedComposityGrid<FindingDocumentPK>, Serializable {

    private static final long serialVersionUID = 1L;
    private FindingDocumentPK id;

    public FindingDocument() {
    }

    public FindingDocument(FindingDocumentPK id) {
        this.id = id;
    }
    
    public FindingDocument(Long findingId, Long documentId) {
        this.id = new FindingDocumentPK(findingId, documentId);
    }
    
    @Override
    public FindingDocumentPK identifuerValue() {
        return id;
    }

    @Override
    @EmbeddedId
    public FindingDocumentPK getId() {
        return id;
    }

    @Override
    public void setId(FindingDocumentPK id) {
        this.id = id;
    }
    
    @Override
    public int hashCode() {
        int hash = 5;
        hash = 67 * hash + Objects.hashCode(this.id);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final FindingDocument other = (FindingDocument) obj;
        return Objects.equals(this.id, other.getId());
    }
    
}
