package DPMS.Mapping;

import Framework.Config.CompositeStandardEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import qms.util.interfaces.ILinkedComposityGrid;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;


/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tblcarpetausuario")
public class NodeUser extends CompositeStandardEntity<NodeUserPK> implements Serializable, ILinkedComposityGrid<NodeUserPK> {

    private static final long serialVersionUID = 1L;
    
    private NodeUserPK id;
    private Date tspfechahoracreacion;

    public NodeUser() {
    }

    public NodeUser(NodeUserPK id) {
        this.id = id;
    }

    public NodeUser(Long intnodoid, Long intusuarioid) {
        this.id = new NodeUserPK(intnodoid, intusuarioid);
    }

    @Override
    @EmbeddedId
    public NodeUserPK getId() {
        return id;
    }

    @Override
    public NodeUserPK identifuerValue() {
        return getId();
    }

    @Override
    public void setId(NodeUserPK id) {
        this.id = id;
    }

    @Column(name = "tspfechahoracreacion", insertable = false)
    @Temporal(TemporalType.TIMESTAMP)
    public Date getFechaCreacion() {
        return tspfechahoracreacion;
    }

    public void setFechaCreacion(Date tspfechahoracreacion) {
        this.tspfechahoracreacion = tspfechahoracreacion;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3337
        if (!(object instanceof NodeUser)) {
            return false;
        }
        NodeUser other = (NodeUser) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.NodeUser[ id=" + id + " ]";
    }
}
