package DPMS.Mapping;

import Framework.Config.DomainObject;
import java.io.Serializable;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "tbl_option")

public class Option extends DomainObject implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Long optionId;
        private String optionType;
        private String optionText;
        private Long questionId;
        private String optionDependents;
    // @Max(value=?)  @Min(value=?)//if you know range of your decimal fields consider using these annotations to enforce field validation
    private Double optionWeight;

    public Option() {
    }

    public Option(Long optionId) {
        this.optionId = optionId;
    }

    @Id
    @Basic(optional = false)
    @Column(name = "option_id")
    @GeneratedValue(generator="increment",strategy=GenerationType.SEQUENCE)
    @GenericGenerator(name="increment", strategy = "increment")
    
    public Long getOptionId() {
        return optionId;
    }

    public void setOptionId(Long optionId) {
        this.optionId = optionId;
    }

    @Column(name = "option_type")
    public String getOptionType() {
        return optionType;
    }

    public void setOptionType(String optionType) {
        this.optionType = optionType;
    }

    @Column(name = "option_text")
    public String getOptionText() {
        return optionText;
    }

    public void setOptionText(String optionText) {
        this.optionText = optionText;
    }

    @Column(name = "question_id")
    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    @Column(name = "option_dependents")
    public String getOptionDependents() {
        return optionDependents;
    }

    public void setOptionDependents(String optionDependents) {
        this.optionDependents = optionDependents;
    }

    @Column(name = "option_weight")
    public Double getOptionWeight() {
        return optionWeight;
    }

    public void setOptionWeight(Double optionWeight) {
        this.optionWeight = optionWeight;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (optionId != null ? optionId.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3340
        if (!(object instanceof Option)) {
            return false;
        }
        Option other = (Option) object;
        if ((this.optionId == null && other.optionId != null) || (this.optionId != null && !this.optionId.equals(other.optionId))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Option[ optionId=" + optionId + " ]";
    }
    
}