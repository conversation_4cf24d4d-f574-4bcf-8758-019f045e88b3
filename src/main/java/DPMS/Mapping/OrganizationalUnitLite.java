package DPMS.Mapping;

import Framework.Config.DomainObjectInterface;
import Framework.Config.StandardEntity;
import bnext.reference.UserRef;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;
import qms.framework.interfaces.IPlainOrganizationalUnit;

@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "organizational_unit")
public class OrganizationalUnitLite extends StandardEntity<OrganizationalUnitLite>
        implements DomainObjectInterface, IPlainOrganizationalUnit {

    private static final long serialVersionUID = 1L;
    
    private Long predecessorId;
    private Long documentManagerId;
    private UserRef documentManager;

    private String code = "";
    private String description = "";
    private Integer status = 1;
    private Integer deleted = 0;
    

    public OrganizationalUnitLite() {
        this.setDeleted(IS_NOT_DELETED);
        this.setStatus(ACTIVE_STATUS);
    }

    public OrganizationalUnitLite(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "organizational_unit_id", precision = 19, scale = 0)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 100)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }


    @Basic(optional = false)
    @Column(name = "code", nullable = false, length = 255)
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String clave) {
        this.code = clave;
    }

    @LocalizedField
    @Basic(optional = false)
    @Column(name = "description", nullable = false, length = 255)
    @Override
    public String getDescription() {
        return description;
    }
    @Override
    public void setDescription(String descripcion) {
        this.description = descripcion;
    }

    @Column(name = "status")
    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public void setStatus(Integer estatus) {
        if(estatus == null) {
            status = 1;
        }
        this.status = estatus;
    }

    @Column(name = "deleted")
    @Override
    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public void setDeleted(Integer borrado) {
        if(borrado == null) {
            borrado = IS_DELETED;
        }
        this.deleted = borrado;
    }
    
    @Override
    public boolean equals(Object object) {
        // Req 3343
        if (!(object instanceof DPMS.Mapping.OrganizationalUnitLite)) {
            return false;
        }
        DPMS.Mapping.OrganizationalUnitLite other = (DPMS.Mapping.OrganizationalUnitLite) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Organizational Unit[ id=" + id + " ]";
    }

    @Column(name = "predecessor")
    @Override
    public Long getPredecessorId() {
        return predecessorId;
    }

    @Override
    public void setPredecessorId(Long predecessorId) {
        this.predecessorId = predecessorId;
    }

    @Column(name = "document_manager_id")
    @Override
    public Long getDocumentManagerId() {
        return documentManagerId;
    }

    @Override
    public void setDocumentManagerId(Long documentManagerId) {
        this.documentManagerId = documentManagerId;
    }

    @JsonIgnore
    @JoinColumn(name = "document_manager_id", referencedColumnName = "user_id",
            insertable = false, updatable = false)
    @OneToOne(optional = false, fetch = FetchType.EAGER)
    public UserRef getDocumentManager() {
        return documentManager;
    }

    public void setDocumentManager(UserRef documentManager) {
        this.documentManager = documentManager;
    }
}