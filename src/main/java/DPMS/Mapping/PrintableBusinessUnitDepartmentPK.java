package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class PrintableBusinessUnitDepartmentPK implements Serializable {

    private Long printableId;
    private Long businessUnitDepartmentId;

    public PrintableBusinessUnitDepartmentPK() {
    }

    public PrintableBusinessUnitDepartmentPK(Long printableId, Long businessUnitDepartmentId) {
        this.printableId = printableId;
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Basic(optional = false)
    @Column(name = "printable_id")
    public Long getPrintableId() {
        return printableId;
    }

    public void setPrintableId(Long printableId) {
        this.printableId = printableId;
    }

    @Basic(optional = false)
    @Column(name = "business_unit_department_id")
    public Long getBusinessUnitDepartmentId() {
        return businessUnitDepartmentId;
    }

    public void setBusinessUnitDepartmentId(Long businessUnitDepartmentId) {
        this.businessUnitDepartmentId = businessUnitDepartmentId;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.printableId);
        hash = 97 * hash + Objects.hashCode(this.businessUnitDepartmentId);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final PrintableBusinessUnitDepartmentPK other = (PrintableBusinessUnitDepartmentPK) obj;
        if (!Objects.equals(this.printableId, other.printableId)) {
            return false;
        }
        if (!Objects.equals(this.businessUnitDepartmentId, other.businessUnitDepartmentId)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PrintableBusinessUnitDepartmentPK{" + "printableId=" + printableId + ", businessUnitDepartmentId=" + businessUnitDepartmentId + '}';
    }

}
