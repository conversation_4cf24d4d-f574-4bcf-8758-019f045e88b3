package DPMS.Mapping;

import Framework.Config.DomainObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import qms.framework.core.LocalizedEntity;
import qms.framework.core.LocalizedField;

/**
 *
 * <AUTHOR> Cavazos Galindo
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@LocalizedEntity
@Table(name = "organizational_unit")
public class OrganizationalUnitSimple extends DomainObject implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String description;

    public OrganizationalUnitSimple() {
    }
    
    public OrganizationalUnitSimple(Long id) {
        this.id = id;
    }

    @Id
    @Basic
    @Column(name = "organizational_unit_id", precision = 19)
    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @LocalizedField
    @Column(name = "description")
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public boolean equals(Object object) {
        // Req 3344
        if (!(object instanceof DPMS.Mapping.OrganizationalUnitLite)) {
            return false;
        }
        DPMS.Mapping.OrganizationalUnitSimple other = (DPMS.Mapping.OrganizationalUnitSimple) object;
        return !((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id)));
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + (this.id != null ? this.id.hashCode() : 0);
        return hash;
    }

    @Override
    public String toString() {
        return "DPMS.Mapping.Organizational Unit[ id=" + id + " ]";
    }
    
}