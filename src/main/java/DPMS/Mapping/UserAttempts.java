/**
 * Copyright (C) Block Networks S.A. de C.V. - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */
package DPMS.Mapping;

import Framework.Config.DomainObject;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import com.fasterxml.jackson.annotation.JsonInclude;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.TableGenerator;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Table(name = "users_attepmts")
public class UserAttempts extends DomainObject implements Serializable {
    
    private String username;
    private Long userId;
    private Integer attepmt;
    private Date lastModified;
    private String userAgent;
    private String ipAddress;

    

    public UserAttempts() {
    }
    
    public UserAttempts(Long id) {
        this.id = id;
    }

    public UserAttempts(Long id, String username, Long userId, Integer attepmt, Date lastModified, String userAgent, String ipAddress) {
        this.id = id;
        this.username = username;
        this.userId = userId;
        this.attepmt = attepmt;
        this.lastModified = lastModified;
        this.userAgent = userAgent;
        this.ipAddress = ipAddress;
    }

    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.TABLE, generator = "id-gen")
    @TableGenerator(name = "id-gen", allocationSize = 50)
    @Column(name = "id_attempt", nullable = false)
    @Override
    public Long getId() {
        return id;
    }
    
    @Override
    public void setId(Long id) {
        this.id = id;
    }
    
    @Column(name = "username" , nullable = false)
    public String getUsername() {
        return username;
    }

    public void setUsername(String userId) {
        this.username = userId;
    }

    @Column(name = "user_id")
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Column(name = "attempt")
    public Integer getAttepmt() {
        return attepmt;
    }

    public void setAttepmt(Integer attepmt) {
        this.attepmt = attepmt;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_modified")
    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    @Column(name = "user_agent")
    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    @Column(name = "ip_address")
    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    
}
