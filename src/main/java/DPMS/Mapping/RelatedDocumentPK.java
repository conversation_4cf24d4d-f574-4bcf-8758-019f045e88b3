package DPMS.Mapping;

import java.io.Serializable;
import java.util.Objects;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import qms.util.annotations.DialogId;
import qms.util.annotations.GroundId;

/**
 *
 * <AUTHOR>
 */
@Embeddable
public class RelatedDocumentPK implements Serializable {

    private Long intdocumentoid;
    private Long intpadreid;

    public RelatedDocumentPK() {
    }

    public RelatedDocumentPK(Long intdocumentoid, Long intpadreid) {
        this.intdocumentoid = intdocumentoid;
        this.intpadreid = intpadreid;
    }

    @DialogId
    @Basic(optional = false)
    @Column(name = "document_id2")
    public Long getDocumentIdA() {
        return intdocumentoid;
    }

    public void setDocumentIdA(Long intdocumentoid) {
        this.intdocumentoid = intdocumentoid;
    }

    @GroundId
    @Basic(optional = false)
    @Column(name = "document_id1")
    public Long getDocumentIdB() {
        return intpadreid;
    }

    public void setDocumentIdB(Long intpadreid) {
        this.intpadreid = intpadreid;
    }

    @Override
    public int hashCode() {
        int hash = 3;
        hash = 97 * hash + Objects.hashCode(this.intdocumentoid);
        hash = 97 * hash + Objects.hashCode(this.intpadreid);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RelatedDocumentPK other = (RelatedDocumentPK) obj;
        if (!Objects.equals(this.intdocumentoid, other.intdocumentoid)) {
            return false;
        }
        if (!Objects.equals(this.intpadreid, other.intpadreid)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "RelatedDocumentPK{" + "intdocumentoid=" + intdocumentoid + ", intpadreid=" + intpadreid + '}';
    }
}
