package DPMS.Acceso;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IProfileDAO;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Profile;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import java.util.Objects;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
public class CRUD_Profile extends CRUD_Generic<Profile> {

    @SMDMethod
    public GenericSaveHandle save(final Profile entity) throws QMSException {
        GenericSaveHandle gsh = new GenericSaveHandle();
        Profile ent = entity;
        IProfileDAO dao = getBean(IProfileDAO.class);
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Acceso.CRUD_Profile.save ... " + Utilities.getSerializedObj(ent));
        }
        boolean nuevo = (ent.getId() == -1);
        if ((getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO)
                && (getLoggedUserServices().contains(ProfileServices.ADMON_SISTEMA)
                || getLoggedUserServices().contains(ProfileServices.ADMON_ACCESOS))) || isAdmin()) {
            if (nuevo) {
                ICodeSequenceDAO sq = (ICodeSequenceDAO) getBean("CodeSequence");
                ent.setCode("PEF-" + Utilities.todayDateBy("yy") + sq.next(CodeSequence.type.NONE));
            }
            //Se intenta guardar en la base de datos
            ent.setIntBReporteReunion(0);
            if (!nuevo) {
                Profile oldProfile = dao.HQLT_findById(ent.getId());
                if (!oldProfile.equals(ent)) {
                    ent = dao.loadLicenseCode(ent);
                    dao.setValidServiceValues(ent, getLoggedUserId());
                    ent = dao.editProfile(ent, oldProfile, getLoggedUserDto());
                } else {   
                    dao.setValidServiceValues(ent, getLoggedUserId());
                    ent = dao.makePersistent(ent);
                }
            } else {
                dao.setValidServiceValues(ent, getLoggedUserId());
                ent = dao.makePersistent(ent);
            }

        } else {
            ent = null;
        }
        if (ent != null) {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage("{\"tipo\":" + (nuevo ? "\"add_success\"" : "\"edit_success\"") + "}");
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    @SMDMethod
    @Override
    public GenericSaveHandle toggleStatus(Long id) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        IProfileDAO dao = getBean(IProfileDAO.class);
        getLogger().trace("toggleStatus ... " + id);
        Profile p = dao.HQLT_findById(id, false);
        p.setStatus(Objects.equals(p.getStatus(), Profile.ESTATUS_ACTIVO) ? Profile.ESTATUS_INACTIVO : Profile.ESTATUS_ACTIVO);
        //Se intenta guardar en la base de datos
        p = dao.makePersistent(p);
        if (p == null) {
            gsh.setOperationEstatus(0);
        } else {
            gsh.setOperationEstatus(1);
            gsh.setSuccessMessage(""
                    + "{"
                    + "tipo:'toggle_status_success'"
                    + ",fijo:[" + Utilities.getSerializedObj(new TextHasValue("clave", p.getCode())) + "]"
                    + "}");
        }
        return gsh;
    }
    
    @SMDMethod
    @Override
    public GridInfo getRows(SortedPagedFilter filter) {
        String scope = getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO) || isAdmin()?
                "1=1"
                :"c.intBUsuarioPlanta=1";
        filter.getCriteria().put("<condition>", scope);
        return super.getRows(filter);
    }
    
    @SMDMethod
    public GridInfo getRowsActive(SortedPagedFilter filter) {
        String scope = getLoggedUserServices().contains(ProfileServices.USUARIO_CORPORATIVO) || isAdmin()?
                "1=1 AND c.status=1 "
                :"c.intBUsuarioPlanta=1 AND c.status=1 ";
        filter.getCriteria().put("<condition>", scope);
        //filter.getCriteria().put("c.status", "1");
        return super.getRows(filter);
    }
 
    @SMDMethod
    public boolean checkProfile(String profileName) {
        return getLoggedUserServices().contains(ProfileServices.getServicio(profileName));
    }
    
}
