package DPMS.Acceso;

import DPMS.DAO.HibernateDAO_Position;
import DPMS.DAOInterface.IBusinessUnitDAO;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IDepartmentProcessDAO;
import DPMS.DAOInterface.IPositionDAO;
import DPMS.DAOInterface.IPositionLoadDAO;
import DPMS.DAOInterface.IProcessDAO;
import DPMS.DAOInterface.IReceiptAcknowledgmentDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.DepartmentProcess;
import DPMS.Mapping.Position;
import DPMS.Mapping.PositionLoad;
import DPMS.Mapping.Process;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import com.google.common.base.CaseFormat;
import java.util.List;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.configuration.dto.JobPendingCountDTO;
import qms.util.LinkedGridConfig;
import qms.util.interfaces.LinkedGridType;

/**
 *
 * <AUTHOR> Limas
 */
public class CRUD_Position extends CRUD_Generic<Position> {
    
    /**
     * Ejecucion de la implementacion del metodo para guardar puestos
     *
     * @param entity puesto a guardar/actualizar
     * @return GenericSaveHandle con los resultados de la operacion
     * <AUTHOR> Cavazos Galindo
     * @since *********
     * @see HibernateDAO_Position#save(DPMS.Mapping.Position)
     */
    @SMDMethod
    public GenericSaveHandle save(final Position entity) {
        IPositionLoadDAO dao = Utilities.getBean(IPositionLoadDAO.class);
        Position ent = entity;
        if (ent.getId() > 0) {
            ent = dao.loadLicenseCode(ent);
        }
        return dao.save(ent, getLoggedUserDto());
    }

    @SMDMethod
    public GridInfo getRowsFacility(SortedPagedFilter filter) {
        IPositionLoadDAO dao = Utilities.getBean(IPositionLoadDAO.class);
        String where = isAdmin() 
            ? " c.deleted = 0 AND c.perfil.intBUsuarioPlanta = 1 "
            : " c.deleted = 0 AND c.perfil.intBUsuarioPlanta = 1 AND " + HibernateDAO_Position.validEntitiesFilterByUserPosition
                .replace(":userId", getLoggedUserId().toString())
                .replace(":servicio", ProfileServices.getCodedServices("perfil", getServiciosActivos()))
        ;

        return dao.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id,"
                    + " c.status AS status,"
                    + " c.code AS code,"
                    + " c.description AS description,"
                    + " perfil.description AS perfilDescription,"
                    + " perfil.id AS perfilId,"
                    + " c.licenseCode AS licenseCode,"
                    + " corp.description AS corpDescription,"
                    + " count(1) AS departmentCount,"
                    + " max(p.id) AS maxDepartmentId,"
                    + " une.description AS uneDescription,"
                    + " string_agg(p.description) AS departamentos,"
                    + " string_agg(process.description) as procesos"
                + " )"
                + " FROM " + Position.class.getCanonicalName() + " c "
                + " LEFT JOIN c.perfil perfil "
                + " LEFT JOIN c.corp corp "
                + " LEFT JOIN c.une une "
                + " LEFT JOIN c.departamentos p "
                + " LEFT JOIN c.departmentProcess dp "
                + " LEFT JOIN dp.process process "
                + " WHERE " + where
                + " GROUP BY"
                    + " c.id "
                    + " ,c.status "
                    + " ,c.code "
                    + " ,c.description "
                    + " ,perfil.description "
                    + " ,perfil.id"
                    + " ,c.licenseCode "
                    + " ,corp.description "
                    + " ,une.description"
                + "", filter);
    }
    
    @SMDMethod
    public GridInfo getRowsCorporate(SortedPagedFilter filter) {
        IPositionLoadDAO dao = Utilities.getBean(IPositionLoadDAO.class);
        return dao.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id,"
                    + " c.status AS status,"
                    + " c.code AS code,"
                    + " c.description AS description,"
                    + " p.description AS perfilDescription,"
                    + " p.id AS perfilId,"
                    + " c.licenseCode AS licenseCode,"
                    + " corp.description AS corpDescription"
                + " )"
                + " FROM " + Position.class.getCanonicalName() + " c "
                + " JOIN c.perfil p "
                + " JOIN c.corp corp "
                + " WHERE"
                    + " c.deleted = 0"
                    + " AND ( "
                        + " p.intBUsuarioCorporativo = 1 "
                        + " OR p.fillOnly = 1 "
                    + " )"
                    + "", filter);
    }
    
    @SMDMethod
    public GridInfo getRowsForUser(SortedPagedFilter filter) {
        filter.getCriteria().put("status", Position.ESTATUS_ACTIVO.toString());
        return this.getRows(filter);
    }

    @SMDMethod
    @Override
    public GenericSaveHandle toggleStatus(Long id) {
        getLogger().trace("toggleStatus ... " + id);
        final IPositionDAO dao = Utilities.getBean(IPositionDAO.class);
        return dao.toggleStatus(id, getLoggedUserDto());
    }

    @SMDMethod
    public GridInfo<BusinessUnit> unes(SortedPagedFilter filter) {
        IBusinessUnitDAO DAO = Utilities.getBean(IBusinessUnitDAO.class);
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT p.une.id "
                + "FROM DPMS.Mapping.Position p "
                + "WHERE p.id = " + firstParamCurrentEntityId()
                + ")");
        return DAO.getRows(filter);
    }

    @SMDMethod
    public GridInfo<Process> procesos(SortedPagedFilter filter) {
        IProcessDAO DAO = Utilities.getBean(IProcessDAO.class);
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT proceso.id "
                + "FROM DPMS.Mapping.Position p "
                + "JOIN p.procesos proceso "
                + "WHERE p.id = " + firstParamCurrentEntityId()
                + ")");/**/
        return DAO.getRows(filter);
    }
    
    public List<PositionLoad> loadPositionsCorp(Long corp) {
        IPositionLoadDAO DAO = Utilities.getBean(IPositionLoadDAO.class);
        return DAO.HQLT_findByQuery("FROM DPMS.Mapping.PositionLoad c WHERE c.corp.id = " + corp);
    }

    public List<PositionLoad> loadPositionsUNE(Long une) {
        IPositionLoadDAO DAO = Utilities.getBean(IPositionLoadDAO.class);
        return DAO.HQLT_findByQuery("FROM DPMS.Mapping.PositionLoad c WHERE c.une.id = " + une);
    }

    @Override
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        getLogger().trace("linkedGridId: {}, groundId: {}", linkedGridId, groundId);
        LinkedGridType linkedGridEnum = LinkedGridType.valueOf(CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, linkedGridId));
        switch (linkedGridEnum) { 
            case DEPARTMENTS:
                return new LinkedGridConfig(
                        "c.status = " + BusinessUnitDepartmentLoad.STATUS.ACTIVE.getValue(),
                        IBusinessUnitDepartmentLoadDAO.class, new String[]{
                            "id", "code", "description", "businessUnit.description"
                        },
                        "pos.id = " + groundId,
                        Position.class.getCanonicalName() + " pos JOIN pos.departamentos "
                );
            case PROCESSES:
                return new LinkedGridConfig(
                        "c.status = " + DepartmentProcess.ACTIVE_STATUS,
                        IDepartmentProcessDAO.class, new String[]{
                            "id", "code", "description", "department.description", "department.id"
                        },
                        "pos.id = " + groundId,
                        Position.class.getCanonicalName() + " pos JOIN pos.departmentProcess "
                );
        }
        return super.getLinkedGridConfig(linkedGridId, groundId);
    }
    
    @SMDMethod
    public JobPendingCountDTO getJobPendingsCount(final Long jobId) {
        getLogger().trace("getJobPendingsCount jobId: {}", jobId);
        if (!isAdmin() || !getLoggedUserServices().contains(ProfileServices.ADMON_ACCESOS)) {
            getLogger().error("Access denied to getJobPendingsCount jobId: {}", jobId);
            return new JobPendingCountDTO();
        }
        if (jobId == null || jobId == -1L) {
            return new JobPendingCountDTO();
        }
        final JobPendingCountDTO count = new JobPendingCountDTO();
        final IReceiptAcknowledgmentDAO ackDao = getBean(IReceiptAcknowledgmentDAO.class);
        final Long countDeliverCopies = ackDao.getCopiesToDeliverCountByJob(jobId);
        count.setDeliverCopies(countDeliverCopies);
        return count;
    }

}
