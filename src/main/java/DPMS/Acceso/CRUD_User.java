package DPMS.Acceso;

import DPMS.DAO.HibernateDAO_User;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IPositionDAO;
import DPMS.DAOInterface.IReceiptAcknowledgmentDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.DAOInterface.IUserIdNameDAO;
import DPMS.Mapping.BusinessUnit;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.BusinessUnitDepartmentLoad;
import DPMS.Mapping.Department;
import DPMS.Mapping.DepartmentLite;
import DPMS.Mapping.Node;
import DPMS.Mapping.Position;
import DPMS.Mapping.PositionLoad;
import DPMS.Mapping.User;
import DPMS.Mapping.UserPosition;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextHasValue;
import Framework.Config.UserHasValue;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericHibernateDAO;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.IPendingQueries;
import bnext.licensing.LicenseUtil;
import bnext.reference.UserRef;
import com.google.common.base.CaseFormat;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.LockAcquisitionException;
import org.springframework.dao.DataIntegrityViolationException;
import qms.access.dto.ILoggedUser;
import qms.access.dto.LoggedUser;
import qms.access.dto.UserEditionInfoDTO;
import qms.configuration.core.UserPositionService;
import qms.configuration.dto.UserPendingCountDTO;
import qms.configuration.pending.imp.ToAssignJob;
import qms.framework.entity.Owner;
import qms.framework.entity.OwnerTeam;
import qms.framework.entity.OwnerTeamUser;
import qms.framework.rest.SecurityUtils;
import qms.framework.security.UserLogin;
import qms.framework.util.ExceptionUtils;
import qms.framework.util.UserCreationSource;
import qms.util.CustomLinkedSelect;
import qms.util.DAOException;
import qms.util.LinkedGridConfig;
import qms.util.UserUtil;
import qms.util.dto.FormApproverAnalystByDepartment;
import qms.util.dto.FormApproverAnalystSelection;
import qms.util.interfaces.LinkedGridType;

/**
 *
 * <AUTHOR> Limas
 */

public class CRUD_User extends CRUD_Generic<User> {

    private static final long serialVersionUID = 1L;
    
    private static final LoadingCache<Long, Object> USER_LOCKS = CacheBuilder.newBuilder()
        .expireAfterAccess(60, TimeUnit.MINUTES)
        .maximumSize(5000)
        .build(CacheLoader.from(Object::new));
    
    @SMDMethod
    public UserRef loadUserRef(Long userId) {
        return getUntypedDAO().HQLT_findById(UserRef.class, userId);
    }

    @SMDMethod
    @Override
    public GridInfo<User> getRows(SortedPagedFilter filter) {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        filter.getCriteria().put("<condition>", ""
                + "exists ("
                + "SELECT u.id "
                + "FROM DPMS.Mapping.User u "
                + "JOIN u.puestos puesto "
                + "WHERE u.id = c.id"
                + ")");
        return super.getRows(filter, usrDAO);
    }
    
    @SMDMethod
    public GridInfo<User> getRowsActive(SortedPagedFilter filter) {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        filter.getCriteria().put("<condition>", ""
                + "exists ("
                + "SELECT u.id "
                + "FROM DPMS.Mapping.User u "
                + "JOIN u.puestos puesto "
                + "WHERE u.id = c.id "
                + "AND u.status = " + User.ACTIVE_STATUS
                + ")");
        return super.getRows(filter, usrDAO);
    }
    
    private GridInfo<Map<String, Object>> getUserQuery(int status, SortedPagedFilter filter, boolean onlyWithJobs){
        final IUserDAO daoUser = getBean(IUserDAO.class);
        daoUser.setValidEntitiesFilter(filter, getLoggedUserId().toString(), getServiciosActivos(), isAdmin(), status);
        String query = " SELECT new map("
                    + " c.id AS id,"
                    + " c.status AS status,"
                    + " c.code AS code,"
                    + " c.description AS description,"
                    + " c.cuenta AS cuenta,"
                    + " c.correo AS correo,"
                    + " c.licenseCode AS licenseCode,"
                    + " c.creationSource AS creationSource,"
                    + " c.authTypeIntegrated AS authTypeIntegrated,"
                    + " c.inactiveBySystem AS inactiveBySystem,"
                    + " c.authTypeLdap AS authTypeLdap,"
                    + " c.authTypeLandingPage AS authTypeLandingPage,"
                    + " c.authTypeOidc AS authTypeOidc,"
                    + " c.oidcProvider AS oidcProvider,"
                    + " c.isAnonymous AS isAnonymous,"
                    + " c.isAway AS isAway,"
                    + " c.isAwaySince AS isAwaySince,"
                    + " c.isAwayReason AS isAwayReason,"
                    + " boss.id AS bossId,"
                    + " boss.description AS bossName,"
                    + " businessUnit.id AS businessUnitId,"
                    + " businessUnit.description AS businessUnitName,"
                    + " businessUnitDepartment.id AS businessUnitDepartmentId,"
                    + " businessUnitDepartment.description AS businessUnitDepartmentName,"
                    + " string_agg(job.description) as jobName,"
                    + " string_agg(prof.description) as profileName,"
                    + " c.createdDate as createdDate,"
                    + " u.description as createdByDescription"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c "
                + (!onlyWithJobs ? " LEFT" : "") + " JOIN c.puestos job"
                + (!onlyWithJobs ? " LEFT" : "") + " JOIN job.perfil prof"
                + " LEFT JOIN c.boss boss "
                + " LEFT JOIN c.businessUnitDepartment businessUnitDepartment "
                + " LEFT JOIN businessUnitDepartment.businessUnit businessUnit "
                + " LEFT JOIN " + User.class.getCanonicalName() + " u ON u.id = c.createdBy "
                + " WHERE c.deleted = 0"
                + " AND c.status = " + status
                + " GROUP BY"
                    + " c.id,"
                    + " c.status,"
                    + " c.code,"
                    + " c.description,"
                    + " c.cuenta,"
                    + " c.correo,"
                    + " c.licenseCode,"
                    + " c.creationSource,"
                    + " c.authTypeIntegrated,"
                    + " c.inactiveBySystem,"
                    + " c.authTypeLdap,"
                    + " c.authTypeLandingPage,"
                    + " c.authTypeOidc,"
                    + " c.oidcProvider,"
                    + " c.isAnonymous,"
                    + " c.isAway,"
                    + " c.isAwaySince,"
                    + " c.isAwayReason,"
                    + " boss.id,"
                    + " boss.description,"
                    + " businessUnit.id,"
                    + " businessUnit.description,"
                    + " businessUnitDepartment.id,"
                    + " businessUnitDepartment.description,"
                    + " c.createdDate,"
                    + " u.description";
        return daoUser.HQL_getRows(query, filter);
    }
    
        @SMDMethod
    public GridInfo<Map<String, Object>> getRowsSimple(SortedPagedFilter filter) {
        return getUserQuery( User.STATUS.ACTIVE.getValue(), filter, true);
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getUserWithTeamsRows(SortedPagedFilter filter) {
        final IUserDAO daoUser = getBean(IUserDAO.class);
        daoUser.setValidEntitiesFilter(filter, getLoggedUserId().toString(), getServiciosActivos(), isAdmin());
        return daoUser.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id,"
                    + " c.status AS status,"
                    + " c.code AS code,"
                    + " c.description AS description,"
                    + " c.cuenta AS cuenta,"
                    + " c.correo AS correo,"
                    + " c.licenseCode AS licenseCode,"
                    + " boss.id AS bossId,"
                    + " boss.description AS bossName,"
                    + " businessUnit.id AS businessUnitId,"
                    + " businessUnit.description AS businessUnitName,"
                    + " businessUnitDepartment.id AS businessUnitDepartmentId,"
                    + " businessUnitDepartment.description AS businessUnitDepartmentName,"
                    + " string_agg(team.description) as teamName"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " LEFT JOIN c.boss boss "
                + " LEFT JOIN c.businessUnitDepartment businessUnitDepartment "
                + " LEFT JOIN businessUnitDepartment.businessUnit businessUnit "
                + " LEFT JOIN c.userTeams team "
                + " WHERE c.deleted = 0"
                + " AND c.status = " + User.STATUS.ACTIVE.getValue()
                + " GROUP BY"
                    + " c.id,"
                    + " c.status,"
                    + " c.code,"
                    + " c.description,"
                    + " c.cuenta,"
                    + " c.correo,"
                    + " c.licenseCode,"
                    + " boss.id,"
                    + " boss.description,"
                    + " businessUnit.id,"
                    + " businessUnit.description,"
                    + " businessUnitDepartment.id,"
                    + " businessUnitDepartment.description"
                + "", filter
        );
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsToActivate(final SortedPagedFilter filter) {
        if (!isAdmin() && !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_ACCESOS)) {
            return Utilities.EMPTY_GRID_INFO;
        }
        final IUserDAO dao = getBean(IUserDAO.class);
        filter.getCriteria().put("<condition>", UserPositionService.USER_TO_ACTIVATE_FILTER);
        return dao.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id"
                    + ", c.status AS status"
                    + ", c.deleted AS deleted"
                    + ", c.code AS code"
                    + ", c.description AS description"
                    + ", c.cuenta AS cuenta"
                    + ", c.correo AS correo"
                    + ", c.licenseCode AS licenseCode"
                    + ", boss.description AS boss_description"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " LEFT JOIN c.boss boss",
                filter
        );
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsRef(SortedPagedFilter filter) {
        IUserDAO dao = getBean(IUserDAO.class);
        filter.getCriteria().put("<condition>", ""
                + "exists ("
                    + "SELECT 1 "
                    + "FROM " + User.class.getCanonicalName() + " u "
                    + "JOIN u.puestos puesto "
                    + "WHERE u.id = c.id"
                + ")");
        if (filter.getField().getOrderBy() == null || filter.getField().getOrderBy().isEmpty()) {
            filter.getField().setOrderBy("id");
            filter.setDirection((byte) 2);
        }  
        dao.setValidEntitiesFilter(filter, getLoggedUserId().toString(), getServiciosActivos(), isAdmin());
        return dao.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id"
                    + ", c.status AS status"
                    + ", c.deleted AS deleted"
                    + ", c.code AS code"
                    + ", c.description AS description"
                    + ", string_agg(job.description) AS job_description"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " LEFT JOIN c.puestos job"
                + " WHERE c.deleted = 0"
                + " GROUP BY "
                    + " c.id"
                    + ", c.status"
                    + ", c.deleted"
                    + ", c.code"
                    + ", c.description", 
                filter
        );
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsSimpleActive(SortedPagedFilter filter) {
        IUserDAO daoUser = getBean(IUserDAO.class);
        filter.getCriteria().put("<condition>", ""
                + "exists ("
                    + "SELECT u.id "
                    + "FROM " + User.class.getCanonicalName() + " u "
                    + "JOIN u.puestos puesto "
                    + "WHERE u.id = c.id "
                    + "AND u.status = " + User.ACTIVE_STATUS
                + ")");
        daoUser.setValidEntitiesFilter(filter, getLoggedUserId().toString(), getServiciosActivos(), isAdmin());
        return daoUser.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id"
                    + ", c.code AS code"
                    + ", c.description AS description"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c",
                filter
        );
    }

    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsWithoutPosition(SortedPagedFilter filter) {
        if (!isAdmin() 
            && (
                !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_ACCESOS) 
                || !isEscalationManager()
            )
        ) {
            return Utilities.EMPTY_GRID_INFO;
        }
        IUserDAO usrDAO = getBean(IUserDAO.class);
        filter.getCriteria().put("<condition>", ""
                + " c.status = " + User.STATUS.ACTIVE.getValue()
                + " AND c.deleted = 0 "
                + " AND NOT EXISTS ("
                    + "SELECT u.id "
                    + "FROM " + User.class.getCanonicalName() + " u "
                    + "JOIN u.puestos puesto "
                    + "WHERE u.id = c.id"
                + ")");
        getLogger().debug("{}", filter.getCriteria().get("<condition>"));
        return usrDAO.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id"
                    + ", c.code AS code"
                    + ", c.description AS description"
                    + ", c.status AS status"
                    + ", c.avatarId AS avatarId"
                    + ", c.cuenta AS cuenta"
                    + ", c.correo AS correo"
                    + ", c.licenseCode AS licenseCode"
                    + ", c.creationSource AS creationSource"
                    + ", c.authTypeIntegrated AS authTypeIntegrated"
                    + ", c.inactiveBySystem AS inactiveBySystem"
                    + ", c.authTypeLdap AS authTypeLdap"
                    + ", c.authTypeLandingPage AS authTypeLandingPage"
                    + ", c.authTypeOidc AS authTypeOidc"
                    + ", c.oidcProvider AS oidcProvider"
                    + ", c.createdDate AS createdDate"
                    + ", boss.description AS boss_description"
                    + ", businessUnitDepartment.description AS businessUnitDepartment_description"
                    + ", businessUnit.description AS businessUnit_description"
                    + ", u.description as createdByDescription"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " LEFT JOIN c.boss boss"
                + " LEFT JOIN c.businessUnitDepartment businessUnitDepartment"
                + " LEFT JOIN businessUnitDepartment.businessUnit businessUnit"
                + " LEFT JOIN " + User.class.getCanonicalName() + " u ON u.id = c.createdBy ",
                filter
        );
    }

    @SMDMethod
    public GridInfo<Map<String, Object>> getToAssignJob(SortedPagedFilter filter) {
        final IUntypedDAO dao = getUntypedDAO();
        final IPendingQueries ops = new ToAssignJob(dao);
        filter.getCriteria().put("<condition>", ops.filterRecordsByUser(getLoggedUserId(), "c").toString());
        return dao.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id"
                    + ", c.code AS code"
                    + ", c.cuenta AS cuenta"
                    + ", c.description AS description"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c",
                filter
        );
    }

    /**
     * Ejecución de la implementacion del metodo para guardar/actualizar usuarios
     * 
     * @param user usuario que será guardado o actualizado
     * @return GenericSaveHandle con lo datos de la operación
     */
    @SMDMethod
    public GenericSaveHandle save(final User user) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Acceso.CRUD_User @ save: " + Utilities.getSerializedObj(user));
        }
        final Long userId = user.getId();
        if (userId != null && !Objects.equals(userId, -1L) && USER_LOCKS.getIfPresent(userId) != null) {
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setErrorMessage(HibernateDAO_User.ALREADY_IN_EDTION);
            return gsh;
        }
        try {
            final LoggedUser loggedUser = getLoggedUserDto();
            if (userId != null && !Objects.equals(userId, -1L)) {
                try {
                    synchronized (USER_LOCKS.getUnchecked(userId)) {
                        closeSession(true, user.getId());
                        user.setCloseSession(true);
                        // Ponemos en 0 para evitar renovar la contraseña en la creación de usuarios nuevos,
                        // ya que, ya se registraron con las reglas de validación de la contraseña
                        user.setAskToRenewPassword(false);
                        // Lógica para EDITAR
                        final IUserDAO dao = getBean(IUserDAO.class);
                        Map<String, Object> result = dao.HQL_findSimpleMap(""
                            + " SELECT new map( "
                                + " u.isAway as isAway "
                                + " ,u.avatarId as avatarId "
                                + " ,u.isAnonymous as isAnonymous "
                            + " )"
                            + " FROM " + User.class.getCanonicalName() + " u "
                            + " WHERE u.id = :userId", "userId", userId
                        );
                        user.setIsAway((Boolean) result.get("isAway"));
                        user.setAvatarId((Long) result.get("avatarId"));
                        final boolean anonymous = Boolean.TRUE.equals(result.get("isAway"));
                        if (isAdmin()) {
                            if (anonymous && !Boolean.TRUE.equals(user.getIsAnonymous())) {
                                // Si era anonimo y ya no lo es, se ponen las banderas de pedir información personal
                                user.setAskToRenewPassword(true);
                                user.setAskToRenewLocation(true);
                                user.setAskToRenewTimezone(true);
                            }
                        } else {
                            // Mantiene lo que ya tenía, solo el administrador puede cambiar si un usuario es anonimo
                            user.setIsAnonymous(anonymous);
                        }
                        this.setAnonymousCache(user, user.getIsAnonymous());
                        final GenericSaveHandle gsh = dao.save(user, loggedUser);
                        if (gsh.getOperationEstatus() == 0) {
                            return gsh;
                        }
                        LicenseUtil.updateLicencesCount();
                        return gsh;
                    }
                } finally {
                    clearUserEditionLock(userId);
                }
            } else {
                if (!isAdmin()) {
                    // Solo el administrador puede cambiar si un usuario es anónimo
                    user.setIsAnonymous(false);
                }
                this.setAnonymousCache(user, user.getIsAnonymous());
                // Ponemos en 0 para evitar renovar la contraseña en la creación de usuarios nuevos,
                // ya que, ya se registraron con las reglas de validación de la contraseña
                user.setAskToRenewPassword(false);
                // Lógica para dar de ALTA
                user.setIsAway(false);
                if (user.getCreationSource() == null) {
                    user.setCreationSource(UserCreationSource.CATALOG.getValue());
                }
                final IUserDAO dao = getBean(IUserDAO.class);
                final GenericSaveHandle gsh = dao.save(user, loggedUser);
                if (gsh.getOperationEstatus() == 0) {
                    return gsh;
                }
                LicenseUtil.updateLicencesCount();
                return gsh;
            }
        } catch (final DataIntegrityViolationException | LockAcquisitionException | DAOException ex) {
            return onFailedSaveUser(user, ex);
        } catch (final Exception ex) {
            return handleGenericError(ex, user);
        }
    }

    private void setAnonymousCache(final User user, boolean isAnonymous) {
        if (isAnonymous) {
            Utilities.addAnonymous(user.getCuenta());
            user.setAskToRenewPassword(false);
            user.setAskToRenewLocation(false);
            user.setAskToRenewTimezone(false);
            user.setTimezone(Utilities.getSettings().getTimeZone());
        } else {
            Utilities.removeAnonymous(user.getCuenta());
        }
    }

    private void clearUserEditionLock(final Long userId) {
        try {
            if (userId == null || USER_LOCKS.getIfPresent(userId) == null) {
                return;
            }
            USER_LOCKS.invalidate(userId);
        } catch(final Exception e) {
           getLogger().error(
                        "Failed to release user edition lock for userId {}",
                        new Object[]{userId, e}
           );   
        }
    }

    private void closeSession(final Boolean closeSession, final Long userId) {
        if (Boolean.TRUE.equals(closeSession)) {
            final UserLogin loginInfo = new UserLogin(userId);
            loginInfo.removeSession(true);
        }
    }
    
    private GenericSaveHandle onFailedSaveUser(final User entity, final Exception ex) {
        if (ex.getCause() != null && ex.getCause() instanceof ConstraintViolationException) {
            final GenericSaveHandle gsh = new GenericSaveHandle();
            final Throwable cause = getCause(ex);
            if (cause.getMessage() != null && cause.getMessage().contains(HibernateDAO_User.UQ_CODE_KEY_NAME)) {
                getLogger().error(
                        "Failed to create user {} with account {}. The code {} is repeated.",
                        entity.getDescription(),
                        entity.getCuenta(),
                        entity.getCode());
                gsh.setErrorMessage(HibernateDAO_User.DUPLICATE_KEY_CODE);
                return gsh;
            } else if (cause.getMessage() != null && cause.getMessage().contains(HibernateDAO_User.UQ_ACCOUNT_KEY_NAME)) {
                getLogger().error(
                        "Failed to create user {} with account {}. The account {} is repeated.",
                        entity.getDescription(),
                        entity.getCode(),
                        entity.getCuenta());
                gsh.setErrorMessage(HibernateDAO_User.DUPLICATE_KEY_ACCOUNT);
                return gsh;
            }
            return handleGenericError(ex, entity);
        } else if (ex instanceof LockAcquisitionException
                || (ex.getCause() != null && ex.getCause() instanceof LockAcquisitionException)) {
            getLogger().error(
                    "Failed to update user {} with account {}. Could to obtain lock.",
                    entity.getDescription(),
                    entity.getCuenta(), ex);
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setErrorMessage(HibernateDAO_User.TEMPORALY_UNAVAILABLE_RETRY_SAVE);
            return gsh;
        }
        return handleGenericError(ex, entity);
    }

    private GenericSaveHandle handleGenericError(final Exception ex, final User entity) {
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final Throwable cause = getCause(ex);
        getLogger().error("Failed to save user {}.", entity, cause);
        gsh.setErrorMessage(ExceptionUtils.getRootCauseMessage(cause));
        return gsh;
    }

    private Throwable getCause(final Exception ex) {
        final Throwable cause = ExceptionUtils.getRootCause(ex);
        if (cause != null) {
            return cause;
        }
        return ex;
    }
    
    @SMDMethod
    @Override
    public GenericSaveHandle toggleStatus(Long id) {  
        IUserDAO dao = getBean(IUserDAO.class);
        GenericSaveHandle gsh = dao.toggleStatus(id, getLoggedUserDto());
        LicenseUtil.updateLicencesCount();
        return gsh;
    }

    @SMDMethod
    public GridInfo<PositionLoad> puestos(SortedPagedFilter filter) {
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT puesto.id "
                + "FROM DPMS.Mapping.User u "
                + "JOIN u.puestos puesto "
                + "WHERE u.id = " + firstParamCurrentEntityId()
                + ")");/**/
        return new GenericHibernateDAO<PositionLoad, Long>() {
        }.getRows(filter);
    }

    /**
     * Returns an entity of user to be used on selector
     *
     * @deprecated usar getUserByBusinessUnit, getUserByDepartment... etc
     * @param id the id for user
     * @return entity loaded with values.
     */
    @SMDMethod
    public UserRef getValuesForCombo(Long id) {
        System.out.println("Getting User for Combo with id: " + id);
        GenericHibernateDAO<UserRef, Long> localDAO =
                new GenericHibernateDAO<UserRef, Long>() {
                };
        return localDAO.HQLT_findById(id, false);
    }

    /**
     *
     * @param userId Id of the user that must be in the list
     * @return Returns a list of entities of user where they belong to the user's bussines unit
     */
    @SMDMethod
    public List<UserRef> getUsersByCurrentUserBusinessUnits(Long userId) {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.getUsersByUserBusinessUnits(userId, getLoggedUserId(), isAdmin(), getServiciosActivos(), true);
    }
    
    /**
     *
     * @return Returns a list of entities of user where they belong to the user's bussines unit
     */
    @SMDMethod
    public List<UserRef> getAllUsersByCurrentUserBusinessUnits() {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.getUsersByUserBusinessUnits(0L, getLoggedUserId(), isAdmin(), getServiciosActivos(), false);
    }

    /**
     * Returns an entity of user to be used on selector
     *
     * @param id the id for user
     * @return entity loaded with values.
     */
    @SMDMethod
    public List<User> getUserWithValidationAccess(long id) {
        System.out.println("Getting getUserWithValidationAccess for Combo with id: ");
        GenericHibernateDAO<User, Long> localDAO =
                new GenericHibernateDAO<User, Long>() {
                };
        String filter = "c.validationRole >= 1";
        return localDAO.HQLT_findByQueryFilter(filter);
    }

    /**
     * Returns an entity of user to be used on selector
     *
     * @return entity loaded with values.
     */
    @SMDMethod
    public List<User> getUserWithValidationAccess() {
        System.out.println("Getting getUserWithValidationAccess for Combo with id: ");

        GenericHibernateDAO<User, Long> localDAO =
                new GenericHibernateDAO<User, Long>() {
                };
        String filter = "c.validationRole >= 1";
        return localDAO.HQLT_findByQueryFilter(filter);
    }

    @SMDMethod
    public List<UserRef> getUserByBusinessUnit(String buId) {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.getUserByBusinessUnit(buId,isAdmin());
    }
    
    @SMDMethod
    public List<UserRef> getActiveUserByBusinessUnit(String buId) {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.getActiveUserByBusinessUnit(buId,isAdmin());
    }

    @SMDMethod
    public List<UserRef> getUserByDepartment(String deptId) {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.getUserByDepartment(deptId);
    }

    /**
     * Función para cargar los departamentos de un usuario
     *
     * @return lista de departamentos
     * @since 2.3.2.38
     * @autor Germán Lares
     */
    @SMDMethod
    public List<BusinessUnitDepartmentLite> getDepartments() {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.getDepartments(this.getLoggedUserId().toString(), isAdmin(), isCorporative());
    }

    @SMDMethod
    public User getCurrentUser() {
        IUserDAO DAO = getBean(IUserDAO.class);
        return DAO.HQLT_findById(getLoggedUserId());
    }
    
    @SMDMethod
    public UserRef getCurrentUserIdName() {
        IUserIdNameDAO dao = getBean(IUserIdNameDAO.class);
        return dao.HQLT_findById(getLoggedUserId());
    }
    
    @SMDMethod
    public List<Long> getSurveyDocumentAccess() {
        IUserDAO dao = getBean(IUserDAO.class);
        return dao.getSurveyDocumentAccess(getLoggedUserId());
    }
            
    private static final String GET_ROWS_USER_POSITION_ACTIVE = 
            /*Filtro para usuarios activos*/
              " exists ("
            + " SELECT u.id "
            + " FROM DPMS.Mapping.User u "
            + " WHERE u.id = c.userId"
            + " AND u.status = 1 "
            + ")";
    private static final String GET_ROWS_USER_POSITION_ACCESS_CONTROL = 
            /*Filtro para usuarios corporativos*/
            " exists ("
            + " SELECT u.id "
            + " FROM DPMS.Mapping.User u "
            + " JOIN u.puestos p "
            + " JOIN p.perfil pr "
            + " WHERE ("
                + " exists ("
                    + " SELECT pu.une.id "
                    + " FROM DPMS.Mapping.User us "
                    + " JOIN us.puestos pu "
                    + " JOIN pu.perfil perfil "
                    + " WHERE us.id = :userId "
                    + " AND ( "
                        + " p.une.id = pu.une.id "
                        + " OR u.businessUnitId = pu.une.id "
                        + " OR p.une.id = us.businessUnitId "
                        + " OR u.businessUnitId = us.businessUnitId "
                    + " ) "
                    + " AND 1 IN (:servicesMine)"
                + " )"
                + " AND u.id = c.userId "
                + " AND u.status = 1 "
            + ")"
            + ") ";
    private static final String GET_ROWS_USER_POSITION_CONDITION = ""
    /*Filtro para usuarios dentro de los puestos seleccionados para la copia controlada*/
                + " AND exists ("
                    + " SELECT ph.id.positionId "
                    + " FROM DPMS.Mapping.PhysicalReader ph "
                    + " WHERE ph.id.positionId = c.positionId AND ph.id.documentId = :docId" 
                + " )";

    public static final String GET_READERS_TO_ADD_BY_POSITION = ""
            + " ( "
            //Si el NODO es un repositorio se puede ver
            + " EXISTS ("
                + " SELECT 1 "
                + " FROM " + Node.class.getCanonicalName() + " node "
                + " WHERE node.id = :nodeId AND parent = 0"
            + " )"
            //solo usuarios que pueden ver el documento por permiso de su PLANTA
            + " OR c.positionId IN ("
                + " SELECT p.id"
                + " FROM DPMS.Mapping.NodeBusinessUnit nbu, DPMS.Mapping.Position p"
                + " WHERE nbu.id.nodeId = :nodeId"
                + " AND nbu.id.businessUnitId = p.une.id"
            + " )"
            + " OR c.userId IN ("
                + " SELECT u.id"
                + " FROM DPMS.Mapping.NodeBusinessUnit nbu, DPMS.Mapping.User u"
                + " WHERE nbu.id.nodeId = :nodeId"
                + " AND nbu.id.businessUnitId = u.businessUnitId"
            + " )"
            //solo usuarios que pueden ver el documento por permiso de su PLANTA-DEPARTAMENTO
            + " OR c.positionId IN ("
                + " SELECT p.id"
                + " FROM DPMS.Mapping.NodeBusinessUnitDepartment nbud, DPMS.Mapping.Position p"
                + " JOIN p.departamentos d"
                + " WHERE nbud.id.nodeId = :nodeId AND nbud.id.businessUnitDepartmentId = d.id"
            + " )"
            + " OR c.userId IN ("
                + " SELECT u.id"
                + " FROM DPMS.Mapping.NodeBusinessUnitDepartment nbud, DPMS.Mapping.User u"
                + " WHERE nbud.id.nodeId = :nodeId"
                + " AND nbud.id.businessUnitDepartmentId = u.businessUnitDepartmentId"
            + " )"
            //solo usuarios que pueden ver el documento por permiso de su USUARIO
            + " OR c.positionId IN ("
                + " SELECT p.id"
                + " FROM DPMS.Mapping.NodeUser nu, DPMS.Mapping.User u"
                + " JOIN u.puestos p"
                + " WHERE"
                + " u.id = nu.id.userId"
                + " AND nu.id.nodeId = :nodeId"
            + " )"
            //solo usuarios que pueden ver el documento por permiso de su PROCESO
            + " OR c.positionId IN ("
                + " SELECT p.id"
                + " FROM DPMS.Mapping.NodeArea na, DPMS.Mapping.Position p"
                + " JOIN p.procesos pro"
                + " WHERE na.id.nodeId =  :nodeId"
                + " AND na.id.areaId = pro.id"
            + " )"
        + " )"
    ;
    /**
     * Metodo para obtener todos los usuarios relacionados al documento en base al puesto asignado
     * como lector del documento.
     *
     * @param filter
     * @return GridInfo<UserPosition> de usuarios con sus respectivos puestos asignados.
     *
     * <AUTHOR> Cavazos Galindo
     * @since 2.3.2.117
     */
    @SMDMethod
    public GridInfo getRowsUserPosition(SortedPagedFilter filter) {

        IUntypedDAO dao = getUntypedDAO();
        String 
            documentId = this.firstParamCurrentEntityId(),
            filterByUser
        ;
        Long nodeId = dao.HQL_findSimpleLong("SELECT c.nodo.id FROM DPMS.Mapping.Document c WHERE c.id = " + documentId);
        
        if (Utilities.getSettings().getPositionForControlledCopy() == 1) {
            if(isAdmin()) {
                filterByUser = ""
                        + GET_ROWS_USER_POSITION_ACTIVE
                        + GET_ROWS_USER_POSITION_CONDITION.replaceAll(":docId",documentId).replaceAll(":nodeId", nodeId.toString());
            } else {
                filterByUser = ""
                        + GET_ROWS_USER_POSITION_ACCESS_CONTROL
                        + GET_ROWS_USER_POSITION_CONDITION.replaceAll(":docId",documentId).replaceAll(":nodeId", nodeId.toString());
            }
        } else {
            if(isAdmin()) {
                filterByUser = GET_ROWS_USER_POSITION_ACTIVE 
                        + "AND ( " + GET_READERS_TO_ADD_BY_POSITION.replaceAll(":nodeId", nodeId.toString()) + " )";
            } else {
                filterByUser = GET_ROWS_USER_POSITION_ACCESS_CONTROL 
                        + "AND ( " + GET_READERS_TO_ADD_BY_POSITION.replaceAll(":nodeId", nodeId.toString()) + " )";
            }
        } 
        filter.getCriteria().put("<condition>", filterByUser
                .replaceAll(":userId", getLoggedUserId().toString())
                .replaceAll(":servicesMine", "1")
                .replaceAll(":servicesTheirs", "1"));
        return dao.HQL_getRows(""
            + " SELECT new map("
                + " c.id as id,"
                + " c.userCode as userCode,"
                + " c.userDescription as userDescription,"
                + " c.positionDescription as positionDescription,"
                + " b.description as businessUnitName"
            + " )"
            + " FROM "
                + UserPosition.class.getCanonicalName() + " c "
                + "," + BusinessUnit.class.getCanonicalName() + " b "
            + " WHERE "
                + " c.businessUnitId = b.id ", filter)
        ;
    }
    
    @Override
    @SMDMethod
    public Map<String, Object> load(Long userId) {
        getLogger().trace("DPMS.Acceso.CRUD_User @ load: [userId={}]", userId);
        String query = ""
                + " SELECT new map( "
                    + " c.description AS description "
                    + ",c.cuenta as cuenta "
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c "
                + " WHERE c.id = :userId";
        final IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleMap(query, ImmutableMap.of("userId", userId));
    }
    
    @SMDMethod
    public Map<String, Object> loadEdition(Long userId) {
        getLogger().trace("DPMS.Acceso.CRUD_User @ load: [userId={}]", userId);
        String query = ""
                + " SELECT new map( "
                    + " c.id AS id "
                    + ", c.description AS description "
                    + ",c.cuenta as cuenta "
                    + ",c.hashedPassword as hashedPassword "
                    + ",c.code as code "
                    + ",c.isAway as isAway "
                    + ",c.isAwaySince as isAwaySince "
                    + ",c.isAwayReason as isAwayReason "
                    + ",c.correo as correo "
                    + ",c.status as status "
                    + ",c.businessUnitDepartmentId as businessUnitDepartmentId "
                    + ",c.bossId as bossId "
                    + ",c.lang as lang "
                    + ",c.locale as locale "
                    + ",c.defaultWorkflowPositionId as defaultWorkflowPositionId "
                    + ",c.accessEditProfile as accessEditProfile "
                    + ",c.isAnonymous as isAnonymous "
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " WHERE c.id = :userId";
        final IUntypedDAO dao = getUntypedDAO();
        final Map<String, Object> record = dao.HQL_findSimpleMap(query, ImmutableMap.of("userId", userId));
        if (record.get("hashedPassword") != null) {
            record.put("hashedPassword", "hashedPassword");
        }
        return record;
    }
    
    /**
     * linkedGridId vale "linkedGridId" (que declaramos en JS) y "groundId" vale
     * dom.byId('id').value (lo que le pasamos al constructor de LinkedGrid de
     * JS).
     *
     * El resto de los datos a configurar son los que vienen en el constructor
     * de LinkedGridConfig
     *
     * @param linkedGridId LinkedGridId vale "linkedGridId" (que declaramos en
     * JS)
     * @param groundId Vale dom.byId('id').value (lo que le pasamos al
     * constructor de LinkedGrid de JS).
     * @return LinkedGridConfig
     */
    @Override
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        getLogger().trace("linkedGridId: {}, groundId: {}", linkedGridId, groundId);
        LinkedGridType linkedGridEnum = LinkedGridType.valueOf(CaseFormat.LOWER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, linkedGridId));
        switch (linkedGridEnum) {
            case POSITIONS:
                return new LinkedGridConfig(
                        Position.STATUS.ACTIVE,
                        IPositionDAO.class, //<--- Es el DAO que regresa los valores del grid flotante
                        new CustomLinkedSelect(
                                LinkedGridConfig.columns(
                                    "c.id AS id",
                                    "c.code AS code", 
                                    "c.description AS description",
                                    "c.licenseCode AS licenseCode",
                                    "bu.description AS businessUnit", 
                                    "c.perfilId AS profid",
                                    "prf.description AS profdescription"
                                ),
                                LinkedGridConfig.joins(
                                        "LEFT JOIN c.une bu",
                                        "LEFT JOIN c.perfil prf"
                                )
                        ),
                        " u.id = " + groundId, //<--- Es la condición para hacer "match" del ID actual con sus "muchos"
                        User.class.getCanonicalName() + " u JOIN u.puestos"
                );
            case USER_TEAMS:
                return new LinkedGridConfig(""
                        + " c.deleted = 0"
                        + " AND c.status = " + Owner.STATUS.ACTIVE.getValue()
                        + " AND c.type IN ( " + Owner.TYPE.TEAM_BY_GROUP.getValue() + "," + Owner.TYPE.GLOBAL_TEAM.getValue() + ")",
                        IUntypedDAO.class, //<--- Es el DAO que regresa los valores del grid flotante,
                        OwnerTeam.class,
                        new CustomLinkedSelect(
                                LinkedGridConfig.columns(
                                    "c.id AS id",
                                    "c.code AS code", 
                                    "c.type AS type", 
                                    "c.description AS description",
                                    "MAX(g.id) as groupId",
                                    "MAX(g.description) as groupDescription"
                                ),
                                LinkedGridConfig.joins(
                                    "LEFT JOIN c.groups g",
                                    "LEFT JOIN c.users u"
                                ),
                                LinkedGridConfig.groups(
                                    "c.id",
                                    "c.code",
                                    "c.type",
                                    "c.description"
                                ),
                                "c.description"
                        ),
                        //<--- Es la condición para hacer "match" del ID actual con sus "muchos"
                         "c.deleted = 0"
                        + " AND c.status = " + Owner.STATUS.ACTIVE.getValue()
                        + " AND (("
                            + " c.type = " + Owner.TYPE.TEAM_BY_GROUP.getValue()
                            + " AND u.type = " + OwnerTeamUser.TYPE.BY_GROUP.getValue() 
                            + " AND g.id = u.groupId"
                        + " ) OR ("
                            + " c.type = " + Owner.TYPE.GLOBAL_TEAM.getValue()
                            + " AND u.type = " + OwnerTeamUser.TYPE.GLOBAL.getValue() 
                        + " ))"
                        +  "AND u.userId = " + groundId, 
                        null
                        
                );
            case USERS_GRID:
                return new LinkedGridConfig(
                        User.STATUS.ACTIVE,
                        IUserDAO.class, //<--- Es el DAO que regresa los valores del grid flotante
                        new CustomLinkedSelect(
                                LinkedGridConfig.columns(
                                    "c.id AS id",
                                    "c.code AS code",
                                    "c.correo AS correo",
                                    "c.description AS description",
                                    "bu.departmentDescription AS departmentDescription",
                                    "bu.businessUnit.description AS buDescription"
                                ),
                                LinkedGridConfig.joins(
                                    "LEFT JOIN c.businessUnitDepartment bu"
                                )
                        ),
                        "c.deleted = 0",
                        null
                                
                );
        }
        return super.getLinkedGridConfig(linkedGridId, groundId);
    }
    
    /**
     * Ejecución de la implementacion del metodo para inactivar la ventana de Bnext Launcher
     * 
     * @return boolean true si el update se realizó correctamente, false en caso contrario
     */
    @SMDMethod
    public boolean updateShowExternalDialog() {
        IUserDAO dao = getBean(IUserDAO.class);
        return dao.updateShowExternalDialog(getLoggedUserId());
    }
    
    @SMDMethod
    public Map getLoggedUserDetail() { 
        return getUntypedDAO().HQL_findSimpleMap(""
                + " SELECT new Map("
                    + " c.id as id,"
                    + " c.userDescription as userDescription,"
                    + " c.userCode as userCode,"
                    + " c.positionId as positionId,"
                    + " c.positionDescription as positionDescription"
                + " )"
                + " FROM " + UserPosition.class.getCanonicalName() + " c"
                + " WHERE c.userId = :userId", "userId", getLoggedUserId());
    }

    @SMDMethod
    public List<UserHasValue> getUsersInMyBusinessUnit(Long businesUnitId) {
        IUserDAO dao = getBean(IUserDAO.class);
        return dao.getFindingManagerInBusinessUnitCbo(businesUnitId);
    } 
    
    @SMDMethod
    public GridInfo getAllUsersMap(SortedPagedFilter filter) {
        IUserDAO dao = getBean(IUserDAO.class);
        return dao.getAllUsersToDefineForm(filter, SecurityUtils.getLoggedUser());
    }
    
    @SMDMethod
    public List<Map> getAllUsers() {
        IUserDAO dao = getBean(IUserDAO.class);
        return dao.getAllUsersToDefineForm(SecurityUtils.getLoggedUser());
    }
    
    @SMDMethod
    public List<DepartmentLite> getDepartmentsLite() {
        IUntypedDAO dao = Utilities.getUntypedDAO();
        return dao.HQL_findByQuery(""
            + "SELECT new map ("
                + " d.id as value, "
                + " d.description as text "
            + ") FROM " + DepartmentLite.class.getCanonicalName() + " d "
            + " WHERE d.status = :status AND "
                + "d.deleted = :deleted ", 
            ImmutableMap.of("status", Department.ACTIVE_STATUS,
                    "deleted", Department.IS_NOT_DELETED
            ));
    }
    
    /**
     * Ejecución de la implementacion del metodo para actualizar la cuenta del usuario
     * 
     * @param entity cuenta del usuario que será actualizado
     * @return GenericSaveHandle con lo datos de la operación
     */
    @SMDMethod
    public GenericSaveHandle updateUserInfo(User entity) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (!getLoggedUserId().equals(entity.getId())) {
            gsh.setErrorMessage("no-access");
            return gsh;
        }
        final IUserDAO dao = getBean(IUserDAO.class);
        final User user = dao.HQLT_findById(entity.getId());
        final boolean isAway = Boolean.TRUE.equals(entity.getIsAway());
        final boolean shouldRefreshAway = !Boolean.valueOf(isAway).equals(Boolean.TRUE.equals(user.getIsAway())) || 
                (entity.getChangeAwayReplacement() != null && entity.getChangeAwayReplacement() == 1);
        List<FormApproverAnalystByDepartment> departmentRegistries = Utilities.EMPTY_LIST;
        user.setDescription(entity.getDescription());
        user.setCorreo(entity.getCorreo());
        user.setLang(entity.getLang());
        user.setLocale(entity.getLocale());
        user.setGridSize(entity.getGridSize());

        user.setDetailGridSize(entity.getDetailGridSize());
        user.setFloatingGridSize(entity.getFloatingGridSize());
        user.setSearchInSubfolders(entity.getSearchInSubfolders());
        user.setShowExternalDialog(entity.getShowExternalDialog());
        user.setShowWelcomeDialog(entity.getShowWelcomeDialog());
        user.setLegacyOpenFilters(entity.getLegacyOpenFilters());
        user.setTimezone(entity.getTimezone());
        if (shouldRefreshAway) {
            if (isAway) {
                user.setIsAway(true);
                user.setIsAwayReason(entity.getIsAwayReason());
                user.setIsAwaySince(Utilities.getNow());
                // Coloca reemplazos de pendientes de autorización de formularios
                this.setApproverReplacements(entity.getAnalystPerBusinessUnitDepartment());
                // Respalda analistas auxiliares seleccionados
                departmentRegistries = entity.getAnalystPerBusinessUnitDepartment().stream().map((analyst) -> new FormApproverAnalystByDepartment(analyst.getAnalystUserId())).collect(Collectors.toList());
            } else {
                user.setIsAway(false);
                user.setIsAwayReason(null);
                user.setIsAwaySince(null);
                // Libera pendientes de autorización de formularios
                dao.notAwayAnymore(getLoggedUserId());
                // Respalda analistas auxiliares actuales
                departmentRegistries = UserUtil.getFormApproverSubstitutes(user.getId());
            }
        }
        if(dao.makePersistent(user) != null){
            if (shouldRefreshAway) {
                // Se refrescan los pendientes de los usuarios involucrados
                dao.refreshFormApproversPendings(user.getId(), departmentRegistries);
                // Se caduca la sesión para quitar el banner
                final UserLogin loginInfo = new UserLogin(user.getId());
                loginInfo.removeSession(true);
            }
            gsh.setOperationEstatus(1);
        }
        return gsh;
    }
        
    private void setApproverReplacements(List<FormApproverAnalystSelection> analystPerBusinessUnitDepartment) {
        IBusinessUnitDepartmentLoadDAO dao = getBean(IBusinessUnitDepartmentLoadDAO.class);
        dao.modifyApproverReplacements(getLoggedUserId(), analystPerBusinessUnitDepartment);
    }
        
    @SMDMethod 
    public Map<String, Object> dataSource(Long userId) {
        List businessUnitDepartments;
        IUntypedDAO dao = getUntypedDAO();
        businessUnitDepartments = dao.HQL_findByQuery(""
            + " SELECT new " + TextHasValue.class.getCanonicalName() + "(bud.description, bud.id)"
            + " FROM " + BusinessUnitDepartmentLoad.class.getCanonicalName() + " bud "
            + " WHERE"
                + " bud.status = 1 " + ( userId == null || userId.intValue() <= 0 ? "" : " "
                + " OR bud.id = ("
                    + " SELECT u.businessUnitDepartmentId"
                    + " FROM " + User.class.getCanonicalName() + " u "
                    + " WHERE u.id = " + userId
                + " ) "
            )
            + " GROUP BY bud.description, bud.id"
        );
        return ImmutableMap.of(
            "businessUnitDepartments", businessUnitDepartments
        );
    }  
    
    @SMDMethod 
    public UserEditionInfoDTO getUserEditionInfoByAccount(final String account) {
        final IUserDAO dao = getBean(IUserDAO.class);
        return dao.getUserEditionInfoByAccount(account, getLoggedUserDto());
    }

    @SMDMethod 
    public UserEditionInfoDTO getUserEditionInfoByCode(final String code) {
        final IUserDAO dao = getBean(IUserDAO.class);
        return dao.getUserEditionInfoByCode(code, getLoggedUserDto());
    }
    
    @SMDMethod
    public UserPendingCountDTO getUserPendingsCount(final Long userId) {
        getLogger().trace("getUserPendingsCount userId: {}", userId);
        if (!isAdmin() && !getLoggedUserServices().contains(ProfileServices.ADMON_ACCESOS)) {
            getLogger().error("Access denied to getUserPendingsCount userId: {}", userId);
            return new UserPendingCountDTO();
        }
        if (userId == null || userId == -1L) {
            return new UserPendingCountDTO();
        }
        final UserPendingCountDTO count = new UserPendingCountDTO();
        final IRequestDAO reqDao = getBean(IRequestDAO.class);
        final Integer countFillForms = reqDao.getActiveFillFormsByAuthorCount(userId);
        count.setFillForms(countFillForms);
        final IReceiptAcknowledgmentDAO ackDao = getBean(IReceiptAcknowledgmentDAO.class);
        final Long countDeliverCopies = ackDao.getCopiesToDeliverCountByUser(userId);
        count.setDeliverCopies(countDeliverCopies);
        return count;
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsUsersLockeds(SortedPagedFilter filter) {
        if (!isAdmin() 
            && (
                !getLoggedUserServices().contains(mx.bnext.access.ProfileServices.ADMON_ACCESOS) 
                || !isEscalationManager()
            )
        ) {
            return Utilities.EMPTY_GRID_INFO;
        }
        String userLocked = "c.deleted = 0 AND c.status = " + User.STATUS.LOCKED.getValue();
        filter.getCriteria().put("<condition>", userLocked);
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.HQL_getRows(""
                + " SELECT new map("
                    + " c.id AS id"
                    + ", c.code AS code"
                    + ", c.description AS description"
                    + ", c.status AS status"
                    + ", c.avatarId AS avatarId"
                    + ", c.cuenta AS cuenta"
                    + ", c.correo AS correo"
                    + ", c.licenseCode AS licenseCode"
                    + ", c.createdDate AS createdDate"
                    + ", boss.description AS boss_description"
                    + ", u.description as createdByDescription"
                + " )"
                + " FROM " + User.class.getCanonicalName() + " c"
                + " LEFT JOIN c.boss boss"
                + " LEFT JOIN c.businessUnitDepartment businessUnitDepartment"
                + " LEFT JOIN businessUnitDepartment.businessUnit businessUnit"
                + " LEFT JOIN " + User.class.getCanonicalName() + " u ON u.id = c.createdBy ",
                filter
        );
    }
    
    @SMDMethod
    public GridInfo<Map<String, Object>> getRowsInactiveUsers(SortedPagedFilter filter) {
        return getUserQuery( User.STATUS.INACTIVE.getValue(), filter, false);
    }
    
    @SMDMethod
    public boolean updateAvatarProfile(Long avatarId) {
        final Map<String, Object> params = new HashMap<>();
        final ILoggedUser loggedUser = getLoggedUserDto();
        params.put("avatarId", avatarId);
        return UserUtil.updateUser(params, loggedUser.getId(), loggedUser.getId(), null);
    }

    @SMDMethod
    public boolean removeAvatarProfile() {
        final ILoggedUser loggedUser = getLoggedUserDto();
        final Map<String, Object> params = new HashMap<>();
        params.put("avatarId", null);
        return UserUtil.updateUser(params, loggedUser.getId(), loggedUser.getId(), null);
    }

    @SMDMethod
    public boolean deleteUser(Long userId) {
        IUserDAO usrDAO = getBean(IUserDAO.class);
        return usrDAO.deleteUser(userId, SecurityUtils.getLoggedUserId());
    }
    
}
