package DPMS.Device;

import DPMS.DAO.HibernateDAO_Device;
import DPMS.DAO.HibernateDAO_Document;
import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IDeviceDAO;
import DPMS.DAOInterface.IDocumentDAO;
import DPMS.DAOInterface.IFilesDAO;
import DPMS.DAOInterface.IRequestDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.Device;
import DPMS.Mapping.DeviceLite;
import DPMS.Mapping.DisposedDevice;
import DPMS.Mapping.DocumentSimple;
import DPMS.Mapping.Files;
import DPMS.Mapping.User;
import Framework.Config.ITextHasValue;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.IPendingQueries;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.device.pending.imp.ToChange;
import qms.device.pending.imp.ToSchedule;

public class CRUD_Device extends CRUD_Generic<Device> {
    
    /**
     * Metodo para cambiar el estado de un equipo
     * 
     * @param id id del equipo del cual se cambiara el estado
     * @param status estado del equipo al cuál será cambiado el equipo
     * @return GenericSaveHandle con los datos de la operación
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @SMDMethod
    public GenericSaveHandle toggleStatus(Long id, Integer status) {
        getLogger().trace("DPMS.Device.CRUD_Device.toggleStatus");
        try {
            IDeviceDAO daoDevice = Utilities.getBean(IDeviceDAO.class);
            GenericSaveHandle gsh = new GenericSaveHandle();
            Device device = daoDevice.toggleStatus(new Device(id), status, getLoggedUserDto());
            if (device != null) {
                gsh.setOperationEstatus(1);
                gsh.setSuccessMessage("{\"tipo\":\"dispose_success\"}");
            } else {
                gsh.setOperationEstatus(0);
            }
            return gsh;
        } catch (Exception ex) {
            GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setOperationEstatus(0);
            getLogger().error("DPMS.Device.CRUD_Device.toggleStatus() ... {}", new Object[]{id, status}, ex);
            return gsh;
        }
    }

    /**
     * Recibe la petición de guardar y manda el correo correspondiente a cada caso
     *
     * @param entity la entidad a guardar
     * @return el resultado de la funcioón de guardado en forma de GenericSaveHandle
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @SMDMethod
    public GenericSaveHandle save(final Device entity) {
        Device ent = entity; 
        if (!isAdmin()
                && !getLoggedUserServices().contains(ProfileServices.DEVICE_EDITOR)
                && !getLoggedUserServices().contains(ProfileServices.DEVICE_MANAGER)
                && !getLoggedUserServices().contains(ProfileServices.DEVICE_CREATOR)) {
            GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setErrorMessage(NO_ACCESS);
            return gsh;
        }
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Device.CRUD_Device @ save:" + Utilities.getSerializedObj(ent));
        }        
        return update(ent);
    }

    /**
     * Ejecucion de la implementacion del metodo para guardar/actualizar equipos
     *
     * @param ent equipo que se va a guardar o actualizar
     * @return GenericSaveHandle con los resultados de la operacion
     * <AUTHOR> Cavazos Galindo
     * @since *********
     * @see HibernateDAO_Device#update(DPMS.Mapping.Device) 
     */
    public GenericSaveHandle update(final Device ent) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Device.CRUD_Device @ update:" + Utilities.getSerializedObj(ent));
        }
        final IDeviceDAO device = getBean(IDeviceDAO.class);
        final boolean nuevo = (ent.getId() == -1);
        if (nuevo) {
            return device.create(ent, getLoggedUserDto());
        }
        return device.update(ent, getLoggedUserDto());
    }

    /**
     * Metodo para desechar un equipo
     * 
     * @param ent equipo que será desechado
     * @return GenericSaveHandle con los datos de la operación
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @SMDMethod
    public GenericSaveHandle dispose(final DisposedDevice ent) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Device.CRUD_Device @ dispose: " + Utilities.getSerializedObj(ent));
        }
        final IDeviceDAO dao = getBean(IDeviceDAO.class);
        final GenericSaveHandle gsh = new GenericSaveHandle();
        final Device device = dao.dispose(new Device(), ent, getLoggedUserDto());
        if (device != null) {
            gsh.setOperationEstatus(1);
        } else {
            gsh.setOperationEstatus(0);
        }
        return gsh;
    }

    @SMDMethod
    public GridInfo<DocumentSimple> getDocuments(SortedPagedFilter filter) {
        getLogger().trace("DPMS.Device.CRUD_Device.getDocuments");
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT document.id "
                + "FROM DPMS.Mapping.Device d "
                + "JOIN d.documents document "
                + "WHERE d.id = " + firstParamCurrentEntityId()
                + ")");
        
        IDocumentDAO daoDocument = (IDocumentDAO) Utilities.getBean(HibernateDAO_Document.class.getSimpleName());
        return daoDocument.getRows(filter);
    }

    @SMDMethod
    public GridInfo<Files> getFiles(SortedPagedFilter filter) {
        getLogger().trace("DPMS.Device.CRUD_Device.getFiles");
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT files.id "
                + "FROM " + Device.class.getCanonicalName() + " d "
                + "JOIN d.files files "
                + "WHERE d.id = " + firstParamCurrentEntityId()
                + ")");        
        IFilesDAO daoDocument = Utilities.getBean(IFilesDAO.class);
        return daoDocument.getRows(filter);
    }

    

    /**
    * Obtiene la lista de equipos por reemplazar (donde la fecha de reemplazo sea menor o igual a hoy)
     *
     * @return la lista de equipos por reemplazar
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    public List<DeviceLite> getExpiredDevices() {
        getLogger().trace("DPMS.Device.CRUD_Device.getExpiredDevices()");
        Map params = new HashMap();
        params.put("changeDate", new Date());
        IDeviceDAO daoDevice = Utilities.getBean(IDeviceDAO.class);
        return daoDevice.HQL_findByQuery("SELECT c FROM " + DeviceLite.class.getCanonicalName() + " c WHERE c.changeDate <= :changeDate AND c.deleted = 0", params);
    }

    /**
     * Obtiene la lista de equipos por reemplazar (donde la fecha de reemplazo sea menor o igual a hoy) formateada para
     * grid
     *
     * @param filter
     * @return la lista de equipos por reemplazar
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @SMDMethod
    public GridInfo<Device> getRowsToChange(SortedPagedFilter filter) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Device.CRUD_Device.getRowsToChange() "+filter);        
        }
        IRequestDAO dao = getBean(IRequestDAO.class);
        IPendingQueries ops = new ToChange(dao);
        filter.getCriteria().put("<condition>", ops.filterRecordsByUser(getLoggedUserId(), "c"));
        return super.getRows(filter, dao);
    }

    /**
     * Obtiene la lista de equipos por programar servicios formateada para grid
     *
     * @param filter
     * @return la lista de equipos por programar servicios
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @SMDMethod
    public GridInfo<Device> getRowsToSchedule(SortedPagedFilter filter) {
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Device.CRUD_Device.getRowsToSchedulec() "+filter);        
        }
        IRequestDAO dao = getBean(IRequestDAO.class);
        IPendingQueries ops = new ToSchedule(dao);
        filter.getCriteria().put("<condition>", ops.filterRecordsByUser(getLoggedUserId(), "c"));
        return super.getRows(filter, dao);
    }

    @SMDMethod
    public List<ITextHasValue> getDepartmentByUserBuAndDevice(Long id) {
        getLogger().trace("DPMS.Device.CRUD_Device.getDepartmentByUserBuAndDevice() "+id);       
        IUserDAO daoUser = getBean(IUserDAO.class);
        List<Long> bunList = daoUser.getBusinessUnits(getLoggedUserId(), isAdmin(), isCorporative());
        String businessUnits = StringUtils.join(bunList, ",");
        IBusinessUnitDepartmentLoadDAO DAO = getBean(IBusinessUnitDepartmentLoadDAO.class);
        if (isCorporative()) {
            String filter = ""
                + " c.businessUnitId IN (" + Utilities.safeIN(businessUnits) + ")"
                + " OR c.id = " + id.toString();
            return DAO.getStrutsComboListWithBusinessUnit(filter);
        } else {
            return DAO.getDepartmentByUserAndBusinessUnit(getLoggedUserId().toString(), businessUnits, id.toString(), isAdmin());
        }
        
    }

    @SMDMethod
    @Override
    public GridInfo getRows(SortedPagedFilter filter) {
        return super.getRows(filter);
    }
   
    @SMDMethod
    public Map<String, Object> loadEdit(Long id) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleMap(""
                + " SELECT new Map("
                + " c.id AS id,"
                + " c.status AS status,"
                + " c.deleted AS deleted,"
                + " c.description AS description,"
                + " c.code AS code,"
                + " c.modelBrand AS modelBrand,"
                + " c.serial AS serial,"
                + " c.departmentId AS departmentId,"
                + " c.areaId AS areaId,"
                + " c.localization AS localization,"
                + " c.operationRange AS operationRange,"
                + " c.precision AS precision,"
                + " c.treshold AS treshold,"
                + " c.uncertainty AS uncertainty,"
                + " c.calibrationMethod AS calibrationMethod,"
                + " c.calibrationMethodText AS calibrationMethodText,"
                + " c.useDate AS useDate,"
                + " c.purchaseDate AS purchaseDate,"
                + " c.priorityId AS priorityId,"
                + " c.classificationId AS classificationId,"
                + " c.responsibleId AS responsibleId,"
                + " c.ownerId AS ownerId,"
                + " c.deviceGroupId AS deviceGroupId,"
                + " c.deviceTypeId AS deviceTypeId,"
                + " c.changeDate AS changeDate"
                + " )"
                + " FROM " + Device.class.getCanonicalName() + " c"
                + " WHERE c.id = " + id);
    }
    
    @SMDMethod
    public Map<String, Object> loadInfo(Long id) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleMap(""
                + " SELECT new Map(c.id as id, c.description as description, c.code as code, c.attendant as attendant)"
                + " FROM " + Device.class.getCanonicalName() + " c"
                + " WHERE c.id = " + id);
    }
    
    @SMDMethod
    public Map loadPending(Long deviceId) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleMap(""
                + " SELECT new map("
                    + " c.code as code,"
                    + " c.description as description,"
                    + " c.changeDate as changeDate,"
                    + " c.modelBrand as modelBrand,"
                    + " c.serial as serial,"
                    + " deviceGroup.description as deviceGroup,"
                    + " deviceType.description as deviceType,"
                    + " department.description as department,"
                    + " area.description as area,"
                    + " attendant.description as attendant,"
                    + " owner.description as owner,"
                    + " classification.description as classification"
                + ")"
                + " FROM " + Device.class.getCanonicalName() + " c"
                + " LEFT JOIN c.deviceGroup AS deviceGroup"
                + " LEFT JOIN c.deviceType AS deviceType"
                + " LEFT JOIN c.department AS department"
                + " LEFT JOIN c.area AS area"
                + " LEFT JOIN c.attendant AS attendant"
                + " LEFT JOIN c.owner AS owner"
                + " LEFT JOIN c.classification AS classification"
                + " WHERE c.id = " + deviceId);
    }
    
    @SMDMethod
    public Map loadForScheduler(Long id) {
        IUntypedDAO dao = getUntypedDAO();
        Map result = dao.HQL_findSimpleMap(""
                + " SELECT new map("
                    + " c.id as id,"
                    + " c.code as code,"
                    + " c.description as description,"
                    + " c.attendant as attendant,"
                    + " c.department.businessUnitId as bud,"
                    + " 1 AS availableUserToApprove"
                + ")"
                + " FROM " + Device.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", id);
        if (Utilities.getSettings().getSchedulingToApprove().equals(1)) {
            Long bud = (Long) result.get("bud");
            List users = dao.HQL_findByQuery(""
                + " SELECT usr.id FROM " + User.class.getCanonicalName() + " usr "
                + " JOIN usr.puestos pos "
                + " JOIN pos.perfil perf "
                + " JOIN pos.une une "
                + " WHERE perf." + ProfileServices.DEVICE_MANAGER.getCode() + " = 1 "
                + " AND une.id = :bud", "bud", bud);
            if(users.isEmpty()){
                result.put("availableUserToApprove", 0);
            }
        } 
        return result;
    }
    
}