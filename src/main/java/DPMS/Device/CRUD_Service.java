package DPMS.Device;

import DPMS.DAOInterface.IServiceApprovalDAO;
import DPMS.DAOInterface.IServiceDAO;
import DPMS.DAOInterface.IUserDAO;
import DPMS.Mapping.FilesLite;
import DPMS.Mapping.Measurement;
import DPMS.Mapping.Service;
import DPMS.Mapping.ServiceApproval;
import DPMS.Mapping.ServiceMetric;
import Framework.Config.ITextHasValue;
import Framework.Config.SortedPagedFilter;
import Framework.Config.TextHasValue;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.IPendingQueries;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.device.pending.imp.ToApproveService;
import qms.framework.util.ExceptionUtils;

public class CRUD_Service extends CRUD_Generic<Service> {

    private static final String validEntitiesFilter = ""
            + "exists ("
            + "SELECT dept.id FROM "
            + "DPMS.Mapping.User as u "
            + "JOIN u.puestos as p "
            + "JOIN p.departamentos as dept "
            + "JOIN p.perfil as perfil "
            + "WHERE dept.businessUnitId = c.department.businessUnitId "
            + "AND u.id = :userId AND 1 IN (:servicios)"
            + ")";

    private static final String serviceToApprove = ""
            + " c.department.businessUnitId IN(:businessUnitId) "
            + (
                Utilities.getSettings().getServiceToApprove().equals(1) 
            ? ""
            + "AND c.id IN ("
            + "    SELECT ssa.serviceId FROM DPMS.Mapping.ServiceApproval ssa "
            + "    WHERE ssa.approvedBy IS NULL"
            + "    AND ssa.approvedOn IS NULL"
            + "    AND ssa.status = " + ServiceApproval.NEWLY_CREATED
            + ")"
                : ""
            )
            + "AND c.status IN (" + ServiceMetric.ON_TIME + "," + ServiceMetric.ON_RANGE + "," + ServiceMetric.OUT_OF_TIME + ")";

    /**
     * Metodo para guardar/actualizar un servicio
     *
     * @param entity servicio a guardar/actualizar
     * @return GenericSaveHandle con los datos de la operación
     */
    @SMDMethod
    public GenericSaveHandle save(final Service entity) {
        Service ent = entity;
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("save:{}", Utilities.getSerializedObj(ent));
        }
        boolean nuevo = (ent.getId() == -1);
        IServiceDAO dao = getBean(IServiceDAO.class);
        GenericSaveHandle gsh = new GenericSaveHandle();
        if (nuevo) {
            try {
                if (Utilities.getSettings().getServiceToApprove().equals(1)) {
                    return dao.requestServiceApproval(ent, getLoggedUserDto());                  
                } else {
                    return dao.realizeService(entity, getLoggedUserDto());
                }
            } catch (final Exception ex) {
                final Throwable cause = ExceptionUtils.getRootCause(ex);
                getLogger().error("Failed to save service {}.", entity, cause);
                gsh.setErrorMessage(ExceptionUtils.getRootCauseMessage(ex) + " Failed to save service");
                return gsh;
            }
        }
        gsh.setOperationEstatus(0);
        gsh.setErrorMessage("UNSUPORTED_OPERATION");
        return gsh;
    }

    @SMDMethod
    @Override
    public GridInfo<ServiceMetric> getRows(SortedPagedFilter filter) {
        String id = firstParamCurrentEntityId();
        String deviceFilter, deviceFilterAnd;
        Boolean hasDeviceId = id != null && !id.isEmpty() && !"-1".equals(id);
        if (hasDeviceId) {
            deviceFilter = " c.deviceId = " + id;
            deviceFilterAnd = " AND" + deviceFilter;
        } else {
            deviceFilter = "";
            deviceFilterAnd = "";
        }
        getLogger().info("DPMS.Device.CRUD_Service @ getRowsToChange");
        IUntypedDAO dao = getUntypedDAO();
        Long count = 0L;
        Long exact = 0L;
        Long valid = 0L;
        Long past = 0L;
        List<ServiceMetric> listResult = new ArrayList<>();
        List<ITextHasValue> consolidates = new ArrayList<>();
        GridInfo<ServiceMetric> result = new GridInfo<>();

        ProfileServices[] servicio = {ProfileServices.ALL};

        filter.getCriteria().put("<filtered-entity>", isAdmin() ? ""
                : validEntitiesFilter.replace(":userId", getLoggedUserId().toString())
                        .replace(":servicios", ProfileServices.getCodedServices("perfil", servicio)));

        HashMap<String, String> original = new HashMap<>();
        HashMap<String, String> originalLike = new HashMap<>();
        HashMap lowerLimit = new HashMap<>();
        HashMap upperLimit = new HashMap<>();
        Object criteria = filter.getCriteria().clone();
        Object like = filter.getLikeCriteria().clone();
        Object upperL = filter.getUpperLimit().clone();
        Object lowerL = filter.getLowerLimit().clone();
        original = (HashMap<String, String>) criteria;
        originalLike = (HashMap<String, String>) like;
        upperLimit = (HashMap) upperL;
        lowerLimit = (HashMap) lowerL;
        SortedPagedFilter consolidatesFilter = new SortedPagedFilter();
        consolidatesFilter.setCriteria(original);
        consolidatesFilter.setLikeCriteria(originalLike);
        consolidatesFilter.setUpperLimit(upperLimit);
        consolidatesFilter.setLowerLimit(lowerLimit);
        consolidatesFilter.getCriteria().put("<condition>", "c.status = 1" + deviceFilterAnd );
        consolidatesFilter.getCriteria().put("<filtered-entity>", isAdmin() ? ""
                : validEntitiesFilter.replace(":userId", getLoggedUserId().toString())
                        .replace(":servicios", ProfileServices.getCodedServices("perfil", servicio)));
        exact = getRowCount(consolidatesFilter);
        consolidates.add(0, new TextHasValue("exact", exact));

        consolidatesFilter = new SortedPagedFilter();
        consolidatesFilter.setCriteria(original);
        consolidatesFilter.setLikeCriteria(originalLike);
        consolidatesFilter.setUpperLimit(upperLimit);
        consolidatesFilter.setLowerLimit(lowerLimit);
        consolidatesFilter.getCriteria().put("<condition>", "c.status = 2" + deviceFilterAnd );
        consolidatesFilter.getCriteria().put("<filtered-entity>", isAdmin() ? ""
                : validEntitiesFilter.replace(":userId", getLoggedUserId().toString())
                        .replace(":servicios", ProfileServices.getCodedServices("perfil", servicio)));
        valid = getRowCount(consolidatesFilter);
        consolidates.add(0, new TextHasValue("valid", valid));

        consolidatesFilter = new SortedPagedFilter();
        consolidatesFilter.setCriteria(original);
        consolidatesFilter.setLikeCriteria(originalLike);
        consolidatesFilter.setUpperLimit(upperLimit);
        consolidatesFilter.setLowerLimit(lowerLimit);
        consolidatesFilter.getCriteria().put("<condition>", "c.status = 3" + deviceFilterAnd );
        consolidatesFilter.getCriteria().put("<filtered-entity>", isAdmin() ? ""
                : validEntitiesFilter.replace(":userId", getLoggedUserId().toString())
                        .replace(":servicios", ProfileServices.getCodedServices("perfil", servicio)));
        past = getRowCount(consolidatesFilter);
        consolidates.add(0, new TextHasValue("past", past));

        //Primero se obtiene el número de resultados
        if (hasDeviceId) {
            filter.getCriteria().put("<condition>", deviceFilter);
        }
        count = getRowCount(filter);
        result.setCount(count);

        if (count > 0) {
            listResult = dao.HQLT_findByPagedFilter(ServiceMetric.class, filter);
        }
        result.setData(listResult);
        result.setGridId(filter.getGridId());
        result.setConsolidates(consolidates);
        return result;
    }

    @SMDMethod
    @Override
    public Long getRowCount(SortedPagedFilter filter) {
        getLogger().info("DPMS.Device.CRUD_Service @ getRowCount");
        return getUntypedDAO().HQL_countByPagedFilter(ServiceMetric.class, filter);
    }

    @SMDMethod
    public GridInfo<Measurement> getMeasurements(SortedPagedFilter filter) {
        getLogger().info("DPMS.Device.CRUD_Service @ getMeasurements");
        IUntypedDAO daoMeasurement = getUntypedDAO();
        if (getCurrentEntityId() != null && getCurrentEntityId()[0] != null) {
            filter.getCriteria().put("<condition>", ""
                    + "c.id IN "
                    + "("
                    + "SELECT measurement.id "
                    + "FROM DPMS.Mapping.ServiceSchedule s "
                    + "JOIN s.measurements measurement "
                    + "WHERE s.id = " + firstParamCurrentEntityId()
                    + ")");
        } else {
            filter.getCriteria().put("<condition>", "0=1");
        }
        return daoMeasurement.getRows(Measurement.class, filter);
    }

    @SMDMethod
    public GridInfo<Measurement> getMeasurementsReview(SortedPagedFilter filter) {
        getLogger().info("DPMS.Device.CRUD_Service @ getMeasurementsReview");
        IUntypedDAO daoMeasurement = getUntypedDAO();
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT measurement.id "
                + "FROM DPMS.Mapping.Service s "
                + "JOIN s.measurements measurement "
                + "WHERE s.id = " + firstParamCurrentEntityId()
                + ")");
        return daoMeasurement.getRows(Measurement.class, filter);
    }

    @SMDMethod
    public GridInfo<FilesLite> getDocuments(SortedPagedFilter filter) {
        getLogger().info("DPMS.Device.CRUD_Service @ getDocuments");
        if (getCurrentEntityId() != null && getCurrentEntityId()[0] != null) {
            filter.getCriteria().put("<condition>", ""
                    + "c.id IN "
                    + "("
                    + "SELECT document.id "
                    + "FROM DPMS.Mapping.Service s "
                    + "JOIN s.documents document "
                    + "WHERE s.id = " + firstParamCurrentEntityId()
                    + ")");
        } else {
            filter.getCriteria().put("<condition>", "1=0");
        }
        return Utilities.getUntypedDAO().getRows(FilesLite.class, filter);
    }

    @SMDMethod
    @Override
    public Service load(Long id) {
        getLogger().info("DPMS.Device.CRUD_Service @ load");
        IServiceDAO dao = getBean(IServiceDAO.class);
        return dao.load(id);
    }

    @SMDMethod
    @Override
    public List load() {
        getLogger().info("DPMS.Device.CRUD_Service @ load");
        IServiceDAO dao = getBean(IServiceDAO.class);
        return dao.HQLT_findAll();
    }

    @SMDMethod
    public GridInfo<ServiceMetric> getRowsServiceToApprove(SortedPagedFilter filter) {
        IUserDAO dao = getBean(IUserDAO.class);
        IPendingQueries ops = new ToApproveService(dao);
        filter.getCriteria().put("<condition>", ops.filterRecordsByUser(getLoggedUserId(), "c"));
        return dao.getRows(ServiceMetric.class, filter);
    }


    @SMDMethod
    public GenericSaveHandle processService(Long serviceId, String comment, Boolean accept) {
        if (serviceId == null || comment == null || accept == null) {
            return new GenericSaveHandle();
        }
        IServiceApprovalDAO dao = getBean(IServiceApprovalDAO.class);
        if (accept) {
            return dao.approveService(serviceId, comment, getLoggedUserDto());
        }
        return dao.rejectService(serviceId, comment, getLoggedUserDto());
    }

    @SMDMethod
    public Map<String, Object> loadInfo(Long id) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleMap(""
                + " SELECT new Map("
                + " c.id as id, "
                + " c.code as code, "
                + " c.schedule.deviceId as deviceId, "
                + " c.schedule.serviceTypeId as serviceTypeId, "
                + " c.company as company, "
                + " c.certificateNumber as certificateNumber, "
                + " c.validFrom as validFrom, "
                + " c.registeredOn as registeredOn, "
                + " c.certificate as certificate, "
                + " c.resultId as resultId "
                + " )"
                + " FROM " + Service.class.getCanonicalName() + " c"
                + " WHERE c.id = " + id);
    }

}
