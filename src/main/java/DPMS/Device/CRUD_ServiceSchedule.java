package DPMS.Device;

import DPMS.DAOInterface.IScheduledServicesDAO;
import DPMS.DAOInterface.ISchedulingApprovalDAO;
import DPMS.DAOInterface.IServiceScheduleDAO;
import DPMS.Mapping.Device;
import DPMS.Mapping.Measurement;
import DPMS.Mapping.ScheduledServices;
import DPMS.Mapping.ServiceSchedule;
import DPMS.Mapping.User;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import ape.pending.core.IPendingQueries;
import ape.pending.core.PendingService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.device.entity.ServiceScheduleRef;
import qms.device.pending.imp.ToApproveScheduling;
import qms.device.pending.imp.ToRealize;
import qms.device.pending.imp.ToRealizePastService;

public class CRUD_ServiceSchedule extends PendingService<ScheduledServices> {
    
    /**
     *
     * @param id
     * @return
     */
    @SMDMethod
    @Override
    public GenericSaveHandle toggleStatus(Long id) {
        GenericSaveHandle gsh = new GenericSaveHandle();
        //TODO: Se puede usar el metodo desde CRUD_Generic
        try {
            IUntypedDAO dao = getUntypedDAO();
            ServiceSchedule ent = dao.HQLT_findById(ServiceSchedule.class, id);
            ent.setStatus(Objects.equals(ent.getStatus(), ServiceSchedule.ACTIVE_STATUS) ? ServiceSchedule.INACTIVE_STATUS : ServiceSchedule.ACTIVE_STATUS);
            ent = dao.makePersistent(ent, getLoggedUserId());
            if (ent == null) {
                gsh.setOperationEstatus(0);
            } else {
                gsh.setOperationEstatus(1);
            }
            return gsh;
        } catch (Exception ex) {
            getLogger().error("DPMS.Device.CRUD_ServiceSchedule.toggleStatus() ... {}", new Object[]{
                id
            }, ex);
        }
        return null;
    }

    /**
     * Metodo para guardar/actualizar programaciones de servicios
     *
     * @param entity programación de servicio a guardar/actualizar
     * @return GenericSaveHandle con los datos de la operación
     */
    @SMDMethod
    public GenericSaveHandle save(final ServiceSchedule entity) {
        final ServiceSchedule ent = entity;
        if (getLogger().isTraceEnabled()) {
            getLogger().trace("DPMS.Device.CRUD_ServiceSchedule @ save: {}", Utilities.getSerializedObj(ent));
        }
        final IServiceScheduleDAO dao = getBean(IServiceScheduleDAO.class);
        final boolean nuevo = (ent.getId() == -1);
        final GenericSaveHandle gsh;
        if (nuevo) {
            if (Utilities.getSettings().getSchedulingToApprove().equals(1)) {
                Long devideBud = dao.HQL_findSimpleLong(""
                + " SELECT c.department.businessUnitId"
                + " FROM " + Device.class.getCanonicalName() + " c"
                + " WHERE c.id = :id", "id", entity.getDeviceId());
                List users = dao.HQL_findByQuery(""
                    + " SELECT usr.id FROM " + User.class.getCanonicalName() + " usr "
                    + " JOIN usr.puestos pos "
                    + " JOIN pos.perfil perf "
                    + " JOIN pos.une une "
                    + " WHERE perf." + ProfileServices.DEVICE_MANAGER.getCode() + " = 1 "
                    + " AND une.id = :bud", "bud", devideBud);
                if (users.isEmpty()){
                    gsh = new GenericSaveHandle();
                    gsh.setOperationEstatus(0);
                    return gsh;
                }
                gsh = dao.requestScheduleApproval(ent, getLoggedUserDto());
            } else {
                gsh = dao.scheduleService(ent, getLoggedUserDto());
            }
        } else {
            gsh = dao.editSchedule(ent, getLoggedUserDto());
        }
        return gsh;
    }


    @SMDMethod
    @Override
    public GridInfo<ScheduledServices> getRows(SortedPagedFilter filter) {
        /*Se filtran equipos desechados*/
        filter.getCriteria().put("<condition>", ""
                + "c.device_deleted = " + Device.IS_NOT_DELETED);
        return super.getRows(filter, getBean(IScheduledServicesDAO.class));
    }

    @SMDMethod
    public GridInfo<Measurement> getMeasurements(SortedPagedFilter filter) {
        getLogger().info(">> getMeasurements");
        IUntypedDAO dao = getUntypedDAO();
        filter.getCriteria().put("<condition>", ""
                + "c.id IN "
                + "("
                + "SELECT measurement.id "
                + "FROM DPMS.Mapping.ServiceSchedule ss "
                + "JOIN ss.measurements measurement "
                + "WHERE ss.id = " + firstParamCurrentEntityId()
                + ")");
        return dao.getRows(Measurement.class, filter);
    }

    public List<ServiceSchedule> getExpiredServices() {
        getLogger().trace("DPMS.Device.CRUD_ServiceSchedule @ getExpiredServices");
        IServiceScheduleDAO dao = getBean(IServiceScheduleDAO.class);
        return dao.HQLT_findByQueryFilter(" DATEDIFF(day,c.nextService,getdate()) > 0 "
                + "AND c.status = 1"
                + "AND c.device.deleted = 0");
    }

    public List<ServiceSchedule> getRealizableServices() {
        getLogger().info(">> getRealizableServices");
        IServiceScheduleDAO dao = getBean(IServiceScheduleDAO.class);
        return dao.HQLT_findByQueryFilter(" DATEDIFF(day,getdate(),c.nextService) <= c.advanceDays "
                + "AND DATEDIFF(day,getdate(),c.nextService) >=0  "
                + "AND c.status = 1 "
                + "AND c.device.deleted = 0");
    }
    
       /**
     * Obtiene la lista de equipos a los cuales se necesita realizar un servicio
     * (donde la fecha del siguiente servicio sea mayor o igual a hoy y menor o igual al dia de hoy más 3 dias)
     * formateada para grid
     *
     * @param filter
     * @return la lista de equipos por reemplazar
     * @since ********
     * <AUTHOR> Germán Lares Lares
     */
    @SMDMethod
    public GridInfo getRowsToRealize(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return getActiveRecordRows(filter, dao, new ToRealize(dao));
    }
    
    @SMDMethod
    public GridInfo getRowsToRealizePastService(SortedPagedFilter filter) {
        IUntypedDAO dao = getUntypedDAO();
        return getActiveRecordRows(filter, dao, new ToRealizePastService(dao));
    }
    
    @SMDMethod
    public GridInfo<ScheduledServices> getRowsSchedulingToApprove(SortedPagedFilter filter) {
        IScheduledServicesDAO dao = getBean(IScheduledServicesDAO.class);
        IPendingQueries ops = new ToApproveScheduling(dao);
        filter.getCriteria().put("<condition>", ops.filterRecordsByUser(getLoggedUserId(), "c"));
        return dao.getRows(filter);
    }

    @SMDMethod
    public GenericSaveHandle processScheduling(Long scheduleId, String comment, Boolean accept) {
        if (scheduleId == null || comment == null || accept == null) {
            return new GenericSaveHandle();
        }
        ISchedulingApprovalDAO dao = getBean(ISchedulingApprovalDAO.class);
        ServiceScheduleRef schedule = dao.HQLT_findById(ServiceScheduleRef.class, scheduleId);
        if (accept) {
            return dao.approveSchedule(schedule, comment, getLoggedUserDto());
        }
        return dao.rejectSchedule(schedule, comment, getLoggedUserDto());
    }

    @SMDMethod
    public Map<String, Object> loadInfo(Long id) {
        IUntypedDAO dao = getUntypedDAO();
        return dao.HQL_findSimpleMap(""
                + " SELECT new Map("
                + " c.id as id, "
                + " c.code as code, "
                + " c.deviceId as deviceId, "
                + " c.serviceTypeId as serviceTypeId "
                + " )"
                + " FROM " + ServiceSchedule.class.getCanonicalName() + " c"
                + " WHERE c.id = " + id);
    }
}
