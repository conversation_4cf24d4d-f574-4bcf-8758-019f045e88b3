package DPMS.Document;

import DPMS.DAOInterface.IBusinessUnitDepartmentLoadDAO;
import DPMS.DAOInterface.IBusinessUnitLiteDAO;
import DPMS.DAOInterface.INodeAccessDAO;
import DPMS.DAOInterface.IProcessDAO;
import DPMS.DAOInterface.IUserIdNameDAO;
import DPMS.Mapping.Area;
import DPMS.Mapping.BusinessUnitDepartmentLite;
import DPMS.Mapping.BusinessUnitLite;
import DPMS.Mapping.Node;
import DPMS.Mapping.NodeAccess;
import DPMS.Mapping.NodeArea;
import DPMS.Mapping.NodeBusinessUnit;
import DPMS.Mapping.NodeBusinessUnitDepartment;
import DPMS.Mapping.NodeSimple;
import DPMS.Mapping.NodeUser;
import DPMS.Mapping.Persistable;
import DPMS.Mapping.Process;
import DPMS.Mapping.User;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IUntypedDAO;
import bnext.reference.UserRef;
import com.google.common.collect.ImmutableMap;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import mx.bnext.core.util.GridInfo;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.util.LinkedGridConfig;
import qms.util.interfaces.LinkedGridType;

public class CRUD_Node extends CRUD_Generic<NodeSimple> {

    private static final long serialVersionUID = 1L;

    /**
     * Regresa la información del nodo.
     *
     * @param nodeId id del nodo.
     * @return el nodo.
     *
     * @since ********
     * <AUTHOR> Germán Lares
     */
    @SMDMethod
    public Map<String, Object> getNodeAccessDetails(Long nodeId) {
        final IUntypedDAO dao = Utilities.getUntypedDAO();
        return dao.HQL_findSimpleMap(""
                + " SELECT new map("
                    + "c.description as description,"
                    + "c.module as module"
                + " )"
                + " FROM " + Node.class.getCanonicalName() + " c"
                + " WHERE c.id = :id",
                "id", nodeId);
    }

    /**
     * Cambia el nodo en el que se encuentra la carpeta.
     *
     * @param NodeId id de la carpeta que se va a mover.
     * @param DestinationId id del nodo al que se va a mover.
     * @return Regresa true si actualizo algún registro o false si no.
     *
     * @since ********
     * <AUTHOR> Germán Lares
     */
    @SMDMethod
    public boolean moveFolder(Long NodeId, Long DestinationId) {
        INodeAccessDAO dao = getBean(INodeAccessDAO.class);
        return dao.changeParentFolder(NodeId, DestinationId);
    }
    /**
     * Cambia el nodo en el que se encuentra la carpeta sin afectar los permisos configurados.
     *
     * @param NodeId id de la carpeta que se va a mover.
     * @param DestinationId id del nodo al que se va a mover.
     * @return Regresa true si actualizo algún registro o false si no.
     *
     * @since 2.7.5.3
     * <AUTHOR> Guadalupe Alemán Reyes
     */
    @SMDMethod
    public boolean moveFolderKeepingPermissions(Long NodeId, Long DestinationId) {
        INodeAccessDAO dao = getBean(INodeAccessDAO.class);
        return dao.changeParentFolderKeepingPermissions(NodeId, DestinationId);
    }
    
    @SMDMethod
    public Map<String, Object> saveFolder(NodeSimple ent) {
        Map<String,Object> result = new HashMap<>(1);
        getLogger().trace("DPMS.Document.CRUD_Node @ saveFolder: {}", getSerializedObj(ent));
        IUntypedDAO dao = getUntypedDAO();
        Map<String,Object> params = new HashMap<>(3);
        params.put("deleted", NodeSimple.IS_NOT_DELETED);
        params.put("code", ent.getCode());
        params.put("parent", ent.getParent());
        Object tmp = 
                dao.HQL_findSimpleObject("SELECT c.id FROM DPMS.Mapping.NodeSimple c "
                        + "WHERE c.parent = :parent AND c.deleted = :deleted AND upper(c.code) = upper(:code)", params);
        if (tmp != null){
            throw new RuntimeException("duplicate-node");
        }
        /*Se guarda la carpeta*/
        NodeSimple node = new NodeSimple(-1L);
        node.setCode(ent.getCode());
        node.setDeleted(0);
        node.setModule(ent.getModule());
        node.setPath(ent.getPath() + ent.getCode() + "\\");
        node.setDescription(ent.getCode());
        node.setParent(ent.getParent());
        node.setStatus(NodeSimple.STATUS.ACTIVE.getValue());
        node.setTopLevel(0);
        node = dao.makePersistent(node, getLoggedUserId());
        if (node == null) {
            return result;
        }
        /*Si el padre no es de nivel superior, asignar los permisos de la carpeta superior*/
        NodeSimple parent = dao.HQLT_findById(NodeSimple.class, ent.getParent());
        if (parent.getTopLevel() != 1) {
            /*Usuarios*/
            dao.SQL_updateByQuery(""
                    + " INSERT INTO tblcarpetausuario (intnodoid,intusuarioid) "
                    + " SELECT " + node.getId() + ",intusuarioid "
                    + " FROM tblcarpetausuario "
                    + " WHERE intnodoid = :nodeId",
                    ImmutableMap.of("nodeId", parent.getId().toString()),
                    0,
                    Arrays.asList("tblcarpetausuario")
            );
            /*Procesos*/
            dao.SQL_updateByQuery(""
                    + " INSERT INTO tblcarpetaarea (intnodoid,intareaid) "
                    + " SELECT " + node.getId() + ",intareaid "
                    + " FROM tblcarpetaarea "
                    + " WHERE intnodoid = :nodeId",
                    ImmutableMap.of("nodeId", parent.getId().toString()),
                    0,
                    Arrays.asList("tblcarpetaarea")
            );
            /*Departamentos*/
            dao.SQL_updateByQuery(""
                    + " INSERT INTO node_business_unit_department (node_id,business_unit_department_id) "
                    + " SELECT " + node.getId() + ",business_unit_department_id "
                    + " FROM node_business_unit_department "
                    + " WHERE node_id = :nodeId",
                    ImmutableMap.of("nodeId", parent.getId().toString()),
                    0,
                    Arrays.asList("node_business_unit_department")
            );
            /*Plantas*/
            dao.SQL_updateByQuery(""
                    + " INSERT INTO node_business_unit (node_id,business_unit_id) "
                    + " SELECT " + node.getId() + ",business_unit_id "
                    + " FROM node_business_unit "
                    + " WHERE node_id = :nodeId",
                    ImmutableMap.of("nodeId", parent.getId().toString()),
                    0,
                    Arrays.asList("node_business_unit")
            );
        } else {
            NodeUser user = new NodeUser(node.getId(), getLoggedUserId());
            user = dao.makePersistent(user, getLoggedUserId());
            if (user == null) {
                return result;
            }
        }
        result.put("node", node);
        return result;
    }

    @SMDMethod
    public GridInfo<UserRef> getUsers(SortedPagedFilter filter) {
        final String firstParam = firstParamCurrentEntityId();
        if (firstParam == null || firstParam.trim().isEmpty() || !NumberUtils.isCreatable(firstParam)) {
            getLogger().error("getUsers failed due to invalid nodeId {} provided ", firstParam);
            return Utilities.EMPTY_GRID_INFO;
        }
        final Long nodeId = Long.valueOf(firstParam);
        filter.getCriteria().put("<condition>", ""
                + "EXISTS "
                + "("
                    + " SELECT user.id "
                    + " FROM " + NodeAccess.class.getCanonicalName() + "  n "
                    + " JOIN " + NodeUser.class.getCanonicalName() + " nuser"
                    + " ON nuser.id.nodeId = n.id"
                    + " JOIN " + User.class.getCanonicalName() + " user"
                    + " ON user.id = nuser.id.userId"
                    + " WHERE n.id = " + nodeId + " "
                    + " AND user.status = " + User.STATUS.ACTIVE.getValue() + " "
                    + " AND  c.id = user.id"
                + ")");
        IUserIdNameDAO  DAO = getBean(IUserIdNameDAO.class);
        return DAO.getRows(filter);
    }

    @SMDMethod
    public GridInfo<BusinessUnitDepartmentLite> getDepartments(SortedPagedFilter filter) {
        final String firstParam = firstParamCurrentEntityId();
        if (firstParam == null || firstParam.trim().isEmpty() || !NumberUtils.isCreatable(firstParam)) {
            getLogger().error("getDepartments failed due to invalid nodeId {} provided ", firstParam);
            return Utilities.EMPTY_GRID_INFO;
        }
        final Long nodeId = Long.parseLong(firstParam);
        filter.getCriteria().put("<condition>", ""
                + "EXISTS "
                + "("
                    + "SELECT dept.id "
                    + "FROM DPMS.Mapping.NodeAccess n "
                    + "JOIN n.departments dept "
                    + "WHERE n.id = " + nodeId + " "
                    + "AND  c.id = dept.id"
                + ")");
        return getUntypedDAO().getRows(BusinessUnitDepartmentLite.class, filter);
    }

    @SMDMethod
    public GridInfo<Process> getProcesses(SortedPagedFilter filter) {
        final String firstParam = firstParamCurrentEntityId();
        if (firstParam == null || firstParam.trim().isEmpty() || !NumberUtils.isCreatable(firstParam)) {
            getLogger().error("getProcesses failed due to invalid nodeId {} provided ", firstParam);
            return Utilities.EMPTY_GRID_INFO;
        }
        final Long nodeId = Long.parseLong(firstParam);
        IProcessDAO dao = getBean(IProcessDAO.class);
        filter.getCriteria().put("<condition>", " "
                + "EXISTS "
                + "("
                    + "SELECT pro.id "
                    + "FROM DPMS.Mapping.NodeAccess n "
                    + "JOIN n.process pro "
                    + "WHERE n.id = " + nodeId + " "
                    + "AND  c.id = pro.id"
                + ")");
        return dao.getRows(filter);
    }

    @SMDMethod
    public GridInfo<BusinessUnitLite> getBusinessUnits(SortedPagedFilter filter) {
        final String firstParam = firstParamCurrentEntityId();
        if (firstParam == null || firstParam.trim().isEmpty() || !NumberUtils.isCreatable(firstParam)) {
            getLogger().error("getBusinessUnits failed due to invalid nodeId {} provided ", firstParam);
            return Utilities.EMPTY_GRID_INFO;
        }
        final Long nodeId = Long.parseLong(firstParam);
        filter.getCriteria().put("<condition>", ""
                + "EXISTS "
                + "("
                + "SELECT bun.id "
                + "FROM DPMS.Mapping.NodeAccess n "
                + "JOIN n.businessUnits bun "
                + "WHERE n.id = " + nodeId + " "
                + "AND  c.id = bun.id"
                + ")");
        IBusinessUnitLiteDAO DAO = getBean(IBusinessUnitLiteDAO.class);
        return DAO.getRows(filter);
    }

    @SMDMethod
    public GenericSaveHandle save(NodeAccess node) throws Exception {
        INodeAccessDAO dao = getBean(INodeAccessDAO.class);
        return dao.saveNodeAccess(node, getLoggedUserDto());
    }
    
    @Override
    protected boolean hasSaveAccess(Persistable backup) {
        INodeAccessDAO dao = getBean(INodeAccessDAO.class);
        return dao.hasSaveNodeAccess(backup, getLoggedUserDto());
    }

    @Override
    public LinkedGridConfig getLinkedGridConfig(String linkedGridId, Long groundId) throws Exception {
        LinkedGridType linkedGridEnum = LinkedGridType.valueOf(linkedGridId.toUpperCase());
        switch (linkedGridEnum) {
            case NODE_ACCESS_USERS:
                return new LinkedGridConfig(
                        User.STATUS.ACTIVE,
                        IUserIdNameDAO.class,
                        new String[]{"description", "code", "account", "correo"},
                        groundId,
                        NodeUser.class
                );
            case NODE_ACCESS_DEPARTMENTS:
                return new LinkedGridConfig(
                        BusinessUnitDepartmentLite.STATUS.ACTIVE,
                        IBusinessUnitDepartmentLoadDAO.class,
                        new String[]{"description", "code"},
                        groundId,
                        NodeBusinessUnitDepartment.class
                );
            case NODE_ACCESS_BUSINESS_UNIT:
                return new LinkedGridConfig(
                        BusinessUnitLite.STATUS.ACTIVE,
                        IBusinessUnitLiteDAO.class,
                        new String[]{"description", "code"},
                        groundId,
                        NodeBusinessUnit.class
                );
            case NODE_ACCESS_AREA:
                return new LinkedGridConfig(
                        Area.STATUS.ACTIVE,
                        IProcessDAO.class,
                        new String[]{"description", "code"},
                        groundId,
                        NodeArea.class
                );
        }
        return super.getLinkedGridConfig(linkedGridId, groundId);
    }
}
