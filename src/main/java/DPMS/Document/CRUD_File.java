package DPMS.Document;

import DPMS.Mapping.FilesLite;
import DPMS.Mapping.Settings;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import bnext.reference.document.FileRef;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import mx.bnext.core.file.FileUtils;
import mx.bnext.core.file.IFileData;
import org.apache.struts2.action.UploadedFilesAware;
import org.apache.struts2.dispatcher.LocalizedMessage;
import org.apache.struts2.dispatcher.multipart.MultiPartRequestWrapper;
import org.apache.struts2.dispatcher.multipart.UploadedFile;
import qms.access.dto.ILoggedUser;
import qms.framework.file.BnextSpringConfiguration;
import qms.framework.file.FileManager;

/**
 *
 * <AUTHOR>
 */
public class CRUD_File extends CRUD_Generic<FilesLite> implements UploadedFilesAware {

    private static final long serialVersionUID = 1L;
    private static final String CORRUPT_FILE = "CORRUPT_FILE";

    private List<UploadedFile> file = new ArrayList<>();

    private final List<Long> fileId = new ArrayList<>();
    private String contentType;
    private String result;
    private final List<Map<String, String>> uploadResponse = new ArrayList<>();
    private List<String> fileName;


    protected boolean filePdfPagesEnabled = true;


    public boolean getFilePdfPagesEnabled() {
        return filePdfPagesEnabled;
    }

    /**
     * @return the result
     */
    public String getResult() {
        return result;
    }

    /**
     * @param result the result to set
     */
    public void setResult(String result) {
        this.result = result;
    }


    public List<Map<String, String>> getUploadResponse() {
        return uploadResponse;
    }

    protected void addUploadResponse(int index, Map<String, String> uploadResponse) {
        this.uploadResponse.add(index, uploadResponse);
    }
    
    public String getContentType() {
        return contentType;
    }
    
    @Override
    public void withUploadedFiles(List<UploadedFile> uploadedFiles) {
        this.file = uploadedFiles;
        if (this.file != null && !this.file.isEmpty()) {
            this.contentType = this.file.stream().map(UploadedFile::getContentType).collect(Collectors.joining(","));
            this.fileName = this.file.stream().map(UploadedFile::getOriginalName).collect(Collectors.toList());
        }
    }

    @Override
    public String execute() throws IOException {
        return doExecute();
    }
    
    private Boolean validMaxSize(UploadedFile file) throws IllegalStateException {
        if (file == null) {
            return true;
        }
        final long maxSize = BnextSpringConfiguration.getUploadFileMaxSizeBytes();
        final long size = file.length();
        if (size > maxSize && maxSize > 0) {
            getLogger().error(
                    "The file {} exceeds the size limit of {} Bytes (size: {})",
                    fileName,
                    maxSize,
                    size
            );
            return false;
        }
        return true;
    }
    
    private Boolean validFilesStorageMaxSize() throws IllegalStateException {
        if (file == null) {
            return true;
        }
        boolean res = true;
        final Settings settings = Utilities.getSettings();
            Long filesStorageLimit = Utilities.limitFileStorageBytes();   
            if (filesStorageLimit != null && filesStorageLimit > 0) {
            long uploadedFilesStorage = settings.getTotalUploadedBytes();
            long sizeUploaded;
            for (final UploadedFile uploadedFile : file) {
                final long size = file.size();
                    sizeUploaded = uploadedFilesStorage + size;
                    if (sizeUploaded > filesStorageLimit) {
                    getLogger().error(
                            "The file {} can´t be uploaded limit {} Bytes exeeded (size: {})",
                            uploadedFile.getOriginalName(),
                            sizeUploaded,
                            filesStorageLimit
                    );
                        res = false;
                    }
                }
            }
        return res;
    }
    
    public String doExecute() {
        final MultiPartRequestWrapper multipart = (MultiPartRequestWrapper) getRequest();
        if (this.file == null || this.file.isEmpty() || this.file.get(0) == null) {
            if (multipart != null && multipart.hasErrors()) {                
                final GenericSaveHandle gsh = new GenericSaveHandle();
                gsh.setOperationEstatus(0);
                final LocalizedMessage firstError = multipart.getErrors().iterator().next();
                if (
                        "struts.messages.upload.error.FileSizeLimitExceededException".equals(firstError.getTextKey())
                       || "struts.messages.error.file.too.large	".equals(firstError.getTextKey())
                       || "struts.messages.upload.error.SizeLimitExceededException".equals(firstError.getTextKey())
                       || "struts.messages.upload.error.FileCountLimitExceededException".equals(firstError.getTextKey())
                ) {
                    gsh.setErrorMessage("MAX_SIZE_EXCEEDED");
                } else {
                    if (Objects.equals(multipart.getErrors().size(), 1)) {
                        gsh.setErrorMessage("Failed to upload file. Details: " + firstError.getTextKey());
                    } else {
                        gsh.setErrorMessage("Failed to upload file. Details: " + Utilities.getSerializedObj(multipart.getErrors()));
                    }
                }
                getLogger().error("Failed to upload file. Details: {}", gsh.getErrorMessage());
                addUploadResponse(0, Utilities.entityToMap(gsh));
                return ERROR;
            } else if (contentType != null && fileName != null) {
                final GenericSaveHandle gsh = new GenericSaveHandle();
                gsh.setErrorMessage(CORRUPT_FILE);
                addUploadResponse(0, Utilities.entityToMap(gsh));
                return ERROR;
            } else {
                getLogger().error("Failed to upload file");
                final GenericSaveHandle gsh = new GenericSaveHandle();
                gsh.setOperationEstatus(0);
                gsh.setErrorMessage("Failed to upload file.");
                addUploadResponse(0, Utilities.entityToMap(gsh));
                return ERROR;
            }
        } else if (!validFilesStorageMaxSize()) {
            final GenericSaveHandle gsh = new GenericSaveHandle();
            gsh.setOperationEstatus(0);
            gsh.setErrorMessage("TOTAL_SIZE_EXCEEDED");
            addUploadResponse(0, Utilities.entityToMap(gsh));
            return ERROR;
        }
        String r = "";
        String[] fileNames = this.fileName == null ? new String[this.file.size()] : this.fileName.toArray(new String[0]);
        String[] contentTypes = this.contentType == null ? new String[this.file.size()] : this.contentType.split(",");
        final FileManager fileManager = new FileManager();
        final ILoggedUser loggedUser = getLoggedUserDto();
        for (int i = 0; i < file.size(); i++) {
            final UploadedFile uploadedFile = file.get(i);
            if (!validMaxSize(uploadedFile)) {
                final GenericSaveHandle gsh = new GenericSaveHandle();
                gsh.setOperationEstatus(0);
                gsh.setErrorMessage("MAX_SIZE_EXCEEDED");
                addUploadResponse(i, Utilities.entityToMap(gsh));
                r = ERROR;
                break;
            }
            Path content = ((File) uploadedFile.getContent()).toPath();
            String contentFileName = fileNames[i] == null ? uploadedFile.getOriginalName() : fileNames[i];
            final GenericSaveHandle gsh = fileManager
                    .persistFileContent(
                        -1L,
                        content,
                        contentFileName,
                        contentTypes[i],
                        filePdfPagesEnabled,
                        loggedUser
                    )
                    .getGsh();
            addUploadResponse(i, Utilities.entityToMap(gsh));
            uploadedFile.delete();
            if (gsh.getOperationEstatus().equals(1)) {
                prepareDocumentViewer(gsh, fileManager, loggedUser);
                this.fileId.add(Long.valueOf(gsh.getSavedId()));
                r = SUCCESS;
            } else {
                r = ERROR;
                break;
            }
        }
        return r;
    }

    private void prepareDocumentViewer(final GenericSaveHandle gsh, final FileManager fileManager, final ILoggedUser loggedUser) {
        final FileRef fileData = Utilities.getUntypedDAO().HQLT_findById(FileRef.class, gsh.getPersistentId());
        final IFileData metadata = fileManager.newMetadataInstance(fileData);
        final boolean isPdf = FileUtils.CONTENT_TYPE_PDF.equals(metadata.getContentType());
        if (FileUtils.isCommonImage(metadata.getExtension()) || isPdf) {
            fileManager.prepareFileForPdfViewer(metadata, loggedUser);
        }
    }
    
    protected List<String> getFileName() {
        return fileName;
}

    protected List<Long> getFileId() {
        return fileId;
    }
    
    public List<UploadedFile> getFile() {
        return file;
    }
}
