package DPMS.Document;

import DPMS.DAOInterface.ICodeSequenceDAO;
import DPMS.DAOInterface.IGalleryDAO;
import DPMS.Mapping.CodeSequence;
import DPMS.Mapping.Gallery;
import Framework.Config.SortedPagedFilter;
import Framework.Config.Utilities;
import Framework.DAO.CRUD_Generic;
import Framework.DAO.GenericSaveHandle;
import bnext.reference.document.FileRef;
import mx.bnext.access.ProfileServices;
import mx.bnext.core.util.GridInfo;
import org.apache.struts2.json.annotations.SMDMethod;
import qms.framework.player.VideoCompatibilityHTML5;
import qms.util.QMSException;

/**
 *
 * <AUTHOR>
 */
public class CRUD_Gallery extends CRUD_Generic<Gallery> {

    @SMDMethod
    public GenericSaveHandle save(Gallery gItem) throws QMSException {
        IGalleryDAO DAO = Utilities.getBean(IGalleryDAO.class);
        gItem.setAuthorId(this.getLoggedUserId());
        if (gItem.getCode() == null || gItem.getCode().trim().isEmpty()) {
            ICodeSequenceDAO codeDAO = Utilities.getBean(ICodeSequenceDAO.class);
            String code = codeDAO.next(CodeSequence.type.CODE_GALLERY);
            gItem.setCode("IMG-" + code);
        }
        return DAO.save(gItem);
    }

    @SMDMethod
    public GridInfo getRowsVideosByNode(SortedPagedFilter filter){
        IGalleryDAO DAO = Utilities.getBean(IGalleryDAO.class);
        return DAO.HQL_getRows(""
            + " SELECT new map("
                + " c.code AS code, "
                + " c.deleted AS deleted, "
                + " c.description AS description, "
                + " c.fileId AS fileId, "
                + " c.id AS id, "
                + " c.status AS status, "
                + " f.contentType AS contentType, "
                + " f.extension AS extension "
            + " )"
            + " FROM " + Gallery.class.getCanonicalName() + " c "
            + " JOIN c.node n "
            + " JOIN " + FileRef.class.getCanonicalName() + " f "
            + " ON f.id = c.fileId"
            + " WHERE"
                + " c.deleted = 0"
                + " AND n.id = " + this.firstParamCurrentEntityId()
                // se excluyen los videos
                + " AND ("
                    + " f.contentType IN ("
                        + VideoCompatibilityHTML5.getContentTypes()
                    + " )"
                    + " OR f.contentType LIKE '%video%'"
                + " )"
            , filter
        );
    }

    @SMDMethod
    public GridInfo getRowsByNode(SortedPagedFilter filter){
        IGalleryDAO DAO = Utilities.getBean(IGalleryDAO.class);
        return DAO.HQL_getRows(""
            + " SELECT new map("
                + " c.code AS code, "
                + " c.deleted AS deleted, "
                + " c.description AS description, "
                + " c.fileId AS fileId, "
                + " c.id AS id, "
                + " c.status AS status, "
                + " f.extension AS extension, "
                + " f.contentType AS contentType "
            + " )"
            + " FROM " + Gallery.class.getCanonicalName() + " c "
            + " JOIN c.node n " 
            + " JOIN " + FileRef.class.getCanonicalName() + " f "
            + " ON f.id = c.fileId"
            + " WHERE"
                + " c.deleted = 0"
                + " AND n.id = " + this.firstParamCurrentEntityId()
                // se excluyen los videos
                + " AND ("
                    + " f.contentType NOT IN ("
                        + VideoCompatibilityHTML5.getContentTypes()
                    + " )"
                    + " AND f.contentType NOT LIKE '%video%'"
                + ")"
            , filter
        );
    }
    
    @Override
    @SMDMethod
    public Integer delete(Long id) {
        IGalleryDAO DAO = Utilities.getBean(IGalleryDAO.class);
        if (isAdmin()
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_EDITOR)
                || getLoggedUserServices().contains(ProfileServices.DOCUMENTO_ENCARGADO)){
            return DAO.delete(id);
        }
        return 0;
    }

}
