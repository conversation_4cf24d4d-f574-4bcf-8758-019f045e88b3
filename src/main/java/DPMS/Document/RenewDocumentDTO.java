package DPMS.Document;

import bnext.reference.UserRef;
import bnext.reference.document.DocumentRef;
import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class RenewDocumentDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    private UserRef user;
    private DocumentRef document;

    public RenewDocumentDTO(DocumentRef document, UserRef user) {
        this.document = document;
        this.user = user;
    }

    public UserRef getUser() {
        return user;
    }

    public void setUser(UserRef user) {
        this.user = user;
    }

    public DocumentRef getDocument() {
        return document;
    }

    public void setDocument(DocumentRef document) {
        this.document = document;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 79 * hash + Objects.hashCode(this.user);
        hash = 79 * hash + Objects.hashCode(this.document);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final RenewDocumentDTO other = (RenewDocumentDTO) obj;
        if (!Objects.equals(this.user, other.user)) {
            return false;
        }
        return Objects.equals(this.document, other.document);
    }

    @Override
    public String toString() {
        return "RenewDocumentDTO{" + "user=" + user + ", document=" + document + '}';
    }
    
}
