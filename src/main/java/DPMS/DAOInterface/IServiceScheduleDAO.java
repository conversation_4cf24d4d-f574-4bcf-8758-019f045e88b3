package DPMS.DAOInterface;

import DPMS.Mapping.ServiceSchedule;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import qms.access.dto.LoggedUser;

/**
 *
 * <AUTHOR> G<PERSON>
 */
@Implementation(name = "HibernateDAO_ServiceSchedule")
public interface IServiceScheduleDAO extends IGenericDAO<ServiceSchedule, Long> {

    public GenericSaveHandle update(ServiceSchedule ent, LoggedUser user);
    
    public GenericSaveHandle requestScheduleApproval(ServiceSchedule ent, LoggedUser user);
    
    public GenericSaveHandle scheduleService(ServiceSchedule ent, LoggedUser user);
       
    public GenericSaveHandle editSchedule(ServiceSchedule ent, LoggedUser user);

    public Long getDeviceIdByServiceScheduleId(Long serviceScheduleId);
}
