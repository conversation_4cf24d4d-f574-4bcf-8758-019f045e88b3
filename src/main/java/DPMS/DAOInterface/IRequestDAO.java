package DPMS.DAOInterface;

import DPMS.Mapping.AutorizationPoolDetailComment;
import DPMS.Mapping.FilesLite;
import DPMS.Mapping.Request;
import DPMS.Mapping.WorkflowPool;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import Framework.DAO.SaveHandle;
import bnext.reference.document.DocumentRef;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Nonnull;
import mx.bnext.core.security.ISecurityUser;
import qms.access.dto.ILoggedUser;
import qms.document.dto.PreValidateRequestDTO;
import qms.document.dto.RequestSaveHandle;
import qms.document.interfaces.IPlainRequest;
import qms.document.interfaces.IJoinableRequest;
import qms.form.dto.FieldCommentDTO;
import qms.form.dto.ReadableFieldDTO;
import qms.form.util.IOutstandingSurveysLoadAnswers;
import qms.framework.dto.DocumentDataIndex;
import qms.util.QMSException;
import qms.workflow.util.IWorkflowBaseDAO;
import qms.workflow.util.WorkflowAuthRole;

/**
 *
 * <AUTHOR> Limas
 */
@Implementation(name = "HibernateDAO_Request")
public interface IRequestDAO extends IGenericDAO<Request, Long>, IWorkflowBaseDAO {
    
    @Override
    IRequestDAO getAspectJAutoProxy();

    RequestSaveHandle save(
            Request sol,
            FilesLite file,
            Integer tipo,
            ILoggedUser loggedUser,
            boolean isAForm,
            boolean ajax
    ) throws IOException, QMSException;

    String getDocumentControlledType(Long docTypeId);

    SaveHandle verifyRequest(Request request, Long user, Boolean skipAutomaticApproval, WorkflowAuthRole authRole, ILoggedUser loggedUser) throws IOException, QMSException;

    boolean setToNotBusy(Long id);

    boolean setToBusy(Request p, boolean fill, String nombre, Long userId);

    boolean rejectAutorization(
            Request request, String originalCode, String comment, WorkflowAuthRole authRole, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    boolean rejectFillForm(
            Request request, String comment, WorkflowAuthRole authRole, ILoggedUser loggedUser
    ) throws IOException, QMSException;

    String getBusinessUnitFromRequest(Long requestId);

    Map<String, Object> loadRequestLockedInfo(Long requestId);

    String lockRequest(Long requestId, Long userId, String userDescription, @Nonnull ISecurityUser admin);
   
    boolean signTheForm(
            Long outstandingSurveyId,
            Boolean sign,
            WorkflowAuthRole authRole,
            List<FieldCommentDTO> comment,
            Long surveyFieldObjId,
            ILoggedUser loggedUser,
            Long currentAutorizationPoolIndex,
            Integer currentRecurrence,
            Request request
    );

    boolean readSigned(
            Long outstandingSurveyId,
            Long currentAutorizationPoolIndex,
            ILoggedUser loggedUser,
            List<FieldCommentDTO> listComment,
            Integer currentRecurrence
    ) throws QMSException;

    List<ReadableFieldDTO> readableFields(
            Long outstandingSurveyId,
            Long currentAutorizationPoolIndex,
            Integer currentRecurrence
    );

    List<Long> cancelableFillIndexes(Long outstandingSurveyId);
    
    GenericSaveHandle rejectFilledSection(
            Long outstandingSurveyId,
            Long invalidAutorizationPoolIndex,
            Long currentAutorizationPoolIndex,
            Long currentAutorizationPoolDetailId,
            Integer currentRecurrence,
            List<Long> readedFieldSectionsId,
            Long rejectedFieldSeccionId,
            Long rejectedFieldObjSeccionId,
            String rejectionComment,
            Map<Long, String> dataComments,
            ILoggedUser loggedUser
    ) throws QMSException;

    Boolean rejectVerification(Long requestId, Long docId, String reason, ILoggedUser loggedUser);
    
    Boolean cancelSurveyRequest(Long requestId, String razon, ILoggedUser loggedUser);

    Boolean cancelRequest(Long requestId, String razon, ILoggedUser loggedUser);

    Boolean cancelRequests(List<Long> requestIds, String razon, ILoggedUser loggedUser);
    
    Boolean destroyRequest(Long requestId, String razon);

    IOutstandingSurveysLoadAnswers startFillForm(Request sol, IOutstandingSurveysLoadAnswers outstandingSurvey, Long activityId, ILoggedUser loggedUser);

    RequestSaveHandle requestNewDocument(Request sol, FilesLite file, ILoggedUser loggedUser, boolean ajax);

    RequestSaveHandle requestDocumentModification(Request request, FilesLite file, ILoggedUser loggedUser, boolean ajax);

    RequestSaveHandle requestDocumentApproval(Request request, FilesLite file, ILoggedUser loggedUser, boolean ajax);

    RequestSaveHandle requestDocumentCancellation(Request request, FilesLite file, ILoggedUser loggedUser, boolean ajax);

    Request verifyNewManager(Request request, ILoggedUser loggedUser);

    Request verifyApproved(Request request, WorkflowAuthRole authRole, ILoggedUser loggedUser) throws IOException, QMSException;

    Request verifyApprovedPublished(Request request, ILoggedUser loggedUser) throws IOException, QMSException;

    Request publishNewDocument(Request request, Boolean readers, ILoggedUser loggedUser);

    Request publishDocumentModification(Request request, DocumentRef previousDocument, Boolean readers, ILoggedUser loggedUser) throws QMSException;

    Request publishEditDetailsDocument(Request request, DocumentRef document, ILoggedUser loggedUser);

    Request finalizeDocumentReapproval(Request request, ILoggedUser loggedUser);

    Request finalizeDocumentCancellation(Request request, String originalCode, ILoggedUser loggedUser);

    AutorizationPoolDetailComment rejectFilledSection(
            Long outstandingSurveyId,
            Long apdId,
            AutorizationPoolDetailComment comment,
            Long invalidAutorizationPoolIndex,
            Long currentAutorizationPoolIndex,
            ILoggedUser loggedUser
    );

    GenericSaveHandle rejectFilledSectionAndCreateCopy(
            Long outstandingSurveyId,
            Long invalidAutorizationPoolIndex,
            Long currentAutorizationPoolIndex,
            Long currentAutorizationPoolDetailId,
            Integer currentRecurrence,
            List<Long> readedFieldSectionsId,
            Long rejectedFieldSeccionId,
            Long rejectedFieldObjSeccionId,
            ArrayList<FieldCommentDTO> rejectionCommentList,
            ArrayList<FieldCommentDTO> acceptedCommentlist,
            ILoggedUser loggedUser
    ) throws IOException, QMSException;

    Boolean requestForm(Long requestId, ILoggedUser loggedUser);

    Request nextAuthorizeNewDocument(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails);

    Request nextAuthorizeModification(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails);

    Request nextAuthorizeReapproval(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails);

    Request nextAuthorizeCancellation(Request request, ILoggedUser loggedUser, WorkflowPool poolDetails);

    boolean isUserTheAuthor(Long requestId, Long loggedUserId);

    boolean isDocumentManager(Long requestId, Long loggedUserId, boolean admin);

    boolean hasAuthorizationPending(Long requestId, Long loggedUserId);
    
    boolean hasAuthorizationPending(List<Long> requestIds, Long loggedUserId);

    boolean hasVerificationPending(Long requestId, Long loggedUserId);
    
    boolean hasVerificationPending(List<Long> requestIds, Long loggedUserId);
    
    String generateDocumentMasterId();
    
    PreValidateRequestDTO preValidateRequest(Request request, ILoggedUser loggedUser);
    
    void cancelFillsByUserId(Long userId, String comment, ILoggedUser loggedUser);
    
    Integer getActiveFillFormsByAuthorCount(Long userId);

    boolean isRequestInStandBy(final Long requestId);
    
    Boolean hasAccessById(final Long requestId, final ILoggedUser loggedUser);
        
    GenericSaveHandle multipleReapprove(List<Long> documentIds, String reason, ILoggedUser loggedUser) throws IOException, QMSException;
       
    GenericSaveHandle authorizeMultipleRequest(List<Long> requestIds, String comments, ILoggedUser loggedUser) throws IOException, QMSException;

    GenericSaveHandle rejectMultipleAutorization(List<Long> requestIds, String comments, ILoggedUser loggedUser) throws IOException, QMSException;

    String getDocumentMasterIdByRequestId(Long requestId);

    IJoinableRequest loadRequestJoinable(Long requestId, DocumentDataIndex index);

    IPlainRequest loadPlainRequest(Long requestId);

}
