package DPMS.DAOInterface;

import DPMS.Mapping.Service;
import Framework.DAO.GenericSaveHandle;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import qms.access.dto.LoggedUser;

@Implementation(name = "HibernateDAO_Service")
public interface IServiceDAO extends IGenericDAO<Service, Long> {

    Service load(Long id);

    GenericSaveHandle requestServiceApproval(Service service, LoggedUser loggedUser);

    GenericSaveHandle realizeService(Service service, LoggedUser loggedUser);
}
