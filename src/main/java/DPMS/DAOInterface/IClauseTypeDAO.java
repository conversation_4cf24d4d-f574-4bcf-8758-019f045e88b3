package DPMS.DAOInterface;

import DPMS.Mapping.ClauseType;
import Framework.Config.ITextHasValue;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;

import java.util.List;
/**
 * <AUTHOR>
 * @since Noviembre 21, 2014
 */
@Implementation(name = "HibernateDAO_ClauseType")
public interface IClauseTypeDAO extends IGenericDAO<ClauseType, Long> {
    
    public List<ITextHasValue> getActivesAndSelected(Long auditId);
}
