package DPMS.DAOInterface;

import DPMS.Mapping.InternalDocumentCatalogItem;
import Framework.DAO.IGenericDAO;
import Framework.DAO.Implementation;
import java.util.Map;
import mx.bnext.core.util.GridInfo;

@Implementation(name = "HibernateDAO_InternalDocumentCatalogItem")
public interface IInternalDocumentCatalogItemDAO extends IGenericDAO<InternalDocumentCatalogItem, Long> {

    GridInfo<Map<String, Object>> getCatalogContents(Long internalCatalogId);
    
}
