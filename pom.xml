<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>mx.bnext</groupId>
    <artifactId>qms</artifactId>
    <version>*********</version>
    <packaging>war</packaging>
    <!-- context name -->
    <name>app</name>
    <description>Gaia Efficient</description>
    <url>http://bnext.mx/bnext-qms/</url>

    <scm>
        <connection>scm:git:http://***********/Bnext/QMS_3.0.0.git</connection>
        <url>http://***********/Bnext/QMS_3.0.0.git</url>
    </scm>

    <properties>
        <app.revisionDate>13/05/2025</app.revisionDate>
        <!-- runtime java version -->
        <java.version>1.8</java.version>
        <!-- maven java version -->
        <maven-jdk.version>11</maven-jdk.version>
        <maven.build.timestamp.format>dd/MM/yyyy</maven.build.timestamp.format>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <arial-extension.version>1.0</arial-extension.version>
        <aspectj-maven.version>1.14</aspectj-maven.version>
        <!-- Versión compatible con JDK 8 -->
        <aspectj.version>1.9.8.M1</aspectj.version>
        <biweekly.version>0.6.8</biweekly.version>
        <bnext-cipher.version>1.10.0</bnext-cipher.version>
        <bnext-core.version>0.0.89</bnext-core.version>
        <bnext-jdbc.version>1.0.5</bnext-jdbc.version>
        <cache-api.version>1.1.1</cache-api.version>
        <commons-beanutils.version>1.10.1</commons-beanutils.version>
        <commons-cli.version>1.9.0</commons-cli.version>
        <commons-codec.version>1.18.0</commons-codec.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-digester.version>2.1</commons-digester.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <commons-io.version>2.18.0</commons-io.version>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-net.version>3.11.1</commons-net.version>
        <commons-pool2.version>2.12.1</commons-pool2.version>
        <commons-text.version>1.13.0</commons-text.version>
        <dojo.version>1.17.3</dojo.version>
        <ehcache.version>3.10.8</ehcache.version>
        <encoder.version>1.3.1</encoder.version>
        <extra-enforcer-rules.version>1.9.0</extra-enforcer-rules.version>
        <frontend-maven-plugin.version>1.15.1</frontend-maven-plugin.version>
        <geronimo-stax-api.version>1.0.1</geronimo-stax-api.version>
        <git-commit-id-maven-plugin.version>9.0.1</git-commit-id-maven-plugin.version>
        <groovy.version>4.0.24</groovy.version>
        <hibernate-search.version>5.11.12.Final</hibernate-search.version>
        <hibernate.version>5.6.15.Final</hibernate.version>
        <hikari-cp.version>4.0.3</hikari-cp.version>
        <ignite-ui.version>18.1</ignite-ui.version>
        <jasperreports.version>6.21.4</jasperreports.version>
        <javassist.version>3.30.2-GA</javassist.version>
        <javax.activation.version>1.1.1</javax.activation.version>
        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>
        <javax.servlet-api.version>3.1.0</javax.servlet-api.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <jaxb-impl.version>2.3.6</jaxb-impl.version>
        <jboss-logging-annotations.version>2.2.1.Final</jboss-logging-annotations.version>
        <jboss-logging.version>3.4.3.Final</jboss-logging.version>
        <jdbc-stdext.version>2.0</jdbc-stdext.version>
        <jetty-jspc.version>9.4.57.v20241219</jetty-jspc.version>
        <jfreechart.version>1.5.5</jfreechart.version>
        <jjwt.version>0.12.6</jjwt.version>
        <joda-time.version>2.13.0</joda-time.version>
        <jodconverter-core.version>3.0-beta-4</jodconverter-core.version>
        <jsoup.version>1.18.1</jsoup.version>
        <jsqlparser.version>4.9</jsqlparser.version>
        <jstl.version>1.2</jstl.version>
        <jta.version>1.1</jta.version>
        <junit-jupiter.version>5.11.3</junit-jupiter.version>
        <jxl.version>2.6.12</jxl.version>
        <licencing-api.version>1.4.67</licencing-api.version>
        <licencing-reader.version>0.0.5</licencing-reader.version>
        <liquibase-slf4j.version>5.1.0</liquibase-slf4j.version>
        <liquibase.version>4.31.1</liquibase.version>
        <log4j.version>2.24.2</log4j.version>
        <lucene.version>5.5.5</lucene.version>
        <mariadb-java-client.version>3.5.2</mariadb-java-client.version>
        <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
        <maven-clean-plugin.version>3.4.1</maven-clean-plugin.version>
        <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
        <maven-enforcer-plugin.version>3.5.0</maven-enforcer-plugin.version>
        <maven-failsafe-plugin.version>3.5.2</maven-failsafe-plugin.version>
        <maven-install-plugin.version>3.1.3</maven-install-plugin.version>
        <maven-replacer-plugin.version>1.4.1</maven-replacer-plugin.version>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <maven-scm-plugin.version>2.1.0</maven-scm-plugin.version>
        <maven-site-plugin.version>3.21.0</maven-site-plugin.version>
        <maven-surefire-plugin.version>3.5.2</maven-surefire-plugin.version>
        <maven-war-plugin.version>3.4.0</maven-war-plugin.version>
        <maven.version>3.6.0</maven.version>
        <mockito.version>4.11.0</mockito.version>
        <modelmapper.version>3.2.1</modelmapper.version>
        <mssql-jdbc.version>12.8.1.jre8</mssql-jdbc.version>
        <net.tascalate.javaflow.api.version>2.7.6</net.tascalate.javaflow.api.version>
        <opencsv.version>5.9</opencsv.version>
        <openpdf.version>1.3.32</openpdf.version>
        <poi.version>5.3.0</poi.version>
        <postgresql.version>42.7.5</postgresql.version>
        <qms-docs.version>3.0.0.2</qms-docs.version>
        <qms-stackedit.version>1.0.1</qms-stackedit.version>
        <snakeyaml.version>2.3</snakeyaml.version>
        <scrimage.version>4.3.0</scrimage.version>
        <spotless.version>2.44.0.BETA4</spotless.version>
        <spring-data.version>2.7.18</spring-data.version>
        <spring-oauth.version>2.5.2.RELEASE</spring-oauth.version>
        <spring-security.version>5.8.16</spring-security.version>
        <spring.version>5.3.39</spring.version>
        <struts2.version>6.6.1</struts2.version>
        <thumbnailator.version>0.4.20</thumbnailator.version>
        <thymeleaf.version>3.1.3.RELEASE</thymeleaf.version>
        <tomcat.version>9.0.100</tomcat.version>
        <uap-java.version>1.6.1</uap-java.version>
        <waffle.version>3.3.0</waffle.version>
        <app.projectVersion>${project.version}</app.projectVersion>
        <app.buildVersion>${project.version}</app.buildVersion>
        <app.buildNumber>${project.version}</app.buildNumber>
        <app.buildDate>${maven.build.timestamp}</app.buildDate>
        <app.buildDescription>${project.description}</app.buildDescription>
        <app.name>${project.name}</app.name>
        <app.ngappOutputPath>dist</app.ngappOutputPath>
        <spring-aop-file>spring-aop-config-development.xml</spring-aop-file>
        <project.testresult.directory>${project.build.directory}/test-results</project.testresult.directory>
        <junit.itReportFolder>${project.testresult.directory}/integrationTest</junit.itReportFolder>
        <junit.utReportFolder>${project.testresult.directory}/test</junit.utReportFolder>
        <p.excludedGroups>integration</p.excludedGroups>
        <p.Build-Git-Info/>
        <p.Build-MavenSite/>
        <p.Build-Tomcat-Resources/>
        <p.Skip-Unit-Test/>
        <p.Skip-Compile/>
        <p.Skip-War/>
        <p.Build-AspectJ-CompileTime/>
        <p.Build-Angular/>
        <p.Run-CheckJavaRules/>
        <p.Build-War/>
        <p.Run-Liquibase/>
        <p.Run-Integration-Test/>
        <bun.version>v1.2.4</bun.version>
        <node.version>v23.8.0</node.version>
        <npm.version>11.1.0</npm.version>
        <!-- liga del servidor está guardado el caché de NPM y node, es una instancia de nginx -->
        <!-- https://nodejs.org/dist/v20.11.0/win-x64/node.exe-->
        <downloadNodeUrl>http://***********:8182/nodejs/</downloadNodeUrl>
        <!-- https://registry.npmjs.org/npm/-/npm-10.4.0.tgz-->
        <downloadNpmUrl>http://***********:8182/npm/</downloadNpmUrl>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mattbertolini</groupId>
            <artifactId>liquibase-slf4j</artifactId>
            <version>${liquibase-slf4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- jsoup HTML parser library @ https://jsoup.org/ -->
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>${mssql-jdbc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>${hikari-cp.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>${jfreechart.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>${commons-beanutils.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>${commons-cli.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons-lang.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-digester</groupId>
            <artifactId>commons-digester</artifactId>
            <version>${commons-digester.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${commons-text.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool2.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${commons-net.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>activation</artifactId>
            <version>${javax.activation.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.sql</groupId>
            <artifactId>jdbc-stdext</artifactId>
            <version>${jdbc-stdext.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.transaction</groupId>
            <artifactId>jta</artifactId>
            <version>${jta.version}</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.version}</version>
        </dependency>
        <dependency>
            <groupId>jstl</groupId>
            <artifactId>jstl</artifactId>
            <version>${jstl.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>bnext-jdbc</artifactId>
            <version>${bnext-jdbc.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>bnext-cipher</artifactId>
            <version>${bnext-cipher.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>ignite-ui</artifactId>
            <version>${ignite-ui.version}</version>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>licencing-api</artifactId>
            <version>${licencing-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>licencing-reader</artifactId>
            <version>${licencing-reader.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>mx.bnext</groupId>
                    <artifactId>licencing-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Bnext library, deployed in Server ***********:8181 -->
            <groupId>mx.bnext</groupId>
            <artifactId>qms-docs</artifactId>
            <version>${qms-docs.version}</version>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>bnext-core</artifactId>
            <version>${bnext-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mx.bnext</groupId>
                    <artifactId>bnext-cipher</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>${thumbnailator.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sf.biweekly</groupId>
            <artifactId>biweekly</artifactId>
            <version>${biweekly.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>${ehcache.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>jaxb-runtime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.cache</groupId>
                    <artifactId>cache-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.activation</groupId>
                    <artifactId>jakarta.activation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jakarta.xml.bind</groupId>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.cache</groupId>
            <artifactId>cache-api</artifactId>
            <version>${cache-api.version}</version>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jfree</groupId>
                    <artifactId>jfreechart</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports-fonts</artifactId>
            <version>${jasperreports.version}</version>
        </dependency>
        <dependency>
            <groupId>net.tascalate.javaflow</groupId>
            <artifactId>net.tascalate.javaflow.api</artifactId>
            <version>${net.tascalate.javaflow.api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>arial-extension</groupId>
            <artifactId>arial-extension</artifactId>
            <version>${arial-extension.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.librepdf</groupId>
            <artifactId>openpdf</artifactId>
            <version>${openpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sksamuel.scrimage</groupId>
            <artifactId>scrimage-core</artifactId>
            <version>${scrimage.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.twelvemonkeys.imageio</groupId>
                    <artifactId>imageio-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.twelvemonkeys.imageio</groupId>
                    <artifactId>imageio-jpeg</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sksamuel.scrimage</groupId>
            <artifactId>scrimage-webp</artifactId>
            <version>${scrimage.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>${jxl.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-stax-api_1.0_spec</artifactId>
            <version>${geronimo-stax-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jcl</artifactId>
            <version>${log4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j2-impl</artifactId>
            <version>${log4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <version>${log4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>stax</groupId>
                    <artifactId>stax-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>${poi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.struts</groupId>
            <artifactId>struts2-core</artifactId>
            <version>${struts2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.ben-manes.caffeine</groupId>
                    <artifactId>caffeine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.struts</groupId>
            <artifactId>struts2-json-plugin</artifactId>
            <version>${struts2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.struts</groupId>
            <artifactId>struts2-spring-plugin</artifactId>
            <version>${struts2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons-fileupload.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax.annotation-api.version}</version>
        </dependency>
        <dependency>
            <!-- Not found in Maven Central, deployed in Server 158 -->
            <groupId>org.artofsolving.jodconverter</groupId>
            <artifactId>jodconverter-core</artifactId>
            <version>${jodconverter-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.openoffice</groupId>
                    <artifactId>juh</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.openoffice</groupId>
                    <artifactId>ridl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.openoffice</groupId>
                    <artifactId>unoil</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectj.version}</version>
        </dependency>
        <!-- Se utiliza en JasperReports -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>${groovy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>${hibernate.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.xml.bind</groupId>
                    <artifactId>jaxb-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.bytebuddy</groupId>
                    <artifactId>byte-buddy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>javax.activation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.logging</groupId>
                    <artifactId>jboss-logging</artifactId>
                </exclusion>
                <exclusion>
                    <!--
                        Esta librería no se es compatible
                        con JAVA 8, se quita por que no la utilizamos
                    -->
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>jaxb-runtime</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-entitymanager</artifactId>
            <version>${hibernate.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jboss.logging</groupId>
                    <artifactId>jboss-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-jcache</artifactId>
            <version>${hibernate.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.cache</groupId>
                    <artifactId>cache-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.logging</groupId>
                    <artifactId>jboss-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-search-engine</artifactId>
            <version>${hibernate-search.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-search-analyzers</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.lucene</groupId>
                    <artifactId>lucene-facet</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.logging</groupId>
                    <artifactId>jboss-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jboss.logging</groupId>
                    <artifactId>jboss-logging-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>${lucene.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-search-orm</artifactId>
            <version>${hibernate-search.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-search-analyzers</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>${jsqlparser.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging</artifactId>
            <version>${jboss-logging.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jboss.logging</groupId>
            <artifactId>jboss-logging-annotations</artifactId>
            <version>${jboss-logging-annotations.version}</version>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
            <version>${liquibase.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.liquibase</groupId>
                    <artifactId>liquibase-commercial</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.xml.bind</groupId>
                    <artifactId>jaxb-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.opencsv</groupId>
                    <artifactId>opencsv</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>javax.activation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.liquibase.ext</groupId>
            <artifactId>liquibase-modify-column</artifactId>
            <version>${liquibase.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>${opencsv.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-text</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>${javassist.version}</version>
        </dependency>
        <dependency>
            <groupId>org.owasp.encoder</groupId>
            <artifactId>encoder</artifactId>
            <version>${encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.waffle</groupId>
            <artifactId>waffle-jna</artifactId>
            <version>${waffle.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.errorprone</groupId>
                    <artifactId>error_prone_annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
            <version>${spring.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.aspectj</groupId>
                    <artifactId>aspectjweaver</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-orm</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>${tomcat.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-el</artifactId>
            <version>${tomcat.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-websocket</artifactId>
            <version>${tomcat.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-annotations-api</artifactId>
            <version>${tomcat.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
            <version>${spring-data.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
            <version>${spring-data.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.aspectj</groupId>
                    <artifactId>aspectjrt</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>jcl-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-tx</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-orm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-commons</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-config</artifactId>
            <version>${spring-security.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
            <version>${spring-security.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-client</artifactId>
            <version>${spring-security.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
            <version>${spring-security.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
            <version>${mariadb-java-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.waffle</groupId>
                    <artifactId>waffle-jna</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
            <version>${spring-oauth.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
            <version>${spring-security.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>dojo</artifactId>
            <version>${dojo.version}</version>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf</artifactId>
            <version>${thymeleaf.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>ognl</groupId>
                    <artifactId>ognl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ognl</groupId>
                    <artifactId>ognl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>${snakeyaml.version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.checkerframework</groupId>
                    <artifactId>checker-qual</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${javax.servlet-api.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.github.ua-parser</groupId>
            <artifactId>uap-java</artifactId>
            <version>${uap-java.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Don't update until JDK 11 or newer. See https://stackoverflow.com/a/72151763/2804966 -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>${jaxb-api.version}</version>
        </dependency>
        <!-- Don't update until JDK 11 or newer. See https://stackoverflow.com/a/72151763/2804966 -->
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>${jaxb-impl.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sun.activation</groupId>
                    <artifactId>jakarta.activation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>${modelmapper.version}</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mx.bnext</groupId>
            <artifactId>qms-stackedit</artifactId>
            <version>${qms-stackedit.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${app.name}</finalName>
        <resources>
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>applicationContext.xml</include>
                </includes>
            </resource>
            <resource>
                <filtering>false</filtering>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>applicationContext.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>${maven-clean-plugin.version}</version>
                <configuration>
                    <fast>true</fast>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <debug>true</debug>
                    <excludes>
                        <exclude>ngapp/**</exclude>
                    </excludes>
                    <showDeprecation>true</showDeprecation>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <version>${maven-install-plugin.version}</version>
                <executions>
                    <execution>
                        <id>default-install</id>
                        <phase>none</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <excludedGroups>${p.excludedGroups}</excludedGroups>
                    <trimStackTrace>false</trimStackTrace>
                    <runOrder>alphabetical</runOrder>
                    <reportsDirectory>${junit.utReportFolder}</reportsDirectory>
                    <argLine>-Xmx2048m</argLine>
                    <systemPropertyVariables>
                        <java.awt.headless>true</java.awt.headless>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>${maven-antrun-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <configuration>
                            <target name="copy biome.json">
                                <copy file="src/main/ngapp/biome.json" tofile="biome.json"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>Build-Git-Info</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-Git-Info>,Build-Git-Info</p.Build-Git-Info>
                <!--suppress UnresolvedMavenProperty -->
                <app.buildVersion>@git.commit.id.describe@</app.buildVersion>
                <!--suppress UnresolvedMavenProperty -->
                <app.buildNumber>@git.commit.id.abbrev@</app.buildNumber>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>io.github.git-commit-id</groupId>
                        <artifactId>git-commit-id-maven-plugin</artifactId>
                        <version>${git-commit-id-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>revision</goal>
                                </goals>
                                <phase>validate</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <useNativeGit>true</useNativeGit>
                            <offline>true</offline>
                            <abbrevLength>10</abbrevLength>
                            <includeOnlyProperties>
                                <includeOnlyProperty>git.commit.id.describe</includeOnlyProperty>
                                <includeOnlyProperty>git.commit.id.abbrev</includeOnlyProperty>
                            </includeOnlyProperties>
                            <replacementProperties>
                                <replacementProperty>
                                    <property>git.commit.id.describe</property>
                                    <token>^(.*?)(-dirty)?$</token>
                                    <value>$1</value>
                                </replacementProperty>
                            </replacementProperties>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-scm-plugin</artifactId>
                        <version>${maven-scm-plugin.version}</version>
                        <configuration>
                            <connectionType>connection</connectionType>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-MavenSite</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-MavenSite>,Build-MavenSite</p.Build-MavenSite>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-site-plugin</artifactId>
                        <version>${maven-site-plugin.version}</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-Tomcat-Resources</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-Tomcat-Resources>,p.Build-Tomcat-Resources</p.Build-Tomcat-Resources>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-resources-plugin</artifactId>
                        <version>${maven-resources-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>copy-context</id>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>src/main/webapp/META-INF</outputDirectory>
                                    <overwrite>true</overwrite>
                                    <resources>
                                        <resource>
                                            <directory>src/main/tomcat</directory>
                                            <includes>
                                                <include>context.xml</include>
                                            </includes>
                                            <filtering>true</filtering>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Skip-Unit-Test</id>
            <properties>
                <p.Skip-Unit-Test>,Skip-Unit-Test</p.Skip-Unit-Test>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Skip-Compile</id>
            <properties>
                <p.Skip-Compile>,Skip-Compile</p.Skip-Compile>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default-compile</id>
                                <phase>compile</phase>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                                <configuration>
                                    <skipMain>true</skipMain>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Skip-War</id>
            <properties>
                <p.Skip-War>,Skip-War</p.Skip-War>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>${maven-war-plugin.version}</version>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>war</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-AspectJ-CompileTime</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-AspectJ-CompileTime>,Build-AspectJ-CompileTime</p.Build-AspectJ-CompileTime>
                <spring-aop-file>spring-aop-config-production.xml</spring-aop-file>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>dev.aspectj</groupId>
                        <artifactId>aspectj-maven-plugin</artifactId>
                        <version>${aspectj-maven.version}</version>
                        <configuration>
                            <aspectLibraries>
                                <aspectLibrary>
                                    <groupId>org.springframework</groupId>
                                    <artifactId>spring-aspects</artifactId>
                                </aspectLibrary>
                            </aspectLibraries>
                            <ajdtBuildDefFile>src/main/resources/buildaj.properties</ajdtBuildDefFile>
                            <complianceLevel>${java.version}</complianceLevel>
                            <source>${java.version}</source>
                            <target>${java.version}</target>
                            <Xlint>adviceDidNotMatch=error</Xlint>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>compile</goal>
                                </goals>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>org.aspectj</groupId>
                                <artifactId>aspectjtools</artifactId>
                                <version>${aspectj.version}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-Angular</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-Angular>,Build-Angular</p.Build-Angular>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.code.maven-replacer-plugin</groupId>
                        <artifactId>maven-replacer-plugin</artifactId>
                        <version>${maven-replacer-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>replace-manifest</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>replace</goal>
                                </goals>
                                <configuration>
                                    <file>src/main/ngapp/src/manifest.webmanifest</file>
                                    <regex>true</regex>
                                    <replacements>
                                        <replacement>
                                            <token>"name":.*?".+?"</token>
                                            <value>"name": "Bnext QMS instance for ${app.name}"</value>
                                        </replacement>
                                        <replacement>
                                            <token>"short_name":.*?".+?"</token>
                                            <value>"short_name": "${app.name}"</value>
                                        </replacement>
                                    </replacements>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>${frontend-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>default-clean</id>
                                <phase>none</phase>
                            </execution>
                            <execution>
                                <id>install node and npm</id>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                            </execution>
                            <execution>
                                <id>Execution [npm install --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <configuration>
                                    <arguments>install --progress=false</arguments>
                                </configuration>
                                <phase>generate-resources</phase>
                            </execution>
                            <execution>
                                <id>Execution [npm run biome-lint --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run biome-lint --folderName=${app.name}
                                        --outputPath=${app.ngappOutputPath}
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>Execution [npm run biome-check --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run biome-check --folderName=${app.name}
                                        --outputPath=${app.ngappOutputPath}
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>Execution [npm run test --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run test-ci --folderName=${app.name}</arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>Execution [npm run build --a-lot-of-args]</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run build --folderName=${app.name} --outputPath=${app.ngappOutputPath}
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <bunVersion>${bun.version}</bunVersion>
                            <nodeVersion>${node.version}</nodeVersion>
                            <npmVersion>${npm.version}</npmVersion>
                            <workingDirectory>${project.basedir}/src/main/ngapp</workingDirectory>
                            <nodeDownloadRoot>${downloadNodeUrl}</nodeDownloadRoot>
                            <npmDownloadRoot>${downloadNpmUrl}</npmDownloadRoot>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-clean-plugin</artifactId>
                        <configuration>
                            <fast>true</fast>
                            <filesets>
                                <fileset>
                                    <directory>src/main/ngapp/node_modules</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/.angular</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/.nx</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/node</directory>
                                </fileset>
                                <fileset>
                                    <directory>src/main/ngapp/dist</directory>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Run-CheckJavaRules</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Run-CheckJavaRules>,Run-CheckJavaRules</p.Run-CheckJavaRules>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <version>${maven-enforcer-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>enforce-versions</id>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>enforce-dependencyConvergence</id>
                                <configuration>
                                    <rules>
                                        <DependencyConvergence/>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>enforce-bytecode-version</id>
                                <configuration>
                                    <rules>
                                        <enforceBytecodeVersion>
                                            <maxJdkVersion>${java.version}</maxJdkVersion>
                                            <ignoreClasses>
                                                <ignoreClass>module-info</ignoreClass>
                                                <ignoreClass>freemarker.core._Java9</ignoreClass>
                                                <ignoreClass>freemarker.core._Java9Impl</ignoreClass>
                                                <ignoreClass>freemarker.core._Java16</ignoreClass>
                                                <ignoreClass>freemarker.core._Java16Impl</ignoreClass>
                                            </ignoreClasses>
                                        </enforceBytecodeVersion>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <message>You are running an older version of Maven. Bnext QMS requires at least
                                        Maven ${maven.version}
                                    </message>
                                    <version>[${maven.version},)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <message>
                                        Compilation of Bnext QMS requires JDK ${maven-jdk.version}
                                    </message>
                                    <version>[${maven-jdk.version},)</version>
                                </requireJavaVersion>
                                <bannedDependencies>
                                    <!-- the implementation javax.activation:activation contains the api classes too -->
                                    <excludes>
                                        <exclude>jakarta.activation:jakarta.activation-api</exclude>
                                    </excludes>
                                    <message>the implementation javax.activation:activation is included and it contains
                                        the API classes too
                                    </message>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>org.codehaus.mojo</groupId>
                                <artifactId>extra-enforcer-rules</artifactId>
                                <version>${extra-enforcer-rules.version}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                    <plugin>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-jspc-maven-plugin</artifactId>
                        <version>${jetty-jspc.version}</version>
                        <dependencies>
                            <dependency>
                                <groupId>org.apache.tomcat</groupId>
                                <artifactId>tomcat-jasper</artifactId>
                                <version>${tomcat.version}</version>
                            </dependency>
                        </dependencies>
                        <executions>
                            <execution>
                                <id>jspc</id>
                                <goals>
                                    <goal>jspc</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <sourceVersion>${java.version}</sourceVersion>
                            <targetVersion>${java.version}</targetVersion>
                        </configuration>
                    </plugin>
                    <!--
                    <plugin>
                        <groupId>com.diffplug.spotless</groupId>
                        <artifactId>spotless-maven-plugin</artifactId>
                        <version>${spotless.version}</version>
                        <configuration>
                            <java>
                                <googleJavaFormat/>
                            </java>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <phase>validate</phase>
                            </execution>
                        </executions>
                    </plugin>
                    -->
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Run-Integration-Test</id>
            <properties>
                <p.Run-Integration-Test>,Run-Integration-Test</p.Run-Integration-Test>
                <p.excludedGroups/>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven-failsafe-plugin.version}</version>
                        <configuration>
                            <runOrder>alphabetical</runOrder>
                            <reportsDirectory>${junit.itReportFolder}</reportsDirectory>
                            <includes>
                                <include>**/*IT*</include>
                                <include>**/*IntTest*</include>
                                <include>**/*CucumberIT*</include>
                            </includes>
                            <excludedGroups>${p.excludedGroups}</excludedGroups>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>-Xmx8192m</argLine>
                            <systemPropertyVariables>
                                <java.awt.headless>true</java.awt.headless>
                            </systemPropertyVariables>
                        </configuration>
                        <executions>
                            <execution>
                                <id>Integration-Test</id>
                                <goals>
                                    <goal>integration-test</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>verify</id>
                                <goals>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-War</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Build-War>,Build-War</p.Build-War>
            </properties>
            <build>
                <resources>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/@infragistics/igniteui-angular/</directory>
                        <targetPath>scss/@infragistics/igniteui-angular</targetPath>
                        <includes>
                            <include>lib/core/styles/</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/sass-embedded-win32-x64/dart-sass/</directory>
                        <targetPath>dart-sass</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/sass-embedded-linux-x64/dart-sass/</directory>
                        <targetPath>dart-sass</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/foundation-sites/</directory>
                        <targetPath>scss/foundation-sites</targetPath>
                        <excludes>
                            <exclude>dist/</exclude>
                            <exclude>js/</exclude>
                            <exclude>.github</exclude>
                            <exclude>customizer/</exclude>
                        </excludes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/dist/</directory>
                        <targetPath>${project.build.directory}/${app.name}/qms</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/@material-design-icons/</directory>
                        <targetPath>scss/material-icons</targetPath>
                        <includes>
                            <include>font/</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/igniteui-theming/</directory>
                        <targetPath>scss/igniteui-theming</targetPath>
                        <includes>
                            <include>sass/</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/minireset.css/</directory>
                        <targetPath>scss/minireset.css</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/node_modules/@typopro/web-titillium/</directory>
                        <targetPath>scss/@typopro/web-titillium/</targetPath>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/src</directory>
                        <targetPath>scss</targetPath>
                        <includes>
                            <include>styles.scss</include>
                            <include>materialFonts.scss</include>
                            <include>_foundation-settings.scss</include>
                            <include>foundation.scss</include>
                            <include>componentColors.scss</include>
                        </includes>
                    </resource>
                    <resource>
                        <filtering>false</filtering>
                        <directory>${basedir}/src/main/ngapp/src</directory>
                        <targetPath>scss/src</targetPath>
                        <includes>
                            <include>styles/</include>
                        </includes>
                    </resource>
                </resources>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-war-plugin</artifactId>
                        <version>${maven-war-plugin.version}</version>
                        <configuration>
                            <recompressZippedFiles>false</recompressZippedFiles>
                            <archive>
                                <addMavenDescriptor>false</addMavenDescriptor>
                                <manifest>
                                    <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                </manifest>
                                <manifestEntries>
                                    <Implementation-Build>${app.buildNumber}</Implementation-Build>
                                </manifestEntries>
                            </archive>
                            <webResources>
                                <resource>
                                    <directory>src/main/tomcat</directory>
                                    <targetPath>WEB-INF</targetPath>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>rewrite.config</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <directory>src/main/tomcat</directory>
                                    <targetPath>META-INF</targetPath>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>context.xml</include>
                                    </includes>
                                </resource>
                            </webResources>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Run-Liquibase</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <p.Run-Liquibase>,Run-Liquibase</p.Run-Liquibase>
            </properties>
        </profile>
    </profiles>
</project>
